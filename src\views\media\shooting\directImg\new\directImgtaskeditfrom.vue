<template>
  <!-- 编辑操作 -->
  <my-container v-loading="pageLoading">
    <el-form :model="addForm" :rules="calcAddFormRules" ref="addForm" :disabled="islook" label-width="100px"
      class="demo-ruleForm">
      <div class="bzbjrw">
        <div style="position: fixed; z-index: 999">
          <div class="bzbjbt">
            <span style="float: left">编辑/操作</span>
            <span style="float: right" v-if="!islook">
              <a href="#"><i @click="onEndShootingTaskAction" v-if="addForm.isend == 0" class="el-icon-document-delete"
                  title="点击终止"></i></a>
              <a href="#"><i @click="onEndRestartAction" v-if="addForm.isend == 1" class="el-icon-document-checked"
                  title="点击重启"></i></a>
              <a href="#"><i @click="onSignShootingTaskAction" v-if="addForm.isTopOldNum == 0" class="el-icon-news"
                  title="点击标记"></i></a>
              <a href="#"><i @click="onUnSignShootingTaskAction" v-if="addForm.isTopOldNum > 0" class="el-icon-news"
                  title="取消标记"></i></a>
              <a href="#"><i class="el-icon-odometer" title="催办任务(待开发)"></i></a>
              <a href="#"><i @click="onDeleteShootingTaskAction" class="el-icon-delete" title="删除任务"></i></a>
              <a href="#"><i class="el-icon-more" title="更多操作(待开发)"></i></a>
            </span>
          </div>
          <div style="height: 70px">
            <div class="rwmc">
              <div class="xh" style="width: 55px">{{ addForm.directImgTaskId }}</div>
              <div class="mc" style="height: 66px">|</div>
              <div class="mc" style="width: 360px">
                <el-tooltip v-if="inputshow" class="item" effect="dark"
                  :content="addForm.productShortName ? addForm.productShortName : ''" placement="top">
                  <div style="margin: 0; width: 100%; height: 50px;" class="linecs" @click="inputshowfunc">
                    <span>{{ addForm.productShortName ? addForm.productShortName : '请点击输入产品简称' }} </span>
                  </div>
                </el-tooltip>
                <el-input v-else-if="!inputshow" @blur="inputshow = true" size="medium"
                  style="margin: 0; width: 100%; margin-top: -3px; font-size: 18px;"
                  v-model.trim="addForm.productShortName" :maxlength=100 placeholder="产品简称" clearable />
              </div>
              <div class="icon" style="float: right;width: 70px;">
                <el-button size="mini" type="primary" @click="submitForm('addForm')">&nbsp;保&nbsp;存&nbsp;</el-button>
              </div>
            </div>
          </div>
        </div>
        <!-- 表单 star -->
        <div style="padding-top: 150px">
          <div class="bzbjlx">
            <div class="lxwz">平台</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="platform">
                <el-select size="mini" style="width: 60%" v-model="addForm.platform" placeholder="请选择平台" :clearable="true"
                  :collapse-tags="true" filterable @change="onchangeplatform">
                  <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="bzbjlx">
            <div class="lxwz">店铺</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="shopName">
                <el-select size="mini" style="width: 100%" v-model="addForm.shopName" placeholder="请选择店铺"
                  :clearable="true" :collapse-tags="true" @change="selShopInfoChange(addForm.shopName, index)" filterable>
                  <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                    :value="item.shopCode" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="bzbjlx">
            <div class="lxwz">运营小组</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="operationGroup" :clearable="true"
                @change="selOpergroupInfoChange(addForm.operationGroup, index)" filterable>
                <el-select size="mini" style="width: 60%" v-model="addForm.operationGroup" placeholder="请选择小组">
                  <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="bzbjlx">
            <div class="lxwz">对接人</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="dockingPeople">
                <el-input size="mini" style="width: 50%" v-model="addForm.dockingPeople" :clearable="true"></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="bzbjlx" v-if="checkPermission('directImgUrgencyEdit')">
            <div class="lxwz">紧急程度</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="taskUrgency">
                <el-select size="mini" style="width: 50%" v-model="addForm.taskUrgency" placeholder="正常" filterable>
                  <el-option v-for="item in taskUrgencyList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="bzbjlx">
            <div class="lxwz">拍摄样品</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" label-position="left" prop="warehouse">
                <el-select size="mini" style="width: 80%" v-model="addForm.warehouse" placeholder="请选择仓库"
                  @change="selwarehouseChange()">
                  <el-option v-for="item in cwarehouselist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="bzbjlx">
            <div class="lxwz">订单号</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="orderNoInner">
                <el-input size="mini" style="width: 70%" v-model="addForm.orderNoInner" placeholder="请填写订单号"
                  :clearable="true"></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="bzbjlx">
            <div class="lxwz">快递单号</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="expressNo">
                <el-input size="mini" style="width: 100%" v-model="addForm.expressNo" placeholder="请填写快递单号"
                  :clearable="true"></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="floateRow">
            <div class="itemlabel">拍摄任务</div>
            <div class="itemvalue">
              <el-form-item label="" prop="shootingTaskPickList">
                <el-checkbox-group v-model="addForm.shootingTaskPickList" @change="taskPickChange">
                  <el-checkbox label="1" border>照片拍摄</el-checkbox>
                  <el-checkbox style="margin-left: -8px;" label="4" border>车图排版</el-checkbox>
                  <el-checkbox style="margin-left: -8px;" label="5" border>照片建模</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </div>
          </div>
          <div class="floateRow" v-if="checkPermission('directImgFpsheji')">
            <div class="itemlabel">任务分配</div>
            <div class="itemvalue">
              <el-form-item label="" prop="fpDetailLqName">
                <el-select size="mini" style="width: 92px" placeholder="分配照片" v-model="addForm.fpPhotoLqName"
                  :disabled="fpPhotoLqNameEnable" :clearable="true" :collapse-tags="true" filterable
                  remote reserve-keyword :remote-method="remoteMethod1">
                  <el-option v-for="(item,i) in useroptions1" :key="item.id + i" :label="item.label" :value="item.label" />
                </el-select>
                <el-select size="mini" style="width: 92px;margin-left: 7px;" placeholder="分配车图"
                  v-model="addForm.fpDetailLqName" :disabled="fpDetailLqNameEnable" :clearable="true"
                  :collapse-tags="true" filterable
                  remote reserve-keyword :remote-method="remoteMethod2">
                  <el-option v-for="(item,i) in useroptions2" :key="item.id + i" :label="item.label" :value="item.label" />
                </el-select>
                <el-select size="mini" style="width: 92px;margin-left: 7px;" placeholder="分配建模"
                  v-model="addForm.fpModelLqName" :disabled="fpModelLqNameEnable" :clearable="true" :collapse-tags="true"
                  filterable remote reserve-keyword :remote-method="remoteMethod3">
                  <el-option v-for="(item,i) in useroptions3" :key="item.id + i" :label="item.label" :value="item.label" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="bzbjlx" style="margin-top: 15px;height: 150px;">
            <div class="lxwz">附件</div>
            <div style="display: inline-block; margin-left: 12px;  vertical-align: top; width: 180px;">
              <uploadfile :minisize="false" ref="uploadexl" :islook="islook" :buttontext="'上传附件'" :isdown="true"
                :uploadInfo="addForm.execlUpfiles" :boxsize="{ width: '150px', height: '100px' }" :limit="10000"
                :accepttyes="'.xlsx'" :delfunction="deluplogexl" />
            </div>
          </div>
          <div class="bzbjlx" style="margin: auto auto 60px auto">
            <el-input type="textarea" :rows="2" :maxlength="800" show-word-limit placeholder="请输入内容"
              v-model="addForm.taskRemark"> </el-input>
          </div>
        </div>
        <!-- 表单 end -->
        <!-- 操作选项 star -->
        <div class="bzczxx">
          <el-menu class="el-menu-demo" mode="horizontal" collapse-transition active-text-color="#409eff">
            <el-menu-item style="margin-left: 36px; line-height: 65px" index="1" @click="menuview = 1"><i
                class="el-icon-set-up"></i>操作任务</el-menu-item>
          </el-menu>
        </div>
        <div class="bzbjfgx" v-show="menuview == 1">
          <div class="bzsccg" v-if="checkPermission('api:media:directImg:GetUploadSuccessAttachmentNew')">
            <el-button style="width: 100%" size="medium" type="primary" :disabled="false"
              @click="toResultmatter">+上传成果文件</el-button>
          </div>
          <div class="bzczx" style="min-height:200px;">
            <div class="bzczk">
              <div class="bzczksps">
                <div class="bzczs" style="display: block;">照片拍摄</div>
              </div>
              <div class="bzczkz">
                <div class="bzczan">
                  <el-button size="mini" :disabled="islook || addForm.photoLqNameStr == '-'"
                    :type="addForm.photoIsOver == 0 && addForm.photoLqNameStr != '-' ? 'primary' : ''"
                    @click="pickTask(addForm.photoLqBtnStr, 1, addForm.directImgTaskId)">{{ addForm.photoLqBtnStr
                    }}</el-button>
                </div>
                <div class="bzczmz">{{ addForm.photoLqNameStr == "-" ? "" : addForm.photoLqNameStr }}</div>
                <div class="bzczsj">{{ addForm.photoOverTimeStr == "-" ? "" : addForm.photoOverTimeStr }}</div>
              </div>
            </div>
            <div class="bzczk">
              <div class="bzczksps">
                <div class="bzczs" style="display: block;">美工排版</div>
              </div>
              <div class="bzczkz">
                <div class="bzczan">
                  <el-button size="mini" :disabled="islook || addForm.detailLqNameStr == '-'"
                    :type="addForm.detailIsOver == 0 && addForm.detailLqNameStr != '-' ? 'primary' : ''"
                    @click="pickTask(addForm.detailLqBtnStr, 4, addForm.directImgTaskId)">{{ addForm.detailLqBtnStr
                    }}</el-button>
                </div>
                <div class="bzczmz">{{ addForm.detailLqNameStr == "-" ? "" : addForm.detailLqNameStr }}</div>
                <div class="bzczsj">{{ addForm.detailOverTimeStr == "-" ? "" : addForm.detailOverTimeStr }}</div>
              </div>
              <div class="bzczky">
                <div class="bzczan">
                  <el-form style="display:inline-block">
                    <div>
                      <!--确认信息按钮-->
                      <el-button size="mini" :disabled="islook || addForm.detailLqNameStr == '-'"
                        :type="addForm.detailConfirmIsOver == 0 && addForm.detailLqNameStr != '-' ? 'primary' : ''"
                        @click="confirmTaskInfo(addForm.detailConfirmBtnStr, 4, addForm.directImgTaskId)">{{
                          addForm.detailConfirmBtnStr }}</el-button>
                    </div>
                  </el-form>
                </div>
                <div class="bzczmz">{{ addForm.detailConfirmNameStr == "-" ? "" : addForm.detailConfirmNameStr }}</div>
                <div class="bzczsj">{{ addForm.detailConfirmTimeStr == "-" ? "" : addForm.detailConfirmTimeStr }}</div>
              </div>
            </div>
            <div class="bzczk">
              <div class="bzczksps">
                <div class="bzczs" style="display: block;">建模照片</div>
              </div>
              <div class="bzczkz">
                <div class="bzczan">
                  <el-button size="mini" :disabled="islook || addForm.modelPhotosLqNameStr == '-'"
                    :type="addForm.modelPhotosIsOver == 0 && addForm.modelPhotosLqNameStr != '-' ? 'primary' : ''"
                    @click="pickTask(addForm.modelPhotoBtnStr, 5, addForm.directImgTaskId)">{{ addForm.modelPhotoBtnStr
                    }}</el-button>
                </div>
                <div class="bzczmz">{{ addForm.modelPhotosLqNameStr == "-" ? "" : addForm.modelPhotosLqNameStr }}</div>
                <div class="bzczsj">{{ addForm.modelPhotosOverTimeStr == "-" ? "" : addForm.modelPhotosOverTimeStr }}</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 操作选项 end -->
        <!-- 操作日志 star -->
        <!-- <div class="dspbjfgx" v-show="menuview == 1" style="min-height:160px">
          <div class="bzczrz" style="margin-top:10px;"> </div>
          <div class="bzczrzx" v-for="(item, index ) in loginfoList " :key="index">
            <div>
              <div class="rztx"></div>
              <div class="rzmz">{{ item.name }}</div>
              <div class="rzxgx"> {{ item.changeinfo }} </div>
              <div class="rzxgsj"> {{ item.time }}</div>
            </div>
            <div>
              <div class="rzxgq">修改后：</div>
              <div class="rzxgnr">{{ item.before }}</div>
            </div>
            <div>
              <div class="rzxgq">修改前：</div>
              <div class="rzxgnr">{{ item.after }}</div>
            </div>
          </div>
        </div> -->
        <div class="bzbjlx" style="margin: auto auto 60px auto">
          <el-input type="textarea" :rows="5" v-model="loginfo" :disabled="true" placeholder="操作日志" />
        </div>
        <!-- 操作日志 end -->
        <div class="qxtj"></div>
      </div>
    </el-form>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import uploadfile from '@/views/media/shooting/uploadfile';
import {
  addOrUpdateShootingVideoTaskAsync, getShootingTaskFliesAsync, delShootingTploadFileTaskAsync, pickShootingTaskAsync,
  signShootingTaskActionAsync, unSignShootingTaskActionAsync,
  deleteShootingTaskActionAsync, endShootingTaskActionAsync, endRestartActionAsync,
  unPickShootingTaskAsync, confrimShootingTaskAsync, unConfrimShootingTaskAsync
} from '@/api/media/directImgtask';
import { getErpUserInfoView } from '@/api/media/mediashare';
import { getList as getshopList } from '@/api/operatemanage/base/shop'
export default {
  props: {
    taskUrgencyList: { type: Array, default: [] },
    groupList: { type: Array, default: [] },
    platformList: { type: Array, default: [] },
    warehouselist: { type: Array, default: [] },
    onCloseAddForm: { type: Function, default: null },
    islook: { type: Boolean, default: false },
    listtype: { type: Number, default: 1 },
  },
  components: { uploadfile, MyContainer },
  data() {
    return {
      loginfo: null,
      userList: [],
      bhmark: null,
      bhmarkVisible: false,
      that: this,
      inputshow: true,
      pageLoading: false,
      productVisible: false,//选择产品窗口
      shopList: [],
      cwarehouselist: [],
      addForm: {
        shootingTaskPickList: [],
        directImgTaskId: null,
        isend: 0,
        execlUpfiles: [],
      },
      fpPhotoLqNameEnable: false,
      fpVideoLqNameEnable: false,
      fpDetailLqNameEnable: false,
      fpModelLqNameEnable: false,
      loginfoList: [],
      menuview: 1,
      bhsumbit: false,
      useroptions1:[],
      useroptions2:[],
      useroptions3:[],
    };
  },
  computed: {
    calcAddFormRules() {
      return {
        productShortName: [{ required: true, message: '请填写', trigger: 'blur' }],
        shopName: [{ required: true, message: '请选择', trigger: 'blur' }],
        operationGroup: [{ required: true, message: '请选择', trigger: 'blur' }],
        dockingPeople: [{ required: true, message: '请填写', trigger: 'blur' }],
        shootingTaskPickList: [{ required: true, message: '请选择', trigger: 'blur' }],
        platform: [{ required: true, message: '请选择', trigger: 'blur' }],
        taskUrgency: [{ required: true, message: '请选择', trigger: 'blur' }],
        productCode: [{ required: true, message: '请选择', trigger: 'blur' }],
        packClass: [{ required: true, message: '请选择', trigger: 'blur' }],
        brand: [{ required: true, message: '请选择', trigger: 'blur' }],
        izcjdz: [{ required: true, message: '请选择', trigger: 'blur' }],
      }
    }
  },
  async mounted () {
    await this.getuser();
    for (let num in this.warehouselist) {
      this.cwarehouselist.push(this.warehouselist[num]);
    }
    if (this.addForm.dockingPeople == null)
      this.addForm.dockingPeople = this.$store.getters.userName?.split("-")[0].trim();
  },
  methods: {
     // 任务分配过滤
     remoteMethod1 (query) {
        if (query !== '') {
          setTimeout(() => {
            this.useroptions1 = this.userList.filter(item => {
              return item.label.toLowerCase()
                .indexOf(query.toLowerCase()) > -1;
            });
          }, 200);
        } else {
          this.useroptions1 = [];
        }
    },
    remoteMethod2 (query) {
        if (query !== '') {
          setTimeout(() => {
            this.useroptions2 = this.userList.filter(item => {
              return item.label.toLowerCase()
                .indexOf(query.toLowerCase()) > -1;
            });
          }, 200);
        } else {
          this.useroptions2 = [];
        }
      },

      remoteMethod3 (query) {
        if (query !== '') {
          setTimeout(() => {
            this.useroptions3 = this.userList.filter(item => {
              return item.label.toLowerCase()
                .indexOf(query.toLowerCase()) > -1;
            });
          }, 200);
        } else {
          this.useroptions3 = [];
        }
    },
    async getuser() {
      var res = await getErpUserInfoView();
      this.userList = res || [];
    },
    taskPickChange(value) {
      if (!value) return;
      //未选择，只读，且清空
      if (value.indexOf("1") < 0) {
        this.fpPhotoLqNameEnable = true;
      } else {
        this.fpPhotoLqNameEnable = false;
      }
      if (value.indexOf("2") < 0) {
        this.fpVideoLqNameEnable = true;
      } else {
        this.fpVideoLqNameEnable = false;
      }
      if (value.indexOf("4") < 0) {
        this.fpDetailLqNameEnable = true;
      } else {
        this.fpDetailLqNameEnable = false;
      }
      if (value.indexOf("5") < 0 && value.indexOf("6") < 0) {
        this.fpModelLqNameEnable = true;
      } else {
        this.fpModelLqNameEnable = false;
      }
    },
    toResultmatter(row) {
      if (row != null) {
        let routeUrl = this.$router.resolve({
          path: '/directimguploadfile',
          query: { id: this.addForm.directImgTaskId, name: this.addForm.productShortName }
        });
        window.open(routeUrl.href, '_blank');
      }
    },
    //下拉改变值
    inputshowfunc() {
      if (this.islook) return;
      this.inputshow = false;
    },
    async onchangeplatform(val) {
      var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 10000 });
      this.addForm.shopName = "";
      this.shopList = res1.data.list;
    },
    selwarehouseChange() {
      for (let num in this.warehouselist) {
        if (this.warehouselist[num].value == this.addForm.warehouse) {
          this.addForm.warehouseStr = this.warehouselist[num].label;
        }
      }
    },
    selShopInfoChange(val) {
      let resultArr = this.shopList.find((item) => {
        return item.shopCode == val;
      });
      this.addForm.shopNameStr = resultArr.shopName;
    },
    selOpergroupInfoChange(val) {
      var resultArr = this.groupList.find((item) => {
        return item.value == val;
      });
      this.addForm.operationGroupstr = resultArr.label;
    },
    //下拉改变值结束
    //选择产品窗口
    onSelctProduct() {
      if (!this.islook)
        this.productVisible = true;
    },

    async getNewTaskInfo() {
      this.pageLoading = true;
      var res = await getShootingTaskFliesAsync({ taskid: this.addForm.directImgTaskId });
      if (res?.success) {
        this.loginfo = res.data.loginfo;
      }
      this.pageLoading = false;
    },
    async editTask(row) {
      this.pageLoading = true;
      if (row.platform)
        await this.onchangeplatform(row.platform);
      //获取拍摄上传的附件,和任务信息
      var res = await getShootingTaskFliesAsync({ taskid: row.directImgTaskId });

      if (res?.success) {
        this.addForm = res.data.task;
        this.addForm.execlUpfiles = [];
        res.data.data.forEach(element => {

          if (element.upLoadType == 2) {
            this.addForm.execlUpfiles.push(element);
          }
        });
        //this.$nextTick(function () {
        this.$refs.uploadexl.setData(this.addForm.execlUpfiles);
        //})
        this.loginfo = res.data.loginfo;
      } else {
        this.addForm = row;
      }
      this.pageLoading = false;
    },
    //删除上传附件操作
    async deluplogexl(ret) {
      this.addLoading = true;
      await delShootingTploadFileTaskAsync({ upLoadPhotoId: ret.upLoadPhotoId, type: 2 }).catch(_ => {
        this.addLoading = false;
      });
      this.addLoading = false;
    },
    //提交保存
    async submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          var res = this.$refs.uploadexl.getReturns();
          if (!res.success) return;
          this.addForm.execlUpfiles = res.data;
          this.addForm.PhotoUpfiles = [];
          var value = this.addForm.shootingTaskPickList;
          if (value.indexOf("1") < 0) { this.addForm.fpPhotoLqName = null; }
          if (value.indexOf("4") < 0) { this.addForm.fpDetailLqName = null; }
          if (value.indexOf("5") < 0) { this.addForm.fpModelLqName = null; }

          let para = _.cloneDeep(this.addForm);
          this.pageLoading = true;
          var res = await addOrUpdateShootingVideoTaskAsync(para);
          this.pageLoading = false;
          if (!res?.success) { return; }
          this.$message({ message: this.$t('保存成功'), type: 'success' });
          this.onCloseAddForm(2);
          await this.editTask({ directImgTaskId: this.addForm.directImgTaskId});
        } else {
          return false;
        }
      });
    },
    //完成任务
    async pickTask(btnstr, index, directImgTaskId) {
      var that = this;
      switch (btnstr) {
        case "标记完成":
          this.$confirm("确认完成, 是否继续?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(async () => {
            var res = await pickShootingTaskAsync({ taskid: directImgTaskId, index: index });
            if (res?.success) {
              that.$message({ message: '完成成功', type: "success" });
              await this.editTask({ directImgTaskId: directImgTaskId });
              this.onCloseAddForm(2);
            }
          });
          break;
        case "取消完成":
          this.$confirm("确认取消完成, 是否继续?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(async () => {
              var res = await unPickShootingTaskAsync({ taskid: directImgTaskId, index: index })
              if (res?.success) {
                await this.editTask({ directImgTaskId: directImgTaskId });
                that.$message({ message: '取消成功', type: "success" });
                that.onCloseAddForm(2);
              }
            });
          break;
      }
    },
    //确认Or取消任务
    async confirmTaskInfo(btnstr, index, directImgTaskId) {
      var that = this;
      switch (btnstr) {
        case "确认完成":
          this.$confirm("是否确认?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(async () => {
              var res = await confrimShootingTaskAsync({ taskid: directImgTaskId, index: index });
              if (res?.success) {
                that.$message({ message: '完成成功', type: "success" });
                await that.editTask({ directImgTaskId: directImgTaskId });
                await that.onCloseAddForm(0);

              }
            });
          break;
        case "取消确认":
          this.$confirm("确认取消, 是否继续?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(async () => {
              var res = await unConfrimShootingTaskAsync({ taskid: directImgTaskId, index: index });
              if (res?.success) {
                that.$message({ message: '取消成功', type: "success" });
                await that.editTask({ directImgTaskId: directImgTaskId });
                await that.onCloseAddForm(0);
              }
            });
          break;
      }

    },
    //终止重启
    async onEndRestartAction() {
      this.$confirm("选中的任务将会重启，是否确定 ", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await endRestartActionAsync([this.addForm.directImgTaskId]);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.addForm.isend = 0;
          this.onCloseAddForm(2);
        }
      });
    },
    //终止
    async onEndShootingTaskAction() {
      this.$confirm("选中的任务将会终止，是否确定 ", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await endShootingTaskActionAsync([this.addForm.directImgTaskId]);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.addForm.isend = 1;
          this.onCloseAddForm(2);
        }
      });
    },
    // 删除操作
    async onDeleteShootingTaskAction() {
      if (this.islook) return;
      this.$confirm("选中的任务会移动到回收站，是否确定执行", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await deleteShootingTaskActionAsync([this.addForm.directImgTaskId]);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onCloseAddForm(3);
        }
      });
    },
    //任务标记
    async onSignShootingTaskAction() {
      this.$confirm("标记选中任务，是否确定执行", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await signShootingTaskActionAsync([this.addForm.directImgTaskId]);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.addForm.isTopOldNum = 2;
          this.onCloseAddForm(2);
        }
      });
    },
    // 取消标记
    async onUnSignShootingTaskAction() {
      this.$confirm("取消标记任务，是否确定执行", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await unSignShootingTaskActionAsync([this.addForm.directImgTaskId]);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.addForm.isTopOldNum = 0;
          this.onCloseAddForm(2);
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item {
  margin: 0px !important;
}

::v-deep .el-form-item__error {
  position: absolute !important;
  top: 30% !important;
  left: 400px !important;
  width: 60px !important;
}

::v-deep .mycontainer {
  padding: 0px 5px !important;
  height: 100% !important;
}

::v-deep .bzbjrw {
  width: 750px;
  background-color: #fff;
}

::v-deep .bzbjrw .bzbjbt {
  height: 60px;
  background-color: rgb(255, 255, 255);
  font-size: 18px;
  color: #666;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 20px 35px;
}

::v-deep .bzbjrw .bzbjbt i {
  color: #999;
}

::v-deep .bzbjrw .bzbjbt i {
  margin-left: 8px;
  line-height: 26px;
}

::v-deep .bzbjrw .bzbjbt i:hover {
  margin-left: 8px;
  line-height: 26px;
  color: #409eff;
  position: relative;
  top: -2px;
}

::v-deep .bzbjrw .rwmc {
  width: 750px;
  height: 70px;
  background-color: rgb(255, 255, 255);
  float: left;
  box-shadow: 0px 3px 5px #eeeeee;
  box-sizing: border-box;
  padding: 0 56px;
}

::v-deep .bzbjrw .rwmc .xh {
  height: 65px;
  font-size: 18px;
  line-height: 68px;
  box-sizing: border-box;
  padding: 0 2px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #666;
}

::v-deep .bzbjrw .rwmc .mc,
::v-deep .icon {
  height: 65px;
  font-size: 18px;
  line-height: 68px;
  box-sizing: border-box;
  margin-left: 10px;
  padding: 0 2px;
  display: inline-block;
  color: #666;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::v-deep .bzbjrw .bzbjlx {
  width: 100%;
  height: 35px;
  /* background-color: bisque; */
  box-sizing: border-box;
  padding: 0 60px;
}

::v-deep .bzbjrw .bzbjlxf {
  width: 100%;
  /* background-color: bisque; */
  box-sizing: border-box;
  padding: 0 60px;
}

::v-deep .bzbjrw .bzbjlx .lxwz {
  width: 112px;
  font-size: 14px;
  color: #666;
  vertical-align: top;
  line-height: 26px;
  /* background-color: rgb(204, 204, 255); */
  display: inline-block;
}

::v-deep .bzbjrw .scpd {
  width: 100px;
  font-size: 14px;
  color: #666;
  line-height: 36px;
  /* background-color: rgb(204, 204, 255); */
  float: left;
}

::v-deep .bzbjrw .zjck {
  display: inline-block;
  float: right;
  position: relative;
  top: 5px;
}

::v-deep .bzbjrw .bzczxx {
  width: 100%;
  box-sizing: border-box;
  /* padding: 0 60px; */
  text-align: center;
  border: 1px solid #dcdfe6;
  border-right: 0px;
  border-bottom: 0px;
  border-left: 0px;
  margin: 0 0;
}

::v-deep .bzbjrw .qxtj {
  height: 30px;
  /* background-color: aquamarine; */
  box-sizing: border-box;
  padding: 10px 60px;
}

::v-deep .bzbjrw .bzsccg {
  width: 100%;
  box-sizing: border-box;
  padding: 5px 60px;
  margin-bottom: 30px;
}

::v-deep .bzbjrw .bzczx {
  box-sizing: border-box;
  padding: 5px 60px;
  font-size: 14px;
  color: #666;
}

::v-deep .bzbjrw .bzscpd .bzczan,
.bzczs,
.bzczan,
.bzczmz,
.bzczsj {
  min-width: 50px;
  max-width: 150px;
  display: inline-block;
  margin-right: 10px;
  /* background-color: aquamarine; */
}

::v-deep .bzbjrw .bzczsj {
  color: #999;
}

::v-deep .bzbjrw .bzczk {
  height: 35px;
}

::v-deep .bzbjrw .bzczksps {
  width: 14%;
  display: inline-block;
}

::v-deep .bzbjrw .bzczkzy {
  width: 43%;
  /* background-color: aqua; */
  display: inline-block;
}

::v-deep .bzbjrw .bzczs {
  width: 65px;
}

::v-deep .bzbjrw .bzbjfgx {
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 25px 0;
}

::v-deep .bzbjrw .bzczrz {
  box-sizing: border-box;
  padding: 10px 60px;
}

::v-deep .bzbjrw .bzczrzx {
  box-sizing: border-box;
  padding: 10px 60px;
}

::v-deep .bzbjrw .rztx,
.rzmz,
.rzxgx,
.rzxgsj {
  height: 30px;
  display: inline-block;
  font-size: 14px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::v-deep .bzbjrw .rztx {
  width: 25px;
  height: 25px;
  background-color: #f5f5f5;
  border-radius: 50%;
  margin-right: 15px;
}

::v-deep .bzbjrw .rzmz {
  width: 50px;
  margin-right: 5px;
}

::v-deep .bzbjrw .rzxgx {
  max-width: 200px;
  margin-right: 10px;
  color: #999;
}

::v-deep .bzbjrw .rzxgsj {
  max-width: 200px;
  color: #999;
}

::v-deep .bzbjrw .rzxgq,
.rzxgnr {
  max-width: 450px;
  line-height: 15px;
  display: inline-block;
  font-size: 12px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::v-deep .bzbjrw .rzxgq {
  width: 50px;
  margin-left: 43px;
  margin-right: 2px;
}

::v-deep ::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}

::v-deep .bzbjrw .bzczksps {
  width: 15%;
  display: inline-block;
}

::v-deep .bzbjrw .bzczkz {
  width: 38%;
  /* background-color: aqua; */
  display: inline-block;
}

::v-deep .bzbjrw .bzczky {
  width: 42%;
  /* background-color: aqua; */
  display: inline-block;
}

.floateRow {
  display: flex;
  flex-direction: row;
  overflow-wrap: break-word;
  align-items: center;
  flex-wrap: wrap;
  font-size: 14px;
  color: #666;
}

.floateRow .itemlabel {
  margin-left: 60px;

}

.floateRow .itemvalue {
  margin-left: -33px;
  width: 85%;
}
</style>
