<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker style="width: 240px" v-model="timeRange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" 
          range-separator="至" start-placeholder="起始日期" end-placeholder="结束日期" :picker-options="pickerOptions" clearable >
        </el-date-picker>
        <el-input v-model="filter.ticketID" v-model.trim="filter.ticketID" placeholder="优惠券ID(多个ID，隔开)" style="width:180px;" clearable ></el-input>
        <el-select v-model="filter.shopName" placeholder="店铺搜索" multiple clearable collapse-tags filterable style="width:200px;">
          <el-option v-for="item in shopNameList" :key="item" :label="item" :value="item"></el-option>
        </el-select>
        <el-select v-model="filter.groupID" placeholder="运营组搜索" multiple clearable collapse-tags filterable style="width:200px;">
          <el-option v-for="item in groupNameList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onExport">导出</el-button>
        <el-button type="primary" @click="onImport">导入</el-button>
      </div>
    </template>
    
    <vxetablebase :id="'RedEnvelopePercentage202410031609'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' 
        @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" 
        :summaryarry="summaryarry" :showsummary='true' style="width: 100%;margin: 0" :loading="listLoading" 
        :height="'100%'" :treeProp="{ rowField: 'id', parentField: 'parentId' }">
    </vxetablebase>
    
    <!--分页-->
    <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入红包占比数据" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
      <el-row>
          <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
              <el-upload ref="upload" :auto-upload="false" :multiple="false" action
                  accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                  <template #trigger>
                      <el-button size="small" type="primary">选取文件</el-button>
                  </template>
                  <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                      @click="submitupload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
              </el-upload>
          </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
  </el-dialog>
  
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/vxetablebase.vue";
import { getRedEnvelopePercentage, exportRedEnvelopePercentage, importRedEnvelopePercentage } from "@/api/pddplatform/RedEnvelopePercentage"
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import dayjs from 'dayjs'

const tableCols = [
  { sortable: 'custom', istrue: true, width: '200', align: 'center', label: '日期', prop: 'date' },
  { istrue: true, width: '200', align: 'center', label: '优惠券ID', prop: 'ticketID' },
  { sortable: 'custom', istrue: true, width: '200', align: 'center', label: '店名', prop: 'shopName', treeNode: true },
  { sortable: 'custom', istrue: true, width: '200', align: 'center', label: '运营组', prop: 'groupName' },
  { sortable: 'custom', istrue: true, width: '200', align: 'center', label: '现金总收入', prop: 'cashTotalIncome' },
  { sortable: 'custom', istrue: true, width: '200', align: 'center', label: '红包总收入', prop: 'redEnvelopeTotalIncome' },
  { sortable: 'custom', istrue: true, width: '200', align: 'center', label: '总支出', prop: 'totalIncome' },
  { sortable: 'custom', istrue: true, width: '200', align: 'center', label: '红包占比', prop: 'redEnvelopePercentage' }
];

export default {
  name: "redEnvelopePercentage",
  components: { MyContainer, vxetablebase, getRedEnvelopePercentage, exportRedEnvelopePercentage, importRedEnvelopePercentage, getDirectorGroupList, getshopList },
  data() {
    return {
      that: this,
      listLoading: false,//查询过程
      dialogVisible: false,//上传对话框
      uploadLoading: false,//上传过程
      timeRange: [],//日期
      shopNameList: [],//店铺列表
      groupNameList: [],//运营组列表
      filter: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        //过滤条件
        startDate: null,//开始日期
        endDate: null,//结束日期
        ticketID: null,//优惠券ID
        shopName: [],//店铺
        groupID: [],//运营组
      },
      fileList: [],//导入文件
      tableCols: tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      pager: { orderBy: "date", isAsc: false },
      pickerOptions: {
        shortcuts: [{
        text: '前一天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
          }
        }, {
        text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            end.setTime(end.getTime());
            picker.$emit('pick', [start, end]);
          }
        }, {
        text: '近一个月',
          onClick(picker) {
            const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
            const date2 = new Date(); date2.setDate(date2.getDate());
            picker.$emit('pick', [date1, date2]);
          }
        }]
      },
    }
  },
  async mounted() {
    this.init();
    this.getList();
  },
  methods: {
    //初始化
    async init() {
        let end = new Date();
        let start = new Date();
        start.setTime(start.getTime());
        end.setTime(end.getTime());
        
        this.timeRange = [start, end];
        this.filter.startDate = start;
        this.filter.endDate = end;
      //获取店铺列表
      const shopName  = await getshopList({ platform: null, CurrentPage: 1, PageSize: 100000 });
      this.shopNameList = Array.from(
        new Set(
          shopName.data.list
          .map(x => x.shopName)
          .filter(name => name.includes('拼多多-'))
        )
      );//去重，取拼多多店铺
      //获取运营组列表
      const groupName = await getDirectorGroupList({})
      // this.groupNameList = (groupName.data || []);
      this.groupNameList = groupName.data.map(item => { return { value: item.key, label: item.value } });
    },
    sortchange(column) {
      if (column.order) {
        this.filter.orderBy = column.prop;
        this.filter.isAsc = column.order.indexOf("descending") == -1 ? true : false;
        this.getList();
      }
    },
    changeTime(e) {
      const formatData = date => {
        if (!date) return null;
        const localDate = new Date(date);
        const offset = localDate.getTimezoneOffset();
        return new Date(localDate.getTime() + offset * 60000).toISOString().split('T')[0];
      };
      this.filter.startDate = formatData(e[0]);
      this.filter.endDate = formatData(e[1]);
    },
    //每页数量改变
    Sizechange(val) {
      this.listLoading = true;
      this.filter.currentPage = 1;
      this.filter.pageSize = val;
      this.getList();
      this.listLoading = false;
    },
    //当前页改变
    Pagechange(val) {
      this.filter.currentPage = val;
      this.getList();
    },
    //查询
    onSearch() {
      //点击查询时才将页数重置为1
      this.filter.currentPage = 1;
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      this.filter.startDate = this.timeRange ? this.timeRange[0] : null;
      this.filter.endDate = this.timeRange ? this.timeRange[1] : null;
      this.listLoading = true;
      const { data, success } = await getRedEnvelopePercentage(this.filter);
      this.listLoading = false;
      if (success) {
        this.tableData = data.list;
        this.tableData.forEach(item => {
          if ('0' == item.parentId) {
            item.date = dayjs(item.date).format('YYYY-MM-DD');
          }
        })
        this.total = data.total;
        this.summaryarry = data.summary;
      } else {
        this.$message.error("获取红包占比数据失败");
      }
    },
    //导出
    async onExport() {
      this.listLoading = true;
      const res = await exportRedEnvelopePercentage(this.filter);
      this.listLoading = false;
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute("download", '红包占比_' + new Date().toLocaleString() + '.xlsx');
      aLink.click();
    },
    // 打开上传弹窗
    onImport() {
      this.dialogVisible = true;
      this.uploadLoading = false;
      this.$nextTick(() => {
          if (this.$refs.upload) {
              this.$refs.upload.clearFiles();
          }
      });
      this.fileList.splice(0, 1);
    },
    // 上传文件
    async uploadFile(item) {
      this.uploadLoading = true;
      const form = new FormData();
      form.append("upfile", item.file);
      const res = await importRedEnvelopePercentage(form);
      if (res?.success) {
        this.$message({message: '上传成功,正在导入中...', type: "success"});
      }
    },
    // 更改上传文件
    async uploadChange(file, fileList) {
      if (fileList.length == 2) {
          fileList.splice(1, 1);
          this.$message({ message: "只允许单文件导入", type: "warning" });
          return false;
      }
      this.fileList.push(file);
    },
    // 移除上传文件
    uploadRemove() {
        this.fileList.splice(0, 1);
    },
    // 提交上传文件
    async submitupload() {
      if (this.fileList.length == 0) {
          this.$message.warning('您没有选择任何文件！');
          return;
      }
      this.$refs.upload.submit();
      this.$refs.upload.clearFiles();
      this.fileList.splice(0, 1);
      this.dialogVisible = false;
      this.getList();
    },
  }
};
</script>
<style lang="scss" scoped>
  .top {
      display: flex;
      margin-bottom: 10px;
  }

  .publicCss {
      width: 200px;
      margin-right: 10px;
  }

  //解决下拉菜单多选由文字太长导致样式问题
  ::v-deep .el-select__tags-text {
  max-width: 60px;
  }
</style>