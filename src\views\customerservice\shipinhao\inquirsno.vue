<template>
  <my-container v-loading="pageLoading">
    <!-- 顶部操作 -->
    <template #header>
      <el-button style="padding: 0;margin: 0;border: none;">
        <el-select v-model="filter.shopCodeList" placeholder="店铺" filterable multiple clearable collapse-tags style="width: 180px;">
          <el-option v-for="item in filterShopList" :key="item.shopCode" :label="item.shopName"
            :value="item.shopCode"></el-option>
        </el-select>
      </el-button>
      <el-button style="padding: 0;margin: 0;border: none;">
        <el-input v-model.trim="filter.snick" placeholder="昵称" style="width:160px;" clearable :maxlength="50" />
      </el-button>
      <el-button style="padding: 0;margin: 0;border:none">
        <el-date-picker style="width: 280px" v-model="filter.sdate" type="daterange" format="yyyy-MM-dd"
          value-format="yyyy-MM-dd" range-separator="至" start-placeholder="起始支付日期" end-placeholder="结束支付日期"
          :picker-options="pickerOptions" @change="handleDateChange" :clearable="false">
        </el-date-picker>
      </el-button>
      <el-button type="primary" @click="onSearch">查询</el-button>
    </template>

    <!-- 列表 -->
    <vxetableNotFixNum ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      @select='selectchange' :isSelection='false' :tableData='data.list' :tableCols='tableCols' :loading="listLoading" />

    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="data.total" :checked-count="sels.length" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
    
    <el-dialog title="添加客服人员分组管理信息" :visible.sync="addgroupdialogVisible" width="25%" label-width="120px" v-dialogDrag>
      <el-form ref="refaddForm" :model="addForm" :rules="formRules">
        <el-form-item prop="groupType" label="分类（售前/后）">
          <el-radio-group v-model="addForm.groupType">
            <el-radio :label=0>售前</el-radio>
            <el-radio :label=1>售后</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="groupName" label="分组">
          <el-input style="width: 80%;" v-model="addForm.groupName" :maxlength="20" show-word-limit clearable ></el-input>
        </el-form-item>
        <el-form-item prop="groupManager_DDid" label="组长">
          <YhUserelector v-if="addgroupdialogVisible" style="width: 80%;" :value.sync="addForm.groupManager_DDid" :text.sync="addForm.groupManager" @change="addManageChange"></YhUserelector>
        </el-form-item>
        <el-form-item prop="sname_DDid" label="姓名">
          <YhUserelector v-if="addgroupdialogVisible" style="width: 80%;" :value.sync="addForm.sname_DDid" :text.sync="addForm.sname" @change="addSnameChange"></YhUserelector>
        </el-form-item>
        <el-form-item prop="snick" label="昵称">
          <el-input style="width: 80%;" v-model="addForm.snick" :maxlength="20" show-word-limit clearable :disabled=true></el-input>
        </el-form-item>
        <el-form-item prop="shopCode" label="店铺">
          <el-select style="width: 80%;" v-model="addForm.shopCode" placeholder="店铺" @change="addShopChange" filterable clearable :disabled=true>
            <el-option v-for="item in filterShopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="phoneNo" label="手机号">
          <el-input style="width: 80%;" v-model="addForm.phoneNo" :maxlength="20" show-word-limit clearable ></el-input>
        </el-form-item>
        <el-form-item prop="joinDate" label="入组日期">
          <el-date-picker v-model="addForm.joinDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
            type="date" style="width:80%" placeholder="选择日期" clearable >
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="leaveDate" label="离组日期">
          <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="addForm.leaveDate"
            style="width:80%" type="date" placeholder="选择日期" clearable >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <el-button @click="addgroupdialogVisible=false">关闭</el-button>
      <el-button @click="addGroup">确定</el-button>
    </el-dialog>
    
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import vxetableNotFixNum from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import YhUserelector from '@/components/YhCom/yh-userselector.vue';
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser';
import { formatTime } from "@/utils";
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import { getSPHInquirsNoExistsList, addGroup } from '@/api/customerservice/shipinhaoinquirs.js';

const tableCols = [
  {
    istrue: true, type: "button", label: '操作', width: "120", btnList: [
      { label: "生成分组", handle: (that, row) => that.onAddGroupShow(row) },
    ]
  },
  { sortable: 'custom', istrue: true, width: '180', align: 'center', label: '店铺', prop: 'shopName' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '日期', prop: 'sdate', formatter: (row) => formatTime(row.sdate, 'YYYY-MM-DD') },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '客服昵称', prop: 'snick' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '咨询用户数', prop: 'inquirsCount' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '咨询会话数', prop: 'receiveCount' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '3分钟人工回复率', prop: 'threeSecondReplyRate', formatter: (row) => row.threeSecondReplyRate.toFixed(2) + "%" },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '平均回复时长（秒）', prop: 'responseTime' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '用户满意度', prop: 'satisDegree', formatter: (row) => row.satisDegree.toFixed(2) + "%" },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '不回复率', prop: 'noReplyRate', formatter: (row) => row.noReplyRate.toFixed(2) + "%" },
  { sortable: 'custom', istrue: true, width: '150', align: 'center', label: '导入时间', prop: 'createdTime' },
  { sortable: 'custom', istrue: true, width: '180', align: 'center', label: '批次号', prop: 'batchNumber' },
];

export default {
  name: "inquirsno", 
  components: { MyContainer, vxetablebase, YhUserelector, vxetableNotFixNum }, 
  data() { 
    return {
      that: this,
      pageLoading: false,
      listLoading: false,
      addgroupdialogVisible: false,
      filter: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        //过滤条件
        shopCodeList: [],
        snick: '',
        sdate: [],
        startDate: '',
        endDate: '',
        inquirsType: 0,
      },
      addForm: {
        groupType: 0,
        groupName: '',
        groupManager_DDid: '',
        groupManager: '',
        sname_DDid: '',
        sname: '',
        snick: '',
        shopCode: '',
        shopID: '',
        shopName: '',
        phoneNo: '',
        joinDate: '',
        leaveDate: '',
      },
      formRules: {
        groupName: [{ required: true, message: '请输入分组', trigger: 'change' }],
        groupManager_DDid: [{ required: true, message: '请输入组长', trigger: 'change' }],
        groupManager: [{ required: true, message: '请输入组长', trigger: 'change' }],
        sname_DDid: [{ required: true, message: '请输入姓名', trigger: 'change' }],
        sname: [{ required: true, message: '请输入姓名', trigger: 'change' }],
        snick: [{ required: true, message: '请输入昵称', trigger: 'change' }],
        shopCode: [{ required: true, message: '请输入店铺', trigger: 'change' }],
        joinDate: [{ required: true, message: '请输入入组日期', trigger: 'change' }],
        leaveDate: [{ required: true, message: '请输入离组日期', trigger: 'change' }],
      },
      sels: [],// 列表选中列
      data: {},// 查询返回数据集
      tableCols: tableCols,
      filterShopList: [],// 店铺选择器列表
      pickerOptions: {
        shortcuts: [{
          text: '前一天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            end.setTime(end.getTime());
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近一个月',
          onClick(picker) {
            const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
            const date2 = new Date(); date2.setDate(date2.getDate());
            picker.$emit('pick', [date1, date2]);
          }
        }]
      },
    };
  },
  async mounted() {
    //默认昨天日期
    let start = new Date();
    let end = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
    end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
    this.filter.sdate = [start, end];
    this.filter.startDate = start;
    this.filter.endDate = end;
    //获取店铺列表
    await this.getSPHShop();
    this.onSearch();
  },
  methods: {
    //店铺选择器
    async getSPHShop() {
      let res = await getshopList({ platform: 20, CurrentPage: 1, PageSize: 100000 });
      this.filterShopList = res.data.list;
    },
    selectchange: function (rows, row) {
      this.sels = [];
      rows.forEach(f => {
        this.sels.push(f);
      });
    },
    // 检查是否选择时间
    handleDateChange() {
      this.filter.startDate = this.filter.sdate ? this.filter.sdate[0] : null;
      this.filter.endDate = this.filter.sdate ? this.filter.sdate[1] : null;
    },
    //每页数量改变
    Sizechange(val) {
      this.filter.currentPage = 1;
      this.filter.pageSize = val;
      this.getList();
    },
    //当前页改变
    Pagechange(val) {
      this.filter.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.filter.orderBy = prop
        this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async getList() {
      this.listLoading = true;
      this.handleDateChange();
      try {
        const { data, success } = await getSPHInquirsNoExistsList(this.filter);
        if (success) {
          this.data = data;
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.listLoading = false;
      }
    },
    //查询分组
    onSearch() {
      //点击查询按钮时才将页数重置为1
      this.filter.currentPage = 1;
      this.$refs.pager.setPage(1);
      this.getList();
    },
    //打开添加弹窗
    onAddGroupShow(row) {
      this.addgroupdialogVisible = true;
      this.$nextTick(() => {
        this.$refs.refaddForm.clearValidate(); // 清除上次的校验结果
        this.$refs.refaddForm.resetFields();   // 重置表单数据
        this.addForm.groupManager = '';
        this.addForm.sname = '';
        this.$forceUpdate(); // 强制更新视图
        this.addForm.snick = row.snick;
        this.addForm.shopCode = row.shopCode;
        this.addForm.shopID = row.shopID;
        this.addForm.shopName = row.shopName;
      });
    },
    //添加分组-组长选择
    async addManageChange() {
      var res = await QueryAllDDUserTop100({ keywords: this.addForm.groupManager_DDid });
      if (res?.success) {
        this.addForm.groupManager = res.data[0].userName;
      }
      console.log(this.addForm);
    },
    //添加分组-姓名选择
    async addSnameChange() {
      var res = await QueryAllDDUserTop100({ keywords: this.addForm.sname_DDid });
      if (res?.success) {
        this.addForm.sname = res.data[0].userName;
      }
    },
    //添加分组-店铺选择
    addShopChange() {
      if (null == this.addForm.shopCode) {
        this.addForm.shopName = null;
        this.addForm.shopID = null;
        return;
      }
      var shop = this.filterShopList.find(f => f.shopCode == this.addForm.shopCode);
      this.addForm.shopName = shop.shopName;
      this.addForm.shopID = shop.platformShopID;
    },
    //添加分组
    async addGroup() {
      var that = this;
      this.$refs.refaddForm.validate(async valid => {
        if (valid) {
          var res = await addGroup(that.addForm);
          if (res?.success) {
            this.$message({ message: '已添加', type: "success" });
            this.addgroupdialogVisible = false;
          }
        } else { 
          // 表单验证失败，提示用户
          this.$message.error('请检查填写的信息!');
        }
      });
    },
  }
}
</script>
<style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }

  .levelQueryInfoBox {
    display: flex;
    margin-bottom: 10px;
  }

  .publicCss {
    width: 220px;
    margin-right: 10px;
  }

  //解决下拉菜单多选由文字太长导致样式问题
  ::v-deep .el-select__tags-text {
    max-width: 60px;
  }
</style>
