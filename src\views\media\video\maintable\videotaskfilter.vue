<template>
  <!-- 筛选 操作 -->
  <el-button-group>
    <div class="ssanc">
      <div style="width:20%;display: inline-block;text-align: left;">
        <span style="margin-right:0.8%;">
          <el-input style="width:30%;" placeholder="任务编号" v-model="filter.videoTaskId"
            oninput="value=value.replace(/[^\d]/g,'')" maxlength="9"></el-input>
        </span>
        <span style="margin-right:0.8%;">
          <el-input style="width:66.4%;" v-model.trim="filter.productShortName" :maxlength=100 placeholder="产品简称"
            @keyup.enter.native="onSearch" clearable />
        </span>
      </div>
      <div style="width:25%;display: inline-block;">
        <span style="margin-left:10px;">
          <el-button style="width:90px;" type="primary" @click="onSearch">查询</el-button>
        </span>
        <span style="margin-left:5px;">
          <el-button @click="onclear">重置</el-button>
        </span>
      </div>
      <div style="width:55%;display: inline-block;text-align: right;">
        <span v-if="!islook" style=" margin-left:20px;padding:0 2px;">
          <el-button size="mini" style="width:100px;border: 1px solid #b3d8ff;" type="primary" plain @click="onAddTask"
            v-if="checkPermission('api:media:vediotask:AddOrUpdateVideoTaskAsync') && listtype == 1"><i
              class="el-icon-plus"></i>&nbsp;创建任务</el-button>
        </span>
        <span v-if="!islook" style=" margin-left:5px;">
          <el-button style="height: 28px;" type="primary" @click="onAddOrder"
            v-if="checkPermission('api:media:vediotask:VedioTaskAddOrderSave') && listtype == 1">下单发货</el-button>
        </span>
        <span style=" margin-left:6px;">
          <el-dropdown v-if="checkPermission('vedioTask-plcz')" size="mini" split-button type="primary"
            icon="el-icon-share" @command="handleCommand"> 批量操作 <el-dropdown-menu slot="dropdown">
              <el-dropdown-item class="Batcoperation" command="x" style="height:10px;"></el-dropdown-item>
              <el-dropdown-item class="Batcoperation" v-if="listtype == 1" command="n">批量分配</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" v-if="listtype == 1" command="a">批量重启</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" v-if="listtype == 1" command="b">批量终止</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" v-if="(listtype == 1)" command="c">批量标记</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" v-if="(listtype == 1)" command="d">取消标记</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" v-if="(listtype == 1)" command="e">批量删除</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" v-if="(listtype == 3)" command="g">批量统计</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" v-if="(listtype == 4 || listtype == 3)"
                command="j">批量存档</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" v-if="(listtype == 5)" command="k">取消存档</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" v-if="(listtype == 4)" command="l">取消统计</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" v-if="(listtype == 2)" command="m">批量完成</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" v-if="listtype == 1" command="o">批量到货</el-dropdown-item>
              <!--  <el-dropdown-item class="Batcoperation" v-if="(listtype==8) "                   
                    command="n">任务重启</el-dropdown-item> -->
              <el-dropdown-item class="Batcoperation" command="x" style="height:10px;"></el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </span>
        <span style="box-sizing: border-box; margin-left:6px;">
          <el-button type="primary" @click="onExport" v-if="checkPermission('vedioTask-dc')">导出</el-button>
        </span>
        <span style="margin-left:6px;">
          <el-checkbox style="width: 111px;position: relative;top: 1px;" v-if="(listtype == 1 || listtype == 2)"
            v-model="filter.isComplateCheck" :checked-value="1" :unchecked-value="0" border>隐藏已完成</el-checkbox>
        </span>
        <span>
          <el-radio-group style="margin-left:6px;" size="mini" v-if="checkPermission('shootingDropDownList')"
            v-model="onCommand" @change="ShowHideonSearch">
            <el-radio-button label="b">默认</el-radio-button>
            <el-radio-button label="a">全部</el-radio-button>
          </el-radio-group>
        </span>
      </div>
    </div>
    <div class="heardcss">
      <span>
        <el-select style="width: 5%;" v-model="filter.isTopOld" :clearable="true" placeholder="是否标记">
          <el-option label="是" value="1"></el-option>
          <el-option label="否" value="0"></el-option>
        </el-select>
      </span>
      <span>
        <el-select style="width: 5%;" v-model="filter.isAllAudioStatus" :clearable="true" placeholder="是否完成">
          <el-option label="是" value="1"></el-option>
          <el-option label="否" value="0"></el-option>
        </el-select>
      </span>
      <span>
        <el-select style="width: 5%;" v-model="filter.hasConfirmTime" @keyup.enter.native="onSearch" :clearable="true"
          placeholder="是否剪辑">
          <el-option label="是" value="1"></el-option>
          <el-option label="否" value="0"></el-option>
        </el-select>
      </span>
      <span>
        <el-select style="width: 11.8%;" v-model="filter.warehouse" placeholder="拍摄样品" multiple :collapse-tags="true"
          clearable>
          <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </span>
      <span>
        <el-select style="width: 10%;" v-model="filter.DockingPeoples" placeholder="分配查询" multiple :collapse-tags="true"
          filterable clearable>
          <el-option v-for="(item,index ) in fpPhotoLqNameList" :key="index+'ww'" :label="item.label"
            :value="item.label"></el-option> 
        </el-select>
      </span>
      <span>
        <el-select style="width: 5%;" v-model="filter.approvedstatus" placeholder="审核状态"  :collapse-tags="true"
          filterable clearable>
          <el-option label="未传" value="-"/>
            <el-option label="待审" value="1"/>
            <el-option label="通过" value="2"/>
            <el-option label="补拍" value="3"/>
            <el-option label="重拍" value="4"/>
        </el-select>
      </span>
      <span>
        <el-select style="width:5%;" v-model="filter.operationGroup" :clearable="true" placeholder="运营组" filterable>
          <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </span>
      <span>
        <el-select style="width: 5%;" v-model="filter.cuteLqName" :clearable="true" placeholder="负责人" filterable>
          <el-option v-for="(item,index ) in erpUserInfoList" :key="index+'tw'" :label="item.label"
            :value="item.label"></el-option>
        </el-select>
      </span>
      <span>
        <el-select style="width:6.5%;" v-model="filter.platform" placeholder="平台"   :collapse-tags="true" clearable
          @change="onchangeplatform">
          <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </span>
      <span>
        <el-select style="width:12.8%;" filterable v-model="filter.shopName" placeholder="店铺" clearable>
          <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
        </el-select>
      </span>
      <span>
        <el-select style="width:5.5%;" v-model="filter.searchTimeType" placeholder="选择时间" filterable clearable>
          <el-option label="拍摄时间" value="1"></el-option>
          <el-option label="审核时间" value="2"></el-option>
          <el-option label="剪辑时间" value="3"></el-option>
          <el-option label="创建时间" value="4"></el-option>
        </el-select>
      </span>
      <span>
        <el-date-picker style="width:20%;position: relative;top:1px;" type="daterange" format="yyyy-MM-dd"
          value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开启日期" end-placeholder="结束日期"
          v-model="filter.createdtimerange" @change="changedatebefore" />
      </span>
      <span>
      </span>
    </div>
  </el-button-group>
</template>
<script>
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import { getErpShootDeptUserInfo, getErpUserInfoView } from '@/api/media/mediashare';
export default {
  props: {
    fpPhotoLqNameList:{ type: Array, default: () => { return []; } },
    erpUserInfoList:{ type: Array, default: () => { return []; } },
    platformList: { type: Array, default: () => { return []; } }, //平台
    warehouselist: { type: Array, default: () => { return []; } }, //仓库 
    groupList: { type: Array, default: () => { return []; } }, //运营组 
    taskUrgencyList: { type: Array, default: () => { return []; } },
    islook: { type: Boolean, default: true }, //平台 
    //批量操作方法 
    listtype: { type: Number, default: 99 },
    //1 任务,2 已完成,3 确认信息,4 已使用,5 统计列表,6 信息存档,7存档,8回收站
  },
  data() {
    return {
      shopList: [],
      onCommand: "b",
      filter: {
        searchTimeType: null,
        productShortName: '',
        videoTaskId: undefined,
        hasOverTime: '',
        hasConfirmTime: '',
        shopName: null,//店铺
        operationGroup: null,//运营组
        hasOverTime: null,//完成时间
        hasConfirmTime: null,//确认时间
        warehouse: null,//仓库
        DockingPeoples: [],//分配人
        cuteLqName: null,//对接人
        approvedstatus:null,
        platform: null,//平台
        izcjdz: [],
        packClass: null,
        brand: null,
        isComplateCheck: true
      },
      //erpUserInfoList: [],
      //fpPhotoLqNameList: [],
    };
  },
  async mounted() {
    //await this.geterpuser();
  },
  methods: {
    async geterpuser() {
      var res = await getErpShootDeptUserInfo();
      this.fpPhotoLqNameList = res || [];

      var res = await getErpUserInfoView();
      this.erpUserInfoList = res || [];
    },
    onSearch() {
      if (this.filter.createdtimerange) {
        this.filter.createdstartTime = this.filter.createdtimerange[0];
        this.filter.createdendTime = this.filter.createdtimerange[1];
      } else {
        this.filter.createdstartTime = null;
        this.filter.createdendTime = null;
      }
      this.filter.isComplateChecked = this.filter.isComplateCheck == true ? 0 : 1;
      this.$emit('topSearch', this.filter)
    },
    onAddTask() {
      this.$emit('onAddTask')
    },
    onAddOrder() {
      this.$emit('onAddOrder')
    },
    ShowHideonSearch() {
      this.$emit('ShowHideonSearch', this.onCommand)
    },
    handleCommand(command) {
      this.$emit('handleCommand', command)
    },
    onExport() {
      this.$emit('onExport')
    },
    onclear() {
      this.filter.productShortName = '';
      this.filter.videoTaskId = null;
      this.filter.isAllAudioStatus = '';
      this.filter.hasConfirmTime = '';
      this.filter.warehouse = [];
      this.filter.createdtimerange = [];
      this.filter.searchTimeType = null;
      this.filter.shopName = null;
      this.filter.platform = '';
      this.filter.operationGroup = null;
      this.filter.fpPhotoLqName = [];
      this.filter.brand = [];
      this.filter.packClass = [];
      this.filter.izcjdz = null;
      this.filter.isComplateCheck = true;
      this.filter.DockingPeoples = [];
      this.filter.cuteLqName = '';
      this.filter.approvedstatus= '';
    },
    changedatebefore() {
      if (this.filter.searchTimeType == null && this.filter.createdtimerange.length > 0) {

        this.$message({ message: this.$t('请先选择时间'), type: 'info' });
        this.filter.createdtimerange = [];
      }

    },
    async onchangeplatform(val) {
      var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100 });
      this.filter.shopName = "";
      this.shopList = res1.data.list;
    },
  }
};
</script>
<style lang="scss" scoped>
::v-deep .el-drawer__header {
  border: none !important;
  padding: 16px 24px 0 24px;
}

::v-deep .el-header {
  padding: 10px 5px !important;
}

.ssanc {
  width: 100%;
  height: 38px;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  display: inline-block;

}

.heardcss {
  width: 100%;
  min-width: 1150px;
  min-height: 35px;
  // background-color: aqua;
  box-sizing: border-box;
  display: inline-block;
  margin-top: 8px;
}


.heardcss span,
.ssanc span {
  padding: 4px 0.2% 4px 0;
}


::v-deep .el-button {
  margin: 1px 1px !important;
}

.marginone {
  margin: 1px 1px 1px 0px;
}

::v-deep span .el-radio-button__inner {
  line-height: 14px !important;
}

::v-deep .vxetablecss {
  width: 100%;
  margin-top: -20px !important;
}

::v-deep .vxetoolbar20221212 {
  top: 97px;
  right: 15px;
}


::v-deep .el-button-group>.el-button:last-child {
  margin-left: -2px !important;
}

::v-deep .vxe-header--row {
  height: 58px;
}

::v-deep .el-table__body-wrapper {
  height: 220px !important;
}

::v-deep .el-table__body-wrapper {
  overflow-y: auto;
}
</style>

