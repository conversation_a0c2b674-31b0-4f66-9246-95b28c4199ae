<template>
  <div style="padding:10px; postion:relative;height: 100%;">
    <!-- <el-card class="box-card">
      <template #header></template>
    </el-card>-->
    <div class="card-header2">
      <span style=" margin-right: 20px">用户-U盾</span>
      <span>
        <el-input
          placeholder="输入用户搜索"
          v-model.trim="params.filter.name"
          clearable
          style="width: 200px"
          @keydown.enter.native="search()"
        ></el-input>
      </span>
      <span>
        <el-button @click="search()" type="primary">搜索</el-button>
      </span>
      <span>
        <el-button @click="add" type="primary">添加用户</el-button>
      </span>
    </div>
    <div style="height: 89%;">
      <el-table :data="list" border tooltip-effect="dark myTooltips" height="100%">
        <el-table-column prop="nickName" label="用户" />
        <el-table-column prop="uShieldNo" label="U盾外壳号" />
        <el-table-column label="证书" show-overflow-tooltip>
          <template #default="{row}">
            <span>{{ row.uShieldCertificate }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="{row}">
            <el-button @click="edit(row)" type="primary">编辑</el-button>
            <el-button @click="del(row.id)" type="danger">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="page">
      <el-pagination
        background
        v-model:current-page="params.currentPage"
        v-model:page-size="params.pageSize"
        :page-sizes="[15, 30, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="dialogVisible"
      :title="form.id?'编辑':'添加'"
      width="30%"
    >
      <el-form ref="form" :model="form" label-width="100px" :rules="rules">
        <el-form-item label="用户" prop="userId">
          <el-select
            clearable
            :filter-method="filter"
            style="width: 100%;"
            v-model="form.userId"
            filterable
            placeholder="请选择"
          >
            <el-option
              v-for="item in person"
              :key="item.ddUserId"
              :label="item.userName+'/'+item.position+'/'+item.deptName"
              :value="item.userId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="U盾外壳号" prop="uShieldNo">
          <el-input v-model.trim="form.uShieldNo"></el-input>
        </el-form-item>
        <el-form-item label="证书" prop="uShieldCertificate">
          <el-input v-model.trim="form.uShieldCertificate" type="textarea" :rows="2"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="submit('form')">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  GetPage,
  SaveUserUShield,
  DelUserUShield
} from "@/api/admin/viewUShield.js";
import { QueryAllDDUserTop100 } from "@/api/admin/deptuser";
export default {
  name: "YunHanAdminUshield",
  data() {
    return {
      rules: {
        userId: [{ required: true, message: "请选择用户", trigger: "change" }],
        uShieldNo: [
          { required: true, message: "请输入U盾外壳号", trigger: "blur" }
        ],
        uShieldCertificate: [
          { required: true, message: "请输入证书编号", trigger: "blur" }
        ]
      },
      person: [],
      state: "",
      timeout: null,
      form: {
        userId: "",
        uShieldNo: "",
        uShieldCertificate: ""
      },
      dialogVisible: false,
      total: 0,
      params: {
        currentPage: 1,
        pageSize: 15,
        filter: {
          name: ""
        },
        currentDeptCode: "",
        currentCorpId: "",
        isContainChildDeptUser: true,
        dynamicFilter: {
          field: "",
          operator: 0,
          value: "",
          logic: 0,
          filters: []
        }
      },
      list: []
    };
  },
  mounted() {
    this.getPage();
    this.getPerson();
  },
  methods: {
    search() {
      this.getPage();
    },
    handleSizeChange(val) {
      this.params.currentPage = 1;
      this.params.pageSize = val;
      this.getPage();
    },
    handleCurrentChange(val) {
      this.params.currentPage = val;
      this.getPage();
    },
    filter(val) {
      this.getPerson({ keywords: val });
    },
    async getPerson(val) {
      const res = await QueryAllDDUserTop100(val);
      this.person = res.data;
    },
    async getPage() {
      const res = await GetPage(this.params);
      this.list = res.data.list;
      this.total = res.data.total;
    },
    del(id) {
      this.$confirm("确定要删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(async () => {
          await DelUserUShield({ id: id });
          this.getPage();
          this.$message({
            type: "success",
            message: "删除成功!"
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    add() {
      this.dialogVisible = true;
      this.form = {};
    },
    edit(row) {
      this.dialogVisible = true;
      // this.form.nickName = row.nickName;
      this.form = row;
      console.log(this.form);
    },
    submit(formName) {
      this.$refs[formName].validate(async valid => {
        if (valid) {
          const res = await SaveUserUShield(this.form);
          if (res.code == 1) {
            this.$message.success("添加成功");
            this.dialogVisible = false;
            this.getPage();
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.container {
  padding: 10px;
}
.card-header2 {
  // display: flex;
  // justify-content: space-between;
  // align-items: center;
  margin-bottom: 10px;
}
.card-header2 span {
  margin-right: 5px;
}
.text {
  font-size: 14px;
}
.page {
  float: right;
}
.item {
  margin-bottom: 18px;
}

.box-card {
  width: 66vw;
}
</style>

<style>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.el-textarea__inner {
  padding-left: 5px;
  padding-right: 5px;
  font-family: microsoft YaHei;
}
.myTooltips {
  max-width: 50%;
  max-height: 500px;
  overflow-y: auto;
}
</style>
