<template>
    <div style="margin-top: 5px;">
        <el-card class="box-card">
            <el-row :gutter="10">
                <el-col :span="24">
                    <el-form class="ad-form-query" :inline="true">
                        <el-form-item label="">
                                <el-select v-model="filter.candidateType" clearable placeholder="请选择类型"
                                        style="width: 100%;">
                                        <el-option label="邀约" :value="1"></el-option>
                                        <el-option label="面试" :value="2"></el-option>
                                        <el-option label="入职" :value="3"></el-option>
                                    </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="onSearch">查询</el-button>
                        </el-form-item>
                    </el-form>
                    <p>人群特征概述</p>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-card style="width: 100%;" shadow="never">
                        <div id="chart-ring1" style="width: 100%; height:300px; "></div>
                    </el-card>
                </el-col>
                <el-col :span="12">
                    <el-card style="width: 100%;" shadow="never">
                        <div id="chart-ring2" style="width: 100%; height:300px; "></div>
                    </el-card>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-card style="width: 100%;" shadow="never">
                        <div id="chart-ring3" style="width: 100%; height:300px; "></div>
                    </el-card>
                </el-col>
                <el-col :span="12">
                    <el-card style="width: 100%;" shadow="never">
                        <el-tabs v-model="activeName" style="height: calc(100% - 40px);">
                            <el-tab-pane label="省份分布" name="first1">
                                <div id="top-card-colunm1" style="width: 40vw; height:261px; "></div>
                            </el-tab-pane>
                            <el-tab-pane label="市级分布" name="first2">
                                <div id="top-card-colunm2" style="width: 40vw; height:261px; "></div>
                            </el-tab-pane>
                        </el-tabs>
                    </el-card>
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>
  
<script>
import * as echarts from "echarts";
import { getALLDDDeptTree } from '@/api/profit/personnel'
import bus from './bus.js';
import { hrHomePageCandidateAttrCharts } from "@/api/profit/hr"

export default {
    name: "topCard",//首页顶部内容
    data () {
        return {
            filter: {
                startDate: null,//
                endDate: null,
                deptId: null,//
                position:null,
                // dateGroupType: null,//
                candidateType:null,
            },
            ringChart1: null,
            ringOptions1: null,
            ringChart2: null,
            ringOptions2: null,
            ringChart3: null,
            ringOptions3: null,
            colunmChart1: null,
            colunmOptions1: null,
            colunmChart2: null,
            colunmOptions2: null,
            activeName: "first1",
            ringData1: [],
            ringData2: [],
            ringData3: [],
            colunmData1: [],
            colunmData2: [],
        };
    },
    created () {
    },
    destroyed () {
        window.removeEventListener('resize', this.handleResizeChart);
    },
    async mounted () {
        await bus.$on('filter', data => {
            for (const prop in data) {
                if (prop in this.filter) {
                    this.filter[prop] = data[prop];
                }
            }
            this.getData();
        })
        this.initChart();
        window.addEventListener('resize', this.handleResizeChart);

    },
    methods: {
        handledeptNodeClick (data) {
            // 筛选
            this.filter.department = data.name;
            this.filter.departmentId = data.dept_id;
            this.$refs.selectdepartmentCharat.blur();
        },
       
        // 初始化
        initChart () {
            //饼状图
            this.ringChart1 = echarts.init(document.getElementById('chart-ring1'))
            // 配置参数
            this.ringOptions1 = {
                tooltip: {
                    trigger: 'item',
                    valueFormatter: (value) =>  Number(value).toFixed(2)+'%'
                },
                title: {
                    show: true,
                    subtext: '性别分布',
                },
                series: [
                    {
                        name: '性别分布',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 1
                        },
                        label: {
                            show: true,
                            position: 'outside'
                        },

                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 20,
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: true
                        },
                        data: [
                            { value: 1048, name: '男' },
                            { value: 735, name: '女' },
                        ]
                    }
                ]
            }
            // 使用 setOption 方法将配置项设置为实例的属性
            this.ringChart1.setOption(this.ringOptions1);

            //饼状图
            this.ringChart2 = echarts.init(document.getElementById('chart-ring2'))
            // 配置参数
            this.ringOptions2 = {
                tooltip: {
                    trigger: 'item',
                    valueFormatter: (value) =>  Number(value).toFixed(2)+'%'
                },
                title: {
                    show: true,
                    subtext: '年龄分布',
                },
                series: [
                    {
                        name: '年龄分布',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 1
                        },
                        label: {
                            show: true,
                            position: 'outside'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 20,
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: true
                        },
                        data: [
                            { value: 1048, name: '18-28' },
                            { value: 735, name: '28-38' },
                            { value: 580, name: '38-48' },
                            { value: 484, name: '48-58' },
                            { value: 300, name: '58以上' }
                        ]
                    }
                ]
            }
            // 使用 setOption 方法将配置项设置为实例的属性
            this.ringChart2.setOption(this.ringOptions2);

            //饼状图
            this.ringChart3 = echarts.init(document.getElementById('chart-ring3'))
            // 配置参数
            this.ringOptions3 = {
                tooltip: {
                    trigger: 'item',
                    // valueFormatter: (value) =>  Number(value).toFixed(2)+'%'
                },
                title: {
                    show: true,
                    subtext: '学历分布',
                },
                series: [
                    {
                        name: '学历分布',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 1
                        },
                        label: {
                            show: true,
                            position: 'outside'
                        },

                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 20,
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: true
                        },
                        data: [
                            { value: 1048, name: '初中' },
                            { value: 735, name: '高中' },
                            { value: 580, name: '大专' },
                            { value: 484, name: '本科' },
                            { value: 300, name: '硕士' }
                        ]
                    }
                ]
            }
            // 使用 setOption 方法将配置项设置为实例的属性
            this.ringChart3.setOption(this.ringOptions3);

            this.colunmChart1 = echarts.init(document.getElementById('top-card-colunm1'))
            // let colorList = ['#468FF7', '#00FED2', '#FFD37A', '#468FF7', '#00FED2', '#FFD37A']
            // 配置参数
            this.colunmOptions1 = {
                // color: colorList,
                // backgroundColor: '#000416',
                tooltip: {
                    show: true,
                    trigger: 'item',
                    valueFormatter: (value) =>  Number(value).toFixed(2)+'%'
                    // padding: [8, 15],
                    // backgroundColor: 'rgba(12, 51, 115,0.8)',
                    // borderColor: 'rgba(3, 11, 44, 0.5)',
                    // textStyle: {
                    //     color: 'rgba(255, 255, 255, 1)'
                    // },
                },
                legend: {
                    show: false,
                },
                grid: {
                    right: '10%',
                    left: '15%',
                    top: '5%',
                    bottom: '5%',
                },
                xAxis: [
                    {
                        splitLine: {
                            show: false,
                        },
                        type: 'value',
                        show: false,
                    },
                ],
                yAxis: [
                    {
                        splitLine: {
                            show: false,
                        },
                        axisLine: {
                            show: false,
                        },
                        type: 'category',
                        axisTick: {
                            show: false,
                        },
                        inverse: true,
                        data: [],
                        axisLabel: {
                            color: '#303133',
                            fontSize: 12,
                            margin: 10,
                        },
                    },
                    {
                        type: 'category',
                        inverse: true,
                        axisTick: 'none',
                        axisLine: 'none',
                        show: true,
                        axisLabel: {
                            textStyle: {
                                color: '#303133',
                                fontSize: 12,
                            },
                            formatter: function (value) {
                                return Number(value).toFixed(2) + '%';
                            },
                        },
                        data: [],
                    },
                ],
                series: [
                    {
                        // name: '率',
                        type: 'bar',
                        barWidth: 10, // 柱子宽度
                        MaxSize: 0,
                        showBackground: true,
                        backgroundStyle: {
                            color: '#f3f4f6',
                            borderRadius: 5,
                        },
                        label: {
                            show: false,
                        },
                        itemStyle: {
                            borderRadius: 5,
                            normal: {
                                barBorderRadius: 10,
                                color: function (params) {
                                    var color;
                                    if (params.dataIndex == 0) {
                                        color = {
                                            type: 'linear',
                                            x: 0,
                                            y: 0,
                                            x2: 1,
                                            y2: 0,
                                            colorStops: [
                                                {
                                                    offset: 0,

                                                    color: '#CF3537', // 0% 处的颜色
                                                },
                                                {
                                                    offset: 1,
                                                    color: '#CF3537', // 100% 处的颜色
                                                },
                                            ],
                                        };
                                    } else if (params.dataIndex == 1) {
                                        color = {
                                            type: 'linear',
                                            x: 0,
                                            y: 0,
                                            x2: 1,
                                            y2: 0,
                                            colorStops: [
                                                {
                                                    offset: 0,

                                                    color: '#FFAF04', // 0% 处的颜色
                                                },
                                                {
                                                    offset: 1,
                                                    color: '#FFAF04', // 100% 处的颜色
                                                },
                                            ],
                                        };
                                    } else if (params.dataIndex == 2) {
                                        color = {
                                            type: 'linear',
                                            x: 0,
                                            y: 0,
                                            x2: 1,
                                            y2: 0,
                                            colorStops: [
                                                {
                                                    offset: 0,

                                                    color: '#FFF600', // 0% 处的颜色
                                                },
                                                {
                                                    offset: 1,
                                                    color: '#FFF600', // 100% 处的颜色
                                                },
                                            ],
                                        };

                                    }
                                    else {
                                        color = {
                                            type: 'linear',
                                            x: 0,
                                            y: 0,
                                            x2: 1,
                                            y2: 0,
                                            colorStops: [
                                                {
                                                    offset: 0,
                                                    color: '#299CD4', // 0% 处的颜色
                                                },
                                                {
                                                    offset: 1,
                                                    color: '#299CD4', // 100% 处的颜色
                                                },
                                            ],
                                        };
                                    }
                                    return color;
                                },
                            },
                        },
                        data: [],
                    },
                ],
            }
            // 使用 setOption 方法将配置项设置为实例的属性
            this.colunmChart1.setOption(this.colunmOptions1);

            this.colunmChart2 = echarts.init(document.getElementById('top-card-colunm2'))

            // 配置参数
            this.colunmOptions2 = {
                // backgroundColor: '#000416',
                tooltip: {
                    show: true,
                    trigger: 'item',
                    valueFormatter: (value) =>  Number(value).toFixed(2)+'%'
                },
                legend: {
                    show: false,
                },
                grid: {
                    right: '10%',
                    left: '15%',
                    top: '5%',
                    bottom: '5%',
                },
                xAxis: [
                    {
                        splitLine: {
                            show: false,
                        },
                        type: 'value',
                        show: false,
                    },
                ],
                yAxis: [
                    {
                        splitLine: {
                            show: false,
                        },
                        axisLine: {
                            show: false,
                        },
                        type: 'category',
                        axisTick: {
                            show: false,
                        },
                        inverse: true,
                        data: [],
                        axisLabel: {
                            color: '#303133',
                            fontSize: 12,
                            margin: 5,
                            overflow: 'truncate',
                            width:120,
                            ellipsis: '...',
                        },
                    },
                    {
                        type: 'category',
                        inverse: true,
                        axisTick: 'none',
                        axisLine: 'none',
                        show: true,
                        axisLabel: {
                            textStyle: {
                                color: '#303133',
                                fontSize: 12,
                            },
                            formatter: function (value) {
                                return Number(value).toFixed(2) + '%';
                            },
                        },
                        data: [],
                    },
                ],
                series: [
                    {
                        // name: '率',
                        type: 'bar',
                        barWidth: 10, // 柱子宽度
                        MaxSize: 0,
                        showBackground: true,
                        backgroundStyle: {
                            color: '#f3f4f6',
                            borderRadius: 5,
                        },
                        label: {
                            show: false,
                        },
                        itemStyle: {
                            borderRadius: 5,
                            normal: {
                                barBorderRadius: 10,
                                color: function (params) {
                                    var color;
                                    if (params.dataIndex == 0) {
                                        color = {
                                            type: 'linear',
                                            x: 0,
                                            y: 0,
                                            x2: 1,
                                            y2: 0,
                                            colorStops: [
                                                {
                                                    offset: 0,

                                                    color: '#CF3537', // 0% 处的颜色
                                                },
                                                {
                                                    offset: 1,
                                                    color: '#CF3537', // 100% 处的颜色
                                                },
                                            ],
                                        };
                                    } else if (params.dataIndex == 1) {
                                        color = {
                                            type: 'linear',
                                            x: 0,
                                            y: 0,
                                            x2: 1,
                                            y2: 0,
                                            colorStops: [
                                                {
                                                    offset: 0,

                                                    color: '#FFAF04', // 0% 处的颜色
                                                },
                                                {
                                                    offset: 1,
                                                    color: '#FFAF04', // 100% 处的颜色
                                                },
                                            ],
                                        };
                                    } else if (params.dataIndex == 2) {
                                        color = {
                                            type: 'linear',
                                            x: 0,
                                            y: 0,
                                            x2: 1,
                                            y2: 0,
                                            colorStops: [
                                                {
                                                    offset: 0,

                                                    color: '#FFF600', // 0% 处的颜色
                                                },
                                                {
                                                    offset: 1,
                                                    color: '#FFF600', // 100% 处的颜色
                                                },
                                            ],
                                        };

                                    }
                                    else {
                                        color = {
                                            type: 'linear',
                                            x: 0,
                                            y: 0,
                                            x2: 1,
                                            y2: 0,
                                            colorStops: [
                                                {
                                                    offset: 0,
                                                    color: '#299CD4', // 0% 处的颜色
                                                },
                                                {
                                                    offset: 1,
                                                    color: '#299CD4', // 100% 处的颜色
                                                },
                                            ],
                                        };
                                    }
                                    return color;
                                },
                            },

                        },
                        data:[],
                    },
                ],
            }
            // 使用 setOption 方法将配置项设置为实例的属性
            this.colunmChart2.setOption(this.colunmOptions2);
        },
        // 重绘
        handleResizeChart () {
            if (this.ringChart1) {
                this.ringChart1.resize();
            }
            if (this.ringChart2) {
                this.ringChart2.resize();
            }
            if (this.ringChart3) {
                this.ringChart3.resize();
            }
            if (this.colunmChart1) {
                this.colunmChart1.resize();
            }
            if (this.colunmChart2) {
                this.colunmChart2.resize();
            }
        },
        //筛选
        onSearch () {
            this.getData()
        },
        // 获取数据
        getData () {
            hrHomePageCandidateAttrCharts(this.filter).then(res => {
                if (res.success) {
                    this.ringOptions1.series[0].data = res.data.性别分布;
                    this.ringChart1.setOption(this.ringOptions1);
                    this.ringOptions2.series[0].data = res.data.年龄分布;
                    this.ringChart2.setOption(this.ringOptions2);
                    this.ringOptions3.series[0].data = res.data.学历分布;
                    this.ringChart3.setOption(this.ringOptions3);
                    this.colunmOptions1.series[0].data = res.data.省份分布.map((item, index) => {
                            return {
                                name: item.name,
                                value: item.value,
                            }
                    });
                    this.colunmOptions1.yAxis[0].data = res.data.省份分布.map((item) => item.name);
                    this.colunmOptions1.yAxis[1].data = res.data.省份分布.map((item) => item.value);
                    this.colunmChart1.setOption(this.colunmOptions1);
                    this.colunmOptions2.series[0].data = res.data.市级分布.map((item, index) => {
                            return {
                                name: item.name,
                                value: item.value,
                            }
                    });
                    this.colunmOptions2.yAxis[0].data = res.data.市级分布.map((item) => item.name);
                    this.colunmOptions2.yAxis[1].data = res.data.市级分布.map((item) => item.value);
                    this.colunmChart2.setOption(this.colunmOptions2);
                }
            })
        }
    }

}
</script>
  
<style  lang="scss" scoped>
.card-box {
    min-width: 75px;
    height: 50px;
    background-color: #f5faff;
    padding: 20px;
    text-align: center;
    float: left;
    margin: 5px;
    border-radius: 8px;

    .box-value {
        font-size: 22px;
        color: #409eff;
    }

    .box-title {
        font-size: 14px;
        color: #409eff;
    }
}
</style>