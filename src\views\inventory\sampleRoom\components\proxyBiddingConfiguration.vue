<template>
  <div class="proxy-bidding-configuration">
    <el-tabs v-model="currentTab" type="border-card" @tab-click="handleTabClick">
      <el-tab-pane v-for="region in regions" :key="region" :label="region" :name="region">
        <proxyBiddingConfigForm :ref="`regionForm_${region}`" :custom-list="customList"
          :purchasing-list="purchasingList" :region="region" @saved="handleSaved" @cancel="handleCancel" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import proxyBiddingConfigForm from './proxyBiddingConfigForm.vue';

export default {
  name: "ProxyBiddingConfiguration",
  components: {
    proxyBiddingConfigForm
  },
  props: {
    customList: {
      type: Array,
      default: () => []
    },
    purchasingList: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      activeTab: '义乌',
      currentTab: '义乌', // 用于 v-model 绑定
      regions: ['义乌', '南昌', '西安'],
      pendingTab: null, // 待切换的标签页
      isTabSwitching: false // 标记是否正在切换标签页
    }
  },
  watch: {
    currentTab(newTab, oldTab) {
      if (this.isTabSwitching || newTab === oldTab) {
        return;
      }
      this.handleTabChange(newTab, oldTab);
    }
  },
  mounted() {
    // 回显数据
    this.$nextTick(() => {
      this.loadCurrentTabData(this.activeTab);
    });
  },
  methods: {
    handleTabClick(tab) {
      // 这个方法现在主要用于处理点击事件，实际的切换逻辑在 watch 中处理
      console.log('Tab clicked:', tab.name);
    },
    async handleTabChange(newTab, oldTab) {
      // 检查当前标签页是否有未保存的更改
      const currentForm = this.getCurrentFormByRegion(oldTab);
      if (currentForm && currentForm.hasAnyChanges && currentForm.hasAnyChanges()) {
        // 阻止标签页切换，恢复到原来的标签页
        this.isTabSwitching = true;
        this.currentTab = oldTab;
        this.$nextTick(() => {
          this.isTabSwitching = false;
        });
        this.pendingTab = newTab;
        this.showUnsavedChangesDialog();
        return;
      }

      // 没有未保存的更改，直接切换
      this.activeTab = newTab;
      this.loadCurrentTabData(newTab);
    },
    // 切换到指定标签页
    switchToTab(tabName) {
      this.isTabSwitching = true;
      this.activeTab = tabName;
      this.currentTab = tabName;
      this.$nextTick(() => {
        this.loadCurrentTabData(tabName);
        this.isTabSwitching = false;
      });
      this.pendingTab = null;
    },
    // 显示未保存更改的确认对话框
    showUnsavedChangesDialog() {
      this.$confirm('当前标签页有未保存的更改，是否要保存？', '提示', {
        confirmButtonText: '保存数据并切换',
        cancelButtonText: '放弃保存并切换',
        distinguishCancelAndClose: true,
        showClose: false,
        closeOnClickModal: false,
        type: 'warning'
      }).then(() => {
        // 用户选择保存数据并切换
        this.saveAndSwitch();
      }).catch((action) => {
        if (action === 'cancel') {
          // 用户选择放弃保存并切换
          this.discardAndSwitch();
        } else {
          // 用户选择关闭对话框，取消切换
          this.pendingTab = null;
        }
      });
    },
    // 保存数据并切换
    async saveAndSwitch() {
      const currentForm = this.getCurrentForm();
      if (currentForm && currentForm.saveConfig) {
        try {
          await currentForm.saveConfig();
          // 保存成功后切换标签页
          this.switchToTab(this.pendingTab);
        } catch (error) {
          console.error('保存失败:', error);
          this.pendingTab = null;
        }
      }
    },
    // 放弃保存并切换
    discardAndSwitch() {
      // 重新加载当前标签页数据，丢弃未保存的更改
      const currentForm = this.getCurrentForm();
      if (currentForm && currentForm.loadRegionData) {
        currentForm.loadRegionData();
      }
      // 切换到新标签页
      this.switchToTab(this.pendingTab);
    },
    // 回显当前页签的数据
    loadCurrentTabData(region) {
      const formRef = this.$refs[`regionForm_${region}`];
      if (formRef && formRef[0] && formRef[0].loadRegionData) {
        formRef[0].loadRegionData();
      }
    },
    handleSaved(region) {
      this.$emit('saved', region);
    },
    handleCancel() {
      this.$emit('close');
    },
    getCurrentForm() {
      return this.$refs[`regionForm_${this.activeTab}`] && this.$refs[`regionForm_${this.activeTab}`][0];
    },
    getCurrentFormByRegion(region) {
      return this.$refs[`regionForm_${region}`] && this.$refs[`regionForm_${region}`][0];
    },
    saveAllConfigs() {
      // 遍历所有区域，调用每个表单的保存方法
      this.regions.forEach(region => {
        const formRef = this.$refs[`regionForm_${region}`];
        if (formRef && formRef[0]) {
          formRef[0].saveConfig();
        }
      });
    }
  }
}
</script>

<style scoped lang="scss">
.proxy-bidding-configuration {
  padding: 20px;
  background-color: #f5f7fa;
  //min-height: 100vh;
}

.el-tabs {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

// 页签样式优化
:deep(.el-tabs__header) {
  margin: 0;
  background-color: #fafafa;
  border-radius: 8px 8px 0 0;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0 20px;
}

:deep(.el-tabs__item) {
  font-size: 16px;
  font-weight: 500;
  color: #606266;

  &.is-active {
    color: #409eff;
    font-weight: 600;
  }
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tab-pane) {
  background-color: #ffffff;
}
</style>
