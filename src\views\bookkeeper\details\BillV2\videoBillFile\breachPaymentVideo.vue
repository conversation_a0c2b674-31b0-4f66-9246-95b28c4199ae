<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <!-- <el-select filterable v-model="ListInfo.accountTypeist" placeholder="请选择账单项目"  multiple  clearable style="width: 130px">
              <el-option label="违规赔付" value="违规赔付" />
              <el-option label="未处理" value="未处理" />
          </el-select>

          <el-select filterable v-model="ListInfo.billTypeList" placeholder="请选择账单项目"  multiple  clearable style="width: 130px">
              <el-option label="违规赔付" value="违规赔付" />
              <el-option label="无" value="无" />
          </el-select> -->
          <div class="publicCss">
          <inputYunhan ref="relatedBusinessNumber" :inputt.sync="ListInfo.relatedBusinessNumber" v-model="ListInfo.transactionAmount"
            width="150px" placeholder="订单号/Enter订单号" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'relatedBusinessNumber')" title="订单号">
          </inputYunhan>
        </div>
        <div class="publicCss">
          <inputYunhan ref="shopName" :inputt.sync="ListInfo.shopName" v-model="ListInfo.shopName"
            width="150px" placeholder="店铺名称/Enter店铺名称" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'shopName')" title="店铺名称">
          </inputYunhan>
        </div>
        <div class="publicCss">
          <inputYunhan ref="relatedGiftNo" :inputt.sync="ListInfo.relatedGiftNo" v-model="ListInfo.relatedGiftNo"
            width="150px" placeholder="礼物单号/Enter礼物单号" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'relatedGiftNo')" title="礼物单号">
          </inputYunhan>
        </div>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
      </div>
    </template>
    <vxetablebase :id="'breachPaymentVideo202412021559'" :tablekey="'breachPaymentVideo202412021559'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { importIllegalDeduction_WeChatAsync, getIllegalDeduction_WeChat } from '@/api/bookkeeper/reportdayV2'
import inputYunhan from "@/components/Comm/inputYunhan";

import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'billNumberOrCreateTime', label: '账单单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'createTime', label: '创建时间', },
  //{ istrue: true, prop: 'billingItem', label: '账单项目', tipmesg: '', width: '100', sortable: 'custom', },
  { istrue: true, prop: 'billType', label: 'ERP账务类型', tipmesg: '', width: '100', sortable: 'custom', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'businessType', label: '业务类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'businessStatus', label: '业务状态', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'transactionAmount', label: '动账金额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'relatedBusinessNumber', label: '关联交易单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'relatedAfterSaleNo', label: '关联售后单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'relatedBzNo', label: '关联保障单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'relatedJFNo', label: '关联纠纷单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'relatedWYNo', label: '关联违约单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'relatedGiftNo', label: '关联礼物单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'relatedBusinessTkNo', label: '关联商家退款单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'description', label: '详情', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
  //{ sortable: 'custom', width: 'auto', align: 'center', prop: 'yearMonthDayDate', label: '时间', },
]
export default {
  name: "breachPaymentVideo",
  components: {
    MyContainer, vxetablebase,inputYunhan
  },
  data() {
    return {
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        relatedBusinessNumber:null,
        shopName:null,
        relatedGiftNo:null,
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    // await this.getList()
    if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
  },
  methods: {
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    callbackGoodsCode(val, type) {
      const map = {
        relatedBusinessNumber: () => (this.ListInfo.relatedBusinessNumber = val),
        shopName: () => (this.ListInfo.shopName = val),
        relatedGiftNo: () => (this.ListInfo.relatedGiftNo = val),

      };
      map[type]?.();
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importIllegalDeduction_WeChatAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      const { data, success } = await getIllegalDeduction_WeChat(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
