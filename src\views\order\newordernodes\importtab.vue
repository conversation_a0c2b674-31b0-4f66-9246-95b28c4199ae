<template>
  <el-container>
    <el-header style="height: 15px;"></el-header>
    <el-main>
      <el-row>
        <div class="grid-content bg-purple-dark" align="center" style="float:left;width: 33%;height: 100%;">
          <el-button type="info" plain size="large" @click="onstartImportBh">导入补货订单</el-button><br />
          <!--<el-link  type="primary" @click="ClickdownloadTemplate(1)">下载模板</el-link>-->
        </div>
        <div class="grid-content bg-purple-dark" align="center" style="float:left;width: 33%;height: 100%;">
          <el-button type="info" plain size="large" @click="onstartImportJhpc">导入拣货批次订单</el-button><br />
          <!--<el-link type="primary" @click="ClickdownloadTemplate(2)">下载模板</el-link>-->
        </div>
        <div class="grid-content bg-purple-dark" align="center" style="float:left;width: 33%;height: 100%;">
          <el-button type="info" plain size="large" @click="onstartImportNode">导入订单节点</el-button><br />
          <!--<el-link type="primary" @click="ClickdownloadTemplate(3)">v-if="checkPermission('PressNewOrderNodeImport')下载模板[api:inventory:processingorder:FinishProcessReceiptAsyn]</el-link>-->
        </div>
      </el-row>
      <el-row style="margin-top: 10px;">
        <div class="grid-content bg-purple-dark" align="center" style="float:left;width: 33%;height: 100%;">
          <el-button type="info" plain size="large" @click="startImport1">导入库存超卖</el-button><br />
        </div>
        <div class="grid-content bg-purple-dark" align="center" style="float:left;width: 33%;height: 100%;">
          <el-button type="info" plain size="large" @click="startImport2">导入缺货订单</el-button><br />
        </div>
        <div class="grid-content bg-purple-dark" align="center" style="float:left;width: 33%;height: 100%;">
          <el-button type="info" plain size="large" @click="startImport3">导入补货订单</el-button><br />
        </div>
      </el-row>
    </el-main>
    <el-footer align="left" height="100%">
      <p style="color:red">订单拣货方式分"组团配货","批次拣货","裁剪" 分别对应的操作员前缀 "FH1","ZTPH1","CJ"</p>
      <p style="color:red">仓库内作业流程如下：</p>
      <div align="center">
        <img :style="{ height: '666px', width: '1092px' }" :src="avatarDefault" />
      </div>
    </el-footer>
    <!--订单节点-->
    <el-dialog :title="uploadtitle" :visible.sync="dialogVisiblejd" width="40%" v-dialogDrag @close="closeupload"
      :close-on-click-modal="false">
      <span>
        <el-row>
          <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
            <el-upload ref="uploadjd" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
              accept=".xlsx" :http-request="uploadFilejd" :file-list="fileList" :data="fileparm">
              <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
            </el-upload>
          </el-col>
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeupload">关闭</el-button>
      </span>
    </el-dialog>
    <!--拣货批次-->
    <el-dialog :title="uploadtitle" :visible.sync="dialogVisiblejh" width="40%" v-dialogDrag @close="closeupload"
      :close-on-click-modal="false">
      <span>
        <el-row>
          <el-col :xs="3" :sm="3" :md="3" :lg="3" :xl="3">
            <span>
              是否纸质:
            </span>
          </el-col>
          <el-col :xs="21" :sm="21" :md="21" :lg="21" :xl="21">
            <el-radio v-model="ispaper" label="1">是</el-radio>
            <el-radio v-model="ispaper" label="2">否</el-radio>
          </el-col>
        </el-row>
        <p></p>
        <el-row>
          <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
            <el-upload ref="uploadjh" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
              accept=".xlsx" :http-request="uploadFilejh" :file-list="fileList" :data="fileparm">
              <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
            </el-upload>
          </el-col>
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeupload">关闭</el-button>
      </span>
    </el-dialog>
    <!--补货批次-->
    <el-dialog :title="uploadtitle" :visible.sync="dialogVisiblebh" width="40%" v-dialogDrag @close="closeupload"
      :close-on-click-modal="false">
      <span>
        <el-row>
          <el-button style="padding: 0;margin: 0;">
            <el-select v-model="warehouse" placeholder="请选择仓库" style="width: 200px">
              <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>
        </el-row>
        <p></p>
        <el-row>
          <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
            <el-upload ref="uploadbh" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
              accept=".xlsx" :http-request="uploadFilebh" :file-list="fileList" :data="fileparm">
              <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
            </el-upload>
          </el-col>
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeupload">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="导入库存超卖/京东仓加工仓" :visible.sync="dialogVisible1" width="30%" v-dialogDrag
      :close-on-click-modal="false">
      <el-alert title="温馨提示：请选取两个文件，文件名需分别包含“库存超卖”、“京东仓加工仓”。" type="success" :closable="false">
      </el-alert>
      <span>
        <el-upload ref="upload1" class="upload-demo" :auto-upload="false" :multiple="true" :limit="2" action
          accept=".xlsx" :http-request="uploadFile1" :on-change="uploadChange1" :on-remove="uploadRemove1"
          :on-success="uploadSuccess1">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading1"
            @click="submitUpload1">{{ (uploadLoading1 ? '上传中' : '上传') }} </el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible1 = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="导入缺货订单" :visible.sync="dialogVisible2" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading2"
            @click="submitUpload2">{{ (uploadLoading2 ? '上传中' : '上传') }} </el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible2 = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="导入补货订单" :visible.sync="dialogVisible3" width="30%" v-dialogDrag :close-on-click-modal="false">
      <el-alert title="温馨提示：请选取两个文件，文件名需分别包含“@批次号@待补订单明细”、“@批次号@待补商品明细”。" type="success" :closable="false">
      </el-alert>
      <span>
        <el-upload ref="upload3" class="upload-demo" :auto-upload="false" :multiple="true" :limit="2" action
          accept=".xlsx" :http-request="uploadFile3" :on-change="uploadChange3" :on-remove="uploadRemove3"
          :on-success="uploadSuccess3">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading3"
            @click="submitUpload3">{{ (uploadLoading3 ? '上传中' : '上传') }} </el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible3 = false">关闭</el-button>
      </span>
    </el-dialog>
  </el-container>
</template>
<script>
import { importOrderBh, importOrderPc, importOrderNodes } from '@/api/order/newordernodes';
import { importStockExceedAndOtherWareAsync, importRealTimeLackOrderAsync, importReplenishOrderAsync } from '@/api/inventory/inventoryorder';
import { sendwarehouselist } from '@/utils/tools';
export default {
  name: 'Roles',
  components: {},
  props: {

  },
  data() {
    return {
      avatarDefault: require('@/views/order/newordernodes/img/wms.png'),
      dialogVisiblejd: false,
      dialogVisiblebh: false,
      dialogVisiblejh: false,
      uploadLoading: false,
      importFilte: {},
      warehouselist: sendwarehouselist,
      //仓库
      warehouse: "",
      //纸质
      ispaper: "",
      fileList: [],
      fileparm: {},
      uploadtitle: "",

      dialogVisible1: false,
      uploadLoading1: false,
      fileList1: [],
      fileParm1: {},

      dialogVisible2: false,
      uploadLoading2: false,
      fileList2: [],
      fileParm2: {},

      dialogVisible3: false,
      uploadLoading3: false,
      fileList3: [],
      fileParm3: {},
    }
  },
  async mounted() {
  },
  methods: {
    ClickdownloadTemplate(type) {
      if (type == 1) {
        window.open("/static/excel/order/订单关键节点模板.xlsx", "_blank");
      } else if (type == 2) {
        window.open("/static/excel/order/拣货批次订单模板.xlsx", "_blank");
      } else if (type == 3) {
        window.open("/static/excel/order/补货商品信息模板.xlsx", "_blank");
      }

    },
    //导入 0：关键节点 1：补货
    async onstartImportBh() {
      this.uploadtitle = "导入补货订单";
      this.dialogVisiblebh = true;
    },
    async onstartImportJhpc() {
      this.uploadtitle = "导入拣货批次订单";
      this.dialogVisiblejh = true;
    },
    async onstartImportNode() {
      this.uploadtitle = "导入订单节点";
      this.dialogVisiblejd = true;
    },
    submitUpload() {
      if (this.uploadtitle == "导入补货订单") {
        if (this.warehouse == "") {
          this.$message({ message: "请选择仓库", type: "error" });
          return false;
        }
        if (this.$refs.uploadbh.uploadFiles.length == 0) {
          this.$message({ message: "请选择文件", type: "error" });
          return false;
        }
        this.$refs.uploadbh.submit();

      } else if (this.uploadtitle == "导入拣货批次订单") {
        if (this.ispaper == "") {
          this.$message({ message: "请选择是否纸质", type: "error" });
          return false;
        }
        if (this.$refs.uploadjh.uploadFiles.length == 0) {
          this.$message({ message: "请选择文件", type: "error" });
          return false;
        }
        this.$refs.uploadjh.submit();
      } else {
        if (this.$refs.uploadjd.uploadFiles.length == 0) {
          this.$message({ message: "请选择文件", type: "error" });
          return false;
        }
        this.$refs.uploadjd.submit();
      }
    },
    async uploadFilejd(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importOrderNodes(form);
      if (res.code == 1) {
        this.$message({ message: "上传成功,正在导入中...，请在任务中查看进度", type: "success" });
        this.closeupload();
      }
      else this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading = false
    },
    //拣货
    async uploadFilejh(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("ispaper", this.ispaper);
      var res = await importOrderPc(form);
      if (res.code == 1) {
        this.$message({ message: "上传成功,正在导入中...，请在任务中查看进度", type: "success" });
        this.closeupload();
      }
      else this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading = false
    },
    //补货
    async uploadFilebh(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("warehouse", this.warehouse);
      var res = await importOrderBh(form);
      if (res.code == 1) {
        this.$message({ message: "上传成功,正在导入中...，请在任务中查看进度", type: "success" });
        this.closeupload();
      }
      else this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading = false
    },
    async closeupload() {
      this.dialogVisiblebh = false;
      this.dialogVisiblejh = false;
      this.dialogVisiblejd = false;
      this.uploadLoading = false;
      if (this.uploadtitle == "导入补货订单") {
        this.$refs.uploadbh.clearFiles();
      } else if (this.uploadtitle == "导入拣货批次订单") {
        this.$refs.uploadjh.clearFiles();
      } else {
        this.$refs.uploadjd.clearFiles();
      }
      this.warehouse = ""
      //纸质
      this.ispaper = ""
    },



    startImport1() {
      this.dialogVisible1 = true;
    },
    uploadSuccess1(response, file, fileList1) {
      if (fileList1.length == 2) {
        fileList1.splice(0, 1);
        fileList1.splice(0, 1);
      }
      if (fileList1.length == 1) {
        fileList1.splice(0, 1);
      }
    },
    async submitUpload1() {
      if (!this.fileList1 || this.fileList1.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.fileHasSubmit = true;
      this.$refs.upload1.submit();
    },
    async uploadFile1(item) {
      if (!this.fileHasSubmit) {
        return false;
      }
      this.fileHasSubmit = false;
      let stockExceedFile = null;
      let otherWareFile = null;
      for (const key in this.fileList1) {
        let name = this.fileList1[key].name;
        if (name.indexOf("库存超卖") > -1 && name.indexOf("京东仓加工仓") < 0) {
          stockExceedFile = this.fileList1[key];
        }
        if (name.indexOf("库存超卖") < 0 && name.indexOf("京东仓加工仓") > -1) {
          otherWareFile = this.fileList1[key];
        }
      }
      if (!stockExceedFile || !otherWareFile) {
        this.$message({ message: "请同时上传库存超卖和京东仓加工仓", type: "error" });
        return false;
      }
      console.log(stockExceedFile, "stockExceedFile")
      console.log(otherWareFile, "otherWareFile")
      this.uploadLoading1 = true;
      const form = new FormData();
      form.append("stockExceedFile", stockExceedFile);
      form.append("otherWareFile", otherWareFile);
      const res = await importStockExceedAndOtherWareAsync(form);
      this.uploadLoading1 = false;
      if (!res) {
        this.$message({ message: "没有数据", type: "error" });
        return;
      }
      this.$message({ message: "操作成功,已生成：缺货SKU.txt", type: "success" });
      const aLink = document.createElement("a");
      let blob = new Blob([res])
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '缺货SKU' + new Date().toLocaleString() + '.txt')
      aLink.click();
    },
    uploadChange1(file, fileList1) {
      if (fileList1 && fileList1.length > 0) {
        var list = [];
        for (var i = 0; i < fileList1.length; i++) {
          if (fileList1[i].status == "success")
            list.push(fileList1[i]);
          else
            list.push(fileList1[i].raw);
        }
        this.fileList1 = list;
      }
    },
    uploadRemove1(file, fileList1) {
      this.uploadChange1(file, fileList1);
    },



    startImport2() {
      this.dialogVisible2 = true;
    },
    uploadSuccess2(response, file, fileList) {
      this.fileList2.splice(fileList.indexOf(file), 1);
      this.uploadLoading2 = false;
      this.dialogVisible2 = false;
    },
    async submitUpload2() {
      this.$refs.upload2.submit();
    },
    async uploadFile2(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading2 = true;
      const form = new FormData();
      form.append("file", item.file);
      const res = await importRealTimeLackOrderAsync(form);
      this.uploadLoading2 = false;
      if (res?.success) {
        this.$message({ message: '导入成功', type: "success" });
        this.dialogVisible2 = false;
        //this.onSearch();
      }
    },

    startImport3() {
      this.dialogVisible3 = true;
    },
    uploadSuccess3(response, file, fileList3) {
      if (fileList3.length == 2) {
        fileList3.splice(0, 1);
        fileList3.splice(0, 1);
      }
      if (fileList3.length == 1) {
        fileList3.splice(0, 1);
      }
    },
    async submitUpload3() {
      if (!this.fileList3 || this.fileList3.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.fileHasSubmit3 = true;
      this.$refs.upload3.submit();
    },
    async uploadFile3(item) {
      if (!this.fileHasSubmit3) {
        return false;
      }
      this.fileHasSubmit3 = false;
      let file1 = null;
      let file2 = null;
      for (const key in this.fileList3) {
        let name = this.fileList3[key].name;
        if (name.indexOf("待补订单明细") > -1 && name.indexOf("待补商品明细") < 0) {
          file1 = this.fileList3[key];
        }
        if (name.indexOf("待补订单明细") < 0 && name.indexOf("待补商品明细") > -1) {
          file2 = this.fileList3[key];
        }
      }
      if (!file1 || !file2) {
        this.$message({ message: "请同时上传@待补订单明细和@待补商品明细", type: "error" });
        return false;
      }
      console.log(file1, "file1")
      console.log(file2, "file2")
      this.uploadLoading3 = true;
      const form = new FormData();
      form.append("file1", file1);
      form.append("file2", file2);
      const res = await importReplenishOrderAsync(form);
      this.uploadLoading3 = false;
      if (res?.success) {
        this.$message({ message: '上传成功，后台导入中...', type: "success" });
        this.dialogVisible2 = false;
        //this.onSearch();
      }
    },
    uploadChange3(file, fileList3) {
      if (fileList3 && fileList3.length > 0) {
        var list = [];
        for (var i = 0; i < fileList3.length; i++) {
          if (fileList3[i].status == "success")
            list.push(fileList3[i]);
          else
            list.push(fileList3[i].raw);
        }
        this.fileList3 = list;
      }
    },
    uploadRemove3(file, fileList3) {
      this.uploadChange3(file, fileList3);
    },
  }
}
</script>
