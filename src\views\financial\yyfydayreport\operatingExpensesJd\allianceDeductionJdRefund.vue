<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <!-- <el-select v-model="ListInfo.isStock" placeholder="地区" class="publicCss" clearable>
          <el-option :key="'是'" label="是" :value="0" />
          <el-option :key="'否'" label="否" :value="1" />
        </el-select> -->
        <el-input v-model.trim="ListInfo.shopName" placeholder="店铺" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.shopGoodsCode" placeholder="商品编号" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.orderNo" placeholder="订单号" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
      </div>
    </template>
    <vxetablebase :id="'allianceDeductionJdRefund202411282236'" :tablekey="'allianceDeductionJdRefund202411282236'"
      ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
      :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="display: flex; align-items: baseline; height: 75px;margin-top: 10px;">
        <el-date-picker style="width: 150px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay" type="date"
          placeholder="选择日期" :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
        </el-date-picker>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { importAffiliateDeduction_JingDongasync, getAffiliateDeductionTuiHuo_JingDongSelfSupport } from '@/api/bookkeeper/reportdayV2'
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopGoodsCode', label: '商品编号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '订单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderDate', label: '下单日期', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'finishDate', label: '完成日期', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'productQuantity', label: '商品数量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'tuiHuoQty', label: '退货数量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'paymentAmount', label: '支付金额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'commissionCalculationAmount', label: '计佣金额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'commission', label: '佣金', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'teamLeaderServiceFee', label: '团长服务费', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'jdPlatformTechServiceFee', label: '京东平台技术服务费', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalCommission', label: '总佣金', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'refundDate', label: '返款日期', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'refundAmount', label: '返款金额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'refundReason', label: '返款原因', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'belongingPlan', label: '所属计划', },
]
export default {
  name: "allianceDeductionJdRefund",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      yearMonthDay: '',
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        shopGoodsCode: '',//商品编号
        orderNo: '',//订单号
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("dataType", 2);
      form.append("yearMonthDay", dayjs(this.yearMonthDay).format('YYYYMMDD'));
      var res = await importAffiliateDeduction_JingDongasync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      const { data, success } = await getAffiliateDeductionTuiHuo_JingDongSelfSupport(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach((item, index) => {
          item.orderDate = item.orderDate ? dayjs(item.orderDate).format('YYYY-MM-DD') : ''
          item.finishDate = item.finishDate ? dayjs(item.finishDate).format('YYYY-MM-DD') : ''
          item.refundDate = item.refundDate ? dayjs(item.refundDate).format('YYYY-MM-DD') : ''
        })
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
