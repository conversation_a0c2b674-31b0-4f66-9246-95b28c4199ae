<template>
    <MyContainer v-loading="chatProp.chatLoading">
        <div style="display: flex;justify-content: center;margin-top: 20px;">
            <el-radio-group v-model="radio1" @change="changeType($event, true)">
                <el-radio-button label="仓库"></el-radio-button>
                <el-radio-button label="快递公司"></el-radio-button>
            </el-radio-group>
        </div>
        <div>
            <div style="display: flex;">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" @change="changeTime" :picker-options="pickerOptions"
                    style="margin-right: 10px;" :clearable="false" />
                <el-select v-model="chatInfo.isTimeOut" placeholder="是否超时" class="publicCss" style="margin-right: 10px;"
                    @change="changeType(radio1)">
                    <el-option key="超时" label="超时" :value="true" />
                    <el-option key="未超时" label="未超时" :value="false" />
                </el-select>
                <el-button type="primary" @click="changeType(radio1)">查询</el-button>
            </div>
            <buschar :analysisData="chatProp.data" v-if="!chatProp.chatLoading"></buschar>
        </div>
    </MyContainer>
</template>

<script>
import buschar from '@/components/Bus/buschar'
import dayjs from 'dayjs'
import MyContainer from "@/components/my-container";
import { pickerOptions } from '@/utils/tools'
import { GetStatDataBySendWms, GetStatDataBySendExpCmy } from '@/api/warning/LogisticsCollecting'
export default {
    components: {
        buschar, MyContainer
    },
    data() {
        return {
            pickerOptions,
            radio1: '仓库',
            chatProp: {
                chatDialog: false,//趋势图弹窗
                chatTime: null,//趋势图时间
                chatLoading: true,//趋势图loading
                data: [],//趋势图数据
            },
            chatInfo: {
                startTime: null,//开始时间
                endTime: null,//结束时间
                isTimeOut: true,//是否超时
            },
        }
    },
    async mounted() {
        this.init()
        await this.getWareHouseData()
    },
    methods: {
        // initProps(data) {
        //     data.series[1].forEach(item => {
        //         item = item + '%'
        //     })
        //     return data
        // },
        init() {
            this.chatProp.chatTime = [dayjs().subtract(1, 'week').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
            this.chatInfo.startTime = this.chatProp.chatTime[0]
            this.chatInfo.endTime = this.chatProp.chatTime[1]
        },
        async changeTime(e) {
            this.chatInfo.startTime = e ? dayjs(e[0]).format('YYYY-MM-DD') : null
            this.chatInfo.endTime = e ? dayjs(e[1]).format('YYYY-MM-DD') : null
            await this.changeType(this.radio1)
        },
        changeType(e, isChangeType) {
            if (isChangeType) {
                this.init()
            }
            if (e == '仓库') {
                this.getWareHouseData()
            } else {
                this.getExpressData()
            }
        },
        async getWareHouseData() {
            this.chatProp.chatLoading = true
            const { data, success } = await GetStatDataBySendWms(this.chatInfo)
            if (success) {
                this.$nextTick(() => {
                    this.chatProp.data = data
                })
            }
            this.chatProp.chatLoading = false
        },
        async getExpressData() {
            this.chatProp.chatLoading = true
            const { data, success } = await GetStatDataBySendExpCmy(this.chatInfo)
            if (success) {
                this.$nextTick(() => {
                    data.series[1].data.forEach(item => {
                        item = item + '%'
                    })
                    console.log(data.series[1].data, 'data.series[1].data');
                    this.chatProp.data = data
                    console.log(data, 'data2');
                })
            }
            this.chatProp.chatLoading = false
        },
    }
}
</script>

<style scoped lang="scss"></style>