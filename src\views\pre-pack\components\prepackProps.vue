<template>
  <MyContainer>
    <template #header>
      <el-form>
        <div ref="condition" class="condition">
          <el-row :gutter="10" class="top">
            <el-col :span="3">
              <dynamic-selecter v-model="query.dynamicFilter" :scene="$route.name" class="publicCss" :fields="tableCols.filter(
                (item) => item.label != '' && item.label != '操作'
              )
                " @update="getList('search')" />
            </el-col>
            <el-col :span="3">
              <tagSelecter v-model="query.dynamicPropFilter" class="publicCss" @update="getList('search')" />
            </el-col>
            <el-col :span="3">
              <el-select v-model="query.canPrePack" placeholder="是否可以预包" class="publicCss" clearable>
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </el-col>
            <el-col :span="3">
              <el-select v-model="query.isSafeStock" placeholder="是否安全库存" class="publicCss" clearable>
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </el-col>
            <el-col :span="3">
              <el-select v-model="query.isEntityGoods" placeholder="是否实体商品" class="publicCss" clearable>
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </el-col>
            <el-col :span="3">
              <el-select v-model="query.prePackType" placeholder="计算方式" class="publicCss" clearable>
                <el-option label="七日日均生产" :value="0" />
                <el-option label="按昨日销量生产" :value="1" />
                <el-option label="按日销比生产" :value="2" />
                <el-option label="按加工比例生产" :value="3" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" size="mini" style="margin-left: 3px" @click="getList('search')">搜索</el-button>
              <el-button type="primary" size="mini" style="margin-left: 3px" :disabled="isExport"
                @click="exportProps">导出</el-button>
              <el-button type="primary" size="mini" style="margin-left: 3px" @click="prePackSet">预包设置</el-button>
              <el-button type="primary" size="mini" style="margin-left: 3px" @click="selectCheckBox">勾选加工</el-button>
              <el-button type="primary" size="mini" style="margin-left: 3px" @click="queryCreatePlan">查询结果加工</el-button>
            </el-col>
            <el-col :span="3">
              <number-range class="publicCss" :min.sync="query.qtyMin" :max.sync="query.qtyMax" min-label="昨日销量最小值"
                max-label="昨日销量最大值" />
            </el-col>
            <el-col :span="3">
              <number-range class="publicCss" :min.sync="query.day7QtyMin" :max.sync="query.day7QtyMax"
                min-label="七日销量最小值" max-label="七日销量最大值" />
            </el-col>
            <el-col :span="3">
              <number-range class="publicCss" :min.sync="query.usableQtyMin" :max.sync="query.usableQtyMax"
                min-label="全仓库存最小值" max-label="全仓库存最大值" />
            </el-col>
            <el-col :span="3">
              <number-range class="publicCss" :min.sync="query.safeDayMin" :max.sync="query.safeDayMax"
                min-label="全仓安全天数最小值" max-label="全仓安全天数最大值" />
            </el-col>
            <el-col :span="3">
              <number-range class="publicCss" :min.sync="query.sendWmsUsableQtyMin"
                :max.sync="query.sendWmsUsableQtyMax" min-label="发货仓库存最小值" max-label="发货仓库存最大值" />
            </el-col>
            <el-col :span="3">
              <number-range class="publicCss" :min.sync="query.sendWmsUsableDayMin"
                :max.sync="query.sendWmsUsableDayMax" min-label="发货仓安全天数最小值" max-label="发货仓安全天数最大值" />
            </el-col>
            <el-col :span="3">
              <number-range class="publicCss" :min.sync="query.entityQtyMin" :max.sync="query.entityQtyMax"
                min-label="昨预包销量最小值" max-label="昨预包销量最大值" />
            </el-col>
            <el-col :span="3">
              <number-range class="publicCss" :min.sync="query.dayRateMin" :max.sync="query.dayRateMax"
                min-label="日销比最小值" max-label="日销比最大值" />
            </el-col>
            <el-col :span="3">
              <number-range class="publicCss" :min.sync="query.operatePackQtyMin" :max.sync="query.operatePackQtyMax"
                min-label="运营预包数量最小值" max-label="运营预包数量最大值" />
            </el-col>
            <el-col :span="3">
              <number-range class="publicCss" :min.sync="query.operatePackMultipleQtyMin"
                :max.sync="query.operatePackMultipleQtyMax" min-label="运营计划加工数量最小值" max-label="运营计划加工数量最大值" />
            </el-col>
            <el-col :span="3">
              <el-select v-model="query.IsOutStock" placeholder="是否生产缺货" class="publicCss" clearable>
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </el-col>
            <el-col :span="3">
              <el-select v-model="query.isCombineGoods" placeholder="是否组合商品" class="publicCss" clearable>
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </el-col>
            <el-col :span="3">
              <el-select v-model="query.isPrePack" class="publicCss" placeholder="外仓是否加工" clearable>
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </el-col>
            <el-col :span="3">
              <inputYunhan ref="productCode" :inputt.sync.stop="query.goodsCode" v-model="query.goodsCode" width="100%"
                placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
                @callback="goodsCodeCallback" title="商品编码" style="margin: 0 5px 5px 0;">
              </inputYunhan>
              <!-- <el-input v-model.trim="query.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" /> -->
            </el-col>
            <el-col :span="3">
              <el-input v-model.trim="query.goodsName" placeholder="商品名称" maxlength="50" clearable class="publicCss" />
            </el-col>
            <el-col :span="3">
              <inputYunhan ref="productCode" :inputt.sync="query.entityCode" v-model="query.entityCode" width="100%"
                placeholder="实体编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
                @callback="entityCodeCallback" title="实体编码" style="margin: 0 5px 5px 0;">
              </inputYunhan>
              <!-- <el-input v-model.trim="query.entityCode" placeholder="实体编码" maxlength="50" clearable class="publicCss" /> -->
            </el-col>
            <el-col :span="3">
              <inputYunhan ref="productCode" :inputt.sync="query.skus" v-model="query.skus" width="100%"
                placeholder="skus/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
                @callback="skusCallback" title="skus" style="margin: 0 5px 5px 0;">
              </inputYunhan>
              <el-input v-model.trim="query.skus" placeholder="SKUS" maxlength="50" clearable class="publicCss" />
            </el-col>
            <el-col :span="3">
              <chooseWareHouse v-model="query.sendWmsId" style="width: 100%" :filter="sendWmsesFilter"
                @update="getList('search')" />
            </el-col>
          </el-row>
        </div>
      </el-form>
    </template>
    <vxetablebase :id="'prepackProps202408041852'" ref="table" v-loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
      :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
      :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0" :height="'100%'"
      :showsummary="data.summary ? true : false" :summaryarry="data.summary" @sortchange="sortchange"
      @select="checkboxRangeEnd" @onTrendChart="trendChart">
      <template #prePackWmsName="{ row, index }">
        <div>
          <el-popover style="overflow: hidden" placement="left" width="300"
            :trigger="row.canPackWmsesList.length > 1 ? 'hover' : 'manual'">
            <el-table :data="row.canPackWmsesList" max-height="200">
              <el-table-column property="subGoodsWmsName" label="仓库" show-overflow-tooltip />
              <el-table-column property="subGoodsUsableQty" width="100" label="可加工量" show-overflow-tooltip />
              <el-table-column property="subGoodsTurnoverDay" width="100" label="周转天数" show-overflow-tooltip />
            </el-table>
            <div slot="reference">{{ row.prePackWmsName }}</div>
          </el-popover>
        </div>
      </template>
      <template #usableQty="{ row }">
        <inventoryPopover :goods-codes="[row.goodsCode, row.entityCode]" :width="800" height="400" :columns="[
          'wmsName',
          'goodsCode',
          'quantity',
          'usableQty',
          'orderUseQty',
          'wmsWaitSendQty',
          'inWmsInventory',
          'allotQty',
          'jstUsableQty',
          'modifyTime',
        ]">
          {{ row.usableQty }}</inventoryPopover>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
    <el-dialog v-dialogDrag title="预包设置" :visible.sync="addVisable" width="20%">
      <el-form ref="ruleForm" :model="ruleForm" status-icon :rules="rules" label-width="180px" class="demo-ruleForm">
        <el-form-item label="预包天数" prop="prePackDay">
          <el-input-number v-model="ruleForm.prePackDay" :min="0" :max="9999" placeholder="预包天数" :controls="false" />
        </el-form-item>
        <el-form-item label="预包安全天数" prop="prePackSafeDay">
          <el-input-number v-model="ruleForm.prePackSafeDay" :min="0" :max="9999" placeholder="预包安全天数"
            :controls="false" />
        </el-form-item>
        <!-- <el-form-item label="预包安全数量" prop="prePackSafeCount">
          <el-input-number v-model="ruleForm.prePackSafeCount" :min="0" :max="999999" placeholder="预包安全数量"
            :controls="false" />
        </el-form-item> -->
        <el-form-item label="预包最小数量" prop="prePackMinCount">
          <el-input-number v-model="ruleForm.prePackMinCount" placeholder="预包最小数量" :min="0" :max="9999"
            :controls="false" />
        </el-form-item>
        <el-form-item label="物料安全天数" prop="prePackMtlRsvDay">
          <el-input-number v-model="ruleForm.prePackMtlRsvDay" placeholder="物料安全天数" :min="0" :max="9999"
            :controls="false" />
        </el-form-item>
        <el-form-item label="昨日销量占比" prop="day1Rate">
          <el-input-number v-model="ruleForm.day1Rate" placeholder="昨日销量占比" :min="0" :max="100" :controls="false" />
        </el-form-item>
        <el-form-item label="三日均销占比" prop="day3Rate">
          <el-input-number v-model="ruleForm.day3Rate" placeholder="三日均销占比" :min="0" :max="100" :controls="false" />
        </el-form-item>
        <el-form-item label="七日均销占比" prop="day7Rate">
          <el-input-number v-model="ruleForm.day7Rate" placeholder="七日均销占比" :min="0" :max="100" :controls="false" />
        </el-form-item>
        <el-form-item label="运营预包最小七日日均" prop="operateConfirmMin">
          <el-input-number v-model="ruleForm.operateConfirmMin" placeholder="运营预包最小七日日均" :min="0" :max="1000"
            :controls="false" />
        </el-form-item>
        <el-form-item label="运营预包倍数" prop="operateMultiple">
          <el-input-number v-model="ruleForm.operateMultiple" placeholder="运营预包倍数" :min="0" :max="100"
            :controls="false" />
        </el-form-item>
        <el-form-item label="运营确认时间(小时)" prop="operateConfirmHour">
          <el-input-number v-model="ruleForm.operateConfirmHour" placeholder="运营确认时间(小时)" :min="0" :max="100"
            :controls="false" />
        </el-form-item>
        <el-form-item label="预包最大重量（g）" prop="prePackMaxWeight">
          <el-input-number v-model="ruleForm.prePackMaxWeight" placeholder="预包最大重量（g）" :min="0" :max="9999"
            :controls="false" />
        </el-form-item>
        <el-form-item label="义乌可加工标签" prop="yiWuWmsNames">
          <el-select v-model="ruleForm.yiWuWmsNames" multiple clearable placeholder="请选择" style="height: auto">
            <el-option v-for="item in wmsesList" :key="item.name" :label="item.name" :value="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="resetForm('ruleForm')">重置</el-button>
          <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog v-dialogDrag title="创建加工单" :visible.sync="processingOrderVisable" width="50%">
      <ProcessingOrder v-if="processingOrderVisable" :goods-codes="goodsCodeList" @close="close" />
    </el-dialog>
    <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false" direction="btt">
      <div v-if="!chatProp.chatLoading">
        <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd" :picker-options="pickerOptions" style="margin: 10px" @change="
            trendChart({
              ...chatPropOption,
              startDate: $event[0],
              endDate: $event[1],
            })
            " />
        <buschar v-if="!chatProp.chatLoading" :analysis-data="chatProp.data" />
      </div>
      <div v-else v-loading="chatProp.chatLoading" />
    </el-drawer>
  </MyContainer>
</template>
<script>
import MyContainer from "@/components/my-container";
import numberRange from "@/components/number-range/index.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dynamicSelecter from "@/components/customQuery/selecter.vue";
import tagSelecter from "@/components/tag/selecter.vue";
import inventoryPopover from "./../inventory/popover.vue";
import { pickerOptions } from "@/utils/tools";
import ProcessingOrder from "./ProcessingOrder.vue";
import { download } from "@/utils/download";
import buschar from "@/components/Bus/buschar";
import dayjs from "dayjs";
import inputYunhan from "@/components/Comm/inputYunhan";
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import {
  getColumns,
  pageGetData,
  exportData,
  getSetting,
  setting,
  getWaitPrePacks,
  getTrendChart,
} from "@/api/vo/prePack";
export default {
  name: "ScanCodePage",
  components: {
    MyContainer,
    vxetablebase,
    dynamicSelecter,
    numberRange,
    ProcessingOrder,
    inventoryPopover,
    buschar,
    tagSelecter,
    chooseWareHouse,
    inputYunhan
  },
  data() {
    return {
      chatPropOption: {},
      wmsesList: [],
      rules: {
        prePackDay: [
          { required: true, message: "请输入预包天数", trigger: "blur" },
          { type: "number", message: "预包天数 必须为数字值", trigger: "blur" },
        ],
        prePackSafeDay: [
          { required: true, message: "请输入预包安全天数", trigger: "blur" },
          {
            type: "number",
            message: "预包安全天数 必须为数字值",
            trigger: "blur",
          },
        ],
        // prePackSafeCount: [
        //   { required: true, message: '请输入预包安全数量', trigger: 'blur' },
        //   { type: 'number', message: '预包安全数量 必须为数字值', trigger: 'blur' }
        // ],
        prePackMinCount: [
          { required: true, message: "请输入预包最小数量", trigger: "blur" },
          {
            type: "number",
            message: "预包最小数量 必须为数字值",
            trigger: "blur",
          },
        ],
        prePackMaxWeight: [
          { required: true, message: "请输入预包最大重量", trigger: "blur" },
          {
            type: "number",
            message: "预包最大重量 必须为数字值",
            trigger: "blur",
          },
        ],
        prePackMtlRsvDay: [
          { required: true, message: "请输入物料安全天数", trigger: "blur" },
          {
            type: "number",
            message: "物料安全天数 必须为数字值",
            trigger: "blur",
          },
        ],
        day1Rate: [
          { required: true, message: "请输入昨日销量占比", trigger: "blur" },
          {
            type: "number",
            message: "昨日销量占比 必须为数字值",
            trigger: "blur",
          },
        ],
        day3Rate: [
          { required: true, message: "请输入三日均销占比", trigger: "blur" },
          {
            type: "number",
            message: "三日均销占比 必须为数字值",
            trigger: "blur",
          },
        ],
        day7Rate: [
          { required: true, message: "请输入七日均销占比", trigger: "blur" },
          {
            type: "number",
            message: "七日均销占比 必须为数字值",
            trigger: "blur",
          },
        ],
        operateConfirmMin: [
          {
            required: true,
            message: "请输入运营预包最小七日日均",
            trigger: "blur",
          },
          {
            type: "number",
            message: "运营预包最小七日日均 必须为数字值",
            trigger: "blur",
          },
        ],
        operateMultiple: [
          { required: true, message: "请输入运营预包倍数", trigger: "blur" },
          {
            type: "number",
            message: "运营预包倍数 必须为数字值",
            trigger: "blur",
          },
        ],
      },
      that: this,
      query: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        canPrePack: null, // 是否可以预包
        isSafeStock: null, // 是否安全库存
        isEntityGoods: null, // 是否实体商品
        goodsCode: null, // 商品编码
        goodsName: null, // 商品名称
        isCombineGoods: null, // 是否组合商品
        dynamicFilter: {},
        dynamicPropFilter: {},
        prePackType: 3,
        dayRateMin: null,
        dayRateMax: null,
        isGenerateManifest: false,
        entityCode: null,
        skus: null,
        summarys: [],
        // sendWmsId: null,
        // usableQtyMin: null,//剩余库存最小值
        // usableQtyMax: null,//剩余库存最大值
        // safeDayMin: null,//安全天数最小值
        // safeDayMax: null,//安全天数最大值
      },
      data: { total: 0, list: [], summary: {} },
      processingOrderVisable: false,
      timeRanges: [],
      tableCols: [],
      loading: true,
      pickerOptions,
      isExport: false,
      allotResultStatus: [],
      addVisable: false,
      ruleForm: {},
      goodsCodeList: [],
      chatProp: {
        chatDialog: false, // 趋势图弹窗
        chatTime: null, // 趋势图时间
        chatLoading: true, // 趋势图loading
        data: [], // 趋势图数据
      },
      warehouselist: [],
    };
  },
  updated() {
    this.$refs.condition.scrollTop = 0;
  },
  async mounted() {
    await this.getCol();
    this.getList();
    window.formatLink = (platform, proCode) => {
      // 是否跨境
      proCode = proCode ? String(proCode) : "";
      let proCodeLink = proCode;
      if (proCode && proCode.toLowerCase().indexOf("kj") == 0) {
        proCodeLink = proCode.substring(2);
      }
      var proBaseUrl = "";
      switch (platform) {
        case 1: // 淘系
        case "淘系": // 淘系
        case "天猫": // 天猫
          proBaseUrl = "https://detail.tmall.com/item.htm?id=" + proCodeLink;
          break;
        case 2: // 拼多多
        case "拼多多": // 拼多多
          proBaseUrl =
            "https://mobile.yangkeduo.com/goods2.html?goods_id=" + proCodeLink;
          break;
        case 8: // 淘系
        case "淘工厂": // 淘系
          proBaseUrl = "https://detail.tmall.com/item.htm?id=" + proCodeLink;
          break;
        case 9: // 淘系
        case "淘宝": // 淘系
          proBaseUrl = "https://detail.tmall.com/item.htm?id=" + proCodeLink;
          break;
        case 7: // 京东
        case "京东": // 京东
          proBaseUrl = "https://item.jd.com/" + proCodeLink + ".html";
          break;
        case 4: // 阿里巴巴
        case "阿里巴巴": // 阿里巴巴
          proBaseUrl =
            "https://detail.1688.com/offer/" +
            proCodeLink +
            ".html?spm=a26286.8251493.description.2.221425b2kIBGkR";
          break;
        case 6: // 抖音
        case "抖音": // 抖音
          proBaseUrl =
            "https://haohuo.jinritemai.com/views/product/detail?id=" +
            proCodeLink;
          break;
      }
      if (proCode && proBaseUrl) {
        return window.open(proBaseUrl);
      }
      return "";
    };
  },
  methods: {
    goodsCodeCallback (data) {
      this.query.goodsCode = data
    },
    entityCodeCallback (data) {
      this.query.entityCode = data
    },
    skusCallback (data) {
      this.query.skus = data
    },
    sendWmsesFilter(wmses) {
      this.wmsesList = wmses;
      console.log(this.wmsesList);

      return wmses.filter((a) => a.isSendWarehouse === "是");
    },
    async trendChart(option) {
      var endDate = null;
      var startDate = null;

      if (option.startDate && option.endDate) {
        startDate = option.startDate;
        endDate = option.endDate;
      } else {
        endDate = option.date;
        startDate = new Date(option.date);
        startDate.setDate(option.date.getDate() - 30);

        startDate = dayjs(startDate).format("YYYY-MM-DD");
        endDate = dayjs(endDate).format("YYYY-MM-DD");
      }
      option.filter.filters = option.filter.filters.filter((item) => item.field !== option.dateField);
      option.filter.filters.push({
        field: option.dateField,
        operator: "GreaterThanOrEqual",
        value: startDate,
      });
      option.filter.filters.push({
        field: option.dateField,
        operator: "LessThanOrEqual",
        value: endDate,
      });

      option.startDate = startDate;
      option.endDate = endDate;

      this.chatProp.chatTime = [startDate, endDate];

      this.chatProp.chatLoading = true;

      const { data, success } = await getTrendChart(option);
      if (success) {
        this.chatProp.data = data;
      }

      this.chatProp.chatLoading = false;
      this.chatProp.chatDialog = true;

      this.chatPropOption = option;
    },
    // 查询生成加工计划
    async CreatePlan() {
      this.$message.info("正在生成中，请稍后...");
      try {
        const { data, success } = await getWaitPrePacks(this.query);
        if (success) {
          this.$message.success("生成成功");
        }
      } catch (error) {
        this.$message.error("失败");
      }
    },
    async queryCreatePlan() {
      this.query.isGenerateManifest = true;
      if (this.total > 100) {
        this.$confirm(`预计生成${this.total}条加工计划，是否继续?`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            await this.CreatePlan();
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消",
            });
          });
      } else {
        await this.CreatePlan();
      }
    },
    close() {
      this.processingOrderVisable = false;
    },
    async selectCheckBox() {
      if (this.goodsCodeList.length === 0)
        return this.$message.error("至少选择一条数据");
      this.processingOrderVisable = true;
    },
    checkboxRangeEnd(row) {
      this.goodsCodeList = row.map((item) => item.goodsCode);
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          try {
            if (this.ruleForm.yiWuWmsNames) {
              this.ruleForm.yiWuWmsName = JSON.stringify(
                this.ruleForm.yiWuWmsNames
              );
            }

            await setting(this.ruleForm);
            this.$message({
              type: "success",
              message: "提交成功!",
            });
            this.addVisable = false;
            this.getList();
          } catch (error) {
            this.$message.error("提交失败");
          }
        } else {
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    async getPrePackSet() {
      const { data, success } = await getSetting();
      if (success) {
        if (data.yiWuWmsName) {
          data.yiWuWmsNames = JSON.parse(data.yiWuWmsName);
        }
        this.ruleForm = data || {};
      }
    },
    async prePackSet() {
      await this.getPrePackSet();
      this.addVisable = true;
    },
    async getCol() {
      const { data, success } = await getColumns();
      if (success) {
        // 在data顶部添加一列
        data.unshift({
          label: "",
          type: "checkbox",
        });
        this.tableCols = data;
        this.query.summarys = data
          .filter((a) => a.summaryType)
          .map((a) => {
            return { column: a["sort-by"], summaryType: a.summaryType };
          });
      }
    },
    // 导出数据,这里前端可以封装一个方法
    async exportProps() {
      this.isExport = true;
      await exportData(this.query)
        .then(download)
        .finally(() => {
          this.isExport = false;
        });
    },
    async getList(type) {
      if (type === "search") {
        this.query.currentPage = 1;
        this.$refs.pager.setPage(1);
      }
      this.loading = true;
      // 使用时将下面的方法替换成自己的接口
      try {
        const { data, success } = await pageGetData(this.query);
        if (success) {
          data.list.forEach((item) => {
            item.canPackWmsesList = item.canPackWmsesList
              ? item.canPackWmsesList.sort(
                (a, b) => b.subGoodsUsableQty - a.subGoodsUsableQty
              )
              : [];
          });
          this.data = data;
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.loading = false;
      }
    },
    // 每页数量改变
    Sizechange(val) {
      this.query.currentPage = 1;
      this.query.pageSize = val;
      this.getList();
    },
    // 当前页改变
    Pagechange(val) {
      this.query.currentPage = val;
      this.getList();
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.query.orderBy = prop;
        this.query.isAsc = order.indexOf("descending") === -1;
        this.getList();
      }
    },
  },
};
</script>
<style scoped lang="scss">
.condition {
  height: 35px;
  max-height: 35px;
  overflow: hidden;
  position: relative;

  &:hover {
    overflow: visible;

    .top {
      box-shadow: 2px 2px 2px 1px rgba(0, 0, 0, 0.2);
    }
  }

  .top {
    position: absolute;
    background: white;
    z-index: 100;
    top: 0;

    .el-col {
      height: 30px;
      margin-bottom: 5px;

      .publicCss {
        width: 100%;
      }
    }
  }
}
</style>
