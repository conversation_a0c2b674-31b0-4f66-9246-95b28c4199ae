<template>
  <div class="ces-main">
    <el-table :data="traindata">
      <el-table-column type="index" width="60"></el-table-column>
      <el-table-column prop="coach" label="教练" width="120" />
      <el-table-column
        prop="trainees"
        sortable
        label="被培训人"
        width="420"
      ></el-table-column>
      <el-table-column
        prop="tDate"
        sortable
        label="时间"
        width="220"
      ></el-table-column
    ></el-table>
    <div class="block">
      <el-pagination
        layout="prev, pager, next"
        @current-change="changePage"
        :total="total"
         :page-size="pageSize"
      >
      </el-pagination>
    </div>
  </div>
</template>
 
        
    
<script>
import { gethistrainplandata } from "@/api/customerservice/trainplan";
export default {
  data() {
    return {
      that: this,

      loading: false,
      productid:'',
      total:0,
pageSize:5,
      pager: {
        total: 0,
        pageIndex: 1,
        pageSize: 5,
        OrderBy: "traindate",
        isAsc:false,
        Trainstatus: 2,
      },
      traindata: [],
    };
  },
  components: {},
  computed: {},
  methods: {
     clear(){
  this.traindata=[];

    },
    changePage(e) {

     var that = this;
      that.traindata = [];
      

      const params = {
         
        ...this.pager,
      };
      params.productid = that.productid;
      this.pager.pageIndex=e;

      gethistrainplandata(params).then((res) => {
        that.traindata = res.data.list;
      });



    },
    init(productid) {
      var that = this;
      this.pager.pageIndex=0;
      that.traindata = [];
      // var ProductID=this.$router.query.

      
    

      const params = {
         
        ...this.pager,
      };
      params.productid = productid;

      gethistrainplandata(params).then((res) => {
        that.traindata = res.data.list;
        that.pager.total=res.data.total;
        that.total=res.data.total;
        
        that.$forceUpdate()
      });
    },
  },
  mounted() {
    this.init();
  },
};
</script>