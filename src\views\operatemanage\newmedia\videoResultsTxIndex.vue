<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <el-tabs v-model="activeName" style="height:94%;">
      
        <el-tab-pane label="视频数据管理" name="tab1" style="height: 100%;">
          <videoResultsTx :filter="filter" ref="videoResultsTx" style="height: 100%;"/>
      </el-tab-pane>
     
      <el-tab-pane label="投稿记录管理" name="tab3" style="height: 100%;">
          <videoContributeTx :filter="filter" ref="videoContributeTx" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane label="人员业绩" name="tab2" style="height: 100%;">
        <PersonnelPerformanceTx :filter="filter" ref="PersonnelPerformanceTx" style="height: 100%;"/>
    </el-tab-pane>
    <el-tab-pane label="店铺业绩" name="tab4" style="height: 100%;">
      <ShopPerformanceTx :filter="filter" ref="ShopPerformanceTx" style="height: 100%;"/>
  </el-tab-pane>
  <el-tab-pane label="原创视频店铺业绩" name="tab5" style="height: 100%;">
    <OriginalVideoShopPerformanceTx :filter="filter" ref="OriginalVideoShopPerformanceTx" style="height: 100%;"/>
</el-tab-pane>
  
     
    </el-tabs>
    </my-container >
  
   </template>
  <script>
  import MyContainer from "@/components/my-container";
  import videoResultsTx from '@/views/operatemanage/newmedia/videoResultsTx' 
  import publisher from '@/views/operatemanage/newmedia/publisher' 
  import videoinfo from '@/views/operatemanage/newmedia/videoinfo' 
  import shopinquirsstatistics from '@/views/operatemanage/newmedia/shopachievement' 
  import videoContributeTx from '@/views/operatemanage/newmedia/videoContributeTx' 
  import PersonnelPerformanceTx from '@/views/operatemanage/newmedia/PersonnelPerformanceTx' 
  import ShopPerformanceTx from '@/views/operatemanage/newmedia/ShopPerformanceTx' 
  import OriginalVideoShopPerformanceTx from '@/views/operatemanage/newmedia/OriginalVideoShopPerformanceTx' 
  

  
  export default {
    name: "Users",
    components: { MyContainer,videoResultsTx,publisher,videoinfo,shopinquirsstatistics,videoContributeTx,PersonnelPerformanceTx,ShopPerformanceTx,OriginalVideoShopPerformanceTx},
    data() {
      return {
        that:this,
        pageLoading:'',
        filter: {
        },
        shopList:[],
        userList:[],
        groupList:[],
        selids:[],
        dialogVisibleSyj:false,
        fileList:[],
        activeName:'tab1'
      };
    },
    mounted() { 
      window.showtab4=this.showtab4
    },
    methods: {
      showtab4(){
       this.activeName="tab3"
  
  
      }
  
    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
  