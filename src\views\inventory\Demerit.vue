<template>
    <container>
        <template #header>
            <el-form :inline="true">
                <el-form-item label="添加时间:">
                    <el-date-picker style="width:230px" v-model="timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        :picker-options="pickerOptions" @change="changeTime"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.deptId" placeholder="请选择采购组" :clearable="true" filterable>
                        <el-option v-for="item in deptList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.post" placeholder="请选择岗位" :clearable="true" filterable>
                        <el-option v-for="item in postList" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.userId" placeholder="请选择采购" :clearable="true" filterable>
                        <el-option v-for="item in userList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="getList('Search')">查询</el-button>
                    <el-button type="primary" @click="openAddForm">新增</el-button>
                    <el-button type="primary" @click="importFile">导入</el-button>
                    <el-button type="primary" @click="exportList">导出</el-button>
                </el-form-item>
            </el-form>
        </template>
        <vxetablebase ref="table" :id="'Demerit202040828'" :that='that' :isIndex='true' @sortchange='sortchange'
            :isSelection='true' :hasexpand='true' :tableData='list' :tableCols='tableCols'
            :loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
        <el-dialog title="新增" :visible.sync="visible" width="30%" v-dialogDrag>
            <el-form style="margin: 0 auto;margin-top: 30px;width: 220px;text-align: right;">
                <el-form-item label="采购:">
                    <el-select v-model="form.userId" placeholder="请选择被投诉人" :clearable="true" filterable
                        style="width: 130px;">
                        <el-option v-for="item in userList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="采购单备注:">
                    <el-input-number v-model="form.orderDemerit" :min="0" :max="100" step-strictly></el-input-number>
                </el-form-item>
                <el-form-item label="Erp备注:">
                    <el-input-number v-model="form.erpDemerit" :min="0" :max="100" step-strictly></el-input-number>
                </el-form-item>
            </el-form>
            <div style="width: 80%;margin: 0 auto;text-align: right;margin-top: 30px;">
                <el-button type="primary" @click="submitForm">保存</el-button>
                <el-button type="primary" @click="visible = false">取消</el-button>
            </div>
        </el-dialog>
        <el-dialog title="导入行为扣分" :visible.sync="uploadVisible" v-dialogDrag width="20%" >
            <div style="width: 200px;height: 100px;">
            <el-upload ref="upload" :auto-upload="false" :multiple="false" action accept=".xlsx"
                            :http-request="uploadRequest" :on-change="uploadChange" :on-remove="uploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploading"
                                @click="uploadSubmit">{{ (uploading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
            </div>
        </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue"
import { AddDemerit, ExportDemerit, GetDeptList, GetPostList, GetUserList, QueryDemerit, ImportDemeritFile } from '@/api/inventory/Demerit.js'
import { formatTime, pickerOptions } from '@/utils/tools'
const tableCols = [
    { istrue: true, prop: 'userName', label: '采购', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'deptName', label: '组', width: '270', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'post', label: '岗位', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'createdTime', label: '时间', sortable: 'custom', align: 'center', formatter: (row) => formatTime(row.createdTime, "YYYY-MM-DD") },
    { istrue: true, prop: 'orderDemerit', label: '采购单备注', sortable: 'custom', align: 'center', formatter: (row) => { return row.orderDemerit * -1 } },
    { istrue: true, prop: 'erpDemerit', label: 'erp备注', sortable: 'custom', align: 'center', formatter: (row) => { return row.erpDemerit * -1 } },
    { istrue: true, prop: 'demerit', label: '汇总', sortable: 'custom', align: 'center', formatter: (row) => { return row.demerit * -1 } },
]

export default {
    name: 'Demerit',
    components: { container, vxetablebase },
    data() {
        return {
            that: this,
            userList: null,
            deptList: null,
            postList: null,
            filter: {
                startTime: null,
                endTime: null,
                deptId: null,
                post: null,
                userId: null,
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
            form: {
                userId: null,
                orderDemerit: null,
                erpDemerit: null,
            },
            tableCols,
            list: [],
            loading: false,
            total: null,
            visible: false,
            pickerOptions,
            timerange: [],
            uploadVisible:false,
            fileList: [],
            uploading: false,
        };
    },

    async mounted() {
        this.deptList = await GetDeptList();
        this.postList = await GetPostList();
        this.userList = await GetUserList();
        await this.getList();
    },
    methods: {
        //获取列表
        async getList(val) {
            this.loading=true;
            if (val == "Search") {
                this.filter.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            const { success, data } = await QueryDemerit(this.filter);
            if (success) {
                this.list = data?.list;
                this.total = data?.total;
            }
            this.loading=false;
        },
        //导出
        async exportList() {
            const res = await ExportDemerit(this.filter);
            if (!res?.data) return this.$message.error("导出失败,请稍后重试");
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '行为扣分' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
        //导入
        async importFile() {
            console.log(1);
            this.uploadVisible = true;
            this.uploading=false;
            this.$refs.upload.clearFiles();
            this.fileList.splice(0, 1);

        },
        async uploadRequest(item) {
            const form = new FormData();
            form.append("upfile", item.file);
            const{success} = await ImportDemeritFile(form);
            if(success)
            {
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
                this.uploadVisible=false;
            }
        },
        async uploadChange(file, fileList) {
            if (fileList.length == 2) {
                fileList.splice(1, 1);
                this.$message({ message: "只允许单文件导入", type: "warning" });
                return false;
            }
            this.fileList.push(file);
        },
        uploadRemove(file, fileList) {
            this.fileList.splice(0, 1);
        },
        uploadSubmit() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return;
            }
            this.$refs.upload.submit();
            this.$refs.upload.clearFiles();
            this.fileList.splice(0, 1);
        },
        //每页数量改变
        Sizechange(val) {
            this.filter.currentPage = 1;
            this.filter.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.filter.currentPage = val;
            this.getList()
        },
        //排序查询
        sortchange({ order, prop }) {
            if (prop) {
                this.filter.orderBy = prop
                this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        openAddForm() {
            this.form.userId = "";
            this.form.orderDemerit = "";
            this.form.erpDemerit = "";
            this.visible = true;
        },
        //提交表单
        async submitForm() {
            if (!this.form.userId) {
                this.$message.error("请选择采购");
                return;
            }
            const { success } = await AddDemerit(this.form);
            if (success) {
                this.$message.success("添加成功");
                this.visible = false;
                this.getList();
            }
        },
        //改变时间
        async changeTime(e) {
            this.filter.startTime = e ? e[0] : null;
            this.filter.endTime = e ? e[1] : null;
        },
    },
};
</script>

<style lang="scss" scoped></style>