<template>
    <my-container v-loading="pageLoading"  :element-loading-text="upmsginfo">
      <div class="scdgwj">
        <div class="cgbt">微详情视频-上传成果</div>
        <div class="cgcz">
          <span>任务简称：</span><span>{{rowinfo  }}</span>&nbsp;&nbsp;|&nbsp;&nbsp;<span >{{productname}}  </span>
          <div style="float: right; position: relative; top: -2px">
            <el-button size="mini" type="primary"   @click="sumbitUploadInfo()">保存修改</el-button>
          </div>
        </div>
        <div class="cgscq">
          <div class="cgscqjz">
            <div class="ywj"  v-for="(i,index ) in InitDataArrayOne" :key="index">
                <packdesginfileforsuccess  
                    v-if="refesh"
                    :ref="i.refName" 
                    :uploadInfo="i.uploadinfo" 
                    :limit="10000" 
                    :islook="islook"
                    :accepttyes="i.acctpye" 
                    :uptype="i.uptype" 
                    :title="i.title" 
                />
            </div>

          </div>
        </div>
      </div>
    </my-container>
  </template>
  
  <script>
  import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
  import packdesginfileforsuccess from '@/views/media/shooting/packdesgin/packdesginfileforsuccess';
  import MyContainer from "@/components/my-container";
  import {getUploadSuccessAttachmentNew,checkShootingTaskAction,uploadSuccessAttachment  } from '@/api/media/microvedio';
  export default { 
    components: { packdesginfileforsuccess ,MyContainer},
    props:{ 
        islook:{ type: Boolean, default:false },
        isdown:{ type: Boolean, default:false }, 
    },
    data() {
        return {
            pageLoading:false,
            InitDataArrayOne:[],
            InitDataArrayTwo:[],
            imgtype:".image/jpg,image/jpeg,image/png",
            rartype:".rar,.zip,.7z",
            psdtype:".psd,.psb",
            vediotype:".mp4,.mov,.vedio,.av,.wmv,.mpg,.mpeg,.rm,.flv,.swf", 
            num:1,
            productname:null,
            upmsginfo:'努力加载中',
            uploadinfos:[],
            uploadtype:0,
            upmsgtxt:null,
            percentage:0,
            contentshow: true,
            rowinfo:null,
            refesh :true,
        };
    },
  
    async mounted() {
        await this.gettaskid();
        await this.initdata();
    },
  
    methods: {
        gettaskid(){
            this.rowinfo = this.$route.query.id;
            this.productname = this.$route.query.name;
        },
        async initdata(){ 
            this.contentshow = false;
            var ret = await getUploadSuccessAttachmentNew({taskId:this.rowinfo});
            if(ret?.success){
                this.InitDataArrayOne = ret?.data?.initDataArrayOne;  
            }
            this.contentshow = true; 
        },
        whactclick(num){
            let _this = this;
            _this.num = num;
        },
         //判断是否改变
         getIsChange(){
            var ischange  = false;
            for(let num in this.InitDataArrayOne)
            { 
                var infos = this.$refs[this.InitDataArrayOne[num].refName][0]?.getChange();
                if(infos){
                    ischange =true;
                } 
            } 
            return ischange;
        },
        getUploadInfo(){ 
            var retarray = [];
            for(let num in this.InitDataArrayOne)
            { 
                var infos = this.$refs[this.InitDataArrayOne[num].refName][0].getReturns();
                var uploadType = this.InitDataArrayOne[num].uploadType;
                var fileType = this.InitDataArrayOne[num].fileType;
                infos.data.forEach(function(item){
                    item.uploadType = uploadType
                    item.fileType = fileType
                    retarray.push(item);
                });
            }
      
            return retarray;
        },
        //提交上传的信息
        async sumbitUploadInfo()
         {
            var res =  await checkShootingTaskAction({taskid:this.rowinfo});
            if(!res?.success){
                return
            }
            if(!this.getIsChange()){
                this.$message({ message: '未发生改变，请勿提交', type: "error" });
                return;
            }
            //校验是否可以提交

            this.uploadinfos = this.getUploadInfo();
            this.pageLoading = true;
            this.tempoutlist = [];
            this.uploadinfos.forEach(element => { 
                this.tempoutlist.push(element); 
            });
            this.startIndex = this.tempoutlist.length;
           
            for (var i = 0; i < this.tempoutlist.length; i++) {
                var item = this.tempoutlist[i];
                this.atfterUplaodData = null;
                this.percentage = 0;
                this.startIndex = this.startIndex - 1;
                //判断是否需要上传  //只上传新加的文件
                if(item.filestaus == 0){
                    this.upmsgtxt = "正在上传："+ item.file.name 
                    this.upmsginfo =this.upmsgtxt +"   " +  this.percentage +"%";
                    await this.AjaxFile(item.file, 0, "").then(async x=>{ 
                        if (this.atfterUplaodData != null) {
                            await this.afteruploadtempList(item, this.startIndex);
                        }
                    }).catch(err => { 
                        console.log(err)
                    });  
                   
                }else{
                    this.upmsginfo = "正在处理："+item.fileName;
                    await this.afteruploadtempList(item, this.startIndex);
                }
                this.percentage = 0;
            }  
            this.pageLoading = false;
        },
        //startIndex == 0 判断全部上传完成，也代表文件的顺序，采用倒序排版
        async afteruploadtempList(item,startIndex){
            //进行业务处理
            var params = {
                ...item,
                microVedioTaskId:this.rowinfo,
                OrderNum: startIndex
            }
            if(item.filestaus == 0){
                params.url = this.atfterUplaodData.url;
                params.relativePath = this.atfterUplaodData.relativePath;
            }
            var res =  await uploadSuccessAttachment(params);
            //上传到数据记录中
            if(res?.success)
            {
                for (let num in this.uploadinfos) {
                    if (this.uploadinfos[num].uid == item.uid) {
                        this.uploadinfos.splice(num, 1)
                    }
                }
            }
            if (startIndex == 0) {
                this.upmsginfo = "上传完成！";
                this.$message({ message: '上传完成！', type: "success" });
                if (this.uploadinfos.length > 0) 
                {
                    this.$message({ message: '存在操作失败信息请查看，截图保存', type: "error" });
                    this.dialogVisibleSyjout = true;
                    this.uploadtype = item.uploadType;
                }else{ 
                    this.refesh =false;
                    await this.initdata();
                    this.refesh =true;
                }
            }
        },
         //切片上传
        async AjaxFile(file, i, batchnumber) {
            var name = file.name; //文件名
            var size = file.size; //总大小shardSize = 2 * 1024 * 1024,
            var shardSize = 1*1024 * 1024;
            var shardCount = Math.ceil(size / shardSize); //总片数
            if (i >= shardCount) {
                return;
            }
            //计算每一片的起始与结束位置
            var start = i * shardSize;
            var end = Math.min(size, start + shardSize);
            //构造一个表单，FormData是HTML5新增的
            i = i + 1;
            var form = new FormData();
            form.append("data", file.slice(start, end)); //slice方法用于切出文件的一部分
            form.append("batchnumber", batchnumber);
            form.append("fileName", name);
            form.append("total", shardCount); //总片数
            form.append("index", i); //当前是第几片
            const res = await xMTVideoUploadBlockAsync(form);
            if (res?.success) {
                this.percentage = (i * 100 / shardCount).toFixed(2);
                this.upmsginfo =this.upmsgtxt +"   " +  this.percentage +"%";
                if (i == shardCount) {
                    this.atfterUplaodData = res.data;
                } else {
                    await this.AjaxFile(file, i, res.data);
                }
            } else {
                this.$message({ message: res?.msg, type: "warning" });
            }
        }, 

    },
  };
  </script>
  
  <style lang="scss" scoped>
    ::v-deep .cgbt {
    width: 100%;
    margin: 0 auto;
    min-width: 1250px;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 20px 35px 10px 35px;
    font-size: 18px;
    color: #666;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
  }
  
  ::v-deep .cgcz {
    width: 100%;
    margin: 0 auto;
    min-width: 1250px;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 15px 60px;
    color: #666;
    box-shadow: 0px 3px 5px #eeeeee;
  }
  ::v-deep .cgscq {
    width: 100%;
    background-color: #f3f6f7;
  }
  ::v-deep  .cgscqjz {
    width: 80%;
    min-width: 1600px;
    margin: 0 auto;
    box-sizing: border-box;
    padding: 20px 0;
  }
  ::v-deep .ywj,
  .ywjq,
  .xgt,
  .sjg {
    width: 24%;
    min-width: 360px;
    /* height: 500px; */
    background-color: rgb(255, 255, 255);
    margin: 0.5%;
    display: inline-block;
    box-sizing: border-box;
    overflow: hidden;
  }
  
  ::v-deep .cgxbt {
    color: #666;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
    padding: 10px 20px;
    line-height: 32px;
  }
  
  ::v-deep .wjnrq {
    height: 400px;
    box-sizing: border-box;
    padding: 15px 30px;
    overflow: auto;
    background-color: rgb(255, 255, 255);
  }
  
  ::v-deep .wjdnr {
    width: 75%;
    display: inline-block;
    font-size: 14px;
    color: #666;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 20px;
  } 
  </style>