<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="query.startTime" :endDate.sync="query.endTime" class="publicCss" />
                <dateRange :startDate.sync="query.startOnTime" :endDate.sync="query.endOnTime" class="publicCss"
                    startPlaceholder="开始上架时间" endPlaceholder="结束上架时间" />
                <el-input v-model.trim="query.styleCode" placeholder="系列编码" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="query.productId" placeholder="宝贝ID" maxlength="50" clearable
                    class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" size="mini" @click="exportProps">导出</el-button>
                <el-button type="primary" size="mini" @click="hangUp(assignIdList, 0, 'batch')">批量挂走</el-button>
                <el-button type="primary" size="mini" @click="hangUp(assignIdList, 1, 'batch')">批量下架</el-button>
            </div>
        </template>
        <vxetablebase :id="'Violations_details202408041838'" ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;height: 400px;"
            :showsummary="data.summary ? true : false" :summaryarry="data.summary" @sortchange="sortchange"
            @select="checkboxRangeEnd" @onTrendChart="trendChart">
            <template slot="right" v-if="groupId == selfGroupId || checkPermission('pblGoodsApproval')">
                <vxe-column title="操作" width="120" fixed="right">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="hangUp(row, 0)">挂走</el-button>
                            <el-button type="text" @click="hangUp(row, 1)">下架</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false"
            :modal="false" direction="btt">
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
                    :picker-options="pickerOptions" style="margin: 10px" @change="
                        trendChart({
                            ...chatPropOption,
                            startDate: $event[0],
                            endDate: $event[1],
                        })
                        " />
                <buschar v-if="!chatProp.chatLoading" :analysis-data="chatProp.data" />
            </div>
            <div v-else v-loading="chatProp.chatLoading" />
        </el-drawer>


    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, formatLinkProCode } from '@/utils/tools'
import dayjs from 'dayjs'
import buschar from "@/components/Bus/buschar";
import dateRange from "@/components/date-range/index.vue";
import request from '@/utils/request'
import { download } from '@/utils/download'
import AnchoredPage from './Anchored_Page.vue'
import checkPermission from '@/utils/permission'
import { getUserInfo } from '@/api/operatemanage/productalllink/alllink'
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, AnchoredPage, buschar
    },
    props: {
        styleCode: {
            type: String,
            default: ''
        },
        groupId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            chatPropOption: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            that: this,
            query: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                summarys: [],
                styleCode: this.styleCode,
            },
            data: {},
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            assignIdList: [],
            selfGroupId: null
        }
    },
    async mounted() {
        this.init()
        await this.getCol()
        await this.getList()
    },
    methods: {
        async init() {
            const { data, success } = await getUserInfo()
            if (success) {
                this.selfGroupId = data.groupId
            }
        },
        checkboxRangeEnd(row) {
            this.assignIdList = row
        },
        async exportProps() {
            this.isExport = true
            await request.post('/api/bookkeeper/pblGoodPdd/IllegalListing/ExportData', this.query, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        hangUp(row, val, type) {
            if (type == 'batch' && this.assignIdList.length == 0) return this.$message.error('请选择要操作的数据')
            let params = []
            if (type == 'batch') {
                params = row
                params.forEach(item => {
                    item.type = val
                    item.oriGroupId = item.assignGroupId
                    item.oriGroupName = item.groupName
                })
            } else {
                params = row
                params.type = val
                params.oriGroupId = params.assignGroupId
                params.oriGroupName = params.groupName
            }
            this.$confirm(`是否${val == 0 ? '挂走' : '下架'}, 是否继续?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await request.post('/api/bookkeeper/PblGoodPdd/Product/Apply', type == 'batch' ? params : [params])
                if (success) {
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                    this.getList()
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
        },
        async getCol() {
            const { data, success } = await request.post('/api/bookkeeper/pblGoodPdd/IllegalListing/GetColumns');
            if (success) {
                data.unshift({
                    label: "",
                    type: "checkbox",
                });
                data.forEach(item => {
                    if (item.prop == 'productId') {
                        item.type = 'html'
                        item.formatter = (row) => formatLinkProCode(row.platform, row.productId)
                    }
                })
                this.tableCols = data;
                this.query.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async trendChart(option) {
            var endDate = null;
            var startDate = null;

            if (option.startDate && option.endDate) {
                startDate = option.startDate;
                endDate = option.endDate;
            } else {
                endDate = option.date;
                startDate = new Date(option.date);
                startDate.setDate(option.date.getDate() - 30);

                startDate = dayjs(startDate).format("YYYY-MM-DD");
                endDate = dayjs(endDate).format("YYYY-MM-DD");
            }
            option.filter.filters = option.filter.filters.filter((item) => item.field !== option.dateField);
            option.filter.filters.push({
                field: option.dateField,
                operator: "GreaterThanOrEqual",
                value: startDate,
            });
            option.filter.filters.push({
                field: option.dateField,
                operator: "LessThanOrEqual",
                value: endDate,
            });

            option.startDate = startDate;
            option.endDate = endDate;

            this.chatProp.chatTime = [startDate, endDate];

            this.chatProp.chatLoading = true;

            const { data, success } = await request.post('/api/bookkeeper/pblGoodPdd/IllegalListing/GetTrendChart', option);
            if (success) {
                this.chatProp.data = data;
            }

            this.chatProp.chatLoading = false;
            this.chatProp.chatDialog = true;

            this.chatPropOption = option;
        },
        async getList(type) {
            if (type === "search") {
                this.query.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post('/api/bookkeeper/pblGoodPdd/IllegalListing/PageGetData', this.query)
                if (success) {
                    data.list.forEach((item) => {
                        item.canPackWmsesList = item.canPackWmsesList
                            ? item.canPackWmsesList.sort(
                                (a, b) => b.subGoodsUsableQty - a.subGoodsUsableQty
                            )
                            : [];
                    });
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.query.currentPage = 1;
            this.query.pageSize = val;
            this.getList();
        },
        //当前页改变
        Pagechange(val) {
            this.query.currentPage = val;
            this.getList();
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.query.orderBy = prop;
                this.query.isAsc = order.indexOf("descending") == -1 ? true : false;
                this.getList();
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
