<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss" />
                <el-select v-model="ListInfo.applyStatus" placeholder="申报状态" class="publicCss" clearable multiple
                    collapse-tags>
                    <el-option label="待发起" value="待发起" />
                    <el-option label="审批中" value="审批中" />
                    <el-option label="通过" value="通过" />
                    <el-option label="拒绝" value="拒绝" />
                </el-select>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="goodsCodesCallback" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;" class="publicCss">
                </inputYunhan>
                <el-select filterable v-model="ListInfo.groupIds" collapse-tags multiple clearable placeholder="运营组"
                    class="publicCss" style="width: 160px">
                    <el-option key="无运营组" label="无运营组" :value="0"></el-option>
                    <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select filterable v-model="filter.directorIds" collapse-tags clearable placeholder="申报人"
                    class="publicCss" multiple style="width: 160px">
                    <el-option key="无运营专员" label="无运营专员" :value="0"></el-option>
                    <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select filterable v-model="ListInfo.platforms" clearable placeholder="平台" class="publicCss"
                    style="width: 160px" multiple collapse-tags>
                    <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :showsummary='true'
            :summaryarry="summaryarry" @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" border
            :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatPlatform } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { getDirectorGroupList, getDirectorList } from '@/api/operatemanage/base/shop'
import { pageStyleCodeGoodsStockApplyAsync } from '@/api/bookkeeper/styleCodeRptData'
import inputYunhan from "@/components/Comm/inputYunhan";
const tableCols = [
    { istrue: true, label: '小组头像', width: '70', type: 'ddAvatar', ddInfo: { type: 1, prop: 'groupId' } },
    { sortable: 'custom', width: '100', align: 'left', prop: 'groupId', label: '运营组', formatter: (row) => row.groupName, type: 'ddTalk', ddInfo: { type: 1, prop: 'groupId', name: 'groupName' }, },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'platform', label: '平台', formatter: (row) => formatPlatform(row.platform) },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'applyStatusStr', label: '申报状态', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'applyTime', label: '申报日期', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'applyQty', label: '申报数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'directorId', label: '申报人',formatter:(row) => row.directorName },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, inputYunhan
    },
    props: {
        filter: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            grouplist: [],
            directorlist: [],
            summaryarry: {}
        }
    },
    async mounted() {
        this.ListInfo = { ...this.ListInfo, ...this.filter }
        await this.init()
        await this.getList()
    },
    methods: {
        goodsCodesCallback(val) {
            this.ListInfo.goodsCodes = val
        },
        async init() {
            var { data } = await getDirectorGroupList();
            this.grouplist = data?.map(item => { return { value: item.key, label: item.value }; });
            var { data: data1 } = await getDirectorList();
            this.directorlist = data1?.map(item => { return { value: item.key, label: item.value }; });
        },
        async getList(type) {
            if (type == 'search') {
                let params = {
                    currentPage: this.ListInfo.currentPage,
                    pageSize: this.ListInfo.pageSize,
                    orderBy: this.ListInfo.orderBy,
                    isAsc: this.ListInfo.isAsc,
                    startTime: this.ListInfo.startTime ? dayjs(this.ListInfo.startTime).format('YYYY-MM-DD') : null,
                    endTime: this.ListInfo.endTime ? dayjs(this.ListInfo.endTime).format('YYYY-MM-DD') : null,
                    applyStatus: this.ListInfo.applyStatus,
                    goodsCodes: this.ListInfo.goodsCodes,
                    groupIds: this.ListInfo.groupIds,
                    directorIds: this.filter.directorIds,
                    platforms: this.ListInfo.platforms
                }
                this.ListInfo = { ...this.ListInfo, ...this.filter, ...params }
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await pageStyleCodeGoodsStockApplyAsync(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.summaryarry = data.summary
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
