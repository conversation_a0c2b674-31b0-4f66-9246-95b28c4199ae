<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <div class="left">
          <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
            style="width: 220px; margin-right: 5px;" :value-format="'yyyy-MM-dd'" :clearable="false"
            @change="changeTime($event, 1)">
          </el-date-picker>
          <el-date-picker style="width: 275px;margin-right: 5px;" v-model="shelvestimeranges" type="datetimerange"
          format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" range-separator="至" start-placeholder="上架开始时间"
          end-placeholder="上架结束时间" :picker-options="pickerOptions" @change="changeTime($event, 2)">
        </el-date-picker>
          <el-select filterable v-model="ListInfo.superviseId" placeholder="运营主管" clearable class="publicCss">
            <el-option v-for="item in superviselist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select filterable v-model="ListInfo.groupId" placeholder="运营组" clearable class="publicCss">
            <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
          <el-select filterable v-model="ListInfo.operateSpecialUserId" placeholder="运营专员" clearable class="publicCss">
            <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
          <el-select filterable v-model="ListInfo.userId" placeholder="运营助理" clearable class="publicCss">
            <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
          <el-select filterable v-model="ListInfo.shopCodeList" placeholder="店铺" clearable multiple collapse-tags class="publicCss" style="width:165px">
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
            </el-option>
          </el-select>
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" @click="exportProps">导出</el-button>
        </div>
      </div>
      <div style="margin-top: 10px;">
        <el-radio-group v-model="targetType" @input="onShowTarget">
          <el-radio-button label="全场景"></el-radio-button>
          <el-radio-button label="关键词推广"></el-radio-button>
          <el-radio-button label="货品运营"></el-radio-button>
          <el-radio-button label="全站推广"></el-radio-button>
          <el-radio-button label="人群推广"></el-radio-button>
          <el-radio-button label="多目标直投"></el-radio-button>
        </el-radio-group>
      </div>
    </template>
    <el-tabs v-model="activeName" style="height: 94%;" @tab-click="handleClick">
      <el-tab-pane label="运营主管" name="first2" lazy style="height: 100%">
        <superVisor ref="superVisor" :ListInfo="ListInfo" />
      </el-tab-pane>
      <el-tab-pane label="运营组" name="first3" lazy style="height: 100%">
        <operationsGroup ref="operationsGroup" :ListInfo="ListInfo" />
      </el-tab-pane>
      <el-tab-pane label="运营专员" name="first4" lazy style="height: 100%">
        <operationsSpecialist ref="operationsSpecialist" :ListInfo="ListInfo" />
      </el-tab-pane>
      <el-tab-pane label="运营助理" name="first5" lazy style="height: 100%">
        <operationsAssistant ref="operationsAssistant" :ListInfo="ListInfo" />
      </el-tab-pane>
      <el-tab-pane label="店铺" name="first6" lazy style="height: 100%">
        <operationsShop ref="operationsShop" :ListInfo="ListInfo" />
      </el-tab-pane>
      <el-tab-pane label="全量数据" name="first1" style="height: 100%">
        <fullData ref="fullData" :ListInfo="ListInfo" />
      </el-tab-pane>
    </el-tabs>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getDirectorList, getDirectorGroupList, getDirectorGroupList2, getAllList } from '@/api/operatemanage/base/shop'
import { pickerOptions } from '@/utils/tools'
import fullData from '@/views/operatemanage/advertisingFeeSummary/components/summaryComponents/fullData'
import superVisor from '@/views/operatemanage/advertisingFeeSummary/components/summaryComponents/superVisor'
import operationsGroup from '@/views/operatemanage/advertisingFeeSummary/components/summaryComponents/operationsGroup'
import operationsSpecialist from '@/views/operatemanage/advertisingFeeSummary/components/summaryComponents/operationsSpecialist'
import operationsAssistant from '@/views/operatemanage/advertisingFeeSummary/components/summaryComponents/operationsAssistant'
import operationsShop from '@/views/operatemanage/advertisingFeeSummary/components/summaryComponents/operationsShop'
import dayjs from 'dayjs'
export default {
  name: "expenseSummary",
  components: {
    MyContainer, vxetablebase, fullData, superVisor, operationsGroup, operationsSpecialist, operationsAssistant, operationsShop
  },
  data() {
    return {
      shelvestimeranges:[],
      superviselist: [],//运营主管列表
      directorGroupList: [],//运营组长列表
      directorList: [],//运营专员列表
      shopList: [],//店铺列表
      targetType: '全场景',
      activeName: 'first3',
      that: this,
      ListInfo: {
        startTime: null,//开始时间
        endTime: null,//结束时间
        onstartTime: null,//上架开始时间
        onendTime: null,//上架结束时间
        productName: null,
        groupId: null,//运营组长
        operateSpecialUserId: null,//运营专员
        userId: null,//运营助理
        superviseId: null,//运营主管
        shopCodeList: [],//店铺
      },
      timeRanges: [],
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.init()
    await this.getList()
  },
  methods: {
    async init() {
      //获取运营主管列表
      this.superviselist = [];
      let res2 = await getDirectorGroupList2();
      let sIds = res2.data?.filter(f => f.superviseId > 0).map(m => m.superviseId);
      let sIds2 = [...new Set(sIds)];
      if (sIds2.length > 0) {
        this.superviselist = sIds2
          .filter(id => res2.data.some(x => x.id === id))
          .map(id => res2.data.find(x => x.id === id))
          .map(cur => ({ value: cur.id, label: cur.userName }));
      }
      //获取运营专员列表
      const res1 = await getDirectorList({})
      this.directorList = res1.data || [];
      //获取运营组长列表
      const res3 = await getDirectorGroupList({})
      this.directorGroupList = res3.data || [];
      const res4 = await getAllList({ platforms: [1, 8, 9, 10] });
      this.shopList = [];
      res4.data?.forEach(f => {
        if (f.isCalcSettlement && f.shopCode && (f.platform == 1 || f.platform == 8 || f.platform == 9 || f.platform == 10))
          this.shopList.push(f);
      });
    },
    async exportProps() {
      this.$nextTick(() => {
        if (this.activeName == 'first1') {
          this.$refs.fullData.derivationMethod()
        } else if (this.activeName == 'first2') {
          this.$refs.superVisor.derivationMethod()
        } else if (this.activeName == 'first3') {
          this.$refs.operationsGroup.derivationMethod()
        } else if (this.activeName == 'first4') {
          this.$refs.operationsSpecialist.derivationMethod()
        } else if (this.activeName == 'first5') {
          this.$refs.operationsAssistant.derivationMethod()
        } else if (this.activeName == 'first6') {
          this.$refs.operationsShop.derivationMethod()
        }
      })
    },
    onShowTarget(type) {
      if (type == '全场景') {
        this.ListInfo.productName = null
      } else {
        this.ListInfo.productName = type
      }
      this.getList()
    },
    handleClick(tab, event) {
      this.activeName = tab.name
      this.$nextTick(() => {
        this.getList()
      })
    },
    async changeTime(e, val) {
      if (val == 1) {
        this.ListInfo.startTime = e ? e[0] : null
        this.ListInfo.endTime = e ? e[1] : null
      } else {
        this.ListInfo.onstartTime = e ? e[0] : null
        this.ListInfo.onendTime = e ? e[1] : null
      }
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      if (this.activeName == 'first1') {
        this.$refs.fullData.getList()
      } else if (this.activeName == 'first2') {
        this.$refs.superVisor.getList()
      } else if (this.activeName == 'first3') {
        this.$refs.operationsGroup.getList()
      } else if (this.activeName == 'first4') {
        this.$refs.operationsSpecialist.getList()
      } else if (this.activeName == 'first5') {
        this.$refs.operationsAssistant.getList()
      } else if (this.activeName == 'first6') {
        this.$refs.operationsShop.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.left {
  display: flex;
  align-items: center;
}

.publicCss {
  width: 150px;
  margin-right: 5px;
}

::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>
