<template >
    <my-container v-loading="pageLoading" style="height:100%">
        <ces-table style="height:90%" ref="openwebjushuitanjhpctable" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='jhpcList' :isSelection='false' :summaryarry="summaryarry"
            :tableCols='tableCols' :isSelectColumn="true">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-date-picker style="width: 225px" v-model="filter.dates" type="daterange" format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="同步开始时间"
                            end-placeholder="同步结束时间">
                        </el-date-picker>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.warehouse" clearable filterable placeholder="发货仓" style="width: 200px">
                            <el-option v-for="item in warehouselist" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input placeholder="商品编码" v-model="filter.sku_id" style="width: 130px" clearable
                            maxlength="20"></el-input>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input placeholder="款式编码" v-model="filter.i_id" style="width: 130px" clearable
                            maxlength="20"></el-input>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input placeholder="商品名称" v-model="filter.name" style="width: 160px" clearable
                            maxlength="100"></el-input>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getNewOrderListAsync" />
        </template>
    </my-container>
</template>
<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import datepicker from '@/views/customerservice/datepicker'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import { getJstKuCunCheckPageList, getTbWarehouseList } from '@/api/inventory/openwebjushuitan';
import { importStockExceedAndOtherWareAsync } from '@/api/inventory/inventoryorder';
const tableCols = [
    { istrue: true, prop: 'createdTime', label: '巡检时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'wms_co_name', label: '仓库', width: '140', sortable: 'custom' },
    { istrue: true, prop: 'sku_id', label: '商品编码', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'i_id', label: '款式编码', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'name', label: '商品名称', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'qty', label: '主仓实际库存', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'pick_lock', label: '仓库待发数', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'purchase_qty', label: '采购在途数', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'return_qty', label: '销退仓库存', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'in_qty', label: '进货仓库存', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'defective_qty', label: '次品库存', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'min_qty', label: '安全库存下限', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'max_qty', label: '安全库存上限', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'lock_qty', label: '库存锁定数', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'sendtime', label: '通知时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'sendusername', label: '通知人员', width: '100', sortable: 'custom' },
];
export default {
    name: "realtimeinventorycheck",
    components: { cesTable, MyContainer, datepicker },
    props: {

    },
    data() {
        return {
            warehouselist: [],
            pageLoading: false,
            tableCols: tableCols,
            jhpcList: [],
            that: this,
            sels: [], // 列表选中列
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "createdTime", IsAsc: false },
            filter: {
                dates: [formatTime(dayjs(), "YYYY-MM-DD"), formatTime(dayjs(), "YYYY-MM-DD")],
                sdate: null,
                edate: null,
                warehouse: null,
                wave_id: null,
            },
        }
    },
    async mounted() {
        await this.getInventoryWareHouseList()
        this.onSearch();
    },
    methods: {
        async getInventoryWareHouseList() {
            if (this.warehouselist.length <= 0) {
                let wares = await getTbWarehouseList();
                if (wares?.success && wares?.data && wares?.data.length > 0) {
                    wares?.data.forEach(f => {
                        if (f.manager_ddid)
                            this.warehouselist.push({ value: f.wms_co_id, label: f.name });
                    });
                }
            }
        },
        //排序查询      
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getNewOrderListAsync();
        },
        async getNewOrderListAsync() {
            if (this.filter.dates && this.filter.dates.length > 1) {
                this.filter.sdate = this.filter.dates[0];
                this.filter.edate = this.filter.dates[1];
            }
            else {
                this.filter.sdate = null;
                this.filter.edate = null;
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = { ...pager, ...page, ... this.filter }
            this.pageLoading = true;
            let res = await getJstKuCunCheckPageList(params);
            this.pageLoading = false;
            if (res?.success) {
                this.total = res.data.total
                this.jhpcList = res.data.list;
                //this.summaryarry = res.summary;
            }
        },
    }
}
</script>