<template>
    <my-container v-loading="pageLoading" style="height: 100%">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter">
                <el-form-item>
                    <el-date-picker style="width: 220px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="扣款开始日期" end-placeholder="扣款结束日期" @change="onSearch">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.proCode" style="width: 130px" placeholder="宝贝ID" @keyup.enter.native="onSearch" clearable />
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.orderNo" style="width: 200px" placeholder="订单编号" @keyup.enter.native="onSearch" clearable />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <span style="color:red;">每天11-12点自动统计截至昨日的运营责任扣款数据</span>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </my-container>
</template>

<script>
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import dayjs from "dayjs";
    import { formatTime, } from "@/utils";
    import { getOrderWithholdByYunYingPage } from "@/api/order/orderdeductmoney"
    const tableCols = [
        { istrue: true, prop: 'occurrenceTime', label: '扣款日期', width: '100', sortable: 'custom', formatter: (row) => formatTime(row.occurrenceTime, "YYYY-MM-DD") },
        { istrue: true, prop: 'orderNo', label: '订单编号', width: '190', sortable: 'custom' },
        { istrue: true, prop: 'proCode', label: '宝贝ID', width: '120', sortable: 'custom'},
        { istrue: true, prop: 'goodsName', label: '商品名称', sortable: 'custom', formatter: (row) => !row.goodsName ? "" : row.goodsName },
        { istrue: true, prop: 'shopId', label: '店铺', width: '180', formatter: (row) => !row.shopName ? " " : row.shopName },
        { istrue: true, prop: 'operateSpecialUserId', label: '运营专员', sortable: 'custom', width: '80', formatter: (row) => !row.operateSpecialUserName ? " " : row.operateSpecialUserName },
        { istrue: true, prop: 'amountPaid', label: '金额', width: '60', sortable: 'custom', formatter: (row) => parseFloat(row.amountPaid.toFixed(6)) },
        { istrue: true, prop: 'illegalType', label: '扣款原因', width: '120',sortable: 'custom', formatter: (row) => !row.illegalTypeName ? " " : row.illegalTypeName },
    ]

    export default {
        name: 'OrderIllegaldetailForYunYing',
        components: { cesTable, MyContainer, MyConfirmButton },
        props: {
            yyId: { type: Number, default: 0 },
        },
        data() {
            return {
                that: this,
                list: [],
                summaryarry: {},
                pager: { OrderBy: "OccurrenceTime", IsAsc: false },
                filter: {
                    operateSpecialUserId: this.yyId,
                    platform: 2,
                    timerange: [formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"), formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD")],
                    startDate: null,
                    endDate: null,
                    proCode: null,
                    orderNo: null,
                },
                tableCols: tableCols,
                total: 0,
                sels: [],
                pageLoading: false,
            };
        },
        async mounted() {
            await this.onSearch()
        },
        methods: {
            //查询第一页
            async onSearch() {
                console.log(this.filter)
                if (!this.filter.timerange) { this.$message({ message: "请选择日期", type: "warning", }); return; }
                this.$refs.pager.setPage(1)
                await this.getlist();
            },
            //获取查询条件
            getCondition() {
                this.filter.startDate = null
                this.filter.endDate = null
                if (this.filter.timerange && this.filter.timerange.length > 1) {
                    this.filter.startDate = this.filter.timerange[0];
                    this.filter.endDate = this.filter.timerange[1];
                }
                else {
                    this.$message({ message: "请先选择日期", type: "warning" });
                    return false;
                }
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }
                return params;
            },
            //分页查询
            async getlist() {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.pageLoading = true
                const res = await getOrderWithholdByYunYingPage(params)
                this.pageLoading = false
                if (!res?.success) {
                    return
                }
                console.log(res)
                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data.summary;
                this.list = data
            },
            //排序查询
            async sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            selectchange: function (rows, row) {
                this.selids = []; console.log(rows)
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
        },
    };
</script>

<style lang="scss" scoped></style>
