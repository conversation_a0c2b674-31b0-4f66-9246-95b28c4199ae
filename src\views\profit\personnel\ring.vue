<template>
    <div>
        <el-row :gutter="5" style="margin-top: 5px;">
            <el-col :span="8">
                <el-card style="width: 100%;">
                    <div id="chart-ringa" style="width: 100%; height:300px; " ref="ringcharta"></div>
                </el-card>
            </el-col>
            <el-col :span="8">
                <el-card style="width: 100%;">
                    <div id="chart-ringb" style="width: 100%; height:300px; " ref="ringchartb"></div>
                </el-card>
            </el-col>
            <el-col :span="8">
                <el-card style="width: 100%;">
                    <div id="chart-ringc" style="width: 100%; height:300px; " ref="ringchartc"></div>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>
  
<script>
import * as echarts from "echarts";
import bus from './bus.js';
import { hrHomePageLostDimissionPieCharts } from "@/api/profit/hr"

export default {
    name: "ring",//饼状图
    data () {
        return {
            ringCharta: null,
            ringOptionsa: null,
            ringChartb: null,
            ringOptionsb: null,
            ringChartc: null,
            ringOptionsc: null,
            ringaData: [],
            ringaDatb: [],
            ringaDatc: [],
            filter: {
                startDate: null,
                endDate: null,
                deptId: 0,
                position: null,
                // dateGroupType: 0,
            },

        };
    },
    created () {
    },
    destroyed () {
        window.removeEventListener('resize', this.handleResizeChart);
    },
    async mounted () {
        await bus.$on('filter', data => {
            for (const prop in data) {
                if (prop in this.filter) {
                    this.filter[prop] = data[prop];
                }
            }
            this.getData();
        })
        this.initChart();
        window.addEventListener('resize', this.handleResizeChart);

    },
    methods: {
        // 初始化图表
        initChart () {
            //饼状图 人次流失
            this.ringCharta = echarts.init(document.getElementById('chart-ringa'))
            // 配置参数
            this.ringOptionsa = {
                tooltip: {
                    trigger: 'item'
                },
                title: {
                    show: true,
                    subtext: '人才流失原因占比',
                },
                series: [
                    {
                        name: '人才流失原因',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 1
                        },
                        label: {
                            show: true,
                            position: 'outside'
                        },

                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 20,
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: true
                        },
                        data: [
                        ]
                    }
                ]
            }
            // 使用 setOption 方法将配置项设置为实例的属性
            this.ringCharta.setOption(this.ringOptionsa);

            //饼状图 离职类型
            this.ringChartb = echarts.init(document.getElementById('chart-ringb'))
            // 配置参数
            this.ringOptionsb = {
                tooltip: {
                    trigger: 'item'
                },
                title: {
                    show: true,
                    subtext: '离职类型占比',
                },
                series: [
                    {
                        name: '离职类型',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 1
                        },
                        label: {
                            show: true,
                            position: 'outside'
                        },

                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 20,
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: true
                        },
                        data: [
                        ]
                    }
                ]
            }
            // 使用 setOption 方法将配置项设置为实例的属性
            this.ringChartb.setOption(this.ringOptionsb);


            //饼状图 离职原因
            this.ringChartc = echarts.init(document.getElementById('chart-ringc'))
            // 配置参数
            this.ringOptionsc = {
                tooltip: {
                    trigger: 'item'
                },
                title: {
                    show: true,
                    subtext: '离职原因占比',
                },
                series: [
                    {
                        name: '离职原因',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 1
                        },
                        label: {
                            show: true,
                            position: 'outside'
                        },

                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 20,
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: true
                        },
                        data: [
                        ]
                    }
                ]
            }
            // 使用 setOption 方法将配置项设置为实例的属性
            this.ringChartc.setOption(this.ringOptionsc);


        },
        // 重绘图表
        handleResizeChart () {
            if (this.ringCharta) {
                this.ringCharta.resize();
            }
            if (this.ringChartb) {
                this.ringChartb.resize();
            }
            if (this.ringChartc) {
                this.ringChartc.resize();
            }
        },
        // 获取数据
        getData () {
            hrHomePageLostDimissionPieCharts(this.filter).then(res => {
                if (res.success) {
                    this.ringOptionsc.series[0].data = res.data.离职原因占比;
                    this.ringChartc.setOption(this.ringOptionsc);
                    this.ringOptionsa.series[0].data = res.data.人才流失原因占比;
                    this.ringCharta.setOption(this.ringOptionsa);
                    this.ringOptionsb.series[0].data = res.data.离职类型占比;
                    this.ringChartb.setOption(this.ringOptionsb);
                }
            })
        },

    }

}
</script>
  
<style  lang="scss" scoped>
.card-box {
    min-width: 75px;
    height: 50px;
    background-color: #f5faff;
    padding: 20px;
    text-align: center;
    float: left;
    margin: 5px;
    border-radius: 8px;

    .box-value {
        font-size: 22px;
        color: #409eff;
    }

    .box-title {
        font-size: 14px;
        color: #409eff;
    }
}
</style>