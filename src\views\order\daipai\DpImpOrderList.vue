<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <!-- <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                <el-button-group>
                             
                </el-button-group>
            </el-form>
        </template> -->
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='tbdatalist' @select='selectchange' 
        :isSelection='false' :isSelectColumn="true" :tableCols='tableCols' :loading="listLoading" :rowkey="'id'">

            <template slot='extentbtn'>
                <el-button-group>        
                        
                    <el-button type="primary" href="/static/excel/order/daipai/待代拍订单模板.xlsx" target="_blank" @click="downImportTemp(0)">下载导入模板</el-button>

                    <el-button type="primary" @click="dialogVisibleSyj=true">导入</el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="Filter.dpType" placeholder="代拍类型" clearable style="width:100px;">
                            <el-option :value="null" :label="'全部类型'" />
                            <el-option :value="0" :label="fmtDpType(0)" />
                            <el-option :value="1" :label="fmtDpType(1)" />
                            <el-option :value="2" :label="fmtDpType(2)" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="Filter.dpState" placeholder="代拍状态" clearable style="width:100px;">
                            <el-option v-for="item in AllLinkDaiPaiImpOrderDpStateOpts" :value="item.value" :label="item.label" :key="item.value"/>                          
                        </el-select>
                    </el-button>
                    
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="Filter.timeType" placeholder="时间类型" style="width:90px;">
                            <el-option :value="0" :label="'下单时间'" />
                            <el-option :value="1" :label="'付款时间'" />                            
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-date-picker style="width:220px" v-model="Filter.gDate" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"></el-date-picker>
                    </el-button>


                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="Filter.keywords" type="text" maxlength="100" clearable placeholder="请输入关键字..." style="width:200px;" >
                            <el-tooltip slot="suffix" class="item" effect="dark" :content="keywordsTip" placement="bottom">
                                <i  class="el-input__icon el-icon-question"></i>
                            </el-tooltip>                            
                        </el-input>
                    </el-button>
                    
                    <el-button type="primary" @click="onSearch" icon="el-icon-search">查询</el-button>
                    <el-button @click="()=>{Filter={};}"  icon="el-icon-close">清空查询条件</el-button>         

                </el-button-group>
            </template>
            
            <el-table-column width="120" label="操作" fixed="right">               
                <template slot-scope="scope"> 
                    <el-button type="text" v-if="scope.row.dpState===0" @click="onGenerateDpSpOrder(scope.row.id)" >生成代拍</el-button>
                    <el-button type="text" @click="onOpenDtl(scope.row.id,false)" >详情</el-button>
                </template>
            </el-table-column>

        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettbdatalist1" />
        </template>


        <el-dialog title="代拍订单导入" :visible.sync="dialogVisibleSyj" width="30%"  >
            <span>               
                <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx" 
                :http-request="uploadFile" :on-success="uploadSuccess" v-loading="uploading" >
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitUpload">上传</my-confirm-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>     

    </my-container>
</template>
<script>  

    import {
        PageDpImpOrderListAsync, ImportDpImpOrdersAsync,GenerateDpSpOrderAsync
    } from '@/api/order/alllinkDaiPai'

    import cesTable from "@/components/Table/table.vue";
    import { formatmoney, formatPercen, getUrlParam,formatTime, setStore, getStore,formatLink ,AllLinkDaiPaiImpOrderDpStateOpts,fmtAllLinkDaiPaiImpOrderDpState} from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    
    
    const fmtDpType=function(state){
        switch(state){
            case 0:return '待确认';
            case 1:return '运营代拍';
            case 2:return '采购代拍';
        }
        return '其他';
    }
    

    const tableCols = [        
        { istrue: true, prop: 'dpType', label: '代拍类型', width: '90', sortable: 'custom' ,formatter:(row)=>fmtDpType(row.dpType)},
        { istrue: true, prop: 'dpState', label: '代拍状态', width: '90', sortable: 'custom' ,formatter:(row)=>fmtAllLinkDaiPaiImpOrderDpState(row.dpState)},
        { istrue: true, prop: 'generateDpOrderReason', label: '异常原因', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'innerOrderNum', label: '内部订单号', width: '80', sortable: 'custom',type:'orderLogInfo',orderType:'orderNoInner'  },
        { istrue: true, prop: 'onlineOrderNum', label: '线上订单号', width: '160', sortable: 'custom' },
        { istrue: true, prop: 'shopName', label: '店铺名称', width: '110', sortable: 'custom' },
      
        { istrue: true, prop: 'goodsCode', label: '商品编码', width: '140', sortable: 'custom' },
        { istrue: true, prop: 'goodsName', label: '商品名称', minwidth: '170', sortable: 'custom' },
        { istrue: true, prop: 'orderTime', label: '下单时间', width: '100', sortable: 'custom',formatter:(row)=>formatTime(row.orderTime,'YYYY-MM-DD') },
        { istrue: true, prop: 'payTime', label: '付款日期', width: '100', sortable: 'custom',formatter:(row)=>formatTime(row.payTime,'YYYY-MM-DD') },
        { istrue: true, prop: 'goodsCjAmount', label: '成交额', width: '80', sortable: 'custom' },

        { istrue: true, prop: 'count', label: '数量', width: '60', sortable: 'custom' },

        { istrue: true, prop: 'shopStatus', label: '店铺状态', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'exceptionStatus', label: '状态', width: '60', sortable: 'custom' },
        { istrue: true, prop: 'exceptionType', label: '异常类型', width: '100', sortable: 'custom' },
        

        { istrue: true, prop: 'orderRemark', label: '订单备注', width: '120', sortable: 'custom' },

        { istrue: true, prop: 'buyerSjrProvince', label: '省', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'buyerSjrCity', label: '市', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'buyerSjrCounty', label: '区县', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'buyerSjrStreet', label: '街道', width: '100', sortable: 'custom' },     

    ];

    export default {
        name: "DpImpOrderList",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable }, 
        data() {
            return {
                that: this,
                AllLinkDaiPaiImpOrderDpStateOpts:AllLinkDaiPaiImpOrderDpStateOpts,
                Filter: {
                    gDate: [ ],
                    dpType:null,
                    timeType:0
                },
                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一月',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三月',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                tbdatalist: [],
                tableCols: tableCols,
                total: 0,
                pager: { OrderBy: "", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                uploading:false,
                //
                selids: [],
                dialogVisibleSyj:false,//导入订单
                keywordsTip:"支持搜索的内容：内外订单号、店名、商品编码名称、备注、省市县区街道、异常状态类型、店铺状态",
               

            };
        },
        async mounted() {

            this.onSearch();
        },
        methods: {
            downImportTemp() {               
                window.open("/static/excel/order/daipai/待代拍订单模板.xlsx");                
            },
            fmtDpType:fmtDpType,
            async onGenerateDpSpOrder(oid){
                this.pageLoading=true;
                let rlt=await GenerateDpSpOrderAsync({ImpOrderId:oid});
                this.pageLoading=false;
                if(rlt && rlt.success){
                    if(rlt.data.success){
                        this.$message.success('生成成功！');
                        this.onSearch();
                    }else{
                        this.$alert(rlt.data.errMsg, '失败');
                    }
                }
            },
            async uploadFile(item) {
                this.uploading=true;
                const form = new FormData();
                form.append("upfile", item.file);              
                const res =await ImportDpImpOrdersAsync(form);
                if(res && res.success){
                    this.$message({ message: `导入成功，新增：${res.data.newCount}条，重复${res.data.editCount}条！`, type: "success" });
                    this.onSearch();
                }
                this.uploading=false;
            },
            async uploadSuccess(response, file, fileList) {
                fileList.splice(fileList.indexOf(file), 1);
                this.dialogVisibleSyj=false;
            },
            async onSubmitUpload() {
                this.$refs.upload2.submit()
            },            
            onOpenDtl(oid,mode){   
                let self=this;
                this.$showDialogform({
                    path:`@/views/order/daipai/DpImpOrderForm.vue`,
                    title:'订单详情',
                    args:{oid:oid,mode:mode},
                    height:300,                    
                });   

            },          
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderField = column.prop;

                    this.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                }
                this.onSearch();
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.gettbdatalist1();
            },
            async gettbdatalist1() {
                if (this.Filter.gDate) {
                    this.Filter.timeStart = this.Filter.gDate[0];
                    this.Filter.timeEnd = this.Filter.gDate[1];
                }
                else {
                    this.Filter.timeStart = null;
                    this.Filter.timeEnd = null;

                }
                const para = { ...this.Filter };
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...para,
                };

                this.listLoading = true;
                const res = await PageDpImpOrderListAsync(params);

                this.listLoading = false;

                this.total = res.data.total
                this.tbdatalist = res.data.list;

            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
        },
    };
</script>
