<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss"
                    startPlaceholder="开始时间" endPlaceholder="结束时间" :valueFormat="'yyyy-MM-dd HH:mm:ss'"
                    type="datetimerange" :clearable="false" />
                <div style="display: flex;align-items: center;">
                    <div style="font-size: 14px;padding-bottom: 8px;">最小亏损:</div>
                    <el-input-number v-model="ListInfo.minProfit" :min="-100000" :max="999999999" placeholder="最小亏损"
                        :controls="false" :precision="0" class="publicCss" style="width: 60px;" />
                </div>
                <el-select v-model="ListInfo.platform" placeholder="平台" class="publicCss" clearable
                    style="width: 100px;">
                    <el-option v-for="item in platformlist" :label="item.label" :value="item.value" :key="item.value" />
                </el-select>
                <number-range class="publicCss" :min.sync="ListInfo.profitSumLossMin"
                    :max.sync="ListInfo.profitSumLossMax" min-label="亏损金额最小" max-label="亏损金额最大" />
                <number-range class="publicCss" :min.sync="ListInfo.orderCountLossMin"
                    :max.sync="ListInfo.orderCountLossMax" min-label="亏损订单最小" max-label="亏损订单最大" />
                <number-range class="publicCss" :min.sync="ListInfo.rateLossCountMin"
                    :max.sync="ListInfo.rateLossCountMax" min-label="亏损订单比例最小" max-label="亏损订单比例最大" />
                <number-range class="publicCss" :min.sync="ListInfo.rateProfitMin" :max.sync="ListInfo.rateProfitMax"
                    min-label="亏损金额比例最小" max-label="亏损金额比例最大" />
                <el-input v-model.trim="ListInfo.styleCode" placeholder="系列编码" maxlength="50" clearable
                    class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :loading="loading" id="srcviewsorderrealTimeLossesindex" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false" :isIndexFixed="true"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%" :isRemoteSort="false"
            @sortchange="sortchange" :showsummary="true" :summaryarry="data.summary">
            <template slot="right">
                <vxe-column title="操作" width="120">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="onTrendChart(row)">趋势图</el-button>
                            <el-button type="text" @click="lossDown(row.proCode, row.platform)">下架</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <!-- <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template> -->

        <el-dialog title="批量操作" :visible.sync="batchOperateVisible" width="70%" v-dialogDrag>
            <detailsPage :proCodes="proCodes" v-if="batchOperateVisible" :startTime="ListInfo.startTime"
                :endTime="ListInfo.endTime" />
        </el-dialog>

        <el-dialog title="趋势图" :visible.sync="chatProp.chatDialog" width="60%" v-dialogDrag @close="onclose">
          <div class="btnGroup">
            <span class="btnGroup_Css">类型:</span>
            <el-select v-model="chatProp.type" placeholder="类型" class="btnGroup_Css" @change="onTypeChart">
              <el-option key="小时" label="小时" :value="0" />
              <el-option key="天" label="天" :value="1" />
            </el-select>
            <span class="btnGroup_Css">日期:</span>
            <el-date-picker class="btnGroup_Css" v-model="timeRanges" type="datetimerange" unlink-panels range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false"
              style="width: 340px;" format="yyyy-MM-dd HH:mm" :value-format="'yyyy-MM-dd HH:mm'"
              @change="changeTime">
            </el-date-picker>
          </div>
          <div style="height: 550px;" v-loading="chatProp.chatLoading">
            <buschar ref="buschar" v-if="chatProp.refresh" :analysisData="chatProp.data"
              :loading="chatProp.chatLoading"></buschar>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button @click="chatProp.chatDialog = false">关闭</el-button>
          </span>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableVirtualScroll.vue";
import { pickerOptions, platformlist, formatLinkProCode, formatPlatform } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
import { getList as getShopList, getDirectorGroupList } from '@/api/operatemanage/base/shop'
import detailsPage from './component/detailsPage.vue'
const api = '/api/verifyOrder/SalePriceAnalysis/LossAls/'
import { getUserInfo } from "@/api/operatemanage/productalllink/alllink";
const statusList = [
    {
        label: '同意',
        value: 1
    },
    {
        label: '拒绝',
        value: 2
    },
    {
        label: '未操作',
        value: 0
    },
]
const dictionary = ['10005', '10113', '0', '10009', '10005', '41878', '41878', '10009', '41071']
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, detailsPage
    },
    data() {
        return {
            dictionary,
            batchOperateVisible: false,
            shopList: [],
            statusList,
            api,
            platformlist,
            that: this,
            ListInfo: {
                minProfit: -100,
                currentPage: 1,
                pageSize: 100000,
                orderBy: '',
                isAsc: false,
                styleCode: '',
                //当前时间往前推24小时
                startTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
                endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                startTime: null, // 趋势图开始时间
                endTime: null,  // 趋势图结束时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
                type: 0, // 趋势图类型
                proCode: null, // 趋势图proCode
                refresh: false, //刷新趋势图
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            grouplist: [],
            ids: [],
            batchOperateInfo: {
                ids: [],
                status: null
            },
            proCodes: ''
        }
    },
    async mounted() {
        if (this.$route.query.starttime) {
            this.ListInfo.startTime = dayjs(this.$route.query.starttime).format('YYYY-MM-DD HH:mm:ss')
        }
        if (this.$route.query.endtime) {
            this.ListInfo.endTime = dayjs(this.$route.query.endtime).format('YYYY-MM-DD HH:mm:ss')
        }
        this.getGroupList()
        await this.getCol();
        await this.getList()
    },
    methods: {
        //关闭趋势图
        onclose(){
          this.chatProp.startTime = null
          this.chatProp.endTime = null
          this.chatProp.data = []
          this.chatProp.proCode = null
          this.chatProp.type = 0
        },
        //清除时间给默认时间
        onTypeChart(){
          this.chatProp.startTime = null
          this.chatProp.endTime = null
          this.onTrendChart()
        },
        //趋势图时间改变
        changeTime(e) {
          this.chatProp.startTime = e ? e[0] : null
          this.chatProp.endTime = e ? e[1] : null
          this.onTrendChart()
        },
        //趋势图
        async onTrendChart(row){
          if(row){
            this.chatProp.proCode = row.proCode ? row.proCode : this.chatProp.proCode
          }
          if (this.chatProp.type === 0) {
            this.chatProp.endTime = this.chatProp.endTime ? this.chatProp.endTime : dayjs().format('YYYY-MM-DD HH:mm');
            this.chatProp.startTime = this.chatProp.startTime ? this.chatProp.startTime : dayjs(this.chatProp.endTime).subtract(1, 'day').format('YYYY-MM-DD HH:mm');
          }
          if (this.chatProp.type === 1) {
            this.chatProp.endTime = this.chatProp.endTime ? this.chatProp.endTime : dayjs().format('YYYY-MM-DD HH:mm');
            this.chatProp.startTime = this.chatProp.startTime ? this.chatProp.startTime : dayjs(this.chatProp.endTime).subtract(7, 'day').startOf('day').format('YYYY-MM-DD HH:mm');
          }
          this.timeRanges = [this.chatProp.startTime, this.chatProp.endTime]
          this.chatProp.chatLoading = true
          this.chatProp.refresh = false
          const { data , success }  = await request.post(`${this.api}TrendChart`,{type:this.chatProp.type,proCode:this.chatProp.proCode,startTime: this.chatProp.startTime, endTime: this.chatProp.endTime})
          this.chatProp.refresh = true
          if(success){
            this.chatProp.data = {}
            this.chatProp.data = data
            this.chatProp.data.title = ''
            this.chatProp.chatDialog = true
          }
          this.chatProp.chatLoading = false
        },
        openDetails(proCodes) {
            this.proCodes = proCodes
            this.batchOperateVisible = true
        },
        lossDown(proCode, platform) {
            this.$confirm('此操作将下架该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await request.post(`${this.api}ProductDown`, { proCode })
                if (success) {
                    this.$message({
                        type: 'success',
                        message: '下架成功!'
                    });
                    this.getList()
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消下架'
                });
            });
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getGroupList() {
            var { data } = await getDirectorGroupList();
            this.grouplist = data.map(item => { return { value: item.key, label: item.value }; });
        },
        proCodeCallback(val) {
            this.ListInfo.proCode = val
        },
        async getCol() {
            let that = this
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                data.forEach(item => {
                    if (item.prop == 'proCode') {
                        item.type = 'html'
                        item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
                    }
                    if (item.prop == 'orderCountLoss') {
                        item.type = 'click'
                        item.handle = (that, row) => that.openDetails(row.proCode)
                    }
                    if (item.prop == 'platform') {
                        item.formatter = (row) => {
                            return formatPlatform(row.platform)
                        }
                    }
                    if (item.prop == 'rateProfit') {
                        item.formatter = (row) => {
                            return row.rateProfit !== null ? row.rateProfit + '%' : ''
                        }
                    }
                    if (item.prop == 'rateLossCount') {
                        item.formatter = (row) => {
                            return row.rateLossCount !== null ? row.rateLossCount + '%' : ''
                        }
                    }
                })
                this.tableCols = data;
            }
        },
        async getList(type) {
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    align-items: center;

    .publicCss {
        width: 200px;
        margin: 0 10px 10px 0;
    }
}

.btnGroup {
    padding: 5px 0;
    display: flex;
    align-items: center;

    .btnGroup_Css {
        margin-right: 10px;
    }
}
</style>
