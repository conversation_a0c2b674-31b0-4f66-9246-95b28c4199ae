<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <!-- <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" :clearable="false"
                    start-placeholder="导入开始日期" end-placeholder="导入结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" @change="changeTime($event, 'export')">
                </el-date-picker> -->
                <el-date-picker v-model="payTimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="付款开始日期" end-placeholder="付款结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" @change="changeTime($event, 'payment')">
                </el-date-picker>
                <el-input v-model="ListInfo.orderNo" placeholder="线上订单号" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.orderNoInner" placeholder="内部订单号" maxlength="50" clearable class="publicCss" />
                <!-- <el-select v-model="ListInfo.isStock" placeholder="是否需要调拨" class="publicCss" clearable>
                    <el-option :key="'是'" label="是" :value="0" />
                    <el-option :key="'否'" label="否" :value="1" />
                </el-select> -->
                <el-select v-model="ListInfo.allotResult" placeholder="调拨状态" class="publicCss" clearable>
                    <el-option v-for="item in  allotResultStatus" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="chooseType = true">导入</el-button>
            </div>
        </template>
        <vxetablebase :id="'compute202408041746'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            style="width: 100%; height: 680px; margin: 0" v-loading="loading" />
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />

        <el-dialog title="订单商品信息" :visible.sync="dialogVisible" width="50%" v-dialogDrag>
            <orderGoodInfo :orderNo="dialogQueryInfo.orderNo" :orderNoInner="orderNoInner" v-if="dialogVisible" />
        </el-dialog>

        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
            v-dialogDrag>
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNoInner="orderNoInner"
                style="z-index:10000;height:600px" />
        </el-dialog>

        <el-dialog title="请选择导入数据模版" :visible.sync="chooseType" width="20%" height="600px" v-dialogDrag>
            <div style="display: flex;justify-content: center;margin-top: 10px;">
                <el-button type="primary" @click="openExport('kc')">库存数据</el-button>
                <el-button type="primary" @click="openExport('dd')">订单数据</el-button>
            </div>
        </el-dialog>

        <el-dialog :title="exportTitle" :visible.sync="exportVisible" width="20%" height="600px" v-dialogDrag>
            <el-upload class="upload-demo" :http-request="uploadFile2" :limit="1" :file-list="fileList"
                :on-remove="removeFile" accept=".xlsx">
                <el-button class="addsc" type="text">点击上传</el-button>
            </el-upload>
            <div style="color: red;text-align: center;">温馨提示:只能上传一个xlsx文件</div>
            <div style="display: flex;justify-content: center;margin-top: 10px;">
                <el-button type="primary" @click="submitExport">确定</el-button>
                <el-button type="primary" @click="colse">取消</el-button>
            </div>
        </el-dialog>

        <el-dialog title="日志" :visible.sync="logListVisible" width="40%" v-dialogDrag>
            <logList :orderNo="orderNo" :orderNoInner="orderNoInner" v-if="logListVisible" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { replaceSpace } from '@/utils/getCols'
import dayjs from 'dayjs'
import { pickerOptions, formatPlatform } from '@/utils/tools'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pageGetVoOrder, importInventoryAsync, importOrderAsync, orderGoodsCodeAllot } from '@/api/vo/VerifyOrder'
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
import orderGoodInfo from './orderGoodInfo.vue'
import logList from './logList.vue'
const allotResultStatus = [
    { label: '调拨失败', value: -1 },
    { label: '等待调拨', value: 0 },
    { label: '调拨中', value: 1 },
    { label: '调拨成功', value: 2 },
]
const tableCols = [
    { istrue: true, prop: 'orderNo',align: 'center', label: '线上订单号', sortable: 'custom', width: '120', type: 'click', handle: (that, row) => that.showLogDetail(row) },
    { istrue: true, prop: 'orderNoInner', label: '内部订单号', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'importTime', label: '导入时间', sortable: 'custom', width: 'auto', formatter: (row) => dayjs(row.importTime).format('YYYY-MM-DD HH:mm') },
    { istrue: true, prop: 'planSendTime', label: '计划发货', sortable: 'custom', width: 'auto', formatter: (row) => row.planSendTime ? dayjs(row.planSendTime).format('YYYY-MM-DD HH:mm') : null },
    { istrue: true, prop: 'timePay', label: '付款日期', sortable: 'custom', width: 'auto', formatter: (row) => dayjs(row.timePay).format('YYYY-MM-DD HH:mm') },
    { istrue: true, prop: 'weight', label: '重量', align: 'center',sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'orderStatus', label: '订单状态', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'seriesName', label: '系列编码',align: 'left', sortable: 'custom', width: 'auto' },
    // { istrue: true, prop: 'allotResult', label: '调拨状态', sortable: 'custom', width: 'auto', formatter: (row) => allotResultStatus.find(item => item.value == row.allotResult).label },
    { istrue: true, prop: 'goodsCount', align: 'center', label: '编码数量', sortable: 'custom', width: 'auto', type: 'click', handle: (that, row) => that.openDialog(row) },
    { istrue: true, prop: 'totalQuantity', align: 'center', label: '数量', sortable: 'custom', width: 'auto', type: 'click', handle: (that, row) => that.openDialog(row) },
    {
        istrue: true, label: '', width: '70', type: 'button', btnList:
            [
                { istrue: true, label: '日志', handle: (that, row) => that.openLog(row) },
            ]
    },
]

const dialogTableCols = [
    { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'quantity', label: '数量', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'orderStatus', label: '订单状态', sortable: 'custom', width: 'auto' },
    {
        istrue: true, prop: 'orderStatus', label: '操作', sortable: 'custom', width: 'auto', type: 'button', btnList: [
            { label: '调拨', display: (row) => row.orderStatus != '调拨失败', handle: (that, row) => that.allocateOperate(row) }
        ]
    },
]
export default {
    name: "computeVue",
    components: {
        MyContainer, vxetablebase, OrderActionsByInnerNos, orderGoodInfo, logList
    },
    data() {
        return {
            value: '',
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                isMain: true,
                finalStatus: 0,//计算后状态
                orderNo: null,//线上订单号
                startDate: null,//导入时间
                endDate: null,//导入时间
                payStartDate: null,//付款时间
                payEndDate: null,//付款时间
                isStock: null,//是否需要调拨
                allotResult: null,//调拨状态
                orderNoInner: null,//内部订单号
            },
            dialogQueryInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                orderNo: null
            },
            timeRanges: [],
            payTimeRanges: [],
            dialogTableData: [],
            tableCols,
            tableData: [],
            pickerOptions,
            total: 0,
            dialogTotal: 0,
            dialogVisible: false,
            dialogTableCols,
            extData: {},
            loading: true,
            allotResultStatus,
            orderNo: null,
            dialogHisVisible: false,
            exportTitle: null,
            chooseType: false,
            exportVisible: false,
            exportQueryInfo: {
                file: null,//文件
            },
            fileList: [],
            logListVisible: false,
            orderNoInner: null,
            isTx: false
        };
    },
    mounted() {
        this.getList()
    },
    methods: {
        openLog(row) {
            this.orderNo = row.orderNo;
            this.orderNoInner = row.orderNoInner;
            this.logListVisible = true;
        },
        allocateOperate(row) {
            this.$confirm('此操作将改变该商品的调拨状态, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await orderGoodsCodeAllot({ orderNo: row.orderNo, goodsCode: row.goodsCode })
                if (success) {
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消'
                });
            });
        },
        removeFile(file, fileList) {
            this.exportQueryInfo.file = null
        },
        colse() {
            this.exportVisible = false
            this.exportQueryInfo.file = null
        },
        async submitExport() {
            if (!this.exportQueryInfo.file) {
                this.$message({ message: '请上传文件', type: "error" });
                return
            }
            if (this.exportTitle == '导入库存数据') {
                const { success } = await importInventoryAsync(this.exportQueryInfo.file)
                if (success) {
                    this.$message({ message: '库存数据上传成功,正在导入中...', type: "success" });
                    this.exportVisible = false
                } else {
                    this.$message({ message: '上传失败', type: "error" });
                }
            } else {
                const { success } = await importOrderAsync(this.exportQueryInfo.file)
                if (success) {
                    this.$message({ message: '订单数据上传成功,正在导入中...', type: "success" });
                    this.exportVisible = false
                } else {
                    this.$message({ message: '上传失败', type: "error" });
                }
            }
        },
        async uploadFile2(item) {
            const form = new FormData();
            form.append("file", item.file);
            this.exportQueryInfo.file = form
        },
        openExport(type) {
            this.fileList = []
            if (type == 'kc') {
                this.exportTitle = '导入库存数据'
            } else {
                this.exportTitle = '导入订单数据'
            }
            this.chooseType = false
            this.exportVisible = true
        },
        showLogDetail(row) {
            this.orderNoInner = row.orderNoInner
            this.dialogHisVisible = true;
        },
        async openDialog(row) {
            this.orderNoInner = row.orderNoInner;
            this.dialogQueryInfo.orderNo = row.orderNo
            this.dialogVisible = true
        },
        changeTime(e, type) {
            if (e) {
                if (type == 'export') {
                    this.ListInfo.startDate = dayjs(e[0]).format('YYYY-MM-DD')
                    this.ListInfo.endDate = dayjs(e[1]).format('YYYY-MM-DD')
                } else if (type == 'payment') {
                    this.ListInfo.payStartDate = dayjs(e[0]).format('YYYY-MM-DD')
                    this.ListInfo.payEndDate = dayjs(e[1]).format('YYYY-MM-DD')
                }
            } else {
                if (type == 'export') {
                    this.ListInfo.startDate = null
                    this.ListInfo.endDate = null
                } else if (type == 'payment') {
                    this.ListInfo.payStartDate = null
                    this.ListInfo.payEndDate = null
                }
            }
            this.getList()
        },
        //查询列表
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
            }
            // if (this.timeRanges.length == 0) {
            //     //默认给近7天时间
            //     this.timeRanges = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
            //     this.ListInfo.startDate = this.timeRanges[0]
            //     this.ListInfo.endDate = this.timeRanges[1]
            // }
            if (this.payTimeRanges.length == 0) {
                //默认给近7天时间
                this.payTimeRanges = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
                this.ListInfo.payStartDate = this.payTimeRanges[0]
                this.ListInfo.payEndDate = this.payTimeRanges[1]
            }
            const replaceArr = ['orderNo', 'orderNoInner']
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            const { data, success } = await pageGetVoOrder(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.top {
    display: flex;
    margin-bottom: 20px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.dialogBox {
    display: flex;
    flex-direction: column;

    .dialogBox_top {
        display: flex;
        flex-direction: column;
        margin-bottom: 10px;

        .word {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        div {
            display: flex;

            div {
                width: 50%;
                height: 40px;
                line-height: 40px;

                &:nth-child(1) {
                    background-color: rgb(239, 239, 239);
                    width: 20%;
                }
            }
        }
    }
}
</style>