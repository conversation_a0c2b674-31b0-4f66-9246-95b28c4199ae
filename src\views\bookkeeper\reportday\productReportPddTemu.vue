<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <div>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;" v-show="selectedTitles.includes('SKC')">
            <inputYunhan ref="proCode" v-model.trim="filter.proCode" :inputt.sync="filter.proCode" placeholder="SKC"
              :maxRows="3000" :maxlength="90000" width="110px" :clearable="true" @callback="callbackProCode"
              title="SKC">
            </inputYunhan>
          </el-button>
          <el-button style="padding: 0;margin: 0;" v-show="selectedTitles.includes('商品名称')">
            <el-input v-model.trim="filter.productName" :maxlength="150" clearable placeholder="商品名称"
              style="width:100px;" />
          </el-button>


          <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('商品编码')">
            <inputYunhan :key="'1'" :keys="'one'" :width="'220px'" ref="" :inputt.sync="filter.goodsCodes"
              v-model.trim="filter.goodsCodes" placeholder="商品编码/若输入多条请按回车" :clearable="true"
              @callback="callbackGoodsCode" title="商品编码" @entersearch="entersearch" :maxRows="100">
            </inputYunhan>
          </el-button>
          <!-- <el-button style="padding: 0;margin: 0;">
            <el-select v-model.trim="styleCode" multiple collapse-tags filterable remote reserve-keyword
              placeholder="系列编码" @paste.native="handlePaste" clearable :remote-method="remoteMethod"
              style="width: 170px" :loading="searchloading">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-button> -->
          <el-button style="padding: 0;margin: 0;">
            <el-date-picker style="width: 210px" v-model.trim="filter.timerange" type="daterange" :clearable="false"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期"></el-date-picker>
          </el-button>
          <el-button style="padding: 0;border: none;margin: 0;" v-show="selectedTitles.includes('上架天数最小值')">
            <el-input-number v-model="filter.minOnTimeNum" :min="-9999" :max="9999" :precision="0" :controls="false"
              placeholder="＞上架天数最小值"></el-input-number>
          </el-button>
          <el-button style="padding: 0;border: none;margin: 0;" v-show="selectedTitles.includes('上架天数最大值')">
            <el-input-number v-model="filter.maxOnTimeNum" :min="-9999" :max="9999" :precision="0" :controls="false"
              placeholder="≤上架天数最大值"></el-input-number>
          </el-button>
          <el-button style="padding: 0;margin: 0;" v-show="selectedTitles.includes('店铺')">
            <el-select filterable clearable v-model="filter.shopCodeList" placeholder="店铺" style="width: 170px" multiple
              collapse-tags>
              <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
              </el-option>
            </el-select>
          </el-button>
          <!-- <el-button style="padding: 0;width: 90px;">
            <el-select filterable v-model.trim="filter.brandId" clearable placeholder="采购" @paste.native="handlePaste"
              style=" width: 90px">
              <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-button> -->
          <el-button style="padding: 0;width: 170px;" v-show="selectedTitles.includes('运营组')">
            <el-select filterable v-model.trim="filter.groupIds" collapse-tags clearable placeholder="运营组" multiple
              @paste.native="handlePaste" style="width: 170px">
              <el-option key="无运营组" label="无运营组" :value="0"></el-option>
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;" v-show="selectedTitles.includes('运营专员')">
            <el-select filterable v-model.trim="filter.operateSpecialUserIds" collapse-tags clearable placeholder="运营专员"
              multiple @paste.native="handlePaste" style="width: 150px">
              <el-option key="无运营专员" label="无运营专员" :value="0"></el-option>
              <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;" v-show="selectedTitles.includes('运营助理')">
            <el-select filterable v-model.trim="filter.userIds" collapse-tags clearable placeholder="运营助理" multiple
              @paste.native="handlePaste" style="width: 150px">
              <el-option key="无运营助理" label="无运营助理" :value="0"></el-option>
              <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;" v-show="selectedTitles.includes('毛利1')">
            <el-select filterable v-model.trim="filter.profit1UnZero" collapse-tags clearable placeholder="毛利1"
              @paste.native="handlePaste" style="width: 90px">
              <el-option label="正利润" :value="false" />
              <el-option label="负利润" :value="true" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('白名单')">
            <el-select v-model="filter.isIgnoreRptProCode" style="width: 90px ;height: 29px" placeholder="白名单"
              filterable collapse-tags>
              <el-option label="启用白名单" :value="1" />
              <el-option label="禁用白名单" :value="0" />
              <el-option label="只看白名单" :value="2" />
            </el-select>
          </el-button>
          <!-- <el-button style="padding: 0;border: none;float: left;">
            <el-select v-model="filter.isIgnoreSpecialProCode" placeholder="请选择" style="width: 90px;" filterable>
              <el-option v-for="item in hearlist" :key="item.label" :label="item.label" :value="item.value" />
            </el-select>
          </el-button> -->
          <!-- <el-button style="padding: 0;border: none;float: left;">
            <el-select filterable clearable v-model="filter.projName" placeholder="请选择项目" style="width: 170px" multiple collapse-tags>
              <el-option v-for="item in projectList" :key="item.projName" :label="item.projName"
                :value="item.projName"></el-option>
            </el-select>
          </el-button> -->
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="BatchCalculation"
            v-if="checkPermission('kj_repordDay_Batchsum')">批量计算</el-button>
          <el-button icon="vxe-icon-custom-column" @click="clickToolbar" />
        </el-button-group>
        <!-- <div class="el-backtop" style="right: 5px;" @click="showupload"><i class="el-icon-caret-right"></i></div> -->
      </div>
    </template>
    <!-- <vxetablebase :id="'productReportPddTemu202302031421'" :border="true" :align="'center'" :cstmExportFunc="onExport"
      :tablekey="'productReportPddTemu202302031421'" ref="table" :that='that' :isIndex='true' :hasexpand='false'
      @sortchange='sortchange' :isSelectColumn="true" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry'
      :tableData='financialreportlist' @summaryClick='onsummaryClick' :tableCols='tableCols' @cellClick="cellClick"
      :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:95%;margin: 0" :xgt="9999"
      :showheaderoverflow="false">
      <template slot='extentbtn'>
        <el-button type="primary" size="small" :style="{ color: verifyDailyPaper ? 'red' : '' }"
          @click="onConfirmationMethod">日报确认</el-button>
        <el-button-group>
          <el-radio-group v-model="filter.refundType" size="small">
            <el-radio-button :label="1">发生维度</el-radio-button>
          </el-radio-group>
        </el-button-group>
      </template>
    </vxetablebase> -->
    <vxetablebase :id="'productReportPddTemu202302031421'" :border="true" :align="'center'" :cstmExportFunc="onExport"
      :tablekey="'productReportPddTemu202302031421'" ref="table" :that='that' :isIndex='true' :hasexpand='false'
      @sortchange='sortchange' :isSelectColumn="true" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry'
      :tableData='financialreportlist' @summaryClick='onsummaryClick' :tableCols='tableCols'
      :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:95%;margin: 0" :xgt="9999"
      :showheaderoverflow="false">
      <template slot='extentbtn'>
        <el-button type="primary" size="small" :style="{ color: verifyDailyPaper ? 'red' : '' }"
          @click="onConfirmationMethod">日报确认</el-button>
        <el-button-group>
          <el-radio-group v-model="filter.refundType" size="small">
            <el-radio-button :label="1">发生维度</el-radio-button>
          </el-radio-group>
        </el-button-group>
      </template>
    </vxetablebase>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>

    <el-dialog title="导入胜算" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
      <span>
        <el-row>
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker style="width: 100%" v-model="onimportfilter.yearmonthday" type="date" format="yyyyMMdd"
              value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
            <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
              accept=".xlsx" :http-request="uploadFile" :file-list="fileList" :data="fileparm">
              <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
            </el-upload>
          </el-col>
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-drawer title="参数设置" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
      :visible.sync="editparmVisible" direction="btt" size="'auto'" class="el-drawer__wrapper"
      style="position:absolute;">
      <form-create :rule="autoformparm.rule" v-model="autoformparm.fApi" :option="autoformparm.options" />
      <div class="drawer-footer">
        <el-button @click.native="editparmVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="editparmLoading" @click="onSetEditParm" />
      </div>
    </el-drawer>
    <el-dialog title="商品数据趋势图&&生意参谋平台" :visible.sync="dialogDrVisible" width="80%" v-dialogDrag>
      <div>
        <span>
          <productdrchart v-if="dialogDrVisible"></productdrchart>
        </span>
      </div>
      <div>
        <span>
          <BusinessStaffPlatForm v-if="dialogDrVisible"></BusinessStaffPlatForm>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogDrVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    <!-- <el-dialog title="实际快递费明细" :visible.sync="freightDetail.visible" width="80%" v-dialogDrag>
      <freightDetail ref="freightDetail" :filter="freightDetail.filter" style="height:600px;"></freightDetail>
    </el-dialog> -->
    <el-dialog title="订单明细" :visible.sync="freightDetail.visible" width="80%" v-dialogDrag>
      <div>
        <div>
          <el-alert title="温馨提示:由于统计、明细数据来源不一样，有少许可接受的差异属正常情况！" type="warning" :closable="false"></el-alert>
        </div>
        <freightDetail ref="freightDetail" :filter="freightDetail.filter" style="height:600px;"></freightDetail>
      </div>
    </el-dialog>
    <el-dialog title="每日退款明细" :visible.sync="EveryDayrefund.visible" width="80%" v-dialogDrag>
      <div>
        <EveryDayrefund ref="EveryDayrefund" :filter="EveryDayrefund.filter" style="height:600px;"></EveryDayrefund>
      </div>
    </el-dialog>

    <el-dialog title="盘亏计算明细" :visible.sync="InventoryCheckFee.visible" width="80%" v-dialogDrag>
      <div>
        <InventoryCheckFee ref="InventoryCheckFee" :filter="InventoryCheckFee.filter" style="height:600px;">
        </InventoryCheckFee>
      </div>
    </el-dialog>

    <el-dialog title="赠品成本明细" :visible.sync="giftDetail.visible" width="80%" v-dialogDrag>
      <ordergiftdetail ref="ordergiftdetail" style="height:600px;"></ordergiftdetail>
    </el-dialog>

    <el-dialog :visible.sync="costDialog.visible" v-dialogDrag :show-close="false" width="300px">
      <el-table :data="costDialog.rows" border style="width: 100%">
        <el-table-column prop="saleCostSrc" label="自发成本" />
        <el-table-column prop="replaceSendCost" label="代发成本" />
      </el-table>
    </el-dialog>

    <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
      <span>
        <buschar ref="buschar" v-if="buscharDialog.visible" :analysisData="buscharDialog.data"
          :loading="buscharDialog.loading"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-drawer title="每日文件上传跟踪" :visible.sync="drawervisible" direction="rtl">
      <importmodule ref="importmodule" v-if="drawervisible" :id="1"></importmodule>
    </el-drawer>

    <el-dialog title="确认数据" :visible.sync="dialogConfirmdata" width="55%" v-dialogDrag>
      <div style="height: 550px;">
        <dailyConfirmation v-if="dialogConfirmdata" :dailyPaperList="dailyPaperList" />
      </div>
    </el-dialog>

    <el-dialog title="计算日报" :visible.sync="dialogCalDayRepotVis" width="40%" v-dialogDrag>
      <span>
        <el-row>
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker style="width: 50%; float: left;" v-model="calDayRepotyDate" type="date" format="yyyyMMdd"
              value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
            <el-button type="success" style="margin-left:  30px;" @click="calDayRepoty">计算日报</el-button>
          </el-col>
        </el-row>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="dailyNewspaperToolbar" width="40%" v-dialogDrag>
      <template #title>
        <div class="dialog-header">
          查询条件设置
          <el-button style="margin-left: 10px;" @click="selectAll">全选/取消全选</el-button>
        </div>
      </template>
      <el-scrollbar>
        <div style="height: 150px;">
          <el-checkbox-group v-model="colOptions" @change="changeOptions">
            <el-checkbox v-for="(item, index) in colSelect"
              v-if="(filter.refundType == 3) || (filter.refundType == 4 && !['毛三（减退款率)', '毛四（减退款率)'].includes(item)) ||
                (filter.refundType != 4 && item !== '日期' && !['毛三（减退款）是否负利润', '毛四（减退款）是否负利润', '毛三（减退款率)', '毛四（减退款率)'].includes(item))" :label="item"
              :key="item"></el-checkbox>
          </el-checkbox-group>
        </div>
      </el-scrollbar>
      <div style="margin-top: 40px;display: flex;justify-content: end;">
        <el-button @click="dailyNewspaperToolbar = false">取消</el-button>
        <el-button type="primary" @click="verifyOptions" v-throttle="3000">确认</el-button>
      </div>
    </el-dialog>

    <!-- 批量计算 -->
    <el-dialog title="批量计算" center :visible.sync="batchvisible" v-dialogDrag>
      <span>
        <el-row>
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker style="width: 210px" v-model.trim="BatchDate" type="daterange" :clearable="false"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期"></el-date-picker>

            <el-button type="success" style="margin-left:  30px;" @click="BatchRepoty">确认</el-button>

          </el-col>
        </el-row>
      </span>
    </el-dialog>

    <!-- 提醒 -->
    <el-dialog title="异常数据" :visible.sync="detailVisible" :center="false" width="40%" v-dialogDrag>
      <vxetablebase :id="'factoryProxySend20241221'" :tablekey="'factoryProxySend20241221'"
        @sortchange="detailSortChange" :tableData='detailList' :tableCols='detailTableCols' :loading='detailLoading'
        :border='true' :that="that" height="440px" ref="detailVxetable" :showsummary='false' :toolbarshow="false">
      </vxetablebase>
    </el-dialog>
  </my-container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { getDirectorGroupList, getDirectorList, getList as getshopList } from '@/api/operatemanage/base/shop'
import { getParm, setParm } from '@/api/bookkeeper/reportday'
import { pageProductDayReport, queryDayReportAnalysisAsync, exportProductDayReport, getPageDailyReportItemConfirmList, insertDailyReportConfirmList, calDayRepoty_KJAsync, batchCalDayRepoty_KJAsync } from '@/api/bookkeeper/crossBorderV2'
import { importProductDayReport, ImportBusinessStaffPlatForm } from '@/api/bookkeeper/import'
import productdrchart from '@/views/bookkeeper/reportday/productdrchart'
import BusinessStaffPlatForm from '@/views/bookkeeper/reportday/BusinessStaffPlatForm'
import { formatPlatform, formatTime, formatYesornoBool, formatLinkProCode, platformlist } from "@/utils/tools";
import { getAllProBrand } from '@/api/inventory/warehouse'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import InputMult from "@/components/Comm/InputMult";
import { Loading } from 'element-ui';
import { ruleDirectorGroup } from '@/utils/formruletools'
import freightDetail from '@/views/bookkeeper/reportday/freightDetail'
import buschar from '@/components/Bus/buschar'
import importmodule from '@/components/Bus/importmodule'
import expressfreightanalysis from '@/views/express/expressfreightanalysis'
import ordergiftdetail from '@/views/order/gift/ordergiftDetail'
import { getListByStyleCode } from "@/api/inventory/basicgoods"
import EveryDayrefund from '@/views/bookkeeper/reportday/EveryDayrefund'
import InventoryCheckFee from '@/views/bookkeeper/reportday/InventoryCheckFee'
import inputYunhan from "@/components/Comm/inputYunhan";
import { getTimeDiff } from '@/utils/getCols'
import numberRange from '@/components/number-range/index.vue'
import dailyConfirmation from '@/views/bookkeeper/KJReportday/dailyConfirmation'
import { getProductProjectList } from "@/api/operatemanage/productmanager"
// import { getPageDailyReportItemConfirmList } from '@/api/bookkeeper/dayReport'
import { SetVxeTableColumnCacheAsync, GetVxeTableColumnCacheAsync } from '@/api/admin/business'
import dayjs from 'dayjs'
import {
  getProductStyleTagList
} from '@/api/operatemanage/base/product'
const hearlist = [{ label: '查看全部', value: null }, { label: '查看普通ID', value: 1 }, { label: '查看特殊ID/店铺', value: 2 }];
let loading;
const startLoading = () => {
  loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
  });
};
const tableCols = [
  { istrue: true, fixed: 'left', prop: 'yearMonthDay', label: '年月日', sortable: 'custom', width: '60', type: 'custom' },
  { istrue: true, fixed: 'left', prop: 'platform', fix: true, exportField: 'platformstr', label: '平台', width: '45', sortable: 'custom', formatter: (row) => formatPlatform(row.platform), type: 'custom' },
  // { istrue: true, fixed: 'left', prop: 'projName', label: '项目', width: '60', sortable: 'custom', },
  { istrue: true, fixed: 'left', prop: 'styleCode', label: '系列编码', sortable: 'custom', width: '70', formatter: (row) => row.styleCode || ' ', }, // type: 'click',handle: (that, row) => that.showProcodesimilarity(row)
  { istrue: true, fixed: 'left', prop: 'shopCode', label: '店铺ID', sortable: 'custom', width: '70', type: 'custom' },
  { istrue: true, fixed: 'left', prop: 'shopName', exportField: 'shopName', label: '店铺名称', sortable: 'custom', width: '70', formatter: (row) => row.shopName, type: 'custom' },
  // { istrue: true, fixed: 'left', prop: 'brandId', exportField: 'brandName', label: '采购', sortable: 'custom', width: '45', formatter: (row) => row.brandName || ' ', type: 'custom' ,tipmesg: '暂无数据'},
  { istrue: true, fixed: 'left', label: '小组头像', width: '70', type: 'ddAvatar', ddInfo: { type: 1, prop: 'groupId' } },
  { istrue: true, fixed: 'left', prop: 'groupId', exportField: 'groupName', label: '小组', sortable: 'custom', width: '70', formatter: (row) => row.groupName, type: 'ddTalk', ddInfo: { type: 1, prop: 'groupId', name: 'groupName' }, },
  { istrue: true, fixed: 'left', label: '专员头像', width: '70', type: 'ddAvatar', ddInfo: { type: 2, prop: 'operateSpecialUserId' } },
  { istrue: true, fixed: 'left', prop: 'operateSpecialUserId', exportField: 'operateSpecialUserName', label: '运营专员', sortable: 'custom', width: '70', formatter: (row) => row.operateSpecialUserName, type: 'ddTalk', ddInfo: { type: 2, prop: 'operateSpecialUserId', name: 'operateSpecialUserName' }, },
  { istrue: true, fixed: 'left', prop: 'userId', exportField: 'userName', label: '运营助理', sortable: 'custom', width: '45', permission: "cgcoltxpddprsi", formatter: (row) => row.userName, type: 'custom' },
  // { istrue: true, fixed: 'left', prop: 'userId2', exportField: 'userName2', label: '车手', sortable: 'custom', width: '45', permission: "cgcoltxpddprsi", formatter: (row) => row.userName2, type: 'custom' ,tipmesg: '暂无数据'},
  // { istrue: true, fixed: 'left', prop: 'userId3', exportField: 'userName3', label: '备用负责人', sortable: 'custom', width: '45', permission: "cgcoltxpddprsi", formatter: (row) => row.userName3, type: 'custom' ,tipmesg: '暂无数据'},
  { istrue: true, type: 'echarts', prop: 'profit3IncreaseGoOnDays', chartProp: 'profit3IncreaseGoOnDaysChartData', fix: true, label: '毛三趋势', width: '80', permission: "lirunprsi,profit3rsi" },
  { istrue: true, fixed: 'left', prop: 'syncTime', label: '计算时间', sortable: 'custom', width: '80', formatter: (row) => formatTime(row.syncTime, 'YYYY-MM-DD') },
  { istrue: true, fixed: 'left', prop: 'proCode', fix: true, label: 'SKC', width: '75', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
  // { istrue: true, fixed: 'left', prop: 'styleTag', label: '款式标签', sortable: 'custom', width: '60', type: 'custom',tipmesg: '暂无数据' },
  { istrue: true, fixed: 'left', prop: 'goodsCode', label: '商品编码', sortable: 'custom', width: '80' },
  { istrue: true, fixed: 'left', prop: 'goodsName', label: '商品名称', sortable: 'custom', width: '80' },
  // { istrue: true, fixed: 'left', prop: 'productCategoryId', exportField: 'productCategoryName', label: '类目', width: '100',tipmesg: '暂无数据', sortable: 'custom', permission: "cgcoltxpddprsi", formatter: (row) => (row.productCategoryId?.length > 3 ? row.productCategoryName : ""), type: 'custom' },
  { istrue: true, fixed: 'left', prop: 'onTime', label: '上架时间', sortable: 'custom', width: '75', permission: "cgcoltxpddprsi", formatter: (row) => formatTime(row.onTime, 'YYYY-MM-DD') },
  { istrue: true, fixed: 'left', prop: 'onTimeCount', exportField: 'onTimeCount', label: '上架天数', sortable: 'custom', width: '75', permission: "cgcoltxpddprsi", formatter: (row, that) => getTimeDiff([row.onTime, that.filter.endTime]) },
  { istrue: true, summaryEvent: true, prop: 'orderCount', label: '订单量', sortable: 'custom', width: '65', tipmesg: '（付款订单量）此字段仅供参考；如果一个订单中有3种商品，这3行商品的销售订单数均为1，所以不能把此字段的合计值作为订单数量。因为订单就只有1个。但是销售订单数合计值为3。', formatter: (row) => !row.orderCount ? " " : row.orderCount },
  // { istrue: true, summaryEvent: true, prop: 'wcOrderCount', label: '预包订单量', sortable: 'custom', width: '65', tipmesg: '', formatter: (row) => !row.wcOrderCount ? " " : row.wcOrderCount },
  // { istrue: true, summaryEvent: true, prop: 'wcOrderCountRate', label: '预包率', sortable: 'custom', width: '65', tipmesg: '预包订单量/订单量', formatter: (row) => !row.wcOrderCountRate ? " " : row.wcOrderCountRate },
  { istrue: true, summaryEvent: true, prop: 'payAmont', label: '付款金额', sortable: 'custom', width: '60', formatter: (row) => !row.payAmont ? " " : row.payAmont, type: 'click' },
  // { istrue: true, summaryEvent: true, prop: 'payAmont', label: '付款金额', sortable: 'custom', width: '70', formatter: (row) => !row.payAmont ? " " : row.payAmont, type: 'click', handle: (that, row) => that.showOrderDetail(row) },
  { istrue: true, summaryEvent: true, prop: 'coupon', label: '优惠券', sortable: 'custom', width: '65' },
  { istrue: true, summaryEvent: true, prop: 'saleAmont', label: '销售金额', sortable: 'custom', width: '70', tipmesg: '计算公式：付款金额-优惠券', style: (that, row) => that.renderAmont(row), handle: (that, row) => that.showAmont(row), formatter: (row) => !row.saleAmont ? " " : row.saleAmont },
  { istrue: true, summaryEvent: true, prop: 'saleCost', label: '销售成本', sortable: 'custom', width: '75', formatter: (row) => !row.saleCost ? " " : row.saleCost },
  { istrue: true, summaryEvent: true, prop: 'dropshippingCost', label: '厂家代发成本', sortable: 'custom', width: '75', formatter: (row) => !row.dropshippingCost ? " " : row.dropshippingCost },

  { istrue: true, summaryEvent: true, prop: 'purchaseFreight', label: '采购运费', sortable: 'custom', width: '80', formatter: (row) => row.purchaseFreight == 0 ? " " : row.purchaseFreight, },
  { istrue: true, summaryEvent: true, prop: 'productFreight', label: '产品运费', sortable: 'custom', width: '70', formatter: (row) => row.productFreight == 0 ? " " : row.productFreight, tipmesg: '物流点提货回仓库的运费，入库单的产品运费分摊到该入库单的商品编码对应的ID上' },
  { istrue: true, summaryEvent: true, prop: 'profit1', label: '毛一', sortable: 'custom', width: '70', type: 'custom', tipmesg: '计算公式：销售金额-销售成本-采购运费-产品运费-厂家代发成本', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  { istrue: true, summaryEvent: true, prop: 'profit1Rate', label: '毛一利率', sortable: 'custom', width: '70', type: 'custom', tipmesg: '计算公式：毛一/销售金额', formatter: (row) => !row.profit1Rate ? " " : row.profit1Rate + '%' },
  {
    istrue: true, summaryEvent: true, rop: '', label: `账单费用`, merge: true, prop: 'mergeField', permission: "cgcoltxpddprsi", width: '90',
    cols: [
      { istrue: true, summaryEvent: true, prop: 'dK8', label: '售后预留金额', sortable: 'custom', width: '80', formatter: (row) => row.dK8 == 0 ? " " : row.dK8 },
      { istrue: true, summaryEvent: true, prop: 'dK9', label: '售后释放金额', sortable: 'custom', width: '80', formatter: (row) => row.dK9 == 0 ? " " : row.dK9 },
      { istrue: true, summaryEvent: true, prop: 'dK5', label: '商品补寄赔偿金', sortable: 'custom', width: '80', formatter: (row) => row.dK5 == 0 ? " " : row.dK5 },
      { istrue: true, summaryEvent: true, prop: 'dK4', label: '仓储综合服务费', sortable: 'custom', width: '80', formatter: (row) => row.dK4 == 0 ? " " : row.dK4 },
      { istrue: true, summaryEvent: true, prop: 'dK2', label: '商品环保费', sortable: 'custom', width: '80', formatter: (row) => row.dK2 == 0 ? " " : row.dK2, },
      { istrue: true, summaryEvent: true, prop: 'dK10', label: '物流包装环保费', sortable: 'custom', width: '80', formatter: (row) => row.dK10 == 0 ? " " : row.dK10, },
      { istrue: true, summaryEvent: true, prop: 'dK3', label: '售后赔付', sortable: 'custom', width: '75', formatter: (row) => row.dK3 == 0 ? " " : row.dK3 },
      { istrue: true, summaryEvent: true, prop: 'dkOther', label: '其他', sortable: 'custom', width: '70', formatter: (row) => row.dkOther == 0 ? " " : row.dkOther, tipmesg: '暂无数据，未参与计算到日报' },
      { istrue: true, summaryEvent: true, prop: 'dkAmont', label: '费用合计', sortable: 'custom', width: '80', formatter: (row) => row.dkAmont == 0 ? " " : row.dkAmont, tipmesg: '计算公式：售后预留金额-售后释放金额+商品补寄赔偿金+仓储综合服务费+商品环保费+包装物流环保费+售后赔付+其他' },
    ]
  },

  { istrue: true, summaryEvent: true, prop: 'freightFeeTotal', label: '发货运费(快递费)', sortable: 'custom', width: '70', type: 'custom', formatter: (row) => !row.freightFeeTotal ? " " : row.freightFeeTotal },
  { istrue: true, summaryEvent: true, prop: 'packageFee', label: '包装费用(辅料)', sortable: 'custom', width: '70', formatter: (row) => !row.packageFee ? " " : row.packageFee, tipmesg: '暂无数据，未参与计算到日报' },
  { istrue: true, summaryEvent: true, prop: 'profit2', label: '毛二', sortable: 'custom', width: '55', type: 'custom', tipmesg: '计算公式：毛一-账单费用-包装费用(辅料)-发货运费(快递费)', formatter: (row) => !row.profit2 ? " " : row.profit2 },
  { istrue: true, summaryEvent: true, prop: 'profit2Rate', label: '毛二利率', sortable: 'custom', width: '70', type: 'custom', tipmesg: '计算公式：毛二/销售金额', formatter: (row) => !row.profit2Rate ? " " : row.profit2Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'wages', label: '运营工资', sortable: 'custom', width: '70', formatter: (row) => row.wages == 0 ? " " : row.wages, tipmesg: '计算公式：该运营单条销售金额/该运营TEMU全托平台当日销售总额*TEMU全托平台该运营每日承担工资*0.5+该运营单条销量/该运营TEMU全托平台当日总销量*TEMU全托平台该运营每日承担工资*0.5' },
  { istrue: true, summaryEvent: true, prop: 'sampleFeeBX', label: '样品费用', sortable: 'custom', width: '70', formatter: (row) => row.sampleFeeBX == 0 ? " " : row.sampleFeeBX, tipmesg: '暂无数据，未参与计算到日报' },
  { istrue: true, summaryEvent: true, prop: 'profit3', label: '毛三', sortable: 'custom', width: '55', type: 'custom', tipmesg: '计算公式：毛二-运营工资-样品费用', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit3 ? " " : row.profit3 },
  { istrue: true, summaryEvent: true, prop: 'profit3Rate', label: '毛三利率', type: 'custom', tipmesg: '计算公式：毛三/销售金额', sortable: 'custom', width: '50', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit3Rate ? " " : row.profit3Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'exitCost', label: '出仓成本', sortable: 'custom', width: '70', formatter: (row) => !row.exitCost ? " " : row.exitCost, tipmesg: '计算公式：出仓成本均价*订单量' },
  { istrue: true, summaryEvent: true, prop: 'profit33', label: '毛四', sortable: 'custom', width: '75', type: 'custom', tipmesg: '计算公式：毛三-出仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit33 ? " " : row.profit33 },
  { istrue: true, summaryEvent: true, prop: 'profit33Rate', label: '毛四利率', sortable: 'custom', width: '80', type: 'custom', tipmesg: '计算公式：毛四/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'dK1', label: '平台弃货', sortable: 'custom', width: '60', formatter: (row) => row.dK1 == 0 ? " " : row.dK1 },
  { istrue: true, summaryEvent: true, prop: 'shareFee', label: '公摊费用', type: 'custom', sortable: 'custom', width: '60', permission: "lirunprsi", formatter: (row) => !row.shareFee ? " " : row.shareFee },
  { istrue: true, summaryEvent: true, prop: 'profit4', label: '净利润', type: 'custom', tipmesg: '计算公式：毛四-平台弃货-公摊费用', sortable: 'custom', width: '65', permission: "lirunprsi" },
  { istrue: true, summaryEvent: true, prop: 'profit4Rate', label: '净利率', type: 'custom', tipmesg: '计算公式：净利润/销售金额', sortable: 'custom', width: '65', permission: "lirunprsi", formatter: (row) => !row.profit4Rate ? "0%" : row.profit4Rate + '%' },



  // { istrue: true, summaryEvent: true, prop: 'packageAvgFee', label: '包装均价', sortable: 'custom', width: '70', permission: "cgcoltxpddprsi", formatter: (row) => !row.packageAvgFee ? " " : row.packageAvgFee },
  // { istrue: true, summaryEvent: true, prop: 'giftLinkAmont', label: '赠品链接成本', sortable: 'custom', width: '80', permission: "cgcoltxpddprsi", formatter: (row) => !row.giftLinkAmont ? ' ' : row.giftLinkAmont },
  // { istrue: true, summaryEvent: true, prop: 'freightAvgFee', label: '快递均价', sortable: 'custom', width: '70', formatter: (row) => !row.freightAvgFee ? " " : row.freightAvgFee },

  // { istrue: true, summaryEvent: true, prop: 'profit32', label: '毛三利润新', sortable: 'custom', width: '67', type: 'custom', tipmesg: '毛二利润-预估费用', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit32 ? " " : row.profit32 },
  // { istrue: true, summaryEvent: true, prop: 'profit32Rate', label: '毛三利润新率', type: 'custom', tipmesg: '毛三利润/销售金额', sortable: 'custom', width: '50', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit32Rate ? " " : row.profit32Rate + '%' },
  // // { istrue: true, summaryEvent: true, prop: 'exitCostRate', label: '出仓成本占比', sortable: 'custom', width: '70', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + '%' },
  // // { istrue: true, summaryEvent: true, prop: 'exitProfit', label: '出仓利润', sortable: 'custom', width: '70', formatter: (row) => !row.exitProfit ? " " : row.exitProfit },
  // // { istrue: true, summaryEvent: true, prop: 'isExitProfit', label: '正出仓利润', sortable: 'custom', width: '70', type: 'click', formatter: (row) => !row.isExitProfit ? " " : row.isExitProfit, handle: (that, row) => that.onGoodsProfitShow(row, 1) },
  // { istrue: true, summaryEvent: true, prop: 'replaceSendCost', label: '代发成本差', sortable: 'custom', width: '80', tipmesg: '运营导入', type: 'click', formatter: (row) => !row.replaceSendCost ? " " : row.replaceSendCost },
  // { istrue: true, summaryEvent: true, prop: 'goodProfitRate', label: '商品毛利率', sortable: 'custom', width: '80', tipmesg: '（付款金额-（销售成本+赠品成本+赠品链接成本+代发成本差））/付款金额', formatter: (row) => !row.goodProfitRate ? " " : row.goodProfitRate + '%' },
  // {
  //   istrue: true, summaryEvent: true, rop: '', label: `退款`, merge: true, prop: 'mergeField1', width: '60',
  //   cols: [
  //     { istrue: true, summaryEvent: true, prop: 'cancelOrderCount', label: '发货前退款单量', sortable: 'custom', width: '90', type: 'custom' },
  //     { istrue: true, summaryEvent: true, prop: 'refundAmontBefore', label: '发货前退款', sortable: 'custom', width: '80', type: 'custom' },
  //     { istrue: true, summaryEvent: true, prop: 'refundAmontBeforeRate', label: '发货前退款率', sortable: 'custom', tipmesg: '发货前退款/付款金额', width: '80', type: 'custom', formatter: (row) => !row.refundAmontBeforeRate ? " " : (row.refundAmontBeforeRate) + '%' },
  //     { istrue: true, summaryEvent: true, prop: 'refundAmontAfter', label: '发货后退款', sortable: 'custom', width: '80', type: 'custom' },
  //     { istrue: true, summaryEvent: true, prop: 'refundAmontAfterRate', label: '发货后退款率', sortable: 'custom', tipmesg: '发货后退款/付款金额', width: '80', type: 'custom', formatter: (row) => !row.refundAmontAfterRate ? " " : (row.refundAmontAfterRate) + '%' },
  //     { istrue: true, summaryEvent: true, prop: 'refundAmont', label: '总退款金额', sortable: 'custom', width: '70', tipmesg: '当日发生的总退款金额，包括历史订单', formatter: (row) => !row.refundAmont ? " " : row.refundAmont },
  //   ]
  // },
  // { istrue: true, summaryEvent: true, prop: 'saleAmontAvg', label: '销售金额(分摊)', sortable: 'custom', width: '80', tipmesg: '销售金额(无id分摊)', formatter: (row) => !row.saleAmontAvg ? " " : row.saleAmontAvg },
  // { istrue: true, summaryEvent: true, prop: 'brushAmont', label: '刷单金额', sortable: 'custom', width: '80', tipmesg: '刷单金额', formatter: (row) => !row.brushAmont ? " " : row.brushAmont },
  // { istrue: true, summaryEvent: true, prop: 'cancelCost', label: '取消单返还成本', sortable: 'custom', width: '50', tipmesg: '返还客服已确认的取消单成本,以确认时间统计', formatter: (row) => !row.cancelCost ? " " : row.cancelCost, type: 'click', handle: (that, row) => that.showEveryDayrefund(row) },
  // // { istrue: true, summaryEvent: true, prop: 'refundCostSj', label: '销退仓成本', sortable: 'custom', width: '70', formatter: (row) => !row.refundCostSj ? " " : row.refundCostSj, type: 'click', handle: (that, row) => that.showEveryDayrefund(row) },
  // { istrue: true, summaryEvent: true, prop: 'giftAmont', label: '赠品成本', sortable: 'custom', width: '70', permission: "cgcoltxpddprsi", formatter: (row) => !row.giftAmont ? ' ' : row.giftAmont, type: 'click', handle: (that, row) => that.showGiftDetail(row) },
  // { istrue: true, summaryEvent: true, prop: 'saleCostAvg', label: '销售成本(分摊)', sortable: 'custom', width: '80', tipmesg: '销售成本(无id分摊)', formatter: (row) => !row.saleCostAvg ? " " : row.saleCostAvg },
  // { istrue: true, summaryEvent: true, prop: 'negativeExitProfit', label: '负出仓利润', sortable: 'custom', width: '70', type: 'click', formatter: (row) => !row.negativeExitProfit ? " " : row.negativeExitProfit, handle: (that, row) => that.onGoodsProfitShow(row, 2) },

  // { istrue: true, summaryEvent: true, prop: 'profit1', label: '订单毛利', sortable: 'custom', width: '60', type: 'custom', tipmesg: '销售金额-销售成本', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  // { istrue: true, summaryEvent: true, prop: 'profit1Rate', label: '毛利率', sortable: 'custom', width: '50', type: 'custom', tipmesg: '订单毛利/销售金额', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate) + '%' },
  // { istrue: true, summaryEvent: true, prop: 'dkAmont', label: '平台扣点', sortable: 'custom', width: '70', type: 'custom', permission: "cgcoltxpddprsi", formatter: (row) => !row.dkAmont ? " " : row.dkAmont },
  // { istrue: true, summaryEvent: true, prop: 'deductAmount', label: '违规总扣款', sortable: 'custom', width: '45', type: 'custom', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
  // {istrue:true,summaryEvent:true,prop:'shoudanlijing',label:'首单礼金',sortable:'custom', width:'80',permission:"cgcoltxpddprsi",formatter:(row)=> row.shoudanlijing==0?" ": row.shoudanlijing},
  // { istrue: true, summaryEvent: true, prop: 'advratio', label: '广告占比%', sortable: 'custom', width: '80', formatter: (row) => !row.advratio ? " " : (row.advratio) + "%" },
  // { istrue: true, summaryEvent: true, prop: 'alladv', label: '总广告费', sortable: 'custom', width: '70', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
  // { istrue: true, summaryEvent: true, prop: 'isExitProfit', label: '正出仓利润', sortable: 'custom', width: '70', type: 'click', formatter: (row) => !row.isExitProfit ? " " : row.isExitProfit, handle: (that, row) => that.onGoodsProfitShow(row, 1) },
  // { istrue: true, summaryEvent: true, prop: 'exitProfit', label: '出仓利润', sortable: 'custom', width: '70', formatter: (row) => !row.exitProfit ? " " : row.exitProfit },
  // { istrue: true, summaryEvent: true, prop: 'negativeExitProfit', label: '负出仓利润', sortable: 'custom', width: '70', type: 'click', formatter: (row) => !row.negativeExitProfit ? " " : row.negativeExitProfit, handle: (that, row) => that.onGoodsProfitShow(row, 2) },
  // { istrue: true, summaryEvent: true, prop: 'freightFee', label: '发货运费(快递费)', sortable: 'custom', width: '80', type: 'click', handle: (that, row) => that.showFreightDetail(row) },

  // { istrue: true, summaryEvent: true, prop: 'replaceSendFreightFee', label: '代发快递费', sortable: 'custom', width: '80', formatter: (row) => !row.replaceSendFreightFee ? " " : row.replaceSendFreightFee },
  // { istrue: true, summaryEvent: true, prop: 'freightFeeVirtual', label: '虚拟快递费', sortable: 'custom', width: '80', tipmesg: '(订单量-发货前退款订单量-代发订单量)*平均快递费', formatter: (row) => !row.freightFeeVirtual ? " " : row.freightFeeVirtual },
  // { istrue: true, summaryEvent: true, prop: 'freightAvgWeight', label: '快递均重', sortable: 'custom', width: '70', formatter: (row) => !row.freightAvgWeight ? " " : row.freightAvgWeight },
  // {
  //   istrue: true, summaryEvent: true, prop: 'profit3PredictRate', label: '毛三预估比例', type: 'custom', permission: "lirunprsi,profit3rsi", tipmesg: '(空白链接ID成本+异常成本+补发成本+代发成本+采购运费+产品费用+工资+损耗)/销售金额',
  //   sortable: 'custom', width: '80', formatter: (row) => !row.profit3PredictRate ? " " : (row.profit3PredictRate * 100)?.toFixed(0) + '%'
  // },
  // { istrue: true, summaryEvent: true, prop: 'profit3PredictFee', label: '预估费用', type: 'custom', tipmesg: '毛三预估比例*销售金额', permission: "lirunprsi,profit3rsi", sortable: 'custom', width: '80', formatter: (row) => !row.profit3PredictFee ? " " : row.profit3PredictFee },
  // {
  //   istrue: true, summaryEvent: true, prop: 'ygfy', label: `预估费用`, merge: true, prop: 'mergeField2', width: '90',
  //   cols: [
  //     { istrue: true, summaryEvent: true, prop: 'cuiShouFee', label: '催收费', sortable: 'custom', width: '70', formatter: (row) => row.cuiShouFee == 0 ? " " : row.cuiShouFee, tipmesg: '每日导入' },
  //     { istrue: true, summaryEvent: true, prop: 'sampleFee', label: '拿样费', sortable: 'custom', width: '70', formatter: (row) => row.sampleFee == 0 ? " " : row.sampleFee, tipmesg: '运营/美工拿样，预估，5000元/月，每日167元' },
  //     { istrue: true, summaryEvent: true, prop: 'shootFee', label: '道具费', sortable: 'custom', width: '70', formatter: (row) => row.shootFee == 0 ? " " : row.shootFee, tipmesg: '美工拍摄道具费，预估，10000元/月' },
  //     { istrue: true, summaryEvent: true, prop: 'processingCost', label: '加工部工资', sortable: 'custom', width: '70', formatter: (row) => row.processingCost == 0 ? " " : row.processingCost, tipmesg: '参考日报数据维护-加工费（工价*（订单量-发货前退款单量））' },
  //     { istrue: true, summaryEvent: true, prop: 'lossOffFee', label: '损耗下架', sortable: 'custom', width: '70', formatter: (row) => row.lossOffFee == 0 ? " " : row.lossOffFee, tipmesg: '预估，0.1%' },
  // { istrue: true, summaryEvent: true, prop: 'serviceFee', label: '仓储费', sortable: 'custom', width: '70', formatter: (row) => row.serviceFee == 0 ? " " : row.serviceFee, },
  // { istrue: true, summaryEvent: true, prop: 'inventoryCheckFee', label: '盘点损益', sortable: 'custom', width: '80', formatter: (row) => row.inventoryCheckFee == 0 ? " " : row.inventoryCheckFee, tipmesg: '(盘亏+盘盈),商品ID对应的商品编码在仓库中盘亏的金额,多个商品ID共有的商品编码前7天的销售成本分摊', type: 'click', handle: (that, row) => that.showInventoryCheckFee(row) },
  // { istrue: true, summaryEvent: true, prop: 'customerServiceWages', label: '客服工资', sortable: 'custom', width: '70', formatter: (row) => row.customerServiceWages == 0 ? " " : row.customerServiceWages, tipmesg: '销售金额*1%' },
  // { istrue: true, summaryEvent: true, prop: 'serviceFee', label: '资金费用', sortable: 'custom', width: '70', formatter: (row) => " " },
  //   ]
  // },
  // { istrue: true, summaryEvent: true, prop: 'saleAfterAmount', label: '售后工资（不参与计算）', sortable: 'custom', width: '80', formatter: (row) => row.saleAfterAmount == 0 ? " " : row.saleAfterAmount },
  // { istrue: true, summaryEvent: true, prop: 'saleBeforeAmount', label: '售前工资（不参与计算）', sortable: 'custom', width: '80', formatter: (row) => row.saleBeforeAmount == 0 ? " " : row.saleBeforeAmount },
  // { istrue: true, summaryEvent: true, prop: 'deductAmount', label: '违规总扣款', sortable: 'custom', width: '80', type: 'custom', tipmesg: '违规总扣款', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
  // { istrue: true, summaryEvent: true, prop: 'shareRate', label: '公摊费率', type: 'custom', tipmesg: '明细2/销售金额', sortable: 'custom', width: '70', permission: "lirunprsi", formatter: (row) => !row.shareRate ? " " : (row.shareRate * 100)?.toFixed(0) + '%' },
  // { istrue: true, summaryEvent: true, prop: 'exitCostRate', label: '出仓成本占比', sortable: 'custom', width: '70', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + '%' },
  // { istrue: true, summaryEvent: true, prop: 'exitCost', label: '出仓成本', sortable: 'custom', width: '70', formatter: (row) => !row.exitCost ? " " : row.exitCost, tipmesg: '真实出仓成本+预估出仓成本' },
  // { istrue: true, summaryEvent: true, prop: 'extAmount3', label: '真实出仓成本', sortable: 'custom', width: '60', formatter: (row) => row.extAmount3 == 0 ? " " : row.extAmount3 },
  // { istrue: true, summaryEvent: true, prop: 'extAmount4', label: '预估出仓成本', sortable: 'custom', width: '60', formatter: (row) => row.extAmount4 == 0 ? " " : row.extAmount4 },
];
const tableHandles = [
  // { label: "导入胜算", handle: (that) => that.onstartImport() },
  //{label:"参数设置", handle:(that)=>that.onShowEditParm()},
  /*   {label:"导入", handle:(that)=>that.startImport()}, */
  { label: "导出", permission: 'productReportPddTemu_kj_export', handle: (that) => that.$refs.table.setExportCols() },
  { label: "计算日报", handle: (that) => that.showCalDayRepoty() },
  { label: "刷新", handle: (that) => that.onRefresh() },
  /* {label:"模板-生意参谋平台", handle:(that)=>that.downloadTemplate()} */
];
const detailTableCols = [
  { istrue: true, prop: 'dateYearMonthDay', label: '日报', formatter: (row) => formatTime(row.dateYearMonthDay, 'YYYY-MM-DD') },
  { istrue: true, prop: 'pageName', label: '页面', },
  { istrue: true, prop: 'userName', label: '运营', },
  { istrue: true, prop: 'content', label: '异常', },
]
export default {
  name: "productReportPddCrossBorder",
  components: { MyContainer, MyConfirmButton, numberRange, MySearch, MySearchWindow, cesTable, productdrchart, InputMult, freightDetail, buschar, expressfreightanalysis, importmodule, ordergiftdetail, BusinessStaffPlatForm, EveryDayrefund, InventoryCheckFee, inputYunhan, vxetablebase, dailyConfirmation },
  data() {
    return {
      colOptions: [],
      colSelect: ["SKC", "商品名称", "商品编码",
        "上架天数最小值", '上架天数最大值',
        '店铺', '运营组', '运营专员',
        '运营助理', '毛利1', '白名单',
      ],
      selectedTitles: [],
      dailyNewspaperToolbar: false,
      storeid: 'sheinDailyPaper202408271310',
      shopCodeList: [],
      shopList: [],
      projectList: [],
      dailyPaperList: {
        startTime: null,
        endTime: null,
        dailyReportType: null,
        platform: null,
      },
      verifyDailyPaper: false,
      that: this,
      filter: {
        projName: [],
        isIgnoreRptProCode: 0,
        isIgnoreSpecialProCode: null,
        refundType: 1,
        reportType: 0,
        platform: 13,
        shopCode: null,
        proCode: null,
        styleCode: null,
        productName: null,
        brandId: null,
        groupId: null,
        startTime: null,
        endTime: null,
        timerange: null,
        // 运营助理
        userId: null,
        // 车手
        userId2: null,
        // 备用
        userId3: null,
        // 运营专员 ID
        operateSpecialUserId: null,
        profit2UnZero: null,
        profit3UnZero: null,
        profit33UnZero: null,
        profit4UnZero: null,
        groupType: null,
        noProfitDay: null,
        userIds: [],//运营助理
        operateSpecialUserIds: [],//运营专员
        groupIds: [],//小组
        listingEndTime: null,
        listingStartTime: null,
        goodsCodes: null,
      },
      onimportfilter: {
        yearmonthday: null,
      },
      styleCode: null,
      options: [],
      hearlist,
      platformlist: platformlist,
      userList: [],
      calDayRepotyDate: null,
      brandlist: [],
      grouplist: [],
      directorlist: [],
      financialreportlist: [],
      confirmDate: '',
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      pager: { OrderBy: " saleAmont ", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      earchloading: false,
      dialogCalDayRepotVis: false,
      pageLoading: false,
      summaryarry: {},
      selids: [],
      fileList: [],
      dialogVisibleData: false,
      dialogConfirmdata: false,
      dialogVisible: false,
      uploadLoading: false,
      importFilte: {},
      fileList: [],
      fileparm: {},
      editparmVisible: false,
      editLoading: false,
      editparmLoading: false,
      drawervisible: false,
      /* dialogDrVisibleShengYi:false, */
      dialogDrVisible: false,
      drparamProCode: '',
      autoformparm: {
        fApi: {},
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
        rule: []
      },
      freightDetail: {
        visible: false,
        filter: {
          proCode: null,
          timeRange: []
        }
      },
      EveryDayrefund: {
        visible: false,
        filter: {
          proCode: null,
          timeRange: [],
          afterSaleType: "2",
          orderStatus: "已取消",
          goodsStatus: "",
          timeRange1: []
        }
      },
      InventoryCheckFee: {
        visible: false,
        filter: {
          proCode: null,
        }
      },
      giftDetail: { visible: false },
      costDialog: { visible: false, rows: [] },
      buscharDialog: { visible: false, title: "", data: {}, loading: false },
      drawervisible: false,
      searchloading: false,
      styleTaglist: [],
      dailyNewspaperToolbar: false,

      batchvisible: false,  //批量计算（不同步开发自己用）
      BatchDate: null,

      // 提醒
      detailTableCols: detailTableCols,
      detailVisible: false,
      detailList: [],
      detailLoading: false,
    };
  },
  async mounted() {
    this.onverifyMethod()
    this.oninitializeEcho();
    const { data: data1, success: success1 } = await getProductProjectList({ projName: '' });
    if (success1) {
      this.projectList = data1;
    }
  },
  async created() {
    await this.init()
    await this.getShopList();
    await this.initformparm();
    if (this.$route.query && this.$route.query.dayCount) {
      this.filter.noProfitDay = parseInt(this.$route.query.dayCount);
      this.filter.platform = this.$route.query.platform == null ? null : parseInt(this.$route.query.platform);
      this.filter.shopCode = this.$route.query.shopCode;
      this.filter.groupId = this.$route.query.groupId;
      this.filter.operateSpecialUserId = this.$route.query.operateSpecialUserId;
      let dateStr = this.$route.query.yearMonthDay.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
      this.filter.timerange = [dateStr, dateStr];
      this.filter.refundType = 1;
      this.onSearch();
    }
  },
  methods: {
    changeOptions(valArr) {
      this.colOptions = valArr;
    },
    //初始化
    selectAll() {
      if (this.colOptions.length != this.colSelect.length) {
        this.colOptions = this.colSelect.map(i => i);
      } else {
        this.colOptions = [];
      }
    },
    //数据初始化回显
    async oninitializeEcho() {
      const { data, success } = await GetVxeTableColumnCacheAsync({ tableId: this.storeid });
      if (success) {
        let storeData = data ? JSON.parse(data) : [];
        this.colOptions = this.colSelect.filter(i => storeData.includes(i));
      } else {
        this.colOptions = [];
      }
      this.selectedTitles = this.colOptions
    },
    //点击设置
    clickToolbar() {
      this.oninitializeEcho();
      this.dailyNewspaperToolbar = true;
    },
    //全选
    changeOptions(valArr) {
      this.colOptions = valArr;
    },
    //点击确定
    async verifyOptions() {
      await SetVxeTableColumnCacheAsync({ tableId: this.storeid, ColumnConfig: JSON.stringify(this.colOptions) });
      var arr = this.colSelect.filter(i => this.colOptions.indexOf(i) < 0); // 未选中的
      this.selectedTitles = this.colSelect.filter(i => {
        if (i !== '日期') {
          return !arr.includes(i)
        }
      });
      this.dailyNewspaperToolbar = false;
    },
    async onverifyMethod() {
      // this.onConditionsMethod()
      // const { data, success } = await getPageDailyReportItemConfirmList({ ...this.dailyPaperList, currentPage: 1, pageSize: 50 })
      // if (!success) return
      // data.forEach(item => {
      //   if (item.dailyReportItem == "终版新版日报" && item.clacConfirmZt != 1) {
      //     this.verifyDailyPaper = true
      //   }
      // });

      // const res1 = await getAllShopList({ platforms: [13] });
      // this.shopList = [];
      // res1.data?.forEach(f => {
      //   if (f.shopName && f.shopCode)
      //     this.shopList.push(f);
      // });
    },
    onConditionsMethod() {
      if (this.filter.timerange) {
        this.dailyPaperList.startTime = this.filter.timerange[0];
        this.dailyPaperList.endTime = this.filter.timerange[1];
      }
      this.dailyPaperList.platform = this.filter.platform;
      this.dailyPaperList.dailyReportType = '拼多多跨境日报'
    },
    onConfirmationMethod() {
      this.onConditionsMethod()
      this.dialogConfirmdata = true
    },
    //粘贴去空格
    handlePaste(event) {
      event.preventDefault();
      const clipboardData = event.clipboardData || window.clipboardData;
      const pastedText = clipboardData.getData('text');
      const trimmedText = pastedText.replace(/\s/g, '');
      document.execCommand('insertText', false, trimmedText);
    },
    async calDayRepoty() {
      this.detailLoading = true;
      if (this.calDayRepotyDate == null) {
        this.$message({ type: 'warning', message: '请先选择日期!' });
        return
      }
      let res = await calDayRepoty_KJAsync({ type: 'PDDTEMU', yearMonthDay: this.calDayRepotyDate });
      console.log("res",res)
      this.detailLoading = false;
      if (res.success) {
        this.dialogCalDayRepotVis = false;
        if (res.data != null) {
          this.detailVisible = true;
          this.detailList = res.data
        } else {
          this.$message({ type: 'success', message: '正在计算中,请稍候...' });
        }
      } else {
        return;
      }
    },
    async showCalDayRepoty() {
      this.dialogCalDayRepotVis = true;
    },
    async confirmData() {
      if (!this.confirmDate) {
        this.$message({ type: 'warning', message: '请选择日期!' });
        return;
      }
      let par = {
        dailyReportDate: this.confirmDate,
        dailyReportType: '拼多多跨境日报',
      };
      let confirmtitle = '【' + this.confirmDate + '】' + par.dailyReportType + '数据，确定确认？';
      this.$confirm(confirmtitle)
        .then(async _ => {
          let res = await insertDailyReportConfirmList(par);
          if (res.data) {
            this.$message({ type: 'success', message: '保存成功!' });
          }
          this.dialogConfirmdata = false;
        })
        .catch(_ => { });
    },
    cellClick(prms) {
      if (prms?.column?.field && prms?.column?.field === 'profit3IncreaseGoOnDays') {
        let row = prms.row;
        this.showprchart2(row.proCode, row.platform);
      }

    },
    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init() {
      var date1 = new Date(); date1.setDate(date1.getDate() - 1);
      var date2 = new Date(); date2.setDate(date2.getDate() - 1);
      this.filter.timerange = [];
      this.filter.timerange[0] = this.datetostr(date1);
      this.filter.timerange[1] = this.datetostr(date2);
    },
    async initformparm() {
      let that = this;
      this.autoformparm.rule = [{ type: 'hidden', field: 'id', title: 'id', value: '', col: { span: 13 } },
      { type: 'select', field: 'groupId', title: '组长', value: '', update(val, rule) { { that.updateruleGroup(val) } }, ...await ruleDirectorGroup(), props: { filterable: true } },
      { type: 'InputNumber', field: 'Profit3PredictRate', title: '毛三预估比例%', value: null, props: { min: 0, precision: 3 }, col: { span: 6 } },
      { type: 'InputNumber', field: 'ShareRate', title: '公摊费率%', value: null, props: { min: 0, precision: 3 }, col: { span: 6 } }]
    },
    //系列编码远程搜索
    async remoteMethod(query) {
      if (query !== '') {
        this.searchloading == true
        this.options = [];
        setTimeout(async () => {
          const res = await getListByStyleCode({ currentPage: 1, pageSize: 50, styleCode: query })
          this.searchloading = false
          res?.data?.forEach(f => {
            this.options.push({ value: f.styleCode, label: f.styleCode })
          });
        }, 200)
      }
      else {
        this.options = []
      }
    },
    async getShopList() {
      const res1 = await getAllShopList({ platforms: [13] });
      this.shopList = [];
      res1.data?.forEach(f => {
        if (f.shopName && f.shopCode)
          this.shopList.push(f);
      });
      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });
      var res5 = await getProductStyleTagList();
      this.styleTaglist = res5.data.map(item => {
        return { value: item.styleTag, label: item.styleTag };
      });
      var res4 = await getAllProBrand();
      this.brandlist = res4.data.map(item => {
        return { value: item.key, label: item.value };
      });
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      if (column.prop == 'onTime') {
        //this.pager.IsAsc = !this.pager.IsAsc;
      }
      await this.onSearch();
    },
    onRefresh() {
      this.onSearch()
    },
    async onSearch() {

      this.filter.listingEndTime = null;
      this.filter.listingStartTime = null;

      this.$refs.table.changecolumn_setTrue(["yearMonthDay"]);
      if (this.filter.groupType == 1 || this.filter.groupType == 2) {
        this.$refs.table.changecolumn(["yearMonthDay"]);
      }
      this.$refs.pager.setPage(1);
      await this.getList().then(res => { });
      // loading.close();
    },
    async getList() {
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      if (this.filter.minOnTimeNum && this.filter.maxOnTimeNum && this.filter.minOnTimeNum > this.filter.maxOnTimeNum) {
        return this.$message.error('最小上架天数不能大于最大上架天数');
      }
      if (this.filter.minOnTimeNum) {
        this.filter.listingEndTime = dayjs(this.filter.timerange[0]).subtract(this.filter.minOnTimeNum, 'day').format('YYYY-MM-DD');
      }
      if (this.filter.maxOnTimeNum) {
        this.filter.listingStartTime = dayjs(this.filter.timerange[1]).subtract(this.filter.maxOnTimeNum, 'day').format('YYYY-MM-DD');
      }
      this.filter.styleCode = Array.isArray(this.styleCode) ? this.styleCode.join() : '';
      var that = this;
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };
      // this.listLoading = true;
      startLoading();
      const res = await pageProductDayReport(params).then(res => {
        loading.close();
        if (res?.data?.list && res?.data?.list.length > 0) {
          for (var i in res.data.list) {
            if (!res.data.list[i].freightFee) {
              res.data.list[i].freightFee = " ";
            }
            if (that.filter.refundType == 2) {
              res.data.list[i].RefundAmont = res.data.list[i].RefundAmontByPay;
              res.data.list[i].Profit3 = res.data.list[i].Profit3ByPay;
              res.data.list[i].Profit3Rate = res.data.list[i].Profit3RateByPay;
            }
          }
        }
        if (that.filter.refundType == 2) {
          res.data.summary.Profit3Rate_sum = res.data?.summary?.Profit3RateByPay_sum;
          res.data.summary.RefundAmontByPay = res.data?.summary?.RefundAmontByPay_sum;
          res.data.summary.Profit3ByPay = res.data?.summary?.Profit3ByPay_sum;
        }
        that.total = res.data?.total;
        that.financialreportlist = res.data?.list;
        that.$refs.table.loadRowEcharts();
        that.summaryarry = res.data?.summary;
      });
    },
    showEveryDayrefund(row) {
      this.EveryDayrefund.filter.proCode = row.proCode;
      if (row.yearMonthDay != null) {
        var dayStr = row.yearMonthDay.substr(0, 4) + '-' + row.yearMonthDay.substr(4, 2) + '-' + row.yearMonthDay.substr(6, 2)
        this.EveryDayrefund.filter.timeRange = [dayStr, dayStr];
      }
      else {
        this.EveryDayrefund.filter.timeRange = this.filter.timerange
      }
      this.EveryDayrefund.visible = true;
      setTimeout(async () => {
        await this.$refs.EveryDayrefund.onSearch();
      }, 100);
    },
    showInventoryCheckFee(row) {
      this.InventoryCheckFee.filter.proCode = row.proCode;
      if (row.yearMonthDay != null) {
        var dayStr = row.yearMonthDay.substr(0, 4) + '-' + row.yearMonthDay.substr(4, 2) + '-' + row.yearMonthDay.substr(6, 2)
        this.InventoryCheckFee.filter.timeRange = [dayStr, dayStr];
      }
      else {
        this.InventoryCheckFee.filter.timeRange = this.filter.timerange
      }
      this.InventoryCheckFee.visible = true;
      setTimeout(async () => {
        await this.$refs.InventoryCheckFee.onSearch();
      }, 100);
    },
    showOrderDetail(row) {
      this.freightDetail.filter.proCode = row.proCode;
      if (row.yearMonthDay != null) {
        var dayStr = row.yearMonthDay.substr(0, 4) + '-' + row.yearMonthDay.substr(4, 2) + '-' + row.yearMonthDay.substr(6, 2)
        this.freightDetail.filter.timeRange = [dayStr, dayStr];
      }
      else {
        this.freightDetail.filter.timeRange = this.filter.timerange
      }
      this.freightDetail.visible = true;
      setTimeout(async () => {
        await this.$refs.freightDetail.onSearch();
      }, 100);
    },
    showFreightDetail(row) {
      if (row.freightFee >= 1) {
        this.freightDetail.filter.proCode = row.proCode;
        if (row.yearMonthDay != null) {
          var dayStr = row.yearMonthDay.substr(0, 4) + '-' + row.yearMonthDay.substr(4, 2) + '-' + row.yearMonthDay.substr(6, 2)
          this.freightDetail.filter.timeRange = [dayStr, dayStr];
        }
        else {
          this.freightDetail.filter.timeRange = this.filter.timerange
        }
        this.freightDetail.visible = true;
        setTimeout(async () => {
          await this.$refs.freightDetail.onSearch();
        }, 100);
      }
    },
    showProcodesimilarity(row) {
      if (row.styleCode != null) {
        this.$router.push({ path: '/order/procodesimilarity', query: { styleCode: row.styleCode } })
      }
    },
    async showGiftDetail(row) {
      var yearMonthDayStart = row.yearMonthDay
      var yearMonthDayEnd = row.yearMonthDay
      if (this.filter.groupType) {
        yearMonthDayStart = this.filter.timerange[0].replace("-", "").replace("-", "")
        yearMonthDayEnd = this.filter.timerange[1].replace("-", "").replace("-", "")
      }
      this.giftDetail.visible = true;
      let _th = this;
      await this.$nextTick(async () => { await _th.$refs.ordergiftdetail.onShow(yearMonthDayStart, yearMonthDayEnd, row.proCode); });
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    onRefresh() {
      this.onSearch()
    },
    async updateruleGroup(groupid) {
      if (!groupid)
        this.autoformparm.fApi.resetFields()
      else {
        const res = await getParm({ groupId: groupid })
        var arr = Object.keys(this.autoformparm.fApi);
        res.data.groupId = groupid;
        if (!res.data?.Profit3PredictRate) res.data.Profit3PredictRate = 0;
        if (!res.data?.ShareRate) res.data.ShareRate = 0;
        await this.autoformparm.fApi.setValue(res.data)
      }
    },
    async showprchart2(prcode, platform) {
      window['lastseeprcodedrchart'] = prcode
      window['lastseeprcodedrchart1'] = platform
      window['lastseeprcodedrchart2'] = this.filter.refundType
      this.drparamProCode = prcode
      this.dialogDrVisible = true
      /* this.dialogDrVisibleShengYi=true */
    },
    async onstartImport() {
      this.dialogVisible = true;
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      if (!this.onimportfilter.yearmonthday) {
        this.$message({ type: 'warning', message: '请选择年月!' });
        return;
      }
      this.uploadLoading = true
      this.$refs.upload.submit();
    },
    async uploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("platform", 1);
      form.append("yearmonthday", this.onimportfilter.yearmonthday);
      var res = await importProductDayReport(form);
      if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
      else this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading = false
    },
    async onExport(opt) {
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter, ...opt };
      startLoading();
      var res = await exportProductDayReport(params);
      loading.close()
      if (!res?.data) {
        return
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', 'TEMU全托日报' + new Date().toLocaleString() + '_.xlsx')
      aLink.click()
    },
    async onShowEditParm() {
      this.editparmVisible = true
      const res = await getParm()
      var arr = Object.keys(this.autoformparm.fApi);
      if (arr.length > 0)
        this.autoformparm.fApi.resetFields()
      await this.autoformparm.fApi.setValue(res.data)
    },
    async onSetEditParm() {
      this.editparmLoading = true;
      await this.autoformparm.fApi.validate(async (valid, fail) => {
        if (valid) {
          const formData = this.autoformparm.fApi.formData();
          const res = await setParm(formData);
          if (res.code == 1) this.editparmVisible = false;
        } else { }
      })
      this.editparmLoading = false;
    },
    async onsummaryClick(property) {
      // this.$message({message:property,type:"warning"});
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };
      params.column = property;
      let that = this;
      that.listLoading = true;
      that.buscharDialog.loading = true;
      const res = await queryDayReportAnalysisAsync(params).then(res => {
        that.buscharDialog.loading = false;
        that.buscharDialog.data = res.data;
        that.buscharDialog.title = res.data.legend[0];
      });
      that.listLoading = false;
      that.buscharDialog.visible = true;
      await that.$refs.buschar.initcharts();

    },
    showCost(row) {
      if (row.replaceSendCost > 0) {
        this.costDialog.visible = true;
        this.costDialog.rows = [row];
      }
    },
    renderCost(row) {
      if (row.replaceSendCost > 0) {
        return "color:blue;cursor:pointer;";
      }
      else {
        return "";
      }
    },
    showupload() {
      this.drawervisible = true;
    },
    async callbackProCode(val) {
      this.filter.proCode = val;
    },
    async onGoodsProfitShow(row, vala) {
      let request = {
        startDate: this.filter.timerange[0],
        endDate: this.filter.timerange[1],
        proCode: row.proCode,
        plat: 'qpt'
      };
      this.$showDialogform({
        path: `@/views/bookkeeper/reportday/productReportPddGoods.vue`,
        title: '编码利润',
        args: { row, request, vala },
        height: '650px',
        width: '90%',
      })
    },
    async callbackGoodsCode(val) {
      this.filter.goodsCodes = val;
      //this.onSearch();
    },
    async entersearch(val) {
      // this.filter.indexNo = val;
      this.onSearch();
    },
    async BatchCalculation() {
      this.batchvisible = true

    },
    async BatchRepoty() {

      const params = {
        type: 'PDDTEMU',
        yearMonthDayStart: this.BatchDate[0],
        yearMonthDayEnd: this.BatchDate[1],
        noTimeIf: 1

      }

      let res = await batchCalDayRepoty_KJAsync(params);
      if (res.data) {
        this.$message({ type: 'success', message: '保存成功!' });
      }
      this.batchvisible = false;
    }



  },

};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

::v-deep .el-select__tags-text {
  max-width: 50px;
}
</style>
