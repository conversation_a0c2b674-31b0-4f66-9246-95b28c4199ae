<template>
    <MyContainer>
    <div style="display: flex; flex-direction: row; width: 100%; height: 100%;overflow-y: hidden;">
      <div style="flex: 1;height: 390px;">
        <div class="tooltips" v-if="!isPlatform">红色代表自己！！！</div>
        <vxetablebase :id="'styleCodeReportDetails202408041737'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
          :isRemoteSort="false" :tableData='tableData' :tableCols='isPlatform ? tableCols : tableCols2'
          :isSelection="false" :isSelectColumn="false" :toolbarshow="false"
          style="width: 100%;height: 340px;  margin: 0" v-loading="loading">
        </vxetablebase>
        共 {{ tableData.length }} 条
      </div>
      <div style="flex: 1;height: 100%;height: 390px; justify-content: center; align-items: center;">
        <div style="display: flex; flex-direction: row; width: 100%; justify-content: center; align-items: center;">
          <el-radio v-model="oneradio" label="1" @change="changepie('1')">{{ isPlatform?'数量':'金额' }}</el-radio>
          <el-radio v-model="oneradio" label="2" @change="changepie('2')">占比</el-radio>
        </div>
        <buscharpie :charid="'charNegative1'" :isshowbai="oneradio == '2' ? true : false"  :thisStyle="thisStyle" :gridStyle="gridStyle" :analysisData="pieList"
          v-if="pieList"></buscharpie>
      </div>
    </div>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from 'dayjs'
import { formatPlatform } from '@/utils/tools'
import pieChart from '@/views/admin/homecomponents/PieChart'
import buscharpie from "@/views/order/LossOrderCostStatistics/childPages/piebus.vue"
const tableCols = [
    { width: 'auto', align: 'center', sortable: true, prop: 'Platform', label: '平台', formatter: (row) => row.Platform ? formatPlatform(row.Platform) : null, },
    { width: 'auto', align: 'center', sortable: true, prop: 'CountValue', label: '数量', },
    { width: 'auto', align: 'center', sortable: true, prop: 'CountRate', label: '占比', formatter: (row) => row.CountRate !== null ? row.CountRate.toFixed(2) + '%' : 0 + '%', },
]
const tableCols2 = [
    { width: 'auto', align: 'center', sortable: true, prop: 'StyleCode', label: '系列编码', color: (row) => row.diffStyleCode == row.StyleCode ? 'red' : 'black' },
    { width: 'auto', align: 'center', sortable: true, prop: 'CountValue', label: '金额', color: (row) => row.diffStyleCode == row.StyleCode ? 'red' : 'black' },
    { width: 'auto', align: 'center', sortable: true, prop: 'CountRate', label: '占比', color: (row) => row.diffStyleCode == row.StyleCode ? 'red' : 'black', formatter: (row) => row.CountRate !== null ? row.CountRate.toFixed(2) + '%' : 0 + '%', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, pieChart, buscharpie
    },
    props: {
        data: {
            type: Array,
            default: () => []
        },
        isPlatform: {
            type: Boolean,
            default: false
        },
        detailsLabel: {
            type: String,
            default: ''
        },
        styleCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            that: this,
            tableCols,
            tableCols2,
            tableData: [],
            loading: true,
            pieList: null,
            oneradio: '1',
            thisStyle: {
                width: '100%', height: '390px', 'box-sizing': 'border-box', 'line-height': '360px'
            },
            pieobj: {},
            gridStyle: {
                top: '0',
                left: '5%',
                right: '15%',
                bottom: '0',
                containLabel: false
            },
        }
    },
    async mounted() {
        this.$nextTick(() => {
            this.tableCols.forEach(item => {
                if (item.prop == 'CountValue') {
                    this.$set(item, 'label', this.detailsLabel)
                }
            })
            this.tableCols2.forEach(item => {
                if (item.prop == 'CountValue') {
                    this.$set(item, 'label', this.detailsLabel)
                }
            })
            if(this.isPlatform == true){
              this.pieobj.name = 'Platform';
              this.pieobj.value = 'CountValue';
              this.pieobj.code = 'CountRate';
            } else {
              this.pieobj.name = 'StyleCode';
              this.pieobj.value = 'CountValue';
              this.pieobj.code = 'CountRate';
            }
            this.changepie('1')
        })
        // this.data.forEach(item => {
        //     item.CountRate = item.CountRate !== null ? item.CountRate.toFixed(2) + '%' : 0 + '%'
        // })
        this.tableData = this.data
        this.loading = false
    },
    methods: {
      changepie(val){
        let chatlist = [];
        this.pieList = null;
        if(val == '1'){
          this.data.map((item)=>{
            chatlist.push({
              name: this.pieobj.name == 'Platform' ? formatPlatform(item[this.pieobj.name]) : item[this.pieobj.name],
              value: item[this.pieobj.value]
            })
          })
        }else if(val == '2'){
          this.data.map((item)=>{
            chatlist.push({
              name: this.pieobj.name == 'Platform' ? formatPlatform(item[this.pieobj.name]) : item[this.pieobj.name],
              value: item[this.pieobj.code].toFixed(2)
            })
          })
        }
        this.$nextTick(()=>{
          this.pieList = {
            title: {
              left: 'center',
              top: 'center'
            },
            series: [
              {
                type: 'pie',
                data: chatlist,
                radius: ['40%', '70%']
              }
            ]
          };
        })
      }
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.tooltips {
    color: red;
    font-size: 14px;
    margin-bottom: 10px;
}
</style>
