<template>
    <div>
        <el-form :label-position="labelPosition" label-width="150px" :model="ruleForm">
            <el-row>
                <el-col :span="8">
                    <el-form-item label="款式编码:">
                        <el-input v-model="ruleForm.styleCode" :disabled="true" placeholder="款式编码"
                            style="width: 220px;"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="商品编码:">
                        <el-input v-model="ruleForm.goodsCode" :disabled="true" placeholder="商品编码"
                            style="width: 220px;"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="采购:">
                        <el-input v-model="ruleForm.brandName" :disabled="true" placeholder="采购"
                            style="width: 220px;"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="供应商名称:">
                        <el-select v-model="ruleForm.supplierId" ref="supplier_id" filterable remote reserve-keyword
                            placeholder="请输入供应商" :remote-method="remoteSearchSupplier" :loading="supplierLoading"
                            style="width:220px">
                            <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
                                :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="是否半成品自动开单:">
                        <el-select v-model="ruleForm.isSemiOrder" style="width: 220px;" placeholder="是否半成品自动开单"
                            class="publicCss" clearable>
                            <el-option label="是" value="是" />
                            <el-option label="否" value="否" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="是否自动开单:">
                        <el-select v-model="ruleForm.isAutoOrder" style="width: 220px;" placeholder="是否自动开单"
                            class="publicCss" clearable>
                            <el-option label="是" value="是" />
                            <el-option label="否" value="否" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24">
                    <el-form-item label="触发进货天数:">
                        如值大于等于计划采购对应N,则按最大进货天数开单,且天数不可以大于【最大进货天数】
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24">
                    <el-radio-group v-model="ruleForm.triggerPurchaseType" class="boxCenter" style="margin-left: 60px;">
                        <el-radio label="按结果" class="boxCenter" style="width: 33%;">
                            <div class="boxCenter">
                                <div>按结果:</div>
                                <el-tooltip class="item" effect="dark" content="最大9999，不可输入0，保留一位小数"
                                    placement="top-start">
                                    <el-input-number :min="0.1" :max="9999" :controls="false" :precision="1"
                                        v-model="ruleForm.triggerPurchaseResultDay"></el-input-number>
                                </el-tooltip>
                            </div>
                        </el-radio>
                        <el-radio label="按公式" class="boxCenter" style="width: 33%;">
                            <div class="boxCenter">
                                <div>按公式:</div>
                                N+ <el-input-number :min="0" :max="9999" :controls="false" :precision="1"
                                    v-model="ruleForm.triggerPurchaseFixed"></el-input-number>
                            </div>
                        </el-radio>
                    </el-radio-group>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24">
                    <el-form-item label="同系列连带天数:">
                        天数不可以大于【最大进货天数】
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24">
                    <el-radio-group v-model="ruleForm.equalStyleIncludedType" class="boxCenter"
                        style="margin-left: 60px;">
                        <el-radio label="按结果" class="boxCenter" style="width: 33%;">
                            <div class="boxCenter">
                                <div>按结果:</div>
                                <el-tooltip class="item" effect="dark" content="最大9999，不可输入0，保留一位小数"
                                    placement="top-start">
                                    <el-input-number :min="0.1" :max="9999" :controls="false" :precision="1"
                                        v-model="ruleForm.equalStyleIncludedResultDay"></el-input-number>
                                </el-tooltip>
                            </div>
                        </el-radio>
                        <el-radio label="按公式" class="boxCenter" style="width: 33%;">
                            <div class="boxCenter">
                                <div>按公式:</div>
                                (<el-input-number :min="1" :max="9999" :controls="false" :precision="0"
                                    v-model="ruleForm.equalStyleIncludedMultiple"></el-input-number>
                                <div>N+</div>
                                <el-input-number :min="0" :max="9999" :controls="false" :precision="1"
                                    v-model="ruleForm.equalStyleIncludedFixed"></el-input-number>)
                                <div>*</div>

                                <el-input-number :min="0.1" :max="9999" :controls="false" :precision="1"
                                    v-model="ruleForm.equalStyleIncludedRate"></el-input-number>
                            </div>
                        </el-radio>
                    </el-radio-group>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24">
                    <el-form-item label="最大进货天数:">
                        如值大于等于计划采购对应N,则按最大进货天数开单
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24">
                    <el-radio-group v-model="ruleForm.maxPurchaseType" class="boxCenter" style="margin-left: 60px;">
                        <el-radio label="按结果" class="boxCenter" style="width: 33%;">
                            <div class="boxCenter">
                                <div>按结果:</div>
                                <el-tooltip class="item" effect="dark" content="最大9999，不可输入0，保留一位小数"
                                    placement="top-start">
                                    <el-input-number :min="0.1" :max="9999" :controls="false" :precision="1"
                                        v-model="ruleForm.maxPurchaseResultDay"></el-input-number>
                                </el-tooltip>
                            </div>
                        </el-radio>
                        <el-radio label="按公式" class="boxCenter" style="width: 33%;">
                            <div class="boxCenter">
                                <div>按公式:</div>
                                <el-input-number :min="1" :max="9999" :controls="false" :precision="0"
                                    v-model="ruleForm.maxPurchaseMultiple"></el-input-number>
                                <div>N+</div>
                                <el-input-number :min="0" :max="9999" :controls="false" :precision="1"
                                    v-model="ruleForm.maxPurchaseFixed"></el-input-number>
                            </div>
                        </el-radio>
                    </el-radio-group>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24">
                    <el-form-item label="备注:">
                        <el-input v-model="ruleForm.remark" type="textarea" maxlength="500" resize="none"
                            rows="4"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <div class="boxCenter" style="justify-content: center;">
                <el-button @click="$emit('close')">取消</el-button>
                <el-button @click="submitForm" type="primary">提交</el-button>
            </div>
        </el-form>
    </div>
</template>

<script>
import { pageSupplierAll } from '@/api/inventory/supplier'
import { GetPurchaseAutoOrderDataCalSet, BatchEditPurchaseAutoOrderData, EditPurchaseAutoOrderData } from '@/api/inventory/purchaseAutoOrder'
export default {
    data() {
        return {
            ruleForm: {},
            supplierOptions: [],
            isBatch: false,
        }
    },
    props: {
        selectList: {
            type: Array,
            default: () => []
        }
    },
    async created() {
        if (this.selectList?.length > 1) {
            this.ruleForm.triggerPurchaseFixed = 1
            this.ruleForm.equalStyleIncludedMultiple = 2
            this.ruleForm.equalStyleIncludedFixed = 1
            this.ruleForm.equalStyleIncludedRate = 0.6
            this.ruleForm.maxPurchaseMultiple = 2
            this.ruleForm.maxPurchaseFixed = 1
            this.isBatch = true
        } else {
            await this.getProps()
        }
    },
    mounted() { },
    methods: {
        async getProps() {
            const { data } = await GetPurchaseAutoOrderDataCalSet({ id: this.selectList[0].id });
            data.equalStyleIncludedResultDay = data.equalStyleIncludedResultDay ?? undefined
            data.maxPurchaseResultDay = data.maxPurchaseResultDay ?? undefined
            data.triggerPurchaseResultDay = data.triggerPurchaseResultDay ?? undefined
            this.ruleForm = data
            await this.remoteSearchSupplier(data.supplierName);
        },
        async submitForm() {
            if (this.selectList?.length == 1) {
                const arr = ['supplierId', 'isSemiOrder', 'isAutoOrder'];
                const map = {
                    'supplierId': '供应商名称',
                    'isSemiOrder': '是否半成品自动开单',
                    'isAutoOrder': '是否自动开单'
                }
                arr.forEach(key => {
                    if (this.ruleForm[key] === undefined || this.ruleForm[key] === null || this.ruleForm[key] === '') {
                        this.$message.error(`${map[key]}不能为空`);
                        throw new Error(`${map[key]}不能为空`);
                    }
                });
            }
            if (this.selectList?.length == 1) {
                const { success } = await EditPurchaseAutoOrderData(this.ruleForm);
                if (!success) return
                this.$message.success('操作成功');
                this.$emit('close');
                this.$emit('clear');
            } else {
                console.log('2222');

                const { success } = await BatchEditPurchaseAutoOrderData({ ids: this.selectList.map(f => f.id), ...this.ruleForm });
                if (!success) return
                this.$message.success('操作成功');
                this.$emit('close');
                this.$emit('clear');
            }
        },
        //绑定供应商选择
        async remoteSearchSupplier(parm) {
            this.supplierOptions = [];
            if (!parm) {
                return;
            }
            var options = [];
            const res = await pageSupplierAll({ currentPage: 1, pageSize: 50, name: parm });
            res?.data?.list.forEach(f => {
                options.push({ value: f.supplier_id, label: f.name })
            });
            this.supplierOptions = options;
        },
    }
}
</script>

<style scoped lang="scss">
.boxCenter {
    display: flex;
    align-items: center;
    gap: 10px;
}

.el-row {
    margin-bottom: 10px;
}
</style>