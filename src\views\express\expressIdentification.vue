<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="快递包材识别" name="first1" style="height: 100%">
        <basicData ref="basicData" style="height: 100%"></basicData>
      </el-tab-pane>
      <el-tab-pane label="快递包材核对" name="first2" style="height: 100%">
        <expressPackageCheck ref="expressPackageCheck" style="height: 100%">
        </expressPackageCheck>
      </el-tab-pane>
      <el-tab-pane label="快递重量" name="first3" style="height: 100%">
        <courierWeight ref="courierWeight" style="height: 100%">
        </courierWeight>
      </el-tab-pane>
      <el-tab-pane label="集包设备" name="first4" style="height: 100%">
        <equipment ref="equipment" style="height: 100%">
        </equipment>
      </el-tab-pane>
      <el-tab-pane label="集包揽收" name="first5" style="height: 100%">
        <shipping ref="shipping" style="height: 100%">
        </shipping>
      </el-tab-pane>
      <el-tab-pane label="集包人员" name="first6" style="height: 100%">
        <packagePerson ref="packagePerson" style="height: 100%">
        </packagePerson>
      </el-tab-pane>
      <el-tab-pane label="集包工作量" name="first7" style="height: 100%">
        <packageWorkload ref="packageWorkload" style="height: 100%">
        </packageWorkload>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import basicData from "@/views/express/basicData";
import expressPackageCheck from "@/views/express/expressPackageCheck";
import courierWeight from "@/views/express/courierWeight";
import equipment from "@/views/express/equipment";
import shipping from "@/views/express/shipping";
import packagePerson from "@/views/express/packagePerson";
import packageWorkload from "@/views/express/packageWorkload";
export default {
  name: 'expressIdentification',
  components: {
    MyContainer, basicData, expressPackageCheck,courierWeight,equipment,shipping,packagePerson,packageWorkload
  },

  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },

  async mounted() {

  },

  methods: {

  },
};
</script>

<style lang="scss" scoped></style>
