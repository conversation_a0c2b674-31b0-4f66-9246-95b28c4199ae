<template>
  <div>
    <el-form ref="refruleForm" :model="ruleForm" label-width="90px" v-loading="loading" :rules="rules">
      <el-row>
        <el-col :span="12">
          <el-form-item label="仓库" prop="warehouse">
            <el-select v-model="ruleForm.warehouse" placeholder="请选择" class="publicCss" @change="handleWarehouseChange">
              <el-option v-for="item in stashList" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="区域" prop="warehouseArea">
            <el-select v-model="ruleForm.warehouseArea" placeholder="请选择" :disabled="manageEdit" class="publicCss"
              @change="handleWarehouseAreaChange">
              <el-option v-for="item in regionList" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="通道号" prop="channelNumber">
            <el-input v-model.trim="ruleForm.channelNumber" placeholder="请输入通道号" maxlength="50" clearable
              class="publicCss" @blur="handleWarehouseAreablur" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="货架号" prop="shelfNumber">
            <el-input v-model.trim="ruleForm.shelfNumber" placeholder="请输入货架号" maxlength="50" clearable
              class="publicCss" @blur="handleWarehouseAreablur" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="层号" prop="floorNumber">
            <el-input v-model.trim="ruleForm.floorNumber" placeholder="请输入层号" maxlength="50" clearable class="publicCss"
              @blur="handleWarehouseAreablur" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="位号" prop="bitNumber">
            <el-input v-model.trim="ruleForm.bitNumber" placeholder="请输入位号" maxlength="50" clearable class="publicCss"
              @blur="handleWarehouseAreablur" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="库位编码" prop="warehouseBitCode">
            <el-input style="width: 96%;" v-model.trim="ruleForm.warehouseBitCode" placeholder="请输入" maxlength="50"
              clearable />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div style="display: flex;justify-content: center;margin-top: 20px;">
      <el-button @click="cancelClick">取消</el-button>
      <el-button type="primary" @click="handleClick">保存</el-button>
    </div>
  </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from 'dayjs'
import { addOrEditWarehouseLocationManagement } from '@/api/inventory/sampleGoods';
export default {
  name: "manageStorageLocation",
  components: {
    MyContainer, vxetablebase
  },
  props: {
    //仓库
    stashList: {
      type: Array,
      default: () => []
    },
    //区域
    regionList: {
      type: Array,
      default: () => []
    },
    parameterEdit: {
      type: Object,
      default: () => { }
    },
    stashData: {
      type: Array,
      default: () => []
    },
    result: {
      type: Array,
      default: () => []
    },
    stashlt: {
      type: Array,
      default: () => []
    },
    manageEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rules: {
        warehouse: [{ required: true, message: '请选择仓库' }],
        warehouseArea: [{ required: true, message: '请选择区域' }],
        warehouseBitCode: [{ required: true, message: '请输入库位编码' }],
        channelNumber: [{ required: true, message: '请输入通道号' }],
        shelfNumber: [{ required: true, message: '请输入货架号' }],
        floorNumber: [{ required: true, message: '请输入层号' }],
        bitNumber: [{ required: true, message: '请输入位号' }],
      },
      ruleForm: {
        warehouse: null,//仓库
        warehouseArea: null,//区域
        channelNumber: null,//通道号
        shelfNumber: null,//货架号
        floorNumber: null,//层号
        bitNumber: null,//位号
        warehouseBitCode: null,//库位编码
        warehouseAreaCode: null,//区域编码
        warehouseCode: null,//仓库编码
      },
      that: this,
      timeRanges: [],
      loading: false,
    }
  },
  watch: {
    ruleForm: {
      handler(newVal) {
        let warehouseBitCode = newVal.warehouseAreaCode || '';
        const parts = [];
        if (newVal.channelNumber) {
          parts.push(newVal.channelNumber);
        }
        if (newVal.shelfNumber) {
          parts.push(newVal.shelfNumber);
        }
        if (newVal.floorNumber) {
          parts.push(newVal.floorNumber);
        }
        if (newVal.bitNumber) {
          parts.push(newVal.bitNumber);
        }
        if (parts.length > 0) {
          warehouseBitCode += parts.join('-');
        }
        this.$set(this.ruleForm, 'warehouseBitCode', warehouseBitCode);
      },
      deep: true, // 深度监听，确保嵌套字段变化时触发
      immediate: true // 组件初始化时立即执行一次
    }
  },
  async mounted() {
    this.ruleForm = JSON.parse(JSON.stringify(this.parameterEdit || {}))
    //清除表单校验状态
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate()
    })
  },
  methods: {
    handleWarehouseChange(value) {
      const selectedWarehouse = this.stashlt.find(warehouse => warehouse.warehouse === value);
      this.ruleForm.warehouseCode = selectedWarehouse ? selectedWarehouse.warehouseCode : null
      this.ruleForm.warehouseArea = ''
      this.regionList = this.result.filter(item => item.warehouseCode === this.ruleForm.warehouseCode).map(item => item.warehouseArea)
    },
    handleWarehouseAreablur() {
      const numberRegex = /^\d+$/;
      const fields = [
        { key: 'bitNumber', label: '位号' },
        { key: 'floorNumber', label: '层号' },
        { key: 'shelfNumber', label: '货架号' },
        { key: 'channelNumber', label: '通道号' }
      ];
      for (const field of fields) {
        const value = this.ruleForm[field.key];
        if (value && !numberRegex.test(value)) {
          this.$message.warning(`${field.label}必须为数字`);
          this.ruleForm[field.key] = '';
          return;
        }
      }
      this.handleWarehouseAreaChange(this.ruleForm.warehouseArea)
    },
    handleWarehouseAreaChange(value) {
      const selectedArea = this.result.find(area => area.warehouseArea === value);
      this.ruleForm.warehouseAreaCode = selectedArea ? selectedArea.warehouseAreaCode : null
      let warehouseBitCode = '';
      if (this.ruleForm.warehouseAreaCode) {
        warehouseBitCode = this.ruleForm.warehouseAreaCode;
        if (this.ruleForm.channelNumber || this.ruleForm.shelfNumber || this.ruleForm.floorNumber || this.ruleForm.bitNumber) {
          warehouseBitCode += '--';
          if (this.ruleForm.channelNumber) {
            warehouseBitCode += this.ruleForm.channelNumber;
          }
          if (this.ruleForm.shelfNumber) {
            warehouseBitCode += '-' + this.ruleForm.shelfNumber;
          }
          if (this.ruleForm.floorNumber) {
            warehouseBitCode += '-' + this.ruleForm.floorNumber;
          }
          if (this.ruleForm.bitNumber) {
            warehouseBitCode += '-' + this.ruleForm.bitNumber;
          }
        }
      }
      this.$set(this.ruleForm, 'warehouseBitCode', warehouseBitCode)
    },
    async handleClick() {
      this.$refs.refruleForm.validate(async (valid) => {
        if (valid) {
          this.loading = true
          let { success, data } = await addOrEditWarehouseLocationManagement(this.ruleForm)
          this.loading = false
          if (success) {
            this.$message.success('保存成功')
            this.$emit('successClick')
          }
        } else {
          return false
        }
      })
    },
    cancelClick() {
      this.$emit('cancelClick')
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
}

.publicCss {
  width: 90%;
}
</style>
