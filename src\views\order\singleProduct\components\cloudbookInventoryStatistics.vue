<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss"
          style="width: 210px;" />
        <chooseWareHouse v-model="ListInfo.wmsIds" style="width: 192px;" multiple class="publicCss"
          :defaultId="[14131873, 14435900, 14302081, 14319983, 14300743, 14196460, 13947804, 14101668]" />
        <inputYunhan ref="refgoodsCodes" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes" width="175px"
          placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500" :maxlength="1000000"
          @callback="goodsCodesCallback($event)" title="商品编码" style="margin:0 10px 0 0;">
        </inputYunhan>
        <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.styleCode" placeholder="系列编码" maxlength="50" clearable class="publicCss" />
        <el-input-number v-model="ListInfo.saveDay" :min="0" :max="99999" placeholder="保留天数" :controls="false"
          :precision="1" style="width:110px;" class="publicCss" />
        <number-range :min.sync="ListInfo.allotQtyMin" :max.sync="ListInfo.allotQtyMax" min-label="调拨数量-最小值"
          max-label="调拨数量-最大值" class="publicCss" :precision="0" />
        <number-range :min.sync="ListInfo.allotPackQtyMin" :max.sync="ListInfo.allotPackQtyMax" min-label="调拨箱数-最小值"
          max-label="调拨箱数-最大值" class="publicCss" :precision="0" />
        <div>
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
          <el-button type="primary" size="mini" @click="importProps">发起审批</el-button>
        </div>
      </div>
    </template>
    <vxetablebase ref="table" id="20241205155758" :loading="loading" :that="that" :is-index="true" :hasexpand="true"
      :tablefixed="true" :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols"
      :condition="ListInfo" :is-selection="false" :is-select-column="true" :is-index-fixed="false"
      style="width: 100%; margin: 0;" height="100%" :showsummary="data.summary ? true : false" :showTrendChart="false"
      :summaryarry="data.summary" @onTrendChart="trendChart" @sortchange="sortchange">
      <template slot="right">
        <vxe-column title="操作" width="80" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" @click="() => {
                isSummary = false
                $refs.table.onTrendChart(row)
              }">趋势图</el-button>
            </div>
          </template>
          <template #footer="{ row, _columnIndex }">
            <el-button type="text" @click="() => {
              isSummary = true
              $refs.table.onTrendChart(data.list[0])
            }">趋势图</el-button>
          </template>
        </vxe-column>
      </template>
      <template #wmsId="{ row }">
        <div>
          {{ row.wmsName }}
        </div>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-drawer :title="chatProp.data._title" :visible.sync="chatProp.chatDialog" size="90%"
      :close-on-click-modal="false" direction="btt">
      <div v-if="!chatProp.chatLoading">
        <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至" :clearable="false"
          start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" :picker-options="pickerOptions"
          style="margin: 10px" @change="
            trendChart({
              ...chatPropOption,
              startDate: $event[0],
              endDate: $event[1],
            }, row)
            " />
        <buschar v-if="!chatProp.chatLoading" :analysis-data="chatProp.data"
          :thisStyle="{ width: '100%', height: '690px', 'box-sizing': 'border-box', 'line-height': '360px' }" />
      </div>
      <div v-else v-loading="chatProp.chatLoading" />
    </el-drawer>

    <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div slot="title" class="header-title">
        <span class="title-text"><span>导入数据</span></span>
        <span class="title-close"><el-button @click="downLoadFile">下载模版</el-button></span>
      </div>
      <div>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload" :disabled="uploadLoading" v-throttle="2000">{{ (uploadLoading ? '上传中' : '上传')
            }}</el-button>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/PackBinInv/AllotBackInvStat/'
const api1 = '/api/verifyOrder/SaleItems/CodeStatBrand/'
import { mergeTableCols } from '@/utils/getCols'
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import decimal from '@/utils/decimalToFixed'
export default {
  name: "cloudbookInventoryStatistics",
  components: {
    MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, chooseWareHouse
  },
  data() {
    return {
      api,
      api1,
      platformlist,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        summarys: [],
        startDate: dayjs().subtract(0, 'day').format('YYYY-MM-DD'),
        endDate: dayjs().subtract(0, 'day').format('YYYY-MM-DD'),
        goodsName: '',
        styleCode: '',
        saveDay: undefined,
        wmsIds: [],
        queryOutWms: true,
      },
      data: {},
      chatProp: {
        chatDialog: false, // 趋势图弹窗
        chatTime: null, // 趋势图时间
        chatLoading: true, // 趋势图loading
        data: {}, // 趋势图数据
      },
      timeRanges: [],
      tableCols: [],
      tableData: [],
      total: 0,
      loading: true,
      pickerOptions,
      isExport: false,
      isSummary: false,
      row: {},
      importVisible: false,
      uploadLoading: false,
    }
  },
  async mounted() {
    await this.getCol();
    await this.getList()
  },
  methods: {
    importProps() {
      this.fileList = []
      this.file = null
      this.uploadLoading = false
      this.importVisible = true
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.importVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("file", item.file);
      var res = await request.post(`${this.api1}ImportForGoodsAllot`, form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.importVisible = false;
      await this.getList()
    },
    downLoadFile() {
      window.open("../../../static/excel/发起调拨.xlsx", "_self");
    },
    //商品编码
    goodsCodesCallback(e) {
      this.ListInfo.goodsCodes = e
    },
    async trendChart(option, row) {
      this.row = JSON.parse(JSON.stringify(row));
      var endDate = null;
      var startDate = null;
      if (option.startDate && option.endDate) {
        startDate = option.startDate;
        endDate = option.endDate;
      } else {
        endDate = option.date;
        startDate = new Date(option.date);
        startDate.setDate(option.date.getDate() - 30);
        startDate = dayjs(startDate).format("YYYY-MM-DD");
        endDate = dayjs(endDate).format("YYYY-MM-DD");
      }
      option.filter.filters = option.filter.filters.filter((item) => item.field !== option.dateField);
      option.filter.filters.push({
        field: option.dateField,
        operator: "GreaterThanOrEqual",
        value: startDate,
      });
      option.filter.filters.push({
        field: option.dateField,
        operator: "LessThanOrEqual",
        value: endDate,
      });
      if (this.isSummary) {
        option.filter.filters = option.filter.filters.filter((item) => option.key.indexOf(item.field) <= -1);
      }
      option.key = !this.isSummary ? option.key : [];
      option.startDate = startDate;
      option.endDate = endDate;
      this.chatProp.chatTime = [startDate, endDate];
      this.chatProp.chatLoading = true;
      const { data, success } = await await request.post(`${this.api}GetTrendChart`, option);
      if (success) {
        if (option.key?.length > 0 && data.title) {
          data._title = data.title + ' - 趋势图'
        } else {
          data._title = '汇总趋势图'
        }
        data.title = null
        this.chatProp.data = data;
      }
      this.chatProp.chatLoading = false;
      this.chatProp.chatDialog = true;
      this.chatPropOption = option;
    },
    proCodeCallback(val) {
      this.ListInfo.proCode = val
    },
    // 导出数据,这里前端可以封装一个方法
    async exportProps() {
      this.isExport = true
      await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
        this.isExport = false
      })
    },
    async getCol() {
      const { data, success } = await request.post(`${this.api}GetColumns`)
      if (success) {
        this.tableCols = mergeTableCols(data)
        this.ListInfo.summarys = data
          .filter((a) => a.summaryType)
          .map((a) => {
            return { column: a["sort-by"], summaryType: a.summaryType };
          });
      }
    },
    async getList(type) {
      if (type === "search") {
        this.ListInfo.currentPage = 1;
        this.$refs.pager.setPage(1);
      }
      this.loading = true;
      // 使用时将下面的方法替换成自己的接口
      try {
        const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
        if (success) {
          for (let key in data.summary) {
            if (key == 'orderRate_sum') {
              data.summary[key] = data.summary[key] !== null ? decimal(data.summary[key], 100, '*') + '%' : ''
            }
            if (key == 'date_sum') {
              data.summary[key] = data.summary[key] !== null ? String(data.summary[key]) : ''
            }
          }
          this.data = data;
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.loading = false;
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 5px;

  .publicCss {
    width: 175px;
    margin: 0 5px 5px 0;
  }
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px 0 0;

  .title-text {
    display: flex;
    align-items: center;

    .title-close {
      margin-left: 10px;
    }
  }
}
</style>
