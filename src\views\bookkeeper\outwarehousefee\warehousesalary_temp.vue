<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>

                <el-button style="padding: 0;margin: 0;border: none;">
                  <span style="margin-right: 2px;">启用起始时间:</span>
                  <el-date-picker style="width: 280px" v-model="filter.daterange1" type="daterange" format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                      :clearable="false" :picker-options="pickerOptions"></el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin: 0;border: none;margin-left: 5px;">
                  <span style="margin-right: 2px;">启用结束时间:</span>
                  <el-date-picker style="width: 280px" v-model="filter.daterange2" type="daterange" format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                      :clearable="false" :picker-options="pickerOptions"></el-date-picker>
                </el-button>
                <!-- <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-select v-model="filter.shopName" style="width: 240px" size="mini" clearable>
                        <el-option v-for="item in myWarehouseList" :key="item.label" :label="item.label"
                            :value="item.label" />
                    </el-select>
                </el-button> -->
                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-input v-model.trim="filter.wareHouse" placeholder="仓库" style="width:160px;" clearable
                        maxlength="40" />
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>

                <el-button type="primary" @click="onAddnewMethod(false)">新增</el-button>
                <el-button type="primary" @click="onAddnewMethod(true)">快捷录入</el-button>
                <el-button type="primary" @click="onExport" :loading="exportLoading">导出</el-button>
                <!-- <el-button type="primary" @click="onSearch">计算出仓费</el-button> -->
            </el-button-group>

        </template>

        <vxetablebase :id="'warehousesalary20241115'" :border="true" :align="'center'"
            :tablekey="'warehousesalary20241115'" ref="table" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :isSelectColumn="true" :showsummary='true' :tablefixed='true'
            :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols' :tableHandles='tableHandles'
            :loading="listLoading" style="width:100%;height:100%;margin: 0"   :xgt="9999">
            <template slot="right">
        <vxe-column title="操作" width="120" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;align-items: center;">
              <el-button type="text" @click="onEditMethod(row)">编辑</el-button>
              <my-confirm-button  type="delete" :loading="row._loading" @click="onDelete($index, row)" />
            </div>
          </template>
        </vxe-column>
      </template>
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

        <el-dialog :title="editName" :visible.sync="editdialogVisible" width="600px" v-dialogDrag>
      <div style="height: 260px;width: 100%;" v-loading="editloading">
        <el-form :model="editform" ref="refeditform" :rules="editrules" label-width="150px" class="demo-ruleForm">
          <el-form-item label="仓库" prop="warehouseName" v-if="!isFastInput">
            <el-input v-model="editform.warehouseName" style="width: 80%;"></el-input>
          </el-form-item>
          <el-form-item label="请输入薪资" prop="totalSalary">
            <el-input v-model="editform.totalSalary" style="width: 80%;"></el-input>
          </el-form-item>
          <el-form-item label="启用起始日期" prop="qyStartDate">
            <el-date-picker v-model="editform.qyStartDate" type="date" style="width: 80%;" :value-format="'yyyy-MM-dd'" />
          </el-form-item>
          <el-form-item label="启用结束日期" prop="qyEndDate">
            <el-date-picker v-model="editform.qyEndDate" type="date" style="width: 80%;" :value-format="'yyyy-MM-dd'" />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editdialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSaveMethod">确 定</el-button>
      </span>
    </el-dialog>

    </my-container>
</template>
<script>
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { platformlist, formatPlatform, formatTime, pickerOptions,warehouselist } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar';
import inputYunhan from "@/components/Comm/inputYunhan";
import decimal from '@/utils/decimal'


import { getTempWareHouseSalary,editTempWareHouseSalary,delTempWareHouseSalary, ExportTempWareHouseSalary} from '@/api/bookkeeper/reportdayV2'

const tableCols = [
    { istrue: true, prop: 'warehouseName', label: '仓库', sortable: 'custom', width: '260', formatter: (row) => row.warehouseName },
    { istrue: true, prop: 'qyStartDate', label: '启用起始时间', sortable: 'custom', width: '150', formatter: (row) => formatTime(row.qyStartDate,"YYYY-MM-DD")  },
    { istrue: true, prop: 'qyEndDate', label: '启用结束时间', sortable: 'custom', width: '150' , formatter: (row) => formatTime(row.qyEndDate,"YYYY-MM-DD") },
    { istrue: true, prop: 'totalSalary', label: '薪资', sortable: 'custom', width: '80' },
];
const tableHandles = [
    //{ label: "编辑", handle: (that, row) => that.onEdit(row) },
];
const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");

export default {
    name: "WarehouseSalary",
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable,
        buschar, vxetablebase,inputYunhan
    },
    data() {
        return {
            isFastInput: false,
            that: this,
            filter: {
                daterange1: [startDate, endDate],
                daterange2: [startDate, endDate],
                feeType: "拣货_订单数",
                orderNoInners:null,
                wareHouse: null,
                startTime : null,
                endTime : null,
                startTime2 : null,
                endTime2 : null,
            },
            pickerOptions: pickerOptions,
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            datalist: [],
            pager: { OrderBy: "", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            fileList: [],
            fileparm: {},

            importFeeType: "",
            myWarehouseList: warehouselist,

            dialogUploadData: {
                isSuccess: true,
                title: "",
                visible: false,
                uploadLoading: false,
                importWarehouseCode: 0,
                importFeeType: "",
            },
            editrules: {
              warehouseName: [{ required: true, message: '请输入仓库', trigger: 'blur' }],
              totalSalary: [{ required: true, message: '请输入薪资', trigger: 'blur' }],
              qyStartDate: [{ required: true, message: '请输入启用起始日期', trigger: 'blur' }],
              qyEndDate: [{ required: true, message: '请输入启用结束日期', trigger: 'blur' }],
            },
      editloading: false,
      editform: {
        expressCompany: null,
        area: null,
        expressFee: null,
        id: 0,
      },
      editdialogVisible: false,
      editName: '新增',
      exportLoading: false,
        };
    },
    async mounted() {
        await this.onSearch();
    },
    async created() {
    },
    methods: {
      async sortchange(column) {
        if (!column.order)
          this.pager = {};
        else
          this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
        await this.onSearch();
      },
      async onSearch() {
        this.$refs.pager.setPage(1);
        await this.getList();
      },
      async getList() {
        this.filter.startTime = null;
        this.filter.endTime = null;
        if (this.filter.daterange1) {
          this.filter.startTime = this.filter.daterange1[0];
          this.filter.endTime = this.filter.daterange1[1];
        }
          this.filter.startTime2 = null;
          this.filter.endTime2 = null;
          if (this.filter.daterange2) {
              this.filter.startTime2 = this.filter.daterange2[0];
              this.filter.endTime2 = this.filter.daterange2[1];
          }
          var that = this;
          var pager = this.$refs.pager.getPager();
          const params = { ...pager, ...this.pager, ...this.filter };
          this.listLoading = true;
          const res = await getTempWareHouseSalary(params);
          that.total = res.data?.total;
          that.datalist = res.data?.list;
          that.summaryarry = res.data?.summary;
          this.listLoading = false;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },

        async onImport(feeType) {
            this.dialogUploadData.importWarehouseCode = this.filter.warehouseCode;
            this.dialogUploadData.importFeeType = feeType;
            this.dialogUploadData.title = "导入" + feeType;
            this.dialogUploadData.visible = true;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.dialogUploadData.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("dataType", this.dialogUploadData.importFeeType);
            form.append("warehouseCode", this.dialogUploadData.importWarehouseCode.toString());
            form.append("warehouseName", this.myWarehouseList.find(f => f.value == this.dialogUploadData.importWarehouseCode)?.label);
            console.log(form, 'form')
            var res = await ImportOutWarehouseFee(form);
            if (res?.success) {
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
            }
            this.dialogUploadData.uploadLoading = false;
            this.dialogUploadData.isSuccess = res?.success;
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            if (this.dialogUploadData.isSuccess)
                this.dialogUploadData.visible = false;
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadRemove(file, fileList) {
            this.fileList = [];
        },
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            if (!this.dialogUploadData.importWarehouseCode) {
                this.$message({ message: "请先选择仓库", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        orderNoInnerBack(val) {
            this.filter.orderNoInners = val;
        },
        async onSaveMethod() {
          this.$refs.refeditform.validate(async (valid) => {
            if (valid) {
              if (this.isFastInput) {
                this.editloading = true;
                let allSuccess = true;
                try {
                  let a = ['义乌市昀晗供应链管理有限公司', '南昌退件仓', '【西安分仓】', '【昀晗-WH】'];
                  let b = decimal(this.editform.totalSalary ? Number(this.editform.totalSalary) : 0, 4, 2, '/');
                  for (let i = 0; i < a.length; i++) {
                    await new Promise(resolve => setTimeout(resolve, 500));// 延迟 500ms
                    let res = await editTempWareHouseSalary({
                      warehouseName: a[i],
                      totalSalary: b,
                      qyStartDate: this.editform.qyStartDate,
                      qyEndDate: this.editform.qyEndDate,
                      id: 0
                    });
                    if (!res?.success) {
                      allSuccess = false;
                      this.$message.error(`保存失败：${a[i]}`);
                      break; // 或 return 或 continue
                    }
                  }
                  if (allSuccess) {
                    this.$message.success("保存成功");
                    this.editdialogVisible = false;
                    await this.getList();
                  }
                } catch (err) {
                  console.error(err);
                  this.$message.error("请求出错");
                } finally {
                  this.editloading = false; // 关闭 loading
                }
              } else {
                this.editloading = true
                var res = await editTempWareHouseSalary(this.editform)
                this.editloading = false
                if (res?.success) {
                  this.$message({ message: "保存成功", type: "success" });
                  this.editdialogVisible = false
                  await this.getList()
                }
              }
            }
          });
        },
        onAddnewMethod(value) {
          this.setEditForm('新增', {
            expressCompany: null,
            area: null,
            expressFee: null,
            id: 0
          });
          this.isFastInput = value;
        },
        onClearingMethod(){
          this.$refs.refeditform.clearValidate()
          this.$refs.refeditform.resetFields();
          this.editform = {
            warehouseName: null,
            totalSalary: null,
            qyStartDate: null,
            qyEndDate: null,
            id: 0
          }
        },
        onEditMethod(row) {
          this.isFastInput = false;
          this.setEditForm('编辑', row);
        },
        setEditForm(editName, formData) {
          this.editdialogVisible = true;
          this.onClearingMethod();
          this.editName = editName;
          this.editform = {
            warehouseName: formData.warehouseName,
            totalSalary: formData.totalSalary,
            qyStartDate: formData.qyStartDate,
            qyEndDate: formData.qyEndDate,
            id: formData.id
          };
        },
        async onDelete(index, row) {
            row._loading = true
            const para = { id: row.id }
            const res = await delTempWareHouseSalary(para)

            row._loading = false

            if (!res?.success) {
              return
            }
            this.$message({
              message: this.$t('admin.deleteOk'),
              type: 'success'
            })
            await  this.getList()
        },
        //导出
        async onExport(){
          this.filter.startTime = null;
          this.filter.endTime = null;
          this.filter.startTime2 = null;
          this.filter.endTime2 = null;
          if (this.filter.daterange1) {
              this.filter.startTime = this.filter.daterange1[0];
              this.filter.endTime = this.filter.daterange1[1];
          }
          if (this.filter.daterange2) {
              this.filter.startTime2 = this.filter.daterange2[0];
              this.filter.endTime2 = this.filter.daterange2[1];
          }
          var that = this;
          var pager = this.$refs.pager.getPager();
          const params = { ...pager, ...this.pager, ...this.filter };
          this.exportLoading = true;
          var res = await ExportTempWareHouseSalary(params);
          this.exportLoading = false;
          if (!res?.data) {
            return false;
          };
          const aLink = document.createElement("a");
          let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
          aLink.href = URL.createObjectURL(blob)
          aLink.setAttribute('download', '临时仓库薪资导出' + new Date().toLocaleString() + '_.xlsx')
          aLink.click()
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
