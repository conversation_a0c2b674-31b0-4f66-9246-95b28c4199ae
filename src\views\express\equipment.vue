<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <div>
                    <el-button type="primary" @click="getList('search')">查询</el-button>
                    <el-button type="primary" @click="batcHupgrade">批量升级</el-button>
                    <el-button type="primary" @click="upgradeFlag">升级列表</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @select="selectChange" @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
            :height="'100%'">
            <template slot="right" width="100">
                <vxe-column title="操作">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="openLogs(row)">日志</el-button>
                            <el-button type="text" @click="handleEdit(row)">编辑</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="日志" :visible.sync="logVisible" width="80%" height="600px" :append-to-body="true" v-dialogDrag>
            <equipmentLogs :deviceIp="deviceIp" :macAddress="macAddress"   v-if="logVisible" />
        </el-dialog>

        <el-dialog title="编辑设备" :visible.sync="editVisible" width="30%" v-dialogDrag>
            <el-form :model="editForm" label-width="100px">
                <el-form-item label="设备编号">
                    <el-input v-model="editForm.deviceId" placeholder="请输入设备编号"></el-input>
                </el-form-item>
                <el-form-item label="设备IP">
                    <el-input v-model="editForm.ip" placeholder="请输入设备IP"></el-input>
                </el-form-item>
            </el-form>
            <div class="btnGroup">
                <el-button @click="editVisible = false">取消</el-button>
                <el-button type="primary" @click="submitEdit">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog :title="importDialogTitle" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <el-input v-model="configCommand" placeholder="请输入配置指令" style="width: 210px;"></el-input>
            <el-button type="primary" @click="sendConfigCommand" style="margin-left: 15px;">发送配置指令</el-button><br><br>
            <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                :on-remove="removeFile" :file-list="fileList" accept=".hex,.bin" :http-request="uploadFile">
                <el-tooltip class="item" effect="dark" content="只能上传一个hex文件或者bin文件" placement="top-start">
                    <el-button size="small" type="primary">点击上传</el-button>
                </el-tooltip>
            </el-upload>
            <fieldset style="height: 100px;width: 300px;overflow: auto">
                <legend>设备编号:</legend>
                <div v-if="selectList && selectList?.length > 0" style="display: flex;flex-wrap: wrap;">
                    <span v-for="item in selectList" style="margin-right: 10px;">{{ item.deviceId }}</span>
                </div>
            </fieldset>
            <el-progress :percentage="process"></el-progress>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>
        <upgradeListDialog :isupgrade.sync="isupgrade" :visible.sync="upgradeListVisible" :title="importDialogTitle" @sendConfigCommand="sendConfigCommand" @close="handleUpgradeListClose"/>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import {collectingEquipmentPage, sendStartUpgrade, sendOrder, collectingEquipmentSubmit} from '@/api/order/orderData'
import request from '@/utils/request'
import middlevue from "@/store/middle.js"
import equipmentLogs from './equipmentLogs.vue'
import upgradeListDialog from './upgradeListDialog.vue'
const tableCols = [
    { type: 'checkbox', label: '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'deviceIp', label: '设备硬件地址', formatter: (row) => row.deviceIp.replace('\u0000', '') },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'ip', label: '集包台IP', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'deviceId', label: '(集包台)设备编号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'version', label: '设备软件版本', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'updateTime', label: '最后升级时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'status', label: '状态', formatter: (row) => {
      if (row.status == "1") {
        return '在线'
      }
      return '离线'
    }},
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'finishTime', label: '最后升级完成时间', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, equipmentLogs, upgradeListDialog
    },
    data() {
        return {
            upgradeListVisible: false,
            isupgrade: false,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            selectList: [],
            logVisible: false,
            fileList: [],
            importLoading: false,
            importVisible: false,
            file: null,
            process: 0,
            logVisible: false,
            configCommand: '',
            editVisible: false,
            deviceId: null,
            editForm: {
                deviceId: '',
                deviceIp: '',
                id: null,
                ip: '',
            },
            importDialogTitle: '批量升级',
        }
    },
    async mounted() {
        await this.getList()
        middlevue.$on('electricUploadHexProcess', (data) => {
            if (JSON.parse(data)[0].process == 100) {
                this.$message.success('升级成功')
            }
            this.$set(this, 'process', JSON.parse(data)[0].process)
        })
    },
    beforeDestroy() {
        middlevue.$off('electricUploadHexProcess')
    },
    methods: {
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            //没有时间就提示
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData();
            const res = this.selectList.map(item => item.ip)
            
            form.append("dexFile", this.file);
            form.append("ips", res);
            form.append("version", this.selectList[0].version);
            this.importLoading = true
            await sendStartUpgrade(form).then(({ success }) => {
                if (success) {
                    this.$message.success('正在升级,请稍等')
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('升级失败')
                this.$set(this, 'process', 0)
            })
        },
        handleUpgradeListClose() {
          this.upgradeListVisible = false;
        },
        upgradeFlag () {
            this.isupgrade = false
            this.upgradeListVisible = !this.upgradeListVisible
            this.importDialogTitle = '升级列表'
        },
        batcHupgrade() {
            if (this.selectList?.length == 0) return this.$message.warning('请选择要升级的设备')
            this.process = 0
            this.fileList = []
            this.file = null
            this.isupgrade = true
            this.upgradeListVisible = true
            this.importDialogTitle = '批量升级'
        },
        removeFile(file, fileList) {
            this.file = null
        },
        async openLogs(row) {
            this.deviceIp = row.ip
            this.macAddress = row.deviceIp
            this.logVisible = true
        },
        selectChange(val) {
            this.selectList = val
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await collectingEquipmentPage(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
      async sendConfigCommand(configCommand) {
        // if (!this.configCommand) {
        //   this.$message.error('请输入配置指令');
        //   return;
        // }
        const [fileName, version] = configCommand.split(',');
        const res = this.selectList.map(item => item.ip)
        this.$message.info('正在发送指令,请稍后...')
        const form = new FormData();
        form.append("order", fileName);
        form.append("ips", res.join(","));
        form.append("version", version);
        try {
          const response = await sendOrder(form);
          if (response.success) {
            this.$message.success('配置指令发送成功');
          } else {
            this.$message.error('配置指令发送失败');
          }
        } catch (error) {
          this.$message.error('配置指令发送失败');
        }
      },
      async handleEdit(row) {
          this.editForm.deviceId = row.deviceId
          this.editForm.deviceIp = row.deviceIp
          this.editForm.id = row.id
          this.editForm.ip = row.ip
          this.editVisible = true
      },
      async submitEdit() {
          if (!this.editForm.deviceId) {
              this.$message.error('请输入设备编号')
              return
          }
          try {

            const { data, success } = await collectingEquipmentSubmit(this.editForm);
              if (success) {
                  this.$message.success('修改成功')
                  this.editVisible = false
                  this.getList()
              } else {
                  this.$message.error(response.msg || '修改失败')
              }
          } catch (error) {
              this.$message.error('修改失败')
          }
      },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>
