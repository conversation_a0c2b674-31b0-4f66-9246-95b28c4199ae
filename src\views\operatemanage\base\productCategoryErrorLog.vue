<template>
  <container>
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        筛选：
        <el-form-item >
          <el-date-picker style="width: 230px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
            :picker-options="pickerOptions"></el-date-picker>
        </el-form-item>
        <el-form-item >
        <el-select filterable v-model="filter.platform" placeholder="平台" clearable
            style="width: 80px">
            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <inputYunhan title="产品ID" :row="inputrow" :maxRows="1000" :inputshow="inputshow" :clearable="true" placeholder="宝贝ID"
            @callback="callback1" :inputt.sync="filter.productIds"></inputYunhan>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        同步：
        <el-form-item>
          <el-select filterable v-model="syncFilter.platform" placeholder="平台" @change="onchangeplatform" clearable
            style="width: 80px">
            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select filterable v-model="syncFilter.shopCode" placeholder="店铺" clearable style="width: 130px">
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <inputYunhan title="产品ID" :row="inputrow" :maxRows="1000" :inputshow="inputshow" :clearable="true" placeholder="同步宝贝ID"
            @callback="callback" :inputt.sync="syncFilter.proCodes"></inputYunhan>
        </el-form-item>
        <el-tooltip class="item" effect="dark" content="只对左侧筛选有效【平台、店铺、同步宝贝ID】" placement="right">
          <el-button type="warning" @click="onSync">同步产品类目</el-button>
        </el-tooltip>
        <el-tooltip class="item" effect="dark"  content="只对左侧筛选有效【平台：京东、淘宝、天猫、快手】" placement="right">
          <el-button type="warning" @click="onSyncShop">同步店铺平台ID</el-button>
        </el-tooltip>
      </el-form>
    </template>

    <vxetablebase :id="'productcategoryerrorlog20241103'" ref="table" :that='that' :isIndex='true'
      @sortchange='sortchange' :tableData='list' :tableCols='tableCols' :loading="listLoading" :isSelectColumn="false">
    </vxetablebase>

    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>
  </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getProductCategorySyncLog, syncProNoCategoryByFilter, syncPlatformShopIdByWuHanShop } from '@/api/operatemanage/base/category.js'
import { formatTime, formatPlatform, platformlist } from "@/utils/tools";
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import inputYunhan from '@/components/Comm/inputYunhan.vue'

const tableCols = [
  { istrue: true, prop: 'platform', label: '平台', width: '60', sortable: 'custom', formatter: row => formatPlatform(row.platform) },
  { istrue: true, prop: 'shopName', label: '店铺', width: '200', sortable: 'custom', },
  { istrue: true, prop: 'productCode', label: '产品ID', tipmesg: '', width: '120', sortable: 'custom', },
  { istrue: true, prop: 'errorTime', label: '同步时间', tipmesg: '同步时间', width: '120', sortable: 'custom', formatter: (row) => formatTime(row.errorTime, "YYYY-MM-DD HH:mm:ss") },
  { istrue: true, prop: 'errorMsg', label: '错误原因', tipmesg: '',  sortable: 'custom', }
]

const tableHandles = [
  //{ label: "导入", handle: (that) => that.startImport() }
];

export default {
  name: 'YunHanAdminProductCategoryErrorLog',
  components: { container, vxetablebase, MyConfirmButton, inputYunhan },
  data() {
    return {
      buscharDialog: { visible: false, title: "", data: [] },
      that: this, 
      filter: {
        startTime: null,
        endTime: null,
        timerange: [],
        productIds: null,
        platform:null
      },
      syncFilter: {
        platform: null,
        shopCode: null,
        proCodes: null
      },
      list: [],
      pager: { OrderBy: "productCode", IsAsc: false },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      pickOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      listLoading: false,
      summaryarry: {},
      inputshow: 0,
      inputrow: 12,
      shopList: [],
      platformlist: platformlist,
      sels: [],
    };
  },

  async mounted() {
    //await this.onSearch()
  },

  methods: {
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    async onchangeplatform(val) {
      this.syncFilter.platform = val;
      const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 }); 
      this.shopList = res1.data.list 
    },
    async getlist() {
      if (this.pager.OrderBy == null) {
        this.pager.OrderBy = "date";
        this.pager.IsAsc = false;
      }
      let pager = this.$refs.pager.getPager();
      let page = this.pager;
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      const params = { ...pager, ...page, ... this.filter }
      if (params === false) {
        return;
      }
      this.listLoading = true
      const res = await getProductCategorySyncLog(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.list = data 
    },
    async nSearch() {
      await this.getlist()
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    },
    async onSync() {
      if(this.syncFilter.platform == null){
        this.$message({ message: "请选择平台", type: "error" });
        return;
      }
      this.$confirm('是否确认同步产品？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let params = this.syncFilter;
        const res = await syncProNoCategoryByFilter(params)
        if (res.success) {
          this.$message({ message: "正在同步中,请稍后查看同步结果...", type: "success" });
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    },
    async onSyncShop(){
      if(this.syncFilter.platform == null){
        this.$message({ message: "请选择平台", type: "error" });
        return;
      }
      this.$confirm('是否确认同步店铺？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let params = this.syncFilter;
        const res = await syncPlatformShopIdByWuHanShop(params)
        if (res.success) {
          this.$message({ message: "正在同步中,请稍后查看同步结果...", type: "success" });
        }  
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    },
    //批量数据组合，回调数据
    async callback(val) {
      this.syncFilter.proCodes = val
    },
    async callback1(val) {
      this.filter.productIds = val
    },
  }
};
</script>

<style lang="scss" scoped></style>
