<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                        start-placeholder="支付开始日期" end-placeholder="支付结束日期" :picker-options="pickerOptions" clearable
                        style="width: 280px;" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                    </el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-input v-model.trim="filter.deliveryCity" style="width: 240px" :maxlength="40" placeholder="发货城市"
                        clearable />
                </el-button>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-input v-model.trim="filter.receiverCity" style="width: 240px" :maxlength="40" placeholder="收货城市"
                        clearable />
                </el-button>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-input v-model.trim="filter.expressCompanyName" style="width: 240px" :maxlength="40"
                        placeholder="快递公司" clearable />
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onExport">导出</el-button>
            </el-button-group>
        </template>

        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :border='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :showsummary='true' :summaryarry='summaryarry' :isSelectColumn="false" style="width: 100%;  margin: 0"
            :treeProp="{ rowField: 'id', parentField: 'parentId' }" v-loading="listLoading" :height="'100%'">
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </my-container>
</template>
<script>
import {
    getExpressRoutePageList, exportExpressRoute
} from '@/api/express/expressdataanalyse'
import vxetablebase from "@/components/VxeTable/vxetablebase.vue";
import { formatTime } from "@/utils";
import { pickerOptions } from '@/utils/tools'
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import dayjs from "dayjs";
const tableCols = [
    { istrue: true, prop: 'deliveryCity', label: '发货城市', width: '200', sortable: 'custom', treeNode: true },
    { istrue: true, prop: 'receiverCity', label: '收货城市', width: '200', sortable: 'custom', treeNode: true },
    { istrue: true, prop: 'expressCompanyName', label: '物流公司', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'orderNoInnerCount', label: '订单数', width: '200', sortable: 'custom' },
    {
        istrue: true, prop: 'orderNoInnerCountCityRate', label: '同城市订单占比', width: '200', sortable: 'custom',
        formatter: (row) => (row.orderNoInnerCountCityRate != null ? (row.orderNoInnerCountCityRate + "%") : null)
    },
    { istrue: true, prop: 'zhiFu_LanShou_Hour', label: '支付-揽收时长(h)', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'lanShou_SongDa_Hour', label: '揽收-签收时长(h)', width: '200', sortable: 'custom' },
];
export default {
    name: "expressroute",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, vxetablebase },
    data() {
        return {
            that: this,
            filter: {
                timerange: [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().subtract(1, 'day').format('YYYY-MM-DD')],
                startDate: null,
                endDate: null,
                deliveryCity: null,
                receiverCity: null,
                expressCompanyName: null,
            },
            tableData: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            pickerOptions,

        };
    },
    async mounted() {
    },
    methods: {
        getParam() {
            if (this.filter.timerange && this.filter.timerange.length == 2) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.$message({ message: "请选择日期", type: "warning", });
                return false;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para
            };

            return params;
        },
        async getList() {
            const params = this.getParam();
            if (params == false)
                return;
            this.listLoading = true;
            const res = await getExpressRoutePageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.tableData = res.data.list;
            // this.summaryarry = res.data.summary;

            let summary = res.data.summary || {}

            const resultsum = {};
            Object.entries(summary).forEach(([key, value]) => {
                resultsum[key] = formatNumber(value);
            });
            function formatNumber(number) {
                const options = {
                    useGrouping: true,
                };
                return new Intl.NumberFormat('zh-CN', options).format(number);
            }
            this.summaryarry = resultsum
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getList();
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async onExport() {
            const params = this.getParam();
            if (params == false)
                return;
            this.listLoading = true
            await exportExpressRoute(params).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '快递线路' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.listLoading = false
                }
            }).catch(() => {
                this.listLoading = false
            })
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
