<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.ShopName" clearable maxlength="100" placeholder="店铺" class="publicCss" />
        <el-input v-model.trim="ListInfo.OrderNo" clearable maxlength="100" placeholder="订单号" class="publicCss" />
        <el-input v-model.trim="ListInfo.TransactionType" clearable maxlength="100" placeholder="交易类型" class="publicCss" />
        <el-input v-model.trim="ListInfo.PaymentAccountType" clearable maxlength="100" placeholder="收款账户类型"
          class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
      </div>
    </template>
    <vxetablebase :id="'alipayWeChatDetails202412051608'" :tablekey="'alipayWeChatDetails202412051608'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-row>
          <el-col :xs="4" :sm="6" :md="8" :lg="6">

            <el-date-picker style="width: 100%" v-model="YearMonthDay" type="date" format="yyyyMMdd"
              value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
          </el-col>
          <el-col :xs="8" :sm="6" :md="12" :lg="12">
            <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
              accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
              :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
              <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
            </el-upload>
          </el-col>
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { importPayWeChat_RedBookAsync, getPayWeChat_RedBook } from '@/api/bookkeeper/reportdayV2'
import { pickerOptions, formatTime } from '@/utils/tools'
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'yearMonthDayDate', label: '日期', formatter: (row) => formatTime(row.yearMonthDayDate, 'YYYY-MM-DD') },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'entryTime', label: '入账时间', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'transactionType', label: '交易类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '订单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'paymentAccountType', label: '收款账户类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'incomeAmount', label: '收入', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'expenseAmount', label: '支出', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'accountBalance', label: '账户余额', },
]
export default {
  name: "alipayWeChatDetails",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        ShopName: null,//店铺
        OrderNo: null,//订单号
        TransactionType: null,//交易类型
        PaymentAccountType: null,//收款账户类型
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      YearMonthDay: null,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("YearMonthDay", this.YearMonthDay);
      var res = await importPayWeChat_RedBookAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给昨天时间
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      const { data, success } = await getPayWeChat_RedBook(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 120px;
    margin-right: 5px;
  }
}
</style>
