<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button  style="padding:0;margin:0;" >
                    <el-date-picker v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width:230px;">
                    </el-date-picker>
                </el-button>
                 <el-button  style="padding:0;margin:0;" >
                  <el-select v-model="filter.platform" placeholder="请选择平台" clearable filterable style="width: 100%">
                    <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
                  </el-select>
                 </el-button>
                 <el-button  style="padding:0;margin:0;" >
                   <el-input v-model.trim="filter.giftRuleNo" placeholder="赠品规则" :maxlength="50" clearable></el-input>
                 </el-button>
                <el-button  style="padding:0;margin:0;" >
                  <el-input v-model.trim="filter.proCode" placeholder="商品ID" :maxlength="50" clearable></el-input>
                </el-button>
                <el-button  style="padding:0;margin:0;">
                  <el-input v-model.trim="filter.goodsCode" placeholder="商品编码" :clearable="true" :maxlength="50"></el-input>
                </el-button>
                <el-button  style="padding:0;margin:0;">
                  <el-input v-model.trim="filter.orderNo" placeholder="原始订单号" :clearable="true" :maxlength="50"></el-input>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
            </el-button-group>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn="false"  
          :showsummary='true' :tablefixed='false' :summaryarry='summaryarry' :tableData='tableData' 
          :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:100%;margin: 0">
        </ces-table>
        <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
      </template>
   </container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import container from '@/components/my-container';
import cesTable from '@/components/Table/table.vue';
import {formatLinkProCode,platformlist} from '@/utils/tools'
import { pageOrderGitDetail } from '@/api/order/ordergoods';

const tableCols =[
    {istrue:true,prop:'yearMonthDay',label:'年月日',sortable:'custom', width:'80'},
    {istrue:true,prop:'orderNo',label:'原始订单号',sortable:'custom', width:'160'},
    {istrue:true,prop:'goodsCode',label:'赠品编码',sortable:'custom', width:'100'},
    {istrue:true,prop:'proCode',label:'产品ID', width:'120',sortable:'custom',type:'html',formatter:row=>formatLinkProCode(row.platform,row.proCode)},
    {istrue:true,prop:'giftRuleNo',label:'赠品规则', width:'80',sortable:'custom'},
    {istrue:true,prop:'goodsPrice',label:'成本价', width:'70',sortable:'custom'},
    {istrue:true,prop:'qty',label:'数量', width:'60',sortable:'custom'},
    {istrue:true,prop:'giftAmont',label:'赠品成本', width:'80',sortable:'custom'},
    {istrue:true,prop:'updateTime',label:'更新时间', width:'150',sortable:'custom'},
    {istrue:true,prop:'createdTime',label:'创建时间', width:'150',sortable:'custom'},
];

const startDate = formatTime(dayjs().subtract(15,'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default ({
    name:"Users",
    components:{container,cesTable},
    data(){
        return {
            that:this, 
            filter:{
                timerange:[startDate, endDate],
                yearMonthDayStart:null,
                yearMonthDayEnd:null,
                orderNo:null,
                goodsCode:null,
                platform:null,
                proCode:null,
                giftRuleNo:null,
            },
            platformlist:platformlist,
            tableCols:tableCols,
            tableHandles:null,
            tableData:[],
            total: 0,
            pager:{OrderBy:" YearMonthDay ",IsAsc:false},
            listLoading: false,
            pageLoading: false,
            summaryarry:{},    
            sels:[],
        };
    },
    methods:{
         async onShow(yearMonthDayStart,yearMonthDayEnd,proCode)  {
            this.filter.timerange=[];
            this.filter.timerange[0]=yearMonthDayStart.substr(0,4)+'-'+yearMonthDayStart.substr(4,2)+'-'+yearMonthDayStart.substr(6,2)
            this.filter.timerange[1]=yearMonthDayEnd.substr(0,4)+'-'+yearMonthDayEnd.substr(4,2)+'-'+yearMonthDayEnd.substr(6,2)
            this.filter.proCode=proCode;
            await this.getList();
        },
        async onSearch()  {
          await this.getList();
        },
        async sortchange(column){
            if(!column.order)
                this.pager={};
            else
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
            this.$refs.pager.setPage(1);
            await this.onSearch();            
        },
        async getList(){
            this.filter.yearMonthDayStart =null;
            this.filter.yearMonthDayEnd =null;
            if (this.filter.timerange && this.filter.timerange.length>0) {
                this.filter.yearMonthDayStart = this.filter.timerange[0].replace('-','').replace('-','');
                this.filter.yearMonthDayEnd = this.filter.timerange[1].replace('-','').replace('-','');
            } 
            var that=this;
            this.listLoading=true;
            var pager = this.$refs.pager.getPager();
            const params = {...pager,...this.pager,...this.filter,...this.myfilter};
            const res = await pageOrderGitDetail(params).then(res=>{
                that.total = res.data?.total;
                that.tableData = res.data?.list;
                that.summaryarry=res.data?.summary;
            });
            this.listLoading=false;
        },
    }
})
</script>
<style scoped>
    ::v-deep .el-table__fixed-footer-wrapper tbody td{
        color:blue;
    }
</style>

