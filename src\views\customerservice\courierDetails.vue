<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="addTimeRanges" type="daterange" unlink-panels range-separator="至"
                    :clearable="false" start-placeholder="开始添加日期" end-placeholder="结束添加日期"
                    :picker-options="pickerOptions" style="width: 270px;margin-right: 10px;"
                    :value-format="'yyyy-MM-dd'" @change="changeTime($event, 'add')">
                </el-date-picker>
                <el-date-picker v-model="rebotNoticeTime" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="机器人开始通知日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 310px;margin-right: 10px;" :value-format="'yyyy-MM-dd'"
                    @change="changeTime($event, 'notice')">
                </el-date-picker>
                <el-date-picker v-model="rebotOperateTime" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="机器人开始操作日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 310px;margin-right: 10px;" :value-format="'yyyy-MM-dd'"
                    @change="changeTime($event, 'operate')">
                </el-date-picker>
                <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable>
                    <el-option label="取消拦截" :value="-2" />
                    <el-option label="匹配快递公司失败" :value="-1" />
                    <el-option label="待通知" :value="0" />
                    <el-option label="已通知" :value="1" />
                </el-select>
                <el-select v-model="ListInfo.expressCompanyNameList" placeholder="快递公司"
                    style="width: 270px;margin-right: 5px;" clearable collapse-tags multiple filterable>
                    <el-option label="无快递公司" value="无快递公司" />
                    <el-option v-for="item in kdCompany" :key="item" :label="item" :value="item" />
                </el-select>
                <el-select v-model="ListInfo.scanStatus" placeholder="是否扫码退件" class="publicCss" clearable>
                    <el-option label="否" :value="0" />
                    <el-option label="是" :value="1" />
                </el-select>
                <div style="display: flex;margin-top: 10px;">
                    <el-select v-model="ListInfo.scanStatusResult" placeholder="扫码退件结果" class="publicCss" clearable>
                        <el-option v-for="item in scanStatusResult" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                    <inputYunhan ref="productCode1" :inputt.sync="ListInfo.expressNo" v-model="ListInfo.expressNo"
                        class="publicCss" placeholder="物流单号/多条请按回车" :clearable="true" :clearabletext="true"
                        :maxRows="300" :maxlength="6000" @callback="productCodeCallback" title="物流单号">
                    </inputYunhan>
                    <inputYunhan ref="productCode2" :inputt.sync="ListInfo.orderNoInner" v-model="ListInfo.orderNoInner"
                        class="publicCss" placeholder="内部订单号/多条请按回车" :clearable="true" :clearabletext="true"
                        :maxRows="300" :maxlength="6000" @callback="orderNoInnerBack" title="内部订单号">
                    </inputYunhan>
                    <inputYunhan ref="productCode3" :inputt.sync="ListInfo.newOrderNoInner"
                        v-model="ListInfo.newOrderNoInner" class="publicCss" placeholder="新内部订单号/多条请按回车"
                        :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
                        @callback="newOrderNoInnerBack" title="内部订单号">
                    </inputYunhan>
                    <el-input v-model.trim="ListInfo.addUserName" placeholder="添加人" maxlength="50" clearable
                        class="publicCss" />
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="downLoadFile">下载模版</el-button>
                    <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                    <el-button type="primary" @click="importProps">导入</el-button>
                    <el-button type="primary" @click="setTzTime">设置通知时间</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <!-- 导入数据 -->
        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                    <el-button size="small" type="primary">点击上传</el-button>
                </el-tooltip>
            </el-upload>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>

        <!-- 设置通知时间 -->
        <el-dialog title="设置通知时间" :visible.sync="setTzTimeVisible" width="80%" v-dialogDrag @close="onClosesetTzTime"
            v-loading="setTzTimeLoading">
            <div style="display: flex;">
                <el-select v-model="formData.expressCompanyName" placeholder="快递公司"
                    style="width: 270px;margin-right: 5px;" filterable clearable>
                    <el-option v-for="item in kdCompany" :key="item" :label="item" :value="item" />
                </el-select>
                <el-button type="primary" @click="getSetNoticeTimeProps(formData.expressCompanyName)"
                    style="margin-left: 10px;">搜索</el-button>

                <div style="margin-left: 100px;">
                    <el-button type="primary" @click="addSetNoticeCom()"
                        v-show="!addSetNoticeComShow">添加快递公司</el-button>
                    <el-select v-model="addSetNoticeComSel" placeholder="快递公司" filterable multiple collapse-tags
                        v-show="addSetNoticeComShow" style="width: 400px;">
                        <el-option v-for="item in kdCompany" :key="item" :label="item" :value="item" />
                    </el-select>
                    <el-button type="primary" @click="addSetNoticeComSave()"
                        v-show="addSetNoticeComShow">确认添加</el-button>
                </div>

            </div>
            <el-table :data="formData.setTableData" style="width: 100%" max-height="600">
                <el-table-column prop="date" label="快递公司" width="250">
                    <template #default="{ row }">
                        <el-select v-model="row.expressCompanyName" placeholder="快递公司" filterable disabled
                            style="width: 240px;">
                            <el-option v-for="item in kdCompany" :key="item" :label="item" :value="item" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column prop="workTime" label="工作时间">
                    <template #default="{ row, $index }">
                        <div style="display: flex;">
                            <el-time-select placeholder="起始时间" v-model="row.workStartTime" :picker-options="{
                                start: '00:00',
                                step: '00:15',
                                end: '23:45'
                            }">
                            </el-time-select>
                            <el-time-select placeholder="结束时间" v-model="row.workEndTime" :picker-options="{
                                start: '00:00',
                                step: '00:15',
                                end: '23:45',
                                minTime: row.workStartTime
                            }">
                            </el-time-select>
                            <el-button type="text" @click="batchEditProps($index, 'work')">批量</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="address" label="通知间隔" width="160">
                    <template #default="{ row, $index }">
                        <div style="display: flex;">
                            <el-time-select v-model="row.noticeIntervalTimeStr" :picker-options="{
                                start: '00:30',
                                step: '00:30',
                                end: '48:00'
                            }" placeholder="通知间隔">
                            </el-time-select>
                            <el-button type="text"
                                @click="batchEditProps(row.noticeIntervalTimeStr, 'tz')">批量</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="address" label="订单成本-拦截标准" width="180">
                    <template #header>
                        <span>订单成本-拦截标准</span>
                        <el-tooltip class="item" effect="dark" content='订单成本>=此值时才拦截，设置空或0为全部拦截' placement="top-end">
                            <i class="el-icon-question"></i>
                        </el-tooltip>
                    </template>
                    <template #default="{ row, $index }">
                        <div style="display: flex;">
                            <el-input-number v-model="row.orderCost" placeholder="订单成本-拦截标准" :precision="2" :step="1"
                                :max="9999.99" :min="-9999.99" clearable class="publicCss"></el-input-number>
                            <el-button type="text" @click="batchEditProps(row.orderCost, 'cost')">批量</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="address" label="操作网址" width="120">
                    <template #default="{ row, $index }">
                        <el-input v-model="row.operateUrl" placeholder="操作网址" maxlength="400" clearable
                            class="publicCss" />
                    </template>
                </el-table-column>
                <el-table-column prop="address" label="操作账号" width="120">
                    <template #default="{ row, $index }">
                        <el-input v-model="row.operateAccount" placeholder="操作账号" maxlength="100" clearable
                            class="publicCss" />
                    </template>
                </el-table-column>
                <el-table-column prop="address" label="操作密码" width="120">
                    <template #default="{ row, $index }">
                        <el-input v-model="row.operatePassword" placeholder="操作密码" maxlength="100" clearable
                            class="publicCss" />
                    </template>
                </el-table-column>
                <el-table-column prop="actualPayAmountIsShow" label="是否显示支付金额" width="140">
                    <template #default="{ row, $index }">
                        <el-select v-model="row.actualPayAmountIsShow" placeholder="是否显示支付金额" clearable
                            class="publicCss">
                            <el-option label="是" :value=1 />
                            <el-option label="否" :value=0 />
                        </el-select>
                    </template>
                </el-table-column>

                <el-table-column lable="操作" width="80">
                    <template slot-scope="scope">
                        <el-button type="danger" @click="onDelCom(scope.row, scope.$index)">删除<i
                                class="el-icon-remove-outline"></i>
                        </el-button>
                    </template>
                </el-table-column>

            </el-table>
            <div class="btnGroup">
                <el-button @click="setTzTimeVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbitSetTime" v-throttle="1000">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import inputYunhan from "@/components/Comm/inputYunhan";
import {
    GetExpressInterceptPageList,
    ImporExpressInterceptAsync,
    ExportExpressInterceptList,
    GetExpressCompanyNameList,
    GetExpressInterceptSetNoticeList,
    SaveExpressInterceptSetNotice, DeleteExpressInterceptSetNotice, CancelExpressIntercept
} from '@/api/customerservice/expressIntercept'
import { re } from 'mathjs';

const scanStatusResult = [
    { label: '无法识别', value: 0 },
    { label: '匹配成功', value: 1 },
    { label: '待再匹配', value: 2 },
    { label: '拆包', value: 3 },
    { label: '丢弃', value: 4 },
]
const tableCols = [
    { sortable: 'custom', width: '140', align: 'center', prop: 'createdTime', label: '创建时间', },
    {
        sortable: 'custom', width: '140', align: 'center', prop: 'status', label: '状态',
        formatter: (row) => (row.status == 0 ? '待通知' : (row.status == -1 ? '匹配快递公司失败' : (row.status == -2 ? '取消拦截' : '已通知')))
    },
    { sortable: 'custom', width: '140', align: 'center', prop: 'expressNo', label: '物流单号', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'expressCompanyName', label: '快递公司', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'orderNoInner', label: '内部订单号', type: 'orderLogInfo', orderType: 'orderNoInner' },
    { sortable: 'custom', width: '80', align: 'center', prop: 'actualPayAmount', label: '支付金额', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'orderCost', label: '订单成本', },
    { sortable: 'custom', width: '140', align: 'center', prop: 'addTime', label: '添加时间', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'addUserName', label: '添加人', },
    { sortable: 'custom', width: '140', align: 'center', prop: 'noticeTime', label: '通知时间', },
    { sortable: 'custom', width: '140', align: 'center', prop: 'operateTime', label: '机器人操作时间', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'scanStatus', label: '是否扫码退件', formatter: (row) => row.scanStatus == 0 ? '否' : '是' },
    { sortable: 'custom', width: '80', align: 'center', prop: 'scanStatusResult', label: '扫码退件结果', formatter: (row) => row.scanStatusResult >= 0 ? scanStatusResult.find(item => item.value == row.scanStatusResult)?.label : null },
    { sortable: 'custom', width: '120', label: '新内部订单号', prop: 'newOrderNoInner', type: 'orderLogInfo', orderType: 'orderNoInner' },
    { sortable: 'custom', width: '400', label: '收件人信息', prop: 'expressReceiveInfo' },
    {
        istrue: true, type: 'button', label: '操作', width: '120', fixed: 'right', align: 'center',
        btnList: [
            { label: "取消拦截", display: (row) => { return (row.status != 0 && row.status != -1); }, handle: (that, row) => that.onQuXiao(row) },
            { label: "日志", handle: (that, row) => that.onLog(row) },
        ]
    },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, inputYunhan
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startAddTime: null,//添加开始时间
                endAddTime: null,//添加结束时间
                startNoticeTime: null,//通知开始时间
                endNoticeTime: null,//通知结束时间
                startOperateTime: null,//操作开始时间
                endOperateTime: null,//操作结束时间
                orderNoInner: null,//内部订单号
                orderNoInnerList: [],//内部订单号列表
                newOrderNoInner: null,//新内部订单号
                newOrderNoInnerList: [],//新内部订单号列表
                addUserName: null,//添加人
            },
            scanStatusResult: scanStatusResult,
            timeRanges: [],
            rebotNoticeTime: [],
            rebotOperateTime: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            isExport: false,
            importVisible: false,
            file: null,
            setTzTimeVisible: false,
            setTzTimeLoading: false,
            formData: {
                expressCompanyName: null,//快递公司
                setTableData: [
                    {
                        expressCompanyName: null,//快递公司
                        workStartTime: null,//开始时间
                        workEndTime: null,// 结束时间
                        noticeIntervalTime: null,//通知间隔时间
                        operateUrl: null,//操作地址
                        operateAccount: null,//操作账号
                        operatePassword: null,//操作密码
                        noticeIntervalTimeStr: null,//通知间隔时间
                    }
                ],//批量设置通知时间
            },
            importLoading: false,
            fileList: [],
            addTimeRanges: [],
            kdCompany: [],//快递公司

            addSetNoticeComSel: [],
            addSetNoticeComShow: false,
        }
    },
    async mounted() {
        await this.getKdCompany()
        await this.getList()
    },
    methods: {
        async getKdCompany() {
            const { data, success } = await GetExpressCompanyNameList()
            if (success) {
                this.kdCompany = data
            }
        },
        downLoadFile() {
            window.open("../../static/excel/customerservice/快递拦截导入模板.xlsx", "_self");
        },
        async sumbitSetTime() {
            this.formData.setTableData.forEach((item, i) => {
                if ((item.workStartTime && !item.workEndTime) || (!item.workStartTime && item.workEndTime)) {
                    this.$message.error(`第${i + 1}行通知间隔,工作开始时间/工作结束时间/通知间隔必须同时存在`)
                    throw new Error(`第${i + 1}行通知间隔,工作开始时间/工作结束时间/通知间隔必须同时存在`)
                }
                if ((item.workStartTime && item.workEndTime) && !item.noticeIntervalTimeStr) {
                    this.$message.error(`第${i + 1}行通知间隔,工作开始时间/工作结束时间/通知间隔必须同时存在`)
                    throw new Error(`第${i + 1}行通知间隔,工作开始时间/工作结束时间/通知间隔必须同时存在`)
                }
                if (!(item.workStartTime && item.workEndTime) && item.noticeIntervalTimeStr) {
                    this.$message.error(`第${i + 1}行通知间隔,工作开始时间和工作结束时间必须同时存在`)
                    throw new Error(`第${i + 1}行通知间隔,工作开始时间和工作结束时间必须同时存在`)
                }
                // if (item.noticeIntervalTimeStr && (item.operateUrl || item.operateAccount || item.operatePassword)) {
                //     this.$message.error(`第${i + 1}行通知间隔和操作不能同时存在`)
                //     throw new Error(`第${i + 1}通知间隔和操作不能同时存在`)
                // }
            })
            this.setTzTimeLoading = true;
            const { success } = await SaveExpressInterceptSetNotice(this.formData.setTableData);
            this.setTzTimeLoading = false;
            if (success) {
                this.$message({
                    message: '保存成功',
                    type: 'success'
                })
                this.formData.expressCompanyName = null
                this.setTzTimeVisible = false
            } else {
                // this.$message({
                //     message: '保存失败',
                //     type: 'error'
                // })
            }
        },
        //获取设置通知时间数据
        async getSetNoticeTimeProps(expressCompanyName) {
            const params = expressCompanyName ? { ...this.ListInfo, expressCompanyName } : this.ListInfo
            const { data, success } = await GetExpressInterceptSetNoticeList(params)
            if (success) {
                data.forEach(item => {
                    item.workStartTime = item.workStartTime ? dayjs(item.workStartTime).format('HH:mm') : ''
                    item.workEndTime = item.workEndTime ? dayjs(item.workEndTime).format('HH:mm') : ''

                    if (item.orderCost == null) {
                        item.orderCost = undefined
                    }
                })
                this.formData.setTableData = data
            }
        },
        //设置通知时间
        async setTzTime() {
            this.formData.expressCompanyName = null;
            this.setTzTimeVisible = true;
            this.setTzTimeLoading = true;
            await this.getSetNoticeTimeProps();
            this.setTzTimeLoading = false;
        },
        batchEditProps(e, type) {
            const map = {
                work: '工作时间',
                tz: '通知时间',
                cost: '订单成本-拦截标准'
            }
            this.$confirm(`此操作将批量操作${map[type]}, 是否继续?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.formData.setTableData.forEach((item, i) => {
                    if (type === 'work') {
                        item.workStartTime = this.formData.setTableData[e].workStartTime ? this.formData.setTableData[e].workStartTime : null
                        item.workEndTime = this.formData.setTableData[e].workEndTime ? this.formData.setTableData[e].workEndTime : null
                    }
                    else if (type === 'cost') {
                        item.orderCost = e
                    }
                    else {
                        item.noticeIntervalTimeStr = e
                    }
                })
                this.$message({
                    type: 'success',
                    message: '操作成功!'
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在上传中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            this.importLoading = true
            await ImporExpressInterceptAsync(form).then(({ success }) => {
                if (success) {
                    this.$message.success('上传成功，正在排队导入中...')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        async productCodeCallback(val) {
            this.ListInfo.expressNo = val;
        },
        orderNoInnerBack(val) {
            this.ListInfo.orderNoInner = val;
        },
        newOrderNoInnerBack(val) {
            this.ListInfo.newOrderNoInner = val;
        },
        async changeTime(e, type) {
            if (type == 'add') {
                this.ListInfo.startAddTime = e ? e[0] : null
                this.ListInfo.endAddTime = e ? e[1] : null
            } else if (type == 'notice') {
                this.ListInfo.startNoticeTime = e ? e[0] : null
                this.ListInfo.endNoticeTime = e ? e[1] : null
            } else {
                this.ListInfo.startOperateTime = e ? e[0] : null
                this.ListInfo.endOperateTime = e ? e[1] : null
            }
            await this.getList()
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await ExportExpressInterceptList(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '快递拦截明细' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            if (this.addTimeRanges && this.addTimeRanges.length == 0) {
                //默认给近7天时间
                this.ListInfo.startAddTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
                this.ListInfo.endAddTime = dayjs().format('YYYY-MM-DD')
                this.addTimeRanges = [this.ListInfo.startAddTime, this.ListInfo.endAddTime]
            }
            const replaceArr = ['expressNo', 'addUserName', 'orderNoInner']
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            try {
                const { data: { list, total }, success } = await GetExpressInterceptPageList(this.ListInfo)

                if (success) {
                    this.tableData = list
                    this.total = total
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.$message.error('获取列表失败')
            } finally {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        onClosesetTzTime() {
            this.addSetNoticeComSel = [];
            this.addSetNoticeComShow = false;
        },
        addSetNoticeCom() {
            //添加快递公司
            this.addSetNoticeComSel = [];
            this.addSetNoticeComShow = true;
        },
        addSetNoticeComSave() {
            //添加快递公司
            if (this.addSetNoticeComSel.length <= 0) {
                this.addSetNoticeComShow = false;
                return;
            }
            this.addSetNoticeComSel.forEach(f => {
                if (this.formData.setTableData.find(w => w.expressCompanyName == f) == null) {
                    this.formData.setTableData.unshift({
                        id: 0,
                        expressCompanyName: f,
                        workStartTime: null,
                        workEndTime: null,
                        noticeIntervalTimeStr: null,
                    });
                }
            });
            this.addSetNoticeComShow = false;
        },
        async onDelCom(row, index) {
            if (row.id) {
                this.$confirm('确定要删除吗?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const res = await DeleteExpressInterceptSetNotice({ id: row.id });
                    if (res?.success) {
                        this.formData.setTableData.splice(index, 1)
                        this.$message.success('删除成功')
                    }
                }).catch(() => {
                });
            }
            else {
                this.formData.setTableData.splice(index, 1)
                this.$message.error('删除成功')
            }
        },
        async onQuXiao(row) {
            this.$confirm('确定要取消拦截吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const res = await CancelExpressIntercept({ id: row.id, nickName: "" });
                if (res?.success) {
                    this.$message.success('取消拦截成功')
                    this.getList()
                }
            }).catch(() => {
            });
        },
        async onLog(row) {
            this.$showDialogform({
                path: `@/views/customerservice/courierDetailsLog.vue`,
                title: '操作日志',
                args: {
                    expressInterceptId: row.id,
                },
                height: '500px',
                width: '700px',
                callOk: this.afterSave
            });
        },
        afterSave() {

        },

    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
    align-items: center;

    .publicCss {
        width: 200px;
        margin: 0 10px 0 0;
        align-items: center;
    }
}

.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
</style>