<template>
    <my-container v-loading="pageLoading">
        <template #header>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
                :isSelectColumn="false" :loading="listLoading" :tableHandles="tableHandles1">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import cesTable from "@/components/Table/table.vue";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getWarehouseMonthSalaryUserSetPageList, } from '@/api/profit/warehousewages';
const tableCols = [
    { istrue: true, prop: 'userName', label: '姓名', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'yearMonth', label: '月份', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'mustWorkDays', label: '应出勤天数', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'monthSalary', label: '月薪', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'monthSalary2', label: '绩效', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'houseSubsidy', label: '房补', width: '60', sortable: 'custom' },
    { istrue: true, prop: 'eatSubsidy', label: '餐补', width: '60', sortable: 'custom' },
    { istrue: true, prop: 'nightSubsidy', label: '夜班补贴', width: '60', sortable: 'custom'},
    { istrue: true, prop: 'fullAttendance', label: '全勤', width: '60', sortable: 'custom' },
    { istrue: true, prop: 'modifiedTime', label: '操作时间', width: '150', sortable: 'custom' },
]
const tableHandles1 = [
    //{ label: "计算人效", handle: (that) => that.onCompute() },
    // { label: "导出", handle: (that) => that.onExport() },
];
export default {
    name: 'sumpackwages',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow },
    data() {
        return {
            that: this,
            filter: {
                id: 0,
                userName: null,
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "CreatedTime", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            tableHandles1: tableHandles1,

        };
    },
    async mounted() {
    },
    methods: {
        async loadData(row) {
            this.filter.id = row.id;
            this.filter.userName = row.userName;
            console.log(row);
            await this.onSearch()
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const res = await getWarehouseMonthSalaryUserSetPageList(params)
            this.listLoading = false;
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            data.forEach(d => {
                d.userName = this.filter.userName;
                d._loading = false;
            })
            this.summaryarry = res.data.summary;
            this.list = data;
        },
        //排序查 询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            console.log(rows)
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
    },
};
</script>

<style lang="scss" scoped></style>
