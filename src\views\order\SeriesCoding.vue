<template>
    <container>
        <template #header>
            <el-form :inline="true">
                <el-form-item label="系列编码:">
                    <el-input v-model.trim="filter.styleCode" style="width:120px;" maxlength="100" clearable></el-input>
                </el-form-item>
                <el-form-item label="扣款日期:">
                    <el-date-picker style="width: 220px" v-model="timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        :clearable="true" @change="changeTime"></el-date-picker>
                </el-form-item>
                <el-form-item label="扣款金额:">
                    <el-input-number v-model="filter.amountPaidMin" :min="0" :max="9999999.99" style="width:120px;"
                        :precision="2"></el-input-number>
                    至
                    <el-input-number v-model="filter.amountPaidMax" :min="0" :max="9999999.99" style="width:120px;"
                        :precision="2"></el-input-number>
                </el-form-item>
                <el-form-item label="扣款订单:">
                    <el-input-number v-model="filter.orderCountMin" step-strictly :min="0" :max="9999999"
                        style="width:120px;"></el-input-number>
                    至
                    <el-input-number v-model="filter.orderCountMax" step-strictly :min="0" :max="9999999"
                        style="width:120px;"></el-input-number>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="getList('Search')">搜索</el-button>
                    <el-button type="primary" @click="exportList">导出</el-button>
                </el-form-item>
            </el-form>
        </template>
        <vxetablebase ref="table" :id="'SeriesCoding202040831'" :that='that' :isIndex='true' @sortchange='sortchange'
            :isSelection='true' :hasexpand='true' :tableData='list' :tableCols='tableCols' :showsummary="true"
            @summaryClick='onsummaryClick' :summaryarry="summary" :loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog :title="chatProp.title" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false"
            v-dialogDrag>
            <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至" :clearable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                start-placeholder="开始日期" end-placeholder="结束日期" @change="changChartTime" clearable
                style="margin: 10px;" />
            <el-button type="primary" @click="getChart">搜索</el-button>
            <div v-if="!chatProp.chatLoading">
                <buschar :analysisData="chatProp.data" v-if="!chatProp.chatLoading"></buschar>
            </div>
            <div v-else v-loading="chatProp.chatLoading"></div>
        </el-dialog>

        <el-dialog :visible.sync="detailVisible" width="80%" v-dialogDrag>
            <el-form :inline="true">
                <el-form-item>
                    <el-input v-model.trim="detail.proCode" style="width:120px;" maxlength="100" clearable
                        placeholder="宝贝id"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="detail.goodsName" style="width:120px;" maxlength="100" clearable
                        placeholder="商品名称"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="detail.orderNo" style="width:120px;" maxlength="100" clearable
                        placeholder="线上订单号"></el-input>
                </el-form-item>
                <el-form-item label="扣款金额:">
                    <el-input-number v-model="detail.amountPaidMin" :min="0" :max="9999999.99" style="width:120px;"
                        :precision="2"></el-input-number>
                    至
                    <el-input-number v-model="detail.amountPaidMax" :min="0" :max="9999999.99" style="width:120px;"
                        :precision="2"></el-input-number>
                </el-form-item>
                <el-form-item label="扣款日期:">
                    <el-date-picker style="width: 220px" v-model="detailTimeRange1" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        :clearable="true" @change="changeDetailTime1"></el-date-picker>
                </el-form-item>
                <el-form-item label="付款日期:">
                    <el-date-picker style="width: 220px" v-model="detailTimeRange2" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        :clearable="true" @change="changeDetailTime2"></el-date-picker>
                </el-form-item>
                <el-form-item label="发货日期:">
                    <el-date-picker style="width: 220px" v-model="detailTimeRange3" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        :clearable="true" @change="changeDetailTime3"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="detail.illegalType" placeholder="请选择平台原因" style="width:130px" filterable
                        clearable>
                        <el-option v-for="item in causeList" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="detail.groupId" placeholder="请选择运营组" style="width:130px" filterable clearable>
                        <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value"
                            :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="detail.operateSpecialUserId" placeholder="请选择运营专员" style="width:130px" filterable
                        clearable>
                        <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="getDetail('Search')">搜索</el-button>
                    <el-button type="primary" @click="exportDetail">导出</el-button>
                </el-form-item>
            </el-form>
            <div style="height: 500px;">
                <vxetablebase ref="detailTable" :id="'Detail202040831'" :that='that' :isIndex='true' :toolbarshow="false"
                    @sortchange='detailSortchange' :isSelection='true' :hasexpand='true' :tableData='detailList'
                    :tableCols='detailTableCols' :loading="detailLoading" :summaryarry="detail.summary" :showsummary="true">
                </vxetablebase>
            </div>
            <template #footer>
                <my-pagination ref="pager2" :total="detailTotal" @page-change="detailPagechange"
                    @size-change="detailSizechange" />
            </template>
        </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { GetStyleOrderIllegalByPlatfomrReason, GetStyleOrderIllegalByPlatfomrReasonDetail, GetStyleOrderIllegalAnalysisByPlatfomrReason, ExportStyleOrderIllegalByPlatfomrReason, ExportStyleOrderIllegalByPlatfomrReasonDetail } from '@/api/order/orderdeductmoney.js';
import buschar from '@/components/Bus/buschar'
import { formatTime } from "@/utils/tools";
import { getDirectorGroupList, getDirectorList } from '@/api/operatemanage/base/shop';
import dayjs from 'dayjs'

const tableCols = [
    { istrue: true, prop: 'deductPlatformName', label: '平台', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', align: 'center', type: 'click', handle: (that, row) => that.openDetail(row), },
    {
        istrue: true, prop: 'echarts', label: '趋势图', align: 'center', type: 'button', summaryEvent: true, btnList: [
            {
                label: '趋势图',
                handle: (that, row) => {
                    that.openChart(row);
                },
            },
        ]
    },
    { istrue: true, prop: 'amountPaid', label: '总扣款金额', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'orderCount', label: '总扣款订单', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'occurrenceTime', label: '扣款日期', align: 'center',formatter: (row) => formatTime(row.occurrenceTime, 'YYYY-MM-DD') },
    { istrue: true, prop: 'afterSaleAmountPaid', label: '售后补偿金额', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'afterSaleOrderCount', label: '售后补偿订单', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'merchantAmountPaid', label: '商家责任退货金额', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'merchantOrderCount', label: '商家责任退货订单', sortable: 'custom', align: 'center' },
]
const detailTableCols = [
    { istrue: true, prop: 'proCode', label: '宝贝ID', sortable: 'custom', align: 'center', width:'130' },
    { istrue: true, prop: 'goodsName', label: '商品名称', sortable: 'custom', align: 'center', width:'300'  },
    { istrue: true, prop: 'orderNo', label: '线上订单号', sortable: 'custom', align: 'center',  },
    { istrue: true, prop: 'amountPaid', label: '扣款金额', sortable: 'custom', align: 'center', width:'80' },
    { istrue: true, prop: 'occurrenceTime', label: '扣款日期', sortable: 'custom', align: 'center', formatter: (row) => formatTime(row.occurrenceTime, 'YYYY-MM-DD'), width:'120' },
    { istrue: true, prop: 'payTime', label: '付款日期', sortable: 'custom', align: 'center', formatter: (row) => formatTime(row.payTime, 'YYYY-MM-DD') , width:'120'},
    { istrue: true, prop: 'sendTime', label: '发货日期', sortable: 'custom', align: 'center', formatter: (row) => formatTime(row.sendTime, 'YYYY-MM-DD'), width:'120' },
    { istrue: true, prop: 'illegalType', label: '平台原因', sortable: 'custom', align: 'center', formatter: (row) => { return row.illegalTypeName }, width:'120' },
    { istrue: true, prop: 'groupId', label: '运营组', sortable: 'custom', align: 'center',formatter: (row) => { return row.groupName } , width:'120' },
    { istrue: true, prop: 'operateSpecialUserId', label: '运营专员', sortable: 'custom', align: 'center',formatter: (row) => { return row.operateSpecialUserName }  },
]

export default {
    name: 'SeriesCoding',
    components: { container, vxetablebase, buschar },
    data() {
        return {
            that: this,
            timerange:null,
            filter: {
                styleCode: null,
                // amountPaidMin: null,
                // orderCountMin: null,
                startDate: null,
                endDate: null,
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },

            detail: {
                styleCode: null,
                proCode: null,
                goodsName: null,
                orderNo: null,
                //amountPaidMin: null,
                startDate: null,
                endDate: null,
                payStartTime: null,
                payEndTime: null,
                sendStartTime: null,
                sendEndTime: null,
                illegalType: null,
                groupId: null,
                operateSpecialUserId: null,
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                summary:null
            },
            chatInfo: {
                styleCode: null,
                startDate: null,
                endDate: null,
                isSummary: 0,
            },
            chatProp: {
                chatDialog: false,//趋势图弹窗
                chatTime: [],//趋势图时间
                chatLoading: true,//趋势图loading
                data: [],//趋势图数据
            },
            tableCols,
            list: [],
            loading: false,
            total: null,
            summary: null,
            detailVisible: false,
            detailList: [],
            detailTableCols,
            detailLoading: false,
            detailTotal: null,
            causeList: [
                { value: 4, label: "商家责任退货" },
                { value: 5, label: "售后补偿" }
            ],
            directorList: [],
            directorGroupList: [],
            detailTimeRange1:null,
            detailTimeRange2:null,
            detailTimeRange3:null,
        };
    },

    async mounted() {
        const res1 = await getDirectorList({});
        const res2 = await getDirectorGroupList({});
        this.directorList = res1.data;
        this.directorGroupList = [{ key: "0", value: "未知" }].concat(
            res2.data || []
        );
        this.filter.startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD');
        this.filter.endDate = dayjs().format('YYYY-MM-DD');
        this.timerange = [ this.filter.startDate, this.filter.endDate];
        await this.getList();
    },
    methods: {
        //获取列表
        async getList(val) {
            if (val == "Search") {
                this.filter.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            const { success, data } = await GetStyleOrderIllegalByPlatfomrReason(this.filter);
            if (success) {
                this.list = data?.list;
                this.total = data?.total;
                this.summary = data?.summary;
                if (this.summary)
                    this.summary.echarts_sum = "趋势图";
            }
            this.loading = false;
        },
        //获取日期
        async changeTime(e) {
            this.filter.startDate = e ? e[0] : null
            this.filter.endDate = e ? e[1] : null
        },
        async changChartTime(e) {
            this.chatInfo.startDate = e ? e[0] : null
            this.chatInfo.endDate = e ? e[1] : null
        },
        //每页数量改变
        Sizechange(val) {
            this.filter.currentPage = 1;
            this.filter.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.filter.currentPage = val;
            this.getList()
        },
        //排序查询
        sortchange({ order, prop }) {
            if (prop) {
                this.filter.orderBy = prop
                this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        //初始化趋势图
        async getChart() { 
            this.chatProp.chatLoading = true;
            var { success, data } = await GetStyleOrderIllegalAnalysisByPlatfomrReason(this.chatInfo);
            if (success) {
                this.chatProp.data = data
                this.chatProp.chatDialog = true
                this.chatProp.chatLoading = false
            }
        },

        //每页数量改变
        detailSizechange(val) {
            this.detail.currentPage = 1;
            this.detail.pageSize = val;
            this.getDetail()
        },
        //当前页改变
        detailPagechange(val) {
            this.detail.currentPage = val;
            this.getDetail()
        },
        //排序查询
        detailSortchange({ order, prop }) {
            if (prop) {
                this.detail.orderBy = prop
                this.detail.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getDetail()
            }
        },
        //打开详情页
        async openDetail(row) {
            this.detail.orderBy = null;
            this.detail.isAsc = false;
            this.detail.styleCode = row.styleCode;
            this.detail.startDate = this.filter.startDate;
            this.detail.endDate = this.filter.endDate;
            this.detailTimeRange1 = [ this.detail.startDate, this.detail.endDate];
            this.detail.currentPage = 1;
            this.$refs.pager2.setPage(1);
            this.detailVisible = true;
            this.getDetail();
        },
        //获取详情
        async getDetail() {
            this.detailLoading = true;
            const { success, data } = await GetStyleOrderIllegalByPlatfomrReasonDetail(this.detail);
            if (success) {
                this.detailList = data?.list;
                this.detailTotal = data?.total;
                this.detail.summary = data?.summary;
            }
            this.detailLoading = false;
        },
        //打开趋势图
        async openChart(row) { 
            this.chatProp.chatDialog = true;
            this.chatProp.chatTime = this.timerange;
            this.chatInfo = {
                startDate: this.chatProp.chatTime == [] || this.chatProp.chatTime == null ? null : this.chatProp.chatTime[0],
                endDate: this.chatProp.chatTime == [] || this.chatProp.chatTime == null ? null : this.chatProp.chatTime[1],
                styleCode: row.styleCode,
                isSummary: 0,
            }; 
            let that = this;
            this.$nextTick(() => { 
                that.getChart();
            })
        },
        //汇总点击
        async onsummaryClick() {
            this.chatProp.chatDialog = true;
            this.chatProp.chatTime = this.timerange;
            this.chatInfo = {
                startDate: this.chatProp.chatTime == [] || this.chatProp.chatTime == null ? null : this.chatProp.chatTime[0],
                endDate: this.chatProp.chatTime == [] || this.chatProp.chatTime == null ? null : this.chatProp.chatTime[1],
                styleCode: null,
                isSummary: 1,
            };
            let that = this;
            this.$nextTick(() => {
                that.getChart();
            })
        },
        //导出
        async exportList() {
             await ExportStyleOrderIllegalByPlatfomrReason(this.filter);
        },
        //导出
        async exportDetail() {
            await ExportStyleOrderIllegalByPlatfomrReasonDetail(this.detail);
        },
        //更改时间
        changeDetailTime1(e) {
            this.detail.startDate = e ? e[0] : null
            this.detail.endDate = e ? e[1] : null
        },
        changeDetailTime2(e) {
            this.detail.payStartTime = e ? e[0] : null
            this.detail.payEndTime = e ? e[1] : null
        }, changeDetailTime3(e) {
            this.detail.sendStartTime = e ? e[0] : null
            this.detail.sendEndTime = e ? e[1] : null
        },
    },
};
</script>

<style lang="scss" scoped></style>