<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" :before-leave="beforeLeave" style="height:94%;">
           
            <el-tab-pane label="加班审批" name="tab1" style="height: 100%;">
                <OverWorkApproveWorkData ref="OverWorkApproveWorkData" style="height: 100%;" />
            </el-tab-pane>

        </el-tabs>
    </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";
import OverWorkApproveWorkData from '@/views/profit/OverWorkApprove/OverWorkApproveWorkData';

export default {
    name: "OverWorkApproveIndex",
    components: {
        MyContainer, OverWorkApproveWorkData
    },
    data() {
        return {
            that: this,
            pageLoading: '',
            activeName:"tab1",
        };
    },
    mounted() {
    },
    methods: {
    },


};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
