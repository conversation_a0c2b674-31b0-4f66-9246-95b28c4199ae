<template>
    <my-container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="快递公司:">
                    <el-select v-model="filter.companyId" placeholder="请选择快递公司" style="width: 130px">
                        <el-option label="所有" value="" />
                        <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name"
                            :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onAdd">新增</el-button>
                </el-form-item>
            </el-form>
        </template>

        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
            @select="selectchange" :tableHandles='tableHandles' @cellclick="cellclick" :loading="listLoading">
        </ces-table>
        <!-- <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"  @get-page="getlist"/>
        </template> -->

        <div v-show="isshowstate">
            <expresscomanystationallset ref="expresscomanystationallset" @changelist="changelist" @onSearch="onSearch">
            </expresscomanystationallset>
        </div>

        <el-dialog title="新增" :visible.sync="addFormVisible" :close-on-click-modal="false" @close="onCloseAddForm" v-dialogDrag>
            <el-form ref="addForm" :model="addForm" label-width="110px" :rules="addFormRules">
                <el-form-item v-if="false" label="id" prop="id">
                    <el-input v-model="addForm.id" placeholder="id" />
                </el-form-item>
                <el-form-item label="快递公司" prop="comanyName">
                    <el-input v-model="addForm.comanyName" placeholder="快递公司" />
                </el-form-item>
                <el-form-item label="别名" prop="otherNames">
                    <el-input v-model="addForm.otherNames" placeholder="别名" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click.native="addFormVisible = false">取消</el-button>
                    <my-confirm-button type="submit" :validate="addFormValidate" :loading="addLoading"
                        @click="onAddSubmit" />
                </div>
            </template>
        </el-dialog>

        <el-dialog title="编辑" :visible.sync="editFormVisible" :close-on-click-modal="false" @close="onCloseEditForm" v-dialogDrag>
            <el-form ref="editForm" :model="editForm" label-width="110px" :rules="editFormRules">
                <el-form-item label="快递公司" prop="comanyName">
                    <el-input v-model="editForm.comanyName" placeholder="快递公司" />
                </el-form-item>
                <el-form-item label="别名" prop="otherNames">
                    <el-input v-model="editForm.otherNames" placeholder="别名" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click.native="editFormVisible = false">取消</el-button>
                    <my-confirm-button type="submit" :validate="editFormValidate" :loading="editLoading"
                        @click="onEditSubmit" />
                </div>
            </template>
        </el-dialog>
    </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import MyContainer from "@/components/my-container";
import cesTable from "@/components/Table/table.vue";
import MyConfirmButton from '@/components/my-confirm-button';
import { getOrderAnalysisWrongHairList } from '@/api/order/ordernodes';
import { getExpressComanyStationAll, getExpressComanyAll, addOrEditExpressComany, getExpressComany, deleteExpressComany } from "@/api/express/express";
import expresscomanystationallset from './expresscomanystationallset.vue'

const tableCols = [
    { istrue: true, prop: 'comanyName', label: '快递公司', width: '150', },
    { istrue: true, prop: 'stationName', label: '站点', width: '400', },
    { istrue: true, prop: 'otherNames', label: '别名', width: 'auto', },
    {
        istrue: true, type: 'button', width: '220', label: '操作', btnList: [
            { label: "设置站点", display: (row) => { return row.isHandle == true; }, handle: (that, row) => that.nclick(row) },
            { label: "编辑快递公司", display: (row) => { return row.isHandle == true; }, handle: (that, row) => that.onEdit(row) },
            { label: "删除", display: (row) => { return row.isHandle == true; }, handle: (that, row) => that.onDelete(row) },
        ]
    },

]
const tableHandles = [
    { label: '刷新', handle: (that) => that.onSearch() },
]

const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunhanAdminExpresscomanystationall',
    components: { MyContainer, cesTable, MyConfirmButton, expresscomanystationallset },

    data() {
        return {
            that: this,
            cashRedResonItems: [],
            filter: {
                afterNo: '',
                orderNoInner: '',
                companyId: null,
                goodsName: '',
                goodsCode: '',
                resonLevel1: '',
                resonLevel2: '',
                saleAfter_Type: '5',
                timerange: [startTime, endTime],
                startTime: null,
                endTime: null,
            },
            shareFeeType: 5,
            params: {},
            that: this,
            list: [],
            picList: [],
            saleList: [],
            expresscompanylist: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: 'Confirm_Date', IsAsc: false },
            summaryarry: {},
            total: 0,
            sels: [],
            selids: [],
            listLoading: false,
            pageLoading: false,
            isshowstate: false,
            addFormVisible: false,// 新增界面是否显示
            addLoading: false,
            addFormRules: {
                comanyName: [{ required: true, message: '请输入快递公司', trigger: 'change' },
                             ],
            },
            addForm: { comanyName: '', otherNames: '' },
            addFormKey: 1,
            editFormVisible: false, // 编辑界面是否显示
            editLoading: false,
            editFormRules: {
                comanyName: [{ required: true, message: '请输入快递公司', trigger: 'change' }],
            },
            // 编辑界面数据
            editForm: { id: '0', comanyName: '', otherNames: '' },
            editFormKey: 1,
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                },
            },
        };
    },

    async mounted() {
        await this.onSearch();
        await this.getExpressComanyList();
    },

    methods: {
        // // 新增
        addFormValidate: function () {
            let isValid = false
            this.$refs.addForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        onCloseAddForm() {
            this.$refs.addForm.resetFields()
            ++this.addFormKey
        },
        async onAdd() {
            this.addFormVisible = true
        },
        async onAddSubmit() {
            this.addLoading = true
            const para = _.cloneDeep(this.addForm)
            const res = await addOrEditExpressComany(para)
            this.addLoading = false

            if (!res?.success) {
                return
            }
            this.$message({
                message: this.$t('admin.addOk'),
                type: 'success'
            })
            this.$refs['addForm'].resetFields()
            this.addFormVisible = false
            this.onSearch()
        },
        onCloseEditForm() {
            this.$refs.editForm.resetFields()
            ++this.editFormKey
        },
        // 显示编辑界面
        async onEdit(row) {
            console.log('编辑参数',row)
            const loading = this.$loading()
            const res = await getExpressComany(row)
            loading.close()
            this.editFormVisible = true;
            if (res && res.success) {
                const data = res.data
                this.editForm.id = data.id
                this.editForm.comanyName = data.comanyName
                this.editForm.otherNames = data.otherNames
                ++this.editFormKey
            }
        },
        // 编辑
        editFormValidate: function () {
            let isValid = false
            this.$refs.editForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        async onEditSubmit() {
            this.editLoading = true
            const para = _.cloneDeep(this.editForm)
            const res = await addOrEditExpressComany(para)
            this.editLoading = false
            if (!res?.success) {
                return
            }
            this.$message({
                message: this.$t('admin.updateOk'),
                type: 'success'
            })
            this.$refs['editForm'].resetFields()
            this.editFormVisible = false
            this.getlist()
        },
        async onDelete(row) {

            row._loading = true
            this.$confirm('确定删除吗, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                .then(async () => {
                    var res = await deleteExpressComany({id:row.id});
                    if (res?.data) {
                        this.$message({ message: '删除成功', type: "success" })
                        await this.getlist();
                    }
                    else {
                        this.$message({ message: '删除失败', type: "warning" })
                    }
                }).catch(() => {
                    this.$message({ type: 'info', message: '已取消' });
                });
        },
        async onSearch() {
            //this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            // var pager = this.$refs.pager.getPager();
            // var page  = this.pager;
            // this.filter.startTime = null;
            // this.filter.endTime = null;
            // if (this.filter.timerange) {
            //     this.filter.startTime = this.filter.timerange[0];
            //     this.filter.endTime = this.filter.timerange[1];
            // }
            const params = { ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await getExpressComanyStationAll(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            //this.total = res.data.total;
            const data = res.data;
            //this.summaryarry=res.data.summary;
            data.forEach(d => {
                d._loading = false
            })
            this.list = data
        },
        async nclick(row) {
            console.log('数据来了', row)
            this.isshowstate = true
            this.$nextTick(async () => {
                await this.$refs.expresscomanystationallset.OnSearch(row.id, this.list)
            })
        },
        async changelist(e) {
            this.list = e
        },
        async getExpressComanyList() {
            const res = await getExpressComanyAll({});
            if (!res?.success) {
                return;
            }
            const data = res.data;
            this.expresscompanylist = data;
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event) {

        },
    },
};
</script>

<style lang="scss" scoped>

</style>