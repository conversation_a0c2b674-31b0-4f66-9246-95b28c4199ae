<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
            <div class="top">
                <el-button-group>

                    <el-button style="padding: 0;margin: 0; border: 0; ">
                        <el-select v-model="filter.selectDateType" filterable placeholder="" style="width: 100px">
                            <el-option label="建编码日期" :value="1" />
                            <el-option label="进货日期" :value="2" />
                            <el-option label="上链接日期" :value="3" />
                            <el-option label="出单日期" :value="4" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                            :clearable="false" style="width: 240px;" :value-format="'yyyy-MM-dd'" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0; ">
                        <el-input v-model.trim="filter.styleCode" type="text" maxlength="40" clearable
                            placeholder="款式编码" style="width:120px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0; ">
                        <el-input v-model.trim="filter.goodsCode" type="text" maxlength="40" clearable
                            placeholder="商品编码" style="width:120px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0; ">
                        <el-select v-model="filter.pushNewUserIds" clearable filterable placeholder="推荐人" multiple
                            collapse-tags style="width: 150px">
                            <el-option v-for="item in createdUserList" :key="item.createdUserId"
                                :label="item.createdUserName" :value="item.createdUserId" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0; ">
                        <el-select filterable v-model="filter.chooseGroupIds" placeholder="选品组" style="width: 150px" clearable multiple collapse-tags >
                            <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0; ">
                        <el-select filterable v-model="filter.choosePlatform" placeholder="选品人平台" clearable
                        style="width: 120px">
                            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
        
                    <el-button style="padding: 0;margin: 0; border: 0; ">
                        <el-select v-model="filter.chooseUserIds" clearable filterable multiple collapse-tags
                            placeholder="选品人" style="width: 150px">
                            <el-option v-for="item in chooseUserList" :key="item.chooseUserId"
                                :label="item.chooseUserName" :value="item.chooseUserId" />
                        </el-select>
                    </el-button>


                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.chooseUserRoles" clearable filterable placeholder="职位" multiple
                            collapse-tags style="width: 250px">
                            <el-option v-for="item in chooseUserRoleList" :key="item.chooseUserRole"
                                :label="item.chooseUserRole" :value="item.chooseUserRole" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.chooseUserDeptNames" clearable filterable placeholder="架构" multiple
                            collapse-tags style="width: 250px">
                            <el-option v-for="item in chooseUserDeptNameList" :key="item.chooseUserDeptName"
                                :label="item.chooseUserDeptName" :value="item.chooseUserDeptName" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0; ">
                        <el-select v-model="filter.errorDateType" clearable filterable placeholder="异常类型" style="width: 150px">
                            <el-option label="建编码后未进货<7天‌‌" :value="1" />
                            <el-option label="建编码后未进货7-30天‌" :value="2" />
                            <el-option label="建编码后未进货>30天‌" :value="3" />
                            <el-option label="进货后未上链接<7天‌" :value="4" />
                            <el-option label="进货后未上链接7-30天‌" :value="5" />
                            <el-option label="进货后未上链接>30天‌‌" :value="6" />
                            <el-option label="进货后未出单<7天‌" :value="7" />
                            <el-option label="进货后未出单7-30天‌" :value="8" />
                            <el-option label="进货后未出单>30天‌‌" :value="9" />
                        </el-select>
                    </el-button>

                    <el-button type="primary" @click="onSearch()" style="">查询</el-button>
                    <el-button type="primary" @click="onExport()" :loading="exportloading">导出</el-button>

                    <el-button style="padding: 0;margin: 10px 0px 0px 0px; border: 0; ">
                        {{ a0 }}{{ extData.createdTime }}；

                        {{ a1 }}{{ extData.pur7Down }}；
                        {{ a2 }}{{ extData.pur7To30 }}；
                        {{ a3 }}{{ extData.pur30Up }}；

                        {{ a4 }}{{ extData.pro7Down }}；
                        {{ a5 }}{{ extData.pro7To30 }}；
                        {{ a6 }}{{ extData.pro30Up }}；

                        {{ a7 }}{{ extData.sale7Down }}；
                        {{ a8 }}{{ extData.sale7To30 }}；
                        {{ a9 }}{{ extData.sale30Up }}；
                    </el-button>

                </el-button-group>

            </div>
        </template>
        <vxetablebase :id="'HotSaleBrandPushNewSale2202408041719'" ref="table" :that='that' :isIndex='true'
            :hasexpand='true' :tablefixed='true' :border="true" @sortchange='sortchange' @select='selectchange'
            :tableData='tableData' :tableCols='tableCols' :showsummary='true' :summaryarry='summaryarry'
            :isSelection="false" :isSelectColumn="false"
            :treeProp="{ rowField: 'rowId', parentField: 'parentId', expandAll: false, transform: true, }"
            style="width: 100%;  margin: 0" v-loading="listLoading" :height="'100%'">
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import dayjs from 'dayjs';
import datepicker from '@/views/customerservice/datepicker';
import { pickerOptions,platformlist } from '@/utils/tools';
import { formatTime } from "@/utils";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    GetHotSaleBrandPushNewChooseSearch, GetHotSaleBrandPushNewKczjPageList,ExportHotSaleBrandPushNewKczjList
} from '@/api/operatemanage/productalllink/alllink'
import {  getDirectorGroupList} from '@/api/operatemanage/base/shop'

const tableCols = [
    { sortable: 'custom', width: '120', align: 'center', prop: 'styleCode', label: '款式编码', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'costPrice', label: '进货价', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'stockNumber', label: '当前库存', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'stockAmount', label: '库存资金', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'docTime', label: '建编码时间', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'purchaseTime', label: '第一次进货时间', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'proCodeTime', label: '上链接时间', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'saleTime', label: '第一次出单时间', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'pushNewUserName', label: '推品人', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'chooseUserName', label: '选品人', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'chooseUserDeptName', label: '选品人组织架构', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'chooseUserRole', label: '选品人岗位', },

];
export default {
    name: "HotSaleBrandPushNewKczjDtl",
    components: {
        MyContainer, datepicker, vxetablebase
    },
    data() {
        return {
            auditVisible: false,
            activities: [],
            timeRanges: [],
            that: this,
            pickerOptions,
            filter: {
                timerange: [(dayjs().subtract(1, 'day').format('YYYY-MM') + '-01'), dayjs().subtract(1, 'day').format('YYYY-MM-DD')],
                docStartDate: null,
                docEndDate: null,
                selectDateType: 1,
            },
            pager: { OrderBy: "docTime", IsAsc: false },
            tableCols,
            tableData: [],
            extData: {},
            total: 0,
            listLoading: false,
            pageLoading: false,
            exportloading: false,
            sels: [],
            selids: [],
            summaryarry: {},
            createdUserList: [],
            chooseUserList: [],
            chooseUserRoleList: [],
            chooseUserDeptNameList: [],
            platformlist: platformlist,

            a0: "最新更新时间：",
            a1: "建编码后未进货<7天：",
            a2: "建编码后未进货7-30天：",
            a3: "建编码后未进货30天：",
            a4: "进货后未上链接<7天：",
            a5: "进货后未上链接7-30天：",
            a6: "进货后未上链接>30天：",
            a7: "进货后未出单<7天：",
            a8: "进货后未出单7-30天：",
            a9: "进货后未出单>30天：",
        }
    },
    async mounted() {
        await this.otherdata();
    },
    computed: {
    },
    methods: {
        async otherdata() {
            let ret = await GetHotSaleBrandPushNewChooseSearch({ type: 5 });
            this.createdUserList = ret.data;

            let ret2 = await GetHotSaleBrandPushNewChooseSearch({ type: 2 });
            this.chooseUserList = ret2.data;

            let ret3 = await GetHotSaleBrandPushNewChooseSearch({ type: 3 });
            this.chooseUserRoleList = ret3.data;

            let ret4 = await GetHotSaleBrandPushNewChooseSearch({ type: 4 });
            this.chooseUserDeptNameList = ret4.data;

            
      const res2 = await getDirectorGroupList({})
      this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getParam() {
            if (this.filter.timerange && this.filter.timerange.length == 2) {
                this.filter.docStartDate = this.filter.timerange[0];
                this.filter.docEndDate = this.filter.timerange[1];
            }
            else {
                this.filter.docStartDate = null;
                this.filter.docEndDate = null;
            }
            let pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            return params;
        },
        async getList() {
            this.sels = [];
            this.selids = [];
            let param = this.getParam();
            this.listLoading = true
            const res = await GetHotSaleBrandPushNewKczjPageList(param)
            this.listLoading = false
            console.log(res);
            if (res?.success) {
                this.tableData = res.data.list;
                this.total = res.data.total;
                this.summaryarry = res.data.summary;
                this.extData = res.data.extData;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport(opt) {
            let pars = this.getParam();
            const params = { ...pars, ...opt };
            this.exportloading = true;
            let res = await ExportHotSaleBrandPushNewKczjList(params);
            this.exportloading = false;
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '采购推新库存资金明细' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}

::v-deep .el-select__tags-text {
    max-width: 120px;
}
</style>
