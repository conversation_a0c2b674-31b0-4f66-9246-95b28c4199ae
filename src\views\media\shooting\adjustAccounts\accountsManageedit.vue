<template>
    <!-- 员工设置 + 行内编辑 -->
    <my-container v-loading="pageLoading">
        <div style="height: 95%;">
            <vxetablebase :id="'accountmanage202301291318001'" :that='that' height="98%" border ref="xtable"
                class="draggable-table" :isIndex='true' :hasexpand='false' :isSelectColumn='true' :tableData='tasklist'
                :tableCols='tableCols' tablekey="accountmanage" :loading="listLoading" :isBorder="false" :editconfig="{
                    trigger: 'manual', mode: 'row', showStatus: true, showIcon: false
                    , autoClear: false
                }" :validRules="validRules" @sortchange='sortchange' :row-class-name="redLine" :hascheck="true" @selectchangeevent="selectcallback" @checkboxall="selectcallback">
                <template slot='right'>
                    <vxe-column title="操作" width="160" fixed="right">
                        <template #default="{ row }">
                            <template v-if="row.editstatus && row.editstatus == 1">
                                <vxe-button size="mini" @click="saveRowEvent(row)">保存</vxe-button>
                                <vxe-button size="mini" @click="cancelRowEvent(row)">取消</vxe-button>
                            </template>
                            <template v-else>
                                <vxe-button size="mini" @click="editRowEvent(row)">编辑</vxe-button>

                                <vxe-button size="mini" @click="onDel(row)">删除</vxe-button>
                            </template>
                        </template>
                    </vxe-column>
                </template>
                <template slot='extentbtn'>
                    <div style="width: 100%">
                        <el-tabs v-model="activeName" @tab-click="handleClick">
                            <el-tab-pane label="所有员工" name="a" v-if="checkPermission('sjsjb-syyg')">
                            </el-tab-pane>
                            <el-tab-pane label="摄影师" name="b" v-if="checkPermission('sjsjb-sys')">
                            </el-tab-pane>
                            <el-tab-pane label="摄像师" name="c" v-if="checkPermission('sjsjb-sxs')">
                            </el-tab-pane>
                            <el-tab-pane label="美工" name="d" v-if="checkPermission('sjsjb-mg')">
                            </el-tab-pane>
                            <el-tab-pane label="3D建模" name="e" v-if="checkPermission('sjsjb-3djm')">
                            </el-tab-pane>
                            <el-tab-pane label="短视频" name="f" v-if="checkPermission('sjsjb-dsp')">
                            </el-tab-pane>
                            <el-tab-pane label="模特/助理" name="h" v-if="checkPermission('sjsjb-mtzl')">
                            </el-tab-pane>
                            <el-tab-pane label="其他" name="g" v-if="checkPermission('sjsjb-qt')">
                            </el-tab-pane>
                        </el-tabs>
                    </div>
                    <div style="margin:10px 5px 5px 0;">
                        <span style="padding: 0;margin-right:2px;">
                            <el-input style="width:15%;" v-model="filter.userName" v-model.trim="filter.userName"
                                :maxlength=100 placeholder="姓名" @keyup.enter.native="onSearch" clearable />
                        </span>
                        <span style="padding: 0;margin-right:5px;">
                            <el-select style="width:8%;" v-model="filter.hascode" placeholder="erp账号" clearable>
                                <el-option label="有" value="1"></el-option>
                                <el-option label="无" value="0"></el-option>
                            </el-select>
                        </span>
                        <span style="padding: 0;margin-right:5px;">
                            <el-select style="width:8%;" v-model="filter.workPosition" :filterable="true" :clearable="true"
                                placeholder="工作岗位">
                                <el-option v-for="item in workPositionlist" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </span>
                        <span style="padding: 0;margin-right:5px;">
                            <el-select style="width:8%;" v-model="filter.commissionPosition" :filterable="true"
                                :clearable="true" placeholder="提成岗位">
                                <el-option v-for="item in commissionPositionlist" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </span>
                        <span style="padding: 0;margin-right:5px;">
                            <el-select style="width:8%;" v-model="filter.companyName" placeholder="公司" clearable>
                                <el-option label="义乌" value="义乌"></el-option>
                                <el-option label="南昌" value="南昌"></el-option>
                                <el-option label="武汉" value="武汉"></el-option>
                                <el-option label="深圳" value="深圳"></el-option>
                            </el-select>
                        </span>
                        <el-select v-model="filter.employeeStatus" style="width:120px;margin-right:5px;" filterable
                            placeholder="状态" :clearable="true">
                            <el-option label="正常" value="3"></el-option>
                            <!-- <el-option label="实习" value="2"></el-option> -->
                            <el-option label="离职" value="2"></el-option>
                        </el-select>
                        <span style="padding: 0;margin-right:5px;">
                            <el-button type="primary" @click="onSearch">查询</el-button>
                        </span>
                        <el-button-group><el-button type="primary" @click="onOpenAdd" v-if="activeName!='a'">新增人员</el-button>
                            <!-- <el-button type="primary" @click="saveOrder" v-if="activeName!='a'">保存排序</el-button> -->
                            <el-button type="primary" @click="onImportSyj" v-if="activeName!='a'">员工导入</el-button>
                            <el-button type="primary" @click="ClickdownloadTemplate" v-if="activeName!='a'">下载导入模板</el-button>
                            <el-button type="primary" @click="assessmentSetting" v-if="activeName!='a'">考核设置</el-button>
                            <el-button type="primary" @click="exportDataEvent">导出</el-button>
                        </el-button-group>
                    </div>
                </template>
            </vxetablebase>
        </div>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList"
                :page-size="100" />
        </template>
        <el-dialog :title="editformTitle" :visible.sync="editformdialog" width="60%" :close-on-click-modal="false"
            v-loading="editLoading" element-loading-text="拼命加载中" v-dialogDrag :append-to-body="true">
            <!-- 新增人员弹窗 -->
            <el-form :model="editform" ref="editform" label-width="120px" :rules="editformRules">
                <el-row>&nbsp;</el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item prop="userId" label="姓名">
                            <el-select v-model="editform.userId" :filterable="true" :clearable="true"
                                @change="selUserInfoChange(editform.userId, index)">
                                <el-option v-for="item in userList" :label="item.label" :value="item.id"
                                    :key="item.id + ''"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item prop="companyName" label="公司">
                            <el-select v-model="editform.companyName" :clearable="true" :collapse-tags="true" filterable>
                                <el-option label="义乌" value="义乌"></el-option>
                                <el-option label="南昌" value="南昌"></el-option>
                                <el-option label="武汉" value="武汉"></el-option>
                                <el-option label="深圳" value="深圳"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="workPosition" label="工作岗位">
                            <el-select v-model="editform.workPosition" :filterable="true" :clearable="true">
                                <el-option v-for="item in workPositionlist" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="commissionPosition" label="提成岗位">
                            <el-select v-model="editform.commissionPosition" :filterable="true" :clearable="true">
                                <el-option v-for="item in commissionPositionlist" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <!-- <el-col :span="6">
                        <el-form-item prop="classtype" label="类型">
                            <el-input :clearable="true" v-model.trim="editform.classtype" :maxlength="50"></el-input>
                        </el-form-item>
                    </el-col> -->
                    <el-col :span="6">
                        <el-form-item prop="basepay" label="底薪">
                            <el-input-number :clearable="true" v-model.trim="editform.jBasepay" :min="0" :max="1000000"
                                :controls="false" :precision="2"></el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="achievement" label="绩效">
                            <el-input-number :clearable="true" v-model.trim="editform.jAchievement" :min="0" :max="1000000"
                                :controls="false" :precision="2"></el-input-number>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item prop="subsidy" label="补贴">
                            <el-input-number :clearable="true" v-model.trim="editform.subsidy" :min="0" :max="1000000"
                                :controls="false" :precision="2"></el-input-number>

                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="overTimePay" label="加班费">
                            <el-input-number :clearable="true" v-model.trim="editform.overTimePay" :min="0" :max="1000000"
                                :controls="false" :precision="2"></el-input-number>

                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="modephone" label="手机号">
                            <el-input :clearable="true" :maxlength="50" v-model.trim="editform.modephone"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="nowAddress" label="现住址">
                            <el-input :clearable="true" :maxlength="50" v-model.trim="editform.nowAddress"></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="6">
                        <el-form-item prop="homeAddress" label="家庭住址">
                            <el-input :clearable="true" :maxlength="50" v-model.trim="editform.homeAddress"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="bank" label="银行">
                            <el-input :clearable="true" :maxlength="50" v-model.trim="editform.bank"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="bankCode" label="银行卡">
                            <el-input :clearable="true" :maxlength="50" v-model.trim="editform.bankCode"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="accountingItem" label="核算项目">
                            <el-input :clearable="true" :maxlength="50" v-model.trim="editform.accountingItem"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="editformdialog = false">关 闭</el-button>
                    <my-confirm-button type="submit" @click="onSubmit" />
                </span>
            </template>
        </el-dialog>
        <!--薪资模板导入-->
        <el-dialog title="员工导入" :visible.sync="dialogVisibleSyj" width="30%" :before-close="onImportClose">
            <span>
                <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                    :file-list="fileList" action accept=".xlsx" :http-request="uploadFile2">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <my-confirm-button style="margin-left: 10px;" size="small" type="success"
                        @click="onSubmitupload2">上传</my-confirm-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="onImportClose()">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog title="提示" :visible.sync="dialogVisible" width="25%" center :before-close="handleClose"  v-dialogDrag>
          <div style="width:100%;display: flex;justify-content: center;">
            <el-radio v-model="assesType" label="1" border size="mini">岗位考核</el-radio>
            <el-radio v-model="assesType" label="2" border size="mini">部门考核</el-radio>
          </div>
          <div slot="footer" class="dialog-footer" style="margin-bottom:10px;">
            <el-button size="mini" @click="dialogVisible = false">取 消</el-button>
            <el-button size="mini" type="primary" @click="determination">确 定</el-button>
          </div>
        </el-dialog>
    </my-container>
</template>
<script>
//导入dayjs
import dayjs from 'dayjs';
import vxetablebase from "@/components/VxeTable/vxetablemedianew.vue";
import MyContainer from "@/components/my-container";
import { formatTime, listToTree } from "@/utils";
import cesTable from "@/components/Table/tableforvedio.vue";
import accountsWorkPostionManage from '@/views/media/shooting/adjustAccounts/accountsWorkPostionManage';
import { getErpUserInfoViewforshoot, getWorkPostListAsync, getErpUserInfoView } from '@/api/media/mediashare';
import { addOrUpdatePersonnelPositionAsync, getPersonnelPositionAsync, delPersonnelPositionAsync, sortPersonnelPosition, setPersonnelIsHsAsync, importShootingSalaryAsync, assessTypeSet } from '@/api/media/shootingset'
import MyConfirmButton from "@/components/my-confirm-button";
import Sortable from 'sortablejs';
import checkPermission from '@/utils/permission'

export default {
    components: { MyContainer, cesTable, MyConfirmButton, accountsWorkPostionManage, vxetablebase },
    data () {
        return {
            idList: [],
            assesType: null,
            dialogVisible: false,
            WorkCategoryType: null,
            activeName: 'a',
            that: this,
            pageLoading: false,
            positionOrder: false,
            summaryarry: [],
            userList: [],
            tasklist: [],
            fileList: [],
            workPositionlist: [],
            commissionPositionlist: [],
            retdata: [],
            sels: [], // 列表选中列
            //tableCols: tableCols,
            listLoading: false,
            //人员编辑模块
            editLoading: false,
            editformdialog: false,
            dialogVisibleSyj: false,
            editformTitle: null,
            editform: {
                openType: 1,
                positionId: 0,
                userId: null,
                userName: null,
                companyName: null,
                workPosition: null,
                commissionPosition: null,
                commissionRate: 100,
                isCyCommission: 0,
                fpTaskAutoRate: 100,
                isCyFp: 1,
                classtype: null,
                basepay: null,
                achievement: null,
                subsidy: null,
                overTimePay: null,
                modephone: null,
                workCategoryType:null
            },
            total: 0,
            pager: { OrderBy: "orderNum", IsAsc: true },
            filter: {
            },
            editformRules: {
                userId: [{ required: true, message: '请选择', trigger: 'blur' }],
                commissionRate: [{ required: true, message: '请填写', trigger: 'blur' }],
                companyName: [{ required: true, message: '请选择', trigger: 'blur' }],
                commissionPosition: [{ required: true, message: '请填写', trigger: 'blur' }],
                workPosition: [{ required: true, message: '请填写', trigger: 'blur' }],
                fpTaskAutoRate: [{ required: true, message: '请填写', trigger: 'blur' }],
                classtype: [{ required: true, message: '请填写', trigger: 'blur' }],
            },
            //表格编辑校验
            validRules: {
                companyName: [
                    { required: true, message: '必填' }
                ],
                workPosition: [
                    { required: true, message: '必填' }
                ],
                commissionPosition: [
                    { required: true, message: '必填' }
                ],
                classtype: [
                    { required: true, message: '必填' }
                ],
            },
        };
    },
    computed: {
        tableCols () {
            let tableCols = [
                { istrue: true, prop: 'checkbox', type: 'checkbox' },
                { istrue: true, type: '', prop: 'userName', label: '姓名', width: '70', fixed: "left" },
                { istrue: true, type: 'editselect', prop: 'companyName', label: '公司', fixed: "left", width: '65', options: [{ label: '南昌', value: '南昌' }, { label: '义乌', value: '义乌' }, { label: '武汉', value: '武汉' }, { label: '深圳', value: '深圳' }] },
                { istrue: true, type: 'editselect', prop: 'workPosition', label: '工作岗位', width: '85', options: this.workPositionlist },
                { istrue: true, type: 'editselect', prop: 'commissionPosition', label: '提成岗位', width: '85', options: this.commissionPositionlist },
                { istrue: true, type: 'editText', maxlength: '50', prop: 'accountingItem', label: '核算项目', width: '70' },
                { istrue: true, type: '', prop: 'assessType', label: '考核类型', width: '70', formatter: (row) => {return row.assessType == 1 ? '岗位考核' : row.assessType == 2 ? '部门考核' : ''} },
                { istrue: true, type: '', prop: 'userId', label: 'erp账号', width: '65', formatter: (row) => { return row.userId == 999999 ? '无' : '' } },
                // { istrue: true, type: 'editText', maxlength: '50', prop: 'classtype', label: '类型', width: '80' },
                { istrue: true, type: 'editNumber', min: '0', max: '1000000', prop: 'jBasepay', label: '底薪', width: '80' },
                { istrue: true, type: 'editNumber', min: '0', max: '1000000', prop: 'jAchievement', label: '绩效', width: '70' },
                { istrue: true, type: 'editNumber', min: '0', max: '1000000', prop: 'sbSubsidy', label: '设备补助', width: '70' },
                { istrue: true, type: 'editselect', prop: 'workCategoryType', label: '工作组', width: '80', options: [{ label: '摄影师', value: 1 }, { label: '摄像师', value: 2 }, { label: '美工', value: 3 }, { label: '3D建模', value: 4 }, { label: '短视频', value: 5 }, { label: '模特/助理', value: 6 }, { label: '其他', value: 0 }] },
                { istrue: true, type: 'editText', maxlength: '13', prop: 'modephone', label: '手机号', width: '100' },
                { istrue: true, type: 'editText', maxlength: '200', prop: 'nowAddress', label: '现住址', width: '150' },
                { istrue: true, type: 'editText', maxlength: '200', prop: 'homeAddress', label: '家庭住址', width: '150' },
                { istrue: true, type: 'editText', maxlength: '200', prop: 'bank', label: '银行', width: '80' },
                { istrue: true, type: 'editText', maxlength: '19', prop: 'bankCode', label: '银行卡号', width: '150' },
                { istrue: true, type: '', maxlength: '200', prop: 'employeeStatusStr', label: '状态', width: '70', type: 'tag', tagstatus: (row) => { return row.employeeStatus == 3 ? 'success' : row.employeeStatus == 2 ? 'warning' : 'info' }, },
                { istrue: true, type: '', prop: 'yggl', label: '工龄', width: '120' },
                { istrue: true, type: 'editDate', prop: 'joinedDate', label: '入职日期', width: '90', formatter: (row) => { return row.joinedDate == null ? '' : formatTime(row.joinedDate, 'YYYY-MM-DD') } },
                { istrue: true, type: 'editDate', prop: 'leaveDate', label: '离职日期', width: '90', formatter: (row) => { return row.leaveDate == null ? '' : formatTime(row.leaveDate, 'YYYY-MM-DD') } },
            ];
            return tableCols;
        }
    },
    async mounted () {
        await this.getUserList();
        await this.getWorkPostList();
        //await this.onSearch();
        // this.rowDrop();
        this.ShowHideonSearch();
        if (checkPermission('sjsjb-syyg'))
        {
            this.activeName='a';
            this.handleClick();
        }else if (checkPermission('sjsjb-sys'))
        {
            this.activeName='b';
            this.handleClick();
        }else if(checkPermission('sjsjb-sxs'))
        {
            this.activeName='c';
            this.handleClick();
        }else if(checkPermission('sjsjb-mg'))
        {
            this.activeName='d';
            this.handleClick();
        }else if(checkPermission('sjsjb-3djm'))
        {
            this.activeName='e';
            this.handleClick();
        }else if(checkPermission('sjsjb-dsp'))
        {
            this.activeName='f';
            this.handleClick();
        }else if(checkPermission('sjsjb-mtzl'))
        {
            this.activeName='h';
            this.handleClick();
        }else if(checkPermission('sjsjb-qt'))
        {
            this.activeName='g';
            this.handleClick();
        }
    },
    methods: {
      async determination(){
        const params  = { assesType: this.assesType , idList : this.idList}
        console.log(params,'params');
        const { success } = await assessTypeSet(params)
        if(success){
          this.$message({ message: '设置成功',  type: 'success' });
          this.dialogVisible = false
          this.onSearch()
        }
      },
      handleClose(done) {
        this.$confirm('确认关闭？')
          .then(_ => {
            done();
            this.assesType = null
          })
          .catch(_ => {});
      },
      assessmentSetting(){
        this.assesType = '-1';
        this.dialogVisible = true
      },
      selectcallback(val){
        this.idList = [];
        val.forEach((obj) => {
          this.idList.push(obj.userId);
        });
      },
      //默认列隐藏
      async ShowHideonSearch() {
            this.listLoading = true;
            var arrlist = [];
            arrlist = [ 'homeAddress', 'bank', 'bankCode' ];
            await this.$refs.xtable.ShowHidenColums(arrlist);
            this.listLoading = false;
        },
        //加上红线
        // redLine ({ row, rowIndex, $rowIndex }) {
        //     console.log(row, 'row');
        //     console.log(row.employeeStatus, 'row.employeeStatus');
        //     if (!row.employeeStatus == 2) {
        //         return '';
        //     } else {
        //         return 'droprow';
        //     }
        // },
        //抽离参数
        async getParams (props) {
            var pager = this.$refs.pager.getPager();
            let params = {
                ...this.filter,
                ...pager,
                ...this.pager,
                WorkCategoryType: props,
            };
            this.listLoading = true;
            let res = await getPersonnelPositionAsync(params);
            this.listLoading = false;
            this.total = res.data.total;
            res.data.list.forEach((item, index) => {
                item.editstatus = 0;
                item.joinedDate = item.joinedDate ? dayjs(item.joinedDate).format('YYYY-MM-DD') : '';
                item.leaveDate = item.leaveDate ? dayjs(item.leaveDate).format('YYYY-MM-DD') : '';
            });
            this.tasklist = res.data.list;
        },
        //点击tabs切换
        async handleClick () {
            if (this.activeName == 'a') {
                this.WorkCategoryType = null;
                this.getParams(null)
            } else if (this.activeName == 'b') {
                this.WorkCategoryType = 1;
                this.getParams(1)
            } else if (this.activeName == 'c') {
                this.WorkCategoryType = 2;
                this.getParams(2)
            } else if (this.activeName == 'd') {
                this.WorkCategoryType = 3
                this.getParams(3)
            } else if (this.activeName == 'e') {
                this.WorkCategoryType = 4
                this.getParams(4)
            } else if (this.activeName == 'f') {
                this.WorkCategoryType = 5
                this.getParams(5)
            }else if (this.activeName == 'h') {
                this.WorkCategoryType = 6
                this.getParams(6)
            } else if (this.activeName == 'g') {
                this.WorkCategoryType = 0
                this.getParams(0)
            }
        },
        //编辑保存
        async saveRowEvent (row) {
            const $table = this.$refs.xtable;
            if (! await $table.validate())
                return;
            const joinedDate = +new Date(row.joinedDate);
            const leaveDate = +new Date(row.leaveDate);
            if (joinedDate && leaveDate) {
                if (joinedDate > leaveDate) {
                    this.$message({ message: '保存失败，离职日期不能小于入职日期!', type: "fail" });
                    return
                }
            }
            if ($table.isUpdateByRow(row)) {
                row.openType = 1;
                var res = await addOrUpdatePersonnelPositionAsync(row);
                if (res?.success) {
                    this.$message({ message: '保存成功!', type: "success" });
                }
                $table.reloadRow(row);
                $table.clearActivedRowEvent(row);
                this.onSearch();
            } else {
                this.$message({ message: '数据未发生变化!', type: "info" });
                $table.clearActivedRowEvent(row);
            }
        },
        //开启编辑
        editRowEvent (row) {
            this.$refs.xtable.editRowEvent(row);
        },
        //取消编辑
        cancelRowEvent (row) {
            this.$refs.xtable.cancelRowEvent(row);
        },
        //导出
        exportDataEvent () {
            this.$refs.xtable.exportData("员工信息");

        },
        onImportClose () {
            this.dialogVisibleSyj = false;
            this.fileList = [];
        },
        onImportSyj () {
            this.dialogVisibleSyj = true;
        },
        ClickdownloadTemplate () {
            window.open("/static/excel/shooting/视觉设计部员工模板.xlsx", "_blank");
        },
        async onSubmitupload2 () {
            this.$refs.upload2.submit()
        },
        async uploadFile2 (item) {
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("actiontype", 0);
            form.append("workCategorytype", this.WorkCategoryType);
            var res = await importShootingSalaryAsync(form);
            this.fileList = [];
            if (res?.success) {
                if (res.data == null)
                    this.$message({ message: '上传成功!', type: "success" });
                else
                    this.$message({ message: res.data, type: "error" });
                this.onSearch();
                this.dialogVisibleSyj = false;
            }
        },
        //保存排序
        async saveOrder () {
            this.listLoading = true;
            var res = await sortPersonnelPosition({orderType: this.WorkCategoryType,entityList: this.tasklist});
            if (res?.success)
                this.$message({ message: this.$t('操作成功'), type: 'success' });
            this.listLoading = false;
        },
        //下拉排序
        rowDrop () {
            const tbody = document.querySelector('.draggable-table .vxe-table--body-wrapper tbody')
            const _this = this
            Sortable.create(tbody, {
                onEnd ({ newIndex, oldIndex }) {
                    if (newIndex == oldIndex) return
                    _this.tasklist.splice(
                        newIndex,
                        0,
                        _this.tasklist.splice(oldIndex, 1)[0]
                    )
                    var newArray = _this.tasklist.slice(0)
                    _this.tasklist = []
                    _this.$nextTick(function () {
                        _this.tasklist = newArray
                    })
                }
            })
        },
        //删除数据
        async onDel (row) {
            this.$confirm("将进行删除操作，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await delPersonnelPositionAsync({ positionId: row.positionId });
                if (!res?.success) { return; }
                this.$message({ message: this.$t('操作成功'), type: 'success' });
                await this.onSearch()
            });
        },
        //是否核算
        async CyCommission (row) {
            var res = await setPersonnelIsHsAsync({ positionId: row.positionId, isCyCommission: row.isCyCommissionbool ? 1 : 0 });
            if (!res?.success) { return; }
            this.$message({ message: this.$t('保存成功'), type: 'success' });
        },
        //下拉切换用户
        selUserInfoChange (val, index) {
            let resultArr = this.userList.find((item) => {
                return item.id == val;
            });
            this.editform.userName = resultArr.label;
        },
        //获取岗位下拉数据
        async getWorkPostList () {
            var res = await getWorkPostListAsync();
            if (res) {
                this.workPositionlist = res.workPositionlist.map(item => ({ value: item.id, label: item.label }));
                this.commissionPositionlist = res.commissionPositionlist.map(item => ({ value: item.id, label: item.label }));
            }
        },
        //获取用户信息
        async getUserList () {
            this.userList = await getErpUserInfoView();
            return this.userList;
        },
        //打开新增窗口
        async onOpenAdd () {
            this.initform();
            this.editformdialog = true;
            await getWorkPostListAsync();
        },
        initform () {
            this.editform.positionId = 0;
            this.editform.userId = null;
            this.editform.userName = null;
            this.editform.companyName = null;
            this.editform.workPosition = null;
            this.editform.commissionPosition = null;
            this.editform.commissionRate = 100;
            this.editform.isCyCommission = 0;
            this.editform.fpTaskAutoRate = 100;
            this.editform.isCyFp = 1;
            this.editform.classtype = null;
            this.editform.basepay = null;
            this.editform.achievement = null;
            this.editform.subsidy = null;
            this.editform.overTimePay = null;
            this.editform.modephone = null;
        },
        //打开新增窗口
        async onEditAdd (row) {

            this.editformdialog = true;
            this.editLoading = true;
            await getWorkPostListAsync();
            this.editform = row;
            this.editform.userId = row.userId.toString();
            this.editformTitle = "编辑人员";
            this.editLoading = false;
        },
        //提交保存时验证
        onSubmitValidate: function () {
            let isValid = true;
            this.$refs.editform.validate(valid => {
                isValid = valid
            })
            return isValid;
        },
        async onSubmit () {
            if (!this.onSubmitValidate()) {
                return;
            }
            this.editform.openType = 1;
            this.editform.workCategoryType=this.WorkCategoryType;
            const para = _.cloneDeep(this.editform);
            this.editLoading = true;
            var res = await addOrUpdatePersonnelPositionAsync(para);
            this.editLoading = false;
            if (!res?.success) { return; }
            this.$message({ message: this.$t('保存成功'), type: 'success' });
            this.onSearch();
            this.editformdialog = false;
        },
        async onSearch () {
            this.$refs.pager.setPage(1);
            // this.getTaskList();
            this.getParams(this.WorkCategoryType)
        },
        async getTaskList () {
            var pager = this.$refs.pager.getPager();
            let params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            this.getParamsg = true;
            let res = await getPersonnelPositionAsync(params);
            this.listLoading = false;
            this.total = res.data.total;
            res.data.list.forEach((item, index) => {
                item.editstatus = 0;
                item.joinedDate = item.joinedDate ? dayjs(item.joinedDate).format('YYYY-MM-DD') : '';
                item.leaveDate = item.leaveDate ? dayjs(item.leaveDate).format('YYYY-MM-DD') : '';
            });
            this.tasklist = res.data.list;
        },
        sortchange (column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
    },
};
</script>

<style lang="scss" scoped>
::v-deep .droprow td {
    color: rgb(250, 9, 9);
    position: relative;
}

::v-deep .droprow ::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 0.1px;
    background-color: rgb(250, 9, 9);
    transform: translateY(-50%);
}
</style>

