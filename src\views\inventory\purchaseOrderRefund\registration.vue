<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="110px" width="300px">
          <el-row>
            <el-col :span="5">
              <el-form-item label="退款金额" prop="refundAmount">
                <el-input-number v-model.trim="ruleForm.refundAmount" placeholder="请输入" :min="-99999" :max="9999999"
                  :precision="2" :controls="false" class="subkey_item" />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="扣除金额" prop="deductAmount">
                <el-input-number v-model.trim="ruleForm.deductAmount" placeholder="请输入" :min="-99999" :max="9999999"
                  :precision="2" :controls="false" class="subkey_item">
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="退款时间" prop="refundDatetime">
                <el-date-picker v-model="ruleForm.refundDatetime" type="datetime" placeholder="选择日期时间"
                  format="yyyy-MM-dd" :value-format="'yyyy-MM-dd'" class="subkey_item">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="账号">
                <el-select v-model="ruleForm.serviceAlias" placeholder="账号" clearable filterable class="subkey_item"
                  @change="onCollectChange">
                  <el-option label="无" value="无" />
                  <el-option v-for="item in serviceData" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="收款账号" prop="collectAccount">
                <el-select v-model="ruleForm.collectAccount" placeholder="收款账号" clearable filterable disabled
                  class="subkey_item" @change="onCollectChange">
                  <el-option v-for="item in receivables" :key="item.value"
                    :label="`${item.userName} ${item.bankType} ${item.label}`" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row style="height: 132px;">
            <el-col :span="5">
              <el-form-item label="收款方式" prop="collectType">
                <el-input v-model="ruleForm.collectType" placeholder="收款方式" disabled class="subkey_item"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="收款人" prop="collectUser">
                <el-input v-model="ruleForm.collectUser" placeholder="收款人" disabled class="subkey_item"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="附件" prop="annexUrl">
                <uploadimgFile ref="uploadimgFile" v-if="editPriceVisible" :accepttyes="accepttyes" :isImage="true"
                  class="subkey_item" :uploadInfo="ruleForm.annexUrl" :keys="[1, 1]" @callback="getRuleFormPic"
                  :imgmaxsize="9" :limit="9" :multiple="true">
                </uploadimgFile>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="备注">
                <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 5 }" placeholder="请输入内容"
                  v-model="ruleForm.remark" maxlength="100" show-word-limit class="subkey_item">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <!-- <el-col :span="3">
              <el-form-item label="待入判断金额">
                <span style="color: red">{{ ruleForm.pendingJudgeAmount }}</span>
              </el-form-item>
            </el-col> -->
            <el-col :span="4">
              <el-form-item label="调整类型" prop="adjustType">
                <el-select v-model="ruleForm.adjustType" placeholder="调整类型" filterable style="width: 150px;">
                  <!-- <el-option label="主对冲" value="主对冲" />
                  <el-option label="多入不退" value="多入不退" /> -->
                  <el-option label="对冲需完结" value="对冲需完结" />
                  <el-option label="退款需完结" value="退款需完结" />
                  <el-option label="已完结退款" value="已完结退款" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="20">
              <el-form-item label="采购单号" prop="po_Id">
                <div style="display: flex;align-items: baseline;width: 100%;">
                  <div>
                    <el-button class="button-new-tag" size="small" @click="showInput">新增采购单号</el-button>
                  </div>
                  <div class="tag-container">
                    <el-tag :key="tag" v-for="tag in ruleForm.po_Id" closable :disable-transitions="false"
                      @close="handleClose(tag)" class="tag_item">
                      <span class="tag_item_value">{{ tag }}</span>
                    </el-tag>
                    <el-input-number v-show="inputVisible" v-model.trim="inputValue" placeholder="请输入" size="small"
                      ref="saveTagInput" :min="0" :max="9999999" :precision="2" :controls="false"
                      @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm" class="input-new-tag" />
                  </div>
                  <el-button type="primary" size="small" @click="onGeneratedData"
                    style="width: 70px;margin-left: 10px;">生成</el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </template>
    <vxetablebase :id="'refundModule202302031421'" ref="table" :that='that' v-if="tableshow" :isIndex='true'
      :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
      :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <template #isPay="{ row }">
        <div class="line_style">
          <!-- <el-checkbox v-model="row.isPay" @change="isPayChange($event, row)" /> -->
          <el-select v-model="row.isPay" @change="isPayChange($event, row)">
            <el-option label="是" value="是" />
            <el-option label="否" value="否" />
            <el-option label="部分" value="部分" />
          </el-select>
        </div>
      </template>
      <template #payAmount="{ row }">
        <div class="line_style">
          <span v-if="!row.rowVerify">
            {{ row.payAmount }}
          </span>
          <el-input-number v-else v-model.trim="row.payAmount" placeholder="请输入" size="small" :max="9999999"
            :precision="2" :controls="false" @blur="saveRowEvent(row, 9)" />
        </div>
      </template>
      <template slot="right">
        <!-- <vxe-column field="adjustAmount" title="调整金额" width="140">
          <template #default="{ row, $index }">
            <div class="line_style">
              <span>
                {{ row.adjustAmount }}
              </span>
            </div>
          </template>
  <template #footer="{ items }">
            <span>{{ summaryarry.adjustAmount_sum }}</span>
          </template>
  </vxe-column> -->
        <vxe-column field="returnAmount" title="退货款" width="150">
          <template #default="{ row, $index }">
            <div class="line_style">
              <span v-if="!row.lineEdit">
                {{ row.returnAmount }}
              </span>
              <el-input-number v-else v-model.trim="row.returnAmount" placeholder="请输入" size="small" :max="9999999"
                :precision="2" :controls="false" @blur="saveRowEvent(row, 1)" />
            </div>
          </template>
          <template #footer="{ items }">
            <span>{{ summaryarry.returnAmount_sum }}</span>
          </template>
        </vxe-column>
        <!--
        <vxe-column field="adjustType" title="调整类型" width="140">
          <template #default="{ row, $index }">
            <div class="line_style">
              <span v-if="!row.lineEdit" class="adjust-type">
                {{ row.adjustType }}
              </span>
              <el-select v-else v-model="row.adjustType" placeholder="调整类型" clearable filterable>
                <el-option label="对冲(有相对应的对冲采购单号)" value="对冲(有相对应的对冲采购单号)" />
                <el-option label="多入不付" value="多入不付" />
                <el-option label="多退不补" value="多退不补" />
                <el-option label="采购补差价负数入库" value="采购补差价负数入库" />
                <el-option label="未付款" value="未付款" />
                <el-option label="对冲(有相对应的采购单号)" value="对冲(有相对应的采购单号)" />
              </el-select>
            </div>
          </template>
          <template #footer="{ items }">
            <span></span>
          </template>
        </vxe-column> -->
        <vxe-column field="returnFreight" title="退运费" width="150">
          <template #default="{ row, $index }">
            <div class="line_style">
              <span v-if="!row.lineEdit">
                {{ row.returnFreight }}
              </span>
              <el-input-number v-else v-model.trim="row.returnFreight" placeholder="请输入" size="small" :min="0"
                :max="9999999" :precision="2" :controls="false" @blur="saveRowEvent(row, 3)" />
            </div>
          </template>
          <template #footer="{ items }">
            <span>{{ summaryarry.returnFreight_sum }}</span>
          </template>
        </vxe-column>
        <vxe-column field="totalRefunsds" title="总退款" width="150">
          <template #default="{ row, $index }">
            <div class="line_style">
              <!-- {{ row.totalRefunsds }} -->
              <el-input-number v-model.trim="row.totalRefunsds" placeholder="请输入" size="small" :min="0" :max="9999999"
                :precision="2" :controls="false" @blur="saveRowEvent(row, 10)" />
            </div>
          </template>
          <template #footer="{ items }">
            <span>{{ summaryarry.totalRefunsds_sum }}</span>
          </template>
        </vxe-column>
        <!-- { width: '150', align: 'center', prop: 'tempTotal', label: '合计', } -->
        <vxe-column field="tempTotal" title="合计" width="150">
          <template #default="{ row, $index }">
            <div class="line_style">
              {{ row.tempTotal }}
            </div>
          </template>
          <template #footer="{ items }">
            <span>{{ summaryarry.tempTotal_sum }}</span>
          </template>
        </vxe-column>

        <!-- <vxe-column title="操作" width="130">
          <template #default="{ row, $index }">
            <template v-if="row.lineEdit">
              <vxe-button @click="saveRowEvent(row)">保存</vxe-button>
            </template>
            <template v-else>
              <vxe-button @click="editRowEvent(row)">编辑</vxe-button>
            </template>
          </template>
        </vxe-column> -->
      </template>
    </vxetablebase>
    <template #footer>
      <!-- <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" /> -->
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import { generatePurchaseOrderRefundDetails, addPurchaseOrderRefundDetails, editPurchaseOrderRefundDetailsERP, editAddPurchaseOrderRefundDetails } from '@/api/inventory/purchase'
import { getAllOnlineBank, queryOnlineBankSet } from '@/api/cwManager/bankFlow.js';
import dayjs from 'dayjs'
const tableCols = [
  { align: 'center', prop: 'po_Id', label: '采购单号', },
  { width: '120', align: 'center', prop: 'isPay', label: '是否付款', },
  { width: '150', align: 'center', prop: 'payAmount', label: '付款金额', },
  // { width: '150', align: 'center', prop: 'adjustAmount', label: '调整金额', },
  // { width: '100', align: 'center', prop: 'sku_id', label: '商品编码', },
  // { width: '80', align: 'center', prop: 'price', label: '单价', },
  // { width: '80', align: 'center', prop: 'buy_qty', label: '采购数量', },
  { width: '150', align: 'center', prop: 'buy_Amount', label: '采购金额', },
  { width: '150', align: 'center', prop: 'cost_Amount', label: '已入库金额', },
  { width: '150', align: 'center', prop: 'pendingAmount', label: '未入库金额', },
  // { width: '80', align: 'center', prop: 'qty', label: '已入库数量', },
  // { width: '80', align: 'center', prop: 'pending_qty', label: '未入库数量', },
  // { width: '80', align: 'center', prop: 'refund_qty', label: '退货数量', },
]
export default {
  name: "registration",
  props: {
    purchaseNumber: {
      type: Array,
      default() {
        return []
      }
    },
    verify: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  components: {
    MyContainer, vxetablebase, uploadimgFile,
  },
  data() {
    return {
      tableshow: true,
      receivables: [],
      serviceData: [],//业务别名选择器数据
      ruleForm: {
        refundAmount: undefined,
        deductAmount: undefined,//扣除金额
        refundDatetime: '',
        collectAccount: '',
        serviceAlias: '',
        collectType: '',
        collectUser: '',
        annexUrl: [],
        remark: '',
        name: '',
        po_Id: [],
        adjustType: '对冲需完结',
        pendingJudgeAmount: 0,
      },
      inputVisible: false,
      inputValue: undefined,
      accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {
        payAmount_sum: 0,
        buy_Amount_sum: 0,
        cost_Amount_sum: 0,
        refund_Amount_sum: 0,
        pendingAmount_sum: 0,
        returnAmount_sum: 0,
        adjustAmount_sum: 0,
        returnFreight_sum: 0,
        totalRefunsds_sum: 0,
        tempTotal_sum: 0,
      },
      total: 0,
      loading: false,
      pickerOptions,
      urlList: [],
      editPriceVisible: false,
      rules: {
        refundAmount: [{ required: true, message: '请输入退款金额', trigger: 'blur' }],
        refundDatetime: [{ required: true, message: '请选择退款时间', trigger: 'blur' }],
        // collectAccount: [{ required: true, message: '请选择收款账号', trigger: 'blur' }],
        // collectType: [{ required: true, message: '请选择收款方式', trigger: 'blur' }],
        // collectUser: [{ required: true, message: '请输入收款人', trigger: 'blur' }],
        annexUrl: [{ required: true, message: '请上传附件', trigger: 'blur' }],
        remark: [{ required: true, message: '请输入备注', trigger: 'blur' }],
        po_Id: [{ required: true, message: '请选择采购单号', trigger: 'blur' }],
        adjustType: [{ required: true, message: '请选择调整类型', trigger: 'blur' }],
        serviceAlias: [{ required: true, message: '请选择业务别名', trigger: 'blur' }],
      }
    }
  },
  async mounted() {
    await this.init()
    if (this.purchaseNumber && this.purchaseNumber.length > 0) {
      this.loading = true
      const { data, success } = await editPurchaseOrderRefundDetailsERP(this.purchaseNumber)
      this.loading = false
      if (!success) return
      this.$nextTick(() => {
        this.tableData = data.list
        this.ruleForm.refundDatetime = dayjs(data.list[0].refundDatetime).format('YYYY-MM-DD HH:mm:ss')
        this.ruleForm.deductAmount = data.list[0].deductAmount
        this.ruleForm.collectAccount = data.list[0].collectAccount
        this.ruleForm.collectType = data.list[0].collectType
        this.ruleForm.collectUser = data.list[0].collectUser
        if (data.list[0].annexUrl) {
          const urls = data.list[0].annexUrl.split(',');
          this.ruleForm.annexUrl = urls.map((url, i) => ({
            url: url,
          }));
        } else {
          this.ruleForm.annexUrl = [];
        }
        this.ruleForm.remark = data.list[0].remark
        this.ruleForm.adjustType = data.list[0].adjustType ? data.list[0].adjustType : '对冲需完结'
        let obj = {}
        this.ruleForm.po_Id = data.list.map(item => item.po_Id)
        this.ruleForm.po_Id = this.ruleForm.po_Id.reduce((cur, next) => {
          obj[next] ? "" : obj[next] = true && cur.push(next);
          return cur;
        }, [])
        this.summaryarry = data.summary
        this.tableData.forEach(item => {
          item.rowVerify = false
          item.lineEdit = true
          // item.tempTotal = 0
          // if (item.isPay == '是' || item.isPay == '否') {
          //   item.isPay = item.isPay == '是' ? true : false
          // } else {
          //   item.isPay = true
          // }
          if (item.isPay != '是' && item.isPay != '否' && item.isPay != '部分') {
            item.isPay = '是'
          }
          item.temporarily = item.returnAmount
          // item.mesgid = item.mesgid ? item.mesgid.toString() : ''
        })
        this.editPriceVisible = true
        this.onCalculated()
      })
    } else {
      this.editPriceVisible = true
    }
  },
  methods: {
    isPayChange(e, row) {
      this.$nextTick(() => {
        this.tableData.forEach(item => {
          if (item.po_Id === row.po_Id) {
            item.isPay = e;
            if (e === '是') {
              item.payAmount = item.buy_Amount;
              item.rowVerify = false;
              item.returnAmount = item.temporarily;
            } else if (e === '否') {
              Object.assign(item, { temporarily: item.returnAmount, adjustAmount: 0, returnAmount: 0, payAmount: 0, rowVerify: false });
              item.rowVerify = true;
              item.payAmount = 0;
              if (item.cost_Amount <= 0) {
                item.temporarily = item.returnAmount;
                item.adjustAmount = 0;
                item.returnAmount = 0;
              } else if (item.cost_Amount > 0) {
                item.adjustAmount = this.precisionAdd(-Math.abs(item.cost_Amount), item.outboundAmount);
                item.temporarily = item.temporarily || 0;
                item.returnAmount = 0;
              }
            } else if (e === '部分') {
              // this.$message.warning('付款金额必填！');
              // 调整金额 = -（已入库-出库-付款+退款）
              item.adjustAmount = -this.precisionAdd(this.precisionSub(this.precisionSub(item.cost_Amount, item.outboundAmount), item.payAmount), item.totalRefunsds);
              item.rowVerify = true;
            }
            this.saveRowEvent(item, 5);
          }
        });
        this.$forceUpdate();
      });
    },
    async onSave() {
      let vaird = false
      this.$refs.ruleForm.validate(async (valid) => {
        if (!valid) {
          this.$message.error('请填写必填项')
          vaird = true
        }
      })

      if (this.summaryarry.buy_Amount_sum + this.summaryarry.totalRefunsds_sum < this.summaryarry.payAmount_sum && this.ruleForm.deductAmount === null) {
        this.$message.error('已入库金额+退款金额<付款金额时，扣除金额必填！');
        return;
      }
      this.tableData.forEach(item => {
        if (item.isPay == '部分' && item.payAmount == null) {
          this.$message.error('部分付款时，付款金额必填！');
        }
      });

      if (vaird) return
      // 确保所有值都是数字类型
      const refundAmount = Number(this.ruleForm.refundAmount);
      const pendingJudgeAmount = Number(this.ruleForm.pendingJudgeAmount);
      const totalRefunds = Number(this.summaryarry.totalRefunsds_sum);
      // if (refundAmount !== pendingJudgeAmount || pendingJudgeAmount !== totalRefunds) {
      //   this.$message.error('退款金额与总退款金额不一致');
      //   return;
      // }
      //总退款金额与其他2个不同
      if (totalRefunds !== refundAmount) {
        this.$message.error('退款金额与总退款金额不一致');
        return;
      }
      //待入判断金额与其他2个不同
      // if (pendingJudgeAmount !== refundAmount && refundAmount === totalRefunds) {
      //   this.$message.error('退款金额与待入判断金额不一致');
      //   return;
      // }
      //退款金额与其他2个不同
      // if (refundAmount !== totalRefunds && pendingJudgeAmount === totalRefunds) {
      //   this.$message.error('退款金额与总退款金额不一致');
      //   return;
      // }
      // if (refundAmount !== totalRefunds && pendingJudgeAmount !== totalRefunds && refundAmount !== pendingJudgeAmount) {
      //   this.$message.error('退款金额、待入判断金额、总退款金额均不一致');
      //   return;
      // }
      let tableDatas = this.tableData
      // tableDatas.forEach(item => {
      //   item.isPay = item.isPay ? '是' : '否'
      // })

      const params = { ...this.ruleForm, totalRefunsds_sum: this.summaryarry.totalRefunsds_sum, tableData: tableDatas, adjustType: this.ruleForm.adjustType }
      this.loading = true
      let success = false;
      let data;
      this.$emit('onUnpackParent')
      if (this.verify) {
        ({ data, success } = await addPurchaseOrderRefundDetails(params));
      } else {
        ({ data, success } = await editAddPurchaseOrderRefundDetails(params));
      }
      this.loading = false;
      this.$emit('onClosesublevel')
      if (!success) return;
      this.$emit('onStorageMethod')
      this.$message.success('保存成功')
    },
    async onCollectChange(e) {
      if (e && e != '无') {
        // const { data } = await queryOnlineBankSet({ busAccountName: e })
        this.receivables.forEach(item => {
          if (item.busAccountName == e) {
            this.ruleForm.collectUser = item.userName
            this.ruleForm.collectType = item.bankType
            this.ruleForm.collectAccount = item.account
          }
        })
      } else if (e == '无') {
        this.ruleForm.collectAccount = '无'
        this.ruleForm.collectType = null
        this.ruleForm.collectUser = null
      } else {
        this.ruleForm.collectUser = null
        this.ruleForm.collectType = null
      }
    },
    // 处理小数精度
    precisionAdd(arg1, arg2) {
      arg1 = Number(arg1) || 0;
      arg2 = Number(arg2) || 0;
      let r1 = 0, r2 = 0, m, n;
      try { r1 = arg1.toString().split(".")[1].length; } catch (e) { }
      try { r2 = arg2.toString().split(".")[1].length; } catch (e) { }
      m = Math.pow(10, Math.max(r1, r2));
      n = Math.max(r1, r2);
      let result = ((arg1 * m + arg2 * m) / m).toFixed(n);
      return isNaN(result) ? 0 : Number(result);
    },
    precisionSub(arg1, arg2) {
      arg1 = Number(arg1) || 0;
      arg2 = Number(arg2) || 0;
      let r1 = 0, r2 = 0, m, n;
      try { r1 = arg1.toString().split(".")[1].length; } catch (e) { }
      try { r2 = arg2.toString().split(".")[1].length; } catch (e) { }
      m = Math.pow(10, Math.max(r1, r2));
      n = Math.max(r1, r2);
      let result = ((arg1 * m - arg2 * m) / m).toFixed(n);
      return isNaN(result) ? 0 : Number(result);
    },
    onCalculated() {
      //计算待入判断金额
      this.ruleForm.pendingJudgeAmount = 0
      if (Object.keys(this.summaryarry).length === 0 && this.summaryarry.constructor === Object) return
      let summaryarry = JSON.parse(JSON.stringify(this.summaryarry))
      summaryarry.adjustAmount_sum = (summaryarry.adjustAmount_sum)
      let sum = 0;
      this.tableData.forEach(item => {
        sum += item.refund_qty * item.price;
      });
      this.ruleForm.pendingJudgeAmount = this.precisionAdd(this.precisionSub((summaryarry.pendingAmount_sum), (summaryarry.adjustAmount_sum)), summaryarry.returnFreight_sum);
      this.$forceUpdate()
    },
    saveRowEvent(row, val) {

      let index = this.tableData.findIndex(item => item === row)
      // this.tableData[index].lineEdit = !row.lineEdit
      //将输入的值转换为数字类型
      const fieldsToCheck = ['adjustAmount', 'returnAmount', 'returnFreight'];
      fieldsToCheck.forEach(field => {
        if (!this.tableData[index][field]) {
          this.tableData[index][field] = 0;
        }
      });
      // 调整金额的值为未入库金额减去退货款
      //所以改变退货款的值时(val为1)，调整金额的值也会改变
      if (val == 1) {
        // if (this.ruleForm.adjustType != '多入不退') {
        if (this.ruleForm.adjustType == '对冲需完结' && this.tableData[index].isPay == '是') {
          this.tableData[index].adjustAmount = this.precisionSub((this.tableData[index].pendingAmount), this.precisionAdd(this.tableData[index].returnFreight, this.tableData[index].returnAmount));
        }
        // this.isPayChange(this.tableData[index].isPay, this.tableData[index]);
        this.tableData.forEach(item => {
          item.temporarily = item.returnAmount
        })
      }
      this.tableshow = false;
      // 总退款的值为退货款加上退运费
      this.tableData[index].totalRefunsds = this.precisionAdd(this.tableData[index].returnAmount, this.tableData[index].returnFreight)
      //合计=已入库金额+总退款-付款金额
      this.tableData[index].tempTotal = this.precisionSub(this.precisionAdd(this.tableData[index].cost_Amount, this.tableData[index].totalRefunsds), this.tableData[index].payAmount);
      if (this.tableData[index].isPay == '是') {
        this.tableData[index].adjustAmount = this.precisionSub(this.tableData[index].pendingAmount, this.tableData[index].totalRefunsds);
      } else {
        // 调整金额 = -（已入库-出库-付款+退款）(原逻辑)
        // if (this.tableData[index].cost_Amount <= 0) {
        //   this.tableData[index].adjustAmount = 0;
        // } else {
        //   this.tableData[index].adjustAmount = this.precisionAdd((-Math.abs(this.tableData[index].cost_Amount)), this.tableData[index].outboundAmount);
        // }
        // 调整金额 = -（已入库-出库-付款+退款）(新逻辑)
        this.tableData[index].adjustAmount = -this.precisionAdd(this.precisionSub(this.precisionSub(this.tableData[index].cost_Amount, this.tableData[index].outboundAmount), this.tableData[index].payAmount), this.tableData[index].totalRefunsds);
      }
      this.summaryarry.returnFreight_sum = 0;
      this.summaryarry.totalRefunsds_sum = 0;
      this.summaryarry.adjustAmount_sum = 0;
      this.summaryarry.returnAmount_sum = 0;
      this.summaryarry.payAmount_sum = 0;
      this.summaryarry.tempTotal_sum = 0;
      //计算合计行数据
      this.tableData.forEach(item => {
        this.summaryarry.returnFreight_sum = this.precisionAdd(this.summaryarry.returnFreight_sum, item.returnFreight ? item.returnFreight : 0);
        this.summaryarry.adjustAmount_sum = this.precisionAdd(this.summaryarry.adjustAmount_sum, item.adjustAmount ? item.adjustAmount : 0);
        this.summaryarry.returnAmount_sum = this.precisionAdd(this.summaryarry.returnAmount_sum, item.returnAmount ? item.returnAmount : 0);
        this.summaryarry.payAmount_sum = this.precisionAdd(this.summaryarry.payAmount_sum, item.payAmount ? item.payAmount : 0);
        this.summaryarry.tempTotal_sum = this.precisionAdd(this.summaryarry.tempTotal_sum, item.tempTotal ? item.tempTotal : 0);
      })
      // this.summaryarry.adjustAmount_sum = Number(this.summaryarry.adjustAmount_sum)
      this.summaryarry.adjustAmount_sum = this.summaryarry.adjustAmount_sum.toString()
      // 总退款的值为退货款汇总加上退运费汇总
      this.summaryarry.totalRefunsds_sum = this.precisionAdd(this.summaryarry.returnAmount_sum, this.summaryarry.returnFreight_sum);
      this.tableshow = true;
      this.onCalculated()
      // this.$nextTick(() => {
      //   const tableRef = this.$refs.table;
      //   if(this.$refs.table) {
      //     let a = this.$refs.table.$refs.xTable.getColumns();
      //     this.$refs.table.footerMethod(a);
      //   }
      // });
      this.$forceUpdate()
    },
    editRowEvent(row) {
      let index = this.tableData.findIndex(item => item === row)
      this.tableData[index].lineEdit = !row.lineEdit
      this.$forceUpdate()
    },
    performancescoreverify(row, $event, type) {
    },
    async onGeneratedData() {
      if (this.ruleForm.po_Id.length == 0) {
        this.$message.error('请输入采购单号')
        return
      }
      let purchase = []
      purchase = this.ruleForm.po_Id.map(item => Number(item))
      let params = { po_Ids: purchase, adjustType: this.ruleForm.adjustType, ...this.ListInfo }
      this.loading = true
      const { data, success } = await generatePurchaseOrderRefundDetails(params)
      this.loading = false
      if (!success) return
      this.tableData = data.list
      this.tableData.forEach(item => {
        item.lineEdit = true
        // if (item.isPay == '是' || item.isPay == '否') {
        //   item.isPay = item.isPay == '是' ? true : false
        // } else {
        //   item.isPay = true
        // }
        if (item.isPay != '是' && item.isPay != '否' && item.isPay != '部分') {
          item.isPay = '是'
        }
        // item.temporarily = 0
        item.temporarily = item.returnAmount
      })
      this.total = data.total
      this.summaryarry = data.summary
      this.editPriceVisible = true
      this.onCalculated()
    },
    handleClose(tag) {
      this.tableData = this.tableData.filter(item => item.po_Id != tag)
      this.ruleForm.po_Id.splice(this.ruleForm.po_Id.indexOf(tag), 1);
      this.onCalculated()
      this.$forceUpdate()
    },

    showInput() {
      this.inputVisible = true;
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },

    handleInputConfirm() {
      if (this.ruleForm.po_Id.some(id => String(id) === String(this.inputValue))) {
        this.$message.error('采购单号存在重复');
        return;
      }
      let inputValue = this.inputValue;
      if (inputValue && this.ruleForm.po_Id.length < 20) {
        this.ruleForm.po_Id.push(inputValue);
      } else if (this.ruleForm.po_Id.length >= 20) {
        this.$message.warning('一次性最多只能添加20个采购单号');
      }
      this.inputVisible = false;
      this.inputValue = undefined;
      this.onGeneratedData();
    },
    getRuleFormPic(data) {
      if (data) {
        let remarkImages = data.map(item => item.url)
        this.ruleForm.annexUrl = remarkImages
      }
    },
    async init() {
      this.loading = true
      const data = await getAllOnlineBank();
      this.receivables = data
      const { data: data1, success: success1 } = await queryOnlineBankSet({ "currentPage": 1, "pageSize": 10000, "orderBy": "", "isAsc": true })
      this.loading = false
      if (!success1) return
      this.serviceData = data1.list.filter(item => item.busAccountName != null && item.busAccountName != '' && item.busAccountName != undefined).map(item => item.busAccountName)
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.onGeneratedData()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.onGeneratedData()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.onGeneratedData()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

.el-tag+.el-tag {
  margin-left: 10px;
}

.button-new-tag {
  // margin-left: 10px;
  margin: 0 5px 0 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  // vertical-align: bottom;
}

.tag-container {
  white-space: nowrap;
  overflow-x: auto;
  cursor: grab;
  flex-wrap: wrap;
  align-items: center;
  margin-top: -2px;
  max-width: 708px;
}

/* 自定义横向滚动条 */
.tag-container::-webkit-scrollbar {
  height: 6px;
  /* 滚动条高度 */
}

/* 滚动条轨道 */
.tag-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  /* 可以根据需要自定义轨道颜色 */
}

/* 滚动条滑块 */
.tag-container::-webkit-scrollbar-thumb {
  background: #888;
  /* 滚动条颜色 */
  border-radius: 10px;
  /* 圆角 */
}

/* 滚动条滑块悬停时的样式 */
.tag-container::-webkit-scrollbar-thumb:hover {
  background: #555;
  /* 悬停时滑块颜色 */
  cursor: pointer;
  /* 鼠标样式 */
}

.tag_item {
  height: 33px;
  padding-top: 5px;

  .tag_item_value {
    font-size: 13px;
  }
}

.line_style {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 5px;
}

.line_style span {
  display: inline-block;
  max-width: 150px;
  /* 设置合适的最大宽度 */
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  /* 添加省略号 */
}

.subkey_item {
  width: 100%;
}
</style>
