<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select class="publicCss" v-model.trim="ListInfo.dept" filterable clearable placeholder="部门"
                    style="width: 175px">
                    <el-option v-for="item in deptList" :label="item" :value="item" />
                </el-select>
                <el-select class="publicCss" v-model.trim="ListInfo.post_name_list" multiple collapse-tags filterable
                    clearable placeholder="岗位" style="width: 175px">
                    <el-option v-for="item in postList" :label="item" :value="item" />
                </el-select>
                <el-select class="publicCss" v-model.trim="ListInfo.first_dept_name" filterable clearable
                    placeholder="一级部门" style="width: 175px">
                    <el-option v-for="item in firstDeptList" :label="item" :value="item" />
                </el-select>
                <el-select class="publicCss" v-model.trim="ListInfo.second_dept_name" filterable clearable
                    placeholder="二级部门" style="width: 175px">
                    <el-option v-for="item in secondDeptList" :label="item" :value="item" />
                </el-select>
                <el-select class="publicCss" v-model.trim="ListInfo.third_dept_name" filterable clearable
                    placeholder="三级部门" style="width: 175px">
                    <el-option v-for="item in thirdDeptList" :label="item" :value="item" />
                </el-select>
                <el-input class="publicCss" style="width: 175px" v-model="ListInfo.label" placeholder="标签"
                    maxlength="100" clearable />
                <el-input class="publicCss" style="width: 175px" v-model="ListInfo.userName" placeholder="姓名"
                    maxlength="100" clearable />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="logsVisible = true">查看日志</el-button>
                    <el-switch v-model="ListInfo.isExitLabel" active-text="有标签" inactive-text="无标签"
                        style="margin-right: 10px;">
                    </el-switch>
                    <el-switch v-model="ListInfo.isDelete" active-text="删除" inactive-text="没删除">
                    </el-switch>
                </div>
                <div>
                    <el-button v-for="item in fastQueryBtn" :key="item" @click="fastQuery(item)">{{ item }}</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="200">
                    <template #default="{ row, $index }">
                        <div style="display: flex">
                            <el-button type="text" @click="handleEdit(row)" v-if="!isDelete">编辑标签</el-button>
                            <el-button type="text" @click="handleCopy(row)" :disabled="!isExitLabel"
                                v-if="!isDelete">复制标签给</el-button>
                            <el-button type="text" @click="handleDelete(row.ddUserId)" v-if="!isDelete">删除记录</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="编辑标签" :visible.sync="tagVisible" width="40%" v-dialogDrag :close-on-click-modal="false">
            <div style="display: flex;flex-wrap: wrap;align-items: center;">
                <div style="display: flex;flex-wrap: wrap;align-items: center;"
                    v-if="tagInfo.labelList && tagInfo.labelList.length > 0">
                    <el-tag v-for="(item, i) in tagInfo.labelList" :key="item" closable class="tagClass" size="medium"
                        @close="close(i)">{{
                            item
                        }}</el-tag>
                </div>
                <div v-else style="margin-right: 10px;">暂无标签</div>
                <el-input class="input-new-tag" v-if="inputVisible" v-model.trim="inputValue" ref="saveTagInput"
                    size="small" @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm">
                </el-input>
                <el-button v-else class="button-new-tag" size="small" @click="showInput">新增标签</el-button>
            </div>
            <div style="display: flex;justify-content: center;margin-top: 10px;">
                <el-button @click="tagVisible = false">取消</el-button>
                <el-button type="primary" @click="handleEditTag" v-throttle="2000">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog title="日志" :visible.sync="logsVisible" width="60%" v-dialogDrag>
            <logs v-if="logsVisible" />
        </el-dialog>

        <el-dialog title="复制标签给" :visible.sync="copyVisible" width="20%" v-dialogDrag>
            <div style="display: flex;justify-content: center;align-items: center;">
                <yhUserselectors :value.sync="copyInfo.copyToDDUserIdList" clearable v-if="copyVisible" />
            </div>
            <div style="display: flex;justify-content: center;margin-top: 10px;">
                <el-button @click="copyVisible = false">取消</el-button>
                <el-button type="primary" @click="handleCopySave" v-throttle="2000">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import yhUserselectors from '@/components/YhCom/yh-userselectors.vue'
import {
    GetSimplePostContrast,
    EditSimplePostContrastLabel,
    CopySimplePostContrastLabel,
    DeleteSimplePostContrast,
    GetAllDept,
    GetAllPost,
    GetFirstDept,
} from '@/api/admin/deptuser'
import logs from "./component/logs.vue";
const tableCols = [
    { sortable: 'custom', width: '300', align: 'left', prop: 'full_name', label: '部门', },
    { sortable: 'custom', width: '100', align: 'left', prop: 'post_name', label: '岗位', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'status', label: '状态', formatter: (row) => row.status == 0 ? '在职' : row.status == 1 ? '离职' : row.status },
    { sortable: 'custom', width: '80', align: 'center', prop: 'avatar', label: '头像', type: 'images' },
    { sortable: 'custom', width: '80', align: 'left', prop: 'userName', label: '人员', type: 'ddTalk', ddInfo: { prop: 'ddUserId', type: 5, name: 'userName' } },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'label', label: '标签', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, inputYunhan, logs, yhUserselectors
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间,
                isExitLabel: true,
                isDelete: false,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            tagVisible: false,
            tagInfo: {
                ddUserId: null,
                label: null,
                labelList: [],
            },
            inputVisible: false,
            inputValue: '',
            logsVisible: false,
            copyVisible: false,
            copyInfo: {
                ddUserId: null,
                label: '',
                copyToDDUserIdList: [],
            },
            deptList: [],
            postList: [],
            firstDeptList: [],
            secondDeptList: [],
            thirdDeptList: [],
            fastQueryBtn: ['仓储部', '财务部', 'IT部', '采购部', '运营部', '审单部', '综合处理部', '视觉设计部', '新媒体运营部', '人力资源部'],
            isExitLabel: true,
            isDelete: false,
        }
    },
    async mounted() {
        this.init()
        await this.getList()
    },
    methods: {
        fastQuery(e) {
            this.$set(this.ListInfo, 'dept', e)
            this.getList('search')
        },
        async init() {
            const { data } = await GetAllDept()
            this.deptList = data?.list
            const { data: postList } = await GetAllPost()
            this.postList = postList?.list
            const { data: firstDeptList } = await GetFirstDept({ lvl: 1 })
            this.firstDeptList = firstDeptList?.list
            const { data: secondDeptList } = await GetFirstDept({ lvl: 2 })
            this.secondDeptList = secondDeptList?.list
            const { data: thirdDeptList } = await GetFirstDept({ lvl: 3 })
            this.thirdDeptList = thirdDeptList?.list
        },
        async handleCopySave() {
            if (this.copyInfo.copyToDDUserIdList.length == 0) return this.$message.error('请选择用户')
            const { success } = await CopySimplePostContrastLabel(this.copyInfo)
            if (!success) return
            this.copyVisible = false
            this.getList()
        },
        async handleEditTag() {
            if (this.tagInfo.labelList == 0) return this.$message.error('标签不能为空')
            this.tagInfo.labelList = Array.from(new Set(this.tagInfo.labelList))
            this.tagInfo.label = this.tagInfo.labelList.join(',')
            const { success } = await EditSimplePostContrastLabel(this.tagInfo)
            if (!success) return
            this.tagVisible = false
            this.getList()
            this.$message.success('编辑成功')
        },
        close(i) {
            this.tagInfo.labelList.splice(i, 1)
        },
        handleInputConfirm() {
            let inputValue = this.inputValue;
            if (inputValue) {
                this.tagInfo.labelList.push(inputValue);
            }
            this.inputVisible = false;
            this.inputValue = '';
        },
        showInput() {
            this.inputVisible = true;
            this.$nextTick(_ => {
                this.$refs.saveTagInput.$refs.input.focus();
            });
        },
        handleEdit(row) {
            const res = JSON.parse(JSON.stringify(row))
            this.tagInfo = {
                ddUserId: res.ddUserId,
                label: res.label,
                labelList: res.label?.split(',') || []
            }
            this.tagVisible = true
        },
        handleCopy(row) {
            const res = JSON.parse(JSON.stringify(row))
            this.copyInfo = {
                ddUserId: res.ddUserId,
                label: res.label,
                copyToDDUserIdList: []
            }
            this.copyVisible = true
        },
        handleDelete(ddUserId) {
            this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await DeleteSimplePostContrast({ ddUserId })
                if (!success) return
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
                this.getList()
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.isDelete = this.ListInfo.isDelete
            this.isExitLabel = this.ListInfo.isExitLabel
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetSimplePostContrast(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}

.tagClass {
    cursor: pointer;
    margin: 0 10px 10px 0;
}

.input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
}

.button-new-tag {
    height: 30px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
}
</style>
