<template>
    <my-container v-loading="pageLoading" style="height: 100%">
        <el-tabs v-model="activeName" style="height: 90%">
            <el-tab-pane label="开始同步" name="first1" style="height: 100%">
                <el-row>
                    <el-col style="height: 380px;">
                        <el-input v-model="syncProCodes" type="textarea" :autosize="{ minRows: 20, maxRows: 20 }"
                            maxlength="2000" auto-complete="off" show-word-limit
                            placeholder="请填写产品ID，每个产品ID换行或逗号分隔，单次请勿超过100个产品ID。">
                        </el-input>
                    </el-col>
                </el-row>
                <el-row style="padding-top: 20px;">
                    <el-col :span="20">
                        <div>点击确认同步后，将在半小时内将产品同步到ERP，可在产品管理中查询。</div>
                    </el-col>
                    <el-col :span="4">
                        <!-- <el-button>取消</el-button>&nbsp;&nbsp; -->
                        <el-button type="primary" @click="onproductsyncbyidOk">确认同步</el-button>
                    </el-col>
                </el-row>

            </el-tab-pane>
            <!-- <el-tab-pane label="同步记录" name="first2" style="height: 100%" lazy>
                <el-row>
                    <el-col style="height: 430px;">
                        <vxetablebase :id="'productsyncbyid20240118'" :tableData='loglist' :tableCols='tableCols'
                            :tableHandles='tableHandles' :loading='listLoading' :border='true' :that="that"
                            ref="vxetable" />
                    </el-col>
                </el-row>
                <el-row style="padding-top: 30px;">
                    <el-col :span="18">
                        <div>&nbsp;</div>
                    </el-col>
                    <el-col :span="6">
                    </el-col>
                </el-row>
            </el-tab-pane> -->
        </el-tabs>
    </my-container>
</template>
  
<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import cesTable from "@/components/Table/table.vue";
import { addSyncShopGoodsJob_V2_TimeRange2, getSyncShopGoodsJob_V2_TimeRange } from '@/api/operatemanage/base/product'
const tableCols = [
    { istrue: true, align: 'center', prop: 'clickTime', label: '点击时间', width: '150', },
    { istrue: true, align: 'center', prop: 'startTime', label: '开始同步时间', width: '150', },
    { istrue: true, align: 'center', prop: 'endTime', label: '结束同步时间', width: '150', },
    { istrue: true, prop: 'proCode', label: '产品ID', width: '150', },
    { istrue: true, prop: 'syncResult', label: '同步结果', width: '100', },
    { istrue: true, prop: 'syncDes', label: '同步描述', width: '150', },
];
const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
];
export default {
    name: "productsyncbyid",
    components: {
        cesTable, MyContainer, MyConfirmButton, vxetablebase
    },
    data() {
        return {
            that: this,
            pageLoading: false,
            activeName: "first1",
            syncProCodes: "",
            loglist: [],
            listLoading: false,
            tableCols: tableCols,
            tableHandles: tableHandles,
        };
    },
    created() {
    },
    async mounted() {
    },
    methods: {
        async loadData() {

        },
        async onproductsyncbyidOk() {
            var res = await addSyncShopGoodsJob_V2_TimeRange2({ skuIds: this.syncProCodes });
            if (res.success) {
                this.$message({
                    type: 'success', message: '操作成功!请在同步记录中查看同步情况。'
                });
            }
        },
    },
};
</script>
  
<style lang="scss" scoped></style>
  