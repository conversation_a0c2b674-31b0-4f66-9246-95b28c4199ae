<template>
  <container v-loading="pageLoading">
     <ces-table ref="table" :that='that' :isIndex='true'  @sortchange='sortchange' :isSelectColumn='false'
        :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading"> 
     </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
  </container>
</template>

<script>
import {queryFStockGoodsCode} from '@/api/inventory/abnormal'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import { formatYesornoBool,formatWarehouse,formatmoney,formatTime,formatNoLink,formatSecondToHour} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'goodsCode',label:'商品编码', width:'100',sortable:'custom'},
      {istrue:true,prop:'goodsName',label:'商品名称', width:'200',sortable:'custom'},
      {istrue:true,prop:'brandName',label:'采购员', width:'70'},
      {istrue:true,prop:'buyNo',label:'采购单号', width:'80'},
      {istrue:true,prop:'warehousingNo',label:'入库单号', width:'80',sortable:'custom'},
      {istrue:true,prop:'wms_co_id',label:'入库仓', width:'80',sortable:'custom',formatter:(row)=> { return row.wms_Name}},
      {istrue:true,prop:'purchaseDate',label:'采购时间', width:'150'},
      {istrue:true,prop:'warehousingDate',label:'入库时间', width:'150',sortable:'custom'},
      {istrue:true,prop:'inTransit',label:'在途时长', width:'100',sortable:'custom',formatter:(row)=>formatSecondToHour(row.inTransit)},
      {istrue:true,prop:'qtyUsable',label:'入库时仓库可用数', width:'150',sortable:'custom'},
     ];
const tableHandles=[];
export default {
  name: 'Roles',
  components: {cesTable, container},
  data() {
    return {
      that:this,
      formatTime:formatTime,
      filter: {
        brandId:null, 
        year:null,
        month:null,
      },
      list: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      pager:{OrderBy:"",IsAsc:false},
      summaryarry:{},
      selids: [],
      listLoading: false, 
      pageLoading: false,
    }
  },
  methods: {
    async onSearch(brandId,year,month) {
        this.filter.brandId=brandId
        this.filter.year=year
        this.filter.month=month     
        this.$refs.pager.setPage(1)
        this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      this.listLoading = true
      const res = await queryFStockGoodsCode(params)
      this.listLoading = false
      if (!res?.success)return
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.getlist();
    },
  }
}
</script>
