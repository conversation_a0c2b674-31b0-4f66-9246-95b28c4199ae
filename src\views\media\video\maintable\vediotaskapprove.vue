<template>
  <my-container v-loading="pageLoading">
    <div class="dspshsp">
      <div class="dspshspbt"><span>视频审核</span></div>
      <div class="dspshmc">
        <div style="width: 1540px; margin: 0 auto; color: #555">
          <div>
            <span>任务名称：</span>{{ videoTaskId }}<span>&nbsp;&nbsp;|&nbsp;&nbsp;</span>
            <span>{{ productShortName }}</span> <span>&nbsp;&nbsp;&nbsp;&nbsp; 参考视频{{ ckindexstring }}</span>
            <div style="display: inline-block; float: right">
              <span>审核结果：</span>
              <template>
                <span v-if="approvedStatusEnum == 4" style="color: #eb0000">重拍&nbsp;&nbsp;</span>
                <span v-else-if="approvedStatusEnum == 2" style="color: #04a600">通过&nbsp;&nbsp;</span>
                <span v-else-if="approvedStatusEnum == 3" style="color: #e6a23c">补拍&nbsp;&nbsp;</span>
                <span v-else>待审&nbsp;&nbsp;</span>
              </template>
              <span>审核人：</span><span>{{ approvedstatusname }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
              <span>审核时间：</span><span>{{ isformatTime(approvedstatustime) }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
              <span> <el-checkbox v-model="isComplateCheck" :checked-value="1" :unchecked-value="0"
                  border>隐藏已通过</el-checkbox>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
              <span style="position:relative;top:-2px"><el-button size="mini" type="primary"
                  @click="bacthdownVideo">一键下载所有视频</el-button></span>
            </div>
          </div>
        </div>
      </div>
      <el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" :disabled="islook">
        <div class="dsppdkz">
          <div class="pdzh">
            <div v-show="!(isComplateCheck && item.uploadapproveStatusEnum == 2)" class="pdkh" style="padding-bottom: 6px"
              v-for="(item, index) in approvedlist" :key="item.cuteid">
              <div class="pdbt">
                <span>{{ item.cutetitle }}</span><span style="float: right">
                  <!-- 下载按钮 -->
                  <el-link type="primary" v-if="item.uploadapproveStatusEnum == 2"
                    @click="downloadFile({ url: item.uploadurl, fileName:  '参考视频'+ckindexstring+'-'+item.cutetitle })"><i style="font-weight: bold"
                      class="el-icon-download"></i></el-link></span>
              </div>
              <div style="box-sizing: border-box; padding: 5px 5px">
                <div class="ckpd">
                  <div class="ckpdv divcenter"><!-- 剪切的视频放在这里 -->
                    <img style="width: 100%; height: 100%; object-fit: contain;" :src="item.cuteimg == null ? imagedefault : item.cuteimg" controls
                      @click="playVideo(item.cuteurl)" />
                  </div>
                  <div>
                    <el-button style="width: 100%" size="mini" type="danger" plain @click="delCuteInfo(item.cuteid)"
                      :disabled="!checkPermission('vedioTask-shsp-scpd')">删除</el-button>
                  </div>
                </div>
                <div class="pspd">
                  <div class="ckpdv divcenter"><!-- 上传的视频放在这里 -->
                    <img style="width: 100%; height: 100%; object-fit: contain;"  :src="item.uploadimg == null ? imagedefault : item.uploadimg" controls
                      @click="playVideo(item.uploadurl)" />
                  </div>
                  <div>
                    <el-upload ref="upload" class="inline-block" style="width: 100%;" action="#" :auto-upload="true"
                      :multiple="false" :limit="1" :show-file-list="false" :http-request="UpSuccessload"
                      :data="{ 'cuteid': item.cuteid }" :file-list="filelist" :accept="vediotype">
                      <div class="flexwidth">
                        <el-button size="mini" style="width: 100%; height: auto;" type="primary"
                          icon="el-icon-upload">上&nbsp;&nbsp;传</el-button>
                      </div>
                    </el-upload>
                  </div>
                </div>
                <div class="shcz">
                  <div class="ckpdvv">
                    <div>
                      <span>片段 &nbsp;:&nbsp;1</span>
                    </div>
                    <div>
                      <el-button style="width: 100%; margin: 10px 0" size="mini" type="primary"
                        :disabled="item.uploadapproveStatusEnum == 2 || !checkPermission('vedioTask-list-tgbh')"
                        @click="approverejectaction(item)">驳回</el-button>
                    </div>
                    <div>
                      <el-button style="width: 100%" size="mini" type="primary"
                        :disabled="item.uploadapproveStatusEnum == 2 || !checkPermission('vedioTask-list-tgbh')"
                        @click="approveaction(item.uploadid)">通过</el-button>
                    </div>
                  </div>
                  <div>
                    <el-popover placement="right" width="500" trigger="click">
                      <!-- <div>{{ item.uploadlist }}</div> -->
                      <div class="wjnrq"
                        style="display: flex; max-height: 300px; overflow-y: auto; flex-direction: column;">
                        <div v-for="(i, num) in item.uploadlist" :key="i.uid"
                          style="display: flex; flex-direction: row; width: 100%; ">
                          <el-tooltip effect="dark" :content="i.fileName" placement="left">
                            <div class="wjdnr" :style="num == 0 ? 'color:#6c9afb' : ''" style="width: 200px;"
                              @click="playVideo(i.url)"> {{ i.fileName }} </div>
                          </el-tooltip>
                          <div style="display: flex; flex-direction: row; margin-left: auto; justify-content: end;">
                            <div style="margin-right: 20px;"> <template>
                                <span style="color: #04a600;" v-if="i.statusEnum == 2"> {{ i.approvestatus }} </span>
                                <span style="color: #eb0000" v-else-if="i.statusEnum == 3"> {{ i.approvestatus }} </span>
                                <span v-else> {{ i.approvestatus }} </span>
                              </template>
                            </div>
                            <div style="margin-right: 20px;">上传时间:{{ i.uploadtime }}</div>
                            <!-- 设置默认 -->
                            <div class="dnrxz" style="margin: 0 10px;" v-if="checkPermission('vedioTask-shsp-szmr')">
                              <el-link :underline="false" type="primary" @click="setDefault(i.id)"><i
                                  class="el-icon-success" :style="num == 0 ? 'color:#67C23A' : 'color:#868a8f'"></i></el-link>
                            </div>
                            <!-- 下载 -->
                            <div class="dnrxz" style="margin: 0 10px;" v-if="checkPermission('vedioTask-shsp-scsc')">
                              <el-link :underline="false" type="primary" @click="downfile(i)"><i
                                  class="el-icon-download"></i></el-link>
                            </div>
                            <!-- 删除 -->
                            <div class="dnrsc" v-if="checkPermission('vedioTask-shsp-scsc')">
                              <el-link :underline="false" type="danger" @click="delUploadInfo(i.id)"><i
                                  class="el-icon-delete"></i></el-link>
                            </div>
                          </div>
                        </div>
                      </div>
                      <el-button slot="reference" style="width: 100%" size="mini" plain>已传素材：<span>{{ item.uploadsum
                      }}</span></el-button>
                    </el-popover>
                  </div>
                </div>
              </div>
              <div class="pdsx"> 上传人：<span>{{ item.uploadname }}</span><span>&nbsp;&nbsp;{{ isformatTime(item.uploadtime)
              }}</span> </div>
              <div style="box-sizing: border-box; padding: 0 12px">
                <el-collapse accordion>
                  <el-collapse-item>
                    <div slot="title">
                      <span>审核结果/意见：</span>
                      <template>
                        <span style="color: #04a600" v-if="item.uploadapproveStatusEnum == 2"> <i
                            class="el-icon-success"></i>&nbsp;{{ item.uploadapprovestatus }} </span>
                        <span style="color: #eb0000" v-else-if="item.uploadapproveStatusEnum == 3">
                          <i class="el-icon-error"></i>&nbsp;{{ item.uploadapprovestatus }} </span>
                        <span style="color: #409eff" v-else>
                          <i class="el-icon-question"></i>&nbsp;{{ item.uploadapprovestatus }} </span>
                      </template>
                    </div>
                    <div style="max-height: 300px;overflow-y: auto;">
                      <!-- 评论1 star -->
                      <div style="margin-top: 6px " v-for="(info) in item.reviews" :key="info.id">
                        <div style="  display: inline-block;  width: 100%;  color: #555;  ">
                          <div
                            :style="info.level == 1 ? 'display: inline-block; max-width: 50%' : 'display: inline-block; max-width: 50%;;padding-left: 20px'">
                            {{ info.level == 1 ? "审核人：" + info.name : info.name + " to：" + info.toname }} </div>
                          <div style="display: inline-block; width: 45%;">&nbsp;&nbsp;{{ isformatTime(info.time) }}</div>
                          <div style="display: inline-block; width: 5%">
                            <el-popover :key="info.id + refleshtime" placement="right" width="400" trigger="click">
                              <pastimgInput minHeight="50px" v-model="info.remark" placeholder="请输入内容" :keyid="info.id">
                              </pastimgInput>
                              <el-link slot="reference" :underline="false"><i class="el-icon-s-comment"></i></el-link>
                              <div class="dialog-footer" style="width: 100%; margin-top: 10px; display: flex;">
                                <div style="justify-content: end; margin-left: auto;">
                                  <el-button type="primary" :disabled="bhsumbit" @click="repaymarkclick(info, index)">回
                                    复</el-button>
                                </div>
                              </div>
                            </el-popover>
                          </div>
                        </div>
                        <div v-html="info.mark" class="vhtml" :style="info.level == 1 ? '' : 'margin-left: 20px; '"
                          @click="editorClick"></div>
                      </div>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </div>
        </div>
      </el-form>
    </div>
    <!-- 驳回审核意见输入 -->
    <el-dialog title="驳回意见" :visible.sync="bhmarkVisible" width='30%' v-dialogDrag append-to-body>
      <span>{{ curshpdtitle + " : " + curshtitle }} </span>
      <pastimgInput v-model="bhmark" placeholder="请输入内容" :keyid="'all'"></pastimgInput>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="bhmarkVisible = false">取 消</el-button>
          <el-button type="primary" :disabled="bhsumbit" @click="bhmarkclick()">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-image-viewer v-if="showGoodsImage" :initialIndex="imgindex" :url-list="imgList" :wrapperClosable="false"
      :on-close="closeFunc" style="z-index:9999;" />
    <!--视频播放-->
    <el-dialog title="视频播放" :visible.sync="dialogVisible" width="50%" @close="closeVideoPlyer" :append-to-body="true"
      v-dialogDrag>
      <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeVideoPlyer">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import { formatTime } from "@/utils";
import ElImageViewer from '@/views/media/shooting/imageviewer.vue'//图片查看
import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
import { delTaskCuteVideo } from '@/api/media/video';
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
import {
  getVideoTaskApprovedInfo, deleteUploadVideoByIdAsync, setUploadVideoDefault,
  approveUploadVideoByIdAsync, rejectUploadVideoByIdAsync, saveUploadTeskVideoAsync,
  replyCommentInfo
} from "@/api/media/vediotask";
import pastimgInput from '@/views/media/video/maintable/commpents/pastimgInput.vue'
function pause(msec) {
  return new Promise(
    (resolve, reject) => {
      setTimeout(resolve, msec || 500);
    }
  );
}
export default {
  props: {
    videoTaskId: { type: Number, default: 0 },//限制上传的数量 
    ckindex: { type: Number, default: 0 },
    islook: { type: Boolean, default: false },
    closefun: { type: Function, default: null },
  },
  components: { ElImageViewer, videoplayer, MyContainer, pastimgInput },
  data() {
    return {
      vediotype: ".mp4,.mov,.vedio,.av,.wmv,.mpg,.mpeg,.rm,.flv,.swf",
      imgindex: 0,
      productShortName: null,
      approvedstatus: null,
      approvedstatustime: null,
      approvedstatusname: null,
      approvedlist: [],
      approvedStatusEnum: 0,
      arrmark: [],
      videoplayerReload: false,
      dialogVisible: false,
      videoplayerReload: false,
      showGoodsImage: false,
      pageLoading: false,
      ckindexstring: null,
      bhmarkVisible: false,
      bhsumbit: false,
      bhmark: null,
      isComplateCheck: false,
      curshtitle: null,
      curshpdtitle: null,
      curuploadid: 0,
      refleshtime: 0,
      filelist: [],
      imagedefault: require('@/assets/images/detault.jpeg'),
      atfterUplaodData: {}
    };
  },
  async mounted() {
    switch (this.ckindex) {
      case 1:
        this.ckindexstring = "一";
        break;
      case 2:
        this.ckindexstring = "二";
        break;
      case 3:
        this.ckindexstring = "三";
        break;
      case 4:
        this.ckindexstring = "四";
        break;
      case 5:
        this.ckindexstring = "五";
        break;
    }
    await this.getPdCuteinfo();
  },
  methods: {
    downloadFile(data){
        this.$confirm('1、跳转打开，将直接预览打开，可右键另存保存，可以直接看到进度。<br/>2、直接下载，是静默下载，文件下载完成后，会提示您进行保存，过程中，不要反复去点击下载。', " 请选择下载方式？", {
        distinguishCancelAndClose: true,
        dangerouslyUseHTMLString: true,
        distinguishCancelAndClose: true,
        confirmButtonText: '直接下载',
        cancelButtonText: '跳转打开',
        beforeClose: async(action, instance, done) => {
            if (action === 'cancel') {
                await this.downfile(data, 2);
            }else if (action === 'confirm') {
                await this.downfile(data, 1);
                this.$message.success("正在下载，请稍等。")
                done();
            } else {
              done();
            }
          }
        })
    },
    async bacthdownVideo() {
      debugger
      for (var num in this.approvedlist) {
        if (this.approvedlist[num].uploadapproveStatusEnum == 2) {
          //'参考视频'+ckindexstring+''-+item.cutetitle
          await this.downfile({ url: this.approvedlist[num].uploadurl, fileName: '参考视频' + this.ckindexstring +'-' +this.approvedlist[num].cutetitle});
          await pause(500);
        }
      }
    },
    //上传方法
    async UpSuccessload(item) {
      this.pageLoading = true;
      await this.AjaxFile(item.file, 0, "");

      if (this.atfterUplaodData != null) {
        const form = new FormData();
        this.atfterUplaodData.fileName = item.file.name;
        form.append("upfile", JSON.stringify(this.atfterUplaodData));
        form.append("videotaskid", this.videoTaskId);
        form.append("cuteid", item.data.cuteid);
        const res = await saveUploadTeskVideoAsync(form);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.filelist = [];
          await this.getPdCuteinfo();
        }
      }
      this.pageLoading = false;
    },
    async AjaxFile(file, i, batchnumber) {
      var name = file.name; //文件名
      var size = file.size; //总大小shardSize = 2 * 1024 * 1024,
      var shardSize = 15 * 1024 * 1024;
      var shardCount = Math.ceil(size / shardSize); //总片数
      if (i >= shardCount) {
        return;
      }
      //计算每一片的起始与结束位置
      var start = i * shardSize;
      var end = Math.min(size, start + shardSize);
      //构造一个表单，FormData是HTML5新增的
      i = i + 1;
      var form = new FormData();
      form.append("data", file.slice(start, end)); //slice方法用于切出文件的一部分
      form.append("batchnumber", batchnumber);
      form.append("fileName", name);
      form.append("total", shardCount); //总片数
      form.append("index", i); //当前是第几片

      const res = await xMTVideoUploadBlockAsync(form);
      if (res?.success) {
        this.percentage = (i * 100 / shardCount).toFixed(2);

        if (i == shardCount) {
          this.atfterUplaodData = res.data;
        } else {
          await this.AjaxFile(file, i, res.data);
        }
      } else {
        this.$message({ message: res?.msg, type: "warning" });
        this.ScpdLoading = false;

      }
    },
    //设置默认
    async setDefault(uploadid) {
      this.$confirm("确认设置为默拍摄片段？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await setUploadVideoDefault({ uploadid: uploadid });
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          await this.getPdCuteinfo();
        }
      });
    },
    //移除文件
    async removefile(file) {

    },
    //下载文件
    async downfile(file, type) {
      if(type==2){
        window.open(file.url,'_blank')
        return;
      }
      var xhr = new XMLHttpRequest();
      xhr.open('GET', file.url, true);
      xhr.responseType = 'arraybuffer'; // 返回类型blob
      xhr.onload = function () {
        if (xhr.readyState === 4 && xhr.status === 200) {
          let blob = this.response;
          console.log(blob);
          // 转换一个blob链接
          // 注: URL.createObjectURL() 静态方法会创建一个 DOMString(DOMString 是一个UTF-16字符串)，
          // 其中包含一个表示参数中给出的对象的URL。这个URL的生命周期和创建它的窗口中的document绑定
          let downLoadUrl = window.URL.createObjectURL(new Blob([blob], { type: 'video/mp4' }));
          // 视频的type是video/mp4，图片是image/jpeg
          // 01.创建a标签
          let a = document.createElement('a');
          // 02.给a标签的属性download设定名称
          a.download = file.fileName;
          // 03.设置下载的文件名
          a.href = downLoadUrl;
          // 04.对a标签做一个隐藏处理
          a.style.display = 'none';
          // 05.向文档中添加a标签
          document.body.appendChild(a);
          // 06.启动点击事件
          a.click();
          // 07.下载完毕删除此标签
          a.remove();
        };
      };
      xhr.send();
    },
    getCaption(obj, state) {
      var index = obj.lastIndexOf("\.");
      if (state == 0) {
        obj = obj.substring(0, index);
      } else {
        obj = obj.substring(index + 1, obj.length);
      }
      return obj;
    },

    async editorClick(e) {
      if (e.target.nodeName.toLocaleLowerCase() == 'img') {
        this.showGoodsImage = true;
        this.imgList = [];
        if (e.target.src) {
          this.imgList.push(e.target.src);
        }
        else {
          this.imgList.push(this.imagedefault);
        }
      }
    },
    async getPdCuteinfo() {
      if (this.videoTaskId > 0) {
        this.pageLoading = true;
        var res = await getVideoTaskApprovedInfo({ videoTaskId: this.videoTaskId, ckindex: this.ckindex });
        this.pageLoading = false;
        if (res?.success) {
          this.refleshtime = this.refleshtime + 1;
          this.productShortName = res.data.productShortName;
          this.approvedstatus = res.data.approvedstatus;
          this.approvedstatustime = res.data.approvedstatustime;
          this.approvedstatusname = res.data.approvedstatusname;
          this.approvedlist = res.data.approvedlist;
          this.approvedStatusEnum = res.data.approvedStatusEnum;
        }
      }
    },
    playVideo(videoUrl) {
      this.videoplayerReload = false;
      this.videoplayerReload = true;
      this.dialogVisible = true;
      this.videoUrl = videoUrl;
    },
    isformatTime(val) {
      return val == null ? null : formatTime(val, "YY-MM-DD HH:mm")
    },
    //删除剪辑
    delCuteInfo(id) {
      this.$confirm("确认删除当前片段吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await delTaskCuteVideo({ cuteId: id })
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          await this.getPdCuteinfo();
        }
      });
    },
    //删除上传
    delUploadInfo(id) {
      this.$confirm("确认删除当前拍摄视频吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await deleteUploadVideoByIdAsync({ videoId: id })
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          await this.getPdCuteinfo();
        }
      });
    },
    //审核通过，
    approveaction(id) {
      if (id < 1) {
        this.$message({ message: '未上传视频，请勿操作', type: "info" });
        return;
      }
      this.$confirm("确认审核通过？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await approveUploadVideoByIdAsync({ uploadid: id })
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          await this.getPdCuteinfo();
        }
      });
    },
    //审核驳回
    approverejectaction(row) {
      this.curuploadid = row.uploadid;
      if (row.uploadid < 1) {
        this.$message({ message: '未上传视频，请勿操作', type: "info" });
        return;
      }
      this.curshtitle = row.cutetitle;
      this.curshpdtitle = "片段1";
      this.bhmark = null;
      this.bhmarkVisible = true;
    },
    //驳回提交
    bhmarkclick() {
      if (this.curuploadid < 1) {
        this.$message({ message: '未上传视频，请勿操作', type: "info" });
        return;
      }
      this.$confirm("确认驳回？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await rejectUploadVideoByIdAsync({ uploadid: this.curuploadid, Comment: this.bhmark })
        if (res?.success) {
          this.bhmark = null;
          this.bhmarkVisible = false;
          this.$message({ message: '操作成功', type: "success" });
          await this.getPdCuteinfo();
        }
      });
    },
    //提交最后的审核结果
    submitform() {

    },
    // 关闭图片
    async closeFunc() {
      this.showGoodsImage = false;
    },
    async closeVideoPlyer() {
      this.dialogVisible = false;
      this.videoplayerReload = false;
    },
    //消息回復
    async repaymarkclick(info, index, id) {
      var res = await replyCommentInfo({ uploadid: info.uploadid, toId: info.id, Comment: info.remark })
      if (res?.success) {
        this.$message({ message: '操作成功', type: "success" });
        await this.getPdCuteinfo();
      }
    },

  },
};
</script>

<style scoped lang="scss">
::v-deep .dspshsp {
  background-color: #fff;
}

::v-deep .dspshsp .dspshspbt {
  height: 55px;
  background-color: rgb(255, 255, 255);
  box-sizing: border-box;
  padding: 20px 35px;
  font-size: 16px;
  color: #666;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
}

::v-deep .dspshsp .dspshmc {
  min-width: 1650px;
  height: 55px;
  background-color: rgb(255, 255, 255);
  box-sizing: border-box;
  padding: 20px 0;
  font-size: 16px;
  color: #666;
  position: relative;
  left: -6px;
  box-shadow: 0px 3px 5px #eeeeee;
  z-index: 999;
}

::v-deep .dsppdkz {
  min-width: 1650px;
  height: 710px;
  background-color: #f3f6f7;
  box-sizing: border-box;
  padding: 15px 0px;
  overflow: auto;
}

::v-deep .dspshsp .pdzh {
  width: 1550px;
  flex-wrap: wrap;
  overflow: hidden;
  /* background-color: rgb(0, 94, 94); */
  /* box-sizing: border-box; */
  padding: 0 35px;
  margin: 0 auto;
  display: flex;
}

::v-deep .dspshsp .pdkh {
  width: 300px;
  background-color: rgb(255, 255, 255);
  margin: 5px;
  /* display: inline-block; */
  /* border: 1px solid #dcdfe6; */
}

::v-deep .pdbt {
  width: 300px;
  height: 38px;
  line-height: 20px;
  font-size: 14px;
  color: #666;
  box-sizing: border-box;
  padding: 10px 12px 5px 12px;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  margin-bottom: 5px;
  background-color: rgb(255, 255, 255);
}

::v-deep .pdsx {
  width: 300px;
  line-height: 20px;
  font-size: 14px;
  color: #888;
  box-sizing: border-box;
  padding: 5px 12px;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  background-color: rgb(255, 255, 255);
}

::v-deep .dspshsp .ckpd,
::v-deep .pspd {
  width: 80px;
  /* background-color: bisque; */
  margin: 0 5px;
  display: inline-block;
}

::v-deep.dspshsp .shcz {
  width: 100px;
  /* background-color: bisque; */
  margin: 0 5px;
  display: inline-block;
}

::v-deep .dspshsp .ckpdv {
  width: 100%;
  height: 120px;
  /* background-color: rgb(196, 255, 204); */
  margin-bottom: 5px;
  box-sizing: border-box;
  border: 1px solid #dcdfe6;
}

::v-deep .dspshsp .ckpdvv {
  width: 100%;
  height: 120px;
  /* background-color: rgb(196, 255, 204); */
  margin-bottom: 5px;
  font-size: 14px;
  color: #666;
  box-sizing: border-box;
  padding: 8px 10px;
}

::v-deep .dspshsp .qxtj {
  height: 80px;
  background-color: rgb(255, 255, 255);
  box-sizing: border-box;
  padding: 20px 60px;
  border: 1px solid #dcdfe6;
  border-right: 0px;
  border-bottom: 0px;
  border-left: 0px;
}

.wjdnr {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}


::v-deep .divcenter {
  width: 100%;
  background-color: rgb(255, 255, 255);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

::-webkit-scrollbar {
  width: 5px;
  height: 10px;
}

.vhtml {
  word-break: break-all;
  white-space: normal;
}</style>