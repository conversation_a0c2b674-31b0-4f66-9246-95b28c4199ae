<template>
    <MyContainer>
      <template #header>
        <div class="top">
          <el-date-picker v-model="ListInfo.calculateMonthArr" unlink-panels range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期"   type="monthrange" style="width: 250px;margin-right: 5px;" :clearable="false"
            :value-format="'yyyy-MM'" >
          </el-date-picker>
          <el-select v-model="ListInfo.type" placeholder="类型" class="publicCss" clearable @change="changeType">
            <el-option v-for="item in typeList" :key="item" :label="item" :value="item" />
          </el-select>
          <el-select v-model="ListInfo.regionName" placeholder="区域" class="publicCss" clearable multiple collapse-tags>
            <el-option v-for="item in districtList" :key="item" :label="item" :value="item" />
          </el-select>
          <el-select v-model="ListInfo.deptName" placeholder="部门类型" class="publicCss" clearable>
            <el-option v-for="item in sectionList" :key="item" :label="item" :value="item" />
          </el-select>
          <el-select v-model="ListInfo.dept" placeholder="部门" class="publicCss" clearable>
            <el-option v-for="item in deptList" :key="item" :label="item" :value="item" />
          </el-select>
          <el-button type="primary" @click="getList()">查询</el-button>
          <el-button type="primary" @click="downExcel">模板下载</el-button>
          <el-button type="primary" @click="startImport" v-if="checkPermission('ArchiveStatusEditing')">导入</el-button>
          <el-button type="primary" @click="startImport" v-else :disabled="timeCundang">导入</el-button>
          <el-button type="primary" @click="exportExcel('search')">导出</el-button>
          <el-button type="primary" @click="saveBane('search')">存档</el-button>

          <div style="color: red; margin-left: 5px;">
            存档时间：{{timeCundang??'-'}}
          </div>
        </div>
      </template>
      <!-- :footer-cell-class-name="footerCellClassName" -->
      <!-- :footer-row-class-name="footerRowClassName" -->
      <vxe-table
        border
        ref="newtable"
        show-footer
        height="100%"
        :loading="loading"
        :merge-footer-items="mergeFooterItems"
        :footer-data="footerData"
        :span-method="mergeRowMethod"
        :footer-span-method="footerSpanMethod"
        :row-class-name="rowClassName"
        :cell-class-name="cellClassName"


        :row-config="{height: 40}"
        show-overflow
        :data="tableData">
        <vxe-column field="calculateMonth" width="80" title="月份" footer-align="center">
          <template #footer="{ items, _columnIndex, row }">
            <span v-if="mergeColumn?.column?.includes('calculateMonth')" class="display_centered">
              {{ getMergeDisplayValue(row) }}
            </span>
            <span v-else>{{ items[_columnIndex] }}</span>
          </template>
        </vxe-column>
        <vxe-column field="type" width="80" title="类型" footer-align="center">
          <template #footer="{ items, _columnIndex, row }">
            <span v-if="mergeColumn?.column?.includes('type')" class="display_centered">
              {{ getMergeDisplayValue(row) }}
            </span>
            <span v-else>{{ items[_columnIndex] }}</span>
          </template>
        </vxe-column>

        <vxe-column field="regionName" title="区域" width="70" footer-align="center">
          <template #footer="{ items, _columnIndex, row }">
            <span v-if="mergeColumn?.column?.includes('regionName')" class="display_centered">
              {{ getMergeDisplayValue(row) }}
            </span>
            <span v-else>{{ items[_columnIndex] }}</span>
          </template>
        </vxe-column>
        <vxe-column field="deptName" width="100" title="部门类型" footer-align="center">
          <template #footer="{ items, _columnIndex, row }">
            <span v-if="mergeColumn?.column?.includes('deptName')" class="display_centered">
              {{ getMergeDisplayValue(row) }}
            </span>
            <span v-else>{{ items[_columnIndex] }}</span>
          </template>
        </vxe-column>
        <vxe-column field="dept" width="80" title="部门" footer-align="center">
          <template #footer="{ items, _columnIndex, row }">
            <span v-if="mergeColumn?.column?.includes('dept')" class="display_centered">
              {{ getMergeDisplayValue(row) }}
            </span>
            <span v-else>{{ items[_columnIndex] }}</span>
          </template>
        </vxe-column>
        <vxe-column field="total" width="80" title="总人数" ></vxe-column>
        <vxe-column field="lastMonthPersonnel" width="80" title="上月人数" ></vxe-column>
        <vxe-column field="personnelFluctuation" width="80" title="人员增减浮动" :formatter="formatPercentage"></vxe-column>

        <vxe-colgroup title="员工结构人数" width="150">
            <vxe-column field="probationCount" width="70" title="试用期人数"></vxe-column>
            <vxe-column field="regularCount" title="正式人数"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="仓储薪资结构人数" width="150">
            <vxe-column field="regularPieceCount" width="70" title="计件人数"></vxe-column>
            <vxe-column field="regularMonthsalaryCount" title="月薪人数"></vxe-column>

        </vxe-colgroup>

        <vxe-colgroup title="职级在岗人数" width="150">
            <vxe-column field="assistantCount" title="助理人数"></vxe-column>
            <vxe-column field="attacheCount" title="专员人数"></vxe-column>
            <vxe-column field="groupLeaderCount" title="组长人数"></vxe-column>
            <vxe-column field="superviseCount" title="主管人数"></vxe-column>
            <vxe-column field="managerCount" title="经理人数"></vxe-column>
            <vxe-column field="directorCount" title="总监/区负责人"></vxe-column>
            <vxe-column field="generalManager" title="总经理"></vxe-column>
            <vxe-column field="ceoCount" title="总裁"></vxe-column>
        </vxe-colgroup>







        <!-- <vxe-column field="total" width="80" title="总人数" ></vxe-column>
        <vxe-column field="regularCount" width="90" title="正式员工（办公室）" footer-align="left"></vxe-column>
        <vxe-column field="regularPieceCount" width="90" title="计件人数（仓储）" footer-align="left">
            <template #header>
                <span style="color: orange">计件人数（仓储）</span>
            </template>
        </vxe-column>
        <vxe-column field="regularMonthsalaryCount" width="90" title="月薪人数（仓储）" footer-align="left">
            <template #header>
                <span style="color: orange">月薪人数（仓储）</span>
            </template>
        </vxe-column>
        <vxe-column field="probationCount" width="100" title="试用期员工（办公室）" footer-align="left"></vxe-column>
        <vxe-column field="probationPieceCount" width="90" title="计件人数（仓储）" footer-align="left">
            <template #header>
                <span style="color: orange">计件人数（仓储）</span>
            </template>
        </vxe-column>
        <vxe-column field="probationMonthsalaryCount" width="90" title="月薪人数（仓储）" footer-align="left">
            <template #header>
                <span style="color: orange">月薪人数（仓储）</span>
            </template>
        </vxe-column>

        <vxe-column field="assistantCount" width="80" title="助理人数" footer-align="left"></vxe-column>
        <vxe-column field="attacheCount" width="80" title="专员人数" footer-align="left"></vxe-column>
        <vxe-column field="groupLeaderCount" width="80" title="组长人数" footer-align="left"></vxe-column>
        <vxe-column field="superviseCount" width="80" title="主管人数" footer-align="left"></vxe-column>

        <vxe-column field="managerCount" width="80" title="经理人数" footer-align="left"></vxe-column>
        <vxe-column field="directorCount" width="70" title="总监" footer-align="left"></vxe-column>
        <vxe-column field="generalManager" title="总经理/区负责人" width="90" footer-align="left"></vxe-column>
        <vxe-column field="ceoCount" title="总裁" width="70" footer-align="left"></vxe-column> -->
        <vxe-column title="操作" footer-align="left"  width="90" fixed="right">
            <template slot-scope="scope">
              <!-- <el-button type="text" size="mini" v-if="scope.row.deptName.indexOf('小计')==-1" @click="handleEdit(scope.$index, scope.row)">编辑</el-button> -->

              <el-button type="text" size="mini" v-if="scope.row.deptName.indexOf('小计')==-1" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
               <!-- <el-button type="text" size="mini" :disabled="scope.row.status==1" v-else-if="scope.row.deptName.indexOf('小计')==-1" @click="handleEdit(scope.$index, scope.row)">编辑</el-button> -->
               <el-button type="text" size="mini" style="color:red" v-if="scope.row.deptName.indexOf('小计')==-1" @click="handleRemove(scope.$index, scope.row)">删除</el-button>

            </template>
        </vxe-column>
      </vxe-table>
      <template #footer>
        <div class="footer-container">
          <span class="total-count-badge">
            共{{ total || 0 }}条
          </span>
        </div>
      </template>
      <!-- <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template> -->

      <el-drawer title="编辑" :visible.sync="dialogVisibleEdit" size="25%">
        <departmentEdit ref="departmentEdit" v-if="dialogVisibleEdit" :editInfo="editInfo" @search="closeGetlist"
            @cancellationMethod="cancellationMethod" />
      </el-drawer>

      <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
        <span>
            <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
            accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
            <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
            </el-upload>
        </span>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
        </el-dialog>
    </MyContainer>
  </template>

  <script>
  import MyContainer from "@/components/my-container";
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
  import { pickerOptions } from '@/utils/tools';
  import departmentEdit from "./departmentEdit.vue";
  import { downloadLink } from "@/utils/tools.js";
  import dayjs from 'dayjs'
  import checkPermission from '@/utils/permission'
  import { allRegionPersonPage, allRegionPersonArchive, allRegionPersonImport,allRegionPersonRemove } from '@/api/people/peoplessc.js';
  import tableFooterMerge from "@/views/profit/sscManager/tableFooterMerge.js";
  const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '审批状态', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNoInner', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'importTime', label: '商品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'planSendTime', label: '原价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'timePay', label: '现价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight', label: '进货数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderStatus', label: '涨价原因', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'seriesName', label: '添加日期', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCount', label: '涨价日期', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '添加人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '岗位', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '分公司', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人组长', },
  ]
  export default {
    name: "scanCodePage",
    mixins: [tableFooterMerge],
    components: {
      MyContainer, vxetablebase, departmentEdit
    },

    data() {
        const tableData = [
    //   { id: 10001,regionName: '义务', deptName: 'it部门', nickname: 'T1', role: 'Develop', regularCount: 'Man', probationCount: 28, address: 'test abc' },
    //   { id: 10002,regionName: '义务', deptName: '人事部', nickname: 'T2', role: 'Test', regularCount: 'Women', probationCount: 22, address: 'Guangzhou' },
    //   { id: 10002,regionName: '义务', deptName: '小计', nickname: 'T2', role: 'Test', regularCount: 'Women', probationCount: 22, address: 'Guangzhou' },
    //   { id: 10003,regionName: '南昌', deptName: '运营部', nickname: 'T3', role: 'PM', regularCount: 'Man', probationCount: 32, address: 'Shanghai' },
    //   { id: 10004,regionName: '南昌', deptName: 'it部门', nickname: 'T4', role: 'Designer', regularCount: 'Women', probationCount: 23, address: 'test abc' },
    //   { id: 10002,regionName: '南昌', deptName: '小计', nickname: 'T2', role: 'Test', regularCount: 'Women', probationCount: 22, address: 'Guangzhou' },
    //   { id: 10005,regionName: '北京', deptName: '人事部', nickname: 'T5', role: 'Develop', regularCount: 'Women', probationCount: 30, address: 'Shanghai' },
    //   { id: 10006,regionName: '北京', deptName: '运营部', nickname: 'T6', role: 'Designer', regularCount: 'Women', probationCount: 21, address: 'test abc' },
    //   { id: 10002,regionName: '北京', deptName: '小计', nickname: 'T2', role: 'Test', regularCount: 'Women', probationCount: 22, address: 'Guangzhou' },
    //   { id: 10007,regionName: '深圳', deptName: 'it部门', nickname: 'T7', role: 'Test', regularCount: 'Man', probationCount: 29, address: 'test abc' },
    //   { id: 10008,regionName: '深圳', deptName: '人事部', nickname: 'T8', role: 'Develop', regularCount: 'Man', probationCount: 35, address: 'test abc' },
    //   { id: 10002,regionName: '深圳', deptName: '小计', nickname: 'T2', role: 'Test', regularCount: 'Women', probationCount: 22, address: 'Guangzhou' },
    ]
    const footerData = [
    //   {regionName: '办公室合计', deptName: '办公室合计', role: '33', rate: '56' },
    //   {regionName: '仓储合计', deptName: '仓储合计', role: 'bb', rate: '56' },
    //   {regionName: '全区域合计', deptName: '全区域合计', role: 'bb', rate: '1235' }
    ]
    const mergeFooterItems = [
      { row: 0, col: 0, rowspan: 1, colspan: 2 },
      { row: 1, col: 0, rowspan: 1, colspan: 2 },
      { row: 2, col: 0, rowspan: 1, colspan: 2 }
    ]
      return {
        downloadLink,
        dialogVisible: false,
        fileList: [],
        typeList: [],
        districtList: [],
        allDistrictList: [],
        sectionList: [],
        deptList: [],
        timeCundang: '',
        tableData,
        footerData,
        mergeFooterItems,
        somerow: 'type,regionName,calculateMonth',
        mergeColumn: {
          column: ['deptName', 'regionName', 'type', 'calculateMonth', 'dept'], // 需要合并的列
          default: 'deptName' // 默认显示字段
        },
        dialogVisibleEdit: false,
        that: this,
        editInfo: {},
        ListInfo: {
            calculateMonthArr: [dayjs().subtract(0, 'month').format('YYYY-MM'), dayjs().subtract(0, 'month').format('YYYY-MM')]
        //   currentPage: 1,
        //   pageSize: 50,
        //   orderBy: null,
        //   isAsc: false,
        //   startTime: null,//开始时间
        //   endTime: null,//结束时间
        },
        timeRanges: [],
        tableCols,
        summaryarry: {},
        total: 0,
        loading: false,
        pickerOptions,
      }
    },
    async mounted() {
      for (let i = 0; i < 2; i++) {
        await this.getList()
      }
    },
    methods: {
        changeType(e){
          if (!e) {
            this.districtList = [...this.allDistrictList];
            return;
          }
          if (e === '办公室') {
            this.districtList = this.allDistrictList.filter(item => !item.includes('仓'));
          } else {
            this.districtList = this.allDistrictList.filter(item => item.includes('仓'));
          }
        },
        formatPercentage({ cellValue }) {
            if (cellValue === null || cellValue === undefined || cellValue === '') {
                return '0%';
            }
            return cellValue + '%';
        },
        cancellationMethod(){
            this.dialogVisibleEdit = false;
            this.getList();
        },
        //上传文件
        onUploadRemove(file, fileList) {
            this.fileList = []
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            this.dialogVisible = false;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            const form = new FormData();
            form.append("file", item.file);
            // form.append("calculateMonth", this.ListInfo.calculateMonth);
            form.append("isArchive", checkPermission("ArchiveStatusEditing"));
            var res = await allRegionPersonImport(form);
            if (res?.success){
                this.$message({ message: res.msg, type: "success" });

            }
            this.uploadLoading = false
            this.dialogVisible = false;
            await this.getList()
        },
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        //导入弹窗
        startImport() {
            this.fileList = []
            this.dialogVisible = true;
        },
        downExcel(){
            downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250711/1943609185535500288.xlsx', '岗位职级分布导入模板.xlsx');
        },
        async saveBane(){
              this.$confirm('是否存档？存档后不可修改！', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const { data, success } = await allRegionPersonArchive(this.ListInfo)
                    if(!success){
                        return;
                    }
                    this.getList();
                    this.$message.success('保存存档成功！')

                }).catch(() => {
                    // this.$message.error('取消')
                });


        },
        closeGetlist(){
            this.dialogVisibleEdit = false;
            this.getList()
        },
        handleEdit(index, row){
            this.editInfo = row;

            this.dialogVisibleEdit = true;
        },
      async handleRemove(index, row) {
        this.$confirm('是否删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.editInfo = row;
          this.editInfo.calculateMonthStart = this.ListInfo.calculateMonthArr[0]
          this.editInfo.calculateMonthEnd = this.ListInfo.calculateMonthArr[1]
          this.loading = true
          const {data, success} = await allRegionPersonRemove(this.editInfo)
          this.loading = false
          if (success) {
            this.$message.success('删除成功')
            this.getList();
          } else {
            this.$message.error('删除失败')
          }
        }).catch(() => {

        });


      },
        exportExcel(){
            // this.$refs.newtable.exportData({filename:'全区域人员',    sheetName: 'Sheet1',type: 'xlsx' })
            this.$refs.newtable.exportData({filename:'岗位职级分布',    sheetName: 'Sheet1',type: 'xlsx' })

        },
        footerCellClassName(event){
            console.log("=====--", event)

            // if(event.row.regionName == '办公室合计'&&event.column.title=='区域'||event.row.regionName == '办公室合计'&&event.column.title=='部门'
            // || event.row.regionName == '仓储合计'&&event.column.title=='区域'||event.row.regionName == '仓储合计'&&event.column.title=='部门'){
            //     return 'row-green'
            // }
            return null
        },
        footerRowClassName(event){
            // if(event.row.deptName=='全区域合计'){
            //     return 'row-bagreen'
            // }else{
            //     return 'row-green'
            // }
            // return null
        },
        rowClassName (event) {
            if(event.row.deptName == '小计'){
                return 'row-green'
            }
            return null
        },
        cellClassName ({ row, column }) {
        if (column.field === 'regularCount') {
            if (row.probationCount <= 26) {
            return 'col-red'
            } else if (row.probationCount > 26) {
            return 'col-orange'
            }
        }
        return null
        },
        // 通用行合并函数（将相同多列数据合并为一行）
        mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
            const fields = this.somerow.split(',')
            const cellValue = row[column.property]
            if (cellValue && fields.includes(column.property)) {
            const prevRow = visibleData[_rowIndex - 1]
            let nextRow = visibleData[_rowIndex + 1]
            if (prevRow && prevRow[column.property] === cellValue) {
                return { rowspan: 0, colspan: 0 }
            } else {
                let countRowspan = 1
                while (nextRow && nextRow[column.property] === cellValue) {
                nextRow = visibleData[++countRowspan + _rowIndex]
                }
                if (countRowspan > 1) {
                return { rowspan: countRowspan, colspan: 1 }
                }
            }
            }
        },

      async changeTime(e) {
        this.ListInfo.startTime = e ? e[0] : null
        this.ListInfo.endTime = e ? e[1] : null
      },
      //导出数据,使用时将下面的方法替换成自己的接口
      // async exportProps() {
      //     const { data } = await exportStatData(this.ListInfo)
      //     const aLink = document.createElement("a");
      //     let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      //     aLink.href = URL.createObjectURL(blob)
      //     aLink.setAttribute('download', '汇总数据' + new Date().toLocaleString() + '.xlsx')
      //     aLink.click()
      // },
      async getList(type) {
        // if (type == 'search') {
        //   this.ListInfo.currentPage = 1
        //   this.$refs.pager.setPage(1)
        // }
        // if (this.timeRanges && this.timeRanges.length == 0) {
        //   //默认给近7天时间
        //   this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        //   this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        //   this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
        // }
        if (this.ListInfo.calculateMonthArr && this.ListInfo.calculateMonthArr.length > 0) {
          //默认给近7天时间
        //   this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        //   this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        //   this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
            this.ListInfo.calculateMonthStart = this.ListInfo.calculateMonthArr[0]
            this.ListInfo.calculateMonthEnd = this.ListInfo.calculateMonthArr[1]
        }
        this.loading = true
        let region = this.ListInfo.regionName ? this.ListInfo.regionName : []
        const { data, success } = await allRegionPersonPage({ ...this.ListInfo, regionName: region.join(',') })
        if (success) {
            console.log(data)
          this.tableData = data.list
          const newDistricts = this.tableData.map(item => item.regionName).filter(district => district !== undefined && district !== null)
          this.allDistrictList = Array.from(new Set([...this.allDistrictList, ...newDistricts]));
          if (!this.ListInfo.type) {
            this.districtList = [...this.allDistrictList];
          }
          const newTypes = this.tableData.map(item => item.type).filter(district => district !== undefined && district !== null)
          this.typeList = Array.from(new Set([...this.typeList, ...newTypes]));
          const newsections = this.tableData.map(item => item.deptName).filter(section => section !== undefined && section !== null && section != '小计')
          this.sectionList = Array.from(new Set([...this.sectionList, ...newsections]));
          const newdept = this.tableData.map(item => item.dept).filter(dept => dept !== undefined && dept !== null && dept != '小计')
          this.deptList = Array.from(new Set([...this.deptList, ...newdept]));
          this.footerData = data.summary
          console.log(this.footerData,'this.footerData');

          const fieldsToCheck = ['personnelFluctuation', 'lastMonthPersonnel'];
          this.footerData.forEach((item) => {
            fieldsToCheck.forEach((field) => {
              if (item[field] === null || item[field] === undefined) {
                item[field] = 0;
              }
            });
            if (item.personnelFluctuation !== null && item.personnelFluctuation !== undefined) {
              item.personnelFluctuation = item.personnelFluctuation + '%';
            }
          });
          const fieldsToFormat = [
            "total",
            "regularCount",
            "probationCount",
            "assistantCount",
            "attacheCount",
            "groupLeaderCount",
            "superviseCount",
            "managerCount",
            "directorCount",
            "generalManager",
            "ceoCount"
          ];
          this.footerData.forEach((item) => {
            fieldsToFormat.forEach((field) => {
              if (item[field] !== null && item[field] !== undefined) {
                item[field] = this.formatNumberWithThousandSeparator(item[field]);
              }
            });
          });
          this.timeCundang = data.summary[0].archiveTime

          this.total = data.total
        //   this.summaryarry = data.summary
          this.loading = false
        } else {
          this.loading = false
          this.$message.error('获取列表失败')
        }
      },
      formatNumberWithThousandSeparator(value){
        if (value === null || value === undefined) return value;
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      },
      //每页数量改变
      Sizechange(val) {
        this.ListInfo.currentPage = 1;
        this.ListInfo.pageSize = val;
        this.getList()
      },
      //当前页改变
      Pagechange(val) {
        this.ListInfo.currentPage = val;
        this.getList()
      },
      sortchange({ order, prop }) {
        if (prop) {
          this.ListInfo.orderBy = prop
          this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
    }
  }
  </script>

  <style scoped lang="scss">
  .top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
      width: 150px;
      margin-right: 5px;
    }
  }

  ::v-deep .row-green {
    background-color: rgb(247, 230, 193);
    // color: #fff;
  }
  ::v-deep .row-bagreen {
    background-color: rgb(243, 138, 138);
    // color: #fff;
  }
  ::v-deep .rowcommon {
    height: 40px;
    // color: #fff;
  }
  ::v-deep(.mytable-style.vxe-table .vxe-header--column.col-blue) {
    background-color: #2db7f5;
    color: #fff;
  }
  ::v-deep(.mytable-style.vxe-table .vxe-body--column.col-red) {
    background-color: red;
    color: #fff;
  }
  :deep(.vxe-header--column){
    background: #00937e;
    color: white;
    font-weight: 600;
}
:deep(.vxe-footer--row){
    background: #00937e;
    color: white;
    font-weight: 600;
}

.footer-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 8px 3px;
  background-color: #fafafa;
  border-top: 1px solid #e6e6e6;
}

.total-count-badge {
  display: inline-block;
  font-size: 13px;
  min-width: 35.5px;
  height: 28px;
  line-height: 28px;
  vertical-align: top;
  box-sizing: border-box;
  color: #606266;
  background-color: #f5f7fa;
  padding: 0 2px;
  font-weight: 500;
  transition: all 0.3s ease;
}
.display_centered {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  font-weight: bold;
}
  </style>
