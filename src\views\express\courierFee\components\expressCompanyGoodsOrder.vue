<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime" :clearable="false">
        </el-date-picker>
        <el-button style="padding: 0;margin: 0;width: 500px; border: none;">
          <queryCondition ref="refqueryCondition" :valueChanged.sync="topfilter" />
        </el-button>
        <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
            <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="" />
        </el-button>
    
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
      </div>
    </template>
    <el-row>
      <el-col :span="18" style="height: 550px;">
        <vxetablebase :id="'expressCompanyGoodsWeight1111'" :tablekey="'expressCompanyGoodsWeight1111'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
      </el-col>
      <el-col :span="6">
        <div id="wrhPie"></div>
      </el-col>
    </el-row>
   
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    
    <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px" append-to-body
            v-dialogDrag>
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNoInner="orderNoInner" :orderNo="orderNo" :isTx="isTx"
                style="z-index:10000;height:500px" />
        </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { warehouselist, formatWarehouseNew,formatTime } from "@/utils/tools";
import { getExpressComanyAll , getExpressInfoData_MonthAll,exportExpressInfoData_MonthALL_GoodsCode ,queryExpressCompanyAnalysisAsync} from "@/api/express/express";
import dayjs from 'dayjs'
import inputYunhan from "@/components/Comm/inputYunhan";
import queryCondition from "../../dailyCourierFee/components/queryCondition.vue";
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
import * as echarts from "echarts";

const goodsCodeList = [
  'PPK',
  'TM-2021',
]
const tableCols = [

  { sortable: 'custom', width: '110', align: 'center', prop: 'mailNumber', label: '快递单号', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'orderNo', label: '内部单号', type: 'click',  handle: (that, row) => that.showOrderLog(row),  formatter: (row) => row.orderNo ? row.orderNo : '', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'quantity', label: '编码数', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'expressCompanyName', label: '快递公司', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'prosimstateId', label: '快递站点',  formatter: (row) => row.prosimstate, type: 'custom' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouseId', label: '发货仓库', formatter: (row) => formatWarehouseNew(row.warehouseId)  },
  { sortable: 'custom', width: '130', align: 'center', prop: 'jstWeight', label: '聚水潭重量（KG）', },
  { sortable: 'custom', width: '120', align: 'center', prop: 'billingWeight', label: '快递重量（KG）', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'diffWeight', label: '差额（KG）', },

]
export default {
  name: "expressCompanyGoodsWeight1111",
  components: {
    MyContainer, vxetablebase,inputYunhan,queryCondition,OrderActionsByInnerNos
  },
  data() {
    return {
      goodsCodeList,
      topfilter: {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
      },
      formatWarehouseNew:formatWarehouseNew,
      timeRanges: [],
      that: this,
      ListInfo: {
        DataType:2,
        yearmonth:null,
        currentPage: 1,
        pageSize: 50,
        orderBy: 'inportDate',
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        noUseCatch: false,//非缓存
        shopName1:null,//店铺
        goodsCount:1,
        goodsCode:'',
        weighttype:0
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      goodsCode:'',
      dialogHisVisible:false,
      orderNoInner:'',
      orderNo:'',
      isTx:false,
      myChart:null,
    }
  },
  async mounted() {
    // this.ListInfo.yearmonth= formatTime(new Date(),'YYYYMM');
    // if (this.timeRanges && this.timeRanges.length == 0) {
    //   //默认给当前月第一天至今天
    //   this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
    //   this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
    //   this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
    // }
    // await this.getList()
    //await this.loadData()
    await this.init()
  },
  productCodeCallback2(val) {
      this.ListInfo.goodsCode = val;
    },
  methods: {
    async init() {
      this.$nextTick(() => {
        this.$refs.refqueryCondition.init()
      })
      // const res = await getExpressComanyAll({});
      // if (!res?.success) return
      // this.expresscompanylist = res.data;
    },
    showOrderLog(row){
          this.orderNoInner = row.orderNo;
          this.orderNo = row.originalOnlineOrderNo;

          if (this.orderNo && row.platform && (row.platform == 1 || row.platform == 4 || row.platform == 8 || row.platform == 9)) {
                          this.isTx = true
            }else{
                this.isTx = false
            }
            this.dialogHisVisible=true;
    },
    productSelect(e,val) {
      this.$nextTick(() => {
      let value = e.target.value; // 输入框值
        if(value) { // 你输入才有这个值 不为空，如果你下拉框选择的话 这个值为空
          if(val == 1) {
            this.ListInfo.goodsCodeList = value
          } else if(val == 2) {
            this.ListInfo.labels = value
          } else if(val == 3) {
            this.ListInfo.status = value
          } else if(val == 4) {
            this.ListInfo.remark = value
          }
        }
      });
    },
    async loadData(args) {
            this.plat = args.request.plat
            this.timeRanges= [args.request.startDate, args.request.endDate];
            this.ListInfo.startTime = args.request.startDate;
            this.ListInfo.endTime = args.request.endDate;
            this.ListInfo.goodsCode=args.request.goodsCode;
            this.ListInfo.weighttype = args.request.weighttype;

            await this.getList();
        },
    //导出
    async exportProps() {
      this.loading = true
      const res = await exportExpressInfoData_MonthALL_GoodsCode({...this.ListInfo,...this.topfilter})
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '编码订单导出' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getExpressInfoData_MonthAll({...this.ListInfo,...this.topfilter})
    await this.onSearchCharts();

        if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary

        let summary = data.summary || {}

const resultsum = {};
Object.entries(summary).forEach(([key, value]) => {
    resultsum[key] = formatNumber(value);
});
function formatNumber(number) {
    const options = {
        useGrouping: true,
    };
    return new Intl.NumberFormat('zh-CN', options).format(number);
}
this.summaryarry = resultsum
        this.loading = false

      } else {
        this.$message.error('获取列表失败')
      }

    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async onSearchCharts() {
    
      const res = await queryExpressCompanyAnalysisAsync({...this.ListInfo,...this.topfilter});
    
      var chartDom = document.getElementById("wrhPie");
      this.myChart && this.myChart.clear();
      var piedata=[];
      for(var i in res.data.pieSeries)
      {
        piedata.push({name:res.data.pieSeries[i].name+"(订单量)",value:res.data.pieSeries[i].value})
      }
      this.piesData = res.data;
      if(this.piesData&&this.piesData.length>0)
        this.isShowPiesTable=true;
      this.myChart = this.myChart ?? echarts.init(chartDom);
      var option = await this.Getoptions(piedata);
      await option && this.myChart.setOption(option);
    },  
    //图表配置
    async Getoptions(data) {    
      var option = {
        title: { text: "快递公司订单量占比",left: 'center' },
        tooltip: {
          trigger: "item",
          textStyle: { align: "left" },
        },
        legend: {
          formatter: function (name) {
            return echarts.format.truncateText(
              name,
              200,
              "10px Microsoft Yahei",
              "..."
            );
          },
          tooltip: {
            show: true,
          },
          left: 'center',
          top:'bottom',
        },
        grid: {
          left: "1%",
          right: "1%",
          bottom: "1%",
          containLabel: true,
        },
        series: {
          type: 'pie',
          radius: '50%',
          data:data,
          /*emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
          },*/
          labelLine: {
              show: true
          },
          label:{
            normal:{
               show:true,
               formatter:'{b}'+'\n\r'+'{c}' + '\n\r' + '{d}%',
               position:'outside'
            }
          }
        },
      };
      return option;
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
#wrhPie{
  width:100%;
  height:500px;
  margin-top:40px;
}
</style>
