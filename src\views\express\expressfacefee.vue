<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="日期范围:">
            <el-date-picker v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" ></el-date-picker>
        </el-form-item>
        <el-form-item label="快递公司:">
          <el-select v-model="filter.expressCompanyId" placeholder="请选择快递公司" @change="getprosimstatechange()" style="width: 100%">
            <el-option label="请选择" value></el-option>
              <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id"/>
            </el-select>
        </el-form-item>
        <el-form-item label="快递站点:">
          <el-select v-model="filter.prosimstate" placeholder="请选择快递站点" style="width: 130px">
                  <el-option label="暂无站点" value=""/>
                  <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id"/>
                </el-select>
        </el-form-item>
        <el-form-item label="发货仓库:">
          <el-select v-model="filter.warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 100%">
            <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>

     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='true'
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='false'
              :loading="listLoading">
    </ces-table>

    <template #footer>
       <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>

    <el-drawer :title="formtitle" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="addFormVisible"
            direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
      <form-create ref="formcreate" :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
      <br/>
      <div class="drawer-footer">
        <el-button @click.native="addFormVisible = false">取消</el-button>
        <my-confirm-button type="submit"  :loading="addLoading" @click="onAddSubmit" />
      </div>
    </el-drawer>
  </my-container>
</template>

<script>
import {getExpressComanyAll, addFaceFee, batchUpdateFaceFee,batchUpdateVolCoeff,deleteExpressFaceFee,
  deleteExpressFaceFeeList,pageExpressFaceFee,getExpressComanyStationName} from '@/api/express/express'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import {formatWarehouse,formatTime,formatYesornoBool,warehouselist} from "@/utils/tools";
import { ruleExpressComany,ruleWarehouse,ruleYesornoBool} from '@/utils/formruletools'
import { getOrderCountNotSendDetailOrderNos } from '../../api/order/ordergoods';
const tableCols =[
      {istrue:true,prop:'date',label:'日期', width:'120',sortable:'custom',formatter:(row)=>formatTime(row.date,'YYYY-MM-DD')},
      {istrue:true,prop:'warehouse',label:'仓库', width:'100',sortable:'custom',formatter:(row)=>formatWarehouse(row.warehouse)},
      {istrue:true,prop:'expressCompany',label:'快递公司', width:'120'},
      {istrue:true,prop:'prosimStateName',label:'快递站点', width:'120'},
      {istrue:true,prop:'faceFee',label:'面单费', width:'100',sortable:'custom'},
      {istrue:true,prop:'hasContainFaceFee',label:'账单是否含面单费', width:'180',sortable:'custom',formatter:(row)=>formatYesornoBool(row.hasContainFaceFee)},
      {istrue:true,prop:'volCoeff',label:'体积系数', width:'150',sortable:'custom'},
      {istrue:true,type:'button',label:'操作',btnList:[{label:"删除",handle:(that,row)=>that.onDelete(row)}]}
     ];
const tableHandles1=[
        {label:"新增", handle:(that)=>that.onAdd()},
        {label:"批量更新面单费", handle:(that)=>that.onEdit()},
        {label:"批量更新体积系数", handle:(that)=>that.onEditvol()},
        {label:"批量删除", handle:(that)=>that.onbacthDelete()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: {
        date: null,
        startDate: null,
        endDate: null,
        expressCompanyId:null,
        prosimstate:null,
        warehouse:null
      },
      warehouselist:warehouselist,
      prosimstatelist:[],//快递站点
      list: [],
      expresscompanylist: [],
      pager:{OrderBy:"id",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      autoform:{
               fApi:{},
               rule:[],
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 12 }}}}
        },
      total: 0,
      sels: [],
      selids: [],
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,
      deleteLoading: false,
      formtitle:"新增",
      handletype:0 //0:add 1:edit
    }
  },
  mounted() {
    this.getExpressComanyList();
    this.getlist()
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async getExpressComanyList() {
       const res = await getExpressComanyAll({});
      if (!res?.success) {
        return;
      }
      const data = res.data;
      this.expresscompanylist = data;
    },
    //获取状态信息
    async getprosimstatelist(val){
        var option = []
        option.push({value:'28',label:'暂无站点'})

    },
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    //获取状态信息
    async getprosimstatechange(){
      var id = this.filter.expressCompanyId
        var res = await getExpressComanyStationName({id: id});
        if (res?.code){
            this.prosimstatelist = res.data
        }
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      if (params.timerange) {
        params.startDate = params.timerange[0];
        params.endDate = params.timerange[1];
      }
      this.listLoading = true
      const res = await pageExpressFaceFee(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   async updateruleshop(platformid) {
        await this.autoform.fApi.setValue({shopId:''})
        await this.autoform.fApi.updateRule('shopId',{... await ruleShop(platformid)})
        await this.autoform.fApi.sync('shopId')
    },
   async onEdit() {
    if (this.selids.length==0) {
       this.$message({type: 'warning',message: '请先选择!'});
       return;
    }
     this.handletype=1
      this.formtitle='批量编辑面单费';
      this.addFormVisible = true
      this.autoform.rule=[{type:'hidden',field:'ids',title:'ids',value: this.selids.join()},
                         {type:'InputNumber',field:'faceFee',title:'面单费',validate: [{required: true, message:'请输入'}],props:{precision:2,max:99999999,min:-99999999}},
                         {type:'select',field:'hasContainFaceFee',title:'账单是否含面单费', value: null, ...await ruleYesornoBool()}]
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
          this.autoform.fApi.reload()
    },
  async onEditvol() {
    if (this.selids.length==0) {
       this.$message({type: 'warning',message: '请先选择!'});
       return;
    }
    this.handletype=2
      this.formtitle='批量编辑体积系数';
      this.addFormVisible = true
      this.autoform.rule=[{type:'hidden',field:'ids',title:'ids',value: this.selids.join()},
                         {type:'InputNumber',field:'volCoeff',title:'体积系数',props:{precision:0,max:99999999,min:-99999999},validate: [{required: true, message:'请输入'}]}]
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
          this.autoform.fApi.reload()
    },
   async onAdd() {
      let that = this
     this.handletype=0
      this.formtitle='新增';
      this.addFormVisible = true
      this.autoform.rule=[{type:'hidden',field:'id',title:'id',value: '0'},
                      {type:'select',field:'expressCompanyId',title:'快递公司', value: '', update(val, rule){{ that.updaterulestation(val)}}, ...await ruleExpressComany(),props:{filterable:true}},
                      {type:'select',field:'prosimstate',title:'快递站点', value: '', ...that.getprosimstatelist(1) , validate: [{ required: true, message:'请选择'}], props:{filterable:true}},
                      {type:'select',field:'warehouse',title:'仓库', value: null, ...await ruleWarehouse()},
                      {type:'InputNumber',field:'faceFee',title:'面单费',validate: [{required: true, message:'请输入'}] ,props:{max:99999999,min:-99999999}},
                      {type:'select',field:'hasContainFaceFee',title:'账单含面单费', value: null, ...await ruleYesornoBool()},
                      {type:'InputNumber',field:'volCoeff',title:'体积系数',props:{precision:0,max:99999999,min:-99999999},validate: [{required: true, message:'请输入'}]},
                      //{type:'DatePicker',field:'date',title:'具体日期(二选一)',validate: [{required: true, message:'请输入'}]},
                      {type:'DatePicker',field:'startDate',title:'开始日期',validate: [{required: true, message:'请输入'}]},
                      {type:'DatePicker',field:'endDate',title:'结束日期',validate: [{required: true, message:'请输入'}]}]
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
          this.autoform.fApi.reload()
    },
    async updaterulestation(val){
        var options=[];
        var res = await getExpressComanyStationName({id: val});
        if (res?.data <= 0){
            options.push({value:28,label:'暂无站点'})
        } else {
            res?.data?.forEach(f => {
              options.push({value:f.id,label:f.stationName})
          })
        }
        await this.$nextTick(async () =>{
          var arr = Object.keys(this.autoform.fApi)
            if(arr.length > 0)
            await this.autoform.fApi.setValue({prosimstate:null})
        })
        this.autoform.fApi.getRule('prosimstate').options= options;
    },
    async onAddSubmit() {
      this.addFormVisible=true;
      this.addLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData()
          var res;
          if (this.handletype==0) {
              res = await addFaceFee(formData)
          }
          else if (this.handletype==1) {
              res = await batchUpdateFaceFee(formData)
          }
          else if (this.handletype==2) {
              res = await batchUpdateVolCoeff(formData)
          }
          if (res.code==1) {
            this.getlist()
            this.addFormVisible=false;
          }
        }else{
          //todo 表单验证未通过
        }
     })
      this.addLoading=false;
    },
    async onDelete(row) {
      row._loading = true
      this.$confirm('确认删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
            const res = await deleteExpressFaceFee({id:row.id})
            row._loading = false
            if (!res?.success) {return }
            this.$message({
                type: 'success',
                message: '删除成功!'
            });
            this.getlist()
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
           row._loading = false
        });
    },
   async onbacthDelete() {
      if (this.selids.length==0) {
       this.$message({type: 'warning',message: '请先选择!'});
       return;
      }
      this.$confirm('确认删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
            const res = await deleteExpressFaceFeeList({ids:this.selids.join()})
            if (!res?.success) {return }
            this.$message({type: 'success',message: '删除成功!'});
            this.getlist()
        }).catch(() => {
          this.$message({type: 'info',message: '已取消删除'});
        });
    },
    selsChange: function(sels) {
      this.sels = sels
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
  }
}
</script>
