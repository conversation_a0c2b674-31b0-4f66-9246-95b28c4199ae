<template>
  <container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent></el-form>
      <div style="display:flex;flex-direction: column;">
        <el-button-group v-show="isVisible1">
          <el-button style="padding: 0;border: none;margin: 0;">
            <el-date-picker style="width: 330px" v-model="filter.timerange" @change="changeTime($event,1)" type="daterange"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"></el-date-picker>
          </el-button>
          <el-button style="padding: 0;border: none;margin: 0;">
            <el-date-picker style="width: 330px" v-model="filter.timerangeOnTime" @change="changeTime($event,2)"
              type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="true" range-separator="至"
              start-placeholder="上架开始日期" end-placeholder="上架结束日期" :picker-options="pickerOptions"></el-date-picker>
          </el-button>
          <el-button v-show="(userType == 2 || userType == 1) && !visiblePersonal"
            style="padding: 0;border: none;margin: 0;">
            <el-date-picker style="width: 310px" v-model="filter.timerangeEntry" @change="changeTime($event,3)" type="daterange"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="true" range-separator="至"
              start-placeholder="入职开始日期" end-placeholder="入职结束日期" :picker-options="pickerOptions"></el-date-picker>
          </el-button>
          <el-button v-show="userType == 4 && !visiblePersonal" style="padding: 0;border: none;margin: 0;">
            <el-select filterable @change="changegroup" v-model="filter.shopManager" collapse-tags clearable
              placeholder="店铺负责人" style="width: 120px">
              <el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.label" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;border: none;">
            <el-select filterable @change="changeDate" v-model="filter.platform" collapse-tags placeholder="平台"
              style="width: 100px">
              <el-option key="全部" label="全部" :value="0"></el-option>
              <el-option key="天猫" label="天猫" :value="1"></el-option>
              <el-option key="淘宝" label="淘宝" :value="9"></el-option>
              <el-option key="淘工厂" label="淘工厂" :value="8"></el-option>
              <el-option key="苏宁" label="苏宁" :value="10"></el-option>
            </el-select>
          </el-button>
          <el-button v-show="userType != 8" style="padding: 0;border: none;">
            <el-select filterable @visible-change="operationgroup" v-model="filter.groupIds" collapse-tags clearable
              placeholder="运营组" multiple style="width: 150px">
              <!-- <el-option key="全部运营组" label="全部运营组" :value="9999"></el-option> -->
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>
          <el-button v-show="userType == 8 || userType == 3" style="padding: 0;border: none;">
            <el-select filterable @visible-change="operationgroup" v-model="filter.superviseIds" collapse-tags clearable
              placeholder="运营主管" multiple style="width: 150px">
              <el-option v-for="item in superviselist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>
          <el-button v-show="userType != 8" style="padding: 0;border: none;">
            <el-select filterable @change="changeDate" v-model="filter.company" collapse-tags clearable
              placeholder="分公司" style="width: 100px">
              <el-option v-for="item in childcompany" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>
          <el-button v-show="userType != 8" style="padding: 0;border: none;">
            <el-select filterable @change="changeDate" v-model="filter.onlineStatus" collapse-tags clearable
              placeholder="员工状态" style="width: 100px">
              <el-option key="正式" label="正式" :value="3"></el-option>
              <el-option key="试用" label="试用" :value="2"></el-option>
              <el-option key="离职" label="离职" :value="1"></el-option>
            </el-select>
          </el-button>
          <el-button style="padding: 0;border: none;">
            <el-select filterable @change="changeDate" v-model="filter.Profit3Lose" collapse-tags clearable
              placeholder="毛三利润" style="width: 100px">
              <el-option key="正利润" label="正利润" :value="0"></el-option>
              <el-option key="负利润" label="负利润" :value="1"></el-option>
            </el-select>
          </el-button>
          <el-button style="padding: 0;border: none;">
            <el-select filterable @change="changeDate" v-model="filter.Profit33Lose" collapse-tags clearable
              placeholder="毛四利润" style="width: 100px">
              <el-option key="正利润" label="正利润" :value="0"></el-option>
              <el-option key="负利润" label="负利润" :value="1"></el-option>
            </el-select>
          </el-button>
          <el-button v-show="userType == 4 && !visiblePersonal" style="padding: 0;border: none;margin: 0;">
            <el-date-picker style="width: 300px" v-model="filter.timerange2" type="daterange" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" range-separator="至" start-placeholder="店铺创建时间开始" end-placeholder="店铺创建时间结束"
              :picker-options="pickerOptions" @change="changeTime($event,4)"></el-date-picker>
          </el-button>
          <el-button style="padding: 0;border: none;margin: 0;">
            <el-select v-model="filter.xiFenPlatformList" @change="changeDate" filterable clearable placeholder="细分平台" multiple collapse-tags style="width: 170px" v-if="checkPermission('SegmentationPlatform')" >
              <el-option v-for="item in segmentationList" :key="item" :label="item" :value="item" />
            </el-select>
          </el-button>
          <el-button type="primary" @click="onExport" style="margin-left: 10px;">导出</el-button>
        </el-button-group>

        <el-button-group v-show="isVisible2">
          <el-button style="padding: 0;border: none;margin: 0;">
            <el-date-picker v-model="price.filter.value1" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
              placeholder="选择日期"></el-date-picker>
          </el-button>
          <el-button style="padding: 0;border: none;">
            <el-select filterable v-model="price.filter.platform" collapse-tags placeholder="平台" style="width: 100px">
              <el-option key="天猫" label="天猫" :value="1"></el-option>
              <el-option key="淘宝" label="淘宝" :value="9"></el-option>
            </el-select>
          </el-button>
          <el-button style="padding: 0;border: none;">
            <el-select filterable v-model="pricer.filter.groupId" collapse-tags clearable placeholder="运营组"
              style="width: 120px">
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;border: none;">
            <el-select filterable v-model="price.filter.company" collapse-tags clearable placeholder="分公司"
              style="width: 100px">
              <el-option v-for="item in childcompany" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>

          <el-button style="padding: 0;border: none;">
            <el-select v-model="pricer.filter.isStar" placeholder="达标" filterable style="width: 80px;">
              <el-option label="达标" :value="1"></el-option>
              <el-option label="未达标" :value="2"></el-option>
            </el-select>
          </el-button>
          <el-button type="primary" @click="forceSearch">搜索</el-button>
        </el-button-group>

        <el-button-group style="margin-top:3px">
          <el-button :type="buttonStyle[0]" @click="onShowZl">运营助理</el-button>
          <el-button :type="buttonStyle[1]" @click="onShowZy">运营专员</el-button>
          <el-button :type="buttonStyle[21]" @click="onShowDj">运营带教</el-button>
          <el-button :type="buttonStyle[4]" @click="onShowGp">运营组</el-button>
          <el-button :type="buttonStyle[6]" @click="onShowSupervise">运营主管</el-button>
          <el-button :type="buttonStyle[5]" @click="onShowShop">店铺</el-button>
          <el-button :type="buttonStyle[9]" @click="onAllPlatform" v-if="checkPermission('TXFullPlatformSummaryPermissions')">全平台汇总</el-button>
          <el-button type="info" @click="editZlparam">助理目标参数</el-button>
          <el-button type="info" @click="editZyparam">专员目标参数</el-button>
          <el-button :type="buttonType" @click="priceforce" v-if="checkPermission('Priceforce')">价格力</el-button>
        </el-button-group>
        <el-button-group style="margin-top:3px" v-if="!hideButtons">
          <el-button style="margin-top:1px;" v-if="checkPermission('TxPerformance_TxPerformanceProfit3')"
            :type="buttonStyle[2]" @click="onShowTarget(1)">毛三利润</el-button>
          <el-button style="margin-top:1px;" v-if="checkPermission('TxPerformance_TxPerformanceNetProfit')"
            :type="buttonStyle[3]" @click="onShowTarget(2)">净利润</el-button>
          <el-radio-group v-model="filter.refundType" size="small" style="margin-left:5px;"
            @change="refundTypeChange();">
              <el-radio-button :label="1">发生维度</el-radio-button>
              <!-- <el-radio-button :label="2">付款维度</el-radio-button> -->
              <!-- <el-radio-button :label="3">运营维度</el-radio-button> -->

          </el-radio-group>
        </el-button-group>
      </div>
    </template>

    <!-- style="width:100%;height:95%;margin: 0" -->

    <vxetablebase v-show="userType == 1 && !visiblePersonal && showreportList.length>0" tablekey="tablekey1" ref="table1" :showsummary='true' :id="'202408301130Txtablekey1'" :showheaderoverflow="false"
      :summaryarry='summaryarry' :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="false"
      :tableData='showreportList' :tableCols='tableCols_1' :tableHandles='tableHandles' @sortchange="sortchange"
      @summaryClick='onsummaryClick' :loading="listLoading" style="height:99%" :border="true" >
    </vxetablebase>

    <vxetablebase v-show="userType == 2 && !visiblePersonal && showreportList.length>0" tablekey="tablekey2" ref="table2" :showsummary='true' :id="'202408301131Txtablekey2'" :showheaderoverflow="false"
      :summaryarry='summaryarry' :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="false"
      :tableData='showreportList' :tableCols='tableCols_2' :tableHandles='tableHandles' @sortchange="sortchange"
      @summaryClick='onsummaryClick' :loading="listLoading" style="height:99%" :border="true" >
    </vxetablebase>

    <vxetablebase v-show="userType == 21 && !visiblePersonal && showreportList.length>0" tablekey="tablekey21" ref="table21" :showsummary='true' :id="'202410261607pddtablekey21'" :showheaderoverflow="false"
        :summaryarry='summaryarry' :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="true"
        :tableData='showreportList' :tableCols='tableCols_21' :tableHandles='tableHandles'
        @summaryClick='onsummaryClick' :loading="listLoading" @sortchange="sortchange" style="height:97%" :border="true" >
    </vxetablebase>

    <vxetablebase v-show="userType == 3 && !visiblePersonal && showreportList.length>0" tablekey="tablekey3" ref="table3" :showsummary='true' :id="'202408291517Txtablekey3'" :showheaderoverflow="false"
      :summaryarry='summaryarry' :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="false"
      :tableData='showreportList' :tableCols='tableCols_3' :tableHandles='tableHandles' @sortchange="sortchange"
      @summaryClick='onsummaryClick' :loading="listLoading" style="height:99%" :border="true" >
    </vxetablebase>

    <vxetablebase v-show="userType == 8 && !visiblePersonal && showreportList.length>0" tablekey="tablekey8" ref="table8" :showsummary='true' :id="'202408301132Txtablekey8'" :showheaderoverflow="false"
      :summaryarry='summaryarry' :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="false"
      :tableData='showreportList' :tableCols='tableCols_8' :tableHandles='tableHandles' @sortchange="sortchange"
      @summaryClick='onsummaryClick' :loading="listLoading" style="height:99%" :border="true" >
    </vxetablebase>

    <vxetablebase v-show="userType == 4 && !visiblePersonal && showreportList.length>0" tablekey="tablekey4" ref="table4" :showsummary='true' :id="'202408301133Txtablekey4'" :showheaderoverflow="false"
      :summaryarry='summaryarry' :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="false"
      :tableData='showreportList' :tableCols='tableCols_4' :tableHandles='tableHandles' @sortchange="sortchange"
      @summaryClick='onsummaryClick' :loading="listLoading" style="height:99%" :border="true" >
    </vxetablebase>

    <vxetablebase v-show="userType == 7 && !visiblePersonal && showreportList.length>0" tablekey="tablekey7" ref="table7" :showsummary='true' :id="'202408301134Txtablekey5'" :showheaderoverflow="false"
      :summaryarry='summaryarry' :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="false"
      :tableData='pricelist' :tableCols='tableCols_7' :tableHandles='tableHandles' @sortchange="sortforcechange"
      @summaryClick='onsummarypriceClick' :loading="listLoading" style="height:99%" :border="true" >
    </vxetablebase>

    <vxetablebase v-show="userType == 9 && !visiblePersonal && showreportList.length>0" tablekey="tablekey9" ref="table9" :showsummary='true' :id="'202506071000tablekey9'" :showheaderoverflow="false"
      :summaryarry='summaryarry' :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="false"
      :tableData='showreportList' :tableCols='tableCols_9' :tableHandles='tableHandles' @sortchange="sortchange"
      @summaryClick='onsummaryClick' :loading="listLoading" style="height:99%" :border="true" >
    </vxetablebase>

    <!-- <my-pagination ref="pageer" v-if="!visiblePersonal" :total="total" :checked-count="sels.length" @page-change="detailPagechange"
      @size-change="detailSizechange" /> -->

    <el-dialog title="趋势图" :visible.sync="dialogMapVisible.visible" width="80%" v-dialogDrag>
      <div>
        <span>
          <template>
            <el-date-picker style="width: 410px" v-model="dialogMapVisible.filter.timerange" type="daterange"
              @change="similarityDateChange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false"
              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
              :picker-options="pickerOptions"></el-date-picker>
          </template>
        </span>
        <span>
          <buschar ref="dialogMapVisibleBuschar" v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data">
          </buschar>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="小组店铺明细数据" :visible.sync="dialogGroupDetailVisible.visible" width="80%" v-dialogDrag>
      <div>
        <span>
          <groupShopDetail style="height: 600px;" ref="groupShopDetail" :filter="groupShopDetail.filter"
            v-if="dialogGroupDetailVisible.visible"></groupShopDetail>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogGroupDetailVisible.visible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="设置助理目标参数" :visible.sync="dialogSetParamZlVisible" width="40%" v-dialogDrag>
      <span>
        <el-row>毛三目标(元)
          <el-input v-model="targetData" />
        </el-row>
        <el-row>净利目标(元)
          <el-input v-model="targetData1" />
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="saveZl()">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="设置专员目标参数" :visible.sync="dialogSetParamZyVisible" width="40%" v-dialogDrag>
      <span>
        <el-row>毛三目标(元)
          <el-input v-model="targetData" />
        </el-row>
        <el-row>净利目标(元)
          <el-input v-model="targetData1" />
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="dialogSetParamZyVisible = false">确定</el-button> -->
        <el-button @click="saveZy()">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="趋势图" :visible.sync="drawerVisible" width="80%" v-dialogDrag @close="handleClose">
      <div>
        <span>
          <template>
            <el-date-picker v-model="price.filter.value2" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="showchart2(price.filter)">
            </el-date-picker>
          </template>
        </span>
        <span>
          <!-- <el-button type="primary" @click="showchart2">搜索</el-button> -->
        </span>
        <span>
          <buschar v-if="quantityprocessed.visible" ref="drawerbuschar" :analysisData="quantityprocessed.data">
          </buschar>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="drawerVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="汇总趋势图" :visible.sync="collectdrawerVisible" width="80%" v-dialogDrag @close="handleClose">
      <div>
        <span>
          <template>
            <el-date-picker v-model="price.filter.value3" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="onsummarypriceClick">
            </el-date-picker>
          </template>
        </span>
        <span>
          <!-- <el-button type="primary" @click="onsummarypriceClick">搜索</el-button> -->
        </span>
        <span>
          <buschar v-if="collectquantityprocessed.visible" ref="collectdrawerbuschar"
            :analysisData="collectquantityprocessed.data">
          </buschar>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="collectdrawerVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="popoverdetailvisible" width="530px" v-dialogDrag>
      <el-table :data="editList" max-height="250" style="width: 100%;margin-top:10px">
        <el-table-column type="index" width="50" sortable></el-table-column>
        <el-table-column width="110" property="proCode" label="宝贝ID" sortable></el-table-column>
        <el-table-column width="160" property="shopName" label="店铺" sortable></el-table-column>
        <el-table-column width="90" property="operateSpecialUserName" label="专员" sortable></el-table-column>
        <el-table-column width="75" property="userName" label="助理" sortable></el-table-column>
      </el-table>
    </el-dialog>
    <div v-show="visiblePersonal">
      <financialReportStaticsByOneUser ref="financialReportStaticsByOneUser" />
    </div>
  </container>
</template>
<script>
import { childcompany, formatPlatform } from '@/utils/tools';
import { getDirectorGroupList, getDirectorGroupList2, getAllList as getAllShopList, getAllShopXiFenPlatform } from '@/api/operatemanage/base/shop'
import {
  SetPerformanceTarget, getPerformanceTarget, getPerformanceStaticticsByUser, getPerformanceStaticticsByGroup,
  getPerformanceStaticticsByUserMap, getPerformanceStaticticsByShop, getPerformanceStaticticsByShopMap, getPerformanceStaticticsByonSuperviseMap,
  exportPerformanceStaticticsByUser, exportPerformanceStaticticsByGroup, exportPerformanceStaticticsByShop
} from '@/api/bookkeeper/reportday'
import { getPerformanceStaticticsByUserAnalysis, getPerformanceStaticticsByGroupAnalysis } from '@/api/bookkeeper/pddstaticsreport'
import { getPriceForce, getPriceForceDialog, getPriceForceChart, getPriceForceSumChart } from "@/api/operatemanage/base/product";

import financialReportStaticsByOneUser from '@/views/bookkeeper/reportday/financialReportStaticsByOneUser'
import cesTable from "@/components/Table/table.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetablevisable.vue";
import container from "@/components/my-container/nofooter";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import InputMult from "@/components/Comm/InputMult";
import productdrchart from '@/views/bookkeeper/reportday/productdrchart'
import { Loading } from 'element-ui';
import freightDetail from '@/views/bookkeeper/reportday/freightDetail'
import buschar from '@/components/Bus/buschar'
import groupShopDetail from '@/views/bookkeeper/reportday/groupShopDetail'
import checkPermission from '@/utils/permission'
import middlevue from "@/store/middle.js"
import { formatTime } from "@/utils";
import dayjs from "dayjs";


import { getPerformanceStaticticsByGroupMap } from '@/api/bookkeeper/reportday'
let loading;
const startLoading = () => {
  loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
  });
};
const tableCols1 = [
  { istrue: true, display: true, prop: 'userName', label: '运营助理', width: '80', type: "click", handle: (that, row, column, cell) => that.canclick(row, column, cell) },
  { istrue: true, label: '小组头像', width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
  { istrue: true, display: true, prop: 'groupName', label: '所在组', width: '70', formatter: (row) => row.groupName,type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, handle: (that, row) => that.showGroupDetail(row) },
  { istrue: true, sortable: 'custom', prop: 'xiFenPlatform', label: '细分平台', width: '100', permission: "SegmentationPlatform" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'orderCount', label: '订单量', width: '80', formatter: (row) => !row.orderCount ? "0" : row.orderCount },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'payAmont', label: '付款金额', width: '80', formatter: (row) => !row.payAmont ? "0" : row.payAmont },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleAmont', label: '销售金额', width: '80', formatter: (row) => !row.saleAmont ? "0" : row.saleAmont },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontBefore', exportField: 'refundAmont', label: '退款', width: '80', tipmesg: '发货前退款', formatter: (row) => !row.refundAmontBefore ? "0" : row.refundAmontBefore },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontBeforeRate', exportField: 'refundAmontRateStr', label: '退款率', width: '80', tipmesg: '发货前退款率', formatter: (row) => !row.refundAmontBeforeRate ? "0%" : row.refundAmontBeforeRate.toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleCost', label: '成本', width: '80', formatter: (row) => !row.saleCost ? " " : row.saleCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit1After', label: '运营毛一(减退款)', width: '80', formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1', label: '毛一利润(发生)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit11', label: '毛一利润(付款)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate', exportField: 'profit1RateStr', label: '毛一利润率(发生)', width: '80', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate1', label: '毛一利润率(付款)', width: '90', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv', label: '广告费', width: '100', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv_rate', exportField: 'alladv_rateStr', label: '广告占比', width: '80', formatter: (row) => (row.alladv_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit2After', label: '运营毛二(减退款)', width: '80', formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3After', label: '运营毛三(减退款)', width: '80', formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3', label: '毛三利润(发生)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit31', label: '毛三利润(付款)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3AfterRate', label: '毛3率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit3AfterRate ? " " : (row.yyProfit3AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate', exportField: 'profit3_rateStr', label: '毛三利润率(发生)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate1', label: '毛三利润率(付款)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4After', label: '运营毛四(减退款)', width: '80', formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5After', label: '运营毛五(减退款)', width: '80', formatter: (row) => !row.yyProfit5After ? " " : row.yyProfit5After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6After', label: '运营毛六(减退款)', permission: "profit6SixtyCents", width: '80', formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5', label: '毛五利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5 ? " " : row.profit5 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6', label: '毛六利润(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6 ? " " : row.profit6 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4AfterRate', label: '毛4率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit4AfterRate ? " " : (row.yyProfit4AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5AfterRate', label: '毛5率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit5AfterRate ? " " : (row.yyProfit5AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6AfterRate', label: '毛6率(运营减退)', permission: "profit6SixtyCents", width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit6AfterRate ? " " : (row.yyProfit6AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate', exportField: 'profit33RateStr', label: '毛四利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate1', label: '毛四利润率(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5Rate', exportField: 'profit5Rate', label: '毛五利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6Rate', exportField: 'profit6Rate', label: '毛六利润率(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost', label: '出仓成本(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost1', label: '出仓成本(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '90', formatter: (row) => !row.warehouseSalary ? 0 : row.warehouseSalary },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCostRate', exportField: 'exitCostRateStr', label: '出仓成本占比', sortable: 'custom', width: '100', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + "%"},
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4', label: '净利润(发生)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit41', label: '净利润(付款)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate', exportField: 'profit4_rateStr', label: '净利率(发生)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate1', label: '净利率(付款)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'negativeExitProfitIdDetail', label: '出仓负利润', sortable: 'custom', width: '90', formatter: (row) => !row.negativeExitProfitIdDetail ? " " : row.negativeExitProfitIdDetail },
  { istrue: true, summaryEvent: true, display: true, prop: 'deductAmount', label: '违规扣款金额', width: '100', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
  { istrue: true, display: true, prop: 'progress', label: '完成比例', type: "progress" },
  { istrue: true, sortable: 'custom', display: true, prop: 'onlineStatus', label: '员工状态', width: '80', formatter: (row) => row.onlineStatus },
  { istrue: true, display: true, prop: 'createTime', label: '入职时间', width: '100' },
  { istrue: true, display: true, sortable: 'custom', prop: 'workTime', label: '上班时长', width: '80', formatter: (row) => (row.workTime).toString() },
  { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

const tableCols2 = [
  { istrue: true, label: '专员头像', width: '70',type:'ddAvatar',ddInfo:{type:2,prop:'userId'} },
  { istrue: true, display: true, prop: 'userName', label: '运营专员', width: '80', type:'ddTalk',ddInfo:{type:2,prop:'userId',name:'userName'},handle: (that, row, column, cell) => that.canclick(row, column, cell) },
  { istrue: true, label: '小组头像', width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
  { istrue: true, display: true, prop: 'groupName', label: '所在组', width: '70', formatter: (row) => row.groupName,type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, handle: (that, row) => that.showGroupDetail(row) },
  { istrue: true, sortable: 'custom', prop: 'xiFenPlatform', label: '细分平台', width: '100', permission: "SegmentationPlatform" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'orderCount', label: '订单量', width: '100', formatter: (row) => !row.orderCount ? "0" : row.orderCount },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'payAmont', label: '付款金额', width: '100', formatter: (row) => !row.payAmont ? "0" : row.payAmont },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleAmont', label: '销售金额', width: '120', formatter: (row) => !row.saleAmont ? "0" : row.saleAmont },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontBefore', exportField: 'refundAmont', label: '退款', width: '120', tipmesg: '发货前退款', formatter: (row) => !row.refundAmontBefore ? "0" : row.refundAmontBefore },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontBeforeRate', exportField: 'refundAmontRateStr', label: '退款率', width: '120', tipmesg: '发货前退款率', formatter: (row) => !row.refundAmontBeforeRate ? "0%" : row.refundAmontBeforeRate.toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleCost', label: '成本', width: '120', formatter: (row) => !row.saleCost ? " " : row.saleCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit1After', label: '运营毛一(减退款)', width: '80', formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1', label: '毛一利润(发生)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit11', label: '毛一利润(付款)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate', exportField: 'profit1RateStr', label: '毛一利润率(发生)', width: '80', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate1', label: '毛一利润率(付款)', width: '90', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv', label: '广告费', width: '100', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv_rate', exportField: 'alladv_rateStr', label: '广告占比', width: '80', formatter: (row) => (row.alladv_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit2After', label: '运营毛二(减退款)', width: '80', formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3After', label: '运营毛三(减退款)', width: '80', formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3', label: '毛三利润(发生)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit31', label: '毛三利润(付款)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3AfterRate', label: '毛3率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit3AfterRate ? " " : (row.yyProfit3AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate', exportField: 'profit3_rateStr', label: '毛三利润率(发生)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate1', label: '毛三利润率(付款)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4After', label: '运营毛四(减退款)', width: '80', formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5After', label: '运营毛五(减退款)', width: '80', formatter: (row) => !row.yyProfit5After ? " " : row.yyProfit5After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6After', label: '运营毛六(减退款)', permission: "profit6SixtyCents", width: '80', formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5', label: '毛五利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5 ? " " : row.profit5 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6', label: '毛六利润(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6 ? " " : row.profit6 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4AfterRate', label: '毛4率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit4AfterRate ? " " : (row.yyProfit4AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5AfterRate', label: '毛5率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit5AfterRate ? " " : (row.yyProfit5AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6AfterRate', label: '毛6率(运营减退)', permission: "profit6SixtyCents", width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit6AfterRate ? " " : (row.yyProfit6AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate', exportField: 'profit33RateStr', label: '毛四利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate1', label: '毛四利润率(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5Rate', exportField: 'profit5Rate', label: '毛五利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6Rate', exportField: 'profit6Rate', label: '毛六利润率(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost', label: '出仓成本(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost1', label: '出仓成本(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '90', formatter: (row) => !row.warehouseSalary ? 0 : row.warehouseSalary },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCostRate', exportField: 'exitCostRateStr', label: '出仓成本占比', sortable: 'custom', width: '100', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + "%"},
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4', label: '净利润(发生)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit41', label: '净利润(付款)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate', exportField: 'profit4_rateStr', label: '净利率(发生)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate1', label: '净利率(付款)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'negativeExitProfitIdDetail', label: '出仓负利润', sortable: 'custom', width: '90', formatter: (row) => !row.negativeExitProfitIdDetail ? " " : row.negativeExitProfitIdDetail },
  { istrue: true, summaryEvent: true, display: true, prop: 'deductAmount', label: '违规扣款金额', width: '100', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
  { istrue: true, display: true, prop: 'progress', label: '完成比例', type: "progress" },
  { istrue: true, sortable: 'custom', display: true, prop: 'onlineStatus', label: '员工状态', width: '80', formatter: (row) => row.onlineStatus },
  { istrue: true, display: true, prop: 'createTime', label: '入职时间', width: '100' },
  { istrue: true, display: true, sortable: 'custom', prop: 'workTime', label: '上班时长', width: '80', formatter: (row) => (row.workTime).toString() },
  { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

const tableCols21 = [
{ istrue: true, label: '带教头像', width: '70',type:'ddAvatar',ddInfo:{type:2,prop:'userId'} },
  { istrue: true, display: true, prop: 'userName', label: '运营带教', width: '80', type:'ddTalk',ddInfo:{type:2,prop:'userId',name:'userName'}, handle: (that, row, column, cell) => that.canclick(row, column, cell) },
  { istrue: true, label: '小组头像', width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
  { istrue: true, display: true, prop: 'groupName', label: '所在组', width: '70', formatter: (row) => row.groupName,type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, handle: (that, row) => that.showGroupDetail(row) },
  { istrue: true, sortable: 'custom', prop: 'xiFenPlatform', label: '细分平台', width: '100', permission: "SegmentationPlatform" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'orderCount', label: '订单量', width: '100', formatter: (row) => !row.orderCount ? "0" : row.orderCount },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'payAmont', label: '付款金额', width: '100', formatter: (row) => !row.payAmont ? "0" : row.payAmont },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleAmont', label: '销售金额', width: '120', formatter: (row) => !row.saleAmont ? "0" : row.saleAmont },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontBefore', exportField: 'refundAmont', label: '退款', width: '120', tipmesg: '发货前退款', formatter: (row) => !row.refundAmontBefore ? "0" : row.refundAmontBefore },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontBeforeRate', exportField: 'refundAmontRateStr', label: '退款率', width: '120', tipmesg: '发货前退款率', formatter: (row) => !row.refundAmontBeforeRate ? "0%" : row.refundAmontBeforeRate.toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleCost', label: '成本', width: '120', formatter: (row) => !row.saleCost ? " " : row.saleCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit1After', label: '运营毛一(减退款)', width: '80', formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1', label: '毛一利润(发生)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit11', label: '毛一利润(付款)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate', exportField: 'profit1RateStr', label: '毛一利润率(发生)', width: '80', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate1', label: '毛一利润率(付款)', width: '90', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv', label: '广告费', width: '100', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv_rate', exportField: 'alladv_rateStr', label: '广告占比', width: '80', formatter: (row) => (row.alladv_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit2After', label: '运营毛二(减退款)', width: '80', formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3After', label: '运营毛三(减退款)', width: '80', formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3', label: '毛三利润(发生)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit31', label: '毛三利润(付款)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3AfterRate', label: '毛3率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit3AfterRate ? " " : (row.yyProfit3AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate', exportField: 'profit3_rateStr', label: '毛三利润率(发生)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate1', label: '毛三利润率(付款)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4After', label: '运营毛四(减退款)', width: '80', formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5After', label: '运营毛五(减退款)', width: '80', formatter: (row) => !row.yyProfit5After ? " " : row.yyProfit5After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6After', label: '运营毛六(减退款)', permission: "profit6SixtyCents", width: '80', formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5', label: '毛五利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5 ? " " : row.profit5 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6', label: '毛六利润(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6 ? " " : row.profit6 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4AfterRate', label: '毛4率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit4AfterRate ? " " : (row.yyProfit4AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5AfterRate', label: '毛5率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit5AfterRate ? " " : (row.yyProfit5AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6AfterRate', label: '毛6率(运营减退)', permission: "profit6SixtyCents", width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit6AfterRate ? " " : (row.yyProfit6AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate', exportField: 'profit33RateStr', label: '毛四利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate1', label: '毛四利润率(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5Rate', exportField: 'profit5Rate', label: '毛五利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6Rate', exportField: 'profit6Rate', label: '毛六利润率(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost', label: '出仓成本(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost1', label: '出仓成本(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '90', formatter: (row) => !row.warehouseSalary ? 0 : row.warehouseSalary },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCostRate', exportField: 'exitCostRateStr', label: '出仓成本占比', sortable: 'custom', width: '100', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + "%"},
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4', label: '净利润(发生)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit41', label: '净利润(付款)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate', exportField: 'profit4_rateStr', label: '净利率(发生)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate1', label: '净利率(付款)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'negativeExitProfitIdDetail', label: '出仓负利润', sortable: 'custom', width: '90', formatter: (row) => !row.negativeExitProfitIdDetail ? " " : row.negativeExitProfitIdDetail },
  { istrue: true, summaryEvent: true, display: true, prop: 'deductAmount', label: '违规扣款金额', width: '100', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
  { istrue: true, display: true, prop: 'progress', label: '完成比例', type: "progress" },
  { istrue: true, sortable: 'custom', display: true, prop: 'onlineStatus', label: '员工状态', width: '80', formatter: (row) => row.onlineStatus },
  { istrue: true, display: true, prop: 'createTime', label: '入职时间', width: '100' },
  { istrue: true, display: true, sortable: 'custom', prop: 'workTime', label: '上班时长', width: '80', formatter: (row) => (row.workTime).toString() },
  { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

const tableCols3 = [
{ istrue: true, label: '小组头像', width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
  { istrue: true, display: true, prop: 'groupName', label: '所在组', width: '70', formatter: (row) => row.groupName,type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, handle: (that, row) => that.showGroupDetail(row) },
  { istrue: true, sortable: 'custom', prop: 'xiFenPlatform', label: '细分平台', width: '100', permission: "SegmentationPlatform" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'orderCount', label: '订单量', width: '100', formatter: (row) => !row.orderCount ? "0" : row.orderCount },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'payAmont', label: '付款金额', width: '100', formatter: (row) => !row.payAmont ? "0" : row.payAmont },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleAmont', label: '销售金额', width: '120', formatter: (row) => !row.saleAmont ? "0" : row.saleAmont },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontBefore', exportField: 'refundAmont', label: '退款', width: '120', tipmesg: '发货前退款', formatter: (row) => !row.refundAmontBefore ? "0" : row.refundAmontBefore },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontBeforeRate', exportField: 'refundAmontRateStr', label: '退款率', width: '120', tipmesg: '发货前退款率', formatter: (row) => !row.refundAmontBeforeRate ? "0%" : row.refundAmontBeforeRate.toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleCost', label: '成本', width: '120', formatter: (row) => !row.saleCost ? " " : row.saleCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit1After', label: '运营毛一(减退款)', width: '80', formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1', label: '毛一利润(发生)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit11', label: '毛一利润(付款)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate', exportField: 'profit1RateStr', label: '毛一利润率(发生)', width: '80', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate1', label: '毛一利润率(付款)', width: '90', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv', label: '广告费', width: '100', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv_rate', exportField: 'alladv_rateStr', label: '广告占比', width: '80', formatter: (row) => (row.alladv_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit2After', label: '运营毛二(减退款)', width: '80', formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3After', label: '运营毛三(减退款)', width: '80', formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3', label: '毛三利润(发生)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit31', label: '毛三利润(付款)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3AfterRate', label: '毛3率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit3AfterRate ? " " : (row.yyProfit3AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate', exportField: 'profit3_rateStr', label: '毛三利润率(发生)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate1', label: '毛三利润率(付款)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4After', label: '运营毛四(减退款)', width: '80', formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5After', label: '运营毛五(减退款)', width: '80', formatter: (row) => !row.yyProfit5After ? " " : row.yyProfit5After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6After', label: '运营毛六(减退款)', permission: "profit6SixtyCents", width: '80', formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5', label: '毛五利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5 ? " " : row.profit5 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6', label: '毛六利润(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6 ? " " : row.profit6 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4AfterRate', label: '毛4率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit4AfterRate ? " " : (row.yyProfit4AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5AfterRate', label: '毛5率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit5AfterRate ? " " : (row.yyProfit5AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6AfterRate', label: '毛6率(运营减退)', permission: "profit6SixtyCents", width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit6AfterRate ? " " : (row.yyProfit6AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate', exportField: 'profit33RateStr', label: '毛四利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate1', label: '毛四利润率(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5Rate', exportField: 'profit5Rate', label: '毛五利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6Rate', exportField: 'profit6Rate', label: '毛六利润率(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost', label: '出仓成本(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost1', label: '出仓成本(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '90', formatter: (row) => !row.warehouseSalary ? 0 : row.warehouseSalary },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCostRate', exportField: 'exitCostRateStr', label: '出仓成本占比', sortable: 'custom', width: '100', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + "%"},
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4', label: '净利润(发生)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit41', label: '净利润(付款)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate', exportField: 'profit4_rateStr', label: '净利率(发生)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate1', label: '净利率(付款)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'negativeExitProfitIdDetail', label: '出仓负利润', sortable: 'custom', width: '90', formatter: (row) => !row.negativeExitProfitIdDetail ? " " : row.negativeExitProfitIdDetail },
  { istrue: true, summaryEvent: true, display: true, prop: 'deductAmount', label: '违规扣款金额', width: '100', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
  { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

const tableCols4 = [
  { istrue: true, display: true, prop: 'shopName', label: '店铺', width: '60', formatter: (row) => row.shopName, type: 'click', handle: (that, row) => that.showShopDetail(row) },
  { istrue: true, sortable: 'custom', prop: 'xiFenPlatform', label: '细分平台', width: '100', permission: "SegmentationPlatform" },
  { istrue: true, display: true, sortable: 'custom', prop: 'shopManager', label: '店铺负责人', width: '105', },
  { istrue: true, display: true, sortable: 'custom', prop: 'shopCreateTime', label: '店铺创建时间', width: '105', formatter: (row) => row.shopCreateTime },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'orderCount', label: '订单量', width: '80', formatter: (row) => !row.orderCount ? "0" : row.orderCount },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'payAmont', label: '付款金额', width: '80', formatter: (row) => !row.payAmont ? "0" : row.payAmont },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleAmont', label: '销售金额', width: '80', formatter: (row) => !row.saleAmont ? "0" : row.saleAmont },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontBefore', exportField: 'refundAmont', label: '退款', width: '60', tipmesg: '发货前退款', formatter: (row) => !row.refundAmontBefore ? "0" : row.refundAmontBefore },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontBeforeRate', exportField: 'refundAmontRateStr', label: '退款率', width: '80', tipmesg: '发货前退款率', formatter: (row) => !row.refundAmontBeforeRate ? "0%" : row.refundAmontBeforeRate.toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleCost', label: '成本', width: '60', formatter: (row) => !row.saleCost ? " " : row.saleCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit1After', label: '运营毛一(减退款)', width: '80', formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1', label: '毛一利润(发生)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit11', label: '毛一利润(付款)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate', exportField: 'profit1RateStr', label: '毛一利润率(发生)', width: '80', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate1', label: '毛一利润率(付款)', width: '90', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv', label: '广告费', width: '80', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv_rate', exportField: 'alladv_rateStr', label: '广告占比', width: '80', formatter: (row) => (row.alladv_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit2After', label: '运营毛二(减退款)', width: '80', formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3After', label: '运营毛三(减退款)', width: '80', formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3', label: '毛三利润(发生)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit31', label: '毛三利润(付款)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3AfterRate', label: '毛3率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit3AfterRate ? " " : (row.yyProfit3AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate', exportField: 'profit3_rateStr', label: '毛三利润率(发生)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate1', label: '毛三利润率(付款)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4After', label: '运营毛四(减退款)', width: '80', formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5After', label: '运营毛五(减退款)', width: '80', formatter: (row) => !row.yyProfit5After ? " " : row.yyProfit5After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6After', label: '运营毛六(减退款)', permission: "profit6SixtyCents", width: '80', formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5', label: '毛五利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5 ? " " : row.profit5 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6', label: '毛六利润(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6 ? " " : row.profit6 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4AfterRate', label: '毛4率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit4AfterRate ? " " : (row.yyProfit4AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5AfterRate', label: '毛5率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit5AfterRate ? " " : (row.yyProfit5AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6AfterRate', label: '毛6率(运营减退)', permission: "profit6SixtyCents", width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit6AfterRate ? " " : (row.yyProfit6AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate', exportField: 'profit33RateStr', label: '毛四利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate1', label: '毛四利润率(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5Rate', exportField: 'profit5Rate', label: '毛五利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6Rate', exportField: 'profit6Rate', label: '毛六利润率(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost', label: '出仓成本(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost1', label: '出仓成本(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '90', formatter: (row) => !row.warehouseSalary ? 0 : row.warehouseSalary },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCostRate', exportField: 'exitCostRateStr', label: '出仓成本占比', sortable: 'custom', width: '100', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + "%"},
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4', label: '净利润(发生)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit41', label: '净利润(付款)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate', exportField: 'profit4_rateStr', label: '净利率(发生)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate1', label: '净利率(付款)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'negativeExitProfitIdDetail', label: '出仓负利润', sortable: 'custom', width: '90', formatter: (row) => !row.negativeExitProfitIdDetail ? " " : row.negativeExitProfitIdDetail },
  { istrue: true, summaryEvent: true, display: true, prop: 'deductAmount', label: '违规扣款金额', width: '100', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
  { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

const tableCols7 = [
{ istrue: true, label: '小组头像', width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
  { istrue: true, prop: 'groupName', label: '所在组', width: '130', formatter: (row) => row.groupId == 0 ? "无运营组" : row.groupName ,type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'},},
  { istrue: true, prop: 'yearMonthDay', label: '日期', width: '130', },
  { istrue: true, prop: 'isStar', label: '状态', width: '130', formatter: (row) => row.isStar == 1 ? "达标" : (row.isStar == 2 ? "不达标" : "未知") },
  { istrue: true, prop: 'starLv5CountArray', label: '五星', width: '120', style: "position:relative,height:40px", type: 'starclick', handle: (that, row, column) => that.cellclick(row, column), sortable: 'custom', summaryEvent: true },
  { istrue: true, prop: 'starLv4CountArray', label: '四星', width: '120', style: "position:relative,height:40px", type: 'starclick', handle: (that, row, column) => that.cellclick(row, column), sortable: 'custom', summaryEvent: true },
  { istrue: true, prop: 'starLv3CountArray', label: '三星', width: '120', style: "position:relative,height:40px", type: 'starclick', handle: (that, row, column) => that.cellclick(row, column), sortable: 'custom', summaryEvent: true },
  { istrue: true, prop: 'starLv2CountArray', label: '二星', width: '120', style: "position:relative,height:40px", type: 'starclick', handle: (that, row, column) => that.cellclick(row, column), sortable: 'custom', summaryEvent: true },
  { istrue: true, prop: 'starLv1CountArray', label: '一星', width: '120', style: "position:relative,height:40px", type: 'starclick', handle: (that, row, column) => that.cellclick(row, column), sortable: 'custom', summaryEvent: true },
  { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: '120', formatter: (row) => '趋势图', type: 'click', handle: (that, row, column) => that.showchart2(row, column) },
];
const tableCols8 = [
  { istrue: true, display: true, prop: 'superviseId', exportField: 'superviseName', label: '运营主管', width: '80', formatter: (row) => row.superviseName },//, type: 'click', handle: (that, row) => that.showSuperviseDetail(row)
  { istrue: true, sortable: 'custom', prop: 'xiFenPlatform', label: '细分平台', width: '100', permission: "SegmentationPlatform" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'orderCount', label: '订单量', width: '100', formatter: (row) => !row.orderCount ? "0" : row.orderCount },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'payAmont', label: '付款金额', width: '100', formatter: (row) => !row.payAmont ? "0" : row.payAmont },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleAmont', label: '销售金额', width: '120', formatter: (row) => !row.saleAmont ? "0" : row.saleAmont },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontBefore', exportField: 'refundAmont', label: '退款', width: '120', tipmesg: '发货前退款', formatter: (row) => !row.refundAmontBefore ? "0" : row.refundAmontBefore },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontBeforeRate', exportField: 'refundAmontRateStr', label: '退款率', width: '120', tipmesg: '发货前退款率', formatter: (row) => !row.refundAmontBeforeRate ? "0%" : row.refundAmontBeforeRate.toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleCost', label: '成本', width: '120', formatter: (row) => !row.saleCost ? " " : row.saleCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit1After', label: '运营毛一(减退款)', width: '80', formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1', label: '毛一利润(发生)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit11', label: '毛一利润(付款)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate', exportField: 'profit1RateStr', label: '毛一利润率(发生)', width: '80', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate1', label: '毛一利润率(付款)', width: '90', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv', label: '广告费', width: '100', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv_rate', exportField: 'alladv_rateStr', label: '广告占比', width: '80', formatter: (row) => (row.alladv_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit2After', label: '运营毛二(减退款)', width: '80', formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3After', label: '运营毛三(减退款)', width: '80', formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3', label: '毛三利润(发生)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit31', label: '毛三利润(付款)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3AfterRate', label: '毛3率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit3AfterRate ? " " : (row.yyProfit3AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate', exportField: 'profit3_rateStr', label: '毛三利润率(发生)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate1', label: '毛三利润率(付款)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4After', label: '运营毛四(减退款)', width: '80', formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5After', label: '运营毛五(减退款)', width: '80', formatter: (row) => !row.yyProfit5After ? " " : row.yyProfit5After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6After', label: '运营毛六(减退款)', permission: "profit6SixtyCents", width: '80', formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5', label: '毛五利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5 ? " " : row.profit5 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6', label: '毛六利润(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6 ? " " : row.profit6 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4AfterRate', label: '毛4率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit4AfterRate ? " " : (row.yyProfit4AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5AfterRate', label: '毛5率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit5AfterRate ? " " : (row.yyProfit5AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6AfterRate', label: '毛6率(运营减退)', permission: "profit6SixtyCents", width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit6AfterRate ? " " : (row.yyProfit6AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate', exportField: 'profit33RateStr', label: '毛四利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate1', label: '毛四利润率(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5Rate', exportField: 'profit5Rate', label: '毛五利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6Rate', exportField: 'profit6Rate', label: '毛六利润率(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost', label: '出仓成本(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost1', label: '出仓成本(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '90', formatter: (row) => !row.warehouseSalary ? 0 : row.warehouseSalary },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCostRate', exportField: 'exitCostRateStr', label: '出仓成本占比', sortable: 'custom', width: '100', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + "%"},
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4', label: '净利润(发生)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit41', label: '净利润(付款)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate', exportField: 'profit4_rateStr', label: '净利率(发生)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate1', label: '净利率(付款)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'negativeExitProfitIdDetail', label: '出仓负利润', sortable: 'custom', width: '90', formatter: (row) => !row.negativeExitProfitIdDetail ? " " : row.negativeExitProfitIdDetail },
  { istrue: true, summaryEvent: true, display: true, prop: 'deductAmount', label: '违规扣款金额', width: '100', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
  { istrue: true, display: true, fixed: 'right', label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

const tableCols9 = [
  // { istrue: true, label: '小组头像', width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
  // { istrue: true, display: true, prop: 'groupName', label: '所在组', width: '70', formatter: (row) => row.groupName,type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, handle: (that, row) => that.showGroupDetail(row) },
  { istrue: true, sortable: 'custom', prop: 'platform', label: '平台', width: '45', formatter: (row) => formatPlatform(row.platform), type: 'custom' },
  { istrue: true, sortable: 'custom', prop: 'orderCount', label: '订单量', width: '80', formatter: (row) => !row.orderCount ? "0" : row.orderCount },
  { istrue: true, sortable: 'custom', prop: 'payAmont', label: '付款金额', width: '80', formatter: (row) => !row.payAmont ? "0" : row.payAmont },
  { istrue: true, sortable: 'custom', prop: 'saleAmont', label: '销售金额', width: '80', formatter: (row) => !row.saleAmont ? "0" : row.saleAmont },
  { istrue: true, sortable: 'custom', prop: 'refundAmontBefore', exportField: 'refundAmont', label: '退款', width: '80', tipmesg: '发货前退款', formatter: (row) => !row.refundAmontBefore ? "0" : row.refundAmontBefore },
  { istrue: true, sortable: 'custom', prop: 'refundAmontBeforeRate', exportField: 'refundAmontRateStr', label: '退款率', width: '80', tipmesg: '发货前退款率', formatter: (row) => !row.refundAmontBeforeRate ? "0%" : row.refundAmontBeforeRate.toString() + "%" },
  { istrue: true, sortable: 'custom', prop: 'saleCost', label: '成本', width: '80', formatter: (row) => !row.saleCost ? " " : row.saleCost },
  { istrue: true, sortable: 'custom', prop: 'yyProfit1After', label: '运营毛一(减退款)', width: '80', formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After },
  { istrue: true, sortable: 'custom', prop: 'profit1', label: '毛一利润(发生)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  { istrue: true, sortable: 'custom', prop: 'profit11', label: '毛一利润(付款)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  { istrue: true, sortable: 'custom', prop: 'profit1Rate', exportField: 'profit1RateStr', label: '毛一利润率(发生)', width: '80', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
  { istrue: true, sortable: 'custom', prop: 'profit1Rate1', label: '毛一利润率(付款)', width: '90', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
  { istrue: true, sortable: 'custom', prop: 'alladv', label: '广告费', width: '100', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
  { istrue: true, sortable: 'custom', prop: 'alladv_rate', exportField: 'alladv_rateStr', label: '广告占比', width: '80', formatter: (row) => (row.alladv_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', prop: 'yyProfit2After', label: '运营毛二(减退款)', width: '80', formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After },
  { istrue: true, sortable: 'custom', prop: 'yyProfit3After', label: '运营毛三(减退款)', width: '80', formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After },
  { istrue: true, sortable: 'custom', prop: 'profit3', label: '毛三利润(发生)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
  { istrue: true, sortable: 'custom', prop: 'profit31', label: '毛三利润(付款)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
  { istrue: true, sortable: 'custom', prop: 'yyProfit3AfterRate', label: '毛3率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit3AfterRate ? " " : (row.yyProfit3AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', prop: 'profit3_rate', exportField: 'profit3_rateStr', label: '毛三利润率(发生)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', prop: 'profit3_rate1', label: '毛三利润率(付款)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', prop: 'yyProfit4After', label: '运营毛四(减退款)', width: '80', formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After },
  { istrue: true, sortable: 'custom', prop: 'yyProfit5After', label: '运营毛五(减退款)', width: '80', formatter: (row) => !row.yyProfit5After ? " " : row.yyProfit5After },
  { istrue: true, sortable: 'custom', prop: 'yyProfit6After', label: '运营毛六(减退款)', permission: "profit6SixtyCents", width: '80', formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After },
  { istrue: true, sortable: 'custom', prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
  { istrue: true, sortable: 'custom', prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
  { istrue: true, sortable: 'custom', prop: 'profit5', label: '毛五利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5 ? " " : row.profit5 },
  { istrue: true, sortable: 'custom', prop: 'profit6', label: '毛六利润(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6 ? " " : row.profit6 },
  { istrue: true, sortable: 'custom', prop: 'yyProfit4AfterRate', label: '毛4率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit4AfterRate ? " " : (row.yyProfit4AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', prop: 'yyProfit5AfterRate', label: '毛5率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit5AfterRate ? " " : (row.yyProfit5AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', prop: 'yyProfit6AfterRate', label: '毛6率(运营减退)', permission: "profit6SixtyCents", width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit6AfterRate ? " " : (row.yyProfit6AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', prop: 'profit33Rate', exportField: 'profit33RateStr', label: '毛四利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
  { istrue: true, sortable: 'custom', prop: 'profit33Rate1', label: '毛四利润率(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
  { istrue: true, sortable: 'custom', prop: 'profit5Rate', exportField: 'profit5Rate', label: '毛五利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + "%" },
  { istrue: true, sortable: 'custom', prop: 'profit6Rate', exportField: 'profit6Rate', label: '毛六利润率(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + "%" },
  { istrue: true, sortable: 'custom', prop: 'exitCost', label: '出仓成本(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
  { istrue: true, sortable: 'custom', prop: 'exitCost1', label: '出仓成本(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
  { istrue: true, sortable: 'custom', prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '90', formatter: (row) => !row.warehouseSalary ? 0 : row.warehouseSalary },
  { istrue: true, sortable: 'custom', prop: 'exitCostRate', exportField: 'exitCostRateStr', label: '出仓成本占比', sortable: 'custom', width: '100', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + "%"},
  { istrue: true, sortable: 'custom', prop: 'profit4', label: '净利润(发生)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
  { istrue: true, sortable: 'custom', prop: 'profit41', label: '净利润(付款)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
  { istrue: true, sortable: 'custom', prop: 'profit4_rate', exportField: 'profit4_rateStr', label: '净利率(发生)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', prop: 'profit4_rate1', label: '净利率(付款)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', prop: 'negativeExitProfitIdDetail', label: '出仓负利润', sortable: 'custom', width: '90', formatter: (row) => !row.negativeExitProfitIdDetail ? " " : row.negativeExitProfitIdDetail },
  { istrue: true, display: true, prop: 'deductAmount', label: '违规扣款金额', width: '100', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
  { istrue: true, display: true, prop: 'progress', label: '完成比例', type: "progress" },
  // { istrue: true, sortable: 'custom', display: true, prop: 'onlineStatus', label: '员工状态', width: '80', formatter: (row) => row.onlineStatus },
  // { istrue: true, display: true, prop: 'createTime', label: '入职时间', width: '100' },
  // { istrue: true, display: true, sortable: 'custom', prop: 'workTime', label: '上班时长', width: '80', formatter: (row) => (row.workTime).toString() },
  // { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

const startDate = ("YYYY-MM-DD");
const endDate = ("YYYY-MM-DD");

const tableHandles = [
];
export default {
  name: "Users",
  components: { container, MyConfirmButton, MySearch, MySearchWindow, cesTable, productdrchart, InputMult, freightDetail, financialReportStaticsByOneUser, buschar, groupShopDetail, vxetablebase },
  data() {
    return {
      popoverdetailvisible: false,
      isVisible1: true,
      isVisible2: false,
      pricelist: [],
      platformData: [],
      editList: [],
      buttonStyle: ['default', 'success', 'warning', 'danger', 'info'],
      hideButtons: false,
      buttonType: 'info',
      childcompany: childcompany,
      tablekey: null,
      dialogMapVisible: {
        visible: false,
        title: "",
        data: [],
        filter: {
          timerange: []
        },
        params: {},
        type: 0//1趋势图汇总专员、助理 2汇总运营组、店铺  3助理 4专员 5运营组 6店铺 8运营主管
      },
      dialogGroupDetailVisible: { visible: false, title: "" },
      that: this,
      quantityprocessed: { visible: false, title: "", data: {} },
      collectquantityprocessed: { visible: false, title: "", data: {} },
      pricer: {
        filter: {
          isStar: 1,
          groupId: null,
        },
      },
      price: {
        filter: {
          starLv: null,
          startYmdDate: null,
          endYmdDate: null,
          value1: null,
          value2: [startDate, endDate],
          value3: [startDate, endDate],
          isStar: null,
          platform: 9,
          groupId: null,
          company: null,
        },
      },
      groupShopDetail: {
        filter: {
          timerange: [],
          groupId: '',
          platform: '',
          UserType: '',
          shopCode: '',
          company: '',
          timerange2: [],
          Profit3Lose: null,
          Profit33Lose: null,
          startTime3: null,
          endTime3: null,
          timerangeOnTime: [],
          superviseIds: [],
        }
      },
      segmentationList: [],
      filter: {
        platform: 1,
        shopCode: null,
        proCode: null,
        productName: null,
        groupId: null,
        groupIds: [],
        superviseIds: [],
        startTime: null,
        endTime: null,
        startTime3: null,
        endTime3: null,
        nickName: '',
        timerange: null,
        // 运营助理
        userId: null,
        // 车手
        userId2: null,
        // 备用
        userId3: null,
        // 运营专员 ID
        operateSpecialUserId: null,
        //分公司
        company: null,
        //付款维度
        refundType: 1,
        onlineStatus: null,
        timerangeOnTime: [],
        Profit3Lose: null,
        Profit33Lose: null,
        shopManager: null,
        timerangeEntry: [],
        startTimeEntry: null,
        endTimeEntry: null,
        startTime2: null,
        endTime2: null,
        timerange2: [],
        xiFenPlatformList: [],
      },
      onimportfilter: {
        yearmonthday: null,
      },
      //销售额	毛3利润	净利	广告费
      //成本	毛一利润	毛一利润率

      hahaha: 111,
      shopList: [],
      userList: [],
      grouplist: [],
      superviselist: [],
      directorlist: [],
      financialreportlist: [],
      visiblePersonal: false,

      showreportList: [],
      dialogSetParamZlVisible: false,//设置助理
      dialogSetParamZyVisible: false,//设置专员
      pageType: '',
      buttonStyle: ["info", "info", "info", "info", "info", "info", "info"],
      targetData: 0,
      buttonStyle: Array(22).fill("info"),
      targetData1: 0,
      userType: 1,
      targetType: 0,
      tableCols_1: tableCols1,
      tableCols_2: tableCols2,
      tableCols_21: tableCols21,
      tableCols_3: tableCols3,
      tableCols_4: tableCols4,
      tableCols_7: tableCols7,
      tableCols_8: tableCols8,
      tableCols_9: tableCols9,
      tableHandles: tableHandles,
      total: 0,
      pageer: { OrderBy: '', IsAsc: false, PageSize: 50, CurrentPage: 1 },
      pager: { OrderBy: null, IsAsc: false, pageSize: 100000 },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      summaryarry: {},
      selids: [],
      fileList: [],
      dialogVisible: false,
      uploadLoading: false,
      importFilte: {},
      fileList: [],
      fileparm: {},
      showprogress: true,
      editparmVisible: false,
      editLoading: false,
      editparmLoading: false,
      drawerVisible: false,
      collectdrawerVisible: false,
      dialogDrVisible: false,
      drparamProCode: '',
      autoformparm: {
        fApi: {},
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
        rule: []
      }, pickerOptions: {
        shortcuts: [{
          text: '前一天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
            window.setshowprogress(false);
          }
        }, {
          text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
            window.setshowprogress(false);
          }
        }, {
          text: '近一个月',
          onClick(picker) {
            const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate() - 1);
            const date2 = new Date(); date2.setDate(date2.getDate() - 1);
            picker.$emit('pick', [date1, date2]);
            window.setshowprogress(false);
          }
        }, {
          text: '近三个月',
          onClick(picker) {
            const date1 = new Date(); date1.setMonth(date1.getMonth() - 3); date1.setDate(date1.getDate() - 1);
            const date2 = new Date(); date2.setDate(date2.getDate() - 1);
            picker.$emit('pick', [date1, date2]);
            window.setshowprogress(true);
          }
        }]
      },
      freightDetail: {
        visible: false,
        filter: {
          proCode: null,
          timeRange: []
        }
      }
    };
  },
  async mounted() {
    this.onShowupMethod()
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    this.price.filter.value1 = yesterday.toISOString().split("T")[0];
    this.init();
    let _this = this;
    // if(localStorage.getItem("refundType")){
    //   _this.refundTypeChange();
    // }
    // this.filter.refundType = localStorage.getItem("refundType")
    middlevue.$on('refundType', function (msg) {
      _this.filter.refundType = msg;
      _this.refundTypeChange();
    })
    middlevue.$on('timerangeTx', function (msg) {
      _this.filter.timerange = msg;
      _this.refundTypeChange();
    })
    middlevue.$on('timerangeGC', function (msg) {
      _this.filter.timerange = msg;
      _this.refundTypeChange();
    })
    middlevue.$on('timerangeSn', function (msg) {
      _this.filter.timerange = msg;
      // _this.filter.refundType = msg;
      _this.refundTypeChange();
    })
    const { data: data2 } = await getAllShopXiFenPlatform();
    this.segmentationList = data2;
  },
  async created() {
    let res1 = await getDirectorGroupList();
    this.grouplist = res1.data?.map(item => { return { value: item.key, label: item.value }; });

    this.superviselist = [];
    let res2 = await getDirectorGroupList2();
    let sIds = res2.data?.filter(f => f.superviseId > 0).map(m => m.superviseId);
    let sIds2 = [...new Set(sIds)];
    if (sIds2.length > 0) {
      sIds2.forEach(f => {
        let cur = res2.data.find(x => x.id == f);
        if (cur) {
          this.superviselist.push({ value: cur.id, label: cur.userName })
        }
      });
    }
  },
  methods: {
    //日期选择器赋值
    changeTime(e, val) {
      const [startKey, endKey] = val === 1 ? ['startTime', 'endTime'] : val === 2 ? ['startTime3', 'endTime3'] : val === 3 ? ['startTimeEntry', 'endTimeEntry'] : ['startTime2', 'endTime2'];
      this.filter[startKey] = e ? e[0] : null;
      this.filter[endKey] = e ? e[val === 1 ? 1 : 0] : null;
      this.changeDate();
    },
    onShowupMethod(){
      let that = this;
      that.$nextTick(() => {
      const takePlace = ['profit1', 'profit1Rate', 'profit3', 'profit3_rate', 'profit33', 'profit33Rate', 'exitCost', 'profit4', 'profit4_rate'];
      const payment = ['profit11', 'profit1Rate1', 'profit31', 'profit3_rate1', 'profit331', 'profit33Rate1', 'exitCost1', 'profit41', 'profit4_rate1'];
          if (!that.visiblePersonal) {
            const tableIndex = that.userType;
            const tableRef = that.$refs[`table${tableIndex}`]; // 获取相应的表格引用
            if (tableRef) {
              if (that.filter.refundType === 1) {
                tableRef.changecolumn_setTrue(takePlace);
                tableRef.changecolumn(payment);
              } else {
                tableRef.changecolumn(takePlace);
                tableRef.changecolumn_setTrue(payment);
              }
            }
          }
      });
    },
    operationgroup(e) {
      if (!e) { this.changegroup() }
    },
    //行趋势图/汇总趋势图关闭后
    handleClose() {
      this.price.filter.value2 = null;
      this.price.filter.value3 = null;
    },
    //汇总行趋势图
    async onsummarypriceClick(row) {
      console.log("property", row);
      this.price.filter.endYmdDate = null;
      this.price.filter.startYmdDate = null;
      if (this.price.filter.value3 == null) {
        let date = new Date(this.price.filter.value1);
        date.setMonth(date.getMonth() - 1);
        this.price.filter.startYmdDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
        this.price.filter.endYmdDate = this.price.filter.value1;
      } else {
        this.price.filter.startYmdDate = this.price.filter.value3[0];
        this.price.filter.endYmdDate = this.price.filter.value3[1];
      }
      //汇总趋势图日期回显
      this.price.filter.value3 = [new Date(this.price.filter.startYmdDate), new Date(this.price.filter.endYmdDate)];
      this.price.filter.value3 = this.price.filter.value3.map((date, index) => {
        if (index === 0) {
          const dateObj = new Date(date + 'Z');
          dateObj.setDate(dateObj.getDate());
          return dateObj.toISOString().split('T')[0];
        }
        return new Date(date + 'Z').toISOString().split('T')[0];
      });
      const params = {
        ...this.price.filter,
      };
      const { data } = await getPriceForceSumChart(params);
      data.series.map((item) => {
        item.itemStyle = {
          "normal": {
            "label": {
              "show": true,
              "position": "top",
              "textStyle": {
                "fontSize": 14
              }
            }
          }
        }
        item.emphasis = {
          "focus": "series"
        }
        item.smooth = false;
      });
      this.collectquantityprocessed.visible = true;
      this.collectquantityprocessed.data = data
      this.collectquantityprocessed.title = data.legend[0]
      this.$nextTick(() => {
        this.$refs.collectdrawerbuschar.initcharts();
      });
      // await this.$refs.collectdrawerbuschar.initcharts()
      this.collectdrawerVisible = true;

    },
    //页面数量改变
    detailSizechange(val) {
      this.pageer.CurrentPage = 1;
      this.pageer.PageSize = val;
      this.forceSearch();
    },
    //当前页改变
    detailPagechange(val) {
      this.pageer.CurrentPage = val;
      this.forceSearch();
    },
    async showchart2(row, column) {
      this.price.filter.starLv = null;
      this.price.filter.endYmdDate = null;
      this.price.filter.startYmdDate = null;
      if (this.price.filter.value2 == null || this.price.filter.value2.length == 0) {
        let date = new Date(this.price.filter.value1);
        date.setMonth(date.getMonth() - 1);
        this.price.filter.startYmdDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
        this.price.filter.endYmdDate = this.price.filter.value1;
      } else {
        this.price.filter.startYmdDate = this.price.filter.value2[0];
        this.price.filter.endYmdDate = this.price.filter.value2[1];
      }
      //趋势图日期回显
      this.price.filter.value2 = [new Date(this.price.filter.startYmdDate), new Date(this.price.filter.endYmdDate)];
      this.price.filter.value2 = this.price.filter.value2.map((date, index) => {
        if (index === 0) {
          const dateObj = new Date(date + 'Z');
          dateObj.setDate(dateObj.getDate());
          return dateObj.toISOString().split('T')[0];
        }
        return new Date(date + 'Z').toISOString().split('T')[0];
      });

      this.price.filter.groupId = row.groupId,
        this.price.filter.isStar = row.isStar
      if (this.price.filter.groupId === undefined) {
        this.price.filter.groupId = 0,
          this.price.filter.isStar = 1
      }
      const params = {
        ...this.pageer,
        ...this.price.filter,
      };
      const { data } = await getPriceForceChart(params);
      data.series.map((item) => {
        item.itemStyle = {
          "normal": {
            "label": {
              "show": true,
              "position": "top",
              "textStyle": {
                "fontSize": 14
              }
            }
          }
        }

        item.emphasis = {
          "focus": "series"
        }
        item.smooth = false;
      })
      this.quantityprocessed.visible = true;
      this.quantityprocessed.data = data
      this.quantityprocessed.title = data.legend[0]
      this.$nextTick(() => {
        this.$refs.drawerbuschar.initcharts();
      });
      // await this.$refs.drawerbuschar.initcharts()

      this.drawerVisible = true;
    },
    async forceSearch() {
      if (this.price.filter.value1) {
        this.price.filter.startYmdDate = this.price.filter.value1;
        this.price.filter.endYmdDate = this.price.filter.value1;
      }
      if (this.price.filter.value1 === null) {
        this.price.filter.startYmdDate = null;
        this.price.filter.endYmdDate = null;
      }
      this.price.filter.value2 = null;
      this.price.filter.value3 = null;
      const params = {
        ...this.pageer,
        ...this.price.filter,
        ...this.pricer.filter,
      };
      if (params === false) {
        return;
      };
      // this.listLoading = true;
      const { data } = await getPriceForce(params);
      this.total = data.total;
      this.pricelist = data.list;
      this.summaryarry = data.summary;
      console.log("this.summaryarry", this.summaryarry);
    },
    async cellclick(row, column) {
      console.log("column", column);
      // this.price.filter.starLv = column.prop;
      switch (column.prop) {
        case 'starLv1CountArray':
          this.price.filter.starLv = 1;
          break;
        case 'starLv2CountArray':
          this.price.filter.starLv = 2;
          break;
        case 'starLv3CountArray':
          this.price.filter.starLv = 3;
          break;
        case 'starLv4CountArray':
          this.price.filter.starLv = 4;
          break;
        case 'starLv5CountArray':
          this.price.filter.starLv = 5;
          break;
        default:
          console.log(11);
      }
      console.log("this.price.filter.starLv", this.price.filter.starLv);
      //获取列表每列的坐标
      // let starLevel = parseInt(column.prop.replace(/[^\d]/g, ''), 10);
      // if (!isNaN(starLevel) && starLevel >= 1 && starLevel <= 5) {
      //   this.price.filter.isStar = starLevel;
      // }
      this.price.filter.groupId = row.groupId,
        this.price.filter.isStar = row.isStar,
        this.price.filter.startYmdDate = row.yearMonthDay
      this.price.filter.endYmdDate = row.yearMonthDay
      if (row.yearMonthDay) {
        const originalDate = row.yearMonthDay.toString();
        const formattedDate = `${originalDate.slice(0, 4)}-${originalDate.slice(4, 6)}-${originalDate.slice(6, 8)}`;
        this.price.filter.startYmdDate = formattedDate;
        this.price.filter.endYmdDate = formattedDate;
        this.price.filter.value2 = []
      }
      const params = {
        // ...this.pageer,
        ...this.price.filter,
        // ...this.pricer.filter,
      };
      const { data } = await getPriceForceDialog(params);
      this.editList = data,
        this.popoverdetailvisible = true;
    },
    async priceforce() {
      this.visiblePersonal = false;
      this.userType = 7;//价格力为7用来区分其他功能
      // this.filter.value1 = null;
      this.buttonType = this.buttonType === 'info' ? 'primary' : 'info';
      this.isVisible1 = !this.isVisible1;
      // this.isVisible2 = !this.isVisible2;
      if (this.isVisible2 === false) {
        this.isVisible2 = !this.isVisible2;
        this.userType == 7;
      } else {
        this.userType == 1;
        this.onShowZl();
        this.isVisible2 = !this.isVisible2;
      }
      if (this.buttonType === 'primary') {//价格力界面，取消掉其他功能
        this.$set(this.buttonStyle, 0, 'info');
        this.$set(this.buttonStyle, 1, 'info');
        this.$set(this.buttonStyle, 4, 'info');
        this.$set(this.buttonStyle, 5, 'info');
        this.$set(this.buttonStyle, 6, 'info');
        this.$set(this.buttonStyle, 7, 'info');
        this.$set(this.buttonStyle, 8, 'info');
        this.$set(this.buttonStyle, 21, 'info');
        this.$set(this.buttonStyle, 9, 'info');
        this.hideButtons = true;
      } else {
        this.$set(this.buttonStyle, 0, 'primary');
        this.$set(this.buttonStyle, 1, 'info');
        this.$set(this.buttonStyle, 4, 'info');
        this.$set(this.buttonStyle, 5, 'info');
        this.$set(this.buttonStyle, 6, 'info');
        this.$set(this.buttonStyle, 7, 'info');
        this.$set(this.buttonStyle, 8, 'info');
        this.$set(this.buttonStyle, 21, 'info');
        this.$set(this.buttonStyle, 9, 'info');
        this.hideButtons = false;
      }
      // this.queryReport()
      this.onShowupMethod()
      this.forceSearch();
    },

    async sortforcechange(column) {
      if (!column.order)
        this.pageer = {};
      else
        this.pageer = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.forceSearch();
    },

    mapColumnProp(prop) {
      const propMapping = {
          'profit4_rate1': 'profit4_rate',
          'profit41': 'profit4',
          'profit11': 'profit1',
          'profit1Rate1': 'profit1Rate',
          'profit31': 'profit3',
          'profit3_rate1': 'profit3_rate',
          'profit331': 'profit33',
          'profit33Rate1': 'profit33Rate',
          'exitCost1': 'exitCost',
      };
      return propMapping[prop] || prop; // 返回映射的属性，或者原属性
    },
    async sortchange(column) {

      if (!column.order) this.pager = {};
      else {
        column.prop = this.mapColumnProp(column.prop),
        this.pager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      }
      await this.onSearch();
    },
    async onSearch() {

      this.queryReport();
    },
    async showGroupDetail(vals) {
      this.dialogGroupDetailVisible.visible = true;
      this.groupShopDetail.filter.timerange = this.filter.timerange
      this.groupShopDetail.filter.platform = this.filter.platform
      this.groupShopDetail.filter.groupId = vals.groupId
      this.groupShopDetail.filter.shopCode = null
      this.groupShopDetail.filter.UserType = this.userType
      this.groupShopDetail.filter.company = this.filter.company
      this.groupShopDetail.filter.refundType = this.filter.refundType
      this.groupShopDetail.filter.Profit3Lose = this.filter.Profit3Lose
      this.groupShopDetail.filter.Profit33Lose = this.filter.Profit33Lose
      this.groupShopDetail.filter.timerangeOnTime = this.filter.timerangeOnTime
      this.$nextTick(async () => {
        await this.$refs.groupShopDetail.onSearch();
      });
    },
    async showSuperviseDetail(vals) {
      this.dialogGroupDetailVisible.visible = true;
      this.groupShopDetail.filter.timerange = this.filter.timerange
      this.groupShopDetail.filter.platform = this.filter.platform
      this.groupShopDetail.filter.groupId = null
      this.groupShopDetail.filter.shopCode = null
      this.groupShopDetail.filter.UserType = this.userType
      this.groupShopDetail.filter.company = this.filter.company
      this.groupShopDetail.filter.refundType = this.filter.refundType
      this.groupShopDetail.filter.Profit3Lose = this.filter.Profit3Lose
      this.groupShopDetail.filter.Profit33Lose = this.filter.Profit33Lose
      this.groupShopDetail.filter.timerangeOnTime = this.filter.timerangeOnTime
      this.groupShopDetail.filter.superviseIds = [vals.superviseId]
      this.$nextTick(async () => {
        await this.$refs.groupShopDetail.onSearch();
      });
    },
    async showShopDetail(vals) {
      this.dialogGroupDetailVisible.visible = true;
      this.groupShopDetail.filter.timerange = this.filter.timerange
      this.groupShopDetail.filter.platform = this.filter.platform
      this.groupShopDetail.filter.groupId = null
      this.groupShopDetail.filter.shopCode = vals.shopCode
      this.groupShopDetail.filter.UserType = this.userType
      this.groupShopDetail.filter.company = this.filter.company
      this.groupShopDetail.filter.refundType = this.filter.refundType
      this.groupShopDetail.filter.Profit3Lose = this.filter.Profit3Lose
      this.groupShopDetail.filter.Profit33Lose = this.filter.Profit33Lose
      this.groupShopDetail.filter.timerangeOnTime = this.filter.timerangeOnTime
      this.$nextTick(async () => {
        await this.$refs.groupShopDetail.onSearch();
      });
    },
    async showchart(row) {
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1]
      }
      let startTime3 = null
      let endTime3 = null
      if(this.filter.timerangeOnTime && this.filter.timerangeOnTime.length>0){
          startTime3 = this.filter.timerangeOnTime[0]
          endTime3 = this.filter.timerangeOnTime[1]
      } else{
        startTime3 = null
        endTime3 = null
      }

      // let date1 = new Date();
      // date1.setMonth(date1.getMonth() - 1);
      // date1.setDate(date1.getDate() - 1);
      // let date2 = new Date();
      // date2.setDate(date2.getDate() - 1);

      let date1 = new Date(this.filter.timerange[1]);
      date1.setMonth(date1.getMonth() - 1);
      date1.setDate(date1.getDate() - 1);
      let date2 = new Date(this.filter.timerange[1]);

      let data3 = new Date(this.filter.timerange[0]).getTime();
      if (date1.getTime() > data3) {
        date1 = new Date(this.filter.timerange[0]);
      }
      this.dialogMapVisible.filter.timerange = [date1, date2];


      if (this.userType == 1) {
        var params = {
          userId: row.userId,
          startTime: this.dialogMapVisible.filter.timerange[0],
          endTime: this.dialogMapVisible.filter.timerange[1],
          groupId: row.groupId,startTime3,endTime3,
          profit33Lose: this.filter.Profit33Lose,
          userType: this.userType, platform: this.filter.platform, groupId: this.filter.groupId, refundType: this.filter.refundType
        }
        let that = this;

        that.dialogMapVisible.type = 3;
        that.dialogMapVisible.params = params;
        const res = await getPerformanceStaticticsByUserMap(params).then(res => {
          that.dialogMapVisible.visible = true;
          that.dialogMapVisible.data = res.data
          that.dialogMapVisible.title = res.data.legend[0]
        })

        this.dialogMapVisible.visible = true
      }
      else if (this.userType == 2 || this.userType == 21) {
        var params = {
          operateSpecialUserId: row.operateSpecialUserId,
          startTime: this.dialogMapVisible.filter.timerange[0],
          endTime: this.dialogMapVisible.filter.timerange[1],
          groupId: row.groupId,startTime3,endTime3,
          profit33Lose: this.filter.Profit33Lose,
          userType: this.userType, platform: this.filter.platform, groupId: this.filter.groupId, refundType: this.filter.refundType
        }
        let that = this;

        that.dialogMapVisible.type = 4;
        that.dialogMapVisible.params = params;
        const res = await getPerformanceStaticticsByUserMap(params).then(res => {
          that.dialogMapVisible.visible = true;
          that.dialogMapVisible.data = res.data
          that.dialogMapVisible.title = res.data.legend[0]
        })
        this.dialogMapVisible.visible = true
      }
      else if (this.userType == 4) {
        var params = {
          shopCode: row.shopCode,
          startTime: this.dialogMapVisible.filter.timerange[0],
          endTime: this.dialogMapVisible.filter.timerange[1],
          groupId: row.groupId,startTime3,endTime3,
          userType: this.userType, platform: this.filter.platform, groupId: this.filter.groupId, Company: this.filter.company, refundType: this.filter.refundType
        }
        let that = this;

        that.dialogMapVisible.type = 6;
        that.dialogMapVisible.params = params;
        const res = await getPerformanceStaticticsByShopMap(params).then(res => {
          that.dialogMapVisible.visible = true;
          that.dialogMapVisible.data = res.data
          that.dialogMapVisible.title = res.data.legend[0]
        })
        this.dialogMapVisible.visible = true
      }
      else if (this.userType == 3) {
        var params = {
          groupId: row.groupId,
          startTime: this.dialogMapVisible.filter.timerange[0],
          endTime: this.dialogMapVisible.filter.timerange[1],
          groupId: row.groupId,startTime3,endTime3,
          platform: this.filter.platform, Company: this.filter.company, refundType: this.filter.refundType
        }
        let that = this;

        that.dialogMapVisible.type = 5;
        that.dialogMapVisible.params = params;
        const res = await getPerformanceStaticticsByGroupMap(params).then(res => {
          that.dialogMapVisible.visible = true;
          that.dialogMapVisible.data = res.data
          that.dialogMapVisible.title = res.data.legend[0]
        })
        this.dialogMapVisible.visible = true
      }
      else if (this.userType == 8) {
        let params = {
          superviseIds: !row.superviseId ? [-1] : [row.superviseId],
          startTime: this.dialogMapVisible.filter.timerange[0],
          endTime: this.dialogMapVisible.filter.timerange[1],
          platform: this.filter.platform,
          refundType: this.filter.refundType,
          profit3Lose: this.filter.Profit3Lose,
          profit33Lose: this.filter.Profit33Lose,
          groupId: row.groupId,startTime3,endTime3,
        }

        if (this.filter.timerangeOnTime&&this.filter.timerangeOnTime.length>0) {
          params.startTime3 = this.filter.timerangeOnTime[0];
          params.endTime3 = this.filter.timerangeOnTime[1];
        }

        let that = this;
        that.dialogMapVisible.type = 8;
        that.dialogMapVisible.params = params;
        const res = await getPerformanceStaticticsByonSuperviseMap(params).then(res => {
          that.dialogMapVisible.visible = true;
          that.dialogMapVisible.data = res.data
          that.dialogMapVisible.title = res.data.legend[0]
        })
        this.dialogMapVisible.visible = true
      }
    },
    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async changeDate() {
      this.queryReport();
    },
    async init() {
      var date1 = new Date();
      //date1.setMonth(date1.getMonth() - 3);
      date1.setDate(date1.getDate() - 1);
      var date2 = new Date(); date2.setDate(date2.getDate() - 1);
      this.filter.timerange = [];
      this.filter.timerange[0] = this.datetostr(date1);
      this.filter.timerange[1] = this.datetostr(date2);
      var that = this;
      this.userType = 1;

      this.pageType = "运营助理"
      if (this.targetType == 0 && checkPermission('TxPerformance_TxPerformanceProfit3')) {
        this.targetType = 1;
      } else if (this.targetType == 0 && checkPermission('TxPerformance_TxPerformanceNetProfit')) {
        this.targetType = 2;
      }
      this.targetData = (await getPerformanceTarget({ targetName: '运营助理毛三目标' })).data;
      this.targetData1 = (await getPerformanceTarget({ targetName: '运营助理净利目标' })).data;

      window.setshowprogress = function (status) {
        that.showprogress = status;
      }

      this.queryReport();
    },
    async onShowZl() {
      this.tablekey = 'onShowZl';
      this.targetData = (await getPerformanceTarget({ targetName: '运营助理毛三目标' })).data;
      this.targetData1 = (await getPerformanceTarget({ targetName: '运营助理净利目标' })).data;
      this.buttonType = 'info';
      if (this.isVisible2 == true) {
        this.isVisible2 = false;//通过运营助理按钮取消价格力
        this.isVisible1 = true;
        this.hideButtons = false;
      }

      this.pageType = "运营助理"
      this.userType = 1;
      this.pager.OrderBy = null;
      this.onShowupMethod()
      this.queryReport()
    },
    async onShowDj() {
      this.tablekey = 'onShowDj';
      this.targetData = (await getPerformanceTarget({ targetName: '店经毛三目标' })).data;
      this.targetData1 = (await getPerformanceTarget({ targetName: '店经净利目标' })).data;
      this.buttonType = 'info';
      if (this.isVisible2 == true) {
        this.isVisible2 = false;//通过运营带教按钮取消价格力
        this.isVisible1 = true;
        this.hideButtons = false;
      }
      this.pageType = "运营带教"
      this.userType = 21;
      this.pager.OrderBy = null;
      this.onShowupMethod()
      this.queryReport()
    },
    async onShowZy() {
      this.tablekey = 'onShowZy';
      this.targetData = (await getPerformanceTarget({ targetName: '运营专员毛三目标' })).data;
      this.targetData1 = (await getPerformanceTarget({ targetName: '运营专员净利目标' })).data;
      this.buttonType = 'info';
      if (this.isVisible2 == true) {
        this.isVisible2 = false;//通过运营专员按钮取消价格力
        this.isVisible1 = true;
        this.hideButtons = false;
      }

      this.pageType = "运营专员"
      this.userType = 2;
      this.pager.OrderBy = null;
      this.onShowupMethod()
      this.queryReport()
    },

    async onShowGp() {
      this.tablekey = 'onShowGp';
      this.targetData = (await getPerformanceTarget({ targetName: '运营专员毛三目标' })).data;
      this.targetData1 = (await getPerformanceTarget({ targetName: '运营专员净利目标' })).data;
      this.buttonType = 'info';
      if (this.isVisible2 == true) {
        this.isVisible2 = false;//通过运营组按钮取消价格力
        this.isVisible1 = true;
        this.hideButtons = false;
      }

      this.pageType = "运营组"
      this.userType = 3;
      this.pager.OrderBy = null;
      this.onShowupMethod()
      this.queryReport()
    },

    async onShowSupervise() {
      this.tablekey = 'onShowSupervise';
      this.targetData = (await getPerformanceTarget({ targetName: '运营专员毛三目标' })).data;
      this.targetData1 = (await getPerformanceTarget({ targetName: '运营专员净利目标' })).data;
      this.buttonType = 'info';
      if (this.isVisible2 == true) {
        this.isVisible2 = false;//通过运营组按钮取消价格力
        this.isVisible1 = true;
        this.hideButtons = false;
      }

      this.pageType = "运营主管"
      this.userType = 8;
      this.pager.OrderBy = null;
      this.onShowupMethod()
      this.queryReport()
    },
    async onShowShop() {
      this.tablekey = 'onShowShop';
      this.targetData = (await getPerformanceTarget({ targetName: '运营专员毛三目标' })).data;
      this.targetData1 = (await getPerformanceTarget({ targetName: '运营专员净利目标' })).data;
      this.buttonType = 'info';
      if (this.isVisible2 == true) {
        this.isVisible2 = false;//通过店铺按钮取消价格力
        this.isVisible1 = true;
        this.hideButtons = false;
      }

      this.pageType = "店铺"
      this.userType = 4;
      this.pager.OrderBy = null;
      this.onShowupMethod()
      this.queryReport()
    },

    async onAllPlatform() {
      this.tablekey = 'onAllPlatform';
      this.userType = 9;
      this.pager.OrderBy = null;
      this.onShowupMethod()
      this.queryReport()
    },

    async getShopList() {
      const { data } = await getAllShopList({ platforms: [this.filter.platform] });
      this.shopList = data.filter((item) => item.shopManager !== null).map(
        (item) => ({ label: item.shopManager, value: item.shopCode })
      );
    },
    async queryReport() {
      if (this.targetType == 0) {
        return;
      }
      this.getShopList();
      this.visiblePersonal = false;
      startLoading();
      this.showreportList = [];
      this.total = 0;
      this.summaryarry = {};
      this.filter.startTime = null;
      this.filter.endTime = null;
      this.filter.startTime2 = null;
      this.filter.endTime2 = null;
      this.filter.startTime3 = null;
      this.filter.endTime3 = null;
      this.filter.startTimeEntry = null;
      this.filter.endTimeEntry = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      if (this.filter.timerange2) {
        this.filter.startTime2 = this.filter.timerange2[0];
        this.filter.endTime2 = this.filter.timerange2[1];
      }
      if (this.filter.timerangeOnTime) {
        this.filter.startTime3 = this.filter.timerangeOnTime[0];
        this.filter.endTime3 = this.filter.timerangeOnTime[1];
      }
      if (this.filter.timerangeEntry) {
        this.filter.startTimeEntry = this.filter.timerangeEntry[0];
        this.filter.endTimeEntry = this.filter.timerangeEntry[1];
      }
      if (!this.filter.groupId) {
        this.filter.groupId = ''
      }
      var that = this;
      var pager = this.pager;
      console.log(this.userType,'this.userType');
      if (this.userType == 1) {
        this.$set(this.buttonStyle, 0, 'primary');
        this.$set(this.buttonStyle, 1, 'info');
        this.$set(this.buttonStyle, 4, 'info');
        this.$set(this.buttonStyle, 5, 'info');
        this.$set(this.buttonStyle, 6, 'info');
        this.$set(this.buttonStyle, 21, 'info');
        this.$set(this.buttonStyle, 9, 'info');
      }
      if (this.userType == 2) {
        this.$set(this.buttonStyle, 1, 'primary');
        this.$set(this.buttonStyle, 0, 'info');
        this.$set(this.buttonStyle, 4, 'info');
        this.$set(this.buttonStyle, 5, 'info');
        this.$set(this.buttonStyle, 6, 'info');
        this.$set(this.buttonStyle, 21, 'info');
        this.$set(this.buttonStyle, 9, 'info');
      }
      if (this.userType == 3) {
        this.$set(this.buttonStyle, 4, 'primary');
        this.$set(this.buttonStyle, 0, 'info');
        this.$set(this.buttonStyle, 1, 'info');
        this.$set(this.buttonStyle, 5, 'info');
        this.$set(this.buttonStyle, 6, 'info');
        this.$set(this.buttonStyle, 21, 'info');
        this.$set(this.buttonStyle, 9, 'info');
      }
      if (this.userType == 4) {
        this.$set(this.buttonStyle, 5, 'primary');
        this.$set(this.buttonStyle, 0, 'info');
        this.$set(this.buttonStyle, 1, 'info');
        this.$set(this.buttonStyle, 4, 'info');
        this.$set(this.buttonStyle, 6, 'info');
        this.$set(this.buttonStyle, 21, 'info');
        this.$set(this.buttonStyle, 9, 'info');
      }
      if (this.userType == 8) {
        this.$set(this.buttonStyle, 6, 'primary');
        this.$set(this.buttonStyle, 0, 'info');
        this.$set(this.buttonStyle, 1, 'info');
        this.$set(this.buttonStyle, 4, 'info');
        this.$set(this.buttonStyle, 5, 'info');
        this.$set(this.buttonStyle, 21, 'info');
        this.$set(this.buttonStyle, 9, 'info');
      }
      if (this.userType ==  21){
        this.$set(this.buttonStyle, 21, 'primary');
        this.$set(this.buttonStyle, 6, 'info');
        this.$set(this.buttonStyle, 0, 'info');
        this.$set(this.buttonStyle, 1, 'info');
        this.$set(this.buttonStyle, 4, 'info');
        this.$set(this.buttonStyle, 5, 'info');
        this.$set(this.buttonStyle, 9, 'info');
      }
      if (this.userType == 9) {
        this.$set(this.buttonStyle, 9, 'primary');
        this.$set(this.buttonStyle, 0, 'info');
        this.$set(this.buttonStyle, 1, 'info');
        this.$set(this.buttonStyle, 4, 'info');
        this.$set(this.buttonStyle, 5, 'info');
      }
      if (this.targetType == 1) {
        if (pager.OrderBy == null) {
          pager.OrderBy = "profit3"
          this.pager.OrderBy = "profit3"
        }
        this.$set(this.buttonStyle, 2, 'primary');
        this.$set(this.buttonStyle, 3, 'info');
      }

      else {
        if (pager.OrderBy == null) {
          pager.OrderBy = "profit4"
          this.pager.OrderBy = "profit4"
        }
        this.$set(this.buttonStyle, 3, 'primary');
        this.$set(this.buttonStyle, 2, 'info');
      }
      const params = { ...pager, ...this.pager, ...this.filter };
      params.UserType = this.userType
      var data = [];
      if (this.userType == 3)
        data = (await getPerformanceStaticticsByUser(params));
      else if (this.userType == 4)
        data = (await getPerformanceStaticticsByUser(params));
      else
        data = (await getPerformanceStaticticsByUser(params));

      loading.close();

      if (data.code == 403) {

        this.showreportList = [];
        return;
      }

      for (var i in data.data.list) {
        if (this.targetType == 1) {
          //this.tableCols[8].display=true;
          if (data.data.list[i].profit3 > 0 && this.targetData != 0) {
            data.data.list[i].progress = (data.data.list[i].profit3 * 100 / this.targetData);
          } else {
            data.data.list[i].progress = 0;
          };
        }
        else if (this.targetType == 2) {
          //this.tableCols[8].display=true;
          if (data.data.list[i].profit4 > 0 && this.targetData1 != 0) {
            data.data.list[i].progress = (data.data.list[i].profit4 * 100 / this.targetData1);
          }
          else {
            data.data.list[i].progress = 0;
          }
        }

      }
      this.showreportList = data.data.list;
      that.total = data.data?.total;
      that.summaryarry = data.data?.summary;
        for (const key in that.summaryarry) {
          that.summaryarry.profit4_rate1_sum = that.summaryarry.profit4_rate_sum;
          that.summaryarry.profit41_sum = that.summaryarry.profit4_sum;
          that.summaryarry.exitCost1_sum = that.summaryarry.exitCost_sum;
          that.summaryarry.profit331_sum = that.summaryarry.profit33_sum;
          that.summaryarry.profit33Rate1_sum = that.summaryarry.profit33Rate_sum;
          that.summaryarry.profit31_sum = that.summaryarry.profit3_sum;
          that.summaryarry.profit1Rate1_sum = that.summaryarry.profit1Rate_sum;
          that.summaryarry.profit11_sum = that.summaryarry.profit1_sum;
          that.summaryarry.profit3_rate1_sum = that.summaryarry.profit3_rate_sum;
        }
      that.summaryarry.alladv_rate_sum = that.summaryarry.alladv_rate_sum.toString() + "%"
      that.summaryarry.profit3_rate_sum = that.summaryarry.profit3_rate_sum.toString() + "%"
      that.summaryarry.profit4_rate_sum = that.summaryarry.profit4_rate_sum.toString() + "%"
      that.summaryarry.refundAmontBeforeRate_sum = that.summaryarry.refundAmontBeforeRate_sum.toString() + "%"
      that.summaryarry.profit1Rate_sum = that.summaryarry.profit1Rate_sum.toString() + "%"


    },
    async onShowGroup() {
      const count = this.financialreportlist.filter(item => item.status === '0').length; // 6
    },
    async showprchart2(prcode) {
      window['lastseeprcodedrchart'] = prcode
      this.drparamProCode = prcode
      this.dialogDrVisible = true
    },

    async changegroup() {

      this.queryReport();
    },

    async saveZl() {

      SetPerformanceTarget({ targetName: '运营助理毛三目标', targetData: this.targetData });
      SetPerformanceTarget({ targetName: '运营助理净利目标', targetData: this.targetData1 });
      this.dialogSetParamZlVisible = false;

    },
    async saveZy() {
      SetPerformanceTarget({ targetName: '运营专员毛三目标', targetData: this.targetData });
      SetPerformanceTarget({ targetName: '运营专员净利目标', targetData: this.targetData1 });
      this.dialogSetParamZyVisible = false;
    },
    async editZlparam() {
      this.targetData = (await getPerformanceTarget({ targetName: '运营助理毛三目标' })).data;
      this.targetData1 = (await getPerformanceTarget({ targetName: '运营助理净利目标' })).data;
      this.dialogSetParamZlVisible = true;
    },
    async editZyparam() {
      this.targetData = (await getPerformanceTarget({ targetName: '运营专员毛三目标' })).data;
      this.targetData1 = (await getPerformanceTarget({ targetName: '运营专员净利目标' })).data;
      this.dialogSetParamZyVisible = true;
    },
    async onShowTarget(ttype) {
      this.targetType = ttype;
      this.queryReport()
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    async canclick(row, column, cell) {
      this.visiblePersonal = true;
      this.filter.startTime = null;
      this.filter.endTime = null;
      this.filter.startTime3 = null;
      this.filter.endTime3 = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      if (this.filter.timerangeOnTime) {
        this.filter.startTime3 = this.filter.timerangeOnTime[0] ? this.filter.timerangeOnTime[0] : null;
        this.filter.endTime3 = this.filter.timerangeOnTime[1] ? this.filter.timerangeOnTime[1] : null;
      }
      window.setFinancialFilterTime(this.filter.startTime, this.filter.endTime, row.userId, row.operateSpecialUserId, row.groupId, this.filter.platform, this.filter.refundType, this.filter.startTime3, this.filter.endTime3, this.filter.Profit3Lose, this.filter.Profit33Lose, this.userType);
    },
    //汇总趋势图
    async onsummaryClick(property) {
      startLoading();
      this.filter.startTime = null;
      this.filter.endTime = null;

      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }

      if (!this.filter.groupId) {
        this.filter.groupId = ''
      }
      var that = this;
      var pager = this.pager;
      const params = { ...pager, ...this.pager, ...this.filter };

      let date1 = new Date(this.filter.timerange[1]);
      date1.setMonth(date1.getMonth() - 1);
      date1.setDate(date1.getDate() - 1);
      let date2 = new Date(this.filter.timerange[1]);

      let data3 = new Date(this.filter.timerange[0]).getTime();
      if (date1.getTime() > data3) {
        date1 = new Date(this.filter.timerange[0]);
      }

      this.dialogMapVisible.filter.timerange = [date1, date2];

      if (this.dialogMapVisible.filter.timerange) {
        params.startTime = this.dialogMapVisible.filter.timerange[0];
        params.endTime = this.dialogMapVisible.filter.timerange[1];
      }

      params.UserType = this.userType
      params.column = this.mapColumnProp(property);
      console.log("点击事件", params)
      if (this.pageType == '运营助理' || this.pageType == '运营专员' || this.pageType == '运营带教') {
        await getPerformanceStaticticsByUserAnalysis(params).then(res => {
          that.dialogMapVisible.type = 1;
          that.dialogMapVisible.params = params;
          that.dialogMapVisible.visible = true;
          that.dialogMapVisible.data = res.data
          that.dialogMapVisible.title = res.data.legend[0]
          console.log("运营助理1111params", params)
        });
      }
      else if (this.pageType == '运营组' || this.pageType == '店铺') {
        params.IsTxShopSearch = this.pageType == '店铺';
        await getPerformanceStaticticsByGroupAnalysis(params).then(res => {
          that.dialogMapVisible.type = 2;
          that.dialogMapVisible.params = params;
          that.dialogMapVisible.visible = true;
          that.dialogMapVisible.data = res.data
          that.dialogMapVisible.title = res.data.legend[0]
        });
      }
      else if (this.pageType == '运营主管') {
        if (this.filter.timerangeOnTime) {
          params.startTime3 = this.filter.timerangeOnTime[0];
          params.endTime3 = this.filter.timerangeOnTime[1];
        }
        let that = this;
        that.dialogMapVisible.type = 8;
        that.dialogMapVisible.params = params;
        const res = await getPerformanceStaticticsByonSuperviseMap(params).then(res => {
          that.dialogMapVisible.visible = true;
          that.dialogMapVisible.data = res.data
          that.dialogMapVisible.title = res.data.legend[0]
        })
        this.dialogMapVisible.visible = true
      }

      loading.close();
    },
    //导出
    async onExport() {
      if (this.targetType == 0) {
        return;
      }
      let pager = this.pager;
      if (this.targetType == 1) {
        if (pager.OrderBy == null) {
          pager.OrderBy = "profit3"
          this.pager.OrderBy = "profit3"
        }
      }
      else {
        if (pager.OrderBy == null) {
          pager.OrderBy = "profit4"
          this.pager.OrderBy = "profit4"
        }
      }
      const params = { "targetType": this.targetType, ...pager, ...this.pager, ...this.filter };
      params.UserType = this.userType
        let exportCnColumns = [];
        let exportColumns = [];
        const processTableColumns = (tableCols) => {
        tableCols.forEach(item => {
            if (item.label && item.prop) {
              const updatedLabel = this.filter.refundType == 2
                  ? (item.label.includes('(发生)') ? item.label.replace('(发生)', '(付款)')
                    : item.label.includes('(付款)') ? item.label.replace('(付款)', '(发生)')
                    : item.label)
                  : item.label;
              exportCnColumns.push(updatedLabel);
              exportColumns.push(item.exportField ? item.exportField : item.prop);
            }
          });
        };

        if (this.userType == 3) {
            processTableColumns(this.tableCols_3);
        } else if (this.userType == 1) {
            processTableColumns(this.tableCols_1);
        } else if (this.userType == 4) {
            processTableColumns(this.tableCols_4);
        } else if (this.userType == 8) {
            processTableColumns(this.tableCols_8);
        } else if (this.userType == 2) {
            processTableColumns(this.tableCols_2);
        } else if (this.userType == 7) {
            processTableColumns(this.tableCols_7);
        } else if (this.userType == 21) {
            processTableColumns(this.tableCols_21);
        } else if (this.userType == 9) {
            processTableColumns(this.tableCols_9);
        }
        params.YH_EXT_ExportCnColumns = exportCnColumns;
        params.YH_EXT_ExportColumns = exportColumns;
      let res;
      let name = "";
      if (this.userType == 3) {
        res = (await exportPerformanceStaticticsByGroup(params));
        name = "运营组";
      }
      else if (this.userType == 4) {
        res = (await exportPerformanceStaticticsByShop(params));
        name = "店铺";
      }
      else if (this.userType == 8) {
        res = (await exportPerformanceStaticticsByUser(params));
        name = "运营主管";
      }
      else {
        if (this.userType == 1) {
          name = "运营助理";
        } else if(this.userType == 21){
          name = "运营带教";
        } else if(this.userType == 9){
          name = "全平台汇总";
        } else {
          name = "运营专员";
        }
        res = (await exportPerformanceStaticticsByUser(params));
      }
      if (!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '淘系人员业绩统计_' + name + '_' + new Date().toLocaleString() + '.xlsx');
      aLink.click()
    },
    async refundTypeChange() {
      this.onShowupMethod()
      this.queryReport();
    },
    async similarityDateChange() {
      if (this.dialogMapVisible.filter.timerange) {
        this.dialogMapVisible.filter.startTime = this.dialogMapVisible.filter.timerange[0];
        this.dialogMapVisible.filter.endTime = this.dialogMapVisible.filter.timerange[1];
      }
      let params = { ...this.dialogMapVisible.params, ...this.dialogMapVisible.filter }
      let that = this;
      if (this.dialogMapVisible.type == 1) {
        await getPerformanceStaticticsByUserAnalysis(params).then(res => {
          that.dialogMapVisible.data = res.data
          that.dialogMapVisible.title = res.data.legend[0]
        });
      } else if (this.dialogMapVisible.type == 2) {
        await getPerformanceStaticticsByGroupAnalysis(params).then(res => {
          that.dialogMapVisible.data = res.data
          that.dialogMapVisible.title = res.data.legend[0]
        });
      } else if (this.dialogMapVisible.type == 3 || this.dialogMapVisible.type == 4) {
        await getPerformanceStaticticsByUserMap(params).then(res => {
          that.dialogMapVisible.data = res.data
          that.dialogMapVisible.title = res.data.legend[0]
        })
      } else if (this.dialogMapVisible.type == 5) {
        await getPerformanceStaticticsByGroupMap(params).then(res => {
          that.dialogMapVisible.data = res.data
          that.dialogMapVisible.title = res.data.legend[0]
        })
      } else if (this.dialogMapVisible.type == 6) {
        await getPerformanceStaticticsByShopMap(params).then(res => {
          that.dialogMapVisible.data = res.data
          that.dialogMapVisible.title = res.data.legend[0]
        })
      }
      await this.$refs.dialogMapVisibleBuschar.initcharts()
    }
  }
};
</script>
<style lang="scss" scoped>
.el-progress-bar__innerText {
  padding-right: 100%;
  position: relative
}
</style>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
