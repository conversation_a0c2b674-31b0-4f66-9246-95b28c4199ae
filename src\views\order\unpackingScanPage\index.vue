<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 95%;">
            <el-tab-pane label="拆包件统计" name="first" style="height: 100%;" lazy>
                <unpackStatistics @toDetails="toDetails" />
            </el-tab-pane>
            <el-tab-pane label="拆包件明细" name="second" style="height: 100%;">
                <unpackDetails ref="unpackDetails" />
            </el-tab-pane>
            <el-tab-pane label="扫码明细" name="third" style="height: 100%;" lazy>
                <scanDetails />
            </el-tab-pane>
            <el-tab-pane label="货架统计" name="forth" style="height: 100%;" lazy>
                <shelvesStatistics />
            </el-tab-pane>
            <el-tab-pane label="工作量统计" name="fifth" style="height: 100%;" lazy>
                <unpackStatusStatistics />
            </el-tab-pane>
            <el-tab-pane label="拆包设置" name="sixth" style="height: 100%;" lazy>
                <unpackSetting v-if="checkPermission('unpackSettingView')" />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import scanDetails from './component/scanDetails.vue'
import shelvesStatistics from './component/shelvesStatistics.vue'
import unpackDetails from './component/unpackDetails.vue'
import unpackSetting from './component/unpackSetting.vue'
import unpackStatistics from './component/unpackStatistics.vue'
import unpackStatusStatistics from './component/unpackStatusStatistics.vue'
import MyContainer from '@/components/my-container'
export default {
    components: {
        scanDetails,
        shelvesStatistics,
        unpackDetails,
        unpackSetting,
        unpackStatistics,
        unpackStatusStatistics,
        MyContainer
    },
    data() {
        return {
            activeName: 'first'
        }
    },
    mounted() { },
    methods: {
        toDetails(e) {
            this.activeName = 'second'
            this.$refs.unpackDetails.getStatisticProps(e)
        }
    }
}
</script>

<style scoped lang="scss"></style>