<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="商品编码:">
                    <el-input v-model.trim="filter.goodsCode" clearable style="width: 110px" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="getlist">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' @sortchange='sortchange' :hasexpand='true' :tableData='list'
            :tableCols='tableCols' :loading="listLoading" style="width:100%;height:90%;margin: 0">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getlist" />
        </template>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import { pageOutOfStockTimeLog } from '@/api/inventory/abnormal'
import { formatTime, formatNoLink, formatIsCheckError } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
const tableCols = [
    { istrue: true, prop: 'goodsCode', label: '商品编码' },
    { istrue: true, prop: 'outOfStockTimeCurrent', label: '出现负库存数时间', sortable: 'custom', formatter: (row) => formatTime(row.outOfStockTimeCurrent, 'YYYY-MM-DD HH:mm:ss') },
];
export default {
    name: 'YunHanAdminAllAbnormalTimeLog',
    components: { MyContainer, cesTable },
    data() {
        return {
            that: this,
            filter: {
                goodsCode: null
            },
            list: [],
            pageLoading: false,
            tableCols: tableCols,
            pager: { OrderBy: "outOfStockTimeCurrent", IsAsc: false },
            total: 0,
        };
    },
    async mounted() {
    },
    methods: {
        async loadData({ goodsCode }) {
            this.filter.goodsCode = goodsCode;
            await this.getlist();
        },
        async getlist() {
            if (!this.pager.OrderBy) this.pager.OrderBy = "";
            var pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ...this.filter }
            this.listLoading = true
            const res = await pageOutOfStockTimeLog(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res.data.total
            const data = res.data.list
            this.list = data;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.getlist();
        },
    },
};
</script>
<style lang="scss" scoped></style>