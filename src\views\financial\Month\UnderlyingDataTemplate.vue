<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true'
              :hasexpand='true' @sortchange='sortchange' :tableData='dahuixionglist'
              @select='selectchange' :isSelection='false' :showsummary='true'  :summaryarry='summaryarry'
         :tableCols='tableCols' :loading="listLoading" >
        <el-table-column   type="expand" prop="nameGrossList" align="center" width="30" height="30">
          <template slot-scope="props">
          <ces-table style="height: 200px;" ref="table1" :that='that' :isIndex='true' :isSelectColumn="false"
              :hasexpand='true' @sortchange='sortchange' :tableData='props.row.nameGrossList'
              @select='selectchange' :isSelection='false' :showsummary='true'  :summaryarry='summaryarry'
         :tableCols='tableCols1' :loading="listLoading" ></ces-table>
        </template>
        </el-table-column>
      
       <template slot='extentbtn'>
          <el-button-group>
                            <el-button style="padding: 0;margin: 0;">
                                <el-input v-model.trim="Filter.ProCode" clearable maxlength="100" placeholder="商品ID" style="width:120px;"/>
                            </el-button>
                            <!-- <el-button style="padding: 0;margin: 0;">
                              <el-input v-model.trim="Filter.Name" clearable maxlength="10" placeholder="姓名" style="width:120px;"/>
                          </el-button> -->
                          <el-button style="padding: 0;margin: 0;">
                            <el-input v-model.trim="Filter.OperationsTeam" clearable maxlength="10" placeholder="运营小组" style="width:120px;"/>
                        </el-button>
                            <el-button style="padding: 0;margin: 0;">
                               <el-date-picker style="width:280px" v-model="Filter.UseDate" type="monthrange" format="yyyy-MM " value-format="yyyy-MM "
                                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" ></el-date-picker>
                            </el-button>
                            <el-button style="padding: 0;margin: 0;">
                              <el-select filterable v-model="Filter.platform" placeholder="请选择平台" @change="onchangeplatform" clearable style="width: 130px">
                                <el-option label="淘系" value='1'></el-option>
                                <el-option label="拼多多" value="2"></el-option>
                                <el-option label="阿里巴巴" value="4"></el-option>
                                <el-option label="抖音" value="6"></el-option>
                                <el-option label="京东" value="7"></el-option>
                                <el-option label="淘工厂" value="8"></el-option>
                                <el-option label="天猫" value="9"></el-option>
                                <el-option label="苏宁" value="10"></el-option>
                                </el-select>
                            </el-button>
                            <el-button style="padding: 0;margin: 0;">
                              <el-select filterable v-model="Filter.shopCode" placeholder="请选择店铺" clearable style="width: 130px">
                                  <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"/>
                                </el-select>
                            </el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="ExportOperatingCommission">导出</el-button>
            <el-button type="primary" @click="onImportSyj">计算</el-button>
          </el-button-group>
        </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getJdExpressList"
      />
    </template>
    <el-dialog title="计算" :visible.sync="dialogVisibleSyj" width="30%" v-dialogDrag>
      <span>
        <el-row>
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker style="width: 50%; float: left;" v-model="calUnderlyingDataTemplateDate" type="month" format="yyyyMM"
              value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
              <el-button  type="success" style="margin-left:  30px;"  @click="calUnderlyingDataTemplate">计算</el-button>
          </el-col>
        </el-row>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>

import {calcUnderlyingDataTemplate,getUnderlyingDataTemplateList,exportOperatingCommission} from '@/api/financial/yyfy'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import {platformlist} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'time',label:'年月', width:'120',sortable:'custom'},
      {istrue:true,prop:'platformName',label:'平台', width:'120',sortable:'custom'},
      {istrue:true,prop:'store',label:'店铺', width:'200',sortable:'custom'},
      {istrue:true,prop:'proCode',label:'ID', width:'120',sortable:'custom'},
      {istrue:true,prop:'operationsTeam',label:'运营小组', width:'120',sortable:'custom'},
      {istrue:true,prop:'grossProfit',label:'毛三', width:'120',sortable:'custom'},
      {istrue:true,prop:'netProfit',label:'净利', width:'120',sortable:'custom'},
      {istrue:true,prop:'grossProfitCommission',label:'毛三提成', width:'120',sortable:'custom', tipmesg: '毛利分成合计*毛三',},
      {istrue:true,prop:'netProfitCommission',label:'净利提成', width:'120',sortable:'custom', tipmesg: '净利分成合计*净利',},
      {istrue:true,prop:'accounting',label:'验算', width:'80',sortable:'custom', tipmesg: '提成合计-毛三提成-净利提成',},
      {istrue:true,prop:'totalCommission',label:'提成合计', width:'120',sortable:'custom', tipmesg: '该ID下每个人的毛利提成和净利提成的总和',},
     ];
     const  tableCols1=[
      {istrue:true,prop:'name',label:'姓名', width:'270'},
      {istrue:true,prop:'grossProfitShare',label:'毛利分成', width:'270', formatter: (row) => !row.grossProfitShare ? " " : row.grossProfitShare*100+ '%'},
      {istrue:true,prop:'netProfitShare',label:'净利分成', width:'270', formatter: (row) => !row.netProfitShare ? " " : row.netProfitShare*100+ '%'},
      {istrue:true,prop:'peoGrossProfitCommission',label:'毛利提成', width:'270', tipmesg: '毛利分成*毛三',},
      {istrue:true,prop:'peoNetProfitCommission',label:'净利提成', width:'270', tipmesg: '净利分成*净利',},
     ]
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
  data() {
    return {
      that:this,
      calUnderlyingDataTemplateDate:null,
      Filter: {
          platform:null,
          shopCode:null,
          UseDate:[],
        UseDstartAccountDateate:null,
        endAccountDate:null,
        ProCode:null,
        platform:null,
        OperationsTeam:null
      },
      shopList:[],
      userList:[],
      groupList:[],
      dahuixionglist: [],
      tableCols:tableCols,
      tableCols1:tableCols1,
      total: 0,
      summaryarry:{},
      pager:{OrderBy:"ProCode",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
      platform:0,
      yearMonth:"",
      platformlist:platformlist,
    };
  },
  async mounted() {
    this.onSearch();
  },
  methods: {
    async ExportOperatingCommission(){
                     var pager = this.$refs.pager.getPager();
                    const params = {
                         ...this.pager,
                        ...this.Filter,
                    };
                    var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
                    var res = await exportOperatingCommission(params);
                   loadingInstance.close();
                    if (!res?.data) {
                        this.$message({ message: "没有数据", type: "warning" });
                        return
                    }
                    const aLink = document.createElement("a");
                    let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '运营提成核算_' + new Date().toLocaleString() + '.xlsx')
                    aLink.click();
    },
   async calUnderlyingDataTemplate(){

    if(this.calUnderlyingDataTemplateDate==null||this.calUnderlyingDataTemplateDate=="")
    {
      this.$message({ message: "请先选择月份", type: "warning" });
      return false;
    }
    this.dialogVisibleSyj = false;
    this.$message({type: 'success',message: '计算中，请稍后....'});
      var  res= await calcUnderlyingDataTemplate({YearMonth:this.calUnderlyingDataTemplateDate})
      if (!res?.success)  return
      this.$message({type: 'success',message: '计算完成'});
    this.calUnderlyingDataTemplateDate=null
   },
  async onchangeplatform(val){
    if(val==null||val=='')
    {
      this.Filter.shopCode = "";
    }
    const res1 = await getshopList({platform:val,CurrentPage:1,PageSize:300});
    this.shopList=res1.data.list
  },
    setplatform(platform){
this.platform=platform;
},
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    onImportSyj(){
      this.dialogVisibleSyj = true
    },
    onRefresh(){
        this.onSearch()
    },
    onSearch(){
       this.$refs.pager.setPage(1);
       this.getJdExpressList();
    },
    async getJdExpressList(){
      this.Filter.proCode=null;
      this.Filter.startAccountDate=null;
         this.Filter.endAccountDate=null;
      if(this.Filter.UseDate){
        this.Filter.startAccountDate=this.Filter.UseDate[0];
         this.Filter.endAccountDate=this.Filter.UseDate[1];
       }
      const para = {...this.Filter};
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,

      };
      this.listLoading = true;
      const res = await getUnderlyingDataTemplateList(params);
      this.listLoading = false;
      this.total = res.data.total
      this.dahuixionglist = res.data.list;
      this.summaryarry=res.data.summary;
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
