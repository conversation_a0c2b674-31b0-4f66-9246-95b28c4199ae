<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent></el-form>
      <el-button-group>
        <el-button style="padding: 0;">
          <el-select filterable v-model="filter.version" placeholder="类型" style="width: 130px">
            <el-option label="工资月报" value="v1"></el-option>
            <el-option label="参考月报" value="v2"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin: 0;">
          <el-date-picker style="width: 120px" v-model="filter.yearMonth" type="month" format="yyyyMM"
            value-format="yyyyMM" placeholder="核算月份"></el-date-picker>
        </el-button>
        <el-button style="padding: 0;">
          <el-input v-model.trim="filter.shopCode" placeholder="请输入店铺编码" maxlength="50" clearable
            style="width:150px;" />
        </el-button>
        <el-button style="padding: 0;margin: 0;">
          <el-input v-model.trim="filter.distributorName" :maxlength="150" clearable placeholder="分销商名称" style="width:100px;"/>
        </el-button>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onExport">导出</el-button>
      </el-button-group>
    </template>
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      :id="'reportKWaiShop202409241159'" :tablekey="'reportKWaiShop202409241159'" :showheaderoverflow="false"
      :isSelectColumn="false" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry'
      @summaryClick='onsummaryClick' :tableData='financialreportlist' :tableCols='tableCols'
      :tableHandles='tableHandles' :loading="listLoading">
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>

    <vxe-modal title="明细" v-model="calcdetails.visible" width="80%" v-dialogDrag>
      <calcdetails ref="calcdetails" style="height:600px;"></calcdetails>
    </vxe-modal>

    <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
      <span>
        <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>
import { getAllList as getAllShopList, getDirectorGroupList } from '@/api/operatemanage/base/shop';
import { getFinancialReportList } from '@/api/financial/yyfy'
import { exportFinancialReport } from '@/api/monthbookkeeper/financialreport'
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import calcdetails from "@/views/bookkeeper/reportmonth/calcdetails";
import { Loading } from 'element-ui';
import buschar from '@/components/Bus/buschar'
import { getAnalysisCommonResponse } from '@/api/admin/common'
let loading;
const startLoading = () => {  // 使用Element loading-start 方法
  loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
  });
};
const tableCols = [
  { sortable: 'custom', width: '100', align: 'center', prop: 'distributorName', label: '分销商名称', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'shopCode', label: '店铺编号', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'order_count', label: '订单数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'revenue', label: '营业收入', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'revenue1', label: '营业收入1', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'return_amount', label: '退货金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'product_cost', label: '商品成本', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'actual_return_cost', label: '实退成本', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'drop_shipping_cost_difference', label: '代发成本差', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'purchase_shipping_fee', label: '采购运费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'gross_profit1', label: '毛一', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'gross_profit1_rate', label: '毛一利润率', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'express_fee', label: '快递费用', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'express_feeavg', label: '快递费分摊', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'express_feeradio', label: '快递费率', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'external_warehouse_order_count', label: '外仓订单量', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'external_warehouse_min_express_fee', label: '外仓最低快递费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'external_warehouse_express_fee_difference', label: '外仓快递费-实际快递费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'packageFee', label: '包装费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'packageFeeAuct', label: '真实包装费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'packageFeePredict', label: '预估包装费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'packageFeeShare', label: '分摊包装费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'product_shipping_fee', label: '产品运费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'operation_salary', label: '运营工资', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'customer_service_salary', label: '客服工资', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'store_expense', label: '店铺费用', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'accessories_cost', label: '辅料', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'gross_profit3', label: '毛三', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'outbound_costyg', label: '预估出仓成本', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'outbound_cost', label: '真实出仓成本', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'outbound_costtotal', label: '出仓成本', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'wareWages', label: '仓库薪资', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'wareWagesYunYing', label: '仓库薪资（运）', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'gross_profit4', label: '毛四', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'gross_profit4_rate', label: '毛四利润率', },
]
// const tableCols = [
//   { istrue: true, fixed: 'left', prop: 'yearMonth', label: '年月', sortable: 'custom', width: '65' },
//   { istrue: true, fixed: 'left', prop: 'proCode', fix: true, label: '商品ID', width: '120', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
//   { istrue: true, fixed: 'left', prop: 'groupId', label: '运营', sortable: 'custom', width: '70', formatter: (row) => row.groupName || ' ' },
//   {
//     istrue: true, prop: '', label: `账单`, merge: true, prop: 'mergeField',
//     cols: [
//       { istrue: true, prop: 'goodsCodes', label: '商品编码', sortable: 'custom', width: '120' },
//       { istrue: true, prop: 'proCode', label: '商品名称', sortable: 'custom', width: '150', formatter: (row) => row.proName || ' ' },
//       { istrue: true, prop: 'styleCode', label: '系列编码', width: '100', sortable: 'custom' },
//       { istrue: true, prop: 'shopCode', label: '店铺名称', sortable: 'custom', width: '150', formatter: (row) => row.shopName || ' ' },
//       { istrue: true, prop: 'orderCount', label: '订单数', sortable: 'custom', width: '70' },
//       { istrue: true, prop: 'count', label: 'ID数', sortable: 'custom', width: '70' },
//       { istrue: true, prop: 'amountSettlement', label: '结算收入', sortable: 'custom', width: '100' },
//       { istrue: true, prop: 'amountSettlement_1', label: '结算收入-1', sortable: 'custom', width: '95', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
//       { istrue: true, prop: 'amountSettlement_2', label: '2月之前月份收入', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
//       { istrue: true, prop: 'amountCrossMonthIn', label: '跨月收入', width: '100' },
//       { istrue: true, prop: 'amountOut', label: '退款', sortable: 'custom', width: '70', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
//       { istrue: true, prop: 'amountCrossMonthOut', label: '跨月退款', sortable: 'custom', width: '80' },
//       { istrue: true, prop: 'amountTaoKeNot', label: '淘客不计', sortable: 'custom', width: '80' },
//       { istrue: true, prop: 'amountShare', label: '参与公摊金额', sortable: 'custom', width: '110' },
//       { istrue: true, prop: 'amountCost', label: '结算成本', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
//       { istrue: true, prop: 'amountOutCost', label: '退款成本', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
//       { istrue: true, prop: 'amountEmptyId', label: '空白链接成本', sortable: 'custom', width: '80' },
//       { istrue: true, prop: 'agentCost', label: '代发成本差', sortable: 'custom', width: '80' },
//       { istrue: true, prop: 'amountReSendCost', label: '补发成本', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
//       { istrue: true, prop: 'amountExceptionCost', label: '异常成本', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
//       { istrue: true, prop: 'cbcamont', label: '护墙角定制差额', width: '80', sortable: 'custom' },
//       { istrue: true, prop: 'amontFreightfee', label: '采购成本差', width: '80', sortable: 'custom' },
//       { istrue: true, prop: 'grossProfit', label: '销售毛利', sortable: 'custom', width: '100' },
//       { istrue: true, prop: 'orderCount_FX', label: '分销订单数', sortable: 'custom', width: '80' },
//       { istrue: true, prop: 'count_FX', label: '分销ID数', sortable: 'custom', width: '80' },
//       { istrue: true, prop: 'amountSettlement_FX', label: '分销结算收入', sortable: 'custom', width: '80' },
//       { istrue: true, prop: 'amountCost_FX', label: '分销结算成本', sortable: 'custom', width: '80' },
//       { istrue: true, prop: 'dingZhiKuanTotalCost', label: '定制款成本', sortable: 'custom', width: '80' },
//       { istrue: true, prop: 'dingZhiKuanAvgCost', label: '定制款分摊成本', sortable: 'custom', width: '80' },
//       { istrue: true, prop: 'dingZhiKuanExceptionCost', label: '定制款异常成本', sortable: 'custom', width: '80' },
//     ]
//   },
//   {
//     istrue: true, prop: '', label: `账单费用`, merge: true, prop: 'mergeField1',
//     cols: [
//       { istrue: true, prop: 'priceBackCommissionFee', sortable: 'custom', label: '价保返佣', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
//       { istrue: true, prop: 'priceDeductFee', sortable: 'custom', label: '价保扣款', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
//       { istrue: true, prop: 'insuranceServiceFee', sortable: 'custom', label: '保险服务费', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
//       { istrue: true, prop: 'saleAfterCompensateFee', sortable: 'custom', label: '售后卖家赔付费', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
//       { istrue: true, prop: 'orderGiveBean', sortable: 'custom', label: '随单送的京豆', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
//       { istrue: true, prop: 'commissionFee', sortable: 'custom', label: '佣金', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
//       { istrue: true, prop: 'freightInsuranceServiceFee', sortable: 'custom', label: '运费保险服务费', type: 'click', width: '80', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
//       { istrue: true, prop: 'collectionDeliveryFee', sortable: 'custom', width: '80', label: '代收配送费', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
//       { istrue: true, prop: 'advertReducCommission', sortable: 'custom', width: '100', label: '广告联合活动降扣佣金', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
//       { istrue: true, prop: 'tradeServiceFee', sortable: 'custom', label: '交易服务费', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
//       { istrue: true, prop: 'sellerBackFreight', sortable: 'custom', label: '卖家返还运费', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
//       { istrue: true, prop: 'jingZhunTongCollection', sortable: 'custom', label: '京准通代收款', width: '80' },
//       { istrue: true, prop: 'avgFee', sortable: 'custom', label: '账单费用公摊', width: '80' },
//       { istrue: true, prop: 'dkTotalAmont', sortable: 'custom', label: '账单费用合计', width: '80' }
//     ]
//   },
//   {
//     istrue: true, prop: '', label: `订单费用`, merge: true, prop: 'mergeField2',
//     cols: [
//       { istrue: true, prop: 'freightFee', label: '快递费', sortable: 'custom', width: '80' },
//       { istrue: true, prop: 'withholdfee', label: '快递违规扣款', sortable: 'custom', width: '80' },
//       { istrue: true, prop: 'packageFee', label: '包装费', sortable: 'custom', width: '70' },
//       { istrue: true, prop: 'totalOrderCost', label: '订单费用合计', sortable: 'custom', width: '80' }
//     ]
//   },
//   {
//     istrue: true, prop: '', label: `京东仓储配送费`, merge: true, prop: 'mergeField3',
//     cols: [
//       { istrue: true, prop: 'collectionServiceFee', sortable: 'custom', label: '收派服务费', width: '80' },
//       { istrue: true, prop: 'storageServiceFee_Hc', sortable: 'custom', label: '仓储服务费-耗材', width: '80' },
//       { istrue: true, prop: 'shippingFee_Zxj', sortable: 'custom', label: '运输转运费-中小件', width: '80' },
//       { istrue: true, prop: 'storageServiceFee_Ty', sortable: 'custom', label: '仓储服务费-通用', width: '80', },
//       { istrue: true, prop: 'cangChuTotalAmont', sortable: 'custom', label: '京东仓储配送费合计', width: '80', },
//     ]
//   },
//   {
//     istrue: true, prop: '', label: `运营费用`, merge: true, prop: 'mergeField4',
//     cols: [
//       { istrue: true, prop: 'jingdongzhanwei', label: '京东展位', sortable: 'custom', width: '90' },
//       { istrue: true, prop: 'jingdongzhitou', label: '京东直投', sortable: 'custom', width: '90' },
//       { istrue: true, prop: 'jingdongkuaiche', label: '京东快车', sortable: 'custom', width: '90' },
//       { istrue: true, prop: 'gouwuchudian', label: '购物触点', sortable: 'custom', width: '90' },
//       { istrue: true, prop: 'jingsutui', label: '京速推', sortable: 'custom', width: '90' },
//       { istrue: true, prop: 'tesudanfeiyong', label: '特殊单费用', sortable: 'custom', width: '90' },
//       { istrue: true, prop: 'tesudanyongjin', label: '特殊单佣金', sortable: 'custom', width: '90' },
//       { istrue: true, prop: 'tesudanben', label: '特殊单成本', sortable: 'custom', width: '90' },
//       { istrue: true, prop: 'advFee', sortable: 'custom', label: '运营费用合计', width: '90' },
//     ]
//   },
//   {
//     istrue: true, prop: '', label: `产品费用`, merge: true, prop: 'mergeField5',
//     cols: [
//       { istrue: true, prop: 'amontPick', sortable: 'custom', label: '产品运费', width: '90' },
//       { istrue: true, prop: 'amontSampleBX', sortable: 'custom', label: '样品费', width: '90' },
//       { istrue: true, prop: 'amontSampleGrop', sortable: 'custom', label: '运营拿样', width: '90' },
//       { istrue: true, prop: 'amontSampleMG', sortable: 'custom', label: '美工拿样', width: '90' },
//       { istrue: true, prop: 'amontShoot', sortable: 'custom', label: '美工拍摄费用', width: '90' },
//       { istrue: true, prop: 'dianpuAmount', sortable: 'custom', label: '店铺费用', width: '90' },
//       { istrue: true, prop: 'cuishou', sortable: 'custom', label: '催收费用', width: '90' },
//       { istrue: true, prop: 'laxinAmount', sortable: 'custom', label: '拉新', width: '90' },
//       { istrue: true, prop: 'totalProductCost', sortable: 'custom', label: '产品费用合计', width: '90' },
//     ]
//   },
//   {
//     istrue: true, prop: '', label: `工资`, merge: true, prop: 'mergeField6',
//     cols: [
//       { istrue: true, prop: 'amontCommissionMG', sortable: 'custom', label: '美工提成', width: '90' },
//       { istrue: true, prop: 'amontCommissionCG', sortable: 'custom', label: '采购提成', width: '90' },
//       { istrue: true, prop: 'amontWagesGroup', label: '小组运营', sortable: 'custom', width: '90' },
//       { istrue: true, prop: 'amontMachineGZ', label: '加工工资', sortable: 'custom', width: '90' },
//       { istrue: true, prop: 'amontCommissionXMT', label: '新媒体提成', sortable: 'custom', width: '90' },
//       { istrue: true, prop: 'totalWagesCost', label: '工资合计', sortable: 'custom', width: '80' }
//     ]
//   },
//   { istrue: true, prop: 'amontStoreLossfee', label: '仓储损耗', sortable: 'custom', width: '90', tipmesg: '可在"财务管理/月报/产品费用"中查看明细' },
//   { istrue: true, prop: 'amontOffLinefee', label: '运营下架', sortable: 'custom', width: '90', tipmesg: '可在"财务管理/月报/产品费用"中查看明细' },
//   { istrue: true, prop: 'saleProfit', label: '产品利润', sortable: 'custom', width: '120' }
// ];
export default {
  name: "reportFenXiao",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, calcdetails, buschar },
  data() {
    return {
      that: this,
      filter: {
        proCode: null,
        platform: 11,
        yearMonth: null,
        shopCode: null,
        groupId: null,
        productName: null,
        version: "v1"
      },
      userList: [],
      grouplist: [],
      financialreportlist: [],
      tableCols: tableCols,
      tableHandles: [],
      total: 0,
      // summaryarry:{count_sum:10},
      pager: { OrderBy: "", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      summaryarry: {},
      selids: [],
      dialogVisibleSyj: false,
      calcdetails: { visible: false },
      fileList: [],
      analysisFilter: {
        searchName: "View_FinancialMonthReport_JD",
        extype: 5,
        selectColumn: "Count",
        filterTime: "YearMonth",
        isYearMonthDay: false,
        filter: null,
        columnList: [{ columnNameCN: '订单数', columnNameEN: 'Count' }]
      },
      buscharDialog: { visible: false, title: "", data: [] },
    };
  },
  async mounted() {
    //await this.onSearch()
    //await this.getShopList();
  },
  methods: {
    async getShopList() {
      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      await this.onSearch();
    },
    async onSearch() {
      this.$refs.pager.setPage(1);
      await this.getList().then(res => { });
      // loading.close();
    },
    async getList() {
      var that = this;
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter, };
      // this.listLoading = true;
      startLoading();
      const res = await getFinancialReportList(params).then(res => {
        loading.close();
        that.total = res.data?.total
        that.financialreportlist = res.data?.list;
        that.summaryarry = res.data?.summary;
      });
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    async onExport() {
      if (!this.filter.yearMonth) {
        this.$message({ message: "请先选择月份！", type: "warning" });
        return;
      }
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };
      var res = await exportFinancialReport(params);
      if (!res?.data) {
        this.$message({ message: "没有数据", type: "warning" });
        return
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '分销月报数据' + new Date().toLocaleString() + '_.xlsx')
      aLink.click()
    },
    async showcalcdetails(row, column) {
      this.calcdetails.visible = true;
      console.log('this.$refs', this.$refs)
      console.log('this.$refs.calcdetails;', this.$refs.calcdetails)
      var calc = this.$refs.calcdetails;
      console.log('calcdetails1', calc)
      var that = this;
      this.$nextTick(async () => {
        console.log('calcdetails2', calc)
        await that.$refs.calcdetails.onSearch(column, this.filter.version, row.shopCode, row.yearMonth, row.proCode)
      });
    },
    async onsummaryClick(property) {
      let that = this;
      this.analysisFilter.filter = {
        proCode: [that.filter.proCode, 0],
        //platform: [that.filter.platform, 0],
        yearMonth: [that.filter.yearMonth, 0],
        shopCode: [that.filter.shopCode, 0],
        groupId: [that.filter.groupId, 0],
        productName: [that.filter.productName, 5],
        version: [that.filter.version, 0],
      };
      this.analysisFilter.selectColumn = property;
      var cn = "金额";
      if (property == "orderCount" || property == "count") {
        cn = "数量";
      }
      this.analysisFilter.columnList = [{ columnNameCN: cn, columnNameEN: property }];
      const res = await getAnalysisCommonResponse(this.analysisFilter).then(res => {
        that.buscharDialog.visible = true;
        that.buscharDialog.data = res.data
        that.buscharDialog.title = res.data.legend[0]
      });
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
