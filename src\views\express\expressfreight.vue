<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="运单号:">
                    <el-input v-model.trim="filter.id" style="width: 150px" />
                </el-form-item>
                <el-form-item label="批次号:">
                    <el-input v-model="filter.batchNumber" style="width: 150px" :maxlength="19" oninput="if(value){value=value.replace(/[^\d]/g,'')} if(value<0){value=''}" />
                </el-form-item>
                <el-form-item label="发货仓库:">
                    <el-select v-model="filter.warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 120px">
                        <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="发往省份:">
                    <el-input v-model.trim="filter.Province" style="width: 100px" />
                </el-form-item>
                <el-form-item label="快递公司:">
                    <el-select v-model="filter.companyId" @change="getprosimstatelist(2)" placeholder="请选择快递公司" style="width: 130px">
                        <el-option label="所有" value="" />
                        <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="快递站点:">
                    <el-select v-model="filter.prosimstate" placeholder="请选择快递站点" style="width: 130px">
                        <!-- <el-option label="暂无站点" value="" /> -->
                        <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="面单费:">
                    <el-input type="number" placeholder="面单费" v-model="filter.FaceFee" style="width: 100px" oninput="if(value>*********){value=*********} else if(value<-*********){value=-*********}" />
                </el-form-item>
                <el-form-item label="差额范围:">
                    <el-row style="width: 200px">
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                            <el-input type="number" placeholder="最小差额" v-model="filter.minDifference" oninput="if(value>*********){value=*********} else if(value<-*********){value=-*********}" />
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                            <el-input type="number" placeholder="最大差额" v-model="filter.maxDifference" oninput="if(value>*********){value=*********} else if(value<-*********){value=-*********}" />
                        </el-col>
                    </el-row>
                </el-form-item>
                <el-form-item label="重量范围(kg):">
                    <el-row style="width: 200px">
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                            <el-input type="number" placeholder="最小重量" v-model="filter.minWeight" oninput="if(value>*********){value=*********} else if(value<-*********){value=-*********}" />
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                            <el-input type="number" placeholder="最大重量" v-model="filter.maxWeight" oninput="if(value>*********){value=*********} else if(value<-*********){value=-*********}" />
                        </el-col>
                    </el-row>
                </el-form-item>
                <el-form-item label="揽收时间:">
                    <el-date-picker style="width:280px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                </el-form-item>
                <el-form-item label="导入时间:">
                    <el-date-picker style="width:290px" v-model="filter.timerangeimport" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                </el-form-item>
                <el-form-item label="结算月份:">
                    <el-date-picker style="width: 110px" v-model="filter.settMonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch(true)">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-dropdown @command="downCommand">
                        <span class="el-button el-button--primary el-button--small"> 下载运费模板<i class="el-icon-arrow-down el-icon--right"></i>
                        </span>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="圆通(返0.1)">圆通(返0.1)快递费</el-dropdown-item>
                                <el-dropdown-item command="圆通(不返0.1)">圆通(不返0.1)快递费</el-dropdown-item>
                                <el-dropdown-item command="申通">申通快递费</el-dropdown-item>
                                <el-dropdown-item command="顺丰">顺丰快递费</el-dropdown-item>
                                <el-dropdown-item command="百世">百世快递费</el-dropdown-item>
                                <el-dropdown-item command="百世">百世汇通快递费</el-dropdown-item>
                                <el-dropdown-item command="德邦">德邦快递费</el-dropdown-item>
                                <el-dropdown-item command="邮政">邮政快递费</el-dropdown-item>
                                <el-dropdown-item command="中通">中通快递费</el-dropdown-item>
                                <el-dropdown-item command="优速">优速快递费</el-dropdown-item>
                                <el-dropdown-item command="韵达">韵达快递费</el-dropdown-item>
                                <el-dropdown-item command="京广">京广快递费</el-dropdown-item>
                                <el-dropdown-item command="速尔">速尔快递费</el-dropdown-item>
                                <el-dropdown-item command="极兔">极兔快递费</el-dropdown-item>
                                <el-dropdown-item command="安徽邮政">安徽邮政快递费</el-dropdown-item>
                                <el-dropdown-item command="安徽中通">安徽中通快递费</el-dropdown-item>
                                <el-dropdown-item command="上海韵达">上海韵达快递费</el-dropdown-item>
                                <el-dropdown-item command="义乌万通">义乌万通快递费</el-dropdown-item>
                                <el-dropdown-item command="义乌廿三里圆通">义乌廿三里圆通快递费</el-dropdown-item>
                                <el-dropdown-item command="宅急送">宅急送快递费</el-dropdown-item>
                                <el-dropdown-item command="月账单">月账单模板</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </el-form-item>
                <el-row>
                    <el-alert title="温馨提示:账单费=快递公司结算费、实收费用=账单费+面单费、校验费用=系统计算出的快递费、差额=校验费用-实收费用.针对没有账单费的快递后期可以导入月账单来修正" type="warning" show-icon :closable="false">
                    </el-alert>
                </el-row>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='ExpressList' :summaryarry='summaryarry' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" :showsummary='true' @summaryClick='onsummaryClick'>
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col,index) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="index"></el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getExpressList(false)" />
        </template>
        <el-dialog :visible.sync="dialogVisible" width="40%" :title="importtype==0?'导入运费' :importtype==1?'导入月账单' :importtype==2?'重新核算运费':'核算其他收支'" v-dialogDrag>
            <span>
                <el-row>
                    <el-alert v-if="importtype==0" type="warning" show-icon :closable="false" title="温馨提示:导入运费前请确认是否导入了最新规则并确认开启！导入邮政重量单位是‘克’">
                    </el-alert>
                    <el-alert v-else-if="importtype==1" type="warning" show-icon :closable="false" title="温馨提示:导入月账单前请确认是否导入了相对应的运费！不支持导入不含面单的月账单">
                    </el-alert>
                    <el-alert v-else-if="importtype==2" type="warning" show-icon :closable="false" title="温馨提示:重新核算前请确认导入了相对应的规则并启用！">
                    </el-alert>
                    <el-alert v-else-if="importtype==3" type="warning" show-icon :closable="false" title="温馨提示:重新核算前请确认导入了相对应的其他收支！">
                    </el-alert>
                </el-row>
                <br>
                <el-row>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="8">
                        <el-select v-model="importFilte.companyid" placeholder="请选择快递公司" @change="selectchange(1)" style="width: 100%">
                            <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
                        </el-select>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="8">
                        <el-select v-model="importFilte.prosimstate" placeholder="请选择快递公司站点" @change="selectchange(3)" style="width: 100%">
                            <el-option label="暂无站点" value="28" />
                            <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
                        </el-select>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="8">
                        <el-select v-model="importFilte.warehouse" clearable filterable placeholder="请选择发货仓库" @change="selectchange(2)" style="width: 100%">
                            <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                        <el-date-picker v-model="importFilte.yearmonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份" style="width: 100%" @change="datechange"></el-date-picker>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" v-if="importtype==0||importtype==2">
                        <el-select v-model="importFilte.rulebatchnumber" placeholder="请选择规则批次号" style="width: 100%">
                            <el-option label="请选择规则批次号" value></el-option>
                            <el-option v-for="item in rulebatchnumberlist" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-col>
                </el-row>
                <el-row v-if="importtype==2||importtype==3">
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                        <el-date-picker v-model="importFilte.start" type="datetime" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" placeholder="开始时间" style="width: 100%"></el-date-picker>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                        <el-date-picker v-model="importFilte.end" type="datetime" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" placeholder="结束时间" style="width: 100%"></el-date-picker>
                    </el-col>
                </el-row>
                <el-row v-if="importtype==0||importtype==1">
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action accept=".xlsx" :http-request="uploadFile" :file-list="fileList" :on-change="uploadChange" :on-remove="uploadRemove" :data="fileparm">
                            <template #trigger>
                                <el-button size="small" type="primary">选取运费文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
                <el-row v-else-if="importtype==2||importtype==3">
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="examexpressfright">重新核算</my-confirm-button>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog title="批量删除" :visible.sync="dialogdeletebatchNumberVisible" width="500px" v-dialogDrag>
            <el-row>
                <el-col :xs="24" :sm="10" :md="10" :lg="10" :xl="10">
                    <el-select v-model="deletefilter.companyId" placeholder="请选择快递公司" style="width: 100%">
                        <el-option label="所有" value="" />
                        <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-col>
                <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
                    <el-input placeholder="请输入批次号" v-model="deletefilter.batchNumber" style="width: 100%" :maxlength="19" oninput="if(value){value=value.replace(/[^\d]/g,'')} if(value<0){value=''}"></el-input>
                </el-col>
                <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
                    <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker>
                </el-col>
            </el-row>
            <el-row>
                <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
                    <el-button type="primary" @click="onDeletebatchRule">删除</el-button>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogdeletebatchNumberVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <!-- 系列编码趋势图 -->
        <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
    import cesTable from "@/components/Table/table.vue";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    import {
        getExpressComanyAll, getPageExpressFreightList,getExpressFreightSummaryAsync, importExpressFreight, exportExpressFreight, importExpressFreightMonth, examExpressFreightAgain, examExpressFreightExinAgain,
        getExpressRuleBatchNumber, batchDeleteExpress, getExpressComanyStationName
    } from "@/api/express/express";
    import { formatTime, formatPass, formatRule, formatWarehouse, warehouselist } from "@/utils/tools";
    import buschar from '@/components/Bus/buschar'
    import { getAnalysisCommonResponse } from '@/api/admin/common'
    import { pickerOptions } from '@/utils/tools'
    const tableCols = [
        { istrue: true, prop: 'hasError', label: '校验', width: '60', sortable: 'custom', formatter: (row) => formatPass(row.hasError) },
        { istrue: true, prop: 'receiveTime', label: '年月', width: '60', sortable: 'custom', formatter: (row) => formatTime(row.receiveTime, 'YYYYMM') },
        { istrue: true, prop: 'id', label: '运单号码', width: '110', sortable: 'custom' },
        { istrue: true, prop: 'expressCompanyName', label: '快递公司', width: '75' },
        { istrue: true, prop: 'prosimStateName', label: '快递站点', width: '75' },
        { istrue: true, prop: 'warehouse', label: '发货仓', width: '75', sortable: 'custom', formatter: (row) => formatWarehouse(row.warehouse) },
        { istrue: true, prop: 'freightRuleType', label: '适用规则', width: '80', formatter: (row) => formatRule(row.freightRuleType) },
        { istrue: true, summaryEvent: true, prop: 'weight', label: '重量(KG)', width: '85', sortable: 'custom' },
        { istrue: true, summaryEvent: true, prop: 'totalFee', label: '账单费', width: '75', sortable: 'custom', summary: true },
        { istrue: true, summaryEvent: true, prop: 'apAmount', label: '其他收支合计', width: '110', sortable: 'custom', summary: true },
        { istrue: true, summaryEvent: true, prop: 'otherFee', label: '其他费用', width: '90', sortable: 'custom' },
        { istrue: true, summaryEvent: true, prop: 'violationAmount', label: '罚款', width: '65', sortable: 'custom' },
        { istrue: true, summaryEvent: true, prop: 'claimAmount', label: '理赔', width: '65', sortable: 'custom' },
        { istrue: true, summaryEvent: true, prop: 'sjTotalFee', label: '账单合计', width: '90', sortable: 'custom' },
        { istrue: true, summaryEvent: true, prop: 'overWeightFee', label: '续重费', width: '75', sortable: 'custom' },
        { istrue: true, summaryEvent: true, prop: 'jsTotalFee', label: '核算运费合计', width: '110', sortable: 'custom' },
        { istrue: true, summaryEvent: true, prop: 'difference', label: '差额', width: '90', sortable: 'custom' },
        { istrue: true, summaryEvent: true, prop: 'faceSheetFee', label: '面单费', width: '80', sortable: 'custom' },
        { istrue: true, summaryEvent: true, prop: 'shareOrderTotalFee', label: '订单分摊', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'volume', label: '体积', width: '55' },
        { istrue: true, summaryEvent: true, prop: 'volumeWeight', label: '体积重(kg)', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'province', label: '发往省份', width: '90', sortable: 'custom' },
        { istrue: true, prop: 'count', label: '件数', width: '60', sortable: 'custom' },
        { istrue: true, prop: 'receiveTime', label: '揽收时间', width: '145', sortable: 'custom' },
        { istrue: true, prop: 'createdTime', label: '创建时间', width: '145', sortable: 'custom' },
        { istrue: true, prop: 'settMonth', label: '结算月份', width: '80' },
        { istrue: true, prop: 'consignee', label: '收货人', width: '150' },
        { istrue: true, prop: 'reMark', label: '备注', width: '150' },
        { istrue: true, prop: 'freightErrorStr', label: '校验不通过原因', width: '150' },
        { istrue: true, prop: 'createdUserName', label: '创建者' },
        { istrue: true, prop: 'batchNumber', label: '批次号', width: '180' },
        { istrue: true, prop: 'ruleBatchNumber', label: '规则批次号', width: '180' },
    ];
    const tableHandles1 = [
        { label: "导出", handle: (that) => that.onExport() },
        { label: '导入运费', handle: (that) => that.startImport() },
        { label: '导入运费月账单', handle: (that) => that.startImportMonth() },
        { label: '重新核算运费', handle: (that) => that.startExamExpressFreight() },
        { label: '核算其他收支', handle: (that) => that.startExamExpressFreightExin() },
        { label: '批量删除', handle: (that) => that.startDelete() },
        //{label:'查看重复订单', handle:(that)=>that.showRepeat()}
    ];
    export default {
        name: "Users",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, buschar },
        data () {
            return {
                timeRanges: [],
                that: this,
                filter: {
                    startCreatedTime: "",
                    endCreatedTime: "",
                    warehouse: null,
                    prosimstate: null,
                    province: null,
                    enabled: null,
                    timerange: "",
                    timerangeimport: "",
                    settMonth: null,
                    batchNumber: null
                },
                warehouselist: warehouselist,
                deletefilter: { companyId: null, batchNumber: null,startTime:null,endTime:null },
                pickerOptions,
                ExpressList: [],
                summaryarry: {},
                total: 0,
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                userNameReadonly: true,
                editForm: {},
                addFormVisible: false, // 新增界面是否显示
                addLoading: false,
                addFormRules: {
                    userName: [
                        { required: true, message: "请输入用户名", trigger: "blur" },
                    ],
                    password: [{ required: true, message: "请输入密码", trigger: "blur" }],
                },
                //新增界面数据
                addForm: {},
                deleteLoading: false,
                selectedexpresscompanyid: "",
                expresscompanylist: [],
                rulebatchnumberlist: [],
                prosimstatelist: [],//快递站点
                dialogVisible: false,
                fileList: [],
                fileparm: {},
                importFilte: { companyid: null, prosimstate: null, warehouse: null, yearmonth: null, rulebatchnumber: null, start: "", end: "" },
                batchNumber: "",
                pager: { OrderBy: "id", IsAsc: false },
                tableCols: tableCols,
                tableHandles: tableHandles1,
                importtype: 0,//0:快递账单 1:月账单
                dialogdeletebatchNumberVisible: false,
                repeatVisible: false,
                uploadLoading: false,
                analysisFilter: {
                    searchName: "ExpressFreight",
                    isYearMonthDay: true,
                    isTimeFormat: true,
                    extype: 2,
                    selectColumn: "",
                    filterTime: "receiveTime",
                    filter: null,
                    columnList: [],
                },
                buscharDialog: { visible: false, title: "", data: [] },
            };
        },
        async mounted () {
            await this.init();
            await this.init2();
            //await this.onSearch();
            await this.getExpressComanyList();
        },
        methods: {
            async changeTime(e) {
                this.deletefilter.startTime = e ? e[0] : null
                this.deletefilter.endTime = e ? e[1] : null
            },
            datetostr (date) {
                var year = date.getFullYear();
                var month = ("0" + (date.getMonth() + 1)).slice(-2);
                var day = ("0" + date.getDate()).slice(-2);
                var hours = ("0" + date.getHours()).slice(-2);
                var minutes = ("0" + date.getMinutes()).slice(-2);
                var seconds = ("0" + date.getSeconds()).slice(-2);
                var time = year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds;
                return time;
            },
            datetostr2(date) {
                var y = date.getFullYear();
                var m = ("0" + (date.getMonth() + 1)).slice(-2);
                var d = ("0" + date.getDate()).slice(-2);
                var hours = ("00").slice(-2);
                var minutes = ("00").slice(-2);
                var seconds = ("00" ).slice(-2);
                return y + "-" + m + "-" + d+ " " + hours + ":" + minutes+ ":" + seconds;
             },
            async init () {
                var date1 = new Date(); date1.setDate(date1.getDate() - 3);
                var date2 = new Date();
                this.filter.timerangeimport = [];
                this.filter.timerangeimport[0] = this.datetostr(date1);
                this.filter.timerangeimport[1] = this.datetostr(date2);
            },
            async init2 () {
                var date1 = new Date(); date1.setDate(date1.getDate() - 14);
                var date2 = new Date();
                this.filter.timerange = [];
                this.filter.timerange[0] = this.datetostr2(date1);
                this.filter.timerange[1] = this.datetostr2(date2);
            },
            startImport () {
                this.importtype = 0;
                this.dialogVisible = true;
            },
            startImportMonth () {
                this.importtype = 1;
                this.dialogVisible = true;
            },
            startExamExpressFreight () {
                this.importtype = 2;
                this.dialogVisible = true;
            },
            startExamExpressFreightExin () {
                this.importtype = 3;
                this.dialogVisible = true;
            },
            cancelImport () {
                this.dialogVisible = false;
            },
            beforeRemove () {
                return false;
            },
            async uploadChange (file, fileList) {
                if (fileList.length == 2) {
                    fileList.splice(1, 1);
                    this.$message({ message: "只允许单文件导入", type: "warning" });
                    return false;
                }
                this.fileList.push(file);
            },
            submitUpload () {
                if (!this.importFilte.companyid) {
                    this.$message({ message: "请选择快递公司！", type: "warning", });
                    return;
                }
                if (!this.importFilte.prosimstate) {
                    this.$message({ message: "请选择快递公司站点！", type: "warning", });
                    return;
                }
                else if (this.importFilte.warehouse == null) {
                    this.$message({ message: "请选择发货仓！", type: "warning", });
                    return;
                }
                else if (!this.importFilte.yearmonth) {
                    this.$message({ message: "请选择年月！", type: "warning", });
                    return;
                }
                else if (!this.importFilte.rulebatchnumber && this.importtype == 0) {
                    if (this.importFilte.companyid != 12) {
                        this.$message({ message: "请选择规则批次号！", type: "warning", });
                        return;
                    }
                    else
                        this.importFilte.rulebatchnumber = 0;
                }
                if (this.fileList.length == 0) {
                    this.$message({ message: "请先上传文件", type: "warning" });
                    return;
                }
                this.uploadLoading = true;
                this.$refs.upload.submit();
            },
            async uploadFile (item) {
                const form = new FormData();
                form.append("companyid", this.importFilte.companyid);
                form.append("prosimstate", this.importFilte.prosimstate);
                form.append("warehouse", this.importFilte.warehouse);
                form.append("rulebatchnumber", this.importFilte.rulebatchnumber);
                form.append("yearmonth", this.importFilte.yearmonth);
                form.append("upfile", this.fileList[0].raw);
                var res = {};
                if (this.importtype == 0) {
                    res = await importExpressFreight(form);
                }
                else if (this.importtype == 1) {
                    res = await importExpressFreightMonth(form);
                }
                if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
                else this.$message({ message: res.msg, type: "warning" });
                this.$refs.upload.clearFiles()
                this.uploadLoading = false;
                this.dialogVisible = false;
                this.fileList = [];
            },
            uploadRemove (file, fileList) {
                this.fileList.splice(0, 1);
            },
            async selectchange (val) {
                if (val == 1) {
                    this.importFilte.warehouse = null;
                    this.importFilte.yearmonth = null;
                    this.importFilte.rulebatchnumber = null;
                    this.importFilte.prosimstate = null;
                    await this.getprosimstatelist(1);
                }
                else if (val == 2) {
                    this.importFilte.yearmonth = null;
                    this.importFilte.rulebatchnumber = null;
                }
                else if (val == 3) {
                    this.importFilte.warehouse = null;
                    this.importFilte.yearmonth = null;
                    this.importFilte.rulebatchnumber = null;
                }
            },
            async datechange (value) {
                if (!this.importFilte.companyid) {
                    this.$message({ message: "请选择快递公司！", type: "warning", });
                    this.importFilte.yearmonth = null;
                    this.importFilte.rulebatchnumber = null;
                    return;
                }
                else if (this.importFilte.warehouse == null) {
                    this.importFilte.yearmonth = null;
                    this.importFilte.rulebatchnumber = null;
                    this.$message({ message: "请选择发货仓！", type: "warning", });
                    return;
                }
                this.importFilte.rulebatchnumber = null
                var res = await getExpressRuleBatchNumber(this.importFilte);
                this.rulebatchnumberlist = res.data;
            },
            async examexpressfright () {
                if (!this.importFilte.companyid) {
                    this.$message({ message: "请选择快递公司！", type: "warning", });
                    return;
                }
                else if (this.importFilte.warehouse == null) {
                    this.$message({ message: "请选择发货仓！", type: "warning", });
                    return;
                }
                else if (!this.importFilte.yearmonth) {
                    this.$message({ message: "请选择月份！", type: "warning", });
                    return;
                }
                else if (!this.importFilte.rulebatchnumber && this.importtype == 2) {
                    this.$message({ message: "请选择规则批次号！", type: "warning", });
                    return;
                }
                const form = new FormData();
                form.append("companyid", this.importFilte.companyid);
                form.append("prosimstate", this.importFilte.prosimstate);
                form.append("warehouse", this.importFilte.warehouse);
                form.append("yearmonth", this.importFilte.yearmonth);
                form.append("rulebatchnumber", this.importFilte.rulebatchnumber);
                form.append("start", this.importFilte.start);
                form.append("end", this.importFilte.end);
                let res;
                if (this.importtype == 2) {
                    res = await examExpressFreightAgain(form);
                }
                else if (this.importtype == 3) {
                    res = await examExpressFreightExinAgain(form);
                }
                if (res.data)
                    this.$message({ message: res.data, type: "success", });
            },
            sortchange (column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch(false);
            },
            async getExpressComanyList () {
                const res = await getExpressComanyAll({});
                if (!res?.success) {
                    return;
                }
                const data = res.data;
                this.expresscompanylist = data;
            },
            //获取状态信息
            async getprosimstatelist (val) {
                var id;
                if (val == 1)
                    id = this.importFilte.companyid
                else if (val == 2) {
                    id = this.filter.companyId
                    this.filter.prosimstate = null
                }

                var res = await getExpressComanyStationName({ id: id });
                if (res?.code) {
                    // this.prosimstatelist = res.data.map(function (item) {
                    // var ob = new Object();
                    // ob.state = item;
                    // ob.isshow = false;
                    // ob.selectedicon = "";
                    // ob.selecttype = "fail";
                    // return ob;
                    // })
                    this.prosimstatelist = res.data
                }
            },
            async onExport () {
                const params = { ...this.filter };
                if (this.filter.timerangeimport) {
                    params.startCreatedTime = this.filter.timerangeimport[0];
                    params.endCreatedTime = this.filter.timerangeimport[1];
                }
                if (this.filter.timerange) {
                    params.startReciveTime = this.filter.timerange[0];
                    params.endReciveTime = this.filter.timerange[1];
                }
                var hasparm = false;
                for (let key of Object.keys(params)) {
                    if (params[key])
                        hasparm = true;
                }
                if (!hasparm) {
                    this.$message({ message: "请选择条件后导出！", type: "warning", });
                    return;
                }
                var res = await exportExpressFreight(params);
                if (!res?.data) return
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '快递费_' + new Date().toLocaleString() + '.xlsx')
                aLink.click()
            },
            // 查询
            onSearch (isSearchSummary) {
                this.$refs.pager.setPage(1);
                this.getExpressList(isSearchSummary);
            },
            async loadSummary(params){
                this.summaryarry = {};
                const res = await getExpressFreightSummaryAsync(params); 
                if (!res?.success) return; 
                this.summaryarry = res.data;
            },
            async getExpressList (isSearchSummary) {
                var pager = this.$refs.pager.getPager();
                const params = { ...pager, ...this.pager, ...this.filter };
                if (this.filter.timerangeimport) {
                    params.startCreatedTime = this.filter.timerangeimport[0];
                    params.endCreatedTime = this.filter.timerangeimport[1];
                }
                if (this.filter.timerange) {
                    params.startReciveTime = this.filter.timerange[0];
                    params.endReciveTime = this.filter.timerange[1];
                }
                this.listLoading = true;
                const res = await getPageExpressFreightList(params);
                this.listLoading = false;
                if (!res?.success) return;
                this.total = res.data.total;
                const data = res.data.list;
                if (isSearchSummary) { 
                    //异步加载
                    this.loadSummary(params); 
                }
                data.forEach((d) => {
                    d._loading = false;
                    d.detailcols = [];
                    d.detaildata = [];
                    if (d.detail) {
                        var i = 0;
                        d.detail.forEach((da) => {
                            var dlist = JSON.parse(da.extent);
                            var row = {};
                            dlist.forEach((item) => {
                                if (i == 0)
                                    d.detailcols.push({ prop: item.name, label: item.lable });
                                row[item.name] = item.value;
                            });
                            d.detaildata.push(row);
                            i++;
                        });
                    }
                });
                this.ExpressList = data;
            },
            async downCommand (command) {
                var alink = document.createElement("a");
                alink.href = `../static/excel/financial/${command}快递费导入模板.xlsx`;
                alink.click();
            },
            async onDelete (row) {
                await this.$confirm("确定删除吗?, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(async () => {
                        const para = { id: row.id };
                        const res = await deleteExpressRule(row.id);
                        if (!res?.success) {
                            return;
                        }
                        this.$message({ message: this.$t("admin.deleteOk"), type: "success", });
                        this.getExpressList(true);
                    })
                    .catch(() => {
                        this.$message({ type: "info", message: "已取消删除", });
                    });
            },
            async startDelete () {
                this.dialogdeletebatchNumberVisible = true;
                this.deletefilter = { companyId: null, batchNumber: null };
            },
            async onDeletebatchRule () {
                if (!this.deletefilter.batchNumber) {
                    this.$message({ message: "请输入批次号！", type: "warning" });
                    return;
                }
                else if (!this.deletefilter.companyId) {
                    this.$message({ message: "请选择快递公司！", type: "warning" });
                    return;
                }else if (this.timeRanges.length == 0) {
                    this.$message({ message: "请选择时间范围！", type: "warning" });
                    return;
                }
                const res = await batchDeleteExpress(this.deletefilter);
                this.dialogdeletebatchNumberVisible = false;
                if (!res?.success) return;
                this.$message({ message: "已批量删除", type: "success" });
                this.getExpressList(true);
            },
            onSelsChange (sels) {
                this.sels = sels;
            },
            async onsummaryClick (property) {
                this.analysisFilter.columnList = [];
                this.analysisFilter.filter = null;
                let that = this;
                let summaryEventList = this.tableCols.filter(f => f.summaryEvent);
                summaryEventList.forEach(element => {
                    this.analysisFilter.columnList.push({ columnNameCN: element.label, columnNameEN: element.prop });
                });


                this.analysisFilter.filter = {
                    billnumber: [this.filter.Billnumber, 0],
                    faceFee: [this.filter.faceFee, 0],
                    settMonth: [this.filter.settMonth, 0],
                    difference: [this.filter.minDifference, this.filter.maxDifference, 99],
                    weight: [this.filter.minWeight, this.filter.maxWeight, 99],
                    warehouse: [this.filter.warehouse, 0],
                    expressCompanyId: [this.filter.companyId, 0],
                    prosimState: [this.filter.prosimState, 0],
                    batchNumber: [this.filter.batchNumber, 0],
                    createdTime: [this.filter.startCreatedTime, this.filter.endCreatedTime, 3],
                    province: [this.filter.Province, 5],
                }
                if (this.filter.timerange) {
                    this.analysisFilter.filter.receiveTime = [this.filter.timerange[0], this.filter.timerange[1], 0];
                }
                if (this.filter.timerangeimport) {
                    this.analysisFilter.filter.createdTime = [this.filter.timerangeimport[0], this.filter.timerangeimport[1], 0];
                }
                this.analysisFilter.selectColumn = property;
                const res = await getAnalysisCommonResponse(that.analysisFilter).then(res => {
                    that.buscharDialog.visible = true;
                    that.buscharDialog.data = res.data
                    that.buscharDialog.title = res.data.legend[0]
                });
            },
        },
    };
</script>

<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
