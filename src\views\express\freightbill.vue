<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="快递公司:">
          <el-select v-model="filter.companyId" filterable placeholder="请选择快递公司" style="width: 120px">
              <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id"/>
           </el-select>
        </el-form-item>
        <el-form-item label="发货仓库:">
           <el-select v-model="filter.warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 120px">
             <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
         <el-form-item label="结算月份:">
           <el-date-picker v-model="filter.settMonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry='summaryarry'
        :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
     </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
    <el-drawer :title="formtitle" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="addFormVisible" 
                direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
    <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
      <div class="drawer-footer">
        <el-button @click.native="addFormVisible = false">取消</el-button>
        <my-confirm-button type="submit"  :loading="addLoading" @click="onAddSubmit" />
      </div>
    </el-drawer>
  </my-container>
</template>

<script>
import {pageFreightBill} from '@/api/express/freight'
import {getExpressComanyAll} from "@/api/express/express";
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatYesornoBool,formatLink,formatmoney,formatTime,warehouselist} from "@/utils/tools";
const tableCols =[
    {istrue:true,companyid:0,prop:'billnumber',label:'运单号码', width:'100',sortable:'custom'},
    {istrue:true,companyid:0,prop:'province',label:'省份', width:'60',sortable:'custom'},
    {istrue:true,companyid:0,prop:'receiveTime',label:'揽收时间', width:'110',sortable:'custom',formatter:(row)=>formatTime(row.receiveTime,'MM-DD HH:mm:ss')},
    {istrue:true,companyid:0,prop:'consignee',label:'客户姓名', width:'80'},
    {istrue:true,companyid:0,prop:'consigneeAddress',label:'目的地', width:'130',sortable:'custom'},
    {istrue:true,companyid:0,prop:'count',label:'件数', width:'60',sortable:'custom'},
    {istrue:true,companyid:0,prop:'weight',label:'称入重量', width:'80',sortable:'custom'},
    {istrue:true,companyid:0,prop:'otherExInAmount',label:'其他收支合计', width:'70'},
    {istrue:true,companyid:0,prop:'sjTotalFee',label:'账单合计', width:'80',sortable:'custom',formatter:(row)=>formatmoney(row.sjTotalFee)},
    {istrue:true,companyid:0,prop:'overWeightFee',label:'续重费', width:'70',sortable:'custom'},
    {istrue:true,companyid:0,prop:'faceSheetFee',label:'面单费', width:'90',sortable:'custom'},
    {istrue:true,companyid:0,prop:'jsTotalFee',label:'核算合计', width:'80',sortable:'custom'},
    {istrue:true,companyid:0,prop:'difference',label:'差额', width:'80',sortable:'custom'},
    {istrue:true,companyid:0,prop:'volume',label:'体积', width:'80'},
    {istrue:true,companyid:0,prop:'vWeight',label:'体积重', width:'70'},
    {istrue:true,companyid:0,prop:'sjVMoney',label:'体积金额', width:'80'},
    {istrue:true,companyid:0,prop:'difference',label:'差异金额', width:'80'},
    {istrue:true,companyid:0,prop:'lpAmount',label:'理赔', width:'80'},
    {istrue:true,companyid:0,prop:'lpResons',label:'理赔原因', width:'100'},
    {istrue:true,companyid:0,prop:'pyAmount',label:'加收派费', width:'80'},
    {istrue:true,companyid:0,prop:'tjAmount',label:'退件费', width:'70'},
    {istrue:true,companyid:0,prop:'zjAmount',label:'转件费', width:'70'},
    {istrue:true,companyid:0,prop:'qtAmount',label:'其他', width:'60'},

    {istrue:true,companyid:1, prop:'receiver',label:'收件员', width:'90'},
    {istrue:true,companyid:1, prop:'zHFee',label:'中转费', width:'70'},
    {istrue:true,companyid:1, prop:'weightFee',label:'称重费', width:'70'},
    {istrue:true,companyid:1, prop:'scanFee',label:'扫描费', width:'70'},
    {istrue:true,companyid:1, prop:'supportFee',label:'扶持费', width:'70'},
    {istrue:true,companyid:1, prop:'chaoWeightFee',label:'超重费', width:'70'},
    {istrue:true,companyid:1, prop:'beijShanghContinuWeightFee',label:'北京/上海续重派费', width:'80'},
    {istrue:true,companyid:1, prop:'timeRange',label:'派送时效', width:'80'},

    {istrue:true,companyid:2, prop:'examTime',label:'审核日期', width:'130',formatter:(row)=>formatTime(row.examTime,'MM-DD HH:mm:ss')},
    {istrue:true,companyid:2, prop:'custermNo',label:'客户编号', width:'80'},
    {istrue:true,companyid:2, prop:'custermer',label:'客户简称', width:'80'},
    {istrue:true,companyid:2, prop:'wanWan',label:'旺旺号', width:'70'},
    {istrue:true,companyid:2, prop:'shopType',label:'店铺类型', width:'80'},

    {istrue:true,companyid:3, prop:'zHFee',label:'中转费', width:'70'},
    {istrue:true,companyid:3, prop:'fivefee',label:'五省加收费用', width:'100'},
    {istrue:true,companyid:3, prop:'beijShanghContinuWeightFee',label:'北京/上海续重派费', width:'150'},
    {istrue:true,companyid:3, prop:'xzNmgFee',label:'西藏/内蒙古加收', width:'120'},
    {istrue:true,companyid:3, prop:'retrun2kg',label:'2kg以下返', width:'120'},    

    {istrue:true,companyid:4, prop:'jFWeight',label:'计费重量', width:'80'},
    {istrue:true,companyid:4, prop:'facefeeCustomer',label:'面单发放客户', width:'150'},
    {istrue:true,companyid:4, prop:'shopName',label:'店铺', width:'80'},
    {istrue:true,companyid:4, prop:'sender',label:'发件人', width:'70'},
    {istrue:true,companyid:4, prop:'receiveCustomer',label:'揽收客户', width:'80'},
    {istrue:true,companyid:4, prop:'receiver',label:'收/派件员', width:'80'},
    
    {istrue:true,companyid:5, prop:'flowNo',label:'流水号', width:'70'},
    {istrue:true,companyid:5, prop:'goodsType',label:'产品类型', width:'80'},
    {istrue:true,companyid:5, prop:'pickType',label:'提货方式', width:'80'},
    {istrue:true,companyid:5, prop:'sender',label:'发货人', width:'70'},
    {istrue:true,companyid:5, prop:'goodsName',label:'货物名称', width:'80'},
    {istrue:true,companyid:5, prop:'insurance',label:'保价', width:'60'},
    {istrue:true,companyid:5, prop:'insuranceFee',label:'保价费', width:'70'},
    {istrue:true,companyid:5, prop:'billingWeight',label:'计费重量', width:'80'},
    {istrue:true,companyid:5, prop:'collectionAmount',label:'代收货款金额', width:'100'},
    {istrue:true,companyid:5, prop:'warehouseFee',label:'送货进仓费', width:'100'},
    {istrue:true,companyid:5, prop:'boxFee',label:'包装费', width:'70'},
    {istrue:true,companyid:5, prop:'transferFee',label:'中转费', width:'70'},
    {istrue:true,companyid:5, prop:'collectionFee',label:'代收货款手续费', width:'150'},
    {istrue:true,companyid:5, prop:'publishedFreight',label:'公布价运费', width:'100'},
    {istrue:true,companyid:5, prop:'distributionFee',label:'配送费（家装）', width:'120'},
    {istrue:true,companyid:5, prop:'upstairsFee',label:'大件上楼费', width:'100'},
    {istrue:true,companyid:5, prop:'overweightOperFee',label:'超重操作费', width:'100'},
    {istrue:true,companyid:5, prop:'installationFee',label:'安装费', width:'70'},
    {istrue:true,companyid:5, prop:'preServiceFee',label:'预售前置服务费', width:'110'},
    {istrue:true,companyid:5, prop:'returnType',label:'返单类别', width:'80'},
    {istrue:true,companyid:5, prop:'preferentialFee',label:'优惠费用', width:'80'},
    {istrue:true,companyid:5, prop:'payType',label:'付款方式', width:'80'},
    {istrue:true,companyid:5, prop:'goodsAmont',label:'总金额', width:'70'},
    {istrue:true,companyid:5, prop:'discountMoney',label:'事后折金额', width:'100'},
    {istrue:true,companyid:5, prop:'unOffAmount',label:'未核销金额', width:'100'},
    {istrue:true,companyid:5, prop:'offAmount',label:'已核销金额', width:'100'},
    {istrue:true,companyid:5, prop:'uninvoicedAmount',label:'未开票金额', width:'100'},
    {istrue:true,companyid:5, prop:'invoiceMark',label:'发票标记', width:'80'},
    {istrue:true,companyid:5, prop:'isOverseas',label:'是否境内境外', width:'100'},
    {istrue:true,companyid:5, prop:'signDate',label:'签收日期', width:'80'},
    {istrue:true,companyid:5, prop:'arriveCity',label:'返货前到达城市', width:'120'},
    {istrue:true,companyid:5, prop:'sourceNo',label:'来源单据编号', width:'110'},
    {istrue:true,companyid:5, prop:'flowType',label:'流水类型', width:'80'},
    {istrue:true,companyid:5, prop:'flowChildType',label:'流水子类型', width:'100'},
    {istrue:true,companyid:5, prop:'receiverName',label:'收货客户名称', width:'110'},
    {istrue:true,companyid:5, prop:'receiverContact',label:'收货人联系方式', width:'120'},
    {istrue:true,companyid:5, prop:'senderName',label:'发货客户名称', width:'120'},
    {istrue:true,companyid:5, prop:'senderContact',label:'发货人联系方式', width:'150'},
    {istrue:true,companyid:5, prop:'senderAddress',label:'发货人详细地址', width:'150'},
    {istrue:true,companyid:5, prop:'orderNo',label:'订单号', width:'80'},
    {istrue:true,companyid:5, prop:'selfReduction',label:'自提减免', width:'80'},
    {istrue:true,companyid:5, prop:'otherFee',label:'其他费用', width:'80'},
    {istrue:true,companyid:5, prop:'dbvolume',label:'体积', width:'60'},
    {istrue:true,companyid:5, prop:'reMark',label:'备注', width:'80'},                        

    {istrue:true,companyid:6, prop:'receiver',label:'揽件员', width:'70'},
    {istrue:true,companyid:6, prop:'goodsName',label:'产品名称', width:'80'},
    {istrue:true,companyid:6, prop:'addDate',label:'添加日期', width:'80',formatter:(row)=>formatTime(row.addDate,'MM-DD HH:mm:ss')},
    {istrue:true,companyid:6, prop:'selfCustomer',label:'自有客户', width:'80'},
    {istrue:true,companyid:6, prop:'protocolCustomer',label:'协议客户', width:'80'},
    {istrue:true,companyid:6, prop:'protocolNo',label:'协议号', width:'70'},
    {istrue:true,companyid:6, prop:'consigneeCity',label:'收件市', width:'70'},
    {istrue:true,companyid:6, prop:'sender',label:'寄件人', width:'70'},
    {istrue:true,companyid:6, prop:'senderContact',label:'寄件电话', width:'80'},
    {istrue:true,companyid:6, prop:'senderAddress',label:'寄件地址', width:'80'},
    {istrue:true,companyid:6, prop:'receiverNo',label:'集团揽件员工号', width:'120'},
    {istrue:true,companyid:6, prop:'receiverName',label:'集团揽件员姓名', width:'120'},
    
    {istrue:true,companyid:7, prop:'city',label:'目的地市', width:'80'},
    {istrue:true,companyid:7, prop:'weightInt',label:'取整重量', width:'80'},
    
    {istrue:true,companyid:8, prop:'expressType',label:'产品类型', width:'80'},
    {istrue:true,companyid:8, prop:'payType',label:'付款方式', width:'80'},
    {istrue:true,companyid:8, prop:'beforeTotalFee',label:'费用(元)', width:'80'},
    {istrue:true,companyid:8, prop:'discountMoney',label:'折扣/促销', width:'80'},
    {istrue:true,companyid:8, prop:'receiver',label:'经手人', width:'70'},
    {istrue:true,companyid:8, prop:'overflowMoney',label:'增值费用', width:'80'}
     ];
const tableHandles1=[
        //{label:"新增", handle:(that)=>that.onAdd()},
        //{label:'编辑', handle:(that)=>that.onEdit()}
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: {
          settMonth:null,
          companyId:"1", 
          warehouse :null,
      },
      warehouselist:warehouselist,
      list: [],
      expresscompanylist:[],
      pager:{OrderBy:"id",IsAsc:false},
      summaryarry:{},
      tableCols:[],
      tableHandles:tableHandles1,
      platFormList:[],
      autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 16 }}}},
               rule:[{type:'hidden',field:'id',title:'id',value: ''},
                     //{type:'input',field:'proBianMaName',title:'商品编码名称',value: '',validate: [{type: 'string', required: true, message:'请输入商品编码名称'}]},
                     {type:'input',field:'proBianMaName',title:'商品编码名称',value: ''},
                     {type:'inputNumber',field:'weight',title:'重量(kg)',value: 0},
                     {type:'inputNumber',field:'volumeWeight',title:'计泡重量(kg)',value: 0},
                     {type:'select',field:'removeCalc',title:'排除计算', validate: [{type: 'boolean', required: true, message:'请选择'}],value: null,options: [{value:null, label:'请选择'},{value:false, label:'否'},{value:true, label:'是'}]},
                ]
        },
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,     
      deleteLoading: false,
      formtitle:"新增",
      groupList:[],
      brandlist:[]  
    }
  },
  async mounted() {
    this.filter.settMonth= formatTime(new Date(),'YYYYMM');
    await this.getExpressComanyList();   
    await this.getlist();
    //await this.setGroupSelect();
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async inittableCols(companyid){
        companyid=parseInt(companyid);
        this.tableCols=[];
        tableCols.forEach(f=>{
           if (f.companyid==0|| f.companyid==companyid) 
              this.tableCols.push(f);
        })
      },
    async oncompanychange(){

     },
    async onSearch() {
      
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getExpressComanyList() {
      const res = await getExpressComanyAll({});
      if (!res?.success) return;
      const data = res.data;
      this.expresscompanylist = data;
     },
    async getlist() {
      await this.inittableCols(this.filter.companyId);
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      this.listLoading = true
      const res = await pageFreightBill(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
  async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
  async onEdit(row) {
      this.formtitle='编辑';
      this.addFormVisible = true
      const res = await getWarehousesigle({id:row.id})
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
         this.autoform.fApi.resetFields()
      await this.autoform.fApi.setValue(res.data)
    },  
  async onEditSubmit() {
      this.addFormVisible = true
      await onAddSubmit();
    },
  async onAddSubmit() {
      this.addLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          formData.id=formData.id?formData.id:0;
          formData.Enabled=true;
          const res = await updateWarehouse(formData);
          if(res.code==1){
            this.getlist();
            this.addFormVisible=false;
          }
        }else{
          //todo 表单验证未通过
        }
     })
     this.addLoading=false;
    },
    selsChange: function(sels) {
      this.sels = sels
    }
  }
}
</script>
