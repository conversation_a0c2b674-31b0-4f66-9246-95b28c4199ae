<template>
    <el-row>
        <el-col :span="15" style="height: 100%;">
            <MyContainer v-loading="pageLoading">
                <template #header>
                </template>
                <vxetablebase :id="'HotSaleBrandPushNewSale32202408170011'" ref="table" :that='that' :isIndex='true'
                    :hasexpand='true' :tablefixed='true' :border="true" @sortchange='sortchange' @select='selectchange'
                    :tableData='tableData' :tableCols='tableCols' :showsummary='true' :summaryarry='summaryarry'
                    :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="listLoading"
                    height="100%">
                </vxetablebase>
                <template #footer>
                    <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
                </template>
            </MyContainer>
        </el-col>
        <el-col :span="9" style="height: 100%;">
            <pie-chart :pie-data="pieData" height="430px" />
        </el-col>
    </el-row>

</template>

<script>
import MyContainer from "@/components/my-container";
import datepicker from '@/views/customerservice/datepicker';
import { pickerOptions } from '@/utils/tools';
import dayjs from 'dayjs';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    GetHotSaleBrandPushNewSaleDtlPlarformList2
} from '@/api/operatemanage/productalllink/alllink'
import PieChart from '@/views/admin/homecomponents/PieChart.vue'
import { tonumfuc } from '@/utils/tonumqian.js'
const tableCols = [
    { sortable: 'custom', width: '120', align: 'center', prop: 'chooseUserPlatform', label: '平台', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'profit4', label: '毛四', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'profit4Rate', label: '毛四占比', formatter: (row) => row.profit4Rate + '%' },
];
export default {
    name: "HotSaleBrandPushNewSale4",
    components: {
        MyContainer, datepicker, vxetablebase, PieChart
    },
    data() {
        return {
            auditVisible: false,
            activities: [],
            timeRanges: [],
            that: this,
            pickerOptions,
            filter: {
                timerange: [],
                saleStartDate: null,
                saleEndDate: null,
            },
            pager: { OrderBy: "profit4Rate", IsAsc: false },
            tableCols,
            tableData: [],
            total: 0,
            listLoading: false,
            pageLoading: false,
            sels: [],
            selids: [],
            summaryarry: {},
            pieData: {},
        }
    },
    async mounted() {

    },
    computed: {
    },
    methods: {
        async loadData(args) {
            this.filter.createdUserId = args.createdUserId;
            this.filter.createdUserName = args.createdUserName;
            this.filter.timerange = args.timerange;
            this.isSum = args.isSum;

            this.filter.createdUserNames = args.createdUserNames;
            this.filter.createUserAreas = args.createUserAreas;
            this.filter.createUserRoles = args.createUserRoles;
            this.filter.createUserDeptNames = args.createUserDeptNames;

            console.log(this.filter, "this.filter");
            this.onSearch();
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getParam() {
            if (this.filter.timerange && this.filter.timerange.length == 2) {
                this.filter.saleStartDate = this.filter.timerange[0];
                this.filter.saleEndDate = this.filter.timerange[1];
            }
            else {
                this.filter.saleStartDate = null;
                this.filter.saleEndDate = null;
            }
            let pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            return params;
        },
        async getList() {
            let param = this.getParam();
            this.listLoading = true
            const res = await GetHotSaleBrandPushNewSaleDtlPlarformList2(param)
            this.listLoading = false
            console.log(res);
            if (res?.success) {
                this.tableData = res.data.list;
                this.total = res.data.total;
                // Object.keys(res.data.summary).forEach(f => {
                //     res.data.summary[f] = res.data.summary[f].toString();
                // });
                this.summaryarry = res.data.summary;

                let pieList = res.data.list.map(f => {
                    return {
                        name: f.chooseUserPlatform,
                        value: tonumfuc(f.profit4, '-'),
                        ratio: f.profit4Rate,
                    }
                });

                this.pieData = {
                    title: "平台毛三占比",
                    legend: res.data.list.map(f => f.chooseUserPlatform),
                    pieSeries: pieList
                };

            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}
</style>
