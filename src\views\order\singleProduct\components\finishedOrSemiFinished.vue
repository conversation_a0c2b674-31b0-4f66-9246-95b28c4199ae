<template>
    <MyContainer>
        <vxetablebase ref="table" id="20241216144300" :loading="loading" :that="that" :is-index="true" :hasexpand="true"
            :tablefixed="true" :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols"
            :is-selection="false" class="mainTable1" :isNeedExpend="false" :is-select-column="true"
            :is-index-fixed="false" @cellStyle="cellStyle" cellStyle @footerCellStyle="footerCellStyle"
            somerow="goodsCode,goodsName,qty,sellStock,packingQty,picture" :showoverflow="false"
            style="width: 100%; margin: 0;height: 500px;" height="100%" :showsummary="data.summary ? true : false"
            :summaryarry="data.summary" @sortchange="sortchange" />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/SaleItems/PackBom/'
import { mergeTableCols } from '@/utils/getCols'
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan
    },
    props: {
        query: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            api,
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false,
                goodsCode: this.query.goodsCode,
                summarys: [],
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        await this.getCol();
        await this.getList()
    },
    methods: {
        footerCellStyle(row, callback) {
            let cols = []
            this.tableCols.forEach(item => {
                if (item.cols && item.cols.length > 0) {
                    item.cols.forEach(item1 => {
                        cols.push(item1)
                    })
                } else {
                    cols.push(item)
                }
            })
            const res = cols.find(item => item.prop == row.column.field)
            for (let i = 0; i <= cols.length; i++) {
                if (res.headerBgColor) {
                    callback({ backgroundColor: res.headerBgColor })
                }
            }
        },
        async cellStyle(row, column, callback) {
            let cols = []
            this.tableCols.forEach(item => {
                if (item.cols && item.cols.length > 0) {
                    item.cols.forEach(item1 => {
                        cols.push(item1)
                    })
                } else {
                    cols.push(item)
                }
            })
            const res = cols.find(item => item.prop == column.field)
            callback({ backgroundColor: res ? res.headerBgColor : '' })
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                // data.forEach(item => {
                //     item.width = 'auto'
                // })
                this.tableCols = mergeTableCols(data)
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0;
    }
}

::v-deep .mainTable1 .vxe-table td {
    border-right: solid 1px #888 !important;
    border-top: solid 1px #888 !important;
    box-sizing: content-box;

    & :last-child {
        border-top: none !important;
    }
}
</style>
