<template>
  <el-scrollbar>
  <div style="padding: 1% 2%;height: 90vh;box-sizing: border-box;">
    <div v-show="!indexContent">
      <div style="font-size: 14px;margin-bottom: 10px;"><span>最近访问</span>
      </div>
      <div style="min-height:50px">
        <el-tag class="oftenTag" v-for="tag in oftenQuestionList.item1" :key="tag.reqPath" @click="toRouterPage(tag)"> {{tag.moduleName}}-{{tag.pageName}}</el-tag>
      </div>
      <div style="font-size: 14px;margin-bottom: 10px;"><span>经常访问</span>
      </div>
      <div style="min-height:50px">
        <el-tag class="oftenTag" v-for="tag in oftenQuestionList.item2" :key="tag.reqPath" @click="toRouterPage(tag)"> {{tag.moduleName}}-{{tag.pageName}}</el-tag>
      </div>
      <!-- <div style="font-weight: bold;font-size: 20px;">最近使用应用</div>
      <div class="search-bar" style="display: flex;">
        <div class="search-bar_tagbutton">
          <el-scrollbar style="height: 100%">
            <span v-for="(item, index) in labelContent" @click="onTagEvent(item)" :key="index" class="labelStyle">
              {{ item }}
            </span>
          </el-scrollbar>
        </div>
      </div> -->
    </div>
    <div>
      <div class="title"><span>问题解答</span>
      </div>

      <div class="questionContent">
              <div v-if="!searchData" style="color: #999999;">
                没有找到你想看的东西，快去提问吧
                </div>
             <template v-else>
                <div v-for="(item,index) in searchData.questionList" @click="onHotIssueMethod(item)" v-if="searchData.questionList?.length>0"  :key="index" >
                  {{index+1+'.'}}<span v-html="(item.showType == 0 && item.question) ? item.question : (item.showType == 1 && item.answer) ? item.answer : ''"></span>
                </div>

              </template>
    </div>
    <slot name="pagechange"></slot>
    <div>
      <div class="title">
        <span>欢迎提问</span>
      </div>
      <el-form style="margin-top:10px" label-position='left' label-width="110px" :model="ruleForm" :rules="rules" ref="ruleForm">
            <el-form-item label="内容:" prop="content">
                  <el-input v-model="ruleForm.content"  type="textarea" placeholder="请输入" maxlength="300" clearable
                    style="width: 100%;" />
             </el-form-item>
            <el-form-item label="添加申请图片:" prop="imgsPrivate">
                    <div class="questionImg">
                      <uploadimgFile ref="uploadimgFilePrivate" :accepttyes="accepttyes" :isImage="true"
                          :uploadInfo="imgUrlsPrivate" :keys="[1, 1]"  @callback="getImgsPrivate"
                          :imgmaxsize="5" :limit="5" :multiple="true">
                      </uploadimgFile>
                      <span class="imgTips">点击灰框可直接贴图</span>
                </div>
                </el-form-item>
                <el-form-item label="添加公开图片:" style="margin:30px 0;" prop="imgsPublic">
                    <div class="questionImg">
                      <uploadimgFile ref="uploadimgFilePublic" :accepttyes="accepttyes" :isImage="true"
                          :uploadInfo="imgUrlsPublic" :keys="[1, 1]"  @callback="getImgsPublic"
                          :imgmaxsize="5" :limit="5" :multiple="true">
                      </uploadimgFile>
                      <span class="imgTips">点击灰框可直接贴图</span>
                </div>
              </el-form-item>
                   <el-form-item prop="beAskedUserIds" label="指定回答人:" >
                    <el-select
                    v-model="ruleForm.beAskedUserIds"
                    multiple
                    filterable
                    default-first-option
                    collapse-tags
                    reserve-keyword
                     remote
                     :remote-method="remoteMethod"
                    :loading="loading"
                    placeholder="请选择回答人员">
                    <el-option
                      v-for="item in personList"
                      :key="item.userId"
                      :label="item.name +'-'+ item.posts"
                      :value="item.userId">
                    </el-option>
                  </el-select>
                  </el-form-item>
                  <el-form-item label="标签:" prop="linkId">
                    <el-select v-model="ruleForm.linkId" placeholder="请选择标签" clearable filterable>
                      <el-option v-for="item in tagMaintenanceData" :key="item.id" :label="item.value" :value="item.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                     <div style="display: flex;justify-content: end">
                      <el-button @click="handInQuestion('取消')">取消</el-button>
                       <el-button type="primary" @click="handInQuestion('提交')">提交</el-button>
                     </div>
                    </el-form-item>
          </el-form>
    </div>
    <!-- <div>
      <div class="title" style="margin-top:10px">热门问题</div>
      <div class="questionContent" >
              <div v-if="!searchData" style="color: #999999;">
                没有找到你想看的东西，快去提问吧
                </div>
              <template v-else>
                <div v-for="(item,index) in searchData.questionHot" :key="index" >
                  <div>{{index+1+'.'}}<span v-html="item.title" style="cursor: pointer;" v-if="searchData.questionList&&searchData.questionHot.length>0" @click="onHotIssueMethod(item)"></span></div>
                  <div>
                    <span>回答：</span>
                    <span v-html="item.description"></span>
                  </div>
                </div>
              </template>
      </div>

    </div> -->
    <el-dialog title="提示" :visible.sync="questionDialogVisible" width="30%" v-dialogDrag>
      <div><i class="el-icon-success" style="color:green"></i>
        已提交问题
      </div>
        <div slot="footer" class="dialog-footer">
        <el-button @click="questionDialogVisible = false">
          取 消
          </el-button>
        <el-button type="primary" @click="onTagEvent('');questionDialogVisible=false">
          去查看
          </el-button>
      </div>
    </el-dialog>
  </div>
  </div>
</el-scrollbar>

</template>

<script>

import { saveQuestion } from "@/api/kbs/qa.js"
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'
import { employeeList } from '@/api/bladegateway/xzgateway.js';
import middlevue from "@/store/middle.js"
import { pageList } from "@/api/order/orderData";
import {HotPageReqRecord} from "@/api/admin/opration-log.js"
export default {
  name: 'SearchPage',
    components:{YhQuillEditor,uploadimgFile},
    components:{YhQuillEditor,uploadimgFile},
  props: {
    searchData: {
      type: Object,
      default() {
        return {}
      }
    },
    searchContent: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading:false,
      rules: {
        content: [
            { required: true, message: '请填写问题', trigger: 'blur' }
        ],
      },
       accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
      ruleForm:{
        content:null,
        beAskedUserIds:null,
        linkId:null,
         imgs:{
          private:[],
          public:[]
        },
      },
      tagMaintenanceData: [],
      imgUrlsPrivate:[],
      imgUrlsPublic:[],
      questionContent: null,
      questionDialogVisible: false,
      labelContent: [],
      personList:[],
      oftenQuestionList:[],
      indexContent:''
    };
  },
  watch: {
    searchData(newVal, oldVal) {
     },
     searchContent(newVal, oldVal) {
      this.indexContent = newVal
     },
    },
  async mounted() {
    console.log(this.searchData, 'searchData');
    await this.getoftenQuestion()
    await this.init()
  },
  methods: {
    toRouterPage(page){
      this.$router.push({ path: page.reqPath })
      this.$emit('searchEngineClose', true)
    },
   async getoftenQuestion(){
    const {data} =  await HotPageReqRecord()
    this.oftenQuestionList = data
    },
    async init() {
      const { data, success } = await pageList({pageSize: 20, currentPage: 1})
      if(success){
        this.tagMaintenanceData = data
      }
    },
    extractTextFromHTML(html) {
      let text = '';
      let inTag = false;
      let buffer = '';

      for (let i = 0; i < html.length; i++) {
          let char = html[i];
          if (char === '<') {
              if (!inTag && buffer.trim() !== '') {
                  text += buffer.trim();
                  buffer = '';
              }
              inTag = true;
          } else if (char === '>' && inTag) {
              inTag = false;
          } else if (!inTag) {

              buffer += char;
          }
      }

      if (buffer.trim() !== '') {
          text += buffer.trim();
      }
      return text.trim();
    },
    onHotIssueMethod(item){
      if(item){
        let params = {
            ...item,
            question: this.extractTextFromHTML(item.question),
          }
          localStorage.setItem('searchEngine/kbs', this.extractTextFromHTML(item.question));
          middlevue.$emit('searchEnginekbs', params)
          // this.$emit('searchEngineClose')
          this.$emit('searchEngineClose', true)
          this.$nextTick(() => {
            setTimeout(() => {
            this.$router.push({ path: '/kbs/searchEngine/kbs' })
            },0 )
        })
      }
    },

     async remoteMethod(query) {
        if (query !== '') {
          this.loading = true;
           const {data} = await employeeList({name:query})
            this.personList =  data.map(item=>{
          return {name:item.name,posts:item.posts,userId:item.userId}
        })
          this.loading = false;
        } else {
           this.personList = [];
        }
      },
    onTagEvent(item) {
      localStorage.setItem('searchEngine/kbs', item);
      this.$router.push({ path: '/kbs/searchEngine/kbs', })
    },
    getImgsPublic(data) {
        if (data) {
            this.imgUrlsPublic = data ? data : []
            this.ruleForm.imgs.public = data.map(item => item.url)
        }
    },
    getImgsPrivate(data) {
        if (data) {
            this.imgUrlsPrivate = data ? data : []
            this.ruleForm.imgs.private = data.map(item => item.url)
        }
    },
    async handInQuestion(type) {
      //提交问题 //清空内容
      let params = {
          content:this.ruleForm.content,
          beAskedUserIds:this.ruleForm.beAskedUserIds.join(','),
          images:this.ruleForm.imgs,
        }
      if(type == '取消'){


              this.ruleForm.imgs.private=null
                this.ruleForm.imgs.public=null
                this.imgUrlsPrivate = []
                this.imgUrlsPublic = []
              this.$refs.uploadimgFilePublic._data.retdata = []
                this.$refs.uploadimgFilePrivate._data.retdata = []

        this.$refs.ruleForm.resetFields();
        this.$emit('searchEngineClose', true)
        return
      }
      this.$refs.ruleForm.validate(async(va)=>{
          if(va){

            let res = await saveQuestion(params)
            if (res.success) {
                this.$message({
                  message: '提交问题成功',
                  type: 'success',
                  offset: 40
                });
                 this.ruleForm.imgs.private=null
                this.ruleForm.imgs.public=null
                this.imgUrlsPrivate = []
                this.imgUrlsPublic = []
              this.$refs.uploadimgFilePublic._data.retdata = []
                this.$refs.uploadimgFilePrivate._data.retdata = []
                 this.$refs.ruleForm.resetFields();
                 this.$emit('searchEngineClose', true)
              } else {
                this.$message({
                  message: '提交失败请重新尝试',
                  offset: 40
                });
              }
          }
      })



    },
  },
};
</script>

<style scoped lang="scss">
::v-deep .el-input__inner{
  border: 1px solid #DCDFE6 !important;
}
::v-deep .search-bar .search-bar_tagbutton .el-scrollbar .el-scrollbar__wrap .el-scrollbar__view {
  white-space: nowrap;
}

::v-deep .search-bar .search-bar_tagbutton .el-scrollbar {
  align-items: center;
}

::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
  margin-bottom: 10px !important;
}
.questionImg {
    position: relative;

    .imgTips {
        position: absolute;
        left: 0;
        color: #ff0000;
        font-size: 14px;
    }
}

.labelStyle {
  padding: 0 15px;
  margin-right: 10px;
  cursor: pointer;
  height: 30px;
  width: 130px;
  line-height: 30px;
  align-items: center !important;
  border-radius: 5px;
  border: 1px solid #000;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.search-bar {
  display: flex;
  align-items: center;
  width: 100%;
  margin: 2% 0;

  .search-bar_tagbutton {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    overflow-y: hidden !important;
  }
}

.title {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  color: #333333;
}

.tipBox {
  position: relative;
}
.questionContent{
   margin-bottom:10px ;
   padding:10px;
    overflow-y: scroll;

height: 150px;
background: #F5F6F7;
border-radius: 7px;
font-weight: 400;
font-size: 14px;
color: #333333;
line-height: 28px;
}
.oftenTag{
  height: 30px;
  line-height: 30px;
  box-sizing: border-box;
  margin: 0 10px 10px 0;
  cursor: pointer;
}
</style>
