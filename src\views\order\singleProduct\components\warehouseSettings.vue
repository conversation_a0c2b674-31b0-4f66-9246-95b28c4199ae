<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="goodsCodesCallback($event, 1)" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable
                    class="publicCss" />
                <inputYunhan ref="packMtlCodes" :inputt.sync="ListInfo.packMtlCodes" v-model="ListInfo.packMtlCodes"
                    placeholder="包材编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="goodsCodesCallback($event, 2)" title="包材编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <el-input v-model.trim="ListInfo.packMtl" placeholder="包装材料" maxlength="50" clearable
                    class="publicCss" />
                <el-select v-model="ListInfo.isMatchProvince" placeholder="匹配发货地" clearable class="publicCss" style="width: 130px;">
                  <el-option label="一致" :value="true" />
                  <el-option label="不一致" :value="false" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
                <el-button type="primary" @click="importProps">导入</el-button>
                <el-button type="primary" @click="batchDel"
                    v-if="checkPermission('warehouseSettingsBatchDel')">批量删除</el-button>
                <el-button type="primary" @click="scaleSettings"
                    v-if="checkPermission('warehouseSettingsBatchEdit')">批量编辑</el-button>
                <el-button type="primary" @click="onLockMethod">锁定</el-button>
            </div>
        </template>
        <vxetablebase ref="table" id="20241201104659" :loading="loading" :that="that" :is-index="true" :hasexpand="true"
            :tablefixed="true" :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols"
            :is-selection="false" :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;"
            height="100%" @select="selectCheckBox" @sortchange="sortchange" >
            <template #isMatchProvince="{ row, index }">
              <div :style="{ color: row.isMatchProvince == false ? 'red' : '' }">{{ row.isMatchProvince ? '一致' : '不一致' }}</div>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="批量编辑" :visible.sync="scaleSettingsVisible" width="20%" v-dialogDrag>
            <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="120px"
                class="demo-ruleForm">
                <el-form-item label="安全天数" prop="safeDay">
                    <el-input-number v-model="ruleForm.safeDay" :min="0" :max="100" placeholder="安全天数" :controls="false"
                        :precision="1" style="width: 200px;" />
                </el-form-item>
                <el-form-item label="仓库" prop="wmsId">
                    <chooseWareHouse v-model="ruleForm.wmsId" :filter="sendWmsesFilter" @chooseWms="chooseWms"
                        v-if="scaleSettingsVisible" style="width: 200px;" />
                </el-form-item>
                <el-form-item label="克重区间" prop="weightMax">
                  <number-range :min.sync="ruleForm.weightMin" :max.sync="ruleForm.weightMax" min-label="重量小"
                    v-if="scaleSettingsVisible" max-label="重量大" style="width: 200px;" />
                </el-form-item>
                <el-form-item label="包装材料" prop="packMtlCode">
                    <el-select v-model="ruleForm.packMtlCode" placeholder="请选择包装材料" style="width: 200px;" clearable filterable>
                        <el-option v-for="item in basicData" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="外仓加工比例(%)" prop="rateNum">
                    <el-input-number v-model="ruleForm.rateNum" :min="0" :max="100" placeholder="外仓加工比例(%)"
                        :precision="2" :controls="false" style="width: 200px;" />
                </el-form-item>
                <el-form-item label="是否锁定" prop="isLock">
                  <el-select v-model="ruleForm.isLock" clearable filterable placeholder="是否锁定"
                    style="width: 200px;">
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button @click="scaleSettingsVisible = false">关闭</el-button>
                    <el-button type="primary" @click="submitForm('ruleForm')" v-throttle="2000">提交</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>

        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
            <div slot="title" class="header-title">
                <span class="title-text"><span>导入数据</span></span>
                <span class="title-close"><el-button @click="downLoadFile">下载模版</el-button></span>
            </div>
            <div>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                    :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                </el-upload>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="importVisible = false">关闭</el-button>
            </div>
        </el-dialog>

        <el-dialog title="锁定" :visible.sync="lockInfo.visible" width="20%" v-dialogDrag>
          <el-form :model="lockInfo" status-icon :rules="rulesLock" ref="ruleFormLock" label-width="120px"
            class="demo-ruleForm" v-loading="lockInfo.loading">
            <el-form-item label="是否锁定" prop="isLock">
              <el-select v-model="lockInfo.isLock" clearable filterable placeholder="是否锁定"
                style="width: 200px;">
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="lockInfo.visible = false">关闭</el-button>
            <el-button type="primary" @click="submitLockForm('ruleFormLock')" v-throttle="2000">提交</el-button>
          </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode, downloadLink } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/SaleItems/WmsCode/'
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import {
    getList as getGoodsList,
} from "@/api/inventory/basicgoods"
import decimal from "@/utils/decimalToFixed";
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, chooseWareHouse
    },
    data() {
        return {
            api,
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false,
                summarys: [],
                isMatchProvince: null,
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            lockInfo: {
              loading: false,
              isLock: false,
              visible: false
            },
            rulesLock: {
              isLock: [{ required: true, message: '请选择是否锁定', trigger: 'change' }]
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            selectList: [],
            scaleSettingsVisible: false,
            ruleForm: {
                safeDay: undefined,
                wmsId: null,
                wmsName: '',
                packMtlCode: '',
                rateNum: undefined,
                rate: null,
                weightMin: undefined,
                isLock: false,
                weightMax: undefined
            },
            rules: {
                safeDay: [{ required: true, message: '请输入安全天数', trigger: 'blur' }],
                wmsId: [{ required: true, message: '请选择仓库', trigger: 'change' }],
            },
            fileList: [],
            importLoading: false,
            importVisible: false,
            uploadLoading: false,
            file: null,
            selectList: [],
            fileparm: {},
            basicData: []
        }
    },
    async mounted() {
        this.init()
        await this.getCol();
        await this.getList()
    },
    methods: {
        async init() {
            const params = {
                currentPage: 1,
                pageSize: 2000,
                brandId: ['5'],
                groupId: ['23'],
                unBrandId: ["23", "12", "141", "150", "10101"]
            }
            const { data, success } = await getGoodsList(params)
            if (success) {
                this.basicData = data.list.map(item => {
                    return {
                        label: item.goodsCode + ' - ' + item.goodsName,
                        value: item.goodsCode
                    }
                })
            }
        },
        importProps() {
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        //上传文件
        onUploadRemove(file, fileList) {
            this.fileList = []
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            this.importVisible = false;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            const form = new FormData();
            form.append("file", item.file);
            var res = await request.post(`${this.api}Import`, form);
            if (res?.success)
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
            this.uploadLoading = false
            this.importVisible = false;
            await this.getList()
        },
        downLoadFile() {
            downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250527/1927252904943992832.xlsx', '产品编码快递均重仓库设置模版.xlsx');
        },
        chooseWms(wms) {
            this.ruleForm.wmsName = wms.name
        },
        sendWmsesFilter(wmses) {
            console.log(wmses);
            this.wmsesList = wmses;
            return wmses.filter((a) => a.name.includes('【昀晗-'));
        },
        submitForm(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    const res = this.selectList.map(item => {
                        item.safeDay = this.ruleForm.safeDay
                        item.wmsName = this.ruleForm.wmsName
                        item.oldWmsId = item.wmsId
                        item.wmsId = this.ruleForm.wmsId
                        item.packMtlCode = this.ruleForm.packMtlCode
                        item.rate = this.ruleForm.rateNum ? decimal(this.ruleForm.rateNum, 100, '/') : null
                        item.isLock = this.ruleForm.isLock
                        item.weightMin = this.ruleForm.weightMin
                        item.weightMax = this.ruleForm.weightMax
                        return item
                    })
                    const { success } = await request.post(`${this.api}ChangeWms`, res)
                    if (success) {
                        this.$message({
                            type: 'success',
                            message: '保存成功!'
                        });
                        this.scaleSettingsVisible = false
                        this.getList()
                        this.selectList = []
                    }
                } else {
                    return false;
                }
            });
        },
        submitLockForm(formName) {
          this.$refs[formName].validate(async (valid) => {
            if (valid) {
              const res = {
                ids: this.selectList.map(item => item.id),
                isLock: this.lockInfo.isLock,
              }
              this.lockInfo.loading = true
              const { success } = await request.post(`${this.api}Lock`, res)
              this.lockInfo.loading = false
              if (success) {
                this.$message({ message: '锁定成功!', type: 'success' })
                this.lockInfo.visible = false
                this.getList()
                this.selectList = []
              }
            }
          })
        },
        onLockMethod() {
          if (this.selectList.length == 0) return this.$message.error('请选择要锁定的数据!')
          this.lockInfo.visible = true
          this.lockInfo.isLock = false
          this.$nextTick(() => {
            this.$refs.ruleFormLock.resetFields();
          });
        },
        scaleSettings() {
            if (this.selectList.length == 0) return this.$message.error('请选择要编辑的数据!')
            this.ruleForm = {
                safeDay: undefined,
                wmsId: null,
                wmsName: '',
                packMtlCode: '',
                rateNum: undefined,
                rate: null,
                weightMin: undefined,
                isLock: false,
                weightMax: undefined
            }
            this.scaleSettingsVisible = true
            this.$nextTick(() => {
                this.$refs.ruleForm.resetFields();
            });
        },
        selectCheckBox(val) {
            this.selectList = JSON.parse(JSON.stringify(val))
        },
        batchDel() {
            if (this.selectList.length == 0) return this.$message.error('请选择要删除的数据!')
            this.$confirm('此操作将删除这些数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await request.post(`${this.api}Delete`, this.selectList)
                if (success) {
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                    this.getList()
                    this.selectList = []
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        goodsCodesCallback(e, val) {
            if (val == 1) {
                this.ListInfo.goodsCodes = e
            } else {
                this.ListInfo.packMtlCodes = e
            }
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                data.unshift({ label: '', type: 'checkbox', })
                data.forEach(item => {
                    if (item.prop == 'orderNoInner') {
                        item.type = 'orderLogInfo'
                        item.orderType = 'orderNoInner'
                    }
                    if (item.prop == 'proCode') {
                        item.type = 'html'
                        item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
                    }
                })
                this.tableCols = data;
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}

.dialog_top {
    height: 150px;
    display: flex;
    align-items: center;
    padding-bottom: 20px;
}

.header-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px 0 0;

    .title-text {
        display: flex;
        align-items: center;

        .title-close {
            margin-left: 10px;
        }
    }
}

.dialog_bottom {
    display: flex;
    justify-content: center;
    padding: 20px 0;
}

</style>
