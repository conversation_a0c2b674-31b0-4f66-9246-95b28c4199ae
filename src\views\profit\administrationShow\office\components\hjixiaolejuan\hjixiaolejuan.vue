<template>
    <MyContainer>
        <template #header>
            <div class="top">
          <el-date-picker v-model="timeRanges" type="monthrange" range-separator="至" start-placeholder="开始月份"
            end-placeholder="结束月份" style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM'" :clearable="false" />


                <!-- <el-select v-model="ListInfo.isStock" placeholder="区域" class="publicCss" clearable>
            <el-option :key="'是'" label="是" :value="0" />
            <el-option :key="'否'" label="否" :value="1" />
          </el-select> -->
                <!-- <el-select v-model="ListInfo.costType" placeholder="类型" class="publicCss" clearable>
                    <el-option v-for="item in districtList" :key="item" :label="item" :value="item" />
                </el-select> -->

                <!-- <el-input v-model.trim="ListInfo.orderNo" placeholder="退件快递单号" maxlength="50" clearable class="publicCss" />
          <el-input v-model.trim="ListInfo.orderNoInner" placeholder="原订单号" maxlength="50" clearable class="publicCss" /> -->
                <el-button type="primary" @click="getList('search')">查询</el-button>
                <el-button type="primary" @click="startImport">导入</el-button>
                <el-button type="primary" @click="downExcel">模板下载</el-button>
                <el-button type="primary" @click="exportExcel('search')">导出</el-button>
                <!-- <el-button type="primary" @click="saveBane('search')">存档</el-button>
          <div style="color: red; margin-left: 5px;">
            存档时间：{{timeCundang??'-'}}
          </div> -->

            </div>
        </template>
        <!-- :footer-data="footerData" -->
        <vxe-table border show-footer width="100%" height="100%" ref="newtable" :row-config="{ height: 40 }" show-overflow
            :loading="loading" :column-config="{ resizable: true }" :merge-footer-items="mergeFooterItems"
            :footer-method="footerMethod" :span-method="mergeRowMethod" :row-class-name="rowClassName"
            :data="tableData">
            <vxe-column field="monthDate" width="150" title="月份"></vxe-column>
            <vxe-column field="costType" width="170" title="类型"></vxe-column>

            <vxe-column field="ncCost" width="160" title="南昌" footer-align="left">
                <!-- <template slot-scope="scope">
                    {{ scope.row.costType == '占比' ? scope.row.ncCost + '%' : scope.row.ncCost }}
                </template> -->
            </vxe-column>
            <vxe-column field="whCost" width="160" title="武汉" footer-align="left">
                <!-- <template slot-scope="scope">
                    {{ scope.row.costType == '占比' ? scope.row.whCost + '%' : scope.row.whCost }}
                </template> -->
            </vxe-column>
            <vxe-column field="ywCost" width="160" title="义乌" footer-align="left">
                <!-- <template slot-scope="scope">
                    {{ scope.row.costType == '占比' ? scope.row.ywCost + '%' : scope.row.ywCost }}
                </template> -->
            </vxe-column>
            <vxe-column field="szCost" width="160" title="深圳" footer-align="left">
                <!-- <template slot-scope="scope">
                    {{ scope.row.costType == '占比' ? scope.row.szCost + '%' : scope.row.szCost }}
                </template> -->
            </vxe-column>
            <vxe-column field="productSelectionCenter" width="160" title="选品中心" footer-align="left">
                <!-- <template slot-scope="scope">
                    {{ scope.row.costType == '占比' ? scope.row.szCost + '%' : scope.row.szCost }}
                </template> -->
            </vxe-column>
            <vxe-column field="totalCount" width="170" title="合计" footer-align="left">
                <!-- <template slot-scope="scope">
                    {{ scope.row.costType == '占比' ? scope.row.totalCount + '%' : scope.row.totalCount }}
                </template> -->
            </vxe-column>
            <vxe-column field="remarks" width="290" title="备注"></vxe-column>

            <vxe-column title="操作" footer-align="left" width="100" fixed="right">
                <template slot-scope="scope">
                    <el-button type="text" size="mini"
                        v-if="!scope.row.costType.includes('合计')"
                        @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
                    <el-button type="text" size="mini" style="color:red" :disabled="scope.row.status == 1"
                        v-if="!scope.row.costType.includes('合计')"
                        @click="handleRemove(scope.$index, scope.row)">删除</el-button>
                </template>
            </vxe-column>
        </vxe-table>
        <!-- <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template> -->
        <el-drawer title="编辑" :visible.sync="dialogVisibleEdit" size="25%">
            <departmentEdit ref="departmentEdit" v-if="dialogVisibleEdit" :editInfo="editInfo" @search="closeGetlist"
                @cancellationMethod="dialogVisibleEdit = false" />
        </el-drawer>
        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
            <span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                    :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { downloadLink } from "@/utils/tools.js";
import { performanceDonationPage, dimissionManageArchive, performanceDonationImport, performanceDonationRemove } from '@/api/people/peoplessc.js';
import departmentEdit from "./departmentEdit.vue";
import checkPermission from '@/utils/permission'
const tableCols = [
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '审批状态', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNoInner', label: '商品编码', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'importTime', label: '商品名称', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'planSendTime', label: '原价', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'timePay', label: '现价', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight', label: '进货数量', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderStatus', label: '涨价原因', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'seriesName', label: '添加日期', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCount', label: '涨价日期', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '添加人', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '岗位', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '分公司', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人组长', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, departmentEdit
    },
    data() {
        const tableData = [
            //   { id: 10001, calculateMonth: '12月', regionName: '义务', name: 'Test1', nickname: 'T1', role: 'Develop', sex: 'Man', age: 28, address: 'test abc' },
            //   { id: 10002, calculateMonth: '12月', regionName: '义务', name: 'Test2', nickname: 'T2', role: 'Test', sex: 'Women', age: 22, address: 'Guangzhou' },
            //   { id: 10003, calculateMonth: '12月', regionName: '南昌', name: 'Test3', nickname: 'T3', role: 'PM', sex: 'Man', age: 32, address: 'Shanghai' },
            //   { id: 10004, calculateMonth: '12月', regionName: '南昌', name: 'Test4', nickname: 'T4', role: 'Designer', sex: 'Women', age: 23, address: 'test abc' },
            //   { id: 10005, calculateMonth: '12月', regionName: '北京', name: 'Test5', nickname: 'T5', role: 'Develop', sex: 'Women', age: 30, address: 'Shanghai' },
            //   { id: 10006, calculateMonth: '12月', regionName: '北京', name: 'Test6', nickname: 'T6', role: 'Designer', sex: 'Women', age: 21, address: 'test abc' },
            //   { id: 10007, calculateMonth: '12月', regionName: '深圳', name: 'Test7', nickname: 'T7', role: 'Test', sex: 'Man', age: 29, address: 'test abc' },
            //   { id: 10008, calculateMonth: '12月', regionName: '深圳', name: 'Test8', nickname: 'T8', role: 'Develop', sex: 'Man', age: 35, address: 'test abc' }
        ]
        const footerData = [
            //   { calculateMonth: '12月', regionName: '办公室合计', name: '办公室合计', role: '33', rate: '56' },
            //   { calculateMonth: '12月', regionName: '仓储合计', name: '仓储合计', role: 'bb', rate: '56' },
            //   { calculateMonth: '12月', regionName: '全区域合计', name: '全区域合计', role: 'bb', rate: '1235' }
        ]
        const mergeFooterItems = [
            //   { row: 0, col: 0, rowspan: 3, colspan: 1 },
            //   { row: 0, col: 1, rowspan: 0, colspan: 2 },
            //   { row: 1, col: 1, rowspan: 1, colspan: 2 },
            //   { row: 2, col: 1, rowspan: 2, colspan: 2 }
        ]
        return {
            downloadLink,
            dialogVisibleEdit: false,
            editInfo: {},
            fileList: [],
            dialogVisible: false,
            districtList: [],
            timeCundang: '',
            tableData,
            footerData,
            mergeFooterItems,
            somerow: 'costType,region,monthDate',
            that: this,
            ListInfo: {
                //   currentPage: 1,
                //   pageSize: 50,
                //   orderBy: null,
                //   isAsc: false,
                //   startTime: null,//开始时间
                monthDate: '',
            },
            timeRanges: [dayjs().subtract(1, 'month').format('YYYY-MM'),dayjs().subtract(1, 'month').format('YYYY-MM')],
            tableCols,
            summaryarry: {},
            total: 0,
            loading: false,
            pickerOptions,
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        footerMethod({ columns, data }) {
            console.log("底部数据", data)
            const sums = [];
            if (!this.footerData)
                return sums
            let newfield = columns.map(item => item.field)
            let newfooterdata = [];
            this.footerData.forEach((item, index) => {
                let newarr2 = [];
                newfield.forEach((item2, index2) => {
                    newarr2.push(item[item2])
                })
                newfooterdata.push(newarr2)
            })

            return newfooterdata;
        },
        rowClassName(event) {
            if (event.row.costType.indexOf('小计') > -1 || event.row.costType.indexOf('占比') > -1
            || event.row.costType.indexOf('合计')>-1) {
                return 'row-green'
            }
            return null
        },
        //上传文件
        onUploadRemove(file, fileList) {
            this.fileList = []
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            this.dialogVisible = false;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            const form = new FormData();
            form.append("file", item.file);
            form.append("isArchive", checkPermission("ArchiveStatusEditing"));
            form.append("calculateMonth", this.ListInfo.calculateMonth);
            var res = await performanceDonationImport(form);
            if (res?.success) {
                this.$message({ message: res.msg, type: "success" });
            }
            this.uploadLoading = false
            this.dialogVisible = false;
            await this.getList()
        },
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        //导入弹窗
        startImport() {
            this.fileList = []
            this.dialogVisible = true;
        },
        downExcel() {
            //下载excel
            downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250509/1920756520111751168.xlsx', '绩效乐捐导入模板.xlsx');
        },
        async saveBane() {
            this.$confirm('是否存档？存档后不可修改！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { data, success } = await dimissionManageArchive(this.ListInfo)
                if (!success) {
                    return;
                }
                this.getList();
                this.$message.success('保存存档成功！')

            }).catch(() => {
                // this.$message.error('取消')
            });
        },
        exportExcel() {
            this.$refs.newtable.exportData({ filename: '绩效乐捐', sheetName: 'Sheet1', type: 'xlsx' })
        },
        closeGetlist() {
            this.dialogVisibleEdit = false;
            this.getList()
        },
        handleEdit(index, row) {
            this.editInfo = row;
            this.dialogVisibleEdit = true;
        },
        async handleRemove(index, row) {
            this.$confirm('是否删除！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.editInfo = row;
                this.loading = true
                const { data, success } = await performanceDonationRemove({ ids: row.id })
                this.loading = false
                if (success) {
                    this.$message.success('删除成功')
                    this.getList();
                } else {
                    this.$message.error('删除失败')
                }
            }).catch(() => {

            });
        },
        // 通用行合并函数（将相同多列数据合并为一行）
        mergeRowMethod({ row, _rowIndex, column, visibleData }) {
            const fields = this.somerow.split(',')
            const cellValue = row[column.property]
            if (cellValue && fields.includes(column.property)) {
                const prevRow = visibleData[_rowIndex - 1]
                let nextRow = visibleData[_rowIndex + 1]
                if (prevRow && prevRow[column.property] === cellValue) {
                    return { rowspan: 0, colspan: 0 }
                } else {
                    let countRowspan = 1
                    while (nextRow && nextRow[column.property] === cellValue) {
                        nextRow = visibleData[++countRowspan + _rowIndex]
                    }
                    if (countRowspan > 1) {
                        return { rowspan: countRowspan, colspan: 1 }
                    }
                }
            }
        },
        async changeTime(e) {
            this.ListInfo.startTime = e ? e[0] : null
            this.ListInfo.endTime = e ? e[1] : null
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        // async exportProps() {
        //     const { data } = await exportStatData(this.ListInfo)
        //     const aLink = document.createElement("a");
        //     let blob = new Blob([data], { type: "application/vnd.ms-excel" })
        //     aLink.href = URL.createObjectURL(blob)
        //     aLink.setAttribute('download', '汇总数据' + new Date().toLocaleString() + '.xlsx')
        //     aLink.click()
        // },
        async getList(type) {
            // if (type == 'search') {
            //   this.ListInfo.currentPage = 1
            //   this.$refs.pager.setPage(1)
            // }
            if (this.timeRanges && this.timeRanges.length > 0) {
              this.ListInfo.monthDate = this.timeRanges.join(',')
            }
            this.loading = true
            const { data, success } = await performanceDonationPage(this.ListInfo)
            if (success) {
                //   data.list.map((row)=>{
                //     row.twoYearRate =  row.twoYearRate === 100 ? "100%" : row.twoYearRate ? Number(row.twoYearRate).toFixed(2) + "%" : ''
                //     row.notAdaptedWorkRate =  row.notAdaptedWorkRate === 100 ? "100%" : row.notAdaptedWorkRate ? Number(row.notAdaptedWorkRate).toFixed(2) + "%" : ''
                //     row.hasWorkRate =  row.hasWorkRate === 100 ? "100%" : row.hasWorkRate ? Number(row.hasWorkRate).toFixed(2) + "%" : ''

                //     row.familyReasonsRate =  row.familyReasonsRate === 100 ? "100%" : row.familyReasonsRate ? Number(row.familyReasonsRate).toFixed(2) + "%" : ''
                //     row.bodyReasonsRate =  row.bodyReasonsRate === 100 ? "100%" : row.bodyReasonsRate ? Number(row.bodyReasonsRate).toFixed(2) + "%" : ''
                //     row.eliminateRate =  row.eliminateRate === 100 ? "100%" : row.eliminateRate ? Number(row.eliminateRate).toFixed(2) + "%" : ''
                //   })
                this.tableData = data.list
                //   this.total = data.total
                //   this.summaryarry = data.summary
                data.summary.regionName = '合计';
                data.summary.calculateMonth = data.list.length > 0 ? data.list[0].calculateMonth : '';
                // this.timeCundang = data.list.length>0?data.list[0].archiveTime:''
                if (data.summary) {
                    this.timeCundang = data.summary.archiveTime
                }

                let zhanbi = ['eliminateRate', 'bodyReasonsRate', 'familyReasonsRate', 'hasWorkRate', 'notAdaptedWorkRate'];

                zhanbi.map((item) => {
                    data.summary[item] = data.summary[item] ? data.summary[item] + '%' : ''
                })

                this.footerData = data.summary
                const fieldsToFormat = [
                    "ywCost",
                    "whCost",
                    "totalCount",
                    "szCost",
                    "ncCost",
                ];
                this.footerData.forEach((item) => {
                    fieldsToFormat.forEach((field) => {
                        if (item[field] !== null && item['ledgerDate'] == '区域总占比') {
                            item[field] = item[field] + '%'
                            return;
                        }
                        if (item[field] !== null && item[field] !== undefined) {
                            item[field] = this.formatNumberWithThousandSeparator(item[field]);
                        }
                    });
                    // zhanbi.indexOf()

                });
                console.log("111111", this.footerData)
                //

                //取列表中的区域
                const newDistricts = this.tableData.map(item => item.costType).filter(district => district !== undefined && district !== null && district.indexOf('小计') == -1 && district.indexOf('占比') == -1)
                this.districtList = Array.from(new Set([...this.districtList, ...newDistricts]));

                this.loading = false
            } else {
                this.loading = false
                this.$message.error('获取列表失败')
            }
        },
        formatNumberWithThousandSeparator(value) {
            if (value === null || value === undefined) return value;
            return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 150px;
        margin-right: 5px;
    }
}

:deep(.vxe-header--column) {
    background: #00937e;
    color: white;
    font-weight: 600;
}

:deep(.vxe-footer--row) {
    background: #00937e;
    color: white;
    font-weight: 600;
}

:deep(.row-green) {
    background-color: rgb(247, 230, 193);
    // color: #fff;
}
</style>
