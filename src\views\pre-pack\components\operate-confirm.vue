<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <inputYunhan ref="productCode" :inputt.sync="query.goodsCode" v-model="query.goodsCode" width="200px"
          placeholder="组合编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
          @callback="goodsCodeCallback" title="组合编码" style="margin: 0 10px 5px 0;">
        </inputYunhan>
        <!-- <el-input v-model.trim="query.goodsCode" placeholder="组合编码" maxlength="50" clearable class="publicCss" /> -->
        <el-input v-model.trim="query.goodsName" placeholder="名称" maxlength="50" clearable class="publicCss" />
        <inputYunhan ref="productCode" :inputt.sync="query.entityCode" v-model="query.entityCode" width="200px"
          placeholder="实体编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
          @callback="entityCodeCallback" title="实体编码" style="margin: 0 10px 5px 0;">
        </inputYunhan>
        <!-- <el-input v-model.trim="query.entityCode" placeholder="实体编码" maxlength="50" clearable class="publicCss" /> -->
        <el-input v-model.trim="query.groupName" placeholder="小组名称" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="query.operateSpecialName" placeholder="运营" maxlength="50" clearable class="publicCss" />
        <el-select v-model="query.confirmResult" placeholder="状态" class="publicCss" clearable>
          <el-option label="未确认" :value="0" />
          <el-option label="确认" :value="1" />
          <el-option label="拒绝" :value="2" />
        </el-select>
        <div>
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" :disabled="isExport" @click="exportProps">导出</el-button>
          <el-button type="primary" @click="bahchClick(1)">批量确认</el-button>
          <el-button type="primary" @click="bahchClick(2)">批量拒绝</el-button>
        </div>
      </div>
    </template>
    <vxetablebase :id="'operate-confirm202408041851'" ref="table" v-loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
      :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
      :is-select-column="false" :is-index-fixed="false" style="width: 100%;  margin: 0" :height="'100%'"
      :showsummary="data.summary ? true : false" :summaryarry="data.summary" @sortchange="sortchange"
      @select="checkboxRangeEnd" @onTrendChart="trendChart">
      <template #confirmResultStr="{ row }">{{ row.confirmResultStr }}
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false" direction="btt">
      <div v-if="!chatProp.chatLoading">
        <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="margin: 10px;" @change="
            trendChart({
              ...chatPropOption,
              startDate: $event[0],
              endDate: $event[1],
            })
            " />
        <buschar v-if="!chatProp.chatLoading" :analysis-data="chatProp.data" />
      </div>
      <div v-else v-loading="chatProp.chatLoading" />
    </el-drawer>
  </MyContainer>
</template>
<script>
import MyContainer from '@/components/my-container'
import vxetablebase from '@/components/VxeTable/yh_vxetable.vue'
import day7 from './day7.vue'
import dayjs from 'dayjs'
import { formatLinkProCode, pickerOptions } from '@/utils/tools'
import { download } from '@/utils/download'
import buschar from '@/components/Bus/buschar'
import inputYunhan from "@/components/Comm/inputYunhan";
import {
  getColumns_ProPrePack, pageGetData_ProPrePack, exportData_ProPrePack, confirmResult, getTrendChart_ProPrePack
} from '@/api/vo/operateConfirm'

export default {
  name: 'ScanCodePage',
  components: {
    MyContainer, vxetablebase, day7, buschar, inputYunhan
  },
  data() {
    return {
      chatPropOption: {},
      pickerOptions,
      rules: {
      },
      that: this,
      query: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startDate: '',
        endDate: ''
      },
      data: { total: 0, list: [], summary: {} },
      tableCols: [],
      loading: false,
      isExport: false,
      ruleForm: {},
      batchSetInvtoryVisable: false,
      chooseList: [],
      logVisible: false,
      logTableCols: [],
      logData: [],
      chatProp: {
        chatDialog: false, // 趋势图弹窗
        chatTime: null, // 趋势图时间
        chatLoading: true, // 趋势图loading
        data: []// 趋势图数据
      },
    }
  },
  async mounted() {
    await this.getCol()
    this.getList()
  },
  methods: {
    entityCodeCallback(val) {
      this.query.entityCode = val
    },
    goodsCodeCallback(val) {
      this.query.goodsCode = val
    },
    async trendChart(option) {
      var endDate = null;
      var startDate = null;

      if (option.startDate && option.endDate) {
        startDate = option.startDate;
        endDate = option.endDate;
      } else {
        endDate = option.date;
        startDate = new Date(option.date);
        startDate.setDate(option.date.getDate() - 30);

        startDate = dayjs(startDate).format("YYYY-MM-DD");
        endDate = dayjs(endDate).format("YYYY-MM-DD");
      }
      option.filter.filters = option.filter.filters.filter((item) => item.field !== option.dateField);
      option.filter.filters.push({
        field: option.dateField,
        operator: "GreaterThanOrEqual",
        value: startDate,
      });
      option.filter.filters.push({
        field: option.dateField,
        operator: "LessThanOrEqual",
        value: endDate,
      });

      option.startDate = startDate;
      option.endDate = endDate;

      this.chatProp.chatTime = [startDate, endDate];

      this.chatProp.chatLoading = true;

      const { data, success } = await getTrendChart(option);
      if (success) {
        this.chatProp.data = data;
      }

      this.chatProp.chatLoading = false;
      this.chatProp.chatDialog = true;

      this.chatPropOption = option;
    },
    checkboxRangeEnd(row) {
      this.chooseList = row.map(item => item.id)
    },
    bahchClick(cResult) {
      if (this.chooseList.length === 0) return this.$message.error('请选择数据')
      this.$confirm('此操作将进行批量操作, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await confirmResult({ ids: this.chooseList, confirmResult: cResult })
        if (success) {
          this.$message.success('操作成功')
          this.getList()
        } else {
          this.$message.error('操作失败')
        }
      }).catch((error) => {
        console.log(error)
        this.$message({
          type: 'info',
          message: '操作失败'
        })
      })
    },
    async getCol() {
      const { data, success } = await getColumns_ProPrePack()
      if (success) {
        // 在data顶部添加一列
        data.unshift({
          label: '',
          type: 'checkbox'
        })
        data.forEach(item => {
          if (item.prop == 'proCode') {
            item.type = 'html'
            item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
          }
        })
        this.tableCols = data
        this.query.summarys = data.filter(a => a.summaryType).map(a => { return { column: a['sort-by'], summaryType: a.summaryType } })
      }
    },
    // 导出数据,这里前端可以封装一个方法
    async exportProps() {
      this.isExport = true
      await exportData_ProPrePack(this.query).then(download).finally(() => {
        this.isExport = false
      })
    },
    async getList(type) {
      if (type === 'search') {
        this.query.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      // 使用时将下面的方法替换成自己的接口
      try {
        const { data, success } = await pageGetData_ProPrePack(this.query)
        if (success) {
          this.data = data
        } else {
          this.$message.error('获取列表失败')
        }
      } catch (error) {
        this.$message.error('获取列表失败')
      } finally {
        this.loading = false
      }
    },
    // 每页数量改变
    Sizechange(val) {
      this.query.currentPage = 1
      this.query.pageSize = val
      this.getList()
    },
    // 当前页改变
    Pagechange(val) {
      this.query.currentPage = val
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.query.orderBy = prop
        this.query.isAsc = order.indexOf('descending') === -1
        this.getList()
      }
    }
  }
}
</script>
<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
  flex-wrap: wrap;

  .publicCss {
    width: 190px;
    margin: 0 10px 5px 0;
  }
}
</style>
