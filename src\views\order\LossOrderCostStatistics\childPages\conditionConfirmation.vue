<template>
  <MyContainer>
    <template #header>
      <div style="font-size: 15px;margin-bottom: 15px;font-weight: bold"><span v-show="shippingWarehouse">情况确认--- {{
        shippingWarehouse }}</span> <span v-show="serialCoding">系列编码: {{ serialCoding }}</span></div>
      <div class="top">
        <span style="font-size: 15px;margin-right: 10px;display: flex;align-items: center;">操作人:</span>
        <el-input v-model.trim="createdUserName" placeholder="操作人" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">查询</el-button>
      </div>
    </template>
    <vxetablebase :id="'conditionConfirmation202408041759'" ref="table" :that='that' :toolbarshow="false" :isIndex='true' :hasexpand='true' :tablefixed='true'
      @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
      <template #pictureUrls="{ row }">
        <div style="position: relative; display: inline-block;" v-if="row.pictureUrls">
          <el-image class="custom-image" slot="reference" :src="row.pictureUrls[0] || ''" fit="fill"
            :preview-src-list="row.pictureUrls != '' ? row.pictureUrls : ''" style="width: 40px; height: 38px;">
          </el-image>
          <span class="circle-badge">
            {{ row.pictureUrls.length }}
          </span>
        </div>
      </template>
    </vxetablebase>

    <el-dialog title="情况确认及说明" :visible.sync="confirmationExplanation" width='60%' v-dialogDrag append-to-body>
      <div style="height: 250px;padding: 3% 2%;">
        <el-form :model="condition" :rules="rules" ref="condition" label-width="100px" class="demo-ruleForm">
          <el-form-item label="情况说明: " prop="explainMsg">
            <el-input type="textarea" placeholder="请输入内容" v-model="condition.explainMsg" maxlength="100" show-word-limit
              :autosize="{ minRows: 10, maxRows: 20 }">
            </el-input>
          </el-form-item>
          <el-form-item label="图片: " prop="pictureUrls">
            <div class="chatPicUrl">
              <uploadimgFile ref="uploadimgFile" :disabled="isView" :ispaste="!isView" v-if="uploadimgFileVisable"
                :accepttyes="accepttyes" :isImage="true" :uploadInfo="condition.pictureUrls" :keys="[1, 1]"
                @callback="quotationVoucherGet" :imgmaxsize="9" :limit="9" :multiple="true">
              </uploadimgFile>
              <span class="picTips" v-if="!isView">提示:点击灰框可直接贴图,最多可上传9张！！！</span>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="confirmationExplanation = false">取消</el-button>
        <el-button type="primary" @click="onSaveClose">保存关闭</el-button>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import { getDamagedOrderExplainAsync, saveDamagedOrderExplain } from '@/api/customerservice/DamagedOrders'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";

import dayjs from 'dayjs'
const tableCols = [
  { width: 'auto', align: 'center', prop: 'managerMemberName', label: '责任上级', },
  { width: 'auto', align: 'center', prop: 'sendOrderCount', label: '发货订单数', },
  { width: 'auto', align: 'center', prop: 'damagedOrderCount', label: '损耗订单数', },
  { width: 'auto', align: 'center', prop: 'damagedGoodsCount', label: '损耗商品数', },
  { width: 'auto', align: 'center', prop: 'damagedOrderRatio', label: '损耗订单比', formatter: (row) => !row.damagedOrderRatio ? " " : row.damagedOrderRatio + '%' },
  { width: 'auto', align: 'center', prop: 'explainMsg', label: '情况说明', },
  { width: 'auto', align: 'center', prop: 'pictureUrls', label: '图片', },
  { width: 'auto', align: 'center', prop: 'createdUserName', label: '操作人', },
  { width: '120', align: 'center', prop: 'createdTime', label: '操作时间', },
  {
    istrue: true, label: '操作', width: 'auto', fixed: 'right', type: 'button', btnList: [
      { label: '情况确认及说明', handle: (that, row) => that.onRecord(row) }
    ]
  }
]
export default {
  name: "conditionConfirmation",
  components: {
    MyContainer, vxetablebase, uploadimgFile
  },
  props: {
    conditionData: { type: Object, default: () => { return {} } },
    query: { type: Object, default: () => { return {} } },
  },
  data() {
    return {
      statement: {},
      accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
      isView: false,
      uploadimgFileVisable: false,
      rules: {
        explainMsg: [
          { required: true, message: '请输入内容', trigger: 'blur' },
        ],
        pictureUrls: [
          { required: true, message: '请上传运费凭证', trigger: 'change' }
        ],
      },
      filter: {},
      condition: {
        explainMsg: null,
        pictureUrls: [],
      },
      serialCoding: null,
      shippingWarehouse: null,
      confirmationExplanation: false,
      that: this,
      createdUserName: null,
      ListInfo: {},
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    this.ListInfo = this.query
    this.filter = this.conditionData
    this.shippingWarehouse = this.conditionData.sendWareHouse
    this.serialCoding = this.conditionData.styleCode
    await this.getList()
    this.uploadimgFileVisable = true
  },
  methods: {
    async onSaveClose() {
      let verify = false
      if (!this.condition.explainMsg || this.condition.pictureUrls.length == 0) {
        this.$message.warning('请填写必填数据!')
        verify = true
      }
      if (verify) {
        return
      }
      if (this.statement.damagedOrderRatio) {
        this.statement.damagedOrderRatio = Number(this.statement.damagedOrderRatio)
      }
      const params = {
        ...this.statement,
        ...this.condition,
      }
      const { success } = await saveDamagedOrderExplain(params)
      if (success) {
        this.$message({ message: '操作成功', type: "success" });
        this.confirmationExplanation = false
        await this.getList()
      }
    },
    quotationVoucherGet(data) {
      if (data) {
        this.condition.pictureUrls = []
        data.forEach(item => {
          if (item.url) {
            this.condition.pictureUrls.push(item.url);
          }
        });
      }
    },
    onRecord(row) {
      this.statement = { ...row }
      this.condition.explainMsg = null
      this.condition.pictureUrls = []
      this.$nextTick(() => {
        this.$refs.uploadimgFile.setData([]);
      })
      this.confirmationExplanation = true
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
      }
      this.loading = true
      const { data, success } = await getDamagedOrderExplainAsync({ ...this.ListInfo, createdUserName: this.createdUserName })
      if (success) {
        this.tableData = data
        this.total = data
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

.chatPicUrl {
  position: relative;

  .picTips {
    position: absolute;
    top: 0;
    left: 150px;
    color: #ff0000;
    font-size: 16px;
  }
}

::v-deep .custom-image img {
  max-width: 40px !important;
  max-height: 40px !important;
}

.circle-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: red;
  color: white;
  font-size: 12px;
  width: 13px;
  height: 13px;
  line-height: 13px;
  text-align: center;
  border-radius: 50%;
}
</style>
