<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="统计时间:">
            <el-date-picker
                v-model="filter.timerange"
                type="datetimerange"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
           ></el-date-picker>
        </el-form-item>
        <el-form-item label="分类名称:">
          <el-input v-model="filter.brandName" placeholder="品牌名称"/>
        </el-form-item>
        <el-form-item label="平台:">
          <el-select v-model="filter.Platform" placeholder="请选择平台">
            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="所属店铺:">
          <el-select v-model="filter.shopid" placeholder="请选择店铺" style="width: 100%">
            <el-option label="所有" value></el-option>  
            <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.id"/>
          </el-select>
        </el-form-item>
         <el-form-item label="状态:">
          <el-select v-model="filter.enabled" placeholder="请选择状态" style="width: 100%">
            <el-option label="所有" value></el-option>
            <el-option label="是" value='true'></el-option>
            <el-option label="否" value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>

     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='false'
              :loading="listLoading">
    </ces-table>
    
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getlist"/>
    </template>

    <el-drawer
      :title="formtitle"
      :modal="false"
      :wrapper-closable="true"
      :modal-append-to-body="false"
      :visible.sync="addFormVisible"
      direction="btt"
      size="'auto'"
      class="el-drawer__wrapper"
      style="position:absolute;"
    >
    <form-create ref="formcreate" :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
      <div class="drawer-footer">
        <el-button @click.native="addFormVisible = false">取消</el-button>
        <my-confirm-button type="submit"  :loading="addLoading" @click="onAddSubmit" />
      </div>
    </el-drawer>
  </my-container>
</template>

<script>
import { addOrUpdateShopServiceFee, deleteShopServiceFee,getShopServiceFeeById,getShopServiceFeePageList} from '@/api/operatemanage/base/shop'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatYesornoBool,formatPlatform,platformlist} from "@/utils/tools";
import { rulePlatform,ruleShop} from '@/utils/formruletools'
const tableCols =[
      {istrue:true,prop:'id',label:'编号', width:'180',sortable:'custom'},
      {istrue:true,prop:'tjTime',label:'统计日期', width:'150',sortable:'custom'},
      {istrue:true,prop:'platform',label:'平台', width:'100',formatter:(row)=>formatPlatform(row.platform)},
      {istrue:true,prop:'shopName',label:'店铺名称', width:'120',sortable:'custom'},
      {istrue:true,prop:'servicePer',label:'服务费比率(%)', width:'150',sortable:'custom'},
      {istrue:true,prop:'enabled',label:'状态', width:'80',sortable:'custom',formatter:(row)=>formatYesornoBool(row.enabled)},
      {istrue:true,prop:'createdTime',label:'更新时间', width:'150',sortable:'custom'},
      {istrue:true,prop:'reMark',label:'备注', width:'200'},
      {istrue:true,type:'button',btnList:[{label:"编辑",handle:(that,row)=>that.onEdit(row)},{label:"删除",handle:(that,row)=>that.onDelete(row)}]}
     ];
const tableHandles1=[
        {label:"新增", handle:(that)=>that.onAdd()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: { 
        name: ''
      },
      list: [],
      shopList:[],
      platformlist:platformlist,
      pager:{OrderBy:"id",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      autoform:{
               fApi:{},
               rule:[],
               option:{submitBtn:false,global: {'*': {props: {  disabled: true },col: { span: 8 }}}}
        },
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,     
      deleteLoading: false,
      formtitle:"新增",
    }
  },
  mounted() {
    this.getlist()
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async getDirectorlist() {
      const res3 = await getshopList({})
      this.shopList=res3.data
    },
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {
        ...pager,
        ... this.filter
      }
      this.listLoading = true
      const res = await getShopServiceFeePageList(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    async updateruleshop(platformid) {
        await this.autoform.fApi.setValue({shopId:''})
        await this.autoform.fApi.updateRule('shopId',{... await ruleShop(platformid)})
        await this.autoform.fApi.sync('shopId')
    },
    async onEdit(row) {
        var arr = Object.keys(this.autoform.fApi);
        if(arr.length ==0)
              await this.onAdd()
      this.formtitle='编辑';
      this.addFormVisible = true
      const res = await getShopServiceFeeById(row.id )
      if (res.data&&res.data.platform==0) res.data.platform=null
      await this.autoform.fApi.setValue(res.data)
    },
   async onAdd() {
      this.formtitle='新增';
      this.addFormVisible = true
      let that=this;
      this.autoform.rule=[{type:'hidden',field:'id',title:'id',value: '0'},
                      {type:'select',field:'platform',title:'平台', value: null, update(val, rule){ if (val) {that.updateruleshop(val)}}, ...await rulePlatform()},
                      {type:'select',field:'shopId',title:'所属店铺', value: ''},
                      {type:'InputNumber',field:'servicePer',title:'服务费比率(%)'},
                      {type:'DatePicker',field:'tjTime',title:'统计时间',validate: [{type: 'string', required: true, message:'请输入统计时间'}]},
                      //{type:'select',field:'enabled',title:'状态', value: null, ...await formatYesornoBool()},
                      {type:'input',field:'reMark',title:'备注',props:{type:'textarea'}}]
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
          this.autoform.fApi.reload()
    },
    async onEditSubmit() {
      this.addFormVisible = true
      await onAddSubmit();
    },
    async onAddSubmit() {
       this.addFormVisible=true;
       this.addLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData()
          const res = await addOrUpdateShopServiceFee(formData)
          this.getlist()
          console.log(formData)
          this.addFormVisible=false;
        }else{
          //todo 表单验证未通过
        }
     })
      this.addLoading=false;
    },   
    async onDelete(row) {
      row._loading = true
      this.$confirm('确认删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
            const res = await deleteShopServiceFee({id:row.id})
            row._loading = false
            if (!res?.success) {return }
            this.$message({
                type: 'success',
                message: '删除成功!'
            });
            this.getlist()
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
           row._loading = false
        });
    },
    selsChange: function(sels) {
      this.sels = sels
    }
  }
}
</script>
