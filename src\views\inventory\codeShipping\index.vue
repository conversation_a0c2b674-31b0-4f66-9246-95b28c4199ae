<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 95%;">
            <el-tab-pane label="包邮" name="first" style="height: 100%;" lazy>
                <freeShipping />
            </el-tab-pane>
            <el-tab-pane label="寄付" name="second" style="height: 100%;" lazy>
                <delivery />
            </el-tab-pane>
            <el-tab-pane label="到付" name="third" style="height: 100%;" lazy>
                <collect />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import collect from './component/collect.vue';
import delivery from './component/delivery.vue';
import freeShipping from './component/freeShipping.vue';

export default {
    components: {
        MyContainer, freeShipping, delivery, collect
    },
    data() {
        return {
            activeName: 'first'
        };
    },
    methods: {

    }
};
</script>

<style lang="scss" scoped></style>