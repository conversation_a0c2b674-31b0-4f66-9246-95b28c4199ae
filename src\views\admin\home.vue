<template>
  <my-container >
    <component :is="currentRole"></component>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import HomeiIndex from "./homecomponents/home-index.vue"
import EditorHomeIndex from "./homecomponents/editor-home-index.vue"
import { mapGetters } from 'vuex'
import { getTokenKeyValue } from '@/api/admin/auth'
import  checkPermission  from '@/utils/permission'

export default {
  name: 'Welcome',
  components: {MyContainer,HomeiIndex,EditorHomeIndex},
  data() {
    return {
      currentRole: '',
    }
  },
  // computed: {
  //   ...mapGetters([
  //     'token',
  //   ]),
  // },
  async created() {
    
    var param = {key: 'supadmin'}
    var res =await getTokenKeyValue(param)
    if (res?.data == 'True' ||  checkPermission('homepermission')){
      this.currentRole = 'HomeiIndex'
    }else{
      this.currentRole = 'EditorHomeIndex'
    }  
  },
  async mounted() {
    
  },
  methods:{
    
  },
  
  }
</script>

<style lang="scss" scoped>

</style>
