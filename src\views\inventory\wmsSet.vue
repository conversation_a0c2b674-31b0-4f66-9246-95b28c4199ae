<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select v-model="ListInfo.warehouseName" clearable filterable placeholder="请选择仓库" class="publicCss">
                    <el-option v-for="item in warehouselist" :key="item.name" :label="item.name" :value="item.name" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="addProps">新增一行</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' border
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;height: 400px; margin: 0" :loading="loading">
            <template #warehouseName="{ row, index }">
                <el-select v-model="row.wms_co_id" clearable filterable placeholder="请选择仓库" v-if="row.isEdit"
                    @change="changeRowWms($event, index)">
                    <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                        :value="item.wms_co_id" />
                </el-select>
                <span v-else>{{ row.warehouseName }}</span>
            </template>
            <template #adress="{ row }">
                <el-input v-model.trim="row.adress" v-if="row.isEdit" clearable maxlength="50" style="width:150px"
                    placeholder="请输入地址" />
                <span v-else>{{ row.adress }}</span>
            </template>
            <template #fullName="{ row }">
                <el-input v-model.trim="row.fullName" v-if="row.isEdit" clearable maxlength="50" style="width:150px"
                    placeholder="请输入收件人" />
                <span v-else>{{ row.fullName }}</span>
            </template>
            <template #phone="{ row }">
                <el-input v-model.trim="row.phone" v-if="row.isEdit" clearable maxlength="20" style="width:150px"
                    placeholder="请输入手机号" />
                <span v-else>{{ row.phone }}</span>
            </template>
            <template slot="right">
                <vxe-column title="操作" width="120" align="center">
                    <template #default="{ row, $rowIndex }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="row.isEdit = !row.isEdit" v-if="!row.isEdit">编辑</el-button>
                            <el-button type="text" @click="catchEdit(row)" v-else>取消</el-button>
                            <el-button type="text" @click="handleSave(row, $rowIndex)" v-if="row.isEdit">保存</el-button>
                            <el-button type="text" @click="handleDelete(row, $rowIndex)">删除</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { getAllProBrand, getAllWarehouse, getWarehouseReceiveAddressSets, saveWarehouseReceiveAddressSet, deleteWarehouseReceiveAddressSetById } from '@/api/inventory/warehouse'
import dateRange from "@/components/date-range/index.vue";
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouseName', label: '仓库', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'adress', label: '地址', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'fullName', label: '收件人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'phone', label: '联系电话', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            warehouselist: [],
            historyList: [],
        }
    },
    async mounted() {
        this.init()
        await this.getList()
    },
    methods: {
        catchEdit(row) {
            row.isEdit = !row.isEdit
            if (row.id) {
                const history = this.historyList.find(x => x.id == row.id)
                for (const key in row) {
                    this.$set(row, key, history[key])
                }
            }
        },
        changeRowWms(val, index) {
            this.tableData[index].warehouseName = this.warehouselist.find(x => x.wms_co_id == val).name
        },
        handleDelete(row, index) {
            this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                if (row.id) {
                    const { success } = await deleteWarehouseReceiveAddressSetById({ id: row.id })
                    if (success) {
                        this.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                        this.getList()
                    }
                } else {
                    this.tableData.splice(index, 1)
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        async handleSave(row, index) {
            console.log(index, 'index');
            const map = {
                wms_co_id: '仓库',
                adress: '地址',
                fullName: '收件人',
                phone: '联系电话',
            }
            for (const key in map) {
                if (!row[key]) {
                    this.$message.error(`第${index + 1}行${map[key]}不能为空`)
                    return
                }
            }
            const { success } = await saveWarehouseReceiveAddressSet(row)
            if (success) {
                this.$message.success('保存成功')
                row.isEdit = false
                this.getList()
            }
        },
        addProps() {
            this.tableData.push({
                warehouseName: '',
                adress: '',
                fullName: '',
                phone: '',
                isEdit: true,
                wms_co_id: ''
            })
        },
        async init() {
            var res3 = await getAllWarehouse();
            this.warehouselist = res3.data.filter((x) => x.name.indexOf('代发') < 0);
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            const { data, success } = await getWarehouseReceiveAddressSets(this.ListInfo)
            console.log(data, 'data');
            if (success) {
                data.list.forEach(item => {
                    item.isEdit = false
                })
                this.tableData = data.list
                this.historyList = JSON.parse(JSON.stringify(data.list))
                this.total = data.total
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
            this.loading = false
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
