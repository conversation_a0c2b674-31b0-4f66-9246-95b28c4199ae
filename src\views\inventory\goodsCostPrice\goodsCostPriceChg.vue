<template>
    <container v-loading="pageLoading">
        <!--列表-->
        <vxetablebase :id="'goodsCostPriceChgList202301031318001'" :tableData='list' :tableCols='tableCols'
            :tree-config="{}" :loading='listLoading' :border='true' :tableHandles="tableHandles" :that="that"
            ref="vxetable" @sortchange='sortchange'>
            <template slot="right">
                <vxe-column title="操作" :field="'col_opratorcol'" width="220" fixed="right">
                    <template #default="{ row }">
                        <div v-if="!(row.isAllot == true && row.priceChanged == false)">
                            <template width="auto" v-if="row.isRoot">
                                <el-button type="text" size="default" @click="onHand(row, 1)"
                                    v-if="isView(row.checkDate)">编辑</el-button>
                                <el-button type="text" size="default" @click="showLog(row)">日志</el-button>
                            </template>
                            <template width="auto" v-else>
                                <el-button type="text" :loading="onHandLoading" :disabled="row.isApproving"
                                    size="default" @click="onHand(row, 2)">
                                    {{ !row.isApproving ? '领取' : '领取中' }}
                                </el-button>
                                <el-button type="text" size="default"
                                    v-if="checkPermission('api:inventory:BasicGoods:DelCostPriceState')"
                                    @click="onDel(row)">删除</el-button>
                            </template>
                        </div>
                    </template>
                </vxe-column>
            </template>
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="margin: 0;">
                        {{ lastUpdateTime }}
                    </el-button>
                </el-button-group>
            </template>
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-drawer title="核价数据" :visible.sync="dialogAddVisible" :before-close="handleClose" direction="rtl"
            custom-class="demo-drawer" ref="drawer">
            <div class="demo-drawer__content">
                <div style="height: 15%;"></div>
                <el-descriptions direction="horizontal" size="medium" :column="1" border>
                    <el-descriptions-item label="采购单号">{{ addForm.buyNo }}</el-descriptions-item>
                    <el-descriptions-item label="Erp单号">{{ addForm.indexNo }}</el-descriptions-item>
                    <el-descriptions-item label="变更时间">{{ addForm.checkDate }}</el-descriptions-item>
                    <el-descriptions-item label="上次价格">
                        <template>
                            <span>
                                <vxe-input v-model="addForm.prevPrice" size="mini" :disabled="onFinishLoading"
                                    :digits="4" :min="0" :max="99999" type="float"></vxe-input>
                                <!-- <el-input-number v-model="addForm.prevPrice" :disabled="onFinishLoading" :step="0.0001" :max="99999" :precision="4" :controls="false"></el-input-number> -->
                            </span>
                        </template>
                    </el-descriptions-item>
                    <el-descriptions-item label="新价格">
                        <template>
                            <span>
                                <vxe-input v-model="addForm.price" size="mini" :disabled="onFinishLoading" :digits="4"
                                    :min="0" :max="99999" type="float"></vxe-input>
                                <!-- <el-input-number v-model="addForm.price" :disabled="onFinishLoading" :step="0.0001" :precision="4"  :max="99999" :controls="false"></el-input-number> -->
                            </span>
                        </template>
                    </el-descriptions-item>
                    <el-descriptions-item label="核价人员">
                        {{ addForm.brandName }}
                    </el-descriptions-item>
                </el-descriptions>
            </div>
            <div class="demo-drawer__footer">
                <span class="dialog-footer">
                    <el-button @click="dialogAddVisible = false">取 消</el-button>
                    <!-- <my-confirm-button type="submit" :loading="onFinishLoading" @click="onFinish()">
                    </my-confirm-button> -->
                    <el-button type="primary" @click="nuclearPriceVisiable = true">提交</el-button>
                </span>
            </div>

        </el-drawer>

        <!-- 编辑手动采购单 -->
        <el-dialog title="领取核价信息" :visible.sync="dialogAppVisible" width='60%' height='500px'>
            <el-form ref="appForm" :model="appForm" :rules="appFormRules" label-width="100px">
                <el-row>
                    <el-form-item prop="reMark" label="采购单备注">
                        <el-input type="textarea" ref="supplier_id" :rows="2" placeholder="请输入内容"
                            @input="changetextarea($event)" maxlength="200" v-model="appForm.reMark"></el-input>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item prop="purImageUrl" label="图片">
                            <yh-img-upload :value.sync="appForm.purImageUrl" ref="supplier_id"
                                :limit="4"></yh-img-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogAppVisible = false">取 消</el-button>
                    <my-confirm-button type="submit" :validate="finishappFormValidate" :loading="onFinishappLoading"
                        @click="onappFinish()">
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="核价数据确认" :visible.sync="nuclearPriceVisiable" width="30%" v-dialogDrag>
            <div style="text-align: center;">请确认核价编辑 <span style="color: red;">{{ addForm.prevPrice >
                addForm.price ? '降价区间' : '涨价区间'
                    }}</span> 是否为{{ addForm.prevPrice }}至{{ addForm.price }}</div>
            <div class="btnBox">
                <el-button @click="nuclearPriceVisiable = false" style="margin-right: 20px;">取消</el-button>
                <el-button type="primary" @click="onFinish">确定</el-button>
            </div>
        </el-dialog>

        <vxe-modal v-model="dialogpurlogVisible" title="操作日志" :width="1000" :height="550"
            esc-closable="true" show-zoom resize maskClosable :zIndex="99">
            <template  #default>
            <goodscostchginfolog :filter="filterLog" ref="goodscostchginfolog"></goodscostchginfolog>
            </template>
        </vxe-modal>

    </container>
</template>

<script>
import cesTable from "@/components/Table/table.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { getCostPriceList, exportCostPriceListAsync, updateCostPriceState, BatchAddCostPrice, GetCostPriceByOne, updateCostPriceBrand, delCostPriceState } from "@/api/inventory/basicgoods"
import { nullLiteral } from "@babel/types";
import dayjs from "dayjs";
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
import goodscostchginfolog from "./goodscostchginfolog.vue";
import {
    getChangedPurchaseDataAsync,
    exportChangedPurchaseDataAsync,
    editCostPriceAsync,
    receiveSplit,
    deleteSplit,
    allot
} from '@/api/inventory/purchaseData'

const tableCols = [
    {
        istrue: true, treeNode: true, prop: 'buyNo', label: '采购单号', width: '100', sortable: 'custom',
        //htmlformatter: (row) => {  return `${<i class="el-icon-star-on" style="color: red"></i>}`}
    },
    { istrue: true, prop: 'indexNo', label: 'Erp编号', width: '120', },
    { istrue: true, prop: 'picture', label: '图片', width: '120', type: 'images' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '120', },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: '120', },
    { istrue: true, prop: 'purchaseDate', label: '变动时间', width: '160',sortable: 'custom', },
    { istrue: true, prop: 'prevPrice', label: '原成本', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'price', label: '新成本', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'diffPrice', label: '差价单价', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'count', label: '采购量', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'diffAmount', label: '差价总额', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'brandName', label: '采购员', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'isAllot', label: '是否调货', width: 'auto', sortable: 'custom', type: 'switch', change: (row, that) => that.changeStatus(row) },
    // {
    //     istrue: true, type: 'button', label: '操作', width: '60', btnList: [{
    //         label: "编辑", handle: (that, row) => that.onHand(row)
    //     }]
    // },
];
const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
    { label: "导出", handle: (that) => that.onExport() },
];

export default {
    name: 'YunHanAdminGoodsCostPriceChg',
    components: { cesTable, container, MyConfirmButton, vxetablebase, YhImgUpload, goodscostchginfolog },
    props: {
        filter: {},
        lastUpdateTime: '',
    },

    data() {
        return {
            that: this,
            addForm: {
                id: null,
                checkDate: null,
                buyNo: null,
                indexNo: null,
                parentId: null,
                prevPrice: null,
                price: null,
                brandName: null
            },
            appForm: {
                id: null,
                purImageUrl: null,
                reMark: null,
            },
            appFormRules: {
                purImageUrl: [{ required: true, message: '请上传图片', trigger: 'blur' }],
            },
            filterLog: {
                buyNo: null,
                goodsCode: null
            },
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "checkDate", IsAsc: false },
            summaryarry: {},
            total: 0,
            sels: [],
            pageLoading: false,
            listLoading: false,
            dialogAddVisible: false,
            dialogAppVisible: false,
            onFinishLoading: false,
            onHandLoading: false,
            onFinishappLoading: false,
            dialogpurlogVisible: false,
            addFormRules: {
                price: [{ required: true, message: '请输入金额', trigger: 'blur' }],
            },
            nuclearPriceVisiable: false,
        };
    },

    async mounted() {

    },

    methods: {
        isView(row) {
            const newTime = dayjs().format('YYYY-MM-DD');
            const oldTime = dayjs(row).format('YYYY-MM-DD');
            //如果当前时间减去row小于60天，返回true,使用dayjs
            return dayjs(newTime).diff(dayjs(oldTime), 'day') < 60;
        },
        async onExport() {
            var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
            const params = { ... this.filter }

            if (params === false) {
                return;
            }
            var res = await exportChangedPurchaseDataAsync(params);
            loadingInstance.close();
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '基础数据导出_' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        async getlist() {
            var pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }
            this.listLoading = true
            const res = await getChangedPurchaseDataAsync(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res.data.total
            let dtList = [];
            res.data.list.forEach(x => {
                x.isRoot = true
                dtList.push(x);
                if (x.dtList && x.dtList.length > 0) {
                    dtList = dtList.concat([...x.dtList]);
                }
            })
            this.list = dtList
            this.summaryarry = res.data.summary;
        },
        async clearaddFrom() {
            this.addForm = {
                id: null,
                parentId: null,
                prevPrice: null,
                price: undefined,
                brandName: null
            }
        },
        //新增采购单时提交验证
        finishFormValidate: function () {
            let isValid = false
            this.$refs.addForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        //新增采购单时提交验证
        finishappFormValidate: function () {
            let isValid = false
            this.$refs.appForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        async onFinish() {
            var _this = this;
            _this.onFinishLoading = true;
            const para = _.cloneDeep(this.addForm);
            para.oldCostPrice = Number(this.addForm.prevPrice);
            para.newCostPrice = Number(this.addForm.price);
            console.log(para, 'para');
            var res = await editCostPriceAsync(para);
            if (res?.success) {
                _this.addForm.brandName = res.data
                this.$message({
                    type: 'success',
                    message: '操作成功!'
                });
                this.dialogAddVisible = false;
                this.nuclearPriceVisiable = false;
                await this.onSearch();
            } else {
                this.$message({
                    type: 'info',
                    message: '操作失败，请检查数据！！！'
                });
            }
            _this.onFinishLoading = false;
            //_this.dialogAddVisible = false;
        },
        async changeStatus(row) {
            // if (row.isAllot == false && row.priceChanged==false) {
            //     this.$message({
            //         type: 'info',
            //         message: '平价调拨数据不允许操作!'
            //     });
            //     row.isAllot = !row.isAllot;
            //     return;
            // }
            var _this = this;
            this.$confirm('此操作将改变调货状态, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                _this.listLoading = true
                var res = await allot({ id: row.id, isAllot: row.isAllot, checkDate: row.checkDate, goodsCode: row.goodsCode, buyNo: row.buyNo })
                if (!res?.success) {
                    row.isAllot = !row.isAllot;
                    _this.listLoading = false
                    return;
                }
                this.$message({
                    type: 'success',
                    message: '操作成功!'
                });
                this.onSearch();
            }).catch(() => {
                row.isAllot = !row.isAllot;
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
            _this.listLoading = false
        },
        async onHand(row, num) {
            console.log(row, 'row');
            // 信息编辑
            if (num == 1) {
                await this.clearaddFrom();
                this.addForm.parentId = null;
                this.addForm.id = row.id;
                this.addForm.prevPrice = row.prevPrice;
                this.addForm.price = row.price;
                this.addForm.brandName = row.brandName
                this.addForm.buyNo = row.buyNo;
                this.addForm.indexNo = row.indexNo;
                this.addForm.checkDate = row.checkDate;

                this.dialogAddVisible = true;
            } else if (num == 2) {  // 领取
                this.appForm.id = row.id;
                // var param = { id: row.id };
                // this.onHandLoading = true;
                // var res = await updateCostPriceBrand(param)
                // this.onHandLoading = false;
                // if (res.success) {
                //     this.$message.success("申请成功");
                //     this.getlist()
                // } else { }
                this.appForm.purImageUrl = null;
                this.appForm.reMark = null;
                this.dialogAppVisible = true;
            }
        },
        async onDel(row) {
            // var param = { id: row.id, parentId: row.parentId };
            this.listLoading = true
            var res = await deleteSplit(row.id);
            if (res.success) {
                this.$message.success("操作成功");
                this.getlist()
            } else { }
            this.listLoading = false;
        },
        async showLog(row) {
            this.filterLog = { goodsCode: row.goodsCode, buyNo: row.buyNo };
            this.dialogpurlogVisible = true;
            this.$nextTick(async () => {
                await this.$refs.goodscostchginfolog.onSearch()
            });
        },
        async onappFinish() {
            this.onFinishappLoading = true;
            var param = { ...this.appForm };
            var res = await receiveSplit(param)
            if (res.success) {
                this.$message.success("申请成功");
                this.getlist()
            } else { }
            this.onFinishappLoading = false;
            this.dialogAppVisible = false;
        },
        async handleClose() {
            this.$confirm('确定要关闭吗, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.dialogAddVisible = false;
                console.log('ok');
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
        },
        async cellclick(row, column, cell, event) {

        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>

<style lang="scss" scoped>
.demo-drawer__content {
    display: flex;
    flex-direction: column;
    height: 95%;
}

.demo-drawer__footer {
    display: flex;
    margin-left: 10%;
}

.btnBox {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
</style>
