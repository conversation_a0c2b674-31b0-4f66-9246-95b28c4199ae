<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' style="height:93%;" :summaryarry="summaryarry" :isIndex='true' :hasexpand='false' @sortchange='sortchange' :tableData='inquirsstatisticslist' @select='selectchange' :isSelection='false' :tableCols='tableCols' :loading="listLoading">
                <template slot='extentbtn'>
                    <el-input v-model="filter.sname" v-model.trim="filter.sname" placeholder="姓名" style="width:120px;" disabled="true" :maxlength="50" />
                    <el-input v-model="filter.startDate" style="width:120px;" disabled="true" />至
                    <el-input v-model="filter.endDate" style="width:120px;" disabled="true" />
                </template>
            </ces-table>
        </template>
    </my-container>
</template>
<script>
    import datepicker from '@/views/customerservice/datepicker'
    import {
        getTaoBaoShouHouShopPersonalEfficiencyList
    } from '@/api/customerservice/taobaoshouhou'
    import cesTable from "@/components/Table/table.vue";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";

    const tableCols = [
        { istrue: true, prop: 'shopName', label: '店铺名称', width: '260', sortable: 'custom' },
        { istrue: true, prop: 'sname', label: '姓名', width: '110', sortable: 'custom' },
        { istrue: true, prop: 'inquirs', label: '咨询人数', width: '120', sortable: 'custom' },
        { istrue: true, prop: 'receiveds', label: '接待人数', width: '120', sortable: 'custom' },
        { istrue: true, prop: 'responseTime', label: '平均响应(秒)', width: '120', sortable: 'custom', formatter: (row) => (row.responseTime).toFixed(2)  },
        { istrue: true, prop: 'salesvol', label: '销售额', width: '140', sortable: 'custom' , formatter: (row) => (row.salesvol).toFixed(2)},
        { istrue: true, prop: 'satisDegree', label: '满意度', width: '100', sortable: 'custom',formatter:(row) => row.satisDegree !== null  ? (row.satisDegree).toFixed(2) +'%' : '0%' },
        { istrue: true, prop: 'satisfaction', label: '客户满意率', width: '110', sortable: 'custom', formatter: (row) => (row.satisfaction).toFixed(2) + "%" },
        { istrue: true, prop: 'lateReceiveds', label: '慢接待人数', width: '110', sortable: 'custom' },
        { istrue: true, prop: 'responseRate', label: '回复率', width: '110', sortable: 'custom', formatter: (row) => (row.responseRate).toFixed(2) + "%" },
        { istrue: true, prop: 'verySatisfied', label: '很满意', width: '110', sortable: 'custom' },
        { istrue: true, prop: 'threeResponseRate', label: '平台3分钟响应率', width: '130', sortable: 'custom', formatter: (row) => (row.threeResponseRate).toFixed(2) + "%" },
    ];
    export default {
        name: "Users",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, datepicker, cesTable },
        data() {
            return {
                that: this,
                filter: {
                    sname: "",
                    sdate: [],
                    endDate: "",
                    startDate: ""
                },
                shopList: [],
                userList: [],
                groupList: [],
                inquirsstatisticslist: [],
                tableCols: tableCols,
                total: 0,
                summaryarry: { count_sum: 10 },
                pager: { OrderBy: "shopName", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                selids: [],
                dialogVisibleSyj: false,
                fileList: [],
            };
        },
        async mounted() {

        },
        created() {

        },
        methods: {
            async dialogOpenAfter(data) {
                this.filter.sdate[0] = data.startDate;
                this.filter.sdate[1] = data.endDate;
                this.filter.startDate = data.startDate;
                this.filter.endDate = data.endDate;
                this.filter.sname = data.sname;
                this.onSearch();
            },
            onSearch() {
                this.getinquirsstatisticsList();
            },
            async getinquirsstatisticsList() {
                const para = { ...this.filter };
                const params = {
                    ...this.pager,
                    ...para,
                };
                this.listLoading = true;
                const res = await getTaoBaoShouHouShopPersonalEfficiencyList(params);
                this.listLoading = false;
                this.total = res.data.total
                this.inquirsstatisticslist = res.data.list;
                this.summaryarry = res.data.summary;
            },
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch();
            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            }
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
