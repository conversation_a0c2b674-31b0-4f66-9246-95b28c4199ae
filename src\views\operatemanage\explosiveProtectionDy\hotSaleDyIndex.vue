<template>
  <MyContainer>
    <el-tabs v-model="activeName" style="height: 95%">
      <el-tab-pane label="爆款保护" name="first" style="height: 100%" lazy>
        <hitProtectionDy :directorGroupList="directorGroupList" :directorlist="directorlist" :shopList="shopList"
          :statsList="statsList" />
      </el-tab-pane>
      <el-tab-pane label="趋势款" name="fifth" style="height: 100%" lazy>
        <trendyStyleDy :directorGroupList="directorGroupList" :directorlist="directorlist" :shopList="shopList"
          :statsList="statsList" />
      </el-tab-pane>
      <el-tab-pane label="爆款流失" name="second" style="height: 100%" lazy>
        <explosiveLossDy :directorGroupList="directorGroupList" :directorlist="directorlist" :shopList="shopList"
          :statsList="statsList" />
      </el-tab-pane>
    </el-tabs>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import hitProtectionDy from "./components/hitProtectionDy.vue";
import trendyStyleDy from "./components/trendyStyleDy.vue";
import explosiveLossDy from "./components/explosiveLossDy.vue";
import { getDirectorGroupList, getList as getshopList, getDirectorList } from '@/api/operatemanage/base/shop'
const statsList = ['爆款', '百货爆款', '百货趋势款', '五金爆款', '五金趋势款', '强季节性爆款', '强季节性趋势款']
export default {
  components: {
    MyContainer, hitProtectionDy, trendyStyleDy, explosiveLossDy
  },
  data() {
    return {
      activeName: 'first',
      directorGroupList: [],
      directorlist: [],
      shopList: [],
      statsList,
    };
  },
  async mounted() {
    await this.init();
  },
  methods: {
    async init() {
      const res2 = await getDirectorGroupList({});
      this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);
      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });
      const res1 = await getshopList({ platform: 6, CurrentPage: 1, PageSize: 100000 });
      this.shopList = res1.data.list.map(item => { return { value: item.shopCode, label: item.shopName }; });
    },
  },
};
</script>

<style lang="scss" scoped></style>
