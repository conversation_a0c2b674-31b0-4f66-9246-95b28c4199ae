<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-button type="primary" @click="getList">刷新</el-button>
        <el-button type="primary" @click="exportList">导出</el-button>
        <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button @click="startImport"
          type="primary" icon="el-icon-share" @command="handleCommand"> 导入
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a">下载模版</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </template>

    <template>
      <vxetablebase :id="'overseasGodownCost202409270425'" :tablekey="'overseasGodownCost202409270425'"
        :tableData='tableData'  :isSelection="true" :tableCols='tableCols'
        @sortchange='sortchange' :loading='loading' :border='true' :that="that" ref="vxetable" :showsummary='true'
        :summaryarry="summaryarry" >
      </vxetablebase>
    </template>

    <template #footer>
      <my-pagination ref="pager" :total="total" @get-page="getList" />
    </template>

    <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
      <span>
        <buschar ref="buschar" v-if="buscharDialog.visible" :analysisData="buscharDialog.data"
          :loading="buscharDialog.loading"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="display: flex; align-items: baseline; height: 75px;margin-top: 10px;">
        <!-- <el-date-picker style="width: 150px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay" type="date"
              placeholder="选择日期" :clearable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
            </el-date-picker> -->
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import { getWarehouseWeightRangePageList, import_WarehouseWeightRange, exportWarehouseWeightRange } from '@/api/bookkeeper/crossBorderV2'
import inputYunhan from "@/components/Comm/inputYunhan";
import { formatWarehouseTypeListKj } from '@/utils/tools'
import buschar from '@/components/Bus/buschar'

const tableCols = [
  {
    istrue: true, sortable: 'custom', prop: 'warehouseType', label: '仓库类型', formatter: (row) => {
      return formatWarehouseTypeListKj(row.warehouseType)
    },
  },
  { istrue: true, sortable: 'custom', prop: 'minWeight', label: '重量区间', formatter: (row) => row.minWeight + "< x ≤ " + row.maxWeight, },
];

export default {
  name: 'overseasGodown',
  components: { vxetablebase, MyContainer, inputYunhan, buschar },
  props: {
    type: {
      type: String,
      required: true
    },
    name: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      that: this,
      queryInfo: {
        timeRanges: [],
        reference_no: null,
        ft_name: null,
        warehouseType: 5,
        page: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: '',
        endTime: ''
      },
      costType: [],
      financeFeeNameType: [],
      tableData: [],
      tableCols: tableCols,
      summaryarry: [],
      loading: false,
      total: 0,
      buscharDialog: { visible: false, title: "", data: {}, loading: false },
      deleteDialogVisible: false,
      dateRange: [],
      dialogVisible: false,//导入弹窗
      fileList: [],//上传文件列表
      uploadLoading: false,//上传按钮loading
      fileparm: {},//上传文件参数
      // yearMonthDay: null,//导入日期
      selids: [],
    };
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async getList(type) {
      if (type == 'search') {
        this.queryInfo.page = 1
        this.$refs.pager.setPage(1)
      }
      let page = this.$refs.pager.getPager()
      this.queryInfo.currentPage = page.currentPage
      this.queryInfo.pageSize = page.pageSize
      this.loading = true
      const res = await getWarehouseWeightRangePageList(this.queryInfo)
      this.loading = false
      this.total = res.data.total
      this.tableData = res.data.list;
      this.summaryarry = res.data.summary
    },
    async exportList() {
      var res = await exportWarehouseWeightRange(this.queryInfo);
      if (res?.data.success) {
        this.$message({ message: res.data.msg, type: "success" });
      }
    },


    sortchange({ order, prop }) {
      if (prop) {
        this.queryInfo.orderBy = prop
        this.queryInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async changeTime(e) {
      this.queryInfo.startDate = e ? e[0] : null
      this.queryInfo.endDate = e ? e[1] : null
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      // this.yearMonthDay = null
      this.dialogVisible = true;
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      // form.append("YearMonthDay", dayjs(this.yearMonthDay).format('YYYY-MM-DD'));
      form.append("warehouseType", 5);
      var res = await import_WarehouseWeightRange(form);
      if (res?.success) {
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
        this.uploadLoading = false
        this.dialogVisible = false;
        await this.getList()
      } else {
        this.$message.error('导入失败');
        this.uploadLoading = false;
      }

    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    async handleCommand(command) {
      switch (command) {
        //下载模版
        case 'a':
          await this.downLoadFile()
          break;
      }
    },
    async downLoadFile() {
      window.open("/static/excel/CrossBorderDownloadTemplate/仓库运费重量区间导入模版.xlsx", "_blank");
    },
  }
};
</script>

<style lang="scss" scoped>
.top {
  display: flex;
  margin-bottom: 10px;
}
</style>