<template>
  <my-container v-loading="pageLoading">
    <template #header>
        <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent label-position="right" label-width="90px">       
          <button style="padding: 0;  border: none;">
            <el-select v-model="filter.warehouseId" clearable filterable :collapse-tags="true" placeholder="请选择仓库" style="width: 130px">
                <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"  :value="item.wms_co_id" />
            </el-select> 
          </button>
          <button style="padding: 0;  border: none;">
          <inputYunhan title="商品编码" :row="12" placeholder="商品编码" :inputshow="0" :clearable="true"
            @callback="callback" :maxRows="50" :inputt.sync="filter.goodscodes"></inputYunhan>
          </button>
          <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button> 
          </el-form-item> 
          <el-form-item>
              <el-button type="primary" @click="preGoodsInventoryDialogVisible = true">导入预包编码库存</el-button>
          </el-form-item>
          {{lastTime}} 
        </el-form>
    </template>
 
    <vxetablebase ref="table" :id="'PrepackInventory20240124'" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
      :tableData='list' :tableCols='tableCols'  :border='true'  :tableHandles='tableHandles' :loading="listLoading" :showsummary='true'>  
    </vxetablebase>
    
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>

    <el-dialog title="导入预包编码库存" :visible.sync="preGoodsInventoryDialogVisible" width="40%" v-dialogDrag>
      <span>
          <el-row>
              <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                  <el-upload ref="uploadPreGoodsInventory" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx"
                      :on-change="uploadChangePreGoodsInventoryFile" :on-remove="uploadRemovePreGoodsInventoryFile" :http-request="uploadPreGoodsInventoryFile" :data="fileparm">
                  <template #trigger>
                      <el-button size="small" type="primary">选取文件</el-button>
                  </template>
                  <el-button style="margin-left: 10px" size="small" type="success" :loading="preGoodsInventoryFileUploadLoading" @click="submitUploadPreGoodsInventoryFile">{{(preGoodsInventoryFileUploadLoading?'上传中':'上传' )}}</el-button>
              </el-upload>
              </el-col>
          </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
          <el-button @click="preGoodsInventoryDialogVisible = false">关闭</el-button>
      </span>
      </el-dialog>

  </my-container>
</template>

<script>
import MyContainer from '@/components/my-container' 
import cesTable from "@/components/Table/table.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue"; 
import { getAllWarehouse } from '@/api/inventory/warehouse'
import inputYunhan from '@/components/Comm/inputYunhan.vue'
import {  
  pageGetGoodsPrePackInventoryAsync,

  getLastPrePackInventoryUpdateTime
} from "@/api/inventory/prepack.js"

import {  
  importPreGoodsInventoryAsync
} from "@/api/inventory/prepackImport.js"

const tableCols = [
    {istrue : true, prop:'warehouseName', label:'仓库', sortable:'custom',},
    {istrue : true, prop:'goodsCode', label:'商品编码', sortable:'custom',},
    {istrue : true, prop:'canUseCount', label:'可用数', sortable:'custom',},
    {istrue : true, prop:'inWarehouseCount', label:'进仓库存数', sortable:'custom',},
    {istrue : true, prop:'inTransitCount', label:'调拨在途数', sortable:'custom',} 
];

const tableHandles1=[
];

export default {
  name: 'PrepackInventory',
  components: {cesTable, vxetablebase, MyContainer, inputYunhan},
  data() {
    return {
      that:this,  
      fileparm: {},
      warehouselist:[],
      filter: { 
        warehouseId:null,
        goodscodes:null
      }, 
      addshow: false,
      list: [],
      summaryarry:{},
      pager:{orderBy:"",isAsc:false},
      tableCols:tableCols,
      tableHandles: tableHandles1, 
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false, 
      selrows:[],
      preGoodsInventoryDialogVisible:false,
      preGoodsInventoryFileUploadLoading: false,
      preGoodsInventoryFileList:[],
      lastTime:null
    }
  },
  async mounted() {
    await this.init();
    await this.getlist();   
  },
  methods: {
    async init() {
      let res = await getAllWarehouse();
      let warehouselist1 = res.data.filter((x) => x.name.indexOf('代发') < 0); 
      this.warehouselist = warehouselist1; 
    }, 
    async callback(val) {
      this.filter.goodscodes = val 
    },
    //获取查询条件
    getCondition(){
      var pager = this.$refs.pager.getPager();
      var page  = this.pager;
      const params = { ...pager, ...page, ... this.filter}
      return params;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    //分页查询
    async getlist() {
      var params=this.getCondition();
      if(params===false){
        return;
      }
      this.pageLoading = true;
      var res = await pageGetGoodsPrePackInventoryAsync(params);
      this.pageLoading = false;
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry=res.data.summary; 
      this.list = data;

      let lastTime = await getLastPrePackInventoryUpdateTime().data;
      this.lastTime=lastTime;
    },
    //排序查询
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else{
        var orderBy =column.prop;
        this.pager={OrderBy:orderBy,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearch();
    },
    submitUploadPreGoodsInventoryFile() {
          if(!this.preGoodsInventoryFileList||this.preGoodsInventoryFileList.length==0){
              this.$message({message: "请先选择文件", type: "warning" });
              return false;
          }
          this.preGoodsInventoryFileUploadLoading=true
          this.$refs.uploadPreGoodsInventory.submit();
      },
      async uploadPreGoodsInventoryFile(item) {
        console.log("file", item)
          if(!item||!item.file||!item.file.size){
              this.$message({message: "请先选择文件", type: "warning" });
              this.preGoodsInventoryFileUploadLoading=false
              return false;
          }
          const form = new FormData();
          form.append("file", item.file); 
          var res= await importPreGoodsInventoryAsync(form);

          this.preGoodsInventoryFileUploadLoading=false
          
          this.preGoodsInventoryFileList = [];
          this.$refs.uploadPreGoodsInventory.clearFiles();
          if (!res?.success){
              return
          }
          this.preGoodsInventoryFileUploadLoading = false;
          this.$message.success("上传成功,正在导入中...")
          
          this.onSearch();
      },
      uploadChangePreGoodsInventoryFile(file, fileList) {
          if (fileList && fileList.length > 0) {
              var list = [];
              for(var i=0;i<fileList.length;i++){
              if(fileList[i].status=="success")
                  list.push(fileList[i]);
              else
                  list.push(fileList[i].raw);
              }
              this.preGoodsInventoryFileList = list;
          }
      },
      uploadRemovePreGoodsInventoryFile(file, fileList){
          this.preGoodsInventoryFileList = fileList;
      // this.uploadChange(file, fileList);
      }
  }
}
</script> 
