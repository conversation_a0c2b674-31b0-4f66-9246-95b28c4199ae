<template>
  <MyContainer>
    <template #header>
      <div>
        <span>
          <el-date-picker v-model="filter.yearMonthDay" placeholder=" 计算时间 " type="date" :clearable="true"
            format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 190px">
          </el-date-picker>
          <el-tooltip class="item" effect="dark" content="计算时间" placement="top-start">
            <i class="el-icon-question" style="margin-right: 10px; padding-top: 5px;"></i>
          </el-tooltip>
        </span>
        <span>
          <el-date-picker v-model="purchaseDate" type="daterange" unlink-panels range-separator="至"
              start-placeholder="采购单开始日期" end-placeholder="采购单结束日期" style="width: 280px;"
              class="publicCss" :value-format="'yyyy-MM-dd'" @change="changeTime">
          </el-date-picker>
          <el-tooltip class="item" effect="dark" content="采购单时间" placement="top-start">
            <i class="el-icon-question" style="margin-right: 10px; padding-top: 5px;"></i>
          </el-tooltip>
        </span>
        <span>
          <el-select v-model="filter.Adder" placeholder="添加人" filterable clearable multiple collapse-tags
            style="width: 200px" ref="AdderSelect" @input.native="AdderfilterData">
            <el-option v-for="item in adderlist" :key="'_adder_' + item.value" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </span>

        <span>
          <el-select v-model="filter.PurDept" placeholder="小组" filterable clearable multiple collapse-tags
            style="width: 240px">
            <el-option v-for="item in purchasegrouplist" :key="'_PurDept_' + item.value" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </span>

        <span>
          <el-select v-model="filter.Area" placeholder="区域" filterable clearable multiple collapse-tags
            style="width: 200px">
            <el-option label="义乌" value="义乌"></el-option>
            <el-option label="南昌" value="南昌"></el-option>
            <el-option label="深圳" value="深圳"></el-option>
            <el-option label="武汉" value="武汉"></el-option>
          </el-select>
        </span>

        <span>
          <el-select v-model="filter.DDeptName" placeholder="职位" filterable clearable multiple collapse-tags
            style="width: 200px">
            <el-option v-for="item in positionList" :key="item.titleName" :label="item.titleName"
              :value="item.titleName"></el-option>
          </el-select>
        </span>

        <span>
          <el-input v-model="filter.StyleCode" placeholder="系列编码" style="width: 200px" maxlength="30"
            clearable></el-input>
        </span>
        <span>
          <el-input v-model="filter.GoodsCode" placeholder="商品编码" style="width: 200px" maxlength="30"
            clearable></el-input>
        </span>

        <span style="margin-left:5px;">
          <el-button type="primary" @click="onSearch" style="width: 70px;">搜索</el-button>
        </span>
        <span style="margin-left:5px;">
          <el-button type="primary" @click="onAdd()" style="width: 70px;">新增</el-button>
        </span>

      </div>
    </template>
    <vxetablebase ref="table" :tableData="tableData" :tableCols="tableCols" :is-index="true" :that="that" :id="'purchaseNoFreePostageToFreePostage202408041544'" 
      :showsummary="false" style="width: 100%; height: 100%; margin: 0" @sortchange='sortchange'
      :treeProp="{ rowField: 'id', parentField: 'parentId' }" :loading="loading" class="already">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" :height="'10px'"/>
    </template>

    <el-dialog title="新增编辑" :visible.sync="showDialog" width="850px" @closeDialog="closeDialog" v-dialogDrag
      @close="cancle()">
      <el-form ref="formInfo" :model="formInfo" :rules="rules" class="demo-form-inline" validate-on-change="false">
        <el-form-item prop="goodsCode">
          <span class="required-field">商品编码: <el-input v-model="formInfo.goodsCode"
              style="width: 130px;margin-right: 30px;"></el-input></span>
          <span class="required-field">聚水潭成本: <el-input-number v-model="formInfo.amountJstCost" placeholder="请输入数字" :min=0 :max=9999
              :precision="2" style="width: 130px;margin-right: 30px;"></el-input-number></span>
          <span class="required-field">新成本: <el-input-number v-model="formInfo.newAmountJstCost" placeholder="请输入数字" :min=0 :max=9999
              :precision="2" style="width: 130px;"></el-input-number> </span>
        </el-form-item>
      </el-form>
      <div>
            <div style="margin: 10px 0 5px 0;">
              <el-link style="font-size: 14px;color: #409EFF;" :underline="false"  @click="onAddRow">新增一行</el-link>
            </div>
            <el-scrollbar style="height: 200px;">
              <div v-for="(item, i) in formInfo.rows" :key="i"
                style="display: flex;align-items: center;margin-top: 5px;">
                <span class="required-field">
                  当月采购量:
                  <el-input-number v-model="item.monthAmountPurchase" placeholder="请输入数字" :min=0 :max=999999
                  :precision="2" style="width: 120px;margin-right: 30px;"></el-input-number>
              </span>
              <span  class="required-field">
                  采购单号:
                  <el-input v-model="item.purchaseOrder"
                  style="width: 120px;margin-right: 30px;"></el-input>
              </span>
              <span  class="required-field">
                  采购单时间:
                  <el-date-picker v-model="item.purchaseOrderTime" type="datetime" style="width: 180px;" placeholder="请选择" format="yyyy-MM-dd HH:mm:ss" :value-format="'yyyy-MM-dd HH:mm:ss'"
                            align="right">
                  </el-date-picker>
              </span>
                <i class="el-icon-remove" style="font-size: 25px;margin-right: 10px;cursor: pointer;"
                  @click="onDelRow(item)"></i>
              </div>
            </el-scrollbar>
          </div>
       <div style="text-align: right;">      
        <el-button type="primary" @click="cancle">取消</el-button>
        <el-button type="primary" @click="submitForm">保存</el-button>
       </div>
    </el-dialog>

  </MyContainer>
</template>
<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/vxetablebase.vue";
import { getBianManPositionListV2 } from '@/api/inventory/warehouse';
import { getPurchaseNewPlanTurnDayBrandList, getPurchaseDeptList } from '@/api/inventory/purchaseordernew';
import inputYunhan from '@/components/Comm/inputYunhan.vue'
import { GetPurchaseNoFreeTransferPageList, AddPurchaseNoFreeTransferAsync, DeletePurchaseNoFreeTransferAsync } from '@/api/inventory/purchaseNoFreePostageToFreePostage';
import { getUserInfo } from '@/api/operatemanage/productalllink/alllink';
import dayjs from 'dayjs'

const tableCols = [
  { istrue: true, sortable: 'custom', width: '100', align: 'center', prop: 'adder', label: '添加人' , treeNode: true, fixed: 'left',},
  { istrue: true, sortable: 'custom', width: '200', align: 'center', prop: 'purDept', label: '小组' },
  { istrue: true, sortable: 'custom', width: '80', align: 'center', prop: 'area', label: '区域' },
  { istrue: true, sortable: 'custom', width: '80', align: 'center', prop: 'dDeptName', label: '职位' },
  { istrue: true, sortable: 'custom', width: '120', align: 'center', prop: 'styleCode', label: '系列编码' },
  { istrue: true, sortable: 'custom', width: '120', align: 'center', prop: 'goodsCode', label: '商品编码' },
  { istrue: true, sortable: 'custom', width: '150', align: 'center', prop: 'goodsName', label: '商品名称' },
  { istrue: true, sortable: 'custom', width: '120', align: 'center', prop: 'amountJstCost', label: '聚水潭成本' },
  { istrue: true, sortable: 'custom', width: '80', align: 'center', prop: 'newAmountJstCost', label: '新成本' },
  { istrue: true, sortable: 'custom', width: '120', align: 'center', prop: 'calculatedDate', label: '计算时间' },
  { istrue: true, sortable: 'custom', width: '150', align: 'center', prop: 'monthAmountPurchase', label: '当月采购量(汇总)' },
  { istrue: true,  width: '120', align: 'center', prop: 'purchaseOrder', label: '采购单号' },
  { istrue: true,  width: '150', align: 'center', prop: 'purchaseOrderTime', label: '采购单时间' },
  { istrue: true, sortable: 'custom', width: '100', align: 'center', prop: 'reductionAmountMargin', label: '降价差额' },
  { istrue: true, sortable: 'custom', width: '120', align: 'center', prop: 'reductionPercentage', label: '降价百分比' },
  { istrue: true, sortable: 'custom', width: '180', align: 'center', prop: 'unCalculatedPerformance', label: '未计算出业绩(汇总)' },
  {
            istrue: true, type: 'button', width: '100', label: '操作', btnList: [
                { label: "编辑", display: (row) => { return false; }, handle: (that, row) => that.onSet(row) , 
                ishide: (that, row) => { return row.parentId!=0 }},
                { label: "删除", display: (row) => { return false; }, handle: (that, row) => that.onDelete(row),
                ishide: (that, row) => { return row.parentId!=0 }}]
        },
];

export default {
  name: 'purchaseNoFreePostageToFreePostage',
  components: {
    MyContainer, vxetablebase, inputYunhan
  },
  data() {
    return {
      that:this,
      filter: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'calculatedDate', 
        isAsc: false,
        //过滤条件
        yearMonthDay: dayjs().format('YYYY-MM-DD'),
        Adder: [],
        PurDept: [],
        Area: [],
        DDeptName: [],
        StyleCode: "",
        GoodsCode: "",
        purchaseStartDate: null,
        purchaseEndDate: null
      },
      rowData: {
        mesgId: null,//雪花id主键
        adderID: null,//添加人ID
        ddUserId : null,//添加人钉钉用户ID
        adder: null,//添加人
        purDeptID: null,//小组ID
        purDept: null,//小组
        area: null,//区域
        dDeptName: null,//职位
        styleCode: null,//系列编码
        goodsCode: null,//商品编码
        goodsName: null,//商品名称
        amountJstCost: null,//聚水潭成本
        newAmountJstCost: null,//新成本
        monthAmountPurchase: null,//当月采购量
        erpCalculatedPerformance: null,//ERP已计算业绩
        totalPriceReduction: null,//合计降价总额
        unCalculatedPerformance: null,//未计算出业绩
      },
      formInfo: {
        goodsCode: "",//商品编码
        // goodsName: null,//商品名称
        amountJstCost: 0,//聚水潭成本
        newAmountJstCost: 0,//新成本
        monthAmountPurchase: 0,//当月采购量
        erpCalculatedPerformance: 0.00,//ERP已计算业绩
        totalPriceReduction: 0.00,//合计降价总额
        unCalculatedPerformance: 0.00,//未计算出业绩
        rows:[],
      },
      //选择器数据
      adderlist: [],//添加人
      purchasegrouplist: [],//小组
      positionList: [],//职位
      purchaseDate: [],//第一次采购时间
      ddUserId: '',//添加人钉钉ID
      styleOptions: null,//系列编码
      skuOptions: null,//商品编码
      loading: false,
      total: 0,
      tableCols: tableCols,
      tableData: [],//表格数据
      summaryarry: {},
      pager: { orderBy: 'brandSort', isAsc: true },
      //新增、编辑弹窗
      showDialog: false,//新增、编辑按钮显示弹窗
      that: this,
      rules: {//校验输入信息
        goodsCode: [
          { required: true, message: '请输入商品编码' }
        ],
        amountJstCost: [
          { required: true, message: '请输入聚水潭成本' },
        ],
        newAmountJstCost: [
          { required: true, message: '请输入新成本' },
        ],
      }
    };
  },
  mounted() {
    this.init();
    this.onSearch();
  },
  methods: {
    changeTime(e){
      this.filter.purchaseStartDate = e ? e[0] : null
      this.filter.purchaseEndDate = e ? e[1] : null
    },
    //获取选择器数据
    async init() {
      //添加人
      var { data } = await getPurchaseNewPlanTurnDayBrandList();
      this.adderlist = data.map(item => { return { value: item.id, label: item.brandName }; });
      //小组
      let { data: deptList, success } = await getPurchaseDeptList();
      if (success) {
        this.purchasegrouplist = deptList.map(item => { return { value: item.dept_id, label: item.full_name }; });
      }
      //职位
      var resPosition = await getBianManPositionListV2();
      this.positionList = resPosition?.data;
      //当前使用者钉钉ID
      var { data } = await getUserInfo();
      this.ddUserId = data.ddUserId;
    },
    //添加人最大字数
    AdderfilterData() {
      var str = this.AdderSelect.$data.selectedLabel;//得到输入汉字
      //控制的js
      if (str.length > 10) {
        this.$refs.sgSelect.$data.selectedLabel = str.substr(0, 10);
      }
    },
    //查询列表
    async onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist();
    },
    async getlist() {
      this.loading = true;

      const { data, success } = await GetPurchaseNoFreeTransferPageList(this.filter);
      if (success) {
        this.tableData = data.list;
        this.total = data.total;
        this.summaryarry = data.summaryarry;
        this.loading = false;
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.filter.currentPage = 1;
      this.filter.pageSize = val;
      this.getlist()
    },
    //当前页改变
    Pagechange(val) {
      this.filter.currentPage = val;
      this.getlist()
    },
    //排序查询
    async sortchange(column) {
      if (column.order) {
        this.filter.orderBy = column.prop;
        this.filter.isAsc = column.order.indexOf("descending") == -1 ? true : false;
        this.getlist();
      }
    },
    //添加按钮，点击显示弹窗
    async onAdd() {
      this.showDialog = true;

      this.$nextTick(() => {
        this.$refs.formInfo.clearValidate(); // 清除上次的校验结果
        //this.$refs.formInfo.validate(); // 手动触发校验
      });
      
      this.rowData = {};
      this.formInfo = {rows:[{ monthAmountPurchase: null, purchaseOrder: null,purchaseOrderTime:null }]};
    },
    //编辑按钮，点击显示弹窗
    async onSet(row) {
      this.showDialog = true; // 关闭对话框

      this.$nextTick(() => {
        this.$refs.formInfo.clearValidate(); // 清除上次的校验结果
        //this.$refs.formInfo.validate(); // 手动触发校验
      });

      this.rowData = {};
      this.formInfo = { 
        goodsCode: row.goodsCode, 
        amountJstCost: row.amountJstCost, 
        newAmountJstCost: row.newAmountJstCost, 
        rows:row.rows
       };
      this.rowData = row;
    },
    //取消操作
    cancle() {
      this.showDialog = false; // 关闭对话框
      this.$refs.formInfo.resetFields(); // 清空表单数据
      this.$refs.formInfo.clearValidate(); // 清空校验状态
    },
    // 保存操作
    submitForm() {
      this.formInfo.mesgId = this.rowData.mesgId || '';
      this.formInfo.adderID = this.rowData.adderID || 0;
      this.formInfo.ddUserId = this.rowData.ddUserId || '0';
      this.formInfo.adder = this.rowData.adder || '';
      this.formInfo.purDeptID = this.rowData.purDeptID || 0;
      this.formInfo.purDept = this.rowData.purDept || '';
      this.formInfo.area = this.rowData.area || '';
      this.formInfo.dDeptName = this.rowData.dDeptName || '';
      this.formInfo.styleCode = this.rowData.styleCode || '';
      this.formInfo.goodsName = this.rowData.goodsName || '';


      if (this.formInfo.amountJstCost == 0 || this.formInfo.newAmountJstCost == 0) {
        this.$message({
          type: 'error',
          message: '操作失败: 请为商品编码、聚水潭成本、新成本输入有效值!'
        });
        // 关闭弹窗
        this.$refs.formInfo.resetFields(); // 重置表单字段
        this.$refs.formInfo.clearValidate(); // 清空校验状态
        return;
      }
      let tag = false;
      this.formInfo.rows.forEach(row=>{
        if(!row.monthAmountPurchase||!row.purchaseOrder||!row.purchaseOrderTime)
        {
          tag=true;
        }
      })
      if(tag)
      {
        this.$message.error("当月采购量、采购单号、采购单时间不允许为空!");
        return;
      }

      this.$refs.formInfo.validate(valid => {
        if ( valid ) {

          AddPurchaseNoFreeTransferAsync(this.formInfo)
            .then(response => {
              // 操作成功的处理逻辑
              if (response.data) {
                this.$message({
                  type: 'success',
                  message: '操作成功!'
                });
                this.getlist();
              } 
              else if (!response.data){
                  this.$message({
                  type: 'error',
                  message: '操作失败!'
                });
              }
              this.showDialog = false;
              this.$refs.formInfo.resetFields(); // 重置表单字段
              this.$refs.formInfo.clearValidate(); // 清空校验状态
              }
            )
            .catch(error => {
              // 处理错误情况
              console.error('Error saving data:', error);
              this.$refs.formInfo.resetFields(); // 重置表单字段
              this.$refs.formInfo.clearValidate(); // 清空校验状态
            });
        }

      })
    },
    // 关闭弹窗
    closeDialog(flag) {
      this.showDialog = false;
      this.$refs.formInfo.resetFields(); // 清空表单数据
      this.$refs.formInfo.clearValidate(); // 清空校验状态
    },
    //删除
    onDelete(row) {
      this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户确认删除后执行删除操作
        DeletePurchaseNoFreeTransferAsync(row)
          .then(response => {
            if (response.data) {
              // 删除成功的处理逻辑
              this.$message({
                type: 'success',
                message: '删除成功!'
              });
            // 成功后刷新列表
            this.getlist(); // 调用重新获取数据的方法
            }
            else {
              this.$message({
                type: 'error',
                message: '删除失败: 您没有权限执行此操作'
              });
            }
          })
      }).catch(() => {
        // 用户取消删除的处理逻辑
        this.$message({
          type: 'info',
          message: '已取消删除!'
        });
      });
    },
    onAddRow()
    {
      this.formInfo.rows.push({ monthAmountPurchase: null, purchaseOrder: null,purchaseOrderTime:null });
    },
    onDelRow(item)
    {
      if(this.formInfo.rows.length>1)
      {
        this.formInfo.rows=this.formInfo.rows.filter((row) => row!=item);
      }else{
        this.$message.error("最后一个元素不允许删除");
      }

    }
  },
};
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

//控制选择器多选-标签宽度
::v-deep .el-select__tags-text {
  max-width: 100px;
}

//新增、编辑el-input行间距
.custom-form .el-form-item {
  margin-bottom: 120px;
  /* 自定义行间距 */
}
.required-field:before {
    content: "*";
    color: red;
    margin-right: 2px;
}
</style>
