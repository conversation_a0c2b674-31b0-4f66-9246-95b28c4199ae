<template>
    <my-container v-loading="listLoading">
        <el-form :model="addForm" ref="addForm" label-width="100px" :rules="calcAddFormRules" :disabled="islook || isCopy" style="height: 100%;overflow-y: auto;">
            <div class="bzjzcjrw">
                <div class="bt">
                    <span style="float: left">编辑/操作</span>
                    <div style="float: right; padding: 3px 0; display: flex; flex-direction: row;" type="text">
                        <a href="#"><i class="el-icon-document-delete" v-if="checkPermission('api:Inventory:PackagesProcessing:EdReset')" @click="onEndShootingTaskAction" title="点击终止"></i></a>
                        <a href="#"><i class="el-icon-document-checked" v-if="checkPermission('api:Inventory:PackagesProcessing:EdReset')" @click="onEndRestartAction" title="点击重启"></i></a>
                        <a href="#"><i v-if="checkPermission('api:Inventory:PackagesProcessing:EdMark')" class="el-icon-news" @click="onSignShootingTaskAction" title="点击标记"></i></a>
                        <!-- <a href="#"><i  class="el-icon-news" title="取消标记"></i></a> -->
                        <a href="#"><i v-if="checkPermission('api:Inventory:PackagesProcessing:EdUnMark')" class="el-icon-odometer" @click="onUnSignShootingTaskAction" title="取消标记"></i></a>
                        <a href="#"><i class="el-icon-delete" v-if="checkPermission('api:Inventory:PackagesProcessing:EdDel')" @click="deltask" title="删除任务"></i></a>
                        <a href="#"><i class="el-icon-more" title="更多操作(待开发)"></i></a>
                    </div>
                </div>
                <div class="rwmc">
                    <div class="xh" style="width: 120px">{{ addForm.packagesProcessingId }}</div>
                    <div class="mc" style="height: 66px">|</div>
                    <div class="mc" style="width: 600px; height: 60px;">

                        <el-tooltip  class="item" effect="dark" :content="addForm.finishedProductName" placement="top">
                            <div @click="inputshow = false" style="overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            ">
                                {{ addForm.finishedProductName }}
                            </div>
                        </el-tooltip>

                    </div>
                    <div class="icon" style="float: right;width: 70px;">
                        <el-button type="primary" @click="onSubmit">保存</el-button>
                    </div>
                </div>
                <div class="bzccjlx">
                    <div class="lxwz">成品编码</div>
                    <div class="lxwz2">
                        <el-form-item prop="finishedProductCode" label=" " label-width="12px">
                            <!-- <el-select v-model="addForm.finishedProductCode" :clearable="true" :collapse-tags="true" filterable @change="onchangeplatform" >
                            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select> -->
                            <div style="display: flex; flex-direction: row;">
                                <div style="width:65%">
                                    <el-input :clearable="true" disabled
                                        v-model.trim="addForm.finishedProductCode" :maxlength=100>
                                        <el-button slot="append" icon="el-icon-plus" @click="onSelctCp(0)"></el-button>
                                    </el-input>
                                </div>
                                <!-- <div style="margin-left: 20px;text-align: right;">
                                    <span><el-button type="primary"
                                            @click="addForm.finishedProductCode = '暂无编码'">暂无编码</el-button></span>
                                </div> -->
                            </div>

                        </el-form-item>
                    </div>
                </div>

                <div class="bzccjlx" v-if="checkPermission('api:Inventory:PackagesProcessing:EdUrgencyDegree')">
                    <div class="lxwz">紧急程度</div>
                    <div class="lxwz2">
                        <el-form-item prop="urgencyDegree" label=" " label-width="12px">
                            <el-select  @change="statustotext" style="width:30%" :value="statustotext(addForm.urgencyDegree)" :clearable="true" :collapse-tags="true"
                                filterable>
                                <el-option v-for="item in statuslist" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>

                <div class="bzccjlx">
                    <div class="lxwz">品牌</div>
                    <div class="lxwz2">
                        <el-form-item prop="brandName" label=" " label-width="12px">
                            <el-select key="1" style="width:30%" @change="codetoname1" :value="codetoname1(addForm.brandCode)" :clearable="true" :collapse-tags="true"
                                filterable>
                                <el-option v-for="item in allsellistfuc.brandList" :key="item.setId" :label="item.sceneCode"
                                    :value="item.setId" />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>

                <div class="bzccjlx">
                    <div class="lxwz">包装材料</div>
                    <div class="lxwz2">
                        <el-form-item prop="packingMaterialName" label=" " label-width="12px">
                            <el-select key="2" style="width:50%" @change="codetoname2" :value="codetoname2(addForm.packingMaterialCode)" :clearable="true" filterable>
                                <el-option v-for="item in allsellistfuc.packingMaterialList" :key="item.setId" :label="item.sceneCode"
                                    :value="item.setId" />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>

                <div class="bzccjlx">
                    <div class="lxwz">机型</div>
                    <div class="lxwz2">
                        <el-form-item prop="machineTypeName" label=" " label-width="12px">
                            <el-select key="3" style="width:40%" @change="codetoname3" :value="codetoname3(addForm.machineTypeCode)" :clearable="true" filterable>
                                <el-option v-for="item in allsellistfuc.machineTypeList" :key="item.setId" :label="item.sceneCode"
                                    :value="item.setId" />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>

                <div class="bzccjlx">
                    <div class="lxwz">包装尺寸</div>
                    <div class="lxwz2">
                        <el-form-item prop="packageSizeCode" label=" " label-width="12px">
                            <el-select key="4" style="width:45%" @change="codetoname4($event)" :value="codetoname4(addForm.packageSizeCode)" :clearable="true" filterable>
                                <el-option v-for="item in allsellistfuc.packageSizeList" :key="item.setId" :label="item.sceneCode"
                                    :value="item.setId" />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>

                <div class="bzccjlx">
                    <div class="lxwz">成品数量</div>
                    <div class="lxwz2">
                        <el-form-item prop="finishedProductQuantity" label=" " label-width="12px">
                            <el-input-number :step="1" controls-position="right" style="width:25%" :clearable="true"
                                v-model.trim="addForm.finishedProductQuantity" :min="1" :max="1000000"></el-input-number>
                        </el-form-item>
                    </div>
                </div>

                <div class="bzccjlx">
                    <div class="lxwz">半成品加工仓</div>
                    <div class="lxwz2">
                      <el-form-item prop="halfProcessWarehouse" label=" " label-width="12px">
                          <el-select style="width:45%" v-model.trim="addForm.halfProcessWarehouse" :clearable="true" :collapse-tags="true"
                              filterable>
                                <el-option v-for="item in wareList" :key="item.wms_co_id" :label="item.name" :value="item.wms_co_id" />
                          </el-select>
                      </el-form-item>
                    </div>
                </div>

                <div class="bzccjlx">
                    <div class="lxwz">类型</div>
                    <div class="lxwz2">
                        <el-form-item label=" " label-width="12px">
                            <div style="display: flex; flex-direction: row;">
                              <el-select size="mini" style="width:250px;" v-model="addForm.typeStr" placeholder="请选择">
                                <el-option v-for="item in typeSettinglist" :key="item.value" :label="item.label" :value="item.label">
                                </el-option>
                              </el-select>
                            </div>
                        </el-form-item>
                    </div>
                </div>

                <div class="bzccjlx">
                    <div class="lxwz">成品调入仓</div>
                    <div class="lxwz2">
                        <el-form-item prop="finishTranWarehouse" label=" " label-width="12px">
                            <el-select style="width:45%" v-model="addForm.finishTranWarehouse" :clearable="true" :collapse-tags="true"
                                filterable>
                                <el-option v-for="item in  wareList" :key="item.wms_co_id" :label="item.name" :value="item.wms_co_id" />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>

                <div class="bzccjlx">
                    <div class="lxwz">计划完成日期</div>
                    <div class="lxwz2">
                        <el-form-item prop="pfDate" label=" " label-width="12px">
                            <el-date-picker v-model="addForm.pfDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                type="date" style="width:35%" placeholder="结束时间">
                            </el-date-picker>
                        </el-form-item>
                    </div>
                </div>

                <div class="bzccjlx">
                    <div class="lxwz">成品图片</div>
                    <div class="lxwz2">
                        <el-form-item prop="purImageUrl" label=" ">
                            <yh-img-upload :value.sync="addForm.finishedProductImg" ref="supplier_id" :limit="1" ></yh-img-upload>
                        </el-form-item>
                    </div>
                </div>

                <div class="box-card">
                    <div slot="header" class="clearfix" style="display: flex;margin-bottom: 10px;">
                        <div style="width:50%;line-height:28px;font-size:16px;"><span>半成品编码</span></div>
                        <div style="margin-left: auto; z-index: 99;">
                            <el-button type="primary" @click="onSelctCp(1)">选择半成品编码</el-button>
                        </div>
                    </div>
                    <div style="width:100% ;height: 300px;overflow: auto; border: 1px solid #dcdfe6;">
                        <el-table :data="addForm.detialPackagesProcess" header-row-class-name="bcpb">
                            <el-table-column label="序号" width="50" align="center">
                                <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                            </el-table-column>
                            <el-table-column prop="id" label="id" v-if="false" />
                            <el-table-column prop="halfProductCode" label="半成品编码" width="120" />
                            <el-table-column prop="halfProductName" label="半成品名称" width="200" />
                            <el-table-column prop="halfProductQuantity" label="组合数量" width="160">
                                <template slot-scope="scope">
                                    <el-input-number v-model="scope.row.halfProductQuantity" :min="0" :max="10000"
                                        placeholder="数量" :precision="4">
                                    </el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column lable="操作">
                                <template slot-scope="scope">
                                    <el-button type="danger" @click="onDelDtlGood(scope.$index)">移除 <i
                                            class="el-icon-remove-outline"></i>
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
                <div class="box-card">
                    <div slot="header" class="clearfix" style="display: flex;margin-bottom: 10px;">
                        <div style="width:50%;line-height:28px;font-size:16px;"><span>耗材编码</span></div>
                        <div style="margin-left: auto; z-index: 99;">
                            <el-button type="primary" @click="onSelctCp(3)">选择耗材编码</el-button>
                        </div>
                    </div>
                    <div style="width:100% ;height: 300px;overflow: auto; border: 1px solid #dcdfe6;">
                        <el-table :data="addForm.detailConsumableList" header-row-class-name="bcpb">
                            <el-table-column label="序号" width="50" align="center">
                                <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                            </el-table-column>
                            <el-table-column prop="id" label="id" v-if="false" />
                            <el-table-column prop="consumableProductCode" label="耗材编码" width="120" />
                            <el-table-column prop="consumableProductName" label="耗材编码名称" width="160" />
                            <el-table-column prop="consumableQuantity" label="所用数量" width="150">
                                <template slot-scope="scope">
                                    <el-input-number v-model="scope.row.consumableQuantity" :min="1" :max="10000"
                                        placeholder="数量" :precision="0">
                                    </el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column prop="consumableLength" label="长" width="50">
                                <template slot-scope="scope">
                                    <el-input v-model="scope.row.consumableLength" :min="1" :max="10000" maxlength="5"
                                        placeholder="长" :precision="0" @input="handleInput(scope.row, 'consumableLength')" @keyup="handleInput(scope.row, 'consumableLength')">
                                    </el-input>
                                </template>
                            </el-table-column>
                            <el-table-column prop="consumableWide" label="宽" width="50">
                                <template slot-scope="scope">
                                    <el-input v-model="scope.row.consumableWide" :min="1" :max="10000" maxlength="5"
                                        placeholder="宽" :precision="0" @input="handleInput(scope.row, 'consumableWide')" @keyup="handleInput(scope.row, 'consumableWide')">
                                    </el-input>
                                </template>
                            </el-table-column>
                            <el-table-column lable="操作">
                                <template slot-scope="scope">
                                    <el-button type="danger" @click="onDeconsumableGood(scope.$index)">移除 <i
                                            class="el-icon-remove-outline"></i>
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </div>
            <!-- <el-row>
                <el-col :span="24" v-if="addForm.shootingTaskId > 0">
                    <el-form-item label="操作日志" prop="loginfo">
                        <el-input type="textarea" :rows="5" v-model="loginfo" :disabled="true" placeholder="请输入备注" />
                    </el-form-item>
                </el-col>
            </el-row> -->
             <!-- 操作日志 star -->
             <div class="bzbjfgx" style="min-height:160px">
                <div  class="rzxgsj" style="margin-left: 20px;">操作日志</div>
                        <div class="bzczrzx" v-for="(item,index ) in loginfo " :key="index+'log'">
                            <div>
                            <div class="rztx">
                                <el-avatar :size="25" fit="cover" :src="item.avatar"></el-avatar>
                            </div>
                            <div class="rzmz">{{ item.name }}</div>
                            <div class="rzxgx">
                               <span> {{ item.changeinfo }}</span>
                            </div>
                            <el-tooltip class="item" effect="dark" :content="item.time" placement="top-start">
                                <div class="rzxgsj">  {{ item.time }}</div>
                            </el-tooltip>
                            </div>
                            <div>
                            <div class="rzbox">
                            <div class="rzxgq">修改后：</div>
                            <div class="rzxgnr">
                            <el-tooltip class="item" effect="dark" :content="item.after" placement="top-start">
                                <span>{{ item.after }}</span>
                            </el-tooltip>
                            </div>
                            </div>
                            </div>
                            <div>
                            <div class="rzbox">
                                <div class="rzxgq">修改前：</div>
                                <div class="rzxgnr">
                             <el-tooltip class="item" effect="dark" :content="item.before" placement="top-start">
                                  <span>{{ item.before }}</span>
                             </el-tooltip>
                                </div>
                            </div>
                            </div>
                        </div>

                        </div>
            <!-- 操作日志 end -->
        </el-form>

        <!--选择商品-->
        <el-dialog title="选择编码" :visible.sync="goodschoiceVisible" width='88%' height='500px' v-dialogDrag append-to-body>
            <goodschoice :ischoice="true" ref="goodschoice" style="z-index:10000;height:500px" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="goodschoiceVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onQueren">确 定</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="参考列表" :visible.sync="dialogVisible" width="35%" :append-to-body="true">
            <shootingreferencelist ref="shootingreferencelist" style="z-index:10000;height:400px"></shootingreferencelist>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">关 闭</el-button>
                    <el-button type="primary" @click="selreference()">创建任务</el-button>
                </span>
            </template>
        </el-dialog>
    </my-container>
</template>
<script>
import uploadfile from '@/views/media/shooting/uploadfile'
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import { addOrUpdateShootingVideoTaskAsync, getShootingTaskFliesAsync, delShootingTploadFileTaskAsync } from '@/api/media/ShootingVideo';
import { editPackagesProcess, getUrgencyDegreeyList, endAction, markAction, restartAction, getAllWarehouse, deletePackagesProcessing, unMarkAction,
    replaceTemplatesWorkPrice, getPackagesProcessingListDetialAsync
 } from '@/api/inventory/packagesprocess';//包装加工

import { getPackagesDetialByIdAsync , getPackagesSetData} from '@/api/inventory/packagesSetProcessing.js';

import { getList as getshopList } from '@/api/operatemanage/base/shop'
import { formatWarehouse } from "@/utils/tools";
import shootingreferencelist from '@/views/media/shooting/fuJianmanage/shootingreferencelistSel';
import semifinishedtable from '@/views/media/packagework/semifinishedtable.vue';
import goodschoice from "@/views/base/goods/goods2.vue";
import { getShootingSetDataById, getShootingSetData, saveShootingSet, deleteShootingSet, saveDataOrderListDataAsync } from '@/api/media/shootingset'
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
export default {
    props: ["taskUrgencyList", 'groupList', 'platformList', 'islook', 'onCloseAddForm', 'warehouselist', 'versionId', 'packagesProcessingId','isCopy'],
    inject: ['allsellist'],
    components: { MyContainer, MyConfirmButton, uploadfile, shootingreferencelist, semifinishedtable, goodschoice, YhImgUpload },
    data() {
        return {
            that: this,
            addLoading: true,
            dialogFormVisible: false,
            dialogVisible: false,
            pageLoading: false,
            listLoading: false,
            inputshow: true,
            endworktime: '',
            formatWarehouse: formatWarehouse,
            //选择商品窗口
            goodschoiceVisible: false,
            allAlign1: null,
            tableData1: [
            ],
            shopList: [],
            userList: [],
            taskPhotofileList: [],
            taskExeclfileList: [],
            typeSettinglist: [],
            packageSizeList: [],
            machineTypeList: [],
            packingMaterialList: [],
            brandList: [],
            statuslist: [
                {
                    "label": "压单",
                    "value": 0
                },
                {
                    "label": "正常",
                    "value": 9
                },
                {
                    "label": "待审",
                    "value": 2
                },
                {
                    "label": "加急",
                    "value": 1
                }
            ],
            //warehouseList:[],
            wareList:[],
            addForm: {
                packagesProcessId: 0,
                finishedProductName: "",
                finishedProductCode: "",
                finishedProductImg: "",
                brandCode: "",
                brandName: "",
                packingMaterialCode: "",
                packingMaterialName: "",
                machineTypeCode: "",
                machineTypeName: "",
                packageSizeCode: "",
                packageSizeName: "",
                finishedProductQuantity: 0,
                pfDate: "",
                urgencyDegree: "",
                quantityRequired: 0,
                certificateInfo: "",
                remark: "",
                detialPackagesProcess: [],
                detailConsumableList: [],
                finishTranWarehouse:null,
                typeStr:""
            },
            loginfo: null,
            fpPhotoLqNameEnable: false,
            fpVideoLqNameEnable: false,
            fpDetailLqNameEnable: false,
            fpModelLqNameEnable: false,
            addFormRules: {
                productShortName: [{ required: true, message: '请填写', trigger: 'blur' }],
                shopName: [{ required: true, message: '请选择', trigger: 'blur' }],
                operationGroup: [{ required: true, message: '请选择', trigger: 'blur' }],
                dockingPeople: [{ required: true, message: '请填写', trigger: 'blur' }],
                warehouse: [{ required: true, message: '请选择', trigger: 'blur' }],
                isDelivered: [{ required: true, message: '请选择', trigger: 'blur' }],
                shootingTaskPickList: [{ required: true, message: '请选择', trigger: 'blur' }],
                platform: [{ required: true, message: '请选择', trigger: 'blur' }],

            },
            extBzTypeArgs: null,
            taskrow: {}
        };
    },
    async created() {
        // this.gettabmsg();
    },
    async mounted() {
        this.setWare();
        //通过非任务列表打开组件，将编辑/操作抽屉禁用
        if (this.islook !== false) {
          this.islook = true;
        }
        // this.addForm.dockingPeople = this.$store.getters.userName?.split("-")[0].trim();
    },
    watch: {
        packagesProcessingIdfuc: {
            handler(newVal){
                let _this = this;
                if(_this.versionId){
                    _this.hisgetfamsg(newVal,_this.versionId);
                }else{
                    _this.getfamsg(newVal);
                }
            },
            immediate: true
        },

    },
    computed: {
        packagesProcessingIdfuc () {
            return this.packagesProcessingId
        },
        calcAddFormRules() {
            return {
                // productShortName: [{ required: true, message: '请填写', trigger: 'blur' }],
                // shopName: [{ required: true, message: '请选择', trigger: 'blur' }],
                // operationGroup: [{ required: true, message: '请选择', trigger: 'blur' }],
                // dockingPeople: [{ required: true, message: '请填写', trigger: 'blur' }],
                // warehouse: [{ required: true, message: '请选择', trigger: 'blur' }],
                // isDelivered: [{ required: true, message: '请选择', trigger: 'blur' }],
                // shootingTaskPickList: [{ required: true, message: '请选择', trigger: 'blur' }],
                // platform: [{ required: true, message: '请选择', trigger: 'blur' }],
                // isYYJY: [{ required: true, message: '请选择', trigger: 'blur' }],
                // yyExpressNum: [{ required: this.addForm.isYYJY == 1, message: '请填写', trigger: 'blur' }],
            }
        },
        allsellistfuc(){
            return this.allsellist()
        }
    },
    methods: {
        handleInput(row, field) {
          if (row[field] < 0) {
            row[field] = 0;
            this.$message.error('请输入正数');
          }
        },
        //终止重启
        async onEndRestartAction() {
            let _this = this;
            this.$confirm("选中的任务将会重启，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await restartAction(_this.addForm.packagesProcessingId);
                if (res?.success) {
                this.$message({ message: '操作成功', type: "success" });
                this.addForm.isend = 0;
                this.$emit('getTaskList');
                // this.onCloseAddForm(2);
                }
            });
        },
        //终止
        async onEndShootingTaskAction() {
            let _this = this;
            this.$confirm("选中的任务将会终止，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await endAction(_this.addForm.packagesProcessingId);
                if (res?.success) {
                this.$message({ message: '操作成功', type: "success" });
                this.addForm.isend = 1;
                this.$emit('getTaskList');
                // this.onCloseAddForm(2);
                }
            });
        },
        // 删除操作
        async onDeleteShootingTaskAction() {
            if (this.islook) return;
            this.$confirm("选中的任务会移动到回收站，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await deleteShootingTaskActionAsync([this.addForm.changeImgTaskId]);
                if (res?.success) {
                this.$message({ message: '操作成功', type: "success" });
                // this.onCloseAddForm(3);
                }
            });
        },
        //任务标记
        async onSignShootingTaskAction() {
            let _this = this;
            this.$confirm("标记选中任务，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await markAction(_this.addForm.packagesProcessingId);
                if (res?.success) {
                this.$message({ message: '操作成功', type: "success" });
                this.addForm.isTopOldNum = 2;
                this.$emit('getTaskList');
                // this.onCloseAddForm(2);
                }
            });
        },
        //任务标记
        async deltask() {
            let _this = this;
            this.$confirm("删除选中任务，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await deletePackagesProcessing(_this.addForm.packagesProcessingId);
                if (res?.success) {
                this.$message({ message: '操作成功', type: "success" });
                this.addForm.isTopOldNum = 2;
                this.$emit('getTaskList');
                this.$emit("onCloseAddForm",1);
                // this.onCloseAddForm(2);
                }
            });
        },
        // 取消标记
        async onUnSignShootingTaskAction() {
            let _this = this;
            this.$confirm("取消标记任务，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await unMarkAction(_this.addForm.packagesProcessingId);
                if (res?.success) {
                this.$message({ message: '操作成功', type: "success" });
                this.addForm.isTopOldNum = 0;
                this.$emit('getTaskList');
                // this.onCloseAddForm(2);
                }
            });
        },
        codetoname1(code){
            var a = '';
            this.addForm.brandCode = code;
                this.allsellistfuc.brandList.forEach((item)=>{
                    if(item.setId==code){
                        a = item.sceneCode;
                        return
                    }
                })
            return a;
        },
        codetoname2(code){
            var b = '';
                this.addForm.packingMaterialCode = code;
                    this.allsellistfuc.packingMaterialList.forEach((item)=>{
                        if(item.setId==code){
                            b = item.sceneCode;
                            return
                        }
                    })
            return b;
        },
        codetoname3(code){
            var c = '';
            this.addForm.machineTypeCode = code;
                    this.allsellistfuc.machineTypeList.forEach((item)=>{
                        if(item.setId==code){
                            c = item.sceneCode;
                            return
                        }
                    })
            return c;

        },
        codetoname4(code){
            var d = '';
            this.addForm.packageSizeCode = code;
                    this.allsellistfuc.packageSizeList.forEach((item)=>{
                        if(item.setId==code){
                            d = item.sceneCode;
                            // throw Error("");
                            return
                        }
                    })
            return d;
        },

        async getfamsg(e) {
            this.addForm = {};
            let params = {IsNeedDetial: true,packagesProcessId:e}
            const res = await getPackagesProcessingListDetialAsync(params);
            if (!res?.success) {
                return
            }
            if(res.data.list.length>0){
                this.addForm = res.data.list[0];
                this.addForm.detialPackagesProcess = res.data.list[0]?.detialList;
                this.addForm.detailConsumableList = res.data.list[0]?.consumableList;
            }
            this.loginfo = res.data.loginfo;


        },
        async hisgetfamsg(e,hisid) {
            this.addForm = {};
            let params = {IsNeedDetial: true,packagesProcessId:e, versionId: hisid }
            const res = await getPackagesDetialByIdAsync(params);
            if (!res?.success) {
                return
            }
            if(res.data.list.length>0){
                this.addForm = res.data.list[0];
                this.addForm.detialPackagesProcess = res.data.list[0]?.detialList;
                this.addForm.detailConsumableList = res.data.list[0]?.consumableList;
            }
            this.loginfo = res.data.loginfo;


        },
        async gettabmsg(data) {
            this.listLoading = true;
            // await this.getDataSetList(14);
            // await this.getDataSetList(15);
            // await this.getDataSetList(16);
            // await this.getDataSetList(17);
            // this.allsellistfuc.packageSizeList = this.allsellistfuc.packageSizeList;
            // this.allsellistfuc.machineTypeList = this.allsellistfuc.machineTypeList;
            // this.allsellistfuc.brandList = this.allsellistfuc.brandList;
            // this.allsellistfuc.packingMaterialList = this.allsellistfuc.packingMaterialList;
            // await this.getStatus();
            this.listLoading = false;
        },

        async getStatus(){
            const res = await getUrgencyDegreeyList();
            if (!res?.success) {
                return
            }
            this.statuslist = res.data;
        },
        statustotext(val){
            this.addForm.urgencyDegree = val;
            var num = ""
            this.statuslist.forEach((item)=>{
                if(item.value == val) num = item.label;
                return
            })
            return num;
        },
        async getDataSetList(index) { //14包装加工，15-品牌，16包装加工-机型，17包装加工-尺寸
            const res = await getShootingSetData({ setType: index });
            if (!res?.success) {
                return
            }
            switch (index) {
                case 14:
                    this.allsellistfuc.packingMaterialList = res?.data?.data;
                    break;
                case 15:
                    this.allsellistfuc.brandList = res?.data?.data;
                    break;
                case 16:
                    this.allsellistfuc.machineTypeList = res?.data?.data;
                    break;
                case 17:
                    this.allsellistfuc.packageSizeList = res?.data?.data;
                    break;
            }
        },
        //移除明细
        async onDelDtlGood(index) {
            this.addForm.detialPackagesProcess.splice(index, 1);
        },
        async onDeconsumableGood(index) {
            this.addForm.detailConsumableList.splice(index, 1);
        },
        //选择商品确定
        async onQueren() {
            if (this.seltype == 0) {
                //选择成品商品确定
                var choicelist = await this.$refs.goodschoice.getchoicelistOnly();
                if (choicelist && choicelist.length == 1) {
                    choicelist.forEach(f => {
                        //反填数据
                        this.addForm.finishedProductCode = f.goodsCode;
                        this.addForm.finishedProductName = f.goodsName;
                    })
                    this.goodschoiceVisible = false;
                }
            }
            else if (this.seltype == 2) {
                //完成界面选择商品
                var choicelist = await this.$refs.goodschoice.getchoicelistOnly();
                if (choicelist && choicelist.length == 1) {
                    choicelist.forEach(f => {
                        //反填数据
                        this.finishForm.consumeGoodsCode = f.goodsCode;
                        this.finishForm.consumeGoodsName = f.goodsName;
                        this.finishForm.consumePicture = f.picture;
                    })
                    this.goodschoiceVisible = false;
                }
            }
            else if (this.seltype == 3) {
                //选择耗材编码
                if(this.addForm.detailConsumableList === undefined){
                  this.addForm.detailConsumableList = []
                }
                var choicelist = await this.$refs.goodschoice.getchoicelist();
                if (choicelist && choicelist.length > 0) {
                  choicelist.forEach((item) => {
                    const newObject = {
                      consumableProductCode: item.goodsCode,
                      consumableProductName: item.goodsName,
                      consumableQuantity: 0,
                      consumableLength: 0,
                      consumableWide: 0,
                    };
                    this.addForm.detailConsumableList.push(newObject);
                  });
                    //反填数据,
                    this.goodschoiceVisible = false;
                }
            }
            else if (this.seltype == 10) {
                //完成界面选择商品
                var choicelist = await this.$refs.goodschoice.getchoicelistOnly();
                if (choicelist && choicelist.length == 1) {
                    choicelist.forEach(f => {
                        //反填数据
                        this.updateUserGoodsAmountData.consumeGoodsCode = f.goodsCode;
                        this.updateUserGoodsAmountData.consumeGoodsName = f.goodsName;
                        this.updateUserGoodsAmountData.consumePicture = f.picture;
                    })
                    this.goodschoiceVisible = false;
                }
            }
            else {
                //选择半成品商品确定
                var choicelist = await this.$refs.goodschoice.getchoicelist();
                if (choicelist && choicelist.length > 0) {
                    //反填数据,
                    if (this.addForm.detialPackagesProcess) {
                        //已存在的不添加
                        var temp = this.addForm.detialPackagesProcess;
                        var isNew = true;
                        choicelist.forEach(f => {
                            isNew = true;
                            temp.forEach(old => {
                                if (old.halfProductCode == f.goodsCode) {
                                    isNew = false;
                                }
                            });
                            //
                            if (isNew) {
                                this.addForm.detialPackagesProcess.push({ halfProductCode: f.goodsCode, halfProductName: f.goodsName, halfProductQuantity: 0, dtlActualGoodsAmount: 0 });
                            } else {
                                this.addForm.detialPackagesProcess.forEach(told => {
                                    if (told.halfProductCode == f.goodsCode) {
                                        told.halfProductName = f.goodsName;
                                    }
                                });
                            }
                        })

                    }
                    this.goodschoiceVisible = false;
                }
            }
        },
        //新增/编辑/完成界面的【选择商品】按钮
        onSelctCp(type) {
            this.seltype = type;
            this.goodschoiceVisible = true;
            this.$nextTick(() => {
                this.$refs.goodschoice.removeSelData();
            })
        },
        async OpenAdd() {
            this.addDialogTitle = "创建加工表单";
            this.dialogFormVisible = true;
        },
        async selreference() {
            var res = await editPackagesProcess(this.addForm);
            this.addLoading = false;
            if (!res?.success) { this.$message({ message: res.msg, type: 'error' }); return; }
            this.$message({ message: '创建任务成功！', type: 'success' });
        },
        reference() {
            this.dialogVisible = true;
        },

        setShootO(warehouselist) {
            this.warehouselist = warehouselist;
        },
        //编码
        taskPickChange(value) {
            //未选择，只读，且清空
            if (value.indexOf("1") < 0) {
                this.fpPhotoLqNameEnable = true;
            } else {
                this.fpPhotoLqNameEnable = false;
            }
            if (value.indexOf("2") < 0) {
                this.fpVideoLqNameEnable = true;
            } else {
                this.fpVideoLqNameEnable = false;
            }
            if (value.indexOf("3") < 0) {
            } else {
            }
            if (value.indexOf("4") < 0) {
                this.fpDetailLqNameEnable = true;
            } else {
                this.fpDetailLqNameEnable = false;
            }
            if (value.indexOf("5") < 0 && value.indexOf("6") < 0) {
                this.fpModelLqNameEnable = true;
            } else {
                this.fpModelLqNameEnable = false;
            }
        },
        async onchangeplatform(val) {
            var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
            this.addForm.shopName = "";
            this.shopList = res1.data.list;
        },
        async setWare() {
            const res = await getAllWarehouse();
            if (!res?.success) {
                return
            }
            this.wareList = res?.data;
            const { data, success } = await getPackagesSetData({ setType: 12 })
            if (success) {
              this.typeSettinglist = data.map(item => { return { value: item.setId, label: item.sceneCode }; });
            }
        },
        //提交保存时验证
        onSubmitValidate: function () {
            let isValid = true;
            this.$refs.addForm.validate(valid => {
                isValid = valid
            })
            return isValid;
        },
        //提交保存
        async onSubmit() {
            if(this.addForm.finishedProductQuantity == 0||!this.addForm.finishedProductQuantity){
                this.$message({ message: '请填写成品数量！', type: 'warning' });
                return
            }
            this.addForm.packagesProcessId = this.addForm.packagesProcessingId;
            var res = await editPackagesProcess(this.addForm);
            this.addLoading = false;
            if (!res?.success) { this.$message({ message: res.msg, type: 'error' }); return; }
            // this.$message({ message: '修改任务成功！', type: 'success' });
            // this.$emit('onCloseAddForm',1);
            if (res.data.templateId<=0||!res.data.templateId) {
                this.$message({ message: '修改任务成功！', type: 'success' });
                this.finishedProductCode = "";
                this.addLoading = false;
                // this.onCloseAddForm(1);
                this.$emit('onCloseAddForm',1);
            }else{

            this.$confirm("当前任务存在历史工价，是否覆盖？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
                distinguishCancelAndClose: true,
            })
                .then(async () => {
                let params = {
                    ...res.data
                }
                var ress = await replaceTemplatesWorkPrice(params);
                if (ress?.success) {

                    this.$message({ message: '修改任务成功！', type: "success" });
                    // await this.$emit('getTaskList');
                    // this.onCloseAddForm(1);
                    this.$emit('onCloseAddForm',1);
                    this.addLoading = false;
                }
                }).catch(async (e) => {
                    this.$message({ message: '未沿用工价，修改任务成功！', type: 'success' });
                    // this.onCloseAddForm(1);
                    this.$emit('onCloseAddForm',1);
                    this.addLoading = false;

                });
            }
        },
        //删除上传附件操作
        async deluplogexl(ret) {
            this.addLoading = true;
            await delShootingTploadFileTaskAsync({ upLoadPhotoId: ret.upLoadPhotoId, type: 2 }).catch(_ => {
                this.addLoading = false;
            });
            this.addLoading = false;
        },
        //删除上传图片操作
        async deluplogimg(ret) {
            this.addLoading = true;
            await delShootingTploadFileTaskAsync({ upLoadPhotoId: ret.upLoadPhotoId, type: 1 }).catch(_ => {
                this.addLoading = false;
            });
            this.addLoading = false;
        },
    },
};
</script>
<style lang="scss" scoped>
::v-deep .bzjzcjrw {
    width: 100%;
    margin-top: 15px;
    background-color: #fff;
}

::v-deep .bzjzcjrw .bt {
    height: 40px;
    /* background-color: aquamarine; */
    font-size: 18px;
    color: #666;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
    box-sizing: border-box;
    padding: 0 35px;
}

::v-deep .bzjzcjrw .rwmc {
    margin-bottom: 20px;
}

::v-deep .bzjzcjrw .bt i {
    color: #999;
}

::v-deep .bzjzcjrw .bt i {
    margin-left: 8px;
    line-height: 26px;
}

::v-deep .bzjzcjrw .bt i:hover {
    margin-left: 8px;
    line-height: 26px;
    color: #409eff;
    position: relative;
    top: -2px;
}

::v-deep .bzjzcjrw .bzccjlx {
    box-sizing: border-box;
    padding: 0 60px;
    display: flex;
}

::v-deep .bzjzcjrw .bzccjlx {
    width: 100%;
    height: 40px;
    box-sizing: border-box;
    padding: 0 60px;
    display: flex;
}

::v-deep .bzjzcjrw .bzccjlx .lxwz {
    width: 20%;
    font-size: 14px;
    color: #666;
    vertical-align: top;
    line-height: 26px;
    /* background-color: rgb(204, 204, 255); */
}

::v-deep .bzjzcjrw .bzccjlx .lxwz2 {
    width: 80%;
}
::v-deep .el-form-item__content{
    margin-left: 30px !important;
}

::v-deep .bzczxx {
    width: 100%;
    box-sizing: border-box;
    /* padding: 0 60px; */
    text-align: center;
    border: 1px solid #dcdfe6;
    border-right: 0px;
    border-bottom: 0px;
    border-left: 0px;
    font-size: 12px;
    margin: 0 0;
}

.bzbjfgx {
    // border: 1px solid #dcdfe6;
    border-top: 1px solid #dcdfe6;
    border-right: 0px;
    border-left: 0px;
    box-sizing: border-box;
    font-size: 12px;
    padding: 25px 0;
    margin-top: 20px;
}

.bzczrzx {
    box-sizing: border-box;
    padding: 10px 60px;
}

::v-deep .rztx,
.rzmz,
.rzxgx,
.rzxgsj {
    height: 30px;
    display: inline-block;
    font-size: 14px;
    line-height: 30px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.rztx {
    width: 25px;
    height: 25px;
    background-color: #f5f5f5;
    border-radius: 50%;
    margin-right: 15px;
}

.rzmz {
    width: 50px;
    margin-right: 5px;
}

.rzxgx {
    max-width: 200px;
    margin-right: 10px;
    color: #999;
}

.rzxgsj {
    max-width: 200px;
    color: #999;
}

.bzbjrw .rzxgq,
.rzxgnr {
    max-width: 450px;
    line-height: 15px;
    font-size: 12px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.rzbox{
    display: flex;
}

.rzxgq {
    width: 50px;
    margin-left: 43px;
    margin-right: 2px;
}
</style>

