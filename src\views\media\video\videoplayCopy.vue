<template>

    <el-row :gutter="15">
        <el-col :xs="24" :sm="24" :lg="15">
            <div class="video-container" style="padding-left:22px">
                <video class="my-video" ref="originVideo" oncontextmenu="return false;"></video>
                <!-- <div class="block"><input type="file" @change="select_origin_file" /></div> -->
            </div>

        </el-col>
        <el-col :xs="24" :sm="24" :lg="9">
            <div class="block">
                <el-table :data="clipArray" style="width:500px;" highlight-current-row @row-click="palyClipV">
                    <el-table-column prop="name" label="剪辑段落" width="180">
                    </el-table-column>
                    <el-table-column prop="beginTime1" label="开始时间" width="180">
                    </el-table-column>
                    <el-table-column prop="endTime1" label="结束时间">
                    </el-table-column>
                    <!-- <el-table-column prop="beginTime" label="开始时间" width="180">
                    </el-table-column>
                    <el-table-column prop="endTime" label="结束时间">
                    </el-table-column> -->
                </el-table>

            </div>
        </el-col>
        <el-col :xs="24" :sm="24" :lg="24">

            <div v-show="isShow" style="padding-left:22px;padding-right:22px">
                <div class="block">
                    <!-- <button type="button" @click="clipVideo">剪辑</button>
                    <button type="button" @click="clearClip">清空</button> -->
                    <!-- <button type="button" @click="moveFrame('up')">上一帧</button> -->
                    <!-- <button type="button" @click="playVio">{{this.isPlay ?'暂停':'播放' }}</button> -->
                    <!-- <button type="button" @click="moveFrame('down')">下一帧</button> -->
                    <el-button type="primary" @click="clearClip">清空</el-button>
                    <el-button type="primary" @click="moveFrame('up')">上一帧</el-button>
                    <el-button type="primary" @click="playVio">{{this.isPlay ?'暂停':'播放' }}</el-button>
                    <el-button type="primary" @click="moveFrame('down')">下一帧</el-button>
                    <el-button type="primary" @click="clipVideo">剪辑</el-button>
                </div>
                <!-- <button type="button" @click="setVoice">{{this.isSound ?'关闭声音':'打开声音' }}</button>
                    <span>音量:</span>
                    <input type="range" v-model="rangeData" max="100" min="0" @change="setRangevalue" /> -->
                <div class="block">
                    <div style="float:right;color: #72767b;font-size: 9px;">{{videoTime}}</div>
                    <el-slider v-model="nowVideoV" range :max="maxVideoV" :marks="sliderMarks" :format-tooltip="formatTooltip" @change="setNowVideoV">
                    </el-slider>
                </div>
            </div>

            <div id="kdx" class="axis" data-v-6b73916c="" style="height: 150px;margin-top:20px">
                <div class="axis-dragbar" data-v-56d3f446="" data-v-6b73916c=""></div>

                <div class="axis-play" data-v-6b73916c="" style="padding-left: 0px;">

                    <div class="axis-play-main" data-v-6b73916c="" style="height: 150px;"><svg id="svg" width="100000" height="16" data-v-dfd7e312="" data-v-6b73916c="">
                            <line y1="14.5" y2="14.5" x2="100000" id="y1" stroke-width="3" stroke="#d9d9d9" stroke-dasharray="2,8" data-v-dfd7e312=""></line>
                            <line y1="13" y2="13" x2="100000" id="y2" stroke-width="6" stroke="#d9d9d9" stroke-dasharray="2,48" data-v-dfd7e312=""></line>
                            <g font-size="8" fill="#86909c" text-anchor="middle" data-v-dfd7e312="" id="glist">
                            </g>
                        </svg>
                        <div class="axis-timeline axis-play-pic-bar" data-log="{mod: 'axis', c: 'dragTimeline'}" data-v-1cd08a9c="" data-v-6b73916c="" style="left: 9px;">
                            <div class="axis-timeline-top" data-v-1cd08a9c=""></div>
                        </div>
                        <div class="free-axis free-axis--free" data-v-67e71ce5="" style="width: 1386px; height: 164px;">
                            <ul class="free-axis__stickers" data-v-67e71ce5="" style="height: 64px;"></ul>

                            <!--v-if-->
                            <ul class="free-axis__audios" data-v-67e71ce5="" style="height: 64px;"></ul>
                            <!--v-if-->
                        </div>
                        <!--v-if-->
                        <!--v-if-->

                    </div>
                </div>
            </div>
        </el-col>
    </el-row>

</template>

<script>

    import $ from 'jquery'
    export default {
        props: {
            videopath: '',
            videoid: '',
            videoduration: 100000
        },
        data() {
            return {
                videoTime: '00:00:00.000',
                textindex: 0,
                splength: 10000,
                paddingLeft: 9,
                fristText: 0,
                lastText: 0,
                isShow: false,
                leftPx: 0,
                isPlay: false,
                isSound: true,
                rangeData: 10,
                oldRangeData: 10,
                maxVideoV: 1000,
                nowVideoV: 0,
                clickClipRow: {},
                lineInterval: null,
                clipArray: [], //存储剪辑的数组
                sliderMarks: {
                }

            }
        },
        mounted() {
            // 监听视频播放
            this.$refs.originVideo.addEventListener("play", () => {
                console.log("video is playing");
                //this.openTimer();
            });
            // 监听视频暂停
            this.$refs.originVideo.addEventListener("pause", () => {
                console.log("video is stop");
            });
            //监听视频可以播放
            this.$refs.originVideo.addEventListener("canplay", () => {
                this.isShow = true;
                this.maxVideoV = Math.floor(this.$refs.originVideo.duration * 100);
                //开始渲染进度条
                // this.sliderMarks = {};
                // var maxFor = Math.floor(this.maxVideoV / 100)
                // for (var i = 1; i <= maxFor; i++) {
                //     this.$set(this.sliderMarks, i * 100, this.transformSecondToVideoFormat(i * 100, true));
                // }
                //this.$refs.originVideo.playbackRate = 0.5;
            });
            //监听视频播放时间改变
            this.$refs.originVideo.addEventListener("timeupdate", () => {
                console.log("video is timeupdate");
                //console.log(this.$refs.originVideo.duration);
                this.nowVideoV = Math.floor(this.$refs.originVideo.currentTime * 100)
                if (this.clickClipRow.endTime !== undefined) {
                    if (this.nowVideoV >= Math.floor(this.clickClipRow.endTime * 100)) {
                        this.isPlay = true;
                        this.playVio();
                    }
                }
                this.videoTime = this.transformSecondToVideoFormat(this.nowVideoV, false);
                // console.log(this.transformSecondToVideoFormat(this.nowVideoV, true));
            });
            //视频停止
            this.$refs.originVideo.addEventListener("ended", () => {
                this.isPlay = false;
                clearInterval(this.lineInterval);
                this.syncKd();
            });
            this.splength = this.videoduration / 1000;
            debugger
            this.select_origin_file();
            this.initText((this.videoduration / 10));
            this.initData();
            this.initEvent();
            this.drag($(".axis-timeline"));
        },

        methods: {
            padLeft(num, n) {
                var y = '00000000000000000000000000000' + num; //爱几个0就几个，自己够用就行  
                return y.substr(y.length - n);
            },
            clacLeft: function () {
                this.leftPx = 200;
                if ($(".collapsedLogo").hasClass("logo-collapse")) {
                    this.leftPx = 64;
                }
            },
            initData() {
                var that = this;
                var lineWidth = (that.splength + 1) * 100;
                $("#svg").attr("width", lineWidth);
                $("#y1").attr("width", lineWidth);
                $("#y2").attr("width", lineWidth);

            },
            initEvent() {
                var that = this;

                $(".axis-play-main").click(function (e) {

                    that.clacLeft();
                    var positionX = e.pageX - that.leftPx + $(this).scrollLeft(); //获取当前鼠标相对div的X坐标  

                    if (positionX <= that.fristText) {
                        $(".axis-timeline").css("left", "10px");
                        that.textindex = 0;
                    }
                    else if (positionX >= (that.lastText + 10)) {
                        that.textindex = parseInt((that.lastText - that.paddingLeft) / 10);;
                        $(".axis-timeline").css("left", (that.lastText - that.paddingLeft + 10) + "px");
                    }
                    else {
                        that.textindex = parseInt((positionX - that.paddingLeft) / 10) - 2;
                        $(".axis-timeline").css("left", (that.textindex * 10 + 10) + "px");
                    }
                    that.syncVideo();
                });
            },
            initText(second) {
                this.lastText = this.splength * 10 * 10 + this.paddingLeft;
                var text = "";
                var j = 0;
                var time = '';
                var x = 0;
                var mm = 0;
                var hm = 0;
                var s = 0;
                for (var i = 0; i <= second; i++) {
                    x = i / 5 * 50;
                    if (i == 0) {
                        x = 10;
                    }
                    if (i % 10 == 0) {
                        s = i / 10;
                        if (s < 60) {
                            time = "00:" + this.padLeft(s, 2);
                        } else {
                            mm = parseInt(s / 60);
                            s = s - mm * 60;
                            time = this.padLeft(mm, 2) + ":" + this.padLeft(s, 2);
                        }
                        j = 0;
                        text += '<text x="' + x + '" y="7" data-v-dfd7e312="">' + time + '</text>';
                    } else if (i % 5 == 0) {
                        j++;
                        time = (j * 5) + "f";
                        text += '<text x="' + x + '" y="7" data-v-dfd7e312="">' + time + '</text>';
                    }
                }
                $("#glist").html(text);

            },
            syncVideo() {
                //分
                this.$refs.originVideo.currentTime = this.textindex / 10;

            },
            syncKd() {
                this.textindex = this.nowVideoV / 10;
                $(".axis-timeline").css("left", (this.textindex * 10 + 10) + "px");
            },
            getTime(hms) {
                var _xsw = hms - parseInt(hms);
                hms = hms * 100;
                //秒
                var s = parseInt(hms / 1000);
                //毫秒
                var hm = 0;
                var mm = 0;

                mm = parseInt(s / 60);

                var hm = parseInt(hms / 100);
                if (s > 60) {
                    s = s - mm * 60;
                }
                var time = this.padLeft(mm, 2) + ":" + this.padLeft(s, 2) + ":" + this.padLeft(hm, 2) + ".0" + (_xsw * 100).toFixed(0);
                return time;
            },
            drag(obj) {
                var gapX = 0;
                var that = this;
                obj.bind("mousedown", start);

                function start(event) {
                    if (event.button == 0) {//判断是否点击鼠标左键
                        that.clacLeft();

                        /*
        
                         * clientX和clientY代表鼠标当前的横纵坐标
        
                         * offset()该方法返回的对象包含两个整型属性：top 和 left，以像素计。此方法只对可见元素有效。
        
                         * bind()绑定事件，同样unbind解绑定，此效果的实现最后必须要解绑定，否则鼠标松开后拖拽效果依然存在
        
                         * getX获取当前鼠标横坐标和对象离屏幕左侧距离之差（也就是left）值，
        
                         * getY和getX同样道理，这两个差值就是鼠标相对于对象的定位，因为拖拽后鼠标和拖拽对象的相对位置是不变的
        
                         */

                        gapX = event.clientX - $(".axis-play-main").scrollLeft() - obj.offset().left;

                        console.log(gapX)
                        //gapY = event.clientY - obj.offset().top;

                        //movemove事件必须绑定到$(document)上，鼠标移动是在整个屏幕上的

                        $(document).bind("mousemove", move);

                        //此处的$(document)可以改为obj

                        $(document).bind("mouseup", stop);

                    }

                    return false;//阻止默认事件或冒泡

                }

                function move(event) {
                    console.log(event.clientX - gapX)
                    obj.css({
                        "left": (event.clientX - gapX - that.fristText - that.leftPx + 10) + "px"
                    });

                    return false;//阻止默认事件或冒泡

                }

                function stop() {

                    //解绑定，这一步很必要，前面有解释

                    $(document).unbind("mousemove", move);

                    $(document).unbind("mouseup", stop);

                }

            },


            select_origin_file(e) {
                //const file = e.target.files[0]
                // const url = window.webkitURL.createObjectURL(file)

                var url = this.videopath;//'http://www.si-tech.com.cn/pub-ui/images/radio/sitech.mp4';
                // var url = 'http://localhost:8300/statics/upload/media/video/1570610230377713664.mp4'
                this.$refs.originVideo.src = url;
                this.$refs.originVideo.volume = this.rangeData / 100;
                this.$refs.originVideo.muted = false;

            },
            playVio() {
                if (this.isPlay) {
                    this.isPlay = false;
                    this.$refs.originVideo.pause();
                    clearInterval(this.lineInterval);
                } else {
                    this.isPlay = true;
                    this.$refs.originVideo.play();
                    if (this.lineInterval) {
                        clearInterval(this.lineInterval);
                    }

                    this.lineInterval = setInterval(() => {
                        this.moveTimeline(1)
                    }, 10);
                }
                // alert(this.$refs.originVideo.duration)
                //console.log(this.nowVideoV);

            },
            moveTimeline() {
                $(".axis-timeline").css("left", (parseFloat($(".axis-timeline").css("left").replace("px", '')) + 1) + "px");
            },
            setVoice() {
                const isSoundThat = this.isSound;
                this.$refs.originVideo.muted = isSoundThat;
                if (isSoundThat) {
                    this.oldRangeData = this.rangeData;
                    this.rangeData = 0
                } else {
                    if (this.rangeData != 10) {
                        this.rangeData = this.oldRangeData;
                    }
                }
                this.isSound = !isSoundThat;
            },
            //拖动进度条
            setNowVideoV() {
                //this.nowVideoV = Math.floor(this.nowVideoV / 100) * 10000;
                this.clickClipRow = {};
                this.$refs.originVideo.currentTime = this.nowVideoV / 100;

                this.syncKd();
            },
            //设置视频声音
            setRangevalue() {
                if (this.rangeData == 0) {
                    this.isSound = false;
                } else {
                    this.$refs.originVideo.volume = this.rangeData / 100;
                    this.$refs.originVideo.muted = false;
                    this.isSound = true;
                }

            },
            //帧移动
            moveFrame(actionType) {
                this.isPlay = true;
                this.playVio();
                if (actionType == "up") {
                    if (this.nowVideoV > 0) {
                        this.$refs.originVideo.currentTime = this.nowVideoV / 100 - 0.04;
                    }
                } else {
                    if (this.nowVideoV < this.maxVideoV) {
                        this.$refs.originVideo.currentTime = this.nowVideoV / 100 + 0.04;
                    }
                }
                this.syncKd();
            },
            clipVideo() {
                //先视频暂停播放
                this.isPlay = true;
                this.playVio();
                var thisCilpTime = Math.floor(this.$refs.originVideo.currentTime * 100) / 100;
                if (this.clipArray.length == 0) {
                    this.clipArray.push({ "name": "第1段", "beginTime": 0, "endTime": thisCilpTime, "beginTime1": this.formatTooltip(0), "endTime1": this.formatTooltip(thisCilpTime * 100) })
                    this.clipArray.push({ "name": "第2段", "beginTime": thisCilpTime, "endTime": this.maxVideoV / 100, "beginTime1": this.formatTooltip(thisCilpTime * 100), "endTime1": this.formatTooltip(this.maxVideoV) })
                } else {
                    //循环所有 其实就是把节点获取出来
                    var arrList = [];
                    this.clipArray.forEach(element => {
                        arrList.push(element.endTime);
                    });
                    arrList.push(thisCilpTime);
                    arrList.sort((a, b) => {
                        return a - b// 按降序排
                    });
                    var setList = new Set(arrList);
                    //按照时间进行排序                    
                    //将之前的数据清空 重新渲染
                    this.clickClipRow = {};
                    this.clipArray = [];
                    var frist = 0;
                    var index = 1
                    setList.forEach(element => {
                        this.clipArray.push({ "name": "第" + index + "段", "beginTime": frist, "endTime": element, "beginTime1": this.formatTooltip(frist * 100), "endTime1": this.formatTooltip(element * 100) })
                        frist = element;
                        index++;
                    });
                }
            },
            getData() {
                return this.clipArray;
            },
            palyClipV(row) {
                this.clickClipRow = row;
                this.$refs.originVideo.currentTime = row.beginTime;
                this.isPlay = false;
                this.playVio();
                this.textindex = row.beginTime * 10;
                //alert(this.textindex);
                $(".axis-timeline").css("left", (this.textindex * 10 + 10) + "px");
            },
            clearClip() {
                this.clickClipRow = {};
                this.clipArray = [];
                this.$refs.originVideo.currentTime = 0;
                this.isPlay = true;
                this.playVio();
                $(".axis-timeline").css("left", "10px");
                that.textindex = 0;
            },
            formatTooltip(val) {
                return this.transformSecondToVideoFormat(val, false);
            },
            transformSecondToVideoFormat(value = 0, isSecond = false) {
                const totalMilliSecond = Number(value * 10)

                let hours = Math.floor((totalMilliSecond % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
                let minutes = Math.floor((totalMilliSecond % (1000 * 60 * 60)) / (1000 * 60))
                let second = Math.floor((totalMilliSecond % (1000 * 60)) / 1000)

                let hoursText = ''
                let minutesText = ''
                let secondText = ''
                let millisecondText = ''
                if (hours < 10) {
                    hoursText = `0${hours}`
                } else {
                    hoursText = `${hours}`
                }
                if (minutes < 10) {
                    minutesText = `0${minutes}`
                } else {
                    minutesText = `${minutes}`
                }
                if (second < 10) {
                    secondText = `0${second}`
                } else {
                    secondText = `${second}`
                }

                if (!isSecond) {
                    if (totalMilliSecond % 1000 < 10) {
                        millisecondText = `000${Math.floor((totalMilliSecond % 1000))}`
                    } else if (totalMilliSecond % 1000 < 100) {
                        millisecondText = `00${Math.floor((totalMilliSecond % 1000))}`
                    } else {
                        millisecondText = `0${Math.floor((totalMilliSecond % 1000))}`
                    }
                    //:${millisecondText}
                    return `${hoursText}:${minutesText}:${secondText}.${millisecondText}`
                } else {

                    return `${hoursText}:${minutesText}:${secondText}`
                }

            }
        }
    }
</script>

<style>
    .my-video {
        width: 600px;
        height: 400px;
    }
    .slider-container {
        padding: 0 16px;
    }
</style>
<style type="text/css">
    .axis-cover[data-v-6b73916c],
    .axis[data-v-6b73916c] {
        display: flex;
        flex-direction: column;
    }

    .axis-dragbar[data-v-56d3f446] {
        box-sizing: border-box;
        cursor: ns-resize;
        height: 8px;
        left: 0;
        position: absolute;
        right: 0;
        top: -5px;
    }

    .axis-play[data-v-6b73916c] {
        flex-grow: 1;
        overflow: hidden;
        position: relative;
        width: 100%;
    }

    .axis-play-main[data-v-6b73916c] {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        margin-left: 16px;
        min-height: 100%;
        overflow: hidden;
        overflow-x: auto;
        padding-left: 9px;
        position: relative;
    }

    svg[data-v-dfd7e312] {
        margin: 5px 0 17px;
        transform: translate(-1px);
    }

    /* svg:not(:root) {
                                                                                                                overflow: hidden;
                                                                                                            }

                                                                                                            :not(svg) {
                                                                                                                transform-origin: 0px 0px;
                                                                                                            } */

    .axis-play-main > [data-v-6b73916c] {
        flex-shrink: 0;
    }

    .axis-timeline[data-v-1cd08a9c] {
        height: 100%;
        opacity: 0.8;
        position: absolute;
        top: 0;
        width: 0;
        z-index: 3;
    }

    .axis-timeline-top[data-v-1cd08a9c] {
        cursor: grab;
        height: 10px;
        left: -10px;
        overflow: hidden;
        position: absolute;
        top: 0;
        width: 20px;
    }

    .axis-timeline-top[data-v-1cd08a9c]:before {
        background: #ff4057;
        border-radius: 3px;
        content: "";
        height: 10px;
        left: 5px;
        position: absolute;
        top: -3px;
        transform: rotate(45deg);
        width: 10px;
    }

    .free-axis--free[data-v-67e71ce5] {
        overflow: hidden;
        padding: 4px 50% 0 6px;
        transform: translate(-6px, -4px);
    }

    .free-axis[data-v-67e71ce5] {
        position: relative;
    }

    .free-axis ul[data-v-67e71ce5] {
        position: relative;
        transform: translate(-6px, -4px);
    }

    .free-axis__stickers[data-v-67e71ce5] {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
    }

    .free-axis ul ~ ul[data-v-67e71ce5] {
        margin-top: 4px;
    }

    .free-axis ul[data-v-67e71ce5] {
        position: relative;
        transform: translate(-6px, -4px);
    }

    .axis-play-main > [data-v-6b73916c] {
        flex-shrink: 0;
    }

    .axis-timeline[data-v-1cd08a9c] {
        height: 100%;
        opacity: 0.8;
        position: absolute;
        top: 0;
        width: 0;
        z-index: 3;
    }

    .axis-timeline[data-v-1cd08a9c]:after {
        background: #ff4057;
        box-sizing: border-box;
        content: "";
        height: 100%;
        pointer-events: none;
        position: absolute;
        transform: translateX(-1px) scaleX(0.5);
        width: 2px;
    }
</style>