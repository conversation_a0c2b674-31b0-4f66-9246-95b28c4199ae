<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
            <div class="top">
                <el-button-group>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-date-picker style="width: 120px" v-model="filter.yearMonth" type="month" format="yyyyMM"
                            value-format="yyyyMM" placeholder="月份"></el-date-picker>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.createdUserName" clearable filterable placeholder="推荐人"
                            style="width: 150px">
                            <el-option v-for="item in createdUserNameList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.createUserArea" clearable filterable placeholder="地区"
                            style="width: 150px">
                            <el-option v-for="item in createUserAreaList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.createUserRole" clearable filterable placeholder="职位"
                            style="width: 150px">
                            <el-option v-for="item in createUserRoleList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.createUserDeptName" clearable filterable placeholder="架构"
                            style="width: 150px">
                            <el-option v-for="item in createUserDeptNameList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>

                    <el-button type="primary" @click="onSearch()">查询</el-button>
                </el-button-group>
            </div>
        </template>
        <vxetablebase :id="'HotSaleBrandPushNewWan0202408041719'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :border="true"
            @sortchange='sortchange' @select='selectchange' :tableData='tableData' :tableCols='tableCols'
            :showsummary='true' :summaryarry='summaryarry' :isSelection="false" :isSelectColumn="false"
            style="width: 100%;  margin: 0" v-loading="listLoading" :height="'100%'">
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <!-- <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" /> -->
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import datepicker from '@/views/customerservice/datepicker';
import { pickerOptions } from '@/utils/tools';
import dayjs from 'dayjs';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    GetHotSaleBrandPushNewReport, GetHotSaleBrandPushNewReportUsers
} from '@/api/operatemanage/productalllink/alllink'
const tableCols = [
    { sortable: 'custom', width: '80', align: 'center', prop: 'createdUserName', label: '推荐人', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'createUserArea', label: '地区', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'createUserRole', label: '职位', },
    { sortable: 'custom', width: '200', align: 'center', prop: 'createUserDeptName', label: '架构', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'wanOrderStyleCount', label: '万单数量', },//万单款式编码个数
    { sortable: 'custom', width: '120', align: 'center', prop: 'profit3', label: '毛三利润', },
];
export default {
    name: "HotSaleBrandPushNewWan0",
    components: {
        MyContainer, datepicker, vxetablebase
    },
    data() {
        return {
            that: this,
            auditVisible: false,
            activities: [],
            timeRanges: [],
            pickerOptions,
            filter: {
                yearMonth: dayjs().subtract(1, 'month').format('YYYYMM'),
            },
            pager: { OrderBy: "wanOrderStyleCount", IsAsc: false },
            tableCols,
            tableData: [],
            total: 0,
            listLoading: false,
            pageLoading: false,
            sels: [],
            selids: [],
            summaryarry: {},

            createdUserNameList: [],
            createUserAreaList: [],
            createUserRoleList: [],
            createUserDeptNameList: [],
        }
    },
    async mounted() {
        await this.getSelectData();
        this.onSearch();
    },
    computed: {
    },
    methods: {
        async getSelectData() {
            let ret = await GetHotSaleBrandPushNewReportUsers({ getType: 1 });
            this.createdUserNameList = ret.data;

            let ret2 = await GetHotSaleBrandPushNewReportUsers({ getType: 2 });
            this.createUserAreaList = ret2.data;

            let ret3 = await GetHotSaleBrandPushNewReportUsers({ getType: 3 });
            this.createUserRoleList = ret3.data;

            let ret4 = await GetHotSaleBrandPushNewReportUsers({ getType: 4 });
            this.createUserDeptNameList = ret4.data;
        },
        async onSearch() {
            //this.$refs.pager.setPage(1);
            await this.getList();
        },
        getParam() {
            //let pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                //...pager,
                ...this.pager,
            };
            return params;
        },
        async getList() {
            let param = this.getParam();
            this.listLoading = true
            const res = await GetHotSaleBrandPushNewReport(param)
            this.listLoading = false
            console.log(res);
            if (res?.success) {
                this.tableData = res.data.list;
                this.total = res.data.total;
                this.summaryarry = res.data.summary;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport() {
            let params = this.getParam();
            this.listLoading = true
            const res = await ExportPurchaseOrderNewApprovePageList(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '新品采购单审批_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },

        async jumpReport1(row, index, title,internationalType) {
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBrandPushNewReport` + index + `.vue`,
                title: title,
                args: {
                    internationalType: internationalType,
                    createdUserId: row.createdUserId,
                    timerange: this.filter.timerange,
                },
                height: '650px',
                width: '1000px',
                callOk: this.afterSave
            });
        },
        afterSave(buyNo) {
            
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}
</style>
