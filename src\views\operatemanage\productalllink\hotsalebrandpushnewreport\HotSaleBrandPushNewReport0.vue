<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
            <div class="top">
                <el-button-group>

                    <!-- <el-button style="padding: 0;margin: 0;border: 0;">
                        <el-radio-group v-model="filter.internationalType" @change="onSearch()">
                            <el-radio-button :label="-1">全部</el-radio-button>
                            <el-radio-button :label="0">国内</el-radio-button>
                            <el-radio-button :label="1">跨境</el-radio-button>
                        </el-radio-group>
                    </el-button> -->
                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-radio-group v-model="filter.isSj" @change="onIsSjChange">
                            <el-radio-button label="发生维度"></el-radio-button>
                            <el-radio-button label="实际维度"></el-radio-button>
                        </el-radio-group>
                    </el-button>
                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期" clearable :picker-options="pickerOptions"
                            style="width: 240px;" :value-format="'yyyy-MM-dd'" />
                    </el-button>

                    <!-- <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="推品开始日期" end-placeholder="推品结束日期" clearable
                            :picker-options="pickerOptions" style="width: 240px;" :value-format="'yyyy-MM-dd'" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-date-picker v-model="filter.timerange2" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="选中开始日期" end-placeholder="选中结束日期" clearable
                            :picker-options="pickerOptions" style="width: 240px;" :value-format="'yyyy-MM-dd'" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-date-picker v-model="filter.timerange3" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="建立开始日期" end-placeholder="建立结束日期" clearable
                            :picker-options="pickerOptions" style="width: 240px;" :value-format="'yyyy-MM-dd'" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-date-picker v-model="filter.timerange4" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="归档开始日期" end-placeholder="归档结束日期" clearable
                            :picker-options="pickerOptions" style="width: 240px;" :value-format="'yyyy-MM-dd'" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-date-picker v-model="filter.timerange5" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="有销量开始日期" end-placeholder="有销量结束日期" clearable
                            :picker-options="pickerOptions" style="width: 240px;" :value-format="'yyyy-MM-dd'" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-date-picker v-model="filter.timerange6" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="进货开始日期" end-placeholder="进货结束日期" clearable
                            :picker-options="pickerOptions" style="width: 240px;" :value-format="'yyyy-MM-dd'" />
                    </el-button> -->

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.createdUserNames" clearable filterable placeholder="推荐人" multiple
                            collapse-tags style="width: 150px">
                            <el-option v-for="item in createdUserNameList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.isDelete" clearable filterable placeholder="在职状态"
                            style="width: 120px">
                            <el-option label="在职" :value="false" />
                            <el-option label="离职" :value="true" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.hired_dateDes" clearable filterable placeholder="入职时间"
                            style="width: 120px">
                            <el-option label="3个月以内" value="3个月以内" />
                            <el-option label="超过3个月" value="超过3个月" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.createUserAreas" clearable filterable placeholder="地区" multiple
                            collapse-tags style="width: 140px">
                            <el-option v-for="item in createUserAreaList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.createUserRoles" clearable filterable placeholder="职位" multiple
                            collapse-tags style="width: 250px">
                            <el-option v-for="item in createUserRoleList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.createUserDeptNames" clearable filterable placeholder="架构" multiple
                            collapse-tags style="width: 250px">
                            <el-option v-for="item in createUserDeptNameList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>

                    <el-button type="primary" @click="onSearch()">查询</el-button>
                    <el-button type="primary" @click="onBaiMingDan()">采购推新白名单</el-button>
                </el-button-group>
            </div>
        </template>
        <vxetablebase :id="'HotSaleBrandPushNewReport0202408041714'" ref="table" :that='that' :isIndex='true'
            :hasexpand='true' :tablefixed='true' :border="true" @sortchange='sortchange' @select='selectchange'
            :tableData='tableData' :tableCols='tableCols' :showsummary='true' :summaryarry='summaryarry'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="listLoading"
            @summaryClick='onsummaryClick' :height="'100%'">
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <!-- <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" /> -->
        </template>

        <el-dialog title="推新趋势图" :visible.sync="indexChatDialog.visible" width="80%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag v-loading="indexChatDialog.loading">
            <buschar ref="HotSaleBrandPushNewReport0buschar1" v-if="!indexChatDialog.loading"
                :analysisData="indexChatData1" :legendChanges="legendChanges1">
            </buschar>
        </el-dialog>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import datepicker from '@/views/customerservice/datepicker';
import { pickerOptions } from '@/utils/tools';
import { formatTime } from "@/utils";
import dayjs from 'dayjs';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    GetHotSaleBrandPushNewReport, GetHotSaleBrandPushNewReportUsers, GetHotSaleBrandPushNewReportChartData,
    GetHotSaleBrandPushNewReport_Sj
} from '@/api/operatemanage/productalllink/alllink'
import buschar from '@/components/Bus/buschar';
const tableCols = [
    { sortable: 'custom', width: '80', align: 'center', prop: 'createdUserName', label: '推荐人', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'isDelete', label: '在职状态', formatter: (row) => row.isDelete == true ? "离职" : "在职" },
    { sortable: 'custom', width: '80', align: 'center', prop: 'hired_date', label: '入职日期', formatter: (row) => row.hired_date == null ? "" : formatTime(row.hired_date, "YYYY-MM-DD") },
    { sortable: 'custom', width: '80', align: 'center', prop: 'createUserArea', label: '地区', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'createUserRole', label: '职位', },
    { sortable: 'custom', width: '200', align: 'center', prop: 'createUserDeptName', label: '架构', },

    {
        align: 'center', prop: '', label: '推品数量', merge: true, prop: 'mergeField', width: '60', summaryEvent: true,
        cols: [
            { sortable: 'custom', width: '60', align: 'center', prop: 'pushNewCount', label: '全部', type: 'click', handle: (that, row) => that.jumpReport1(row, 1, "推品数量", -1), color: 'red', summaryEvent: true },
            { sortable: 'custom', width: '60', align: 'center', prop: 'pushNewCount2', label: '国内', type: 'click', handle: (that, row) => that.jumpReport1(row, 1, "推品数量", 0), summaryEvent: true },
            { sortable: 'custom', width: '60', align: 'center', prop: 'pushNewCount3', label: '跨境', type: 'click', handle: (that, row) => that.jumpReport1(row, 1, "推品数量", 1), summaryEvent: true },
        ]
    },

    {
        align: 'center', prop: '', label: '推品被选中数量', merge: true, prop: 'mergeField1', width: '60', summaryEvent: true,
        cols: [
            { sortable: 'custom', width: '60', align: 'center', prop: 'chooseCount', label: '全部', type: 'click', handle: (that, row) => that.jumpReport1(row, 2, "推品被选中数量", -1), color: 'red', summaryEvent: true },
            { sortable: 'custom', width: '60', align: 'center', prop: 'chooseCount2', label: '国内', type: 'click', handle: (that, row) => that.jumpReport1(row, 2, "推品被选中数量", 0), summaryEvent: true },
            { sortable: 'custom', width: '60', align: 'center', prop: 'chooseCount3', label: '跨境', type: 'click', handle: (that, row) => that.jumpReport1(row, 2, "推品被选中数量", 1), summaryEvent: true },
        ]
    },

    {
        align: 'center', prop: '', label: '已创建系列编码数量', merge: true, prop: 'mergeField2', width: '60', summaryEvent: true,
        cols: [
            { sortable: 'custom', width: '60', align: 'center', prop: 'docCount', label: '全部', type: 'click', handle: (that, row) => that.jumpReport1(row, 3, "已创建系列编码数量", -1), color: 'red', summaryEvent: true },
            { sortable: 'custom', width: '60', align: 'center', prop: 'docCount2', label: '国内', type: 'click', handle: (that, row) => that.jumpReport1(row, 3, "已创建系列编码数量", 0), summaryEvent: true },
            { sortable: 'custom', width: '60', align: 'center', prop: 'docCount3', label: '跨境', type: 'click', handle: (that, row) => that.jumpReport1(row, 3, "已创建系列编码数量", 1), summaryEvent: true },
        ]
    },

    {
        align: 'center', prop: '', label: '选品后被归档数量', merge: true, prop: 'mergeField3', width: '60', summaryEvent: true,
        cols: [
            { sortable: 'custom', width: '60', align: 'center', prop: 'chooseCloseCount', label: '全部', type: 'click', handle: (that, row) => that.jumpReport1(row, 4, "选品后被归档数量", -1), color: 'red', summaryEvent: true },
            { sortable: 'custom', width: '60', align: 'center', prop: 'chooseCloseCount2', label: '国内', type: 'click', handle: (that, row) => that.jumpReport1(row, 4, "选品后被归档数量", 0), summaryEvent: true },
            { sortable: 'custom', width: '60', align: 'center', prop: 'chooseCloseCount3', label: '跨境', type: 'click', handle: (that, row) => that.jumpReport1(row, 4, "选品后被归档数量", 1), summaryEvent: true },
        ]
    },

    // { align: 'center', prop: '', label: '已上架有销量' , merge: true, prop: 'mergeField4', width: '60', 
    //     cols: [
    //         { sortable: 'custom', width: '60', align: 'center', prop: 'saleCount', label: '全部', type: 'click', handle: (that, row) => that.jumpReport1(row, 5, "已上架有销量",-1) },
    //         { sortable: 'custom', width: '60', align: 'center', prop: 'saleCount2', label: '国内', type: 'click', handle: (that, row) => that.jumpReport1(row, 5, "已上架有销量",0) },
    //         { sortable: 'custom', width: '60', align: 'center', prop: 'saleCount3', label: '跨境', type: 'click', handle: (that, row) => that.jumpReport1(row, 5, "已上架有销量",1) },
    //     ]
    // },

    // { align: 'center', prop: '', label: '已进货' , merge: true, prop: 'mergeField5', width: '60', 
    //     cols: [
    //         { sortable: 'custom', width: '60', align: 'center', prop: 'purchaseCount', label: '全部', type: 'click', handle: (that, row) => that.jumpReport1(row, 6, "已进货",-1) },
    //         { sortable: 'custom', width: '60', align: 'center', prop: 'purchaseCount2', label: '国内', type: 'click', handle: (that, row) => that.jumpReport1(row, 6, "已进货",0) },
    //         { sortable: 'custom', width: '60', align: 'center', prop: 'purchaseCount3', label: '跨境', type: 'click', handle: (that, row) => that.jumpReport1(row, 6, "已进货",1) },
    //     ]
    // },



    {
        align: 'center', prop: '', label: '已上架有销量/已进货', merge: true, prop: 'mergeField6', width: '60', summaryEvent: true,
        cols: [
            { sortable: 'custom', width: '60', align: 'center', prop: 'salePurchaseCount', label: '全部', type: 'click', handle: (that, row) => that.jumpReport1(row, 7, "已上架有销量/已进货", -1), color: 'red', summaryEvent: true },
            { sortable: 'custom', width: '60', align: 'center', prop: 'salePurchaseCount2', label: '国内', type: 'click', handle: (that, row) => that.jumpReport1(row, 7, "已上架有销量/已进货", 0), summaryEvent: true },
            { sortable: 'custom', width: '60', align: 'center', prop: 'purchaseCountOther2', label: '国内2', type: 'click', handle: (that, row) => that.jumpReport1(row, 7, "已上架有销量/已进货", 10), summaryEvent: true },
            { sortable: 'custom', width: '60', align: 'center', prop: 'salePurchaseCount3', label: '跨境', type: 'click', handle: (that, row) => that.jumpReport1(row, 7, "已上架有销量/已进货", 1), summaryEvent: true },
        ]
    },


    { sortable: 'custom', width: '120', align: 'center', prop: 'rate1', label: '推品/选中', formatter: (row) => (row.rate1) + '%', summaryEvent: true },
    { sortable: 'custom', width: '120', align: 'center', prop: 'rate2', label: '选中/建立', formatter: (row) => (row.rate2) + '%', summaryEvent: true },
    { sortable: 'custom', width: '160', align: 'center', prop: 'rate3', label: '(有销量+已进货)/建立', formatter: (row) => (row.rate3) + '%', summaryEvent: true },
];
export default {
    name: "HotSaleBrandPushNewReport0",
    components: {
        MyContainer, datepicker, vxetablebase, buschar,
    },
    data() {
        return {
            that: this,
            auditVisible: false,
            activities: [],
            timeRanges: [],
            pickerOptions,
            filter: {
                isSj: "发生维度",
                internationalType: null,
                timerange: [(dayjs().subtract(1, 'day').format('YYYY-MM') + '-01'), dayjs().subtract(1, 'day').format('YYYY-MM-DD')],
                timerange2: [],
                timerange3: [],
                timerange4: [],
                timerange5: [],
                timerange6: [],
                createdStartDate: null,
                createdEndDate: null,
                createdUserNames: [],
                createUserAreas: [],
                createUserRoles: [],
                createUserDeptNames: [],
                hired_dateDes: "",
            },
            pager: { OrderBy: "pushNewCount", IsAsc: false },
            tableCols,
            tableData: [],
            total: 0,
            listLoading: false,
            pageLoading: false,
            sels: [],
            selids: [],
            summaryarry: {},

            createdUserNameList: [],
            createUserAreaList: [],
            createUserRoleList: [],
            createUserDeptNameList: [],

            indexChatDialog: {
                title: "",
                visible: false,
                loading: false,
            },
            indexChatData1: {},
            indexChatSelectedLegend1: [],

        }
    },
    async mounted() {
        await this.getSelectData();
        this.onSearch();
    },
    computed: {
    },
    methods: {
        async getSelectData() {
            let ret = await GetHotSaleBrandPushNewReportUsers({ getType: 1 });
            this.createdUserNameList = ret.data;

            let ret2 = await GetHotSaleBrandPushNewReportUsers({ getType: 2 });
            this.createUserAreaList = ret2.data;

            let ret3 = await GetHotSaleBrandPushNewReportUsers({ getType: 3 });
            this.createUserRoleList = ret3.data;

            let ret4 = await GetHotSaleBrandPushNewReportUsers({ getType: 4 });
            this.createUserDeptNameList = ret4.data;
        },
        async onIsSjChange() {
            await this.onSearch();
        },
        async onSearch() {
            //this.$refs.pager.setPage(1);
            await this.getList();
        },
        getParam() {
            //推品日期
            if (this.filter.timerange && this.filter.timerange.length == 2) {
                this.filter.createdStartDate = this.filter.timerange[0];
                this.filter.createdEndDate = this.filter.timerange[1];
            }
            else {
                this.filter.createdStartDate = null;
                this.filter.createdEndDate = null;
            }
            // //选中日期
            // if (this.filter.timerange2 && this.filter.timerange2.length == 2) {
            //     this.filter.chooseStartDate = this.filter.timerange2[0];
            //     this.filter.chooseEndDate = this.filter.timerange2[1];
            // }
            // else {
            //     this.filter.chooseStartDate = null;
            //     this.filter.chooseEndDate = null;
            // }
            // //建立日期
            // if (this.filter.timerange3 && this.filter.timerange3.length == 2) {
            //     this.filter.docStartDate = this.filter.timerange3[0];
            //     this.filter.docEndDate = this.filter.timerange3[1];
            // }
            // else {
            //     this.filter.docStartDate = null;
            //     this.filter.docEndDate = null;
            // }
            // //归档日期
            // if (this.filter.timerange4 && this.filter.timerange4.length == 2) {
            //     this.filter.chooseCloseStartDate = this.filter.timerange4[0];
            //     this.filter.chooseCloseEndDate = this.filter.timerange4[1];
            // }
            // else {
            //     this.filter.chooseCloseStartDate = null;
            //     this.filter.chooseCloseEndDate = null;
            // }
            // //有销量日期
            // if (this.filter.timerange5 && this.filter.timerange5.length == 2) {
            //     this.filter.saleStartDate = this.filter.timerange5[0];
            //     this.filter.saleEndDate = this.filter.timerange5[1];
            // }
            // else {
            //     this.filter.saleStartDate = null;
            //     this.filter.saleEndDate = null;
            // }
            // //进货日期
            // if (this.filter.timerange6 && this.filter.timerange6.length == 2) {
            //     this.filter.purchaseStartDate = this.filter.timerange6[0];
            //     this.filter.purchaseEndDate = this.filter.timerange6[1];
            // }
            // else {
            //     this.filter.purchaseStartDate = null;
            //     this.filter.purchaseEndDate = null;
            // }


            //let pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                //...pager,
                ...this.pager,
            };
            return params;
        },
        async getList() {
            let param = this.getParam();
            this.listLoading = true
            let res = null;
            if (this.filter.isSj == "实际维度") {
                res = await GetHotSaleBrandPushNewReport_Sj(param)
            }
            else {
                res = await GetHotSaleBrandPushNewReport(param)
            }
            this.listLoading = false
            if (res?.success) {
                this.tableData = res.data.list;
                this.total = res.data.total;
                console.log(res.data.summary.rate1_sum, "res.data.summary.rate1_sum");

                if (res.data.summary.rate1_sum) {
                    let v = parseFloat(res.data.summary.rate1_sum.slice(0, -1));
                    if (v >= 100) {
                        res.data.summary.rate1_sum = v.toFixed(0).toString() + "%";
                    }
                }
                if (res.data.summary.rate2_sum) {
                    let v = parseFloat(res.data.summary.rate2_sum.slice(0, -1));
                    if (v >= 100) {
                        res.data.summary.rate2_sum = v.toFixed(0).toString() + "%";
                    }
                }
                if (res.data.summary.rate3_sum) {
                    let v = parseFloat(res.data.summary.rate3_sum.slice(0, -1));
                    if (v >= 100) {
                        res.data.summary.rate3_sum = v.toFixed(0).toString() + "%";
                    }
                }
                this.summaryarry = res.data.summary;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport() {
            let params = this.getParam();
            this.listLoading = true
            const res = await ExportPurchaseOrderNewApprovePageList(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '新品采购单审批_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },

        async jumpReport1(row, index, title, internationalType) {
            let internationalType2 = -10;
            if (internationalType == 10) {
                internationalType = 0;
                internationalType2 = 2
            }
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBrandPushNewReport` + index + `.vue`,
                title: title,
                args: {
                    isSj: this.filter.isSj,
                    internationalType: internationalType,
                    internationalType2: internationalType2,
                    createdUserId: row.createdUserId,
                    timerange: this.filter.timerange,
                },
                height: '650px',
                width: '80%',
                callOk: this.afterSave
            });
        },
        afterSave(buyNo) {
            this.$router.push({ path: '/inventory/purchaseindex', query: { buyNo } })
        },

        async legendChanges1(selected) {
            this.indexChatSelectedLegend1 = selected;
        },
        async onsummaryClick(property) {
            this.indexChatDialog.visible = true;
            let param = this.getParam();
            this.indexChatDialog.loading = true;
            let res = await GetHotSaleBrandPushNewReportChartData(param);
            this.indexChatDialog.loading = false;

            if (property == "pushNewCount" || property == "pushNewCount2" || property == "pushNewCount3") {
                res.selectedLegend = ["推-全部", "推-国内", "推-跨境"];
            }
            else if (property == "chooseCount" || property == "chooseCount2" || property == "chooseCount3") {
                res.selectedLegend = ["被选-全部", "被选-国内", "被选-跨境"];
            }
            else if (property == "docCount" || property == "docCount2" || property == "docCount3") {
                res.selectedLegend = ["建立-全部", "建立-国内", "建立-跨境"];
            }
            else if (property == "chooseCloseCount" || property == "chooseCloseCount2" || property == "chooseCloseCount3") {
                res.selectedLegend = ["被归档-全部", "被归档-国内", "被归档-跨境"];
            }
            else if (property == "salePurchaseCount" || property == "salePurchaseCount2" || property == "salePurchaseCount3") {
                res.selectedLegend = ["销量|进货-全部", "销量|进货-国内", "销量|进货-跨境"];
            }
            else if (property == "rate1") {
                res.selectedLegend = ["推品/选中"];
            }
            else if (property == "rate2") {
                res.selectedLegend = ["选中/建立"];
            }
            else if (property == "rate3") {
                res.selectedLegend = ["销量|进货/建立"];
            }


            this.indexChatData1 = res;
        },

        async onBaiMingDan() {
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBrandPushNewReport8.vue`,
                title: "采购推新白名单",
                args: {
                    timerange2: this.filter.timerange,
                },
                height: '650px',
                width: '80%',
                callOk: this.afterSave8
            });
        },
        afterSave8(buyNo) {
            this.$router.push({ path: '/inventory/purchaseindex', query: { buyNo } })
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}

::v-deep .el-select__tags-text {
    max-width: 120px;
}
</style>
