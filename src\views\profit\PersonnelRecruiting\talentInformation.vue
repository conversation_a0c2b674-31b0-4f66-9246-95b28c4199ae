<template>
    <div>
        <!-- 人才信息 -->
        <talentInformationTop :isEdit="isEdit" :candidateInfo="candidateInfo" ref="talentInformationTop">
        </talentInformationTop>
        <!-- 基础信息 -->
        <talentBaseInfor :isEdit="isEdit" :candidateInfo="candidateInfo" ref="talentBaseInfor"></talentBaseInfor>
        <!-- 工作经历 -->
        <workExperience :isEdit="isEdit" :candidateInfo="candidateInfo" ref="workExperience"></workExperience>
        <!-- 补充信息 -->
        <supplementaryInformation :isEdit="isEdit" :candidateInfo="candidateInfo" ref="supplementaryInformation">
        </supplementaryInformation>
        <!-- 职位信息 -->
        <positionInformation :isEdit="isEdit" :candidateInfo="candidateInfo" ref="positionInformation">
        </positionInformation>
        <!-- 合同签订 -->
        <contract :isEdit="isEdit" :candidateInfo="candidateInfo" ref="contract"></contract>
    </div>
</template>
  
<script>
import talentInformationTop from "@/views/profit/PersonnelRecruiting/talentInformationTop";
import talentBaseInfor from "@/views/profit/PersonnelRecruiting/talentBaseInfor";
import supplementaryInformation from "@/views/profit/PersonnelRecruiting/supplementaryInformation";
import workExperience from "@/views/profit/PersonnelRecruiting/workExperience";
import positionInformation from "@/views/profit/PersonnelRecruiting/positionInformation";
import contract from "@/views/profit/PersonnelRecruiting/contract";
import { addCandidate, editCandidate } from "@/api/profit/hr"
import { formatTime } from "@/utils/tools";

export default {
    name: 'talentInformation',//,人才信息
    components: {
        talentInformationTop, talentBaseInfor, supplementaryInformation, workExperience,
        positionInformation, contract,
    },
    props: {
        isEdit: {
            type: Boolean,
            default: () => { return false; }
        },
        candidateInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
        talentId: {
            default: () => { return 0 }
        },
    },
    data () {
        return {
            talentInfo: {
            },

        }
    },
    mounted () {
        if (!this.candidateInfo.candidateId) {
            this.resetFrom();
        }
        if (this.candidateInfo.birthDate) {
            this.candidateInfo.birthDate = formatTime(this.candidateInfo.birthDate, 'YYYY-MM-DD')
        }
        if (this.candidateInfo.graduationDate) {
            this.candidateInfo.graduationDate = formatTime(this.candidateInfo.graduationDate, 'YYYY-MM-DD')
        }
        if (this.candidateInfo.employmentDate) {
            this.candidateInfo.employmentDate = formatTime(this.candidateInfo.employmentDate, 'YYYY-MM-DD')
        }
        if (this.candidateInfo.seniorityDate) {
            this.candidateInfo.seniorityDate = formatTime(this.candidateInfo.seniorityDate, 'YYYY-MM-DD')
        }
        if (this.candidateInfo.conversionDate) {
            this.candidateInfo.conversionDate = formatTime(this.candidateInfo.conversionDate, 'YYYY-MM-DD')
        }
        if (this.candidateInfo.contractSigningDate) {
            this.candidateInfo.contractSigningDate = formatTime(this.candidateInfo.contractSigningDate, 'YYYY-MM-DD')
        }
        if (this.candidateInfo.contractExpirationDate) {
            this.candidateInfo.contractExpirationDate = formatTime(this.candidateInfo.contractExpirationDate, 'YYYY-MM-DD')
        }
        if (this.candidateInfo.commercialInsurancePurchaseDate) {
            this.candidateInfo.commercialInsurancePurchaseDate = formatTime(this.candidateInfo.commercialInsurancePurchaseDate, 'YYYY-MM-DD')
        }
        if (this.candidateInfo.workExperienceList && this.candidateInfo.workExperienceList.length > 0) {
            this.candidateInfo.workExperienceList.forEach((element, i) => {
                this.candidateInfo.workExperienceList[i].employmentEndDate = formatTime(element.employmentEndDate, 'YYYY-MM-DD')
                this.candidateInfo.workExperienceList[i].employmentStartDate = formatTime(element.employmentStartDate, 'YYYY-MM-DD')
            });
        }
    },
    methods: {
        //在职管理查看详情，默认打开职位信息
        openPosition () {
            this.$refs.positionInformation.isOpen = true;
            this.$refs.positionInformation.isShowPositionChgLog = true;
            this.$refs.positionInformation.activeName = 'content';
        },
        resetFrom () {
            this.$refs.talentInformationTop.reset();
            this.$refs.talentBaseInfor.reset();
            this.$refs.supplementaryInformation.reset();
            this.$refs.workExperience.reset();
            this.$refs.positionInformation.reset();
            this.$refs.contract.reset();
        },
        getTalent () {
            // this.talentId 获取人才信息
        },
        //提交
        submitForm (formName) {
            let from = {
                ...this.candidateInfo,
                ...this.$refs.talentInformationTop.ruleForm, ...this.$refs.talentBaseInfor.ruleForm,
                ...this.$refs.supplementaryInformation.ruleForm, ...this.$refs.workExperience.workExperience,
                ...this.$refs.positionInformation.ruleForm, ...this.$refs.contract.ruleForm,
            }
            from.interviewMeetingList = from.interviewMeetingList.filter(item => item.meetingType != null);
            if (!from.initialTestResult) {
                from.finalTestResult = null;
            }
            if (!from.isContract) {
                from.contractSigningDate = null;
                from.contractExpirationDate = null;
                from.remainingDates = null;
                from.contractNumber = null;
            }
            if (!from.hasCommercialInsurance) {
                from.commercialInsurancePurchaseDate = null;
            }
            this.$refs.talentInformationTop.$refs.topForm.validate((valid, Obj) => {
                if (valid) {
                    this.$refs.talentBaseInfor.$refs.baseForm.validate((va, ob) => {
                        if (va) {
                            this.$emit("openLoading");
                            if (this.candidateInfo.candidateId) {
                                from.candidateId = this.candidateInfo.candidateId;
                                // 修改
                                editCandidate(from).then(res => {
                                    this.$emit("closeLoading");
                                    if (res.success) {
                                        this.$message({ message: '保存成功', type: "success" })
                                        this.resetFrom();
                                        this.$emit("closeDialog");
                                    }
                                })
                            } else {
                                // 新增
                                addCandidate(from).then(res => {
                                    this.$emit("closeLoading");
                                    if (res.success) {
                                        this.$message({ message: '新增成功', type: "success" })
                                        this.resetFrom();
                                        this.$emit("closeDialog")
                                    }
                                })
                            }

                        } else {
                            for (const prop in ob) {
                                this.$message({ message: ob[prop][0].message, type: "error" })
                                return false;
                            }
                        }

                    })
                } else {
                    for (const prop in Obj) {
                        this.$message({ message: Obj[prop][0].message, type: "error" })
                        return false;
                    }
                }
            })
            // this.$refs[formName].validate((valid) => {
            //     if (valid) {
            //         alert('submit!');
            //     } else {
            //         console.log('error submit!!');
            //         return false;
            //     }
            // });
        },
        //获取界面最新数据,帮忙刷新钉钉同步信息的时候丢失用户填写的其他信息
        getform(){
            let form = {
                ...this.candidateInfo,
                ...this.$refs.talentInformationTop.ruleForm, ...this.$refs.talentBaseInfor.ruleForm,
                ...this.$refs.supplementaryInformation.ruleForm, ...this.$refs.workExperience.workExperience,
                ...this.$refs.positionInformation.ruleForm, ...this.$refs.contract.ruleForm,
            }

            return form;
        }
    }
}
</script>
  
<style scoped>
.title {
    cursor: pointer;
}

.ruleForm {
    padding: 10px;
}

.des-box {
    padding: 0 10px 0 30px
}

::v-deep .el-descriptions__title {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
}

::v-deep .el-descriptions__header {
    margin-bottom: 10px;
}

::v-deep .el-divider--horizontal {
    margin: 5px 0;
}

::v-deep .el-descriptions-item__label:not(.is-bordered-label) {
    margin-right: 0px;
}

.footer-box {
    width: 100%;
    display: flex;
    justify-content: flex-end;
}
</style>