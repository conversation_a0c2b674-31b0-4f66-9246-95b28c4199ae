<template>
  <my-container v-loading="pageLoading">
    <div>
      <div>
        <div style="width:100%;display: flex;margin-bottom:5px;">
          <div style="width:150px;display: flex;">
            <el-button size="mini" type="primary" @click="onadd" v-if="checkPermission('api:packprocess:PackagesSetProcessing:DeployPriceSetOperate')">新增</el-button>
            <el-button size="mini" type="primary" @click="onrefresh">刷新</el-button>
          </div>
          <div
            style="width:70px;position:relative;top:-1px;display:flex;align-items: center;font-size:14px;font-weight:bold;color:#666;">
            排除类型:
          </div>
          <div style="width:30%;display:flex;align-items: center;margin-right: 10px;">
            <el-select size="mini" style="width:450px;" v-model="exclusionType" multiple placeholder="请选择"
              :collapse-tags="judgmentcondition" @change="onChange">
              <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.label">
              </el-option>
            </el-select>
          </div>
          <div>
            <el-button size="mini" @click="onSave" v-if="checkPermission('api:packprocess:PackagesSetProcessing:DeployPriceSetOperate')">保存排除</el-button>
          </div>
          <div style="flex: 1;"></div> <!-- 占位符，将操作日志按钮推到最右侧 -->
          <div style="margin-left: auto;">
            <el-button size="mini" @click="onLog">操作日志</el-button>
          </div>
        </div>
        <div>
          <vxe-table border resizable show-overflow="ellipsis" ref="xTable" :loading="loading" size="mini" height="700"
            :data="deploytableData" style="width: 100%;" :span-method="rowspanMethod"
            :edit-config="{ trigger: 'manual', mode: 'row', showIcon: false, showStatus: true, autoClear: false }">

            <vxe-colgroup field="b" title="每箱数量（个人装箱）">
              <vxe-column field="personalGreaterOrEqual" title="大于或等于" :edit-render="{}">
                <template #header>
                  <span style="color: #F56C6C; margin: 0 7px 0 0">*</span>
                  <span>大于或等于</span>
                </template>
                <template #edit="{ row }">
                  <vxe-input v-model.trim="row.personalGreaterOrEqual" type="text" placeholder="请输入"
                    @blur="performancescoreverify($event)"></vxe-input>
                </template>
              </vxe-column>

              <vxe-column field="personalLessScore" title="小于" :edit-render="{}">
                <template #header>
                  <span style="color: #F56C6C; margin: 0 7px 0 0">*</span>
                  <span>小于</span>
                </template>
                <template #edit="{ row }">
                  <vxe-input v-model="row.personalLessScore" type="text" placeholder="请输入"
                    @blur="performancescoreverify($event)"></vxe-input>
                </template>
              </vxe-column>

              <vxe-column field="personalDeployPrice" title="装箱工价" :edit-render="{}">
                <template #header>
                  <span style="color: #F56C6C; margin: 0 7px 0 0">*</span>
                  <span>装箱工价</span>
                </template>
                <template #edit="{ row }">
                  <vxe-input v-model="row.personalDeployPrice" type="text" placeholder="请输入"
                    @blur="performancescoreverify($event)"></vxe-input>
                </template>
              </vxe-column>
            </vxe-colgroup>

            <vxe-colgroup field="f" title="每箱数量（所有装箱）">
              <vxe-column field="allGreaterOrEqual" title="大于或等于" :edit-render="{}">
                <template #header>
                  <span style="color: #F56C6C; margin: 0 7px 0 0">*</span>
                  <span>大于或等于</span>
                </template>
                <template #edit="{ row }">
                  <vxe-input v-model="row.allGreaterOrEqual" type="text" placeholder="请输入"
                    @blur="performancescoreverify($event)"></vxe-input>
                </template>
              </vxe-column>

              <vxe-column field="allLessScore" title="小于" :edit-render="{}">
                <template #header>
                  <span style="color: #F56C6C; margin: 0 7px 0 0">*</span>
                  <span>小于</span>
                </template>
                <template #edit="{ row }">
                  <vxe-input v-model="row.allLessScore" type="text" placeholder="请输入"
                    @blur="performancescoreverify($event)"></vxe-input>
                </template>
              </vxe-column>

              <vxe-column field="allDeployPrice" title="装箱工价" :edit-render="{}">
                <template #header>
                  <span style="color: #F56C6C; margin: 0 7px 0 0">*</span>
                  <span>装箱工价</span>
                </template>
                <template #edit="{ row }">
                  <vxe-input v-model="row.allDeployPrice" type="text" placeholder="请输入"
                    @blur="performancescoreverify($event)"></vxe-input>
                </template>
              </vxe-column>
            </vxe-colgroup>

            <vxe-column field="markPrice" title="打标工价">
              <template #header>
                <span style="color: #F56C6C; margin: 0 7px 0 0">*</span>
                <span>打标工价</span>
              </template>
              <template #default="{ row }">
                <template v-if="!isMergedCell || row.isMerged">
                  {{ row.markPrice }}
                </template>
                <template v-else>
                  <vxe-input v-model="row.markPrice" type="text" placeholder="请输入"
                    @blur="performancescoreverify($event)" @input="handleInput(row)"></vxe-input>
                </template>
              </template>
            </vxe-column>

            <vxe-column title="操作">
              <template #default="{ row }">
                <template v-if="$refs.xTable.isActiveByRow(row)">
                  <el-button @click="saveRowEvent(row)">保存</el-button>
                  <el-button @click="cancelRowEvent(row)">取消</el-button>
                </template>
                <template v-else>
                  <el-button size="mini" @click="editRowEvent(row)" icon="el-icon-edit-outline" style="width: 56px;"
                    v-if="checkPermission('api:packprocess:PackagesSetProcessing:DeployPriceSetOperate')"></el-button>
                  <el-button size="mini" type="danger" plain @click="deletecomparison(row)" icon="el-icon-delete"
                    style="width: 56px;" v-if="checkPermission('api:packprocess:PackagesSetProcessing:DeployPriceSetOperate')"></el-button>
                </template>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
      </div>
    </div>
    <el-dialog :visible.sync="operationLog" width="50%" height="90%" v-dialogDrag style="margin-top: -10vh;">
      <div class="bzbjfgx" style="min-height:160px">
        <div class="rzxgsj" style="margin-left: 20px;">操作日志</div>
        <div style="max-height: 644px; overflow: auto;height: 644px;">
          <div class="bzczrzx" v-for="(item, index ) in loginfo" :key="index + 'log'">
            <div style="display: flex;">
              <div class="rztx">
                <el-avatar :size="25" fit="cover" :src="item.createdAvatar"></el-avatar>
              </div>
              <div class="rzmz">{{ item.createdUserName }}</div>
              <el-tooltip class="item" effect="dark" :content="item.createdTime" placement="top-start">
                <div class="rzxgx">{{ item.createdTime }}</div>
              </el-tooltip>
              <div class="rzxgsj">
                <span>{{ item.logInfo }}</span>
              </div>
            </div>
            <div v-if="item.afterInfo != null">
              <div class="rzbox">
                <div class="rzxgq">修改后：</div>
                <div class="rzxgnr">
                  <el-tooltip class="item" effect="dark" :content="item.afterInfo" placement="top-start">
                    <span>{{ item.afterInfo }}</span>
                  </el-tooltip>
                </div>
              </div>
            </div>
            <div>
              <div class="rzbox">
                <div class="rzxgq" v-if="item.afterInfo != null">修改前：</div>
                <div :class="{ 'add-margin': item.afterInfo == null, 'rzxgnr': item.afterInfo != null }">
                  <el-tooltip class="item" effect="dark" :content="item.beforeInfo" placement="top-start">
                    <span>{{ item.beforeInfo }}</span>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style="display: flex; justify-content: flex-end;">
        <el-pagination layout="prev, pager, next" :total="total" @current-change="handleCurrentChange"
          @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </el-dialog>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import { getPackagesSetData, getDeployPriceSet, editDeployPriceSet, delDeployPriceSet, saveExcludeType, getDeployPriceLogList } from '@/api/inventory/packagesSetProcessing';

export default {
  name: "redeploymentRate",
  components: { MyContainer },
  data() {
    return {
      loginfo: [],
      operationLog: false,
      judgmentcondition: false,
      conditions: null,
      distinguish: false,
      isMergedCell: false,
      editBackup: {}, //备份编辑前的数据
      loading: false,
      exclusionType: [],
      pageLoading: false,
      brandlist: [],
      deploytableData: [],
      verify: false,
      tableCheck: false,
      total: 0,
      page: {
        pageSize: 10,
        currentPage: 1,
      },
    };
  },

  mounted() {
    this.init()
    this.onrefresh()
  },

  methods: {
    //每页数量改变
    handleSizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
      this.onLog()
    },
    //当前页改变
    handleCurrentChange(val) {
      this.page.currentPage = val;
      this.onLog()
    },
    async onLog() {
      const params = {
        ...this.page,
      }
      const { data, success } = await getDeployPriceLogList(params)
      if (success) {
        this.loginfo = data.list
        this.total = data.total
        this.operationLog = true
      } else {
        this.$message.error('数据有误');
      }
    },
    onChange(tags) {
      const tagWidth = 61;
      const totalWidth = tags.length * tagWidth;
      if (totalWidth > 450) {
        this.judgmentcondition = true;
      } else {
        this.judgmentcondition = false;
      }
    },
    performancescoreverify(event) {
      this.tableCheck = true
      const inputValue = event.value;
      if (!inputValue) {
        this.$message.error('请输入数据');
        return
      }
      // 校验输入值是否超过 100000
      if (parseFloat(inputValue) > 100000) {
        this.$message.error('输入的值不能超过100000');
        event.value = ' '
        this.verify = true
        setTimeout(() => {
          this.verify = false;
        }, 2000);
        return;
      }
      // 校验输入值是否为整数或小数
      if (!/^\d+(\.\d+)?$/.test(inputValue)) {
        this.$message.error('请输入整数或小数');
        event.value = ' '
        this.verify = true
        setTimeout(() => {
          this.verify = false;
        }, 2000);
        return;
      }
      //校验当值为小数再执行校验，输入值不能超过小数点后两位
      if (typeof inputValue == 'string' && inputValue.indexOf('.') > -1) {
        const arr = inputValue.split('.');
        if (arr[1].length > 2) {
          this.$message.error('小数点后只能输入两位');
          event.value = ' ';
          this.verify = true;
          setTimeout(() => {
            this.verify = false;
          }, 2000);
          return;
        }
      }
    },
    async onSave() {
      const { success } = await saveExcludeType(this.exclusionType)
      if (success) {
        this.$message({ message: '保存成功', type: 'success' });
        this.onrefresh()
      }
    },
    async init() {
      const { data, success } = await getPackagesSetData({ setType: 12 })
      if (success) {
        this.brandlist = data.map(item => { return { value: item.setId, label: item.sceneCode }; });
      }
    },
    async onrefresh() {
      this.init()
      this.tableCheck = false
      const { data, success } = await getDeployPriceSet()
      if (success) {
        this.deploytableData = data.deployPriceSetList;
        this.exclusionType = [];
        this.brandlist.forEach(item => {
          if (data.excludeTypeList.includes(item.label)) {
            this.exclusionType.push(item.label);
          }
        });
      }
    },
    rowspanMethod({ row, _rowIndex, column, visibleData }) {
      let fields = ['markPrice'];
      let cellValue = row[column.property];
      if (cellValue && fields.includes(column.property)) {
        let prevRow = visibleData[_rowIndex - 1];
        let nextRow = visibleData[_rowIndex + 1];
        if (prevRow && prevRow[column.property] === cellValue) {
          return { rowspan: 0, colspan: 0, isMerged: true };
        } else {
          let countRowspan = 1;
          while (nextRow && nextRow[column.property] === cellValue) {
            nextRow = visibleData[++countRowspan + _rowIndex];
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1, isMerged: true };
          }
        }
      }
    },
    handleInput(row) {
      this.deploytableData.forEach(item => {
        item.markPrice = row.markPrice;
      });
    },
    groupByMarkingwages(data) {
      const groupedData = {};
      data.forEach((item, index) => {
        const key = item.markPrice;
        if (!groupedData[key]) {
          groupedData[key] = [];
        }
        groupedData[key].push({ ...item, index });
      });
      return groupedData;
    },
    //删除
    deletecomparison(row) {
      this.$confirm("是否删除数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const { success } = await delDeployPriceSet({ id: row.id })
          if (success) {
            this.$message({ message: '删除成功！', type: 'success' });
            this.tableCheck = false
            this.onrefresh()
          }
        })
    },
    //编辑
    editRowEvent(row) {
      if (this.tableCheck) {
        this.$message.error('请先保存数据');
        return;
      }
      //将上一次打标工价的值存起来
      this.deploytableData.forEach(item => {
        this.conditions = item.markPrice;
      });
      //判断条件
      if (!this.distinguish) {
        this.isMergedCell = !this.isMergedCell
      }
      this.distinguish = true
      this.editBackup = Object.assign({}, row);
      const $table = this.$refs.xTable
      $table.setActiveRow(row)
    },
    //取消
    //取消时将上一次打标工价的值重新赋上
    cancelRowEvent(row) {
      this.tableCheck = false
      this.deploytableData.forEach(item => {
        item.markPrice = this.conditions;
      });
      //判断条件
      this.distinguish = false
      this.isMergedCell = !this.isMergedCell
      Object.assign(row, this.editBackup);
      const $table = this.$refs.xTable
      $table.clearActived().then(() => {
        $table.revertData(row)
      })
    },
    //保存
    saveRowEvent(row) {
      if (this.verify == true) {
        return
      }
      const requiredFields = ['personalGreaterOrEqual', 'personalLessScore', 'personalDeployPrice', 'allGreaterOrEqual', 'allLessScore', 'allDeployPrice', 'markPrice'];
      if (!requiredFields.every(field => row[field] != null && row[field] !== '')) {
        this.$message({ message: '请填写完整必填项！', type: 'warning' });
        return;
      }
      if (Number(row.personalGreaterOrEqual) > Number(row.personalLessScore)) {
        this.$message.error('当前行每箱数量（个人）取值区间有误！');
        return
      }
      if (Number(row.allGreaterOrEqual) > Number(row.allLessScore)) {
        this.$message.error('当前行每箱数量（所有）取值区间有误！');
        return
      }
      if (this.validatePrice(row.allDeployPrice) || this.validatePrice(row.personalDeployPrice)) {
        return;
      }
      //判断条件所输入的值是否在大于等于小于的区间内
      const personalGreaterOrEqual = row.personalGreaterOrEqual;
      var a = [...this.deploytableData]; // 将this.deploytableData的值复制给a数组
      for (let i = a.length - 1; i >= 0; i--) {
        if (a[i]._X_ROW_KEY.toString() === row._X_ROW_KEY) {
          a.splice(i, 1); // 删除_X_ROW_KEY相同的项
        }
      }
      const isDuplicate = a.some(item => {
        if (
          personalGreaterOrEqual >= item.personalGreaterOrEqual &&
          personalGreaterOrEqual < item.personalLessScore
        ) {
          return true;
        }
        return false;
      });
      if (isDuplicate) {
        this.$message.error('个人装箱区间数据重复');
        return
      }
      const allGreaterOrEqual = row.allGreaterOrEqual;
      const allisDuplicate = a.some(item => {
        if (
          allGreaterOrEqual >= item.allGreaterOrEqual &&
          allGreaterOrEqual < item.allLessScore
        ) {
          return true;
        }
        return false;
      });
      if (allisDuplicate) {
        this.$message.error('所有装箱区间数据重复');
        return
      }
      //判断条件
      this.tableCheck = false
      this.distinguish = false
      this.isMergedCell = !this.isMergedCell
      const $table = this.$refs.xTable
      $table.clearActived().then(() => {
        setTimeout(() => {
          this.onSaveEvent(row)
        }, 300)
      })
    },
    async onSaveEvent(row) {
      for (let key in row) {
        if (key === 'markPrice' || key === 'allDeployPrice' || key === 'allLessScore' || key === 'allGreaterOrEqual' || key === 'personalDeployPrice' || key === 'personalLessScore' || key === 'personalGreaterOrEqual') {
          if (typeof row[key] === 'string') {
            let num = parseFloat(row[key]);
            if (!isNaN(num)) {
              row[key] = num.toFixed(2);
            }
          } else if (typeof row[key] === 'number') {
            row[key] = row[key].toFixed(2);
          }
        }
      }
      const { data, success } = await editDeployPriceSet(row)
      if (success) {
        this.$message({ message: '保存成功', type: 'success' });
        this.onrefresh()
      }
    },
    validatePrice(value) {
      const reg = /^\d+(\.\d{1,2})?$/; // 正则表达式，匹配最多两位小数的数字
      if (!reg.test(value)) {
        this.$message({ message: '小数点后只能输入两位！', type: 'warning' });
        return true; // 超过两位小数，返回true
      }
      return false; // 未超过两位小数，返回false
    },
    //新增
    onadd() {
      var newObject = {
        id: 0,
        personalGreaterOrEqual: '',
        personalLessScore: '',
        personalDeployPrice: '',
        allGreaterOrEqual: '',
        allLessScore: '',
        allDeployPrice: '',
      };
      if (this.deploytableData && this.deploytableData.length > 0) {
        newObject.markPrice = this.deploytableData[0].markPrice;
      } else {
        newObject.markPrice = '';
      }
      this.tableCheck = false
      this.deploytableData.push(newObject);
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .bzjzcjrw {
  width: 100%;
  margin-top: 15px;
  background-color: #fff;
}

::v-deep .bzjzcjrw .bt {
  // height: 40px;
  // font-size: 18px;
  // color: #666;
  // margin-bottom: 20px;
  // border: 1px solid #dcdfe6;
  // border-top: 0px;
  // border-right: 0px;
  // border-left: 0px;
  // box-sizing: border-box;
  // padding: 0 35px;
  height: 30px;
  font-size: 16px;
  color: #666;
  // margin-bottom: 20px;
  // border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 0 35px;
}

::v-deep .bzjzcjrw .bzccjlx {
  box-sizing: border-box;
  padding: 0 60px;
  display: flex;
}

::v-deep .bzjzcjrw .bzccjlx {
  width: 100%;
  height: 35px;
  box-sizing: border-box;
  padding: 0 60px;
  display: flex;
}

::v-deep .bzjzcjrw .bzccjlx .lxwz {
  width: 20%;
  font-size: 14px;
  color: #666;
  vertical-align: top;
  line-height: 26px;
}

::v-deep .bzjzcjrw .bzccjlx .lxwz2 {
  width: 80%;
}

.bzdbl {
  max-height: 500px;
  overflow: auto;
}

.bzbjfgx {
  // border: 1px solid #dcdfe6;
  border-top: 1px solid #dcdfe6;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  font-size: 12px;
  padding: 25px 0;
  margin-top: 20px;
}

.rzxgsj {
  max-width: 200px;
  color: #999;
}

.bzczrzx {
  box-sizing: border-box;
  padding: 10px 60px;
}

.rztx {
  width: 25px;
  height: 25px;
  background-color: #f5f5f5;
  border-radius: 50%;
  margin-right: 15px;
}

.rzmz {
  width: 50px;
  margin-right: 5px;
}

.rzxgx {
  max-width: 200px;
  margin-right: 10px;
  color: #999;
}

.rzxgsj {
  max-width: 200px;
  color: #999;
}

.rzbox {
  display: flex;
}

.rzxgq {
  width: 50px;
  margin-left: 43px;
  margin-right: 2px;
}

.rzxgnr {
  max-width: 687px;
  line-height: 15px;
  font-size: 12px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.add-margin {
  margin-left: 43px;
  max-width: 687px;
  line-height: 15px;
  font-size: 12px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
