<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
            <div class="top">
                <el-button-group>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="销量开始日期" end-placeholder="销量结束日期" :picker-options="pickerOptions"
                            :clearable="false" style="width: 240px;" :value-format="'yyyy-MM-dd'" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.createdUserNames" clearable filterable placeholder="推荐人" multiple
                            collapse-tags style="width: 150px">
                            <el-option v-for="item in createdUserNameList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.isDelete" clearable filterable placeholder="在职状态"
                            style="width: 120px">
                            <el-option label="在职" :value="false" />
                            <el-option label="离职" :value="true" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.hired_dateDes" clearable filterable placeholder="入职时间"
                            style="width: 150px">
                            <el-option label="3个月以内" value="3个月以内" />
                            <el-option label="超过3个月" value="超过3个月" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.createUserAreas" clearable filterable placeholder="地区" multiple
                            collapse-tags style="width: 150px">
                            <el-option v-for="item in createUserAreaList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.createUserRoles" clearable filterable placeholder="职位" multiple
                            collapse-tags style="width: 250px">
                            <el-option v-for="item in createUserRoleList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.createUserDeptNames" clearable filterable placeholder="架构" multiple
                            collapse-tags style="width: 250px">
                            <el-option v-for="item in createUserDeptNameList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>

                    <el-button type="primary" @click="onSearch()">查询</el-button>
                </el-button-group>
            </div>
        </template>
        <vxetablebase :id="'HotSaleBrandPushNewSale0202408041714'" ref="table" :that='that' :isIndex='true'
            :hasexpand='true' :tablefixed='true' :border="true" @sortchange='sortchange' @select='selectchange'
            :tableData='tableData' :tableCols='tableCols' :showsummary='true' :summaryarry='summaryarry'
            :isSelection="false" :isSelectColumn="false" @summaryClick='onsummaryClick' style="width: 100%;  margin: 0"
            v-loading="listLoading" :height="'100%'">
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import datepicker from '@/views/customerservice/datepicker';
import { platformlist, pickerOptions } from '@/utils/tools';
import dayjs from 'dayjs';
import { formatTime } from "@/utils";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    GetHotSaleBrandPushNewSaleFsPageList, GetHotSaleBrandPushNewReportUsers
} from '@/api/operatemanage/productalllink/alllink'
const tableCols = [
    { sortable: 'custom', fixed: 'left', width: '80', align: 'center', prop: 'createdUserName', label: '推荐人', },
    { sortable: 'custom', fixed: 'left', width: '80', align: 'center', prop: 'isDelete', label: '在职状态', formatter: (row) => row.isDelete == true ? "离职" : "在职" },
    { sortable: 'custom', fixed: 'left', width: '80', align: 'center', prop: 'hired_date', label: '入职日期', formatter: (row) => row.hired_date == null ? "" : formatTime(row.hired_date, "YYYY-MM-DD") },
    { sortable: 'custom', fixed: 'left', width: '80', align: 'center', prop: 'createUserArea', label: '地区', },
    { sortable: 'custom', fixed: 'left', width: '100', align: 'center', prop: 'createUserRole', label: '职位', },
    { sortable: 'custom', fixed: 'left', width: '280', align: 'center', prop: 'createUserDeptName', label: '架构', },
    { sortable: 'custom', width: '190', align: 'center', prop: 'styleCodeCount1', label: '销量>=1万<2万系列编码', type: 'click', handle: (that, row, col) => that.JumpDetail(row, col), summaryEvent: true },
    { sortable: 'custom', width: '190', align: 'center', prop: 'styleCodeCount2', label: '销量>=2万<3万系列编码', type: 'click', handle: (that, row, col) => that.JumpDetail(row, col), summaryEvent: true },
    { sortable: 'custom', width: '190', align: 'center', prop: 'styleCodeCount3', label: '销量>=3万系列编码', type: 'click', handle: (that, row, col) => that.JumpDetail(row, col), summaryEvent: true },

    { sortable: 'custom', width: '190', align: 'center', prop: 'styleCodeCount10', label: '单量>=500<1000系列编码', type: 'click', handle: (that, row, col) => that.JumpDetail(row, col), summaryEvent: true },
    { sortable: 'custom', width: '190', align: 'center', prop: 'styleCodeCount11', label: '单量>=1000<2000系列编码', type: 'click', handle: (that, row, col) => that.JumpDetail(row, col), summaryEvent: true },
    { sortable: 'custom', width: '190', align: 'center', prop: 'styleCodeCount12', label: '单量>=2000<3000系列编码', type: 'click', handle: (that, row, col) => that.JumpDetail(row, col), summaryEvent: true },
    { sortable: 'custom', width: '190', align: 'center', prop: 'styleCodeCount13', label: '单量>=3000<4000系列编码', type: 'click', handle: (that, row, col) => that.JumpDetail(row, col), summaryEvent: true },
    { sortable: 'custom', width: '190', align: 'center', prop: 'styleCodeCount14', label: '单量>=4000<5000系列编码', type: 'click', handle: (that, row, col) => that.JumpDetail(row, col), summaryEvent: true },
    { sortable: 'custom', width: '190', align: 'center', prop: 'styleCodeCount15', label: '单量>=5000<6000系列编码', type: 'click', handle: (that, row, col) => that.JumpDetail(row, col), summaryEvent: true },
    { sortable: 'custom', width: '190', align: 'center', prop: 'styleCodeCount16', label: '单量>=6000<7000系列编码', type: 'click', handle: (that, row, col) => that.JumpDetail(row, col), summaryEvent: true },
    { sortable: 'custom', width: '190', align: 'center', prop: 'styleCodeCount17', label: '单量>=7000<8000系列编码', type: 'click', handle: (that, row, col) => that.JumpDetail(row, col), summaryEvent: true },
    { sortable: 'custom', width: '190', align: 'center', prop: 'styleCodeCount18', label: '单量>=8000<9000系列编码', type: 'click', handle: (that, row, col) => that.JumpDetail(row, col), summaryEvent: true },
    { sortable: 'custom', width: '190', align: 'center', prop: 'styleCodeCount19', label: '单量>=9000<10000系列编码', type: 'click', handle: (that, row, col) => that.JumpDetail(row, col), summaryEvent: true },
    { sortable: 'custom', width: '190', align: 'center', prop: 'styleCodeCount20', label: '单量>=10000<20000系列编码', type: 'click', handle: (that, row, col) => that.JumpDetail(row, col), summaryEvent: true },
    { sortable: 'custom', width: '190', align: 'center', prop: 'styleCodeCount21', label: '单量>=20000系列编码', type: 'click', handle: (that, row, col) => that.JumpDetail(row, col), summaryEvent: true },


    { sortable: 'custom', width: '120', align: 'center', prop: 'orderCount', label: '总单量', type: 'click', handle: (that, row, col) => that.JumpDetail(row, col), summaryEvent: true },
    { sortable: 'custom', width: '120', align: 'center', prop: 'goodsCount', label: '总销量', type: 'click', handle: (that, row, col) => that.JumpDetail(row, col), summaryEvent: true },
    { sortable: 'custom', width: '120', align: 'center', prop: 'saleAmont', label: '总销售额', type: 'click', handle: (that, row, col) => that.JumpDetail(row, col), summaryEvent: true },
    { sortable: 'custom', width: '120', align: 'center', prop: 'profit4', label: '总毛四利润', type: 'click', handle: (that, row, col) => that.JumpDetail(row, col), summaryEvent: true },
];
export default {
    name: "HotSaleBrandPushNewSale0",
    components: {
        MyContainer, datepicker, vxetablebase
    },
    data() {
        return {
            that: this,
            auditVisible: false,
            activities: [],
            timeRanges: [],
            platformlist: platformlist,
            pickerOptions,
            filter: {
                timerange: [(dayjs().subtract(1, 'day').format('YYYY-MM') + '-01'), dayjs().subtract(1, 'day').format('YYYY-MM-DD')],
                createdStartDate: null,
                createdEndDate: null,
                createdUserNames: [],
                createUserAreas: [],
                createUserRoles: [],
                createUserDeptNames: [],
                hired_dateDes: "",
            },
            pager: { OrderBy: "goodsCount", IsAsc: false },
            tableCols: tableCols,
            tableData: [],
            total: 0,
            listLoading: false,
            pageLoading: false,
            sels: [],
            selids: [],
            summaryarry: {},
            createdUserNameList: [],
            createUserAreaList: [],
            createUserRoleList: [],
            createUserDeptNameList: [],
        }
    },
    async mounted() {
        await this.getSelectData();
        this.onSearch();
    },
    computed: {
    },
    methods: {
        async getSelectData() {
            let ret = await GetHotSaleBrandPushNewReportUsers({ getType: 1 });
            this.createdUserNameList = ret.data;

            let ret2 = await GetHotSaleBrandPushNewReportUsers({ getType: 2 });
            this.createUserAreaList = ret2.data;

            let ret3 = await GetHotSaleBrandPushNewReportUsers({ getType: 3 });
            this.createUserRoleList = ret3.data;

            let ret4 = await GetHotSaleBrandPushNewReportUsers({ getType: 4 });
            this.createUserDeptNameList = ret4.data;
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getParam() {
            //选品日期
            if (this.filter.timerange && this.filter.timerange.length == 2) {
                this.filter.createdStartDate = this.filter.timerange[0];
                this.filter.createdEndDate = this.filter.timerange[1];
            }
            else {
                this.filter.createdStartDate = null;
                this.filter.createdEndDate = null;
            }
            let pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            return params;
        },
        async getList() {
            let param = this.getParam();
            this.listLoading = true
            const res = await GetHotSaleBrandPushNewSaleFsPageList(param)
            this.listLoading = false
            console.log(res);
            if (res?.success) {
                this.tableData = res.data.list;
                this.total = res.data.total;
                // Object.keys(res.data.summary).forEach(f => {
                //     res.data.summary[f] = res.data.summary[f].toString();
                // });
                res.data.summary.mx_sum = "查看";
                res.data.summary.mx2_sum = "查看";
                this.summaryarry = res.data.summary;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },







        async JumpDetail(row, col) {
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBrandPushNewSale0_3.vue`,
                title: '发生明细',
                args: {
                    createdUserId: row.createdUserId,
                    createdUserName: row.createdUserName,
                    timerange: this.filter.timerange,
                    hired_dateDes: this.filter.hired_dateDes,
                    createdUserNames: this.filter.createdUserNames,
                    createUserAreas: this.filter.createUserAreas,
                    createUserRoles: this.filter.createUserRoles,
                    createUserDeptNames: this.filter.createUserDeptNames,
                    clickColKey: col.prop,
                },
                height: '650px',
                width: '70%',
                callOk: this.afterSave
            });
        },
        afterSave() {

        },
        onsummaryClick(property) {
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBrandPushNewSale0_3.vue`,
                title: '发生明细',
                args: {
                    createdUserId: null,
                    createdUserName: null,
                    timerange: this.filter.timerange,
                    hired_dateDes: this.filter.hired_dateDes,
                    createdUserNames: this.filter.createdUserNames,
                    createUserAreas: this.filter.createUserAreas,
                    createUserRoles: this.filter.createUserRoles,
                    createUserDeptNames: this.filter.createUserDeptNames,
                    clickColKey: property,
                },
                height: '650px',
                width: '70%',
                callOk: this.afterSave
            });
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}

::v-deep .el-select__tags-text {
    max-width: 120px;
}
</style>
