<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-select v-model="ListInfo.approveStatuses" placeholder="状态" style="width: 160px;" class="publicCss" clearable
          filterable multiple collapse-tags>
          <el-option label="审批中" value="审批中" />
          <el-option label="已撤销" value="已撤销" />
          <el-option label="待审批" value="待审批" />
          <el-option label="已终止" value="已终止" />
          <el-option label="拒绝" value="拒绝" />
          <el-option label="同意" value="同意" />
        </el-select>
        <el-select v-model="ListInfo.allotTypes" placeholder="类型" style="width: 160px;" class="publicCss" clearable
          multiple collapse-tags>
          <el-option label="本调云" :value="0" />
          <el-option label="云调本" :value="1" />
          <el-option label="本调本" :value="2" />
          <el-option label="云调云" :value="3" />
        </el-select>
        <inputYunhan ref="productApproveCodes" :inputt.sync="ListInfo.approveCodes" v-model="ListInfo.approveCodes"
          width="180px" placeholder="审批编号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
          :maxlength="1000000" @callback="approveCodesCallback" title="审批编号" style="width: 180px;margin:0 5px 0 0;">
        </inputYunhan>
        <chooseWareHouse v-model="ListInfo.allotOutWmsIds" style="width: 180px;" multiple placeholder="调出仓"
          class="publicCss" />
        <chooseWareHouse v-model="ListInfo.allotInWmsIds" style="width: 180px;" multiple placeholder="调入仓"
          class="publicCss" />
        <el-select v-model="ListInfo.aprAllotTypes" placeholder="调拨类型" class="publicCss" clearable filterable multiple
          collapse-tags>
          <el-option label="删除原仓库位" value="删除原仓库位" />
          <el-option label="正常调拨" value="正常调拨" />
        </el-select>
        <el-select v-model="ListInfo.isFullAllots" placeholder="全部挪/部分挪" class="publicCss" clearable filterable multiple
          collapse-tags>
          <el-option label="全部挪" :value="true" />
          <el-option label="部分挪" :value="false" />
        </el-select>
        <el-select v-model="ListInfo.brandIds" clearable filterable multiple collapse-tags placeholder="采购"
          class="publicCss">
          <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="ListInfo.applyUserNames" multiple collapse-tags clearable filterable remote reserve-keyword
          placeholder="请模糊输入并选择发起人" :remote-method="remoteMethod" :loading="initiatorloading" class="publicCss">
          <el-option v-for="item in initiatorList" :key="'userSelector' + item.value + item.extData.defaultDeptId"
            :label="item.label" :value="item.label">
            <span>{{ item.label }}</span>
            <span style=" color: #8492a6; ">({{ item.extData.position }},{{ item.extData.empStatusText }}{{
              item.extData.jstUserName
                ? "," + item.extData.jstUserName : "" }})</span>
            <!-- <span style=" color: #8492a6; "> {{ item.extData.deptName }}</span> -->
          </el-option>
        </el-select>
        <dateRange :startDate.sync="ListInfo.startApplyTime" :endDate.sync="ListInfo.endApplyTime" class="publicCss"
          style="width: 230px;" :startPlaceholder="'发起开始时间'" :endPlaceholder="'发起结束时间'" />
        <dateRange :startDate.sync="ListInfo.startApproveTime" :endDate.sync="ListInfo.endApproveTime" class="publicCss"
          style="width: 230px;" :startPlaceholder="'采购审批开始时间'" :endPlaceholder="'采购审批结束时间'" />
        <dateRange :startDate.sync="ListInfo.startAllotOutTime" :endDate.sync="ListInfo.endAllotOutTime"
          class="publicCss" style="width: 250px;" :startPlaceholder="'调出仓出库开始时间'" :endPlaceholder="'调出仓出库结束时间'" />
        <dateRange :startDate.sync="ListInfo.startTransitInTime" :endDate.sync="ListInfo.endTransitInTime"
          class="publicCss" style="width: 230px;" :startPlaceholder="'中转入仓开始时间'" :endPlaceholder="'中转入仓结束时间'" />
        <dateRange :startDate.sync="ListInfo.startTransitOutTime" :endDate.sync="ListInfo.endTransitOutTime"
          class="publicCss" style="width: 230px;" :startPlaceholder="'中转出仓开始时间'" :endPlaceholder="'中转出仓结束时间'" />
        <dateRange :startDate.sync="ListInfo.startAllotInTime" :endDate.sync="ListInfo.endAllotInTime" class="publicCss"
          style="width: 250px;" :startPlaceholder="'调入仓入库开始时间'" :endPlaceholder="'调入仓入库结束时间'" />
        <div>
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
        </div>
      </div>
    </template>
    <vxetablebase :id="'operationRecord202501151730'" ref="table" :loading="loading" :that="that" :is-index="true"
      :hasexpand="true" :tablefixed="true" :has-seq="false" :border="true" :table-data="data.list"
      :table-cols="tableCols" :is-selection="false" :is-select-column="true" :is-index-fixed="false"
      style="width: 100%; margin: 0;" height="100%" :showsummary="data.summary ? true : false"
      :summaryarry="data.summary" @sortchange="sortchange" />
    <template #footer>
      <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="调拨审批单详情" :visible.sync="approvalVisible" width="72%" v-dialogDrag>
      <div style="height: 550px;">
        <approvalCode v-if="approvalVisible" :approvalListInfo="approvalListInfo" />
      </div>
    </el-dialog>

    <el-dialog title="调拨记录" :visible.sync="allotInTimeVisible" width="75%" v-dialogDrag>
      <div style="height: 550px;">
        <allotInTime v-if="allotInTimeVisible" :allotInTimeListInfo="allotInTimeListInfo" />
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import { getAllProBrand, getBianManPositionListV2 } from '@/api/inventory/warehouse'
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser'
import YhUserelector from '@/components/YhCom/yh-userselector.vue'
import numberRange from "@/components/number-range/index.vue";
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/GoodAllot/AllotOperate/'
import { mergeTableCols } from '@/utils/getCols'
import approvalCode from './approvalCode.vue'
import allotInTime from './allotInTime.vue'
export default {
  name: "operationRecord",
  components: {
    MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, approvalCode, allotInTime, chooseWareHouse, YhUserelector
  },
  data() {
    return {
      api,
      platformlist,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        brandIds: [],//采购
        applyUserNames: [],//发起人
        approveStatuses: [],//状态
        allotTypes: [0, 1],//调拨类型
        approveCodes: '',//审批编码
        allotOutWmsIds: [],//调出仓
        allotInWmsIds: [],//调入仓
        aprAllotTypes: [],//调拨类型
        isFullAllots: [],//全部挪/部分挪
        startApplyTime: null,//发起时间
        endApplyTime: null,//发起时间
        startApproveTime: null,//采购审批时间
        endApproveTime: null,//采购审批时间
        startTransitInTime: null,//中转入仓时间
        endTransitInTime: null,//中转入仓时间
        startTransitOutTime: null,//中转出仓时间
        endTransitOutTime: null,//中转出仓时间
        startAllotInTime: null,//调入仓时间
        endAllotInTime: null,//调入仓时间
        startAllotOutTime: null,//调出仓时间
        endAllotOutTime: null,//调出仓时间
      },
      initiatorList: [],
      initiatorloading: false,
      brandlist: [],
      approvalListInfo: {},
      allotInTimeListInfo: {},
      approvalVisible: false,
      allotInTimeVisible: false,
      data: {},
      chatProp: {
        chatDialog: false, // 趋势图弹窗
        chatTime: null, // 趋势图时间
        chatLoading: true, // 趋势图loading
        data: [], // 趋势图数据
      },
      timeRanges: [],
      tableCols: [],
      tableData: [],
      total: 0,
      loading: true,
      pickerOptions,
      isExport: false
    }
  },
  async mounted() {
    await this.init()
    await this.getCol();
    await this.getList()
  },
  methods: {
    async remoteMethod(query) {
      if (query && query.length > 50) return this.$message.error("输入内容过长");
      this.initiatorloading = true;
      if (query !== '') {
        let rlt = await QueryAllDDUserTop100({ keywords: query });
        if (rlt && rlt.success) {
          this.initiatorList = rlt.data?.map(item => {
            return { label: item.userName, value: item.ddUserId, extData: item }
          });
        }
      } else {
        this.initiatorList = [...this.orgOptions];
      }
      this.initiatorloading = false;
    },
    async init() {
      var res2 = await getAllProBrand();
      this.brandlist = res2.data.map(item => {
        return { value: item.key, label: item.value };
      });
    },
    async openPropsApproval(row, val) {
      let info = JSON.parse(JSON.stringify(row))
      if (val == 1) {
        this.approvalListInfo = info
        this.approvalVisible = true
      } else if (val == 2) {
        this.allotInTimeListInfo = info
        this.allotInTimeVisible = true
      }
    },
    approveCodesCallback(val) {
      this.ListInfo.approveCodes = val
    },
    // 导出数据,这里前端可以封装一个方法
    async exportProps() {
      this.isExport = true
      await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
        this.isExport = false
      })
    },
    async getCol() {
      const { data, success } = await request.post(`${this.api}GetColumns`)
      if (success) {
        data.forEach(item => {
          if (item.prop == 'orderNoInner') {
            item.type = 'orderLogInfo'
            item.orderType = 'orderNoInner'
          }
          if (item.prop == 'proCode') {
            item.type = 'html'
            item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
          }
          if (item.prop == 'approveCode') {
            item.type = 'click'
            item.handle = (that, row) => that.openPropsApproval(row, 1)
          }
          if (item.prop == 'allotInTime') {
            item.type = 'click'
            item.handle = (that, row) => that.openPropsApproval(row, 2)
          }
        })
        this.tableCols = mergeTableCols(data)
      }
    },
    async getList(type) {
      if (type === "search") {
        this.ListInfo.currentPage = 1;
        this.$refs.pager.setPage(1);
      }
      this.loading = true;
      try {
        const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
        if (success) {
          this.data = data;
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.loading = false;
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-select__tags-text {
  max-width: 50px;
}

.top {
  display: flex;
  margin-bottom: 5px;
  flex-wrap: wrap;

  .publicCss {
    width: 190px;
    margin: 0 5px 5px 0;
  }
}
</style>
