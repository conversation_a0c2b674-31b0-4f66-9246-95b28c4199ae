<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="ListInfo.orderNo" placeholder="订单编号" maxlength="50" clearable class="publicCss" @keyup.enter.native="getList('search')"/>
        <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable class="publicCss" />
        <el-select v-model="ListInfo.companyOrPersonal" placeholder="请选择公司/个人" class="publicCss" clearable>
          <el-option :key="'公司'" label="公司" :value="'公司'" />
          <el-option :key="'个人'" label="个人" :value="'个人'" />
        </el-select>
        <el-input v-model.trim="ListInfo.invoiceHeader" placeholder="发票抬头" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.taxID" placeholder="税号" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.openingBank" placeholder="开户行" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.accountNumber" placeholder="账号" maxlength="50" clearable class="publicCss" />
        <!-- <el-input v-model.trim="ListInfo.mobile" placeholder="电话" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.customerEmail" placeholder="客户邮箱" maxlength="50" clearable class="publicCss" /> -->
        <el-input v-model.trim="ListInfo.dataType" placeholder="类型" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.registrantName" placeholder="登记人名称" maxlength="50" clearable
          class="publicCss" />
        <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable>
          <el-option :key="'作废'" label="作废" :value="'作废'" />
          <el-option :key="'已登记待审核'" label="已登记待审核" :value="'已登记待审核'" />
          <el-option :key="'财务审核驳回'" label="财务审核驳回" :value="'财务审核驳回'" />
          <el-option :key="'已通过待开票'" label="已通过待开票" :value="'已通过待开票'" />
          <el-option :key="'已通过开票成功'" label="已通过开票成功" :value="'已通过开票成功'" />
          <el-option :key="'已通过开票失败'" label="已通过开票失败" :value="'已通过开票失败'" />
          <el-option :key="'待红冲'" label="待红冲" :value="'待红冲'" />
          <el-option :key="'已红冲'" label="已红冲" :value="'已红冲'" />
        </el-select>
        <el-input v-model.trim="ListInfo.billNumber" placeholder="票据号" maxlength="50" clearable class="publicCss" />
        <el-select v-model="ListInfo.invoiceType" placeholder="开票类型" class="publicCss" clearable filterable>
          <el-option key="常规发票" label="常规发票" value="常规发票" />
          <el-option key="红冲发票" label="红冲发票" value="红冲发票" />
        </el-select>
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="登记开始时间" end-placeholder="登记结束时间" :picker-options="pickerOptions" style="width: 230px;"
          class="publicCss" :value-format="'yyyy-MM-dd'" @change="changeTime($event, 1)">
        </el-date-picker>
        <el-date-picker v-model="timeRangesAudit" type="daterange" unlink-panels range-separator="至"
          start-placeholder="审核开始时间" end-placeholder="审核结束时间" :picker-options="pickerOptions" style="width: 230px;"
          class="publicCss" :value-format="'yyyy-MM-dd'" @change="changeTime($event, 2)">
        </el-date-picker>
        <el-date-picker v-model="timeRangesInvoice" type="daterange" unlink-panels range-separator="至"
          start-placeholder="开票开始时间" end-placeholder="开票结束时间" :picker-options="pickerOptions" style="width: 230px;"
          class="publicCss" :value-format="'yyyy-MM-dd'" @change="changeTime($event, 3)">
        </el-date-picker>
        <el-select v-model="ListInfo.isRepeatOrder" placeholder="是否重复登记" class="publicCss" clearable filterable>
          <el-option key="是" label="是" value="是" />
          <el-option key="否" label="否" value="否" />
        </el-select>
        <el-select v-model="ListInfo.isUrgent" placeholder="是否加急" class="publicCss" clearable filterable>
          <el-option key="是" label="是" value="是" />
          <el-option key="否" label="否" value="否" />
        </el-select>
        <div style="margin: 2px;">
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" @click="handleAdd"
            v-if="checkPermission('AfterSalesInvoiceManageCustomerDataPermission')">
            新增
          </el-button>
        </div>
      </div>
    </template>
    <vxetablebase :id="'afterSalesManage202503251921'" :tablekey="'afterSalesManage202503251921'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0"
      :loading="loading" :height="'100%'" :border="true">
      <template #invoiceFileUrl="{ row }">
        <div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center">
          <el-image v-if="row.invoiceFileUrl"
            :src="'https://nanc.yunhanmy.com:10010/media/video/20250415/1912067771609825280.png'"
            style="width: 30px; height: 30px; cursor: pointer" @click="downloadAttachment(row)" />
        </div>
      </template>
      <template #orderNo="{ row }">
        <div v-if="!row.verify" style="width: 100%; text-align: left">
          <a @click="handleLinkClick(row, { prop: 'orderNo', label: '订单编号' })" style="color: blue; cursor: pointer;">
            {{ row.orderNo }}
          </a>
        </div>
        <div v-else style="width: 100%; text-align: left; cursor: pointer; color: #409EFF"
          @click="openOrderDetail(row)">
          {{ row.orderNo }}
        </div>
      </template>
      <template slot="right" v-if="checkPermission('AfterSalesInvoiceManageCustomerDataPermission')">
        <vxe-column title="操作" width="80" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" @click="handleEdit(row)" :disabled="row.status !== '已登记待审核'">编辑</el-button>
              <el-button type="text" @click="handleVoid(row)" :disabled="row.status !== '已登记待审核'"
                v-if="row.status !== '作废'">作废</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog :title="dialogTitle" :visible.sync="registrationVisible" width="1024px" v-dialogDrag
      style="margin-top: -7vh;" :close-on-click-modal="false">
      <invoiceRegistration v-if="registrationVisible" ref="refinvoiceRegistration" :editData="editData"
        @cancelFormMethod="cancelFormMethod" @close="registrationVisible = false" />
    </el-dialog>

    <el-dialog :visible.sync="detailDialogVisible" width="50%" title="订单详情" v-dialogDrag
      style="margin-top: -5vh !important">
      <div>
        <el-table :data="orderDtls" style="width: 100%" border height="400">
          <el-table-column type="index" label="序号" width="55" align="center" />
          <el-table-column prop="orderNo" label="订单编号" width="150" align="center" show-overflow-tooltip>
            <template #default="{ row, column }">
              <a @click="handleLinkClick(row, { prop: 'orderNo', label: '订单编号' })" style="color: blue; cursor: pointer;">
                {{ row.orderNo }}
              </a>
            </template>
          </el-table-column>
          <el-table-column prop="goodsName" label="商品名称" width="auto" align="center" show-overflow-tooltip />
          <el-table-column prop="goodsCount" label="商品数量" width="100" align="center" show-overflow-tooltip />
          <el-table-column prop="spec" label="规格" width="120" align="center" show-overflow-tooltip />
          <el-table-column prop="unit" label="单位" width="120" align="center" show-overflow-tooltip />
          <el-table-column prop="actualInvoicedAmount" label="实际开票金额" width="120" align="center" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ formatAmount(scope.row.actualInvoicedAmount) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import invoiceRegistration from './components/invoiceRegistration.vue'
import { getAfterSalesInvoiceManageCustomerPage, nullifyAfterSalesInvoiceManageData } from '@/api/customerservice/afterSalesInvoiceManage'
import { canJump, onJumpLink } from '@/utils/tools';
import dayjs from 'dayjs'
const tableCols = [
  { width: '100', align: 'center', prop: 'orderNo', label: '订单编号', },
  { width: '100', align: 'center', prop: 'goodsName', label: '商品名称', },
  { width: '100', align: 'center', prop: 'goodsCount', label: '商品数量', },
  { width: '100', align: 'center', prop: 'spec', label: '规格', },
  { width: '100', align: 'center', prop: 'unit', label: '单位', },
  { width: '100', align: 'center', prop: 'actualInvoicedAmount', label: '实际开票金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'companyOrPersonal', label: '公司/个人', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'invoiceHeader', label: '发票抬头', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'taxID', label: '税号', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'openingBank', label: '开户行', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'accountNumber', label: '账号', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'address', label: '地址', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'mobile', label: '电话', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'customerEmail', label: '客户邮箱', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'dataType', label: '类型', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'remark', label: '备注', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'registrantName', label: '登记人名称', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'status', label: '状态', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'invoiceType', label: '开票类型', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'auditRemarks', label: '审核备注', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'auditTime', label: '审核时间', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'billNumber', label: '票据号', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'invoiceFileUrl', label: '发票文件', },
  {
    sortable: 'custom', width: '100', align: 'center', prop: 'isRepeatOrder', label: '是否重复登记', color: (row) => {
      if (row.isRepeatOrder == '是') {
        return '#f56c6c'
      } else {
        return '#000000'
      }
    },
  },
  {
    sortable: 'custom', width: '100', align: 'center', prop: 'isUrgent', label: '是否加急', color: (row) => {
      if (row.isUrgent == '是') {
        return '#f56c6c'
      } else {
        return '#000000'
      }
    },
  },
  { sortable: 'custom', width: '120', align: 'center', prop: 'invoiceDate', label: '开票时间', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'cancelReason', label: '作废原因', },
  { sortable: 'custom', width: '120', align: 'center', prop: 'createdTime', label: '登记时间', },
]
export default {
  name: "afterSalesManage",
  components: {
    MyContainer, vxetablebase, invoiceRegistration
  },
  data() {
    return {
      timeRanges: [],
      timeRangesAudit: [],
      timeRangesInvoice: [],
      detailDialogVisible: false,
      orderDtls: [],
      dialogTitle: '编辑',
      editData: {},
      registrationVisible: false,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        orderNo: null,//订单编号
        goodsName: null,//商品名称
        companyOrPersonal: null,//对公对私 公司、个人
        invoiceHeader: null,//发票抬头
        taxID: null,//税号
        openingBank: null,//开户行
        accountNumber: null,//账号
        dataType: null,//类型
        registrantName: null,//登记人名称
        status: null,//状态
        billNumber: null,//票据号
        startDate: null,//登记开始时间
        endDate: null,//登记结束时间
        invoiceType: null,//开票类型
        auditStartTime: null,//审核开始时间
        auditEndTime: null,//审核结束时间
        isRepeatOrder: null,//是否重复登记
        isUrgent: null,//是否加急
        invoiceStartDate: null,//开票开始时间
        invoiceEndDate: null,//开票结束时间
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    canJump,
    async handleLinkClick(row, column) {
      if (!this.canJump(row, column)) return;
      try {
        await onJumpLink(row[column.prop], column.prop);
      } catch (err) {
        this.$message.error('小昀工具箱不在线，请开启后使用！');
      }
    },
    downloadAttachment(row) {
      if (row.invoiceFileUrl) {
        window.open(row.invoiceFileUrl, '_blank');
      }
    },
    async changeTime(e, type) {
      if (type == 1) {
        this.ListInfo.startDate = e ? e[0] : null
        this.ListInfo.endDate = e ? e[1] : null
      } else if (type == 2) {
        this.ListInfo.auditStartTime = e ? e[0] : null
        this.ListInfo.auditEndTime = e ? e[1] : null
      } else if (type == 3) {
        this.ListInfo.invoiceStartDate = e ? e[0] : null
        this.ListInfo.invoiceEndDate = e ? e[1] : null
      }
    },
    formatAmount(cellValue) {
      if (!cellValue && cellValue != 0) return;
      return cellValue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    },
    openOrderDetail(row) {
      this.orderDtls = row.orderDtls || []
      this.detailDialogVisible = true
    },
    handleVoid(row) {
      this.$confirm('确定作废该数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { data, success } = await nullifyAfterSalesInvoiceManageData({ id: row.id })
        if (success) {
          this.$message.success('作废成功')
          this.getList()
        } else {
          this.$message.error('作废失败')
        }
      })
    },
    cancelFormMethod() {
      this.registrationVisible = false
      this.getList()
    },
    handleEdit(row) {
      this.editData = JSON.parse(JSON.stringify(row))
      this.dialogTitle = '编辑'
      this.registrationVisible = true
    },
    handleAdd() {
      this.editData = {}
      this.dialogTitle = '新增'
      this.registrationVisible = true
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getAfterSalesInvoiceManageCustomerPage(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach((item) => {
          if (!item.orderDtls?.length) return;
          const firstOrder = item.orderDtls[0];
          item.verify = item.orderDtls.length > 1;
          item.orderNo = firstOrder.orderNo;
          item.goodsName = firstOrder.goodsName;
          item.goodsCount = firstOrder.goodsCount;
          item.actualInvoicedAmount = firstOrder.actualInvoicedAmount;
          item.spec = firstOrder.spec;
          item.unit = firstOrder.unit;
        });
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>
<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 5px;
  flex-wrap: wrap;

  .publicCss {
    width: 150px;
    margin: 2px;
  }
}
</style>
