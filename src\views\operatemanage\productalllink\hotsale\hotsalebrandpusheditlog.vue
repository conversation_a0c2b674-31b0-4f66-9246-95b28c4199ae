<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :tableData='logList'
            @select='selectchange' :isSelection='false' :tableCols='tableCols' :loading="listLoading" style="height:90%"
            :isSelectColumnCols="false">
            <template slot='extentbtn'>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getLogList" />
        </template>
    </my-container>
</template>
<script>
import {
    getHotSaleBrandPushEditLogPageList
} from '@/api/operatemanage/productalllink/alllink'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
const tableCols = [
    { istrue: true, prop: 'logContent', label: '日志内容', sortable: 'custom' },
    { istrue: true, prop: 'opearName', label: '操作人', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'opearName', label: '操作人', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'opearTime', label: '操作时间', width: '150', sortable: 'custom' },
];
export default {
    name: "hotsalebrandpusheditlog",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
    data() {
        return {
            that: this,
            filter: {
            },
            logList: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "opearTime", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            hotSaleBrandPushId: 0,
        };
    },
    async mounted() {
    },
    methods: {
        async loadData(rowinfo) {
            this.hotSaleBrandPushId = rowinfo.id;
            this.onSearch();
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getLogList();
        },
        async getLogList() {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
            };
            params.id = this.hotSaleBrandPushId;
            this.listLoading = true;
            const res = await getHotSaleBrandPushEditLogPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.logList = res.data.list;
            //this.summaryarry=res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
