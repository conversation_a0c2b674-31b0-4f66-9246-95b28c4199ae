<template>
    <container v-loading="pageLoading">
        <template #header>
            <div class="top">
                <el-date-picker style="width: 210px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                    :picker-options="pickerOptions" :clearable="false"></el-date-picker>
                <el-tooltip effect="dark" content="默认为精确匹配在开始或者结尾输入*进行模糊匹配" placement="top">
                    <inputYunhan :key="'3'" :width="'150px'" ref="childGoodsCode" v-model="filter.goodsCode"
                        :inputt.sync="filter.goodsCode" :maxlength="1000" placeholder="商品编码" :clearable="true"
                        @callback="callbackGoodsCode" title="商品编码" style="padding: 0;margin: 0;">
                    </inputYunhan>
                </el-tooltip>
                <el-input class="item" v-model.trim="filter.styleCode" placeholder="请输入系列编码" maxlength="500"
                    style="width: 100px"></el-input>
                <el-select class="item" v-model="filter.groupId" clearable filterable placeholder="请选择运营组"
                    style="width: 140px">
                    <el-option label="所有" value="" />
                    <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select class="item" v-model="filter.isWaitCount" clearable filterable placeholder="是否待领取"
                    style="width: 100px">
                    <el-option label="是" value="1" />
                    <el-option label="否" value="2" />
                </el-select>
                <el-select class="item" v-model="filter.isClaimsCount" clearable filterable placeholder="是否申报"
                    style="width: 100px">
                    <el-option label="是" value="1" />
                    <el-option label="否" value="2" />
                </el-select>
                <el-input-number :min="0" :max="999999" :precision="0" placeholder="昨日销量"
                    v-model="filter.minYestodaySale" style="width: 110px"></el-input-number>
                <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">至</el-button>
                <el-input-number class="item" ::min="0" :max="999999" :precision="0" placeholder="昨日销量"
                    v-model="filter.maxYestodaySale" style="width: 110px"></el-input-number>
                <el-select class="item" v-model="filter.selectedturnover" clearable placeholder="周转范围"
                    style="width: 90px;">
                    <el-option label="周转一天" value="1"></el-option>
                    <el-option label="周转三天" value="3"></el-option>
                </el-select>
                <el-input-number class="item" :min="-100" :max="999" :precision="2" v-model="filter.minturnoverDays"
                    placeholder="周转范围" style="width: 110px"></el-input-number>
                至
                <el-input-number class="item" :min="-100" :max="999" :precision="2" v-model="filter.maxturnoverDays"
                    placeholder="周转范围" style="width: 110px"></el-input-number>
                <el-radio-group v-model="filter.isAll" size="mini" v-if="!currentRole">
                    <el-radio-button label="1">全部</el-radio-button>
                    <el-radio-button label="0">个人</el-radio-button>
                </el-radio-group>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="batchApply">批量申报</el-button>
                <el-button type="primary" @click="onSetAllocation">批量认领</el-button>
                <el-button type="primary" @click="batchRl">批量领取分配</el-button>
                <el-button type="primary" @click="styleCodeSb">系列编码申报</el-button>
            </div>
        </template>
        <!--列表-->
        <!-- @checCheckboxkMethod="checCheckboxkMethod" :isDisableCheckBox="true" -->
        <vxetablebase :id="'goodsCostPriceChgList202301031318001'" :isIndex='true' @select='selectchange'
            :tableData='list' :tableCols='tableCols' :tablefixed='true' :loading='listLoading' :border='true'
            :that="that" ref="vxetable" @cellClick='cellclick' @sortchange='sortchange'
            :checkbox-config="{ labelField: 'id', highlight: true, range: true }" @checkbox-range-end="callback"
              :isIndexFixed="false" :tablekey="'goodscodestockIndex202304221441'">
            <template slot="tbHeader">


            </template>
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <!-- 编码明细 -->
        <el-dialog :visible.sync="dialogVisible" title="详情信息" width="1200" v-dialogDrag>
            <procodedetail :filter="filterdetail" ref="procodedetail"></procodedetail>
        </el-dialog>

        <!-- 销量趋势 -->
        <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="75%" height='700px'
            v-dialogDrag>
            <span>
                <template>
                    <el-form class="ad-form-query" :model="filterchart" @submit.native.prevent label-width="100px">
                        <el-row>
                            <el-col :xs="24" :sm="3" :md="3" :lg="3" :xl="3">
                                <el-radio-group v-model="timeType"
                                    @change="getbirchartOnSearch(goodsCode, timeNum, timeType)">
                                    <el-radio-button label="0">付款维度</el-radio-button>
                                    <el-radio-button label="1">发货维度</el-radio-button>
                                </el-radio-group>
                            </el-col>
                            <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
                                <el-form-item label="日期:">
                                    <el-date-picker style="width: 260px" v-model="filterchart.timerange"
                                        type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                                        :picker-options="pickerOptionspie" :clearable="false"></el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="9" :md="9" :lg="9" :xl="9">
                                <el-form-item>
                                    <el-button type="primary"
                                        @click="getbirchartOnSearch(goodsCode, timeNum, timeType)">刷新</el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </template>
            </span>
            <!-- <span>
                <el-radio-group v-model="timeType" @change="getbirchart(goodsCode, timeNum, timeType)">
                    <el-radio-button label="0">付款维度</el-radio-button>
                    <el-radio-button label="1">发货维度</el-radio-button>
                </el-radio-group>
            </span> -->
            <span>
                <buschar v-if="buscharDialog.visible" ref="buschar" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <!-- 编码进货 -->
        <el-dialog :visible.sync="dialogVisiblecodedpurchase" width="75%" v-dialogDrag>
            <el-tabs v-model="activeName" style="height: calc(100% - 40px);" @tab-click="handleClick">
                <el-tab-pane label="主动申报" name="tab1" style="height: 100%;">
                    <codedpurchase ref="codedpurchase" style="z-index:1000;"></codedpurchase>
                </el-tab-pane>
                <el-tab-pane label="认领数量" name="tab2" style="height: 100%;" :lazy="true">
                    <applycodedpurchase ref="applycodedpurchase" style="z-index:1000;"></applycodedpurchase>
                </el-tab-pane>
            </el-tabs>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisiblecodedpurchase = false">取消</el-button>
                    <my-confirm-button type="submit" :loading="onFinishLoading" @click="onFinish(false)">保存&关闭
                    </my-confirm-button>
                    <my-confirm-button type="submit" :loading="onFinishLoading" @click="onFinish(true)">保存&发起审批
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>

        <!-- 历史进货数据 -->
        <el-dialog :visible.sync="dialogVisiblecodedsearch" width="80%" v-dialogDrag>
            <el-tabs v-model="activeNameNew" style="height: calc(100% - 40px);" @tab-click="handleClickNew">
                <el-tab-pane label="主动申报" name="tab1" style="height: 100%;">
                    <codedpurchasehistory :goodsCodeFilter="goodsCodeFilter" ref="codedpurchasehistory"
                        style="z-index:1000;">
                    </codedpurchasehistory>
                </el-tab-pane>
                <el-tab-pane label="认领数量" name="tab2" style="height: 100%;" :lazy="true">
                    <codedpurchasehistory :goodsCodeFilter="goodsCodeFilter" ref="codedpurchasehistory"
                        style="z-index:1000;">
                    </codedpurchasehistory>
                </el-tab-pane>

            </el-tabs>
        </el-dialog>

        <!-- 上传数据 -->
        <el-dialog title="导入数据" :visible.sync="dialogVisibleimport" width="30%">
            <span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action accept=".xlsx"
                    :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }} </el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleimport = false">关闭</el-button>
            </span>
        </el-dialog>

        <!-- 毛三趋势 -->
        <el-dialog :title="profit3BuscharDialog.title" :visible.sync="profit3BuscharDialog.visible" width="80%"
            v-dialogDrag>
            <span>
                <buschar v-if="profit3BuscharDialog.visible" ref="profit3Buschar"
                    :analysisData="profit3BuscharDialog.data">
                </buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="profit3BuscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>


        <el-dialog title="已领取数据展示" :visible.sync="CollectData.visible" width="80%" v-dialogDrag>
            <div>
                <CollectData ref="CollectData" :filter="CollectData.filter" style="height:600px;"></CollectData>
            </div>
        </el-dialog>

    </container>
</template>
<script>
import { Loading } from 'element-ui';
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import inputYunhan from "@/components/Comm/inputYunhan";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { pageGoodsCodeStock, getPurOrderAnalysisForOperate, importGoodsCodeSellAnalyst, getProfit3RateAnalysis, writeApplyStockGoodsLog } from "@/api/inventory/goodscodestock"
import procodedetail from './procodedetail.vue'
import codedpurchase from './codedpurchase.vue'
import applycodedpurchase from './applycodedpurchase.vue'
import codedpurchasehistory from './codedpurchasehistory.vue'
import buschar from '@/components/Bus/buschar'
import { formatNoLink } from "@/utils/tools";
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import { getTokenKeyValue } from '@/api/admin/auth'
import CollectData from './CollectData.vue'
import middlevue from "@/store/middle.js"
const tableCols = [
    { istrue: true, label: '', width: '100', type: "checkbox", },
    { istrue: true, prop: 'platformName', label: '平台', width: '80', },
    { istrue: true, prop: 'operateSpecialUserName', label: '运营专员', width: '130', },
    { istrue: true, prop: 'userName', label: '运营助理', width: '130', },
    { istrue: true, prop: 'styleCode', label: '系列编码', width: '110', },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '110', align: 'center', type: 'htmlOther', tootip: '暂不支持进货', isBrage: (row) => row.isNotStock == 1, formatter: (row) => formatNoLink(row.goodsCode), },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: '200', },
    { istrue: true, prop: 'warehouseStock', label: '公有可用数', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'personUsableQty', label: '个人可用数', width: '100' },

    {
        istrue: true, label: `销量`, merge: true, prop: 'mergeField',
        cols: [
            { istrue: true, prop: 'salesDay', label: '昨天', width: '80', sortable: 'custom', },
            { istrue: true, prop: 'salesDay3', label: '3天', width: '80', sortable: 'custom', },
            { istrue: true, prop: 'salesDay7', label: '7天', width: '80', sortable: 'custom', type: 'click', handle: (that, row) => that.getbirchart(row.goodsCode, 7, 0) },
            { istrue: true, prop: 'salesDay15', label: '15天', width: '80', sortable: 'custom', type: 'click', handle: (that, row) => that.getbirchart(row.goodsCode, 15, 0) },
        ]
    },
    { istrue: true, prop: 'profit3Rate', label: '毛三利率', width: '80', type: 'click', formatter: (row) => (row.profit3Rate * 100).toFixed(2) + "%", handle: (that, row) => that.showProfit3RateChat(row.goodsCode) },

    {
        istrue: true, label: `预计销量`, merge: true, prop: 'mergeField1',
        cols: [
            { istrue: true, prop: 'expectedSales3', label: '3天', width: '80', tipmesg: '三维算法=（昨日销量+（3日销量/3）+（7日销量/7））/3 *3', sortable: 'custom', },
            { istrue: true, prop: 'expectedSales7', label: '7天', width: '80', tipmesg: '三维算法=（昨日销量+（3日销量/3）+（7日销量/7））/3 *7', sortable: 'custom', },
            { istrue: true, prop: 'expectedSales15', label: '15天', width: '80', tipmesg: '三维算法=（昨日销量+（3日销量/3）+（7日销量/7））/3 *15', sortable: 'custom', },
        ]
    },
    { istrue: true, prop: 'turnoverDays', label: '1天周转天数', width: '75', sortable: 'custom', formatter: (row) => (row.turnoverDays).toFixed(2) },
    { istrue: true, prop: 'turnoverDays3', label: '3天周转天数', width: '75', sortable: 'custom', formatter: (row) => (row.turnoverDays3).toFixed(2) },

    { istrue: true, prop: 'waitFpQty', label: '待分配', width: '75', fixed: 'right', sortable: 'custom' },
    { istrue: true, prop: 'waitCount', label: '待领取', width: '75', fixed: 'right' },
    { istrue: true, prop: 'claimCount', label: '领取中', width: '75', fixed: 'right' },
    { istrue: true, prop: 'count', label: '已领取', width: '75', tipmesg: '最后领取入仓数据', fixed: 'right', type: 'click', handle: (that, row) => that.getcount(row.goodsCode, 1) },
    { istrue: true, prop: 'inTransitNum', label: '采购在途数', width: '75', fixed: 'right' },
    { istrue: true, prop: 'claimstockInQty', label: '申报中', width: '75', fixed: 'right' },
    { istrue: true, prop: 'stockInQty', label: '已申报', tipmesg: '最后申报入仓数据', width: '75', fixed: 'right', type: 'click', handle: (that, row) => that.getcount(row.goodsCode, 2) },
    // {
    //     istrue: true, type: "button", label: '操作', width: "auto", btnList: [{ label: "编码进货", handle: (that, row) => that.handclick(row) },
    //     { label: "历史进货", handle: (that, row) => that.handsearchclick(row) }]
    // }
];
const tableHandles = [
    { label: "导入", handle: (that) => that.startImport(), permission: "api:Inventory:goodscodestock:ImportGoodsCodeSellAnalystAsync" },
];

const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");

export default {
    name: 'YunHanAdminIndex',
    components: { container, MyConfirmButton, vxetablebase, inputYunhan, procodedetail, codedpurchase, applycodedpurchase, codedpurchasehistory, buschar, CollectData },
    data() {
        return {
            that: this,
            activeName: 'tab1',
            activeNameNew: 'tab1',
            filter: {
                startDate: null,
                endDate: null,
                goodsCode: null,
                styleCode: null,
                groupId: null,
                yestodaySaleRange: null,
                minYestodaySale: undefined,
                maxYestodaySale: undefined,
                selectedturnover: null,
                minturnoverDays: undefined,
                maxturnoverDays: undefined,
                isAll: 0,
                timerange: [startTime, endTime],
            },
            filterdetail: {
                goodsCode: null,
                startDate: null,
                endDate: null,
                groupId: null,
            },
            filterchart: {
                startDate: null,
                endDate: null,
                groupId: null,
                timerange: [startDate, endDate]
            },
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "goodsCode", IsAsc: false },
            total: 0,
            sels: [],
            selrows: [],
            grouplist: [],
            chooseTags: [],
            currentRole: false,
            yestodaySaleList: [],
            addForm: {
                id: null,
                goodsCode: null,
                picture: null,
                profit3Rate: null,
                turnoverDays: null,
                turnoverDays3: null,
                expectedSale: null,
                appStatus: null,
                salesDay: 0,
                salesDay7: 0,
                salesDay15: 0,
            },
            timeType: 0, // 时间类型 0：付款维度，1：发货维度
            timeNum: null,
            goodsCodeFilter: {
                goodsCode: null,
                appStatus: null,
            },
            listLoading: false,
            pageLoading: false,
            uploadLoading: false,
            dialogVisibleimport: false,
            dialogVisiblecodedpurchase: false,
            dialogVisiblecodedsearch: false,
            onFinishLoading: false,
            dialogVisible: false,
            buscharDialog: { visible: false, title: "", data: [] },
            profit3BuscharDialog: { visible: false, title: "", data: [] },
            pickerOptions: {
                onPick: ({ maxDate, minDate }) => {
                    this.selectDate = minDate.getTime()
                    if (maxDate) {
                        this.selectDate = ''
                    }
                },
                disabledDate: (time) => {
                    if (this.selectDate !== '') {
                        const one = 30 * 24 * 3600 * 1000
                        const minTime = this.selectDate - one
                        const maxTime = this.selectDate + one
                        return time.getTime() < minTime || time.getTime() > maxTime
                    }
                }
            },
            pickerOptionspie: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近半个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            CollectData: {
                visible: false,
                filter: {
                    goodsCode: null,

                }
            },
            selectRows: null,
            selectedRows: [],
        };
    },
    async created() {
        var param = { key: 'supadmin' }
        var res = await getTokenKeyValue(param)
        if (res?.data == 'True') {
            this.currentRole = true
        } else {
            this.currentRole = false
        }
    },
    async mounted() {
        await this.onSearch();
        await this.init();
    },

    methods: {
        loadData({ selectedRows }) {
          console.log("selectedRows",selectedRows);
          this.selectedRows = selectedRows
          // 循环取出集合中的商品编码
          var goodsCodes = this.selectedRows.map((item) => {
            return item.goodsCode
          })
          console.log("goodsCodes",goodsCodes);
          this.filter.goodsCode = goodsCodes.join(',');
          this.getlist();
        },
        styleCodeSb() {
            if (this.selectRows.length == 0) return this.$message.warning("请选择数据");
            let isSame = this.selectRows.every(item => item.styleCode == this.selectRows[0].styleCode);
            if (!isSame) return this.$message.warning("请选择相同系列编码的数据");

            let istoo = this.istofuc();
            if (istoo) {
                this.$message.warning("不允许操作");
                return
            }

            this.$emit('toStyleCodeLoss')
            setTimeout(() => {
                middlevue.$emit('toStyleCodeLossPage', this.selectRows[0].styleCode)
            }, 500);
        },
        checCheckboxkMethod(row, callback) {
            let isNotStock = row.isNotStock != 1
            callback(isNotStock)
        },
        //展示已认领数据
        showCollectData(row) {
            this.CollectData.filter.goodsCode = row.goodsCode;
            this.CollectData.visible = true;
            setTimeout(async () => {
                await this.$refs.CollectData.onSearch();
            }, 100);

        },

        //批量领取分配
        async batchRl() {
            let self = this;
            if (!self.selrows || self.selrows.length <= 0) {
                this.$message({ message: "请勾选至少一行数据", type: "warning" });
                return;
            }

            let istoo = this.istofuc();
            if (!istoo) {
                this.$message.error('选择存在亏损编码，不允许操作');
                return
            }

            let selData = self.selrows.map((item) => {
                let newItem = { ...item, rlState: 3 };

                return newItem;
            })


            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/goodscodestock/WhStockSurplusRlForm.vue`,
                title: '批量领取分配',
                autoTitle: false,
                args: { selRows: selData },
                height: 700,
                width: '80%',
                //callOk: self.onSearch
            })
        },
        async init() {
            var res1 = await getDirectorGroupList();
            this.grouplist = res1.data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        istofuc() {
            let self = this;
            let isto = true;
            self.selrows.map((item) => {
                if (item.isNotStock == 1) {
                    isto = false;
                }
            })
            if (!isto) {
                return false;
            }
            return true;
        },
        //批量申报
        async batchApply() {
            let self = this;
            if (!self.selrows || self.selrows.length <= 0) {
                this.$message({ message: "请勾选至少一行数据", type: "warning" });
                return;
            }

            let selData = self.selrows.map((item) => {
                let newItem = { ...item };

                return newItem;
            })


            let istoo = this.istofuc();
            if (!istoo) {
                this.$message.error('选择存在亏损编码，不允许操作');
                return
            }


            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/goodscodestock/ApplyStockGoodsForm.vue`,
                title: '批量申报',
                autoTitle: false,
                args: { selRows: selData },
                height: 700,
                width: '80%',
                //callOk: self.onSearch
            })
        },
        // 一键认领
        async onSetAllocation() {
            let self = this;
            if (!self.selrows || self.selrows.length <= 0) {
                this.$message({ message: "请勾选至少一行数据", type: "warning" });
                return;
            }

            let istoo = this.istofuc();
            if (!istoo) {
                this.$message.error('选择存在亏损编码，不允许操作');
                return
            }

            let selData = self.selrows.map((item) => {
                let newItem = { ...item };

                return newItem;
            })

            selData.forEach(f => {
                if (f.waitCount <= 0) {
                    this.$message.warning("温馨提示：请选择有未认领数的编码信息");
                    throw ("禁止操作");
                }
            })
            await writeApplyStockGoodsLog({ goodsCode: selData.map(s => s.goodsCode).join() });

            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/goodscodestock/setgoodscodeallocation.vue`,
                args: { ...selData },
                title: "认领数量",
                width: '90%',
                height: '600px',
                callOk: () => {
                    self.onRefresh();
                }
            });
        },
        async onRefresh() {
            //this.getlist();
        },
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await pageGoodsCodeStock(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.total = res.data.total;
            const data = res.data.list;
            this.list = data
        },
        async onFinish(isEnd) {
            this.onFinishLoading = true;
            var flag = false;
            if (this.activeName == 'tab1') {
                flag = await this.$refs.codedpurchase.onFinish(isEnd);
            } else if (this.activeName == 'tab2') {
                flag = await this.$refs.applycodedpurchase.onFinish(isEnd);
            }
            console.log('数据输出', flag)
            this.onFinishLoading = false;
            if (flag) {
                this.dialogVisiblecodedpurchase = false;
            }
        },
        async clickProfit(row) {
            this.dialogVisible = true;
            this.filterdetail.goodsCode = row.goodsCode
            this.filterdetail.groupId = this.filter.groupId
            this.filterdetail.startDate = null;
            this.filterdetail.endDate = null;
            if (this.filter.timerange) {
                this.filterdetail.startDate = this.filter.timerange[0];
                this.filterdetail.endDate = this.filter.timerange[1];
            }
            this.$nextTick(() => {
                this.$refs.procodedetail.clearData();
                this.$refs.procodedetail.onSearch();
            })
        },
        async callbackGoodsCode(val) {
            // this.inputedit = true;
            this.filter.goodsCode = val;
            this.onSearch();
        },
        async getbirchart(goodsCode, number, type) {
            this.startDate = formatTime(dayjs().subtract(number, 'day'), "YYYY-MM-DD");
            this.endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
            this.filterchart.timerange = [this.startDate, this.endDate];
            this.filterchart.startDate = null;
            this.filterchart.endDate = null;
            if (this.filterchart.timerange) {
                this.filterchart.startDate = this.startDate;
                this.filterchart.endDate = this.endDate;
                this.filterchart.groupId = this.filter.groupId
            }
            this.goodsCode = goodsCode;
            this.timeNum = number;
            this.timeType = type;
            let loadingInstance = Loading.service();
            Loading.service({ fullscreen: true });
            var that = this;
            const params = { goodsCode: goodsCode, day: number, timeType: type, ...this.filterchart };
            //console.log('数据来了', params);
            await getPurOrderAnalysisForOperate(params).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data;
                that.buscharDialog.title = '商品编码：' + goodsCode;
            });
            await this.$refs.buschar.initcharts()
            loadingInstance.close();
        },
        async getcount(goodsCode, type) {
            var para = { goodsCode: goodsCode, type: type }
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/goodscodestock/pagecollectdatadetail.vue`,
                args: { ...para },
                title: "标题",
                width: '90%',
                height: '600px',
                callOk: () => {
                    self.onRefresh();
                }
            });
        },
        async getbirchartOnSearch(goodsCode, number, type) {
            this.filterchart.startDate = null;
            this.filterchart.endDate = null;
            if (this.filterchart.timerange) {
                this.filterchart.startDate = this.filterchart.timerange[0];
                this.filterchart.endDate = this.filterchart.timerange[1];
            }
            this.goodsCode = goodsCode;
            this.timeNum = number;
            this.timeType = type;
            let loadingInstance = Loading.service();
            Loading.service({ fullscreen: true });
            var that = this;
            const params = { goodsCode: goodsCode, day: number, timeType: type, ...this.filterchart };
            //console.log('数据来了', params);
            await getPurOrderAnalysisForOperate(params).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data;
                that.buscharDialog.title = '商品编码：' + goodsCode;
            });
            await this.$refs.buschar.initcharts()
            loadingInstance.close();
        },
        async handclick(row) {
            this.activeName = 'tab1';
            this.dialogVisiblecodedpurchase = true;
            const { goodsCode, picture, profit3Rate, turnoverDays, turnoverDays3, expectedSale, salesDay, salesDay7, salesDay15, } = row;
            this.addForm = {
                ...this.addForm,
                goodsCode,
                picture,
                profit3Rate,
                turnoverDays,
                turnoverDays3,
                expectedSale,
                salesDay,
                salesDay7,
                salesDay15
            };
            this.$nextTick(async () => {
                await this.$refs.codedpurchase.onSearch(this.addForm);
            })
        },
        async handleClick() {
            if (this.activeName == 'tab1') {
                this.$nextTick(async () => {
                    this.addForm.appStatus = 0;
                    await this.$refs.codedpurchase.onSearch(this.addForm);
                })
            } else if (this.activeName == 'tab2') {
                this.$nextTick(async () => {
                    this.addForm.appStatus = 1;
                    await this.$refs.applycodedpurchase.onSearch(this.addForm);
                })
            }
        },
        async handsearchclick(row) {
            this.activeNameNew = 'tab1';
            this.dialogVisiblecodedsearch = true;
            this.goodsCodeFilter.goodsCode = row.goodsCode;
            this.goodsCodeFilter.appStatus = 0;
            this.$nextTick(async () => {
                await this.$refs.codedpurchasehistory.onSearch();
            })
        },
        async handleClickNew() {
            if (this.activeNameNew == 'tab1') {
                this.$nextTick(async () => {
                    this.goodsCodeFilter.appStatus = 0;
                    await this.$refs.codedpurchasehistory.onSearch();
                })
            } else if (this.activeNameNew == 'tab2') {
                this.$nextTick(async () => {
                    this.goodsCodeFilter.appStatus = 1;
                    await this.$refs.codedpurchasehistory.onSearch();
                })
            }
        },
        //新增时提交验证
        finishFormValidate: function () {
            let isValid = false
            this.$refs.addForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        startImport() {
            this.dialogVisibleimport = true;
        },
        cancelImport() {
            this.dialogVisibleimport = false;
        },
        beforeRemove() {
            return false;
        },
        uploadSuccess(response, file, fileList) {
            if (response.code == 200) {
            } else {
                fileList.splice(fileList.indexOf(file), 1);
            }
        },
        async submitUpload() {
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请先选取文件", type: "warning" });
                return false;
            }
            this.fileHasSubmit = true;
            this.uploadLoading = true;
            this.$refs.upload.submit();
        },
        async uploadFile(item) {
            if (!this.fileHasSubmit) {
                return false;
            }
            this.fileHasSubmit = false;
            const form = new FormData();
            form.append("token", this.token);
            form.append("upfile", item.file);
            const res = await importGoodsCodeSellAnalyst(form);
            if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
            else this.$message({ message: res.msg, type: "warning" });
            this.uploadLoading = false;
        },
        async uploadChange(file, fileList) {
            if (fileList && fileList.length > 0) {
                var list = [];
                for (var i = 0; i < fileList.length; i++) {
                    if (fileList[i].status == "success")
                        list.push(fileList[i]);
                    else
                        list.push(fileList[i].raw);
                }
                this.fileList = list;
            }
        },
        uploadRemove(file, fileList) {
            this.uploadChange(file, fileList);
        },
        async cellclick({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }) {
            if (column?.property == 'goodsCode')
                await this.clickProfit(row)
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        // selectchange: function (rows, row) {
        //     this.selids = [];
        //     rows.forEach(f => {
        //         this.selids.push(f);
        //     })
        // },
        callback(val) {
            console.log(val, 'val');
            this.selrows = [...val];

            this.tablelist = [];
            this.tablelist = val;
            var goodsCode = val.map((item) => {
                return item.goodsCode;
            })
            this.chooseTags = goodsCode;
            console.log("goods返回值", this.chooseTags)
        },
        selectchange: function (rows, row) {
            this.selectRows = rows
            //先把当前也的数据全部移除
            this.list.forEach(f => {
                let index = this.chooseTags.findIndex((v) => (v === f.goodsCode));
                if (index !== -1) {
                    this.chooseTags.splice(index, 1);
                    this.selrows.splice(index, 1);
                }
            });
            //把选中的添加
            rows.forEach(f => {
                let index = this.chooseTags.findIndex((v) => (v === f.goodsCode));
                if (index === -1) {
                    this.chooseTags.push(f.goodsCode);
                    this.selrows.push(f);
                    console.log("选中数据", this.selrows);
                }
            });

            ///
            let _this = this;
            if (rows.length > 0) {
                var a = [];
                rows.forEach(element => {
                    let b = _this.list.indexOf(element);
                    a.push(b + 1);
                });

                let d = _this.list.indexOf(row);

                var b = Math.min(...a)
                var c = Math.max(...a)

                a.push(d);
                if (d < b) {
                    var b = _this.list.indexOf(row);
                    var c = Math.max(...a)
                } else if (d > c) {
                    var b = Math.min(...a) - 1
                    var c = Math.max(...a)
                } else {
                    var b = Math.min(...a) - 1
                    var c = _this.list.indexOf(row) + 1;
                }

                let neww = [b, c];
                _this.selids = neww;
            }
            console.log('选择的数据', this.selids)
        },
        async showProfit3RateChat(goodsCode) {
            let loadingInstance = Loading.service();
            let params = { goodsCode: goodsCode, startDate: this.filter.startDate, endDate: this.filter.endDate }
            let that = this;
            await getProfit3RateAnalysis(params).then(res => {
                that.profit3BuscharDialog.visible = true;
                that.profit3BuscharDialog.data = res.data;
                that.profit3BuscharDialog.title = '商品编码：' + goodsCode;
            });
            await this.$refs.profit3Buschar.initcharts()
            loadingInstance.close();
        },
        // 数据分割
        handleRangeChange() {
            const [min, max] = this.filter.yestodaySaleRange.split('-')
            this.filter.minYestodaySale = parseInt(min);
            this.filter.maxYestodaySale = parseInt(max);
        }
    }
};
</script>

<style lang="scss" scoped>
.top {
    display: flex;
    flex-wrap: wrap;
    align-items: center;

    .item {
        width: 200px;
        // margin-right: 10px;
    }
}
</style>
