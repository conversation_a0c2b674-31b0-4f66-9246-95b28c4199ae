<template>
    <MyContainer v-loading="pageLoading">
        <orderabnormalnew></orderabnormalnew>
        <!-- <el-tabs v-model="activeName" style="height: 94%;">
            <el-tab-pane label="缺货订单" name="first" style="height: 100%;" >
                <orderabnormal></orderabnormal>
            </el-tab-pane>
            <el-tab-pane label="缺货订单（新）" name="second" style="height: 100%;">
                <orderabnormalnew></orderabnormalnew>
            </el-tab-pane>
        </el-tabs> -->
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import orderabnormal from "@/views/inventory/orderabnormal";
import orderabnormalnew from "@/views/inventory/orderabnormalnew";
export default {
    name: 'StockoutplateNew',
    components: {MyContainer,orderabnormal,orderabnormalnew},
    data() {
        return {
            pageLoading: false,
            activeName : "second"
        };
    },

    mounted() {
        // this.$router.push({path: '/inventory/orderabnormal'})
    },

    methods: {

    },
};
</script>

<style lang="scss" scoped>

</style>
