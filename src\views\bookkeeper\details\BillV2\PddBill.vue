<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="发生时间">
                    <el-date-picker style="width: 249px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" :clearable="false"
                        end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <!-- <el-form-item label="平台:">
                    <el-select filterable v-model="filter.platform" placeholder="平台" @change="onchangeplatform" clearable style="width: 80px">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
                    </el-select>
                </el-form-item> -->
                <el-form-item label="商户订单号:">
                    <!-- <el-input v-model="filter.OrderNo" :clearable="true" maxlength="20" placeholder="商户订单号" style="width:130px;"/> -->
                    <inputYunhan ref="productCode2" :inputt.sync="filter.OrderNo" v-model="filter.OrderNo"
                        class="publicCss" placeholder="商户订单号/多条请按回车" :clearable="true" :clearabletext="true"
                        :maxRows="2000" :maxlength="600000" @callback="orderNoInnerBack" title="商户订单号">
                    </inputYunhan>
                </el-form-item>
                <el-form-item label="店铺款式编码:">
                    <el-input v-model="filter.ProCode" :clearable="true" maxlength="20" placeholder="店铺款式编码" style="width:130px;"/>
                </el-form-item>
                <el-form-item label="所属店铺:">
                    <YhShopSelector :names="['所有店铺']" :values="[-1]"  platform="2" :checkboxOrRadio="'checkbox'" :addAllWarehouseConcept="true" @onChange="(v)=>{filter.shopCode=v[0].join(','); }">
                    </YhShopSelector>
                </el-form-item>
                <el-form-item label="账单项目:">
                    <el-select filterable v-model="accountTypeist" placeholder="请选择账单项目"  multiple clearable style="width: 130px">
                        <el-option label="交易收入" value="交易收入" />
                        <el-option label="退款" value="退款" />
                        <el-option label="技术服务费" value="技术服务费" />
                        <el-option label="优惠券结算" value="优惠券结算" />
                        <el-option label="多多进宝" value="多多进宝" />
                        <el-option label="其他服务" value="其他服务" />
                        <el-option label="分账" value="分账" />
                        <el-option label="扣款" value="扣款" />
                        <el-option label="提现" value="提现" />
                        <el-option label="转账" value="转账" />
                        <el-option label="平台补贴-补贴汇入" value="平台补贴-补贴汇入" />
                        <el-option label="其他" value="其他" />
                    </el-select>
                </el-form-item>
                <el-form-item label="ERP账务类型:">
                    <el-select filterable v-model="BillTypeList" placeholder="请选择ERP账务类型"  multiple clearable style="width: 130px">
                        <el-option label="物流提醒短信服务费" value="物流提醒短信服务费" />
                        <el-option label="售后补偿现金券扣款" value="售后补偿现金券扣款" />
                        <el-option label="多多进宝" value="多多进宝" />
                        <el-option label="技术服务费" value="技术服务费" />
                        <el-option label="退运费" value="退运费" />
                        <el-option label="欺诈发货" value="欺诈发货" />
                        <el-option label="小额打款" value="小额打款" />
                        <el-option label="优惠券结算" value="优惠券结算" />
                        <el-option label="发票服务承诺未兑现" value="发票服务承诺未兑现" />
                        <!-- <el-option label="多多推广" value="多多推广" /> -->
                        <el-option label="其他支出" value="其他支出" />
                        <el-option label="无" value="无" />
                        <el-option label="其他收入-申诉补回" value="其他收入-申诉补回" />
                        <el-option label="申诉补回-仅退款" value="申诉补回-仅退款" />
                        <el-option label="申诉补回-售后补偿" value="申诉补回-售后补偿" />
                        <el-option label="申诉补回-运费险赔付" value="申诉补回-运费险赔付" />
                        <el-option label="其他收入-分账收入" value="其他收入-分账收入" />
                        <el-option label="其他收入-多单立减" value="其他收入-多单立减" />
                        <el-option label="货款充值单店满返" value="货款充值单店满返" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                <el-switch v-model="filter.isGroup"  active-color="#67C23A"  inactive-color="#409EFF"
                    @change="onSearch"   active-text="汇总查询"  active-value="1" inactive-value="0"  inactive-text="全量查询">
                    </el-switch>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
            @select="selectchange" :tableHandles='tableHandles' @cellclick="cellclick" :loading="listLoading">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="导入数据" :visible.sync="dialogVisible" :close-on-click-modal="false" width="40%" v-dialogDrag>
            <el-alert title="温馨提示：拼多多请选择两个文件上传(最多两个文件)" type="success" :closable="false" style="margin-bottom:10px;">
            </el-alert>
            <span :gutter="20">
                <el-row >
                    <el-col :xs="4" :sm="6" :md="8" :lg="6">
                        <el-date-picker style="width: 100%" v-model="importDialog.filter.YearMonthDay" type="date" format="yyyyMMdd"
                        value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
                      </el-col>
                    <el-col :xs="4" :sm="6" :md="8" :lg="6">
                        <el-select filterable v-model="importDialog.filter.PlatForm" placeholder="请选择平台" clearable>
                            <el-option label="淘系" value="1" />
                            <el-option label="拼多多" value="2" />
                            <el-option label="阿里巴巴" value="4" />
                            <el-option label="抖音" value="6" />
                            <el-option label="京东" value="7" />
                            <el-option label="淘工厂" value="8" />
                            <el-option label="苏宁" value="10" />
                            <el-option label="希音" value="12" />
                            <el-option label="快手" value="14" />
                            <el-option label="淘系微信" value="101" />
                            <el-option label="淘工厂微信" value="801" />
                            <el-option label="京东自营入仓" value="74" />
                            <el-option label="视频号资金账单" value="20" />
                            <el-option label="小红书" value="21" />
                        </el-select>
                    </el-col>
                    <el-col :xs="4" :sm="6" :md="8" :lg="6">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true"  :limit="2" action
                            accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove"
                            :file-list="fileList">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }} </el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { platformlist} from '@/utils/tools'
import { formatTime, formatTime1 } from "@/utils";
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import { importBillingCharge as importBillFee,importPDDBillingCharge } from '@/api/bookkeeper/reportdayV2'
//import { getBillFeePageList } from '@/api/bookkeeper/financialDetail'
import {getNewPddBillingCharge,exportNewPddBillingCharge} from '@/api/bookkeeper/reportday'
import inputYunhan from "@/components/Comm/inputYunhan";
import YhShopSelector from "@/components/YhCom/YhShopSelector";

const tableCols = [
    { istrue: true, prop: 'occurrenceTime', label: '发生时间', tipmesg: '', width: '150', sortable: 'custom', },
    { istrue: true, prop: 'merchantOrderNumber', label: '商户订单号', tipmesg: '', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'proCode', label: '店铺款式编码', tipmesg: '', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'shopCode', sortable: 'custom', label: '店铺名', tipmesg: '', width: '200', formatter: (row) => !row.shopName ? " " : row.shopName },
    { istrue: true, prop: 'accountType', label: '账单项目', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'billType', label: 'ERP账务类型', tipmesg: '', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'amountIncome', label: '金额', tipmesg: '收入金额-支出金额', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'serviceDescription', label: '业务描述', tipmesg: '', width: '400', sortable: 'custom', },
    { istrue: true, prop: 'remark', label: '备注', tipmesg: '', width: 'auto', sortable: 'custom', },
]

const tableHandles = [
    { label: "导入", handle: (that) => that.startImport() },
];

const startTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const curMonth = formatTime1(dayjs().startOf("month").subtract(1, "month"), "yyyyMM");

export default {
    name: 'YunHanAdminIndex',
    components: { container, cesTable, MyConfirmButton,inputYunhan,YhShopSelector },

    data() {
        return {
            that: this,
            filter: {
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime],
                shopCode: null,
                BillType:null
            },
            BillTypeList:[],
            accountTypeist:[],
            platformlist:platformlist,
            importDialog: {
                filter: {
                    YearMonthDay: null,
                    PlatForm: null
                }
            },
            list: [],
            shopList: [],
            summaryarry: {},
            pager: { OrderBy: "occurrenceTime", IsAsc: false },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            pickOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            // onHandNumber: null,
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            sels: [],
            // editparmLoading: false,
            uploadLoading: false,
            // editparmLoading1: false,
            // editparmLoading2: false,
            // editparmVisible: false,
            // editparmVisible1: false,
            // editparmVisible2: false,
            dialogVisible: false,
            listLoading: false,
            // showDetailVisible: false,
            fileList: []
        };
    },

    async mounted() {
        await this.onSearch()
        await this.onchangeplatform()
    },

    methods: {
    async onExport() {
     if (this.onExporting) return;
     try{
        this.filter.startTime = null;
        this.filter.endTime = null;
        if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
        this.uploadLoading = true;
        this.filter.BillType = this.BillTypeList.join(',');
        this.filter.AccountType = this.accountTypeist.join(',');
        const params = {...this.pager,...this.filter}
        var res= await exportNewPddBillingCharge(params);
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','新版拼多多账单费用_' + new Date().toLocaleString() + '.xlsx' )
        this.uploadLoading = false;

        aLink.click()
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
      this.onExporting=false;
     },

        //获取店铺
        async onchangeplatform() {
            this.categorylist = []
            const res1 = await getshopList({ platform: this.filter.platform, CurrentPage: 1, PageSize: 100000 });
            this.filter.shopCode = null
            this.shopList = res1.data.list
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            this.filter.BillType = this.BillTypeList.join(',');
            this.filter.AccountType = this.accountTypeist.join(',');
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await getNewPddBillingCharge(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            this.list = data
        },
        async nSearch() {
            await this.getlist()
        },
        orderNoInnerBack(val) {
            this.filter.OrderNo = val;
        },
        //字体颜色
        // renderRefundStatus(row) {
        //     if (row.refundStatus == '成功退款' || row.refundStatus == '等待退款') {
        //         return "color:red;cursor:pointer;";
        //     } else return "";
        // },
        //开始导入
        startImport() {
           // this.importDialog.filter.platform = null
            this.fileList = []
            this.uploadLoading=false
            this.dialogVisible = true;
        },
        //取消导入
        cancelImport() {
            this.dialogVisible = false;
        },
        uploadSuccess(response, file, fileList) {
            if (response.code == 200) {
            } else {
                fileList.splice(fileList.indexOf(file), 1);
            }
        },
        async submitUpload() {
            if (this.importDialog.filter.PlatForm == null) {
                this.$message({ message: "请选择平台", type: "warning" });
                return false;
            }
            if (this.importDialog.filter.YearMonthDay == null) {
                this.$message({ message: "请先选择日期", type: "warning" });
                return false;
            }
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请选取文件", type: "warning" });
                return false;
            }
            this.fileHasSubmit = true;
            this.uploadLoading = true;
            this.$refs.upload.submit();
        },
        clearFiles(){
            this.$refs['upload'].clearFiles();
        },
        async uploadFile(item) {
            if (!this.fileHasSubmit) {
                return false;
            }
            this.fileHasSubmit = false;
            this.uploadLoading = true;
            const form = new FormData();
            if(this.importDialog.filter.PlatForm==2 || this.importDialog.filter.PlatForm==8)
            {
                if(this.fileList.length<2)
                {
                    this.$message({ message: "请选择两个文件上传", type: "warning" });
                    this.fileList = []
                    this.uploadLoading = false;
                    return false;
                }
            form.append("token", this.token);
            form.append("upfile1", this.fileList[0].raw);
            form.append("upfile2", this.fileList[1].raw);
            form.append("PlatForm", this.importDialog.filter.PlatForm);
            form.append("YearMonthDay", this.importDialog.filter.YearMonthDay);
            let res = await importPDDBillingCharge(form);
                if (res.code == 1) {
                    this.$message({ message: "上传成功,正在导入中...", type: "success" });
                    this.$refs.upload.clearFiles();
                    this.dialogVisible = false;
                }
            this.fileList = []
            this.uploadLoading = false;

            }
            else
            {
                if(this.fileList.length>1)
                {
                    this.$message({ message: "目前只支持拼多多和淘工厂多个文件上传", type: "warning" });
                    this.fileList = []
                    this.uploadLoading = false;
                    return false;
                }
                form.append("token", this.token);
                form.append("upfile",  item.file);
                form.append("PlatForm", this.importDialog.filter.PlatForm);
                form.append("YearMonthDay", this.importDialog.filter.YearMonthDay);
                let res = await importBillFee(form);
                    if (res.code == 1) {
                        this.$message({ message: "上传成功,正在导入中...", type: "success" });
                        this.$refs.upload.clearFiles();
                        this.dialogVisible = false;
                    }
                this.fileList = []
                this.uploadLoading = false;
            }

        },
        async uploadChange(file, fileList) {
            let files=[];
            files.push(file)
            this.fileList = fileList;
        },
        async uploadRemove(file, fileList) {
            this.fileList = []
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event) {

        },
    }
};
</script>

<style lang="scss" scoped>

</style>
