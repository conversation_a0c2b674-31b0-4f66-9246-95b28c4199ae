<template>
  <my-container style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="订单量时序图" name="first1" style="height: 100%">
        <orderQuantity />
      </el-tab-pane>
      <el-tab-pane label="商品波动数据" name="first2" :lazy="true" style="height: 100%">
        <commodityVolatility />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import orderQuantity from "./orderQuantity.vue";
import commodityVolatility from "./commodityVolatility.vue";

export default {
  name: "index",
  components: {
    MyContainer, orderQuantity, commodityVolatility
  },
  data() {
    return {
      that: this,
      activeName: "first1",
    };
  },
};
</script>

<style lang="scss" scoped></style>
