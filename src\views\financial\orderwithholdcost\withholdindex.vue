<template>
    <container v-loading="pageLoading">
       <template #header>
        <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            <el-form-item label="结算月份:">
                <el-date-picker style="width: 120px" v-model="filter.settMonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="选择"          
                :picker-options="pickOptions" @change="onSearch"></el-date-picker>
            </el-form-item>        
            <el-form-item label="计算状态:">
                <el-select v-model="filter.computeStatus" placeholder="计算状态" style="width: 200px" :clearable="true"  @change="onSearch">
                    <el-option v-for="item in computeStatusList" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>
            </el-form-item>
            <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            </el-form-item>
        </el-form>
       </template> 

        <el-tabs v-model="activeName" style="height: 94%;">
        <el-tab-pane label="快递扣款" name="ordernoexpressfee" style="height: 100%;">
            <withholdfree :filter="filter" ref="withholdfree" @onstartImport='onstartImport' @onExport="onExport" @ondeleteByBatch='ondeleteByBatch' :shopList="shopList" @changePlatform="changePlatform" :platformList="platformList"/>
        </el-tab-pane>
        <el-tab-pane label="计算" name="compute" style="height: 100%;">
            <withhodscompute :filter="filter" ref="withhodscompute" @onstartImport='onstartImport' @ondeleteByBatch='ondeleteByBatch' @onstartcomput='onstartcomput' :shopList="shopList" :platformList="platformList" />
        </el-tab-pane>
        <el-tab-pane label="月报" name="computeresult" style="height: 100%;">
            <withholdcomputeresult :filter="filter" ref="withholdcomputeresult"  :shopList="shopList" @changePlatform="changePlatform" :platformList="platformList" @onExport="onExport"/>
        </el-tab-pane>
    </el-tabs>

          <el-dialog title="导入" :visible.sync="dialogVisible" width="30%" v-dialogDrag>
            <span>
                <el-form class="ad-form-query" :inline="true" :model="importDialog.filter" @submit.native.prevent>
                <el-form-item label="结算月份:">
                    <el-date-picker style="width: 120px" v-model="importDialog.filter.setMonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="选择" :picker-options="pickOptions">              
                    </el-date-picker>
                </el-form-item>
            </el-form>
                <el-row style="margin: 10px;">
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload
                        ref="upload"
                        class="upload-demo"
                        :auto-upload="false"
                        :multiple="false"
                        :limit="1"
                        action
                        accept=".xlsx"
                        :http-request="uploadFile"
                        :file-list="fileList"
                        :data="fileparm">
                        <template #trigger>
                            <el-button size="small" type="primary">选取文件</el-button>
                        </template>
                        <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
            </el-dialog>

            <el-dialog title="删除" :visible.sync="dialogdeletebatchNumberVisible" width="500px" v-dialogDrag>
                <el-form class="ad-form-query" :inline="true" :model="deletefilter" @submit.native.prevent label-position="right" label-width="100px">
                    <el-form-item label="结算月份:">
                        <el-date-picker style="width: 200px" v-model="deletefilter.settMonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="选择"></el-date-picker>
                    </el-form-item>
                    <!-- <el-form-item label="平台">
                        <el-select
                            v-model="filter.platform"
                            placeholder="平台" style="width: 100px"  @change="changePlatform"
                            :clearable="true" :collapse-tags="true"  filterable>
                            <el-option
                                v-for="item in platformList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"/>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="店铺:">
                        <el-select
                        v-model="deletefilter.shopCode" @change="onSearch"
                        placeholder="店铺" style="width: 200px"
                        :clearable="true" :collapse-tags="true"  filterable>
                        <el-option
                            v-for="item in shopList"
                            :key="item.shopCode"
                            :label="item.shopName"
                            :value="item.shopCode"/>
                        </el-select>
                    </el-form-item> -->
                    <el-form-item >
                        <el-button type="primary" @click="deleteByBatch">删除</el-button>
                    </el-form-item>
                </el-form>
            <!-- <span slot="footer" class="dialog-footer">
                <el-button @click="dialogdeletebatchNumberVisible = false">关闭</el-button>
            </span> -->
            </el-dialog>

            <el-dialog title="计算分摊" :visible.sync="dialogcomputVisible" width="40%" v-dialogDrag>
      <el-form class="ad-form-query" :inline="true" :model="computfilter" @submit.native.prevent>
            <el-form-item label="平台:">
                <el-select
                  v-model="computfilter.platform"
                  placeholder="平台" style="width: 90px"  @change="changePlatform"
                  :clearable="true" :collapse-tags="true"  filterable>
                  <el-option
                    v-for="item in platformList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"/>
                </el-select>            
            </el-form-item>
            <el-form-item label="结算月份:">
                <el-date-picker style="width: 100%" v-model="computfilter.settMonth" type="month" format="yyyyMM"  
                  value-format="yyyyMM" placeholder="选择月份" :picker-options="pickOptions"
                ></el-date-picker>
            </el-form-item>
            <el-form-item label="月报类型">
              <el-select filterable v-model="computfilter.Version" placeholder="类型" style="width: 100px">
                <el-option label="工资月报" value="v1"></el-option>
                <el-option label="参考月报" value="v2"></el-option>
            </el-select>
            </el-form-item>
            <el-form-item >
                <el-button style="float:right" type="primary" @click="oncomput">计算分摊</el-button>
            </el-form-item>
          </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogcomputVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    </container>
</template>

<script>
import {
  importOrderWithholdFee,
  deleteOrderWithholdFee,
  computeOrderWithholdCost,
  exportOrderWithholdFee,
  exportOrderWithholdCostShare,
} from '@/api/financial/ordercost'
import container from '@/components/my-container/nofooter'
import dayjs from "dayjs";
import { formatTime1 } from "@/utils";
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import { rulePlatform} from "@/utils/formruletools";
import withholdfree from './withholdfree.vue'
import withhodscompute from './withholdcompute.vue'
import withholdcomputeresult from './withholdcomputeresult.vue'

var curMonth =formatTime1( dayjs().startOf("month").subtract(1,"month"),"yyyyMM");
export default {
    name: 'YunhanAdminIndex',
    components: {container, withholdfree, withhodscompute, withholdcomputeresult},

    data() {
        return {
            activeName: 'ordernoexpressfee',
            filter: {
                startTime: null,
                endTime: null,
                settMonth:curMonth,
                timerange:null,
                shopCode:'',
                orderNo:'',
            },
            deletefilter: {shareFeeType:0,batchNumber:'' },
            computfilter: {settMonth:'',shareFeeType:0 },
            expresscompanylist: [],
            onimportfilter: {shareFeeType:0 },
            pageLoading: false,    
            dialogVisible: false,
            uploadLoading:false,
            importFilte:{},
            fileList:[],
            fileparm:{},
            dialogdeletebatchNumberVisible:false,
            dialogcomputVisible:false,
            importDialog:{
                filter:{
                setMonth:curMonth,
                }
            },
            pickOptions:{
                disabledDate(time){
                return time.getTime() > Date.now()
                }
            },
            shopList:[],
            platformList:[],
            computeStatusList:[
                {label:"已计算",value:1},
                {label:"未计算",value:0},
            ],
        };
    },

    async mounted() {
        await this.setPlatform();
        await this.setShopList();   
    },

    methods: {
        //设置平台下拉
        async setPlatform() {
            var pfrule = await rulePlatform();
            this.platformList = pfrule.options;
        },
        async changePlatform(val){
            await this.setShopList(val);
        },
        //设置店铺下拉
        async setShopList(val){
            const res = await getshopList({platform:val,CurrentPage:1,PageSize:1000});
            this.shopList=res.data.list;
            this.filter.shopCode='';
        },
        onSearch(){
            this.filter.startTime=null;
            this.filter.endTime=null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            switch (this.activeName){
                case "ordernoexpressfee":
                this.$refs.withholdfree.onSearch();
                break;
                case "virtualtype":
                this.$refs.virtualtype.onSearch();
                break;
                case "packagemonthaveragecost":
                this.$refs.packagemonthaveragecost.onSearch();
                break;
                case "compute":
                this.$refs.withhodscompute.onSearch();
                break;
                case "computeresult":
                this.$refs.withholdcomputeresult.onSearch();
                break;
      } 
        },
        async onstartImport(shareFeeType){
            this.dialogVisible=true;      
            this.onimportfilter.shareFeeType=shareFeeType;
            //await this.setShopList(this.importDialog.filter.platform);
        },
        async ondeleteByBatch(shareFeeType) {
            this.dialogdeletebatchNumberVisible=true;
            this.deletefilter.shareFeeType=shareFeeType;
            this.deletefilter.batchNumber='';
        },
        async onstartcomput(shareFeeType) {
            this.dialogcomputVisible=true;
            this.computfilter.shareFeeType=shareFeeType;
            this.computfilter.yearmonth='';
        },
        uploadSuccess(response, file, fileList) {
            if (response.code == 200) {
            } else {
                fileList.splice(fileList.indexOf(file), 1);
            }
        },
        submitUpload() {     
            if(!this.importDialog.filter.setMonth){
                this.$message({message: "请选择结算月份", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        async uploadFile(item) {
            console.log('导入来了',this.onimportfilter.shareFeeType)
            if(!item||!item.file||!item.file.size){
                this.$message({message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading=true
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("setMonth", this.importDialog.filter.setMonth);
            var res;
            if (this.onimportfilter.shareFeeType==0) res= await importOrderWithholdFee(form);
        
            if (res.code==1) this.$message({message: "上传成功,正在导入中...", type: "success" });
            else this.$message({message: res.msg, type: "warning" });
            this.uploadLoading=false
            },
        async deleteByBatch() {
            if (!this.deletefilter.settMonth) {
            this.$message({type: 'warning',message: '请输入结算月份!'});
            return;
            } 
            // if (!this.deletefilter.shopCode) {
            // this.$message({type: 'warning',message: '请选择店铺!'});
            // return;
            // } 
            this.$confirm('确认删除, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
                }).then(async () => {
                    var res;
                    var params={...this.deletefilter};
                    switch (this.activeName){
                    case "ordernoexpressfee":
                        res = await deleteOrderWithholdFee(params)
                        break;
                    case "virtualtype":
                        break;
                    case "packagemonthaveragecost":
                        break;
                    case "compute":
                        break;
                    case "computeresult":
                        break;
                    }
                    
                    if (!res?.success) {return }
                    this.$message({type: 'success',message: '删除成功!'});
                    this.onSearch()
                }).catch(() => {
                this.$message({type: 'info',message: '已取消删除'});
                });
            },
            async oncomput(){
                if (!this.computfilter.settMonth) {
                    this.$message({type: 'warning',message: '请选择年月!'});
                    return;
                }
                this.$confirm('确认计算分摊, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
                    }).then(async () => {
                        const res = await computeOrderWithholdCost(this.computfilter)
                        if (!res?.success) {return }
                        this.$message({type: 'success',message: '提交成功,正在后台计算分摊...'});
                        this.onSearch();
                    }).catch(() => {
                    this.$message({type: 'info',message: '已取消计算'});
                    });
            },
            async onExport(){
                if(!this.filter.settMonth){
                    this.$message({message: "请选择结算月份", type: "warning" });
                    return false;
                }
                var res;
                var fileName="";
                this.filter.startTime=null;
                this.filter.endTime=null;
                if (this.filter.timerange) {
                    this.filter.startTime = this.filter.timerange[0];
                    this.filter.endTime = this.filter.timerange[1];
                }
                var params={
                    ...this.filter
                };
                switch (this.activeName){
                    case "ordernoexpressfee":
                    res = await exportOrderWithholdFee(params);
                    fileName="快递扣款费用_"+this.filter.settMonth;
                    break;
                    case "virtualtype":
                    break;
                    case "packagemonthaveragecost":
                    break;
                    case "compute":
                    break;
                    case "computeresult":
                    res = await exportOrderWithholdCostShare(params);
                    fileName="快递扣款_"+this.filter.settMonth;
                    break;
                }
                if(!res?.data) return;
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download',fileName+'_' + new Date().toLocaleString() + '.xlsx' )
                aLink.click();
        }
    },
};
</script>

<style lang="scss" scoped>

</style>