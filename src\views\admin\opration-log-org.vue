<template>
  <my-container>
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-row>
          <el-col>
            <el-form-item label="环境">
              <el-select v-model="filter.environment" clearable placeholder="环境" style="width: 120px">
                <el-option label="正式" value="Production"></el-option>
                <el-option label="测试" value="Development"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-input v-model="filter.username" placeholder="操作人" clearable @keyup.enter.native="onSearch">
                <template #prefix>
                  <i class="el-input__icon el-icon-search" />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="filter.apiname" placeholder="接口名称" clearable maxlength="40"></el-input>
            </el-form-item>
            <el-form-item>
              <el-date-picker v-model="timeList" type="daterange" unlink-panels range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false" :picker-options="pickerOptions" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="维度复选：">
            <!-- <el-checkbox v-model="filter.department" style="margin-right: 5px;">操作部门</el-checkbox> -->
            <el-checkbox v-model="filter.groupusername" style="margin-right: 5px;">操作人</el-checkbox>
            <el-checkbox v-model="filter.grouprequestpath" style="margin-right: 5px;">接口地址</el-checkbox>
            <el-checkbox v-model="filter.groupapiname" style="margin-right: 5px;">接口名称</el-checkbox>
            <el-checkbox v-model="filter.groupmachinename" style="margin-right: 5px;">机器名称</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="onExport">导出</el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </template>

    <el-table v-loading="listLoading" :data="list" highlight-current-row height="'100%'" style="width: 100%;height:100%;"
      @sort-change='sortchange'>
      <!-- <el-table-column prop="id" label="编号" width="80" />
      <el-table-column prop="createdUserName" label="操作账号" width="110">
        <template #default="{ row }">
          {{ row.createdUserName }}
        </template>
      </el-table-column>
      <el-table-column prop="ip" label="IP地址" width="160" />
      <el-table-column prop="apiLabel" label="操作名称" width="" />
      <el-table-column prop="apiPath" label="操作接口" width="" /> -->
      <!-- <el-table-column prop="browser" label="浏览器" width="100" />
      <el-table-column prop="os" label="操作系统" width="100" /> -->
      <!-- <el-table-column prop="elapsedMilliseconds" width="70">
        <template #header>
          耗时<br>毫秒
        </template>
      </el-table-column> -->
      <!-- <el-table-column prop="elapsedMilliseconds" label="耗时毫秒" width="80" />
      <el-table-column prop="status" label="操作状态" width="80">
        <template #default="{ row }">
          <el-tag :type="row.status ? 'success' : 'danger'" disable-transitions>{{ row.status ? '成功' : '失败' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="msg" label="操作消息" width="120" /> -->

      <el-table-column prop="environment" label="环境" width="120" />
      <el-table-column prop="requestpath" label="接口地址" width="500" v-if="checkcol.requestpathcheck" />
      <el-table-column prop="apiname" label="接口名称" width="300" v-if="checkcol.apinamecheck" />
      <el-table-column prop="machinename" label="机器名称" width="200" v-if="checkcol.machinenamecheck" />
      <el-table-column prop="username" label="操作人" width="120" v-if="checkcol.usernamecheck" />
      <el-table-column prop="createdtime" label="操作时间" :formatter="formatCreatedTime" width="180"
        v-if="checkcol.createdtimecheck" />
      <el-table-column prop="usecount" label="操作频率(次)" width="120" sortable='custom' />
    </el-table>

    <template #footer>
      <my-pagination ref="pager" :total="total" @get-page="getList" />
    </template>
  </my-container>
</template>

<script>
import dayjs from 'dayjs'
import { pickerOptions } from '@/utils/tools'
import { formatTime } from '@/utils'
import { getOprationLogPage, getOprationLogPage2, exportAsync2 } from '@/api/admin/opration-log'
import MyContainer from '@/components/my-container'
// import { ElRow } from 'element-ui/types/row'  

export default {
  name: 'OprationLog',
  components: { MyContainer },//, ElRow
  data() {
    return {
      pickerOptions,
      timeList: null,//时间范围
      filter: {
        username: '',
        environment: "Production",
        apiname: null,
        startDate: null,
        endDate: null,

      },
      pager: { OrderBy: "", IsAsc: true },
      list: [],
      total: 0,
      listLoading: false,

      checkcol: {
        createdtimecheck: false,
        requestpathcheck: false,
        apinamecheck: false,
        machinenamecheck: false,
        usernamecheck: false,
      },

    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    formatCreatedTime: function (row, column, time) {
      return formatTime(time, 'YYYY-MM-DD HH:mm')
    },

    setCols() {
      this.checkcol = {
        createdtimecheck: false,
        requestpathcheck: false,
        apinamecheck: false,
        machinenamecheck: false,
        usernamecheck: false,
      };
      if (this.filter.groupusername == true ||
        this.filter.grouprequestpath == true ||
        this.filter.groupapiname == true ||
        this.filter.groupmachinename == true) {
        if (this.filter.groupusername == true) {
          this.checkcol.usernamecheck = true;
        }
        if (this.filter.grouprequestpath == true) {
          this.checkcol.requestpathcheck = true;
        }
        if (this.filter.groupapiname == true) {
          this.checkcol.apinamecheck = true;
        }
        if (this.filter.groupmachinename == true) {
          this.checkcol.machinenamecheck = true;
        }
      }
      else {
        this.checkcol.createdtimecheck = true;
        this.checkcol.requestpathcheck = true;
        this.checkcol.apinamecheck = true;
        this.checkcol.machinenamecheck = true;
        this.checkcol.usernamecheck = true;
      }
    },
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getList()
    },
    getpara() {
      if (!this.timeList) {
        //默认时间为当前时间往前推一个月
        this.filter.startDate = dayjs().subtract(2, 'day').format('YYYY-MM-DD')
        this.filter.endDate = dayjs().format('YYYY-MM-DD')
        this.timeList = [this.filter.startDate, this.filter.endDate]
      }
      else {
        this.filter.startDate = dayjs(this.timeList[0]).format('YYYY-MM-DD');
        this.filter.endDate = dayjs(this.timeList[1]).format('YYYY-MM-DD');
      }
      const pager = this.$refs.pager.getPager()
      const para = {
        ...pager,... this.pager,
        ...this.filter
      }
      return para;
    },
    // 获取列表
    async getList() {
      this.setCols();
      let para = this.getpara();
      this.listLoading = true
      const res = await getOprationLogPage2(para)
      this.listLoading = false

      if (!res?.success) {
        return
      }

      this.total = res.data.total
      this.list = res.data.list
    },
    async onExport() {
      let para = this.getpara();
      this.listLoading = true
      const res = await exportAsync2(para)
      this.listLoading = false
      if (!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '操作日志导出_' + name + '_' + new Date().toLocaleString() + '.xlsx');
      aLink.click()
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
  }
}
</script>
