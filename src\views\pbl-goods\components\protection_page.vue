<template>
    <div>
        <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
            <el-form-item label="保护期限" prop="extendDay" v-if="type == 0">
                <el-input-number v-model="ruleForm.extendDay" :min="0" :max="9999" placeholder="保护期限(天)"
                    :controls="false" :precision="0" />
            </el-form-item>
            <el-form-item label="保护理由" prop="reason">
                <el-input type="textarea" placeholder="请输入保护理由" :rows="4" v-model="ruleForm.reason" maxlength="200"
                    show-word-limit style="width: 400px;" />
            </el-form-item>
            <el-form-item label="参考图片" prop="picture">
                <uploadimgFile :accepttyes="accepttyes" :isImage="true" :uploadInfo="picture" :keys="[1, 1]"
                    v-if="uploadimgFileVisable" @callback="getImg" :imgmaxsize="9" :limit="9" :multiple="true">
                </uploadimgFile>
            </el-form-item>
            <el-form-item label="参考附件" prop="annex">
                <uploadimgFile :filemaxsize="1" :accepttyes="fileAccepttyes" :isImage="true" :uploadInfo="annex"
                    :keys="[1, 1]" v-if="uploadimgFileVisable" @callback="getFile" :imgmaxsize="0" :limit="1" :multiple="true">
                </uploadimgFile>
            </el-form-item>
            <el-form-item>
                <div style="display: flex;justify-content: end;">
                    <el-button @click="close">取消</el-button>
                    <el-button type="primary" @click="submitForm('ruleForm')" v-throttle="1000">提交</el-button>
                </div>

            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import request from '@/utils/request'
export default {
    props: {
        assignIdList: {
            type: Array,
            default: () => []
        },
        type: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            ruleForm: {
                type: this.type,//申请类型 0:延长 1:撤销
                assignIds: this.assignIdList,
                // extendDay: '',
                reason: '',
                picture: '',
                annex: '',
            },
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            fileAccepttyes: '.doc,.docx,.xls,.xlsx,',
            uploadimgFileVisable: false,
            picture: [],//参考图片
            annex: [],//参考附件
            rules: {
                extendDay: [
                    { required: true, message: '请输入保护期限', trigger: 'blur' },
                ],
                reason: [
                    { required: true, message: '请输入保护理由', trigger: 'blur' }
                ],
                // picture: [
                //     { required: true, message: '请上传参考图片', trigger: 'blur' }
                // ],
                // annex: [
                //     { required: true, message: '请上传参考附件', trigger: 'blur' }
                // ]
            }
        }
    },
    components: {
        uploadimgFile
    },
    mounted() {
        this.uploadimgFileVisable = true
    },
    methods: {
        close() {
            this.$emit('close')
        },
        getImg(data) {
            if (data) {
                this.picture = data ? data : []
                this.ruleForm.picture = data.map(item => item.url).join(',')
            }
        },
        getFile(data) {
            if (data) {
                this.annex = data ? data : []
                this.ruleForm.annex = data.map(item => item.url).join(',')
            }
        },
        submitForm(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    const { success } = await request.post('/api/bookkeeper/PblGoodPdd/Protect/Appval', {
                        ...this.ruleForm,
                        ids: this.assignIdList
                    })
                    if (success) {
                        this.$message({
                            type: 'success',
                            message: '操作成功!'
                        });

                    }
                } else {
                    return false;
                }
                this.$emit('getList')
                this.$emit('close')
            });
        },
    }
}
</script>

<style scoped lang="scss"></style>