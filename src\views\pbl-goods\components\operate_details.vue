<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="query.startTime" :endDate.sync="query.endTime" class="publicCss" />
                <el-input v-model.trim="query.styleCode" placeholder="系列编码" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="query.productId" placeholder="宝贝ID" maxlength="50" clearable
                    class="publicCss" />
                <el-select v-model="query.status" placeholder="状态" class="publicCss" clearable>
                    <el-option label="待审批" :value="0" />
                    <el-option label="同意" :value="1" />
                    <el-option label="拒绝" :value="2" />
                </el-select>
                <el-select v-model="query.type" placeholder="操作类型" class="publicCss" clearable>
                    <el-option label="挂走" :value="0" />
                    <el-option label="下架" :value="1" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
            </div>
        </template>
        <vxetablebase :ib="'operate_details202408041838'" ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :toolbarshow="false" :is-select-column="true" :is-index-fixed="false"
            style="width: 100%; margin: 0;height: 400px;" :showsummary="data.summary ? true : false"
            :summaryarry="data.summary" @sortchange="sortchange" @onTrendChart="trendChart" />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false"
            direction="btt" :modal="false">
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
                    :picker-options="pickerOptions" style="margin: 10px" @change="
                        trendChart({
                            ...chatPropOption,
                            startDate: $event[0],
                            endDate: $event[1],
                        })
                        " />
                <buschar v-if="!chatProp.chatLoading" :analysis-data="chatProp.data" />
            </div>
            <div v-else v-loading="chatProp.chatLoading" />
        </el-drawer>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, formatLinkProCode } from '@/utils/tools'
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, dateRange
    },
    props: {
        styleCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            that: this,
            query: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                styleCode: this.styleCode,
                summarys: []
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        await this.getCol();
        await this.getList()
    },
    methods: {
        async getCol() {
            const { data, success } = await request.post('/api/bookkeeper/pblGoodPdd/Product/GetColumns')
            if (success) {
                data.forEach(item => {
                    if (item.prop == 'productId') {
                        item.type = 'html'
                        item.formatter = (row) => formatLinkProCode(row.platform, row.productId)
                    }
                })
                this.tableCols = data;
                this.query.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.query.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post('/api/bookkeeper/pblGoodPdd/Product/PageGetData', this.query)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        async trendChart(option) {
            var endDate = null;
            var startDate = null;

            if (option.startDate && option.endDate) {
                startDate = option.startDate;
                endDate = option.endDate;
            } else {
                endDate = option.date;
                startDate = new Date(option.date);
                startDate.setDate(option.date.getDate() - 30);

                startDate = dayjs(startDate).format("YYYY-MM-DD");
                endDate = dayjs(endDate).format("YYYY-MM-DD");
            }
            option.filter.filters = option.filter.filters.filter((item) => item.field !== option.dateField);
            option.filter.filters.push({
                field: option.dateField,
                operator: "GreaterThanOrEqual",
                value: startDate,
            });
            option.filter.filters.push({
                field: option.dateField,
                operator: "LessThanOrEqual",
                value: endDate,
            });

            option.startDate = startDate;
            option.endDate = endDate;

            this.chatProp.chatTime = [startDate, endDate];

            this.chatProp.chatLoading = true;
            const { data, success } = await request.post('/api/bookkeeper/pblGoodPdd/Product/GetTrendChart', option)
            if (success) {
                this.chatProp.data = data;
            }

            this.chatProp.chatLoading = false;
            this.chatProp.chatDialog = true;

            this.chatPropOption = option;
        },
        async changeTime(e) {
            this.query.startTime = e ? e[0] : null
            this.query.endTime = e ? e[1] : null
            await this.getList()
        },
        //每页数量改变
        Sizechange(val) {
            this.query.currentPage = 1;
            this.query.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.query.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.query.orderBy = prop
                this.query.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
