<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <inputYunhan ref="refstyleCodes" :inputt.sync="ListInfo.styleCodes" v-model="ListInfo.styleCodes"
          placeholder="款式编码/多条按回车(末尾加*号模糊搜索)" :clearable="true" :clearabletext="true" :maxRows="500"
          :maxlength="1000000" @callback="callbackGoodsCode($event, 'styleCodes')" title="款式编码"
          style="width: 255px;margin:0 5px 0 0;" width="255px">
        </inputYunhan>
        <inputYunhan ref="refgoodsCodes" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
          placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500" :maxlength="1000000"
          @callback="callbackGoodsCode($event, 'goodsCodes')" title="商品编码" style="width: 180px;margin:0 5px 0 0;"
          width="180px">
        </inputYunhan>
        <el-input-number v-model="ListInfo.length" :min="0" :max="10000000" placeholder="长(mm)" class="publicCss"
          :controls="false" auto-complete="off" :precision="2" />
        <el-input-number v-model="ListInfo.width" :min="0" :max="10000000" placeholder="宽(mm)" class="publicCss"
          :controls="false" auto-complete="off" :precision="2" />
        <el-input-number v-model="ListInfo.height" :min="0" :max="10000000" placeholder="高(mm)" class="publicCss"
          :controls="false" auto-complete="off" :precision="2" />
        <number-range :min.sync="ListInfo.weightMin" :max.sync="ListInfo.weightMax" min-label="质量(g) - 最小值"
          max-label="质量(g) - 最大值" class="publicCss" style="width: 200px;" />
        <el-select v-model="ListInfo.groupIds" clearable filterable multiple collapse-tags placeholder="分类"
          class="publicCss" style="width: 170px">
          <el-option label="所有" value="" />
          <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <inputYunhan ref="reflabels" :inputt.sync="ListInfo.labels" v-model="ListInfo.labels"
          placeholder="标签/多条按回车(末尾加*号模糊搜索)" :clearable="true" :clearabletext="true" :maxRows="500" :maxlength="1000000"
          @callback="callbackGoodsCode($event, 'labels')" title="标签" style="width: 180px;margin:0 5px 0 0;"
          width="180px">
        </inputYunhan>
        <chooseWareHouse v-model="ListInfo.wmsIds" style="width: 170px;margin:0 5px 0 0;" multiple placeholder="仓库"
          class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
      </div>
    </template>
    <vxetablebase ref="table" :id="'commodityInventoryInformation202504261018'"
      :tablekey="'commodityInventoryInformation202504261018'" :loading="loading" :that="that" :is-index="true"
      :hasexpand="true" :tablefixed="true" :has-seq="false" :border="true" :table-data="data.list"
      :table-cols="tableCols" :is-selection="false" :is-select-column="true" :is-index-fixed="false"
      style="width: 100%; margin: 0;" height="100%" :showsummary="data.summary ? true : false"
      :summaryarry="data.summary" @sortchange="sortchange" :treeProp="{ childrenField: 'children' }" hasSeq="true" />
    <template #footer>
      <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/inventory/GoodsInvInfo/'
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import { mergeTableCols } from '@/utils/getCols'
export default {
  name: "commodityInventoryInformation",
  components: {
    MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, chooseWareHouse
  },
  data() {
    return {
      api,
      platformlist,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        summarys: [],
        styleCodes: '',//款式编码
        goodsCodes: '',//商品编码
        labels: '',//标签
        length: undefined,//长
        width: undefined,//宽
        height: undefined,//高
        weightMin: undefined,//质量(g) - 最小值
        weightMax: undefined,//质量(g) - 最大值
        groupIds: [],//分类
        wmsIds: [],//仓库
      },
      grouplist: [],
      isExport: false,
      data: {},
      chatProp: {
        chatDialog: false, // 趋势图弹窗
        chatTime: null, // 趋势图时间
        chatLoading: true, // 趋势图loading
        data: [], // 趋势图数据
      },
      timeRanges: [],
      tableCols: [],
      tableData: [],
      total: 0,
      loading: true,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getCol();
    await this.getList()
    await this.init()
  },
  methods: {
    // 导出
    async exportProps() {
      this.isExport = true
      await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
        this.isExport = false
      })
    },
    async init() {
      let res1 = await getDirectorGroupList();
      this.grouplist = res1.data.map(item => {
        return { value: item.key, label: item.value };
      });
    },
    callbackGoodsCode(val, type) {
      const map = {
        styleCodes: () => (this.ListInfo.styleCodes = val),
        goodsCodes: () => (this.ListInfo.goodsCodes = val),
        labels: () => (this.ListInfo.labels = val),
      };
      map[type]?.();
    },
    async getCol() {
      const { data, success } = await request.post(`${this.api}GetColumns`)
      if (success) {
        data.forEach((item, index) => {
          if (item.prop == 'orderNoInner') {
            item.type = 'orderLogInfo'
            item.orderType = 'orderNoInner'
          }
          if (item.prop == 'proCode') {
            item.type = 'html'
            item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
          }
          if (item.prop == 'children') {
            data.splice(index, 1)
          }
        })
        this.tableCols = mergeTableCols(data)
        this.ListInfo.summarys = data
          .filter((a) => a.summaryType)
          .map((a) => {
            return { column: a["sort-by"], summaryType: a.summaryType };
          });
      }
    },
    async getList(type) {
      if (type === "search") {
        this.ListInfo.currentPage = 1;
        this.$refs.pager.setPage(1);
      }
      this.loading = true;
      // 使用时将下面的方法替换成自己的接口
      try {
        const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
        if (success) {
          this.data = data;
          this.data.list.forEach((item) => {
            item.clWeight = item.clWeight ? String(item.clWeight) : '';
          })
          console.log(this.data.list);
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.loading = false;
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 5px;

  .publicCss {
    width: 115px;
    margin: 0 5px 0 0;
  }
}

::v-deep .el-select__tags-text {
  max-width: 50px;
}
</style>
