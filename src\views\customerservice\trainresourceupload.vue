<template>
  <div class="ces-main">
    <span>

 <el-row>
        <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
          <el-upload
            ref="upload"
            class="upload-demo"
            :auto-upload="true"
            :multiple="true"
            :limit="2"
            action
            accept=".rar,.zip,.ppt,.xls,.xlsx,.pptx,.doc,.docx,.png,.jpg"
            :http-request="upfile"
            :on-change="uploadchange"
            :file-list="fileList"
            :data="fileparm"
          >
            <template #trigger>
              <el-button size="small" type="primary"
                >添加培训资料文件</el-button
              >
            </template>
            <!-- <el-button
                style="margin-left: 10px"
                size="small"
                type="success"
                @click="submitUpload"
                >上传</el-button> -->
          </el-upload>
        </el-col>
      </el-row>


 <el-table :data="data">
      <el-table-column type="index" width="60"></el-table-column>
       
      <el-table-column
        prop="fileName"
        sortable
        label="资料名称"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="createdTime"
        sortable
        label="上传时间"
        width="220"
      ></el-table-column
    >
    <el-table-column prop="xiaz" label="下载资料" min-width="120" align="center" >
      <template  #default="{ row }" >
         <a target="_blank" :href="row.filePath">下载</a>
         <el-button style="margin-left:20px" @click="deleteresource(row)">删除</el-button>
          
　　　</template> 

 
</el-table-column>
     
    
    </el-table>
    <div class="block">
      <el-pagination
        layout="prev, pager, next"
        @current-change="changehisPage"
        :total="pager.total"
        :page-size="pager.pageSize"
      >
      </el-pagination>
    </div>

















     
    </span>
  </div>
</template>
 
        
    
<script>
import { upLoadFile, upLoadImage } from "@/api/upload/file";
import {
  trainfilesUpload,
  getTrainResourceDataList,
  disableTrainResourceData
} from "@/api/customerservice/trainplan";
export default {
  data() {
    return {
      that: this,

      loading: false,
      productid: "",
      total: 0,
      pageSize: 5,
      pager: {
        total: 0,
        pageIndex: 1,
        pageSize: 5,
        OrderBy: "traindate",
        isAsc: false,
        Trainstatus: 2,
      },
      data: [],
      fileList: [],
      fileparm: {},
    };
  },
  components: {},
  computed: {},
  methods: {
    changehisPage(e){
      var that=this;
  const params = {
        ...this.pager,
      };
      params.productid = that.productid;



          that.data = [];
       
       getTrainResourceDataList(params).then(res=>{
        
        
        that.data = res.data.list;
        that.pager.total=res.data.total;
        that.total=res.data.total;



       });


    },
    deleteresource(e){
      console.log(e)
      var that=this;
      disableTrainResourceData({id:e.id}).then(res=>{
        
        that.init(e.productID)



      });

    },
    uploadchange(e) {
      

      console.log("uploadchange");
    },

    submitUpload() {
      console.log("upload");
    },
    async upfile(parms) {
      var item;
      var that=this;

console.log("upload finish");


      const form = new FormData();
      form.append("file", parms.file);
      const res = await upLoadFile(form);
      if (res.code == 1)
        item = { uid: parms.file.uid, url: res.data, name: parms.file.name,productid:this.productid };
      else throw new Error(res.msg);


      trainfilesUpload(item).then((res) => {


        that.fileList=[];
        that.init(that.productid);

      this.$message({
          message: "上传成功,正在导入中...",
          type: "success",
        });
      }
      );


        //var fileinfo={filename:parms.file.name,filepath:res.data};


        



      return item;
    },

    // uploadFile(item) {
    //   console.log("startupload");

    //   var that = this;
    //   const form = new FormData();
    //   form.append("token", this.token);
    //   form.append("upfile", item.file);

    //   console.log(form);

    //   const res = trainfilesUpload(form).then((res) => {
    //     console.log(res);

    //     that.dialogVisible = false;
    //     that.fileList = [];

    //     this.$message({
    //       message: "上传成功,正在导入中...",
    //       type: "success",
    //     });
    //   });
    // },
    uploadchange() {
      // this.$refs.upload.clearFiles();
    },
    changePage(e) {
      var that = this;
      that.data = [];

      const params = {
        ...this.pager,
      };
      params.productid = that.productid;
      this.pager.pageIndex = e;
    },
    init(_productid) {
      var that = this;
      this.productid=_productid;
      this.pager.pageIndex = 0;
      that.data = [];
      // var ProductID=this.$router.query.

      const params = {
        ...this.pager,
      };
      params.productid = _productid;



          that.data = [];
       
       getTrainResourceDataList(params).then(res=>{
        
        
        that.data = res.data.list;
        that.pager.total=res.data.total;
        that.total=res.data.total;



       });





    },
  },
  mounted() {
    //this.init();
  },
};
</script>