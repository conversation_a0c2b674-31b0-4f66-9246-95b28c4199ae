<template>
    <MyContainer>
        <template #header>
            <div class="top">

                <el-select v-model="ListInfo.DepotName" placeholder="仓库名称" style="width:120px" class="el-select-content"
                    clearable filterable @change="changePlatform">
                    <el-option v-for="item in DepotNameList" :key="item.sort" :label="item.codeName"
                        :value="item.code" />
                </el-select>

                <el-select v-model="ListInfo.DepotCode" placeholder="仓库名称" style="width:120px; margin-left: 10px;"
                    class="el-select-content" clearable filterable @focus="changPlatformState">
                    <el-option v-for="item in DepotCodeList" :key="item.key" :label="item.value" :value="item.key" />
                </el-select>

                <el-select v-model="ListInfo.calculateAvailable" placeholder="仓库试算是否启用" clearable filterable
                    class="publicCss" style="margin-left: 10px;">
                    <el-option :key="true" label="是" :value="true" />
                    <el-option :key="false" label="否" :value="false" />
                </el-select>

                <el-select v-model="ListInfo.labelAvailable" placeholder="面单渠道是否启用" clearable filterable
                    class="publicCss" style="margin-left: 5px;">
                    <el-option :key="true" label="是" :value="true" />
                    <el-option :key="false" label="否" :value="false" />
                </el-select>

                <el-button type="primary" @click="getList('search')" style="margin-left: 10px;">搜索</el-button>
                <el-button type="primary" @click="onExport" style="margin-left: 10px;"
                    v-if="checkPermission('LogisticsChannel_Managemen_export')">导出</el-button>
            </div>
        </template>
        <vxetablebase :id="'derelict202408041402'" ref="table" :that="this" :isIndex="true" :hasexpand="true"
            :tablefixed="true" @sortchange="sortchange" :tableData="tableData" :tableCols="tableCols"
            :isSelection="false" :isSelectColumn="false" style="width: 100%; margin: 0;" :loading="loading"
            :height="'100%'">
            <template #temuCode="{ row, column, store }">
                <el-select v-model="row.temuCode" multiple collapse-tags placeholder="请选择" style="width: 150px;">
                    <el-option v-for="(option, index) in temuCodeOptions" :key="index" :label="option" :value="option">
                    </el-option>
                </el-select>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange"
                @get-page="getList" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getShippingMethodPageList, updateShippingMethodAvailable, exportShippingMethodPageList } from '@/api/kj/logistics'
import { getThirdPlatform } from '@/api/kj/system'
import { getWarehouseCodeBySupplier } from '@/api/kj/warehouse'
const tableCols = [
    {
        istrue: true, sortable: 'custom', prop: 'platform', label: '仓库名称', formatter: (row) => {
            return row.platform + "(" + row.houseName + ")";
        },
    },
    { istrue: true, sortable: 'custom', prop: 'code', label: '仓库物流编码', },
    { istrue: true, sortable: 'custom', prop: 'name', label: '仓库物流名称', },
    { istrue: true, sortable: 'custom', prop: 'houseCode', label: '仓库编码', },
    {
        istrue: true, sortable: 'custom', prop: 'calculate_available', label: '仓库试算是否启用', type: 'switch', change: (row, that) => that.changeStatus(row, 1), align: 'center'
    },
    { istrue: true, sortable: 'custom', prop: 'temuCode', label: 'TEMU平台编码', slot: 'temuCode' },
    { istrue: true, sortable: 'custom', prop: 'label_available', label: '面单渠道是否启用', type: 'switch', change: (row, that) => that.changeStatus(row, 2), align: 'center' },
];
export default {
    name: "derelict",
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            that: this,
            isType: 'USPS',
            ListInfo: {
                page: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                // DepotName: null,
                // DepotCode: null,
                thirdPlatform: [],
                warehouseCode: [],
                labelAvailable: false,
                calculateAvailable: false

            },
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            DepotNameList: [],
            DepotCodeList: [],
            temuCodeOptions: ['UPS', 'USPS', 'FedEx'], // 固定的下拉选项
        }
    },
    async mounted() {
        await this.getDropdownList()
        await this.getList()

    },
    methods: {
        // check(v){
        //     console.log(v,this.isType,'4399')
        // },
        async getDropdownList() {
            const thirdPlatform = await getThirdPlatform();
            this.DepotNameList = thirdPlatform.data;
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.page = 1
                this.$refs.pager.setPage(1)
            }

            this.loading = true
            if (this.ListInfo.DepotName) {
                this.ListInfo.thirdPlatform = [this.ListInfo.DepotName];
            } else {
                this.ListInfo.thirdPlatform = []
            }
            if (this.ListInfo.DepotCode) {
                this.ListInfo.warehouseCode = [this.ListInfo.DepotCode];
            } else {
                this.ListInfo.warehouseCode = []
            }
            const res = await getShippingMethodPageList(this.ListInfo);
            this.loading = false;
            this.total = res.total

            this.tableData = res.data;
            // this.tableData.forEach(item => {
            //     item.temuCode = item.temuCode.join(';')
            // })
            this.tableData.forEach(row => {
                row.temuCode = row.temuCode ? row.temuCode.split(';') : [];
            });

            console.log(this.tableData)
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.page = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.page = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },

        async changePlatform(val) {
            this.DepotCodeList = []
            if (val) {
                const WarehouseCode = await getWarehouseCodeBySupplier({ supplier: val });
                this.DepotCodeList = WarehouseCode.data;
            }
        },
        changPlatformState(val) {
            if (!this.ListInfo.DepotName) {
                this.$message({ message: "请先选择仓库！", type: "warning" });
                return false;
            }
        },
        async changeStatus(row, num) {
            // 根据 num 判断是哪个按钮的更改操作
            if (num === 2 && (!row.temuCode || row.temuCode.length === 0)) {
                this.$message({ message: "请填写TEMU平台编码", type: "warning" });
                await this.getList()
                return;
            }

            this.$confirm('将改变按钮的监控状态，是否继续?', '提示', {
                confirmButtonText: '是', cancelButtonText: '否', type: 'warning'
            }).then(async () => {
                var params = [{
                    id: row.id,
                    labelAvailable: row.label_available,
                    calculateAvailable: row.calculate_available,
                    temuCode: row.temuCode.join(';')
                }];

                const res = await updateShippingMethodAvailable(params);
                if (res.isSuccess) {
                    this.$message({ message: "操作成功", type: "success" });
                    await this.getList()
                }

            }).catch(() => {
                row.isUpdate = !row.isUpdate;
                this.getList()
            });
        },
        async onExport() {//导出列表数据；
            if (this.ListInfo.DepotName) {
                this.ListInfo.thirdPlatform = [this.ListInfo.DepotName];
            } else {
                this.ListInfo.thirdPlatform = []
            }
            if (this.ListInfo.DepotCode) {
                this.ListInfo.warehouseCode = [this.ListInfo.DepotCode];
            } else {
                this.ListInfo.warehouseCode = []
            }
            var res = await exportShippingMethodPageList(this.ListInfo);
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '跨境物流渠道管理导出_' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
            this.listLoading = false;
        },

    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 150px;
        margin-right: 5px;
    }
}

.el-select {
    width: 100%;
}
</style>