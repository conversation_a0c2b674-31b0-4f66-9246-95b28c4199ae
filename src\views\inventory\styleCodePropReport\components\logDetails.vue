<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select v-model="ListInfo.platforms" placeholder="平台" class="publicCss" clearable
                    :collapse-tags="true" filterable multiple v-if="ListInfo.popupType == 3">
                    <el-option v-for="item in platformlist" :label="item.label" :value="item.value" :key="item.value" />
                </el-select>
                <el-select filterable v-model="ListInfo.goodsCode" collapse-tags clearable placeholder="商品编码"
                  class="publicCss" multiple v-if="ListInfo.popupType == 2">
                  <el-option v-for="item in goodsCodeList" :key="item" :label="item" :value="item" />
                </el-select>
                <el-select filterable v-model="ListInfo.groupIdList" collapse-tags clearable placeholder="运营组"
                    class="publicCss" multiple v-if="ListInfo.popupType == 2">
                    <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-button type="primary" @click="getList('search')"
                    v-if="ListInfo.popupType == 3 || ListInfo.popupType == 2">搜索</el-button>
            </div>
        </template>
        <vxetablebase :id="'styleCodePropReport_logDetails202408041611'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :showsummary="true"
            :summaryarry="summary" @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :toolbarshow="false" :isSelection="false" :isSelectColumn="false"
            style="width: 100%;height:400px;  margin: 0" v-loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { platformlist } from '@/utils/tools'
import dayjs from 'dayjs'
import { pageContinuLossesGoodsCodeReport, getGoodsCodeByStyleCode } from '@/api/operatemanage/continuLosses'
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'styleCode', label: '系列编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'groupId', label: '运营组', formatter: (row) => row.groupName },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'platform', label: '平台', formatter: (row) => row.platformStr },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'bzCategory1', label: '一级类目', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'bzCategory2', label: '二级类目', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'day30OrderCount', label: '近30天订单数', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'day30SalesAmount', label: '近30天销售额', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'day30Profit3', label: '毛三利润', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'day30Profit3Rate', label: '毛三率', formatter: (row) => row.day30Profit3Rate ? row.day30Profit3Rate + '%' : null },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase
    },
    props: {
        queryType: {
          type: Number,
          default: 0
        },
        logDetailsQuery: {
            type: Object,
            default: () => {
                return {
                    popupType: null,
                    styleCode: null,
                    groupId: null,
                    platform: null,
                    bzCategory1: null,
                    bzCategory2: null
                }
            }
        },
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                popupType: this.logDetailsQuery.popupType,
                styleCode: this.logDetailsQuery.styleCode,
                groupId: this.logDetailsQuery.groupId,
                platform: this.logDetailsQuery.platform,
                bzCategory1: this.logDetailsQuery.bzCategory1,
                bzCategory2: this.logDetailsQuery.bzCategory2,
                goodsCode: [],
                platforms: [this.logDetailsQuery.platform],
                groupIdList: [],
            },
            platformlist,
            timeRanges: [],
            goodsCodeList: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            isExport: false,
            summary: {},
            grouplist: []
        }
    },
    async mounted() {
      await this.init()
      const params = { styleCode: this.logDetailsQuery.styleCode }
      const { data,success } = await getGoodsCodeByStyleCode(params)
      if(success) {
        this.goodsCodeList = data
        this.ListInfo.goodsCode = this.goodsCodeList
      }
        const columns = {
            groupId: 'groupId',//运营组
            platform: 'platform',//平台
            groupName: 'groupName',//运营组名
            bzCategory1: 'bzCategory1',//一级类目
            bzCategory2: 'bzCategory2',//二级类目
            styleCode: 'styleCode',//系列编码
            goodsCode: 'goodsCode',//商品编码
        };
        const hideColumns = {
            1: ['groupId', 'platform', 'bzCategory1', 'bzCategory2'],
            2: ['platform', 'bzCategory1', 'bzCategory2'],
            3: ['groupId', 'bzCategory1', 'bzCategory2'],
            4: ['styleCode', 'groupName', 'groupId', 'platform', 'bzCategory2', 'groupName','goodsCode'],
            5: ['styleCode', 'groupName', 'groupId', 'platform', 'groupName','goodsCode'],
        };
        this.$nextTick(() => {
            hideColumns[this.logDetailsQuery.popupType].forEach(field => {
                this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField(columns[field]));
            });
        })
        await this.getList()
    },
    methods: {
        async init() {
            const { data } = await getDirectorGroupList();
            this.grouplist = data?.map(item => { return { value: item.key, label: item.value }; });
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.ListInfo.platform = this.ListInfo.platforms ? this.ListInfo.platforms.join(',') : ''
            let goodsCodes = this.ListInfo.goodsCode?.length ? this.ListInfo.goodsCode.join(',') : null
            this.loading = true
            if (this.ListInfo.popupType == 2) {
                this.ListInfo.groupId = (this.ListInfo.groupIdList > 0) ? this.ListInfo.groupIdList.join(',') : null
            }
            // 使用时将下面的方法替换成自己的接口
            try {
                const params = {...this.ListInfo,goodsCode:goodsCodes}
                const { data: { list, total, summary }, success } = await pageContinuLossesGoodsCodeReport(params)
                if (success) {
                    this.tableData = list
                    this.total = total
                    this.loading = false
                    this.summary = summary
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
::v-deep .el-select__tags-text {
  max-width: 70px;
}
</style>
