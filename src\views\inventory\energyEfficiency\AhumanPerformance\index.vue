<template>
    <MyContainer>
        <template #header>
        <div class="top">
          <!-- <el-date-picker v-model="ListInfo.calculateMonth" unlink-panels range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期"  type="month" style="width: 250px;margin-right: 5px;" :clearable="false"
            :value-format="'yyyy-MM'" >
          </el-date-picker> -->
          <dateRange style="width: 250px;" :startDate.sync="ListInfo.startQueryTime" :endDate.sync="ListInfo.endQueryTime" class="publicCss" />

          <!-- <el-select v-model="ListInfo.isStock" placeholder="业务部门" class="publicCss" clearable>
            <el-option :key="'是'" label="是" :value="0" />
            <el-option :key="'否'" label="否" :value="1" />
          </el-select> -->
          <el-select filterable v-model="ListInfo.platformArr" placeholder="请选择平台" multiple collapse-tags
              @change="onchangeplatform" clearable style="width: 250px">
              <el-option v-for="(item,i) in options.platformlist" :key="i"  :label="item" :value="item" />
            </el-select>

          <el-button type="primary" @click="getList('search')">查询</el-button>
          <!-- <el-button type="primary" @click="startImport">导入</el-button>
          <el-button type="primary" @click="downExcel">模板下载</el-button> -->
          <el-button type="primary" @click="exportProps('search')">导出</el-button>
          <!-- <el-button type="primary" @click="saveBane('search')">存档</el-button>
          <div style="color: red; margin-left: 5px;">
            存档时间：{{timeCundang??'-'}}
          </div> -->
          <div style="color: red; margin-left: 5px;font-size: 14px;display: flex;align-items: center;">
            数据来源:各平台运营人员业绩统计
            </div>

        </div>
      </template>
      <vxe-table
        border
        ref="newtable"
        show-footer
        :loading="loading"
        height="100%"
        :row-config="{height: '30px'}"
        :merge-footer-items="mergeFooterItems"
        :footer-data="footerData"
        :span-method="mergeRowMethod"
        :row-class-name="rowClassName"
        :cell-class-name="cellClassName"
        :footer-cell-class-name="footerCellClassName"
        :footer-row-class-name="fafooterRowClassName"
        :expand-config="{visibleMethod: visibleMethod, trigger: 'manual',accordion: true, padding: true}"
        @cell-click="cellClick"
        :data="tableData">
        <vxe-column type="seq" title="#" width="50"></vxe-column>

        <vxe-column field="platform" title="平台" width="100"></vxe-column>
        <!-- field="platform" -->
        <vxe-column type="expand"   width="0">
            <template #content="{ row }">
            <div style="box-sizing: border-box;">
                <vxe-table
                show-footer
                show-header-overflow="tooltip"
                :row-config="{height: '30px'}"
                :column-config="{resizable: true}"
                :cell-class-name="cellClassName"
                :row-class-name="rowClassName"
                :data="extData.list" height="450px"
                :merge-footer-items="mergeFooterItems"
                :footer-data="extData.summary"
                :span-method="mergeRowMethodnei"
                :footer-row-class-name="footerRowClassName">
                    <vxe-column type="seq" width="70" title="#"></vxe-column>
                    <vxe-column field="platform" title="平台"></vxe-column>
                    <vxe-column field="regionName" title="片区"></vxe-column>
                    <vxe-column field="employeeStatusStr" title="员工结构"></vxe-column>
                    <vxe-column field="userCount" title="人数">
                        <template #default="{ row }">
                        <span @click="handleEdit(row, '人数', 2)"  :style="row.regionName&&row.regionName.indexOf('小计')>-1?{}:{color: '#409EFF', cursor: 'pointer'}">{{ tonumfuc(row.userCount, '人数') }}</span>
                        </template>
                    </vxe-column>
                    <vxe-column field="overtimeDurationMonth" title="月度加班时长">
                        <template #default="{ row }">
                        <span @click="handleEdit(row, '月度加班时长', 2)" :style="row.regionName&&row.regionName.indexOf('小计')>-1?{}:{color: '#409EFF', cursor: 'pointer'}">{{tonumfuc(row.overtimeDurationMonth, '月度加班时长')}}</span>
                        </template>
                    </vxe-column>
                    <vxe-column field="overtimeDurationDay" title="日加班时长">
                        <template #default="{ row }">
                        <span @click="handleEdit(row, '日加班时长', 2)" :style="row.regionName&&row.regionName.indexOf('小计')>-1?{}:{color: '#409EFF', cursor: 'pointer'}">{{tonumfuc(row.overtimeDurationDay, '日加班时长')}}</span>
                        </template>
                    </vxe-column>

                    <vxe-column field="perSaleAmount" title="人均销售金额">
                        <template #default="{ row }">
                        <span @click="handleEdit(row, '人均销售金额', 2)" :style="row.regionName&&row.regionName.indexOf('小计')>-1?{}:{color: '#409EFF', cursor: 'pointer'}">{{tonumfuc(row.perSaleAmount, '人均销售金额')}}</span>
                        </template>
                    </vxe-column>
                    <vxe-column field="avgSaleAmount" title="人均销售金额（去掉最高及最低）">
                        <template #default="{ row }">
                        <span @click="handleEdit(row, '人均销售金额（去掉最高及最低）', 2)" :style="row.regionName&&row.regionName.indexOf('小计')>-1?{}:{color: '#409EFF', cursor: 'pointer'}">{{tonumfuc(row.avgSaleAmount, '人均销售金额（去掉最高及最低）')}}</span>
                        </template>
                    </vxe-column>
                    <vxe-column field="perProfit33" title="人均毛四利润">
                        <template #default="{ row }">
                        <span @click="handleEdit(row, '人均毛四利润', 2)" :style="row.regionName&&row.regionName.indexOf('小计')>-1?{}:{color: '#409EFF', cursor: 'pointer'}">{{tonumfuc(row.perProfit33, '人均毛四利润')}}</span>
                        </template>
                    </vxe-column>
                    <vxe-column field="avgProfit33" title="人均毛四利润（去掉最高及最低）">
                        <template #default="{ row }">
                        <span @click="handleEdit(row, '人均毛四利润（去掉最高及最低）', 2)" :style="row.regionName&&row.regionName.indexOf('小计')>-1?{}:{color: '#409EFF', cursor: 'pointer'}">{{tonumfuc(row.avgProfit33, '人均毛四利润（去掉最高及最低）')}}</span>
                        </template>
                    </vxe-column>
                    <vxe-column field="perProfit33Rate" title="人均毛四利润率">
                        <template #default="{ row }">
                        <span @click="handleEdit(row, '人均毛四利润率', 2)" :style="row.regionName&&row.regionName.indexOf('小计')>-1?{}:{color: '#409EFF', cursor: 'pointer'}">{{row.perProfit33Rate?row.perProfit33Rate+'%':''}}</span>
                        </template>
                    </vxe-column>
                    <vxe-column field="avgProfit33Rate" title="人均毛四利润率（去掉最高及最低）">
                        <template #default="{ row }">
                        <span @click="handleEdit(row, '人均毛四利润率（去掉最高及最低）', 2)" :style="row.regionName&&row.regionName.indexOf('小计')>-1?{}:{color: '#409EFF', cursor: 'pointer'}">{{tonumfuc(row.avgProfit33Rate, '人均毛四利润率（去掉最高及最低）') + '%'}}</span>
                        </template>
                    </vxe-column>
                </vxe-table>
            </div>
            </template>
        </vxe-column>
        <!-- field="platform"  -->
        <vxe-column title="" width="30">
            <template #default="{ row, $index }">
                <i class="el-icon-arrow-right" v-show="!row.isopen" @click="openSumDialog(row, $index)"></i>
                <i class="el-icon-arrow-down" v-show="row.isopen" @click="openSumDialog(row, $index)"></i>
            </template>
        </vxe-column>

        <vxe-column field="regionName" title="片区" width="60"></vxe-column>


        <vxe-column field="saleAmount" title="销售金额" >
            <template #default="{ row }">
            <span @click="handleEdit(row, '销售金额', 1)" :style="row.regionName&&row.regionName.indexOf('小计')>-1?{}:{color: '#409EFF', cursor: 'pointer'}">{{tonumfuc(row.saleAmount, '销售金额')}}</span>
            </template>
        </vxe-column>
        <vxe-column field="orderCount" title="订单量" >
            <template #default="{ row }">
            <span @click="handleEdit(row, '订单量', 1)" :style="row.regionName&&row.regionName.indexOf('小计')>-1?{}:{color: '#409EFF', cursor: 'pointer'}">{{tonumfuc(row.orderCount, '订单量')}}</span>
            </template>
        </vxe-column>
        <vxe-column field="profit33" title="毛四利润" footer-align="left">
            <template #default="{ row }">
            <span @click="handleEdit(row, '毛四利润', 1)" :style="row.regionName&&row.regionName.indexOf('小计')>-1?{}:{color: '#409EFF', cursor: 'pointer'}">{{tonumfuc(row.profit33, '毛四利润')}}</span>
            </template>
        </vxe-column>
        <vxe-column field="profit33Rate" title="毛四利润率" footer-align="left">
            <template #default="{ row }">
            <span @click="handleEdit(row, '毛四利润率', 1)" :style="row.regionName&&row.regionName.indexOf('小计')>-1?{}:{color: '#409EFF', cursor: 'pointer'}">{{row.profit33Rate?row.profit33Rate+'%':''}}</span>
            </template>
        </vxe-column>

        <vxe-column field="userCount" title="有效人数" footer-align="left" width="90">
            <template #default="{ row }">
            <span @click="handleEdit(row, '有效人数', 1)" :style="row.regionName&&row.regionName.indexOf('小计')>-1?{}:{color: '#409EFF', cursor: 'pointer'}">{{tonumfuc(row.userCount, '有效人数')}}</span>
            </template>
        </vxe-column>
        <vxe-column field="perSaleAmount" title="人均销售金额" footer-align="left">
            <template #default="{ row }">
            <span @click="handleEdit(row, '人均销售金额', 1)" :style="row.regionName&&row.regionName.indexOf('小计')>-1?{}:{color: '#409EFF', cursor: 'pointer'}">{{tonumfuc(row.perSaleAmount, '人均销售金额')}}</span>
            </template>
        </vxe-column>
        <vxe-column field="perProfit33" title="人均毛四利润" footer-align="left">
            <template #default="{ row }">
            <span @click="handleEdit(row, '人均毛四利润', 1)" :style="row.regionName&&row.regionName.indexOf('小计')>-1?{}:{color: '#409EFF', cursor: 'pointer'}">{{tonumfuc(row.perProfit33, '人均毛四利润')}}</span>
            </template>
        </vxe-column>
        <vxe-column field="perProportion" title="销售额及毛四占比均值" footer-align="left" width="160">
            <template #default="{ row }">
            <span @click="handleEdit(row, '销售额及毛四占比均值', 1)" style="color: #409EFF">{{row.perProportion?row.perProportion+"%":''}}</span>
            </template>
        </vxe-column>

        <vxe-column field="userCountProportion" title="人数占比" footer-align="left">
            <template #default="{ row }">
            <span @click="handleEdit(row, '人数占比', 1)" :style="row.regionName&&row.regionName.indexOf('小计')>-1?{}:{color: '#409EFF', cursor: 'pointer'}">{{row.userCountProportion?row.userCountProportion+'%':''}}</span>
            </template>
        </vxe-column>
        <vxe-column field="projectPreparation" title="预计编制" footer-align="left">
            <template #default="{ row }">
            <span @click="handleEdit(row, '预计编制', 1)" :style="row.regionName&&row.regionName.indexOf('小计')>-1?{}:{color: '#409EFF', cursor: 'pointer'}">{{tonumfuc(row.projectPreparation, '预计编制')}}</span>
            </template>
        </vxe-column>

        <!-- <vxe-column title="操作" footer-align="left">
            <template slot-scope="scope">
              <el-button type="text" size="mini" :disabled="scope.row.status==1" v-if="scope.row.deptName.indexOf('小计')==-1" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
              <el-button type="text" style="color: red" size="mini" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
            </template>
        </vxe-column> -->
      </vxe-table>
      <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template>

      <el-dialog title="对比" :visible.sync="dialogVisibleEdit" width="85%" height="90%" v-dialogDrag>
        <departmentEdit ref="departmentEdit" v-if="dialogVisibleEdit" :editInfo="editInfo" @search="closeGetlist" :options="options"
            @cancellationMethod="cancellationMethod" :ListInfo="ListInfo" />
      </el-dialog>

      <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
        <span>
            <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
            accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
            <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
            </el-upload>
        </span>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
        </el-dialog>
    </MyContainer>
  </template>

  <script>
  import MyContainer from "@/components/my-container";
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
  import { tonumfuc } from '@/utils/tonumqian.js'
//   import { pickerOptions } from '@/utils/tools';
  import departmentEdit from "./departmentEdit.vue";
  import { downloadLink, pickerOptions, platformlist } from "@/utils/tools.js";
  import dateRange from "@/components/date-range/index.vue";
  import dayjs from 'dayjs'
  import {  getOperateHumanEffectivenessList, sscDataArchive, sscDataImport, getOperateHumanEffectivenessReportExport, getOperateListValue } from '@/api/people/peoplessc.js';
  const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '审批状态', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNoInner', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'importTime', label: '商品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'planSendTime', label: '原价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'timePay', label: '现价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight', label: '进货数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderStatus', label: '涨价原因', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'seriesName', label: '添加日期', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCount', label: '涨价日期', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '添加人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '岗位', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '分公司', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人组长', },
  ]
  export default {
    name: "scanCodePage",
    components: {
      MyContainer, vxetablebase, departmentEdit, dateRange
    },
    data() {
        const tableData = [
    //   { id: 10001,regionName: '义务', deptName: 'it部门', nickname: 'T1', role: 'Develop', regularCount: 'Man', probationCount: 28, address: 'test abc' },
    //   { id: 10002,regionName: '义务', deptName: '人事部', nickname: 'T2', role: 'Test', regularCount: 'Women', probationCount: 22, address: 'Guangzhou' },
    //   { id: 10002,regionName: '义务', deptName: '小计', nickname: 'T2', role: 'Test', regularCount: 'Women', probationCount: 22, address: 'Guangzhou' },
    //   { id: 10003,regionName: '南昌', deptName: '运营部', nickname: 'T3', role: 'PM', regularCount: 'Man', probationCount: 32, address: 'Shanghai' },
    //   { id: 10004,regionName: '南昌', deptName: 'it部门', nickname: 'T4', role: 'Designer', regularCount: 'Women', probationCount: 23, address: 'test abc' },
    //   { id: 10002,regionName: '南昌', deptName: '小计', nickname: 'T2', role: 'Test', regularCount: 'Women', probationCount: 22, address: 'Guangzhou' },
    //   { id: 10005,regionName: '北京', deptName: '人事部', nickname: 'T5', role: 'Develop', regularCount: 'Women', probationCount: 30, address: 'Shanghai' },
    //   { id: 10006,regionName: '北京', deptName: '运营部', nickname: 'T6', role: 'Designer', regularCount: 'Women', probationCount: 21, address: 'test abc' },
    //   { id: 10002,regionName: '北京', deptName: '小计', nickname: 'T2', role: 'Test', regularCount: 'Women', probationCount: 22, address: 'Guangzhou' },
    //   { id: 10007,regionName: '深圳', deptName: 'it部门', nickname: 'T7', role: 'Test', regularCount: 'Man', probationCount: 29, address: 'test abc' },
    //   { id: 10008,regionName: '深圳', deptName: '人事部', nickname: 'T8', role: 'Develop', regularCount: 'Man', probationCount: 35, address: 'test abc' },
    //   { id: 10002,regionName: '深圳', deptName: '小计', nickname: 'T2', role: 'Test', regularCount: 'Women', probationCount: 22, address: 'Guangzhou' },
    ]
    const footerData = [
    //   {regionName: '办公室合计', deptName: '办公室合计', role: '33', rate: '56' },
    //   {regionName: '仓储合计', deptName: '仓储合计', role: 'bb', rate: '56' },
    //   {regionName: '全区域合计', deptName: '全区域合计', role: 'bb', rate: '1235' }
    ]
    const mergeFooterItems = [
    //   { row: 0, col: 0, rowspan: 1, colspan: 2 },
    //   { row: 1, col: 0, rowspan: 1, colspan: 2 },
    //   { row: 2, col: 0, rowspan: 1, colspan: 2 }
    ]
//     const lastMonth = dayjs().subtract(1, 'month');
//   this.ListInfo.startTime = lastMonth.format('YYYY-MM');
      return {
        downloadLink,
        tonumfuc,
        options: {
            platformlist: [
              '淘系', '拼多多', '抖音', '京东'
            ],
            regionNamelist: [],
            grouplist: []
        },
        dialogVisible: false,
        fileList: [],
        districtList: [],
        extData: {
            list: []
        },
        extDataAll: {},
        timeCundang: '',
        tableData,
        footerData,
        mergeFooterItems,
        somerow: 'platform',
        somerownei: 'platform',
        dialogVisibleEdit: false,
        that: this,
        editInfo: {},
        ListInfo: {
            // calculateMonth: dayjs().subtract(1, 'month').format('YYYY-MM')
            startQueryTime: dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
            endQueryTime: dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD'),
        //   currentPage: 1,
        //   pageSize: 50,
        //   orderBy: null,
        //   isAsc: false,
        //   startTime: null,//开始时间
        //   endTime: null,//结束时间
        },
        timeRanges: [],
        tableCols,
        summaryarry: {},
        total: 0,
        loading: false,
        pickerOptions,
      }
    },
    async mounted() {
      var res3 = await getOperateListValue('fullName');
      this.options.regionNamelist = res3.data;

      await this.getList()

    },
    methods: {
        openSumDialog(row, indexx){

            this.tableData.forEach((item, index)=>{
                console.log(index, this.tableData.indexOf(row))

                if(this.tableData.indexOf(row) != index){
                    item.isopen = false;
                }
            })


            this.$nextTick(()=>{
                row.isopen = !row.isopen

                if(row.isopen){
                    this.somerow = '';
                }else{
                    this.somerow = 'platform';
                }

                Object.keys(this.extDataAll).map((item)=>{
                    if(item == row.platform){
                        this.extData = this.extDataAll[item]
                    }
                })


                const $table = this.$refs.newtable
                if ($table) {
                    $table.toggleRowExpand(row)
                    $table.refreshData()
                }

            })

            


        },
        cellClick(event){
            // event.row.isopen = !event.row.isopen
            console.log("点击触发", event)
        },
        visibleMethod ({ row }) {
            // if ([10002, 10003].includes(row.id)) {
            // return true
            // }
            return false
        },
        //上传文件
        onUploadRemove(file, fileList) {
        this.fileList = []
        },
        async onUploadChange(file, fileList) {
        this.fileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
        fileList.splice(fileList.indexOf(file), 1);
        this.fileList = [];
        this.dialogVisible = false;
        },
        async onUploadFile(item) {
        if (!item || !item.file || !item.file.size) {
            this.$message({ message: "请先上传文件", type: "warning" });
            return false;
        }
        this.uploadLoading = true
        const form = new FormData();
        form.append("file", item.file);
        var res = await sscDataImport(form);
        if (res?.success){
            this.$message({ message: res.msg, type: "success" });

        }
        this.uploadLoading = false
        this.dialogVisible = false;
        await this.getList()
        },
        onSubmitUpload() {
        if (this.fileList.length == 0) {
            this.$message({ message: "请先上传文件", type: "warning" });
            return false;
        }
        this.$refs.upload.submit();
        },
        //导入弹窗
        startImport() {
        this.fileList = []
        this.dialogVisible = true;
        },
        downExcel(){
            downloadLink('https://nanc.yunhanmy.com:10010/media/video/20241218/1869309885535715329.xlsx', 'ssc数据导入模板.xlsx');
        },
        async saveBane(){
              this.$confirm('是否存档？存档后不可修改！', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const { data, success } = await sscDataArchive(this.ListInfo)
                    if(!success){
                        return;
                    }
                    this.getList();
                    this.$message.success('保存存档成功！')

                }).catch(() => {
                    // this.$message.error('取消')
                });


        },
        closeGetlist(){
            this.dialogVisibleEdit = false;
            this.getList()
        },
        handleEdit(row, name, moudletype){
            if(row.regionName&&row.regionName.indexOf('小计')>-1){
                return;
            }

            this.editInfo = row;
            if(moudletype == 1){
                this.editInfo.options = [
                    '销售金额',
                    '订单量',
                    '毛四利润',
                    '毛四利润率',
                    '有效人数',
                    '人均销售金额',
                    '人均毛四利润',
                    '销售额及毛四占比均值',
                    '人数占比',
                    '预计编制',
                ]
            }else{
                this.editInfo.options = [
                    '人数',
                    '月度加班时长',
                    '日加班时长',
                    '人均销售金额',
                    '人均销售金额（去掉最高及最低）',
                    '人均毛四利润',
                    '人均毛四利润（去掉最高及最低）',
                    '人均毛四利润率',
                    '人均毛四利润率（去掉最高及最低）'
                ]
            }

            this.editInfo.checkName = name;
            this.editInfo.moudletype = moudletype;
            this.dialogVisibleEdit = true;

        },
        exportExcel(){
            this.$refs.newtable.exportData({filename:'ssc数据',    sheetName: 'Sheet1',type: 'xlsx' })
        },
        footerCellClassName(event){

            if(event.row.regionName == '办公室合计'&&event.column.title=='区域'||event.row.regionName == '办公室合计'&&event.column.title=='部门'
            || event.row.regionName == '仓储合计'&&event.column.title=='区域'||event.row.regionName == '仓储合计'&&event.column.title=='部门'){
                return 'row-green'
            }
            return null
        },
        footerRowClassName(event){
            // if(event.row.deptName == '全区域合计'){
            //     return 'row-bagreen'
            // }
            // return null

            return 'row-bagreen'
        },
        fafooterRowClassName(event){
            // if(event.row.deptName == '全区域合计'){
            //     return 'row-bagreen'
            // }
            // return null

            return 'row-bagray'
        },
        rowClassName (event) {
            // if(event.row.regionName&&event.row.regionName.indexOf('小计')>-1){
            //     return 'row-green'
            // }
            return null
        },
        cellClassName ({ row, column }) {
            if(row.regionName&&row.regionName.indexOf('小计')>-1){
                return 'row-green'
            }
            return null
        },
        // 通用行合并函数（将相同多列数据合并为一行）
        mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
            const fields = this.somerow.split(',')
            const cellValue = row[column.property]
            if (cellValue && fields.includes(column.property)) {
            const prevRow = visibleData[_rowIndex - 1]
            let nextRow = visibleData[_rowIndex + 1]
            if (prevRow && prevRow[column.property] === cellValue) {
                return { rowspan: 0, colspan: 0 }
            } else {
                let countRowspan = 1
                while (nextRow && nextRow[column.property] === cellValue) {
                nextRow = visibleData[++countRowspan + _rowIndex]
                }
                if (countRowspan > 1) {
                return { rowspan: countRowspan, colspan: 1 }
                }
            }
            }
        },
        // 通用行合并函数（将相同多列数据合并为一行）
        mergeRowMethodnei ({ row, _rowIndex, column, visibleData }) {
            const fields = this.somerownei.split(',')
            const cellValue = row[column.property]
            if (cellValue && fields.includes(column.property)) {
            const prevRow = visibleData[_rowIndex - 1]
            let nextRow = visibleData[_rowIndex + 1]
            if (prevRow && prevRow[column.property] === cellValue) {
                return { rowspan: 0, colspan: 0 }
            } else {
                let countRowspan = 1
                while (nextRow && nextRow[column.property] === cellValue) {
                nextRow = visibleData[++countRowspan + _rowIndex]
                }
                if (countRowspan > 1) {
                return { rowspan: countRowspan, colspan: 1 }
                }
            }
            }
        },
      async changeTime(e) {
        this.ListInfo.startTime = e ? e[0] : null
        this.ListInfo.endTime = e ? e[1] : null
      },
      //导出数据,使用时将下面的方法替换成自己的接口
      async exportProps() {
      this.isExport = true
      await getOperateHumanEffectivenessReportExport(this.ListInfo).then((data ) => {
        if (data) {
          const aLink = document.createElement("a");
          let blob = new Blob([data], { type: "application/vnd.ms-excel" })
          aLink.href = URL.createObjectURL(blob)
          aLink.setAttribute('download', '人效数据分析' + new Date().toLocaleString() + '.xlsx')
          aLink.click()
          this.isExport = false
        }
      }).catch(() => {
        this.isExport = false
      })
    },
      async getList(type) {
        // if (type == 'search') {
        //   this.ListInfo.currentPage = 1
        //   this.$refs.pager.setPage(1)
        // }
        // if (this.timeRanges && this.timeRanges.length == 0) {
        //   //默认给近7天时间
        //   this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        //   this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        //   this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
        // }
        this.loading = true
        this.ListInfo.platform = this.ListInfo.platformArr.join(',');
        const { data, success } = await  getOperateHumanEffectivenessList(this.ListInfo)
        if (success) {
            console.log(data)
            data.list.map((item)=>{
                item.isopen = false;
            })
          this.tableData = data.list
          this.total = data.total
          let newarr = [];
          data.summary.forEach((itemm)=>{
            itemm.userCountProportion = itemm.userCountProportion ?itemm.userCountProportion + '%': ''
            itemm.perProportion = itemm.perProportion ?itemm.perProportion + '%': ''
            itemm.profit33Rate = itemm.profit33Rate ?itemm.profit33Rate + '%': ''


            let processedSummaryarry = {};
            Object.keys(itemm).forEach(key => {
            processedSummaryarry[key] = tonumfuc(itemm[key]);
            });
            newarr.push(processedSummaryarry);
          })
          this.footerData = newarr;

        //   let  newprocessedSummaryarry = {};
          Object.keys(data.extData).forEach(key => {
            // data.extData[key][] = tonumfuc(itemm[key]);


            if(!!data.extData[key]?.summary){

                data.extData[key]?.summary.map((itemmm)=>{

                    Object.keys(itemmm).forEach(keyy => {
                        if(keyy == 'perProfit33Rate'){
                            itemmm[keyy] = itemmm[keyy] ? itemmm[keyy]+'%' : '';
                        }else{
                            itemmm[keyy] = tonumfuc(itemmm[keyy]);
                        }
                    });

                })

            }
          });
          this.extDataAll = data.extData;
        //   this.timeCundang = data.list.length>0?data.list[0].archiveTime:''

           //取列表中的区域
        // const newDistricts = this.tableData.map(item => item.deptName).filter(district => district !== undefined && district !== null && district != '小计')
        // this.districtList = Array.from(new Set([...this.districtList, ...newDistricts]));

        //   this.total = data.total

        //   this.summaryarry = data.summary
          this.loading = false
        } else {
            this.loading = false
          this.$message.error('获取列表失败')
        }
      },
      //每页数量改变
      Sizechange(val) {
        this.ListInfo.currentPage = 1;
        this.ListInfo.pageSize = val;
        this.getList()
      },
      //当前页改变
      Pagechange(val) {
        this.ListInfo.currentPage = val;
        this.getList()
      },
      sortchange({ order, prop }) {
        if (prop) {
          this.ListInfo.orderBy = prop
          this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
    }
  }
  </script>

  <style scoped lang="scss">
  .top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
      width: 150px;
      margin-right: 5px;
    }
  }

  ::v-deep .row-green {
    background-color: rgba(172, 221, 214, 0.5);
    // color: #fff;
  }
  ::v-deep .row-bagreen {
    background-color: rgba(161, 241, 159, 0.5);
    // color: #fff;
  }
  ::v-deep .row-bagray {
    background-color: rgba(253, 245, 232, 0.5);
    // color: #fff;
  }
  ::v-deep(.mytable-style.vxe-table .vxe-header--column.col-blue) {
    background-color: #2db7f5;
    color: #fff;
  }
  ::v-deep(.mytable-style.vxe-table .vxe-body--column.col-red) {
    background-color: red;
    color: #fff;
  }

  ::v-deep .vxe-table--expanded .vxe-table--expand-btn{
    color: red;
    // 添加伪元素
    &::after {
        content: '';
        display: block;
        width: 30px;
        height: 30px;
        background-color: rgba($color: #000000, $alpha: 0.3);
        position: absolute;
        top: 2px;
        right: 2px;
    }

  }


    ::v-deep .vxe-table--body tbody tr.vxe-body--row {
    height: 35px !important;
    }

    ::v-deep .vxe-table--body tbody tr.vxe-body--row:nth-child(1n) .vxe-body--column {

        padding: 5px 0px !important;
        .vxe-cell--label {
            overflow: hidden;
            white-space: nowrap;
            wrap: nowrap;
        }
    }

    ::v-deep .vxe-table--footer tfoot tr.vxe-footer--row:nth-child(1n) {
    height: 35px !important;
    }
    ::v-deep .vxe-table--footer tfoot tr.vxe-footer--row:nth-child(1n) .vxe-footer--column {
    padding: 5px 0px !important;
    .vxe-cell--label {
        overflow: hidden;
        white-space: nowrap;
        wrap: nowrap;
    }
    }

    ::v-deep .vxe-table--header thead tr.vxe-header--row:nth-child(1n) {
    height: 40px !important;
    }
    ::v-deep .vxe-table--header thead tr.vxe-header--row:nth-child(1n) .vxe-header--column {
    padding: 5px 0px !important;
    .vxe-cell--label {
        overflow: hidden;
        white-space: nowrap;
        wrap: nowrap;
    }
    }
  </style>
