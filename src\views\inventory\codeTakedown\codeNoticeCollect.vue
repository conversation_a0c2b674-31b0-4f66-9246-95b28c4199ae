<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
            <div class="top" style="display: flex; flex-wrap: wrap; ">
                <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="修改开始日期"
                    end-placeholder="修改结束日期" :picker-options="pickerOptions" style="width: 250px;"
                    :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker>
                <div class="publicCss">
                    <inputYunhan ref="goodsCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode" width="150px"
                        placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100" :maxlength="2000"
                        @callback="callbackGoodsCode" title="商品编码">
                    </inputYunhan>
                </div>
                <div class="publicCss">
                    <inputYunhan ref="combineCode" :inputt.sync="ListInfo.combineCode" v-model="ListInfo.combineCode" width="150px"
                        placeholder="组合装编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100" :maxlength="2000"
                        @callback="callbackCombineCode" title="组合装编码">
                    </inputYunhan>
                </div>
                <el-select v-model="ListInfo.brandIds" placeholder="品牌" filterable multiple collapse-tags class="publicCss" clearable style="width: 160px;">
                    <el-option v-for="item in brandList" :key="item.id" :label="item.brandName" :value="item.id" />
                </el-select>
                <div class="publicCss">
                    <inputYunhan ref="proCode" :inputt.sync="ListInfo.proCode" v-model="ListInfo.proCode" width="150px"
                        placeholder="ID/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100" :maxlength="2000"
                        @callback="callbackID" title="ID">
                    </inputYunhan>
                </div>
                <el-input v-model.trim="ListInfo.sellStock" placeholder="可用库存数" maxlength="7" clearable class="publicCss" style="width: 95px;"
                    oninput="value=value.replace(/[^\d]/g,''); if(value.replace('-', '').length > 7) value = value.slice(0, 7)" />
                <el-select v-model="ListInfo.enabled" placeholder="是否上架" class="publicCss" clearable style="width: 95px;">
                    <el-option :key="'是'" label="是" :value="true" />
                    <el-option :key="'否'" label="否" :value="false" />
                </el-select>
                <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable style="width: 95px;">
                    <el-option :key="'待通知'" label="待通知" :value="'待通知'" />
                    <el-option :key="'已通知'" label="已通知" :value="'已通知'" />
                    <el-option :key="'已完成'" label="已完成" :value="'已完成'" />
                </el-select>
                <el-select v-model="ListInfo.groupId" placeholder="运营组" filterable class="publicCss" clearable>
                    <el-option v-for="item in groupList" :key="item.groupId" :label="item.userName" :value="item.id" />
                </el-select>
                <el-select v-model="ListInfo.directorId" placeholder="运营专员" filterable class="publicCss" clearable>
                    <el-option v-for="item in directorList" :key="item.id" :label="item.userName" :value="item.id" />
                </el-select>
                <el-select v-model="ListInfo.assistantId" placeholder="运营助理" filterable class="publicCss" clearable>
                    <el-option v-for="item in assistantList" :key="item.id" :label="item.userName" :value="item.id" />
                </el-select>
                <el-input v-model.trim="ListInfo.notifierName" placeholder="通知人" maxlength="50" clearable class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="exportProps">导出</el-button>
                <el-button type="primary" @click="editBatchEnabled" v-if="checkPermission('viewGoodsBanNoticeStatusSet')">操作</el-button>
            </div>
        </template>
        <vxetablebase :id="'codeDelistDetail20241029'" ref="table" :that='that' :isIndex='true' :hasexpand='true' @select='selectchange'
            :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0"
            :loading="listLoading" :height="'100%'" :border="true">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
        <el-dialog title="操作ID" :visible.sync="editBatchEnabledVisiable" width="35%" :close-on-click-modal="false" v-dialogDrag>
            <span>
                <el-form>
                    <el-form-item label="是否上架">
                        <el-select v-model="enabled" placeholder="是否上架" class="publicCss" clearable>
                            <el-option :key="'是'" label="是" :value="true" />
                            <el-option :key="'否'" label="否" :value="false" />
                        </el-select>
                    </el-form-item>
                </el-form>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="editBatchEnabledVisiable = false">关闭</el-button>
                <el-button @click="editGoodsBanNoticeStatus">确定</el-button>
            </span>
        </el-dialog>
    </MyContainer>
</template>
<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, formatLinkProCode } from '@/utils/tools'
import dayjs from 'dayjs';
import inputYunhan from "@/components/Comm/inputYunhan";
import { 
    getGoodsBanNoticePage, 
    batchEditGoodsBanNoticeStatus, 
    exportGoodsBanNotice,
    getGoodsBanNoticePageQueryCondition
} from "@/api/inventory/basicgoods"

const tableCols = [
    { width: '60', type: 'checkbox' },
    { width: 'auto', align: 'center', prop: 'picture', label: '图片', type: "images", },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'platformStr', label: '平台', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'combineCode', label: '组合装编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'styleCode', label: '系列编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'brandName', label: '品牌', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'proCode', label: '宝贝ID', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode), },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'sellStock', label: '可用库存数', color: (row) => row.isRed ? "red" : "" },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'groupName', label: '运营组', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'directorName', label: '运营专员', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'assistantName', label: '运营助理', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'enabledStr', label: '是否上架', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'status', label: '状态', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'notifierName', label: '通知人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'noticeTime', label: '通知时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'noticeDuration', label: '通知时长(天)', color: (row) => row.noticeDurationMarker ? "red" : "" },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'syncTime', label: '修改时间', },
];

export default{
    name: "codeNoticeCollect",
    components: {
        MyContainer, vxetablebase, inputYunhan
    },
    data(){
        return {
            tableCols: tableCols,
            pickerOptions,
            timeRanges: [],
            ListInfo:{
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: null,//开始时间
                endDate: null,//结束时间
                goodsCode: null,
                combineCode: null, //组合装编码
                brandId: null,
                brandIds: [],
                proCode: null,
                sellStock: undefined,
                enabled: null,
                status: null,
                groupId: null,
                directorId: null,
                assistantId: null,
                notifierName: null
            },
            brandList: [],
            groupList: [],
            directorList: [],
            assistantList: [],
            tableData: [],
            summaryarry: {},
            total: 0,
            listLoading: false,
            pageLoading: false,
            selids: [],
            editBatchEnabledVisiable: false,
            enabled: false,
        }
    },
    async mounted() {
        //await this.getList('')
        await this.init()
    },
    methods: {
        async init() {
            //默认给近7天时间
            this.ListInfo.startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
            this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
            this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]

            var res = await getGoodsBanNoticePageQueryCondition();
            if(res?.success){
                this.brandList = res.data.brandList;
                this.groupList = res.data.groupList;
                this.directorList = res.data.directorList;
                this.assistantList = res.data.assistantList;
            }

            this.getList('search');
        },
        Jumpinit(row) {
            let that = this
            that.ListInfo.startDate = row.startDate;
            that.ListInfo.endDate = row.endDate;
            that.ListInfo.goodsCode = row.goodsCode;

            that.ListInfo.brandId= null;
            that.ListInfo.brandIds= [];
            that.ListInfo.proCode= null;
            that.ListInfo.sellStock= undefined;
            that.ListInfo.enabled= null;
            that.ListInfo.status= null;
            that.ListInfo.groupId= null;
            that.ListInfo.directorId= null;
            that.ListInfo.assistantId= null;
            that.ListInfo.notifierName= null;
            if (row.startDate && row.endDate) {
                that.timeRanges = [row.startDate, row.endDate];
            } else {
                that.timeRanges = [];
                that.timeCheck = true;
            }
            that.getList('search')
        },
        getColor(row){
            if(row.isRed)
                return "red";
        },
        callbackCombineCode(v){
            this.ListInfo.combineCode = v;
        },
        callbackGoodsCode(val) {
            this.ListInfo.goodsCode = val;
        },
        callbackID(val) {
            this.ListInfo.proCode = val;
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            if (this.timeRanges && this.timeRanges.length == 0 && !this.timeCheck) {
                //默认给近7天时间
                this.ListInfo.startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
                this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
            }
            this.listLoading = true
            var res = await getGoodsBanNoticePage(this.ListInfo);
            if(res?.success){
                this.tableData = res.data.list;
                this.total = res.data.total;
                //this.summaryarry = res.summary;
            }
            this.listLoading = false;
            this.selids = [];
        },
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList('')
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList('')
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList('')
            }
        },
        async changeTime(e) {
            this.ListInfo.startDate = e ? e[0] : null
            this.ListInfo.endDate = e ? e[1] : null
        },
        async exportProps() {
            if (this.timeRanges && this.timeRanges.length == 0 && !this.timeCheck) {
                //默认给近7天时间
                this.ListInfo.startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
                this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
            }

            this.pageLoading = true;
            var res = await exportGoodsBanNotice(this.ListInfo);
            this.pageLoading = false;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => { this.selids.push(f.id); });
        },
        editBatchEnabled() {
            if (this.selids.length == 0) {
                this.$message({ message: "至少选择一行！", type: "warning", });
                return
            }
            this.enabled = false;
            this.editBatchEnabledVisiable = true;
        },
        async editGoodsBanNoticeStatus(){
            var param = {
                ids: this.selids.join(','),
                enabled: this.enabled
            };
            this.listLoading = true;
            var res = await batchEditGoodsBanNoticeStatus(param);
            if(res?.success){
                this.$message({message:"修改成功！", type: "success"});
                this.getList('');
                this.editBatchEnabledVisiable = false;
            }
            this.listLoading = false;
        }
    },
};

</script>
<style scoped lang="scss">
//控制选择器多选-标签宽度
::v-deep .el-select__tags-text {
  max-width: 30px;
}
.top {
    display: flex;
    margin-bottom: 10px;
}
.publicCss {
    width: 150px;
    margin-right: 5px;
}

::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}
</style>