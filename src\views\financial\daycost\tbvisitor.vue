<template>
  <container>
    <ces-table ref="table"  :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
      :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true'
      :summaryarry='summaryarry' :loading="listLoading">
      <template slot='extentbtn'>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;"><el-input style="width: 120px" v-model.trim="filter1.proCode" maxlength="50"  clearable placeholder="商品ID"/></el-button>
          <el-button style="padding: 0;margin: 0;"><el-input style="width: 80px" v-model.trim="filter1.importor" maxlength="10"  clearable placeholder="导入人"/></el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-date-picker style="width: 210px" v-model="filter1.timeimportrange" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
              start-placeholder="开始导入时间" end-placeholder="结束导入时间" :clearable="true" class="date_picker"></el-date-picker>
          </el-button>
          <el-button type="primary" @click="onSearch">刷新</el-button>
        </el-button-group>
      </template>
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>
  </container>
</template>
<script>
import { pageDayTBVistorData } from '@/api/financial/daycost'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue"
import {formatTime} from "@/utils/tools";
const tableCols = [
  { istrue: true, prop: 'yearMonthDay', label: '付款日期', width: '100', sortable: 'custom',formatter:(row)=>formatTime(row.yearMonthDay,'YYYY-MM-DD')},
  { istrue: true, prop: 'proCode', label: '商品ID', width: '120', sortable: 'custom' },
  { istrue: true, prop:'capital', label:'本金', width:'100',sortable:'custom'},
  { istrue: true, prop: 'amont', label: '佣金', width: '100', sortable: 'custom', },
  { istrue: true,prop:'createdUserId',label:'导入人', width:'100',sortable:'custom',formatter:(row)=>row.createdUserName},
  { istrue: true,prop:'createdTime',label:'导入时间', width:'auto',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD HH:mm:ss')}
];
const tableHandles = [
  { label: "导入", handle: (that) => that.onimport() },
  { label: "下载导入模板", handle: (that) => that.ondownloadmb('淘宝客数据导入模板') },
];
export default {
  name: 'YunHantbvisitor',
  components: { cesTable, container },
  props: {
    filter: {}
  },
  data() {
    return {
      filter1: {
          proCode:null,
          importor:null,
          startImportTime:null,
          endImportTime:null,
          timeimportrange:null,
      },
      shareFeeType: 8, 
      that:this,
      list: [],
      tableCols: tableCols,
      tableHandles: tableHandles,
      pager: { OrderBy: "id", IsAsc: false },
      summaryarry: {},
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false
    }
  },
  mounted() {

  },
  methods: {
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      if (this.filter1.timeimportrange) {
          this.filter1.startImportTime = this.filter1.timeimportrange[0];
          this.filter1.endImportTime = this.filter1.timeimportrange[1];
      }
      let pager = this.$refs.pager.getPager()
      const params = {...pager, ...this.pager, ... this.filter, ... this.filter1}
      this.listLoading = true
      const res = await pageDayTBVistorData(params)
      this.listLoading = false
      if (!res?.success) return
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry = res.data.summary; 
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    async onbatchDelete() {
      await this.$emit('ondeleteByBatch', this.shareFeeType);
    },
    async oncomput() {
      this.$emit('onstartcomput', this.shareFeeType);
    },
    async onimport() {
      await this.$emit('onstartImport', this.shareFeeType);
    },
    async ondownloadmb(name) {
      await this.$emit('ondownloadmb', name);
    },
  }
}
</script>
  