<template>
  <my-container v-loading="pageLoading">
    <template #header>
     <div>
       <el-button-group>

          <el-button style="padding: 0;margin: 0;">
                <el-date-picker style="width: 249px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                :clearable="true"
                value-format="yyyy-MM-dd" range-separator="至" start-placeholder="登记开始时间" end-placeholder="登记结束时间"></el-date-picker>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="filter.CreatedUser"  maxlength="30" clearable  placeholder="登记人" style="width:130px;"/>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
              <inputYunhan  ref="OnlineOrderNumber" :inputt.sync="filter.OnlineOrderNumber" v-model="filter.OnlineOrderNumber" placeholder="线上单号" :clearable="true" @callback="callbackOnlineOrderNumber" title="线上单号" width="130px"></inputYunhan>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
              
              <inputYunhan  ref="InternalOrderNumber" :inputt.sync="filter.InternalOrderNumber" v-model="filter.InternalOrderNumber" placeholder="内部单号" :clearable="true" @callback="callbackInternalOrderNumber" title="内部单号" width="130px"></inputYunhan>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="filter.ReceivingInformation"  maxlength="30" clearable  placeholder="收货信息" style="width:130px;"/>
          </el-button>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="ImportOrderTemplate">下载导入模板</el-button>
        <el-button type="primary" @click="ImportOrder">导入订单</el-button>
        <el-button type="primary" @click="ImportreceivingInformation">导入收货信息</el-button>
        <el-button type="primary" @click="ExportUnreceivedOrders">导出无收货信息订单</el-button>
        <el-button type="primary" @click="ExportReceivedInformationSelectedRow">导出选中行收货信息</el-button>
       </el-button-group>
     </div>
    </template>
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'   :isSelection='true'
          :showsummary='true'  :summaryarry='summaryarry' :tableData='financialreportlist'
          :tableCols='tableCols'  :tableHandles='tableHandles'  :loading="listLoading" style="height: 740px; "@select="selectchange">
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
    </template>


    <el-dialog title="导入订单信息" :visible.sync="dialogVisibleSyj" width="30%" v-dialogDrag>
      <span>
          <el-upload ref="upload2" class="upload-demo"
                  :auto-upload="false"
                  :multiple="false"
                  :limit="1"
                  action
                  accept=".xlsx"
                  :http-request="uploadFile2"
                  :on-change="uploadChange" :on-remove="uploadRemove">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <el-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">上传</el-button>
          </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyj = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>
import {getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
import {getDirectorGroupList,getDirectorList,getList as getshopList} from '@/api/operatemanage/base/shop'
import {formatPlatform,formatTime,formatYesornoBool,formatLinkProCode,platformlist} from "@/utils/tools";
import {getReceivingInformationQuery,orderPartExport,importOrderPart,exportReceivedInformationSelectedRow} from '@/api/operatemanage/base/product'
import {getAllProBrand} from '@/api/inventory/warehouse'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import InputMult from "@/components/Comm/InputMult";
import { Loading } from 'element-ui';
import { ruleDirectorGroup } from '@/utils/formruletools'
import importmodule from '@/components/Bus/importmodule'
import { getListByStyleCode } from "@/api/inventory/basicgoods"
import StyleCodeDetail from '@/views/bookkeeper/reportday/StyleCodeDetail'
import inputYunhan from "@/components/Comm/inputYunhan";
let loading;
const startLoading = () => {
  loading = Loading.service({
  lock: true,
  text: '加载中……',
  background: 'rgba(0, 0, 0, 0.7)'
  });
};
const tableCols =[
         {istrue:true ,prop:'onlineOrderNumber',label:'线上单号',sortable:'custom', width:'100'},
        {istrue:true ,prop:'internalOrderNumber',label:'内部单号',sortable:'custom', width:'100',type:'orderLogInfo',orderType:'orderNoInner' },
        {istrue:true ,prop:'createdUserName',label:'登记人',sortable:'custom', width:'100'},
         {istrue:true ,prop:'createdTime',fix:true,label:'登记日期', width:'150',sortable:'custom'},
         {istrue:true ,prop:'receivingInformationStatus',fix:true,label:'同步状态', width:'100',sortable:'custom',formatter:(row)=> row.receivingInformationStatus==1?"已同步":"未同步"},
        {istrue:true ,prop:'receivingInformation',label:'收货信息',sortable:'custom', width:'850',formatter:(row)=> row.receivingInformation},
        {istrue:true ,prop:'receiptInformationTime',label:'导入收货信息时间',sortable:'custom', width:'200',formatter:(row)=> row.receiptInformationTime=='1970-01-01 00:00:00'?"":row.receiptInformationTime=='0001-01-01 00:00:00'?"":row.receiptInformationTime},
  ];
const tableHandles=[
      ];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable,InputMult,importmodule,inputYunhan,StyleCodeDetail},
  data() {
    return {
      that:this,
      filter: {
        startTime: null,
        endTime: null,
        timerange:null,
        OnlineOrderNumber:null,
        InternalOrderNumber:null,
        ReceivingInformation :null,
        CreatedUser:null
      },
      dialogVisibleSyj:false,
      onimportfilter:{
        yearmonthday:null,
      },
      editVisible:false,
      styleCode:null,
      profit3UnZero:null,
      options:[],
      platformlist:platformlist,
      shopList:[],
      userList:[],
      brandlist:[],
      grouplist:[],
      directorlist:[],
      financialreportlist: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      total: 0,
      pager:{OrderBy:"",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      earchloading:false,
      pageLoading: false,
      summaryarry:{},
      selids:[],
      fileList:[],
      dialogVisibleData:false,
      dialogVisible:false,
      uploadLoading:false,
      importFilte:{},
      fileList:[],
      fileparm:{},
      editparmVisible:false,
      editLoading:false,
      editparmLoading:false,
      drawervisible:false,
      searchloading:false,
      /* dialogDrVisibleShengYi:false, */
      dialogDrVisible:false,
      expressfreightanalysisVisible:false,
      drparamProCode:'',
        StyleCodeDetail:{
          visible:false,
          filter:{
          StyleCode:''
          }
        },
      giftDetail:{visible:false},
      costDialog:{visible:false,rows:[]},
      buscharDialog:{visible:false,title:"",data:[]},
      drawervisible:false,
    };
  },
  async mounted() {
  },
  async created() {
    await this.getShopList();
  },
  methods: {
    async callbackOnlineOrderNumber(val) {
        this.filter.OnlineOrderNumber = val;
    },
    async callbackInternalOrderNumber(val) {
        this.filter.InternalOrderNumber = val;
    },
    ImportOrderTemplate(){
      window.open("/static/excel/operateManage/导入模板.xlsx", "_blank");
    },
    ImportreceivingInformation(){
      this.dialogVisibleSyj = true
    },
    ImportOrder(){
        this.dialogVisibleSyj = true
      },
      async onSubmitupload2() {
        if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
        this.$refs.upload2.submit()
      },
      async uploadFile2(item) {
        const form = new FormData();
        form.append("upfile", item.file);
        const res = importOrderPart(form);
        this.$message({message: '上传成功,正在导入中...', type: "success"});
        this.fileList = []
        this.$refs.upload2.clearFiles();
        this.dialogVisibleSyj = false;
      },
      async uploadChange (file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for (var i = 0; i < fileList.length; i++) {
          if (fileList[i].status == "success") list.push(fileList[i]);
          else list.push(fileList[i].raw);
        }
        this.fileList = list;
      } else {
        this.fileList = [];
      }
    },
    uploadRemove (file, fileList) {
      this.uploadChange(file, fileList);
    },
    async ExportReceivedInformationSelectedRow(){
      var params = { selids: this.selids };
         var  res =await exportReceivedInformationSelectedRow(params);
         const aLink = document.createElement("a");
                    let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '无收货信息订单_' + new Date().toLocaleString() + '.xlsx')
                    aLink.click();
    },
    async ExportUnreceivedOrders(){
                    const params = {
                    };
                    var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
                    var res = await orderPartExport(params);
                    console.log("导出的数据...........",res.data)
                    loadingInstance.close();
                    if (!res?.data) {
                        this.$message({ message: "没有数据", type: "warning" });
                        return
                    }
                    const aLink = document.createElement("a");
                    let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '无收货信息订单_' + new Date().toLocaleString() + '.xlsx')
                    aLink.click();
    },
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
     await this.onSearch();
    },
    onRefresh(){
      this.onSearch()
    },
    async onSearch(){
      this.$refs.pager.setPage(1);
      await this.getList().then(res=>{  });
    },
    async getList(){
      this.filter.startTime =null;
      this.filter.endTime =null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      var that=this;
      var pager = this.$refs.pager.getPager();
      const params = {...pager,...this.pager,...this.filter};
      startLoading();
      const res = await getReceivingInformationQuery(params).then(res=>{
          loading.close();
          that.total = res.data?.total;
          if(res?.data?.list&&res?.data?.list.length>0){
          }
          that.financialreportlist = res.data?.list;
          that.summaryarry=res.data?.summary;
      });
    },

    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
   onRefresh(){
      this.onSearch()
    },
},
};

</script>
<style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  ::v-deep .el-table td.el-table__cell div{
    color: gray;
  }
</style>

