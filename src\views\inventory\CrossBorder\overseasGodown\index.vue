<template>
  <MyContainer>
    <el-tabs v-model="activeName" style="height: 95%;">
      <el-tab-pane label="海外仓同步库存" name="first" style="height: 100%;" lazy>
        <managingOverseasInventory />
      </el-tab-pane>
      <el-tab-pane label="历史存档的库存" name="second" style="height: 100%;" lazy>
        <managingOverseasInventoryCopy />
      </el-tab-pane>
    </el-tabs>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import managingOverseasInventory from './tab/managingOverseasInventory.vue';
import managingOverseasInventoryCopy from './tab/managingOverseasInventoryCopy.vue';

export default {
  components: {
    MyContainer, managingOverseasInventory, managingOverseasInventoryCopy
  },
  data() {
    return {
      activeName: 'first'
    };
  },
  methods: {

  }
};
</script>

<style lang="scss" scoped></style>