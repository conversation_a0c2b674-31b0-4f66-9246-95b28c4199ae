<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model.trim="Filter.supplierName" type="text" maxlength="50" clearable placeholder="供应商" style="width:130px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model.trim="Filter.spGoodsName" type="text" maxlength="50" clearable placeholder="商品" style="width:130px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model.trim="Filter.spSkuName" type="text" maxlength="50" clearable placeholder="SKU" style="width:130px;" />
                    </el-button>
                    <el-button type="primary" @click="onSearch" icon="el-icon-search">查询</el-button>
                    <el-button @click="()=>{Filter={};}"  icon="el-icon-close">清空查询条件</el-button>   
                    <el-button type="primary" @click="tagClear" icon="el-icon-clear">清空已选择</el-button>
                                    
                </el-button-group>
            </el-form>
        </template>
        <el-row>
            <el-col :span="24">
                <el-tag v-for="tag in tags" :key="tag.id" closable  @close="tagClose(tag)">
                   {{tag.supplierName}}-{{tag.spGoodsName}}-{{tag.spSkuName}}
                </el-tag>
            </el-col>
        </el-row>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='tbdatalist' @select='selectchange' 
        :isSelection='true' :isSelectColumn="false" :tableCols='tableCols' :loading="listLoading" >          
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettbdatalist1" />
            <el-row>
                <el-col :span="24" style="text-align:center">
                    <el-button @click="()=>{that.$emit('close');}">取消</el-button>
                    <el-button type="primary" @click="()=>{ 
                        if(that.tags.length==0) { 
                            that.$message.warning('请确认已选择数据！');
                            return;
                        }   
                        that.$emit('selected',[...that.tags]);
                        that.$emit('close');
                        }">确定</el-button>
                </el-col>
            </el-row>
            
        </template>
    </my-container>
</template>
<script>  

    import {
        PageSupplierSkusAsync
    } from '@/api/order/alllinkDaiPai'

    import cesTable from "@/components/Table/table.vue";
    import { formatTime } from "@/utils";
    import { formatmoney, formatPercen, getUrlParam,  setStore, getStore,formatLink } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";

    const tableCols = [        
        { istrue: true, prop: 'supplierName', label: '厂商', width: '120', sortable: 'custom' },
        { istrue: true, prop: 'spGoodsName', label: '厂商商品', width: '160', sortable: 'custom' },
        { istrue: true, prop: 'spSkuName', label: '厂商商品SKU', minwidth: '160', sortable: 'custom' },
        { istrue: true, prop: 'price', label: '价格', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'spSkuAttr1', label: 'SKU属性1', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'spSkuAttr2', label: 'SKU属性2', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'spSkuAttr3', label: 'SKU属性3', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'spSkuAttr4', label: 'SKU属性4', width: '100', sortable: 'custom' },
    ];

    export default {
        name: "SupplierSkuList",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
        data() {
            return {
                that: this,
                Filter: {
                    supplierName: "",
                    spGoodsName: "",
                    spSkuName:""
                },
                tbdatalist: [],
                tableCols: tableCols,
                total: 0,
                pager: { OrderBy: "", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                //
                selids: [],
                tags: [                    
                ]
            };
        },
        async mounted() {
            this.onSearch();
            
        },
        methods: {  
            tagClear(){
                this.tags=[];
                this.tbdatalist.forEach(x=>{
                    this.$refs.table.toggleRowSelection(x,false);
                });
            },
            tagClose(tag){
                let idx=this.tags.findIndex(x=> x.id==tag.id );
                if(idx>-1){
                    this.tags.splice(idx,1);
                    this.$refs.table.toggleRowSelection(tag,false);
                }                    
            },
            selectchange: function (rows, row) {     
                let self=this;  
                self.tags=[];
                self.tags=[...rows]   ;                
            },              
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderField = column.prop;

                    if(orderField=="supplierName"){
                        orderField="c.Name"
                    }

                    this.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                }
                this.onSearch();
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.gettbdatalist1();
                this.tagClear();
            },
            async gettbdatalist1() {              
                const para = { ...this.Filter };
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...para,
                };

                this.listLoading = true;
                const res = await PageSupplierSkusAsync(params);

                this.listLoading = false;

                this.total = res.data.total
                this.tbdatalist = res.data.list;

            },           
        },
    };
</script>
