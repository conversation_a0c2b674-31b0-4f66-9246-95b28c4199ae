<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="添加开始日期" end-placeholder="添加结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable>
          <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <div class="publicCss" style="width: 170px;">
          <inputYunhan ref="refexpressNos" :inputt.sync="ListInfo.expressNos" v-model="ListInfo.expressNos"
            width="170px" placeholder="物流单号/Enter多行输入" :clearable="true" :clearabletext="true" :maxRows="50000"
            :maxlength="1000000" :valuedOpen="true" @callback="callbackMethod($event, 'expressNos')" title="物流单号">
          </inputYunhan>
        </div>
        <div class="publicCss" style="width: 170px;">
          <inputYunhan ref="refexpressCompanyNames" :inputt.sync="ListInfo.expressCompanyNames"
            v-model="ListInfo.expressCompanyNames" width="170px" placeholder="物流公司/Enter多行输入" :clearable="true"
            :clearabletext="true" :maxRows="100" :maxlength="1000000" :valuedOpen="true"
            @callback="callbackMethod($event, 'expressCompanyNames')" title="物流公司">
          </inputYunhan>
        </div>
        <div class="publicCss" style="width: 170px;">
          <inputYunhan ref="refcreatedUserNames" :inputt.sync="ListInfo.createdUserNames"
            v-model="ListInfo.createdUserNames" width="170px" placeholder="添加人/Enter多行输入" :clearable="true"
            :clearabletext="true" :maxRows="100" :maxlength="3000" :valuedOpen="true"
            @callback="callbackMethod($event, 'createdUserNames')" title="添加人">
          </inputYunhan>
        </div>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" @click="onExport">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'customerServiceImportData202505271427'" :tablekey="'customerServiceImportData202505271427'"
      ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading" :height="'100%'"
      :border='true'>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div slot="title" class="header-title">
        <span class="title-text"><span>导入数据</span></span>
        <span class="title-close"><el-button @click="downLoadFile">下载模版</el-button></span>
      </div>
      <div>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions, downloadLink } from '@/utils/tools'
import inputYunhan from "@/components/Comm/inputYunhan";
import dayjs from 'dayjs'
import { getExpressClaimCusImportRecordListAsync, importExpressClaimCusImportRecordAsync, exportExpressClaimCusImportRecordListAsync } from '@/api/customerservice/expressClaimOrder'
const statusList = [
  { label: '进行中', value: '进行中' },
  { label: '归档', value: '归档' },
  { label: '待匹配', value: '待匹配' },
  { label: '匹配失败', value: '匹配失败' },
]
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'status', label: '状态', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'expressNo', label: '物流单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'expressCompany', label: '物流公司', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'claimReason', label: '理赔原因', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'innerOrderNo', label: '内部订单号', headerBgColor: '#bbbbbb', type: 'orderLogInfo', orderType: 'orderNoInner', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdUserName', label: '添加人', headerBgColor: '#bbbbbb', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '添加日期', headerBgColor: '#bbbbbb', },
]
export default {
  name: "customerServiceImportData",
  components: {
    MyContainer, vxetablebase, inputYunhan
  },
  data() {
    return {
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {
        upfile: null
      },
      statusList,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'createdTime',
        isAsc: false,
        startDate: null,//开始时间
        endDate: null,//结束时间
        expressNos: '',//物流单号
        expressCompanyNames: '',//物流公司
        createdUserNames: '',//添加人
        status: '',//状态
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    callbackMethod(val, type) {
      const map = {
        createdUserNames: () => (this.ListInfo.createdUserNames = val),
        expressNos: () => (this.ListInfo.expressNos = val),
        expressCompanyNames: () => (this.ListInfo.expressCompanyNames = val),
      };
      map[type]?.();
    },
    downLoadFile() {
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250527/1927190019781869569.xlsx', '快递理赔-客服导入数据导入模版.xlsx');
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importExpressClaimCusImportRecordAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async changeTime(e) {
      this.ListInfo.startDate = e ? e[0] : null
      this.ListInfo.endDate = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近一个月时间
        this.ListInfo.startDate = dayjs().subtract(30, 'day').format('YYYY-MM-DD')
        this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
      }
      this.loading = true
      const { data, success } = await getExpressClaimCusImportRecordListAsync(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    // 导出
    async onExport() {
      const res = await exportExpressClaimCusImportRecordListAsync(this.ListInfo);
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute("download", '客服导入数据_' + new Date().toLocaleString() + '.xlsx');
      aLink.click();
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

::v-deep .el-select__tags-text {
  max-width: 80px;
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px 0 0;

  .title-text {
    display: flex;
    align-items: center;

    .title-close {
      margin-left: 10px;
    }
  }
}
</style>
