<template>
    <my-container v-loading="pageLoading" >
      <div style="height: 40px; ">
        <el-button type="primary" @click="clickRefresh">刷新</el-button>
        <el-button type="primary" @click="addNew">新增</el-button>
      </div>
      <div style="height: 100%;">
        <vxe-table ref="xTable" border="default" height="100%" :show-footer="true" style="width: 100%;"
        class="vxetable202212161323 mytable-scrollbar20221212" resizable stripe :id="id" show-overflow
        :show-footer-overflow="'tooltip'" keep-source size="mini" :loading="tableloading" :data="tableData"
        :scroll-y="{ gt: 100 }" :scroll-x="{ gt: 100 }"  :header-row-class-name="'height cellheight1'"
        :expand-config="{ accordion: true}"
        :row-class-name="rowStyleFun"
        :row-config="{ isCurrent: true, isHover: true }">


          <vxe-column title="编号" field="employeeId" width="200" align="left" fixed='left'>
          </vxe-column>

          <vxe-column field="employeeName" title="员工姓名" width="300">
          </vxe-column>
          <vxe-column field="companyName" title="公司" width="300">
          </vxe-column>
          <vxe-column field="employeeWorkPrice" title="工价比例" width="300">
            <template #default="{ row }">
              {{ (row.eWorkPrice * 1).toFixed(2) + '%' }}
            </template>
          </vxe-column>
          <vxe-column field="isAddAccounting" title="加入核算" :edit-render="{}" width='300'>
              <template #default="{ row }">
                  <vxe-switch v-model="row.isAddAccounting" open-label="是" close-label="否" :open-value="true"
                      :close-value="false" @change="saveRowEvent(row)"></vxe-switch>
              </template>
              <template #edit="{ row }">
                  <vxe-switch v-model="row.isAddAccounting" open-label="是" close-label="否" :open-value="true"
                      :close-value="false"></vxe-switch>
              </template>
          </vxe-column>
          <vxe-column title="操作" width="520" align="left" >
            <template #default="{ row }">
              <el-button type="primary" @click="editNew(row)">编辑</el-button>
              <my-confirm-button type="danger" @click="deltask(row)">删除</my-confirm-button>
            </template>
          </vxe-column>
        </vxe-table>
        <div style="font-size: 14px; color: #606266;">共：{{tableData?.length}} 条</div>
      </div>

      <el-drawer :visible.sync="editTaskshow" wrapperClosable :close-on-click-modal="true" direction="rtl" size="767px"
        element-loading-text="拼命加载中" v-loading="addLoading" :show-close="false">
        <dialogright ref="refdialogright" style="height: 100%;width:100%" @onCloseAddForm="onCloseAddForm"
          :allsellist="allsellist"></dialogright>
        <span slot="title" style="height: 0px;"></span>
      </el-drawer>

      <el-dialog :visible.sync="showAddnew" width="750px" v-dialogDrag :close-on-click-modal="true">
        <el-form :model="addForm" ref="addForm" label-width="120px" :rules="addFormRules">
              <div class="bzjzcjrw">
                  <div class="bt">
                      <span style="float: left">{{ this.formTitle }}</span>
                  </div>
                  <div class="bzccjlx">
                      <div class="lxwz">员工姓名</div>
                      <div class="lxwz2 formtop">
                          <el-form-item prop="employeeName"  label=" " label-width="12px">
                              <vxe-input :maxlength="20"  style="height: 31px; width: 320px; font-size: 12px;" v-model="addForm.employeeName" placeholder="员工姓名" clearable></vxe-input>
                          </el-form-item>
                      </div>
                  </div>
                  <br/>
                  <div class="bzccjlx">
                    <div class="lxwz">公司</div>
                    <div class="lxwz2">
                        <el-form-item prop="companyId" label=" " label-width="12px">
                            <el-select style="width:320px" v-model="addForm.companyId" filterable>
                                <el-option v-for="item in companyList" :key="item.setId" :label="item.sceneCode"
                                    :value="item.setId" />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>
                <br/>
                  <div class="bzccjlx">
                      <div class="lxwz">工价比例</div>
                      <div class="lxwz2">
                          <el-form-item prop="employeeWorkPrice" label=" " label-width="12px">
                            <el-input-number :controls="false" v-model="addForm.employeeWorkPrice" :precision="2" :step="0.1" :min="0" :max="99999"  style="height: 40px; width: 320px; font-size: 12px;"></el-input-number>
                          </el-form-item>
                      </div>
                  </div>
              </div>

          </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="showAddnew = false">取 消</el-button>
          <el-button type="primary" @click="onSubmit">提 交</el-button>
        </span>
  </el-dialog>

  </my-container>
  </template>
  <script>
  import MyConfirmButton from "@/components/my-confirm-button";
  import { formatTime } from "@/utils";
  import MyContainer from "@/components/my-container/noheader.vue";
  import dialogright from '@/views/media/packagework/dialogright';
  import {
    getPackagesProcessingRecordListAsync, getCallOutRecordListAsync,deleteReord,
    getCurrentUser, editPackagesOtherEmployees,delPackagesOtherEmployees
  } from '@/api/inventory/packagesprocess';//包装加工
  import {getPackagesSetData,savePackagesSet, saveDataOrderListDataAsync as saveDataOrderPackagesAsync,deletePackagesSet} from '@/api/inventory/packagesSetProcessing';
  import goodschoice from "@/views/base/goods/goods2.vue";
  export default {
    name: "pricelist",
    props: ['summaryarry', 'storagelist', 'allsellist','machineTypeList'],
    components: { MyContainer, dialogright, MyConfirmButton,goodschoice },
    filters: {
      datefifter: (date) => {
        if (!date) {
          return '';
        }
        return date.slice(2, 10);
      }
    },
    data() {
      return {
        formTitle:"新增员工",
        showAddnew: false,
        addForm: {
            employeeId: 0,
            employeeName: "",
            employeeWorkPrice:0,
            companyId:null,
            companyName:null,
            isAddAccounting:null,
        },
        addFormRules: {
            employeeName: [{ required: true, message: '请填写', trigger: 'blur' }],
            employeeWorkPrice: [{ required: true, message: '请填写', trigger: 'blur' }],
              },
        companyList:[],
        showfinishedProductImg:false,
        fileList: [],
        uploadLoading: false,
        userid: '',
        value101: '',
        value20: '',
        tableedittitle: '',
        keys: 'four',
        tokey: 'one',
        shenpidata: {},
        diaobodata: {},
        shenpishow: false,
        btnloading: false,
        updialogVisible: false,
        addLoading: true,
        tableeditshow: false,
        pageLoading: false,
        qualificationshow: false,
        disabled: true,
        workshow: false,
        editTaskshow: false,
        viewReferenceRemark: false,
        shootingTaskRemarkrawer: false,
        nohgz: false,
        editshow: true,
        qualfifter: {
          msg: ''
        },
        zhijiandata: {},
        isnei: false,
        taskroww: {},
        outlist: {},
        upForm: {},
        upFinishForm:{},
        titlerow: null,
        loginuser: {},
        tableData: [],
        allAlign1: null,
        tableData1: [],
        allAlign2: null,
        tableData2: [],
        typeId: null,
        taskrow: null,
        allid: [],
        userlist: [],
        neiarr: [],
        recordTitle: "",
        dialogVisibleSyj: false,
        seltype:null,
        goodschoiceVisible: false,
      };
    },
    mounted () {
       this.getCompanyList();
    },
    methods: {
      //加入核算开关
      async saveRowEvent(row){
        const keys = Object.keys(row);
          keys.forEach(key => {
            if (this.addForm.hasOwnProperty(key)) {
              this.addForm[key] = row[key];
            }
          });
        const { success } = await editPackagesOtherEmployees(this.addForm)
      },
      cellclick(events){
        let _this = this;
        if(events.column.field == 'expand'){
          _this.loadingg = true;
          _this.openexpand(events.row,null)
        }
      },
      async getCompanyList()
      {
        var re = await getPackagesSetData({ setType: 2 });
        if(re?.success)
        {
            this.companyList=re.data;
        }
      },
      onSubmit () {
        this.$refs.addForm.validate((valid) => {
          if (valid) {
            editPackagesOtherEmployees(this.addForm).then(res => {
              if (!res.success) {
                return
              } else {
                this.$message({ message: '添加成功', type: 'success' })
                this.showAddnew = false;
                this.clickRefresh();
              }
            })
          }
        })
      },
      addNew () {
        this.getCompanyList();
        this.formTitle = "新增员工";
        this.addForm = {
            employeeId: 0,
            employeeName: "",
            employeeWorkPrice:100,
            companyId:null
        },
        this.showAddnew = true;
      },
      editNew(row)
      {
        this.getCompanyList();
        this.formTitle = "编辑员工";
        this.addForm.employeeId=row.employeeId;
        this.addForm.employeeName=row.employeeName;
        this.addForm.employeeWorkPrice=row.employeeWorkPrice;
        this.addForm.companyId=row.companyId;
        this.showAddnew = true;
      },
      async deltask(row){

        this.$confirm('确认删除, 是否继续?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    const res = await delPackagesOtherEmployees(row.employeeId);
                    if (res?.success) {
                        this.$message({ type: 'success', message: '删除成功!' });
                        this.$emit('getOtheremployeeslisfuc');
                    }
                }).catch(() => {
                    this.$message({ type: 'info', message: '已取消删除' });
                });
      },
      codetoname(val){
        var a = "";
        this.machineTypeList.map((item)=>{
          if(item.setId == val){
            a = item.sceneCode
          }
        })
        return a;
      },
      rowStyleFun({ row, rowIndex, $rowIndex }) {
          if (!row.isEnd) {
              return '';
          } else {
              return 'droprow';
          }
      },
      sumNum(list, field) {
        let count = 0
        list.forEach(item => {
          count += Number(item[field])
        })
        return count
      },
      countAmount(row) {
        return row.amount * row.num1
      },
      countAllAmount(data) {
        let count = 0
        data.forEach(row => {
          count += this.countAmount(row)
        })
        return count
      },
      footerMethod({ columns, data }) {
        // debugger
        const sums = [];
        if (!this.summaryarry)
          return sums
        var arr = Object.keys(this.summaryarry);
        if (arr.length == 0)
          return sums
        var hashj = false;
        columns.forEach((column, index) => {
          if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
            var sum = this.summaryarry[column.property + '_sum'];
            if (sum == null) return;
            sums[index] = sum
            // else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
            // else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum
            // else sums[index] = sum
          }
          else sums[index] = ''
        });
        return [sums]
      },
      footerMethod1({ columns, data }) {
        if(this.typeId==1){
          const sums = [];
          sums[2] = this.summaryy.totalQuantity;
          return [sums]
        }else{
          const sums = [];
          sums[3] = this.summaryy.totalQuantity;
          return [sums]
        }

      },
      userselect(user) {
        this.taskrow.createPackId = user.value;
        this.taskroww.selUserId = user.value;
        this.taskroww.createPackId = user.value;
        this.userid = user.value;
        this.userlist.map((item)=>{
          if(item.userId == user.value){
            this.taskroww.createUserName = item.userName;
            this.zhijiandata.createUserName = item.userName;
          }
        })
        // this.tableData2.map((item)=>{
        //   if(item.recordId == this.taskrow.recordId){
        //     item.createUserName = this.fiftername(user.value);
        //   }
        // })
        this.$forceUpdate();
      },
      formatIsCommission(value) {
        return value == null ? null : formatTime(value, 'YY-MM-DD')
      },
      getuser(val) {
        this.userlist = val;
        this.getloginuser();
      },
      async getloginuser() {
        const res = await getCurrentUser();
        if (!res?.success) {
          return
        }
        this.loginuser = res.data;
      },
      fiftername(value) {
        var info = ' '
        this.userlist.forEach((item) => {
          if (item.userId == value) info = item.userName
        })
        return info;
      },
      updatelistt(e) {
        this.tableData = e.data;
      },
      openAlert(options) {
        VXETable.modal.alert(options)
      },
      async tableeditfuc(row, i) {
        this.typeId = i;
        this.titlerow = row;
        this.taskrow = row;
        this.tableedittitle = i == 1? '加工编辑':this.tableedittitle = i == 2? '调出编辑':this.tableedittitle = i == 3? '收货编辑'
        :this.tableedittitle = i == 4? '调入编辑':this.tableedittitle = i == 6? '质检编辑':'其他';
        switch (i) {
          case 1:
            var params = {
              packagesProcessId: row.packagesProcessingId,
              typeId: i
            }
            this.tokey = 'one';
            const res = await getPackagesProcessingRecordListAsync(params);
            if (!res?.success) {
              return
            }

            // res.data.recordList.map((row)=>{
            //   if(row.createDate){
            //     row.createDate = row.createDate.slice(2,10)
            //   }
            // })
            this.tableData2 = res.data.recordList;
            this.summaryy = res.data;
            break;
          case 2:
            var params = {
              packagesProcessId: row.packagesProcessingId,
              typeId: i
            }
            this.tokey = 'two';
            const res2 = await getCallOutRecordListAsync(params);
            if (!res2?.success) {
              return
            }
            this.tableData2 = res2.data.recordList;
            this.summaryy = res2.data;
            break;
          case 3:
            var params = {
              packagesProcessId: row.packagesProcessingId,
              packagesProcessDetialId: row.id,
              typeId: i
            }
            this.tokey = 'three';
            const res3 = await getPackagesProcessingRecordListAsync(params);
            if (!res3?.success) {
              return
            }
            this.tableData2 = res3.data.recordList;
            this.summaryy = res3.data;
            break;
          case 4:
            this.halfrow = row;
            var params = {
              packagesProcessId: row.packagesProcessingId,
              typeId: 1,
              packagesProcessDetialId: row.id
              // packaesProcessingDetialId: row.id

            }
            this.tokey = 'four';
            const res4 = await getCallOutRecordListAsync(params);
            if (!res4?.success) {
              return
            }

            this.tableData2 = res4.data.recordList;
            this.summaryy = res4.data;
            break;
          case 6:
            var params = {
              packagesProcessId: row.packagesProcessingId,
              typeId: 2
            }
            this.tokey = 'six';
            const res6 = await getPackagesProcessingRecordListAsync(params);
            if (!res6?.success) {
              return
            }
            this.tableData2 = res6.data.recordList;
            this.summaryy = res6.data;
            break;
        }
        this.tableeditshow = true;
      },
      doubleclick(e) {
        this.editTaskshow = true;
        this.$nextTick(() => {
          this.$refs.refdialogright.getfamsg(e);
        })

      },
      //关闭窗口，初始化数
      async onCloseAddForm(type) {
        await this.$emit('getTaskList');
        if (type == 1) {
          // this.addTask = false;
          this.editTaskshow = false;
        }
      },
      copytext(e) {
        let textarea = document.createElement("textarea")
        textarea.value = e
        textarea.readOnly = "readOnly"
        document.body.appendChild(textarea)
        textarea.select()
        let result = document.execCommand("copy")
        if (result) {
          this.$message({
            message: '复制成功',
            type: 'success'
          })
        }
        textarea.remove()
      },
      neitablesel(data){
        var arr = [];
        data.records.map((item)=>{
          arr.push(item.recordId)
        })
        this.neiarr = arr;
        this.diaobodata.id = arr.join();
      },
      checkchange(data) {
        this.$nextTick(()=>{
          this.$emit('pids', data.records);
        });
      },
      chilcheckchange(data) {
        this.$emit('chipids', data.records);
      },
      async delRecord(row) {
        this.$confirm("是否确定删除, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          let params = {
            recordIds: row.recordId,
            typeId: (this.typeId == 1 || this.typeId == 3 || this.typeId == 6) ? 0 : 1
          }
          var res = await deleteReord(params);
          if (!res?.success) {
            return
          }
          this.tableData2.splice(this.tableData2.indexOf(row), 1)
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.$emit('getTaskList');
        });
      },
      clickRefresh(){
        this.$emit('getOtheremployeeslisfuc');
      }
    },
  };
  </script>
  <style lang="scss" scoped>
  ::v-deep .el-form-item--mini.el-form-item,
  .el-form-item--small.el-form-item {
    margin-bottom: 20px;
  }

  ::v-deep .vxe-table--render-default .vxe-header--column {
    line-height: 18px !important;
  }

  /*滚动条整体部分*/
  ::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar {
    width: 18px !important;
    height: 26px !important;
  }

  /*滚动条的轨道*/
  ::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-track {
    background-color: #f1f1f1 !important;
  }

  /*滚动条里面的小方块，能向上向下移动*/
  ::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
    background-color: #c1c1c1 !important;
    border-radius: 3px !important;
    box-sizing: border-box !important;
    border: 2px solid #F1F1F1 !important;
    box-shadow: inset 0 0 6px rgba(255, 255, 255, .5) !important;
  }

  // 滚动条鼠标悬停颜色
  ::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
    background-color: #A8A8A8 !important;
  }

  // 滚动条拖动颜色
  ::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
    background-color: #787878 !important;
  }

  /*边角，即两个滚动条的交汇处*/
  ::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
    background-color: #dcdcdc !important;
  }


  // 表格内边距
  ::v-deep .vxe-table--render-default .vxe-cell {
    padding: 0 0 0 8px !important;
  }





  .flexrow {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .qualibtn ::v-deep .el-button {
    margin-left: 0 !important;
    margin-top: 10px;
  }

  .marginrt {
    margin: 0 10px 0 auto;
  }

  .point:hover {
    cursor: pointer;
  }

  .point {
    color: #409EFF;
  }


  .item {
    margin-bottom: 18px;
  }

  .clearfix {
    font-size: 20px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }

  .clearfix:after {
    clear: both
  }

  ::v-deep .box-card {
    margin-top: 20px;
    box-sizing: border-box;
    padding: 0 30px;

  }

  .flexcenter {
    display: flex;
    justify-content: left;
    align-items: left;
  }

  ::v-deep .height {
    height: 58px !important;
  }

  ::v-deep .height1 {
    height: 48px !important;
    font-size: 12px !important;
  }

  ::v-deep .cellheight1 {
    font-size: 12px !important;
  }

  .relativebox{
    position: relative;
    width: 100%;
  }
  .relativeboxx{
    position: relative;
    width: 100%;
  }
  .positioncenter{
    position: absolute;
    right: 10px;
    top: 28%;
    bottom: 50%;
    // transform: translate(-50%,-50%);
  }

  ::v-deep .droprow td {
      color:  rgb(250, 9, 9);
      position: relative;
  }

  ::v-deep  .droprow  ::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 0;
      width: 100%;
      height: 0.1px;
      background-color: rgb(250, 9, 9);
      transform: translateY(-50%);
  }


  .copyhover{
    display: none;
  }
  .relativebox{
    width: 250px;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
  }
  .relativebox:hover{
    width: 225px;
  }
  .textover{
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
  }
  .relativebox:hover .textover{
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
  }

  .relativebox:hover .copyhover{
    display: block;
    position: absolute;
    top: 50%;
    left: 80%;
    margin: 0 10px;
    z-index: 99;
    transform: translate(-50%,-50%);
    color: #409EFF;
    font-weight: 600;
  }

  // .copyhover:hover>.copyhover{
  //   display: block;

  //   position: absolute;
  //   top: 50%;
  //   left: 50%;
  //   transform: translate(-50%,-50%);
  // }

  .minibtn ::v-deep .el-button--mini {
    padding: 7px 8px;
  }

  .vxeclass {
    z-index: 10000 !important;
  }

  ::v-deep .vxe-body--expanded-cell {
    padding: 0 !important;
  }

  .el-icon-document-copy {
    font-size: 16px;
    margin-left: 2px;
  }

  .el-icon-document-copy:hover {
    font-size: 16px;
    margin-left: 2px;

  }

  ::v-deep .vxe-modal--header {
    margin-top: 8px !important;
    background-color: transparent !important;
    padding: 0 6px;

  }

  ::v-deep .vxe-modal--header-title {
    font-size: 16px;
    color: #666;
    font-weight: 500;
  }

  ::v-deep .vxe-modal--header-right {
    // color: transparent ;
    font-size: 12px;
    line-height: 32px;
  }

  ::v-deep .vxe-modal--content {
    padding: 20px 35px 35px 35px;
  }

  ::v-deep .bzbjbt {
    height: 60px;
    background-color: rgb(255, 255, 255);
    font-size: 18px;
    color: #666;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
    box-sizing: border-box;
    padding: 20px 35px;
  }

  ::v-deep .rwmc {
    // width: 750px;
    height: 70px;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 3px 5px #eeeeee;
    box-sizing: border-box;
    padding: 0 56px;
    display: flex;
  }

  ::v-deep .rwmc .xh {
    height: 65px;
    font-size: 18px;
    line-height: 68px;
    box-sizing: border-box;
    padding: 0 2px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #666;
  }

  .tablehei {
    height: 80vh;
  }

  ::v-deep .rwmc .mc,
  ::v-deep .icon {
    height: 65px;
    font-size: 18px;
    line-height: 68px;
    box-sizing: border-box;
    margin-left: 10px;
    padding: 0 2px;
    display: inline-block;
    color: #666;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  ::v-deep .el-drawer .el-drawer__header {
    padding: 0 !important;
  }

  ::v-deep .vxe-footer--row {
    height: 50px;
  }

  .statuscss ::v-deep .el-tag--dark {
    border-color: #fff !important;
  }

  .minisize ::v-deep .vxe-button {
    padding: 0 !important;
  }

  ::v-deep .el-main{
    overflow: hidden;
  }

  ::v-deep .bzjzcjrw {
      width: 100%;
      margin-top: 15px;
      background-color: #fff;
  }

  ::v-deep .bzjzcjrw .bt {
      height: 40px;
      /* background-color: aquamarine; */
      font-size: 18px;
      color: #666;
      margin-bottom: 20px;
      border: 1px solid #dcdfe6;
      border-top: 0px;
      border-right: 0px;
      border-left: 0px;
      box-sizing: border-box;
      padding: 0 35px;
  }

  ::v-deep .bzjzcjrw .bzccjlx {
      box-sizing: border-box;
      padding: 0 30px;
      display: flex;
  }

  ::v-deep .bzjzcjrw .bzccjlx {
      width: 100%;
      height: 35px;
      box-sizing: border-box;
      padding: 0 30px;
      display: flex;
  }

  ::v-deep .bzjzcjrw .bzccjlx .lxwz {
      width: 20%;
      font-size: 14px;
      color: #666;
      vertical-align: top;
      line-height: 26px;
      /* background-color: rgb(204, 204, 255); */
  }

  ::v-deep .bzjzcjrw .bzccjlx .lxwz2 {
      width: 80%;
  }
  </style>

