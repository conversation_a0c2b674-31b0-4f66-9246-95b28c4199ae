<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss"
                    startPlaceholder="创建时间" endPlaceholder="创建时间" />
                <dateRange :startDate.sync="ListInfo.payStartTime" :endDate.sync="ListInfo.payEndTime" class="publicCss"
                    startPlaceholder="付款时间" endPlaceholder="付款时间" />
                <el-input v-model.trim="ListInfo.buyUserId" placeholder="买家ID" maxlength="200" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.orderNo" placeholder="订单号" maxlength="200" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.internalOrderNo" placeholder="内部订单号" maxlength="200" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="200" clearable
                    class="publicCss" />
                <!-- <el-input v-model="ListInfo.createdUserName" placeholder="创建人" maxlength="50" clearable
                    class="publicCss" /> -->
                <!-- <el-select v-model="ListInfo.analysisError" placeholder="解析" class="publicCss" clearable>
                    <el-option v-for="item in [{ label: '正常', value: true }, { label: '异常', value: false }]" :key="item.value"
                        :label="item.label" :value="item.value">
                    </el-option>
                </el-select> -->
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                <!-- <el-button type="primary" @click="importProps" v-if="checkPermission('yggrmImport')">导入</el-button> -->
            </div>
        </template>
        <vxetablebase :id="'yggrmQuoteDetails202408041634'" ref="table" :tableData="tableData" :tableCols="tableCols"
            :is-index="true" :that="that" style="width: 100%;  margin: 0" @sortchange='sortchange' :height="'100%'"
            :showsummary="true" class="detail" :summaryarry="summaryarry"
            :treeProp="{ rowField: 'id', parentField: 'pId', transform: true, }" v-loading="loading">
            <!-- isCustomization = 1:定制款  2：常规款（不显示编辑按钮） -->

            <!-- <template slot="right">
                <vxe-column title="操作" width="120" align="center" fixed="right">
                    <template #default="{ row, $index }">
                        <div style="display: flex">
                            <el-button type="text" v-if="row.pId == '0' && checkPermission('yggrmEdit') && row.isCustomization===1"    @click="editProps(row.id)">编辑</el-button>
                            <el-button type="text" v-if="row.pId == '0'" @click="viewLogs(row.id)">日志</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template> -->
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <!-- <el-date-picker v-model="yearMonthDay" align="right" type="date" placeholder="选择日期"
                    :picker-options="pickerOptions1" value-format="yyyy-MM-dd" style="margin-bottom: 30px;">
                </el-date-picker> -->
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog title="日志" :visible.sync="viewLogsVisible" width="30%" v-dialogDrag>
            <viewLogsVue :parentId="viewLogsInfo.parentId" tableName="YGGRMNormsCSRecordDtl" v-if="viewLogsVisible" />
        </el-dialog>

        <el-drawer title="编辑" :visible.sync="drawer" direction="rtl" size="90%" :wrapperClosable="false">
            <el-form :model="ruleForm" status-icon ref="ruleForm" label-width="100px" class="demo-ruleForm"
                style="padding: 16px;box-sizing: border-box;" :rules="rules">
                <el-form-item label="买家id:" prop="pass">
                    <div>{{ ruleForm.buyUserId }}</div>
                </el-form-item>
                <el-form-item label="聊天记录:" prop="chatPicUrl">
                    <div class="chatPicUrl">
                        <uploadimgFile v-if="drawer" ref="uploadimgFile" :ispaste="false" :noDel="false"
                            :accepttyes="accepttyes" :isImage="true" :uploadInfo="chatUrls" :keys="[1, 1]"
                            @callback="getImg" :imgmaxsize="10" :limit="10" :multiple="true">
                        </uploadimgFile>
                        <span class="picTips">提示:点击灰框可直接贴图！！！</span>
                    </div>
                </el-form-item>
                <el-form-item label="规格报价明细:" prop="pass">
                    <span style="color: red;">(禁止截图给客户！！！)</span>
                    <el-button style="margin-left: 20px;" @click="addProps" type="primary">新增一行</el-button>
                </el-form-item>
                <div style="height: 400px;">
                    <el-table :data="ruleForm.dtls" style="width: 100%;height:100%" max-height="400">
                        <el-table-column prop="norms" label="规格" width="75">
                            <template #header="{ column }">
                                <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>规格
                            </template>
                            <template #default="{ row, $index }">
                                <el-select v-model="row.norms" placeholder="规格" @change="changeName('gg', $index)">
                                    <el-option key="6mm" label="6mm" value="6mm" />
                                    <el-option key="8mm" label="8mm" value="8mm" />
                                    <el-option key="祥云" label="祥云" value="祥云" />
                                    <el-option key="绵羊" label="绵羊" value="绵羊" />
                                    <el-option key="小树林" label="小树林" value="小树林" />
                                    <el-option key="蓝天白云" label="蓝天白云" value="蓝天白云" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="bbType" label="是否包边" width="75">
                            <template #header="{ column }">
                                <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>是否包边
                            </template>
                            <template #default="{ row, $index }">
                                <el-select v-model="row.bbType" placeholder="包边" @change="changeName('bb', $index)">
                                    <el-option key="不包边" label="不包边" value="不包边" />
                                    <el-option key="包黑边" label="包黑边" value="包黑边" />
                                    <el-option key="包灰边" label="包灰边" value="包灰边" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="isDK" label="是否打扣" width="50">
                            <template #default="{ row, $index }">
                                <div style="display: flex;justify-content: center;">
                                    <el-checkbox v-model="row.isDK" @change="changeName('dk', $index)" />
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="isNeedXP" label="是否需要吸盘" width="60">
                            <template #default="{ row, $index }">
                                <div style="display: flex;justify-content: center;">
                                    <el-checkbox v-model="row.isNeedXP" @change="changeName('xp', $index)" />
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="fullNormsName" label="规格全称" width="200">
                            <template #default="{ row }">
                                <el-input v-model="row.fullNormsName" placeholder="规格全称" disabled />
                            </template>
                        </el-table-column>
                        <el-table-column prop="sheetSquareSaleAmount" label="1平方售价" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.sheetSquareSaleAmount" :min="0" :max="10000"
                                    :precision="2" :controls="false" label="1平方售价" class="iptCss"
                                    @change="changePrice($index)" disabled />
                            </template>
                        </el-table-column>
                        <el-table-column prop="goodsCode" label="商品编码" width="85" v-if="false">
                            <template #default="{ row }">
                                <el-input v-model="row.goodsCode" placeholder="商品编码" disabled />
                            </template>
                        </el-table-column>
                        <el-table-column prop="sheetDKCount" label="卡扣数" width="85">
                            <template #default="{ row }">
                                <el-input-number v-model="row.sheetDKCount" :min="0" :max="10000" :precision="0"
                                    :controls="false" label="包边布一米价格" class="iptCss" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="sheetLength" label="长(米)" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.sheetLength" :min="0" :max="10000" :precision="4"
                                    :controls="false" label="长(米)" class="iptCss" @change="changePrice($index)" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="sheetWidth" label="宽(米)" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.sheetWidth" :min="0" :max="10000" :precision="4"
                                    :controls="false" label="宽(米)" class="iptCss" @change="changePrice($index)" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="sheetCount" label="张" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.sheetCount" :min="0" :max="10000" :precision="0"
                                    :controls="false" label="张" class="iptCss" @change="changePrice($index)" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="totalAmount" label="价格" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.totalAmount" :min="0" :max="9999999" :precision="2"
                                    :controls="false" label="价格" class="iptCss" @change="changePrice($index)"
                                    disabled />
                            </template>
                        </el-table-column>
                        <el-table-column prop="discount" label="折扣" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.discount" :min="0" :max="10" :precision="2"
                                    :controls="false" label="折扣" class="iptCss" @change="changePrice($index)" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="actualAmount" label="折后价" width="85">
                            <template #default="{ row }">
                                <el-input-number v-model="row.actualAmount" :min="0" :max="9999999" :precision="2"
                                    :controls="false" label="折后价" class="iptCss" disabled />
                            </template>
                        </el-table-column>
                        <el-table-column prop="remark" label="备注" width="auto">
                            <template #default="{ row }">
                                <el-input v-model="row.remark" maxlength="50" clearable placeholder="备注" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="orderFreightBagFee" label="操作" width="70">
                            <template #default="{ row, $index }">
                                <el-button type="danger" @click="ruleForm.dtls.splice($index, 1)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <el-form-item label="合计:" prop="pass">
                    <div>{{ getSimilarity }}</div>
                </el-form-item>
                <el-form-item label="实际报价:" prop="pass">
                    <el-input-number v-model="ruleForm.actualTotalAmount" :min="0" :max="9999999" :precision="2"
                        :controls="false" label="实际报价" class="iptCss" />
                </el-form-item>
                <div style="display: flex;justify-content: end;margin-top: 100px;">
                    <el-button type="primary" @click="submitForm(true)" v-throttle="5000">保存</el-button>
                    <el-button type="primary" @click="copyProps('bj')">复制报价</el-button>
                    <el-button type="primary" @click="copyProps('bz')">复制加工备注</el-button>
                </div>
            </el-form>
        </el-drawer>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import viewLogsVue from './viewLogs.vue'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { getYGGRMSaleOrderRecordList, updateYGGRMRecordDtlAsync, importCustomMadeMultipleAsync } from '@/api/inventory/customNormsGoods'
import { getYGGRMCSRecordPageList, getYGGRMCSRecordByBuyUserId, saveYGGRMCSRecord, getCostSetByYGGRMCache, getYGGRMCSRecordById, getCustomMadeRecordDtlLogAsync, exportYGGRMSaleOrderRecordList } from '@/api/inventory/customNormsGoods'
const tableCols = [
    { sortable: 'custom', width: '120', align: 'left', prop: 'internalOrderNo', label: '内部订单号', type: 'orderLogInfo', orderType: 'orderNoInner', treeNode: true, },
    { sortable: 'custom', width: '120', align: 'left', prop: 'orderNo', label: '订单编号', },
    { sortable: 'custom', width: '80', align: 'left', prop: 'buyUserId', label: '买家ID', },
    { sortable: 'custom', width: '150', align: 'left', prop: 'createdTime', label: '创建时间', },
    { sortable: 'custom', width: '150', align: 'left', prop: 'payTime', label: '付款时间', },
    // { sortable: 'custom', width: '65', align: 'left', prop: 'createdUserName', label: '创建人', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'orderFreightBagFee', label: '快递袋费', },
    { sortable: 'custom', width: '65', align: 'center', prop: 'orderPackProcessFee', label: '打包费', },
    { sortable: 'custom', width: '65', align: 'center', prop: 'totalSheetCount', label: '总张数', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'totalCost', label: '总成本', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'actualTotalAmount', label: '实际报价', },
    // { width: '80', align: 'left', prop: 'pics', label: '聊天记录', type: 'images' },
    {
        sortable: 'custom', width: '80', align: 'center', prop: 'analysisError', label: '状态', formatter: (row) => {
            if (row.pId == 0) {
                return row.analysisError == false ? '正常' : '异常'
            } else {
                return ''
            }
        }
    },
    { width: '180', align: 'left', prop: 'fullNormsName', label: '规格全称', },
    { width: '110', align: 'center', prop: 'sheetSquareCost', label: '单张平方价格', },
    { width: '110', align: 'center', prop: 'metreBBClothCost', label: '包边布一米价格', },
    { width: '80', align: 'center', prop: 'sheetBBPackProcessCost', label: '包边加工费', },
    { width: '110', align: 'center', prop: 'sheetSixIronbuckleCost', label: '单张铁扣成本', },
    { width: '80', align: 'center', prop: 'sheetDKPackProcessCost', label: '打扣加工费', },
    { width: '80', align: 'center', prop: 'sheetSixXPCost', label: '单张吸盘成本', },
    { width: '80', align: 'center', prop: 'sheetMSTCost', label: '单张魔术贴成本', },
    { width: '50', align: 'center', prop: 'sheetDishClothcCost', label: '抹布', },
    { width: '50', align: 'center', prop: 'sheetSprayKettleCost', label: '喷壶', },
    { width: '70', align: 'center', prop: 'sheetCroppingCost', label: '单张裁剪', },
    { width: '60', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { width: '60', align: 'center', prop: 'sheetDKCount', label: '卡扣数', },
    { width: '60', align: 'center', prop: 'sheetWidth', label: '宽(米)', },
    { width: '60', align: 'center', prop: 'sheetLength', label: '长(米)', },
    // {
    //     width: '90', align: 'center', prop: 'analysisError', label: '解析', formatter: (row) => {
    //         if (row?.analysisError === null || row?.analysisError === undefined) {
    //             return "";
    //         } else {
    //             return row?.analysisError === true ? '正常' : '异常'
    //         }
    //     }
    // },
    { width: '90', align: 'center', prop: 'remark', label: '备注', },
    { width: '90', align: 'center', prop: 'orderRemark', label: '订单备注', },
    { sortable: 'custom', width: '90', align: 'center', prop: 'updatedUserName', label: '修改人', },
    // {
    //     width: '90', align: 'center', label: '操作', type: 'button', btnList: [
    //         { label: "编辑", handle: (that, row) => that.editProps(row.id) },
    //         { label: "日志", handle: (that, row) => that.viewLogs(row) }
    //     ]
    // },
]

const viewsLogCols = [
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'createdUserName', label: '操作人', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'createdTime', label: '时间', formatter: (row) => row.createdTime ? dayjs(row.createdTime).format('YYYY-MM-DD HH:mm:ss') : '' },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'content', label: '操作内容', },
]

const ys = {
    norms: '规格',
    fullNormsName: '规格全称',
    sheetSquareSaleAmount: '1平方售价',
    sheetDKCount: '打扣数',
    sheetWidth: '单张宽',
    sheetLength: '单张长',
    sheetCount: '张数',
    totalAmount: '总价',
    discount: '折扣',
    actualAmount: '实际报价',
    remark: '备注',
}
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, uploadimgFile, viewLogsVue, dateRange
    },
    data() {
        return {
            ys,
            paytimeRanges: [],
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'createdTime',
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
                buyUserId: null,//买家ID
                orderNo: null,//订单号
                createdUserName: null,//创建人
                payStartTime: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),//付款开始时间
                payEndTime: dayjs().format('YYYY-MM-DD'),//付款结束时间
                analysisError: null
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            summaryarry: {},
            isExport: false,
            importVisible: false,
            importVisible: false,
            fileList: [],
            file: null,
            importLoading: false,
            viewLogsVisible: false,
            viewsLogCols,
            viewLogsData: [],
            viewLogsInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: true,
                parentId: null,
                tableName: 'YGGRMNormsCSRecordDtl'
            },
            ruleForm: {
                buyUserId: null,//买家id
                orderNo: null,//订单号
                totalAmount: null,//总价
                actualTotalAmount: null,//实际总价
                chatPicUrl: null,//聊天记录
                dtls: [
                    {
                        // bbColor: '无',//包边颜色
                        goodsCode: null,//商品编码
                        norms: null,//规格
                        bbType: null,//是否包边
                        isDK: true,//是否打扣
                        isNeedXP: true,//是否需要吸盘
                        fullNormsName: null,//规格全称
                        remark: null,//备注
                        discount: 1,//折扣
                        sheetSquareSaleAmount: null,//1平方售价
                        sheetDKCount: 6,//打扣数
                        sheetWidth: null,//单张宽
                        sheetLength: null,//单张长
                        sheetCount: null,//张数
                        totalAmount: null,//总价
                        actualAmount: null,//实际报价
                    }
                ],
            },
            rules: {
                chatPicUrl: [
                    { required: true, message: '请上传聊天记录', trigger: 'change' }
                ]
            },
            chatUrls: [],
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            drawer: false,
        }
    },
    computed: {
        getSimilarity() {
            let sum = 0
            this.ruleForm.dtls.forEach(item => {
                if (item.actualAmount !== null && item.actualAmount !== undefined && item.actualAmount !== '') {
                    sum += item.actualAmount
                }
            })
            sum = Number(sum).toFixed(2)
            this.ruleForm.totalAmount = sum
            return sum
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        changePrice(i) {
            //计算价格
            const item = this.ruleForm.dtls[i]
            if ((item.sheetLength !== null && item.sheetLength !== undefined) && (item.sheetWidth !== null && item.sheetWidth !== undefined) && (item.sheetCount !== null && item.sheetCount !== undefined) && (item.sheetSquareSaleAmount !== null && item.sheetSquareSaleAmount !== undefined)) {
                item.totalAmount = (item.sheetLength * item.sheetWidth * item.sheetCount * item.sheetSquareSaleAmount).toFixed(2)
            }
            //计算折后价
            if (item.totalAmount && item.discount) {
                item.actualAmount = (item.totalAmount * item.discount).toFixed(2)
            }
        },
        async changeName(type, i) {
            const res = this.catchList.filter(item => item.norms == this.ruleForm.dtls[i].norms && item.bbType == this.ruleForm.dtls[i].bbType && item.isDK == this.ruleForm.dtls[i].isDK && item.isNeedXP == this.ruleForm.dtls[i].isNeedXP)
            if (res.length > 0) {
                this.ruleForm.dtls[i].fullNormsName = res[0].fullNormsName
                this.ruleForm.dtls[i].sheetSquareSaleAmount = res[0].sheetSquareSaleAmount
                this.ruleForm.dtls[i].goodsCode = res[0].goodsCode
                //将后面的数据全部清空
                this.ruleForm.dtls[i].sheetDKCount = this.ruleForm.dtls[i].isDK ? 6 : null
                this.ruleForm.dtls[i].sheetLength = null
                this.ruleForm.dtls[i].sheetWidth = null
                this.ruleForm.dtls[i].sheetCount = null
                this.ruleForm.dtls[i].totalAmount = null
                this.ruleForm.dtls[i].discount = 1
                this.ruleForm.dtls[i].actualAmount = null
                this.ruleForm.dtls[i].remark = null
                // this.ruleForm.dtls[i].bbColor = this.ruleForm.dtls[i].bbColor?this.ruleForm.dtls[i].bbColor:'无'
            } else {
                this.ruleForm.dtls[i].fullNormsName = null
                this.ruleForm.dtls[i].sheetDKCount = this.ruleForm.dtls[i].isDK ? 6 : null
                this.ruleForm.dtls[i].sheetSquareSaleAmount = null
                this.ruleForm.dtls[i].sheetLength = null
                this.ruleForm.dtls[i].sheetWidth = null
                this.ruleForm.dtls[i].sheetCount = null
                this.ruleForm.dtls[i].totalAmount = null
                this.ruleForm.dtls[i].discount = 1
                this.ruleForm.dtls[i].actualAmount = null
                this.ruleForm.dtls[i].remark = null
                // this.ruleForm.dtls[i].bbColor = this.ruleForm.dtls[i].bbColor?this.ruleForm.dtls[i].bbColor:'无'
                this.ruleForm.dtls[i].goodsCode = null
            }
            if (type == 'dk') {
                this.ruleForm.dtls[i].sheetDKCount = this.ruleForm.dtls[i].isDK ? 6 : null
            }
        },
        //获取缓存
        async getCatchList() {
            const { data, success } = await getCostSetByYGGRMCache()
            if (success) {
                this.catchList = data
            }
        },
        addProps() {
            this.ruleForm.dtls.push({
                // id: '0',
                norms: null,//规格
                bbType: null,//是否包边
                isDK: true,//是否打扣
                isNeedXP: true,//是否需要吸盘
                fullNormsName: null,//规格全称
                remark: null,//备注
                discount: 1,//折扣
                sheetSquareSaleAmount: null,//1平方售价
                sheetDKCount: 6,//打扣数
                sheetWidth: null,//单张宽
                sheetLength: null,//单张长
                sheetCount: null,//张数
                // bbColor: '无',//包边颜色
                goodsCode: null,//商品编码
            })
        },
        getImg(data) {
            if (data) {
                this.chatUrls = data
                this.ruleForm.chatPicUrl = data.map(item => item.url).join(',')
            }
        },
        copyProps(type) {
            this.validate()
            let textarea = document.createElement("textarea")
            let e = ''
            let totalSheetCount = this.ruleForm.dtls.reduce((total, item) => total + item.sheetCount, 0);
            if (type == 'bj') {
                this.ruleForm.dtls.forEach(item => {
                    e += `定制:${item.fullNormsName},卡扣数:${item.sheetDKCount},#${item.goodsCode ? item.goodsCode : ''}@${item.sheetLength}*${item.sheetWidth}*${item.sheetCount}*张#,折扣后总价:${item.actualAmount}\n`
                })
                e = e + `共${totalSheetCount}张,合计:${this.ruleForm.totalAmount}元,优惠后总价:${this.ruleForm.actualTotalAmount}元\n`
            } else {
                this.ruleForm.dtls.forEach(item => {
                    e += `定制:${item.fullNormsName},卡扣数:${item.sheetDKCount},#${item.goodsCode ? item.goodsCode : ''}@${item.sheetLength}*${item.sheetWidth}*${item.sheetCount}*张#,备注:${item.remark ? item.remark : ''}\n`
                })
                e = e + `共${totalSheetCount}张\n`
            }
            textarea.value = e
            textarea.readOnly = "readOnly"
            document.body.appendChild(textarea)
            textarea.select()
            let result = document.execCommand("copy")
            if (result) {
                this.$message({
                    message: '复制成功',
                    type: 'success'
                })
            }
            textarea.remove()
        },
        validate() {

            this.ruleForm.dtls.forEach((item, i) => {
                for (const key in item) {
                    if (key.includes('sheet') && (item[key] === null || item[key] === undefined || item[key] === '' || item[key] < 0)) {
                        this.$message.error(`第${i + 1}行${ys[key]}有为空或小于0的数据,请检查`)
                        throw new Error(`第${i + 1}行${ys[key]}有为空或小于0的数据,请检查`)
                    }
                    if (key.includes('Amount') && (item[key] === null || item[key] === undefined || item[key] === '' || item[key] <= 0)) {
                        this.$message.error(`第${i + 1}行${ys[key]}有为空或小于等于0的数据,请检查`)
                        throw new Error(`第${i + 1}行${ys[key]}有为空或小于等于0的数据,请检查`)
                    }
                    if (key.includes('count') && (item[key] === null || item[key] === undefined || item[key] === '' || item[key] <= 0)) {
                        this.$message.error(`第${i + 1}行${ys[key]}有为空或小于等于0的数据,请检查`)
                        throw new Error(`第${i + 1}行${ys[key]}有为空或小于等于0的数据,请检查`)
                    }
                }
            })
            if (this.ruleForm.actualTotalAmount <= 0 || this.ruleForm.actualTotalAmount === null || this.ruleForm.actualTotalAmount === undefined) {
                this.$message.error('实际报价不能为空或小于等于0')
                throw new Error('实际报价不能为空或小于等于0')
            }
        },
        async submitForm(isOrder) {
            if (this.ruleForm.dtls.length === 0) return this.$message.error('至少添加一条数据')
            for (const key in this.ruleForm) {
                if (key != 'orderNo' && key != 'actualTotalAmount') {
                    //判断是否有空值
                    if (key == 'chatPicUrl') {
                        if (this.ruleForm[key] === null || this.ruleForm[key] === undefined || this.ruleForm[key] === '') {
                            this.$message.error('请上传聊天记录')
                            throw new Error('请上传聊天记录')
                        }
                    }
                }
            }
            this.validate()
            if (isOrder && !this.ruleForm.orderNo) return this.$message.error('请输入订单号')
            this.ruleForm.orderNo = isOrder ? this.ruleForm.orderNo : null
            const { data, success } = await updateYGGRMRecordDtlAsync(this.ruleForm)
            if (success) {
                await this.getList()
                this.$message.success('提交成功')
                this.drawer = false
                this.orderVisable = false
            } else {
                this.$message.error('提交失败')
            }
        },
        async editProps(id) {
            await this.getCatchList()
            const { data, success } = await getYGGRMCSRecordById({ id })
            if (success) {
                if (data.chatPicUrl) {
                    this.chatUrls = data.chatPicUrl.split(',').map((item, i) => {
                        return {
                            url: item,
                            name: `聊天截图${i + 1}`
                        }
                    })
                } else {
                    this.chatUrls = []
                }
                this.ruleForm = data
                this.ruleForm.dtls = data.dtls.map(item => {
                    return {
                        id: item.id,
                        norms: item.norms,//规格
                        bbType: item.bbType,//是否包边
                        isDK: item.isDK,//是否打扣
                        isNeedXP: item.isNeedXP,//是否需要吸盘
                        fullNormsName: item.fullNormsName,//规格全称
                        sheetSquareSaleAmount: item.sheetSquareSaleAmount,//1平方售价
                        sheetDKCount: item.sheetDKCount,//打扣数
                        sheetWidth: item.sheetWidth,//单张宽
                        sheetLength: item.sheetLength,//单张长
                        sheetCount: item.sheetCount,//张数
                        totalAmount: item.totalAmount,//总价
                        discount: item.discount,//折扣
                        actualAmount: item.actualAmount,//实际报价
                        remark: item.remark,//备注
                        // bbColor: item.bbColor,//包边颜色
                        goodsCode: item.goodsCode,//商品编码
                    }
                })
                this.drawer = true
            }
        },
        async viewLogs(id) {
            this.viewLogsInfo.parentId = id
            this.viewLogsVisible = true
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            //没有时间就提示
            // if (!this.yearMonthDay) return this.$message.error('请选择日期')
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            // form.append("yearMonthDay", this.yearMonthDay);
            this.importLoading = true
            await importCustomMadeMultipleAsync(form).then(({ success }) => {
                if (success) {
                    this.$message.success('导入成功')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            // this.yearMonthDay = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await exportYGGRMSaleOrderRecordList(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '阳光隔热膜报价明细' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
            this.isExport = false
        },
        async changeTime(e, type) {
            if (type == 'create') {
                this.ListInfo.startTime = e ? e[0] : null
                this.ListInfo.endTime = e ? e[1] : null
            } else {
                this.ListInfo.payStartTime = e ? e[0] : null
                this.ListInfo.payEndTime = e ? e[1] : null
            }
            await this.getList()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            const { data: { list, total, summary }, success } = await getYGGRMSaleOrderRecordList(this.ListInfo)
            if (success) {
                list.forEach((item, index) => {
                    if (item.chatPicUrl) {
                        item.pics = JSON.stringify(item.chatPicUrl.split(",").map(a => {
                            return {
                                url: a
                            }
                        }));
                    }
                });
                this.tableData = list
                this.total = total
                this.summaryarry = summary
                this.loading = false
            } else {
                //获取列表失败
                this.loading = false
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.detail ::v-deep .vxetoolbar20221212 {
    display: none !important;
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}

.iptCss {
    width: 80px;
}

::v-deep .cell {
    padding-left: 0;
}

.chatPicUrl {
    position: relative;

    .picTips {
        position: absolute;
        top: 0;
        left: 150px;
        color: #ff0000;
        font-size: 16px;
    }
}
</style>
