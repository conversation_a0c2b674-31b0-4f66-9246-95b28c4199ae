<template>
  <MyContainer>
    <template #header>
      <el-row>
        <el-col :span="24" style="display: flex; flex-direction: row;  line-height: 30px;">
          <div style="margin-right: 10px; font-size: 12px;">统计维度</div>
          <!-- <el-checkbox-group v-model="filter.groupTypes" @change="wdselchange" style="line-height: 30px;" v-if="wdlist">
            <el-checkbox v-for="item in wdlist" :key="item.value" :disabled="item.checked && selnum == 1"
              :label="item.value" :value="item.value" :checked="item.checked">{{ item.label }}</el-checkbox>
          </el-checkbox-group> -->
          <el-select class="marginleft" v-model="filter.groupTypes" clearable placeholder="统计维度" ref="selectIt" @focus="closeIt"
            style="width: 250px; margin-left: 10px" multiple collapse-tags>
            <el-option v-for="(item, i) in wdlist" :key="i" :label="item.label" :value="item.value" />
          </el-select>
          <span style="color: red; font-size: 12px">点此选择查看的数据统计维度,可以选择多维度查看</span>
        </el-col>
      </el-row>
      <el-row>
        <span style="font-size: 12px; margin-right: 10px">发货时间: </span>
        <el-date-picker :picker-options="pickerOptions" style="width: 210px;margin-right: 5px;" v-model="filter.timerange" type="daterange"
          format="yyyy-MM-dd" @change="changedate" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :clearable="false">
        </el-date-picker>

        <!-- <el-date-picker :picker-options="pickerOptions" style="width: 210px" v-model="importTime" type="daterange"
          format="yyyy-MM-dd" @change="changeTime" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始导入时间"
          end-placeholder="截止导入时间" :clearable="true">
        </el-date-picker> -->

        <el-select class="marginleft" v-model="filter.sendWareHouseId" clearable placeholder="发货仓"
          style="width: 200px; margin-left: 5px">
          <el-option v-for="(item, i) in newWareHouseList" :key="i" :label="item.name" :value="item.wms_co_id" />
        </el-select>

        <!-- <el-select class="marginleft" v-model="filter.platform"  clearable placeholder="平台" style="width: 250px"  >
        <el-option v-for="(item,i) in platformList" :key="i" :label="item.label" :value="item.value" />
      </el-select> -->

        <el-input class="marginleft" v-model.trim="filter.styleCode" placeholder="系列编码" clearable style="width: 200px"
          maxlength="50"></el-input>

        <el-input class="marginleft" v-model.trim="filter.goodsCode" placeholder="商品编码" clearable style="width: 200px"
          maxlength="50"></el-input>

        <el-select class="marginleft" v-model="filter.zrDepartment" clearable placeholder="损耗部门" style="width: 200px;"
          @change="getZrType(filter.zrDepartment)">
          <el-option v-for="(item, i) in damagedList" :key="i" :label="item" :value="item" />
        </el-select>

        <el-select class="marginleft" v-model="filter.zrType2" clearable placeholder="损耗类型" style="width: 200px;"
          @change="zrType2change()" @visible-change="shopIncident">
          <el-option v-for="(item, i) in damagedList2" :key="i" :label="item" :value="item" />
        </el-select>

        <el-select class="marginleft" v-model="filter.damagedHandType" clearable placeholder="处理方式" style="width: 170px">
          <el-option v-for="(item, i) in damagedHandList" :key="i" :label="item.name" :value="item.value" />
        </el-select>
        <!-- //////////////////////////////////////// -->
        <!-- <el-date-picker  class="marginleft"  style="width: 220px; " v-model="filter.SendGoodsDate" type="daterange" format="yyyy-MM-dd"
        value-format="yyyy-MM-dd" range-separator="至" :picker-options="pickerOptions"
        :start-placeholder="'发货开始时间'"
        :end-placeholder="'发货结束时间'">
      </el-date-picker> -->
        <span style="font-size: 15px; margin-right: 10px;font-size: 12px;">售后发起时间: </span>
        <el-date-picker class="marginleft" style="width: 220px; " v-model="filter.AfterSaleApproveDate" type="daterange"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" :picker-options="pickerOptions"
          @change="update" :start-placeholder="'售后发起时间'" :end-placeholder="'售后结束时间'">
        </el-date-picker>

        <span style="font-size: 15px; margin-right: 10px;font-size: 12px;">责任开始计算时间: </span>
        <el-date-picker class="marginleft" style="width: 220px; " v-model="filter.ZrSetDate" type="daterange"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" :picker-options="pickerOptions"
          @change="update" :start-placeholder="'责任开始计算时间'" :end-placeholder="'责任结束计算时间'">
        </el-date-picker>

        <el-input class="marginleft" v-model.trim="filter.orderInnerNo" placeholder="内部订单号" clearable style="width: 140px"
          maxlength="50"></el-input>
        <el-input class="marginleft" v-model.trim="filter.orderNo" placeholder="线上订单号" clearable style="width: 140px"
          maxlength="50"></el-input>


        <el-select v-model="filter.platform" clearable placeholder="平台" @change="onchangeplatform" style="width: 140px">
          <el-option v-for="item in platformList" :key="item.label" :label="item.label" :value="item.value" />
        </el-select>

        <el-select class="marginleft" filterable clearable v-model="filter.shopCode" placeholder="所属店铺"
          style="width: 200px" @change="zrType2change()">
          <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
            :value="item.shopCode"></el-option>
        </el-select>


        <!-- <el-select class="marginleft" v-model="filter.sendWareHouseId"    clearable placeholder="发货仓" style="width: 160px; margin-left: 10px;"  >
        <el-option v-for="item in newWareHouseList" :key="item.name" :label="item.name" :value="item.wms_co_id" />
      </el-select> -->



        <el-input v-model.trim="filter.expressOrder" clearable placeholder="快递单号" style="width: 160px"
          maxlength="50"></el-input>

        <!-- <el-input class="marginleft" v-model="filter.goodsCode" clearable placeholder="商品编码" style="width: 180px" maxlength="50"></el-input> -->


        <!-- <el-select class="marginleft" v-model="filter.zrDepartment"    clearable placeholder="责任部门(大类)" style="width: 200px;" @change="getZrType(filter.zrDepartment)"  >
        <el-option v-for="item in damagedList" :key="item" :label="item" :value="item"  />
      </el-select>

      <el-select class="marginleft" v-model="filter.zrType2"    clearable placeholder="责任类型(细类)" style="width: 200px;" @change="zrType2change()" >
        <el-option v-for="item in damagedList2" :key="item" :label="item" :value="item" />
      </el-select> -->


        <el-input class="marginleft" v-model.trim="filter.memberName" clearable placeholder="责任人" style="width: 140px"
          maxlength="50"></el-input>

        <el-input class="marginleft" v-model.trim="filter.managerMemberName" clearable placeholder="责任上级" style="width: 160px"
          maxlength="50"></el-input>

        <el-select class="marginleft" v-model="filter.zrAppealState" clearable placeholder="申诉状态" style="width: 160px">
          <el-option v-for="item in zrAppealStateList" :key="item.name" :label="item.name" :value="item.value" />
        </el-select>
        <span style="font-size: 12px;" v-show="revealing">损耗订单比:发货订单数></span>
        <el-input-number v-model="filter.sendOrderCount" v-show="revealing" controls-position="right" style="width: 70px;" :controls="false" :min="500" :max="999999999"></el-input-number>
        <span style="font-size: 12px;" v-show="revealing">且损耗订单比≥</span>
        <el-input-number v-model="filter.damagedOrderRatio" v-show="revealing" controls-position="right" style="width: 70px;" :controls="false" :min="0.0001" :max="1" :precision="4"></el-input-number>
        <span v-show="revealing" style="margin-right: 5px;">%</span>

        <el-button class="marginleft" type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>

        <el-button type="primary" @click="daoruFile('bf', '补发订单导入')">补发订单导入</el-button>

        <el-button type="primary" @click="daoruFile('hb', '红包补偿订单导入')">红包补偿订单导入</el-button>
        <el-button type="primary" @click="daoruFile('qt', '其他订单导入')">其他订单导入</el-button>
        <el-dropdown style="margin-left: 10px;" @command="downLoadType">
          <el-button type="primary">
            导入模版下载<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="bf">补发订单模版下载</el-dropdown-item>
            <el-dropdown-item command="hb">红包补偿订单模版下载</el-dropdown-item>
            <el-dropdown-item command="qt">其他订单模版下载</el-dropdown-item>
            <el-dropdown-item command="rpa">RPA导入下载</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

        <el-button type="primary" style="margin-left: 10px;" @click="daoruFile('rpa', 'RPA导入')">RPA导入</el-button>
      </el-row>


    </template>


    <template>
      <vxetablebase :id="'lossDataStatisticsOne202408041801_1'" :tableData='list' :tableCols='tableCols'
        :loading='listLoading' :border='true' @summaryClick='onsummaryClick' :treeProp="treeProp" :that="that"
        ref="vxetable" @sortchange='sortchange' :showsummary='true' :summaryarry='summaryarry'>
        <!-- <template slot="right">
            <vxe-column title="操作" :field="'col_opratorcol'" width="180" fixed="right">
                <template #default="{ row }">
                    <template>
                        <el-button type="text" @click="onSetInfo(row, true)">同意</el-button>
                        <el-button type="text" @click="onSetInfo(row, false)">拒绝</el-button>
                    </template>
                </template>
            </vxe-column>
        </template> -->
      </vxetablebase>
    </template>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <el-dialog :title="digtitle" :visible.sync="dialogVisible" width="30%" v-dialogDrag>
      <span>
        <el-upload ref="upload" class="upload-demo" v-if="dialogVisible" :auto-upload="false" :multiple="false" :limit="1"
          action accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :file-list="fileList">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="chartdialog" width="1000px" :close-on-click-modal="true"
      element-loading-text="拼命加载中" v-dialogDrag :append-to-body="true">
      <div style="height: 610px;margin-top: 10px;" v-loading="!chartsdata1">
      <span style="font-size: 15px; margin-right: 10px;font-size: 12px;">发货时间: </span>
      <el-date-picker style="width: 250px" v-model="filter.charttimerange" type="daterange" format="yyyy-MM-dd"
        @change="changedatee" value-format="yyyy-MM-dd" :picker-options="pickerOptions" range-separator="至"
        start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false">
      </el-date-picker>
      <buschar :charid="'charNegative1'" :analysisData="chartsdata1" v-if="chartsdata1"></buschar>
      </div>

    </el-dialog>

    <el-dialog title="查看定责图片" :visible.sync="viewImagesDialogVisiable" width="70%" :close-on-click-modal="true"
      v-dialogDrag :before-close="handleClose">
      <!-- class="father" -->
      <div class="viewImageBox father">
        <div class="viewImageBox_item" v-for="(item, i) in viewImgList">
          <el-image v-if="item.damagedZrFileUrls" class="viewImageBox_item_img"
            :src="item.damagedZrFileUrls ? item.damagedZrFileUrls[0] : ''" @click="deliverImg(item, i)">
          </el-image>
          <el-image v-else>
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
          <div class="viewImageBox_item_bottom">
            <div class="viewImageBox_item_info">
              <div class="viewImageBox_item_info_left">责任人</div>
              <div class="viewImageBox_item_info_right">{{ item.memberName ? item.memberName : '无' }}</div>
            </div>
            <div class="viewImageBox_item_info">
              <div class="viewImageBox_item_info_left">损耗部门</div>
              <div class="viewImageBox_item_info_right">{{ item.zrDepartment ? item.zrDepartment : '无' }}</div>
            </div>
          </div>
          <div class="viewImageBox_item_fixed">{{ item.damagedZrFileUrlCount }}</div>
        </div>
        <!-- <div class="imageModalPos" v-if="imageModelVisiable">
          <imageModel :index="index" :maxIndex="maxIndex" :imgList="imgList" :imageInfo="imageInfo"
            :closeModal="closeModal" ref="imageModelRef" />
        </div> -->
        <div class="imageModalPos" v-if="imageModelVisiable">
          <imageModel :index="index" :domIndex="domIndex" :maxIndex="maxIndex" @childGetImgList="childGetImgList"
            v-if="imgList" :imgList="imgList" :imageInfo="imageInfo" :closeModal="closeModal" ref="imageModelRef"
            :checkList="filter.groupTypes" />
        </div>
      </div>
      <my-pagination :sizes="[8, 16, 24]" :page-size="8" ref="imgPager" :total="imgTotal"
        @page-change="ImgViewPagechange" @size-change="ImgViewSizechange" style="margin-top: 40px;" />
    </el-dialog>


    <el-dialog :visible.sync="circhartdialog" width="1500px" height="400px" :close-on-click-modal="true" v-loading="chartsdata3loading"
      element-loading-text="拼命加载中" v-dialogDrag :append-to-body="true">
      <div style="width: 100%; font-weight: 600;" class="centerflex">责任部门数据1</div>
      <div style="display: flex; flex-direction: row; width: 100%; height: 100%; box-sizing: border-box;">
        <div style="flex: 1; height: 100%;">

          <div style="display: flex; flex-direction: row;margin-bottom: 10px;">
            <span style="font-size: 15px; margin-right: 10px;font-size: 12px;">时间: </span>
            <!--  type="datetimerange" -->
            <el-date-picker style="width: 200px" v-model="filter.charttimerange"  type="daterange" format="yyyy-MM-dd"
              @change="canclick(null)" value-format="yyyy-MM-dd" :picker-options="pickerOptions" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false">
            </el-date-picker>
          </div>
          <vxetablebase :toolbarshow="false" :id="'YunHanAdminGoodsFinishedpart2023070221'" somerow="styleCode"  height="300px" :tableData='list1' :tableCols='tableCols1'
            :loading='listLoading' :border='true'  :that="that"
            ref="vxetable" @sortchange='sortchange1' :showsummary='true' :summaryarry='summaryarry1'>
          </vxetablebase>

        </div>
        <div style="flex: 1;height: 100%;height: 390px; justify-content: center; align-items: center;">
          <div style="display: flex; flex-direction: row; width: 100%; justify-content: center; align-items: center;">
            <el-radio v-model="oneradio" label="1" @change="changepie('1')">损耗订单数</el-radio>
            <el-radio v-model="oneradio" label="2" @change="changepie('2')">损耗订单比</el-radio>
          </div>
          <buscharpie :charid="'charNegative1'" :thisStyle="thisStyle" :isshowbai="oneradio == '2' ? true : false" :gridStyle="gridStyle" :analysisData="chartsdata3" v-if="chartsdata3"></buscharpie>
        </div>
      </div>

    </el-dialog>

    <el-dialog :visible.sync="reasonchartdialog" width="1500px" :close-on-click-modal="true"
      element-loading-text="拼命加载中" v-dialogDrag :append-to-body="true">
      <div style="width: 100%; font-weight: 600;" class="centerflex">损耗具体原因</div>
      <div style="display: flex; flex-direction: row; width: 100%; height: 100%;" v-loading="chartsdata4loading">
        <div style="flex: 1;  height: 100%;">

          <div style="display: flex; flex-direction: row;margin-bottom: 10px;">
            <span style="font-size: 15px; margin-right: 10px;font-size: 12px;">时间: </span>
            <el-date-picker style="width: 200px" v-model="filter.charttimerangetwo"  type="daterange" format="yyyy-MM-dd"
              @change="diagclick(null)" value-format="yyyy-MM-dd" :picker-options="pickerOptions" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false">
            </el-date-picker>
          </div>
          <vxetablebase :toolbarshow="false" :id="'YunHanAdminGoodsFinishedpart2023073301'" somerow="styleCode"  height="350px" :tableData='list2' :tableCols='tableCols2'
            :loading='listLoading' :border='true'  :that="that"
            ref="vxetable" :showsummary='true' :summaryarry='summaryarry2'>
          </vxetablebase>

        </div>
        <div style="flex: 1;height: 100%;height: 390px; justify-content: center; align-items: center;">
          <div style="display: flex; flex-direction: row; width: 100%; justify-content: center; align-items: center;">
            <!-- <el-radio v-model="radio" label="1">损耗原因占比</el-radio> -->
            损耗原因占比
          </div>
          <buscharpie :charid="'charNegative1'" :thisStyle="thisStyle" :isshowbai="true" :gridStyle="gridStyle" :analysisData="chartsdata4" v-if="chartsdata4"></buscharpie>
        </div>
      </div>

    </el-dialog>

    <el-dialog :visible.sync="statisticalDimension" width='18%' :close-on-click-modal="false" v-dialogDrag>
      <el-scrollbar style="height: 350px;border: 1px solid #e7e7e7;margin-top: 10px;">
        <div style="height: 100%;display: flex;align-items: center;padding: 5% 10%;">
          <el-checkbox-group v-model="groupTages" style="line-height: 30px;" v-if="wdlist">
            <el-checkbox v-for="item in wdlist" :key="item.value" :disabled="(item.checked && selnum == 1) || item.value == 4"
              :label="item.value" :value="item.value" :checked="item.checked" style="display: block; text-align: left;">{{ item.label }}</el-checkbox>
          </el-checkbox-group>
        </div>
      </el-scrollbar>
      <div style="margin-top: 5%; display: flex; justify-content: space-between; align-items: center;">
        <el-checkbox v-model="selectAll" @change="onAllcheckbox">全选</el-checkbox>
        <div>
          <el-button @click="statisticalDimension = false">取 消</el-button>
          <el-button type="primary" @click="onDimension(1)">确 定</el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="serialCoding" width="1400px" :close-on-click-modal="true"
      element-loading-text="拼命加载中" v-dialogDrag :append-to-body="true" append-to-body>
      <div style="width: 100%; font-weight: 600;" class="centerflex">损耗具体原因</div>
      <div style="display: flex; flex-direction: row; width: 100%; height: 100%;" v-loading="serialCodingloading">
        <div style="flex: 1;  height: 100%;">
          <div style="display: flex; flex-direction: row;margin-bottom: 10px;">
            <span style="font-size: 15px; margin-right: 10px;display: flex;align-items: center;">时间: </span>
            <el-date-picker style="width: 200px" v-model="serialchart.serialcharttimerange"  type="daterange" format="yyyy-MM-dd"
              @change="onCommodityCode('search')" value-format="yyyy-MM-dd" :picker-options="pickerOptions" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false">
            </el-date-picker>
          </div>
          <vxetablebase :toolbarshow="false" :id="'YunHanAdminGoodsFinishedpart2023073301'" somerow="styleCode"  height="350px" :tableData='seriallist' :tableCols='tableCols3'
            :loading='seriallistLoading' :border='true' :that="that" ref="serialvxetable" :showsummary='true' :summaryarry='serialsummaryarry'>
          </vxetablebase>
        </div>
        <div style="flex: 1;height: 100%;height: 390px; flex: 1;justify-content: center; align-items: center;">
          <div style="display: flex; flex-direction: row; width: 100%; justify-content: center; align-items: center;">
            损耗原因占比
          </div>
          <!-- <buscharpie :charid="'charNegative1'" :thisStyle="thisStyle" :isshowbai="true" :gridStyle="gridStyle" :analysisData="serialchartsdata" v-if="serialchartsdata"></buscharpie> -->
          <buscharpie :charid="'charNegative1'" :thisStyle="thisStyle" :isshowbai="true" :piesuspension="true" :gridStyle="gridStyle" :analysisData="serialchartsdata" v-if="causeLoss"></buscharpie>
        </div>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="confirmation" width='60%' v-dialogDrag>
      <div style="height: 450px;">
        <conditionConfirmation ref="refconditionConfirmation" :conditionData="conditionData" :query="query" v-if="confirmation"/>
      </div>
    </el-dialog>

    <el-dialog title="操作记录" :visible.sync="operationRecord" width='60%' v-dialogDrag>
      <div style="height: 450px;">
        <operationRecording ref="refoperationRecording" :recordData="recordData" v-if="operationRecord"/>
      </div>
    </el-dialog>

  </MyContainer>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import buschar from "../components/buschar.vue"
import buscharpie from "./piebus.vue"
import conditionConfirmation from "./conditionConfirmation.vue"
import operationRecording from "./operationRecording.vue"
import imageModel from "../components/viewImages.vue"
import { getAllWarehouse } from '@/api/inventory/warehouse';
import { getTime } from '@/utils/getCols.js'
import { formatTime, formatPlatform, pickerOptions } from "@/utils/tools";
import middlevue from '@/store/middle.js'
import { getUserInfo } from '@/api/operatemanage/productalllink/alllink'
import dayjs from 'dayjs';
import {getUserListPage} from '@/api/admin/user'
import { getAllListInCalc as getAllShopList, getList as getshopListt } from '@/api/operatemanage/base/shop';
import {
  getDamagedOrdersStatisAsync, importDamagedOrdersBF, importDamagedOrdersHB, importDamagedOrdersQT,
  getDamagedOrdersStatisAnlysisAsync, getDamagedOrdersZrDept, getDamagedOrdersZrType, exportDamagedOrdersStatisAsync, getDamagedOrdersStatisRowPageAsync, GetDamagedOrdersStatisRowPageDtlAsync,
  getDamagedOrdersRowDetailStatisListAsync, importDamagedOrdersRPA, getDamagedOrdersGoodsDetailStatisListAsync
} from '@/api/customerservice/DamagedOrders'
import { set } from "lodash";

const hearlist = [
  { label: '发货日期', value: 1, filed: 'sendGoodsDate', checked: false },
  { label: '发货仓', value: 2, filed: 'sendWareHouse', checked: false },
  { label: '平台', value: 3, filed: 'platform', checked: false },
  { label: '系列编码', value: 4, filed: 'styleCode', checked: true },
  { label: '商品编码', value: 5, filed: 'goodsCode', checked: false },
  { label: '损耗部门', value: 6, filed: 'zrDepartment', checked: false },
  { label: '损耗类型', value: 7, filed: 'zrType2', checked: false },
  { label: '处理方式', value: 8, filed: 'damagedHandType', checked: false },
  { label: '责任人', value: 9, filed: 'memberName', checked: false },
  { label: '责任上级', value: 10, filed: 'managerMemberName', checked: false },
];
const tableCols = [
  // { treeNode:true, istrue: true, prop: 'label', label: '权限', minwidth: '220',align:'left'},
  // { istrue: true, prop: 'id', label: '编号', width: '100',sortable:true},
  { istrue: true, prop: 'sendGoodsDate', label: '发货日期', width: '80', sortable: 'custom', formatter: (row) => { if (row.sendGoodsDate) { return row.sendGoodsDate.slice(0, 10) } else { return '' } } },
  { istrue: true, prop: 'sendWareHouse', label: '发货仓', width: '80', sortable: 'custom', },
  { istrue: true, prop: 'platform', label: '平台', width: '80', formatter: (row) => row.platform === 0 ? '' : formatPlatform(row.platform), sortable: 'custom', },
  { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: '80', type: "click", handle: (that, row, column, cell) => that.canclick(row, column, cell) },
  { istrue: true, prop: 'goodsPic', label: '商品图片', width: '80', type: "images" },
  // {
  //   istrue: true, label: '定责图片', width: '80', type: "button", btnList: [
  //     { prop: 'platform',formatter: (row) => row.damagedZrFileUrlCount + '张', },
  //     { label: '查看', handle: (that, row) => that.viewImages(row) }
  //   ]
  // },
  { istrue: true, prop: 'damagedZrFileUrlCount', label: '定责图片', width: '80', formatter: (row) => row.damagedZrFileUrlCount + '张' + '  ' + '查看', type: 'click', handle: (that, row) => that.viewImages(row) },
  { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'zrDepartment', label: '损耗部门', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'zrType2', label: '损耗类型', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'memberName', label: '责任人', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'managerMemberName', label: '责任上级', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'damagedHandType', label: '处理方式', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'sendOrderCount', label: '发货订单数', sortable: 'custom', width: '80', summaryEvent: true },
  { istrue: true, prop: 'damagedOrderCount', label: '损耗订单数', sortable: 'custom', width: '80', summaryEvent: true, type: 'click', handle: (that, row) => that.linkToZr(row) },
  { istrue: true, prop: 'damagedGoodsCount', label: '损耗商品数', sortable: 'custom', width: '80', summaryEvent: true },
  { istrue: true, prop: 'damagedOrderRatio', label: '损耗订单比', sortable: 'custom', width: '80', summaryEvent: true, formatter: (row) => { return row.damagedOrderRatio + "%" } },

  { istrue: true, prop: 'damagedPayAmount', label: '打款金额', sortable: 'custom', width: '80', summaryEvent: true },
  { istrue: true, prop: 'goodsCostPrice', label: '商品单价', sortable: 'custom', width: '80', summaryEvent: true },
  { istrue: true, prop: 'expressAmonut', label: '商品快递费', sortable: 'custom', width: '80', summaryEvent: true },
  { istrue: true, prop: 'damagedAmount', label: '损耗总费用', sortable: 'custom', width: '80', summaryEvent: true },
  // { istrue: true, prop: 'createdTime', label: '数据导入时间', sortable: 'custom', width: '80',},
  // { istrue: true, prop: 'sendWareHouseId', label: 'id',sortable: 'custom', width: 'auto', summaryEvent: true },
  // { istrue: true, prop: 'qusitu', label: '趋势图', width: '80' },
  {
    istrue: true, label: '操作', width: '180', fixed: 'right', type: 'button', btnList: [
      { label: '情况确认', handle: (that, row) => that.onConfirmation(row) },
      {
        label: '趋势图',
        // permission: 'api:order:orderdeductmoney:SetZrMemberCustomize',
        handle: (that, row) => that.getrowqst(row)
        // ishide: (that, row) => { return  row.zrAppealState ==1  ; }
      },
      { label: '操作记录', handle: (that, row) => that.onRecord(row) }
    ]
  }
  // { istrue: true, prop: 'sort', label: '最后计算时间', width: '80' },


];

const tableCols1 = [
  {istrue: true, label: '系列编码', prop: 'styleCode', width: '95', },
  {istrue: true, label: '商品编码', prop: 'goodsCode', width: '95', type: "click", handle: (that, row, column, cell) => that.onCommodityCode(row) },
  {istrue: true, label: '责任部门', prop: 'zrDepartment', width: '95', type: "click", handle: (that, row, column, cell) => that.diagclick(row) },
  {istrue: true, label: '损耗订单数', prop: 'damagedOrderCount', width: '95', },
  {istrue: true, label: '损耗订单比', prop: 'damagedOrderDetailRatio', width: '95', formatter: (row) => { return row.damagedOrderDetailRatio.toFixed(2) + "%" } },
  {istrue: true, label: '损耗总费用', prop: 'damagedAmount', width: 'auto', },
];
const tableCols2 = [
  {istrue: true, label: '系列编码', prop: 'styleCode', width: '120', },
  {istrue: true, label: '损耗原因', prop: 'zrType2', width: '120', type: "click", handle: (that, row, column, cell) => that.neidiagclick(row) },
  {istrue: true, label: '损耗原因数量', prop: 'damagedOrderCount', width: '120', },
  {istrue: true, label: '损耗原因占比', prop: 'damagedOrderDetailRatio', width: 'auto', formatter: (row) => { return row.damagedOrderDetailRatio.toFixed(2) + "%" } },
];
const tableCols3 = [
  {istrue: true, label: '商品编码', prop: 'goodsCode', width: '100', },
  {istrue: true, label: '损耗原因', prop: 'damagedReason', width: '145', },
  {istrue: true, label: '责任部门', prop: 'zrDepartment', width: '100', },
  {istrue: true, label: '组长', prop: 'managerMemberName', width: '100', },
  {istrue: true, label: '损耗原因订单数', prop: 'damagedOrderCount', width: '100', },
  {istrue: true, label: '损耗原因占比', prop: 'damagedOrderRatio', width: '100', },
];
const tableHandles = [
  //{ label: "导入", handle: (that) => that.startImport() },
];
export default {
  name: 'Vue2demoLossDataStatisticsOne',
  components: { vxetablebase, MyContainer, buschar, imageModel, buscharpie, conditionConfirmation, operationRecording },
  data() {
    return {
      recordData: {},
      operationRecord: false,
      conditionData: {},
      query:{},
      serialchartsdata: null,
      causeLoss: false,//显隐趋势图
      confirmation: false,
      seriallist: [],
      serialsummaryarry:{},
      seriallistLoading:false,
      serialchart: {
        serialcharttimerange:[],
        styleCode: null,
        goodsCode: null,
        zrDepartment: null,
      },
      serialCodingloading:false,
      tableCols3,
      serialCoding: false,
      groupTages: [],
      importStartTime: null,
      importEndTime: null,
      importTime: [],
      selectAll: false,
      statisticalDimension: false,
      selnum: 0,
      digtitle: '',
      editLoading: true,
      circhartdialog: false,
      reasonchartdialog: false,
      oneradio: '1',
      list2: [],
      tableCols2: tableCols2,
      tableCols1: tableCols1,
      chartsdata2: {},
      summaryarry1: {},
      summaryarry2: {},
      chartsdata3: null,
      chartsdata4: null,
      tworow: {},
      onerow: {},
      thisStyle: {
          width: '100%', height: '390px', 'box-sizing': 'border-box', 'line-height': '360px'
      },
      gridStyle: {
          top: '0',
          left: '5%',
          right: '15%',
          bottom: '0',
          containLabel: false
      },
      treeProp: null,
      chartsdata4loading: false,
      chartsdata3loading: false,
      shopList: [],
      list1: [],
      filter: {
        isAsc: false,
        orderBy: '',
        goodsCode: null,
        zrDepartment: null,
        zrType2: null,
        damagedHandType: null,
        zrAppealState: null,
        managerMemberName: null,
        memberName: null,
        expressOrder: null,
        platform: null,
        shopCode: null,
        orderNo: null,
        orderInnerNo: null,
        styleCode: null,
        groupTypes: [],
        timerange: [],
        charttimerange: [],
        charttimerangetwo: [],
        AfterSaleApproveDate: [],
        ZrSetDate: [],
        startAfterSaleApproveDate: null,
        endAfterSaleApproveDate: null,
        startSendGoodsDate: null,
        endSendGoodsDate: null,
        startZrSetDate: null,
        endZrSetDate: null,
        managerMemberName: null,
        damagedOrderRatio: undefined,
        sendOrderCount: undefined,
      },
      zrAppealStateList: [{ name: '申诉已拒绝', value: -1 }, { name: '未申诉', value: 0 }, { name: '申诉中', value: 1 }, { name: '申诉已通过', value: 2 },
      { name: '已指派', value: 3 },],
      pickerOptions: pickerOptions,
      summaryarry: {},
      damagedList: [],
      damagedList2: [],
      chartdialog: false,
      chartsdata1: null,
      dialogVisible: false,
      fileList: [],
      rowlist: {},
      that: this,
      total: 0,
      sels: [],
      newWareHouseList: [],
      permissionTree: [],
      listLoading: false,
      tableCols: tableCols,
      list: [],
      filetype: '',
      url: '',
      damagedHandList: [
        { name: '补发', value: '补发' },
        { name: '红包补偿', value: '红包补偿' },
        { name: '订单部分补偿', value: '订单部分补偿' },
        { name: '订单全额退款', value: '订单全额退款' },
        { name: '订单退货退款', value: '订单退货退款' },
        { name: '订单退货补发', value: '订单退货补发' },

      ],
      wdlist: null,
      userList:[],
      revealing: false,
      viewImagesDialogVisiable: false,
      index: 0,
      maxIndex: 0,
      imageModelVisiable: false,
      imgList: null,
      imageInfo: {},
      maxIndex: 0,
      viewImgList: [],
      timerangeList: [],
      imgTotal: 0,
      imgPageInfo: {
        currentPage: 1,
        pageSize: 8,
        orderBy: 'zrDepartment',
        isAsc: false
      },
      row: null,
      queryInfo: null,
      domIndex: 0,
      platformList: [{ label: '天猫', value: 1 }, { label: '拼多多', value: 2 }, { label: '阿里巴巴', value: 4 },
      { label: '抖音', value: 6 }, { label: '京东', value: 7 }, { label: '淘工厂', value: 8 }, { label: '淘宝', value: 9 }, { label: '苏宁', value: 10 },],
    };
  },

  async mounted() {

    this.wdlist = null;
    var res = await getAllWarehouse();
    this.newWareHouseList = res.data.filter((x) => { return x.name.indexOf('代发') < 0; });


    let end = new Date();
    let start = new Date();
    start.setDate(start.getDate() - 30);
    this.filter.timerange = [formatTime(start, "YYYY-MM-DD"), formatTime(end, "YYYY-MM-DD")]
    this.filter.charttimerange = [formatTime(start, "YYYY-MM-DD"), formatTime(end, "YYYY-MM-DD")]

    this.timerangeList = [dayjs().subtract(30, 'day').format('YYYY-MM-DD'),dayjs().format('YYYY-MM-DD')]
    localStorage.removeItem('tjweidu');
    await this.getZrDept();
    // this.getZrType();
    if (JSON.parse(localStorage.getItem('tjweidu'))) {
      this.wdlist = hearlist;
      await this.loaclarr();
    } else {
      this.wdlist = hearlist;
    }
    localStorage.removeItem('tjweidu');
    // if (JSON.parse(localStorage.getItem('tjweidu'))) {
    //   this.wdlist = hearlist;
    //   await this.loaclarr();

    // } else {
    //   this.wdlist = hearlist;
    //   let newarrimg = [];
    //   this.$refs.vxetable.$refs.xTable.resetColumn();
    //   hearlist.map((item) => {
    //     if (!item.checked) {
    //       this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField(item.filed))
    //     } else {
    //       newarrimg.push(item.filed)
    //     }
    //   })
    //   if (newarrimg.indexOf('goodsCode') == -1 && newarrimg.indexOf('styleCode') == -1) {
    //     this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('goodsPic'))
    //   }
    // }
    this.wdselchange();
    this.onDimension()
    await this.dataDimension()
    await this.onSearch();
  },
  //  watch: {
  //   wdlist:{
  //     handler(newVal, oldVal) {
  //       let _this = this;
  //       _this.selnum = this.arrlength(newVal);
  //       console.log("打印数据_this.selnum",_this.selnum)
  //     },
  //     deep: true
  //   }
  //  },


  methods: {
    async onDimension(val){
      this.statisticalDimension = false
      this.filter.groupTypes = this.groupTages
      this.serialCodingMethod(1)
      if(val == 1){
        await this.onSearch();
      }
    },
    serialCodingMethod(val){
      this.revealing = this.filter.groupTypes.some(item => item == 4);
      if (this.revealing) {
        if(val == 1){
        this.filter.sendOrderCount = !this.filter.sendOrderCount ? undefined : this.filter.sendOrderCount;
        this.filter.damagedOrderRatio = !this.filter.damagedOrderRatio ? undefined : this.filter.damagedOrderRatio;
        } else {
          this.filter.sendOrderCount = 500;
          this.filter.damagedOrderRatio = 1;
        }
      } else {
        this.filter.sendOrderCount = undefined;
        this.filter.damagedOrderRatio = undefined;
      }
    },
    async onCommodityCode(row){
      if(row != 'search'){
        this.serialchart.goodsCode = row.goodsCode
        this.serialchart.styleCode = row.styleCode
        this.serialchart.zrDepartment = row.zrDepartment
        var [start, end] = this.filter.charttimerange ? this.filter.charttimerange : this.filter.timerange ? this.filter.timerange : [null, null];
        this.serialchart.serialcharttimerange = [start, end]
      } else {
        var [start, end] = this.serialchart.serialcharttimerange ? this.serialchart.serialcharttimerange : this.filter.charttimerange ? this.filter.charttimerange : this.filter.timerange ? this.filter.timerange : [null, null];
      }
      if (typeof row === 'object' && row !== null) {
        var { goodsCode, styleCode, zrDepartment } = row;
      } else if (row === 'search') {
        var { goodsCode, styleCode, zrDepartment } = this.serialchart;
      }
      const params = { goodsCode, styleCode, startSendGoodsDate: start, endSendGoodsDate: end, zrDepartment };
      this.serialCodingloading = true;
      const {data,success} = await getDamagedOrdersGoodsDetailStatisListAsync(params)
      this.serialCodingloading = false;
      if(success){
        this.seriallist = data.list
        this.serialsummaryarry = data.summary
        let chatlist = []
        this.seriallist.map((item) => {
          chatlist.push({
            name: item.damagedReason,
            value: item.damagedOrderRatio ? parseFloat(item.damagedOrderRatio.replace('%', '')).toFixed(2) : 0
          });
        });

        this.$nextTick(() => {
          this.causeLoss = false
          if (this.seriallist.length == 0) {
            this.serialchartsdata = {};
          } else {
          this.serialchartsdata = {
            title: {
              left: 'center',
              top: 'center'
            },
            series: [
              {
                label: {
                  alignTo: 'edge',
                  formatter: '{b}\n{c} %',
                  minMargin: 5,
                  edgeDistance: 10,
                  lineHeight: 15,
                  rich: {
                    time: {
                      fontSize: 10,
                      color: '#999'
                    }
                  }
                },
                type: 'pie',
                data: chatlist,
                radius: ['40%', '70%']
              }
            ]
          };
        }
          this.$nextTick(()=>{
            this.serialCoding = true
            this.causeLoss = true
          })
        })
      }
    },
    onRecord(row){
      let [startTime, endTime] = this.filter.timerange ? this.filter.timerange : [null, null];
      this.recordData = { ...row, startTime, endTime };
      this.operationRecord = true
    },
    onConfirmation(row){
      const pager = this.$refs.pager.getPager()
      this.conditionData = { ...row}
      this.query = {...pager, ...this.filter,styleCode:row.styleCode,sendWareHouseId:row.sendWareHouseId,managerMemberName:row.managerMemberName}
      this.confirmation = true
    },
    async dataDimension(){
      this.filter.groupTypes = []
      this.filter.sendOrderCount = undefined
      this.filter.damagedOrderRatio = undefined
      const {data,success} = await getUserInfo()
      const params = {
        pageSize: 50,
        currentPage: 1,
        dynamicFilter: {field:'nickName',operator:'Equal',value:data.nickName},
        filter: null,
        currentDeptCode: null,
        currentCorpId: null,
        isContainChildDeptUser: true
      }
      const {data:data1,success:success1} = await getUserListPage(params)
      this.userList = data1.list
      this.userList.filter(item => item.id == data.id).map(item => {
        if(item.roleNames.includes('超级管理员') || item.roleNames.includes('系统管理员')){
          this.filter.groupTypes = [4]
          this.filter.sendOrderCount = 500
          this.filter.damagedOrderRatio = 1
        }else{
          if((data.fullName.includes('仓'))||((item.title.includes('仓'))||(item.roleNames.includes('仓')))) {
            this.filter.zrDepartment = '仓库'
            this.filter.groupTypes = [2,4,6]
            this.filter.sendOrderCount = 500
            this.filter.damagedOrderRatio = 1
          } else if((data.fullName.includes('运营'))||((item.title.includes('运营'))||(item.roleNames.includes('运营')))) {
            this.filter.zrDepartment = '运营'
            this.filter.sendOrderCount = 500
            this.filter.damagedOrderRatio = 1
            this.filter.groupTypes = [3,4,5,6]
          } else if((data.fullName.includes('客服'))||((item.title.includes('客服'))||(item.roleNames.includes('客服')))){
            this.filter.zrDepartment = '客服'
            this.filter.groupTypes = [3,4,5,6]
            this.filter.sendOrderCount = 500
            this.filter.damagedOrderRatio = 1
          } else {
            //若不是以上部门，则默认为其他
            this.filter.groupTypes = [4]
            this.filter.sendOrderCount = 500
            this.filter.damagedOrderRatio = 1
          }
        }
      })
      this.serialCodingMethod()
    },
    closeIt() {
        this.$refs.selectIt.blur();
        this.$nextTick(() => {
          this.onStatistics()
        });
    },
    shopIncident(e) {
      if (e && e == true && !this.filter.zrDepartment) {
        this.$message.warning('请先选择损耗部门')
      }
    },
    changeTime(e){
      this.filter.importStartTime = e ? e[0] : null
      this.filter.importEndTime = e ? e[1] : null
    },
    onAllcheckbox() {
      if (this.selectAll) {
        this.groupTages = this.wdlist.map(item => item.value);
      } else {
        this.groupTages = [4];
      }
    },
    onStatistics(){
      this.statisticalDimension = true;
      this.$nextTick(() => {
        setTimeout(() => {
          this.selectAll = this.wdlist.every(item => this.filter.groupTypes.includes(item.value));
          this.groupTages = this.filter.groupTypes;
          if(this.groupTages.indexOf(4) == -1){
            this.groupTages.push(4)
          }
        }, 0);
      });
    },
    neidiagclick(row){
      row.SendGoodsDate = this.filter.charttimerangetwo;
      this.$emit('changeTab', 'second');
      setTimeout(()=>{
        middlevue.$emit('shReasonGetList', row)
        this.reasonchartdialog = false;
        this.circhartdialog = false;
      }, 300)

    },
    async diagclick(row){
      if(row && row.zrDepartment == '其他'){
        row.SendGoodsDate = this.filter.charttimerangetwo;
        this.$emit('changeTab', 'second');
        setTimeout(()=>{
          middlevue.$emit('shReasonGetList', row)
          this.reasonchartdialog = false;
          this.circhartdialog = false;
        }, 300)
        return
      }
      this.chartsdata4loading = true;
      if(!row){
        row = this.tworow;
      }else{
        this.tworow = row;
        this.filter.charttimerangetwo = this.filter.timerange;
      }

      this.reasonchartdialog = true;

      let params = {
        'groupTypes': [4, 6, 7],
        'styleCode': row.styleCode,
        'zrDepartment': row.zrDepartment,
        'goodsCode': row.goodsCode,
        'startSendGoodsDate': this.filter.charttimerangetwo ? this.filter.charttimerangetwo[0] : null,
        'endSendGoodsDate': this.filter.charttimerangetwo ? this.filter.charttimerangetwo[1] : null,
      };
      let res = await getDamagedOrdersRowDetailStatisListAsync(params);
      if(!res.success){
        this.chartsdata4loading = false;
        return
      }
      res.data.list.map((item)=>{
        item.damagedOrderDetailRatio = item.damagedOrderDetailRatio * 100;
      })

      this.summaryarry2 = res.data.summary;
      this.list2 = res.data.list;

      this.$nextTick(() => {
        this.changepie('3');
      });

    },
    async canclick(row){
      this.chartsdata3loading = true;
      this.oneradio = '1';
      if(!row){
        row = this.onerow;
      }else{
        this.onerow = row;
        this.filter.charttimerange = this.filter.timerange;
      }
      this.circhartdialog = true;

      let params = {
        'groupTypes': [4,6],
        'styleCode': row.styleCode,
        'goodsCode': row.goodsCode,
        'startSendGoodsDate': this.filter.charttimerange ? this.filter.charttimerange[0] : null,
        'endSendGoodsDate': this.filter.charttimerange ? this.filter.charttimerange[1] : null,
      };
      let res = await getDamagedOrdersRowDetailStatisListAsync(params);

      if(!res.success){
        this.chartsdata3loading = false;
        return
      }
      this.summaryarry1 = res.data.summary;
      res.data.list.map((item)=>{
        item.damagedOrderDetailRatio = item.damagedOrderDetailRatio * 100;
      })
      this.list1 = res.data.list;

      this.$nextTick(() => {
        this.changepie('1');
      });

    },
    changepie(val){
      let chatlist = [];
      if(val == '1'){
        this.chartsdata3 = null;
        this.list1.map((item)=>{
          chatlist.push({
            name: item.zrDepartment,
            value: item.damagedOrderCount
          })
        })
      }else if(val == '2'){
        this.chartsdata3 = null;
        this.list1.map((item)=>{
          chatlist.push({
            name: item.zrDepartment,
            value: item.damagedOrderDetailRatio
          })
        })
        chatlist.map((item)=>{
          item.value = item.value.toFixed(2);
        })
      }else if(val == '3'){
        this.chartsdata4 = null;
        this.list2.map((item)=>{
          chatlist.push({
            name: item.zrType2,
            value: item.damagedOrderDetailRatio
          })
        })
        chatlist.map((item)=>{
          item.value = item.value.toFixed(2);
        })

        this.$nextTick(() => {
        this.chartsdata4 = {
            title: {
              left: 'center',
              top: 'center'
            },
            series: [
              {
                type: 'pie',
                data: chatlist,
                radius: ['40%', '70%']
              }
            ],
          };
        })
        this.chartsdata4loading = false;

        return;
      }

      this.$nextTick(() => {

        this.chartsdata3 = {
          title: {
            left: 'center',
            top: 'center'
          },
          series: [
            {
              type: 'pie',
              data: chatlist,
              radius: ['40%', '70%']
            }
          ]
        };
      })
      console.log(this.chartsdata3,'this.chartsdata3');
      console.log(this.chartsdata4,'this.chartsdata4');
      this.chartsdata3loading = false;
      this.chartsdata3loading = false;
    },
    linkToZr(row) {
      this.$emit('changeTab', 'second')
      let params = {}
      if(this.filter.groupTypes.includes(1)){
        params.sendGoodsDate = row.sendGoodsDate
        params.sendGoodsDateIsNull = false
      }else{
        params.sendGoodsDateIsNull = true
        params.sendGoodsDate = this.filter.timerange
      }
      if(this.filter.groupTypes.includes(2)){
        params.sendWareHouse = row.sendWareHouse
      }
      if(this.filter.groupTypes.includes(3)){
        params.platform = row.platform
      }
      if(this.filter.groupTypes.includes(4)){
        params.styleCode = row.styleCode
      }
      if(this.filter.groupTypes.includes(5)){
        params.goodsCode = row.goodsCode
      }
      if(this.filter.groupTypes.includes(6)){
        params.zrDepartment = row.zrDepartment
      }
      if(this.filter.groupTypes.includes(7)){
        params.zrType2 = row.zrType2
      }
      if(this.filter.groupTypes.includes(8)){
        params.damagedHandType = row.damagedHandType
      }
      if(this.filter.groupTypes.includes(9)){
        params.memberName = row.memberName
      }
      params.sendGoodsDate = params.sendGoodsDate ? params.sendGoodsDate : this.timerangeList
      setTimeout(() => {
        middlevue.$emit('zrGetList', params)
      }, 1000);
    },
    downLoadType(type) {
      if (type == 'bf') {
        window.open("/static/excel/order/补发订单模板.xlsx", "_blank");
      } else if (type == 'hb') {
        window.open("/static/excel/order/红包补偿订单模板.xlsx", "_blank");
      } else if (type == 'qt') {
        window.open("/static/excel/order/其它订单模板.xlsx", "_blank");
      }else if (type == 'rpa') {
        window.open("/static/excel/order/RPA订单模板.xlsx", "_blank");
      }
    },
    ImgViewPagechange(val) {
      this.imgPageInfo.currentPage = val;
      this.viewImages(this.row, 'change')
    },
    ImgViewSizechange(val) {
      this.imgPageInfo.currentPage = 1;
      this.imgPageInfo.pageSize = val;
      this.viewImages(this.row, 'change')
    },
    handleClose() {
      this.viewImagesDialogVisiable = false
      this.closeModal()
    },
    closeModal() {
      this.imageModelVisiable = false
      // this.$refs.imageModelRef.clearCss()
    },
    async childGetImgList(type) {
      if (type == 'right') {
        if (this.domIndex > this.viewImgList.length - 1) return
        this.domIndex++
      } else {
        if (this.domIndex < 0) return
        this.domIndex--
      }
      let params = {
        memberName: this.viewImgList[this.domIndex].memberName ? this.viewImgList[this.domIndex].memberName : null,
        zrDepartment: this.viewImgList[this.domIndex].zrDepartment ? this.viewImgList[this.domIndex].zrDepartment : null
      }
      const { data, success } = await GetDamagedOrdersStatisRowPageDtlAsync({ ...this.queryInfo, ...params })
      if (success) {
        this.imgList = data.list
        this.imgList.forEach(item => item.damagedZrFileUrls = item.damagedZrFileUrls ? item.damagedZrFileUrls.split(',') : null)
        this.imgList = this.imgList.map(item => {
          return item.damagedZrFileUrls.map(url => {
            return {
              sendGoodsDate: item.sendGoodsDate,
              sendWareHouse: item.sendWareHouse,
              platform: item.platform,
              styleCode: item.styleCode,
              goodsCode: item.goodsCode,
              zrDepartment: item.zrDepartment,
              zrType2: item.zrType2,
              damagedHandType: item.damagedHandType,
              damagedZrFileUrls: url,
              memberName: item.memberName
            }
          })
        }).flat()
      }
    },
    async deliverImg(item, i) {
      this.domIndex = i
      if (item.damagedZrFileUrlCount == 0) return this.$message.warning('暂无图片')
      const params = {
        memberName: item.memberName ? item.memberName : null,
        zrDepartment: item.zrDepartment ? item.zrDepartment : null
      }
      const { data, success } = await GetDamagedOrdersStatisRowPageDtlAsync({ ...this.queryInfo, ...params })
      if (success) {
        this.imgList = data.list
        this.imgList.forEach(item => item.damagedZrFileUrls = item.damagedZrFileUrls ? item.damagedZrFileUrls.split(',') : null)
        this.imgList = this.imgList.map(item => {
          return item.damagedZrFileUrls.map(url => {
            return {
              sendGoodsDate: item.sendGoodsDate,
              sendWareHouse: item.sendWareHouse,
              platform: item.platform,
              styleCode: item.styleCode,
              goodsCode: item.goodsCode,
              zrDepartment: item.zrDepartment,
              zrType2: item.zrType2,
              damagedHandType: item.damagedHandType,
              damagedZrFileUrls: url,
              memberName: item.memberName
            }
          })
        }).flat()
        console.log("打印数据this.imgList2222", this.imgList);
      }
      this.imageModelVisiable = true
      this.$nextTick(() => {
        this.$refs.imageModelRef.clearCss()
      })
    },
    async viewImages(row) {
      if (row.damagedZrFileUrlCount == 0) return this.$message.warning('暂无图片')
      this.row = row
      const params = {}
      if (row.sendGoodsDate) {
        params.sendGoodsDate = row.sendGoodsDate;
      }
      if (row.sendWareHouse) {
        params.sendWareHouse = row.sendWareHouse;
      }
      if (row.platform) {
        params.platform = row.platform;
      }
      if (row.styleCode) {
        params.styleCode = row.styleCode;
      }
      if (row.goodsCode) {
        params.goodsCode = row.goodsCode;
      }
      if (row.zrDepartment) {

        params.zrDepartment = row.zrDepartment;
      }
      if (row.zrType2) {
        params.zrType2 = row.zrType2;
      }
      if (row.damagedHandType) {
        params.damagedHandType = row.damagedHandType;
      }
      if (row.memberName) {
        params.memberName = row.memberName;
      }
      this.queryInfo = { ...this.filter, ...params, ...this.imgPageInfo }
      const { data, success } = await getDamagedOrdersStatisRowPageAsync({ ...this.filter, ...params, ...this.imgPageInfo })
      if (success) {
        this.viewImgList = data.list
        this.imgTotal = data.total
        this.viewImgList.forEach(item => {
          item.damagedZrFileUrls = item.damagedZrFileUrls ? item.damagedZrFileUrls.split(',') : null
        })
        this.domList = this.viewImgList
        this.maxIndex = this.viewImgList.length - 1
        //将this.viewImgList的damagedZrFileUrls用map返回一个对象
        // this.viewImgList.map((item) => {
        this.imageInfo = row
        this.viewImagesDialogVisiable = true
      }
    },
    update() {
      this.$forceUpdate();
    },
    async onchangeplatform(val) {
      this.filter.shopCode = null;
      const res1 = await getshopListt({ platform: val, CurrentPage: 1, PageSize: 100000 });
      this.shopList = res1.data.list
    },
    zrType2change() {
      this.$forceUpdate();
    },
    arrlength(arr) {
      var aa = 0;
      arr.map((item) => {
        if (item.checked) {
          aa += 1;
        }
      })
      return aa;
    },
    async exportProps() {
      const pager = this.$refs.pager.getPager()
      if(this.filter.timerange && this.filter.timerange.length>0){
        this.filter.startSendGoodsDate = dayjs(this.filter.timerange[0]).format('YYYY-MM-DD');
        this.filter.endSendGoodsDate = dayjs(this.filter.timerange[1]).format('YYYY-MM-DD');
      } else {
        this.filter.startSendGoodsDate = null;
        this.filter.endSendGoodsDate = null;
      }


      if(this.filter.AfterSaleApproveDate && this.filter.AfterSaleApproveDate.length>0){
        this.filter.startAfterSaleApproveDate = dayjs(this.filter.AfterSaleApproveDate[0]).format('YYYY-MM-DD');
        this.filter.endAfterSaleApproveDate = dayjs(this.filter.AfterSaleApproveDate[1]).format('YYYY-MM-DD');
      } else {
        this.filter.startAfterSaleApproveDate = null;
        this.filter.endAfterSaleApproveDate = null;
      }


      if(this.filter.ZrSetDate && this.filter.ZrSetDate.length>0){
        this.filter.startZrSetDate = dayjs(this.filter.ZrSetDate[0]).format('YYYY-MM-DD');
        this.filter.endZrSetDate = dayjs(this.filter.ZrSetDate[1]).format('YYYY-MM-DD');
      } else {
        this.filter.startZrSetDate = null;
        this.filter.endZrSetDate = null;
      }


      const para = {
        ...pager,
        ...this.filter
      }
      this.listLoading = true;
      const { data } = await exportDamagedOrdersStatisAsync(para)
      this.listLoading = false;
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '损耗订单' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    loaclarr() {
      let _this = this;
      let selarr = JSON.parse(localStorage.getItem('tjweidu'));
      let newarr = [];
      if (JSON.parse(localStorage.getItem('tjweidu'))) {
        selarr.map((item) => {
          newarr.push(item.value)
        })

        this.wdlist.map((item) => {
          if (newarr.indexOf(item.value) != -1) {
            item.checked = false;
          } else {
            item.checked = true;
          }
        });
        _this.selnum = _this.arrlength(_this.wdlist);
      }

    },
    wdselchange() {
      let newarr = [];
      // if(this.groupTages.length>0){
      //   this.filter.groupTypes = this.groupTages
      // }
      if(hearlist){
        hearlist.map((item) => {
          newarr.push(item.value)
        })
        this.filter.groupTypes = newarr
      }
      this.filter.groupTypes = newarr
      this.$refs.vxetable.$refs.xTable.resetColumn()
      var filteredArray = this.wdlist.filter((item) => {
        return this.filter.groupTypes.indexOf(item.value) === -1;
      });
      filteredArray.map((item) => {
        if (item.filed == this.filter.orderBy) {
          this.filter.orderBy = null;
        }
      })

      localStorage.setItem('tjweidu', JSON.stringify(filteredArray))

      // filteredArray.map((item) => {
      //   this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField(item.filed))
      // })

      var newarrimg = [];

      this.wdlist.map((item) => {
        if (this.filter.groupTypes.indexOf(item.value) != -1) {
          item.checked = true;
          newarrimg.push(item.filed)
        } else {
          item.checked = false;
        }
      });
      // if (newarrimg.indexOf('goodsCode') == -1 && newarrimg.indexOf('styleCode') == -1) {
      //   this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('goodsPic'))
      // }

      let _this = this;
      _this.selnum = _this.arrlength(this.wdlist);
      this.statisticalDimension = false;
    },
    async onsummaryClick(property) {
      let that = this;
      this.chartsdata1 = null;
      this.chartdialog = true;
      this.bottomcharts = true;
      // this.filter.charttimerange = this.filter.timerange;
      this.filter.charttimerange = (this.filter.timerange ? this.filter.timerange : this.timerangeList);
      let params = {
        ...this.filter,
        selectColumn: property,
        isRowAnlysis: false,
        startSendGoodsDate: this.filter.charttimerange ? this.filter.charttimerange[0] : null,
        endSendGoodsDate: this.filter.charttimerange ? this.filter.charttimerange[1] : null,
      }
      this.rowlist = params;
      let res = await getDamagedOrdersStatisAnlysisAsync(params);

      if (!res?.success) {
        return
      }
      this.chartsdata1 = res.data;
    },
    async getZrDept() {
      let res = await getDamagedOrdersZrDept();
      this.damagedList = res?.data;
      // damagedList2
    },
    async getZrType(name) {
      let res = await getDamagedOrdersZrType(name);
      this.damagedList2 = res.data;
      this.filter.zrType2 = '';

    },
    getPrevious30Days(dateString) {
      const date = new Date(dateString);
      date.setDate(date.getDate() - 30);

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      const previousDate = `${year}-${month}-${day}`;
      return previousDate;
    },
    getDaysDifference(date1, date2) {
      const oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
      const firstDate = new Date(date1);
      const secondDate = new Date(date2);

      // 将日期转换为时间戳，并且取绝对值以确保始终得到正数
      const diffDays = Math.abs(Math.round((firstDate - secondDate) / oneDay));

      return diffDays;
    },
    changedate(val) {
      if(!val){
        return
      }
      let days = this.getDaysDifference(val[0], val[1]);
      let startDate;
      let endDate;

      // if(days<30){
      //   this.$message.info("请选择至少一个月时间，已为您自动选择一个月时间")
      //   startDate = this.getPrevious30Days(val[1]);
      //   endDate = val[1];
      // }else{
      startDate = val[0];
      endDate = val[1];
      // }

      this.filter.timerange = [startDate, endDate];


      this.filter.charttimerange = [this.getPrevious30Days(endDate), endDate]




    },
    async changedatee(val) {
      let days = this.getDaysDifference(val[0], val[1]);
      let startDate;
      let endDate;
      this.chartsdata1 = null;
      // if(days<30){
      //   this.$message.info("请选择至少一个月时间，已为您自动选择一个月时间")
      //   startDate = this.getPrevious30Days(val[1]);
      //   endDate = val[1];
      // }else{
      //   startDate = val[0];
      //   endDate = val[1];
      // }
      startDate = val[0];
      endDate = val[1];

      this.filter.charttimerange = [startDate, endDate];


      let params = {
        ...this.rowlist,
        startSendGoodsDate: this.filter.charttimerange[0],
        endSendGoodsDate: this.filter.charttimerange[1],
      };
      let res = await getDamagedOrdersStatisAnlysisAsync(params);

      if (!res?.success) {
        return
      }
      this.chartsdata1 = res.data;


    },
    sortchange1({ order, prop }) {
      this.listLoading = true
      if (prop) {
        this.filter.orderBy = prop
        this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getlist()
      }
    },
    async getrowqst(row) {  //趋势图
      this.chartdialog = true;
      this.chartsdata1 = null;
      console.log(this.filter.timerange,'this.filter.timerange')
      console.log(this.timerangeList,'this.timerangeList')
      this.filter.charttimerange = (this.filter.timerange ? this.filter.timerange : this.timerangeList);
       console.log(this.filter.charttimerange,'this.filter.charttimerange')
      // this.filter.charttimerange = this.filter.timerange;
      // if(this.filter.sendWareHouseId===0){
      //   this.filter.sendWareHouseId = null
      // }
      // row.sendGoodsDate = null;
      // row.endSendGoodsDate = null;
      let params;
      if (row.sendWareHouseId == 0 || !row.sendWareHouseId || row.sendWareHouseId == "") {
        params = {
          ...row,
          // ...this.filter,
          isRowAnlysis: true,
          startSendGoodsDate: this.filter.charttimerange ? this.filter.charttimerange[0] : null,
          endSendGoodsDate: this.filter.charttimerange ? this.filter.charttimerange[1] : null,
          sendOrderCount: this.filter.sendOrderCount ? this.filter.sendOrderCount : undefined,
          damagedOrderRatio: this.filter.damagedOrderRatio ? this.filter.damagedOrderRatio : undefined,
          groupTypes:this.filter.groupTypes
        }
      } else {
        params = {
          // ...this.filter,
          ...row,
          isRowAnlysis: true,
          startSendGoodsDate: this.filter.charttimerange ? this.filter.charttimerange[0] : null,
          endSendGoodsDate: this.filter.charttimerange ? this.filter.charttimerange[1] : null,
          sendOrderCount: this.filter.sendOrderCount ? this.filter.sendOrderCount : undefined,
          damagedOrderRatio: this.filter.damagedOrderRatio ? this.filter.damagedOrderRatio : undefined,
          groupTypes:this.filter.groupTypes
        }
      }
      params.platform = params.platform == 0 ? null : params.platform;
      this.rowlist = params;
      let res = await getDamagedOrdersStatisAnlysisAsync(params);

      if (!res?.success) {
        return
      }
      this.chartsdata1 = res.data;

    },
    daoruFile(e, name) {
      this.digtitle = name;
      this.filetype = e;
      this.fileList = [];
      this.$nextTick(() => {
        this.dialogVisible = true
        this.$refs.upload.clearFiles();
      });

    },
    async uploadFile(item) {
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      let res;
      if (this.filetype == 'bf') {
        res = await importDamagedOrdersBF(form);
      } else if (this.filetype == 'hb') {
        res = await importDamagedOrdersHB(form);
      } else if (this.filetype == 'qt') {
        res = await importDamagedOrdersQT(form);
      }else if (this.filetype == 'rpa') {
        res = await importDamagedOrdersRPA(form);
      }

      // if (res.code == 1) {
      //     this.$message({ message: "上传成功,正在导入中...", type: "success" });
      //     this.dialogVisible = false;
      // }
      //
      this.dialogVisible = false;
      this.$message({
        message: '正在导入中...',
        type: "success",
      });
    },
    async uploadChange(file, fileList) {
      let files = [];
      files.push(file)
      this.fileList = files;
    },
    submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请选取文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
      this.$refs.upload.clearFiles();
      this.fileList = []

    },
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    //排序
    sortchange({ order, prop }) {
      this.listLoading = true
      if (prop) {
        this.filter.orderBy = prop
        this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getlist()
      }
    },
    // 获取列表
    async getlist() {
      let _this = this;
      if(this.filter.groupTypes.length == 0){
        this.filter.groupTypes = [4];
      }
      const pager = this.$refs.pager.getPager()
      this.filter.startSendGoodsDate = this.filter.timerange ? this.filter.timerange[0] : null;
      this.filter.endSendGoodsDate = this.filter.timerange ? this.filter.timerange[1] : null;

      this.filter.AfterSaleApproveDate = this.filter.AfterSaleApproveDate ? this.filter.AfterSaleApproveDate : []
      this.filter.startAfterSaleApproveDate = this.filter.AfterSaleApproveDate[0];
      this.filter.endAfterSaleApproveDate = this.filter.AfterSaleApproveDate[1];

      this.filter.ZrSetDate = this.filter.ZrSetDate ? this.filter.ZrSetDate : []
      this.filter.startZrSetDate = this.filter.ZrSetDate[0];
      this.filter.endZrSetDate = this.filter.ZrSetDate[1];


      // this.wdlist.map((item)=>{
      //   if(item.checked){
      //     this.filter.groupTypes.push(item.value)
      //   }
      // })

      const params = {
        ...pager,
        ...this.filter
      }
      this.listLoading = true
      let newObj = JSON.parse(JSON.stringify(params))
      // this.$emit('updateCondition', newObj);//传递查询条件,当页面切换时,会将第一个页面的查询条件传递到父组件
      const res = await getDamagedOrdersStatisAsync(newObj)
      this.listLoading = false

      if (!res?.success) {
        return
      }

      this.total = res.data.total
      // res.data.list.map((item) => {
      //   if (!item.goodsPic) {
      //     item.goodsPic = [];
      //   } else {
      //     if (item.goodsPic.indexOf(',') != -1) {
      //       item.goodsPic = item.goodsPic.split(",")
      //     } else {
      //       item.goodsPic = [item.goodsPic]
      //     }
      //   }
      // })
      // res.data.list.map((item) => {
      //   if (item.sendWareHouseId === 0) {
      //     item.sendWareHouseId = null;
      //   }
      // })
      res.data.list.map((item)=>{
        if (item.sendWareHouseId === 0) {
          item.sendWareHouseId = null;
        }
      })
      //截取第一张图片
      res.data.list.forEach(item => {
        if (item.goodsPic) {
          let firstPic = item.goodsPic.split(',')[0];
          item.goodsPic = JSON.stringify([{url: firstPic}])
        }
      });
      // console.log(res.data.list,'res.data.list');

      _this.list = res.data.list;
      // res.data.summary.damagedOrderRatio_sum = res.data.summary.damagedOrderRatio_sum + "%"
      _this.summaryarry = res.data.summary;
      _this.$forceUpdate();


      // this.dictionaries = listToTree(_.cloneDeep(list), {
      //   id: 0,
      //   parentId: 0,
      //   name: '根节点'
      // })

      // list.forEach(d => {
      //   d._loading = false
      // })
      // const tree = listToTree(list)
      // this.dictionaryTree = tree
    },

  },
};
</script>

<style lang="scss" scoped>
.father {
  position: relative;

  .imageModalPos {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    overflow: hidden;
  }
}

.a {
  align-items: center;
  font-size: 15px
}

.marginleft {
  margin-right: 5px;
  margin-bottom: 10px;
}

.viewImageBox {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  height: 600px;
  overflow: auto;

  .viewImageBox_item {
    width: 300px;
    height: 300px;
    display: flex;
    flex-direction: column;
    margin-right: 10px;
    position: relative;

    .viewImageBox_item_fixed {
      position: absolute;
      top: 0;
      left: 0;
      width: 50px;
      height: 30px;
      background-color: red;
      color: #fff;
      text-align: center;
      line-height: 30px;
    }


    .viewImageBox_item_bottom {
      flex: 1;

      .viewImageBox_item_info {
        display: flex;

        .viewImageBox_item_info_left,
        .viewImageBox_item_info_right {
          text-align: center;
          height: 30px;
          box-sizing: border-box;
          border: 1px solid #ccc;
          line-height: 30px;
        }

        .viewImageBox_item_info_left {
          width: 80px;
        }

        .viewImageBox_item_info_right {
          flex: 1;
        }
      }
    }
  }
}

.viewImageBox_item_img ::v-deep img {
  min-width: 300px !important;
  min-height: 220px !important;
  width: 300px !important;
  height: 220px !important;
}

.viewImageBox_item_img ::v-deep div {
  min-width: 300px !important;
  min-height: 220px !important;
  width: 300px !important;
  height: 220px !important;
}
.centerflex{
  display: flex; flex-direction: row;justify-content: center; align-items: center;
}
</style>
