<template>
    <!-- 薪资核算 -->
    <my-container>
        <template #header>
            <!-- 名字 -->
            <el-select v-model="filter.userName" style="width: 7%;margin-right:2px;" filterable placeholder="请选择姓名"
                :clearable="true">
                <el-option v-for="item in selname" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>
            <!-- 公司 -->
            <el-select v-model="filter.companyName" style="width: 6%;margin-right:2px;" filterable placeholder="请选择公司"
                :clearable="true">
                <el-option label="义乌" value="义乌"></el-option>
                <el-option label="南昌" value="南昌"></el-option>
                <el-option label="武汉" value="武汉"></el-option>
            </el-select>
            <!-- 工作岗位 -->
            <el-select v-model="filter.workPosition" style="width: 7%;margin-right:2px;" filterable placeholder="工作岗位"
                :clearable="true">
                <el-option v-for="item in selwork" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>
            <!-- 提成岗位 -->
            <el-select v-model="filter.commissionPosition" style="width: 7%;margin-right:2px;" filterable placeholder="提成岗位"
                :clearable="true">
                <el-option v-for="item in selpush" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>
            <!-- 类型 -->
            <el-select v-model="filter.classtype" style="width: 7%;margin-right:2px;" filterable placeholder="类型"
                :clearable="true">
                <el-option v-for="item in seltype" :key="item" :label="item" :value="item">
                </el-option>
            </el-select>
            <!-- 是否核算 -->
            <el-select v-model="filter.isCyCommission" style="width: 6%;margin-right:5px;" filterable placeholder="是否核算"
                :clearable="true">
                <el-option label="是" value="1"></el-option>
                <el-option label="否" value="0"></el-option>
            </el-select>
            <el-button style="height: 28px;margin-right:5px;" type="primary" @click="onSearch">查询</el-button>
            <el-button-group style="margin-right:5px;">
              <el-button style="height: 28px;" type="primary" v-if="checkPermission('shootingHs-xzhs-czl')"
                      @click="archive">版本存档</el-button>
              <el-button style="height: 28px;" type="primary"  v-if="checkPermission('shootingHs-xzhs-czl')"
                      @dblclick.native="resetdata">重置数据</el-button>
              <el-button style="height: 28px;" type="primary" v-if="checkPermission('shootingHs-xzhs-czl')"
                      @click="sendRowEventAll">一键发送</el-button>
              <el-button style="height: 28px;" type="primary" v-if="checkPermission('shootingHs-xzhs-czl')"
                      @click="commissionment">提成考核</el-button>
              <el-button style="height: 28px;" type="primary" v-if="checkPermission('shootingHs-xzhs-czl')"
                      @click="performancetemplatee">绩效模板</el-button>
              <el-button style="height: 28px;" type="primary"
                      @click="createOnePrit">打印绩效</el-button>
              <el-dropdown @command="confirmationsignature">
                  <el-button style="height: 28px;" type="primary">
                    确认签字<i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown" >
                      <el-dropdown-item command="2">主管签字</el-dropdown-item>
                      <el-dropdown-item command="3">经理签字</el-dropdown-item>
                  </el-dropdown-menu>
              </el-dropdown>
            </el-button-group>
            <el-button style="height: 28px;margin-right:5px;" type="primary" v-if="checkPermission('shootingHs-xzhs-czl')"
                    @click="exportDataEvent">导出</el-button>
            <el-dropdown @command="onImportSyj" style="margin-right:5px;">
                  <el-button style="height: 28px;" type="primary" v-if="checkPermission('shootingHs-xzhs-czl')">
                    导入<i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown" >
                      <el-dropdown-item command="a">模板导入</el-dropdown-item>
                      <el-dropdown-item command="b">匹配导入</el-dropdown-item>
                  </el-dropdown-menu>
              </el-dropdown>
              <!-- <el-button style="height: 28px;" type="primary" v-if="checkPermission('shootingHs-xzhs-czl')"
                    @click="ClickdownloadTemplate">下载模板</el-button>
                <el-button style="height: 28px;" type="primary" @click="sendRowEventAll">一键发送</el-button> -->
        </template>
        <template>
            <div class="content">

                <div style="width: 100%; height: 100%; position: relative; overflow-x: hidden;">
                    <vxe-toolbar ref="xToolbar" custom class="vxetoolbar20221212">
                        <template #buttons>
                            <slot name="tbHeader" />
                        </template>
                    </vxe-toolbar>
                    <vxe-table border style="width: 100%; height: 100%;" height="100%" resizable show-overflow
                        @checkbox-all="checkmethod" @checkbox-change="checkmethod"
                        show-footer-overflow keep-source size="mini" ref="xTable" :loading="listLoading" :data="tasklist"
                        show-footer :footer-method="footerMethod" stripe class="vxetable202212161323 mytable-scrollbar20221212" :row-config="{isCurrent: true, isHover: true}"
                        :edit-config="{ trigger: 'manual', mode: 'row', showStatus: true, showIcon: false, autoClear: false }">
                        <vxe-column type="checkbox" width="40" fixed="left"></vxe-column>
                        <vxe-column type="seq" width="40" fixed="left"></vxe-column>
                        <vxe-column field="userName" fixed="left" title="姓名" width='85'> </vxe-column>
                        <vxe-column field="companyName" fixed="left" title="公司" width='65'>
                        </vxe-column>
                        <vxe-column field="workPositionStr" title="工作岗位" width='85'>
                        </vxe-column>
                        <vxe-column field="commissionPositionStr" title="提成岗位" width='85'>
                        </vxe-column>
                        <vxe-column field="accountingItem" title="核算项目" width='85'>
                        </vxe-column>
                        <!-- <vxe-column field="classtype" title="类型" width='60'>
                        </vxe-column> -->
                        <vxe-column field="jycqts" title="应出勤" :edit-render="{}" width='70'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.jycqts" type="float" min="0" max="31" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="ycqts" title="实际出勤" :edit-render="{}" width='100'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.ycqts" type="float" min="0" max="31" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="jBasepay" title="基本薪资" :edit-render="{}" width='70'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.jBasepay" type="float" min="0" max="500000" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="basepay" title="实发薪资" width='70'>
                        </vxe-column>
                        <vxe-column field="jAchievement" title="应发绩效" width='70'> </vxe-column>
                        <vxe-column field="achievement" title="实发绩效" width='100'>
                        </vxe-column>
                        <vxe-column field="shootingAjustCount" title="新品" width='80'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.shootingAjustCount" type="float" min="0" max="500000"
                                    :controls="false" placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="vedioTaskAjustCount" title="短视频拍摄" width='80'>
                        </vxe-column>
                        <vxe-column field="directImgAjustCount" title="车图" width='80'>
                        </vxe-column>
                        <vxe-column field="microDetailAjustCount" title="微详情" width='80'>
                        </vxe-column>
                        <vxe-column field="shopDecorationAjustCount" title="店铺装修" width='80'>
                        </vxe-column>
                        <vxe-column field="packingDesignAjustCount" title="包装设计" :edit-render="{}" width='80'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.packingDesignAjustCount" type="float" min="0" max="500000"
                                    :controls="false" placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="dayModImgAjustCount" title="日常改图" width='95'>
                        </vxe-column>
                        <vxe-column field="quanqingAward" title="全勤奖" :edit-render="{}" width='70'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.quanqingAward" type="float" min="0" max="500000" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="overTimePay" title="加班补贴" :edit-render="{}" width='70'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.overTimePay" type="float" min="0" max="500000" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="sbSubsidy" title="设备补助" width='70'>
                        </vxe-column>
                        <vxe-column field="subsidy" title="补贴" :edit-render="{}" width='70'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.subsidy" type="float" min="0" max="500000" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="fuLiFee" title="福利" :edit-render="{}" width='70'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.fuLiFee" type="float" min="0" max="500000" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="supplyAgain" title="补发" :edit-render="{}" width='70'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.supplyAgain" type="float" min="0" max="500000" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="otherAjustCount" title="其他津贴" :edit-render="{}" width='95'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.otherAjustCount" type="float" min="0" max="500000" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="shouldFee" title="应发金额" width='100'> </vxe-column>
                        <vxe-column field="shuiDianFee" title="宿舍水电费" :edit-render="{}" width='80'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.shuiDianFee" type="float" min="0" max="500000" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="chiDaoFee" title="迟到扣款" :edit-render="{}" width='70'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.chiDaoFee" type="float" min="0" max="500000" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="queKaFee" title="缺卡扣款" :edit-render="{}" width='70'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.queKaFee" type="float" min="0" max="500000" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="zhaoTuiFee" title="早退扣款" :edit-render="{}" width='70'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.zhaoTuiFee" type="float" min="0" max="500000" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="kuangGongFee" title="旷工扣款" :edit-render="{}" width='70'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.kuangGongFee" type="float" min="0" max="500000" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="cutPayment" title="责任扣款" :edit-render="{}" width='70'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.cutPayment" type="float" min="0" max="500000" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="otherDeductions" title="其他扣款" :edit-render="{}" width='70'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.otherDeductions" type="float" min="0" max="500000"
                                    :controls="false" placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="socialSecurityFee" title="社保扣款" :edit-render="{}" width='70'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.socialSecurityFee" type="float" min="0" max="500000"
                                    :controls="false" placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="otherFee" title="其他减" :edit-render="{}" width='95'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.otherFee" type="float" min="0" max="500000" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="yuZhiFee" title="工资预支" :edit-render="{}" width='70'>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.yuZhiFee" type="float" min="0" max="500000" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="commissionRate" fixed="right" title="提成比例" :edit-render="{}" width='70'>
                            <template #default="{ row }"> <span :style="{ cursor: 'pointer' }" @click="percentage(row)">{{ row.commissionRate + '%' }}</span> </template>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.commissionRate" type="integer" min="0" max="120" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="performance" title="绩效分" width='70' fixed="right">
                            <template #default="{ row }">
                              <!-- <span :style="{ color: row.jAchievement > 0 ? row.isAssess == true? '#67c23a':'#409eff' :'black', cursor: row.jAchievement > 0 ? 'pointer' : 'text' }" @click="handleClick(row)"> {{ row.userId === '40363' ? row.performance : row.performance + '%' }} </span> -->
                              <span :style="{ color: row.jAchievement > 0 ? row.isAssess == true? '#67c23a':'#409eff' :'black', cursor: row.jAchievement > 0 ? 'pointer' : 'text' }" @click="handleClick(row)"> {{ row.performanceStr }} </span>

                            </template>
                            <template #edit="{ row }">
                                <vxe-input v-model="row.performance" type="integer" min="0" max="120" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="isCyCommission" fixed="right" title="提成核算" :edit-render="{}" width='75' v-if="checkPermission('shootingHs-xzhs-czl')">
                            <template #default="{ row }">
                                <vxe-switch v-model="row.isCyCommission" open-label="是" close-label="否" :open-value="1"
                                    :close-value="0" @change="saveRowEvent(row)"></vxe-switch>
                            </template>
                            <template #edit="{ row }">
                                <vxe-switch v-model="row.isCyCommission" open-label="是" close-label="否" :open-value="1"
                                    :close-value="0"></vxe-switch>
                            </template>
                        </vxe-column>
                        <vxe-column field="actualFee" title="实发金额" fixed="right" width='90'> </vxe-column>
                        <vxe-column field="joinedDate" title="入职日期" width='80'> <template #default="{ row }">{{
                            isformatTime(row.joinedDate) }}</template></vxe-column>
                        <vxe-column field="leaveDate" title="离职日期" width='80'><template #default="{ row }">{{
                            isformatTime(row.leaveDate) }}</template> </vxe-column>
                        <vxe-column title="操作" width="160" fixed="right" v-if="checkPermission('shootingHs-xzhs-czl')">
                            <template #default="{ row }">
                                <template v-if="$refs.xTable.isActiveByRow(row)">
                                    <vxe-button size="mini" @click="saveRowEvent(row)">保存</vxe-button>
                                    <vxe-button size="mini" @click="cancelRowEvent(row)">取消</vxe-button>
                                </template>
                                <template v-else>
                                    <vxe-button size="mini" @click="editRowEvent(row)">编辑</vxe-button>
                                    <vxe-button size="mini" @click="sendRowEvent(row)">发送钉钉</vxe-button>
                                </template>
                            </template>
                        </vxe-column>
                    </vxe-table>
                </div>
                <!-- <div style="margin-left: 10px; margin-top: -21px; height: 75vh;">
                    <vxetablebase :id="'mediaNCCommissiontotal'" height="100%" :hasSeq="false" :border="true"
                        :showToolbar="false" :hasexpand='true' :hascheck="false" :align="'center'" ref="table" :that='that'
                        :tableData='tasklist1' :tableCols='tableCols1' :loading="listLoading"></vxetablebase>
                </div> -->
            </div>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList"
                :page-size="300" />
        </template>
        <!--薪资模板导入-->
        <el-dialog title="薪资模板导入" :visible.sync="dialogVisibleSyj" width="30%" :before-close="onImportClose" v-dialogDrag>
            <span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                    :file-list="fileList" action accept=".xlsx" :http-request="uploadFile">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <my-confirm-button style="margin-left: 10px;" size="small" type="success"
                        @click="onSubmitupload">上传</my-confirm-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="onImportClose()">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog :visible.sync="departmentmanagerscoringpage" width="1150px" height="700px" v-dialogDrag  @close="closeDialog">
          <div style="box-sizing: border-box;padding: 32px 60px;">
            <div ref="onheard">
              <div class="jxbt">
                视觉设计部人员月度绩效考核
              </div>
              <div class="jxan">
                <div style="width:20%;font-weight:bold;font-size:14px;">
                  <span>被考核人：</span>
                  <span v-text="assessoruserName"></span>
                </div>
                <div style="width:19%;font-weight:bold;font-size:14px;margin-top: -3px;">
                  <span>考核月份：</span>
                  <span>
                    <el-select filterable v-model="monthDay" placeholder="" style="width: 55px">
                      <el-option label="1" :value="1"></el-option>
                      <el-option label="2" :value="2" :key="2"></el-option>
                      <el-option label="3" :value="3" :key="3"></el-option>
                      <el-option label="4" :value="4" :key="4"></el-option>
                      <el-option label="5" :value="5" :key="5"></el-option>
                      <el-option label="6" :value="6" :key="6"></el-option>
                      <el-option label="7" :value="7" :key="7"></el-option>
                      <el-option label="8" :value="8" :key="8"></el-option>
                      <el-option label="9" :value="9" :key="9"></el-option>
                      <el-option label="10" :value="10" :key="10"></el-option>
                      <el-option label="11" :value="11" :key="11"></el-option>
                      <el-option label="12" :value="12" :key="12"></el-option>
                    </el-select>
                  </span>
                  <span style="margin-left:10px;">月</span>
                </div>
                <div style="width:75%;text-align: right;">
                  <span style="margin-right:10px;">
                    <el-select size="mini" v-model="postworkPosition" placeholder="请选择考核岗位"  clearable :disabled="true">
                      <el-option v-for="item in assessmentpost" :key="item.value" :label="item.label" :value="item.value">
                      </el-option>
                    </el-select>
                  </span>
                  <span style="margin-right:10px;"><el-button size="mini" type="primary" @click="refresh"
                      >刷新</el-button></span>
                </div>
              </div>
              <div>
              <div class="xptcblbj2">
                <div>
                  <div :style="{ 'font-weight': 'bold', 'font-size': '15px' }">及时完成率对应提成</div>
                    <el-table :data="managerinTimeRateList" style="width: 100%">
                      <el-table-column prop="styleName" label="款式类型" width="180"></el-table-column>
                      <el-table-column prop="equalOrGreaterScore" label="大于或等于" width="180"></el-table-column>
                      <el-table-column prop="lessScore" label="小于" width="180"></el-table-column>
                      <el-table-column prop="performanceScore" label="绩效分" ></el-table-column>
                    </el-table>
                  </div>
                  <div style="padding: 20px 0 0 0;">
                    <div :style="{ 'font-weight': 'bold', 'font-size': '15px' }">质量+配合度对应提成</div>
                    <el-table :data="manageradaptabilityDeduct" style="width: 100%">
                      <el-table-column prop="equalOrGreaterScore" label="大于或等于" width="180"></el-table-column>
                      <el-table-column prop="lessScore" label="小于" width="220"></el-table-column>
                      <el-table-column prop="performanceScore" label="绩效分"></el-table-column>
                    </el-table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-dialog>
        <el-dialog title="版本存档" :visible.sync="archivedialogVisible" element-loading-text="拼命加载中" v-dialogDrag
            :append-to-body="true" v-loading="editLoading">
            <el-form :model="archiveform" ref="archiveform" label-width="120px" :rules="editformRules">
                <el-row>&nbsp;</el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item prop="ycqts" label="版本名称">
                            <el-input :clearable="true" v-model.trim="archiveform.versionName" :min="0" max="10000"
                                placeholder="版本名称"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="archivedialogVisible = false">关 闭</el-button>
                    <my-confirm-button type="submit" @click="archiveonSubmit" />
                </span>
            </template>
        </el-dialog>
        <el-dialog  :visible.sync="percentagecommissions" element-loading-text="拼命加载中" v-dialogDrag
            :append-to-body="true" v-loading="editLoading">
            <div class="bzbjbt">
              <span style="float: left">提成考核</span>
            </div>
            <div class="xptcblbj2">
              <div>
              <div :style="{ 'font-weight': 'bold', 'font-size': '15px' }">及时完成率对应提成</div>
              <el-table :data="inTimeRateList" style="width: 100%">
                <el-table-column v-if="showStyleNameColumn" prop="styleName" label="款式类型" width="180"></el-table-column>
                <el-table-column prop="equalOrGreaterScore" label="大于或等于" width="180"></el-table-column>
                <el-table-column prop="lessScore" label="小于" width="180"></el-table-column>
                <el-table-column prop="commissionReduced" label="提成比例减" ></el-table-column>
              </el-table>
            </div>
            <div style="padding: 20px 0 0 0;">
              <div :style="{ 'font-weight': 'bold', 'font-size': '15px' }">质量+配合度对应提成</div>
              <el-table :data="adaptabilityDeduct" style="width: 100%">
                <el-table-column prop="equalOrGreaterScore" label="大于或等于" width="180"></el-table-column>
                <el-table-column prop="lessScore" label="小于" width="220"></el-table-column>
                <el-table-column prop="commissionReduced" label="提成比例减"></el-table-column>
              </el-table>
            </div>
            </div>
        </el-dialog>
      <!--绩效模板-->
      <el-drawer :visible.sync="performancestencil"   direction="rtl"
          :size="1500" element-loading-text="拼命加载中" v-loading="addLoading" :show-close="false" @close="closeDrawer">
          <perforManceTemplatenewshoot ref="perforManceTemplatenewshoot1" :myValue="parentValue" :postselwork="selwork" @update-templatepageclose="handleUpdateTemplatepageclose">
          </perforManceTemplatenewshoot>
      </el-drawer>
      <!-- 打分页 -->
      <el-dialog :visible.sync="performancesdialogtencil" width="1150px" height="20px" v-dialogDrag  @close="closeDialog">
        <perforManceTemplatenewshoot ref="perforManceTemplatenewshoot2" @onSearch="onSearch" :myValue="parentValue" @close="performancesdialogtencil= false" :historicalversion ="historical" :ddUserId="parentDdUserId"
         :workPosition="parentWorkPosition" :rowc="rows" :versionId="performanceversionId" :postselwork="selwork">
          </perforManceTemplatenewshoot>
      </el-dialog>

      <el-dialog :visible.sync="performancesdialogtencilall" width="1150px" height="20px" v-dialogDrag  @close="closeDialog">
        <perforManceTemplatenewshootall ref="perforManceTemplatenewshootallref" @onSearch="onSearch" :myValue="parentValue" @close="performancesdialogtencilall= false" :historicalversion ="historical" :ddUserId="parentDdUserId"
         :workPosition="parentWorkPosition" :rowc="rows" :versionId="performanceversionId" :postselwork="selwork">
          </perforManceTemplatenewshootall>
      </el-dialog>

      <el-drawer title="提成考核" size="1500px" :append-to-body="true" :visible.sync="missionassessment" label="rtl">
        <commissionassessment ref="commissionassessmentref" >
          </commissionassessment>
      </el-drawer>
    </my-container>
</template>

<script>
// import VXETable from 'vxe-table'
// import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
// import ExcelJS from 'exceljs'
// VXETable.use(VXETablePluginExportXLSX, {
//       ExcelJS
//     })
import { formatTime } from "@/utils";
import { addOrUpdatePersonnelPositionAsync, getPersonnelPositionAsync, importShootingSalaryAsync, archiveCalculateCheck, sendSalery,exportPersonnelSalary, confirmPerformAssessAll, resetSalaryData, getShootingSetData, getCommCommissionAssess, getDeptManagerCommCommissionAssess } from '@/api/media/shootingset'
import MyConfirmButton from "@/components/my-confirm-button";
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import MyContainer from "@/components/my-container";
import perforManceTemplatenewshoot from "./perforManceTemplatenewshoot.vue";
import checkPermission from '@/utils/permission'
import perforManceTemplatenewshootall from "./perforManceTemplatenewshootall.vue";
import commissionassessment from "./commissionassessment.vue";


const tableCols1 = [
    { istrue: true, prop: 'classtype', label: '类型', width: '90' },
    { istrue: true, prop: 'sum', label: '合计', width: '90' },
];
export default {
    name: 'mediaTotalCommission',
    props:['historical'],
    components: { MyContainer, vxetablebase, MyConfirmButton, perforManceTemplatenewshoot, perforManceTemplatenewshootall, commissionassessment },
    data() {
        return {
            assessoruserId: null,
            assessoruserName: null,
            monthDay: null,
            postworkPosition: null,
            assessmentpost: [],
            adaptabilityDeduct: [],
            inTimeRateList: [],
            managerinTimeRateList: [],
            manageradaptabilityDeduct: [],
            percentagecommissions : false,
            departmentmanagerscoringpage: false,
            missionassessment: false,
            templatepageclose: false,//模板页区分是否排序
            userIdList: [],
            performancesdialogtencilall: false,
            performanceversionId:null,//历史版本打分页所需数据
            rowc:null,//打分页所需数据
            rows:null,//打分页所需数据
            parentDdUserId: null,//打分页所需数据
            parentWorkPosition: null,//打分页所需数据
            parentValue: null,//传值区分模板打分页
            performancestencil: false,//模板页
            performancesdialogtencil: false,//打分页
            addLoading: false,//模板加载
            that: this,
            listLoading: false,
            archivedialogVisibleSyj: false,
            isloadstore: true,
            tableCols1: tableCols1,
            userName: '',
            companyName: '',
            workPositionStr: '',
            commissionPositionStr: '',
            classtype: '',
            isCyCommissionbool: '',
            selname: [],
            printrow: [],
            selcompany: [],
            selwork: [],
            selpush: [],
            seltype: [],
            selissum: [],
            summaryarry: [],
            tasklist: [],
            tasklist1: [],
            tasklistSend: [],
            fileList: [],
            total: 0,
            sels: [], // 列表选中列
            pager: { OrderBy: "orderNum", IsAsc: true },
            editformdialog: false,
            editLoading: false,
            archivedialogVisible: false,
            dialogVisibleSyj: false,
            editformTitle: null,
            archiveform: {
                versionName: null,
            },
            editform: {
                openType: 2,
                commissionRate: 0,
                isCyCommission: 1,
                ycqts: 1,
                basepay: 1
            },
            isCyCommissionList: [
                { label: '是', value: 1 },
                { label: '否', value: 0 }
            ],
            filter: {
                needCacl: 1,

            },
            editformRules: {
                commissionRate: [{ required: true, message: '请填写', trigger: 'blur' }],
                isCyCommission: [{ required: true, message: '请选择', trigger: 'blur' }],
            },
        };
    },
    computed: {
      showStyleNameColumn() {
        return this.inTimeRateList.every(item => item.typeId !== 1)
      }
    },
    //向子组件注册方法
    provide() {
        return {
        }
    },
    created() {
        // VXETable.use(VXETablePluginExportXLSX);
        this.$nextTick(() => {
            // 手动将表格和工具栏进行关联
            this.$refs.xTable.connect(this.$refs.xToolbar)
        })
    },
    async mounted() {
        await this.onSearch();
        const { data } = await getShootingSetData({settype:11})
        this.assessmentpost = data.data.map(obj => ({ value: obj.setId, label: obj.sceneCode }));
    },
    methods: {
      async refresh(){
        const { data ,success } = await getDeptManagerCommCommissionAssess({userId:this.assessoruserId})
          if (success) {
            if (data.inTimeRateList !== null && data.inTimeRateList !== undefined && data.inTimeRateList.length > 0) {
              this.managerinTimeRateList = data.inTimeRateList.filter(item => item !== null).map(item => {
                return {  ...item,  lessScore: item.lessScore + '%',  equalOrGreaterScore: item.equalOrGreaterScore + '%'  };
              });
            }
              this.manageradaptabilityDeduct = data.adaptabilityDeduct.filter(item => item !== null);
          }
      },
      async percentage(row){
        //userId-1时，为错误数据
        if(row.userId == '-1' || (row.workPositionStr == "部门经理" && row.commissionPositionStr == "部门经理")){
          return;
        }
        if(row.assessType == 1 || row.assessType == 2){
          const {data} = await getCommCommissionAssess({userId:row.userId})
          if(data.inTimeRateList != null && data.inTimeRateList !== undefined && data.inTimeRateList.length > 0){
            this.inTimeRateList = data.inTimeRateList.filter(item => item !== null);
          }
          this.adaptabilityDeduct = data.adaptabilityDeduct
          this.percentagecommissions = true
        }else{
          return
        }
      },
      commissionment(){
        this.missionassessment = true
        this.$nextTick(() => {
          this.$refs.commissionassessmentref.onSearch();
        });
      },
      //模板页排序事件
      handleUpdateTemplatepageclose(value) {
        this.templatepageclose = value;
      },
      //重置数据
        async resetdata(done){
        this.$confirm("是否重置数据？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(async () => {
            const { success } = await resetSalaryData();
            if (success) {
              this.$message({ message: '重置数据成功！', type: "success" });
              await this.onSearch();
            }
          })
        },
        //确认签字
        async confirmationsignature(command){
          const params = {userIdList:this.userIdList,confirmType:command}
          const { success } = await confirmPerformAssessAll(params)
          if(success){
            this.$message({ message: "确认成功", type: "success" });
            this.userIdList = [];//确认签字后，清除选中的ID
            await this.onSearch();
          }else{
            return
          }
        },
        createOnePrit(){
            if(this.printrow.length==0){
                this.$message.error("请选择至少一条数据打印")
                return
            }
            let _this = this;
            _this.performancesdialogtencilall = true;
            _this.$nextTick(() => {
                _this.$refs.perforManceTemplatenewshootallref.forgetlist(_this.printrow);
            });

        },
        checkmethod(row){
            this.printrow = row.records;
            this.userIdList = row.records.map(record => record.userId)
        },
        //模板页关闭
        closeDrawer() {
            if (this.templatepageclose) {
                this.$confirm("已进行表格排序，并未保存，确认关闭吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(() => {
                    this.performancestencil = false;//不保留排序，关闭模板抽屉
                    this.templatepageclose = false;//不保留排序，将区分排序字段恢复默认false
                }).catch(() => {
                    this.performancestencil = true;//保留排序，模板抽屉保持打开
                });
                this.performancestencil = true;//弹出选择框时，模板抽屉保持打开
            } else {
                this.performancestencil = false;
                this.$nextTick(() => {
                    this.$refs.perforManceTemplatenewshoot1.performanFilterMonthDay();
                });
            }
        },
        //打分页关闭
        closeDialog() {
          this.performancesdialogtencil = false;
          this.$nextTick(() => {
              this.$refs.perforManceTemplatenewshoot2.closeFilterMonthDay();
            });
        },
        //绩效分页
        async handleClick(row) {
          console.log(row,'row990');
        //   if(row.workPositionStr == "部门经理" && row.commissionPositionStr == "部门经理"){
        // //   if(row.jAchievement == 0 && row.userId !== "40363"){
        //     return
        //   }

          if((row.assessType==1|| row.assessType==2)&& row.workPositionStr == "部门经理"){
            this.assessoruserId = row.userId
            this.assessoruserName = row.userName
            this.postworkPosition = row.workPosition
            var date = new Date();
            var month = date.getMonth();
            var day = date.getDate();
            // 判断是否需要将月份加1
            if (day > 19) {
                month++;
            }
            if (month < 0) {
                month = 11;
            }
            this.monthDay = parseInt(month);
            const {data} = await getDeptManagerCommCommissionAssess({userId:row.userId})
            if (data.inTimeRateList !== null && data.inTimeRateList !== undefined && data.inTimeRateList.length > 0) {
                this.managerinTimeRateList = data.inTimeRateList.filter(item => item !== null).map(item => {
                return {  ...item,  lessScore: item.lessScore + '%',  equalOrGreaterScore: item.equalOrGreaterScore + '%'  };
                });
            }
            this.manageradaptabilityDeduct = data.adaptabilityDeduct.filter(item => item !== null);
            this.departmentmanagerscoringpage = true
        }else{
            if(row.jAchievement == 0){
                return
            }
          //   if(row.userId !== "40363"){
            this.performanceversionId = this.versionId;//历史版本所需参数
            this.parentValue = 2;//区分模板和打分页
            this.performancesdialogtencil = true;
            console.log(row.userName,'11.rowc.userName');
            this.rows = row;//行数据传子组件
            console.log(this.rows,'this.rows11');
            this.$nextTick(() => {
                this.$refs.perforManceTemplatenewshoot2.getlist();
          });
          }
        },
        //绩效模板
        async performancetemplatee(){
          this.parentValue = 1;//区分模板和打分页
          this.performancestencil = true;
          this.$nextTick(() => {
            this.$refs.perforManceTemplatenewshoot1.getlist();
          });
        },
        isformatTime(val) {
            return val == null ? null : formatTime(val, "YY-MM-DD")
        },
        async sendRowEventAll() {
            var res = await sendSalery(this.tasklist);
            if (res.success) {
                this.$message({ message: '钉钉请求发送成功!', type: "success" });
            }
        },
        async sendRowEvent(row) {
            this.tasklistSend = [];
            this.tasklistSend.push(row);
            var res = await sendSalery(this.tasklistSend);
            if (res.success) {
                this.$message({ message: '钉钉请求发送成功!', type: "success" });
            }
            /*  this.tasklistSend =[];
             this.tasklistSend.push(row);
             setTimeout(()=>{
                 html2canvas(this.$refs.oneboxx).then((canvas) => {
                 let dataURL = canvas.toDataURL('image/png')
                 console.log(dataURL);
             })
             },2000) */

        },
        formatIsCommission(value) {
            if (value === 1) {
                return '是'
            }
            if (value === 0) {
                return '否'
            }
            return '否'
        },
        historyInfo() {
        },
        //版本存档
        async archive() {
            this.archiveform.versionName = null;
            this.archivedialogVisible = true;
        },
        async archiveonSubmit() {
            this.editLoading = true;
            var res = await archiveCalculateCheck({ versionname: this.archiveform.versionName });
            this.editLoading = false;
            if (!res?.success) { return; }
            this.$message({ message: this.$t('操作成功，请到历史版本中查看'), type: 'success' });
            this.archivedialogVisible = false;
        },
       async exportDataEvent() {
            // this.$refs.xTable.exportData({ filename: "薪资核算", type: 'csv' })
            // this.$refs.xTable.exportData({filename:"薪资核算",    sheetName: 'Sheet1',type: 'xlsx' })
            let selSalaryList = [];
            if (checkPermission('shootingHs-salaryyw')) {
              selSalaryList.push(1);
            }
            if (checkPermission('shootingHs-salarync')) {
              selSalaryList.push(2);
            }
            if (checkPermission('shootingHs-salarywh')) {
              selSalaryList.push(3);
            }
            if (checkPermission('shootingHs-sysalary')) {
              selSalaryList.push(4);
            }
            if (checkPermission('shootingHs-sssalary')) {
              selSalaryList.push(5);
            }
            if (checkPermission('shootingHs-zlsalary')) {
              selSalaryList.push(6);
            }
            if (checkPermission('shootingHs-mgsalary')) {
              selSalaryList.push(7);
            }
            if (checkPermission('shootingHs-jmsalary')) {
              selSalaryList.push(8);
            }
            if (checkPermission('shootingHs-dspsalary')) {
              selSalaryList.push(9);
            }
            if (checkPermission('shootingHs-qtsalary')) {
              selSalaryList.push(10);
            }
            selSalaryList = selSalaryList.join(',');
            var pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
                selSalaryList:selSalaryList
            };
            this.listLoading = true;
            var res = await exportPersonnelSalary(params);
            if (res?.data?.type == 'application/json') { this.listLoading = false; return;}
            const aLink = document.createElement("a");
            var blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '薪资核算导出.xlsx')
            aLink.click()
            this.listLoading = false;
        },
        onImportClose() {
            this.dialogVisibleSyj = false;
            this.fileList = [];
        },
        onImportSyj() {
            this.dialogVisibleSyj = true;
        },
        ClickdownloadTemplate() {
            window.open("/static/excel/shooting/视觉设计部薪资模板.xlsx", "_blank");
        },
        async onSubmitupload() {
            this.$refs.upload.submit()
        },
        async uploadFile(item) {
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("actiontype", 1);
            var res = await importShootingSalaryAsync(form);
            this.fileList = [];
            if (res?.success) {
                this.$message({ message: '导入成功!', type: "success" });
                this.onSearch();
                this.dialogVisibleSyj = false;
            }
        },
        async onSearch() {
            this.userIdList = [];
            this.printrow = [];
            this.$refs.pager.setPage(1);
            this.getTaskList();
        },
        async getTaskList() {
            let selSalaryList = [];
            if (checkPermission('shootingHs-salaryyw')) {
              selSalaryList.push(1);
            }
            if (checkPermission('shootingHs-salarync')) {
              selSalaryList.push(2);
            }
            if (checkPermission('shootingHs-salarywh')) {
              selSalaryList.push(3);
            }
            if (checkPermission('shootingHs-sysalary')) {
              selSalaryList.push(4);
            }
            if (checkPermission('shootingHs-sssalary')) {
              selSalaryList.push(5);
            }
            if (checkPermission('shootingHs-zlsalary')) {
              selSalaryList.push(6);
            }
            if (checkPermission('shootingHs-mgsalary')) {
              selSalaryList.push(7);
            }
            if (checkPermission('shootingHs-jmsalary')) {
              selSalaryList.push(8);
            }
            if (checkPermission('shootingHs-dspsalary')) {
              selSalaryList.push(9);
            }
            if (checkPermission('shootingHs-qtsalary')) {
              selSalaryList.push(10);
            }
            selSalaryList = selSalaryList.join(',');
            let _this = this;
            var pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
                selSalaryList:selSalaryList,
            };
            this.listLoading = true;
            const res = await getPersonnelPositionAsync(params);
            if (this.isloadstore) {
                this.selname = [];
                var companyName = [];
                var workPositionStr = [];
                var commissionPositionStr = [];
                var classtype = [];
                var isCyCommissionbool = [];


                res.data.list.map((item) => {
                    _this.selname.push({ 'value': item.userName, 'label': item.userName })
                    companyName.push(item.companyName)
                    workPositionStr.push({ value: item.workPosition, label: item.workPositionStr })
                    commissionPositionStr.push({ value: item.commissionPosition, label: item.commissionPositionStr })
                    classtype.push(item.classtype)
                    isCyCommissionbool.push(item.isCyCommissionbool ? '是' : '否')
                })

                this.selcompany = [...new Set(companyName)];
                // this.selwork = [...new Set(workPositionStr)];
                this.selwork = this.qcarray(workPositionStr)

                // this.selpush = [...new Set(commissionPositionStr)];
                this.selpush = this.qcarray(commissionPositionStr)
                this.seltype = [...new Set(classtype)];
                this.selissum = [...new Set(isCyCommissionbool)];
                this.isloadstore = false;

            }
            this.listLoading = false;
            this.total = res.data.total
            this.tasklist = res.data.list;
            this.tasklist1 = res.data.summary.totaldx;
            this.summaryarry = res.data.summary;
        },
        qcarray(arr) {
            var result = [];
            var obj = {};
            for (var i = 0; i < arr.length; i++) {
                if (!obj[arr[i].value]) {
                    result.push(arr[i]);
                    obj[arr[i].value] = true;
                }
            }
            return result;
        },
        onSave() {

        },
        //提交保存时验证
        onSubmitValidate: function () {
            let isValid = true;
            this.$refs.editform.validate(valid => {
                isValid = valid
            })
            return isValid;
        },
        async onSubmit() {
            if (!this.onSubmitValidate()) {
                return;
            }
            this.editform.openType = 2;
            const para = _.cloneDeep(this.editform);
            this.editLoading = true;
            var res = await addOrUpdatePersonnelPositionAsync(para);
            this.editLoading = false;
            if (!res?.success) { return; }
            this.$message({ message: this.$t('保存成功'), type: 'success' });
            this.onSearch();
            this.editformdialog = false;
        },
        async onEditAdd(row) {
            this.editformdialog = true;
            this.editLoading = true;
            this.editform.positionId = row.positionId;
            this.editform.userName = row.userName;
            this.editform.isCyCommission = row.isCyCommission;
            this.editform.commissionRate = row.commissionRate;
            this.editform.ycqts = row.ycqts;
            this.editform.basepay = row.basepay;
            this.editformTitle = "编辑";
            this.editLoading = false;
        },
        editRowEvent(row) {
            const $table = this.$refs.xTable
            $table.setActiveRow(row)
        },
        async saveRowEvent(row) {
            const $table = this.$refs.xTable
            if (!$table.isUpdateByRow(row)) {
                this.$message({ message: this.$t('数据未改动！'), type: 'success' });
                return;
            }
            this.listLoading = true
            this.editform = row;
            this.editform.openType = 2;
            this.editform.commissionRate = parseInt(row.commissionRate);
            this.editform.isCyCommission = parseInt(row.isCyCommission);
            const para = _.cloneDeep(this.editform);
            var res = await addOrUpdatePersonnelPositionAsync(para);

            if (res?.success) {
                this.$message({ message: this.$t('保存成功'), type: 'success' });
                $table.clearActived();
                row.basepay = row.jycqts == "0.00" ? 0 : (parseFloat(row.ycqts) / parseFloat(row.jycqts) * parseFloat(row.jBasepay)).toFixed(2);
                // var performanceScore = parseFloat(row.performance)>95?100:parseFloat(row.performance)<=70?0:parseFloat(row.performance);
                var ycqtsDay = row.companyType!="0" ? parseFloat(row.ycqts) >parseFloat(row.jycqts)?parseFloat(row.jycqts):parseFloat(row.ycqts):parseFloat(row.ycqts);
                row.achievement = row.jycqts == "0.00" ? 0 : ((parseFloat(row.jAchievement) * row.performance *0.01)/  parseFloat(row.jycqts) *ycqtsDay ).toFixed(2);
                if( Number(row.achievement) > Number(row.jAchievement) ){
                  row.achievement = row.jAchievement
                }
                row.sbSubsidy = row.jycqts == "0.00" ? 0 : (parseFloat(row.ycqts) / parseFloat(row.jycqts) * parseFloat(row.sbSubsidyJs)).toFixed(2);
                row.shouldFee = (parseFloat(row.basepay) + parseFloat(row.achievement) + parseFloat(row.subsidy) + parseFloat(row.overTimePay) + parseFloat(row.fuLiFee)
                    + parseFloat(row.quanqingAward)
                    + parseFloat(row.douYinFee) + parseFloat(row.kuangJinFee) + parseFloat(row.sbSubsidy) + parseFloat(row.supplyAgain)).toFixed(2);
                if (row.isCyCommission == 1) {

                    row.shouldFee = parseFloat(row.shouldFee) +
                        (parseFloat(row.vedioTaskAjustCount) + parseFloat(row.shootingAjustCount) + parseFloat(row.directImgAjustCount) + parseFloat(row.microDetailAjustCount)
                            + parseFloat(row.shopDecorationAjustCount)
                            + parseFloat(row.packingDesignAjustCount) + parseFloat(row.dayModImgAjustCount) + parseFloat(row.otherAjustCount))
                }
                row.actualFee = (parseFloat(row.shouldFee) - parseFloat(row.shuiDianFee)
                    - parseFloat(row.cutPayment) - parseFloat(row.chiDaoFee) - parseFloat(row.zhaoTuiFee) - parseFloat(row.queKaFee)
                    - parseFloat(row.kuangGongFee) - parseFloat(row.yuZhiFee) - parseFloat(row.socialSecurityFee) - parseFloat(row.otherFee)).toFixed(2);

                $table.reloadRow(row, {})
            }

            this.listLoading = false;
        },
        cancelRowEvent(row) {
            this.$refs.xTable.revertData(row);
            this.$refs.xTable.clearActived();
        },
        formatNumber(number){
            const absNumber = Math.abs(number);
            const options = {
                minimumFractionDigits: absNumber >= 100 ? 0 : 2,
                maximumFractionDigits: absNumber >= 100 ? 0 : 2,
            };
            return new Intl.NumberFormat('zh-CN', options).format(number);
            }, 
        footerMethod({ columns, data }) {
            const sums = [];
            if (!this.summaryarry)
                return sums
            var arr = Object.keys(this.summaryarry);
            if (arr.length == 0)
                return sums
            //const { columns, data } = param;
            var hashj = false;
            columns.forEach((column, index) => {
                if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
                    var sum = this.summaryarry[column.property + '_sum'];
                    if (sum == null) return;
                    else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
                    else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum.toFixed(2)
                    else sums[index] = this.formatNumber(sum)
                }
                else sums[index] = ''
            });
            /*  if (this.summarycolumns.length == 0) {
                  this.summarycolumns = columns;
                  this.initsummaryEvent();
              } */
            return [sums]
        },
        commissionChange() {

        }
    },
};
</script>
<style lang="scss" scoped>
.content {
    display: flex;
    flex-direction: row;
    width: 100%;
    // height: 100%;
    height: 81vh;
}

::v-deep ::-webkit-scrollbar {
    height: 10px;
    width: 10px;
}

/*滚动条整体部分*/
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
    background-color: #A8A8A8;
}

.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
    background-color: #787878;
}

/*滚动条整体部分*/
.mytable-scrollbar20221212 ::-webkit-scrollbar {
    width: 18px;
    height: 26px;
}

/*滚动条的轨道*/
.mytable-scrollbar20221212 ::-webkit-scrollbar-track {
    background-color: #f1f1f1;
}

/*滚动条里面的小方块，能向上向下移动*/
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 3px;
    box-sizing: border-box;
    border: 2px solid #F1F1F1;
    box-shadow: inset 0 0 6px rgba(255, 255, 255, .5);
}

// 滚动条鼠标悬停颜色
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
    background-color: #A8A8A8;
}

// 滚动条拖动颜色
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
    background-color: #787878;
}

/*边角，即两个滚动条的交汇处*/
.mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
    background-color: #dcdcdc;
}

.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
    background-color: #787878;
}

/*滚动条整体部分*/
.mytable-scrollbar20221212 ::-webkit-scrollbar {
    width: 18px;
    height: 26px;
}

/*滚动条的轨道*/
.mytable-scrollbar20221212 ::-webkit-scrollbar-track {
    background-color: #f1f1f1;
}

/*滚动条里面的小方块，能向上向下移动*/
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 3px;
    box-sizing: border-box;
    border: 2px solid #F1F1F1;
    box-shadow: inset 0 0 6px rgba(255, 255, 255, .5);
}

// 滚动条鼠标悬停颜色
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
    background-color: #A8A8A8;
}

// 滚动条拖动颜色
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
    background-color: #787878;
}

/*边角，即两个滚动条的交汇处*/
.mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
    background-color: #dcdcdc;
}

::v-deep .vxetoolbar20221212{
    top: 30px !important;
}
::v-deep .bzbjbt {
  height: 45px;
  background-color: rgb(255, 255, 255);
  font-size: 18px;
  color: #666;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  // padding: 20px 35px;
}
.xptcblbj2 {
  width: 100%;
  box-sizing: border-box;
  padding: 40px 25px 10px 25px;
}
.jxbt {
  width: 100%;
  height: 80px;
  font-size: 30px;
  font-weight: bold;
  letter-spacing: 2px;
  /* background-color: aquamarine; */
  text-align: center;
}

.jxan {
  width: 100%;
  height: 38px;
  /* background-color: aquamarine; */
  display: flex;
}
</style>

