<template>
    <my-container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
               
            </el-form>
        </template>

        <el-tabs v-model="activeName" style="height: 93%;">
          <el-tab-pane label="抖音账号添加" name="first" style="height:100%;">
            <AccountNumberDY :filter="filter" :filterDetail="Filter" ref="AccountNumberDY" style="height:100%"></AccountNumberDY>
        </el-tab-pane>
        <el-tab-pane label="抖音新品推荐详情" name="third" style="height:100%;">
            <ProductNewRecommendDY :filter="filter" :filterDetail="Filter" ref="ProductNewRecommendDY" style="height:100%"></ProductNewRecommendDY>
        </el-tab-pane>
      </el-tabs>

    </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import { getDirectorList, getDirectorGroupList,getProductBrandPageList,getList as getshopList } from '@/api/operatemanage/base/shop'
import { platformlist} from '@/utils/tools'
import {getAllProBrand} from '@/api/inventory/warehouse'
import {pageProductNewAsync, queryGuardProductNewsis, getProductStateName, getProductAdveClick} from '@/api/operatemanage/base/product'
import {getcusgroups,} from "@/api/customerservice/customergroup";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import AccountNumberDY from "./AccountNumberDY.vue";
import ProductNewRecommendDY from "./ProductNewRecommendDY.vue";


const startTime = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");
const star = formatTime(dayjs().subtract(7,'day'), "YYYY-MM-DD");
export default {
    name: 'YunhanAdminProductnewtab',
    components : {MyContainer, MyConfirmButton, MySearch, MySearchWindow, AccountNumberDY,ProductNewRecommendDY},

    data() {
        return {
            that:this,
            activeName: 'first',    
            filter: {
                startTime: null,
                endTime: null,
                timerange:[startTime, endTime],
                procode:null,
                title:null,
                platform:null,
                shopCode:null,
                groupId:null,
                operateSpecialId:null,
                user1Id:null,
                user3Id:null,
                shopId:null,
                platform:null,
                newPattern:null,
                customer:null,
            },
            detailfilter: {
                procode: null,
                platform:null,
                startTime: null,
                endTime: null,
                timerange:[star, endTime]
            },
            Filter:{
                StartDate:null,
                EndDate:null,
                timerange:[startTime, endTime]
            },
            personpager: {
                OrderBy: "rensuccess",
                pageSize: 200,
                pageIndex: 1,
                IsAsc: false,
            },
            platformlist:platformlist,
            platformList: [],
            grouplist: [],    
            brandlist: [],
            directorList:[], 
            productnewList:[],
            cusgroupslist:[],
            shopList:[],
            directorGroupList:[],
            opList:[],
            total: 0,
            sels: [], 
            selids: [], 
            pickerOptions: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                    picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近半个月',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                    picker.$emit('pick', [start, end]);
                    }
                },{
                    text: '最近一个月',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                    picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                    picker.$emit('pick', [start, end]);
                    }
                }]
            },
        };
    },

    async mounted() {
        await this.onSearch()
        await this.getDirectorlist()
        await this.init()
        await this.getPruductNewState()
        await this.getProductCustomer()
    },

    methods: {
        async getPruductNewState() {
            var res = await getProductStateName();
            if (res?.code){
                this.productnewList = res.data.map(function (item) {
                var ob = new Object();
                ob.state = item;
                return ob;
                })
            } 
        },
        async getProductCustomer() {
            var g = await getcusgroups({});

            this.cusgroupslist = g.data.list.map(function (item) {
                var ob = new Object();
                ob.state = item;
                return ob;
            });
        },
        async getDirectorlist() {
            const res1 = await getDirectorList({})
            const res2 = await getDirectorGroupList({})     
            const res3 = await getProductBrandPageList()
            
            this.directorList = res1.data
            this.directorGroupList =[{key:'0',value:'未知'}].concat(res2.data ||[]);
            this.bandList = res3.data?.list
        },
        async onchangeplatform(val){
            this.categorylist =[]
            const res1 = await getshopList({platform:val,CurrentPage:1,PageSize:300});
            this.shopList=res1.data.list
        },
        async init(){
            var res= await getAllProBrand();
            this.brandlist = res.data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        async onSearch(){
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange&&this.filter.timerange.length>1) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            if (this.Filter.timerange) {
                this.Filter.StartDate = this.Filter.timerange[0];
                this.Filter.EndDate = this.Filter.timerange[1];
            }
            
            this.$nextTick(async () =>{
                
                if(this.activeName == 'first')
                    await this.$refs.AccountNumberDY.onSearch()
                    else if (this.activeName == 'third')
                    await this.$refs.ProductNewRecommendDY.onSearch()
            })
        }
    },
};
</script>

<style lang="scss" scoped>

</style>