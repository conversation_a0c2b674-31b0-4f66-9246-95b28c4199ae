<template>
    <container>
        <template #header>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            @select='selectchange' :isSelection='false' :isSelectColumn='true' :tableData='list' :tableCols='tableCols'
            :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry' :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input placeholder="主仓" v-model="filter1.warehouse_main" style="width: 150px" clearable></el-input>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;margin-left: 1px;">
                        <el-input placeholder="子仓" v-model="filter1.namewarehouse" style="width: 150px" clearable></el-input>
                    </el-button>
                </el-button-group>
            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sel.length" @get-page="getlist" />
        </template>
    </container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { GetPackageAndOutwarehousefeeWarehouseMain, ExportPackageAndOutwarehousefeeWarehouseMain } from '@/api/monthbookkeeper/financialDetail'
import container from '@/components/my-container/noheader'
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";

const tableCols = [
    { istrue: true, prop: 'yearMonth', label: '年月', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'version', label: '版本', sortable: 'custom', width: '100',formatter:(row)=>!row.version?"": row.version=='v1'?"工资月报":"参考月报" },
    { istrue: true, prop: 'warehouse_main', label: '主仓', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'namewarehouse', label: '子仓', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'bzsj', label: '实际包装费', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'bzyg', label: '预估包装', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'bzleft', label: '包装分摊', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'cost_total', label: '总包装费', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'bzordercount', label: '订单量（包装）', sortable: 'custom', width: '140' },
    { istrue: true, prop: 'wages', label: '仓库薪资', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'wages_yun', label: '仓库薪资（运）', sortable: 'custom', width: '140' },
    { istrue: true, prop: 'out_yun', label: '出仓成本（运）', sortable: 'custom', width: '140' },
    { istrue: true, prop: 'outsj', label: '真实出仓成本', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'outyg', label: '预估出仓成本', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'out_total', label: '出仓成本', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'xzordercount', label: '订单量（仓库薪资）', sortable: 'custom', width: '180' },
    { istrue: true, prop: 'amountsaleje', label: '销售额（仓库薪资）', sortable: 'custom', width: '180' },
];		

const tableHandles = [
    { label: "导出", handle: (that) => that.onExport() },
]
export default {
    name: 'packageAndOutwarehousefeeWarehouseMain',
    components: { cesTable, container, vxetablebase, MyConfirmButton },
    props: {
        filter: {}
    },
    data() {
        return {
            that: this,
            filter1: {
                warehouse_main: '',
                namewarehouse: ''
            },
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "yearMonth", IsAsc: false },
            total: 0,
            sel: [],
            listLoading: false,
            summaryarry: null,
        }
    },
    mounted() {

    },
    beforeUpdate() { },
    methods: {
        onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ...this.filter, ...this.filter1 }
            this.listLoading = true
            const res = await GetPackageAndOutwarehousefeeWarehouseMain(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res.data?.total ?? 0
            const data = res.data?.list
            this.summaryarry = res.data?.summary;
            data?.forEach(d => {
                d._loading = false
            })
            this.list = data
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async onExport(){
            let pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter, ...this.filter1 }
            const res = await ExportPackageAndOutwarehousefeeWarehouseMain(params);
            if (!res?.data) return;
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            aLink.setAttribute("download", '仓库查询界面_' + new Date().toLocaleString() + '.xlsx');
            aLink.click();
        }
    }
}
</script>
