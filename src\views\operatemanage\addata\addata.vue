<template>
  <my-container v-loading="pageLoading">
    <template #header>
     <div>
       <el-button-group>
          <el-button style="padding: 0;margin: 0;">
             <el-input v-model.trim="filter.proCode" clearable placeholder="商品ID" style="width:150px;"/>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
             <el-input v-model.trim="filter.goodsName" clearable placeholder="商品名称" style="width:160px;"/>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
              <el-date-picker style="width: 210px" clearable v-model="filter.yearmonth" type="datetimerange" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </el-button>
      
        <el-button type="primary" @click="onSearch">查询</el-button>
       </el-button-group>
      
     </div>
    </template>
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn="true"  
          :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='financialreportlist' @summaryClick='onsummaryClick'
          :tableCols='tableCols' :tableHandles='tableHandles'  :loading="listLoading" style="width:100%;height:96%;margin: 0">
       <template slot='extentbtn'>
         
       </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
    </template>

<el-dialog title="导入数据" :visible.sync="dialogVisibleData" width="30%" v-dialogDrag>
      <span>
       
          <el-row>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
              <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action accept=".xlsx"
              :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
              <template #trigger>
                  <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
            </el-upload>
            </el-col>
          </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleData = false">关闭</el-button>
      </span>
    </el-dialog>

  </my-container>
</template>
<script>
import {getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
import {getDirectorGroupList,getDirectorList} from '@/api/operatemanage/base/shop'
import {getProductAD,importProductPDD,importProductListPDD,importPDDAdv} from '@/api/operatemanage/addata/addata'
import {importProductDayReport,ImportBusinessStaffPlatForm} from '@/api/bookkeeper/import'
import productdrchart from '@/views/bookkeeper/reportday/productdrchart'
import BusinessStaffPlatForm from '@/views/bookkeeper/reportday/BusinessStaffPlatForm'
import {formatPlatform,formatTime,formatYesornoBool,formatLinkProCode} from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import InputMult from "@/components/Comm/InputMult";
import { Loading } from 'element-ui';
import { ruleDirectorGroup } from '@/utils/formruletools'
import freightDetail from '@/views/bookkeeper/reportday/freightDetail'
import buschar from '@/components/Bus/buschar'
import importmodule from '@/components/Bus/importmodule'
import expressfreightanalysis from '@/views/express/expressfreightanalysis'
import ordergiftdetail from '@/views/order/gift/ordergiftDetail'
let loading;
const startLoading = () => {
  loading = Loading.service({
  lock: true,
  text: '加载中……',
  background: 'rgba(0, 0, 0, 0.7)'
  });
}; 
const tableCols =[
         {istrue:true,fixed:true,prop:'yearMonth',label:'日期',sortable:'custom', width:'80'},
        {istrue:true,fixed:true,prop:'goodsId',fix:true,label:'商品ID', width:'80',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.goodsId)},
        {istrue:true,fixed:true,prop:'goodsName',label:'商品名称',sortable:'custom', width:'100'},
         {istrue:true,summaryEvent:true,prop:'promotionPlan',label:'推广计划',sortable:'custom', width:'80'},
         {istrue:true,summaryEvent:true,prop:'state',label:'状态',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'dataType',label:'数据类型',sortable:'custom', width:'80'},
         {istrue:true,summaryEvent:true,prop:'cate1Id',label:'分类ID1',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'cate1Name',label:'分类ID1名称',sortable:'custom', width:'120'},
         {istrue:true,summaryEvent:true,prop:'cate2Id',label:'分类ID2',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'cate2Name',label:'分类ID2名称',sortable:'custom', width:'120'},
         {istrue:true,summaryEvent:true,prop:'cate3Id',label:'分类ID3',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'cate3Name',label:'分类ID3名称',sortable:'custom', width:'120'},
         {istrue:true,summaryEvent:true,prop:'cat_id_4',label:'分类ID4',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'cat_name_4',label:'分类ID4名称',sortable:'custom', width:'120'},
        {istrue:true,summaryEvent:true,prop:'goodsFavCnt',label:'商品收藏数',sortable:'custom', width:'120'},
        {istrue:true,summaryEvent:true,prop:'goodsUv',label:'点击量',sortable:'custom', width:'100'},
         {istrue:true,summaryEvent:true,prop:'clickRate',label:'点击率(%)',sortable:'custom', width:'120',formatter:(row)=>row.clickRate+"%"},
         {istrue:true,summaryEvent:true,prop:'roi',label:'投入产出比',sortable:'custom', width:'120',formatter:(row)=>row.roi+"%"},
         {istrue:true,summaryEvent:true,prop:'goodsPv',label:'曝光量',sortable:'custom', width:'100'},
        {istrue:true,summaryEvent:true,prop:'payOrdrCnt',label:'支付订单数',sortable:'custom', width:'100'},
        {istrue:true,summaryEvent:true,prop:'goodsVcr',label:'支付转化率(%)',sortable:'custom', width:'120',formatter:(row)=>row.goodsVcr+"%"},
        {istrue:true,summaryEvent:true,prop:'payOrdrGoodsQty',label:'支付件数',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'payOrdrUsrCnt',label:'支付买家数',sortable:'custom', width:'100',type:'custom'},
        {istrue:true,summaryEvent:true,prop:'payOrdrAmt',label:'支付金额',sortable:'custom', width:'80'},
         {istrue:true,summaryEvent:true,prop:'zjSuccessAmount',label:'直接交易额（元）',sortable:'custom', width:'140'},
         {istrue:true,summaryEvent:true,prop:'jjSuccessAmount',label:'间接交易额（元）',sortable:'custom', width:'140'},
         {istrue:true,summaryEvent:true,prop:'zjSuccessNumber',label:'直接成交笔数',sortable:'custom', width:'100'},
         {istrue:true,summaryEvent:true,prop:'jjSuccessNumber',label:'间接成交笔数',sortable:'custom', width:'100'},
        {istrue:true,summaryEvent:true,prop:'everyNumberSuccessCost',label:'每笔成交花费（元）',sortable:'custom', width:'140'},
        {istrue:true,summaryEvent:true,prop:'everyNumberSuccessAmount',label:'每笔成交金额',sortable:'custom', width:'120'},
        {istrue:true,summaryEvent:true,prop:'zjEveryNumberSuccessAmount',label:'每笔直接成交金额',sortable:'custom', width:'140'},
        {istrue:true,summaryEvent:true,prop:'jjEveryNumberSuccessAmount',label:'每笔间接成交金额',sortable:'custom', width:'140'},
        {istrue:true,summaryEvent:true,prop:'clickCost',label:'扣费点击',sortable:'custom', width:'80'},
         {istrue:true,summaryEvent:true,prop:'clickCostAvg',label:'平均点击花费',sortable:'custom', width:'120'},
         {istrue:true,summaryEvent:true,prop:'totalStationFeeRate',label:'全站推广费比',sortable:'custom', width:'120',formatter:(row)=>row.totalStationFeeRate+"%"},
        
          {istrue:true,summaryEvent:true,prop:'thousandExposures',label:'千次曝光花费（元）',sortable:'custom', width:'140'},
         {istrue:true,summaryEvent:true,prop:'shopFocusNumber',label:'店铺关注数',sortable:'custom', width:'100'},
         {istrue:true,summaryEvent:true,prop:'shopFocusAmount',label:'店铺关注成本',sortable:'custom', width:'100'},
         {istrue:true,summaryEvent:true,prop:'vNumber',label:'询单量',sortable:'custom', width:'80'},
         {istrue:true,summaryEvent:true,prop:'suctionPowderRate',label:'吸粉率',sortable:'custom', width:'80',formatter:(row)=>row.suctionPowderRate+"%"},
        {istrue:true,summaryEvent:true,prop:'liveWatchNumber',label:'直播观看量',sortable:'custom', width:'100'},
         {istrue:true,summaryEvent:true,prop:'liveCommentsNumber',label:'直播评论量',sortable:'custom', width:'100'},
        
        {istrue:true,summaryEvent:true,prop:'useMoney',label:'花费（元）',sortable:'custom', width:'100'},
        {istrue:true,summaryEvent:true,prop:'adStrategyDesc',label:'智能推广',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'imprUsrCnt',label:'流量损失指数',sortable:'custom', width:'100'},
        {istrue:true,summaryEvent:true,prop:'ordrVstrRto',label:'下单率(%)',sortable:'custom', width:'100',formatter:(row)=>row.ordrVstrRto+"%"},
      
        {istrue:true,summaryEvent:true,prop:'video_orderUV',label:'视频成交人数',sortable:'custom', width:'100'},
        {istrue:true,summaryEvent:true,prop:'video_playCount',label:'视频播放量',sortable:'custom', width:'100'},
        {istrue:true,summaryEvent:true,prop:'video_totalGMV',label:'视频成交金额',sortable:'custom', width:'100'},
        {istrue:true,summaryEvent:true,prop:'video_orderCount',label:'视频订单数',sortable:'custom', width:'100'},
         {istrue:true,summaryEvent:true,prop:'created_at',label:'创建时间',sortable:'custom', width:'80'}  
        ]
const tableHandles=[  
        {label:"导入商品列表", handle:(that)=>that.startImport(2)},
        {label:"导入商品明细", handle:(that)=>that.startImport(1)},
        {label:"导入广告明细", handle:(that)=>that.startImport(3)},
       
        // {label:"模板-广告数据", handle:(that)=>that.downloadTemplate()},
        
         {label:"刷新", handle:(that)=>that.onRefresh()}
      ];

export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable,productdrchart,InputMult,freightDetail,buschar,expressfreightanalysis,importmodule,ordergiftdetail,BusinessStaffPlatForm},
  data() {
    return {
      that:this,
      numberid:'',
      filter: {
        proCode:null,
        goodsName:null,
        brandId:null,
        groupId:null,
        StartDate: null,
        EndDate: null,
        yearmonth:null
      },
      onimportfilter:{
        yearmonthday:null,
      },
      shopList:[],
      userList:[],
      brandlist:[],
      grouplist:[],
      directorlist:[],
      financialreportlist: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      total: 0,
      pager:{OrderBy:"",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      summaryarry:{},
      selids:[],
      fileList:[],
      dialogVisibleData:false,
      dialogVisible:false,
      uploadLoading:false,
      importFilte:{},
      fileList:[],
      fileparm:{},
      editparmVisible:false,
      editLoading:false,
      editparmLoading:false,
      drawervisible:false,
      /* dialogDrVisibleShengYi:false, */
      dialogDrVisible:false,
      expressfreightanalysisVisible:false,
      drparamProCode:'',
      autoformparm:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[]
        },
      freightDetail:{
        visible:false,
        filter:{
          proCode:null,
          timeCalc:[]
        }
      },
      giftDetail:{visible:false},
      costDialog:{visible:false,rows:[]},
      buscharDialog:{visible:false,title:"",data:[]},
      drawervisible:false,
    };
  },
  async mounted() {
  },
  async created() {
    await this.init()
    await this.getShopList();
    /* await this.initformparm(); */
  },
  methods: {
    
     //导入
    async uploadFile1(item) {
      /* if(!this.fileHasSubmit){
        return false;
      } */
      this.fileHasSubmit=false;
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      form.append("yearmonthday", this.onimportfilter.yearmonthday);
      console.log('111',form)
      const res =await importProductPDD(form);
      if (res.code==1)   this.$message({ message: "上传成功,正在导入中...", type: "success" });
      else  this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading=false;    
      },
    async uploadChange(file, fileList) {
    if (fileList && fileList.length > 0) {
      var list = [];
      for(var i=0;i<fileList.length;i++){
        if(fileList[i].status=="success")
          list.push(fileList[i]);
        else
          list.push(fileList[i].raw);
      }
      this.fileList = list;
    }
    },
    uploadRemove(file, fileList){
       this.uploadChange(file, fileList);
    },
    //导入数据弹框
    startImport(number) {
      this.numberid=number;
      console.log("导入的number",this.numberid)
      this.dialogVisibleData = true;
    },
   
    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init(){
        var date1 = new Date(); date1.setDate(date1.getDate()-10);
        var date2 = new Date(); date2.setDate(date2.getDate()-1);
        this.filter.timeCalc=[];
        this.filter.timeCalc[0]=this.datetostr(date1);
        this.filter.timeCalc[1]=this.datetostr(date2);
      },
    
   async getShopList(){
      const res1 = await getAllShopList();
      this.shopList=[];
        res1.data?.forEach(f => {
          if(f.isCalcSettlement&&f.shopCode&&(f.platform==1||f.platform==8))
              this.shopList.push(f);
        });
        var res2= await getDirectorGroupList();
        this.grouplist = res2.data?.map(item => {return { value: item.key, label: item.value };}); 

        var res3= await getDirectorList();
        this.directorlist = res3.data?.map(item => {return { value: item.key, label: item.value };}); 

       /*  var res4= await getAllProBrand(); */
        /* this.brandlist = res4.data.map(item => {
            return { value: item.key, label: item.value };
        }); */
    },
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
     await this.onSearch();
    },
    onRefresh(){
      this.onSearch()
    },
    async onSearch(){
      this.$refs.pager.setPage(1);
      await this.getList().then(res=>{  });
      // loading.close();
    },
    async getList(){
      this.filter.StartDate =null;
      this.filter.EndDate =null;
      if (this.filter.yearmonth) {
        this.filter.StartDate = this.filter.yearmonth[0];
        this.filter.EndDate = this.filter.yearmonth[1];
      }
      var that=this;
      var pager = this.$refs.pager.getPager();
      const params = {...pager,...this.pager,...this.filter};
     // this.listLoading = true;
      startLoading(); 
      const res = await getProductAD(params).then(res=>{
          loading.close();
          that.total = res.data?.total;
          if(res?.data?.list&&res?.data?.list.length>0){
            for (var i in res.data.list) {
              if (!res.data.list[i].freightFee) {
                res.data.list[i].freightFee =" ";
              }
            }
          }
          that.financialreportlist = res.data?.list;
          that.summaryarry=res.data?.summary;
      });
    },
    showFreightDetail(row){
        if(row.freightFee>=1){
          this.freightDetail.filter.proCode = row.proCode;
          if (row.yearMonthDay != null){
            var dayStr =row.yearMonthDay.substr(0,4)+'-'+row.yearMonthDay.substr(4,2)+'-'+row.yearMonthDay.substr(6,2)
            this.freightDetail.filter.timeCalc = [dayStr,dayStr];
          }
          else {
            this.freightDetail.filter.timeCalc = this.filter.timeCalc
          }
          this.freightDetail.visible=true; 
          setTimeout(async () => {
            await this.$refs.freightDetail.onSearch(); 
          }, 100);
        }
    },
   async showGiftDetail(row){
      this.giftDetail.visible=true;
      let _th=this;
      await this.$nextTick(async () => {  await _th.$refs.ordergiftdetail.onShow(row.yearMonthDay,row.proCode); });
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
   onRefresh(){
        this.onSearch()
    },
  async updateruleGroup(groupid) {
     if(!groupid)
        this.autoformparm.fApi.resetFields()
     else{
       const res = await getParm({groupId:groupid})
       var arr = Object.keys(this.autoformparm.fApi);
       res.data.groupId=groupid;
       if(!res.data?.Profit3PredictRate) res.data.Profit3PredictRate=0;
       if(!res.data?.ShareRate) res.data.ShareRate=0;
       await this.autoformparm.fApi.setValue(res.data)
      }
    },
  async showprchart2(prcode){
      window['lastseeprcodedrchart']=prcode

      this.drparamProCode=prcode
      this.dialogDrVisible=true
      /* this.dialogDrVisibleShengYi=true */
   },
  async onstartImport(){
      this.dialogVisible=true;
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      /* if (!this.onimportfilter.yearmonthday) {
       this.$message({type: 'warning',message: '请选择年月!'});
       return;
      } */
      this.uploadLoading=true
      this.$refs.upload.submit();
    },
   async uploadFile(item) {
       if(this.numberid==1)
       {
           if(!item||!item.file||!item.file.size){
        this.$message({message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("platform", 1);
     
      var res = await importProductPDD(form);
      if (res.code==1) this.$message({message: "上传成功,正在导入中...", type: "success" });
      else this.$message({message: res.msg, type: "warning" });
      this.uploadLoading=false

       }
       else if(this.numberid==2)
       {
          if(!item||!item.file||!item.file.size){
        this.$message({message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("platform", 1);
     
      var res = await importProductListPDD(form);
      if (res.code==1) this.$message({message: "上传成功,正在导入中...", type: "success" });
      else this.$message({message: res.msg, type: "warning" });
      this.uploadLoading=false

       }


       else if(this.numberid==3)
       {
          if(!item||!item.file||!item.file.size){
        this.$message({message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("platform", 1);
     
      var res = await importPDDAdv(form);
      if (res.code==1) this.$message({message: "上传成功,正在导入中...", type: "success" });
      else this.$message({message: res.msg, type: "warning" });
      this.uploadLoading=false

       }
     
    },
  async onExport(){
      this.filter.startTime =null;
      this.filter.endTime =null;
      if (this.filter.timeCalc) {
        this.filter.startTime = this.filter.yearmonth[0];
        this.filter.endTime = this.filter.yearmonth[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = {  ...pager,   ...this.pager,   ...this.filter};
      var res= await exportFinancialDayReport(params);
      if(!res?.data) {
         this.$message({message:"没有数据",type:"warning"});
         return
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download','财务账单数据' +  new Date().toLocaleString() + '_.xlsx' )
      aLink.click()
    },
  async onShowEditParm(){
      this.editparmVisible = true
      const res = await getParm()
      var arr = Object.keys(this.autoformparm.fApi);
      if(arr.length >0)
         this.autoformparm.fApi.resetFields()
      await this.autoformparm.fApi.setValue(res.data)
    },
  async onSetEditParm(){
      this.editparmLoading=true;
      await this.autoformparm.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoformparm.fApi.formData();
          const res = await setParm(formData);
          if(res.code==1) this.editparmVisible=false;
        }else{}
     })
     this.editparmLoading=false;      
  },
  async onsummaryClick(property){
    // this.$message({message:property,type:"warning"});
      this.filter.startTime =null;
      this.filter.endTime =null;
      if (this.filter.timeCalc) {
        this.filter.startTime = this.filter.yearmonth[0];
        this.filter.endTime = this.filter.yearmonth[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = {...pager,...this.pager,...this.filter};
      params.column=property;
      let that=this;
      const res = await queryTXDayReportAnalysis(params).then(res=>{
        that.buscharDialog.visible=true;
        that.buscharDialog.data=res.data
        that.buscharDialog.title=res.data.legend[0]
      });
  },
  showCost(row){
    if(row.replaceSendCost >0){
      this.costDialog.visible =true;
      this.costDialog.rows=[row];
    }
  },
  showexpressfreightanalysis() {
    this.expressfreightanalysisVisible=true;
    this.$nextTick(() => { this.$refs.expressfreightanalysis.onSearch()});
  },
  renderCost(row){
    if(row.replaceSendCost >0){
      return "color:blue;cursor:pointer;";
    }
    else{
      return "";
    }
  },
 
},
  
};
</script>
<style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
</style>
 
 