<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="">
                    <el-select ref="selectdepartment" v-model="filter.department" clearable style="width: 200px" size="mini"
                        placeholder="招聘部门" @clear="() => { filter.departmentId = null }">
                        <el-option hidden value="一级菜单" :label="filter.department"></el-option>
                        <el-tree style="width: 200px;" :data="deptList" :props="defaultProps" :expand-on-click-node="false"
                            :check-on-click-node="true" @node-click="handledeptNodeClick">
                        </el-tree>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.keywords" placeholder="输入部门名称、岗位名称或模板名称" style="width:200px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.isEnabled" placeholder="是否启用" size="mini" style="width:120px;" clearable>
                        <el-option label="是" :value="true"></el-option>
                        <el-option label="否" :value="false"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">筛选</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onAddTemple">新增工资模板</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="downloadTemplate">下载薪资确认单模板</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onstartImport">导入</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            style="height: calc(100vh - 255px);" :tableData='datalist' @select='selectchange' :isSelection="false"
            :tableCols='tableCols' :isSelectColumn='true' :customRowStyle="customRowStyle" :loading="listLoading"
            :summaryarry="summaryarry" :isBorder="false">
            <template slot='extentbtn'> </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getDataList" />
        </template>
        <!-- 薪资模板新增修改 -->
        <el-dialog :title="title" :visible.sync="showFinishDialog" width="30%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag>
            <el-form label-width="80px" :model="loseForm" ref="templateForm" :rules="rule">
                <el-form-item label="名称" prop="templateName">
                    <el-input placeholder="名称" v-model="loseForm.templateName" maxlength="30">
                    </el-input>
                </el-form-item>
                <el-form-item label="部门" prop="department">
                    <el-select ref="selectDept" v-model="loseForm.department" clearable
                        @clear="() => { loseForm.departmentId = null }" style="width: 100%" size="mini" placeholder="招聘部门">
                        <el-option hidden value="一级菜单" :label="loseForm.department"></el-option>
                        <el-tree style="width: 200px;" :data="deptList" :props="defaultProps" :expand-on-click-node="false"
                            :check-on-click-node="true" @node-click="handleNodeClick">
                        </el-tree>
                    </el-select>
                </el-form-item>
                <el-form-item label="岗位" prop="position">
                    <el-select ref="selectPosit" v-model="loseForm.position" style="width: 100%;">
                        <!-- <el-option v-for="item in postList" :label="item.positionName"
                            :value="item.positionName"></el-option> -->
                        <el-option hidden value="一级菜单" :label="loseForm.position"></el-option>
                        <el-tree style="width: 200px;" :data="postList" :props="postProps" :expand-on-click-node="false"
                            :check-on-click-node="true" @node-click="handleNodePostClick">
                        </el-tree>
                    </el-select>
                </el-form-item>
                <el-form-item label="基本工资" prop="baseSalary">
                    <el-input-number placeholder="基本工资"  :controls="false" :min="1" :max="99999999" style="width: 100%" :precision="2"
                        v-model="loseForm.baseSalary">
                   </el-input-number>
                </el-form-item>
                <el-form-item label="岗位工资">
                    <el-input-number placeholder="岗位工资"  :controls="false" :min="0" :max="99999999" style="width: 100%" :precision="2"
                        v-model="loseForm.positionSalary">
                    </el-input-number>
                </el-form-item>
                <el-form-item label="绩效工资">
                    <el-input-number  placeholder="绩效工资" :controls="false" :min="0" :max="99999999" style="width: 100%" :precision="2"
                        v-model="loseForm.performanceSalary">
                   </el-input-number>
                </el-form-item>
                <el-form-item label="小时工资">
                    <el-input-number placeholder="小时工资" :controls="false" :min="0" :max="99999999" style="width: 100%" :precision="2"
                        v-model="loseForm.hourlyWage">
                   </el-input-number>
                </el-form-item>
                <el-form-item label="竞业补贴">
                    <el-select v-model="loseForm.competitionAllowanceType" placeholder="竞业补贴" size="mini"
                        @change="oncompetitionAllowanceType" clearable style="width: 100%"
                        @clear="() => { loseForm.competitionAllowanceAmount = 0 }">
                        <el-option label="无" value="无"></el-option>
                        <el-option label="运营专员" value="运营专员"></el-option>
                        <el-option label="IT部" value="IT部"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="社保补贴">
                    <el-select v-model="loseForm.socialSecurityType" placeholder="社保补贴" size="mini"
                        @change="onsocialSecurityType" clearable style="width: 100%"
                        @clear="() => { loseForm.socialSecurityAmount = 0 }">
                        <el-option label="毕业" value="毕业"></el-option>
                        <el-option label="实习生" value="实习生"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="保密费">
                    <el-input-number placeholder="保密费" :controls="false" :min="0" :max="99999999"  style="width: 100%" :precision="2"
                        v-model="loseForm.secrecyFee">
                    </el-input-number>
                </el-form-item>
                <el-form-item label="合计薪资">
                    <el-input-number placeholder="合计薪资" disabled :controls="false"  style="width: 100%" :precision="2"
                        v-model="totalSalary">
                    </el-input-number>
                </el-form-item>
                <el-form-item label="是否启用">
                    <el-radio-group v-model="loseForm.isEnabled">
                        <el-radio :label="true">是</el-radio>
                        <el-radio :label="false">否</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showFinishDialog = false">取 消</el-button>
                    <el-button type="primary" @click="loseSubmit">保存</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 导入 -->
        <el-dialog title="导入薪资模板" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
            <span>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action
                            accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="submitUpload">{{ uploadLoading ? "上传中" : "上传" }}
                            </el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import cesTable from "@/components/Table/tableforvedio.vue";
import MyContainer from "@/components/my-container";
import { AllDDDeptTreeNcWh, getDingRolesTree } from '@/api/profit/personnel'
import {
    pageSalaryTemplate, createSalaryTemplate,
    editSalaryTemplate, deleteSalaryTemplate, getSalaryTemplate, exportSalaryTemplate, importSalaryTemplate
} from '@/api/profit/hr'


const tableCols = [
    { istrue: true, prop: 'templateName', align: 'left', label: '模板名称',sortable: 'custom', },
    { istrue: true, prop: 'position', align: 'left', label: '岗位名称',sortable: 'custom', },
    { istrue: true, prop: 'department', align: 'left', label: '部门',sortable: 'custom', },
    { istrue: true, prop: 'createTime', align: 'left', label: '创建时间',sortable: 'custom', },
    { istrue: true, prop: 'creator', align: 'left', label: '创建人' ,sortable: 'custom',},
    { istrue: true, prop: 'isEnabled', align: 'left', label: '是否启用', formatter: (row) => row.isEnabled==null ?'':row.isEnabled ? '是' : '否',sortable: 'custom', },
    {
        istrue: true, type: "button", label: '操作', width: "240",
        btnList: [
            { label: "修改", permission: "", handle: (that, row) => that.editPostion(row.templateId) },
            { type: "danger", permission: "", label: "删除", handle: (that, row) => that.deletePostion(row.templateId) }
        ]
    }
];
export default {
    name: "salaryTemplate",//薪资模板
    components: {
        MyContainer, cesTable,
    },
    props: {
        showDialog: {
            type: Boolean,
            default: () => { return false; }
        },
        diologTitle: {
            type: String,
            default: () => { return ''; }
        },
    },
    data () {
        return {
            chooseName: '',
            defaultProps: {
                children: 'childDeptList',
                label: 'name'
            },
            deptList: [],
            postProps: {
                children: 'roles',
                label: 'name'
            },
            postList: [],
            filter: {
                departmentId: null,//
                department: null,
                position: null,//
                templateName: null,//
                isEnabled: null,
                keywords: null,
            },
            loseForm: {
                departmentId: null,
                templateName: null,
                department: null,
                position: null,
                baseSalary: 1850,
                positionSalary: 0,
                performanceSalary: 0,
                hourlyWage: 0,
                competitionAllowanceType: null,
                competitionAllowanceAmount: 0,
                socialSecurityType: null,
                socialSecurityAmount: 0,
                secrecyFee: 100,
                totalSalary: null,
                isEnabled: true,
            },
            rule: {
                department: [{ required: true, message: '请选择部门', trigger: 'change' }
                ],
                templateName: [
                    { required: true, message: '请输入模板名称', trigger: 'blur' }
                ],
                position: [
                    { required: true, message: '请选择岗位', trigger: 'change' }
                ],
                baseSalary: [
                    {
                        validator: (rule, value, callback) => {
                            if (value <= 0) {
                                return callback(new Error("基本薪资必须大于0。"));
                            } else {
                                callback();
                            }
                        },
                        trigger: "blur",
                    },
                ]
            },
            istLoading: false,
            summaryarry: {},
            datalist: [
            ],
            islook: false,
            total: 0,
            sels: [], // 列表选中列
            listLoading: false,
            that: this,
            pageLoading: false,
            pager: {},
            tableCols: tableCols,
            isEdit: false,
            showFinishDialog: false,//显示完成弹窗
            direction: 'rtl',
            dialogVisible: false,
            uploadLoading: false,
            fileList: [],
            fileHasSubmit: false,
            title: '',
        };
    },
    computed: {
        totalSalary () {
            let sum = 0
                sum = this.loseForm.baseSalary +this.loseForm.positionSalary + this.loseForm.performanceSalary
                + this.loseForm.hourlyWage + this.loseForm.competitionAllowanceAmount +
                this.loseForm.socialSecurityAmount+ this.loseForm.secrecyFee
            return sum == 0 ? null : sum;
        }
    },
    async created () {

    },
    async mounted () {
        this.getDeptList();
        this.getPositList();
        this.onSearch();
    },
    methods: {
         //下载导入模板
         downloadTemplate () {
            window.open("../../static/excel/hr/薪资确认单模板.xlsx","_self");//模板文件
        },
        loseSubmit () {
            this.loseForm.totalSalary = this.totalSalary;
            this.$refs.templateForm.validate((valid) => {
                if (valid) {
                    if (this.loseForm.templateId) {
                        editSalaryTemplate(this.loseForm).then(res => {
                            if (res.success) {
                                this.$message({ message: '修改模板成功', type: "success" })
                                this.onSearch()
                            }
                            this.showFinishDialog = false;
                        })
                    } else {
                        createSalaryTemplate(this.loseForm).then(res => {
                            if (res.success) {
                                this.$message({ message: '新增模板成功', type: "success" })
                                this.onSearch()
                            }
                            this.showFinishDialog = false;
                        })
                    }
                }
            })

        },
        oncompetitionAllowanceType (e) {
            if (e) {
                if (e == '无') {
                    this.loseForm.competitionAllowanceAmount = 0;
                } else if (e == '运营专员') {
                    this.loseForm.competitionAllowanceAmount = 1000;
                } else if (e == 'IT部') {
                    this.loseForm.competitionAllowanceAmount = 2000;
                }
            }

        },
        onsocialSecurityType (e) {
            if (e) {
                if (e == '毕业') {
                    this.loseForm.socialSecurityAmount = 800;
                } else {
                    this.loseForm.socialSecurityAmount = 300;
                }
            }
        },
        // 获取招聘岗位列表
        async getPositList (ddDeptId) {
            const params = {
                closeStatus: 0,//计划状态:-1删除、0进行中、1已完成
                ddDeptId: ddDeptId,//招聘部门
                currentPage: 1,
                pageSize: 50,
                positionName: null,//岗位名称
                recruiterIds: [],//招聘专员
                closeReason: null,//完成原因
            };
            const res = await getDingRolesTree();
            this.postList = res.data;
            this.postList.forEach(item => {
                item.disabled = true;
            })
        },
        // 节点点击事件
        handleNodeClick (data) {
            // 表单
            // 配置树形组件点击节点后，设置选择器的值，配置组件的数据
            this.chooseName = data.name;
            this.loseForm.departmentId = data.dept_id;
            this.loseForm.department = data.name;
            // this.loseForm.position = null,
            // 选择器执行完成后，使其失去焦点隐藏下拉框效果
            this.$refs.selectDept.blur();
        },
        handledeptNodeClick (data) {
            // 筛选
            this.filter.department = data.name;
            this.filter.departmentId = data.dept_id;
            this.$refs.selectdepartment.blur();
        },

        handleNodePostClick (data) {
            if (!data.disabled) {
                this.loseForm.position = data.name;
                this.$refs.selectPosit.blur();
            }
        },
        // 获取部门列表
        async getDeptList () {
            await AllDDDeptTreeNcWh().then(res => {
                if (res.success) {
                    this.deptList = res.data.childDeptList;
                } else {
                    this.$message({ message: res.msg, type: "danger" });
                }
            })
        },
        onAddTemple () {
            this.loseForm = {
                departmentId: null,
                templateName: null,
                department: null,
                position: null,
                baseSalary: 1850,
                positionSalary: 0,
                performanceSalary: 0,
                hourlyWage: 0,
                competitionAllowanceType: null,
                competitionAllowanceAmount: 0,
                socialSecurityType: null,
                socialSecurityAmount: 0,
                secrecyFee: 100,
                totalSalary: null,
                isEnabled: true,
            },
                this.title = '新增薪资模板'
            this.showFinishDialog = true
        },
        // 导出
        async onExport () {
            var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
            const params = { ... this.filter }

            if (params === false) {
                return;
            }
            var res = await exportSalaryTemplate(params);//导出
            loadingInstance.close();
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '薪资模板_' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        // 筛选
        onSearch (filter) {
            this.$refs.pager.setPage(1);
            this.getDataList();
        },
        //删除
        deletePostion (templateId) {
            this.$confirm('是否确认删除该条薪资模板?', '删除', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await deleteSalaryTemplate({ templateId: templateId })
                if (!res?.success) { return }
                this.$message({ type: 'success', message: '删除成功!' });
                this.onSearch()
            }).catch(() => {
                this.$message({ type: 'info', message: '已取消删除' });
            });
        },
        // 编辑
        editPostion (templateId) {
            this.title = '修改薪资模板'
            // this.loseForm = JSON.parse(JSON.stringify(row));
            getSalaryTemplate({ templateId: templateId }).then(res => {
                if (res.success) {
                    this.loseForm = res.data;
                    this.showFinishDialog = true
                }
            })
        },
        //获取数据
        async getDataList () {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            this.listLoading = true;
            const res = await pageSalaryTemplate(params);
            this.listLoading = false;
            this.total = res.data.total
            this.datalist = res.data.list;
            this.summaryarry = res.data.summary;
        },

        //列表排序
        sortchange (column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        //多选事件
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.shopDecorationTaskId);
            })
        },
        customRowStyle (row, index) {
            if (row.row?.isend && row.row.isend == 1) {
                let styleJson = {};
                styleJson.color = "rgb(216 216 216)";
                return styleJson
            } else {
                return null
            }

        },

        // 导入
        async onstartImport () {
            this.fileList = [];
            this.uploadLoading = false;
            this.dialogVisible = true;
            setTimeout(() => {
                this.$refs.upload.clearFiles();
            }, 1);
        },
        async submitUpload () {
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请先选取文件", type: "warning" });
                return false;
            }
            this.fileHasSubmit = true;
            this.uploadLoading = true;
            this.$refs.upload.submit();
        },
        async uploadFile (item) {
            if (!this.fileHasSubmit) {
                return false;
            }
            this.fileHasSubmit = false;
            const form = new FormData();
            form.append("token", this.token);
            form.append("upfile", item.file);
            const res = await importSalaryTemplate(form);//导入接口
            if (res.code == 1) {
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
                this.fileList = []
                this.$refs.upload.clearFiles();
                this.onSearch();
            }
            else this.$message({ message: res.msg, type: "warning" });
            this.uploadLoading = false;
        },
        async uploadChange (file, fileList) {
            if (fileList && fileList.length > 0) {
                var list = [];
                for (var i = 0; i < fileList.length; i++) {
                    if (fileList[i].status == "success") list.push(fileList[i]);
                    else list.push(fileList[i].raw);
                }
                this.fileList = list;
            } else {
                this.fileList = [];
            }
        },
        uploadRemove (file, fileList) {
            this.uploadChange(file, fileList);
        },
    },
};
</script>
<style lang="scss" scoped>
::v-deep .el-input-number.is-without-controls .el-input__inner {
    padding-left: 5px;
    padding-right: 2px;
    text-align: left;
}
</style>