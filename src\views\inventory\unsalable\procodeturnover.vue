<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="商品编码:">
          <el-input v-model="filter.goodsCode" placeholder="商品编码"/>
        </el-form-item>
        <el-form-item label="商品编码名称:">
          <el-input v-model="filter.goodsName" placeholder="商品名称"/>
        </el-form-item>
        <el-form-item label="周转天数:">
          <el-select v-model="filter.days" placeholder="请选择周转天数">
            <el-option label="1天周转天数" value="1"></el-option>
            <el-option label="3天周转天数" value="3"></el-option>
            <el-option label="7天周转天数" value="7"></el-option>
            <el-option label="15天周转天数" value="15"></el-option>
            <el-option label="30天周转天数" value="30"></el-option>
            <el-option label="45天周转天数" value="45"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日期:">
           <el-date-picker v-model="filter.date" align="right" type="date" placeholder="选择日期" :picker-options="pickerOptions"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
        :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>

     <el-dialog title="分析" :visible.sync="dialogVisible" width="80%">
      <span>
          <probianmaanalysis :filter="analysisfilter" ref="probianmaanalysis1"/>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
  </el-dialog>
  </my-container>
</template>

<script>
import {getProCodeTurnover} from '@/api/inventory/warehouse'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import probianmaanalysis from '@/views/inventory/probianmaanalysis'
import { formatYesornoBool,formatLink,formatmoney} from "@/utils/tools";
const tableHandles1=[
        //{label:"新增", handle:(that)=>that.onAdd()},
        //{label:'编辑', handle:(that)=>that.onEdit()}
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton,probianmaanalysis},
  data() {
    return {
      that:this,
      pickerOptions: {
          disabledDate(time) {return time.getTime() > Date.now();},
          shortcuts: [{
            text: '今天',
            onClick(picker) {picker.$emit('pick', new Date());}
          }, {
            text: '昨天',
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit('pick', date);
            }
          }, {
            text: '一周前',
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', date);
            }
          }]
        },
      filter: {
          goodsCode:'',
          goodsName:'',
          date :null,
          days :"3",
      },
      analysisfilter:{
        startDate: null,
        endDate: null,
        proBianMa:""
      },
      list: [],
      pager:{OrderBy:"GoodsCode",IsAsc:false},
      tableCols:[],
      tableHandles:tableHandles1,
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      dialogVisible:false,
    }
  },
  async mounted() {
    var now = new Date();
    now= await this.datetostr(now);
    await this.inittableCols(this.filter.days,now)
    //await this.setBandSelect();
    //await this.setGroupSelect();
    this.filter.date=now;
    this.getlist();
    await this.init();
  },
  beforeUpdate() {  },
  methods: {
     async init(){
        var date1=new Date();
        this.analysisfilter.endDate=await this.datetostr(date1);
        var date2=await this.addDate(this.analysisfilter.endDate,-90);
        this.analysisfilter.startDate=await this.datetostr(new Date(date2));
      },
    async inittableCols(days,date){
       var date1= new Date(date);
       var date2= await this.addDate(date,-1);// date1.setDate(date1.getDate()-1);
       var date3= await this.addDate(date,-2);
       var date4= await this.addDate(date,-3);
       var date5= await this.addDate(date,-4);
       var date6= await this.addDate(date,-5);
       var date7= await this.addDate(date,-6);
        this.tableCols =[
        {istrue:true,prop:'goodsCode',label:'商品编码', width:'130',sortable:'custom'},
        {istrue:true,prop:'goodsName',label:'商品编码名称', width:'200'},
        {istrue:true,type:'button', width:'70',btnList:[{label:"90天分析",handle:(that,row)=>that.onanalysis(row)}]},
        {istrue:true,prop:'turnover7',label:`${date7}`, width:'120',sortable:'custom'},
        {istrue:true,prop:'turnover6',label:`${date6}`, width:'120',sortable:'custom'},
        {istrue:true,prop:'turnover5',label:`${date5}`, width:'120',sortable:'custom'},
        {istrue:true,prop:'turnover4',label:`${date4}`, width:'120',sortable:'custom'},
        {istrue:true,prop:'turnover3',label:`${date3}`, width:'120',sortable:'custom'},
        {istrue:true,prop:'turnover2',label:`${date2}`, width:'120',sortable:'custom'},
        {istrue:true,prop:'turnover1',label:`${await this.datetostr(date1)}`, width:'120',sortable:'custom'},
        {istrue:true,prop:'diffTurnover',label:`差值`, width:'120',sortable:'custom'},
        ];
    },
    async datetostr(date) {
        var y = date.getFullYear();
        var m = ("0" + (date.getMonth() + 1)).slice(-2);
        var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async addDate(date,days){ 
        var d=new Date(date); 
        d.setDate(d.getDate()+days); 
        var m=d.getMonth()+1; 
        return d.getFullYear()+'-'+m+'-'+d.getDate(); 
    },
    async onSearch() {
       if (!this.filter.date) {
         this.$message({message: "请选择日期",type: "warning",});
         return;
        }
      await this.inittableCols(this.filter.days,this.filter.date)
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      this.listLoading = true
      const res = await getProCodeTurnover(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
    async onanalysis(row){
        this.dialogVisible=true;
        this.$nextTick(() => {
           this.$refs.probianmaanalysis1.onShow([row.goodsCode]);
        });
    },
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    selsChange: function(sels) {
      this.sels = sels
    }
  }
}
</script>
