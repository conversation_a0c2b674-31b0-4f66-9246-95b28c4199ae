<template>
 <MyContainer>
   <template #header>

    <div style="display: flex;flex-wrap: wrap;">



      <el-select class="marginleft" v-model="filter.grabPlatform" clearable placeholder="平台" filterable @change="changeCategoryone"
         style="width: 150px;">
         <el-option v-for="(item, i) in categoryTypelist" :key="i" :label="item.value" :value="item.value" />
       </el-select>
       <el-select class="marginleft" v-model="filter.categoryName" clearable placeholder="类目" filterable @change="changeCategory"
         style="width: 150px;">
         <el-option v-for="(item, i) in categoryTypelistfa" :key="i" :label="item" :value="item" />
       </el-select>
       <el-select class="marginleft" v-model="filter.categorySubName" clearable placeholder="子类目" filterable
         style="width: 150px; margin-left: 5px">
         <el-option v-for="(item, i) in subcategory" :key="i" :label="item.categorySubName" :value="item.categorySubName" />
       </el-select>

       <el-input class="marginleft" v-model.trim="filter.goodsTitle" placeholder="商品标题" clearable style="width: 150px"
         maxlength="100"></el-input>
         <div class="marginleft">
         <inputYunhan ref="productCode" :inputt.sync="filter.goodsNos" v-model="filter.goodsNos" width="170px"
          placeholder="货号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
          @callback="productCodeCallback" title="货号">
        </inputYunhan>
        </div>
        <el-input v-model="filter.startPrice" placeholder="价格区间" type="number" min="0.01" max="999999999" style="width:120px;"  />
        <span style="position: relative; top: 4px;">至</span>
        <el-input v-model="filter.endPrice" placeholder="价格区间"  type="number" min="0.01" max="999999999" style="width:120px;"  />



       <el-select class="marginleft" v-model="filter.sortType" clearable placeholder="排序方式" style="width: 130px; margin-left: 5px">
         <el-option  label="最受好评" value="1" />
         <el-option  label="新品上市" value="2" />
       </el-select>

       <span style="position: relative; top: 5px;font-size: 12px; margin-left: 5px">上架时间: </span>
       <el-date-picker :picker-options="pickerOptions" style="width: 200px" v-model="filter.listingTime" type="daterange"
         format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
         end-placeholder="结束日期" :clearable="true" @change="changeTime($event, 1)">
       </el-date-picker>

       <span style="position: relative; top: 5px;font-size: 12px; margin-left: 5px">抓取时间: </span>
       <el-date-picker :picker-options="pickerOptions" style="width: 200px" v-model="filter.grabTime" type="daterange"
         format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
         end-placeholder="结束日期" @change="changeTime($event, 2)" :clearable="true">
       </el-date-picker>

       <el-button class="marginleft" type="primary" @click="onSearch" style="margin-left: 5px;">查询</el-button>
       <el-button class="marginleft" type="primary" @click="exportProps" v-if="checkPermission('api:bladegateway:yunhan-gis-competition:export-competitiveDataqxd')">导出</el-button>


     </div>


   </template>

   <!-- :summaryarry='summaryarry' @summaryClick='onsummaryClick'  -->
   <template>
     <vxetablebase :id="'competitorLinkindex20240702'" :tablekey="'competitorLinkindex20240702'" :tableData='datalist' :tableCols='tableCols'
       :loading='listLoading' :border='true' :treeProp="treeProp" :that="that"
       ref="vxetable" @sortchange='sortchange' :showsummary='false' >
     </vxetablebase>
   </template>
   <template #footer>
     <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
   </template>

   <el-dialog :title="digtitle" :visible.sync="dialogVisible" width="30%" v-dialogDrag>
     <span>
       <el-upload ref="upload" class="upload-demo" v-if="dialogVisible" :auto-upload="false" :multiple="false" :limit="1"
         action accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :file-list="fileList">
         <template #trigger>
           <el-button size="small" type="primary">选取文件</el-button>
         </template>
         <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传</el-button>
       </el-upload>
     </span>
     <span slot="footer" class="dialog-footer">
       <el-button @click="dialogVisible = false">关闭</el-button>
     </span>
   </el-dialog>

   <el-dialog :visible.sync="chartdialog" width="1000px" :close-on-click-modal="true" v-loading="!chartsdata1"
     element-loading-text="拼命加载中" v-dialogDrag :append-to-body="true">
     <span style="font-size: 15px; margin-right: 10px;font-size: 12px;">发货时间: </span>
     <el-date-picker style="width: 200px" v-model="filter.charttimerange" type="datetimerange" format="yyyy-MM-dd"
       @change="changedatee" value-format="yyyy-MM-dd" :picker-options="pickerOptions" range-separator="至"
       start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false">
     </el-date-picker>
     <!-- <buschar :charid="'charNegative1'" :analysisData="chartsdata1" v-if="chartsdata1"></buschar> -->

   </el-dialog>




 </MyContainer>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";

import { getTime } from '@/utils/getCols.js'
import { formatTime, formatPlatform, pickerOptions } from "@/utils/tools";
import middlevue from '@/store/middle.js'
import dayjs from 'dayjs';
import { competitiveDataPage, exportCompetitiveData, getAllCategoryType } from '@/api/bladegateway/yunhangiscompetition.js';
import inputYunhan from "@/components/Comm/inputYunhan";




const tableCols = [
 { istrue: true, prop: 'grabPlatform', label: '平台', sortable: 'custom', width: '80px',},
 // { istrue: true, prop: 'sendGoodsDate', label: '类目', width: 'auto', sortable: 'custom', formatter: (row) => { if (row.sendGoodsDate) { return row.sendGoodsDate.slice(0, 10) } else { return '' } } },
 { istrue: true, prop: 'categoryName', label: '类目', sortable: 'custom', width: '100px' },
 { istrue: true, prop: 'categorySubName', label: '子类目', sortable: 'custom', width: '100px' },
 { istrue: true, prop: 'goodsInfo', label: '商品链接', width: '150px', type: 'click', handle: (that, row) => that.openurl(row),  },
 { istrue: true, prop: 'goodsTitle', label: '标题', sortable: 'custom', width: '100px' },
 { istrue: true, prop: 'goodsNo', label: '货号', sortable: 'custom', width: '130px' },
 { istrue: true, prop: 'price', label: '价格', sortable: 'custom', width: '80px' },
 { istrue: true, prop: 'discountInfo', label: '折扣', sortable: 'custom', width: '80px' },
 { istrue: true, prop: 'evaluationsNum', label: '评价数', sortable: 'custom', width: '90px' },
//  formatter: (row) => row.damagedZrFileUrlCount + '张' + '  ' + '查看', type: 'click', handle: (that, row) => that.viewImages(row)
 { istrue: true, prop: 'mainPic', label: '主图', width: '110px', type: "images" },
 { istrue: true, prop: 'detailPic', label: '图片列表', width: '110px', type: "imagess"  },
 { istrue: true, prop: 'score', label: '评分', sortable: 'custom', width: '100px' },
 { istrue: true, prop: 'colorInfo', label: '颜色', sortable: 'custom', width: '130px' },
 { istrue: true, prop: 'sizeInfo', label: '尺寸', sortable: 'custom', width: '130px' },
 { istrue: true, prop: 'sortType', label: '排序方式', sortable: 'custom', width: '80px', formatter: (row) => row.sortType=="1"?'最受好评':row.sortType=="2"?'新品上市':'', },
  {
    istrue: true,
    prop: 'listingTime',
    label: '上架时间',
    sortable: 'custom',
    width: '100px',
    formatter: (row) => {
      if(row.grabPlatform == 'Amazon'){
        return row.listingTime;
      }
      let formattedGoodsNo = '';
      if (row.goodsNo && typeof row.goodsNo === 'string') {
        let digits = row.goodsNo.replace(/\D/g, ''); // 移除所有非数字字符
        let firstSixDigits = digits.substr(0, 6);
        formattedGoodsNo = firstSixDigits.replace(/^(..)(..)(..)$/, '$1.$2.$3');
      }
      return formattedGoodsNo;
    }
  },
 { istrue: true, prop: 'grabTime', label: '抓取时间', sortable: 'custom', width: '130px', },




];
const tableHandles = [
 //{ label: "导入", handle: (that) => that.startImport() },
];
export default {
 name: 'Vue2demoLossDataStatisticsOne',
 components: { vxetablebase, MyContainer, inputYunhan, },
 data() {
   return {
     selnum: 0,
     digtitle: '',
     editLoading: true,
     treeProp: null,
     alllist: {},
     shopList: [],
     filter: {
      grabTime: [],
      listingTime: [],
      sortType: '',
      endPrice: null,
      startPrice: null,
      goodsNos: '',
      goodsTitle: '',
      categoryName: '',
      categorySubName: '',
      endGrabTime: null,
      startGrabTime: null,
      endListingTime: null,
      startListingTime: null,
      orderBy: 'grabTime',
      isAsc: false,
     },

     pickerOptions: pickerOptions,
     summaryarry: {},
     damagedList: [],
     damagedList2: [],
     chartdialog: false,
     chartsdata1: null,
     dialogVisible: false,
     fileList: [],
     rowlist: {},
     that: this,
     total: 0,
     sels: [],
     categoryTypelist: [],
     subcategory: [],
     newWareHouseList: [],
     permissionTree: [],
     categoryTypelistfa: [],
     listLoading: false,
     tableCols: tableCols,
     datalist: [],
     filetype: '',
     url: '',
     damagedHandList: [
       { name: '补发', value: '补发' },
       { name: '红包补偿', value: '红包补偿' },
       { name: '订单部分补偿', value: '订单部分补偿' },
       { name: '订单全额退款', value: '订单全额退款' },
       { name: '订单退货退款', value: '订单退货退款' },
       { name: '订单退货补发', value: '订单退货补发' },

     ],
     wdlist: null,
     viewImagesDialogVisiable: false,
     index: 0,
     maxIndex: 0,
     imageModelVisiable: false,
     imgList: null,
     imageInfo: {},
     maxIndex: 0,
     viewImgList: [],
     imgTotal: 0,
     imgPageInfo: {
       currentPage: 1,
       pageSize: 8,
       orderBy: 'zrDepartment',
       isAsc: false
     },
     row: null,
     queryInfo: null,
     domIndex: 0,

   };
 },

 async mounted() {
   const {data,success} = await getAllCategoryType()
    if(success){
      this.alllist = data;
      this.categoryTypelist = Object.keys(data).map(key => {
        return {
          label: data[key],
          value: key
        }
      })
    }
    this.filter.startGrabTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
    this.filter.endGrabTime = dayjs().format('YYYY-MM-DD')
    this.filter.grabTime = [this.filter.startGrabTime, this.filter.endGrabTime]
   await this.onSearch();
 },
 methods: {
  openurl(row){
    if(row.goodsInfo){
      window.open(row.goodsInfo)
    }
  },
  // clearCategory(){
  //   this.filter.categorySubName = '';
  //   this.subcategory = [];
  // },
  async changeCategoryone(e){
    if(e){
      this.categoryTypelistfa = [];
      this.filter.categoryName = '';
      this.filter.categorySubName = '';
      this.subcategory = [];

      this.categoryTypelistfa = Object.keys(this.alllist[e])
    }else{
      this.filter.categoryName = '';
      this.categoryTypelistfa = [];
      this.filter.categorySubName = '';
      this.subcategory = [];
    }
  },
  async changeCategory(e){
    if(e){
      this.subcategory = [];
      this.filter.categorySubName = '';
      this.subcategory = this.alllist[this.filter.grabPlatform][e];
      // [this.filter.categoryName]
      // let matchedCategory = this.categoryTypelist.find(item => item.value === e);
      // if (matchedCategory) {
      //   this.subcategory = matchedCategory.label;
      // }
    }else{
      this.filter.categorySubName = '';
      this.subcategory = []
    }
  },
  //货号回调
  productCodeCallback(val) {
    this.filter.goodsNos = val
  },
  changeTime(val,index){
    if(!val||val == null){
      val = [];
    }

    if(index==1){
      this.filter.listingTime = val;
    }else{
      this.filter.grabTime = val;
    }
  },
   downLoadType(type) {
     if (type == 'bf') {
       window.open("/static/excel/order/补发订单模板.xlsx", "_blank");
     } else if (type == 'hb') {
       window.open("/static/excel/order/红包补偿订单模板.xlsx", "_blank");
     } else if (type == 'qt') {
       window.open("/static/excel/order/其它订单模板.xlsx", "_blank");
     }
   },

   async exportProps() {
    //  const pager = this.$refs.pager.getPager()

     if(this.filter?.listingTime?.length>0){
      this.filter.startListingTime = this.filter.listingTime[0] + ' 00:00:00';;
      this.filter.endListingTime = this.filter.listingTime[1] + ' 23:59:59';
     } else {
      this.filter.startListingTime = null;
      this.filter.endListingTime = null;
     }

     if(this.filter?.grabTime?.length>0){
      this.filter.startGrabTime = this.filter.grabTime[0] + ' 00:00:00';;
      this.filter.endGrabTime = this.filter.grabTime[1] + ' 23:59:59';
     } else {
      this.filter.startGrabTime = null;
      this.filter.endGrabTime = null;
     }

    let flag = false;
    for (let key in this.filter) {
      if(this.filter[key]&&key != 'orderBy'&&this.filter[key].length>0){
        flag = true;
        break;
      }
    }
    if (!flag) {
      this.$message({ message: "请先设置查询条件后导出数据", type: "warning" });
      return;
    }
     const pager = this.$refs.pager.getPager()

     const para = {
       ...pager,
       ...this.filter
     }
     this.listLoading = true
     const { data } = await exportCompetitiveData(para)
     const aLink = document.createElement("a");
     let blob = new Blob([data], { type: "application/vnd.ms-excel" })
     aLink.href = URL.createObjectURL(blob)
     aLink.setAttribute('download', '竞品链接' + new Date().toLocaleString() + '.xlsx')
     aLink.click()
      this.listLoading = false
   },

   async onsummaryClick(property) {
     let that = this;
     this.chartsdata1 = null;
     this.chartdialog = true;
     this.bottomcharts = true;
     // this.filter.charttimerange = this.filter.timerange;
     this.filter.charttimerange = getTime(this.filter.timerange);
     let params = {
       ...this.filter,
       selectColumn: property,
       isRowAnlysis: false,
       startSendGoodsDate: this.filter.charttimerange[0],
       endSendGoodsDate: this.filter.charttimerange[1],
     }
     this.rowlist = params;
     let res = await getDamagedOrdersStatisAnlysisAsync(params);

     if (!res?.success) {
       return
     }
     this.chartsdata1 = res.data;
   },
   async getZrDept() {
     let res = await getDamagedOrdersZrDept();
     this.damagedList = res?.data;
     // damagedList2
   },
   async getZrType(name) {
     let res = await getDamagedOrdersZrType(name);
     this.damagedList2 = res.data;
     this.filter.zrType2 = '';

   },
   getPrevious30Days(dateString) {
     const date = new Date(dateString);
     date.setDate(date.getDate() - 30);

     const year = date.getFullYear();
     const month = String(date.getMonth() + 1).padStart(2, '0');
     const day = String(date.getDate()).padStart(2, '0');

     const previousDate = `${year}-${month}-${day}`;
     return previousDate;
   },
   getDaysDifference(date1, date2) {
     const oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
     const firstDate = new Date(date1);
     const secondDate = new Date(date2);

     // 将日期转换为时间戳，并且取绝对值以确保始终得到正数
     const diffDays = Math.abs(Math.round((firstDate - secondDate) / oneDay));

     return diffDays;
   },
   changedate(val) {
     let days = this.getDaysDifference(val[0], val[1]);
     let startDate;
     let endDate;

     // if(days<30){
     //   this.$message.info("请选择至少一个月时间，已为您自动选择一个月时间")
     //   startDate = this.getPrevious30Days(val[1]);
     //   endDate = val[1];
     // }else{
     startDate = val[0];
     endDate = val[1];
     // }

     this.filter.timerange = [startDate, endDate];


     this.filter.charttimerange = [this.getPrevious30Days(endDate), endDate]




   },
   async changedatee(val) {
     let days = this.getDaysDifference(val[0], val[1]);
     let startDate;
     let endDate;
     this.chartsdata1 = null;
     // if(days<30){
     //   this.$message.info("请选择至少一个月时间，已为您自动选择一个月时间")
     //   startDate = this.getPrevious30Days(val[1]);
     //   endDate = val[1];
     // }else{
     //   startDate = val[0];
     //   endDate = val[1];
     // }
     startDate = val[0];
     endDate = val[1];

     this.filter.charttimerange = [startDate, endDate];


     let params = {
       ...this.rowlist,
       startSendGoodsDate: this.filter.charttimerange[0],
       endSendGoodsDate: this.filter.charttimerange[1],
     };
     let res = await getDamagedOrdersStatisAnlysisAsync(params);

     if (!res?.success) {
       return
     }
     this.chartsdata1 = res.data;


   },
   async getrowqst(row) {
     this.chartdialog = true;
     this.chartsdata1 = null;
     this.filter.charttimerange = getTime(this.filter.timerange);
     // this.filter.charttimerange = this.filter.timerange;
     // if(this.filter.sendWareHouseId===0){
     //   this.filter.sendWareHouseId = null
     // }
     // row.sendGoodsDate = null;
     // row.endSendGoodsDate = null;
     let params;
     if (row.sendWareHouseId == 0 || !row.sendWareHouseId || row.sendWareHouseId == "") {
       params = {
         ...row,
         ...this.filter,
         isRowAnlysis: true,
         platform: null,
         startSendGoodsDate: this.filter.charttimerange[0],
         endSendGoodsDate: this.filter.charttimerange[1],
       }
     } else {
       params = {
         ...this.filter,
         ...row,
         isRowAnlysis: true,
         platform: null,
         startSendGoodsDate: this.filter.charttimerange[0],
         endSendGoodsDate: this.filter.charttimerange[1],
       }
     }
     this.rowlist = params;
     let res = await getDamagedOrdersStatisAnlysisAsync(params);

     if (!res?.success) {
       return
     }
     this.chartsdata1 = res.data;

   },
   daoruFile(e, name) {
     this.digtitle = name;
     this.filetype = e;
     this.fileList = [];
     this.$nextTick(() => {
       this.dialogVisible = true
       this.$refs.upload.clearFiles();
     });

   },
   uploadFile(item) {
     const form = new FormData();
     form.append("token", this.token);
     form.append("upfile", item.file);
     let res;
     if (this.filetype == 'bf') {
       res = importDamagedOrdersBF(form);
     } else if (this.filetype == 'hb') {
       res = importDamagedOrdersHB(form);
     } else if (this.filetype == 'qt') {
       res = importDamagedOrdersQT(form);
     }

     // if (res.code == 1) {
     //     this.$message({ message: "上传成功,正在导入中...", type: "success" });
     //     this.dialogVisible = false;
     // }
     //
     this.dialogVisible = false;
     this.$message({
       message: '正在导入中...',
       type: "success",
     });
   },
   async uploadChange(file, fileList) {
     let files = [];
     files.push(file)
     this.fileList = files;
   },
   submitUpload() {
     if (!this.fileList || this.fileList.length == 0) {
       this.$message({ message: "请选取文件", type: "warning" });
       return false;
     }
     this.$refs.upload.submit();
     this.$refs.upload.clearFiles();
     this.fileList = []

   },
   onSearch() {
     this.$refs.pager.setPage(1)
     this.getlist()
   },
   //排序
   sortchange({ order, prop }) {
     this.listLoading = true
     if (prop) {
       this.filter.orderBy = prop
       this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
       this.getlist()
     }
   },
   // 获取列表
   async getlist() {
    if(typeof this.filter.goodsNos == 'string' && this.filter.goodsNos.trim() !== ''){
      this.filter.goodsNos = this.filter.goodsNos.split(',');
    }
     let _this = this;

     const pager = this.$refs.pager.getPager()

     if(this.filter?.listingTime?.length>0&&this.filter.listingTime){
      this.filter.startListingTime = this.filter.listingTime[0] + ' 00:00:00';;
      this.filter.endListingTime = this.filter.listingTime[1] + ' 23:59:59';
     } else {
      this.filter.startListingTime = null;
      this.filter.endListingTime = null;
     }

     if(this.filter?.grabTime?.length>0&&this.filter.grabTime){
      this.filter.startGrabTime = this.filter.grabTime[0] + ' 00:00:00';;
      this.filter.endGrabTime = this.filter.grabTime[1] + ' 23:59:59';
     } else {
      this.filter.startGrabTime = null;
      this.filter.endGrabTime = null;
     }


    //  this.filter.AfterSaleApproveDate = this.filter.AfterSaleApproveDate ? this.filter.AfterSaleApproveDate : []


     const params = {
       ...pager,
       ...this.filter
     }
     this.listLoading = true
     let newObj = JSON.parse(JSON.stringify(params))


     let res = await competitiveDataPage(newObj)


     this.listLoading = false

     if (!res?.success) {
       return
     }
     res.data.list.map((item)=>{
      if(item.detailPic&&item.detailPic.length>1){
        item.detailPic = JSON.parse(item.detailPic);
      }else{
        item.detailPic = [];
      }
     })

    this.total = res.data.total;

    this.datalist = res.data.list;


   },

 },
};
</script>

<style lang="scss" scoped>
.father {
 position: relative;

 .imageModalPos {
   position: fixed;
   top: 0;
   left: 0;
   z-index: 999;
   overflow: hidden;
 }
}

.a {
 align-items: center;
 font-size: 15px
}

.marginleft {
 margin-right: 5px;
 margin-bottom: 5px;
}

.viewImageBox {
 width: 100%;
 display: flex;
 flex-wrap: wrap;
 height: 700px;
 overflow: auto;

 .viewImageBox_item {
   width: 300px;
   height: 300px;
   display: flex;
   flex-direction: column;
   margin-right: 10px;
   position: relative;

   .viewImageBox_item_fixed {
     position: absolute;
     top: 0;
     left: 0;
     width: 50px;
     height: 30px;
     background-color: red;
     color: #fff;
     text-align: center;
     line-height: 30px;
   }


   .viewImageBox_item_bottom {
     flex: 1;

     .viewImageBox_item_info {
       display: flex;

       .viewImageBox_item_info_left,
       .viewImageBox_item_info_right {
         text-align: center;
         height: 30px;
         box-sizing: border-box;
         border: 1px solid #ccc;
         line-height: 30px;
       }

       .viewImageBox_item_info_left {
         width: 80px;
       }

       .viewImageBox_item_info_right {
         flex: 1;
       }
     }
   }
 }
}

.viewImageBox_item_img ::v-deep img {
 min-width: 300px !important;
 min-height: 220px !important;
 width: 300px !important;
 height: 220px !important;
}

.viewImageBox_item_img ::v-deep div {
 min-width: 300px !important;
 min-height: 220px !important;
 width: 300px !important;
 height: 220px !important;
}
</style>
