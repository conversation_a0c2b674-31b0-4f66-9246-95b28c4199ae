<template>
  <MyContainer>
    <template #header>
      <el-row>
        <span style="font-size: 12px; margin-right: 10px">发货时间: </span>
        <el-date-picker class="marginleft" style="width: 220px; " v-model="filter.SendGoodsDate" type="daterange"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" :picker-options="pickerOptions"
          :start-placeholder="'发货开始时间'" :end-placeholder="'发货结束时间'" :clearable="false">
        </el-date-picker>
        <span style="font-size: 12px; margin-right: 10px">售后发起时间: </span>
        <el-date-picker class="marginleft" style="width: 220px; " v-model="filter.AfterSaleApproveDate" type="daterange"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" :picker-options="pickerOptions"
          :start-placeholder="'售后发起时间'" :end-placeholder="'售后结束时间'">
        </el-date-picker>
        <span style="font-size: 12px; margin-right: 10px">责任开始计算时间: </span>
        <el-date-picker class="marginleft" style="width: 220px; " v-model="filter.ZrSetDate" type="daterange"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" :picker-options="pickerOptions"
          :start-placeholder="'责任开始计算时间'" :end-placeholder="'责任结束计算时间'">
        </el-date-picker>
        <el-input class="marginleft" v-model="filter.orderInnerNo" placeholder="内部订单号" clearable style="width: 180px"
          maxlength="50"></el-input>
        <el-input class="marginleft" v-model="filter.orderNo" placeholder="线上订单号" clearable style="width: 180px"
          maxlength="50"></el-input>
        <el-select class="marginleft" v-model="filter.platform" clearable placeholder="平台" @change="onchangeplatform"
          style="width: 180px">
          <el-option v-for="item in platformList" :key="item.label" :label="item.label" :value="item.value" />
        </el-select>
        <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺" style="width: 200px"
          @change="zrType2change()">
          <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
            :value="item.shopCode"></el-option>
        </el-select>
        <el-select class="marginleft" v-model="filter.sendWareHouseId" clearable placeholder="发货仓"
          style="width: 160px; margin-left: 10px;">
          <el-option v-for="item in newWareHouseList" :key="item.name" :label="item.name" :value="item.wms_co_id" />
        </el-select>
        <el-input class="marginleft" v-model="filter.expressOrder" clearable placeholder="快递单号" style="width: 180px"
          maxlength="50"></el-input>
        <el-input class="marginleft" v-model="filter.styleCode" placeholder="系列编码" clearable style="width: 250px"
          maxlength="50" />
        <el-input class="marginleft" v-model="filter.goodsCode" clearable placeholder="商品编码" style="width: 180px"
          maxlength="50"></el-input>
        <el-input class="marginleft" type="number" v-model.trim="filter.batchNumber" clearable placeholder="批次号" style="width: 180px"
        maxlength="20" @blur="filter.batchNumber = filter.batchNumber.slice(0,19)"></el-input>
        <el-select class="marginleft" v-model="filter.zrDepartment" clearable placeholder="责任部门(大类)" style="width: 200px;"
          @change="getZrType(filter.zrDepartment)">
          <el-option v-for="item in damagedList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select class="marginleft" v-model="filter.zrType2" clearable placeholder="责任类型(细类)" style="width: 200px;"
          @change="zrType2change()" @visible-change="shopIncident">
          <el-option v-for="item in damagedList2" :key="item" :label="item" :value="item" />
        </el-select>
        <!-- <el-input class="marginleft" v-model="filter.memberName" clearable placeholder="责任人" style="width: 160px"
          maxlength="50"></el-input> -->
        <!-- <YhUserelector :value.sync="filter.memberName" :text.sync="filter.memberName"></YhUserelector> -->

        <!-- <el-select v-model="filter.memberName" filterable placeholder="责任人" @change="changeName">
          <el-option v-for="item in userList" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select> -->
        <el-input class="marginleft" v-model="filter.managerUserName" clearable placeholder="责任上级" style="width: 180px"
          maxlength="10"></el-input>
        <el-select v-model="filter.memberName" filterable remote reserve-keyword placeholder="责任人" clearable
          :remote-method="remoteMethod">
          <el-option v-for="item in userList" :key="'userSelector' + item.value + item.extData.defaultDeptId"
            :label="item.label" :value="item.label">
            <span>{{ item.label }}</span>
            <span style=" color: #8492a6; ">({{ item.extData.position }},{{ item.extData.empStatusText }}{{
              item.extData.jstUserName
              ? "," + item.extData.jstUserName : "" }})</span>
            <span style=" color: #8492a6; "> {{ item.extData.deptName }}</span>
          </el-option>
        </el-select>
        <el-select class="marginleft" v-model="filter.zrAppealState" clearable placeholder="申诉状态" style="width: 160px">
          <el-option v-for="item in zrAppealStateList" :key="item.name" :label="item.name" :value="item.value" />
        </el-select>
        <el-button class="marginleft" type="primary" @click="onSearch">查询</el-button>
        <!-- <el-button   type="primary" @click="exportProps">导出</el-button> -->
        <el-button class="marginleft" type="primary" @click="allzrApply">批量申诉</el-button>
      </el-row>
      <el-row style="height: 35px; display: flex; align-items: center;">
        <span style="font-size: 12px;margin-right: 10px;">统计维度</span>
        <!-- <el-radio-group v-model="radio" style="margin-right: 10px;" @change="selweidu">
      <el-radio :label="3">内部订单号</el-radio>
      <el-radio :label="6">负责人</el-radio>
    </el-radio-group> -->
        <el-checkbox v-model="checked" @change="selweidu">内部订单号-责任人</el-checkbox>
        <el-dropdown style="margin:0 10px;" @command="delby"
          v-if="checkPermission(['api:Customerservice:DamagedOrders:DeleteDamagedOrder'])">
          <el-button type="danger">
            删除方式<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="sel">删除已选择数据</el-dropdown-item>
            <el-dropdown-item command="condition">按条件删除数据</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown @command="daochufuc">
          <el-button type="primary">
            导出方式<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="one">明细导出</el-dropdown-item>
            <el-dropdown-item command="two">财务/人事导出</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button type="primary" style="margin-left: 10px;" @click="allEditOrder"
          v-if="checkPermission('Orderdetailsediting1')">
          订单明细编辑
        </el-button>
      </el-row>
      <div style="color: red; font-size: 14px; margin-left: 15px;">申诉开放时间以发起责任计算时间起当天17：30至次日9：00，其他时间或者过期均不开放</div>
    </template>
    <template>
      <vxetablebase :id="'summaryResponsibilityTwo20230701'" :tablekey="'summaryResponsibilityTwo20230701'" :enableCheckRange="false" :tableData='list'
        v-show="list1.length == 0" :tableCols='tableCols' @select="chooseCode" :loading='listLoading'
        :border='true' :that="that" ref="vxetable" @sortchange='sortchange' :showsummary='true'
        :summaryarry='summaryarry'>
      </vxetablebase>

      <vxetablebase :id="'YunHanAdminGoodsFinishedpart202301201'" :enableCheckRange="false" v-show="list1.length > 0"
        :tableData='list1' :tableCols='tableCols1' :loading='listLoading' :border='true' :that="that" ref="vxetable2"
        @sortchange='sortchange' :showsummary='false'>
      </vxetablebase>
    </template>

    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <el-dialog :visible.sync="seldialog" width="1000px" :close-on-click-modal="true" element-loading-text="拼命加载中"
      v-dialogDrag :append-to-body="true">
      <div>删除数据</div>
      {{ deltitle }}
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="seldialog = false">取 消</el-button>
        </span>
        <span class="dialog-footer">
          <el-button @click="issuredelone" type="primary">确认删除</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog :visible.sync="condialog" width="1000px" :close-on-click-modal="true" element-loading-text="拼命加载中"
      v-dialogDrag :append-to-body="true">
      按条件删除数据
      <conddel :ids="ids" @issearch="issearchfuc" @disabledfuc="disabledfuc"></conddel>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="condialog = false">取 消</el-button>
        </span>
        <span class="dialog-footer" @click="issuredel">
          <el-button type="primary" :disabled="!issearch">确认删除</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog :visible.sync="tabledialog" width="1700px" :close-on-click-modal="true" element-loading-text="拼命加载中"
      v-dialogDrag :append-to-body="true" title="订单明细编辑" :modal-append-to-body="false">
      <span style="color: red;">编辑订单明细开放时间为责任计算时间当天9：00至17：30，其他时间均不开放该功能（只允许编辑未申诉数据）</span>
      <edittable :tabledata="allorderList" ref="edittable" @update-templatepageclose="handleUpdateTemplatepageclose"
        @tocreateimgbilitytable="tocreateimgbility"></edittable>
      <!-- <template #footer> -->
      <div class="dialog-prompt-button ">
        <span class="dialog-prompt">
          <el-button @click="tabledialog = false" style="margin-right:10px;">取 消</el-button>
        </span>
        <span class="dialog-prompt">
          <el-button type="primary" @click="saveorder">保存</el-button>
        </span>
      </div>
      <!-- </template> -->
    </el-dialog>
    <el-dialog title="图片操作" :visible.sync="pictureupload" width="50%" v-dialogDrag>
      <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="10"
        :on-success="handleSuccess" :file-list="picFileList" multiple :show-file-list="false"
        accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
        <el-button class="addsc" type="text">上传图片</el-button>
      </el-upload>
      <div v-if="picLists.length > 0">
        <div class="imageList_box">
          <div class="imageList" v-for="(item, i) in picLists" :key="i">
            <el-image class="imgcss" style="width: 100px; height: 100px" :src="item" :preview-src-list="picLists">
            </el-image>
            <span class="del" @click="delImg(item, i)">x</span>
          </div>
        </div>
      </div>
      <div style="text-align: right;margin-top: 20px;">
        <el-button type="primary" @click="pictureupload = false">取消</el-button>
        <el-button type="primary" @click="operateSubmit">提交</el-button>
      </div>
    </el-dialog>
    <el-dialog title="日志" :visible.sync="viewthelogpopwindow" width="40%" height="300px" v-dialogDrag @change="logoff">
      <div>
        <el-date-picker style="width: 250px;margin-right:5px;" v-model="controlslogs.logstimerange" type="daterange"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始时间"
          end-placeholder="结束时间" :picker-options="pickerOptions"></el-date-picker>
        <el-input v-model.trim="controlslogs.userName" clearable placeholder="操作人"
          style="width: 150px;margin-right:5px;" />
        <el-button type="primary" @click="onViewlogs">查询</el-button>
      </div>
      <div style="width: 100%;height: 250px;">
        <el-table :data="tableDatalogs" height="200px">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column prop="createdTime" label="时间" width="180"></el-table-column>
          <el-table-column prop="createdUserName" label="操作人" width="80"></el-table-column>
          <el-table-column prop="operationcontent" label="操作内容"></el-table-column>
        </el-table>
      </div>
      <div style="text-align: right;margin-top: 20px;">
        <el-button type="primary" @click="logoff">关闭</el-button>
      </div>
      <!-- <template #footer>
        <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="viewtotal"
          @page-change="detailPagechange" @size-change="detailSizechange" style="margin-top: 10px;" />
      </template> -->
    </el-dialog>

    <el-dialog title="聊天记录" v-if="diaChatVisible" :visible.sync="diaChatVisible" width="50%" height="600px"
      v-dialogDrag>
      <div style="height: 600px; overflow: auto;">
        <el-row v-for="(item,i) in chatlist" :key="i" style="margin-bottom: 40px;">
          <!-- {{chatlist}} -->
          <el-row v-show="item.SendName == firstname?true:false">
            <el-col :span="12"><div class="grid-content bg-purple">
              <div ><span style="font-weight: 600;">{{item.SendName+"  "}}</span><span style="font-size: 12px;">{{item.SendTime}}</span></div>
              <div style="width: 100%; display: flex;">
                <div style="padding: 15px 10px; background-color: #eee; border-radius: 10px; margin-right: auto;" v-show="item.SendMsgType==0?true:false">
                  <span >{{item.SendTextContent}}</span>
                </div>

                <div style="padding: 15px 10px; background-color: #eee; border-radius: 10px; margin-right: auto;" v-show="item.SendMsgType==1?true:false">
                  <el-badge  style="margin-top:10px;">
                      <el-image  class="imgstyle" :src="item.SendTextContent" fit="fill" :preview-src-list="[item.SendTextContent]">
                      </el-image>
                  </el-badge>
                </div>

                <div style="padding: 15px 10px; background-color: #95caff; border-radius: 10px;" class="marleft" v-show="item.SendMsgType==2?true:false">
                  <el-badge  style="margin-top:10px;">
                      <video style="height: 50%;" height="50%" :src="item.SendTextContent" fit="fill" >
                      </video>
                  </el-badge>
                </div>


              </div>
            </div></el-col>
          </el-row>
          <el-row v-show="item.SendName == firstname?false:true">
            <el-col :span="12" :offset="12">
              <div class="flex" style="width: 100%;">
                <div class="marleft"><span style="font-size: 12px;">{{item.SendTime+'  '}}</span><span style="font-weight: 600;">{{item.SendName}}</span></div><br/>
              </div>
              <div class="flex" style="width: 100%;">
                <div style="padding: 15px 10px; background-color: #95caff; border-radius: 10px;" class="marleft" v-show="item.SendMsgType==0?true:false">
                  <div class="marleft">{{item.SendTextContent}}</div>
                </div>
                <div style="padding: 15px 10px; background-color: #95caff; border-radius: 10px;" class="marleft" v-show="item.SendMsgType==1?true:false">
                  <el-badge  style="margin-top:10px;">
                      <el-image  class="imgstyle" :src="item.SendTextContent" fit="fill" :preview-src-list="[item.SendTextContent]">
                      </el-image>
                  </el-badge>
                </div>

                <div style="padding: 15px 10px; background-color: #95caff; border-radius: 10px;" class="marleft" v-show="item.SendMsgType==2?true:false">
                    <video @click="videoshow(item)" style="height: 100px;" height="100px" class="imgstyle" :src="item.SendTextContent" fit="fill" >
                    </video>
                </div>

              </div>
            </el-col>
          </el-row>
        </el-row>
      </div>
    </el-dialog>

    <el-dialog title="视频播放" :visible.sync="videoplayerReload" v-dialogDrag width="50%" @close="videoplayerReload = false"  :append-to-body="true" >

    <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />

    <span slot="footer" class="dialog-footer">

        <el-button @click="videoplayerReload = false">关闭</el-button>

    </span>

    </el-dialog>

    <!-- <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' /> -->
  </MyContainer>
  <!-- </div> -->
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getAllWarehouse } from '@/api/inventory/warehouse'
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser'
import middlevue from '@/store/middle.js'
import { formatTime, formatPlatform, pickerOptions } from "@/utils/tools";
import YhUserelector from '@/components/YhCom/yh-userselector.vue'
import { getAllListInCalc as getAllShopList, getList as getshopListt } from '@/api/operatemanage/base/shop';
import {
  getDamagedOrdersWithholdListAsync, getDeductZrAppeal4CRUD, getDamagedOrdersZrType, getDamagedOrdersZrDept, exportDamagedOrdersWithholdAsync,
  deleteDamagedOrderBefore, deleteDamagedOrder, getDamagedOrdersWithholdListGroupAsync, exportDamagedOrdersWithholdListGroupAsync, getDamagedOrderLogs
} from '@/api/customerservice/DamagedOrders'
import conddel from "../dialogForm/conddel.vue";
import edittable from "../dialogForm/edittable.vue";
import videoplayer from '@/views/media/video/videoplaynotdown'

const tableCols = [
  // { treeNode:true, istrue: true, prop: 'label', label: '权限', minwidth: '220',align:'left'},
  // { istrue: true, prop: 'id', label: '编号', width: '100',sortable:true},
  { istrue: true, label: '复选框', type: "checkbox", width: 60, fixed: 'left' },
  { istrue: true, prop: 'goodsPic', label: '商品图片', width: '80', type: "imagess" },
  { istrue: true, prop: 'orderInnerNo', sortable: 'custom', label: '内部单号', width: '80',type:'orderLogInfo',orderType:'orderNoInner'  },
  { istrue: true, prop: 'batchNumber', label: '批次号', sortable: 'custom', width: '80', },
  { istrue: true, prop: 'orderNo', sortable: 'custom', label: '线上订单号', width: '100' },
  { istrue: true, prop: 'sendGoodsDate', sortable: 'custom', label: '发货时间', width: '80' },
  { istrue: true, prop: 'afterSaleApproveDate', sortable: 'custom', label: '售后发起时间', width: '150' },
  { istrue: true, prop: 'platformName', sortable: 'custom', label: '平台', width: '80' },
  { istrue: true, prop: 'shopName', sortable: 'custom', label: '店铺', width: '80' },
  { istrue: true, prop: 'sendWareHouse', sortable: 'custom', label: '发货仓', width: '80' },
  { istrue: true, prop: 'expressOrder', sortable: 'custom', label: '快递单号', width: '80' },
  { istrue: true, prop: 'expressCompany', sortable: 'custom', label: '快递公司', width: '80' },
  { istrue: true, prop: 'styleCode', sortable: 'custom', label: '系列编码', width: '80' },
  { istrue: true, prop: 'goodsCode', sortable: 'custom', label: '商品编码', width: '80' },
  { istrue: true, prop: 'goodsCostPrice', sortable: 'custom', label: '商品单价', width: '80' },
  { istrue: true, prop: 'damagedGoodsCount', sortable: 'custom', label: '损耗商品数量', width: '100' },
  { istrue: true, prop: 'damagedAmount', sortable: 'custom', label: '损耗金额', width: '80' },
  { istrue: true, prop: 'orgZrDepartment', sortable: 'custom', label: '责任部门', width: '80' },
  { istrue: true, prop: 'orgZrType2', sortable: 'custom', label: '责任类型', width: '80' },
  { istrue: true, prop: 'managerUserName', sortable: 'custom', label: '责任上级', width: '80' },
  { istrue: true, prop: 'memberName', sortable: 'custom', label: '责任人', width: '80' },
  { istrue: true, prop: 'damagedReason', sortable: 'custom', label: '损耗具体原因', width: '80' },
  { istrue: true, prop: 'fileUrls', label: '定责资料', width: '200', type: "imagess", labels:'聊天记录', props: 'chatRecords', handle: (that, row) => that.showChatDetail(row) },
  // { istrue: true, prop: 'sort', label: '发起人', width: '80' },
  { istrue: true, prop: 'zrSetDate', sortable: 'custom', label: '责任计算时间', width: '80' },
  { istrue: true, prop: 'zrAppealStateText', sortable: 'custom', label: '申诉状态', width: '80' },
  // { istrue: true, prop: 'sort', label: '功能', width: '80' },
  // fixed:'right',
  {
    istrue: true, label: '功能', width: '120', type: 'button', fixed: 'right', btnList: [
      {
        label: '申诉',
        handle: (that, row) => that.zrApply(row),
        ishide: (that, row) => { return !row.showZrAppealBtn; }
      },
      {
        label: '指派',
        permission: 'api:Customerservice:DamagedOrders:SetZrMemberCustomize',
        handle: (that, row) => that.onSetZrMember(row),
        ishide: (that, row) => { return row.zrAppealState == 1; }
      },
      {
        label: '日志',
        handle: (that, row) => that.onViewlogs(row),
        permission: 'Responsibleoperationlog',
        // ishide: (that, row) => { return  row.zrAppealState ==1  ; }
      },
      // {
      //   label:'重算',
      //   handle:(that,row)=>that.zrRecalc(row),
      //   ishide:(that,row)=>{ return [1,2,3,9].indexOf(row.illegalType)<0  || that.store.getters.userName.indexOf('徐洪鹏')<0;; }
      // }
      // {
      //   label:'查看',
      //   handle:(that,row)=>that.seeFiles(row),
      //   ishide:(that,row)=>{ return row.fileUrls?false:true; }
      // }
    ]
  }
];

const tableCols1 = [
  { istrue: true, prop: 'orderInnerNo', sortable: 'custom', label: '内部单号', width: '80' },
  { istrue: true, prop: 'platformNames', sortable: 'custom', label: '平台', width: '100' },
  { istrue: true, prop: 'shopNames', sortable: 'custom', label: '店铺', width: '150' },
  { istrue: true, prop: 'sendWareHouses', sortable: 'custom', label: '发货仓', width: '80' },
  // { istrue: true, prop: 'expressCompanys',sortable: 'custom',  label: '快递单号', width: '80' },
  { istrue: true, prop: 'expressCompanys', sortable: 'custom', label: '快递公司', width: '160' },
  { istrue: true, prop: 'zrTypes', sortable: 'custom', label: '责任部门', width: '100' },
  { istrue: true, prop: 'zrType2s', sortable: 'custom', label: '责任类型', width: '80' },
  { istrue: true, prop: 'zrUserName', sortable: 'custom', label: '责任人', width: '80' },
  { istrue: true, prop: 'damagedReasons', sortable: 'custom', label: '损耗具体原因', width: '130' },
  { istrue: true, prop: 'fileUrls', label: '定责资料', width: '80', type: "imagess" },
  { istrue: true, prop: 'zrSetDate', sortable: 'custom', label: '责任计算时间', width: '100' },
  // { istrue:true, label:'功能', width:'90', fixed:'right', type:'button'}
];

const tableHandles = [
  //{ label: "导入", handle: (that) => that.startImport() },
];
export default {
  name: 'Vue2demoLossDataStatisticsOne',
  components: { vxetablebase, MyContainer, conddel, edittable, YhUserelector, videoplayer },
  data() {
    return {
      delivery: false,
      tableDatalogs: [],
      viewthelogpopwindow: false,
      videoplayerReload: false,
      pictureid: "",
      firstname: '',
      picFileList: [],//图片上传列表
      picLists: [],
      pictureupload: false,
      tabledialog: false,
      condialog: false,
      seldialog: false,
      issearch: false,
      checked: false,
      diaChatVisible: false,
      deltitle: "",
      radio: null,
      list1: [],
      shopList: [],
      ids: [],
      damagedList: [],
      damagedList2: [],
      summaryarry: {},
      filter: {
        ZrSetDate: [],
        AfterSaleApproveDate: [],
        SendGoodsDate: [],
        shopCode: '',
        platform: null,
        startSendGoodsDate: null,
        endSendGoodsDate: null,
        startAfterSaleApproveDate: null,
        endAfterSaleApproveDate: null,
        startZrSetDate: null,
        endZrSetDate: null,
        memberName: null,
        managerUserName: null,
        zrType2: null,
        expressOrder: null,
        styleCode: null,
        goodsCode: null,
        zrDepartment: null,
        sendWareHouseId: null,
        memberNameDDid: null,
        sendWareHouse: null,
        batchNumber: ''
      },
      userList: [],
      delparams: {},
      pickerOptions: pickerOptions,
      newWareHouseList: [],
      total: 0,
      allorderList: [],
      sels: [],
      chatlist: [],
      that: this,
      permissionTree: [],
      listLoading: false,
      tableCols: tableCols,
      tableCols1: tableCols1,
      controlslogs: {
        logstimerange: [],
        damagedOrderId: null,
        userName: '',
        startTime: null,
        endTime: null,
        currentPage: 0,
        pageSize: 0,
        orderBy: '',
        isAsc: true,
      },
      dialogHisVisible: false,
      list: [],
      orderNo: null,
      platformList: [{ label: '天猫', value: 1 }, { label: '拼多多', value: 2 }, { label: '阿里巴巴', value: 4 },
      { label: '抖音', value: 6 }, { label: '京东', value: 7 }, { label: '淘工厂', value: 8 }, { label: '淘宝', value: 9 }, { label: '苏宁', value: 10 },],
      zrAppealStateList: [{ name: '申诉已拒绝', value: -1 }, { name: '未申诉', value: 0 }, { name: '申诉中', value: 1 }, { name: '申诉已通过', value: 2 },
      { name: '已指派', value: 3 },],
      middleFilter: null,
      acceptParams: null,
    };
  },

  async mounted() {
    let end = new Date();
    let start = new Date();
    start.setDate(start.getDate() - 30);
    this.filter.SendGoodsDate = [formatTime(start, "YYYY-MM-DD"), formatTime(end, "YYYY-MM-DD")]
    var res = await getAllWarehouse();
    this.newWareHouseList = res.data.filter((x) => { return x.name.indexOf('代发') < 0; });
    this.getZrDept();
    this.getShopList();
    middlevue.$on('zrGetList', (row) => {
      this.listLoading = true
      if (row.sendGoodsDateIsNull) {
        this.filter.SendGoodsDate = row.sendGoodsDate
      } else {
        this.filter.SendGoodsDate = [row.sendGoodsDate, row.sendGoodsDate]
      }
      this.filter.startSendGoodsDate = this.filter.SendGoodsDate[0];
      this.filter.endSendGoodsDate = this.filter.SendGoodsDate[1];
      this.filter.sendWareHouse = row.sendWareHouse
      this.filter.platform = row.platform
      this.filter.styleCode = row.styleCode
      this.filter.goodsCode = row.goodsCode
      this.filter.zrDepartment = row.zrDepartment
      this.filter.zrType2 = row.zrType2
      this.filter.memberName = row.memberName
      if (this.filter.sendWareHouse) {
        this.filter.sendWareHouseId = this.newWareHouseList.find(x => x.name == this.filter.sendWareHouse).wms_co_id
      }
      setTimeout(() => {
        this.getlist();
      }, 800);
    })

    middlevue.$on('shReasonGetList', (row) => {
      if(row.styleCode){
        console.log('存在数据',row)
        this.filter.styleCode = row.styleCode;
        this.filter.zrDepartment = row.zrDepartment;
        this.filter.zrType2 = row.zrType2;
        this.filter.SendGoodsDate = row.SendGoodsDate;
        this.$nextTick(() => {
          this.getlist();
        })
      }
    })


    this.onSearch();
  },
  beforeDestroy() {
    middlevue.$off('zrGetList')
    middlevue.$off('shReasonGetList')
  },
  methods: {
    tabSwitch(params){
      let hasUpdated = false;  // 标志是否有字段被更新
      // 遍历params对象的所有键
      Object.keys(params).forEach(key => {
        // 检查key是否存在于this.filter中
        if (this.filter.hasOwnProperty(key)) {
          // 是否需要更新值
          if (this.filter[key] !== params[key]) {
            // 将params的值赋给对应的this.filter字段
            this.$set(this.filter, key, params[key]);
            hasUpdated = true;  // 标志字段已更新
          }
        }
      });
      // 如果有字段被更新，则调用this.onSearch
      if (hasUpdated) {
        this.filter.SendGoodsDate = []
        this.filter.startSendGoodsDate = null
        this.filter.endSendGoodsDate = null
        this.filter.SendGoodsDate = [params.startSendGoodsDate,params.endSendGoodsDate]
        this.delivery = true
          this.onSearch();
      }
    },
    shopIncident(e) {
      if (e && e == true && !this.filter.zrDepartment) {
        this.$message.warning('请先选择责任部门')
      }
    },
    videoshow(item){
      this.videoUrl = item.SendTextContent;
      this.videoplayerReload = true;
    },
    showChatDetail(row){
      this.chatlist = JSON.parse(row.chatRecords);
      this.chatlist.map(item=>{
        if(item.SendTime){
          item.SendTime = item.SendTime.replace("T", " ");
        }
      })
      this.firstname = this.chatlist[0].SendName;
      this.diaChatVisible = true;
    },
    async remoteMethod(e) {
      const { data, success } = await QueryAllDDUserTop100({ keywords: e })
      if (success) {
        this.userList = data?.map(item => {
          return { label: item.userName, value: item.ddUserId, extData: item }
        });
      }
    },
    //排序查询
    async sortchange(column) {
      if (!column.order) {
        this.controlslogs.orderBy = 'createdTime';
        this.controlslogs.isAsc = false;
      } else {
        this.controlslogs.orderBy = column.prop
        this.controlslogs.isAsc = column.order.indexOf("descending") == -1 ? true : false
        this.onSearch();
      }
    },
    //页面数量改变
    detailSizechange(val) {
      this.controlslogs.currentPage = 1;
      this.controlslogs.pageSize = val;
      this.onSearch();
    },
    //当前页改变
    detailPagechange(val) {
      this.controlslogs.currentPage = val;
      this.onSearch();
    },
    async logoff() {
      this.viewthelogpopwindow = false
      this.controlslogs.logstimerange = [];
      this.controlslogs.userName = null;
      this.controlslogs.damagedOrderId = null;

    },
    async onViewlogs(row) {
      if (this.controlslogs.damagedOrderId === null) {
        this.controlslogs.damagedOrderId = row.id
      }
      this.controlslogs.startTime = null;
      this.controlslogs.endTime = null;
      if (this.controlslogs.logstimerange && this.controlslogs.logstimerange.length > 1) {
        this.controlslogs.startTime = this.controlslogs.logstimerange[0];
        this.controlslogs.endTime = this.controlslogs.logstimerange[1];
      }
      const { data } = await getDamagedOrderLogs(this.controlslogs)
      data.list.forEach(obj => {
        obj.operationcontent = `将 ${obj.changeInfo} 中 ${obj.oldValue} 改为 ${obj.newValue}`;
      });
      this.tableDatalogs = data.list
      this.viewthelogpopwindow = true;
      return this.tableDatalogs.sort((a, b) => new Date(b.createdTime) - new Date(a.createdTime));//根据时间倒序排序
    },
    async operateSubmit() {
      this.$nextTick(() => {
        let pictureid = this.pictureid
        this.$refs.edittable.personincharge(this.picLists, pictureid);
        this.pictureupload = false
      });
    },
    delImg(item, i) {
      debugger
      this.picLists.splice(i, 1)
    },
    //图片上传成功回调
    async handleSuccess({ data }) {
      if (this.picLists.length === 9) {
        this.$message({ message: "导入最多为9张", type: "warning" });
        return
      }
      this.picLists.push(data.url)
      this.$message({ message: "上传成功", type: "success" });
    },
    tocreateimgbility(value, row) {
      this.pictureid = row.id
      this.picLists = row.fileUrls
      this.pictureupload = true;
    },
    handleUpdateTemplatepageclose() {
      this.tabledialog = false;
      this.onSearch();
    },
    async saveorder() {
      this.$nextTick(() => {
        this.$refs.edittable.detaileditsave();
      });
    },
    allEditOrder() {
      if (this.ids.length == 0) {
        this.$message("请选择至少一条数据！")
        return
      }
      this.tabledialog = true;
      this.$nextTick(() => {
        this.$refs.edittable.onchangeplatform()
      })
    },
    async selweidu(val) {
      this.list = null;

      this.onSearch();

    },
    issearchfuc(val) {
      this.delparams = val;
      this.issearch = true;
    },
    disabledfuc() {
      this.issearch = false;
    },
    async daochufuc(val) {
      if (val == 'one') {
        this.exportProps();
      } else if (val == 'two') {
        const pager = this.$refs.pager.getPager()
        this.filter.startSendGoodsDate = this.filter.SendGoodsDate[0];
        this.filter.endSendGoodsDate = this.filter.SendGoodsDate[1];

        this.filter.startAfterSaleApproveDate = this.filter.AfterSaleApproveDate[0];
        this.filter.endAfterSaleApproveDate = this.filter.AfterSaleApproveDate[1];

        this.filter.startZrSetDate = this.filter.ZrSetDate[0];
        this.filter.endZrSetDate = this.filter.ZrSetDate[1];



        const para = {
          ...pager,
          ...this.filter
        }
        const { data } = await exportDamagedOrdersWithholdListGroupAsync(para)
        const aLink = document.createElement("a");
        let blob = new Blob([data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '损耗订单-财务/人事导出' + new Date().toLocaleString() + '.xlsx')
        aLink.click()
      }
    },
    delby(delname) {

      if (delname == 'sel') {
        if (this.allorderList.length == 0) {
          this.$message("请先选择数据!")
          return
        }
        this.delparams = {
          ids: this.ids
        }
        this.delone();
        this.seldialog = true;

      } else if (delname == 'condition') {
        this.deltwo();
        this.condialog = true;
        this.issearch = false;
      }
    },
    async delone() {
      // this.ids = this.allorderList.map(item=> item.id)
      let res = await deleteDamagedOrderBefore({ ids: this.ids })
      if (!res.success) {
        return
      }
      this.deltitle = res.data.meg;
    },
    async deltwo() {
      // let params = {
      //   id: this.ids,
      //   ...this.filter
      // }
      // let res = await deleteDamagedOrderBefore({id: this.ids})
      // if(!res.success){
      //   return
      // }
      // this.deltitle = res.data.meg;
    },
    async issuredelone() {

      let res = await deleteDamagedOrder(this.delparams)
      if (!res.success) {
        return
      }
      this.condialog = false;
      this.seldialog = false;
      this.onSearch();
      this.$message.success("删除成功！")
    },
    async issuredel() {
      if (!this.issearch) {
        this.$message("请先点查询确认后再进行删除操作！")
        return
      }
      this.delparams.ids = null;
      let res = await deleteDamagedOrder(this.delparams)
      if (!res.success) {
        return
      }
      this.condialog = false;
      this.seldialog = false;
      this.onSearch();
      this.$message.success("删除成功！")
    },
    async onchangeplatform(val) {
      this.filter.shopCode = null;
      const res1 = await getshopListt({ platform: val, CurrentPage: 1, PageSize: 100000 });
      this.shopList = res1.data.list
    },
    seeFiles(row) {
    },
    zrType2change() {
      this.$forceUpdate();
    },
    async getShopList() {
      const res1 = await getAllShopList();
      this.shopList = res1.data;
      return;
    },
    async exportProps() {
      const pager = this.$refs.pager.getPager()
      this.filter.startSendGoodsDate = this.filter.SendGoodsDate[0];
      this.filter.endSendGoodsDate = this.filter.SendGoodsDate[1];
      this.filter.startAfterSaleApproveDate = this.filter.AfterSaleApproveDate[0];
      this.filter.endAfterSaleApproveDate = this.filter.AfterSaleApproveDate[1];
      this.filter.startZrSetDate = this.filter.ZrSetDate[0];
      this.filter.endZrSetDate = this.filter.ZrSetDate[1];
      const para = {
        ...pager,
        ...this.filter
      }
      const { data } = await exportDamagedOrdersWithholdAsync(para)
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '损耗订单-明细导出' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    chooseCode(val) {
      // this.allorderList = []
      val.map((item) => {
        item.isclick = false
      })
      this.allorderList = val;
      this.ids = this.allorderList.map(item => item.id)
    },
    async getZrDept() {
      let res = await getDamagedOrdersZrDept();
      this.damagedList = res?.data;
      // damagedList2
    },
    async getZrType(name) {
      let res = await getDamagedOrdersZrType(name);
      this.damagedList2 = res.data;
      this.filter.zrType2 = ""
    },
    //指派
    async onSetZrMember(row) {
      let self = this;
      this.$showDialogform({
        path: `@/views/order/LossOrderCostStatistics/dialogForm/assignmentRespon.vue`,
        title: '责任指派',
        autoTitle: false,
        args: { ...row, ...self.filter, formtype: '指派' },
        height: 300,
        width: '80%',
        callOk: self.onSearch
      })
    },
    //申诉
    async zrApply(row) {
      // debugger
      let self = this;
      this.$showDialogform({
        path: `@/views/order/LossOrderCostStatistics/dialogForm/assignmentRespon.vue`,
        title: '责任申诉',
        autoTitle: false,
        args: { ...row, ...self.filter, formtype: '申诉', },
        height: 300,
        width: '80%',
        callOk: self.onSearch
      })
    },
    //批量申诉
    async allzrApply(row) {
      let self = this;
      this.$showDialogform({
        path: `@/views/order/LossOrderCostStatistics/dialogForm/assignmentRespon.vue`,
        title: '批量责任申诉',
        autoTitle: false,
        args: { ...row, ...self.filter, formtype: '批量申诉', allorderList: self.allorderList },
        height: 300,
        width: '80%',
        callOk: self.onSearch
      })
    },
    //查询第一页
    onSearch() {
      // if (!this.filter.timerange) {
      //   this.$message({ message: "请选择日期", type: "warning" });
      //   return;
      // }\
      // if(!this.checked){
      this.list1 = [];
      this.$refs.pager.setPage(1);
      this.getlist();
      return
      // }
      // this.getneitable();
    },
    async getneitable() {
      let params = {
        CurrentPage: 1,
        PageSize: 50,
        ...this.filter,
      }
      let res = await getDamagedOrdersWithholdListGroupAsync(params)
      if (!res.success) {
        return
      }
      res.data.list.map((item) => {
        if (!item.fileUrls) {

          item.fileUrls = [];
        } else {
          if (item.fileUrls.indexOf(',') != -1) {
            if (item.fileUrls) {
              let newarr = item.fileUrls.split(',')
              item.fileUrls = newarr;
            }
          } else {
            item.fileUrls = [item.fileUrls]
          }
        }
      })
      this.list1 = res.data.list;
      this.total = res.data.total;
    },
    //排序
    sortchange({ order, prop }) {
      this.listLoading = true
      if (prop) {
        this.filter.orderBy = prop
        this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getlist()
      }
    },
    // 获取列表
    async getlist() {
      if (this.checked) {
        this.getneitable();
        return
      }
      const pager = this.$refs.pager.getPager()
      this.filter.SendGoodsDate = this.filter.SendGoodsDate ? this.filter.SendGoodsDate : []
      this.filter.startSendGoodsDate = this.filter.SendGoodsDate[0];
      this.filter.endSendGoodsDate = this.filter.SendGoodsDate[1];
      this.filter.AfterSaleApproveDate = this.filter.AfterSaleApproveDate ? this.filter.AfterSaleApproveDate : []
      this.filter.startAfterSaleApproveDate = this.filter.AfterSaleApproveDate[0];
      this.filter.endAfterSaleApproveDate = this.filter.AfterSaleApproveDate[1];
      this.filter.ZrSetDate = this.filter.ZrSetDate ? this.filter.ZrSetDate : []
      this.filter.startZrSetDate = this.filter.ZrSetDate[0];
      this.filter.endZrSetDate = this.filter.ZrSetDate[1];
      let para = {}
      // if (!this.middleFilter) {
      para = {
        ...pager,
        ...this.filter
      }
      console.log(this.filter, 'this.filterGetList');
      // console.log(this.middleFilter, 'this.middleFilter');
      this.listLoading = true
      const res = await getDamagedOrdersWithholdListAsync(para)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.summaryarry = res.data.summary;
      this.total = res.data.total
      res.data.list.map((item) => {
        if (!item.fileUrls) {
          item.fileUrls = [];
        } else {
          if (item.fileUrls.indexOf(',') != -1) {
            if (item.fileUrls) {
              let newarr = item.fileUrls.split(',')
              item.fileUrls = newarr;
            }
          } else {
            item.fileUrls = [item.fileUrls]
          }
        }
        if (!item.goodsPic) {
          item.goodsPic = [];
        } else {
          if (item.goodsPic.indexOf(',') != -1) {
            item.goodsPic = item.goodsPic.split(",")
          } else {
            item.goodsPic = [item.goodsPic]
          }
        }
      })
      this.list = res.data.list;
      // this.dictionaries = listToTree(_.cloneDeep(list), {
      //   id: 0,
      //   parentId: 0,
      //   name: '根节点'
      // })

      // list.forEach(d => {
      //   d._loading = false
      // })
      // const tree = listToTree(list)
      // this.dictionaryTree = tree
    },
  },
};
</script>

<style></style>


<style lang="scss" scoped>
.a {
  align-items: center;
  font-size: 15px
}

.marginleft {
  margin-right: 10px;
  margin-top: 10px;
}
.marleft{
  margin-left: auto;
}

.imgstyle img{
  max-height: 200px !important;
  // max-width: 100% !important;
}

::v-deep .el-container {
  padding: 0;
  margin: 0;
  height: 100vh;
}
.flex{
  display: flex;
}

.dialog-prompt-button {
  // display: flex;
  // justify-content: flex-end;
  margin-left: 1500px;
}

.imageList_box {
  display: flex;
  flex-wrap: wrap;

  .imageList {
    position: relative;
    width: 100px;
    height: 100px;

    .imgcss ::v-deep img {
      min-width: 100px !important;
      min-height: 100px !important;
      width: 100px !important;
      height: 100px !important;
    }


    .del {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 16px;
      width: 15px;
      height: 15px;
      border-radius: 50%;
      color: black;
      text-align: center;
      line-height: 15px;
      cursor: pointer;
    }
  }
}
</style>
