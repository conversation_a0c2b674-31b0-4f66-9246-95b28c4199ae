<template>
  <div>
    <el-row>
      <el-col>
        <div>
          <el-button-group>
            <el-button :type="groupbtnty.ty0" @click="onclick(0)">审单</el-button>
            <el-button :type="groupbtnty.ty1" @click="onclick(1)">组团打单</el-button>
            <el-button :type="groupbtnty.ty4" @click="onclick(4)">上架</el-button>
            <el-button :type="groupbtnty.ty2" @click="onclick(2)">配货</el-button>
            <el-button :type="groupbtnty.ty3" @click="onclick(3)">发货</el-button>
            <el-button style="padding:0;margin:0;">
              <el-alert style="padding:4px;margin:0;" type="warning" show-icon :closable="false" 
                  title="温馨提示:审单时长=审单时间-付款时间、打单时长=打印时间-审单时间、拣货时长=拣货时间-打印时间、发货时长=发货时间-拣货时间"> </el-alert>
            </el-button>
            <el-button style="padding:0;margin:0;">
              <el-date-picker style="width: 210px" v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd"
                       value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </el-button>
            <!-- <el-button style="padding: 0;margin: 0;">
              <el-select filterable clearable v-model="filter.orderError" placeholder="异常订单" style="width: 90px">
                <el-option label="否" :value="false"></el-option>
                <el-option label="是" :value="true"></el-option>
              </el-select>
            </el-button> -->
         </el-button-group>
        </div>
       </el-col>
      </el-row>
    <el-row>
      <el-col>
        <div id="orderpositionanalysis" :style="{height:height,width:width}" />
      </el-col>
    </el-row>
  </div>
</template>
<script>
import {getOrderPositionByNodeAnalysis} from '@/api/order/ordernodes'
import container from '@/components/my-container/noheader'
import buschar from '@/components/Bus/buschar'
import pieChart from '@/views/admin/homecomponents/PieChart'
import * as echarts from 'echarts';
export default {
  name: 'Roles',
  components: {container,buschar,pieChart},
  props:{
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '330px'
    },
    filter:{}
  },
  data() {
    return {
        parms:{},
        selectedIndex:null,
        buscharData:{},
        groupbtnty:{ty0:'warning',ty1:'primary',ty2:'primary',ty3:'primary',ty4:'primary'}
      }
  },
  async mounted() {  },
  async created() { },
  methods: {
    async onSearch() {
      if(this.selectedIndex!=null) return;
      await this.onclick(0)
    },
    async onclick(nodeindex) {
      this.selectedIndex=nodeindex;
      this.groupbtnty.ty0=this.selectedIndex==0?'warning':'primary';
      this.groupbtnty.ty1=this.selectedIndex==1?'warning':'primary';
      this.groupbtnty.ty2=this.selectedIndex==2?'warning':'primary';
      this.groupbtnty.ty3=this.selectedIndex==3?'warning':'primary';
      this.groupbtnty.ty4=this.selectedIndex==4?'warning':'primary';
      if (this.filter.timerange&&this.filter.timerange.length>1) {
          this.filter.startTime = this.filter.timerange[0];
          this.filter.endTime = this.filter.timerange[1];
      }
      else {
          this.$message({message:"请先选择日期",type:"warning"});
          return false;
      }
      this.parms=this.filter;
      this.parms.nodeIndex=this.selectedIndex;
      await this.getAnalysis()
    },
    async getAnalysis() {
      let res = await getOrderPositionByNodeAnalysis(this.parms);
      await this.initchartsline(res.data);
    },
   async initchartsline(analysisData) {
     let that=this;
     this.$nextTick(() => {
        var chartDom1 = document.getElementById('orderpositionanalysis');
        var myChart1 = echarts.init(chartDom1);
        myChart1.clear();
        var option1 = that.getoptionsline(analysisData);
        myChart1.setOption(option1);
      });
    },
    getoptionsline(element){
      var series=[]
      element.series.forEach(s=>{
         series.push({smooth: true, ...s})
      })
      var yAxis=[]
      element.yAxis.forEach(s=>{
        yAxis.push({type: 'value',minInterval:10,offset:s.offset,splitLine:s.splitLine,position:s.position,name: s.name,axisLabel: {formatter: '{value}'+s.unit}})
      }) 
      var selectedLegend={};
      if(element.selectedLegend){
       element.legend.forEach(f=>{
          if(!element.selectedLegend.includes(f)) selectedLegend[f]=false
        })
      }
      var option = {
        title: {text: element.title},
        tooltip: {trigger: 'axis'},
        legend: {
           selected:selectedLegend,
           data: element.legend,
           top: '7%'
         },
        grid: {
            top: '20%',
            left:'5%',
            right: '4%',
            bottom: '5%',
            containLabel: false
        },
        title: {
          left: 'center',
          //text: this.righttext
        },
        toolbox: {feature: {
           // magicType: {show: true, type: ['line', 'bar']},
        }},
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis:  yAxis,
        series:  series
    };
    return option;
   },
   getoptionspie(element){
        var option={tooltip:{trigger: 'item',formatter: '{a} <br/>{b} : {c} ({d}%)'},
        title: {
          left: 'center',
          text: '订单节点平均时长汇总'
        },
        series: [
              {
                name: '订单节点平均时长占比',
                type: 'pie',
                radius: [15, 100],
                center: ['45%', '55%'],
                data: [
                  // { value: 1048, name: 'Search Engine' },
                  // { value: 735, name: 'Direct' },
                ],
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                }
              }
            ],
        };
        element.pieSeries.forEach(s=>{
          option.series[0].data.push({value: s.value, name: s.name})
        })
        return option;
    },     
  }
}
</script>
