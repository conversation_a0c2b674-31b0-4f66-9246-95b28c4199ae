<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent>
        <el-row>
          <el-form-item label="统计类型">
          <el-radio-group @change="typeChange" v-model="Filter.type">
                    <el-radio
                  v-for="item in typeList"
                  :key="item.id"
                  :label="item.id">{{item.name}}             
                </el-radio>
              </el-radio-group>
        </el-form-item>
        </el-row>
        <el-row>
          <el-col :span="7">
              <el-form-item label="统计时间:">
                <el-date-picker style="width:320px"
                  v-model="Filter.timerange"
                  type="daterange"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  range-separator="至"
                  start-placeholder="从："
                  end-placeholder="到："
                  :picker-options="pickerOptions"
                ></el-date-picker>
              </el-form-item>
          </el-col>
          <el-col :span="8">
              <el-form-item v-if="Filter.type == 1" label="平台:" label-position="right" label-width="72px">
                <el-select clearable multiple filterable  v-model="Filter.platform" placeholder="请选择" class="el-select-content" style="width:500px">
                  <el-option label="所有" value=0>
                  </el-option>
                  <el-option 
                    v-for="item in platformList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">             
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item v-else-if="Filter.type == 2" label="所属店铺:" label-position="right" label-width="72px">
                <el-select clearable multiple filterable v-model="Filter.idShop" placeholder="请选择" class="el-select-content" style="width:500px">
                  <el-option label="所有" value=0 />
                <el-option 
                  v-for="item in shopList"
                  :key="item.id"
                  :label="item.shopName"
                  :value="item.id">             
                </el-option>
              </el-select>
              </el-form-item>

              <el-form-item v-else-if="Filter.type == 3" label="组长:" label-position="right" label-width="72px">
                <el-select clearable multiple filterable  v-model="Filter.idDirectorGroup" placeholder="请选择" class="el-select-content" style="width:500px">
                  <el-option label="所有" value=0 />
                  <el-option 
                  v-for="item in groupList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key">             
                  </el-option>
                </el-select>
              </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-form-item label="汇总:">
                <el-switch :width="40"
                v-model="Filter.isSumm"
                inactive-color="#228B22"
                active-text="是"
                inactive-text="否">
                </el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-form-item>
              <el-button type="primary" @click="onSearch" style="width:100px">统计</el-button>
            </el-form-item>
         </el-col>
        </el-row>
      </el-form>
    </template>
    <div style="height = 10px"></div>
    <el-row>
      <el-col :span="24">
         <div id="echartmonitChart" style="height: 500px; box-sizing:border-box; line-height: 400px;">     
        </div>
      </el-col>
    </el-row>
    <el-row>
       <el-table
      :data="SumList"
      :fit = true
      :tableCols='tableCols'
      :border=true
      max-height="400px"
      style="width: 100%;">
        <template v-for="(item, index) in tableCols">
          <el-table-column show-overflow-tooltip
          :key="index"
          :prop="item.prop" 
          :label="item.label"
          :width="item.width" 
          align="left">
            <template slot-scope="scope" >
              <span v-if="item.type==='html'" v-html="(item.formatter && item.formatter(scope.row))"></span>
              <span v-if="item.type==='format'">{{  }} </span>
                  <!-- 默认 -->
              <span v-if="!item.type" 
                    :style="item.itemStyle && item.itemStyle(scope.row)" 
                    :class="item.itemClass && item.item.itemClass(scope.row)">{{(item.formatter && item.formatter(scope.row)) || scope.row[item.prop]}}</span>
            </template>
          </el-table-column>
        </template>
    </el-table>
    </el-row>
  </my-container>
</template>
<script>

import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList,
         getDirectorGroupList as getDirectorGroupList,
         getDirectorList as getDirectorList
       } from '@/api/operatemanage/base/shop';
import {saleAmountAnalysisChartAsync} from '@/api/olderp/profit'
import * as echarts from 'echarts';
import { formatPlatform,formatLink} from "@/utils/tools";
import { time } from 'echarts';


export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
  data() {
    return {
      that:this,
      showAll : true,
      Filter: {
        timerange: [formatTime(dayjs().startOf("month"), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")],
        platform:[],
        idShop:[],
        idDirectorGroup:[],
        type:1,
        pillarElements:[],
        lineElements:[],
        isSumm:false
      },
      tableCols:[],
      typeList:[{id:1,name:"平台"},{id:2,name:"店铺"},{id:3,name:"小组"}],
      platformList:[{id:1, name:"淘系"}, {id:2, name:"拼多多"}],
      shopList:[],
      groupList:[],
      SumList: [],
      PerDayList: [],
      listLoading: false,
      pageLoading: false,
      //
      pickerOptions:{
        shortcuts: [{
            text: '今天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date().setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '昨天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 1).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '3天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 2).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '7天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 6).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '15天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 14).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '30天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 29).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }]
      },
    };
  },
  async mounted() {
    await this.getShopList();
    await this.getGroupList();
  },
  methods: {
    typeChange:function(val) {
      console.log(val)

       //清空图
       var pieChart = document.getElementById('echartmonitChart');
       var myChartPie = echarts.init(pieChart);
       myChartPie.clear();

       //清空表
       this.tableCols = []
       this.SumList = []
    },
    //所属店铺列表
    async getShopList(){
      const res1 = await getAllShopList();
      this.shopList=res1.data;
      return;
    },
    //组长列表
    async getGroupList(){
      const res = await getDirectorGroupList();
      this.groupList = res.data;
      return;
    },
    GetChartoptions(element, para){

      //数据
        var series=[]
        /*
        element.series.forEach(s=>{
          series.push({  smooth: true, label:{show:true,position:'top'}, ...s})
        })
        */

        for(var i = 0; i < element.series.length; i++)
        {
          if( i % 4 == 0 || i % 4 == 1 || i % 4 == 2)
          {
            series.push({  smooth: true, label:{show:true,position:'top',formatter: function(a){ return a.value != 0 ? a.value:''}}, ...element.series[i]})
          }
          else if( i % 4 == 3)
          {
             series.push({  smooth: true, label:{show:true,position:'top',formatter: function(a){ return a.value != 0 ? ((a.value*100).toFixed(2) + '%'):''}}, ...element.series[i]})
          }
        }

        //y轴
        var yAxis = []

        yAxis.push({type:'value', position: 'left', offset: 0 ,name:'', axisLabel:{formatter: '{value}'}})
        //yAxis.push({type:'value', position: 'left', offset: 50 ,name:'', axisLabel:{formatter: '{value}'}})
        //yAxis.push({type:'value', position: 'right', offset: 0 ,name:'', axisLabel:{formatter: '{value}'}})
        yAxis.push({type:'value', position: 'right', offset: 0 ,name:'', axisLabel:{formatter: function(a){ return a*100 + '%'} }})

      var option = {
        title: {
              text: '',
              subtext: '',
              left: 'left'
          },
        tooltip: {trigger: 'axis'},
        legend: {
            data: element.legend
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {feature: {
        }},
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis: yAxis,
        series:  series
    };
    return option;
    },
    async DealChart(){
      const para = {...this.Filter};
      if (this.Filter.timerange) {
        para.dateStart = this.Filter.timerange[0];
        para.dateEnd = this.Filter.timerange[1];
      }

      if(!(para.dateStart&&para.dateEnd)){
        this.$message({message:"请先选择日期！",type:"warning"});
        return;
      }

      var pieChart = document.getElementById('echartmonitChart');
      var myChartPie = echarts.init(pieChart);
      myChartPie.clear();

      console.log(para)
      const pieSumChart = await saleAmountAnalysisChartAsync(para);
        if (!pieSumChart?.code)  return; 
        if (!pieSumChart.data) {
         this.$message({message: "没有数据!",type: "warning",});
         return;
       }

        var optionPie = this.GetChartoptions(pieSumChart.data, para);

        console.log(JSON.stringify(optionPie))

        optionPie && myChartPie.setOption(optionPie);

        //设置表格

        //标题
        this.tableCols = []
        for(var i = 0; i < pieSumChart.data.tableHeaders.length; i++)
        {
           var obj = {prop:'_'+i,label: pieSumChart.data.tableHeaders[i]}
           this.tableCols.push(obj)
        }

        this.SumList = []
        for(var i = 0; i < pieSumChart.data.tableRows.length; i++)
        {
           var obj = {}
           for(var j = 0; j < pieSumChart.data.tableRows[i].length; j++)
           {
              console.log("obj._" + j + "='" + pieSumChart.data.tableRows[i][j]+ "'")
              eval("obj._" + j + "='" + pieSumChart.data.tableRows[i][j]+ "'")
           }
           this.SumList.push(obj)
        }
    },
    async onSearch(){
        await this.DealChart()
    },
  },
};

</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>