<template>
    <MyContainer >
       <template #header>
         <div >
           <el-radio-group v-model="radio">
             <el-radio-button  label="收支明细"></el-radio-button>
             <el-radio-button label="佳速达"></el-radio-button>
             <el-radio-button label="九方"></el-radio-button>
             <el-radio-button label="左海"></el-radio-button>
             <el-radio-button label="赤道"></el-radio-button>
             <el-radio-button label="环世"></el-radio-button>
           </el-radio-group>
       </div>
       
     </template>
       <IncomePayDetail v-show="radio=='收支明细'"/>
       <Shipout v-show="radio=='佳速达'" />
       <Jiufang v-show="radio=='九方'" />
       <LeftOcean v-show="radio=='左海'" />
       <Sogoodselle v-show="radio=='赤道'" />
       <Worldwide v-show="radio=='环世'" />
    </MyContainer>
   </template>
   
   <script>
   
   import MyContainer from "@/components/my-container";
   import IncomePayDetail from "./components/IncomePayDetail.vue";
   import Shipout from "./components/Shipout.vue";
   import LeftOcean from "./components/LeftOcean.vue";
   import Jiufang from "./components/Jiufang.vue";
   import Sogoodselle from "./components/Sogoodselle.vue";
   import Worldwide from "./components/Worldwide.vue";

   export default {
    name: 'overseasGodownIncomePayDetail',
    components: {MyContainer,IncomePayDetail,Shipout,LeftOcean,Jiufang,Sogoodselle,Worldwide
    },
    data() {
      return {
           radio:'收支明细'
      };
    },
     async mounted(){
   
     },
    methods:{
        
    }
   };
   </script>
   
   <style lang="scss" scoped>
   ::v-deep .el-radio-button__inner{
     width: 80px;
   }
   </style>
   