<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="商品编码:">
          <el-input v-model="filter.goodsCode" placeholder="商品编码"/>
        </el-form-item>
        <el-form-item label="商品名称:">
          <el-input v-model="filter.goodsName" placeholder="商品名称"/>
        </el-form-item>
        <!-- <el-form-item label="仓库:">
         <el-select v-model="filter.warehouse" placeholder="仓库">
            <el-option label="请选择" value></el-option>
            <el-option label="义乌" value="0"></el-option>
            <el-option label="昌东" value="1"></el-option>
            <el-option label="昌北" value="2"></el-option>
            <el-option label="安徽" value="3"></el-option>
          </el-select>
        </el-form-item>
         <el-form-item label="仓位:">
          <el-input v-model="filter.position" placeholder="仓位"/>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' :isSelectColumn='true' @sortchange='sortchange'
        :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
        <template slot='extentbtn'>
          <el-button-group>
            <el-button style="padding: 0;margin: 0;margin-left: 20px;">
                <el-upload class="avatar-uploader" style="width:35px;height:35px;display:block;" action="#" :show-file-list="false" :http-request="imgsearchRequest" :before-upload="beforeAvatarUpload">
                  <el-image :src="filter.tempimageUrl">
                    <div slot="error" class="image-slot">
                       <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                </el-upload>
            </el-button>
            <el-button type="primary" icon="el-icon-search" @click="searchImageslist">相似检索</el-button>
          </el-button-group>
        </template>

        <template #leftone>
            <el-table-column width="400" label="仓位信息" float="right">
                <template slot-scope="scope">
                    <!-- {{scope.row.wmsPositions.length}} -->
                    <div v-for="(item2, i) in scope.row.wmsPositions" :key="i" style="width: 100%; margin-top: 5px; display: flex; align-items: center">
                        <div style="display: flex; align-items: center;margin-bottom: 5px;"><span style="font-weight: 600">仓库：</span>
                            {{ item2.wmsName }}
                        </div>
                        <div style="display: flex; align-items: center;margin-bottom: 5px; margin-left: 10px"><span style="font-weight: 600">仓位：</span>
                            {{ item2.position }}
                        </div>
                    </div>
                </template>
            </el-table-column>
        </template>
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
    <el-drawer :title="formtitle" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="addFormVisible" 
                direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
     <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
       <div>
          <el-row style="width: 620px;padding: 0;padding-left: 50px">
             <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">图片:</el-col>
             <el-col :xs="24" :sm="15" :md="15" :lg="15" :xl="15">
                <el-upload class="avatar-uploader" action="" :show-file-list="false"  :http-request="httpRequest" :before-upload="beforeAvatarUpload">
                    <img v-if="imageUrl" :src="imageUrl" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    <div slot="tip" class="el-upload__tip">支持jpg、png、jpeg，大小不超过5M。</div>
                </el-upload>
             </el-col>
          </el-row>
      </div>
      <div class="drawer-footer">
        <el-button @click.native="addFormVisible = false">取消</el-button>
        <my-confirm-button type="submit"  :loading="addLoading" @click="onAddSubmit" />
      </div>
    </el-drawer>
  </my-container>
</template>

<script>
import {addImage,updateImage,deleteImage,queryImage,pageImages,searchImages,exportImages} from '@/api/storehouse/images'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { uploadCommonFile} from '@/api/upload/filenew'
import { formatWarehouse} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'goodsCode',label:'商品编码', width:'100',sortable:'custom'},
      {istrue:true,prop:'goodsName',label:'商品名称', width:'200'},
      {istrue:true,prop:'url',label:'图片', width:'60',type:'image'},
      {istrue:true,prop:'score',label:'相似度', width:'70'},
    //   {istrue:true,prop:'warehouse',label:'仓库', width:'70',sortable:'custom',formatter:(row)=>formatWarehouse(row.warehouse)},
    //   {istrue:true,prop:'position',label:'仓位',sortable:'custom', width:'150'},
      {istrue:true,prop:'remark',label:'备注', width:'140'},
      {istrue:true,prop:'contSign',label:'标识', width:'100'},
      {istrue:true,prop:'createdTime',label:'创建时间', width:'150',sortable:'custom'},
      {istrue:true,prop:'createdUserName',label:'创建人', width:'90',sortable:'custom'},
      {istrue:true,prop:'modifiedTime',label:'更新时间', width:'150',sortable:'custom'},
      {istrue:true,prop:'modifiedUserName',label:'更新人', width:'90',sortable:'custom'},
      {istrue:true,type:'button',label:'操作',btnList:[{label:"编辑",/*display:(row)=>{return true},display:true,*/ handle:(that,row)=>that.onEdit(row)},
                                          {label:"删除",/*display:(row)=>{return true},display:true,*/ handle:(that,row)=>that.onDelete(row)}]},
     ];
const tableHandles1=[{label:"新增", handle:(that)=>that.onAdd()},{label:"导出", handle:(that)=>that.onExport()}];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: {
         goodsCode:null,
         goodsName:null,
         warehouse :null,
         position :null,
         tempimageUrl:null,
      },
      list: [],
      pager:{OrderBy:"id",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: { disabled: false },col: { span: 22 }}}},
               rule:[ ]
        },
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,
      formtitle:"新增",
      imageUrl: '',
      dialogVisible: false,
      disabled: false
    }
  },
  async mounted() {
    this.getlist();
    await this.initform();
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
     async initform(){
       let that=this;
       this.autoform.rule= [{type:'hidden',field:'id',title:'id',value: ''},
                     {type:'input',field:'goodsCode',title:'商品编码',value: '',validate: [{type: 'string', required: true, message:'请输入商品编码'}],props:{multiple: true,clearable: true}},
                    //  {type:'upload',field:'url', title:'图片', value:'',
                    //       props:{type:"select",uploadType:"image",action: '/',accept:"image\/*",format:["jpg","jpeg","png"],maxSize:4048,multiple:false,showfilelist:false,
                    //       httpRequest : async(parms)=> { var url = await that.uploadfile(parms);  that.upLoadImageUrl= 'http://**************:9999/images/'+ url;},
                    //       onSuccess:function (res, file,fileList ) {file.url = that.upLoadImageUrl;},
                    //       onError:function ( error, file, fileList) {console.log('error',error);},
                    //       }  },
                    {type:'input',field:'goodsName',title:'商品名称',value: '' ,props:{disabled: true}},
                    {type:'select',field:'warehouse',title:'仓库',value: 0,validate: [{type: 'number', required: true, message:'请选择'}],options: [{value:0, label:'义乌'},{value:1, label:'昌东'},{value:3, label:'安徽'}]},
                    {type:'input',field:'position',title:'仓位',value: '',validate: [{type: 'string', required: true, message:'请输入仓位'}]},
                    {type:'input',field:'remark',title:'备注',value: ''},
                ]
     },
     //type:0 正常查询  1：相似查询
     async initCols(type){
         this.tableCols.forEach(f=>{
            if(f.prop=="score"&&type==0) f.istrue=false;
            else if(f.prop=="score"&&type==1) f.istrue=true;
         })
       },
     imgsearchRequest: function (parms){
         let that=this
         this.uploadtempimage(parms)
             .then(function (url) { that.filter.tempimageUrl= url })
             //.then(function (url) { that.filter.tempimageUrl= url.replace('192.168.16.13','**************');})
             .catch(function (err) { console.error(err);});
     },
     httpRequest: function (parms){
         let that=this
         this.uploadfile(parms)
             .then(function (url) { that.imageUrl=  url; })
             .catch(function (err) { console.error(err);});
     },
    async uploadfile(parms){
      var url="";
      const form = new FormData();
      form.append("file", parms.file);
      const res =await uploadCommonFile(form);
      if (res.code==1)  url = res.data.url;
      else throw new Error(res.msg);
      return url;
    },
    async uploadtempimage(parms){
      var url="";
      const form = new FormData();
      form.append("file", parms.file);
      const res =await uploadCommonFile(form);
      if (res.code==1)  url = res.data.url;
      else throw new Error(res.msg);
      return url;
    },
    beforeAvatarUpload(file) {
        const isJPG = file.type === 'image/jpeg'||  file.type ==='image/png';
        const isLt2M = file.size / 1024 / 1024 < 11;
        if (!isJPG) {
          this.$message.error('上传图片只能是 JPG 格式!');
        }
        if (!isLt2M) {
          this.$message.error('上传图片大小不能超过 4MB!');
        }
        return isJPG && isLt2M;
      },
    async onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      await this.initCols(0)
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      this.listLoading = true
      const res = await pageImages(params)
      this.listLoading = false
      if (!res?.success) {return }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {d._loading = false})
      this.list = data
    },
   async searchImageslist() {
      await this.initCols(1)
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      this.listLoading = true
      const res = await searchImages(params)
      this.listLoading = false
      if (!res?.success) {return}
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => { d._loading = false})
      this.list = data
    },
  async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
  async onAdd(row) {
      this.formtitle='新增';
      this.addFormVisible = true
      this.imageUrl="";
      this.$nextTick( async function () {
          await  this.autoform.fApi.disabled(false, 'goodsCode')
          await  this.autoform.fApi.resetFields()
      });
    },
  async onEdit(row) {
      this.formtitle='编辑';
      this.addFormVisible = true
      const res = await queryImage(row.id)
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
         this.autoform.fApi.resetFields()
      await this.autoform.fApi.setValue(res.data)
      await this.autoform.fApi.disabled(true, 'goodsCode')
      this.imageUrl=res.data.url;
    },
  async onDelete(row) {
    if (!row.id) {
       this.$message({type: 'warning',message: '参数错误!'});
       return;
      }
      this.$confirm('确认删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
            const res = await deleteImage(row.id)
            if (!res?.success) {return }
            this.$message({type: 'success',message: '删除成功!'});
            await this.getlist()
        }).catch(() => {
          this.$message({type: 'info',message: '已取消删除'});
        });
  },
  async onEditSubmit() {
      this.addFormVisible = true
      await onAddSubmit();
    },
  async onAddSubmit() {
      this.addLoading=true;
      if(!this.imageUrl) {
          this.$message.error('图片不能为空');
          return;
      }
      await this.autoform.fApi.validate(async (valid, fail) => {
        debugger
      if(valid){
          const formData = this.autoform.fApi.formData();
          formData.url=this.imageUrl;
          var res;
          if(!formData.id)
            res = await addImage(formData);
          else
            res = await updateImage(formData);

          this.addLoading=false;
          if(res.code==1){
            this.getlist();
            this.addFormVisible=false;
         }
        }else{
          //todo 表单验证未通过
           this.addLoading=false;
        }
     })
    },
    async onExport() {
     if (this.onExporting) return;
     try{
        const params = {...this.pager,... this.filter}
        var res= await exportImages(params);
        if(!res?.data) return
        
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','商品库位信息导出_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click()
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
      this.onExporting=false;
     },
    selsChange: function(sels) {
      this.sels = sels
    }
  }
}
</script>
<style scoped>
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
  .searchavatar {
    width: 35px;
    height: 35px;
    display: block;
  }
</style>