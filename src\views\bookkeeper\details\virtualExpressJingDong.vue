<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="">
                    <el-date-picker style="width: 110px" v-model="filter.yearMonth" type="month" format="yyyyMM"
                        value-format="yyyyMM" placeholder="年月">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="" label-position="right">
                    <el-input v-model="filter.orderNumber" placeholder="原始线上订单号" style="width:160px;" clearable />
                </el-form-item>
                <el-form-item label="" label-position="right">
                    <el-input v-model="filter.expressStatus" placeholder="物流状态" style="width:160px;" clearable />
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onImport2">导入</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='ZTCKeyWordList' :showsummary='true' :summaryarry='summaryarry' @select='selectchange'
            :isSelection='false' :tableCols='tableCols' :loading="listLoading" :isSelectColumn='false'>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>


        <el-dialog :title="importTitle" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading"
            :close-on-click-modal="false">
            <div style="display: flex;flex-direction: column;justify-content: center;">

                <el-date-picker style="width: 120px;margin-top: 20px; margin-bottom: 20px;" v-model="importUseMonth"
                    type="month" format="yyyyMM" value-format="yyyyMM" placeholder="导入月份"></el-date-picker>

                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>

            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>


    </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { GetVirtualExpressJD as getPageList } from '@/api/monthbookkeeper/financialDetail'
import { ImportJingDongVirtualExpressAsync } from '@/api/monthbookkeeper/import'
const tableCols = [
    { istrue: true, prop: 'yearMonth', label: '年月', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'numberExpress', label: '快递单号', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'expressCompany', label: '快递公司', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'lanShouTime', label: '揽收时间', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'zuiXinTime', label: '最新时间', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'shiXiao', label: '时效', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'expressStatus', label: '物流状态', sortable: 'custom', width: '120' },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
    data() {
        return {
            that: this,
            filter: {
                yearMonth: null,
                shopCode: null,
                shopName: null,
                orderNumber: null,
                numberExpress: null,
            },
            ZTCKeyWordList: [],
            tableCols: tableCols,
            summaryarry: {},
            total: 0,
            pager: { OrderBy: "id", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],


            fileList: [],
            importTitle: "",
            importFeeType: "",
            importUseMonth: null,
            importLoading: false,
            importVisible: false,
        };
    },
    async mounted() {
    },
    methods: {
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onRefresh() {
            this.onSearch()
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getList();
        },
        async getList() {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter,
            };
            this.listLoading = true;

            const res = await getPageList(params);

            this.listLoading = false;
            this.total = res.data?.total
            this.ZTCKeyWordList = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        onImport2() {
            this.importTitle = "导入虚拟发货快递"
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            if (!this.importUseMonth) {
                this.$message({ message: "请先选择月份！", type: "warning" });
                return;
            }
            if (this.file == null) return this.$message.error('请上传文件')
            const form = new FormData();
            form.append("upfile", this.file);
            form.append("yearmonth", this.importUseMonth);
            this.importLoading = true
            let res = await ImportJingDongVirtualExpressAsync(form)
            this.importLoading = false
            if (res?.success) {
                this.$message.success('正在后台导入中,请稍后刷新界面查看....')
                this.importVisible = false
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>
