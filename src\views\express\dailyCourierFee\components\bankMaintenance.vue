<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <div>
          <queryCondition ref="refqueryCondition" :valueChanged.sync="topfilter" />
        </div>
        <!-- <el-select v-model="ListInfo.expressCompanyId" clearable filterable placeholder="快递公司" class="publicCss"
          @change="getprosimstatelist(2)">
          <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-select v-model="ListInfo.prosimstateId" clearable filterable placeholder="快递站点" class="publicCss">
          <el-option label="暂无站点" value="" />
          <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
        </el-select>
        <el-select v-model="ListInfo.warehouseId" clearable filterable placeholder="发货仓库" class="publicCss">
          <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select> -->
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="onAddMethod">新增</el-button>
      </div>
    </template>
    <vxetablebase :ispoint="false" :id="'bankMaintenance202410311830'" :tablekey="'bankMaintenance202410311830'"
      ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
      :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="120">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" @click="onEdit(row)">编辑</el-button>
              <el-button type="text" @click="onDelete(row)"><span style="color: red;">删除</span></el-button>
              <el-button type="text" @click="onInitiatingProcess(row)">发起流程</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog :title="ruleTitle" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
      <div style="padding: 30px 5px 0 5px;">
        <el-form :model="ruleForm" :rules="editrules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="快递公司" :label-width="'125px'" prop="expressCompanyId">
                <el-select v-model="ruleForm.expressCompanyId" placeholder="快递公司" class="editCss" clearable
                  @change="getprosimstatelist(1)">
                  <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item label="发货仓库" :label-width="'125px'" prop="warehouseId">
                <el-select v-model="ruleForm.warehouseId" clearable filterable placeholder="请选择发货仓库" class="editCss">
                  <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="快递公司名称" :label-width="'125px'" prop="expressCompanyFullName">
                <el-input v-model.trim="ruleForm.expressCompanyFullName" placeholder="请输入快递公司名称" maxlength="50"
                  clearable class="editCss" />
              </el-form-item>
              <el-form-item label="支付方式" :label-width="'125px'" prop="paymentMethod">
                <el-input v-model.trim="ruleForm.paymentMethod" placeholder="请输入支付方式" maxlength="50" clearable
                  class="editCss" />
              </el-form-item>
              <el-form-item label="账号" :label-width="'125px'" prop="accountNumber">
                <el-input v-model.trim="ruleForm.accountNumber" placeholder="请输入账号" maxlength="50" clearable
                  class="editCss" />
              </el-form-item>
              <el-form-item label="面单" :label-width="'125px'" prop="waybill">
                <el-input-number v-model.trim="ruleForm.waybill" placeholder="请输入面单金额" :min="-*********"
                  :max="*********" :precision="2" :controls="false" class="editCss" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="快递站点" :label-width="'125px'" prop="prosimstateId">
                <el-select v-model="ruleForm.prosimstateId" placeholder="请选择快递站点" clearable class="editCss">
                  <el-option label="暂无站点" value="" />
                  <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName"
                    :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item label="付款类型" :label-width="'125px'" prop="payType">
                <el-select v-model="ruleForm.payType" placeholder="付款类型" class="editCss" clearable filterable>
                  <el-option label="三日结" value="三日结" />
                  <el-option label="预付款" value="预付款" />
                  <el-option label="面单" value="面单" />
                  <el-option label="月结" value="月结" />
                </el-select>
              </el-form-item>
              <el-form-item label="仓库分类" :label-width="'125px'" prop="warehouseClassification">
                <el-select v-model="ruleForm.warehouseClassification" clearable filterable placeholder="请选择仓库分类"
                  class="editCss">
                  <el-option v-for="item in warehouseOptions" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
              <el-form-item label="账户名" :label-width="'125px'" prop="accountName">
                <el-input v-model.trim="ruleForm.accountName" placeholder="请输入账户名" maxlength="50" clearable
                  class="editCss" />
              </el-form-item>
              <el-form-item label="开户行" :label-width="'125px'" prop="bankName">
                <el-input v-model.trim="ruleForm.bankName" placeholder="请输入开户行" maxlength="50" clearable
                  class="editCss" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSingleSave">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="flowTitle" :visible.sync="flowVisible" width="45%" v-dialogDrag style="margin-top: -13vh;"
      :close-on-click-modal="false">
      <div>
        <topUpApproval ref="reftopUpApproval" v-if="flowVisible" :infoFormData="infoFormData"
          @closeCallback="closeCallback" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="flowVisible = false">取 消</el-button>
        <el-button type="primary" @click="onStorageMethodDebounced(0)">暂 存</el-button>
        <el-button type="primary" @click="onStorageMethodDebounced(1)">发起流程</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import { addOrUpdateExpressBankInfoy, deleteExpressBankInfoEntity, getExpressBankInfoList, getExpressComanyAll, getExpressComanyStationName } from "@/api/express/express";
import dayjs from 'dayjs'
import { formatTime } from "@/utils";
import topUpApproval from "./topUpApproval.vue";
import { warehouselist, formatWarehouseNew } from "@/utils/tools";
import queryCondition from "./queryCondition.vue";
const warehouseOptions = [
  '义乌仓',
  '南昌仓东',
  '罗兵云仓',
  '上海嘉定仓',
  '代发仓',
  '湖北武汉仓',
  '杭州仓',
  '西安港务仓',
  '广东江门仓',
  '江苏昆山仓',
  '广东东莞仓',
  '安徽仓',
  '湖南云仓',
  '苏州云仓',
  '西安威阳仓（西安分仓)',
  '西安仓',
  '江苏常熟仓',
  '江西抚州仓',
  '上海金山仓',
  '东莞全品类仓',
  '其他',
]
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'expressCompanyId', label: '快递公司', formatter: (row) => row.expressCompanyName ? row.expressCompanyName : '', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'prosimstateId', label: '快递站点', formatter: (row) => row.prosimstateName ? row.prosimstateName : '', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouseId', label: '发货仓库', formatter: (row) => formatWarehouseNew(row.warehouseId), },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'expressCompanyFullName', label: '快递公司名称', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouseClassification', label: '仓库分类', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'accountName', label: '账户名', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'accountNumber', label: '账号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'bankName', label: '开户行', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'payType', label: '付款类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'paymentMethod', label: '支付方式', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'waybill', label: '面单', },
]
export default {
  name: "bankMaintenance",
  components: {
    MyContainer, vxetablebase, topUpApproval, queryCondition
  },
  data() {
    return {
      topfilter: {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
      },
      warehouseOptions,
      infoFormData: {},
      flowTitle: '新增',
      flowVisible: false,
      ruleTitle: '新增',
      ruleForm: {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
        expressCompanyFullName: null,
        warehouseClassification: null,
        accountName: null,
        accountNumber: null,
        bankName: null,
        prosimstateId: null,
        payType: null,
        paymentMethod: null,
        waybill: null,
      },
      editrules: {
        expressCompanyId: [{ required: true, message: '请选择快递公司', trigger: 'change' }],
        prosimstateId: [{ required: true, message: '请选择快递站点', trigger: 'change' }],
        warehouseId: [{ required: true, message: '请选择发货仓库', trigger: 'change' }],
        expressCompanyFullName: [{ required: true, message: '请输入快递公司名称', trigger: 'blur' }],
        warehouseClassification: [{ required: true, message: '请输入仓库分类', trigger: 'blur' }],
        accountName: [{ required: true, message: '请输入账户名', trigger: 'blur' }],
        accountNumber: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        bankName: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
        paymentMethod: [{ required: true, message: '请输入支付方式', trigger: 'blur' }],
        payType: [{ required: true, message: '请输入付款类型', trigger: 'blur' }],
        waybill: [{ required: true, message: '请输入面单金额', trigger: 'blur' }],
      },
      dialogVisible: false,//新增编辑弹窗
      formatWarehouseNew,
      warehouselist,//发货仓库
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      expresscompanylist: [],//快递公司
      prosimstatelist: [],//快递站点
    }
  },
  async mounted() {
    // await this.getList()
    await this.init()
  },
  methods: {
    //防抖
    onStorageMethodDebounced: _.debounce(function (param) {
      this.onStorageMethod(param);
    }, 1000),
    closeCallback() {
      this.flowVisible = false
      this.getList()
    },
    onStorageMethod(val) {
      const argument = { id: 0 }
      this.$refs.reftopUpApproval.onSingleSave(val, argument)
    },
    onInitiatingProcess(row) {
      this.infoFormData = row
      this.flowTitle = '发起流程'
      this.flowVisible = true
    },
    onSingleSave() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          const { success } = await addOrUpdateExpressBankInfoy(this.ruleForm)
          if (success) {
            this.$message.success('操作成功')
            this.dialogVisible = false
            this.getList()
          } else {
            this.$message.error('操作失败')
          }
        }
      })
    },
    onCleardataMethod() {
      this.ruleForm = {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
        expressCompanyFullName: null,
        warehouseClassification: null,
        accountName: null,
        accountNumber: null,
        bankName: null,
      }
    },
    onAddMethod() {
      this.onCleardataMethod()
      this.ruleTitle = '新增'
      this.dialogVisible = true
    },
    async onDelete(row) {
      this.$confirm('是否删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await deleteExpressBankInfoEntity({ id: row.id })
        if (success) {
          this.$message.success('删除成功')
          this.getList()
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      });
    },
    async onEdit(row) {
      this.onCleardataMethod()
      let id = row.expressCompanyId
      let res = await getExpressComanyStationName({ id: id });
      if (res?.code) {
        this.prosimstatelist = res.data
      }
      this.ruleForm = JSON.parse(JSON.stringify(row))
      this.ruleForm.expressCompanyId = String(this.ruleForm.expressCompanyId);
      this.ruleForm.prosimstateId = String(row.prosimstateId);
      this.ruleTitle = '编辑'

      this.dialogVisible = true
    },
    async init() {
      const res = await getExpressComanyAll({});
      if (!res?.success) return
      this.expresscompanylist = res.data;
      this.$nextTick(() => {
        this.$refs.refqueryCondition.init()
      })
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给当前月第一天至今天
        // this.ListInfo.startCreateTime = dayjs().startOf('month').format('YYYY-MM-DD')
        // this.ListInfo.endCreateTime = dayjs().format('YYYY-MM-DD')
        // this.timeRanges = [this.ListInfo.startCreateTime, this.ListInfo.endCreateTime]
      }
      this.loading = true
      const { data, success } = await getExpressBankInfoList({ ...this.ListInfo, ...this.topfilter })
      this.loading = false
      if (success && data && data.list) {
        this.tableData = data.list
        this.total = data.total
        // this.summaryarry = data.summary

        let summary = data.summary || {}

        const resultsum = {};
        Object.entries(summary).forEach(([key, value]) => {
          resultsum[key] = formatNumber(value);
        });
        function formatNumber(number) {
          const options = {
            useGrouping: true,
          };
          return new Intl.NumberFormat('zh-CN', options).format(number);
        }
        this.summaryarry = resultsum
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async getprosimstatelist(val) {
      let id;
      if (val == 1) {
        id = this.ruleForm.expressCompanyId
        this.ruleForm.prosimstateId = null
      } else if (val == 2) {
        id = this.ListInfo.expressCompanyId
        this.ListInfo.prosimstateId = null
      }
      let res = await getExpressComanyStationName({ id: id });
      if (res?.code) {
        this.prosimstatelist = res.data
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 170px;
    margin-right: 5px;
  }
}

.editCss {
  width: 80%;
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}
</style>
