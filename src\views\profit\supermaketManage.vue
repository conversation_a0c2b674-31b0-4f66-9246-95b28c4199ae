<template>
    <div style="height:100%;width:100%;">
        <el-tabs v-model="activeName" style="height: 94%; width:100%;" @tab-click="tabclick">
            <el-tab-pane label="超市商品管理" name="first" style="height: 100%;">
                <baseSupermarketGoodManage ref="baseSupermarketGoodManage" />
            </el-tab-pane>
            <el-tab-pane label="超市订单管理" name="second" style="height: 100%; overflow: auto;">
                <orderSupermarketGoodManage ref="orderSupermarketGoodManage" />
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
  
<script>
//import {  } from '@/api/express/express'    
import baseMenuManage from '@/views/profit/baseMenuManage.vue'
import { getGwInfo, getIntegralRuleAsync, saveIntegralRuleAsync } from '@/api/profit/orderfood'

import dingdingUserAttendance from '@/views/operatemanage/base/dingdingUserAttendance.vue'
import orderSupermarketGoodManage from '@/views/profit/orderSupermarketGoodManage.vue'
import baseSupermarketGoodManage from '@/views/profit/baseSupermarketGoodManage.vue'
export default {
    name: 'DingDingShow',
    components: { baseMenuManage, orderSupermarketGoodManage,baseSupermarketGoodManage },
    data () {
        return {
            activeName: 'first',
            form: {
                gwczList: [],
                gdvalue: '',
                gdvalue1: '',
                zsvalue: '',
                zsEnabled: null,
                syvalue: '',
                syEnabled: null,
                zsqlvalue: '',
                zsqlEnabled: null,
                syqlvalue: '',
                syqlEnabled: null,

            },
            gwList: [],
            ruleList: [],

        }
    },
    async mounted () {
        

    },
    methods: {
        async tabclick () {
            this.$nextTick(async () => {
                if (this.activeName == 'first') {
                    this.$refs.baseSupermarketGoodManage.getOrderMenuType();
                    this.$refs.baseSupermarketGoodManage.onSearch();
                }
                else if (this.activeName == 'second') {
                    this.$refs.orderSupermarketGoodManage.onSearch();
                }
            })
        }
    }
}
</script>
  