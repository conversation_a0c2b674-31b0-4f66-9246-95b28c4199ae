<template>
    <container>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            @select='selectchange' :isSelection='false' :isSelectColumn='true' :tableData='list' :tableCols='tableCols'
            :tableHandles='tableHandles' :showsummary='false' :loading="listLoading" >
            <template slot='extentbtn'>
              <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                    <el-select filterable clearable v-model="filter1.type" placeholder="状态" style="width: 120px">
                      <el-option label="售后主题分析" :value="-1"></el-option>
                      <el-option label="销售主题分析" :value="0"></el-option>
                      <el-option label="天猫结算数据" :value="1"></el-option>
                      <el-option label="代发成本" :value="2"></el-option>
                      <el-option label="首单拉新-千牛后台id" :value="3"></el-option>
                      <el-option label="首单拉新-千牛后台明细" :value="4"></el-option>
                      <el-option label="菜鸟驿站" :value="5"></el-option>
                      <el-option label="销售明细-拼多多" :value="6"></el-option>
                      <el-option label="结算-拼多多" :value="7"></el-option>
                      <el-option label="销售明细-抖音" :value="8"></el-option>
                      <el-option label="结算-抖音" :value="9"></el-option>
                      <el-option label="销售明细-京东" :value="10"></el-option>
                      <el-option label="结算-京东" :value="11"></el-option>
                      <el-option label="杂项费用-京东" :value="12"></el-option>
                      <el-option label="钱包-京东" :value="13"></el-option>
                      <el-option label="结算-快手" :value="14"></el-option>
                      <el-option label="销售明细-快手" :value="15"></el-option>
                      <el-option label="资金明细-快手" :value="16"></el-option>
                      <el-option label="其他费用-快手" :value="17"></el-option>
                      <el-option label="快递费-分销" :value="18"></el-option>
                      <el-option label="保费扣除-抖音" :value="19"></el-option>
                      <el-option label="商务达人数据-抖音" :value="20"></el-option>
                      <el-option label="达人销售主题分析-抖音" :value="21"></el-option>
                      <el-option label="微信结算数据-淘系" :value="22"></el-option>
                      <el-option label="微信结算数据-淘工厂" :value="23"></el-option>
                      <el-option label="京东虚拟发货-快递" :value="24"></el-option>
                      <el-option label="京东虚拟发货-订单" :value="25"></el-option>
                      <el-option label="视频号结算数据" :value="26"></el-option>
                      <el-option label="视频号违规赔付" :value="27"></el-option>
                      <el-option label="视频号商责欠款" :value="28"></el-option>
                      <el-option label="小红书店铺余额" :value="29"></el-option>
                      <el-option label="小红书支付宝微信结算数据" :value="30"></el-option>
                      <el-option label="小红书账户明细" :value="31"></el-option>
                      <el-option label="京东非销售单结算数据" :value="32"></el-option>
                    </el-select>
                  </el-button>
                  <el-button style="padding: 0;margin: 0;">
                    <el-select filterable clearable v-model="filter1.isSyncShuCang" placeholder="数仓同步状态" style="width: 120px">
                      <el-option label="失败" :value="0"></el-option>
                      <el-option label="成功" :value="1"></el-option>
                    </el-select>
                  </el-button>
                    <el-button style="padding: 0;margin: 0;">
                    <el-select filterable clearable v-model="filter1.isChayi" placeholder="同步数量是否有差异" style="width: 160px">
                      <el-option label="有" :value="1"></el-option>
                      <el-option label="无" :value="2"></el-option>
                    </el-select>
                  </el-button>
                  <el-button style="padding: 0;margin: 0;">
                    <el-input placeholder="店铺Code" v-model="filter1.shopCode" style="width: 110px"></el-input>
                  </el-button>
              </el-button-group>
              </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>


        <el-dialog title="先采后付同步" :visible.sync="syncVisible" width="40%" v-dialogDrag append-to-body
            :close-on-click-modal="false">
            将{{ syncShopName }}先采后付
            <el-date-picker style="width: 110px" v-model="syncOldYearMonth" type="month" format="yyyyMM"
                value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
            月的数据同步至
            <el-date-picker style="width: 110px" v-model="syncNewYearMonth" type="month" format="yyyyMM"
                value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
            月
            <span slot="footer" class="dialog-footer">
                <el-button @click="syncVisible = false">取 消</el-button>
                <el-button type="primary" :loading="syncLoading" @click="onXchfSync">确认</el-button>
            </span>
        </el-dialog>


        <el-dialog title="先采后付撤销" :visible.sync="backVisible" width="40%" v-dialogDrag append-to-body
            :close-on-click-modal="false">
            将{{ backShopName }}先采后付
            <el-date-picker style="width: 110px" v-model="backOldYearMonth" type="month" format="yyyyMM"
                value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
            月已经同步至
            <el-date-picker style="width: 110px" v-model="backNewYearMonth" type="month" format="yyyyMM"
                value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
            月的数据<span style="color:red;">撤销</span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="backVisible = false">取 消</el-button>
                <el-button type="primary" :loading="backLoading" @click="onXchfBack">确认</el-button>
            </span>
        </el-dialog>



    </container>
</template>
<script>
import { getDataUploadList, exportDataUploadList, 
    XianCaiHouFuAlibabaSyncBefore,XianCaiHouFuAlibabaSyncBefore_Platform ,
    SyncXhsItemIds
} from '@/api/monthbookkeeper/financialDetail'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
const tableCols = [
    { istrue: true, prop: 'yearMonth', label: '月份', width: '80' },
    { istrue: true, prop: 'createdTime', label: '上传时间', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'shopCode', label: '店铺编码', sortable: 'custom', width: '110' },
    { istrue: true, prop: 'shopCode', label: '店铺名称', sortable: 'custom', width: '220', formatter: (row) => row.shopName },
    { istrue: true, prop: 'type', label: '数据类型', sortable: 'custom', width: '120', formatter: (row) => row.typeName },
    { istrue: true, prop: 'isSyncShuCang', label: '同步到数仓', width: '200', type: "progress" },
    { istrue: true, prop: 'sourceCount', label: '上传数据量', width: '100'},
    { istrue: true, prop: 'shuCangCount', label: '同步到数仓数据量', width: '150'},
    { istrue: true, prop: 'xchfStatus', label: '先采后付备注', width: '300'},
    
    {
        istrue: true, type: 'button', label: '操作', width: '200', btnList: [
            {
                label: "先采后付同步",
                ishide: (that, row) => {
                    return (!row.shopName.includes('阿里巴巴-') || row.type != 1);

                },
                handle: (that, row) => that.onXchfSyncShow(row)
            },
            {
                label: "先采后付撤销",
                ishide: (that, row) => {
                    return (!row.shopName.includes('阿里巴巴-') || row.type != 1);

                },
                handle: (that, row) => that.onXchfBackShow(row)
            }
        ]
    },
];
const tableHandles = [
    { label: "导出", handle: (that) => that.onExport() },
    { label: "阿里巴巴全平台先采后付同步", handle: (that) => that.onXchfSyncShow2() },
    { label: "阿里巴巴全平台先采后付撤销", handle: (that) => that.onXchfBackShow2() },
    { label: "小红书ItemId同步", handle: (that) => that.onXhsItemId() },
]
export default {
    name: 'Roles',
    components: { cesTable, container },
    props: {
        filter: {}
    },
    data() {
        return {
            shareFeeType: 11,
            that: this,
            list: [],
            filter1: {
                type:null,
                isSyncShuCang:null
            },
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "id", IsAsc: false },
            summaryarry: {},
            total: 0,
            sels: [],
            selids: [],
            listLoading: false,
            pageLoading: false,

            syncVisible: false,
            syncLoading: false,
            syncShopCode: '',
            syncShopName: '',
            syncOldYearMonth: null,
            syncNewYearMonth: null,
            syncPlatform: false,


            backVisible: false,
            backLoading: false,
            backShopCode: '',
            backShopName: '',
            backOldYearMonth: null,
            backNewYearMonth: null,
            
        }
    },
    mounted() {

    },
    beforeUpdate() { },
    methods: {
        //导出
        async onExport() {
            var pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter , ...this.filter1}
            var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
            var res = await exportDataUploadList(params);
            loadingInstance.close();
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '月报计算（已上传）_' + new Date().toLocaleString() + '.xlsx')
            aLink.click();
        },
        onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        async getlist() {
            var pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter,...this.filter1 }
            this.listLoading = true
            const res = await getDataUploadList(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res.data?.total ?? 0
            const data = res.data?.list
            data?.forEach(d => {
                d._loading = false
            })
            this.list = data
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selsChange: function (sels) {
            this.sels = sels
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        onXchfSyncShow(row) {
            this.syncShopCode = row.shopCode;
            this.syncShopName = row.shopName;
            this.syncPlatform=false;
            this.syncVisible = true;
        },
        onXchfSyncShow2() {
            this.syncShopName = "阿里巴巴整个平台";
            this.syncPlatform=true;
            this.syncVisible = true;
        },
        onXchfSync() {
            if(this.syncPlatform==true){
                let params = {
                    oldYearMonth: this.syncOldYearMonth,
                    newYearMonth: this.syncNewYearMonth,
                    syncType: 1
                };
                this.$confirm(`确定要执行先采后付同步吗?`, '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    this.syncLoading = true;
                    const res = await XianCaiHouFuAlibabaSyncBefore_Platform(params);
                    this.syncLoading = false;
                    if (res?.success == true) {
                        this.$message({ type: 'success', message: '操作成功，正在后台努力同步中!' });
                        this.syncVisible = false;
                        this.onSearch();
                    }
                }).catch(() => {
                });
            }
            else{
                let params = {
                    shopCode: this.syncShopCode,
                    oldYearMonth: this.syncOldYearMonth,
                    newYearMonth: this.syncNewYearMonth,
                    syncType: 1
                };
                this.$confirm(`确定要执行先采后付同步吗?`, '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    this.syncLoading = true;
                    const res = await XianCaiHouFuAlibabaSyncBefore(params);
                    this.syncLoading = false;
                    if (res?.success == true) {
                        this.$message({ type: 'success', message: '操作成功，正在后台努力同步中!' });
                        this.syncVisible = false;
                        this.onSearch();
                    }
                }).catch(() => {
                });
            }
        },
        onXchfBackShow(row) {
            this.backShopCode = row.shopCode;
            this.backShopName = row.shopName;
            this.syncPlatform=false;
            this.backVisible = true;
        },
        onXchfBackShow2() {
            this.backShopName = "阿里巴巴整个平台";
            this.syncPlatform=true;
            this.backVisible = true;
        },
        onXchfBack() {
            if(this.syncPlatform==true){
                let params = {
                    oldYearMonth: this.backOldYearMonth,
                    newYearMonth: this.backNewYearMonth,
                    syncType: 2
                };
                this.$confirm(`确定要执行先采后付撤销吗?`, '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    this.backLoading = true;
                    const res = await XianCaiHouFuAlibabaSyncBefore_Platform(params);
                    this.backLoading = false;
                    if (res?.success == true) {
                        this.$message({ type: 'success', message: '操作成功，正在后台努力撤销中!' });
                        this.backVisible = false;
                        this.onSearch();
                    }
                }).catch(() => {
                });
            }
            else{
                let params = {
                    shopCode: this.backShopCode,
                    oldYearMonth: this.backOldYearMonth,
                    newYearMonth: this.backNewYearMonth,
                    syncType: 2
                };
                this.$confirm(`确定要执行先采后付撤销吗?`, '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    this.backLoading = true;
                    const res = await XianCaiHouFuAlibabaSyncBefore(params);
                    this.backLoading = false;
                    if (res?.success == true) {
                        this.$message({ type: 'success', message: '操作成功，正在后台努力撤销中!' });
                        this.backVisible = false;
                        this.onSearch();
                    }
                }).catch(() => {
                });
            }
        },

        onXhsItemId(){
            this.$confirm(`确定要将小红书所有店铺的销售主题分析/售后主题分析/账单明细同步ItemId吗?`, '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await SyncXhsItemIds({"yearMonth":this.filter.yearMonth});
                if (res?.success == true) {
                    this.$message({ type: 'success', message: '操作成功，正在后台努力同步中!' });
                }
            }).catch(() => {
            });
        }
    }
}
</script>
