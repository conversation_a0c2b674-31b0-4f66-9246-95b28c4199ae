<template>
    <div>
        <el-dialog title="一键计算" :visible.sync="showComputeDialog" width="30%" v-dialogDrag @close="handleClose">
            <div class="pickerBox">
                <el-date-picker v-model="pickerValue" type="datetimerange" :picker-options="computePickerOptions" format="yyyy-MM-dd"
                    range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" @change="datePicker">
                </el-date-picker>
            </div>

            <div class="btnBox">
                <el-button type="primary" style="width: 100px;height: 30px;" @click="handleClick">确定</el-button>
                <el-button style="width: 100px;height: 30px;" @click="handleClose">取消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import dayjs from 'dayjs'
export default {
    //接收父组件传来的值
    props: {
        showComputeDialog: {
            type: Boolean,
            default: false
        },
        type: {
            type: Number,
            default: 1
        }
    },
    name: "dailyPicker",
    data () {
        return {
            pickerValue: null,
            computePickerOptions: {
                shortcuts: [{
                    text: '一天',
                    onClick (picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近二个月',
                    onClick (picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 60);
                        picker.$emit('pick', [start, end]);
                    }
                }],
                //只能选择60天以内的日期
                disabledDate (time) {
                    return time.getTime() > Date.now() || time.getTime() < Date.now() - 3600 * 1000 * 24 * 60;
                }
            },
        }
    },
    mounted () {
        //清空时间
        this.pickerValue = null
    },
    methods: {
        clearTime () {
            this.pickerValue = null
        },
        handleClick () {
            if (this.type == 1) {
                //如果没有时间，提示用户选择时间
                if (!this.pickerValue) {
                    this.$message({
                        message: '请选择时间',
                        type: 'warning'
                    })
                    return
                }
                this.$emit('handlePickSearch',this.pickerValue)
            }
        },
        handleClose () {
                this.$emit('closePicker')
        },
        datePicker () {
            //使用dayjs将日期转换成YYYY-MM-DD格式
            let startTime = dayjs(this.pickerValue[0]).format('YYYY-MM-DD')
            let endTime = dayjs(this.pickerValue[1]).format('YYYY-MM-DD')
            //将值传给父组件
            this.$emit('handlePick', {
                startTime,
                endTime
            })
        }
    }
}
</script>

<style scoped lang="scss">
.btnBox {
    display: flex;
    justify-content: space-around;
}

.pickerBox {
    display: flex;
    justify-content: center;
    margin: 20px 0;
}
</style>