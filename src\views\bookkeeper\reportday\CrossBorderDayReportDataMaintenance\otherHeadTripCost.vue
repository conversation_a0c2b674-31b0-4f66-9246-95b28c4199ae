<template>
    <MyContainer>
        <template #header>
            <div class="top">

                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss" />
                <el-select style="width: 180px;" v-model="ListInfo.platforms" placeholder="平台" class="publicCss"
                    clearable multiple filterable collapse-tags>
                    <el-option v-for="item in platformlistKj" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>

                <el-button style="padding: 0;margin-right: 10px;border: none;float: left;">
                    <inputYunhan :key="'1'" :keys="'one'" :width="'220px'" ref="" :inputt.sync="ListInfo.goodsCodes"
                        v-model.trim="ListInfo.goodsCodes" placeholder="商品编码/若输入多条请按回车" :clearable="true"
                        @callback="callbackGoodsCode" title="商品编码" @entersearch="entersearch" :maxRows="100">
                    </inputYunhan>
                </el-button>

                <el-button style="padding: 0;margin-right: 10px;border: none;float: left;">
                    <inputYunhan :key="'2'" :keys="'two'" :width="'220px'" ref="" :inputt.sync="ListInfo.orderNos"
                        v-model.trim="ListInfo.orderNos" placeholder="订单号/若输入多条请按回车" :clearable="true"
                        @callback="callbackOrderNo" title="订单号" @entersearch="entersearch" :maxRows="100">
                    </inputYunhan>
                </el-button>

                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button
                    @click="importProps" type="primary" icon="el-icon-share" @command="handleCommand"> 导入
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item class="Batcoperation" style="padding: 0 25px"
                            command="a">下载模版</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
                <el-button type="primary" @click="onExport" style="margin-left: 5px;">导出</el-button>
                <!-- <el-button type="primary" @click="showCalDayRepoty" style="margin-left: 5px;">费用计算</el-button> -->
            </div>

        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :showsummary='true'
            :summaryarry='summaryarry' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
            :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>
        <el-dialog title="计算日报" :visible.sync="dialogCalDayRepotVis" width="40%" v-dialogDrag>
            <span>
                <el-row>
                    <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
                        <el-date-picker style="width: 50%; float: left;" v-model="calDayRepotyDate" type="date"
                            format="yyyyMMdd" value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
                        <el-button type="success" style="margin-left:  30px;" @click="calDayRepoty">计算日报</el-button>
                    </el-col>
                </el-row>
            </span>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { platformlistKj, formatPlatformkj } from "@/utils/tools";
import { formatTime } from "@/utils";
import inputYunhan from "@/components/Comm/inputYunhan";
import dateRange from "@/components/date-range/index.vue";
import { ImportIncidentalExpensesAsync } from '@/api/bookkeeper/reportdayV2'
import { firstLegOther_Export, getFirstLegOtherPageList, importFirstLegOther, calOtherTouChen_KJAsync } from '@/api/bookkeeper/crossBorderV2'
const tableCols = [
    {
        sortable: 'custom', width: 'auto', align: 'center', prop: 'calcDateTime', label: '计算日期', formatter: (row) => {
            return row.calcDateTime ? formatTime(row.calcDateTime, "YYYY-MM-DD") : "";
        },
    },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'platform', label: '平台', formatter: (row) => { return formatPlatformkj(row.platform) } },
    {
        sortable: 'custom', width: 'auto', align: 'center', prop: 'yearMonthDayDate', label: '日报日期', formatter: (row) => {
            return row.yearMonthDayDate ? formatTime(row.yearMonthDayDate, "YYYY-MM-DD") : "";
        },
    },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'quantity', label: '数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'freightFee', label: '运费', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'unloadingFee', label: '卸货上架费', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'loadingFee', label: '装车费', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'packCost', label: '包装成本', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '订单号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'purchaseFreight', label: '采购运费', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'productFreight', label: '产品运费', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'tailTransportFees', label: '尾程运输费', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'tailOperatingFees', label: '尾程操作费', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'tailOtherFees', label: '尾程其他', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'tailTotalFees', label: '尾程费用合计', },

]

export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, inputYunhan, dateRange
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: null,//开始时间
                endDate: null,//结束时间
                platforms: null,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            fileList: [],
            importLoading: false,
            importVisible: false,
            file: null,
            summaryarry: {},
            platformlistKj,
            dialogCalDayRepotVis: false,
            calDayRepotyDate: null,

        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            //没有时间就提示
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            this.importLoading = true
            await importFirstLegOther(form).then(({ success }) => {
                if (success) {
                    this.$message.success('导入成功')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                this.ListInfo.platform = this.ListInfo.platforms.join(",")
                const { data, success } = await getFirstLegOtherPageList(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.summaryarry = data.summary
                    this.loading = false
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        async handleCommand(command) {
            switch (command) {
                //下载模版
                case 'a':
                    await this.downLoadFile()
                    break;
            }
        },
        async downLoadFile() {
            window.open("/static/excel/CrossBorderDailyDataMaintenance/其他平台的头程费用导入模版.xlsx", "_blank");
        },

        async onExport() {//导出列表数据；
            var res = await firstLegOther_Export(this.ListInfo);
            if (res?.success) {
                this.$message({ message: res.msg, type: "success" });
            }
        },
        //多条查询部分
        async entersearch(val) {
            this.getList('search');
        },
        async callbackGoodsCode(val) {
            this.ListInfo.goodsCodes = val;
        },
        async callbackOrderNo(val) {
            this.ListInfo.orderNos = val;
        },

        async showCalDayRepoty() {
            console.log("看看平台", this.ListInfo.platforms[0])

            if (this.ListInfo.platforms.length !== 1) {
                if (this.ListInfo.platforms.length === 0) {
                    this.$message({ type: 'warning', message: '请选择一个平台!' });
                } else {
                    this.$message({ type: 'warning', message: '只能选择一个平台!' });
                }
                return;
            }
            if (this.ListInfo.platforms[0] != 22) {
                this.$message({ type: 'warning', message: '暂未开通该平台!' });
                return
            }
            this.dialogCalDayRepotVis = true;
        },
        async calDayRepoty() {
            console.log("平台", this.ListInfo.platforms)
            if (this.calDayRepotyDate == null) {
                this.$message({ type: 'warning', message: '请先选择日期!' });
                return
            } 

            let res = await calOtherTouChen_KJAsync({ type: 'TIKTOKHALFSTOCK', yearMonthDay: this.calDayRepotyDate });

            if (res.success) {
                this.$message({ type: 'success', message: '正在计算中,请稍候...' });
                this.dialogCalDayRepotVis = false;
            } else {
                return;
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>
