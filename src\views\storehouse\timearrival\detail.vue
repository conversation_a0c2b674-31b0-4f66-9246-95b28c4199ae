<template>
  <my-container v-loading="pageLoading">
    <template #header>
    </template>
    <vxetablebase :id="'goodsCostPriceChgList202301031318005'" :tableData='list' :tableCols='tableCols' @cellClick='cellclick'
      :loading='listLoading' :border='true' :that="that" ref="vxetable" @sortchange='sortchange' :showsummary='true'
      :summaryarry='summaryarry'>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

  </my-container>
</template>

<script>
import { pagePurchasePlan, getLastUpdateTimeyPurchasePlan, importPurchasePlan, exportPurchasePlan, editPurchasePlan, getPurchasePlan, getPurchaseArrivalInfoDetailAsync } from '@/api/inventory/purchase'
import { getAllProBrand } from '@/api/inventory/warehouse'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { upLoadImage } from '@/api/upload/file'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import probianmaanalysis from '@/views/inventory/probianmaanalysis'
import goodscoderecord from '@/views/inventory/goodscoderecord'
import logistics from '@/components/Comm/logistics'
import { formatYesornoBool, formatPurchasePlanError, formatmoney, formatTime, formatNoLink, formatSecondToHour } from "@/utils/tools";
import { ruleExpressComanycode } from '@/utils/formruletools'
import { throttle } from 'throttle-debounce';
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";
const tableCols = [
  { istrue: true, sortable:'custom', prop: 'buyNo', label: '采购单号', width: '120', type: 'html', formatter: (row) => formatNoLink(row.buyNo) },
  { istrue: true, sortable:'custom',prop: 'indexNo', label: 'ERP单号', width: '120', },
  { istrue: true, sortable:'custom',prop: 'planArrivalTime', label: '预计到货日期', width: '200', formatter: (row) => row.planArrivalTime == null ? null : formatTime(row.planArrivalTime, 'YYYY-MM-DD') },
  { istrue: true, sortable:'custom',prop: 'warehouse', label: '第三方物流和分仓', width: '200', formatter: (row) => row.warehouseName },
  { istrue: true, sortable:'custom',prop: 'boxCount', label: '到货数量', width: '200' },
  { istrue: true, sortable:'custom',prop: 'notBcBoxCount', label: '剔除包材到货数量', width: '200' },
  { istrue: true, sortable:'custom',prop: 'packNum', label: '到货件数', width: '200' },
  { istrue: true, sortable:'custom',prop: 'notBcPackNum', label: '剔除包材到货件数', width: 'auto' }
];
const tableHandles = [];
export default {
  name: 'Roles',
  props: {
    filter: {}
  },
  components: { cesTable, vxetablebase, MyContainer, MyConfirmButton, probianmaanalysis, goodscoderecord, logistics },
  data() {
    return {
      that: this,
      formatTime: formatTime,
      analysisfilter: {
        startDate: null,
        endDate: null,
        proBianMa: ""
      },
      goodscoderecordfilter: { goodsCode: "", buyNo: "" },
      list: [],
      brandlist: [],
      recodelist: [],
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      sels: [],
      listLoading: false,
      pageLoading: false,
      importVisible: false,
      prevTarget: null, // 编辑 Popover 的 Reference （参照），用于 popover.js 对齐两个元素
      popperFlag: false, // 用于编辑 Popover 的刷新
      visiblepopover: false,
      visiblepopoverdetail: false,
      dialogOrderDetailVisible: false,
      popperFlagdetail: false,
      pager: { OrderBy: "", IsAsc: false },
      summaryarry: {},
      selids: [],
      fileList: [],
      listLoading: false,
      pageLoading: false,
      uploadLoading: false,
      lastUpdateTime: '',
      onExporting: false,
      dialogVisible: false,
      editVisible: false,
      editLoading: false,
      drawervisible: false,
      autoform: {
        fApi: {},
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
        rule: []
      },
    }
  },
  watch: {
    value(n) {
      if (n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
      }
    }
  },
  async mounted() {
    //this.init();
    this.getlist();
    //this.initform();
    //formCreate.component('editor', FcEditor);
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      this.filter.startDate = null;
      this.filter.endDate = null;
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      const params = { ...pager, ...this.pager, ... this.filter }
      this.listLoading = true
      const res = await getPurchaseArrivalInfoDetailAsync(params)
      this.listLoading = false
      if (!res?.success) return
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry = res.data.summary;
    },
    async cellclick( {row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event}) {
      console.log('来了',column.property)
      debugger;
      if (column.property == 'buyNo') {
        if (row.buyNo)
          this.$router.push({ path: '/inventory/purchaseindex', query: { buyNo: row.buyNo } })
      }
    },
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
  }
}
</script>
