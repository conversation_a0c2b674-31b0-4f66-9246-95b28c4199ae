<template>
  <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <template>
        <el-tabs v-model="activeName" style="height: 100%">
            <el-tab-pane label="按原基本售价(供货价)" name="supply" style="height: 100%">
                <GoodsEditPriceForm type="supply" v-if="dataList.length>0" ref="supplyRef" :list="dataList" style="height: 100%" @handle="onHandle" @loading="onLoading" />
            </el-tab-pane>
            <el-tab-pane label="按固定金额" name="fixed" style="height: 100%">
                <GoodsEditPriceForm type="fixed" v-if="dataList.length>0" ref="fixedRef" :list="dataList" style="height: 100%" @handle="onHandle" @loading="onLoading" />
            </el-tab-pane>
            <el-tab-pane label="按成本价" name="cost" style="height: 100%">
                <GoodsEditPriceForm type="cost" v-if="dataList.length>0" ref="costRef" :list="dataList" style="height: 100%" @handle="onHandle" @loading="onLoading" />
            </el-tab-pane>
            <el-tab-pane label="按零售价" name="retail" style="height: 100%">
                <GoodsEditPriceForm type="retail" v-if="dataList.length>0" ref="retailRef" :list="dataList" style="height: 100%" @handle="onHandle" @loading="onLoading" />
            </el-tab-pane>
        </el-tabs>
      </template>
  </my-container>
</template>
<script>  
import MyContainer from "@/components/my-container";
import GoodsEditPriceForm from "./goodseditpriceform.vue";

export default {
    name: "GoodsEditPriceIndex",
    components: { MyContainer, GoodsEditPriceForm},
    data() {
        return {              
            that: this,
            activeName: "supply",
            pageLoading: false,
            dataList:[]
        };
    },
    methods: {
        loadData({list}){
            this.dataList = list;
        },
        onHandle(flag){
            if(flag){
                this.$emit('afterSave');
                this.$emit('close');
            }
        },
        onLoading(flag){
            this.pageLoading = flag
        }
    },
};
</script>
<style lang="scss" scoped>
</style>