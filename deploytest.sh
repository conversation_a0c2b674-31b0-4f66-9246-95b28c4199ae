#!/bin/sh
#cd \xxxx\xxxx\MeiYiJia.TakeOut 需要在MeiYiJia.TakeOut目录下执行
#:set ff=unix
systemctl daemon-reload
systemctl restart docker.service

docker rmi -f $(docker images | grep yunhan | awk  '{print $3}')
docker rmi -f $(docker images | grep none | awk  '{print $3}')

echo $$ #输出当前进程PID
docker --version
deploy_ver="1.1.0"  #打包版本
deploy_pros=()
deploy_pros[0]="YunHan.Admin"
function deploy(){
   typeset -l VARIABLE
   VARIABLE=$1

   echo "$(date "+%Y-%m-%d %H:%M:%S") docker starting build ${VARIABLE} "
   sudo docker build -f ./Dockerfile -t $VARIABLE .
   echo "$(date "+%Y-%m-%d %H:%M:%S") docker build ${VARIABLE} success "
   
   echo "$(date "+%Y-%m-%d %H:%M:%S") docker tag ${VARIABLE} version:${deploy_ver}"
   sudo docker tag $VARIABLE registry.cn-hangzhou.aliyuncs.com/myjstore/myj:$VARIABLE.$deploy_ver
   echo "$(date "+%Y-%m-%d %H:%M:%S") docker tag ${VARIABLE} version:${deploy_ver} success"

   echo "$(date "+%Y-%m-%d %H:%M:%S") docker push ${VARIABLE} to registry.cn-hangzhou.aliyuncs.com/myjstore/myj:${VARIABLE}.${deploy_ver}"
   sudo docker push registry.cn-hangzhou.aliyuncs.com/myjstore/myj:$VARIABLE.$deploy_ver
   echo "$(date "+%Y-%m-%d %H:%M:%S")  docker push ${VARIABLE} to registry.cn-hangzhou.aliyuncs.com/myjstore/myj:${VARIABLE}.${deploy_ver} success"
}

for pro in ${deploy_pros[@]};do
   deploy $pro
done

