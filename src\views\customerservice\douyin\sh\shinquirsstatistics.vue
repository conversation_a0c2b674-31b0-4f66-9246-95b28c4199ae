<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='inquirsstatisticslist' @select='selectchange' :isSelection='false' :tableCols='tableCols'
            :loading="listLoading" :summaryarry="summaryarry">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.groupNameList" filterable multiple :collapse-tags="true"
                            placeholder="分组"   clearable>
                            <el-option v-for="item in filterGroupList" :key="item" :label="item"
                                :value="item">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.sname" placeholder="姓名" style="width:120px;" clearable
                            :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <datepicker v-model="filter.sdate"></datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport" style="margin-left: 10px;">导出</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"
                @get-page="getinquirsstatisticsList" />
        </template>

        <el-dialog title="个人效率按店统计" :visible.sync="dialogVisibleSyj" width="80%" :close-on-click-modal="false"
            v-dialogDrag>
            <span>
                <shinquirsstatisticsbyshop v-if="dialogVisibleSyj" ref="shinquirsstatisticsbyshop"
                    style="height: 600px;" />
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
            :close-on-click-modal="false" v-dialogDrag>
            <div>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="个人服务数据按店统计" :visible.sync="dialogVisibleSyj2" width="50%" :close-on-click-modal="false"
            v-dialogDrag>
            <span>
                <shinquirskfstatisticsbyshop v-if="dialogVisibleSyj2" ref="shinquirskfstatisticsbyshop"
                    style="height: 580px;" />
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj2 = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import shinquirsstatisticsbyshop from '@/views/customerservice/douyin/sh/shinquirsstatisticsbyshop'
import shinquirskfstatisticsbyshop from '@/views/customerservice/douyin/sh/shinquirskfstatisticsbyshop'
import {
    getDouYinGroup,
    getDouYinPersonalEfficiencyPageList, getDouYinPersonalEfficiencyChat, exportDouYinPersonalEfficiencyPageList
} from '@/api/customerservice/douyininquirs'
import datepicker from '@/views/customerservice/datepicker'
import buschar from '@/components/Bus/buschar'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
const tableCols = [
    { istrue: true, prop: 'groupName', label: '组名称', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'sname', label: '姓名', width: '120', type: "click", handle: (that, row, column, cell) => that.canclick(row, column, cell), formatter: (row) => row.sname },
    { istrue: true, prop: 'inquirs', label: '人工已接待会话量', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'receiveds', label: '人工已接待人数', width: '80', sortable: 'custom' },
    {
        istrue: true, prop: 'noSatisfactionRate', label: '不满意率', width: '70', sortable: 'custom', type: "click",
        handle: (that, row, column, cell) => that.canclickkf(row, column, cell), formatter: (row) => (row.noSatisfactionRate == null ? '0.00' : row.noSatisfactionRate) + "%"
    },
    {
        istrue: true, prop: 'noSatisfactions', label: '不满意人数', width: '70', sortable: 'custom',
        formatter: (row) => (row.noSatisfactions == null ? '0' : row.noSatisfactions)
    },
    { istrue: true, prop: 'threeResponseRate', label: '3分钟人工回复率', width: '80', sortable: 'custom', formatter: (row) => (row.threeResponseRate).toFixed(2) + "%" },
    { istrue: true, prop: 'threeResponses', label: '3分钟回复人数', width: '80', sortable: 'custom', formatter: (row) => (row.threeResponses).toFixed(2) },
    { istrue: true, prop: 'responseTime', label: '平均响应时长(秒)', width: '80', sortable: 'custom', formatter: (row) => (row.responseTime).toFixed(2) },
    {
        istrue: true, prop: 'satisfactionRate', label: '满意率', width: '70', sortable: 'custom', type: "click",
        handle: (that, row, column, cell) => that.canclickkf(row, column, cell), formatter: (row) => (row.satisfactionRate).toFixed(2) + "%"
    },
    { istrue: true, prop: 'satisfactions', label: '满意人数', width: '70', sortable: 'custom', formatter: (row) => (row.satisfactions).toFixed(0) },
    { istrue: true, prop: 'salesvol', label: '客服销售额(元)', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsCount', label: '询单人数', width: '70', sortable: 'custom' },
    { istrue: true, prop: 'payers', label: '支付人数', width: '70', sortable: 'custom' },
    { istrue: true, prop: 'ipsRate', label: '询单转化率', width: '80', sortable: 'custom', formatter: (row) => (row.ipsRate).toFixed(2) + "%" },
    { istrue: true, prop: 'outTimes', label: '出勤人次', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '80', sortable: 'custom' },
    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", formatter: (row) => '趋势图', width: '80', type: 'click', handle: (that, row) => that.showchart(row) },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar, shinquirsstatisticsbyshop, shinquirskfstatisticsbyshop },
    props:["partInfo"],
    data() {
        return {
            dialogMapVisible: { visible: false, title: "", data: [] },
            that: this,
            filter: {
                groupType: 1,
                inquirsType: 1,
            },
            matchType: 0,
            shopList: [],
            filterGroupList: [],
            userList: [],
            inquirsstatisticslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: {},
            pager: { OrderBy: "inquirs", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            dialogVisibleSyj2: false,
            fileList: [],
            isleavegroup:this.partInfo,//是否离组
        };
    },
    watch:{
        partInfo(){
            this.isleavegroup = this.partInfo;
            this.getDouYinGroup();
        }
    },
    async mounted() {
        this.isleavegroup = this.partInfo;
        await this.getDouYinGroup();
        window.showlist44 = this.showlist44;
    },
    methods: {
        async getDouYinGroup() {
            let groups = await getDouYinGroup({ groupType: 1,isleavegroup:this.isleavegroup });
            if (groups?.success && groups?.data && groups?.data.length > 0) {
                this.filterGroupList=groups.data;
            }
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getinquirsstatisticsList();
        },
        getParam() {
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            console.log(this.filter.sdate)
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            return params;
        },
        async getinquirsstatisticsList() {
            let params = this.getParam();
            this.listLoading = true;
            const res = await getDouYinPersonalEfficiencyPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.inquirsstatisticslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        showlist44(groupName, startdate, enddate) {
            this.filter.groupName = groupName;
            this.filter.groupNameList = [groupName];
            this.filter.sdate = [startdate, enddate];
            if (startdate == null || enddate == null)
                this.filter.sdate = ["", ""];

            this.filter.startDate = startdate;
            this.filter.endDate = enddate;
            this.onSearch()
        },
        async showchart(row) {
            let params = this.getParam();
            params.groupName = row.groupName;
            params.sname = row.sname;
            params.matchType = 1;
            const res = await getDouYinPersonalEfficiencyChat(params).then(res => {
                if (res) {
                    this.dialogMapVisible.visible = true;
                    this.dialogMapVisible.data = res;
                    this.dialogMapVisible.title = res.title;
                    res.title = "";
                }
            })
            this.dialogMapVisible.visible = true;
            this.matchType = 0;
        },
        async canclick(row, column, cell) {
            var fstartsdate = "";
            var fendsdate = "";
            if (this.filter.sdate) {
                var d = new Date(this.filter.sdate[0])
                fstartsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
                d = new Date(this.filter.sdate[1])
                fendsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
            }
            if (fstartsdate == "NaN-NaN-NaN") {
                fstartsdate = "";
                fendsdate = "";
            }
            var fsname = row.sname;
            this.dialogVisibleSyj = true;
            this.$nextTick(() => {
                this.$refs.shinquirsstatisticsbyshop.dialogOpenAfter({
                    startDate: fstartsdate,
                    endDate: fendsdate,
                    sname: fsname
                });
            });
        },
        async canclickkf(row, column, cell) {
            var fstartsdate = "";
            var fendsdate = "";
            if (this.filter.sdate) {
                var d = new Date(this.filter.sdate[0])
                fstartsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
                d = new Date(this.filter.sdate[1])
                fendsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
            }
            if (fstartsdate == "NaN-NaN-NaN") {
                fstartsdate = "";
                fendsdate = "";
            }
            var fsname = row.sname;
            var fgroupName = row.groupName;
            this.dialogVisibleSyj2 = true;
            this.$nextTick(() => {
                this.$refs.shinquirskfstatisticsbyshop.dialogOpenAfter({
                    startDate: fstartsdate,
                    endDate: fendsdate,
                    sname: fsname,
                    groupName: fgroupName,
                });
            });
        },
        async onExport() {
            let params = this.getParam();
            this.listLoading = true
            const res = await exportDouYinPersonalEfficiencyPageList(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '抖音个人效率统计(售后组)_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 60px !important;
}
</style>
