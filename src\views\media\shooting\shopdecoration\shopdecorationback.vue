<template> 
    <my-container v-loading="pageLoading">
        <!--顶部操作----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <template #header>
            <el-button-group> 
                    <el-button style="padding: 0;">
                        <el-input-number style=" width: 100px;"  :clearable="true" v-model="filter.shopDecorationTaskId"   v-model.trim="filter.shopDecorationTaskId" :min="1" :max="10000000" :controls="false" :precision="0"  placeholder="任务编号" ></el-input-number>
                    </el-button>
                    <el-button style="padding: 0;width: 90px;"> 
                        <el-select v-model="filter.hasOverTime" placeholder="是否完成" style="width:100%;" clearable>
                            <el-option label="是" value="1"></el-option>
                            <el-option label="否" value="0"></el-option>
                        </el-select>
                    </el-button>
                
                    <el-button style="padding: 0;">
                        <el-select    filterable v-model="filter.platform" placeholder="平台" clearable :collapse-tags="true" @change="onchangeplatform" style="width: 80px">
                            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding:0;">
                        <el-select  filterable v-model="filter.shopName" placeholder="店铺" clearable style="width: 130px">
                            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
                        </el-select>
                    </el-button>
 
                    <el-button style="padding: 0;width: 100px;">
                       <el-select  v-model="filter.fpPhotoLqName"   placeholder="分配查询" filterable clearable style="width:100px" >  
                            <el-option v-for="item in fpPhotoLqNameList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>
                    
                 
                    <el-button style="padding: 0;">
                        <el-select  v-model="filter.operationGroup" :clearable="true"  placeholder="运营组"  filterable  style="width:100px">
                            <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;width: 100px;">
                      <el-select  v-model="filter.dockingPeople" :clearable="true"  placeholder="对接人"  filterable  style="width:100px">
                            <el-option v-for="item in dockingPeopleList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>
                   
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExeprotShootingTask"   v-if="checkPermission('api:media:shopdecoration:ExportChangeImgeTaskReport')"  >导出</el-button>
                    <el-button  style="margin-left: 20px" @click="onMoveTaskRestart"  v-if="checkPermission('api:media:shopdecoration:TaskRestartActionAsync')" icon="el-icon-share" >批量重启</el-button>
               
    <!--                 <el-button style=" margin-left: 1; padding: 0 1 0 1 ;width: 100px;" type="primary" v-if="checkPermission('shopdecorationDropDownList')">
                        <el-dropdown @command="handleCommand" >
                            <span class="el-dropdown-link" style="font-size: 14;color: #fff;">
                                更多列表<i class="el-icon-arrow-down el-icon--right"></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="a">显示全部</el-dropdown-item>
                                <el-dropdown-item command="b">显示默认</el-dropdown-item>
                                <el-dropdown-item command="c">查看分配</el-dropdown-item>
                                <el-dropdown-item command="d">查看小组</el-dropdown-item>
                                <el-dropdown-item command="e">寄样时效</el-dropdown-item>
                                <el-dropdown-item command="f">拍摄时效</el-dropdown-item>
                                <el-dropdown-item command="g">隐藏操作</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </el-button> -->
            </el-button-group>
        </template>
        <!--列表----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <ces-table ref="table" :that='that' :isIndex='false' :hasexpand='false'  @sortchange='sortchange'
            :tableData='tasklist' @select='selectchange' :isSelection="true" :tableCols='tableCols'
            :isSelectColumn ='true'  :tablekey="tablekey" :customRowStyle="customRowStyle"
            :loading="listLoading" :summaryarry="summaryarry"     @summaryClick='onsummaryClick'  :selectColumnHeight="'0px'"   :isBorder="false"> 
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>
             
            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList" />
        </template>

        <!--创建任务----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog :title="taskPageTitle"  :visible.sync="addTask" width="60%" :close-on-click-modal="false" :key="opentime"
            element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <microVediotaskeditfrom :key="opentime" ref="microVediotaskeditfrom"   :taskUrgencyList="taskUrgencyList"  :groupList="groupList"
            :activetypelist="activetypelist"
             :platformList="platformList" :islook='islook'  :onCloseAddForm="onCloseAddForm"></microVediotaskeditfrom>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCloseAddForm(0)">取 消</el-button>
                    <my-confirm-button type="submit"  @click="onSubmit" v-show="!islook" />
                </span>
            </template>  
        </el-dialog>
        <!--上传文件----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-drawer title="上传成果文件" :key="opentime" :visible.sync="successfiledrawer" direction="rtl" 
            :wrapperClosable="true"  :close-on-press-escape ="false"
            :before-close="successfiledrawerClose" size="60%"  > 
            <shootinguploadaction  ref="successfileinfo" :rowinfo="selectRowKey" :islook="islook"  style="height: 92%;width:100%"></shootinguploadaction>
            <div class="drawer-footer" >
                <el-button @click="successfiledrawer = false">取 消</el-button> 
            </div>
        </el-drawer>

         <!--查看上传文件并打分----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-drawer title="查看成果文件"  :key="opentime" :visible.sync="successfileshow" direction="ltr" 
         :wrapperClosable="true"  :close-on-press-escape ="false"
         size="50%"   > 
            <microvediotaskuploadsuccessfilesocre  ref="microvediotaskuploadsuccessfilesocre" 
            :clostfun ="successfiledrawerscoreClose"  
            :rowinfo="selectRowKey"  
            :islook="islook"
            :isOverList="true"
             style="height: 92%;width:100%"></microvediotaskuploadsuccessfilesocre>
             <div class="drawer-footer" >
                <el-button   @click="successfileshow=false">关 闭</el-button>  
            </div> 
        </el-drawer>

        <!--查看上传附件---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="查看参考" :key="opentime"  :visible.sync="viewReference" width="60%" :close-on-click-modal="true" 
            element-loading-text="拼命加载中"  v-dialogDrag v-loading="addLoading">
            <microvediotaskuploadfile  ref="microvediotaskuploadfile" :rowinfo="selectRowKey"></microvediotaskuploadfile>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="viewReference = false">关 闭</el-button> 
                </span>
            </template>
        </el-dialog>
        <!--查看详情------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="查看备注" :key="opentime"  :visible.sync="viewReferenceRemark" width="60%" :close-on-click-modal="false" 
            element-loading-text="拼命加载中"  v-dialogDrag v-loading="addLoading">
            <microVedioTaskRemark  ref="shootingTaskRemark" :rowinfo="selectRowKey"   :islook="islook"></microVedioTaskRemark>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="viewReferenceRemark = false">关 闭</el-button> 
                    <my-confirm-button type="submit" :loading="shootingTaskRemarkrawer"  @click="sumbitshootingTaskRemark" v-show="!islook"/>
                </span>
            </template>
        </el-dialog>
        <!--加急审核------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="加急审核"  :visible.sync="taskUrgencyAproved" width="20%" :close-on-click-modal="false" :key="opentime"
                    element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <div style="vertical-align: middle; margin-top: 20px;margin-left: 80px;"> 
                    <el-radio v-model="taskUrgencyStatus" label="1" border>同意</el-radio>
                    <el-radio v-model="taskUrgencyStatus" label="9" border>驳回</el-radio> 
            </div> 
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="(taskUrgencyAproved = false)">取 消</el-button>
                    <my-confirm-button type="submit"  @click="taskUrgencyApp"/>
                </span>
            </template>  
        </el-dialog>
   
        <!--分配人趋势图------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-drawer title="分配任务统计" :visible.sync="shootingchartforfpvisible"  direction="btt" :append-to-body="true" size='75%'  :key="opentime" >
            <shootingchartforfp ref="shootingchartforfp" :charttype="fpcharttype"></shootingchartforfp>
        </el-drawer>
    </my-container>
</template>
<script> 
import cesTable from "@/components/Table/tableforvedio.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { pageShootingViewTaskAsync
        ,pickShootingTaskAsync,unPickShootingTaskAsync,deleteShootingTaskActionAsync,endShootingTaskActionAsync
        ,delShootingTploadFileTaskAsync,confrimShootingTaskAsync,unConfrimShootingTaskAsync,endRestartActionAsync,deleteTaskActionAsync
        ,taskOverActionsAsync,exportShootingTaskReport,shootUrgencyCilckAsync ,taskRestartActionAsync,taskShopActionAsync   
} from '@/api/media/shopdecoration';
import uploadfile from '@/views/media/shooting/uploadfile' 
import shootinguploadaction from '@/views/media/shooting/shopdecoration/shopdecorationuploadactiondetail' 
import microVedioTaskRemark from '@/views/media/shooting/shopdecoration/shopdecorationTaskRemark' 
import microvediotaskuploadfile from '@/views/media/shooting/shopdecoration/shopdecorationtaskuploadfile' 
import microvediotaskuploadsuccessfilesocre from '@/views/media/shooting/shopdecoration/shopdecorationtaskuploadsuccessfilesocre'
import microVediotaskeditfrom from '@/views/media/shooting/shopdecoration/shopdecorationtaskeditfrom' 
 
import { getList as getshopList ,getDirectorGroupList} from '@/api/operatemanage/base/shop' 
import { rulePlatform } from "@/utils/formruletools"; 
import {  formatWarehouse } from "@/utils/tools"; 


const tableCols = [
    { istrue: true, prop: 'shopDecorationTaskId', label: '编号', width: '35'     }, 
    { istrue: true, prop: 'activityTypeStr', align:'left', width: '100', label: '活动类型'},
    { istrue: true, prop: 'platformStr', align:'left', width: '100', label: '平台'},
    { istrue: true, prop: 'shopNameStr', align:'left', width: '200', label: '店铺'},
    { istrue: true, prop: 'taskUrgencyName', label: '紧急程度', width: '80'  ,type: 'UrgencyButton'
        , handle: (that, row) => that.shootUrgencyCilck(row.taskUrgencyName ,row.shopDecorationTaskId)    
    }, 
    { istrue: true, prop: 'bz', label: '备注', width: '50' , align:'center' ,type: "clickflag" , handle: (that, row) => that.openTaskRmarkInfo(row) }, 
 
    //详情页 
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'}, 
    { istrue: true, prop: 'detailLqNameStr', label: '设计排版', width: "54", align:'center'},
 
    { istrue: true,  prop: 'detailOverTimeStr', width: '75',  label: '完成日期' , align:'center'},
    { istrue: true,  prop: 'detailConfirmNameStr', label: '确认人',  width: "60" , align:'center' }, 
    { istrue: true,   prop: 'detailConfirmTimeStr',  label: '确认日期', width: '75', align:'center'},
  
    //分配
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'},  
    { istrue: true, prop: 'fpDetailLqNameStr', width: '54', align:'center', label: '分配设计'},  
    // 
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'},  
    { istrue: true, prop: 'operationGroupstr', align:'left', width: '90', label: '运营小组'},
    { istrue: true, prop: 'dockingPeople', align:'left',  width: '90', label: '对接人'},

    { istrue: true, prop: 'taskOverTimeStr',    width: '75',  label: '完成时间'  }, 
    { istrue: true, prop: 'createdTime',  width: '85',  label: '创建日期', sortable: 'custom' ,formatter: (row) => row.createdTimeStr },    
    
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'}, 
    {
        istrue: true, type: "button", label: '操作', fixed: 'right', width: "240",
        btnList: [
            { label: "详情", handle: (that, row) => that.detailTask(row) },
            { label: "成果", permission: "shopdecorationUploadOutComFile",handle: (that, row) => that.onUploadSuccessFile(row) }, 
            { label: "重启", permission: "api:media:shopdecoration:EndRestartActionAsync", handle: (that, row) => that.onTaskRestartAction(row) },
            
            {type:"danger",  permission: "api:media:shopdecoration:DeleteTaskActionAsync",label: "彻底删除", handle: (that, row) => that.deleteTaskAction(row) }
        ]
    }
];
 
export default {
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow
        ,cesTable,uploadfile,shootinguploadaction,microVedioTaskRemark
        ,microvediotaskuploadfile,microvediotaskuploadsuccessfilesocre,microVediotaskeditfrom, 
    },
    inject:['getactivetypeListRelod','getShootingViewPersonRelod' ],
    props:["tablekey",'role'],
    watch: {  
    },
    data() {
        return {
            selshopDecorationTaskId:0,
            taskUrgencyStatus:"1",
            taskUrgencyAproved:false,
            dialogOrderDtlLoading:false,
            Oncommand:'a',
            shootingTaskRemarkrawer:false,
            viewReferenceRemark:false,
            shootingvediotask:'shootingvediotask',
            opentime:null,
            outconfilekey:null,
            fpcharttype:null,
            //分配趋势图
            shootingchartforfpvisible:false,
            //上传成果文件
            successfiledrawer:false,
            successfileshow:false,
            dialogOrderDtlColsed:false,
            //查看参考
            viewReference:false,
            //选中的行
            selectRowKey:null,
            that: this,
            pageLoading :false,
            islook:true,
            filter: { 
                isShop:null,
                isdel:1,
                isComplate:null,
                shopName:null,
                operationGroup:null
            },
            tasklist:[], 
            taskPageTitle: "创建任务",
            referenceVideoList: [],
            multipleSelection: [],
            addLoading :false,
            formatWarehouse: formatWarehouse,
            activetypelist: [],
            shopList: [],
            userList: [],
            groupList: [],

            fpDetailLqNameList: [],
            fpModelLqNameList: [],
            fpPhotoLqNameList: [],
            fpVideoLqNameList: [],
            dockingPeopleList: [],
            
            taskUrgencyList :[{value:1,label:"加急"},{value:0,label:"紧急"},{value:2,label:"确认"},{value:9,label:"正常"}],
            platformList :[],
            tableCols: tableCols,
            total: 0,
            //选中的行id
            selids : [],
            taskPhotofileList:[],
            taskExeclfileList:[],
            addTask: false,
            loginfo:null,
            summaryarry: {},
            pager: {  },
            sels: [], // 列表选中列
            listLoading: false,
            dialogAddOrderLoading:false,
            dialogAddOrderSubmitLoding:false,
            dialogAddOrderVisible:false, 
            dialogOrderDtlVisible:false, 
            drawervisible: false,  
            sendOrderNoInner: "",
            dialogHisVisible: false, 
 
        };
    },
    watch: { },
    async created() {   },
    async mounted() {
        await this.onSearch();
        await this.onGetdrowList();
        await this.onGetdrowList2();
        /* this.Oncommand = this.role; 
        this.pageLoading =true;
        await this.ShowHideonSearch();
        this.pageLoading =false; */
        await this.getShootingViewPer();
        
    },
    methods: { 
        async onsummaryClick(property) { 
            this.fpcharttype = property;
            this.$nextTick(function () { 
                this.$refs.shootingchartforfp.showviewMain();
            });
           
            this.shootingchartforfpvisible = true;
        },
        async onExeprotShootingTask(){
            var pager = this.$refs.pager.getPager();
            this.filter.isShop =null;
            this.filter.isdel = 1; 
            this.filter.isComplate = null;  
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            this.pageLoading = true;
            var res = await exportShootingTaskReport(params);
            if (res?.data?.type == 'application/json') {return;}
            this.pageLoading = false;
            const aLink = document.createElement("a");
            var blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '包装设计导出.xlsx')
            aLink.click()

        },
        customRowStyle(row,index){
            if(row.row?.isend && row.row.isend==1){ 
                let styleJson = {};
                styleJson.color = "rgb(216 216 216)"; 
                return styleJson
            }else{
                return null
            }
            
        }, 
        async onMoveTaskRestart()
        {
            if(this.selids.length==0){ this.$message({ type: 'warning', message: "请选择一个任务" });  return; } 
            await this.onTaskRestartActionShared(this.selids);  
        },
        async onMoveTaskShop()
        { 
            if(this.selids.length==0){
                this.$message({ type: 'warning', message: "请选择一个任务" });
                return;
            } 
            await this.onTaskShopActionShared(this.selids)   
        },
        //存档操作
        async onTaskShopAction(row)
        {
            await this.onTaskShopActionShared([row.shopDecorationTaskId]); 
        },
        async onTaskShopActionShared(array)
        {
            this.$confirm("选中的任务移动到存档列表，是否确定存档", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            }).then(async () => {
                var res =  await taskShopActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await  this.onRefresh();
                    this.selids = [];
                } 
            });
        },

        //重新启动
        async onTaskRestartAction(row)
        { 
            await this.onTaskRestartActionShared([row.shopDecorationTaskId]); 
        },
        //重新启动
        async onTaskRestartActionShared(array)
        {
            this.$confirm("选中的任务将会移动至任务列表，是否确定执行", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            }).then(async () => {
                var res =  await taskRestartActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await  this.onRefresh();
                    this.selids = [];
                } 
            });
        },
        //回收站彻底删除操作
        async deleteTaskAction(row)
        { 
            await this.deleteTaskActionShared([row.shopDecorationTaskId]); 
        },
        //回收站彻底删除操作
        async deleteTaskActionShared(array)
        { 
            this.$confirm("选中的任务会彻底删除，是否确定执行", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            }).then(async () => {
                var res =  await deleteTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await  this.onRefresh();
                    this.selids = [];
                } 
            });
        }, 
        //获取下拉数据
        async onGetdrowList()
        {  
            var pfrule = await rulePlatform();
            this.platformList = pfrule.options;   
            var res = await getDirectorGroupList();
            this.groupList = res.data?.map(item => { return { value: item.key, label: item.value }; });           
        },
        async onGetdrowList2()
        { 
            var res =await this.getactivetypeListRelod();  
            this.activetypelist =res?.map(item => { return { value: item.id, label: item.label }; });
        },
        async onchangeplatform(val) {
            var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100 });
            this.filter.shopName = "";
            this.shopList = res1.data.list;
        },
        //获取分配人下拉，对接人下啦
        async getShootingViewPer() {
            var res = await this.getShootingViewPersonRelod();
            if(res){ 
                this.dockingPeopleList=res.dockingPeopleList;
                this.fpPhotoLqNameList=res.fpallList;
            }
        },
        
       async ShowHideonSearch(){ 
            var checkedColumnsFora= [];  
            switch (this.Oncommand) {
                //显示全部 ,部门经理，超管
                case "a":
                    checkedColumnsFora=
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注', '参考',
                    '确认','完成','确认人','确认日期', '完成日期', 
                    '照片', '视频', '微详情', '详情页','照片建模','视频建模',  '张数','数量', '个数', '天数',
                    '分配照片', '分配视频', '分配详情', '分配建模' ,
                    '运营小组','对接人', '平台', '店铺','完成时间','到货日期','到货天数','发货日期','发货天数','发货日期', '申请日期', '申请天数', 
                    '创建日期','内部单号','快递单号','拿样跟踪','操作'];  
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    await this.$refs["table"].initsummaryEvent(); 
                    break;
                //显示默认
                case "b":
                    checkedColumnsFora=
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注', '参考',
                     '确认人',  '完成日期', 
                    '照片', '视频', '微详情', '详情页','照片建模','视频建模',  '张数','数量', '个数',  
                    '分配照片', '分配视频', '分配详情', '分配建模' ,
                    '运营小组','对接人', '平台', '店铺','完成时间','到货日期','发货日期','发货日期', '申请日期', 
                    '创建日期','内部单号','快递单号','拿样跟踪','操作'];  
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    var i  = checkedColumnsFora.indexOf('分配照片');
                    await this.oncoulumCheck([i +15,i +16,i +17,i +18]); 
                    break;
                //查看分配
                case "c":
                    checkedColumnsFora=
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注', '参考', 
                    '照片', '视频', '微详情', '详情页','照片建模','视频建模',   
                    '分配照片', '分配视频', '分配详情', '分配建模' ,
                    '运营小组','对接人','创建日期'  ,'操作']; 
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    var i  = checkedColumnsFora.indexOf('分配照片');
                    await this.oncoulumCheck([i +8,i +9,i +10,i +11]);   
                    break;
                //查看小组
                case "d":
                    checkedColumnsFora=
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注',    
                    '分配照片', '分配视频', '分配详情', '分配建模' ,
                    '运营小组','对接人', '平台', '店铺', 
                    '创建日期','内部单号','快递单号','拿样跟踪','操作'];  
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    var i  = checkedColumnsFora.indexOf('分配照片');
                    await this.oncoulumCheck([i +8,i +9,i +10,i +11]);   
                    break;
                //寄样实效
                case "e":
                    checkedColumnsFora=
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注' ,
                    '分配照片', '分配视频', '分配详情', '分配建模' ,
                    '到货日期','到货天数','发货日期','发货天数','发货日期','申请日期', '申请天数', 
                    '创建日期','内部单号','快递单号','拿样跟踪','操作']; 
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    var i  = checkedColumnsFora.indexOf('分配照片');
                    await this.oncoulumCheck([i +8,i +9,i +10,i +11]);    
                    break;
                //拍摄实效
                case "f":
                    checkedColumnsFora=  
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注',  
                    '确认人','确认日期', '完成日期', 
                    '照片', '视频', '微详情', '详情页','照片建模','视频建模',  '张数','数量', '个数', '天数', 
                    '到货日期','创建日期', '操作']; 
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    break;
                //隐藏操作
                case "g":
                    checkedColumnsFora=  ['编号', '产品简称', '大货仓', '紧急程度', '备注', '参考',
                    '确认','完成','确认人','确认日期', '完成日期', 
                    '照片', '视频', '微详情', '详情页','照片建模','视频建模',  '张数','数量', '个数', '天数',
                    '分配照片', '分配视频', '分配详情', '分配建模' ,
                    '运营小组','对接人', '平台', '店铺','完成时间','到货日期','到货天数','发货日期','发货天数','发货日期', '申请日期', '申请天数', 
                    '创建日期','内部单号','拿样跟踪','快递单号'];  
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    await this.$refs["table"].initsummaryEvent(); 
                    break;
                default: 
                    break;
            } 
        },
        async oncoulumCheck(array){
       
            await this.$refs["table"].initsummaryEventNew(array,'onsummaryClick'); 
        },
        //更多列操作
        async handleCommand(command) {
            this.Oncommand = command;
            this.pageLoading =true;
            await  this.ShowHideonSearch();
            this.pageLoading =false;
        },
        //查询
        async onSearch(){
            this.$refs.pager.setPage(1);
            this.getTaskList();
            this.selids = [];
        },
        //刷新当前页
        async onRefresh(){
             await  this.getTaskList();
        },
        //获取数据
        async getTaskList() {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            this.listLoading = true;
            const res = await pageShootingViewTaskAsync(params);
            this.listLoading = false;
            this.total = res.data.total
            this.tasklist = res.data.list;
            this.summaryarry =  res.data.summary;
        },
        //列表排序
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        //多选事件
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.shopDecorationTaskId);
            })
        },
        //任务详情
        async detailTask(row) {
            this.addLoading =true;
            this.opentime = this.opentime +1;
            this.taskPageTitle = "任务详情";
            this.addTask = true;
            this.$nextTick(function () { 
                this.$refs.microVediotaskeditfrom.editTask(row); 
            });
            this.addLoading =false;
        },
        //编辑任务
       /*  async editTask(row) {
            this.addLoading =true;
            this.opentime = this.opentime +1;
            this.taskPageTitle = "编辑任务";
            this.addTask = true;
            await this.$nextTick(function () { 
                this.$refs.microVediotaskeditfrom.editTask(row);
            });
            this.addLoading =false;
        }, */
        //新增任务
        async onAddTask() {
            this.addLoading =true;
            this.opentime = this.opentime +1;
            this.addTask = true;
            this.taskPageTitle = "创建任务"; 
            this.islook = false; 
            this.addLoading =false;
        }, 
        //提交保存
        async onSubmit() { 
            this.addLoading =true;
            await  this.$nextTick(function () {
                this.$refs.microVediotaskeditfrom.onSubmit();
            });
            this.addLoading =false;
        },
        //删除上传附件操作
        async deluplogexl(ret){
            this.addLoading =true;
            
            await delShootingTploadFileTaskAsync({upLoadPhotoId:ret.upLoadPhotoId,type:2}).catch(_ => {
                this.addLoading =false;
            });  
          
            this.addLoading =false;
        },
        //删除上传图片操作
        async deluplogimg(ret){ 
            this.addLoading =true; 
            await delShootingTploadFileTaskAsync({upLoadPhotoId:ret.upLoadPhotoId,type:1}).catch(_ => {
                this.addLoading =false;
            }); 
            this.addLoading =false;
        },
        //关闭窗口，初始化数
        onCloseAddForm(type){  
            this.addTask = false;
            if(type == 1){
                this.onSearch();
            }
        },
        //确认Or取消任务
        async ConfirmTaskInfo(btnstr, index,shopDecorationTaskId)
        { 
            var that = this;
            switch(btnstr){ 
                case "确认":
                    this.$confirm("确认完成, 是否继续?", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    })
                    .then(async () => {
                        var res =  await confrimShootingTaskAsync({ taskid: shopDecorationTaskId, index: index });
                            if (res?.success) {
                                that.$message({ message: '操作成功', type: "success" });
                                await  that.onRefresh();
                            } 
                    });
                break;
                case "取消": 
                    this.$confirm("确认取消, 是否继续?", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    })
                    .then(async () => {
                        var res =  await unConfrimShootingTaskAsync({ taskid: shopDecorationTaskId, index: index });
                        if (res?.success) {
                            that.$message({ message: '操作成功', type: "success" });
                            await  that.onRefresh();
                        } 
                    });
                break;
            }

        },
        //紧急程度按钮点击
        async shootUrgencyCilck(btnstr,shopDecorationTaskId){
             
        },
        async taskUrgencyApp() {
            var res =  await shootUrgencyCilckAsync({ taskid: this.selshopDecorationTaskId, index: this.taskUrgencyStatus });
            if (res?.success) {
                this.taskUrgencyAproved = false; 
                this.$message({ message: '操作成功', type: "success" });
                await  this.onRefresh();
            } 
        },
        //完成任务
        async pickTask(btnstr, index,shopDecorationTaskId) {
            var that = this;
            switch(btnstr){ 
                case "完成":
                    this.$confirm("确认完成, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                    }).then(async () => {
                        var res =  await pickShootingTaskAsync({ taskid:  shopDecorationTaskId, index: index });
                        if (res?.success) {
                            that.$message({ message: '完成成功', type: "success" });
                            await  that.onRefresh();
                        } 
                    });
                break;
                case "取消":
                    this.$confirm("确认取消完成, 是否继续?", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    })
                    .then(async () => {
                        var res=   await unPickShootingTaskAsync({ taskid:  shopDecorationTaskId, index: index })
                        if (res?.success) {
                            that.$message({ message: '取消成功', type: "success" });
                            await that.onRefresh();
                        }
                    });
                break;
            }
        },
        async onxdfhmainCellClick(row) {
            this.xdfhmainlist.forEach(
                f => {
                    if (f.shootingTaskOrderId == row.shootingTaskOrderId) {
                        this.xdfhdtllist = f.dtlEntities;
                    }
                }
            );
        },
        //查看详情备注页
        openTaskRmarkInfo(row){
            this.opentime = this.opentime +1;
            this.selectRowKey = row.shopDecorationTaskId;
            this.viewReferenceRemark = true;
        },
        //成果文件上传
        onUploadSuccessFile(row){
            this.opentime = this.opentime +1;
            this.selectRowKey =row.shopDecorationTaskId;
            this.outconfilekey = row.shopDecorationTaskId;
            this.successfiledrawer = true;
        },   
        //关闭成果文件上传
        successfiledrawerClose(){
            this.successfiledrawer = false;
        },
        //查看参考附件
        videotaskuploadfileDetal(row){
            this.opentime = this.opentime +1;
            this.selectRowKey = row.shopDecorationTaskId;
            this.viewReference = true;
        },
        //查看成果文件
        openComputOutInfo(row){
            this.opentime = this.opentime +1;
            this.selectRowKey =row.shopDecorationTaskId;
            this.successfileshow = true;
        },
        successfiledrawerscoreClose(){
            this.successfileshow = false;
        },

        async sumbitshootingTaskRemark(){
            this.shootingTaskRemarkrawer = true;
            await this.$refs.shootingTaskRemark.onsubmit();
            this.shootingTaskRemarkrawer = false;
        },
         //下单发货
         async onAddOrder() {
            if (this.selids.length <= 0) {
                this.$message({ message: '请勾选任务', type: "warning" });
                return;
            } 
            await this.$nextTick(function () {
                this.$refs.downOrderTaskRemark.onAddOrder(this.selids); 
            })  
            this.dialogAddOrderVisible = true;
        },
        //关闭下单发货界面
        async closeAddOrder() {
            this.dialogAddOrderSubmitLoding = false;
        },
        //下单保存
        async onAddOrderSave() {
            this.dialogAddOrderLoading =true;
            await  this.$nextTick(function () {
                this.$refs.downOrderTaskRemark.onAddOrderSave();
            }) 
            this.dialogAddOrderLoading =false;
        },
        async onShowOrderDtl(row) {
            this.dialogOrderDtlVisible = true;
            this.xdfhmainLoading = true;
            this.xdfhdtlLoading = true;
            var ret = await getShootingTaskOrderListById({ shopDecorationTaskId: row.shopDecorationTaskId });
            this.xdfhmainLoading = false;
            this.xdfhdtlLoading = false;
            if (ret?.success && ret.data.length > 0) {
                ret.data.forEach(f => f.shopDecorationTaskId = row.shopDecorationTaskId);
                this.xdfhmainlist = ret.data;
                this.xdfhdtllist = ret.data[0].dtlEntities;
            }
        },
        async onShowExproessHttp(row) {
            this.drawervisible = true;
            let expressNo=row.expressNo;
            if(!expressNo)
            {
                expressNo=row.sampleExpressNo;
            }
            this.$nextTick(function () {
                this.$refs.logistics.showlogistics("",expressNo);
            })
        },
        showLogDetail (row) {
            this.dialogHisVisible = true;
            let sampleRrderNo=row.sampleRrderNo;
            if(!sampleRrderNo)
            {
                sampleRrderNo=row.orderNoInner;
            }
            this.sendOrderNoInner = sampleRrderNo;
        },
    }, 
};
</script>