<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding:0;margin:0;">
                    <el-date-picker v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        style="width:230px;">
                    </el-date-picker>
                </el-button>
                <!-- <el-button style="padding:0;margin:0;">
                    <el-select v-model="filter.status" placeholder="状态" clearable filterable style="width: 100%">
                        <el-option label="待送" value="待送" />
                        <el-option label="已送" value="已送" />
                    </el-select>
                </el-button> -->
                <el-button style="padding:0;margin:0;">
                    <el-input v-model.trim="filter.keyWord" placeholder="关键字搜索" :maxlength="50" clearable>
                        <el-tooltip slot="suffix" class="item" effect="dark" content="支持搜索的内容：订单编号，商品名称，积分，点单人"
                            placement="bottom">
                            <i class="el-input__icon el-icon-question"></i>
                        </el-tooltip>
                    </el-input>

                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="exportOrderMenuManage">导出</el-button>
            </el-button-group>
        </template>
        <!-- <div>
            <span>今日订单：</span><span style="font-size: 28px;">{{orderCount.count}}</span>
            <span style="padding-left: 50px;">已送：</span><span style="font-size: 28px;">{{orderCount.yscount}}</span>
            <span style="padding-left: 50px;">待送：</span><span style="font-size: 28px;">{{orderCount.dscount}}</span>
        </div> -->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :isSelectColumn="false" :showsummary='true' :tablefixed='false' :tableData='tableData' :tableCols='tableCols'
            :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:100%;margin: 0">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import container from '@/components/my-container';
import cesTable from '@/components/Table/table.vue';
import { formatLinkProCode, platformlist } from '@/utils/tools'
import {
    getPageOrderSupermaketGoodManageAsync,
    getOrderSupermaketGoodCountAsync,
    updateOrderSupermaketGoodStatusAsync,
    exportOrderSupermaketGoodManageAsync
} from '@/api/profit/orderfood';

const tableCols = [
    { istrue: true, prop: 'id', label: '订单编号', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'createdUserName', label: '点单人', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'createdGroupName', label: '对应组', sortable: 'custom', width: '120' },
    // { istrue: true, prop: 'station', label: '工位', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'createdTime', label: '点单时间', width: '160', sortable: 'custom', },
    // { istrue: true, prop: 'gysName', label: '供应商名称', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'goodNames', label: '商品名称', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'integral', label: '积分', width: '80', sortable: 'custom' },
    // { istrue: true, prop: 'price', label: '价格', width: '80', sortable: 'custom' },
    // {
    //     istrue: true, prop: 'status', label: '状态', width: '80', sortable: 'custom',
    // },
    // {
    //     istrue: true, type: "button", label: '操作', width: "200",
    //     btnList: [
    //         { label: "修改状态", handle: (that, row) => that.editstatus(row) },
    //     ]
    // }
];

const startDate = formatTime(dayjs(), "YYYY-MM-DD");
const endDate = startDate;
//const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default ({
    name: "Users",
    components: { container, cesTable },
    data () {
        return {
            that: this,
            filter: {
                timerange: [startDate, endDate],
                startTime: startDate,
                endTime: endDate,
                keyWord: null,
                status:''
            },
            orderCount: {},
            platformlist: platformlist,
            tableCols: tableCols,
            tableHandles: null,
            tableData: [],
            total: 0,
            pager: { OrderBy: "id", IsAsc: false },
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            sels: [],
        };
    },
    mounted () {
        this.onSearch();
        //this.getOrderSupermaketGoodCount();
    },
    methods: {
        async onSearch () {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        async getOrderSupermaketGoodCount (row) { 
            const res = await getOrderSupermaketGoodCountAsync();
            this.orderCount = res?.data;
            
        },
        async editstatus (row) { 
            let p = {
                id: row.id,
                status : row.status == "待送"?"已送":"待送",
            }
            const res = await updateOrderSupermaketGoodStatusAsync(p);
            if (res.data) {
                this.$message({ type: 'success', message: '修改成功!' });
                await this.getList();
                await this.getOrderSupermaketGoodCount();
            }
        },
        async exportOrderMenuManage () {
            if (this.filter.timerange && this.filter.timerange.length > 0) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...this.filter };
            const res = await exportOrderSupermaketGoodManageAsync(params);
            if (!res?.data) return;
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '超市订单数据_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
        async sortchange (column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.$refs.pager.setPage(1);
            await this.onSearch();
        },
        async getList () {
            if (this.filter.timerange && this.filter.timerange.length > 0) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            else { 
                this.filter.startTime = null;
                this.filter.endTime = null;
            }
            var that = this;
            this.listLoading = true;
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter, ...this.myfilter };
            const res = await getPageOrderSupermaketGoodManageAsync(params).then(res => {
                that.total = res.data?.total;
                that.tableData = res.data?.list;
            });
            this.listLoading = false;
        },
    }
})
</script>
<style scoped>
::v-deep .el-table__fixed-footer-wrapper tbody td {
    color: blue;
}
</style>

