<template>
    <container>
        <template #header>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            @select='selectchange' :isSelection='false' :isSelectColumn='true' :tableData='list' :tableCols='tableCols'
            :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry' :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input placeholder="商品ID" v-model="filter1.procode" style="width: 150px" clearable></el-input>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <inputYunhan ref="numberinternalorder" :inputt.sync="filter1.numberinternalorder" v-model="filter1.numberinternalorder"
                            class="publicCss" placeholder="内部单号/多条请按回车" :clearable="true" :clearabletext="true"
                            :maxRows="300" :maxlength="6000" @callback="numberinternalorder" title="内部单号">
                        </inputYunhan>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <inputYunhan ref="numberonlineorderorigin" :inputt.sync="filter1.numberonlineorderorigin" v-model="filter1.numberonlineorderorigin"
                            class="publicCss" placeholder="原始单号/多条请按回车" :clearable="true" :clearabletext="true"
                            :maxRows="300" :maxlength="6000" @callback="numberonlineorderorigin" title="原始单号">
                        </inputYunhan>
                    </el-button>
                </el-button-group>
            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sel.length" @get-page="getlist" />
        </template>
    </container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { GetBaozhuangMonth, ExportBaozhuangMonth } from '@/api/monthbookkeeper/financialDetail'
import container from '@/components/my-container/noheader'
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import { formatPlatform, formatLinkProCode } from "@/utils/tools";

const tableCols = [
    { istrue: true, prop: 'dt', label: '年月', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'version', label: '版本', sortable: 'custom', width: '100', formatter:(row)=>!row.version?"": row.version=='v1'?"工资月报":"参考月报" },
    { istrue: true, prop: 'platform', label: '平台', sortable: 'custom', width: '120', formatter: row => formatPlatform(row.platform) },
    { istrue: true, prop: 'procode', label: '商品ID', sortable: 'custom', width: '120', type:'html', formatter: (row) => formatLinkProCode(row.platform, row.procode) },
    { istrue: true, prop: 'numberinternalorder', label: '内部单号', sortable: 'custom', width: '150'},
    { istrue: true, prop: 'numberonlineorderorigin', label: '原始单号', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'shopcode', label: '店铺', sortable: 'custom', width: '120', formatter: row => row.shopName || '' },
    { istrue: true, prop: 'bzsj', label: '实际包装费', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'bzyg', label: '预估包装', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'bztotal', label: '总包装费', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'wages', label: '仓库薪资', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'wages_yun', label: '仓库薪资（运）', sortable: 'custom', width: '140' },
    { istrue: true, prop: 'out_yun', label: '出仓成本（运）', sortable: 'custom', width: '140' },
    { istrue: true, prop: 'outsj', label: '真实出仓成本', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'outyg', label: '预估出仓成本', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'out_total', label: '出仓成本', sortable: 'custom', width: '120' },
];																		

const tableHandles = [
    { label: "导出", handle: (that) => that.onExport() },
]
export default {
    name: 'baozhuangMonth',
    components: { cesTable, container, vxetablebase, MyConfirmButton, inputYunhan },
    props: {
        filter: {}
    },
    data() {
        return {
            that: this,
            filter1: {
                procode: '',
                numberinternalorder: '',
                numberonlineorderorigin: ''
            },
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "dt", IsAsc: false },
            total: 0,
            sel: [],
            listLoading: false,
            summaryarry: null,
        }
    },
    mounted() {

    },
    beforeUpdate() { },
    methods: {
        onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ...this.filter, ...this.filter1 }
            
            this.listLoading = true
            const res = await GetBaozhuangMonth(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res.data?.total ?? 0
            const data = res.data?.list
            this.summaryarry = res.data?.summary;
            data?.forEach(d => {
                d._loading = false
            })
            this.list = data
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async onExport(){
            let pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter, ...this.filter1 }
            const res = await ExportBaozhuangMonth(params);
            if (!res?.data) return;
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            aLink.setAttribute("download", '明细查询界面_' + new Date().toLocaleString() + '.xlsx');
            aLink.click();
        },
        numberinternalorder(val) {
            let that = this;
            that.filter1.numberinternalorder = val;
        },
        numberonlineorderorigin(val) {
            let that = this;
            that.filter1.numberonlineorderorigin = val;
        }
    }
}
</script>
