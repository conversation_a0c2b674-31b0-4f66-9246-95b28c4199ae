<template>
    <my-container v-loading="pageLoading">
        <div style="display: flex; gap: 10px;">
            <!-- 第一个表单 -->
            <div style="flex: 1; margin-right:10px">
                <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                    <el-date-picker style="width: 280px" v-model="Filter.conversationTime" type="datetimerange"
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" :picker-options="pickerOptions"
                        :default-value="defaultDate"></el-date-picker>
                    <el-button type="primary" @click="getRoles" style="margin-left: 10px;">查询</el-button>
                    <el-button type="primary" @click="onAdd">新增运营工资及精力占比</el-button>
                </el-form>
            </div>

            <!-- 第二个表单 -->
            <div style="flex: 1;">
                <el-form class="ad-form-query" :inline="true" :model="Filters" @submit.native.prevent>
                    <el-form-item label="">
                        <el-select v-model="Filters.userIdList" placeholder="运营" class="el-select-content" filterable
                            multiple clearable collapse-tags>
                            <el-option v-for="item in groupNameList" :key="item.key" :label="item.label"
                                :value="item.key" />
                        </el-select>
                    </el-form-item>
                    <el-button type="primary" @click="handelQuery">查询</el-button>
                    <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button
                        @click="startImport" type="primary" icon="el-icon-share" @command="handleCommand"> 导入
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item class="Batcoperation" style="padding: 0 25px"
                                command="a">下载模版</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                    <el-button type="primary" style="margin-left: 5px;" @click="reminder">提醒</el-button>
                </el-form>
            </div>
        </div>
        <!--左边显示部分（显示角色信息）-->
        <el-row style="height:calc(100% - 40px);">
            <el-col :span="2" style="height:100%;">
                <div style="height:100% ;">
                    <!--列表-->
                    <el-table ref="dataTreeList" height="100%" v-loading="listLoading" :data="roles" border
                        highlight-current-row @select-all="handleSelectAll"
                        @selection-change="debounceHandleSelectionChange" @row-click="GetRoleUsersinfo" row-key="id">
                        <el-table-column prop="effectiveTime" label="生效日期" :formatter="formatCreatedTime" />
                    </el-table>
                </div>
            </el-col>

            <el-col :span="22" style="height:100%;">
                <div style="height:100%;">
                    <!--弹出显示该角色的用户-->
                    <vxetablebase ref="balanceVxetable" :id="'operationMaintenance20241230'" :that='that'
                        :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchangeBalance'
                        :tableData='list' :tableCols='balanceTableCols' :isSelection="false" :isSelectColumn="false"
                        :summaryarry='balanceSummaryarry' :showsummary='true' :loading="youlistLoading">
                    </vxetablebase>
                    <!-- <my-pagination ref="balancePage" :total="balanceTotal" @get-page="checkBalance()" /> -->
                </div>
            </el-col>
        </el-row>

        <el-drawer title="新增运营工资" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
            :visible.sync="addFormVisiblerole" direction="btt" size="'auto'" class="el-drawer__wrapper"
            style="position:absolute;" @close="closeAddForm">
            <section style="padding:24px 48px 74px 24px;">

                <el-form ref="addForm" :model="addForm" :rules="addFormRules" label-width="120px" :inline="false">
                    <el-row :gutter="20" type="flex" justify="space-between">
                        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
                            <el-form-item label="运营" prop="userIdList">
                                <el-select v-model="addForm.userIdList" placeholder="运营" filterable multiple clearable
                                    collapse-tags>
                                    <el-option v-for="item in groupNameList" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
                            <el-form-item label="每日工资" prop="wages">
                                <el-input clearable v-model="addForm.wages" maxlength="8"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
                            <el-form-item label="生效日期" prop="effectiveTime">
                                <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                    v-model="addForm.effectiveTime" type="date" style="width: 100%;" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
                            <el-form-item label="TEMU半托比例">
                                <el-input v-model.trim="addForm.rate15" placeholder="请输入" maxlength="50" clearable
                                    class="publicCss smallInput">
                                    <template #append>
                                        <span>%</span>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
                            <el-form-item label="TEMU全托比例">
                                <el-input v-model.trim="addForm.rate13" placeholder="请输入" maxlength="50" clearable
                                    class="publicCss smallInput">
                                    <template #append>
                                        <span>%</span>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20" type="flex" justify="space-between">

                        <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
                            <el-form-item label="SHEIN全托比例">
                                <el-input v-model.trim="addForm.rate12" placeholder="请输入" maxlength="50" clearable
                                    class="publicCss smallInput" style="width: 180px;">
                                    <template #append>
                                        <span>%</span>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
                            <el-form-item label="SHEIN自营比例">
                                <el-input v-model.trim="addForm.rate16" placeholder="请输入" maxlength="50" clearable
                                    class="publicCss smallInput" style="width: 190px;">
                                    <template #append>
                                        <span>%</span>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
                            <el-form-item label="SHEIN半托比例">
                                <el-input v-model.trim="addForm.rate19" placeholder="请输入" maxlength="50" clearable
                                    class="publicCss smallInput" style="width: 190px;">
                                    <template #append>
                                        <span>%</span>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
                            <el-form-item label="TIKTOK半托比例">
                                <el-input v-model.trim="addForm.rate22" placeholder="请输入" maxlength="50" clearable
                                    class="publicCss smallInput">
                                    <template #append>
                                        <span>%</span>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
                            <el-form-item label="跨境分销比例">
                                <el-input v-model.trim="addForm.rate23" placeholder="请输入" maxlength="50" clearable
                                    class="publicCss smallInput">
                                    <template #append>
                                        <span>%</span>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </section>
            <div class="drawer-footer">
                <el-button @click="addFormVisiblerole = false">取消</el-button>
                <!-- <my-confirm-button type="submit" :loading="addLoading" @click="onAddSubmit" /> -->
                <el-button type="primary" @click="onAddSubmit">确定</el-button>
            </div>
        </el-drawer>

        <!--编辑窗口-->
        <el-drawer title="编辑运营工资" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
            :visible.sync="editFormVisible" direction="btt" size="'auto'" style="position:absolute;"
            @close="closeEditForm">
            <section style="padding: 24px 48px 74px 24px;">
                <el-form ref="editForm" :model="editForm" :rules="editFormRules" label-width="120px" :inline="false">
                    <el-row :gutter="20" type="flex" justify="space-between">
                        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
                            <el-form-item label="运营">
                                <el-input style="width: 100%;" clearable v-model="editForm.userName"
                                    disabled></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
                            <el-form-item label="每日工资" prop="wages">
                                <el-input clearable v-model="editForm.wages" maxlength="8"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
                            <el-form-item label="生效日期" prop="effectiveTime">
                                <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                    v-model="editForm.effectiveTime" type="date" style="width: 100%;"
                                    placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
                            <el-form-item label="TEMU半托比例">
                                <el-input v-model.trim="editForm.temuBanRate" placeholder="请输入" maxlength="50" clearable
                                    class="publicCss smallInput">
                                    <template #append>
                                        <span>%</span>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
                            <el-form-item label="TEMU全托比例">
                                <el-input v-model.trim="editForm.temuRate" placeholder="请输入" maxlength="50" clearable
                                    class="publicCss smallInput">
                                    <template #append>
                                        <span>%</span>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20" type="flex" justify="space-between">

                        <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
                            <el-form-item label="SHEIN全托比例">
                                <el-input v-model.trim="editForm.sheInRate" placeholder="请输入" maxlength="50" clearable
                                    class="publicCss smallInput" style="width: 190px;">
                                    <template #append>
                                        <span>%</span>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
                            <el-form-item label="SHEIN自营比例">
                                <el-input v-model.trim="editForm.sheInSelfRate" placeholder="请输入" maxlength="50"
                                    clearable class="publicCss smallInput" style="width: 190px;">
                                    <template #append>
                                        <span>%</span>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
                            <el-form-item label="SHEIN半托比例">
                                <el-input v-model.trim="editForm.sheInHalfStockRate" placeholder="请输入" maxlength="50"
                                    clearable class="publicCss smallInput" style="width: 190px;">
                                    <template #append>
                                        <span>%</span>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
                            <el-form-item label="TIKTOK半托比例">
                                <el-input v-model.trim="editForm.tikTokHalfStockRate" placeholder="请输入" maxlength="50"
                                    clearable class="publicCss smallInput">
                                    <template #append>
                                        <span>%</span>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
                            <el-form-item label="跨境分销比例">
                                <el-input v-model.trim="editForm.kjFenXiaoRate" placeholder="请输入" maxlength="50"
                                    clearable class="publicCss smallInput">
                                    <template #append>
                                        <span>%</span>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </section>
            <div class="drawer-footer">
                <el-button @click.native="editFormVisible = false">取消</el-button>
                <!-- <my-confirm-button type="submit" :loading="editLoading" @click="onEditSubmit" /> -->
                <el-button type="primary" @click="onEditSubmit">确定</el-button>
            </div>
        </el-drawer>
        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
            <div style="display: flex; align-items: baseline; height: 75px;margin-top: 10px;">
                <el-date-picker style="width: 150px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay"
                    type="date" placeholder="选择生效日期" :clearable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                </el-date-picker>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                    :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                </el-upload>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <!-- 提醒 -->
        <el-dialog title="异常数据" :visible.sync="detailVisible" :center="false" width="75%" v-dialogDrag>

            <vxetablebase :id="'factoryProxySend20241106'" :tablekey="'factoryProxySend20241106'"
                @sortchange="detailSortChange" :tableData='detailList' :tableCols='detailTableCols'
                :loading='detailLoading' :border='true' :that="that" height="440px" ref="detailVxetable"
                :showsummary='false' :toolbarshow="false">
            </vxetablebase>

            <my-pagination ref="detailPage" :total="detailTotal" @get-page="reminder()" />
        </el-dialog>
    </my-container>
</template>

<script>

import { formatTime } from '@/utils'
import { getDirectorGroupEffectiveTimeList, pageDirectorGroupWagesByEffectiveTime, updateDirectorGroupWages, getDirectorGroupNameList, deleteDirectorGroupWages, imporDirectorGroupWagesAsync, pageDirectorGroupTipsList } from '@/api/bookkeeper/crossBorderV2'
import MyContainer from '@/components/my-container'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";

const balanceTableCols = [
    { istrue: true, prop: 'effectiveTime', label: '生效日期', sortable: 'custom', formatter: (row) => formatTime(row.effectiveTime, 'YYYY-MM-DD') },
    { istrue: true, prop: 'userName', label: '运营', sortable: 'custom', },
    { istrue: true, prop: 'wages', label: '每日工资', sortable: 'custom', },
    {
        istrue: true,  rop: '', label: `TEMU-半托`, merge: true,
        cols: [
            { istrue: true,  prop: 'temuBanRate', label: '比例', sortable: 'custom', width: '80', formatter: (row) => row.temuBanRate + "%" },
            { istrue: true,  prop: 'temuBanAmount', label: '金额', sortable: 'custom', width: '80', },
        ]
    },
    {
        istrue: true,  rop: '', label: `TEMU-全托`, merge: true,
        cols: [
            { istrue: true,  prop: 'temuRate', label: '比例', sortable: 'custom', width: '80', formatter: (row) => row.temuRate + "%" },
            { istrue: true,  prop: 'temuAmount', label: '金额', sortable: 'custom', width: '80', },
        ]
    },
    {
        istrue: true,  rop: '', label: `SHEIN-全托`, merge: true,
        cols: [
            { istrue: true,  prop: 'sheInRate', label: '比例', sortable: 'custom', width: '80', formatter: (row) => row.sheInRate + "%" },
            { istrue: true,  prop: 'sheInAmount', label: '金额', sortable: 'custom', width: '80', },
        ]
    },
    {
        istrue: true,  rop: '', label: `SHEIN-自营`, merge: true,
        cols: [
            { istrue: true,  prop: 'sheInSelfRate', label: '比例', sortable: 'custom', width: '80', formatter: (row) => row.sheInSelfRate + "%" },
            { istrue: true,  prop: 'sheInSelfAmount', label: '金额', sortable: 'custom', width: '80', },
        ]
    },
    {
        istrue: true,  rop: '', label: `SHEIN-半托`, merge: true,
        cols: [
            { istrue: true,  prop: 'sheInHalfStockRate', label: '比例', sortable: 'custom', width: '80', formatter: (row) => row.sheInHalfStockRate + "%" },
            { istrue: true,  prop: 'sheInHalfStockAmount', label: '金额', sortable: 'custom', width: '80', },
        ]
    },
    {
        istrue: true,  rop: '', label: `TikTok-半托`, merge: true,
        cols: [
            { istrue: true,  prop: 'tikTokHalfStockRate', label: '比例', sortable: 'custom', width: '80', formatter: (row) => row.tikTokHalfStockRate + "%" },
            { istrue: true,  prop: 'tikTokHalfStockAmount', label: '金额', sortable: 'custom', width: '80', },
        ]
    },
    {
        istrue: true,  rop: '', label: `跨境分销`, merge: true,
        cols: [
            { istrue: true,  prop: 'kjFenXiaoRate', label: '比例', sortable: 'custom', width: '80', formatter: (row) => row.kjFenXiaoRate + "%" },
            { istrue: true,  prop: 'kjFenXiaoAmount', label: '金额', sortable: 'custom', width: '80', },
        ]
    },
    {
        istrue: true, type: "button", label: '操作', btnList: [
            { label: "编辑", handle: (that, row) => that.onEdit(row) },
            { label: "删除", handle: (that, row) => that.onDelete(row) }
        ]
    }
]


const detailTableCols = [
    { istrue: true, prop: 'yearMonthDay', label: '日报', sortable: 'custom', },
    { istrue: true, prop: 'pageName', label: '页面', sortable: 'custom', },
    { istrue: true, prop: 'userName', label: '运营', sortable: 'custom', },
    { istrue: true, prop: 'content', label: '异常', sortable: 'custom', },
]

export default {
    name: 'Roles',
    components: { MyContainer, vxetablebase },
    data() {
        return {
            adduserFormVisible: false,
            userNameReadonly: true,
            defaultMenuList: [],
            showMenuList: [],
            formtitle: "",
            roleid: '',
            rolename: "",
            dialog: false,
            form: "",
            addFormVisible: false,
            dialogTableVisible: false,
            roles: [],
            rolesFilter: [],
            list: [],
            total: 0,
            sels: [], // 列表选中列
            statusList: [
                { name: '激活', value: true },
                { name: '禁用', value: false }
            ],
            listLoading: false,
            youlistLoading: false,
            pageLoading: false,
            addDialogFormVisible: false,
            editFormVisible: false, // 编辑界面是否显示
            editLoading: false,
            editFormRules: {
                // name: [{ required: true, message: '请输入角色名', trigger: 'blur' }],
                // enabled: [{ required: true, message: '请输入状态', trigger: 'change' }]
            },
            // 编辑界面数据
            editForm: {
                userIdList: [],
                wages: "",
                effectiveTime: "",
                // conversationTime: "",
                temuBanRate: null,
                temuRate: null,
                sheInRate: null,
                sheInSelfRate: null,
                sheInHalfStockRate: null,
                tikTokHalfStockRate: null,
                kjFenXiaoRate: null,

            },
            editFormRef: null,
            addFormVisiblerole: false, // 新增界面是否显示
            addLoading: false,
            addFormRules: {
                effectiveTime: [{ required: true, message: '请输入生效日期', trigger: 'blur' }],
                userIdList: [{ required: true, message: '请选择运营', trigger: 'change' }],
                wages: [{ required: true, message: '请输入每日工资', trigger: 'change' }],
            },
            // 新增界面数据
            addForm: {
                userIdList: [],
                wages: "",
                effectiveTime: "",
                // conversationTime: "",
                rate15: null,
                rate13: null,
                rate12: null,
                rate16: null,
                rate19: null,
                rate22: null,
                rate23: null,
            },
            addFormRef: null,
            deleteLoading: false,

            isExpansion: true,
            groupNameList: [],
            Filter: {
            },
            Filters: {

            },
            effectiveTime: "",
            effectiveTimes: "",
            dialogVisible: false,
            fileList: [],
            yearMonthDay: null,//导入日期

            // 提醒
            detailTableCols: detailTableCols,
            detailVisible: false,
            detailList: [],
            detailLoading: false,
            detailTotal: 0,
            detailEnum: {
                orderBy: '',
                isAsc: true,

            },
            currentRow: {},
            balanceSummaryarry: {},
            balanceTableCols: balanceTableCols,
            that:this,
        }
    },
    async mounted() {
        const GroupName = await getDirectorGroupNameList();
        this.groupNameList = GroupName.data?.map(item => {
            return {
                label: item.userName,
                value: [
                    item.id ? item.id : '',
                    item.userId ? item.userId : '',
                    item.userName ? item.userName : '',
                ].filter(Boolean).join(','), // 拼接 id 和 userId，用逗号分隔
                key: item.userId,

            }
        });
        this.getRoles()
    },
    beforeUpdate() {
    },
    created() {
        // this.debounceHandleSelectionChange = debounce(this.handleSelectionChange)
    },
    methods: {
        handelQuery() {
            this.GetRoleUsersinfo(this.effectiveTimes)
        },
        //根据生效时间来获取右侧值
        async GetRoleUsersinfo(row, column, e) {
            this.youlistLoading = true
            this.effectiveTimes = row
            const para = { ...this.Filters };
            const params = {
                ...para,
                effectiveTime: row
            };
            var res = await pageDirectorGroupWagesByEffectiveTime(params)
            this.youlistLoading = false
            // this.balanceTotal = res.data.total
            this.list = res.data.list;
            this.balanceSummaryarry = res.data.summary;
        },

        formatCreatedTime: function (row, column, time) {
            return formatTime(row, 'YYYY-MM-DD')
        },
        formatCreatedTimes: function (list, column, time) {
            return formatTime(list.effectiveTime, 'YYYY-MM-DD')
        },


        async getRoles() {
            this.listLoading = true
            const para = { ...this.Filter };
            if (this.Filter.conversationTime) {
                para.start = this.Filter.conversationTime[0];
                para.end = this.Filter.conversationTime[1];
            }
            const params = {
                ...para
            };
            const res = await getDirectorGroupEffectiveTimeList(params)
            this.listLoading = false
            this.roles = res.data
            if (!res?.success) {
                return
            }

        },

        // 显示编辑界面
        onEdit(row) {
            this.editFormVisible = true;
            this.editForm = { ...row }; // 确保创建一个新对象，不直接引用
        },
        onDelete(row) {
            this.$confirm('确认要删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const res = await deleteDirectorGroupWages({ id: row.id })
                if (!res.success) return
                this.$message({ type: 'success', message: '删除成功' });

                this.handelQuery();
                this.getRoles();
            }).catch(() => {
            });

        },


        closeEditForm() {
            this.$refs.editForm.resetFields()
        },
        // 显示新增界面
        async onAdd() {
            this.addFormVisiblerole = true
        },
        closeAddForm() {
            this.$refs.addForm.resetFields()
        },
        closeAddForm() {
        },
        // 编辑
        async onEditSubmit() {


            if (!this.editForm.wages && this.editForm.wages !== 0) {
                this.$message({ message: '工资不能为空', type: "warning" });
                return;
            }

            if (!this.editForm.effectiveTime) {
                this.$message({ message: '生效时间不能为空', type: "warning" });
                return;
            }

            // 检查 rateList 中是否有至少一个有效的比例值
            const rates = [
                this.editForm.temuBanRate,
                this.editForm.temuRate,
                this.editForm.sheInRate,
                this.editForm.sheInSelfRate,
                this.editForm.sheInHalfStockRate,
                this.editForm.tikTokHalfStockRate,
                this.editForm.kjFenXiaoRate
            ];
            const hasValidRate = rates.some(rate => rate !== null && rate !== undefined && rate !== '');

            if (!hasValidRate) {
                this.$message({ message: '必须选择一个平台占比', type: "warning" });
                return;
            }

            // 计算所有比例的总和
            const totalRate = rates.reduce((sum, rate) => {
                return sum + (rate ? parseFloat(rate) : 0);
            }, 0);

            if (totalRate > 100) {
                this.$message({ message: '各平台占比总和相加大于100%', type: "warning" });
                return;
            }

            if (totalRate < 100) {
                this.$message({ message: '各平台占比总和相加小于100%', type: "warning" });
                return;
            }
            this.addLoading = true;

            // 创建 rateList 数组
            const rateList = [];

            // 动态添加 rateDto 对象
            if (this.editForm.temuBanRate !== null && this.editForm.temuBanRate !== undefined && this.editForm.temuBanRate !== '') {
                rateList.push({ platForm: 15, rate: this.editForm.temuBanRate });
            }
            if (this.editForm.temuRate !== null && this.editForm.temuRate !== undefined && this.editForm.temuRate !== '') {
                rateList.push({ platForm: 13, rate: this.editForm.temuRate });
            }
            if (this.editForm.sheInRate !== null && this.editForm.sheInRate !== undefined && this.editForm.sheInRate !== '') {
                rateList.push({ platForm: 12, rate: this.editForm.sheInRate });
            }
            if (this.editForm.sheInSelfRate !== null && this.editForm.sheInSelfRate !== undefined && this.editForm.sheInSelfRate !== '') {
                rateList.push({ platForm: 16, rate: this.editForm.sheInSelfRate });
            }
            if (this.editForm.sheInHalfStockRate !== null && this.editForm.sheInHalfStockRate !== undefined && this.editForm.sheInHalfStockRate !== '') {
                rateList.push({ platForm: 19, rate: this.editForm.sheInHalfStockRate });
            }
            if (this.editForm.tikTokHalfStockRate !== null && this.editForm.tikTokHalfStockRate !== undefined && this.editForm.tikTokHalfStockRate !== '') {
                rateList.push({ platForm: 22, rate: this.editForm.tikTokHalfStockRate });
            }
            if (this.editForm.kjFenXiaoRate !== null && this.editForm.kjFenXiaoRate !== undefined && this.editForm.kjFenXiaoRate !== '') {
                rateList.push({ platForm: 23, rate: this.editForm.kjFenXiaoRate });
            }

            const params = {
                id: this.editForm.id,
                effectiveTime: this.editForm.effectiveTime,
                userIdList: this.editForm.userIdList,
                wages: this.editForm.wages,
                rateList: rateList
            };
            this.addLoading = true
            const res = await updateDirectorGroupWages(params)
            this.addLoading = false
            if (!res?.success) {
                return
            }
            this.editFormVisible = false
            this.getRoles();
            this.handelQuery();
        },

        // 新增角色
        async onAddSubmit() {
            if (!this.addForm.userIdList || this.addForm.userIdList.length === 0) {
                this.$message({ message: '运营不能为空', type: "warning" });
                return;
            }

            if (!this.addForm.wages && this.addForm.wages !== 0) {
                this.$message({ message: '工资不能为空', type: "warning" });
                return;
            }

            if (!this.addForm.effectiveTime) {
                this.$message({ message: '生效时间不能为空', type: "warning" });
                return;
            }

            // 检查 rateList 中是否有至少一个有效的比例值
            const rates = [
                this.addForm.rate15,
                this.addForm.rate13,
                this.addForm.rate12,
                this.addForm.rate16,
                this.addForm.rate19,
                this.addForm.rate22,
                this.addForm.rate23
            ];

            const hasValidRate = rates.some(rate => rate !== null && rate !== undefined && rate !== '');

            if (!hasValidRate) {
                this.$message({ message: '必须选择一个平台占比', type: "warning" });
                return;
            }

            // 计算所有比例的总和
            const totalRate = rates.reduce((sum, rate) => {
                return sum + (rate ? parseFloat(rate) : 0);
            }, 0);

            if (totalRate > 100) {
                this.$message({ message: '各平台占比总和相加大于100%', type: "warning" });
                return;
            }
            if (totalRate < 100) {
                this.$message({ message: '各平台占比总和相加小于100%', type: "warning" });
                return;
            }

            this.addLoading = true;

            // 创建 rateList 数组
            const rateList = [];

            // 动态添加 rateDto 对象
            if (this.addForm.rate15 !== null && this.addForm.rate15 !== undefined && this.addForm.rate15 !== '') {
                rateList.push({ platForm: 15, rate: this.addForm.rate15 });
            }
            if (this.addForm.rate13 !== null && this.addForm.rate13 !== undefined && this.addForm.rate13 !== '') {
                rateList.push({ platForm: 13, rate: this.addForm.rate13 });
            }
            if (this.addForm.rate12 !== null && this.addForm.rate12 !== undefined && this.addForm.rate12 !== '') {
                rateList.push({ platForm: 12, rate: this.addForm.rate12 });
            }
            if (this.addForm.rate16 !== null && this.addForm.rate16 !== undefined && this.addForm.rate16 !== '') {
                rateList.push({ platForm: 16, rate: this.addForm.rate16 });
            }
            if (this.addForm.rate19 !== null && this.addForm.rate19 !== undefined && this.addForm.rate19 !== '') {
                rateList.push({ platForm: 19, rate: this.addForm.rate19 });
            }
            if (this.addForm.rate22 !== null && this.addForm.rate22 !== undefined && this.addForm.rate22 !== '') {
                rateList.push({ platForm: 22, rate: this.addForm.rate22 });
            }
            if (this.addForm.rate23 !== null && this.addForm.rate23 !== undefined && this.addForm.rate23 !== '') {
                rateList.push({ platForm: 23, rate: this.addForm.rate23 });
            }

            const params = {
                effectiveTime: this.addForm.effectiveTime,
                userIdList: this.addForm.userIdList,
                wages: this.addForm.wages,
                rateList: rateList
            };

            const res = await updateDirectorGroupWages(params);
            this.addLoading = false;
            this.addForm.userIdList = [];
            this.addForm.wages = "";
            this.addForm.effectiveTime = "";
            this.addForm.rate15 = null,
                this.addForm.rate13 = null,
                this.addForm.rate12 = null,
                this.addForm.rate16 = null,
                this.addForm.rate19 = null,
                this.addForm.rate22 = null,
                this.addForm.rate23 = null

            if (res?.success) {
                this.addFormVisiblerole = false;
                this.handelQuery();
                this.getRoles();
                this.$message({ message: '添加成功', type: "success" });
                this.$refs.addForm.resetFields();
            } else {
                this.$message({ message: '添加失败', type: "warning" });
                this.$refs.addForm.resetFields();
            }
        },
        startImport() {
            this.fileList = []
            this.yearMonthDay = null
            this.dialogVisible = true;
        },
        //上传文件
        onUploadRemove(file, fileList) {
            this.fileList = []
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            this.dialogVisible = false;
        },

        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("effectiveTime", this.yearMonthDay);
            var res = await imporDirectorGroupWagesAsync(form);
            if (res?.success)
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
            this.uploadLoading = false
            this.dialogVisible = false;
            this.handelQuery();
            this.getRoles();
        },
        onSubmitUpload() {
            if (!this.yearMonthDay) {
                    this.$message({ message: "请选择日期", type: "warning" });
                    return false;
                }
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        async handleCommand(command) {
            switch (command) {
                //下载模版
                case 'a':
                    await this.downLoadFile()
                    break;
            }
        },
        async downLoadFile() {
            window.open("/static/excel/CrossBorderDailyDataMaintenance/运营工资导入模版.xlsx", "_blank");
        },
        async reminder() {
            if (!this.detailVisible) {
                this.detailVisible = true
            }
            this.$nextTick(async () => {
                var pager = this.$refs.detailPage.getPager();
                const params = {
                    ...pager,
                    ...this.detailEnum,
                };
                this.detailLoading = true;
                const res = await pageDirectorGroupTipsList(params);
                this.detailLoading = false;
                this.detailTotal = res.data.total
                this.detailList = res.data.list;
            })
        },
        //查看明细   
        detailSortChange({ order, prop }) {
            if (prop) {
                this.detailEnum.orderBy = prop
                this.detailEnum.isAsc = order.indexOf("descending") == -1 ? true : false
                this.reminder();

            }
        },
        sortchangeBalance({ order, prop }) {
            if (prop) {
                this.Filters.orderBy = prop
                this.Filters.isAsc = order.indexOf("descending") == -1 ? true : false
                this.handelQuery();

            }
        },


        

    },

}
</script>


<style lang="scss" scoped>
/* 定义一个公共样式 */
.publicCss {
    width: 100%;
}

/* 定义一个样式，使输入框宽度缩小三分之一 */
.smallInput {
    width: calc(100% - 33.33%);
}

/* 让标签单独占据一行 */
.el-form-item__label {
    display: block;
    width: 100%;
    text-align: left;
}

.el-form-item__content {
    margin-left: 0 !important;
}
</style>
