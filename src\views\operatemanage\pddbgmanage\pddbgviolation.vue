<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="违规通知时间">
                    <el-date-picker style="width: 200px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        ></el-date-picker>
                </el-form-item>
                <el-form-item label="运营组：">
                    <el-select filterable v-model="filter.groupId" placeholder="运营组" style="width: 110px" clearable>
                        <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value"
                            :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item label="店铺名称:">
                    <el-select filterable v-model="filter.shopCode" placeholder="请选择店铺" clearable style="width: 120px">
                        <el-option v-for="item in shopList" :key="item.id" :label="item.shopName"
                            :value="item.shopCode" />
                    </el-select>
                </el-form-item>
                <el-form-item label="处理进度">
                    <el-select filterable v-model="filter.punishStatus" placeholder="处罚状态" style="width: 120px" clearable>
                        <el-option label="待申诉" value="待申诉"></el-option>
                        <el-option label="平台处理中" value="平台处理中"></el-option>
                        <el-option label="待完善资料" value="待完善资料"></el-option>
                        <el-option label="申诉成功" value="申诉成功"></el-option>
                        <el-option label="申诉失败" value="申诉失败"></el-option>
                        <el-option label="超时关闭申诉" value="超时关闭申诉"></el-option>
                        <el-option label="已处理" value="已处理"></el-option>
                        <el-option label="申诉终止" value="申诉终止"></el-option>
                        <el-option label="确认违规，处理中" value="确认违规，处理中"></el-option>
                        <el-option label="未缴纳保证金" value="未缴纳保证金"></el-option>
                        <el-option label="主动认罚" value="主动认罚"></el-option>
                        <el-option label="申诉完结" value="申诉完结"></el-option>
                        <el-option label="申诉部分成功" value="申诉部分成功"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="违规类型">
                    <el-select filterable v-model="filter.punishInfo" placeholder="违规类型" style="width: 120px" clearable>
                        <el-option label="虚假发货" value="虚假发货"></el-option>
                        <el-option label="刷单套取平台补贴（优惠券、积分、红包、补贴等）" value="刷单套取平台补贴（优惠券、积分、红包、补贴等）"></el-option>
                        <el-option label="诱导第三方" value="诱导第三方"></el-option>
                        <el-option label="欺诈发货" value="欺诈发货"></el-option>
                        <el-option label="假货" value="假货"></el-option>
                        <el-option label="商品描述或质量抽检不合格" value="商品描述或质量抽检不合格"></el-option>
                        <el-option label="缺货" value="缺货"></el-option>
                        <el-option label="关联关系" value="关联关系"></el-option>
                        <el-option label="知产侵权" value="知产侵权"></el-option>
                        <el-option label="检测报告申诉" value="检测报告申诉"></el-option>
                        <el-option label="违禁品" value="违禁品"></el-option>
                        <el-option label="严重恶意扰乱消费者" value="严重恶意扰乱消费者"></el-option>
                        <el-option label="刷单套补贴" value="刷单套补贴"></el-option>
                        <el-option label="发票违规" value="发票违规"></el-option>
                        <el-option label="加运费发顺丰服务未履约" value="加运费发顺丰服务未履约"></el-option>
                        <el-option label="发货违规/消费者未实际收到货" value="发货违规/消费者未实际收到货"></el-option>
                        <el-option label='"顺丰包邮"服务未履约' value='"顺丰包邮"服务未履约'></el-option>
                        <el-option label="万人团/限时秒杀活动违约" value="万人团/限时秒杀活动违约"></el-option>
                        <el-option label="虚假轨迹" value="虚假轨迹"></el-option>
                        <el-option label="违规使用消费者信息" value="违规使用消费者信息"></el-option>
                        <el-option label="服务态度问题" value="服务态度问题"></el-option>
                        <el-option label="商品描述不符或质量抽检不合格" value="商品描述不符或质量抽检不合格"></el-option>
                        <el-option label="涉嫌售假" value="涉嫌售假"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="违规编号">
                     <el-input v-model="filter.pddViolationId" v-model.trim="filter.pddViolationId" placeholder="违规编号" style="width: 150px" maxlength="50" clearable></el-input>
                </el-form-item>
                <el-form-item label="重要:">
                        <el-select  v-model="filter.isImportant" placeholder="重要"
                            :collapse-tags="true" clearable >
                            <el-option label="重要" :value="true"></el-option>
                        </el-select>
               </el-form-item>

                <el-form-item label="是否处理:">
                        <el-select v-model="filter.isHandel" placeholder="是否处理"
                            :collapse-tags="true" clearable >
                            <el-option label="未处理" :value="false"></el-option>
                            <el-option label="已处理" :value="true"></el-option>
                        </el-select>
               </el-form-item>
               <el-form-item label="处理人:">
                    <el-select filterable v-model="filter.handeler" placeholder="请选择处理人" clearable style="width: 120px">
                        <el-option v-for="(item,i) in handelerList" :key="i" :label="item.msg"
                            :value="item.haderUserId" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSetImportant">设置重要</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='list'
            :tableCols='tableCols' :isSelection="false" :loading="listLoading">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="设置重要" :show-close="false" :visible.sync="pddbdemailImportantVisible" width="420px" close-on-click-modal
      element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
        <div style="width:100%;margin:15px auto 70px auto;">
            <span style="margin-left:5px">违规类型:</span>
                    <el-select filterable v-model="pddbdemailImportant.OperateManageIds" placeholder="违规类型" multiple style="width: 260px" clearable>
                        <el-option label="虚假发货" value="虚假发货"></el-option>
                        <el-option label="刷单套取平台补贴（优惠券、积分、红包、补贴等）" value="刷单套取平台补贴（优惠券、积分、红包、补贴等）"></el-option>
                        <el-option label="诱导第三方" value="诱导第三方"></el-option>
                        <el-option label="欺诈发货" value="欺诈发货"></el-option>
                        <el-option label="假货" value="假货"></el-option>
                        <el-option label="商品描述或质量抽检不合格" value="商品描述或质量抽检不合格"></el-option>
                        <el-option label="缺货" value="缺货"></el-option>
                        <el-option label="关联关系" value="关联关系"></el-option>
                        <el-option label="知产侵权" value="知产侵权"></el-option>
                        <el-option label="检测报告申诉" value="检测报告申诉"></el-option>
                        <el-option label="违禁品" value="违禁品"></el-option>
                        <el-option label="严重恶意扰乱消费者" value="严重恶意扰乱消费者"></el-option>
                        <el-option label="刷单套补贴" value="刷单套补贴"></el-option>
                        <el-option label="发票违规" value="发票违规"></el-option>
                        <el-option label="加运费发顺丰服务未履约" value="加运费发顺丰服务未履约"></el-option>
                        <el-option label="发货违规/消费者未实际收到货" value="发货违规/消费者未实际收到货"></el-option>
                        <el-option label='"顺丰包邮"服务未履约' value='"顺丰包邮"服务未履约'></el-option>
                        <el-option label="万人团/限时秒杀活动违约" value="万人团/限时秒杀活动违约"></el-option>
                        <el-option label="虚假轨迹" value="虚假轨迹"></el-option>
                        <el-option label="违规使用消费者信息" value="违规使用消费者信息"></el-option>
                        <el-option label="服务态度问题" value="服务态度问题"></el-option>
                        <el-option label="商品描述不符或质量抽检不合格" value="商品描述不符或质量抽检不合格"></el-option>
                        <el-option label="涉嫌售假" value="涉嫌售假"></el-option>
                    </el-select>
          </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="pddbdemailImportantVisible = false">取消</el-button>
          <el-button @click="submitSetImportant" v-throttle="3000" type="primary" v-loading="btnloading">保存</el-button>
        </span>
      </template>
    </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { getPingduoduoBGViolationList,exportPingduoduoBGViolationList,
    setPddBackGroundViolationImportant,handlePddBackGroundViolation,getPddEmailDetial,getHandelerList } from '@/api/operatemanage/pddbgmanage/pddbgmanage'
import { getDirectorGroupList,getList as getshopList } from '@/api/operatemanage/base/shop'
import dayjs from "dayjs";
import { formatTime } from "@/utils";

const tableCols = [
    { istrue: true, prop: 'groupId', label: '运营组', tipmesg: '', width: '120', sortable: 'custom', formatter: (row) =>row.groupName},
    { istrue: true, prop: 'shopName', label: '店铺名称', tipmesg: '', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'pddViolationId', label: '违规编号', tipmesg: '', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'violationType', label: '违规类型', width: '200', tipmesg: '', sortable: 'custom' },
    { istrue: true, prop: 'isImportant', label: '重要', width: '100', sortable: 'custom', formatter: (row) => {return row.isImportant?"重要":"不重要"} },
    { istrue: true, prop: 'violationTzTime', label: '违规通知时间',width: '200',  tipmesg: '', sortable: 'custom', formatter: (row) =>row.violationTzTime == null ? "--": formatTime(row.violationTzTime, "YYYY-MM-DD HH:mm:ss") },
    { istrue: true, prop: 'complainTime', label: '申诉时间',width: '200',  tipmesg: '', sortable: 'custom', formatter: (row) =>row.complainTime == null ? "--": formatTime(row.complainTime, "YYYY-MM-DD HH:mm:ss") },
    { istrue: true, prop: 'platfromHandleTime', label: '平台处理时间',width: '200',  tipmesg: '', sortable: 'custom', formatter: (row) => row.platfromHandleTime == null ? "--" :  formatTime(row.platfromHandleTime, "YYYY-MM-DD HH:mm:ss") },
    { istrue: true, prop: 'complainStatus', label: '处罚状态',tipmesg: '', sortable: 'custom' },
    {  istrue: true, type: 'isHandel', label: '是否处理', width: '100', style:(that,row)=>{return row.isHandel==true?"color:gray;":"color: blue;cursor: pointer"}, formatter: (row) =>{ return row.isHandel==true?"已处理":"未处理"},type:'click',handle: (that, row) => that.handelOp(row)},
    { istrue: true, prop: 'handlerUserName', label: '处理人', sortable: 'custom'},
    { istrue: true, prop: 'handlerTime', label: '处理时间', sortable: 'custom'}
]

export default {
    name: 'YunHanAdminPddBGViolation',
    components: { container, cesTable, MyConfirmButton },
    data() {
        return {
            that: this,
            filter: {
                groupId: null,
                shopCode: null,
                pddViolationId:null,
                punishStatus:null,
                punishInfo:null,
                timerange: null,
                startTime:null,
                endTime:null,
                isImportant:true,
                isHandel:false,
                handeler:null
            },
            list: [],
            shopList: [],
            directorGroupList:[],
            pager: { OrderBy: "violationTzTime", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            uploadLoading: false,
            dialogVisible: false,
            listLoading: false,
            fileList: [],
            //设置重要
           pddEmailTyleList:[],
           pddbdemailImportantVisible:false,
            pddbdemailImportant: {
                OperateManageIds: []
            },
            addLoading: true,
            btnloading:false,
            handelerList:[]
        };
    },

    async mounted() {
        await this.onSearch()
        await this.onchangeplatform()
    },

    methods: {
        //获取店铺
        async onchangeplatform() {
            this.categorylist = []
            const res1 = await getshopList({ platform: 2, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
            let res3 = await getDirectorGroupList({})
            this.directorGroupList = res3.data

            var resEmailType = await getPddEmailDetial({typeId:2,flag:0});
            this.pddEmailTyleList=resEmailType?.data;
            this.pddbdemailImportant.OperateManageIds=[];
            this.pddEmailTyleList.forEach(item => {
                if (item.isImportant) {
                    this.pddbdemailImportant.OperateManageIds.push(item.label);
                }
            })

            await this.getHandelList();
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            if(this.pager.OrderBy==null){
                this.pager.OrderBy="violationTzTime";
                this.pager.IsAsc=false;
            }
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await getPingduoduoBGViolationList(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.list = data
        },
        async nSearch() {
            await this.getlist()
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        async onExport(){
            if(this.pager.OrderBy==null){
                this.pager.OrderBy="violationTzTime";
                this.pager.IsAsc=false;
            }
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            const params = { ...pager, ...page, ... this.filter }
            let res = await exportPingduoduoBGViolationList(params);
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }

            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '拼多多店铺违规管理_' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        async submitSetImportant () {
         var res = await setPddBackGroundViolationImportant({typeId:2,operateManageIds:this.pddbdemailImportant.OperateManageIds});
         if (!res?.success) {
            this.btnloading = false;
            return
        }
        this.$message({ type: 'success', message: "设置成功" });
        this.btnloading = false;
        this.pddbdemailImportantVisible = false;
        await  this.onSearch();

        },
        onSetImportant () {
          this.pddbdemailImportantVisible = true;
        },
        async handelOp(row)
        {
            if(row.isHandel)
            {
                this.$message({type: 'info',message: '已处理不可重复处理'});
                return;
            }
            this.$confirm('确认处理吗, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
            }).then(async () => {
                const res = await handlePddBackGroundViolation({bid:row.id,typeId:2});
                if (!res?.success) {return }
                this.$message({type: 'success',message: '操作成功!'});
                await this.getHandelList();
                await this.onSearch();
            }).catch(() => {
            this.$message({type: 'info',message: '已取消操作'});
            });
        },
        async getHandelList()
        {
            var resHandeler = await getHandelerList({typeId:2});
            this.handelerList = resHandeler?.data;
        }
    }
};
</script>

<style lang="scss" scoped>

</style>
