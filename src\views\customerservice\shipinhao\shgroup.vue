<template>
  <my-container v-loading="pageLoading">
    <!-- 顶部操作 -->
    <template #header>
      <el-button-group>
        <el-button style="padding: 0;margin: 0;border: none;">
          <el-select v-model="filter.groupNameList" placeholder="分组" filterable multiple clearable collapse-tags>
            <el-option v-for="item in filterGroupList" :key="item" :label="item" :value="item">
            </el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin: 0;border: none;">
          <el-select v-model="filter.shopCodeList" placeholder="店铺" filterable multiple clearable collapse-tags>
            <el-option v-for="item in filterShopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin: 0;border: none;">
          <el-input v-model.trim="filter.sname" placeholder="姓名" style="width:160px;" clearable :maxlength="50" />
        </el-button>
        <el-button style="padding: 0;margin: 0;border: none;">
          <el-input v-model.trim="filter.snick" placeholder="客服昵称" style="width:160px;" clearable :maxlength="50" />
        </el-button>
        <el-button style="padding: 0;margin: 0;border: none;">
          <el-switch :width="40" @change="changeingroup" v-model="filter.isLeaveGroup" inactive-color="#228B22" active-text="包含离组" inactive-text="当前在组">
          </el-switch>
        </el-button>

        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onAddGroupShow">添加</el-button>
        <el-button type="primary" @click="onImport">导入</el-button>
        <el-button type="primary" @click="downLoadFile">下载模板</el-button>
        <el-button type="primary" @click="batchLeaveGroup">批量离组</el-button>
        <el-button type="primary" @click="showEditLog">分组编辑日志</el-button>
      </el-button-group>
    </template>

    <!-- 列表 -->
    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='false' 
    @sortchange='sortchange' @select='selectchange' :isSelection='false' 
    :tableData='data.list' :tableCols='tableCols' :loading="listLoading" />
    
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="data.total" :checked-count="sels.length" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="添加客服人员分组管理信息" :visible.sync="addgroupdialogVisible" width="25%" label-width="120px" v-dialogDrag>
      <span>
        <el-form ref="addForm" :model="addForm" :rules="formRules">
          <el-form-item prop="groupName" label="分组">
            <el-input style="width: 80%;" v-model="addForm.groupName" :maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
          <el-form-item prop="groupManager_DDid" label="组长">
            <YhUserelector v-if="addgroupdialogVisible" style="width: 80%;" :value.sync="addForm.groupManager_DDid" :text.sync="addForm.groupManager" @change="addManageChange"></YhUserelector>
          </el-form-item>
          <el-form-item prop="sname_DDid" label="姓名">
            <YhUserelector v-if="addgroupdialogVisible" style="width: 80%;" :value.sync="addForm.sname_DDid" :text.sync="addForm.sname" @change="addSnameChange"></YhUserelector>
          </el-form-item>
          <el-form-item prop="snick" label="昵称">
            <el-input style="width: 80%;" v-model="addForm.snick" :maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
          <el-form-item prop="shopCode" label="店铺">
            <el-select style="width: 80%;" v-model="addForm.shopCode" placeholder="店铺" @change="addShopChange" filterable clearable >
              <el-option v-for="item in filterShopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="phoneNo" label="手机号">
            <el-input style="width: 80%;" v-model="addForm.phoneNo" :maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
          <el-form-item prop="joinDate" label="入组日期">
            <el-date-picker v-model="addForm.joinDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
              type="date" style="width:80%" placeholder="选择日期" clearable >
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="leaveDate" label="离组日期">
            <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="addForm.leaveDate"
              style="width:80%" type="date" placeholder="选择日期" clearable >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </span>

      <span slot="footer" class="dialog-footer">
        <el-button @click="addgroupdialogVisible=false">关闭</el-button>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addGroup">确定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="编辑客服人员分组管理信息" :visible.sync="updategroupdialogVisible" width="25%" label-width="120px" v-dialogDrag>
      <span>
        <el-form ref="updateForm" :model="updateForm" :rules="formRules">
          <el-form-item prop="groupName" label="分组">
            <el-input style="width: 80%;" v-model="updateForm.groupName" :maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
          <el-form-item prop="groupManager_DDid" label="组长">
            <YhUserelector v-if="updategroupdialogVisible" style="width: 80%;" :value.sync="updateForm.groupManager_DDid" :text.sync="updateForm.groupManager" @change="updateManageChange"></YhUserelector>

          </el-form-item>
          <el-form-item prop="sname_DDid" label="姓名">
            <YhUserelector v-if="updategroupdialogVisible" style="width: 80%;" :value.sync="updateForm.sname_DDid" :text.sync="updateForm.sname" @change="updateSnameChange"></YhUserelector>
          </el-form-item>
          <el-form-item prop="snick" label="昵称">
            <el-input style="width: 80%;" v-model="updateForm.snick" :maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
          <el-form-item prop="shopCode" label="店铺">
            <el-select style="width: 80%;" v-model="updateForm.shopCode" placeholder="店铺" @change="updateShopChange" filterable clearable >
              <el-option v-for="item in filterShopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="phoneNo" label="手机号">
            <el-input style="width: 80%;" v-model="addForm.phoneNo" :maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
          <el-form-item prop="joinDate" label="入组日期">
            <el-date-picker v-model="updateForm.joinDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
              type="date" style="width:80%" placeholder="选择日期" clearable >
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="leaveDate" label="离组日期">
            <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="updateForm.leaveDate"
              style="width:80%" type="date" placeholder="选择日期" clearable >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </span>

      <span slot="footer" class="dialog-footer">
        <el-button @click="updategroupdialogVisible=false">关闭</el-button>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="updateGroup">确定</el-button>
      </span>
    </el-dialog>
    
    <el-dialog title="分组管理数据导入" :visible.sync="importdialogVisible" width="30%" v-dialogDrag>
      <el-row>
        <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
          <el-upload ref="upload" :auto-upload="false" :multiple="false" action
              accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
              <template #trigger>
                  <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                  @click="submitupload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
            </el-upload>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importdialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="离组日期选择" :visible.sync="leavegroupdialogVisible" width="30%" v-dialogDrag>
      <span>
        <template class="block">
          <el-date-picker v-model="dialogLeaveDate" style="width:63%" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
          </el-date-picker>
        </template>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="leavegroupdialogVisible = false">关闭</el-button>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="updategroupLeave()">提交</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import YhUserelector from '@/components/YhCom/yh-userselector.vue';
import { formatTime } from "@/utils";
import { downloadLink } from "@/utils/tools";
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser';
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import { getGroupNameList, getGroupList, addGroup, importSPHGroupAsync, batchUpdateLeaveDate, 
  getGroupLogList, updateGroup, deleteGroup, batchDeleteGroup
} from '@/api/customerservice/shipinhaoinquirs.js';

const tableCols = [
  { istrue: true, label: '', type: "checkbox" },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '分组', prop: 'groupName' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '组长', prop: 'groupManager' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '姓名', prop: 'sname' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '客服昵称', prop: 'snick' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '绑定手机号', prop: 'phoneNo' },
  { sortable: 'custom', istrue: true, width: '180', align: 'center', label: '店铺', prop: 'shopName' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '入组日期', prop: 'joinDate', formatter: (row) => formatTime(row.joinDate, 'YYYY-MM-DD') },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '离组日期', prop: 'leaveDate', formatter: (row) => formatTime(row.leaveDate, 'YYYY-MM-DD') },
  { sortable: 'custom', istrue: true, width: '150', align: 'center', label: '导入时间', prop: 'createdTime' },
  { sortable: 'custom', istrue: true, width: '180', align: 'center', label: '批次号', prop: 'batchNumber' },
  { istrue: true, type: "button", label: '操作', width: "180", btnList: [
      { label: "编辑", handle: (that, row) => that.handleupdategroup(row) }, 
      { label: "删除", handle: (that, row) => that.deletegroup(row) },
      { label: '批次删除', handle: (that, row) => that.batchdeletegroup(row) }
    ] 
  }
];

export default {
  name: "group",
  components: { MyContainer, vxetablebase, YhUserelector },
  data() {
    return {
      that: this,
      pageLoading: false,
      listLoading: false,
      uploadLoading: false,// 上传
      addgroupdialogVisible: false,// 新增
      updategroupdialogVisible: false,// 编辑
      importdialogVisible: false,// 导入
      leavegroupdialogVisible: false,// 批量离组
      filter: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        //过滤条件
        groupNameList: [],
        shopCodeList: [],
        sname: '',
        snick: '',
        isLeaveGroup: false,
        groupType: 1,
      },
      addForm: {
        groupType: 1,
        groupName: '',
        groupManager_DDid: '',
        groupManager: '',
        sname_DDid: '',
        sname: '',
        snick: '',
        shopCode: '',
        shopID: '',
        shopName: '',
        phoneNo: '',
        joinDate: '',
        leaveDate: '',
      },
      updateForm: {
        groupName: '',
        groupManager_DDid: '',
        groupManager: '',
        sname_DDid: '',
        sname: '',
        snick: '',
        shopCode: '',
        shopID: '',
        shopName: '',
        phoneNo: '',
        joinDate: '',
        leaveDate: '',
      },
      formRules: {
        groupName: [{ required: true, message: '请输入分组', trigger: 'change' }],
        groupManager_DDid: [{ required: true, message: '请输入组长', trigger: 'change' }],
        groupManager: [{ required: true, message: '请输入组长', trigger: 'change' }],
        sname_DDid: [{ required: true, message: '请输入姓名', trigger: 'change' }],
        sname: [{ required: true, message: '请输入姓名', trigger: 'change' }],
        snick: [{ required: true, message: '请输入昵称', trigger: 'change' }],
        shopCode: [{ required: true, message: '请输入店铺', trigger: 'change' }],
        joinDate: [{ required: true, message: '请输入入组日期', trigger: 'change' }],
        leaveDate: [{ required: true, message: '请输入离组日期', trigger: 'change' }],
      },
      sels: [],// 列表选中列
      data: {},// 查询返回数据集
      // total: 0,
      tableCols: tableCols,
      fileList: [],// 导入文件列表
      filterGroupList: [],// 分组选择器列表
      filterShopList: [],// 店铺选择器列表
      options: [
        { label: '已参与计算绩效', },
        { label: '未参与计算绩效', },
      ],
      dialogLeaveDate: null,// 离组日期
    };
  },
  async mounted() {
    //获取分组列表
    await this.getSPHGroup();
    //获取店铺列表
    await this.getSPHShop();
    this.onSearch();
  },
  methods: {
    //分组新增、编辑后，刷新
    onRefresh() {
      this.getSPHGroup();
      this.onSearch();
    },
    //在组离组切换
    changeingroup() {
      this.$emit("callBackInfo", this.filter.isLeaveGroup);
      this.onRefresh();
    },
    //分组选择器
    async getSPHGroup() {
      let res = await getGroupNameList({ isLeaveGroup: this.filter.isLeaveGroup, groupType: this.filter.groupType });
      this.filterGroupList = res.data;
    },
    //店铺选择器
    async getSPHShop() {
      let res = await getshopList({ platform: 20, CurrentPage: 1, PageSize: 100000 });
      this.filterShopList = res.data.list;
    },
    selectchange: function (rows, row) {
      this.sels = [];
      rows.forEach(f => {
        this.sels.push(f);
      });
    },
    //下载模版
    downLoadFile() {
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250616/1934541153118953473.xlsx', '视频号分组导入模板.xlsx');
    },
    //每页数量改变
    Sizechange(val) {
      this.filter.currentPage = 1;
      this.filter.pageSize = val;
      this.getList();
    },
    //当前页改变
    Pagechange(val) {
      this.filter.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.filter.orderBy = prop
        this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async getList() {
      this.listLoading = true;
      try {
        const { data, success } = await getGroupList(this.filter);
        if (success) {
          this.data = data;
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.listLoading = false;
      }
    },
    //查询分组
    onSearch() {
      //点击查询按钮时才将页数重置为1
      this.filter.currentPage = 1;
      this.$refs.pager.setPage(1);
      this.getList();
    },
    //打开添加弹窗
    onAddGroupShow() {
      this.$nextTick(() => {
        this.addgroupdialogVisible = true;
        this.$refs.addForm.clearValidate(); // 清除上次的校验结果
        this.$refs.addForm.resetFields();   // 重置表单数据
        this.addForm.groupManager = '';
        this.addForm.sname = '';
        this.$forceUpdate();
      });
    },
    //添加分组-组长选择
    async addManageChange() {
      var res = await QueryAllDDUserTop100({ keywords: this.addForm.groupManager_DDid });
      if (res?.success) {
        this.addForm.groupManager = res.data[0].userName;
      }
    },
    //添加分组-姓名选择
    async addSnameChange() {
      var res = await QueryAllDDUserTop100({ keywords: this.addForm.sname_DDid });
      if (res?.success) {
        this.addForm.sname = res.data[0].userName;
      }
    },
    //添加分组-店铺选择
    addShopChange() {
      if (null == this.addForm.shopCode) {
        this.addForm.shopName = null;
        this.addForm.shopID = null;
        return;
      }
      var shop = this.filterShopList.find(f => f.shopCode == this.addForm.shopCode);
      this.addForm.shopName = shop.shopName;
      this.addForm.shopID = shop.platformShopID;
    },
    //添加分组
    async addGroup() {
      var that = this;
      this.addForm.groupType = this.filter.groupType;
      this.$refs.addForm.validate(async valid => {
        if (valid) {
          var res = await addGroup(that.addForm);
          if (res?.success) {
            this.$message({ message: '添加成功', type: "success" });
            that.onRefresh();
            this.addgroupdialogVisible = false;
          }
        } else { 
          // 表单验证失败，提示用户
          this.$message.error('请检查填写的信息!');
        }
      });
    },
    //打开编辑弹窗
    handleupdategroup(row) {
      this.updategroupdialogVisible = true;
      this.$nextTick(() => {
        this.$refs.updateForm.clearValidate(); // 清除上次的校验结果
      });
      this.updateForm = row ? JSON.parse(JSON.stringify(row)) : {};
    },
    //编辑分组-组长选择
    async updateManageChange() {
      var res = await QueryAllDDUserTop100({ keywords: this.updateForm.groupManager_DDid });
      if (res?.success) {
        this.updateForm.groupManager = res.data[0].userName;
      }
    },
    //编辑分组-姓名选择
    async updateSnameChange() {
      var res = await QueryAllDDUserTop100({ keywords: this.updateForm.sname_DDid });
      if (res?.success) {
        this.updateForm.sname = res.data[0].userName;
      }
    },
    //编辑分组-店铺选择
    updateShopChange() {
      if (null == this.updateForm.shopCode) {
        this.updateForm.shopName = null;
        this.updateForm.shopID = null;
        return;
      }
      var shop = this.filterShopList.find(f => f.shopCode == this.updateForm.shopCode);
      this.updateForm.shopName = shop.shopName;
      this.updateForm.shopID = shop.shopID;
    },
    //编辑分组
    async updateGroup() {
      var that = this;
      this.$refs.updateForm.validate(async valid => {
        if (valid) {
          var res = await updateGroup(that.updateForm);
          if (res?.success) {
            this.$message({ message: '编辑成功', type: "success" });
            that.onRefresh();
            this.updategroupdialogVisible = false;
          }
        } else { 
          // 表单验证失败，提示用户
          this.$message.error('请检查填写的信息!');
        }
      });
    },
    //删除
    async deletegroup(row) {
      var that = this;
      this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteGroup({ id :row.id }).then(response => {
        if (response?.success) {
          // 删除成功的处理逻辑
          this.$message.success('删除成功!');
          that.onRefresh();
        } else {
          this.$message.error("删除失败");
        }
      })
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    //批量删除
    async batchdeletegroup(row) {
      var that = this;
      this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        batchDeleteGroup({ batchNumber: row.batchNumber }).then(response => {
        if (response?.success) {
          // 删除成功的处理逻辑
          this.$message.success('删除成功!');
          that.onRefresh();
        } else {
          this.$message.error("删除失败");
        }
      })
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    onImport() {
      this.importdialogVisible = true;
      this.uploadLoading = false;
      this.$nextTick(() => {
        if (this.$refs.upload) {
          this.$refs.upload.clearFiles();
        }
      });
      this.fileList.splice(0, 1);
    },
    //上传文件
    async uploadFile(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("groupType", this.filter.groupType);
      this.uploadLoading = true;
      const res = await importSPHGroupAsync(form);
      this.uploadLoading = false;
      if (res?.success) {
        this.$message({ message: '上传成功,正在导入中...', type: "success" });
        this.importdialogVisible = false;
        //导入文件后，分组列表需要刷新
        this.onRefresh();
      }
    },
    //更改上传文件
    async uploadChange(file, fileList) {
      if (fileList.length == 2) {
        fileList.splice(1, 1);
        this.$message({ message: "只允许单文件导入", type: "warning" });
        return false;
      }
      this.fileList.push(file);
    },
    //移除上传文件
    uploadRemove() {
      this.fileList.splice(0, 1);
    },
    //提交上传文件
    async submitupload() {
      if (this.fileList.length == 0) {
        this.$message.warning('您没有选择任何文件！');
        return;
      }
      this.$refs.upload.submit();
      this.$refs.upload.clearFiles();
      this.fileList.splice(0, 1);
      this.importdialogVisible = false;
    },
    //批量离组
    batchLeaveGroup() {
      if (0 == this.sels.length) {
        this.$message({ message: "至少选择一行", type: "warning", });
        this.leavegroupdialogVisible = false;
        return;
      }
      this.leavegroupdialogVisible = true;
    },
    //提交离组日期
    async updategroupLeave() {
      if (this.dialogLeaveDate == null || this.dialogLeaveDate == '') {
        this.$message({ message: "请选择日期", type: "warning" });
        return false;
      }
      if (this.sels.length == 0) {
        this.$message({ message: "请选择要修改的组", type: "warning" });
        return false;
      }
      var selids = this.sels.map(f => f.id);
      const params = {
          leaveDate: this.dialogLeaveDate,
          idList: selids
      };
      let res = await batchUpdateLeaveDate(params);
      if (res.success == true) {
        this.$message({ message: "修改成功", type: 'success', });
        this.leavegroupdialogVisible = false;
        this.sels = [];
        this.dialogLeaveDate = null;
        this.onRefresh();
      }
    },
    //分组编辑日志
    showEditLog() {
      this.$showDialogform({
        path: `@/views/customerservice/shipinhao/sphgrouplog.vue`,
        title: '分组编辑日志',
        args: {},
        height: '600px',
        width: '75%',
        callOk: this.afterSave,
      });
    },
    afterSave() {
      
    },

  }
};

</script>
<style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }

  .levelQueryInfoBox {
    display: flex;
    margin-bottom: 10px;
  }

  .publicCss {
    width: 220px;
    margin-right: 10px;
  }

  //解决下拉菜单多选由文字太长导致样式问题
  ::v-deep .el-select__tags-text {
    max-width: 60px;
  }
</style>
