<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-input v-model.trim="ListInfo.yh_EXT_Keywords" placeholder="关键字查询" maxlength="50" clearable
                    class="publicCss" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false" border
            id="20250530165859" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
            :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dateRange from "@/components/date-range/index.vue";
import { PageAliYanHuoGoodsInfoWaitImg } from '@/api/inventory/AliGfyh'
const tableCols = [
    { width: '', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { width: '', align: 'center', prop: 'addTime', label: '产生时间', },

]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            isExport: false,
            selectList: [],
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await PageAliYanHuoGoodsInfoWaitImg(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
