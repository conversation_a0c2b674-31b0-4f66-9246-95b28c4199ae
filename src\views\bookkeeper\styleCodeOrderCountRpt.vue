<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="时间" v-show="false">
                    <el-date-picker style="width: 230px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"></el-date-picker>
                </el-form-item>
                <el-form-item label="系列编码"> 
                    <el-input style="width:100%" :clearable="true" v-model="filter.styleCode"  v-model.trim="filter.styleCode" :maxlength =50></el-input>
                </el-form-item> 
                <el-form-item label="查询开学季系列">
                    <el-switch v-model="filter.isKxj" :active-value="true" :inactive-value="false">
                    </el-switch>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item> 
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='list'
            :tableCols='tableCols' :isSelection="false" :tableHandles='tableHandles' :loading="listLoading" :summaryarry="summaryarry">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template> 

        <el-dialog :title="chatProp.title" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false" v-dialogDrag>
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-show="false" v-model="chatProp.chatTime" type="daterange" range-separator="至" :clearable="false"
                    start-placeholder="开始日期" end-placeholder="结束日期" @change="chatSearch"
                    style="margin: 10px;" />
                <buschar :analysisData="chatProp.data" v-if="!chatProp.chatLoading"></buschar>
            </div>
            <div v-else v-loading="chatProp.chatLoading"></div>
        </el-dialog>

    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import {
    pageStyleCodeOrderCountRptAsync,
    styleCodeOrderCountRptAnalysis
} from "@/api/bookkeeper/styleCodeRptData";
import dayjs from "dayjs";
import buschar from '@/components/Bus/buschar'

const tableCols = [ 
    { istrue: true, prop: 'styleCode', label: '系列编码', tipmesg: '', width: '250', sortable: 'custom', },
    { istrue: true, prop: 'orderTotal', label: '订单量(汇总)', tipmesg: '', width: '200', sortable: 'custom', },
    {
        istrue: true, label: '趋势图', width: 'auto', type: 'button', btnList: [
            { label: '趋势图', handle: (that, row) => that.openChat(row.styleCode) }
        ]
    },
]

const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() }
];

export default {
    name: 'YunHanStyleCodeOrderCountRpt',
    components: { container, cesTable, MyConfirmButton,buschar },
    data() {
        return {
            that: this,
            filter: {
                startTime: null,
                endTime: null,
                groupId: null,
                shopCode: null,
                isKxj:false,
                timerange: []
            },
            list: [],
            shopList: [],
            directorGroupList: [],
            pager: { OrderBy: "orderTotal", IsAsc: false }, 
            chatProp: {
                chatDialog: false,//趋势图弹窗
                chatTime: null,//趋势图时间
                chatLoading: true,//趋势图loading
                data: [],//趋势图数据
            },
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            sels: [],
            uploadLoading: false,
            dialogVisible: false,
            listLoading: false,
            fileList: [],
            summaryarry:{}
        };
    },

    async mounted() {
        await this.onSearch() 
    },

    methods: { 
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            if(this.pager.OrderBy==null){
                this.pager.OrderBy="orderTotal";
                this.pager.IsAsc=false;
            }
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            } 
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await pageStyleCodeOrderCountRptAsync(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.list = data
            this.summaryarry=res.data.summary;
        },  
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        async openChat(styleCode) {
            //默认给7天时间
            this.chatInfo = {
                startDate: this.filter.startDate,
                endDate: this.filter.endDate,
                styleCode: styleCode,//用户id
            }
            this.chatProp.chatTime = [this.chatInfo.startDate, this.chatInfo.endDate]
            this.chatProp.title = `${styleCode}`
            await this.getCharts(this.chatInfo)
        },
        async getCharts(queryInfo) {
            this.chatProp.chatLoading = true
            const { data, success } = await styleCodeOrderCountRptAnalysis(queryInfo)
            if (success) {
                this.chatProp.data = data
                this.chatProp.chatDialog = true
                this.chatProp.chatLoading = false
            }
        },
        async chatSearch() {
            await this.getCharts(this.chatInfo)
        },
    }
};
</script>

<style lang="scss" scoped>

</style>
