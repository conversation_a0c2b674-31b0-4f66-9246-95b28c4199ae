<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter">
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='pagelist' @select='selectchange' :isSelection='false' :tableCols='tableCols'
            :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:0">
                      <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                          start-placeholder="开始时间" end-placeholder="结束时间"
                          style="width: 250px" :value-format="'yyyy-MM-dd'" @change="changeTime">
                      </el-date-picker>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:0">
                        <el-select v-model="filter.shopCode" placeholder="店铺" style="width:160px;" filterable clearable>
                            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                                :value="item.shopCode">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:0">
                        <el-select v-model="filter.saleAfterReason" placeholder="售后原因" style="width:160px;" filterable
                            clearable>
                            <el-option v-for="item in saleAfterReasonBaseList" :key="item" :label="item" :value="item">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:0">
                        <el-select v-model="filter.expressStatus" placeholder="发货物流状态" style="width:140px;" filterable
                            clearable>
                            <el-option v-for="item in expressStatusBaseList" :key="item" :label="item" :value="item">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:0">
                        <el-select v-model="filter.batchStr" placeholder="批次号" style="width:120px;" filterable
                            clearable>
                            <el-option v-for="item in pettyPaymentList" :key="item.batchStr" :label="item.batchStr"
                                :value="item.batchStr">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExportOnlyRefund">导出</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getpageList" />
        </template>

        <el-dialog :title="dialogTitle" :visible.sync="isOnlyVisable" width="70%" :close-on-click-modal="false"
            v-dialogDrag :before-close="close">
            <!-- 仅退款订单数据 -->
            <onlyrefundorder v-if="isOnly" ref="onlyrefundorder" :queryInfo="queryInfo"
                :saleAfterReasonBaseList="saleAfterReasonBaseList" :expressStatusBaseList="expressStatusBaseList"
                :backExpressComBaseList="backExpressComBaseList" />
            <!-- 退货退款订单数据 -->
            <backrefundorder v-if="isback" ref="backrefundorder" :queryInfo="queryInfo"
                :saleAfterReasonBaseList="saleAfterReasonBaseList" :expressStatusBaseList="expressStatusBaseList"
                :backExpressComBaseList="backExpressComBaseList" />
        </el-dialog>
         <el-dialog title='小额打款' :visible.sync="shopnamepopup" width="70%" v-dialogDrag append-to-body>
                <div style="height: 400px;">
                    <shopNameModule ref="shopNameModule" v-if="shopnamepopup" />
                </div>
        </el-dialog>

    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import onlyrefundorder from '@/views/customerservice/douyin/refund/onlyrefundorderdialog1';
import backrefundorder from '@/views/customerservice/douyin/refund/backrefundorderdialog1';
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import dayjs from 'dayjs'
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import {
    getSumRefundPageList, exportSumRefundList
} from '@/api/customerservice/douyinrefund'
import shopNameModule from '@/views/customerservice/douyin/refund/pettyPayment/shopNameModule.vue';

const tableCols = [
    { istrue: true, prop: 'shopName', label: '店铺', width: '200', sortable: 'custom', type: 'click', handle: (that, row) => that.openRefundSumGoods(row) },
    { istrue: true, prop: 'backOrderCount', label: '退货退款单量-占比', width: '150', sortable: 'custom', type: 'click', handle: (that, row) => that.jumpbackrefundorder(row, 0, false, '退货退款单量'),
        formatter: (row) => {
             const number=row.backOrderCountProportion>0 && row.backOrderCountProportion!=null ? row.backOrderCountProportion :0;
          return row.backOrderCount+' / '+number+'%';
        },
       
     },
    { istrue: true, prop: 'backAllAmount', label: '退货退款总金额-占比', width: '150', sortable: 'custom', type: 'click', handle: (that, row) => that.jumpshoprefundsumbackallamountdtl(row) ,
        formatter: (row) => {
             const number=row.backAllAmountProportion>0 && row.backAllAmountProportion!=null ? row.backAllAmountProportion :0;
          return row.backAllAmount+' / '+number+'%';
        },
        
    },
    { istrue: true, prop: 'backExpressCount', label: '退回快递-占比', width: '150', sortable: 'custom', type: 'click',  handle: (that, row) => that.jumpbackrefundorder(row, 1, false, '退回快递'), 
        formatter: (row) => { 
             const number=row.backExpressCountProportion>0 && row.backExpressCountProportion!=null ? row.backExpressCountProportion :0;
          return row.backExpressCount+' / '+number+'%';
        },
    },
    { istrue: true, prop: 'backExpressComCount', label: '退货物流公司', width: '150', sortable: 'custom', type: 'click', handle: (that, row) => that.jumpbackrefundgoodcom(row) },
    { istrue: true, prop: 'backSaleAfterReasonCount', label: '退货退款售后原因', width: '150', sortable: 'custom', type: 'click', handle: (that, row) => that.jumpbacksaleafterreason(row) },
    { istrue: true, prop: 'onlyOrderCount', label: '仅退款单量-占比', width: '150', sortable: 'custom', type: 'click', handle: (that, row) => that.jumpbackrefundorder(row, 0, true, '仅退款单量'),
        formatter: (row) => {
             const number=row.onlyOrderCountProportion>0 && row.onlyOrderCountProportion!=null ? row.onlyOrderCountProportion :0;
          return row.onlyOrderCount+' / '+number+'%';
        },
    },
    { istrue: true, prop: 'onlyAllAmount', label: '仅退款总金额-占比', width: '150', sortable: 'custom', type: 'click',handle: (that, row) => that.jumpshoprefundsumonlyallamountdtl(row) , 
        formatter: (row) => {
             const number=row.onlyAllAmountProportion>0 && row.onlyAllAmountProportion!=null ? row.onlyAllAmountProportion :0;
          return row.onlyAllAmount+' / '+number+'%';
        }
    },
    { istrue: true, prop: 'onlysaleAfterReasonCount', label: '仅退款售后原因', width: '150', sortable: 'custom', type: 'click', handle: (that, row) => that.jumponlysaleafterreason(row) },
    { istrue: true, prop: 'pettyPaymentCount', label: '小额打款单量-占比', width: '150', sortable: 'custom', type: 'click', handle: (that, row) => that.jumpOrderNumberDialog(row) ,
        formatter: (row) => {
             const number=row.pettyPaymentCountRate>0 && row.pettyPaymentCountRate!=null ? row.pettyPaymentCountRate :0;
          return row.pettyPaymentCount+' / '+number+'%';
        },
        
    },
    { istrue: true, prop: 'pettyPaymentAmount', label: '小额打款金额-占比', width: '150', sortable: 'custom', type: 'click', handle: (that, row) => that.jumpOrderNumberDialog(row) ,
         formatter: (row) => {
             const number=row.pettyPaymentAmountRate>0 && row.pettyPaymentAmountRate!=null ? row.pettyPaymentAmountRate :0;
          return row.pettyPaymentAmount+' / '+number+'%';
        },
        
    },
];
export default {
    name: "shoprefundsum",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, onlyrefundorder, backrefundorder,shopNameModule},
    props: ["saleAfterReasonBaseList", "expressStatusBaseList", "backExpressComBaseList", "batchStrBaseList"],
    data() {
        return {
            that: this,
            pageLoading: false,
            filter: {
                timerange: [],
                startDate: null,
                endDate: null,
                shopCode: null,
                saleAfterReason: null,
                batchStr: null,
            },
            shopList: [],
            pettyPaymentList: [],
            saleAfterReasonList: [],//售后原因
            expressStatusList: [],//发货物流状态
            tableCols: tableCols,
            listLoading: false,
            pagelist: [],
            total: 0,
            summaryarry: {},
            pager: { OrderBy: "shopName", IsAsc: false },
            sels: [], // 列表选中列
            selids: [],
            isOnlyVisable: false,
            isOnly: false,
            isback: false,
            queryInfo: null,
            dialogTitle: null,
            shopnamepopup: false,
        };
    },
    async mounted() {
        await this.getAllShopList();
        this.onSearch();
    },
    methods: {
        async changeTime(e) {
          this.filter.startDate = e ? e[0] : null
          this.filter.endDate = e ? e[1] : null
          this.filter.timerange = e
          this.batchScreening()
        },
        batchScreening() {
          let dates = [ new Date(this.filter.timerange[0]), new Date(this.filter.timerange[1] + ' 23:59:59') ];
          this.pettyPaymentList = this.batchStrBaseList.filter(item => {
            let createdTime = new Date(item.createdTime);
            return createdTime >= dates[0] && createdTime <= dates[1];
          });
        },
        async getAllShopList() {
            let shops = await getAllShopList({ platforms: [6] });
            this.shopList = [];
            this.shopList.push({ shopCode: '未知', shopName: '未知' });
            shops.data?.forEach(f => {
                if (f.shopCode && f.platform == 6)
                    this.shopList.push(f);
            });
            if (this.filter.timerange.length == 0) {
              this.filter.startDate = dayjs().format('YYYY-MM-DD')
              this.filter.endDate = dayjs().format('YYYY-MM-DD')
              this.filter.timerange = [this.filter.startDate, this.filter.endDate]
              this.batchScreening()
            }
        },
        getParam() {
            if (this.filter.timerange && this.filter.timerange.length > 0) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
                this.$message({ message: "请先选择日期！", type: "warning", });
                return;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para
            };
            return params;
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getpageList();
        },
        async getpageList() {
            const params = this.getParam();
            this.listLoading = true;
            const res = await getSumRefundPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.pagelist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async openRefundSumGoods(row) {
            let param = {
                startDate: this.filter.startDate,
                endDate: this.filter.endDate, shopCode: row.shopCode
            };
            this.$showDialogform({
                path: `@/views/customerservice/douyin/refund/shoprefundsumgoods.vue`,
                title: '商品',
                args: { param },
                height: '600px',
                width: '1280px',
            })
        },
        close() {
            this.isOnlyVisable = false;
            this.isback = false;
            this.isOnly = false;
        },
        jumpbackrefundorder(row, type, only, title) {
            if (!this.filter.startDate || !this.filter.endDate) {
                this.$message({ message: "请先选择日期！", type: "warning", });
                return;
            }
            this.dialogTitle = title
            let param = {
                shopCode: row.shopCode,
                shopName: row.shopName,
            };
            param.startDate = this.filter.startDate;
            param.endDate = this.filter.endDate;
            param.timerange = [param.startDate, param.endDate];
            param.onlysaleAfterReasonSort = null;
            param.isBackExpressNo = null;
            param.backExpressComSort = null;
            param.saleAfterReason = this.filter.saleAfterReason;
            param.expressStatus = this.filter.expressStatus;
            this.isonly = false;
            if (type == 1) {
                param.isBackExpressNo = true;
            }
            else if (type == 2) {
                param.isBackExpressNo = true;
                param.backExpressComSort = true;
            } else {
                param.onlysaleAfterReasonSort = true;
            }


            if (param.backExpressComSort == true && !only) {
                this.pager = { OrderBy: "backExpressCom", IsAsc: false }
            }
            if (only && param.onlysaleAfterReasonSort == true) {
                this.pager = { OrderBy: "saleAfterReason", IsAsc: false }
            }
            if (!only) {
                this.queryInfo = param.backExpressComSort ? param : { ...param, ...this.pager, }
                this.isback = true
            } else {
                this.queryInfo = { ...param, ...this.pager }
                this.isOnly = true
            }
            this.isOnlyVisable = true;
        },
        async onExportOnlyRefund() {
            const params = this.getParam();
            this.listLoading = true;
            const res = await exportSumRefundList(params);
            this.listLoading = false;
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '抖音退款店铺数据汇总_' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        jumpshoprefundsumbackallamountdtl(row) {
            this.$showDialogform({
                path: `@/views/customerservice/douyin/refund/shoprefundsumbackallamountdtl.vue`,
                title: row.shopName,
                args: { filter: this.filter, shopCode: row.shopCode, shopName: row.shopName },
                height: '380px',
                width: '800px',
            });
        },
        jumpbackrefundgoodcom(row) {
            this.$showDialogform({
                path: `@/views/customerservice/douyin/refund/shoprefundsumcomdtl.vue`,
                title: row.shopName,
                args: { filter: this.filter, shopCode: row.shopCode, shopName: row.shopName },
                height: '380px',
                width: '700px',
            });
        },
        jumpshoprefundsumonlyallamountdtl(row) {
            this.$showDialogform({
                path: `@/views/customerservice/douyin/refund/shoprefundsumonlyallamountdtl.vue`,
                title: row.shopName,
                args: { filter: this.filter, shopCode: row.shopCode, shopName: row.shopName },
                height: '380px',
                width: '800px',
            });
        },

        jumpbacksaleafterreason(row) {
            this.$showDialogform({
                path: `@/views/customerservice/douyin/refund/shoprefundsumbackreasondtl.vue`,
                title: row.shopName,
                args: { filter: this.filter, shopCode: row.shopCode, shopName: row.shopName },
                height: '480px',
                width: '700px',
            });
        },

        jumponlysaleafterreason(row) {
            this.$showDialogform({
                path: `@/views/customerservice/douyin/refund/shoprefundsumonlyreasondtl.vue`,
                title: row.shopName,
                args: { filter: this.filter, shopCode: row.shopCode, shopName: row.shopName },
                height: '480px',
                width: '700px',
            });
        },
        jumpOrderNumberDialog(row) {  //打款单量-占比
           if (!this.filter.startDate || !this.filter.endDate) {
                this.$message({ message: "请先选择日期！", type: "warning", });
                return;
            }
           const params = { startDate:this.filter.startDate,endDate:this.filter.endDate,shopName:row.shopName,shopCode:''}
            this.shopnamepopup = true
            this.$nextTick(() => {
                this.$refs.shopNameModule.getList('search', params)
            })
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
