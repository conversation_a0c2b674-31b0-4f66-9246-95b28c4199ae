<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="商品编码:">
          <el-input v-model="filter.goodsCode" placeholder="商品编码"/>
        </el-form-item>
        <el-form-item label="商品名称:">
          <el-input v-model="filter.goodsName" placeholder="商品名称"/>
        </el-form-item>
        <el-form-item label="仓库:">
         <el-select v-model="filter.warehouse" placeholder="仓库">
            <el-option label="请选择" value></el-option>
            <el-option label="义乌" value="0"></el-option>
            <el-option label="昌东" value="1"></el-option>
            <el-option label="安徽" value="3"></el-option>
          </el-select>
        </el-form-item>
         <el-form-item label="仓位:">
          <el-input v-model="filter.position" placeholder="仓位"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' :isSelectColumn='true' @sortchange='sortchange'
        :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
  
    <el-dialog title="导入商品库位" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
      <span>
          <el-row>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                <el-upload
                  ref="upload"
                  class="upload-demo"
                  :auto-upload="false"
                  :multiple="false"
                  :limit="1"
                  action
                  accept=".xlsx"
                  :http-request="uploadFile"
                  :file-list="fileList"
                  :data="fileparm">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
              </el-upload>
            </el-col>
          </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>

<script>
import {pageStorePosition,importStorePosition} from '@/api/storehouse/storehouse'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatWarehouse,downMobanCommand} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'goodsCode',label:'商品编码', width:'100',sortable:'custom'},
      {istrue:true,prop:'goodsName',label:'商品名称', width:'200'},
      {istrue:true,prop:'url',label:'图片', width:'60',type:'image'},
      {istrue:true,prop:'warehouse',label:'仓库', width:'70',sortable:'custom',formatter:(row)=>formatWarehouse(row.warehouse)},
      {istrue:true,prop:'position',label:'仓位',sortable:'custom', width:'150'},
      {istrue:true,prop:'remark',label:'备注', width:'150'},
      {istrue:true,prop:'modifiedTime',label:'编辑时间', width:'150',sortable:'custom'},
      {istrue:true,prop:'createdTime',label:'创建时间', width:'150',sortable:'custom'},
      {istrue:true,prop:'createdUserName',label:'创建人', width:'90',sortable:'custom'}
     ];
const tableHandles1=[{label:"导入商品库位", handle:(that)=>that.onstartImport()}, {label:'下载导入模板', handle:(that)=>downMobanCommand('inventory/箱及仓位导入模板')},];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton},
  data() {
    return {
      that:this,
      filter: {
         goodsCode:null,
         goodsName:null,
         warehouse :null,
         position :null,
         tempimageUrl:null,
      },
      list: [],
      pager:{OrderBy:"id",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      addLoading: false,
      dialogVisible: false,
      disabled: false,
      uploadLoading:false,
      fileList:[],
      fileparm:{},
    }
  },
  async mounted() {
    this.getlist();
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
  async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      this.listLoading = true
      const res = await pageStorePosition(params)
      this.listLoading = false
      if (!res?.success) {return }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {d._loading = false})
      this.list = data
    },
  async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
  selsChange: function(sels) {
      this.sels = sels
    },
  async onstartImport(){
      this.dialogVisible=true;
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      this.uploadLoading=true
      this.$refs.upload.submit();
    },
  async uploadFile(item) {
     if(!item||!item.file||!item.file.size){
        this.$message({message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importStorePosition(form);
      if (res.code==1) this.$message({message: "上传成功,正在导入中...", type: "success" });
      else this.$message({message: res.msg, type: "warning" });
      this.uploadLoading=false
    }
  }
}
</script>