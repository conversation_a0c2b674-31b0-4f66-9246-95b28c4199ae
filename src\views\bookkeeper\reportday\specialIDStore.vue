<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-select v-model="ListInfo.enabled" placeholder="状态" class="publicCss" clearable>
          <el-option key="启用" label="启用" :value="true" />
          <el-option key="禁用" label="禁用" :value="false" />
        </el-select>
        <el-select v-model="ListInfo.platform" placeholder="平台" @change="onchangeplatform" clearable filterable
          style="width: 160px;margin-right: 5px;">
          <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="ListInfo.shopName" placeholder="店铺" clearable filterable class="publicCss"
          @visible-change="shopIncident">
          <el-option v-for="item in shopList" :key="item.shopName" :label="item.shopName" :value="item.shopName" />
        </el-select>
        <el-input v-model.trim="ListInfo.proCode" placeholder="商品ID" maxlength="50" clearable class="publicCss" />
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="导入开始时间" end-placeholder="导入结束时间" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.createdUserName" placeholder="导入人" maxlength="50" clearable
          class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
        <el-button type="primary" @click="onkeyForbidden('enable')">一键启用</el-button>
        <el-button type="primary" @click="onkeyForbidden('forbidden')">一键禁用</el-button>
      </div>
    </template>
    <vxetablebase :id="'specialIdStore202408041444'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" @select="chooseCode"
      style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="120">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;align-items: center;">
              <el-button type="text" @click="onDeleteOperation(row)">删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getTianMaoComplainList } from '@/api/customerservice/taobaoshouhou'
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions, platformlist, formatPlatform } from '@/utils/tools'
import dayjs from 'dayjs'
import { importSpecialIdShopAsync, getSpecialIdShop, editSpecialIdShopAsync, exportSpecialIdShop, deleteSpecialIdShopAsync } from '@/api/bookkeeper/reportdayV2'

const tableCols = [
  { istrue: true, width: '100', type: "checkbox" },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'enabled', label: '状态', formatter: (row) => row.enabled == true ? "启用" : row.enabled == false ? "禁用" : '', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'platForm', label: '平台', formatter: (row) => formatPlatform(row.platForm), },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'proCode', label: '商品ID', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '导入时间', },
  { width: 'auto', align: 'center', prop: 'createdUserName', label: '导入人', },
]
export default {
  name: "scanCodePage",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      shopList: [],
      platformlist,
      ids: [],
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      summaryarry: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        enabled: null,//状态
        platform: null,//平台
        shopName: null,//店铺
        proCode: null,//商品ID
        createdUserName: null,//导入人
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    onkeyForbidden(label) {
      if (this.ids.length == 0) {
        this.$message.warning('请先选择数据')
        return
      }
      this.$confirm(`是否确认${label == 'enable' ? '启用' : '禁用'}选中数据`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.loading = true
        const { success } = await editSpecialIdShopAsync({ ids: this.ids, enabled: label == 'enable' ? true : false })
        this.loading = false
        if (success) {
          this.$message({
            type: 'success',
            message: `${label == 'enable' ? '启用' : '禁用'}成功!`
          });
          this.getList()
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    },
    //店铺选项数据为空则提示
    shopIncident(e) {
      if (e && e == true && this.shopList.length == 0) {
        this.$message.warning('请先选择平台')
      }
    },
    //平台改变
    async onchangeplatform(val) {
      if (!val) {
        this.shopList = []
        this.ListInfo.shopName = null
        return
      }
      //获取店铺信息
      const { data, success } = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
      if (!success) return
      this.shopList = data.list
    },
    //获取店铺信息
    async storeRequestData(val) {
      const { data, success } = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
      if (!success) return
      this.shopList = this.shopList.concat(data.list)
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importSpecialIdShopAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    //复选框数据
    chooseCode(row) {
      this.ids = []
      this.ids = row.map(item => item.id)
    },
    onDeleteOperation(row) {
      let ids = []
      ids.push(row.id)
      this.$confirm('是否确认删除选中数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.loading = true
        const { success } = await deleteSpecialIdShopAsync({ ids: ids })
        this.loading = false
        if (success) {
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
          this.getList()
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async exportProps() {
      const { data } = await exportSpecialIdShop(this.ListInfo)
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '特殊ID/店铺数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      const { data, success } = await getSpecialIdShop(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
