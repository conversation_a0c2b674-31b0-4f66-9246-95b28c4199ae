<template>
<el-container style="height: 100%; border: 1px solid #eee">
    <el-aside width="200px" style="background-color: rgb(238, 241, 246)">
            <DeptViewLeftTree @selected="leftTreeSelected" />
        </el-aside>
            <el-container style="height:100%;">
                <el-main>
                <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' tablekey="UserStatusReportList20230810"
                    @select='selectchange' :isSelection='false' :isSelectColumn='true' :tableData='list'
                    :summaryarry="summaryarry"  @summaryClick="onsummaryClick"  :showsummary='true' 
                    :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
                    <template slot='extentbtn'>

                        <el-button-group style="border:none;">
                            <el-button style="padding: 0;border:none;">
                                <el-date-picker style="width: 249px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" :clearable="false"
                                value-format="yyyy-MM-dd" range-separator="至" ></el-date-picker>

                            </el-button>                         
                            <el-button type="primary" @click="deptOnSearch">查询</el-button>
                        </el-button-group>
                    </template>
                </ces-table>
            </el-main>
            <el-footer>
                <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
            </el-footer>
              

            <el-dialog title="" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
                <span >                  
                        <el-form class="ad-form-query" :model="chartFilter" @submit.native.prevent label-width="100px">
                            日期：<el-date-picker style="width: 260px" v-model="chartFilter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false"></el-date-picker>
                            <el-button type="primary" @click="showEchartByRefresh">刷新</el-button>
                        </el-form>                  
                </span>
                <span>
                    <buschar  ref="buschar" :analysisData="buscharDialog.data"></buschar>
                </span>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="buscharDialog.visible = false">关闭</el-button>
                </span>
            </el-dialog>


            </el-container>
     
    </el-container>

</template>
<script>
import MyConfirmButton from "@/components/my-confirm-button";
import { getPageDeptUser, updateDDuserdingId } from '@/api/operatemanage/base/dingdingShow'
import { formatTime, } from "@/utils";
import dayjs from "dayjs";
import { PageEmployeeStatusReport,EmployeeStatusReportChart } from '@/api/admin/deptuser'

import DeptViewLeftTree from '@/views/admin/organization/DeptViewLeftTree.vue'

import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import buschar from '@/components/Bus/buschar'

const tableCols = [
  
    { istrue: true, prop: 'ymdDate', label: '日期', sortable: 'custom', width: '110', formatter: (row) => formatTime( row.ymdDate,"YYYY-MM-DD")  },
    { istrue: true, prop: 'workingCount', label: '在职人数', sortable: 'custom', width: '90',summaryEvent:true,type:'button',  btnList:[       
        {
            label:'',
            htmlformatter:(row)=> row.workingCount ,
            handle:(that,row)=>that.onDtlClick(row.ymdDate,1),         
        }          
    ]},
    { istrue: true, prop: 'joinCount', label: '入职人数', sortable: 'custom', width: '90',summaryEvent:true,type:'button',  btnList:[       
        {
            label:'',
            htmlformatter:(row)=> row.joinCount ,
            handle:(that,row)=>that.onDtlClick(row.ymdDate,2),         
        }          
    ]},
    { istrue: true, prop: 'leaveCount', label: '离职人数', sortable: 'custom', width: '90' ,summaryEvent:true,type:'button', btnList:[       
        {
            label:'',
            htmlformatter:(row)=> row.leaveCount ,
            handle:(that,row)=>that.onDtlClick(row.ymdDate,3),         
        } ]       
    },  
   
];
const tableHandles = []
export default {
    name: 'UserStatusReportList',
    components: { DeptViewLeftTree, container, cesTable, MyConfirmButton,buschar },
    data() {
        return {
            dingCode: null,
            ddUserId: 0,
            dialogVisibleSyj: false,
            that: this,
            isLoad: false,
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "", IsAsc: false },
            summaryarry: {},
            total: 0,
            sels: [],
            selids: [],
            listLoading: false,
            pageLoading: false,
            filter: {
                startTime:null,
                endTime:null,
                timerange:[  
                    formatTime(dayjs().subtract(7, "day"), "YYYY-MM-DD"),
                    formatTime(new Date(), "YYYY-MM-DD")
                ],
                deptCode: "",     
                corpId:'',
            },
            buscharDialog:{
                visible:false,
                data:null,
            },
            chartFilter: {              
                startTime: null,
                endTime: null,
                timerange: []
            },
        }
    },
    mounted() {

    },
    beforeUpdate() { },
    methods: {
        leftTreeSelected(row) {      
            console.log(row);   
            this.filter.deptCode=row && row.full_code? row.full_code:'';
            this.filter.corpId= row && row.corpId? row.corpId:'';
            this.deptOnSearch();
          
        },      
       
        deptOnSearch() {
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        async getlist() {
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            
            var pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }
            this.listLoading = true
            this.list = [];
            const res = await PageEmployeeStatusReport(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res?.data?.total
            const data = res?.data?.list     
                 
            this.list = data;
            this.summaryarry = res.data.summary;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.deptOnSearch();
        },
        selsChange: function (sels) {
            this.sels = sels
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
       
        async onsummaryClick(){
         
            this.chartFilter.timerange=[...this.filter.timerange];

            await this.showEchartByRefresh();
        },
        async showEchartByRefresh() {
            let that=this;
          
            if (this.chartFilter.timerange) {
                this.chartFilter.startTime = this.chartFilter.timerange[0];
                this.chartFilter.endTime = this.chartFilter.timerange[1];
            }
         

            var pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter,...this.chartFilter }
            const res = await EmployeeStatusReportChart(params);
            if(res && res.success){
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data;
                that.buscharDialog.title = "";
                that.$nextTick(() => {
                    that.$refs.buschar.initcharts();
                });
            }
              
        },
        //
        onDtlClick(ymdDate,dtlType) {
            let self = this;

            let prs={...this.filter};
            prs.dtlType=dtlType;
            prs.endTime=ymdDate;

            this.$showDialogform({
                path: `@/views/admin/organization/UserStatusReportDtlList.vue`,
                title: dtlType=='1'? '在职明细' : dtlType=='2'? '入职明细': '离职明细',
                autoTitle: false,
                args: prs,
                height: 820,
                width: '1024px',
                //callOk: self.onSearch
            })

        },
    }
}
</script>
<style scoped lang="scss" >
::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: rgb(233, 233, 248);
    border-radius: 3px;
}

::v-deep .el-table__body-wrapper {
    overflow: auto;
}
</style>
  