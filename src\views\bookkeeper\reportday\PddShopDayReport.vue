<template>
    <my-container v-loading="pageLoading">
      <template #header>
        <div>
        <el-button-group>
            <el-button style="padding: 0;margin: 0;">
              <el-select filterable v-model="filter.timeType" collapse-tags placeholder="时间类型"
                style="width: 110px">
                <el-option label="发生时间" :value="0" />
                <el-option label="订单时间" :value="1" />
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-date-picker style="width: 210px" v-model="filter.timerange" type="daterange" :clearable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                      start-placeholder="开始日期" end-placeholder="结束日期">
              </el-date-picker>
            </el-button>
            <!-- <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="filter.orderNoInner" :maxlength="150" clearable placeholder="内部单号" style="width:180px;"/>
            </el-button> -->
            <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter.orderTypes" multiple collapse-tags clearable placeholder="订单类型"
                style="width: 150px">
                <el-option v-for="item in orderDayRpt_OrderTypes" :label="item.label" :value="item.value"
                  :key="'orderTypes_filter_' + item.label" />
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <span>排除类型:</span>
              <el-select  v-model="filter.exceptOrderTypes" multiple collapse-tags clearable placeholder="排除订单类型" style="width: 150px">
                <el-option v-for="item in orderDayRpt_OrderTypes" :label="item.label" :value="item.value" :key="'orderTypes_filter_'+item.label" />
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter.orderStatusS" multiple collapse-tags clearable placeholder="订单状态"
                style="width: 150px">
                <el-option v-for="item in orderDayRpt_OrderStatuss" :label="item.label" :value="item.value"
                  :key="'orderStatusS_filter_' + item.label" />
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="filter.proCode" :maxlength="150" clearable placeholder="商品ID" style="width:180px;"/>
            </el-button>
            <!-- <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="filter.styleCode" :maxlength="150" clearable placeholder="系列编码" style="width:160px;"/>
            </el-button> -->
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="filter.goodsName" :maxlength="150" clearable placeholder="商品名称" style="width:160px;"/>
            </el-button>
            <el-button style="padding: 0;">
              <el-select filterable clearable v-model="filter.shopName" placeholder="店铺" style="width: 100px">
                <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopName">
                </el-option>
              </el-select>
            </el-button>
            <el-button style="padding: 0;width: 150px;margin: 0; border: none;">
              <el-select filterable clearable v-model="filter.brandShopList" placeholder="品牌" style="width: 150px" multiple collapse-tags>
                <el-option v-for="item in brandArr" :key="item" :label="item" :value="item" />
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
            <YyGroupSelector :value="filter.groupIds" @change="(v) => { filter.groupIds = v; }" :width="'190px'" :multiple="true" :collapse="true" ref="YyGroupSelector"/>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <YyDirectorSelector :value="filter.operateSpecialUserIds" :placeholder="'专员'"
              @change="(v) => { filter.operateSpecialUserIds = v; }" :width="'150px'" :multiple="true" :collapse="true" ref="YyDirectorSelector"/>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <YyDirectorSelector :value="filter.userIds" :placeholder="'助理'" @change="(v) => { filter.userIds = v; }" :width="'150px'" :multiple="true" :collapse="true" ref="YyDirectorSelector1"/>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.profit1UnZero" collapse-tags clearable placeholder="毛利1" style="width: 90px">
              <el-option label="正利润" :value="false"/>
              <el-option label="负利润" :value="true"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.profit2UnZero" collapse-tags clearable placeholder="毛利2" style="width: 90px">
              <el-option label="正利润" :value="false"/>
              <el-option label="负利润" :value="true"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.profit3UnZero" collapse-tags clearable placeholder="毛利3" style="width: 90px">
              <el-option label="正利润" :value="false"/>
              <el-option label="负利润" :value="true"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.exitProfitUnZero" collapse-tags clearable placeholder="出仓利润" style="width: 90px">
              <el-option label="正利润" :value="false"/>
              <el-option label="负利润" :value="true"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
          <el-select style="width: 115px;" v-model="filter.bzCategory" placeholder="经营大类" :collapse-tags="true"
          remote  :remote-method="remoteMethodBusinessCategory" clearable filterable>
            <el-option v-for="(item, i) in filterList.bussinessCategoryNames" :key="'bussinessCategoryNames' + i + 1"
              :label="item" :value="item" />
          </el-select>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-select style="width: 115px;" v-model="filter.bzCategory1" placeholder="一级类目" :collapse-tags="true"
          remote  :remote-method="remoteMethodCategoryName1s" clearable filterable>
            <el-option v-for="(item, i) in filterList.categoryName1s" :key="'categoryName1Level' + i + 1" :label="item"
              :value="item" />
          </el-select>
          </el-button>
            <el-button style="padding: 0;margin: 0;">
            <el-select style="width: 115px;" v-model="filter.bzCategory2" placeholder="二级类目" :collapse-tags="true"
            remote  :remote-method="remoteMethodCategoryName2s" clearable filterable>
              <el-option v-for="(item, i) in filterList.categoryName2s" :key="'categoryName2Level' + i + 1" :label="item"
                :value="item" />
            </el-select>
          </el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="clearaway">清除</el-button>
        </el-button-group>
        </div>
     </template>
      <vxetablebase
        :id="'pddShopDayReport202302031421'" :border="true" :align="'center'" :tablekey="'pddShopDayReport202302031421'"
        ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :cstmExportFunc="onExport"
        :isSelectColumn="true" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry'
        @summaryClick='onsummaryClick'
        :tableData='financialreportlist'  :tableCols='tableCols' @cellClick="cellClick"
        :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:95%;margin: 0"   :xgt="9999">
         <template slot='extentbtn'>
          <!-- <el-button type="primary" size="small" @click="dialogConfirmdata =true">日报确认</el-button> -->
          <!-- <el-button type="primary" size="small" @click="dialogConfirmdata2 =true">违规扣款确认</el-button> -->
          <!-- <el-button-group>
              <el-radio-group v-model="filter.refundType" size="small">
                <el-radio-button label="false" >发生维度</el-radio-button>
                <el-radio-button label="true">付款维度</el-radio-button>
             </el-radio-group>
          </el-button-group> -->
         </template>
      </vxetablebase>
      <!--分页-->
      <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
      </template>

      <el-dialog title="计算日报" :visible.sync="dialogCalDayRepotVis" width="40%" v-dialogDrag>
        <span>
          <el-row>
            <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
              <el-date-picker style="width: 50%; float: left;" v-model="calDayRepotyDate" type="date" format="yyyyMMdd"
                value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
                <el-button  type="success" style="margin-left:  30px;"  @click="calDayRepoty">计算日报</el-button>
            </el-col>
          </el-row>
        </span>
      </el-dialog>

      <el-dialog title="导入胜算" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
        <span>
            <el-row>
              <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
              <el-date-picker style="width: 100%" v-model="onimportfilter.yearmonthday" type="date" format="yyyyMMdd"   value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
           </el-col>
           </el-row>
            <el-row>
              <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                  <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx"
                    :http-request="uploadFile" :file-list="fileList" :data="fileparm">
                  <template #trigger>
                      <el-button size="small" type="primary">选取文件</el-button>
                  </template>
                  <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
                </el-upload>
              </el-col>
            </el-row>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </el-dialog>

      <el-dialog title="确认数据" :visible.sync="dialogConfirmdata" width="40%" v-dialogDrag>
        <span>
          <el-row>
            <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
              <el-date-picker style="width: 50%; float: left;" v-model="confirmDate" type="date" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
              <el-button type="success" style="margin-left:  30px;" @click="confirmData">确认</el-button>
            </el-col>
          </el-row>
        </span>
      </el-dialog>

     <el-dialog title="商品数据趋势图" :visible.sync="dialogDrVisible" width="80%" v-dialogDrag>
        <span>
            <productdrchart v-if="dialogDrVisible"></productdrchart>
        </span>

        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogDrVisible = false">关闭</el-button>
        </span>
      </el-dialog>

      <el-dialog title="实际快递费明细" :visible.sync="freightDetail.visible" width="80%" v-dialogDrag>
         <freightDetail ref="freightDetail" :filter="freightDetail.filter" style="height:600px;"></freightDetail>
      </el-dialog>

      <el-dialog title="每日退款明细" :visible.sync="EveryDayrefund.visible" width="80%" v-dialogDrag>
        <div>
         <EveryDayrefund ref="EveryDayrefund" :filter="EveryDayrefund.filter" style="height:600px;"></EveryDayrefund>
        </div>
      </el-dialog>
       <el-dialog title="赠品成本明细" :visible.sync="giftDetail.visible" width="80%" v-dialogDrag>
         <ordergiftdetail ref="ordergiftdetail" style="height:600px;"></ordergiftdetail>
      </el-dialog>
      <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
        <span>
          <buschar v-if="buscharDialog.visible" ref="buschar" :analysisData="buscharDialog.data"></buschar>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="buscharDialog.visible = false">关闭</el-button>
        </span>
    </el-dialog>

      <el-dialog title="快递分析" :visible.sync="expressfreightanalysisVisible" width="80%" v-dialogDrag>
        <span>
          <expressfreightanalysis ref="expressfreightanalysis"></expressfreightanalysis>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="expressfreightanalysisVisible = false">关闭</el-button>
        </span>
    </el-dialog>
    </my-container>
  </template>
  <script>
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
  import { getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
  import {getDirectorGroupList,getDirectorList} from '@/api/operatemanage/base/shop'
  import EveryDayrefund from '@/views/bookkeeper/reportday/EveryDayrefund'
  import {productOrderDayReport as pageProductDayReport,exportProductOrderDayReport,queryProductOrderDayReportSumChart,orderDayRpt_OrderTypes,orderDayRpt_OrderStatuss, getBrandFeeList} from '@/api/bookkeeper/reportdayV2'
  import {calDayRepotyDtl as calDayRepoty} from '@/api/bookkeeper/reportday'
  import {importProductDayReport} from '@/api/bookkeeper/import'
  import {getAllProBrand} from '@/api/inventory/warehouse'
  import productdrchart from '@/views/bookkeeper/reportday/productdrchart'
  import ProductADPDD from '@/views/bookkeeper/reportday/ProductADPDD'
  import {formatPlatform,formatTime,formatYesornoBool,formatLinkProCode} from "@/utils/tools";
  import cesTable from "@/components/Table/table.vue";
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  import InputMult from "@/components/Comm/InputMult";
  import { Loading } from 'element-ui';
  import { ruleDirectorGroup } from '@/utils/formruletools'
  import freightDetail from '@/views/bookkeeper/reportday/freightDetail'
  import expressfreightanalysis from '@/views/express/expressfreightanalysis'
  import buschar from '@/components/Bus/buschar'
  import { getBusinessCategorySelectData } from '@/api/operatemanage/base/category'
  import importmodule from '@/components/Bus/importmodule'
  import ordergiftdetail from '@/views/order/gift/ordergiftDetail'
  import { getListByStyleCode } from "@/api/inventory/basicgoods"
  import inputYunhan from "@/components/Comm/inputYunhan";
  import {filterStatus} from '@/utils/getCols'
  import YyGroupSelector from "@/components/YhCom/YyGroupSelector.vue"
  import YyDirectorSelector from "@/components/YhCom/YyDirectorSelector.vue"
  let loading;
  const startLoading = () => {
    loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
    });
  };
  const tableCols =[
          {istrue:true,fixed: 'left',prop:'fsYearMonthDay',label:'年月日',sortable:'custom', width:'60',type:'custom'},
          // { istrue: true, fixed: 'left', prop: 'orderNo', label: '原始线上单号',  width: '80' ,type:'custom'},
          //{ istrue: true, fixed: 'left', prop: 'orderNoInner', label: '内部单号',  width: '80' ,type:'click',handle:(that, row)=>that.JumpPddOrderDayReport(row)},
          // { istrue: true, fixed: 'left', prop: 'orderStatus', label: '订单状态',  width: '80' ,type:'custom'},
          // { istrue: true, fixed: 'left', prop: 'orderType', label: '订单类型',  width: '80' ,type:'custom',formatter:(row)=>row.orderType==0?"普通订单":row.orderType==1?"补发订单":row.orderType==2?"供销Plus":"其他"},
          {istrue:true,fixed: 'left',prop:'shopName',label:'店铺',sortable:'custom', width:'70',formatter:(row)=> row.shopName,type:'click',handle:(that,row)=>that.JumpDetailOrderDayReport(row)},
          // {istrue:true,fixed: 'left',prop:'proCode',fix:true,label:'商品ID', width:'80',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
          //{istrue:true,fixed: 'left',prop:'styleCode',fix:true,label:'系列编码', width:'80',sortable:'custom',type:'custom'},
          { istrue: true,  prop: 'bzCategory', label: '经营大类', sortable: 'custom', width: '70' },
          { istrue: true,  prop: 'bzCategory1', label: '一级类目', sortable: 'custom', width: '70' },
          { istrue: true,  prop: 'bzCategory2', label: '二级类目', sortable: 'custom', width: '70' },
          // {istrue:true,prop:'goodsCode',fix:true,label:'商品编码', width:'80',sortable:'custom',type:'custom'},
          //{istrue:true,prop:'goodsName',label:'商品名称',sortable:'custom', width:'80'},
          { istrue: true, label: '小组头像', width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
  { istrue: true, prop: 'groupId', exportField: 'groupName', label: '小组', sortable: 'custom', width: '70', formatter: (row) => row.groupName,type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, },
  { istrue: true, label: '专员头像', width: '70',type:'ddAvatar',ddInfo:{type:2,prop:'operateSpecialUserId'} },
  { istrue: true, prop: 'operateSpecialUserId', exportField: 'operateSpecialUserName', label: '运营专员', sortable: 'custom', width: '70', formatter: (row) => row.operateSpecialUserName,type:'ddTalk',ddInfo:{type:2,prop:'operateSpecialUserId',name:'operateSpecialUserName'}, },
          { istrue: true,  prop: 'userId', exportField:'userName',label: '运营助理', sortable: 'custom', width: '45', permission: "cgcoltxpddprsi", formatter: (row) => row.userName, type: 'custom' },
          { istrue: true, summaryEvent: true, prop: 'orderCount', label: '订单量', sortable: 'custom', width: '60', formatter: (row) => !row.orderCount ? " " : row.orderCount },
          {istrue:true,summaryEvent:true,prop:'payAmont',label:'付款金额',sortable:'custom', width:'80',formatter:(row)=> !row.payAmont?" ": row.payAmont},
          { istrue: true, summaryEvent: true, prop: 'saleAmont', label: '销售金额', sortable: 'custom', width: '80', type: 'custom', tipmesg: '付款金额-刷单金额-发货前退款-发货后退款', formatter: (row) => !row.saleAmont ? " " : row.saleAmont },
  {istrue:true,summaryEvent:true,prop:'exitProfit',label:'出仓利润',sortable:'custom', width:'80',type:'custom',tipmesg:'付款金额-总销售成本-出仓成本-快递费',formatter:(row)=> !row.exitProfit?" ": row.exitProfit},
  { istrue: true, summaryEvent: true, prop: 'saleCost', label: '总销售成本', sortable: 'custom', width: '80', tipmesg: '付款金额对应的销售成本', style: (that, row) => that.renderCost(row), handle: (that, row) => that.showCost(row), formatter: (row) => !row.saleCost ? " " : row.saleCost },
  {istrue:true,summaryEvent:true,prop:'freightFeeTotal',label:'快递费',sortable:'custom', width:'80',type:'custom',tipmesg:'实际快递费+预估快递费',formatter:(row)=> !row.freightFeeTotal?" ": row.freightFeeTotal},
  { istrue: true, summaryEvent: true, prop: 'exitCost', label: '出仓成本', sortable: 'custom', width: '60', formatter: (row) => row.exitCost == 0 ? " " : row.exitCost, tipmesg: '真实出仓成本+预估出仓成本' },
  { istrue: true, summaryEvent: true, prop: 'extAmount3', label: '真实出仓成本', sortable: 'custom', width: '60', formatter: (row) => row.extAmount3 == 0 ? " " : row.extAmount3 },
  { istrue: true, summaryEvent: true, prop: 'extAmount4', label: '预估出仓成本', sortable: 'custom', width: '60', formatter: (row) => row.extAmount4 == 0 ? " " : row.extAmount4 },
  { istrue: true, summaryEvent: true, prop: 'dkAmountYg', label: '平台扣点(预估)', sortable: 'custom', width: '60', formatter: (row) => row.dkAmountYg == 0 ? " " : row.dkAmountYg, tipmesg: '平台扣点(预估)' },
          {istrue:true,summaryEvent:true,prop:'replaceSendCost',label:'代发成本',sortable:'custom', width:'80',tipmesg:'代发成本',formatter:(row)=> !row.replaceSendCost?" ": row.replaceSendCost},
          {istrue:true,summaryEvent:true,prop:'replacementCost',label:'补发成本',sortable:'custom', width:'80',tipmesg:'订单类型为补发/换货',formatter:(row)=> !row.replacementCost?" ": row.replacementCost},
          { istrue: true, summaryEvent: true, prop: 'giftCost', label: '赠品成本', sortable: 'custom', width: '70', formatter: (row) => !row.giftCost ? " " : row.giftCost },
          {istrue:true,summaryEvent:true,prop:'brushAmont',label:'刷单金额',sortable:'custom', width:'80',tipmesg:'刷单金额',formatter:(row)=> !row.brushAmont?" ": row.brushAmont},
          {istrue:true,summaryEvent: true,prop:'',label:'退款', merge:true,permission:"cgcoltxpddprsi",
              cols:[
              {istrue:true,summaryEvent:true,prop:'refundAmontBefore',label:'发货前退款',sortable:'custom', width:'80',type:'custom'},
              {istrue:true,summaryEvent:true,prop:'refundAmontAfter',label:'发货后退款',sortable:'custom', width:'80',type:'custom'},
             {istrue:true,summaryEvent:true,prop:'refundAmont',label:'总退款金额',sortable:'custom', width:'80',tipmesg:'当日发生的退款金额，包括历史订单',formatter:(row)=> !row.refundAmont?" ": row.refundAmont},
            ]},

            {istrue:true,summaryEvent:true,prop:'cancelCost',label:'取消单返还成本',sortable:'custom', width:'80',tipmesg:'返还客服已确认的取消单成本,以确认时间统计',formatter:(row)=> !row.cancelCost?" ": row.cancelCost,type:'custom'},
            {istrue:true,summaryEvent:true,prop:'extAmount6',label:'销退仓返还成本',sortable:'custom', width:'60',tipmesg:'退件扫码再发返还成本',formatter:(row)=> !row.extAmount6?" ": row.extAmount6,type:'custom'},
            { istrue: true, summaryEvent: true, prop: 'diffCornerCost', label: '定制款成本差', sortable: 'custom', width: '110', formatter: (row) => row.diffCornerCost == 0 ? " " : row.diffCornerCost },
            {istrue:true,summaryEvent:true,prop:'purchaseFreight',label:'采购成本差价',sortable:'custom', width:'100',formatter:(row)=> row.purchaseFreight==0?0: row.purchaseFreight,tipmesg:'入库单的采购运费分摊到该入库单的商品编码对应的ID上'},
            {istrue:true,summaryEvent:true,prop:'profit1',label:'毛一',sortable:'custom', width:'80',type:'custom',tipmesg:'销售金额-总销售成本+取消单返还成本-采购成本差',formatter:(row)=> !row.profit1?" ": row.profit1},
            // { istrue: true, summaryEvent: true, prop: 'expressDeductAmount', label: '延迟发货扣款', sortable: 'custom', width: '90', type: 'custom', tipmesg: '延迟发货扣款(快递罚款)', permission: "cgcoltxpddprsi", formatter: (row) => !row.expressDeductAmount ? " " : row.expressDeductAmount },
            // { istrue: true, summaryEvent: true, prop: 'unctionDeductAmount', label: '虚假轨迹扣款', sortable: 'custom', width: '45', type: 'custom', tipmesg: '虚假轨迹扣款(快递罚款)', permission: "cgcoltxpddprsi", formatter: (row) => !row.unctionDeductAmount ? " " : row.unctionDeductAmount },
            // { istrue: true, summaryEvent: true, prop: 'trackCargoDeductAmount', label: '虚假发货扣款', sortable: 'custom', width: '45', type: 'custom', tipmesg: '虚假轨迹扣款(快递罚款)', permission: "cgcoltxpddprsi", formatter: (row) => !row.trackCargoDeductAmount ? " " : row.trackCargoDeductAmount },
            // { istrue: true, summaryEvent: true, prop: 'stockoutDeductAmount', label: '缺货扣款', sortable: 'custom', width: '45', type: 'custom', tipmesg: '缺货扣款(快递罚款)', permission: "cgcoltxpddprsi", formatter: (row) => !row.stockoutDeductAmount ? " " : row.stockoutDeductAmount },
            // { istrue: true, summaryEvent: true, prop: 'merchantDeductAmount', label: '商家责任退货扣款', sortable: 'custom', width: '45', type: 'custom', tipmesg: '其它扣款', permission: "cgcoltxpddprsi", formatter: (row) => !row.merchantDeductAmount ? " " : row.merchantDeductAmount },
            // { istrue: true, summaryEvent: true, prop: 'sumAmount', label: '违规总扣款', sortable: 'custom', width: '45', type: 'custom', tipmesg: '违规总扣款', formatter: (row) => !row.sumAmount ? " " : row.sumAmount },
            {
            istrue: true, summaryEvent: true, rop: '', label: `平台扣点`, merge: true, prop: 'mergeField', permission: "cgcoltxpddprsi",
            cols: [
            { istrue: true, summaryEvent: true, prop: 'dK14', label: '物流提醒短信服务', sortable: 'custom', width: '130', tipmesg: '60010304物流提醒短信服务费用', formatter: (row) => row.dK14 == 0 ? " " : row.dK14 },
            { istrue: true, summaryEvent: true, prop: 'dK15', label: '售后补偿', sortable: 'custom', width: '40', tipmesg: '60010305售后补偿现金券扣款', ormatter: (row) => row.dK15 == 0 ? " " : row.dK15 },
            { istrue: true, summaryEvent: true, prop: 'dK19', label: '多多进宝', sortable: 'custom', width: '45', formatter: (row) => row.dK19 == 0 ? " " : row.dK19 },
            { istrue: true, summaryEvent: true, prop: 'dK20', label: '技术服务费(不计算)', sortable: 'custom', width: '45', tipmesg: '60011302技术服务费(不计算)', formatter: (row) => row.dK20 == 0 ? " " : row.dK20 },
            { istrue: true, summaryEvent: true, prop: 'dK20_YuGu', label: '技术服务费（预估）', sortable: 'custom', width: '45', tipmesg: '技术服务费（预估）', formatter: (row) => row.dK20_YuGu == 0 ? " " : row.dK20_YuGu },
            { istrue: true, summaryEvent: true, prop: 'refundFreight', label: '退运费', sortable: 'custom', width: '45', formatter: (row) => row.refundFreight == 0 ? " " : row.refundFreight },
            { istrue: true, summaryEvent: true, prop: 'fraudShipments', label: '欺诈发货', sortable: 'custom', width: '70', formatter: (row) => row.fraudShipments == 0 ? " " : row.fraudShipments },
            { istrue: true, summaryEvent: true, prop: 'dK22', label: '多多推广', sortable: 'custom', width: '70', formatter: (row) => row.dK22 == 0 ? " " : row.dK22 },
            // { istrue: true, summaryEvent: true, prop: 'dK50', label: '投诉赔偿', sortable: 'custom', width: '80', tipmesg: '60010306投诉赔偿：售后维权投诉赔偿扣款', formatter: (row) => row.dK50 == 0 ? " " : row.dK50 },
            { istrue: true, summaryEvent: true, prop: 'dkOther', label: '其他支出', sortable: 'custom', width: '80', tipmesg: '其他支出', formatter: (row) => row.dkOther == 0 ? " " : row.dkOther },
            { istrue: true, summaryEvent: true, prop: 'dkAmont', label: '扣点合计', sortable: 'custom', width: '45', formatter: (row) => row.dkAmont == 0 ? " " : row.dkAmont },
            ]
           },
           {
          istrue: true, summaryEvent: true, rop: '', label: `营销费用`, merge: true, prop: 'mergeField1', permission: "cgcoltxpddprsi",
          cols: [
          { istrue: true, summaryEvent: true, prop: 'quanzhan_usemoney', label: '全站推广', sortable: 'custom', width: '45', formatter: (row) => row.quanzhan_usemoney == 0 ? " " : row.quanzhan_usemoney },
            { istrue: true, summaryEvent: true, prop: 'sousuo_usemoney', label: '搜索推广', sortable: 'custom', width: '45', formatter: (row) => row.sousuo_usemoney == 0 ? " " : row.sousuo_usemoney },
            { istrue: true, summaryEvent: true, prop: 'zhibo_usemoney', label: '直播推广', sortable: 'custom', width: '70', formatter: (row) => row.zhibo_usemoney == 0 ? " " : row.zhibo_usemoney },
            { istrue: true, summaryEvent: true, prop: 'changjin_usemoney', label: '场景展示', sortable: 'custom', width: '70', formatter: (row) => row.changjin_usemoney == 0 ? " " : row.changjin_usemoney },
            { istrue: true, summaryEvent: true, prop: 'fangxin_usemoney', label: '放心推', sortable: 'custom', width: '60', formatter: (row) => row.fangxin_usemoney == 0 ? " " : row.fangxin_usemoney },
            { istrue: true, summaryEvent: true, prop: 'biaoZhun_usemoney', label: '标准推广', sortable: 'custom', width: '80', formatter: (row) => row.biaoZhun_usemoney },
            { istrue: true, summaryEvent: true, prop: 'zhiNeng_usemoney', label: '智能托管', sortable: 'custom', width: '80', formatter: (row) => row.zhiNeng_usemoney },
            { istrue: true, summaryEvent: true, prop: 'evaluationCourtesy', label: '评价有礼', sortable: 'custom', width: '80', formatter: (row) => row.evaluationCourtesy },
            { istrue: true, summaryEvent: true, prop: 'promotionFee', label: '合计', sortable: 'custom', width: '60', formatter: (row) => row.promotionFee == 0 ? " " : row.promotionFee },
          ]
         },
            {istrue:true,summaryEvent:true,prop:'packageFee',label:'包装材料',sortable:'custom', width:'80',formatter:(row)=> !row.packageFee?" ": row.packageFee},

          {istrue:true,summaryEvent:true,prop:'freightFee',label:'实际快递费',sortable:'custom', width:'80',type:'custom'},
          {istrue:true,summaryEvent:true,prop:'freightFeeVirtual',label:'预估快递费',sortable:'custom', width:'80',formatter:(row)=> !row.freightFeeVirtual?" ": row.freightFeeVirtual},

        { istrue: true, summaryEvent: true, prop: 'platformSvrFeeYg', label: '平台服务费(预估)', sortable: 'custom', width: '60', formatter: (row) => row.platformSvrFeeYg == 0 ? " " : row.platformSvrFeeYg, tipmesg: '平台服务费(预估)' },
         {istrue:true,summaryEvent:true,prop:'profit2',label:'毛二',sortable:'custom', width:'80',type:'custom',tipmesg:'毛一-包装费-快递费合计-产品运费',
                                                formatter:(row)=>!row.profit2?" ": row.profit2},
      {
        istrue: true, summaryEvent: true, prop: 'ygfy', label: `预估费用（正式）`, merge: true, prop: 'mergeField2',
        cols: [
          { istrue: true, summaryEvent: true, prop: 'productFreight', label: '产品运费', sortable: 'custom', width: '60', formatter: (row) => row.productFreight == 0 ? " " : row.productFreight, tipmesg: '总销售成本*1.2%' },
          { istrue: true, summaryEvent: true, prop: 'processingCost', label: '加工费', sortable: 'custom', width: '60', formatter: (row) => row.processingCost == 0 ? " " : row.processingCost, tipmesg: '参考日报数据维护-加工费（工价*（订单量-发货前退款单量））' },
          { istrue: true, summaryEvent: true, prop: 'sampleFeeBX', label: '样品费', sortable: 'custom', width: '60', formatter: (row) => row.sampleFeeBX == 0 ? " " : row.sampleFeeBX },
          { istrue: true, summaryEvent: true, prop: 'sampleFee', label: '拿样费', sortable: 'custom', width: '60', formatter: (row) => row.sampleFee == 0 ? " " : row.sampleFee },
          { istrue: true, summaryEvent: true, prop: 'shootFee', label: '道具费', sortable: 'custom', width: '60', formatter: (row) => row.shootFee == 0 ? " " : row.shootFee },
          { istrue: true, summaryEvent: true, prop: 'lossOffFee', label: '仓库损耗', sortable: 'custom', width: '60', formatter: (row) => row.lossOffFee == 0 ? " " : row.lossOffFee },
          { istrue: true, summaryEvent: true, prop: 'wages', label: '运营工资', sortable: 'custom', width: '60', formatter: (row) => row.wages == 0 ? " " : row.wages, tipmesg: '参考各组工资占比' },
          { istrue: true, summaryEvent: true, prop: 'customerServiceWages', label: '客服工资', sortable: 'custom', width: '60', formatter: (row) => row.customerServiceWages == 0 ? " " : row.customerServiceWages, tipmesg: '销售金额*0.33%' },
        ]
      },
        {istrue:true,summaryEvent:true,prop:'profit3',label:'毛三利润',sortable:'custom', width:'80',type:'custom',tipmesg:'毛二利润-预估费用',permission:"lirunprsi,profit3rsi",formatter:(row)=> !row.profit3?" ": row.profit3},
        { istrue: true, summaryEvent: true, prop: 'profit32', label: '毛三利润新', sortable: 'custom', width: '67', type: 'custom', tipmesg: '毛二利润-预估费用', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit32 ? " " : row.profit32 },
        ];
  const tableHandles=[
          {label:"导出", handle:(that)=>that.$refs.table.setExportCols()},
         // { label: "计算订单日报", handle: (that) => that.showCalDayRepoty() },
          {label:"刷新", handle:(that)=>that.onRefresh()},
        ];

  export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable,productdrchart,InputMult,freightDetail, buschar, expressfreightanalysis,importmodule,ordergiftdetail,ProductADPDD,EveryDayrefund,inputYunhan,vxetablebase, YyGroupSelector, YyDirectorSelector },
    data() {
      return {
        brandArr: [],
        orderDayRpt_OrderTypes: orderDayRpt_OrderTypes,
        orderDayRpt_OrderStatuss: orderDayRpt_OrderStatuss,
        dialogConfirmdata: false,
        // dialogConfirmdata2: false,
        confirmDate:'',
        confirmDate2:'',
        searchloading:'',
        dialogCalDayRepotVis:false,
        calDayRepotyDate:null,
        that:this,
        filter: {
          orderStatusS: [],//
          orderTypes: [],//
          exceptOrderTypes :[],//
          timeType:1,
          //refundType: false,
          reportType:0,
          platform:2,
          OrderDayReportType:4,
          // shopCode:null,
          proCode:null,//
          // styleCode:null,//
          // productName:null,
          // brandId:null,
          // groupId:null,
          startTime: null,
          endTime: null,
           timerange:null,
           isChart:null,
          // // 运营助理
          // userId :null,
          // // 车手
          // userId2:null,
          // // 备用
          // userId3:null,
          // // 运营专员 ID
          // operateSpecialUserId:null,
          // shopName:null,
          // orderSource:null,
          // distributor:null,
          // profit1UnZero:null,
          // profit2UnZero:null,
          // profit3UnZero :null,
          // profit4UnZero:null,
          // groupType: null,
          // timerange1:null,
          // listingStartTime: null,
          // listingEndTime: null,
          // noProfitDay:null
          orderNo:null,
          orderNoInner:null,
          goodsName:null,
          styleCode:null,
          bzCategory: null,//经营大类
          bzCategory1: null,//一级类目
          bzCategory2: null,//二级类目
          shopName:null,
          groupId:null,
          operateSpecialUserId:null,
          userId:null,
          userIds: [],//运营助理
          operateSpecialUserIds: [],//运营专员
          groupIds: [],//小组
          profit1UnZero:null,
          profit2UnZero:null,
          profit3UnZero:null,
          exitProfitUnZero:null,
          brandShopList: [],
        },
        filterList: {
          bussinessCategoryNames: [],//经营大类
          categoryName1s: [],//一级类目
          categoryName2s: []//二级类目
        },
        onimportfilter:{
          yearmonthday:null,
        },
        styleCode:null,
        shopList:[],
        userList:[],
        brandlist:[],
        grouplist:[],
        directorlist:[],
        financialreportlist: [],
        tableCols:tableCols,
        tableHandles:tableHandles,
        total: 0,
        pager:{OrderBy:" SaleAmont ",IsAsc:false},
        sels: [], // 列表选中列
        options:[],
        listLoading: false,
        pageLoading: false,
        earchloading:false,
        summaryarry:{},
        selids:[],
        fileList:[],
        dialogVisible:false,
        uploadLoading:false,
        importFilte:{},
        fileList:[],
        fileparm:{},
        editparmVisible:false,
        editLoading:false,
        editparmLoading:false,
        drawervisible:false,
        dialogDrVisible:false,
        expressfreightanalysisVisible:false,
        drparamProCode:'',
        autoformparm:{
                 fApi:{},
                 options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
                 rule:[]
          },
        freightDetail:{
          visible:false,
          filter:{
            proCode:null,
            timeRange:[]
          },
        },
        EveryDayrefund:{
             visible:false,
          filter:{
            proCode:null,
            timeRange:[],
            afterSaleType:"2",
            orderStatus:"已取消",
            goodsStatus:"",
            timeRange1:[],
            platform:10
          }
          },
        giftDetail:{visible:false},
        buscharDialog:{visible:false,title:"",data:[]},
        drawervisible:false,
      };
    },
    async mounted() {
      this.init();
      this.filter.orderStatusS = filterStatus('已取消', this.orderDayRpt_OrderStatuss, this.filter.orderStatusS);
      this.filter.orderTypes = filterStatus(["补发订单", "换货订单"], this.orderDayRpt_OrderTypes, this.filter.orderTypes);
      this.filter.exceptOrderTypes = filterStatus(["普通订单", "分销Plus", "分销"], this.orderDayRpt_OrderTypes, this.filter.exceptOrderTypes);
    },
    async created() {
      await this.getShopList();
      await this.initformparm();
      if (this.$route.query && this.$route.query.dayCount) {

        this.filter.noProfitDay = parseInt(this.$route.query.dayCount);
        this.filter.shopCode = this.$route.query.shopCode;
        this.filter.groupId = this.$route.query.groupId;
        this.filter.operateSpecialUserId=this.$route.query.operateSpecialUserId;
        let dateStr = this.$route.query.yearMonthDay.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
        this.filter.timerange=[dateStr,dateStr];
        this.filter.refundType=false;
        this.onSearch();
      }
    },
    methods: {
    //获取经营大类数据
    remoteMethodBusinessCategory(query) {
      setTimeout(async () => {
        const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 1, categoryName: query })
        this.filterList.bussinessCategoryNames = res.data
      }, 200)
    },
    //获取一级类目数据
    remoteMethodCategoryName1s(query) {
      setTimeout(async () => {
        const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 2, categoryName: query })
        this.filterList.categoryName1s = res.data
      }, 200)
    },
    //获取二级类目数据
    remoteMethodCategoryName2s(query) {
      setTimeout(async () => {
        const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 3, categoryName: query })
        this.filterList.categoryName2s = res.data
      }, 200)
    },
    //清除搜索选项内容
    clearaway() {
      this.filter.orderNo = null;
      this.filter.orderNoInner = null;
      this.filter.orderStatusS = null;
      this.filter.orderTypes = [];
      this.filter.exceptOrderTypes = [];
      this.filter.proCode = null;
      this.filter.goodsName = null;
      this.filter.styleCode = null;
      this.filter.shopName = null;
      this.filter.groupId = null;
      this.filter.operateSpecialUserId = null;
      this.filter.userId = null;
      this.filter.profit1UnZero = null;
      this.filter.profit2UnZero = null;
      this.filter.profit3UnZero = null;
      this.filter.exitProfitUnZero = null;
      this.$refs.YyGroupSelector.clearValue()
      this.$refs.YyDirectorSelector.clearValue()
      this.$refs.YyDirectorSelector1.clearValue()
    },
      async onsummaryClick(property){
      this.filter.startTime =null;
      this.filter.endTime =null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      this.filter.isChart=1;
      var pager = this.$refs.pager.getPager();
      const params = {...pager,...this.pager,...this.filter};
      params.column=property;
      let that=this;
      that.listLoading = true;
      that.buscharDialog.loading= true;
      const res = await queryProductOrderDayReportSumChart(params).then(res=>{
        that.buscharDialog.loading= false;
        that.buscharDialog.data=res.data
        that.buscharDialog.title=res.data.legend[0]
      });
      that.listLoading = false;
      that.buscharDialog.visible=true;
      this.$nextTick(async() => {  await that.$refs.buschar.initcharts();});
      this.filter.isChart=null;
      },

      JumpPddOrderDayReport(row){
        this.$emit("ChangeActiveName",row.orderNoInner)
      },
      JumpDetailOrderDayReport(row){
        this.$emit("ChangeActiveName2",row.shopName,4,this.filter.timerange)
      },
      cellClick(prms){
        if(prms?.column?.field   && prms?.column?.field==='profit3IncreaseGoOnDays'  ){
          let row=prms.row;
          this.showprchart2(row.proCode, row.platform) ;
        }

      },
      async confirmData () {
        if (!this.confirmDate) {
          this.$message({ type: 'warning', message: '请选择日期!' });
          return;
        }
        let par = {
          dailyReportDate: this.confirmDate,
          dailyReportType: '拼多多订单日报',
        };
        let confirmtitle = '【' + this.confirmDate + '】' + par.dailyReportType + '数据，确定确认？';
        this.$confirm(confirmtitle)
          .then(async _ => {
            let res = await insertDailyReportConfirmList(par);
            if (res.data) {
              this.$message({ type: 'success', message: '保存成功!' });
            }
            this.dialogConfirmdata = false;
          })
          .catch(_ => { });
      },

      async showCalDayRepoty(){
        this.dialogCalDayRepotVis = true;
      },
      async calDayRepoty(){
        if(this.calDayRepotyDate==null)
      {
        this.$message({ type: 'warning', message: '请先选择日期!' });
        return
      }
        let res= await calDayRepoty({type:'PddOrder',yearMonthDay:this.calDayRepotyDate});
        if (!res?.success)  return
        this.$message({type: 'success',message: '正在计算中,请稍候...'});
        this.dialogCalDayRepotVis=false;
      },
      showEveryDayrefund(row){
        this.EveryDayrefund.filter.proCode = row.proCode;
        if (row.yearMonthDay != null){
          var dayStr =row.yearMonthDay.substr(0,4)+'-'+row.yearMonthDay.substr(4,2)+'-'+row.yearMonthDay.substr(6,2)
          this.EveryDayrefund.filter.timeRange = [dayStr,dayStr];
        }
          else {
          this.EveryDayrefund.filter.timeRange = this.filter.timerange
        }
        this.EveryDayrefund.visible=true;
         setTimeout(async () => {
          await this.$refs.EveryDayrefund.onSearch();
        }, 100);

      },
      datetostr(date) {
        var y = date.getFullYear();
        var m = ("0" + (date.getMonth() + 1)).slice(-2);
        var d = ("0" + date.getDate()).slice(-2);
        return y + "-" + m + "-" + d;
      },
      async init(){
          var date1 = new Date(); date1.setDate(date1.getDate()-1);
          var date2 = new Date(); date2.setDate(date2.getDate()-1);
          this.filter.timerange=[];
          this.filter.timerange[0]=this.datetostr(date1);
          this.filter.timerange[1]=this.datetostr(date2);
          console.log(this.filter)
          const { data:data1 } = await getBrandFeeList({ currentPage: 1, pageSize: 9999999, feeType: 1 })
          this.brandArr = data1.list.map(item => item.brandName)
          this.brandArr = [...new Set(this.brandArr)];
        },
     async showprchart2(prcode,platform){
        window['lastseeprcodedrchart']=prcode
        window['lastseeprcodedrchart1']= platform
        window['lastseeprcodedrchart2'] = this.filter.refundType
        this.drparamProCode=prcode
        this.dialogDrVisible=true
     } ,
     async initformparm(){
        let that=this;
         this.autoformparm.rule= [{type:'hidden',field:'id',title:'id',value: '',col:{span:12}},
                       {type:'select',field:'groupId',title:'组长',value:'', update(val, rule){{that.updateruleGroup(val)}},...await ruleDirectorGroup(),props:{filterable:true}},
                       {type:'InputNumber',field:'Profit3PredictRate',title:'毛三预估比例%',value: null,props:{min:0,precision:3},col:{span:6}},
                       {type:'InputNumber',field:'ShareRate',title:'公摊费率%',value: null,props:{min:0,precision:3},col:{span:6}},
                      ]
      },
      //系列编码远程搜索
      async remoteMethod(query){
        if (query !== ''){
            this.searchloading == true;
            this.options=[];
            setTimeout(async () => {
                const res = await getListByStyleCode({currentPage:1,pageSize:50, styleCode: query})
                console.log("系列编码远程搜索",res);
                this.searchloading = false
                res?.data?.forEach(f=>{
                this.options.push({value:f.styleCode,label:f.styleCode})
                });
            }, 200)
        }
        else{
            this.options = []
        }
      },
     async getShopList(){
      const res1 = await getAllShopList({ platforms: [2] });
      this.shopList = [];
      res1.data?.forEach(f => {
        if (f.isCalcSettlement && f.shopCode)
          this.shopList.push(f);
      });
      },
     async sortchange(column){
        if(!column.order)
          this.pager={};
        else
          this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
       await this.onSearch();
      },
      onRefresh(){
        this.onSearch()
      },
      async onSearch(){
      this.$refs.table.changecolumn_setTrue(["yearMonthDay"]);
      if(this.filter.groupType == 1 || this.filter.groupType == 2){
        this.$refs.table.changecolumn(["yearMonthDay"]);
      }
      this.$refs.pager.setPage(1);
      await this.getList().then(res=>{  });

      // loading.close();
    },
      async getList(){
        this.filter.startTime =null;
        this.filter.endTime =null;
      //   this.filter.listingStartTime = null;
      // this.filter.listingEndTime = null;
        if (this.filter.timerange) {
          this.filter.startTime = this.filter.timerange[0];
          this.filter.endTime = this.filter.timerange[1];
        }
      //   if (this.filter.timerange1) {
      //   this.filter.listingStartTime = this.filter.timerange1[0];
      //   this.filter.listingEndTime = this.filter.timerange1[1];
      // }
      // if(this.filter.starNumber!=null&&this.filter.endNumber!=null)
      // {
      //   if(this.filter.starNumber>this.filter.endNumber)
      //   {
      //     this.$message({ type: 'warning', message: '开始订单量值不能大于结束订单量值!' });
      //    return
      //   }
      // }
        //this.filter.styleCode = this.styleCode.join()
        var that=this;
        var pager = this.$refs.pager.getPager();
        const params = {...pager,...this.pager,...this.filter};
       // this.listLoading = true;
        startLoading();
        const res = await pageProductDayReport(params).then(res=>{
            loading.close();
            if(res?.data?.list&&res?.data?.list.length>0){
              for (var i in res.data.list) {
                if (!res.data.list[i].freightFee) {
                  res.data.list[i].freightFee =" ";
                }
               if(that.filter.refundType){
                res.data.list[i].RefundAmont = res.data.list[i].RefundAmontByPay;
                res.data.list[i].Profit3 = res.data.list[i].Profit3ByPay;
                res.data.list[i].Profit3Rate = res.data.list[i].Profit3RateByPay;
              }
              }
            }
            if(that.filter.refundType==true){
              res.data.summary.Profit3Rate_sum = res.data?.summary?.Profit3RateByPay_sum;
              res.data.summary.RefundAmontByPay = res.data?.summary?.RefundAmontByPay_sum;
              res.data.summary.Profit3ByPay = res.data?.summary?.Profit3ByPay_sum;
            }
            that.total = res.data?.total;
            that.financialreportlist = res.data?.list;
            that.$refs.table.loadRowEcharts();
            that.summaryarry=res.data?.summary;
        });
      },
      showFreightDetail(row){
          if(row.freightFee>=1){
            this.freightDetail.filter.proCode = row.proCode;
            if (row.yearMonthDay != null){
              var dayStr =row.yearMonthDay.substr(0,4)+'-'+row.yearMonthDay.substr(4,2)+'-'+row.yearMonthDay.substr(6,2)
              this.freightDetail.filter.timeRange = [dayStr,dayStr];
            }
            else {
              this.freightDetail.filter.timeRange = this.filter.timerange
            }
            this.freightDetail.visible=true;
            setTimeout(async () => {
              await this.$refs.freightDetail.onSearch();
            }, 100);
          }
      },
      showProcodesimilarity(row){
          if(row.styleCode!=null){
            this.$router.push({path: '/order/procodesimilarity', query: {styleCode: row.styleCode}})
          }
      },
      async showGiftDetail(row){
        var yearMonthDayStart=row.yearMonthDay
        var yearMonthDayEnd=row.yearMonthDay
        if(this.filter.groupType) {
          yearMonthDayStart= this.filter.timerange[0].replace("-","").replace("-","")
          yearMonthDayEnd= this.filter.timerange[1].replace("-","").replace("-","")
        }
        this.giftDetail.visible=true;
        let _th=this;
        await this.$nextTick(async () => {  await _th.$refs.ordergiftdetail.onShow(yearMonthDayStart,yearMonthDayEnd,row.proCode); });
      },
      selectchange:function(rows,row) {
        this.selids=[];
        rows.forEach(f=>{
          this.selids.push(f.id);
        })
      },
     onRefresh(){
          this.onSearch()
      },
    async updateruleGroup(groupid) {
       if(!groupid)
          this.autoformparm.fApi.resetFields()
       else{
         const res = await getParm({groupId:groupid})
         var arr = Object.keys(this.autoformparm.fApi);
         res.data.groupId=groupid;
         if(!res.data?.Profit3PredictRate) res.data.Profit3PredictRate=0;
         if(!res.data?.ShareRate) res.data.ShareRate=0;
         await this.autoformparm.fApi.setValue(res.data)
        }
      },
    async onstartImport(){
        this.dialogVisible=true;
      },
      uploadSuccess(response, file, fileList) {
        if (response.code == 200) {
        } else {
          fileList.splice(fileList.indexOf(file), 1);
        }
      },
      submitUpload() {
        if (!this.onimportfilter.yearmonthday) {
         this.$message({type: 'warning',message: '请选择年月!'});
         return;
        }
        this.uploadLoading=true
        this.$refs.upload.submit();
      },
     async uploadFile(item) {
       if(!item||!item.file||!item.file.size){
          this.$message({message: "请先上传文件", type: "warning" });
          return false;
        }
        const form = new FormData();
        form.append("upfile", item.file);
        form.append("platform", 10);
        form.append("yearmonthday", this.onimportfilter.yearmonthday);
        var res = await importProductDayReport(form);
        if (res.code==1) this.$message({message: "上传成功,正在导入中...", type: "success" });
        else this.$message({message: res.msg, type: "warning" });
        this.uploadLoading=false
      },
    async onExport(opt){
        this.filter.startTime =null;
        this.filter.endTime =null;
        if (this.filter.timerange) {
          this.filter.startTime = this.filter.timerange[0];
          this.filter.endTime = this.filter.timerange[1];
        }
        var pager = this.$refs.pager.getPager();
        const params = {  ...pager,   ...this.pager,   ...this.filter,...opt};
        var res= await exportProductOrderDayReport(params);
        if(!res?.data) {
           return
        }
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','拼多多店铺日报数据' +  new Date().toLocaleString() + '_.xlsx' )
        aLink.click()
      },
    async onShowEditParm(){
        this.editparmVisible = true
        const res = await getParm()
        var arr = Object.keys(this.autoformparm.fApi);
        if(arr.length >0)
           this.autoformparm.fApi.resetFields()
        await this.autoformparm.fApi.setValue(res.data)
      },
    async onSetEditParm(){
        this.editparmLoading=true;
        await this.autoformparm.fApi.validate(async (valid, fail) => {
        if(valid){
            const formData = this.autoformparm.fApi.formData();
            const res = await setParm(formData);
            if(res.code==1) this.editparmVisible=false;
          }else{}
       })
       this.editparmLoading=false;
      },
    showexpressfreightanalysis() {
        this.expressfreightanalysisVisible=true;
        this.$nextTick(() => { this.$refs.expressfreightanalysis.onSearch()});
      },
    renderCost(row){
      if(row.replaceSendCost >0){
        return "color:blue;cursor:pointer;";
      }
      else{
        return "";
      }
    },
    showupload() {
       this.drawervisible=true;
     }
     ,async callbackProCode(val) {
         // this.filter.proCode = val;
      },
    }
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>

