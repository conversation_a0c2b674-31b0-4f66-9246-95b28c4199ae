<template>
  <el-container style="height:100%;">
      <container v-loading="pageLoading" style="width:50%;">
        <ces-table ref="table" :that='that' :isIndex='false' :hasexpand='true' 
          :tableData='list' :tableCols='tableCols' :isSelection="false" @select="selectchange"
          :tableHandles='tableHandles' @cellclick="cellclick" :isSelectColumn="false"
          :loading="listLoading">
        </ces-table>
      </container>
      <!-- 组长信息编辑 -->
     <el-drawer :title="formtitle" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="addFormVisible1"
                                    direction="btt" size="'auto'" class="el-drawer__wrapper"  style="position:absolute;">
        <form-create :rule="autoform.rules" v-model="autoform.fApi" :option="autoform.options"/>
        <div class="drawer-footer">
          <el-button @click.native="addFormVisible1 = false">取消</el-button>
          <my-confirm-button type="submit"  :loading="addLoading" @click="onAddSubmitGroup" />
        </div>
    </el-drawer>
  </el-container>
</template>

<script>
import {getDirectorGroupAllEnabledList,updateDirectorGroupGzzb,GetDirectorGroupById} from "@/api/operatemanage/base/shop"
import {getUserListPage,getUser} from "@/api/admin/user"
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import MyConfirmButton from '@/components/my-confirm-button'
const tableCols1 =[
       {istrue:true,prop:'key',label:'序号', width:'60'},
       {istrue:true,prop:'value',label:'组长', width:'auto'},
          {istrue:true,prop:'wageRate',label:'工资占比(%)', width:'auto'},
         // ,permission:'api:operatemanage:shopmanager:UpdateDirectorGroupGzzb'
       {istrue:true,type:'button', width:'55',permission:'api:operatemanage:shopmanager:UpdateDirectorGroupGzzb' ,btnList:[{label:"编辑",display:(row)=>{return row.isHandle==true;},handle:(that,row)=>that.onEditDirectorGroup(row)}]},
     ];
const tableHandles1=[ ];
export default {
  name: 'roles',
  components: {cesTable,container,MyConfirmButton},
  props:{
    isHistory:false,
  },
  data() {
    return {
      that:this,
      filter: {
        combineCode:null,
        combineName:null,
        goodsCode:null
      },
      list: [],
      summaryarry:{},
      pager:{OrderBy:"combineCode",IsAsc:true},
      Director:{},
      tableCols:tableCols1,
      tableHandles:tableHandles1,
      dialogVisible: false,
      formtitle:"",
      total: 0,
      sels: [],
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addFormVisible1: false,
      addLoading: false,
      deleteLoading: false,
      formtitle:"编辑组员",
      fileList:[],
      selids:[],//选择的id
      detail:{
         pager:{OrderBy:"goodsCode",IsAsc:true},
         list: [],
        //  tableCols:tableCols2,
         listLoading: false,
         total:0,
         sels: [], 
      },
       autoform:{
               fApi:{},
               options:{submitBtn:false,form:{labelWidth: '145px'},
               global: {'*': {props: { disabled: false },col: {span: 8 }}},
                },
         },
    }
  },
  async mounted() {
    await this.getlist();
    await this.onInitautoform();
  },
  methods: {
     async onInitautoform() {
      let rules= [{type:'hidden',field:'id',title:'id',value: ''},
                 {type:'input',field:'userName',title:'名称'},
                 {type:'input',field:'wageRate',title:'工资占比'},
                ];          
      this.autoform.rules=rules
    },
   async remoteSearchUser(parm){
     if(!parm){
       return;
     }
     var dynamicFilter={field:'nickName',operator:'Contains',value:parm}
     var options=[];
     const res = await getUserListPage({currentPage:1,pageSize:50, dynamicFilter:dynamicFilter})
     res?.data?.list.forEach(f=>{
       options.push({value:f.id,label:f.nickName})
     })
    },
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    //分页查询
    async getlist() {
      this.listLoading = true
      var res= await getDirectorGroupAllEnabledList();

      this.listLoading = false
      if (!res?.success) return 
      const data = res.data;
      this.list = data
    },
    selsChange: function(sels) {
      this.sels = sels;
    },
    selectchange:function(rows,row) {
      this.selids=[];console.log(rows)
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
    cellclick(row, column, cell, event){
      if(column.property=='value'&&row.value)
      this.Director=row.key
       this.getDetailList(row.key);
    },
  async getDetailList(parentId) {
      const params = {groupid:parentId }
      var res = await GetDirectorAllList(params);
      this.listLoading = false;
      if (!res?.success) return 
      const data = res.data;
      res?.data?.forEach(f=>{
        f.groupId=parentId
      })
      this.detail.list = data
    },
  //编辑组长信息
  async onEditDirectorGroup(row){
    this.addFormVisible1=true
    var options=[];
    var res=null;
    if(row.key){  
      var _user= await getUser({id:row.key});
      res=await GetDirectorGroupById({id:row.key})
      options=[{value:_user?.id,label:_user?.nickName}];
    }
    var model={id:row.key,userName:row.value,groupId:!row.groupId?'':row.groupId,nickName:_user?.nickName,enabled:res.data.enabled,wageRate:res.data.wageRate}
    var arr = Object.keys(this.autoform.fApi);
    if(arr.length >0) {
        
        await this.autoform.fApi.setValue(model)
        await this.autoform.fApi.disabled(true, 'userName')
        await this.autoform.fApi.sync('userName')
        await this.autoform.fApi.sync('wageRate')
      }
    this.formtitle="编辑组长";
  },
   //修改组长信息
   async onAddSubmitGroup(){
      this.addLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          formData.id=formData.id?formData.id:0;
          //formData.Enabled=true;
          const res = await updateDirectorGroupGzzb(formData);
          if(res.code==1){
            this.getlist();
            this.addFormVisible1=false;
          }
        }else{
          //todo 表单验证未通过
        }
     })
     this.addLoading=false;
    },
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary{
  margin-right: 7px;
}
</style>
