<template>
  <div>
    <vxe-table border resizable height="300px" show-overflow="false" ref="xTable" :loading="loading" :data="tabledataget"
      :edit-config="{ trigger: 'manual', mode: 'row' }">
      <vxe-column type="seq" width="60"></vxe-column>
      <vxe-column field="orderNo" width="180" title="线上订单号">
      </vxe-column>
      <vxe-column field="afterSaleApproveDate" title="售后发起时间" :edit-render="{}">
        <template #default="{ row }">
          <el-date-picker v-model="row.afterSaleApproveDate" type="date" style="width: 135px" placeholder="售后发起时间"
            format="yyyy-MM-dd" value-format="yyyy-MM-dd">
          </el-date-picker>
          <!-- <vxe-input v-model="row.afterSaleApproveDate" type="date" placeholder="售后发起时间" transfer></vxe-input> -->
        </template>
      </vxe-column>
      <vxe-column field="platformName" title="平台" :edit-render="{}" width="100">
        <template #default="{ row }">
          <el-select class="marginleft" v-model="row.platform" clearable :popper-append-to-body="false" placeholder="平台" filterable @change="clearrow(row,1)"
            style="width: 85px">
            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <!-- <vxe-input v-model="row.platformName" type="text" placeholder="平台"></vxe-input> -->
        </template>
      </vxe-column>
      <vxe-column field="shopName" title="店铺" :edit-render="{}" width="140">
        <template #default="{ row }">
          <!-- <el-select filterable clearable v-model="row.shopName" placeholder="所属店铺" style="width: 200px" @change="zrType2change()">
          <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"></el-option>
      </el-select> -->
          <el-select class="marginleft" v-model="row.shopCode" clearable placeholder="所属店铺" :popper-append-to-body="false" @focus="onchangeplatform(row)" @change="shopCodechange(row)"
            filterable style="width: 125px">
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
          </el-select>
          <!-- <vxe-select v-model="row.sex2" multiple transfer>
          <vxe-option v-for="item in sexList" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
        </vxe-select> -->
        </template>
      </vxe-column>
      <!-- <vxe-column field="goodsCode" title="商品编码" :edit-render="{}">
        <template #default="{ row }">
          <el-input v-model="row.goodsCode" type="text" placeholder="平台"></el-input>
        </template>
      </vxe-column> -->
      <vxe-column field="goodsCode" title="商品编码" width="110">
      </vxe-column>
      <vxe-column field="damagedGoodsCount" title="损耗商品数量" :edit-render="{}" width="130">
        <template #default="{ row }">
          <el-input-number v-model="row.damagedGoodsCount" controls-position="right" :min="0" style="width: 105px;" :max="99999" :step="1" @input="validateInput($event, row)"></el-input-number>

        </template>
      </vxe-column>
      <vxe-column field="damagedAmount" title="损耗金额" :edit-render="{}" width="100">
        <template #default="{ row }">
          <el-input-number v-model="row.damagedAmount" controls-position="right" style="width: 85px;" :min="0" :step="0.001" :max="99999.999"></el-input-number>
          <!-- <el-input v-model="row.damagedAmount" min="1" step="1" type="number" style="width: 85px;" placeholder="请输入数值"></el-input> -->
        </template>
      </vxe-column>
      <vxe-column field="orgZrDepartment" title="责任部门" :edit-render="{}" width="100">
        <template #default="{ row }">
          <el-select class="marginleft" v-model="row.orgZrDepartment" clearable placeholder="责任部门(大类)"
          :popper-append-to-body="false" filterable style="width: 85px;" @change="clearrow(row, 2)">
            <el-option v-for="item in damagedList" :key="item" :label="item" :value="item" />
          </el-select>
          <!-- <el-select class="marginleft" v-model="row.orgZrDepartment"    clearable placeholder="责任部门(大类)" style="width: 200px;" @change="getZrType(row.orgZrDepartment)"  >
        <el-option v-for="item in damagedList" :key="item" :label="item" :value="item"  />
      </el-select> -->
          <!-- <vxe-select v-model="row.orgZrDepartment" multiple transfer>
          <vxe-option v-for="item in sexList" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
        </vxe-select> -->
        </template>
      </vxe-column>
      <vxe-column field="orgZrType2" title="责任类型" :edit-render="{}" width="185">
        <template #default="{ row }">
          <el-select class="marginleft" v-model="row.orgZrType2" clearable placeholder="责任类型(细类)" @focus="getZrType(row)" style="width: 160px;"
          :popper-append-to-body="false" filterable @change="zrType2change">
            <el-option v-for="item in damagedList2" :key="item" :label="item" :value="item" />
          </el-select>
          <!-- <el-select class="marginleft" v-model="row.orgZrType2"    clearable placeholder="责任类型(细类)" style="width: 200px;" @change="zrType2change()" >
        <el-option v-for="item in damagedList2" :key="item" :label="item" :value="item" />
      </el-select> -->

          <!-- <vxe-select v-model="row.orgZrType2" multiple transfer>
          <vxe-option v-for="item in sexList" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
        </vxe-select> -->
        </template>
      </vxe-column>
      <vxe-column field="orgZrUserName" title="责任人" :edit-render="{}" width="90">
        <template #default="{ row }">
          <!-- <vxe-input v-model="row.orgZrUserName" type="text" placeholder="请输入责任人"></vxe-input> -->
          <!-- <vxe-select v-model="row.orgZrUserName" multiple transfer>
          <vxe-option v-for="item in sexList" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
        </vxe-select> -->
          <template v-if="row.orgZrDepartment">
            <el-select v-model="row.memberName" @change="newMemberIdChange($event, row)" filterable clearable
              style="width: 75px;" :popper-append-to-body="false" v-if="row.orgZrDepartment === '采购'">
              <el-option v-for="item in brandlist" :key="item.key" :value="item.key" :label="item.value"></el-option>
            </el-select>
            <el-select v-model="row.memberName" @change="newMemberIdChange($event, row)" filterable clearable
              style="width: 75px;" :popper-append-to-body="false" v-else-if="row.orgZrDepartment === '运营'">
              <el-option v-for="item in directorList" :key="item.key" :value="item.key" :label="item.value"></el-option>
            </el-select>
            <!-- <YhUserelector v-else-if="row.orgZrDepartment === '客服' || row.orgZrDepartment === '仓库'" style="width: 75px;"
              @change="customerserviceStash($event, row)" :keyy="row.memberDDUserId" :value.sync="row.memberDDUserId" :text.sync="row.memberName">
            </YhUserelector> -->
            <div v-else-if="row.orgZrDepartment === '客服' || row.orgZrDepartment === '仓库'">
                <div v-if="!row.isclick&&row.orgZrUserName" @click="isclickrow(row)">{{row.orgZrUserName}}</div>
                <el-select v-else v-model="row.memberDDUserId" style="width: 75px;" clearable filterable remote  reserve-keyword 
                    :remote-method="remoteMethod" @clear="clear" :loading="selloading" @blur="namechange(row)">
                    <el-option v-for="item in options" :key="'userSelector'+item.value+ item.extData.defaultDeptId" :label="item.label" :value="item.value">
                        <span>{{item.label}}</span>
                        <span  style=" color: #8492a6; ">({{item.extData.position}},{{item.extData.empStatusText}}{{item.extData.jstUserName ? ","+item.extData.jstUserName:""}})</span>           
                        <span style=" color: #8492a6; "> {{item.extData.deptName}}</span>
                    </el-option>
                </el-select>
            </div>
            
            <el-input v-else v-model="row.orgZrUserName" type="text" maxlength="6" @input="changesel" placeholder="请输入"
              style="width: 75px;"></el-input>
            <!-- {{row.memberDDUserId}}___{{row.memberName}} -->
          </template>
          <!-- <vxe-input v-else v-model="row.memberName" type="text" @input="changesel" placeholder="请输入"></vxe-input> -->
        </template>
      </vxe-column>
      <vxe-column field="damagedReason" title="损耗具体原因" :edit-render="{}" width="140">
        <template #default="{ row }">
          <el-input v-model="row.damagedReason" type="text" maxlength="30" placeholder="平台" style="width: 125px;"></el-input>
        </template>
      </vxe-column>

      <vxe-column field="fileUrls" title="定责资料" :edit-render="{}">
        <!-- <template #default="{ row }">
          <el-image slot="reference" style="width: 50px; height: 50px" :src="row.fileUrls[0]"
            :preview-src-list="row.fileUrls"></el-image>
        </template> -->
        <template #default="{ row }">
          <el-button size="mini" @click="tocreateimg(row)"><i class="el-icon-picture-outline"></i></el-button>
        </template>
      </vxe-column>
      <!-- <vxe-column title="操作" width="160">
        <template #default="{ row }">
          <template v-if="$refs.xTable.isActiveByRow(row)">
            <vxe-button @click="saveRowEvent(row)">保存</vxe-button>
            <vxe-button @click="cancelRowEvent(row)">取消</vxe-button>
          </template>
          <template v-else>
            <vxe-button @click="editRowEvent(row)">编辑</vxe-button>
          </template>
        </template>
      </vxe-column> -->
    </vxe-table>
  </div>
</template>

<script>
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser'
import VXETable from "vxe-table";
import { getDamagedOrdersZrDept, getDamagedOrdersZrType, deleteDamagedOrderBefore, editDamagedOrders } from '@/api/customerservice/DamagedOrders'
import { getAllListInCalc as getAllShopList, getList as getshopListt } from '@/api/operatemanage/base/shop';
import YhUserelector from '@/components/YhCom/yh-userselector.vue'
import { getAllWarehouse, getAllProBrand } from '@/api/inventory/warehouse'
import {
  getDirectorList,
  getDirectorGroupList,
  getProductBrandPageList,
  getList as getshopList,
} from "@/api/operatemanage/base/shop";

export default {
  props: ['tabledata'],
  components: { YhUserelector },
  data() {
    return {
      selloading: false,
      pictureupload: false,
      options: [],
      picFileList: [],
      formEditMode: true,//是否编辑模式
      brandlist: [],
      directorList: [],
      loading: false,
      tabledataget: [],
      shopList: [],
      newWareHouseList: [],
      damagedList: [],
      damagedList2: [],
      form: {
        newMemberName: "",
        newMemberId: null,
        orderList: null,
        newMemberDDUserId: "",
        newMemberDDUserName: ""
      },
      sexList: [
        { label: "", value: "" },
        { label: "男", value: "1" },
        { label: "女", value: "0" },
      ],
      platformList: [{ label: '天猫', value: 1 }, { label: '拼多多', value: 2 }, { label: '阿里巴巴', value: 4 },
      { label: '抖音', value: 6 }, { label: '京东', value: 7 }, { label: '淘工厂', value: 8 }, { label: '淘宝', value: 9 }, { label: '苏宁', value: 10 },],
    };
  },
  watch: {
    "tabledata": {
      handler(val) {
        this.tabledataget = this.tabledata;
      },
      immediate: true
    }
  },
  async mounted() {
    // this.tabledataget.map((item) => {
    //   item.disabled = false;
    // })
    this.getZrDept();
    this.getDirectorlist();
    this.onchangeplatform();
    await this.setBandSelect();

  },
  // filters:{

  // },
  methods: {
    namechange(row){
      // this.tabledataget[this.tabledataget.indexOf(row)].isclick = false;
      // this.$forceUpdate();
    },
    isclickrow(row){
      this.tabledataget[this.tabledataget.indexOf(row)].isclick = true;
      this.$forceUpdate();
    },
    async remoteMethod(query) {
        this.selloading = true;
        if (query !== '') {                  
            let rlt= await QueryAllDDUserTop100({ keywords: query });
            if (rlt && rlt.success) {
                this.options = rlt.data?.map(item => {
                    return { label: item.userName, value: item.ddUserId, extData:item }
                });
            }
        } else {
            this.options = [...this.orgOptions];
        }
        this.selloading = false;
    },
    clear(){
        this.$emit("update:value", null);
        this.$emit("update:text", null);
        this.$emit("change", null);
    },
    clearrow(row,index){
      if(index==1){
        this.tabledataget[this.tabledataget.indexOf(row)].shopCode = ""
      }else if(index==2){
        this.tabledataget[this.tabledataget.indexOf(row)].orgZrType2 = ""

        this.tabledataget[this.tabledataget.indexOf(row)].orgZrUserName = ""
        this.tabledataget[this.tabledataget.indexOf(row)].memberDDUserId = ""
        this.tabledataget[this.tabledataget.indexOf(row)].memberName = ""


      }
    },
    validateInput(value, row) {
      if (value.indexOf('.') >= 0) {
        row.damagedGoodsCount = parseInt(value.replace(/[^\d]/g, ''), 10);
      } else {
        row.damagedGoodsCount = value;
      }
    },
    async shopCodechange(row) {
      this.shopList.forEach((item) => {
        if (item.shopCode === row.shopCode) {
          row.shopName = item.shopName;
        }
      });
    },
    customerserviceStash(row) {
      console.log(row, 'row');
    },
    //图片上传
    tocreateimg(row) {
      this.$emit('tocreateimgbilitytable', this.pictureupload = true, row);
    },
    async personincharge(name, pictureid) {
      console.log(this.tabledataget, 'this.tabledataget');
      console.log(name, 'name');
      console.log(pictureid, 'pictureid');
    },
    async setBandSelect() {
      var res = await getAllProBrand();
      if (!res?.success) return;
      this.brandlist = res.data;
    },
    async getDirectorlist() {
      const res1 = await getDirectorList({});
      const res2 = await getDirectorGroupList({});
      this.directorList = res1.data;
      this.directorGroupList = [{ key: "0", value: "未知" }].concat(
        res2.data || []
      );
    },
    changesel() {
      this.$forceUpdate();
    },
    newMemberIdChange(e, row) {
      //采购
      this.brandlist.forEach((item) => {
        if (e === item.key) {
          row.memberId = item.key
          row.orgZrUserName = item.value
          row.memberName = row.orgZrUserName
          return row.orgZrUserName
        }
      });
      //运营
      this.directorList.forEach((item) => {
        if (e === item.key) {
          row.memberId = item.key
          row.orgZrUserName = item.value
          row.memberName = row.orgZrUserName
          return row.orgZrUserName
        }
      });
      this.changesel();
    },
    async detaileditsave() {
      let ajkdhks = []
      ajkdhks = this.tabledata.map(singleObject => {
        const { orgZrDepartment, orgZrType2, orgZrUserName, memberDDUserId, memberName, memberId, ...rest } = singleObject;
        singleObject = {
          zrDepartment: orgZrDepartment,
          zrType2: orgZrType2,
          newMemberDDUserId: memberDDUserId,
          newMemberName: memberName,
          newMemberName: orgZrUserName,
          newMemberId: memberId,
          ...rest
        };
        if (Array.isArray(singleObject.fileUrls)) {
          singleObject.fileUrls = singleObject.fileUrls.join(',');
        }
        return singleObject;
      });
      ajkdhks.forEach(item => {
        if (item.zrDepartment === "运营" || item.zrDepartment === "采购") {
          delete item.newMemberDDUserId;
          delete item.zrUserName;
        }
        if (item.zrDepartment === "客服" || item.zrDepartment === "仓库") {
          delete item.newMemberId;
          delete item.zrUserName;
        }
      });
      const { data, success } = await editDamagedOrders(ajkdhks)
      if (success) {
        this.$message({ message: "保存成功", type: "success" });
        this.$emit('update-templatepageclose');
      }
    },
    platidtoname(name) {
      if (typeof name === 'number') {
        // this.platformList.filter(item=>item.value == name).label
        let matchingLabels = this.platformList.filter(item => item.value === name).map(item => item.label);
        let text = matchingLabels.join(', ');
        return text;
      } else {
        return name;
      }
    },
    shopidtoname(name) {
      if (Number(name) !== NaN && isFinite(Number(name))) {
        // return this.shopList.filter(item=>item.shopCode == name).shopName;
        let shopidtoLabels = this.shopList.filter(item => item.shopCode === name).map(item => item.shopName);
        let shopidtotext = shopidtoLabels.join(', ');
        return shopidtotext;
      } else {
        return name
      }
    },
    //责任部门
    async getZrType(row) {
      row.orgZrType2 = '';
      row.orgZrUserName = '';
      row.memberName = '';
      row.memberDDUserId = '';
      let res = await getDamagedOrdersZrType(row.orgZrDepartment);
      this.damagedList2 = res.data;
      // this.tabledataget.map((item) => {
      //   if (item.orderNo == row.orderNo) {
      //     item.disabled = false;
      //   } else {
      //     item.disabled = true;
      //   }
      // })
      this.$forceUpdate();
      // this.filter.zrType2=""
      // this.changebtn();
    },
    async getZrDept() {
      let res = await getDamagedOrdersZrDept();
      this.damagedList = res?.data;

      var res1 = await getAllWarehouse();
      this.newWareHouseList = res1.data.filter((x) => { return x.name.indexOf('代发') < 0; });
      // damagedList2
    },
    zrType2change() {
      this.$forceUpdate();
    },
    async onchangeplatform(row) {
      if (row !== undefined) {
        row.shopCode = '';
      }
      const res1 = await getshopListt({ platform: row?.platform, CurrentPage: 1, PageSize: 100000 });
      this.shopList = res1.data.list;

    },
    editRowEvent(row) {
      this.formEditMode = false
      const $table = this.$refs.xTable;
      $table.setActiveRow(row);
    },
    saveRowEvent(row) {
      const $table = this.$refs.xTable;
      $table.clearActived().then(() => {
        this.loading = true;
        setTimeout(() => {
          this.loading = false;
          VXETable.modal.message({ content: "保存成功！", status: "success" });
        }, 300);
      });
    },
    cancelRowEvent(row) {
      const $table = this.$refs.xTable;
      $table.clearActived().then(() => {
        // 还原行数据
        $table.revertData(row);
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
