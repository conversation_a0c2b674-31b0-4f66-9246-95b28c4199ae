<template>
    <MyContainer>
        <div ref="oneboxx" id="oneboxx">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="150px" style="padding:  20px;"
                :disabled="isView" v-loading="loading">
                <el-form-item label="审批人:" prop="approvalUserName">
                    <YhUserelector :value.sync="ruleForm.approvalUserId" maxlength="50" @update:value="updateValue"
                        :text.sync="ruleForm.approvalUserName" style="width:80%;" placeholder="请输入审批人">
                    </YhUserelector>
                </el-form-item>
                <el-form-item label="审批人组长:" prop="appGrouperName">
                    <el-input v-model="ruleForm.appGrouperName" placeholder="审批人组长" style="width: 100%" clearable
                        maxlength="50" disabled />
                </el-form-item>
                <el-form-item label="图片:" prop="pictures">
                    <uploadimgFile v-if="editPriceVisible" ref="uploadimgFile" :disabled="isView" :ispaste="!isView"
                        :noDel="isView" :accepttyes="accepttyes" :isImage="true" :uploadInfo="chatUrls" :keys="[1, 1]"
                        @callback="getImg" :imgmaxsize="9" :limit="9" :multiple="true">
                    </uploadimgFile>
                </el-form-item>
                <el-form-item label="涨价原因:" prop="hikeReason">
                    <el-input v-model="ruleForm.hikeReason" placeholder="涨价原因" style="width: 400px;" clearable
                        maxlength="100" />
                </el-form-item>
                <!-- 表格部分 -->
                <el-button type="text" @click="addtTableProps" v-if="!isView">新增一行</el-button>
                <el-button type="text" @click="batchAddProps" v-if="!isView">批量新增</el-button>
                <el-button type="text" @click="tocreateimg" :disabled="false">生成图片</el-button>
                <el-table :data="ruleForm.items" style="width: 100%;margin-bottom: 20px" max-height="250"
                    v-loading="tableLoading">
                    <el-table-column type="index" width="50" />
                    <el-table-column prop="goodsCode" label="商品编码" width="180">
                        <template #default="{ row, $index }">
                            <el-input v-model="row.goodsCode" placeholder="商品编码" style="width: 100%" clearable
                                maxlength="50" @blur="searchGoodsName(row.goodsCode, $index)" @clear="clearInfo($index)"
                                @change="changeGoodsCode($index)" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="goodsName" label="商品名称" width="180">
                        <template #default="{ row }">
                            <el-input v-model="row.goodsName" placeholder="商品名称" style="width: 100%" clearable disabled
                                maxlength="50" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="prevPrice" label="原价">
                        <template #default="{ row }">
                            <el-input-number v-model="row.prevPrice" placeholder="原价" style="width: 100%" :max="999999"
                                :precision="4" clearable :controls="false" disabled />
                        </template>
                    </el-table-column>
                    <el-table-column prop="price" label="现价">
                        <template #default="{ row }">
                            <el-input-number v-model="row.price" placeholder="现价" style="width: 100%" :max="999999"
                                :precision="4" clearable :controls="false" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="price" label="进货数量">
                        <template #default="{ row }">
                            <div style="display: flex;">
                                <el-input-number v-model="row.quantity" placeholder="进货数量" style="width: 100%"
                                    :max="9999999" :precision="0" clearable :controls="false" />
                                <el-button type="text" @click="bulkOperations(row.quantity)"
                                    v-if="!isView">批量</el-button>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="" label="操作" width="70" v-if="!isView">
                        <template #default="{ row, $index }" v-if="!isView">
                            <el-button type="danger" @click="ruleForm.items.splice($index, 1)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-form-item class="btnCss" v-if="!isView">
                    <el-button type="info" @click="close">关闭</el-button>
                    <el-button type="primary" @click="submitForm('ruleForm', false)" v-throttle="2000">保存</el-button>
                    <el-button type="primary" @click="submitForm('ruleForm', true)" v-throttle="5000">保存并提交</el-button>
                </el-form-item>
                <el-timeline v-if="activities.length > 0 && !isReInitiated">
                    <el-timeline-item v-for="(activity, index) in activities" :key="index"
                        :timestamp="activity.approvalTime">
                        {{ activity.approvalUserName + '-' + activity.approvalStatus + (activity.refuseRemark ? '-' +
                            activity.refuseRemark : '') }}
                    </el-timeline-item>
                </el-timeline>
            </el-form>
        </div>

        <el-dialog title="批量新增" :visible.sync="goodsVisable" width="12%" :close-on-click-modal="false" v-dialogDrag
            :modal="false">
            <inputYunhan ref="productCode" :inputt.sync="goodsCode" v-model="goodsCode" width="200px"
                v-if="goodsVisable" placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300"
                :maxlength="6000" @callback="productCodeCallback" title="商品编码">
            </inputYunhan>
            <div class="btnGroup">
                <el-button type="primary" @click="closeGoodsLog" style="margin-right: 10px;">取消</el-button>
                <el-button type="primary" @click="searchGoodsCode">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import YhUserelector from '@/components/YhCom/yh-userselector.vue'
import { getMyGoodsByCode, } from '@/api/order/tailorloss';
import { mergePurchaseHikeApply, getManagerInfo, getCostPrice, getGoodsPrevPriceList } from '@/api/inventory/purchaseHike'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import html2canvas from 'html2canvas'
import inputYunhan from "@/components/Comm/inputYunhan";
import {
    //分页查询店铺商品资料
    getList,
    //导入
    importData,
} from "@/api/inventory/basicgoods"
export default {
    name: "addOrEditProps",
    components: {
        MyContainer, YhUserelector, uploadimgFile, inputYunhan
    },
    props: {
        formData: {
            type: Object,
            default: () => null
        },
        isEdit: {
            type: Boolean,
            default: false
        },
        auditProps: {
            type: Object,
            default: () => null
        },
        isView: {
            type: Boolean,
            default: false
        },
        isReInitiated: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            ruleForm: {
                id: null,
                approvalUserId: null,//审批人
                approvalUserName: null,//审批人
                appGrouperId: null,//审批人组长
                appGrouperName: null,   //审批人组长
                hikeReason: null,//涨价原因
                pictures: null,
                items: [],
                sendApply: false
            },
            activities: [],
            rules: {
                approvalUserName: [
                    { required: true, message: '请选择审批人', trigger: 'blur change' },
                ],
                appGrouperName: [
                    { required: true, message: '请输入审批人组长', trigger: 'change blur' }
                ],
                seriesName: [
                    { type: 'date', required: true, message: '请输入降价编码', trigger: 'change blur' }
                ],
                hikeReason: [
                    { required: true, message: '请输入涨价原因', trigger: 'change blur' }
                ],
            },
            editPriceVisible: false,
            formEditMode: true,
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            chatUrls: [],
            loading: false,
            goodsVisable: false,
            goodsCode: null,
            tableLoading: false
        }
    },
    mounted() {
        this.ruleForm = this.isEdit ? this.formData : this.ruleForm
        if (this.isReInitiated) {
            this.ruleForm.id = null
        }
        this.activities = this.auditProps
        if (this.ruleForm.pictures) {
            this.chatUrls = this.ruleForm.pictures.split(',').map((item, i) => {
                return {
                    url: item,
                    name: `聊天截图${i + 1}`
                }
            })
        }
        this.editPriceVisible = true
    },
    methods: {
        async searchGoodsCode() {
            if (!this.goodsCode) return this.$message.error('请输入商品编码')
            this.tableLoading = true
            if (this.goodsCode) {
                const { data, success } = await getGoodsPrevPriceList({ goodsCodes: this.goodsCode })
                if (success && data && data.length > 0) {
                    const res = [...this.ruleForm.items, ...data]
                    const goodsCodeArr = res.map(item => item.goodsCode)
                    //找出goodsCodeArr中的重复项,并取出商品编码
                    const goodsCodeArrRepeat = goodsCodeArr.filter((item, index) => goodsCodeArr.indexOf(item) !== index).join(',')
                    //删除res中的重复项的最后一个
                    if (goodsCodeArrRepeat) {
                        this.ruleForm.items = res.filter((item, index) => {
                            if (goodsCodeArr.indexOf(item.goodsCode) !== index) {
                                return false
                            }
                            return true
                        })
                        this.$message.error(`商品编码${goodsCodeArrRepeat}有重复项,已自动删除`)
                    } else {
                        this.ruleForm.items = res
                    }
                } else {
                    this.$message.error('未查询到数据')
                }
                this.goodsVisable = false
                this.tableLoading = false
            }
        },
        closeGoodsLog() {
            this.goodsVisable = false
            this.goodsCode = null
        },
        //商品编码数据
        async productCodeCallback(val) {
            this.goodsCode = val;
        },
        batchAddProps() {
            this.goodsCode = null
            this.goodsVisable = true
        },
        tocreateimg() {
            html2canvas(document.querySelector('#oneboxx'), {
                allowTaint: true,
                useCORS: true,
            }).then(canvas => {
                const imgData = canvas.toDataURL('image/png')
                const img = new Image()
                img.src = imgData
                const a = document.createElement('a')
                a.href = imgData
                a.download = `涨价单审批${new Date().getTime()}.png`
                a.click()
            })
        },
        getImg(data) {
            if (data) {
                this.chatUrls = data
                this.ruleForm.pictures = data.map(item => item.url).join(',')
            }
        },
        changeGoodsCode(i) {
            //判断有没有相同的goodsCode
            const goodsCodeArr = this.ruleForm.items.filter(item => (item.goodsCode != null && item.goodsCode != '' && item.goodsCode != undefined)).map(item => item.goodsCode)
            const goodsCodeSet = new Set(goodsCodeArr)
            if (goodsCodeArr.length !== goodsCodeSet.size) {
                //遍历当前项,清空所有值
                this.ruleForm.items.forEach((item, index) => {
                    if (index === i) {
                        item.goodsCode = null
                        item.goodsName = null
                        item.prevPrice = null
                        item.price = null
                        item.quantity = null
                    }
                })
                return this.$message.error(`第${i + 1}条数据商品编码有重复项,请检查`)
            }
        },
        clearInfo(i) {
            this.ruleForm.items[i].goodsCode = null
            this.ruleForm.items[i].goodsName = null
            this.ruleForm.items[i].prevPrice = null
        },
        async updateValue() {
            const { data, success } = await getManagerInfo({ id: this.ruleForm.approvalUserId })
            if (success) {
                this.ruleForm.appGrouperName = data.userName
                this.ruleForm.appGrouperId = data.ddUserId
            }
        },
        bulkOperations(quantity) {
            this.$confirm('此操作将批量操作进货数量, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.ruleForm.items.forEach(item => {
                    item.quantity = quantity
                })
                this.$message({
                    type: 'success',
                    message: '操作成功!'
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
        },
        async submitForm(formName, sendApply) {
            this.ruleForm.sendApply = sendApply
            if (!this.ruleForm.approvalUserName && !this.ruleForm.approvalUserId) {
                return this.$message.error('请选择审批人')
            }
            if (!this.ruleForm.appGrouperName && !this.ruleForm.appGrouperId) {
                return this.$message.error('请输入审批人组长')
            }
            if (!this.ruleForm.hikeReason) {
                return this.$message.error('请输入涨价原因')
            }
            this.ruleForm.items.forEach((item, index) => {
                if (!item.goodsCode || !item.goodsName || (item.prevPrice < 0 || item.prevPrice === null || item.prevPrice === undefined) || (item.price < 0 || item.price === null
                    || item.price === undefined) || (item.quantity <= 0 || item.quantity === null || item.quantity === undefined)) {
                    this.$message.error(`第${index + 1}条数据有误`)
                    throw new Error(`第${index + 1}条数据有误`)
                }
                if (item.price <= item.prevPrice) {
                    this.$message.error(`第${index + 1}条数据现价小于等于原价`)
                    throw new Error(`第${index + 1}条数据现价小于等于原价`)
                }
            })
            //判断有没有相同的goodsCode
            const goodsCodeArr = this.ruleForm.items.filter(item => (item.goodsCode != null || item.goodsCode != '' || item.goodsCode != undefined)).map(item => item.goodsCode)
            const goodsCodeSet = new Set(goodsCodeArr)
            if (goodsCodeArr.length !== goodsCodeSet.size) {
                this.$message.error('商品编码有重复项,请检查')
                return
            }
            if (this.ruleForm.hasOwnProperty('isSubmit')) {
                delete this.ruleForm.isSubmit
            }
            const { success } = await mergePurchaseHikeApply(this.ruleForm)
            if (success) {
                this.$message.success('操作成功')
                this.$emit('close')
                this.$emit('getList')
                this.$emit('closeTabMethod',this.ruleForm)
            } else {
                this.$message.error('操作失败')
            }
        },
        async searchGoodsName(e, i) {
            //去掉空格
            if (e) {
                e = e.replace(/\s+/g, "")
                const params = {
                    currentPage: 1,
                    pageSize: 50,
                    orderBy: null,
                    isAsc: false,
                    goodsCode: e
                }
                const { data, success } = await getList(params)
                if (success) {
                    if (data.list.length > 0) {
                        this.ruleForm.items[i].goodsName = data.list[0].goodsName ? data.list[0].goodsName : ''
                        // this.ruleForm.items[i].prevPrice = data.list[0].costPrice ? data.list[0].costPrice : ''
                        const res = await getCostPrice(params)
                        this.ruleForm.items[i].prevPrice = res.data ? res.data : ''
                    } else {
                        this.ruleForm.items[i].goodsName = null
                        this.ruleForm.items[i].prevPrice = null
                    }
                }
            } else {
                this.ruleForm.items[i].goodsName = null
                this.ruleForm.items[i].prevPrice = undefined
                this.ruleForm.items[i].price = undefined
                this.ruleForm.items[i].quantity = undefined
            }
        },
        close() {
            this.$emit('close')
        },
        addtTableProps() {
            this.ruleForm.items.push({ goodsCode: null, goodsName: null })
        }
    }
}
</script>

<style scoped lang="scss">
.btnCss {
    margin-top: 40px;
    display: flex;
    justify-content: flex-end;
}

.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}
</style>
