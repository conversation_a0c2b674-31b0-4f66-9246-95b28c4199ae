<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      :tableData='inquirsstatisticslist' :summaryarry="summaryarry" @select='selectchange' :isSelection='false' :tableCols='tableCols'
      :loading="listLoading">
      <el-table-column type="expand">
        <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <template slot='extentbtn'>
        <el-button-group>
           <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="Filter.groupNameList"  placeholder="分组"  filterable multiple clearable collapse-tags>
                            <el-option v-for="item in filterGroupList" :key="item" :label="item" :value="item"></el-option>
                        </el-select>
                    </el-button>
          <!-- <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="Filter.Groupname" placeholder="组名称" style="width:120px;" />
          </el-button> -->
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="Filter.sname" placeholder="姓名" style="width:120px;"  clearable :maxlength="10"/>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <datepicker v-model="Filter.sdate"></datepicker>
          </el-button>
          <!-- <el-button type="text" size="medium" disabled style="color: red;">周转对比:</el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input-number v-model="filter1.startdiff"></el-input-number>至<el-input-number v-model="filter1.enddiff"></el-input-number>
            </el-button> -->
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport" style="margin-left: 10px;" v-if="checkPermission(['api:Customerservice:KuaiShouInquirs:ExportKuaiShouPersonalEfficiencyList1'])">导出</el-button>

        </el-button-group>
      </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" @get-page="getinquirsstatisticsList" />
    </template>

    <el-dialog title="个人效率按店统计" :visible.sync="dialogVisibleSyj" width="65%" :close-on-click-modal="false" v-dialogDrag>
      <span>
        <inquirsstatisticsbyshop v-if="dialogVisibleSyj" ref="inquirsstatisticsbyshop" style="height: 600px;" />
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyj = false">关闭</el-button>
      </span>
    </el-dialog> 

    <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
    :close-on-click-modal="false" v-dialogDrag>
      <div>
        <span>
          <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>
import inquirsstatisticsbyshop from '@/views/customerservice/kuaishou/personshopinquirs'

 
import { getInquirsStatisticsList, getInquirsStatisticsListMap, exportInquirsStatisticsList } from '@/api/customerservice/groupinquirsstatistics'

import {
  getKuaiShouPersonalEfficiencyPageList,
  getKuaiShouPersonalEfficiencyChat,
  getKuaiShouGroup,
  exportKuaiShouPersonalEfficiencyList
} from '@/api/customerservice/kuaishouinquirs'


import datepicker from '@/views/customerservice/datepicker'
import buschar from '@/components/Bus/buschar'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

import { GetGroupNameList } from '@/api/customerservice/group'


const tableCols = [
  { istrue: true, prop: 'groupName', label: '组名称', width: '150', sortable: 'custom'},
  { istrue: true, prop: 'sname', label: '姓名', width: '100', type: "click",handle: (that, row, column, cell) => that.canclick(row, column, cell) },
  { istrue: true, prop: 'inquirs', label: '咨询人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'inquireCount', label: '咨询人次', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'artificialConversation', label: '人工会话量', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'perCapitaReception', label: '人均接待量', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'conversation3MinRate', label: '三分钟回复率（会话）', width: '100', sortable: 'custom', formatter: (row) => (row.conversation3MinRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'customerService3MinRate', label: '三分钟回复率（人维度）', width: '100', sortable: 'custom', formatter: (row) => (row.customerService3MinRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'noServiceRate', label: '不服务率', width: '80', sortable: 'custom', formatter: (row) => (row.noServiceRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'artificialAvgReply', label: '人工平均回复时长', width: '130', sortable: 'custom' },
    { istrue: true, prop: 'conversationNiceCommentRate', label: '好评率（会话）', width: '130', sortable: 'custom', formatter: (row) => (row.conversationNiceCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'niceCommentRate', label: '好评率（人维度）', width: '130', sortable: 'custom', type: "click",handle: (that, row, column, cell) => that.canclick(row, column, cell,'rate') , formatter: (row) => (row.niceCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'badCommentRate', label: '差评率（人维度）', width: '130', sortable: 'custom', type: "click",handle: (that, row, column, cell) => that.canclick(row, column, cell,'rate') , formatter: (row) => (row.badCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'centreCommentRate', label: '中评率（人维度）', width: '130', sortable: 'custom', formatter: (row) => (row.centreCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'imBadRate', label: 'IM不满意率（人维度）', width: '100', sortable: 'custom', formatter: (row) => (row.imBadRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'changePlatformRate', label: '转平台服务率', width: '130', sortable: 'custom', formatter: (row) => (row.changePlatformRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'conversationCentreCommentRate', label: '中评率（会话）', width: '130', sortable: 'custom', formatter: (row) => (row.conversationCentreCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'conversationBadCommentRate', label: '差评率（会话）', width: '130', sortable: 'custom', formatter: (row) => (row.conversationBadCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'inviteConversationCount', label: '邀评会话数', width: '100', sortable: 'custom'},
    { istrue: true, prop: 'inviteCommentRate', label: '邀评率（人维度）', width: '130', sortable: 'custom', formatter: (row) => (row.inviteCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'commentRate', label: '评价率（人维度）', width: '130', sortable: 'custom', formatter: (row) => (row.commentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'commentConversationCount', label: '评价会话数', width: '100', sortable: 'custom'},
    { istrue: true, prop: 'badCommentCount', label: '差评人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'centreComment', label: '中评人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'niceCommentCount', label: '好评人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'askOrderRate', label: '询单转化率', width: '100', sortable: 'custom', formatter: (row) => (row.askOrderRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'askPrice', label: '询单客单价', width: '100', sortable: 'custom'},
    { istrue: true, prop: 'salePrice', label: '客服销售额', width: '100', sortable: 'custom'},
    { istrue: true, prop: 'commentCount', label: '评价人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'placeOrderCount', label: '下单人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'payOrderCount', label: '支付人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'inviteCommentCount', label: '邀评人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'askOrderCount', label: '询单人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'attendanceDays', label: '出勤人次', width: '80', sortable: 'custom'},
  { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, inquirsstatisticsbyshop, datepicker, buschar },
  props:["partInfo"],
  data() {
    return {
      dialogMapVisible: { visible: false, title: "", data: [] },
      that: this,
      Filter: {
      },
      shopList: [],
      userList: [],
      groupList: [],
      inquirsstatisticslist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: { },
      pager: { OrderBy: "inquirs", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      //
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
      filterGroupList:[],
      isleavegroup:this.partInfo,//是否离组
    };
  },
  watch:{
        partInfo(){
            this.isleavegroup = this.partInfo;
            this.getKuaiShouGroup();
        }
    },
  async mounted() {
    this.isleavegroup = this.partInfo;
    window.shshowlisttab55ks = this.shshowlisttab55ks
    await this.GetGroupNameList();
  },
  methods: {
     async GetGroupNameList() {
      let groups = await getKuaiShouGroup({ groupType: 1,isleavegroup:this.isleavegroup });
          if (groups?.success && groups?.data && groups?.data.length > 0) {
              this.filterGroupList=groups.data;
          }
    },
    async showchart(row) {//趋势图

      if (this.Filter.timerange) {
        this.Filter.startSdate = this.Filter.Sdate[0];
        this.Filter.endSdate = this.Filter.Sdate[1]
      }
      var params = { groupName : row.groupName, sname: row.sname, startDate : this.Filter.startSdate, endDate : this.Filter.endSdate,groupType:1 }
      let that = this;

      const res = await getKuaiShouPersonalEfficiencyChat(params).then(res => {
        that.dialogMapVisible.visible = true;
        that.dialogMapVisible.data = res;
        that.dialogMapVisible.title = res.title
        res.title="";
      })
      this.dialogMapVisible.visible = true
    },
    shshowlisttab55ks(groupname, startdate, enddate) {
      this.Filter.sname="";
       this.Filter.groupNameList = [groupname];
      this.Filter.sdate = [startdate, enddate];
      if (startdate == null || enddate == null)
                this.Filter.sdate = ["", ""];
            this.Filter.startDate = startdate;
            this.Filter.endDate = enddate;
      this.onSearch()
    },



    async canclick(row, column, cell,txt) {//姓名，好评率，差评率 点击弹框
      if (this.Filter.sdate) {
        localStorage.setItem("startsdate", this.Filter.sdate[0]);
        localStorage.setItem("endsdate", this.Filter.sdate[1]);
      }
      else {
        localStorage.setItem("startsdate", "");
        localStorage.setItem("endsdate", "");
      }
      if(txt=="rate"){
        localStorage.setItem("rate", "rate");
      }else{
        localStorage.setItem("rate", "");
      }

      localStorage.setItem("sname", row.sname);
      localStorage.setItem("groupType", 1);
      this.dialogVisibleSyj = true
    },
    async deleteBatch(row) {
      var that = this;
      this.$confirm("此操作将删除此批次个人效率统计数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          await deleteInquirsStatisticsBatch({ batchNumber: row.batchNumber })
          that.$message({ message: '已删除', type: "success" });
          that.onRefresh()
        });
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    onImportSyj() {
      this.dialogVisibleSyj = true
    },
    async uploadFile2(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      const res = importInquirsStatisticsAsync(form);
      this.$message({ message: '上传成功,正在导入中...', type: "success" });
    },
    async uploadSuccess2(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
    },
    async onSubmitupload2() {
      this.$refs.upload2.submit()
    },
    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getinquirsstatisticsList();
    },
    getParam() {
      if (this.Filter.sdate) {
        this.Filter.startDate = this.Filter.sdate[0];
        this.Filter.endDate = this.Filter.sdate[1];
        
      }
      else {
        this.Filter.startDate = null;
        this.Filter.endDate = null;
      }
      this.Filter.groupType=1;

      const para = { ...this.Filter };
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,

      };
      return params;
    },
    async getinquirsstatisticsList() {
      let params = this.getParam();
      console.log(params)
      this.listLoading = true;
      const res = await getKuaiShouPersonalEfficiencyPageList(params);
      console.log(res)
      this.listLoading = false;
      console.log(res.data.list)
      //console.log(res.data.summary)

      this.total = res.data.total
      this.inquirsstatisticslist = res.data.list;
      this.summaryarry=res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    async onExport() {
      let params = this.getParam();
      this.listLoading = true
      const res = await exportKuaiShouPersonalEfficiencyList(params)
      this.listLoading = false
      if (!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '快手个人效率统计(售前组)_' + new Date().toLocaleString() + '.xlsx');
      aLink.click()
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 60px;
}
</style>
