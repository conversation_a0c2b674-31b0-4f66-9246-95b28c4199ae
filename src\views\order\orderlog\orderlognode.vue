<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
              <el-button style="padding: 0;">
                    <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" :picker-options="pickerOptions">
                    </el-date-picker>
                </el-button>
                <el-button style="padding: 0;">
                    <el-input v-model.trim="filter.orderNoInner" style="width: 140px" placeholder="内部单号" @keyup.enter.native="onSearch" clearable maxlength="40" />
                </el-button>
                <el-button style="padding: 0;">
                    <el-input v-model.trim="filter.checker" style="width: 90px" placeholder="审单人" @keyup.enter.native="onSearch" clearable maxlength="20" />
                </el-button>
                <el-button style="padding: 0;">
                    <el-input v-model.trim="filter.printer" style="width: 90px" placeholder="打单人" @keyup.enter.native="onSearch" clearable maxlength="20" />
                </el-button>
                <el-button style="padding: 0;">
                    <el-input v-model.trim="filter.bachno" style="width: 140px" placeholder="批次号" @keyup.enter.native="onSearch" clearable maxlength="20" />
                </el-button>
                <el-button style="padding: 0;">
                    <el-input v-model.trim="filter.bacher" style="width: 90px" placeholder="配货人" @keyup.enter.native="onSearch" clearable maxlength="20" />
                </el-button>
                <el-button style="padding: 0;">
                    <el-input v-model.trim="filter.packger" style="width: 90px" placeholder="打包人" @keyup.enter.native="onSearch" clearable maxlength="20" />
                </el-button>
                <el-button style="padding: 0;">
                    <el-input v-model.trim="filter.weighter" style="width: 90px" placeholder="称重人" @keyup.enter.native="onSearch" clearable maxlength="20" />
                </el-button>
                <el-button style="padding: 0;">
                    <el-input v-model.trim="filter.sender" style="width: 90px" placeholder="发货人" @keyup.enter.native="onSearch" clearable maxlength="20" />
                </el-button>
                <el-button style="padding: 0;">
                  <el-select filterable v-model="filter.isyus" collapse-tags clearable placeholder="是否预售" style="width: 90px">
                    <el-option label="是" :value="false" />
                    <el-option label="否" :value="true" />
                  </el-select>
                  <el-select filterable v-model="filter.isqueh" collapse-tags clearable placeholder="是否缺货" style="width: 90px">
                    <el-option label="是" :value="false" />
                    <el-option label="否" :value="true" />
                  </el-select>
                  <el-select filterable v-model="filter.isrefund" collapse-tags clearable placeholder="是否退款" style="width: 90px">
                    <el-option label="是" :value="false" />
                    <el-option label="否" :value="true" />
                  </el-select>
                </el-button>
                <el-button style="padding: 0;">
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button>
            </el-button-group>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false" :loading="listLoading">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px" v-dialogDrag>
            <orderLogPage ref="orderLogPage" :orderNoInner="sendOrderNoInner" style="z-index:10000;height:600px" />
        </el-dialog>
    </my-container>
</template>

<script>
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import dayjs from "dayjs";
    import { formatTime } from "@/utils";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    import orderLogPage from "@/views/order/logisticsWarning/LogisticsEarlyWarLog.vue";
    import { getOrderActionThenNodePageList } from '@/api/order/orderlog';
    const tableCols = [
        { istrue: true, prop: 'orderNoInner', label: '内部单号', width: '90', sortable: 'custom',type:'click',handle:(that,row)=>that.showLogDetail(row) },
        { istrue: true, prop: 'payTime', label: '付款时间', width: '130', sortable: 'custom' },
        { istrue: true, prop: 'ys_OperationTime', label: '预售时间', width: '130', tipmesg: '最晚操作预售时间'},
        { istrue: true, prop: 'qh_OperationTime', label: '缺货时间', width: '130', tipmesg: '最晚缺货时间'},
        { istrue: true, prop: 'qh_Remark', label: '缺货信息', width: '90' , tipmesg: '最晚缺货信息'},
        { istrue: true, prop: 'fc_Name', label: '发货仓', width: '110' },
        { istrue: true, prop: 'ch_OperationTime', label: '审单时间', width: '130' , tipmesg: '最晚审单时间'},
        { istrue: true, prop: 'ch_Operator', label: '审单人', width: '80' , tipmesg: '最晚审单人'},
        { istrue: true, prop: 'pr_OperationTime', label: '打单时间', width: '130' , tipmesg: '最晚打单时间'},
        { istrue: true, prop: 'bch_OperationTime', label: '批次时间', width: '130' , tipmesg: '最晚批次时间'},
        { istrue: true, prop: 'bch_Operator', label: '配货人', width: '80' , tipmesg: '最晚配货人'}, 
        { istrue: true, prop: 'bch_Remark', label: '批次号', width: '120' , tipmesg: '最晚批次号'},
        { istrue: true, prop: 'pck_OperationTime', label: '打包时间', width: '130' , tipmesg: '最晚打包时间'},
        { istrue: true, prop: 'pck_Operator', label: '打包人', width: '80' , tipmesg: '最晚打包人'},
        { istrue: true, prop: 'cz_OperationTime', label: '称重时间', width: '130' , tipmesg: '最晚称重时间'},
        { istrue: true, prop: 'cz_Operator', label: '称重人', width: '80' , tipmesg: '最晚称重人'},
        { istrue: true, prop: 'fh_OperationTime', label: '发货时间', width: '130' , tipmesg: '最晚发货时间'},
        { istrue: true, prop: 'fh_Operator', label: '发货人', width: '80' , tipmesg: '最晚发货人'},
        { istrue: true, prop: 'refund_OperationTime', label: '退款时间', width: '130' ,},
    ]
    export default {
        name: 'intransitdetail',
        components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, orderLogPage},
        props: {

        },
        data() {
            return {
                that: this,
                filter: {
                    orderNoInner: null,
                    timerange: [
                        formatTime(dayjs().subtract(5, "day"), "YYYY-MM-DD"),
                        formatTime(new Date(), "YYYY-MM-DD"),
                    ],
                    startDate: null,
                    endDate: null,
                },
                list: [],
                summaryarry: {},
                pager: { OrderBy: "PayTime", IsAsc: false },
                tableCols: tableCols,
                total: 0,
                sels: [],
                listLoading: false,
                pageLoading: false,
                sendOrderNoInner: null,
                dialogHisVisible:false,
                pickerOptions: {
                    shortcuts: [
                        {
                            text: '近三天',
                            onClick(picker) {
                                const tdate = new Date(new Date().getTime());
                                const end = new Date(new Date(tdate.toLocaleDateString()));
                                const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                                end.setTime(end.getTime() - 3600 * 1000 * 24);
                                picker.$emit('pick', [start, end]);
                            }
                        }]
                },
            };
        },
        async mounted() {
            await this.onSearch()
        },
        methods: {
            //查询第一页
            async onSearch() {
                console.log(this.filter)
                if (!this.filter.timerange) {
                    this.$message({ message: "请选择日期", type: "warning", });
                    return;
                }
                this.$refs.pager.setPage(1)
                await this.getlist();
            },
            //获取查询条件
            getCondition() {
                this.filter.startDate = null;
                this.filter.endDate = null;
                if (this.filter.timerange && this.filter.timerange.length > 1) {
                    this.filter.startDate = this.filter.timerange[0];
                    this.filter.endDate = this.filter.timerange[1];
                }
                else {
                    this.$message({ message: "请先选择时间", type: "warning" });
                    return false;
                }
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }
                return params;
            },
            //分页查询
            async getlist() {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true
                const res = await getOrderActionThenNodePageList(params)
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                //this.summaryarry = res.data.summaryarry;
                console.log(res.data, 'resdata')
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
            },
            //排序查询
            async sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            selectchange: function (rows, row) {
                this.selids = []; console.log(rows)
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
            showLogDetail (row) {
                this.dialogHisVisible = true;
                this.sendOrderNoInner = String(row.orderNoInner);
            },
        },
    };
</script>

<style lang="scss" scoped></style>
