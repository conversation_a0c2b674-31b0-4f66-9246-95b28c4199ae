<template>
    <MyContainer>
        <template #header>
            <div class="header">
                <el-date-picker style="width:230px; margin-right: 10px;" v-model="ListInfo.timerange" type="daterange"
                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始"
                    end-placeholder="结束" :clearable="false"></el-date-picker>
                <el-input placeholder="采购单号" v-model="ListInfo.buyNo" maxlength="50" class="publicMargin"
                    style="width: 120px;" clearable></el-input>
                <el-input placeholder="任务编号" v-model="ListInfo.warehousNo" maxlength="50" class="publicMargin"
                    style="width: 120px;" clearable></el-input>
                <el-select filterable v-model="ListInfo.brandId" clearable placeholder="采购"
                    style="width: 120px; margin-right: 10px;">
                    <el-option v-for="item in brandList" :key="item.value" :label="item.label"
                        :value="item.value"></el-option>
                </el-select>
                <el-select v-model="ListInfo.merchandiserId" filterable placeholder="采购跟单" class="publicMargin"
                    style="width: 120px;" clearable>
                    <el-option v-for="item in inventoryList" :key="item.merchandiserId" :label="item.merchandiser"
                        :value="item.merchandiserId">
                    </el-option>
                </el-select>
                <el-input placeholder="拍摄人" v-model="ListInfo.photographer" maxlength="50" class="publicMargin"
                    style="width: 120px;" clearable></el-input>
                <el-button type="primary" @click="searchList">查询</el-button>
                <el-button @click="showpeizhiVisible">配置</el-button>
            </div>
            <div class="radioGrp">
                <el-button-group>
                    <el-button :type="!isWeightNormal ? 'primary' : ''" @click="btnchange">异常</el-button>
                    <el-button :type="isWeightNormal ? 'primary' : ''" @click="btnchange">正常</el-button>
                </el-button-group>
            </div>
        </template>
        <template>
            <vendorSummary :ListInfo="ListInfo" style="height: 95%; width: 100%;" ref="summaryTable"
                :isHidden="isHidden" />
        </template>

        <!-- 配置 -->
        <el-dialog title="配置" :visible.sync="peizhiVisible" width="20%" v-dialogDrag>
            <peizhi :peizhidata="peizhidata" @closedialog="peizhiVisible = false;" v-if="peizhiVisible"></peizhi>
        </el-dialog>
        <!-- 分组 -->
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import vxetablebase from "@/components/VxeTable/vxetablebase.vue";
import vendorSummary from "./vendorSummary.vue"
import peizhi from "./peizhi.vue"
import { getAllProBrand } from '@/api/inventory/warehouse'
import { getWarehousingOrderVideoWeightCompareConfig, getMerchandiserList } from '@/api/inventory/purchasequality.js'
import dayjs from 'dayjs'
import { formatTime, formatPlatform,downloadLink } from "@/utils/tools";
export default {
    components: { MyContainer, cesTable, vxetablebase, vendorSummary, peizhi },
    name: "vendorSumIndex",
    data() {
        return {
            that: this,
            peizhidata: {},
            peizhiVisible: false,
            isWeightNormal: false,
            ListInfo: {
                isWeightNormal: false,
                timerange: [formatTime(new Date(), "YYYY-MM-DD"), formatTime(new Date(), "YYYY-MM-DD")]
            },
            checkList: [],
            brandList: [],
            btntype: 1,
            isHidden: false,//是否展开
            inventoryList: [],
        };
    },
    mounted() {
        this.getBrandList()
        this.getInventory()
    },
    methods: {
        //获取采购跟单列表
        async getInventory() {
            const { data } = await getMerchandiserList()
            this.inventoryList = data
            console.log(this.inventoryList, 'this.inventoryList');
        },
        btnchange() {
            this.$refs.summaryTable.changepage(this.isWeightNormal)
            this.isWeightNormal = !this.isWeightNormal;
            this.ListInfo.isWeightNormal = this.isWeightNormal;
            this.searchList();
        },
        showpeizhiVisible() {
            this.getpeizhi();
        },
        async getpeizhi() {
            let res = await getWarehousingOrderVideoWeightCompareConfig();
            if (!res.success) {
                return;
            }
            this.peizhidata = res.data || {};
            setTimeout(() => {
                this.peizhiVisible = true;
            }, 0)
        },
        changeHidden(e) {
            if (e) {
                this.$refs.summaryTable.unfoldTree()
            } else {
                this.$refs.summaryTable.foldTree()
            }
        },
        clear() {
            this.checkList = []
        },
        //获取采购列表
        async getBrandList() {
            const { data, success } = await getAllProBrand()
            if (success) {
                this.brandList = data.map(item => {
                    return {
                        label: item.value,
                        value: item.key
                    }
                })
            } else {
                this.$message.error('获取采购人员列表失败')
            }
        },
        searchList() {
            if (this.ListInfo.timerange && this.ListInfo.timerange.length > 0) {
                this.ListInfo.startTime = this.ListInfo.timerange[0];
                this.ListInfo.endTime = this.ListInfo.timerange[1];
            } else {
                this.ListInfo.startTime = null;
                this.ListInfo.endTime = null;
            }
            setTimeout(() => {
                this.$refs.summaryTable.getAlreadyList(true, this.ListInfo)
            }, 0)
        },
    }
};
</script>

<style lang="scss" scoped>
.header {
    display: flex;
    margin-bottom: 10px;
}

.publicMargin {
    margin-right: 20px;
}

.detail ::v-deep .vxetoolbar20221212 {
    display: none !important;
}

::v-deep .el-badge__content {
    padding: 0 4px;
    top: 7px;
    right: 0;
}

.radioGrp {
    margin-bottom: 10px;
    display: flex;
}

::v-deep .el-checkbox__inner {
    border-radius: 7px !important;
}
</style>
