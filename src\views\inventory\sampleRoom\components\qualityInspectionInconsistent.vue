<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable class="publicCss" />
        <el-select v-model="ListInfo.warehouse" placeholder="仓库" class="publicCss" clearable filterable>
          <el-option v-for="item in stashList" :key="item" :label="item" :value="item"></el-option>
        </el-select>
        <el-input v-model.trim="ListInfo.goodsNumber" placeholder="商品编号" maxlength="50" clearable class="publicCss" />
        <el-select v-model="ListInfo.bitNumber" placeholder="模糊搜索并选择库位" :remote-method="remoteMethod" remote clearable
          filterable :loading="searchloading" class="publicCss" style="width: 145px;">
          <el-option v-for="item in locationList" :key="item.warehouseBitCode" :label="item.warehouseBitCode"
            :value="item.warehouseBitCode" />
        </el-select>
        <el-select v-model="ListInfo.groupId" placeholder="运营组" class="publicCss" clearable filterable>
          <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key" />
        </el-select>
        <el-select v-model="ListInfo.brandId" placeholder="采购组" class="publicCss" clearable filterable>
          <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="ListInfo.storageStatus" placeholder="入库状态" class="publicCss" clearable filterable>
          <el-option :key="'1'" label="待入库" value="1" />
          <el-option :key="'2'" label="待归还" value="2" />
          <el-option :key="'3'" label="在库" value="3" />
          <el-option :key="'4'" label="待领取" value="4" />
        </el-select>
        <el-select v-model="ListInfo.sampleSource" placeholder="请选择样品来源" class="publicCss" clearable filterable>
          <el-option label="对手样品" value="对手样品"></el-option>
          <el-option label="商家样品" value="商家样品"></el-option>
          <el-option label="公司样品" value="公司样品"></el-option>
        </el-select>
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="借出开始时间" end-placeholder="借出结束时间" :picker-options="pickerOptions"
          style="width: 210px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <div class="publicCss" style="width: 190px;">
          <inputYunhan ref="refyhGoodsCode" :inputt.sync="ListInfo.yhGoodsCode" v-model="ListInfo.yhGoodsCode"
            width="190px" placeholder="公司商品编码/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackMethod($event, 'yhGoodsCode')" title="公司商品编码">
          </inputYunhan>
        </div>
        <el-button type="primary" @click="getList('search')">查询</el-button>
      </div>
      <div class="top">
        <el-input v-model.trim="ListInfo.buyNo" placeholder="采购单号" maxlength="50" clearable class="publicCss"
          style="width: 100px;" />
        <number-range :min.sync="ListInfo.lendTimeCount" :max.sync="ListInfo.lendEndTimeCount" min-label="借出时长最小值"
          max-label="借出时长最大值" class="publicCss" :precision="2" style="width: 165px;" />
        <el-select v-model="ListInfo.isYuQi" placeholder="是否逾期" class="publicCss" clearable filterable
          style="width: 100px;">
          <el-option :key="'1'" label="是" :value="1" />
          <el-option :key="'0'" label="否" :value="0" />
        </el-select>
        <number-range :min.sync="ListInfo.startDaysOverdue" :max.sync="ListInfo.endLendEndTime" min-label="逾期开始天数"
          max-label="逾期结束天数" class="publicCss" :precision="0" style="width: 165px;" />
        <el-input v-model.trim="ListInfo.manufacturerName" placeholder="厂家名称" maxlength="50" clearable class="publicCss"
          style="width: 100px;" />
        <el-select v-model="ListInfo.isPai" placeholder="已拍/待拍" class="publicCss" style="width: 100px;" clearable
          filterable>
          <el-option label="已拍" :value="1"></el-option>
          <el-option label="待拍" :value="2"></el-option>
        </el-select>
        <el-input v-model.trim="ListInfo.createdUserName" placeholder="拍摄人" maxlength="50" clearable class="publicCss"
          style="width: 100px;" />
        <el-select v-model="ListInfo.goodsState" style="width: 90px" placeholder="编码状态" clearable class="publicCss">
          <el-option label="换厂" :value="1" />
          <el-option label="新品" :value="2" />
          <el-option label="采购降价需要比样" :value="10" />
          <el-option label="月首单且以往都有采购记录" :value="11" />
        </el-select>
      </div>
    </template>
    <vxetablebase :id="'qualityInspectionInconsistent202507221015'"
      :tablekey="'qualityInspectionInconsistent202507221015'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" style="width: 100%; margin: 0" :loading="loading" :height="'100%'" :border="true">
      <template slot="right">
        <vxe-column title="操作" width="90" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" class="Inside_button" @click="handleInspection(row)"
                v-if="row.isLastEqual2 === null && row.numberShots && row.numberShots > 1 && (row.isLastEqual === null || row.isLastEqual === undefined || row.isLastEqual === '')">质检</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="质检" :visible.sync="inspectionInfo.visible" width="70%" v-dialogDrag
      style="margin-top: -6vh!important;">
      <qualityInspection v-if="inspectionInfo.visible" :rowData="inspectionInfo.rowData"
        @close="inspectionInfo.visible = false" :displayData="inspectionInfo.displayData" :isNote="true"
        @successClose="successClose" />
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { getSampleRegistrationNotLastEqualPage, getSampleWarehouse, getWarehouseLocationManagement, getSampleRegistrationVerifyData } from "@/api/inventory/sampleGoods";
import { getAllProBrand } from '@/api/inventory/warehouse'
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import { pickerOptions } from '@/utils/tools'
import qualityInspection from './qualityInspection.vue'
import inputYunhan from "@/components/Comm/inputYunhan";
import numberRange from "@/components/number-range/index.vue";
const tableCols = [
  { sortable: 'custom', width: '140', align: 'center', prop: 'goodsCode', label: '商品编码', treeNode: true },
  { sortable: 'custom', width: '140', align: 'center', prop: 'goodsName', label: '商品名称' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'sampleSource', label: '样品来源' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'goodsNumber', label: '商品编号' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'yhGoodsCode', label: '公司商品编码' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'styleCode', label: '款式名称' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'ckGoodsCode', label: '参考商品编码' },
  {
    sortable: 'custom', width: '100', align: 'center', prop: 'goodsState', label: '编码状态', formatter: (row) => {
      return row.goodsStateStr;
    }
  },
  { sortable: 'custom', width: '100', align: 'center', prop: 'bitNumber', label: '库位' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'warehouse', label: '仓库' },
  {
    sortable: 'custom', width: '100', align: 'center', prop: 'storageStatus', label: '入库状态', formatter: (row) => {
      return row.storageStatus == 1 ? '待入库' : row.storageStatus == 2 ? '待归还' : row.storageStatus == 3 ? '在库' : row.storageStatus == 4 ? '待领取' : ''
    }
  },
  { sortable: 'custom', width: '100', align: 'center', prop: 'buyNo', label: '采购单号' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberShots', label: '拍摄次数' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'shootingTime', label: '拍摄时间' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'createdUserName', label: '拍摄人' },
  { sortable: 'custom', width: '100', align: 'left', prop: 'images', label: '商品图片', type: "images" },
  { sortable: 'custom', width: '100', align: 'left', prop: 'outerPackingPicture', label: '外包装图片', type: "images" },
  { sortable: 'custom', width: '100', align: 'left', prop: 'physicalPicture', label: '实物图片', type: "images" },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isChao40CM', label: '折叠单边是否超40CM', formatter: (row) => row.isChao40CM == 1 ? '是' : row.isChao40CM == 0 ? '否' : '' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isZheDie', label: '是否可折叠', formatter: (row) => row.isZheDie == 1 ? '是' : row.isZheDie == 0 ? '否' : '' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'measuredWeight', label: '测量重(g)' },
  { sortable: 'custom', width: '100', align: 'left', prop: 'measuredWeightImages', label: '测重图', type: "images" },
  { sortable: 'custom', width: '100', align: 'center', prop: 'measuredChang', label: '打包长(mm)' },
  { sortable: 'custom', width: '100', align: 'left', prop: 'measuredChangImages', label: '测打包长图', type: "images" },
  { sortable: 'custom', width: '100', align: 'center', prop: 'measuredWidth', label: '打包宽(mm)' },
  { sortable: 'custom', width: '100', align: 'left', prop: 'measuredWidthImages', label: '测打包宽图', type: "images" },
  { sortable: 'custom', width: '100', align: 'center', prop: 'measuredHeight', label: '打包高(mm)' },
  { sortable: 'custom', width: '100', align: 'left', prop: 'measuredHeightImages', label: '测打包高图', type: "images" },
  { sortable: 'custom', width: '100', align: 'center', prop: 'zhankaiMeasuredChang', label: '展开长(mm)' },
  { sortable: 'custom', width: '100', align: 'left', prop: 'zhankaiMeasuredChangImages', label: '测展开长图', type: "images" },
  { sortable: 'custom', width: '100', align: 'center', prop: 'zhankaiMeasuredWidth', label: '展开宽(mm)' },
  { sortable: 'custom', width: '100', align: 'left', prop: 'zhankaiMeasuredWidthImages', label: '测展开宽图', type: "images" },
  { sortable: 'custom', width: '100', align: 'center', prop: 'zhankaiMeasuredHeight', label: '展开高(mm)' },
  { sortable: 'custom', width: '100', align: 'left', prop: 'zhankaiMeasuredHeightImages', label: '测展开高图', type: "images" },
  { sortable: 'custom', width: '100', align: 'center', prop: 'recentLender', label: '最近借出人' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'lastExitTime', label: '最近借出时间' },
  { width: '100', align: 'center', prop: 'deliveryTime', label: '借出时长' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'estimatedServiceLife', label: '预计归还时间' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'actualReturnTime', label: '实际归还时间' },
  { width: '100', align: 'center', prop: 'daysOverdue', label: '逾期天数' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'colour', label: '颜色' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isJianCe', label: '是否检测', formatter: (row) => row.isJianCe == 1 ? '是' : row.isJianCe == 2 ? '否' : '' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'groupName', label: '运营组' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'brandName', label: '采购员' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'brandDeptName', label: '采购组' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'manufacturerName', label: '厂家名称' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'manufacturerLink', label: '厂家链接' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'opponentShopName', label: '对手店铺' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'opponentLink', label: '对手链接' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'expressNo', label: '快递单号/标识' },
  { width: '120', align: 'center', prop: 'packageGoodsCode', label: '包材商品编码' },
  { width: '100', align: 'center', prop: 'package', label: '包材名称' },
  { width: '100', align: 'center', prop: 'packageImage', label: '包材图片', type: "images" },
  { width: '100', align: 'center', prop: 'packageSize', label: '包材尺寸' },
  { width: '100', align: 'center', prop: 'damageRate', label: '破损率', formatter: (row) => row.damageRate || row.damageRate == 0 ? row.damageRate + '%' : '' },
  { width: '120', align: 'center', prop: 'newPackageGoodsCode', label: '新包材商品编码' },
  { width: '100', align: 'center', prop: 'newPackage', label: '新包材名称' },
  { width: '100', align: 'center', prop: 'newPackageImage', label: '新包材图片', type: "images" },
  { width: '100', align: 'center', prop: 'newPackageSize', label: '新包材尺寸' },
  { width: '100', align: 'center', prop: 'newDamageRate', label: '新破损率', formatter: (row) => row.newDamageRate || row.newDamageRate == 0 ? row.newDamageRate + '%' : '' },
  { width: '100', align: 'center', prop: 'isFuHeTongGuo', label: '复核结果', formatter: (row) => row.isFuHeTongGuo == 1 ? '复核通过' : row.isFuHeTongGuo == 0 ? '复核不通过' : '' },
  { width: '100', align: 'center', prop: 'noFuHeTongGuoRemark', label: '未通过复核原因' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isRevert', label: '是否同品归还', formatter: (row) => row.isRevert == 1 ? '是' : row.isRevert == 0 ? '否' : '' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isLastEqual2', label: '是否与前次数据相同' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'verifyRemark2', label: '质检备注' },
]
export default {
  name: "qualityInspectionInconsistent",
  components: {
    MyContainer, vxetablebase, inputYunhan, numberRange, qualityInspection
  },
  data() {
    return {
      inspectionInfo: {
        visible: false,
        rowData: {},
        displayData: {},
      },
      stashList: [],//仓库
      locationList: [],//库位
      searchloading: false,
      brandlist: [],//采购员
      directorGroupList: [],//运营组
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        goodsCode: null,//商品编码
        goodsName: null,//商品名称
        warehouse: null,//仓库
        goodsNumber: null,//商品编号
        bitNumber: null,//库位
        groupId: null,//运营组
        brandId: null,//采购组
        storageStatus: null,//入库状态
        lendTime: null,//借出开始时间
        lendEndTime: null,//借出结束时间
        lendTimeCount: null,//借出时长最小值
        lendEndTimeCount: null,//借出时长最大值
        startDaysOverdue: null,//借出开始时间
        endLendEndTime: null,//借出结束时间
        isYuQi: null,//是否逾期
        manufacturerName: null,//厂家名称
        createdUserName: null,//拍摄人
        yhGoodsCode: null,//公司商品编码
        buyNo: null,//采购单号
        sampleSource: null,//样品来源
        isLastEqual2: null,//是否与前次数据相同  1一致 0不一致
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
    await this.init()
  },
  methods: {
    successClose() {
      this.inspectionInfo.visible = false
      this.getList()
    },
    async handleInspection(row) {
      this.loading = true;
      const { data, success } = await getSampleRegistrationVerifyData({ id: row.id });
      this.loading = false;
      if (!success) return
      this.$nextTick(() => {
        this.inspectionInfo.rowData = row
        this.inspectionInfo.displayData = data
        this.inspectionInfo.visible = true
      })
    },
    callbackMethod(val, type) {
      const map = {
        yhGoodsCode: () => (this.ListInfo.yhGoodsCode = val),
      };
      map[type]?.();
    },
    async init() {
      const { data: stashList, success: success1 } = await getSampleWarehouse()
      if (success1) {
        this.stashList = [...new Set(stashList)];
      }
      var res = await getAllProBrand();
      this.brandlist = res.data.map(item => {
        return { value: item.key, label: item.value };
      });
      const res2 = await getDirectorGroupList({})
      this.directorGroupList = res2.data || []
    },
    async remoteMethod(query) {
      if (query !== '') {
        this.searchloading == true
        const res = await getWarehouseLocationManagement({ currentPage: 1, pageSize: 50, warehouseBitCode: query })
        this.searchloading = false
        this.locationList = res?.data?.list
      }
      else {
        this.locationList = []
      }
    },
    changeTime(e) {
      this.ListInfo.lendTime = e ? e[0] : null
      this.ListInfo.lendEndTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getSampleRegistrationNotLastEqualPage(this.ListInfo)
      if (success) {
        const fieldsToProcess = [
          'images',
          'outerPackingPicture',
          'physicalPicture',
          'measuredWeightImages',
          'measuringThicknessImages',
          'measuredWidthImages',
          'measuredHeightImages',
          'measuredChangImages',
          'zhankaiMeasuredHeightImages',
          'zhankaiMeasuredWidthImages',
          'zhankaiMeasuredChangImages',
          'packageImage',
          'newPackageImage',
        ];
        const processVoucher = (voucher) => {
          if (voucher) {
            return JSON.stringify(voucher.split(',').map(url => ({ url })));
          }
          return voucher;
        };
        data.list.forEach(item => {
          fieldsToProcess.forEach(field => {
            item[field] = processVoucher(item[field]);
          });
          item.isLastEqual2 = item.isLastEqual2 == 1 ? '是' : item.isLastEqual2 == 0 ? '否' : null
        });
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 130px;
    margin-right: 2px;
  }
}

::v-deep(.el-button.top_button + .el-button.top_button) {
  margin-left: 1px;
}

::v-deep(.el-button.Inside_button + .el-button.Inside_button) {
  margin-left: 5px;
}
</style>
