<template >
    <my-container v-loading="pageLoading" >
        <ces-table style="height:95%" ref="openwebjushuitanjhpctable" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='jhpcList' :isSelection='false' :summaryarry="summaryarry"
            :tableCols='tableCols' :isSelectColumn="true">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-date-picker style="width: 225px" v-model="filter.dates" type="daterange" format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始发生时间"
                            end-placeholder="结束发生时间">
                        </el-date-picker>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.warehouse" clearable filterable placeholder="发货仓" style="width: 200px">
                            <el-option v-for="item in warehouselist" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input placeholder="商品编码" v-model="filter.sku_id" style="width: 130px" clearable
                            maxlength="20"></el-input>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input placeholder="内部单号" v-model="filter.orderNoInner" style="width: 130px" clearable
                            oninput="if(value){value=value.replace(/[^\-\d]/g,'')} if(value>2147483647){value=2147483647} if(value<0){value=0}"
                            maxlength="10"></el-input>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <!-- <el-button type="primary" @click="startImport1">导入库存超卖</el-button>
                    <el-button type="primary" @click="startImport2">导入缺货订单</el-button>
                    <el-button type="primary" @click="startImport3">导入补货订单</el-button> -->
                </el-button-group>
            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getNewOrderListAsync" />
        </template>

        <el-dialog title="导入库存超卖/京东仓加工仓" :visible.sync="dialogVisible1" width="30%" v-dialogDrag
            :close-on-click-modal="false">
            <el-alert title="温馨提示：请选取两个文件，文件名需分别包含“库存超卖”、“京东仓加工仓”。" type="success" :closable="false">
            </el-alert>
            <span>
                <el-upload ref="upload1" class="upload-demo" :auto-upload="false" :multiple="true" :limit="2" action
                    accept=".xlsx" :http-request="uploadFile1" :on-change="uploadChange1" :on-remove="uploadRemove1"
                    :on-success="uploadSuccess1">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading1"
                        @click="submitUpload1">{{ (uploadLoading1 ? '上传中' : '上传') }} </el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible1 = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="导入缺货订单" :visible.sync="dialogVisible2" width="30%" v-dialogDrag :close-on-click-modal="false">
            <span>
                <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading2"
                        @click="submitUpload2">{{ (uploadLoading2 ? '上传中' : '上传') }} </el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible2 = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="导入补货订单" :visible.sync="dialogVisible3" width="30%" v-dialogDrag :close-on-click-modal="false">
            <span>
                <el-upload ref="upload3" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :http-request="uploadFile3" :file-list="fileList3" :data="fileParm3"
                    :on-success="uploadSuccess3">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading3"
                        @click="submitUpload3">{{ (uploadLoading3 ? '上传中' : '上传') }} </el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible3 = false">关闭</el-button>
            </span>
        </el-dialog>


    </my-container>
</template>
<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import datepicker from '@/views/customerservice/datepicker'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import { getTbWarehouseList } from '@/api/inventory/openwebjushuitan';
import { getRealTimeLackOrderPageList, importStockExceedAndOtherWareAsync, importRealTimeLackOrderAsync, importReplenishOrderAsync } from '@/api/inventory/inventoryorder';
const tableCols = [
    //{ istrue: true, prop: 'payTime', label: '付款时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'orderNoInner', label: '内部单号', width: '120', sortable: 'custom',type:'orderLogInfo',orderType:'orderNoInner'  },
    { istrue: true, prop: 'goodsCode', label: '缺货商品编码', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'lackReason', label: '缺货原因', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'modifiedTime', label: '发生时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'dutyUserName', label: '责任人', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'wms_co_name', label: '发货仓', width: '260' },
];
export default {
    name: "realtimeinventorycheck",
    components: { cesTable, MyContainer, datepicker },
    props: {

    },
    data() {
        return {
            warehouselist: [],
            pageLoading: false,
            tableCols: tableCols,
            jhpcList: [],
            that: this,
            sels: [], // 列表选中列
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "modifiedTime", IsAsc: false },
            filter: {
                dates: [formatTime(dayjs(), "YYYY-MM-DD"), formatTime(dayjs(), "YYYY-MM-DD")],
                sdate: null,
                edate: null,
                warehouse: null,
                wave_id: null,
            },

            dialogVisible1: false,
            uploadLoading1: false,
            fileList1: [],
            fileParm1: {},

            dialogVisible2: false,
            uploadLoading2: false,
            fileList2: [],
            fileParm2: {},

            dialogVisible3: false,
            uploadLoading3: false,
            fileList3: [],
            fileParm3: {},
        }
    },
    async mounted() {
        await this.getInventoryWareHouseList()
        this.onSearch();
    },
    methods: {
        async getInventoryWareHouseList() {
            if (this.warehouselist.length <= 0) {
                let wares = await getTbWarehouseList();
                if (wares?.success && wares?.data && wares?.data.length > 0) {
                    wares?.data.forEach(f => {
                        if (f.manager_ddid)
                            this.warehouselist.push({ value: f.wms_co_id, label: f.name });
                    });
                }
            }
        },
        //排序查询      
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getNewOrderListAsync();
        },
        async getNewOrderListAsync() {
            if (this.filter.dates && this.filter.dates.length > 1) {
                this.filter.sdate = this.filter.dates[0];
                this.filter.edate = this.filter.dates[1];
            }
            else {
                this.filter.sdate = null;
                this.filter.edate = null;
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = { ...pager, ...page, ... this.filter }
            this.pageLoading = true;
            let res = await getRealTimeLackOrderPageList(params);
            this.pageLoading = false;
            if (res?.success) {
                this.total = res.data.total
                this.jhpcList = res.data.list;
                //this.summaryarry = res.summary;
            }
        },

        startImport1() {
            this.dialogVisible1 = true;
        },
        uploadSuccess1(response, file, fileList1) {
            if (fileList1.length == 2) {
                fileList1.splice(0, 1);
                fileList1.splice(0, 1);
            }
            if (fileList1.length == 1) {
                fileList1.splice(0, 1);
            }
        },
        async submitUpload1() {
            if (!this.fileList1 || this.fileList1.length == 0) {
                this.$message({ message: "请先选取文件", type: "warning" });
                return false;
            }
            this.fileHasSubmit = true;
            this.$refs.upload1.submit();
        },
        async uploadFile1(item) {
            if (!this.fileHasSubmit) {
                return false;
            }
            this.fileHasSubmit = false;
            let stockExceedFile = null;
            let otherWareFile = null;
            for (const key in this.fileList1) {
                let name = this.fileList1[key].name;
                if (name.indexOf("库存超卖") > -1 && name.indexOf("京东仓加工仓") < 0) {
                    stockExceedFile = this.fileList1[key];
                }
                if (name.indexOf("库存超卖") < 0 && name.indexOf("京东仓加工仓") > -1) {
                    otherWareFile = this.fileList1[key];
                }
            }
            if (!stockExceedFile || !otherWareFile) {
                this.$message({ message: "请同时上传库存超卖和京东仓加工仓", type: "error" });
                return false;
            }
            console.log(stockExceedFile, "stockExceedFile")
            console.log(otherWareFile, "otherWareFile")
            this.uploadLoading1 = true;
            const form = new FormData();
            form.append("stockExceedFile", stockExceedFile);
            form.append("otherWareFile", otherWareFile);
            const res = await importStockExceedAndOtherWareAsync(form);
            this.uploadLoading1 = false;
            if (!res) {
                this.$message({ message: "没有数据", type: "error" });
                return;
            }
            this.$message({ message: "操作成功,已生成：缺货SKU.txt", type: "success" });
            const aLink = document.createElement("a");
            let blob = new Blob([res])
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '缺货SKU' + new Date().toLocaleString() + '.txt')
            aLink.click();
        },
        uploadChange1(file, fileList1) {
            if (fileList1 && fileList1.length > 0) {
                var list = [];
                for (var i = 0; i < fileList1.length; i++) {
                    if (fileList1[i].status == "success")
                        list.push(fileList1[i]);
                    else
                        list.push(fileList1[i].raw);
                }
                this.fileList1 = list;
            }
        },
        uploadRemove1(file, fileList1) {
            this.uploadChange1(file, fileList1);
        },



        startImport2() {
            this.dialogVisible2 = true;
        },
        uploadSuccess2(response, file, fileList) {
            this.fileList2.splice(fileList.indexOf(file), 1);
            this.uploadLoading2 = false;
            this.dialogVisible2 = false;
        },
        async submitUpload2() {
            this.$refs.upload2.submit();
        },
        async uploadFile2(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading2 = true;
            const form = new FormData();
            form.append("file", item.file);
            const res = await importRealTimeLackOrderAsync(form);
            this.uploadLoading2 = false;
            if (res?.success) {
                this.$message({ message: '导入成功', type: "success" });
                this.dialogVisible2 = false;
                this.onSearch();
            }
        },



        startImport3() {
            this.dialogVisible3 = true;
        },
        uploadSuccess3(response, file, fileList) {
            this.fileList3.splice(fileList.indexOf(file), 1);
            this.uploadLoading3 = false;
            this.dialogVisible3 = false;
        },
        async submitUpload3() {
            this.$refs.upload3.submit();
        },
        async uploadFile3(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading3 = true;
            const form = new FormData();
            form.append("file", item.file);
            const res = await importReplenishOrderAsync(form);
            this.uploadLoading3 = false;
            if (res?.success) {
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
                this.dialogVisible3 = false;
                this.onSearch();
            }
        },
    }
}
</script>