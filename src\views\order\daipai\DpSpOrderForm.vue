<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" label-width="100px" label-position="right" :disabled="!formEditMode">
                <el-row >
                    <el-col :span="4">
                        <el-form-item  label="内部订单：">
                            {{form.innerOrderNum}}
                        </el-form-item>                                         
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="线上订单：">
                            {{form.onlineOrderNum}}
                        </el-form-item>
                    </el-col>
                    
                    <el-col :span="6">
                        <el-form-item label="店铺名称：">
                            {{form.shopName}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="商品：">
                            {{form.goodsName}}*{{form.count}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="4">
                        <el-form-item label="异常状态：">
                            {{form.exceptionStatus}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="异常类型：">
                            {{form.exceptionType}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="店铺状态：">
                            {{form.shopStatus}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      
                    </el-col>
                </el-row>
                <el-row >
                    <el-col :span="10">
                        <el-form-item label="买家留言：">
                            {{form.buyerLeaveMsg}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="14">
                        <el-form-item label="订单备注：">
                            {{form.orderRemark}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="4">
                        <el-form-item label="收件人：">
                            {{form.buyerSjrName}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="电话：">
                            {{form.buyerSjrMobile}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="14">
                        <el-form-item label="地址：">
                          <template v-if="!!form.buyerSjrAddress">
                             {{form.buyerSjrAddress}}
                          </template>
                        <template v-else>
                             {{form.buyerSjrProvince}}{{form.buyerSjrCity}}{{form.buyerSjrCounty}}{{form.buyerSjrStreet}}{{form.buyerSjrDoorplate}}
                          </template>
                        </el-form-item>
                    </el-col>
                </el-row>  
                <el-row >
                    <el-col :span="4">
                        <el-form-item label="订单状态：">
                            {{ fmtAllLinkDaiPaiSpOrderDpState(form.dpOrderState) }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item  label="生成时间：">
                            {{form.createdTime}}
                        </el-form-item>                                         
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="代拍厂家：">
                            {{form.name}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="厂家商品：">
                            {{form.spGoodsName}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row >                            
                    <el-col :span="4">
                        <el-form-item label="下单时间：">
                            {{form.dpOrderTime}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="付款时间：">
                            {{form.dpPayTime}}
                        </el-form-item>
                    </el-col>   
                     <el-col :span="6">
                        <el-form-item label="代拍金额：">
                            {{ form.dpTotalAmount }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item  label="发货时间：">
                            {{form.sendOutTime}}
                        </el-form-item>                                         
                    </el-col>                
                </el-row>
                <el-row >
                   
                    <el-col :span="4">
                        <el-form-item label="快递单号：">
                            {{form.dpExpressNum}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="快递公司：">
                            {{form.dpExpressCompany}}
                        </el-form-item>
                    </el-col>      
                    <el-col :span="6">
                        <el-form-item label="机器人通知时间：">
                            {{form.jqrNoticeTime}}
                        </el-form-item>
                    </el-col>     
                    <el-col :span="6">
                        <el-form-item label="机器人错误信息：">
                            {{form.jqrMsg}}
                        </el-form-item>
                    </el-col>                  
                </el-row>
                
                 
                              
                
                <el-row>
                    <el-col :span="24">
                        <el-tabs>
                            <el-tab-pane label="订单明细">
                                <div :style="'height:'+ tableHeight+'px;'">
                                    <!--列表-->
                                    <ces-table ref="dtlTable" :that='that' :isIndex='false' :hasexpandRight='true' 
                                    :hasexpand='true' :tableData='dtlTableData' 
                                    :tableCols='dtlTableCols' :loading="false" :isSelectColumn="false"
                                     rowkey="id" >                                       
                                    </ces-table>
                                </div>

                            </el-tab-pane>
                        </el-tabs>
                    </el-col>
                </el-row>

                
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">  
                    <el-button @click="onClose">关闭</el-button>   
                </el-col>
            </el-row>
        </template>
    </my-container>
</template>
<script>  


    import cesTable from "@/components/Table/table.vue";
    import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode,
        AllLinkDaiPaiSpOrderDpStateOpts,fmtAllLinkDaiPaiSpOrderDpState
    } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import {
          GetDpSpOrderByIdAsync
    } from '@/api/order/alllinkDaiPai';
    


    const dtlTableCols = [
        
        { istrue: true, prop: 'yhGoodsCode', label: 'Yh商品编码', width: '120', sortable: true},
        { istrue: true, prop: 'spSkuName', label: '厂家SKU', minwidth: '160', sortable: true},
        { istrue: true, prop: 'price', label: '厂家单价', width: '100', sortable: true },
        { istrue: true, prop: 'spSkuCount', label: '订单数量', width: '100', sortable: true },
        { istrue: true, prop: 'spOrderPrice', label: '实际单价', width: '100', sortable: true },
        
        { istrue: true, prop: 'spSkuAttr1', label: 'Sku属性1', width: '120', sortable: true },
        { istrue: true, prop: 'spSkuAttr2', label: 'Sku属性2', width: '120', sortable: true },
        { istrue: true, prop: 'spSkuAttr3', label: 'Sku属性3', width: '120', sortable: true },
        { istrue: true, prop: 'spSkuAttr4', label: 'Sku属性4', width: '120', sortable: true },
       
    ];


    export default {
        name: "DpImpOrderForm",
        components: { MyContainer, MyConfirmButton, cesTable   },
        data() {
            return {              
                that: this,
                form: {
                   
                },
                dtlTableCols: dtlTableCols,
                total: 0,
                dtlTableData: [],
                //summaryarry: {},
                pageLoading: false,
                curRow: null,
                formEditMode: true,//是否编辑模式              
                mode:1,              
            };
        },
        async mounted() {
        },
        computed: {
            tableHeight() {
                let rowsCount = 1;
                if (this.dtlTableData && this.dtlTableData.length > 0) {
                    rowsCount = this.dtlTableData.length;                    
                }
                let rowsHeight = (rowsCount + 1) * 60 + 20;
                return rowsHeight > 360 ? 360 : rowsHeight;
            },       
        },
        methods: {  
            fmtAllLinkDaiPaiSpOrderDpState:fmtAllLinkDaiPaiSpOrderDpState,
            onClose(){              
                this.$emit('close');
            },             
            async loadData({oid,mode} ) {                         
                    
                this.pageLoading = true;
                this.mode=mode;
                this.formEditMode = mode!=3;

                if (oid) {
                  
                     let rlt = await GetDpSpOrderByIdAsync( { oid:oid } );
                    if (rlt && rlt.success) {
                        this.form = {...rlt.data.impOrder,...rlt.data.supplier,...rlt.data.suplierGoods,...rlt.data.order};
                        this.dtlTableData = rlt.data.dtlList==null?[]:rlt.data.dtlList;                      
                         this.pageLoading = false;
                    }
                } else {
                    Object.keys(this.form).forEach(key => (this.form[key] = null));
                    // this.form.enabled=true;
                    // this.form.yhGoodsCode='';                   
                    this.dtlTableData =[];

                    this.pageLoading = false;                    
                }
            },           
        },
    };
</script>
