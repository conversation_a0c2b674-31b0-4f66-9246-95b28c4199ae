<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;" :before-leave="beforeleave" >
            <el-tab-pane label="任务列表" name="tab0" style="height: 100%;" :lazy="true" >
                <videotasknew   
                :platformList="platformList"  :warehouselist="warehouselist" 
                :taskUrgencyList="taskUrgencyList" :groupList="groupList"  :listtype="1"
                :filter="{ isAudioComplate :0,   isShop: 0}" :erpUserInfoList="erpUserInfoList" :fpPhotoLqNameList="fpPhotoLqNameList"
                ref="videotask" style="height: 100%;"/>
            </el-tab-pane>
            <el-tab-pane label="已拍摄" name="tab1" style="height: 100%;" :lazy="true" >
                <videotasknewover   
                :platformList="platformList"  :warehouselist="warehouselist" 
                :taskUrgencyList="taskUrgencyList" :groupList="groupList" :listtype="2"
                :filter="{ isAudioComplate :0,   isShop: 0,isUploadVideo:1}"  :erpUserInfoList="erpUserInfoList"  :fpPhotoLqNameList="fpPhotoLqNameList"
                ref="videotask" style="height: 100%;"/>
            </el-tab-pane>
        <el-tab-pane label="已完成" name="tab2" style="height: 100%;" :lazy="true" >
                <videotasknewover   
                :platformList="platformList"  :warehouselist="warehouselist" 
                :taskUrgencyList="taskUrgencyList" :groupList="groupList"  :listtype="3"
                :filter="{ isAudioComplate :1,   isShop: 0}" :islook="true"  :erpUserInfoList="erpUserInfoList"  :fpPhotoLqNameList="fpPhotoLqNameList"
                ref="videotask" style="height: 100%;"/>
            </el-tab-pane>
            <el-tab-pane label="统计列表" name="tab13" style="height: 100%;" :lazy="true"   v-if="checkPermission('vedioTask-tjlb')">
                <videotasknewover   
                :platformList="platformList"  :warehouselist="warehouselist"  :listtype="4"
                :taskUrgencyList="taskUrgencyList" :groupList="groupList" :islook="true"
                :filter="{ isShop: 0, isTjInfo:1}"  :erpUserInfoList="erpUserInfoList"  :fpPhotoLqNameList="fpPhotoLqNameList"
                ref="videotask" style="height: 100%;"/>
            </el-tab-pane>
            <el-tab-pane label="存档" name="tab3" style="height: 100%;" :lazy="true"   v-if="checkPermission('vedioTask-cdlb')">
                <videotasknewover   
                :platformList="platformList"  :warehouselist="warehouselist"   :listtype="5"
                :taskUrgencyList="taskUrgencyList" :groupList="groupList" :islook="true" 
                :filter="{ isShop: 1}"  :erpUserInfoList="erpUserInfoList" :fpPhotoLqNameList="fpPhotoLqNameList"
                ref="videotask" style="height: 100%;"/>
            </el-tab-pane> 
            <el-tab-pane label="上传进度" name="tab4" style="height: 100%;" :lazy="true" >
                <uploadvideo ref="uploadvideo" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="剪切进度" name="tab5" style="height: 100%;" :lazy="true" >
                <cutedvideo ref="cutedvideo" style="height: 100%;" />
            </el-tab-pane>
            <!-- <el-tab-pane label="统计" name="tab6" style="height: 100%;" :lazy="true"  v-if="checkPermission('vedioTask-tjsqt')" >
                <videoechart  ref="videoechart" style="height: 100%;" />
            </el-tab-pane> -->
            <el-tab-pane label="统计" name="tab10" style="height: 100%;" :lazy="true"  > 
                <videoIndex  ref="videoechart" style="height: 100%;" /> 
            </el-tab-pane>
             <el-tab-pane label="设置" name="tab17" style="height: 100%;"  :lazy="true"   v-if="checkPermission('vedioTask-fpsz')">
                <shootAutoAssginmanage ref="accountsWorkCount" :listtype="2"></shootAutoAssginmanage>
            </el-tab-pane>
            <el-tab-pane name="tab99"    style="height: 100%;" :lazy="true" >
                <span slot="label">
                    <el-link type=""  style="color: #fff;" @click="toResultmatter">首页</el-link>
                </span>
            </el-tab-pane>  
        </el-tabs>
    </my-container>

</template>
<script>
    import videoIndex from '@/views/media/index/videoIndex'         
    import MyContainer from "@/components/my-container";
    import ypstask from '@/views/media/video/ypstask'
    import videotask from '@/views/media/video/videotask'
    import videotasknew from '@/views/media/video/videotasknew'
    import videotaskShop from '@/views/media/video/videotaskShop'
    import videotaskOver from '@/views/media/video/videotaskOver'
    import uploadvideo from '@/views/media/video/uploadvideo'
    import cutedvideo from '@/views/media/video/cutedvideo'
    import videobasicset from '@/views/media/video/videobasicset'
    import videoechart from '@/views/media/video/videoechart'
    import videoechartNew from '@/views/media/video/videoechartNew'
    import videotasknewover from '@/views/media/video/videotasknewover'
    import videobasicsetGroup from '@/views/media/video/videobasicsetGroup' 
    import vedioautofp from '@/views/media/video/maintable/vedioautofp' 
    import shootAutoAssginmanage from '@/views/media/shooting/shootAutoAssign/shootAutoAssginmanage';
    import { getShootOperationsGroup,getErpUserInfoView,getErpShootDeptUserInfo} from '@/api/media/mediashare'; 
    import { getDirectorGroupList} from '@/api/operatemanage/base/shop'  
    import { rulePlatform } from "@/utils/formruletools";
    import { ShootingVideoTaskUrgencyOptions } from "@/utils/tools";
    export default {
        name: "Users",
        components: { vedioautofp,MyContainer, ypstask,shootAutoAssginmanage, videotasknew,videotasknewover,videotask,
             uploadvideo, cutedvideo, videobasicset, videotaskShop, videotaskOver, videoechart, videobasicsetGroup,videoechartNew
            ,videoIndex },
        data() {
            return {
                that: this,
                pageLoading: false,
                platformList:[],//平台
                warehouselist:[],//仓库
                groupList:[],//运营组
                erpUserInfoList:[],
                fpPhotoLqNameList:[],
                taskUrgencyList :ShootingVideoTaskUrgencyOptions,
                activeName: 'tab0'
            };
        },
        provide() {
            return {
                reload: this.reload
            }
        },
        async mounted() {
            await this.getDropDownList();
        },
        methods: {
            async getDropDownList(){
                var res =await  getShootOperationsGroup({type:3}); 
                this.warehouselist =res?.map(item => { return { value: item.id, label: item.label }; });
                var pfrule = await rulePlatform();
                this.platformList = pfrule.options;  
                var res = await getDirectorGroupList();
                this.groupList = res.data?.map(item => { return { value: item.key, label: item.value }; });      
                
            
                var res = await getErpUserInfoView();
                this.erpUserInfoList = res || [];
                
                var res = await getErpShootDeptUserInfo();
                this.fpPhotoLqNameList = res || [];
            },
            toResultmatter(){
                this.$router.push({path: '/media/index/homepage'})
            },
            beforeleave(visitName, currentName ){
                
                if(this.isedit){
                    this.$message("请保存后再操作")
                }
                if(visitName== "tab99")
                    return false;
                return !this.isedit;
            },
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
