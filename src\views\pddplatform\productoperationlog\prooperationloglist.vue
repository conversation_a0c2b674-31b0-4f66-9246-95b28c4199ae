<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item style="padding: 0;margin: 0; ">
                    <el-date-picker style="width: 280px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始操作时间" end-placeholder="结束操作时间"
                        :picker-options="pickerOptions">
                    </el-date-picker>
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0; ">
                    <el-input v-model.trim="filter.proCode" style="width: 100px" placeholder="商品ID" clearable
                        maxlength="50" />
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0; ">
                    <el-select filterable v-model="filter.groupId" placeholder="运营组长" style="width: 100px" clearable>
                        <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value"
                            :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0; ">
                    <el-select filterable v-model="filter.operateSpecialId" placeholder="运营专员" clearable
                        style="width: 100px">
                        <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0; ">
                    <el-select filterable v-model="filter.user1Id" placeholder="运营助理" clearable style="width: 100px">
                        <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
                :loading="listLoading" :tableHandles="tableHandles1">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
        <el-dialog title="导入" :visible.sync="dialogVisibleUpload" width="40%" v-dialogDrag>
            <span>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                            accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleUpload = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getProductOperationLogListAsync, importProCodeActionAsync } from '@/api/pddplatform/productoperationlog';
import { getDirectorList, getDirectorGroupList } from '@/api/operatemanage/base/shop'
const tableCols = [
    { istrue: true, prop: 'actTime', label: '操作时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'proCode', label: '商品ID', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'groupId', label: '运营组', width: '100', sortable: 'custom', formatter: (row) => row.groupName || ' ' },
    { istrue: true, prop: 'operateSpecialUserId', label: '运营专员', width: '100', sortable: 'custom', formatter: (row) => row.operateSpecialUserName || ' ' },
    { istrue: true, prop: 'userId', label: '运营助理', width: '100', sortable: 'custom', formatter: (row) => row.userRealName || ' ' },
    { istrue: true, prop: 'actionor', label: '操作人', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'actModul', label: '操作模块', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'actType', label: '操作类型', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'actDetail', label: '详情', width: '600', sortable: 'custom' },
    { istrue: true, prop: 'createdTime', label: '添加时间', width: '150', sortable: 'custom' }
]
const tableHandles1 = [
    { label: "导入", handle: (that) => that.onImport() },
    { label: "下载导入模板", handle: (that) => that.onImportSyjModel() }
];
export default {
    name: 'proopeartionloglist',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow },
    props: {

    },
    data() {
        return {
            that: this,
            shopList: [],
            filter: {
                timerange: [
                    formatTime(dayjs().subtract(3, "day"), "YYYY-MM-DD"),
                    formatTime(new Date(), "YYYY-MM-DD"),
                ],
                startDate: null,
                endDate: null,
                proCode: null
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "actTime", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            pickerOptions: {
                disabledDate(time) {
                    // 设置禁用最小日期为1970年1月1日
                    const minDate = new Date(1970, 0, 1);
                    return time.getTime() < minDate.getTime();
                },
                shortcuts: [
                    {
                        text: '昨天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime());
                            picker.$emit('pick', [start, start]);
                        }
                    }, {
                        text: '近三天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime());
                            const end = new Date(new Date(tdate.toLocaleDateString()));
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一周',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 5);
                            const end = new Date(new Date(tdate.toLocaleDateString()).getTime() + 3600 * 1000 * 24 * 5);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一个月',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 31);
                            console.log("获取前一个月的时间", tdate.getDay());
                            const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
            },
            tableHandles1: tableHandles1,
            dialogVisibleUpload: false,
            fileList: [],
            fileparm: {},
            uploadLoading: false,
            directorList: [],
            directorGroupList: [],
        };
    },
    async mounted() {
        this.getDirectorlist()
        await this.onSearch()
    },
    methods: {
        async getDirectorlist() {
            const res1 = await getDirectorList({})
            const res2 = await getDirectorGroupList({})

            this.directorList = [{ key: '0', value: '未知' }].concat(res1.data || []);
            this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);
        },
        //查询第一页
        async onSearch() {
            console.log(this.filter)
            if (!this.filter.timerange) {
                this.$message({ message: "请选择操作时间", type: "warning", });
                return;
            }
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.$message({ message: "请先选择操作时间", type: "warning" });
                return false;
            }

            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const res = await getProductOperationLogListAsync(params)
            this.listLoading = false;
            if (!res?.success) {
                return
            }
            let zz = this.directorGroupList;
            let zy = this.directorList;
            res.data.list.forEach(f => {
                if (f.groupId > 0)
                    f.groupName = zz.find(x => x.key == f.groupId)?.value;
                if (f.operateSpecialUserId > 0)
                    f.operateSpecialUserName = zy.find(x => x.key == f.operateSpecialUserId)?.value;
                if (f.userId > 0)
                    f.userRealName = zy.find(x => x.key == f.userId)?.value;
            });
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            data.forEach(d => {
                d._loading = false;
            })
            this.list = data;
        },
        //排序查 询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onImport() {
            this.dialogVisibleUpload = true;
        },
        onImportSyjModel() {
            window.open("/static/excel/pddplatform/产品操作日志导入模板.xlsx", "_blank");
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            var res = await importProCodeActionAsync(form);
            if (res?.success) {
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
                this.uploadLoading = false
            } else {
                this.uploadLoading = false
            }

        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            this.dialogVisibleUpload = false;
        },
        async onUploadChange(file, fileList) {
            // let list = [];
            // list.push(file);
            this.fileList = fileList;
        },
        onUploadRemove(file, fileList) {
            this.fileList = []
        },
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        }
    },
};
</script>

<style lang="scss" scoped></style>
