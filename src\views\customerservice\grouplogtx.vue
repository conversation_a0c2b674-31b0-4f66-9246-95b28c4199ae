<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-button style="padding: 0;margin: 0;">
                <datepicker v-model="Filter.Sdate"></datepicker>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
                <el-input v-model="Filter.updateUser" placeholder="操作人" clearable style="width:80px;" />
            </el-button>
            <el-button style="padding: 0;margin: 0;">
                <el-input v-model="Filter.updateType" placeholder="操作类型" clearable style="width:120px;" />
            </el-button>
            <el-button style="padding: 0;margin: 0;">
                <el-input v-model.trim="Filter.updateContextText" placeholder="操作内容" clearable style="width:120px;" />
            </el-button>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='inquirslist' @select='selectchange' :isSelection='false' :tableCols='tableCols'
            :loading="listLoading">
            <template slot='extentbtn'>
                <el-button type="primary" @click="onSearch">查询</el-button>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getinquirsList" />
        </template>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import { GetGroupLogList } from '@/api/customerservice/group'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

const tableCols = [
    { istrue: true, prop: 'createdTime', label: '操作时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'createdUserName', label: '操作人', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'updateType', label: '操作类型', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'updateContextText', label: '操作内容', width: '900', sortable: 'custom' },
];
export default {
    name: "grouplogtx",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker },
    data() {
        return {
            that: this,
            Filter: {
                Sdate: [formatTime(new Date(new Date().getTime() - 3600 * 1000 * 24 * 30), "YYYY-MM-DD 00:00:00"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")],
            },
            inquirslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "createdTime", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
        };
    },
    async mounted() {
    },
    methods: {
        loadData() {
            this.onSearch();
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getinquirsList();
        },
        async getinquirsList() {
            if (this.Filter.Sdate) {
                this.Filter.startSdate = this.Filter.Sdate[0];
                this.Filter.endSdate = this.Filter.Sdate[1];
            }
            else {
                this.Filter.startSdate = null;
                this.Filter.endSdate = null;
            }
            const para = { ...this.Filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };

            this.listLoading = true;
            const res = await GetGroupLogList(params);
            this.listLoading = false;
            this.total = res.data.total;
            this.inquirslist = res.data.list;
            console.dir(this.inquirslist);
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
