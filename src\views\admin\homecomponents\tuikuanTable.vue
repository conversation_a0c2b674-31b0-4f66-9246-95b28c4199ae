<template>
    <my-container>

        <ces-table ref="table" size="mini" :that="that" :is-index="true" :hasexpand="true" :table-data="list" :table-cols="tableCols" :showsummary="true" @sortchange='sortchange'  :isSelectColumn="false" style="width: 100%; height: 95%; margin: 0">

        </ces-table>

    </my-container>
</template>

<script>
    import { getHomeStockout } from '@/api/inventory/abnormal'
    import MyContainer from "@/components/my-container";
    import { formatmoney } from "@/utils/tools";
    import cesTable from "@/components/Table/table.vue";
    import { formatNoLink } from "@/utils/tools";
    import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
    import VueClipboard from 'vue-clipboard2'
    const tableCols = [
        { istrue: true, prop: 'goodsCode', label: '编码', type: 'click', handle: (that, row) => that.copyCode(row), tipmesg: '点击复制', width: '120' },
        { istrue: true, prop: 'goodsName', label: '商品名称' },
        { istrue: true, prop: 'picture', label: '图片', width: '60', type: 'image' },
        { istrue: true, prop: 'tuikuanOrderNum', label: '退款订单数', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'tuikuanGoodNum', label: '退款数量', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'tuikuanAmount', label: '退款金额', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'tuikuanAvgTimeStr', label: '退款时长均值', width: '120', sortable: 'custom', tipmesg: '（申请时间-付款时间）' }
    ]

    //格式化数值列：大于1 时，去掉小数点，小于1时保留小数点
    var myformatnum = function (value) {
        var num = formatmoney(
            Math.abs(value) > 1 ? Math.round(value, 2) : Math.round(value, 1)
        );
        return num
    };
    export default {
        components: { cesTable, MyContainer, ElImageViewer },
        props: {
            tuikuanList: [],
            sortchange: null
        },
        filters: {
            mynum (val) {
                return myformatnum(val)
            }
        },
        watch: {
            tuikuanList: {
                deep: true,
                handler (val) {
                    this.getlist(val)
                }
            }
        },
        data () {
            return {
                that: this,
                list: null,
                mgList: [],
                tableCols: tableCols,
                pagelodaing: false,
                showGoodsImage: false,
            }
        },
        mounted () {
        },
        methods: {

            async linkstockout (row) {
                if (row != null) {
                    this.$router.push({ path: '/customerservice/aftersaleanalysis', query: { goodsCode: row, saleAfter_Type: 3 } })
                }
            },

            getlist (val) {
                this.pagelodaing = true
                this.list = JSON.parse(JSON.stringify(val))
                this.list = this.list.map((item,index)=>{
                    item.tuikuanOrderNum =  myformatnum(item.tuikuanOrderNum)
                    item.tuikuanGoodNum =  myformatnum(item.tuikuanGoodNum)
                    item.tuikuanAmount = myformatnum(item.tuikuanAmount)
                    return item
                })
                //.slice(0, 8)
                this.pagelodaing = false
            },
            copyCode (row) {
                var that = this;
                this.$copyText(row.goodsCode).then(function (e) {
                    that.$message({ type: 'success', message: '复制成功' });
                }, function (e) {
                    that.$message({ type: 'success', message: '复制失败' });
                })

            }
        }
    }
</script>

<style lang="scss" scoped>
    .el-table--scrollable-x .el-table__body-wrapper {
        overflow: auto;
    }
</style>
<style >
    .el-image__inner {
        max-width: 50px;
        max-height: 50px;
    }
</style>

