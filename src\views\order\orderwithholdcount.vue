<template>
    <my-container>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' @select='selectchange' :isSelection='false'
         :isSelectColumn="false" :tableData='productlist' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading"/>
        <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
        </template>
    </my-container>
</template>

<script>
import { getOrderWithholdCount } from '@/api/order/orderdeductmoney'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import {formatLinkProCode, formatPlatform} from "@/utils/tools";

const tableCols =[
      //{istrue:true,prop:'payTime',label:'支付日期', width:'90',sortable:'custom',},
      {istrue:true,prop:'platform',label:'平台', width:'60',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
      {istrue:true,prop:'proCode',label:'宝贝ID', width:'120',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
      {istrue:true,prop:'shopCode',label:'店铺', width:'90',sortable:'custom',formatter:(row)=>row.shopName},
      {istrue:true,prop:'orderNoInner',label:'内部订单号', width:'90',sortable:'custom',},
      //{istrue:true,prop:'amountPaid',label:'扣款金额', width:'90',sortable:'custom',},
      {istrue:true,prop:'occurrenceTime',label:'扣款时间', width:'auto',sortable:'custom',},
     ];
const tableHandles1=[]

export default {
    name: 'YunhanAdminOrderwithholdcount',
    components:{MyContainer,MyConfirmButton,cesTable},

    data() {
        return {
            that:this,
            filter: {
                occurrenceTime: null,
                goodsCode: null,
                brandRate:null,
                platform:null,
                categoryids:[],
            },
            pager:{OrderBy:"id",IsAsc:false},
            tableCols:tableCols,
            tableHandles:tableHandles1,
            productlist: [],
            total: 0,
            sels: [], 
            selids: [], 
            listLoading: false,
        };
    },

    async mounted() {
        
    },

    methods: {
        async onSearch(occurrenceTime, goodsCode,platform){
            this.filter.goodsCode = goodsCode
            this.filter.occurrenceTime = occurrenceTime
            this.filter.platform = platform
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            var pager = this.$refs.pager.getPager()

            if(this.filter.categoryids != null && this.filter.categoryids.length > 0)
            {
                this.filter.productCategoryId = this.filter.categoryids[this.filter.categoryids.length-1]
            }
            const params = {
                ...pager,
                ...this.pager,
                ... this.filter
            }
            this.listLoading = true
            const res = await getOrderWithholdCount(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total
            const data = res.data.list
            data.forEach(d => {
                d._loading = false
            })
            this.productlist = data
            this.selids=[]
        },
        sortchange(column){
            if(!column.order)
                this.pager={};
            else
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};

            this.getlist();
        },
        selsChange: function(sels) {
            this.sels = sels
        },
        selectchange:function(rows,row) {
            this.selids=[];
            rows.forEach(f=>{
                this.selids.push(f.id);
            })
        },
    },
};
</script>

<style lang="scss" scoped>

</style>