<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" :clearable="false"
          @change="changeTime">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.skc" placeholder="SKC" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.sku" placeholder="SKU" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')" style="width: 70px">搜索</el-button>
        <el-button type="primary" @click="showCalDayRepoty">计算</el-button>
        <el-button type="primary" @click="onExport" v-if="checkPermission('sheInInventory_Management_export')">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'shelnWarehousingVariance202408041735'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :border="true"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%; margin: 0" v-loading="loading"
      :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="计算" :visible.sync="dialogCalDayRepotVis" width="40%" v-dialogDrag>
      <span>
        <el-row>
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker style="width: 50%; float: left;" v-model="requestYearMonthDay" type="date" format="yyyyMMdd"
              value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
            <el-button type="success" style="margin-left:  30px;" @click="calDayRepoty">计算</el-button>
          </el-col>
        </el-row>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import dayjs from 'dayjs'
import { getCompanySendGoodsSheIn, calCompanySendGoodsSheIn ,exportCompanySendGoodsSheIn} from '@/api/bookkeeper/reportdayV2'

const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'yearMonthDay', label: '日期', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'skc', label: 'SKC', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'sku', label: 'SKU', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '计算时间', },
  {
    label: `公司发货数据`, merge: true, prop: 'mergeField',
    cols: [
      { sortable: 'custom', width: 'auto', align: 'center', prop: 'sendBusinessType', label: '业务类型', formatter: (row) => row.sendBusinessType == 1 ? "备货" : row.sendBusinessType == 2 ? "急采" : '' },
      { sortable: 'custom', width: 'auto', align: 'center', prop: 'sendOrderNo', label: '单号', },
      { sortable: 'custom', width: 'auto', align: 'center', prop: 'sendSaleCount', label: '数量', },
    ]
  },
  {
    label: `公司入库数据`, merge: true, prop: 'mergeField1',
    cols: [
      { sortable: 'custom', width: 'auto', align: 'center', prop: 'inBusinessType', label: '业务类型', formatter: (row) => row.inBusinessType == 1 ? "备货" : row.inBusinessType == 2 ? "急采" : '' },
      { sortable: 'custom', width: 'auto', align: 'center', prop: 'inOrderNo', label: '单号', },
      { sortable: 'custom', width: 'auto', align: 'center', prop: 'inSaleCount', label: '数量', },
    ]
  },
]
export default {
  name: "sheInWarehousingVariance",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        sku: null,//sku
        skc: null,//skc
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      summaryarry: {},//汇总
      dialogCalDayRepotVis: false,
      requestYearMonthDay: null,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async calDayRepoty() {
      if (!this.requestYearMonthDay) {
        this.$message.error('请选择日期')
        return
      }
      const params = { RequestYearMonthDay: this.requestYearMonthDay }
      const { success } = await calCompanySendGoodsSheIn(params)
      if (success) {
        this.$message.success('计算成功')
        this.dialogCalDayRepotVis = false
        this.getList()
      } else {
        this.$message.error('计算失败')
      }
    },
    showCalDayRepoty() {
      this.requestYearMonthDay = null
      this.dialogCalDayRepotVis = true
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
      // await this.getList()
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
      }
      if (this.timeRanges.length == 0) {
        //默认给近1天时间
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      const replaceArr = ['sku', 'skc']
      this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
      this.loading = true
      const { data, success } = await getCompanySendGoodsSheIn(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    async onExport() {
     if (this.onExporting) return;
     try{
        if (this.timeRanges.length == 0) {
        //默认给近1天时间
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      const replaceArr = ['sku', 'skc']
      this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
        this.uploadLoading = true;
        const params = {...this.pager,...this.ListInfo}
        var res= await exportCompanySendGoodsSheIn(params);
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','希音公司发货与平台入库差异_' + new Date().toLocaleString() + '.xlsx' )
        this.uploadLoading = false;

        aLink.click()
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
      this.onExporting=false;
     },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
