<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-button type="primary" @click="handleAdd(true)" v-if="checkPermission('costSettings')">成本设置</el-button>
        <el-button type="primary" @click="onExport()">导出</el-button>
        <el-button type="primary" @click="importProps" v-if="isCheckPermission">导入</el-button>
        <el-button type="primary" @click="downloadTemplate">下载 隔热膜常规编码-模板</el-button>
        <el-button type="primary" @click="getList('search')">刷新</el-button>
      </div>
    </template>
    <vxetablebase ref="table" :that="that" :isIndex="true" :hasexpand="true" :tablefixed="true" @sortchange="sortchange"
      :tableData="tableData" :tableCols="tableCols" :isSelection="false" :isSelectColumn="false"
      style="width: 100%; margin: 0" v-loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" :pageSize="20" />
    </template>


    <!-- 成本设置 -->
    <el-dialog title="成本设置" :visible.sync="drawer" width="90%" :close-on-click-modal="false" v-dialogDrag>
      <el-button type="text" @click="addProps" v-if="isCbSet">新增一行</el-button>
      <el-table :data="formData" style="width: 95%; height: 95%" max-height="400">
        <el-table-column label="#" type="index" width="50" />
        <el-table-column prop="goodsCode" label="商品编码" width="100">
          <template #default="{ row, $index }">
            <el-input v-model="row.goodsCode" placeholder="商品编码" maxlength="20"
              @change="changeGoodsCode(row, $index)" />
          </template>
        </el-table-column>
        <el-table-column prop="goodsName" label="商品名称" width="100">
          <template #default="{ row }">
            <el-input v-model="row.goodsName" placeholder="商品名称" maxlength="60" />
          </template>
        </el-table-column>
        <el-table-column prop="sheetLength" label="长" width="75">
          <template #default="{ row, $index }">
            <el-input-number v-model="row.sheetLength" :precision="3" :controls="false" label="长" class="iptCss"
              :min="0" :max="10000" @change="computeSquareMeter($index)" />
          </template>
        </el-table-column>
        <el-table-column prop="sheetWidth" label="宽" width="75">
          <template #default="{ row, $index }">
            <el-input-number v-model="row.sheetWidth" :precision="3" :controls="false" label="宽" class="iptCss" :min="0"
              :max="10000" @change="computeSquareMeter($index)" />
          </template>
        </el-table-column>
        <el-table-column prop="squareMeter" label="平方米" width="75">
          <template #default="{ row }">
            <el-input-number v-model="row.squareMeter" :precision="3" :controls="false" label="平方米" class="iptCss"
              :min="0" disabled />
          </template>
        </el-table-column>
        <el-table-column prop="perimeter" label="周长（米）" width="75">
          <template #default="{ row }">
            <el-input-number v-model="row.perimeter" :precision="3" :controls="false" label="周长（米）" class="iptCss"
              :min="0" disabled />
          </template>
        </el-table-column>
        <el-table-column prop="isBB" label="是否包边" width="65">
          <template #default="{ row }">
            <el-input v-model="row.isBB" placeholder="是否包边" maxlength="20" />
          </template>
        </el-table-column>
        <el-table-column prop="isDK" label="是否打卡扣" width="65">
          <template #default="{ row }">
            <el-input v-model="row.isDK" placeholder="是否打卡扣" maxlength="20" />
          </template>
        </el-table-column>
        <el-table-column prop="rawMaterialBasisCost" label="原材料成本" width="75">
          <template #default="{ row, $index }">
            <el-input-number v-model="row.rawMaterialBasisCost" :precision="3" :controls="false" label="原材料成本"
              class="iptCss" :min="0" :max="10000" @change="computeSquareMeter($index)" />
          </template>
        </el-table-column>
        <el-table-column prop="metreBBClothCost" label="包边一米价格" width="75">
          <template #default="{ row, $index }">
            <el-input-number v-model="row.metreBBClothCost" :precision="3" :controls="false" label="包边一米价格"
              class="iptCss" :min="0" :max="10000" @change="computeSquareMeter($index)" />
          </template>
        </el-table-column>
        <el-table-column prop="sheetBBPackProcessCost" label="包边加工费" width="75">
          <template #default="{ row, $index }">
            <el-input-number v-model="row.sheetBBPackProcessCost" :precision="3" :controls="false" label="包边加工费"
              class="iptCss" :min="0" :max="10000" @change="computeSquareMeter($index)" />
          </template>
        </el-table-column>
        <el-table-column prop="ironBuckleSquareMeterCost" label="单个铁扣" width="75">
          <template #default="{ row, $index }">
            <el-input-number v-model="row.ironBuckleSquareMeterCost" :precision="3" :controls="false" label="单个铁扣"
              class="iptCss" :min="0" :max="10000" @change="computeSquareMeter($index)" />
          </template>
        </el-table-column>
        <el-table-column prop="sheetDKPackProcessCost" label="打扣加工费" width="75">
          <template #default="{ row, $index }">
            <el-input-number v-model="row.sheetDKPackProcessCost" :precision="3" :controls="false" label="打扣加工费"
              class="iptCss" :min="0" :max="10000" @change="computeSquareMeter($index)" />
          </template>
        </el-table-column>
        <el-table-column prop="suctionCupSquareMeterCost" label="单个吸盘" width="75">
          <template #default="{ row, $index }">
            <el-input-number v-model="row.suctionCupSquareMeterCost" :precision="3" :controls="false" label="单个吸盘"
              class="iptCss" :min="0" :max="10000" @change="computeSquareMeter($index)" />
          </template>
        </el-table-column>
        <el-table-column prop="sheetCroppingCost" label="单张裁剪" width="75">
          <template #default="{ row, $index }">
            <el-input-number v-model="row.sheetCroppingCost" :precision="3" :controls="false" label="单张裁剪"
              class="iptCss" :min="0" :max="10000" @change="computeSquareMeter($index)" />
          </template>
        </el-table-column>
        <el-table-column prop="rawMaterialCaclueCost" label="原材料成本" width="75">
          <template #default="{ row }">
            <el-input-number v-model="row.rawMaterialCaclueCost" :precision="3" :controls="false" label="原材料成本"
              class="iptCss" :min="0" disabled />
          </template>
        </el-table-column>
        <el-table-column prop="surroundEdgeCost" label="包边成本" width="75">
          <template #default="{ row }">
            <el-input-number v-model="row.surroundEdgeCost" :precision="3" :controls="false" label="包边成本" class="iptCss"
              :min="0" disabled />
          </template>
        </el-table-column>
        <el-table-column prop="ironBuckleCost" label="铁扣成本" width="75">
          <template #default="{ row }">
            <el-input-number v-model="row.ironBuckleCost" :precision="3" :controls="false" label="铁扣成本" class="iptCss"
              :min="0" disabled />
          </template>
        </el-table-column>
        <el-table-column prop="suctionCupCost" label="吸盘成本" width="75">
          <template #default="{ row }">
            <el-input-number v-model="row.suctionCupCost" :precision="3" :controls="false" label="吸盘成本" class="iptCss"
              :min="0" disabled />
          </template>
        </el-table-column>
        <el-table-column prop="totalCost" label="总成本" width="80">
          <template #default="{ row }">
            <el-input-number v-model="row.totalCost" :precision="3" :controls="false" label="总成本" class="iptCss"
              :min="0" disabled />
          </template>
        </el-table-column>
        <el-table-column prop="totalCost" label="快递费" width="80">
          <template #default="{ row }">
            <el-input-number v-model="row.orderFreightBagFee" :precision="3" :controls="false" label="总成本"
              class="iptCss" :min="0" />
          </template>
        </el-table-column>
        <el-table-column prop="totalCost" label="打包费" width="80">
          <template #default="{ row }">
            <el-input-number v-model="row.orderPackProcessFee" :precision="3" :controls="false" label="总成本"
              class="iptCss" :min="0" />
          </template>
        </el-table-column>
        <el-table-column prop="sheetSquareSaleAmount" label="操作" width="80" v-if="isCbSet">
          <template #default="{ row, $index }">
            <el-button type="danger" @click="removeRow($index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="btnGroup">
        <el-button style="margin-right: 10px" @click="drawer = false">取消</el-button>
        <el-button type="primary" @click="submit" v-throttle="3000">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
      <div style="display: flex; flex-direction: column; justify-content: center">
        <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
          :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
          <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
            <el-button size="small" type="primary">点击上传</el-button>
          </el-tooltip>
        </el-upload>
      </div>
      <div class="btnGroup">
        <el-button @click="importVisible = false">取消</el-button>
        <el-button type="primary" @click="sumbit">确定</el-button>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import decimal from '@/utils/decimal'
import {
  getListData,
  exportYGGRMRecordDtl,
  saveYGGRMRoutineSetAsync,
  importYGGRMRoutineSet,
} from "@/api/inventory/customNormsGoodsExtension";
const ys = {
  goodsCode: "商品编码",
  goodsName: "商品名称",
  sheetLength: "长",
  sheetWidth: "宽",
  squareMeter: "平方米",
  perimeter: "周长（米）",
  isBB: "是否包边",
  isDK: "是否打卡扣",
  rawMaterialBasisCost: "原材料成本",
  metreBBClothCost: "包边一米价格",
  sheetBBPackProcessCost: "包边加工费",
  ironBuckleSquareMeterCost: "单张铁扣",
  sheetDKPackProcessCost: "打扣加工费",
  suctionCupSquareMeterCost: "单张吸盘",
  sheetCroppingCost: "单张裁剪",
  rawMaterialCaclueCost: "单张原材料成本",
  surroundEdgeCost: "包边成本",
  ironBuckleCost: "铁扣成本",
  suctionCupCost: "吸盘成本",
  totalCost: "总成本",
};
const tableCols = [
  { sortable: "custom", width: "150", align: "left", prop: "goodsCode", label: "商品编码", },
  { sortable: "custom", width: "150", align: "left", prop: "goodsName", label: "商品名称", },
  { sortable: "custom", width: "80", align: "left", prop: "sheetLength", label: "长", },
  { sortable: "custom", width: "80", align: "left", prop: "sheetWidth", label: "宽", },
  { sortable: "custom", width: "100", align: "left", prop: "squareMeter", label: "平方米", },
  { sortable: "custom", width: "100", align: "left", prop: "perimeter", label: "周长（米）", },
  { sortable: "custom", width: "100", align: "left", prop: "isBB", label: "是否包边", },
  { sortable: "custom", width: "100", align: "left", prop: "isDK", label: "是否打卡扣", },
  { sortable: "custom", width: "100", align: "left", prop: "rawMaterialBasisCost", label: "原材料成本", },
  { sortable: "custom", width: "120", align: "left", prop: "metreBBClothCost", label: "包边一米价格", },
  { sortable: "custom", width: "100", align: "left", prop: "sheetBBPackProcessCost", label: "包边加工费", },
  { sortable: "custom", width: "150", align: "left", prop: "ironBuckleSquareMeterCost", label: "单张铁扣", },
  { sortable: "custom", width: "100", align: "left", prop: "sheetDKPackProcessCost", label: "打扣加工费", },
  { sortable: "custom", width: "150", align: "left", prop: "suctionCupSquareMeterCost", label: "单张吸盘", },
  { sortable: "custom", width: "100", align: "left", prop: "sheetCroppingCost", label: "单张裁剪", },
  { sortable: "custom", width: "100", align: "left", prop: "rawMaterialCaclueCost", label: "单张原材料成本", },
  { sortable: "custom", width: "100", align: "left", prop: "surroundEdgeCost", label: "包边成本", },
  { sortable: "custom", width: "100", align: "left", prop: "ironBuckleCost", label: "铁扣成本", },
  { sortable: "custom", width: "100", align: "left", prop: "suctionCupCost", label: "吸盘成本", },
  { sortable: "custom", width: "100", align: "left", prop: "totalCost", label: "总成本", },
  { sortable: "custom", width: "100", align: "left", prop: "orderFreightBagFee", label: "快递袋费", },
  { sortable: "custom", width: "100", align: "left", prop: "orderPackProcessFee", label: "打包费", },
];
const ggList = [
  {
    label: "1.0",
    value: 1,
  },
  {
    label: "1.5",
    value: 0.7,
  },
  {
    label: "2.0",
    value: 1.1,
  },
  {
    label: "3.0",
    value: 1.4,
  },
  {
    label: "5.0",
    value: 2.2,
  },
];
export default {
  name: "scanCodePage",
  components: { MyContainer, vxetablebase },
  data() {
    return {
      ggList,
      ys,
      tableCols,
      formData: [
        {
          id: null,
          goodsCode: null, //商品编码
          goodsName: null, //"商品编码"
          sheetLength: null, // "长"
          sheetWidth: null, //"宽"
          squareMeter: null, // "平方米",
          perimeter: null, //"周长（米）",
          isBB: null, //"是否包边",
          isDK: null, //"是否打卡扣",
          rawMaterialBasisCost: null, //"原材料成本",
          metreBBClothCost: null, //"包边一米价格",
          sheetBBPackProcessCost: null, // "包边加工费",
          ironBuckleSquareMeterCost: null, //"单张铁扣",
          sheetDKPackProcessCost: null, //"打扣加工费",
          suctionCupSquareMeterCost: null, //"单张吸盘",
          sheetCroppingCost: null, //"单张裁剪",
          rawMaterialCaclueCost: null, //"原材料成本",
          surroundEdgeCost: null, //"包边成本",
          ironBuckleCost: null, //"铁扣成本",
          suctionCupCost: null, //"吸盘成本",
          totalCost: null, //"总成本",
        },
      ],
      that: this,
      ListInfo: {
        orderBy: null,
        isAsc: false,
        currentPage: 1,
        pageSize: 50
      },
      tableData: [],
      total: 0,
      isCheckPermission: false,
      loading: false,
      drawer: false,
      isCbSet: true,
      dialogVisible: false,
      fileList: [],
      removeIds: [],
      saveData: {},
      importVisible: false,
      importLoading: false,
    };
  },
  async mounted() {
    this.isCheckPermission = true;
    await this.getList();
  },
  methods: {
    computeSquareMeter(i) {
      const item = this.formData[i];
      //平方米 = 长 * 宽 
      item.squareMeter = decimal(item.sheetWidth, item.sheetLength, 3, '*');
      //周长=( 长 + 宽 ) * 2 
      item.perimeter = decimal(decimal(item.sheetWidth, item.sheetLength, 3, '+'), 2, 3, '*')
      //原材料成本2 = 平方米 * 原材料成本 
      item.rawMaterialCaclueCost = decimal(item.squareMeter, item.rawMaterialBasisCost, 3, '*');
      //包边成本 = (周长 * 包边一米价格) + 包边加工费 ==> 更改为 (包边加工费 + 包边一米价格) * 周长
      item.surroundEdgeCost = decimal(decimal(item.sheetBBPackProcessCost, item.metreBBClothCost, 3, '+'), item.perimeter, 3, '*');
      //铁扣成本 =（平方米 * 单张铁扣）+ 打扣加工费 ==> 更改为 单张铁扣 + 打扣加工费
      item.ironBuckleCost = decimal(item.ironBuckleSquareMeterCost, item.sheetDKPackProcessCost, 3, '+');
      //吸盘成本 = 平方米 * 单张吸盘 ==> 更改为 单张吸盘
      item.suctionCupCost = item.suctionCupSquareMeterCost
      // //总成本 = 单张裁剪 + 原材料成本 + 包边成本 + 铁扣成本 + 吸盘成本
      item.totalCost = decimal(decimal(decimal(decimal(item.sheetCroppingCost, item.rawMaterialCaclueCost, 3, '+'), item.surroundEdgeCost, 3, '+'), item.ironBuckleCost, 3, '+'), item.suctionCupCost, 3, '+');
    },
    changeGoodsCode(e, a) {
      this.formData.forEach((item, index) => {
        if (item.goodsCode == e.goodsCode && index != a) {
          this.$message(`第${a + 1}行,商品编码存在重复值,请检查`);
          return false;
        }
      });
    },
    //下载导入模板
    downloadTemplate() {
      window.open("../../static/excel/inventory/隔热膜常规编码模板.xlsx", "_self");
    },

    //列表渲染
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true;
      // 使用时将下面的方法替换成自己的接口
      const { data, success } = await getListData(this.ListInfo);
      if (success) {
        data.list.forEach(item => {
          //平方 = 长 * 宽
          item.squareMeter = decimal(item.sheetWidth, item.sheetLength, 3, '*');
          //周长=( 长 + 宽 ) * 2
          item.perimeter = decimal(decimal(item.sheetWidth, item.sheetLength, 3, '+'), 2, 3, '*')
          //原材料成本2 = 平方米 * 原材料成本
          item.rawMaterialCaclueCost = decimal(item.squareMeter, item.rawMaterialBasisCost, 3, '*');
          //包边成本 = (周长 * 包边一米价格) + 包边加工费 ==> 更改为 (包边加工费 + 包边一米价格) * 周长
          item.surroundEdgeCost = decimal(decimal(item.sheetBBPackProcessCost, item.metreBBClothCost, 3, '+'), item.perimeter, 3, '*');
          //铁扣成本 =（平方米 * 单张铁扣）+ 打扣加工费 ==> 更改为 单张铁扣 + 打扣加工费
          item.ironBuckleCost = decimal(item.ironBuckleSquareMeterCost, item.sheetDKPackProcessCost, 3, '+');
          //吸盘成本 = 平方米 * 单张吸盘 ==>更改为 单张吸盘
          item.suctionCupCost = item.suctionCupSquareMeterCost
          // //总成本 = 单张裁剪 + 原材料成本 + 包边成本 + 铁扣成本 + 吸盘成本
          item.totalCost = decimal(decimal(decimal(decimal(item.sheetCroppingCost, item.rawMaterialCaclueCost, 3, '+'), item.surroundEdgeCost, 3, '+'), item.ironBuckleCost, 3, '+'), item.suctionCupCost, 3, '+');
        })
        this.tableData = data.list;
        this.total = data.total;
        this.loading = false;
      } else {
        //获取列表失败
        this.loading = false;
        this.$message.error("获取数据失败");
      }
    },
    //成本设置
    async handleAdd(isCbSet) {
      //const { data, success } = await getListData(this.ListInfo);
      // if (success) {
      //   this.formData = this.tableData;
      //   this.drawer = true;
      // } else {
      //   this.$message.error("获取数据失败");
      //   this.drawer = true;
      // }

      this.formData = JSON.parse(JSON.stringify(this.tableData));
      this.drawer = true;
    },
    //导出
    async onExport() {
      console.log("导出");
      this.listLoading = true;
      const rlt = await exportYGGRMRecordDtl();
      this.listLoading = false;
      if (rlt && rlt.data) {
        const aLink = document.createElement("a");
        let blob = new Blob([rlt.data], { type: "application/vnd.ms-excel" });
        aLink.href = URL.createObjectURL(blob);
        aLink.setAttribute(
          "download",
          "阳光隔热膜常规款设置" + new Date().toLocaleString() + ".xlsx"
        );
        aLink.click();
        this.listLoading = false;
      }
    },
    //导入
    importProps() {
      this.fileList = [];
      this.file = null;
      this.importVisible = true;
    },
    async uploadFile(data) {
      this.file = data.file;
    },
    removeFile(file, fileList) {
      this.file = null;
    },
    async sumbit() {
      if (this.file == null) return this.$message.error("请上传文件");
      this.$message.info("正在导入中,请稍后...");
      const form = new FormData();
      form.append("upfile", this.file);
      this.importLoading = true;
      console.log(111);
      await importYGGRMRoutineSet(form)
        .then(({ success }) => {
          if (success) {
            this.$message.success("导入成功");
            this.importVisible = false;
            this.getList();
          }
          this.importLoading = false;
        })
        .catch((err) => {
          this.importLoading = false;
          this.$message.error("导入失败");
        });
    },
    //增加一行
    addProps() {
      this.formData.push({
        goodsCode: null, //"商品编码",
        goodsName: null, //"商品名称",
        sheetLength: null, //"sheetLength",
        sheetWidth: null, //"宽",
        squareMeter: null, //"平方米",
        perimeter: null, //"周长（米）",
        isBB: null, //"是否包边",
        isDK: null, //"是否打卡扣",
        rawMaterialBasisCost: null, //"原材料成本",
        metreBBClothCost: null, //"包边一米价格",
        sheetBBPackProcessCost: null, //"包边加工费",
        ironBuckleSquareMeterCost: null, //"单张铁扣",
        sheetDKPackProcessCost: null, //"打扣加工费",
        suctionCupSquareMeterCost: null, //"单张吸盘",
        sheetCroppingCost: null, //"单张裁剪",
        rawMaterialCaclueCost: null, //"原材料成本",
        surroundEdgeCost: null, //"包边成本",
        ironBuckleCost: null, //"铁扣成本",
        suctionCupCost: null, //"吸盘成本",
        totalCost: null, //"总成本",
      });
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    //表头排序
    sortchange({ order, prop }) {
      console.log(prop);
      if (prop) {
        this.ListInfo.orderBy = prop;
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false;
        this.getList();
      }
    },
    async save() {
      this.saveData.delIds = this.removeIds;
      this.saveData.entitys = this.formData;
      const { success } = await saveYGGRMRoutineSetAsync(this.saveData);
      if (success) {
        await this.getList();
        this.$message.success("保存成功");
        this.drawer = false;
      } else {
        this.$message.error("保存失败");

      }
    },
    //提交代码
    async submit() {
      this.formData.forEach((item, index) => {
        this.formData.forEach((items, indexs) => {
          if (item.goodsCode == items.goodsCode && index != indexs && item.goodsCode) {
            this.$message(`商品编码存在重复值,请检查`);
            throw new Error(`商品编码存在重复值,请检查`);
          }
        });
      });

      this.formData.forEach((item, i) => {
        for (const key in item) {
          if (
            (key == "goodsCode" ||
              key == "goodsName" ||
              key == "isBB" ||
              key == "isDK") &&
            (item[key] === null || item[key] === "")
          ) {
            this.$message.error(`第${i + 1}行【${ys[key]}】数据为空,请检查`);
            throw new Error(`第${i + 1}行【${ys[key]}】数据为空,请检查`);
          }
        }
      });
      this.save();
    },

    removeRow($index) {
      var id = this.formData[$index].id;
      this.removeIds.push(id);
      this.formData.splice($index, 1);
      console.dir(this.removeIds);
    }
    // paginate(array, page_size, page_number)
    // {
    //   if(array==null || array.length==0)
    //   {
    //      return;
    //   }
    //   // 计算分页开始的索引
    //   const start = (page_number - 1) * page_size;
    //   // 计算分页结束的索引
    //   const end = page_number * page_size;
    //   // 返回分页后的数组
    //   return array.slice(start, end);
    // }
  },
};
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}

.iptCss {
  width: 70px;
}

.btnGroup {
  margin-top: 80px;
  display: flex;
  justify-content: end;
}

::v-deep .el-table__body-wrapper {
  min-height: 300px !important;
  max-height: 400px !important;
}

::v-deep .cell {
  padding-left: 3px;
}
</style>
