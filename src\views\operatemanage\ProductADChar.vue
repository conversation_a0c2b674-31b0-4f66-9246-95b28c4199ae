<template>
  <my-container v-loading="pageLoading">
    <div>
      <span>拼多多推广分析</span>
    </div>
    <el-button-group>
      <el-button style="padding: 0;margin: 0;">
            <el-date-picker
              v-model="filter.timerange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-button>
          <button style="padding: 0; border: none;">
            <el-select filterable v-model="filter.platform" placeholder="平台" @change="onchangeplatform" clearable style="width: 80px">
              <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </button>
          <button style="padding: 0;width: 105px; border: none;">
             <el-cascader v-model="filter.categoryids" :options="categorylist" :props="{ checkStrictly: true, value: 'id' }" filterable  style="width:100%;" placeholder="类目"/>
          </button>
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.shopId" placeholder="店铺" clearable style="width: 130px">
            <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.id"/>
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.groupId" placeholder="运营组长" style="width: 100px" clearable>
            <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key"/>
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.operateSpecialId" placeholder="运营专员" clearable style="width: 100px">
            <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key"/>
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.user1Id"  placeholder="运营助理" clearable style="width: 100px">
            <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key"/>
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.user2Id"  placeholder="运营车手" clearable style="width: 100px">
            <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key"/>
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.user3Id"  placeholder="运营备用" clearable style="width: 95px">
            <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key"/>
          </el-select>
        </button>
        <button style="padding: 0; border-color:#409EFF; background-color: #409EFF; color: white; padding: 6rpx 15rpx; border-radius: 4px;"  @click="onSearch">查询</button>  
        
     </el-button-group>
    <div style="width: 1500px;height:330px ">
      <span>
        <buscharPdd ref="buscharPdd1" :analysisData="showDetailVisible1.data" v-if="showDetailVisible1.data">
        </buscharPdd>
      </span>
      <el-tabs v-model="PddactiveName" @tab-click="handleClick">
        <!-- <el-tab-pane label="拼多多总推广" name=""></el-tab-pane> -->
        <el-tab-pane label="全站推广" name="全站推广"></el-tab-pane>
        <el-tab-pane label="搜索推广" name="搜索推广"></el-tab-pane>
        <el-tab-pane label="场景展示" name="场景展示"></el-tab-pane>
        <el-tab-pane label="直播推广" name="直播推广"></el-tab-pane>
        <el-tab-pane label="明星店铺" name="明星店铺"></el-tab-pane>
      </el-tabs>
      <span>
        <buscharPdd ref="buscharPdd2" v-if="showDetailVisible2.visible" :analysisData="showDetailVisible2.data"></buscharPdd>
      </span>
    </div>
    

  </my-container>
</template>

<script>
import { getDirectorList, getDirectorGroupList, getProductBrandPageList, getList as getshopList } from '@/api/operatemanage/base/shop'
import { getList as getcategorylist } from '@/api/operatemanage/base/category'
import { platformlist} from '@/utils/tools'
import MyContainer from '@/components/my-container'
import buscharPdd from "@/components/Bus/buscharPdd";
import { treeToList, listToTree, getTreeParents } from '@/utils'
import { getProductADCharAsync } from "@/api/operatemanage/PddChart"
export default {
  name: 'ProductADChar',
  components: { MyContainer, buscharPdd },
  data () {
    return {
      PddactiveName:'全站推广',
      filter: {
        name: '',
        timerange:[],
        styleCode:null,
        brandRate:null,
        categoryids: [],
        shopId:'',
        StartTime: '',
        EndTime: '',
        DataType:''
      },
      platformlist:platformlist,
      productlist: [],
      directorList:[],
      directorGroupList:[],
      shopList:[],
      bandList:[],
      categorylist:[],
      radio: '1',
      showDetailVisible: { visible: false, title: "", data: {} },
      showDetailVisible1: { visible: false, title: "", data: {} },
      showDetailVisible2: { visible: false, title: "", data: {} },
      pageLoading: false,
    };
  },
  async mounted () {
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
    this.filter.timerange = [start, end];
    this.filter.StartTime = this.filter.timerange[0] ;
    this.filter.EndTime = this.filter.timerange[1];
    await this.getDirectorlist();
    await this.getshopList();
    await this.onSearch();

  },
  methods: {
    async handleClick (tab, event) {
      let that = this;
      this.filter.DataType = this.PddactiveName;
      const params = {...this.filter};
      const res1 = await getProductADCharAsync(params).then(res => {
        that.showDetailVisible2.visible = true;
        that.showDetailVisible2.data = res.data;
        that.showDetailVisible2.title = res.data.legend[0];
      });
      await this.$refs.buscharPdd2.initcharts()
    },
    async getcategorylist(platform) {
      const res = await getcategorylist({platform:platform })
      if (!res?.code) {
        return
      }
      const list=[];
      res.data.forEach(f=>{
         f.label=f.categoryName;
         list.push(f)
      })
      this.categorylist = listToTree(_.cloneDeep(list), {
        id: '',
        parentId: '',
        label: '所有'
      })
    },
    async onchangeplatform(val){
      this.categorylist = [];
      this.filter.categoryids = [];
      this.filter.shopId = '';
      this.filter.productCategoryId = '';
      const res1 = await getshopList({platform:val,CurrentPage:1,PageSize:300});
      this.shopList=res1.data.list
      if (val) this.getcategorylist(val)
      //const res2 = await getcategoryList({platform:val,CurrentPage:1,PageSize:100});
      //this.productCategorylist=res2.data
    },
    async getDirectorlist() {
      const res1 = await getDirectorList({})
      const res2 = await getDirectorGroupList({})    
      
      this.directorList = res1.data
      this.directorGroupList =[{key:'0',value:'未知'}].concat(res2.data ||[]);
    },
    async getshopList(){
      this.categorylist =[]
      const res1 = await getshopList({CurrentPage:1,PageSize:100});
      this.shopList = res1.data.list;
    },
    async onSearch () {
      var that = this;
      this.filter.StartTime = this.filter.timerange[0] ;
      this.filter.EndTime = this.filter.timerange[1];
      if(this.filter.categoryids != null && this.filter.categoryids.length > 0){
        this.filter.productCategoryId = this.filter.categoryids[this.filter.categoryids.length-1]
      } 
      this.filter.DataType = '';
      const par = { ...this.filter };
      console.log(par,'000000')
      const res = await getProductADCharAsync(par).then((res) => {
        that.showDetailVisible1.visible = true;
        that.showDetailVisible1.data = res.data;
        that.showDetailVisible1.title = res.data.legend[0];
      });
      await this.$refs.buscharPdd1.initcharts();
      this.filter.DataType = this.PddactiveName;
      let par1 = { ...this.filter };
      console.log(par1,'122222')
      const res1 = await getProductADCharAsync(par1).then((res) => {
        that.showDetailVisible2.visible = true;
        that.showDetailVisible2.data = res.data;
        that.showDetailVisible2.title = res.data.legend[0];
      });
      await this.$refs.buscharPdd2.initcharts()
    },
  }
};
</script>

<style lang="scss" scoped></style>
