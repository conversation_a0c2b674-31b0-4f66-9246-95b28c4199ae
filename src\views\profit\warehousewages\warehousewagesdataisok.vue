<template>
    <my-container v-loading="pageLoading">
        <template #header>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
                :isSelectColumn="false" :loading="listLoading" :tableHandles="tableHandles1">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </my-container>
</template>

<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import cesTable from "@/components/Table/table.vue";
import MySearch from "@/components/my-search";
import buschar from '@/components/Bus/buschar';
import MySearchWindow from "@/components/my-search-window";
import {
    getWarehouseWagesDataIsOkPageList
} from '@/api/profit/warehousewages';
const tableCols = [
    { istrue: true, prop: 'workDate', label: '日期', width: '120', sortable: 'custom', formatter: (row) => formatTime(row.workDate, 'YYYY-MM-DD') },
    { istrue: true, prop: 'warehouseName', label: '仓库', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'isOkStatus', label: '确认状态', width: '120', sortable: 'custom', formatter: (row) => (row.isOkStatus == 1 ? "已确认" : row.isOkStatus == -1 ? "已驳回" : row.isOkStatus == 0 ? "确认中" : "未确认") },
    { istrue: true, prop: 'createdTime', label: '操作时间', width: '150', sortable: 'custom', formatter: (row) => row.shTime != null ? row.shTime : row.createdTime },
    { istrue: true, prop: 'createdUserName', label: '操作人', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'remark', label: '备注', width: '120', sortable: 'custom' },
]
const tableHandles1 = [
    //{ label: "计算人效", handle: (that) => that.onCompute() },
    // { label: "导出", handle: (that) => that.onExport() },
];
export default {
    name: 'warehousewagesdataisok',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, buschar },
    data() {
        return {
            that: this,
            filter: {
                warehouseName: null,
                startDate: null,
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "createdTime", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            tableHandles1: tableHandles1,
        };
    },
    async mounted() {
    },
    methods: {
        async loadData(rowinfo) {
            this.filter.warehouseName = rowinfo.warehouseName;
            this.filter.startDate = rowinfo.workDate;
            this.onSearch();
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const res = await getWarehouseWagesDataIsOkPageList(params)
            this.listLoading = false;
            console.log(res, 'getWarehouseWagesDataIsOkPageList')
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            //this.summaryarry = res.data.summary;
            data.forEach(d => {
                d._loading = false;
            })
            this.list = data;
        },
        //排序查 询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            console.log(rows)
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
    },
};
</script>

<style lang="scss" scoped></style>
