<template>
  <container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <!-- <el-form-item label="仓储:">
        <el-select v-model="filter.warehouseArea"  placeholder="请选择类型" style="width: 100%">
          <el-option label="所有" value/>
          <el-option label="义乌仓" value="0"/>
          <el-option label="南昌昌东" value="1"/>
          <el-option label="南昌昌北" value="2"/>
          <el-option label="跨境" value="5"/>
          <el-option label="未知" value="-1"/>
        </el-select>
      </el-form-item> -->
        <el-form-item label="运营组:">
          <el-select v-model="filter.groupId" multiple collapse-tags clearable placeholder="请选择运营组" style="width: 140px" filterable>
            <!-- <el-option label="所有" value=""/> -->
            <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="分公司:">
          <el-select filterable v-model="filter.company" @change="changeSetCompany" collapse-tags clearable
            placeholder="分公司" style="width: 100px">
            <el-option key="义乌" label="义乌" value="义乌"></el-option>
            <el-option key="南昌" label="南昌" value="南昌"></el-option>
            <el-option key="其他" label="其他" value="其他"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="采购员:">
          <el-select v-model="filter.brandId" multiple collapse-tags clearable placeholder="请选择采购员" style="width: 140px" filterable>
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否已完结:">
          <el-select v-model="filter.isHandle" placeholder="请选择" style="width: 100px">
            <el-option label="所有" value></el-option>
            <el-option label="是" value='true'></el-option>
            <el-option label="否" value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审单状态">
          <el-select filterable clearable v-model="filter.isCheckError" placeholder="请选择" style="width: 110px">
            <el-option label="跟进" value="0"></el-option>
            <el-option label="审错" value='1'></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商品编码:">
          <el-input v-model="filter.goodsCode" style="width: 110px" />
        </el-form-item>
        <el-form-item label="商品名称:">
          <el-input v-model="filter.goodsName" style="width: 120px" />
        </el-form-item>
        <el-form-item label="原因:">
          <el-input v-model="filter.reason" style="width: 110px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
    <ces-table ref="table" :that='that' :isIndex='true' @sortchange='sortchange' @select='selectchange'
      :isSelection='true' @cellclick='cellclick' :hasexpand='true' :tableData='list' :tableCols='tableCols'
      :tableHandles='tableHandles' :loading="listLoading" :showsummary='true' :summaryarry='summaryarry'>
      <template slot='extentbtn'>
        <el-button-group>
          <el-button style="margin: 0;" @click="getimportlist">
            {{ lastUpdateTime }}
          </el-button>
          <el-button style="margin: 0;">
            {{ reasonRate }}
          </el-button>
        </el-button-group>
      </template>
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>
    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%">
      <el-alert title="温馨提示：请选取两个文件，文件名需分别包含“订单”、“库存”。" type="success" :closable="false">
      </el-alert>
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="2" action accept=".xlsx"
          :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }} </el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-drawer title="处理" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
      :visible.sync="editVisible" direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
      <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options" />
      <div class="drawer-footer">
        <el-button @click.native="editVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="editLoading" @click="onEditSubmit" />
      </div>
    </el-drawer>

    <!-- <el-popover ref="editPopover" placement="bottom-end" v-model="visiblepopover" :reference="prevTarget" :key="popperFlag">
      <el-table :data="recodelist">
        <el-table-column width="150" property="createdTime" label="时间"></el-table-column>
        <el-table-column width="100" property="createdUserName" label="编辑人"></el-table-column>
        <el-table-column width="100" property="planArrivalTime" label="预计到货日期">
           <template slot-scope="scope">
             <span>{{formatTime(scope.row["planArrivalTime"],'YYYY-MM-DD')}}</span>
          </template>
        </el-table-column>
        <el-table-column width="200" property="reason" label="原因"></el-table-column>
        <el-table-column width="150" property="remark" label=" 解决方案">
          <template slot-scope="scope">

             <div class="wendang" v-html="scope.row['remark']" @click="showImg($event)"></div>
          </template>
        </el-table-column>
      </el-table>
    </el-popover> -->

    <el-dialog :visible.sync="visiblepopover" v-dialogDrag width="80%">
      <goodscoderecord ref="goodscoderecord" :filter="goodscoderecordfilter" style="height: 400px"></goodscoderecord>
    </el-dialog>

    <el-popover ref="detailPopover" placement="bottom-end" v-model="visiblepopoverdetail" :reference="prevTarget"
      :key="('detail' + popperFlagdetail.toString())">
      <el-table :data="detaillist">
        <el-table-column width="150" property="firstOrderTime" label="压单日期"></el-table-column>
        <el-table-column width="150" property="warehouseAreaName" label="仓储" show-overflow-tooltip></el-table-column>
        <el-table-column width="120" property="goodsCode" label="商品编码"></el-table-column>
        <el-table-column width="75" property="isCheckError" label="审单状态">
          <template slot-scope="scope">
            <div class="wendang" v-html="formatIsCheckError(scope.row['isCheckError'])"></div>
          </template>
        </el-table-column>
        <el-table-column label="压单数" width="65" property="waitOrderNum">
          <template slot-scope="scope">
            <el-button @click="showOrderDetail(scope.row['id'])" type="text"
              size="small">{{ scope.row["waitOrderNum"] }}</el-button>
          </template>
        </el-table-column>
        <el-table-column width="70" property="waitGoodNum" label="压品数"></el-table-column>
        <el-table-column width="100" property="totalWaitOrderNum" label="累计压单数"></el-table-column>
        <el-table-column width="100" property="totalWaitGoodNum" label="累计压品数"></el-table-column>
        <el-table-column width="80" property="waitDays" label="压单天数"></el-table-column>
        <el-table-column width="80" property="ninetyDaysNum" label="90天次数"></el-table-column>
      </el-table>

    </el-popover>
    <el-dialog :visible.sync="dialogOrderDetailVisible">
      <orderabnormalorderdetail ref="boxorder"></orderabnormalorderdetail>
    </el-dialog>

    <div class="imgDolg" v-show="imgPreview.show" @click.stop="imgPreview.show = false">
      <i class="el-icon-close" id="imgDolgClose" @click.stop="imgPreview.show = false"></i>
      <img @click.stop="imgPreview.show = true" :src="imgPreview.img" />
    </div>

    <!-- 时间线弹框 -->
    <el-dialog title="" :visible.sync="importtimedialogVisible" v-dialogDrag width="30%">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>当天导入时间记录</span>
        </div>
        <div style="height:400px;overflow-y:auto" class="text item">
          <el-alert v-for="item in importtimelist" :key="item" title="" type="success" :closable="false">
            导入时间 : {{ item.createdTime }}
          </el-alert>
        </div>
      </el-card>
    </el-dialog>
  </container>
</template>
<script>
import {
  pageAbnormalInventory,  editAbnormalInventory, queryAbnormalInventoryDetail, getLastUpdateTime, getAllAbnormalCheckErrorOderNo, getAllAbnormalCheckErrorGoodsCode, getAllAbnormalReasonRate,
  queryAbnormalInventory, queryAbnormalInventoryRecord, queryAbnormalOderDetail, exportAbnormalInventory, GetLastUpdateTimeAbnormalInventory
} from '@/api/inventory/abnormal'
import {
   importAbnormal
} from '@/api/inventory/abnormalImport'
import { upLoadImage } from '@/api/upload/file'
import { getAllProBrand } from '@/api/inventory/warehouse'
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import { formatTime, formatYesornoBool, formatPlatform, formatNoLink, formatIsCheckError } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import goodscoderecord from '@/views/inventory/goodscoderecord'
import { throttle } from 'throttle-debounce';
import orderabnormalorderdetail from '@/views/inventory/orderabnormalOrderDetail';
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";
const tableCols = [
  //{istrue:true,prop:'firstOrderTime',label:'首次压单日期',  width:'110',sortable:'custom',formatter:(row)=>formatTime(row.firstOrderTime,'MM-DD HH:mm:ss')}, `color:${(!row.isReportToday?"red":"green")}`},
  { istrue: true, prop: 'firstOrderTimeCurrent', label: '压单日期', width: '80', sortable: 'custom', formatter: (row) => formatTime(row.firstOrderTimeCurrent, 'MM-DD HH:mm:ss') },
  { istrue: true, prop: 'goodsImage', label: '图片', width: '60', type: 'image' },
  {
    istrue: true, prop: 'isHandle', label: '状态', width: '60', type: 'html', formatter: (row) => {
      return `<a href="javascript:void(0);"  style="font-weight:bold;font-size:small;color:${(row.isHandle == true ? "green" : "red")};">${(row.isHandle == true ? "已完结" : "未完结")}</a>`;
    }
  },
  { istrue: true, prop: 'handleTime', label: '完结时间', width: '105', sortable: 'custom', formatter: (row) => formatTime(row.handleTime, 'MM-DD HH:mm:ss') },
  { istrue: true, prop: 'sellStock', label: '库存数', width: '90', sortable: 'custom', type: 'html', formatter: (row) => formatNoLink(row.sellStock) },
  { istrue: true, prop: 'goodsCode', label: '商品编码', width: '110', sortable: 'custom' },
  { istrue: true, prop: 'goodsName', label: '商品名称', width: '160', sortable: 'custom' },
  {
    istrue: true, prop: 'isCheckError', label: '审单状态', width: '50', type: 'html', formatter: (row) =>
      row.isCheckError == 0 ? '<a href="javascript:void(0);" style="color:#606266;">跟进</a>' :
        row.isCheckError == 1 ? '<a href="javascript:void(0);" style="color:#dc0909">审错</a>' : " "
  },
  { istrue: true, prop: 'waitOrderNumCurrent', label: '压单数', width: '70', sortable: 'custom', type: 'html', formatter: (row) => formatNoLink(row.waitOrderNumCurrent) },
  { istrue: true, prop: 'waitGoodNumCurrent', label: '压品数', width: '70', sortable: 'custom' },
  { istrue: true, prop: 'waitDaysc', label: '压单天数', width: '50' },
  { istrue: true, prop: 'ninetyDaysNum', label: '90天次数', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'buyPerson', label: '采购人', width: '60' },
  { istrue: true, prop: 'buyNo', label: '采购单号', width: '80', type: 'html', formatter: (row) => formatNoLink(row.buyNo) },
  {
    istrue: true, type: 'button', width: '55', btnList: [{
      label: "编辑",
      htmlformatter: (row) => { return `<i class="el-icon-star-on" style="color:${(row.isReportToday == true ? "green" : "red")}"></i>` },
      display: (row) => { return row.isHandle == true; }, handle: (that, row) => that.onHand(row)
    }]
  },
  { istrue: true, prop: 'planArrivalTime', label: '预计到货日期', width: '110', sortable: 'custom', formatter: (row) => formatTime(row.planArrivalTime, 'YYYY-MM-DD') },
  { istrue: true, prop: 'reason', label: '原因', width: '75' },
  { istrue: true, prop: 'remark', label: '解决方案', type: 'editor' },
];
const tableHandles = [
  { label: "导入", handle: (that) => that.startImport() }, { label: "导出", handle: (that) => that.onExport() },
  { label: "复制所有审单异常订单号", handle: (that) => that.copyAllAbnormalOderNo() },
  { label: "复制所有审单异常商品编码", handle: (that) => that.copyAllAbnormalGoodsCode() },
  { label: "批量操作", handle: (that) => that.onHandBatch() }
];
export default {
  name: "Users",
  components: { container, cesTable, MyConfirmButton, goodscoderecord, orderabnormalorderdetail },
  data() {
    return {
      that: this,
      formatPlatform: formatPlatform,
      formatTime: formatTime,
      formatIsCheckError: formatIsCheckError,
      filter: {
        timerange: null,
        startFirstOrderTime: null,
        endFirstOrderTime: null,
        goodsName: null,
        goodsCode: null,
        brandId: null,
        company: null,
        groupId: null,
        warehouseArea: null,
        reason: null,
        isHandle: 'false',
        isCheckError: null,
      },
      goodsCodes: null,
      detailspage: 1,
      goodscoderecordfilter: { goodsCode: "", buyNo: "" },
      imgPreview: { img: "", show: false },
      lastUpdateTime: '',
      reasonRate: '',
      brandlist: [],
      brandlist1: [],
      grouplist: [],
      parentid: 0,
      list: [],
      recodelist: [],
      detaillist: [],
      importtimelist: [],
      oderDetailView: [],
      visiblepopover: false,
      prevTarget: null, // 编辑 Popover 的 Reference （参照），用于 popover.js 对齐两个元素
      popperFlag: false, // 用于编辑 Popover 的刷新
      visiblepopoverdetail: false,
      dialogOrderDetailVisible: false,
      popperFlagdetail: false,
      importtimedialogVisible: false,
      tableCols: tableCols,
      tableHandles: tableHandles,
      pager: { OrderBy: "", IsAsc: false },
      summaryarry: {},
      detailstotal: 0,
      total: 0,
      sels: [],
      selids: [],
      fileList: [],
      listLoading: false,
      dialogVisible: false,
      pageLoading: false,
      editVisible: false,
      editLoading: false,
      uploadLoading: false,
      autoform: {
        fApi: {},
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 8 } } } },
        rule: []
      },
    };
  },
  watch: {
    value(n) {
      if (n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
      }
    }
  },
  async mounted() {
    if (this.$route.query && this.$route.query.goodsCode) {
      this.filter.goodsCode = this.$route.query.goodsCode
    }
    formCreate.component('editor', FcEditor);
    await this.initform();
    await this.init();
    await this.onSearch();

  },
  methods: {
    async initform() {
      let that = this;
      this.autoform.rule = [{ type: 'hidden', field: 'id', title: 'id', value: '' },
      { type: 'input', field: 'goodsCode', title: '商品编码', value: '', props: { readonly: true } },
      { type: 'input', field: 'goodsName', title: '商品名称', value: '', props: { readonly: true } },
      { type: 'input', field: 'buyPerson', title: '采购负责人', value: '', props: { maxlength: 10, readonly: true } },
      //  {type:'select',field:'isCheckError',title:'审单状态',value:'',validate: [{type: 'boolean', required: true, message:'请选择'}],
      //       options: [{value:null, label:'请选择'},{value:false, label:'正常'},{value:true, label:'异常'}]},
      {
        type: "cascader", field: "_reason", title: "原因", value: [], props: {
          options: [
            { value: '采购原因', label: '采购原因', children: [{ value: '进货（时效、数量）判断失误', label: '进货（时效、数量）判断失误' }, { value: '厂家（改价、发货、物流）异常', label: '厂家（改价、发货、物流）异常' }, { value: '特殊（定制产品、专利产品）', label: '特殊（定制产品、专利产品）' }, { value: '其它原因', label: '其它原因' }] },
            { value: '运营原因', label: '运营原因', children: [{ value: '产品冲量、活动（未告知采购）', label: '新品进货（前两次采购单）' }, { value: '运营给量（判断错误）', label: '运营给量（判断错误）' }, { value: '下架产品出单', label: '下架产品出单' }, { value: '更换厂家衔接问题', label: '更换厂家衔接问题' }] },
            { value: '仓库原因', label: '仓库原因', children: [{ value: '审单原因', label: '审单原因' }, { value: '到货提货不及时', label: '到货提货不及时' }, { value: '到货入库不及时', label: '到货入库不及时' }, { value: '质检入库数量型号错误', label: '质检入库数量型号错误' }, { value: '加工时效超时', label: '加工时效超时' }, { value: '质量问题返厂更换', label: '质量问题返厂更换' }, { value: '仓库盘点、其他出库', label: '仓库盘点、其他出库' }] },
            { value: '市场原因', label: '市场原因', children: [{ value: '疫情原因', label: '疫情原因' }, { value: '天气灾害', label: '天气灾害' }, { value: '其他市场波动', label: '其他市场波动' }] },
            { value: '财务原因', label: '财务原因', children: [{ value: '付款不及时', label: '付款不及时' }, { value: '其他', label: '其他' }] },
          ]
        }
      },
      { type: 'DatePicker', field: 'planArrivalTime', title: '预计到货日期', value: '', validate: [{ type: 'string', required: true, message: '请输入预计到货日期' }], props: { type: 'datetime', format: 'yyyy-MM-dd', placeholder: '预计到货日期', } },
      //{type:'input',field:'solution',title:'解决方案',value: '',props:{maxlength:40}},
      {
        type: 'editor', field: 'remark', title: '解决方案', value: '', col: { span: 20 }, validate: [{ type: 'string', required: true, message: '请输入解决方案' }],
        props: { maxlength: 400, init: async (editor) => { await that.initeditor(editor) } }
      }
      ]
      this.autoform.rule.forEach(f => {
        if (f.field == 'toUserId1') f.validate = []
        if (f.field == 'toUserId2') f.validate = []
      })
    },
    async initeditor(editor) {
      editor.config.uploadImgMaxSize = 3 * 1024 * 1024
      editor.config.excludeMenus = ['emoticon', 'video']
      editor.config.uploadImgAccept = []
      editor.config.customUploadImg = async function (resultFiles, insertImgFn) {
        // console.log('resultFiles', resultFiles)
        // const form = new FormData();
        // form.append("image", resultFiles[0]);
        // const res = await upLoadImage(form);
        // var url = `${res.data}`
        // console.log('url', url)
        // insertImgFn(url)
        var xhr = new XMLHttpRequest()
            var formData = new FormData()
            formData.append('file', resultFiles[0])
            xhr.open('post', '/api/uploadnew/file/UploadCommonFileAsync')
            xhr.withCredentials = true
            xhr.responseType = 'json'
            xhr.send(formData)
            xhr.onreadystatechange = () => {
                if (xhr.readyState === 4 && xhr.status === 200) {
                      console.log('url', xhr.response.data.url)
                      insertImgFn(xhr.response.data.url)
                }
            }

      }
    },
    async removeEditPopoverListener(flag) {  // 监听滚动，用于编辑框的滚动移除
      let timer = setTimeout(() => {
        let scrollElement = this.$refs.table.$el.querySelector('.el-table__body-wrapper');
        console.log('监听滚动，用于编辑框的滚动移除', flag, scrollElement);
        let scrollHandle = () => {
          console.log('执行--->', this.visibleEditOpinions);
          if (this.visibleEditOpinions) {
            this.clearEditPopperComponent();
          }
        }
        if (flag) {
          // 滚动节流
          scrollElement.addEventListener('scroll', throttle(500, scrollHandle));
        } else {
          scrollElement.removeEventListener('scroll', scrollHandle);
        }
        clearTimeout(timer);
      }, 0);
    },
    // 复选框选中的数据
    async changeSelection(row) {
      this.selectData = row;
      console.log('复选框选中的数据', this.selectData);
      this.seqs = this.selectData.map((el) => { return el.seq; }).toString();
      console.log('seqs---->', this.seqs);
    },
    // 清空编辑组件
    async clearEditPopperComponent() {
      this.prevTarget = null;
      this.popperFlag = !this.popperFlag;
      this.popperFlagdetail = !this.popperFlagdetail;
      this.visiblepopover = false;
      this.visiblepopoverdetail = false;
    },
    async init() {
      var res1 = await getDirectorGroupList();
      this.grouplist = res1.data.map(item => { return { value: item.key, label: item.value }; });

      var res2 = await getAllProBrand();
      this.brandlist1 = res2.data;
      this.brandlist = res2.data.map(item => { return { value: item.key, label: item.value }; });

      var res3 = await getLastUpdateTime();
      this.lastUpdateTime = "最晚更新时间" + res3.data
    },
    async changeSetCompany() {
      if (this.filter.company === '义乌' || this.filter.company === '南昌') {
        this.brandlist = this.brandlist1.filter(f => f.company === this.filter.company).map(item => {
          return { value: item.key, label: item.value };
        });
      } else if (this.filter.company === '其他') {
        this.brandlist = this.brandlist1.filter(f => f.company !== '南昌' && f.company !== '义乌').map(item => {
          return { value: item.key, label: item.value };
        });
      } else {
        this.brandlist = this.brandlist1.map(item => {
          return { value: item.key, label: item.value };
        });
      }
      this.filter.brandId = null;
    },
    async onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      if (!this.pager.OrderBy) this.pager.OrderBy = "";
      var pager = this.$refs.pager.getPager()
      const params = { ...pager, ...this.pager, ... this.filter }
      if (params.timerange) {
        params.startFirstOrderTime = params.timerange[0];
        params.endFirstOrderTime = params.timerange[1];
      }
      console.log('params', params)
      this.listLoading = true
      const res = await pageAbnormalInventory(params)
      this.listLoading = false
      if (!res?.success) return
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => { d._loading = false })
      this.list = data
      this.summaryarry = res.data.summary;

      var res2 = await getAllAbnormalReasonRate(params);
      this.reasonRate = res2.data;
      var res3 = await getLastUpdateTime();
      this.lastUpdateTime = "最晚更新时间" + res3.data
    },
    async cellclick(row, column, cell, event) {
      if (column.property == 'buyNo') {
        if (row.buyNo)
          this.$router.push({ path: '/inventory/purchaseindex', query: { buyNo: row.buyNo } })
      }
      else if (column.property == 'planArrivalTime'
        || column.property == 'reason'
        || column.property == 'solution'
        || column.property == 'remark111') {
        await this.getrecordlist(row.goodsCode)
        // if (event.stopPropagation) {//阻止事件冒泡，兼容ie
        //   event.stopPropagation();
        // } else if (window.event) {
        //   window.event.cancelBubble = true;
        // }
        // let currentTarget = event.target; // 赋值当前点击的编辑
        // this.editData = row; // 设置编辑数据
        // if (this.prevTarget === currentTarget) { // 判断是否需要切换
        //   this.visiblepopover = !this.visiblepopover; // 同一个元素重复点击
        // } else {
        //   if (this.prevTarget) {  // 切换不同元素, 判断之前是否有点击其他编辑 prevTarget
        //     this.clearEditPopperComponent();   // 先清除之前的编辑框
        //     this.$nextTick(() => { // 然后生成新的编辑框
        //       this.prevTarget = currentTarget;
        //       this.visiblepopover = true;
        //     });
        //   } else {
        //     console.log('首次--->this.prevTarget'); // 首次
        //     this.prevTarget = currentTarget;
        //     this.visiblepopover = true;
        //   }
        // }
      }
      else if (column.property == 'goodsCode'
        || column.property == 'goodsName'
        || column.property == 'waitOrderNumCurrent'
        || column.property == 'waitGoodNumCurrent'
        || column.property == 'waitDaysc'
        || column.property == 'ninetyDaysNum'
      ) {
        await this.getdetaillist(row.id)
        if (event.stopPropagation) {
          event.stopPropagation();
        } else if (window.event) {
          window.event.cancelBubble = true;
        }
        let currentTarget = event.target;
        this.editData = row;
        if (this.prevTarget === currentTarget) {
          this.visiblepopoverdetail = !this.visiblepopoverdetail;
        } else {
          if (this.prevTarget) {
            this.clearEditPopperComponent();
            this.$nextTick(() => {
              this.prevTarget = currentTarget;
              this.visiblepopoverdetail = true;
            });
          } else {
            this.prevTarget = currentTarget;
            this.visiblepopoverdetail = true;
          }
        }
      } else if (column.property == 'sellStock') {
        this.$showDialogform({
          path: `@/views/inventory/AllAbnormalsellStock.vue`,
          title: '详情',
          autoTitle: false,
          args: { goodsCode: row.goodsCode },
          height: 700,
          width: '40%',
          //callOk: self.onSearch
        })
      }
    },
    async showOrderDetail(parentid) {
      this.dialogOrderDetailVisible = true;
      this.$nextTick(async () => {
        this.$refs.boxorder.reloadParentId(parentid);
        this.$refs.boxorder.onSearch();
      });


    },
    async getrecordlist(goodscode) {
      this.visiblepopover = true;

      this.$nextTick(() => {
        this.$refs.goodscoderecord.onSearch(goodscode, '');
      });
    },
    async getdetaillist(parentid) {
      this.detaillist = [];
      const res = await queryAbnormalInventoryDetail({ parentid: parentid })
      if (!(res.code == 1 && res.data)) return
      this.detaillist = res.data;
    },
    //  async getorderdetaillist(parentid){
    //      this.oderDetailView={};
    //      const res = await queryAbnormalOderDetail({parentid:parentid})
    //      if (!(res.code==1&&res.data)) return
    //      this.oderDetailView=res.data;
    //   },
    async copyAllAbnormalOderNo() {
      var res = await getAllAbnormalCheckErrorOderNo();
      if (!res.data) {
        this.$message({ message: "没有获取到订单号", type: "warning" });
        return;
      }
      this.doCopy(res.data)
    },
    async copyAllAbnormalGoodsCode() {
      var res = await getAllAbnormalCheckErrorGoodsCode();
      if (!res.data) {
        this.$message({ message: "没有获取到商品编码", type: "warning" });
        return;
      }
      this.doCopy(res.data)
    },
    async onHand(row) {
      this.formtitle = '编辑';
      this.editVisible = true
      const res = await queryAbnormalInventory({ id: row.id })
      if (res.data.reason)
        res.data._reason = res.data.reason.split('-')
      this.$nextTick(async () => {
        var arr = Object.keys(this.autoform.fApi);
        if (arr.length > 0) {
          await this.autoform.fApi.hidden(false, 'goodsCode')
          await this.autoform.fApi.hidden(false, 'goodsName')
          await this.autoform.fApi.hidden(false, 'buyPerson')
          this.autoform.fApi.resetFields()
          await this.autoform.fApi.setValue(res.data)
          this.goodsCodes = row.goodsCode;
          console.log('敷之后11', this.goodsCodes)
        }
      })
    },
    async onHandBatch() {
      if (this.selids.length <= 0) {
        this.$message.error("请选择要操作的编码！");
        return;
      }
      console.log('1111', this.selids)
      this.formtitle = '编辑';
      this.editVisible = true;
      var _this = this;
      this.$nextTick(async () => {
        var arr = Object.keys(this.autoform.fApi);
        if (arr.length > 0) {
          await this.autoform.fApi.hidden(true, 'goodsCode')
          await this.autoform.fApi.hidden(true, 'goodsName')
          await this.autoform.fApi.hidden(true, 'buyPerson')
          this.autoform.fApi.resetFields()
          _this.goodsCodes = _this.selids.join();
        }
      })
    },
    onDisPlay(row) {
      return row.isHandle == true;
    },
    async onEditSubmit() {
      this.editLoading = true;
      await this.autoform.fApi.validate(async (valid, fail) => {
        if (valid) {
          const formData = this.autoform.fApi.formData();
          if (formData._reason.length > 0) {
            formData.reason = `${formData._reason[0]}-${formData._reason[1]}`;
          }
          formData.goodsCode = this.goodsCodes;
          const res = await editAbnormalInventory(formData);

          if (res.code == 1) {
            this.getlist();
            this.selids = [];
            this.editVisible = false;
          }
        } else { }
      })
      this.editLoading = false;
    },
    startImport() {
      this.dialogVisible = true;
    },
    cancelImport() {
      this.dialogVisible = false;
    },
    beforeRemove() {
      return false;
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    async submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.fileHasSubmit = true;
      this.uploadLoading = true;
      this.$refs.upload.submit();
    },
    async uploadFile(item) {
      if (!this.fileHasSubmit) {
        return false;
      }
      this.fileHasSubmit = false;
      var orderFile = null;
      var warehouseFile = null;
      for (const key in this.fileList) {
        var name = this.fileList[key].name;
        if (name.indexOf("订单") > -1 && name.indexOf("库存") < 0) {
          orderFile = this.fileList[key];
        }
        if (name.indexOf("订单") < 0 && name.indexOf("库存") > -1) {
          warehouseFile = this.fileList[key];
        }
      }
      if (!orderFile || !warehouseFile) {
        this.$message({ message: "请同时上传订单和库存文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfileOrder", orderFile);
      form.append("upfileWarehouse", warehouseFile);
      const res = await importAbnormal(form);
      if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
      else this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading = false;
    },
    uploadChange(file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for (var i = 0; i < fileList.length; i++) {
          if (fileList[i].status == "success")
            list.push(fileList[i]);
          else
            list.push(fileList[i].raw);
        }
        this.fileList = list;
      }
    },
    uploadRemove(file, fileList) {
      this.uploadChange(file, fileList);
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    selsChange: function (sels) {
      this.sels = sels
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.goodsCode);
      })
    },
    doCopy: function (val) {
      let that = this;
      this.$copyText(val).then(function (e) {
        that.$message({ message: "内容已复制到剪切板！", type: "success" });
      }, function (e) {
        that.$message({ message: "抱歉，复制失败！", type: "warning" });
      })
    },
    // 图片点击放大
    showImg(e) {
      // console.log(e.target)
      if (e.target.tagName == 'IMG') {
        this.imgPreview.img = e.target.src
        this.imgPreview.show = true
      }
    },
    async onExport() {
      if (!this.pager.OrderBy) this.pager.OrderBy = "id";
      const params = { ...this.pager, ... this.filter }
      var res = await exportAbnormalInventory(params);
      if (!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '异常订单数据_' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async getimportlist() {
      var res = await GetLastUpdateTimeAbnormalInventory();
      console.log('导入时间', res.data)
      if (!res?.success) return
      this.importtimelist = res.data
      this.importtimedialogVisible = true;
    },
  },
};
</script>
<style lang="scss" scoped>
.imgDolg {
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: 9999;
  background-color: rgba(140, 134, 134, 0.6);
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  #imgDolgClose {
    position: fixed;
    top: 35px;
    cursor: pointer;
    right: 7%;
    font-size: 50px;
    color: white;
  }

  img {
    width: 80%;
  }
}</style>

