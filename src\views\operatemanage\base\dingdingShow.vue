<template>
    <MyContainer >
        <el-tabs v-model="activeName" style="height: 95%; width:99%;" @tab-click="tabclick">
            <el-tab-pane label="部门及用户" name="first" style="height: 100%;" >
                <DDUserInfoList ref="DDUserInfoList" />
            </el-tab-pane>
            <el-tab-pane label="考勤数据" name="second" style="height: 100%;">
                <dingdingUserAttendance ref="dingdingUserAttendance" />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>
  
  <script>
    //import {  } from '@/api/express/express'    
    import dingdingDeptUser from '@/views/operatemanage/base/dingdingDeptUser.vue'
    import DDUserInfoList from '@/views/admin/organization/DDUserInfoList.vue'
    import MyContainer from "@/components/my-container";
    import dingdingUserAttendance from '@/views/operatemanage/base/dingdingUserAttendance.vue'
    export default {
        name: 'DingDingShow',
        components: { dingdingDeptUser, dingdingUserAttendance,DDUserInfoList },
        data () {
            return {
                activeName: 'first'
            }
        },
        async mounted () {
            await this.onSearch()
        },
        methods: {
            async onSearch () {
                this.$nextTick(async () => {
                    // if (this.activeName == 'first')
                    //     await this.$refs.dingdingDeptUser.deptOnSearch()
                    // else if (this.activeName == 'second')
                    //     await this.$refs.dingdingUserAttendance.userOnSearch()
                })
            },
            async tabclick () {
                this.$nextTick(async () => {
                    // if (this.activeName == 'first'){
                    //     this.$refs.dingdingDeptUser.deptOnSearch()

                    // }
                    // else if (this.activeName == 'second'){

                    //     this.$refs.dingdingUserAttendance.userOnSearch()
                    // }
                })
            }
        }
    }
  </script>
  