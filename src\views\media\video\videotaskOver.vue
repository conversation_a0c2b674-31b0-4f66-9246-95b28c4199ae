1addForm
<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='false' :hasexpand='false' @sortchange='sortchange'
            :tableData='tasklist' @select='selectchange' :isSelection='true' :tableCols='tableCols'
            :loading="listLoading" :summaryarry="summaryarry">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>
                <el-button-group>
                  <!--   <el-button style="padding: 0;width: 130px;">
                        <el-input-number v-model="Filter.videoTaskId" placeholder="任务编号" :min="1" :max="100000"   :step="1" :step-strictly="true" controls-position="right"   @keyup.enter.native="onSearch" clearable />
                    </el-button> -->
                    <el-button style="padding: 0;width: 150px;">
                        <el-input v-model="Filter.videoTaskId" style="padding: 0;width: 130px;" type="number" placeholder="任务编号"  clearable></el-input>
                    </el-button>

                    <el-button style="padding: 0;width: 100px;">
                        <el-input v-model="Filter.productId" placeholder="产品Id" @keyup.enter.native="onSearch" clearable />
                    </el-button>
                    <el-button style="padding: 0;width: 110px;">
                        <el-input v-model="Filter.productShortName" placeholder="产品简称" @keyup.enter.native="onSearch"
                            clearable />
                    </el-button>
                    <el-button style="padding: 0;width: 90px;">
                        <el-select v-model="Filter.isDelivered" :clearable="true" :collapse-tags="true" filterable
                                     placeholder="是否到样">
                                <el-option key="0" label="否" value="0"></el-option>
                                <el-option key="1" label="是" value="1"></el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;width: 110px;">
                        <el-select style="width:100%" v-model="Filter.taskUrgency" :clearable="true" filterable
                            placeholder="紧急程度">
                            <el-option v-for="item in taskUrgencyList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;width: 100px;">
                        <el-input v-model="Filter.cuteLqName" placeholder="负责人" @keyup.enter.native="onSearch" clearable />
                    </el-button>
                    <el-button style="padding: 0;width: 100px;">
                        <el-input v-model="Filter.dockingPeopleStr" placeholder="分配拍摄" @keyup.enter.native="onSearch" clearable />
                    </el-button>
                  <!--   <el-button style="padding: 0;width: 120px;">
                        <el-input v-model="Filter.claimant1" placeholder="视频一" clearable />
                    </el-button> 
                    <el-button style="padding: 0;width: 120px;">
                        <el-input v-model="Filter.claimant2" placeholder="视频二"
                            clearable />
                    </el-button> 
                    <el-button style="padding: 0;width: 120px;">
                        <el-input v-model="Filter.claimant3" placeholder="视频三"
                            clearable />
                    </el-button> 
                    <el-button style="padding: 0;width: 120px;">
                        <el-input v-model="Filter.claimant4" placeholder="视频四"
                            clearable />
                    </el-button> 
                    <el-button style="padding: 0;width: 120px;">
                        <el-input v-model="Filter.claimant5" placeholder="视频五" 
                            clearable />
                    </el-button>  
                    <el-button style="padding: 0;width: 155px;">
                                <el-select style="width:100%" :clearable="true" v-model="Filter.firstSceneId" filterable @change="getGridSecondSceneList" placeholder="场景一">
                                    <el-option v-for="item in taskGridFirstSceneList" :key="item.sceneId" :label="item.sceneName" :value="item.sceneId" />
                                </el-select>
                     </el-button>
                   <el-button style="padding: 0;width: 155px;">
                                <el-select style="width:100%" :clearable="true" v-model="Filter.secondSceneId" filterable @change="getGridThirdSceneList" placeholder="场景二">
                                    <el-option v-for="item in taskGridSecondSceneList" :key="item.sceneId" :label="item.sceneName" :value="item.sceneId" />
                                </el-select>
                              </el-button>
                    <el-button style="padding: 0;width: 155px;">
                                <el-select style="width:100%" :clearable="true" v-model="Filter.thirdSceneId" filterable placeholder="场景三">
                                    <el-option v-for="item in taskGridThirdSceneList" :key="item.sceneId" :label="item.sceneName" :value="item.sceneId" />
                                </el-select>
                      </el-button> -->

                    <el-button type="primary" @click="onSearch">查询</el-button>
                  
                    <el-button type="primary" @click="onExeprotVedioTask"  v-if="checkPermission('api:media:vediotask:ExportVedioTaskReport')" >导出</el-button>
                    <el-button  style="margin-left: 20px" @click="onMoveTask" icon="el-icon-share"  v-if="checkPermission('viewvedioShop')" >批量移动</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList" />
        </template>

        <el-dialog :title="taskPageTitle" :visible.sync="addTask" width="70%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" @close="onCloseAddForm"  :loading="addLoading" v-dialogDrag >
            <span>
                <el-form :model="addForm" ref="addForm" label-width="120px" :rules="addFormRules" :disabled="islook">

                    <!-- <el-row>
                        <el-col :span="24">
                            <el-form-item label="任务名称" prop="videoTaskName">
                                <el-input style="width:100%" :clearable="true" v-model="addForm.videoTaskName"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row> -->
                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="平台:">
                                <el-select v-model="addForm.platform" :clearable="true" :collapse-tags="true" filterable
                                    :disabled="true">
                                    <el-option v-for="item in platformList" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-row>
                                <el-col :span="20">
                                    <el-form-item label="产品ID" prop="productId">
                                        <el-input style="width:100%" :clearable="true" v-model="addForm.productId"
                                            :disabled="true"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-button @click="onSelctProduct" style="float: right ;font-size:14px" type="text">
                                        选择</el-button>
                                </el-col>
                            </el-row>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="店铺" prop="shopName">
                                <el-input style="width:100%" :clearable="true" v-model="addForm.shopName"
                                    :disabled="true"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="产品简称" prop="productShortName">
                                <el-input style="width:100%" :clearable="true" v-model="addForm.productShortName"
                                    :disabled="addForm.audioStatus>=1"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="24">
                            <el-row>
                                <el-col :span="23">
                                    <el-form-item label="商品编码" prop="goodCode">
                                        <el-input style="width:100%" :clearable="true" v-model="addForm.goodCode"
                                            :readonly="true">
                                        </el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="1">
                                    <el-button @click="onSelctCp" style="float: right ;font-size:14px" type="text"> 选择
                                    </el-button>
                                </el-col>
                            </el-row>
                        </el-col>

                    </el-row>

                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="紧急程度" prop="taskUrgency">
                                <el-select style="width:100%" v-model="addForm.taskUrgency" :clearable="true" filterable
                                    :disabled="addForm.audioStatus>=1">
                                    <el-option v-for="item in taskUrgencyList" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="内部订单号" prop="shopName">
                                <el-input style="width:100%" :clearable="true" v-model="addForm.sampleRrderNo">
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="快递单号" prop="shopName">
                                <el-input style="width:100%" :clearable="true" v-model="addForm.sampleExpressNo">
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="日期" prop="taskDate">
                                <el-date-picker style="width:100%" v-model="addForm.taskDate"
                                    :disabled="addForm.audioStatus>=1" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                    type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <!-- <el-col :span="6">
                            <el-form-item label="场景一" prop="firstSceneId">
                                <el-select style="width:100%" :clearable="true" v-model="addForm.firstSceneId" filterable @change="getSecondSceneList(true)" :disabled="addForm.audioStatus>=1">
                                    <el-option v-for="item in taskFirstSceneList" :key="item.sceneId" :label="item.sceneName" :value="item.sceneId" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="场景二" prop="secondSceneId">
                                <el-select style="width:100%" :clearable="true" v-model="addForm.secondSceneId" filterable @change="getThirdSceneList(true)" :disabled="addForm.audioStatus>=1">
                                    <el-option v-for="item in taskSecondSceneList" :key="item.sceneId" :label="item.sceneName" :value="item.sceneId" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="场景三" prop="thirdSceneId">
                                <el-select style="width:100%" :clearable="true" v-model="addForm.thirdSceneId" filterable @change="RefreshData($event)" :disabled="addForm.audioStatus>=1">
                                    <el-option v-for="item in taskThirdSceneList" :key="item.sceneId" :label="item.sceneName" :value="item.sceneId" />
                                </el-select>
                            </el-form-item>
                        </el-col> -->
                    </el-row>
                    <el-row>
                        <el-col :span="6">
                            <el-form-item prop="isDelivered" label="是否到样">
                                <el-radio-group v-model="addForm.isDelivered">
                                    <el-radio-button label="1">是</el-radio-button>
                                    <el-radio-button label="0">否</el-radio-button>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item prop="isReissue" label="是否补发">
                                <el-radio-group v-model="addForm.isReissue">
                                    <el-radio-button label="1">是</el-radio-button>
                                    <el-radio-button label="0">否</el-radio-button>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item prop="warehouse" label="仓库" :span="12">
                                <el-select v-model="addForm.warehouse" placeholder="请选择仓库" style="width:100%;"
                                    :disabled="addForm.audioStatus>=1">
                                    <el-option v-for="item in warehouseList" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6">
                            <el-form-item prop="warehouse" label="运营组" :span="12">
                                <el-select v-model="addForm.operationsGroup" placeholder="请选择" style="width:100%;"
                                    :disabled="addForm.audioStatus>=1">
                                    <el-option v-for="item in operationsGroupList" :key="item.id" :label="item.id"
                                        :value="item.id" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item prop="warehouse" label="对接人" :span="12">
                                <el-input style="width:100%" :clearable="true" v-model="addForm.dockingPeopleStr"> </el-input>
                            </el-form-item>
                        </el-col>
                   
                        <el-col :span="6">
                            <el-form-item label="到货日期" prop="arrivalDate">
                                <el-date-picker style="width:100%" v-model="addForm.arrivalDate"
                                    format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                    type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
             

                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="备注" prop="remark">
                                <el-input type="textarea" :rows="3" v-model="addForm.remark" placeholder="请输入备注" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-col :xs="24" :sm="24" :lg="24" v-if="addForm.videoTaskId>0" >
                        <el-card class="box-card" style="width:100% ;height: 45px; overflow: hidden;">
                            <div slot="header" class="clearfix" style="">
                                <span>视频片段</span>
                                <span style="float:right">
                                    <el-button type="primary" @click="onAddpdArray()">增加一行</el-button>
                                </span>
                            </div>
                        </el-card>
                        <el-card class="box-card" style="width:100% ;height: 300px; overflow: auto;">
                            <div class="block">
                                <el-table :data="addForm.pdArray" highlight-current-row>
                                    <el-table-column label="参考" width="120">
                                        <template slot-scope="scope">
                                            <el-select v-model="scope.row.ckVideoIndex" placeholder="请选择"
                                                :disabled="IsPDreading(scope.row.ckVideoIndex,addForm)"
                                                @change="pdChanged">
                                                <el-option v-for="item in pdseloptions" :key="item.value"
                                                    :label="item.label" :value="item.value" :disabled="item.disabled">
                                                </el-option>
                                            </el-select>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="name" label="备注" align="center" width="120">
                                        <template slot-scope="scope">
                                            <el-input v-model="scope.row.title" placeholder="备注"
                                                :disabled="IsPDreading(scope.row.ckVideoIndex,addForm)">
                                            </el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="name" label="场景" align="center" width="150">
                                        <template slot-scope="scope">
                                            <!--  <el-select style="width:100%" :clearable="true" v-model="scope.row.firstSceneId" filterable :disabled="IsPDreading(scope.row.ckVideoIndex,addForm)">
                                                <el-option v-for="item in taskFirstSceneList" :key="item.sceneId" :label="item.sceneName" :value="item.sceneId" />
                                            </el-select> -->
                                            <el-cascader v-model="scope.row.firstSceneIds" :options="taskFirstSceneList"
                                                @change="handleChange"
                                                :disabled="IsPDreading(scope.row.ckVideoIndex,addForm)">
                                            </el-cascader>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="name" label="片段链接" align="center">
                                        <template slot-scope="scope">
                                           <!--  <el-input v-model="scope.row.url" placeholder="片段链接"
                                                :disabled="IsPDreading(scope.row.ckVideoIndex,addForm)"
                                                >
                                            </el-input> -->
                                            <el-input v-model="scope.row.url" placeholder="片段链接" :disabled="true">
                                            </el-input>
                                        </template>
                                    </el-table-column>

                                    <el-table-column lable="操作" width="200px">
                                        <template slot-scope="scope">
                                            <el-button type="primary"
                                                :disabled="IsPDreading(scope.row.ckVideoIndex,addForm)"
                                                @click="onUploadVideoPd(scope.$index)">上传片段
                                            </el-button>
                                           <!--   <el-button type="info"
                                                :disabled="IsPDreading(scope.row.ckVideoIndex,addForm)"
                                                @click="onCutepdArray(scope.row.ckVideoIndex,scope.row.url,scope.row.pdid)">
                                                同步剪切<i class="el-icon-remove-outline"></i>
                                            </el-button>-->
                                            <el-button type="danger"
                                                :disabled="IsPDreading(scope.row.ckVideoIndex,addForm)"
                                                @click="onDelpdArray(scope.$index)">移除<i
                                                    class="el-icon-remove-outline"></i>
                                            </el-button>

                                        </template>
                                    </el-table-column>
                                </el-table>

                            </div>
                        </el-card>

                    </el-col>
                    <!-- <el-row :gutter="15" style="width:100% ;height: 300px;overflow: auto;">
                        <el-col :xs="24" :sm="24" :lg="4" v-for="thisVideo in cutVideoList" :key="thisVideo">
                            <el-card class="video-box">
                                <div style="position: relative;text-align:center">
                                    <el-image :src="thisVideo.imgPath" style="max-width: 100px; max-height: 100px;" fit="fill" :lazy="true"></el-image>
                                    <span style="display: block;position: absolute;top: 13px;left: 40%;">
                                        <a size="mini" class="el-link el-link--primary is-underline" @click="playVideo(thisVideo.videoPath)" style="margin-left: 3px;color:#ccc;font-size: 23px;">
                                            <i class="el-icon-video-play"></i>
                                        </a></span>
                                </div>
                            </el-card>
                        </el-col>
                    </el-row> -->
                </el-form>
            </span>

            <!-- <span slot="footer" class="dialog-footer">
                <el-button @click="addTask = false">关闭</el-button>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="addgroup">确定</el-button>
            </span> -->
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="addTask = false">取 消</el-button>
                    <my-confirm-button type="submit" :validate="onSubmitValidate" @click="onSubmit" v-show="!islook" />
                </span>
            </template>
        </el-dialog>

        <!--选择商品-->
        <el-dialog title="选择产品" :visible.sync="productVisible" width='90%' height='500px' v-dialogDrag>
            <productselect :ischoice="true" ref="productselect" style="z-index:10000;height:500px" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="productVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onQuerenProduct()">确 定</el-button>
                </span>
            </template>
        </el-dialog>

        <!--选择商品-->
        <el-dialog title="选择商品编码" :visible.sync="goodschoiceVisible" width='88%' height='500px' v-dialogDrag>
            <goodschoice :ischoice="true" ref="goodschoice" style="z-index:10000;height:500px" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="goodschoiceVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onQueren()">确 定</el-button>
                </span>
            </template>
        </el-dialog>

        <!--视频播放-->
        <el-dialog title="已剪切视频预览" :visible.sync="yjqdialogVisible" width="60%" @close="closeYjqVideo">
            <taskcutevideolist v-if="yjqdialogReload" ref="taskcutevideolist" @playVideo="playVideo"
                :taskid='videotaskid' :ckindex='cankaoindex' />
            <span slot="footer" class="dialog-footer">
                <el-button @click="yjqdialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <!--视频播放-->
        <el-dialog title="视频播放" :visible.sync="dialogVisible" width="50%" @close="closeVideoPlyer">
            <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <!--上传片段-->
        <el-dialog title="上传片段" :visible.sync="uploadpdVisibleSyj" width="40%" :loading="ScpdLoading"
            @close="closedialogVisibleScpd">
            <span>
                <el-upload ref="upload3" class="upload-demo" :auto-upload="false" :http-request="uploadpdFile"
                    :on-success="uploadpdSuccess" :limit="1" :file-list="SypdjfileList">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onPdSubmitupload">
                        上传</my-confirm-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closedialogVisibleScpd">关闭</el-button>
            </span>
        </el-dialog>
        <!--上传视频-->
        <el-dialog title="上传视频" :visible.sync="dialogVisibleSyj" width="50%" @close="closedialogVisibleSyj">
            <el-card class="box-card" style="width:100% ;height: 300px; overflow: auto;">
                <div class="block">
                    <el-table ref="multipleTable" :data="selPdArray" @selection-change="handleSelectionChange"
                        highlight-current-row>
                        <el-table-column type="selection" width="55">
                        </el-table-column>
                        <el-table-column label="参考视频" width="120">
                            <template slot-scope="scope">
                                参考视频{{scope.row.ckVideoIndex}}
                            </template>
                        </el-table-column>
                        <el-table-column prop="remark" label="备注" align="center">
                        </el-table-column>

                        <el-table-column prop="title" label="标题" align="center">
                        </el-table-column>

                        <el-table-column prop="name" label="参考视频" width="80">
                            <template slot-scope="scope">
                                <div style="position: relative;">
                                    <el-image :src="scope.row.imgPath" style="max-width: 50px; max-height: 50px;"
                                        fit="fill" :lazy="true"></el-image>
                                    <span style="display: block;position: absolute;top: 10px;left: 10px;">
                                        <a size="mini" class="el-link el-link--primary is-underline"
                                            @click="playerVideo(scope.row.videoPath)"
                                            style="margin-left: 3px;color:#ccc;font-size: 23px;">
                                            <i class="el-icon-video-play"></i>
                                        </a></span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="name" label="已传视频" width="80">
                            <template slot-scope="scope">
                                <div v-if="scope.row.UploadId !=null" style="position: relative;">
                                    <el-image :src="scope.row.UploadImgPath" style="max-width: 50px; max-height: 50px;"
                                        fit="fill" :lazy="true"></el-image>
                                    <span style="display: block;position: absolute;top: 10px;left: 10px;">
                                        <a size="mini" class="el-link el-link--primary is-underline"
                                            @click="playerVideo(scope.row.UploadVideoPath)"
                                            style="margin-left: 3px;color:#ccc;font-size: 23px;">
                                            <i class="el-icon-video-play"></i>
                                        </a></span>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-card>
            <span>
                <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="true"
                    :http-request="uploadFile2" :on-success="uploadSuccess2" :file-list="SyjfileList">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">
                        上传</my-confirm-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-drawer title="视频剪辑" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
            :visible.sync="editVideoVisible" direction="btt" size="'auto'" style="position:absolute;overflow:hidden"
            @close="closeCuteForm">
            <section>
                <videoplay v-if="videoplayReload" :filter="filter" :taskGridFirstSceneList="taskGridFirstSceneList"
                    :referenceVideoList="referenceVideoList" ref="videoplay" :videopath="videopath"
                    :videoduration="videoduration" :videoid='videoid' style="height: 80%;width:100%" />
            </section>
            <div class="drawer-footer">
                <el-button @click.native="editVideoVisible = false">取消</el-button>
                <my-confirm-button type="submit" :validate="editFormValidate" :loading="editLoading"
                    @click="onCutVideoSubmit" />
            </div>
        </el-drawer>
        <el-drawer :title="shstatustitleText" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="uploadVideoAudioVisible"
         direction="btt" size="'auto'" style="position:absolute;overflow:hidden" @close="closeVideoAudioCuteForm">
            <section>
                <videotaskaudio v-if="uploadVideoAudioReload" @playVideo="playVideo" :ckindex="ckindex" :cardHeight="'600px'" 
                :videoProductName='videoProductName'
                :islook="false" ref="videotaskaudio" :videoTaskId='videotaskid' style="height: 90%;width:100%" />
            </section>
            <div class="drawer-footer">
                <el-button @click.native="uploadVideoAudioVisible = false">取消</el-button>
                <my-confirm-button type="submit" :validate="editFormValidate" :loading="editLoading" @click="onVideoAudioSubmit" />
            </div>
        </el-drawer>
        <el-drawer :title="stdetalstatustitleText" :modal="false" 
        :wrapper-closable="true" 
        :modal-append-to-body="false"
         :visible.sync="videVideoAudioVisible" 
        direction="btt" size="'auto'" style="position:absolute;overflow:hidden" @close="closeViewVideoAudioForm">
            <section>
                <videotaskaudio  :key="atudiokey" @playVideo="playVideo" :cardHeight="'550px'" :ckindex="ckindex" :islook="true" 
                :videoProductName='videoProductName'
                ref="videotaskaudio" :videoTaskId='videotaskid' style="height: 90%;width:100%" />
            </section>
            <div class="drawer-footer">
                <el-button @click.native="videVideoAudioVisible = false">关闭</el-button>
            </div>
        </el-drawer>

        <!--s上传成品视频视频-->
        <el-dialog ref ="UploadSuccessVideoInfo"  title="成品视频文件" :visible.sync="UploadSuccessVideo" width="70%" @close="closeUploadSuccessVideo" :element-loading-text="addloadingText" v-loading ="addloading"  v-dialogDrag >
            <videotaskoutcom ref="outcom" :row ="outcomrow"></videotaskoutcom>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="UploadSuccessVideo = false">关闭</el-button>
                 
                </span>
            </template>
        </el-dialog>

        <el-dialog title="任务下单记录" :visible.sync="dialogOrderDtlVisible" width='88%' v-dialogDrag :close-on-click-modal="false" :v-loading="dialogOrderDtlLoading" @close="dialogOrderDtlColsed">
            <el-row style="height:35px;line-height:35px; font-size: 16px;font-weight: bold;color: black;margin-bottom: 2px;">
                <span>下单发货信息：</span>
            </el-row>
            <el-container>
                <el-main style="height:192px;">
                    <ces-table ref="tablexdfhmain" :that='that' :isIndex='true' :hasexpand='true' :tableData='xdfhmainlist' :tableCols='xdfhmainTableCols' :loading="xdfhmainLoading" style="height:190px" :isSelectColumn="false" @cellclick="onxdfhmainCellClick">
                    </ces-table>
                </el-main>
            </el-container>
            <el-row style="height:35px;line-height:35px; font-size: 16px;font-weight: bold;color: black; margin-top: 10px;margin-bottom: 2px;">
                <span>下单发货商品明细信息：</span>
            </el-row>
            <el-container>
                <el-main style="height:262px;">
                    <ces-table ref="tablexdfhdtl" :that='that' :isIndex='true' :hasexpand='true' :tableData='xdfhdtllist' :tableCols='xdfhdtlTableCols' :loading="xdfhdtlLoading" style="height:260px" :isSelectColumn="false">
                    </ces-table>
                </el-main>
            </el-container>
        </el-dialog>

        <el-drawer title="物流跟踪" :visible.sync="drawervisible" direction="rtl" :append-to-body="true">
            <logistics ref="logistics"></logistics>
        </el-drawer>

        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px" v-dialogDrag>
            <orderLogPage ref="orderLogPage" :orderNoInner="sendOrderNoInner" style="z-index:10000;height:600px" />
        </el-dialog>
    </my-container>
</template>
<script>
import checkPermission from '@/utils/permission';
import videoplay from '@/views/media/video/videoplay'
import taskcutevideolist from '@/views/media/video/taskcutevideolist'
//播放器
import videoplayer from '@/views/media/video/videoplayer'
import videotaskaudio from '@/views/media/video/videotaskaudio'
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import { rulePlatform } from "@/utils/formruletools";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import 
{ 
    pageViewTaskAsync, getTaskUrgencyList, getSceneList,moveTaskToShop, getSceneListTree, addOrUpdateVideoTaskAsync, deleteVideoTaskAsync, exportVedioTaskReport,
    pickVideoTaskAsync, unPickVideoTaskAsync, saveUploadTeskVideoAsync, uplodPdVideoAsync, getVideoTaskPdList, complatVideoTask ,getVedioTaskOrderListById
} from '@/api/media/vediotask';
import { cuteVideo, getTaskCuteVideoList, videoTaskAudioAsync } from '@/api/media/video'
import videotaskoutcom from '@/views/media/video/videotaskoutcom'
import goodschoice from "@/views/base/goods/goods3.vue";
import productselect from "@/views/operatemanage/base/productselect";
import logistics from '@/components/Comm/logistics'
import orderLogPage from "@/views/order/logisticsWarning/orderLogPage.vue";
const tableCols = [
    { istrue: true, prop: 'videoTaskId', label: '任务编号', width: '50', sortable: 'custom', fixed: true },
    { istrue: true, prop: 'productShortName', label: '产品简称', width: '200', sortable: 'custom', fixed: true },
    { istrue: true, align:'center',prop: 'cuteLqName', dingdingcode: 'cuteLqNameDDingUserId', label: '负责人', width: '80', type: 'ddingtalk' , fixed: true},
    {
        istrue: true, prop: 'taskUrgency', label: '紧急程度', width: '50',
        type: 'html',
        formatter: (row) => row.taskUrgency == 9 ? `<div>${row.taskUrgencyName}</div>` :
            (row.taskUrgency == 2 ? `<div style="color:#f59e1a">${row.taskUrgencyName}</div>` :
                (row.taskUrgency == 1 ? `<div style="color:red">${row.taskUrgencyName}</div>` : ''))

    },
    { istrue: true, type: 'color'  ,backgroudColor:'rgb(200,200,200)'},
    { istrue: true, width: '140', prop: '', label: '视频一',  merge: true, prop: 'mergeField',  
             cols:  [
                { istrue: true,align:'center', prop: 'claimant1', dingdingcode: 'claimantId1DDingUserId', label: '拍摄人', width: '80', type: 'ddingtalk' },
                { istrue: true,align:'center', width: '75', prop: 'claimTime1', label: '拍摄时间', formatter: (row) => { if (!row.claimTime1) { return '' } else { return formatTime(row.claimTime1 || '', 'MM月DD日') } } },
                {
                    istrue: true,align:'center', type: "button", width: "50", label: '审核',
                    btnList: [
                        { label: "详情", handle: (that, row) => that.onViewVideoAudio(row, 1) }
                    ]
                },
                { istrue: true,align:'center', width: '50', prop: 'claimAudioStatus1', label: '审核结果', 
                    type:'html',
                        formatter: (row) => row.claimAudioStatus1 == 2 ? `<div style="color:#4EEE94">通过</div>`:
                             row.claimAudioStatus1 == 3 ? `<div style="color:#f59e1a">补拍</div>`:
                             row.claimAudioStatus1 == 1 ? `<div style="color:#000" >待审</div>`:
                            row.claimAudioStatus1 == 4 ? `<div style="color:red">重拍</div>`:' '
                },
         
                { istrue: true,align:'center', width: '75', prop: 'claimAudioTime1', label: '审核时间', formatter: (row) => { if (!row.claimAudioTime1) { return '' } else { return formatTime(row.claimAudioTime1 || '', 'MM月DD日') } } },

                { istrue: true,align:'center', width: '80', prop: 'cutClaimant1', dingdingcode: 'cutClaimantId1DDingUserId', label: '剪辑人', type: 'ddingtalk' },
                { istrue: true,align:'center', width: '75', prop: 'cutClaimTime1', label: '剪辑时间', formatter: (row) => { if (!row.cutClaimTime1) { return '' } else { return formatTime(row.cutClaimTime1 || '', 'MM月DD日') } } },
             
            ]
        },
        { istrue: true, type: 'color'  ,backgroudColor:'rgb(200,200,200)'},
        { istrue: true, align:'center',width: '140', prop: '', label: '视频二',  merge: true, prop: 'mergeField1',  
             cols:  [
                { istrue: true,align:'center', prop: 'claimant2', dingdingcode: 'claimantId2DDingUserId', label: '拍摄人', width: '80', type: 'ddingtalk' },
                { istrue: true,align:'center', width: '75', prop: 'claimTime2', label: '拍摄时间', formatter: (row) => 
                    { if (!row.claimTime2) { return '' } else { return formatTime(row.claimTime2 || '', 'MM月DD日') } } },
                 {
                    istrue: true, align:'center',type: "button", width: "50", label: '审核',
                    btnList: [
                        { label: "详情", handle: (that, row) => that.onViewVideoAudio(row, 2)  }
                    ]
                },
             
                { istrue: true, align:'center',width: '50', prop: 'claimAudioStatus2', label: '审核结果', 
                    
                    type:'html',
                            formatter: (row) => row.claimAudioStatus2 == 2 ? `<div style="color:#4EEE94">通过</div>`:
                                row.claimAudioStatus2 == 3 ? `<div style="color:#f59e1a">补拍</div>`:
                                row.claimAudioStatus2 == 1 ? `<div  style="color:#000" >待审</div>`:
                                row.claimAudioStatus2 == 4 ? `<div style="color:red">重拍</div>`:''
                },
             
               
                { istrue: true, align:'center',width: '75', prop: 'claimAudioTime2', label: '审核时间', formatter: (row) => { if (!row.claimAudioTime2) { return '' } else { return formatTime(row.claimAudioTime2 || '', 'MM月DD日') } } },
              
                { istrue: true,align:'center', width: '80', prop: 'cutClaimant2', dingdingcode: 'cutClaimantId2DDingUserId', label: '剪辑人', type: 'ddingtalk' },
                { istrue: true,align:'center', width: '75', prop: 'cutClaimTime2', label: '剪辑时间', formatter: (row) => { if (!row.cutClaimTime2) { return '' } else { return formatTime(row.cutClaimTime2 || '', 'MM月DD日') } } },
               
            ]
        },
        { istrue: true, type: 'color'  ,backgroudColor:'rgb(200,200,200)'},

        { istrue: true, width: '140', prop: '', label: '视频三',  merge: true, prop: 'mergeField2',  
             cols:  [
                { istrue: true,align:'center', prop: 'claimant3', dingdingcode: 'claimantId3DDingUserId', label: '拍摄人', width: '80', type: 'ddingtalk' },
                { istrue: true,align:'center', width: '75', prop: 'claimTime3', label: '拍摄时间', formatter: (row) => 
                    { if (!row.claimTime3) { return '' } else { return formatTime(row.claimTime3 || '', 'MM月DD日') } } },
                {
                    istrue: true, align:'center',type: "button", width: "50", label: '审核',
                    btnList: [
                        { label: "详情",  handle: (that, row) => that.onViewVideoAudio(row, 3)  }
                    ]
                },
                { istrue: true,align:'center', width: '50', prop: 'claimAudioStatus3', label: '审核结果',
                    type:'html',
                            formatter: (row) => row.claimAudioStatus3 == 2 ? `<div style="color:#4EEE94">通过</div>`:
                                row.claimAudioStatus3 == 3 ? `<div style="color:#f59e1a">补拍</div>`:
                                row.claimAudioStatus3 == 1 ? `<div style="color:#000" > 待审</div>`:
                                row.claimAudioStatus3 == 4 ? `<div style="color:red">重拍</div>`:' '
                },
                { istrue: true, align:'center',width: '75', prop: 'claimAudioTime3', label: '审核时间',formatter: (row) => { if (!row.claimAudioTime3) { return '' } else { return formatTime(row.claimAudioTime3 || '', 'MM月DD日') } } },
            
                { istrue: true,align:'center', prop: 'cutClaimant3', dingdingcode: 'cutClaimantId3DDingUserId', label: '剪辑人', width: '80', type: 'ddingtalk' },
                { istrue: true,align:'center', width: '75', prop: 'cutClaimTime3', label: '剪辑时间', formatter: (row) => { if (!row.cutClaimTime3) { return '' } else { return formatTime(row.cutClaimTime3 || '', 'MM月DD日') } } },
             
            ]
        },
        { istrue: true, type: 'color'  ,backgroudColor:'rgb(200,200,200)'},
        { istrue: true, width: '140', prop: '', label: '视频四',  merge: true, prop: 'mergeField3',  
             cols:  [
                { istrue: true,align:'center', prop: 'claimant4', dingdingcode: 'claimantId4DDingUserId', label: '拍摄人', width: '80', type: 'ddingtalk' },
                { istrue: true, align:'center',width: '75', prop: 'claimTime4', label: '拍摄时间', formatter: (row) => 
                    { if (!row.claimTime4) { return '' } else { return formatTime(row.claimTime4 || '', 'MM月DD日') } } },
                {
                    istrue: true, align:'center',type: "button", width: "50", label: '审核',
                    btnList: [
                        { label: "详情",  handle: (that, row) => that.onViewVideoAudio(row, 4) }
                    ]
                },
               
                { istrue: true, align:'center',width: '50', prop: 'claimAudioStatus4', label: '审核结果',  type:'html',
                        formatter: (row) => row.claimAudioStatus4 == 2 ? `<div style="color:#4EEE94">通过</div>`:
                             row.claimAudioStatus4 == 3 ? `<div style="color:#f59e1a">补拍</div>`:
                             row.claimAudioStatus4 == 1 ? `<div style="color:#000" >待审</div>`:
                             row.claimAudioStatus4 == 4 ? `<div style="color:red">重拍</div>`:' '
                },
              
                { istrue: true,align:'center', width: '85', prop: 'claimAudioTime4', label: '审核时间', formatter: (row) => { if (!row.claimAudioTime4) { return '' } else { return formatTime(row.claimAudioTime4 || '', 'MM月DD日') } } },

                { istrue: true,align:'center', prop: 'cutClaimant4', dingdingcode: 'cutClaimantId4DDingUserId', label: '剪辑人', width: '80', type: 'ddingtalk' },
                { istrue: true,align:'center', width: '75', prop: 'cutClaimTime4', label: '剪辑时间', formatter: (row) => { if (!row.cutClaimTime4) { return '' } else { return formatTime(row.cutClaimTime4 || '', 'MM月DD日') } } },
                
            ]
        },
        { istrue: true, type: 'color'  ,backgroudColor:'rgb(200,200,200)'},
        { istrue: true, width: '140', prop: '', label: '视频五',  merge: true, prop: 'mergeField4', 
             cols:  [
             { istrue: true,align:'center', prop: 'claimant5', dingdingcode: 'claimantId5DDingUserId', label: '拍摄人', width: '80', type: 'ddingtalk' },
                { istrue: true,align:'center', width: '75', prop: 'claimTime5', label: '拍摄时间', formatter: (row) => 
                    { if (!row.claimTime5) { return '' } else { return formatTime(row.claimTime5 || '', 'MM月DD日') } } },

                {
                    istrue: true,align:'center', type: "button", width: "50", label: '审核',
                    btnList: [
                        { label: "详情",  handle: (that, row) => that.onViewVideoAudio(row, 5) }
                    ]
                },
     
                { istrue: true,align:'center', width: '50', prop: 'claimAudioStatus5', label: '审核结果',
                
                type:'html',
                        formatter: (row) => row.claimAudioStatus5 == 2 ? `<div style="color:#4EEE94">通过</div>`:
                             row.claimAudioStatus5 == 3 ? `<div style="color:#f59e1a">补拍</div>`:
                                  row.claimAudioStatus5 == 1 ? `<div style="color:#000">待审</div>`:
                             row.claimAudioStatus5 == 4 ? `<div style="color:red">重拍</div>`:' '
                },
          
                { istrue: true,align:'center', width: '75', prop: 'claimAudioTime5', label: '审核时间', formatter: (row) => { if (!row.claimAudioTime5) { return '' } else { return formatTime(row.claimAudioTime5 || '', 'MM月DD日') } } },
                { istrue: true,align:'center', prop: 'cutClaimant5', dingdingcode: 'cutClaimantId5DDingUserId', label: '剪辑人', width: '80', type: 'ddingtalk' },
                { istrue: true, align:'center',width: '75', prop: 'cutClaimTime5', label: '剪辑时间', formatter: (row) => { if (!row.cutClaimTime5) { return '' } else { return formatTime(row.cutClaimTime5 || '', 'MM月DD日') } } },
               
             
            ]
        },
        { istrue: true, type: 'color'  ,backgroudColor:'rgb(200,200,200)'},
        { istrue: true, prop: 'operationsGroup', label: '运营', width: '50' },
        { istrue: true, prop: 'dockingPeopleStr', label: '分配拍摄', width: '90' },
        { istrue: true, prop: 'shopId', label: '店铺', width: '175', sortable: 'custom',
                formatter: (row) => row.shopName || ' ' },
        { istrue: true, prop: 'productId', label: '产品ID', width: '120' },
        { istrue: true,prop: 'audioComplateTime', label: '完成时间',  width: '75',  
                formatter: (row) => { if (!row.audioComplateTime) { return '' } else { return formatTime(row.audioComplateTime || '', 'MM月DD日') } } },
        { istrue: true,prop: 'arrivalDate', label: '到货日期', width: '75',  
                formatter: (row) => { if (!row.arrivalDate) { return '' } else { return formatTime(row.arrivalDate || '', 'MM月DD日') } } },
        { istrue: true, prop: 'createdTime', label: '创建日期',  width: '75',
                formatter: (row) => { if (!row.createdTime) { return '' } else { return formatTime(row.createdTime || '', 'MM月DD日') } } },
        { istrue: true, prop: 'sampleRrderNo', label: '内部订单号', width: '200', type: 'click', handle: (that, row, column) => that.showLogDetail(row) },
        { istrue: true, prop: 'sampleExpressNo', label: '快递单号', width: '200', type: 'click', handle: (that, row, column) => that.onShowExproessHttp(row) },
        { istrue: true, prop: 'orderTrack',   label: '拿样跟踪', type: 'click', handle: (that, row, column) => that.onShowOrderDtl(row)},
        {
        istrue: true, type: "button", label: '操作', fixed: 'right', width: "240",
        btnList: [
            { label: "详情", permission: "viewvediotask", handle: (that, row) => that.lookTask(row) },
            { label: "成品视频", handle: (that, row) => that.UploadComplatVideoTask(row) },
            { label: "移动", permission: "viewvedioShop", handle: (that, row) => that.onMoveTaskRow(row.videoTaskId) },
            
         
      ]
    }
];
const xdfhmainTableCols = [
    { istrue: true, prop: 'vedioTaskId', label: '当前任务', width: '80' },
    { istrue: true, prop: 'vedioTaskIds', label: '涉及任务', width: '100' },
    { istrue: true, prop: 'vedioTaskOrderId', label: '下单号', width: '70' },
    { istrue: true, prop: 'createdUserName', label: '下单人', width: '70' },
    { istrue: true, prop: 'receiverName', label: '收货人', width: '70' },
    { istrue: true, prop: 'receiverPhone', label: '收货电话', width: '80' },
    { istrue: true, prop: 'receiverState', label: '收货省', width: '70' },
    { istrue: true, prop: 'receiverCity', label: '收货市', width: '80' },
    { istrue: true, prop: 'receiverDistrict', label: '收货区', width: '80' },
    { istrue: true, prop: 'receiverAddress', label: '收货地址' },
    { istrue: true, prop: 'sampleRrderNo', label: '聚水潭内部单号', width: '120', type: 'click', handle: (that, row) => that.showLogDetail(row)  },
    { istrue: true, prop: 'sampleExpressNo', label: '快递单号', width: '120', type: 'click', handle: (that, row, column) => that.onShowExproessHttp(row)},
    { istrue: true, prop: 'sampleExpressCom', label: '物流公司', width: '80' },
    { istrue: true, prop: 'arrivalDate', label: '到货日期', width: '100', formatter: (row) => row.arrivalDate == null ? null: formatTime(row.arrivalDate, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'isDy', label: '是否到样', width: '80', formatter: (row) => row.isDy == 1 ? "是" : "否" },
    { istrue: true, prop: 'isZt', label: '是否自提', width: '80', formatter: (row) => row.isZt == 1 ? "是" : "否" },
    { istrue: true, prop: 'approveStateName', label: '状态', width: '80'},
    { istrue: true, prop: 'createdTime', label: '创建时间', width: '150', formatter: (row) => row.createdTime == null ? null : formatTime(row.createdTime, 'YYYY-MM-DD HH:mm:ss') },
];
const xdfhdtlTableCols = [
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '200' },
    { istrue: true, prop: 'goodsName', label: '商品名称' },
    { istrue: true, prop: 'goodsPrice', label: '单价', width: '120', display: false },
    { istrue: true, prop: 'goodsQty', label: '数量', width: '120' },
    { istrue: true, prop: 'goodsAmount', label: '总额', width: '120', display: false },
];
export default {
    name: "Users",
    inject:['reload'],
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, 
        goodschoice, productselect, videoplayer, videoplay, videotaskaudio, taskcutevideolist 
        ,videotaskoutcom, logistics, orderLogPage
        },
    data() {
        return {
            UploadSuccessVideo:false,
            outcomrow:{},
            that: this,
            Filter: {
                // taskDate: formatTime(new Date(), "YYYY-MM-DD"),
                isAudioComplate: "1",
                isShop: "0"
            },
            atudiokey :null,
            stdetalstatustitleText:null,
                shstatustitleText:null,
            uploadVideoAudioVisible: false,
            uploadVideoAudioReload: true,
            videVideoAudioVisible: false,
            videVideoAudioReload: true,
            uploadpdrow: null,
            videotaskid: '',
            cutVideoList: [],
            videopath: '',
            videoduration: 10000,
            videoid: '',
            videoplayReload: true,
            editVideoVisible: false,
            islook: false,
            taskPageTitle: "新增任务",
            referenceVideoList: [],
            multipleSelection: [],
            addForm: {
                productId: null,
                shopId: null,
                shopName: null,
                productShortName: null,
                goodCode: null,
                taskDate: formatTime(new Date(), "YYYY-MM-DD"),
                taskUrgency: null,
                firstSceneId: null,
                secondSceneId: null,
                thirdSceneId: null,
                remark: "",
                referenceVideo1: "",
                referenceVideo2: "",
                referenceVideo3: "",
                referenceVideo4: "",
                referenceVideo5: "",
                referenceVideo1Type: 0,
                referenceVideo2Type: 0,
                referenceVideo3Type: 0,
                referenceVideo4Type: 0,
                referenceVideo5Type: 0,
                claimantId1: 0,
                claimantId2: 0,
                claimantId3: 0,
                claimantId4: 0,
                claimantId5: 0,
                taskStatus: 0,
                platform: 0,
                sampleRrderNo: '',
                sampleExpressNo: '',
                pdIds: [],
                pdArray: [],
                isDelivered: 0,
                isReissue: 0,
                warehouse: null,
                audioStatus: 0,
            },
            warehouseList: [
                { label: '义乌', value: 1 },
                { label: '南昌', value: 3 }
            ],
            platformList: [],//平台下拉
            shopList: [],
            updateForm: {},
            taskUrgencyList: [],
            taskFirstSceneList: [],
            taskSecondSceneList: [],
            taskThirdSceneList: [],
            taskGridFirstSceneList: [],
            taskGridSecondSceneList: [],
            taskGridThirdSceneList: [],
            tasklist: [],
            tableCols: tableCols,
            total: 0,
            addTask: false,
            summaryarry: {},
            pager: {},
            selids:[],
            sels: [], // 列表选中列
            listLoading: false,
            ScpdLoading: false,
            pageLoading: false,
            addLoading :false,
            updategroupdialogVisibleSyj: false,
            dialogVisibleSyj: false,
            dialogVisible: false,
            selids: [],
            fileList: [],
            selPdArray: [],
            pdId:0,
            addFormRules: {
                // videoTaskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
                productId: [{ required: true, message: '请选择产品', trigger: 'blur' }],
                // goodCode: [{ required: true, message: '请选择商品', trigger: 'blur' }],
                taskDate: [{ required: true, message: '请选择日期', trigger: 'blur' }],
                // firstSceneId: [{ required: true, message: '请选择场景一', trigger: 'blur' }],
                // secondSceneId: [{ required: true, message: '请选择场景二', trigger: 'blur' }],
                // thirdSceneId: [{ required: true, message: '请选择场景三', trigger: 'blur' }]
                // referenceVideo1: [{ required: true, message: '请输入参考视频1', trigger: 'blur' }],
                // referenceVideo2: [{ required: true, message: '请输入参考视频2', trigger: 'blur' }],
                // referenceVideo3: [{ required: true, message: '请输入参考视频3', trigger: 'blur' }],
                // referenceVideo4: [{ required: true, message: '请输入参考视频4', trigger: 'blur' }],
                // referenceVideo5: [{ required: true, message: '请输入参考视频5', trigger: 'blur' }]
            },
            selTeskId: 0,
            productVisible: false,//选择产品窗口
            goodschoiceVisible: false,//选择商品窗口
            isError: false, // 是否不能播放视频
            errMsg: "",
            videoUrls: 'http://www.si-tech.com.cn/pub-ui/images/radio/sitech.mp4',
            videoUrl: '',
            videoplayerReload: true,
            cankaoindex: 0,
            uploadpdVisibleSyj: false,
            yjqdialogVisible: false,
            yjqdialogReload: false,
            pdseloptions: [
                { value: '1', label: '参考1' },
                { value: '2', label: '参考2' },
                { value: '3', label: '参考3' },
                { value: '4', label: '参考4' },
                { value: '5', label: '参考5' }
            ],
            SyjfileList: [],
            SypdjfileList: [],
            
            dialogOrderDtlVisible: false,
            dialogOrderDtlLoading: false,

            xdfhmainlist: [],
            xdfhmainTableCols: xdfhmainTableCols,
            xdfhmainLoading: false,

            xdfhdtllist: [],
            xdfhdtlTableCols: xdfhdtlTableCols,
            xdfhdtlLoading: false,  

            drawervisible: false,  

            sendOrderNoInner: "",
            dialogHisVisible: false,
        };
    },
    watch: {
        // firstSceneId(val){
        //     this.getSecondSceneList(val);
        // },
        // secondSceneId(val){
        //     this.getThirdSceneList(val);
        // }
    },
    async created() {
       
        //await this.getGridFirstSceneList();
    },
    async mounted() {
        this.onSearch();
        this.checkPress();
        await this.setPlatform();//平台下拉
        await this.getTaskUrgencyList();//紧急状态下拉
        await this.getFirstSceneList();
    },
    methods: {
        async onExeprotVedioTask(){
            var pager = this.$refs.pager.getPager(); 
            const params = {
                ...pager,
                ...this.pager,
                ...this.Filter,
                exporttype:"over"

            };
            this.pageLoading = true;
            var res = await exportVedioTaskReport(params);
            if (res?.data?.type == 'application/json') {return;}
            this.pageLoading = false;
            const aLink = document.createElement("a");
            var blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '短视频完成任务导出.xlsx')
            aLink.click()

        },
        OpenDdingPicking(id){
          if(id){

              window.location.href = "dingtalk://dingtalkclient/action/sendmsg?dingtalk_id="+id; 
          }else{

            this.$message({ type: 'warning', message: "钉钉号未设置" });
          }
        },
        UploadComplatVideoTask(row){
            this.outcomrow = row;
            debugger;
            this.$nextTick(() => {
                this.$refs.outcom.UploadComplatVideoTask();
            });
         

            this.UploadSuccessVideo =true;
        },
        closeUploadSuccessVideo(){
            this.outcomrow = {};
            this.UploadSuccessVideo =false;
        }, 
        checkPress() {
            this.delpress = checkPermission("api:vediotask:deletevideotaskasync");
        
        },
        handleChange(value) {
            console.log(value);
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        onAddpdArray() {
            //debugger;
            if (this.addForm.claimantId1 == 0 || this.addForm.claimantId1 == null) {
                this.addForm.pdArray.push({ ckVideoIndex: "1", url: '' });
            } else if (this.addForm.claimantId2 == 0 || this.addForm.claimantId2 == null) {
                this.addForm.pdArray.push({ ckVideoIndex: "2", url: '' });
            } else if (this.addForm.claimantId3 == 0 || this.addForm.claimantId3 == null) {
                this.addForm.pdArray.push({ ckVideoIndex: "3", url: '' });
            } else if (this.addForm.claimantId4 == 0 || this.addForm.claimantId4 == null) {
                this.addForm.pdArray.push({ ckVideoIndex: "4", url: '' });
            } else if (this.addForm.claimantId5 == 0 || this.addForm.claimantId5 == null) {
                this.addForm.pdArray.push({ ckVideoIndex: "5", url: '' });
            } else {
                this.$message({ type: 'warning', message: "已无‘参考视频’可完成！" });
            }

        },
        onDelpdArray(index) {
            this.addForm.pdArray.splice(index, 1);
        },
        async onComplatVideoTask(row) {
            var that = this;
            this.$confirm("确认完成, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                await complatVideoTask({ videoTaskId: row.videoTaskId, type: 1 });
                that.$message({ message: '操作成功', type: "success" });
                that.onRefresh();
            });
        },
        async onUploadVideoAudio(row) {
            this.uploadVideoAudioReload = false;
            this.uploadVideoAudioVisible = true;
            this.uploadVideoAudioReload = true;
            this.videotaskid = row.videoTaskId;

        },
        async closeVideoAudioCuteForm() {
            this.uploadVideoAudioReload = false;
        },
        async onViewVideoAudio(row,index) {
            this.videVideoAudioReload = false;
         
                this.videoProductName = row.productShortName;
                this.videotaskid = row.videoTaskId;
                this.atudiokey = row.videoTaskId + index;
                this.stdetalstatustitleText = "审核详情      任务编号：" +row.videoTaskId+ "   产品简称："+ row.productShortName;
                this.ckindex = index;
                this.videVideoAudioVisible = true;
                this.videVideoAudioReload = true;
             
        },
        async closeViewVideoAudioForm() {
            this.uploadVideoAudioReload = false;
        },
        async closeCuteForm() {
            this.videoplayReload = false;
        },
        async closeVideoPlyer() {
            this.videoplayerReload = false;
        },
        async onViewYjqVideo(row, index) {
            this.yjqdialogReload = false;
            this.yjqdialogVisible = true;
            this.yjqdialogReload = true;
            this.videotaskid = row.videoTaskId;
            this.cankaoindex = index;
        },
        async closeYjqVideo() {
            this.yjqdialogReload = false;
        },
        async onVideoAudioSubmit() {
            var that = this;

            var data = this.$refs.videotaskaudio.getSaveData();
            if (data.audioStatus == 1) {
                that.$message({ type: 'warning', message: "请选择是否通过" });
                return;
            }
            const res = await videoTaskAudioAsync(data);
            if (!res?.success) {
                that.$message({ type: 'warning', message: res?.msg });
            } else {
                that.$message({ type: 'success', message: '提交成功' });
                this.$refs.videotaskaudio.loadList();
            }
        },
        async onCutVideoSubmit() {
            var that = this;

            var data = this.$refs.videoplay.getData()
            if (data.length == 0) {
                that.$message({ type: 'warning', message: "请至少剪切一段" });
                return;
            }
            var para = [];
            data.forEach((item, index) => {
                var video = {};
                video.ID = that.videoid;
                video.videoPdId = item.pdid;
                video.remark = item.remark;
                video.firstSceneId = item.firstSceneId;
                video.firstSceneIds = item.firstSceneIds;
                video.beginTime = item.beginTime1;
                video.endTime = item.endTime1;
                video.title = item.name;
                video.videoIndex = item.index;
                para.push(video);
            });
            const res = await cuteVideo(para);
            if (!res?.success) {
                that.$message({ type: 'warning', message: res?.msg });
            } else {
                that.$message({ type: 'success', message: '提交成功,排队剪切中，请到剪切后视频中查看剪切进度...' });
                that.closeCuteForm();
            }

        },
        async onCuteVideo(row) {
            await this.getFirstSceneList();
            this.editVideoVisible = true;
            this.videoplayReload = true;
            this.videopath = row.videoPath;
            //  this.videoduration = row.duration;
            this.videoid = row.videoTaskId;
            this.referenceVideoList = [];
            this.referenceVideoList = await this.getPdArray(row, 1);
            // this.pushReferenceVideoList(row.referenceVideo1.trim(), 1);
            // this.pushReferenceVideoList(row.referenceVideo2.trim(), 2);
            // this.pushReferenceVideoList(row.referenceVideo3.trim(), 3);
            // this.pushReferenceVideoList(row.referenceVideo4.trim(), 4);
            // this.pushReferenceVideoList(row.referenceVideo5.trim(), 5);

        },
        async pushReferenceVideoList(url, index) {
            if (url != '') {
                var ckspList = {};
                ckspList.index = index;
                ckspList.name = '参考视频' + index;
                ckspList.url = url;
                this.referenceVideoList.push(ckspList);
            }

        },
        async openVideo(url) {
            var that = this;
            this.$copyText(url).then(function (e) {
                that.$message({ type: 'success', message: '复制成功' });
                window.open(url);
            }, function (e) {
                that.$message({ type: 'success', message: '复制失败' });
                window.open(url);
            })
        },
        //设置平台下拉
        async setPlatform() {
            var pfrule = await rulePlatform();
            this.platformList = pfrule.options;
        },
        async onchangeplatform(val) {
            const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100 });
            this.shopList = res1.data.list;
        },
        //选择产品窗口
        onSelctProduct() {
            this.productVisible = true;
        },
        //选择商品
        onSelctCp() {
            this.goodschoiceVisible = true;
        },
        //选择产品确定
        async onQuerenProduct() {
            var choicelist = await this.$refs.productselect.getchoicelistOnly();
            if (choicelist && choicelist.length == 1) {
                this.addForm.productId = choicelist[0].proCode;
                this.addForm.productShortName = choicelist[0].styleCode;
                this.addForm.shopId = choicelist[0].shopId;
                this.addForm.shopName = choicelist[0].shopName;
                this.addForm.platform = choicelist[0].platform;
                this.productVisible = false;
            }
        },
        //选择商品确定
        async onQueren() {
            var choicelist = await this.$refs.goodschoice.getchoicelist();
            if (choicelist) {
                var goodCodeList = [];
                choicelist.forEach((item) => {
                    goodCodeList.push(item.goodsCode);
                });
                // this.addForm.goodCode = choicelist[0].goodsCode;
                this.addForm.goodCode = goodCodeList.join(',');
                this.goodschoiceVisible = false;
            }
        },
        //提交保存时验证
        onSubmitValidate: function () {
            let isValid = false
            this.$refs.addForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        //提交保存
        async onSubmit() {
            var ids = [];
            this.cutVideoList.forEach((item, index) => {
                ids.push(item.id);
            });
            this.addForm.pdIds = ids;
            const para = _.cloneDeep(this.addForm); 
            var res = await addOrUpdateVideoTaskAsync(para);
            if (!res?.success) {
                return;
            }
            this.$message({
                message: this.$t('保存成功'),
                type: 'success'
            })
            //this.$refs['addForm'].resetFields();
            this.addTask = false;
            await this.onSearch();
        },
        //关闭完成界面时请清空表单数据
        onCloseAddForm() {
            this.addForm = {
                productId: null,
                shopId: null,
                shopName: null,
                productShortName: null,
                goodCode: null,
                taskDate: formatTime(new Date(), "YYYY-MM-DD"),
                taskUrgency: null,
                firstSceneId: null,
                secondSceneId: null,
                thirdSceneId: null,
                isReissue: 0, 
                 warehouse: null,
                remark: "",
                referenceVideo1: "",
                referenceVideo2: "",
                referenceVideo3: "",
                referenceVideo4: "",
                referenceVideo5: "",
                referenceVideo1Type: 0,
                referenceVideo2Type: 0,
                referenceVideo3Type: 0,
                referenceVideo4Type: 0,
                referenceVideo5Type: 0,
                claimantId1: 0,
                claimantId2: 0,
                claimantId3: 0,
                claimantId4: 0,
                claimantId5: 0,
                taskStatus: 0,
                platform: 0,
                sampleRrderNo: '',
                sampleExpressNo: '',
                pdIds: [],
                pdArray: [],
                audioStatus: 0,
                isDelivered: 0,
                isReissue: 0,

            };
            this.pdId=0,
            this.addLoading =false,
            this.ScpdLoading = false,
                this.pdseloptions = [
                    { value: '1', label: '参考1' },
                    { value: '2', label: '参考2' },
                    { value: '3', label: '参考3' },
                    { value: '4', label: '参考4' },
                    { value: '5', label: '参考5' }
                ];
            this.SyjfileList = [];
            this.SypdjfileList = [];
        },
        //设置紧急状态下拉
        async getTaskUrgencyList() {
            const res = await getTaskUrgencyList();
            this.taskUrgencyList = res.data || [];
        },
        //设置场景一下拉源
        async getFirstSceneList(ischeck) {
            const res = await getSceneListTree();

            this.taskFirstSceneList = res.data || [];
            this.taskGridFirstSceneList = res.data || [];
            //await this.getSecondSceneList(ischeck);
        },
        //设置场景二下拉源
        async getSecondSceneList(ischeck) {
            var bindid = !this.addForm.firstSceneId ? null : this.addForm.firstSceneId;
            const res = await getSceneList({ parentid: bindid, level: 2 });
            this.taskSecondSceneList = res.data.list || [];
            if (ischeck) {
                this.addForm.secondSceneId = null;
            }
            await this.getThirdSceneList(ischeck);
        },
        //设置场景三下拉源
        async getThirdSceneList(ischeck) {
            var bindid = !this.addForm.secondSceneId ? null : this.addForm.secondSceneId;
            const res = await getSceneList({ parentid: bindid, level: 3 });
            this.taskThirdSceneList = res.data.list || [];
            if (ischeck) {
                this.addForm.thirdSceneId = null;
            }
        },
        async RefreshData() {
            this.$forceUpdate();
        },
        //新增任务
        async onAddTask() {
            this.addTask = true;
            this.taskPageTitle = "新增任务";
            this.islook = false;
            this.addForm.taskUrgency = 0;
            await this.getFirstSceneList();
        },
        //查看任务按钮显藏
        showlook(row) {
            if (row.videoTaskId === 1) {
                return true;
            }
            return false;
        },
        IsNotPickingDel(row) {
            var ret = true;
            if (this.delpress) {
                ret = false;
            }
            return ret;
        },
        //查看删除拍摄完成任务按钮显藏
        IsNotPicking(row, index) {
            var result = false;
            switch (index) {
                case 1:
                    if (row.claimant1 != null && row.claimant1 != "") {
                        result = true;
                    }
                    break;
                case 2:
                    if (row.claimant2 != null && row.claimant2 != "") {
                        result = true;
                    }
                    console.log(result)
                    break;
                case 3:
                    if (row.claimant3 != null && row.claimant3 != "") {
                        result = true;
                    }
                    break;
                case 4:
                    if (row.claimant4 != null && row.claimant4 != "") {
                        result = true;
                    }
                    break;
                case 5:
                    if (row.claimant5 != null && row.claimant5 != "") {
                        result = true;
                    }
                    break;
                case 6:
                    if (row.cutClaimant1 != null && row.cutClaimant1 != "") {
                        result = true;
                    }
                    break;
                case 7:
                    if (row.cutClaimant2 != null && row.cutClaimant2 != "") {
                        result = true;
                    }
                    break;
                case 8:
                    if (row.cutClaimant3 != null && row.cutClaimant3 != "") {
                        result = true;
                    }
                    break;
                case 9:
                    if (row.cutClaimant4 != null && row.cutClaimant4 != "") {
                        result = true;
                    }
                    break;
                case 10:
                    if (row.cutClaimant5 != null && row.cutClaimant5 != "") {
                        result = true;
                    }
                    break;
                default:
                    if (row.audioStatus != null && row.audioStatus > 0) {
                        result = true;
                    }
                    break;

            }
            return result;
        },
        //查看任务
        async lookTask(row) {
            this.taskPageTitle = "查看任务";
            this.islook = true;
            this.addForm = _.cloneDeep(row);
            this.addTask = true;
            await this.getFirstSceneList();
            this.cutVideoList = [];
            this.addForm.warehouse = this.addForm.warehouse == 0 ? null : this.addForm.warehouse;
            this.addForm.pdArray = await this.getPdArray(row, 1);
            this.addLoading =false;
        },
        //编辑任务
        async editTask(row) {
            this.addLoading =true;
            this.taskPageTitle = "编辑任务";
            this.islook = false;
            this.addForm = _.cloneDeep(row);
            //debugger;
            this.addForm.warehouse = this.addForm.warehouse == 0 ? null : this.addForm.warehouse;
            this.addTask = true;
            this.pdseloptions = [

                { label: "参考1", value: "1", disabled: this.addForm.claimantId1 > 0 },
                { label: "参考2", value: "2", disabled: this.addForm.claimantId2 > 0 },
                { label: "参考3", value: "3", disabled: this.addForm.claimantId3 > 0 },
                { label: "参考4", value: "4", disabled: this.addForm.claimantId4 > 0 },
                { label: "参考5", value: "5", disabled: this.addForm.claimantId5 > 0 }];

            await this.getFirstSceneList();
            this.addForm.pdArray = await this.getPdArray(row, 1);
            this.addLoading =false;

        },
        async getPdArray(row, type) {
            const res = await getVideoTaskPdList({ videoTaskId: row.videoTaskId, type: type });
            var pdlist = [];
            res.data.list.forEach((item, index) => {
                item.ckVideoIndex = item.ckVideoIndex.toString();
                pdlist.push(item);
            });

            return pdlist;
        },
        //删除任务
        async deleteTask(row) {
            var that = this;
            this.$confirm("确认删除, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                await deleteVideoTaskAsync({ videoTaskId: row.videoTaskId })
                that.$message({ message: '删除成功', type: "success" });
                that.onRefresh();
            });

        },
        //拍摄完成任务
        async pickTask(row, index) {
            var that = this;
            this.$confirm("确认拍摄完成, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(async () => {
                    await pickVideoTaskAsync({ videoTaskId: row.videoTaskId, index: index })
                    that.$message({ message: '完成成功', type: "success" });
                    that.onRefresh();
                });
        },
        //取消拍摄完成任务
        async unPickTask(row, index) {
            var that = this;
            this.$confirm("确认取消拍摄完成, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(async () => {
                    await unPickVideoTaskAsync({ videoTaskId: row.videoTaskId, index: index })
                    that.$message({ message: '取消成功', type: "success" });
                    that.onRefresh();
                });
        },


        //列表设置场景一下拉源
        async getGridFirstSceneList() {
            this.Filter.firstSceneId = null;
            const res = await getSceneListTree();

            this.taskGridFirstSceneList = res.data || [];
        },
        //列表设置场景二下拉源
        async getGridSecondSceneList(parentid) {
            this.Filter.secondSceneId = null;
            var bindid = !parentid ? null : parentid;
            const res = await getSceneList({ parentid: bindid, level: 2 });
            this.taskGridSecondSceneList = res.data.list || [];
            await this.getThirdSceneList(null);
        },
        //列表设置场景三下拉源
        async getGridThirdSceneList(parentid) {
            this.Filter.thirdSceneId = null;
            const res = await getSceneList({ parentid: parentid, level: 3 });
            this.taskGridThirdSceneList = res.data.list || [];
        },
        playVideo(videoUrl) {
            this.videoplayerReload = false;
            this.videoplayerReload = true;
            this.dialogVisible = true;
            this.videoUrl = videoUrl;
        },
        error(e) {
            console.log(e);
            if (this.videoUrl == '') {
                this.isError = true
                this.errMsg = "该文章暂无视频！"
            } else {
                this.isError = true
                this.errMsg = "视频链接失效！无法播放！"
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onRefresh() {
            this.onSearch()
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getTaskList();
        },
        async getTaskList() {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.Filter
            };
            this.listLoading = true;
            const res = await pageViewTaskAsync(params);
            this.listLoading = false;
            this.total = res.data.total
            this.tasklist = res.data.list;
            this.summaryarry = { videoTaskId_sum: " _" };
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onUploadVideo(row) {
            var that = this;
            this.selPdArray = [];

            var res = await getTaskCuteVideoList({ videoTaskId: row.videoTaskId, ckindex: 0, type: 2 });
            if (!res?.success) {
                return
            }
            var list = res.data.list;
            list.forEach((item, index) => {
                item.videoList.forEach((v, index) => {
                    that.selPdArray.push(v);
                });
            });
            this.selTeskId = row.videoTaskId;
            this.dialogVisibleSyj = true;
        },
        async uploadFile2(item) {

            const form = new FormData();
            form.append("upfile", item.file);
            form.append("videotaskid", this.selTeskId);
            form.append("cuteid", this.multipleSelection[0].id);
            const res = saveUploadTeskVideoAsync(form);
            this.$message({ message: '正在上传，请在【已上传视频】页签查看', type: "success" });
            this.dialogVisibleSyj = false;
        },
        async uploadSuccess2(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
        },

        closedialogVisibleSyj() {
            this.SyjfileList = [];
        },

        async onSubmitupload2() {
            if (this.multipleSelection.length == 0) {
                this.$message({ message: '请选择一个参考片段', type: "waring" });
                return;
            }
            if (this.multipleSelection.length > 1) {
                this.$message({ message: '只能选择一个片段', type: "waring" });
                return;
            }
            this.$refs.upload2.submit();
        },
        async onUploadVideoPd(index) {
            //debugger;
            this.cankaoindex = index;
            this.uploadpdVisibleSyj = true;
        },
        async uploadpdFile(item) {
            const form = new FormData();
            var pdid = this.addForm.pdArray[this.cankaoindex].pdId??0;
            var ckindex = this.addForm.pdArray[this.cankaoindex].ckVideoIndex ;
            form.append("upfile", item.file);
            form.append("index", ckindex);
            form.append("pdId", pdid);
            form.append("videotaskid", this.addForm.videoTaskId);
            //debugger;
            this.addLoading = true;
            const res = await uplodPdVideoAsync(form);
            if (res?.success) {
                debugger
                this.addForm.pdArray[this.cankaoindex].url = res.data.videoFullPath;
                this.addForm.pdArray[this.cankaoindex].pdId = res.data.pdId;
                this.$message({ message: '上传成功', type: "success" });
                this.addLoading = false;
                // this.cutVideoList.push({ videoPath: res.data.videoPath, imgPath: res.data.videoImgPath, id: res.data.cutid });
            } else {
                this.addLoading = false;
                this.$message({ message: '上传失败', type: "warning" });
            }
            this.uploadpdVisibleSyj = false;
        },
        async uploadpdSuccess(response, file, fileList) {

            this.SypdjfileList.splice(fileList.indexOf(file), 1);
        },

        closedialogVisibleScpd() {
            this.uploadpdVisibleSyj = false;
            this.SypdjfileList = [];

        },
        async onPdSubmitupload() {
            this.$refs.upload3.submit();
        },
        //下拉修改
        pdChanged(index) {
            var result = false;
            var that = this.addForm;
            switch (index) {
                case "1":
                    if (that.claimantId1 > 0) {
                        result = true;
                    }
                    break;
                case "2":
                    if (that.claimantId2 > 0) {
                        result = true;
                    }
                    break;
                case "3":
                    if (that.claimantId3 > 0) {
                        result = true;
                    }
                    break;
                case "4":
                    if (that.claimantId4 > 0) {
                        result = true;
                    }
                    break;
                case "5":
                    if (that.claimantId5 > 0) {
                        result = true;
                    }
                    break;
            }
            //debugger;
            return !result;
        },
        //pdreadonly
        IsPDreading(index, that) {
            var result = false;

            switch (index) {
                case "1":
                    if (that.claimantId1 > 0) {
                        result = true;
                    }
                    break;
                case "2":
                    if (that.claimantId2 > 0) {
                        result = true;
                    }
                    break;
                case "3":
                    if (that.claimantId3 > 0) {
                        result = true;
                    }
                    break;
                case "4":
                    if (that.claimantId4 > 0) {
                        result = true;
                    }
                    break;
                case "5":
                    if (that.claimantId5 > 0) {
                        result = true;
                    }
                    break;
            }
            if(this.islook)
                result = true;
            return result;
        }, //行选择事件
        selectchange:function(rows,row) {
                this.selids=[];
                rows.forEach(f=>{
                    this.selids.push(f.videoTaskId);
                })
        },
        onMoveTaskRow(id){
            this.$confirm("选中的任务将会移动到暂存列表，是否确定移动", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                
                const res= await moveTaskToShop([id])
                if (res?.success)  {
                    this.$message({ message: '移动成功！', type: "success" });
                    this.selids=[];
                    this.onRefresh();
                    var self = this;
                    setTimeout(() => { self.reload();}, 100); 
                }
            });
        },
        //选中的行将会移动到暂存列表
        onMoveTask(){
    
            if(this.selids.length==0){
                this.$message({ type: 'warning', message: "请选择一个任务" });
                return;
            }
            this.$confirm("选中的任务将会移动到暂存列表，是否确定移动", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                const res= await moveTaskToShop(this.selids)
                if (res?.success)  {
                    this.$message({ message: '移动成功！', type: "success" });
                    this.selids=[];
                    this.onRefresh();
                    var self = this;
                    setTimeout(() => { self.reload();}, 100); 
                }
            });
        },
        async dialogOrderDtlColsed() {
            this.xdfhmainlist = [];
            this.xdfhdtllist = [];
        },
        async onShowOrderDtl(row) {
            this.dialogOrderDtlVisible = true;
            this.xdfhmainLoading = true;
            this.xdfhdtlLoading = true;
            var ret = await getVedioTaskOrderListById({ vedioTaskId: row.videoTaskId });
            this.xdfhmainLoading = false;
            this.xdfhdtlLoading = false;
            if (ret?.success && ret.data.length > 0) {
                ret.data.forEach(f => f.vedioTaskId = row.videoTaskId);
                this.xdfhmainlist = ret.data;
                this.xdfhdtllist = ret.data[0].dtlEntities;
            }
        },
        async onxdfhmainCellClick(row) {
            this.xdfhmainlist.forEach(
                f => {
                    if (f.vedioTaskOrderId == row.vedioTaskOrderId) {
                        this.xdfhdtllist = f.dtlEntities;
                    }
                }
            );
        },
        async onShowExproessHttp(row) {
            this.drawervisible = true;
            this.$nextTick(function () {
                this.$refs.logistics.showlogistics("",row.sampleExpressNo);
            })
        },
        showLogDetail (row) {
            this.dialogHisVisible = true;
            this.sendOrderNoInner = row.sampleRrderNo;
        },
    }
     
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
    margin-bottom: 15px;
}
</style>
