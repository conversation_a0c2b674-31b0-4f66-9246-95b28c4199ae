<template>
  <container>
    <template>
      <el-form :model="PostAddInfo" ref="ruleForm" label-width="130px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="分公司" prop="company">
              <el-select
                v-model="PostAddInfo.company"
                clearable
                placeholder="分公司"
              >
                <el-option label="义乌" value="义乌" />
                <el-option label="南昌" value="南昌" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="岗位" prop="purchasePost">
              <el-select
                v-model="PostAddInfo.purchasePost"
                @change="changePost(1)"
                clearable
                placeholder="岗位"
              >
                <el-option
                  v-for="item in postList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否启用" prop="enabled">
              <el-radio-group v-model="PostAddInfo.enabled">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-row>
        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-card
            class="box-card"
            style="width: 100%; height: 45px; overflow: hidden"
          >
            <div slot="header" class="clearfix" style="">
              <el-select
                v-model="PostAddInfo.wagesSetType"
                @change="changeType"
              >
                <el-option
                  v-for="item in checkGroup"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-button
                @click="addData()"
                style="float: right; padding: 3px 0"
                type="text"
                >新增一行
              </el-button>
            </div>
          </el-card>
          <el-card
            class="box-card"
            style="width: 100%; height: 500px; overflow: auto"
          >
            <div style="height: 100%">
              <el-table
                :data="PostAddInfo.detailSet"
                border
                 height="400"
                :v-loading="listLoading"
              >
                <el-table-column label="#" width="40">
                  <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                </el-table-column>
                <el-table-column prop="Id" label="id" v-if="false" />
                <el-table-column prop="employeeType" label="类型">
                  <template slot-scope="scope">
                    <el-select
                      v-model="scope.row.employeeType"
                      clearable
                      placeholder="类型"
                      style="width: 120px"
                    >
                      <el-option label="正式" :value="3" :key="1" />
                      <el-option label="试用" :value="2" :key="2" />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="commissionType" label="取值">
                  <template slot-scope="scope">
                    <el-select
                      v-model="scope.row.commissionType"
                      clearable
                      placeholder="取值"
                      @change="changeCom(scope.row)"
                      :disabled="PostAddInfo.wagesSetType != 3"
                    >
                      <el-option label="个人毛三" value="个人毛三" />
                      <el-option label="个人冲抵" value="个人冲抵" />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="attendanceDays" label="休息天数">
                  <template slot-scope="scope">
                    <el-input-number
                      style="width: 100px"
                      v-model="scope.row.attendanceDays"
                      :precision="0"
                      :controls="false"
                      :min="0"
                      :max="10"
                      placeholder="休息天数"
                      align="center"
                    >
                    </el-input-number>
                  </template>
                </el-table-column>
                <el-table-column prop="baseSalary" label="底薪">
                  <template slot-scope="scope">
                    <el-input-number
                      style="width: 100px"
                      v-model="scope.row.baseSalary"
                      :precision="2"
                      :controls="false"
                      :min="0"
                      :max="100000"
                      placeholder="底薪"
                      align="center"
                      @blur="
                        handleInput(
                          scope.row.baseSalary,
                          scope.$index,
                          'baseSalary'
                        )
                      "
                    >
                    </el-input-number>
                  </template>
                </el-table-column>
                <el-table-column prop="performance" label="绩效">
                  <template slot-scope="scope">
                    <el-input-number
                      style="width: 100px"
                      v-model="scope.row.performance"
                      :precision="2"
                      :controls="false"
                      :min="0"
                      :max="100000"
                      placeholder="绩效"
                      align="center"
                      :disabled="PostAddInfo.wagesSetType == 1"
                      @blur="
                        handleInput(
                          scope.row.performance,
                          scope.$index,
                          'performance'
                        )
                      "
                    >
                    </el-input-number>
                  </template>
                </el-table-column>
                <el-table-column prop="commission" label="提成">
                  <template slot-scope="scope">
                    <el-input-number
                      style="width: 100px"
                      v-model="scope.row.commission"
                      :precision="2"
                      :controls="false"
                      placeholder="提成"
                      width="120"
                      :min="0"
                      :max="100000"
                      :disabled="
                        PostAddInfo.wagesSetType == 1 ||
                        PostAddInfo.wagesSetType == 2
                      "
                      @blur="
                        handleInput(
                          scope.row.commission,
                          scope.$index,
                          'commission'
                        )
                      "
                    >
                    </el-input-number
                    >%
                  </template>
                </el-table-column>
                <el-table-column prop="minimumMonthlyKPI" label="月最低KPI">
                  <template slot-scope="scope">
                    <el-input-number
                      style="width: 100px"
                      v-model="scope.row.minimumMonthlyKPI"
                      :controls="false"
                      placeholder="月最低KPI"
                      width="120"
                      :max="100000"
                      :min="0"
                      :disabled="
                        PostAddInfo.wagesSetType == 1 ||
                        PostAddInfo.wagesSetType == 2
                      "
                      @blur="
                        handleInput(
                          scope.row.minimumMonthlyKPI,
                          scope.$index,
                          'minimumMonthlyKPI'
                        )
                      "
                    >
                    </el-input-number>
                  </template>
                </el-table-column>

                <el-table-column prop="packCount" label="操作">
                  <template slot-scope="scope">
                    <el-button
                      type="text"
                      size="small"
                      @click="deleteTableClo(scope.row, scope.$index)"
                      >删除</el-button
                    >
                    <el-button
                      type="text"
                      size="small"
                      @click="copyProp(scope.row, scope.$index)"
                      >复制一行</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <template>
              <span style="padding-top: 10px; float: right">
                <el-button
                  type="primary"
                  :loading="onFinishLoading"
                  @click="onSubmit"
                  >保存</el-button
                >
                <el-button @click="onclose()">取消</el-button>
              </span>
            </template>
          </el-card>
        </el-col>
      </el-row>
    </template>
  </container>
</template>

<script>
import container from "@/components/my-container";
import {
  getPurchaseWagesCalPositions,
  addOrUpdPurchasePostWagesAsync,
  getPurchasePositionsAsync,
} from "@/api/profit/purchasepostwages";
const checkGroup = [
  {
    label: "底薪",
    value: 1,
  },
  {
    label: "底薪+绩效",
    value: 2,
  },
  {
    label: "底薪+绩效+提成",
    value: 3,
  },
];
export default {
  name: "YunHanAdminOpearpurchasepost",
  comments: { container },
  props: {
    filter: {},
  },

  data() {
    return {
      checkGroup,
      wagesSetType: 1,
      that: this,
      addform: {
        id: null,
        company: null,
        purchasePost: null,
        getValueType: [],
        isParticipate: null,
        enabled: null,
        groupCompany: null,
        groupPurchasePost: null,
        detail: {
          getValueType: null,
        },
        purchasePosts: [],
        positEntities: [],
      },
      PostAddInfo: {
        id: null,
        enabled: true, //是否启用
        company: null, //分公司
        purchasePost: null, //岗位
        wagesSetType: null,
        detailSet: [
          {
            id: null,
            enabled: true, //是否启用
            createdTime: null,
            createdUserId: null, //创建者id
            createdUserName: null, //创建者名称
            purchasePositionWagesCalSetId: null, //主键
            employeeType: null, //岗位类型
            baseSalary: null, //底薪
            performance: null, //绩效
            commission: null, //提成
            commissionType: null, //提成类型
            attendanceDays: null, //出勤天数
            minimumMonthlyKPI: null, //月最低KPI
          },
        ],
      },

      rule: [],
      userList: [],
      userListAll: [],
      postList: [],
      postListAll: [],
      listLoading: false,
      onFinishLoading: false,
      isdetail: false,
      isEdit: false,
      isHJZu: false,
      isHJZuGroup: false,
    };
  },

  mounted() {
    this.init();
  },

  methods: {
    changeType(e) {
      if (e == 1) {
        //将表格中的commission,performance,minimumMonthlyKPI的值变为0
        this.PostAddInfo.detailSet.forEach((f) => {
          f.commission = 0;
          f.performance = 0;
          f.minimumMonthlyKPI = 0;
        });
      }
      if (e == 2) {
        //将表格中的commission,minimumMonthlyKPI的值变为0
        this.PostAddInfo.detailSet.forEach((f) => {
          f.commission = 0;
          f.minimumMonthlyKPI = 0;
        });
      }
      this.PostAddInfo.wagesSetType = e;
    },
    handleInput(row, index, type) {
      //如果row为null、""、undefined,就将列表中的当前数据的commission,performance,minimumMonthlyKPI的值变为0
      if (row == null || row == "" || row == undefined) {
        this.PostAddInfo.detailSet[index][type] = 0;
      }
    },
    changeCom(row) {
      if (row.commissionType == "") {
        row.commissionType = null;
      }
    },
    //复制一行
    copyProp(row, i) {
      //为什么要深拷贝,因为浅拷贝只是拷贝了引用地址,如果直接push到列表中,那么列表中的每一项都是同一个对象,修改其中一个,其他的也会跟着改变
      //深拷贝一行
      const copyRow = JSON.parse(JSON.stringify(row));
      //把深拷贝的一行添加到列表中
      this.PostAddInfo.detailSet.push(copyRow);
    },
    deleteTableClo(row, i) {
      this.$confirm("此操作将永久删除该数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          //找出列表和删除的数据相同的下标
          var index = this.PostAddInfo.detailSet.findIndex(
            (item) => item == row
          );
          //删除列表中对应的数据filter
          this.PostAddInfo.detailSet.splice(index, 1);
          this.$message({
            type: "success",
            message: "删除成功!",
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    async init() {
      const { data, success } = await getPurchaseWagesCalPositions();
      if (!success) {
        return;
      } else {
        this.postList = data.map((item, i) => {
          return {
            label: item,
            value: i,
          };
        });
      }
    },
    async onSearch(val) {
      //TODO:这里需要根据分公司和岗位来查询,如果传来的有数据的话
      this.isEdit = val;
      if (val) {
        this.PostAddInfo.id = this.filter.id;
        var { data, success } = await getPurchasePositionsAsync({
          id: this.filter.id,
        });
        if (!success) {
          return;
        }
        // this.PostAddInfo.company = data.company;
        // this.PostAddInfo.purchasePost = data.purchasePost;
        // this.PostAddInfo.detailSet = data.detailSet;
        // this.PostAddInfo.enabled = data.enabled;
        this.PostAddInfo = data;
      } else {
        //如果是新增,这里清空数据
        this.clearData();
      }
    },
    async changeSetCompany() {
      if (
        this.addform.groupCompany === "义乌" ||
        this.addform.groupCompany === "南昌"
      ) {
        this.userList = this.userListAll
          .filter((f) => f.company === this.addform.groupCompany)
          .map((item) => {
            return item;
          });
      } else if (this.addform.groupCompany === "其他") {
        this.userList = this.userListAll
          .filter((f) => f.company !== "南昌" && f.company !== "义乌")
          .map((item) => {
            return item;
          });
      } else {
        this.userList = this.userListAll.map((item) => {
          return item;
        });
      }
      this.addform.purchasePosts = [];
    },
    async changeSetPost() {
      if (this.addform.groupPurchasePost.length > 0) {
        this.userList = this.userListAll
          .filter((f) => f.purchasePost === this.addform.groupPurchasePost)
          .map((item) => {
            return item;
          });
      } else {
        this.userList = this.userListAll.map((item) => {
          return item;
        });
      }
      this.addform.purchasePosts = [];
    },
    addData() {
      if (!this.isEdit) {
        this.PostAddInfo.detailSet.push({
          id: 0,
          enabled: true, //是否启用
          createdTime: null,
          createdUserId: 0, //创建者id
          createdUserName: null, //创建者名称
          purchasePositionWagesCalSetId: 0, //主键
          employeeType: null, //岗位类型
          baseSalary: null, //底薪
          performance: null, //绩效
          commission: null, //提成
          commissionType: null, //提成类型
          attendanceDays: null, //出勤天数
          minimumMonthlyKPI: null, //月最低KPI
        });
      } else {
        //给this.PostAddInfo.detailSet添加一行
        this.PostAddInfo.detailSet.push({
          id: 0,
          enabled: true, //是否启用
          createdTime: null,
          createdUserId: 0, //创建者id
          createdUserName: null, //创建者名称
          purchasePositionWagesCalSetId: 0, //主键
          employeeType: null, //岗位类型
          baseSalary: null, //底薪
          performance: null, //绩效
          commission: null, //提成
          commissionType: null, //提成类型
          attendanceDays: null, //出勤天数
          minimumMonthlyKPI: null, //月最低KPI
        });
      }
    },
    changeData() {
      var length = this.addform.getValueType.length;
      if (length > 0) {
        this.addform.positEntities = [];
        this.addform.getValueType.forEach((f) => {
          this.addform.positEntities.push({
            Id: 0,
            effectiveDate: null,
            employeeType: null,
            getValueType: String(f),
            attendanceDays: null,
            baseSalary: null,
            performance: null,
            commission: null,
            minimumMonthlyKPI: null,
          });
        });
      } else {
        this.addform.positEntities = [];
      }
    },
    async changePost(val) {
      console.log(val, "val");
      const post = this.postList.find(
        (f) => f.value === this.PostAddInfo.purchasePost
      ).label;
      console.log(post, "post");
      this.PostAddInfo.purchasePost = post;
    },
    async changeenable() {
      if (this.addform.isParticipate) {
        this.isdetail = true;
      } else {
        this.isdetail = false;
      }
    },
    async onSubmit() {
      if (this.PostAddInfo.company == null || this.PostAddInfo.company == "") {
        this.$message.warning("温馨提示：请选择分公司！");
        return;
      }
      if (
        this.PostAddInfo.purchasePost == null ||
        this.PostAddInfo.purchasePost == ""
      ) {
        this.$message.warning("温馨提示：请选择岗位！");
        return;
      }
      if (this.PostAddInfo.wagesSetType == null || this.PostAddInfo.wagesSetType == "" || this.PostAddInfo.wagesSetType == undefined || this.PostAddInfo.wagesSetType == 0) {
        this.$message.warning("温馨提示：请选择薪资类型！");
        return;
      }
      this.onSaveData();
    },
    async onSaveData() {
      if (this.PostAddInfo.detailSet.length <= 0) {
        this.$message.warning("请添加数据！");
        return;
      }
      //判断每一项的数据是否为空.如果有为空的就报错
      if (this.PostAddInfo.wagesSetType == 3) {
        this.PostAddInfo.detailSet.forEach((item) => {
          if (
            item.employeeType == null ||
            item.employeeType == "" ||
            item.employeeType == undefined
          ) {
            this.$message.warning("温馨提示：请填写类型！");
            throw "";
          }
          if (
            item.commissionType == null ||
            item.commissionType == "" ||
            item.commissionType == undefined
          ) {
            this.$message.warning("温馨提示：请填写取值！");
            throw "";
          }
        });
      } else {
        this.PostAddInfo.detailSet.forEach((item) => {
          if (
            item.employeeType == null ||
            item.employeeType == "" ||
            item.employeeType == undefined
          ) {
            this.$message.warning("温馨提示：请填写类型！");
            throw "";
          }
        });
      }

      //比较this.PostAddInfo.detailSet中的每一项数据,如果有相同的,则提示
      // const err = this.compareArrayItems(this.PostAddInfo.detailSet);
      // console.log(err, "err");
      // if (err.length > 0) {
      //   setTimeout(() => {
      //     this.$message.warning(err.join(","));
      //   }, 100);
      //   return;
      // }
      const { success } = await addOrUpdPurchasePostWagesAsync(
        this.PostAddInfo
      );
      if (success) {
        this.$emit("onClose", true);
        this.$message({ type: "success", message: "操作成功!" });
      }
    },
    compareArrayItems(arr) {
      console.log(arr, "arr");
      const duplicates = {};
      const result = [];
      for (let i = 0; i < arr.length; i++) {
        if (duplicates[i] !== undefined) {
          continue; // 如果当前项已经比较过，则跳过
        }

        const currentGroup = [i];

        for (let j = i + 1; j < arr.length; j++) {
          if (duplicates[j] !== undefined) {
            continue; // 如果当前项已经比较过，则跳过
          }

          if (JSON.stringify(arr[i]) === JSON.stringify(arr[j])) {
            currentGroup.push(j);
          }
        }

        if (currentGroup.length > 1) {
          result.push(
            `表格中第 ${currentGroup
              .map((index) => index + 1)
              .join(", ")} 项相同`
          );
          currentGroup.forEach((index) => (duplicates[index] = true)); // 标记已经比较过的项
        }
      }
      return result;
    },
    async onclose() {
      this.$emit("onClose", false);
    },
    async clearData() {
      this.PostAddInfo = {
        id: 0,
        enabled: true, //是否启用
        company: null, //分公司
        purchasePost: null, //岗位
        wagesSetType:1,
        detailSet: [
          {
            id: 0,
            enabled: true, //是否启用
            createdTime: null,
            createdUserId: 0, //创建者id
            createdUserName: null, //创建者名称
            purchasePositionWagesCalSetId: 0, //主键
            employeeType: null, //岗位类型
            baseSalary: null, //底薪
            performance: null, //绩效
            commission: null, //提成
            commissionType: null, //提成类型
            attendanceDays: null, //出勤天数
            minimumMonthlyKPI: null, //月最低KPI
          },
        ],
      };
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-table__body-wrapper {
  height: 380px !important;
}
</style>