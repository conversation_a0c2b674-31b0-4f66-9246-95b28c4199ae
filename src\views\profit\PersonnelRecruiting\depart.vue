<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="">
                    <el-select v-model="filter.startEndDateType" placeholder="时间类型" style="width: 100px" size="mini"
                        clearable @clear="() => { filter.daterange = null }">
                        <el-option label="入职" :value="1"></el-option>
                        <el-option label="转正" :value="2"></el-option>
                        <el-option label="工龄" :value="3"></el-option>
                        <el-option label="离职" :value="5"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-date-picker size="mini" :disabled="!filter.startEndDateType" v-model="filter.daterange"
                        type="daterange" range-separator="至" start-placeholder="开始日期" value-format="yyyy-MM-dd"
                        end-placeholder="结束日期" :picker-options="pickerOptions" clearable>
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="">
                    <el-select ref="selectUpResId" v-model="chooseName" clearable style="width: 200px" size="mini"
                        @clear="() => { filter.ddDeptId = null }" placeholder="招聘部门">
                        <el-option hidden value="一级菜单" :label="chooseName"></el-option>
                        <el-tree style="width: 200px;" :data="deptList" :props="defaultProps" :expand-on-click-node="false"
                            :check-on-click-node="true" @node-click="handleNodeClick">
                        </el-tree>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.positionName" placeholder="请输入岗位名称" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.name" placeholder="请输入人才名称" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.recruiterDDUserId" placeholder="招聘专员" style="width: 100px" size="mini"
                        clearable>
                        <el-option v-for="item in recruiterList" :key="item.ddUserId" :label="item.userName"
                            :value="item.ddUserId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.dimissionReason" placeholder="离职原因" clearable size="mini">
                        <el-option label="不符合个人职业规划" value="不符合个人职业规划"></el-option>
                        <el-option label="不适应工作节奏" value="不适应工作节奏"></el-option>
                        <el-option label="升学/考公" value="升学/考公"></el-option>
                        <el-option label="身体原因" value="身体原因"></el-option>
                        <el-option label="家庭原因" value="家庭原因"></el-option>
                        <el-option label="工作态度不端正" value="工作态度不端正"></el-option>
                        <el-option label="学习进度慢/学习能力差" value="学习进度慢/学习能力差"></el-option>
                        <el-option label="业绩不达标" value="业绩不达标"></el-option>
                        <el-option label="未通过试用期" value="未通过试用期"></el-option>
                        <el-option label="联系不上" value="联系不上"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.candidateFrom" placeholder="类型" style="width: 80px" size="mini"
                        clearable>
                        <el-option label="全部" :value="null"></el-option>
                        <el-option label="社招" :value="0"></el-option>
                        <el-option label="内推" :value="1"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.keywords" style="width: 160px" :maxLength="100" placeholder="关键字查询"
                        clearable>
                        <el-tooltip slot="suffix" class="item" effect="dark" content="姓名、电话、部门、岗位、专员、初试部门、复试部门、离职原因"
                            placement="bottom">
                            <i class="el-input__icon el-icon-question"></i>
                        </el-tooltip>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">筛选</el-button>
                </el-form-item>
                <el-form-item>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            style="height: 100%;" :tableData='datalist' @select='selectchange' :isSelection="true"
            :tableCols='tableCols' :isSelectColumn='true' :customRowStyle="customRowStyle" :loading="listLoading"
            :summaryarry="summaryarry" :selectColumnHeight="'0px'" :isBorder="false" :hasexpandRight="true">
            <!-- <template slot='right'>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-link type="primary" @click="editPostion(scope.row.candidateId, 1)">编辑</el-link>
                        <el-link type="primary" style="margin:0 10px" @click="losePostion(scope.row.candidateId)">面谈</el-link>
                        <el-link type="danger" @click="deletePostion(scope.row.candidateId)">删除</el-link>
                    </template>
                </el-table-column>
            </template> -->

        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getDataList" />
        </template>
        <!-- 新增岗位/编辑岗位 -->
        <el-dialog title="编辑人才" :visible.sync="showDialogSon" width="1024px" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag>
            <div style="height: 65vh; overflow-x: hidden;" ref="show_regular">
                <talentInformation v-if="showDialogSon" ref="talentInformation" :candidateInfo="candidateInfo"
                    :isEdit="isEdit" @closeDialog="closeDialog" @closeLoading="() => { subLoad = false }"
                    @openLoading="() => { subLoad = true }"></talentInformation>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showDialogSon = false">取 消</el-button>
                    <el-button type="primary" :loading="subLoad" @click="submitTalent">保存</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 面谈 -->
        <el-dialog title="面谈信息" :visible.sync="showFinishDialog" width="30%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag>
            <el-form label-width="80px" ref="losFormRef" :model="loseForm" :rules="lostrules">
                <el-form-item label="面谈类型" prop="meetingType">
                    <el-select v-model="loseForm.meetingType" placeholder="面谈类型" size="mini">
                        <el-option label="转正" value="转正"></el-option>
                        <el-option label="升职" value="升职"></el-option>
                        <el-option label="调岗" value="调岗"></el-option>
                        <el-option label="离职" value="离职"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item  v-if="loseForm.meetingType == '离职'" style="margin-bottom:0" label-width="0">
                    <el-form-item label="离职类型" prop="dimissionType" >
                        <el-select v-model="loseForm.dimissionType" placeholder="离职类型" size="mini">
                            <el-option label="辞退" value="辞退"></el-option>
                            <el-option label="自离" value="自离"></el-option>
                            <el-option label="辞职" value="辞职"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="离职原因" prop="dimissionReason">
                        <el-select v-model="loseForm.dimissionReason" placeholder="离职原因" size="mini">
                            <el-option label="不符合个人职业规划" value="不符合个人职业规划"></el-option>
                            <el-option label="不适应工作节奏" value="不适应工作节奏"></el-option>
                            <el-option label="升学/考公" value="升学/考公"></el-option>
                            <el-option label="身体原因" value="身体原因"></el-option>
                            <el-option label="家庭原因" value="家庭原因"></el-option>
                            <el-option label="工作态度不端正" value="工作态度不端正"></el-option>
                            <el-option label="学习进度慢/学习能力差" value="学习进度慢/学习能力差"></el-option>
                            <el-option label="业绩不达标" value="业绩不达标"></el-option>
                            <el-option label="未通过试用期" value="未通过试用期"></el-option>
                            <el-option label="联系不上" value="联系不上"></el-option>
                        </el-select>
                    </el-form-item>
            </el-form-item>
                <el-form-item label="数据佐证" v-else>
                    <el-input placeholder="数据佐证" v-model="loseForm.dataSupport" maxlength="80">
                    </el-input>
                </el-form-item>
                <el-form-item label="面谈时间" prop="meetingDate">
                    <el-date-picker v-model="loseForm.meetingDate" value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
                        placeholder="选择日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="面谈结果" prop="meetingContent">
                    <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" placeholder="面谈结果"
                        v-model="loseForm.meetingContent" maxlength="200" show-word-limit>
                    </el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showFinishDialog = false">取 消</el-button>
                    <!-- <my-confirm-button type="submit" @click="submitMeeting"/> -->
                    <el-button type="primary" :loading="subLoad" @click="submitMeeting">保存</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 人才信息 -->
        <el-drawer title="人才信息" size="50%" :visible.sync="drawer" :direction="direction">
            <talentInformation v-if="drawer" :isEdit="isEdit" :candidateInfo="candidateInfo"></talentInformation>
        </el-drawer>
        <!-- 选择招聘岗位 -->
        <el-dialog title="选择招聘岗位" :visible.sync="showAgainJoinDialog" width="1024px" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag>
            <div style="height: 65vh">
                <recruitmentPositionOnGoingSelect ref="onGogingSelect"></recruitmentPositionOnGoingSelect>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showAgainJoinDialog = false">取 消</el-button>
                    <el-button type="primary" :loading="subLoad" @click="submitAgainJoinDialog">确定</el-button>
                </span>
            </template>
        </el-dialog>
    </my-container>
</template>
<script>
import cesTable from "@/components/Table/tableforvedio.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import postionDialogform from "@/views/profit/PersonnelRecruiting/postionDialogform";
import talentInformation from "@/views/profit/PersonnelRecruiting/talentInformation";
import { AllDDDeptTreeNcWh, getDeptUsers } from '@/api/profit/personnel'
import { pageCandidateLz, getCandidateInfo, exportCandidateLz, addHrInterviewMeeting, delCandidate,againJoinEmployee } from '@/api/profit/hr'
import { formatTime } from "@/utils/tools";
import recruitmentPositionOnGoingSelect from "@/views/profit/PersonnelRecruiting/recruitmentPositionOnGoingSelect.vue";

const tableCols = [
    { istrue: true, prop: 'positionName', align: 'left', label: '岗位名称', sortable: 'custom',width: "280"  },
    { istrue: true, prop: 'department', align: 'left', label: '招聘部门', sortable: 'custom', },
    { istrue: true, prop: 'name', align: 'left', label: '姓名', type: "click", handle: (that, row) => that.editPostion(row.candidateId, 0), sortable: 'custom', },
    { istrue: true, prop: 'gender', align: 'left', label: '性别', formatter: (row) => row.gender == null ? '' : row.gender == 1 ? '男' : '女', sortable: 'custom', },
    // { istrue: true, prop: 'phone', align: 'left', label: '联系电话' },
    { istrue: true, prop: 'recruiter', align: 'left', label: '招聘专员', sortable: 'custom', },
    { istrue: true, prop: 'workingDaysDtoField', align: 'left', label: '在职天数', sortable: 'custom' },
    { istrue: true, prop: 'employmentDate', align: 'left', label: '入职时间', formatter: (row) => formatTime(row.employmentDate, 'YYYY-MM-DD'), sortable: 'custom', },
    { istrue: true, prop: 'dimissionDate', align: 'left', label: '离职时间', formatter: (row) => formatTime(row.dimissionDate, 'YYYY-MM-DD'), sortable: 'custom', },
    { istrue: true, prop: 'dimissionType', align: 'left', label: '离职类型', sortable: 'custom', },
    { istrue: true, prop: 'dimissionReason', align: 'left', label: '离职原因', sortable: 'custom', },
    { istrue: true, prop: 'candidateFrom', align: 'center', label: '类型', formatter: (row) => row.candidateFromTxt, sortable: 'custom' },
    {
        istrue: true, type: "button", label: '操作', width: "240",
        btnList: [
            { label: "编辑", permission: "", handle: (that, row) => that.editPostion(row.candidateId, 1) },
            { label: "面谈", permission: "", handle: (that, row) => that.losePostion(row.candidateId) },
            { type: "danger", permission: "", label: "删除", handle: (that, row) => that.deletePostion(row.candidateId) },
            { type: "danger", permission: "", label: "重新入职",handle: (that, row) => that.againJoinPostion(row.candidateId) }
        ]
    }
];

export default {
    name: "depart",//离职人才
    components: {
        MyContainer, postionDialogform, MyConfirmButton
        , cesTable, talentInformation, recruitmentPositionOnGoingSelect
    },
    props: {
        showDialog: {
            type: Boolean,
            default: () => { return false; }
        },
        diologTitle: {
            type: String,
            default: () => { return ''; }
        },
    },
    watch: {
    },
    data () {
        return {
            subLoad: false,
            showDialogSon: this.showDialog,
            chooseName: '',
            defaultProps: {
                children: 'childDeptList',
                label: 'name'
            },
            deptList: [],
            recruiterList: [],
            filter: {
                ddDeptId: null,//
                recruiterDDUserId: null,//
                name: null,//
                initialTestResult: null,//
                finalTestResult: null,//
                positionName: null,//
                queryType: 5,//1面试人才库、2预备人才库、3试用人才库、4正式人才库、5离职人才库
                dimissionReason: null,
                keywords: null,
                startEndDateType: null,
                startDate: null,
                endDate: null,
                daterange: [],
                candidateFrom:null,
            },
            loseForm: {
                candidateId: null,
                meetingDate: null,
                meetingType: null,
                dimissionType: null,
                dimissionReason: null,
                dataSupport: null,
                meetingContent: null,
            },
            lostrules: {
                meetingContent: [
                    { required: true, message: '请输入面谈结果', trigger: 'blur' },
                ],
                meetingDate: [
                    { required: true, message: '请选择面谈日期', trigger: 'change' },
                ],
                meetingType: [
                    { required: true, message: '请选择面谈类型', trigger: 'change' },
                ],
                dimissionType: [
                    { required: true, message: '请选择离职类型', trigger: 'change' },
                ],
                dimissionReason: [
                    { required: true, message: '请选择离职原因', trigger: 'change' },
                ],
            },
            istLoading: false,
            summaryarry: {},
            datalist: [
            ],
            total: 0,
            sels: [], // 列表选中列
            listLoading: false,
            that: this,
            pageLoading: false,
            pager: {},
            tableCols: tableCols,
            isEdit: false,
            showFinishDialog: false,//显示完成弹窗
            showAgainJoinDialog:false,//是否显示重新入职时选择招聘岗位
            drawer: false,
            direction: 'rtl',
            candidateInfo: {},
            pickerOptions: {
                shortcuts: [{
                    text: '近一周',
                    onClick (picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近一个月',
                    onClick (picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近三个月',
                    onClick (picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            candidateId:0,
            openAgainJoinDialogNum:0
        };
    },
    watch: {
    },
    async created () {

    },
    async mounted () {
        this.getDeptList();
        this.getRecruiters();
        this.onSearch();
    },
    methods: {
        // 提交面谈记录
        submitMeeting () {
            this.$refs.losFormRef.validate((valid) => {
                if (valid) {
                    this.subLoad = true
                    addHrInterviewMeeting(this.loseForm).then(res => {
                        this.subLoad = false
                        if (res.success) {
                            this.$message({ message: '提交成功', type: "success" });
                            this.showFinishDialog = false;
                        }
                    })
                }
            })
        },
        //获取招聘专员
        getRecruiters () {
            let params = {
                deptName: '人事组',
                includeAllChildDpt: 1,
            }
            getDeptUsers(params).then(res => {
                if (res.success) {
                    this.recruiterList = res.data;
                } else {
                    this.$message({ message: res.msg, type: "danger" });
                }
            })
        },
        // 节点点击事件
        handleNodeClick (data) {
            // 配置树形组件点击节点后，设置选择器的值，配置组件的数据
            this.chooseName = data.name;
            this.filter.ddDeptId = data.dept_id;
            // 选择器执行完成后，使其失去焦点隐藏下拉框效果
            this.$refs.selectUpResId.blur();
        },
        // 获取部门列表
        async getDeptList () {
            await AllDDDeptTreeNcWh().then(res => {
                if (res.success) {
                    this.deptList = res.data.childDeptList;
                } else {
                    this.$message({ message: res.msg, type: "danger" });
                }
            })
        },
        // 保存人才信息
        async submitTalent () {
            await this.$refs.talentInformation.submitForm();
        },
        //关闭弹窗
        closeDialog () {
            this.showDialogSon = false;
            this.$refs.talentInformation.resetFrom();
            this.onSearch();
        },
        //导出
        async onExport () {
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            var res = await exportCandidateLz(params);// 导出接口
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '离职员工_' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },

        // 筛选
        onSearch (filter) {
            this.$refs.pager.setPage(1);
            this.getDataList();
        },
        // 流失
        losePostion (candidateId) {
            this.loseForm = {
                candidateId: null,
                meetingDate: null,
                meetingType: null,
                dimissionType: null,
                dimissionReason: null,
                dataSupport: null,
                meetingContent: null,
            }
            this.loseForm.candidateId = candidateId;
            this.showFinishDialog = true;
        },
        //删除
        deletePostion (candidateId) {
            this.$confirm('是否确认删除该条人才资料?', '删除人才信息', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await delCandidate({ candidateId: candidateId })
                if (!res?.success) { return }
                this.$message({ type: 'success', message: '删除成功!' });
                this.onSearch()
            }).catch(() => {
                this.$message({ type: 'info', message: '已取消删除' });
            });
        },

        // 编辑
        async editPostion (candidateId, number) {
            await getCandidateInfo({ candidateId: candidateId }).then(res => {
                if (res.success) {
                    this.candidateInfo = res.data;
                }
            })
            if (number) {
                this.showDialogSon = true;
                this.isEdit = true;
                this.$nextTick(function () {
                    this.$refs.talentInformation.openPosition()
                    setTimeout(() => {
                        let scrollEl = this.$refs.show_regular;
                        scrollEl.scrollTo({ top: scrollEl.scrollHeight, behavior: 'smooth' });
                    }, 300)
                })
            } else {
                this.drawer = true;
                this.isEdit = false;
            }
        },

        //获取数据
        async getDataList () {
            if (this.filter.daterange) {
                this.filter.startDate = this.filter.daterange[0];
                this.filter.endDate = this.filter.daterange[1];
            } else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            this.listLoading = true;
            const res = await pageCandidateLz(params);
            this.listLoading = false;
            this.total = res.data.total
            this.datalist = res.data.list;
            this.summaryarry = res.data.summary;
        },

        //列表排序
        sortchange (column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        //多选事件
        selectchange: function (rows, row) {
            this.self = [];
            rows.forEach(f => {
                this.self.push(f.shopDecorationTaskId);
            })
        },

        customRowStyle (row, index) {
            if (row.row?.isend && row.row.isend == 1) {
                let styleJson = {};
                styleJson.color = "rgb(216 216 216)";
                return styleJson
            } else {
                return null
            }

        },

        //重新入职
        againJoinPostion (candidateId) {
            this.candidateId = candidateId;
            this.showAgainJoinDialog = true;
            this.openAgainJoinDialogNum++;
            if(this.openAgainJoinDialogNum > 1)
            {
                this.$refs.onGogingSelect.resetQuery();
                this.$refs.onGogingSelect.onSearch();
            }
            // this.$confirm('是否确认为该人才办理重新入职?', '人才重新入职', {
            //     confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            // }).then(async () => {
            //     // const res = await againJoinEmployee({ candidateId: candidateId })
            //     const res = await againJoinEmployee(candidateId)
            //     if (!res?.success) { return }
            //     this.$message({ type: 'success', message: '重新入职成功!' });
            //     this.onSearch()
            // }).catch(() => {
            //     this.$message({ type: 'info', message: '已取消重新入职' });
            // });
        },
        async submitAgainJoinDialog(){
            var row = this.$refs.onGogingSelect.getSelectRow();
            if(row){            
                const res = await againJoinEmployee({ CandidateId: this.candidateId,PlanId:row.planId })

                this.showAgainJoinDialog = false;
                
                if (!res?.success) { return }
                this.$message({ type: 'success', message: '重新入职成功!' });
                this.onSearch()

            }
            else
            {
                this.$alert('请选择招聘计划!');
            }
        }
    },
};
</script>
