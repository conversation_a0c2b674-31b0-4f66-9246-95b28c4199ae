<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <!-- <el-date-picker v-model="ListInfo.calculateMonth" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" type="month" style="width: 250px;margin-right: 5px;" :clearable="false"
          :value-format="'yyyy-MM'">
        </el-date-picker> -->
                <el-date-picker v-model="ListInfo.calculateMonthArr" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" type="monthrange"
                    style="width: 250px;margin-right: 5px;" :clearable="false" :value-format="'yyyy-MM'">
                </el-date-picker>
                <el-select v-model="ListInfo.type" placeholder="类型" class="publicCss" clearable @change="changeType">
                    <el-option v-for="item in typeList" :key="item" :label="item" :value="item" />
                </el-select>
                <el-select v-model="ListInfo.regionName" placeholder="区域" class="publicCss" clearable multiple
                    collapse-tags>
                    <el-option v-for="item in districtList" :key="item" :label="item" :value="item" />
                </el-select>
                <el-select v-model="ListInfo.deptName" placeholder="部门" class="publicCss" clearable multiple
                    collapse-tags>
                    <el-option v-for="item in sectionList" :key="item" :label="item" :value="item" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="startImport"
                    v-if="checkPermission('ArchiveStatusEditing')">导入</el-button>
                <el-button type="primary" @click="startImport" v-else :disabled="lastUpdateTime">导入</el-button>
                <el-button type="primary" @click="downExcel">模板下载</el-button>
                <el-button type="primary" @click="exportProps">导出</el-button>
                <el-button type="primary" @click="saveBane('search')">存档</el-button>
                <div style="display: flex;align-items: center;margin: 0 10px;font-size: 15px;color: red;">
                    存档时间: {{ lastUpdateTime ?? '-' }}
                </div>
            </div>
        </template>
        <vxetablebase border :id="'departmentDimensionality202412161432'" :tablekey="'departmentDimensionality202412161432'"
            ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
            :tableData='tableData' :somerow="somerow" :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            :summaryarry='summaryarry' :footerDataArray='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
            :height="'100%'" :cellClassName="cellClassName" :mergeColumn="mergeColumn">
            <template slot="right">
                <vxe-column title="操作" width="90" fixed="right">
                    <template #default="{ row }">
                        <div style="display: flex;justify-content: center;width: 100%;">
                            <el-button v-if="row.deptType.indexOf('小计')==-1" type="text"
                                @click="handleEdit(row)">编辑</el-button>
                            <!-- <el-button v-else type="text" @click="handleEdit(row)"
                                :disabled="row.status == '1'">编辑</el-button> -->
                            <el-button type="text" v-if="row.deptType.indexOf('小计')==-1" size="mini"
                                @click="handleDelete(row)">删除</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
            <span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                    :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-drawer title="编辑" :visible.sync="dialogVisibleEdit" size="25%">
            <departmentEdit ref="departmentEdit" v-if="dialogVisibleEdit" :editInfo="editInfo"
                @cancellationMethod="cancellationMethod" />
        </el-drawer>


    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/views/profit/sscManager/ePeoplefx/yh_vxetable.vue";
import dayjs from 'dayjs'
import departmentEdit from "./departmentEdit.vue";
import { downloadLink } from "@/utils/tools.js";
import checkPermission from '@/utils/permission'
import { departmentDimensionPage, departmentDimensionArchive, departmentDimensionExport, departmentDimensionImport, departmentDimensionRemove } from '@/api/people/peoplessc.js';
const tableCols = [
    { width: 'auto', align: 'center', prop: 'calculateMonth', label: '月份', },
    { width: 'auto', align: 'center', prop: 'type', label: '类型', },
    { width: 'auto', align: 'center', prop: 'regionName', label: '区域', },
    { width: 'auto', align: 'center', prop: 'deptType', label: '部门类型', },
    { width: 'auto', align: 'center', prop: 'deptName', label: '部门', },
    { width: 'auto', align: 'center', prop: 'recruitDemandCount', label: '招聘需求人数', },
    // { width: 'auto', align: 'center', prop: 'initialCount', label: '期初人数', },
    { width: 'auto', align: 'center', prop: 'newHiresCount', label: '入职人数', },
    { width: 'auto', align: 'center', prop: 'recruitAchievementRate', label: '招聘达成率', formatter: (row) => row.recruitAchievementRate ? row.recruitAchievementRate + '%' : '0%' },
    // { width: '120', align: 'center', prop: 'newPeopleResignCount', label: '试用期离职人数', },
    // { width: '120', align: 'center', prop: 'oldPeopleResignCount', label: '正式离职人数', },
    // { width: 'auto', align: 'center', prop: 'transferCount', label: '调入人数', },
    // { width: 'auto', align: 'center', prop: 'outCount', label: '调出人数', },
    // { width: 'auto', align: 'center', prop: 'resignationsCount', label: '离职人数', },
    { width: 'auto', align: 'center', prop: 'newPeopleResignCount', label: '新人离职人数', },
    { width: 'auto', align: 'center', prop: 'newPeopleResignRate', label: '新人离职率', formatter: (row) => row.newPeopleResignRate ? row.newPeopleResignRate + '%' : '0%' },
    { width: 'auto', align: 'center', prop: 'notes', label: '备注', },


    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'endPeriodCount', label: '期末人数', },
]
export default {
    name: "departmentDimensionality",
    components: {
        MyContainer, vxetablebase, departmentEdit
    },
    data() {
        return {
            mergeColumn: {
              column: ['calculateMonth', 'type', 'regionName', 'deptType', 'deptName'],
              default: 'type'
            },
            somerow: 'regionName,type,calculateMonth',
            downloadLink,
            districtList: [],
            allDistrictList: [], // 存储总数据
            sectionList: [],
            typeList: [],
            editInfo: {},
            dialogVisibleEdit: false,
            lastUpdateTime: '',
            dialogVisible: false,
            uploadLoading: false,
            fileList: [],
            fileparm: {},
            that: this,
            ListInfo: {
                // calculateMonth: dayjs().subtract(0, 'month').format('YYYY-MM'),
                calculateMonthArr: [dayjs().subtract(0, 'month').format('YYYY-MM'), dayjs().subtract(0, 'month').format('YYYY-MM')],
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                // startTime: null,//开始时间
                // endTime: null,//结束时间
                regionName: [],
                deptName: [],
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            summaryarry: [],
            total: 0,
            loading: false,
            pickerOptions: {
                shortcuts: [{
                    text: '本月',
                    onClick(picker) {
                        picker.$emit('pick', [new Date(), new Date()]);
                    }
                }, {
                    text: '今年至今',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date(new Date().getFullYear(), 0);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近六个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 6);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
        }
    },
    async mounted() {
        if (this.timeRanges && this.timeRanges.length == 0) {
            this.ListInfo.startTime = dayjs().startOf('month').format('YYYY-MM-DD')
            this.ListInfo.endTime = dayjs().endOf('month').format('YYYY-MM-DD')
            this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime];
        }
        await this.getList()
    },
    methods: {
        changeType(e){
          if (!e) {
            this.districtList = [...this.allDistrictList];
            return;
          }
          if (e === '办公室') {
            this.districtList = this.allDistrictList.filter(item => !item.includes('仓'));
          } else {
            this.districtList = this.allDistrictList.filter(item => item.includes('仓'));
          }
        },
        downExcel() {
            downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250710/1943138433237491713.xlsx', '招聘部门分析_导入模板.xlsx');
        },
        cellClassName(val){
            if(val.row.deptType.indexOf("小计")>-1){
                return 'coloryellow'
            }
        },
        async saveBane() {
            this.$confirm('是否存档？存档后不可修改！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { data, success } = await departmentDimensionArchive(this.ListInfo)
                if (!success) {
                    return;
                }
                this.getList();
                this.$message.success('保存存档成功！')

            }).catch(() => {
                // this.$message.error('取消')
            });


        },
        cancellationMethod(val) {
            this.dialogVisibleEdit = false
            if (val == 1) {
                this.getList('search')
            }
        },
        handleEdit(row) {
            this.editInfo = JSON.parse(JSON.stringify(row))
            this.dialogVisibleEdit = true
        },
        async handleDelete(row) {
            this.$confirm('是否删除！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.editInfo = row;
                this.editInfo.calculateMonthStart = this.ListInfo.calculateMonthArr[0]
                this.editInfo.calculateMonthEnd = this.ListInfo.calculateMonthArr[1]
                this.loading = true
                const { data, success } = await departmentDimensionRemove(this.editInfo)
                this.loading = false
                if (success) {
                    this.$message.success('删除成功')
                    this.getList();
                } else {
                    this.$message.error('删除失败')
                }
            }).catch(() => {

            });


        },
        async changeTime(e) {
            this.ListInfo.startTime = e ? e[0] : null
            this.ListInfo.endTime = e ? e[1] : null
        },
        //上传文件
        onUploadRemove(file, fileList) {
            this.fileList = []
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            this.dialogVisible = false;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            const form = new FormData();
            form.append("file", item.file);
            form.append("isArchive", checkPermission("ArchiveStatusEditing"));
            var res = await departmentDimensionImport(form);
            if (res?.success) {
                this.$message({ message: res.msg, type: "success" });
            }
            this.uploadLoading = false
            this.dialogVisible = false;
            await this.getList()
        },
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        //导入弹窗
        startImport() {
            this.fileList = []
            this.dialogVisible = true;
        },
        async exportProps() {
            this.$nextTick(()=>{
              this.$refs.table.exportDataMethod({filename:'招聘部门分析'+ new Date().toLocaleString(),    sheetName: 'Sheet1',type: 'xlsx' })
            })
            return
            this.loading = true
            let region = this.ListInfo.regionName ? this.ListInfo.regionName : []
            let dept = this.ListInfo.deptName ? this.ListInfo.deptName : []
            this.ListInfo.regionName = region.join(',')
            this.ListInfo.deptName = dept.join(',')
            const data = await departmentDimensionExport(this.ListInfo)
            this.loading = false
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '招聘部门分析' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            if (this.ListInfo.calculateMonthArr && this.ListInfo.calculateMonthArr.length > 0) {
                this.ListInfo.calculateMonthStart = this.ListInfo.calculateMonthArr[0]
                this.ListInfo.calculateMonthEnd = this.ListInfo.calculateMonthArr[1]
            }
            this.loading = true
            let region = this.ListInfo.regionName ? this.ListInfo.regionName : []
            let dept = this.ListInfo.deptName ? this.ListInfo.deptName : []
            const { data, success } = await departmentDimensionPage({ ...this.ListInfo, regionName: region.join(','), deptName: dept.join(',') })
            if (success) {
                this.tableData = data.list
                const newTypes = this.tableData.map(item => item.type).filter(district => district !== undefined && district !== null)
                this.typeList = Array.from(new Set([...this.typeList, ...newTypes]));
                const newDistricts = this.tableData.map(item => item.regionName).filter(district => district !== undefined && district !== null)
                this.allDistrictList = Array.from(new Set([...this.allDistrictList, ...newDistricts]));
                if (!this.ListInfo.type) {
                  this.districtList = [...this.allDistrictList];
                }
                const newsections = this.tableData.map(item => item.deptName).filter(section => section !== undefined && section !== null)
                this.sectionList = Array.from(new Set([...this.sectionList, ...newsections]));
                this.total = data.total


                // data.summary && Object.keys(data.summary).forEach(key => {
                //     if (data.summary[key]) {
                //         data.summary[key] = data.summary[key]?.toFixed(0)
                //     }
                // })
                let a = [
                    'newPeopleResignRate',
                    'recruitAchievementRate',
                ];
                data.summary.forEach(summaryItem => {
                  a.forEach(key => {
                    if (summaryItem[key] != null && summaryItem[key] !== '') {
                      summaryItem[key] = `${summaryItem[key]}%`;
                    } else {
                      summaryItem[key] = '0%';
                    }
                  });
                });
                this.summaryarry = data.summary
                // this.lastUpdateTime = data.list.length > 0 ? data.list[0].archiveTime : ''
                if (data.summary) {
                    this.lastUpdateTime = data.summary.archiveTime
                }
                this.loading = false
            } else {
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 160px;
        margin-right: 5px;
    }
}

::v-deep .el-select__tags-text {
    max-width: 35px;
}

:deep(.vxe-header--column) {
    background: #00937e;
    color: white;
    font-weight: 600;
}

:deep(.vxe-footer--row) {
    background: #00937e;
    color: white;
    font-weight: 600;
}
:deep(.coloryellow){
        background: #fff2cc;
        color: black;
        font-weight: 600;
    }
</style>
