<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-input v-model.trim="filter.name" :maxlength="40" clearable placeholder="姓名" style="width: 120px" />
        <el-select filterable v-model="filter.company" collapse-tags clearable placeholder="分公司" style="width: 120px">
          <el-option key="义乌" label="义乌" value="义乌"></el-option>
          <el-option key="南昌" label="南昌" value="南昌"></el-option>
          <el-option key="武汉" label="武汉" value="武汉"></el-option>
          <el-option key="深圳" label="深圳" value="深圳"></el-option>
          <el-option key="其他" label="其他" value="其他"></el-option>
        </el-select>
        <el-select filterable v-model="filter.purDept" placeholder="架构" style="width:300px;" clearable>
          <el-option v-for="item in bianMaBrandPurDeptList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select filterable v-model="filter.enabled" collapse-tags clearable placeholder="是否启用" style="width: 120px">
          <el-option label="是" value=true></el-option>
          <el-option label="否" value=false></el-option>
        </el-select>
        <el-select filterable v-model="filter.isLeave" collapse-tags clearable placeholder="在职状态" style="width: 120px">
          <el-option label="在职" value="在职"></el-option>
          <el-option label="离职" value="离职"></el-option>
        </el-select>

        <el-date-picker v-model="filter.lztimerange" type="daterange" unlink-panels range-separator="至" clearable
          start-placeholder="离职日期" end-placeholder="离职日期" :picker-options="pickerOptions" style="width: 240px;"
          :value-format="'yyyy-MM-dd'" />

        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onSetShow">设置</el-button>
          <el-button type="primary" @click="onExport">导出</el-button>
        </el-form-item>
      </el-form>
    </template>

    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='list'
      :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='false' :loading="listLoading"
      @cellclick="cellclick">
    </ces-table>

    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <el-drawer :title="formtitle" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
      v-loading="drawerLoading" :visible.sync="addFormVisible" direction="btt" size="'auto'" class="el-drawer__wrapper"
      style="position:absolute;">
      <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options" />
      <div class="drawer-footer">
        <el-button @click.native="addFormVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="addLoading" @click="onAddSubmit" />
      </div>
    </el-drawer>

    <el-drawer title="组员信息" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
      :visible.sync="teamInfosVisible" direction="rtl" size="300" class="el-drawer__wrapper" style="position:absolute;">
      <ces-table ref="teamTable" :treeprops="{ children: 'children', hasChildren: true }" :defaultexpandall='true'
        rowkey="key" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn='false'
        :tableData='teamlist' :tableCols='teamtableCols' :tableHandles='tableHandles' :showsummary='false'
        :loading="listLoading">
      </ces-table>
    </el-drawer>


    <el-dialog title="设置" :visible.sync="setDialog.visible" :close-on-click-modal="false" append-to-body width="610px"
      v-loading="setDialog.loading" v-dialogDrag>
      <el-row style="padding-top: 10px;">
        <el-col :span="24">
          离职后<el-input-number v-model.trim="setDialog.data.autoCloseDays" placeholder="请输入" :min="0" :max="999"
            style="width: 80px;" :precision="0" :controls="false" />天自动禁用
        </el-col>
      </el-row>
      <el-row style="padding-top: 15px;">
        <el-col :span="24">
          离职后重新启用后<el-input-number v-model.trim="setDialog.data.autoCloseDays2" placeholder="请输入" :min="0" :max="999"
            style="width: 80px;" :precision="0" :controls="false" />天自动禁用
        </el-col>
      </el-row>
      <el-row style="padding-top: 20px;">
        <span style="font-size: 10px; color:red;">为空不自动关闭，输入时间加流程完结时间自动关闭，如1月1日流程通过，为0则1月1当天自动关闭；为10则1月1日+10，1月11日0点自动关闭
        </span>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-transfer v-model="setDialog.data.roles" :data="chosenRoles" :props="{key: 'value', label: 'label'}" filterable :titles="['待选角色', '已选角色']">
          </el-transfer>
        </el-col>
      </el-row>
      <template #footer>

        <span class="dialog-footer">
          <el-button type="primary" @click="onSetSave">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog title="离职记录" :visible.sync="leaveLogDialog.visible" :close-on-click-modal="false" append-to-body
      width="400px" v-loading="leaveLogDialog.loading" v-dialogDrag>
      <el-table :data="leaveLogDialog.data" :height="240">
        <el-table-column label="序号" width="50">
          <template slot-scope="scope">{{ scope.$index + 1 }}</template>
        </el-table-column>
        <el-table-column prop="leaveDate" label="离职日期" width="150" />
      </el-table>
    </el-dialog>

    <el-dialog title="编辑岗位" :visible.sync="positionFormVisible" width="45%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 100px;display: flex;align-items: center;justify-content: center;">
        <el-form :model="positionForm" label-width="70px" :rules="positionRules" ref="positionFormRef">
          <el-row>
            <el-col :span="6">
              <el-form-item label="旧岗位:">
                {{ positionForm.oldTitle }}
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item label="新岗位:" prop="title">
                <el-select v-model.trim="positionForm.title" filterable clearable placeholder="岗位"
                  style="width: 100%">
                <el-option v-for="item in positionList" :key="item.titleName" :label="item.titleName"
                  :value="item.titleName" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item label="生效日期:" prop="titleTime" label-width="85px">
                <el-date-picker v-model="positionForm.titleTime" type="date" placeholder="选择日期" :clearable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="positionFormVisible = false">取消</el-button>
        <el-button type="primary" @click="onPositionSave">确定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="操作日志" :visible.sync="operationLogDialog.visible" append-to-body width="40%" v-dialogDrag>
      <div>
        <vxetablebase v-if="operationLogDialog.visible" :id="'operationLogDialog202504211125'" :tablekey="'operationLogDialog202504211125'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
          :tablefixed='true' :tableData='operationLogDialog.data' :tableCols='tableColsLog' :isSelection="false"
          :isSelectColumn="false" style="width: 100%; margin: 0"
          :loading="operationLogDialog.loading" :height="'300px'">
        </vxetablebase>
      </div>
   </el-dialog>
  </my-container>
</template>

<script>
import {
  pageBianMaBrand, getBianMaBrand, updateBianMaBrand, getAllBianMaBrandByFilter, getBianMaBrandTeamTree, GetBianMaBrandPurDept,
  GetBianMaBrandLeaveSet, SaveBianMaBrandLeaveSet, GetBianMaBrandLeaveLogList, ExportBianMaBrandAsync, UpdateTitle, GetTitleHistory, checkRepeatNameAddUser
} from '@/api/inventory/warehouse'
import { getBrandPositionList } from '@/api/inventory/purchaseordernew'
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatYesornoBool, formatNoLink, formatTime } from "@/utils/tools";
import { ruleMultityProBrand } from '@/utils/formruletools'
import dayjs from 'dayjs'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
const chosenRoles = [{value: '采购专员', label: '采购专员'}, {value: '核价组', label: '核价组'}, {value: '采购文员', label: '采购文员'}, {value: '加工组', label: '加工组'}, {value: '跨境仓', label: '跨境仓'}, {value: '选品助理', label: '选品助理'}, {value: '外采组', label: '外采组'}]
const tableCols = [
  { istrue: true, prop: 'id', label: '编号', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'brandName', label: '姓名', width: '100', sortable: 'custom', type: "html", formatter: (row) => formatNoLink(row.brandName) },
  { istrue: true, prop: 'company', label: '分公司', width: '100' },
  { istrue: true, prop: 'purDept', label: '架构', width: '300' },
  { istrue: true, prop: 'dDeptName', label: '岗位名称', width: '150', type: 'click', handle: (that, row) => that.onDDeptNamePosition(row) },
  // { istrue: true, prop: 'oldTitle', label: '原岗位', width: '80', sortable: 'custom' },
  // { istrue: true, prop: 'titleTime', label: '生效日期', width: '80', sortable: 'custom', formatter: (row) => formatTime(row.titleTime, "YYYY-MM-DD") },
  { istrue: true, prop: 'enabled', label: '是否启用', width: '80', type: 'click', formatter: (row) => formatYesornoBool(row.enabled), style: (that, row) => that.renderenabledStatus(row), },
  { istrue: true, prop: 'sort', label: '排序', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'withholdIsTrue', label: '是否参与采购扣款排序', width: '160', type: 'click', formatter: (row) => formatYesornoBool(row.withholdIsTrue), style: (that, row) => that.renderwithholdStatus(row), },
  { istrue: true, prop: 'isAutoOrder', label: '是否自动开单', width: 'auto', width: '160', formatter: (row) => formatYesornoBool(row.isAutoOrder), },

  { istrue: true, prop: 'isLeave', label: '在职状态', width: '80' },
  { istrue: true, prop: 'hiredDate', label: '入职时间', width: '80', formatter: (row) => formatTime(row.hiredDate, "YYYY-MM-DD") },
  {
    istrue: true, prop: 'leaveDate', label: '离职时间', width: '120', sortable: 'custom',
    type: 'click', formatter: (row) => formatTime(row.leaveDate, "YYYY-MM-DD"), handle: (that, row) => that.onLzClick(row)
  },
  { istrue: true, prop: 'inJobDays', label: '在职时长', width: '80' },
  {
    istrue: true, prop: 'titleTime', label: '岗位变动时间', width: '125', sortable: 'custom',
    type: 'click', formatter: (row) => formatTime(row.titleTime, "YYYY-MM-DD"), handle: (that, row) => that.onChangeTime(row)
  },
  { istrue: true, type: 'button', label: '操作', btnList: [{ label: "编辑", handle: (that, row) => that.onEdit(row) }] }
];

const teamtableCols = [
  { istrue: true, prop: 'id', label: '编号', width: '150', sortable: 'custom', formatter: (row) => row.key },
  { istrue: true, prop: 'brandName', label: '姓名', width: '200', sortable: 'custom', formatter: (row) => row.value }
];
const tableColsLog = [
  { istrue: true, prop: 'changeTime', label: '生效日期', width: 'auto', formatter: (row) => formatTime(row.changeTime, "YYYY-MM-DD") },
  { istrue: true, prop: 'oldTitle', label: '原岗位', width: 'auto' },
  { istrue: true, prop: 'title', label: '新岗位', width: 'auto' },
  { istrue: true, prop: 'changeUserName', label: '操作人', width: 'auto' }
];

const tableHandles1 = [
  { label: "新增组员", handle: (that) => that.onAdd() }
];
export default {
  name: 'Roles',
  components: { cesTable, MyContainer, MyConfirmButton, vxetablebase },
  data() {
    return {
      tableColsLog,
      operationLogDialog: {
        data: [],
        visible: false,
        loading: false,
      },
      positionList: [],
      positionForm: {
        id: null,
        oldTitle: null,
        title: null,
        titleTime: null,
        userId: null,
      },
      positionRules: {
        title: [{ required: true, message: '请选择岗位', trigger: 'change' }],
        titleTime: [{ required: true, message: '请选择生效日期', trigger: 'change' }],
      },
      positionFormVisible: false,
      chosenRoles,
      that: this,
      filter: {
        name: '', company: null, purDept: null,
        isLeave: '在职',
        lztimerange: [],
        leaveStartDate: null,
        leaveEndDate: null,
      },
      list: [],
      pager: { OrderBy: "", IsAsc: true },
      tableCols: tableCols,
      tableHandles: tableHandles1,
      autoform: {
        fApi: {},
        options: { submitBtn: false, global: { '*': { props: {}, col: { span: 8 } } } },
      },
      total: 0,
      sels: [],
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,
      deleteLoading: false,
      formtitle: "新增",
      teamlist: [],
      teamInfosVisible: false,
      teamtableCols: teamtableCols,
      bianMaBrandPurDeptList: [],


      drawerLoading: false,

      setDialog: {
        data: {
          roles: [],
        },
        visible: false,
        loading: false,
      },
      leaveLogDialog: {
        data: [],
        visible: false,
        loading: false,
      },


    }
  },
  async mounted() {
    this.getlist()
    this.getBianMaBrandPurDeptList();
    await this.onInitautoform();
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async onChangeTime(row){
      this.operationLogDialog.loading = true;
      const {data,success} = await GetTitleHistory({userId: row.userId});
      if(success){
        this.operationLogDialog.data = data;
        this.operationLogDialog.visible = true;
        this.operationLogDialog.loading = false;
      }
    },
    onDDeptNamePosition(row) {
      this.positionForm.title = null;
      this.positionFormVisible = true;
      this.$nextTick(() => {
        this.$refs.positionFormRef.clearValidate();
        this.positionForm.id = row.id;
        this.positionForm.oldTitle = row.dDeptName;
        this.positionForm.userId = row.userId;
        this.positionForm.titleTime = dayjs().format('YYYY-MM-DD');
      })
    },
    async onPositionSave() {
      this.$refs.positionFormRef.validate(async (valid) => {
        if (valid) {
          const res = await UpdateTitle(this.positionForm);
          if(res?.success){
            this.$message({ message: '更新成功', type: "success" })
            this.positionFormVisible = false;
            this.onSearch();
          }
        }
      })
    },
    async getBianMaBrandPurDeptList() {
      const res = await GetBianMaBrandPurDept();
      res?.data?.forEach(f => {
        this.bianMaBrandPurDeptList.push({ value: f.dept_id, label: f.full_name })
      });
      var resPosition = await getBrandPositionList();
      this.positionList = resPosition?.data
      if(this.positionList.length == 0){
        this.positionList = ["采购文员", "采购行政主管", "采购主管", "采购助理", "采购专员", "采购组长", "抖音行政运营专员", "核价主管", "跨境采购", "跨境采购组长", "软件测试", "软件测试组长", "退件员", "外采专员", "项目采购", "行政文员", "预备采购主管", "预备采购组长", "正式采购助理"].map(titleName => ({ titleName }));;
      }
    },
    async selectSpecialRoleType() {
      let type = this.autoform.fApi.getValue('specialRoleType');
      if (type == 2) {
        this.autoform.fApi.hidden(false, 'specialBrandIds')
      } else {
        this.autoform.fApi.hidden(true, 'specialBrandIds')
      }
      this.autoform.fApi.setValue('specialBrandIds', [])
    },
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async onInitautoform() {
      let rule = [{ type: 'hidden', field: 'id', title: 'id', value: '0' },
      { type: 'input', style: "width:220px", field: 'brandName', title: '姓名', props: { disabled: false }, validate: [{ type: 'string', required: true, message: '请输入姓名' }], },
      { type: 'select', field: 'enabled', title: '是否启用', validate: [{ type: 'boolean', required: true, message: '请选择' }], value: false, options: [{ value: false, label: '否' }, { value: true, label: '是' }] },
      { type: 'select', field: 'withholdIsTrue', title: '是否参与采购扣款排序', validate: [{ type: 'boolean', required: true, message: '请选择' }], value: false, options: [{ value: false, label: '否' }, { value: true, label: '是' }] },
      { type: 'select', field: 'isHomePageShow', title: '是否首页显示', validate: [{ type: 'boolean', required: true, message: '请选择' }], value: true, options: [{ value: true, label: '是' }, { value: false, label: '否' }] },
      {
        type: 'select', style: "width:300px", field: 'userId', title: '登录用户', validate: [{ type: 'number', required: true, message: '请选择用户' }], options: [],
        props: { filterable: true, allowCreate: false, clearable: true, remote: true, remoteMethod: (parm) => this.remoteSearchUser(parm) }
      },
      {
        type: 'select', field: 'pBianMaBrandId', title: '上级组长', validate: [{ required: false }], value: "", options: [],
        props: { filterable: true, allowCreate: false, clearable: true, remote: true, remoteMethod: (parm) => this.remoteSearchBianMaBrand(parm) }
      },
      { type: 'InputNumber', field: 'sort', title: '排序', validate: [{ type: 'number', required: true, message: '请选择' }], value: 0, props: { min: 0, max: 99999999, precision: 0 } },
      {
        type: "radio", field: "specialRoleType", title: "采购数据特殊权限", value: null,
        options: [{ value: 0, label: "无", disabled: false }, { value: 1, label: "全部", disabled: false }, { value: 2, label: "部分", disabled: false }],
        on: { change: () => { this.selectSpecialRoleType() } }
      },
      {
        type: "select", field: "specialBrandIds", size: "large", title: "采购数据特殊权限", value: null, ...await ruleMultityProBrand(),
        props: { multiple: true, clearable: true, filterable: true, 'collapse-tags': true }, validate: [{ required: false }]
      },
      {
        type: 'select', field: 'company', title: '分公司', value: null,
        options: [{ value: "义乌", label: "义乌", disabled: false }, { value: "南昌", label: "南昌", disabled: false },
        { value: "武汉", label: "武汉", disabled: false }, { value: "深圳", label: "深圳", disabled: false },
        { value: "其他", label: "其他", disabled: false }], col: { span: 8 }, validate: [{ required: true, message: '请选择分公司' }]
      },
      {
        type: 'select', field: 'purchaseWage', title: '采购薪资', value: null,
        options: [{ value: 0, label: "关", disabled: false }, { value: 1, label: "开", disabled: false }], col: { span: 8 }
      },
      { type: 'input', field: 'isLeave', title: '在职状态', props: { disabled: true }, style: "width:180px", },
      { type: 'input', field: 'leaveDate', title: '离职时间', props: { disabled: true }, style: "width:180px", },

      ];
      this.autoform.rule = rule
    },
    getParam() {
      if (this.filter.lztimerange && this.filter.lztimerange.length == 2) {
        this.filter.leaveStartDate = this.filter.lztimerange[0];
        this.filter.leaveEndDate = this.filter.lztimerange[1];
      }
      else {
        this.filter.leaveStartDate = null;
        this.filter.leaveEndDate = null;
      }
      let pager = this.$refs.pager.getPager()
      const params = { ...pager, ... this.pager, ... this.filter }
      return params;
    },
    async getlist() {
      const params = this.getParam();
      this.listLoading = true
      const res = await pageBianMaBrand(params)
      this.listLoading = false
      if (!res?.success) return
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
    async onExport() {
      const params = this.getParam();
      this.listLoading = true
      var res = await ExportBianMaBrandAsync(params);
      this.listLoading = false
      if (!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '采购员导出_' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async remoteSearchUser(parm) {
      if (!parm) {
        return;
      }
      var options = [];
      let rlt = await QueryAllDDUserTop100({ keywords: parm });
      if (rlt && rlt.success) {
        options = rlt.data?.map(item => {
          return { label: item.userName + "(" + item.deptName + ")", value: item.userId, extData: item }
        });
      }
      this.autoform.fApi.getRule('userId').options = options;
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    async onAdd() {
      this.addFormVisible = true
      var arr = Object.keys(this.autoform.fApi);
      await this.$nextTick(async () => {
        if (arr.length > 0) {
          await this.autoform.fApi.resetFields()
          await this.autoform.fApi.disabled(false, 'brandName')
        }
        this.autoform.fApi.setValue('purchaseWage', 0)
        this.autoform.fApi.setValue('specialRoleType', 0)
        this.autoform.fApi.setValue('isLeave', '在职')
      })

    },
    async onEdit(row) {
      this.formtitle = '编辑';
      this.addFormVisible = true
      await this.$nextTick(function () {
        this.autoform.fApi.hidden(true, 'specialBrandIds')
        this.autoform.fApi.disabled(true, 'brandName')
        this.autoform.fApi.setValue('specialRoleType', 0)
      })
      this.drawerLoading = true;
      let res = await getBianMaBrand({ id: row.id })
      //获取组长信息
      if (res.data.pBianMaBrandId > 0) {
        const res2 = await getBianMaBrand({ id: res.data.pBianMaBrandId })
        await this.remoteSearchBianMaBrand(res2.data.brandName)
      }
      this.drawerLoading = false;
      let options = [{ value: res.data.userId, label: res.data.userFullName }];
      this.autoform.fApi.getRule('userId').options = options;
      await this.autoform.fApi.setValue(res.data)
      if (res.data.specialRoleType == 2) {
        this.autoform.fApi.hidden(false, 'specialBrandIds')
      }
    },
    async onEditSubmit() {
      this.addFormVisible = true
      await onAddSubmit();
    },
    async onAddSubmit() {
      this.addLoading = true;
      const formData = this.autoform.fApi.formData();

      var checkData = await checkRepeatNameAddUser({ id: formData.id, brandName: formData.brandName, userId: formData.userId });
      if (null != checkData.data) {
        this.$confirm('所添加人员系统中存在重名，是否继续', '提示', {
          confirmButtonText: '继续',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async() => {
          this.listLoading = true;
          const res = await pageBianMaBrand({ name: formData.brandName, userId: formData.userId });
          this.listLoading = false;
          if (!res?.success) return;
          this.total = res.data.total;
          const data = res.data.list;
          data.forEach(d => {
            d._loading = false;
          })
          this.list = data;
          this.addFormVisible = false;
        }).catch(() => {
          this.addFormVisible = false;
          this.$message.info('已取消新增组员');
        });
        this.addLoading = false;
        return;
      }

      await this.autoform.fApi.validate(async (valid, fail) => {
        if (valid) {
          if (!formData.specialRoleType)
            formData.specialRoleType = 0;
          var res = await updateBianMaBrand(formData)
          if (res?.success) {
            this.addFormVisible = false
            this.$message({ message: '保存成功', type: "success" })
            await this.getlist()
          }
          else
            this.$message({ message: '保存失败', type: "warning" })
        } else {
          //todo 表单验证未通过
          this.addLoading = false;
        }
      })
      this.addLoading = false;
    },
    async remoteSearchBianMaBrand(parm) {
      if (!parm) {
        return;
      }
      let dynamicFilter = { field: 'brandName', operator: 'Contains', value: parm }
      let options = [];
      let res = await getAllBianMaBrandByFilter({ currentPage: 1, pageSize: 50, dynamicFilter: dynamicFilter });
      if (res?.success) {
        res?.data?.forEach(f => {
          options.push({ value: f.id, label: f.brandName })
        })
      }
      this.autoform.fApi.getRule('pBianMaBrandId').options = options;
    },
    async cellclick(row, column, e) {
      if (column.property == 'brandName') {
        this.teamInfosVisible = true
        await this.getTeamList(row.id);
      }
    },
    async getTeamList(id) {
      const res2 = await getBianMaBrandTeamTree({ id: id })
      this.teamlist = res2.data;
    },
    selsChange: function (sels) {
      this.sels = sels
    },
    //字体颜色
    renderenabledStatus(row) {
      if (row.enabled == true) {
        return "color:green;cursor:pointer;";
      } else return "color:red;cursor:pointer;";
    },
    //字体颜色
    renderwithholdStatus(row) {
      if (row.withholdIsTrue == true) {
        return "color:green;cursor:pointer;";
      } else return "color:red;cursor:pointer;";
    },

    async onSetShow() {
      this.setDialog.data = {

      };
      this.setDialog.visible = true;
      this.setDialog.loading = true;
      let res = await GetBianMaBrandLeaveSet();
      this.setDialog.loading = false;
      if (res?.data) {
        if (res.data.autoCloseDays == null) {
          res.data.autoCloseDays = undefined;
        }
        if (res.data.autoCloseDays2 == null) {
          res.data.autoCloseDays2 = undefined;
        }
        this.setDialog.data = res.data;
        this.setDialog.data.roles = res.data.roles ? res.data.roles.split(',').filter(item => item !== '') : [];
      }
    },
    async onSetSave() {
      let roleStr = this.setDialog.data.roles.map(item => item).join(',');
      let res = await SaveBianMaBrandLeaveSet({ ...this.setDialog.data, roles: roleStr, roleIds: null });
      if (res?.success) {
        this.$message({ message: '操作成功', type: "success" });
        this.setDialog.visible = false;
      }
    },
    async onLzClick(row) {
      this.leaveLogDialog.data = [];
      this.leaveLogDialog.visible = true;
      this.leaveLogDialog.loading = true;
      let res = await GetBianMaBrandLeaveLogList({ id: row.id });
      this.leaveLogDialog.loading = false;
      if (res?.success) {
        this.leaveLogDialog.data = res.data;
      }
    },
  }
}
</script>
