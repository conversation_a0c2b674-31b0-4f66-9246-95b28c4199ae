<template>
    <MyContainer>
        <template #header>
        </template>
        <vxetablebase :id="'encodingProcessingmsglog202408041725'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            :loading='listLoading' style="width: 100%; height: 450px; margin: 0" />
        <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="total" @page-change="Pagechange"
            @size-change="Sizechange" />
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import inputYunhan from '@/components/Comm/inputYunhan.vue'
import dayjs from 'dayjs'
import { getGoodsHandledDingDingMsgLogPageList } from '@/api/operatemanage/goodsHandledDingDing'
const tableCols = [
    { istrue: true, prop: 'createdTime', label: '通知时间', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'msgContext', label: '通知内容', sortable: 'custom', width: '300' },
    { istrue: true, prop: 'msgSendUserNames', label: '通知人', sortable: 'custom', width: '300' },
]
export default {
    name: "encodingProcessing",
    components: { MyContainer, vxetablebase, inputYunhan },
    data() {
        return {
            that: this,
            ListInfo: {//列表信息
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
            },
            listLoading: true,
            total: 0,//总条数
            tableData: [],
            tableCols,//表头
        };
    },
    mounted() {
        //this.getList()
    },
    methods: {
        async loadData(args) {
            console.log(args);
            this.ListInfo.goodsHandledDingDingId = args.rowid;
            await this.getList();
        },
        //对接记录弹层每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //对接记录弹层当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        //获取列表
        async getList() {
            this.listLoading = true
            const { data, success } = await getGoodsHandledDingDingMsgLogPageList(this.ListInfo);
            this.listLoading = false
            if (success) {
                this.tableData = data.list
                this.total = data.total
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    },
};
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
}

.publicMargin {
    margin-right: 10px;
}

.imgBox {
    display: flex;
    width: 80%;
    overflow: auto;
    flex-wrap: wrap;

    .imgcss ::v-deep img {
        min-width: 100px !important;
        min-height: 100px !important;
        width: 100px !important;
        height: 100px !important;
    }
}

::v-deep .dialog .el-dialog {
    min-width: 500px;
}
</style>
