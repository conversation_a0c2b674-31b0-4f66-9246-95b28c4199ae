<template>
    <container>
        <ces-table ref="table" :that='that' :tablekey="tablekey" :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='true' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry' @summaryClick='onsummaryClick' :loading="listLoading" />
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
        <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </container>
</template>
<script>
    import { importCashRedTX, pageCashRedTX } from '@/api/financial/detail1'
    import { getAnalysisCommonResponse } from '@/api/admin/common'
    import container from '@/components/my-container/noheader'
    import cesTable from "@/components/Table/table.vue";
    import { formatFeeShareOper, formatYesornoBool, companylist } from "@/utils/tools";
    import dayjs from "dayjs";
    import { formatTime1 } from "@/utils";
    import buschar from '@/components/Bus/buschar'
    var curMonth = formatTime1(dayjs().startOf("month").subtract(1, "month"), "yyyyMM");
    const tableCols = [
        { istrue: true, prop: 'yearMonth', label: '年月', width: '70', sortable: 'custom' },
        { istrue: true, prop: 'orderNo', label: '订单编号', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'shopCode', label: '店铺编码', width: '150', sortable: 'custom', formatter: (row) => row.shopName },
        { istrue: true, prop: 'sendAccount', label: '发生账号', width: '150', sortable: 'custom' },
        { istrue: true, summaryEvent: true, prop: 'amont', label: '金额', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'shareOperV1', label: '分摊方式', width: '90', sortable: 'custom', formatter: (row) => formatFeeShareOper(row.shareOperV1), tipmesg: '工资月报' },
        { istrue: true, prop: 'computeStatusV1', label: '计算状态', width: '90', sortable: 'custom', formatter: (row) => { return row.computeStatusV1 == 0 ? '未计算' : '已计算' }, tipmesg: '工资月报' },
        { istrue: true, prop: 'computeTimeV1', label: '计算时间', width: '145', sortable: 'custom', formatter: (row) => formatTime1(row.computeTimeV1, 'yyyy-MM-dd HH:mm:ss'), tipmesg: '工资月报' },
        { istrue: true, prop: 'shareOperV2', label: '分摊方式 ', width: '95', sortable: 'custom', formatter: (row) => formatFeeShareOper(row.shareOperV2), tipmesg: '参考月报' },
        { istrue: true, prop: 'computeStatusV2', label: '计算状态 ', width: '95', sortable: 'custom', formatter: (row) => { return row.computeStatusV2 == 0 ? '未计算' : '已计算' }, tipmesg: '参考月报' },
        { istrue: true, prop: 'computeTimeV2', label: '计算时间 ', width: '145', sortable: 'custom', formatter: (row) => formatTime1(row.computeTimeV2, 'yyyy-MM-dd HH:mm:ss'), tipmesg: '参考月报' },
        { istrue: true, prop: 'createdTime', label: '导入时间', width: '145', sortable: 'custom', formatter: (row) => formatTime1(row.createdTime, 'yyyy-MM-dd HH:mm:ss') }
    ];
    const tableHandles = [
        { label: "导入", handle: (that) => that.onimport() },
        { label: "下载导入模板", handle: (that) => that.ondownloadmb('淘系红包导入模板') },
        { label: "计算分摊", handle: (that) => that.oncomput() },
        { label: "按月删除", handle: (that) => that.onbatchDelete() },
        { label: "刷新", handle: (that) => that.getlist() },
    ];
    export default {
        name: 'Roles',
        components: { cesTable, container, buschar },
        props: {
            filter: {},
            tablekey: { type: String, default:'' }
        },
        data () {
            return {
                fileparm: {},
                uploadLoading: false,
                fileList: [],
                importDialog: {
                    filter: {
                        yearmonth: curMonth
                    }
                },
                analysisFilter: {
                    searchName: "Detail1_CashRedTX",
                    extype: 6,
                    selectColumn: "Amont",
                    filterTime: "YearMonth",
                    isYearMonthDay: false,
                    filter: null,
                    columnList: [{ columnNameCN: '金额', columnNameEN: 'Amont' }]
                },
                ImportdialogVisible: false,
                shareFeeType: 41,
                companylist: companylist,
                that: this,
                list: [],
                tableCols: tableCols,
                tableHandles: tableHandles,
                pager: { OrderBy: "id", IsAsc: false },
                summaryarry: {},
                total: 0,
                sels: [],
                selids: [],
                listLoading: false,
                pageLoading: false,
                pickOptions: {
                    disabledDate (time) {
                        return time.getTime() > Date.now()
                    }
                },
                buscharDialog: { visible: false, title: "", data: [] },
            }
        },
        mounted () {
            //this.onSearch()
        },
        beforeUpdate () { },
        methods: {

            submitUpload () {
                if (!this.importDialog.filter.yearmonth) {
                    this.$message({ message: "请选择结算月份", type: "warning" });
                    return false;
                }
                this.$refs.upload.submit();
            },
            async uploadFile (item) {
                if (!item || !item.file || !item.file.size) {
                    this.$message({ message: "请先上传文件", type: "warning" });
                    return false;
                }
                this.uploadLoading = true
                const form = new FormData();
                form.append("upfile", item.file);
                form.append("yearmonth", this.importDialog.filter.yearmonth);
                var res;
                res = await importCashRedTX(form);
                if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
                else this.$message({ message: res.msg, type: "warning" });
                this.uploadLoading = false
            },
            onSearch () {
                this.$refs.pager.setPage(1)
                this.getlist()
            },
            async getlist () {
                var pager = this.$refs.pager.getPager()
                this.filter.shareFeeType = this.shareFeeType;
                const params = { ...pager, ...this.pager, ... this.filter }
                this.listLoading = true
                const res = await pageCashRedTX(params)
                this.listLoading = false
                if (!res?.success) return
                this.total = res.data.total
                const data = res.data.list
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
                this.summaryarry = res.data.summary;
            },
            sortchange (column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch();
            },
            async onbatchDelete () {
                await this.$emit('ondeleteByBatch', this.shareFeeType);
            },
            async oncomput () {
                this.$emit('onstartcomput', this.shareFeeType);
            },
            async onimport () {
                await this.$emit('onstartImport', this.shareFeeType);
            },
            async ondownloadmb (name) {
                await this.$emit('ondownloadmb', name);
            },
            selsChange: function (sels) {
                this.sels = sels
            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
            async onsummaryClick (property) {
                //this.analysisFilter.filter.YearMonth = [this.filter.yearmonth, 0]
                this.analysisFilter.filter = { YearMonth: [this.filter.yearmonth, 0] };

                let that = this;
                const res = await getAnalysisCommonResponse(this.analysisFilter).then(res => {
                    that.buscharDialog.visible = true;
                    that.buscharDialog.data = res.data
                    that.buscharDialog.title = res.data.legend[0]
                });
            }
        }
    }
</script>
