<template>
    <container style="height: 100%;">
        <el-tabs v-model="activeName" style="height: 95%;">
        <el-tab-pane label="查询" name="查询" style="height: 100%;overflow: scroll;">
            <el-button type="primary" @click="getStatus" style="margin-top: 30px;margin-left:30px;">查询</el-button>
            <div style="overflow: hidden;">
            <div v-for="owned in ownList" style="width: 30%;float: left;margin-left: 30px;margin-top: 30px;">
                <el-button type="primary" @click="getTableStatus(owned)">查询</el-button>
                <el-table :data="getData(owned)" :cell-style="resultStyle" style="width: 100%">
                    <el-table-column :label="owned" style="width: 100%;">
                        <el-table-column prop="title" label="标题">
                        </el-table-column>
                        <el-table-column prop="userName" label="责任人" width="80">
                        </el-table-column>
                        <el-table-column prop="result" label="状态" width="80">
                        </el-table-column>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        </el-tab-pane>
        <el-tab-pane label="配置" name="配置" style="height: 100%">
            <PatrolConfig></PatrolConfig>
        </el-tab-pane>
        </el-tabs>
    </container>
</template>

<script>
import container from '@/components/my-container';
import { QueryData, QueryStatus } from '@/api/bookkeeper/DataPatrol';
import PatrolConfig from './PatrolConfig.vue';

export default {
    name: 'DataPatrol',
    components: { container,PatrolConfig },
    data() {
        return {
            list: null,
            ownList: null,
            activeName:"查询",
        };
    },
    async mounted() {
        const { success, data } = await QueryData();
        if (success) {
            this.list = data?.list;
            this.ownList = new Set();
            this.list.forEach(item => {
                this.ownList.add(item.owned);
            });
        }
    },
    methods: {
        getData(owned) {
            var data = [];
            this.list.forEach(f => {
                if (f.owned == owned) {
                    var obj = {};
                    obj.title = f.title;
                    obj.userName = f.userName;
                    obj.result = f.result;
                    data.push(obj);
                }
            })
            return data;
        },
        async getStatus() {

            for (const owned of this.ownList) {
                const { success, data } = await QueryStatus({ owned: owned });
                if (success) {
                    var list = data.list;
                    this.list.forEach(f => {
                        list.forEach(tmp => {
                            if (f.owned == owned && f.title == tmp.title) {
                                f.result = tmp.result;
                            }
                        });
                    });
                }
            }
        },
        async getTableStatus(owned) {
            const { success, data } = await QueryStatus({ owned: owned });
            if (success) {
                var list = data.list;
                this.list.forEach(f => {
                    list.forEach(tmp => {
                        if (f.owned == owned && f.title == tmp.title) {
                            f.result = tmp.result;
                        }
                    });
                });
            }

        },
        resultStyle({row, column, rowIndex, columnIndex}) {
            if (columnIndex == 2) {
                // 假设我们想根据地址的长度来改变颜色
                if (row.result == "未处理") {
                    return { "color":"red"};
                } else if (row.result == "已处理") {
                    return {"color":"green" };
                } else if (row.result == "待处理") {
                    return { "color":"gray" };
                }
            }
        },
    },
};
</script>

<style lang="scss" scoped></style>