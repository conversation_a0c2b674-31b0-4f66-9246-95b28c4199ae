<template>
    <container>
        <template #header>
            <div class="top">
                    <inputYunhan :key="'1'" :keys="'one'" :width="'230px'" ref="styleCodes" v-model.trim="filter.orderNos" title="系列编码"
                        :maxRows="100" :inputt.sync="filter.styleCodes" placeholder="系列编码/回车搜索多个" :clearable="true"
                        @callback="callbackStyleCodes" style="width: 230px;margin:0 0 0 0;"></inputYunhan>

                    <inputYunhan :key="'1'" :keys="'one'" :width="'230px'" ref="orderNos" v-model.trim="filter.orderNos" title="线上单号"
                        :maxRows="200" :inputt.sync="filter.orderNos" placeholder="线上单号/回车搜索多个" :clearable="true"
                        @callback="callbackOrderNos" style="width: 230px;margin:0 0 0 0;"></inputYunhan>

                    <el-date-picker style="width: 220px" v-model="timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="扣款日期" end-placeholder="扣款日期"
                        :clearable="true" @change="changeTime1"></el-date-picker>

                    <el-date-picker style="width: 220px" v-model="timerange2" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="付款日期" end-placeholder="付款日期"
                        :clearable="true" @change="changeTime2">
                    </el-date-picker>

                    <el-date-picker style="width: 220px" v-model="timerange3" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="发货日期" end-placeholder="发货日期"
                        :clearable="true" @change="changeTime3">
                    </el-date-picker>

                    <el-select clearable v-model="filter.illegalType" collapse-tags placeholder="平台原因"
                        style="width: 130px">
                        <el-option v-for="item in causeList" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>

                    <el-select v-model="filter.wmsCoIds"  multiple collapse-tags clearable placeholder="请选择发货仓" style="width: 280px"
                        key="filter.wmsCoIds">
                        <el-option v-for="item in newWareHouseList" :key="item.name" :label="item.name"
                            :value="item.wms_co_id" />
                    </el-select>

                    <inputYunhan :key="'1'" :keys="'one'" :width="'170px'" ref="expressNos"
                        v-model.trim="filter.expressNos" :maxRows="200" :inputt.sync="filter.expressNos"  title="快递单号"
                        placeholder="快递单号/回车搜索多个" :clearable="true" @callback="callbackExpressNos" style="width: 170px;margin:0 0 0 0;"></inputYunhan>

                    <inputYunhan :key="'1'" :keys="'one'" :width="'170px'" ref="expressCompanyNames"
                        v-model.trim="filter.expressCompanyNames" :maxRows="200"
                        :inputt.sync="filter.expressCompanyNames" placeholder="快递公司/回车搜索多个" :clearable="true"  title="快递公司"
                        @callback="callbackExpressCompanyNames" style="width: 170px;margin:0 0 0 0;"></inputYunhan>

                    <el-select filterable v-model="filter.shopCodes" multiple collapse-tags placeholder="店铺" clearable
                        style="width: 230px; ">
                        <el-option v-for="item in shopList" :key="item.shopCodes" :label="item.shopName"
                            :value="item.id" />
                    </el-select>

                    <el-select clearable v-model="filter.chatMsgType" collapse-tags placeholder="聊天类型"
                        style="width: 130px">
                        <el-option label="静默" value="静默"></el-option>
                        <el-option label="正常" value="正常"></el-option>
                    </el-select>

                    <el-input-number v-model="filter.minPayAmount" :min="0" :max="9999999.99" style="width:120px;"  placeholder="售价"
                        :precision="2"></el-input-number>
                    至
                    <el-input-number v-model="filter.maxPayAmount" :min="0" :max="9999999.99" style="width:120px;"  placeholder="售价"
                        :precision="2"></el-input-number>
                    <el-button type="primary" @click="getList('Search')">搜索</el-button>
                    <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
   </div>

        </template>
        <vxetablebase ref="table" :id="'SeriesCoding202040831'" :that='that' :isIndex='true' @sortchange='sortchange'
            :isSelection='true' :hasexpand='true' :tableData='list' :tableCols='tableCols'  :loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
            v-dialogDrag>
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo"
                style="z-index:10000;height:600px" />
        </el-dialog>

        <el-dialog title="聊天记录" v-if="dialogChatRecordHisVisible" :visible.sync="dialogChatRecordHisVisible" width="40%"
            height="600px" v-dialogDrag>
            <chartComponent ref="chartRef" :isShow="true" :api="GetPinIllegalDetailChatRecordDetail"></chartComponent>
        </el-dialog>

    </container>
</template>

<script>
import container from '@/components/my-container';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { GetPinIllegalDetailChatRecord, GetPinIllegalDetailChatRecordDetail,ExportPinIllegalDetailChatRecord } from '@/api/order/orderdeductmoney.js';
import { formatTime, formatLinkProCode } from "@/utils/tools";
import { getAllWarehouse } from '@/api/inventory/warehouse'
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import inputYunhan from "@/components/Comm/inputYunhan";
import dayjs from 'dayjs'
import chartComponent from "@/views/order/SeriesIllegalChatComponent.vue";
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";

const tableCols = [
    { istrue: true, prop: 'proCode', label: '宝贝ID', width: '120', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
    { istrue: true, prop: 'styleCode', label: '系列编码', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'orderNo', label: '线上订单号', width: '190', sortable: 'custom', formatter: (row) => !row.orderNo ? " " : row.orderNo, type: 'click', handle: (that, row) => that.showLogDetail(row) },
    { istrue: true, prop: 'cost', label: '商品成本', width: '90', sortable: 'custom', formatter: (row) => !row.cost ? "" : parseFloat(row.cost.toFixed(6)) },
    { istrue: true, prop: 'payAmount', label: '已付金额', width: '90', sortable: 'custom', formatter: (row) => !row.payAmount ? "" : parseFloat(row.payAmount.toFixed(6)) },
    { istrue: true, prop: 'amountPaid', label: '扣款金额', width: '90', sortable: 'custom', formatter: (row) => parseFloat(row.amountPaid.toFixed(6)) },
    { istrue: true, prop: 'occurrenceTime', label: '扣款日期', width: '100', sortable: 'custom', formatter: (row) => formatTime(row.occurrenceTime, "YYYY-MM-DD") },
    { istrue: true, prop: 'payTime', label: '付款日期', width: '100', sortable: 'custom', formatter: (row) => !row.payTime ? " " : formatTime(row.payTime, "YYYY-MM-DD HH:mm") },
    { istrue: true, prop: 'sendTime', label: '发货日期', width: '100', sortable: 'custom', formatter: (row) => !row.sendTime ? "" : formatTime(row.sendTime, "YYYY-MM-DD HH:mm") },
    { istrue: true, prop: 'illegalType', label: '平台原因', width: '90', sortable: 'custom', formatter: (row) => !row.illegalTypeName ? " " : row.illegalTypeName },
    { istrue: true, prop: 'sendWarehouse', label: '发货仓', width: '80', sortable: 'custom',formatter: (row) => !row.sendWarehouseName ? " " : row.sendWarehouseName },
    { istrue: true, prop: 'expressNo', label: '快递单号', width: '130', sortable: 'custom', type: 'click', handle: (that, row) => that.onShowLogistics(row) },
    { istrue: true, prop: 'expressCompany', label: '快递公司', width: '130', sortable: 'custom', formatter: (row) => row.expressCompanyName },
    { istrue: true, prop: 'shopId', label: '店铺', width: '120', sortable: 'custom', formatter: (row) => !row.shopName ? " " : row.shopName },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: '200', sortable: 'custom', formatter: (row) => !row.goodsName ? "" : row.goodsName },
    { istrue: true, prop: 'chatMsgType', label: '聊天类型', width: '90', sortable: 'custom', formatter: (row) => !row.chatMsgType ? "静默" : row.chatMsgType },
    {
        istrue: true, label: '操作',  fixed: 'right', type: 'button', btnList: [
            {
                label: '查看',
                handle: (that, row) => that.showChatRecord(row),
                ishide: (that, row) => { return !row.chatMsgType; }
            }
        ]
    }
]

export default {
    name: 'SeriesIllegalChatRecord',
    components: { container, vxetablebase, inputYunhan, chartComponent ,OrderActionsByInnerNos},
    data() {
        return {
            GetPinIllegalDetailChatRecordDetail,
            that: this,
            timerange: null,
            filter: {
                orderNos: null,
                styleCodes:null,
                startDate: null,
                endDate: null,
                startPayDate: null,
                endPayDate: null,
                startSendDate: null,
                endSendDate: null,
                illegalType: null,
                shopCodes: null,
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
            tableCols,
            list: [],
            loading: false,
            total: null,
            summary: null,
            causeList: [
                { value: 4, label: "商家责任退货" },
                { value: 5, label: "售后补偿" }
            ],
            timerange1: null,
            timerange2: null,
            timerange3: null,
            dialogHisVisible: false,

            //聊天记录
            dialogChatRecordHisVisible: false,
            isExport: false,
        };
    },

    async mounted() {
        let res1 = await getshopList({ platform: 2, CurrentPage: 1, PageSize: 100000 });
        this.filter.shopCodes = null
        this.shopList = res1.data.list

        //仓库
        var res = await getAllWarehouse();
        this.newWareHouseList = res.data.filter((x) => { return x.name.indexOf('代发') < 0; });

        this.filter.startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD');
        this.filter.endDate = dayjs().format('YYYY-MM-DD');
        this.timerange = [this.filter.startDate, this.filter.endDate];
        await this.getList();
    },
    methods: {
         //导出数据,使用时将下面的方法替换成自己的接口
         async exportProps() {
            this.isExport = true
            await ExportPinIllegalDetailChatRecord(this.filter).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '平台原因聊天记录' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async callbackStyleCodes(val){
            this.filter.styleCodes = val;
        },
        async callbackOrderNos(val) {
            this.filter.orderNos = val;
        },
        async callbackExpressNos(val) {
            this.filter.expressNos = val;
        },
        async callbackExpressCompanyNames(val) {
            this.filter.expressCompanyNames = val;
        },
        //获取列表
        async getList(val) {
            if (val == "Search") {
                this.filter.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            const { success, data } = await GetPinIllegalDetailChatRecord(this.filter);
            if (success) {
                this.list = data?.list;
                this.total = data?.total;
                this.summary = data?.summary;
                if (this.summary)
                    this.summary.echarts_sum = "趋势图";
            }
            this.loading = false;
        },
        //获取日期
        async changeTime1(e) {
            this.filter.startDate = e ? e[0] : null
            this.filter.endDate = e ? e[1] : null
        },
        async changeTime2(e) {
            this.filter.startPayDate = e ? e[0] : null
            this.filter.endPayDate = e ? e[1] : null
        },
        async changeTime3(e) {
            this.filter.startSendDate = e ? e[0] : null
            this.filter.endSendDate = e ? e[1] : null
        },
        //每页数量改变
        Sizechange(val) {
            this.filter.currentPage = 1;
            this.filter.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.filter.currentPage = val;
            this.getList()
        },
        //排序查询
        sortchange({ order, prop }) {
            if (prop) {
                this.filter.orderBy = prop
                this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        onShowLogistics(row) {
            let self = this;
            this.$showDialogform({
                path: `@/views/order/logisticsWarning/DbLogisticsRecords.vue`,
                title: '物流明细',
                args: { expressNos: row.expressNo },
                height: 300,
            });
        },
        showLogDetail(row) {
            this.dialogHisVisible = true;
            this.orderNo = row.orderNo;
        },
        async showChatRecord(row) {
            this.dialogChatRecordHisVisible = true;
            this.$nextTick(() => {
                this.$refs.chartRef.dataJson = row.orderNo;
            });
        }
    },
};
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>