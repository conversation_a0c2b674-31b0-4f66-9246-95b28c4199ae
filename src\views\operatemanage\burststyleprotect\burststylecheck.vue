<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-date-picker style="width: 260px" v-model="filter.daterange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="保护时间"
                        end-placeholder="保护时间" :clearable="true" :picker-options="pickerOptions"></el-date-picker>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-select filterable clearable v-model="filter.trendType" placeholder="类型" style="width: 120px">
                        <el-option label="爆款" value=99></el-option>
                        <el-option label="趋势款" value=0></el-option>
                        <el-option label="新品编码" value=1></el-option>
                        <el-option label="特殊产品" value=2></el-option>
                        <el-option label="重点产品" value=3></el-option>
                        <el-option label="拼多多不可上架编码" value=100></el-option>
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-input v-model.trim="filter.styleCode" placeholder="系列编码" style="width:120px;" clearable
                        maxlength="40" />
                </el-button>


                <el-button-group style="margin-right:5px;">
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport" :loading="exportLoading3">导出</el-button>
                </el-button-group>
            </el-button-group>
        </template>

        <el-row style="height: 95%;">
            <el-col :span="9" style="height: 100%;">
                <vxetablebase :id="'burststylecheck20231019'" :border="true" :align="'center'"
                    :tablekey="'burststylecheck20231019'" ref="table2" :that='that' :isIndex='true' :hasexpand='false'
                    @sortchange='sortchange' :isSelectColumn="false" :showsummary='false' :tablefixed='true'
                    :isNeedExpend="false" :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols'
                    :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:100%;margin: 0"
                    :xgt="9999" @select="callback">
                </vxetablebase>

                <!--分页-->
                <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
            </el-col>

            <el-col :span="15" style="height: 100%;">
                <vxetablebase :id="'burststylecheck202310193'" :border="true" :align="'center'"
                    :tablekey="'burststylecheck202310193'" ref="table3" :that='that' :isIndex='true' :hasexpand='false'
                    :isNeedExpend="false" :isSelectColumn="false" :showsummary='false' :tablefixed='true'
                    :tableData='datalist3' :tableCols='tableCols3' :loading="listLoading3"
                    style="width:100%;height:100%;margin: 0" :xgt="9999">
                </vxetablebase>


                <!--分页-->
                <my-pagination ref="pager3" :total="total3" @get-page="getList3" />
            </el-col>
        </el-row>
    </my-container>
</template>
<script>
import dayjs from "dayjs";
import { platformlist } from '@/utils/tools'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatPlatform, formatTime, pickerOptions, formatLinkProCode } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import inputYunhan from "@/components/Comm/inputYunhan";

import { getDirectorGroupList, getList as getshopList, getDirectorList } from '@/api/operatemanage/base/shop'
import { GetBurstStyleCheckPageList, GetBurstStyleCheckProCodePageList, ExportBurstStyleCheckProCodeList } from '@/api/operatemanage/burststyleprotect'
import buschar from '@/components/Bus/buschar'

const tableCols = [
    { istrue: true, prop: 'styleCode', label: '不可做编码', sortable: 'custom' },
    { istrue: true, prop: 'trendTypeStr', label: '保护类型', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'burstStyleTime', label: '保护时间', sortable: 'custom', width: '150' },
    {
        istrue: true, label: '查看', style: "color:red;cursor:pointer;", width: '80', align: 'center',
        formatter: (row) => '查看', type: 'click', handle: (that, row, column) => that.onsee(row, column)
    },
];
const tableCols3 = [
    { istrue: true, prop: 'styleCode', label: '系列编码' },
    { istrue: true, prop: 'proCode', label: '产品ID', width: '140', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
    { istrue: true, prop: 'platform', label: '平台', width: '100', formatter: (row) => formatPlatform(row.platform) },
    { istrue: true, prop: 'onTime', label: '上架时间', width: '150' },
    { istrue: true, prop: 'groupId', label: '运营组', width: '100', formatter: (row) => row.groupName || ' ' },
    { istrue: true, prop: 'operateSpecialUserId', label: '运营专员', width: '100', formatter: (row) => row.operateSpecialUserName || ' ' },
    { istrue: true, prop: 'userId', label: '运营助理', width: '100', formatter: (row) => row.userRealName || ' ' },
];
const tableHandles = [
    //{ label: "编辑", handle: (that, row) => that.onEdit(row) },
];
const startDate = formatTime(dayjs(), "YYYY-MM-DD");
const endDate = formatTime(dayjs(), "YYYY-MM-DD");

export default {
    name: "burststylecheck",
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, vxetablebase, inputYunhan, buschar
    },
    data() {
        return {
            that: this,
            filter: {
                daterange: [startDate, endDate],
                goodGroupIds: [],
                canGroupIds: [],
            },
            directorlist: [],
            drawerVisible: false,
            quantityprocessed: { visible: false, title: "", data: {} },
            pickerOptions: pickerOptions,
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            datalist: [],
            pager: { OrderBy: "burstStyleTime", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            selrows: [],

            platformlist: platformlist,
            shopList: [],
            directorGroupList: [],


            tableCols3: tableCols3,
            total3: 0,
            datalist3: [],
            listLoading3: false,
            exportLoading3: false,
            styleCode3: null,
            burstStyleTime3: null,
        };
    },
    async mounted() {
        await this.getloadgroupselect();
        await this.onSearch();
    },
    async created() {
    },
    methods: {
        async getloadgroupselect() {
            const res2 = await getDirectorGroupList({});
            this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);

            let res3 = await getDirectorList();
            this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getCondition() {
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.daterange) {
                this.filter.startDate = this.filter.daterange[0];
                this.filter.endDate = this.filter.daterange[1];
            }
            else {
                this.filter.startDate = '2020-01-01';
                this.filter.endDate = '2030-01-01';
            }
            let pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };

            return params;
        },
        async getList() {
            const params = this.getCondition();
            if (params === false) {
                return;
            }

            this.total3 = 0;
            this.datalist3 = [];

            console.log(params);
            this.listLoading = true;
            const res = await GetBurstStyleCheckPageList(params);
            this.listLoading = false;
            console.log(res);
            this.total = res.data?.total;
            this.datalist = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        callback(val) {
            this.selrows = [...val];
        },
        async onExport() {
            const params = this.getCondition();
            this.exportLoading3 = true;
            const res = await ExportBurstStyleCheckProCodeList(params)
            this.exportLoading3 = false;
            if (!res?.data) {
                this.$message({ type: 'error', message: '没有数据可导出或导出失败!' });
                return;
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', "爆款排查_" + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        async callbackProCode(val) {
            this.filter.proCode = val;
        },
        async onsee(row, col) {
            this.styleCode3 = row.styleCode;
            this.burstStyleTime3 = row.burstStyleTime;
            this.$refs.pager3.setPage(1);
            await this.getList3();
        },

        async getList3() {
            let pager = this.$refs.pager3.getPager();
            const params = { ...pager, };
            params.styleCode = this.styleCode3;
            params.startDate = this.burstStyleTime3;
            this.listLoading3 = true;
            const res = await GetBurstStyleCheckProCodePageList(params);
            this.listLoading3 = false;
            console.log(res);
            this.total3 = res.data?.total;
            this.datalist3 = res.data?.list;
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

::v-deep .el-select__tags-text {
    max-width: 70px;
}
</style>
