<template>

 <el-row :key="upkey" :id="upkey">

   <!-- <el-row> -->
     <div class="linecontent" style="width: 100%;" :key="upkey">

         <el-upload ref="upload"

         :key="upkey"

         class="inline-block"

         action ="#" 

         :auto-upload="true" 

         :multiple="true" 

         :limit="limit" 

         :show-file-list ="false"

         :accept ="accepttyes"

         :http-request="UpSuccessload"

         :on-exceed ="exceed"

         :file-list="retdata"

         :disabled="islook"

         >

         <div class="linecontent">
         <div v-for="(i,j) in retdata" :key="j"  slot="file">

             <div class="linerow img-hover" :style="minisize?{height: '60px',width: '60px'}:{height: '35px',width: '35px'}">

                 <img v-show="i.url.indexOf('.png')>-1||i.url.indexOf('.jpg')>-1||i.url.indexOf('.jpeg')>-1||i.url.indexOf('.gif')>-1?true:false" :src="i.url" @click.stop="showImg(i,j)" 

                      style="height: 100%; width: 100%;" mode="aspectFit"  @dragover="onDragOver" @dragend="onDragEnd(j)"/>
                
                 <img src="@/static/images/vedio.jpg"  v-show="i.url.indexOf('.mp4')>-1||i.url.indexOf('.MOV')>-1?true:false"

                 @click.stop="OpenExeclOnline(i)" style="height: 100%; width: 100%;" mode="aspectFit" />

                <img src="@/static/images/excel.jpg" v-show="i.url.indexOf('.excel')>-1?true:false"

                @click.stop="OpenExeclOnline(i)" style="height: 100%; width: 100%;" mode="aspectFit" />



                <i v-if="retdata.length!=null" class="el-icon-error close-img" @click.stop="removefile(i)"></i>



                 <div v-if="retdata.length!=null" class="close-img-dask"></div>

             </div>

           

         </div>

         <div class="flexcenter card" style="margin-top: 5px;" slot="default">
          <i class="el-icon-plus"></i>
         </div>

         
         </div>

     </el-upload> 

     <el-button v-if="isdown" @click="downExecl" style=" margin-left: 20px;font-size:14px" type="text">下载</el-button> 

     </div>


     <el-image-viewer v-if="showGoodsImage" :url-list="imgList"  :initialIndex="imgindex" :on-close="closeFunc" style="z-index:9999;" />

     <el-dialog title="视频播放" :visible.sync="dialogVisible" width="50%" @close="closeVideoPlyer"  :append-to-body="true" >

         <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />

         <span slot="footer" class="dialog-footer">

             <el-button @click="closeVideoPlyer">关闭</el-button>

         </span>

     </el-dialog>

 <!-- </el-row>  -->

</el-row>

</template>

<script>

function pause(msec) {

 return new Promise(

     (resolve, reject) => {

         setTimeout(resolve, msec || 500);

     }

 );

}

import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'

import ElImageViewer from './imageviewer.vue';

import MyContainer from "@/components/my-container";

import videoplayer from '@/views/media/video/videoplaynotdown' //播放器

export default {

 components: { MyContainer,ElImageViewer,videoplayer}, 

 props:{
     upkey: { type: String, default:"" },

     uploadInfo:{ type: Array, default: ()=>{
        return [];
     }}, 

     accepttyes:{ type: String, default:"*" },

     limit:{ type: Number, default: 100000  },

     delfunction:{ type: Function, default: null},

     islook:{ type: Boolean, default:false },

     isdown:{ type: Boolean, default:false },

     minisize: { type: Boolean, default:true },

 },

 data() {

     return {

         retdata: [],

         accepttypeinfo :"*" ,

         limitnum :100,

         uploading:false,

         imgindex:0,

         imglist: [],

         showGoodsImage: false,

         dialogVisible:false,

         videoplayerReload:false

     };

 },

 async mounted() {
    console.log("父传",this.uploadInfo)
    //  this.retdata = this.uploadInfo;
 },

 watch: {
    uploadInfo: {
      handler(val) {
        if (val) {
          this.retdata = val;
        //   this.getDeliveryWarn();
        }
      },
      deep: true
    }
 },

 

 methods: {

     playVideo(videoUrl) {

         this.videoplayerReload = false;

         this.videoplayerReload = true;

         this.dialogVisible = true;

         this.videoUrl = videoUrl;

     },

     async closeVideoPlyer() {

         this.dialogVisible = false;

         this.videoplayerReload = false;

     },

     setData(array){

         this.retdata = array;

     },

     isformat(val){

         const img = ['jpg','png','tif','gif','svg','webp','apng'];

         for(var item in img){

             if(val.includes(item,0)){

                 console.log(true)

                 return true

             }else{

                 console.log(false)

                 return false

             }

         }

         console.log("是否图片",istrueimg)

     },

     onDragOver(event) {

         event.preventDefault()

         const { src } = event.target

         this.targetSrc = src

     },

     onDragEnd(idx) {

         for(let num in this.retdata){

             if(this.retdata[num].url==this.targetSrc){

                 [this.retdata[idx], this.retdata[num]] = [this.retdata[num], this.retdata[idx]]

                 this.retdata = [...this.retdata]

             }

         }

     },

     //获取返回值

     getReturns(){

         if(this.uploading){

             this.$message({ message:"正在上传，请稍等", type: "warning" });

             return {success:false};

         }

         var curdata= [];

         this.retdata.forEach(function(item){

             curdata.push({domain:item.domain,fileName:item.fileName,relativePath:item.relativePath,url:item.url,  upLoadPhotoId:item.upLoadPhotoId})

         });

         return {success:true ,data :curdata};

     },

     //下载execl文件

     async downExecl(){

         for(let num in this.retdata)

         {  

             await pause(500);

             await this.downfile(this.retdata[num]); 

         }

     },

     //下载文件

     async downfile(file){

         var xhr = new XMLHttpRequest();

         xhr.open('GET', file.url, true);

         xhr.responseType = 'arraybuffer'; // 返回类型blob

         xhr.onload = function() {

         if (xhr.readyState === 4 && xhr.status === 200) {

             let blob = this.response;

             console.log(blob);

             // 转换一个blob链接

             // 注: URL.createObjectURL() 静态方法会创建一个 DOMString(DOMString 是一个UTF-16字符串)，

             // 其中包含一个表示参数中给出的对象的URL。这个URL的生命周期和创建它的窗口中的document绑定

             let downLoadUrl = window.URL.createObjectURL(new Blob([blob], {type: 'video/mp4'}));

             // 视频的type是video/mp4，图片是image/jpeg

             // 01.创建a标签

             let a = document.createElement('a');

             // 02.给a标签的属性download设定名称

             a.download = file.fileName;

             // 03.设置下载的文件名

             a.href = downLoadUrl;

             // 04.对a标签做一个隐藏处理

             a.style.display = 'none';

             // 05.向文档中添加a标签

             document.body.appendChild(a);

             // 06.启动点击事件

             a.click();

             // 07.下载完毕删除此标签

             a.remove();

         };

         };

         xhr.send();



     },

     //移除文件

     async removefile(file){

         if(this.islook){

             return;

         } 

         //发出父级页面移除请求

         var that = this;

         if (file?.upLoadPhotoId > 0)

         {

             that.$confirm('此操作将会彻底删除该文件，是否执行')

             .then( async()=> {

                 await that.delfunction(file);

                 for(let num in that.retdata){

                     if(that.retdata[num].uid==file.uid){

                         that.retdata.splice(num,1)

                     }

                 } 

             })

             .catch(_ => {

             });

         } else{

             for(let num in this.retdata){

                 if(this.retdata[num].uid==file.uid){

                     this.retdata.splice(num,1)

                 }

             } 

         } 

     },

     //上传超出提出

     exceed(){

         this.$message({ message: "超出上传数量限制", type: "warning" });

     },

     //上传方法

     async UpSuccessload(item){

         this.uploading = true;

         await this.AjaxFile(item.file, 0,"");

         this.uploading = false;



     },

     //切片上传

     async AjaxFile(file,i,batchnumber) {

         var name = file.name; //文件名

         var size = file.size; //总大小 

         var shardSize = 1 * 1024 *1024;//2m

         var shardCount = Math.ceil(size / shardSize); //总片数

         if (i >= shardCount) {

             return;

         }

         //计算每一片的起始与结束位置

         var start = i * shardSize;

         var end = Math.min(size, start + shardSize);

         //构造一个表单，FormData是HTML5新增的

         i=i+1;

         var form = new FormData();

         form.append("data", file.slice(start, end)); //slice方法用于切出文件的一部分

         form.append("batchnumber", batchnumber);

         form.append("fileName", name);

         form.append("total", shardCount); //总片数

         form.append("index", i); //当前是第几片

         const res = await xMTVideoUploadBlockAsync(form, {headers: {'Content-Type': 'multipart/form-data'}});

         if (res?.success) {

             if(i == shardCount){

                 res.data.fileName = name;

                 res.data.uid = file.uid;

                 res.data.upLoadPhotoId = 0;

                 this.retdata.push(res.data);

                 // this.imglist.push(res.data.url)

             }else{

                 await this.AjaxFile(file, i,res.data);

             }

         }else{

             this.$message({ message: res?.msg, type: "warning" });

         }

     },

     //完成表单界面显示图片

     OpenExeclOnline(e) {     

         if (e.url.split('.')[e.url.split('.').length-1]=="mp4"   ){

             this.playVideo(e.url);

         }else{



             let url =  process.env.VUE_APP_DOMAIN_NAME +"/"+ e.relativePath 

             let officeUrl = 'https://view.xdocin.com/view?src='+url

             // 在新窗口打开编码后 的链接

             window.open(officeUrl,'_blank')  



         }

         

     },

     //完成表单界面显示图片

     async showImg(i,j) {  

         this.imgList = [];

         for(let num in this.retdata)

         {  

             this.imgList.push(this.retdata[num].url); 

         }  

         this.imgindex=j;

         this.showGoodsImage = true;

     },

     //完成表单界面关闭图片

     async closeFunc() {

         this.showGoodsImage = false;

     }, 

 },

};

</script>

<style  lang="scss" scoped>

.infinite-list-item {

 list-style:none;

 margin: 0;

 padding: 0;

}

.card:hover{
 color: #409EFF;
 border: 1px dashed #409EFF;
}

.infinite-list-item  span{

 width: 50px !important;

 white-space: nowrap!important;

 overflow: hidden!important;

 text-overflow: ellipsis!important;

}

.card{
 height: 60px; 
 width: 60px; 
 border: 1px dashed #eee; 
 border-radius: 10px;
}



.linecontent{

//   background: rgb(238, 236, 236);

width: 100%;

height: 100%;

display: flex;

flex-direction: row;

overflow: hidden;

overflow-x: auto;

white-space: nowrap;

// margin-top: 10px;

}

.linerow{

display: inline-block;

//   background: #fff;

margin: 5px;

//   border: 1px solid rgba(219, 217, 217, 0.781);

//   width: 100px;

//   height: 100px;

}

.img-hover {

 position: relative;

 border: 1px solid rgb(237, 238, 240);

}



.img-hover:hover {

   cursor: pointer;

 .close-img,

 .close-img-dask {

   display: block;

 }

}



.close-img {

 display: none;

 position: absolute;

 right: -6px;

 top: -6px;

 color: rgb(255, 0, 0);

 font-size: 18px;

 z-index: 99999;

}

.inline-block {

display: inline-block;

}

.flexcenter{
 display: flex;
 justify-content: center;
 align-items: center;
}



//   .close-img-dask {

//     display: none;

//     position: absolute;

//     top: 0;

//     left: 0;

//     width: 100%;

//     height: 40%;

//     background-color: rgba(0, 0, 0, .5) !important;

//     transition: opacity .3s;

// }

</style>