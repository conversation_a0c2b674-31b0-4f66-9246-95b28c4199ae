<template>
  <el-container style="height:100%;">
    <my-container v-loading="pageLoading" style="width:70%;">
      <template #header>
        <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
          <el-form-item label="付款时间:">
            <el-date-picker
              style="width:320px"
              v-model="filter.timerange"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始"
              end-placeholder="结束"
              :picker-options="pickerOptions"
            ></el-date-picker>
          </el-form-item>
        <el-form-item label="商品编码:">
              <el-input v-model="filter.goodsCode"  ondblclick="window.showordergoodsrelevanceinput();" onkeydown="if(event.keyCode==13) {window.showordergoodsrelevanceinput();}"   placeholder="商品编码" :maxLength="100"/>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onDirectSearch">查询</el-button>
          </el-form-item>
        </el-form>
      </template>
      <ces-table
        ref="table"
        :that='that'
        :isIndex='true'
        :hasexpand='true'
        @sortchange='sortchange'
        :summaryarry="summaryarry"
        :tableData='list'
        :tableCols='tableCols'
        :tableHandles='tableHandles'
        :loading="listLoading">
      </ces-table>
      <template #footer>
        <my-pagination
          ref="pager"
          :total="total"
          :checked-count="sels.length"
          @get-page="getlist"
        />
      </template>  
    </my-container>
    <my-container style="width:29%;">
        <template #header>
            <div style="margin-top:20px;">
              <div id="wrhPie"></div>
          </div>  
        </template>
             <div style="margin-top:100px;width:100%;">
              <el-table :data="piesData" stripe border highlight-current-row :fit="true" max-height="300" v-show="isShowPiesTable">
                <el-table-column prop="name" label="仓库" width="auto" sortable>
                </el-table-column>
                <el-table-column prop="qty" label="销量" width="100" sortable>
                </el-table-column>
                <el-table-column prop="qtyRatio" label="销量占比(%)" width="100" sortable>
                </el-table-column>
                  <el-table-column prop="value" label="订单量" width="100" sortable>
                </el-table-column>
                <el-table-column prop="ratio" label="订单量占比(%)" width="100" sortable>
                </el-table-column>
              </el-table>
            </div>               
        <template #footer>
            <div style="height:42px;"></div>
        </template>
    </my-container>

    <el-dialog title="请输入商品编码" :visible.sync="inputdialogVisible" width="30%">
      <span>
        <el-input
  type="textarea"
  :autosize="{ minRows: 2, maxRows: 10}"
  placeholder="请输入内容"
  v-model="allgoodcodes">
</el-input>
      </span>
      <span slot="footer" class="dialog-footer">        
        <el-button @click="inputdialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="inputtogoodscode">确定</el-button>
      </span>
    </el-dialog>
  </el-container>

</template>
<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { 
  pageOrderGoodsRelevance, 
  exportOrderGoodsRelevance,
  getOrderGoodsRelevancePie,
} from "@/api/order/ordergoods";
import * as echarts from "echarts";
const tableCols =[
      //  {istrue:true,prop:'id',label:'#', width:'80'},
       {istrue:true,prop:'goodsCode',label:'商品编码', width:'150',type:"click",handle:(that,row,column,cell)=>that.canclick(row,column,cell)},
       {istrue:true,prop:'goodsName',label:'商品名称', width:'auto'},
       {istrue:true,prop:'goodsImage',label:'图片', width:'60',type:'imageGoodsCode',goods:{code:'goodsCode',name:'goodsName'}},
       {istrue:true,prop:'groupName',label:'运营组', width:'110'},
       {istrue:true,prop:'brandName',label:'采购组', width:'130'},
       {istrue:true,prop:'sendWarehouseName',label:'仓库', width:'130'},
        {istrue:true,prop:'orderNum',label:'订单量', width:'100',sortable:'custom'},
       {istrue:true,prop:'orderNumRatio',label:'订单量占比（%）', width:'150',sortable:'custom'},
       {istrue:true,prop:'qty',label:'销量', width:'100',sortable:'custom'},
       {istrue:true,prop:'saleRatio',label:'销量占比（%）', width:'150',sortable:'custom'},
       //{istrue:true,prop:'sendWarehouseRatio',label:'在仓占比（%）', width:'150',sortable:'custom'}
     ];
const tableHandles1=[
        {label:"导出", handle:(that)=>that.onExport()}
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: {
        startDate:null,
        endDate:null,
        goodsCode:null,
        timerange:[formatTime(dayjs().startOf("month"), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")]
      },
      list: [],
      summaryarry:{},
      pager:{OrderBy:"saleRatio",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      treeprops:{
        children: 'children', 
        hasChildren: 'hasChildren'
      },
      platformList: [],
      shopList: [],
      dialogVisible: false,
      autoform:{
               fApi:{},
               rule:[],
               options:{submitBtn:false},
        },
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,     
      deleteLoading: false,
      formtitle:"新增",
      fileList:[],
      allgoodcodes:"",
      inputdialogVisible:false,
      pickerOptions:{
        disabledDate(time){
          return time.getTime()>Date.now();
        }
      },
      myChart:null,
      piesData:[],
      isShowPiesTable:false
    }
  },
  async mounted() {
    await this.setFilterFromQuery();
    var that=this;
    window.showordergoodsrelevanceinput=function(){
          that.inputdialogVisible=true;
    }
  },
  methods: {
    showordergoodsrelevanceinput(){
        this.inputdialogVisible=true;
    },
    async onDirectSearch(){
     var goodscode=this.filter.goodsCode;
     var newgoodslist=[];

     if(!goodscode){
        this.$message.error('请填写商品编码！')
        return;
      }


     if(goodscode.trim().indexOf(',')>0)
     {
       var goodslist=goodscode.trim().split(',');       
       for(var i in goodslist)
       {
         newgoodslist.push(goodslist[i]);
       }
     }
      if(goodscode.trim().indexOf(' ')>0)
     {
       var goodslist=goodscode.trim().split(' ');      
       for(var i in goodslist)
       {
         newgoodslist.push(goodslist[i]);
       }
     }
     if(newgoodslist.length>0)
     {
       this.allgoodcodes=newgoodslist.join('\n');
     }
     else
     {
       this.allgoodcodes=this.filter.goodsCode;
     }
      await this.onSearch();
    },
   async inputtogoodscode(){
      this.inputdialogVisible=false;
      await this.onSearch();
    },
    //从查询字符串中获取参数
    async setFilterFromQuery(){
      if(this.$route.query.goodsCode){
          this.filter.goodsCode=this.$route.query.goodsCode;
          this.filter.groupIdSrc=null;
          this.filter.groupIdNew=null;
          if(this.$route.query.timerange){
            this.filter.timerange=this.$route.query.timerange;
          }
          await this.onSearch();
      }
    },
    //导出
    async onExport(){
        var params=this.getCondition();
        if(params===false){
            return;
        }
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportOrderGoodsRelevance(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','商品关联分析_' +this.filter.goodsCode+'_'+ new Date().toLocaleString() + '.xlsx' )
        aLink.click();
    },
    //获取查询条件
    getCondition(){
        if (this.filter.timerange&&this.filter.timerange.length>1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      else {
        this.$message({message:"请先选择付款时间",type:"warning"});
        return false;
      }
      if(!this.filter.goodsCode){
        this.$message({message:"请先输入商品编码",type:"warning"});
        return false;
      }
      var pager = this.$refs.pager.getPager();
      var page  = this.pager;
      const params = {
        ...pager,
        ...page,
        ... this.filter
      }

      return params;
    },
    //查询第一页
    async onSearch() {
      var that=this;
      that.$refs.pager.setPage(1)   ;
      
     
      var searchedresult=[]; 
      if(that.allgoodcodes.trim().length>0)
        {
          var list=that.allgoodcodes.trim().split('\n');
          var newlist=[];
          for(var i=0;i<list.length;i++)
          {
            if(list[i].trim().length>0)
            newlist.push(list[i]);
            }
          }
          that.filter.goodsCode=newlist.join(",");
          await that.getlist();
    },
    //分页查询
    async getlist() {
      var params=this.getCondition();     
      if(params===false){
            return;
      }
      this.listLoading = true
      const res = await pageOrderGoodsRelevance(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry=res.data.summary;
      data.forEach(d => {
        d._loading = false
      })
      this.list = data;
      await this.onSearchCharts();
    },
    //排序查询
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else{
        var orderBy =column.prop=="shopName"?"shopCode":column.prop;
        this.pager={OrderBy:orderBy,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearch();
    },
    //图表搜索
    async onSearchCharts() {
      var params=this.getCondition();
      if(params===false){
        return false;
      }
      const res = await getOrderGoodsRelevancePie(params);
      this.isShowPiesTable=false;
      if (!res?.code) {
        return false;
      }
      var chartDom = document.getElementById("wrhPie");
      this.myChart && this.myChart.clear();
      var piedata=[];
      for(var i in res.data)
      {
        piedata.push({name:res.data[i].name+"(订单量)",value:res.data[i].value,ratio:res.data[i].ratio})
      }
      this.piesData = res.data;
      if(this.piesData&&this.piesData.length>0)
        this.isShowPiesTable=true;
      this.myChart = this.myChart ?? echarts.init(chartDom);
      var option = await this.Getoptions(piedata);
      await option && this.myChart.setOption(option);
    },  
    //图表配置
    async Getoptions(data) {    
      var option = {
        title: { text: "仓库分布",left: 'left' },
        tooltip: {
          trigger: "item",
          textStyle: { align: "left" },
        },
        legend: {
          formatter: function (name) {
            return echarts.format.truncateText(
              name,
              200,
              "10px Microsoft Yahei",
              "..."
            );
          },
          tooltip: {
            show: true,
          },
          left: 'center',
          top:'bottom',
        },
        grid: {
          left: "1%",
          right: "1%",
          bottom: "1%",
          containLabel: true,
        },
        series: {
          type: 'pie',
          radius: '50%',
          data:data,
          /*emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
          },*/
          labelLine: {
              show: true
          },
          label:{
            normal:{
               show:true,
               formatter:'{b}'+'\n\r'+'{c}' + '\n\r' + '{d}%',
               position:'outside'
            }
          }
        },
      };
      return option;
    },
    //单击单元格
    async canclick(row, column, cell){
     if (column.prop=='goodsCode'&&(!row.children||row.children.length<=0)) { 
        this.$router.push({
          name:"/order/ordergoodssales",
          query:{
            goodsCode:row.goodsCode.replace('（查询条件）',''),
            timerange:this.filter.timerange,
          }
        })
     }
    },
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary{
  margin-right: 7px;
}
#wrhPie{
  width:400px;
  height:400px;
  margin-top:20px;
}
</style>
