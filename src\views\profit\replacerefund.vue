<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="付款时间">
                    <el-date-picker style="width: 210px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <el-form-item label="代拍时间">
                    <el-date-picker style="width: 210px" v-model="filter.timerange1" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <el-form-item label="批次号:">
                    <el-input v-model.trim="filter.batchNumber" :maxlength="19" style="width: 150px" placeholder="批次号"
                        @keyup.enter.native="onSearch" clearable />
                </el-form-item>
                <el-form-item label="内部订单号:">
                    <el-input v-model.trim="filter.orderNoInner" style="width: 150px" placeholder="内部订单号"
                        @keyup.enter.native="onSearch" clearable />
                </el-form-item>
                <el-form-item label="退款金额:">
                    <el-input-number v-model="filter.minRefundAmount" :min="-999999999" :max="999999999"></el-input-number>至<el-input-number :min="-999999999" :max="999999999" v-model="filter.maxRefundAmount"></el-input-number>
            </el-form-item>
                <el-form-item label="导入人:">
                    <el-input v-model.trim="filter.operateName" style="width: 150px" placeholder="导入人"
                        @keyup.enter.native="onSearch" clearable />
                </el-form-item>
                <el-form-item label="商品编码:">
                    <el-input v-model.trim="filter.goodsCode" style="width: 150px" placeholder="商品编码"
                        @keyup.enter.native="onSearch" clearable />
                </el-form-item>
                <el-form-item label="所属店铺:">
                    <el-select filterable v-model="filter.shopCode" placeholder="请选择店铺" clearable style="width: 120px">
                        <el-option v-for="item in shopList" :key="item.id" :label="item.shopName"
                            :value="item.shopCode" />
                    </el-select>
                </el-form-item>
                <el-form-item label="订单状态:">
                    <el-select filterable v-model="orderStatus" placeholder="订单状态" multiple :collapse-tags="true" clearable style="width: 100px">
                        <el-option label="发货中" value="发货中"></el-option>
                        <el-option label="取消" value="取消"></el-option>
                        <el-option label="被合并" value="被合并"></el-option>
                        <el-option label="已发货" value="已发货"></el-option>
                        <el-option label="被拆分" value="被拆分"></el-option>
                        <el-option label="异常" value="异常"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="售后情况:">
                    <el-select filterable v-model="saleAfterNewInfo" placeholder="订单状态" multiple :collapse-tags="true" clearable style="width: 100px">
                        <el-option label="发货前取消" value="发货前取消"></el-option>
                        <el-option label="发货前售后" value="发货前售后"></el-option>
                        <el-option label="发货后取消" value="发货后取消"></el-option>
                        <el-option label="发货后售后" value="发货后售后"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
            @select="selectchange" :tableHandles='tableHandles' @cellclick="cellclick" :loading="listLoading">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="40%">
            <span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="4" action
                    accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }} </el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>


        <el-dialog title="批量删除" :visible.sync="dialogdeletebatchNumberVisible" width="500px" v-dialogDrag>
            <el-row>
                <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
                    <el-input placeholder="请输入批次号" v-model="deletefilter.batchNumber"
                        oninput="value=value.replace(/[^\d]/g,'')" maxlength="19" style="width: 100%"></el-input>
                </el-col>
            </el-row>
            <el-row>
                <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
                    <el-button type="primary" @click="onDeletebatchRule">删除</el-button>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogdeletebatchNumberVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import { exportReplaceReFundList, importReplaceRefund, getReplaceRefundList, batchDeleteReplaceRefund } from "@/api/profit/reportday"
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import replacedayreportdetail from "./replacedayreportdetail.vue"

const tableCols = [
    { istrue: true, prop: 'createdTime', label: '导入时间', tipmesg: '', width: '140', sortable: 'custom', formatter: (row) => { return formatTime(row.createdTime, 'YYYY-MM-DD HH:mm') } },
    { istrue: true, prop: 'createdUserName', label: '导入人员', tipmesg: '', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'batchNumber', label: '批次号', tipmesg: '', width: '160', sortable: 'custom', },
    { istrue: true, prop: 'agentOrderTime', label: '代拍时间', tipmesg: '', width: '140', sortable: 'custom', formatter: (row) => { return formatTime(row.agentOrderTime, 'YYYY-MM-DD HH:mm') } },
    { istrue: true, prop: 'purchaser', label: '采购员', tipmesg: '', width: '100', },
    { istrue: true, prop: 'shopName', label: '店铺名称', tipmesg: '', width: '200', },
    { istrue: true, prop: 'goodsCode', label: '商品编码', tipmesg: '', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: '125', },
    { istrue: true, prop: 'count', label: '数量', sortable: 'custom', tipmesg: '', width: '80', },
    { istrue: true, prop: 'payAmount', label: '买家支付金额', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'orderNoInner', label: '内部订单号', tipmesg: '', width: '150', sortable: 'custom',type:'orderLogInfo',orderType:'orderNoInner'  },
    { istrue: true, prop: 'expressCompany', label: '快递公司', tipmesg: '', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'expressNo', label: '快递单号', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'agentAmount', label: '代拍金额', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'purchasingChannel', label: '采购渠道', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'orderNo', label: '线上订单号（聚水潭公司店铺）', tipmesg: '', width: '160', sortable: 'custom', },
    { istrue: true, prop: 'payOrderNo', label: '支付订单编号（线上线下付款）', tipmesg: '', width: '160', sortable: 'custom', },
    { istrue: true, prop: 'payTime', label: '付款时间', tipmesg: '', width: '140', sortable: 'custom', formatter: (row) => { return row.payTime ? formatTime(row.payTime, 'YYYY-MM-DD HH:mm') : '' } },
    { istrue: true, prop: 'orderRefundAmount', label: '订单退款金额', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'saleAfterInfo', label: '标签', tipmesg: '', width: '160', sortable: 'custom', },
    { istrue: true, prop: 'saleAfterNewInfo', label: '售后情况', tipmesg: '', width: '160', sortable: 'custom', },
    { istrue: true, prop: 'orderStatus', label: '订单状态', tipmesg: '', width: '100', sortable: 'custom', },
]

const tableHandles = [
    { label: "导入", handle: (that) => that.startImport() },
    { label: "导出", handle: (that) => that.onExportDetail() },
    { label: "模板-代拍订单查询退款导入模板", handle: (that) => that.downloadOrherTemplate() },
    { label: "批量删除", handle: (that) => that.onbacthDelete() },
];

const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunhanAdminReplacedayreport',
    components: { container, cesTable, MyConfirmButton, replacedayreportdetail },

    data() {
        return {
            that: this,
            filter: {
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime],
                procode: null,
                title: null,
                platform: null,
                shopCode: null,
                groupId: null,
                operateSpecialId: null,
                operateName: null,
                user3Id: null,
                shopId: null,
                newPattern: null,
                customer: null,
                status: null,
                refundStatus: null,
                minRefundAmount:undefined,
                maxRefundAmount:undefined,
                orderStatus: null,
                saleAfterNewInfo: null
            },
            orderStatus:[],
            saleAfterNewInfo:[],
            list: [],
            shopList: [],
            summaryarry: {},
            pager: { OrderBy: "payTime", IsAsc: false },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            onHandNumber: null,
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            sels: [],
            editparmLoading: false,
            editparmLoading1: false,
            editparmLoading2: false,
            editparmVisible: false,
            editparmVisible1: false,
            editparmVisible2: false,
            dialogVisible: false,
            listLoading: false,
            uploadLoading: false,
            showDetailVisible: false,
            dialogdeletebatchNumberVisible: false,
            deletefilter: { batchNumber: null },
        };
    },

    async mounted() {
        await this.onSearch()
        await this.onchangeplatform()
    },

    methods: {
        //获取店铺
        async onchangeplatform() {
            this.categorylist = []
            const res1 = await getshopList({ platform: null, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            this.filter.payStartTime = null;
            this.filter.payEndTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            if (this.filter.timerange1) {
                this.filter.payStartTime = this.filter.timerange1[0];
                this.filter.payEndTime = this.filter.timerange1[1];
            }
            if (this.orderStatus)
                this.filter.orderStatus = this.orderStatus.join();
            if (this.saleAfterNewInfo)
                this.filter.saleAfterNewInfo = this.saleAfterNewInfo.join();
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await getReplaceRefundList(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            this.list = data
        },
        //导出
        async onExportDetail() {
            let page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            this.filter.payStartTime = null;
            this.filter.payEndTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            if (this.filter.timerange1) {
                this.filter.payStartTime = this.filter.timerange1[0];
                this.filter.payEndTime = this.filter.timerange1[1];
            }
            const params = { ...page, ... this.filter }
            let loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
            let res = await exportReplaceReFundList(params);
            loadingInstance.close();
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '代拍订单退款详情_' + new Date().toLocaleString() + '.xlsx')
            aLink.click();
        },
        //开始导入
        startImport() {
            this.dialogVisible = true;
        },
        //取消导入
        cancelImport() {
            this.dialogVisible = false;
        },
        uploadSuccess(response, file, fileList) {
            if (response.code == 200) {
            } else {
                fileList.splice(fileList.indexOf(file), 1);
            }
        },
        async submitUpload() {
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请先选取文件", type: "warning" });
                return false;
            }
            this.fileHasSubmit = true;
            this.uploadLoading = true;
            this.$refs.upload.submit();
        },
        async uploadFile(item) {
            if (!this.fileHasSubmit) {
                return false;
            }
            this.fileHasSubmit = false;
            const form = new FormData();
            form.append("token", this.token);
            form.append("upfile", item.file);
            const res = await importReplaceRefund(form);
            if (res.code == 1) {
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
                this.$refs.upload.clearFiles();
                this.dialogVisible = false;
            }
            else this.$message({ message: res.msg, type: "warning" });
            this.uploadLoading = false;

        },
        async uploadChange(file, fileList) {
            if (fileList && fileList.length > 0) {
                let list = [];
                for (const element of fileList) {
                    if (element.status == "success")
                        list.push(element);
                    else
                        list.push(element.raw);
                }
                this.fileList = list;
            }
        },
        async uploadRemove(file, fileList) {
            this.uploadChange(file, fileList);
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event) {

        },
        async downloadOrherTemplate() {
            window.open("../static/excel/dayreport/代拍订单查询退款导入模板.xlsx", "_self");
        },
        async onbacthDelete() {
            this.dialogdeletebatchNumberVisible = true;
            this.deletefilter = { batchNumber: null };
        },
        async onDeletebatchRule() {
            if (!this.deletefilter.batchNumber) {
                this.$message({ message: "请输入批次号！", type: "warning" });
                return;
            }
            const res = await batchDeleteReplaceRefund(this.deletefilter);
            this.dialogdeletebatchNumberVisible = false;
            if (!res?.success) return;
            this.$message({ message: "已批量删除", type: "success" });
            this.getlist();
        },
    },
};
</script>

<style lang="scss" scoped>

</style>
