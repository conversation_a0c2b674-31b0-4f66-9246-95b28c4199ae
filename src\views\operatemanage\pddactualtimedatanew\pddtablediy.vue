<template>
  <div style="height: 100%;">
    <vxe-table class="mytable-footer" border show-overflow :loading="tableloading" :show-header="true"
      show-footer @cell-click="cellclick" height="100%" :data="tableData3" :column-config="{resizable: true}"
      :footer-method="footerMethod" @checkbox-all="selectAllEvent" :checkbox-config="{visibleMethod: showCheckboxkMethod}"
       @checkbox-change="selectChangeEvent">
      <vxe-column type="seq" width="60" fixed="left"></vxe-column>
      <vxe-column type="checkbox" width="60"></vxe-column>
      <vxe-column field="yearMonthDay" title="年月日" width="100">
        <!-- <template #default="{ data }">
          <div>{{ data.yearMonthDay }}</div>
        </template> -->
        <template #default="{ row }">
          <div>{{ row.yearMonthDay.slice(0,10) }}</div>
        </template>
      </vxe-column>
      <vxe-column field="styleCode" title="系列编码" width="100"></vxe-column>
      <vxe-column field="shopName" title="店铺名称" width="100"></vxe-column>
      <vxe-column field="groupName" title="小组" width="100"></vxe-column>
      <vxe-column field="operateSpecialUserName" title="专营人员" width="100"></vxe-column>
      <vxe-column field="userName" title="运营助理" width="100"></vxe-column>
      <vxe-column field="user3Name" title="备用负责人" width="100"></vxe-column>
      <vxe-column field="a6" title="趋势图" width="100">
        <template #default="{ data }">
          <el-button type="text">查看</el-button>
        </template>
      </vxe-column>
      <vxe-column field="proCode" title="商品id" width="100"></vxe-column>
      <vxe-column field="title" title="商品名称" width="100"></vxe-column>
      <vxe-column field="shopVisitorNumber" title="总访客数" width="100"></vxe-column>
      <vxe-column field="allPayAmount" title="总支付金额" width="100"></vxe-column>
      <vxe-column field="allPayOrderCount" title="总支付订单数" width="100"></vxe-column>
      <vxe-column field="allTrunOutRate" title="转换率" width="100"></vxe-column>
      <vxe-column field="allSpendAmount" title="总花费" width="100"></vxe-column>
      <vxe-column field="allExtendTradeAmount" title="推广交易额" width="100"></vxe-column>
      <vxe-column field="allPayRate" title="付费占比" width="100"></vxe-column>
      <vxe-column field="allInvestmentRate" title="投产" width="100"></vxe-column>
      <vxe-column field="productSaleCostAmount" title="销售成本" width="100"></vxe-column>
      <vxe-column field="profit1" title="毛一利润" width="100"></vxe-column>
      <vxe-column field="profit1Rate" title="毛一利润率" width="100"></vxe-column>

      <vxe-column field="profit2" title="毛二利润" width="100"></vxe-column>
      <vxe-column field="profit2Rate" title="毛二利润率" width="100"></vxe-column>

      <vxe-column field="profit3" title="毛三利润" width="100"></vxe-column>
      <vxe-column field="profit3Rate" title="毛三利润率" width="100"></vxe-column>
      <vxe-column field="a22" title="链接状况" width="100">
        <template #default="{ data }">
          <el-button type="text">查看</el-button>
        </template>
      </vxe-column>
      <vxe-column field="a23" title="链接详情" width="100">
        <template #default="{ data }">
          <el-button type="text">查看</el-button>
        </template>
      </vxe-column>
      <vxe-column field="a24" title="操作记录" width="100">
        <template #default="{ data }">
          <el-button type="text">查看</el-button>
        </template>
      </vxe-column>
      <!-- <vxe-column field="a25" title="违规扣款" width="100">
        <template #default="{ data }">
          <el-button type="text">查看</el-button>
        </template>
      </vxe-column> -->
      <vxe-column field="refundAmont" title="总退款金额" width="100"></vxe-column>
      <vxe-column field="refundAmontRate" title="总退率" width="100"></vxe-column>

      <vxe-colgroup title="全站推广">
        <vxe-column field="allStationExtendSpendAmount" title="花费" min-width="100" sortable></vxe-column>
        <vxe-column field="allStationExtendTradeAmount" title="交易额" min-width="100"></vxe-column>
        <vxe-column field="allStationExtendInvestmentRate" title="投产" min-width="100"></vxe-column>
        <vxe-column field="allStationExtendOrderCount" title="成交笔数" width="200"></vxe-column>

        <vxe-column field="allStationExtendExposureCount" title="曝光量" min-width="100"></vxe-column>
        <vxe-column field="allStationExtendClickCount" title="点击量" width="200"></vxe-column>
        <vxe-column field="allStationExtendClickRate" title="点击率" min-width="100"></vxe-column>
        <vxe-column field="allStationExtendTrunOutRate" title="转换率" width="200"></vxe-column>
        <vxe-column field="a36" title="推广详情" width="200">
          <template #default="{ data }">
          <el-button type="text">查看</el-button>
        </template>
        </vxe-column>

      </vxe-colgroup>

      <vxe-colgroup title="多多搜索">
        <vxe-column field="duoDuoSearchSpendAmount" title="花费" min-width="100" sortable></vxe-column>
        <vxe-column field="duoDuoSearchTradeAmount" title="交易额" min-width="100"></vxe-column>
        <vxe-column field="duoDuoSearchInvestmentRate" title="投产" min-width="100"></vxe-column>
        <vxe-column field="duoDuoSearchOrderCount" title="成交笔数" width="200"></vxe-column>

        <vxe-column field="duoDuoSearchExposureCount" title="曝光量" min-width="100"></vxe-column>
        <vxe-column field="duoDuoSearchClickCount" title="点击量" width="200"></vxe-column>
        <vxe-column field="duoDuoSearchClickRate" title="点击率" min-width="100"></vxe-column>
        <vxe-column field="duoDuoSearchTrunOutRate" title="转换率" width="200"></vxe-column>
        <vxe-column field="a45" title="推广详情" width="200">
          <template #default="{ data }">
            <el-button type="text">查看</el-button>
          </template>
        </vxe-column>

      </vxe-colgroup>

      <vxe-colgroup title="多多场景">
        <vxe-column field="duoDuoSceneSpendAmount" title="花费" min-width="100" sortable></vxe-column>
        <vxe-column field="duoDuoSceneTradeAmount" title="交易额" min-width="100"></vxe-column>
        <vxe-column field="duoDuoSceneInvestmentRate" title="投产" min-width="100"></vxe-column>
        <vxe-column field="duoDuoSceneOrderCount" title="成交笔数" width="200"></vxe-column>

        <vxe-column field="duoDuoSceneExposureCount" title="曝光量" min-width="100"></vxe-column>
        <vxe-column field="duoDuoSceneClickCount" title="点击量" width="200"></vxe-column>
        <vxe-column field="duoDuoSceneClickRate" title="点击率" min-width="100"></vxe-column>
        <vxe-column field="duoDuoSceneTrunOutRate" title="转换率" width="200"></vxe-column>
        <vxe-column field="a54" title="推广详情" width="200">
          <template #default="{ data }">
            <el-button type="text">查看</el-button>
          </template>
        </vxe-column>

      </vxe-colgroup>

      <vxe-colgroup title="直播推广">
        <vxe-column field="liveExtendSpendAmount" title="花费" min-width="100" sortable></vxe-column>
        <vxe-column field="liveExtendTradeAmount" title="交易额" min-width="100"></vxe-column>
        <vxe-column field="liveExtendInvestmentRate" title="投产" min-width="100"></vxe-column>
        <vxe-column field="liveExtendOrderCount" title="成交笔数" width="200"></vxe-column>
        <vxe-column field="a63" title="推广详情" width="200">
          <template #default="{ data }">
            <el-button type="text">查看</el-button>
          </template>
        </vxe-column>

      </vxe-colgroup>

      <vxe-colgroup title="免费流量">
        <vxe-column field="freeVisitorNumber" title="访客数" min-width="100" sortable></vxe-column>
        <vxe-column field="freePayAmount" title="支付金额" min-width="100"></vxe-column>
        <vxe-column field="freePayOrderCount" title="支付订单数" min-width="100"></vxe-column>
        <vxe-column field="freeTrunOutRate" title="转换率" width="200"></vxe-column>
      </vxe-colgroup>

    </vxe-table>

    <!-- //链接状况 -->
    <el-dialog title="链接状况" :visible.sync="dialogVisible" width="50%" v-dialogDrag>
      <ljzkdialog :rowmsg="rowmsg" ref="ljzkdialogref"></ljzkdialog>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>

    <!-- //链接详情 -->
    <el-dialog title="链接详情" :visible.sync="ljxqdialogshow" width="70%" v-dialogDrag>
      <ljxqdialog></ljxqdialog>
      <span slot="footer" class="dialog-footer">
        <el-button @click="ljxqdialogshow = false">取 消</el-button>
        <el-button type="primary" @click="ljxqdialogshow = false">确 定</el-button>
      </span>
    </el-dialog>

    <!-- //趋势图 -->
    <el-dialog title="趋势图" :visible.sync="qstdialogshow" width="90%" v-dialogDrag>
      <qstdialog :rowmsg="rowmsg"></qstdialog>
      <span slot="footer" class="dialog-footer">
        <el-button @click="qstdialogshow = false">取 消</el-button>
        <el-button type="primary" @click="qstdialogshow = false">确 定</el-button>
      </span>
    </el-dialog>

     <!-- //全栈推广 -->
     <el-dialog title="趋势图" :visible.sync="qztgdialogshow" width="60%" v-dialogDrag>
      <qztgdialog :rowmsg="rowmsg" ref="popularize"></qztgdialog>
      <span slot="footer" class="dialog-footer">
        <el-button @click="qztgdialogshow = false">取 消</el-button>
        <el-button type="primary" @click="handlePopularClick">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 多多搜索 -->
    <el-dialog title="趋势图" :visible.sync="ddssdialogshow" width="60%" v-dialogDrag>
      <ddssdialog :rowmsg="rowmsg" ref="search"></ddssdialog>
      <span slot="footer" class="dialog-footer">
        <el-button @click="ddssdialogshow = false">取 消</el-button>
        <el-button type="primary" @click="handleSearchClick">确 定</el-button>
      </span>
    </el-dialog>

    <!-- //多多场景 -->
    <el-dialog title="趋势图" :visible.sync="ddcjdialogshow" width="60%" v-dialogDrag>
      <ddcjdialog :rowmsg="rowmsg" ref="scenario"></ddcjdialog>
      <span slot="footer" class="dialog-footer">
        <el-button @click="ddcjdialogshow = false">取 消</el-button>
        <el-button type="primary" @click="handleScenarioClick">确 定</el-button>
      </span>
    </el-dialog>

    <!-- //直播推广 -->
    <el-dialog title="趋势图" :visible.sync="zbtgdialogshow" width="60%" v-dialogDrag>
      <zbtgdialog :rowmsg="rowmsg" ref="live"></zbtgdialog>
      <span slot="footer" class="dialog-footer">
        <el-button @click="zbtgdialogshow = false">取 消</el-button>
        <el-button type="primary" @click="handleLiveClick">确 定</el-button>
      </span>
    </el-dialog>

    <!-- //操作记录 -->
    <el-dialog title="操作记录" :visible.sync="czjldialogshow" width="60%" v-dialogDrag>
      <czjldialog :rowmsg="rowmsg" ref="czjldialoglef"></czjldialog>
      <span slot="footer" class="dialog-footer">
        <el-button @click="czjldialogshow = false">取 消</el-button>
        <el-button type="primary" @click="czjldialogshow = false">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import ljzkdialog from '@/views/operatemanage/pddactualtimedatanew/dialogfile/ljzkdialog.vue'
import ljxqdialog from '@/views/operatemanage/pddactualtimedatanew/dialogfile/ljxqdialog.vue'
import qstdialog from '@/views/operatemanage/pddactualtimedatanew/dialogfile/qstdialog.vue'
import qztgdialog from '@/views/operatemanage/pddactualtimedatanew/dialogfile/qztgdialog.vue'
import czjldialog from '@/views/operatemanage/pddactualtimedatanew/dialogfile/czjldialog.vue'

import ddcjdialog from '@/views/operatemanage/pddactualtimedatanew/dialogfile/ddcjdialog.vue'
import ddssdialog from '@/views/operatemanage/pddactualtimedatanew/dialogfile/ddssdialog.vue'
import zbtgdialog from '@/views/operatemanage/pddactualtimedatanew/dialogfile/zbtgdialog.vue'
import dayjs from "dayjs";





export default {
  components: { ljzkdialog, ljxqdialog, qstdialog, qztgdialog, czjldialog, ddcjdialog, ddssdialog, zbtgdialog },
  props: ['alllist', 'tableloading'],
  data() {
    return {
      getlogList:{},
      summaryarry: {},
      summarycolumns: [],
      selrows: [],
      rowmsg: {},
      tableData3: [
      ],
      dialogVisible: false,
      ljxqdialogshow: false,
      qstdialogshow: false,
      qztgdialogshow: false,
      czjldialogshow: false,

      zbtgdialogshow: false,
      ddcjdialogshow: false,
      ddssdialogshow: false,
    }
  },
  watch: {
    async "alllist.list"(val) {
      if (val) {
        this.tableData3 = this.alllist.list;
        this.summaryarry = this.alllist.summary;
      }
    },
  },
  mounted() {


  },
  methods: {
    timeFormater(val) {
      return dayjs(val).format("YYYY-MM-DD")
    },
    showCheckboxkMethod({row}){
      return row.yearMonthDay.slice(0,10) == this.timeFormater(Date.now())
    },
    selectChangeEvent(e){
      this.selrows = e.records;
      this.$emit('update:rowschange',e.records)
    },
    selectAllEvent(e){
      this.selrows = e.records;
      this.$emit('update:rowschange',e.records)
    },
    //点击全站推广的确定
    async handlePopularClick () {
        this.$refs.popularize.SubmitEvent()
        this.qztgdialogshow = false
      },
      //点击多多搜索的确定
    async handleSearchClick () {
        this.$refs.search.SubmitEvent()
        this.ddssdialogshow = false
      },
      //点击多多场景的确定
    async handleScenarioClick () {
        this.$refs.scenario.SubmitEvent()
        this.ddcjdialogshow = false
      },
      //点击直播推广的确定
    async handleLiveClick () {
        this.$refs.live.SubmitEvent()
        this.zbtgdialogshow = false
      },
    footerCellClassName3({ $rowIndex, column }) {
      if (column.type === 'seq') {
        if ($rowIndex === 0) {
          return 'col-blue'
        } else {
          return 'col-red'
        }
      } else if (column.property === 'age') {
        if ($rowIndex === 1) {
          return 'col-red'
        }
      }
    },
    meanNum(list, field) {
      let count = 0
      list.forEach(item => {
        count += Number(item[field])
      })
      return count / list.length
    },
    sumNum(list, field) {
      let count = 0
      list.forEach(item => {
        count += Number(item[field])
      })
      return count
    },
    // footerMethod({ columns, data }) {
    //   const means = []
    //   const sums = []
    //   const others = []
    //   columns.forEach((column, columnIndex) => {
    //     if (columnIndex === 0) {
    //       means.push('平均')
    //       sums.push('和值')
    //       others.push('其他')
    //     } else {
    //       let meanCell = null
    //       let sumCell = null
    //       let otherCell = '-'
    //       switch (column.property) {
    //         case 'age':
    //         case 'amount':
    //           meanCell = this.meanNum(data, column.property)
    //           sumCell = this.sumNum(data, column.property)
    //           break
    //         case 'sex':
    //           otherCell = '无'
    //           break
    //       }
    //       means.push(meanCell)
    //       sums.push(sumCell)
    //       others.push(otherCell)
    //     }
    //   })
    //   // 返回一个二维数组的表尾合计
    //   return [means, sums, others]
    // },
    footerMethod ({ columns, data }) {

        const sums = [];
        if (!this.summaryarry)
            return sums
        var arr = Object.keys(this.summaryarry);
        if (arr.length == 0)
            return sums
        //const { columns, data } = param;
        var hashj = false;
        columns.forEach((column, index) => {
            if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
                var sum = this.summaryarry[column.property + '_sum'];
                if (sum == null) return;
                else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
                else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum.toFixed(2)
                else sums[index] = sum.toFixed(0)
            }
            else if (index == 0) sums[index] = '合计'
            else sums[index] = ''
        });
        if (this.summarycolumns.length == 0) {
            this.summarycolumns = columns;
            // this.initsummaryEvent();
        }
        return [sums]
    },

    cellclick(event) {
      this.rowmsg = event.row;
      if (event.column.property == 'a22') {//链接状况
        this.dialogVisible = true;
        this.$nextTick(async()=>{
          await this.$refs.ljzkdialogref.getDeliveryWarn();
          await this.$refs.ljzkdialogref.getPddActualTimeProduct();
          await this.$refs.ljzkdialogref.getPingduod();

        })
      } else if (event.column.property == 'a23') {//链接详情
        // this.ljxqdialogshow = true;
        let routeUrl = this.$router.resolve({
              path: "/ljxqdialog",
              query: {productCode:this.rowmsg.proCode, yearMonthDay: this.rowmsg.yearMonthDay,}
         });
       window.open(routeUrl.href, '_blank');
      } else if (event.column.property == 'a6') {//趋势图
        this.qstdialogshow = true;
      }else if (event.column.property == 'a36') {//
        this.qztgdialogshow = true;
      }else if (event.column.property == 'a24') {//
        this.czjldialogshow = true;
        this.$nextTick(async()=>{
          await this.$refs.czjldialoglef.getlogList();
        })
      }else if (event.column.property == 'a54') {//a63
        this.ddcjdialogshow = true;
      }else if (event.column.property == 'a45') {//
        this.ddssdialogshow = true;
      }else if (event.column.property == 'a63') {//
        this.zbtgdialogshow = true;
      }
    },
    getlist(val){
      debugger
      this.rowmsg = val;
    },
  }
}
</script>

<style lang="scss" scoped>
.mytable-footer ::v-deep .col-blue {
  background-color: #2db7f5;
  color: #fff;
}

.mytable-footer .col-red {
  background-color: red;
  color: #fff;
}
</style>
