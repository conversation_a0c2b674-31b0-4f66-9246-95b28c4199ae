<template>
  <container v-loading="pageLoading">
    <!-- 人员 -->
    <template #header>
      <div style="display: flex; margin-bottom: 20px">
        <el-select
          v-model="filter.company"
          clearable
          placeholder="分公司"
          @change="changeSetCompany"
          style="width: 150px; margin-right: 10px"
        >
          <el-option label="义乌" value="义乌" />
          <el-option label="南昌" value="南昌" />
        </el-select>
        <!-- <el-select v-model="filter.purchasePost" filterable clearable placeholder="岗位" @change="changeSetPost"
                    style="width: 150px;margin-right: 10px;">
                    <el-option v-for="item in postList" :key="item.purchasePost" :label="item.purchasePost"
                        :value="item.purchasePost" />
                </el-select> -->
        <el-select
          v-model="filter.purchasePost"
          filterable
          clearable
          placeholder="岗位"
          @change="changeSetPost"
          style="width: 150px; margin-right: 10px"
        >
          <el-option
            v-for="item in postList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <!-- <el-select v-model="filter.userName" filterable remote clearable placeholder="姓名"
                    style="width: 150px;margin-right: 10px;" @change="clearName">
                    <el-option v-for="item in userList" :key="item.userName" :label="item.userName"
                        :value="item.userName" />
                </el-select> -->
        <el-input
          v-model="filter.userName"
          clearable
          placeholder="姓名"
          style="width: 150px; margin-right: 10px"
          maxlength="50"
        ></el-input>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onAdd">新增</el-button>
        <!-- <el-button type="primary" @click="onExport">导出</el-button> -->
      </div>
    </template>
    <!--列表-->
    <vxetablebase
      :id="'purchasepostuser20230701'"
      :tablekey="'purchasepostuser20230701'"
      :tableData="list"
      :tableCols="tableCols"
      @cellClick="cellclick"
      @select="selectchange"
      :tableHandles="tableHandles"
      :loading="listLoading"
      :border="true"
      :that="that"
      ref="vxetable"
      @sortchange="sortchange"
    >
      <template slot="right">
        <vxe-column
          title="操作"
          :field="'col_opratorcol'"
          width="220"
          fixed="right"
        >
          <template #default="{ row }">
            <template v-if="row.parentId == null">
              <el-button type="text" size="default" @click="onHand(row, 1)"
                >编辑</el-button
              >
              <el-button type="text" size="default" @click="onHand(row, 2)">{{
                row.enabled == true ? "禁用" : "启用"
              }}</el-button>
              <!-- <el-button type="text" size="default" @click="onHand(row, 3)">计算</el-button> -->
            </template>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>

    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getlist"
      />
    </template>

    <el-dialog
      title="人员编辑"
      :visible.sync="dialogShowInfoVisible"
      v-dialogDrag
      width="71%"
      :close-on-click-modal="false"
      height="800px"
    >
      <opearpurchasepostuser
        :filter="detailfilter"
        @onClose="onClose"
        ref="opearpurchasepostuser"
      >
      </opearpurchasepostuser>
    </el-dialog>
  </container>
</template>

<script>
import { Loading } from "element-ui";
import dayjs from "dayjs";
import inputYunhan from "@/components/Comm/inputYunhan";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import { formatTime, formatNoLink } from "@/utils/tools";
import {
  getPurchasePostUserWagesAsync,
  opearPurchasePostUserWagesAsync,
  getPurchaseWagesCalPositions,
  handComputeWagesAsync,
  exportPurchasePostUserWagesAsync,
  getUserByPositionAsync,
  getUserByPositionByCurUserAsync
} from "@/api/profit/purchasepostwages";
import opearpurchasepostuser from "./opearpurchasepostuser.vue";
import dailyPicker from "./dailyPicker.vue";

const tableCols = [
  {
    istrue: true,
    prop: "company",
    align: "center",
    label: "分公司",
    sortable: "custom",
  },
  { istrue: true, prop: "purchasePost", label: "岗位", sortable: "custom" },
  { istrue: true, prop: "userName", label: "姓名", sortable: "custom" },
  // { istrue: true, prop: 'employeeType', label: '类型', width: 'auto', sortable: 'custom', formatter: (row) => row.employeeTypeName },
  // { istrue: true, prop: 'baseSalary', label: '底薪', width: 'auto', sortable: 'custom', },
  // { istrue: true, prop: 'performance', label: '绩效', width: 'auto', sortable: 'custom', },
  // { istrue: true, prop: 'attendanceDays', label: '出勤天数', width: 'auto', sortable: 'custom', },
];
const tableHandles = [
  //{ label: "导出", throttle: 3000, handle: (that) => that.onExport() },
];

export default {
  name: "YunHanAdminGoodsFinishedpart",
  components: {
    MyConfirmButton,
    container,
    vxetablebase,
    inputYunhan,
    opearpurchasepostuser,
    dailyPicker,
  },

  data() {
    return {
      checked: false,
      title: null,
      postOptions: null, //岗位下拉选择
      firmOptions: null, //分公司下拉选择
      addPersonnelDialog: false, //添加人员弹窗
      that: this,
      filter: {
        company: null,
        purchasePost: null,
        brandName: null,
      },
      detailfilter: null,
      addForm: {
        id: null,
        diffHours: null,
        part_Count: 0,
      },
      rules: {
        diffHours: [
          { required: true, message: "请选择开单间隔", trigger: "change" },
        ],
        part_Count: [
          { required: true, message: "请输入半成品数量", trigger: "blur" },
        ],
      },
      editFilter: {
        selData: [],
        type: null,
      },
      keywordsTip: "支持搜索的内容：采购单号、Erp单号",
      titile: "批量开启/关闭",
      userList: [],
      postList: [],
      userListAll: [],
      postListAll: [],
      basicsList: [],
      list: [],
      tableCols: tableCols,
      tableHandles: tableHandles,
      pager: { OrderBy: "createdTime", IsAsc: false },
      total: 0,
      sels: [],
      chooseTags: [],
      selids: [],
      listLoading: true,
      pageLoading: false,
      dialogAddVisible: false,
      dialogEditVisible: false,
      dialogShowInfoVisible: false,
      onFinishLoading: false,
      dialogLoading: false,
    };
  },

  async mounted() {
    await this.init();
    await this.onSearch();
  },

  methods: {
    clearName() {},
    computeDialog() {
      this.showComputeDialog = true;
    },
    onAdd() {
      this.title = "岗位新增";
      this.dialogShowInfoVisible = true;
      this.$nextTick(async () => {
        this.$refs.opearpurchasepostuser.onSearch(false);
      });
    },
    async onSearch() {
      //清空name的空格
      if (this.filter.userName) {
        this.filter.userName = this.filter.userName.replace(/\s/g, "");
      }
      this.$refs.pager.setPage(1);
      this.getlist();
      this.selids = [];
    },
    async init() {
      const { data, success } = await getPurchaseWagesCalPositions();
      if (!success) {
        return;
      } else {
        this.postList = data.map((item, i) => {
          return {
            label: item,
            value: i,
          };
        });
      }
      const { data: data1, success: success1 } = await getUserByPositionByCurUserAsync({
        positions: this.filter.positions,
      });
      if (!success1) {
        //给错误提示
        this.$message.error("获取岗位薪资人员失败");
        return;
      } else {
        this.userList = data1;
      }
    },
    async changeSetCompany() {
      if (this.filter.company === "义乌" || this.filter.company === "南昌") {
        this.userList = this.userListAll
          .filter((f) => f.company === this.filter.company)
          .map((item) => {
            return item;
          });
      } else if (this.filter.company === "其他") {
        this.userList = this.userListAll
          .filter((f) => f.company !== "南昌" && f.company !== "义乌")
          .map((item) => {
            return item;
          });
      } else {
        this.userList = this.userListAll.map((item) => {
          return item;
        });
      }
      this.filter.brandName = null;
    },
    async changeSetPost(e) {
      //如果岗位为空,就清空姓名
      if (!e) {
        this.filter.brandName = null;
        return;
      }
      //清空岗位
      this.filter.positions = [];
      //根据e找出对应的岗位
      this.filter.positions.push(
        this.postList.find((item) => item.value === e).label
      );
      this.filter.purchasePost = this.postList.find(
        (item) => item.value === e
      ).label;
      const { data, success } = await getUserByPositionByCurUserAsync({
        positions: this.filter.positions,
      });
      if (!success) {
        //给错误提示
        this.$message.error("获取岗位薪资人员失败");
        return;
      } else {
        this.userList = data;
      }
    },
    //分页查询
    async getlist() {
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = { ...pager, ...page, ...this.filter };
      if (params === false) {
        return;
      }
      const { data, success } = await getPurchasePostUserWagesAsync(params);
      this.listLoading = false;
      if (!success) return;
      this.total = data.total;
      this.list = data.list;
    },
    editFormvalidate() {
      let isValid = false;
      this.$refs.ruleForm.validate((valid) => {
        isValid = valid;
      });
      return isValid;
    },
    async onClose(val) {
      if (val) {
        await this.getlist();
      }
      this.dialogShowInfoVisible = false;
    },
    async onHand(row, val) {
      if (val == 1) {
        this.detailfilter = row;
        this.title = "岗位编辑";
        this.dialogShowInfoVisible = true;
        this.detailfilter.id = row.id;
        this.$nextTick(async () => {
          this.$refs.opearpurchasepostuser.onSearch(true);
        });
      } else if (val == 2) {
        var enabled = !row.enabled;
        var para = { id: row.id, enabled };
        this.listLoading = true;
        var res = await opearPurchasePostUserWagesAsync(para);
        this.listLoading = false;
        if (res?.success) {
          this.$message({ type: "success", message: "操作成功!" });
          await this.getlist();
        } else {
          //this.$message({ type: 'success', message: res?.msg });
        }
      } else if (val == 3) {
        this.$confirm("此操作将计算薪资, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            var para = { id: row.id };
            var res = await handComputeWagesAsync(para);
            if (res?.success) {
              this.$message({
                type: "success",
                message: "操作成功，正在计算中!",
              });
              await this.getlist();
            } else {
            }
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消操作",
            });
          });
      }
    },
    async onExport() {
      if (this.onExporting) return;
      try {
        const params = { ...this.filter };
        var res = await exportPurchasePostUserWagesAsync(params);
        if (!res?.data) return;
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
        aLink.href = URL.createObjectURL(blob);
        aLink.setAttribute(
          "download",
          "采购个人薪资_" + new Date().toLocaleString() + ".xlsx"
        );
        aLink.click();
      } catch (err) {
        console.log(err);
      }
      this.onExporting = false;
    },
    async cellclick({
      row,
      rowIndex,
      $rowIndex,
      column,
      columnIndex,
      $columnIndex,
      triggerRadio,
      triggerCheckbox,
      triggerTreeNode,
      triggerExpandNode,
      $event,
    }) {
      if (column.property == "goodsCode") {
        let selData = { goodsCode: row.goodsCode };
        this.dialogShowInfoVisible = true;
        this.$nextTick(async () => {
          this.$refs.goodsfinishedpartdetail.loadData({ selRows: selData });
        });
      }
    },
    sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      this.onSearch();
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach((f) => {
        this.selids.push(f);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-input-number.is-controls-right .el-input__inner {
  text-align: left !important;
}

.infoBox {
  display: flex;
  margin-bottom: 20px;
  align-items: center;
  height: 40px;
  justify-content: space-between;

  .desc {
    width: 60px;
    margin-right: 10px;
  }
}

.mar {
  margin-right: 10px;
}

.title {
  font-size: 16px;
  color: #000;
}

.btnBox {
  display: flex;
  justify-content: space-around;
}

.pickerBox {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}
</style>
