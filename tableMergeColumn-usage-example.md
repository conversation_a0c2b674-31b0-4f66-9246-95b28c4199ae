# yh_vxetable 组件 tableMergeColumn 功能使用说明

## 功能概述

`mergeRowMethod` 方法已经修改为支持两种合并功能：
1. **列合并**：通过 `tableMergeColumn` 配置，将指定的多个列合并为一个单元格
2. **行合并**：通过 `somerow` 配置，将相同值的行进行合并（保留原有逻辑）

## 配置结构

### tableMergeColumn（列合并）
```javascript
tableMergeColumn: {
  column: ['deptType', 'deptName'], // 需要合并的列数组
  default: 'deptType' // 合并后展示的字段
}
```

### somerow（行合并，保持向后兼容）
```javascript
somerow: 'regionName,type,calculateMonth' // 逗号分隔的字段名
```

## 使用示例

### 在父组件中使用

```vue
<template>
  <vxetablebase
    :table-data="tableData"
    :table-cols="tableCols"
    :table-merge-column="tableMergeColumn"
    :some-row="somerow"
  />
</template>

<script>
export default {
  data() {
    return {
      // 列合并配置
      tableMergeColumn: {
        column: ['deptType', 'deptName'], // 将部门类型和部门名称列合并
        default: 'deptType' // 合并后显示部门类型的值
      },
      
      // 行合并配置（保持向后兼容）
      somerow: 'regionName,type,calculateMonth',
      
      tableCols: [
        { prop: 'calculateMonth', label: '计算月份', width: '120px' },
        { prop: 'type', label: '类型', width: '100px' },
        { prop: 'regionName', label: '区域', width: '100px' },
        { prop: 'deptType', label: '部门类型', width: '120px' },
        { prop: 'deptName', label: '部门名称', width: '120px' },
        { prop: 'value', label: '数值', width: '100px' }
      ],
      
      tableData: [
        {
          calculateMonth: '2024-01',
          type: '类型A',
          regionName: '华东',
          deptType: '销售部',
          deptName: '销售一部',
          value: 100
        },
        {
          calculateMonth: '2024-01',
          type: '类型A',
          regionName: '华东',
          deptType: '销售部',
          deptName: '销售二部',
          value: 200
        }
        // ... 更多数据
      ]
    }
  }
}
</script>
```

## 功能特点

### 1. 列合并逻辑（tableMergeColumn）
- 将指定的多个列在水平方向上合并为一个单元格
- 第一个列显示 `default` 字段指定的值
- 后续列被隐藏（rowspan: 0, colspan: 0）
- 合并后的单元格跨越所有指定列（colspan: 列数）

### 2. 行合并逻辑（somerow，保持原有逻辑）
- 将相同值的行在垂直方向上合并
- 只有当指定列的值完全相同时才进行合并
- 第一行显示内容，后续相同行被隐藏

### 3. 优先级处理
1. 首先检查 `tableMergeColumn` 配置，如果匹配则进行列合并
2. 然后检查 `somerow` 配置，如果匹配则进行行合并
3. 最后返回默认的不合并状态

### 4. 显示值处理
- 列合并时，如果配置了 `default` 字段且该字段有值，显示该字段的值
- **如果 `default` 字段为空字符串，合并后的单元格内容也为空**
- 如果没有配置 `default` 字段，显示第一个合并列的值（向后兼容）

## 实际效果

### 原始表格
| 部门类型 | 部门名称 | 数值 |
|---------|---------|------|
| 销售部   | 销售一部 | 100  |
| 销售部   | 销售二部 | 200  |

### 列合并后（tableMergeColumn: {column: ['deptType', 'deptName'], default: 'deptType'}）
| 部门类型（合并后） | 数值 |
|------------------|------|
| 销售部            | 100  |
| 销售部            | 200  |

## 不同 default 值的效果示例

### 示例 1：default 有值
```javascript
tableMergeColumn: {
  column: ['deptType', 'deptName'],
  default: 'deptType' // 显示部门类型的值
}
```
**效果**：合并后的单元格显示 `deptType` 字段的值，如 "销售部"

### 示例 2：default 为空
```javascript
tableMergeColumn: {
  column: ['deptType', 'deptName'],
  default: '' // 合并后显示空内容
}
```
**效果**：合并后的单元格内容为空

### 示例 3：未配置 default（向后兼容）
```javascript
tableMergeColumn: {
  column: ['deptType', 'deptName']
  // 没有 default 字段
}
```
**效果**：合并后的单元格显示第一个列（`deptType`）的值

## 注意事项

1. `tableMergeColumn.column` 数组中的字段名必须与表格列的 `prop` 属性一致
2. 列合并是水平合并，行合并是垂直合并，两者可以同时使用
3. `default` 字段可以是任意表格数据中存在的字段名
4. **当 `default` 为空字符串时，合并后的单元格内容为空**
5. 如果数据中某个字段值为 `null` 或 `undefined`，会显示为空字符串
6. 保持了与原有 `somerow` 配置的完全兼容性
