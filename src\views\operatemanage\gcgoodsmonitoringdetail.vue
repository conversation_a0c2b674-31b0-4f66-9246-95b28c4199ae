<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form
        class="ad-form-query"
        :inline="true"
        :model="filter"
        @submit.native.prevent
      >
                    <!-- <el-input v-model.trim="deletefilter.batchNumber" autocomplete="off" style="width:230px;"></el-input>
                   
               
             
                    <el-date-picker v-model="filter.timeRange" type="daterange" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                        range-separator="至" start-placeholder="导入开始日期" end-placeholder="导入结束日期" style="width:230px;margin-left: 20px;"
                    >
                    </el-date-picker>
                             
              
                <el-button type="primary" @click="onSearch">查询</el-button> -->
                <!-- <el-button type="primary" @click="onExport">导出</el-button>  -->
           </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn="true"  
          :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='tableData' 
          :tableHandles='tableHandles'  
          :tableCols='tableCols'   style="width:100%;height:92%;margin: 0">
        </ces-table>
        <!--分页-->
        <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
        </template>
    </my-container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import MyContainer from '@/components/my-container';
import cesTable from '@/components/Table/table.vue';
import {formatLinkProCode} from '@/utils/tools'
import {
    getOrderDetail, exportOrderDetail
} from '@/api/order/ordergoods';
import {getGongCompeteGoodsDetail,exportGongCompeteGoodsDetail} from '@/api/operatemanage/competegoods';
const tableCols =[
    {istrue:true,prop:'yearMonthDay',label:'日期',sortable:'custom', width:'200'},
    {istrue:true,prop:'payNum',label:'支付人数',sortable:'custom', width:'220'},
    {istrue:true,prop:'diffNum',label:'对比昨日支付人数',sortable:'custom', width:'160'},
];

const tableHandles=[
        {label:"导出", handle:(that)=>that.onExport()},
      ];

// const startDate = formatTime(dayjs().subtract(1,'day'), "YYYY-MM-DD");
// const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default ({
    name:"Users",
    components:{MyContainer,cesTable},
    props: {
            filter: {
               proCode:""

            },
            ischoice: { type: Boolean, default: true },
        },
    data(){
        return {

    deletefilter:{
        batchNumber:''
      },
            that:this, 
            // myfilter:{
            //     //proCode:null,
            //     // timeRange:[startDate,endDate],
            //     // startDate:null,
            //     // endDate:null,
            //     orderNo:null,
            //     orderNoInner:null,
            //     expressNo:null
            // },                      
            tableCols:tableCols,
             tableHandles:tableHandles,
            tableData:[],
            total: 0,
            pager:{OrderBy:"payNum",IsAsc:false},
            // listLoading: false,
            pageLoading: false,
            summaryarry:{},    
            sels:[],
        //       filter:{
        //      ProCode:null,
        //     // startDate:null,
        //     // endDate:null,
            
        // }, 
        };
    },
   
    async mounted() {
   
    
   
  },
  async created(){
    await this.getList();
  },
    methods:{
 
        async sortchange(column){
            if(!column.order)
                this.pager={};
            else
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
            await this.onSearch();
            },
            async onSearch(){
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        async getList(){
            // this.filter.startDate =null;
            // this.filter.endDate =null;
            // if (this.filter.timeRange && this.filter.timeRange.length>0) {
            //     this.filter.startDate = this.filter.timeRange[0];
            //     this.filter.endDate = this.filter.timeRange[1];
            // }
         
            console.log("我是详情的最终参数",this.filter)
            var pager = this.$refs.pager.getPager();
            const params = {...pager,...this.pager,...this.filter};
            const res = await getGongCompeteGoodsDetail(params)
            .then(res=>{
                this.total = res.data?.total;
                this.tableData = res.data?.list;
                that.summaryarry=res.data?.summary;     
            });
           
            // this.listLoading=false;
        },
        async onExport(){
            // this.filter.startDate =null;
            // this.filter.endDate =null;
            // if (this.filter.timeRange && this.filter.timeRange.length>0) {
            //     this.filter.startDate = this.filter.timeRange[0];
            //     this.filter.endDate = this.filter.timeRange[1];
            // }
            // else{
            //     this.$message({type: 'warning',message: '请选择日期范围'});
            //     return;
            // }
            // if(!this.filter.proCode){
            //     this.$message({type: 'warning',message: '请输入商品ID'});
            //     return;
            // }
            var pager = this.$refs.pager.getPager();
            const params = {...pager,...this.pager,...this.filter};
            var res= await exportGongCompeteGoodsDetail(params);
            if(!res?.data) {
                this.$message({message:"没有数据",type:"warning"});
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download','工厂竞品监控数据' +  new Date().toLocaleString() + '_.xlsx' )
            aLink.click()
        },
    }
})
</script>
<style scoped>
    ::v-deep .el-table__fixed-footer-wrapper tbody td{
        color:blue;
    }
</style>

