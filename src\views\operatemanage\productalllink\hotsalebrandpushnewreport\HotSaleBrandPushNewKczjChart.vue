<template>
    <my-container v-loading="pageLoading" style="height:100%">
        <template #header>
            <div class="top">
                <el-button-group>

                    <el-button style="padding: 0;margin: 0; border: 0; ">
                        <el-select v-model="filter.selectDateType" filterable placeholder="" style="width: 100px" disabled>
                            <el-option label="建编码日期" :value="1" />
                            <el-option label="进货日期" :value="2" />
                            <el-option label="上链接日期" :value="3" />
                            <el-option label="出单日期" :value="4" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                            :clearable="false" style="width: 240px;" :value-format="'yyyy-MM-dd'" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0; border: 0; ">
                        <el-select v-model="filter.showChartType" filterable placeholder="" style="width: 100px" disabled>
                            <el-option label="按天" :value="1" />
                            <el-option label="按月" :value="2" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0; ">
                        <el-input v-model.trim="filter.styleCode" type="text" maxlength="40" clearable
                            placeholder="款式编码" style="width:120px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0; ">
                        <el-input v-model.trim="filter.goodsCode" type="text" maxlength="40" clearable
                            placeholder="商品编码" style="width:120px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0; ">
                        <el-select v-model="filter.pushNewUserIds" clearable filterable placeholder="推荐人" multiple
                            collapse-tags style="width: 150px">
                            <el-option v-for="item in createdUserList" :key="item.createdUserId"
                                :label="item.createdUserName" :value="item.createdUserId" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0; ">
                        <el-select filterable v-model="filter.chooseGroupIds" placeholder="选品组" style="width: 150px" clearable multiple collapse-tags >
                            <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0; ">
                        <el-select filterable v-model="filter.choosePlatform" placeholder="选品人平台" clearable
                        style="width: 120px">
                            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
        
                    <el-button style="padding: 0;margin: 0; border: 0; ">
                        <el-select v-model="filter.chooseUserIds" clearable filterable multiple collapse-tags
                            placeholder="选品人" style="width: 150px">
                            <el-option v-for="item in chooseUserList" :key="item.chooseUserId"
                                :label="item.chooseUserName" :value="item.chooseUserId" />
                        </el-select>
                    </el-button>


                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.chooseUserRoles" clearable filterable placeholder="职位" multiple
                            collapse-tags style="width: 250px">
                            <el-option v-for="item in chooseUserRoleList" :key="item.chooseUserRole"
                                :label="item.chooseUserRole" :value="item.chooseUserRole" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.chooseUserDeptNames" clearable filterable placeholder="架构" multiple
                            collapse-tags style="width: 250px">
                            <el-option v-for="item in chooseUserDeptNameList" :key="item.chooseUserDeptName"
                                :label="item.chooseUserDeptName" :value="item.chooseUserDeptName" />
                        </el-select>
                    </el-button>

                    <el-button type="primary" @click="onSearch()" style="">查询</el-button>
                </el-button-group>

            </div>
        </template>

        <template >
            <div>
                <buschar  ref="buschar1" :analysisData="chartData1.data" :loading="chartData1.loading" v-if="!chartData1.loading"></buschar>
            </div>

            <div style="padding-top: 10px; font-size: 20px; font-weight: bold; text-align:center;">
                资金占比图
            </div>
            <div>
                <pie-chart :pie-data="chartData2.data" :loading="chartData2.loading" />
            </div> 

            <div style="padding-top: 10px; font-size: 20px; font-weight: bold; text-align:center;">
                建编码热力图
            </div>
            <div>
                <hotchar ref="hotchar3" :analysisData="chartData3.data" :loading="chartData3.loading" :analysisParam="chartData3.analysisParam" />
            </div>
        </template>

        <template #footer>
        </template>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import buschar from '@/components/Bus/buschar';
import hotchar from '@/components/Bus/hotchar';
import PieChart from '@/views/admin/homecomponents/PieChart2.vue'
import dayjs from 'dayjs';
import datepicker from '@/views/customerservice/datepicker';
import { pickerOptions,platformlist } from '@/utils/tools';
import { formatTime } from "@/utils";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    GetHotSaleBrandPushNewChooseSearch, GetHotSaleBrandPushNewKczjChart1,GetHotSaleBrandPushNewKczjChart2,GetHotSaleBrandPushNewKczjChart3
} from '@/api/operatemanage/productalllink/alllink'
import {  getDirectorGroupList} from '@/api/operatemanage/base/shop'

export default {
    name: "HotSaleBrandPushNewKczjChart",
    components: {
        MyContainer, datepicker, vxetablebase,buschar,PieChart,hotchar
    },
    data() {
        return {
            auditVisible: false,
            activities: [],
            timeRanges: [],
            that: this,
            pickerOptions,
            filter: {
                timerange: [(dayjs().subtract(1, 'day').format('YYYY-MM') + '-01'), dayjs().subtract(1, 'day').format('YYYY-MM-DD')],
                docStartDate: null,
                docEndDate: null,
                selectDateType: 1,
                showChartType:2,
            },
            listLoading: false,
            pageLoading: false,
            createdUserList: [],
            chooseUserList: [],
            chooseUserRoleList: [],
            chooseUserDeptNameList: [],
            platformlist: platformlist,

            chartData1:{
                data:{},
                loading:false,
            },
            chartData2:{
                data:{},
                loading:false,
            },
            chartData3:{
                data:[],
                loading:false,
                analysisParam:{
                    calendarrange:[]
                },
            }
        }
    },
    async mounted() {
        await this.otherdata();
    },
    computed: {
    },
    methods: {
        async otherdata() {
            let ret = await GetHotSaleBrandPushNewChooseSearch({ type: 5 });
            this.createdUserList = ret.data;

            let ret2 = await GetHotSaleBrandPushNewChooseSearch({ type: 2 });
            this.chooseUserList = ret2.data;

            let ret3 = await GetHotSaleBrandPushNewChooseSearch({ type: 3 });
            this.chooseUserRoleList = ret3.data;

            let ret4 = await GetHotSaleBrandPushNewChooseSearch({ type: 4 });
            this.chooseUserDeptNameList = ret4.data;

            const res2 = await getDirectorGroupList({})
            this.directorGroupList = (res2.data || []);
            
        },
        async onSearch() {
            await this.getChart1();
            await this.getChart2();
            await this.getChart3();
        },
        getParam() {
            if (this.filter.timerange && this.filter.timerange.length == 2) {
                this.filter.docStartDate = this.filter.timerange[0];
                this.filter.docEndDate = this.filter.timerange[1];
            }
            else {
                this.$message({ message: "请先选日期", type: "warning" });
                return false;
            }
            const params = {
                ...this.filter,
            };
            return params;
        },

        async  getChart1(){
            let params=this.getParam();
            if(!params) {
                return;
            }
            this.chartData1.loading=true;
            let res = await GetHotSaleBrandPushNewKczjChart1(params)
            this.chartData1.loading=false;
            this.chartData1.data = res; 

        },
        async  getChart2(){
            let params=this.getParam();
            if(!params) {
                return;
            }
            this.chartData2.loading=true;
            let res = await GetHotSaleBrandPushNewKczjChart2(params)
            this.chartData2.loading=false;
            
            let pieList = res.data.map(f => {
                return {
                    name: f.platformName,
                    value: f.stockAmount,
                    ratio: f.stockAmountRate,
                }
            });
            this.chartData2.data = {
                title: "",
                legend: res.data.map(f => f.platformName),
                pieSeries: pieList
            };
        },
        async  getChart3(){
            let params=this.getParam();
            if(!params) {
                return;
            }
            this.chartData3.analysisParam.calendarrange=this.filter.timerange;
            this.chartData3.analysisParam.visualMapMin=0;
            this.chartData3.analysisParam.visualMapMax=300;
            
            this.chartData3.loading=true;
            let res = await GetHotSaleBrandPushNewKczjChart3(params)
            this.chartData3.loading=false;
            let data3=[]; 
            if(res?.success&&res.data&&res.data.length>0){
                res.data.forEach(f=>{
                    data3.push([f.chartX,f.docCount])
                });
            }
            this.chartData3.data =data3;
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}

::v-deep .el-select__tags-text {
    max-width: 120px;
}
</style>
