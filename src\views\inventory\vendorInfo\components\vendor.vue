<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="ListInfo.nameManufacturer" placeholder="厂家名称" maxlength="50" clearable
          class="publicCss" />
        <el-input v-model.trim="ListInfo.contactPerson" placeholder="联系人" maxlength="20" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.contactInformation" placeholder="联系方式" maxlength="20" clearable
          class="publicCss" />
        <el-input v-model.trim="ListInfo.yhContactPerson" placeholder="对接人" maxlength="20" clearable
          class="publicCss" />

        <!-- <el-input v-model.trim="ListInfo.createdUserName" placeholder="添加人" maxlength="20" clearable
          class="publicCss" /> -->
          <div class="publicCss" style="width: 165px;">
          <inputYunhan ref="productyhGoodsCode" :inputt.sync="ListInfo.createdUserName" v-model="ListInfo.createdUserName"
            width="165px" placeholder="添加人/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'createdUserName')" title="添加人">
          </inputYunhan>
        </div>
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="添加起始时间" end-placeholder="添加结束时间" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-select v-model="ListInfo.isDuiJieDaDanPingtai" placeholder="是否对接打单平台" clearable class="publicCss">
          <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-input v-model.trim="ListInfo.daDanPing1" placeholder="打单平台1" maxlength="100" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.daDanPing2" placeholder="打单平台2" maxlength="100" clearable class="publicCss" />
        <el-select v-model="ListInfo.isAllDuiJieDaDanPingtai" placeholder="是否所有商品对接打单平台" clearable class="publicCss">
          <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-input v-model.trim="ListInfo.housekeeperAccountNumber" placeholder="店管家账号" maxlength="100" clearable
          class="publicCss" />
        <div>
          <el-button class="top_button" type="primary" @click="getList('search')">搜索</el-button>
          <el-button class="top_button" type="primary" @click="handleAdd">新增</el-button>
          <el-button class="top_button" type="primary" @click="importProps">导入</el-button>
          <el-button class="top_button" type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
        </div>
      </div>
    </template>
    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' id="**************"
      @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false" border
      :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="80" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" @click="handleEdit(row)">编辑</el-button>
              <!-- <el-button type="text" @click="onGenerate(row)">生成二维码</el-button> -->
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog :title="isEdit ? '编辑' : '新增'" :visible.sync="logVisible" width="60%" v-dialogDrag
      :close-on-click-modal="false">
      <el-form label-width="200px" :model="form" class="logForm" ref="ruleForm" :rules="rules">
        <el-form-item label="厂家名称:" prop="nameManufacturer">
          <el-input v-model="form.nameManufacturer" clearable maxlength="50" placeholder="请输入厂家名称"></el-input>
        </el-form-item>
        <el-form-item label="联系人:" prop="contactPerson">
          <el-input v-model="form.contactPerson" clearable maxlength="20" placeholder="请输入联系人"></el-input>
        </el-form-item>
        <el-form-item label="联系方式:" prop="contactInformation">
          <el-input v-model="form.contactInformation" clearable maxlength="20" placeholder="请输入联系方式"></el-input>
        </el-form-item>
        <!-- <el-form-item label="密码:">
          <el-input v-model="form.passWord" clearable maxlength="50" placeholder="请输入密码"></el-input>
        </el-form-item> -->
        <el-form-item label="通用系数:">
          <el-input-number v-model="form.universalCoefficient" :min="1" :max="999" :controls="false" :precision="4"
            placeholder="请输入通用系数" style="width: 100%;"></el-input-number>
        </el-form-item>
        <el-form-item label="对接人:">
          <el-input v-model="form.yhContactPerson" clearable maxlength="20" placeholder="请输入对接人" disabled></el-input>
        </el-form-item>
        <el-form-item label="是否对接打单平台:">
          <el-select v-model="form.isDuiJieDaDanPingtai" placeholder="是否对接打单平台" clearable style="width: 100%;"
            @change="changeDuiJieDaDanPingtai">
            <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="打单平台1:" v-if="form.isDuiJieDaDanPingtai == 1" prop="daDanPing1">
          <el-input v-model.trim="form.daDanPing1" placeholder="请输入打单平台1" maxlength="100" clearable />
        </el-form-item>
        <el-form-item label="打单平台2:" v-if="form.isDuiJieDaDanPingtai == 1">
          <el-input v-model.trim="form.daDanPing2" placeholder="请输入打单平台2" maxlength="100" clearable />
        </el-form-item>
        <el-form-item label="是否所有商品对接打单平台:">
          <el-select v-model="form.isAllDuiJieDaDanPingtai" placeholder="是否所有商品对接打单平台" clearable style="width: 100%;">
            <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="店管家账号:">
          <el-input v-model.trim="form.housekeeperAccountNumber" placeholder="请输入店管家账号" maxlength="100" clearable />
        </el-form-item>
        <div style="display: flex;justify-content: center;align-items: center;">
          <el-button @click="logVisible = false">取消</el-button>
          <el-button type="primary" @click="submit" v-throttle="1000">确定</el-button>
        </div>
      </el-form>
    </el-dialog>

    <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
      <div slot="title" class="header-title">
        <span class="title-text"><span>导入数据</span></span>
        <span class="title-close"><el-button @click="downLoadFile">下载模版</el-button></span>
      </div>
      <div style="display: flex;justify-content: space-between;">
        <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
          :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
          <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
            <el-button size="small" type="primary">点击上传</el-button>
          </el-tooltip>
        </el-upload>
      </div>
      <div class="btnGroup">
        <el-button @click="importVisible = false">取消</el-button>
        <el-button type="primary" @click="sumbit" v-throttle="1000">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="生成二维码" :visible.sync="codingVisible" width="30%" v-dialogDrag>
      <div style="display: flex;justify-content: center;align-items: center;height: 150px;">
        <el-form label-width="120px" :model="codingform" class="logForm" ref="codingruleForm" :rules="codingrules"
          v-loading="codingLoading">
          <el-form-item label="二维码张数:" prop="num">
            <el-input-number v-model="codingform.num" :min="1" :max="999" placeholder="请输入二维码张数"
              style="width: 80%;"></el-input-number>
          </el-form-item>
          <div style="display: flex;justify-content: center;align-items: center;margin-top: 30px;">
            <el-button @click="codingVisible = false">取消</el-button>
            <el-button type="primary" @click="codingsubmit" v-throttle="1000">确定</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import {
  ExportAlbbSupplierExhibition,
  GetAlbbSupplierExhibition,
  AddOrEditAlbbSupplierExhibitionAsync,
  ImportAlbbSupplierExhibitionAsync,
  initSampleGoods,
} from '@/api/customerservice/albbinquirs'
const yesOrNoOptions = [
  { label: '是', value: 1 },
  { label: '否', value: 0 },
]
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'nameManufacturer', label: '厂家名称', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'contactPerson', label: '联系人', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'contactInformation', label: '联系方式', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'passWord', label: '随机六位数密码', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'universalCoefficient', label: '通用系数', },
  { width: '90', align: 'center', prop: 'totalSelection', label: '选品总数', },
  { width: '90', align: 'center', prop: 'checkTotalPricePassed', label: '核价通过总数', },
  { width: '90', align: 'center', prop: 'failedTotal', label: '未通过总数', },
  { width: '90', align: 'center', prop: 'totalBacklog', label: '待核总数', },
  { width: '90', align: 'center', prop: 'isDuiJieDaDanPingtai', label: '是否对接打单平台', formatter: (row) => row.isDuiJieDaDanPingtai == 1 ? '是' : row.isDuiJieDaDanPingtai == 0 ? '否' : '', },
  { width: '90', align: 'center', prop: 'daDanPing1', label: '打单平台1', },
  { width: '90', align: 'center', prop: 'daDanPing2', label: '打单平台2', },
  { width: '100', align: 'center', prop: 'isAllDuiJieDaDanPingtai', label: '是否所有商品对接打单平台', formatter: (row) => row.isAllDuiJieDaDanPingtai == 1 ? '是' : row.isAllDuiJieDaDanPingtai == 0 ? '否' : '', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'housekeeperAccountNumber', label: '店管家账号', },
  { width: '100', align: 'center', prop: 'yhContactPerson', label: '对接人', },
  // { sortable: 'custom', width: '100', align: 'center', prop: 'createdUserName', label: '供应商创建人', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'createdTime', label: '添加时间', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'createdUserName', label: '添加人', },
]
export default {
  name: "scanCodePage",
  components: {
    MyContainer, vxetablebase, dateRange,inputYunhan
  },
  data() {
    return {
      yesOrNoOptions,
      codingVisible: false,
      codingform: {
        num: undefined,
        supplierId: null,
      },
      codingrules: {
        num: [
          { required: true, message: '请输入二维码张数', trigger: 'blur' },
        ],
      },
      codingLoading: false,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        createdUserName: null,//供应商创建人
        nameManufacturer: null,//厂家名称
        contactPerson: null,//联系人
        yhContactPerson: null,//对接人
        contactInformation: null,//联系方式
        isDuiJieDaDanPingtai: null,//是否对接打单平台
        daDanPing1: null,//打单平台1
        daDanPing2: null,//打单平台2
        isAllDuiJieDaDanPingtai: null,//是否所有商品对接打单平台
        housekeeperAccountNumber: null,//店管家账号
        createdTimeStart:null,
        createdTimeEnd:null,
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: true,
      pickerOptions,
      isExport: false,
      isEdit: false,
      logVisible: false,
      form: {
        nameManufacturer: null,//厂家名称
        contactPerson: null,//联系人
        contactInformation: null,//联系方式
        passWord: null,//密码
        universalCoefficient: 1,//通用系数
        yhContactPerson: null,//对接人
        isDuiJieDaDanPingtai: null,//是否对接打单平台
        daDanPing1: null,//打单平台1
        daDanPing2: null,//打单平台2
        isAllDuiJieDaDanPingtai: null,//是否所有商品对接打单平台
        housekeeperAccountNumber: null,//店管家账号
      },
      lastProp: {},
      fileList: [],
      importLoading: false,
      importVisible: false,
      file: null,
      rules: {
        nameManufacturer: [
          { required: true, message: '请输入厂家名称', trigger: 'blur' },
          { max: 50, message: '长度在 50 个字符以内', trigger: 'blur' }
        ],
        contactPerson: [
          { required: true, message: '请输入联系人', trigger: 'blur' },
          { max: 20, message: '长度在 20 个字符以内', trigger: 'blur' }
        ],
        contactInformation: [
          { required: true, message: '请输入联系方式', trigger: 'blur' },
          { max: 20, message: '长度在 20 个字符以内', trigger: 'blur' }
        ],
        daDanPing1: [
          { required: true, message: '请输入打单平台1', trigger: 'blur' },
        ],
      }
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    changeDuiJieDaDanPingtai(e) {
      if (e !== 1) {
        this.form.daDanPing1 = null
        this.form.daDanPing2 = null
      }
    },
    codingsubmit() {
      this.$refs.codingruleForm.validate(async (valid) => {
        if (valid) {
          const { success } = await initSampleGoods(this.codingform)
          if (success) {
            this.$message.success('生成成功')
            this.codingVisible = false
            this.getList()
          } else {
            this.$message.error('生成失败')
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    callbackGoodsCode(val, type) {
      const map = {
        createdUserName: () => (this.ListInfo.createdUserName = val),
      };
      map[type]?.();
    },
    async changeTime(e) {
      this.ListInfo.createdTimeStart = e ? e[0] : null
      this.ListInfo.createdTimeEnd = e ? e[1] : null
    },
    async onGenerate(row) {
      this.codingform.supplierId = row.id
      this.codingform.num = undefined
      this.codingVisible = true
    },
    downLoadFile() {
      window.open("../../../static/excel/1688选品中心供应商导入模版.xlsx", "_self");
    },
    async uploadFile(data) {
      this.file = data.file
    },
    async sumbit() {
      //没有时间就提示
      if (this.file == null) return this.$message.error('请上传文件')
      this.$message.info('正在导入中,请稍后...')
      const form = new FormData();
      form.append("upfile", this.file);
      this.importLoading = true
      await ImportAlbbSupplierExhibitionAsync(form).then(({ success }) => {
        if (success) {
          this.$message.success('导入成功')
          this.importVisible = false
          this.getList()
        }
        this.importLoading = false
      }).catch(err => {
        this.importLoading = false
        this.$message.error('导入失败')
      })
    },
    importProps() {
      this.fileList = []
      this.file = null
      this.importVisible = true
    },
    removeFile(file, fileList) {
      this.file = null
    },
    submit() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          if (this.isEdit == true) {
            const map = {
              nameManufacturer: '厂家名称',
              contactPerson: '联系人',
              contactInformation: '联系方式',
            }
            let message = ''
            for (const key in map) {
              if (this.form[key] != this.lastProp[key]) {
                message += `原${map[key]}为${this.lastProp[key]},是否修改为${this.form[key]}?`
              }
            }
            if (message) {
              this.$confirm(`${message}`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(async () => {
                await this.handleSave()
              }).catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消!'
                });
              });
            } else {
              await this.handleSave()
            }
          } else {
            await this.handleSave()
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },
    async handleSave() {
      const { success } = await AddOrEditAlbbSupplierExhibitionAsync(this.form)
      if (!success) return
      this.$message.success('保存成功')
      this.logVisible = false
      this.getList()
    },
    handleEdit(row) {
      this.isEdit = true
      this.lastProp = JSON.parse(JSON.stringify(row))
      this.form = JSON.parse(JSON.stringify(row))
      this.form.universalCoefficient = this.form.universalCoefficient ? this.form.universalCoefficient : 1
      this.logVisible = true
      this.$nextTick(() => {
        this.$refs.ruleForm.clearValidate()
      })
    },
    handleAdd() {
      this.isEdit = false
      this.form = {
        nameManufacturer: null,
        contactPerson: null,
        contactInformation: null,
        passWord: null,
        universalCoefficient: 1,
        yhContactPerson: null,
      }
      this.logVisible = true
      this.$nextTick(() => {
        this.$refs.ruleForm.clearValidate()
      })
    },
    //导出数据,使用时将下面的方法替换成自己的接口
    async exportProps() {
      this.isExport = true
      await ExportAlbbSupplierExhibition(this.ListInfo).then(({ data }) => {
        if (data) {
          const aLink = document.createElement("a");
          let blob = new Blob([data], { type: "application/vnd.ms-excel" })
          aLink.href = URL.createObjectURL(blob)
          aLink.setAttribute('download', '1688选品中心' + new Date().toLocaleString() + '.xlsx')
          aLink.click()
          this.isExport = false
        }
      }).catch(() => {
        this.isExport = false
      })
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      // 使用时将下面的方法替换成自己的接口
      try {
        const { data, success } = await GetAlbbSupplierExhibition(this.ListInfo)
        if (success) {
          this.tableData = data.list
          this.total = data.total
          this.loading = false
        } else {
          //获取列表失败
          this.loading = false
          this.$message.error('获取列表失败')
        }
      } catch (error) {
        this.loading = false
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 5px;

  .publicCss {
    width: 140px;
    margin: 0 5px 5px 0px;
  }
}

.btnGroup {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px 0 0;

  .title-text {
    display: flex;
    align-items: center;

    .title-close {
      margin-left: 10px;
    }
  }
}

.logForm ::v-deep .el-input__inner {
  text-align: left;
}

::v-deep(.el-button.top_button + .el-button.top_button) {
  margin-left: 1px;
}
</style>
