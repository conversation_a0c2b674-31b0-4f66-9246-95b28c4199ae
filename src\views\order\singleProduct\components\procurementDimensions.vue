<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 94%">
            <el-tab-pane label="云仓" name="first" style="height: 100%" lazy>
                <cloudOrMinewarehouse />
            </el-tab-pane>
            <el-tab-pane label="品牌仓" name="Eleven" style="height: 100%" lazy>
                <brandWarehouse />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>
<script>
import MyContainer from "@/components/my-container";
import cloudOrMinewarehouse from "./cloudOrMinewarehouse.vue";
import brandWarehouse from "./brandWarehouse.vue";
export default {
    components: {
        MyContainer,
        cloudOrMinewarehouse,
        brandWarehouse
    },
    data() {
        return {
            activeName: 'first',
        };
    },
}
</script>