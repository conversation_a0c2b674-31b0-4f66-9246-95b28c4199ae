<template>
    <div>
        <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
            <el-select v-model="ListInfo.type" @change="changetype" placeholder="类型" class="publicCss" clearable>
                <el-option v-for="item in typeList" :key="item" :label="item" :value="item" />
            </el-select>
            <el-select v-model="ListInfo.regionName" placeholder="片区" class="publicCss" clearable collapse-tags>
                <el-option v-for="item in districtList" :key="item" :label="item" :value="item" />
            </el-select>
            <el-select :disabled="ListInfo.type == '仓储' || ListInfo.blockType == 'RecruitDirector'" v-model="ListInfo.deptType" @change="getdeptName" placeholder="部门类型" class="publicCss" clearable collapse-tags>
            <el-option v-for="item in sectionList" :key="item" :label="item" :value="item" />
            </el-select>
            <el-select :disabled="ListInfo.type == '仓储' || ListInfo.blockType == 'RecruitDirector'" v-model="ListInfo.deptName" placeholder="部门" class="publicCss" clearable collapse-tags>
                <el-option v-for="item in sectionList2" :key="item" :label="item" :value="item" />
            </el-select>
            <el-select v-model="ListInfo.blockType" @change="changegetban" placeholder="同比板块" class="publicCss" clearable collapse-tags>
                <el-option v-for="item in sectionList3" :key="item.blockCode" :label="item.blockName" :value="item.blockCode" />
            </el-select>
            <el-select v-model="ListInfo.dataName" @change="changegetdata" placeholder="同比数据" class="publicCss" clearable collapse-tags>
                <el-option v-for="item in sectionList4" :key="item.blockCode" :label="item.blockName" :value="item.blockCode" />
            </el-select>
            <el-select style="width: 90px" v-model="ListInfo.cycle1" placeholder="同比周期" @change="changTimeType(1)" class="publicCss" :clearable="false" collapse-tags>
                <el-option  key="月" label="月" value="月" />
                <el-option  key="季度" label="季度" value="季度" />
                <el-option v-if="trendChartType == 'one'"  key="年" label="年" value="年" />
            </el-select>

            <!-- <jiduTime v-if="ListInfo.cycle1 == '季度'" v-model="ListInfo.calculateMonthArr"></jiduTime> -->
            <el-date-picker
            v-show="ListInfo.cycle1 == '年'?true:false"
            v-model="ListInfo.calculateMonthArryear"
            type="year"
            placeholder="选择年"
            :picker-options="pickerOptions"
            :value-format="'yyyy'">
            </el-date-picker>
            <!-- <yearTime v-if="ListInfo.cycle1 == '年'" @changetime="changetimeyear($event,1)"></yearTime> -->
            <jiduTime v-show="ListInfo.cycle1 == '季度'?true:false" v-model="ListInfo.calculateMonthArrjidu"></jiduTime>
            <!-- v-if="ListInfo.cycle1 == '季度'||ListInfo.cycle1 == '月'" -->
            <el-date-picker v-show="ListInfo.cycle1 == '月'?true:false" v-model="ListInfo.calculateMonthArr" unlink-panels range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" type="monthrange" style="width: 250px;margin-right: 5px;" :clearable="true"
              :value-format="'yyyy-MM'" >
            </el-date-picker>



            <el-select style="width: 90px" v-model="ListInfo.cycle2" placeholder="同比周期" @change="changTimeType(2)"  class="publicCss" :clearable="false" collapse-tags>
                <!-- <el-option v-for="item in sectionList5" :key="item" :label="item" :value="item" /> -->
                <el-option  key="月" label="月" value="月" />
                <el-option  key="季度" label="季度" value="季度" />
                <el-option v-if="trendChartType == 'one'"  key="年" label="年" value="年" />
            </el-select>

            <el-date-picker
            v-show="ListInfo.cycle2 == '年'?true:false"
            v-model="ListInfo.calculateMonthArryear2"
            type="year"
            :value-format="'yyyy'"
            :picker-options="pickerOptions"
            placeholder="选择年">
            </el-date-picker>
            <!-- <yearTime v-if="ListInfo.cycle1 == '年'" @changetime="changetimeyear($event,2)"></yearTime> -->
            <jiduTime v-show="ListInfo.cycle2 == '季度'?true:false" v-model="ListInfo.calculateMonthArrjidu2"></jiduTime>
            <!-- v-if="ListInfo.cycle2 == '季度'||ListInfo.cycle2 == '月'" -->
            <el-date-picker v-show="ListInfo.cycle2 == '月'?true:false" v-model="ListInfo.calculateMonthArr2" unlink-panels range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期"   type="monthrange" style="width: 250px;margin-right: 5px;" :clearable="true"
              :value-format="'yyyy-MM'" >
            </el-date-picker>


            <el-button type="primary" @click="getList('search')">查询</el-button>
        </div>
        <div style="height: 300px; width: 100%; border: 1px solid #ccc; margin-top: 10px;">
            <div class="chart-title">
              <h3>人事同比数据展示</h3>
            </div>
            <newcharbus v-if="oneCharts" :toolbox="{
                show: true,
                orient: 'vertical',
                left: 'right',
                top: 'center',
                feature: {
                    magicType: { show: true, type: ['line', 'bar', 'stack'] },
                    saveAsImage: { show: true }
                    }
                }" ref="sptsxformchTyright"
                :thisStyle="{
                  width: '100%', height: '275px', 'box-sizing': 'border-box', 'line-height': '300px'
                }" :analysisData="oneCharts">
              </newcharbus>
        </div>


    </div>
</template>

<script>
import newcharbus from "./newcharbus.vue";
import jiduTime from "./jiduTime.vue";
import yearTime from "./yearTime.vue";
import dayjs from 'dayjs'


import {
    trendChart, monthArchivesAnalysisListValue,
    blockList, getDataListByBlock, getDeptList
  } from '@/api/people/peoplessc.js';
import { set } from 'vue';
    export default {
        components: {
            newcharbus,jiduTime, yearTime
        },
        props: {
            editInfo: {
                type: Object,
                default: () => {
                    return {}
                }
            },
            ListInfopro: {
                type: Object,
                default: () => {
                    return {}
                }
            },
            trendChartType: {
                type: String,
                default: 'one'
            }
        },
        data() {
            return {
                pickerOptions: {
                    // disabledDate (time) {
                    // return (
                    //     time.getFullYear() < '2050' ||
                    //     time.getFullYear() > new Date().getFullYear()
                    // ) // 注意是 || 不是 && 哦
                    // }
                },
                warehouseName: '',
                ListInfo: {
                    cycle1: '月',
                    cycle2: '月',

                    type: '',
                    dataName: '',
                    twoDeptName: '',
                    deptName: '',
                    deptType: '',
                    regionName: '南昌',
                    calculateMonthArr: [],
                    calculateMonthArr2: [],

                    calculateMonthArryear: null,
                    calculateMonthArryear2: null,

                    calculateMonthArrjidu: null,
                    calculateMonthArrjidu2: null,

                },
                sectionList: [
                    "业务部门",
                    "职能部门",
                    "项目部",
                ],
                sectionList3: [],
                sectionList4: [],
                sectionList5: [
                    "月",'季度',"年"
                ],
                // "季度",
                charbusdata: {},
                oneCharts: null,

            }
        },
        async mounted() {

            if(this.trendChartType == 'one'){
                // this.ListInfo.blockType = "岗位职级分布";
                // this.ListInfo.dataName = "总人数";
                this.ListInfo.blockType = "AllRegionPerson";
                this.ListInfo.dataName = "total";
                this.ListInfo.cycle1 = "月";
                this.ListInfo.cycle2 = "月";
                this.ListInfo.calculateMonthArr = [
                    dayjs().subtract(1, 'month').format('YYYY-MM'),
                    dayjs().subtract(1, 'month').format('YYYY-MM')];
                this.ListInfo.calculateMonthArr2 = [
                    dayjs().subtract(13, 'month').format('YYYY-MM'),
                    dayjs().subtract(13, 'month').format('YYYY-MM')
                ];


            }else{
                // this.ListInfo.blockType = "招聘数据汇总";
                // this.ListInfo.dataName = "入职人数";

                this.ListInfo.blockType = "RecruitDirector";
                this.ListInfo.dataName = "new_hires_count";

                this.ListInfo.cycle1 = "月";
                this.ListInfo.cycle2 = "月";
                this.ListInfo.calculateMonthArr = [
                    dayjs().subtract(1, 'month').format('YYYY-MM'),
                    dayjs().subtract(1, 'month').format('YYYY-MM')];
                this.ListInfo.calculateMonthArr2 = [
                    dayjs().subtract(2, 'month').format('YYYY-MM'),
                    dayjs().subtract(2, 'month').format('YYYY-MM')
                ];
            }

            this.getxialatwo('sectionList4', {
                'blockType': this.ListInfo.blockType
            }, getDataListByBlock);

            console.log("查询")
            await this.getxiala('typeList', 'type');
            // await this.getxiala('districtList', 'regionName');
            this.districtList = [
                        '南昌',
                        '义乌',
                        '深圳',
                        '武汉',
                        '1688选品中心',
                    ]
            // await this.getxiala('sectionList', 'deptType');
            // this.getxiala('sectionList2', 'deptName');
            await this.getxiala('sectionList3', 'twoDeptName');

            await this.getxialatwo('sectionList3', {}, blockList);

            await setTimeout(async() => {
                await this.getList();
            }, 2000);





        },
        methods: {
            changetype(){
                if(this.ListInfo.type == '仓储'){
                    this.ListInfo.regionName = '';
                    this.districtList = [
                        '义乌仓',
                        '西安仓',
                        '南昌仓',
                    ]

                    this.sectionList = [];
                    this.sectionList2 = [];
                    this.ListInfo.deptName = "";
                    this.ListInfo.deptType = "";
                }else{
                    this.sectionList = [
                        "业务部门",
                        "职能部门",
                        "项目部",
                    ];
                    // this.getxiala('districtList', 'regionName');
                    this.districtList = [
                        '南昌',
                        '义乌',
                        '深圳',
                        '武汉',
                        '1688选品中心',
                    ]
                }
            },
            changetimeyear(val, type){
                if(type == 1){
                    this.ListInfo.startTime1 = val[0];
                    this.ListInfo.endTime1 = val[1];
                    this.ListInfo.calculateMonthArryear = val;

                }else{
                    this.ListInfo.startTime2 = val[0];
                    this.ListInfo.endTime2 = val[1];
                    this.ListInfo.calculateMonthArryear2 = val;

                }
                console.log("打印数据shijian", this.ListInfo)
            },
            changegetdata(val){
                console.log("打印数据", this.sectionList4.filter(item => item.blockCode == val))
                this.ListInfo.dataType = this.sectionList4.filter(item => item.blockCode == val)[0]['blockName'];
            },
            async getdeptName(val){
                this.ListInfo.deptName = "";
                let res = await getDeptList({deptType: val});
                if(!res.success){
                    return;
                }
                this.sectionList2 = res.data;
                this.$forceUpdate();
                return  res.data;
            },
            async getdeptNametwo(val){
                this.ListInfo.deptName = "";
                let res = await getDeptList({deptType: val});
                if(!res.success){
                    return;
                }
                this.sectionList2 = res.data;
                this.$forceUpdate();
                return  res.data;
            },
            changTimeType(val){
                this.ListInfo.startTime1 = null;
                this.ListInfo.startTime2 = null;

                this.ListInfo.endTime1 = null;
                this.ListInfo.endTime2 = null;

                if(this.ListInfo.cycle1 == '季度'){
                    this.ListInfo.calculateMonthArr = [];
                    this.ListInfo.calculateMonthArr2 = [];

                    this.ListInfo.calculateMonthArryear2 = null;
                    this.ListInfo.calculateMonthArryear = null;
                }else if(this.ListInfo.cycle1 == '年') {
                    this.ListInfo.calculateMonthArrjidu2 = null;
                    this.ListInfo.calculateMonthArrjidu = null;

                    this.ListInfo.calculateMonthArr = [];
                    this.ListInfo.calculateMonthArr2 = [];

                }else{
                    this.ListInfo.calculateMonthArryear2 = null;
                    this.ListInfo.calculateMonthArryear = null;

                    this.ListInfo.calculateMonthArrjidu2 = null;
                    this.ListInfo.calculateMonthArrjidu = null;

                }


                if(val == 1){
                    this.ListInfo.cycle2 = this.ListInfo.cycle1
                }else if(val == 2){
                    this.ListInfo.cycle1 = this.ListInfo.cycle2
                }
                // this.ListInfo.calculateMonthArr = null;
            },
            changegetban(){
                this.ListInfo.dataName = "";
                this.getxialatwo('sectionList4', {
                    'blockType': this.ListInfo.blockType
                }, getDataListByBlock);
                if (this.ListInfo.blockType == 'RecruitDirector') {
                    this.ListInfo.deptType = null;
                    this.ListInfo.deptName = null;
                }
            },
            async getxiala(that,val){
                let res = await monthArchivesAnalysisListValue({
                    'fieldName': val
                })
                if(!res.success){
                    return
                }
                this[that] = res.data;
                this.ListInfo[val] = res.data[0];
                setTimeout(async()=>{
                    if(this.ListInfo.deptType){
                        let res = await this.getdeptNametwo(this.ListInfo.deptType)
                        this.ListInfo.deptName = res[0];
                    }
                }, 1000)
            },
            async getxialatwo(that,obj, fuc){
                let res = await fuc(obj)
                if(!res.success){
                    return
                }
                this[that] = res.data;
                this.$forceUpdate();
            },
            getList(){
                this.getoneCharts();
            },
            getQuarterRange(quarterStr) {
                // 检查输入格式
                if (!/^\d{4}-\d{2}$/.test(quarterStr)) {
                    throw new Error('输入格式错误，应为 YYYY-QQ 格式');
                }

                const [year, quarter] = quarterStr.split('-');

                // 将季度转换为月份
                const startMonth = (parseInt(quarter) - 1) * 3 + 1;
                const endMonth = startMonth + 2;

                // 格式化月份，确保是两位数
                const formatMonth = (month) => month.toString().padStart(2, '0');

                return [
                    `${year}-${formatMonth(startMonth)}`,
                    `${year}-${formatMonth(endMonth)}`
                ];
            },
            async getoneCharts(){
                this.oneCharts = null;
                //月
                if (this.ListInfo.calculateMonthArr && this.ListInfo.calculateMonthArr.length > 0) {
                    this.ListInfo.startTime1 = this.ListInfo.calculateMonthArr[0]
                    this.ListInfo.endTime1 = this.ListInfo.calculateMonthArr[1]
                }else if(this.ListInfo.calculateMonthArryear){
                    this.ListInfo.startTime1 = this.ListInfo.calculateMonthArryear+'-01';
                    this.ListInfo.endTime1 = this.ListInfo.calculateMonthArryear+'-12';
                }else if(this.ListInfo.calculateMonthArrjidu){
                    let newarr = this.getQuarterRange(this.ListInfo.calculateMonthArrjidu);
                    this.ListInfo.startTime1 = newarr[0];
                    this.ListInfo.endTime1 = newarr[1];
                }
                // else{
                //     this.ListInfo.startTime1 = null
                //     this.ListInfo.endTime1 = null
                // }
                if (this.ListInfo.calculateMonthArr2 && this.ListInfo.calculateMonthArr2.length > 0) {
                    this.ListInfo.startTime2 = this.ListInfo.calculateMonthArr2[0]
                    this.ListInfo.endTime2 = this.ListInfo.calculateMonthArr2[1]
                }else if(this.ListInfo.calculateMonthArryear2){
                    this.ListInfo.startTime2 = this.ListInfo.calculateMonthArryear2+'-01';
                    this.ListInfo.endTime2 = this.ListInfo.calculateMonthArryear2+'-12';
                }else if(this.ListInfo.calculateMonthArrjidu2){
                    let newarr = this.getQuarterRange(this.ListInfo.calculateMonthArrjidu2);
                    this.ListInfo.startTime2 = newarr[0];
                    this.ListInfo.endTime2 = newarr[1];
                }


                // else{
                //     this.ListInfo.startTime2 = null
                //     this.ListInfo.endTime2 = null
                // }
                //年
                // if (this.ListInfo.calculateMonthArryear && this.ListInfo.calculateMonthArryear.length > 0) {
                //     this.ListInfo.startTime1 = this.ListInfo.calculateMonthArryear[0]
                //     this.ListInfo.endTime1 = this.ListInfo.calculateMonthArryear[1]
                // }else{
                //     this.ListInfo.startTime1 = null
                //     this.ListInfo.endTime1 = null
                // }
                // if (this.ListInfo.calculateMonthArryear2 && this.ListInfo.calculateMonthArryear2.length > 0) {
                //     this.ListInfo.startTime2 = this.ListInfo.calculateMonthArryear2[0]
                //     this.ListInfo.endTime2 = this.ListInfo.calculateMonthArryear2[1]
                // }else{
                //     this.ListInfo.startTime2 = null
                //     this.ListInfo.endTime2 = null
                // }
                let params = {
                    ...this.ListInfo,
                    trendChartType: this.trendChartType
                }
                let res = await trendChart(params);
                if(!res.success){
                    return;
                }
                let newobj = JSON.parse(JSON.stringify(res.data));
                newobj.xAxis = {
                    type: 'value',
                    name: '总人数',
                    axisLabel: {
                        formatter: '{value}'
                    }
                };
                newobj.legend = {
                    data: res.data.legend
                };
                newobj.yAxis = {
                    type: 'category',
                    inverse: true,
                    data: res.data.xAxis,
                };

                if(newobj.series&&newobj.series.length>0){
                    newobj.series.map((item)=>{
                        item.label = {
                            show: true
                        }
                    })
                }
                // this.oneCharts = res.data;
                this.oneCharts = newobj;
                console.log(newobj.legend,'1=======');
                console.log(res.data,'2=======');


                console.log(newobj,'3=======');
            }
        }
    }
</script>

<style scoped lang="scss">
.chart-title {
  text-align: center;
  padding: 15px 0;
  background: linear-gradient(135deg, #00937e 0%, #00b894 100%);
  margin-bottom: 10px;
  border-radius: 8px 8px 0 0;
  position: relative;
  overflow: hidden;
}

.chart-title::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  animation: shimmer 2s infinite;
}

.chart-title h3 {
  margin: 0;
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  letter-spacing: 1px;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
</style>
