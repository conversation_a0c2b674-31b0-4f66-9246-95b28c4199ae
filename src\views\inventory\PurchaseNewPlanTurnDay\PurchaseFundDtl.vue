<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <div style="height: 40px; margin-top: 10px;">
                <span style="margin-right:0.4%;">
                    <el-date-picker style="width: 200px" v-model="filter.yearMonthDay" type="date" placeholder="选择日期"
                        :clearable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                    </el-date-picker>
                </span>
                <span style="margin-right:0.4%;">
                    <el-select style="width:160px" clearable filterable remote reserve-keyword placeholder="分组"
                        :remote-method="getGroupList" :loading="groupListLoading" v-model.trim="filter.groupName"
                        :collapse-tags="true">
                        <el-option v-for="(item, i) in groupList" :key="'groupName' + i + 1" :label="item"
                            :value="item" />
                    </el-select>
                </span>
                <span>
                    <el-select style="width:160px" placeholder="类别" v-model.trim="filter.hourType"
                        :collapse-tags="true">
                        <el-option label="上午" value="上午"></el-option>
                        <el-option label="下午" value="下午"></el-option>
                        <el-option label="晚上" value="晚上"></el-option>
                    </el-select>
                </span>
                <span style="margin-left:5px;">
                    <el-button type="primary" @click="getList" style="width: 70px;">搜索</el-button>
                </span>
                <span style="margin-left:5px;">
                    <el-button type="primary" @click="onExport" style="width: 70px;">导出</el-button>
                </span>
                <span style="margin-left:5px;" v-if="checkPermission('purchaseNewInventoryFundDtlSet')">
                    <el-button type="primary" @click="onAddSet" style="width: 70px;">配置</el-button>
                </span>
                <span style="margin-left:5px;" v-if="checkPermission('purchaseNewInventoryFundDtlImport')">
                    <el-button type="primary" @click="onImport" style="width: 70px;">导入</el-button>
                </span>
                <span style="margin-left: 5px;color:red;font-size: 12px;">
                    <!-- <el-button type="text"> -->
                        最近导入时间:{{ lastImportTime }}
                    <!-- </el-button> -->
                </span>
            </div>
        </template>
        <vxetablebase :id="'purchaseFundDtl20241227'" ref="tabulartable" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' @sortchange='sortChange' :showsummary="true" :summaryarry='summaryarry'
            :tableData='dataList' :tableCols='tableCols' :isSelection="false" :loading="listLoading"
            :isSelectColumn="false" style="width: 100%;margin: 0" @summaryClick='onsummaryClick'>
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="dataTotal" @page-change="pagechange" @size-change="sizechange" />
        </template>
        <el-dialog title="设置" :visible.sync="dialogVisible" width="35%" v-dialogDrag :close-on-click-modal="false">
            <div style="height: 100px;">
                <el-form>
                    <el-row>
                        <el-col>
                            <el-select style="width:40%" clearable placeholder="请选择" v-model.trim="dialogGroupName"
                                :collapse-tags="true" filterable>
                                <el-option v-for="(item, i) in dialogGroupList" :key="'dialogGroupName' + i + 1"
                                    :label="item" :value="item" />
                            </el-select>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col style="margin-top: 10px;">
                            <el-button type="primary" @click="onAddGroupName">添加</el-button>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col>
                            <div style="border: 1px solid #dcdfe6;border-radius: 5px;height: 150px;margin: 10px 0;">
                                <el-scrollbar style="height: 100%;">
                                    <el-tag v-for="(item, i) in dialogGroupData" :key="item" closable
                                        style="margin: 5px;" @close="handleClose(item, i)">
                                        {{ item }}
                                    </el-tag>
                                </el-scrollbar>
                            </div>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false"
            direction="btt">
            <div v-if="!chatProp.chatLoading">
                <div style="width:220px;">
                    <dateRange :startDate.sync="chatProp.startDate" :endDate.sync="chatProp.endDate"
                        @change="changeChart" :clearable="false" />
                </div>
                <buschar v-if="!chatProp.chatLoading" :analysis-data="chatProp.data" />
            </div>
            <div v-else v-loading="chatProp.chatLoading" />
        </el-drawer>

        <el-dialog title="导入" :visible.sync="importDialogVisible" width="35%" v-dialogDrag>
            <div style="min-height: 100px;">
                <el-date-picker style="width:200px;" v-model="importParam.yearMonthDay" type="date" placeholder="选择日期"
                    :clearable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                </el-date-picker>
                <el-select style="width:160px;margin-left:10px;" clearable placeholder="请选择时间" v-model.trim="importParam.hourType"
                    :collapse-tags="true" filterable>
                    <el-option key="上午" label="上午" value="上午"></el-option>
                    <el-option key="下午" label="下午" value="下午"></el-option>
                    <el-option key="晚上" label="晚上" value="晚上"></el-option>
                </el-select>
                <el-upload style="margin-top:10px;" ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action=""
                    accept=".xlsx" :file-list="fileList" :http-request="onUploadFile" :on-success="onUploadSuccess"
                    :on-change="onUploadChange" :on-remove="onUploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary" style="width: 90px;">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px;width: 90px;" size="small" type="success" :loading="uploadLoading"
                        @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                </el-upload>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="importDialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import { getOtherBrandDataDropDown, getOtherBrandDataSet, saveOtherBrandDataSet, delOtherBrandDataSet, getPurchaseFundDtlPage, getQueryGroupList, exportPurchaseFundDtl, getLastImportTime, getPurchaseFundDtlAnalysis } from '@/api/inventory/purchaseordernew'
import { formatTime } from "@/utils/tools";
import { importPurchaseTotalInventoryFunds } from '@/api/inventory/importPurchaseordernew'
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import dayjs from "dayjs";
import buschar from "@/components/Bus/buschar";
import dateRange from "@/components/date-range/index.vue";
const arr = ['采购1组', '采购2组', '采购3组', '采购4组', '采购5组', '采购行政组', '采购一组', '采购二组', '跨境采购组', '首力项目组', '大马采购组', '测试组', '采购组']
const arr1 = ['滞销', '其他', '☆耗材包材',]
const arr2 = ['★清理滞销-不进货', '★待下架-处理', '★已下架-禁用', '★季节款-隔年卖-不进货', '★卖完下架-不进货']
const tableCols = [
    { istrue: true, prop: 'yearMonthDay', label: '日期', width: 'auto', sortable: 'custom', treeNode: true },
    { istrue: true, prop: 'purDept', label: '分组', width: 'auto', sortable: 'custom' },
    {
        istrue: true, prop: 'monthSellStockAllCost', label: '实际库存资金', width: 'auto', sortable: 'custom', type: 'colorClick', handle: (that, row) => that.openChart(row), style: (that, row) => {
            if ((arr.includes(row.purDept) && row.children && row.children.length > 0) || arr1.includes(row.pPurDept)) {
                return { color: '#409eef' }
            } else {
                if (row.pPurDept == '首力项目组' && arr2.includes(row.purDept)) {
                    return { color: '#409eef' }
                }
                return { color: '#606266', cursor: 'default' }
            }
        }, summaryEvent: true
    },
    {
        istrue: true, prop: 'purchaseInTransitQuantityFund', label: '采购在途资金', width: 'auto', sortable: 'custom', type: 'colorClick', handle: (that, row) => that.openChart(row), style: (that, row) => {
            if ((arr.includes(row.purDept) && row.children && row.children.length > 0) || arr1.includes(row.pPurDept)) {
                return { color: '#409eef' }
            } else {
                if (row.pPurDept == '首力项目组' && arr2.includes(row.purDept)) {
                    return { color: '#409eef' }
                }
                return { color: '#606266', cursor: 'default' }
            }
        }, summaryEvent: true
    },
    {
        istrue: true, prop: 'transferInTransitQuantityFund', label: '调拨在途资金', width: 'auto', sortable: 'custom', type: 'colorClick', handle: (that, row) => that.openChart(row), style: (that, row) => {
            if ((arr.includes(row.purDept) && row.children && row.children.length > 0) || arr1.includes(row.pPurDept)) {
                return { color: '#409eef' }
            } else {
                if (row.pPurDept == '首力项目组' && arr2.includes(row.purDept)) {
                    return { color: '#409eef' }
                }
                return { color: '#606266', cursor: 'default' }
            }
        }, summaryEvent: true
    },
    {
        istrue: true, prop: 'purchaseWarehouseStockFund', label: '进货仓资金', width: 'auto', sortable: 'custom', type: 'colorClick', handle: (that, row) => that.openChart(row), style: (that, row) => {
            if ((arr.includes(row.purDept) && row.children && row.children.length > 0) || arr1.includes(row.pPurDept)) {
                return { color: '#409eef' }
            } else {
                if (row.pPurDept == '首力项目组' && arr2.includes(row.purDept)) {
                    return { color: '#409eef' }
                }
                return { color: '#606266', cursor: 'default' }
            }
        }, summaryEvent: true
    },
    {
        istrue: true, prop: 'totalFund', label: '合计金额', width: 'auto', sortable: 'custom', type: 'colorClick', handle: (that, row) => that.openChart(row), style: (that, row) => {
            if ((arr.includes(row.purDept) && row.children && row.children.length > 0) || arr1.includes(row.pPurDept)) {
                return { color: '#409eef' }
            } else {
                if (row.pPurDept == '首力项目组' && arr2.includes(row.purDept)) {
                    return { color: '#409eef' }
                }
                return { color: '#606266', cursor: 'default' }
            }
        }, summaryEvent: true
    },
    {
        istrue: true, prop: 'totalFundDiff', label: '合计金额差值', width: 'auto',  sortable: 'custom',type: 'colorClick', handle: (that, row) => that.openChart(row), style: (that, row) => {
            if ((arr.includes(row.purDept) && row.children && row.children.length > 0) || arr1.includes(row.pPurDept)) {
                return { color: '#409eef' }
            } else {
                if (row.pPurDept == '首力项目组' && arr2.includes(row.purDept)) {
                    return { color: '#409eef' }
                }
                return { color: '#606266', cursor: 'default' }
            }
        }, summaryEvent: true
    },
];
export default {
    name: "Users",
    components: { MyContainer, vxetablebase, buschar, dateRange },
    data() {
        return {
            that: this,
            filter: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: true,
                yearMonthDay: '',
                groupName: null,
                hourType: '上午'
            },
            listLoading: false,
            pageLoading: false,
            groupListLoading: false,
            dataList: [],
            dataTotal: 0,
            groupList: [],
            summaryarry: {},
            tableCols: tableCols,
            dialogVisible: false,
            dialogGroupName: null,
            dialogGroupList: [],
            dialogGroupData: [],
            lastImportTime: '',
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                startDate: null, // 趋势图开始时间
                endDate: null, // 趋势图结束时间
                chatLoading: true, // 趋势图loading
                purDept: null, // 趋势图分组
                hourType: null, // 趋势图时间段
                level: null,// 层级
                ppPurDept: null,// 父级分组
                data: [], // 趋势图数据
            },
            arr,
            arr1,
            arr2,
            importDialogVisible: false,
            importParam: {
                yearMonthDay: '',
                hourType: ''
            },
            uploadLoading: false,
            fileList: []
        };
    },
    async mounted() {
        let today = new Date();
        this.filter.yearMonthDay = formatTime(today, "YYYY-MM-DD")
        await this.getLastImportTime();
        this.getGroupList('');
        await this.getList();
    },
    methods: {
        onsummaryClick() {
            this.chatProp.startDate = dayjs(this.filter.yearMonthDay).subtract(1, 'month').format('YYYY-MM-DD')
            this.chatProp.endDate = dayjs(this.filter.yearMonthDay).format('YYYY-MM-DD')
            this.chatProp.purDept = null
            this.chatProp.ppPurDept = null
            this.chatProp.level = null
            this.chatProp.hourType = this.filter.hourType;
            this.chatProp.groupName = this.filter.groupName;
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = true
            this.changeChart()
        },
        async changeChart(e) {
            this.chatProp.chatLoading = true;
            const { data, success } = await getPurchaseFundDtlAnalysis(this.chatProp);
            if (!success) return
            this.chatProp.data = data;
            this.chatProp.chatLoading = false;
        },
        async openChart1(row) {
            this.chatProp.startDate = dayjs(this.filter.yearMonthDay).subtract(1, 'month').format('YYYY-MM-DD'),
                this.chatProp.endDate = dayjs(this.filter.yearMonthDay).format('YYYY-MM-DD'),
                this.chatProp.purDept = row.purDept;
            this.chatProp.ppPurDept = row.ppPurDept;
            this.chatProp.level = row.level;
            this.chatProp.hourType = this.filter.hourType;
            this.chatProp.groupName = this.filter.groupName;
            this.chatProp.chatDialog = true;
            this.chatProp.chatLoading = true;
            const { data, success } = await getPurchaseFundDtlAnalysis(this.chatProp);
            if (!success) return
            this.chatProp.data = data;
            this.chatProp.chatLoading = false;
        },
        openChart(row) {
            if (
                (row.pPurDept == '首力项目组' && this.arr2.includes(row.purDept) && row.level !== 0) ||
                (this.arr.includes(row.pPurDept) && row.level == 0) ||
                this.arr1.includes(row.pPurDept)
            ) {
                this.openChart1(row);
            }
        },
        async getGroupList(e) {
            this.groupListLoading = true;
            this.groupList = [];
            var param = {
                groupName: e
            };
            var res = await getQueryGroupList(param);
            if (res?.success) {
                this.groupList = res.data;
            };
            this.groupListLoading = false;
        },
        async getList() {
            this.listLoading = true;
            var res = await getPurchaseFundDtlPage(this.filter);
            if (res?.success) {
                this.dataList = res.data.list;
                this.dataTotal = res.data.total;
                this.summaryarry = res.data.summary;
            }
            this.listLoading = false;
        },
        pagechange(val) {
            this.filter.currentPage = val;
            this.getList();
        },
        sizechange() {
            this.filter.currentPage = 1;
            this.filter.pageSize = val;
            this.getList();
        },
        sortChange({ order, prop }) {
            if (prop) {
                this.filter.orderBy = prop
                this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList();
            }
        },
        async onExport() {
            this.listLoading = true;
            var res = await exportPurchaseFundDtl(this.filter);
            if (res.success) {
                this.$message({ type: 'success', message: '导出成功!' });
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '货盘资金明细' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
            this.listLoading = false;
        },
        async onAddSet() {
            this.dialogVisible = true;
            var res = await getOtherBrandDataSet();
            if (res?.success) {
                this.dialogGroupData = res.data;
            }
            var res2 = await getOtherBrandDataDropDown();
            if (res2?.success) {
                this.dialogGroupList = res2.data;
            }
        },
        async onAddGroupName() {
            var params = { brandName: this.dialogGroupName };
            var res = await saveOtherBrandDataSet(params);
            if (res?.success) {
                this.dialogGroupData.push(this.dialogGroupName);
                this.dialogGroupList.splice(this.dialogGroupList.indexOf(this.dialogGroupName), 1);
                this.dialogGroupName = null;
            }
        },
        async handleClose(e, i) {
            var params = { brandName: e };
            var res = await delOtherBrandDataSet(params);
            if (res?.success) {
                this.dialogGroupData.splice(i, 1);
                this.dialogGroupList.push(e);
            }
        },
        async getLastImportTime() {
            var res = await getLastImportTime();
            if (res?.success) {
                this.lastImportTime = res.data ? formatTime(res.data, "YYYY-MM-DD HH:mm:ss") : "";
            }
        },
        onImport (){
            this.importDialogVisible = true;
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            this.importDialogVisible = false;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("yearMonthDay", this.importParam.yearMonthDay);
            form.append("hourType", this.importParam.hourType);
            const res = await importPurchaseTotalInventoryFunds(form);
            if (res?.success) {
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
            } else {
                this.uploadLoading = false
            }
            this.uploadLoading = false
            this.importDialogVisible = false;
            await this.onSearch()
        },
        async onSearch() {
            this.filter.currentPage = 1;
            await this.getlist();
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        //上传文件
        onUploadRemove(file, fileList) {
            this.fileList = []
        },
        onSubmitUpload() {
            if (!this.importParam.yearMonthDay) {
                this.$message({ message: "请选择日期", type: "warning" });
                return false;
            }
            if (!this.importParam.hourType){
                this.$message({ message: "请选择时间", type: "warning" });
                return false;
            }
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
    }
}
</script>
<style lang="scss" scoped></style>
