<template>
  <my-container v-loading="pageLoading">
    <!-- 顶部操作 -->
    <template #header>
    <el-button style="padding: 0;margin: 0;border: none;">
      <el-select v-model="filter.shopCodeList" placeholder="店铺" filterable multiple clearable collapse-tags style="width: 180px;">
      <el-option v-for="item in filterShopList" :key="item.shopCode" :label="item.shopName"
        :value="item.shopCode"></el-option>
      </el-select>
    </el-button>
    <el-button style="padding: 0;margin: 0;border:none">
      <el-date-picker style="width: 280px" v-model="filter.sdate" type="daterange" format="yyyy-MM-dd"
        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="起始支付日期" end-placeholder="结束支付日期"
        :picker-options="pickerOptions" @change="handleDateChange" :clearable="false">
      </el-date-picker>
    </el-button>
    <el-button type="primary" @click="onSearch">查询</el-button>
    <el-button type="primary" @click="onExport">导出</el-button>
    </template>

    <!-- 列表 -->
    <vxetableNotFixNum ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :summaryarry="data.summary" 
      @select='selectchange' :isSelection='false' :tableData='data.list' :tableCols='tableCols' :loading="listLoading" :showsummary='true' />

    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
    
    <!-- 趋势图 -->
    <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
      :close-on-click-modal="false" v-dialogDrag>
      <div>
        <span>
          <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import vxetableNotFixNum from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import cesTable from "@/components/Table/table.vue";
import buschar from '@/components/Bus/buschar'
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import { getSPHShopInquirsAsync, exportSPHShopInquirsAsync, getSPHShopInquirsMap } from '@/api/customerservice/shipinhaoinquirs.js';
import { formatTime } from "@/utils";

const tableCols = [
  { sortable: 'custom', istrue: true, width: '180', align: 'center', label: '店铺', prop: 'shopName' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '咨询用户数', prop: 'inquirsCount' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '咨询会话数', prop: 'receiveCount' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '3分钟人工回复率', prop: 'threeSecondReplyRate', formatter: (row) => row.threeSecondReplyRate.toFixed(2) + "%" },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '3分钟回复人数', prop: 'threeSecondReplyCount', },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '平均回复时长（秒）', prop: 'responseTime' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '用户满意度', prop: 'satisDegree', formatter: (row) => row.satisDegree.toFixed(2) + "%" },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '满意人数', prop: 'satisDegreeCount', },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '不回复率', prop: 'noReplyRate', formatter: (row) => row.noReplyRate.toFixed(2) + "%" },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '不回复人数', prop: 'noReplyCount', },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '出勤人次', prop: 'dutyCount' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '人均接待量', prop: 'perReceptionCount' },
  
  { istrue: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 90, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

export default {
  name: "shopInquirs", 
  components: { MyContainer, vxetablebase, buschar, cesTable, vxetableNotFixNum }, 
  data() {
    return {
      that: this,
      pageLoading: false,
      listLoading: false,
      filter: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        //过滤条件
        shopCodeList: [],
        // sdate: [],
        sdate: [formatTime(new Date(new Date().getTime() - 3600 * 1000 * 24 * 30), "YYYY-MM-DD 00:00:00"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")],
        startDate: '',
        endDate: '',
        inquirsType: 1,
        shopCode: '',
      },
      data: {},// 查询返回数据集
      tableCols: tableCols,
      filterShopList: [],// 店铺选择器列表
      pickerOptions: {
        shortcuts: [{
          text: '前一天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            end.setTime(end.getTime());
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近一个月',
          onClick(picker) {
            const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
            const date2 = new Date(); date2.setDate(date2.getDate());
            picker.$emit('pick', [date1, date2]);
          }
        }]
      },
      dialogMapVisible: { visible: false, title: "", data: [] },
    };
  },
  async mounted() {
    //获取店铺列表
    await this.getSPHShop();
    this.onSearch();
  },
  methods: {
    //店铺选择器
    async getSPHShop() {
      let res = await getshopList({ platform: 20, CurrentPage: 1, PageSize: 100000 });
      this.filterShopList = res.data.list;
    },    // 检查是否选择时间
    handleDateChange() {
      this.filter.startDate = this.filter.sdate ? this.filter.sdate[0] : null;
      this.filter.endDate = this.filter.sdate ? this.filter.sdate[1] : null;
    },    //每页数量改变
    Sizechange(val) {
      this.filter.currentPage = 1;
      this.filter.pageSize = val;
      this.getList();
    },
    //当前页改变
    Pagechange(val) {
      this.filter.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.filter.orderBy = prop
        this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async getList() {
      this.listLoading = true;
      this.handleDateChange();
      try {
        const { data, success } = await getSPHShopInquirsAsync(this.filter);
        if (success) {
          this.data = data;
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.listLoading = false;
      }
    },
    onSearch() {
      //点击查询按钮时才将页数重置为1
      this.filter.currentPage = 1;
      this.$refs.pager.setPage(1);
      this.getList();
    },
    // 导出
    async onExport() {
      this.listLoading = true;
      const res = await exportSPHShopInquirsAsync(this.filter);
      this.listLoading = false;
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute("download", '店效率统计_' + new Date().toLocaleString() + '.xlsx');
      aLink.click();
    },
    // 趋势图
    async showchart(row) {
      let that = this;
      this.handleDateChange();
      this.filter.shopCode = row.shopCode;
      const res = await getSPHShopInquirsMap(this.filter).then(res => {
        that.dialogMapVisible.visible = true;
        that.dialogMapVisible.data = res
        that.dialogMapVisible.title = row.shopName;
        res.title = '';
      })
      this.dialogMapVisible.visible = true
    }

  },
}

</script>
<style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }

  .levelQueryInfoBox {
    display: flex;
    margin-bottom: 10px;
  }

  .publicCss {
    width: 220px;
    margin-right: 10px;
  }

  //解决下拉菜单多选由文字太长导致样式问题
  ::v-deep .el-select__tags-text {
    max-width: 60px;
  }
</style>
