<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="140px" class="demo-ruleForm">
        <el-form-item label="违纪总次数" prop="totalViolations">
          <inputNumberYh v-model="ruleForm.totalViolations" :placeholder="'违纪总次数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="违纪总人数" prop="totalViolators">
          <inputNumberYh v-model="ruleForm.totalViolators" :placeholder="'违纪总人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="考勤总人数" prop="totalAttendance">
          <inputNumberYh v-model="ruleForm.totalAttendance" :placeholder="'考勤总人数'" class="publicCss" />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { departmentalViolationsSubmit } from '@/api/people/peoplessc.js';
export default {
  name: 'departmentEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  data() {
    return {
      ruleForm: {
        totalViolations: '',
        totalViolators: '',
        totalAttendance: '',
      },
      rules: {
        totalAttendance: [
          { required: true, message: '请输入考勤总人数', trigger: 'blur' },
        ],
        totalViolations: [
          { required: true, message: '请输入违纪总次数', trigger: 'blur' },
        ],
        totalViolators: [
          { required: true, message: '请输入违纪总人数', trigger: 'blur' },
        ],
      }
    }
  },

  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };
  },
  methods: {
    cancellationMethod() {
      this.$emit('cancellationMethod');
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          const { data, success } = await departmentalViolationsSubmit(this.ruleForm)
          if (!success) {
            return
          }
          this.$emit("search");
        } else {
          console.error('submit failed, reason: ', valid);
          return false;
        }
      });
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}
</style>
