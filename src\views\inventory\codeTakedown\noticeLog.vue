<template>
    <MyContainer>
        <vxetablebase :id="'noticeLog20241006'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
        :tablefixed='true' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
        :isSelectColumn="false" style="width: 100%;  margin: 0"
        :loading="loading" :height="'100%'" :border="true">
        </vxetablebase>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getGoodsBanNoticeLog } from '@/api/inventory/basicgoods'

const tableCols = [
  { width: '100', align: 'center', prop: 'recipientName', label: '通知接收人', },
  { width: 'auto', align: 'center', prop: 'noticeContent', label: '发送内容', },
  { width: '150', align: 'center', prop: 'sendTime', label: '发送时间', }
]

export default {
    name: "noticeLog",
    props: {
    logsid: {
      type: String,
      default() {
        return "0";
      }
    }
  },
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      that: this,
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async getList() {
      this.loading = true
      const { data, success } = await getGoodsBanNoticeLog({ id: this.logsid ? this.logsid : 0 });
      this.loading = false
      if (success) {
        this.tableData = data
      } else {
        this.$message.error('获取列表失败')
      }
    },
  }
}

</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>