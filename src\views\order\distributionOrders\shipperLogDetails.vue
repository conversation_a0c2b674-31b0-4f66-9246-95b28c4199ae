<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select v-model="ListInfo.shipperFxName" placeholder="货主分销" class="publicCss" clearable>
                    <el-option v-for="item in fxUserNames" :label="item" :value="item" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
            <template slot="right" v-if="checkPermission('api:order:ShipperFxOrder:RejectBalanceShipperFxById')">
                <vxe-column title="操作" width="120" align="center">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="handleReject(row.id)">撤销</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { getShipperFxNameList,GetBalanceShipperFxLog, RejectBalanceShipperFxById } from '@/api/order/shipperFxOrder';

const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shipperFxName', label: '货主', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdUserName', label: '操作人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '操作时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'startTime', label: '结算日期区间', formatter: (row) => dayjs(row.startTime).format('YYYY-MM-DD') + ' - ' + dayjs(row.endTime).format('YYYY-MM-DD'), },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'balanceAmount', label: '结算金额', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    props: {
        shipperFxName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                shipperFxName: null,
                isErp: true,
                startTime: null,//开始时间
                endTime: null,//结束时间
            },
            timeRanges: [],
            tableCols,
            tableData: [], 
            fxUserNames: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        await this.getList()
        await getShipperFxNameList()
        .then(({ data }) => {
            this.fxUserNames = data;
        })
    },
    methods: {
        handleReject(id) {
            this.$confirm('此操作将撤销, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await RejectBalanceShipperFxById({ id })
                if (!success) return
                this.$message({
                    type: 'success',
                    message: '撤销成功!'
                });
                this.getList()
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetBalanceShipperFxLog(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
