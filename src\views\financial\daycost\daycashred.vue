<template>
  <!-- <container> -->
    <el-container style="height: 100%; border: 1px solid #eee">
      <el-main>
      <!-- <el-aside width="800px" style="background-color: rgb(238, 241, 246)"> -->
        <container>
          <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' 
                  :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry' :loading="listLoading"
                  style="width:100%;height:91%;margin: 0">
            <template slot='extentbtn'>
              <el-button-group>
                  <el-button style="padding: 0;margin: 0px 0px 0px 2px;">
                    <el-select filterable clearable v-model="filter1.shopCode" placeholder="店铺" style="width: 150px">
                      <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"></el-option>
                    </el-select>
                  </el-button>
                  <el-button style="padding: 0;margin: 0;">
                    <el-select filterable clearable v-model="filter1.status" placeholder="状态" style="width: 80px">
                      <el-option label="未领取" :value="0"></el-option>
                      <el-option label="已领取" :value="1"></el-option>
                      <el-option label="已过期" :value="2"></el-option>
                    </el-select>
                  </el-button>
                  <el-button style="padding: 0;margin: 0;">
                    <el-select filterable clearable v-model="filter1.resonLevel1" placeholder="大原因" style="width: 90px" @change="resonLevel1chang" >
                      <el-option v-for="item in cashRedResonlist" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                    <el-select v-if="(cashRedResonItems.length>0)" filterable clearable v-model="filter1.resonLevel2" placeholder="小原因" style="width: 90px">
                      <el-option v-for="item in cashRedResonItems" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                    <el-select  filterable clearable v-model="filter1.hasPersonLiable" placeholder="是否有责任人" style="width: 105px">
                      <el-option label="没有" :value="false"></el-option>
                      <el-option label="有" :value="true"></el-option>
                    </el-select>
                     <el-input placeholder="责任人" v-model="filter1.personLiable" style="width: 100px"></el-input>
                  </el-button>
                  <el-button style="padding: 0;margin: 0;">
                    <el-input placeholder="发送账号" v-model="filter1.sendAccount" style="width: 110px"></el-input>
                  </el-button>
                  <el-button style="padding: 0;margin: 0;">
                    <el-input placeholder="买家账号" v-model="filter1.buyAccount" style="width: 110px"></el-input>
                  </el-button>
                  <el-button style="padding: 0;margin: 0;">
                    <el-input placeholder="订单号" v-model="filter1.orderNo" style="width: 110px"></el-input>
                  </el-button>
                  <el-button style="padding: 0;margin: 0;">
                    <el-input placeholder="对应产品ID" v-model="filter1.proCode" style="width: 110px"></el-input>
                  </el-button>
                  <el-button style="padding: 0;margin: 0;">
                    <el-input placeholder="备注" v-model="filter1.remark" style="width: 110px"></el-input>
                  </el-button>
                  <el-button style="padding: 5px 5px;margin: 0;">
                    <el-checkbox v-model="filter1.isOrderNoNull">空订单</el-checkbox>
                  </el-button>
              </el-button-group>
            </template>
          </ces-table>
        <template #footer>
          <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
        </template>
        </container>
        </el-main>
      <!-- </el-aside> -->
      <!-- <el-main> -->
        <el-footer height="400px">
           <daycashredanalysis ref="daycashredanalysis"></daycashredanalysis>
       </el-footer>
      <!-- </el-main> -->
    </el-container>
  <!-- </container> -->
</template>
<script>
import {pageDayCashRed,exportDayCashRed} from '@/api/financial/daycost'
import {getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import daycashredanalysis from "@/views/financial/daycost/daycashredanalysis.vue";
import {formatFeeShareOper,formatTime,formatYesornoBool,formatLinkProCode,cashRedResonlist,formatenmCashRedReson} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'shopCode',label:'店铺', width:'120',sortable:'custom',formatter:(row)=>row.shopName},
      {istrue:true,prop:'sendAccount',label:'发送账号', width:'80',sortable:'custom'},
      {istrue:true,prop:'sendTime',label:'发送时间', width:'140',sortable:'custom',formatter:(row)=>formatTime(row.sendTime,'YYYY-MM-DD HH:mm:ss')},
      {istrue:true,prop:'buyAccount',label:'买家账号', width:'100',sortable:'custom'},
      {istrue:true,prop:'orderNo',label:'订单号', width:'170',sortable:'custom'},
      {istrue:true,prop:'amont',label:'红包金额', width:'80',sortable:'custom'},
      {istrue:true,prop:'payAmont',label:'订单金额', width:'80',sortable:'custom',formatter:(row)=>{return row.payAmont==0?' ':row.payAmont}},
      {istrue:true,prop:'computeProCode',label:'对应产品ID', width:'110',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(1,row.computeProCode)},
      {istrue:true,prop:'remark',label:'备注', width:'*',sortable:'custom'},
      {istrue:true,prop:'resonLevel1',label:'大原因', width:'70',sortable:'custom',formatter:(row)=>formatenmCashRedReson(row.resonLevel1,1)},
      {istrue:true,prop:'resonLevel2',label:'小原因', width:'70',sortable:'custom',formatter:(row)=>formatenmCashRedReson(row.resonLevel2,2)},
      {istrue:true,prop:'personLiable',label:'责任人', width:'70',sortable:'custom'},
      {istrue:true,prop:'status',label:'状态', width:'70',sortable:'custom',formatter:(row)=>{return row.status==0?'未领取':row.status==1?'已领取':row.status==2?'已过期':' '}},
      {istrue:true,prop:'shareOper',label:'分摊方式', width:'80',sortable:'custom',formatter:(row)=>formatFeeShareOper(row.shareOper)},
      {istrue:true,prop:'computeStatus',label:'计算', width:'70',sortable:'custom',formatter:(row)=>{return row.computeStatus==0?'未计算':'已计算'}},
      {istrue:true,prop:'computeTime',label:'计算时间', width:'140',sortable:'custom',formatter:(row)=>formatTime(row.computeTimeV1,'YYYY-MM-DD HH:mm:ss')},
      {istrue:true,prop:'createdTime',label:'导入时间', width:'140',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD HH:mm:ss')}
     ];
const tableHandles=[
        {label:"导入", handle:(that)=>that.onimport()},
        {label:"下载导入模板", handle:(that)=>that.ondownloadmb('现金红包导入模板')},
        {label:"导出", handle:(that)=>that.onExport()},
        //{label:"批量删除", handle:(that)=>that.onbatchDelete()},
        {label:"刷新", handle:(that)=>that.getlist()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, container,daycashredanalysis},
   props:{
       filter: { }
     },
  data() {
    return {
      shareFeeType:5,
      cashRedResonlist:cashRedResonlist,
      cashRedResonItems:[],
      filter1: {
        orderNo:'',
        proCode:'',
        shopCode:'',
        sendAccount:'',
        buyAccount:'',
        remark:'',
        status:null,
        enmReson:null,
        resonLevel1:null,
        resonLevel2:null,
        isOrderNoNull:false,
        hasPersonLiable:null,
        personLiable:null,
       },
      params:{},
      that:this,
      list: [],
      shopList: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:" sendTime ",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [], 
      listLoading: false,
      pageLoading: false
    }
  },
  mounted() {
     //this.onSearch()
     this.getShopList();
  },
  beforeUpdate() { },
  methods: {
    async getShopList(){
      const res1 = await getAllShopList({platforms:[1,9]});
      this.shopList=[];
        res1.data?.forEach(f => {
          if(f.isCalcSettlement&&f.shopCode)
              this.shopList.push(f);
        });
    },
    async resonLevel1chang(val){
      if(val){
         this.cashRedResonItems=this.cashRedResonlist.find(f=>f.value==val).items
        }
      else this.cashRedResonItems=[]; 
    },
    async onSearch() {
     let _th=this;
     //await this.$nextTick(async () => {
      _th.$refs.pager.setPage(1)
      await _th.getlist()
      await _th.loaddaycashredanalysis()
      // });
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      this.filter.shareFeeType=this.shareFeeType;
      this.params={...pager, ...this.pager, ... this.filter,... this.filter1};
      //const params = {...pager, ...this.pager, ... this.filter,... this.filter1}
      this.listLoading = true
      const res = await pageDayCashRed(this.params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
    async loaddaycashredanalysis() {
      console.log('loaddaycashredanalysis')
      let _th=this;
      let params={...this.pager, ... this.filter,... this.filter1};
       await this.$nextTick(async () => {
         await _th.$refs.daycashredanalysis.getAnalysis(params); 
      });
    },
    async onExport(){
      var pager = this.$refs.pager.getPager()
      this.filter.shareFeeType=this.shareFeeType;
      this.params={...pager, ...this.pager, ... this.filter,... this.filter1};
      const res = await exportDayCashRed(this.params)
      if(!res?.data) {
         return
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download','导出淘系红包' +  new Date().toLocaleString() + '_.xlsx' )
      aLink.click()
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   async onbatchDelete() {
       await this.$emit('ondeleteByBatch',this.shareFeeType);
    },
   async oncomput(){
      this.$emit('onstartcomput',this.shareFeeType);
   },
   async onimport(){
     await this.$emit('onstartImport',this.shareFeeType);
   },
   async ondownloadmb(name){
     await this.$emit('ondownloadmb',name);
   },
  selsChange: function(sels) {
    this.sels = sels
  },
  selectchange:function(rows,row) {
    this.selids=[];
    rows.forEach(f=>{
      this.selids.push(f.id);
    })
   },
  }
}
</script>
