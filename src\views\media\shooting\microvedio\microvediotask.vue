<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <template #header>
            <el-button-group> 
                    <el-button style="padding: 0;">
                        <el-input-number style=" width: 100px;"  :clearable="true" v-model="filter.microVedioTaskId"   v-model.trim="filter.microVedioTaskId" :min="1" :max="10000000" :controls="false" :precision="0"  placeholder="任务编号" ></el-input-number>
                    </el-button>
                    <el-button style="padding: 0;width: 90px;"> 
                        <el-select v-model="filter.hasOverTime" placeholder="是否完成" style="width:100%;" clearable>
                            <el-option label="是" value="1"></el-option>
                            <el-option label="否" value="0"></el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;">
                        <el-input  style=" width: 120px;" v-model="filter.productShortName" v-model.trim="filter.productShortName"  :maxlength =100  placeholder="产品简称" @keyup.enter.native="onSearch" clearable />
                    </el-button>
                    <el-button style="padding: 0;">
                        <el-select    filterable v-model="filter.platform" placeholder="平台" clearable :collapse-tags="true" @change="onchangeplatform" style="width: 80px">
                            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding:0;">
                        <el-select  filterable v-model="filter.shopName" placeholder="店铺" clearable style="width: 130px">
                            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
                        </el-select>
                    </el-button>
 
                    <el-button style="padding: 0;width: 100px;">
                       <el-select  v-model="filter.fpPhotoLqName"   placeholder="分配查询" filterable clearable style="width:100px" >  
                            <el-option v-for="item in fpPhotoLqNameList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>
                    
                    <el-button style="padding: 0;width: 120px;"> 
                        <el-select v-model="filter.warehouse" placeholder="大货仓" style="width:100%;" clearable>
                            <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button> 
                    <el-button style="padding: 0;">
                        <el-select  v-model="filter.operationGroup" :clearable="true"  placeholder="运营组"  filterable  style="width:100px">
                            <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;width: 100px;">
                      <el-select  v-model="filter.dockingPeople" :clearable="true"  placeholder="对接人"  filterable  style="width:100px">
                            <el-option v-for="item in dockingPeopleList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>
                   
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExeprotShootingTask"   v-if="checkPermission('api:media:microvedio:ExportDirectImgeTaskReport')"  >导出</el-button>
                    <el-button type="primary" @click="onAddTask" v-if="checkPermission('api:media:microvedio:AddOrUpdateShootingVideoTaskAsync')"  >创建任务</el-button>
                    <el-button type="primary" @click="onAddOrder" v-if="checkPermission('api:media:microvedio:ShootingTaskAddOrderSave')"  >下单发货</el-button>
                    <el-button  style="margin-left: 20px" @click="onMoveTaskOver" icon="el-icon-share" >批量完成</el-button>
                    <el-button style=" margin-left: 1; padding: 0 1 0 1 ;width: 100px;" type="primary" >
                        <el-dropdown @command="handleCommand">
                            <span class="el-dropdown-link" style="font-size: 14;color: #fff;">
                                更多列表<i class="el-icon-arrow-down el-icon--right"></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="a">显示全部</el-dropdown-item>
                                <el-dropdown-item command="b">显示默认</el-dropdown-item>
                                <el-dropdown-item command="c">查看分配</el-dropdown-item>
                                <el-dropdown-item command="d">查看小组</el-dropdown-item>
                                <el-dropdown-item command="e">寄样时效</el-dropdown-item>
                                <el-dropdown-item command="f">拍摄时效</el-dropdown-item>
                                <el-dropdown-item command="g">隐藏操作</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </el-button>
            </el-button-group>
        </template>
        <!--列表----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <ces-table ref="table" :that='that' :isIndex='false' :hasexpand='false'  @sortchange='sortchange'
            :tableData='tasklist' @select='selectchange' :isSelection="checkPermission('shootSelect')" :tableCols='tableCols'
            :isSelectColumn ='true'  :tablekey="tablekey" :customRowStyle="customRowStyle"
            :loading="listLoading" :summaryarry="summaryarry"     @summaryClick='onsummaryClick'  :selectColumnHeight="'0px'"   :isBorder="false">  
            <template slot='extentbtn'> </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList" />
        </template>

        <!--创建任务----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog :title="taskPageTitle"  :visible.sync="addTask" width="60%" :close-on-click-modal="false" :key="opentime"
            element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <microVediotaskeditfrom :key="opentime" ref="microVediotaskeditfrom"   :taskUrgencyList="taskUrgencyList"  :groupList="groupList"
            :warehouselist="warehouselist"
             :platformList="platformList" :islook='false'  :onCloseAddForm="onCloseAddForm"></microVediotaskeditfrom>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCloseAddForm(0)">取 消</el-button>
                    <my-confirm-button type="submit"  @click="onSubmit" v-show="!islook" />
                </span>
            </template>  
        </el-dialog>
        <!--上传文件----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-drawer title="上传成果文件" :key="opentime" :visible.sync="successfiledrawer" direction="rtl" 
            :wrapperClosable="true"  :close-on-press-escape ="false"
            :before-close="successfiledrawerClose" size="60%"  > 
            <shootinguploadaction  ref="successfileinfo" :rowinfo="selectRowKey" :islook="islook"  style="height: 92%;width:100%"></shootinguploadaction>
            <div class="drawer-footer" >
                <el-button @click="successfiledrawer = false">取 消</el-button> 
            </div>
        </el-drawer>

         <!--查看上传文件并打分----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-drawer title="查看成果文件"  :key="opentime" :visible.sync="successfileshow" direction="ltr" 
         :wrapperClosable="true"  :close-on-press-escape ="false"
         size="960px"   > 
            <microvediotaskuploadsuccessfilesocre  ref="microvediotaskuploadsuccessfilesocre" 
            :clostfun ="successfiledrawerscoreClose"  
            :rowinfo="selectRowKey"  
            :islook="islook"
             style="height: 92%;width:'960px'"></microvediotaskuploadsuccessfilesocre>
             <div class="drawer-footer" >
                <el-button   @click="successfileshow=false">关 闭</el-button>  
            </div> 
        </el-drawer>

        <!--查看上传附件---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="查看参考" :key="opentime"  :visible.sync="viewReference" width="60%" :close-on-click-modal="true" 
            element-loading-text="拼命加载中"  v-dialogDrag v-loading="addLoading">
            <microvediotaskuploadfile  ref="microvediotaskuploadfile" :rowinfo="selectRowKey"></microvediotaskuploadfile>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="viewReference = false">关 闭</el-button> 
                </span>
            </template>
        </el-dialog>
        <!--查看详情------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="查看备注" :key="opentime"  :visible.sync="viewReferenceRemark" width="60%" :close-on-click-modal="false" 
            element-loading-text="拼命加载中"  v-dialogDrag v-loading="addLoading">
            <microVedioTaskRemark  ref="shootingTaskRemark" :rowinfo="selectRowKey"   :islook="islook"></microVedioTaskRemark>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="viewReferenceRemark = false">关 闭</el-button> 
                    <my-confirm-button type="submit" :loading="shootingTaskRemarkrawer"  @click="sumbitshootingTaskRemark" v-show="!islook"/>
                </span>
            </template>
        </el-dialog>
        <!--加急审核------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="加急审核"  :visible.sync="taskUrgencyAproved" width="20%" :close-on-click-modal="false" :key="opentime"
                    element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <div style="vertical-align: middle; margin-top: 20px;margin-left: 80px;"> 
                    <el-radio v-model="taskUrgencyStatus" label="1" border>同意</el-radio>
                    <el-radio v-model="taskUrgencyStatus" label="9" border>驳回</el-radio> 
            </div> 
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="(taskUrgencyAproved = false)">取 消</el-button>
                    <my-confirm-button type="submit"  @click="taskUrgencyApp"/>
                </span>
            </template>  
        </el-dialog>
        <!--下单发货--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="下单发货" :visible.sync="dialogAddOrderVisible" width="50%" @close="closeAddOrder" v-dialogDrag v-loading="dialogAddOrderLoading" :close-on-click-modal="false">
            <downOrderTaskRemark  ref="downOrderTaskRemark" :warehouselist="warehouselist" :rowinfo="selectRowKey"    :islook="islook"></downOrderTaskRemark>
            <template #footer>
                <span class="dialog-footer">
                    <span style="font-size:10px;color:red;">点击提交按钮将发起钉钉审批，自提单审批通过即代表到样，非自提单审批通过则自动同步订单到聚水潭。&nbsp;&nbsp;</span>
                    <el-button @click="dialogAddOrderVisible = false">取 消</el-button>
                    <my-confirm-button type="submit"  :loading="dialogAddOrderSubmitLoding" @click="onAddOrderSave">
                        提交
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>
        <!--物流跟踪--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-drawer title="物流跟踪" :visible.sync="drawervisible" direction="rtl" :append-to-body="true">
            <logistics ref="logistics"></logistics>
        </el-drawer>
        <!--订单日志信息------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------>
        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px" v-dialogDrag>
            <orderLogPage ref="orderLogPage" :orderNoInner="sendOrderNoInner" style="z-index:10000;height:600px" />
        </el-dialog>
         <!--任务下单记录---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="任务下单记录" :visible.sync="dialogOrderDtlVisible" width='88%' v-dialogDrag :close-on-click-modal="false" :v-loading="dialogOrderDtlLoading" @close="dialogOrderDtlColsed">
            <el-row style="height:35px;line-height:35px; font-size: 16px;font-weight: bold;color: black;margin-bottom: 2px;">
                <span>下单发货信息：</span>
            </el-row>
            <el-container>
                <el-main style="height:192px;">
                    <ces-table ref="tablexdfhmain" :that='that' :isIndex='true' :hasexpand='true' :tableData='xdfhmainlist' :tableCols='xdfhmainTableCols' :loading="xdfhmainLoading" style="height:190px" :isSelectColumn="false" @cellclick="onxdfhmainCellClick">
                    </ces-table>
                </el-main>
            </el-container>
            <el-row style="height:35px;line-height:35px; font-size: 16px;font-weight: bold;color: black; margin-top: 10px;margin-bottom: 2px;">
                <span>下单发货商品明细信息：</span>
            </el-row>
            <el-container>
                <el-main style="height:262px;">
                    <ces-table ref="tablexdfhdtl" :that='that' :isIndex='true' :hasexpand='true' :tableData='xdfhdtllist' :tableCols='xdfhdtlTableCols' :loading="xdfhdtlLoading" style="height:260px" :isSelectColumn="false">
                    </ces-table>
                </el-main>
            </el-container>
        </el-dialog>
        <!--分配人趋势图------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-drawer title="分配任务统计" :visible.sync="shootingchartforfpvisible"  direction="btt" :append-to-body="true" size='75%'  :key="opentime" >
            <shootingchartforfp ref="shootingchartforfp" :charttype="fpcharttype"></shootingchartforfp>
        </el-drawer>
    </my-container>
</template>
<script> 
import cesTable from "@/components/Table/tableforvedio.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { pageShootingViewTaskAsync
        ,pickShootingTaskAsync,unPickShootingTaskAsync,deleteShootingTaskActionAsync,endShootingTaskActionAsync
        ,delShootingTploadFileTaskAsync,confrimShootingTaskAsync,unConfrimShootingTaskAsync,endRestartActionAsync
        ,taskOverActionsAsync,exportShootingTaskReport,shootUrgencyCilckAsync 
} from '@/api/media/microvedio';
import uploadfile from '@/views/media/shooting/uploadfile' 
import shootinguploadaction from '@/views/media/shooting/microvedio/microvediouploadaction' 
import microVedioTaskRemark from '@/views/media/shooting/microvedio/microvedioTaskRemark' 
import microvediotaskuploadfile from '@/views/media/shooting/microvedio/microvediotaskuploadfile' 
import microvediotaskuploadsuccessfilesocre from '@/views/media/shooting/microvedio/microvediotaskuploadsuccessfilesocre'
import microVediotaskeditfrom from '@/views/media/shooting/microvedio/microvediotaskeditfrom'
import shootingchartforfp from '@/views/media/shooting/shootingchartforfp'
import downOrderTaskRemark from '@/views/media/shooting/shared/downOrderTaskRemark'
import orderLogPage from "@/views/order/logisticsWarning/orderLogPage.vue";
import logistics from '@/components/Comm/logistics'
import { getList as getshopList ,getDirectorGroupList} from '@/api/operatemanage/base/shop' 
import { rulePlatform } from "@/utils/formruletools"; 
import {  formatWarehouse } from "@/utils/tools"; 


const tableCols = [
    { istrue: true, prop: 'microVedioTaskId', label: '编号', width: '35' , fixed: true },
    { istrue: true, prop: 'productShortName', label: '产品简称', width: '180',  fixed: true  , type: "click", handle: (that, row) => that.openComputOutInfo(row) },
    { istrue: true, prop: 'taskUrgencyName', label: '紧急程度', width: '80', fixed: true ,type: 'UrgencyButton'
        , handle: (that, row) => that.shootUrgencyCilck(row.taskUrgencyName ,row.microVedioTaskId)    
    }, 
    { istrue: true, prop: 'bz', label: '备注', width: '50' , align:'center' ,type: "clickflag", fixed: true , handle: (that, row) => that.openTaskRmarkInfo(row) }, 
    
    { istrue: true, prop: 'warehouseStr', label: '大货仓', width: '100'   , fixed: true },
 
   
    { istrue: true, type: "button", width: "50", label: '参考'   ,  
        btnList: [
            { label: "查看", handle: (that, row) => that.videotaskuploadfileDetal(row)   },
        ]
    },
 
    //微详情 
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'},  
    { istrue: true, prop: 'microDetailLqNameStr', label: '微详情',  width: "60"   , align:'center'},
    { istrue: true,   prop: 'microDetailBtnStr', label: '完成', width: '60', align:'center', type: "click"
        , handle: (that, row) => that.pickTask(row.microDetailBtnStr,"3",row.microVedioTaskId) },

    { istrue: true,  prop: 'microDetailDaysStr', label: '天数', width: '53', align:'center'   },

    { istrue: true, prop: 'microDetailOverTimeStr', width: '75', align:'center',  label: '完成日期' },
    { istrue: true,   prop: 'microDetailVedioCounts', width: '54', align:'center', label: '数量' },

    { istrue: true,  prop: 'microDetailConfirmNameStr', label: '确认人',  width: "60" , align:'center' },
    { istrue: true,   prop: 'microDetailConfirmBtnStr', label: '确认', width: '60', align:'center', type: "click"
        , handle: (that, row) => that.ConfirmTaskInfo(row.microDetailConfirmBtnStr,"3",row.microVedioTaskId) },
    { istrue: true,  prop: 'microDetailConfirmTimeStr',  label: '确认日期', width: '75', align:'center'}, 
   //视频建模 
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'},  
    { istrue: true, prop: 'modelVideoLqNameStr',   label: '视频建模', align:'center', width: "53" },
    { istrue: true, prop: 'modelVideoBtnStr',  label: '完成', width: '60', align:'center', type: "click"
        , handle: (that, row) => that.pickTask(row.modelVideoBtnStr,"6",row.microVedioTaskId) },

    { istrue: true, prop: 'modelVideoDaysStr', label: '天数', align:'center', width: '53'  },
    { istrue: true, prop: 'modelVideoOverTimeStr', width: '75', align:'center',  label: '完成日期'  },
    { istrue: true, prop: 'modelVedioCounts', width: '54', align:'center',  label: '个数'  },
  
    //分配
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'}, 
    { istrue: true, prop: 'fpVideoLqNameStr',  width: '54', align:'center', label: '分配视频'},
    { istrue: true, prop: 'fpModelLqNameStr', width: '54', align:'center', label: '分配建模'}, 
    // 
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'},  
    { istrue: true, prop: 'operationGroupstr', align:'left', width: '90', label: '运营小组'},
    { istrue: true, prop: 'dockingPeople', align:'left',  width: '90', label: '对接人'},

    { istrue: true, prop: 'platformStr', align:'left', width: '100', label: '平台'},
    { istrue: true, prop: 'shopNameStr', align:'left', width: '200', label: '店铺'},

    { istrue: true, prop: 'taskOverTimeStr',    width: '75',  label: '完成时间'  },
    { istrue: true, prop: 'arrivalTimeStr',    width: '75',  label: '到货日期'},
    { istrue: true, prop: 'arrivalTimeDays', align:'center',  width: '53',  label: '到货天数'},
    { istrue: true, prop: 'deliverTimeStr',   width: '75',  label: '发货日期' },
    { istrue: true, prop: 'deliverTimeDays', align:'center',  width: '53',  label: '发货天数' },
    { istrue: true, prop: 'applyTimeStr',   width: '75',  label: '申请日期' },
    { istrue: true, prop: 'applyTimeDays', align:'center',  width: '53',  label: '申请天数' },
    { istrue: true, prop: 'createdTime',  width: '85',  label: '创建日期', sortable: 'custom' ,formatter: (row) => row.createdTimeStr },    
    
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'}, 
    { istrue: true, prop: 'orderNoInner',  width: '80', label: '内部单号', type: 'click', handle: (that, row, column) => that.onShowOrderDtl(row)},
    { istrue: true, prop: 'expressNo',  width: '120',  label: '快递单号', type: 'click', handle: (that, row, column) => that.onShowExproessHttp(row)},
    { istrue: true, prop: 'shootOrderTrack', width: '80',  label: '拿样跟踪', type: 'click', handle: (that, row, column) => that.onShowOrderDtl(row)},
    {
        istrue: true, type: "button", label: '操作', fixed: 'right', width: "240",
        btnList: [
            { label: "编辑",   permission: "api:media:microvedio:AddOrUpdateShootingVideoTaskAsync",  handle: (that, row) => that.editTask(row) },
            { label: "成果",   permission: "microvedioUploadOutComFile", handle: (that, row) => that.onUploadSuccessFile(row) },
            { label: "完成", permission: "api:media:microvedio:taskOverActionsAsync",  ishide: (that, row) => { return row.isComplate == 1 }, handle: (that, row) => that.onTaskOverAction(row) },
            { label: "重启",   permission: "api:media:microvedio:EndRestartActionAsync", ishide: (that, row) => { return row.isend == 0 }, handle: (that, row) => that.onEndRestartAction(row) },
            { label: "终止",   permission: "api:media:microvedio:EndShootingTaskActionAsync", type: "stop",   ishide: (that, row) => { return row.isend == 1},handle: (that, row) => that.OnendShootingTaskAction(row) },          
            { type: "danger",  permission: "api:media:microvedio:deleteshootingtaskActionasync",   label: "删除", ishide: (that, row) => { return row.isdel == 1 }, handle: (that, row) => that.OnDeleteShootingTaskAction(row) }
        ]
    }
];

const xdfhmainTableCols = [
    { istrue: true, prop: 'microVedioTaskId', label: '当前任务编号', width: '100' },
    { istrue: true, prop: 'microVedioTaskIds', label: '涉及任务编号', width: '100' },
    { istrue: true, prop: 'shootingTaskOrderId', label: '下单号', width: '70' },
    { istrue: true, prop: 'receiverName', label: '收货人', width: '70' },
    { istrue: true, prop: 'receiverPhone', label: '收货电话', width: '80' },
    { istrue: true, prop: 'receiverState', label: '收货省', width: '80' },
    { istrue: true, prop: 'receiverCity', label: '收货市', width: '80' },
    { istrue: true, prop: 'receiverDistrict', label: '收货区', width: '80' },
    { istrue: true, prop: 'receiverAddress', label: '收货地址' },
    { istrue: true, prop: 'sampleRrderNo', label: '聚水潭内部单号', width: '120', type: 'click', handle: (that, row) => that.showLogDetail(row)},
    { istrue: true, prop: 'sampleExpressNo', label: '快递单号', width: '120', type: 'click', handle: (that, row, column) => that.onShowExproessHttp(row)},
    { istrue: true, prop: 'sampleExpressCom', label: '物流公司', width: '80' },
    { istrue: true, prop: 'arrivalDate', label: '到货日期', width: '100', formatter: (row) => row.arrivalDate == null ? null : formatTime(row.arrivalDate, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'isDy', label: '是否到样', width: '80', formatter: (row) => row.isDy == 1 ? "是" : "否" },
    { istrue: true, prop: 'isZt', label: '是否自提', width: '80', formatter: (row) => row.isZt == 1 ? "是" : "否" },
    { istrue: true, prop: 'approveStateName', label: '状态', width: '80'},
    { istrue: true, prop: 'createdTime', label: '创建时间', width: '150', formatter: (row) => row.createdTime == null ? null : formatTime(row.createdTime, 'YYYY-MM-DD HH:mm:ss') },
];
const xdfhdtlTableCols = [
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '200' },
    { istrue: true, prop: 'goodsName', label: '商品名称' },
    { istrue: true, prop: 'goodsPrice', label: '单价', width: '120', display: false },
    { istrue: true, prop: 'goodsQty', label: '数量', width: '120' },
    { istrue: true, prop: 'goodsAmount', label: '总额', width: '120', display: false },
];
export default {
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow,shootingchartforfp
        ,cesTable,uploadfile,shootinguploadaction,microVedioTaskRemark,downOrderTaskRemark,logistics,orderLogPage
        ,microvediotaskuploadfile,microvediotaskuploadsuccessfilesocre,microVediotaskeditfrom, 
    },
    inject:['getwarehouseListRelod','getShootingViewPersonRelod' ],
    props:["tablekey",'role'],
    watch: {  
    },
    data() {
        return {
            selmicroVedioTaskId:0,
            taskUrgencyStatus:"1",
            taskUrgencyAproved:false,
            dialogOrderDtlLoading:false,
            Oncommand:'a',
            shootingTaskRemarkrawer:false,
            viewReferenceRemark:false,
            shootingvediotask:'shootingvediotask',
            opentime:null,
            outconfilekey:null,
            fpcharttype:null,
            //分配趋势图
            shootingchartforfpvisible:false,
            //上传成果文件
            successfiledrawer:false,
            successfileshow:false,
            dialogOrderDtlColsed:false,
            //查看参考
            viewReference:false,
            //选中的行
            selectRowKey:null,
            that: this,
            pageLoading :false,
            islook:false,
            filter: { 
                isShop:0,
                isdel:0,
                isComplate:0,
                shopName:null,
                operationGroup:null
            },
            tasklist:[], 
            taskPageTitle: "创建任务",
            referenceVideoList: [],
            multipleSelection: [],
            addLoading :false,
            formatWarehouse: formatWarehouse,
            warehouselist: [],
            shopList: [],
            userList: [],
            groupList: [],

            fpDetailLqNameList: [],
            fpModelLqNameList: [],
            fpPhotoLqNameList: [],
            fpVideoLqNameList: [],
            dockingPeopleList: [],
            
            taskUrgencyList :[{value:1,label:"加急"},{value:0,label:"紧急"},{value:2,label:"确认"},{value:9,label:"正常"}],
            platformList :[],
            tableCols: tableCols,
            total: 0,
            //选中的行id
            selids : [],
            taskPhotofileList:[],
            taskExeclfileList:[],
            addTask: false,
            loginfo:null,
            summaryarry: {},
            pager: {  },
            sels: [], // 列表选中列
            listLoading: false,
            dialogAddOrderLoading:false,
            dialogAddOrderSubmitLoding:false,
            dialogAddOrderVisible:false, 
            dialogOrderDtlVisible:false, 
            drawervisible: false,  
            sendOrderNoInner: "",
            dialogHisVisible: false, 

            xdfhmainlist: [],
            xdfhmainTableCols: xdfhmainTableCols,
            xdfhmainLoading: false,

            xdfhdtllist: [],
            xdfhdtlTableCols: xdfhdtlTableCols,
            xdfhdtlLoading: false,
        };
    },
    watch: {
    },
    async created() {

    },
    async mounted() {

        await this.onSearch();
        await this.onGetdrowList();
        await this.onGetdrowList2();
       /*  this.Oncommand = this.role; 
        this.pageLoading =true;
        await this.ShowHideonSearch();
        this.pageLoading =false; */
        await this.getShootingViewPer();
        
    },
    methods: { 
        async onsummaryClick(property) { 
            this.fpcharttype = property;
            this.$nextTick(function () { 
                this.$refs.shootingchartforfp.showviewMain();
            });
           
            this.shootingchartforfpvisible = true;
        },
        async onExeprotShootingTask(){
            var pager = this.$refs.pager.getPager();
            this.filter.isShop = 0;
            this.filter.isdel = 0; 
            this.filter.isComplate = 0;  
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            this.pageLoading = true;
            var res = await exportShootingTaskReport(params);
            if (res?.data?.type == 'application/json') {return;}
            this.pageLoading = false;
            const aLink = document.createElement("a");
            var blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '微详情视频导出.xlsx')
            aLink.click()

        },
        customRowStyle(row,index){
            if(row.row?.isend && row.row.isend==1){ 
                let styleJson = {};
                styleJson.color = "rgb(216 216 216)"; 
                return styleJson
            }else{
                return null
            }
            
        }, 
        //完成操作
        async onTaskOverAction(row)
        {
           await this.onTaskOverActionShared([row.microVedioTaskId]) 
           if (res?.success) {
                this.$message({ message: '操作成功', type: "success" });
                await  this.onRefresh();
                this.selids = [];
            } 
        }, 
        //终止重启
        async onEndRestartAction(row)
        {
            this.$confirm("选中的任务将会重启，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res =  await endRestartActionAsync([row.microVedioTaskId]);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await  this.onRefresh();
                    this.selids = [];
                } 
            });
        },
        //终止
        async OnendShootingTaskAction(row)
        {
            this.$confirm("选中的任务将会终止，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res =  await endShootingTaskActionAsync([row.microVedioTaskId]);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await  this.onRefresh();
                    this.selids = [];
                } 
            });
        },
        // 删除操作
        async OnDeleteShootingTaskAction(row)
        { 
            this.$confirm("选中的任务会移动到回收站，是否确定执行", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            }).then(async () => {
                var res =  await deleteShootingTaskActionAsync([row.microVedioTaskId]);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await  this.onRefresh();
                    this.selids = [];
                } 
            });
        }, 
        //批量完成
        async onMoveTaskOver(){
            if(this.selids.length==0){
                this.$message({ type: 'warning', message: "请选择一个任务" });
                return;
            }
            await this.onTaskOverActionShared(this.selids) 
        },
        //完成操作公共   
        async onTaskOverActionShared(array)
        {
            this.$confirm("选中的任务移动完成列表，是否确定完成", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            }).then(async () => {
                var res =  await taskOverActionsAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.selids = [];
                    await  this.onRefresh();
                } 
            });
        },
        //获取下拉数据
        async onGetdrowList()
        {  
            var pfrule = await rulePlatform();
            this.platformList = pfrule.options;   
            var res = await getDirectorGroupList();
            this.groupList = res.data?.map(item => { return { value: item.key, label: item.value }; });           
        },
        async onGetdrowList2()
        { 
            var res =await this.getwarehouseListRelod();  
            this.warehouselist =res?.map(item => { return { value: item.id, label: item.label }; });
        },
        async onchangeplatform(val) {
            var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100 });
            this.filter.shopName = "";
            this.shopList = res1.data.list;
        },
        //获取分配人下拉，对接人下啦
        async getShootingViewPer() {
            var res = await this.getShootingViewPersonRelod();
            if(res){ 
                this.dockingPeopleList=res.dockingPeopleList;
                this.fpPhotoLqNameList=res.fpallList;
            }
        },
        
       async ShowHideonSearch(){ 
            var checkedColumnsFora= [];  
            switch (this.Oncommand) {
                //显示全部 ,部门经理，超管
                case "a":
                    checkedColumnsFora=
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注', '参考',
                    '确认','完成','确认人','确认日期', '完成日期', 
                    '照片', '视频', '微详情', '详情页','照片建模','视频建模',  '张数','数量', '个数', '天数',
                    '分配照片', '分配视频', '分配详情', '分配建模' ,
                    '运营小组','对接人', '平台', '店铺','完成时间','到货日期','到货天数','发货日期','发货天数','发货日期', '申请日期', '申请天数', 
                    '创建日期','内部单号','快递单号','拿样跟踪','操作'];  
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    await this.$refs["table"].initsummaryEvent(); 
                    break;
                //显示默认
                case "b":
                    checkedColumnsFora=
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注', '参考',
                     '确认人',  '完成日期', 
                    '照片', '视频', '微详情', '详情页','照片建模','视频建模',  '张数','数量', '个数',  
                    '分配照片', '分配视频', '分配详情', '分配建模' ,
                    '运营小组','对接人', '平台', '店铺','完成时间','到货日期','发货日期','发货日期', '申请日期', 
                    '创建日期','内部单号','快递单号','拿样跟踪','操作'];  
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    var i  = checkedColumnsFora.indexOf('分配照片');
                    await this.oncoulumCheck([i +15,i +16,i +17,i +18]); 
                    break;
                //查看分配
                case "c":
                    checkedColumnsFora=
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注', '参考', 
                    '照片', '视频', '微详情', '详情页','照片建模','视频建模',   
                    '分配照片', '分配视频', '分配详情', '分配建模' ,
                    '运营小组','对接人','创建日期'  ,'操作']; 
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    var i  = checkedColumnsFora.indexOf('分配照片');
                    await this.oncoulumCheck([i +8,i +9,i +10,i +11]);   
                    break;
                //查看小组
                case "d":
                    checkedColumnsFora=
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注',    
                    '分配照片', '分配视频', '分配详情', '分配建模' ,
                    '运营小组','对接人', '平台', '店铺', 
                    '创建日期','内部单号','快递单号','拿样跟踪','操作'];  
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    var i  = checkedColumnsFora.indexOf('分配照片');
                    await this.oncoulumCheck([i +8,i +9,i +10,i +11]);   
                    break;
                //寄样实效
                case "e":
                    checkedColumnsFora=
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注' ,
                    '分配照片', '分配视频', '分配详情', '分配建模' ,
                    '到货日期','到货天数','发货日期','发货天数','发货日期','申请日期', '申请天数', 
                    '创建日期','内部单号','快递单号','拿样跟踪','操作']; 
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    var i  = checkedColumnsFora.indexOf('分配照片');
                    await this.oncoulumCheck([i +8,i +9,i +10,i +11]);    
                    break;
                //拍摄实效
                case "f":
                    checkedColumnsFora=  
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注',  
                    '确认人','确认日期', '完成日期', 
                    '照片', '视频', '微详情', '详情页','照片建模','视频建模',  '张数','数量', '个数', '天数', 
                    '到货日期','创建日期', '操作']; 
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    break;
                //隐藏操作
                case "g":
                    checkedColumnsFora=  ['编号', '产品简称', '大货仓', '紧急程度', '备注', '参考',
                    '确认','完成','确认人','确认日期', '完成日期', 
                    '照片', '视频', '微详情', '详情页','照片建模','视频建模',  '张数','数量', '个数', '天数',
                    '分配照片', '分配视频', '分配详情', '分配建模' ,
                    '运营小组','对接人', '平台', '店铺','完成时间','到货日期','到货天数','发货日期','发货天数','发货日期', '申请日期', '申请天数', 
                    '创建日期','内部单号','拿样跟踪','快递单号'];  
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    await this.$refs["table"].initsummaryEvent(); 
                    break;
                default: 
                    break;
            } 
        },
        async oncoulumCheck(array){
       
            await this.$refs["table"].initsummaryEventNew(array,'onsummaryClick'); 
        },
        //更多列操作
        async handleCommand(command) {
            this.Oncommand = command;
            this.pageLoading =true;
            await  this.ShowHideonSearch();
            this.pageLoading =false;
        },
        //查询
        async onSearch(){
            this.$refs.pager.setPage(1);
            this.getTaskList();
            this.selids = [];
        },
        //刷新当前页
        async onRefresh(){
             await  this.getTaskList();
        },
        //获取数据
        async getTaskList() {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            this.listLoading = true;
            const res = await pageShootingViewTaskAsync(params);
            this.listLoading = false;
            this.total = res.data.total
            this.tasklist = res.data.list;
            this.summaryarry =  res.data.summary;
        },
        //列表排序
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        //多选事件
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.microVedioTaskId);
            })
        },

         //编辑任务
        async editTask(row) {
            this.addLoading =true;
            this.opentime = this.opentime +1;
            this.taskPageTitle = "编辑任务";
            this.addTask = true;
            await this.$nextTick(function () { 
                this.$refs.microVediotaskeditfrom.editTask(row);
            });
            this.addLoading =false;
        },
        //新增任务
        async onAddTask() {
            this.addLoading =true;
            this.opentime = this.opentime +1;
            this.addTask = true;
            this.taskPageTitle = "创建任务"; 
            this.islook = false; 
            this.addLoading =false;
        }, 
        //提交保存
        async onSubmit() { 
            this.addLoading =true;
            await  this.$nextTick(function () {
                this.$refs.microVediotaskeditfrom.onSubmit();
            });
            this.addLoading =false;
        },
        //删除上传附件操作
        async deluplogexl(ret){
            this.addLoading =true;
            
            await delShootingTploadFileTaskAsync({upLoadPhotoId:ret.upLoadPhotoId,type:2}).catch(_ => {
                this.addLoading =false;
            });  
          
            this.addLoading =false;
        },
        //删除上传图片操作
        async deluplogimg(ret){ 
            this.addLoading =true; 
            await delShootingTploadFileTaskAsync({upLoadPhotoId:ret.upLoadPhotoId,type:1}).catch(_ => {
                this.addLoading =false;
            }); 
            this.addLoading =false;
        },
        //关闭窗口，初始化数
        onCloseAddForm(type){  
            this.addTask = false;
            if(type == 1){
                this.onSearch();
            }
        },
        //确认Or取消任务
        async ConfirmTaskInfo(btnstr, index,microVedioTaskId)
        { 
            var that = this;
            switch(btnstr){ 
                case "确认":
                    this.$confirm("确认完成, 是否继续?", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    })
                    .then(async () => {
                        var res =  await confrimShootingTaskAsync({ taskid: microVedioTaskId, index: index });
                            if (res?.success) {
                                that.$message({ message: '操作成功', type: "success" });
                                await  that.onRefresh();
                            } 
                    });
                break;
                case "取消": 
                    this.$confirm("确认取消, 是否继续?", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    })
                    .then(async () => {
                        var res =  await unConfrimShootingTaskAsync({ taskid: microVedioTaskId, index: index });
                        if (res?.success) {
                            that.$message({ message: '操作成功', type: "success" });
                            await  that.onRefresh();
                        } 
                    });
                break;
            }

        },
        //紧急程度按钮点击
        async shootUrgencyCilck(btnstr,microVedioTaskId){
            var that =this;
            switch(btnstr){ 
                //申请加急
                case "正常":
                    this.$confirm("是否确认加急?", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    })
                    .then(async () => {
                        var res =  await shootUrgencyCilckAsync({ taskid: microVedioTaskId, index: 0 });
                            if (res?.success) {
                                that.$message({ message: '操作成功', type: "success" });
                                await  that.onRefresh();
                            } 
                    });
                break;
                //确认加急
                case "审核": 
                    this.opentime++;
                    this.selmicroVedioTaskId =microVedioTaskId;
                    this.taskUrgencyAproved = true; 
                break; 
            }
        },
        async taskUrgencyApp() {
            var res =  await shootUrgencyCilckAsync({ taskid: this.selmicroVedioTaskId, index: this.taskUrgencyStatus });
            if (res?.success) {
                this.taskUrgencyAproved = false; 
                this.$message({ message: '操作成功', type: "success" });
                await  this.onRefresh();
            } 
        },
        //完成任务
        async pickTask(btnstr, index,microVedioTaskId) {
            var that = this;
            switch(btnstr){ 
                case "完成":
                    this.$confirm("确认完成, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                    }).then(async () => {
                        var res =  await pickShootingTaskAsync({ taskid:  microVedioTaskId, index: index });
                        if (res?.success) {
                            that.$message({ message: '完成成功', type: "success" });
                            await  that.onRefresh();
                        } 
                    });
                break;
                case "取消":
                    this.$confirm("确认取消完成, 是否继续?", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    })
                    .then(async () => {
                        var res=   await unPickShootingTaskAsync({ taskid:  microVedioTaskId, index: index })
                        if (res?.success) {
                            that.$message({ message: '取消成功', type: "success" });
                            await that.onRefresh();
                        }
                    });
                break;
            }
        },
        async onxdfhmainCellClick(row) {
            this.xdfhmainlist.forEach(
                f => {
                    if (f.shootingTaskOrderId == row.shootingTaskOrderId) {
                        this.xdfhdtllist = f.dtlEntities;
                    }
                }
            );
        },
        //查看详情备注页
        openTaskRmarkInfo(row){
            this.opentime = this.opentime +1;
            this.selectRowKey = row.microVedioTaskId;
            this.viewReferenceRemark = true;
        },
        //成果文件上传
        onUploadSuccessFile(row){
            this.opentime = this.opentime +1;
            this.selectRowKey =row.microVedioTaskId;
            this.outconfilekey = row.microVedioTaskId;
            this.successfiledrawer = true;
        },   
        //关闭成果文件上传
        successfiledrawerClose(){
            this.successfiledrawer = false;
        },
        //查看参考附件
        videotaskuploadfileDetal(row){
            this.opentime = this.opentime +1;
            this.selectRowKey = row.microVedioTaskId;
            this.viewReference = true;
        },
        //查看成果文件
        openComputOutInfo(row){
            this.opentime = this.opentime +1;
            this.selectRowKey =row.microVedioTaskId;
            this.successfileshow = true;
        },
        successfiledrawerscoreClose(){
            this.successfileshow = false;
        },

        async sumbitshootingTaskRemark(){
            this.shootingTaskRemarkrawer = true;
            await this.$refs.shootingTaskRemark.onsubmit();
            this.shootingTaskRemarkrawer = false;
        },
         //下单发货
         async onAddOrder() {
            if (this.selids.length <= 0) {
                this.$message({ message: '请勾选任务', type: "warning" });
                return;
            } 
            await this.$nextTick(function () {
                this.$refs.downOrderTaskRemark.onAddOrder(this.selids); 
            })  
            this.dialogAddOrderVisible = true;
        },
        //关闭下单发货界面
        async closeAddOrder() {
            this.dialogAddOrderSubmitLoding = false;
        },
        //下单保存
        async onAddOrderSave() {
            this.dialogAddOrderLoading =true;
            await  this.$nextTick(function () {
                this.$refs.downOrderTaskRemark.onAddOrderSave();
            }) 
            this.dialogAddOrderLoading =false;
        },
        async onShowOrderDtl(row) {
            this.dialogOrderDtlVisible = true;
            this.xdfhmainLoading = true;
            this.xdfhdtlLoading = true;
            var ret = await getShootingTaskOrderListById({ microVedioTaskId: row.microVedioTaskId });
            this.xdfhmainLoading = false;
            this.xdfhdtlLoading = false;
            if (ret?.success && ret.data.length > 0) {
                ret.data.forEach(f => f.microVedioTaskId = row.microVedioTaskId);
                this.xdfhmainlist = ret.data;
                this.xdfhdtllist = ret.data[0].dtlEntities;
            }
        },
        async onShowExproessHttp(row) {
            this.drawervisible = true;
            let expressNo=row.expressNo;
            if(!expressNo)
            {
                expressNo=row.sampleExpressNo;
            }
            this.$nextTick(function () {
                this.$refs.logistics.showlogistics("",expressNo);
            })
        },
        showLogDetail (row) {
            this.dialogHisVisible = true;
            let sampleRrderNo=row.sampleRrderNo;
            if(!sampleRrderNo)
            {
                sampleRrderNo=row.orderNoInner;
            }
            this.sendOrderNoInner = sampleRrderNo;
        },
    }, 
};
</script>