<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <div class="div-pagetitle">{{productInfo.title}}</div>
        </template>
        <template>
            <div class="div-pagecontent1">
                <table>
                    <tr class="div-table-tr-td-header">
                        <td style="width:60px;">渠道</td>
                        <td style="width:80px;">落地页</td>
                        <td style="width:60px;">状态</td>
                        <td style="width:160px;">价格</td>
                        <td style="width:80px;">选品指数</td>
                        <td style="width:100px;">累计销量</td>
                        <td style="width:160px;">商家</td>
                        <td style="width:160px;">店铺</td>
                        <td style="width:160px;">生产企业</td>
                        <td style="width:80px;">品类</td>
                        <td style="width:80px;">单品</td>
                        <td style="width:120px;">首次发现时间</td>
                        <td style="width:120px;">最后更新时间</td>
                    </tr>
                    <tr class="div-table-tr-td-content">
                        <td>{{this.productInfo.channel}}</td>
                        <td>{{this.productInfo.groundPage}}</td>
                        <td>{{this.productInfo.status}}</td>
                        <td>{{this.productInfo.price}}</td>
                        <td>{{this.productInfo.choiceProductIndex}}</td>
                        <td>{{this.productInfo.cumulativeSales}}</td>
                        <td>{{this.productInfo.businessFirm}}</td>
                        <td>{{this.productInfo.shopName}}</td>
                        <td>{{this.productInfo.manufacturingEnterprise}}</td>
                        <td>{{this.productInfo.category}}</td>
                        <td>{{this.productInfo.singleProduct}}</td>
                        <td>{{this.productInfo.firstFindTime}}</td>
                        <td>{{this.productInfo.lastUpdateTime}}</td>
                    </tr>
                </table>
            </div>
            <div class="div-pagecontent2">
                <div style="width:99%; height: 35px; line-height: 32px; border-bottom: 1px solid #EBEEF5;margin-left: 0px;">
                    <div style="float: left; ">核心数据</div>
                    <div style="float: right; margin-right: 150px;">
                        <el-date-picker type="datetimerange" v-model="productCoreTime" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" disabled>
                        </el-date-picker>
                    </div>
                </div>
                <div style="width:99%; height: 60px; font-size: 14px;">
                    <table style="width:100%;">
                        <tr style="width:100%; height: 28px;">
                            <td style="width:15%;">直播销量</td>
                            <td style="width:15%;">直播销售额</td>
                            <td style="width:15%;">总销量</td>
                            <td style="width:15%;">总销售额</td>
                            <td style="width:15%;">关联直播间</td>
                            <td style="width:15%;">关联短视频</td>
                            <td style="width:15%;">关联达人数</td>
                        </tr>
                        <tr style="width:100%; height: 38px;">
                            <td style="font-size:16px">
                                <span style="color:#007FFF">{{this.productCoreData.liveBroadcastSales}}
                                    <span style="font-size:18px">&nbsp;万</span>
                                    <span style="vertical-align: bottom;font-size:14px">件</span>
                                </span>
                                <span style="vertical-align: bottom;font-size:14px;color: #ccc;">(占总量{{this.productCoreData.liveBroadcastSalesRate}}%)</span>
                            </td>
                            <td style="font-size:16px">
                                <span style="color:#007FFF">{{this.productCoreData.liveBroadcastSaleMoney}}
                                    <span style="font-size:18px">&nbsp;万</span>
                                    <span style="vertical-align: bottom;font-size:14px">元</span>
                                </span>
                                <span style="vertical-align: bottom;font-size:14px;color: #ccc;">(占总量{{this.productCoreData.liveBroadcastSaleMoneyRate}}%)</span>
                            </td>
                            <td style="font-size:16px">
                                <span style="color:#007FFF">{{this.productCoreData.totalSales}}
                                    <span style="font-size:18px">&nbsp;万</span>
                                    <span style="vertical-align: bottom;font-size:14px">件</span>
                                </span>
                            </td>
                            <td style="font-size:16px">
                                <span style="color:#007FFF">{{this.productCoreData.totalSaleMoney}}
                                    <span style="font-size:18px">&nbsp;万</span>
                                    <span style="vertical-align: bottom;font-size:14px">元</span>
                                </span>
                            </td>
                            <td style="font-size:16px">
                                <span style="color:#007FFF">{{this.productCoreData.relationLiveNum}}
                                    <!-- <span style="font-size:18px">&nbsp;万</span> -->
                                    <span style="vertical-align: bottom;font-size:14px">间</span>
                                </span>
                            </td>
                            <td style="font-size:16px">
                                <span style="color:#007FFF">{{this.productCoreData.relationShortVideoNum}}
                                    <span style="vertical-align: bottom;font-size:14px">个</span>
                                </span>
                            </td>
                            <td style="font-size:16px">
                                <span style="color:#007FFF">{{this.productCoreData.relationPersonsNum}}
                                    <span style="vertical-align: bottom;font-size:14px">位</span>
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="div-pagecontent3">
                <div style="width:99%; height: 35px; line-height: 32px; border-bottom: 1px solid #EBEEF5;margin-left: 0px;">
                    <div style="float: left; ">新增销量趋势</div>
                    <div style="float: right; margin-right: 150px;">
                        <el-date-picker type="datetimerange" v-model="productSalesTrendTime" :minTime="productSalesTrendTimeMin" :maxTime="productSalesTrendTimeMax" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="salesTrendTimeChange">
                        </el-date-picker>
                    </div>
                </div>
                <div>
                    <buschar ref="salestrendchar" :analysisData="expressData" :thisStyle="thisStyle" :GridStyle="gridStyle"></buschar>
                </div>
            </div>
            <div class="div-pagecontent4">
                <div style="width:99%; height: 35px; line-height: 32px; margin-left: 0px;">
                    <div style="float: left; ">套餐销量占比</div>
                </div>
                <div style="width:99%; font-size: 14px; height:280px;">
                    <ces-table ref="table1" :tableData="productSalesProportionList" style="width: 100%; " :tableCols='tableCols1' :isIndex='true' :isSelectColumn='false'>
                    </ces-table>
                </div>
            </div>
            <div class="div-pagecontent4">
                <div style="width:99%; height: 35px; line-height: 32px; margin-left: 0px;">
                    <div style="float: left; ">推广日程</div>
                </div>
                <div style="width:99%; font-size: 14px; height:300px;">
                    <ces-table ref="table2" :tableData="productExtendScheduleList" style="width: 100%; " :tableCols='tableCols2' :isIndex='true' :isSelectColumn='false'>
                    </ces-table>
                </div>
            </div>
        </template>
    </my-container>
</template>
<script>  
    import cesTable from "@/components/Table/table.vue";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import buschar from '@/components/Bus/buschar'
    import { getProductInfo, getProductSalesTrendByDate } from '@/api/operatemanage/productalllink/alllink'
    const tableCols1 = [
        { istrue: true, prop: 'specifications', label: '规格' },
        { istrue: true, prop: 'specificationsPrice', label: '套餐价格', width: '200' },
        { istrue: true, prop: 'salesProportion', label: '套餐销量占比', type: "progress" },
    ];
    const tableCols2 = [
        { istrue: true, prop: 'happenDate', label: '日期' },
        { istrue: true, prop: 'launchAdvertNum', label: '投放广告创意', align: "right" },
        { istrue: true, prop: 'totalExposureNum', label: '总曝光数估算', align: "right" },
        { istrue: true, prop: 'liveBroadcastNum', label: '直播场数', align: "right" },
        { istrue: true, prop: 'watchPeopleNum', label: '观看人数', align: "right" },
        { istrue: true, prop: 'liveBroadcastSales', label: '直播销量', align: "right" },
    ];
    export default {
        name: "Users",
        components: { MyContainer, MyConfirmButton, cesTable, buschar },
        data() {
            return {
                that: this,
                tableCols1: tableCols1,
                tableCols2: tableCols2,
                pageLoading: false,
                pageProductId: "",
                pageAllData: {},
                //主信息
                productInfo: {},
                //核心数据
                productCoreData: {},
                //时间
                productCoreTime: [],
                //销量趋势图
                productSalesTrendList: [],
                //时间
                productSalesTrendTime: [],
                productSalesTrendTimeMin: "2000-01-01",
                productSalesTrendTimeMax: "2099-12-31",
                //套餐销量占比
                productSalesProportionList: [],
                //推广日程
                productExtendScheduleList: [],
                //趋势图
                thisStyle: { width: '100%', height: '360px', 'box-sizing': 'border-box', 'line-height': '120px' },
                gridStyle: { left: '1%', right: 15, bottom: 20, top: '12%', containLabel: true },
                //趋势图Data
                expressData: {},
            };
        },
        async mounted() {
        },
        methods: {
            async clearData() {
                this.pageAllData = {};
                this.productInfo = {};
                this.productCoreData = {};
                this.productSalesTrendList = [];
                this.productSalesProportionList = [];
                this.productExtendScheduleList = [];
                this.expressData = {};
                this.productCoreTime = [];
                this.productSalesTrendTime = [];
                this.productSalesTrendTimeMin = "2000-01-01";
                this.productSalesTrendTimeMax = "2099-12-31";
            },

            //初始化：弹开此界面后调用此函数
            //调用步骤 1.pageProductId 主键赋值，2.调用 loadData()
            async loadData() {
                await this.clearData();
                await this.getProductInfo(this.pageProductId);
                await this.getSalesTrendChar();
            },
            //加载趋势图数据
            async getSalesTrendChar() {
                await this.$refs.salestrendchar.initcharts();
            },
            //获取界面初始数据
            async getProductInfo(productId) {
                this.pageLoading = true;
                try {
                    var allData = await getProductInfo(productId);
                    if (allData && allData.success && allData.data && allData.data.productInfo) {
                        this.pageAllData = allData.data;
                        this.productInfo = allData.data.productInfo;
                        this.productCoreData = allData.data.productCoreData;
                        this.productSalesTrendList = allData.data.productSalesTrendList;
                        this.productSalesProportionList = allData.data.productSalesProportionList;
                        this.productExtendScheduleList = allData.data.productExtendScheduleList;
                        this.expressData = allData.data.charData;
                        this.productCoreTime = allData.data.productCoreTime;
                        this.productSalesTrendTime = allData.data.productSalesTrendTime;
                        if (this.productSalesTrendTime.length == 2) {
                            this.productSalesTrendTimeMin = this.productSalesTrendTime[0];
                            this.productSalesTrendTimeMax = this.productSalesTrendTime[1];
                        }
                    }
                    this.pageLoading = false;
                } catch (error) {
                    this.pageLoading = false;
                }
            },
            async salesTrendTimeChange() {
                await this.getProductSalesTrendByDate(this.pageProductId);
            },
            async getProductSalesTrendByDate(productId) {
                if (this.productSalesTrendTime.length == 2) {
                    this.pageLoading = true;
                    try {
                        var parm = { productId: productId, startDate: this.productSalesTrendTime[0], endDate: this.productSalesTrendTime[1] };
                        var salesTrend = await getProductSalesTrendByDate(parm);
                        if (salesTrend && salesTrend.success && salesTrend.data) {
                            this.expressData = salesTrend.data;
                            await this.getSalesTrendChar();
                        }
                        this.pageLoading = false;
                    } catch (error) {
                        this.pageLoading = false;
                    }
                }
            }
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
<style >
    .div-pagetitle {
        width: 99%;
        margin-bottom: 10px;
        font-size: 26px;
    }
    .div-table-tr-td-header {
        margin-top: 5px;
        font-size: 14px;
        color: #ccc;
        height: 30px;
    }
    .div-table-tr-td-content {
        margin-top: 5px;
        font-size: 14px;
        height: 30px;
    }
    .div-pagecontent1 {
        margin-left: 0px;
        margin-top: 5px;
        margin-bottom: 5px;
        width: 99%;
        height: 70px;
        border: 1px solid #ebeef5;
        padding-left: 10px;
    }
    .div-table-tr-td-content td {
        max-width: 1px;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        word-wrap: break-word;
        white-space: nowrap;
    }
    .div-pagecontent2 {
        margin-left: 0px;
        margin-top: 5px;
        margin-bottom: 5px;
        width: 99%;
        height: 110px;
        border: 1px solid #ebeef5;
        padding-left: 10px;
        color: #303133;
    }
    .div-pagecontent2 tr td {
        max-width: 1px;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        word-wrap: break-word;
        white-space: nowrap;
    }
    .div-pagecontent3 {
        margin-left: 0px;
        margin-top: 5px;
        margin-bottom: 5px;
        width: 99%;
        border: 1px solid #ebeef5;
        padding-left: 10px;
        height: 380px;
    }
    .div-pagecontent4 {
        margin-left: 0px;
        margin-top: 5px;
        margin-bottom: 5px;
        width: 99%;
        border: 1px solid #ebeef5;
        padding-left: 10px;
    }
    .el-table th.el-table__cell > .cell {
        color: #303133;
    }
</style>