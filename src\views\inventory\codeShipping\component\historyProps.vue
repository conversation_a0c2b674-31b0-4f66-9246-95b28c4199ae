<template>
    <MyContainer>
        <vxetablebase :id="'historyProps202408041553'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getPurchaseOrderGoodsPostageHisLogByGoodsCode } from '@/api/inventory/purchaseOrderGoodsPostage'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'modifiedTime', label: '时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'postageFreePrice', label: '包邮单价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'postageChargePrice', label: '不包邮单价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'payment', label: '变更记录', formatter: (row) => (row.payment ? row.payment : '') + (row.calType ? '(' + row.calType + ')' : '') },
    { width: 'auto', align: 'left', prop: 'quotationVoucher', label: '报价凭证', type: 'images' },
    { width: 'auto', align: 'left', prop: 'freightVoucher', label: '运费凭证', type: 'images' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'modifiedUserName', label: '操作人', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase
    },
    props: {
        goodsCode: {
            type: String,
            default: ''
        },
        payment: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'modifiedTime',
                isAsc: false,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        await this.getList(this.goodsCode, this.payment)
    },
    methods: {
        async getList(goodsCode, payment) {
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data: { list, total, summary }, success } = await getPurchaseOrderGoodsPostageHisLogByGoodsCode({ goodsCode, payment, ...this.ListInfo })
                if (success) {
                    list.map(item => {
                        const processVoucher = (voucher) => {
                            if (voucher) {
                                let a = voucher.split(',')
                                let newarr = a.map(itemm => ({ url: itemm }))
                                return JSON.stringify(newarr)
                            }
                            return voucher
                        }
                        item.quotationVoucher = processVoucher(item.quotationVoucher)
                        item.freightVoucher = processVoucher(item.freightVoucher)
                    })
                    this.tableData = list
                    this.total = total
                    this.summaryarry = summary
                } else {
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.$message.error('获取列表失败')
            } finally {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList(this.goodsCode, this.payment)
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList(this.goodsCode, this.payment)
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList(this.goodsCode, this.payment)
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>