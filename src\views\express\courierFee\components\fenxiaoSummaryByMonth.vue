<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="ListInfo.yearmonth" type="month" :clearable="false" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"  ></el-date-picker>

        <el-input v-model.trim="ListInfo.distributor" placeholder="分销商" maxlength="50" clearable class="publicCss" />
        <div style="display: flex;align-items: center;padding-top: 1px;">
          <el-checkbox v-model="ListInfo.noUseCatch" class="publicCss" style="width: 60px;">非缓存</el-checkbox>
        </div>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
        <el-button type="primary" @click="startClac">同步进月报</el-button>
      </div>
    </template>
    <vxetablebase :id="'storeSummary202410130959'" :tablekey="'storeSummary202410130959'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <!-- <template slot="right">
        <vxe-column title="操作" width="90" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" @click="onBatchConfirmation(2,row)"><span style="color: red;">同步进月报</span></el-button>
            </div>
          </template>
        </vxe-column>
      </template> -->
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="同步进月报" :visible.sync="dialogVisible2" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 50px;">
            <el-date-picker v-model="yearmonth" type="month" :clearable="false" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"  ></el-date-picker>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible2 = false">关闭</el-button>
        <el-button type="primary" @click="onBatchConfirmation(1,null)">提交确认</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { warehouselist, formatWarehouseNew,formatTime ,platformlist} from "@/utils/tools";
import { getExpressComanyAll , getExpressInfoData_Month, deleteExpressInfoData,getExpressComanyStationName,exportExpressInfoData_Month ,batchIntoMonthExpressFreight_FenXiao} from "@/api/express/express";
import dayjs from 'dayjs'
import queryCondition from "../../dailyCourierFee/components/queryCondition.vue";

const tableCols = [
{ width: '110', align: 'center', prop: 'yearMonth', label: '结算月份'},
{  width: '110', align: 'center', prop: 'platform', label: '平台',  formatter: (row) => row.platformStr  },
  { width: '210', align: 'center', prop: 'distributor', label: '分销商', },
  {  width: '210', align: 'center', prop: 'additionalWeightFee', label: '续重费', },
  { width: '110', align: 'center', prop: 'waybill', label: '面单', },
  {  width: '110', align: 'center', prop: 'totalFreight', label: '运费合计', },
  {  width: '110', align: 'center', prop: 'jsCount', label: '总条数', },
]
export default {
  name: "fenxiaoSummaryByMonth",
  components: {
    MyContainer, vxetablebase,queryCondition
  },
  data() {
    return {
      topfilter: {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
      },
      formatWarehouseNew:formatWarehouseNew,
      platformlist:platformlist,
      timeRanges: [],
      that: this,
      ListInfo: {
        DataType:8,
        yearmonth:null,
        currentPage: 1,
        pageSize: 50,
        orderBy: 'inportDate',
        isAsc: false,
       // startTime: null,//开始时间
       // endTime: null,//结束时间
       noUseCatch: false,//是否使用缓存
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      platform:null,
      yearmonth:null,
      dialogVisible2:false,
    }
  },
  async mounted() {
    this.ListInfo.yearmonth= formatTime(new Date(),'YYYYMM');
    //await this.getList()
    await this.init()
  },
  methods: {
    startClac() {
 this.dialogVisible2 = true;
},
    async init() {
      this.$nextTick(() => {
        this.$refs.refqueryCondition.init()
      })
      const res = await getExpressComanyAll({});
      if (!res?.success) return
      this.expresscompanylist = res.data;
    },
    //导出
    async exportProps() {
      this.loading = true
      const res = await exportExpressInfoData_Month({...this.ListInfo,...this.topfilter})
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', 'ERP导出月账单数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      // if (this.timeRanges && this.timeRanges.length == 0) {
      //   //默认给当前月第一天至今天
      //   this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
      //   this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
      //   this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      // }
      this.loading = true
      const { data, success } = await getExpressInfoData_Month({...this.ListInfo,...this.topfilter})
        if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary

        let summary = data.summary || {}

const resultsum = {};
Object.entries(summary).forEach(([key, value]) => {
    resultsum[key] = formatNumber(value);
});
function formatNumber(number) {
    const options = {
        useGrouping: true,
    };
    return new Intl.NumberFormat('zh-CN', options).format(number);
}
this.summaryarry = resultsum

        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    async onBatchConfirmation(lx,row) {
      let platform=11
      let yearmonth=this.yearmonth
      if(lx==2){
        platform = row.platform
        yearmonth = row.yearMonth
      }

      if(!platform){
        this.$message.error('请选择平台')
        return
      }

      if(!yearmonth){
        this.$message.error('请选择月份')
        return
      }
      this.$confirm('是否同步该'+(lx==1?'平台':'店铺')+'?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } =  await batchIntoMonthExpressFreight_FenXiao({ platform: platform,yearmonth:yearmonth})
        if (success) {
          this.$message.success('同步成功,请到任务管理中查看同步进度')
        } else {
          this.$message.error('同步失败')
        }
      }).catch(() => {
        this.$message.info('已取消同步')
      });
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
