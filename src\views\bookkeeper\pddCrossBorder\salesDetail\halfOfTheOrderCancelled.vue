<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <div>
          <inputYunhan :key="'1'" :keys="'one'" :width="'150px'" ref="" :inputt.sync="ListInfo.orderNo"
            v-model.trim="ListInfo.orderNo" placeholder="订单号/若输入多条请按回车" :clearable="true" @callback="callbackOrderNo"
            title="订单号" @entersearch="entersearch">
          </inputYunhan>
        </div>

        <div>
          <inputYunhan :key="'3'" :keys="'three'" :width="'150px'" ref="" :inputt.sync="ListInfo.goodsCode"
            v-model.trim="ListInfo.goodsCode" placeholder="商品编码/若输入多条请按回车" :clearable="true"
            @callback="callbackgoodsCode" title="商品编码" @entersearch="entersearch">
          </inputYunhan>
        </div>

        <el-input v-model.trim="ListInfo.shopName" placeholder="店铺" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>

        <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button @click="startImport"
          type="primary" icon="el-icon-share" @command="handleCommand"> 导入
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a">下载模版</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button type="primary" @click="onExport" style="margin-left: 5px;" v-if="checkPermission('salesDetail_kj_export')">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'salesDetails_SheInSelf202409051757'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :border="true" :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
      :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="display: flex; align-items: baseline; height: 75px;margin-top: 10px;">
        <el-date-picker style="width: 150px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay" type="date"
          placeholder="选择日期" :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
        </el-date-picker>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { importCodeSalesCancellationOrder_Temu_BanTuoAsync, getCodeSalesCancellationOrder_Temu_BanTuoPageList,codeSalesCancellationOrder_Temu_BanTuo_Export } from '@/api/bookkeeper/crossBorderV2'

import dayjs from 'dayjs'
import { formatTime } from "@/utils";
import inputYunhan from "@/components/Comm/inputYunhan";

const tableCols = [
  { istrue: true, prop: 'yearMonthDay', label: '年月日', sortable: 'custom', width: '90' },
  { istrue: true, prop: 'shopCode', label: '店铺ID', sortable: 'custom', width: '90' },
  { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom' },
  { istrue: true, prop: 'orderNo', label: '订单号', sortable: 'custom' },
  { istrue: true, prop: 'area', label: '地区', sortable: 'custom' },
  { istrue: true, prop: 'cancelReason', label: '取消原因', sortable: 'custom' },
  { istrue: true, prop: 'cancelSource', label: '取消来源', sortable: 'custom' },
  { istrue: true, prop: 'orderStatus', label: '状态', sortable: 'custom' },
  { istrue: true, prop: 'subOrderNumber', label: '售后子订单号', sortable: 'custom' },
  { istrue: true, prop: 'goodsName', label: '商品名称', sortable: 'custom' },
  { istrue: true, prop: 'skuid', label: 'SKU ID', sortable: 'custom' },
  { istrue: true, prop: 'orderCount', label: '数量', sortable: 'custom' },
  { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom' },
  { istrue: true, prop: 'attribute', label: '属性', sortable: 'custom' },
  { istrue: true, prop: 'refundTime', label: '退款完成时间', sortable: 'custom' },
  { istrue: true, prop: 'requestTime', label: '请求时间', sortable: 'custom' },
  {
    istrue: true, prop: 'submitTime', label: '提交日期', sortable: 'custom',
    formatter: (row) => {
      return row.submitTime ? formatTime(row.submitTime, "YYYY-MM-DD") : "";
    }
  },

]
export default {
  name: "sheInSalesDetails",
  components: {
    MyContainer, vxetablebase, inputYunhan
  },
  data() {
    return {
      yearMonthDay: null,
      dialogVisible: false,//导入弹窗
      fileList: [],//上传文件列表
      uploadLoading: false,//上传按钮loading
      fileparm: {},//上传文件参数
      timeRanges: [],//时间范围
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startDate: null,//开始时间
        endDate: null,//结束时间
        orderNo: null,//线上单号
      },
      tableCols,
      summaryarry: {},
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async changeTime(e) {
      this.ListInfo.startDate = e ? e[0] : null
      this.ListInfo.endDate = e ? e[1] : null
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("YearMonthDay", dayjs(this.yearMonthDay).format('YYYYMMDD'));
      //form.append("PlatForm", 12);
      var res = await importCodeSalesCancellationOrder_Temu_BanTuoAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (!this.yearMonthDay) {
        this.$message({ message: "请选择日期", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.yearMonthDay = null
      this.dialogVisible = true;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近1天时间
        this.ListInfo.startDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
      }
      this.loading = true
      const { data, success } = await getCodeSalesCancellationOrder_Temu_BanTuoPageList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async handleCommand(command) {
      switch (command) {
        //下载模版
        case 'a':
          await this.downLoadFile()
          break;
      }
    },
    async downLoadFile() {
      window.open("/static/excel/KjSalesDetails/TEMU-半托取消订单.xlsx", "_blank");
    },
    async entersearch(val) {
      this.getList();
    },
    async callbackOrderNo(val) {
      this.ListInfo.orderNo = val;
    },
    async callbackgoodsCode(val) {
      this.ListInfo.goodsCode = val;
    },
    async onExport() {//导出列表数据；

      var res = await codeSalesCancellationOrder_Temu_BanTuo_Export(this.ListInfo);
      if (res?.success) {
        this.$message({ message: res.msg, type: "success" });
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>