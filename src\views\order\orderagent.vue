<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="代拍时间:">
          <el-date-picker style="width:320px"
            v-model="filter.timerange"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始"
            end-placeholder="结束"
            :picker-options="pickerOptions"
          ></el-date-picker>
        </el-form-item>        
        <el-form-item label="店铺:">
            <el-select
              v-model="filter.shopCode"
              placeholder="请选择"
              :clearable="true" :collapse-tags="true"  filterable>
              <el-option
                v-for="item in shopList"
                :key="item.shopCode"
                :label="item.shopName"
                :value="item.shopCode"/>
            </el-select>
          </el-form-item>
          <el-form-item label="采购员:">
              <el-input v-model="filter.purchaser" placeholder="采购员"/>             
          </el-form-item>
          <el-form-item label="订单号:">
                <el-input v-model="filter.orderNo" placeholder="订单号"/>         
            </el-form-item>
            <el-form-item label="商品编码:">
                <el-input v-model="filter.goodsCode" placeholder="商品编码"/>         
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button>
            </el-form-item>
          </el-form>
    </template>
 
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
      :tableData='list'    :tableCols='tableCols'
      :tableHandles='tableHandles'
      :loading="listLoading">
    </ces-table>
    
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getlist"
      />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%">
      <span>
        <el-upload
          ref="upload"
          class="upload-demo"
          :auto-upload="false"
          :multiple="false"
          :limit="1"
          action
          accept=".xlsx"
          :http-request="uploadFile"
          :file-list="fileList"
        >
         <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
         </template> 
          <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>

<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatPlatform,formatYesorno} from "@/utils/tools";
import { rulePlatform,ruleShopCode} from "@/utils/formruletools";
import { 
  importOrderAgent,
  pageOrderAgent,
  exportOrderDeductMoney,
} from "@/api/order/ordergoods"
const tableCols =[
       {istrue:true,prop:'agentOrderTime',label:'代拍时间', width:'100',sortable:'custom'},
       {istrue:true,prop:'purchaser',label:'采购员', width:'80',sortable:'custom',}, 
       {istrue:true,prop:'shopName',label:'店铺名', width:'150',sortable:'custom',},
       {istrue:true,prop:'goodsCode',label:'商品编码', width:'110',sortable:'custom',},
       {istrue:true,prop:'goodsName',label:'商品名称', width:'200',sortable:'custom',},
       {istrue:true,prop:'qty',label:'数量', width:'80',sortable:'custom',},
       {istrue:true,prop:'buyerAmount',label:'买家支付金额', width:'120',sortable:'custom',},
       {istrue:true,prop:'agentAmount',label:'代拍金额', width:'110',sortable:'custom',},
       {istrue:true,prop:'orderNo',label:'线上订单号', width:'180',sortable:'custom',},
       {istrue:true,prop:'payOrderNo',label:'支付订单编号', width:'180',sortable:'custom',},
       {istrue:true,prop:'expressCompany',label:'快递公司', width:'110',sortable:'custom',},
       {istrue:true,prop:'expressNo',label:'快递单号', width:'130',sortable:'custom',},       
       {istrue:true,prop:'purchasingChannel',label:'采购渠道', width:'110',sortable:'custom',},
       {istrue:true,prop:'buyerInfo',label:'客户信息', width:'110',sortable:'custom',}
     ];
const tableHandles1=[
        //{label:"导入模板", handle:(that)=>that.downloadTemplate()},
        {label:"导入", handle:(that)=>that.startImport()},
        //{label:"导出", handle:(that)=>that.onExport()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: {
        startDate:null,
        endDate:null,
        platform:null,
        shopCode:"",
        orderNo:null,
        goodsCode:null,
        timerange:[formatTime(dayjs().startOf("month"), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")]
      },
      list: [],
      summaryarry:{},
      pager:{OrderBy:"agentOrderTime",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      platformList: [],
      shopList: [],
      dialogVisible: false,
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,     
      deleteLoading: false,
      formtitle:"新增",
      fileList:[],
      pickerOptions:{
        disabledDate(time){
          return time.getTime()>Date.now();
        }
      }
    }
  },
  async mounted() {
    await this.onchangeplatform();

    await this.getlist();
    
  },
  methods: {
    //设置平台下拉
    async setPlatform() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;
    },
    //设置店铺下拉
    async onchangeplatform(val){
      const res = await getshopList({platform:val,CurrentPage:1,PageSize:1000});
      this.shopList=res.data.list||[];
      this.shopList.push({shopCode:"{线下}",shopName:"{线下}"});
      this.filter.shopCode="";
    },
    //下载导入模板
    downloadTemplate(){
        window.open("../static/excel/订单代拍导入模板.xlsx","_self");
    },
    //开始导入
    startImport(){
      this.dialogVisible=true;
    },
    //取消导入
    cancelImport(){
      this.dialogVisible=false;
    },
    //上传成功
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {        
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    //提交导入
    submitUpload() {
        this.$refs.upload.submit();       
    },
    //上传文件
    uploadFile(item) {
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      const res = importOrderAgent(form);
       this.$message({
                      message: '上传成功,正在导入中...',
                      type: "success",
                    }); 
    },
    //导出
    async onExport(){
        var params=this.getCondition();
        if(params===false){
            return;
        }
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportOrderDeductMoney(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','代拍订单_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click();
    },
    //获取查询条件
    getCondition(){
        if (this.filter.timerange&&this.filter.timerange.length>1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      else {
        this.$message({message:"请先选择代拍时间！",type:"warning"});
        return false;
      }
      var pager = this.$refs.pager.getPager();
      var page  = this.pager;
      const params = {
        ...pager,
        ...page,
        ... this.filter
      }

      return params;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    //分页查询
    async getlist() {
      var params=this.getCondition();
      if(params===false){
            return;
      }

      this.listLoading = true
      const res = await pageOrderAgent(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry=res.data.summary;
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
    //排序查询
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else{
        var orderBy =column.prop=="shopName"?"shopCode":column.prop;
        this.pager={OrderBy:orderBy,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearch();
    }, 
    selsChange: function(sels) {
      this.sels = sels
    }
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary{
  margin-right: 7px;
}
</style>
