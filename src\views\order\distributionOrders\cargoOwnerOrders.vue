<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" :clearable="false"
          start-placeholder="付款开始日期" end-placeholder="付款结束日期" :picker-options="pickerOptions"
          style="width: 230px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <dateRange :startDate.sync="ListInfo.sendTimeStart" :endDate.sync="ListInfo.sendTimeEnd"
          style="width: 230px;margin-right: 5px;" class="publicCss" startPlaceholder="发货时间" endPlaceholder="发货时间" />
        <el-input v-model.trim="ListInfo.platFormName" placeholder="平台站点" maxlength="50" clearable class="publicCss" />
        <div class="publicCss" style="display: flex;">
          <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode" width="150px"
            placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="50" :maxlength="1000"
            @callback="callbackGoodsCode" title="商品编码">
          </inputYunhan>
        </div>
        <el-input v-model.trim="ListInfo.orderInnerNo" placeholder="内部订单号" maxlength="50" clearable class="publicCss" />
        <el-select v-model="ListInfo.shipperFxName" placeholder="货主分销" class="publicCss" clearable>
          <el-option v-for="item in fxUserNames" :key="'2' + item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable>
          <el-option key="已发货" label="已发货" value="已发货" />
          <el-option key="取消" label="取消" value="取消" />
          <el-option key="异常" label="异常" value="异常" />
          <el-option key="被拆分" label="被拆分" value="被拆分" />
          <el-option key="发货中" label="发货中" value="发货中" />
          <el-option key="被合并" label="被合并" value="被合并" />
        </el-select>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" size="mini" :disabled="isExport" @click="onExport">导出</el-button>
        <el-button type="primary" @click="openSet">设置</el-button>
      </div>
    </template>
    <vxetablebase :id="'cargoOwnerOrders202410281512'" :tablekey="'cargoOwnerOrders202410281512'" ref="table" border
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="设置" :visible.sync="openSetVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <el-form :label-position="labelPosition" label-width="180px" :model="ruleForm" v-if="openSetVisible">
        <el-form-item label="货主分销">
          <el-select v-model="ruleForm.shipperFxName" placeholder="货主分销" class="publicCss" :clearable="false"
            @change="changeName" style="width: 200px;">
            <el-option v-for="item in fxUserNames" :key="'3' + item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="平均每单快递费">
          <el-input-number v-model="ruleForm.expressFee" :max="999" :min="0" :precision="2" :controls="false"
            placeholder="平均每单快递费" style="width: 200px;" />
        </el-form-item>
        <el-form-item label="平均每单出仓费">
          <el-input-number v-model="ruleForm.outWareFee" :max="999" :min="0" :precision="2" :controls="false"
            placeholder="平均每单出仓费" style="width: 200px;" />
        </el-form-item>
        <el-form-item label="平均每单包材费">
          <el-input-number v-model="ruleForm.packFee" :max="999" :min="0" :precision="2" :controls="false"
            placeholder="平均每单包材费" style="width: 200px;" />
        </el-form-item>
        <el-form-item label="平均每单坪效费">
          <el-input-number v-model="ruleForm.fatEffectFee" :max="999" :min="0" :precision="2" :controls="false"
            placeholder="平均每单坪效费" style="width: 200px;" />
        </el-form-item>
        <div class="btnGroup">
          <el-button @click="openSetVisible = false">关闭</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getShipperFxNameList, getShipperFxOrderList, importShipperFxOrders, GetShipperFxFeeSet, SaveShipperFxFeeSet, ExportShipperFxOrderList } from '@/api/order/shipperFxOrder';
import inputYunhan from "@/components/Comm/inputYunhan";
import dayjs from 'dayjs'
import { rulePlatform } from "@/utils/formruletools";
import dateRange from "@/components/date-range/index.vue";
const tableCols = [
  { sortable: 'custom', width: '90', align: 'center', prop: 'orderNoInner', label: '内部订单号', type: 'orderLogInfo', orderType: 'orderNoInner' },
  { sortable: 'custom', width: '90', align: 'center', prop: 'orderNo', label: '线上订单号', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'shopCode', label: '店铺编号', },
  { sortable: 'custom', width: '250', align: 'center', prop: 'shopName', label: '店铺名称', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'shipperFxName', label: '货主分销', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'platformName', label: '平台站点', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'buyerAccount', label: '买家帐号', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'orderTime', label: '下单时间', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'payTime', label: '付款日期', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'orderRmark', label: '订单备注', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'sendTime', label: '发货日期', },
  { width: '150', align: 'center', prop: 'labels', label: '标签', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'amonted', label: '已付金额', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'status', label: '状态', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'qty', label: '数量', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'rmark', label: '备注', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'costPrice', label: '成本价', },
  { tipmesg: '快递费（汇总行：已发货快递费为0的单量 / 总快递费 / 平均快递费）', sortable: 'custom', width: '90', align: 'center', prop: 'exPressFee', label: '真实快递费', },
  { tipmesg: '出仓费（汇总行：已发货出仓费为0的单量 / 总出仓费 / 平均出仓费）', sortable: 'custom', width: '90', align: 'center', prop: 'outWareFee', label: '真实出仓费', },
  { tipmesg: '包材费（汇总行：已发货包材费为0的单量 / 总打包费 / 平均打包费）', sortable: 'custom', width: '90', align: 'center', prop: 'packFee', label: '真实包材费', },
  { tipmesg: '设置的快递费（汇总行：已发货设置快递费为0的单量 / 总设置快递费 / 平均设置快递费）', width: '90', align: 'center', prop: 'setExPressFee', label: '快递费', },
  { tipmesg: '设置的出仓费（汇总行：已发货设置出仓费为0的单量 / 总设置出仓费 / 平均设置出仓费）', sortable: 'custom', width: '90', align: 'center', prop: 'setOutWareFee', label: '出仓费', },
  { tipmesg: '设置的打包费（汇总行：已发货设置打包费为0的单量 / 总设置打包费 / 平均设置打包费）', sortable: 'custom', width: '90', align: 'center', prop: 'setPackFee', label: '打包费', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'orderWeight', label: '订单快递重量', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'weight', label: '订单商品重量', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'expressCompany', label: '快递公司', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'expressNo', label: '快递单号', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'province', label: '省份', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'city', label: '城市', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'fxSupplierName', label: '供销商', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'norms', label: '颜色及规格', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'fxSupplierDetailName', label: '明细供销商', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'shopStyleCode', label: '店铺款式编码', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'shopGoodCode', label: '店铺商品编码', },
]
export default {
  name: "cargoOwnerOrders",
  components: {
    MyContainer, vxetablebase, inputYunhan, dateRange
  },
  data() {
    return {
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      fxUserNames: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        payTimeStart: null,//开始时间
        payTimeEnd: null,//结束时间
        orderInnerNo: null,//内部订单号
        shipperFxName: null,//货主分销
        goodsCode: null,//商品编码
        platFormName: null,//平台
        status: null,//状态
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      openSetVisible: false,
      ruleForm: {
        shipperFxName: '',
        expressFee: 0,
        outWareFee: 0,
        packFee: 0,
        fatEffectFee: 0,
      },
      platformList: [],
    }
  },
  async mounted() {
    await this.getList();

    var pfrule = await rulePlatform();
    this.platformList = pfrule.options;

    await getShipperFxNameList()
      .then(({ data }) => {
        this.fxUserNames = data;
      })
  },
  methods: {
    async submitForm() {
      if (!this.ruleForm.shipperFxName) {
        this.$message.error('请选择货主分销')
        return
      }
      const { success } = await SaveShipperFxFeeSet(this.ruleForm)
      if (success) {
        this.$message.success('设置成功')
        this.openSetVisible = false
        this.getList()
      }
    },
    async changeName(e) {
      const { data, success } = await GetShipperFxFeeSet(e)
      if (success) {
        this.ruleForm = data ? data : this.ruleForm
      }
    },
    openSet() {
      this.ruleForm = {
        shipperFxName: '',
        expressFee: 0,
        outWareFee: 0,
        packFee: 0,
        fatEffectFee: 0,
      }
      this.openSetVisible = true
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importShipperFxOrders(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async changeTime(e) {
      this.ListInfo.payTimeStart = e ? e[0] : null
      this.ListInfo.payTimeEnd = e ? e[1] : null
    },
    callbackGoodsCode(val) {
      this.ListInfo.goodsCode = val
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        this.ListInfo.payTimeStart = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.payTimeEnd = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.payTimeStart, this.ListInfo.payTimeEnd]
      }
      this.loading = true
      const { data, success } = await getShipperFxOrderList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },

    async onExport(val) {
      this.loading  = true;
      await ExportShipperFxOrderList(this.ListInfo)
        .then((data) => {
          if (!data.success) {
            return;
          }
          window.$message.success(data.msg);
        })
        .catch(() => {
          this.loading = false;
        });
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

.btnGroup {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
</style>
