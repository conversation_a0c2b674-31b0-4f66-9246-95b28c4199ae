<template>
    <MyContainer>
        <el-container style="height: 60vh; border: 1px solid #eee;box-sizing: border-box;">
            <el-aside width="200px" style="background-color: rgb(238, 241, 246)">
                <DeptViewLeftTree @selected="leftTreeSelected" />
            </el-aside>
            <el-container style="height:100%;">
                <el-main>
                    <div class="top">
                        <el-select v-model="ListInfo.isContainChildDeptUser" style="width: 130px">
                            <el-option label="包含子部门用户" :value="true" />
                            <el-option label="不包含子部门用户" :value="false" />
                        </el-select>
                        <el-select v-model="ListInfo.isDelete" clearable placeholder="在职状态" style="width: 90px">
                            <el-option label="在职状态" :value="null" />
                            <el-option label="在职" :value="false" />
                            <el-option label="离职" :value="true" />
                        </el-select>
                        <el-select v-model="ListInfo.employeeStatus" clearable placeholder="员工结构" style="width: 90px">
                            <el-option label="员工结构" :value="null" />
                            <el-option label="正式" :value="3" />
                            <el-option label="试用" :value="2" />
                        </el-select>
                        <el-input v-model.trim="ListInfo.keywords" style="width: 160px" :maxLength="100"
                            placeholder="关键字查询" @keyup.enter.native="getList" clearable>
                            <el-tooltip slot="suffix" effect="dark" content="钉ID、姓名、手机号、ERP昵称、ERP账号、职位、钉钉号。"
                                placement="bottom">
                                <i class="el-input__icon el-icon-question"></i>
                            </el-tooltip>
                        </el-input>
                        <el-button type="primary" @click="getList(true)">查询</el-button>
                    </div>
                    <vxetablebase :id="'arrPublicDialogPage202408041352'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
                        @select="checkboxRangeEnd" @sortchange='sortchange' :tableData='tableData'
                        :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" v-loading="loading"
                        :style="{ width: '100%', height: isMore ? '280px' : '370px', margin: 0 }" />
                    <div class="father_bottom" v-if="isMore">
                        <div class="father_bottom_topWord">已选择人员</div>
                        <div style="width: 100%">
                            <el-tag v-for="(item, index) in seleteData" :key="index" closable type=""
                                style="margin-right: 5px;margin-left: 5px;" @close="delprops(item, index)">{{
                    item.userName
                }}</el-tag>
                        </div>
                    </div>
                </el-main>
                <el-footer>
                    <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
                </el-footer>
                <div class="btnGroup">
                    <el-button type="primary" @click="submit">确定</el-button>
                    <el-button @click="onClose">取消</el-button>
                </div>
            </el-container>
        </el-container>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import DeptViewLeftTree from '@/views/admin/organization/DeptViewLeftTree.vue'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { PageDDUserInfo } from '@/api/admin/deptuser'
import dayjs from "dayjs";
const reg = /(\d{3})\d*(\d{4})/
const tableCols = [
    { istrue: true, label: '', type: "checkbox" },
    { istrue: true, prop: 'avatar', label: '头像', type: "images", width: 'auto' },
    { istrue: true, prop: 'userName', label: '姓名', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'erpUserName', label: 'ERP账号', sortable: 'custom', width: '90', formatter: (row) => row.erpUserName ? row.erpUserName.replace(reg, '$1****$2') : null },
    { istrue: true, prop: 'erpNickName', label: 'ERP昵称', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'title', label: '职位', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'deptFullName', label: '所属组织', sortable: 'custom', minwidth: '120' },//dayjs
    { istrue: true, prop: 'isDelete', label: '在职状态', style: 'center', width: 'auto', sortable: 'custom', formatter: (row) => row.isDeleteText },
    { istrue: true, prop: 'employeeStatus', label: '员工结构', style: 'center', width: 'auto', sortable: 'custom', formatter: (row) => row.employeeStatusText },
    { istrue: true, prop: 'hired_date', label: '入职时间', width: '100', sortable: 'custom', formatter: (row) => row.hired_date ? dayjs(row.hired_date).format('YYYY-MM-DD') : '' },
];
export default {
    components: { MyContainer, DeptViewLeftTree, vxetablebase },
    data() {
        return {
            tableCols,
            that: this,
            total: 0,
            tableData: [],
            loading: false,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                deptCode: "",
                isContainChildDeptUser: true,
                isDelete: false,
                employeeStatus: null,
                keywords: '',
                corpId: '',
            },
            seleteData: [],
            isMore: true,//默认值是true,代表可以多选,如果是false,代表只能单选
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
        loadData({ isMore }) {
            this.isMore = isMore
        },
        delprops(item, i) {
            this.seleteData.splice(i, 1)
            this.$nextTick(() => {
                this.tableData.forEach(f => {
                    if (item.ddUserId == f.ddUserId) {
                        this.$refs.table.$refs.xTable.setCheckboxRow(f, false)
                    }
                })
            })
        },
        leftTreeSelected(row) {
            this.ListInfo.deptCode = row && row.full_code ? row.full_code : '';
            this.ListInfo.corpId = row && row.corpId ? row.corpId : '';
            this.getList();
        },
        submit() {
            if (this.seleteData.length == 0) return this.$message.error('您还没选择任何数据')
            if (this.isMore === false && this.seleteData.length > 1) return this.$message.error('只能选一个用户')
            this.$emit('afterSave', this.seleteData);
            this.$emit('close')
        },
        onClose() {
            this.$emit('close')
        },
        checkboxRangeEnd(row) {
            this.tableData.forEach(f => {
                if (row.includes(f)) {
                    if (this.seleteData.filter(item => item.ddUserId === f.ddUserId).length == 0) {
                        this.seleteData.push(f)
                    }
                }
            })
            this.tableData.forEach(f => {
                if (!row.includes(f)) {
                    var item = this.seleteData.filter(item => item.ddUserId === f.ddUserId)[0]
                    if (item != null) {
                        let index = this.seleteData.indexOf(item);
                        if (index !== -1) {
                            this.seleteData.splice(index, 1);
                        }
                    }
                }
            })
        },
        async getList(isSearch) {
            if (isSearch) {
                this.ListInfo.currentPage = 1
            }
            this.loading = true
            const { data, success } = await PageDDUserInfo(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                if (this.seleteData.length > 0) {
                    data.list.forEach(item => {
                        this.seleteData.forEach(f => {
                            if (item.ddUserId == f.ddUserId) {
                                let _this = this
                                this.$nextTick(() => {
                                    _this.$refs.table.$refs.xTable.setCheckboxRow(item, true)
                                })
                            }
                        })
                    })
                }
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        margin-right: 10px;
        width: 200px;
    }
}

.btnGroup {
    display: flex;
    justify-content: end;
}

.father_bottom {
    height: 80px;
    overflow: auto;
    border: 1px solid #ccc;
    margin-top: 10px;

    .father_bottom_topWord {
        text-align: center;
    }
}
</style>