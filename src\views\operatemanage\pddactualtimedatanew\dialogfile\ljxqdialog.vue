<template>
  <div style="width: 100%; height: 100vh; overflow-y: auto;">
    <div style="display: flex; flex-direction: column; background-color: #DCDFE6; padding: 50px 100px;">
      <div style="background-color: white; border-radius: 10px 10px 0 0; padding: 20px; border-bottom: 1px solid #eee;">
        <div style="font-size: 14px;">商品轮播图 :<span
            style="color: #909399; font-size: 13px;">图片要求：宽高比例为1:1或3:4.且宽高均大于480px,大小3M以内，已上传8/10张，拖拽可调整顺序</span></div>
        <!-- <div>时间：{{ time | timeFormater }}</div> -->
        <div key="uploadfileone">
          <uploadfile ref="uploadChatImgone" :uploadInfo="allmsg.productTurnPicUrls" :minisize="true" :upkey="'uploadfileone'"
            placeholder="请上传供应商聊天记录" :limit="10000" :accepttyes="'.image/jpg,image/jpeg,image/png'"
            uploadVerifyType="图片" />
        </div>


        <div class="flexrow" style="align-items: center;margin: 10px 0 20px 0;">
          <div style="width: 100px; font-size: 14px;">商品标题：</div>
          <el-input v-model="allmsg.productName" :maxlength="200" :placeholder="allmsg.productName"></el-input>
        </div>


        <div style="font-size: 14px;">商品视频 :<span
            style="color: #909399; font-size: 13px;">视频要求：时长60秒以内，宽高比为1:1或16:9或3:4，上传后展示商品轮播图位置首位，享全站流量扶持，提升转化</span>
        </div>

        <div key="uploadfiletwo">
          <uploadfile ref="uploadChatImgtwo" :uploadInfo="allmsg.productVideoUrl" :minisize="true" :upkey="'uploadfiletwo'"
            placeholder="上传视频" :limit="1" :accepttyes="'.mp4,.MOV'" uploadVerifyType="视频" />
        </div>



        <div style="margin-top: 10px;font-size: 14px;">商品讲解视频 :<span
            style="color: #909399; font-size: 13px;">视频要求：时长10秒~10分钟以内；宽高比为9:16，上传后展示在商详悬浮窗入口</span></div>
        <div key="uploadfilethr">
          <uploadfile ref="uploadChatImgthr" :uploadInfo="allmsg.productExplainVideoUrl" :minisize="true" :upkey="'uploadfilethr'"
            placeholder="上传视频" :limit="1" :accepttyes="'.mp4,.MOV'" uploadVerifyType="视频" />
        </div>

        <div style="margin-top: 10px;font-size: 14px;">商详视频 :<span
            style="color: #909399; font-size: 13px;">视频要求：时长3分钟以内；宽高比为16：9。上传后展示在商详图文详情顶部</span></div>
        <div key="uploadfilefiv">
          <uploadfile ref="uploadChatImgthr" :uploadInfo="allmsg.productDetailVideoUrl" :minisize="true" :upkey="'uploadfilefiv'"
            placeholder="上传视频" :limit="1" :accepttyes="'.mp4,.MOV'" uploadVerifyType="视频" />
        </div>



        <div style="margin-top: 10px;font-size: 14px;">商品详情 :<span
            style="color: #909399; font-size: 13px;">介绍商品以提升转化，若未编辑，商品发布后轮播图自动填充图文详情</span></div>
        <div key="uploadfilefor" style="font-size: 14px;">
          快捷编辑
          <div style="color: #909399; font-size: 13px;">仅支持上传图片，最多上传50张，已上传7/50张，拖拽可拖动顺序</div>
          <uploadfile ref="uploadChatImgone" :uploadInfo="allmsg.productDetailPicUrls" :minisize="true" :upkey="'uploadfileone'"
            placeholder="请上传供应商聊天记录" :limit="10000" :accepttyes="'.image/jpg,image/jpeg,image/png'"
            uploadVerifyType="图片" />
        </div>


        <div class="flexrow" style="margin-top: 10px;">
          <div style="width: 80px;font-size: 14px;">商品规格</div>
          <div key="uploadfilefor" style="width: 100%; min-height: 180px; background-color: #eaecf0; padding: 10px 20px;">
            <span style="color: #909399; font-size: 13px;">最多添加2个商品规格类型</span>
            <div style="background-color: white;">
              <div style="display: flex;border-bottom: 1px solid #eee; padding: 10px 20px;">
                <div style="margin-right: auto; align-items: center;" class="flexrow">
                  <!-- //第一个类型 -->
                  <el-select style="width: 200px;" v-model="allmsg.firstCloumnType" placeholder="类型" :collapse-tags="true" clearable>
                    <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                  <el-checkbox style="margin-left: 10px;">添加图片</el-checkbox>
                </div>
                <div style="margin-left: auto; margin: 0 10px;" class="flexrow">
                  <!-- <el-button type="text" style="margin-right: 10px;">下移</el-button> -->
                  <!-- <div>|</div> -->
                  <!-- <el-button type="text" style="margin-left: 10px;">删除规格类型</el-button> -->
                </div>
              </div>

              <div style="background-color: white; width: 100%; min-height: 100px; padding-bottom: 10px;" class="flexrow overline">
                <div style="align-items: center; width: 200px; margin-bottom: auto; margin-left:20px; margin-top:10px;" class="flexrow" v-for="(item, i) in allmsg.firstCloumns"
                  :key="i">
                  <el-input style="width:200px;" :maxlength="200" :value="item"  placeholder="请输入规格名称" clearable />
                  <el-button type="text" style="margin-left: 10px;"  @click="deletetype(i,'skuone',item)">删除</el-button>
                </div>

                <div style="align-items: center; width: 160px; margin-bottom: auto; margin-left:20px; margin-top:10px" class="flexrow" >
                    <el-input style="width:200px;" :maxlength="200" @blur="skuoneblur(skuone)" v-model="skuone"  placeholder="请输入规格名称" clearable />
                  </div>
              </div>


            </div>
          </div>
        </div>


        <div class="flexrow">
          <div style="width: 80px;"></div>
          <div key="uploadfilefor"
            style="width: 100%; min-height: 190px; background-color: #eaecf0; padding: 10px 20px 10px 20px;">
            <div style="background-color: white;">
              <div style="display: flex;border-bottom: 1px solid #eee; padding: 10px 20px;">
                <div style="margin-right: auto; align-items: center;" class="flexrow">
                  <el-select style="width: 200px;" v-model="allmsg.secondCloumnType" placeholder="店铺编号" :collapse-tags="true" clearable>
                    <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                  <el-checkbox style="margin-left: 10px;">添加图片</el-checkbox>
                </div>
                <div style="margin-left: auto; margin: 0 10px;" class="flexrow">
                  <!-- <el-button type="text" style="margin-right: 10px;">下移</el-button> -->
                  <!-- <div>|</div>
                  <el-button type="text" style="margin-left: 10px;">删除规格类型</el-button> -->
                </div>
              </div>

              <div style="background-color: white;  min-height: 100px; width: 100%; padding-bottom: 10px;" class="flexrow overline">
                <div style="align-items: center; width: 200px; margin-bottom: auto; margin-left:20px; margin-top:10px" class="flexrow" v-for="(item, i) in allmsg.secondCloumns"
                  :key="i">
                  <el-input style="width:200px;" :value="item" :maxlength=100 placeholder="请输入规格名称" clearable />
                  <el-button type="text" style="margin-left: 10px;" @click="deletetype(i,'skutwo',item)">删除</el-button>
                </div>

                <div style="align-items: center; width: 160px; margin-bottom: auto; margin-left:20px; margin-top:10px" class="flexrow" >
                    <el-input style="width:200px;" @blur="skutwoblur(skutwo)"  v-model="skutwo" :maxlength=100 placeholder="请输入规格名称" clearable />
                  </div>

              </div>


            </div>
          </div>
        </div>


        <div class="flexrow" style="margin-top: 10px;">
          <div style="width: 80px; font-size: 14px;">价格及库存</div>
          <div key="uploadfilefor" style="width: 100%; min-height: 180px; background-color: #eaecf0; padding: 10px 20px;">
            <span style="color: #909399; font-size: 13px;">请如实填写库存信息，以确保商品可以在承诺发货时间发出，避免可能的物流违规</span>
            <div style="background-color: white;">
              <div style="background-color: white;  min-height: 100px; padding: 10px 20px;">
                <vxe-table border resizable show-overflow :data="allmsg.skus" :span-method="rowspanMethod"
                  :edit-config="{ trigger: 'click', mode: 'row' }">
                  <!-- <vxe-column type="seq" width="60"></vxe-column> -->
                  <vxe-column field="firstCloumnName" :title="allmsg.firstCloumnType" >
                    <!-- <template #edit="{ row }">
                      <vxe-input v-model="row.firstCloumnName" type="text"></vxe-input>
                    </template> -->
                  </vxe-column>
                  <vxe-column field="secondCloumnName" :title="allmsg.secondCloumnType" >
                    <!-- <template #edit="{ row }">
                      <vxe-input v-model="row.secondCloumnName" type="text"></vxe-input>
                    </template> -->
                  </vxe-column>
                  <vxe-column field="inventoryNum" title="当前库存" :edit-render="{}">
                    <template #edit="{ row }">
                      <vxe-input v-model="row.inventoryNum" type="text"></vxe-input>
                    </template>
                  </vxe-column>



                  <!-- <vxe-column field="address" title="改后库存" :edit-render="{}">
                    <template #edit="{ row }">
                      <vxe-input v-model="row.address" type="text"></vxe-input>
                    </template>
                  </vxe-column> -->

                  <vxe-column field="address" title="预售时间" width="170px" v-if="allmsg.preSaleType=='规格预售'">
                    <template #default="{ row }">
                      <!-- <vxe-input v-model="row.address" type="text"></vxe-input> -->
                      <el-date-picker
                        size="mini"
                        style="width: 130px;"
                        v-model="row.skuSealDate"
                        value-format="yyyy-MM-dd"
                        type="date"
                        placeholder="选择日期"
                        :picker-options="pickerOptions"
                      />
                    </template>
                  </vxe-column>

                  <vxe-column field="unitPrice" title="拼单价(元)" :edit-render="{}">
                    <template #edit="{ row }">
                      <vxe-input v-model="row.unitPrice" type="text"></vxe-input>
                    </template>
                  </vxe-column>

                  <vxe-column field="price" title="单买价" :edit-render="{}">
                    <template #edit="{ row }">
                      <vxe-input v-model="row.price" type="text"></vxe-input>
                    </template>
                  </vxe-column>

                  <vxe-column field="image" title="预览图">
                    <template #default="{ row }">
                      <!-- <vxe-input v-model="row.image" type="text"></vxe-input> -->
                      <el-image
                        style="width: 60px; height: 60px"
                        :src="row.image"
                        :preview-src-list="row.image">
                      </el-image>
                    </template>
                  </vxe-column>

                  <vxe-column field="goodsCode" title="规格编码" :edit-render="{}">
                    <template #edit="{ row }">
                      <vxe-input v-model="row.goodsCode" type="text"></vxe-input>
                    </template>
                  </vxe-column>

                  <vxe-column field="skuOnlineStatus" title="状态" >
                    <template #default="{ row }">
                      <!-- <vxe-input v-model="row.skuOnlineStatus" type="text"></vxe-input> -->

                      <el-switch
                        :value="row.skuOnlineStatus=='上架'?true:false"
                        @change="switchtab(row)"
                        active-color="#13ce66"
                        inactive-color="#ff4949">
                      </el-switch>

                    </template>
                  </vxe-column>
                </vxe-table>
              </div>


            </div>
          </div>
        </div>


        <div class="flexrow" style="align-items: center;margin: 10px 0 20px 0;">
          <div style="width: 100px; font-size: 14px;">商品参考价</div>
          <el-input style="width: 200px;" :maxlength="200" v-model="allmsg.productReferencePrice" placeholder="商品参考价">
            <el-button slot="append">元</el-button>
          </el-input>
        </div>


        <div class="flexrow" style="align-items: center;margin: 10px 0 20px 0;">
          <div style="width: 100px; font-size: 14px;">是否预售</div>
          <el-radio-group v-model="allmsg.preSaleType">
            <el-radio :label="'非预售'">非预售</el-radio>
            <el-radio :label="'定时预售'" class="botradio" style="margin-left: 5px;">定时预售</el-radio>
            <el-radio :label="'时段预售'" class="botradio" >时段预售</el-radio>
            <el-radio :label="'规格预售'" class="botradio" >规格预售</el-radio>

          </el-radio-group>
        </div>

        <div class="flexrow" style="align-items: center;margin: 10px 0 20px 0;" v-if="allmsg.preSaleType == '定时预售'">
          <div style="width: 100px; font-size: 14px;">预售时间</div>

          <el-date-picker
            size="mini"
            v-model="allmsg.preSaleTime"
            value-format="yyyy-MM-dd"
            type="date"
            placeholder="选择日期"
            :picker-options="pickerOptions"
          />
          <div style="width: 300px; font-size: 14px; color: #666666;" v-if="allmsg.preSaleTime">
            需在{{allmsg.preSaleTime}} 23:59:59前完成发货
          </div>

        </div>

        <div class="flexrow" style="align-items: center;margin: 10px 0 20px 0;" v-if="allmsg.preSaleType == '时段预售'">
          <div style="width: 100px; font-size: 14px;">预售时间</div>

          <div style="display: flex; flex-direction: row; color: #666666; font-size: 13px; align-items: center;">
            支付成功后
            <el-select style="width: 200px;" v-model="allmsg.preSaleTimeInt" placeholder="请选择天数" :collapse-tags="true" clearable>
              <el-option v-for="item in days" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            天发货

          </div>

        </div>

        <!-- <div class="flexrow" style="align-items: center;margin: 10px 0 20px 0;" v-if="allmsg.preSaleType == '规格预售'">
          <div style="width: 100px; font-size: 14px;">预售时间</div>
          333
        </div> -->

        <div class="flexrow" style="align-items: center;margin: 10px 0 20px 0;">
          <div style="width: 100px; font-size: 14px;">承诺发货时间</div>
          <el-radio-group v-model="allmsg.commitmentSendHours">
            <el-radio :label="0">当日发货</el-radio>
            <el-radio :label="24" style="margin-left: 5px;">24小时</el-radio>
            <el-radio :label="48">48小时</el-radio>
          </el-radio-group>
        </div>


        <div class="flexrow" style="align-items: center;margin: 10px 0 20px 0;">
          <div style="width: 100px; font-size: 14px;">运费模板</div>
          <el-radio-group v-model="allmsg.freightTemplateType">
            <el-radio :label="1">默认模板</el-radio>
            <el-radio :label="2" style="margin-left: 5px;">其他模板</el-radio>
          </el-radio-group>
        </div>


        <div style="display: flex; flex-direction: row;">
          <el-select style="width: 200px; margin-left: 100px;" v-model="allmsg.freightTemplateName" placeholder="店铺编号" :collapse-tags="true" clearable>
            <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-button type="text" style="margin-left: 10px;">新建运费模板</el-button>
        </div>









      </div>

      <div class="fiexd" style="bottom: 0; left: 0; width: 100%; height: 50px; background: white;">
        <div style="display: flex; justify-content: center; align-items: center; width: 100%; height: 100%;">
          <el-button type="primary" style="width: 200px;" @click="submit">保存</el-button>
        </div>
      </div>







    </div>

  </div>
</template>

<script>
import uploadfile from '@/views/operatemanage/pddactualtimedatanew/commponts/uploadfile.vue';
import { getActualTimeProductByTimeId, saveActualTimeProduct } from '@/api/operatemanage/datapdd/actualtimedatapdd.js';
import dayjs from "dayjs";
export default {
  name: 'Vue2demoLjxqdialog',
  components: { uploadfile },
  data() {
    return {
      uploadInfothr: [],
      uploadInfotwo: [],
      uploadInfoone: [],
      uploadInfofor: [],
      skuone: '',
      skutwo: '',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()+ 27 * 24 * 60 * 60 * 1000 || time.getTime() < Date.now()+ 2 * 24 * 60 * 60 * 1000;
        },
      },
      days: [
      {
        label: '3天',
        value: '3'
      },
      {
        label: '4天',
        value: '4'
      },
      {
        label: '5天',
        value: '5'
      },
      {
        label: '6天',
        value: '6'
      },
      {
        label: '7天',
        value: '7'
      },
      {
        label: '8天',
        value: '8'
      },
      {
        label: '9天',
        value: '9'
      },
      {
        label: '10天',
        value: '10'
      },
      {
        label: '11天',
        value: '11'
      },
      {
        label: '12天',
        value: '12'
      },
      {
        label: '13天',
        value: '13'
      },
      {
        label: '14天',
        value: '14'
      },
      {
        label: '15天',
        value: '15'
      },
      ],
      directorlist: [{
        label: '尺寸',
        value: '尺寸'
      },{
        label: '型号',
        value: '型号'
      },{
        label: '款式',
        value: '款式'
      },{
        label: '器型',
        value: '器型'
      },{
        label: '口味',
        value: '口味'
      },{
        label: '色号',
        value: '色号'
      },{
        label: '适用人群',
        value: '适用人群'
      },{
        label: '颜色',
        value: '颜色'
      },{
        label: '容量',
        value: '容量'
      },{
        label: '花型',
        value: '花型'
      },{
        label: '尺码',
        value: '尺码'
      },{
        label: '地点',
        value: '地点'
      },{
        label: '香型',
        value: '香型'
      },{
        label: '货号',
        value: '货号'
      },{
        label: '组合',
        value: '组合'
      },{
        label: '成份',
        value: '成份'
      },{
        label: '版本',
        value: '版本'
      },{
        label: '度数',
        value: '度数'
      },{
        label: '运营商',
        value: '运营商'
      },{
        label: '属性',
        value: '属性'
      },{
        label: '重量',
        value: '重量'
      },{
        label: '地区',
        value: '地区'
      },{
        label: '套餐',
        value: '套餐'
      },{
        label: '类别',
        value: '类别'
      },{
        label: '适用年龄',
        value: '适用年龄'
      },{
        label: '功效',
        value: '功效'
      },{
        label: '品类',
        value: '品类'
      },{
        label: '时间',
        value: '时间'
      },],
      submitmsg: {},
      groupedData: {},

      inputonearr: ['1'],
      radio: 1,
      time: 1677568554956,
      allmsg: {
        firstCloumns: [],
        secondCloumns: []
      },
      routermsg: {}
    };
  },
  filters: {
    timeFormater(val) {
      return dayjs(val).format("YYYY年MM月DD日 HH时mm分ss秒")
    }
  },

  mounted() {
    this.routermsg = this.$route.query;

    this.getljxqlist();

  },

  methods: {
    switchtab(row){
      const index = this.allmsg.skus.indexOf(row);
      let istrue = row.skuOnlineStatus=='上架'?'下架':row.skuOnlineStatus=='下架'?'上架':"";
      this.allmsg.skus[index].skuOnlineStatus = istrue;

    },
    // 通用行合并函数（将相同多列数据合并为一行）
    rowspanMethod ({ row, _rowIndex, column, visibleData }) {
      let fields = ['firstCloumnName']
      let cellValue = row[column.property]
      if (cellValue && fields.includes(column.property)) {
        let prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && prevRow[column.property] === cellValue) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow && nextRow[column.property] === cellValue) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
      // return { rowspan: 1, colspan: 1 }
    },
    async submit(){
      this.submitmsg.oldData = JSON.parse(decodeURIComponent(localStorage.getItem('ljxqolddata')));
      this.submitmsg.newData = {
        ...this.allmsg,
        productVideoUrl: this.arrtoString(this.allmsg.productVideoUrl),
        productDetailVideoUrl: this.arrtoString(this.allmsg.productDetailVideoUrl),
        productExplainVideoUrl: this.arrtoString(this.allmsg.productExplainVideoUrl),

        productDetailPicUrls: this.arrtoArr(this.allmsg.productDetailPicUrls),
        productTurnPicUrls: this.arrtoArr(this.allmsg.productTurnPicUrls),

      };
      let params = {
        ...this.submitmsg
      }
      let res = await saveActualTimeProduct(params);
      if(!res.success){
        return
      }
      this.$message.success('保存成功！')
    },
    deletetype(i,type,name){
      if(i == 0&&this.allmsg.firstCloumns.length==1||i == 0&&this.allmsg.secondCloumns.length==1){
        this.$message.error("最后一列不能删除!")
        return
      }
      if(type=='skuone'){
        this.allmsg.firstCloumns.splice(i,1)


        var newarr = []
        this.allmsg.skus.forEach((item)=>{
          if(item.firstCloumnName == name){
            newarr.push(item)
          }
        })

        const filteredArray = this.allmsg.skus.filter(item => !newarr.includes(item));

        this.allmsg.skus = (filteredArray?.length>0)?filteredArray:[];

      }else if(type=='skutwo'){
        this.allmsg.secondCloumns.splice(i,1)


        var newarr = []
        this.allmsg.skus.forEach((item)=>{
          if(item.secondCloumnName == name){
            newarr.push(item)
          }
        })

        const filteredArray = this.allmsg.skus.filter(item => !newarr.includes(item));

        this.allmsg.skus = (filteredArray?.length>0)?filteredArray:[];

      }

    },
    skuoneblur(e){

      if(this.allmsg.firstCloumns.includes(e)){
        this.skuone = '';
        this.$message({
          type: 'info',
          message: "已存在重复数据！"
        })
        return
      }


      if(this.skuone){
        this.allmsg.firstCloumns.push(e)
        this.skuone = ''
      }



      let newarr = [];
      var bbb = '';
      this.allmsg.skus = this.allmsg.skus?this.allmsg.skus:[]
      this.allmsg.skus.forEach((item,index)=>{
        if(index==0){
          bbb = item.firstCloumnName
        }
        // if((bbb == item.firstCloumnName)&&(index+1<this.allmsg.skus.length/2+1)){
        //   let params = {
        //     firstCloumnName: e,
        //     goodsCode: null,
        //     image: null,
        //     inventoryNum: null,
        //     price: null,
        //     proCode: item.proCode,
        //     secondCloumnName: item.secondCloumnName,
        //     skuOnlineStatus: '上架',
        //     skuSealDate: null,
        //     unitPrice: null,
        //   }
        //   newarr.push(params)
        // }else
        if(this.allmsg.firstCloumns.length==2){
          let params = {
            firstCloumnName: e,
            goodsCode: null,
            image: null,
            inventoryNum: null,
            price: null,
            proCode: item.proCode,
            secondCloumnName: item.secondCloumnName,
            skuOnlineStatus: '上架',
            skuSealDate: null,
            unitPrice: null,
          }
          newarr.push(params)
        }else if((bbb == item.firstCloumnName)&&(index+1<this.allmsg.skus.length/2+1)){
          let params = {
            firstCloumnName: e,
            goodsCode: null,
            image: null,
            inventoryNum: null,
            price: null,
            proCode: item.proCode,
            secondCloumnName: item.secondCloumnName,
            skuOnlineStatus: '上架',
            skuSealDate: null,
            unitPrice: null,
          }
          newarr.push(params)
        }
      })

      this.allmsg.skus = this.allmsg.skus.concat(newarr);

    },
    ischu(number){
      if (number % this.allmsg.firstCloumns.length === 0) {
        return true
      } else {
        return false
      }
    },
    skutwoblur(e){
      if(this.allmsg.secondCloumns.includes(e)){
        this.skutwo = '';
        this.$message({
          type: 'info',
          message: "已存在重复数据！"
        })
        return
      }
      if(this.skutwo){
        this.allmsg.secondCloumns.push(e)
        this.skutwo = ''

        this.changecolumn(e);
      }

    },
    changecolumn(e){
      ////////////////////////
      const array = this.allmsg.skus;
      const newArray = [];
      //且分数组
      function groupBy(arr, property) {
          return arr.reduce((acc, obj) => {
              const key = obj[property];
              if (!acc[key]) {
                  acc[key] = [];
              }
              acc[key].push(obj);
              return acc;
          }, {});
      }

      // 按照 firstCloumnName 属性进行分组
      const groupedData = groupBy(array, "firstCloumnName");

      var result = [];
      var group = [];
      for (const key in groupedData) {

        const addObject = {
            firstCloumnName: groupedData[key][0].firstCloumnName,
            secondCloumnName: e,
            goodsCode: null,
            image: "",
            inventoryNum: null,
            price: null,
            proCode: null,
            skuOnlineStatus: "上架",
            skuSealDate: null,
            unitPrice: null,
        };

          group = groupedData[key];
          group.push(addObject);




          result = [...group,...result,]

      }

      this.groupedData = groupedData;

      this.allmsg.skus = result;
    },
    formatSex(value) {
      if (value === '1') {
        return '男'
      }
      if (value === '0') {
        return '女'
      }
      return ''
    },
    async getljxqlist(){
      let params = {
        // yearMonthDay: "2023-09-08 00:00:00",
        // productCode: "1"

        yearMonthDay: this.routermsg.yearMonthDay,
        productCode: this.routermsg.productCode,
      }
      let res = await getActualTimeProductByTimeId(params);
      if(!res.success){
        return
      }

      // res.data.yearMonthDay = this..yearMonthDay;
      // res.data.proCode = this..proCode;
      let zanmsg = {
        ...res.data,
        proCode: this.routermsg.productCode,
        yearMonthDay: this.routermsg.yearMonthDay,
      }
      localStorage.setItem('ljxqolddata',encodeURIComponent(JSON.stringify(zanmsg)));


      this.allmsg = {
        skus: res.data.skus,
        freightTemplateName: res.data.freightTemplateName,
        freightTemplateType: res.data.freightTemplateType,
        commitmentSendHours: res.data.commitmentSendHours,
        preSaleType: res.data.preSaleType,
        productDiscount: res.data.productDiscount,
        productReferencePrice: res.data.productReferencePrice,
        secondCloumns: res.data.secondCloumns,
        secondCloumnType: res.data.secondCloumnType,
        firstCloumns: res.data.firstCloumns,
        firstCloumnType: res.data.firstCloumnType,
        productDetailPicUrls: this.stringtoArr(res.data.productDetailPicUrls),
        productDetailVideoUrl: this.stringtoArr(res.data.productDetailVideoUrl),
        productExplainVideoUrl: this.stringtoArr(res.data.productExplainVideoUrl),
        productVideoUrl: this.stringtoArr(res.data.productVideoUrl),
        productTurnPicUrls: this.stringtoArr(res.data.productTurnPicUrls),
        productName: res.data.productName,

        proCode: this.routermsg.productCode,
        yearMonthDay: this.routermsg.yearMonthDay,
      }


      this.changecolumndan(res.data.skus);


      // this.allmsg.skus = newArray;

    },
    changecolumndan(array){
      const newArray = [];

      array.forEach((item, index) => {
        let params = {
          firstCloumnName: item.firstCloumnName,
          goodsCode: index+1<array.length?array[index+1].goodsCode:array[0].goodsCode,
          image: index+1<array.length?array[index+1].image:array[0].image,
          inventoryNum: index+1<array.length?array[index+1].inventoryNum:array[0].inventoryNum,
          price: index+1<array.length?array[index+1].price:array[0].price,
          proCode: index+1<array.length?array[index+1].proCode:array[0].proCode,
          secondCloumnName: index+1<array.length?array[index+1].secondCloumnName:array[0].secondCloumnName,
          skuOnlineStatus: index+1<array.length?array[index+1].skuOnlineStatus:array[0].skuOnlineStatus,
          skuSealDate: index+1<array.length?array[index+1].skuSealDate:array[0].skuSealDate,
          unitPrice: index+1<array.length?array[index+1].unitPrice:array[0].unitPrice,
        }

        newArray.push(item); // 将原始元素添加到新数组
        // this.allmsg.skus[index+1] = params;
        newArray.splice(index+1, 0, params);

        // this.allmsg.skus.forEach((item)=>{
        //   newArray.push(item)
        // })
      });


      this.allmsg.skus = newArray;
    },
    arrtoArr(e){
      var a = [];
      if(e.length>0){
        e.map((item)=>{
          a.push(item.url);
        })
      }else{
        a = [];
      }
      return a;
    },
    arrtoString(e){
      var a = ''
      if(e.length>0){
        e.map((item)=>{
          a = item.url;
        })
      }else{
        a = '';
      }
      return a;

    },
    stringtoArr(e){
      var a =[];
      if(Array.isArray(e)){
        e.forEach((item,index)=>{
          // item = {url:item, name: item}
          a[index] = {url:item, name: item}
        })

      }else{
        a = [{url:e, name: e}];
      }
      return a;
    }
  },
};
</script>

<style lang="scss" scoped>
.flexrow {
  display: flex;
  flex-direction: row
}
.overline{
  flex-wrap: wrap;
  overflow-x: hidden;
  white-space: nowrap;

}

.botradio{
  border-bottom: 1px dashed #353131;
}

.fiexd{
  position: fixed;
}
</style>
