<template>
    <my-container >
        <el-tabs v-model="activeName"  style="height: calc(100% - 40px);" >
            <el-tab-pane label="拼多多" name="first"  style="height: 100%;">
                <List v-if="activeName === 'first'"></List>
            </el-tab-pane>
        </el-tabs>
    </my-container >
</template>

<script>
import MyContainer from "@/components/my-container";
import List from './unpaidorder.vue'
export default {
    name: "unpaidOrder",
    components: {
        MyContainer,
        List
    },
    data () {
        return {
            activeName: 'first'
        }
    },
}
</script>

<style scoped lang="scss"></style>