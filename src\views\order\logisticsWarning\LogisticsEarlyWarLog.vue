<template>
    <my-container v-loading="pageLoading">
        <div>
            <div :class="orderErpLogList.length>0? 'divleft':''">
                <div style="cursor:pointer;margin-left:90%; margin-top: 5px;margin-bottom: 2px;">
                    <span @click="orderByList" style="color:#0094ff;display:inline-block;user-select:none;">
                        ⬆⬇{{ isOrder ? "正序" : "倒序" }}
                    </span>
                </div>
                <tbody v-for="orderLog in orderLogList">
                    <tr style="line-height:30px;">
                        <td style="width:150px;height:20px;">{{orderLog.created}}</td>
                        <td style="width:120px;height:20px;">{{orderLog.creator_name}}</td>
                        <td style="width:300px;height:20px;">{{orderLog.name}}</td>
                        <td style="height:20px;">{{orderLog.remark}}</td>
                    </tr>
                </tbody>
            </div>
            <div v-if="(orderErpLogList.length>0)" class="divright">
                <div style="cursor:pointer;margin-left:85%; margin-top: 5px;margin-bottom: 2px;">
                    <span @click="orderByErpList" style="color:#0094ff;display:inline-block;user-select:none;">
                        ⬆⬇{{ isErpOrder ? "正序" : "倒序" }}
                    </span>
                </div>
                <tbody v-for="orderErpLog in orderErpLogList">
                    <tr style="line-height:30px;">
                        <td style="width:150px;height:20px;">{{orderErpLog.createdTime}}</td>
                        <td style="width:50px;height:20px;">{{orderErpLog.typeStr}}</td>
                        <td style="height:20px;">{{orderErpLog.remarks}}</td>
                    </tr>
                </tbody>
            </div>
        </div>
    </my-container>
</template>

<script>
    import MyContainer from '@/components/my-container'
    import { getOrderLogListAsync, getLogisticsEarlyWarLogListAsync } from "@/api/order/logisticsEarlyWarPage";
    export default {
        name: 'OrderLogPage',
        components: { MyContainer },
        props: {
            orderNoInner: { type: String, default: "" },
        },
        data () {
            return {
                orderLogList: [],
                isOrder: true,
                orderErpLogList: [],
                isErpOrder: true,
                pageLoading: false,
            }
        },
        async mounted () {
            await this.initData();
        },
        methods: {
            async initData () {
                this.pageLoading = true
                const res = await getOrderLogListAsync(this.orderNoInner);
                if (!res?.success) {
                    this.pageLoading = false
                    return
                }
                const ress = await getLogisticsEarlyWarLogListAsync(this.orderNoInner);
                this.pageLoading = false
                if (!ress?.success) {
                    return
                }
                this.orderLogList = res.data;
                this.orderErpLogList = ress.data;
            },
            orderByList () {
                this.isOrder = !this.isOrder;
                //进行数组排序
                if (this.isOrder) {
                    this.sortKeyAsc(this.orderLogList, "oa_id");
                } else {
                    this.sortKeyDesc(this.orderLogList, "oa_id");
                }
            },

            orderByErpList () {
                this.isErpOrder = !this.isErpOrder;
                //进行数组排序
                if (this.isErpOrder) {
                    this.sortKeyAsc(this.orderErpLogList, "createdTime");
                } else {
                    this.sortKeyDesc(this.orderErpLogList, "createdTime");
                }
            },

            //正序
            sortKeyAsc (array, key) {
                return array.sort(function (a, b) {
                    var x = a[key];
                    var y = b[key];
                    return ((x < y) ? -1 : (x > y) ? 1 : 0)
                })
            },
            //倒序
            sortKeyDesc (array, key) {
                return array.sort(function (a, b) {
                    var x = a[key];
                    var y = b[key];
                    return ((x > y) ? -1 : (x < y) ? 1 : 0)
                })
            }

        },
    }
</script>

<style scoped>
    .divleft {
        float: left;
        width: 70%;
    }
    .divright {
        display: flex;
        justify-content: center;
        flex-direction: column;
        position: absolute;
        width: 30%;
        right: 0;
        top: 6%;
        transform: translate(0, -50%);
    }
</style>