<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="仓库">
                    <el-select v-model="filter.warehouseCode" style="width: 160px" size="mini" @change="onSearch">
                        <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="日期">
                    <el-date-picker style="width: 280px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        :picker-options="pickerOptions">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.describe" placeholder="操作员" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.workType" style="width: 80px" size="mini" @change="onSearch" clearable
                        placeholder="班次">
                        <el-option label="白班" value="白班"></el-option>
                        <el-option label="晚班" value="晚班"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.postCode" placeholder="岗位编码" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.userName" placeholder="姓名" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-checkbox v-model="filter.isNotUserName" @change="onSearch">包含姓名为空</el-checkbox>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onImport">导入</el-button>
                </el-form-item>
                <el-form-item>
                </el-form-item>
                <el-form-item>
                    <!-- <el-button type="primary" @click="onImportModel">下载模板</el-button> -->
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
                :isSelectColumn="false" :loading="listLoading" :tableHandles="tableHandles1">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
        <el-dialog title="导入" :visible.sync="dialogVisibleUpload" width="40%" v-dialogDrag
            :close-on-click-modal="false">
            <span>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-select v-model="importWarehouseCode" style="width: 150px;margin-right: 20px;" size="mini">
                            <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                        <el-date-picker v-model="importWorkDate" type="date" placeholder="选择日期"
                            style="width:150px;margin-right: 20px;">
                        </el-date-picker>
                        <el-select v-model="importVersion" style="width: 200px;" size="mini">
                            <el-option label="老版本仓库工作量统计" value="老版本仓库工作量统计" />
                            <el-option label="新版本仓库工作量统计" value="新版本仓库工作量统计" />
                        </el-select>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        &nbsp;
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                            action accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-input type="textarea" :autosize="{ minRows: 6, maxRows: 8 }" placeholder="" readonly
                            style="color: red;" v-model="daoruerrormsg">
                        </el-input>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleUpload = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getWarehouseUserWorkDataPageList, importWarehouseUserWorkData, deleteWarehouseUserWorkDataBatch } from '@/api/profit/warehousewages';
const tableCols = [
    { istrue: true, prop: 'workDate', label: '日期', width: '100', sortable: 'custom', formatter: (row) => formatTime(row.workDate, 'YYYY-MM-DD') },
    { istrue: true, prop: 'describe', label: '操作员', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'warehouseName', label: '仓库', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'workType', label: '班次', width: '60', sortable: 'custom' },
    { istrue: true, prop: 'postCode', label: '岗位编码', width: '60', sortable: 'custom' },
    { istrue: true, prop: 'userName', label: '姓名', width: '60', sortable: 'custom' },

    { istrue: true, prop: 'zhiJian', label: '质检', width: '60', sortable: 'custom', formatter: (row) => row.zhiJian == null ? 0 : row.zhiJian },
    { istrue: true, prop: 'anXiangShangJia', label: '按箱上架', width: '60', sortable: 'custom', formatter: (row) => row.anXiangShangJia == null ? 0 : row.anXiangShangJia },
    { istrue: true, prop: 'qiTaJinCang', label: '其他进仓', width: '60', sortable: 'custom', formatter: (row) => row.qiTaJinCang == null ? 0 : row.qiTaJinCang },
    { istrue: true, prop: 'qiTaJinCangShuLiang', label: '其他进仓数量', width: '80', sortable: 'custom', formatter: (row) => row.qiTaJinCangShuLiang == null ? 0 : row.qiTaJinCangShuLiang },
    { istrue: true, prop: 'caiGouTuiHuo', label: '采购退货', width: '60', sortable: 'custom', formatter: (row) => row.caiGouTuiHuo == null ? 0 : row.caiGouTuiHuo },
    { istrue: true, prop: 'xiaoTuiShangJia', label: '销退上架', width: '60', sortable: 'custom', formatter: (row) => row.xiaoTuiShangJia == null ? 0 : row.xiaoTuiShangJia },
    { istrue: true, prop: 'xiaoTuiShangJiaShuLiang', label: '销退上架数量', width: '80', sortable: 'custom', formatter: (row) => row.xiaoTuiShangJiaShuLiang == null ? 0 : row.xiaoTuiShangJiaShuLiang },
    { istrue: true, prop: 'jianHuo', label: '拣货', width: '60', sortable: 'custom', formatter: (row) => row.jianHuo == null ? 0 : row.jianHuo },
    { istrue: true, prop: 'jianHuoShuLiang', label: '拣货数量', width: '80', sortable: 'custom', formatter: (row) => row.jianHuoShuLiang == null ? 0 : row.jianHuoShuLiang },
    { istrue: true, prop: 'jianHuo_DingDanShu', label: '拣货_订单数', width: '80', sortable: 'custom', formatter: (row) => row.jianHuo_DingDanShu == null ? 0 : row.jianHuo_DingDanShu },
    { istrue: true, prop: 'zhengXiangJian', label: '整箱拣', width: '60', sortable: 'custom', formatter: (row) => row.zhengXiangJian == null ? 0 : row.zhengXiangJian },
    { istrue: true, prop: 'ckyh_Zong', label: '出库验货_总', width: '80', sortable: 'custom', formatter: (row) => row.ckyh_Zong == null ? 0 : row.ckyh_Zong },
    { istrue: true, prop: 'ckyhShuLiang', label: '出库验货数量', width: '80', sortable: 'custom', formatter: (row) => row.ckyhShuLiang == null ? 0 : row.ckyhShuLiang },
    { istrue: true, prop: 'ckyh_Pczp', label: '出库验货_排除赠品', width: '100', sortable: 'custom', formatter: (row) => row.ckyh_Pczp == null ? 0 : row.ckyh_Pczp },
    { istrue: true, prop: 'faHuo', label: '发货', width: '60', sortable: 'custom', formatter: (row) => row.faHuo == null ? 0 : row.faHuo },
    { istrue: true, prop: 'faHuoShuLiang', label: '发货数量', width: '80', sortable: 'custom', formatter: (row) => row.faHuoShuLiang == null ? 0 : row.faHuoShuLiang },
    { istrue: true, prop: 'faHuo_Pczpsl', label: '发货_排除赠品数量', width: '100', sortable: 'custom', formatter: (row) => row.faHuo_Pczpsl == null ? 0 : row.faHuo_Pczpsl },
    { istrue: true, prop: 'daBaoShuLiang', label: '打包数量', width: '80', sortable: 'custom', formatter: (row) => row.daBaoShuLiang == null ? 0 : row.daBaoShuLiang },
    { istrue: true, prop: 'daBao_Zong', label: '打包_总', width: '80', sortable: 'custom', formatter: (row) => row.daBao_Zong == null ? 0 : row.daBao_Zong },
    { istrue: true, prop: 'shouHou', label: '售后', width: '60', sortable: 'custom', formatter: (row) => row.shouHou == null ? 0 : row.shouHou },
    { istrue: true, prop: 'shouHouShuLiang', label: '售后数量', width: '60', sortable: 'custom', formatter: (row) => row.shouHouShuLiang == null ? 0 : row.shouHouShuLiang },
    { istrue: true, prop: 'shouHouDanShu', label: '售后单数', width: '60', sortable: 'custom', formatter: (row) => row.shouHouDanShu == null ? 0 : row.shouHouDanShu },
    { istrue: true, prop: 'yiXiang', label: '移箱', width: '60', sortable: 'custom', formatter: (row) => row.yiXiang == null ? 0 : row.yiXiang },
    { istrue: true, prop: 'panDian', label: '盘点', width: '60', sortable: 'custom', formatter: (row) => row.panDian == null ? 0 : row.panDian },
    { istrue: true, prop: 'qiTaChuKu', label: '其他出库', width: '60', sortable: 'custom', formatter: (row) => row.qiTaChuKu == null ? 0 : row.qiTaChuKu },
    { istrue: true, prop: 'fhcjdjShuLiang', label: '发货抽检登记数量', width: '80', sortable: 'custom', formatter: (row) => row.fhcjdjShuLiang == null ? 0 : row.fhcjdjShuLiang },
    { istrue: true, prop: 'yanHuoGongZuoLiang', label: '验货工作量', width: '80', sortable: 'custom', formatter: (row) => row.yanHuoGongZuoLiang == null ? 0 : row.yanHuoGongZuoLiang },
    { istrue: true, prop: 'faHuoChouJianDengJi', label: '发货抽检登记', width: '80', sortable: 'custom', formatter: (row) => row.faHuoChouJianDengJi == null ? 0 : row.faHuoChouJianDengJi },
    { istrue: true, prop: 'chengZhong', label: '称重', width: '80', sortable: 'custom', formatter: (row) => row.chengZhong == null ? 0 : row.chengZhong },
    { istrue: true, prop: 'kuaiSuShangJiaShuLiang', label: '快速上架数量', width: '80', sortable: 'custom', formatter: (row) => row.kuaiSuShangJiaShuLiang == null ? 0 : row.kuaiSuShangJiaShuLiang },
    { istrue: true, prop: 'xiaJiaZhuangXiang', label: '下架装箱', width: '80', sortable: 'custom', formatter: (row) => row.xiaJiaZhuangXiang == null ? 0 : row.xiaJiaZhuangXiang },
    { istrue: true, prop: 'zhuangXiang', label: '装箱', width: '80', sortable: 'custom', formatter: (row) => row.zhuangXiang == null ? 0 : row.zhuangXiang },
    { istrue: true, prop: 'yuBaoJiaGong009', label: '预包加工0.09', width: '80', sortable: 'custom', formatter: (row) => row.yuBaoJiaGong009 == null ? 0 : row.yuBaoJiaGong009 },
    { istrue: true, prop: 'yuBaoJiaGong009ShuLiang', label: '预包加工0.09数量', width: '80', sortable: 'custom', formatter: (row) => row.yuBaoJiaGong009ShuLiang == null ? 0 : row.yuBaoJiaGong009ShuLiang },
    { istrue: true, prop: 'daYinKuaiDiDanShuLiang', label: '打印快递单数量', width: '80', sortable: 'custom', formatter: (row) => row.daYinKuaiDiDanShuLiang == null ? 0 : row.daYinKuaiDiDanShuLiang },
    { istrue: true, prop: 'yiHuo', label: '移货', width: '80', sortable: 'custom', formatter: (row) => row.yiHuo == null ? 0 : row.yiHuo },
    { istrue: true, prop: 'jiaGongPinZhong1_3ShuLiang', label: '加工品种1-3数量', width: '80', sortable: 'custom', formatter: (row) => row.jiaGongPinZhong1_3ShuLiang == null ? 0 : row.jiaGongPinZhong1_3ShuLiang },
    { istrue: true, prop: 'jiaGongPinZhong4_9ShuLiang', label: '加工品种4-9数量', width: '80', sortable: 'custom', formatter: (row) => row.jiaGongPinZhong4_9ShuLiang == null ? 0 : row.jiaGongPinZhong4_9ShuLiang },
    { istrue: true, prop: 'jiaGongPinZhong10ShuLiang', label: '加工品种10以上数量', width: '80', sortable: 'custom', formatter: (row) => row.jiaGongPinZhong10ShuLiang == null ? 0 : row.jiaGongPinZhong10ShuLiang },
    
    { istrue: true, prop: 'kuaJingFeiJiHeDaBaoDanShu', label: '跨境（飞机盒）打包0.13元/单单数', width: '120', sortable: 'custom', formatter: (row) => row.kuaJingFeiJiHeDaBaoDanShu == null ? 0 : row.kuaJingFeiJiHeDaBaoDanShu },
    { istrue: true, prop: 'tieDanDanShu', label: '贴单0.045元/单单数', width: '80', sortable: 'custom', formatter: (row) => row.tieDanDanShu == null ? 0 : row.tieDanDanShu },
    { istrue: true, prop: 'daBaoCiShu', label: '打包次数', width: '80', sortable: 'custom', formatter: (row) => row.daBaoCiShu == null ? 0 : row.daBaoCiShu },

    
    { istrue: true, prop: 'batchNumber', label: '批次号', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'createdTime', label: '导入时间', width: '150', sortable: 'custom' },
    {
        istrue: true, type: 'button', label: '操作', width: '100',
        btnList: [
            { label: "删除批次", handle: (that, row) => that.onDeleteBatch(row.batchNumber) },
        ]
    },
];
const tableHandles1 = [
    //{ label: "导入", handle: (that) => that.onImport() },
    // { label: "导出", handle: (that) => that.onExport() },
];
export default {
    name: 'warehouseuserworkdata',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow },
    props: ['myWarehouseList', 'myOnePostNameList'],
    data() {
        return {
            that: this,
            filter: {
                timerange: [
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                ],
                startDate: null,
                endDate: null,
                warehouseCode: 10361546,//诚信仓
                isNotUserName: false,
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "workDate", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            pickerOptions: {
                shortcuts: [
                    {
                        text: '昨天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime());
                            picker.$emit('pick', [start, start]);
                        }
                    }, {
                        text: '近三天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime());
                            const end = new Date(new Date(tdate.toLocaleDateString()));
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一周',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 5);
                            const end = new Date(new Date(tdate.toLocaleDateString()).getTime() + 3600 * 1000 * 24 * 5);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一个月',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 31);
                            console.log("获取前一个月的时间", tdate.getDay());
                            const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
            },
            tableHandles1: tableHandles1,
            dialogVisibleUpload: false,
            fileList: [],
            fileparm: {},
            uploadLoading: false,
            dialogHisVisible: false,
            dialogHisVisible1: false,
            dialogHisVisible2: false,
            importWarehouseCode: null,
            importWorkDate: null,
            importVersion: "老版本仓库工作量统计",
            daoruerrormsg: null,
        };
    },
    async mounted() {
        await this.onSearch()
    },
    methods: {
        //查询第一页
        async onSearch() {
            console.log(this.filter)
            if (!this.filter.timerange) {
                this.$message({ message: "请选择日期", type: "warning", });
                return;
            }
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.$message({ message: "请先选择日期", type: "warning" });
                return false;
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const res = await getWarehouseUserWorkDataPageList(params)
            this.listLoading = false;
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            data.forEach(d => {
                d._loading = false;
            })
            this.list = data;
        },
        //排序查 询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            console.log(rows)
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onImport() {
            this.daoruerrormsg = "";
            this.importWarehouseCode = this.filter.warehouseCode;
            if (!this.importWorkDate)
                this.importWorkDate = formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD");
            this.dialogVisibleUpload = true;
        },
        async onImportModel() {
            window.open("/static/excel/profit/仓库薪资-导入聚水潭数据源模板.xlsx", "_blank");
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("workDate", formatTime(this.importWorkDate, "YYYY-MM-DD"));
            form.append("warehouseCode", this.importWarehouseCode.toString());
            form.append("version", this.importVersion);
            form.append("warehouseName", this.myWarehouseList.find(f => f.value == this.importWarehouseCode)?.label);
            console.log(form, 'form')
            var res = await importWarehouseUserWorkData(form);
            if (res?.success) {
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
                this.daoruerrormsg = "";
            }
            else {
                this.daoruerrormsg = res?.msg;
            }
            this.uploadLoading = false
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            //this.dialogVisibleUpload = false;
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadRemove(file, fileList) {
            this.fileList = [];
        },
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            if (!this.importWarehouseCode) {
                this.$message({ message: "请先选择仓库", type: "warning" });
                return false;
            }
            if (!this.importWorkDate) {
                this.$message({ message: "请先选择日期", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        async onDeleteBatch(batchnum) {
            this.$confirm('确定要执行删除吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await deleteWarehouseUserWorkDataBatch({ batchnum: batchnum });
                if (res?.success) {
                    this.$message({ type: 'success', message: '删除成功，查看最新薪资数据需要重新点击计算!' });
                    await this.onSearch();
                }
            }).catch(() => {
            });
        },
    },
};
</script>

<style lang="scss" scoped></style>
