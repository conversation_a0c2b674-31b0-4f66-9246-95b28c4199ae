<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="快递路线" name="tab1" style="height: 100%;">
                <expressroute :filter="filter" ref="expressroute" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="24小时支-揽" name="tab2" style="height: 100%;" lazy>
                <expresslanshou :filter="filter" ref="expresslanshou" style="height: 100%;" />
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import expressroute from './expressroute.vue'
import expresslanshou from './expresslanshou.vue'

export default {
    name: "expressdataanalyseindex",
    components: { MyContainer, expressroute, expresslanshou, },
    data() {
        return {
            that: this,
            pageLoading: false,
            filter: {
            },
            selids: [],
            activeName: 'tab1',
        };
    },
    mounted() {
    },
    methods: {
    },


};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
