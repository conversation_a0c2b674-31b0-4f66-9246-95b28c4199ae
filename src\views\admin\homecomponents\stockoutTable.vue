<template>
<my-container>
  <el-table :data="list" v-loading="pagelodaing" height="500">
    <el-table-column label="商品编码" width="130">
      <template slot-scope="scope">
        <el-tag>
          {{ '上架时长：' + scope.row.createdMonth + "个月"}}
        </el-tag>
        <el-button type="text" size="mini" @click="linkstockout(scope.row.goodsCode)">         
            {{ scope.row.goodsCode }}         
        </el-button>
      </template>
    </el-table-column>
    <el-table-column label="编码名称" show-overflow-tooltip prop="goodsName" min-width="110"></el-table-column>
    <el-table-column label="图片" width="90" align="center">
      <template slot-scope="scope">
        <el-image :src="scope.row.goodsImage" @click="showImg(scope.row.goodsImage)" style="max-width: 50px; max-height: 50px;" fit="fill" :lazy="true"></el-image>
      </template>
    </el-table-column>
    <el-table-column label="缺货数量" width="80" align="center">
      <template slot-scope="scope">
        {{ scope.row.waitGoodNumCurrent | mynum}}
      </template>
    </el-table-column>
    <el-table-column label="压单数" width="80" align="center">
      <template slot-scope="scope">
        {{ scope.row.waitOrderNumCurrent | mynum}}
      </template>
    </el-table-column>
  </el-table>

  <el-image-viewer v-if="showGoodsImage" :url-list="imgList" :on-close="closeFunc" style="z-index:9999;"/>

  </my-container>
</template>

<script>
import { getHomeStockout } from '@/api/inventory/abnormal'
import MyContainer from "@/components/my-container";
import { formatmoney } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import {formatNoLink} from "@/utils/tools";
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'

const tableCols =[
  {istrue:true,prop:'goodsCode',label:'商品编码', width:'110',type:'html',formatter:(row)=>formatNoLink(row.waitOrderNumCurrent)},
  {istrue:true,prop:'goodsImage',label:'图片', width:'110', type:'image'},
  {istrue:true,prop:'waitGoodNumCurrent',label:'缺货数量', width:'110',},
  {istrue:true,prop:'waitOrderNumCurrent',label:'压单数', width:'110',},
]

//格式化数值列：大于1 时，去掉小数点，小于1时保留小数点
var myformatnum = function (value) {
  var num= formatmoney(
    Math.abs(value) > 1 ? Math.round(value,2) : Math.round(value, 1)
   );
  return num
};
export default {
  components: {cesTable, MyContainer, ElImageViewer},
  props:{
      stockoutList:[],
  },
  filters:{
    mynum(val) {
        return myformatnum(val)
    }
  },
  watch: {
  stockoutList: {
    deep: true,
    handler(val) {   
      this.getlist(val)
    }
  }
  },
  data() {
    return {
      that:this,
      list: null,
      mgList:[],
      tableCols:tableCols,
      pagelodaing:false,
      showGoodsImage:false,
    }
  },
  mounted() {
    //this.getstockoutlist()
  },
  methods: {
      // async getstockoutlist(){
      //       this.pagelodaing = true
      //       const res = await getHomeStockout();
      //       this.pagelodaing = false
      //       if (!res?.code){
      //           return
      //       }
      //       const data = res.data.list
      //       this.list = data
      //   },
        async linkstockout(row){
            if(row!=null){
            this.$router.push({path: '/inventory/stockoutplatenew', query: {goodsCode: row}})
            }
        },
        async showImg(e){
          this.showGoodsImage = true;
          this.imgList = [];
          this.imgList.push(e);
        },
        async closeFunc(){
          this.showGoodsImage = false;
      },
      getlist(val){
          this.pagelodaing = true
          this.list = val//.slice(0, 8)
          this.pagelodaing = false
      }
  }
}
</script>

<style lang="scss" scoped>
.el-table--scrollable-x .el-table__body-wrapper {
    overflow: auto;
}
::v-deep .el-tag--mini{
  border-color: #409EFF;
}
</style>
