<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <!-- <el-button style="padding: 0;margin:0;" clearable>
                <el-radio-group v-model="importTimeSel" @change="importTimeSelChange">
                    <el-radio-button label="1">昨天至今日</el-radio-button>
                    <el-radio-button label="2">近7天</el-radio-button>
                    <el-radio-button label="3">近14天</el-radio-button>
                    <el-radio-button label="4">近30天</el-radio-button>
                </el-radio-group>
            </el-button> -->
            <el-button style="padding: 0;margin:0;border:none;">
                <el-date-picker style="width:220px;height: 30px;" 
                v-model="filter.importTimeRange" type="datetimerange" 
                format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" 
                start-placeholder="开始时间" end-placeholder="结束时间" 
                @change="importTimeSelChangePick" clearable
                 :picker-options="pickerOptions"
                ></el-date-picker>
          
                <el-select style="width:270px" v-model="filter.warehouses" placeholder="选择仓库" clearable multiple  collapse-tags>
                    <el-option v-for="item in warehouseList" :key="item.value" :label="item.label" :value="item.value" clearable />
                </el-select>
           
                <el-input style="width:220px;" v-model.trim="filter.position"  clearable  :maxlength="40">
                    <el-select style="width:90px;" v-model="filter.positionMode" slot="prepend" >
                        <el-option label="仓位等于" value="eq"></el-option>
                        <el-option label="仓位起始" value="like"></el-option>
                    </el-select>
                </el-input>
          
                <el-input style="width:230px;" v-model.trim="filter.goodsCode" placeholder="" clearable   :maxlength="40">
                    <el-select style="width:110px;" v-model="filter.goodsCodeMode" slot="prepend" >
                        <el-option label="商品编码等于" value="eq"></el-option>
                        <el-option label="商品编码包含" value="like"></el-option>
                    </el-select>
                </el-input>
          
                <el-input style="width:180px;" v-model.trim="filter.goodsName" placeholder="商品名称关键字" clearable   :maxlength="40"/>
            </el-button>
        </template>

        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true'
             @summaryClick='onsummaryClick' 
         :isSelectColumn='true' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :loading="listLoading">
            <template slot='extentbtn' style="height:100px">
                <el-button-group>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="openBatchImport">商品仓位、商品拣货日志导入</el-button>
                    <el-button type="primary" @click="warhouseFormVisible=true">仓库维护</el-button>

                    
                    <!-- <el-button type="primary" @click="importData('导入商品仓位','1')">导入商品仓位</el-button>
                    <el-button type="primary" @click="importData('导入商品操作日志','2')">导入商品操作日志</el-button>
                    <span class="titleRatio">
                        <li class="el-alert__icon el-icon-warning"></li>1.导入商品仓位文件时需转换为xlsx格式！ 2.商品仓位导入作业完成之后才能进行商品操作日志的导入！
                    </span> -->
                </el-button-group>

            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog :title="importTitle" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
            <span>
                <el-alert style="height:30px;" :title="'1.导入商品仓位文件时需转换为xlsx格式！ 2.商品仓位导入作业完成之后才能进行商品操作日志的导入！'" type="warning" show-icon :closable="false">
                </el-alert>
            </span>
            <span v-loading="importLoading">
                <el-button style="padding: 0;margin: 0;" v-if="importTitle=='导入商品仓位'">
                    <el-select style="width:250px" v-model="importWarehouse" placeholder="选择仓库" clearable>
                        <el-option v-for="item in warehouseList" :key="item.value" :label="item.label" :value="item.value" clearable />
                    </el-select>
                </el-button>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx" :on-success="uploadSuccess" :http-request="uploadFile" :file-list="fileList">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传</el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog title="趋势图" :visible.sync="dialogMapVisible.visible" width="80%">
            <div>
                <div>
                    <el-button style="padding: 0;margin:0;">
                        <el-date-picker style="width:220px;height: 30px;" v-model="echarImportTimeRange" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" @change="changeChartTime" clearable></el-date-picker>
                    </el-button>
                </div>
                <div>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data" ref="buschar"></buschar>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="商品仓位、商品拣货日志导入" :visible.sync="batchImportVisible" width="80%" :close-on-click-modal="false" v-loading="importLoading">
            
            <el-table  border :data="batchData"  row-key="id">
                   <el-table-column
                        prop="fileType"
                        label="文件类型"
                        width="100">
                         <template slot-scope="scope">
                            <el-select v-model="scope.row.fileType">
                                <el-option value="商品仓位" label="商品仓位"></el-option>
                                <el-option value="商品日志" label="商品日志"></el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column                       
                        column-key="id"
                        label="文件名"
                        >
                        <template slot-scope="scope">
                            <el-upload v-if="!(scope.row.fileList && scope.row.fileList.length>0)"  :key="scope.row.id"   :class="(scope.row.fileList && scope.row.fileList.length>0)?'rowupload20230101113501':'rowupload20230101113502'"                       
                                action="/api/uploadnew/file/UploadCommonFileAsync"                                                                          
                                :before-upload="beforeAvatarUpload"
                                :limit="1"     
                                              
                                :file-list="scope.row.fileList"
                                :on-success="handleAvatarSuccess"
                                accept=".xlsx">
                                <el-button size="mini" 
                                    type="primary" @click="currentRow=scope.row">点击上传</el-button>
                            </el-upload>
                            <template v-else>
                                {{scope.row.fileList[0].name}}
                                  <el-button type="text" icon="el-icon-delete" @click="scope.row.fileList=[]"></el-button>
                            </template>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="warehouse"
                        label="仓库"
                        width="290px">
                        <template slot-scope="scope">
                            <el-select v-if="scope.row.fileType && scope.row.fileType=='商品仓位' "  v-model="scope.row.warehouse" style="width:270px;" clearable>
                                <el-option v-for="item in warehouseList" :key="item.value" :label="item.label" :value="item.value" clearable />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column                      
                        label=""
                        align="center"
                        width="104px">
                         <template slot="header" >
                            <el-button type="primary" @click="batchData.push({id: new Date().valueOf().toString()  ,fileType:'',fileList:[],warehouse:''})">添加一行</el-button>
                        </template>
                        <template slot-scope="scope">
                            <el-button type="danger" @click="deleteRow(scope)">删除</el-button>
                        </template>
                    </el-table-column>
            </el-table>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="importBatchData">开始导入</el-button>
            </span>
        </el-dialog>

        <el-dialog title="统计趋势图" :visible.sync="sumChart.visible" width="80%">
            <div>    
                <buschar v-if="sumChart.visible" :analysisData="sumChart.data" ref="sumChartBuschar"></buschar>               
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="sumChart.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="仓库维护"  :visible.sync="warhouseFormVisible" :close-on-click-modal="false" v-loading="warhouseFormLoading" width="600px" >
              <el-table  border :data="warehouseDtos" >                  
                    <el-table-column
                        prop="value"
                        label="仓库名称"
                        >
                        <template slot-scope="scope">
                            <el-input v-model.trim="scope.row.value" :maxlength="20"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column                      
                        label=""
                        align="center"
                        width="104px">
                         <template slot="header" >
                            <el-button type="primary" @click="warehouseDtos.push({value:''})">添加一行</el-button>
                        </template>
                        <template slot-scope="scope">
                            <el-button type="danger" @click="deleteWarehouseRow(scope)">删除</el-button>
                        </template>
                    </el-table-column>
            </el-table>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="warhouseFormSave">保存</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>

<script>
    import { queryPositionAnalysis, showPositionAnalysis, showSummaryPositionAnalysis,
    getWarehouseList,SetWarehouseList, importPositionGoodsAsync,importPositionGoodsBatchAsync } from '@/api/storehouse/storehouse'
    import MyContainer from '@/components/my-container'
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import buschar from '@/components/Bus/buschar'
    import dayjs from "dayjs";
    import { formatTime } from "@/utils";
    import {       
        pickerOptions
    } from "@/utils/tools";
    const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
    const endTime = formatTime(new Date(), "YYYY-MM-DD");
    const tableCols = [
        { istrue: true, prop: 'importTime', label: '日期', width: '100', sortable: 'custom',formatter:(row)=>formatTime(row.importTime,"YYYY-MM-DD") },
        { istrue: true, prop: 'warehouse', label: '仓库', width: '200', sortable: 'custom', },
        { istrue: true, prop: 'position', label: '仓位', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'goodsCode', label: '商品编码', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'goodsName', label: '商品名称', minwidth: '150', sortable: 'custom' },
        { istrue: true, prop: 'goodsQty', label: '商品数量', width: '100', sortable: 'custom' ,summaryEvent: true},
        { istrue: true, prop: 'pickQty', label: '拣货数量', width: '100', sortable: 'custom'  ,summaryEvent: true},
        { istrue: true, prop: 'inventoryFunds', label: '库存资金', width: '100', sortable: 'custom' ,summaryEvent: true },
        { istrue: true, prop: 'movingMoney', label: '动销资金', width: '100', sortable: 'custom'  ,summaryEvent: true},
        { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
    ];

    export default {
        name: 'PositionAnalysis',
        components: { cesTable, MyContainer, MyConfirmButton, buschar },
        data () {
            return {
                pickerOptions:pickerOptions,
                that: this,
                filter: {
                    importTimeRange: [startTime, endTime],
                    importStartTime: null,
                    importEndTime: null,
                    warehouse: null,
                    warehouses:[],
                    position: null,
                    goodsCode: null,
                    goodsName: null,
                    positionMode:"eq",
                    goodsCodeMode:"eq"
                },
                lastFilter:{},
                echartFilter: {
                    importStartTime: null,
                    importEndTime: null,
                    warehouse: null,
                    warehouses:[],
                    position: null,
                    goodsCode: null,
                    goodsName: null
                },
                importTimeSel: "",
                echarImportTimeRange: [null, null],
                warehouseList: [],
                list: [],
                summaryarry: {},
                pager: { OrderBy: "id", IsAsc: false },
                tableCols: tableCols,
                total: 0,
                sels: [],
                listLoading: false,
                pageLoading: false,
                addLoading: false,
                importTitle: "",
                dialogVisible: false,
                importLoading: false,
                importDataType: "",
                importWarehouse: "",
                fileList: [],
                fileparm: {},
                dialogMapVisible: { visible: false, title: "", data: [] },
                sumChart: { visible: false, title: "", data: [] },
                batchImportVisible:false,//批量导入
                batchData:[],
                currentRow:null,
                warehouseDtos:[],
                warhouseFormVisible:false,
                warhouseFormLoading:false,
            }
        },
        async mounted () {
            const res = await getWarehouseList();

            this.warehouseList = res.data;
            this.warehouseDtos=[...res.data];
            await this.getlist();
        },
        methods: {
            async onsummaryClick(property) { 

                let smData=await showSummaryPositionAnalysis(this.lastFilter);
                if(smData && smData.success){
                    this.sumChart.data=smData.data;
                    this.sumChart.visible=true;      
                    this.$nextTick(()=>{
                        this.$refs.sumChartBuschar.initcharts();
                    });                    
                }
               
            },
            //批量导入
            async importBatchData(){
                if(this.batchData && this.batchData.length>0){
                    let postData=[];
                    let boolCw=false;
                    let boolLog=false;
                    for(let i=0;i<this.batchData.length;i++){
                        let row =this.batchData[i];
                        let rowIndex=`第${i+1}行，`;
                        if(!row.fileType){
                            this.$alert(rowIndex+"请选择文件类型！");        
                            return false;
                        }else if(row.fileType=="商品仓位"){
                            boolCw=true;
                            if(!row.warehouse){
                                this.$alert(rowIndex+"文件类型为【商品仓位】时请选择【仓库】！");  
                                return false;
                            }
                        }else if(row.fileType=="商品日志"){
                            boolLog=true;
                        }else{
                            this.$alert(rowIndex+"请选择正确的文件类型！");        
                            return false;
                        }

                        if(!row.fileList || row.fileList.length==0){
                            this.$alert(rowIndex+"请上传文件，如果正在上传中，请等待文件上传完成！");        
                            return false;
                        }

                        postData.push({
                            fileType:row.fileType,
                            warehouse:row.warehouse,
                            fileUrl:row.fileList[0].url,
                            fileName:row.fileList[0].name,
                            fileSize:row.fileList[0].fileSize
                        });
                    }

                    if(boolLog && !boolCw){
                        this.$alert("商品操作日志必须与仓位一起导入！");      
                        return false; 
                    }

                    this.importLoading = true;
                    const res = await importPositionGoodsBatchAsync(postData);
                    this.importLoading = false;
                    if(res && res.success)
                    {
                        this.$message({ message: '上传成功,正在导入中...', type: "success" });
                        this.batchData=[];
                        this.batchImportVisible=false;
                    }
                   
                }
                else{
                    this.$message.error("请添加要导入的数据！");
                }
            },
            openBatchImport(){
                this.batchImportVisible=true;
            },
            beforeAvatarUpload:function(file){
                if(file.name && file.name.length>4 && file.name.substring(file.name.length-4)=="xlsx")
                {
                    return true;
                }
                else{
                    this.$message.error('仅支持xlsx格式的excel文件');
                    return false;
                }
            },
            handleAvatarSuccess: function (response, file, fileList) {

                if (response && response.success && response.data.url) {                 
                  console.log(file);
                    var tempList = [];                  
                    tempList.push({ name: file.name, url: response.data.url ,fileSize:file.size});
                    this.currentRow.fileList=tempList;
                   
                }
            },
            deleteRow(scope){               
                this.batchData.splice(scope.$index,1);
            },
            deleteWarehouseRow(scope){
                this.warehouseDtos.splice(scope.$index,1);
            },
            async warhouseFormSave(){
                if( !(this.warehouseDtos && this.warehouseDtos.length>0)) {
                    this.$alert("请填维护仓库信息，且仓库信息不能为空！");
                    return;
                }


                let dtos=[];
                let errMsg='';
                this.warehouseDtos.forEach(x=>{
                    if(!x.value){
                        errMsg="请填维护仓库信息，且仓库名称信息不能为空！";
                    }
                    if(x.value.indexOf(',')>-1||x.value.indexOf(' ')>-1){
                        errMsg="仓库名称不允许逗号、空格！";
                    }
                    dtos.push({"label":x.value,"value":x.value});
                });

                if(!errMsg){
                    //查重
                    for(let i=0;i<this.warehouseDtos.length;i++){
                        for(let j=i+1;j<this.warehouseDtos.length;j++){
                            //debugger;
                            if(this.warehouseDtos[i].value==this.warehouseDtos[j].value){
                                errMsg="仓库名称重复，"+this.warehouseDtos[i].value;
                                break;
                            }
                        }
                        if(errMsg)
                            break;
                    }
                }

                if(errMsg){
                  
                    this.$alert(errMsg);
                    return;
                }
                
                this.warhouseFormLoading=true;

                let rlt=await SetWarehouseList(dtos);
                if(rlt && rlt.success){
                    this.warehouseList=rlt.data;

                    this.$message.success("仓库信息维护成功！");
                    this.warhouseFormVisible=false;
                }
                this.warhouseFormLoading=false;

            },
            async onSearch () {
                this.$refs.pager.setPage(1)
                await this.getlist()
            },
            async getlist () {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true;
                const res = await queryPositionAnalysis(params);
                this.listLoading = false;
                if (!res?.success) { return };
                this.total = res.data.total;
                this.summaryarry = res.data.summary;
                const data = res.data.list;
                data.forEach(d => { d._loading = false });
                this.list = data;

            },
            //获取查询条件
            getCondition () {
                if (this.filter.importTimeRange && this.filter.importTimeRange.length > 1) {
                    this.filter.importStartTime = this.filter.importTimeRange[0];
                    this.filter.importEndTime = this.filter.importTimeRange[1];
                } else {
                    this.filter.importStartTime = null;
                    this.filter.importEndTime = null;
                }
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }

                this.lastFilter={...this.filter};

                return params;
            },
            async sortchange (column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch();
            },
            selsChange: function (sels) {
                this.sels = sels
            },
            async showchart (row) {
                let oneDayTime = 24 * 60 * 60 * 1000;
                this.echarImportTimeRange = [
                    formatTime(new Date(new Date(row.importTime).getTime() - 59 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                    formatTime(new Date(row.importTime), "YYYY-MM-DD 23:59:59")
                ];

                this.echartFilter.importStartTime = this.echarImportTimeRange[0];
                this.echartFilter.importEndTime = this.echarImportTimeRange[1];
                this.echartFilter.warehouse = row.warehouse;
                this.echartFilter.position = row.position;
                this.echartFilter.goodsCode = row.goodsCode;
                this.echartFilter.goodsName = row.goodsName;
                await this.searchChart(this.echartFilter);
            },
            async changeChartTime () {
                this.echartFilter.importStartTime = this.echarImportTimeRange[0];
                this.echartFilter.importEndTime = this.echarImportTimeRange[1];
                await this.searchChart(this.echartFilter);
            },
            async searchChart (params) {
                let that = this;
                const res = await showPositionAnalysis(params).then(res => {
                    that.dialogMapVisible.visible = true;
                    that.dialogMapVisible.data = res;
                    that.dialogMapVisible.title = res.legend[0]
                })
                this.dialogMapVisible.visible = true;
                await this.$refs.buschar.initcharts();
            },
            async importData (titleName, dataType) {
                this.importDataType = dataType;
                this.importTitle = titleName;
                this.dialogVisible = true;
            },
            //取消导入
            cancelImport () {
                this.dialogVisible = false;
                this.importWarehouse = "";
            },
            uploadSuccess (response, file, fileList) {
                this.dialogVisible = false;
                this.fileList = [];
                this.importWarehouse = "";
            },
            submitUpload () {
                if (this.importWarehouse == "" && this.importTitle == "导入商品仓位") {
                    this.$message({ message: '请选择导入仓库！', type: 'error' })
                    return;
                }
                this.$refs.upload.submit();
            },
            async uploadFile (item) {
                const form = new FormData();
                form.append("token", this.token);
                form.append("upfile", item.file);
                form.append("importDataType", this.importDataType);
                form.append("importWarehouse", this.importWarehouse);
                this.importLoading = true;
                const res = await importPositionGoodsAsync(form);
                this.importLoading = false;
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
            },
            //时间设置
            importTimeSelChangePick () {
                if (this.filter.importTimeRange && this.filter.importTimeRange.length > 1) {
                    if (this.filter.importTimeRange[1] != formatTime(new Date(), "YYYY-MM-DD")) {
                        this.importTimeSel = "";
                        return;
                    }
                    var d1 = dayjs(this.filter.importTimeRange[0]);
                    var d2 = dayjs(this.filter.importTimeRange[1]);
                    switch (d2.diff(d1, "day")) {
                        //昨天至今日
                        case 1:
                            this.importTimeSel = "1";
                            break;
                        //近7天
                        case 6:
                            this.importTimeSel = "2";
                            break;
                        //近14天
                        case 13:
                            this.importTimeSel = "3";
                            break;
                        //近30天
                        case 29:
                            this.importTimeSel = "4";
                            break;
                        //默认
                        default:
                            this.importTimeSel = "";
                            break;
                    }
                }
                else {

                    this.importTimeSel = "";
                }

            },
            //付款时间设置默认值
            importTimeSelChange () {
                let oneDayTime = 24 * 60 * 60 * 1000;
                switch (this.importTimeSel) {
                    //昨天至今日
                    case "1":
                        this.filter.importTimeRange = [
                            formatTime(new Date(new Date().getTime() - 1 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //近7天
                    case "2":
                        this.filter.importTimeRange = [
                            formatTime(new Date(new Date().getTime() - 6 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //近14天
                    case "3":
                        this.filter.importTimeRange = [
                            formatTime(new Date(new Date().getTime() - 13 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //近30天
                    case "4":
                        this.filter.importTimeRange = [
                            formatTime(new Date(new Date().getTime() - 29 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //默认
                    default:
                        this.filter.importTimeRange = [];
                        break;

                }
            },
        }
    }
</script>
<style scoped>
    ::v-deep .el-link.el-link--primary {
        margin-right: 7px;
    }
    ::v-deep #app .el-container .is-vertical .main .el-main .el-tabs--top {
        height: 95% !important;
        background-color: red;
    }
    .titleRatio {
        border-radius: 20px;
        border: 1px solid var(--color);
        text-align: center;
        cursor: pointer;
        color: red;
        background: #fdf6ec;
        transform: scale(1.04);
        box-sizing: border-box;
        -moz-user-select: none;
        -ms-user-select: none;
        -webkit-user-select: none;
        user-select: none;
    }

    ::v-deep .rowupload20230101113501 .el-upload{
        display: none;
    }
</style>