<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="年月:">
                    <el-date-picker style="width: 110px" v-model="filter.yearMonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="平台:">
                    <el-select filterable v-model="filter.platform" placeholder="请选择平台" disabled clearable style="width:110px">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="所属店铺:" label-position="right">
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺" style="width: 180px">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="订单编号:" label-position="right">
                    <el-input v-model="filter.OrderNumber" placeholder="主订单号" style="width:160px;" />
                </el-form-item>
                <el-form-item label="类型" label-position="right">
                    <el-select filterable clearable v-model="filter.FeeType" placeholder="请选择" style="width:160px;">
                        <el-option label="收派服务费" value="收派服务费"></el-option>
                        <el-option label="仓储服务费-耗材" value="仓储服务费-耗材"></el-option>
                        <el-option label="仓储服务费-通用" value="仓储服务费-通用"></el-option>
                        <el-option label="运输转运费-中小件" value="运输转运费-中小件"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :tableData='ZTCKeyWordList' :showsummary='true' :summaryarry='summaryarry' @select='selectchange' :isSelection='false' :tableCols='tableCols' :loading="listLoading" :isSelectColumn='false'>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </my-container>
</template>
<script>
    import cesTable from "@/components/Table/table.vue";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    import { getAllList as getAllShopList, getList as getshopList } from '@/api/operatemanage/base/shop';
    import { formatPlatform, formatLink, platformlist } from "@/utils/tools";
    import { getBaoCaiDetailJD as getPageList } from '@/api/monthbookkeeper/financialDetail'
    const tableCols = [
        { istrue: true, prop: 'yearMonth', label: '年月', width: '90', sortable: 'custom' },
        { istrue: true, prop: 'orderNumber', label: '订单编号', sortable: 'custom', width: '160', type: 'html' },
        { istrue: true, prop: 'feeType', label: '类型', sortable: 'custom', width: '160', type: 'html' },
        { istrue: true, prop: 'settlementAmount', label: '结算金额', sortable: 'custom', width: '100', type: 'html', formatter: (row) => { return row.settlementAmount?.toFixed(2) } },
    ];
    export default {
        name: "Users",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
        data() {
            return {
                that: this,
                filter: {
                    platform: 7,
                    yearMonth: null,
                    shopCode: null,
                    proCode: null,
                    OrderNumber: null,
                    FeeType: null
                },
                shopList: [],
                userList: [],
                groupList: [],
                platformlist: platformlist,
                ZTCKeyWordList: [],
                tableCols: tableCols,
                summaryarry: {},
                total: 0,
                pager: { OrderBy: "id", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                selids: [],
                dialogVisibleSyj: false,
                fileList: [],
            };
        },
        async mounted() {
            this.onchangeplatform();
        },
        methods: {
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch();
            },
            async onchangeplatform() {
                const res1 = await getshopList({ platform: 7, CurrentPage: 1, PageSize: 100000 });
                this.shopList = res1.data.list
            },
            // async getShopList(){
            //   const res1 = await getAllShopList();
            //   this.shopList=[];
            //     res1.data?.forEach(f => {
            //       if(f.isCalcSettlement&&f.shopCode)
            //           this.shopList.push(f);
            //     });
            // },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.getList();
            },
            async getList() {
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...this.filter,
                };
                this.listLoading = true;
                const res = await getPageList(params);
                this.listLoading = false;
                this.total = res.data?.total
                this.ZTCKeyWordList = res.data?.list;
                this.summaryarry = res.data?.summary;
            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            }
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
