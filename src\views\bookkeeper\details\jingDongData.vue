<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="结算数据" name="tab1" style="height: 100%;">
                <settlementDetailJingDong :filter="filter" ref="settlementDetailJingDong" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="包材数据" name="tab2" style="height: 100%;">
                <baoCaiDetailJingDong :filter="filter" ref="baoCaiDetailJingDong" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="钱包数据" name="tab3" style="height: 100%;">
                <walletDetailJingDong :filter="filter" ref="walletDetailJingDong" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="虚拟发货快递" name="tab4" style="height: 100%;">
                <virtualExpressJingDong :filter="filter" ref="virtualExpressJingDong" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="虚拟发货订单" name="tab5" style="height: 100%;">
                <virtualOrderJingDong :filter="filter" ref="virtualOrderJingDong" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="非销售单结算明细" name="tab6" style="height: 100%;">
                <settlementDetailJingDongNoSale :filter="filter" ref="settlementDetailJingDongNoSale"
                    style="height: 100%;" />
            </el-tab-pane>
        </el-tabs>
    </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";
import settlementDetailJingDong from '@/views/bookkeeper/details/settlementDetailJingDong.vue'
import baoCaiDetailJingDong from '@/views/bookkeeper/details/baoCaiDetailJingDong.vue'
import walletDetailJingDong from '@/views/bookkeeper/details/walletDetailJingDong.vue'
import virtualExpressJingDong from '@/views/bookkeeper/details/virtualExpressJingDong.vue'
import virtualOrderJingDong from '@/views/bookkeeper/details/virtualOrderJingDong.vue'
import settlementDetailJingDongNoSale from '@/views/bookkeeper/details/settlementDetailJingDongNoSale.vue'

export default {
    name: "Users",
    components: { MyContainer, settlementDetailJingDong, baoCaiDetailJingDong, walletDetailJingDong, virtualExpressJingDong, virtualOrderJingDong, settlementDetailJingDongNoSale },
    data() {
        return {
            that: this,
            pageLoading: '',
            filter: {
            },
            shopList: [],
            userList: [],
            groupList: [],
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            activeName: 'tab1'
        };
    },
    mounted() {

    },
    methods: {


    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
