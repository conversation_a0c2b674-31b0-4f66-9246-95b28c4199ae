<template>
  <div style="width: 100%;height: 500px;padding: 5px 0;">
    <div
      style="display: flex;justify-content: end;padding: 0 30px 10px 0;font-size: 15px;color: #409EFF;cursor: pointer">
      <span @click="onNewLine">新增一行</span>
    </div>
    <el-table :data="tableData" style="width: 100%" border height="420">
      <el-table-column label="采购单类型" width="140" show-overflow-tooltip tooltip="true">
        <template slot-scope="scope">
          <el-select v-model="scope.row.payment" placeholder="采购单类型" clearable :disabled="!scope.row.enabled">
            <el-option key="包邮" label="包邮" value="包邮" />
            <el-option key="寄付" label="寄付" value="寄付" />
            <el-option key="仓库到付" label="仓库到付" value="仓库到付" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="feeType" label="仓库费用明细" show-overflow-tooltip tooltip="true">
        <template slot-scope="scope">
          <el-select v-model="scope.row.feeType" placeholder="状态" clearable :disabled="!scope.row.enabled">
            <el-option key="托运费" label="托运费" value="托运费" />
            <el-option key="送货费" label="送货费" value="送货费" />
            <el-option key="提货费" label="提货费" value="提货费" />
            <el-option key="货拉拉" label="货拉拉" value="货拉拉" />
            <el-option key="装车费" label="装车费" value="装车费" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="conditionType" label="设置条件" width="140" show-overflow-tooltip tooltip="true">
        <template slot-scope="scope">
          <el-select v-model="scope.row.conditionType" placeholder="状态" clearable :disabled="!scope.row.enabled"
            @change="contrastChange($event, scope.row, tableData.indexOf(scope.row))">
            <el-option key="大于" label="大于" value="大于" />
            <el-option key="小于" label="小于" value="小于" />
            <el-option key="介于" label="介于" value="介于" />
            <el-option key="等于" label="等于" value="等于" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="firstVal" width="160" show-overflow-tooltip tooltip="true">
        <template slot-scope="scope">
          <el-input-number v-model.trim="scope.row.firstVal" placeholder="最小值" :min="0" :max="99999999" :precision="4"
            :disabled="!scope.row.enabled" :controls="false" />
        </template>
      </el-table-column>
      <el-table-column prop="secondVal" width="160" show-overflow-tooltip tooltip="true">
        <template slot-scope="scope">
          <el-input-number v-model.trim="scope.row.secondVal" placeholder="最大值" :min="0" :max="99999999" :precision="4"
            :disabled="!scope.row.enabled" :controls="false" />
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="100">
        <template slot-scope="scope">
          <el-button @click="onDeleteOperation(scope.row)" type="text" size="small">删除</el-button>
          <el-button v-if="scope.row.enabled" @click="handleClick(scope.row)" type="text" size="small">禁用</el-button>
          <el-button v-if="!scope.row.enabled" @click="enableClick(scope.row)" type="text" size="small">启用</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="display: flex;justify-content: center;gap: 20px;margin-top: 20px;">
      <el-button @click="resetForm">取消</el-button>
      <el-button type="primary" @click="submitForm('ruleForm')">确定</el-button>
    </div>
  </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from 'dayjs'
import { getPurchaseCostVerifyPurSetList, savePurchaseCostVerifyPurSet } from '@/api/inventory/purchaseCostVerify'
export default {
  name: "setRulesPage",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      that: this,
      tableData: [],
      loading: false,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    onNewLine() {
      this.tableData.push({ payment: '', feeType: '', conditionType: '', firstVal: undefined, secondVal: undefined, enabled: true });
    },
    contrastChange(val, row, index) {
      if (index !== -1) {
        this.tableData[index].firstVal = undefined;
        this.tableData[index].secondVal = undefined;
      }
    },
    handleClick(row) {
      row.enabled = false;
    },
    enableClick(row) {
      row.enabled = true;
    },
    onDeleteOperation(row) {
      const index = this.tableData.indexOf(row)
      this.tableData.splice(index, 1)
    },
    async submitForm(formName) {
      const { msg, success } = await savePurchaseCostVerifyPurSet(this.tableData);

      if (success) {
        this.$message.success('保存规则成功！')
        this.$emit('setRulesPageClose')
      }

    },
    //重置关闭表单
    resetForm(formName) {
      this.$emit('setRulesPageClose')
    },
    async getList(type) {
      this.loading = true
      const { data, success } = await getPurchaseCostVerifyPurSetList()
      this.loading = false
      if (success) {
        this.tableData = data
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
}

.publicCss {
  width: 260px;
}
</style>
