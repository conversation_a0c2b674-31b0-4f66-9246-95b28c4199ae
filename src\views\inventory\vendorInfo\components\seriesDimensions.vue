<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-input v-model.trim="ListInfo.nameManufacturer" placeholder="供应商名称" maxlength="50" clearable
                    class="publicCss" />
                <el-select v-model="ListInfo.catroyType" placeholder="类目" clearable filterable class="publicCss">
                    <el-option v-for="item in categoryList" :key="item" :label="item" :value="item" />
                </el-select>
                <el-input v-model.trim="ListInfo.productName" placeholder="商品名称" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编号" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.yhGoodsCode" placeholder="商品编码" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.styleCode" placeholder="系列编码" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.proCode" placeholder="商品ID" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.expressNo" placeholder="快递单号" maxlength="40" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.yhContactPerson" placeholder="对接人" maxlength="20" clearable
                    class="publicCss" />
                <el-select v-model.trim="ListInfo.deptId" clearable filterable placeholder="对接人架构" class="publicCss">
                    <el-option v-for="item in purchasegrouplist" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.operateIdList" placeholder="选品中心链接运营" clearable filterable multiple
                    collapse-tags class="publicCss" style="width: 160px;">
                    <el-option v-for="item in referrerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.isCompetitivePowerH5List" placeholder="竞争力" clearable filterable multiple
                    collapse-tags class="publicCss" style="width: 160px;">
                    <el-option label="空" :value="0" />
                    <el-option v-for="item in competeList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.isCompetitivePowerH5Price" placeholder="参考价有无" clearable class="publicCss"
                    style="width: 100px;">
                    <el-option label="有" :value="1" />
                    <el-option label="无" :value="2" />
                </el-select>
                <div>
                    <el-button type="primary" class="top_button" @click="getList('search')">搜索</el-button>
                    <el-switch v-model="isHidden" active-text="展开" inactive-text="折叠" @change="changeHidden" />
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' border
            :treeProp="{}" @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="130" fixed="right">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;align-items: center;">
                            <el-button type="text" @click="handleMaintenanceId(row)"
                                v-if="row.children && row.children?.length > 0">ID维护</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="ID维护" :visible.sync="encodingVisible" width="600px" v-dialogDrag>
            <div class="encoding-container" v-loading="editloading">
                <div class="encoding-row">
                    <span class="encoding-label">ID:</span>
                    <el-input v-model="encoding.proCode" clearable maxlength="40" placeholder="请输入ID"
                        class="encoding-input" />
                </div>
                <div class="encoding-row">
                    <span class="encoding-label">选品中心链接运营:</span>
                    <el-select v-model="encoding.operateId" placeholder="请选择选品中心链接运营" clearable filterable
                        class="encoding-select" :disabled="current.operateId == 41071 ? false : true && !verification">
                        <el-option v-for="item in referrerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </div>
                <div class="encoding-row">
                    <span class="encoding-label">商品名称:</span>
                    <el-input v-model="encoding.productName" clearable maxlength="40" placeholder="请输入商品名称"
                        class="encoding-input" />
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="encodingVisible = false">取 消</el-button>
                <el-button type="primary" @click="dencodingMethod">保 存</el-button>
            </span>
        </el-dialog>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions, formatLinkProCode, formatProCodeStutas3 } from '@/utils/tools'
import dayjs from 'dayjs'
import { GetDeptList } from '@/api/inventory/Demerit.js'
import dateRange from "@/components/date-range/index.vue";
import { getUserInfo } from '@/api/operatemanage/productalllink/alllink';
import { PageProductList, SaveSampleStyleAsync, getCrmEfficiencyDept } from '@/api/customerservice/albbinquirs'
const categoryList = ['3C数码配件、电器', '艺术收藏用品', '运动户外', '载具用品', '仪器仪表', '五金建材工具', '居家布艺', '配饰专区', '饰品装饰', '美妆美容美发美体用品', '服饰专区', '玩具动漫周边', '日用餐厨饮具', '生活工具', '收纳清洁用具', '孕产妇/婴童用品', '宠物用品', '办公文化', '节庆用品礼品']
import { getLoginInfo } from '@/api/admin/auth'
import { row } from "mathjs";
const competeList = [
    { label: '无竞争力(平台售价≤商品成本)', value: 1 },
    { label: '有竞争力-普通款(平台售价＞商品成本)', value: 2 },
    { label: '有竞争力-竞争力款(平台售价＞商品成本)', value: 3 },
    { label: '无竞价产品', value: 4 },
]
const tableCols = [
    { sortable: 'custom', width: '250', align: 'center', prop: 'nameManufacturer', label: '供应商名称', treeNode: true },
    {
        sortable: 'custom', width: '130', align: 'center', prop: 'catroyType', label: '类目', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.catroyType
        }
    },
    { width: '100', align: 'center', prop: 'image', label: '商品主图', type: "images", },
    { sortable: 'custom', width: '130', align: 'center', prop: 'styleCode', label: '系列编码', },
    { sortable: 'custom', width: '300', align: 'center', prop: 'productName', label: '商品名称', },
    { istrue: true, prop: 'proCode', fix: true, label: '商品ID', width: '150', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(4, row.proCode) },
    {
        sortable: 'custom', width: '100', align: 'center', prop: 'goodsCode', label: '商品编号', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.goodsCode
        }
    },
    {
        sortable: 'custom', width: '130', align: 'center', prop: 'yhGoodsCode', label: '商品编码', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.yhGoodsCode
        }
    },
    {
        sortable: 'custom', width: '130', align: 'center', prop: 'gysGoodsCode', label: '供应商商品编号', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.gysGoodsCode
        }
    },
    {
        sortable: 'custom', width: '130', align: 'center', prop: 'oppositeLink', label: '供应商商品链接', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.oppositeLink
        }, type: "click", handle: (that, row, column, cell) => that.canclick(1, row, column, cell)
    },
    {
        sortable: 'custom', width: '150', align: 'center', prop: 'competitorLink', label: '1688选品中心链接', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.competitorLink
        }, type: "click", handle: (that, row, column, cell) => that.canclick(2, row, column, cell)
    },
    {
        sortable: 'custom', width: '100', align: 'center', prop: 'competitivePowerH5Price', label: '参考价', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.competitivePowerH5Price
        }
    },
    {
        sortable: 'custom', width: '150', align: 'center', prop: 'operateName', label: '选品中心链接运营', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.operateName
        }
    },
    {
        sortable: 'custom', width: '140', align: 'center', prop: 'expressNo', label: '快递单号', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.expressNo
        }
    },
    {
        sortable: 'custom', width: '100', align: 'center', prop: 'origin', label: '产地', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.origin
        }
    },
    {
        sortable: 'custom', width: '100', align: 'center', prop: 'material', label: '材质', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.material
        }
    },
    {
        sortable: 'custom', width: '100', align: 'center', prop: 'colour', label: '颜色', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.colour
        }
    },
    {
        sortable: 'custom', width: '100', align: 'center', prop: 'packagingSpecification', label: '包装', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.packagingSpecification
        }
    },
    {
        sortable: 'custom', width: '100', align: 'center', prop: 'boxSpecification', label: '箱规', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.boxSpecification
        }
    },
    {
        sortable: 'custom', width: '100', align: 'center', prop: 'weight', label: '重量', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.weight
        }
    },
    {
        sortable: 'custom', width: '100', align: 'center', prop: 'dailySpot', label: '日均现货', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.dailySpot
        }
    },
    {
        sortable: 'custom', width: '100', align: 'center', prop: 'averageDailyCapacity', label: '日均产能', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.averageDailyCapacity
        }
    },
    {
        width: '100', align: 'center', prop: 'zizhiImage', label: '资质', type: "images", formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.zizhiImage
        }
    },
    {
        width: '100', align: 'center', prop: 'zhuanliImage', label: '专利', type: "images", formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.zhuanliImage
        }
    },
    { sortable: 'custom', width: '100', align: 'center', prop: 'contactPerson', label: '联系人', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'contactInformation', label: '联系方式', },
    {
        sortable: 'custom', width: '100', align: 'center', prop: 'yhContactPerson', label: '对接人', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.yhContactPerson
        }
    },
    {
        sortable: 'custom', width: '240', align: 'center', prop: 'deptName', label: '对接人架构', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.deptName
        }
    },
    // { sortable: 'custom', width: '100', align: 'center', prop: 'createdUserName', label: '创建人', },
    // {
    //     sortable: 'custom', width: '100', align: 'center', prop: 'price', label: '价格系数', formatter: (row) => {
    //         if (row.children && row.children?.length > 0) {
    //             return ''
    //         }
    //         return row.price
    //     }
    // },
    { sortable: 'custom', width: '100', align: 'center', prop: 'isPriceControl', label: '是否控价', formatter: (row) => row.isPriceControl == 1 ? '是' : row.isPriceControl == 0 ? '否' : '', },
    {
        sortable: 'custom', width: '100', align: 'center', prop: 'isUse', label: '是否同步商品详情', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.isUse == 1 ? '是' : row.isUse == 0 ? '否' : ''
        }
    },
    {
        sortable: 'custom', width: '100', align: 'center', prop: 'isDaYin', label: '是否已打印', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.isDaYin == 1 ? '是' : '否'
        }
    },
    { sortable: 'custom', width: '100', align: 'center', prop: 'minimumSellingPrice', label: '最低售价', },
    {
        sortable: 'custom', width: '100', align: 'center', prop: 'isCompetitivePower', label: '是否竞争力', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.isCompetitivePower == 1 ? '是' : row.isCompetitivePower == 0 ? '否' : ''
        }
    },
    {
        sortable: 'custom', width: '170', align: 'center', prop: 'isCompetitivePowerH5String', label: '竞争力', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.isCompetitivePowerH5String
        }
    },
    {
        sortable: 'custom', width: '100', align: 'center', prop: 'boothInformation', label: '展位', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.boothInformation
        }
    },
    {
        sortable: 'custom', width: '100', align: 'center', prop: 'hotLink', label: '热销链接', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.hotLink
        }, type: "click", handle: (that, row, column, cell) => that.canclick(3, row, column, cell)
    },
    {
        sortable: 'custom', width: '130', align: 'center', prop: 'isDuiJieDaDanPingtai', label: '是否对接打单平台', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.isDuiJieDaDanPingtai == 1 ? '是' : row.isDuiJieDaDanPingtai == 0 ? '否' : ''
        }
    },
    {
        sortable: 'custom', width: '100', align: 'center', prop: 'daDanPing1', label: '打单平台1', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.daDanPing1
        }
    },
    {
        sortable: 'custom', width: '100', align: 'center', prop: 'daDanPing2', label: '打单平台2', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.daDanPing2
        }
    },
    {
        sortable: 'custom', width: '130', align: 'center', prop: 'isAllDuiJieDaDanPingtai', label: '是否所有商品对接打单平台', formatter: (row) => {
            if (row.children && row.children?.length > 0) {
                return ''
            }
            return row.isAllDuiJieDaDanPingtai == 1 ? '是' : row.isAllDuiJieDaDanPingtai == 0 ? '否' : ''
        }
    },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            categoryList,
            referrerOptions: [],
            that: this,
            competeList,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                nameManufacturer: null,
                contactPerson: null,
                contactInformation: null,
                yhContactPerson: null,
                createdUserName: null,
                yhGoodsCode: null,
                goodsCode: null,
                proCode: null,//商品ID
                oppositeLink: null,//供应商商品链接
                competitorLink: null,//1688选品中心链接
                origin: null,//产地
                articleNumber: null,//货号
                material: null,//材质
                packagingSpecification: null,//包装
                isUse: null,//是否同步商品详情
                isDaYin: null,//是否已打印
                gysGoodsCode: null,//供应商商品编号
                boothInformation: null,//展位
                isPriceControl: null,//是否控价
                hotLink: null,//热销链接
                productName: null,//商品名称
                expressNo: null,//快递单号
                isGoodsCode: null,//是否有商品编码
                isYhGoodsCode: null,//是否有供应商商品编码
                isOppositeLink: null,//是否有供应商商品链接
                isCompetitorLink: null,//是否有1688选品中心链接
                isOperateId: null,//是否有选品中心链接运营
                isCompetitivePowerH5List: [],//竞争力
                isCompetitivePowerH5Price: null,//参考价
                deptId: null,//对接人架构
                styleCode: null,//系列编码
                operateIdList: [],
                catroyType: null,//类目
                isImage: null,//是否有商品主图
            },
            isHidden: false,
            purchasegrouplist: [],
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            verification: false,
            loading: true,
            pickerOptions,
            isExport: false,
            encoding: {
                proCode: '',
                operateId: '',
                productName: '',
            },
            current: {
                operateId: '',
                operateName: '',
            },
            encodingVisible: false,
            separateCheck: false,

        }
    },
    async mounted() {
        let originalArr = await GetDeptList();
        this.purchasegrouplist = originalArr.map(({ id, name }) => ({ value: Number(id), label: name }));
        await this.getList()
        setTimeout(async () => {
            const { data } = await getUserInfo();
            this.current.operateId = data.id || ''
            this.current.operateName = data.nickName || ''
            const { data: directorGroupList } = await getLoginInfo()
            this.loginMessage = data
            this.loginMessage.roles = directorGroupList.roles
            this.checkAuthority(data.fullName, directorGroupList.roles);
            if (this.current.operateId == 41071 && this.current.operateName == '陈鑫河') {
                this.specialVerify = false
                this.separateCheck = true
            }
        }, 500)
        const { data: data1 } = await getCrmEfficiencyDept()
        this.referrerOptions = (data1 || []).map(item => ({
        label: item.userName,
        value: item.userId
      }))
    },
    methods: {
        checkAuthority(fullName, roles) {
            const is1688Operator = fullName.includes('1688运营');
            const isSuperAdmin = roles.some(role => role.name === '超级管理员');
            this.verification = is1688Operator || isSuperAdmin;
            this.separateCheck = is1688Operator || isSuperAdmin;
        },
        changeHidden(e) {
            if (e) {
                this.$refs.table.$refs.xTable.setTreeExpand(this.tableData, true)
            } else {
                this.$refs.table.$refs.xTable.clearTreeExpand()
            }
        },
        canclick(val, row, column, cell) {
            let url = ''
            if (val == 1) {
                url = row.oppositeLink
            } else if (val == 2) {
                url = row.competitorLink
            } else if (val == 3) {
                url = row.hotLink
            }
            if (url) {
                window.open(url, '_blank');
            }
        },
        verifyonMethod(row) {
            return !(row.operateId && row.operateId !== this.current.operateId && row.operateName && row.operateName !== this.current.operateName);
        },
        async dencodingMethod() {
            this.editloading = true
            const { success } = await SaveSampleStyleAsync(this.encoding)
            this.editloading = false
            if (!success) return
            this.$message.success('操作成功')
            this.encodingVisible = false
            this.getList()
        },
        handleMaintenanceId(row) {
            this.encoding = JSON.parse(JSON.stringify(row))
            this.encoding.proCode = row.proCode ? row.proCode : ''
            this.encoding.operateId = row.operateId ? row.operateId : ''
            this.encodingVisible = true
        },
        prepareParams() {
            const { operateIdList, isCompetitivePowerH5List, ...rest } = this.ListInfo;
            return {
                ...rest,
                operateIdList: operateIdList?.length ? operateIdList.join(',') : '',
                isCompetitivePowerH5List: isCompetitivePowerH5List?.length ? isCompetitivePowerH5List.join(',') : '',
            };
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const params = this.prepareParams();
                const { data, success } = await PageProductList(params)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.selectList = []
                    this.loading = false
                    if (this.isHidden) {
                        this.$nextTick(() => {
                            this.$refs.table.$refs.xTable.setTreeExpand(this.tableData, true)
                        })
                    } else {
                        this.$nextTick(() => {
                            this.$refs.table.$refs.xTable.clearTreeExpand()
                        })
                    }
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 120px;
        margin: 0 5px 5px 0px;
    }
}

.encoding-container {
    padding: 20px;
    min-height: 120px;
}

.encoding-row {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    &:last-child {
        margin-bottom: 0;
    }
}

.encoding-label {
    min-width: 120px;
    text-align: right;
    padding-right: 12px;
    color: #606266;
}

.encoding-input {
    flex: 1;
    max-width: 300px;
}

.encoding-select {
    flex: 1;
    max-width: 300px;
}

::v-deep .el-select__tags-text {
    max-width: 40px;
}
</style>
