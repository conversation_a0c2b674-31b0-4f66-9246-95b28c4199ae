<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='inquirsstatisticslist' @select='selectchange' :isSelection='false' :tableCols='tableCols'
            :loading="listLoading" :summaryarry="summaryarry">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.GroupNameList"   placeholder="分组" clearable filterable multiple :collapse-tags="true">
                            <el-option v-for="item in filterGroupList" :key="item" :label="item"
                                :value="item">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.sname" placeholder="姓名" style="width:120px;" clearable
                            :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <datepicker v-model="filter.sdate"></datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport" style="margin-left: 10px;">导出</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"
                @get-page="getinquirsstatisticsList" />
        </template>

        <el-dialog title="个人效率按店统计" :visible.sync="dialogVisibleSyj" width="80%" :close-on-click-modal="false"
            v-dialogDrag>
            <span>
                <sqinquirsstatisticsbyshop v-if="dialogVisibleSyj" ref="sqinquirsstatisticsbyshop"
                    style="height: 600px;" />
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
            :close-on-click-modal="false" v-dialogDrag>
            <div>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import sqinquirsstatisticsbyshop from '@/views/customerservice/jingdong/sq/sqinquirsstatisticsbyshop'
import {
    getJingDongGroup,
    getJingDongPersonalEfficiencyPageList, getJingDongPersonalEfficiencyChat, exportJingDongPersonalEfficiencyList
} from '@/api/customerservice/jingdonginquirs'
import datepicker from '@/views/customerservice/datepicker'
import buschar from '@/components/Bus/buschar'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
const tableCols = [
    { istrue: true, prop: 'groupName', label: '组名称', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'sname', label: '姓名', width: '120', type: "click", handle: (that, row, column, cell) => that.canclick(row, column, cell), formatter: (row) => row.sname },
    { istrue: true, prop: 'visitTotalCount', label: '咨询量', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'receptionCount', label: '接待量', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'responseTime', label: '平均响应时间', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'responseRate', label: '30s应答率', width: '80', sortable: 'custom', formatter: (row) => (row.responseRate).toFixed(2) + "%" },

    { istrue: true, prop: 'inquirs', label: '售前接待人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'orderers', label: '促成下单人数', width: '80', sortable: 'custom' },
    // { istrue: true, prop: 'outers', label: '促成出库人数', width: '80', sortable: 'custom' },
    // { istrue: true, prop: 'orderSalesvol', label: '促成下单商品金额', width: '80', sortable: 'custom' },
    // { istrue: true, prop: 'outSalesvol', label: '促成出库商品金额', width: '80', sortable: 'custom' },
    // { istrue: true, prop: 'ipsRate', label: '咨询->出库转化率', width: '80', sortable: 'custom', formatter: (row) => row.ipsRate? (row.ipsRate).toFixed(2) : 0 + "%" },
    { istrue: true, prop: 'orderConversionRate', label: '咨询->下单转化率', width: '80', sortable: 'custom', formatter: (row) => (row.orderConversionRate || 0).toFixed(2) + "%" },
  // { istrue: true, prop: 'outTimes', label: '出勤人次', width: '100', sortable: 'custom' },
  // { istrue: true, prop: 'responseIn30sCount', label: '30s回复人数', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'veryGoodCount', label: '非常满意', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'goodCount', label: '满意', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'normalCount', label: '一般', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'badCount', label: '不满意', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'veryBadCount', label: '非常不满意', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'evaluateCount', label: '总评价数', width: '80', sortable: 'custom' },

    { istrue: true, prop: 'satisfactionRate', label: '满意率', width: '80', sortable: 'custom', formatter: (row) => (row.satisfactionRate || 0).toFixed(2) + "%" },
    { istrue: true, prop: 'unsatisfactionRate', label: '不满意率', width: '80', sortable: 'custom', formatter: (row) => (row.unsatisfactionRate || 0).toFixed(2) + "%" },
    { istrue: true, prop: 'invitationEvaluationCount', label: '邀评数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'invitationEvaluationRate', label: '邀评率', width: '80', sortable: 'custom', formatter: (row) => (row.invitationEvaluationRate || 0).toFixed(2) + "%" },
  { istrue: true, prop: 'attendanceDays', label: '出勤天数', width: '90', sortable: 'custom' },
  { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '90', sortable: 'custom' },
    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", formatter: (row) => '趋势图', width: '80', type: 'click', handle: (that, row) => that.showchart(row) },

];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar, sqinquirsstatisticsbyshop },
    props:["partInfo"],
    data() {
        return {
            dialogMapVisible: { visible: false, title: "", data: [] },
            that: this,
            filter: {
                groupType: 0,
                inquirsType: 0,
            },
            shopList: [],
            filterGroupList: [],
            userList: [],
            inquirsstatisticslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: {},
            pager: { OrderBy: "inquirs", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            isleavegroup:this.partInfo,//是否离组
        };
    },
    watch:{
        partInfo(){
            this.isleavegroup = this.partInfo;
            this.getJingDongGroup();
        }
    },
    async mounted() {
         this.isleavegroup = this.partInfo;
        await this.getJingDongGroup();
        window.showlist34jd = this.showlist34jd;
    },
    methods: {
        async getJingDongGroup() {
            let groups = await getJingDongGroup({ groupType: 0,isleavegroup:this.isleavegroup });
            if (groups?.success && groups?.data && groups?.data.length > 0) {
               this.filterGroupList=groups.data;
            }
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getinquirsstatisticsList();
        },
        getParam() {
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            return params;
        },
        async getinquirsstatisticsList() {
            let params = this.getParam();
            console.log(params, "params")
            this.listLoading = true;
            const res = await getJingDongPersonalEfficiencyPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.inquirsstatisticslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        showlist34jd(groupName, startdate, enddate) {
            this.filter.groupName = groupName;

            this.filter.sdate = [startdate, enddate];
            if (startdate == null || enddate == null)
                this.filter.sdate = ["", ""];
            this.filter.startDate = startdate;
            this.filter.endDate = enddate;
            this.onSearch()
        },
        async showchart(row) {
          const rateFields = ["咨询->下单转化率", "30s应答率", "满意率", "不满意率", "邀评率"];

            let params = this.getParam();
            params.groupName = row.groupName;
            params.sname = row.sname;
            await getJingDongPersonalEfficiencyChat(params).then(res => {
                if (res) {
                    this.dialogMapVisible.visible = true;
                  this.dialogMapVisible.data = res;
                  this.dialogMapVisible.data.series.forEach(series => {
                    if (rateFields.includes(series.name)) {
                      series.data = series.data.map(value => `${value}%`);
                    }
                  });
                    this.dialogMapVisible.data = res;
                    this.dialogMapVisible.title = res.title;
                    res.title = "";
                }
            })
            this.dialogMapVisible.visible = true
        },
        async canclick(row, column, cell) {
            var fstartsdate = "";
            var fendsdate = "";
            if (this.filter.sdate) {
                var d = new Date(this.filter.sdate[0])
                fstartsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
                d = new Date(this.filter.sdate[1])
                fendsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
            }
            if (fstartsdate == "NaN-NaN-NaN") {
                fstartsdate = "";
                fendsdate = "";
            }
            var fsname = row.sname;
            this.dialogVisibleSyj = true;
            this.$nextTick(() => {
                this.$refs.sqinquirsstatisticsbyshop.dialogOpenAfter({
                    startDate: fstartsdate,
                    endDate: fendsdate,
                    sname: fsname
                });
            });
        },
        async onExport() {
            let params = this.getParam();
            this.listLoading = true
            const res = await exportJingDongPersonalEfficiencyList(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '京东个人效率统计(售前组)_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 60px;
}
</style>
