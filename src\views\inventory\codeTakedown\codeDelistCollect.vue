<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-select v-model="ListInfo.brandIds" placeholder="品牌" filterable multiple collapse-tags class="publicCss" clearable style="width: 175px">
          <el-option v-for="item in brandList" :key="item.id" :label="item.brandName" :value="item.id" />
        </el-select>
        <div class="publicCss">
          <inputYunhan ref="goodsCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode" width="150px"
            placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100" :maxlength="2000"
            @callback="callbackGoodsCode" title="商品编码">
          </inputYunhan>
        </div>
        <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.styleCode" placeholder="款式编码" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.sellStock" placeholder="可用库存数" maxlength="7" clearable class="publicCss"
            oninput="value=value.replace(/[^\d]/g,''); if(value.replace('-', '').length > 7) value = value.slice(0, 7)" />
        <el-select v-model="ListInfo.status" placeholder="数据状态" class="publicCss" clearable>
          <el-option v-for="item in statusList" :key="item" :label="item" :value="item"></el-option>
        </el-select>
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="修改开始日期"
          end-placeholder="修改结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <!-- <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" @click="downloadTemplate">下载导入模版</el-button> -->
        <el-button type="primary" @click="onExport">导出</el-button>
        <el-button type="primary" @click="onStockSet" v-if="checkPermission('viewGoodsBanStockStandardSet')">库存配置</el-button>
        <el-button type="primary" @click="onBrandSet" v-if="checkPermission('viewGoodsBanBrandSet')">品牌配置</el-button>
        <el-button type="text">
            上次通知时间:{{ lastNoticeTime }}
        </el-button>
      </div>
    </template>
    <vxetablebase :id="'codeDelistCollect20240941'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0"
      :loading="loading" :height="'100%'" :border="true">
      <template #proCodeCount="{ row }">
        <div style="display: flex;align-items: center;justify-content: center;">
          <span style="color: #4095e5;cursor: pointer;" @click="onJumpMethod(row)">
            {{ row.banProCodeCount ? row.banProCodeCount : 0 }}
          </span>
          /
          <span style="color: #ff2c3c">
            {{ row.proCodeCount ? row.proCodeCount : 0 }}
          </span>
        </div>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="库存设置" :visible.sync="dialogStockSetVisible"  width="40%" v-dialogDrag :close-on-click-modal="false"> 
      <el-form label-width="120px" ref="editFormRef" :model="stockSetForm">
        <div style="padding: 0 10px;">
          <el-row>
            <el-col :span="8">
                <el-form-item label="通用库存标准" prop="generalStandard">
                  <span slot="label">通用库存标准
                    <el-tooltip class="item" effect="dark" placement="top-start" content="根据该输入数判定非特殊的编码需要下架的库存界限，输入数字最大9999999，最小为0">
                      <i class="el-icon-question"/>
                    </el-tooltip>
                  </span>
                  <el-input v-model.trim="stockSetForm.generalStandard" placeholder="通用库存标准" maxlength="7" clearable 
                    oninput="value=value.replace(/[^\d]/g,''); if(value.replace('-', '').length > 7) value = value.slice(0, 7)" />  
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="通知周期" prop="noticeCycle">
                    <el-input v-model.trim="stockSetForm.noticeCycle" placeholder="通知周期" maxlength="7" clearable 
                    oninput="value=value.replace(/[^\d]/g,''); if(value.replace('-', '').length > 7) value = value.slice(0, 7)" />  
                </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <el-button @click="onSaveGeneralInventory">保存</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div style="border: 1px solid #eee; width: 100%; padding: 10px; margin-bottom: 10px; box-sizing: border-box;">
          <el-row>
            <el-col :span="8">
              <el-form-item label="特殊库存标准" prop="specialInventory">
                <span slot="label">特殊库存标准
                  <el-tooltip class="item" effect="dark" placement="top-start" content="根据该输入数判定特殊的编码需要下架的库存界限">
                    <i class="el-icon-question"/>
                  </el-tooltip>
                </span>
                <el-input v-model.trim="stockSetForm.specialInventory" placeholder="特殊库存标准" maxlength="7" clearable 
                    oninput="value=value.replace(/[^\d]/g,''); if(value.replace('-', '').length > 7) value = value.slice(0, 7)" />  
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="商品编码" prop="goodsCode">
                <inputYunhan ref="goodsCode" :inputt.sync="stockSetForm.goodsCode" v-model="stockSetForm.goodsCode" width="150px"
                  placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="200" :maxlength="4000"
                  @callback="callbackSetGoodsCode" title="商品编码">
                </inputYunhan>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <el-button @click="onSaveSpecialStandard">添加</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="商品编码" prop="goodsCode">
                <inputYunhan ref="goodsCode" :inputt.sync="stockSetForm.queryGoodsCode" v-model="stockSetForm.queryGoodsCode" width="150px"
                  placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="200" :maxlength="4000"
                  @callback="callbackQuerySetGoodsCode" title="商品编码">
                </inputYunhan>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <el-button @click="querySpecialStandard">查询</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <vxetablebase :id="'specialStandard20250106'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
                :tablefixed='true' :tableData='stockSetForm.specialStandardData' :tableCols='stockSetForm.specialStandardTableCols' :isSelection="false"
                :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="stockSetForm.specialStandardLoading" :height="'300px'" :border="true">
                <template slot="right">
                  <vxe-column title="操作" width="160" fixed="right">
                    <template #default="{ row, $index }">
                      <div style="display: flex;justify-content: center;align-items: center;">
                        <el-button type="text" @click="onEditSpecialStandard(row, $index)">编辑</el-button>
                        <el-button type="text" @click="onDelSpecialStandard(row, $index)">删除</el-button>
                      </div>
                    </template>
                  </vxe-column>
                </template>
              </vxetablebase>
              <div>
                <div style="display: flex; flex-direction: row; align-items: center; justify-content: space-between; width: 100%;">共 {{stockSetForm.specialStandardTotal  }} 条
                  <my-pagination ref="specialStandardPager" :checked-count="0" v-if="dialogStockSetVisible" :total="stockSetForm.specialStandardTotal" 
                    @page-change="specialStandardPagechange" @size-change="specialStandardSizechange" />
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogStockSetVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="特殊库存标准" :visible.sync="editSpecialStockSetVisible" width="35%" v-dialogDrag :close-on-click-modal="false">
      <el-form label-width="120px" :model="specialStockSetForm">
        <el-row>
            <el-col :span="8">
                <el-form-item label="特殊库存标准" prop="specialStandard">
                  <span slot="label">特殊库存标准
                    <el-tooltip class="item" effect="dark" placement="top-start" content="根据该输入数判定特殊的编码需要下架的库存界限">
                      <i class="el-icon-question"/>
                    </el-tooltip>
                  </span>
                  <el-input v-model.trim="specialStockSetForm.specialStandard" placeholder="特殊库存标准" maxlength="7" clearable 
                    oninput="value=value.replace(/[^\d]/g,''); if(value.replace('-', '').length > 7) value = value.slice(0, 7)" />  
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="商品编码" prop="specialGoodsCode">
                    <el-input placeholder="商品编码" v-model.trim="specialStockSetForm.specialGoodsCode" :disabled="true"></el-input>
                </el-form-item>
            </el-col>
          </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editSpecialStockSetVisible = false">关闭</el-button>
        <el-button @click="onSaveSpecialStockSet">确认</el-button>
      </span>
    </el-dialog>

    <el-dialog title="品牌设置" :visible.sync="dialogBrandSetVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <el-form label-width="40px">
        <el-row>
          <el-form-item label="品牌" prop="selBrandId">
            <el-select v-model.trim="selBrandId" placeholder="品牌" filterable class="publicCss" clearable :filterable="true">
              <el-option v-for="item in brandList" :key="item.id" :label="item.brandName" :value="item.id"></el-option>
            </el-select>
            <el-button style="margin-left:10px;" @click="onAddSelBrand">添加</el-button>
          </el-form-item>
        </el-row>
        <el-row>
          <div style="border: 1px solid #dcdfe6;border-radius: 5px;height: 150px;margin: 10px 0;">
            <el-scrollbar style="height: 100%;">
              <el-tag v-for="(item, i) in selBrandList" :key="item" closable style="margin: 5px;" @close="handleBrandClose(item, i)">
                  {{ item.brandName }}
              </el-tag>
            </el-scrollbar>
          </div>
        </el-row>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogBrandSetVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { getGoodsBanPage } from "@/api/inventory/basicgoods"
import { importBanGoodsCodeAsync } from "@/api/inventory/goodscodestock"
import { 
  getBrandSetData,
  saveBrandSetDate,
  delBrandSetDate,
  getInventoryStandardSet,
  getSpecialStandardList,
  saveGeneralInventoryStandard,
  saveSpecialStandard,
  delSpecialStandard,
  getGoodsBanNoticePageQueryCondition,
  exportGoodsBan,getLastNoticeTime 
} from "@/api/inventory/basicgoods"
import inputYunhan from "@/components/Comm/inputYunhan";
import { pageBianMaBrand } from '@/api/inventory/warehouse';

const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'picture', label: '图片', type: "images", },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', treeNode: true, type: 'click', handle: (that, row) => that.onJumpMethod(row), },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'styleCode', label: '款式编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'brandName', label: '品牌', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'sellStock', label: '可用库存数', color: (row) => row.isRed ? "red" : "" },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'proCodeCount', label: '已下架ID/涉及ID', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'noticeDuration', label: '通知时长(天)', color: (row) => row.noticeDurationMarker ? "red" : "" },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'syncTime', label: '修改时间', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'syncStatus', label: '数据状态', },
];
const statusList = ['加载中','进行中','已完结'];
const specialStandardTableCols = [
  { width: 'auto', align: 'center', prop: 'createdTime', label: '添加时间', },
  { width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { width: 'auto', align: 'center', prop: 'inventoryStandard', label: '特殊库存标准', }
];
export default {
  name: "codeDelistCollect",
  components: {
    MyContainer, vxetablebase, inputYunhan
  },
  data() {
    return {
      brandList: [],//品牌列表
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startDate: null,//开始时间
        endDate: null,//结束时间
        goodsCode: null,//商品编码
        styleCode: null,//款式编码
        brandId: null,
        brandIds: [],
        goodsName: null,//商品名称
        sellStock: undefined,//可用库存
        status: null,//数据状态
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      statusList: statusList,
      dialogStockSetVisible: false,
      dialogBrandSetVisible: false,
      stockSetForm:{
        generalStandard: 5,
        noticeCycle: null,
        specialInventory: undefined,
        goodsCode: null,
        queryGoodsCode: null,
        specialStandardData: [],
        specialStandardTotal: 0,
        currentPage: 1,
        pageSize: 50,
        specialStandardTableCols: specialStandardTableCols,
        specialStandardLoading: false,
      },
      editSpecialStockSetVisible: false,
      specialStockSetForm:{
        specialStandard: null,
        specialGoodsCode: null
      },
      selBrandId: null,
      selBrandList: [],
      lastNoticeTime: null
    }
  },
  async mounted() {
    await this.getList()
    await this.init()
  },
  methods: {
    async init() {
      /*
      let { data, success } = await pageBianMaBrand({ pageSize: 1000, currentPage: 1, OrderBy: "", IsAsc: true, name: '★' })
      if (!success) return
      this.brandList = data.list
      */
      var res = await getLastNoticeTime();
      if(res?.success)
        this.lastNoticeTime = res.data;

      var res = await getGoodsBanNoticePageQueryCondition();
      if(res?.success){
        this.brandList = res.data.brandList;
      }
    },
    callbackSetGoodsCode(val) {
      this.stockSetForm.goodsCode = val;
    },
    callbackQuerySetGoodsCode(val) {
      this.stockSetForm.queryGoodsCode = val;
    },
    callbackGoodsCode(val) {
      this.ListInfo.goodsCode = val;
    },
    onJumpMethod(row) {
      this.$nextTick(() => {
        const params = { ...this.ListInfo, ...row }
        this.$emit('ChangeActiveName', params)
      })
    },
    async changeTime(e) {
      this.ListInfo.startDate = e ? e[0] : null
      this.ListInfo.endDate = e ? e[1] : null
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importBanGoodsCodeAsync(form);
      if (res?.success)
        this.$message({ message: res.data || "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
      }
      this.loading = true
      const { data, success } = await getGoodsBanPage(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.summaryarry.proCodeCount_sum = `${this.summaryarry.banProCodeCount_sum}/${this.summaryarry.proCodeCount_sum}`;
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    specialStandardSizechange(val){
      this.stockSetForm.currentPage = 1;
      this.stockSetForm.pageSize = val;
      this.querySpecialStandard();
    },
    specialStandardPagechange(val){
      this.stockSetForm.currentPage = val;
      this.querySpecialStandard();
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    //下载采购出库退货导入模板
    downloadTemplate(){
        window.open("../../../static/excel/inventory/编码下架导入模版.xlsx","_self");
    },
    onStockSet(){
      this.dialogStockSetVisible = true;
      this.stockSetForm.specialInventory = undefined;
      this.stockSetForm.goodsCode = '';
      this.getStockStandardSet();
    },
    onBrandSet(){
      this.selBrandId = null;
      this.dialogBrandSetVisible = true;
      this.getBrandSetList();
    },
    async onSaveGeneralInventory(){
      var param = {
        generalStandard: this.stockSetForm.generalStandard,
        noticeCycle: this.stockSetForm.noticeCycle
      };

      var res = await saveGeneralInventoryStandard(param);
      if(res?.success){
        this.$message({message: "保存成功！",type: "success"});
      }
    },
    async onSaveSpecialStandard(){
      var param = {
        goodsCode: this.stockSetForm.goodsCode,
        specialInventory: this.stockSetForm.specialInventory
      };

      var res = await saveSpecialStandard(param);
      if(res?.success){
        this.$message({message: "添加成功！",type: "success"});
        this.querySpecialStandard();
      }
    },
    onEditSpecialStandard(row, i){
      this.editSpecialStockSetVisible = true;
      this.specialStockSetForm.specialGoodsCode = row.goodsCode;
      this.specialStockSetForm.specialStandard = row.inventoryStandard;
    },
    async onDelSpecialStandard(row, i){
      this.$confirm("确定删除？", '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
      }).then(async () => {
        var param = { goodsCode: row.goodsCode };
        var res = await delSpecialStandard(param);
        if (res?.success){
          this.$message({message: "删除成功！",type: "success"});
          this.querySpecialStandard();
        }
      }).catch(() => {
      });
    },
    async querySpecialStandard(){
      var param = {
        goodsCode: this.stockSetForm.queryGoodsCode,
        currentPage: this.stockSetForm.currentPage,
        pageSize: this.stockSetForm.pageSize,
      };
      this.stockSetForm.specialStandardLoading = true;
      var res = await getSpecialStandardList(param);
      if(res?.success){
        this.stockSetForm.specialStandardData = res.data.list;
        this.stockSetForm.specialStandardTotal = res.data.total;
      }

      this.stockSetForm.specialStandardLoading = false;
    },
    async onSaveSpecialStockSet(){
      var param = {
        goodsCode: this.specialStockSetForm.specialGoodsCode,
        specialInventory: this.specialStockSetForm.specialStandard
      };

      var res = await saveSpecialStandard(param);
      if(res?.success){
        this.$message({message: "保存成功！",type: "success"});
        this.editSpecialStockSetVisible = false;
        this.querySpecialStandard();
      }
    },
    async getStockStandardSet(){
      var res = await getInventoryStandardSet();
      if (res?.success){
        this.stockSetForm.specialStandardData = (res?.data?.standardList?.list ? res?.data?.standardList?.list : []);
        this.stockSetForm.specialStandardTotal = (res?.data?.standardList?.total ? res?.data?.standardList?.total : []);
        this.stockSetForm.generalStandard = res?.data?.generalStandard;
        this.stockSetForm.noticeCycle = res?.data?.noticeCycle;
      }
    },
    async getBrandSetList(){
      var res = await getBrandSetData();
      if (res?.success){
        this.selBrandList = res.data;
      }
    },
    async onAddSelBrand(){
      if(!this.selBrandId)
        return;
      var brand = this.brandList.find(x => x.id == this.selBrandId);
      if(!brand)
        return;
      if(this.selBrandList.find(x => x.id == this.selBrandId))
        return;

      var sqlBrand = {
        id: brand.id,
        brandName: brand.brandName
      };
      var res = await saveBrandSetDate(sqlBrand);
      if(res?.success){
        this.selBrandList.push(sqlBrand);
      }
    },
    async handleBrandClose(o,i){
      var param = {
        id: o.id,
        brandName: o.brandName
      };

      var res = await delBrandSetDate(param);
      if(res?.success){
        this.selBrandList.splice(i, 1);
      }
    },
    async onExport(){
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
      }
      this.loading = true;
      var res = await exportGoodsBan(this.ListInfo);
      this.loading = false;
    }
  }
}
</script>

<style scoped lang="scss">
//控制选择器多选-标签宽度
::v-deep .el-select__tags-text {
  max-width: 35px;
}
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}
</style>
