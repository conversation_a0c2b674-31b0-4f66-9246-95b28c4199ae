<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="希音日报" name="first1" style="height: 100%">
        <productReportSheIn ref="productReportSheIn" style="height: 100%"></productReportSheIn>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import productReportSheIn from "./productReportSheIn.vue";
export default {
  name: "productReportSheInIndex",
  components: {
    MyContainer, productReportSheIn
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },
};
</script>

<style lang="scss" scoped></style>
