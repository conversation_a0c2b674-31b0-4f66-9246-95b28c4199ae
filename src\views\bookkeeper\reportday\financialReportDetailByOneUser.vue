<template>
    <my-container>
<div>
    <vxetablebase ref="table" :that='that'
             :tableData='financialreportlist'
          :tableCols='tableCols' :tableHandles='tableHandles' :summaryarry='summaryarry'  @sortchange='sortchange' :loading="listLoading" style="width:100%;height: 600px;margin: 0;">
    </vxetablebase>
    </div>
     </my-container>

</template>
<script>
import { getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
import {getDirectorGroupList,getDirectorList} from '@/api/operatemanage/base/shop'
import {exportFinancialStaticticsByUser,getParm,setParm,getFinancialStaticticsByUser,getDayReportStaticticsByIDUser} from '@/api/bookkeeper/reportday'
import productdrchart from '@/views/bookkeeper/reportday/productdrchart'
import {formatPlatform,formatTime,formatYesornoBool,formatLinkProCode} from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import InputMult from "@/components/Comm/InputMult";
import { Loading } from 'element-ui';
import { ruleDirectorGroup } from '@/utils/formruletools'
import freightDetail from '@/views/bookkeeper/reportday/freightDetail'
let loading;
const startLoading = () => {
  loading = Loading.service({
  lock: true,
  text: '加载中……',
  background: 'rgba(0, 0, 0, 0.7)'
  });
};

const tableCols =[



        {istrue:true,prop:'proCode',label:'商品ID',width:'120',type:'html',formatter:(row)=>formatLinkProCode(1,row.proCode)},
        {istrue:true, prop:'shopCode',label:'店铺名称',sortable:'custom', width:'220',formatter:(row)=> row.shopName},
        {istrue:true, prop:'goodsName',label:'商品名称',sortable:'custom', width:'350'},
        {istrue:true,prop:'payAmont',label:'付款金额',sortable:'custom',width:'100',formatter:(row)=> !row.payAmont?" ": row.payAmont.toFixed(2)},
        {istrue:true,prop:'saleAmont',label:'销售金额',sortable:'custom',width:'100',formatter:(row)=> !row.saleAmont?" ": row.saleAmont.toFixed(2)},
        {istrue:true, prop:'saleCost',label:'销售成本',sortable:'custom',width:'120',formatter:(row)=> row.saleCost==0?" ": row.saleCost?.toFixed(2)},
        {istrue:true,prop:'profit1',label:'毛一利润(发生)',width:'120',sortable:'custom',formatter:(row)=> !row.profit1?" ": row.profit1.toFixed(2)},
        {istrue:true,prop:'profit11',label:'毛一利润(付款)',width:'120',sortable:'custom',formatter:(row)=> !row.profit1?" ": row.profit1.toFixed(2)},
        {istrue:true,prop:'profit1Rate',label:'毛一利润率(发生)',width:'90',sortable:'custom',formatter:(row)=> !row.profit1Rate?" ": (row.profit1Rate*100).toFixed(2)+"%"},
        {istrue:true,prop:'profit1Rate1',label:'毛一利润率(付款)',width:'90',sortable:'custom',formatter:(row)=> !row.profit1Rate?" ": (row.profit1Rate*100).toFixed(2)+"%"},
        {istrue:true,prop:'alladv',label:'广告费',width:'100',sortable:'custom',formatter:(row)=> row.alladv==0?"0": row.alladv?.toFixed(2)},
        {istrue:true,prop:'alladv_rate',label:'广告占比',  width:'80',formatter:(row)=>row.saleAmont ==0 ?'0.00%' : (row.alladv / row.saleAmont * 100.00)?.toFixed(2).toString()+"%"},
        {istrue:true,prop:'profit3',label:'毛三利润(发生)',width:'100',sortable:'custom',type:'custom',formatter:(row)=> row.profit3==0?" ": row.profit3?.toFixed(2)},
        {istrue:true,prop:'profit31',label:'毛三利润(付款)',width:'100',sortable:'custom',type:'custom',formatter:(row)=> row.profit3==0?" ": row.profit3?.toFixed(2)},
        {istrue:true,prop:'profit3_rate',label:'毛三利润率(发生)',  width:'100',formatter:(row)=> row.saleAmont ==0 ?'0.00%' :(row.profit3/ row.saleAmont * 100.00)?.toFixed(2).toString()+"%"},
        {istrue:true,prop:'profit3_rate1',label:'毛三利润率(付款)',  width:'100',formatter:(row)=> row.saleAmont ==0 ?'0.00%' :(row.profit3/ row.saleAmont * 100.00)?.toFixed(2).toString()+"%"},
        { istrue: true, sortable: 'custom', prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33.toFixed(2) },
        { istrue: true, sortable: 'custom', prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33.toFixed(2) },
        { istrue: true, prop: 'profit6', label: '毛六利润', width: '90', sortable: 'custom', formatter: (row) => !row.profit6 ? " " : row.profit6.toFixed(2), permission: "profit6SixtyCents" },
        { istrue: true, prop: 'profit6Rate', label: '毛六利润率', width: '90', sortable: 'custom', formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + '%', permission: "profit6SixtyCents" },
        {istrue:true,prop:'profit4',label:'净利润(发生)',width:'80',sortable:'custom',formatter:(row)=> row.profit4==0?" ": row.profit4?.toFixed(2)},
        {istrue:true,prop:'profit41',label:'净利润(付款)',width:'80',sortable:'custom',formatter:(row)=> row.profit4==0?" ": row.profit4?.toFixed(2)},
        {istrue:true,prop:'profit4_rate',label:'净利率(发生)' ,sortable:'custom', width:'80',formatter:(row)=> row.saleAmont ==0 ?'0.00%' : (row.profit4 / row.saleAmont * 100.00)?.toFixed(2).toString()+"%"},
        {istrue:true,prop:'profit4_rate1',label:'净利率(付款)' ,sortable:'custom', width:'80',formatter:(row)=> row.saleAmont ==0 ?'0.00%' : (row.profit4 / row.saleAmont * 100.00)?.toFixed(2).toString()+"%"},


            ];
const tableHandles=[
      ];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable,productdrchart,InputMult,freightDetail,vxetablebase},
  data() {
    return {
      that:this,
      filter: {
        refundType: null,
        platform:null,
        shopCode:null,
        proCode:null,
        productName:null,
        groupId:null,
        startTime: null,
        endTime: null,
        timerange:null,
        // 运营助理
        userId :null,
        // 车手
        userId2:null,
        // 备用
        userId3:null,
        // 运营专员 ID
        operateSpecialUserId:null,
        Profit3Lose:null,
        listingStartTime:null,
        listingEndTime:null
      },
      onimportfilter:{
        yearmonthday:null,
      },
      shopList:[],
      userList:[],
      grouplist:[],
      directorlist:[],
      financialreportlist: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      total: 0,
      pager:{OrderBy:" saleAmont ",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      summaryarry:{},
      selids:[],
      fileList:[],
      dialogVisible:false,
      uploadLoading:false,
      importFilte:{},
      fileList:[],
      fileparm:{},
      editparmVisible:false,
      editLoading:false,
      editparmLoading:false,
      drawervisible:false,
      dialogDrVisible:false,
      drparamProCode:'',
      autoformparm:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[]
        },
      freightDetail:{
        visible:false,
        filter:{
          proCode:null,
          timeRange:[]
        }
      }
    };
  },

  async mounted() {
    this.init();
    var that=this;
    this.onSearch();
  },
  async created() {
    console.log("created")
    await this.getShopList();
  },
  methods: {
    onShowupMethod(){
      let that = this;
      that.$nextTick(() => {
      const takePlace = ['profit1', 'profit1Rate', 'profit3', 'profit3_rate', 'profit33', 'profit33Rate', 'exitCost', 'profit4', 'profit4_rate'];
      const payment = ['profit11', 'profit1Rate1', 'profit31', 'profit3_rate1', 'profit331', 'profit33Rate1', 'exitCost1', 'profit41', 'profit4_rate1'];
          if (that.$refs.table) {
            if (that.filter.refundType === 1) {
              that.$refs.table.changecolumn_setTrue(takePlace);
              that.$refs.table.changecolumn(payment);
            } else {
              that.$refs.table.changecolumn(takePlace);
              that.$refs.table.changecolumn_setTrue(payment);
            }
          }
      });
    },
    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init(){
        var date1 = new Date(); date1.setDate(date1.getDate()-10);
        var date2 = new Date(); date2.setDate(date2.getDate()-1);
        this.filter.timerange=[];
        this.filter.timerange[0]=this.datetostr(date1);
        this.filter.timerange[1]=this.datetostr(date2);
        console.log(this.filter)
      },
   async showprchart2(prcode){
      window['lastseeprcodedrchart']=prcode
      this.drparamProCode=prcode
      this.dialogDrVisible=true
   } ,
   async getShopList(){
      const res1 = await getAllShopList();
      this.shopList=[];
        res1.data?.forEach(f => {
          if(f.isCalcSettlement&&f.shopCode&&f.platform==2)
              this.shopList.push(f);
        });
        var res2= await getDirectorGroupList();
        this.grouplist = res2.data?.map(item => {return { value: item.key, label: item.value };});

        var res3= await getDirectorList();
        this.directorlist = res3.data?.map(item => {return { value: item.key, label: item.value };});
    },
    onRefresh(){
      this.onSearch()
    },
    async onSearch(){
       this.filter=window.financialReportDetailByOneUser;
       console.log(this.filter);
       this.onShowupMethod();
      await this.getList().then(res=>{  });
      // loading.close();
    },
    async getList(){
      //this.filter.startTime =null;
     // this.filter.endTime =null;
      // if (this.filter.timerange) {
      //   this.filter.startTime = this.filter.timerange[0];
      //   this.filter.endTime = this.filter.timerange[1];
      // }
      var that=this;
      this.pager.pageSize=100;
      this.pager.currentPage=1;
      const params = {...this.pager,...this.filter};
     this.listLoading = true;
      // startLoading();
      const res = await getDayReportStaticticsByIDUser(params).then(res=>{
          // loading.close();
          this.listLoading = false;
          that.total = res.data?.total;
          if(res?.data?.list&&res?.data?.list.length>0){
            for (var i in res.data.list) {
              if (!res.data.list[i].freightFee) {
                res.data.list[i].freightFee =" ";
              }
            }
          }
          that.financialreportlist = res.data?.list;
          that.summaryarry=res.data?.summary;
      });
    },
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
     await this.onSearch();
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },

  }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>

