<template>
 <div>
  <vxe-table
    border
    resizable
    height="300"
    :scroll-y="{enabled: false}"
    :span-method="mergeRowMethod"
    :data="skulist">
    <vxe-column field="colour" title="款式"></vxe-column>
    <!-- <vxe-column field="spec" title="型号"></vxe-column> -->
    <vxe-column field="skuCode" title="sku编码"></vxe-column>
    <!-- <vxe-column field="supplyPrice" title="售价"></vxe-column> -->
    <vxe-column field="costPrice" title="成本价"></vxe-column>
    <vxe-column field="supplyPrice" title="聚水潭供货价"></vxe-column>
    <vxe-column field="baseSalePrice" title="ERP供货价"></vxe-column>
    <vxe-column field="stockQuantity" title="库存数"></vxe-column>
    <vxe-column field="weight" title="重量"></vxe-column>
  </vxe-table>
 </div>
</template>

<script>
export default {
 name: 'Vue2demoGoodstable',
 props: ['skulist'],
 data() {
  return {
  };
 },

 mounted() {
  
 },

 methods: {
  mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
     const fields = ['colour','spec']
     const cellValue = row[column.property]
     if (cellValue && fields.includes(column.property)) {
       const prevRow = visibleData[_rowIndex - 1]
       let nextRow = visibleData[_rowIndex + 1]
       if (prevRow && prevRow[column.property] === cellValue) {
         return { rowspan: 0, colspan: 0 }
       } else {
         let countRowspan = 1
         while (nextRow && nextRow[column.property] === cellValue) {
           nextRow = visibleData[++countRowspan + _rowIndex]
         }
         if (countRowspan > 1) {
           return { rowspan: countRowspan, colspan: 1 }
         }
       }
     }
   }
 },
};
</script>

<style lang="scss" scoped>

</style>