<template>
  <my-container v-loading="pageLoading">
    <template #header>
    </template>
    <vxetablebase :id="'goodsCostPriceChgList2023010313180'" :tableData='list' :tableCols='tableCols'
      :loading='listLoading' :border='true' :that="that" ref="vxetable" :somerow="'planArrivalTime'"
      @sortchange='sortchange' :showsummary='true' :summaryarry='summaryarry'>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

  </my-container>
</template>
  
<script>
import { getPurchaseArrivalInfoAsync } from '@/api/inventory/purchase'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import probianmaanalysis from '@/views/inventory/probianmaanalysis'
import goodscoderecord from '@/views/inventory/goodscoderecord'
import logistics from '@/components/Comm/logistics'
import { formatYesornoBool, formatPurchasePlanError, formatmoney, formatTime, formatNoLink, formatSecondToHour } from "@/utils/tools";
const tableCols = [
  { istrue: true, prop: 'planArrivalTime', label: '预计到货日期', width: '200', formatter: (row) => formatTime(row.planArrivalTime, 'YYYY-MM-DD') },
  { istrue: true, prop: 'warehouse', label: '第三方物流和分仓', width: '200', formatter: (row) => row.warehouseName },
  { istrue: true, prop: 'count', label: '采购单数', width: '150', },
  { istrue: true, prop: 'boxCount', label: '到货数量', width: '200' },
  { istrue: true, prop: 'notBcBoxCount', label: '剔除包材到货数量', width: '200' },
  { istrue: true, prop: 'packNum', label: '到货件数', width: '200' },
  { istrue: true, prop: 'notBcPackNum', label: '剔除包材到货件数', width: 'auto' }
];
const tableHandles = [];
export default {
  name: 'Roles',
  props: {
    filter: {}
  },
  components: { cesTable, MyContainer, MyConfirmButton, probianmaanalysis, goodscoderecord, logistics, vxetablebase },
  data() {
    return {
      that: this,
      formatTime: formatTime,
      analysisfilter: {
        startDate: null,
        endDate: null,
        proBianMa: ""
      },
      goodscoderecordfilter: { goodsCode: "", buyNo: "" },
      list: [],
      brandlist: [],
      recodelist: [],
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      sels: [],
      listLoading: false,
      pageLoading: false,
      importVisible: false,
      prevTarget: null, // 编辑 Popover 的 Reference （参照），用于 popover.js 对齐两个元素
      popperFlag: false, // 用于编辑 Popover 的刷新
      visiblepopover: false,
      visiblepopoverdetail: false,
      dialogOrderDetailVisible: false,
      popperFlagdetail: false,
      pager: { OrderBy: "", IsAsc: false },
      summaryarry: {},
      selids: [],
      fileList: [],
      uploadLoading: false,
      lastUpdateTime: '',
      onExporting: false,
      dialogVisible: false,
      editVisible: false,
      editLoading: false,
      drawervisible: false,
      autoform: {
        fApi: {},
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
        rule: []
      },
    }
  },
  watch: {
    value(n) {
      if (n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
      }
    }
  },
  async mounted() {
    //this.init();
    this.getlist();
    //this.initform();
    //formCreate.component('editor', FcEditor);
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      this.filter.startDate = null;
      this.filter.endDate = null;
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      const params = { ...pager, ...this.pager, ... this.filter }
      this.listLoading = true
      const res = await getPurchaseArrivalInfoAsync(params)
      this.listLoading = false
      if (!res?.success) return
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry = res.data.summary;
    },
    async cellclick(row, column, cell, event) {

    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
  }
}
</script>
  