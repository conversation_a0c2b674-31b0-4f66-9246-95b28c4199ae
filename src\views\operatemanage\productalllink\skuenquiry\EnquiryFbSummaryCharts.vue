<template>
    <my-container v-loading="pageLoading">
        <el-row>
            <el-col :span="24">
                <el-select style="width:180px;margin-left:5px;" v-model="Filter.yyPlatforms" placeholder="运营平台" multiple clearable filterable :collapse-tags="true">
                    <el-option v-for="(item ) in platformlist" :key="'platform-'+item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>

                <!-- 运营组 -->
                <el-select style="width:180px;margin-left:5px;" v-model="Filter.yyGroupIds" placeholder="运营组" multiple clearable filterable  :collapse-tags="true">
                    <el-option v-for="(item ) in yyGroupList" :key="'yyGroup-'+item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>

                <!-- 含已归档  0不含、1含 -->              
                <el-radio-group v-model="Filter.hasContainSealClose" size="mini"  style="margin-left:5px;">
                    <el-radio-button :label="0">不含归档</el-radio-button>                      
                    <el-radio-button :label="1">含归档</el-radio-button>                   
                </el-radio-group>

                <!-- 统计维度 0按品次、1按人次 -->              
                <el-radio-group v-model="Filter.sumType" size="mini" style="margin-left:5px;">
                    <el-tooltip class="item" effect="dark" content="按品次统计时，取时间范围内登记的询价，以及这些询价的是否有报价(一个询价对应的所有报价只计一次)与采纳情况。报价与采纳不受时间范围限制。" placement="top-start">
                        <el-radio-button :label="0">按品次统计</el-radio-button>
                    </el-tooltip>
                    
                    <el-tooltip class="item" effect="dark" content="按人次统计时，取时间范围内登记的询价、时间范围内的报价次数、时间范围内采纳次数。三者无关联关系。" placement="top-start">
                        <el-radio-button :label="1">按人次统计</el-radio-button>       
                    </el-tooltip>            
                </el-radio-group>

                <el-date-picker style="width:220px;margin-left:10px;" v-model="Filter.gDate" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" :clearable="false" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions">
                </el-date-picker>
                <el-button type="primary" @click="loadData">查询</el-button>
            </el-col>           
        </el-row>
        <el-row style="width:100%;height:86%;">
            <el-col :span="24" style="width:100%;margin-top:20px;height:100%;" >
                <div style="min-width:1024px;min-height:500px;width:100%;height:100%;" :ref="'echarts1'" :id="'rptIdecharts1'" v-loading="echartsLoading1" ></div>
            </el-col>
        </el-row>

        <el-dialog title="详情" :visible.sync="echartsDtlVisible" :loading="echartsDtlLoading" width="80%" v-dialogDrag>          
            <div style="min-width:900px;width:100%;height:600px;" :ref="'echartsDtl'"></div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="echartsDtlVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>

    import * as echarts from 'echarts'
    import MyContainer from "@/components/my-container";
    import {
        platformlist, pickerOptions, formatTime
    } from "@/utils/tools";
    import dayjs from "dayjs";
    import { GetEnquiryFeedbakSummaryCharts } from '@/api/operatemanage/productalllink/alllink'
    import { getDirectorGroupList } from '@/api/operatemanage/base/shop';

    const endTime = formatTime(new Date(), "YYYY-MM-DD");
    const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
    const checkOptions = ['已选品', '已计算利润', '已确认', '已采样', '已建编码', '已归档'];

    const constEchartOpts = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        toolbox: {
            feature: {
                dataView: { show: true, readOnly: false },
                magicType: { show: true, type: ['line', 'bar'] },
                restore: { show: false },
                saveAsImage: { show: true }
            }
        },
        yAxis: {
            type: 'value'
        },
    }

    export default {
        name: "EnquiryFbSummaryCharts",
        components: { MyContainer },
        data() {
            return {
                platformlist: platformlist,
                pickerOptions: pickerOptions,
                yyGroupList:[],
                Filter: {
                    yyPlatforms: [],
                    startTime: '',
                    endTime: '',
                    hasContainSealClose:0,
                    sumType:0,
                    gDate: [startTime, endTime]
                },
                that: this,
                pageLoading: false,
                isIndeterminate: true,
                checkAll: false,
                echartsLoading1: false,
                constEchartOpts: constEchartOpts,
                chart1Opt: {},
                myChart1:{},   
                //明细图
                echartsDtlVisible:false,
                echartsDtlLoading:false,
                dtlFilter:{}
           
            };
        },
        async mounted(){
            var res = await getDirectorGroupList();
            this.yyGroupList = res.data?.map(item => { return { value: item.key, label: item.value }; });    

            // this.loadData();
        },
        methods: {         
            resize(){
                let self=this;
                if(self.myChart1 && self.myChart1.resize)
                    self.myChart1.resize();
              
            },
            async loadData() {
                let self = this;
                this.echartsLoading1 = true;

                if (this.Filter.gDate) {
                    this.Filter.startTime = this.Filter.gDate[0];
                    this.Filter.endTime = this.Filter.gDate[1];
                }
                else {
                    this.Filter.startTime = null;
                    this.Filter.endTime = null;
                }

                let rlt = await GetEnquiryFeedbakSummaryCharts({ ...this.Filter });
                if (rlt && rlt.success) {

                    this.dtlFilter={ ...this.Filter };

                    let opt1 = { ...constEchartOpts, ...rlt.data.option1 };

                    //检测是否已经存在echarts实例，如果不存在，则不再去初始化
                    let myChart = echarts.getInstanceByDom(
                        this.$refs['echarts1']
                    );
                    if (myChart == null) {
                        myChart = echarts.init(this.$refs['echarts1']);                    


                        myChart.on('selectchanged',function(v){                           
                            self.groupDtlReload(self.chart1Opt.xAxis[0].data[v.fromActionPayload.dataIndexInside])
                        });

                        window.addEventListener("resize", () => {
                            myChart.resize();
                        });
                    }

                    self.myChart1=myChart;     

                    this.chart1Opt = opt1;

                    myChart.setOption(this.chart1Opt,true);                 
                }

                this.echartsLoading1 = false;

            },    
            async groupDtlReload(groupName){               

                if(!groupName)
                    return;              

                let self = this;    
               
                this.dtlFilter={ ...this.Filter,DtlDate:groupName };

                let rlt = await GetEnquiryFeedbakSummaryCharts({ ...this.dtlFilter });
                if (rlt && rlt.success){
                    let opt = { ...constEchartOpts, ...rlt.data.option1 };
                                    
                    self.echartsDtlVisible=true;
                 
                    self.echartsDtlLoading=true;
                    
                    
                    self.$nextTick(()=>{
                        
                        //检测是否已经存在echarts实例，如果不存在，则不再去初始化
                        let myChart = echarts.getInstanceByDom(
                            this.$refs['echartsDtl']
                        );
                        if (myChart == null) {
                            myChart = echarts.init(this.$refs['echartsDtl']);    
                            window.addEventListener("resize", () => {
                                myChart.resize();
                            });                                            
                        }
                        myChart.setOption(opt,true);
                        self.echartsDtlLoading=false;      
                        
                        _.delay(function(){
                            myChart.resize()
                        },300);
                    })

                }
            },  
          
        }
    }

</script>