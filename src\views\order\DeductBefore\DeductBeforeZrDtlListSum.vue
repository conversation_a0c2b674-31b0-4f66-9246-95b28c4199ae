<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-date-picker style="width: 220px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" range-separator="至" :start-placeholder="'提交开始时间'" :end-placeholder="'提交结束时间'"
                :picker-options="pickerOptions">
            </el-date-picker>

            <el-button type="primary" @click="onSearch(true)">查询</el-button>
        </template>
        <template>

            <div style="text-align: center; margin-bottom: 10px;">
                <el-checkbox name="suoyou" v-model="filter.myTypeCheck.suoyou"
                    @change="onSearch(false)">所有</el-checkbox>
                <el-checkbox name="bpc" v-model="filter.myTypeCheck.bpc" @change="onSearch(false)">绑批次超时</el-checkbox>
                <el-checkbox name="zph" v-model="filter.myTypeCheck.zph" @change="onSearch(false)">杂配货超时</el-checkbox>
                <el-checkbox name="tdd" v-model="filter.myTypeCheck.tdd" @change="onSearch(false)">团打单超时</el-checkbox>
                <el-checkbox name="tph" v-model="filter.myTypeCheck.tph" @change="onSearch(false)">团配货超时</el-checkbox>
                <el-checkbox name="db" v-model="filter.myTypeCheck.db" @change="onSearch(false)">打包超时</el-checkbox>
                <el-checkbox name="cz" v-model="filter.myTypeCheck.cz" @change="onSearch(false)">称重超时</el-checkbox>
            </div>
            <div style="text-align: center; margin-bottom: 10px;">
                <el-checkbox name="cang0" v-model="filter.myWareCheck.cang0" @change="onSearch(false)">全仓</el-checkbox>
                <el-checkbox name="cang1" v-model="filter.myWareCheck.cang1"
                    @change="onSearch(false)">义乌市昀晗供应链管理有限公司</el-checkbox>
                <el-checkbox name="cang2" v-model="filter.myWareCheck.cang2"
                    @change="onSearch(false)">【义乌圆通孵化仓】</el-checkbox>
                <el-checkbox name="cang3" v-model="filter.myWareCheck.cang3"
                    @change="onSearch(false)">【义乌圆通爆款仓】</el-checkbox>
                <el-checkbox name="cang4" v-model="filter.myWareCheck.cang4"
                    @change="onSearch(false)">【义乌圆通2楼】</el-checkbox>
                <el-checkbox name="cang5" v-model="filter.myWareCheck.cang5"
                    @change="onSearch(false)">【义乌圆通3楼】</el-checkbox>
                <el-checkbox name="cang6" v-model="filter.myWareCheck.cang6"
                    @change="onSearch(false)">【义乌圆通5楼】</el-checkbox>
                <el-checkbox name="cang7" v-model="filter.myWareCheck.cang7"
                    @change="onSearch(false)">【义乌邮政仓】</el-checkbox>
                <el-checkbox name="cang8" v-model="filter.myWareCheck.cang8"
                    @change="onSearch(false)">【义乌加工仓】</el-checkbox>
                <el-checkbox name="cang9" v-model="filter.myWareCheck.cang9"
                    @change="onSearch(false)">【天天2楼仓】</el-checkbox>
                <el-checkbox name="cang10" v-model="filter.myWareCheck.cang10"
                    @change="onSearch(false)">【义乌跨境仓】</el-checkbox>
                <el-checkbox name="cang11" v-model="filter.myWareCheck.cang11"
                    @change="onSearch(false)">【杭州分仓】</el-checkbox>
                <el-checkbox name="cang12" v-model="filter.myWareCheck.cang12"
                    @change="onSearch(false)">【南昌全品类仓】</el-checkbox>
                <el-checkbox name="cang13" v-model="filter.myWareCheck.cang13"
                    @change="onSearch(false)">【南昌定制仓】</el-checkbox>
                <el-checkbox name="cang14" v-model="filter.myWareCheck.cang14"
                    @change="onSearch(false)">【南昌裁剪仓】</el-checkbox>
                <el-checkbox name="cang15" v-model="filter.myWareCheck.cang15"
                    @change="onSearch(false)">【西安港务分仓】</el-checkbox>
                <el-checkbox name="cang16" v-model="filter.myWareCheck.cang16"
                    @change="onSearch(false)">【西安分仓】</el-checkbox>
                <el-checkbox name="cang17" v-model="filter.myWareCheck.cang17"
                    @change="onSearch(false)">预包加工仓</el-checkbox>
                <el-checkbox name="cang18" v-model="filter.myWareCheck.cang18"
                    @change="onSearch(false)">昀晗-KS</el-checkbox>
                <el-checkbox name="cang19" v-model="filter.myWareCheck.cang19"
                    @change="onSearch(false)">昀晗-退件仓</el-checkbox>
                <el-checkbox name="cang20" v-model="filter.myWareCheck.cang20"
                    @change="onSearch(false)">昀晗-云仓</el-checkbox>
                <!-- <el-checkbox name="cang21" v-model="filter.myWareCheck.cang21"
                    @change="onSearch(false)">昀晗-gd</el-checkbox> -->
                <el-checkbox name="cang22" v-model="filter.myWareCheck.cang22"
                    @change="onSearch(false)">JD-昀晗义乌仓</el-checkbox>
                <el-checkbox name="cang23" v-model="filter.myWareCheck.cang23"
                    @change="onSearch(false)">外仓加工-成品仓</el-checkbox>
                <el-checkbox name="cang24" v-model="filter.myWareCheck.cang24"
                    @change="onSearch(false)">昀晗-中转仓</el-checkbox>
                <el-checkbox name="cang25" v-model="filter.myWareCheck.cang25"
                    @change="onSearch(false)">【昀晗-WH】</el-checkbox>
            </div>

            <div style="font-size: 20px; font-weight: bold;">
              违规罚款次数图
            </div>
            <div>
                <buschar ref="indexchatbuschar" :analysisData="indexChatData" :legendChanges="legendChanges">
                </buschar>
            </div>

            <div style="font-size: 20px; font-weight: bold;">
              违规罚款金额图
            </div>
            <div>
                <buschar ref="indexchatbuschar2" :analysisData="indexChatData2" :legendChanges="legendChanges2">
                </buschar>
            </div>

        </template>
    </my-container>
</template>

<script>
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import { pickerOptions } from '@/utils/tools'
import { formatLinkProCode, formatSendWarehouse, formatExpressCompany } from "@/utils/tools";
import { GetDeductChatData } from "@/api/order/deductbefore"
import MyContainer from "@/components/my-container";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import MyConfirmButton from '@/components/my-confirm-button'
import buschar from '@/components/Bus/buschar';
export default {
    name: 'DeductBeforeZrDtlList',
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, buschar },
    data() {
        return {
            that: this,
            filter: {
                submitDateStart: null,
                submitDateEnd: null,
                isSearch: true,
                isAmount: false,
                timerange: [
                    formatTime(dayjs().subtract(1, "month"), "YYYY-MM-DD"),
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                ],
                myTypeCheck: {
                    suoyou: true,
                    bpc: false,
                    zph: false,
                    tdd: false,
                    tph: false,
                    db: false,
                    cz: false,
                },
                myWareCheck: {
                    cang0: true,
                    cang1: false,
                    cang2: false,
                    cang3: false,
                    cang4: false,
                    cang5: false,
                    cang6: false,
                    cang7: false,
                    cang8: false,
                    cang9: false,
                    cang10: false,
                    cang11: false,
                    cang12: false,
                    cang13: false,
                    cang14: false,
                    cang15: false,
                    cang16: false,
                    cang17: false,
                    cang18: false,
                    cang19: false,
                    cang20: false,
                    cang21: false,
                    cang22: false,
                    cang23: false,
                    cang24: false,
                    cang25: false,
                }
            },
            pickerOptions: pickerOptions,
            list: [],
            listLoading: false,
            pageLoading: false,

            indexChatData: {},
            indexChatSelectedLegend: [],

            indexChatData2: {},
            indexChatSelectedLegend2: [],
        };
    },
    async mounted() {
        await this.onSearch(true);
    },
    methods: {
        //查询第一页
        async onSearch(isSearch) {
            this.filter.isSearch = isSearch;
            this.filter.isAmount = false;
            
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.submitDateStart = this.filter.timerange[0];
                this.filter.submitDateEnd = this.filter.timerange[1];
            } else {
                this.$message({ message: "请先选择日期", type: "warning" });
                return false;
            }
            this.pageLoading = true;
            let res = await GetDeductChatData(this.filter);
            this.pageLoading = false;
            this.indexChatData = res;
            this.$nextTick(() => {
                this.$refs.indexchatbuschar.initcharts();
            });

            this.filter.isAmount = true;
            this.pageLoading = true;
            let res2 = await GetDeductChatData(this.filter);
            this.pageLoading = false;
            this.indexChatData2 = res2;
            this.$nextTick(() => {
                this.$refs.indexchatbuschar2.initcharts();
            });

        },
        async legendChanges(selected) {
            this.indexChatSelectedLegend = selected;
        },
        async legendChanges2(selected) {
            this.indexChatSelectedLegend2 = selected;
        },
    },
};
</script>

<style lang="scss" scoped></style>
