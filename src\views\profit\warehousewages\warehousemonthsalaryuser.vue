<!-- 常规计件 -->
<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">

                <el-form-item label="">
                    <el-input v-model.trim="filter.companyName" placeholder="公司" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.warehouseCode" style="width: 160px" size="mini" placeholder="仓库"
                        @change="onSearch">
                        <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label" clearable
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.title" placeholder="岗位" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <!-- <el-input v-model.trim="filter.ddUserId" placeholder="员工ID" style="width:110px;" clearable
                        maxlength="20" /> -->
                    <inputYunhan title="请输入员工ID" :row="12" placeholder="员工ID" :maxRows="300" :inputshow="0"
                        :clearable="true" :inputt.sync="filter.ddUserId" @callback="callbackDDUserId"></inputYunhan>
                </el-form-item>
                <el-form-item label="">
                    <!-- <el-input v-model.trim="filter.userName" placeholder="姓名" style="width:110px;" clearable
                        maxlength="20" /> -->
                    <inputYunhan title="请输入姓名" :row="12" placeholder="姓名" :maxRows="300" :inputshow="0"
                        :clearable="true" :inputt.sync="filter.userName" @callback="callbackUserName"></inputYunhan>
                </el-form-item>
                <el-form-item label="">
                    <el-date-picker style="width: 120px" v-model="filter.yearMonth" type="month" placeholder="月份"
                        format="yyyyMM" value-format="yyyyMM" clearable>
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onaddsalaryuser">新增</el-button>
                    <el-button type="primary" @click="onmustdays">设置应出勤</el-button>
                    <el-button type="primary" @click="onbatchmonthsalary">批量设置薪资</el-button>
                    <el-button type="primary" @click="onImport2">导入人员</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="true"
                @select="selectchange" :isSelectColumn="false" :loading="listLoading" :tableHandles="tableHandles1">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog :title="salaryuserTitle" :visible.sync="salaryuserVisible" width="30%" v-dialogDrag
            :close-on-click-modal="false" v-loading="salaryusersaveLoading">
            <span>
                <el-form class="ad-form-query" :model="salaryuserFormData" :inline="true" :rules="salaryuserFormRules"
                    label-width="140px">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="仓库" prop="warehouseCode">
                                <el-select v-model="salaryuserFormData.warehouseCode" style="width: 260px" size="mini"
                                    filterable>
                                    <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="姓名" prop="ddUserId">
                                <el-select v-model="salaryuserFormData.ddUserId" style="width: 260px" size="mini"
                                    filterable remote reserve-keyword :remote-method="getDDUserList"
                                    @change="onDDUserIdChange" placeholder="请输入姓名" :loading="salaryuseroptionLoading"
                                    :disabled="salaryuserTitle != '新增'">
                                    <el-option v-for="item in ddUserList" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="公司" prop="companyName">
                                <el-input v-model.trim="salaryuserFormData.companyName" placeholder=""
                                    style="width:260px;" clearable maxlength="20" disabled />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="岗位" prop="title">
                                <el-input v-model.trim="salaryuserFormData.title" placeholder="" style="width:260px;"
                                    clearable maxlength="20" disabled />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="手机号" prop="mobile">
                                <el-input v-model.trim="salaryuserFormData.mobile" placeholder="" style="width:260px;"
                                    clearable maxlength="20" disabled />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row v-if="(salaryuserTitle == '新增')">
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="月薪" prop="lastSetMonthSalary">
                                <el-input-number v-model.trim="salaryuserFormData.lastSetMonthSalary" :min="0"
                                    :max="1000000" auto-complete="off" :precision="2" style="width:260px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row v-if="(salaryuserTitle == '新增')">
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="绩效" prop="lastSetMonthSalary2">
                                <el-input-number v-model.trim="salaryuserFormData.lastSetMonthSalary2" :min="0"
                                    :max="1000000" auto-complete="off" :precision="2" style="width:260px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label=" " prop="isManager">
                                <el-checkbox v-model="salaryuserFormData.isManager">是否管理岗</el-checkbox>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </span>
            <span slot="footer" class="dialog-footer">
                <my-confirm-button type="submit" @click="onsalaryusersave" /> &nbsp;
                <el-button @click="salaryuserVisible = false">关闭</el-button>
            </span>
        </el-dialog>


        <el-dialog title="设置“所有人”应出勤" :visible.sync="mustdaysVisible" width="20%" v-dialogDrag
            :close-on-click-modal="false" v-loading="mustdaysLoading">
            <span>
                <el-form class="ad-form-query" :model="mustdaysFormData" :inline="true" :rules="mustdaysFormRules"
                    label-width="100px">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="月份" prop="yearMonth">
                                <el-date-picker style="width: 180px" v-model="mustdaysFormData.yearMonth" type="month"
                                    format="yyyyMM" value-format="yyyyMM" clearable>
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="应出勤天数" prop="mustWorkDays">
                                <el-input-number v-model.trim="mustdaysFormData.mustWorkDays" :min="0" :max="31"
                                    auto-complete="off" :precision="2" style="width:180px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="onmustdayssave">提交</el-button>
                <el-button @click="mustdaysVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="批量设置月薪" :visible.sync="batchsetsalaryuserVisible" width="20%" v-dialogDrag
            :close-on-click-modal="false" v-loading="batchsetsalaryuserLoading">
            <span>
                <el-form class="ad-form-query" :model="batchsetsalaryuserFormData" :inline="true"
                    :rules="batchsetsalaryuserFormRules" label-width="100px">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="月份" prop="yearMonth">
                                <el-date-picker style="width: 180px" v-model="batchsetsalaryuserFormData.yearMonth"
                                    type="month" format="yyyyMM" value-format="yyyyMM" clearable>
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="月薪" prop="monthSalary">
                                <el-input-number v-model.trim="batchsetsalaryuserFormData.monthSalary" :min="0"
                                    :max="1000000" auto-complete="off" :precision="2" style="width:180px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="绩效" prop="monthSalary2">
                                <el-input-number v-model.trim="batchsetsalaryuserFormData.monthSalary2" :min="0"
                                    :max="1000000" auto-complete="off" :precision="2" style="width:180px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="房补" prop="houseSubsidy">
                                <el-input-number v-model.trim="batchsetsalaryuserFormData.houseSubsidy" :min="0"
                                    :max="1000000" auto-complete="off" :precision="2" style="width:180px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="餐补" prop="eatSubsidy">
                                <el-input-number v-model.trim="batchsetsalaryuserFormData.eatSubsidy" :min="0"
                                    :max="1000000" auto-complete="off" :precision="2" style="width:180px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="夜班补贴" prop="nightSubsidy">
                                <el-input-number v-model.trim="batchsetsalaryuserFormData.nightSubsidy" :min="0"
                                    :max="1000000" auto-complete="off" :precision="2" style="width:180px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="全勤" prop="fullAttendance">
                                <el-input-number v-model.trim="batchsetsalaryuserFormData.fullAttendance" :min="0"
                                    :max="1000000" auto-complete="off" :precision="2" style="width:180px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="onbatchsetsalaryusersave">提交</el-button>
                <el-button @click="batchsetsalaryuserVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="导入人员" :visible.sync="dialogVisibleUpload2" width="30%" v-dialogDrag
            :close-on-click-modal="false">
            <span>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                            action accept=".xlsx" :file-list="fileList2" :data="fileparm2" :http-request="onUploadFile2"
                            :on-success="onUploadSuccess2" :on-change="onUploadChange2" :on-remove="onUploadRemove2">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading2"
                                @click="onSubmitUpload2">{{ (uploadLoading2 ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleUpload2 = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="设置人员月薪" :visible.sync="setsalaryuserVisible" width="20%" v-dialogDrag
            :close-on-click-modal="false" v-loading="setsalaryuserLoading">
            <span>
                <el-form class="ad-form-query" :model="setsalaryuserFormData" :inline="true"
                    :rules="setsalaryuserFormRules" label-width="100px">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="月份" prop="yearMonth">
                                <el-date-picker style="width: 180px" v-model="setsalaryuserFormData.yearMonth"
                                    type="month" format="yyyyMM" value-format="yyyyMM" clearable>
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="月薪" prop="monthSalary">
                                <el-input-number v-model.trim="setsalaryuserFormData.monthSalary" :min="0"
                                    :max="1000000" auto-complete="off" :precision="2" style="width:180px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="绩效" prop="monthSalary2">
                                <el-input-number v-model.trim="setsalaryuserFormData.monthSalary2" :min="0"
                                    :max="1000000" auto-complete="off" :precision="2" style="width:180px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="房补" prop="houseSubsidy">
                                <el-input-number v-model.trim="setsalaryuserFormData.houseSubsidy" :min="0"
                                    :max="1000000" auto-complete="off" :precision="2" style="width:180px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="餐补" prop="eatSubsidy">
                                <el-input-number v-model.trim="setsalaryuserFormData.eatSubsidy" :min="0" :max="1000000"
                                    auto-complete="off" :precision="2" style="width:180px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="夜班补贴" prop="nightSubsidy">
                                <el-input-number v-model.trim="setsalaryuserFormData.nightSubsidy" :min="0"
                                    :max="1000000" auto-complete="off" :precision="2" style="width:180px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="全勤" prop="fullAttendance">
                                <el-input-number v-model.trim="setsalaryuserFormData.fullAttendance" :min="0"
                                    :max="1000000" auto-complete="off" :precision="2" style="width:180px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="onsetsalaryusersave">提交</el-button>
                <el-button @click="setsalaryuserVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>

<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import cesTable from "@/components/Table/table.vue";
import MySearch from "@/components/my-search";
import buschar from '@/components/Bus/buschar';
import MySearchWindow from "@/components/my-search-window";
import inputYunhan from '@/components/Comm/inputYunhan.vue'
import {
    getWarehouseMonthSalaryUserPageList, getDingtalkUsers, saveWarehouseMonthSalaryUser,
    enabledWarehouseMonthSalaryUser, deleteWarehouseMonthSalaryUser,
    batchWarehouseMonthSalaryUserSet, mustDayWarehouseMonthSalaryUserSet,
    importWarehouseMonthSalaryUser
} from '@/api/profit/warehousewages';
const tableCols = [
    { istrue: true, prop: 'companyName', label: '公司', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'warehouseName', label: '仓库', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'title', label: '岗位', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'ddUserId', label: '员工ID', width: '180', sortable: 'custom' },
    { istrue: true, prop: 'userName', label: '姓名', width: '70', sortable: 'custom' },
    { istrue: true, prop: 'isManager', label: '是否管理岗', width: '60', sortable: 'custom', formatter: (row) => row.isManager ? "是" : "否" },
    //{ istrue: true, prop: 'workType', label: '考勤班次', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'enabled', label: '状态', width: '60', sortable: 'custom', formatter: (row) => row.enabled ? "禁用" : "启用" },
    { istrue: true, prop: 'yearMonth', label: '月份', width: '60', sortable: 'custom', formatter: (row) => row.lastSetYearMonth },
    { istrue: true, prop: 'mustWorkDays', label: '应出勤天数', width: '60', sortable: 'custom', formatter: (row) => row.lastSetMustWorkDays },
    { istrue: true, prop: 'monthSalary', label: '月薪', width: '60', sortable: 'custom', formatter: (row) => row.lastSetMonthSalary },
    { istrue: true, prop: 'monthSalary2', label: '绩效', width: '60', sortable: 'custom', formatter: (row) => row.lastSetMonthSalary2 },
    { istrue: true, prop: 'houseSubsidy', label: '房补', width: '60', sortable: 'custom', formatter: (row) => row.lastSetHouseSubsidy },
    { istrue: true, prop: 'eatSubsidy', label: '餐补', width: '60', sortable: 'custom', formatter: (row) => row.lastSetEatSubsidy },
    { istrue: true, prop: 'nightSubsidy', label: '夜班补贴', width: '60', sortable: 'custom', formatter: (row) => row.lastSetNightSubsidy },
    { istrue: true, prop: 'fullAttendance', label: '全勤', width: '60', sortable: 'custom', formatter: (row) => row.lastSetFullAttendance },
    {
        istrue: true, type: 'button', label: '操作', width: '240',
        btnList: [
            { label: "编辑", handle: (that, row) => that.oneditsalaryuser(row.id, row.warehouseCode) },
            { label: "删除", handle: (that, row) => that.ondeletesalaryuser(row.id) },
            { label: "禁用", ishide: (that, row) => row.enabled == true, handle: (that, row) => that.onenabledsalaryuser(row.id, true) },
            { label: "启用", ishide: (that, row) => row.enabled == false, handle: (that, row) => that.onenabledsalaryuser(row.id, false) },
            { label: "设置月薪", handle: (that, row) => that.onmonthsalary(row) },
            { label: "查看月薪", handle: (that, row) => that.showusermonthsalary(row) },
        ]
    },
]
const tableHandles1 = [
    //{ label: "计算人效", handle: (that) => that.onCompute() },
    // { label: "导出", handle: (that) => that.onExport() },
];
export default {
    name: 'warehousemonthsalaryuser',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, buschar, inputYunhan },
    props: ['myWarehouseList'],
    data() {
        return {
            that: this,
            filter: {
                yearMonth: formatTime(dayjs(), "YYYYMM")
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "createdtime", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            selids: [],
            listLoading: false,
            pageLoading: false,
            tableHandles1: tableHandles1,

            ddUserList: [],
            salaryuseroptionLoading: false,

            salaryuserTitle: "",
            salaryuserVisible: false,
            salaryusersaveLoading: false,
            salaryuserFormData: {
                warehouseCode: null,
                warehouseName: null,
                ddUserId: null,
                companyName: null,
                title: null,
                mobile: null,
                userName: null,
                isManager: false,
                lastSetMonthSalary: 0,
                lastSetMonthSalary2: 0,
                lastSetHouseSubsidy: 0,
                lastSetEatSubsidy: 0,
                lastSetNightSubsidy: 0,
                lastSetFullAttendance: 0,
            },
            salaryuserFormRules: {
                warehouseCode: [{ required: true, message: '请输入仓库', trigger: 'blur' }],
                ddUserId: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
            },

            mustdaysVisible: false,
            mustdaysLoading: false,
            mustdaysFormData: {
                yearMonth: null,
                mustWorkDays: null
            },
            mustdaysFormRules: {
                yearMonth: [{ required: true, message: '请输入月份', trigger: 'blur' }],
                mustWorkDays: [{ required: true, message: '请输入应出勤', trigger: 'blur' }]
            },

            batchsetsalaryuserVisible: false,
            batchsetsalaryuserLoading: false,
            batchsetsalaryuserFormData: {
                yearMonth: null,
                monthSalary: null,
                monthSalary2: null,
                houseSubsidy: null,
                eatSubsidy: null,
                nightSubsidy: null,
                fullAttendance: null,
            },
            batchsetsalaryuserFormRules: {
                yearMonth: [{ required: true, message: '请输入月份', trigger: 'blur' }],
                monthSalary: [{ required: true, message: '请输入月薪', trigger: 'blur' }]
            },

            setsalaryuserVisible: false,
            setsalaryuserLoading: false,
            setsalaryuserFormData: {
                parentId: 0,
                yearMonth: null,
                monthSalary: null,
                monthSalary2: null,
                houseSubsidy: null,
                eatSubsidy: null,
                nightSubsidy: null,
                fullAttendance: null,
            },
            setsalaryuserFormRules: {
                yearMonth: [{ required: true, message: '请输入月份', trigger: 'blur' }],
                monthSalary: [{ required: true, message: '请输入月薪', trigger: 'blur' }]
            },

            dialogVisibleUpload2: false,
            fileList2: [],
            fileparm2: {},
            uploadLoading2: false,
        };
    },
    async mounted() {
        await this.onSearch();
    },
    methods: {
        async getDDUserList(parm, parm2) {
            this.ddUserList = [];
            let options = [];
            let aisleUsers = await getDingtalkUsers({ ddUserId: parm2, userName: parm });
            aisleUsers.forEach(f => {
                options.push({ value: f.ddUserId, label: f.userName, ...f });
            });
            this.ddUserList = options;
        },
        async onDDUserIdChange(value) {
            let find = this.ddUserList.find(f => f.value == value);
            if (find) {
                this.salaryuserFormData.companyName = find.companyName;
                this.salaryuserFormData.title = find.title;
                this.salaryuserFormData.mobile = find.mobile;
                this.salaryuserFormData.userName = find.userName;
            }
            else {
                this.salaryuserFormData.companyName = "";
                this.salaryuserFormData.title = "";
                this.salaryuserFormData.mobile = "";
                this.salaryuserFormData.userName = "";
            }
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            this.sels = [];
            this.selids = [];
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const res = await getWarehouseMonthSalaryUserPageList(params)
            this.listLoading = false;
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            //this.summaryarry = res.data.summary;
            data.forEach(d => {
                d._loading = false;
            })
            this.list = data;
        },
        //排序查 询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            console.log(rows)
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onaddsalaryuser() {
            this.salaryuserFormData = {
                id: 0,
                warehouseCode: null,
                warehouseName: null,
                ddUserId: null,
                companyName: null,
                title: null,
                mobile: null,
                userName: null,
                isManager: false,
                lastSetMonthSalary: 0,
                lastSetMonthSalary2: 0,
                lastSetHouseSubsidy: 0,
                lastSetEatSubsidy: 0,
                lastSetNightSubsidy: 0,
                lastSetFullAttendance: 0,
            };
            this.salaryuserTitle = "新增";
            this.salaryuserVisible = true;
        },
        async onsalaryusersave() {
            this.salaryuserFormData.warehouseName = this.myWarehouseList.find(f => f.value == this.salaryuserFormData.warehouseCode)?.label;
            console.log(this.salaryuserFormData);
            this.salaryusersaveLoading = true;
            const res = await saveWarehouseMonthSalaryUser(this.salaryuserFormData);
            this.salaryusersaveLoading = false;
            if (res?.success) {
                this.$message({ type: 'success', message: '操作成功!' });
                this.salaryuserVisible = false;
                await this.onSearch();
            }
        },
        async ondeletesalaryuser(rowid) {
            this.$confirm('确定要执行删除吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                console.log(rowid);
                const res = await deleteWarehouseMonthSalaryUser({ id: rowid });
                if (res?.success) {
                    this.$message({ type: 'success', message: '删除成功!' });
                    await this.onSearch();
                }
            }).catch(() => {
            });
        },
        //编辑
        async oneditsalaryuser(rowid, warehouseCode) {
            this.salaryuserTitle = "编辑";
            this.ddUserList = [];
            this.salaryuserVisible = true;
            let params = { id: rowid, currentPage: 1, pageSize: 1 };
            this.salaryusersaveLoading = true;
            const res = await getWarehouseMonthSalaryUserPageList(params);
            this.salaryusersaveLoading = false;
            if (res?.success) {
                let data = res.data.list[0];
                this.ddUserList.push({
                    value: data.ddUserId, label: data.userName,
                    companyName: data.companyName, title: data.title,
                    mobile: data.mobile, isManager: data.isManager,
                    userName: data.userName,
                });
                console.log(data);
                this.salaryuserFormData = {
                    id: data.id,
                    warehouseCode: data.warehouseCode,
                    warehouseName: data.warehouseName,
                    ddUserId: data.ddUserId,
                    companyName: data.companyName,
                    title: data.title,
                    mobile: data.mobile,
                    isManager: data.isManager,
                    userName: data.userName,
                    lastSetMonthSalary: data.lastSetMonthSalary,
                    lastSetMonthSalary2: data.lastSetMonthSalary2,
                    lastSetHouseSubsidy: data.lastSetHouseSubsidy,
                    lastSetEatSubsidy: data.lastSetEatSubsidy,
                    lastSetNightSubsidy: data.lastSetNightSubsidy,
                    lastSetFullAttendance: data.lastSetFullAttendance,
                };
            }
        },
        //启用禁用
        async onenabledsalaryuser(rowid, myenabled) {
            this.listLoading = true;
            const res = await enabledWarehouseMonthSalaryUser({ id: rowid, isEnabled: myenabled });
            this.listLoading = false;
            if (res?.success) {
                this.$message({ type: 'success', message: '操作成功!' });
                await this.onSearch();
            }
        },
        //批量设置薪资
        async onbatchmonthsalary() {
            if (this.selids.length <= 0) {
                this.$message({ type: 'warning', message: '请勾选要操作的行!' });
                return;
            }
            this.batchsetsalaryuserVisible = true;
        },
        async onbatchsetsalaryusersave() {
            this.$confirm('确定要批量设置薪资吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                let setList = [];
                this.selids.forEach(f => {
                    setList.push({
                        id: 0, parentId: f,
                        yearMonth: this.batchsetsalaryuserFormData.yearMonth,
                        monthSalary: this.batchsetsalaryuserFormData.monthSalary,
                        monthSalary2: this.batchsetsalaryuserFormData.monthSalary2,
                        houseSubsidy: this.batchsetsalaryuserFormData.houseSubsidy,
                        eatSubsidy: this.batchsetsalaryuserFormData.eatSubsidy,
                        nightSubsidy: this.batchsetsalaryuserFormData.nightSubsidy,
                        fullAttendance: this.batchsetsalaryuserFormData.fullAttendance,
                    })
                });
                console.log(setList);
                const res = await batchWarehouseMonthSalaryUserSet(setList);
                if (res?.success) {
                    this.$message({ type: 'success', message: '批量设置薪资成功!' });
                    this.batchsetsalaryuserVisible = false;
                    await this.onSearch();
                }
            }).catch(() => {
            });
        },
        //设置人员薪资
        async onmonthsalary(row) {
            this.setsalaryuserFormData.parentId = row.id;
            console.log(row);
            this.setsalaryuserVisible = true;
            this.setsalaryuserFormData.yearMonth = row.lastSetYearMonth.toString();
            this.setsalaryuserFormData.monthSalary = row.lastSetMonthSalary;
            this.setsalaryuserFormData.monthSalary2 = row.lastSetMonthSalary2;
            this.setsalaryuserFormData.houseSubsidy = row.lastSetHouseSubsidy;
            this.setsalaryuserFormData.eatSubsidy = row.lastSetEatSubsidy;
            this.setsalaryuserFormData.nightSubsidy = row.lastSetNightSubsidy;
            this.setsalaryuserFormData.fullAttendance = row.lastSetFullAttendance;
        },
        async onsetsalaryusersave() {
            this.$confirm('确定要设置人员薪资吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                let setList = [];
                setList.push({
                    id: 0, parentId: this.setsalaryuserFormData.parentId,
                    yearMonth: this.setsalaryuserFormData.yearMonth,
                    monthSalary: this.setsalaryuserFormData.monthSalary,
                    monthSalary2: this.setsalaryuserFormData.monthSalary2,
                    houseSubsidy: this.setsalaryuserFormData.houseSubsidy,
                    eatSubsidy: this.setsalaryuserFormData.eatSubsidy,
                    nightSubsidy: this.setsalaryuserFormData.nightSubsidy,
                    fullAttendance: this.setsalaryuserFormData.fullAttendance,
                })
                console.log(setList);
                const res = await batchWarehouseMonthSalaryUserSet(setList);
                if (res?.success) {
                    this.$message({ type: 'success', message: '设置人员薪资成功!' });
                    this.setsalaryuserVisible = false;
                    await this.onSearch();
                }
            }).catch(() => {
            });
        },
        async showusermonthsalary(row) {
            this.$showDialogform({
                path: `@/views/profit/warehousewages/warehousemonthsalaryuserdtl.vue`,
                title: '月薪',
                args: { ...row },
                height: '500px',
                width: '970px',
                //callOk: self.onRefresh
            })
        },
        //设置应出勤
        async onmustdays() {
            this.mustdaysVisible = true;
        },
        async onmustdayssave() {
            this.$confirm('确定要设置应出勤吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await mustDayWarehouseMonthSalaryUserSet({
                    yearMonth: this.mustdaysFormData.yearMonth,
                    mustWorkDays: this.mustdaysFormData.mustWorkDays,
                });
                if (res?.success) {
                    this.$message({ type: 'success', message: '设置应出勤成功!' });
                    this.mustdaysVisible = false;
                    await this.onSearch();
                }
            }).catch(() => {
            });
        },
        async onImport2() {
            this.dialogVisibleUpload2 = true;
        },
        async onUploadFile2(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading2 = true
            const form = new FormData();
            form.append("upfile", item.file);
            var res = await importWarehouseMonthSalaryUser(form);
            if (res?.success)
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
            this.uploadLoading2 = false
        },
        onUploadSuccess2(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList2 = [];
            this.dialogVisibleUpload2 = false;
        },
        async onUploadChange2(file, fileList) {
            this.fileList2 = fileList;
        },
        onUploadRemove2(file, fileList) {
            this.fileList2 = [];
        },
        onSubmitUpload2() {
            if (this.fileList2.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload2.submit();
        },
        async callbackDDUserId(val) {
            this.filter.ddUserId = val
        },
        async callbackUserName(val) {
            this.filter.userName = val
        },
    },
};
</script>

<style lang="scss" scoped></style>
