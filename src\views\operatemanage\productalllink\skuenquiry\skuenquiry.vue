<template>
    <my-container v-loading="pageLoading">

        <el-tabs v-model="activeName" style="height: calc(100% - 40px);" @tab-click="handleClick">

            <el-tab-pane label="询价报价" name="tab1" style="height: 100%;">
                <skuenquirylist ref="skuenquirylist" />

            </el-tab-pane>
            <el-tab-pane label="询价报价(管理)" name="tab5" style="height: 100%;"
                v-if="checkPermission('api:operatemanage:alllink:PageChooseEnquirySummaryAsync')">
                <SkuEnquirySummaryList ref="SkuEnquirySummaryList" />

            </el-tab-pane>
            <el-tab-pane label="新品采样" name="tab2" style="height: 100%;">
                <hotsalegoodsskuorderchooselist ref="hotsalegoodsskuorderchooselist" />
            </el-tab-pane>

            <el-tab-pane label="建编码" name="tab3" style="height: 100%;">
                <hotsalegoodsbuildgoodscaigou ref="hotsalegoodsbuildgoodscaigou" />
            </el-tab-pane>

            <el-tab-pane label="新品初核" name="tab10" style="height: 100%;">
                <newGoodsAudit ref="newGoodsAudit" />
            </el-tab-pane>

            <el-tab-pane label="新品进货" name="tab4" style="height: 100%;">
                <HotPurchasePlanList ref="HotPurchasePlanList" />
            </el-tab-pane>
            <el-tab-pane label="公司商品" v-if="checkPermission('pageGetCompanyGoods')" name="tab11" style="height: 100%;"
                :lazy="true">
                <companyGoods ref="companyGoods" />
            </el-tab-pane>

            <el-tab-pane label="编码进货" name="tab7" style="height: 100%;" :lazy="true">
                <purchasegoods ref="purchasegoods" />
            </el-tab-pane>

            <el-tab-pane label="询价报价报表" name="tab6" style="height: 100%;">
                <EnquiryFbSummaryCharts ref="EnquiryFbSummaryCharts" />
            </el-tab-pane>

            <el-tab-pane label="编码处理" name="tab8" style="height: 100%;">
                <encodingProcessing ref="EnquiryFbSummaryCharts" />
            </el-tab-pane>

            <el-tab-pane label="采购退货出库" name="tab9" style="height: 100%;">
                <encodingProcessingBack ref="EnquiryFbSummaryCharts" />
            </el-tab-pane>

            <el-tab-pane label="新品采购单审批" name="tab12" style="height: 100%;" lazy>
                <PurchaseOrderNewApprove ref="PurchaseOrderNewApprove" />
            </el-tab-pane>
        </el-tabs>

    </my-container>
</template>
<script>

import { rulePlatform } from "@/utils/formruletools";
import MyContainer from "@/components/my-container";
import skuenquirylist from "@/views/operatemanage/productalllink/skuenquiry/skuenquirylist.vue";
import SkuEnquirySummaryList from "@/views/operatemanage/productalllink/skuenquiry/SkuEnquirySummaryList.vue";
import hotsalegoodsskuorderchooselist from "@/views/operatemanage/productalllink/skuenquiry/hotsalegoodsskuorderchooselist.vue";
import hotsalegoodsbuildgoodscaigou from "@/views/operatemanage/productalllink/skuenquiry/hotsalegoodsbuildgoodscaigou.vue";
import HotPurchasePlanList from "@/views/operatemanage/productalllink/skuenquiry/HotPurchasePlanList.vue";
import EnquiryFbSummaryCharts from "@/views/operatemanage/productalllink/skuenquiry/EnquiryFbSummaryCharts.vue";
import PurchaseOrderNewApprove from "@/views/operatemanage/productalllink/skuenquiry/PurchaseOrderNewApprove.vue";
import purchasegoods from "../goodscodestock/purchasegoods.vue";
import encodingProcessing from "./encodingProcessing.vue";
import encodingProcessingBack from "./encodingProcessingBack.vue";
import newGoodsAudit from "./newGoodsAudit.vue";
import companyGoods from "./companyGoods.vue";

export default {
    name: "hotsalegoods",
    components: {
        MyContainer, encodingProcessing, skuenquirylist, SkuEnquirySummaryList,
        hotsalegoodsskuorderchooselist, hotsalegoodsbuildgoodscaigou, HotPurchasePlanList,
        EnquiryFbSummaryCharts, purchasegoods, encodingProcessingBack, newGoodsAudit, companyGoods,PurchaseOrderNewApprove
    },
    data() {
        return {
            filter: {
                yearMonth: '',
                groupName: '',
                shopName: '',
                Platform: [],
                PlatformName: null,
                startTime: '',
                endTime: ''
            },
            activeName: 'tab1',
            that: this,
            pageLoading: false,
            importNumber: ''
        };
    },
    methods: {
        handleClick(tab, event) {
            if (tab && tab.$options && tab.$options.propsData.name == "tab6") {
                this.$nextTick(() => {
                    this.$refs["EnquiryFbSummaryCharts"].resize();
                });
            }
        }
    }

}

</script>