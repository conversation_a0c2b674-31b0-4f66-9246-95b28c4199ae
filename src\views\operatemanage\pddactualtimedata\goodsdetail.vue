<template>
  <container>
    <template #header>
      <div style="display:flex;flex-direction: column;">
        <el-button-group>
          <el-button style="margin-top:0px">
            <el-date-picker style="width: 410px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" :clearable="false" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" :picker-options="pickerOptions"></el-date-picker>
            <el-select filterable v-model="filter.shopCode" placeholder="店铺" clearable
              style="width: 130px;margin-left: 5px;">
              <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
            </el-select>
            <el-input style="margin-left: 5px;width: 150px;" v-model="filter.productName"
              v-model.trim="filter.productName" :maxlength=50 placeholder="商品名称" @keyup.enter.native="onSearch"
              clearable />
            <el-input style="margin-left: 5px;width: 150px;" v-model="filter.productCode"
              v-model.trim="filter.productCode" :maxlength=50 placeholder="商品ID" @keyup.enter.native="onSearch"
              clearable />
            <!-- <el-input style="margin-left: 5px;width: 150px;" v-model="filter.productName"
              v-model.trim="filter.productName" :maxlength=50 placeholder="商品名称" @keyup.enter.native="onSearch"
              clearable /> -->
            <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组"
              style="width: 90px;margin-left: 5px;">
              <el-option key="无运营组" label="无运营组" :value="0"></el-option>
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-select filterable v-model="filter.operateSpecialUserId" collapse-tags clearable placeholder="运营专员"
              style="width: 90px;margin-left: 5px;">
              <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-select filterable v-model="filter.userId" collapse-tags clearable placeholder="运营助理"
              style="width: 90px;margin-left: 5px;">
              <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-select filterable v-model="filter.userId3" collapse-tags clearable placeholder="备用负责人"
              style="width: 90px;margin-left: 5px;">
              <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-button type="primary" @click="onSearch()" style="margin-left: 10px;">查询</el-button>
          </el-button>
        </el-button-group>
        <el-button-group style="margin:3px 0 5px 0">
          <el-radio-group v-model="filter.searchType" size="small" style="margin-left:5px;" @change="onSearch();">
            <el-radio-button label="0">商品数据</el-radio-button>
            <el-radio-button label="1">店铺数据</el-radio-button>
            <el-radio-button label="2">运营助理</el-radio-button>
            <el-radio-button label="3">运营专员</el-radio-button>
            <el-radio-button label="4">运营组</el-radio-button>
          </el-radio-group>
        </el-button-group>
      </div>
    </template>
    <!-- <div style="height:708px;margin-top: 5px;"> -->
    <ces-table :id="'pddactualtimedata_goodsdetail202408041657_1'" :tablekey="'PddActualTimePro202304011421'" v-show="filter.searchType == '0'" ref="tablePro"
      :showsummary='true' :summaryarry='proSummaryarry' :that='that' :isIndex='true' :tablefixed='true' :hasexpand='false'
      :isSelectColumn="true" :tableData='proTableList' :tableCols='proTableCols' :tableHandles='tableHandles'
      @sortchange="sortchange" :loading="listLoading"   :border="true" @summaryClick='onsummaryClick'>
    </ces-table>
    <ces-table :id="'pddactualtimedata_goodsdetail202408041657_2'" :tablekey="'PddActualTimeShop202304011421'" v-show="filter.searchType == '1'" ref="tableShop"
      :showsummary='true' :summaryarry='shopSummaryarry' :that='that' :isIndex='true' :tablefixed='true'
      :hasexpand='false' :isSelectColumn="true" :tableData='shopTableList' :tableCols='shopTableCols'
      :tableHandles='tableHandles' @sortchange="sortchange" :loading="listLoading"   :border="true"
      @summaryClick='onsummaryClick'>
    </ces-table>
    <ces-table :id="'pddactualtimedata_goodsdetail202408041657_3'" :tablekey="'PddActualTimeUser202305171421'" v-show="filter.searchType == '2'" ref="tableUser"
      :showsummary='true' :summaryarry='userSummaryarry' :that='that' :isIndex='true' :tablefixed='true'
      :hasexpand='false' :isSelectColumn="true" :tableData='userTableList' :tableCols='userTableCols'
      :tableHandles='tableHandles' @sortchange="sortchange" :loading="listLoading"   :border="true"
      @summaryClick='onsummaryClick'>
    </ces-table>
    <ces-table :id="'pddactualtimedata_goodsdetail202408041657_4'" :tablekey="'PddActualTimeSpecialUser202305171421'" v-show="filter.searchType == '3'" ref="tableSpecialUser"
      :showsummary='true' :summaryarry='specialUserSummaryarry' :that='that' :isIndex='true' :tablefixed='true'
      :hasexpand='false' :isSelectColumn="true" :tableData='specialUserTableList' :tableCols='specialUserTableCols'
      :tableHandles='tableHandles' @sortchange="sortchange" :loading="listLoading"   :border="true"
      @summaryClick='onsummaryClick'>
    </ces-table>
    <ces-table :id="'pddactualtimedata_goodsdetail202408041657_5'" :tablekey="'PddActualTimeGroup202305171421'" v-show="filter.searchType == '4'" ref="tableGroup"
      :showsummary='true' :summaryarry='groupSummaryarry' :that='that' :isIndex='true' :tablefixed='true'
      :hasexpand='false' :isSelectColumn="true" :tableData='groupTableList' :tableCols='groupTableCols'
      :tableHandles='tableHandles' @sortchange="sortchange" :loading="listLoading"   :border="true"
      @summaryClick='onsummaryClick'>
    </ces-table>
    <!-- 行内趋势图 -->
    <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
      <span>
        <buschar v-if="buscharDialog.visible" ref="buschar" :analysisData="buscharDialog.data"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 汇总趋势图 -->
    <el-dialog :title="summaryDialog.title" :visible.sync="summaryDialog.visible" width="80%" height="700px" v-dialogDrag>
      <span>
        <template>
          <el-date-picker style="width: 410px" v-model="summaryDialog.filter.timerange" type="daterange"
            @change="summaryDateChange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            :picker-options="pickerOptions"></el-date-picker>
          <el-select :multiple="true" filterable @change="changeBusChatSelect" v-model="summaryDialog.data.selectedLegend"
            placeholder="请选择趋势图列" collapse-tags clearable style="width:300px;margin-left: 10px;">
            <el-option v-for="item in summaryDialog.data.legend" :label="item" :value="item" />
          </el-select>
        </template>
      </span>
      <span>
        <buschar v-if="summaryDialog.visible" ref="summaryBuschar" :legendChanges="legendChanges"
          :analysisData="summaryDialog.data"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="summaryDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>

    <template #footer>
      <my-pagination v-show="filter.searchType == 0" ref="pager" :total="total" :checked-count="sels.length"
        @get-page="getList" />

      <my-pagination v-show="filter.searchType == 1" ref="shopPager" :total="total" :checked-count="sels.length"
        @get-page="getGroupByDataList" />

      <my-pagination v-show="filter.searchType == 2" ref="userPager" :total="total" :checked-count="sels.length"
        @get-page="getGroupByDataList" />

      <my-pagination v-show="filter.searchType == 3" ref="specialUserPager" :total="total" :checked-count="sels.length"
        @get-page="getGroupByDataList" />

      <my-pagination v-show="filter.searchType == 4" ref="groupPager" :total="total" :checked-count="sels.length"
        @get-page="getGroupByDataList" />
    </template>
  </container>
</template>
<script>
import {
  getPddActualTimeDataByProAsync, getPddActualTimeDataGroupByAsync, getPddActualTimeAnalysisGroupByAsync,
  getPddActualTimeAnalysisProAsync, getPddActualTimeAnalysisBySummaryAsync
} from '@/api/operatemanage/pddactualtimedata'
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import { getDirectorGroupList, getDirectorList } from '@/api/operatemanage/base/shop'
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import { Loading } from 'element-ui';
import buschar from '@/components/Bus/buschar'
let loading;
const startLoading = () => {
  loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
  });
};
let tableColsPro = [
  { istrue: true, fixed: 'left', prop: 'yearMonthDay', sortable: 'custom', label: '年月日', width: '60' },
  { istrue: true, fixed: 'left', prop: 'shopCode', sortable: 'custom', label: '店铺名称', width: '150', formatter: (row) => row.shopName },
  { istrue: true, fixed: 'left', prop: 'groupId', sortable: 'custom', label: '小组', width: '50', formatter: (row) => row.groupName },
  { istrue: true, fixed: 'left', prop: 'operateSpecialUserId', sortable: 'custom', width: '50', label: '运营专员', formatter: (row) => row.operateSpecialUserName },
  { istrue: true, fixed: 'left', prop: 'userId', sortable: 'custom', label: '运营助理', width: '50', formatter: (row) => row.userName },
  { istrue: true, fixed: 'left', prop: 'userId3', sortable: 'custom', label: '备用负责人', width: '50', formatter: (row) => row.userName3 },
  { istrue: true, fixed: 'left', prop: 'ccc', type: 'click', label: '趋势图', width: '50', align: 'center', formatter: (row) => '趋势图', handle: (that, row) => that.showSimilarity(row) },
  { istrue: true, fixed: 'left', prop: 'proCode', sortable: 'custom', label: '商品Id', width: '85' },
  { istrue: true, fixed: 'left', prop: 'proCodeName', label: '商品名称', width: '80' },
];

let tableColsShop = [
  { istrue: true, fixed: 'left', prop: 'yearMonthDay', sortable: 'custom', label: '年月日', width: '60' },
  { istrue: true, fixed: 'left', prop: 'shopCode', sortable: 'custom', label: '店铺名称', width: '150', formatter: (row) => row.shopName },
  { istrue: true, fixed: 'left', prop: 'groupId', sortable: 'custom', label: '小组', width: '50', formatter: (row) => row.groupName },
  { istrue: true, fixed: 'left', prop: 'ccc', type: 'click', label: '趋势图', width: '50', align: 'center', formatter: (row) => '趋势图', handle: (that, row) => that.showSimilarity(row) },

];

let tableColsUser = [
  { istrue: true, fixed: 'left', prop: 'yearMonthDay', sortable: 'custom', label: '年月日', width: '60' },
  { istrue: true, fixed: 'left', prop: 'userId', sortable: 'custom', label: '运营助理', width: '50', formatter: (row) => row.userName },
  { istrue: true, fixed: 'left', prop: 'groupId', sortable: 'custom', label: '小组', width: '50', formatter: (row) => row.groupName },
  { istrue: true, fixed: 'left', prop: 'ccc', type: 'click', label: '趋势图', width: '50', align: 'center', formatter: (row) => '趋势图', handle: (that, row) => that.showSimilarity(row) },
];

let tableColsSpecialUser = [
  { istrue: true, fixed: 'left', prop: 'yearMonthDay', sortable: 'custom', label: '年月日', width: '60' },
  { istrue: true, fixed: 'left', prop: 'operateSpecialUserId', sortable: 'custom', label: '运营专员', width: '50', formatter: (row) => row.operateSpecialUserName },
  { istrue: true, fixed: 'left', prop: 'groupId', sortable: 'custom', label: '小组', width: '50', formatter: (row) => row.groupName },
  { istrue: true, fixed: 'left', prop: 'ccc', type: 'click', label: '趋势图', width: '50', align: 'center', formatter: (row) => '趋势图', handle: (that, row) => that.showSimilarity(row) },
];

let tableColsGroup = [
  { istrue: true, fixed: 'left', prop: 'yearMonthDay', sortable: 'custom', label: '年月日', width: '60' },
  { istrue: true, fixed: 'left', prop: 'groupId', sortable: 'custom', label: '小组', width: '50', formatter: (row) => row.groupName },
  { istrue: true, fixed: 'left', prop: 'ccc', type: 'click', label: '趋势图', width: '50', align: 'center', formatter: (row) => '趋势图', handle: (that, row) => that.showSimilarity(row) },
];

let tableAmountCols = [
  { istrue: true, summaryEvent: true, prop: 'shopVisitorNumber', sortable: 'custom', label: '总访客数', width: '70' },
  { istrue: true, summaryEvent: true, prop: 'allPayAmount', sortable: 'custom', label: '总支付金额', width: '80' },
  { istrue: true, summaryEvent: true, prop: 'allPayOrderCount', sortable: 'custom', label: '总支付订单数', width: '80' },
  { istrue: true, summaryEvent: true, prop: 'allTrunOutRate', sortable: 'custom', label: '转化率', width: '60', formatter: (row) => !row.allTrunOutRate ? "0%" : (row.allTrunOutRate * 100).toFixed(2) + '%' },
  { istrue: true, summaryEvent: true, prop: 'allSpendAmount', sortable: 'custom', label: '总花费', width: '60' },
  { istrue: true, summaryEvent: true, prop: 'allExtendTradeAmount', sortable: 'custom', label: '推广交易额', width: '80' },
  { istrue: true, summaryEvent: true, prop: 'allPayRate', sortable: 'custom', label: '付费占比', width: '70', formatter: (row) => !row.allPayRate ? "0%" : (row.allPayRate * 100).toFixed(2) + '%' },
  { istrue: true, summaryEvent: true, prop: 'allInvestmentRate', sortable: 'custom', label: '投产', width: '60', formatter: (row) => !row.allInvestmentRate ? "0" : (row.allInvestmentRate).toFixed(2) },
  {
    istrue: true, rop: '', label: `全站推广`, merge: true, prop: 'mergeField',
    cols: [
      { istrue: true, summaryEvent: true, prop: 'allStationExtendSpendAmount', label: '花费', sortable: 'custom', width: '90' },
      { istrue: true, summaryEvent: true, prop: 'allStationExtendTradeAmount', label: '交易额', sortable: 'custom', width: '80' },
      { istrue: true, summaryEvent: true, prop: 'allStationExtendInvestmentRate', label: '投产', sortable: 'custom', width: '60', formatter: (row) => !row.allStationExtendInvestmentRate ? "0" : (row.allStationExtendInvestmentRate).toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'allStationExtendOrderCount', label: '成交笔数', sortable: 'custom', width: '80' },
      { istrue: true, summaryEvent: true, prop: 'allStationExtendExposureCount', label: '曝光量', sortable: 'custom', width: '80' },
      { istrue: true, summaryEvent: true, prop: 'allStationExtendClickCount', label: '点击量', sortable: 'custom', width: '70' },
      { istrue: true, summaryEvent: true, prop: 'allStationExtendClickRate', label: '点击率', sortable: 'custom', width: '60', formatter: (row) => !row.allStationExtendClickRate ? "0%" : (row.allStationExtendClickRate * 100).toFixed(2) + '%' },
      { istrue: true, summaryEvent: true, prop: 'allStationExtendTrunOutRate', label: '转化率', sortable: 'custom', width: '60', formatter: (row) => !row.allStationExtendTrunOutRate ? "0%" : (row.allStationExtendTrunOutRate * 100).toFixed(2) + '%' }
    ]
  },
  {
    istrue: true, rop: '', label: `多多搜索`, merge: true, prop: 'mergeField1',
    cols: [
      { istrue: true, summaryEvent: true, prop: 'duoDuoSearchSpendAmount', label: '花费', sortable: 'custom', width: '90', },
      { istrue: true, summaryEvent: true, prop: 'duoDuoSearchTradeAmount', label: '交易额', sortable: 'custom', width: '80', },
      { istrue: true, summaryEvent: true, prop: 'duoDuoSearchInvestmentRate', label: '投产', sortable: 'custom', width: '60', formatter: (row) => !row.duoDuoSearchInvestmentRate ? "0" : (row.duoDuoSearchInvestmentRate).toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'duoDuoSearchOrderCount', label: '成交笔数', sortable: 'custom', width: '80', },
      { istrue: true, summaryEvent: true, prop: 'duoDuoSearchExposureCount', label: '曝光量', sortable: 'custom', width: '80', },
      { istrue: true, summaryEvent: true, prop: 'duoDuoSearchClickCount', label: '点击量', sortable: 'custom', width: '70' },
      { istrue: true, summaryEvent: true, prop: 'duoDuoSearchClickRate', label: '点击率', sortable: 'custom', width: '60', formatter: (row) => !row.duoDuoSearchClickRate ? "0%" : (row.duoDuoSearchClickRate * 100).toFixed(2) + '%' },
      { istrue: true, summaryEvent: true, prop: 'duoDuoSearchTrunOutRate', label: '转化率', sortable: 'custom', width: '60', formatter: (row) => !row.duoDuoSearchTrunOutRate ? "0%" : (row.duoDuoSearchTrunOutRate * 100).toFixed(2) + '%' }
    ]
  },
  {
    istrue: true, rop: '', label: `多多场景`, merge: true, prop: 'mergeField2',
    cols: [
      { istrue: true, summaryEvent: true, prop: 'duoDuoSceneSpendAmount', label: '花费', sortable: 'custom', width: '90', },
      { istrue: true, summaryEvent: true, prop: 'duoDuoSceneTradeAmount', label: '交易额', sortable: 'custom', width: '80', },
      { istrue: true, summaryEvent: true, prop: 'duoDuoSceneInvestmentRate', label: '投产', sortable: 'custom', width: '60', formatter: (row) => !row.duoDuoSceneInvestmentRate ? "0" : (row.duoDuoSceneInvestmentRate).toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'duoDuoSceneOrderCount', label: '成交笔数', sortable: 'custom', width: '80', },
      { istrue: true, summaryEvent: true, prop: 'duoDuoSceneExposureCount', label: '曝光量', sortable: 'custom', width: '80', },
      { istrue: true, summaryEvent: true, prop: 'duoDuoSceneClickCount', label: '点击量', sortable: 'custom', width: '70' },
      { istrue: true, summaryEvent: true, prop: 'duoDuoSceneClickRate', label: '点击率', sortable: 'custom', width: '60', formatter: (row) => !row.duoDuoSceneClickRate ? "0%" : (row.duoDuoSceneClickRate * 100).toFixed(2) + '%' },
      { istrue: true, summaryEvent: true, prop: 'duoDuoSceneTrunOutRate', label: '转化率', sortable: 'custom', width: '60', formatter: (row) => !row.duoDuoSceneTrunOutRate ? "0%" : (row.duoDuoSceneTrunOutRate * 100).toFixed(2) + '%' }
    ]
  },
  {
    istrue: true, rop: '', label: `直播推广`, merge: true, prop: 'mergeField3',
    cols: [
      { istrue: true, summaryEvent: true, prop: 'liveExtendSpendAmount', label: '花费', sortable: 'custom', width: '90', },
      { istrue: true, summaryEvent: true, prop: 'liveExtendTradeAmount', label: '交易额', sortable: 'custom', width: '80', },
      { istrue: true, summaryEvent: true, prop: 'liveExtendInvestmentRate', label: '投产', sortable: 'custom', width: '60', formatter: (row) => !row.liveExtendInvestmentRate ? "0" : (row.liveExtendInvestmentRate).toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'liveExtendOrderCount', label: '成交笔数', sortable: 'custom', width: '80', },
    ]
  },
  {
    istrue: true, rop: '', label: `免费流量`, merge: true, prop: 'mergeField4',
    cols: [
      { istrue: true, summaryEvent: true, prop: 'freeVisitorNumber', label: '访客数', sortable: 'custom', width: '90', },
      { istrue: true, summaryEvent: true, prop: 'freePayAmount', label: '支付金额', sortable: 'custom', width: '80', },
      { istrue: true, summaryEvent: true, prop: 'freePayOrderCount', label: '支付订单数', sortable: 'custom', width: '80', },
      { istrue: true, summaryEvent: true, prop: 'freeTrunOutRate', label: '转化率', sortable: 'custom', width: '60', formatter: (row) => !row.freeTrunOutRate ? "0%" : (row.freeTrunOutRate * 100).toFixed(2) + '%' }
    ]
  }
]

tableColsPro = tableColsPro.concat(tableAmountCols);
tableColsShop = tableColsShop.concat(tableAmountCols);
tableColsUser = tableColsUser.concat(tableAmountCols);
tableColsSpecialUser = tableColsSpecialUser.concat(tableAmountCols);
tableColsGroup = tableColsGroup.concat(tableAmountCols);

const tableHandles = [
];

export default {
  name: "PddActualTimeDataGoodsDetail",
  components: { container, cesTable, buschar },
  data() {
    return {
      that: this,
      filter: {
        searchType: 0,
        productName: null,
        productCode: null,
        shopCode: null,
        startTime: null,
        endTime: null,
        timerange: null,
        //组
        groupId: null,
        // 运营助理
        userId: null,
        // 备用
        userId3: null,
        // 运营专员 ID
        operateSpecialUserId: null
      },
      shopList: [],
      proTableCols: tableColsPro,
      shopTableCols: tableColsShop,
      userTableCols: tableColsUser,
      specialUserTableCols: tableColsSpecialUser,
      groupTableCols: tableColsGroup,
      tableHandles: tableHandles,
      total: 0,
      proPager: { OrderBy: null, IsAsc: false },
      shopPager: { OrderBy: null, IsAsc: false },
      userPager: { OrderBy: null, IsAsc: false },
      specialUserPager: { OrderBy: null, IsAsc: false },
      groupPager: { OrderBy: null, IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      proTableList: [],
      shopTableList: [],
      userTableList: [],
      specialUserTableList: [],
      groupTableList: [],
      proSummaryarry: {},
      shopSummaryarry: {},
      userSummaryarry: {},
      specialUserSummaryarry: {},
      groupSummaryarry: {},
      selids: [],
      grouplist: [],
      directorlist: [],
      pickerOptions: {
        shortcuts: [{
          text: '前一天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
            window.setshowprogress(false);
          }
        }, {
          text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
            window.setshowprogress(false);
          }
        }, {
          text: '近一个月',
          onClick(picker) {
            const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate() - 1);
            const date2 = new Date(); date2.setDate(date2.getDate() - 1);
            picker.$emit('pick', [date1, date2]);
            window.setshowprogress(false);
          }
        }]
      },
      buscharDialog: {
        title: "趋势图",
        visible: false,
        data: []
      },
      summaryDialog: {
        title: "趋势图",
        visible: false,
        data: [],
        filter: {
          startTime: null,
          endTime: null,
          timerange: null,
          column: null
        }
      },
    };
  },
  async mounted() {
    let end = new Date();
    let start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
    end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
    this.filter.timerange = [start, end];
    let res1 = await getshopList({ platform: 2, CurrentPage: 1, PageSize: 100000 });
    this.filter.shopCode = null
    this.shopList = res1.data.list
    let res2 = await getDirectorGroupList();
    this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

    let res3 = await getDirectorList();
    this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });
    this.onSearch();
  },
  async created() {

  },
  methods: {
    async sortchange(column) {
      if (this.filter.searchType == 0) {
        if (!column.order) this.proPager = {};
        else
          this.proPager = {
            OrderBy: column.prop,
            IsAsc: column.order.indexOf("descending") == -1 ? true : false,
          };
      } else if (this.filter.searchType == 1){
        if (!column.order) this.shopPager = {};
        else
          this.shopPager = {
            OrderBy: column.prop,
            IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      } else if (this.filter.searchType == 2){
        if (!column.order) this.userPager = {};
        else
          this.userPager = {
            OrderBy: column.prop,
            IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      } else if (this.filter.searchType == 3){
        if (!column.order) this.specialUserPager = {};
        else
          this.specialUserPager = {
            OrderBy: column.prop,
            IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      } else if (this.filter.searchType == 4){
        if (!column.order) this.groupPager = {};
        else
          this.groupPager = {
            OrderBy: column.prop,
            IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      }
      await this.onSearch();
    },
    async onSearch() {
      if (this.filter.searchType == 0) {
        this.$refs.pager.setPage(1);
        this.getList();
      } else {
        this.$refs.pager.setPage(1);
        this.getGroupByDataList();
      }
    },
    async getList() {
      let pager = this.$refs.pager.getPager()
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      this.listLoading = true;
      let params = { ...pager, ...this.proPager, ... this.filter }
      let res = await getPddActualTimeDataByProAsync(params);
      this.listLoading = false;
      this.proTableList = res.data?.list;
      this.total = res.data?.total;
      this.proSummaryarry = res.data?.summary;
    },
    async getGroupByDataList() {
      let pager = {};
      if (this.filter.searchType == 1) {
        pager = this.$refs.shopPager.getPager();
      } else if (this.filter.searchType == 2) {
        pager = this.$refs.userPager.getPager();
      } else if (this.filter.searchType == 3) {
        pager = this.$refs.specialUserPager.getPager();
      } else {
        pager = this.$refs.groupPager.getPager();
      }
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      this.listLoading = true;
      let params = { };
      if (this.filter.searchType == 1) {
        params={...pager, ...this.shopPager, ... this.filter,groupBy : "Shop"};
      } else if (this.filter.searchType == 2) {
        params={...pager, ...this.userPager, ... this.filter,groupBy : "User"};
      } else if (this.filter.searchType == 3) {
        params={...pager, ...this.specialUserPager, ... this.filter,groupBy : "SpecialUser"};
      } else {
        params={...pager, ...this.groupPager, ... this.filter,groupBy : "Group"};
      }

      let res = await getPddActualTimeDataGroupByAsync(params);
      this.listLoading = false;

      if (this.filter.searchType == 1) {
        this.shopTableList = res.data?.list;
        this.shopSummaryarry = res.data?.summary;
      } else if (this.filter.searchType == 2) {
        this.userTableList = res.data?.list;
        this.userSummaryarry = res.data?.summary;
      } else if (this.filter.searchType == 3) {
        this.specialUserTableList = res.data?.list;
        this.specialUserSummaryarry = res.data?.summary;
      } else {
        this.groupTableList = res.data?.list;
        this.groupSummaryarry = res.data?.summary;
      }

      this.total = res.data?.total;
    },
    async showSimilarity(row) {
      let loadingInstance = Loading.service();
      Loading.service({ fullscreen: true });
      let that = this;
      if (this.filter.searchType == 0) {
        let params = { productCode: row.proCode, yearMonthDay: row.yearMonthDay };
        await getPddActualTimeAnalysisProAsync(params).then(res => {
          that.buscharDialog.visible = true;
          that.buscharDialog.data = res.data;
        });
      } else {
        let params = {...this.filter,  yearMonthDay: row.yearMonthDay };
        if (this.filter.searchType == 1) {
          params.groupBy = "Shop";
          params.shopCode=row.shopCode;
        } else if (this.filter.searchType == 2) {
          params.groupBy = "User";
          params.userId=row.userId;
        } else if (this.filter.searchType == 3) {
          params.groupBy = "SpecialUser";
          params.operateSpecialUserId=row.operateSpecialUserId;
        } else {
          params.groupBy = "Group";
          params.groupId=row.groupId;
        }
        await getPddActualTimeAnalysisGroupByAsync(params).then(res => {
          that.buscharDialog.visible = true;
          that.buscharDialog.data = res.data;
        });
      }
      await this.$refs.buschar.initcharts()
      loadingInstance.close();
    },
    async onsummaryClick(property) {
      // this.$message({message:property,type:"warning"});
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.summaryDialog.filter.startTime = this.filter.timerange[0];
        this.summaryDialog.filter.endTime = this.filter.timerange[1];
      }
      let pager = this.$refs.pager.getPager();
      this.summaryDialog.filter.column = property;
      let params = { ...pager, ...this.pager, ...this.filter, ...this.summaryDialog.filter };
      let that = this;
      let res = await getPddActualTimeAnalysisBySummaryAsync(params).then(res => {
        that.summaryDialog.visible = true;
        that.summaryDialog.data = res.data
        that.summaryDialog.filter.timerange = that.filter.timerange;
      });
    },
    async summaryDateChange() {
      this.summaryDialog.filter.startTime = null;
      this.summaryDialog.filter.endTime = null;
      if (this.summaryDialog.filter.timerange) {
        this.summaryDialog.filter.startTime = this.summaryDialog.filter.timerange[0];
        this.summaryDialog.filter.endTime = this.summaryDialog.filter.timerange[1];
      }
      let pager = this.$refs.pager.getPager();
      let params = { ...pager, ...this.pager, ...this.filter, ...this.summaryDialog.filter };
      let that = this;
      let list = this.summaryDialog.data.selectedLegend;
      let res = await getPddActualTimeAnalysisBySummaryAsync(params).then(res => {
        that.summaryDialog.data = res.data
        that.summaryDialog.data.selectedLegend = list
      });
      await this.$refs.summaryBuschar.initcharts()
    },
    async changeBusChatSelect() {
      await this.$refs.summaryBuschar.initcharts()
    },
    legendChanges(params) {
      let list = [];
      if (params != null) {
        for (let key in params) {
          if (params[key]) {
            list.push(key)
          }
        }
      }
      this.summaryDialog.data.selectedLegend = list;
    }
  }
}


</script>
<style></style>
