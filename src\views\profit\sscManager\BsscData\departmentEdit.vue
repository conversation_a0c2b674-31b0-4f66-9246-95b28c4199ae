<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="140px" class="demo-ruleForm">
        <el-form-item label="区域：">
          <el-input v-model.trim="ruleForm.regionName" placeholder="区域" maxlength="20" clearable class="publicCss" />
        </el-form-item>
        <el-form-item label="部门类型：">
          <el-input v-model.trim="ruleForm.deptName" placeholder="部门类型" maxlength="20" clearable class="publicCss" />
        </el-form-item>
        <el-form-item label="部门：">
          <el-input v-model.trim="ruleForm.dept" placeholder="部门" maxlength="20" clearable class="publicCss" />
        </el-form-item>
        <el-form-item label="在职人数：" prop="onpostPersonCount">
          <inputNumberYh v-model="ruleForm.onpostPersonCount" :placeholder="'在职人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="转正人数：" prop="regularPersonCount">
          <inputNumberYh v-model="ruleForm.regularPersonCount" :placeholder="'转正人数'" class="publicCss" @blur="calculateChangesPersonCount" />
        </el-form-item>
        <el-form-item label="晋升人数：" prop="promotionPersonCount">
          <inputNumberYh v-model="ruleForm.promotionPersonCount" :placeholder="'晋升人数'" class="publicCss" @blur="calculateChangesPersonCount" />
        </el-form-item>
        <el-form-item label="调薪人数：" prop="salarychangePersonCount">
          <inputNumberYh v-model="ruleForm.salarychangePersonCount" :placeholder="'调薪人数：'" class="publicCss" @blur="calculateChangesPersonCount" />
        </el-form-item>
        <el-form-item label="调岗人数：" prop="postchangePersonCount">
          <inputNumberYh v-model="ruleForm.postchangePersonCount" :placeholder="'调岗人数：'" class="publicCss" @blur="calculateChangesPersonCount" />
        </el-form-item>
        <el-form-item label="异动合计" prop="changesPersonCount">
          <!-- <inputNumberYh v-model="ruleForm.changesPersonCount" :placeholder="'异动合计'" class="publicCss" /> -->
           {{ ruleForm.changesPersonCount }}
        </el-form-item>
        <el-form-item label="参保人数：" prop="insuredPersonCount">
          <inputNumberYh v-model="ruleForm.insuredPersonCount" :placeholder="'参保人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="企业社保费用：" prop="socialSecurityCost">
          <inputNumberYh v-model="ruleForm.socialSecurityCost" :placeholder="'企业社保费用'" :fixed="2" clearable class="publicCss" />
        </el-form-item>
        <el-form-item label="企业公积金费用：" prop="socialProvidentCost">
          <inputNumberYh v-model="ruleForm.socialProvidentCost" :placeholder="'企业公积金费用'" :fixed="2" clearable class="publicCss" />
        </el-form-item>
        <!-- <el-form-item label="经理人数：" prop="managerCount">
          <inputNumberYh v-model="ruleForm.managerCount" :placeholder="'经理人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="总监人数：" prop="directorCount">
          <inputNumberYh v-model="ruleForm.directorCount" :placeholder="'总监人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="总经理/区负责人：" prop="generalManager">
          <inputNumberYh v-model="ruleForm.generalManager" :placeholder="'总经理/区负责人'" class="publicCss" />
        </el-form-item>
        <el-form-item label="总裁：" prop="ceoCount">
          <inputNumberYh v-model="ruleForm.ceoCount" :placeholder="'总裁'" class="publicCss" />
        </el-form-item> -->

      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { sscDataSubmit } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission'
import decimal from '@/utils/decimal'
export default {
  name: 'departmentEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      selectProfitrates: [],
      ruleForm: {
        label: '',
        name: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入活动名称', trigger: 'blur' }
        ],
        label: [
          { required: true, message: '请输入活动名称', trigger: 'blur' }
        ]
      }
    }
  },

  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };

    // 初始化时计算异动合计
    this.calculateChangesPersonCount();
  },
  methods: {
    // 计算异动合计
    calculateChangesPersonCount() {
      const regularPersonCount = this.ruleForm.regularPersonCount || 0;
      const promotionPersonCount = this.ruleForm.promotionPersonCount || 0;
      const salarychangePersonCount = this.ruleForm.salarychangePersonCount || 0;
      const postchangePersonCount = this.ruleForm.postchangePersonCount || 0;

      // 使用 decimal 函数计算和，保留0位小数
      this.ruleForm.changesPersonCount = decimal(
        decimal(regularPersonCount, promotionPersonCount, 0, '+'),
        decimal(salarychangePersonCount, postchangePersonCount, 0, '+'),
        0, '+'
      );
    },

    cancellationMethod() {
      this.$emit('cancellationMethod');
    },
    submitForm(formName) {
      console.log(this.ruleForm.label, 'this.ruleForm.label');
      this.$refs[formName].validate(async(valid) => {
        if (valid) {
        // alert('submit!');
        this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing")
        const { data, success } = await sscDataSubmit(this.ruleForm)
        if(!success){
            return
        }
        await this.$emit("search");

        } else {
        console.log('error submit!!');
        return false;
        }
    });
    //   this.$confirm('是否保存?', '提示', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'warning'
    //   }).then(async () => {
    //     this.$refs[formName].validate((valid) => {
    //       if (valid) {
    //         alert('submit!');
    //       } else {
    //         console.log('error submit!!');
    //         return false;
    //       }
    //     });
    //   }).catch(() => {
    //   });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}
</style>
