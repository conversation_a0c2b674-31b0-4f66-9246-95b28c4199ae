<template>
    <el-dialog :visible.sync="visible" width="400px"   :title="'修改财务类型'" center>
            <el-form ref="editNoteForm" :model="editNoteForm" >
                <el-form-item  label="" prop="finance_name">
                    <el-select v-model="editNoteForm.finance_name" filterable  placeholder="财务类型" style="width:200px; "
                        class="el-select-content" clearable>
                        <el-option v-for="item in FeeNameList" :key="item.key+'dialog'" :label="item.value" :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="visible = false">取 消</el-button>
                <el-button type="primary" :loading="saveLoading" @click="saveOperation()">保 存</el-button>
            </div>
        </el-dialog>
</template>
<script>
import {  updateBillFinanceTypeItem } from '@/api/kj/cost.js';
import { boolean } from 'mathjs';

export default {
    name:'editform',
    components: {

    },
    props: {
        selids: {//批量的ids
            type: Array,
            required: true
        },
        FeeNameList:{//获取的列表
            type:Array,
            required: true,
        },
        thirdPlatform:{//对应平台
            type:String,
            required: true,
        },
        isSync:{//是否同步的 是同步时 syncCode必传
            type:Boolean,
            required: true,
        },
        syncCode:{
            type:String,
            default:undefined,
        },
        isMultiple:{//批量还是单个 单个时currentRow必传
            type:Boolean,
            required: true,
        },
        currentRow:{
            type:Object,
            default:{}
        },
    },
    data() {
        return {
            editNoteForm:{},
            FeeNameList:[],
            visible:false,
            saveLoading:false
        };
    },
    methods: {
        // async getFeeNameList(){
        //     const financeFeeName = await GetFinanceFeeTypes();
        //     this.FeeNameList = financeFeeName.data;
        // },
        async saveOperation(){
             if(this.editNoteForm.finance_name){
                this.$confirm("确认提交吗?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(async () => {
                    const params = {
                        thirdPlatform:this.thirdPlatform,
                        tableExtend:this.isSync?this.syncCode:undefined,
                        financeType:this.editNoteForm.finance_name,
                        ids: this.isMultiple ? this.selids : [this.currentRow.id]
                    }
                    let res = await updateBillFinanceTypeItem(params);
                    this.$refs.editNoteForm.resetFields()
                    if (res?.isSuccess) {
                        this.$message({ message: res.message, type: "success" });
                        this.$emit('getList','search')
                    }
                    else {
                        this.$message({ message: res.message, type: "error" });
                    }
                    this.visible = false
                });
             }else{
                this.$message({ message: '请选择需要修改的财务类型', type: "warning" });
             }
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .el-form-item__content{
    display: flex;
    justify-content: center;
}
</style>
