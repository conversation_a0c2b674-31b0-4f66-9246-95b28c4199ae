<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent></el-form>
    </template>
    <!--列表-->
    <ces-table v-if="showtable" ref="table" :that="that" :isIndex="true" :hasexpand="false" @sortchange="sortchange"
      :tableData="pddcontributeinfolist" @select="selectchange" :isSelection="false" :tableCols="tableCols"
      :loading="listLoading" :summaryarry='summaryarry'>
      <el-table-column type="expand">
        <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="col in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <template slot="extentbtn">
        <el-button-group>
          <el-button style="padding: 0;width: 135px;">
            <el-select v-model="styleCode" multiple filterable remote reserve-keyword placeholder="商品大类" clearable
              :remote-method="remoteMethod" :loading="searchloading">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="Filter.proCode" maxlength="50" clearable placeholder="商品ID" style="width:150px;" />
          </el-button>
          <el-button style="padding: 0;">
                <el-select filterable clearable v-model="Filter.shopCode" placeholder="店铺" style="width: 150px">
                  <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
                  </el-option>
                </el-select>
          </el-button>
          <el-button style="padding: 0;margin: 0;"> 
            <el-date-picker style="width: 210px" v-model="Filter.timerange" type="datetimerange" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
                </el-date-picker>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="Filter.SumType" collapse-tags clearable placeholder="数据分组汇总"
              style="width: 120px"> 
              <el-option label="按商品大类汇总" :value="1" />
             
            </el-select>
          </el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onstartImport">导入</el-button>
          <el-button type="primary" @click="onExportType">导出</el-button>
        </el-button-group>
      </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>

    <el-dialog title="导入" :visible.sync="dialogVisible" width="40%">
      <span>
        <el-row>
          <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
            <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action accept=".xlsx"
              :http-request="uploadFile" :file-list="fileList" :on-change="uploadChange">
              <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
            </el-upload>
          </el-col>
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="下载导出数据" :visible.sync="onExportDialogVisible" width="50%">

      <el-row>
        <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
          <el-select filterable v-model="Filter.ExportType" placeholder="请选择您要导出的数据" clearable>
            <el-option label="淘系售后退款明细" value="1"></el-option>
            <el-option label="淘系售后退款按月汇总" value="2"></el-option>
            <el-option label="按商品大类汇总" value="4"></el-option>
           
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
          <el-button :loading="uploadLoading" type="primary" @click="onExport">下载</el-button>
        </el-col>

      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="onExportDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>

import { getSaleAfterTxList ,exportSaleAfterTxList} from '@/api/bookkeeper/reportday'
import { importSaleAfterTx } from '@/api/bookkeeper/import'
import dayjs from "dayjs";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { formatPlatform, formatLinkProCode, formatTime } from "@/utils/tools";
import { getListByStyleCode } from "@/api/inventory/basicgoods"
  
const startDate = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
  name: "AfterSaleTx",
  components: {
    MyContainer,
    MyConfirmButton,
    MySearch,
    MySearchWindow,
    cesTable,
  },
  data() {
    return {
      onExportDialogVisible: false,
      importfilter: {
        version: ''
      },
      that: this,
      Filter: {
        platform: 1,
        proCode: null,
        startTime: null,
        timerange: [startDate, endDate],
        endTime: null,
        selectType: "1",
        ExportType: '',
        styleCode: null,
        SumType:null
      },
      shopList: [],
      styleCode: null,
      userList: [],
      groupList: [],
      pddcontributeinfolist: [],
      tableCols: this.gettableCols(),
      total: 0,
      summaryarry: {},
      pager: { OrderBy: "procode", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids: [],
      fileList: [],
      dialogVisible: false,
      uploadLoading: false,
      showtable: true
    };
  },
  async created() { 
    await this.getShopList();
    
  },  
  methods: {
       //系列编码远程搜索
       async remoteMethod(query) {
        if(query.length>300)
        {
          this.$message.error('输入长度过长请重新输入！！！！！！！！');
          return; 
        }
      if (query !== '') {
        this.searchloading == true
        setTimeout(async () => {
          const res = await getListByStyleCode({ currentPage: 1, pageSize: 50, styleCode: query })
          this.searchloading = false
          res?.data?.forEach(f => {
            this.options.push({ value: f.styleCode, label: f.styleCode })
          });
        }, 200)
      }
      else {
        this.options = []
      }
    },
   //导出弹窗
   onExportType(){
        this.onExportDialogVisible = true;
      },
 //导出
 async onExport() {
     if (this.onExporting) return;
     try{
          this.Filter.StartTime = null;
          this.Filter.EndTime = null;
          if (this.Filter.timerange) {
          this.Filter.StartTime = this.Filter.timerange[0];
          this.Filter.EndTime = this.Filter.timerange[1];
        }
        this.Filter.styleCode = this.styleCode.join()
        this.uploadLoading = true;
        const params = {...this.pager,...this.Filter}
        var res= await exportSaleAfterTxList(params);
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','淘系售后退款_' + new Date().toLocaleString() + '.xlsx' )
        this.uploadLoading = false;
        this.onExportDialogVisible = false;
        aLink.click()
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
      this.onExporting=false;
     },


    async getShopList() {
      const res1 = await getAllShopList({ platforms: [1, 9] });
      this.shopList = [];
      res1.data?.forEach(f => {
        if (f.isCalcSettlement && f.shopCode)
          this.shopList.push(f);
      });
    },
    gettableCols() {
      return [
      { istrue: true,  prop: 'yearMonthDay', label: '年月日', sortable: 'custom', width: '80' },
      { istrue: true,  prop: 'proCode', label: '商品ID', width: '110' },
      { istrue: true,  prop: 'shopName',  label: '店铺', width: '100' },
      { istrue: true,  prop: 'styleCode', label: '商品大类', width: '110', tipmesg: '系列编码',formatter: (row) => row.styleCode || ' '},
      { istrue: true,  prop: 'goodsName', label: '商品名称', width: '90' },
      { istrue: true,  prop: 'orderCount', label: '总单量', sortable: 'custom', width: '90', formatter: (row) => row.orderCount},
      { istrue: true,  prop: 'refundCountBefore', label: '发货前退款单量', sortable: 'custom', width: '150', formatter: (row) => row.refundCountBefore },
      { istrue: true,  prop: 'refundBeforeRate', label: '发货前退款率', sortable: 'custom', width: '150', formatter: (row) => !row.refundBeforeRate ? "0" : row.refundBeforeRate.toFixed(2) + '%'},
      { istrue: true,  prop: 'refundCountAfter', label: '发货后退款单量', sortable: 'custom', width: '150', formatter: (row) => row.refundCountAfter },
      { istrue: true,  prop: 'refundAfterRate', label: '发货后退款率', sortable: 'custom', width: '150', formatter: (row) => !row.refundAfterRate ? "0" : row.refundAfterRate.toFixed(2) + '%' },
      { istrue: true,  prop: 'payAmont', label: '支付金额', sortable: 'custom', width: '110',  formatter: (row) => row.payAmont },
      { istrue: true,  prop: 'refundAmontBefore', fix: true, label: '发货前退款金额', width: '150', sortable: 'custom',  },
      { istrue: true,  prop: 'refundBeforeRatio', label: '发货前退款占比', sortable: 'custom', width: '150' ,formatter: (row) => !row.refundBeforeRatio ? "0" : row.refundBeforeRatio.toFixed(2)+ '%'},
      { istrue: true,  prop: 'refundAmontAfter', label: '发货后退款金额', width: '150', sortable: 'custom',  },
      { istrue: true,  prop: 'refundAfterRatio', label: '发货后退款占比', sortable: 'custom', width: '150',  formatter: (row) => !row.refundAfterRatio ? "0" : row.refundAfterRatio.toFixed(2) + '%'},
      ]
    },
    showClo(){
      return this.Filter.startTime==this.Filter.endTime;
    },
    changeSelectType() { 
      this.getList();
    },
    sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      this.onSearch();
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
        this.Filter.StartTime = null;
        this.Filter.EndTime = null;
      
        if (this.Filter.timerange) {
          this.Filter.StartTime = this.Filter.timerange[0];
          this.Filter.EndTime = this.Filter.timerange[1];
        }
      this.Filter.styleCode = this.styleCode.join()
      const para = { ...this.Filter };
      let pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      this.listLoading = true;
      const res = await getSaleAfterTxList(params);
      if (!res.success) {
        this.listLoading = false;
        return;
      }
      this.listLoading = false;
      this.total = res.data.total;
      this.pddcontributeinfolist = res.data.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach((f) => {
        this.selids.push(f.id);
      });
    },
    async onstartImport() {
      this.fileList = [];
      this.uploadLoading = false;
      this.dialogVisible = true;
    },
    submitUpload() {
      this.uploadLoading = true
      this.$refs.upload.submit();
    },
    async uploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      let res = await importSaleAfterTx(form);
      if (res.code == 1) {
        this.dialogVisible = false
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      }
      this.fileList = []
      this.uploadLoading = false
    },
    async uploadChange(file, fileList) {
      let files = [];
      files.push(file);
      this.fileList = files;
    },
  },
};


</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
  