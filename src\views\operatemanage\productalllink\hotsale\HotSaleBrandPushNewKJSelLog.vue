<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
        </template>
        <vxetablebase :id="'HotSaleBrandPushNewKJSelLog202408041710'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :border="true"
            @sortchange='sortchange' @select='selectchange' :tableData='tableData' :tableCols='tableCols'
           :isSelection="false" :isSelectColumn="false"
            style="width: 100%;  margin: 0" v-loading="listLoading" :height="'100%'">
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    GetHotSaleBrandPushNewKJSelLogPageList
} from '@/api/operatemanage/productalllink/alllink'
const tableCols = [
    { width: '150', align: 'center', prop: 'createdTime', label: '操作时间', },
    { width: '150', align: 'center', prop: 'createdUserName', label: '操作人', },
    { align: 'center', prop: 'operationContent', label: '操作内容', },
];
export default {
    name: "HotSaleBrandPushNewKJSelLog",
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            that: this,
            filter: {
                id:0,
            },
            pager: { OrderBy: "createdTime", IsAsc: false },
            tableCols:tableCols,
            tableData: [],
            total: 0,
            listLoading: false,
            pageLoading: false,
            sels: [],
            selids: [],
            summaryarry: {},
        }
    },
    async mounted() {
        
    },
    methods: {
        async loadData(val) {
            this.filter.id=val.id;
            this.onSearch();
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getParam() {
            let pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            return params;
        },
        async getList() {
            let param = this.getParam();
            this.listLoading = true
            const res = await GetHotSaleBrandPushNewKJSelLogPageList(param)
            this.listLoading = false
            console.log(res);
            if (res?.success) {
                this.tableData = res.data.list;
                this.total = res.data.total;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}
</style>
