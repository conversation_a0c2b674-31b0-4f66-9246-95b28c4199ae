<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent>
      </el-form>
    </template>

      <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                   <el-input v-model="Filter.id" placeholder="数据ID" style="width:120px;"/>
                 </el-button>
                            <el-button style="padding: 0;margin: 0;">
                                <el-input v-model="Filter.FeeTypeL2" placeholder="费用子类" style="width:120px;"/>
                            </el-button>
                            <el-button style="padding: 0;margin: 0;">
                                <el-input v-model="Filter.ProductID" placeholder="商品ID" style="width:120px;"/>
                            </el-button>
                            <el-button style="padding: 0;margin: 0;">
                                <el-input v-model="Filter.FeeTypeL1" placeholder="费用大类" style="width:120px;"/>
                            </el-button>
                            <el-button style="padding: 0;margin: 0;">
                                <el-input v-model="Filter.UseMonth" placeholder="核算月份" style="width:120px;"/>
                            </el-button>
        
            <el-button type="primary" @click="onSearch">查询</el-button>

            
          </el-button-group>


    <!--列表-->
    <el-table ref="table" :that='that' :isIndex='true'
              :hasexpand='false' @sortchange='sortchange' :tableData='financialreportlist'
              @select='selectchange' :isSelection='false'
         :loading="listLoading">



   <el-table-column v-bind:key="index" v-for="(column,index) in tableCols" :label="column.label">
     <el-table-column v-bind:key="childcolindex" v-for="(childcol,childcolindex) in column.children" :label="childcol.label"
      
        prop="name"
        
        width="120">
      </el-table-column>

   </el-table-column>



       

       
    </el-table>
    <!--分页-->
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getfinancialreportList"
      />
    </template>

     
  </my-container>
</template>
<script>

import {getFinancialReportList } from '@/api/financial/yyfy'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
 
 
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
  data() {
    return {
      that:this,
      Filter: {
      },
      shopList:[],
      userList:[],
      groupList:[],
      financialreportlist: [],
      tableCols:[],
      total: 0,
      summaryarry:{count_sum:10},
      pager:{OrderBy:"id",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      //
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
    };
  },
  async mounted() {
     this.onSearch()
  },
  methods: {
    inittablecols(data){

      console.log(data)

     var cols =[{istrue:true,prop:'productID',label:'商品ID', width:'100',sortable:'custom',children:[]}];
var that=this;

      data.forEach(fee => {

        var filtercols=cols.filter(function(item){return item.label==fee.feeTypeL1;});

          if(filtercols.length==0)
            cols.push({istrue:true,prop:fee.feeTypeL1,label:fee.feeTypeL1, width:'200' ,children:[{istrue:true,prop:fee.FeeTypeL2Name ,label:fee.feeTypeL2, width:'200',sortable:'custom'}]});

          else
          {

           var filterchildrencol= filtercols[0].children.filter(function(item){return item.label==fee.feeTypeL2;});
           if(filterchildrencol.length==0)

           {
                  filtercols[0].children.push({istrue:true,prop:fee.FeeTypeL2Name,label:fee.feeTypeL2, width:'200',sortable:'custom'});

           } 



          }        
      });

this.tableCols=cols;





    },
   
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    
    onRefresh(){
        this.onSearch()
    },
    onSearch(){
       this.$refs.pager.setPage(1);
       this.getfinancialreportList();
    },
    async getfinancialreportList(){
     
      const para = {...this.Filter};
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,

      };

      console.log(para)

      this.listLoading = true;
      const res = await getFinancialReportList(params);
      console.log(res)
      this.listLoading = false;
      console.log(res.data.list)
      //console.log(res.data.summary)


this.inittablecols(res.data.summary);

      

      this.total = res.data.total
      this.financialreportlist = res.data.list;
      //this.summaryarry=res.data.summary;
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
