<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select v-model="ListInfo.applyStatusList" placeholder="状态" class="publicCss" clearable multiple collapse-tags>
                    <el-option label="待提交" value="待提交" />
                    <el-option label="审核中" value="审核中" />
                    <el-option label="已通过" value="已通过" />
                    <el-option label="已驳回" value="已驳回" />
                </el-select>
                <el-input v-model.trim="ListInfo.businessId" placeholder="审批编号" maxlength="50" clearable
                    class="publicCss" />
                <el-select v-model="ListInfo.handlingMethodList" placeholder="请选择退货事由" clearable class="publicCss" multiple collapse-tags>
                  <el-option label="滞销品退货" value="滞销品退货" />
                  <el-option label="运营要求退回" value="运营要求退回" />
                  <el-option label="约定退货" value="约定退货" />
                  <el-option label="正常工作范围退回" value="正常工作范围退回" />
                  <el-option label="采购正常错单退货" value="采购正常错单退货" />
                </el-select>
                <el-input v-model.trim="ListInfo.styleCode" placeholder="系列编码" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable
                    class="publicCss" />
                <el-select v-model="ListInfo.brandIdList" clearable filterable placeholder="请选择采购员" class="publicCss" multiple collapse-tags>
                    <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <dateRange :startDate.sync="ListInfo.initiationStartTime" :endDate.sync="ListInfo.initiationEndTime"
                    class="publicCss" start-placeholder="开始发起时间" end-placeholder="结束发起时间" />
                <dateRange :startDate.sync="ListInfo.approvalStartTime" :endDate.sync="ListInfo.approvalEndTime"
                    class="publicCss" start-placeholder="开始审批时间" end-placeholder="结束审批时间" />
                <dateRange :startDate.sync="ListInfo.completionStartTime" :endDate.sync="ListInfo.completionEndTime"
                    class="publicCss" start-placeholder="开始完成时间" end-placeholder="结束完成时间" />
                <el-select v-model.trim="ListInfo.editReturnReasonType" multiple collapse-tags placeholder="请选择修改退货事由类型" clearable class="publicCss">
                    <el-option label="未设置" value="未设置" />
                    <el-option label="保留任务,剔除奖金" value="保留任务,剔除奖金" />
                    <el-option label="剔除任务,同时剔除奖金" value="剔除任务,同时剔除奖金" />
                </el-select>
                <el-select v-model.trim="ListInfo.isProSelGoods" collapse-tags placeholder="请选择是否为选品中心的货物" clearable class="publicCss">
                    <el-option label="是" value="是" key="是"></el-option>
                    <el-option label="否" value="否" key="否"></el-option>
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                    <el-button type="primary" @click="importProps">导入</el-button>
                    <el-button type="primary" @click="downLoadExample">下载导入模版</el-button>
                    <el-button type="primary" @click="delProps">删除</el-button>
                    <el-button type="primary" @click="initiateApproval">发起审批</el-button>
                    <el-button type="primary" @click="onBonusSet" v-if="checkPermission('purchaseReturnExWarehouseBonusSet')">奖金设置</el-button>
                    <el-button type="primary" @click="viewUpdateReturnReason" v-if="checkPermission('updateReturnReason')">修改退货事由</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :treeProp="{}"
            @select="selectChexkBox" @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :showsummary="true" :summaryarry="summary" :isSelection="false" :isSelectColumn="false"
            style="width: 100%;  margin: 0" :loading="loading" :isDisableCheckBox="true"
            @checCheckboxkMethod="checCheckboxkMethod" :height="'100%'">
            <template #applyStatus="{ row }">
                <div style="display: flex;align-items: center;gap: 5px;justify-content:center;width: 100%;">
                  <span>{{ row.applyStatus }}</span>
                  <span v-if="row.currentNodeUserName && (row.currentNodeTime !== null && row.currentNodeTime !== undefined && row.currentNodeTime !== '')">({{ row.currentNodeUserName}}{{ formatDate(row.currentNodeTime) }})</span>
                </div>
            </template>
            <template #discountRatio="{ row }">
              <div style="gap: 5px;display: flex;" v-if="row.id">
                <div>{{row.discountRatio1}}%</div>
                <div>/</div>
                <div>{{ row.discountRatio2 }}%</div>
              </div>
            </template>
            <template #refoundAmount="{ row }">
              <div>
                {{row.refoundAmount}}
                <span v-if="!row.parentId">/
                {{ row.totalRefundAmount }}</span>
              </div>
            </template>
            <template slot="right">
                <vxe-column title="操作" width="180" fixed="right">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;" v-if="row.children">
                            <el-button type="text" @click="viewData(row)">查看</el-button>
                            <el-button type="text" @click="editData(row)" :disabled="row.applyStatus=='待提交' ? false : row.applyStatus=='已驳回' ? false :
                                row.applyStatus=='已撤销' ? false : true">编辑</el-button>
                            <el-button type="text" @click="cancleBonus(row)" v-if="row.applyStatus=='已通过' && row.purchaseBonus > 0
                                && checkPermission(['api:inventory:PurchaseReturnExWarehouse:CancleBonusPurchaseReturnExWarehouseApply'])">取消奖金</el-button>
                            <el-button type="text" @click="onEditActualQty(row)" v-if="row.applyStatus=='审核中'">编辑厂家实收数量</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
            <template #downLoad="{ row }">
                <el-button type="text" @click="downLoadFile(row.fileUrl)"
                    v-if="row.batchNumber && row.fileUrl">下载</el-button>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog v-dialogDrag v-loading="importLoading" title="导入数据" :visible.sync="importVisible" width="30%">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>

        <el-drawer title="发起审批" :visible.sync="drawer" direction="rtl" size="40%" @close="closeMethod">
            <div class="ApprovalDrawer">
                <el-form ref="form" :model="form" label-width="150px" :rules="rules">
                    <el-form-item label="所在部门:" prop="deptName">
                        <el-input v-model="form.deptName" disabled :disabled="true" placeholder="所在部门" class="formItemCss" />
                    </el-form-item>
                    <el-form-item label="退货事由:" prop="returnReason">
                        <el-select v-model="form.returnReason" placeholder="请选择退货事由" clearable :disabled="isView && edited" class="formItemCss">
                            <el-option label="滞销品退货" value="滞销品退货" />
                            <el-option label="运营要求退回" value="运营要求退回" />
                            <el-option label="约定退货" value="约定退货" />
                            <el-option label="正常工作范围退回" value="正常工作范围退回" />
                            <el-option label="采购正常错单退货" value="采购正常错单退货" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="系统供应商名称:" prop="supplier">
                        <el-input v-model="form.supplier" placeholder="请输入系统供应商名称" clearable :disabled="isView && edited" maxlength="50"
                            class="formItemCss" />
                    </el-form-item>
                    <el-button type="text" style="margin-left: 150px;" :disabled="isView && edited" @click="onNewDataMethod">新增一行</el-button>
                    <el-form-item label="退货审批:">
                      <el-table :data="form.returnApproval" style="width: 100%" v-loading="loading" max-height="200" height="200">
                        <el-table-column label="商品编码" width="125">
                          <template slot-scope="scope">
                            <el-input v-model.trim="scope.row.goodsCode" placeholder="商品编码" maxlength="50" clearable :disabled="isView && edited" class="formItemCss" @change="getBrekenProps($event,scope.row)"/>
                          </template>
                        </el-table-column>
                        <el-table-column prop="goodsName" label="商品名称"></el-table-column>
                        <el-table-column label="退货数量" width="65">
                          <template slot-scope="scope">
                            <el-input-number v-model="scope.row.returnQty" placeholder="退货数量" :min="0" :max="999999999" :precision="0" :controls="false" :disabled="isView && edited" class="formItemCss" />
                          </template>
                        </el-table-column>
                        <el-table-column label="厂家实收数量" width="65">
                          <template slot-scope="scope">
                            <el-input-number v-model="scope.row.actualQty" placeholder="厂家实收数量" :min="0" :max="999999999" :precision="0" :controls="false" :disabled="true" class="formItemCss" />
                          </template>
                        </el-table-column>
                        <el-table-column label="聚水潭成本" width="75">
                          <template slot-scope="scope">
                            <el-input-number v-model="scope.row.price" placeholder="聚水潭成本" :min="0" :max="999999999" :precision="4" :controls="false" :disabled="isView && edited" class="formItemCss" />
                          </template>
                        </el-table-column>
                        <el-table-column label="退货单价" width="65">
                          <template slot-scope="scope">
                            <el-input-number v-model="scope.row.returnPrice" placeholder="退货单价" :min="0" :max="999999999" :precision="4" :controls="false" :disabled="isView && edited" class="formItemCss" />
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" width="60">
                          <template slot-scope="scope">
                              <el-button type="text" style="color: red;" @click="onDeleteMethod(scope.row,scope.$index)" :disabled="isView && edited">删除</el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-form-item>
                    <el-form-item label="采购单号" prop="buyNo">
                        <el-input v-model="form.buyNo" placeholder="没有采购单号的填写0" clearable :disabled="isView && edited" maxlength="50" class="formItemCss"/>
                    </el-form-item>
                    <el-form-item label="是否为选品中心货物" prop="isProSelGoods">
                        <el-select v-model="form.isProSelGoods" placeholder="是否为选品中心的货物" clearable :disabled="isView && edited" maxlength="50" class="formItemCss">
                            <el-option value="是" key="是" label="是"></el-option>
                            <el-option value="否" key="否" label="否"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="退货处理方式:" prop="handlingMethod">
                        <el-select v-model="form.handlingMethod" placeholder="请选择退货处理方式" clearable :disabled="isView" class="formItemCss">
                            <el-option label="退货款" value="退货款" />
                            <el-option label="抵扣下次采购款" value="抵扣下次采购款" />
                            <el-option label="其它" value="其它" />
                            <el-option label="返厂维修" value="返厂维修" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="退货方式:" prop="returnMethod">
                        <el-select v-model="form.returnMethod" placeholder="请选择退货方式" clearable :disabled="isView" class="formItemCss">
                            <el-option label="快递（联系采购建单）" value="快递（联系采购建单）" />
                            <el-option label="快运（壹米滴答等……)" value="快运（壹米滴答等……)" />
                            <el-option label="物流到付（货运站退货）" value="物流到付（货运站退货）" />
                            <el-option label="物流现付（货运站退回）" value="物流现付（货运站退回）" />
                            <el-option label="本地货拉拉（到付）" value="本地货拉拉（到付）" />
                            <el-option label="本地货拉拉（现付）" value="本地货拉拉（现付）" />
                            <el-option label="不出库" value="不出库" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="退货地址:" prop="returnAddress">
                        <el-input type="textarea" v-model="form.returnAddress" :rows="3" :disabled="isView" placeholder="请输入退货地址"
                            maxlength="100" class="formItemCss" show-word-limit />
                    </el-form-item>
                    <el-form-item label="退货仓" prop="returnWarehouse">
                        <el-select v-model="form.returnWarehouse" placeholder="请选择退货仓" clearable :disabled="isView" class="formItemCss">
                            <el-option label="退跨境仓库存" value="退跨境仓库存"></el-option>
                            <el-option label="退国内仓库存" value="退国内仓库存"></el-option>
                            <el-option label="首力供应链库存" value="首力供应链库存"></el-option>
                            <el-option label="1688仓库存" value="1688仓库存"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="厂家实际退款总金额:">
                        <el-input v-model="form.refundAmount" disabled :disabled="true" class="formItemCss" placeholder="厂家实际退款总金额" />
                    </el-form-item>
                    <el-form-item label="大写:">
                        <el-input v-model="form.refundAmountCN" disabled :disabled="true" class="formItemCss" placeholder="大写" />
                    </el-form-item>
                    <el-form-item label="备注:">
                        <el-input type="textarea" :rows="3" placeholder="请输入备注" v-model="form.remark" :disabled="isView" maxlength="500"
                            show-word-limit class="formItemCss" />
                    </el-form-item>
                    <el-form-item label="图片:" prop="chatUrls">
                        <uploadimgFile v-if="editPriceVisible" ref="uploadimgFile" :accepttyes="accepttyes"
                            :disabled="isView" :ispaste="!isView" :noDel="isView" :isImage="true" :uploadInfo="form.chatUrls"
                            :keys="[1, 1]" @callback="getImg" :imgmaxsize="10" :limit="10" :multiple="true">
                        </uploadimgFile>
                    </el-form-item>
                    <!-- <el-form-item label="附件:">
                        <el-link type="primary" :href="form.fileUrl" target="_self"
                            @click="downLoadFile(form.fileUrl)">{{
                                form.fileUrl }}</el-link>
                    </el-form-item> -->
                    <el-form-item>
                        <div style="display: flex;justify-content: end;">
                            <el-button @click="drawer = false">取消</el-button>
                            <el-button type="primary" @click="submitForm('form')" v-throttle="1000" :disabled="isView">发起审批</el-button>
                            <el-button type="primary" @click="onSaveMethod('form')" v-throttle="1000" :disabled="isView && edited">保存</el-button>
                        </div>
                    </el-form-item>
                </el-form>
            </div>

        </el-drawer>

        <el-dialog v-dialogDrag v-loading="bonusSetLoading" title="奖金设置" :visible.sync="bonusSetVisible" width="30%">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-form>
                    <el-form-item>
                        <el-table :data="bonusSetList" style="width: 100%" v-loading="loading" max-height="600" height="400">
                            <el-table-column width="auto">
                                <template slot-scope="scope">
                                    <span>{{ scope.row.upperLimit }}{{ scope.row.upperLimit?">":"" }}折扣比例{{ scope.row.lowerLimit?"≥":"" }}{{ scope.row.lowerLimit }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column width="140">
                                <template slot-scope="scope">
                                    <div style="display: flex;flex-direction:row;justify-content:left;">
                                    <el-input-number v-model="scope.row.discount" placeholder="" :min="0" :max="999999999" :precision="0" :controls="false" :disabled="isView && edited" class="formItemCss" />
                                    <span>%</span>
                                </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-form-item>
                </el-form>
            </div>
            <div class="btnGroup">
                <el-button @click="bonusSetVisible = false">取消</el-button>
                <el-button type="primary" @click="onSaveBonusSet">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog v-dialogDrag v-loading="updateReturnReasonLoading" title="修改退货事由" :visible.sync="updateReturnReasonVisible" width="25%">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-form>
                    <el-form-iten>
                        <el-select v-model="editReturnReasonType">
                            <el-option label="保留任务,剔除奖金" key="保留任务,剔除奖金" value="保留任务,剔除奖金"></el-option>
                            <el-option label="剔除任务,同时剔除奖金" key="剔除任务,同时剔除奖金" value="剔除任务,同时剔除奖金"></el-option>
                        </el-select>
                    </el-form-iten>
                </el-form>
            </div>
            <div class="btnGroup">
                <el-button @click="updateReturnReasonVisible = false">取消</el-button>
                <el-button type="primary" @click="submitReturnReasonVisible">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog v-dialogDrag title="日志" :visible.sync="returnLogVisible" width="40%">
            <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :treeProp="{}" :rowHeight="80"
                @sortchange='returnLogSortchange' :tableData='returnLogList' :tableCols='returnLogTableCols' :isSelection="false"
                :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="returnLogLoading" :height="500">
                <template #operateBefore="{ row }">
                    <div style="display: flex;flex-direction: column;justify-content: center;">
                        <span>采购奖金：{{ row.purchaseBonusBefore }}</span>
                        <span>公司实际到款：{{ row.actualMoneyBefore }}</span>
                        <span>报损金额：{{ row.reportedLossBefore }}</span>
                    </div>
                </template>
                <template #operateAfter="{ row }">
                    <div style="display: flex;flex-direction: column;justify-content: center;">
                        <span>采购奖金：{{ row.purchaseBonusAfter }}</span>
                        <span>公司实际到款：{{ row.actualMoneyAfter }}</span>
                        <span>报损金额：{{ row.reportedLossAfter }}</span>
                    </div>
                </template>
            </vxetablebase>
            <template #footer>
                <my-pagination ref="returnLogPager" :total="returnLogTotal" @page-change="returnLogPagechange" @size-change="returnLogSizechange" />
            </template>
        </el-dialog>

        <el-dialog v-dialogDrag title="编辑厂家实收数量" :visible.sync="editActualQtyVisible" width="45%">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-form>
                    <el-form-item>
                        <el-table :data="actualQtyParam.goodsDtos" style="width: 100%" v-loading="actualQtyLoading" max-height="600" height="400">
                            <el-table-column prop="goodsCode" label="商品编码" width="140"></el-table-column>
                            <el-table-column prop="goodsName" label="商品名称" width="auto"></el-table-column>
                            <el-table-column prop="returnQty" label="退货数量" width="80"></el-table-column>
                            <el-table-column prop="price" label="聚水潭成本" width="80"></el-table-column>
                            <el-table-column prop="returnPrice" label="退货单价" width="80"></el-table-column>
                            <el-table-column label="厂家实收数量" width="100">
                                <template slot-scope="scope">
                                    <el-input-number v-model="scope.row.actualQty" placeholder="厂家实收数量" :precision="0"
                                    :min="0" :max="99999" :controls="false" style="width: 90px;"></el-input-number>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-form-item>
                </el-form>
            </div>
            <div class="btnGroup">
                <el-button @click="editActualQtyVisible = false">取消</el-button>
                <el-button type="primary" @click="submitEditActualQty" :loading="submitEditActualQtyLoading">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import { getAllProBrand, getBianManPositionListV2 } from '@/api/inventory/warehouse'
import {
    getPurchaseReturnExWarehouseDtlPage,
    exportPurchaseReturnExWarehouseDtl,
    delPurchaseReturnExWarehouseByBatch,
    getPurchaseReturnExWarehouseApplyInfo,
    sendPurchaseReturnExWarehouseApply,
    savePurchaseReturnExWarehouseApplyData,
    cancleBonusPurchaseReturnExWarehouseApply,
    getPurchaseReturnExWarehouseApplyData,
    getBonusSetData,saveBonusSetData,
    updateReturnReason,
    getPurchaseRturnExWarehouseLogPage,
    getPurchaseRturnExWarehouseGoods,
    editPurchaseRturnExWarehouseActualQty
} from '@/api/inventory/purchaseReturnExWarehouse'
import { importPurchaseReturnExWarehouese } from '@/api/inventory/purchaseImport.js'
import { getListForScan } from "@/api/inventory/basicgoods"
const tableCols = [
    { label: '', type: 'checkbox' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'batchNumber', label: '批次号', fixed: 'left', treeNode: true },
    { sortable: 'custom', width: '160', align: 'center', prop: 'applyStatus', label: '审批状态', fixed: 'left', },
    { sortable: 'custom', width: '90', align: 'center', prop: 'businessId', label: '审批编号', },
    { width: '90', align: 'center', prop: 'returnWarehouse', label: '退货仓', },
    { sortable: 'custom', width: '90', align: 'center', prop: 'handlingMethod', label: '退货事由', },
    { sortable: 'custom', width: '90', align: 'center', prop: 'styleCode', label: '系列编码', },
    { width: '90', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { width: '90', align: 'center', prop: 'goodsName', label: '商品名称', },
    { width: '90', align: 'center', prop: 'returnQty', label: '退货数量', },
    { width: '120', align: 'center', prop: 'actualQty', label: '厂家实收数量', },
    { width: '120', align: 'center', prop: 'price', label: '聚水潭成本', },
    // { width: '90', align: 'center', prop: 'totalPrice', label: '货品成本', },
    // { width: '90', align: 'center', prop: 'refoundAmount', label: '退款总成本', },
    { width: '90', align: 'center', prop: 'returnPrice', label: '退货单价', },
    { width: '120', align: 'center', prop: 'totalPrice', label: '货物总成本', },
    { width: '120', align: 'center', prop: 'refoundAmount', label: '退款总成本', tipmesg: '明细数据汇总((退货数量 或 厂家实收数量)×退货单价) / (流程)退款总成本' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'shippingFee', label: '运费', tipmesg: '(流程)运费' },
    { sortable: 'custom', width: '90', align: 'center', prop: 'purchaseBonus', label: '采购奖金', tipmesg: '((流程)退回金额-(流程)运费)×折扣比例对应的奖金比例' },
    { sortable: 'custom', width: '130', align: 'center', prop: 'actualMoney', label: '公司实际到款', tipmesg: '(流程)退款总成本-采购奖金-(流程)运费' },
    { width: '90', align: 'center', prop: 'discountRatio', label: '折扣比例', tipmesg: '退款中成本÷货物总成本 / ((流程)退回金额-(流程)运费)÷货品总成本' },
    { sortable: 'custom', width: '90', align: 'center', prop: 'reportedLoss', label: '报损金额', tipmesg: '货物总成本-公司实际到款' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'brandName', label: '采购', },
    { width: '70', align: 'center', prop: 'isProSelGoods', label: '是否为选品中心的货物', },
    { sortable: 'custom', width: '120', align: 'center', type: 'click', prop: 'editReturnReasonType', label: '修改退货事由类型', handle: (that, row) => that.viewReturnLogVisible(row) },
    // {
    //     sortable: 'custom', width: '70', align: 'center', prop: 'downLoad', label: '附件', fixed: 'right'
    // },
    { sortable: 'custom', width: '90', align: 'center', prop: 'initiationTime', label: '发起时间', fixed: 'right', },
    { sortable: 'custom', width: '90', align: 'center', prop: 'approvalTime', label: '审批时间', fixed: 'right', },
    { sortable: 'custom', width: '90', align: 'center', prop: 'completionTime', label: '完成时间', fixed: 'right', },
]
const tableCols1 = [
    { width: '110', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', },
    { width: '60', align: 'center', prop: 'returnQty', label: '退货数量', },
    { width: '80', align: 'center', prop: 'actualQty', label: '厂家实收数量', },
    { width: '70', align: 'center', prop: 'price', label: '聚水潭单价', },
    { width: '60', align: 'center', prop: 'returnPrice', label: '退货单价', },
]
const bonusSetList = [
    { upperLimit: null, lowerLimit: 1, discount: 0, sort: 1 },
    { upperLimit: 1, lowerLimit: 0.9, discount: 0, sort: 2 },
    { upperLimit: 0.9, lowerLimit: 0.8, discount: 0, sort: 3 },
    { upperLimit: 0.8, lowerLimit: 0.7, discount: 0, sort: 4 },
    { upperLimit: 0.7, lowerLimit: 0.6, discount: 0, sort: 5 },
    { upperLimit: 0.6, lowerLimit: 0.5, discount: 0, sort: 6 },
    { upperLimit: 0.5, lowerLimit: 0.4, discount: 0, sort: 7 },
    { upperLimit: 0.4, lowerLimit: null, discount: 0, sort: 8 }
]
const returnLogTableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'businessId', label: '审批编号' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'operateContent', label: '操作类型' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdUserName', label: '操作人' },
    { width: 'auto', align: 'center', prop: 'operateBefore', label: '修改前' },
    { width: 'auto', align: 'center', prop: 'operateAfter', label: '修改后' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '操作时间' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, uploadimgFile
    },
    data() {
        return {
            formatDate(time) {
              return time ? dayjs(time).format('YYYY-MM-DD') : '';
            },
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                applyStatusList: ['待提交'],
                handlingMethodList: [],
                brandIdList: []
            },
            isView: false,
            edited: false,
            timeRanges: [],
            tableCols,
            tableCols1,
            editPriceVisible: false,
            tableData: [],
            total: 0,
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            loading: true,
            pickerOptions,
            isExport: false,
            importVisible: false,
            importLoading: false,
            bonusSetLoading: false,
            bonusSetVisible: false,
            bonusSetList: bonusSetList,
            fileList: [],
            file: null,
            selectList: [],
            drawer: false,
            form: {
                deptName: null,
                returnReason: null,
                supplier: null,
                handlingMethod: null,
                returnMethod: null,
                returnAddress: null,
                returnWarehouse: null,
                refundAmount: null,
                refundAmountCN: null,
                desc: null,
                tableData: [],
                remark: null,
                chatUrls: [],
                batchNumber: null,
                returnApproval: [],
                buyNo: null,
                isProSelGoods: null
            },
            brandlist: [],
            rules: {
                returnReason: [{ required: true, message: '请选择退货事由', trigger: 'change' }],
                supplier: [{ required: true, message: '请输入系统供应商名称', trigger: 'change' }],
                buyNo: [{ required: true, message: '请输入采购单号', trigger: 'change' }],
                isProSelGoods: [{ required: true, message: '请选择是否为选品中心货物', trigger: 'change' }],
                handlingMethod: [{ required: true, message: '请选择退货处理方式', trigger: 'change' }],
                returnMethod: [{ required: true, message: '请选择退货方式', trigger: 'change' }],
                returnAddress: [{ required: true, message: '请输入退货地址', trigger: 'change' }],
                returnWarehouse: [{ required: true, message: '请输选择退货仓', trigger: 'change' }],
                remark: [{ required: true, message: '请输入备注', trigger: 'blur' }],
                chatUrls: [{ required: true, message: '请上传图片', trigger: 'change' }]
            },
            summary: {},
            updateReturnReasonLoading: false,
            updateReturnReasonVisible: false,
            editReturnReasonType: null, //修改退货事由类型   保留任务,剔除奖金   剔除任务,同时剔除奖金
            returnLogTableCols: returnLogTableCols,
            logListInfo:{
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                mainId: null
            },
            returnLogLoading: false,
            returnLogVisible: false,
            returnLogList: [],
            returnLogTotal: 0,
            editActualQtyVisible: false,
            actualQtyParam: {
                id: null,
                goodsDtos: []
            },
            actualQtyLoading: false,
            submitEditActualQtyLoading: false
        }
    },
    async mounted() {
        this.init()
        await this.getList()
    },
    methods: {
        closeMethod() {
          this.drawer = false
          this.editPriceVisible = false
          this.$refs.form.resetFields()
        },
        async getBrekenProps(skus,row) {
          //根据商品编码获取商品信息
          const params = {
            currentPage: 1,
            pageSize: 1000,
            orderBy: null,
            isAsc: false,
            goodsCode: skus,
          }
          const { data: { list }, success } = await getListForScan(params)
          if (success) {
            const index = this.form.returnApproval.findIndex(item => item.goodsCode === row.goodsCode)
            this.form.returnApproval[index].goodsName = list[0].goodsName
          } else {
            this.$message.error('暂未查询到相关商品信息')
          }
        },
        onDeleteMethod(row,index) {
          // const index = this.form.returnApproval.findIndex(item => item.goodsCode === row.goodsCode)
          this.form.returnApproval.splice(index, 1)
        },
        onNewDataMethod() {
          this.form?.returnApproval.push({
                goodsCode: '',
                goodsName: '',
                returnQty: '',
                price: '',
                returnPrice: ''
            })
            this.$nextTick(() => {
              this.$set(this.form, 'returnApproval', this.form.returnApproval)
            })
        },
        async editData(row){
          const { data, success } = await getPurchaseReturnExWarehouseApplyInfo({ batchNumber:row.batchNumber })
            if (success) {
                this.isView = false
                this.edited = false
                this.form = data ? data : {}
                this.form.chatUrls = (data && data.pictures) ? JSON.parse(data.pictures) : []
                this.form.returnApproval = data && data.returnApproval ? data.returnApproval : row.children ? row.children : []
                this.form.batchNumber = row.batchNumber ? row.batchNumber : ''
                this.drawer = true
                this.editPriceVisible = true
                this.$nextTick(() => {
                    this.$refs.form.clearValidate()
                })
            }
        },

        async cancleBonus(row) {
            this.$confirm('是否确认取消奖金?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const res = await cancleBonusPurchaseReturnExWarehouseApply({ batchNumber:row.batchNumber })
                this.loading = false
                if (res.success)
                    this.$message({ type: 'success', message: '操作成功!' });
                await this.getList()
            }).catch(() => {
            });
        },
        async viewData(row) {
            const { data, success } = await getPurchaseReturnExWarehouseApplyInfo({ batchNumber:row.batchNumber })
            if (success) {
                this.isView = true
                this.edited = true
                this.form = data ? data : {}
                this.form.chatUrls = (data && data.pictures) ? JSON.parse(data.pictures) : []
                this.form.returnApproval = data && data.returnApproval ? data.returnApproval : row.children ? row.children : []
                this.form.batchNumber = row.batchNumber ? row.batchNumber : ''
                this.drawer = true
                this.editPriceVisible = true
                this.$nextTick(() => {
                    this.$refs.form.clearValidate()
                })
            }
        },
        getImg(val) {
            this.form.chatUrls = val
            this.form.pictures = (val && val.length > 0) ? JSON.stringify(val) : ''
        },
        submitForm(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    const { success } = await sendPurchaseReturnExWarehouseApply({...this.form,goodsList:this.form.returnApproval})
                    if (success) {
                        this.$message({
                            type: 'success',
                            message: '发起审批成功!'
                        });
                        this.drawer = false
                        this.editPriceVisible = false
                        this.getList()
                    }
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        onSaveMethod(formName) {
          let flag = false;
          for (let index = 0; index < this.form.returnApproval.length; index++) {
            const item = this.form.returnApproval[index];
            if (!item.goodsCode || !item.goodsName || !item.returnQty) {
              flag = true;
              if (!item.goodsCode) {
                this.$message.error(`第${index + 1}行数据商品编码为空值`);
              } else if (!item.goodsName) {
                this.$message.error(`第${index + 1}行数据商品名称为空值`);
              } else if (!item.returnQty) {
                this.$message.error(`第${index + 1}行数据退货数量为空值`);
            //   } else if (!item.returnPrice) {
            //     this.$message.error(`第${index + 1}行数据退货单价为空值`);
            //   } else if (!item.price) {
            //     this.$message.error(`第${index + 1}行数据聚水潭单价为空值`);
              } else {
                this.$message.error(`第${index + 1}行数据有误`);
              }
              return;
            }
          }
          if (flag) return;
          this.$refs[formName].validate(async (valid) => {
              if (valid) {
                  const { success } = await savePurchaseReturnExWarehouseApplyData({...this.form,goodsList:this.form.returnApproval})
                  if (success) {
                      this.$message({
                          type: 'success',
                          message: '保存成功!'
                      });
                      this.drawer = false
                      this.editPriceVisible = false
                      this.getList()
                  }
              } else {
                  console.log('error submit!!');
                  return false;
              }
          });
        },
        checCheckboxkMethod(row, callback) {
            let isNotStock = row.batchNumber
            callback(isNotStock)
        },
        async init() {
            var res2 = await getAllProBrand();
            this.brandlist1 = res2.data;
            this.brandlist = res2.data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        async initiateApproval() {
            if (this.selectList.length == 0) return this.$message.error('请选择要审批的数据')
            if (this.selectList.length > 1) return this.$message.error('只能选择一条数据')
            // if (this.selectList[0].applyStatus != '待提交') return this.$message.error('只能选择待提交的数据')
            const { data, success } = await getPurchaseReturnExWarehouseApplyInfo({ batchNumber: this.selectList[0].batchNumber })
            if (success) {
                this.editPriceVisible = false
                this.$set(this, 'form', {})
                this.isView = false
                this.edited = false
                this.form = { ...data, batchNumber: this.selectList[0].batchNumber }
                this.form.chatUrls = (data && data.pictures) ? JSON.parse(data.pictures) : []
                this.form.returnApproval = data && data.returnApproval ? data.returnApproval : this.selectList[0].children ? this.selectList[0].children : []
                this.$set(this.form, 'returnApproval', this.selectList[0].children)
                //this.$set(this.form, 'chatUrls', [])
                this.$nextTick(() => {
                    this.drawer = true
                    this.editPriceVisible = true
                    setTimeout(() => {
                      if (this.$refs.form) {
                        this.$refs.form.clearValidate();
                      }
                    }, 0);
                })
            }
        },
        downLoadExample() {
            window.open("../../../static/excel/采购退货出库导入模版.xlsx", "_self");
        },
        delProps() {
            if (this.selectList.length == 0) return this.$message.error('请选择要删除的数据')
            this.$confirm('此操作将改批次下所有数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let batchNumber = this.selectList.map(item => item.batchNumber).join(',')
                const { success } = await delPurchaseReturnExWarehouseByBatch({ batchNumber });
                if (success) {
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                    this.getList()
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        selectChexkBox(val) {
            this.selectList = val
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData()
            form.append('upfile', this.file)
            this.importLoading = true
            await importPurchaseReturnExWarehouese(form).then(({ success }) => {
                if (success) {
                    this.$message.success('导入成功')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        downLoadFile(file) {
            window.open(file, "_self");
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await exportPurchaseReturnExWarehouseDtl(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '采购退货出库明细' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getPurchaseReturnExWarehouseDtlPage(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.summary = data.summary
                    this.selectList = []
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        async onBonusSet(){
            this.bonusSetVisible = true;
            var res = await getBonusSetData();
            if(res?.success && res?.data && res?.data.length > 0){
                this.bonusSetList = res.data
            }
        },
        async onSaveBonusSet(){
            this.bonusSetLoading = true;
            if(this.bonusSetList.length <= 0){
                this.$message.error('请输入奖金设置！');
                this.bonusSetLoading = false;
                return;
            }
            var params = [];
            this.bonusSetList.map(item => {
                params.push({
                    upperLimit: item.upperLimit,
                    lowerLimit: item.lowerLimit,
                    discount: item.discount
                });
            });
            var res = await saveBonusSetData(params);
            if(res?.success){
                this.$message.success(res.msg?res.msg:"保存成功！");
                this.bonusSetVisible = false;
            }
            this.bonusSetLoading = false;
        },
        viewUpdateReturnReason(){
            if (this.selectList.length == 0) return this.$message.error('请选择要修改的数据！');
            this.$confirm('当前已选择'+this.selectList.length+'条数据！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.editReturnReasonType = null;
                this.updateReturnReasonVisible = true;
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作！'
                });
            });
        },
        async submitReturnReasonVisible(){
            this.updateReturnReasonLoading = true;
            var ids = this.selectList.map(item => item.id);
            var param = {
                ids: ids,
                editReturnReasonType: this.editReturnReasonType
            }
            var res = await updateReturnReason(param);
            if(res?.success){
                this.updateReturnReasonVisible = false;
                this.getList();
            }
            this.updateReturnReasonLoading = false;
        },
        viewReturnLogVisible(row) {
            this.logListInfo.mainId = row.id;
            this.returnLogVisible = true;
            this.getLogList();
        },
        async getLogList(){
            this.returnLogLoading = true;
            var res = await getPurchaseRturnExWarehouseLogPage(this.logListInfo);
            if(res?.success){
                this.returnLogTotal = res?.data?.total??0;
                this.returnLogList = res?.data?.list??[];
            }
            this.returnLogLoading = false;
        },
        returnLogSortchange({ order, prop }) {
            if (prop) {
                this.logListInfo.orderBy = prop
                this.logListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getLogList()
            }
        },
        returnLogPagechange(val) {
            this.logListInfo.currentPage = val;
            this.getLogList();
        },
        returnLogSizechange(val) {
            this.logListInfo.currentPage = 1;
            this.logListInfo.pageSize = val;
            this.getList();
        },
        async onEditActualQty(row) {
            this.actualQtyParam.id = row.id;
            var param = { id: row.id };
            var res = await getPurchaseRturnExWarehouseGoods(param);
            if (res?.success) {
                this.actualQtyParam.goodsDtos = res?.data ?? [];
            }else{
                return;
            }
            this.editActualQtyVisible = true;
        },
        async submitEditActualQty() {
            this.submitEditActualQtyLoading = true;
            var res = await editPurchaseRturnExWarehouseActualQty({...this.actualQtyParam});
            this.submitEditActualQtyLoading = false;
            if (res?.success) {
                this.$message({
                    type: 'success',
                    message: '保存成功!'
                });
                this.getList();
                this.editActualQtyVisible = false;
            }else{
                this.$message({type: 'error',message: res?.msg ?? '保存失败!'});
            }
        }
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}

.ApprovalDrawer {
    padding: 7px;
}

.btnGroup {
    display: flex;
    justify-content: end;
}

.formItemCss {
    width: 100%;
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

::v-deep .cell {
    padding: 0 5px !important;
}

//控制选择器多选-标签宽度
::v-deep .el-select__tags-text {
  max-width: 50px;
}
</style>
