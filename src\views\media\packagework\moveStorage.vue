<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <div class="top_left">
                    <el-select v-model="ListInfo.accountantName" placeholder="核算人" style="width: 150px;"
                        class="publicMargin" clearable>
                        <el-option v-for="item in AccountantOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                    <el-select v-model="ListInfo.operatorName" placeholder="操作人" style="width: 150px;" class="publicMargin"
                        clearable>
                        <el-option v-for="item in operatorOptions" :key="item.label" :label="item.label"
                            :value="item.label" />
                    </el-select>
                    <el-input v-model="ListInfo.goodsNo" placeholder="商品编号" clear maxlength="50" style="width: 150px;"
                        class="publicMargin" clearable />
                    <el-button type="primary" @click="getList" class="publicMargin" v-if="type">查询</el-button>
                    <el-button type="primary" @click="getHisList" class="publicMargin" v-else>查询</el-button>
                    <el-button class="publicMargin" @click="clear">重置</el-button>
                    <div v-if="type" style="display: flex;">
                        <div>
                            工价: <el-input-number placeholder="工价" clear maxlength="50" :disabled="isDisabled"
                                style="width: 150px;" class="publicMargin" v-model="workPrice" :controls="false"
                                :max="10000" :min="0" :precision="2" />
                        </div>
                        <el-button @click="isDisabled = false">修改</el-button>
                        <el-button type="primary" class="publicMargin" @click="editWordPrice">保存</el-button>
                    </div>
                    <el-button type="primary" @click="downLoad" v-if="type">下载模版</el-button>
                </div>
                <div>
                    <el-dropdown v-if="type" class="publicMargin" @command="deleteWorkPrice">
                        <el-button type="primary">
                            批量操作<i class="el-icon-arrow-down el-icon--right"></i>
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="first">删除选中</el-dropdown-item>
                            <el-dropdown-item command="second">一键删除</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                    <el-button type="primary" @click="onexport" v-if="type">导出</el-button>
                    <el-button type="primary" @click="getHisExportData" v-else >导出</el-button>
                    <el-button type="primary" @click="dialogVisible = true" v-if="type">导入</el-button>
                </div>
            </div>
        </template>
        <vxetablebase :id="'moveStorage202408041635'" ref="table" @checkbox-range-end="chooseCode" :that='that' :isIndex='true' 
                :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
                :isIndexFixed="false"  style="width: 100%; height: 100%; margin: 0">
            </vxetablebase>
            <template #footer>
                <my-pagination :sizes="[500, 1000, 2000, 3000]" :page-size="50" ref="pager" :total="total"
                @page-change="movedPagechange" @size-change="moveSizechange" />
            </template>

            <el-dialog title="导入" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                            accept=".xlsx" :on-change="uploadFile" :file-list="fileList" :on-remove="uploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success"
                                @click="submitUpload">上传</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="dialogVisible = false">关闭</el-button>
                </span>
            </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getMoveStockListAsync, getMoveStockSelMemberAsync, exportMoveStockData, importMoveStockAsync, updateMoveStockWorkPriceAsync, deleteMoveStockAsync,deleteMoveStockAllAsync } from '@/api/inventory/packagesprocess'
import { getHisMoveStockListAsync, getHisMoveStockSelMemberAsync, getHisMoveStockExportData } from '@/api/inventory/packagesSetProcessing'
const tableCols = [
    { istrue: true, label: '', type: "checkbox",width:60 , align: 'left'},
    { istrue: true, prop: 'accountantName', label: '核算人' , align: 'left', width:108,type: 'custom'},
    { istrue: true, prop: 'operatorName', label: '操作人',width:120 , align: 'left'},
    { istrue: true, prop: 'operateDateStr', label: '操作日期',width:125,type: 'custom'  , align: 'left'},
    { istrue: true, prop: 'goodsNo', label: '商品编号',width:128,type: 'custom'  , align: 'left'},
    { istrue: true, prop: 'caseNo', label: '箱号',width:180 , align: 'left'},
    { istrue: true, prop: 'remark', label: '备注',width:260,type: 'custom'  , align: 'left'},
    { istrue: true, prop: 'binNo', label: '仓位号',width:118 , align: 'left'},
    { istrue: true, prop: 'estimatedWeight', label: '预估重量',width:120,type: 'custom'  , align: 'left'},
    { istrue: true, prop: 'qty', label: '数量',width:95 , align: 'left'},
    { istrue: true, prop: 'workPrice', label: '工价',width:'auto' , align: 'left'},
    { istrue: true, prop: 'totalPrice', label: '金额',width:120 , align: 'left'}, 
]
export default {
    name: "moveStorage",
    props: {
        type: {
            type: Boolean,
            default: true
        },
        versionId: {
            type: String,
            default: null
        }
    },
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            ids: [],
            workPrice: null,//工价
            isDisabled: true,//是否编辑
            total: 0,
            tableData: [],
            that: this,
            tableCols,
            AccountantOptions: [],//核算人
            operatorOptions: [],//操作人
            ListInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//排序方式
                accountantName: null,//核算人
                operatorName: null,//操作人
                goodsNo: null,//商品编号
                versionId: null//版本id
            },
            fileList: [],
            dialogVisible: false
        };
    },
    mounted() {
        if (this.type) {
            console.log(this.versionId, 'this.versionId');
            this.ListInfo.versionId = null
            this.getList();
            this.getPersonList()
        } else {
            this.ListInfo.versionId = this.versionId
            this.getHisList()
            this.getHisPersonList()
        }
    },
    methods: {
        deleteWorkPrice(e) {
            if (e == 'first') {
                if (this.ids.length == 0) {
                    this.$message.warning('请选择要删除的数据')
                    return
                }
                this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const { success } = await deleteMoveStockAsync({ ids: this.ids })
                    if (success) {
                        this.getList()
                        this.$message.success('删除成功')
                    } else {
                        this.$message.error('删除失败')
                    }

                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            } else {
                this.$confirm('此操作将删除移箱入库所有数据, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const { success } = await deleteMoveStockAllAsync()
                    if (success) {
                        this.getList()
                        this.$message.success('删除成功')
                    } else {
                        this.$message.error('删除失败')
                    }

                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            }
        },
        //下载模版
        downLoad() {
            window.open("../../static/excel/package/移箱入库导入模板.xlsx", "_self");
        },
        //修改移箱入库工价
        async editWordPrice() {
            const { success } = await updateMoveStockWorkPriceAsync({ workPrice: this.workPrice })
            if (success) {
                this.getList()
                this.$message.success("修改成功")
                this.isDisabled = true
            } else {
                this.$message.error("修改失败")
            }
        },
        //重置
        clear() {
            this.ListInfo.accountantName = null
            this.ListInfo.operatorName = null
            this.ListInfo.goodsNo = null
        },
        async submitUpload() {
            if (this.fileList.length == 0) {
                this.$message.warning('您没有选择任何文件！')
                return
            }
            const form = new FormData();
            form.append("upfile", this.fileList[0].raw);
            const { success } = await importMoveStockAsync(form)
            if (success) {
                this.getList()
                this.$message.success('上传成功')
                this.fileList = []
                this.dialogVisible = false
            }
        },
        uploadRemove() {
            this.fileList = []
        },
        async uploadFile(file, fileList) {
            this.fileList = fileList
        },
        //历史数据导出
        async getHisExportData() {
            let params = this.ListInfo
            //删除currentPage,pageSize
            delete params.currentPage
            delete params.pageSize
            const data = await getHisMoveStockExportData(params)
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '移箱入库历史' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        //导出
        async onexport() {
            let params = this.ListInfo
            //删除currentPage,pageSize
            delete params.currentPage
            delete params.pageSize
            const { data } = await exportMoveStockData(params)
            console.log(data, 'data');
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '移箱入库' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        async chooseCode(row) {
            this.ids = []
            //将row这个数组里面的styleCode放入setCategoryName里面的styleCodes
            this.ids = row.map(item => item.id)
        },
        //页面数量改变
        moveSizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.PageSize = val;
            this.getList();
        },
        //当前页改变
        movedPagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList();
        },
        //获取移箱入库搜索人员
        async getPersonList() {
            const { data, success } = await getMoveStockSelMemberAsync()
            if (success) {
                this.AccountantOptions = data.accountantList.map(item => {
                    return {
                        label: item,
                        value: item
                    }
                })
                this.operatorOptions = data.operatorList.map(item => {
                    return {
                        label: item,
                        value: item
                    }
                })
            } else {
                this.$message.error('获取移箱入库搜索人员失败');
            }
        },
        //获取移箱入库历史搜索人员
        async getHisPersonList() {
            const { data, success } = await getHisMoveStockSelMemberAsync()
            if (success) {
                this.AccountantOptions = data.accountantList.map(item => {
                    return {
                        label: item,
                        value: item
                    }
                })
                this.operatorOptions = data.operatorList.map(item => {
                    return {
                        label: item,
                        value: item
                    }
                })
            } else {
                this.$message.error('获取移箱入库历史搜索人员失败');
            }
        },
        //获取移箱入库列表
        async getList() {
            if (this.ListInfo.goodsNo) {
                this.ListInfo.goodsNo = this.ListInfo.goodsNo.replace(/\s+/g, "");
            }
            const { data, success } = await getMoveStockListAsync(this.ListInfo);
            if (success) {
                this.tableData = data.list;
                this.total = data.total;
                this.workPrice = data.extData.workPrice
            } else {
                this.$message.error('获取移箱入库列表失败');
            }
        },
        //获取移箱入库历史列表
        async getHisList() {
            if (this.ListInfo.goodsNo) {
                this.ListInfo.goodsNo = this.ListInfo.goodsNo.replace(/\s+/g, "");
            }
            const { data, success } = await getHisMoveStockListAsync(this.ListInfo);
            if (success) {
                this.tableData = data.list;
                this.total = data.total;
            } else {
                this.$message.error('获取移箱入库历史列表失败');
            }
        },
        //拍讯
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.top {
    display: flex;
    width: 100%;
    margin-bottom: 10px;
    justify-content: space-between;

    .top_left {
        display: flex;
    }
}

.publicMargin {
    margin-right: 10px;
}

::v-deep .vxetoolbar20221212 {
    position: absolute;
    top: 60px;
    right: 0px;
    padding-top: 0;
    padding-bottom: 0;
    z-index: 999;
    background-color: rgb(255 255 255 / 0%);
}
</style>