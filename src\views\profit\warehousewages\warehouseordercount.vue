<!-- 常规计件 -->
<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="日期">
                    <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        :picker-options="pickerOptions">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.warehouseCode" style="width: 160px" size="mini" @change="onSearch"
                        placeholder="仓库">
                        <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label" clearable
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onaddordercount">新增</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
                :isSelectColumn="false" :loading="listLoading" :tableHandles="tableHandles1">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog :title="ordercountTitle" :visible.sync="ordercountVisible" width="30%" v-dialogDrag
            :close-on-click-modal="false" v-loading="ordercountsaveLoading">
            <span>
                <el-form class="ad-form-query" :model="ordercountFormData" :inline="true" :rules="ordercountFormRules"
                    label-width="120px">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="日期" prop="workDate">
                                <el-date-picker style="width: 240px" v-model="ordercountFormData.workDate" type="date"
                                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" :picker-options="pickerOptions">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="仓库" prop="warehouseCode">
                                <el-select v-model="ordercountFormData.warehouseCode" style="width: 240px" size="mini"
                                    filterable>
                                    <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="发件量" prop="orderCount">
                                <el-input-number v-model="ordercountFormData.orderCount" :min="0" :max="100000000"
                                    auto-complete="off" style="width:100%" :precision="0" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="打包量" prop="daBaoCount">
                                <el-input-number v-model="ordercountFormData.daBaoCount" :min="0" :max="100000000"
                                    auto-complete="off" style="width:100%" :precision="0" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </span>
            <span slot="footer" class="dialog-footer">
                <my-confirm-button type="submit" @click="onordercountsave" /> &nbsp;
                <el-button @click="ordercountVisible = false">关闭</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>

<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import cesTable from "@/components/Table/table.vue";
import MySearch from "@/components/my-search";
import buschar from '@/components/Bus/buschar';
import MySearchWindow from "@/components/my-search-window";
import {
    getWarehouseOrderCountPageList, warehouseOrderCountSave, deleteWarehouseOrderCount,
} from '@/api/profit/warehousewages';
const tableCols = [
    { istrue: true, prop: 'workDate', label: '日期', width: '120', sortable: 'custom', formatter: (row) => formatTime(row.workDate, 'YYYY-MM-DD') },
    { istrue: true, prop: 'warehouseName', label: '仓库', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'orderCount', label: '发件量', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'daBaoCount', label: '打包量', width: '120', sortable: 'custom' },
    {
        istrue: true, type: 'button', label: '操作', width: '120',
        btnList: [
            { label: "编辑", handle: (that, row) => that.oneditordercount(row.id) },
            { label: "删除", handle: (that, row) => that.ondeleteordercount(row.id) }
        ]
    },
]
const tableHandles1 = [
    //{ label: "计算人效", handle: (that) => that.onCompute() },
    // { label: "导出", handle: (that) => that.onExport() },
];
export default {
    name: 'warehouseordercount',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, buschar },
    props: ['myWarehouseList'],
    data() {
        return {
            that: this,
            filter: {
                timerange: [
                    formatTime(dayjs().subtract(1, "month"), "YYYY-MM-DD"),
                    formatTime(dayjs(), "YYYY-MM-DD"),
                ],
                startDate: null,
                endDate: null,
                warehouseCode: null,
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "createdTime", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            tableHandles1: tableHandles1,
            pickerOptions: {
                shortcuts: [
                    {
                        text: '昨天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime());
                            picker.$emit('pick', [start, start]);
                        }
                    }, {
                        text: '近三天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime());
                            const end = new Date(new Date(tdate.toLocaleDateString()));
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一周',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 5);
                            const end = new Date(new Date(tdate.toLocaleDateString()).getTime() + 3600 * 1000 * 24 * 5);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一个月',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 31);
                            console.log("获取前一个月的时间", tdate.getDay());
                            const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
            },

            ordercountTitle: "",
            ordercountVisible: false,
            ordercountsaveLoading: false,
            myPostIdList: [],
            ordercountFormData: {
                workDate: null,
                warehouseCode: null,
                warehouseName: null,
                orderCount: 0,
                daBaoCount: 0,
            },
            ordercountFormRules: {
                workDate: [{ required: true, message: '请输入日期', trigger: 'blur' }],
                warehouseCode: [{ required: true, message: '请输入仓库', trigger: 'blur' }],
            },
        };
    },
    async mounted() {
        await this.onSearch();
    },
    methods: {
        //查询第一页
        async onSearch() {
            console.log(this.filter)
            if (!this.filter.timerange) {
                this.$message({ message: "请选择日期", type: "warning", });
                return;
            }
            this.$refs.pager.setPage(1);
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.$message({ message: "请先选择日期", type: "warning" });
                return false;
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const res = await getWarehouseOrderCountPageList(params)
            this.listLoading = false;
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            //this.summaryarry = res.data.summary;
            data.forEach(d => {
                d._loading = false;
            })
            this.list = data;
        },
        //排序查 询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            console.log(rows)
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onaddordercount() {
            this.ordercountFormData = {
                workDate: null,
                warehouseCode: null,
                warehouseName: null,
                orderCount: 0,
                daBaoCount: 0,
            };
            this.ordercountTitle = "新增";
            this.ordercountVisible = true;
        },
        async onordercountsave() {
            this.ordercountFormData.warehouseName = this.myWarehouseList.find(f => f.value == this.ordercountFormData.warehouseCode)?.label;
            console.log(this.ordercountFormData);
            this.ordercountsaveLoading = true;
            const res = await warehouseOrderCountSave(this.ordercountFormData);
            this.ordercountsaveLoading = false;
            if (res?.success) {
                this.$message({ type: 'success', message: '操作成功!' });
                this.ordercountVisible = false;
                await this.onSearch();
            }
        },
        async ondeleteordercount(rowid) {
            this.$confirm('确定要执行删除吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                console.log(rowid);
                const res = await deleteWarehouseOrderCount({ id: rowid });
                if (res?.success) {
                    this.$message({ type: 'success', message: '删除成功!' });
                    await this.onSearch();
                }
            }).catch(() => {
            });
        },
        async oneditordercount(rowid) {
            this.ordercountVisible = true;
            let params = { id: rowid, currentPage: 1, pageSize: 1, startDate: null, endDate: null };
            this.ordercountsaveLoading = true;
            const res = await getWarehouseOrderCountPageList(params);
            this.ordercountsaveLoading = false;
            if (res?.success) {
                let data = res.data.list[0];
                console.log(data);
                this.ordercountFormData = {
                    id: data.id,
                    workDate: data.workDate,
                    orderCount: data.orderCount,
                    daBaoCount: data.daBaoCount,
                    warehouseCode: data.warehouseCode,
                    warehouseName: data.warehouseName,
                };
            }
        },
    },
};
</script>

<style lang="scss" scoped></style>
