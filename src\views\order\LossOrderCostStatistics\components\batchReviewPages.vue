<template>
    <!-- 批量审核 -->
    <div>
        <vxetablebase :id="'batchReviewPages202408041822'" ref="table" :tableData="tableData" :tableCols="tableCols" :that="that" :hasSeq="false"
            :isIndexFixed="false" :isSelection="false" :isSelectColumn="false" :isIndex="true" :hasexpand="false"
            style="width: 100%; height: 420px; margin: 0" />
        <div style="display: flex;flex-direction: column;">
            <div style="display: flex;align-items: center;">
                <div style="margin-right: 10px;"><span style="color: red">*</span>定责方式:</div>
                <el-radio-group v-model="radio" style="margin: 20px;" @change="changeRaiod">
                    <el-radio :label="3">按申诉填报新责任类型</el-radio>
                    <el-radio :label="6">指定新责任部门负责人</el-radio>
                </el-radio-group>
            </div>
            <div v-if="radio == 6" style="margin: 20px 0;display: flex;">
                <div style="display: flex;align-items: center;">
                    <div style="margin: 0 20px 0 0;">新责任部门: </div>
                    <el-select class="marginleft" v-model="form.newZrDepartment" clearable placeholder="新责任部门(大类)"
                        style="width: 150px;" @change="getZrType(form.newZrDepartment)">
                        <el-option v-for="item in damagedList" :key="item" :label="item" :value="item" />
                    </el-select>
                </div>
                <div style="display: flex;align-items: center;">
                    <div style="margin: 0 20px 0 0;">新责任类型: </div>
                    <el-select class="marginleft" v-model="form.newZrType2" @change="changesel" clearable
                        placeholder="原责任类型(细类)" style="width: 200px;">
                        <el-option v-for="item in damagedList2" :key="item" :label="item" :value="item" />
                    </el-select>
                </div>
                <div style="display: flex;align-items: center;">
                    <div style="margin: 0 20px 0 0;width: 100px;">新责任人: </div>
                    <el-select v-if="form.newZrDepartment == '采购'"
                        v-model="form.newMemberId" filterable @change="newMemberIdChange">
                        <el-option v-for="item in brandlist" :key="item.key" :label="item.value" :value="item.key" />
                    </el-select>
                    <el-select v-else-if="form.newZrDepartment == '运营'" v-model="form.newMemberId" filterable
                        @change="newMemberIdChange">
                        <el-option v-for="item in directorList" :key="item.key" :label="item.value"
                            :value="item.key" />
                    </el-select>
                    <YhUserelector v-else-if="form.newZrDepartment == '客服' || form.newZrDepartment == '仓库'"
                        :value.sync="form.newMemberDDUserId" :text.sync="form.newMemberName"></YhUserelector>
                    <el-input v-else v-model="form.newMemberName" @input="changesel" clearable maxlength="10" />
                </div>

            </div>
            <div style="display: flex;align-items: center;">
                <div style="margin: 0 20px 0 0;"><span style="color: red">*</span>审核意见:</div>
                <el-input type="textarea" placeholder="审核意见" v-model="form.auditRemark" maxlength="200" show-word-limit
                    style="width: 400px;" :rows="4" />
            </div>
        </div>
        <div class="btnBox">
            <el-button @click="handleClose">关闭</el-button>
            <el-button type="primary" @click="auditFun(1)">审核通过</el-button>
            <el-button type="danger" @click="auditFun(0)">审核驳回</el-button>
        </div>
    </div>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'
import { auditDeductZrAppeal } from '@/api/customerservice/DamagedOrders'
import dayjs from 'dayjs'
import YhUserelector from '@/components/YhCom/yh-userselector.vue'
import {
    getDirectorList,
    getDirectorGroupList,
    getProductBrandPageList,
    getList as getshopList,
} from "@/api/operatemanage/base/shop";
import { getAllWarehouse, getAllProBrand } from '@/api/inventory/warehouse'
import {
    getDamagedOrdersWithholdListAsync, getDeductZrAppeal4CRUD, getDamagedOrdersZrType, getDamagedOrdersZrDept, setZrMemberCustomize, saveDeductZrAppeal,
    batchDeductZrAppeal, batchAuditDeductZrAppeal
} from '@/api/customerservice/DamagedOrders'
//认可状态
const approvalStatus = [
    {
        label: '待初审',
        value: 0
    },
    {
        label: '不认可',
        value: -1
    },
    {
        label: '认可',
        value: 1
    },
]
//申请状态
const applyStatus = [
    {
        label: '打回',
        value: -1
    },
    {
        label: '草稿待申请',
        value: 0
    },
    {
        label: '申请中',
        value: 1
    },
    {
        label: '已通过',
        value: 2
    },
]
//平台
const platformList = [
    { label: '天猫', value: 1 },
    { label: '拼多多', value: 2 },
    { label: '阿里巴巴', value: 4 },
    { label: '抖音', value: 6 },
    { label: '京东', value: 7 },
    { label: '淘工厂', value: 8 },
    { label: '淘宝', value: 9 },
    { label: '苏宁', value: 10 }
]
const tableCols = [
    { istrue: true, prop: 'platform', label: '平台', width: '80', formatter: (row) => platformList.find(item => item.value == row.platform)?.label },
    { istrue: true, prop: 'orderNo', label: '内部订单号', width: '170' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '100' },
    { istrue: true, prop: 'afterSaleApproveDate', label: '售后发起时间', width: '130', formatter: (row) => dayjs(row.afterSaleApproveDate).format('YYYY-MM-DD') },
    { istrue: true, prop: 'damagedAmount', label: '损耗金额', width: '100' },
    { istrue: true, prop: 'orgZrDepartment', label: '原责任部门', width: '130' },
    { istrue: true, prop: 'orgZrType2', label: '原责任类型', width: '130' },
    { istrue: true, prop: 'orgZrUserName', label: '原责任人', width: '100' },
    { istrue: true, prop: 'zrSetDate', label: '责任计算时间', width: '130' },
    { istrue: true, prop: 'applyUserName', label: '申请人', width: '130' },
    { istrue: true, prop: 'applyTime', label: '申诉时间', width: '130' },
    { istrue: true, prop: 'newZrDepartment', label: '新责任部门', width: '130' },
    { istrue: true, prop: 'newZrType2', label: '新责任类型', width: '130' },
    { istrue: true, prop: 'newZrUserName', label: '新责任人', width: '100' },
    { istrue: true, prop: 'applyReason', label: '申诉理由', width: '100' },
    { istrue: true, prop: 'applyContent', label: '申诉内容', width: '100' },
]
export default {
    props: {
        //接收父组件传递的值
        handleClose: {
            type: Function,
            required: true
        },
        tableData: {
            type: Array,
            required: true
        },
        ids: {
            type: Array,
            required: true
        }
    },
    components: { vxetablebase, YhQuillEditor, YhUserelector },
    name: "batchReviewPages",
    data() {
        return {
            damagedList: [],
            damagedList2: [],
            directorList: [],
            approvalStatus,
            applyStatus,
            platformList,
            that: this,
            tableCols,
            radio: 3,
            form: {
                ids: [],
                auditState: null,
                auditRemark: "",
                appointByAudtior: null,
                newZrDepartment: "",
                newZrType2: "",
                newZrUserName: "",
                newZrUserId: 0,
                newZrDDUserId: "",
                newMemberId: 0,
                newMemberName: "",
                newMemberDDUserId: "",
                newMemberDDUserName: "",
                orderList: null,
            },
        };
    },
    mounted() {
        this.form = {
            ids: [],
            auditState: null,
            auditRemark: "",
            appointByAudtior: null,
            newZrDepartment: "",
            newZrType2: "",
            newZrUserName: "",
            newZrUserId: 0,
            newZrDDUserId: "",
            newMemberId: 0,
            newMemberName: "",
            newMemberDDUserId: "",
            newMemberDDUserName: "",
            orderList: null,
        },
        this.getZrDept();
        this.setBandSelect();
        this.getDirectorlistt();
    },
    methods: {
        async getDirectorlistt() {
            const res1 = await getDirectorList({});
            const res2 = await getDirectorGroupList({});

            this.directorList = res1.data;
            this.directorGroupList = [{ key: "0", value: "未知" }].concat(
            res2.data || []
            );
        },
        changesel() {
            this.$forceUpdate();
        },
        newMemberIdChange() {
            // let arr = null;
            // if (this.form.newZrDepartment == "采购" || this.form.newZrDepartment == "运营") {
            //     arr = [...this.brandlist];
            // }
            // //  else if (this.form.newZrDept == "运营") {
            // //      arr = [...this.directorList];
            // //  }
            // //  if (arr != null && arr && this.form.newMemberId) {
            // let opt = arr.find(x => x.key == this.form.newMemberId);
            // if (opt) {
            //     this.form.newMemberName = opt.value;
            // }
            // //  }
            let arr=null;
            if(this.form.newZrDepartment=="采购"){
                arr=[...this.brandlist];                   
            }
            else if(this.form.newZrDepartment=="运营"){
                arr=[...this.directorList];                    
            }    
            
            // if(arr!=null && arr && this.form.newMemberId){                  
                let opt=arr.find(x=>x.key==this.form.newMemberId);
                if(opt){
                    this.form.newMemberName=opt.value;                      
                }
            // }

            this.changesel();
        },
        async getZrDept() {
            let res = await getDamagedOrdersZrDept();
            if (!res.success) {
                return
            }
            this.damagedList = res?.data;
            console.log("打印数据GetDamagedOrdersZrDept", this.damagedList)
            // damagedList2
            // this.changesel();
        },
        async getZrType(name) {
            let res = await getDamagedOrdersZrType(name);
            if (!res.success) {
                return
            }
            this.damagedList2 = res.data;
            this.form.newMemberName = '';
            this.form.newMemberDDUserId = '';
            this.form.newMemberId = '';
            this.form.newZrType2 = '';
            this.$forceUpdate();

        },
        async setBandSelect() {
            var res = await getAllProBrand();
            if (!res?.success) return;
            this.brandlist = res.data;
        },
        changeRaiod(e) {
            console.log(e, 'e');
        },
        async auditFun(val) {
            console.log(this.form, 'form');
            if (!this.form.auditRemark) {
                this.$message({
                    message: '请输入审核意见',
                    type: 'warning'
                })
                return
            }
            //如果没有选择新责任部门
            if (this.radio == 6 && !this.form.newZrDepartment) {
                this.$message({
                    message: '请选择新责任部门',
                    type: 'warning'
                })
                return
            }
            //如果没有选择新责任类型
            if (this.radio == 6 && !this.form.newZrType2) {
                this.$message({
                    message: '请选择新责任类型',
                    type: 'warning'
                })
                return
            }
            //如果没有选择新责任人
            if (this.radio == 6 && !this.form.newMemberName) {
                this.$message({
                    message: '请选择新责任人',
                    type: 'warning'
                })
                return
            }
            this.form.ids = this.ids
            this.form.auditState = val
            if (this.radio == 3) {
                this.form = {
                    ids: this.ids,
                    auditState: val,
                    auditRemark: this.form.auditRemark,
                    appointByAudtior: 0,
                    newZrDepartment: "",
                    newZrType2: "",
                    newZrUserName: "",
                    newZrUserId: 0,
                    newZrDDUserId: "",
                    newMemberId: 0,
                    newMemberName: "",
                    newMemberDDUserId: "",
                    newMemberDDUserName: "",
                    orderList: null,
                }
            } else {
                this.form.appointByAudtior = 1
            }
            const { success } = await batchAuditDeductZrAppeal(this.form)
            if (success) {
                if ((val == 1)) {
                    this.$message({
                        message: '审核成功',
                        type: 'success'
                    })
                } else {
                    this.$message({
                        message: '驳回成功',
                        type: 'success'
                    })
                }
                this.handleClose()
                this.$emit('getList')
            }
        }
    },
};
</script>

<style scoped lang="scss">
.btnBox {
    display: flex;
    justify-content: end;
    margin-top: 20px;
}
</style>
