<template>
  <section class="wrap">
        <div v-if="dataReady">
            <el-card style="width:100%" v-for="item in list" :key="item.id" v-loading="updatelogisshow">
                <div slot="header" class="clearfix">
                    <span >{{item.title}}</span>
                </div>
                <div class="text item html">
                    <div v-html="item.content" @click="showImg($event)"></div>
                </div>
                <!-- 附件 -->
                <div v-if="item.attachments && item.attachments.length > 0" class="attachments-section">
                    <div class="attachments-title">附件：</div>
                    <div class="attachments-list">
                        <div
                            v-for="(file, index) in item.attachments"
                            :key="index"
                            class="attachment-item"
                            @click="downloadFile(file)"
                        >
                            <i :class="getFileIcon(file.name)"></i>
                            <span class="file-name">{{ file.name }}</span>
                            <i class="el-icon-download download-icon"></i>
                        </div>
                    </div>
                </div>
            </el-card>
        </div>
    <el-backtop target=".container .main" :visibility-height="200" class="backtop" />

    <el-image-viewer v-if="showGoodsImage" :url-list="imgList" :on-close="closeFunc" style="z-index:9999;"/>
  </section>
</template>

<script>
import { formatTime } from "@/utils";
import { getPurchaseLogAsync,addPurchaseClickLog } from '@/api/admin/opration-log'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import cescolumnmerge from "@/components/Table/yhcolumnmerge.vue";
import cescolumn from "@/components/Table/yhcolumn.vue";

export default {
  name: 'Welcome',
  components: {cescolumnmerge,cescolumn,ElImageViewer},
  data() {
    return {
      filter:{
            id:null,
            title:null
            },
      pager:{OrderBy:"createdTime",IsAsc:false},
      list: [],
      imgList:[],
      showGoodsImage:false,
      dataReady: false, // 控制数据是否准备完成
      updatelogisshow:false
    }
  },
  async mounted() {
    //每两小时更新一次
    // this.$nextTick(async () =>{
    //   setInterval(this.getlist,7200000)
    // })

  },
  methods:{
    async getlist(row){
      this.dataReady = false; // 重置数据状态
      this.filter.title = row.title
      this.filter.id = row.id
      var params = {...this.filter, ...this.pager}
      // console.log("打印参数",params);
      this.updatelogisshow = true
      const res = await getPurchaseLogAsync(params)
      if (!res?.code){
        this.updatelogisshow = false
        return
      }
       //添加点击记录
       await addPurchaseClickLog({ id: row.id });
      this.updatelogisshow = false
      const data = res.data
      // 处理附件数据
      data.forEach(item => {
        if (item.fileUrlJson) {
          try {
            const attachments = JSON.parse(item.fileUrlJson);
            item.attachments = attachments || [];
          } catch (error) {
            console.error('解析附件数据失败:', error);
            item.attachments = [];
          }
        } else {
          item.attachments = [];
        }
      });
      this.list = data;
      // 数据处理完成，然后渲染
      this.dataReady = true;
    },
    async showImg(e){
      if (e.target.tagName == 'IMG') {
        console.log('image',e)
        this.showGoodsImage = true;
        this.imgList = [];
        this.imgList.push(e.target.src);
      }
    },
    async clicktimelist(row){
      console.log('时间',row)
    },
    async handleSetLineChartData(type){

    },
    async closeFunc(){
      this.showGoodsImage = false;
    },
    // 获取文件的图标
    getFileIcon(fileName) {
      const extension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
      switch (extension) {
        case 'pdf':
          return 'el-icon-document';
        case 'doc':
        case 'docx':
          return 'el-icon-document';
        case 'xls':
        case 'xlsx':
        case 'xlsm':
          return 'el-icon-s-grid';
        default:
          return 'el-icon-document';
      }
    },
    downloadFile(file) {
      // 使用 fetch 下载文件并指定文件名
      fetch(file.url)
        .then(response => response.blob())
        .then(blob => {
          // 创建一个临时的 URL
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = file.name; // 使用 file.name 作为下载文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          // 释放临时 URL
          window.URL.revokeObjectURL(url);
        })
        .catch(error => {
          console.error('下载文件失败:', error);
          this.$message.error('下载文件失败');
        });
    },
  },
  }
</script>
<style scoped>
.clearfix{
  height: 20px;
}

/* 附件样式 */
.attachments-section {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.attachments-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 10px;
}

.attachments-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  max-width: 300px;
}

.attachment-item:hover {
  background-color: #ecf5ff;
  border-color: #b3d8ff;
  color: #409eff;
}

.attachment-item i {
  margin-right: 8px;
  font-size: 16px;
  color: #909399;
}

.attachment-item:hover i {
  color: #409eff;
}

.file-name {
  flex: 1;
  font-size: 13px;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.attachment-item:hover .file-name {
  color: #409eff;
}

.download-icon {
  margin-left: 8px;
  margin-right: 0;
  font-size: 14px;
  opacity: 0.7;
}

.attachment-item:hover .download-icon {
  opacity: 1;
}

::v-deep table {
        border-collapse: collapse;
    }
::v-deep td,th {
        border: 1px solid #ccc;
        min-width: 50px;
        height: 20px;
    }
::v-deep th {
        background-color: #f1f1f1;
    }
</style>
