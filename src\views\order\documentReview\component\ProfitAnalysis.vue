<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss" />
                <number-range :min.sync="ListInfo.profit1Min" :max.sync="ListInfo.profit1Max" min-label="出仓利润 - 最小值"
                    max-label="出仓利润 - 最大值" class="publicCss" />
                <!-- <number-range :min.sync="ListInfo.profit2Min" :max.sync="ListInfo.profit2Max" min-label="推广利润 - 最小值"
                    max-label="推广利润 - 最大值" class="publicCss" /> -->
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.orderNo" v-model="ListInfo.orderNo"
                    placeholder="内部订单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300"
                    :maxlength="6000" @callback="orderNoCallback" title="内部订单号" style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.proCode" v-model="ListInfo.proCode"
                    placeholder="宝贝ID/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300"
                    :maxlength="6000" @callback="proCodeCallback" title="宝贝ID" style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <!-- <el-input v-model.trim="ListInfo.proCode" placeholder="宝贝ID" maxlength="50" clearable
                    class="publicCss" /> -->
                <el-input v-model.trim="ListInfo.title" placeholder="标题" maxlength="50" clearable class="publicCss" />
                <el-select v-model="ListInfo.platform" placeholder="平台" class="publicCss" clearable>
                    <el-option v-for="item in platformlist" :label="item.label" :value="item.value" :key="item.value" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
                <el-button type="primary" size="mini" @click="basicSettings">设置</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%"
            :showsummary="data.summary ? true : false" :summaryarry="data.summary" @sortchange="sortchange" />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="设置" :visible.sync="setVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
            <el-input type="textarea" placeholder="请输入内容" v-model="ignoreQuesType" maxlength="500" show-word-limit />
            <div style="display: flex;justify-content: center;margin-top: 20px;">
                <el-button @click="setVisible = false">取消</el-button>
                <el-button type="primary" @click="basicSettingsSubmit">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan
    },
    props: {
        styleCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'payTime',
                isAsc: false,
                styleCode: this.styleCode,
                summarys: [],
                startDate: dayjs().format('YYYY-MM-DD'),
                endDate: dayjs().format('YYYY-MM-DD'),
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            ignoreQuesType: '',
            setVisible: false
        }
    },
    async mounted() {
        await this.getCol();
        await this.getList()
    },
    methods: {
        basicSettingsSubmit() {
            if (!this.ignoreQuesType) return this.$message.error('请输入内容')
            request.post('/api/verifyOrder/SalePriceAnalysis/Setting', { ignoreQuesType: this.ignoreQuesType }).then(({ success }) => {
                if (success) {
                    this.$message.success('设置成功')
                    this.setVisible = false
                } else {
                    this.$message.error('设置失败')
                }
            })
        },
        async basicSettings() {
            this.ignoreQuesType = ''
            try {
                await request.post('/api/verifyOrder/SalePriceAnalysis/GetSetting').then(({ data }) => {
                    this.ignoreQuesType = data ? data.ignoreQuesType : ''
                })
                this.setVisible = true
            } catch (error) {
                this.$message.error('获取设置失败')
            }
        },
        proCodeCallback(val) {
            this.ListInfo.proCode = val
        },
        orderNoCallback(val) {
            this.ListInfo.orderNo = val
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await request.post('/api/verifyOrder/SalePriceAnalysis/ExportData', this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getCol() {
            const { data, success } = await request.post('/api/verifyOrder/SalePriceAnalysis/GetColumns')
            if (success) {
                data.forEach(item => {
                    if (item.prop == 'orderNoInner') {
                        item.type = 'orderLogInfo'
                        item.orderType = 'orderNoInner'
                    }
                    if (item.prop == 'proCode') {
                        item.type = 'html'
                        item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
                    }
                })
                this.tableCols = data;
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post('/api/verifyOrder/SalePriceAnalysis/PageGetData', this.ListInfo)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        async changeTime(e) {
            this.ListInfo.startTime = e ? e[0] : null
            this.ListInfo.endTime = e ? e[1] : null
            await this.getList()
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
