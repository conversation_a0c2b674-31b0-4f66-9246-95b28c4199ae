<template>
  <MyContainer>
    <div style="height: 40px; margin-top: 10px; display: flex; align-items: center;gap: 5px;">
      <div>
        <el-date-picker style="width: 210px" v-model="timerange" type="daterange" :clearable="false" format="yyyy-MM-dd"
          value-format="yyyy-MM-dd" range-separator="至" :start-placeholder="'开始时间'" :end-placeholder="'结束时间'"
          :picker-options="pickerOptions" @change="changeTime"></el-date-picker>
      </div>
      <div>
        <el-select v-model="ListInfoTop.fullNameBefore" placeholder="片区" class="publicCss" clearable filterable>
          <el-option v-for="item in optionData.fullNameList" :key="item" :label="item" :value="item" />
        </el-select>
      </div>
      <div>
        <el-select v-model="ListInfoTop.platformBefore" placeholder="平台" class="publicCss" clearable filterable>
          <el-option v-for="item in optionData.platformlist" :key="item" :label="item" :value="item" />
        </el-select>
      </div>
      <div>
        <el-select v-model="ListInfoTop.groupIdBefore" placeholder="运营组" class="publicCss" clearable filterable>
          <el-option v-for="item in optionData.grouplist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div>
        <el-select v-model="ListInfoTop.userNameBefore" placeholder="姓名" :filter-method="changeoptions" class="publicCss" clearable filterable>
          <el-option v-for="item in directorlist" :key="item" :label="item" :value="item" />
        </el-select>
      </div>
      <div style="width: 30px; font-weight: 600; text-align: center">对比</div>
      <div>
        <el-select v-model="ListInfoTop.fullNameAfter" placeholder="片区" class="publicCss" clearable filterable>
          <el-option v-for="item in optionData.fullNameList" :key="item" :label="item" :value="item" />
        </el-select>
      </div>
      <div>
        <el-select v-model="ListInfoTop.platformAfter" placeholder="平台" class="publicCss" clearable filterable>
          <el-option v-for="item in optionData.platformlist" :key="item" :label="item" :value="item" />
        </el-select>
      </div>
      <div>
        <el-select v-model="ListInfoTop.groupIdAfter" placeholder="运营组" class="publicCss" clearable filterable>
          <el-option v-for="item in optionData.grouplist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div>
        <el-select v-model="ListInfoTop.userNameAfter" placeholder="姓名" :filter-method="changeoptions" class="publicCss" clearable filterable>
          <el-option v-for="item in directorlist" :key="item" :label="item" :value="item" />
        </el-select>
      </div>
      <el-button type="primary" @click="onSearch" style="width: 70px;">查询</el-button>
      <div>
        <el-radio v-model="ListInfoTop.groupBy" label="月">月</el-radio>
        <el-radio v-model="ListInfoTop.groupBy" label="日">日</el-radio>
      </div>
    </div>
    <div style="margin: 10px 0;display: flex;justify-content: center;">
      <el-checkbox-group v-model="ListInfoTop.checkList" @change="showchart(ListInfoTop)">
        <el-checkbox label="月均加班时长"></el-checkbox>
        <el-checkbox label="日均加班时长"></el-checkbox>
        <el-checkbox label="订单量"></el-checkbox>
        <el-checkbox label="销售金额"></el-checkbox>
        <el-checkbox label="毛四利润(发生)"></el-checkbox>
        <el-checkbox label="毛四利润率"></el-checkbox>
      </el-checkbox-group>
    </div>
    <div style="height: 550px;" v-loading="orderLoading">
      <div>
        <buschar v-if="orderAnalysisChar.visible" ref="detailtrendchartref" :analysisData="orderAnalysisChar.data">
        </buschar>
      </div>
    </div>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getOperatePerformanceReport, getOperateListValueRemote } from '@/api/people/peoplessc.js';
import dayjs from "dayjs";
import { pickerOptions } from '@/utils/tools'
import buschar from "@/components/Bus/buschar";
import fatEffectSet from "@/views/order/distributionOrders/fatEffectSet.vue"
export default {
  name: "detailDialogBox",
  components: { MyContainer, vxetablebase, buschar, fatEffectSet },
  props: {
    ListInfo: {
      type: Object,
      default: {}
    },
    optionData: {
      type: Object,
      default: {}
    },
  },

  data() {
    return {
      detailtrendchart: { visible: false, title: "", data: {} },
      orderAnalysisChar: { visible: false, title: "", data: {} },
      inventoryAnalysisChar: { visible: false, title: "", data: {} },
      that: this,
      directorlist: [],
      ListInfoTop: {
        groupBy: "月",
        checkList: [],
        startQueryTime: null, //开始时间
        endQueryTime: null, //结束时间
        fullNameBefore: null,
        fullNameAfter: null,
        platformBefore: null,
        platformAfter: null,
        groupIdBefore: null,
        groupIdAfter: null,
        userNameBefore: null,
        userNameAfter: null,
        monthOrDayBefore: null,
        monthOrDayAfter: null,
      },
      timerange: [],
      orderTime: [],
      inventoryTime: [],
      loading: false,
      orderLoading: false,
      inventoryLoading: false,
      pickerOptions,
      fatEffectSetDialog: {
        visiable: false,
        title: "坪效设置",
      }
    };
  },
  async mounted() {
    let a = { ...this.ListInfo };
    const defaultStart = dayjs().subtract(30, 'day').toDate();
    const defaultEnd = dayjs().toDate();
    this.ListInfoTop.startQueryTime = a.startQueryTime || defaultStart;
    this.ListInfoTop.endQueryTime = a.endQueryTime || defaultEnd;
    this.timerange = [
      dayjs(this.ListInfoTop.startQueryTime).toDate(),
      dayjs(this.ListInfoTop.endQueryTime).toDate(),
    ];
    console.log("this.optionData.row",this.optionData.row);
    this.ListInfoTop.fullNameBefore = this.optionData.row.regionName || null
    this.ListInfoTop.platformBefore = this.optionData.row.platform || null
    this.ListInfoTop.groupIdBefore = this.optionData.row.groupId || null
    this.ListInfoTop.userNameBefore = this.optionData.row.userName || null
    this.ListInfoTop.checkList = this.optionData.type ? (this.optionData.type).split(',') : []

    this.directorlist = this.optionData.directorlist;
    await this.showchart(this.ListInfoTop);
  },
  methods: {
    async changeoptions(val){
        let { data: data2 } = await getOperateListValueRemote({ currentPage: 1, pageSize: 50, value: val, fieldName: 'userName' })
        this.directorlist = data2
    },
    async changeTime(e) {
      this.ListInfoTop.startQueryTime = e ? e[0] : null
      this.ListInfoTop.endQueryTime = e ? e[1] : null
    },
    async onSearch() {
      await this.showchart(this.ListInfoTop)
    },
    async showchart(params) {
      this.orderLoading = true;
      this.orderAnalysisChar.visible = false;
      const { data, success } = await getOperatePerformanceReport(params);
      this.orderLoading = false;
      if (success) {
        const a = [];
        this.ListInfoTop.checkList.forEach(item => {
          if (item === '月均加班时长') {
            a.push("月均加班时长");
          } else if (item === '日均加班时长') {
            a.push("日均加班时长");
          } else if (item === '订单量') {
            a.push("订单量")
          } else if (item === '销售金额') {
            a.push("销售金额")
          } else if (item === '毛四利润(发生)') {
            a.push("毛四利润(发生)")
          } else if (item === '毛四利润率') {
            a.push("毛四利润率");
          }
        });
        data.legend = data.legend ? data.legend.filter(item => {
          return a.some(str => item.includes(str))
        }) : []
        data.selectedLegend = data.selectedLegend ? data.selectedLegend.filter(item => {
          return a.some(str => item.includes(str))
        }) : data.legend
        data.series = data.series ? data.series.filter(item => {
          return a.some(str => item.name.includes(str))
        }) : []
        data.series.map((item) => {
          item.itemStyle = {
            normal: {
              label: {
                show: true,
                position: "top",
                textStyle: {
                  fontSize: 14,
                },
              },
            },
          };
          item.emphasis = {
            focus: "series",
          };
          item.smooth = false;
        });
        this.orderAnalysisChar.visible = true;
        this.orderAnalysisChar.data = data;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.publicCss {
  width: 135px;
}
</style>
