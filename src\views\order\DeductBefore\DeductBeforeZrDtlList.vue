<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-date-picker style="width: 220px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" range-separator="至" :start-placeholder="'提交开始时间'" :end-placeholder="'提交结束时间'"
                :picker-options="pickerOptions">
            </el-date-picker>

            <el-select v-model="filter.noPassType" style="width:90px;" placeholder="违规类型" :clearable="true">
                <el-option label="==所有==" :value="0"></el-option>
                <el-option label="绑批次超时" :value="1"></el-option>
                <el-option label="杂配货超时" :value="2"></el-option>
                <el-option label="团打单超时" :value="3"></el-option>
                <el-option label="团配货超时" :value="4"></el-option>
                <el-option label="打包超时" :value="5"></el-option>
                <el-option label="称重超时" :value="6"></el-option>
            </el-select>

            <el-input v-model.trim="filter.orderNoInner" placeholder="内部单号" style="width:120px;" clearable
                oninput="if(value){value=value.replace(/[^\-\d]/g,'')} if(value>2147483647){value=2147483647} if(value<0){value=0}" />

            <el-input v-model.trim="filter.keywords" clearable placeholder="关键字查询" style="width:180px;"
                :maxlength="100">
                <el-tooltip slot="suffix" class="item" effect="dark"
                    content="支持搜索的内容：绑批次超时6、杂配货超时4、团打单超时2、团配货超时2、打包超时8、称重超时10、发货仓库、快递公司" placement="bottom">
                    <i class="el-input__icon el-icon-question"></i>
                </el-tooltip>
            </el-input>

            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="onExport">导出</el-button>
        </template>

        <template style="margin-top: 10px;">
            <vxetablebase :id="'DeductBeforeZrDtlList202408041744'" ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" @select="selectchange" :tableData='list' :tableCols='tableCols'
                :isSelection="true" :loading="listLoading">

            </vxetablebase>
        </template>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="selids.length" @get-page="getlist" />
        </template>

        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
            v-dialogDrag>
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNoInner="orderNoInner"
                style="z-index:10000;height:600px" />
        </el-dialog>
    </my-container>
</template>

<script>
import MyConfirmButton from '@/components/my-confirm-button'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import { pickerOptions } from '@/utils/tools'
import { PageDeductBeforeZrList, ExportDeductBeforeZrList } from "@/api/order/deductbefore"
import MyContainer from "@/components/my-container";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";

const tableCols = [
    { istrue: true, prop: 'orderNoInner', fixed: 'left', label: '内部单号', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showLogDetail(row) },

    { istrue: true, prop: 'node1NoPass', label: '绑批次超时6', width: '80', sortable: 'custom', formatter: (row) => row.node1NoPass == 1 && !row.node1ZrMemberName ? 'x' : row.node1ZrMemberName },
    { istrue: true, prop: 'node2NoPass', label: '杂配货超时4', width: '80', sortable: 'custom', formatter: (row) => row.node2NoPass == 1 && !row.node2ZrMemberName ? 'x' : row.node2ZrMemberName },
    { istrue: true, prop: 'node3NoPass', label: '团打单超时2', width: '80', sortable: 'custom', formatter: (row) => row.node3NoPass == 1 && !row.node3ZrMemberName ? 'x' : row.node3ZrMemberName },
    { istrue: true, prop: 'node4NoPass', label: '团配货超时2', width: '80', sortable: 'custom', formatter: (row) => row.node4NoPass == 1 && !row.node4ZrMemberName ? 'x' : row.node4ZrMemberName },
    { istrue: true, prop: 'node5NoPass', label: '打包超时8', width: '80', sortable: 'custom', formatter: (row) => row.node5NoPass == 1 && !row.node5ZrMemberName ? 'x' : row.node5ZrMemberName },
    { istrue: true, prop: 'node6NoPass', label: '称重超时10', width: '80', sortable: 'custom', formatter: (row) => row.node6NoPass == 1 && !row.node6ZrMemberName ? 'x' : row.node6ZrMemberName },

    { istrue: true, prop: 'noPassSum', label: '违规合计', width: '70', sortable: 'custom' },


    { istrue: true, prop: 'submitTime', label: '提交发货', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.submitTime, "MM-DD HH:mm") },
    { istrue: true, prop: 'bindWaveTime', label: '绑定批次', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.bindWaveTime, "YYYY") == '2050' ? '' : formatTime(row.bindWaveTime, "MM-DD HH:mm") },
    { istrue: true, prop: 'printMdTime', label: '打印面单', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.printMdTime, "YYYY") == '2050' ? '' : formatTime(row.printMdTime, "MM-DD HH:mm") },
    { istrue: true, prop: 'phEndTime', label: '配货完成', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.phEndTime, "YYYY") == '2050' ? '' : formatTime(row.phEndTime, "MM-DD HH:mm") },
    { istrue: true, prop: 'packageTime', label: '打包完成', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.packageTime, "YYYY") == '2050' ? '' : formatTime(row.packageTime, "MM-DD HH:mm") },
    { istrue: true, prop: 'weightTime', label: '称重时间', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.weightTime, "YYYY") == '2050' ? '' : formatTime(row.weightTime, "MM-DD HH:mm") },
    { istrue: true, prop: 'maxWmsName', label: '发货仓库', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'maxWaveId', label: '批次号', width: '70', sortable: 'custom' },
    { istrue: true, prop: 'maxExpressCompanyName', label: '快递公司', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'waveOperator', label: '批次操作', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'waveRemark', label: '批次备注', width: '100', sortable: 'custom' },



]

export default {
    name: 'DeductBeforeZrDtlList',
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, OrderActionsByInnerNos, vxetablebase },
    props: {
        outFilter: {
            type: Object,
            default: () => { return {}; }
        },
    },
    data() {
        return {
            that: this,
            filter: {
                submitDateStart: null,
                submitDateEnd: null,
                noPassType: 0,
                orderNoInner: null,
                timerange: [
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                ],
                keywords: null,
            },
            pickerOptions: pickerOptions,
            dialogHisVisible: false,
            orderNo: '',
            orderNoInner: 0,
            list: [],
            summaryarry: {},
            pager: { OrderBy: "submitTime", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            selids: [],
            listLoading: false,
            pageLoading: false,
            dialogVisible: false
        };
    },
    async mounted() {
        await this.onSearch()
    },
    methods: {
        showLogDetail(row) {
            this.orderNoInner = row.orderNoInner;
            this.dialogHisVisible = true;
        },
        onShowLogistics(row) {
            let self = this;
            this.$showDialogform({
                path: `@/views/order/logisticsWarning/DbLogisticsRecords.vue`,
                title: '物流明细',
                args: { expressNos: row.expressNo },
                height: 300,
            });
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.submitDateStart = this.filter.timerange[0];
                this.filter.submitDateEnd = this.filter.timerange[1];
            } else {
                this.$message({ message: "请先选择日期", type: "warning" });
                return false;
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            console.log(this.outFilter);
            const params = {
                ...pager,
                ...page,
                ...this.outFilter,
                ... this.filter
            }
            console.log(params);
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await PageDeductBeforeZrList(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.selids = [];
            this.sels = [];

            this.total = res.data.total;
            const data = res.data.list;
            this.list = data;
            this.summaryarry = res.data.summary;
        },
        //排序查询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            this.sels = [];
            //console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
                this.sels.push(f);
            })
        },
        async onExport() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await ExportDeductBeforeZrList(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', new Date().toLocaleString() + '导出-违规明细.xlsx');
            aLink.click()
        },
    },
};
</script>

<style lang="scss" scoped></style>
