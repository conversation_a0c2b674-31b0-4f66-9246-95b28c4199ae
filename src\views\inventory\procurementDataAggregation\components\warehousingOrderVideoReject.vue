<template>
    <MyContainer>
        <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="table.list" :table-cols="tableCols" :is-selection="false"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%;height: 480px; margin: 0;"
            :isNeedExpend="false" @sortchange="sortchange" />
        <template #footer>
            <my-pagination ref="pager" :total="table.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>

import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import request from '@/utils/request'

const tableCols = [
    { sortable: 'custom', istrue: true, width: '160', align: 'center', label: '入库拍摄任务编号', prop: 'warehousNo' },
    { sortable: 'custom', istrue: true, width: '140', align: 'center', label: '驳回人', prop: 'createdUserName' },
    { sortable: 'custom', istrue: true, width: 'auto', align: 'center', label: '备注', prop: 'remark' },
    { sortable: 'custom', istrue: true, width: '180', align: 'center', label: '驳回时间', prop: 'createdTime' },
];

export default {
    name: 'WarehousingOrderVideoRejectRecord',
    components: {MyContainer,vxetablebase},
    props: {
        api: {
            type: String,
            default: '/api/inventory/PurchaseSummary/InWms/'
        },
        detailsInfo: {
            type: Object,
            default: {}
        }
    },
    data() {
        return {
            that: this,
            tableCols: tableCols,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false
            },
            total: 0,
            loading: true,
            table: {
                list: [],
                total: 0
            }
        }
    },
    async mounted() {
        await this.getList();
    },
    methods: {
        async getList() {
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}GetWarehousingOrderVideoRejectRecord`, { ...this.ListInfo, ...this.detailsInfo })
                if (success) {
                    this.table = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList();
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList();
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList();
            }
        },
    }
}

</script>