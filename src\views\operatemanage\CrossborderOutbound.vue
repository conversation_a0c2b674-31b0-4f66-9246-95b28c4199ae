<template>
  <my-container>
    <template #header>
      <el-form class="top" v-model="form" :inline="true">
        <el-form-item>
          <el-button :type="form.Outbound.type" @click="changeType('outbound')" style="margin:0">出库箱</el-button>
          <el-button :type="form.Inbound.type" @click="changeType('inbound')" style="margin:0">装箱柜</el-button>
        </el-form-item>
        <el-form-item>
          <el-input v-show="form.Outbound.type == 'primary'" placeholder="出库箱编码" v-model="form.Outbound.filter.BoxCode"
            :clearable="true" style="width: 130px;" maxlength="20"></el-input>
          <el-input v-show="form.Inbound.type == 'primary'" placeholder="装箱柜编码"
            v-model="form.Inbound.filter.ContainerCode" :clearable="true" style="width: 130px;"
            maxlength="20"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-show="form.Outbound.type == 'primary'" placeholder="产品sku" v-model="form.Outbound.filter.goodsCode"
            :clearable="true" style="width: 130px;" maxlength="20"></el-input>
          <el-input v-show="form.Inbound.type == 'primary'" placeholder="产品sku" v-model="form.Inbound.filter.goodsCode"
            :clearable="true" style="width: 130px;" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-show="form.Outbound.type == 'primary'" placeholder="装箱柜编码"
            v-model="form.Outbound.filter.ContainerCode" :clearable="true" style="width: 130px;"
            maxlength="20"></el-input>
          <el-input v-show="form.Inbound.type == 'primary'" placeholder="出库箱编码" v-model="form.Inbound.filter.BoxCode"
            :clearable="true" style="width: 130px;" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item>
          <!-- <el-input  placeholder="装箱柜编码"
            v-model="form.Outbound.filter.ContainerCode" :clearable="true" style="width: 130px;"
            maxlength="20"></el-input> -->
          <el-input v-show="form.Outbound.type == 'primary'" placeholder="批次号"
            v-model="form.Outbound.filter.BatchNumber" :clearable="true" style="width: 130px;"
            maxlength="20"></el-input>
        </el-form-item>
        <el-form-item>
          <el-date-picker v-model="form.Outbound.filter.timerange" v-show="form.Outbound.type == 'primary'"
            type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            :picker-options="pickerOptions" style="width: 210px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
          </el-date-picker>
          <el-date-picker v-model="form.Inbound.filter.timerange" v-show="form.Inbound.type == 'primary'"
            type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            :picker-options="pickerOptions" style="width: 210px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
          </el-date-picker>
        </el-form-item>
        <el-button type="primary" @click="getList('search')">查询</el-button>


        <el-select v-model="exportType" placeholder="选择导出" style="width: 90px; margin-left: 10px;" filterable>
          <el-option label="父列表" :value="1"></el-option>
          <el-option label="子列表" :value="2"></el-option>
        </el-select>
        <el-button type="primary" @click="exportdemo()" v-if="checkPermission('Crossborder_Outbound_export')">导出</el-button>
        <el-button type="primary" @click="showImport" v-throttle="1000">导入</el-button>
        <el-button type="primary" @click="getTemplate" v-throttle="1000">下载导入模板</el-button>           
      </el-form>
    </template>
    <vxetablebase :id="'CrossborderOutbound202408041641_1'" v-show="form.Outbound.type == 'primary'" ref="outTable"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :row-config="{ isHover: true }"
      :tableData='outTableData' :tableCols='outTableCols' @sortchange='outSortchange' :isSelection="false"
      :isSelectColumn="false" style="width: 100%;  margin: 0" :summaryarry="outBoundSummary" :showsummary="true"
      :loading="form.Outbound.loading" :height="'100%'">
    </vxetablebase>

    <vxetablebase :id="'CrossborderOutbound202408041641_2'" v-show="form.Inbound.type == 'primary'" ref="inTable"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :row-config="{ isHover: true }"
      :tableData='inTableData' :tableCols='inTableCols' @sortchange='InboundSortchange' :isSelection="false"
      :isSelectColumn="false" style="width: 100%;  margin: 0" :summaryarry="inBoundSummary" :showsummary="true"
      :loading="form.Inbound.loading" :height="'100%'">
    </vxetablebase>
    <el-dialog title="商品信息" class="dialog" :visible.sync="visiable" width="800px" v-dialogDrag>
      <vxetablebase :id="'CrossborderOutbound202408041641_3'" ref="goodsTable" :that='that' :isIndex='true'
        :hasexpand='false' :toolbarshow="false" :tablefixed='true' :row-config="{ isHover: true }"
        :tableData='goodsTableData' :tableCols='goodsTableCols' :isSelection="false" :isSelectColumn="false"
        style="width: 100%;  margin: 0;" v-loading="goodLoading" :summaryarry="goodSummary" :showsummary="true"
        :height="'600px'">
      </vxetablebase>
    </el-dialog>


    <el-dialog title="修改包装成本" :visible.sync="editBoxDataVisible" width="15%" v-dialogDrag style="text-align: center;">
      <div style="padding-top: 10px;">
        <el-input-number v-model="box.packCost" :precision="3" :step="1" :max="99999999.999" :min="0"
          size="medium"></el-input-number>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editBoxDataVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitBoxData">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="编辑成本" :visible.sync="editContainerDataVisible" width="20%" v-dialogDrag
      style="text-align: center;">
      <div style="padding-top: 10px;text-align: left;margin-left: 10px;">
        <div style="margin-left: 46px;">
          <span>运费</span><el-input-number v-model="container.freightFee" :precision="3" :step="1" :max="99999999.999"
            :min="0" size="small" style="width: 180px;margin-left: 14px;">
          </el-input-number>
        </div>
        <div style="margin-top: 10px;margin-left: 36px;"><span>装车费</span><el-input-number v-model="container.loadingFee"
            :precision="3" :step="1" :max="99999999.999" :min="0" size="small"
            style="width: 180px;margin-left: 10px;"></el-input-number></div>
        <div style="margin-top: 10px;"><span>卸货+上架费</span><el-input-number
            v-model="container.unloadingFee" :precision="3" :step="1" :max="99999999.999" :min="0" size="small"
            style="width: 180px;margin-left: 10px;"></el-input-number></div>
        <div style="margin-top: 10px;margin-left: 20px;"><span>上架时间</span> <el-date-picker v-model="container.shelfTime"
            type="datetime" :value-format="'yyyy-MM-dd HH:mm:ss'" placeholder="选择日期时间" style="margin-left: 10px;">
          </el-date-picker></div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editContainerDataVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitContainerData">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="操作记录" :visible.sync="logVisibile" width="40%" v-dialogDrag style="text-align: center;">
      <vxetablebase :id="'CrossborderOutbound202408041641_4'" ref="logTable" :that='that' :isIndex='true'
        :hasexpand='false' :toolbarshow="false" :tablefixed='true' :row-config="{ isHover: true }"
        :tableData='logTableData' :tableCols='logTableCols' :isSelection="false" :isSelectColumn="false"
        style="width: 100%;  margin: 0;" v-loading="logLoading" :summaryarry="logSummary" :showsummary="true"
        :height="'600px'">
      </vxetablebase>
      <template #footer>
        <my-pagination ref="pager" :total="logTotal" @page-change="logPagechange" @size-change="logSizechange" />
      </template>
    </el-dialog>

    <template #footer>
      <my-pagination ref="pager1" :total="total1" @page-change="Pagechange1" @size-change="Sizechange1"
        v-show="form.Inbound.type == 'primary'" />
      <my-pagination ref="pager2" :total="total2" @page-change="Pagechange2" @size-change="Sizechange2"
        v-show="form.Outbound.type == 'primary'" />
    </template>


    <el-dialog :title="this.form.Outbound.type == 'primary'?'导入出库箱':'导入装箱柜'" :visible.sync="importVisible" width="600px" style="height: 300px;" v-dialogDrag>
            <span>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <!-- <el-upload ref="upload" :auto-upload="false" :multiple="false" action accept=".xlsx"
                            :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload> -->

                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                          <template #trigger>
                            <el-button size="small" type="primary">选取文件</el-button>
                          </template>
                          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
        </el-dialog>

        <!-- 修改备注 -->
    <el-dialog :visible.sync="editNoteVisible" width="540px" title="修改备注" v-dialogDrag class="editNoteCss">
        <el-form ref="editNoteForm" :model="editNoteForm" label-width="80px" style="padding: 0;margin: 0;">
            <el-form-item label="备注信息" prop="remark">
                <el-input
                type="textarea"
                :rows="6"
                placeholder="请输入内容"
                v-model="editNoteForm.remark"
                maxlength="200"
                show-word-limit
                >
                </el-input>
            </el-form-item>
        </el-form>
       

         <div slot="footer" class="dialog-footer">
                <el-button @click="editNoteVisible = false">取 消</el-button>
                <el-button type="primary" @click="editNoteSet()">保 存</el-button>
            </div>
    </el-dialog>

  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import { getCrossBorderOutBoxPcPage, getCrossBorderOutBoxEntity, getCrossBorderPackingCabinetPcPage, exportPackingCabinetAsync, exportOutBoxAsync,UpdateCrossBorderOutBoxPcPage,GetCrossBorderOutBoxLogEntity,UpdateCrossBorderPackingCabinetPcPage,ImportCrossBorderPackingCabinetPcPage,ImportCrossBorderOutBoxPcPage,UpdateCrossBorderPackingCabinetRemark} from "@/api/inventory/crossBorder";
import { pickerOptions, formatTime } from '@/utils/tools'
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";

const outTableCols = [
  { istrue: true, prop: 'boxCode', label: '箱码',width: '130', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'batchNumber', label: '批次号', width: '130',sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'sort', label: '序号',width: '50', align: 'center' },
  { istrue: true, prop: 'weight', label: '重量', width: '50',sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'goodsNum', label: '商品编码数', width: '100',sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'goodsQty', label: '商品件数',width: '100',sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'goodsCost', label: '商品成本', width: '100',sortable: 'custom',formatter: (row) => {return row.goodsCost?row.goodsCost:0},  align: 'center' },
  { istrue: true, prop: 'packCost', label: '包装成本', width: '100',sortable: 'custom', formatter: (row) => {return row.packCost?row.packCost:0 }, align: 'center' },
  { istrue: true, prop: 'costPrice', label: '合计成本',width: '100', sortable: 'custom',formatter: (row) => {return row.costPrice?row.costPrice:0},  align: 'center' },
  { istrue: true, prop: 'picture', label: '图片',width: '100', type: 'images', align: 'left' },
  { istrue: true, prop: 'containerCode', label: '所属装箱柜',width: '200', sortable: 'custom', align: 'center' },
  {
    istrue: true, label: '商品信息', type: 'button', width: '100',align: 'center', btnList: [
      { label: "详情", handle: (that, row) => that.onDetail(row.boxCode) }
    ]
  },
  { istrue: true, prop: 'volume', label: '长宽高',width: '160', align: 'center' },
  { istrue: true, prop: 'volumeValue', label: '箱体积',width: '100',formatter: (row) => {return (row.volumeValue?row.volumeValue:0)+"cm³"},sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'createdTime', label: '创建时间',width: '130', sortable: 'custom', align: 'center'},
  { istrue: true, prop: 'createdUserName', label: '创建人',width: '100', sortable: 'custom', align: 'center' },
  {
        istrue: true, type: "button", label: '操作', fixed: 'right', width: '150',
        btnList: [
            { label: "编辑", handle: (that, row) => that.onEditBoxData(row)},
            { label: "操作记录", handle: (that, row) => that.onShowLog(row)},
        ]
    },
];


const inTableCols = [
  { istrue: true, prop: 'containerCode', label: '柜码', sortable: 'custom', align: 'center', width: '160' },
  { istrue: true, prop: 'weight', label: '重量', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'picture', label: '图片', type: 'images', align: 'left', width: '50' },
  {
    istrue: true, label: '出库箱信息', type: 'button', align: 'center', btnList: [
      { label: "查看", handle: (that, row) => that.getOutDetail(row) }
    ]
  },
  { istrue: true, prop: 'createdTime', label: '创建时间', sortable: 'custom', align: 'center', width: '150' },
  { istrue: true, prop: 'shelfTime', label: '上架时间', sortable: 'custom', align: 'center', width: '150' },
  { istrue: true, prop: 'createdUserName', label: '创建人', sortable: 'custom', align: 'center', width: '90'},
  { istrue: true, prop: 'goodsCost', label: '商品成本', sortable: 'custom', width: '100',formatter: (row) => {return row.goodsCost?row.goodsCost:0}, align: 'center'},
  { istrue: true, prop: 'packCost', label: '包装成本', sortable: 'custom', width: '100', formatter: (row) => {return row.packCost?row.packCost:0}, align: 'center'},
  { istrue: true, prop: 'sumCost', label: '费用总计', sortable: 'custom', width: '100', formatter: (row) => {return row.sumCost?row.sumCost:0}, align: 'center'},
  { istrue: true, prop: 'freightFee', label: '运费', sortable: 'custom', width: '100',formatter: (row) => {return row.freightFee?row.freightFee:0}, align: 'center' },
  { istrue: true, prop: 'loadingFee', label: '装车费', sortable: 'custom', width: '100',formatter: (row) => {return row.loadingFee?row.loadingFee:0}, align: 'center' },
  { istrue: true, prop: 'unloadingFee', label: '卸货+上架费', sortable: 'custom', width: '100',formatter: (row) => {return row.unloadingFee?row.unloadingFee:0}, align: 'center' },
  { istrue: true, prop: 'remark', label: '备注', sortable: 'custom',align: 'center' },
  {
        istrue: true, type: "button", label: '操作', fixed: 'right', width: '170',
        btnList: [
            { label: "编辑", handle: (that, row) => that.onEditContainerData(row)},
            { label: "操作记录", handle: (that, row) => that.onShowLog(row)},
            {label:"修改备注", handle:(that,row)=>{that.editNoteVisible = true ;that.editNoteForm = JSON.parse(JSON.stringify(row))}}

        ]
    },
];
const goodsTableCols = [
  { istrue: true, prop: 'picture', label: '商品图片', type: 'images', align: 'center' },
  { istrue: true, prop: 'goodsCode', label: '商品编码', align: 'center' },
  { istrue: true, prop: 'title', label: '商品名称', align: 'center' },
  { istrue: true, prop: 'volume', label: '长宽高', align: 'center' },
  { istrue: true, prop: 'volumeValue', label: '商品体积',formatter: (row) => {return (row.volumeValue?row.volumeValue:0)+"cm³"}, align: 'center' },
  { istrue: true, prop: 'castPrice', label: '成本', formatter: (row) => {if(row.castPrice) return row.castPrice + '￥' }, align: 'center' },
  { istrue: true, prop: 'quantity', label: '数量', align: 'center' },
  { istrue: true, prop: 'weight', label: '重量', align: 'center' },
]
const logTableCols = [
  { istrue: true, prop: 'userName', label: '操作人', align: 'center' },
  { istrue: true, prop: 'beforeContent', label: '修改前内容', align: 'center' },
  { istrue: true, prop: 'content', label: '修改后内容', align: 'center' },
  { istrue: true, prop: 'time', label: '操作时间', align: 'center' },
]

export default {
  name: "CrossborderOutbound",
  components: { MyContainer, vxetablebase },
  data() {
    return {
      that: this,
      exportType: null,
      form: {
        Outbound: {
          filter: {
            currentPage: 1,
            pageSize: 50,
            BoxCode: null,
            startDate: null,
            endDate: null,
            timerange: [],
            ContainerCode: null,
            ProCode: null,
            orderBy: null,
            isAsc: false,
          },
          type: "primary",
          loading: false,
          total: 0,
        },
        Inbound: {
          filter: {
            currentPage: 1,
            pageSize: 50,
            BoxCode: null,
            startDate: null,
            endDate: null,
            timerange: [],
            ContainerCode: null,
            ProCode: null,
            orderBy: null,
            isAsc: false,
          },
          type: "plain",
          loading: false,
          total: 0,
        }
      },
      pickerOptions,
      outTableCols,
      outTableData: [],
      inTableCols,
      inTableData: [],
      total1: 0,
      total2: 0,
      goodsTableCols,
      goodsTableData: [],
      visiable: false,
      goodLoading: false,
      tag: 0,
      outBoundSummary:null,
      inBoundSummary:null,
      goodSummary:null,
      box:{},
      editBoxDataVisible:null,
      logVisibile:false,
      logTableCols,
      logTableData:null,
      logSummary:null,
      logTotal:0,
      logLoading:false,
      logRequest:{
        logType:null,
        dataId:null,
        currentPage:1,
        pageSize:50,
      },
      container:{},
      editContainerDataVisible:false,
      importVisible: false,
      uploadLoading: false,
      fileparm: {},
      fileList: [],
      //修改备注
      editNoteVisible:false,
        editNoteForm:{
            remark:''
        }
    }
  },
  async mounted() {
    await this.getOutList();
  },
  methods: {
     //修改备注
     async editNoteSet(){

      // 调接口修改 
      let res =  await UpdateCrossBorderPackingCabinetRemark(this.editNoteForm)
      this.$message({message:res.success?'修改成功':res.msg,type:res.success?'success':'error'})
      this.getList()
      this.editNoteVisible = false
      this.editNoteForm = {}
    },
    async exportdemo(){
      let patams;
      if(!this.exportType){
        this.$message.info("请选择导出类型");
        return;
      }
      if(this.form.Inbound.type == 'primary'){ //装箱柜
        patams = {
          exportType: this.exportType,
          ...this.form.Inbound.filter
        }
        const { data, success,msg } = await exportPackingCabinetAsync(patams);
        if(!success){
          return;
        }
        this.$message.success(msg);


      }else{ //出库箱子
        patams = {
          exportType: this.exportType,
          ...this.form.Outbound.filter
        }
        const { data, success } = await exportOutBoxAsync(patams);
        if(!success){
          return;
        }
        this.$message.success(msg);

      }


    },
    async changeType(type) {
      if (type == "outbound") {
        this.form.Outbound.type = "primary";
        this.form.Inbound.type = "plain";
        this.$refs.pager2.setPage(this.form.Outbound.filter.currentPage);
        this.total2 = this.form.Outbound.total;
      } else {
        this.form.Outbound.type = "plain";
        this.form.Inbound.type = "primary";
        if (this.tag == 0) {
          await this.getInList();
          this.tag = 1;
        } else {
          this.$refs.pager1.setPage(this.form.Inbound.filter.currentPage);
          this.total1 = this.form.Inbound.total;
        }
      }
    },
    async getOutList(type) {
      if (type == 'search') {
        this.form.Outbound.filter.currentPage = 1
        this.$refs.pager2.setPage(1)
      }
      if (this.form.Outbound.filter.timerange != null && this.form.Outbound.filter.timerange.length == 0) {
        //默认给近7天时间
        this.form.Outbound.filter.startDate = dayjs().format('YYYY-MM-DD')
        this.form.Outbound.filter.endDate = dayjs().format('YYYY-MM-DD')
        this.form.Outbound.filter.timerange = [this.form.Outbound.filter.startDate, this.form.Outbound.filter.endDate]
      }
      this.form.Outbound.loading = true
      // 使用时将下面的方法替换成自己的接口
      const { data, success } = await getCrossBorderOutBoxPcPage(this.form.Outbound.filter)
      if (success) {
        this.outTableData = data.list
        this.form.Outbound.total = data.total
        this.total2 = data.total;
        if(data.summary)data.summary=this.summaryFormat(data.summary);
        this.outBoundSummary=data.summary;
        if(this.outBoundSummary['volumeValue_sum'])this.outBoundSummary['volumeValue_sum'] = this.outBoundSummary['volumeValue_sum']+"cm³";
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
      this.form.Outbound.loading = false
    },
    async getInList(type) {
      if (type == 'search') {
        this.form.Inbound.filter.currentPage = 1
        this.$refs.pager1.setPage(1)
      }
      if (this.form.Inbound.filter.timerange != null && this.form.Inbound.filter.timerange.length == 0) {
        //默认给近7天时间
        this.form.Inbound.filter.startDate = dayjs().format('YYYY-MM-DD')
        this.form.Inbound.filter.endDate = dayjs().format('YYYY-MM-DD')
        this.form.Inbound.filter.timerange = [this.form.Inbound.filter.startDate, this.form.Inbound.filter.endDate]
      }
      this.form.Inbound.loading = true
      // 使用时将下面的方法替换成自己的接口
      const { data, success } = await getCrossBorderPackingCabinetPcPage(this.form.Inbound.filter)
      if (success) {
        this.inTableData = data.list
        this.form.Inbound.total = data.total
        this.total1 = data.total;
        if(data.summary)data.summary=this.summaryFormat(data.summary);
        this.inBoundSummary=data.summary;
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
      this.form.Inbound.loading = false
    },
    async getList(type) {
      if (this.form.Outbound.type == "primary") {
        await this.getOutList(type);
      }
      if (this.form.Inbound.type == "primary") {
        await this.getInList(type);
      }
    },
    async onDetail(boxCode) {
      this.visiable = true;
      this.goodLoading = true;
      const { data, success } = await getCrossBorderOutBoxEntity({ "boxCode": boxCode });
      if (success) {
        this.goodsTableData = data.dtlList;
        let volumeValue_sum=0;
        let castPrice_sum = 0;
        let quantity_sum = 0;
        let weight_sum = 0;
        this.goodsTableData.forEach(f=>{
          volumeValue_sum = volumeValue_sum + f.volumeValue;
          castPrice_sum= castPrice_sum + f.castPrice;
          quantity_sum = quantity_sum + f.quantity;
          weight_sum = weight_sum + f.weight;
        });

        this.goodSummary={
          volumeValue_sum: this.formatNumber(volumeValue_sum)+"cm³",
          castPrice_sum: this.formatNumber(castPrice_sum)+"￥",
          quantity_sum: this.formatNumber(quantity_sum),
          weight_sum: this.formatNumber(weight_sum)
        };
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
      this.goodLoading = false;
    },
    async getOutDetail(row) {
      this.form.Outbound.filter.ContainerCode = row.containerCode;
      this.form.Outbound.filter.BoxCode = null;
      // this.form.Outbound.filter.startDate = this.form.Inbound.filter.startDate;
      // this.form.Outbound.filter.endDate = this.form.Inbound.filter.endDate;
      // this.form.Outbound.filter.timerange = this.form.Inbound.filter.timerange;
      await this.getOutList("search");
      this.changeType("outbound");
    },
    outSortchange({ order, prop }) {
      if (prop) {
        this.form.Outbound.filter.orderBy = prop
        this.form.Outbound.filter.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getOutList()
      }
    },
    InboundSortchange({ order, prop }) {
      if (prop) {
        this.form.Inbound.filter.orderBy = prop
        this.form.Inbound.filter.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getInList()
      }
    },
    //每页数量改变
    async Sizechange1(val) {
      if (this.form.Inbound.type == "primary") {
        this.form.Inbound.filter.pageSize = val;
        await this.getInList("search");
      }
    },

    async Sizechange2(val) {
      if (this.form.Outbound.type == "primary") {
        this.form.Outbound.filter.pageSize = val;
        await this.getOutList("search");
      }
    },
    //当前页改变
    async Pagechange1(val) {
      if (this.form.Inbound.type == "primary") {
        this.form.Inbound.filter.currentPage = val;
        await this.getInList();
      }
    },
    //当前页改变
    async Pagechange2(val) {
      if (this.form.Outbound.type == "primary") {
        this.form.Outbound.filter.currentPage = val;
        await this.getOutList();
      }
    },
    changeTime(e) {
      if (this.form.Outbound.type == "primary") {
        this.form.Outbound.filter.startDate = e ? e[0] : null;
        this.form.Outbound.filter.endDate = e ? e[1] : null;
      }
      if (this.form.Inbound.type == "primary") {
        this.form.Inbound.filter.startDate = e ? e[0] : null;
        this.form.Inbound.filter.endDate = e ? e[1] : null;
      }
    },
    onEditBoxData(row) {
      this.box = JSON.parse(JSON.stringify(row));
      this.editBoxDataVisible = true;
    },
    async submitBoxData() {
      const { success } = await UpdateCrossBorderOutBoxPcPage(this.box);
      if (success) {
        this.$message.success("更新成功");
        this.editBoxDataVisible = false;
        await this.getOutList("search");
      }
    },
    async onShowLog(row) {
      if (this.form.Outbound.type == "primary") {
        this.logRequest.logType = "Box-PC";
        this.logRequest.dataId = row.boxCode;
      }
      if (this.form.Inbound.type == "primary") {
        this.logRequest.logType = "Container-PC";
        this.logRequest.dataId = row.containerCode;
      }

      await this.getLogList();
    },
    async getLogList() {
      const { success, data } = await GetCrossBorderOutBoxLogEntity(this.logRequest);
      this.logVisibile = true;
      this.logLoading = true;
      if (success) {
        this.logTableData = data?.list;
        this.logTotal = data?.total;
        this.logSummary = data?.summary;
      }
      this.logLoading = false;
    },
    //每页数量改变
    async logSizechange(val) {
      this.logRequest.pageSize = val;
      this.logRequest.currentPage = 1;
      this.getLogList();
    },
    async logPagechange(val) {
      this.logRequest.currentPage = val;
      await this.getLogList();
    },
    onEditContainerData(row) {
      this.container = JSON.parse(JSON.stringify(row));
      this.editContainerDataVisible = true;
    },
    async submitContainerData() {
      const { success } = await UpdateCrossBorderPackingCabinetPcPage(this.container);
      if (success) {
        this.$message.success("更新成功");
        this.editContainerDataVisible = false;
        await this.getInList("search");
      }
    },
    summaryFormat(obj)
    {
        for(let key in obj)
        {
          obj[key] = this.formatNumber(obj[key]);
        }
        return obj;
    },
    // 格式化函数，处理千位分隔符和小数位
    formatNumber(number) {
      const absNumber = Math.abs(number);
      const options = {
        minimumFractionDigits: absNumber >= 100 ? 0 : 2,
        maximumFractionDigits: absNumber >= 100 ? 0 : 2,
      };
      return new Intl.NumberFormat('zh-CN', options).format(number);
    },
     //获取模板
     getTemplate() {
            const aLink = document.createElement("a");
            if (this.form.Outbound.type == "primary") {
            aLink.href = "/static/excel/operateManage/出库箱导入模板.xlsx";
            aLink.setAttribute('download', '出库箱导入模板.xlsx')
            }
            if (this.form.Inbound.type == "primary") {
              aLink.href = "/static/excel/operateManage/装箱柜导入模板.xlsx";
              aLink.setAttribute('download', '装箱柜导入模板.xlsx')
            }
            aLink.click();
        },
        //上传文件
        onUploadRemove(file, fileList) {
          this.fileList = []
        },
        async onUploadChange(file, fileList) {
          this.fileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
          fileList.splice(fileList.indexOf(file), 1);
          this.fileList = [];
          this.importVisible = false;
        },
        async onUploadFile(item) {
          if (!item || !item.file || !item.file.size) {
            this.$message({ message: "请先上传文件", type: "warning" });
            return false;
          }
          this.uploadLoading = true
          const form = new FormData();
          form.append("upfile", item.file);
          var res;
          if(this.form.Inbound.type == "primary")
          {
            res = await ImportCrossBorderPackingCabinetPcPage(form);
          }
          if(this.form.Outbound.type == "primary")
          {
            res = await ImportCrossBorderOutBoxPcPage(form);
          }

          if (res?.success)
            this.$message({ message: "上传成功,正在导入中...", type: "success" });
          this.uploadLoading = false
          this.importVisible = false;
          await this.getList()
        },
        onSubmitUpload() {
          if (this.fileList.length == 0) {
            this.$message({ message: "请先上传文件", type: "warning" });
            return false;
          }
          this.$refs.upload.submit();
        },
        //打开上传弹窗
        showImport() {
            this.fileList = []
            this.importVisible = true;
            this.uploadLoading = false;
            // this.$refs.upload.clearFiles();
            // this.fileList.splice(0, 1);
        },
        //上传文件
        // async uploadFile(item) {
        //     const form = new FormData();
        //     form.append("upfile", item.file);
        //     let res;
        //     if (this.form.Outbound.type == "primary") {
        //       res = await ImportCrossBorderPackingCabinetPcPage(form);
        //     }
        //     if(this.form.Inbound.type == "primary")
        //     {
        //       res;
        //     }
        //     if(res.success)
        //     {
        //       console.log("触发导入");
        //       this.$message({ message: '上传成功,正在导入中...', type: "success" });
        //     }
        // },
        //更改上传文件
        async uploadChange(file, fileList) {
            if (fileList.length == 2) {
                fileList.splice(1, 1);
                this.$message({ message: "只允许单文件导入", type: "warning" });
                return false;
            }
            this.fileList.push(file);
        },
        //移除上传文件
        uploadRemove(file, fileList) {
            this.fileList.splice(0, 1);
        },
        //提交上传文件
        submitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return;
            }
            this.$refs.upload.submit();
            this.$refs.upload.clearFiles();
            this.fileList.splice(0, 1);
            this.importVisible = false;
        },

  },
}
</script>

<style scoped lang="scss">
::v-deep .editNoteCss .el-dialog__body{
  padding:0 20px;
}
</style>