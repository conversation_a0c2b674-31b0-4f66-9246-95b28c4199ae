<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss" />
                <el-select v-model="ListInfo.isStock" placeholder="是否无法识别" class="publicCss" clearable>
                    <el-option label="是" :value="0" />
                    <el-option label="否" :value="1" />
                </el-select>
                <el-input v-model.trim="ListInfo.orderNo" placeholder="商品编码" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.orderNo" placeholder="货号" maxlength="50" clearable class="publicCss" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="exportProps" :disabled="isExport">导出历史</el-button>
                    <el-button type="primary" @click="batchApproval(true)">一键审批</el-button>
                    <el-button type="primary" @click="pullProps">拉取</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @select="select"
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="120">
                    <template #default="{ row, $index }">
                        <div style="display: flex">
                            <el-button type="text" @click="batchApproval(false, row)">审批</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { GetDingApproveGoodsCostDiffsList, ExportDingApproveGoodsCostDiffsList, CalDingApproveGoodsCostDiffs, ApproveDingApproveGoodsCostDiffsList } from '@/api/cwManager/costMaintenanceManager'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '日期', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNoInner', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'importTime', label: '商品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'planSendTime', label: '历史成本价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'timePay', label: '成本价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight', label: '差异', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderStatus', label: '状态', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'seriesName', label: '操作人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCount', label: '操作时间', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            selectList: []
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        select(val) {
            this.selectList = val
            console.log(val)
        },
        batchApproval(isBatch, row) {
            let obj = {}
            if (isBatch) {
                if (this.selectList.length == 0) {
                    this.$message.error('请选择数据')
                    return
                }
                const details = this.selectList.map(item => {
                    return {
                        yearMonthDay: item.yearMonthDay,
                        goodsCode: item.goodsCode,
                        instanceId: item.instanceId
                    }
                })
                obj = {
                    details,
                    status: 1
                }
            } else {
                obj = {
                    details: [{
                        yearMonthDay: row.yearMonthDay,
                        goodsCode: row.goodsCode,
                        instanceId: row.instanceId
                    }],
                    status: 1
                }
            }
            this.$confirm('此操作审批这些数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await ApproveFinishedPartGoodsCostDiffsList(obj)
                if (success) {
                    this.$message.success('审批成功')
                    this.getList()
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消'
                });
            });

        },
        async pullProps() {
            const { success } = await CalFinishedPartGoodsCostDiffs()
            if (!success) return
            this.$message.success('拉取成功')
            this.getList()
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await ExportFinishedPartGoodsCostDiffsList(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '快递拦截明细' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetFinishedPartGoodsCostDiffsList(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
