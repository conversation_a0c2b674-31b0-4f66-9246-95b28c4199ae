<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <div style="height: 40px; margin-top: 10px;">
        <span style="margin-right:0.4%;">
          <el-date-picker style="width: 250px" v-model="timerange" type="daterange" :clearable="false" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" :start-placeholder="'开始时间'" :end-placeholder="'结束时间'"
            :picker-options="pickerOptions"></el-date-picker>
        </span>

        <span style="margin-right:0.4%;">
          <el-select filterable v-model.trim="filter.warehouse" clearable placeholder="仓库" style="width: 200px">
            <el-option v-for="item in newWareHouseList" :key="item.name" :label="item.name" :value="item.wms_co_id" />
          </el-select>
        </span>

        <span style="margin-right:0.4%;">
          <el-select filterable v-model="filter.platform" placeholder="平台" clearable style="width: 170px" multiple
            collapse-tags>
            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </span>

        <span style="margin-right:0.4%;">
          <el-input style="width:100px;" v-model.trim="filter.goodsCode" :maxlength=100 placeholder="商品编码"
            @keyup.enter.native="onSearch" clearable />
        </span>

        <span style="margin-right:0.4%;">
          <el-select filterable v-model.trim="filter.groupId" placeholder="小组" style="width: 100px" clearable>
            <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </span>

        <span style="margin-right:0.4%;">
          <el-select filterable v-model.trim="filter.operateSpecialUserId" collapse-tags clearable placeholder="运营专员"
            style="width: 100px">
            <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </span>

        <span style="margin-right:0.4%;">
          <el-select filterable v-model.trim="filter.brandId" collapse-tags clearable placeholder="采购"
            style="width: 100px">
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </span>

        <span style="margin-left:5px;">
          <el-button type="primary" @click="onSearch" style="width: 70px;">搜索</el-button>
        </span>

        <span style="margin-left:0.4%;">
          <el-button style="margin: 0;">
            最近更新时间:
            {{ syncCreateTime }}
          </el-button>
        </span>
      </div>
    </template>

    <ces-table :id="'purchasefundsdetail202408041540'" ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
      :summaryarry='summaryarry' :showsummary='true' :tableData='list' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" :loading="listLoading" style="width:100%;height:680px;margin: 0">
    </ces-table>
    <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />

    <el-dialog title="趋势图" :visible.sync="dialogDetailtrendchart" width="80%" v-dialogDrag>
      <div>
        <span>
          <buschar v-if="detailtrendchart.visible" ref="detailtrendchartref" :analysisData="detailtrendchart.data">
          </buschar>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogDetailtrendchart = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="商品编码" :visible.sync="goodsCodeVisible" width="80%" v-dialogDrag>
      <div class="dialogTop">
        <div class="dialogTop_item">
          <div>商品编码</div>
          <div>{{ extData.goodsCode }}</div>
        </div>
        <div class="dialogTop_item">
          <div>商品名称</div>
          <div>{{ extData.goodsName }}</div>
        </div>
      </div>
      <ces-table :id="'purchasefundsdetail202408041540_2'" :that='that' :isIndex='true' :hasexpand='true' @sortchange='goodsCodeSortchange' :tableData='tableData'
        :tableCols='goodCodesTableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='goodsCodeSummaryarry'
        :showsummary='true' style="width:100%;height:400px;margin: 0">
      </ces-table>
      <my-pagination ref="pager" :total="goodCodesTotal" @page-change="goodCodesPagechange"
        @size-change="goodCodesSizechange" />
    </el-dialog>

    <el-dialog title="可售库存" :visible.sync="GoodsCountVisible" width="80%" v-dialogDrag>
      <div class="dialogTop">
        <div class="dialogTop_item">
          <div>商品编码</div>
          <div>{{ extData.goodsCode }}</div>
        </div>
        <div class="dialogTop_item">
          <div>商品名称</div>
          <div>{{ extData.goodsName }}</div>
        </div>
      </div>
      <ces-table :id="'purchasefundsdetail202408041540_3'" :that='that' :isIndex='true' :hasexpand='true' @sortchange='GoodsCountSortchange'
        :summaryarry='GoodsCountSummaryarry' :showsummary='true' :tableData='GoodsCountTableData'
        :tableCols='GoodsCountTableCols' :isSelection="false" :isSelectColumn="false"
        style="width:100%;height:400px;margin: 0">
      </ces-table>
      <my-pagination ref="pager" :total="GoodsCountTotal" @page-change="GoodsCountPagechange"
        @size-change="GoodsCountSizechange" />
    </el-dialog>

    <el-dialog title="近7日销量" :visible.sync="DayVisible" width="80%" v-dialogDrag>
      <div class="dialogTop">
        <div class="dialogTop_item">
          <div>商品编码</div>
          <div>{{ extData.goodsCode }}</div>
        </div>
        <div class="dialogTop_item">
          <div>商品名称</div>
          <div>{{ extData.goodsName }}</div>
        </div>
      </div>
      <ces-table :id="'purchasefundsdetail202408041540_4'" :that='that' :isIndex='true' :hasexpand='true' :tableData='DayTableData' :tableCols='DayTableCols'
        :summaryarry='DaySummaryarry' :showsummary='true' :isSelection="false" :isSelectColumn="false"
        style="width:100%;height:400px;margin: 0">
      </ces-table>
    </el-dialog>

    <el-dialog title="资金合计" :visible.sync="SumVisible" width="80%" v-dialogDrag>
      <div class="dialogTop">
        <div class="dialogTop_item">
          <div>商品编码</div>
          <div>{{ extData.goodsCode }}</div>
        </div>
        <div class="dialogTop_item">
          <div>商品名称</div>
          <div>{{ extData.goodsName }}</div>
        </div>
      </div>
      <ces-table :id="'purchasefundsdetail202408041540_5'" :that='that' :isIndex='true' :hasexpand='true' @sortchange='SumSortchange' :tableData='SumTableData'
        :summaryarry='SumSummaryarry' :showsummary='true' :tableCols='SumTableCols' :isSelection="false"
        :isSelectColumn="false" style="width:100%;height:400px;margin: 0">
      </ces-table>
      <my-pagination ref="pager" :total="SumTotal" @page-change="SumPagechange" @size-change="SumSizechange" />
    </el-dialog>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from "dayjs";
import {
  getPurchaseFundsDetail,
  getPurchaseFundsDetailChart,
  getPurchaseFundsGoodsCodeDialog,
  getPurchaseFundsTotalFundsDialog,
  getPurchaseFundsAvailableInventoryDialog,
  getPurchaseFundsNearly7DaysDialog
} from '@/api/inventory/purchase'
import { getDirectorGroupList, getList as getshopList, getDirectorList } from '@/api/operatemanage/base/shop'
import { getAllWarehouse, getAllProBrand } from '@/api/inventory/warehouse'
import { platformlist } from '@/utils/tools'
import buschar from '@/components/Bus/buschar'
import { formatTime, formatPlatform, pickerOptions } from "@/utils/tools";

const tableCols = [
  { istrue: true, prop: 'yearMonthDay', label: '日期', width: '70', sortable: 'custom' },
  { istrue: true, prop: 'purImageUrl', label: '商品图片', width: '70', type: 'images' },
  { istrue: true, prop: 'goodsCode', label: '商品编码', width: '80', sortable: 'custom', type: 'click', handle: (that, row) => that.openGoodsCodeDialog(row, true) },
  { istrue: true, prop: 'goodsName', label: '商品名称', width: '210', sortable: 'custom' },
  { istrue: true, prop: 'auditState', label: '类型', width: '75', sortable: 'custom', formatter: (row) => row.auditState == 1 ? '申报' : '领取' },
  { istrue: true, prop: 'applyTime', label: '时间', width: '135', sortable: 'custom', tipmesg: '申请/领取时间' },
  { istrue: true, prop: 'warehouseName', label: '仓库', width: '95' },
  { istrue: true, prop: 'platForm', label: '平台', width: '70', formatter: (row) => platformlist.filter(item => item.value == row.platForm)[0].label },
  { istrue: true, prop: 'groupName', label: '小组', width: '70' },
  { istrue: true, prop: 'operateSpecialUserName', label: '专员', width: '70' },
  { istrue: true, prop: 'brandName', label: '采购', width: '70' },
  { istrue: true, prop: 'goodsCodeCost', label: '成本价', width: '80', sortable: 'custom', tipmesg: '商品编码成本价' },
  { istrue: true, prop: 'goodsCodeCount', label: '数量', width: '80', sortable: 'custom', tipmesg: '商品编码数量', type: 'click', handle: (that, row) => that.openGoodsCountDialog(row, true) },
  { istrue: true, prop: 'stockQty', label: '在仓', width: '80', sortable: 'custom', tipmesg: '在仓数量' },
  { istrue: true, prop: 'onPassageCount', label: '在途', width: '80', sortable: 'custom', tipmesg: '在途数量' },
  { istrue: true, prop: 'stockMoney', label: '在仓资金', width: '85', sortable: 'custom' },
  { istrue: true, prop: 'onPassageMoney', label: '在途资金', width: '85', sortable: 'custom' },
  { istrue: true, prop: 'salesLastSevenDays', label: '近七日销量', width: '85', sortable: 'custom', type: 'click', handle: (that, row) => that.open7DayDialog(row, true) },
  { istrue: true, prop: 'totalMoney', label: '资金合计', width: '90', sortable: 'custom', type: 'click', handle: (that, row) => that.openSumDialog(row, true) },
  { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", formatter: (row) => '趋势图', type: 'click', handle: (that, row, column) => that.showchart(row, column) },
];

const goodCodesTableCols = [
  { istrue: true, prop: 'platform', label: '平台', formatter: (row) => platformlist.filter(item => item.value == row.platform)[0].label },
  { istrue: true, prop: 'shopName', label: '店铺', },
  { istrue: true, prop: 'proCode', label: '宝贝ID', },
  { istrue: true, prop: 'categoryName', label: '类目', },
  {
    istrue: true, prop: 'profit3', label: '毛三利润', sortable: 'custom', type: 'html', formatter: (row) => {
      if (row.profit3GreaterThanAverage == 1) {
        return "<span style='color:red'>" + row.profit3 + "</span>"
      } else if (row.profit3GreaterThanAverage == 0) {
        return "<span style='color:green'>" + row.profit3 + "</span>"
      } else {
        return "<span style='color:black'>" + row.profit3 + "</span>"
      }
    }
  },
  {
    istrue: true, prop: 'profit3Rate', label: '毛三利润率', sortable: 'custom', type: 'html', formatter: (row) => {
      if (row.profit3RateGreaterThanAverage == 1) {
        return "<span style='color:red'>" + row.profit3Rate + '%' + "</span>"
      } else if (row.profit3RateGreaterThanAverage == 0) {
        return "<span style='color:green'>" + row.profit3Rate + '%' + "</span>"
      } else {
        return "<span style='color:black'>" + row.profit3Rate + '%' + "</span>"
      }
    }
  },
  {
    istrue: true, prop: 'profit32', label: '净利润', sortable: 'custom', type: 'html', formatter: (row) => {
      if (row.profit32GreaterThanAverage == 1) {
        return "<span style='color:red'>" + row.profit32 + "</span>"
      } else if (row.profit32GreaterThanAverage == 0) {
        return "<span style='color:green'>" + row.profit32 + "</span>"
      } else {
        return "<span style='color:black'>" + row.profit32 + "</span>"
      }
    }
  },
  { istrue: true, prop: 'goodsName', label: '宝贝名称', },
  { istrue: true, prop: 'groupName', label: '运营组', },
  { istrue: true, prop: 'operateSpecialUserName', label: '运营专员', },
  { istrue: true, prop: 'userName', label: '运营助理', },
]
const GoodsCountTableCols = [
  { istrue: true, prop: 'warehouseName', label: '仓库', },
  { istrue: true, prop: 'groupName', label: '运营组', },
  { istrue: true, prop: 'operateSpecialUserName', label: '运营专员', },
  { istrue: true, prop: 'auditState', label: '类型', formatter: (row) => row.auditState == 1 ? '申报' : '领取' },
  { istrue: true, prop: 'availableQuantity', label: '可售库存', sortable: 'custom' },
  { istrue: true, prop: 'onPassageCount', label: '采购在途数', sortable: 'custom' },
]

const DayTableCols = [
  { istrue: true, prop: 'platForm', label: '平台', formatter: (row) => platformlist.filter(item => item.value == row.platForm)[0].label },
  { istrue: true, prop: 'groupName', label: '运营组', },
  { istrue: true, prop: 'salesVolume', label: '销量', },
  { istrue: true, prop: 'salesRatio', label: '占比', formatter: (row) => (row.salesRatio.toFixed(2) + '%') },
]
const SumTableCols = [
  { istrue: true, prop: 'warehouseName', label: '仓库' },
  { istrue: true, prop: 'groupName', label: '运营组', },
  { istrue: true, prop: 'goodsCodeCost', label: '成本价', },
  { istrue: true, prop: 'availableQuantity', label: '可售数量' },
  { istrue: true, prop: 'saleableFunds', label: '可售资金' },
  { istrue: true, prop: 'onPassageCount', label: '在途数量' },
  { istrue: true, prop: 'onPassageMoney', label: '在途资金' },
  { istrue: true, prop: 'totalMoney', label: '合计资金' },
]

export default {
  name: 'purchasefundsdetail',
  components: { MyContainer, cesTable, buschar },
  data() {
    return {
      SumVisible: false,
      SumTotal: null,
      SumTableData: [],
      SumTableCols,
      DayTotal: null,
      DayTableCols,
      DayTableData: [],
      GoodsCountTableData: [],
      goodCodesTableCols,
      pickerOptions: pickerOptions,
      sels: [],
      platformlist: platformlist,//平台
      listLoading: false,
      newWareHouseList: [],//仓库选择器数据
      brandlist: [],//采购选择器数据
      directorlist: [],//运营专员选择器数据
      directorGroupList: [],//运营小组选择器数据
      syncCreateTime: '',//最近更新时间
      that: this,//赋值
      list: [],//表格数据
      total: null,
      tableCols: tableCols,
      summaryarry: {},
      detailtrendchart: { visible: false, title: "", data: {} },
      dialogDetailtrendchart: false,
      timerange: [],
      pager: { orderBy: '', isAsc: false },
      filter: {
        startDate: null,
        endDate: null,
        warehouse: null,
        goodsCode: null,
        groupId: null,
        operateSpecialUserId: null,
        brandId: null,
        platform: [],
      },
      pageLoading: false,
      dialogQueryInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'profit3',
        isAsc: false,
        startTime: null,
        endTime: null,
        goodsCode: null,
        groupId: null,
        operateSpecialUserId: null,
        brandId: null,
        yearMonthDay: null
      },
      goodsCodeVisible: false,
      tableData: [],
      goodCodesTotal: null,
      extData: {
        goodsCode: null,
        goodsName: null,
      },
      GoodsCountTableCols,
      GoodsCountVisible: false,
      GoodsCountTotal: null,
      DayVisible: false,
      goodsCodeSummaryarry: {},
      GoodsCountSummaryarry: {},
      DaySummaryarry: {},
      SumSummaryarry: {},
    };
  },

  async mounted() {
    let end = new Date();
    let start = new Date();
    end.setDate(start.getDate());
    start.setDate(start.getDate() - 30);
    this.timerange = [formatTime(start, "YYYY-MM-DD"), formatTime(end, "YYYY-MM-DD")]
    await this.init();
    await this.onSearch()
  },

  methods: {
    SumPagechange(val) {
      this.dialogQueryInfo.currentPage = val;
      this.openSumDialog(this.dialogQueryInfo, false)
    },
    SumSizechange(val) {
      this.dialogQueryInfo.currentPage = 1;
      this.dialogQueryInfo.pageSize = val;
      this.openSumDialog(this.dialogQueryInfo, false)
    },
    async openSumDialog(row, isClear) {
      if (isClear) {
        this.clear('totalMoney', row)
      }
      this.dialogQueryInfo.yearMonthDay = dayjs(row.yearMonthDay).format('YYYY-MM-DD')
      this.dialogQueryInfo.goodsCode = row.goodsCode
      this.dialogQueryInfo.startTime = dayjs(row.yearMonthDay).format('YYYY-MM-DD')
      this.dialogQueryInfo.endTime = dayjs(row.yearMonthDay).format('YYYY-MM-DD')
      const { data, success } = await getPurchaseFundsTotalFundsDialog(this.dialogQueryInfo)
      if (success) {
        console.log('asdkjhaskjdhaskjdhasjk');
        this.SumTableData = data.list
        this.SumTotal = data.total
        this.SumSummaryarry = data.summary
        this.SumVisible = true
      }
    },
    async open7DayDialog(row, isClear) {
      if (isClear) {
        this.clear('availableQuantity', row)
      }
      this.dialogQueryInfo.yearMonthDay = dayjs(row.yearMonthDay).format('YYYY-MM-DD')
      this.dialogQueryInfo.goodsCode = row.goodsCode
      this.dialogQueryInfo.startTime = dayjs(row.yearMonthDay).format('YYYY-MM-DD')
      this.dialogQueryInfo.endTime = dayjs(row.yearMonthDay).format('YYYY-MM-DD')
      const { data, success } = await getPurchaseFundsNearly7DaysDialog(this.dialogQueryInfo)
      if (success) {
        this.DayTableData = data.list
        // this.DayTotal = data.total
        this.DaySummaryarry = data.summary
        this.DayVisible = true
      }
    },
    GoodsCountPagechange(val) {
      this.dialogQueryInfo.currentPage = val;
      this.openGoodsCountDialog(this.dialogQueryInfo, false)
    },
    GoodsCountSizechange(val) {
      this.dialogQueryInfo.currentPage = 1;
      this.dialogQueryInfo.pageSize = val;
      this.openGoodsCountDialog(this.dialogQueryInfo, false)
    },
    async openGoodsCountDialog(row, isClear) {
      if (isClear) {
        this.clear('availableQuantity', row)
      }
      console.log(row, 'row');
      this.dialogQueryInfo.groupId = row.groupId
      this.dialogQueryInfo.yearMonthDay = dayjs(row.yearMonthDay).format('YYYY-MM-DD')
      this.dialogQueryInfo.goodsCode = row.goodsCode
      this.dialogQueryInfo.startTime = dayjs(row.yearMonthDay).format('YYYY-MM-DD')
      this.dialogQueryInfo.endTime = dayjs(row.yearMonthDay).format('YYYY-MM-DD')
      const { data, success } = await getPurchaseFundsAvailableInventoryDialog(this.dialogQueryInfo)
      if (success) {
        this.GoodsCountTableData = data.list
        this.GoodsCountTotal = data.total
        this.GoodsCountSummaryarry = data.summary
        this.GoodsCountVisible = true
      }
    },
    clear(orderby, row) {
      this.dialogQueryInfo = {
        currentPage: 1,
        pageSize: 50,
        orderBy: orderby,
        isAsc: false,
        startTime: null,
        endTime: null,
        goodsCode: null,
        groupId: null,
        operateSpecialUserId: null,
        brandId: null,
        yearMonthDay: null
      }
      this.extData = {
        goodsCode: row.goodsCode,
        goodsName: row.goodsName,
      }
    },
    //每页数量改变
    goodCodesSizechange(val) {
      this.dialogQueryInfo.currentPage = 1;
      this.dialogQueryInfo.pageSize = val;
      this.openGoodsCodeDialog(this.dialogQueryInfo, false)
    },
    //当前页改变
    goodCodesPagechange(val) {
      this.dialogQueryInfo.currentPage = val;
      this.openGoodsCodeDialog(this.dialogQueryInfo, false)
    },
    async openGoodsCodeDialog(row, isClear) {
      if (isClear) {
        this.clear('profit3', row)
      }
      this.dialogQueryInfo.goodsCode = row.goodsCode
      this.dialogQueryInfo.yearMonthDay = dayjs(row.yearMonthDay).format('YYYY-MM-DD')
      this.dialogQueryInfo.startTime = dayjs(row.yearMonthDay).format('YYYY-MM-DD')
      this.dialogQueryInfo.endTime = dayjs(row.yearMonthDay).format('YYYY-MM-DD')
      this.dialogQueryInfo.groupId = this.filter.groupId ? this.filter.groupId : null
      this.dialogQueryInfo.operateSpecialUserId = this.filter.operateSpecialUserId ? this.filter.operateSpecialUserId : null
      this.dialogQueryInfo.brandId = this.filter.brandId ? this.filter.brandId : null
      const { data, success } = await getPurchaseFundsGoodsCodeDialog(this.dialogQueryInfo)
      if (success) {
        this.tableData = data.list
        this.goodCodesTotal = data.total
        this.goodsCodeSummaryarry = data.summary
        this.goodsCodeVisible = true
      }
    },
    //仓库汇总跳转明细页
    async updateFilterMonthDay(name, timerange) {
      //汇总页跳转到明细页,清除并重新赋值
      this.filter.warehouse = null
      this.filter.goodsCode = null
      this.filter.platform = null
      this.filter.groupId = null
      this.filter.operateSpecialUserId = null
      this.filter.brandId = null
      this.timerange = [formatTime(timerange, "YYYY-MM-DD"), formatTime(timerange, "YYYY-MM-DD")]
      let warehouseee = this.newWareHouseList.find(obj => obj.name === name)?.wms_co_id;
      if (warehouseee === 88888888) {
        warehouseee = null
      }
      this.filter.warehouse = warehouseee;
      await this.onSearch()
    },
    //平台汇总跳转明细页
    async performanFilterMonthDay(names, timerange) {
      //汇总页跳转到明细页,清除并重新赋值
      this.filter.platform = null
      this.filter.goodsCode = null
      this.filter.warehouse = null
      this.filter.groupId = null
      this.filter.operateSpecialUserId = null
      this.filter.brandId = null
      this.timerange = [formatTime(timerange, "YYYY-MM-DD"), formatTime(timerange, "YYYY-MM-DD")]
      if (names === 1) {
        names = [1, 8, 9, 10]
        this.filter.platform = names
      } else {
        this.filter.platform = [names]
      }
      if (names === 88888888 || names === null) {
        names = null
        this.filter.platform = null
      }
      await this.onSearch()
    },
    async onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist();
    },
    async getlist() {
      this.sels = [],
        this.filter.startDate = null;
      this.filter.endDate = null;
      if (this.timerange) {
        this.filter.startDate = this.timerange[0];
        this.filter.endDate = this.timerange[1];
      }
      this.listLoading = true
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = {
        ...pager,
        ...page,
        ...this.filter
      }
      const { data } = await getPurchaseFundsDetail(params)
      this.list = data.list
      this.total = data.total;
      this.summaryarry = data.summary;
      this.syncCreateTime = data.summary.syncCreateTime
      this.listLoading = false
    },
    //获取选择器数据
    async init() {
      //仓库
      var res = await getAllWarehouse();
      this.newWareHouseList = res.data.filter((x) => { return x.name.indexOf('代发') < 0; });
      //采购
      var res1 = await getAllProBrand();
      this.brandlist = res1.data.map(item => { return { value: item.key, label: item.value }; });
      //运营小组
      var res2 = await getDirectorGroupList({});
      this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);
      //运营专员
      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });
    },
    //趋势图
    async showchart(row) {
      let goodsCode = row.goodsCode
      this.filter.startDate = null;
      this.filter.endDate = null;
      if (this.timerange != []) {
        this.filter.startDate = this.timerange[0];
        this.filter.endDate = this.timerange[1];
      }
      const params = {
        startDate: this.filter.startDate,
        endDate: this.filter.endDate,
        goodsCode: goodsCode,
      };
      const res = await getPurchaseFundsDetailChart(params);
      res.series.map((item) => {
        item.itemStyle = {
          "normal": {
            "label": {
              "show": true,
              "position": "top",
              "textStyle": {
                "fontSize": 14
              }
            }
          }
        }
        item.emphasis = {
          "focus": "series"
        }
        item.smooth = false;
      })
      this.detailtrendchart.visible = true;
      this.detailtrendchart.data = res
      this.detailtrendchart.title = res.legend[0]
      this.$nextTick(() => {
        this.$refs.detailtrendchartref.initcharts();
      });
      this.dialogDetailtrendchart = true;
    },
    //排序查询
    async sortchange(column) {
      if (!column.order) {
        this.filter.orderBy = 'yearMonthDay';
        this.filter.isAsc = false;
      } else {
        this.filter.orderBy = column.prop
        this.filter.isAsc = column.order.indexOf("descending") == -1 ? true : false
        this.getlist();
      }
    },
    goodsCodeSortchange({ order, prop }) {
      if (prop) {
        this.dialogQueryInfo.orderBy = prop
        this.dialogQueryInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.openGoodsCodeDialog(this.dialogQueryInfo, false)
      }
    },
    GoodsCountSortchange({ order, prop }) {
      if (prop) {
        this.dialogQueryInfo.orderBy = prop
        this.dialogQueryInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.openGoodsCountDialog(this.dialogQueryInfo, false)
      }
    },
    SumSortchange({ order, prop }) {
      if (prop) {
        this.dialogQueryInfo.orderBy = prop
        this.dialogQueryInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.openSumDialog(this.dialogQueryInfo, false)
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.dialogTop {
  display: flex;
  margin-bottom: 10px;

  .dialogTop_item {
    display: flex;
    width: 50%;

    div {
      width: 50%;
      height: 40px;
      line-height: 40px;
      border: 1px solid rgb(239, 239, 239);

      &:nth-child(1) {
        background-color: rgb(239, 239, 239);
        width: 20%;
      }

      &:nth-child(2) {
        width: 80%;
      }

    }
  }
}
</style>
