<template>
    <container>
        <el-tabs v-model="activeName" style="height: 94%;">
            <el-tab-pane  lazy label="账单明细" name="first" style="height: 100%;">
                <ManicureOrders ref="ManicureOrders"></ManicureOrders>
            </el-tab-pane>
            <el-tab-pane  lazy label="导入大马美甲" name="Manicure" style="height: 100%;">
                <Manicure ref="Manicure"></Manicure>
            </el-tab-pane>
        </el-tabs>
    </container>
</template>

<script>

import container from "@/components/my-container";
import ManicureOrders from '@/views/financial/Manicure_orders.vue';
import Manicure from '@/views/financial/Manicure.vue';

export default { 
    name: 'BigHorse',
    components: { container,ManicureOrders,Manicure},
    data() {
        return {
            that: this,
            activeName:'first',
        };
    },
};
</script>
