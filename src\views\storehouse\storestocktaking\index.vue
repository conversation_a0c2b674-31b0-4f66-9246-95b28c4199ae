<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="数据汇总" name="first1" style="height: 100%">
        <alldata ref="alldata" style="height: 100%"></alldata>
      </el-tab-pane>
      <el-tab-pane label="超时未备注" name="first2" style="height: 100%">
        <outtime ref="outtime" style="height: 100%"></outtime>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import alldata from "./alldata.vue";
import outtime from "./outtime.vue";

export default {
  name: "StoreStockTakingIndex",
  components: {
    MyContainer, alldata, outtime
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },
  async mounted() {

  },
  methods: {
    
  },
};
</script>

<style lang="scss" scoped></style>
