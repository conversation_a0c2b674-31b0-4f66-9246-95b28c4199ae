<template>
  <my-container v-loading="pageLoading">
    <el-tabs v-model="activeName" style="height:94%">
      <el-tab-pane label="售前" name="tab91" style="height: 100%;" lazy >
        <groupinquirsstatistics :filter="filter" ref="groupinquirsstatistics1" style="height: 100%;" 
          :partInfo="infoBool" ></groupinquirsstatistics>
      </el-tab-pane>
      <el-tab-pane label="售后" name="tab92" style="height: 100%;" lazy >
        <groupinquirsstatisticssh :filter="filter" ref="pddofflinemessagestatistics1" style="height: 100%;"
          :partInfo="infoBool" ></groupinquirsstatisticssh>
      </el-tab-pane>
      <el-tab-pane label="一体" name="tab93" style="height: 100%;" lazy >
        <groupyitiinquirsstatistics :filter="filter" ref="groupinquirsstatisticssh1" style="height: 100%;"
          :partInfo="infoBool" ></groupyitiinquirsstatistics>
      </el-tab-pane>
      <el-tab-pane label="离线留言" name="tab94" style="height: 100%;" lazy >
        <pddofflinemessagestatistics :filter="filter" ref="groupyitiinquirsstatistics1" style="height: 100%;"
          :partInfo="infoBool" ></pddofflinemessagestatistics>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import groupinquirsstatistics from '@/views/customerservice/pdd/pddgroupinquirsstatisticsnew';
import groupinquirsstatisticssh from '@/views/customerservice/pdd/sh/pddgroupinquirsstatisticsshnew';
import groupyitiinquirsstatistics from '@/views/customerservice/pdd/pddgroupyitiinquirsstatisticsnew';
import pddofflinemessagestatistics from '@/views/customerservice/pdd/pddofflinemessagestatistics';

export default {
  name: "pddGroupStaticsIndex",
  components: {
    MyContainer,
    groupinquirsstatistics,
    groupinquirsstatisticssh,
    groupyitiinquirsstatistics,
    pddofflinemessagestatistics,
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "tab91",
      filter: {
      },
      infoBool: false,//是否包含离组
    }
  },
  async mounted() {

  },
  methods: {
    async onSearch() {
      this.$nextTick(() => {})
    }
  }
}
</script>