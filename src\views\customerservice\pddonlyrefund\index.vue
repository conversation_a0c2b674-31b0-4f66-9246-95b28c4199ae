<template>
    <my-container v-loading="pageLoading" style="height: 100%">
        <el-tabs v-model="activeName" style="height: 94%">
            <el-tab-pane label="仅退款订单" name="first1" style="height: 100%">
                <pddonlyrefundorder ref="pddonlyrefundorder" style="height: 100%"></pddonlyrefundorder>
            </el-tab-pane>
            <el-tab-pane label="退款接待统计" name="first2" style="height: 100%" lazy>
                <pddonlyrefundreceive ref="pddonlyrefundreceive" style="height: 100%"></pddonlyrefundreceive>
            </el-tab-pane>
            <el-tab-pane label="人员退款接待统计" name="first4" style="height: 100%" lazy>
                <pddonlyrefundreceiveuser ref="pddonlyrefundreceiveuser" style="height: 100%"></pddonlyrefundreceiveuser>
            </el-tab-pane>
            <el-tab-pane label="分组退款接待统计" name="first5" style="height: 100%" lazy>
                <pddonlyrefundreceivegroup ref="pddonlyrefundreceivegroup" style="height: 100%"></pddonlyrefundreceivegroup>
            </el-tab-pane>
            <el-tab-pane label="退款聊天记录" name="first3" style="height: 100%" lazy>
                <pddonlyrefundchat ref="pddonlyrefundchat" style="height: 100%"></pddonlyrefundchat>
            </el-tab-pane>
            <el-tab-pane label="仅退款责任" name="first6" style="height: 100%">
                <PddonlyrefundorderZrList ref="PddonlyrefundorderZrList" style="height: 100%"></PddonlyrefundorderZrList>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
  
<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import pddonlyrefundorder from "@/views/customerservice/pddonlyrefund/pddonlyrefundorder.vue";
import pddonlyrefundreceive from "@/views/customerservice/pddonlyrefund/pddonlyrefundreceive.vue";
import pddonlyrefundchat from "@/views/customerservice/pddonlyrefund/pddonlyrefundchat.vue";
import pddonlyrefundreceiveuser from "@/views/customerservice/pddonlyrefund/pddonlyrefundreceiveuser.vue";
import pddonlyrefundreceivegroup from "@/views/customerservice/pddonlyrefund/pddonlyrefundreceivegroup.vue";

import PddonlyrefundorderZrList from "@/views/customerservice/pddonlyrefund/PddonlyrefundorderZrList.vue";

export default {
    name: "tailorlossindex",
    components: {
        cesTable, MyContainer, MyConfirmButton, pddonlyrefundorder, pddonlyrefundreceive, pddonlyrefundchat, pddonlyrefundreceiveuser,pddonlyrefundreceivegroup,PddonlyrefundorderZrList
    },
    data() {
        return {
            that: this,
            pageLoading: false,
            activeName: "first1",
        };
    },
    async mounted() {

    },
    methods: {
    },
};
</script>
  
<style lang="scss" scoped></style>
  