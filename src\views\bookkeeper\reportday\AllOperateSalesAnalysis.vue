<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" style="width: 250px;margin-right: 5px;" :clearable="false" :value-format="'yyyy-MM-dd'"
          @change="changeTime">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.styleCode" placeholder="系列编码" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'allOperateSalesAnalysis202302031421'" :tablekey="'allOperateSalesAnalysis202302031421'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :cstmExportFunc="onExport" :summaryarry='summaryarry' :showsummary='true' style="width: 100%; margin: 0"
      v-loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import dayjs from 'dayjs'
import { pageProCodeStyleCodeGoodsCountList, exportProCodeStyleCodeGoodsCountList } from '@/api/bookkeeper/reportdayV2'
import { formatTime } from "@/utils/tools";

const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'fsYmdDate', label: '日期', formatter: (row) => formatTime(row.fsYmdDate, 'YYYY-MM-DD') },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'proCodeStyleCode', label: '系列编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'platform', exportField: 'platformName', label: '平台', formatter: (row) => row.platformName, },
  { istrue: true, fixed: 'left', label: '小组头像',  width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'groupId',width: '70', exportField: 'groupName', label: '运营小组', formatter: (row) => row.groupName, type: 'custom',type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, },
  { istrue: true, fixed: 'left', label: '专员头像',  width: '70',type:'ddAvatar',ddInfo:{type:2,prop:'operateSpecialUserId'} },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'operateSpecialUserId',width: '70', exportField: 'operateSpecialUserName', label: '运营专员', formatter: (row) => row.operateSpecialUserName,type:'ddTalk',ddInfo:{type:2,prop:'operateSpecialUserId',name:'operateSpecialUserName'}, },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'sum_OperateSpecialUser', label: '专员销售数量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'sum_Group', label: '小组销售数量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'sum_Platform', label: '平台销售数量', },
  { width: 'auto', align: 'center', prop: 'sum_Platform_Rate', label: '平台销售占比整个系列', formatter: (row) => row.sum_Platform_Rate ? row.sum_Platform_Rate + '%' : '', },
  { width: 'auto', align: 'center', prop: 'sum_Group_Rate', label: '小组占比整个系列', formatter: (row) => row.sum_Group_Rate ? row.sum_Group_Rate + '%' : '', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'sum_ProCodeStyleCode', label: '系列销售数量', },
]
export default {
  name: "AllOperateSalesAnalysis",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      that: this,//this指向
      ListInfo: {
        currentPage: 1,//当前页
        pageSize: 50,//每页数量
        orderBy: null,//排序字段
        isAsc: false,//是否升序
        startTime: null,//开始时间
        endTime: null,//结束时间
        styleCode: null,//系列编码
      },
      timeRanges: [],//时间范围
      tableCols,//表头
      tableData: [],//列表数据
      total: 0,//总数
      loading: false,//加载中
      summaryarry: {},//汇总
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    //导出数据
    async exportProps() {
      let that = this
      that.$refs.table.setExportCols()
    },
    async onExport(opt) {
      const { data } = await exportProCodeStyleCodeGoodsCountList({ ...this.ListInfo, ...opt })
    },
    //获取列表
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
      }
      if (this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      const replaceArr = ['styleCode']
      this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
      this.loading = true
      const { data, success } = await pageProCodeStyleCodeGoodsCountList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    //排序
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 180px;
    margin-right: 5px;
  }
}
</style>
