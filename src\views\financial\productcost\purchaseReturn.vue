<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker class="publicCss" v-model="ListInfo.yearMonth" type="month" format="yyyyMM"
          value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
        <el-input v-model.trim="ListInfo.buyNo" placeholder="采购单号" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
      </div>
    </template>
    <vxetablebase :id="'productReportTx202302031421'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0"
      :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="display: flex; align-items: baseline; height: 75px;margin-top: 10px;">
        <el-date-picker style="width: 150px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonth" type="month"
          format="yyyyMM" value-format="yyyyMM" placeholder="请选择月份" :clearable="false"></el-date-picker>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { importPurchaseOrderMonthBackAsync, pagePurchaseOrderMonthBackAsync } from '@/api/financial/productcost'
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'buyNo', label: '采购单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'purchaseBackTime', label: '退货日期', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouseName', label: '操作仓储方', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'qty', label: '退货数量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'price', label: '单价', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'amount', label: '金额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdUserName', label: '导入人', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '导入时间', },
]
export default {
  name: "purchaseReturn",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      yearMonth: null,//导入时间
      dialogVisible: false,//导入弹窗
      fileList: [],//上传文件列表
      uploadLoading: false,//上传按钮loading
      fileparm: {},//上传文件参数
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        yearMonth: null,//导入时间
        goodsCode: null,//商品编码
        buyNo: null,//采购单号
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("yearMonth", this.yearMonth);
      var res = await importPurchaseOrderMonthBackAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (!this.yearMonth) {
        this.$message({ message: "请选择月份", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.yearMonth = null
      this.dialogVisible = true;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (!this.ListInfo.yearMonth) {
        this.ListInfo.yearMonth = dayjs().format('YYYYMM')
      }
      this.loading = true
      const { data, success } = await pagePurchaseOrderMonthBackAsync(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 220px;
    margin-right: 5px;
  }
}
</style>
