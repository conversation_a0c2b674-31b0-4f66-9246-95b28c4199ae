<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                    <el-select filterable v-model="filter.platform" @change="onchangeplatform" clearable placeholder="平台"
                        style="width:120px;">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;">
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode"></el-option>
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;">
                    <el-input style="width: 200px" v-model="filter.serialNumberOrder" placeholder="订单号" clearable/>
                </el-button>

                <el-button type="primary" @click="onSearch()">查询</el-button>
                <el-button type="primary" @click="onExport()">导出</el-button>

            </el-button-group>
        </template>

        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            @select='selectchange' :isSelection='false' :isSelectColumn='false' :tableData='list' :tableCols='tableCols'
            :showsummary='true' :summaryarry='summaryarry' :loading="listLoading" />
       
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

    </my-container>
</template>
<script>
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import { GetSpecialAmountPageList,ExportSpecialAmountList } from '@/api/monthbookkeeper/financialDetail'
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
import { platformlist } from "@/utils/tools";
const tableCols = [
{ istrue: true, prop: 'computeTime', label: '数据时间', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'platformName', label: '平台', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'shopCode', label: '店铺编码', sortable: 'custom', width: '120'},
    { istrue: true, prop: 'shopName', label: '店铺名称', width: '300' },
    { istrue: true, prop: 'orderNo', label: '订单号', sortable: 'custom', width: '300' },
    { istrue: true, prop: 'amountIncome', label: '收入', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amountPaid', label: '支出', sortable: 'custom', width: '120' },
];
export default {
    name: 'SpecialOrderAmount',
    components: {MyContainer,  cesTable, MyConfirmButton },
    data() {
        return {
            that: this,
            filter:{},
            list: [],
            tableCols: tableCols,
            pager: { OrderBy: "orderNo", IsAsc: false },
            summaryarry: {},
            total: 0,
            sels: [],
            selids: [],
            listLoading: false,
            pageLoading: false,


            dialogVisibleSyj: false,
            dialogLoadingSyj: false,
            fileList: [],
            improtForm: {
            },

            platformlist:platformlist,
            shopList:[],
        }
    },
    mounted() {
        this.onSearch();
    },
    methods: {
        async onchangeplatform(val) {
            const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
        },
        onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }
            this.listLoading = true
            const res = await GetSpecialAmountPageList(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res.data?.total ?? 0
            const data = res.data?.list
            data?.forEach(d => {
                d._loading = false
            })
            this.list = data
            this.summaryarry=res.data?.summary
            
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport() {
            let pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }
            let res = await ExportSpecialAmountList(params);
            if (!res?.data) {
                return
            }
            this.$message({ message: "正在后台导出中，请点击头像-在下载管理中查看", type: "success" });
        },
    }
}
</script>
