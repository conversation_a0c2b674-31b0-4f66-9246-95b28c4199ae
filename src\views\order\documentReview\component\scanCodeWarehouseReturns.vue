<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss" />
        <el-input v-model.trim="ListInfo.expressNo" placeholder="物流编号" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
        <el-button type="primary" @click="onUserSettings">用户设置</el-button>
      </div>
    </template>
    <vxetablebase ref="table" :id="'scanCodeWarehouseReturns202502131750'"
      :tablekey="'scanCodeWarehouseReturns202502131750'" :loading="loading" :that="that" :is-index="true"
      :hasexpand="true" :tablefixed="true" :has-seq="false" :border="true" :table-data="data.list"
      :table-cols="tableCols" :is-selection="false" :is-select-column="true" :is-index-fixed="false"
      style="width: 100%; margin: 0;" height="100%" :showsummary="data.summary ? true : false"
      :summaryarry="data.summary" @sortchange="sortchange" />
    <template #footer>
      <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="用户设置" :visible.sync="userSettingsVisible" width="35%" v-dialogDrag>
      <div style="height: 400px;">
        <scanCodeWuserSettings ref="scanCodeWuserSettings" v-if="userSettingsVisible" />
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/WmsExpressScan/'
import { mergeTableCols } from '@/utils/getCols'
import scanCodeWuserSettings from "@/views/order/documentReview/component/scanCodeWuserSettings.vue";
export default {
  name: "scanCodeWarehouseReturns",
  components: {
    MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, scanCodeWuserSettings
  },
  data() {
    return {
      userSettingsVisible: false,
      api,
      platformlist,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        summarys: [],
        expressNo: '',
        startDate: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
        endDate: dayjs().format('YYYY-MM-DD'),
      },
      data: {},
      chatProp: {
        chatDialog: false, // 趋势图弹窗
        chatTime: null, // 趋势图时间
        chatLoading: true, // 趋势图loading
        data: [], // 趋势图数据
      },
      timeRanges: [],
      tableCols: [],
      tableData: [],
      total: 0,
      loading: true,
      pickerOptions,
      isExport: false
    }
  },
  async mounted() {
    await this.getCol();
    await this.getList()
  },
  methods: {
    onUserSettings() {
      this.userSettingsVisible = true
    },
    // 导出数据,这里前端可以封装一个方法
    async exportProps() {
      this.isExport = true
      await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
        this.isExport = false
      })
    },
    async getCol() {
      const { data, success } = await request.post(`${this.api}GetColumns`)
      if (success) {
        data.forEach(item => {
          if (item.prop == 'orderNoInner') {
            item.type = 'orderLogInfo'
            item.orderType = 'orderNoInner'
          }
          if (item.prop == 'proCode') {
            item.type = 'html'
            item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
          }
        })
        this.tableCols = mergeTableCols(data)
        this.ListInfo.summarys = data
          .filter((a) => a.summaryType)
          .map((a) => {
            return { column: a["sort-by"], summaryType: a.summaryType };
          });
      }
    },
    async getList(type) {
      if (type === "search") {
        this.ListInfo.currentPage = 1;
        this.$refs.pager.setPage(1);
      }
      this.loading = true;
      // 使用时将下面的方法替换成自己的接口
      try {
        const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
        if (success) {
          this.data = data;
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.loading = false;
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 230px;
    margin: 0 5px 0 0;
  }
}
</style>
