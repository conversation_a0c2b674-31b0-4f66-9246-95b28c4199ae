<template>
    <my-container v-loading="pageLoading">
    <!--下单发货-->
    <template>
        <el-form class="ad-form-query" :model="addOrderForm" ref="addOrderForm" :rules="addOrderFormRules" label-position="right" label-width="100px">
            <el-row>
                <el-col :xs="24" :sm="24" :md="24" :lg="19" :xl="19">
                    <el-form-item label="已选任务编号:">
                        <span v-for="(item, index) in selTaskIdSpanList" :key="index" v-html="item"></span>
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="5" :xl="5">
                    <el-form-item label="">
                        <el-button @click="onAddressSet" type="text">发货地址维护
                        </el-button>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :hidden="true">
                <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                    <el-form-item label="收货人" prop="receiverName">
                        <el-input v-model="addOrderForm.receiverName" auto-complete="off" />
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                    <el-form-item label="收货电话" prop="receiverPhone">
                        <el-input v-model="addOrderForm.receiverPhone" auto-complete="off" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                    <el-form-item label="拿样方式" prop="isZt">
                        <el-radio v-model="addOrderForm.isZt" label="1">仓库自提</el-radio>
                        <el-radio v-model="addOrderForm.isZt" label="0">快递寄样</el-radio>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="calculateUnit" label="核算单位">
                                <el-select v-model="addOrderForm.calculateUnit" placeholder="请选择核算单位" style="width:100%;">
                                    <el-option v-for="item in calculateUnitlist" :key="item" :label="item"
                                        :value="item" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
            <el-row>
                <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="addOrderForm.isZt!=1">
                    <el-form-item prop="warehouse" label="自提仓库">
                        <el-select v-model="addOrderForm.warehouse" placeholder="请选择自提仓库" style="width:100%;">
                            <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" :hidden="addOrderForm.isZt!=0">
                    <el-form-item label="详细地址" prop="receiverAddressAllInfo">
                        <el-select v-model="addOrderForm.receiverAddressAllInfo" placeholder="选择详细地址" style="width:100%;" @change="receiverAddressSelChange">
                            <el-option v-for="item in receiverAddressList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                    <el-form-item label="收货省" prop="receiverStateCode">
                        <el-select v-model="addOrderForm.receiverStateCode" placeholder="收货省" style="width:100%;" @change="receiverStateChange">
                            <el-option v-for="item in receiverStateList" :key="item.code" :label="item.name" :value="item.code" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                    <el-form-item label="收货市" prop="receiverCityCode">
                        <el-select v-model="addOrderForm.receiverCityCode" placeholder="收货市" style="width:100%;" @change="receiverCityChange">
                            <el-option v-for="item in receiverCityList" :key="item.code" :label="item.name" :value="item.code" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                    <el-form-item label="收货区" prop="receiverDistrictCode">
                        <el-select v-model="addOrderForm.receiverDistrictCode" placeholder="收货区" style="width:100%;">
                            <el-option v-for="item in receiverDistrictList" :key="item.code" :label="item.name" :value="item.code" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" :hidden="true">
                    <el-form-item label="详细地址" prop="receiverAddress">
                        <el-input v-model="addOrderForm.receiverAddress" auto-complete="off" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                    <el-form-item label="备注" prop="remark">
                        <el-input v-model="addOrderForm.remark" type="textarea" auto-complete="off" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </template>
     <!--下单发货-->
    <template>
        <el-row>
            <el-card class="box-card" style="width:100% ;height: 45px;overflow: hidden;">
                <div slot="header" class="clearfix">
                    <span style="float: left; padding: 0" >商品明细</span>
                    <el-button @click="onSelctOrderGoods()" style="float: right; padding: 3px 0" type="text">添加商品明细</el-button>
                </div>
            </el-card>
            <el-card class="box-card" style="width:100% ;height: 300px;overflow: auto;">
                <el-table :data="addOrderForm.orderGoods">
                    <el-table-column label="序号" width="50">
                        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                    </el-table-column>
                    <el-table-column prop="goodsCode" width="150" label="商品编码" />
                    <el-table-column prop="goodsName" label="商品名称" />
                    <el-table-column prop="goodsPrice" width="100" label="单价" v-if="false" />
                    <el-table-column prop="goodsQty" width="150" label="数量">
                        <template slot-scope="scope">
                            <el-input-number v-model="scope.row.goodsQty" :min="1" :max="100000000" placeholder="数量" :precision="0" @change="addOrderFormGoodsQtyChange(scope.row)">
                            </el-input-number>
                        </template>
                    </el-table-column>
                    <el-table-column prop="goodsAmount" width="100" label="总额" v-if="false" />
                    <el-table-column lable="操作" width="100">
                        <template slot-scope="scope">
                            <el-button type="danger" @click="onDelDtlGood(scope.$index)">删除 <i class="el-icon-remove-outline"></i>
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </el-row>
    </template>
     <!--下单发货-->
    <el-dialog title="选择商品编码" :visible.sync="orderGoodschoiceVisible" width='85%' height='500px' v-dialogDrag :close-on-click-modal="false"  :append-to-body="true" >
        <goodschoice :ischoice="true" ref="orderGoodschoice" style="z-index:10000;height:500px" />
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="orderGoodschoiceVisible = false">取 消</el-button>
                <el-button type="primary" @click="onQuerenOrderGoods()">确 定</el-button>
            </span>
        </template>
    </el-dialog>
     <!--下单发货-->
    <el-dialog title="收货地址维护" :visible.sync="dialogAddressVisible" width='50%' height='400px' v-dialogDrag :close-on-click-modal="false" :append-to-body="true" >
            <template>
                <el-form class="ad-form-query" :model="addAddressForm" ref="addAddressForm" :rules="addAddressFormRules" label-position="right" label-width="100px">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item label="收货人" prop="receiverName">
                                <el-input v-model="addAddressForm.receiverName" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item label="收货电话" prop="receiverPhone">
                                <el-input v-model="addAddressForm.receiverPhone" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item label="收货省" prop="receiverStateCode">
                                <el-select v-model="addAddressForm.receiverStateCode" placeholder="收货省" style="width:100%;" @change="receiverStateChange2">
                                    <el-option v-for="item in receiverStateList" :key="item.code" :label="item.name" :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item label="收货市" prop="receiverCityCode">
                                <el-select v-model="addAddressForm.receiverCityCode" placeholder="收货市" style="width:100%;" @change="receiverCityChange2">
                                    <el-option v-for="item in receiverCityList2" :key="item.code" :label="item.name" :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item label="收货区" prop="receiverDistrictCode">
                                <el-select v-model="addAddressForm.receiverDistrictCode" placeholder="收货区" style="width:100%;">
                                    <el-option v-for="item in receiverDistrictList2" :key="item.code" :label="item.name" :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="详细地址" prop="receiverAddress">
                                <el-input v-model="addAddressForm.receiverAddress" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <ces-table ref="addresstable" :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="false" style="height:350px" :tableData='addressList' :tableCols='addressTableCols' :loading="addressListLoading">
                            </ces-table>
                        </el-col>
                    </el-row>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogAddressVisible = false">取 消</el-button>
                    <my-confirm-button type="submit" :loading="dialogAddAddressSubmitLoding" @click="onAddAddressSave">
                        提交
                    </my-confirm-button>
                </span>
            </template>
    </el-dialog>
   </my-container>
</template>
<script> 
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import uploadfile from '@/views/media/shooting/uploadfile' 
 
 
import {getVedioTaskOrderAddressList, saveVedioTaskOrderAddress,getCityAllData ,
     deleteVedioTaskOrderAddress,vedioTaskAddOrderSaveCheckTaskIds,vedioTaskAddOrderSave} from '@/api/media/vediotask';
import goodschoice from "@/views/base/goods/goods3.vue"; 
 

const addressTableCols = [
    { istrue: true, prop: 'receiverAllAddress', label: '地址' },
    { istrue: true, prop: 'receiverName', label: '收货人', width: '120' },
    { istrue: true, prop: 'receiverPhone', label: '收货电话', width: '120' },
    {
        istrue: true, type: 'button', label: '操作', width: '60',
        btnList: [
            { label: "删除", handle: (that, row) => that.onAddressDelete(row) }
        ]
    }
];
export default {
   components: { MyContainer,uploadfile,goodschoice,MyConfirmButton}, 
   props:{ 
        selids:{ type: Array, default:()=>{ return []} }, 
        warehouselist:{ type: Array, default:()=>{return []} }, 
        orderType:{ type: Number, default: 1},//20230311 封装模块 1.包装设计下单 
        closedlg:{ type: Function, default:()=>{} },  
    }, 
   data() {
       return {
        that:this,
        receiverStateList: [],//省
            receiverCityList: [],//市
            receiverDistrictList: [],//区
            receiverCityList2: [],//市
            receiverDistrictList2: [],//区
            addOrderForm: {
                TaskIds: "",
                receiverName: "",
                receiverPhone: "",
                receiverStateCode: "",
                receiverCityCode: "",
                receiverDistrictCode: "",
                receiverState: "",
                receiverCity: "",
                receiverDistrict: "",
                receiverAddress: "",
                isZt: null,
                warehouse: null,
                remark: "",
                receiverAddressAllInfo: "",
                orderGoods: []
            },
            calculateUnitlist: ['国内', '1688选品中心' , '跨境'],
            selTaskIdSpanList: [],
            orderGoodschoiceVisible: false,
            addOrderFormRules: { 
                isZt: [{ required: true, message: '请输入是否自提', trigger: 'blur' }],
                calculateUnit: [{ required: true, message: '请选择核算单位', trigger: 'blur' }],
            },
    
            pageLoading:false,
            receiverAddressList: [],
            dialogAddressVisible: false,
            addressTableCols: addressTableCols,
            addressListLoading: false,
            dialogAddAddressSubmitLoding: false,
            addAddressForm: {
                receiverName: "",
                receiverPhone: "",
                receiverStateCode: null,
                receiverCityCode: null,
                receiverDistrictCode: null,
                receiverAddress: "",
            },
            addressList: [],
            addAddressFormRules: {
                receiverName: [{ required: true, message: '请输入收货人', trigger: 'blur' }],
                receiverPhone: [{ required: true, message: '请输入收货电话', trigger: 'blur' }],
                receiverStateCode: [{ required: true, message: '请输入收货省', trigger: 'blur' }],
                receiverCityCode: [{ required: true, message: '请输入收货市', trigger: 'blur' }],
                receiverDistrictCode: [{ required: true, message: '请输入成收货区', trigger: 'blur' }],
                receiverAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
            },
        
       };
   }, 
   async mounted() {
        //省
        await this.getCity(0, 1);
        await this.getAddressList();
        this.selids.forEach(item => {
            this.addOrderForm.TaskIds += (item.toString() + ";");
            this.selTaskIdSpanList.push((item.toString() + ";&nbsp"));
        });
   }, 
   methods: {
        //下单发货表单
        async getCity(parentCode, type) { 
            let res = await getCityAllData({ parentCode: parentCode });
            if (res?.success) {
                if (type == 1) {
                    this.receiverStateList = res?.data;
                }
                if (type == 2) {
                    this.receiverCityList = res?.data;
                }
                if (type == 3) {
                    this.receiverDistrictList = res?.data;
                }
            }
        },
        async receiverStateChange() {
            this.receiverCityList = [];
            this.receiverDistrictList = [];
            this.addOrderForm.receiverCityCode = "";
            this.addOrderForm.receiverDistrictCode = "";
            var parentCode = this.addOrderForm.receiverStateCode;
            if (parentCode) {
                this.getCity(parentCode, 2);
            }
        },
        async receiverCityChange() {
            this.receiverDistrictList = [];
            this.addOrderForm.receiverDistrictCode = "";
            var parentCode = this.addOrderForm.receiverCityCode;
            if (parentCode) {
                this.getCity(parentCode, 3);
            }
        },
        async getCity2(parentCode, type,) {
            const res = await getCityAllData({ parentCode: parentCode });
            if (res?.success) {
                if (type == 1) {
                    this.receiverStateList = res?.data;
                }
                if (type == 2) {
                    this.receiverCityList2 = res?.data;
                }
                if (type == 3) {
                    this.receiverDistrictList2 = res?.data;
                }
            }
        },
        async receiverStateChange2() {
            this.receiverCityList2 = [];
            this.receiverDistrictList2 = [];
            this.addAddressForm.receiverCityCode = "";
            this.addAddressForm.receiverDistrictCode = "";
            var parentCode = this.addAddressForm.receiverStateCode;
            if (parentCode) {
                this.getCity2(parentCode, 2);
            }
        },
        async receiverCityChange2() {
            this.receiverDistrictList2 = [];
            this.addAddressForm.receiverDistrictCode = "";
            var parentCode = this.addAddressForm.receiverCityCode;
            if (parentCode) {
                this.getCity2(parentCode, 3);
            }
        },
        //下单发货
        async onAddOrder(selids) { 
            var param  = [];
            param.push(this.orderType);
            param = param.concat(this.selids); 
            const res = await vedioTaskAddOrderSaveCheckTaskIds(param);
            if (!res?.success) {
                return;
            }
            this.addOrderForm.TaskIds = "";
            this.addOrderForm.orderGoods = [];
            this.selTaskIdSpanList = [];
            selids.forEach(taskid => {
                this.addOrderForm.TaskIds += (taskid.toString() + ";");
                this.selTaskIdSpanList.push((taskid.toString() + ";&nbsp"));
            }); 
        },
        //关闭下单发货界面
        async closeAddOrder() {
            this.dialogAddOrderSubmitLoding = false;
        },
        //下单发货：选择商品
        async onSelctOrderGoods() {
            this.orderGoodschoiceVisible = true;
            this.$refs.orderGoodschoice.removeSelData();
        },
        //下单发货：选择商品确定
        async onQuerenOrderGoods() {
            var choicelist = await this.$refs.orderGoodschoice.getchoicelist();
            if (choicelist && choicelist.length > 0) {
                //已存在的不添加
                var temp = this.addOrderForm.orderGoods;
                var isNew = true;
                choicelist.forEach((item) => {
                    isNew = true;
                    temp.forEach(old => {
                        if (old.goodsCode == item.goodsCode) {
                            isNew = false;
                        }
                    });
                    if (isNew) {
                        this.addOrderForm.orderGoods.push({
                            goodsCode: item.goodsCode, goodsName: item.goodsName,
                            shopCode: item.shopId, shopName: item.shopName, proCode: item.shopStyleCode,
                            goodsPrice: item.costPrice ?? 0,
                            goodsQty: 1,
                            goodsAmount: item.costPrice ?? 0
                        });
                    }
                });
                this.orderGoodschoiceVisible = false;
            }
        },
        //移除明细
        async onDelDtlGood(index) {
            this.addOrderForm.orderGoods.splice(index, 1);
        },
        async addOrderFormGoodsQtyChange(row) {
            row.goodsAmount = (row.goodsQty * (row.goodsPrice ?? 0)).toFixed(2);
        },
        //新增编辑提交时验证
        addOrderFormValidate: function () {
            let isValid = false
            this.$refs.addOrderForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        //下单发货-保存
        async onAddOrderSave() {
            if (this.addOrderForm.TaskIds == "" || this.addOrderForm.orderGoods.length <= 0) {
                this.$message({ message: '下单发货信息不完整，请填写', type: "warning" });
                return;
            }
            if (this.addOrderForm.isZt == 1) {
                if (this.addOrderForm.warehouse == "" || this.addOrderForm.warehouse == null) {
                    this.$message({ message: '拿样方式=仓库自提时必须填写自提仓库', type: "warning" });
                    return;
                }
            }
            else {
                this.addOrderForm.warehouse = null;
            }
            if (this.addOrderForm.isZt == 0) {
                if (this.addOrderForm.receiverAddressAllInfo == "" || this.addOrderForm.receiverAddressAllInfo == null) {
                    this.$message({ message: '拿样方式=快递寄样时必须选择详细地址', type: "warning" });
                    return;
                }
                if (this.addOrderForm.receiverName == "" || this.addOrderForm.receiverPhone == "") {
                    this.$message({ message: '收货人信息错误，请刷新后重试', type: "warning" });
                    return;
                }
                if (this.addOrderForm.receiverAddress == "") {
                    this.$message({ message: '下单发货地址错误，请刷新后重试', type: "warning" });
                    return;
                }
            }
            else {
                this.addOrderForm.receiverName = "";
                this.addOrderForm.receiverPhone = "";
                this.addOrderForm.receiverState = "";
                this.addOrderForm.receiverStateCode = "";
                this.addOrderForm.receiverCity = "";
                this.addOrderForm.receiverCityCode = "";
                this.addOrderForm.receiverDistrict = "";
                this.addOrderForm.receiverDistrictCode = "";
                this.addOrderForm.receiverAddress = "";
                this.addOrderForm.receiverAddressAllInfo = "";
            }
            this.addOrderForm.orderType = this.orderType;
            this.dialogAddOrderSubmitLoding = true;
            this.dialogAddOrderLoading = true;
            const res = await vedioTaskAddOrderSave(this.addOrderForm);
            this.dialogAddOrderLoading = false;
            if (res?.success) {
                if (this.addOrderForm.isZt == 1) {
                    this.$message({ message: '操作成功，请关注钉钉审批流程', type: "success" });
                }
                else {
                    this.$message({ message: '操作成功，请关注钉钉审批流程', type: "success" });
                } 
                this.closedlg();
                this.dialogAddOrderVisible = false;
                this.dialogAddOrderSubmitLoding = false;
            } 
        },
        async dialogOrderDtlColsed() {
            this.xdfhmainlist = [];
            this.xdfhdtllist = [];
        },
        
        async onxdfhmainCellClick(row) {
            this.xdfhmainlist.forEach(
                f => {
                    if (f.shootingTaskOrderId == row.shootingTaskOrderId) {
                        this.xdfhdtllist = f.dtlEntities;
                    }
                }
            );
        },
        async receiverAddressSelChange() {
            this.addressList.forEach(f => {
                if (f.vedioTaskOrderAddressId == this.addOrderForm.receiverAddressAllInfo && this.addOrderForm.receiverAddressAllInfo != "") {
                    this.addOrderForm.receiverStateCode = f.receiverStateCode;
                    this.addOrderForm.receiverCityCode = f.receiverCityCode;
                    this.addOrderForm.receiverDistrictCode = f.receiverDistrictCode;
                    this.addOrderForm.receiverAddress = f.receiverAddress;
                    if (f.receiverName)
                        this.addOrderForm.receiverName = f.receiverName;
                    if (f.receiverPhone)
                        this.addOrderForm.receiverPhone = f.receiverPhone;

                    return;
                }
            });
            ;
        },
        async getAddressList() {
            this.addressList = [];
            this.addressListLoading = true;
            var ret = await getVedioTaskOrderAddressList();
            this.addressListLoading = false;
            this.addressList = ret?.data;
            this.receiverAddressList = [];
            if (ret?.success) {
                this.receiverAddressList = ret.data?.map(item => { return { value: item.vedioTaskOrderAddressId, label: item.receiverAllAddress }; });
            }
        },
        async onAddressSet() {
            this.dialogAddressVisible = true;
            this.getAddressList();
        },
        async onAddAddressSave() {
            this.dialogAddAddressSubmitLoding = true;
            var ret = await saveVedioTaskOrderAddress(this.addAddressForm);
            this.dialogAddAddressSubmitLoding = false;
            if (ret?.success) {
                this.addAddressForm = {
                    receiverName: "",
                    receiverPhone: "",
                    receiverStateCode: null,
                    receiverCityCode: null,
                    receiverDistrictCode: null,
                    receiverAddress: "",
                };
                this.getAddressList();
            }
        },
        async onAddressDelete(row) {
            this.$confirm("是否确定删除, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                this.addressListLoading = true;
                var ret = await deleteVedioTaskOrderAddress({ vedioTaskOrderAddressId: row.vedioTaskOrderAddressId });
                this.addressListLoading = false;
                this.getAddressList();
            });
        },
    },
};
</script>
