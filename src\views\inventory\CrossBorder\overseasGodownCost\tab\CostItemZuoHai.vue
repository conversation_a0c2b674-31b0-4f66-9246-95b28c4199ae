<template>
    <MyContainer>
      <template #header>
        <div class="top">
          <el-date-picker v-model="queryInfo.timeRanges" type="daterange" unlink-panels range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
            style="width: 250px;margin-right: 5px;" :clearable="true" :value-format="'yyyy-MM-dd'" @change="changeTime">
          </el-date-picker>
  
          <el-button-group>
            <inputYunhan :key="'1'" :keys="'one'" :width="'150px'" ref="" :inputt.sync="queryInfo.reference_no"
              v-model.trim="queryInfo.reference_no" placeholder="订单号或仓库单号" :clearable="true" @callback="callback"
              title="订单号或仓库单号" @entersearch="entersearch">
            </inputYunhan>
          </el-button-group>
  
  
          <el-select v-model="queryInfo.ft_name" clearable filterable multiple collapse-tags placeholder="请选择费用类型" class="publicCss">
          <el-option v-for="item in costType" :key="item.key" :label="item.value" :value="item.key">
          </el-option>
        </el-select>
        <el-select v-model="queryInfo.finance_name" clearable filterable multiple collapse-tags placeholder="请选择财务类型"class="publicCss">
          <el-option v-for="item in financeFeeNameType" :key="item.key" :label="item.value" :value="item.key">
          </el-option>
        </el-select>
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" @click="exportList" v-if="checkPermission('overseasGodown_Cost_export')">导出</el-button>
          <!-- <el-button type="primary" @click="startImport">导入</el-button> -->
          <!-- <el-button type="primary" @click="deleteList">区间删除</el-button> -->
          <el-button type="primary" @click="BatchHandle('delete')">批量删除</el-button>
          <el-button type="primary" @click="BatchHandle('edit')">批量修改财务类型</el-button>
        </div>
      </template>
  
      <template>
        <vxetablebase :id="'overseasGodownCost202409270424'" :tablekey="'overseasGodownCost202409270424'" :tableData='tableData'
          @select="checkboxRangeEnd" :isSelection="true" :tableCols='tableCols' @sortchange='sortchange'
          :loading='loading' :border='true' :that="that" ref="vxetable" :showsummary='true' :summaryarry="summaryarry"
          @summaryClick='onsummaryClick'>
        </vxetablebase>
        <!-- <vxetablebase :id="'overseasGodownCost20240906'" ref="vxetable" :that='that' :isIndex='true' :hasexpand='true'
          :tablefixed='true' :toolbarshow="false" @select="checkboxRangeEnd" @sortchange='sortchange' 
          :tableData='tableData' :tableCols='tableCols' :isSelection="true"  :tree-config="{}"
          :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="listLoading" :showsummary="true"
          :summaryarry="onsummaryClick" :height="'100%'">
        </vxetablebase> -->
      </template>
  
  
  
      <template #footer>
        <my-pagination ref="pager" :total="total" @get-page="getList" />
      </template>
  
  
      <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
        <span>
          <buschar ref="buschar" v-if="buscharDialog.visible" :analysisData="buscharDialog.data"
            :loading="buscharDialog.loading"></buschar>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="buscharDialog.visible = false">关闭</el-button>
        </span>
      </el-dialog>
      <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
        <div style="height: 75px;">
          <el-date-picker style="width: 200px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay" type="date"
            placeholder="选择日期" :clearable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
          </el-date-picker>
          <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
            accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
            <template #trigger>
              <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
              @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
          </el-upload>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </el-dialog>
  
  
      <el-dialog title="删除数据" :visible.sync="deleteDialogVisible" width="25%" v-dialogDrag :close-on-click-modal="false">
  
        <el-date-picker style="width: 320px" v-model="dateRange" type="datetimerange" format="yyyy-MM-dd"
          value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
          :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
  
        <span slot="footer" class="dialog-footer">
          <el-button @click="deleteTimeList()">删除</el-button>
          <el-button @click="deleteDialogVisible = false">关闭</el-button>
        </span>
      </el-dialog>
      <editform :selids="selids" :currentRow="currentRow" :isMultiple="isMultiple" @getList="getList" ref="editform" :isSync="false" :thirdPlatform="type"  :FeeNameList="financeFeeNameType"></editform>
      <el-dialog title="操作日志" :visible.sync="logVisible" :center="true" width="60%"   v-dialogDrag >
            <viewLog :currentRow="currentRow"  ref="viewLog" ></viewLog>
      </el-dialog>
    </MyContainer>
  </template>
  
  <script>
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
  import MyContainer from "@/components/my-container";
  import { getFeeNames, getCostBillPageList, exportCost, costBillSumChart, importCostBill, deleteImportCostBill ,deleteCostBill} from '@/api/kj/cost.js';
  import inputYunhan from "@/components/Comm/inputYunhan";
  import { pickerOptions } from '@/utils/tools'
  import buschar from '@/components/Bus/buschar'
  import dayjs from 'dayjs';
  import editform from '../components/editform.vue'
  import viewLog from '../components/viewLog.vue'
  
  const tableCols = [
    { istrue: true, width: '30', type: "checkbox", },
    { istrue: true, sortable: 'custom', prop: 'warehouse_order', label: '仓库单号' },
    { istrue: true, sortable: 'custom', prop: 'reference_no', label: '订单号' },
    { istrue: true, sortable: 'custom', prop: 'bill_weight', label: '重量' },
    { istrue: true, sortable: 'custom', prop: 'bill_weight_range', label: '重量区间' },
    { istrue: true, sortable: 'custom', prop: 'ft_name', label: '费用类型' },
    { istrue: true, sortable: 'custom', prop: 'finance_name', label: '财务类型',type: 'clickLink',align:'center',
      style: (that, row) => {  return { color: '#409EFF', cursor: 'pointer' }  },
      handle: (that, row) => {that.$refs.editform.visible = true;that.isMultiple  = false;that.currentRow = row },
    },
    { istrue: true, summaryEvent: true, sortable: 'custom', prop: 'bi_amount', label: '金额(美元)' },
    { istrue: true, summaryEvent: true, sortable: 'custom', prop: 'bi_amount_rmb', label: '金额(人民币)' },
    { istrue: true, sortable: 'custom', prop: 'bi_chargeable_time', label: '记账时间' },
    { istrue: true, sortable: 'custom', prop: 'update_time', label: '更新时间' },
    {type:'button',label:'操作',width:'150px',btnList:[
    {label:"查看日志", handle:(that,row)=>{that.currentRow = row;that.logVisible = true;that.$nextTick(()=>{that.$refs.viewLog.getList('search')})}}]}
  ];
  
  export default {
    name: 'overseasGodown',
    components: { vxetablebase, MyContainer, inputYunhan, buschar, inputYunhan,editform,viewLog },
    props: {
      type: {
        type: String,
        required: true
      },
      name: {
        type: String,
        required: true
      },
      financeFeeNameType:{
        type: Array,
        required: true
      }
    },
    data() {
      return {
        logVisible:false,
        that: this,
        isMultiple:false,
        currentRow:{},
        queryInfo: {
          timeRanges: [],
          reference_no: null,
          ft_name: null,
          thirdPlatform: this.type,
          page: 1,
          pageSize: 50,
          orderBy: null,
          isAsc: false,
          startTime: '',
          endTime: ''
        },
        costType: [],
        tableData: [],
        tableCols: tableCols,
        summaryarry: [],
        loading: false,
        total: 0,
        pickerOptions,
        buscharDialog: { visible: false, title: "", data: {}, loading: false },
        deleteDialogVisible: false,
        dateRange: [],
        dialogVisible: false,//导入弹窗
        fileList: [],//上传文件列表
        uploadLoading: false,//上传按钮loading
        fileparm: {},//上传文件参数
        defaultDate: new Date(),
        pickerOptions: {
          disabledDate(date) {
            // 设置禁用日期
            const start = new Date("1970/1/1");
            const end = new Date("9999/12/31");
            return date < start || date > end;
          },
        },
        yearMonthDay: null,//导入日期
        selids: [],
      };
    },
    async mounted() {
      await this.getCostTypeList()
      await this.getList()
    },
    methods: {
      async getList(type) {
        if (type == 'search') {
          this.queryInfo.page = 1
          this.$refs.pager.setPage(1)
        }
        this.selids = []
        let page = this.$refs.pager.getPager()
        this.queryInfo.page = page.currentPage
        this.queryInfo.pageSize = page.pageSize
        this.loading = true
        const { data, summary, total } = await getCostBillPageList(this.queryInfo)
        this.loading = false
        this.total = total
        this.tableData = data;
        this.summaryarry = summary
      },
      async exportList() {
        this.loading = true
        const { data } = await exportCost(this.queryInfo)
        this.loading = false
        const aLink = document.createElement("a");
        let blob = new Blob([data], { type: "application/vnd.ms-excel;charset=utf-8" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', this.name + '-' + dayjs().format('YYYYMMDD') + '.xlsx')
        aLink.click()
      },
      async getCostTypeList() {
        const { data } = await getFeeNames({
        thirdPlatform: this.type,
      })
        this.costType = data;
      },
  
      sortchange({ order, prop }) {
        if (prop) {
          this.queryInfo.orderBy = prop
          this.queryInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
      async changeTime(e) {
        this.queryInfo.startDate = e ? e[0] : null
        this.queryInfo.endDate = e ? e[1] : null
      },
      async onsummaryClick(property) {
        this.queryInfo.startDate = null;
        this.queryInfo.endDate = null;
        if (this.queryInfo.timeRanges) {
          this.queryInfo.startDate = this.queryInfo.timeRanges[0];
          this.queryInfo.endDate = this.queryInfo.timeRanges[1];
        }
        var pager = this.$refs.pager.getPager();
        const params = { ...pager, ...this.pager, ...this.queryInfo };
        params.column = property;
        let that = this;
        that.listLoading = true;
        that.buscharDialog.loading = true;
        const res = await costBillSumChart(params).then(res => {
          that.buscharDialog.loading = false;
          that.buscharDialog.data = res.data;
          that.buscharDialog.title = res.data.legend[0];
        });
        that.listLoading = false;
        that.buscharDialog.visible = true;
        await that.$refs.buschar.initcharts();
  
      },
      //导入弹窗
      startImport() {
        this.fileList = []
        this.yearMonthDay = null
        this.dialogVisible = true;
      },
      //上传文件
      onUploadRemove(file, fileList) {
        this.fileList = []
      },
      async onUploadChange(file, fileList) {
        this.fileList = fileList;
      },
      onUploadSuccess(response, file, fileList) {
        fileList.splice(fileList.indexOf(file), 1);
        this.fileList = [];
        this.dialogVisible = false;
      },
      async onUploadFile(item) {
        if (!item || !item.file || !item.file.size) {
          this.$message({ message: "请先上传文件", type: "warning" });
          return false;
        }
        this.uploadLoading = true
        const form = new FormData();
        form.append("upfile", item.file);
        form.append("YearMonthDay", dayjs(this.yearMonthDay).format('YYYY-MM-DD'));
        form.append("PlatForm", this.type);
        var res = await importCostBill(form);
        if (res?.isSuccess) {
          this.$message({ message: "上传成功,正在导入中...", type: "success" });
          this.uploadLoading = false
          this.dialogVisible = false;
          await this.getList()
        } else {
          this.$message.error('导入失败');
          this.uploadLoading = false;
        }
  
      },
      onSubmitUpload() {
        if (!this.yearMonthDay) {
          this.$message({ message: "请选择日期", type: "warning" });
          return false;
        }
        if (this.fileList.length == 0) {
          this.$message({ message: "请先上传文件", type: "warning" });
          return false;
        }
        this.$refs.upload.submit();
      },
      //删除数据弹窗
      deleteList() {
        this.deleteDialogVisible = true;
      },
  
      async deleteTimeList() {
        // 检查时间范围是否为空
        if (!this.dateRange[0] || !this.dateRange[1]) {
          this.$message({ type: 'error', message: '请先选择开始和结束日期' });
          return;
        }
  
        var params = {
          startDate: this.dateRange[0],
          endDate: this.dateRange[1],
          thirdPlatform: this.type
        };
  
        this.$confirm('确认删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const res = await deleteImportCostBill(params);
          if (!res?.success) {
            this.$message({ type: 'success', message: '删除成功!' });
            this.deleteDialogVisible = false;
            await this.getList()
          }
  
        });
      },
      async callback(val) {
        this.queryInfo.reference_no = val;
      },
      async entersearch(val) {
        this.getList();
      },
      checkboxRangeEnd(row) {
        this.selids = row.map((item) => item.id);
      },
      //批量处理
      async BatchHandle(type) {
        var that = this;
        if (this.selids.length == 0) {
          this.$message({ message: "至少选择一行", type: "warning", });
          return
        }
        if(type == 'delete'){
          this.$confirm("确认要执行批量通过的操作吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(async () => {
    
            const params = {
              thirdPlatform: this.type,
              ids: this.selids
            }
            let res = await deleteCostBill(params);
            if (res?.isSuccess) {
              that.$message({ message: '已删除', type: "success" });
              that.getList();
            }
            else {
              that.$message({ message: '发生异常，请刷新后重试', type: "error" });
            }
          });
        }else{
          this.isMultiple = true
          this.$refs.editform.visible = true;
        }

      },
    }
  };
  </script>
  
  <style lang="scss" scoped>
  .top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
      width: 200px;
      margin-right: 10px;
    }
  }
  //解决下拉菜单多选由文字太长导致样式问题
  ::v-deep .el-select__tags-text {
    max-width: 45px;
  }
  </style>