<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-tooltip class="item" effect="dark" content="默认为精确匹配在开始或者结尾输入*进行模糊匹配" placement="top">
                        <inputYunhan :key="'3'" :keys="'three'" :width="'180px'" ref="childGoodsCode"
                            v-model="filter.goodsCode" :inputt.sync="filter.goodsCode" placeholder="成品编码" :clearable="true" @callback="callbackGoodsCode"
                            title="成品编码"></inputYunhan>
                    </el-tooltip>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-input v-model.trim="filter.goodsName" type="text" maxlength="100" clearable placeholder="请输入成品名称..."
                        style="width:180px;">
                    </el-input>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select v-model="filter.isAuto" clearable placeholder="请选择开单状态状态" style="width: 180px">
                        <el-option label="开启" value="true" />
                        <el-option label="未开启" value="false" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select filterable v-model="filter.company" @change="changeSetCompany" collapse-tags clearable
                        placeholder="分公司" style="width: 100px">
                        <el-option key="义乌" label="义乌" value="义乌"></el-option>
                        <el-option key="南昌" label="南昌" value="南昌"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select ref="filterbrandId" v-model="filter.brandIds" multiple collapse-tags filterable clearable
                        placeholder="请选择采购员" style="width: 200px">
                        <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-button type="primary" @click="batchUpdateItem">批量操作</el-button>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-button type="primary" @click="batchUpdateAuto">批量开启/关闭</el-button>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-button v-if="checkPermission('api:inventory:machine:SetGoodsFinishedPartAutoAsync')" type="primary"
                        @click="onSetAutoInfo">设置开单信息</el-button>
                </el-button>
            </el-button-group>
        </template>
        <!--列表-->
        <vxetablebase :id="'machineindex20230701'" :tablekey="'machineindex20230701'" :tableData='list' :tableCols='tableCols'
            @cellClick='cellclick' @select='selectchange' :tableHandles='tableHandles' :loading='listLoading' :border='true'
            :checkbox-config="{ labelField: 'id', highlight: true, range: true }" @checkbox-range-end="callback"
            :that="that" ref="vxetable" @sortchange='sortchange' />

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <!-- 设置开单信息 -->
        <el-dialog title="设置开单信息" :visible.sync="dialogAddVisible" v-dialogDrag width='30%' :close-on-click-modal="false"
            height='700px'>
            <el-form ref="ruleForm" :model="addForm" :rules="rules" label-width="100px">
                <el-form-item label="半成品数量" prop="part_Count">
                    <el-input-number v-model="addForm.part_Count" controls-position="right" :precision="0" :min="1"
                        :max="10000000" style="width: 220px;"></el-input-number>
                </el-form-item>
                <el-form-item label="开单间隔" prop="diffHours">
                    <el-select v-model="addForm.diffHours" placeholder="小时" style="width: 220px;">
                        <el-option value="0" label="0"></el-option>
                        <el-option value="12" label="12"></el-option>
                        <el-option value="24" label="24"></el-option>
                        <el-option value="36" label="36"></el-option>
                        <el-option value="48" label="48"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <my-confirm-button type="submit" :loading="onFinishLoading" :validate="editFormvalidate"
                        @click="onFinish()">保存&关闭
                    </my-confirm-button>
                    <!-- <vxe-button type="reset" size="mini" @click="onClose">关闭</vxe-button> -->
                </el-form-item>
            </el-form>
        </el-dialog>

        <el-dialog :title="titile" :visible.sync="dialogEditVisible" v-dialogDrag width='30%' :close-on-click-modal="false"
            height='800px'>
            <OperationGoodsFinishedpartVue @closedialogEdit="closedialogEdit" :filter="editFilter"
                ref="OperationGoodsFinishedpartVue"></OperationGoodsFinishedpartVue>
        </el-dialog>

        <el-dialog title="详情" :visible.sync="dialogShowInfoVisible" v-dialogDrag width='50%' :close-on-click-modal="false"
            height='800px'>
            <goodsfinishedpartdetail ref="goodsfinishedpartdetail" style="height: 500px;"></goodsfinishedpartdetail>
        </el-dialog>

    </container>
</template>

<script>
import { Loading } from 'element-ui';
import dayjs from "dayjs";
import inputYunhan from "@/components/Comm/inputYunhan";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { formatTime, formatNoLink } from "@/utils/tools";
import { getGoodsFinishedPartAutoAsync, updateGoodsFinishedPartAutoAsync, setGoodsFinishedPartAutoAsync, getGoodsFinishedPartAutoSetAsync } from "@/api/inventory/machine"
import { getAllProBrand } from '@/api/inventory/warehouse'
import OperationGoodsFinishedpartVue from './OperationGoodsFinishedpart.vue';
import goodsfinishedpartdetail from './goodsfinishedpartdetail.vue';

const tableCols = [
    { istrue: true, label: '', width: '100', type: "checkbox", },
    { istrue: true, prop: 'goodsCode', align: 'center', label: '成品编码', width: '120', sortable: 'custom', type: 'html', formatter: (row) => formatNoLink(row.goodsCode) },
    { istrue: true, prop: 'goodsName', label: '成品名称', width: '240', sortable: 'custom', },
    { istrue: true, prop: 'brandId', label: '采购员', width: '120', sortable: 'custom', formatter: (row) => row.bianMaBrandName },
    { istrue: true, prop: 'company', label: '分总司', width: '150', sortable: 'custom', formatter: (row) => row.dDeptName },
    { istrue: true, prop: 'amendDay', label: '日均修正量', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'inventoryDay', label: '库存周转天数', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'sellStock', label: '当前库存可用数', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'safetyStockDays', label: '库存安全天数', tipmesg: '如库存周转天数列数字小于填写数字自动开单', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'processingQuantity', label: '需加工数量', width: '100', tipmesg: '设置库存安全天数*日均修正量', sortable: 'custom' },
    { istrue: true, prop: 'brandCode', label: '品牌', width: '80', sortable: 'custom', formatter: (row) => row.brandName },
    { istrue: true, prop: 'plannedCompletionDate', label: '计划完成时间', tipmesg: '等同于新建包装加工计划完成日期，1为+1天，不填默认为当天', width: '90', sortable: 'custom', },
    { istrue: true, prop: 'isAuto', align: 'center', label: '开单状态', width: 'auto', sortable: 'custom', tipmesg: '如半成品数量不足或未设置库存安全天数、加工数量、计划完成时间无法开启', type: 'switch', change: (row, that) => that.changeStatus(row) },

];
const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
];


export default {
    name: 'YunHanAdminGoodsFinishedpart',
    components: { MyConfirmButton, container, vxetablebase, inputYunhan, OperationGoodsFinishedpartVue, goodsfinishedpartdetail },

    data() {
        return {
            that: this,
            filter: {
                goodsCode: null,
                goodsName: null,
                keywords: null,
                version: null,
                eywords: null,
                brandIds: [],
                status: "所有",
                isAuto: null
            },
            addForm: {
                id: null,
                diffHours: null,
                part_Count: 0,
            },
            rules: {
                diffHours: [
                    { required: true, message: '请选择开单间隔', trigger: 'change' },
                ],
                part_Count: [
                    { required: true, message: '请输入半成品数量', trigger: 'blur' },
                ],
            },
            editFilter: {
                selData: [],
                type: null
            },
            keywordsTip: '支持搜索的内容：采购单号、Erp单号',
            titile: '批量开启/关闭',
            brandlist: [],
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "createdTime", IsAsc: false },
            total: 0,
            sels: [],
            chooseTags: [],
            selids: [],
            listLoading: false,
            pageLoading: false,
            dialogAddVisible: false,
            dialogEditVisible: false,
            dialogShowInfoVisible: false,
            onFinishLoading: false,
            dialogLoading: false
        };
    },

    async mounted() {
        await this.onSearch();
        await this.init();
    },

    methods: {
        async init() {
            var res2 = await getAllProBrand();
            this.brandlist1 = res2.data;
            this.brandlist = res2.data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        async changeSetCompany() {
            if (this.filter.company === '义乌' || this.filter.company === '南昌') {
                this.brandlist = this.brandlist1.filter(f => f.company === this.filter.company).map(item => {
                return { value: item.key, label: item.value };
                });
            } else if (this.filter.company === '其他') {
                this.brandlist = this.brandlist1.filter(f => f.company !== '南昌' && f.company !== '义乌').map(item => {
                return { value: item.key, label: item.value };
                });
            } else {
                this.brandlist = this.brandlist1.map(item => {
                return { value: item.key, label: item.value };
                });
            }
            this.filter.brandId = null;
        },
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
            this.selids = [];
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await getGoodsFinishedPartAutoAsync(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.total = res.data.total;
            const data = res.data.list;
            this.list = data
        },
        async callbackGoodsCode(val) {
            this.filter.goodsCode = val;
            //this.onSearch();
        },
        async batchUpdateAuto() {
            if (!this.selids || this.selids.length <= 0) {
                this.$message({ message: "请勾选至少一行数据", type: "warning" });
                return;
            }

            // 获取当前时间的分钟数
            const now = new Date();
            const minutes = now.getMinutes();

            // 如果当前时间在整点前后五分钟内，则返回 true
            if (minutes >= 55 || minutes <= 5) {
                this.$message({ message: '自动开单前后五分钟不允许操作！', type: "warning" });
                return false;
            }

            this.selids.forEach(f => {
                if (f.safetyStockDays == null || f.safetyStockDays <= 0) {
                    this.$message({ message: "存在库存安全天数未设置数据，不允许操作！", type: "warning" });
                    throw ('');
                } else if (f.plannedCompletionDate == null || f.plannedCompletionDate <= 0) {
                    this.$message({ message: "存在计划完成时间未设置数据，不允许操作！", type: "warning" });
                    throw ('');
                }
            })

            let _this = this;
            _this.titile = '批量开启/关闭';
            let selData = this.selids.map((item) => {
                var newItem = { goodsCode: item.goodsCode, safetyStockDays: item.safetyStockDays, brandCode: item.brandCode, plannedCompletionDate: item.plannedCompletionDate };
                return newItem;
            });
            _this.editFilter.selData = selData;
            _this.editFilter.type = 2;
            _this.dialogEditVisible = true;
            this.$nextTick(async () => {
                await _this.$refs.OperationGoodsFinishedpartVue.loadData();
            })
        },
        async batchUpdateItem() {
            if (!this.selids || this.selids.length <= 0) {
                this.$message({ message: "请勾选至少一行数据", type: "warning" });
                return;
            }

            // 获取当前时间的分钟数
            const now = new Date();
            const minutes = now.getMinutes();

            // 如果当前时间在整点前后五分钟内，则返回 true
            if (minutes >= 55 || minutes <= 5) {
                this.$message({ message: '自动开单前后五分钟不允许操作！', type: "warning" });
                return false;
            }

            let _this = this;
            _this.titile = '批量操作';
            let selData = this.selids.map((item) => {
                var newItem = { goodsCode: item.goodsCode, safetyStockDays: item.safetyStockDays, brandCode: item.brandCode, plannedCompletionDate: item.plannedCompletionDate };
                return newItem;
            });
            _this.editFilter.selData = selData;
            _this.editFilter.type = 1;
            _this.dialogEditVisible = true;
            this.$nextTick(async () => {
                await _this.$refs.OperationGoodsFinishedpartVue.loadData();
            })
        },
        closedialogEdit(val) {
            this.dialogEditVisible = false;
            if (val) this.onSearch();
        },
        async onSetAutoInfo() {
            // 获取当前时间的分钟数
            const now = new Date();
            const minutes = now.getMinutes();

            // 如果当前时间在整点前后五分钟内，则返回 true
            if (minutes >= 55 || minutes <= 5) {
                this.$message({ message: '自动开单前后五分钟不允许操作！', type: "warning" });
                return false;
            }

            this.dialogAddVisible = true;
            let loadingInstance = Loading.service();
            Loading.service({ fullscreen: true });
            var res = await getGoodsFinishedPartAutoSetAsync();
            if (res?.data == null) {
                loadingInstance.close();
                return;
            }
            this.addForm.id = res?.data.id ?? null;
            this.addForm.diffHours = res?.data.diffHours ?? this.addForm.diffHours;
            this.addForm.part_Count = res?.data.part_Count ?? this.addForm.part_Count;
            console.log('数据', this.addForm)
            loadingInstance.close();
        },
        editFormvalidate() {
            let isValid = false
            this.$refs.ruleForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        async onClose() {
            this.dialogAddVisible = false;
            this.addForm = {
                id: null,
                diffHours: 0,
                part_Count: 0,
            }
        },
        async onFinish() {
            this.onFinishLoading = true;
            var params = { id: this.addForm.id, diffHours: this.addForm.diffHours, part_Count: this.addForm.part_Count };
            var res = await setGoodsFinishedPartAutoAsync(params)
            if (!res?.success) {
                this.onFinishLoading = false;
                return;
            }
            this.$message({
                type: 'success',
                message: '操作成功!'
            });
            this.onFinishLoading = false;
            this.dialogAddVisible = false;
        },
        async changeStatus(row) {
            // 获取当前时间的分钟数
            const now = new Date();
            const minutes = now.getMinutes();

            // 如果当前时间在整点前后五分钟内，则返回 true
            if (minutes >= 55 || minutes <= 5) {
                this.$message({ message: '自动开单前后五分钟不允许操作！', type: "warning" });
                row.isAuto = !row.isAuto;
                return false;
            }
            var _this = this;
            if (row.safetyStockDays == null || row.safetyStockDays <= 0) {
                _this.$message({ message: "存在库存安全天数未设置数据，不允许操作！", type: "warning" });
                row.isAuto = !row.isAuto;
                return;
            } else if (row.plannedCompletionDate == null || row.plannedCompletionDate <= 0) {
                _this.$message({ message: "存在计划完成时间未设置数据，不允许操作！", type: "warning" });
                row.isAuto = !row.isAuto;
                return;
            }
            this.$confirm('此操作将修改开单状态, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                _this.listLoading = true
                var res = await updateGoodsFinishedPartAutoAsync({ goodsCodes: row.goodsCode, isAuto: row.isAuto, type: 2 })
                if (!res?.success) {
                    row.isAuto = !row.isAuto;
                    // this.$message({
                    //     type: 'error',
                    //     message: '操作失败，请检查数据后重试!'
                    // });
                    return;
                }
                this.$message({
                    type: 'success',
                    message: '操作成功!'
                });
                this.onSearch();
            }).catch(() => {
                row.isAuto = !row.isAuto;
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
            _this.listLoading = false
        },
        selectchange: function (rows, row) {
            //先把当前也的数据全部移除
            this.list.forEach(f => {
                let index = this.chooseTags.findIndex((v) => (v === f.goodsCode));
                if (index !== -1) {
                    this.chooseTags.splice(index, 1);
                    this.selrows.splice(index, 1);
                }
            });
            //把选中的添加
            rows.forEach(f => {
                let index = this.chooseTags.findIndex((v) => (v === f.goodsCode));
                if (index === -1) {
                    this.chooseTags.push(f.goodsCode);
                    this.selrows.push(f);
                    console.log("选中数据", this.selrows);
                }
            });

            ///
            let _this = this;
            if (rows.length > 0) {
                var a = [];
                rows.forEach(element => {
                    let b = _this.list.indexOf(element);
                    a.push(b + 1);
                });

                let d = _this.list.indexOf(row);

                var b = Math.min(...a)
                var c = Math.max(...a)

                a.push(d);
                if (d < b) {
                    var b = _this.list.indexOf(row);
                    var c = Math.max(...a)
                } else if (d > c) {
                    var b = Math.min(...a) - 1
                    var c = Math.max(...a)
                } else {
                    var b = Math.min(...a) - 1
                    var c = _this.list.indexOf(row) + 1;
                }

                let neww = [b, c];
                _this.selids = neww;
            }
            console.log('选择的数据', this.selids)
        },
        callback(val) {
            this.selids = [...val];

            this.tablelist = [];
            this.tablelist = val;
            var goodsCode = val.map((item) => {
                return item.goodsCode;
            })
            this.chooseTags = goodsCode;
            console.log("goods返回值", this.chooseTags)
        },
        async cellclick({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }) {
            if (column.property == 'goodsCode') {
                let selData = { goodsCode: row.goodsCode };
                this.dialogShowInfoVisible = true;
                this.$nextTick(async () => {
                    this.$refs.goodsfinishedpartdetail.loadData({ selRows: selData, });
                });
                // this.$showDialogform({
                //     path: `@/views/inventory/machine/goodsfinishedpartdetail.vue`,
                //     title: '详情信息',
                //     autoTitle: false,
                //     args: { selRows: selData, },
                //     height: '600px',
                //     width: '50%',
                // })
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>

<style lang="scss" scoped>::v-deep .el-input-number.is-controls-right .el-input__inner {
    text-align: left !important;
}</style>
