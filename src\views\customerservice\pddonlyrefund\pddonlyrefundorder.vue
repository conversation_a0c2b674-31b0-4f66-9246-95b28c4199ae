<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="申请时间" style="padding: 0;margin: 0; ">
                    <el-date-picker style="width: 280px" v-model="filter.timerange" type="datetimerange"
                        format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" range-separator="至"
                        start-placeholder="开始申请时间" end-placeholder="结束申请时间" :picker-options="pickerOptions" >
                    </el-date-picker>
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0; ">
                    <el-date-picker style="width: 280px" v-model="filter.timerange2" type="datetimerange"
                        format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" range-separator="至"
                        start-placeholder="同意退款开始时间" end-placeholder="同意退款结束时间" :picker-options="pickerOptions" >
                    </el-date-picker>
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0; ">
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="" style="padding: 0;margin: 0; ">
                    <el-select v-model="filter.partValue" style="width: 120px" size="mini" placeholder="是否全额退款" clearable>
                        <el-option label="全额退款" :value="1"></el-option>
                        <el-option label="部分退款" :value="2"></el-option>
                        <el-option label="全部" :value="0"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="" style="padding: 0;margin: 0; ">
                    <el-select v-model="filter.orderState" style="width: 90px" size="mini" placeholder="订单状态" clearable>
                        <el-option label="已发货" :value="1"></el-option>
                        <el-option label="未发货" :value="2"></el-option>
                        <el-option label="全部" :value="0"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="" style="padding: 0;margin: 0; ">
                    <el-select v-model="filter.signState" style="width: 90px" size="mini" placeholder="签收状态" clearable>
                        <el-option label="已签收" :value="1"></el-option>
                        <el-option label="未签收" :value="2"></el-option>
                        <el-option label="全部" :value="0"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-if="false">
                    <el-select v-model="filter.saleAfterState" style="width: 85px" size="mini" clearable
                        @change="onSaleAfterStateChange">
                        <el-option label="全部" :value="0"></el-option>
                        <el-option label="退款成功" :value="1"></el-option>
                        <el-option label="已撤销" :value="2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0; ">
                    <el-input v-model.trim="filter.dutySnick" style="width: 100px" placeholder="责任人"
                        @keyup.enter.native="onSearch" clearable maxlength="100" />
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0; ">
                    <el-input v-model.trim="filter.queryValue" style="width: 200px" placeholder="请输入关键字.."
                        @keyup.enter.native="onSearch" clearable maxlength="100">
                        <el-tooltip slot="suffix" class="item" effect="dark"
                            content="支持搜索的内容：售后单号、订单编号、商品ID、物流单号、Sku信息、退款原因、同意退款人" placement="bottom">
                            <i class="el-input__icon el-icon-question"></i>
                        </el-tooltip>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
                :loading="listLoading" :tableHandles="tableHandles1">
                <template slot='extentbtn'>
                    <!-- <el-button-group style="margin-left: 10px;">
                        <el-radio-group v-model="filter.refundType" size="mini" @change="onRefundTypeChange">
                          <el-radio-button :label="1">发生维度</el-radio-button>
                          <el-radio-button :label="2">付款维度</el-radio-button>
                          <el-radio-button :label="3">运营维度</el-radio-button>
                        </el-radio-group>
                    </el-button-group> -->
                </template>
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
        <el-dialog title="导入" :visible.sync="dialogVisibleUpload" width="40%" v-dialogDrag>
            <span>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                            accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleUpload = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog title="订单日志信息" v-if="dialogHisVisible1" :visible.sync="dialogHisVisible1" width="70%" height="600px"
            v-dialogDrag>
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="sendOrderNo"
                style="z-index:10000;height:600px" />
        </el-dialog>
        <el-dialog title="聊天信息" v-if="dialogHisVisible2" :visible.sync="dialogHisVisible2" width="70%" height="600px"
            v-dialogDrag>
            <OrderChatListByInnerNos ref="OrderChatListByInnerNos" :orderNo="sendSaleAfterNo"
                style="z-index:10000;height:600px" />
        </el-dialog>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { getPddOnlyRefundOrderPageList, importPddonlyRefundOrderAsync, exportPddOnlyRefundOrderList } from '@/api/customerservice/pddonlyrefund';
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
import OrderChatListByInnerNos from "@/views/customerservice/pddonlyrefund/pddonlyfundchatLog.vue";
const tableCols = [
    { istrue: true, prop: 'saleAfterNo', label: '售后编号', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showChatLogDetail(row) },
    { istrue: true, prop: 'orderNo', label: '订单编号', width: '180', sortable: 'custom', type: 'click', handle: (that, row) => that.showLogDetail(row) },
    { istrue: true, prop: 'shopName', label: '店铺', width: '180', sortable: 'custom' },
    { istrue: true, prop: 'goodId', label: '商品ID', width: '110', sortable: 'custom' },
    { istrue: true, prop: 'tradeMoney', label: '交易金额', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'saleAfterState', label: '售后状态', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'refundType', label: '退款类型', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'refundMoney', label: '退款金额', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'orderState', label: '订单状态', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'signState', label: '签收状态', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'expressInterceptState', label: '快递拦截', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'buyer', label: '买家', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'deliverNo', label: '物流单号', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.ShowLogistics(row) },
    { istrue: true, prop: 'overTime', label: '超时时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'applyTime', label: '申请时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'agreeRefundTime', label: '同意退款时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'refundReason', label: '退款原因', width: '130', sortable: 'custom' },
    { istrue: true, prop: 'agreeRefundUser', label: '同意退款人', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'skuInfo', label: 'sku信息', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'orderFlag', label: '订单标记', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'remark', label: '备注', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'lastReceiveUser', label: '最后接待人', width: '100' },
    // { istrue: true, prop: 'lastReceiveUserUp', label: '接待人上级',  width: '100'},
    { istrue: true, prop: 'lastReceiveMsg', label: '最后一条接待信息', width: '140' },
    //{ istrue: true, prop: 'dutySnick', label: '责任人', width: '180', sortable: 'custom' },
    //{ istrue: true, prop: 'dutyContext', label: '责任内容', width: '300', sortable: 'custom' },
]
const tableHandles1 = [
    { label: "导入", handle: (that) => that.onImport() },
    { label: "导出", handle: (that) => that.onExport() },
];
export default {
    name: 'pddonlyrefundorder',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, OrderActionsByInnerNos, OrderChatListByInnerNos },
    props: {

    },
    data() {
        return {
          date: '',
            that: this,
            shopList: [],
            filter: {
                timerange: [
                    formatTime(dayjs().subtract(3, "day"), "YYYY-MM-DD HH:mm"),
                    formatTime(new Date(), "YYYY-MM-DD HH:mm"),
                ],
                applyStartDate: null,
                applyEndDate: null,
                timerange2: [],
                agreeStartDate: null,
                agreeEndDate: null,
                isPart: false,
                saleAfterState: 0,
                refundType: 2,
                partValue: null,
                signState: null,
                orderState: null,
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "applyTime", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            pickerOptions: {
                shortcuts: [
                    {
                        text: '昨天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime());
                            picker.$emit('pick', [start, start]);
                        }
                    }, {
                        text: '近三天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime());
                            const end = new Date(new Date(tdate.toLocaleDateString()));
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一周',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 5);
                            const end = new Date(new Date(tdate.toLocaleDateString()).getTime() + 3600 * 1000 * 24 * 5);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一个月',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 31);
                            console.log("获取前一个月的时间", tdate.getDay());
                            const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }],
                disabledDate(date) {
                // 设置禁用日期
                const start = new Date('1970/1/1');
                const end = new Date('9999/12/31');
                return date < start || date > end;
              },
              defaultDate: new Date('1970/1/1')
            },
            tableHandles1: tableHandles1,
            dialogVisibleUpload: false,
            fileList: [],
            fileparm: {},
            uploadLoading: false,
            dialogHisVisible: false,
            sendOrderNo: null,
            dialogHisVisible1: false,
            dialogHisVisible2: false,
            sendSaleAfterNo: null
        };
    },
    async mounted() {
        await this.getShopList();
        await this.onSearch()
    },
    methods: {
        async getShopList() {
            const res1 = await getAllShopList({ platforms: [2] });
            this.shopList = [];
            res1.data?.forEach(f => {
                if (f.isCalcSettlement && f.shopCode)
                    this.shopList.push(f);
            });
        },
        async onIsPartChange() {
            await this.onSearch();
        },
        async onSaleAfterStateChange() {
            await this.onSearch();
        },
        async onRefundTypeChange() {
            await this.onSearch();
        },
        //查询第一页
        async onSearch() {
            console.log(this.filter)
            if (!this.filter.timerange) {
                this.$message({ message: "请选择申请时间", type: "warning", });
                return;
            }
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            this.filter.applyStartDate = null;
            this.filter.applyEndDate = null;
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.applyStartDate = this.filter.timerange[0];
                this.filter.applyEndDate = this.filter.timerange[1];
            }
            else {
                this.$message({ message: "请先选择申请时间", type: "warning" });
                return false;
            }
            this.filter.agreeStartDate = null;
            this.filter.agreeEndDate = null;
            if (this.filter.timerange2 && this.filter.timerange2.length > 1) {
                this.filter.agreeStartDate = this.filter.timerange2[0];
                this.filter.agreeEndDate = this.filter.timerange2[1];
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const res = await getPddOnlyRefundOrderPageList(params)
            this.listLoading = false;
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            data.forEach(d => {
                d._loading = false;
            })
            this.list = data;
        },
        //排序查 询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onImport() {
            this.dialogVisibleUpload = true;
        },
        async onExport() {
            this.$confirm('确认导出吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                console.log(params, 'params')
                this.listLoading = true;
                const rlt = await exportPddOnlyRefundOrderList(params);
                this.listLoading = false;
                if (rlt && rlt.data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([rlt.data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '拼多多仅退款订单_' + new Date().toLocaleString() + '_.xlsx')
                    aLink.click()
                }
            }).catch(() => {
            });
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            var res = await importPddonlyRefundOrderAsync(form);
            if (res?.success)
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
            this.uploadLoading = false
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            this.dialogVisibleUpload = false;
        },
        async onUploadChange(file, fileList) {
            // let list = [];
            // list.push(file);
            this.fileList = fileList;
        },
        onUploadRemove(file, fileList) {
            this.fileList = []
        },
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        showLogDetail(row) {
            this.dialogHisVisible1 = true;
            this.sendOrderNo = String(row.orderNo);
        },
        showChatLogDetail(row) {
            this.dialogHisVisible2 = true;
            console.log(row.saleAfterNo);
            this.sendSaleAfterNo = String(row.saleAfterNo);
        },
        ShowLogistics(row) {
            let self = this;
            this.$showDialogform({
                path: `@/views/order/logisticsWarning/DbLogisticsRecords.vue`,
                title: '物流明细',
                args: { expressNos: row.deliverNo },
                height: 300,
            });
        },
    },
};
</script>

<style lang="scss" scoped></style>
