<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template>
      <!--表单-->
      <el-form ref="distributionOnlineFrom" label-position="right" :model="form" :rules="rulesList"
        label-width="100px">
        <el-row>
          <!-- <el-col :span="6">
            <el-form-item label="竞品平台：">
              <el-select prop="platform" disabled v-model="form.platform" style="width:120px;">
                <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->

          <el-col :span="12">
            <el-form-item prop="goodsID" label="竞品ID：">
              <div v-html="formatLinkProCode(2, form.goodsID)"></div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="shopCode" label="店铺："> 
            <el-select filterable v-model="form.shopCode" placeholder="店铺" clearable style="width: 80%">
            <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.shopCode" />
          </el-select>
          </el-form-item>
          </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
            <el-form-item prop="productCategoryList" label="商品类目："> 
              <el-cascader v-model="form.productCategoryList" :options="categorylist" :props="{ checkStrictly: true, value: 'id' }"
               filterable style="width:80%;" placeholder="类目" clearable/>
               <!-- <el-input v-model.trim="form.productCategory" :maxlength="100" :minlength="4" style="width:100%"  placeholder="类目"></el-input> -->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="productName" label="商品名称：">
              <el-input v-model.trim="form.productName" :maxlength="100" :minlength="4" style="width:80%">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="主图：" style="margin-top: 10px;"  prop="viewImageData" >
              <YhImgUpload :value.sync="form.viewImageData" :limit="10" :ismultiple="true" ref="img1"></YhImgUpload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="详情页：" style="margin-top: 10px;" prop="detailGallery">
              <YhImgUpload :value.sync="form.detailGallery" :limit="10" :ismultiple="true"  ref="img2"></YhImgUpload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="视频：" style="margin-top: 10px; " prop="videoGallery">
              <!-- <YhImgUpload :value.sync="form.vidioUrls" style="width: 280px;" :limit="10"></YhImgUpload> -->
              <!-- <viodeUpload :uploadInfo="form.vidioUrls"></viodeUpload> -->
              <viodeUpload :minisize="false" ref="uploadexl"
                               :uploadInfo="form.videoGallery" :limit="2" :accepttyes="'.mp4'" :delfunction="deluplode"
                />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="营销方案：" style="margin-top: 10px; ">
              <el-tag style="margin-left: 3px;" v-for="tag in form.yellowLabelList" closable @close="handleClose(tag)">
                {{ tag }}
              </el-tag>
              <el-input
                class="input-new-tag"
                v-if="inputVisible"
                v-model="inputValue"
                ref="saveTagInput"
                size="mini"
                @keyup.enter.native="handleInputConfirm"
                @blur="handleInputConfirm"
              >
              </el-input>
              <!-- <el-button v-else class="button-new-tag" size="small" @click="showInput">+</el-button> -->
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <div style="height:300px;">
              <!--列表-->
              <ces-table ref="skutable" :that='that' :isIndex='true' :hasexpandRight='false' :hasexpand='false' :isSelectColumnCols="false"
                :tableData='form.skus' :tableCols='skuTableCols' :loading="listLoading" :tableHandles='tableHandles'
                >
                  <template #leftone>
                    <el-table-column width="100" label="商品图片" column-key="id" float="right" :render-header="addRedStar">
                      <template slot-scope="scope">
                        <YhImgUpload2Table :value.sync="scope.row.image"></YhImgUpload2Table>
                      </template>
                    </el-table-column>
                  </template>
              </ces-table>
            </div>
          </el-col>
        </el-row>
        <!-- <div style="width:100%;text-align: right;">
        <el-button size="mini" type="primary" @click="addTableData">添加</el-button>
        </div> -->
      <el-row>
        <el-col :span="24" style="text-align:right;padding-top:30px;">
          <el-button @click="onsave(false)">取 消</el-button>
          <el-button type="primary" @click="onsave(true)" :loading="saveLoading">{{ '保存&关闭' }}</el-button>
        </el-col>
      </el-row>
      </el-form>
    </template>
  </my-container>
</template>
<script>

import cesTable from "@/components/Table/table.vue";
import { formatTime,listToTree } from "@/utils";
import { formatPercen, platformlist, getStore, formatLinkProCode } from "@/utils/tools";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import { getDistributionOnlineProductById,saveDistributionOnlineProduct } from '@/api/operatemanage/distributiongoodsOnline.js';
import { getShopByCurUserGroup } from '@/api/operatemanage/base/shop.js';
import { getList as getcategorylist } from '@/api/operatemanage/base/category'

import YhImgUpload2Table from '@/components/upload/yh-img-upload2Table.vue';
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
import viodeUpload from "@/views/media/shooting/uploadfile.vue";

const skuTableCols = [
  { istrue: true, prop: 'norms', type: "inputtext", label: '款式', minwidth: '200', maxlength: 40, isRequired: true },
  { istrue: true, prop: 'size', type: "inputtext", label: '型号', minwidth: '200', maxlength: 40, isRequired: true },
  { istrue: true, prop: 'unitPrice', type: "inputnumber", precision: 2, label: '拼单价', width: '100', min: 0, max: 900000, isRequired: true },
  { istrue: true, prop: 'price', type: "inputnumber", precision: 2, label: '到手价', width: '100', min: 0, max: 900000, isRequired: true },
  { istrue: true, prop: 'goodsCode', type: "inputtext", label: '商品编码', minwidth: '200', maxlength: 40, isRequired: true },
  {istrue: true,type: "button",width: "120",label: '操作',btnList: [{ label: "删除", handle: (that, row) => that.delTbaleRow(row)}]}
  
];
const tableHandles = [
  { label: "添加", handle: (that) => that.addTableData() }
];
export default {
  name: "hotsalegoodschooselistsku",
  components: { MyContainer, MyConfirmButton, cesTable, YhImgUpload2Table, YhImgUpload,viodeUpload },
  data() {
    return {
      saveLoading:false,
      inputVisible: false,
        inputValue: '',
      that: this,
      mode: 3,
      form: {
      },
      platformlist: platformlist,
      total: 0,
      tansitionshow: false,
      listLoading: false,
      pageLoading: false,
      goodschoiceVisible: false,//选择商品显隐
      curRow: null,
      curCol: "",
      formEditMode: true,//是否编辑模式 
      tableHeightBase: 61,
      goodsOptions: [],
      remoteSeachGoodsLoading: false,
      skuTableCols: skuTableCols,
      tableHandles: tableHandles,
      categorylist: [],
      shopList: [],// 店铺列表
      rulesList: {
        productCategoryList: [{ required: true, message: '请选择商品类目', trigger: 'change' }],
        shopCode: [{ required: true, message: '请输入店铺', trigger: 'blur' },
        { min: 4, max: 100, message: '长度在 4 到 100 个字符', trigger: 'blur' }],
        productName: [{ required: true, message: '输入商品名称', trigger: 'blur' }],
        viewImageData: [{ required: true, message: '请上传主图', trigger: 'change' }],
        detailGallery: [{ required: true, message: '请上传详情页', trigger: 'change' }],
        videoGallery: [{ required: false, message: '请上传视频', trigger: 'change' }],
      },
    };
  },
  async mounted() {
    this.getcategorylist();
    this.getPddShoplist();
  },
  // computed: {
  //   rulesList: function () {
  //     let rulesList = {
  //       supplierPlatForm: [{ required: true, message: '请选择供应商平台', trigger: 'change' }],
  //     };
  //     return rulesList;
  //   },
  // },
  methods: {
    // 图片必填标识
    formatLinkProCode: formatLinkProCode,
    addRedStar(h, { column }) {
      return [
        h("span", { style: "color: red" }, "*"),
        h("span", " " + column.label),
      ];
    },
    // 添加表格数
    addTableData () {
      let item = {
        norms: null,
        size: null,
        unitPrice: null,
        price:null,
        goodsCode:null,
        image:null,
      }
      this.form.skus.push(item)
    },
    // 删除表格数据
    delTbaleRow (row) {
      this.form.skus.splice(this.form.skus.indexOf(row), 1)
    },
    // 删除营销方案
    handleClose(tag) {
        this.form.yellowLabelList.splice(this.form.yellowLabelList.indexOf(tag), 1);
    },
    showInput() {
        this.inputVisible = true;
        this.$nextTick(_ => {
          this.$refs.saveTagInput.$refs.input.focus();
        });
    },
    handleInputConfirm() {
        let inputValue = this.inputValue;
        if (inputValue) {
          this.form.yellowLabelList.push(inputValue);
        }
        this.inputVisible = false;
        this.inputValue = '';
      },
    // 重置表单
    resetFrom () {
      this.form = {
        goodsID: null,
        productCategoryList: null,
        productName: null,
        viewImageData: null,
        detailGallery: null,
        videoGallery: [],
        yellowLabelList: [],
        skus: [],
        shopCode: null,
      };
      this.$refs.uploadexl.setData([]);
    },
    // 删除
    deluplode () {
      
    },
   async getFormData (id) {
      if (id) {
        this.resetFrom();
       await getDistributionOnlineProductById({ id: id }).then(res => {
         if (res.success) {
           this.form = res.data;
           if (res.data.videoGallery?.length > 0 ) {
           let videoGallery = [];
             res.data.videoGallery.forEach((item, i) => {
               let vi = {
                 url: item,
                 fileName:item,
                 relativePath: null,
                 domain:null
               }
               videoGallery.push(vi);
             })
             
             this.form.videoGallery = videoGallery;
             this.$nextTick(() => {
              this.$refs.uploadexl.setData(this.form.videoGallery);
            })
          }
          }
       })
      }
    
    },
    // 提交
     onsave (type) {
      if (!type) {
        this.$emit('onSave');
      } else {
        this.form.productCategory = this.form.productCategoryList[this.form.productCategoryList?.length - 1];
         this.$refs.distributionOnlineFrom.validate( (valid) => {
          if (valid) {
            var res = this.$refs.uploadexl.getReturns();
            if (res.data?.length > 0) {
              let videoGallery = []
              res.data.forEach(item => {
                videoGallery.push(item.url);
              })
              this.form.videoGallery = videoGallery
            }
            if (this.$refs.img1.fileList?.length > 0) {
              let viewImageData = [];
              this.$refs.img1.fileList.forEach(item => {
                viewImageData.push(item.url)
              })
              this.form.viewImageData = viewImageData
            }
            if (this.$refs.img2.fileList?.length > 0) {
              let detailGallery = [];
              this.$refs.img2.fileList.forEach(item => {
                detailGallery.push(item.url)
              })
              this.form.detailGallery = detailGallery
            }
            if (this.form.skus?.length > 0) {
              try {
                this.form.skus.forEach((item, i) => {
                  if (!item.image || !item.norms || !item.size || !item.unitPrice || !item.price || !item.goodsCode) {
                    this.$message.warning(`请检查第` + (i + 1) + `条商品信息是否填写完全，或删除该条商品信息。`)
                    throw new Error("error");
                  }
                })
              } catch (e) {
                return
              }
            }
            this.saveLoading = true;
            saveDistributionOnlineProduct(this.form).then(res => {
              this.saveLoading = false;
              if (res.success) {
                this.$message.success("提交成功！")
                 this.$emit('onSave');
              }
            })
          }
        })
      }
    },
    async getcategorylist () {
      const res = await getcategorylist({ platform: 2 }) // 获取拼多多类目
      if (!res?.code) {
        return
      }
      const list = [];
      res.data.forEach(f => {
        f.label = f.categoryName;
        list.push(f)
      })
      // this.categorylist = listToTree(_.cloneDeep(list), {
      //   id: '',
      //   parentId: '',
      //   label: '所有'
      // })
      this.categorylist = listToTree(_.cloneDeep(list))
    },
     // 获取dpp店铺
  async getPddShoplist () {
      const res1 = await getShopByCurUserGroup();
      this.shopList = res1.data.list
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .mycontainer{
  overflow: hidden;
}
.el-tag + .el-tag {
    margin-left: 10px;
  }
  .button-new-tag {
    margin-left: 10px;
    height: 20px;
    line-height: 20px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
  ::v-deep .linecontent{
    height: 52px !important;
  }
</style> 