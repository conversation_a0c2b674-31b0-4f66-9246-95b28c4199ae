<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <el-row>
                <el-form :model="data" label-width="130px" label-position="right" :disabled="true">
                    <el-form-item label="盘点时间：">
                        {{ (data.io_date == null ? "" : data.io_date) }}
                    </el-form-item>
                    <el-form-item label="审批时间：">
                        {{ (data.approvedTime == null ? "" : data.approvedTime) }}
                    </el-form-item>
                    <el-form-item label="备注：">
                        {{ data.remark }}
                    </el-form-item>
                    <el-form-item label="照片：">
                        <div style="display: flex; flex-direction: row;">
                            <div v-for="(i, j) in data.picUrls" :key="j">
                                <div>
                                    <el-image style="width: 50px; height: 50px;margin-left: 3px;" :src="i" :preview-src-list="data.picUrls">
                                    </el-image>
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                </el-form>
            </el-row>
        </template>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";

export default {
    name: "StoreStockTakingApproveLog",
    components: { MyContainer },
    props: {
        data:  { type: Object, default: {}}
    },
    data() {
        return {
            that: this,
            form: {
                hotSaleGoodsChooseId: '',
                directorId: ''
            },
            directorList: [],
            pageLoading: false,
            curRow: null,
            formEditMode: true,//是否编辑模式              
            mode: 1,
            listLoading: false
        };
    },
    computed: {

    }, 
    methods: { 
        
    },
};
</script>