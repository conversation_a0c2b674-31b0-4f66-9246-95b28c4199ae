<template>
    <my-container v-loading="pageLoading">
        <el-tabs v-model="activeName" style="height: 95%">
            <el-tab-pane label="商务BD" name="first1" style="height: 100%">
                <DyWiseManWorkUser ref="DyWiseManWorkUser" v-if="activeName=='first1'" style="height: 100%" :myGrouplist="grouplist"
                    :myBusinessManList="businessManList" :myShopList="shopList"></DyWiseManWorkUser>
            </el-tab-pane>
            <el-tab-pane label="抖音店铺" name="first3" style="height: 100%" lazy>
                <DyWiseManWorkShop ref="DyWiseManWorkShop" style="height: 100%" :myGrouplist="grouplist" v-if="activeName=='first3'"
                    :myBusinessManList="businessManList" :myShopList="shopList"></DyWiseManWorkShop>
            </el-tab-pane>
            <el-tab-pane label="快手店铺" name="first2" style="height: 100%" lazy>
                <KWaiShopShop ref="KWaiShopShop" style="height: 100%" :myGrouplist="grouplist" v-if="activeName=='first2'"
                    :myBusinessManList="businessManList" :myShopList="shopList"></KWaiShopShop>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import DyWiseManWorkUser from "./DyWiseManWorkUser.vue";
import DyWiseManWorkShop from "./DyWiseManWorkShop.vue";
import KWaiShopShop from '@/views/bookkeeper/reportday/WiseManWorkShop/KWaiShopShop.vue';
import {
    GetAllBusinessDetailList,
} from "@/api/bookkeeper/reportdayDouYin";
import { getDirectorGroupList, getAllList as getAllShopList } from '@/api/operatemanage/base/shop'
export default {
    name: "DyWiseManWorkIndex",//抖音商务BD业绩统计
    components: {
        MyContainer, DyWiseManWorkUser, DyWiseManWorkShop, KWaiShopShop,
    },
    data() {
        return {
            that: this,
            pageLoading: false,
            activeName: "first1",
            grouplist: [],
            businessManList: [],
            shopList: [],
        };
    },
    async mounted() {

        const res1 = await GetAllBusinessDetailList();
        this.businessManList = res1.data?.map(item => { return { value: item.businessMan, label: item.businessMan }; });

        const res2 = await getDirectorGroupList();
        this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

        const res3 = await getAllShopList({ platforms: [6] });
        this.shopList = res3.data?.map(item => { return { value: item.shopCode, label: item.shopName }; });
    },
    methods: {

    },
};
</script>

<style lang="scss" scoped></style>
