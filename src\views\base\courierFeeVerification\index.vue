<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 95%;" @tab-click="tabClick">
            <el-tab-pane label="仓库" name="first" style="height: 100%;" lazy>
                <warehouseDimensions @linkToDetail="linkToDetail" ref="warehouseDimensions" />
            </el-tab-pane>
            <el-tab-pane label="快递公司" name="second" style="height: 100%;" lazy>
                <courierCompanies @linkToDetail="linkToDetail" ref="courierCompanies" />
            </el-tab-pane>
            <el-tab-pane label="商品编码" name="third" style="height: 100%;" lazy>
                <productCode @linkToDetail="linkToDetail" />
            </el-tab-pane>
            <el-tab-pane label="明细" name="forth" style="height: 100%;" lazy>
                <billReview ref="billReview" />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from '@/components/my-container'
import warehouseDimensions from './components/warehouseDimensions.vue'
import productCode from './components/productCode.vue'
import courierCompanies from './components/courierCompanies.vue'
import billReview from './components/billReview.vue'
export default {
    components: {
        MyContainer,
        warehouseDimensions,
        productCode,
        courierCompanies,
        billReview
    },
    data() {
        return {
            activeName: 'first',
        }
    },
    mounted() { },
    methods: {
        linkToDetail(e) {
            this.activeName = 'forth'
            this.$nextTick(() => {
                this.$refs.billReview.getData(e)
            })
        }
    }
}
</script>

<style lang="scss" scoped></style>