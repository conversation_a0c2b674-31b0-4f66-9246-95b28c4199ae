<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button  style="padding:0;margin:0;">
                    <el-date-picker v-model="filter.timeRange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                        range-separator="至" start-placeholder="确认开始日期" end-placeholder="确认结束日期" style="width:230px;">
                    </el-date-picker>
                </el-button>                               
                <el-button  style="padding:0;margin:0;">
                    <el-input v-model.trim="filter.proCode" placeholder="产品ID" :clearable="true" ></el-input>
                </el-button>
                <el-button  style="padding:0;margin:0;">
                   <el-select filterable v-model="filter.afterSaleType" placeholder="请选择售后分类" clearable style="width: 130px">
                    <el-option label="补发" value="0"/>
                    <el-option label="换货" value="1"/>
                    <el-option label="仅退款" value="2"/>
                    <el-option label="拒绝退货" value="3"/>
                    <el-option label="普通退货" value="4"/>
                  </el-select>
                </el-button>
                 <el-button  style="padding:0;margin:0;">
                    <el-date-picker v-model="filter.timeRange1" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                        range-separator="至" start-placeholder="进仓开始日期" end-placeholder="进仓结束日期" style="width:230px;">
                    </el-date-picker>
                </el-button>    
                <el-button  style="padding:0;margin:0;">
                   <el-select filterable v-model="filter.orderStatus" placeholder="请选择订单状态" clearable style="width: 130px">
                    <el-option label="异常" value="异常"/>
                    <el-option label="已取消" value="已取消"/>
                    <el-option label="已发货" value="已发货"/>                  
                  </el-select>
                </el-button>
                <el-button  style="padding:0;margin:0;">
                   <el-select filterable v-model="filter.goodsStatus" placeholder="请选择货物状态" clearable style="width: 130px">
                    <el-option label="买家未收到货" value="买家未收到货"/>
                    <el-option label="卖家已收到退货" value="卖家已收到退货"/>
                    <el-option label="买家已退货" value="买家已退货"/>               
                  </el-select>
                </el-button>
               
                <el-button type="primary" @click="onSearch">查询</el-button>
              
            </el-button-group>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn="false"  
          :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='tableData' 
          :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:98%;margin: 0">
        </ces-table>
        <!--分页-->
        <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
        </template>
    </my-container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import MyContainer from '@/components/my-container';
import cesTable from '@/components/Table/table.vue';
import {
    pageRefundDetail,pageRefund
} from '@/api/financial/refund';
import {formatPlatform,formatYesornoBool,formatLinkProCode,platformlist} from "@/utils/tools";

const tableCols =[
     {istrue:true,prop:'orderNo',label:'订单号', width:'75',sortable:'custom'},
      {istrue:true,prop:'proCode',label:'宝贝ID',sortable:'custom', width:'80',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
      {istrue:true,prop:'platform',fix:true,label:'平台', width:'60',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
      {istrue:true,prop:'goodsCode',label:'商品编码',sortable:'custom', width:'80'},
      {istrue:true,prop:'shopCode',label:'店铺', width:'60',sortable:'custom',formatter:(row)=> row.shopName},
      {istrue:true,prop:'warehouse',label:'发货仓', width:'75',sortable:'custom',formatter:(row)=> row.warehouseStr},
      {istrue:true,prop:'warehouseReceive',label:'收货仓', width:'75',sortable:'custom'},
      {istrue:true,prop:'refundAmount',label:'退货金额',sortable:'custom', width:'80'},
      {istrue:true,prop:'refundCost',label:'退货成本',sortable:'custom', width:'80'},
      {istrue:true,prop:'refundCostSj',label:'实退成本',sortable:'custom', width:'80'},
      {istrue:true,prop:'orderTime',label:'订单日期',sortable:'custom', width:'90'},
      {istrue:true,prop:'timePay',label:'付款日期',sortable:'custom', width:'90'},
      {istrue:true,prop:'timeSend',label:'发货日期',sortable:'custom', width:'90',},
      {istrue:true,prop:'timeRefund',label:'确认日期',sortable:'custom', width:'85'},
      {istrue:true,prop:'timeStockIn',label:'进仓日期',sortable:'custom', width:'90'},
      {istrue:true,prop:'orderStatus',label:'订单状态',sortable:'custom', width:'auto'},
      {istrue:true,prop:'afterSaleType',label:'售后分类',sortable:'custom', width:'auto',formatter:(row)=> !row.afterSaleTypeName? " " : row.afterSaleTypeName},
    //   {istrue:true,prop:'typeProblem',label:'问题类型',sortable:'custom', width:'auto'},
      {istrue:true,prop:'goodsStatus',label:'货物状态',sortable:'custom', width:'auto'},
];

const tableHandles=[
       // {label:"导出", handle:(that)=>that.onExport()},
      ];

const startDate = formatTime(dayjs().subtract(1,'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default ({
    name:"Users",
    components:{MyContainer,cesTable},
    data(){
        return {
          
            that:this, 
            myfilter:{
                //proCode:null,
                // timeRange:[startDate,endDate],
                // startDate:null,
                // endDate:null,
                orderNo:null,
                orderNoInner:null,
                expressNo:null
            },                      
            tableCols:tableCols,
            tableHandles:tableHandles,
            tableData:[],
            total: 0,
            pager:{OrderBy:"",IsAsc:false},
            listLoading: false,
            pageLoading: false,
            summaryarry:{},    
            sels:[],

        };
    },
    props:{
        filter:{
            proCode:null,
            timeRange:[startDate,endDate],
            StartTime:null,
            EndTime:null,
            orderStatus:"",
            goodsStatus:"",
            timeRange1:[],
            platform:null

            //orderNo:null,
            //orderNoInner:null
        },   
    },

    methods:{
        async sortchange(column){
            if(!column.order)
                this.pager={};
            else
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
            await this.onSearch();
            },
            async onSearch(){
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        async getList(){
            this.filter.StartTime =null;
            this.filter.EndTime =null;
            this.filter.StartTimeStockIn =null;
            this.filter.EndTimeStockIn =null;
            if (this.filter.timeRange && this.filter.timeRange.length>0) {
                this.filter.StartTime = this.filter.timeRange[0];
                this.filter.EndTime = this.filter.timeRange[1];
            }
               if (this.filter.timeRange1 && this.filter.timeRange1.length>0) {
               this.filter.StartTimeStockIn = this.filter.timeRange1[0];
              this.filter.EndTimeStockIn = this.filter.timeRange1[1];
            }
            this.filter
            var that=this;
            this.listLoading=true;
            var pager = this.$refs.pager.getPager();
            const params = {...pager,...this.pager,...this.filter,...this.myfilter};
            console.log('最终参数',params)
            const res = await pageRefund(params).then(res=>{
                that.total = res.data?.total;
                console.log("返回数据",res)
                that.tableData = res.data?.list;
                that.summaryarry=res.data?.summary;               
            });
            this.listLoading=false;
        },
        
    }
})
</script>
<style scoped>
    ::v-deep .el-table__fixed-footer-wrapper tbody td{
        color:blue;
    }
</style>

