<template>
    <div>
        <div class="tjgd" style="display: flex; flex-direction: row;">
            <div class="flexrow">
                <span>
                    <el-date-picker  style="position: relative; top: 1px; width: 400px"
                        type="daterange" align="right" unlink-panels range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" v-model="filter.createdtimerange">
                    </el-date-picker>
                </span>
                <span>
                    <el-button type="primary" v-throttle="3000" @click="onSearch(1)">查询</el-button>
                </span>
                <div class="flexrow">
                    <el-checkbox style="margin: 8px" v-model="filter.isProcess">加工组</el-checkbox>
                    <el-checkbox style="margin: 8px" v-model="filter.isPackages">包装组</el-checkbox>
                </div>
            </div>
            <div class="flexcolum" style=" width: 100%;">
                <div class="nerbox">
                    <el-checkbox style="margin: 5px 8px" v-model="filter.isTask">任务列表</el-checkbox>
                    <el-checkbox style="margin: 5px 8px" v-model="filter.isComplete">已完成</el-checkbox>
                    <el-checkbox style="margin: 5px 8px" v-model="filter.isStat">统计列表</el-checkbox>
                    <el-checkbox style="margin: 5px 8px" v-model="filter.isArchive">存档</el-checkbox>
                </div>


            </div>

        </div>
        <div class="sybj">

            <!--任务状态 start-->
            <div class="tjnrk1">
                <div class="tjnrnk">
                    <div class="tjbt">
                        <span>任务状态</span>
                    </div>
                    <div style="height: 230px" class="nrqk">
                        <div class="bzjgsj">
                            <div class="sztjk" style="width:40%;">
                                <div>
                                    <div class="tjsz">{{ formattedTotalWaitOrderNum1 }}</div>
                                    <div class="tjmc">加工总数</div>
                                </div>
                            </div>
                            <div class="sztjk">
                                <div>
                                    <div class="tjsz">{{ formattedTotalWaitOrderNum2 }}</div>
                                    <div class="tjmc">任务总数</div>
                                </div>
                            </div>
                            <div class="sztjk">
                                <div>
                                    <div class="tjsz">{{ formattedTotalWaitOrderNum3 }}</div>
                                    <div class="tjmc">加工编码</div>
                                </div>
                            </div>
                            <div class="sztjk">
                                <div>
                                    <div class="tjsz">{{ formattedTotalWaitOrderNum4 }}</div>
                                    <div class="tjmc">参与加工员</div>
                                </div>
                            </div>
                            <div class="sztjk">
                                <div>
                                    <div class="tjsz">{{ formattedTotalWaitOrderNum5 }}</div>
                                    <div class="tjmc">组合编码数</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--任务状态 end-->

            <!--包装品牌统计 start-->
            <div class="tjnrk2">
                <div class="tjnrnk">
                    <div class="tjbt">
                        <span>包装品牌</span>
                    </div>
                    <div style="height: 230px" class="nrqk">
                        <div id="jgbzpp" style="width: 30vw; height: 230px; margin: 0 auto"></div>
                    </div>
                </div>
            </div>
            <!--包装品牌统计 end-->

            <!--订单占比 start-->
                <statdailyorder :showHome="true" ref="datastatdailyorder"></statdailyorder>
            <!--订单占比 end-->

            <!--加工统计 start-->
            <div class="tjnrk10">
                <div class="tjnrnk">
                    <div class="tjbt">
                        <span>加工统计</span>
                    </div>
                    <div style="height: 360px" class="nrqk">
                        <div id="bzgjhj" style="width: 82vw; height: 360px; margin: 0 auto"></div>
                    </div>
                </div>
            </div>
            <!--加工统计 end-->

            <!--任务统计 start-->
            <div class="tjnrk6">
                <div class="tjnrnk">
                    <div class="tjbt">
                        <span>任务统计</span>
                    </div>
                    <div style="height: 300px" class="nrqk">
                        <div id="jgbzrw" style="width: 82vw; height: 300px; margin: 0 auto"></div>
                    </div>
                </div>
            </div>
            <!--任务统计计 end-->



            <!--包装材料统计 start-->
            <div class="tjnrk3">
                <div class="tjnrnk">
                    <div class="tjbt">
                        <span>包装材料</span>
                    </div>
                    <div style="height: 300px" class="nrqk">
                        <div id="jgbzcl" style="width: 38vw; height: 300px; margin: 0 auto"></div>
                    </div>
                </div>
            </div>
            <!--包装材料统计 end-->

            <!--包装机型统计 start-->
            <div class="tjnrk4">
                <div class="tjnrnk">
                    <div class="tjbt">
                        <span>包装机型</span>
                    </div>
                    <div style="height: 300px" class="nrqk">
                        <div id="jgbzjx" style="width: 38vw; height: 300px; margin: 0 auto"></div>
                    </div>
                </div>
            </div>
            <!--包装机型统计 end-->

            <!--包装尺寸统计 start-->
            <div class="tjnrk5">
                <div class="tjnrnk">
                    <div class="tjbt">
                        <span>包装尺寸</span>
                    </div>
                    <div style="height: 300px" class="nrqk">
                        <div id="jgbzcc" style="width: 82vw; height: 300px; margin: 0 auto"></div>
                    </div>
                </div>
            </div>
            <!--包装尺寸统计 end-->





            <div style="height:110px;"></div>
        </div>

        <el-dialog  title="个人任务数据趋势"  :visible.sync="view9filter.view9show"
            width="80%"
            element-loading-text="拼命加载中"
            :close-on-click-modal="true"
            :append-to-body="true"  >
            <div  style="display:flex; flex-direction: row;">
                <!-- <el-button style="padding: 0;">
                    <el-select  filterable v-model="view10filter.groupName" placeholder="人员" clearable :collapse-tags="true" >
                        <el-option v-for="item in groupNames" :key="item" :label="item" :value="item"     />
                    </el-select>
                </el-button>  -->
                <el-date-picker  style="position: relative; top: 1px; width: 350px"
                        type="daterange" align="right" unlink-panels range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" v-model="view9filter.createdtimerange">
                </el-date-picker>
                <el-button type="primary" style="margin-left: 15px;" v-throttle="3000" @click="onSearch(9)">查询</el-button>
                <div style="width: 530px; margin: 0px 0 0 auto">
                    <el-checkbox style="margin: 5px 8px" v-model="view9filter.isTask">任务列表</el-checkbox>
                    <el-checkbox style="margin: 5px 8px" v-model="view9filter.isComplete">已完成</el-checkbox>
                    <el-checkbox style="margin: 5px 8px" v-model="view9filter.isStat">统计列表</el-checkbox>
                    <el-checkbox style="margin: 5px 8px" v-model="view9filter.isArchive">存档</el-checkbox>
                </div>
            </div>
            <div>
                <div id="view9charts" style="width: 72vw; height: 300px; margin: 0 auto"></div>
            </div>

        </el-dialog>

        <el-dialog  title="个人数据趋势"  :visible.sync="view10filter.view10show"
            width="80%"
            element-loading-text="拼命加载中"
            :close-on-click-modal="true"
            :append-to-body="true"  >
            <div  style="display:flex; flex-direction: row;">
                <!-- <el-button style="padding: 0;">
                    <el-select  filterable v-model="view10filter.groupName" placeholder="人员" clearable :collapse-tags="true" >
                        <el-option v-for="item in groupNames" :key="item" :label="item" :value="item"     />
                    </el-select>
                </el-button>  -->
                <el-date-picker  style="position: relative; top: 1px; width: 350px;"
                        type="daterange" align="right" unlink-panels range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" v-model="view10filter.createdtimerange">
                </el-date-picker>
                    <el-button type="primary" style="margin-left: 15px;" v-throttle="3000" @click="onSearch(10)">查询</el-button>
                <div style="width: 380px; margin: 0px 0 0 auto">
                    <el-checkbox style="margin: 5px 8px" v-model="view10filter.isTask">任务列表</el-checkbox>
                    <el-checkbox style="margin: 5px 8px" v-model="view10filter.isComplete">已完成</el-checkbox>
                    <el-checkbox style="margin: 5px 8px" v-model="view10filter.isStat">统计列表</el-checkbox>
                    <el-checkbox style="margin: 5px 8px" v-model="view10filter.isArchive">存档</el-checkbox>
                </div>
            </div>
            <div>
                <div id="view10charts" style="width: 72vw; height: 300px; margin: 0 auto"></div>
            </div>

        </el-dialog>
    </div>
</template>

<script>
import * as echarts from "echarts";
import { getStatQuantity,getStatPackageSizeList,getStatMachineTypeList,getStatMaterialList,getStatBrandList, getStatTaskList, getStatProcessList,
     getStatTaskPopList, getStatProcessPopList
} from '@/api/inventory/packagesprocess';//包装加工
import statdailyorder from '@/views/media/packagework/statdailyorder.vue';

export default {
    components: {statdailyorder},
    data(){
        return{
            getStatBrandListdata: {},
            getStatMaterialListdata: {},
            getStatMachineTypeListdata: {},
            getStatPackageSizeListdata: {},
            getStatQuantitydata: {},
            getStatTaskListdata: {},
            getStatProcessListdata: {},

            filter: {
                createdtimerange: [],
                isArchive: false,
                isStat: false,
                isComplete: true,
                isTask: true,
                isPackages: false,
                isProcess: false
            },
            view9filter: {
                view9show: false,
                createdtimerange: [],
                isArchive: false,
                isStat: false,
                isComplete: true,
                isTask: true,
            },
            view10filter: {
                view10show: false,
                createdtimerange: [],
                isArchive: false,
                isStat: false,
                isComplete: true,
                isTask: true,
            }
        }
    },
    mounted() {
        this.onSearch(1)
        // this.sevenchart();
        // this.eightchart();
    },
    watch: {
        "filter.isProcess": {
            handler(newVal, oldVal) {
                if(!newVal){
                    return
                }
                this.filter.isPackages = oldVal;
            },
        },
        "filter.isPackages": {
            handler(newVal, oldVal) {
                if(!newVal){
                    return
                }
                this.filter.isProcess = oldVal;
            },
        }
    },
    computed: {
        formattedTotalWaitOrderNum1() {
            return this.getStatQuantitydata.totalProcesses !== undefined && this.getStatQuantitydata.totalProcesses !== null ? this.formatNumber(this.getStatQuantitydata.totalProcesses) : '';
        },
        formattedTotalWaitOrderNum2() {
            return this.getStatQuantitydata.totalTask !== undefined && this.getStatQuantitydata.totalTask !== null ? this.formatNumber(this.getStatQuantitydata.totalTask) : '';
        },
        formattedTotalWaitOrderNum3() {
            return this.getStatQuantitydata.totalCode !== undefined && this.getStatQuantitydata.totalCode !== null ? this.formatNumber(this.getStatQuantitydata.totalCode) : '';
        },
        formattedTotalWaitOrderNum4() {
            return this.getStatQuantitydata.totalWorker !== undefined && this.getStatQuantitydata.totalWorker !== null ? this.formatNumber(this.getStatQuantitydata.totalWorker) : '';
        },
        formattedTotalWaitOrderNum5() {
            return this.getStatQuantitydata.combineCode !== undefined && this.getStatQuantitydata.combineCode !== null ? this.formatNumber(this.getStatQuantitydata.combineCode) : '';
        },
    },
    methods: {
         // 格式化数值的函数
         formatNumber(value){
            const absNumber = Math.abs(value);
            const isInteger =Number.isInteger(value);
            const options ={
            minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
            maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
            useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value);     
                },  

        async onSearch(num) {
            switch(num){
                case 1:
                    if (this.filter.createdtimerange) {
                        this.filter.startDate = this.filter.createdtimerange[0];
                        this.filter.endDate = this.filter.createdtimerange[1];
                    } else {
                        this.filter.startDate = null;
                        this.filter.endDate = null;
                    }
                    await this.getallmsg();
                    break;
                case 9:
                    // if (this.view9filter.createdtimerange) {
                    //     this.view9filter.startDate = this.view9filter.createdtimerange[0];
                    //     this.view9filter.endDate = this.view9filter.createdtimerange[1];
                    // } else {
                    //     this.view9filter.startDate = null;
                    //     this.view9filter.endDate = null;
                    // }
                    this.view9filter.startDate = null;
                    this.view9filter.endDate = null;
                    await this.getStatTaskPopListfuc();
                    await this.initeightchart();
                    break;
                case 10:
                    // if (this.view10filter.createdtimerange) {
                    //     this.view10filter.startDate = this.view10filter.createdtimerange[0];
                    //     this.view10filter.endDate = this.view10filter.createdtimerange[1];
                    // } else {
                    //     this.view10filter.startDate = null;
                    //     this.view10filter.endDate = null;
                    // }
                    this.view10filter.startDate = null;
                    this.view10filter.endDate = null;
                    await this.getStatProcessPopListfuc();
                    await this.initsevenchart();
                    break;
            }
        },
        async getallmsg(){
            await this.getStatQuantityfuc();//任务状态
            await this.getStatBrandListfuc();//包装品牌

            await this.getStatMaterialListfuc(); //包装材料
            await this.getStatMachineTypeListfuc();//包装机型

            await this.getStatPackageSizeListfuc();//包装尺寸

            await this.getStatTaskListfuc();//任务统计
            await this.getStatProcessListfuc();//加工统计
            await this.initchart();
        },
        initchart(){
            this.onechart();
            this.twochart();
            this.threechart();
            this.fourchart();
            this.fivechart();
            this.sixchart();
        },
        async getStatQuantityfuc(){
            let params = {
                ...this.filter
            }
            const res = await getStatQuantity(params);
            if (!res?.success) {
                return
            }
            this.getStatQuantitydata = res.data;
        },
        async getStatPackageSizeListfuc(){
            let params = {
                ...this.filter
            }
            const res = await getStatPackageSizeList(params);
            if (!res) {
                return
            }
            this.getStatPackageSizeListdata = res;
        },
        async getStatMachineTypeListfuc(){
            let params = {
                ...this.filter
            }
            const res = await getStatMachineTypeList(params);
            if (!res) {
                return
            }
            this.getStatMachineTypeListdata = res;
        },
        async getStatMaterialListfuc(){
            let params = {
                ...this.filter
            }
            const res = await getStatMaterialList(params);
            if (!res) {
                return
            }
            this.getStatMaterialListdata = res;
        },
        async getStatBrandListfuc(){
            let params = {
                ...this.filter
            }
            const res = await getStatBrandList(params);
            if (!res) {
                return
            }
            this.getStatBrandListdata = res;
        },

        async getStatTaskListfuc(){
            let params = {
                ...this.filter
            }
            const res = await getStatTaskList(params);
            if (!res) {
                return
            }
            this.getStatTaskListdata = res;
        },
        async getStatProcessListfuc(){
            let params = {
                ...this.filter
            }
            const res = await getStatProcessList(params);
            if (!res) {
                return
            }
            this.getStatProcessListdata = res;
        },

        deleteRow(index, rows) {
            rows.splice(index, 1);
        },

        //  品牌图表 star

        onechart() {
            var myChart = echarts.init(document.getElementById("jgbzpp"));
            // this.getStatBrandListdata
            var option = {
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "cross",
                        crossStyle: {
                            color: "#999",
                        },
                    },
                },
                toolbox: {
                    show: true,
                    orient: "vertical",
                    left: "right",
                    top: "center",
                    feature: {
                        mark: { show: true },
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ["line", "bar", "stack"] },
                        restore: { show: true },
                        saveAsImage: { show: true },
                    },
                },
                legend: {
                    top: 5,
                    data: [""],
                },
                xAxis: [
                    {
                        type: "category",
                        data: this.getStatBrandListdata.xAxis,
                        axisPointer: {
                            type: "shadow",
                        },
                        axisLabel: {
                            interval: 0,
                        }
                    },
                ],
                yAxis: [
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                    {
                        type: "value",
                        name: "",
                        interval: 5,
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                ],

                grid: {
                    top: "45px",
                    bottom: "60px",
                    left: "50",
                    right: "65",
                },

                series: [
                    {
                        name: "加工数",
                        type: "bar",
                        id: "sales",
                        barGap: "5%",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }
                        },
                        emphasis: {
                            focus: "series",
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value) + "";
                            },
                        },
                        data: this.getStatBrandListdata.series[0].data,
                    },
                ],
            };
            const drilldownData = [];
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            }            
            myChart.on("click", function (event) {
                if (event.data) {
                    var subData = drilldownData.find(function (data) {
                        return data.dataGroupId === event.data.groupId;
                    });
                    if (!subData) {
                        return;
                    }
                    myChart.setOption({
                        xAxis: {
                            data: subData.data.map(function (item) {
                                return item[0];
                            }),
                        },
                        series: [
                            {
                                type: "bar",
                                id: "sales",
                                dataGroupId: subData.dataGroupId,
                                data: subData.data.map(function (item) {
                                    return item[1];
                                }),
                                universalTransition: {
                                    enabled: true,
                                    divideShape: "clone",
                                },
                            },
                        ],
                        graphic: [
                            {
                                type: "text",
                                left: "right",
                                top: 12,
                                style: {
                                    text: "Back",
                                    fontSize: 14,
                                },
                                onclick: function () {
                                    myChart.setOption(option);
                                },
                            },
                        ],
                    });
                }
            });

            myChart.setOption(option);
        },

        //  品牌图表 end

        //  包装材料 star

        twochart() {
            var myChart = echarts.init(document.getElementById("jgbzcl"));

            // var option = this.getStatMaterialListdata;
            var option = {
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "cross",
                        crossStyle: {
                            color: "#999",
                        },
                    },
                },
                toolbox: {
                    show: true,
                    orient: "vertical",
                    left: "right",
                    top: "center",
                    feature: {
                        mark: { show: true },
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ["line", "bar", "stack"] },
                        restore: { show: true },
                        saveAsImage: { show: true },
                    },
                },
                legend: {
                    top: 5,
                    data: [""],
                },
                xAxis: [
                    {
                        type: "category",
                        data: this.getStatMaterialListdata.xAxis,
                        axisPointer: {
                            type: "shadow",
                        },
                        axisLabel: {
                            interval: 0,
                        }
                    },
                ],
                yAxis: [
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                    {
                        type: "value",
                        name: "",
                        interval: 5,
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                ],

                grid: {
                    top: "45px",
                    bottom: "60px",
                    left: "50",
                    right: "65",
                },

                series: [
                    {
                        name: "加工数",
                        type: "bar",
                        id: "sales",
                        barGap: "5%",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }
                        },
                        emphasis: {
                            focus: "series",
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value) + "";
                            },
                        },
                        data: this.getStatMaterialListdata.series[0].data,
                    },
                ],
            };

            const drilldownData = [];
            myChart.on("click", function (event) {
                if (event.data) {
                    var subData = drilldownData.find(function (data) {
                        return data.dataGroupId === event.data.groupId;
                    });
                    if (!subData) {
                        return;
                    }
                    myChart.setOption({
                        xAxis: {
                            data: subData.data.map(function (item) {
                                return item[0];
                            }),
                        },
                        series: [
                            {
                                type: "bar",
                                id: "sales",
                                dataGroupId: subData.dataGroupId,
                                data: subData.data.map(function (item) {
                                    return item[1];
                                }),
                                universalTransition: {
                                    enabled: true,
                                    divideShape: "clone",
                                },
                            },
                        ],
                        graphic: [
                            {
                                type: "text",
                                left: "right",
                                top: 12,
                                style: {
                                    text: "Back",
                                    fontSize: 14,
                                },
                                onclick: function () {
                                    myChart.setOption(option);
                                },
                            },
                        ],
                    });
                }
            });
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            } 
            myChart.setOption(option);
        },

        //  包装材料 end

        //  包装机型 star

        threechart() {
            var myChart = echarts.init(document.getElementById("jgbzjx"));

            // var option = this.getStatMachineTypeListdata;
            var option = {
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "cross",
                        crossStyle: {
                            color: "#999",
                        },
                    },
                },
                toolbox: {
                    show: true,
                    orient: "vertical",
                    left: "right",
                    top: "center",
                    feature: {
                        mark: { show: true },
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ["line", "bar", "stack"] },
                        restore: { show: true },
                        saveAsImage: { show: true },
                    },
                },
                legend: {
                    top: 5,
                    data: [""],
                },
                xAxis: [
                    {
                        type: "category",
                        data: this.getStatMachineTypeListdata.xAxis,
                        axisPointer: {
                            type: "shadow",
                        },
                        axisLabel: {
                            interval: 0,
                        }
                    },
                ],
                yAxis: [
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                    {
                        type: "value",
                        name: "",
                        interval: 5,
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                ],

                grid: {
                    top: "45px",
                    bottom: "60px",
                    left: "50",
                    right: "65",
                },

                series: [
                    {
                        name: "加工数",
                        type: "bar",
                        id: "sales",
                        barGap: "5%",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }
                        },
                        emphasis: {
                            focus: "series",
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value) + "";
                            },
                        },
                        data: this.getStatMachineTypeListdata.series[0].data,
                    },
                ],
            };

            const drilldownData = [];
            myChart.on("click", function (event) {
                if (event.data) {
                    var subData = drilldownData.find(function (data) {
                        return data.dataGroupId === event.data.groupId;
                    });
                    if (!subData) {
                        return;
                    }
                    myChart.setOption({
                        xAxis: {
                            data: subData.data.map(function (item) {
                                return item[0];
                            }),
                        },
                        series: [
                            {
                                type: "bar",
                                id: "sales",
                                dataGroupId: subData.dataGroupId,
                                data: subData.data.map(function (item) {
                                    return item[1];
                                }),
                                universalTransition: {
                                    enabled: true,
                                    divideShape: "clone",
                                },
                            },
                        ],
                        graphic: [
                            {
                                type: "text",
                                left: "right",
                                top: 12,
                                style: {
                                    text: "Back",
                                    fontSize: 14,
                                },
                                onclick: function () {
                                    myChart.setOption(option);
                                },
                            },
                        ],
                    });
                }
            });
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            } 
            myChart.setOption(option);
        },

        //  包装机型 end

        //  包装尺寸 star

        fourchart() {
            var myChart = echarts.init(document.getElementById("jgbzcc"));

            // var option = this.getStatPackageSizeListdata;
            var option = {
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "cross",
                        crossStyle: {
                            color: "#999",
                        },
                    },
                },
                toolbox: {
                    show: true,
                    orient: "vertical",
                    left: "right",
                    top: "center",
                    feature: {
                        mark: { show: true },
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ["line", "bar", "stack"] },
                        restore: { show: true },
                        saveAsImage: { show: true },
                    },
                },
                legend: {
                    top: 5,
                    data: [""],
                },
                xAxis: [
                    {
                        type: "category",
                        data: this.getStatPackageSizeListdata.xAxis,
                        axisPointer: {
                            type: "shadow",
                        },
                        axisLabel: {
                            interval: 0,
                        }
                    },
                ],
                yAxis: [
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                    {
                        type: "value",
                        name: "",
                        interval: 5,
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                ],

                grid: {
                    top: "45px",
                    bottom: "60px",
                    left: "50",
                    right: "65",
                },

                series: [
                    {
                        name: "加工数",
                        type: "bar",
                        id: "sales",
                        barGap: "5%",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }
                        },
                        emphasis: {
                            focus: "series",
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value) + "";
                            },
                        },
                        data: this.getStatPackageSizeListdata.series[0].data,
                    },
                ],
            };

            const drilldownData = [];
            myChart.on("click", function (event) {
                if (event.data) {
                    var subData = drilldownData.find(function (data) {
                        return data.dataGroupId === event.data.groupId;
                    });
                    if (!subData) {
                        return;
                    }
                    myChart.setOption({
                        xAxis: {
                            data: subData.data.map(function (item) {
                                return item[0];
                            }),
                        },
                        series: [
                            {
                                type: "bar",
                                id: "sales",
                                dataGroupId: subData.dataGroupId,
                                data: subData.data.map(function (item) {
                                    return item[1];
                                }),
                                universalTransition: {
                                    enabled: true,
                                    divideShape: "clone",
                                },
                            },
                        ],
                        graphic: [
                            {
                                type: "text",
                                left: "right",
                                top: 12,
                                style: {
                                    text: "Back",
                                    fontSize: 14,
                                },
                                onclick: function () {
                                    myChart.setOption(option);
                                },
                            },
                        ],
                    });
                }
            });
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            } 
            myChart.setOption(option);
        },

        //  包装尺寸 end

        //  任务统计 star

        fivechart() {
            var myChart = echarts.init(document.getElementById("jgbzrw"));
            // this.getStatTaskListdata
            var option = {
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "cross",
                        crossStyle: {
                            color: "#999",
                        },
                    },
                },
                toolbox: {
                    show: true,
                    orient: "vertical",
                    left: "right",
                    top: "center",
                    feature: {
                        mark: { show: true },
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ["line", "bar", "stack"] },
                        restore: { show: true },
                        saveAsImage: { show: true },
                    },
                },
                legend: {
                    top: 5,
                    data: [""],
                },
                xAxis: [
                    {
                        type: "category",
                        data: this.getStatTaskListdata.xAxis,
                        axisPointer: {
                            type: "shadow",
                        },
                        axisLabel: {
                            interval: 0,
                        }
                    },
                ],
                yAxis: [
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                    {
                        type: "value",
                        name: "",
                        interval: 5,
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                ],

                grid: {
                    top: "45px",
                    bottom: "60px",
                    left: "50",
                    right: "65",
                },

                series: [
                    {
                        name: "加工数",
                        type: "bar",
                        id: "sales",
                        barGap: "5%",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }
                        },
                        emphasis: {
                            focus: "series",
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value) + "";
                            },
                        },
                        data: this.getStatTaskListdata.series[0].data,
                    },
                ],
                graphic: [
                    {
                        type: "text",
                        left: "right",
                        top: 12,
                        style: {
                            text: "",
                            fontSize: 14,
                        },

                    }
                ],
            };
            myChart.on("click", async (event)=> {
                // this.view9filter.createdtimerange = this.filter.createdtimerange;
                // this.view10filter.createdtimerange = this.filter.createdtimerange;
                if (this.view10filter.createdtimerange) {
                        this.view10filter.startDate = this.view10filter.createdtimerange[0];
                        this.view10filter.endDate = this.view10filter.createdtimerange[1];
                    } else {
                        this.view10filter.startDate = null;
                        this.view10filter.endDate = null;
                    }
                this.view9filter.userName = event.name;
                this.view9filter.view9show = true;
                await this.getStatTaskPopListfuc();
                await this.initeightchart();
                // if (event.data) {
                //     var subData = drilldownData.find(function (data) {
                //         return data.dataGroupId === event.data.groupId;
                //     });
                //     if (!subData) {
                //         return;
                //     }
                //     myChart.setOption({
                //         legend: {
                //             top: 5,
                //             data: ["个人任务", "总任务"],
                //         },
                //         xAxis: {
                //             data: this.sxDataDetail.xAxis,
                //         },
                //         series: [
                //             {
                //                 name: "个人任务",
                //                 label: {
                //                     show: true,
                //                     position: "top",
                //                 },
                //                 type: "line",
                //                 id: "sales",
                //                 data: this.sxDataDetail.series[1].data,
                //                 universalTransition: {
                //                     enabled: true,
                //                     divideShape: "clone",
                //                 },
                //             },
                //             {
                //                 name: "总任务",
                //                 type: "line",
                //                 id: "sales2",
                //                 label: {
                //                     show: true,
                //                     position: "top",
                //                 },
                //                 data: this.sxDataDetail.series[0].data,
                //                 universalTransition: {
                //                     enabled: true,
                //                     divideShape: "clone",
                //                 },
                //             },
                //         ],
                //         graphic: [
                //             {
                //                 type: "text",
                //                 left: "right",
                //                 top: 12,
                //                 style: {
                //                     text: "Back",
                //                     fontSize: 14,
                //                 },
                //                 onclick: function () {
                //                     myChart.setOption(option);
                //                 },
                //             },
                //         ],
                //     });
                // }
            });
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            } 
            myChart.setOption(option);
        },

        //  任务统计 end

        //  加工统计图表 star

        sixchart() {
            var myChart = echarts.init(document.getElementById("bzgjhj"));
            // this.getStatProcessListdata
            var option = {
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "cross",
                        crossStyle: {
                            color: "#999",
                        },
                    },
                },
                toolbox: {
                    show: true,
                    orient: "vertical",
                    left: "right",
                    top: "center",
                    feature: {
                        mark: { show: true },
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ["line", "bar", "stack"] },
                        restore: { show: true },
                        saveAsImage: { show: true },
                    },
                },
                legend: {
                    top: 5,
                    data: this.getStatProcessListdata.legend,
                    selected: {
                        '加工总数': true,
                        '加工统计': false,
                        '参与编码': false,
                    }
                },
                xAxis: [
                    {
                        type: "category",
                        data: this.getStatProcessListdata.xAxis,
                        axisPointer: {
                            type: "shadow",
                        },
                        axisLabel: {
                            interval: 0,
                        }
                    },
                ],
                yAxis: [
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                ],

                grid: {
                    top: "45px",
                    bottom: "60px",
                    left: "100",
                    right: "65",
                },

                series: [
                    {
                        name: "加工总数",
                        type: "bar",
                        id: "sales",
                        barGap: "5%",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }
                        },
                        emphasis: {
                            focus: "series",
                        },
                        // yAxisIndex: 1,
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value) + "";
                            },
                        },
                        data: this.getStatProcessListdata.series[0].data,
                    },
                    {
                        name: "加工统计",
                        type: "bar",
                        barGap: "5%",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }
                        },
                        emphasis: {
                            focus: "series",
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value) + "";
                            },
                        },
                        data: this.getStatProcessListdata.series[2].data,
                    },
                    {
                        name: "参与编码",
                        type: "bar",
                        barGap: "5%",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }
                        },
                        emphasis: {
                            focus: "series",
                        },
                        yAxisIndex: 1,
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value) + "";
                            },
                        },
                        data: this.getStatProcessListdata.series[1].data,
                    },


                ],
            };
            const drilldownData = [];
            myChart.on("click", async (event)=> {
                // this.view9filter.createdtimerange = this.filter.createdtimerange;
                // this.view10filter.createdtimerange = this.filter.createdtimerange;
                if (this.view10filter.createdtimerange) {
                        this.view10filter.startDate = this.view10filter.createdtimerange[0];
                        this.view10filter.endDate = this.view10filter.createdtimerange[1];
                    } else {
                        this.view10filter.startDate = null;
                        this.view10filter.endDate = null;
                    }
                this.view10filter.userName = event.name;
                this.view10filter.view10show = true;
                await this.getStatProcessPopListfuc();
                await this.initsevenchart();
                // if (event.data) {
                //     var subData = drilldownData.find(function (data) {
                //         return data.dataGroupId === event.data.groupId;
                //     });
                //     if (!subData) {
                //         return;
                //     }
                //     myChart.setOption({
                //         xAxis: {
                //             data: subData.data.map(function (item) {
                //                 return item[0];
                //             }),
                //         },
                //         series: [
                //             {
                //                 type: "bar",
                //                 id: "sales",
                //                 dataGroupId: subData.dataGroupId,
                //                 data: subData.data.map(function (item) {
                //                     return item[1];
                //                 }),
                //                 universalTransition: {
                //                     enabled: true,
                //                     divideShape: "clone",
                //                 },
                //             },
                //         ],
                //         graphic: [
                //             {
                //                 type: "text",
                //                 left: "right",
                //                 top: 12,
                //                 style: {
                //                     text: "Back",
                //                     fontSize: 14,
                //                 },
                //                 onclick: function () {
                //                     myChart.setOption(option);
                //                 },
                //             },
                //         ],
                //     });
                // }
            });
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            } 
            myChart.setOption(option);
        },
        async getStatProcessPopListfuc(){//加工统计
            const res = await getStatProcessPopList(this.view10filter);
            if (!res) {
                return
            }
            this.getStatProcessPopListdata = res;
        },
        async getStatTaskPopListfuc(){//任务统计
            const res = await getStatTaskPopList(this.view9filter);
            if (!res) {
                return
            }
            this.getStatTaskPopListdata = res;
        },
        // async view10onSearch(){
        //     if (this.view10filter.createdtimerange) {
        //         this.view10filter.startDate = this.view10filter.createdtimerange[0];
        //         this.view10filter.endDate = this.view10filter.createdtimerange[1];
        //     } else {
        //         this.view10filter.startDate = null;
        //         this.view10filter.endDate = null;
        //     }
        //     await this.getStatProcessPopListfuc();
        //     await this.initsevenchart();
        // },
        //  加工统计图表 end

        //加工内嵌
        initsevenchart(){
            // this.getStatProcessPopListdata
            var myChart = echarts.init(document.getElementById("view10charts"));
            var option = {
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "cross",
                        crossStyle: {
                            color: "#999",
                        },
                    },
                },
                toolbox: {
                    show: true,
                    orient: "vertical",
                    left: "right",
                    top: "center",
                    feature: {
                        mark: { show: true },
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ["line", "bar", "stack"] },
                        restore: { show: true },
                        saveAsImage: { show: true },
                    },
                },
                legend: {
                    top: 5,
                    data: this.getStatProcessPopListdata.legend,
                    // selected: {
                    //     '工时': false,
                    //     '加工数': true,
                    //     '加工统计': false,
                    // }
                },
                xAxis: [
                    {
                        type: "category",
                        data: this.getStatProcessPopListdata.xAxis,
                        axisPointer: {
                            type: "shadow",
                        },
                    },
                ],
                yAxis: [
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                    {
                        type: "value",
                        name: "",
                        // interval: 5,
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                ],

                grid: {
                    top: "45px",
                    bottom: "60px",
                    left: "50",
                    right: "65",
                },
                // series: this.getStatProcessPopListdata.series,
                series: [
                {
                        name: this.getStatProcessPopListdata.series[0].name,
                        type: "line",
                        id: "sales",
                        barGap: "5%",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                return formatValue(params.value);
                                } 
                        },
                        emphasis: {
                            focus: "series",
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value) + "";
                            },
                        },
                        data: this.getStatProcessPopListdata.series[0].data,
                    },
                    {
                        name: this.getStatProcessPopListdata.series[1].name,
                        type: "line",
                        id: "sales2",
                        barGap: "5%",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                return formatValue(params.value);
                                } 
                        },
                        emphasis: {
                            focus: "series",
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value) + "";
                            },
                        },
                        data: this.getStatProcessPopListdata.series[1].data,
                    },
                    // {
                    //     name: this.getStatProcessPopListdata.series[2].name,
                    //     type: "line",
                    //     id: "sales2",
                    //     barGap: "5%",
                    //     label: {
                    //         show: true,
                    //         position: "top",
                    //     },
                    //     emphasis: {
                    //         focus: "series",
                    //     },
                    //     tooltip: {
                    //         valueFormatter: function (value) {
                    //             return value + "";
                    //         },
                    //     },
                    //     data: this.getStatProcessPopListdata.series[2].data,
                    // },
                ],
                graphic: [
                    {
                        type: "text",
                        left: "right",
                        top: 12,
                        style: {
                            text: "",
                            fontSize: 14,
                        },

                    }
                ],
            };
            // myChart.on("click", function (event) {
            //     if (event.data) {
            //         var subData = drilldownData.find(function (data) {
            //             return data.dataGroupId === event.data.groupId;
            //         });
            //         if (!subData) {
            //             return;
            //         }
            //         myChart.setOption({
            //             legend: {
            //                 top: 5,
            //                 data: ["个人任务", "总任务"],
            //             },
            //             xAxis: {
            //                 data: this.sxDataDetail.xAxis,
            //             },
            //             series: [
            //                 {
            //                     name: "个人任务",
            //                     label: {
            //                         show: true,
            //                         position: "top",
            //                     },
            //                     type: "line",
            //                     id: "sales",
            //                     data: this.sxDataDetail.series[1].data,
            //                     universalTransition: {
            //                         enabled: true,
            //                         divideShape: "clone",
            //                     },
            //                 },
            //                 {
            //                     name: "总任务",
            //                     type: "line",
            //                     id: "sales2",
            //                     label: {
            //                         show: true,
            //                         position: "top",
            //                     },
            //                     data: this.sxDataDetail.series[0].data,
            //                     universalTransition: {
            //                         enabled: true,
            //                         divideShape: "clone",
            //                     },
            //                 },
            //             ],
            //             graphic: [
            //                 {
            //                     type: "text",
            //                     left: "right",
            //                     top: 12,
            //                     style: {
            //                         text: "Back",
            //                         fontSize: 14,
            //                     },
            //                     onclick: function () {
            //                         myChart.setOption(option);
            //                     },
            //                 },
            //             ],
            //         });
            //     }
            // });
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            }
            myChart.setOption(option);

        },

        //统计内嵌
        initeightchart(){
            var myChart = echarts.init(document.getElementById("view9charts"));
            var option = {
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "cross",
                        crossStyle: {
                            color: "#999",
                        },
                    },
                },
                toolbox: {
                    show: true,
                    orient: "vertical",
                    left: "right",
                    top: "center",
                    feature: {
                        mark: { show: true },
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ["line", "bar", "stack"] },
                        restore: { show: true },
                        saveAsImage: { show: true },
                    },
                },
                legend: {
                    top: 5,
                    data: this.getStatTaskPopListdata.legend,
                },
                xAxis: [
                    {
                        type: "category",
                        data: this.getStatTaskPopListdata.xAxis,
                        axisPointer: {
                            type: "shadow",
                        },
                    },
                ],
                yAxis: [
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                    // {
                    //     type: "value",
                    //     name: "",
                    //     // interval: 5,
                    //     axisLabel: {
                    //         formatter: "{value} ",
                    //     },
                    // },
                ],

                grid: {
                    top: "45px",
                    bottom: "60px",
                    left: "50",
                    right: "65",
                },

                series: [
                {
                        name: this.getStatTaskPopListdata.series[0].name,
                        type: "line",
                        id: "sales",
                        barGap: "5%",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }
                        },
                        emphasis: {
                            focus: "series",
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value) + "";
                            },
                        },
                        data: this.getStatTaskPopListdata.series[0].data,
                    },
                    {
                        name: this.getStatTaskPopListdata.series[1].name,
                        type: "line",
                        id: "sales2",
                        barGap: "5%",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }
                        },
                        emphasis: {
                            focus: "series",
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value) + "";
                            },
                        },
                        data: this.getStatTaskPopListdata.series[1].data,
                    },
                ],
                graphic: [
                    {
                        type: "text",
                        left: "right",
                        top: 12,
                        style: {
                            text: "",
                            fontSize: 14,
                        },

                    }
                ],
            };
            // myChart.on("click", function (event) {
            //     if (event.data) {
            //         var subData = drilldownData.find(function (data) {
            //             return data.dataGroupId === event.data.groupId;
            //         });
            //         if (!subData) {
            //             return;
            //         }
            //         myChart.setOption({
            //             legend: {
            //                 top: 5,
            //                 data: ["个人任务", "总任务"],
            //             },
            //             xAxis: {
            //                 data: this.sxDataDetail.xAxis,
            //             },
            //             series: [
            //                 {
            //                     name: "个人任务",
            //                     label: {
            //                         show: true,
            //                         position: "top",
            //                     },
            //                     type: "line",
            //                     id: "sales",
            //                     data: this.sxDataDetail.series[1].data,
            //                     universalTransition: {
            //                         enabled: true,
            //                         divideShape: "clone",
            //                     },
            //                 },
            //                 {
            //                     name: "总任务",
            //                     type: "line",
            //                     id: "sales2",
            //                     label: {
            //                         show: true,
            //                         position: "top",
            //                     },
            //                     data: this.sxDataDetail.series[0].data,
            //                     universalTransition: {
            //                         enabled: true,
            //                         divideShape: "clone",
            //                     },
            //                 },
            //             ],
            //             graphic: [
            //                 {
            //                     type: "text",
            //                     left: "right",
            //                     top: 12,
            //                     style: {
            //                         text: "Back",
            //                         fontSize: 14,
            //                     },
            //                     onclick: function () {
            //                         myChart.setOption(option);
            //                     },
            //                 },
            //             ],
            //         });
            //     }
            // });
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            } 
            myChart.setOption(option);


        }

    },
};
</script>

<style lang="scss" scoped>
.sybj {
    min-width: 1100px;
    background-color: #f3f4f6;
    padding: 75px 5px 5px 5px;
    // height: 100%;
    height: calc(100vh - 260px);
    overflow-y: auto;
}

.tjgd {
    width: 100%;
    position: fixed;
    z-index: 999;
    box-sizing: border-box;
    padding: 10px 22px 20px 5px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    align-items: center;
    background-color: #fff;
}
.flexrow{
    display: flex; flex-direction: row;
}

.flexcolum{
    display: flex; flex-direction: column;
}

.tjgd span {
    margin: 5px;
}

.tjbt {
    /* background-color: aquamarine; */
    /* font-weight: bold; */
    color: #333;
    line-height: 30px;
    font-size: 14px;
}

.tjnrk1 {
    width: 60%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk2 {
    width: 40%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk3 {
    width: 50%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk4 {
    width: 50%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}
.nerbox{
    margin: 0 210px 0 auto;
}

.tjnrk5 {
    width: 100%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk6 {
    width: 100%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk10 {
    width: 100%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}


.tjnrnk {
    width: 100%;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 15px 20px;
    float: left;
    border-radius: 6px;
}

.ptsx {
    width: 85%;
    height: 90px;
    background-color: #f7f7f7;
    border-radius: 8px;
    margin: 10px auto;
    box-sizing: border-box;
    padding: 0 35px;
    line-height: 90px;
}

.ptsx span {
    font-size: 16px;
    color: #555;
}


.sydh {
    width: 100%;
    min-width: 1100px;
    height: 125px;

    z-index: 999;
}

.bzjgsj {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    box-sizing: border-box;
    padding: 35px 5%;

}

.sztjk {
    width: 15%;
    min-width: 75px;
    height: 80px;
    background-color: #f5faff;
    padding: 20px;
    text-align: center;
    margin: 5px 0.5%;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;


}

.sztjk .tjsz {
    font-size: 26px;
    text-align: center;
    color: #409eff;
}

.sztjk .tjmc {
    font-size: 14px;
    text-align: center;
    color: #409eff;
}



.sydhsx {
    width: 100%;
    height: 125px;
    background-color: #f3f4f6;
    margin: 0 auto;
    box-sizing: border-box;
    padding: 15px;
}
</style>
