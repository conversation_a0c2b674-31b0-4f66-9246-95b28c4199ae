<template>
    <!-- 责任申诉管理 -->
    <MyContainer>
        <template #header>
            <!-- <div class="top"> -->
            <!-- <div class="top_1"> -->
            <el-row style="margin-bottom: 10px;">
                <span style="font-size: 12px; margin-right: 10px">发货时间: </span>
                <el-date-picker v-model="timeList" type="daterange" align="right" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" class="marginleft"
                    @change="changeTime" />
                <el-select v-model="ListInfo.platform" placeholder="平台" class="marginleft" clearable style="width: 200px; ">
                    <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.orgZrDepartment" placeholder="原责任部门" class="marginleft" clearable
                    @change="changeOrgZrDepartment" style="width: 200px; ">
                    <el-option v-for="(item, i) in ZrDeptList" :key="i" :label="item" :value="item" />
                </el-select>

                <el-select v-model="ListInfo.oldResponType2" placeholder="原责任类型" class="marginleft" clearable
                    @change="zrType2change()" @visible-change="shopIncident">
                    <el-option v-for="item in ZrType" :key="item" :label="item" :value="item" style="width: 200px; " />
                </el-select>

                <el-select v-model="ListInfo.newZrDepartment" placeholder="新责任部门" class="marginleft" clearable
                    style="width: 200px; " @change="changeNewZrDepartment">
                    <el-option v-for="item in ZrDeptList" :key="item" :label="item" :value="item" />
                </el-select>
                <el-select v-model="ListInfo.newZrType2" placeholder="新责任类型" class="marginleft" style="width: 200px; "
                    clearable @change="zrType2change()" @visible-change="department">
                    <el-option v-for="item in newZrType" :key="item" :label="item" :value="item" />
                </el-select>
                <el-select v-model="ListInfo.firstAuditState" placeholder="是否认可" class="marginleft" style="width: 200px; "
                    clearable>
                    <el-option v-for="item in approvalStatus" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.applyState" placeholder="审核状态" class="marginleft" style="width: 150px; "
                    clearable>
                    <el-option v-for="item in applyStatus" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-input v-model="ListInfo.orgZrUserName" placeholder="原责任人" class="marginleft" style="width: 160px; "
                    maxlength="50" clearable />
                <!-- </el-row> -->
                <!-- </div> -->

                <!-- <el-row class="top_2"> -->
                <!-- <el-col class="top_2_left"> -->
                <el-input v-model="ListInfo.orderNo" placeholder="线上订单号" maxlength="50" class="marginleft"
                    style="width: 200px;" clearable />
                <el-input v-model="ListInfo.goodsCode" placeholder="商品编码" class="marginleft" maxlength="50"
                    style="width: 200px;" clearable />
                <el-input v-model="ListInfo.applyUserName" placeholder="申请人" class="marginleft" maxlength="50"
                    style="width: 200px;" clearable />
                <el-input v-model="ListInfo.auditUserName" placeholder="审核人" class="marginleft" maxlength="50"
                    style="width: 200px;" clearable />
                <el-input v-model="ListInfo.newZrUserName" placeholder="新责任人" class="marginleft" maxlength="50"
                    style="width: 200px;" clearable />
                <!-- </el-col>
                    <el-col> -->
                <el-button type="primary" class="btn" @click="getList">查询</el-button>
                <el-button type="primary" class="btn" @click="exportProps">导出</el-button>
                <el-button type="primary" class="btn" @click="openBatchDialogView"
                    v-if="checkPermission('api:Customerservice:DamagedOrders:BatchAuditDeductZrAppeal')">批量审核</el-button>
                <div style="color: red; overflow: hidden; white-space: nowrap; font-size: 13px;">
                    【是否认可】操作时限为17:30到次日9:00,过时将自动认可!</div>

                <!-- <el-button type="primary" class="btn" @click="bulkAppealsView = true">批量申诉</el-button> -->
                <!-- </el-col> -->
            </el-row>
            <!-- <el-row>
                    <div style="color: red; overflow: hidden; white-space: nowrap; font-size: 13px;" >【是否认可】操作时限为17:30到次日9:00,过时将自动认可!</div>
                </el-row> -->
            <!-- </div> -->
        </template>
        <template>
            <vxetablebase :id="'managementAccountabThr202408041802'" ref="table" :tableData="tableData" :tableCols="tableCols" :that="that" :hasSeq="false"
                @checkbox-range-end="chooseCode" :showsummary="true" :summaryarry='summaryarry' :isIndexFixed="false"
                :isSelection="false" :isSelectColumn="false" :isIndex="true" :hasexpand="false" @sortchange='sortchange'
                :loading="listLoading" />
        </template>

        <template #footer>
            <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="total" @page-change="pagechange"
                @size-change="sizechange" />
        </template>

        <el-dialog title="责任申诉审核" :visible.sync="auditDialogView" width="1200px" v-dialogDrag>
            <auditDialog :handleClose="handleClose" :auditDialogProps="auditDialogProps" @getList="getList"
                v-if="auditDialogView" />
        </el-dialog>

        <el-dialog title="责任申诉明细" :visible.sync="viewDialogView" width="1200px" v-dialogDrag>
            <viewDialog :handleClose="handleClose" :auditDialogProps="auditDialogProps" @getList="getList" />
        </el-dialog>

        <el-dialog title="批量审核" :visible.sync="batchDialogView" width="1400px" :before-close="handleClose" v-dialogDrag>
            <batchReviewPages :handleClose="handleClose" :tableData="batchtableData" @getList="getList" :ids="ids" />
        </el-dialog>

        <el-dialog title="认可处理" :visible.sync="approvedProcessingView" width="1400px" :before-close="handleClose"
            v-dialogDrag>
            <approvedProcessing :handleClose="handleClose" @closedia="approvedProcessingView = false"
                :auditDialogProps="auditDialogProps" @getList="getList" />
        </el-dialog>

        <!-- <el-dialog title="责任申诉" :visible.sync="complaintDialogView" width="70%" v-dialogDrag>
            <complaintDialog :handleClose="handleClose" />
        </el-dialog> -->

        <!-- <el-dialog title="批量责任申诉" :visible.sync="bulkAppealsView" width="70%" :before-close="handleClose" v-dialogDrag>
            <bulkAppeals :handleClose="handleClose" :tableData="batchtableData" />
        </el-dialog> -->
    </MyContainer>
</template>

<script>
import { formatTime, formatPlatform, pickerOptions } from "@/utils/tools";
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import auditDialog from "../components/auditDialog.vue";
import batchReviewPages from "../components/batchReviewPages.vue";
import viewDialog from "../components/viewDialog.vue";
// import complaintDialog from "../components/complaintDialog.vue";
import bulkAppeals from "../components/bulkAppeals.vue";
import approvedProcessing from "../components/approvedProcessing.vue";
import { pageDeductZrAppealList, getDamagedOrdersZrDept, getDamagedOrdersZrType, getDeductZrAppeal4CRUD, exportDeductZrAppealAsync } from '@/api/customerservice/DamagedOrders'
import { replaceSpace } from '@/utils/getCols'
import dayjs from 'dayjs'

//认可状态
const approvalStatus = [
    {
        label: '待初审',
        value: 0
    },
    {
        label: '不认可',
        value: -1
    },
    {
        label: '认可',
        value: 1
    },
]
//申请状态
const applyStatus = [
    {
        label: '打回',
        value: -1
    },
    // {
    //     label: '草稿待申请',
    //     value: 0
    // },
    {
        label: '申请中',
        value: 1
    },
    {
        label: '已通过',
        value: 2
    },
]
//平台
const platformList = [
    { label: '天猫', value: 1 },
    { label: '拼多多', value: 2 },
    { label: '阿里巴巴', value: 4 },
    { label: '抖音', value: 6 },
    { label: '京东', value: 7 },
    { label: '淘工厂', value: 8 },
    { label: '淘宝', value: 9 },
    { label: '苏宁', value: 10 }
]
const tableCols = [
    { istrue: true, label: '复选框', type: "checkbox", width: 60, fixed: 'left' },
    { istrue: true, prop: 'goodsPic', label: '图片', width: '80', type: "imagess" },

    { istrue: true, prop: 'platform', label: '平台', sortable: 'custom', width: '80', formatter: (row) => platformList.find(item => item.value == row.platform)?.label },
    { istrue: true, prop: 'orderNo', label: '线上订单号', sortable: 'custom', width: '170' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'afterSaleApproveDate', label: '售后发起时间', sortable: 'custom', width: '130', formatter: (row) => dayjs(row.afterSaleApproveDate).format('YYYY-MM-DD') },
    { istrue: true, prop: 'damagedAmount', label: '损耗金额', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'orgZrDepartment', label: '原责任部门', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'orgZrType2', label: '原责任类型', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'orgZrUserName', label: '原责任人', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'zrSetDate', label: '责任计算时间', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'applyUserName', label: '申请人', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'applyTime', label: '申诉时间', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'newZrDepartment', label: '新责任部门', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'newZrType2', label: '新责任类型', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'newZrUserName', label: '新责任人', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'applyReason', label: '申诉理由', width: '100' },
    { istrue: true, prop: 'applyContent', label: '申诉内容', width: '100' },
    // {
    //     istrue: true, prop: 'firstAuditState', label: '是否认可', width: '100', type: 'button', fixed: 'right', btnList: [
    //         { label: "处理", handle: (that, row) => that.sheviewDialogShow(row), ishide: (that, row) => { return !(row.firstAuditState == 0 || !row.firstAuditState); } },
    //         { label: "不认可", ishide: (that, row) => { return !(row.firstAuditState == -1); } },
    //         { label: "认可", ishide: (that, row) => { return !(row.firstAuditState == 1); } },
    //     ]
    // },
    {
        istrue: true, prop: 'firstAuditState', sortable: 'custom', label: '是否认可', width: '100', type: 'button', btnList: [
            {
                label: "",
                htmlformatter: (row) => row.firstAuditStateText,
                handle: (that, row) => { }
            },
            {
                label: '查看',
                handle: (that, row) => that.sheviewDialogShow(row, true),
                ishide: (that, row) => !row.firstAuditState
            },
            {
                label: '处理',
                handle: (that, row) => that.sheviewDialogShow(row),
                ishide: (that, row) => { return ((row.applyState < 0 || row.applyState > 1) || !row.allowFirstAudit) }
            },
        ]
    },

    { istrue: true, prop: 'auditUserName', label: '审核人', sortable: 'custom', width: '90' },
    { istrue: true, prop: 'auditTime', label: '审核时间', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'applyState', label: '审核状态', sortable: 'custom', width: '100', fixed: 'right', formatter: (row) => applyStatus.find(item => item.value == row.applyState)?.label },
    {
        istrue: true, prop: '', label: '操作', width: '100', type: 'button', fixed: 'right', btnList: [
            { label: "查看", handle: (that, row) => that.viewDialogShow(row) },
            {
                label: "审核", display: (row) => row.applyState == 2 || row.applyState == -1 || row.applyState == 3,
                permission: 'api:Customerservice:DamagedOrders:AuditDeductZrAppeal',
                handle: (that, row) => that.auditDialogShow(row)
            },
            // { label: "申诉", display: (row) => row.applyState == 2 || row.applyState == -1, handle: (that, row) => that.complaintDialogShow(row) }
        ]
    },
]
export default {
    name: "Vue2demoManagementAccountabThr",
    components: {
        MyContainer,
        vxetablebase,
        auditDialog,
        viewDialog,
        batchReviewPages,
        bulkAppeals,
        approvedProcessing
    },
    data() {
        return {
            ListInfo: {//列表参数
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//排序方式
                startDate: null,//责任设置开始时间
                endDate: null,//责任设置结束时间
                platform: null,//平台
                orgZrDepartment: null,//原责任部门
                newZrDepartment: null,//新责任部门
                orderInnerNo: null,//订单内部编码
                goodsCode: null,//商品编码
                oldResponType: null,//原责任类型 大类
                oldResponType2: null,//原责任类型 细类
                oldResponUserName: null,//原责任人
                applyUserName: null,//申请人
                newZrType: null,//责任类型 大类
                newZrType2: null,//责任类型 细类
                newZrUserName: null,//责任人
                firstAuditState: null,//初审状态：null/0 待初审、-1不认可、1认可
                auditUserName: null,//审核人
                applyState: null,//申请状态：-1打回、0草稿待申请、1申请中、2已通过
            },
            timeList: [],//时间
            platformList,//平台
            total: 0,//总条数
            auditDialogView: false,//审核弹窗
            viewDialogView: false,//查看弹窗
            batchDialogView: false,//批量审核弹窗
            complaintDialogView: false,//责任申诉弹窗
            bulkAppealsView: false,//批量责任申诉弹窗
            approvedProcessingView: false,//认可处理弹窗
            that: this,
            tableCols,
            batchtableData: [],//批量审核列表数据
            approvalStatus,//认可状态
            applyStatus,//申请状态
            summaryarry: null,
            tableData: [],//列表数据
            listLoading: true,//加载中
            ZrDeptList: [],//原责任部门列表
            newZrDeptList: [],//新责任部门列表
            ZrType: [],//原责任类型列表
            newZrType: [],//新责任类型列表
            pickerOptions: pickerOptions,
            auditDialogProps: null,
            ids: []
        };
    },
    mounted() {
        this.getList()
        this.getZrDept()
    },
    methods: {
        department(e) {
          if (e && e == true && !this.ListInfo.newZrDepartment) {
            this.$message.warning('请先选择新责任部门')
          }
        },
        shopIncident(e) {
          if (e && e == true && !this.ListInfo.orgZrDepartment) {
            this.$message.warning('请先选择原责任部门')
          }
        },

        //导出
        async exportProps() {
            const { data } = await exportDeductZrAppealAsync(this.ListInfo)
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '损耗订单' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        async sheviewDialogShow(row, type) {
            let params = {
                id: row.id,
                orderNo: row.orderNo,
                goodsCode: row.goodsCode,
            }


            const { data, success } = await getDeductZrAppeal4CRUD(params)
            if (success) {
                if (type) {
                    data.disabled = true;
                } else {
                    data.disabled = false;
                }
                this.auditDialogProps = data
                console.log(this.auditDialogProps, 'this.auditDialogProps');
                console.log(data, 'data');
                this.approvedProcessingView = true;
            }

        },
        openBatchDialogView() {
            if (this.ids.length == 0) return this.$message.error('请选择要审核的数据')
            this.batchDialogView = true
        },
        //责任申诉弹窗
        complaintDialogShow() {
            this.complaintDialogView = true
        },
        //时间改变
        changeTime(e) {
            if (e) {
                this.ListInfo.startDate = dayjs(e[0]).format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs(e[1]).format('YYYY-MM-DD')
                this.timeList = e
            } else {
                this.ListInfo.startDate = null
                this.ListInfo.endDate = null
                this.timeList = []
            }
            this.getList()
        },
        //新责任部门改变
        async changeNewZrDepartment(e) {
            if (e) {
                this.ListInfo.newZrDepartment = e
                this.newZrType = await this.getZrType(this.ListInfo.newZrDepartment)
            } else {
                this.ListInfo.newZrDepartment = null
                this.ListInfo.newZrType2 = null
                this.newZrType = []
            }
            this.ListInfo.newZrType2 = ''
        },
        //原责任部门改变
        async changeOrgZrDepartment(e) {
            if (e) {
                this.ListInfo.orgZrDepartment = e
                this.ZrType = await this.getZrType(this.ListInfo.orgZrDepartment)
            } else {
                this.ListInfo.orgZrDepartment = null
                this.ListInfo.oldResponType2 = null
                this.ZrType = []
            }
            this.ListInfo.oldResponType2 = ""
        },
        zrType2change() {
            this.$forceUpdate();
        },
        //获取责任类型
        async getZrType(val) {
            const { data, success } = await getDamagedOrdersZrType(val)
            if (success) {
                return data
            }
        },
        //获取责任部门
        async getZrDept() {
            const { data, success } = await getDamagedOrdersZrDept()
            if (success) {
                this.ZrDeptList = data
                this.newZrDeptList = data
            } else {
                this.$message.error('获取责任部门失败')
            }
        },
        //关闭弹窗
        handleClose() {
            this.auditDialogView = false
            this.viewDialogView = false
            this.batchDialogView = false
            this.complaintDialogView = false
            this.bulkAppealsView = false
            this.approvedProcessingView = false
        },
        //审核弹窗并获取审核详情
        async auditDialogShow(row) {
            console.log(row.id, 'row.id');
            console.log(row.id.toString(), 'row.id');
            let params = {
                id: row.id,
                orderNo: row.orderNo,
                goodsCode: row.goodsCode,
            }
            const { data, success } = await getDeductZrAppeal4CRUD(params)
            if (success) {
                this.auditDialogProps = data
                console.log(this.auditDialogProps, 'this.auditDialogProps');
                console.log(data, 'data');
                this.auditDialogView = true
            }
        },
        //查看弹窗
        async viewDialogShow(row) {
            const { data, success } = await getDeductZrAppeal4CRUD({ id: row.id })
            if (success) {
                this.auditDialogProps = data
                this.viewDialogView = true
            }
        },
        //获取列表
        async getList() {
            if (this.ListInfo.orderInnerNo) {
                this.ListInfo.orderInnerNo = this.ListInfo.orderInnerNo.replace(/\s+/g, "")
            }
            if (this.ListInfo.goodsCode) {
                this.ListInfo.goodsCode = this.ListInfo.goodsCode.replace(/\s+/g, "")
            }
            if (this.ListInfo.oldResponUserName) {
                this.ListInfo.oldResponUserName = this.ListInfo.oldResponUserName.replace(/\s+/g, "")
            }
            if (this.ListInfo.applyUserName) {
                this.ListInfo.applyUserName = this.ListInfo.applyUserName.replace(/\s+/g, "")
            }
            if (this.ListInfo.newZrUserName) {
                this.ListInfo.newZrUserName = this.ListInfo.newZrUserName.replace(/\s+/g, "")
            }
            if (this.ListInfo.auditUserName) {
                this.ListInfo.auditUserName = this.ListInfo.auditUserName.replace(/\s+/g, "")
            }
            if (this.ListInfo.firstAuditState === '' && this.ListInfo.firstAuditState !== 0) {
                this.ListInfo.firstAuditState = null
            }
            const { data, success } = await pageDeductZrAppealList(this.ListInfo)
            data.list.map((item) => {
                if (!item.goodsPic) {
                    item.goodsPic = [];
                } else {
                    if (item.goodsPic.indexOf(',') != -1) {
                        item.goodsPic = item.goodsPic.split(",")
                    } else {
                        item.goodsPic = [item.goodsPic]
                    }
                }
            })
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.summaryarry = data.summary
                this.listLoading = false
            }
        },
        chooseCode(val) {
            this.ids = []
            val.forEach(item => {
                this.ids.push(item.id)
            });
            console.log(this.ids, 'this.ids');
            this.batchtableData = val
            console.log(this.batchtableData, 'this.batchtableData');
        },
        //页面数量改变
        sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList();
        },
        //当前页改变
        pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList();
        },
        //排序
        sortchange({ order, prop }) {
            this.listLoading = true
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },

    },
};
</script>

<style lang="scss" scoped>
.top {
    padding: 0 5px 10px;

    .top_1 {
        display: flex;
        justify-content: space-between;
    }

    .top_2 {
        margin-top: 10px;
        display: flex;

        .top_2_left {
            display: flex;
        }

        .top_2_right {
            display: flex;
            justify-content: space-between;
        }
    }
}

.publicWidth {
    width: 160px;
}

.publicWidth1 {
    width: 160px;
    margin-right: 24px;
}

.btn {
    width: 80px;
}

.marginleft {
    margin-right: 10px;
    margin-bottom: 10px;
}</style>
