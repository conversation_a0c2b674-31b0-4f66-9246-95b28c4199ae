<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" ref="form" label-width="120px" label-position="right" >     
                <el-row>     
                    <el-col :span="12">
                        <el-form-item label="新责任部门：" prop="newZrDeptAction" :rules="[
                        { required: true, message: '请选择新责任部门', trigger: ['blur', 'change'] }    
                        ]">
                            <el-select v-if="formEditMode" v-model="form.newZrDeptAction" @change="newZrDeptActionChange">
                                <el-option v-for="item in zrDeptActions" :value="item.zrDeptAction" :label="item.zrDeptAction"></el-option>
                            </el-select>
                            <span v-else>{{ form.newZrDeptAction }}</span>
                        </el-form-item>
                    </el-col>   
                    <el-col :span="12">
                        <el-form-item label="新责任人：" prop="newMemberName" >
                            <template  v-if="formEditMode" >
                                <el-select v-if="form.newZrDept=='采购'" v-model="form.newMemberId" filterable @change="newMemberIdChange">
                                    <el-option v-for="item in brandlist" :key="item.key" :label="item.value"  :value="item.key"/>
                                </el-select>
                                <el-select v-else-if="form.newZrDept=='运营'" v-model="form.newMemberId" filterable @change="newMemberIdChange">
                                    <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
                                </el-select>  
                                <YhUserelector v-else-if="form.newZrDept!='机器人' && form.newZrDept!='外部'  && form.newZrDept!='快递'" 
                                            :value.sync="form.newMemberDDUserId" :text.sync="form.newMemberDDUserName"
                                        ></YhUserelector>
                                <el-input  v-else  v-model="form.newMemberName" clearable maxlength="10"  />
                            </template>                            
                            <span v-else>{{ form.newMemberName }}</span>
                        </el-form-item> 
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24" style="max-height:400px;overflow:auto">
                        <el-form-item label="转派原因："  prop="opContent" :rules="[
                        { required: true, message: '请填写转派原因', trigger: ['blur'] }    
                        ]">
                            <yh-quill-editor :value.sync="form.opContent" v-if="formEditMode"></yh-quill-editor>
                            <div v-else v-html="form.opContent" class="tempdiv"></div>
                        </el-form-item>
                    </el-col>
                </el-row>              
                
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">  
                    <el-button @click="onClose">关闭</el-button>                     
                    <el-button  type="primary" @click="onSave(true)">提交转派</el-button>   
                </el-col>
            </el-row>
        </template>
      
    </my-container>
</template>
<script>  

    import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode ,DeductOrderZrDeptActions,DeductOrderZrReasons} from "@/utils/tools";
   
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import { FirstAuditDeductZrTrans2SameDept ,BatchFirstAuditDeductZrTrans2SameDept} from "@/api/order/orderdeductmoney";
    import {getAllWarehouse,getAllProBrand} from '@/api/inventory/warehouse'

    import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'


    import YhUserelector from '@/components/YhCom/yh-userselector.vue'

    import {
        getDirectorList,
        getDirectorGroupList,
        getProductBrandPageList,
        getList as getshopList,
    } from "@/api/operatemanage/base/shop";


    export default {
        name: "OrderDeductZrApplyFirstAuditTrans2OtherForm",
        components: { MyContainer, MyConfirmButton,  YhQuillEditor , YhUserelector},
        data() {
            return {              
                that: this,
                mode:3,
                illegalTypeList:[],
                zrDeptActions:DeductOrderZrDeptActions,
                zrReasons:DeductOrderZrReasons,

                brandlist: [], 
                directorList: [],

                form: {
                    newZrDeptAction:"",
                    newZrDept:"",
                    newMemberName:"",
                    newMemberId:null,
                    newMemberDDUserId:"",
                    newMemberDDUserName:"",
                    appealIds:[],
                },
            
                //summaryarry: {},
                pageLoading: false,
                curRow: null,
                formEditMode: true,//是否编辑模式              
                dialogHisVisible:false,
                isTx:false,      
            };
        },
        async mounted() {           
            await this.setBandSelect();
            await this.getDirectorlist();
            await this.newZrDeptActionChange();
        },
        computed: {    
        },
        methods: {   
            async getDirectorlist () {
                const res1 = await getDirectorList({});
                const res2 = await getDirectorGroupList({});

                this.directorList = res1.data;
                this.directorGroupList = [{ key: "0", value: "未知" }].concat(
                    res2.data || []
                );
            }, 
            async setBandSelect(){
                var res= await  getAllProBrand();
                if (!res?.success) return;
                this.brandlist = res.data;
            },         
            newZrDeptActionChange(){
              
                if(this.form.newZrDeptAction){                    
                    let opt=this.zrDeptActions.find(x=>x.zrDeptAction==this.form.newZrDeptAction);
                    if(opt){
                        if(this.form.newZrDept!=opt.zrDept){
                            this.form.newMemberName="";
                            this.form.newMemberId=null;
                            this.form.newMemberDDUserId="";
                            this.form.newMemberDDUserName="";
                        }
                        this.form.newZrAction=opt.zrAction;
                        this.form.newZrDept=opt.zrDept;
                    }else{
                        this.form.newMemberName="";
                        this.form.newMemberId=null;
                        this.form.newMemberDDUserId="";
                        this.form.newMemberDDUserName="";
                    }                    
                }
            },   
            newMemberIdChange(){  
                let arr=null;
                if(this.form.newZrDept=="采购"){
                    arr=[...this.brandlist];                   
                }
                else if(this.form.newZrDept=="运营"){
                    arr=[...this.directorList];                    
                }    
                
                if(arr!=null && arr && this.form.newMemberId){                  
                    let opt=arr.find(x=>x.key==this.form.newMemberId);
                    if(opt){
                        this.form.newMemberName=opt.value;                      
                    }
                }
            },   
           
            onClose(){
                this.$emit('close');
            },  
            async onSave(isClose){
                if(await this.save()){
                    this.$emit('afterSave');
                    if(isClose)
                        this.$emit('close');
                }
            },
            async loadData({appealId, newZrDeptAction, newZrDept, newZrAction,appealIds}) {     
                let self=this;         
                self.pageLoading = true;
                self.formEditMode = true;
                self.mode = 2;                

                self.form.appealId=appealId;
                self.form.newZrDeptAction=newZrDeptAction;
                self.form.newZrDept=newZrDept;
                self.form.newZrAction=newZrAction;
                if(appealIds && appealIds.length>0)
                    self.form.appealIds=appealIds;
                
                self.pageLoading = false;
            },
            async save() {             

                this.pageLoading = true;
                
                let saveData = { ...this.form };   

                try {
                    await this.$refs["form"].validate();
                } catch (error) {
                    this.pageLoading = false;
                    return false;
                } 
               
                let rlt=null;

                if(this.form.appealIds && this.form.appealIds.length>0){
                    rlt = await BatchFirstAuditDeductZrTrans2SameDept(saveData);
                    if (rlt && rlt.success) {
                        this.$message.success(rlt.data.rltMsg);           
                    }
                }else{
                    rlt = await FirstAuditDeductZrTrans2SameDept(saveData);
                    if (rlt && rlt.success) {
                        this.$message.success('转派成功！');           
                    }
                }
               

                this.pageLoading = false;
              
                return (rlt && rlt.success);
            }
        },
    };
</script>
