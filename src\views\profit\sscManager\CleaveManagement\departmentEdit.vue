<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="140px" class="demo-ruleForm">
        <el-form-item label="类型：">
          <el-input v-model.trim="ruleForm.type" placeholder="类型" maxlength="20" clearable class="publicCss" />
        </el-form-item>
        <el-form-item label="区域：">
          <el-input v-model.trim="ruleForm.regionName" placeholder="区域" maxlength="20" clearable class="publicCss" />
        </el-form-item>
        <el-form-item label="部门类型：">
          <el-input v-model.trim="ruleForm.deptType" placeholder="部门类型" maxlength="20" clearable class="publicCss" />
        </el-form-item>
        <el-form-item label="部门：">
          <el-input v-model.trim="ruleForm.deptName" placeholder="部门" maxlength="20" clearable class="publicCss" />
        </el-form-item>
        <!-- <el-form-item label="总人数：" prop="regionName">
          <inputNumberYh v-model="ruleForm.regionName" :placeholder="'总人数'" class="publicCss" />
        </el-form-item> -->
        <el-form-item label="月离职人数：" prop="monthCount" style="font-size: 22px; font-weight: bold;">
          <inputNumberYh v-model="ruleForm.monthCount" :placeholder="'月离职人数'" class="publicCss" />
        </el-form-item>
        <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">在职天数分析</div>
        <el-form-item label="7天内离职人数：" prop="day7Count">
          <inputNumberYh v-model="ruleForm.day7Count" @input="changeCount(1)" :placeholder="'7天内离职人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="8~15天离职人数：" prop="day15Count">
          <inputNumberYh v-model="ruleForm.day15Count" @input="changeCount(1)" :placeholder="'8~15天离职人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="16~30天离职人数：" prop="day30Count">
          <inputNumberYh v-model="ruleForm.day30Count" @input="changeCount(1)" :placeholder="'16~30天离职人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="30天以上离职人数：" prop="day90Count">
          <inputNumberYh v-model="ruleForm.day90Count" @input="changeCount(1)" :placeholder="'30天以上离职人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="1年以上离职人数：" prop="yearCount">
          <inputNumberYh v-model="ruleForm.yearCount" @input="changeCount(1)" :placeholder="'1年以上离职人数'" class="publicCss" />
        </el-form-item>
        <!-- <el-form-item label="2年以上离职人数：" prop="twoYearCount">
          <inputNumberYh v-model="ruleForm.twoYearCount" @input="changeCount(1)" :placeholder="'2年以上离职人数'" class="publicCss" />
        </el-form-item> -->
<!--
        <el-form-item label="月离职人数(2)：" prop="monthCount" style="font-size: 22px; font-weight: bold;">
          <inputNumberYh v-model="ruleForm.monthCount1" disabled :placeholder="'月离职人数'" class="publicCss" />
        </el-form-item> -->
        <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">离职类型</div>


        <el-form-item label="辞职：" prop="resignCount">
            <inputNumberYh v-model="ruleForm.resignCount" @input="changeCount(2)" :placeholder="'辞职'" class="publicCss" />
            <!-- <el-input style="width:80%;" v-model.trim="ruleForm.resignCount" :maxlength="50" placeholder="辞职" clearable /> -->
        </el-form-item>
        <el-form-item label="劝退：" prop="quitCount">
          <inputNumberYh v-model="ruleForm.quitCount" @input="changeCount(2)" :placeholder="'劝退'" class="publicCss" />
          <!-- <el-input style="width:80%;" v-model.trim="ruleForm.quitCount" :maxlength="50" placeholder="劝退" clearable /> -->
        </el-form-item>
        <el-form-item label="自离：" prop="selfDeparture">
          <inputNumberYh v-model="ruleForm.selfDeparture" @input="changeCount(2)" :placeholder="'自离'" class="publicCss" />
          <!-- <el-input style="width:80%;" v-model.trim="ruleForm.selfDeparture" :maxlength="50" placeholder="自离" clearable /> -->
        </el-form-item>

        <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">员工结构</div>
        <el-form-item label="试用期离职：" prop="probationQuitCount">
          <inputNumberYh v-model="ruleForm.probationQuitCount" @input="changeCount(2)" :placeholder="'试用期离职'" class="publicCss" />
        </el-form-item>
        <el-form-item label="正式离职：" prop="regularQuitCount">
          <inputNumberYh v-model="ruleForm.regularQuitCount" @input="changeCount(2)" :placeholder="'正式离职'" class="publicCss" />
        </el-form-item>

        <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">离职原因</div>
        <el-form-item label="职业发展有变人数：" prop="hasWorkCount">
          <inputNumberYh v-model="ruleForm.hasWorkCount" @input="changeCount(3)" :placeholder="'职业发展有变人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="淘汰人数：" prop="eliminateCount">
          <inputNumberYh v-model="ruleForm.eliminateCount" @input="changeCount(3)" :placeholder="'淘汰人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="不适应工作人数：" prop="highStressCount">
          <inputNumberYh v-model="ruleForm.highStressCount" @input="changeCount(3)" :placeholder="'不适应工作人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="家庭原因人数：" prop="familyReasonsCount">
          <inputNumberYh v-model="ruleForm.familyReasonsCount" @input="changeCount(3)" :placeholder="'家庭原因人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="身体原因人数：" prop="bodyReasonsCount">
          <inputNumberYh v-model="ruleForm.bodyReasonsCount" @input="changeCount(3)" :placeholder="'身体原因人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="其他原因人数：" prop="otherCount">
          <inputNumberYh v-model="ruleForm.otherCount" @input="changeCount(3)" :placeholder="'其他原因人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="自离原因人数：" prop="resignationOneselfCount">
          <inputNumberYh v-model="ruleForm.resignationOneselfCount" @input="changeCount(3)" :placeholder="'自离原因人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="单量原因人数：" prop="orderQuantityCount">
          <inputNumberYh v-model="ruleForm.orderQuantityCount" @input="changeCount(3)" :placeholder="'单量原因人数'" class="publicCss" />
        </el-form-item>

        <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">离职成本分析</div>
        <el-form-item label="总离职成本：" prop="totalResignationCost">
          <inputNumberYh v-model="ruleForm.totalResignationCost" @input="changeCount(3)" :placeholder="'总离职成本'" class="publicCss" />
        </el-form-item>
        <el-form-item label="人均离职成本：" prop="perResignationCost">
          <inputNumberYh v-model="ruleForm.perResignationCost" @input="changeCount(3)" :placeholder="'人均离职成本'" class="publicCss" />
        </el-form-item>



        <!-- <el-form-item label="月离职人数(4)：" prop="monthCount" style="font-size: 22px; font-weight: bold;">
          <inputNumberYh v-model="ruleForm.monthCount3" disabled :placeholder="'月离职人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="试用期离职：" prop="probationQuitCount">
          <inputNumberYh v-model="ruleForm.probationQuitCount" @input="changeCount(4)" :placeholder="'试用期离职'" class="publicCss" />

        </el-form-item>
        <el-form-item label="正式工离职：" prop="regularQuitCount">
          <inputNumberYh v-model="ruleForm.regularQuitCount" @input="changeCount(4)" :placeholder="'正式工离职'" class="publicCss" />

        </el-form-item>
        <el-form-item label="总离职成本：" prop="regularQuitCount">
          <el-input style="width:80%;" v-model.trim="ruleForm.totalResignationCost" :maxlength="8" placeholder="总离职成本" clearable />
        </el-form-item>
        <el-form-item label="人均离职成本：" prop="regularQuitCount">
          <el-input style="width:80%;" v-model.trim="ruleForm.perResignationCost" :maxlength="8" placeholder="人均离职成本" clearable />
        </el-form-item> -->

      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { dimissionManageSubmit } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission'
import decimal from '@/utils/decimal'
export default {
  name: 'departmentEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      selectProfitrates: [],
      ruleForm: {
        label: '',
        name: '',
        monthCount: 0,
        monthCount1: 0,
        monthCount2: 0,
        monthCount3: 0,

      },
      rules: {
        name: [
          { required: true, message: '请输入活动名称', trigger: 'blur' }
        ],
        label: [
          { required: true, message: '请输入活动名称', trigger: 'blur' }
        ]
      }
    }
  },

  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };

    // 延迟执行计算，避免初始化时的循环调用
    setTimeout(() => {
      this.changeCount(1);
      this.changeCount(2);
      this.changeCount(3);
      this.changeCount(4);
    }, 100); // 减少延迟时间
  },
  methods: {
    changeCount(val){
        console.log(val,"进入计算")
        if(val == 1){
            let a = this.ruleForm.day7Count? this.ruleForm.day7Count :  0;
            let b = this.ruleForm.day15Count? this.ruleForm.day15Count :  0;
            let c = this.ruleForm.day30Count? this.ruleForm.day30Count :  0;
            let d = this.ruleForm.day90Count? this.ruleForm.day90Count :  0;
            let e = this.ruleForm.yearCount? this.ruleForm.yearCount :  0;
            let f = this.ruleForm.twoYearCount?  this.ruleForm.twoYearCount :  0;

            this.ruleForm.monthCount = a+b+c+d+e+f;
        }else if(val == 2){
            let a = this.ruleForm.selfDeparture? this.ruleForm.selfDeparture :  0;
            let b = this.ruleForm.quitCount? this.ruleForm.quitCount :  0;
            let c = this.ruleForm.resignCount? this.ruleForm.resignCount :  0;

            this.ruleForm.monthCount1 = a+b+c;
        }else if(val == 3){
            // let a = this.ruleForm.eliminateCount? this.ruleForm.eliminateCount :  0;
            // let b = this.ruleForm.bodyReasonsCount? this.ruleForm.bodyReasonsCount :  0;
            // let c = this.ruleForm.familyReasonsCount? this.ruleForm.familyReasonsCount :  0;
            // let d = this.ruleForm.hasWorkCount? this.ruleForm.hasWorkCount :  0;
            // let e = this.ruleForm.highStressCount ? this.ruleForm.highStressCount : 0;

            // let f = this.ruleForm.otherCount? this.ruleForm.otherCount :  0;

            let a = this.ruleForm.otherCount? this.ruleForm.otherCount :  0;
            let b = this.ruleForm.workErrorCount? this.ruleForm.workErrorCount :  0;
            let c = this.ruleForm.deptDissolutionCount? this.ruleForm.deptDissolutionCount :  0;
            let d = this.ruleForm.highStressCount? this.ruleForm.highStressCount :  0;
            let e = this.ruleForm.bodyReasonsCount ? this.ruleForm.bodyReasonsCount : 0;

            let f = this.ruleForm.hasWorkCount? this.ruleForm.hasWorkCount :  0;


            this.ruleForm.monthCount2 =  a+b+c+d+e+f;
        }else if(val == 4){
            let a = this.ruleForm.regularQuitCount? this.ruleForm.regularQuitCount :  0;
            let b = this.ruleForm.probationQuitCount? this.ruleForm.probationQuitCount :  0;


            this.ruleForm.monthCount3 =  a+b;
        }
        this.$forceUpdate();
    },
    cancellationMethod() {
      this.$emit('cancellationMethod');
    },
    submitForm(formName) {
      console.log(this.ruleForm.label, 'this.ruleForm.label');
      this.$refs[formName].validate(async(valid) => {
          if (valid) {
            this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing")
            const { data, success } = await dimissionManageSubmit(this.ruleForm)
            if(!success){
                return
            }
            await this.$emit("search");

          } else {
            console.log('error submit!!');
            return false;
          }
        });
    //   this.$confirm('是否保存?', '提示', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'warning'
    //   }).then(async () => {
    //     this.$refs[formName].validate(async(valid) => {
    //       if (valid) {
    //         const { data, success } = await dimissionManageSubmit(this.ruleForm)
    //         if(!success){
    //             return
    //         }
    //         await this.$emit("search");

    //       } else {
    //         console.log('error submit!!');
    //         return false;
    //       }
    //     });
    //   }).catch(() => {
    //   });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}
</style>
