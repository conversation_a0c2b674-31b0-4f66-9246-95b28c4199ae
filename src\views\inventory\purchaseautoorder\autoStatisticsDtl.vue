<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker style="width: 220px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束" 
                        clearable></el-date-picker>
                <el-select v-model="filter.BrandIds" multiple clearable collapse-tags filterable placeholder="请选择采购员" class="publicCss">
                    <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="filter.titles" multiple clearable collapse-tags filterable placeholder="请选择岗位" class="publicCss">
                    <el-option v-for="item in titleList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <el-select v-model="filter.deptIds" multiple clearable collapse-tags filterable placeholder="请选择架构" class="publicCss">
                    <el-option v-for="item in deptList" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>
                <el-input v-model.trim="filter.indexNo" clearable placeholder="请输入ERP编号" class="publicCss"></el-input>
                <el-input v-model.trim="filter.goodsCode" clearable placeholder="请输入商品编号" class="publicCss"></el-input>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :loading="loading" :that="that" :isIndex='true' :hasexpand="true" :tablefixed="true"
            :border="true" :table-data="data.list" :table-cols="tableCols" :isSelection="false" 
            :isSelectColumn="false" :isIndexFixed="false" style="width: 100%; margin: 0;" height="100%"
            :isNeedExpend="false" :showsummary="data.summary ? true : false" :summaryarry="data.summary"
            @sortchange="sortChange" >
            <template slot="right">
                <vxe-column title="操作" width="120" fixed="right">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="setPurRemark(row)">采购备注</el-button>
                            <el-button type="text" @click="viewPurRemark(row)">日志</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="pageChange" @size-change="sizeChange" />
        </template>

        <el-dialog v-dialogDrag title="采购备注" :visible.sync="purRemarkFilter.visible" width="25%">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-input type="textarea" v-model.trim="purRemarkFilter.remark" maxlength="200" show-word-limit placeholder="请输入采购备注" resize="none" :rows="7" clearable>
                </el-input>
            </div>
            <div class="btnGroup" style="margin-top: 10px;">
                <el-button @click="purRemarkFilter.visible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog v-dialogDrag title="日志" :visible.sync="purRemarkLog.visible" width="40%">
            <vxetablebase ref="logTable" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :treeProp="{}" 
                :tableData='purRemarkLogList' :tableCols='purRemarkLogTableCols' :isSelection="false" 
                :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="purRemarkLogListLoading" :height="500">
            </vxetablebase>
        </el-dialog>
    </MyContainer>
</template>

<script>

import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from "dayjs";
import { getAutoStatisticsDtl, setAutoPurOrderRmark, getPurchaseAutoOrderRecordLogList, getAutoBrandDept, getAutoBrandTitle } from '@/api/inventory/purchaseordernew'
import { getAllProBrand } from '@/api/inventory/warehouse'
import { pickerOptions, platformlist, formatLinkProCode, formatTime } from '@/utils/tools'

const tableCols = [
    { istrue: true, prop: 'yearMonthDay', label: '开单时间', width: '140', sortable: 'custom', formatter: (row) => formatTime(row.yearMonthDay, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'brandId', label: '采购', width: '120', sortable: 'custom', formatter: (row) => row.brandName },
    { istrue: true, prop: 'title', label: '岗位', width: '140' },
    { istrue: true, prop: 'deptName', label: '架构', width: 'auto' },
    { istrue: true, prop: 'indexNo', label: 'Erp编号', width: '140', sortable: 'custom' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'warehouseCode', label: '仓库', width: '160', sortable: 'custom', formatter: (row) => row.warehouseName, },
    { istrue: true, prop: 'purRemark', label: '采购备注', width: '200', sortable: 'custom' },
];

const purRemarkLogTableCols = [
    { istrue: true, prop: 'createdTime', label: '备注时间', width: '140', formatter: (row) => formatTime(row.createdTime, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'createdUserName', label: '操作人', width: '120', },
    { istrue: true, prop: 'operateContent', label: '操作内容', width: 'auto', },
];

export default {
    name: 'YunHanAutoStatistics',
    components: {MyContainer,vxetablebase},
    data(){
        return {
            platformlist,
            pickerOptions,
            that: this,
            tableCols: tableCols,
            data: {
                list: [],
                total: 0,
            },
            loading: false,
            brandList: [],
            titleList: [],
            deptList: [],
            filter: {
                startDate: null,
                endDate: null,
                brandId: null,
                indexNo: null,
                goodsCode: null,
                title: null,
                brandIds: [],
                titles: [],
                deptIds: [],
                timerange: [formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")],
                deptId: null,
                brandIds: [],
                titles: [],
                deptIds: [],
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false
            },
            purRemarkFilter:{
                id: null,
                indexNo: null,
                goodsCode: null,
                remark: null,
                visible: false
            },
            purRemarkLog:{
                visible: false
            },
            purRemarkLogList: [],
            purRemarkLogTableCols: purRemarkLogTableCols,
            purRemarkLogListLoading: false,
        }
    },
    async mounted() {
        await this.init();
    },
    methods: {
        async init() {
            //采购
            var res1 = await getAllProBrand();
            this.brandList = res1.data.map(item => {
                return { value: item.key, label: item.value };
            });
            //岗位
            var res2 = await getAutoBrandTitle();
            this.titleList = res2.data.map(item => {
                return { value: item, label: item };
            });
            //架构
            var res3 = await getAutoBrandDept();
            this.deptList = res3.data.map(item => {
                return { value: item.deptId, label: item.deptName };
            });
            await this.getList();
        },
        async getList(type) {
            if (type === "search") {
                this.filter.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }

            var param = {...this.filter};

            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const res = await getAutoStatisticsDtl(param);
                if (res?.success) {
                    this.data = res.data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        sizeChange(val) {
            this.filter.currentPage = 1;
            this.filter.pageSize = val;
            this.getList()
        },
        //当前页改变
        pageChange(val) {
            this.filter.currentPage = val;
            this.getList()
        },
        sortChange({ order, prop }) {
            if (prop) {
                this.filter.orderBy = prop
                this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        setPurRemark(row) {
            this.purRemarkFilter.indexNo = row.indexNo;
            this.purRemarkFilter.goodsCode = row.goodsCode;
            this.purRemarkFilter.remark = null;
            this.purRemarkFilter.visible = true;
        },
        async sumbit() {
            if (!this.purRemarkFilter.remark){
                this.$message.error("请输入采购备注！");
            }

            var param = {...this.purRemarkFilter};

            var res = await setAutoPurOrderRmark(param);
            if (res?.success){
                this.purRemarkFilter.visible = false;
                this.$message.success("保存成功！");
                await this.getList();
            }else{
                if (res?.msg)
                    this.$message.error(res?.msg);
                else
                    this.$message.error("保存失败！");
            }
        },
        async viewPurRemark(row) {
            var param = {
                indexNo: row.indexNo,
                goodsCode: row.goodsCode
            };

            var res = await getPurchaseAutoOrderRecordLogList(param);
            if (res?.success){
                this.purRemarkLogList = res?.data ?? [];
                this.purRemarkLog.visible = true;
            }
        },
    }
}

</script>

<style scoped lang="scss">
::v-deep .el-select__tags-text {
  max-width: 30px;
}

.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: end;
}
</style>