<template>
  <el-dialog title="查看结果" :visible.sync="isShow" width="50%" :before-close="closeDialog" v-if="isShow" v-dialogDrag>
    <div style="">
      <el-descriptions size="small" class="margin-top" title="" :column="3">
        <el-descriptions-item label="平台/店铺">{{ platformName }} / {{ dataJson.shopName }}</el-descriptions-item>
        <el-descriptions-item label="线上订单号">{{ dataJson.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ conversationTime }} <el-button style="margin-left:20px;" type="primary"
            @click="showOrhide">{{ this.isShowOrHide ? "收起聊天记录" : "展开聊天记录" }}</el-button></el-descriptions-item>
        <el-descriptions-item
          v-if="interfaceType && dataJson?.afterSalesRemark != '' && dataJson?.afterSalesRemark != null"
          label="售后原因">{{ dataJson?.afterSalesRemark }}</el-descriptions-item>
        <el-descriptions-item label = "运营组">{{dataJson.directorGroupUserName}}</el-descriptions-item>
        <el-descriptions-item label = "负责运营">{{dataJson.directorUserName}}</el-descriptions-item>
        <el-descriptions-item label = "采购">{{dataJson.brand}}</el-descriptions-item>
      </el-descriptions>
    </div>
    <!-- 聊天记录 -->
    <div v-show="isShowOrHide">
      <chartComponent ref="chartRef" :isShow="isShow"></chartComponent>
    </div>

    <el-form ref="formDataRef" label-width="100px" style="margin-top: 20px">
      <!-- 审核 -->
      <div style="border: 1px #eeeeee solid;padding:20px;">
        <div style="display: flex">
          <el-form-item label="审核:" prop="firstStatus" class="first custom-label">
            <div style="width: 50px;">{{ filterInitialAuditType(dataJson.initialAuditType) }}</div>
          </el-form-item>
          <el-form-item label="审核类型:" style="margin-left: 80px" class="custom-label"
            v-if="dataJson.initialAuditType == 2">
            <div style="width:300px">{{ dataJson.refuseInitialAuditType }}</div>
          </el-form-item>
        </div>
        <!-- 售后审核显示退款原因稽查，责任人 -->
        <div style="display: flex" v-if="interfaceType">
          <el-form-item label="责任人:" prop="person" class="custom-label" >
            <div style='width:100px'>{{ dataJson?.person }}</div>
          </el-form-item>
          <el-form-item label="退款原因稽查:" prop="reasonForRefund" style="margin-left:30px" class="custom-label" :rules="{ required: mustFilled }">
            <div>{{ dataJson?.reasonForRefund }}</div>
          </el-form-item>
        </div>
        <!-- 售后审核显示退款原因稽查，责任人 -->
        <div style="display: flex">
          <!-- <el-form-item label="责任客服:" prop="initialResponsibleName" class="custom-label">
            <div style='width:100px'>{{ dataJson.initialResponsibleName }}</div>
          </el-form-item> -->
          <el-form-item label="说明:" prop="firstExplain" class="custom-label" >
            <div>{{ dataJson.initialAuditRemark }}</div>
          </el-form-item>
        </div>
        <div style="display: flex">
          <el-form-item label="审核人:" prop="firstStatus" class="custom-label">
            <div style='width:100px'>{{ dataJson.initialOperator }}</div>
          </el-form-item>
          <el-form-item label="审核凭证:" prop="firstStatus" style="margin-left: 30px" class="custom-label" >
            <span v-for="(image, index) in iniImgsSplitList" :key="index">
              <el-image v-if="image" :src="image" :preview-src-list="[image]" style="padding-right:10px"></el-image>
              <span v-else>无</span>
            </span>
          </el-form-item>
        </div>

      </div>

    </el-form>
    <template #footer>
      <div class="dialog-footer" style="display:flex;justify-content: flex-end;margin-right:35px">
        <div style="position: relative;">
          <el-button @click="btnChange('last')" type="primary" :disabled="isLastButtonDisabled">查看上一个</el-button>
          <div style="position: absolute;right:-20px;top:-20px;cursor:pointer;">
            <el-tooltip class="item" effect="dark" content="点击键盘的上箭头↑，可以快速查看上一个" placement="top"><i
                class="el-icon-question"></i></el-tooltip>
          </div>
        </div>
        <div style="position: relative;margin-left:20px;">
          <el-button @click="btnChange('next')" type="primary" :disabled="isNextButtonDisabled">查看下一个</el-button>
          <div style="position: absolute;right:-20px;top:-20px; cursor:pointer;">
            <el-tooltip class="item" effect="dark" content="点击键盘的下箭头↓，可以快速查看下一个" placement="top"> <i
                class="el-icon-question"></i></el-tooltip>
          </div>
        </div>
      </div>
    </template>
  </el-dialog>
</template>
<script>
import chartComponent from "@/views/operatemanage/OperationalMiddleOffice/SalesDialog/chartComponent";
import { formatTime } from "@/utils";

export default {
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    chartComponent
  },
  computed: {
    platformName() {
      console.log(this.dataJson)
      let platformList = [
        { name: "拼多多", value: 2 },
        { name: "抖音", value: 6 },
        { name: "天猫", value: 1 },
        { name: "淘工厂", value: 8 },
        { name: "淘宝", value: 9 },
      ]
      if (this.dataJson?.platform) {
        return platformList.filter(item => item.value == this.dataJson?.platform)[0]?.name
      } else {
        return ""
      }
    },
    iniImgsSplitList() //审核图片分割
    {
      return this.dataJson?.initialAuditImgs ? this.dataJson?.initialAuditImgs.split(",") : "";
    },
    conversationTime() {//日期转换
      return this.dataJson?.conversationTime ? formatTime(this.dataJson?.conversationTime, "YYYY-MM-DD") : ""
    },
  },
  data() {
    return {
      dataJson: {},
      isShowOrHide: true,
      tableData: [],
      isLastButtonDisabled: false,
      isNextButtonDisabled: false,
      interfaceType: false,// true：售后 false：售前
      mustFilled: false
    };
  },
  created() {
    document.addEventListener('keydown', this.handleArrowUp);
  },
  watch: {
    isShow(newVal, oldVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.$refs.chartRef.dataJson = this.dataJson
        });
        this.buttonDisabled();//按钮是否可用
      }
    },
  },
  methods: {
    handleArrowUp(event) {
      if (!this.isShow) {
        return
      }
      console.log(event.key)
      if (event.key === 'ArrowUp' && !this.isLastButtonDisabled) {
        // 处理向上键被按下的逻辑
        this.btnChange('last');
      }
      if (event.key === 'ArrowDown' && !this.isNextButtonDisabled) {
        // 处理向上键被按下的逻辑
        this.btnChange('next');
      }
    },
    filterInitialAuditType(type) {
      let name = ''
      if (type == 1) {
        name = '合格'
      } else if (type == 2) {
        name = '不合格'
      }
      if (type == 2) {
        this.mustFilled = true;
      }
      if (type != 2) {
        this.mustFilled = false;
      }
      return name;
    },
    closeDialog() {
      this.$emit("closeDialog");
    },
    showOrhide() {
      if (this.isShowOrHide)
        this.isShowOrHide = false;
      else this.isShowOrHide = true
    },
    async btnChange(last) {  //查看上一个、查看下一个
      const index = this.tableData.indexOf(this.dataJson);
      if (last == 'last') {
        const info = this.tableData[index - 1]
        this.dataJson = info;
        this.keyWord = info.conversationId;
        this.platform = info.platform;
        this.$refs.chartRef.dataJson = info;
      } else if (last == 'next') {
        const info = this.tableData[index + 1]
        this.dataJson = info;
        this.keyWord = info.conversationId;
        this.platform = info.platform;
        this.$refs.chartRef.dataJson = info;
      }
      await this.buttonDisabled()//按钮是否禁用
    },
    async buttonDisabled() { //按钮是否禁用
      this.isLastButtonDisabled = false;
      this.isNextButtonDisabled = false;
      const index = this.tableData.indexOf(this.dataJson);
      if (index == 0) {
        this.isLastButtonDisabled = true;
      }
      if (index == this.tableData.length - 1) {
        this.isNextButtonDisabled = true;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__label {
  color: #888888 !important;
  /* 更改为你想要的颜色 */
}

::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
  padding: 15px;
}

/* 顶部可点击div样式 */
::v-deep .el-descriptions__body .el-descriptions__table {
  background-color: rgb(242, 244, 245);
}

::v-deep .el-descriptions__body .el-descriptions-item__container {
  font-size: 14px;
}

.first ::v-deep .el-form-item__label:before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}
</style>
