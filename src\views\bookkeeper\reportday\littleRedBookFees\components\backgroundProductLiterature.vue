<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="ListInfo.shopName" placeholder="店铺" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.specificationId" placeholder="规格id" maxlength="50" clearable
          class="publicCss" />
        <el-input v-model.trim="ListInfo.itemId" placeholder="itemId" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.merchantCode" placeholder="商家编码" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
      </div>
    </template>
    <vxetablebase :id="'backgroundProductLiterature202504251357'" :tablekey="'backgroundProductLiterature202504251357'"
      ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
      :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="120" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;align-items: center;justify-content: center;">
              <el-button type="text" @click="handleEdit(row)">编辑</el-button>
              <el-button type="text" @click="handleDelete(row)">删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="regionTitle" :visible.sync="regionVisible" width="40%" v-dialogDrag>
      <div style="padding: 20px 5px 0 5px;">
        <el-form :model="regionForm" :rules="regionrules" ref="refregionForm" label-width="100px" class="demo-ruleForm">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="店铺" :label-width="'125px'" prop="shopName">
                <el-input v-model.trim="regionForm.shopName" placeholder="店铺" maxlength="50" clearable
                  class="editCss" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="规格id" :label-width="'125px'" prop="specificationId">
                <el-input v-model.trim="regionForm.specificationId" placeholder="规格id" maxlength="50" clearable
                  class="editCss" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="itemId" :label-width="'125px'" prop="itemId">
                <el-input v-model.trim="regionForm.itemId" placeholder="itemId" maxlength="50" clearable
                  class="editCss" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="商家编码" :label-width="'125px'" prop="merchantCode">
                <el-input v-model.trim="regionForm.merchantCode" placeholder="商家编码" maxlength="50" clearable
                  class="editCss" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="regionVisible = false">取 消</el-button>
        <el-button type="primary" @click="onStashSave">确 定</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { importBackEndProductInformation_RedBookAsync, addorEditBackEndProductInformation_RedBook, deleteBackEndProductInformation_RedBookAsync, getBackEndProductInformation_RedBookList } from '@/api/bookkeeper/reportdayV2'
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'specificationId', label: '规格id', tipmesg: '销售主题分析内的店铺商品编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'itemId', label: 'itemId', tipmesg: '正确的店铺款式编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'merchantCode', label: '商家编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'dataCreateTime', label: '创建时间', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'updateTime', label: '更新时间', },
]
export default {
  name: "backgroundProductLiterature",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      regionrules: {
        shopName: [{ required: true, message: '请输入店铺', trigger: 'blur' }],
        specificationId: [{ required: true, message: '请输入规格id', trigger: 'blur' }],
        itemId: [{ required: true, message: '请输入itemId', trigger: 'blur' }],
        merchantCode: [{ required: true, message: '请输入商家编码', trigger: 'blur' }],
      },
      regionTitle: '添加区域',
      regionVisible: false,
      regionForm: {
        shopName: '',
        specificationId: '',
        itemId: '',
        merchantCode: '',
        id: null,
      },
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        shopName: '',
        specificationId: '',
        itemId: '',
        merchantCode: '',
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    handleDelete(row) {
      this.$confirm('此操作将删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await deleteBackEndProductInformation_RedBookAsync({ id: row.id })
        if (success) {
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
          this.getList()
        }
      }).catch(() => {
      });
    },
    onStashSave() {
      this.$refs.refregionForm.validate(async (valid) => {
        if (valid) {
          const { success } = await addorEditBackEndProductInformation_RedBook(this.regionForm)
          if (success) {
            this.$message.success('操作成功')
            this.regionVisible = false
            this.getList()
          } else {
            this.$message.error('操作失败')
          }
        }
      })
    },
    handleEdit(row) {
      this.regionTitle = '编辑'
      this.onCleardataMethod()
      this.regionForm = JSON.parse(JSON.stringify(row))
      this.regionForm.id = row.id
      this.regionVisible = true
    },
    onCleardataMethod() {
      this.$nextTick(() => {
        this.$refs.refregionForm.clearValidate();
      });
      this.stashForm = {
        shopName: '',
        specificationId: '',
        itemId: '',
        merchantCode: '',
        id: null,
      }
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importBackEndProductInformation_RedBookAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getBackEndProductInformation_RedBookList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 210px;
    margin-right: 5px;
  }
}

.editCss {
  width: 90%;
  margin-right: 5px;
}
</style>
