<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="ListInfo.warehouse" placeholder="仓库" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.expressCompany" placeholder="快递公司" maxlength="50" clearable
          class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'cloudWarehouseExpressFee202302031421'" ref="table" :that='that' :isIndex='true'
      :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
      :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="120">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" @click="onEditingMethod(row)">编辑</el-button>
              <el-button type="text" @click="onDeletionMethod(row)">删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="编辑" :visible.sync="editingVisible" width="30%" v-dialogDrag label-width="100px">
      <div style="height: 200px;">
        <el-form :model="editingInfo" label-position="left" :rules="editingRules" ref="editingInfo" label-width="100px"
          class="demo-editingInfo">
          <el-form-item label="仓库" prop="warehouse">
            <el-input v-model="editingInfo.warehouse" placeholder="请输入仓库" style="width: 85%;" />
          </el-form-item>
          <el-form-item label="快递公司" prop="expressCompany">
            <el-input v-model="editingInfo.expressCompany" placeholder="请输入快递公司" style="width: 85%;" />
          </el-form-item>
          <el-form-item label="快递费均价" prop="averageExpressFee">
            <el-input-number v-model.trim="editingInfo.averageExpressFee" placeholder="请输入快递费均价" :min="0" :max="99999"
              :precision="3" :controls="false" style="width: 85%;" />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editingVisible = false">关闭</el-button>
        <el-button type="primary" @click="onSaveMethod">保存</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import {
  pageExpressCompanyFeeAsync, exportExpressCompanyFeeAvgAsync, importExpressCompanyFeeAsync, deleteExpressCompanyFeeAvgAsync, AddOrUpdateExpressCompanyFeeAvgAsync
} from '@/api/financial/productcost'
const tableCols = [
  { sortable: 'custom', width: '350', align: 'center', prop: 'warehouse', label: '仓库', },
  { sortable: 'custom', width: '350', align: 'center', prop: 'expressCompany', label: '快递公司', },
  { sortable: 'custom', width: '350', align: 'center', prop: 'averageExpressFee', label: '快递费均价', },
]
export default {
  name: "cloudWarehouseExpressFee",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      editingVisible: false,
      editingInfo: {
        warehouse: null,
        expressCompany: null,
        averageExpressFee: null,
        id: null,
      },
      fileList: [],//上传文件列表
      dialogVisible: false,//导入弹窗
      uploadLoading: false,//上传loading
      fileparm: {},//上传参数
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        warehouse: null,
        expressCompany: null,
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      editingRules: {
        warehouse: [{ required: true, message: '请输入仓库', trigger: 'blur' }],
        expressCompany: [{ required: true, message: '请输入快递公司', trigger: 'blur' }],
        averageExpressFee: [{ required: true, message: '请输入快递费均价', trigger: 'blur' }],
      }
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importExpressCompanyFeeAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async onDeletionMethod(row) {
      this.$confirm('是否执行删除操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await deleteExpressCompanyFeeAvgAsync({ id: row.id })
        if (success) {
          this.$message({ type: 'success', message: '删除成功!' });
          this.getList()
        }
      }).catch(() => {
      });
    },
    onSaveMethod() {
      this.$refs.editingInfo.validate(async (valid) => {
        if (valid) {
          const { success } = await AddOrUpdateExpressCompanyFeeAvgAsync(this.editingInfo)
          if (success) {
            this.$message({ type: 'success', message: '保存成功!' });
            this.getList()
            this.editingVisible = false
          }
        }
      });
    },
    onEditingMethod(row) {
      this.editingVisible = true
      this.editingInfo = JSON.parse(JSON.stringify(row))
    },
    async exportProps() {
      this.loading = true
      const { data } = await exportExpressCompanyFeeAvgAsync(this.ListInfo)
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '云仓快递费数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await pageExpressCompanyFeeAsync(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 250px;
    margin-right: 5px;
  }
}
</style>
