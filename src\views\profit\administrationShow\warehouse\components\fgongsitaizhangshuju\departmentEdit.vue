<template>
    <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
      <el-scrollbar style="height: 100%">
        <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="140px" class="demo-ruleForm">
          <el-form-item label="1月份：">
              <inputNumberYh @input="computedone" v-model="ruleForm.oneMonthCount" :placeholder="'1月份'" class="publicCss" />
          </el-form-item>
          <el-form-item label="2月份：">
              <inputNumberYh @input="computedone" v-model="ruleForm.twoMonthCount" :placeholder="'2月份'" class="publicCss" />
          </el-form-item>
          <el-form-item label="3月份：">
              <inputNumberYh @input="computedone" v-model="ruleForm.threeMonthCount" :placeholder="'3月份'" class="publicCss" />
          </el-form-item>
          <el-form-item label="4月份：">
              <inputNumberYh @input="computedone" v-model="ruleForm.fourMonthCount" :placeholder="'4月份'" class="publicCss" />
          </el-form-item>
          <el-form-item label="5月份：">
              <inputNumberYh @input="computedone" v-model="ruleForm.fiveMonthCount" :placeholder="'5月份'" class="publicCss" />
          </el-form-item>
          <el-form-item label="6月份：">
              <inputNumberYh @input="computedone" v-model="ruleForm.sixMonthCount" :placeholder="'6月份'" class="publicCss" />
          </el-form-item>
          <el-form-item label="7月份：">
              <inputNumberYh @input="computedone" v-model="ruleForm.sevenMonthCount" :placeholder="'7月份'" class="publicCss" />
          </el-form-item>
          <el-form-item label="8月份：">
              <inputNumberYh @input="computedone" v-model="ruleForm.eightMonthCount" :placeholder="'8月份'" class="publicCss" />
          </el-form-item>
          <el-form-item label="9月份：">
              <inputNumberYh @input="computedone" v-model="ruleForm.nighMonthCount" :placeholder="'9月份'" class="publicCss" />
          </el-form-item>
          <el-form-item label="10月份：">
              <inputNumberYh @input="computedone" v-model="ruleForm.tenMonthCount" :placeholder="'10月份'" class="publicCss" />
          </el-form-item>
          <el-form-item label="11月份：">
              <inputNumberYh @input="computedone" v-model="ruleForm.elevenMonthCount" :placeholder="'11月份'" class="publicCss" />
          </el-form-item>
          <el-form-item label="12月份：">
              <inputNumberYh @input="computedone" v-model="ruleForm.twelveMonthCount" :placeholder="'12月份'" class="publicCss" />
          </el-form-item>

          
  
        </el-form>
      </el-scrollbar>
      <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
        <el-button @click="cancellationMethod">取消</el-button>
        <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
      </div>
    </div>
  </template>
  
  <script>
  import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
  import MyConfirmButton from '@/components/my-confirm-button'
  import { warehouseWorkInjuryLedgerSubmit } from '@/api/people/peoplessc.js';
  import checkPermission from '@/utils/permission'
  import decimal from '@/utils/decimal'
  export default {
    name: 'departmentEdit',
    components: {
      inputNumberYh, MyConfirmButton
    },
    props: {
      editInfo: {
        type: Object,
        default: () => {
          return {}
        }
      },
      districtList: {
        type: Object,
        default: () => {
          return {}
        }
      },
      typeList: {
        type: Object,
        default: () => {
          return {}
        }
      }
    },
    data() {
      return {
        selectProfitrates: [],
        ruleForm: {
          label: '',
          name: ''
        },
        rules: {
          name: [
            { required: true, message: '请输入活动名称', trigger: 'blur' }
          ],
          label: [
            { required: true, message: '请输入活动名称', trigger: 'blur' }
          ]
        }
      }
    },
  
    async mounted() {
      this.$nextTick(() => {
        this.$refs.refruleForm.clearValidate();
      });
      this.ruleForm = { ...this.editInfo };
    },
      methods: {
      computedone(){
          let a = this.ruleForm.totalNumWorkers ? this.ruleForm.totalNumWorkers :  0;
          let b = this.ruleForm.usedWorkstations ? this.ruleForm.usedWorkstations :  0;
  
          this.ruleForm.remainingWorkstations = decimal(a, b, 2, '-').toFixed(0);
          return decimal(a, b, 2, '-').toFixed(0);
      },
      cancellationMethod() {
        this.$emit('cancellationMethod');
      },
      submitForm(formName) {
          console.log(this.ruleForm.label, 'this.ruleForm.label');
        if (this.ruleForm.remainingWorkstations<0) {
          return this.$message.warning('剩余工位不能为负数');
        }
        this.$refs[formName].validate(async(valid) => {
            if (valid) {
              this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing")
              const { data, success } = await warehouseWorkInjuryLedgerSubmit(this.ruleForm)
              if(!success){
                  return
              }
              await this.$emit("search");
  
            } else {
              console.log('error submit!!');
              return false;
            }
          });
      //   this.$confirm('是否保存?', '提示', {
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //     type: 'warning'
      //   }).then(async () => {
      //     this.$refs[formName].validate(async(valid) => {
      //       if (valid) {
      //         const { data, success } = await warehouseWorkInjuryLedgerSubmit(this.ruleForm)
      //         if(!success){
      //             return
      //         }
      //         await this.$emit("search");
  
      //       } else {
      //         console.log('error submit!!');
      //         return false;
      //       }
      //     });
      //   }).catch(() => {
      //   });
      },
      resetForm(formName) {
        this.$refs[formName].resetFields();
      },
    }
  }
  </script>
  <style scoped lang="scss">
  .publicCss {
    width: 80%;
  }
  </style>
  