<template>
    <MyContainer>
        <el-tabs v-model="current" style="height: 95%;">
            <el-tab-pane label="TEMU半托-违规明细" name="tab1" style="height: 98%;">
                <TemuHalfViolationDetails style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="TEMU全托-违规明细" name="tab2" :lazy="true" style="height: 98%;">
                <TemuFullyViolationDetails style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="SHEIN-违规明细" name="tab3" :lazy="true" style="height: 98%;">
                <SheinViolationDetails style="height: 100%;" />
                
            </el-tab-pane>

        </el-tabs>
    </MyContainer>
</template>
<script>
import MyContainer from "@/components/my-container";
import TemuFullyViolationDetails from './tab/TemuFullyViolationDetails.vue'
import TemuHalfViolationDetails from './tab/TemuHalfViolationDetails.vue'
import SheinViolationDetails from './tab/SheinViolationDetails.vue'

export default {
    components: {
        MyContainer, TemuFullyViolationDetails,TemuHalfViolationDetails,SheinViolationDetails
    },
    data() {
        return {
            current: 'tab1'
        };
    },
    methods: {

    }
};
</script>

<style lang="scss" scoped></style>