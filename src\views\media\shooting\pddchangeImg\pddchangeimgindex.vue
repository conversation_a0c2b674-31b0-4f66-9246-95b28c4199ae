<template>
    <my-container v-loading="pageLoading">
        <el-tabs v-model="activeName" style="height:94%;" :before-leave="beforeLeave">
            <el-tab-pane name="tab0" label="任务列表" style="height: 100%;" :lazy="true">
                <changeimgtaskmain :tabkey="'changeimgtask'" :listtype="1" :role="currole" :platformList="platformList"
                    :warehouselist="warehouselist" :dockingPeopleList="dockingPeopleList"
                    :fpPhotoLqNameList="fpPhotoLqNameList" :taskUrgencyList="taskUrgencyList" :groupList="groupList"
                    :tablekey="'changeimgtask'" :islook="false" :filter="{ isShop: 0, isdel: 0, isComplate: 0 }" />
            </el-tab-pane>
            <el-tab-pane name="tab1" label="已确认" style="height: 100%;" :lazy="true">
                <changeimgtaskmain :tabkey="'changeimgtaskover'" :listtype="2" :role="currole" :platformList="platformList"
                    :warehouselist="warehouselist" :dockingPeopleList="dockingPeopleList"
                    :fpPhotoLqNameList="fpPhotoLqNameList" :taskUrgencyList="taskUrgencyList" :groupList="groupList"
                    :tablekey="'changeimgtaskover'" :filter="{ isShop: 0, isdel: 0, isComplate: 1 }" />
            </el-tab-pane>
            <el-tab-pane name="tab4" label="统计列表" style="height: 100%;"  v-if="checkPermission('pddchangeImgtjlb')" :lazy="true">
                <changeimgtaskmain :tabkey="'changeimgtaskcacl'" :listtype="3" :role="currole" :platformList="platformList"
                    :warehouselist="warehouselist" :dockingPeopleList="dockingPeopleList"
                    :fpPhotoLqNameList="fpPhotoLqNameList" :taskUrgencyList="taskUrgencyList" :groupList="groupList"
                    :tablekey="'changeimgtaskcacl'" :filter="{ isShop: 0, isdel: 0, IsTjInfo: 1 }" />
            </el-tab-pane>
            <el-tab-pane name="tab5" label="存档" style="height: 100%;" :lazy="true">
                <changeimgtaskmain :tabkey="'packdesgintaskshopinfo'" :listtype="4" :role="currole"
                    :platformList="platformList" :warehouselist="warehouselist" :dockingPeopleList="dockingPeopleList"
                    :fpPhotoLqNameList="fpPhotoLqNameList" :taskUrgencyList="taskUrgencyList" :groupList="groupList"
                    :tablekey="'packdesgintaskshopinfo'" :filter="{ isShop: 1 }" />
            </el-tab-pane>
            <el-tab-pane name="tab7" label="回收站" style="height: 100%;" :lazy="true">
                <changeimgtaskmain :tabkey="'changeimgtaskback'" :listtype="5" :role="currole" :platformList="platformList"
                    :warehouselist="warehouselist" :dockingPeopleList="dockingPeopleList"
                    :fpPhotoLqNameList="fpPhotoLqNameList" :taskUrgencyList="taskUrgencyList" :groupList="groupList"
                    :tablekey="'maintask'" :filter="{ isdel: 1 }" />
            </el-tab-pane>
            <el-tab-pane name="tab8" label="打包进度" style="height: 100%;" :lazy="true">
                <changeimgPackageInfo ref="changeimgPackageInfo" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="设置" name="tab17" style="height: 100%;"  :lazy="true"   v-if="checkPermission('vedioTask-fpsz')">
                <shootAutoAssginmanage ref="accountsWorkCount" :listtype="8"></shootAutoAssginmanage>
            </el-tab-pane>
            <el-tab-pane name="tab99" style="height: 100%;" :lazy="true">
                <span slot="label">
                    <el-link type="" style="color: #fff;" @click="toResultmatter">首页</el-link>
                </span>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import changeimgtaskmain from '@/views/media/shooting/pddchangeImg/new/pddchangeimgtaskmain';
import changeimgPackageInfo from '@/views/media/shooting/pddchangeImg/pddchangeimgPackageInfo';
import { getShootOperationsGroup } from '@/api/media/mediashare';
import { getUserRoleList, getShootingViewPersonAsync } from '@/api/media/pddchangeimg';
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import { rulePlatform } from "@/utils/formruletools";
import { ShootingVideoTaskUrgencyOptions } from "@/utils/tools";
import shootAutoAssginmanage from '@/views/media/shooting/shootAutoAssign/shootAutoAssginmanage';


export default {
    components: { MyContainer, changeimgtaskmain, changeimgPackageInfo,shootAutoAssginmanage },
    data() {
        return {
            currole: 'tz',
            pageLoading: false,
            platformList: [],//平台
            warehouselist: [],//仓库
            groupList: [],//运营组
            dockingPeopleList: [],//对接人
            fpPhotoLqNameList: [],//分配查询

            taskUrgencyList: ShootingVideoTaskUrgencyOptions,
            that: this,
            filter: {},
            activeName: 'tab0'
        };
    },
    async created() {
        await this.getrole();
    },
    async mounted() {

        await this.getDropDownList();
        await this.getShootingViewPerson();
    },
    methods: {
        toResultmatter() {
            this.$router.push({ path: '/media/index/homepage' })
        },
        beforeLeave(visitName, currentName) {
            if (visitName == "tab99")
                return false;
        },
        async getDropDownList() {
            var res = await getShootOperationsGroup({ type: 3 });
            this.warehouselist = res?.map(item => { return { value: item.id, label: item.label }; });
            var pfrule = await rulePlatform();
            this.platformList = pfrule.options;
            var res = await getDirectorGroupList();
            this.groupList = res.data?.map(item => { return { value: item.key, label: item.value }; });
        },
        async getShootingViewPerson() {
            var res = await getShootingViewPersonAsync();
            if (res?.success) {
                this.dockingPeopleList = res.data.dockingPeopleList;
                this.fpPhotoLqNameList = res.data.fpallList;
            }
        },

        async getrole() {
            var res = await getUserRoleList();
            if (res?.success) {
                if (res.data == null) {
                    this.currole = "tz";
                } else if (res.data.indexOf("视觉部经理") > -1) {
                    this.currole = "b";
                }

            } else {
                this.currole = "tz";
            }
        },
    },
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
    margin-bottom: 20px;
}
</style>

