<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="日期">
                    <el-date-picker style="width: 249px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" :clearable="false"
                        end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <el-form-item label="发生时间">
                    <el-date-picker style="width: 249px" v-model="filter.timerange1" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" :clearable="true"
                        end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <el-form-item label="线上单号:">
                    <!-- <el-input v-model.trim="filter.ProCode" :clearable="true" maxlength="20" placeholder="线上单号" style="width:130px;"/> -->
                    <inputYunhan ref="productCode2" :inputt.sync="filter.OrderNo" v-model="filter.OrderNo"
                        class="publicCss" placeholder="线上单号/多条请按回车" :clearable="true" :clearabletext="true"
                        :maxRows="2000" :maxlength="600000" @callback="orderNoInnerBack" title="线上单号">
                    </inputYunhan>
                </el-form-item>
                <el-form-item label="店铺:">
                  <YhShopSelector :names="['所有店铺']" :values="[-1]"  platform="1,9" :checkboxOrRadio="'checkbox'" :addAllWarehouseConcept="true" @onChange="(v)=>{filter.shopCode=v[0].join(','); }">
                    </YhShopSelector>
                </el-form-item>
                <el-form-item label="账单项目:">
                    <el-select filterable v-model="accountTypeist" placeholder="请选择账单项目"  multiple  clearable style="width: 130px">
                        <el-option label="微信货款" value="微信货款" />
                        <el-option label="微信退款" value="微信退款" />
                        <el-option label="基础软件服务费" value="基础软件服务费" />
                        <el-option label="官方竞价软件服务费" value="官方竞价软件服务费" />
                        <el-option label="保证金扣款" value="保证金扣款" />
                        <el-option label="跨境服务增值费" value="跨境服务增值费" />
                        <el-option label="品牌新享" value="品牌新享" />
                        <el-option label="品牌新享-首单拉新计划" value="品牌新享-首单拉新计划" />
                        <el-option label="其他账单费" value="其他账单费" />
                        <el-option label="淘宝内容推广服务费" value="淘宝内容推广服务费" />
                        <el-option label="新客礼金技术服务费" value="新客礼金技术服务费" />
                        <el-option label="新品礼金技术服务费" value="新品礼金技术服务费" />
                        <el-option label="淘宝直播店技术服务费" value="淘宝直播店技术服务费" />
                        <el-option label="聚划算佣金" value="聚划算佣金" />
                        <el-option label="跨境服务增值费" value="跨境服务增值费" />
                        <el-option label="未处理" value="未处理" />
                    </el-select>
                </el-form-item>

               <el-form-item label="ERP账务类型:">
                    <el-select filterable  v-model="BillTypeList" placeholder="请选择ERP账务类型"  multiple clearable style="width: 130px">
                        <el-option label="基础软件服务费" value="基础软件服务费" />
                        <el-option label="其他账单费" value="其他账单费" />
                        <el-option label="保证金扣款" value="保证金扣款" />
                        <el-option label="跨境服务增值费" value="跨境服务增值费" />
                        <el-option label="品牌新享（不计算）" value="品牌新享（不计算）" />
                        <el-option label="品牌新享-首单拉新扣款" value="品牌新享-首单拉新扣款" />
                        <el-option label="品牌新享新品孵化软件服务费" value="品牌新享新品孵化软件服务费" />
                        <el-option label="淘宝内容推广服务费" value="淘宝内容推广服务费" />
                        <el-option label="无" value="无" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                <el-switch v-model="filter.isGroup"  active-color="#67C23A"  inactive-color="#409EFF"
                    @change="onSearch"   active-text="汇总查询"  active-value="1" inactive-value="0"  inactive-text="全量查询">
                    </el-switch>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
            @select="selectchange" :tableHandles='tableHandles' @cellclick="cellclick" :loading="listLoading">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <!-- <el-dialog title="导入数据" :visible.sync="dialogVisible" :close-on-click-modal="false" width="40%" v-dialogDrag>
            <el-row>
                <el-col :xs="4" :sm="6" :md="8" :lg="6">
                    <el-date-picker style="width: 100%" v-model="importfilter.YearMonthDay" type="date" format="yyyyMMdd"
                    value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
                  </el-col>
                <el-col :xs="4" :sm="6" :md="8" :lg="6" >
                    <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="4" action
                        accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove"
                        :file-list="fileList">
                        <template #trigger>
                            <el-button size="small" type="primary">选取文件</el-button>
                        </template>
                        <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                            @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }} </el-button>
                    </el-upload>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog> -->
    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { platformlist} from '@/utils/tools'
import { formatTime, formatTime1 } from "@/utils";
import {getNewTXBillingChargeWX,exportNewTXBillingChargeWX} from '@/api/bookkeeper/reportdayV2'
import inputYunhan from "@/components/Comm/inputYunhan";
import YhShopSelector from "@/components/YhCom/YhShopSelector";

const tableCols = [
{ istrue: true, prop: 'yearMonthDay', label: '日期', tipmesg: '', width: '80', sortable: 'custom', },
    { istrue: true, prop: 'occurrenceTime', label: '入账时间', tipmesg: '', width: '90', sortable: 'custom', },
    { istrue: true, prop: 'orderNo', label: '线上单号', tipmesg: '', width: '180', sortable: 'custom', },
    { istrue: true, prop: 'shopCode', label: '店铺', sortable: 'custom',  tipmesg: '', width: '200', formatter: (row) => !row.shopName ? " " : row.shopName },
    { istrue: true, prop: 'billingItem', label: '账单项目', tipmesg: '', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'billType', label: 'ERP账务类型', tipmesg: '', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'businessType', label: '入账类型', tipmesg: '', width: '90', sortable: 'custom', },
    { istrue: true, prop: 'totalAmount', label: '金额', tipmesg: '收入金额-支出金额', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'inAmountIncome', label: '收入金额（元）', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'outAmountIncome', label: '支出', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'remark', label: '备注', tipmesg: '', sortable: 'custom', },
]

const tableHandles = [
];

const startTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const curMonth = formatTime1(dayjs().startOf("month").subtract(1, "month"), "yyyyMM");

export default {
    name: 'YunHanAdminIndex',
    components: { container, cesTable, MyConfirmButton ,inputYunhan,YhShopSelector},

    data() {
        return {
            that: this,
            importfilter: {
             YearMonthDay:null
            },
            filter: {
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime],
                shopCode: null,
                BillType:null,
                timerange1:[],
                startOccTime: null,
                endOccTime: null,
                OrderNo:null,
            },
            platformlist:platformlist,
            importDialog: {
                filter: {
                    settMonth: curMonth,
                    platform: null
                }
            },
            BillTypeList:[],
            accountTypeist:[],
            list: [],
            shopList: [],
            summaryarry: {},
            pager: { OrderBy: "RecordId", IsAsc: false },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            pickOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            // onHandNumber: null,
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            sels: [],
            // editparmLoading: false,
            uploadLoading: false,
            // editparmLoading1: false,
            // editparmLoading2: false,
            // editparmVisible: false,
            // editparmVisible1: false,
            // editparmVisible2: false,
            //dialogVisible: false,
            listLoading: false,
            // showDetailVisible: false,
            fileList: []
        };
    },

    async mounted() {
        await this.onSearch()
        //await this.onchangeplatform()
    },

    methods: {
    async onExport() {
     if (this.onExporting) return;
     try{
        this.filter.startTime = null;
        this.filter.endTime = null;
        this.filter.startOccTime = null;
        this.filter.endOccTime = null;
        if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            if (this.filter.timerange1) {
                this.filter.startOccTime = this.filter.timerange1[0];
                this.filter.endOccTime = this.filter.timerange1[1];
            }
            this.filter.BillType = this.BillTypeList.join(',');
            this.filter.AccountType = this.accountTypeist.join(',');
        this.uploadLoading = true;
        const params = {...this.pager,...this.filter}
        var res= await exportNewTXBillingChargeWX(params);
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','新版淘系日报微信账单费用_' + new Date().toLocaleString() + '.xlsx' )
        this.uploadLoading = false;

        aLink.click()
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
      this.onExporting=false;
     },

        //获取店铺
        // async onchangeplatform() {
        //     this.categorylist = []
        //     const res1 = await getshopList({ platform: this.filter.platform, CurrentPage: 1, PageSize: 100 });
        //     this.filter.shopCode = null
        //     this.shopList = res1.data.list
        // },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.startTime = null;
        this.filter.endTime = null;
        this.filter.startOccTime = null;
        this.filter.endOccTime = null;
        if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            if (this.filter.timerange1) {
                this.filter.startOccTime = this.filter.timerange1[0];
                this.filter.endOccTime = this.filter.timerange1[1];
            }
            this.filter.BillType = this.BillTypeList.join(',');
            this.filter.AccountType = this.accountTypeist.join(',');
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await getNewTXBillingChargeWX(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            this.list = data
        },
        async nSearch() {
            await this.getlist()
        },
        orderNoInnerBack(val) {
            this.filter.OrderNo = val;
        },
        //字体颜色
        // renderRefundStatus(row) {
        //     if (row.refundStatus == '成功退款' || row.refundStatus == '等待退款') {
        //         return "color:red;cursor:pointer;";
        //     } else return "";
        // },
        //开始导入
        // startImport() {
        //    // this.importDialog.filter.platform = null
        //     this.fileList = []
        //     this.uploadLoading=false
        //     this.dialogVisible = true;
        // },
        // //取消导入
        // cancelImport() {
        //     this.dialogVisible = false;
        // },
        // uploadSuccess(response, file, fileList) {
        //     if (response.code == 200) {
        //     } else {
        //         fileList.splice(fileList.indexOf(file), 1);
        //     }
        // },
        // async submitUpload() {
        //     if (!this.importfilter.YearMonthDay || this.importfilter.YearMonthDay == null) {
        // this.$message({ message: "请先选择日期", type: "warning" });
        // return false;
        //    }
        //     if (!this.fileList || this.fileList.length == 0) {
        //         this.$message({ message: "请选取文件", type: "warning" });
        //         return false;
        //     }
        //     this.fileHasSubmit = true;
        //     this.uploadLoading = true;
        //     this.$refs.upload.submit();
        // },
        // clearFiles(){
        //     this.$refs['upload'].clearFiles();
        // },
        // async uploadFile(item) {
        //     if (!this.fileHasSubmit) {
        //         return false;
        //     }
        //     this.fileHasSubmit = false;
        //     this.uploadLoading = true;
        //     const form = new FormData();
        //     //form.append("platForm", this.importDialog.filter.platform);
        //     form.append("token", this.token);
        //     form.append("upfile", item.file);
        //     form.append("YearMonthDay", this.importfilter.YearMonthDay);
        //     let res = await importBillFee(form);
        //         if (res.code == 1) {
        //             this.$message({ message: "上传成功,正在导入中...", type: "success" });
        //             this.$refs.upload.clearFiles();
        //             this.dialogVisible = false;
        //         }
        //     this.fileList = []
        //     this.uploadLoading = false;
        // },
        // async uploadChange(file, fileList) {
        //     let files=[];
        //     files.push(file)
        //     this.fileList = files;
        // },
        // async uploadRemove(file, fileList) {
        //     this.fileList = []
        // },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event) {

        },
    }
};
</script>

<style lang="scss" scoped>

</style>
