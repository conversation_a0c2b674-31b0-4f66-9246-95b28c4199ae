<template>
    <MyContainer style="position: relative;">
        <template #header>
            <div class="top" @click="getFocus">
                <el-select v-model="query.wmsId" placeholder="发货仓" class="publicCss" clearable
                    @change="changeWareHouse">
                    <el-option v-for="item in wareHouseList" :key="item.wms_co_id" :label="item.name"
                        :value="item.wms_co_id" />
                </el-select>
                <el-input v-model="query.expressNo" :placeholder="query.isGoodsCode ? '商品编码' : '物流单号'" maxlength="200"
                    clearable class="publicCss" ref="input" @keyup.enter.native="getProps" />
                <el-button type="primary" @click="getProps" style="margin-right: 10px;">搜索</el-button>
                <el-switch v-model="query.isGoodsCode" active-text="商品编码" inactive-text="物流单号" @change="changeCode"
                    style="margin-right: 20px;" />
                <el-switch v-model="query.isSecondScan" active-text="二次扫码" inactive-text="一次扫码" @change="changeType" />
            </div>
        </template>
        <div class="main" @click="getFocus" v-if="query.isSecondScan">
            <div v-for="(item, i) in 4" :class="['main_item', index == i ? 'active' : '']">
                <div class="main_item_top">
                    <div class="item_fixed_top"><span>{{ i + 1 }}</span></div>
                    <vxetablebase :id="'operateUser202408041750_1'" v-if="i == 0" ref="table1" :that='that' :isIndex='true' :hasexpand='true'
                        :indexWidth="40" :tablefixed='true' :tableData='passageOne' :tableCols='tableCols'
                        :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" :height="'100%'"
                        v-loading="pageLoading" class="already" />
                    <vxetablebase :id="'operateUser202408041750_2'" v-if="i == 1" ref="table2" :that='that' :isIndex='true' :hasexpand='true'
                        :indexWidth="40" :tablefixed='true' :tableData='passageTwo' :tableCols='tableCols2'
                        :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" :height="'100%'"
                        v-loading="pageLoading" class="already" />
                    <vxetablebase :id="'operateUser202408041750_3'" v-if="i == 2" ref="table3" :that='that' :isIndex='true' :hasexpand='true'
                        :indexWidth="40" :tablefixed='true' :tableData='passageThree' :tableCols='tableCols3'
                        :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" :height="'100%'"
                        v-loading="pageLoading" class="already" />
                    <vxetablebase :id="'operateUser202408041750_4'" v-if="i == 3" ref="table4" :that='that' :isIndex='true' :hasexpand='true'
                        :indexWidth="40" :tablefixed='true' :tableData='passageFour' :tableCols='tableCols4'
                        :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" :height="'100%'"
                        v-loading="pageLoading" class="already" />
                </div>
                <div class="main_item_bottom" v-if="i == 0">共{{ passageOne.length }}件</div>
                <div class="main_item_bottom" v-if="i == 1">共{{ passageTwo.length }}件</div>
                <div class="main_item_bottom" v-if="i == 2">共{{ passageThree.length }}件</div>
                <div class="main_item_bottom" v-if="i == 3">共{{ passageFour.length }}件</div>
            </div>
        </div>
        <div v-else style="height: 100%;" @click="getFocus">
            <div style="height: 95%;">
                <vxetablebase :id="'operateUser202408041750_5'" ref="table1" :that='that' :isIndex='true' :hasexpand='true' :indexWidth="40"
                    :tablefixed='true' :tableData='firstScanTable' :tableCols='tableCols5' :isSelection="false"
                    :isSelectColumn="false" style="width: 100%;  margin: 0" :height="'100%'" v-loading="pageLoading"
                    class="already" />
            </div>
            <div class="main_item_bottom">共{{ firstScanTable.length }}件</div>
        </div>
        <audio src="../../../../static/audio/一号通道.mp3" ref="one"></audio>
        <audio src="../../../../static/audio/二号通道.mp3" ref="two"></audio>
        <audio src="../../../../static/audio/三号通道.mp3" ref="three"></audio>
        <audio src="../../../../static/audio/四号通道.mp3" ref="four"></audio>
        <audio src="../../../../static/audio/已扫码.mp3" ref="five"></audio>
        <audio src="../../../../static/audio/扫码失败.mp3" ref="six"></audio>
        <audio src="../../../../static/audio/请拆包检查包裹.mp3" ref="seven"></audio>

        <div :class="isSuccess === 1 ? 'success' : 'error'" v-show="isSuccess && !query.isSecondScan">
            <i :class="isSuccess === 1 ? 'el-icon-success' : 'el-icon-error'"></i>
            <span style="margin-left: 20px;">{{ isSuccess === 1 ? '扫码成功！' : '扫码失败！' }}</span>
        </div>


        <el-dialog title="提示" :visible.sync="dialogHisVisible" width="800px" v-dialogDrag>
            <div style="color: red;margin-bottom: 10px;">请拆包检查包裹！！！</div>
            <vxetablebase :id="'operateUser202408041750_6'" ref="table1" :that='that' :isIndex='true' :hasexpand='true' :indexWidth="40"
                :tablefixed='true' :tableData='bgTableData' :tableCols='tableCols6' :isSelection="false"
                :isSelectColumn="false" style="width: 100%;height: 200px;  margin: 0" class="already" />
            <div class="btngroup">
                <el-button @click="errorOperte">破损</el-button>
                <el-button type="primary" @click="unErrorOperte">无损</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { returnOrderScan, print, reject } from "@/api/vo/ReturnOrderScan";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from "dayjs";
import { pageGetTbWarehouseAsync } from "@/api/inventory/prepack.js"
import {
    //分页查询店铺商品资料
    getList,
    //导入
    importData,
} from "@/api/inventory/basicgoods"
const tableCols = [
    { istrue: true, align: 'left', label: '快递单号', prop: 'expressNo', width: 'auto', },
    { istrue: true, align: 'right', label: '扫码时间', prop: 'scanTime', width: 'auto', formatter: (row) => row.scanTime ? dayjs(row.scanTime).format('HH:mm:ss') : '' },
    {
        istrue: true, align: 'right', label: '操作', width: 'auto', type: 'button', btnList: [
            { label: '打印', handle: (that, row) => that.printPage(row.orderNoInner) }
        ]
    },
]
const tableCols2 = [
    { istrue: true, align: 'left', label: '快递单号', prop: 'expressNo', width: 'auto', },
    { istrue: true, align: 'right', label: '扫码时间', prop: 'scanTime', width: 'auto', formatter: (row) => row.scanTime ? dayjs(row.scanTime).format('HH:mm:ss') : '' },
]
const tableCols3 = [
    { istrue: true, align: 'left', label: '快递单号', prop: 'expressNo', width: 'auto', },
    { istrue: true, align: 'right', label: '扫码时间', prop: 'scanTime', width: 'auto', formatter: (row) => row.scanTime ? dayjs(row.scanTime).format('HH:mm:ss') : '' },
]
const tableCols4 = [
    { istrue: true, align: 'left', label: '快递单号', prop: 'expressNo', width: 'auto', },
    { istrue: true, align: 'right', label: '扫码时间', prop: 'scanTime', width: 'auto', formatter: (row) => row.scanTime ? dayjs(row.scanTime).format('HH:mm:ss') : '' },
]
const tableCols5 = [
    { istrue: true, label: '快递单号', prop: 'expressNo', width: 'auto', },
    { istrue: true, label: '扫码时间', prop: 'scanTime', width: 'auto', formatter: (row) => row.scanTime ? dayjs(row.scanTime).format('HH:mm:ss') : '' },
]

const tableCols6 = [
    { istrue: true, label: '商品图片', prop: 'picture', width: 'auto', type: 'images' },
    { istrue: true, label: '商品编码', prop: 'goodsCode', width: 'auto', },
    { istrue: true, label: '商品名称', prop: 'goodsName', width: 'auto', },
    { istrue: true, label: '数量', prop: 'num', width: 'auto', },
]
export default {
    name: "operateUser",
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            pageLoading: false,
            passageOne: [], //通道一
            passageTwo: [], //通道二
            passageThree: [], //通道三
            passageFour: [], //通道四
            tableCols,
            tableCols2,
            tableCols3,
            tableCols4,
            tableCols5,
            tableCols6,
            that: this,
            index: null,
            lastExpressNo: null,
            bgTableData: [],
            query: {
                isSecondScan: false,//是否是二次扫码
                expressNo: null,//快递单号
                wmsId: null,//仓库id
                wmsName: null,//仓库名称
                isGoodsCode: false,//是否是商品编码
                orderNoInner: null//订单号
            },
            wareHouseList: [],
            orderNoInner: null,
            dialogHisVisible: false,
            firstScanTable: [],
            isSuccess: 0,
            filtration:['R02T','r02t']
        }
    },
    async mounted() {
        await this.getWareHouse()
        this.getFocus()
    },
    methods: {
        //查询破损包裹信息
        async getBrekenProps(skus) {
            this.bgTableData = []
            //先按,切割,再按*切割,获取到商品编码和数量
            const arr = skus.split(',')
            const res = arr.map(item => {
                const [code, num] = item.split('*')
                return {
                    goodsCode: code,
                    num: num
                }
            })
            //根据商品编码获取商品信息
            const params = {
                currentPage: 1,
                pageSize: 1000,
                orderBy: null,
                isAsc: false,
                goodsCode: res.map(item => item['goodsCode']).join(',')
            }
            const { data: { list }, success } = await getList(params)
            if (success) {
                this.bgTableData = list.map((item, index) => {
                    return {
                        ...item,
                        ...res[index]
                    }
                })
            } else {
                this.$message.error('暂未查询到相关商品信息')
            }
        },
        async unErrorOperte() {
            const { success } = await print({ orderNoInner: this.orderNoInner, IsSecondScan: this.query.isSecondScan })
            if (success) {
                this.dialogHisVisible = false
                this.$message.success('打印成功')
            } else {
                this.dialogHisVisible = false
                this.$message.error('打印失败')
            }
        },
        async errorOperte() {
            const { success } = await reject({ orderNoInner: this.orderNoInner })
            if (success) {
                this.dialogHisVisible = false
                this.$message.success('操作成功')
            } else {
                this.dialogHisVisible = false
                this.$message.error('操作失败')
            }
        },
        changeWareHouse(e) {
            this.query.wmsName = e ? this.wareHouseList.find(item => item.wms_co_id == e).name : null
        },
        async getWareHouse() {
            const params = {
                currentPage: 1,
                pageSize: 1000,
                orderBy: null,
                isAsc: false,
            }
            const { data: { list } } = await pageGetTbWarehouseAsync(params)
            this.wareHouseList = list
        },
        changeCode(e) {
            this.tableCols.find(item => item.prop == 'expressNo').label = e ? '商品编码' : '物流单号'
            this.tableCols2.find(item => item.prop == 'expressNo').label = e ? '商品编码' : '物流单号'
            this.tableCols3.find(item => item.prop == 'expressNo').label = e ? '商品编码' : '物流单号'
            this.tableCols4.find(item => item.prop == 'expressNo').label = e ? '商品编码' : '物流单号'
            this.tableCols5.find(item => item.prop == 'expressNo').label = e ? '商品编码' : '物流单号'
            this.passageOne = []
            this.passageTwo = []
            this.passageThree = []
            this.passageFour = []
            this.firstScanTable = []
            this.query.expressNo = null
            this.query.wmsId = null
            this.query.wmsName = null
        },
        changeType() {
            this.expressNo = null
            this.lastExpressNo = null
            this.passageOne = []
            this.passageTwo = []
            this.passageThree = []
            this.passageFour = []
            this.firstScanTable = []
        },
        async printPage(orderNoInner) {
            this.query.orderNoInner = orderNoInner
            const { success } = await print(this.query)
            if (success) {
                this.$message.success('打印成功')
            } else {
                this.$message.error('打印失败')
            }
        },
        showTips(val) {
            this.isSuccess = val
            if (this.timer) {
                clearTimeout(this.timer)
            }
            this.timer = setTimeout(() => {
                this.isSuccess = 0
            }, 2000)
        },
        async getProps() {
            if (!this.query.expressNo) return this.$message.error('物流单号为空')
            //过滤掉圆通的特殊字符
            this.filtration.forEach(item => {
                if(this.query.expressNo.includes(item)){
                    this.query.expressNo = this.query.expressNo.replace(item, '')
                }
            })
            //物流单号长度小于7或者大于18,就提示
            if (!this.query.isGoodsCode && (this.query.expressNo.length < 7 || this.query.expressNo.length > 18)) {
                this.$refs.six.play()
                this.showTips(2)
                return
            }
            if (!this.query.wmsId) return this.$message.error('仓库为空')
            this.query.expressNo = this.query.expressNo.replace(/\s*/g, '')
            //如果有特殊符号就报错或者与上次一样就报错
            if ((this.lastExpressNo == this.query.expressNo) && this.lastExpressNo && !this.query.isSecondScan) return
            if ((this.query.expressNo.match(/[^0-9a-zA-Z]/) && !this.query.isGoodsCode)) {
                this.$refs.six.play()
                this.showTips(2)
                return
            }
            var regex = /[^a-zA-Z0-9-]/
            if (regex.test(this.query.expressNo) && this.query.isGoodsCode) {
                this.$refs.six.play()
                this.showTips(2)
                return
            }
            this.showTips(1)
            this.lastExpressNo = this.query.expressNo
            const expressNo = this.query.expressNo
            if (!this.query.isSecondScan) {
                this.query.expressNo = null
            }
            const params = {
                expressNo,
                wmsId: this.query.wmsId,
                isSecondScan: this.query.isSecondScan,
                isGoodsCode: this.query.isGoodsCode,
                wmsName: this.query.wmsName
            }
            if (!this.query.isSecondScan) {
                this.firstScanTable.unshift({
                    expressNo,
                    scanTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
                })
            } else {
                this.pageLoading = true
            }
            const { data, success } = await returnOrderScan(params)
            if (success) {
                this.pageLoading = false
                const map = {
                    1: {
                        data: this.passageOne,
                        fn: () => this.$refs.one.play()
                    },
                    2: {
                        data: this.passageTwo,
                        fn: () => this.$refs.two.play()
                    },
                    3: {
                        data: this.passageThree,
                        fn: () => this.$refs.three.play()
                    },
                    4: {
                        data: this.passageFour,
                        fn: () => this.$refs.four.play()
                    }
                }
                if (this.query.isSecondScan) {
                    this.index = data.status - 1
                    map[data.status].data.unshift(data)
                    this.query.expressNo = null
                    if (data.tipCheck) {
                        this.$refs.seven.play()
                        this.orderNoInner = data.newOrderNoInner
                        this.dialogHisVisible = true
                        if (data.skus) {
                            this.getBrekenProps(data.skus)
                        }
                    } else {
                        map[data.status].fn()
                    }
                }
                this.getFocus()
            } else {
                this.pageLoading = false
                this.$refs.six.play()
            }
        },
        getFocus() {
            this.$refs.input.focus()
        }
    }
}
</script>

<style scoped lang="scss">
.btngroup {
    margin-top: 10px;
    display: flex;
    justify-content: center;
}

.top {
    display: flex;
    margin-bottom: 10px;
    align-items: center;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.main {
    height: 85vh;
    width: 100%;
    display: flex;
    padding: 5px;
    overflow: auto;
    box-sizing: border-box;
    flex-wrap: wrap;
    justify-content: space-between;
    //边框线合并
    border-collapse: collapse;

    .main_item {
        width: 24%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        box-sizing: border-box;
        border: 1px solid #ccc;

        .main_item_top {
            flex: 1;
            display: flex;
            font-size: 16px;
            box-sizing: border-box;
            position: relative;
            flex-direction: column;
            overflow-y: hidden;

            .item_fixed_top {
                position: absolute;
                top: 5px;
                left: 0px;
                width: 100%;
                // height: 30px;
                z-index: 1000;
                font-weight: 700;
                color: #fff;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;
                border-top-left-radius: 0px;

                span {
                    font-size: 16px;
                    text-align: center;
                    width: 30px;
                    height: 30px;
                    background-color: #409EFF;
                    border-radius: 50%;
                    line-height: 30px;
                    margin: auto;
                }
            }
        }

        .main_item_bottom {
            height: 40px;
            width: 100%;
            display: flex;
            justify-content: end;
            font-size: 14px;
            padding: 10px;
            box-sizing: border-box;
        }
    }
}

.active {
    //红色盒子阴影
    outline: #409EFF solid 2px;
}

.already ::v-deep .vxe-tools--operate {
    display: none !important;
}

::v-deep .vxe-table--render-default .vxe-cell {
    padding-left: 10px !important;
    padding-right: 10px !important;
}

.success {
    width: 200px;
    height: 35px;
    opacity: 1;
    background-color: rgb(240, 249, 235);
    color: rgb(24, 151, 33);
    text-align: center;
    line-height: 35px;
    font-size: 16px;
    border-radius: 3px;
    position: fixed;
    top: 100px;
    right: 17px;
    transition: all 0.5s;
}

.error {
    width: 200px;
    height: 35px;
    opacity: 1;
    background-color: #fef0f0;
    color: #f56c6c;
    text-align: center;
    line-height: 35px;
    font-size: 16px;
    border-radius: 3px;
    position: fixed;
    top: 100px;
    right: 17px;
    transition: all 0.5s;
}
</style>