<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-input v-model="input" v-model.trim="ListInfo.styleCode" placeholder="款式编码" maxlength="50" clearable class="publicCss" style="width:150px" />
                <div class="publicCss">
                    <inputYunhan ref="goodsCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode" width="200px" :valuedOpen="true"
                        placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="800" :maxlength="8000"
                        @callback="callbackGoodsCode" title="商品编码">
                    </inputYunhan>
                </div>
                <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable class="publicCss" style="width:150px" />
                <el-button type="primary" @click="getlist('search')">搜索</el-button>
                <el-button type="primary" @click="onShowAddCodeDialog">新增</el-button>
                <el-button type="primary" @click="startImport" v-if="checkPermission('Api:ImportInventory:Purchase:ImportGoodsBomData')">导入</el-button>
                <el-button type="primary" @click="downloadTemplate">下载导入模版</el-button>
            </div>
        </template>
        <!--
        <vxetablebase :id="'goodsBomDataList20241008'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
        :tablefixed='true' @sortchange='sortchange' :tableData='list' :tableCols='tableCols' :isSelection="false"
        :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0"
        :loading="listLoading" :height="'100%'" :border="true">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
        -->
        <el-container style="height: 100%; border: 1px solid #eee">
            <el-aside width="80%" style="background-color: rgb(238, 241, 246)">
                <noheaderContainer>
                    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :summaryarry="summaryarry" 
                        :tableData='list' :tableCols='tableCols' :isSelection="false" @cellClick="cellClick" :loading="listLoading" style="overflow: hidden;">
                    </vxetablebase>
                    <template #footer>
                        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
                    </template>
                </noheaderContainer>
            </el-aside>
            <noheaderContainer>
                <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='false' style="width:340px;overflow: hidden;" :tableData='detail' 
                    :tableCols='tableDtlCols' :isSelection="false" :loading="detailLoading">
                </vxetablebase>
                <!-- <template #footer>
                    <my-pagination ref="pagerDetail" :total="detail.total" :checked-count="detail.length" @get-page="getDetailList" />
                </template> -->
            </noheaderContainer>
        </el-container>

        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
            <span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                    @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="新增" :visible.sync="adddialogVisible" width="25%" v-dialogDrag >
            <div style="height: 200px;width: 100%;">
                <el-input type="textarea" placeholder="请分行输入" v-model="addGoodsCode" resize="none" rows="800" :autosize="{ minRows: 10, maxRows: 10}" show-word-limit />
            </div>
            <div style="display: flex;justify-content: center;align-items: center;">
                <el-button  type="primary" style="width: 150px;" @click="addGoodsBomCode">新增</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import noheaderContainer from '@/components/my-container/noheader';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import { getGoodsBomPageAsync, getGoodsBomDetailAsync, importGoodsBomDataAsync, addGoodsBomCodeAsync } from "@/api/inventory/goodsBom";

const tableCols = [
    { width: 'auto', align: 'center', prop: 'picture', label: '图片', type: "images", },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'styleCode', label: '款式编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'bomCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'propertiesValue', label: '颜色及规格', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'bomName', label: '商品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'boms', label: '主料', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'masterStock', label: '主仓库存', },
    { width: 'auto', align: 'center', prop: 'assembleStock', label: '可组装库存', },
]

const tableDtlCols = [  
    { width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', },
    { width: '50', align: 'center', prop: 'quantity', label: '数量', },
    { width: 'auto', align: 'center', prop: 'sellStock', label: '可用库存', }
]

export default{
    name: "goodsBomDataList",
    components: {
        MyContainer, vxetablebase, inputYunhan, noheaderContainer
    },
    data() {
        return {
            adddialogVisible:false,
            fileList: [],
            fileparm: {},
            uploadLoading: false,
            dialogVisible: false,
            that: this,
            tableCols,
            tableDtlCols,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                styleCode: null,//款式编码
                goodsCode: null,//商品编码
                goodsName: null //商品名称
            },
            listLoading: false,
            detailLoading: false,
            total: 0,
            summaryarry: {},
            list: [],
            detail: [],
            pager: { OrderBy: "modifiedTime", IsAsc: true },
            addGoodsCode:null
        }
    },
    async mounted() {
        await this.getlist()
    },
    methods: {
        onShowAddCodeDialog(){
            this.adddialogVisible = true;
        },
        async addGoodsBomCode(){
            var params = {};
            params.goodsCode = this.addGoodsCode.replace(/\n/g,",");
            
            this.listLoading = true;
            var res = await addGoodsBomCodeAsync(params);
            this.listLoading = false;
            if (!res?.success) {
                return;
            } else {
                this.adddialogVisible = false;
                this.$message.success(res.msg ? res.msg: "编码新增成功！正在同步聚水潭Bom数据...");
                this.addGoodsCode = null;
            }

        },
        //上传文件
        onUploadRemove(file, fileList) {
            this.fileList = []
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            this.dialogVisible = false;
        }, 
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            var res = await importGoodsBomDataAsync(form);
            if (res?.success)
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
            this.uploadLoading = false
            this.dialogVisible = false;
            await this.getList()
        },
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        //导入弹窗
        startImport() {
            this.fileList = []
            this.dialogVisible = true;
        },
        callbackGoodsCode(val) {
            this.ListInfo.goodsCode = val;
        },
        //分页查询
        async getlist() {
            this.listLoading = true;
            var res = await getGoodsBomPageAsync(this.ListInfo);
            this.listLoading = false;
            if (!res?.success) {
                this.list = [];
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            this.list = data;
        },
        //排序查询
        async sortchange(column) {
            console.log(column)
            if (!column.order) {
                this.ListInfo.orderBy = null;
                this.ListInfo.isAsc = false;
            }
            else {
                this.ListInfo.orderBy = column.prop;
                this.ListInfo.isAsc = column.order.indexOf("descending") == -1 ? true : false;
            }
            await this.onSearch();
        },
        //查询第一页
        async onSearch() {
            this.ListInfo.currentPage = 1;
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getlist()
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getlist()
        },
        //主列表点击行
        cellClick({row, column, cell, event}) {
            this.onSearchDetail(row.bomCode);
        },
        
        async onSearchDetail(bomCode) {
            await this.getDetailList(bomCode);
        },
        //获取Bom明细
        async getDetailList(bomCode) {
            this.detailLoading = true;
            const params = {
                bomCode: bomCode
            }

            var res = await getGoodsBomDetailAsync(params);
            this.detailLoading = false;
            if (!res?.success) {
                this.detail = [];
                return
            }
            const data = res.data;
            this.detail = data?data:[];
        },
        //下载导入模版
        downloadTemplate(){
            window.open("../../../static/excel/inventory/大马美甲商品导入模版.xlsx","_self");
        }
    }
}

</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 5px;
  }
}
</style>