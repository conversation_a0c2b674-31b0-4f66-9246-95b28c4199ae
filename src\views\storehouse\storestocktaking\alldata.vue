<template>
  <container>
    <template #header>
      <div style="display:flex;flex-direction: column;">
        <el-button-group>
          <el-button style="margin-top:0px">
            <el-date-picker style="width: 410px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" :clearable="false" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" :picker-options="pickerOptions"></el-date-picker>

            <el-input style="margin-left: 5px;width: 150px;" v-model="filter.io_id" v-model.trim="filter.io_id"
              :maxlength=10 placeholder="盘点单号" @keyup.enter.native="onSearch" clearable />

            <el-select style="margin-left: 5px;width:150px" v-model="filter.tbWareHouse" clearable :collapse-tags="true"
              placeholder="请选择仓库" filterable>
              <el-option label="全仓" :value="0" />
              <el-option v-for="item in storagelist" :key="item.id" :label="item.name" :value="item.wms_co_id" />
            </el-select>


            <el-input style="margin-left: 5px;width: 150px;" v-model="filter.createdUserName"
              v-model.trim="filter.createdUserName" :maxlength=100 placeholder="创建人名" @keyup.enter.native="onSearch"
              clearable />
            <el-input style="margin-left: 5px;width: 150px;" v-model="filter.goodsCode" v-model.trim="filter.goodsCode"
              :maxlength=100 placeholder="商品编码" @keyup.enter.native="onSearch" clearable />
            <el-select filterable v-model="filter.pType" placeholder="类型" clearable
              style="width: 130px;margin-left: 5px;">
              <el-option label="全部" value></el-option>
              <el-option label="盘亏" value='1'></el-option>
              <el-option label="盘盈" value="2"></el-option>
            </el-select>

            <el-select filterable v-model="filter.brandIds" clearable placeholder="品牌" style="width: 150px;margin-left: 5px;" multiple :collapse-tags="true">
              <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>

            <el-select v-model="filter.isRemark" clearable placeholder="是否已备注" style="width: 150px;margin-left: 5px;" :collapse-tags="true">
              <el-option label="是" value='1'></el-option>
              <el-option label="否" value="2"></el-option>
            </el-select>

            <el-button type="primary" @click="onSearch()" style="margin-left: 10px;">查询</el-button>
            <el-button type="primary" @click="onstartExport()">导出</el-button>
            <p></p>
            <el-checkbox-group v-model="filter.checkList" @change="changeCheck" style="float:left;margin-top:1px;">
              <el-checkbox :label="1">按仓</el-checkbox>
              <el-checkbox :label="2">按编码</el-checkbox>
              <el-checkbox :label="3">按天</el-checkbox>
              <el-checkbox :label="4">按人</el-checkbox>
            </el-checkbox-group>
          </el-button>
        </el-button-group>
      </div>
    </template>
    <template>
      <ces-table v-if="showtable" :tablekey="'StoreTocktaking202305091124'" ref="TableCols" :hasexpandRight='true' :showsummary='true'
        :summaryarry='proSummaryarry' :that='that' :isIndex='true' :tablefixed='true' :hasexpand='false'
        :isSelectColumn="false" :tableData='proTableList' :tableCols='TableCols' :tableHandles='tableHandles'
        @sortchange="sortchange" :loading="listLoading"   :border="true">

        <template v-if="filter.checkList.length == 0" slot="right">
          <el-table-column width="140" label="操作" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" @click="showLog(scope.row)">操作日志</el-button>
              <el-button v-if="scope.row.approveStatus==2" type="text"  @click="showApproveLog(scope.row)">审批明细</el-button>
            </template>
          </el-table-column>
        </template>
      </ces-table>
    </template>

    <el-dialog :title="logDialog.title" :visible.sync="logDialog.visible" width="80%" v-dialogDrag>
      <span>
        <operatelog ref="operatelog" :filter="logDialog.filter"></operatelog>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="logDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="approveLogDialog.title" :visible.sync="approveLogDialog.visible" width="50%" v-dialogDrag>
      <span>
        <approvelog ref="operatelog" :data="approveLogDialog.data"></approvelog>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="approveLogDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="导入数据" :visible.sync="importDialog.visible" width="30%" v-dialogDrag>
      <span>
        <el-row>
          <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
            <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action accept=".xlsx"
              :file-list="fileList" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
              <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
            </el-upload>
          </el-col>
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>

    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>
  </container>
</template>
<script>
import { pageStoreStockTaking, pageStockTakingGroup, importStoreStockTakingLog, exportStoreStockTakingLog, getStoreStockTakingApproveLog} from '@/api/inventory/storestocktaking'
import { getAllWarehouse,getAllProBrand } from '@/api/inventory/warehouse'
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import { Loading } from 'element-ui';
import operatelog from "./operatelog.vue";
import approvelog from "./approvelog.vue"
import { formatTime } from "@/utils";
let loading;
const startLoading = () => {
  loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
  });
};

const tableCols1 = [
  { istrue: true, prop: 'io_id', sortable: 'custom', label: '盘点单号', width: '100' },
  { istrue: true, prop: 'warehouse', sortable: 'custom', label: '仓库', width: '100' },
  { istrue: true, prop: 'goodscode', label: '商品编码', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'name', label: '商品名称', width: '230', },
  { istrue: true, prop: 'brandId', label: '品牌', sortable: 'custom' ,width: '60',formatter: (row) => row.brandName },
  { istrue: true, prop: 'qty', label: '盘点数量', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'amont', label: '盘点损益', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'r_qty', label: '盘点后数量', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'ptype', sortable: 'custom',label: '类型', width: '60', },
  { istrue: true, prop: 'computeLoseStatus', label: '盘亏计算状态', width: '100', sortable: 'custom', formatter: (row) => { return row.computeLoseStatus == false ? '未计算' : '已计算' } },
  { istrue: true, prop: 'status', label: '状态', width: '70', sortable: 'custom', formatter: (row) => { return row.status == 'WaitConfirm' ? '待确认' : row.status == 'Confirmed' ? '生效' : row.status == 'Archive' ? '归档' : row.status == 'Cancelled' ? '取消' : '作废' } },
  { istrue: true, prop: 'io_date', label: '单据日期', width: '120', sortable: 'custom', formatter: (row) => formatTime(row.io_date, 'YYYY-MM-DD HH:mm:ss') },
  { istrue: true, prop: 'creator_name', label: '创建人', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'isneedRemark',sortable: 'custom', label: '是否需备注', width: '60', formatter: (row) => {
    return ((row.io_date<='2023-07-19 00:00:00'  && (Math.abs(row.qty) > 20 || Math.abs(row.amont) > 100)) || (row.io_date>'2023-07-19 00:00:00'  && row.qty<-1)
   )? "是" : "否" }
  },
  // { istrue: true, prop: 'remark',sortable: 'custom', label: '聚水潭备注', width: '100' },
  // { istrue: true, prop: 'laseRemarkUserName',sortable: 'custom', label: '聚水潭备注人', width: '80' },
  // { istrue: true, prop: 'laseRemarkTime',sortable: 'custom', label: '聚水潭备注时间', width: '120', formatter: (row) => row.laseRemarkTime == null ? "" : formatTime(row.laseRemarkTime, 'YYYY-MM-DD HH:mm:ss') },
  { istrue: true, prop: 'approveRemark',sortable: 'custom', label: '钉钉备注', width: '80' },
  { istrue: true, prop: 'pics',type:'images', label: '钉钉图片', width: '80' },
];

const tableHandles = [
];

export default {
  name: "Pddsafeinventory",
  components: { container, cesTable, operatelog, approvelog},
  data() {
    return {
      that: this,
      filter: {
        io_id: null,
        goodsCode: null,
        startTime: null,
        endTime: null,
        timerange: null,
        checkList: [],
        createdUserName: null,
        tbWareHouse: null,
        brandIds:null,
        isRemark:null
      },
      storagelist: [],
      brandlist: [],
      TableCols: tableCols1,
      tableHandles: tableHandles,
      total: 0,
      proPager: { OrderBy: null, IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      proTableList: [],
      proSummaryarry: {},
      selids: [],
      pickerOptions: {
        shortcuts: [{
          text: '前一天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
            window.setshowprogress(false);
          }
        }, {
          text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            end.setTime(end.getTime());
            picker.$emit('pick', [start, end]);
            window.setshowprogress(false);
          }
        }, {
          text: '近一个月',
          onClick(picker) {
            const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
            const date2 = new Date(); date2.setDate(date2.getDate());
            picker.$emit('pick', [date1, date2]);
            window.setshowprogress(false);
          }
        }]
      },
      logDialog: {
        title: "操作日志",
        visible: false,
        filter: {
          io_id: 0
        }
      },
      approveLogDialog: {
        title: "审批明细",
        visible: false,
        data: null
      },
      importDialog: {
        visible: false,
      },
      fileList: [],
      uploadLoading: false,
      showtable:true
    };
  },
  async mounted() {
    let end = new Date();
    let start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
    end.setTime(end.getTime());
    this.filter.timerange = [start, end];
    this.onSearch();
  },
  async created() {
    await this.getstorage();
  },
  methods: {
    async changeCheck() {
      this.showtable=false;
      if (this.filter.checkList.length > 0) {
        let tableColsGroups = [
          { istrue: true, prop: 'qty', label: '盘点数量',  sortable: 'custom' },
          { istrue: true, prop: 'amont', label: '盘点损益',  sortable: 'custom' },
          { istrue: true, prop: 'takingCount', sortable: 'custom', label: '次数', width: 'auto' },
        ];
        if (this.filter.checkList.includes(4)) {
          tableColsGroups.unshift({ istrue: true, prop: 'creator_name', label: '创建人', sortable: 'custom' });
        }
        if (this.filter.checkList.includes(3)) {
          tableColsGroups.unshift({ istrue: true, prop: 'io_date_Show', label: '单据日期', sortable: 'custom' });
        }
        if (this.filter.checkList.includes(2)) {
          tableColsGroups.unshift({ istrue: true, prop: 'brandId', label: '品牌',  sortable: 'custom',formatter: (row) => row.brandName  });
          tableColsGroups.unshift({ istrue: true, prop: 'name', label: '商品名称'});
          tableColsGroups.unshift({ istrue: true, prop: 'goodscode', label: '商品编码',  sortable: 'custom' });
        }
        if (this.filter.checkList.includes(1)) {
          tableColsGroups.unshift({ istrue: true, prop: 'warehouse', sortable: 'custom', label: '仓库' });
        }
        this.TableCols = tableColsGroups;
      } else {
        this.TableCols = tableCols1;
      }
      this.proPager = {
        OrderBy: null,
        IsAsc: false,
      };
      this.$refs.TableCols.clearSort();
      this.$refs.pager.setPage(1);
      this.getList();
      this.showtable=true;
    },
    async sortchange(column) {
      if (!column.order) this.proPager = {};
      else
        this.proPager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      await this.onSearch();
    },
    async onSearch() {
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      let pager = this.$refs.pager.getPager()
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      this.listLoading = true;
      let params = { ...pager, ...this.proPager, ... this.filter }
      if (this.filter.checkList.length == 0) {
        let res = await pageStoreStockTaking(params);
        this.listLoading = false;
        this.proTableList = res.data?.list;
        this.total = res.data?.total;
        this.proSummaryarry = res.data?.summary;
      } else {
        let res = await pageStockTakingGroup(params);
        this.listLoading = false;
        this.proTableList = res.data?.list;
        this.total = res.data?.total;
        this.proSummaryarry = res.data?.summary;
      }
    },
    async showLog(row) {
      this.logDialog.filter.io_id = row.io_id;
      this.logDialog.visible = true;
      this.$nextTick(function () {
        this.$refs.operatelog.onSearch();
      })
    },
    async getstorage() {
      let param = { isContainsUnEnable: true };
      let res4 = await getAllProBrand(param);
      this.brandlist = res4.data.map(item => {
        return { value: item.key, label: item.value };
      });

      const res = await getAllWarehouse();
      if (!res?.success) {
        return
      }
      this.storagelist = res.data;
    },
    async onstartImport() {
      this.importDialog.visible = true;
      this.fileList = []
    },
    async onstartExport() {
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };
      var res = await exportStoreStockTakingLog(params);
      if (!res?.data) {
        return
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '仓库盘点数据' + new Date().toLocaleString() + '_.xlsx')
      aLink.click()
    },
    submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      this.$refs.upload.submit();
    },
    async uploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.uploadLoading = false;
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      let res = await importStoreStockTakingLog(form);
      if (res.code == 1) {
        this.importDialog.visible = false
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      }
      this.fileList = []
      this.uploadLoading = false
    },
    async uploadChange(file, fileList) {
      let files = [];
      files.push(file);
      this.fileList = files;
    },
    async uploadRemove(file, fileList) {
      let files = [];
      this.fileList = files;
    },
    async showApproveLog(row) {
      this.approveLogDialog.data={};
      let param = { io_id: row.io_id, goodsCode: row.goodscode }
      let res = await getStoreStockTakingApproveLog(param)
      if (!res?.success) {
        return;
      }
      this.approveLogDialog.data=res.data;
      this.$nextTick(function () {
          this.approveLogDialog.visible = true;
      })
    },
  }
}

</script>
<style></style>
