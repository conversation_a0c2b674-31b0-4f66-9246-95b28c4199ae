<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true">
        <el-form-item>
          <el-tooltip class="item" effect="dark" content="默认为精确匹配在开始或者结尾输入*进行模糊匹配" placement="bottom">
            <inputYunhan :key="'3'" :keys="'three'" :width="'170px'" ref="childGoodsCode" v-model="filter.goodsCode"
              :inputt.sync="filter.goodsCode" placeholder="商品编码" :clearable="true"
              @callback="(val) => filter.goodsCode = val" title="商品编码"></inputYunhan>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-select v-model="filter.groupId" style="width: 130px" placeholder="运营组" :clearable="true"
            :collapse-tags="true" filterable>
            <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="filter.brandId" clearable filterable placeholder="采购员" style="width: 130px"
            :collapse-tags="true">
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="filter.isEnabled" style="width: 130px" placeholder="商品状态" :clearable="true">
            <el-option label="备用" :value="0" />
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="-1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="filter.isQualificationType" style="width: 120px" placeholder="资质证书" :clearable="true">
            <el-option label="已上传" :value="true" />
            <el-option label="未上传" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">筛选</el-button>
        </el-form-item>
      </el-form>
    </template>
    <!--列表----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
    <template>
      <ces-table :id="'productDatabase_index202408041601_1'" ref="table" :that='that' :isIndex='true' :hasexpand='false'
        @sortchange='sortchange' @cellClick='cellclick' :tableData='datalist' :tableCols='tableCols'
        :selectColumnHeight="'0px'" :loading="listLoading" :isBorder="false"
        :treeProp="{ rowField: 'id', parentField: 'parentId' }" :hasexpandRight="true"
        :editconfig="{ trigger: 'click', mode: 'cell', showIcon: false }">
        <template #materialImgs="{ row }">
          <el-image v-if="(row.materialImgs != null) && row.materialImgs.length > 0 && (row.parentId != 0)"
            :src="row.materialImgs[0]" :preview-src-list="row.materialImgs" style="width: 100px; height: 50px;" />
        </template>
      </ces-table>
    </template>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getDataList" />
    </template>
    <!--视频播放-->
    <el-dialog title="视频播放" :visible.sync="videoDialogVisible" width="50%" @close="closeVideoPlyer"
      :append-to-body="true" v-dialogDrag>
      <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeVideoPlyer">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>
import inputYunhan from "@/components/Comm/inputYunhan";
import dayjs from "dayjs";
import cesTable from "@/components/VxeTable/vxetablebase.vue";
import cesTable1 from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import { pageGoodsDocRecordCgList } from "@/api/inventory/basicgoods"
import { getGroupKeyValue } from "@/api/operatemanage/base/product";
import { getAllProBrand, getAllWarehouse } from '@/api/inventory/warehouse'
import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
import YhImgUpload3 from "@/components/upload/yh-img-upload3.vue";
import viodeUpload from "@/views/media/shooting/uploadfile1.vue";
import buschar from '@/components/Bus/buschar'
const tableCols = [
  { istrue: true, prop: 'goodsCode', align: 'left', label: '商品编码', treeNode: true, width: '130', fixed: 'left', },
  { istrue: true, prop: 'goodsName', align: 'left', label: '商品名称', width: '100', },
  { istrue: true, prop: 'materialImgs', align: 'left', label: '包装图片', width: '100' },
  { istrue: true, prop: 'pictureUrl', align: 'left', label: '商品图片', type: "images", width: '100', },
  { istrue: true, prop: 'externalPacking', align: 'left', label: '外包装图片', type: "images", width: '120', },
  { istrue: true, prop: 'goodFrontBackImgs', align: 'left', label: '实物图片', type: "images", width: '120', },
  { istrue: true, prop: 'clWeight', align: 'left', label: '测量重', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'clWeightImgs', align: 'left', label: '测重图', width: '70', type: "images", },
  { istrue: true, prop: 'clLength', align: 'left', label: '测量长', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'clLengthImgs', align: 'left', label: '测长图', width: '70', type: "images", },
  { istrue: true, prop: 'clWidth', align: 'left', label: '测量宽', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'clWidthImgs', align: 'left', label: '测宽图', width: '70', type: "images", },
  { istrue: true, prop: 'clHeight', align: 'left', label: '测量高', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'clHeightImgs', align: 'left', label: '测高图', width: '70', type: "images", },
  {
    istrue: true, prop: 'goodsVedio', align: 'left', label: '商品视频', type: 'html', width: '70',
    formatter: (row) => { return row.goodsVedio != null && row.goodsVedio.length > 0 ? '<i class="el-icon-video-play"></i>' : '' },
  },
  { istrue: true, prop: 'qualificationType', label: '资质证书', width: '80', type: 'files' },
  { istrue: true, prop: 'qualifiedImgs', label: '合格证', width: '80', type: 'files' },
  { istrue: true, prop: 'mediaTime', align: 'left', label: '已拍创建时间', sortable: 'custom', width: '140' },
  { istrue: true, prop: 'isEnabled', align: 'left', label: '商品状态', sortable: 'custom', width: '100', formatter: (row) => row.parentId == 0 ? '' : row.isEnabled == 0 ? '备用' : row.isEnabled == 1 ? '启用' : '禁用', },
  { istrue: true, prop: 'groupName', align: 'left', label: '运营组', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'brandName', align: 'left', label: '采购员', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'brandDeptName', align: 'left', label: '采购组', width: '100' },
  { istrue: true, prop: 'execStandard', align: 'left', label: '执行标准', width: '100' },
  { istrue: true, prop: 'material', align: 'left', label: '材质', width: '100' },
  { istrue: true, prop: 'isPatent', align: 'left', label: '是否专利', width: '100', formatter: (row) => row.isPatent == true ? '是' : row.isPatent == false ? '否' : '' },
];
export default {
  name: "databaseindex",//商品资料库
  components: {
    MyContainer, cesTable, videoplayer, YhImgUpload, YhImgUpload3, viodeUpload, buschar, inputYunhan, cesTable1
  },
  data() {
    return {
      videoDialogVisible: false,
      videoplayerReload: false,
      videoUrl: null,//视频地址
      logList: [],
      filter: {
        goodsCode: '',
        groupId: null,
        brandId: null,
        isEnabled: null,
        daterange: [],
        daterange1: [],
        createdTimeStart: '',
        createdTimeEnd: '',
        hasVedioOrImg: true,
        startMediaTime: null,
        endMediaTime: null,
      },
      summaryarry: {},
      datalist: [],
      total: 0,
      sels: [], // 列表选中列
      listLoading: false,
      that: this,
      pageLoading: false,
      pager: {},
      tableCols: tableCols,
      groupList: [],
      brandlist: [],
    };
  },
  async mounted() {
    this.init();
    this.onSearch();
  },
  methods: {
    // 显示视频播放弹窗
    playVideo(videoUrl) {
      this.videoplayerReload = false;
      this.videoplayerReload = true;
      this.videoDialogVisible = true;
      this.videoUrl = videoUrl;
    },
    // 关闭视频弹窗
    async closeVideoPlyer() {
      this.videoDialogVisible = false;
      this.videoplayerReload = false;
    },
    //设置运营组、采购组下拉
    async init() {
      const res = await getGroupKeyValue({});
      this.groupList = res.data;
      var res2 = await getAllProBrand();
      //取消系统挂靠
      this.brandlist = res2.data
        .filter(item => item.value !== '☆系统挂靠' && item.value !== '☆耗材包材')
        .map(item => {
          return { value: item.key, label: item.value };
        });
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getDataList();
    },
    //获取数据
    async getDataList() {
      if (this.filter.daterange) {
        this.filter.createdTimeStart = this.filter.daterange[0];
        this.filter.createdTimeEnd = this.filter.daterange[1];
      } else {
        this.filter.createdTimeStart = null;
        this.filter.createdTimeEnd = null;
      }
      if (this.filter.daterange1) {
        this.filter.startMediaTime = this.filter.daterange1[0];
        this.filter.endMediaTime = this.filter.daterange1[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...this.filter
      };
      this.listLoading = true;
      const res = await pageGoodsDocRecordCgList(params);
      this.listLoading = false;
      this.total = res.data.total
      this.datalist = res.data.list;
      this.datalist.forEach(item => {
        if (item.materialImgs) {
          item.materialImgs = JSON.parse(item.materialImgs);
          const urlsArray = item.materialImgs.map(imgObject => imgObject.url);
          item.materialImgs = urlsArray;
        }
      });
      this.summaryarry = res.data.summary;
    },
    cellclick(row) {
      if (row.column.property == 'goodsVedio' && row.row.goodsVedio) {
        //弹出视频播放
        this.playVideo(row.row.goodsVedio)
      }
    },

    //列表排序
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
  },
};
</script>
<style lang="scss" scoped>
.el-form-item__content {
  .append_unit {
    position: relative;

    &::after {
      content: attr(data-unit);
      position: absolute;
      top: 0;
      right: 0;
      padding: 0 10px;
      color: #909399;
      background-color: #f5f7fa;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
    }
  }
}

::v-deep .vxe-table--tooltip-wrapper.theme--dark {
  z-index: 2023 !important;
}

::v-deep .el-link.el-link--primary {
  margin-left: 5px;
}
</style>
