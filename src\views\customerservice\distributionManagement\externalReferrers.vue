<template>
    <MyContainer>
        <div class="top">
            <el-select v-model="getListInfo.userName" filterable placeholder="外部推荐人姓名" style="width:150px;margin-right:10px"
                clearable>
                <el-option v-for="item in options" :key="item.id" :label="item.userName" :value="item.userName">
                </el-option>
            </el-select>
            <el-input placeholder="联系电话" maxlength="11" v-model="getListInfo.phone" style="width:150px;margin-right:10px"
                clearable></el-input>
            <el-button type="primary" @click="getOutList">搜索</el-button>
            <el-button type="primary" @click="addReferrers">新增</el-button>
        </div>
        <cesTable ref="detailTable" :table-data="tableData" :table-cols="tableCols" :is-index="true" :that="that"
            style="width: 100%; height: 80%; margin: 0" @sortchange='sortchange'></cesTable>

        <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="total" @get-page="getOutList"
            @page-change="Dispagechange" @size-change="Dissizechange" class="pageBox" />

        <el-dialog :title="idEdit ? '编辑' : '添加外部推荐人'" :visible.sync="addReferrerLog" width="30%" @close="handleClose"  v-dialogDrag>
            <el-form :model="addReferrerForm" :rules="rules" ref="ruleForm" label-width="140px">
                <el-form-item label="外部推荐人姓名" prop="name">
                    <el-input v-model="addReferrerForm.userName" placeholder="请输入外部推荐人姓名" style="width:220px"
                        maxlength="50" clearable></el-input>
                </el-form-item>
                <el-form-item label="联系电话" prop="phoneNumber">
                    <el-input v-model="addReferrerForm.phone" placeholder="请输入联系电话" style="width:220px"
                        maxlength="11" clearable></el-input>
                </el-form-item>
                <el-form-item label="备注" prop="remake">
                    <el-input v-model="addReferrerForm.remark" placeholder="请输入备注" style="width:220px;"  maxlength="200" clearable class="ipt"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleClose">取消</el-button>
                    <el-button type="primary" @click="submit">确定</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>

        <el-dialog title="日志" :visible.sync="logValue" width="50%" :before-close="handleClose" v-loading="logValueLoading"
            v-dialogDrag @close="handleClose">
            <div style="display: flex;" class="publicMargin">
                <el-date-picker v-model="dailyPickValue" type="datetimerange" :picker-options="pickerOptions"
                    range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right"
                    style="width: 300px;margin-right: 10px;" @change="pickDate" format="yyyy-MM-dd">
                </el-date-picker>
                <el-input v-model="logList.operoperatorName" placeholder="请输入操作人" style="width: 200px;margin-right: 10px;"
                    clearable maxlength="200" />
                <el-button type="primary" @click="publicLog">搜索</el-button>
            </div>
            <cesTable ref="detailTable" :table-data="logTableData" :table-cols="tableCols1" :is-index="true" :that="that"
                style="width: 100%; height: 400px; margin: 10px 0 50px" @sortchange='sortchange1'>
            </cesTable>
            <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="logTotal" @get-page="publicLog"
                @page-change="logPagechange" @size-change="logSizechange" />
        </el-dialog>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import cesTable from "@/components/Table/table.vue";
import dayjs from 'dayjs'
import { getOutSiders, queryAllDistributorOutSiders, addOrUpdateOutSiders, getOutSidersLog } from '@/api/customerservice/Distributor.js'
const tableCols = [
    { istrue: true, prop: 'userName', label: '外部推荐人姓名', sortable: 'custom' },
    { istrue: true, prop: 'phone', label: '联系电话', sortable: 'custom' },
    { istrue: true, prop: 'remark', label: '备注', sortable: 'custom' },
    {
        istrue: true, type: 'button', label: '操作', width: 200, btnList: [
            { label: '编辑', handle: (that, row) => that.editOut(row) },
            { label: '日志', handle: (that, row) => that.openLog(row) },
        ]
    }
]
const tableCols1 = [
    { istrue: true, prop: 'oldValue', label: '操作前值', sortable: 'custom' },
    { istrue: true, prop: 'newValue', label: '操作后值', sortable: 'custom' },
    { istrue: true, prop: 'addedDate', label: '操作日期', sortable: 'custom' },
    { istrue: true, prop: 'addedBy', label: '操作人', sortable: 'custom' },
]
export default {
    name: "externalReferrers",
    components: {
        MyContainer,
        cesTable
    },
    data () {
        return {
            logValueLoading: true,
            tableCols1: tableCols1,
            dailyPickValue: [],
            logValue: false,
            total: null,
            logTotal: null,
            idEdit: false,
            getListInfo: {
                userName: null,
                phone: null,
                orderBy: null,
                CurrentPage: 1,
                PageSize: 50,
                IsAsc: false,
            },
            addReferrerForm: {
                id: null,
                userName: null,
                phone: null,
                remark: null
            },
            editReferrerForm: {
                id: null,
                userName: null,
                phone: null,
                remark: null
            },
            rules: {
                name: [
                { required: true, message: '请输入外部推荐人姓名', trigger: 'blur change' }
                ]
            },
            title: null,
            addReferrerLog: false,
            tableCols: tableCols,
            that: this,
            tableData: [],
            logTableData: [],
            phoneNumber: '',
            options: [],
            value: '',
            logList: {
                operoperatorName:null,
                userId: null,
                CurrentPage: 1,
                PageSize: 50,
                startDate: null,
                endDate: null,
                OrderBy: null,
                IsAsc: false,
            },
            pickerOptions: {
                shortcuts: [
                    {
                        text: '近一周',
                        onClick (picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    },
                    {
                        text: '近一个月',
                        onClick (picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    },
                    {
                        text: '近三个月',
                        onClick (picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        },

                    }

                ],
                // 自定义日期禁用函数
                disabledDate (date) {
                    // 获取当前日期
                    const currentDate = new Date();
                    // 如果选中日期在当前日期之后，则禁用它
                    return date > currentDate;
                }
            },
        };
    },

    mounted () {
        this.getOutList()
        this.getOutSelectInfo()
    },
    methods: {
        searchLog () {

        },
        pickDate () {
            console.log(this.dailyPickValue, 'this.dailyPickValue');
            //使用dayjs将this.dailyPickValue[0],this.dailyPickValue[1]转换为YYYY-MM-DD格式
            if (this.dailyPickValue == null || this.dailyPickValue == undefined || this.dailyPickValue == '') {
                //将时间重置为null
                this.logList.startDate = null
                this.logList.endDate = null
                //将页码重置为1
                this.logList.CurrentPage = 1;
                //先清空数据
                this.publicLog()
            } else {
                this.logList.startDate = dayjs(this.dailyPickValue[0]).format('YYYY-MM-DD')
                this.logList.endDate = dayjs(this.dailyPickValue[1]).format('YYYY-MM-DD')
                this.publicLog()
            }
        },
        logSizechange (val) {
            this.logList.CurrentPage = 1;
            this.logList.PageSize = val;
            this.publicLog();
        },
        logPagechange (val) {
            this.logList.CurrentPage = val;
            this.publicLog();
        },
        //当前页改变
        Dissizechange (val) {
            this.getListInfo.CurrentPage = 1;
            this.getListInfo.PageSize = val;
            this.getOutList();
        },
        //页码改变
        Dispagechange (val) {
            this.getListInfo.CurrentPage = val;
            this.getOutList();
        },
        //抽离日志查询
        async publicLog () {
            //清除logList.operoperatorName的空格
            if (this.logList.operoperatorName) {
                this.logList.operoperatorName = this.logList.operoperatorName.replace(/\s+/g, "")
            }
            this.logTableData = []
            const { data } = await getOutSidersLog(this.logList)
            if (data) {
                this.logTableData = data.list
                this.logTotal = data.total
                this.logValue = true
                this.logValueLoading = false
            }
        },
        async openLog (row) {
            this.logList.userId = row.id
            this.publicLog()
        },
        async editOut (row) {
            this.isEdit = true
            this.clear()
            this.addReferrerForm = JSON.parse(JSON.stringify(row))
            this.addReferrerLog = true
        },
        //添加推荐人确定
        async submit () {
            const { success } = await addOrUpdateOutSiders(this.addReferrerForm)
            if (success) {
                //清除logList.operoperatorName的空格，使用replace方法
                this.getOutList()
                this.handleClose()
                this.getOutSelectInfo()
                if (this.isEdit) {
                    this.$message.success('编辑成功')
                } else {
                    this.$message.success('添加成功')
                }
            } else {
                if (this.title == '编辑') {
                    this.$message.error('编辑失败')
                } else {
                    this.$message.error('请输入必填项')
                }
            }
        },
        handleClose () {
            this.addReferrerLog = false
            this.logValue = false
            this.clear()
        },
        //获取外部推荐人下拉数据
        async getOutSelectInfo () {
            const { data, success } = await queryAllDistributorOutSiders()
            if (success) {
                this.options = data
            } else {
                this.$message.error('获取外部推荐人下拉数据失败')
            }
        },
        //分页查询外部推荐人列表
        async getOutList () {
            if (this.getListInfo.phone) {
                this.getListInfo.phone = this.getListInfo.phone.replace(/\s+/g, "")
            }
            const { data, success } = await getOutSiders(this.getListInfo)
            if (success) {
                this.tableData = data.list
                console.log(data, 'data');
                this.total = data.total
            } else {
                this.$message.error('获取外部推荐人列表失败')
            }
        },
        clear () {
            this.addReferrerForm = {
                id: null,
                name: null,
                phoneNumber: null,
                remake: null
            }
        },
        addReferrers () {
            this.idEdit = false
            this.clear()
            this.addReferrerLog = true
        },
        sortchange (column) {
            console.log(column, 'column');
            if (column.order) {
                var orderField = column.prop;
                this.getListInfo.OrderBy = orderField;
                this.getListInfo.IsAsc = column.order.indexOf("descending") == -1 ? true : false
                this.getOutList()
            }
        },
        sortchange1 (column) {
            console.log(column, 'column');
            if (column.order) {
                var orderField = column.prop;
                this.logList.OrderBy = orderField;
                this.logList.IsAsc = column.order.indexOf("descending") == -1 ? true : false
                this.publicLog()
            }
        },
    },
};
</script>

<style scoped lang="scss">
.top {
    display: flex;
    align-items: center;
    height: 60px;
    background-color: #fff;
    border-bottom: 1px solid #ebeef5;
}

.pageBox {
    width: 88%;
    position: fixed;
    bottom: 20px;
    z-index: 999;
}

// .ipt ::v-deep .el-input__inner{
//     height: 40px !important;
// }
</style>
