<template>
    <my-container v-loading="pageLoading">
        <vxetablebase ref="table" :that='that' :id="'newStryleCodeReports_20240810165419'" :isIndex='true'
            :hasexpand='true' :tablefixed='true' :showsummary='true' :summaryarry='summaryarry' @sortchange='sortchange'
            :tableData='list' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            style="width: 100%;  margin: 0" :loading="listLoading" :height="'100%'" />
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </my-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatmoney } from "@/utils/tools";
import { batchAddOrUpdatePack, getProductPackCostBy, getListByStyleCodeCost } from '@/api/operatemanage/base/product'
import { pageStyleCodeGoodsDtlRptListAsync } from '@/api/bookkeeper/styleCodeRptData'
const tableCols = [
    { istrue: true, prop: 'styleCode', label: '系列编码', width: 'auto',  },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'goodsImage', label: '图片', width: 'auto', sortable: 'custom', type: 'images' },
    { istrue: true, prop: 'createdTime', label: '创建时间', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'costPrice', label: '成本', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'kczj', label: '库存资金', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'orderCount', label: '订单数', width: 'auto', },
    { istrue: true, prop: 'wcOrderRate', label: '外仓率', width: 'auto' },
    // { istrue: true, prop: 'masterStock', label: '库存', width: 'auto' },
    // { istrue: true, prop: 'threeDayZZRate', label: '3天周转天数', width: 'auto', sortable: 'custom' },
];
export default {
    name: 'Roles',
    components: { cesTable, MyContainer, MyConfirmButton, vxetablebase },
    data() {
        return {
            that: this,
            list: [],
            summaryarry: {},
            tableCols: tableCols,
            total: 0,
            listLoading: false,
            pageLoading: false,
            editparmVisible: false,
            editparmLoading: false,
            visible: false,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
        }
    },
    props: {
        filter: {
            seriesCode: null,
            goodsCode: null,
            // days:null
        },
    },
    async mounted() {
        this.ListInfo = { ...this.ListInfo, ...this.filter }
        await this.getList();
    },
    methods: {
        //分页查询
        async getList(type) {
            if (type == 'search') {
                this.ListInfo = { ...this.ListInfo, ...this.filter }
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.listLoading = true;
            const { data, success } = await pageStyleCodeGoodsDtlRptListAsync(this.ListInfo);
            this.listLoading = false
            if (!success) return
            this.total = data.total;
            this.summaryarry = data.summary;
            this.list = data.list;
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    },
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary {
    margin-right: 7px;
}

::v-deep .el-table__fixed {
    pointer-events: auto;
}
</style>