<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="活动提交日期" end-placeholder="活动结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>

        <el-select clearable multiple filterable collapse-tags v-model="ListInfo.shopCodes" placeholder="店铺"
          style="width: 180px">
          <el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>

        <div style="margin-left: 10px;">
          <inputYunhan :key="'1'" :keys="'one'" :width="'150px'" ref="" :inputt.sync="ListInfo.sKCID"
            v-model="ListInfo.sKCID" placeholder="SKCID/若输入多条请按回车" :clearable="true" @callback="callbackSKCIDList"
            title="SKC ID">
          </inputYunhan>
        </div>

        <div style="margin-left: 10px;">
          <inputYunhan :key="'2'" :keys="'two'" :width="'150px'" ref="" :inputt.sync="ListInfo.sKUID"
            v-model="ListInfo.sKUID" placeholder="SKUID/若输入多条请按回车" :clearable="true" @callback="callbackSKUIDList"
            title="SKU ID">
          </inputYunhan>
        </div>

        <div style="margin-left: 10px;">
          <inputYunhan :key="'3'" :keys="'three'" :width="'150px'" ref="" :inputt.sync="ListInfo.goodsCode"
            v-model="ListInfo.goodsCode" placeholder="商品编码/若输入多条请按回车" :clearable="true" @callback="callbackgoodsCode"
            title="商品编码">
          </inputYunhan>
        </div>

        <el-button style="margin-left: 10px;" type="primary" @click="getList('search')">搜索</el-button>

        <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button @click="startImport"
          type="primary" icon="el-icon-share" @command="handleCommand"> 导入
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a">下载模版</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button type="primary" @click="onExport" style="margin-left: 5px;"  v-if="checkPermission('salesDetail_kj_export')">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'halfSalesDetails202408041405'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0"
      :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="display: flex; align-items: baseline; height: 75px;margin-top: 10px;">
        <!-- <el-date-picker style="width: 150px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay" type="date"
            placeholder="选择日期" :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
          </el-date-picker> -->
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>


    <el-dialog title="库存为0的时间" :visible.sync="dialogHisVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="margin-top: 10px; height: 400px; overflow: scroll;">
        <ul>
          <li v-for="(time, index) in displayedTimeList" :key="index">
            {{ time }}
          </li>
        </ul>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogHisVisible = false">关闭</el-button>
      </span>
    </el-dialog>

  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { import_ActivePrice, pageActivePriceAsync, exportActivePrice } from '@/api/bookkeeper/crossBorderV2'
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import dayjs from 'dayjs'
import inputYunhan from "@/components/Comm/inputYunhan";
const tableCols = [
  { sortable: 'custom', width: '110', align: 'center', prop: 'shopCode', label: '店铺ID', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'shopName', label: '店铺名称', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'goodsName', label: '商品名称', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'goodsId', label: '商品ID', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'skcId', label: 'SKCID', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'skuId', label: 'SKUID', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'activityId', label: '活动ID', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'activePrice', label: '活动申报价格', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'routinePrice', label: '日常申报价格', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'referencePrice', label: '参考申报价格', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'attributeSets', label: '属性集', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'submitActiveTime', label: '活动提交时间', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'activeStartTime', label: '活动开始时间', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'activeEndTime', label: '活动结束时间', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'activeName', label: '活动名称', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'activeStatus', label: '活动状态', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'activeInventory', label: '活动库存', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'remainderActiveInventory', label: '剩余库存', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'dataCreatedTime', label: '数据抓取时间', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'createdTime', label: '更新时间', },
  {
    istrue: true, type: 'button', width: '110', label: '操作',
    btnList: [
      { label: "库存为0的时间", display: (row) => { return row.isHandle == true; }, handle: (that, row) => that.showDetail(row) }
    ]
  },
]
export default {
  name: "halfSalesDetails",
  components: {
    MyContainer, vxetablebase, inputYunhan
  },
  data() {
    return {
      // yearMonthDay: null,
      dialogVisible: false,//导入弹窗
      fileList: [],//上传文件列表
      uploadLoading: false,//上传按钮loading
      fileparm: {},//上传文件参数
      timeRanges: [],//时间范围
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        start: null,//开始时间
        end: null,//结束时间
        shopCodes: null,
        // sKCIDList:null,
        // sKUIDList:null,
        // goodsCodeList:null,
      },
      tableCols,
      summaryarry: {},
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
      shopList: [],
      dialogHisVisible: false,
      displayedTimeList: [], // 用于存储传入的时间列表
    }
  },
  async mounted() {
    await this.getList()
    await this.getShopList()


  },
  methods: {
    async changeTime(e) {
      this.ListInfo.start = e ? e[0] : null
      this.ListInfo.end = e ? e[1] : null
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      // form.append("yearMonthDay", dayjs(this.yearMonthDay).format('YYYYMMDD'));
      var res = await import_ActivePrice(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      // if (!this.yearMonthDay) {
      //   this.$message({ message: "请选择日期", type: "warning" });
      //   return false;
      // }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      // this.yearMonthDay = null
      this.dialogVisible = true;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      this.ListInfo.shopCode = this.ListInfo.shopCodes.join(",");
      if (this.timeRanges != null && this.timeRanges.length == 0) {
          //默认给近1天时间
          this.ListInfo.startDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
          this.ListInfo.endDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
          this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
      }
      const { data, success } = await pageActivePriceAsync(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async handleCommand(command) {
      switch (command) {
        //下载模版
        case 'a':
          await this.downLoadFile()
          break;
      }
    },
    async downLoadFile() {
      window.open("/static/excel/KjSalesDetails/TEMU-半托活动价.xlsx", "_blank");
    },
    async callbackSKCIDList(val) {
      this.ListInfo.sKCID = val;
    },
    async callbackSKUIDList(val) {
      this.ListInfo.sKUID = val;
    },
    async callbackgoodsCode(val) {
      this.ListInfo.goodsCode = val;
    },
    async onExport() {//导出列表数据；
      this.ListInfo.shopCode = this.ListInfo.shopCodes.join(",");

      var res = await exportActivePrice(this.ListInfo);
      if (res?.data.success) {
        this.$message({ message: res.data.msg, type: "success" });
      }
    },
    async getShopList() {
      const res1 = await getAllShopList({ platforms: [15] });
      this.shopList = res1.data?.map(item => { return { value: item.shopCode, label: item.shopName }; });
    },
    showDetail(row) {
      this.displayedTimeList = row.dataCreatedTimeList; // 保存当前 row 的时间列表
      this.dialogHisVisible = true;
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 45px;
}
</style>