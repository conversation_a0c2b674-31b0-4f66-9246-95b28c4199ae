<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <template #header>
        <el-form  class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent ></el-form>
      </template>
      <!--列表-->
      <ces-table      ref="table"
        :that="that"
        :isIndex="true"
        :hasexpand="false"
        @sortchange="sortchange"
        :tableData="pddcontributeinfolist"
        @select="selectchange"
        :isSelection="false"
        :tableCols="tableCols"
        :loading="listLoading"
        :summaryarry='summaryarry'
      >
        <el-table-column type="expand">
          <template slot-scope="props">
            <div>
              <el-table :data="props.row.detaildata" style="width: 100%">
                <el-table-column
                  v-for="col in props.row.detailcols"
                  :prop="col.prop"
                  :label="col.label"
                  :key="col"
                >
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <template slot="extentbtn">
          <el-button-group>
            
            <el-button style="padding: 0;margin: 0;">
                <el-input v-model.trim="Filter.Sku_ids" maxlength="30" clearable placeholder="调拨编码" style="width:150px;" />
              </el-button>
              <el-button style="padding: 0;">
                <el-select v-model="Filter.Warehouse" placeholder="调出仓" :clearable="true">
                  <el-option v-for="item in Warehouseslist" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              </el-button>
              <el-button style="padding: 0;">
                <el-select v-model="Filter.Link_warehouse" placeholder="调入仓" :clearable="true">
                  <el-option v-for="item in Warehouseslist" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              </el-button>
              <el-button style="padding: 0;margin: 0;">
                <el-input v-model.trim="Filter.CreatedUserName" maxlength="20" clearable placeholder="申请人" style="width:150px;" />
              </el-button>
              <el-button style="padding: 0;margin: 0;">
                <el-input v-model.trim="Filter.Receiver_name" maxlength="20" clearable placeholder="收货人" style="width:150px;" />
              </el-button>
              <el-button style="padding: 0;margin: 0;">
                <el-date-picker style="width: 250px" v-model="Filter.timerange" type="datetimerange" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd" range-separator="至" start-placeholder="申请开始日期" end-placeholder="申请结束日期">
                </el-date-picker>
              </el-button>
              
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-button-group>
        </template>
      </ces-table>
      <!--分页-->
      <template #footer>
        <my-pagination
          ref="pager"
          :total="total"
          :checked-count="sels.length"
          @get-page="getpddcontributeinfoList"
        />
      </template>
      
    </my-container>
  </template>
  <script>
  import { getSaleAfterPddList,exportSaleAfterPddList,getPettyPaymentPDDList,exportPettyPaymentPDDList} from '@/api/bookkeeper/reportday'
  import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
  import dayjs from "dayjs";
  import cesTable from "@/components/Table/table.vue";
  import { formatPlatform, formatTime, formatYesornoBool, formatLinkProCode } from "@/utils/tools";
  import { getListByStyleCode } from "@/api/inventory/basicgoods"
  import { importSaleAfterPdd,importPettyPaymentPDD } from '@/api/bookkeeper/import'
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  import {getAllocateList} from "@/api/inventory/allocate";
  import {getWarehouses} from "@/api/storehouse/storehouse";
  const tableCols = [
  { istrue: true,  prop: 'sku_ids', label: '调拨编码', sortable: 'custom', width: '150' },
  { istrue: true,  prop: 'qtytotal',  label: '调拨数量', width: '100',sortable: 'custom' },
  { istrue: true,  prop: 'expiration_date', label: '有效期', sortable: 'custom', width: '150' },
  { istrue: true,  prop: 'warehouseName', label: '调出仓', sortable: 'custom', width: '150'},
  { istrue: true,  prop: 'link_warehouseName', label: '调入仓', sortable: 'custom', width: '150' },
  { istrue: true,  prop: 'receiver_name', label: '收货人', sortable: 'custom', width: '150',},
  { istrue: true,  prop: 'applicant', label: '申请人', sortable: 'custom', width: '150', },
  { istrue: true,  prop: 'io_date', label: '申请时间', sortable: 'custom', width: '150', },
  { istrue: true,  prop: 'status', label: '状态', sortable: 'custom', width: '150' ,formatter:(row)=>row.status=="-1"?"拒绝":row.status=="0"?"待申请":row.status=="1"?"申请中":row.status=="2"?"调拨已撤销":row.status=="3"?"调拨成功":row.status=="4"?"调拨部分成功":row.status=="5"?"确认调拨失败":row.status=="6"?"创建调拨失败":row.status=="7"?"撤销":""},
  
  ];
  export default {
    name: "Users",
    components: {
      MyContainer,
      MyConfirmButton,
      MySearch,
      MySearchWindow,
      cesTable,
      
    },
    data() {
      return {
        
        dialogVisible: false,
        uploadLoading:false,
        searchloading:false,
        importfilter:{
          version:''
        },
        that: this,
        Filter: {
        Sku_ids: null,
        Warehouse:null,
        Link_warehouse:null,
        CreatedUserName:null,
        Receiver_name:null,
        timerange: null,
        StartDate: null,
          EndDate: null,
      },
      options: [],
      styleCode: null,
        shopList: [],
        userList: [],
        groupList: [],
        pddcontributeinfolist: [],
        tableCols: tableCols,
        total: 0,
        summaryarry: {  },
        pager: { OrderBy: "", IsAsc: false },
        sels: [], // 列表选中列
        listLoading: false,
        pageLoading: false,
        selids: [],
        fileList: [],
        Warehouseslist:[],
      };
    },
    async mounted() {
      await this.getWarehousesList();
    },
    async created() {
    await this.getShopList();
   
  },
    methods: {
      async getWarehousesList() {
      var res3 = await getWarehouses();
      this.Warehouseslist = res3.data?.map(item => { return { value: item.wms_co_id, label: item.name }; });

     
    },

      
      datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
      
      sortchange(column) {
        if (!column.order) this.pager = {};
        else
          this.pager = {
            OrderBy: column.prop,
            IsAsc: column.order.indexOf("descending") == -1 ? true : false,
          };
        this.onSearch();
      },
      
      onSearch() {
        this.$refs.pager.setPage(1);
        this.getpddcontributeinfoList();
      },
      async getpddcontributeinfoList() {
        this.Filter.StartDate = null;
          this.Filter.EndDate = null;
      
        if (this.Filter.timerange) {
          this.Filter.StartDate = this.Filter.timerange[0];
          this.Filter.EndDate = this.Filter.timerange[1];
        }
      
        const para = { ...this.Filter };
        var pager = this.$refs.pager.getPager();
        const params = {
          ...pager,
          ...this.pager,
          ...para,
        };
        this.listLoading = true;
        const res = await getAllocateList(params);
        console.log(res);
        this.listLoading = false;
        console.log(res.data.list);
        this.total = res.data.total;
        this.pddcontributeinfolist = res.data.list;
        this.summaryarry=res.data.summary;
      },
      selectchange: function (rows, row) {
        this.selids = [];
        rows.forEach((f) => {
          this.selids.push(f.id);
        });
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
  