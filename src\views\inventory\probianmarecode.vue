<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="日期:">
                    <el-date-picker style="width: 260px" v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                </el-form-item>
                <el-form-item label="分仓:">
                    <el-select v-model="filter.warehouse" placeholder="请选择分仓" style="width: 100%">
                        <el-option label="请选择" value></el-option>
                        <el-option label="义乌" value="0"></el-option>
                        <el-option label="昌东" value="1"></el-option>
                        <el-option label="安徽" value="3"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="运营组:">
                    <el-select v-model="filter.groupId" placeholder="请选择分类" style="width: 100%">
                        <el-option label="所有" value="" />
                        <el-option v-for="item in categroylist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="采购员:">
                    <el-select v-model="filter.brandId" multiple collapse-tags clearable placeholder="请选择品牌" style="width: 150px">
                        <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="类型:">
                    <el-select v-model="filter.recordType" placeholder="请选择类型" style="width: 100%">
                        <el-option label="所有" value />
                        <el-option label="加工进仓" value="0" />
                        <el-option label="加工出仓" value="1" />
                        <el-option label="采购退货" value="2" />
                        <el-option label="采购进仓" value="3" />
                        <el-option label="其它进仓" value="4" />
                        <el-option label="其它退货" value="5" />
                        <el-option label="其它出库" value="6" />
                        <el-option label="销售出仓" value="7" />
                        <el-option label="销售退货" value="8" />
                        <el-option label="盘点" value="9" />
                    </el-select>
                </el-form-item>
                <el-form-item label="商品编码:">
                    <el-input v-model="filter.proBianMa" />
                </el-form-item>
                <el-form-item label="商品编码名称:">
                    <el-input v-model="filter.proBianMaName" />
                </el-form-item>
                <el-form-item label="数量:">
                    <el-row style="width: 180px;padding: 0">
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                            <el-input type="number" placeholder="最小值" v-model="filter.minCount" oninput="if(value){value=value.replace(/[^\-\d]/g,'')} if(value>2147483647){value=2147483647} if(value<-2147483647){value=-2147483647}" />
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                            <el-input type="number" placeholder="最大值" v-model="filter.maxCount" oninput="if(value){value=value.replace(/[^\-\d]/g,'')} if(value>2147483647){value=2147483647} if(value<-2147483647){value=-2147483647}" />
                        </el-col>
                    </el-row>
                </el-form-item>
                <el-form-item label="成本价:">
                    <el-row style="width: 180px;padding: 0">
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                            <el-input type="number" placeholder="最小值" v-model="filter.minCost" oninput="if(value>2147483647){value=2147483647} if(value<-2147483647){value=-2147483647}" />
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                            <el-input type="number" placeholder="最大值" v-model="filter.maxCost" oninput="if(value>2147483647){value=2147483647} if(value<-2147483647){value=-2147483647}" />
                        </el-col>
                    </el-row>
                </el-form-item>
                <el-form-item label="成本总金额:">
                    <el-row style="width: 180px;padding: 0">
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                            <el-input type="number" placeholder="最小值" v-model="filter.minCostTotal" oninput="if(value>2147483647){value=2147483647} if(value<-2147483647){value=-2147483647}" />
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                            <el-input type="number" placeholder="最大值" v-model="filter.maxCostTotal" oninput="if(value>2147483647){value=2147483647} if(value<-2147483647){value=-2147483647}" />
                        </el-col>
                    </el-row>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' :isSelectColumn='false' @sortchange='sortchange' @select='selectchange' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" :showsummary='true' :summaryarry='summaryarry' />
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </my-container>
</template>
<script>
    import { getAllProBrand, pageWarehouseRecord2 } from '@/api/inventory/warehouse'
    import { getDirectorGroupList as getAllProBianMaCategroy } from '@/api/operatemanage/base/shop'
    import { formatTime, formatYesornoBool, formatmoney, formatWarehouse } from "@/utils/tools";
    import cesTable from "@/components/Table/table.vue";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    const tableCols = [
        { istrue: true, prop: 'date', label: '日期', width: '100', sortable: 'custom', formatter: (row) => formatTime(row.date, 'YYYY-MM-DD') },
        { istrue: true, prop: 'warehouse', label: '分仓', width: '75', sortable: 'custom', formatter: (row) => formatWarehouse(row.warehouse) },
        { istrue: true, prop: 'proBianMa', label: '编码', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'groupName', label: '运营组', width: '100', },
        { istrue: true, prop: 'brandName', label: '采购员', width: '130', },
        { istrue: true, prop: 'proBianMaName', label: '编码名称', width: '220', sortable: 'custom' },
        { istrue: true, prop: 'recordTypeStr', label: '类型', width: '80' },
        { istrue: true, prop: 'count', label: '数量', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'cost', label: '成本价', width: '100', sortable: 'custom', formatter: (row) => formatmoney(row.price) },
        { istrue: true, prop: 'costTotal', label: '成本总金额', width: '150', formatter: (row) => formatmoney(row.saleCount) },
    ];
    const tableHandles = [
        // {label:"导出", handle:(that)=>that.onExport()},
    ];
    export default {
        name: "Users",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
        data () {
            return {
                that: this,
                filter: {
                    timerange: null,
                    startDate: null,
                    endDate: null,
                    warehouse: null,
                    recordType: null,
                    groupId: null,
                    brandId: null,
                    proBianMa: '',
                    proBianMaName: '',
                    maxCount: null,
                    minCount: null,
                    maxCost: null,
                    minCost: null,
                    maxBasePrice: null,
                    minBasePrice: null
                },
                categroylist: [],
                brandlist: [],
                list: [],
                tableCols: tableCols,
                tableHandles: tableHandles,
                pager: { OrderBy: "", IsAsc: false },
                summaryarry: {},
                total: 0,
                sels: [],
                selids: [],
                listLoading: false,
                pageLoading: false
            };
        },
        async mounted () {
            await this.defaultDate();
            await this.init();
            await this.onSearch()
        },
        methods: {
            async defaultDate () {
                let date = new Date()
                let year = date.getFullYear().toString()   //'2019'
                let month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()  //'04'
                let da = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()  //'12'
                let end = year + '-' + month + '-' + da  //当天'2019-04-12'
                let beg = year + '-' + month + '-01'    //当月第一天'2019-04-01'
                this.filter.timerange = [beg, end]
            },
            async init () {
                this.selectloading = true;
                setTimeout(async () => {
                    this.selectloading = false;
                    var res1 = await getAllProBianMaCategroy();
                    if (!res1?.success) return;
                    this.categroylist = res1.data.map(item => {
                        return { value: item.key, label: item.value };
                    });
                    var res2 = await getAllProBrand();
                    if (!res2?.success) return;
                    this.brandlist = res2.data.map(item => {
                        return { value: item.key, label: item.value };
                    });
                }, 200);
            },
            async onSearch () {
                this.$refs.pager.setPage(1)
                this.getlist()
            },
            async getlist () {
                var hasparm = false;
                var arry = Object.keys(this.filter)
                if (arry.length == 0) return;

                for (let key of Object.keys(this.filter)) {
                    if (this.filter[key])
                        hasparm = true;
                }
                if (!hasparm) return;
                if (!this.pager.OrderBy) this.pager.OrderBy = "";
                var pager = this.$refs.pager.getPager()
                const params = {
                    ...pager,
                    ...this.pager,
                    ... this.filter
                }
                if (params.timerange) {
                    params.startDate = params.timerange[0];
                    params.endDate = params.timerange[1];
                }
                this.listLoading = true
                const res = await pageWarehouseRecord2(params)
                this.listLoading = false
                if (!res?.success) return
                this.total = res.data.total
                const data = res.data.list
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
                this.summaryarry = res.data.summary;
            },
            sortchange (column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch();
            },
            selsChange: function (sels) {
                this.sels = sels
            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.proBianMa);
                })
            }
        },
    };
</script>

<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
