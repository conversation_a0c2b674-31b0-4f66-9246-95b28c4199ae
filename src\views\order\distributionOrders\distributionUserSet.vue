<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="ListInfo.phone" placeholder="手机号" maxlength="20" clearable class="publicCss" />
        <el-select v-model="ListInfo.shipperFxName" placeholder="货主分销" filterable class="publicCss"
          clearable filterable>
          <el-option v-for="item in fxUserNames" :key="'2' + item" :label="item" :value="item" />
        </el-select>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
      </div>
    </template>

    <vxetablebase :id="'cargoOwnerSelfOrders202412141512'" :tablekey="'cargoOwnerSelfOrders202412141512'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      border :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0"  :isNeedExpend="false"
      :loading="loading" :height="'100%'">
    </vxetablebase>

    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>


    <el-dialog :title="editUserSetDialog.title" :visible.sync="editUserSetDialog.visiable" width="20%" v-dialogDrag
      :close-on-click-modal="false" append-to-body>
      <div style="margin-bottom: 10px;">
      </div>
      <span>
        <el-form ref="ruleForm" :model="addForm" :rules="rules" label-width="100px">
          <el-form-item label="手机号" prop="phone">
            {{ editUserSetDialog.data.phone }}
          </el-form-item>
          <el-form-item label="货主分销" prop="timerange">
            <el-select v-model="editUserSetDialog.data.supplierNameList" placeholder="货主分销" filterable class="publicCss"  multiple collapse-tags
              clearable>
              <el-option v-for="item in fxUserNames" :key="'1' + item" :label="item" :value="item" />
            </el-select>
          </el-form-item> 
        </el-form> 
        <span slot="footer" class="dialog-footer" style="margin-left: 30%;">
            <el-button type="primary" :loading="editUserSetDialog.loading" @click="editUserSet">保存</el-button>
            <el-button @click="editUserSetDialog.visiable = false">取 消</el-button>
          </span>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { getShipperFxNameList, updateShipperFxWxAppUserSupplierName, getShipperFxWxAppUserList } from '@/api/order/shipperFxOrder';
import inputYunhan from "@/components/Comm/inputYunhan";
const tableCols = [
  { istrue: true, prop: 'phone', label: '手机号', sortable: 'custom', },
  { istrue: true, prop: 'supplierName', label: '货主分销', sortable: 'custom', },
  { istrue: true, prop: 'createdTime', label: '创建时间', sortable: 'custom', },
  {
    istrue: true, type: "button", label: '操作', align: 'center',
    btnList: [
      { label: "编辑", permission: "", handle: (that, row) => that.editFxUser(row) }
    ]
  }
]
export default {
  name: "distributionUserSet",
  components: {
    MyContainer, vxetablebase, inputYunhan, MyConfirmButton
  },
  data() {
    return {
      dialogVisible: false,
      uploadLoading: false,
      fxUserNames: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'createdTime',
        isAsc: false,
        phone: null,
        shipperFxName:null
      },
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      editUserSetDialog: {
        data: {
          phone: null,
          supplierNameList:[],
          supplierName: null
        },
        visiable: false,
        loading: false,
        title: '编辑货主用户'
      }
    }
  },
  async mounted() {
    await getShipperFxNameList()
      .then(({ data }) => {
        this.fxUserNames = data;
      })
    await this.getList('search');
  },
  methods: {
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getShipperFxWxAppUserList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    editFxUser(row) {
      this.editUserSetDialog.visiable = true;
      this.editUserSetDialog.data.phone = row.phone;
      if(row.supplierName){
        this.editUserSetDialog.data.supplierNameList = row.supplierName.split(',');
      }else{
        this.editUserSetDialog.data.supplierNameList = [];
      }
      this.editUserSetDialog.data.supplierName = row.supplierName;
    },
    async editUserSet(){
      this.editUserSetDialog.data.supplierName = this.editUserSetDialog.data.supplierNameList.join(',');
      await updateShipperFxWxAppUserSupplierName({
        phone: this.editUserSetDialog.data.phone,
        supplierName: this.editUserSetDialog.data.supplierName
      }).then(({ data, success }) => {
        if (success) {
          this.$message.success('保存成功！')
          this.editUserSetDialog.visiable = false;
          this.getList();
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
