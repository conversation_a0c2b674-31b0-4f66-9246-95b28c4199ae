<template>
  <el-tabs v-model="activeName" style="height: 94%;">
     <el-tab-pane label="仓库预警" name="third" style="height: 100%;">
      <warnstocksummary ref="warnstocksummary" @showwarnstock='showwarnstock'/>
    </el-tab-pane>
    <el-tab-pane label="仓库预警订单" name="forth" style="height: 100%;">
      <warnstock ref="warnstock"/>
    </el-tab-pane>
    <el-tab-pane label="运营预警" name="first" style="height: 100%;">
      <warnsummary ref="warnsummary" @showwarnorders='showwarnorders'/>
    </el-tab-pane>
    <el-tab-pane label="运营预警订单" name="second" style="height: 100%;">
      <warnorder ref="warnorder"/>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import {getDirectorGroupList} from '@/api/operatemanage/base/shop'
import warnsummary from '@/views/order/warn/warnsummary'
import warnorder from '@/views/order/warn/warnorder'
import warnstocksummary from '@/views/order/warn/warnstocksummary'
import warnstock from '@/views/order/warn/warnstock'
import container from '@/components/my-container/nofooter'
export default {
  name: 'Roles',
  components: {container,warnsummary,warnorder,warnstocksummary,warnstock},
  data() {
    return {
      activeName: 'third',
      grouplist:[],
      pageLoading: false,    
      dialogVisible: false
    }
  },
  async mounted() {
    await this.setGroupSelect();
   // await this.onSearch();
  },
  beforeUpdate() {
     
  },
  methods: {
    async setGroupSelect(){
       var res2= await getDirectorGroupList();
       this.grouplist = res2.data?.map(item => {return { value: item.key, label: item.value };}); 
    },
    onSearch() {
      if (this.activeName=='first') this.$refs.warnsummary.onSearch();
      else if (this.activeName=='second') this.$refs.warnorder.onSearch();
      else if (this.activeName=='third') this.$refs.warnstocksummary.onSearch();
      else if (this.activeName=='forth') this.$refs.warnstock.onSearch();
    },
    showwarnorders(hours,groupid){
       this.activeName="second"
       this.$refs.warnorder.onSearchWarnOrders(hours, groupid?groupid.toString():null);
    },
    showwarnstock(status){
       this.activeName="forth"
       this.$refs.warnstock.onSearchWarnStock(status);
    },
  }
}
</script>
