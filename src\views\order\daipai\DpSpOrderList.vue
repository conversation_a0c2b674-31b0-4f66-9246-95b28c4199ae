<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <!-- <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                <el-button-group>
                             
                </el-button-group>
            </el-form>
        </template> -->
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='tbdatalist' @select='selectchange' 
        :isSelection='false' :isSelectColumn="true" :tableCols='tableCols' :loading="listLoading" :rowkey="'id'">

            <template slot='extentbtn'>
                <el-button-group>    
                    
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="Filter.dpType" placeholder="代拍类型" clearable style="width:100px;">
                            <el-option :value="null" :label="'全部类型'" />
                            <el-option :value="0" :label="fmtDpType(0)" />
                            <el-option :value="1" :label="fmtDpType(1)" />
                            <el-option :value="2" :label="fmtDpType(2)" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="Filter.dpOrderState" placeholder="订单状态" clearable style="width:100px;">
                            <el-option v-for="item in AllLinkDaiPaiSpOrderDpStateOpts" :value="item.value" :label="item.label" :key="item.value"/>                          
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="Filter.timeType" placeholder="时间类型" style="width:90px;">
                            <el-option :value="0" :label="'下单时间'" />
                            <el-option :value="1" :label="'付款时间'" />                            
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-date-picker style="width:220px" v-model="Filter.gDate" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"></el-date-picker>
                    </el-button>


                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="Filter.keywords" type="text" maxlength="100" clearable placeholder="请输入关键字..." style="width:200px;" >
                            <el-tooltip slot="suffix" class="item" effect="dark" :content="keywordsTip" placement="bottom">
                                <i  class="el-input__icon el-icon-question"></i>
                            </el-tooltip>                            
                        </el-input>
                    </el-button>
                    
                    <el-button type="primary" @click="onSearch" icon="el-icon-search">查询</el-button>
                    <el-button @click="()=>{Filter={};}"  icon="el-icon-close">清空查询条件</el-button>         

                </el-button-group>
            </template>
            
            <el-table-column width="120" label="操作" fixed="right">               
                <template slot-scope="scope"> 
                    <el-button type="text" v-if="scope.row.dpState===0" @click="onGenerateDpSpOrder(scope.row.id)" >生成代拍</el-button>
                    <el-button type="text" @click="onOpenDtl(scope.row.id,3)" >详情</el-button>
                </template>
            </el-table-column>

        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettbdatalist1" />
        </template>
       
       
    </my-container>
</template>
<script>  

    import {
        PageDpSpOrderListAsync, 
    } from '@/api/order/alllinkDaiPai'

    import cesTable from "@/components/Table/table.vue";
    
    import { formatmoney, formatPercen, getUrlParam, formatTime, setStore, getStore,formatLink ,AllLinkDaiPaiSpOrderDpStateOpts,fmtAllLinkDaiPaiSpOrderDpState} from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";

 
    
    const fmtDpType=function(state){
        switch(state){
            case 0:return '待确认';
            case 1:return '运营代拍';
            case 2:return '采购代拍';
        }
        return '其他';
    }

    const fmtOrderPlatform=function(v){
        switch(v){
            case 0:return '其他';
            case 1:return '1688';
        }
        return '其他'
    }

    

    const tableCols = [        
        { istrue: true, prop: 'dpType', label: '代拍类型', width: '90', sortable: 'custom' ,formatter:(row)=>fmtDpType(row.dpType)},
        { istrue: true, prop: 'dpOrderState', label: '订单状态', width: '90', sortable: 'custom' ,formatter:(row)=>fmtAllLinkDaiPaiSpOrderDpState(row.dpOrderState)},
        { istrue: true, prop: 'jqrNoticeTime', label: '机器人通知时间', width: '130', sortable: 'custom',formatter:(row)=>formatTime(row.jqrNoticeTime,'MM月DD HH:mm')},
        { istrue: true, prop: 'jqrMsg', label: '机器人错误信息', width: '130', sortable: 'custom' },
        { istrue: true, prop: 'innerOrderNum', label: '内部订单号', width: '80', sortable: 'custom',type:'orderLogInfo',orderType:'orderNoInner'  },
        { istrue: true, prop: 'onlineOrderNum', label: '线上订单号', width: '160', sortable: 'custom' },
        { istrue: true, prop: 'shopName', label: '店铺名称', width: '110', sortable: 'custom' },
      
        { istrue: true, prop: 'goodsCode', label: '商品编码', width: '140', sortable: 'custom' },
        { istrue: true, prop: 'goodsName', label: '商品名称', minwidth: '170', sortable: 'custom' },
        { istrue: true, prop: 'count', label: '数量', width: '60', sortable: 'custom' },

        { istrue: true, prop: 'createdTime', label: '生成时间', width: '100', sortable: 'custom',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD') },
        { istrue: true, prop: 'dpOrderPlatform', label: '代拍平台', width: '100', sortable: 'custom',formatter:(row)=>fmtOrderPlatform(row.dpOrderPlatform) },
        { istrue: true, prop: 'dpOrderNum', label: '代拍单号', width: '160', sortable: 'custom' },

        { istrue: true, prop: 'dpOrderTime', label: '代拍下单时间', width: '100', sortable: 'custom',formatter:(row)=>formatTime(row.dpOrderTime,'YYYY-MM-DD') },
        { istrue: true, prop: 'dpPayTime', label: '代拍付款时间', width: '100', sortable: 'custom',formatter:(row)=>formatTime(row.dpPayTime,'YYYY-MM-DD') },
        { istrue: true, prop: 'dpTotalAmount', label: '代拍金额', width: '80', sortable: 'custom' },

        { istrue: true, prop: 'sendOutTime', label: '代拍发货时间', width: '100', sortable: 'custom',formatter:(row)=>formatTime(row.dpPayTime,'YYYY-MM-DD') },

        { istrue: true, prop: 'dpExpressNum', label: '快递单号', width: '110', sortable: 'custom' },
        { istrue: true, prop: 'dpExpressCompany', label: '快递公司', width: '100', sortable: 'custom' },

    ];

    export default {
        name: "DpSpOrderList",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
        data() {
            return {
                that: this,
                AllLinkDaiPaiSpOrderDpStateOpts:AllLinkDaiPaiSpOrderDpStateOpts,
                Filter: {
                    gDate: [ ],
                    dpType:null,
                    timeType:0
                },
                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一月',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三月',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                tbdatalist: [],
                tableCols: tableCols,
                total: 0,
                pager: { OrderBy: "", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                //
                selids: [],
                keywordsTip:"支持搜索的内容：内外订单号、店名、商品编码名称、代拍厂家、备注、省市县区街道、异常状态类型、店铺状态",
                

            };
        },
        async mounted() {

            this.onSearch();
        },
        methods: { 
            fmtDpType:fmtDpType,
            fmtOrderPlatform:fmtOrderPlatform,
            onOpenDtl(oid,mode){    
                let self=this;
                this.$showDialogform({
                    path:`@/views/order/daipai/DpSpOrderForm.vue`,
                    title:'订单详情',
                    args:{oid:oid,mode:mode},
                    height:300,                    
                });                         
            },          
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderField = column.prop;

                    this.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                }
                this.onSearch();
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.gettbdatalist1();
            },
            async gettbdatalist1() {
                if (this.Filter.gDate) {
                    this.Filter.timeStart = this.Filter.gDate[0];
                    this.Filter.timeEnd = this.Filter.gDate[1];
                }
                else {
                    this.Filter.timeStart = null;
                    this.Filter.timeEnd = null;

                }
                const para = { ...this.Filter };
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...para,
                };

                this.listLoading = true;
                const res = await PageDpSpOrderListAsync(params);

                this.listLoading = false;

                this.total = res.data.total
                this.tbdatalist = res.data.list;

            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
        },
    };
</script>
