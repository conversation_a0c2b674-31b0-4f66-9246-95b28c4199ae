<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker class="publicCss" v-model="ListInfo.yearMonth" type="month" format="yyyyMM"
          value-format="yyyyMM" placeholder="选择月份">
        </el-date-picker>
        <el-select filterable clearable v-model="ListInfo.shopCode" placeholder="所属店铺" class="publicCss">
          <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
            :value="item.shopCode"></el-option>
        </el-select>
        <el-button type="primary" @click="getList('search')">查询</el-button>
        <el-button type="primary" @click="onExport">汇总导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'violationsCompensate202302031421'" :tablekey="'violationsCompensate202302031421'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { getFinancialDetail_ReturnFreight_KS, ExportFinancialDetail_ReturnFreight_KS } from '@/api/monthbookkeeper/financialDetail'
import { getList as getshopList } from '@/api/operatemanage/base/shop';

const tableCols = [
  { sortable: 'custom', width: '250', align: 'center', prop: 'compensationNo', label: '赔付单ID', },
  { sortable: 'custom', width: '250', align: 'center', prop: 'compensationReason', label: '赔付原因', },
  { sortable: 'custom', width: '250', align: 'center', prop: 'compensationAmount', label: '赔付金额', },
  { sortable: 'custom', width: '250', align: 'center', prop: 'compensationStatus', label: '赔付状态', },
  { sortable: 'custom', width: '250', align: 'center', prop: 'orderNumber', label: '订单ID', },
  { sortable: 'custom', width: '250', align: 'center', prop: 'compensationTime', label: '赔付时间', },
  { sortable: 'custom', width: '250', align: 'center', prop: 'importTime', label: '申诉状态', },
  { sortable: 'custom', width: '250', align: 'center', prop: 'shopName', label: '店铺', },
  { sortable: 'custom', width: '250', align: 'center', prop: 'yearMonth', label: '日期', },
]
export default {
  name: "violationsCompensate",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      shopList: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        feeType: 2,//违规赔付
        platform: 14,
        yearMonth: '',
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    const res1 = await getshopList({ platform: 14, CurrentPage: 1, PageSize: 100000 });
    this.shopList = res1.data.list
    // await this.getList()
  },
  methods: {
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getFinancialDetail_ReturnFreight_KS(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async onExport(opt) {
      if (!this.ListInfo.yearMonth) {
        this.$message({ message: "请先选择月份", type: "warning" });
        return;
      }
      const params = { ...this.ListInfo, ...opt };
      let res = await ExportFinancialDetail_ReturnFreight_KS(params);
      if (!res?.data) {
        return
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 180px;
    margin-right: 5px;
  }
}
</style>
