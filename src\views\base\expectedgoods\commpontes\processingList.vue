<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" :value-format="'yyyy-MM-dd'"
                    @change="changeTime($event, 'create')">
                </el-date-picker>
                <el-date-picker v-model="proTimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" :value-format="'yyyy-MM-dd'"
                    @change="changeTime($event, 'process')">
                </el-date-picker>
                <el-select v-model="ListInfo.status" placeholder="完成状态" class="publicCss" clearable>
                    <el-option key="'待领取'" label="待领取" value="待领取" />
                    <el-option key="'已领取'" label="已领取" value="已领取" />
                    <el-option key="'已完成'" label="已完成" value="已完成" />
                </el-select>
                <el-input v-model="ListInfo.batchNo" placeholder="批次号" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.prePackUserName" placeholder="加工人" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model="ListInfo.combineCode" placeholder="组合编码" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model="ListInfo.skus" placeholder="skus" maxlength="50" clearable class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
            </div>
        </template>
        <vxetablebase :id="'processingList202408041354'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="200">
                    <template #default="{ row, $index }">
                        <div style="display: flex">
                            <div v-if="row.status == '待领取'" style="margin-right: 10px;">
                                <el-button v-if="checkPermission('api:inventory:prepack:ReceiveManifestInfo')" type="text" @click="receive(row, false)">领取</el-button>
                            </div>
                            <div v-if="row.status == '已领取'" style="margin-right: 10px;">
                                <el-button v-if="checkPermission('api:inventory:prepack:ReceiveManifestInfo')" type="text" @click="complete(row)">完成</el-button>
                            </div>
                            <el-button v-if="checkPermission('api:inventory:prepack:PageGetManifests')" type="text" @click="printBatches(row)">打印批次</el-button>
                            <el-button v-if="checkPermission('api:inventory:prepack:PageGetManifests')" type="text" @click="printRow(row)">打印此行</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="完成" :visible.sync="compVisible" width="25%" v-dialogDrag :close-on-click-modal="false">
            <cptComplete v-if="compVisible" :id="id" @close="close" @getList="getList"/>
        </el-dialog>

        <el-dialog title="打印加工清单" :visible.sync="printTableVisable" width="1000px" v-dialogDrag
            :close-on-click-modal="false">
            <div style="max-height: 70vh;overflow: auto;" v-if="printTableVisable">
                <printTables ref="printTable" :batchNo="printBatchNo" v-if="printTableVisable" :tableData="queryData" />
            </div>
            <template #footer>
                <el-button type="primary" @click="doPrint">打印</el-button>
            </template>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import cptComplete from './complete.vue'
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import printTables from './printTables.vue'
import { pageGetManifests, receiveManifestInfo, printManifests, getManifestInfo } from '@/api/inventory/prepack'
import dayjs from 'dayjs'
const tableCols = [
    { istrue: true, prop: 'batchNo', align: 'center', label: '批次', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'createdTime', label: '日期', sortable: 'custom', width: 'auto', },
    { istrue: true, prop: 'combineCode', label: '组合编码',align: 'left', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'skus', label: 'skus',align: 'left', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'goodsInvBoxCount', label: '子箱库存', align: 'left', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'quantity', label: '加工数量', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'prePackQty', label: '完成数量', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'status', label: '状态', align: 'left', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'prePackUserName', align: 'center', label: '加工人', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'prePackTime', align: 'center', label: '完成时间', sortable: 'custom', width: 'auto' },
    // {
    //     istrue: true, align: 'center', label: '操作', width: '200', type: 'button', btnList: [
    //         { label: '领取', type: 'primary', handle: (that, row) => that.receive(row) },
    //         { label: '完成', type: 'primary', handle: (that, row) => that.complete(row) },
    //         { label: '打印批次', type: 'primary', handle: (that, row) => that.printBatches(row) },
    //         { label: '打印此行', type: 'primary', handle: (that, row) => that.printRow(row) },
    //     ]
    // },
]
export default {
    name: "processingList",
    components: {
        MyContainer, vxetablebase, cptComplete, printTables
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                createdStartDate: null,//创建开始时间
                createdEndDate: null,//创建结束时间
                packStartDate: null,//加工开始时间
                packEndDate: null,//加工结束时间
                batchNo: null,//批次号
                prePackUserName: null,//加工人
                combineCode: null,//组合编码
                skus: null,//skus
                status: null,//完成状态
            },
            timeRanges: [],
            proTimeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            id: null,
            compVisible: false,
            queryData: [],
            printTableVisable: false,
            printBatchNo: null
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        doPrint(){
            this.$nextTick(() => {
                this.$refs.printTable.doPrint()
            })
        },
        close() {
            this.compVisible = false
            this.id = null
        },
        complete(row) {
            this.id = row.id
            this.compVisible = true
        },
        async afterSave(afterSave) {
            const val = afterSave.map((item) => {
                return {
                    id: this.id,
                    prePackDDId: item.ddUserId,
                    prePackUserName: item.userName
                }
            })
            if (val.length > 1) return this.$message.error('只能选一个用户')
            const { success } = await receiveManifestInfo(val[0])
            if (success) {
                this.$message.success('领取成功')
                await this.getList()
            } else {
                this.$message.error('领取失败')
            }
        },
        //打印此行
        async printRow(row) {
            this.printBatchNo = null
            this.queryData = [row]
            this.printTableVisable = true
        },
        //打印批次
        printBatches(row) {
            this.queryData = null
            this.printBatchNo = row.batchNo
            this.printTableVisable = true
        },
        //领取
        receive(row, isMore) {
            this.id = row.id
            let _this = this
            this.$showDialogform({
                path: `@/views/base/arrPublicDialogPage.vue`,
                title: '选择人员',
                autoTitle: false,
                args: { isMore },
                height: 300,
                width: '1300px',
                callOk: _this.afterSave
            })
        },
        async changeTime(e, type) {
            if (type == 'create') {
                this.ListInfo.createdStartDate = e ? e[0] : null
                this.ListInfo.createdEndDate = e ? e[1] : null
            } else {
                this.ListInfo.packStartDate = e ? e[0] : null
                this.ListInfo.packEndDate = e ? e[1] : null
            }
            await this.getList()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
            }
            const replaceArr = ['batchNo', 'prePackUserName', 'combineCode', 'skus'] //替换空格的方法,该数组对应str类型的input双向绑定的值
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            const { data, success } = await pageGetManifests(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>