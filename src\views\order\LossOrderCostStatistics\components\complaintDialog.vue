<template>
    <!-- 责任申诉 -->
    <div class="containal">
        <div class="top">
            <div>审核须知:</div>
            <div>1、申诉基础步骤：发起申诉-->提交申诉凭证-->审核凭证-->判定申诉结果-->申诉通过后调整新责任部门，原部门责任及相关事宜剔除。</div>
            <div>2、申诉机会仅一次，请您使用好申诉权益。</div>
            <div>3、申诉时间为责任计算时间(非扣款时间)起当天17:30至次日9:00,超时导致申诉入口关闭,无法支持再次申诉。</div>
        </div>
        <div class="center">
            <!-- 加上插值 -->
            <div class="center_item">
                <div class="item_box">线上订单号:</div>
                <div class="item_box">发起时间:</div>
                <div class="item_box">损耗金额:</div>
            </div>
            <div class="center_item">
                <div class="item_box">原责任部门:</div>
                <div class="item_box">原责任类型:</div>
                <div class="item_box">原责任人:</div>
            </div>
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px">
                <div style="display: flex;justify-content: space-between;">
                    <el-form-item label="新责任部门" prop="name">
                        <el-select v-model="ListInfo.newZrDepartment" placeholder="新责任部门" class="publicWidth" clearable
                            @change="changeNewZrDepartment">
                            <el-option v-for="item in newZrDeptList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="新责任类型" prop="name">
                        <el-select v-model="ListInfo.newZrType2" placeholder="新责任类型" class="publicWidth" clearable>
                            <el-option v-for="item in newZrType" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="新责任人" prop="name">
                        <el-input v-model="ListInfo.newZrUserName" placeholder="新责任人" class="publicWidth1" maxlength="50"
                            clearable />
                    </el-form-item>
                </div>

                <el-form-item label="申诉理由" prop="name">
                    <el-input type="textarea" maxlength="200" minlength="1" placeholder="请输入" v-model="textarea"
                        show-word-limit :rows="4">
                    </el-input>
                </el-form-item>
                <el-form-item label="申诉资料" prop="desc">
                    <YhQuillEditor />
                </el-form-item>
            </el-form>

        </div>
        <div class="footer">
            <el-button size="medium" @click="handleClose">关闭</el-button>
            <el-button type="primary" size="medium">提交申诉</el-button>
        </div>
    </div>
</template>

<script>
import { pageDeductZrAppealList, getDamagedOrdersZrDept, getDamagedOrdersZrType } from '@/api/customerservice/DamagedOrders'
import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'
export default {
    name: 'complaintDialog',
    props: {
        //
        
        handleClose: {
            type: Function,
            required: true
        }
    },
    components: { YhQuillEditor },
    data() {
        return {
            textarea: '',
            ruleForm: null,
            rules: {
                desc: [
                    { required: true, message: '请选择活动资源', trigger: 'change,blur,input' }
                ]
            },
            newZrDeptList: [],
            newZrType: [],
            ListInfo: {
                newZrDepartment: null,
                newZrType2: null,
                newZrUserName: null
            },
            ruleForm: {
                name: '',
                desc: ''
            },
        }

    },
    mounted() {
        this.getZrDept()
    },
    methods: {
        //新责任部门改变
        async changeNewZrDepartment(e) {
            if (e) {
                this.ListInfo.newZrDepartment = e
                this.newZrType = await this.getZrType(this.ListInfo.newZrDepartment)
            } else {
                this.ListInfo.newZrDepartment = null
                this.ListInfo.newZrType2 = null
                this.newZrType = []
            }
        },
        async getZrType(val) {
            const { data, success } = await getDamagedOrdersZrType(val)
            if (success) {
                return data
            }
        },
        //获取责任部门
        async getZrDept() {
            const { data, success } = await getDamagedOrdersZrDept()
            if (success) {
                this.newZrDeptList = data
            } else {
                this.$message.error('获取责任部门失败')
            }
        },
    }
}
</script>

<style scoped>
.containal {
    padding: 0 20px;

    .top {
        color: red;
        margin-bottom: 30px;
    }

    .center {
        .center_item {
            padding-left: 33px;
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;

            .item_box {
                width: 220px;
            }
        }
    }

    .footer {
        display: flex;
        justify-content: flex-end;
    }
}
</style>
