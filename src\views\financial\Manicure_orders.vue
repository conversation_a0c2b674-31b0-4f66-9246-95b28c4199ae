<template>
    <my-container v-loading="pageLoading">
      <template #header>
        <div>
          <el-button-group>
            <el-button style="padding: 0;margin: 0; border: none;">
              <el-select filterable v-model="filter.timeType" collapse-tags placeholder="时间类型" style="width: 110px">
                <el-option label="发生时间" :value="0" />
                <el-option label="订单时间" :value="1" />
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0; border: none;">
              <el-date-picker style="width: 210px" v-model="filter.timerange" type="daterange" :clearable="false"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker>
            </el-button>
            <el-button style="padding: 0;margin: 0; border: none;">
              <YhWarehouseSelector :checkboxOrRadio="'checkbox'" @onChange="(v)=>{filter.ware=v[1];}" ref="YhWarehouseSelector">
              </YhWarehouseSelector>
            </el-button>
            <el-button style="padding: 0;margin: 0; border: none;">
              <el-input v-model.trim="filter.orderNo" :maxlength="150" clearable placeholder="原始线上单号"
                style="width:120px;" />
            </el-button>
            <el-button style="padding: 0;margin: 0; border: none;">
              <el-input v-model.trim="filter.orderNoInner" :maxlength="150" clearable placeholder="内部单号"
                style="width:120px;" />
            </el-button>
            <el-button style="padding: 0;margin: 0; border: none;">
              <el-select v-model="filter.orderTypes" multiple collapse-tags clearable placeholder="订单类型"
                style="width: 150px">
                <el-option v-for="item in orderDayRpt_OrderTypes" :label="item.label" :value="item.value"
                  :key="'orderTypes_filter_' + item.label" />
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0; border: none;">
              <span>排除类型:</span>
              <el-select v-model="filter.exceptOrderTypes" multiple collapse-tags clearable placeholder="排除订单类型"
                style="width: 150px">
                <el-option v-for="item in orderDayRpt_OrderTypes" :label="item.label" :value="item.value"
                  :key="'orderTypes_filter_' + item.label" />
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0; border: none;">
              <el-select v-model="filter.orderStatusS" multiple collapse-tags clearable placeholder="订单状态"
                style="width: 150px">
                <el-option v-for="item in orderDayRpt_OrderStatuss" :label="item.label" :value="item.value"
                  :key="'orderStatusS_filter_' + item.label" />
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0; border: none;">
              <el-input v-model.trim="filter.proCode" :maxlength="150" clearable placeholder="商品ID" style="width:100px;" />
            </el-button>
            <el-button style="padding: 0;margin: 0; border: none;">
              <el-input v-model.trim="filter.shopGoodsCode" :maxlength="150" clearable placeholder="店铺SKU" style="width:100px;"/>
            </el-button>
            <el-button style="padding: 0;margin: 0; border: none;">
              <el-input v-model.trim="filter.goodsCode" :maxlength="150" clearable placeholder="商品编码"
                style="width:100px;" />
            </el-button>
            <el-button style="padding: 0;margin: 0; border: none;">
              <el-input v-model.trim="filter.goodsName" :maxlength="150" clearable placeholder="商品名称"
                style="width:100px;" />
            </el-button>
            <el-button style="padding: 0;margin: 0; border: none;">
              <el-input v-model.trim="filter.styleCode" :maxlength="150" clearable placeholder="系列编码"
                style="width:100px;" />
            </el-button>
            <el-button style="padding: 0; border: none;">
              <el-select filterable v-model="filter.PlatFormss" placeholder="请选择平台" multiple collapse-tags @change="onchangeplatform" clearable
                style="width: 100px">
                <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-button>
            <el-button style="padding: 0; border: none;">
              <el-select filterable clearable v-model="filter.shopName" placeholder="店铺" style="width: 100px">
                <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopName">
                </el-option>
              </el-select>
            </el-button>
  
            <el-button style="padding: 0;margin: 0; border: none;">
              <YyGroupSelector :value="filter.groupIds" @change="(v) => { filter.groupIds = v; }" :width="'170px'" :multiple="true" :collapse="true" ref="YyGroupSelector"/>
            </el-button>
            <el-button style="padding: 0;margin: 0; border: none;">
              <YyDirectorSelector :value="filter.operateSpecialUserIds" :placeholder="'专员'"
                @change="(v) => { filter.operateSpecialUserIds = v; }" :width="'150px'" :multiple="true" :collapse="true" ref="YyDirectorSelector"/>
            </el-button>
            <el-button style="padding: 0;margin: 0; border: none;">
              <YyDirectorSelector :value="filter.userIds" :placeholder="'助理'" @change="(v) => { filter.userIds = v; }" :width="'150px'" :multiple="true" :collapse="true" ref="YyDirectorSelector1"/>
            </el-button>
  
            <el-button style="padding: 0; border: none;">
              <el-select filterable v-model="filter.IsSpecialOrder" collapse-tags clearable placeholder="特殊单"
                style="width: 90px">
                <el-option label="否" :value="false" />
                <el-option label="是" :value="true" />
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0; border: none;">
              <el-select style="width: 115px;" v-model="filter.bzCategory" placeholder="经营大类" :collapse-tags="true"
            remote  :remote-method="remoteMethodBusinessCategory" clearable filterable>
              <el-option v-for="(item, i) in filterList.bussinessCategoryNames" :key="'bussinessCategoryNames' + i + 1"
                :label="item" :value="item" />
            </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0; border: none;">
              <el-select style="width: 115px;" v-model="filter.bzCategory1" placeholder="一级类目" :collapse-tags="true"
            remote  :remote-method="remoteMethodCategoryName1s" clearable filterable>
              <el-option v-for="(item, i) in filterList.categoryName1s" :key="'categoryName1Level' + i + 1" :label="item"
                :value="item" />
            </el-select>
            </el-button>
              <el-button style="padding: 0;margin: 0; border: none;">
              <el-select style="width: 115px;" v-model="filter.bzCategory2" placeholder="二级类目" :collapse-tags="true"
              remote  :remote-method="remoteMethodCategoryName2s" clearable filterable>
                <el-option v-for="(item, i) in filterList.categoryName2s" :key="'categoryName2Level' + i + 1" :label="item"
                  :value="item" />
              </el-select>
            </el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="clearaway">清除</el-button>
          </el-button-group>
        </div>
      </template>
      <vxetablebase :id="'manicureorders202409071437'" :border="true" :align="'center'" :cstmExportFunc="onExport"
        :tablekey="'manicureorders202409071437'" ref="table" :that='that' :isIndex='true' :hasexpand='false'
        @sortchange='sortchange' :isSelectColumn="true" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry'
        @summaryClick='onsummaryClick' :tableData='financialreportlist' :tableCols='tableCols' @cellClick="cellClick"
        :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:95%;margin: 0"
        :xgt="9999">
        <template slot='extentbtn'>
        </template>
      </vxetablebase>
      <!--分页-->
      <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
      </template>
  
  
      <el-dialog title="商品数据趋势图" :visible.sync="dialogDrVisible" width="80%" v-dialogDrag>
        <span>
          <productdrchart v-if="dialogDrVisible"></productdrchart>
        </span>
  
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogDrVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </my-container>
  </template>
  <script>
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
  import { getAllList as getAllShopList, getList as getshopList } from '@/api/operatemanage/base/shop';
  import EveryDayrefund from '@/views/bookkeeper/reportday/EveryDayrefund'
  import {
    productOrderDayReport as pageProductDayReport, exportProductOrderDayReport, queryProductOrderDayReportSumChart, orderDayRpt_OrderTypes, orderDayRpt_OrderTypes_fmtFunc
    , orderDayRpt_OrderStatuss
  } from '@/api/bookkeeper/reportdayV2'
  import { importProductDayReport } from '@/api/bookkeeper/import'
  import { getAllProBrand } from '@/api/inventory/warehouse'
  import productdrchart from '@/views/bookkeeper/reportday/productdrchart'
  import ProductADPDD from '@/views/bookkeeper/reportday/ProductADPDD'
  import { formatPlatform, formatTime, formatYesornoBool, formatLinkProCode, platformlist } from "@/utils/tools";
  import cesTable from "@/components/Table/table.vue";
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  import InputMult from "@/components/Comm/InputMult";
  import { Loading } from 'element-ui';
  import { ruleDirectorGroup } from '@/utils/formruletools'
  import freightDetail from '@/views/bookkeeper/reportday/freightDetail'
  import expressfreightanalysis from '@/views/express/expressfreightanalysis'
  import buschar from '@/components/Bus/buschar'
  import importmodule from '@/components/Bus/importmodule'
  import { getBusinessCategorySelectData } from '@/api/operatemanage/base/category'
  import ordergiftdetail from '@/views/order/gift/ordergiftDetail'
  import { getListByStyleCode } from "@/api/inventory/basicgoods"
  import inputYunhan from "@/components/Comm/inputYunhan";
  import middlevue from "@/store/middle.js"
  import YyGroupSelector from "@/components/YhCom/YyGroupSelector.vue"
  import YyDirectorSelector from "@/components/YhCom/YyDirectorSelector.vue"
  import { filterStatus } from '@/utils/getCols'
  import { getAllWarehouse } from '@/api/inventory/warehouse'
  import YhWarehouseSelector from "@/components/YhCom/YhWarehouseSelector";
  
  let loading;
  const startLoading = () => {
    loading = Loading.service({
      lock: true,
      text: '加载中……',
      background: 'rgba(0, 0, 0, 0.7)'
    });
  };
  const tableCols = [
    { istrue: true, fixed: 'left', prop: 'fsYearMonthDay', label: '年月日', sortable: 'custom', width: '60', type: 'custom' },
    { istrue: true, fixed: 'left', prop: 'platform', fix: true, exportField:'platformstr', label: '平台', width: '45', sortable: 'custom', formatter: (row) => formatPlatform(row.platform), type: 'custom' },
    { istrue: true, fixed: 'left', prop: 'orderNo', label: '原始线上单号', width: '80', type: 'custom' },
    { istrue: true,  prop: 'orderNoInner', label: '内部单号', width: '55', type: 'custom',type:'orderLogInfo',orderType:'orderNoInner'  },
    { istrue: true,  prop: 'wmsName', label: '仓库', sortable: 'custom', width: '80' },
    { istrue: true,  prop: 'tagMultiple', label: '订单标签', width: '80', type: 'custom', sortable: 'custom', },
    { istrue: true,  prop: 'orderStatus', label: '订单状态', width: '55', type: 'custom' },
    { istrue: true,  prop: 'orderType', label: '订单类型', width: '45', type: 'custom', formatter: (row) => orderDayRpt_OrderTypes_fmtFunc(row.orderType) },
    { istrue: true,  prop: 'shopName', label: '店铺', sortable: 'custom', width: '70', formatter: (row) => row.shopName, type: 'custom' },
    { istrue: true,  prop: 'brandId', exportField:'brandName', label: '采购', sortable: 'custom', width: '45', formatter: (row) => row.brandName || ' ', type: 'custom' },
    { istrue: true,  prop: 'groupId', exportField:'groupName',label: '小组', sortable: 'custom', width: '45', formatter: (row) => row.groupName, type: 'custom' },
    { istrue: true,  prop: 'operateSpecialUserId', exportField:'operateSpecialUserName',label: '运营专员', sortable: 'custom', width: '45', formatter: (row) => row.operateSpecialUserName, type: 'custom' },
    { istrue: true,  prop: 'userId', exportField:'userName',label: '运营助理', sortable: 'custom', width: '45', permission: "cgcoltxpddprsi", formatter: (row) => row.userName, type: 'custom' },
    { istrue: true,  prop: 'proCode', fix: true, label: '商品ID', width: '55', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
    { istrue: true,  prop: 'shopGoodsCode', label: '店铺SKU', sortable: 'custom', width: '70' },
    { istrue: true,  prop: 'styleCode', fix: true, label: '系列编码', width: '60', sortable: 'custom', type: 'custom' },
    { istrue: true,  prop: 'bzCategory', label: '经营大类', sortable: 'custom', width: '70' },
    { istrue: true,  prop: 'bzCategory1', label: '一级类目', sortable: 'custom', width: '70' },
    { istrue: true,  prop: 'bzCategory2', label: '二级类目', sortable: 'custom', width: '70' },
    { istrue: true,  prop: 'goodsCode', fix: true, label: '商品编码', width: '60', sortable: 'custom', type: 'custom' },
    { istrue: true,  prop: 'pictureBig', label: '图片', width: '50', type: 'images' },
    { istrue: true,  prop: 'goodsName', label: '商品名称', sortable: 'custom', width: '60' },
    { istrue: true,  prop: 'onlineColorSpecification', label: '颜色/规格', sortable: 'custom', width: '75' },
    { istrue: true, summaryEvent: true, prop: 'payAmont', label: '付款金额', sortable: 'custom', width: '50', formatter: (row) => !row.payAmont ? " " : row.payAmont },
    { istrue: true, summaryEvent: true, prop: 'saleAmont', label: '销售金额', sortable: 'custom', width: '80', type: 'custom', tipmesg: '付款金额-刷单金额-发货前退款-发货后退款', formatter: (row) => !row.saleAmont ? " " : row.saleAmont },
    { istrue: true, summaryEvent: true, prop: 'oldSaleCost', label: '销售成本', sortable: 'custom', width: '80', style: (that, row) => that.renderCost(row), handle: (that, row) => that.showCost(row), formatter: (row) => !row.saleCost ? " " : row.oldSaleCost },
    { istrue: true, summaryEvent: true, prop: 'salePrice', label: '成本价', sortable: 'custom', width: '80', style: (that, row) => that.renderCost(row), handle: (that, row) => that.showCost(row), formatter: (row) => !row.saleCost   ? " " : row.salePrice },
    { istrue: true, summaryEvent: true, prop: 'dmSalePrice', label: '大马成本价', sortable: 'custom', width: '80', style: (that, row) => that.renderCost(row), handle: (that, row) => that.showCost(row), formatter: (row) => !row.saleCost  ? " " : row.dmSalePrice },
    { istrue: true, summaryEvent: true, prop: 'saleCost', label: '大马销售成本', sortable: 'custom', width: '80', style: (that, row) => that.renderCost(row), handle: (that, row) => that.showCost(row), formatter: (row) => !row.saleCost  ? " " : row.saleCost },
    
  ];
  const tableHandles = [
    { label: "导出", handle: (that) => that.$refs.table.setExportCols() },
    { label: "刷新", handle: (that) => that.onRefresh() },
  ];
  
  export default {
    name: "Users",
    components: {
      MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, productdrchart, InputMult, freightDetail, buschar, expressfreightanalysis, importmodule, ordergiftdetail, ProductADPDD, EveryDayrefund, inputYunhan, vxetablebase
      , YyGroupSelector, YyDirectorSelector, YhWarehouseSelector
    },
    data() {
      return {
        platformlist: platformlist,
        orderDayRpt_OrderTypes: orderDayRpt_OrderTypes,
        orderDayRpt_OrderStatuss: orderDayRpt_OrderStatuss,
        orderType: null,
        orderTypes: [],
        dialogConfirmdata: false,
        confirmDate: '',
        confirmDate2: '',
        searchloading: '',
        dialogCalDayRepotVis: false,
        calDayRepotyDate: null,
        that: this,
        filter: {
          ware: [],
          shopGoodsCode:null,
          isRefundAmontBefore: false,
          exitProfitUnZero: true,
          IsSpecialOrder: false,
          payAmountLessThan2: true,
          orderType: null,
          orderTypes: [],
          exceptOrderTypes: [],
          groupId: null,
          operateSpecialUserId: null,
          orderStatusS: ["已发货"],
          timeType: 1,
          orderType: null,
          orderNo: null,
          reportType: 1,
          platform: null,
          PlatFormss: null,
          OrderDayReportType: null,
          styleCode: null,
          goodsCode: null,
          startTime: null,
          bzCategory: null,//经营大类
          bzCategory1: null,//一级类目
          bzCategory2: null,//二级类目
          endTime: null,
          timerange: null,
          shopName: null,
          isChart: null,
          orderNoInner:null,
          goodsName:null,
          userId:null,
          userIds: [],//运营助理
          operateSpecialUserIds: [],//运营专员
          groupIds: [],//小组
          profit1UnZero:null,
          profit2UnZero:null,
          profit3UnZero:null,
          proCode:null,
          isdm:1,
        },
        filterList: {
          bussinessCategoryNames: [],//经营大类
          categoryName1s: [],//一级类目
          categoryName2s: []//二级类目
        },
        onimportfilter: {
          yearmonthday: null,
        },
        styleCode: null,
        shopList: [],
        userList: [],
        brandlist: [],
        grouplist: [],
        directorlist: [],
        financialreportlist: [],
        warehouselist:[],
        tableCols: tableCols,
        tableHandles: tableHandles,
        total: 0,
        pager: { OrderBy: " SaleAmont ", IsAsc: false },
        sels: [], // 列表选中列
        options: [],
        listLoading: false,
        pageLoading: false,
        earchloading: false,
        summaryarry: {},
        selids: [],
        fileList: [],
        dialogVisible: false,
        uploadLoading: false,
        importFilte: {},
        fileList: [],
        fileparm: {},
        editparmVisible: false,
        editLoading: false,
        editparmLoading: false,
        drawervisible: false,
        dialogDrVisible: false,
        expressfreightanalysisVisible: false,
        drparamProCode: '',
        autoformparm: {
          fApi: {},
          options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
          rule: []
        },
        freightDetail: {
          visible: false,
          filter: {
            proCode: null,
            timeRange: []
          },
        },
        EveryDayrefund: {
          visible: false,
          filter: {
            proCode: null,
            timeRange: [],
            afterSaleType: "2",
            orderStatus: "已取消",
            goodsStatus: "",
            timeRange1: [],
            platform: 4
          }
        },
        giftDetail: { visible: false },
        buscharDialog: { visible: false, title: "", data: {}, loading: false },
        drawervisible: false,
      };
    },
    async mounted() {
      this.init();
      middlevue.$on('toLinkTxDetailDayReportQuery',(data) =>{
          this.filter.timerange = [data.startDate,data.endDate]
          this.filter.orderNo = data.orderNo;
          this.filter.orderNoInner = data.orderNoInner;
          this.filter.startTime = data.startDate;
          this.filter.endTime = data.endDate;
          this.filter.exitProfitUnZero = false
          this.filter.payAmountLessThan2 = null
          console.log(this.filter,' this.filter');
          this.onSearch();
        })
     // this.filter.orderStatusS = filterStatus('已取消', this.orderDayRpt_OrderStatuss, this.filter.orderStatusS);
      //this.filter.orderTypes = filterStatus(["补发订单", "换货订单"], this.orderDayRpt_OrderTypes, this.filter.orderTypes);
     // this.filter.exceptOrderTypes = filterStatus(["普通订单", "分销Plus", "分销"], this.orderDayRpt_OrderTypes, this.filter.exceptOrderTypes);
    },
    beforeDestroy() {
      middlevue.$off('toLinkTxDetailDayReportQuery')
    },
    async created() {
      await this.getShopList();
      await this.initformparm();
      if (this.$route.query && this.$route.query.dayCount) {
  
        this.filter.noProfitDay = parseInt(this.$route.query.dayCount);
        this.filter.shopCode = this.$route.query.shopCode;
        this.filter.groupId = this.$route.query.groupId;
        this.filter.operateSpecialUserId = this.$route.query.operateSpecialUserId;
        let dateStr = this.$route.query.yearMonthDay.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
        this.filter.timerange = [dateStr, dateStr];
        this.filter.refundType = false;
        this.onSearch();
      }
    },
    methods: {
      //获取经营大类数据
      remoteMethodBusinessCategory(query) {
        setTimeout(async () => {
          const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 1, categoryName: query })
          this.filterList.bussinessCategoryNames = res.data
        }, 200)
      },
      //获取一级类目数据
      remoteMethodCategoryName1s(query) {
        setTimeout(async () => {
          const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 2, categoryName: query })
          this.filterList.categoryName1s = res.data
        }, 200)
      },
      //获取二级类目数据
      remoteMethodCategoryName2s(query) {
        setTimeout(async () => {
          const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 3, categoryName: query })
          this.filterList.categoryName2s = res.data
        }, 200)
      },
      //清除搜索选项内容
      clearaway() {
        this.filter.platform = null;
        this.filter.goodsCode = null;
        this.filter.orderNo = null;
        this.filter.orderNoInner = null;
        this.filter.orderStatusS = null;
        this.filter.orderTypes = [];
        this.filter.exceptOrderTypes = [];
        this.filter.proCode = null;
        this.filter.goodsName = null;
        this.filter.styleCode = null;
        this.filter.shopName = null;
        this.filter.groupId = null;
        this.filter.operateSpecialUserId = null;
        this.filter.userId = null;
        this.filter.profit1UnZero = null;
        this.filter.profit2UnZero = null;
        this.filter.profit3UnZero = null;
        this.filter.exitProfitUnZero = null;
        this.filter.payAmountLessThan2 = null;
        this.filter.isRefundAmontBefore = null;
        this.filter.IsSpecialOrder = null;
        this.filter.shopGoodsCode = null;
        this.filter.ware = [];
        this.$refs.YhWarehouseSelector.clearValue()
        this.$refs.YyGroupSelector.clearValue()
        this.$refs.YyDirectorSelector.clearValue()
        this.$refs.YyDirectorSelector1.clearValue()
      },
      async onchangeplatform(val) {
        this.shopList = []
        const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 1000 });
        this.shopList = res1.data.list
      },
      AllDetailDayReportArgument(e, No, Timerange) {
        if (Timerange) {
          this.filter.timerange = Timerange;
          if (this.filter.timerange) {
            this.filter.startTime = this.filter.timerange[0];
            this.filter.endTime = this.filter.timerange[1];
          }
        }
        if (No == 1) {
          this.filter.orderNo = e;
          this.filter.goodsCode = null;
          this.filter.styleCode = null;
          this.filter.shopName = null;
        }
        else if (No == 2.1) {
          this.filter.styleCode = e;
          this.filter.orderNo = null;
          this.filter.goodsCode = null;
          this.filter.shopName = null;
        }
        else if (No == 2.2) {
          this.filter.goodsCode = e;
          this.filter.styleCode = null;
          this.filter.orderNo = null;
          this.filter.shopName = null;
        }
        else if (No == 4) {
          this.filter.shopName = e;
          this.filter.orderNo = null;
          this.filter.goodsCode = null;
          this.filter.styleCode = null;
        }
        this.getList();
      },
      AllGoodCodeDayReportArgument(e) {
        this.filter.orderNoInner = e;
        this.getList();
      },
      cellClick(prms) {
        if (prms?.column?.field && prms?.column?.field === 'profit3IncreaseGoOnDays') {
          let row = prms.row;
          this.showprchart2(row.proCode, row.platform);
        }
  
      },
     
      datetostr(date) {
        var y = date.getFullYear();
        var m = ("0" + (date.getMonth() + 1)).slice(-2);
        var d = ("0" + date.getDate()).slice(-2);
        return y + "-" + m + "-" + d;
      },
      async init() {
        var date1 = new Date(); date1.setDate(date1.getDate() - 1);
        var date2 = new Date(); date2.setDate(date2.getDate() - 1);
        this.filter.timerange = [];
        this.filter.timerange[0] = this.datetostr(date1);
        this.filter.timerange[1] = this.datetostr(date2);
        console.log(this.filter)
        const res = await getAllWarehouse()
        if(!res.success) return
        var data = res.data.filter((x) => x.name.indexOf('代发') < 0)
        this.warehouselist = data
      },
      async showprchart2(prcode, platform) {
        window['lastseeprcodedrchart'] = prcode
        window['lastseeprcodedrchart1'] = platform
        window['lastseeprcodedrchart2'] = this.filter.refundType
        this.drparamProCode = prcode
        this.dialogDrVisible = true
      },
      async initformparm() {
        let that = this;
        this.autoformparm.rule = [{ type: 'hidden', field: 'id', title: 'id', value: '', col: { span: 12 } },
        { type: 'select', field: 'groupId', title: '组长', value: '', update(val, rule) { { that.updateruleGroup(val) } }, ...await ruleDirectorGroup(), props: { filterable: true } },
        { type: 'InputNumber', field: 'Profit3PredictRate', title: '毛三预估比例%', value: null, props: { min: 0, precision: 3 }, col: { span: 6 } },
        { type: 'InputNumber', field: 'ShareRate', title: '公摊费率%', value: null, props: { min: 0, precision: 3 }, col: { span: 6 } },
        ]
      },
      //系列编码远程搜索
      async remoteMethod(query) {
        if (query !== '') {
          this.searchloading == true;
          this.options = [];
          setTimeout(async () => {
            const res = await getListByStyleCode({ currentPage: 1, pageSize: 50, styleCode: query })
            console.log("系列编码远程搜索", res);
            this.searchloading = false
            res?.data?.forEach(f => {
              this.options.push({ value: f.styleCode, label: f.styleCode })
            });
          }, 200)
        }
        else {
          this.options = []
        }
      },
      async getShopList() {
        const res1 = await getAllShopList({ platforms: [2] });
        this.shopList = [];
        res1.data?.forEach(f => {
          if (f.isCalcSettlement && f.shopCode)
            this.shopList.push(f);
        });
      },
      async sortchange(column) {
        if (!column.order)
          this.pager = {};
        else
          this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
        await this.onSearch();
      },
      onRefresh() {
        this.onSearch()
      },
      async onSearch() {
        this.$refs.table.changecolumn_setTrue(["yearMonthDay"]);
        if (this.filter.groupType == 1 || this.filter.groupType == 2) {
          this.$refs.table.changecolumn(["yearMonthDay"]);
        }
        this.$refs.pager.setPage(1);
        await this.getList().then(res => { });
      },
      async getList() {
        this.filter.startTime = null;
        this.filter.endTime = null;
        if (this.filter.timerange) {
          this.filter.startTime = this.filter.timerange[0];
          this.filter.endTime = this.filter.timerange[1];
        }
        var that = this;
        var pager = this.$refs.pager.getPager();
        const params = { ...pager, ...this.pager, ...this.filter };
        startLoading();
        const res = await pageProductDayReport(params).then(res => {
          loading.close();
          if (res?.data?.list && res?.data?.list.length > 0) {
            for (var i in res.data.list) {
              if (!res.data.list[i].freightFee) {
                res.data.list[i].freightFee = " ";
              }
              if (that.filter.refundType) {
                res.data.list[i].RefundAmont = res.data.list[i].RefundAmontByPay;
                res.data.list[i].Profit3 = res.data.list[i].Profit3ByPay;
                res.data.list[i].Profit3Rate = res.data.list[i].Profit3RateByPay;
              }
            }
          }
          if (that.filter.refundType == true) {
            res.data.summary.Profit3Rate_sum = res.data?.summary?.Profit3RateByPay_sum;
            res.data.summary.RefundAmontByPay = res.data?.summary?.RefundAmontByPay_sum;
            res.data.summary.Profit3ByPay = res.data?.summary?.Profit3ByPay_sum;
          }
          that.total = res.data?.total;
          that.financialreportlist = res.data?.list;
          that.$refs.table.loadRowEcharts();
          that.summaryarry = res.data?.summary;
        });
      },
      showFreightDetail(row) {
        if (row.freightFee >= 1) {
          this.freightDetail.filter.proCode = row.proCode;
          if (row.yearMonthDay != null) {
            var dayStr = row.yearMonthDay.substr(0, 4) + '-' + row.yearMonthDay.substr(4, 2) + '-' + row.yearMonthDay.substr(6, 2)
            this.freightDetail.filter.timeRange = [dayStr, dayStr];
          }
          else {
            this.freightDetail.filter.timeRange = this.filter.timerange
          }
          this.freightDetail.visible = true;
          setTimeout(async () => {
            await this.$refs.freightDetail.onSearch();
          }, 100);
        }
      },
      showProcodesimilarity(row) {
        if (row.styleCode != null) {
          this.$router.push({ path: '/order/procodesimilarity', query: { styleCode: row.styleCode } })
        }
      },
      async showGiftDetail(row) {
        var yearMonthDayStart = row.yearMonthDay
        var yearMonthDayEnd = row.yearMonthDay
        if (this.filter.groupType) {
          yearMonthDayStart = this.filter.timerange[0].replace("-", "").replace("-", "")
          yearMonthDayEnd = this.filter.timerange[1].replace("-", "").replace("-", "")
        }
        this.giftDetail.visible = true;
        let _th = this;
        await this.$nextTick(async () => { await _th.$refs.ordergiftdetail.onShow(yearMonthDayStart, yearMonthDayEnd, row.proCode); });
      },
      selectchange: function (rows, row) {
        this.selids = [];
        rows.forEach(f => {
          this.selids.push(f.id);
        })
      },
      onRefresh() {
        this.onSearch()
      },
      async updateruleGroup(groupid) {
        if (!groupid)
          this.autoformparm.fApi.resetFields()
        else {
          const res = await getParm({ groupId: groupid })
          var arr = Object.keys(this.autoformparm.fApi);
          res.data.groupId = groupid;
          if (!res.data?.Profit3PredictRate) res.data.Profit3PredictRate = 0;
          if (!res.data?.ShareRate) res.data.ShareRate = 0;
          await this.autoformparm.fApi.setValue(res.data)
        }
      },
      
      async onExport(opt) {
        this.filter.startTime = null;
        this.filter.endTime = null;
        if (this.filter.timerange) {
          this.filter.startTime = this.filter.timerange[0];
          this.filter.endTime = this.filter.timerange[1];
        }
        this.filter.yH_EXT_ExportTitle = "大马美甲数据";
        var pager = this.$refs.pager.getPager();
        const params = { ...pager, ...this.pager, ...this.filter, ...opt };
        var res = await exportProductOrderDayReport(params);
        if (!res?.data) {
          return
        }
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '大马美甲导出数据' + new Date().toLocaleString() + '_.xlsx')
        aLink.click()
      },
      async onShowEditParm() {
        this.editparmVisible = true
        const res = await getParm()
        var arr = Object.keys(this.autoformparm.fApi);
        if (arr.length > 0)
          this.autoformparm.fApi.resetFields()
        await this.autoformparm.fApi.setValue(res.data)
      },
      async onSetEditParm() {
        this.editparmLoading = true;
        await this.autoformparm.fApi.validate(async (valid, fail) => {
          if (valid) {
            const formData = this.autoformparm.fApi.formData();
            const res = await setParm(formData);
            if (res.code == 1) this.editparmVisible = false;
          } else { }
        })
        this.editparmLoading = false;
      },
      async onsummaryClick(property) {
        this.filter.startTime = null;
        this.filter.endTime = null;
        if (this.filter.timerange) {
          this.filter.startTime = this.filter.timerange[0];
          this.filter.endTime = this.filter.timerange[1];
        }
        this.filter.isChart = 1;
        var pager = this.$refs.pager.getPager();
        const params = { ...pager, ...this.pager, ...this.filter };
        params.column = property;
        let that = this;
        that.listLoading = true;
        that.buscharDialog.loading = true;
        const res = await queryProductOrderDayReportSumChart(params).then(res => {
          that.buscharDialog.loading = false;
          that.buscharDialog.data = res.data
          that.buscharDialog.title = res.data.legend[0]
        });
        that.listLoading = false;
        that.buscharDialog.visible = true;
        this.$nextTick(async () => { await that.$refs.buschar.initcharts(); });
        this.filter.isChart = null;
      },
      showexpressfreightanalysis() {
        this.expressfreightanalysisVisible = true;
        this.$nextTick(() => { this.$refs.expressfreightanalysis.onSearch() });
      },
      renderCost(row) {
        if (row.replaceSendCost > 0) {
          return "color:blue;cursor:pointer;";
        }
        else {
          return "";
        }
      },
      showupload() {
        this.drawervisible = true;
      }
      , async callbackProCode(val) {
        // this.filter.proCode = val;
      },
    }
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  ::v-deep .el-select__tags-text {
    max-width: 70px;
  }
  </style>
  
  