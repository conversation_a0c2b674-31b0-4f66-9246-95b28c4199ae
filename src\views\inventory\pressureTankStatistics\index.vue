<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss"
                    style="width: 280px;" type="datetimerange" valueFormat="yyyy-MM-dd HH:mm:ss" />
                <el-select v-model="ListInfo.referrerList" multiple collapse-tags clearable filterable placeholder="推荐人"
                    class="publicCss">
                    <el-option v-for="(item) in hotSaleBrandPushNewCreatedUserNameList" :key="item.key"
                        :label="item.value" :value="item.key" />
                </el-select>
                <el-select v-model="ListInfo.positionList" clearable filterable placeholder="职位" multiple collapse-tags
                    class="publicCss">
                    <el-option v-for="item in createUserRoleList" :key="item" :label="item" :value="item" />
                </el-select>
                <el-select v-model="ListInfo.deptList" clearable filterable placeholder="架构" multiple collapse-tags
                    class="publicCss">
                    <el-option v-for="item in createUserDeptNameList" :key="item" :label="item" :value="item" />
                </el-select>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode"
                    class="publicCss" placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true"
                    :maxRows="500" :maxlength="1000000" @callback="proCodeCallback" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <el-input v-model.trim="ListInfo.goodsName" placeholder="产品名称" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.supplierName" placeholder="厂家名称" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.proIngredient" placeholder="产品成分" maxlength="50" clearable
                    class="publicCss" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' border
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="120">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="handleEdit(row.id)">编辑</el-button>
                            <el-button type="text" @click="openLogs(row.id)">日志</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="日志" :visible.sync="dialogHisVisible" width="70%" height="600px" v-dialogDrag>
            <pressureTankLog :id="id" v-if="dialogHisVisible" style="z-index:10000;height:600px" />
        </el-dialog>

        <el-dialog title="编辑" :visible.sync="editVisVisible" width="50%" height="600px" v-dialogDrag>
            <pressureTankEdit :id="id" @close="close" @getList="getList" v-if="editVisVisible"
                style="z-index:10000;height:600px" />
        </el-dialog>

        <el-dialog title="查看" :visible.sync="viewVisVisible" width="30%" v-dialogDrag>
            <pressureTankView :data="data" v-if="viewVisVisible" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import pressureTankLog from './components/pressureTankLog.vue'
import pressureTankView from './components/pressureTankView.vue'
import pressureTankEdit from './components/pressureTankEdit.vue'
import { GetPressureTankStatisticsPage, ExportPressureTankStatistics } from '@/api/operatemanage/pressureTankStatistics'
import { getHotSaleBrandPushNewCreatedUserNameList } from '@/api/operatemanage/productalllink/LogisticsAnalyse.js';
import { GetHotSaleBrandPushNewReportUsers, } from '@/api/operatemanage/productalllink/alllink'
import inputYunhan from "@/components/Comm/inputYunhan";
const tableCols = [
    { sortable: 'custom', width: '120', align: 'center', prop: 'auditTime', label: '审核通过时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'recommendUserName', label: '推荐人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'position', label: '职位', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'deptName', label: '架构', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'userNickName', label: '添加人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsName', label: '产品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsImgUrl', label: '产品图片', type: 'images' },
    {
        width: 'auto', align: 'center', prop: 'goodsCount', label: '厂家名称', type: 'button', btnList: [
            { label: '查看', handle: (that, row) => that.openView(row) }
        ]
    },
    {
        width: 'auto', align: 'center', prop: 'totalQuantity', label: '厂家链接', type: 'button', btnList: [
            { label: '查看', handle: (that, row) => that.openView(row) }
        ]
    },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'length', label: '长(cm)', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'width', label: '宽(cm)', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'height', label: '高(cm)', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight', label: '重量(kg)', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'ingredient', label: '产品成分', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'remark', label: '备注', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, pressureTankLog, pressureTankView, pressureTankEdit, inputYunhan
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                endDate: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            },
            hotSaleBrandPushNewCreatedUserNameList: [],
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            id: '',
            dialogHisVisible: false,
            editVisVisible: false,
            viewVisVisible: false,
            data: [],
            createUserRoleList: [],
            createUserDeptNameList: [],
        }
    },
    async mounted() {
        await this.getList()
        this.getSelectData()
        this.getHotSaleBrandPushNewCreatedUserNameList()
    },
    methods: {
        proCodeCallback(val) {
            this.ListInfo.goodsCode = val
        },
        async getSelectData() {
            let ret3 = await GetHotSaleBrandPushNewReportUsers({ getType: 3 });
            this.createUserRoleList = ret3.data;

            let ret4 = await GetHotSaleBrandPushNewReportUsers({ getType: 4 });
            this.createUserDeptNameList = ret4.data;
        },
        async getHotSaleBrandPushNewCreatedUserNameList() {
            this.hotSaleBrandPushNewCreatedUserNameList = [];
            const res = await getHotSaleBrandPushNewCreatedUserNameList();
            if (res?.success == true) {
                this.hotSaleBrandPushNewCreatedUserNameList = res.data;
            }
        },
        close() {
            this.editVisVisible = false
        },
        handleEdit(id) {
            this.id = id
            this.editVisVisible = true
        },
        openLogs(id) {
            this.id = id
            this.dialogHisVisible = true
        },
        openView(row) {
            this.data = JSON.parse(JSON.stringify(row.supplierList))
            this.viewVisVisible = true
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await ExportPressureTankStatistics(this.ListInfo)
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetPressureTankStatisticsPage(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 160px;
        margin: 0 5px 5px 0px;
    }
}
</style>
