<template>
    <my-container v-loading="pageLoading">

        <el-tabs v-model="activeName" style="height: calc(100% - 40px);">

            <el-tab-pane label="账户资金" name="tab1" style="height: 100%;">
                <pddfunddetail ref="pddfunddetail" />
            </el-tab-pane>
            <el-tab-pane label="站内信" name="tab2" style="height: 100%;" :lazy="true">
                <pddbgemail ref="pddbgemail" />
            </el-tab-pane>
            <el-tab-pane label="店铺违规管理" name="tab3" style="height: 100%;" :lazy="true">
                <pddbgviolation ref="pddbgviolation" />
            </el-tab-pane>
            <el-tab-pane label="店铺/商品违规" name="tab4" style="height: 100%;" :lazy="true">
                <pddbgshopviolation ref="pddbgshopviolation" />
            </el-tab-pane>
            <el-tab-pane label="违规申诉/整改" name="tab5" style="height: 100%;" :lazy="true">
                <pddbgcomplain ref="pddbgcomplain" />
            </el-tab-pane>
            <el-tab-pane label="直播违规" name="tab6" style="height: 100%;" :lazy="true">
                <pddbgliveviolation ref="pddbgliveviolation" />
            </el-tab-pane>
            <el-tab-pane label="售后服务违规" name="tab7" style="height: 100%;" :lazy="true">
                <pddbgaftermarketviolation ref="pddbgaftermarketviolation" />
            </el-tab-pane>
            <el-tab-pane label="推广费用" name="tab8" style="height: 100%;" :lazy="true">
                <pddpromotionfee ref="pddpromotionfee" />
            </el-tab-pane>
            <el-tab-pane label="消费者体验指标" name="tab9" style="height: 100%;" :lazy="true">
                <pddoptimizationopinion ref="pddoptimizationopinion" />
            </el-tab-pane> 
            <el-tab-pane label="客服数据" name="tab10" style="height: 100%;" :lazy="true">
                <pddcustomerpoint ref="pddoptimizationopinion" />
            </el-tab-pane>
        </el-tabs>

    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import pddfunddetail from "@/views/operatemanage/pddbgmanage/pddfunddetail.vue";
import pddbgemail from "@/views/operatemanage/pddbgmanage/pddbgemail.vue";
import pddbgshopviolation from "@/views/operatemanage/pddbgmanage/pddbgshopviolation.vue";
import pddbgcomplain from "@/views/operatemanage/pddbgmanage/pddbgcomplain.vue";
import pddbgliveviolation from "@/views/operatemanage/pddbgmanage/pddbgliveviolation.vue";
import pddbgaftermarketviolation from "@/views/operatemanage/pddbgmanage/pddbgaftermarketviolation.vue";
import pddbgviolation from "@/views/operatemanage/pddbgmanage/pddbgviolation.vue"
import pddpromotionfee from "@/views/operatemanage/pddbgmanage/pddpromotionfee.vue"
import pddoptimizationopinion from "@/views/operatemanage/pddbgmanage/pddoptimizationopinion.vue"
import pddcustomerpoint from "@/views/operatemanage/pddbgmanage/pddcustomerpoint.vue"

export default {
    name: "pddbgmanage",
    components: {
        MyContainer, pddfunddetail, pddbgemail, pddbgshopviolation, pddbgcomplain, pddbgliveviolation, pddbgaftermarketviolation, pddbgviolation,
        pddpromotionfee, pddoptimizationopinion,pddcustomerpoint
    },
    data() {
        return {
            activeName: 'tab1',
            that: this,
            pageLoading: false,
            importNumber: ''
        };
    },
    methods: {

    }
}

</script>