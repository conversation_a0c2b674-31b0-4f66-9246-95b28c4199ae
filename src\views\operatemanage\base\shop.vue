<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:calc(100% - 40px)">
            <el-tab-pane label="店铺列表" name="tab0" style="height: 100%;">
                <ShopList  ref="ShopList" style="height: 100%;"></ShopList>
            </el-tab-pane>
            <el-tab-pane label="店铺在线情况" v-if="checkPermission('homepermission')" :lazy="true" name="tab1" style="height:100%;">
                <ShopOnlineInfo ref="ShopOnlineInfo" style="height:100%"></ShopOnlineInfo>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import ShopList from "@/views/operatemanage/base/ShopList.vue";
import ShopOnlineInfo from "@/views/operatemanage/base/ShopOnlineInfo.vue";
import  checkPermission  from '@/utils/permission'

export default {
    name: "refundManage",
    components: { MyContainer, ShopList , ShopOnlineInfo },
    data() {
        return {
            that: this,
            activeName: 'tab0',
            pageLoading: false,
        };
    },
    async mounted() {
    },
    methods: {      
    },
}
</script>