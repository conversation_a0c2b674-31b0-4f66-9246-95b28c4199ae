<template>
    <MyContainer>
        <el-tabs v-model="activeName">
            <el-tab-pane label="供应商报价" name="first">
                <vendorQuote></vendorQuote>
            </el-tab-pane>
            <el-tab-pane label="供应商记录" name="second" lazy>
                <vendorRecording></vendorRecording>
            </el-tab-pane>
            <el-tab-pane label="编码类目" name="third" lazy>
                <vendorEncodeCategory></vendorEncodeCategory>
            </el-tab-pane>
            <el-tab-pane label="系列编码采购绑定" name="four" lazy>
                <ceccodebind></ceccodebind>
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vendorEncodeCategory from './vendorEncodeCategory.vue';
import vendorQuote from './vendorQuote.vue';
import vendorRecording from './vendorRecording.vue';
import ceccodebind from './ceccodebind.vue';


export default {
    components: {
        MyContainer, vendorEncodeCategory, vendorQuote, vendorRecording, ceccodebind
    },
    data() {
        return {
            activeName: 'first'
        };
    },
    methods: {

    }
};
</script>

<style lang="scss" scoped></style>