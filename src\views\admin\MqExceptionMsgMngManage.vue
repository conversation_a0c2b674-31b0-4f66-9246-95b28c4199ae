<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="MQ队列异常管理" name="tab0" style="height: 100%;">
                <MqExceptionMsgMng  ref="MqExceptionMsgMng" style="height: 100%;"></MqExceptionMsgMng>
            </el-tab-pane>
            <el-tab-pane label="MQ队列异常管理全部展示" name="tab1" style="height: 100%;">
                <MqExceptionMsgMngList ref="MqExceptionMsgMngList" style="height: 100%;"></MqExceptionMsgMngList>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MqExceptionMsgMngList from "@/views/admin/MqExceptionMsgMngList";
import MqExceptionMsgMng from "@/views/admin/MqExceptionMsgMng";

export default {
    name: "refundManage",
    components: { MyContainer, MqExceptionMsgMng , MqExceptionMsgMngList },
    data() {
        return {
            that: this,
            activeName: 'tab0',
            pageLoading: false,
        };
    },
    async mounted() {
    },
    methods: {
        async onSearch() {
            this.$nextTick(() => {
            })
        },
    },
}
</script>