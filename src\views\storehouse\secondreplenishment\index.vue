<template>
  <my-container v-loading="pageLoading">

      <el-tabs v-model="activeName" style="height: calc(100% - 40px);">
          <el-tab-pane label="补货记录" name="tab1" style="height: 100%;">
              <secondreplenishmentlist ref="secondreplenishmentlist" />
          </el-tab-pane>
          <el-tab-pane label="分析模块" name="tab2" style="height: 100%;" :lazy="true">
              <reportanalysis ref="report" />
          </el-tab-pane> 
      </el-tabs>

  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import secondreplenishmentlist from "@/views/storehouse/secondreplenishment/secondreplenishmentlist.vue";
import reportanalysis from "@/views/storehouse/secondreplenishment/reportanalysis.vue"; 

export default {
  name: "YunHanSecondReplenishmentIndex",
  components: {
      MyContainer, secondreplenishmentlist, reportanalysis
  },
  data() {
      return {
          activeName: 'tab1',
          that: this,
          pageLoading: false,
          importNumber: ''
      };
  },
  methods: {

  }
}

</script>