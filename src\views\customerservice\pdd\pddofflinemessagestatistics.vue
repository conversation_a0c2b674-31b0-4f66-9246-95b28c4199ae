<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :summaryarry="summaryarry" :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='offlineMessageList' @select='selectchange'
            :isSelection='false' :tableCols='tableCols' :loading="listLoading">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>
                <el-button style="padding: 0;margin: 0;">
                    <el-select style="float:left;width:120px;" v-model="Filter.PartitionID" placeholder="分区" clearable
                        filterable @change="handlePartitionChange" >
                        <el-option v-for="item in partitionList" :key="item.id" :label="item.partitionName"
                            :value="item.id" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-select style="float:left;" multiple v-model="Filter.GroupNames" placeholder="组名称" clearable
                        collapse-tags filterable>
                        <el-option v-for="item in grouplistsel" :key="item.groupname" :label="item.groupname"
                            :value="item.groupname" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <datepicker v-model="Filter.Sdate"></datepicker>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onExport" style="margin-left: 10px;">导出</el-button>
                <!-- <el-button type="primary" @click="onImport" style="margin-left: 10px;">导入</el-button> -->
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"
                @get-page="getOfflineMessageList" />
        </template>
        <el-dialog title="导入离线留言统计数据" :visible.sync="dialogImportVisible" width="30%" :close-on-click-modal="false"
            v-dialogDrag>
            <span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :http-request="uploadFile" :on-success="uploadSuccess">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <!-- <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload">
                        上传
                    </my-confirm-button> -->
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="onSubmitupload">
                        {{ (uploadLoading ? '上传中' : '上传') }}
                    </el-button>
                    <div slot="tip" class="el-upload__tip">只能上传xlsx文件</div>
                </el-upload>
                <el-button type="text" @click="downloadTemplate">下载导入模板</el-button>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogImportVisible = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
            :close-on-click-modal="true" v-dialogDrag>
            <!-- <div>
                <span> -->
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
                <!-- </span>
            </div> -->
            <!-- <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span> -->
        </el-dialog>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import { getPddGroup, pagePddOfflineMessageList, exportPddOfflineMessageList, importPddOfflineMessageAsync, getAllPartitions, getGroupNamesByPartitionId } from '@/api/customerservice/pddInquirsnew'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar';
import dayjs from "dayjs";
import { formatTime } from "@/utils";

import Decimal from 'decimal.js';
function precision(number, multiple) {
    return new Decimal(number).mul(new Decimal(multiple));
}

const tableCols = [
    { istrue: true, prop: 'groupName', label: '组名', width: '160', sortable: 'custom', type: "click", handle: (that, row, column, cell) => that.groupclick(row, column, cell), formatter: (row) => row.groupname },
    { istrue: true, prop: 'partitionName', label: '分区名称', width: '120', sortable: 'custom' },
    // { istrue: true, prop: 'shopName', label: '店铺', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'inquirs', label: '咨询人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'manualReply', label: '人工接待人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsCount', label: '询单人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'receiveCount', label: '最终成团人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'successpayrate', label: '询单转化率', width: '80', sortable: 'custom', 
        formatter: (row) => { return (row.successpayrate ? row.successpayrate : 0) + "%" } 
        // formatter: (row) => { return (row.successpayrate ? precision(row.successpayrate, 100) : 0) + "%" }
    },
    // { istrue: true, prop: 'offlineMessageCount', label: '离线留言数', width: '80', sortable: 'custom' },
    // { istrue: true, prop: 'offlineReplyCount', label: '已回复数', width: '80', sortable: 'custom' },
    // { istrue: true, prop: 'offlineReplyRate', label: '离线回复率', width: '80', sortable: 'custom', formatter: (row) => { return (row.offlineReplyRate ? precision(row.offlineReplyRate, 100).toFixed(2) : 0) + "%" } },
    { istrue: true, prop: 'threeSecondLost', label: '3分钟未回复人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'threeSecondGet', label: '3分钟回复人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'threeSecondReplyRate', label: '3分钟人工回复率', width: '90', sortable: 'custom', 
        formatter: (row) => { return (row.threeSecondReplyRate ? row.threeSecondReplyRate : 0) + "%" } 
        // formatter: (row) => { return (row.threeSecondReplyRate ? precision(row.threeSecondReplyRate, 100) : 0) + "%" }
    },
    { istrue: true, prop: 'responseTime', label: '均响(秒)', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'thirtySecondResponseRate', label: '30秒应答率', width: '80', sortable: 'custom', 
        formatter: (row) => { return (row.thirtySecondResponseRate ? row.thirtySecondResponseRate : 0) + "%" } 
        // formatter: (row) => { return (row.thirtySecondResponseRate ? precision(row.thirtySecondResponseRate, 100) : 0) + "%" }
    },
    { istrue: true, prop: 'outTimes', label: '出勤人次', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsPercentstr', label: '询单占比', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsPriceRate', label: '客单价', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'salesvol', label: '客服销售额(元)', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'serviceScore', label: '客服服务分', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'lowRatingOrderCount', label: '评分≤3订单数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'disputeRefundCount', label: '纠纷退款数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'complainCount', label: '投诉数', width: '80', sortable: 'custom' },
    // { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];
export default {
    name: "PddOfflineMessageStatistics",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar },
    props: ["partInfo", "filter"],
    data() {
        return {
            dialogMapVisible: { visible: false, title: "", data: [] },
            that: this,
            Filter: {
                Sdate: [formatTime(new Date(new Date().getTime() - 3600 * 1000 * 24 * 30), "YYYY-MM-DD 00:00:00"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")],
                // EnmPddGroupType: 11, // 默认售前组
                isOfflineMessage: 1, // 是否离线留言数据
            },
            shopList: [],
            userList: [],
            groupList: [],
            partitionList: [],
            offlineMessageList: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "inquirs", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogImportVisible: false,
            fileList: [],
            grouplistsel: [],
            isleavegroup: this.partInfo,//是否离组
            uploadLoading: false,
        };
    },
    watch: {
        partInfo() {
            this.isleavegroup = this.partInfo;
            this.setGroupSelect();
        },
        filter: {
            handler(val) {
                if (val) {
                    this.Filter = Object.assign(this.Filter, val);
                }
            },
            deep: true,
            immediate: true
        }
    },
    async mounted() {
        this.isleavegroup = this.partInfo;
        await this.getPartitionList();
        await this.setGroupSelect();
        this.getOfflineMessageList();
    },
    methods: {
        async getPartitionList() {
            try {
                const res = await getAllPartitions({ EnmPddGroupType: 11 });
                if (res.success) {
                    this.partitionList = res.data;
                }
            } catch (error) {
                console.error("获取分区列表失败", error);
            }
        },
        // async handlePartitionChange() {
        //     if (!this.Filter.PartitionID) {
        //         return;
        //     }
        //     try {
        //         const res = await getGroupNamesByPartitionId(this.Filter.PartitionID);
        //         if (res.success) {
        //             this.grouplistsel = res.data;
        //         }
        //     } catch (error) {
        //         console.error("获取分组列表失败", error);
        //     }
        // },
        async setGroupSelect() {
            try {
                const form = new FormData();
                form.append("enmPddGroupType", 11);
                form.append("isleavegroup", this.isleavegroup);
                const res = await getPddGroup(form);
                // const res = await getPddGroup({ enmPddGroupType: 11, isleavegroup: this.isleavegroup });
                if (res.success) {
                    this.grouplistsel = res.data;
                }
            } catch (error) {
                console.error("获取分组列表失败", error);
            }
        },
        async getOfflineMessageList(pager) {
            this.listLoading = true;
            if (pager) {
                this.pager.PageIndex = pager.pageIndex;
                this.pager.PageSize = pager.pageSize;
            } else {
                this.pager.PageIndex = 1;
                this.pager.PageSize = 20;
            }

            try {
                const params = {
                    ...this.Filter,
                    ...this.pager,
                    StartSdate: this.Filter.Sdate ? this.Filter.Sdate[0] :'',
                    EndSdate: this.Filter.Sdate ? this.Filter.Sdate[1] : '',
                };

                const res = await pagePddOfflineMessageList(params);
                if (res.list) {
                    this.offlineMessageList = res.list;
                    this.total = res.total;
                    this.summaryarry = res.summary;
                } else {
                    this.$message.error(res.msg);
                }
            } catch (error) {
                console.error("获取离线留言统计列表失败", error);
            } finally {
                this.listLoading = false;
            }
        },
        async showchart(row) {
            var params = {
                GroupNames: [row.groupName],
                StartSdate: this.Filter.Sdate[0],
                EndSdate: this.Filter.Sdate[1],
                EnmPddGroupType: this.Filter.EnmPddGroupType,
                Position: '售前'
            };

            // 这里需要定义趋势图API
            // const res = await pagePddOfflineMessageListMap(params);
            // this.dialogMapVisible.visible = true;
            // this.dialogMapVisible.data = res;
            // this.dialogMapVisible.title = res.title || "离线留言趋势图";
            this.$message.info("暂未实现趋势图功能");
        },
        sortchange(column) {
            if (!column.order) {
                this.pager = { PageIndex: 1, PageSize: 20 };
            } else {
                this.pager = {
                    OrderBy: column.prop,
                    IsAsc: column.order.indexOf("descending") == -1 ? true : false,
                    PageIndex: 1,
                    PageSize: 20
                };
            }
            this.getOfflineMessageList();
        },
        selectchange(rows) {
            this.sels = rows;
        },
        onSearch() {
            this.getOfflineMessageList();
        },
        async onExport() {
            this.pageLoading = true;
            try {
                const params = {
                    ...this.Filter,
                    StartSdate: null != this.Filter.Sdate ? this.Filter.Sdate[0] : null,
                    EndSdate: null != this.Filter.Sdate ? this.Filter.Sdate[1] : null,
                };
              this.listLoading = true
              const res = await exportPddOfflineMessageList(params);
              this.listLoading = false
              if (!res?.data) return
              const aLink = document.createElement("a");
              let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
              aLink.href = URL.createObjectURL(blob)
              aLink.setAttribute('download', '离线留言统计_' + new Date().toLocaleString() + '.xlsx');
              aLink.click()
            } catch (error) {
                console.error("导出离线留言统计失败", error);
                this.$message.error('导出失败');
            } finally {
                this.pageLoading = false;
            }
        },
        onImport() {
            this.dialogImportVisible = true;
            this.uploadLoading = false;
        },
        async onSubmitupload() {
            this.$refs.upload.submit();
        },
      downloadTemplate() {
        const downloadFile = async () => {
          const url = "https://nanc.yunhanmy.com:10010/media/video/20250411/1910585183240970240.xlsx";
          const fileName = '拼多多离线留言模板.xlsx';

          try {
            // 1. 发起跨域请求获取文件内容
            const response = await fetch(url);
            const blob = await response.blob();

            // 2. 创建临时 URL 并触发下载
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 3. 释放临时 URL
            URL.revokeObjectURL(link.href);
          } catch (error) {
            console.error('下载失败:', error);
          }
        };

        // 调用下载函数
        downloadFile();
        },
        uploadFile(options) {
            this.pageLoading = true;
            const formData = new FormData();
            formData.append('upfile', options.file);
            formData.append('pddGroup', this.Filter.EnmPddGroupType);

            importPddOfflineMessageAsync(formData).then(res => {
                this.pageLoading = false;
                if (res.success) {
                    this.$message.success('导入成功');
                    this.dialogImportVisible = false;
                    this.getOfflineMessageList();
                }
            });
        },
        uploadSuccess(response, file, fileList) {
            if (response.success) {
                this.$message.success('导入成功');
                this.getOfflineMessageList();
            } else {
                this.$message.error(response.msg || '导入失败');
            }
        },
        groupclick(row) {
            // 如果需要点击组名后进行跳转等操作
            console.log("组名点击:", row);

            if (this.Filter.Sdate) {
                this.Filter.startSdate = this.Filter.Sdate[0];
                this.Filter.endSdate = this.Filter.Sdate[1];
            }
            else {
                this.Filter.startSdate = null;
                this.Filter.endSdate = null;
            }

            window.showpddpddlist(row.groupName, this.Filter.startSdate == null ? "" : this.Filter.startSdate
                , this.Filter.endSdate == null ? "" : this.Filter.endSdate)

            window.showpddtab4()
        },
        async handlePartitionChange(partitionId) {
            if (partitionId) {
            try {
                const res = await getGroupNamesByPartitionId({ partitionId });
                if (res.data && res.data.length > 0) {
                    this.grouplistsel = Array.from(
                        new Set(res.data.map(item => item.groupname))
                        ).map(groupname => ({ groupname }));
                } else {
                    this.grouplistsel = [];
                }
                this.grouplistsel = this.grouplistsel.filter(item => item.groupname.includes("离线"));
                // 清空已选择的组名
                this.Filter.GroupNames = [];
            } catch (error) {
                console.error('获取分区下组名失败', error);
            }
            } else {
                // 如果清空分区选择，则获取所有组
                await this.setGroupSelect();
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
