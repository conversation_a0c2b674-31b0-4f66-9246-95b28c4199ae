<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-input v-model="ListInfo.goodsCode" placeholder="请输入商品编码" maxlength="30" clearable
                    style="width: 220px;margin-right: 10px;"></el-input>
                <el-button type="primary" @click="getList('search')">查询</el-button>
                <el-button type="primary" v-if="checkPermission('costSettings')"
                    @click="handleAdd(true, true, true)">成本设置</el-button>
                <el-button type="primary" @click="handleAddProp">新增</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading" :height="'99%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
        <!-- 阳光隔热膜设置 -->
        <el-dialog title="成本设置" :visible.sync="drawer" width="60%" :close-on-click-modal="false" v-dialogDrag>
            <div>
                <el-input v-model="setQuery.goodsCode" placeholder="请输入商品编码" maxlength="30" clearable
                    style="width: 220px;margin-right: 10px;"></el-input>
                <el-button type="primary" @click="handleAdd(true, true)">查询</el-button>
            </div>
            <el-table :data="formData" style="width: 95%;height:100%" max-height="400" v-loading="setLoading">
                <el-table-column label="#" type="index" width="40" />
                <el-table-column prop="norms" label="规格" width="auto">
                    <template #header="{ column }">
                        <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>规格
                    </template>
                    <template #default="{ row, $index }">
                        <el-input v-model="row.norms" placeholder="规格" maxlength="20" />
                    </template>
                </el-table-column>
                <el-table-column prop="hasBaseType" label="底色" width="auto">
                    <template #default="{ row }">
                        <el-input v-model="row.hasBaseType" placeholder="底色" maxlength="20" />
                    </template>
                </el-table-column>
                <el-table-column prop="colorType" label="色号" width="auto">
                    <template #default="{ row }">
                        <el-input v-model="row.colorType" placeholder="色号" maxlength="20" />
                    </template>
                </el-table-column>
                <el-table-column prop="kgPriceCost" label="公斤成本" width="auto">
                    <template #default="{ row, $index }">
                        <div class="publicFlex">
                            <el-input-number v-model="row.kgPriceCost" :max="10000" :precision="3" :controls="false"
                                placeholder="公斤成本" class="iptCss" :min="0" />
                            <el-button type="text" @click="batchEdit('kgPriceCost', $index, 1)">批量</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="croppingCost" label="裁剪费" width="auto">
                    <template #default="{ row, $index }">
                        <div class="publicFlex">
                            <el-input-number v-model="row.croppingCost" :max="10000" :precision="3" :controls="false"
                                placeholder="裁剪费" class="iptCss" :min="0" />
                            <el-button type="text" @click="batchEdit('croppingCost', $index, 1)">批量</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="packProcessCost" label="打包费" width="auto">
                    <template #default="{ row, $index }">
                        <div class="publicFlex">
                            <el-input-number v-model="row.packProcessCost" :max="10000" :precision="3" :controls="false"
                                placeholder="打包费" class="iptCss" :min="0" />
                            <el-button type="text" @click="batchEdit('packProcessCost', $index, 1)">批量</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="bubbleBagCost" label="气泡袋" width="auto">
                    <template #default="{ row, $index }">
                        <div class="publicFlex">
                            <el-input-number v-model="row.bubbleBagCost" :max="10000" :precision="3" :controls="false"
                                placeholder="气泡袋" class="iptCss" :min="0" />
                            <el-button type="text" @click="batchEdit('bubbleBagCost', $index, 1)">批量</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="spongeBangCost" label="海绵棒" width="auto">
                    <template #default="{ row, $index }">
                        <div class="publicFlex">
                            <el-input-number v-model="row.spongeBangCost" :max="10000" :precision="3" :controls="false"
                                placeholder="海绵棒" class="iptCss" :min="0" />
                            <el-button type="text" @click="batchEdit('spongeBangCost', $index, 1)">批量</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="goodsCode" label="商品编码" width="auto">
                    <template #default="{ row }">
                        <el-tooltip class="item" effect="dark" :content="row.goodsCode" placement="top">
                            <el-input v-model="row.goodsCode" maxlength="30" placeholder="商品编码" class="iptCss" />
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column prop="sheetSquareSaleAmount" label="操作" width="auto" v-if="isCbSet">
                    <template #default="{ row, $index }">
                        <el-button type="danger" @click="handleDel(row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div style="margin-top: 40px;">
                <my-pagination ref="pager1" :total="total1" @page-change="Pagechange1" @size-change="Sizechange1"
                    v-if="drawer" />
            </div>
            <div class="btnGroup">
                <el-button style="margin-right: 10px;" @click="drawer = false">取消</el-button>
                <el-button type="primary" @click="submit(1)" v-throttle="3000">保存</el-button>
            </div>
        </el-dialog>

        <el-dialog title="新增" :visible.sync="addvisible" width="60%" :close-on-click-modal="false" v-dialogDrag>
            <el-button type="text" @click="addProps">新增一行</el-button>
            <el-table :data="formData1" style="width: 95%;height:95%" max-height="400" v-if="addvisible">
                <el-table-column label="#" type="index" width="40" />
                <el-table-column prop="norms" label="规格" width="auto">
                    <template #header="{ column }">
                        <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>规格
                    </template>
                    <template #default="{ row, $index }">
                        <el-input v-model="row.norms" placeholder="规格" maxlength="20" />
                    </template>
                </el-table-column>
                <el-table-column prop="hasBaseType" label="底色" width="auto">
                    <template #default="{ row }">
                        <el-input v-model="row.hasBaseType" placeholder="底色" maxlength="20" />
                    </template>
                </el-table-column>
                <el-table-column prop="colorType" label="色号" width="auto">
                    <template #default="{ row }">
                        <el-input v-model="row.colorType" placeholder="色号" maxlength="20" />
                    </template>
                </el-table-column>
                <el-table-column prop="kgPriceCost" label="公斤成本" width="auto">
                    <template #default="{ row, $index }">
                        <div class="publicFlex">
                            <el-input-number v-model="row.kgPriceCost" :max="10000" :precision="3" :controls="false"
                                placeholder="公斤成本" class="iptCss" :min="0" />
                            <el-button type="text" @click="batchEdit('kgPriceCost', $index, 2)">批量</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="croppingCost" label="裁剪费" width="auto">
                    <template #default="{ row, $index }">
                        <div class="publicFlex">
                            <el-input-number v-model="row.croppingCost" :max="10000" :precision="3" :controls="false"
                                placeholder="裁剪费" class="iptCss" :min="0" />
                            <el-button type="text" @click="batchEdit('croppingCost', $index, 2)">批量</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="packProcessCost" label="打包费" width="auto">
                    <template #default="{ row, $index }">
                        <div class="publicFlex">
                            <el-input-number v-model="row.packProcessCost" :max="10000" :precision="3" :controls="false"
                                placeholder="打包费" class="iptCss" :min="0" />
                            <el-button type="text" @click="batchEdit('packProcessCost', $index, 2)">批量</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="bubbleBagCost" label="气泡袋" width="auto">
                    <template #default="{ row, $index }">
                        <div class="publicFlex">
                            <el-input-number v-model="row.bubbleBagCost" :max="10000" :precision="3" :controls="false"
                                placeholder="气泡袋" class="iptCss" :min="0" />
                            <el-button type="text" @click="batchEdit('bubbleBagCost', $index, 2)">批量</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="spongeBangCost" label="海绵棒" width="auto">
                    <template #default="{ row, $index }">
                        <div class="publicFlex">
                            <el-input-number v-model="row.spongeBangCost" :max="10000" :precision="3" :controls="false"
                                placeholder="海绵棒" class="iptCss" :min="0" />
                            <el-button type="text" @click="batchEdit('spongeBangCost', $index, 2)">批量</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="goodsCode" label="商品编码" width="auto">
                    <template #default="{ row }">
                        <el-tooltip class="item" effect="dark" :content="row.goodsCode" placement="top">
                            <el-input v-model="row.goodsCode" maxlength="30" placeholder="商品编码" class="iptCss" />
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column prop="sheetSquareSaleAmount" label="操作" width="auto">
                    <template #default="{ row, $index }">
                        <el-button type="danger" @click="formData1.splice($index, 1)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="btnGroup">
                <el-button style="margin-right: 10px;" @click="addvisible = false">取消</el-button>
                <el-button type="primary" @click="submit(2)" v-throttle="3000">保存</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { getCostSetByPZD, updateSetByPZD, delSetByPZD, addSetByPZD } from "@/api/inventory/customNormsGoods";
const ys = {
    norms: '规格',
    kgPriceCost: '公斤成本',
    croppingCost: '裁剪费',
    packProcessCost: '打包费',
    bubbleBagCost: '气泡袋',
    spongeBangCost: '海绵棒',
    goodsCode: '商品编码',
}
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'norms', label: '规格', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'hasBaseType', label: '底色', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'colorType', label: '色号', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'kgPriceCost', label: '公斤成本' },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'croppingCost', label: '裁剪费', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'packProcessCost', label: '打包费', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'bubbleBagCost', label: '气泡袋', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'spongeBangCost', label: '海绵棒', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            ys,
            that: this,
            ListInfo: {
                orderBy: 'id',
                isAsc: false,
                currentPage: 1,
                pageSize: 50
            },
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            drawer: false,
            formData: [
                {
                    norms: null,
                    hasBaseType: null,
                    colorType: null,
                    kgPriceCost: null,
                    croppingCost: null,
                    packProcessCost: null,
                    bubbleBagCost: null,
                    spongeBangCost: null,
                    goodsCode: null,
                }
            ],
            formData1: [{
                norms: null,
                hasBaseType: null,
                colorType: null,
                kgPriceCost: null,
                croppingCost: null,
                packProcessCost: null,
                bubbleBagCost: null,
                spongeBangCost: null,
                goodsCode: null,
            }],
            isCbSet: true,
            addvisible: false,
            setQuery: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'id',
                isAsc: false,
            },
            setLoading: false
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        handleAddProp() {
            this.formData1 = [{
                norms: null,
                hasBaseType: null,
                colorType: null,
                kgPriceCost: undefined,
                croppingCost: undefined,
                packProcessCost: undefined,
                bubbleBagCost: undefined,
                spongeBangCost: undefined,
                goodsCode: null,

            }],
                this.addvisible = true
        },
        handleDel(row) {
            this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await delSetByPZD({ ids: [row.id] })
                if (!success) return
                this.handleAdd(true)
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        batchEdit(key, index, val) {
            const map = {
                kgPriceCost: '公斤成本',
                croppingCost: '裁剪费',
                packProcessCost: '打包费',
                bubbleBagCost: '气泡袋',
                spongeBangCost: '海绵棒',
            }
            let num = 0
            if (val == 1) {
                num = this.formData[index][key]
            } else {
                num = this.formData1[index][key]
            }
            this.$confirm(`此操作将批量修改${map[key]}, 是否继续?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                if (val == 1) {
                    this.formData.forEach((item, i) => {
                        if (i > index) {
                            item[key] = num
                        }
                    })
                } else {
                    this.formData1.forEach((item, i) => {
                        if (i > index) {
                            item[key] = num
                        }
                    })
                }
                this.$message({
                    type: 'success',
                    message: `批量修改${map[key]}成功!`
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消修改'
                });
            });
        },
        validate(val) {
            const arr = ['norms', 'kgPriceCost', 'croppingCost', 'packProcessCost', 'bubbleBagCost', 'spongeBangCost', 'goodsCode']
            const numberArr = ['kgPriceCost', 'croppingCost', 'packProcessCost', 'bubbleBagCost', 'spongeBangCost']
            if (val == 1) {
                this.formData.forEach((item, i) => {
                    for (const key in item) {
                        //根据key的值,提示对应的字段
                        if (arr.includes(key) && (item[key] === null || item[key] === '' || item[key] === undefined)) {
                            this.$message.error(`第${i + 1}行${ys[key]}为空的数据,请检查`)
                            throw new Error(`第${i + 1}行${ys[key]}为空的数据,请检查`)
                        }
                        //如果是公斤成本 裁剪费 打包费 气泡袋 海绵棒
                        if (numberArr.includes(key)) {
                            if (item[key] < 0) {
                                this.$message.error(`第${i + 1}行${ys[key]}小于0的数据,请检查`)
                                throw new Error(`第${i + 1}行${ys[key]}小于0的数据,请检查`)
                            }
                        }
                    }
                })
            } else {
                this.formData1.forEach((item, i) => {
                    for (const key in item) {
                        //根据key的值,提示对应的字段
                        if (arr.includes(key) && (item[key] === null || item[key] === '' || item[key] === undefined)) {
                            this.$message.error(`第${i + 1}行${ys[key]}为空的数据,请检查`)
                            throw new Error(`第${i + 1}行${ys[key]}为空的数据,请检查`)
                        }
                        //如果是公斤成本 裁剪费 打包费 气泡袋 海绵棒
                        if (numberArr.includes(key)) {
                            if (item[key] < 0) {
                                this.$message.error(`第${i + 1}行${ys[key]}小于0的数据,请检查`)
                                throw new Error(`第${i + 1}行${ys[key]}小于0的数据,请检查`)
                            }
                        }
                    }
                })
            }
        },
        async submit(val) {
            this.validate(val)
            if (val == 1) {
                if (!this.formData || this.formData.length == 0) {
                    return this.$message.error('当前页没有数据,请核实!')
                }
            } else {
                if (!this.formData1 || this.formData1.length == 0) {
                    return this.$message.error('请至少添加一行数据!')
                }
            }
            if (val == 1) {
                console.log(this.formData, 'this.formData');
                const { success } = await updateSetByPZD(this.formData)
                if (success) {
                    await this.getList()
                    this.drawer = false
                    this.$message.success('保存成功')
                }
            } else {
                const { success } = await addSetByPZD(this.formData1)
                if (success) {
                    await this.getList()
                    this.addvisible = false
                    this.$message.success('保存成功')
                }
            }

        },
        addProps(val) {
            this.formData1.push({
                norms: null,
                hasBaseType: null,
                colorType: null,
                kgPriceCost: undefined,
                croppingCost: undefined,
                packProcessCost: undefined,
                bubbleBagCost: undefined,
                spongeBangCost: undefined,
                goodsCode: null,
            })
        },
        async handleAdd(isCbSet, first, isInit) {
            this.drawer = true
            if (first) {
                this.setQuery.currentPage = 1
                this.$nextTick(() => {
                    this.$refs.pager1.setPage(1);
                })
            }
            if (isInit) {
                this.setQuery = {
                    currentPage: 1,
                    pageSize: 50,
                    orderBy: 'id',
                    isAsc: false,
                    goodsCode: ''
                }
                this.$nextTick(() => {
                    this.$refs.pager1.setPage(1);
                })
            }
            this.setLoading = true
            this.isCbSet = isCbSet
            try {
                const { data, success } = await getCostSetByPZD(this.setQuery)
                if (success) {
                    this.formData = data.list
                    this.total1 = data.total
                } else {
                    this.$message.error('获取数据失败')
                    this.drawer = true
                }
            } catch (error) {
                console.log(error);
            } finally {
                this.setLoading = false;
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            const { data, success } = await getCostSetByPZD(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.loading = false
            } else {
                //获取列表失败
                this.loading = false
                this.$message.error('获取数据失败')
            }
        },
        //每页数量改变
        Sizechange1(val) {
            this.setQuery.currentPage = 1;
            this.setQuery.pageSize = val;
            this.handleAdd(true)
        },
        //当前页改变
        Pagechange1(val) {
            this.setQuery.currentPage = val;
            this.handleAdd(true)
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.iptCss {
    width: 70px;
}

.btnGroup {
    margin-top: 10px;
    display: flex;
    justify-content: end;
}

::v-deep .el-table__body-wrapper {
    min-height: 300px !important;
    max-height: 400px !important;
}

::v-deep .cell {
    padding-left: 3px;
}

.publicFlex {
    display: flex;
    align-items: center;
}
</style>
