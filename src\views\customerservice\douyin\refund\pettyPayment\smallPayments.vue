<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 10px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.proCode" placeholder="商品ID" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable class="publicCss" />
        <el-select v-model="ListInfo.batchStr" placeholder="批次号" class="publicCss" clearable>
          <el-option v-for="item in pettyPaymentList" :key="item.batchStr" :label="item.batchStr"
            :value="item.batchStr">
          </el-option>
        </el-select>
        <el-button type="primary" @click="onSearch">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
      </div>
    </template>
    <vxetablebase :id="'smallPayments202408041525'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :summaryarry='summaryarry' :showsummary='true'
      :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @get-page="getList" />
    </template>

    <el-dialog title="导入小额打款数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title='小额打款店铺' :visible.sync="productidpopup" width="65%" v-dialogDrag>
      <div style="height: 500px;">
        <productIdModule ref="productIdModule" v-if="productidpopup" />
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import productIdModule from "./productIdModule.vue"
import { getPettyPayment_DYPageList, importPettyPayment_DYAsync } from '@/api/customerservice/douyinrefund'
import dayjs from 'dayjs'
const tableCols = [
  { istrue: true, prop: 'proCode', label: '商品ID', width: '200', align: 'center', type: 'click', handle: (that, row) => that.onproduct(row), },
  { istrue: true, prop: 'proName', label: '商品名称', width: '300', align: 'center', sortable: 'custom' },
  {
    istrue: true, prop: 'sumOrderCount', label: '小额打款单量-占比', width: '200', align: 'center', sortable: 'custom',
    formatter: (row) => { return row.sumOrderCount + '/' + (row.pettyPaymentCountRate * 100).toFixed(2) + '%' }
  },
  {
    istrue: true, prop: 'sumAmount', label: '金额-占比', width: '200', align: 'center', sortable: 'custom',
    formatter: (row) => { return row.sumAmount + '/' + (row.pettyPaymentAmountRate * 100).toFixed(2) + '%' }
  },
]
export default {
  name: "index",
  components: {
    MyContainer, vxetablebase, productIdModule
  },
  props: ["batchStrBaseList"],
  data() {
    return {
      pettyPaymentList: [],
      dialogVisible: false,
      fileList: [],
      fileparm: {},
      uploadLoading: false,
      productidpopup: false,
      that: this,
      ListInfo: {
        startDate: null,//开始时间
        endDate: null,//结束时间
        proCode: null,//商品ID
        goodsName: null,//商品名称
        batchStr: null,//批次号
      },
      pager: { OrderBy: "", IsAsc: false },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      summaryarry: {},//汇总
      pickerOptions,
    }
  },
  async mounted() {
    this.pettyPaymentList = this.batchStrBaseList
    await this.onSearch()
  },
  methods: {
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importPettyPayment_DYAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    onproduct(row) {
      const params = { ...this.ListInfo, ...row }
      this.productidpopup = true
      this.$nextTick(() => {
        this.$refs.productIdModule.getList('search', params)
      })
    },
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getList()
    },
    async changeTime(e) {
      this.ListInfo.startDate = e ? e[0] : null
      this.ListInfo.endDate = e ? e[1] : null
      this.timeRanges = e
      this.batchScreening()
    },
    batchScreening(){
      this.pettyPaymentList = []
      this.pettyPaymentList = this.batchStrBaseList.filter(item => {
        let createdTime = new Date(item.createdTime).getTime(); // 将 createdTime 转换为时间戳
        let startTime = new Date(this.timeRanges[0] + ' 00:00:00').getTime(); // 将 timeRanges 的开始时间转换为时间戳
        let endTime = new Date(this.timeRanges[1] + ' 23:59:59').getTime(); // 将 timeRanges 的结束时间转换为时间戳
        return createdTime >= startTime && createdTime <= endTime; // 判断 createdTime 是否在时间区间内
      });
    },
    async getList() {
      if (this.timeRanges.length == 0) {
        this.ListInfo.startDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
        this.batchScreening()
      }
      const replaceArr = ['proCode'] //替换空格的方法,该数组对应str类型的input双向绑定的值
      var pager = this.$refs.pager.getPager()
      this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
      this.loading = true
      const params = { ...pager, ...this.pager, ... this.ListInfo }
      // 使用时将下面的方法替换成自己的接口
      const { data, success } = await getPettyPayment_DYPageList(params)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}
</style>
