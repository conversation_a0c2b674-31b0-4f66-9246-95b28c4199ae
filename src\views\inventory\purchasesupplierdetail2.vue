<template>
  <container v-loading="pageLoading">
      <!-- :tableHandles='tableHandles'  -->
     <ces-table ref="table" :that='that' :isIndex='true' @sortchange='sortchange' @select='selectchange' @cellclick='cellclick' 
         :hasexpand='true' :tableData='list' :tableCols='tableCols' :loading="listLoading"
         :showsummary='true' :summaryarry='summaryarry'>
      
         <template slot='extentbtn'>
      <el-button-group>
     
        
    <el-button type="primary" @click="startImport">导入采购退货出库</el-button>
    <el-button type="primary" @click="downloadTemplate">采购退货出库-模板</el-button>
    <el-button type="primary" @click="PurchaseReturnButton">批次号删除</el-button> 
    <el-button type="primary" @click="onExport">导出</el-button>
    <el-button type="primary" @click="onSearch">刷新</el-button> 
        <el-button style="padding: 0;margin: 0;">
      
        <el-input v-model.trim="filter.SuName" clearable style="width: 150px" placeholder="供应商"/>
      </el-button>
       <el-button style="padding: 0;margin: 0;">
         <el-date-picker style="width: 300px" v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至" start-placeholder="入库开始日期" end-placeholder="入库结束日期"></el-date-picker>
       </el-button>
    <el-button type="primary" @click="onSearch">查询</el-button>
      </el-button-group>
       </template>
      </ces-table>
      
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
   
     <el-drawer title="编辑供应商明细" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editVisible" 
                direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
       <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
       <div class="drawer-footer">
        <el-button @click.native="editVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="editLoading" @click="onEditSubmit" />
      </div>
    </el-drawer>
   
   <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%">
      <span>
        <el-upload ref="upload" class="upload-demo"
          :auto-upload="false" :multiple="false" action accept=".xlsx"
          :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}   </el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
<el-dialog title="采购退货出库按批次号删除" :visible.sync="dialogdeletebatchNumberVisible" height="300px" v-dialogDrag>
      <el-row>
      
      </el-row>
       <el-row>
          <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="24">
           
             <purchaseReturnGoods ref="purchaseReturnGoods" :filter="purchaseReturnGoods.filter" style="height:600px;"></purchaseReturnGoods>
          </el-col>
       
      </el-row>
     
  </el-dialog>
 

  </container>
</template>
<script>
import {queryPurchaseOrderDetail,importPurchaseReturnGoods,getpurchaseReturnGoodsList,deletePurchaseReturnGoods} from '@/api/inventory/purchase'
import {getAllSupplier,SupplierAccountsDetail,editSupplierAccountsDetail,exportSupplierAccountsDetail} from '@/api/inventory/supplier'
import {formatTime,formatYesornoBool,formatWarehouseArea,formatIsOutStock,formatSecondToHour} from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import logistics from '@/components/Comm/logistics'
import goodscoderecord from '@/views/inventory/goodscoderecord'
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";
import purchasehistorytjanalysis from '@/views/inventory/components/purchasehistorytjanalysis'
import purchaseReturnGoods from '@/views/inventory/purchaseReturnGoods'
const tableCols =[
   
      {istrue:true,prop:'supplierName',label:'供应商名称', width:'150',sortable:'custom'},
      {istrue:true,prop:'beginAmount',label:'期初余额', width:'100',sortable:'custom'},
      {istrue:true,prop:'inKuAmount',label:'入库金额', width:'100',sortable:'custom'},
       {istrue:true,prop:'outKuAmount',label:'出库金额', width:'100',sortable:'custom'},
        
      {istrue:true,prop:'payAmount',label:'付款金额', width:'100',sortable:'custom'},
      {istrue:true,prop:'adjustAmount',label:'调整金额', width:'100',sortable:'custom'},
      {istrue:true,prop:'returnAmount',label:'退款金额', width:'100',sortable:'custom'},
       {istrue:true,prop:'endAmount',label:'期末余额', width:'100',sortable:'custom'},
      // {istrue:true,prop:'payDate',label:'付款时间', width:'150',},
      {istrue:true,type:'button', width:'55',label:'操作', width:'100',btnList:[{label:"编辑期初余额",handle:(that,row)=>that.onHand(row)}]}
        // {istrue:true,type:'button', width:'55',label:'操作', width:'100',btnList:[{label:"编辑",handle:(that,row)=>that.onHand(row)}]},
    //    {istrue:true,prop:'payAudit',label:'付款审核', width:'100',},
    //  {istrue:true,prop:'adjustAudit',label:'调整审核', width:'100',},
    //   {istrue:true,prop:'butUmpire',label:'可反审', width:'100',}, 
     ];


export default {
  name: "Users",
  components: {container,cesTable,MyConfirmButton,logistics,goodscoderecord,purchasehistorytjanalysis,purchaseReturnGoods},
  data() {
    return {
      purchaseReturnGoods:{
       filter:{     
          timeRange:[]
        }
      },
      sels2:[],
      PurchaseReturnGoodsList:[],
      deletefilter:{
        batchNumber:''
      },
      dialogdeletebatchNumberVisible: false,
      uploadLoading:false,
      dialogVisible: false,
      SupplierName:'',
      that:this,
      formatWarehouseArea:formatWarehouseArea,
      formatYesornoBool:formatYesornoBool,
      formatTime:formatTime,
      formatIsOutStock:formatIsOutStock,
      formatSecondToHour:formatSecondToHour,
      filter: {
        timerange:'',
        SuName:null
      },
      goodscoderecordfilter:{goodsCode:"",buyNo:""},
      imgPreview:{img:"",show:false},
    //   lastUpdateTime:"",
      suppilelist:[],
      list: [],
      detaillist:[],
      oderDetailView:{},
      drawervisible:false,
      dialoganalysisVisible:false,
      visiblepopover: false,
      prevTarget: null, // 编辑 Popover 的 Reference （参照），用于 popover.js 对齐两个元素
      popperFlag: false, // 用于编辑 Popover 的刷新
      visiblepopoverdetail: false,
      dialogOrderDetailVisible:false,
      popperFlagdetail: false,
      tableCols:tableCols,
    //   tableHandles:tableHandles,
      pager:{OrderBy:" beginAmount ",IsAsc:false},
      summaryarry:{},
      total:0,
      total1:0,
      sels: [],
      selids: [],
      fileList:[],
      listLoading: false,
      pageLoading: false,
      editVisible:false,
    //   addVisible:false,
      editLoading:false,
    //   uploadLoading:false,
      hackReset:false,
      goodscoderecord1id: +new Date(),
      autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[]
        },
    };
  },
  watch: {
    value(n) {
      if(n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
      }
    }
  },
 async mounted() {

       
    formCreate.component('editor', FcEditor);
     await this.initform();
    await this.init();
    await this.onSearch();
  },
 methods: {
   onSelsChangeReturnGoods(sels){
    this.sels2 = sels

   },
   
   //批量删除采购退货出库显示
    async PurchaseReturnButton(){
       this.dialogdeletebatchNumberVisible=true;
   },
    //下载采购出库退货导入模板
   downloadTemplate(){
        window.open("../../static/excel/inventory/采购退货出库模板.xlsx","_self");
    },
   //导入
    startImport() {
      this.dialogVisible = true;
    },
    async uploadFile(item) {
      if(!this.fileHasSubmit){
        return false;
      }
      this.fileHasSubmit=false;
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      const res =await importPurchaseReturnGoods(form);
      if (res.success)   this.$message({ message: "上传成功,正在导入中...", type: "success" });
      
      else  this.$message({ message: res.msg, type: "warning" });
     
      this.uploadLoading=false;    
      },
       async uploadChange(file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for(var i=0;i<fileList.length;i++){
          if(fileList[i].status=="success")
            list.push(fileList[i]);
          else
            list.push(fileList[i].raw);
        }
        this.fileList = list;
      }
    },
    uploadRemove(file, fileList){
       this.uploadChange(file, fileList);
    },
    async submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.fileHasSubmit=true;
      this.uploadLoading=true;
      this.$refs.upload.submit();
    },
   //导出
   async onExport() {
     if (this.onExporting) return;
     try{
        const params = {...this.pager,...this.filter}
        if (params.timerange) {
         params.startDate = params.timerange[0];
         params.endDate = params.timerange[1];
      }
      //  params.SuName=this.filter.SuName.join();
        var res= await exportSupplierAccountsDetail(params);
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','供应商账目明细信息_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click()
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
      this.onExporting=false;
     },
  
  async initform(){
       
       this.autoform.rule= [{type:'hidden',field:'id',title:'id',value: ''},
                     {type:'input',field:'supplierName',title:'供应商名称',value: '',props:{readonly:true},col:{span:6}},
                     {type:'input',field:'inKuAmount',title:'入库金额',value: '',props:{readonly:true},col:{span:6}},
                     {type:'input',field:'payAmount',title:'付款金额',value: 0,props:{readonly:true}},
                     {type:'hidden',field:'buyNo',title:'采购单号',value: 0,props:{readonly:true}},
                     {type:'input',field:'adjustAmount',title:'调整金额',value: '',props:{readonly:true},col:{span:6}},
                       {type:'inputnumber',field:'beginAmount',title:'期初余额',value: '',col:{span:6},props:{min:-99999999,max:99999999}},
                     {type:'input',field:'endAmount',title:'期末余额',value: '',props:{readonly:true},col:{span:6}},  
                    //  {type:'DatePicker',field:'payDate',title:'付款时间',value:'',props:{type:'datetime',format:'yyyy-MM-dd:HH:mm:ss',placeholder:'付款时间',readonly:true},col:{span:6}},                
                    ]
                   
                    
    },
  
  async init(data){
      const params={
         SupplierName:data
      }
      var res2= await getAllSupplier(params);
      this.suppilelist = res2.data.map(item => {
          return { value: item.key, label: item.value };
      });
    },
   async onSearch() {
       this.$refs.pager.setPage(1)
       this.getlist()
    },
   async getlist() {
      if (!this.pager.OrderBy) this.pager.OrderBy="";
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      if (params.timerange) {
         params.startDate = params.timerange[0];
         params.endDate = params.timerange[1];
      }
      // params.SuName=this.filter.SuName.join();

      this.listLoading = true
      const res = await SupplierAccountsDetail(params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {d._loading = false})
      this.list = data
      this.summaryarry=res.data.summary;
    },
   async cellclick(row, column, cell, event){
     if (column.property=='expressNo'&&row.expressNo)
        await this.showlogistics(row.companyCode,row.expressNo);
     else if (column.property=='planArrivalTime'||column.property=='reMark'||column.property=='companyName'
         ||column.property=='boxCount') {
        await this.getrecordlist(row.buyNo)
        this.visiblepopover = true;
      // if (event.stopPropagation) {//阻止事件冒泡，兼容ie
      //   event.stopPropagation();
      // } else if (window.event) {
      //   window.event.cancelBubble = true;
      // }
      // let currentTarget = event.target; // 赋值当前点击的编辑
      // this.editData = row; // 设置编辑数据
      // if (this.prevTarget === currentTarget) { // 判断是否需要切换
      //   this.visiblepopover = !this.visiblepopover; // 同一个元素重复点击
      // } else {
      //   if (this.prevTarget) {  // 切换不同元素, 判断之前是否有点击其他编辑 prevTarget
      //     this.clearEditPopperComponent();   // 先清除之前的编辑框
      //     this.$nextTick(() => { // 然后生成新的编辑框
      //       this.prevTarget = currentTarget;
      //       this.visiblepopover = true;
      //     });
      //   } else {
      //     console.log('首次--->this.prevTarget'); // 首次
      //     this.prevTarget = currentTarget;
      //     this.visiblepopover = true;
      //   }
      // }
     }
    else if (column.property=='buyNo') {
      await this.getdetaillist(row.buyNo)
      if (event.stopPropagation) {
        event.stopPropagation();
      } else if (window.event) {
        window.event.cancelBubble = true;
      }
      let currentTarget = event.target;
      this.editData = row;
      if (this.prevTarget === currentTarget) {
        this.visiblepopoverdetail = !this.visiblepopoverdetail;
      } else {
        if (this.prevTarget) {
          this.clearEditPopperComponent();
          this.$nextTick(() => {
            this.prevTarget = currentTarget;
            this.visiblepopoverdetail = true;
          });
        } else {
          this.prevTarget = currentTarget;
          this.visiblepopoverdetail = true;
        }
      }
     }
    },
   async getdetaillist(buyno){
       this.detaillist=[];
       const res = await queryPurchaseOrderDetail({buyno:buyno})
       if (!(res.code==1&&res.data)) return 
       this.detaillist=res.data;
    },
   async getrecordlist(buyno){
       this.goodscoderecordfilter={buyNo:buyno,goodsCode:''};
       this.$nextTick(() => {
           this.$refs.goodscoderecord.onSearch('',buyno);
        });
    },
 async onHand(row){
      this.formtitle='编辑';
      this.editVisible = true
      console.log("当前显示数据",row);
      // const res = await getSupplierAccountsDetailBuyNo({supplier:row.supplierName})
      // console.log("付款数据",res.data)
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
         this.autoform.fApi.resetFields()  
         this.$nextTick(async() =>{
          await this.autoform.fApi.setValue(row)
        })  
      
    },
   onDisPlay(row){
     return row.isHandle==true;
    },
 async onEditSubmit() {
      this.editLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          const res = await editSupplierAccountsDetail(formData);
          if(res.success){
            this.$message.success('修改成功！');
             this.getlist();
            this.editVisible=false;
          }
        }else{}
     })
     this.editLoading=false;
    },
  
    beforeRemove() {
      return false;
    },
    
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   selsChange: function(sels) {
      this.sels = sels
    },
   selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.proBianMa);
      })
    },
   doCopy: function (val) {       
      let that=this;                         
      this.$copyText(val).then(function (e) {
          that.$message({ message: "内容已复制到剪切板！", type: "success" });
      }, function (e) {
          that.$message({ message: "抱歉，复制失败！", type: "warning" });
      })
    },
    showImg(e) {
      if (e.target.tagName == 'IMG') {
        this.imgPreview.img = e.target.src
        this.imgPreview.show = true
      }
    },
   async showlogistics(companycode,number){
      this.drawervisible=true;
      this.$refs.logistics.showlogistics(companycode,number);
    },
   async onShowHistory(){
     this.dialoganalysisVisible=true;
      this.$nextTick(() => {
          this.$refs.purchasehistorytjanalysis.onSearch();
      });
    }
  },
};
</script>


