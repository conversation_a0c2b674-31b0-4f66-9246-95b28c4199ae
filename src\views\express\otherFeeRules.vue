<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-select v-model="ListInfo.CompanyId" placeholder="快递公司" class="publicCss">
          <el-option label="所有" value></el-option>
          <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-input v-model.trim="ListInfo.orderNoInner" placeholder="区域" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
      </div>
    </template>
    <vxetablebase :id="'otherFeeRules202410151606'" :tablekey="'otherFeeRules202302031421'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { getExpressComanyAll } from "@/api/express/express";
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '快递公司', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNoInner', label: '费用类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'importTime', label: '费用', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'planSendTime', label: '起始日期', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'timePay', label: '结束日期', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight', label: '区域', },
]
export default {
  name: "otherFeeRules",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      expresscompanylist: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
    await this.init()
  },
  methods: {
    async init() {
      const res = await getExpressComanyAll({});
      if (!res?.success) return;
      this.expresscompanylist = res.data;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await pageGetVoOrder(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
