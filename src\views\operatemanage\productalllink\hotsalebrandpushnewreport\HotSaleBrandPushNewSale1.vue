<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
            <el-button type="primary" v-if="!isSum" @click="onAddStyleCodeShow()">新增系列编码</el-button>
            <el-button type="primary" @click="onBatchWanDanCunDang()">批量万单存档</el-button>
        </template>
        <vxetablebase :id="'HotSaleBrandPushNewSale1202408041719'" ref="table" :that='that' :isIndex='true'
            :hasexpand='true' :tablefixed='true' :border="true" @sortchange='sortchange' @select='selectchange'
            :tableData='tableData' :tableCols='tableCols' :showsummary='true' :summaryarry='summaryarry'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="listLoading"
            :height="'100%'">
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

        <el-dialog title="新增系列编码" :visible.sync="delStyleCodeDialog.visible" width="300px" :close-on-click-modal="false"
            append-to-body v-dialogDrag>
            <el-row>
                <el-col :span="24">
                    <el-select v-model="delStyleCodeSel">
                        <el-option v-for="item in delStyleCodeList" :key="item" :label="item" :value="item">

                        </el-option>
                    </el-select>
                </el-col>
            </el-row>
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="primary" @click="onAddStyleCode">确认</el-button>
                </span>
            </template>
        </el-dialog>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import datepicker from '@/views/customerservice/datepicker';
import { formatTime } from "@/utils";
import dayjs from 'dayjs'
import { pickerOptions } from '@/utils/tools';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    GetHotSaleBrandPushNewSaleStylePageList, SetHotSaleBrandPushNewSaleEnable, GetHotSaleBrandPushNewSaleDelEnable, HotSaleBrandPushNewSaleStyleCunDang
} from '@/api/operatemanage/productalllink/alllink'
const tableCols = [
    { istrue: true, fixed: 'left', width: '40', type: "checkbox" },
    { sortable: 'custom', width: '80', align: 'center', prop: 'createdUserName', label: '推荐人', },
    { sortable: 'custom', width: '200', align: 'center', prop: 'createUserDeptName', label: '架构', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'createUserArea', label: '地区', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'styleCode', label: '系列编码', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'goodsCount', label: '销量', },
    { sortable: 'custom', width: '140', align: 'center', prop: 'styleCodeFirstCreatedTime', label: '出现时间', },
    { sortable: 'custom', width: '140', align: 'center', prop: 'styleCodeCountArriveDate', label: '达成时间', formatter: (row) => row.styleCodeCountArriveDate == null ? "" : formatTime(row.styleCodeCountArriveDate, "YYYY-MM-DD") },
    {
        width: '80', align: 'center', prop: 'styleEnable', label: '新增移除', type: 'html',
        formatter: (row) => (row.styleEnable == 2 ? '<span style="color:red">已移除</span>' :
            (row.styleEnable == 1 ? '<span style="color:green">被新增</span>'
                : '<span style="color:green">正常</span>'))
    },
    { width: '100', align: 'center', prop: 'cunDangYearMonth', label: '万单存档月', type: 'html', formatter: (row) => ('<span style="color:red">' + (row.cunDangYearMonth ? row.cunDangYearMonth : '') + '</span>') },
    {
        istrue: true, type: 'button', label: '操作', width: '160', align: 'center',
        btnList: [
            //{ label: "新增", display: (row) => { return row.styleEnable == false; }, handle: (that, row) => that.onJinYongQiYong(row,1) },
            { label: "移除", display: (row) => { return row.styleEnable == 2; }, handle: (that, row) => that.onJinYongQiYong(row, 2) },
            { label: "万单存档", display: (row) => !!row.cunDangYearMonth, handle: (that, row) => that.onWanDanCunDang(row) },
            { label: "取消存档", display: (row) => !row.cunDangYearMonth, handle: (that, row) => that.onWanDanCunDang2(row) },
        ]
    },
];
export default {
    name: "HotSaleBrandPushNewSale1",
    components: {
        MyContainer, datepicker, vxetablebase
    },
    data() {
        return {
            auditVisible: false,
            activities: [],
            timeRanges: [],
            that: this,
            pickerOptions,
            filter: {
                timerange: [],
                saleStartDate: null,
                saleEndDate: null,
                createdUserId: 999999999,
                createdUserName: "",
                styleCodeCount: 0,
            },
            pager: { OrderBy: "goodsCount", IsAsc: false },
            tableCols,
            tableData: [],
            total: 0,
            listLoading: false,
            pageLoading: false,
            sels: [],
            selids: [],
            summaryarry: {},

            isSum: false,
            delStyleCodeDialog: {
                visible: false,
            },
            delStyleCodeSel: null,
            delStyleCodeList: [],
        }
    },
    async mounted() {
    },
    computed: {
    },
    methods: {
        async onSearch(args) {
            if (args) {
                this.filter.createdUserId = args.createdUserId;
                this.filter.createdUserName = args.createdUserName;
                this.filter.styleCodeCount = args.styleCodeCount;
                this.filter.timerange = args.timerange;
                this.isSum = args.isSum;

                this.filter.createdUserNames = args.createdUserNames;
                this.filter.createUserAreas = args.createUserAreas;
                this.filter.createUserRoles = args.createUserRoles;
                this.filter.createUserDeptNames = args.createUserDeptNames;

                console.log(this.filter, "this.filter111");
            }
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getParam() {
            if (this.filter.timerange && this.filter.timerange.length == 2) {
                this.filter.saleStartDate = this.filter.timerange[0];
                this.filter.saleEndDate = this.filter.timerange[1];
            }
            else {
                this.filter.saleStartDate = null;
                this.filter.saleEndDate = null;
            }
            let pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            return params;
        },
        async getList() {
            let param = this.getParam();
            this.listLoading = true
            const res = await GetHotSaleBrandPushNewSaleStylePageList(param)
            this.listLoading = false
            console.log(res);
            if (res?.success) {
                this.tableData = res.data.list;
                this.total = res.data.total;
                // Object.keys(res.data.summary).forEach(f=>{
                //     res.data.summary[f]=res.data.summary[f].toString();
                // });
                this.summaryarry = res.data.summary;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onJinYongQiYong(row, type) {
            this.listLoading = true;
            const res = await SetHotSaleBrandPushNewSaleEnable({
                businessDimension: "采购推新选品销量-单推荐人单款1",
                pushNewUserId: row.createdUserId,
                pushNewUserName: row.createdUserName,
                styleCode: row.styleCode,
                enableStyleCode: type,
            });
            this.listLoading = false;
            if (res?.success == true) {
                await this.getList();
            }
            else {
                //this.$message.error(res.msg)
            }
        },
        async onAddStyleCodeShow() {
            this.delStyleCodeDialog.visible = true;

            let res = await GetHotSaleBrandPushNewSaleDelEnable({ businessDimension: "采购推新选品销量-单推荐人单款1" });
            this.delStyleCodeList = res.data;

        },
        async onAddStyleCode() {
            if (!this.delStyleCodeSel) {
                this.$message.error('请选择系列编码')
                return;
            }
            this.listLoading = true;
            const res = await SetHotSaleBrandPushNewSaleEnable({
                businessDimension: "采购推新选品销量-单推荐人单款1",
                pushNewUserId: this.filter.createdUserId,
                pushNewUserName: this.filter.createdUserName,
                styleCode: this.delStyleCodeSel,
                enableStyleCode: 1,
            });
            this.listLoading = false;
            if (res?.success == true) {
                this.delStyleCodeDialog.visible = false;
                await this.onSearch();
            }
            else {
                //this.$message.error(res.msg)
            }
        },
        async onWanDanCunDang(row) {
            if (!this.filter.timerange || this.filter.timerange.length != 2) {
                this.$message.error('没选择日期区间不允许操作万单存档')
                return;
            }
            let entitys = [{
                createdUserId: row.createdUserId,
                CreatedUserName: row.createdUserName,
                styleCode: row.styleCode,
                cunDangDate: this.filter.timerange[1],
                areaEnm: this.filter.styleCodeCount,
                operType: 1
            }];
            this.$confirm('确定要存档吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const res = await HotSaleBrandPushNewSaleStyleCunDang(entitys);
                if (res?.success) {
                    row.cunDangYearMonth = '<span style="color:red">' + dayjs(this.filter.timerange[1]).format('YYYYMM'); +'</span>'
                    this.$message.success('存档成功')
                }
            }).catch(() => {
            });
        },
        async onBatchWanDanCunDang() {
            if (!this.filter.timerange || this.filter.timerange.length != 2) {
                this.$message.error('没选择日期区间不允许操作万单归档')
                return;
            }
            if (this.sels.length <= 0) {
                this.$message.error('未选择')
                return;
            }
            let entitys = [];
            this.sels.forEach(row => {
                entitys.push({
                    createdUserId: row.createdUserId,
                    CreatedUserName: row.createdUserName,
                    styleCode: row.styleCode,
                    cunDangDate: this.filter.timerange[1],
                    areaEnm: this.filter.styleCodeCount,
                    operType: 1
                });
            })

            this.$confirm('确定要存档吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const res = await HotSaleBrandPushNewSaleStyleCunDang(entitys);
                if (res?.success) {
                    this.sels.forEach(row => {
                        row.cunDangYearMonth = '<span style="color:red">' + dayjs(this.filter.timerange[1]).format('YYYYMM'); +'</span>'
                    })
                    this.$message.success('批量存档成功')
                }
            }).catch(() => {
            });
        },

        async onWanDanCunDang2(row) {
            if (!this.filter.timerange || this.filter.timerange.length != 2) {
                this.$message.error('没选择日期区间不允许操作万单存档')
                return;
            }
            let entitys = [{
                createdUserId: row.createdUserId,
                CreatedUserName: row.createdUserName,
                styleCode: row.styleCode,
                cunDangDate: this.filter.timerange[1],
                areaEnm: this.filter.styleCodeCount,
                operType: -1
            }];
            this.$confirm('确定要取消存档吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const res = await HotSaleBrandPushNewSaleStyleCunDang(entitys);
                if (res?.success) {
                    row.cunDangYearMonth = ''
                    this.$message.success('取消存档成功')
                }
            }).catch(() => {
            });
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}
</style>
