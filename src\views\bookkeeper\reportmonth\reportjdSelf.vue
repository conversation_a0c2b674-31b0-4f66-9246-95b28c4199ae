<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent></el-form>
            <el-button-group>
                <el-button style="padding: 0;">
                    <el-select filterable v-model="filter.version" placeholder="类型" style="width: 130px">
                        <el-option label="工资月报" value="v1"></el-option>
                        <el-option label="参考月报" value="v2"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-date-picker style="width: 120px" v-model="filter.yearMonth" type="month" format="yyyyMM"
                        value-format="yyyyMM" placeholder="核算月份"></el-date-picker>
                </el-button>
                <!-- <el-button style="padding: 0;margin: 0;">
                    <el-input v-model="filter.proCode" placeholder="商品ID" style="width:120px;" />
                </el-button> -->
                <el-button style="padding: 0;margin: 0;">
                    <inputYunhan ref="productCode2" :inputt.sync="filter.proCode" v-model="filter.proCode"
                        class="publicCss" placeholder="商品ID/多条请按回车" :clearable="true" :clearabletext="true"
                        :maxRows="300" :maxlength="6000" @callback="proCodeBack" title="商品ID">
                    </inputYunhan>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model="filter.productName" placeholder="商品名称" style="width:160px;" />
                </el-button>
                <el-button style="padding: 0;">
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;">
                    <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组"
                        style="width: 90px">
                        <el-option key="无运营组" label="无运营组" :value="0"></el-option>
                        <el-option v-for="item in grouplist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-select filterable clearable v-model="filter.isPinPai" placeholder="是否品牌" style="width:120px;">
                        <el-option label="是" :value=1></el-option>
                        <el-option label="否" :value=0></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model="filter.pinPai" placeholder="品牌" style="width:120px;" />
                </el-button>

                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onExport" :loading="exportloading">导出</el-button>
                <el-button type="primary" @click="onClacYB('v1')" >计算工资月报</el-button>
                <el-button type="primary" @click="onClacYB('v2')" >计算参考月报</el-button>

            </el-button-group>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :isSelectColumn="false" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry'
            @summaryClick='onsummaryClick' :tableData='financialreportlist' :tableCols='tableCols'
            :tableHandles='tableHandles' :loading="listLoading">
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

        <el-dialog title="明细" :visible.sync="calcdetails.visible" width="80%" v-dialogDrag>
            <calcdetails ref="calcdetails" style="height:600px;"></calcdetails>
        </el-dialog>
        <el-dialog title="淘宝现金红包明细" :visible.sync="CashRedTXDetail.visible" width="80%" v-dialogDrag>
            <div>
                <CashRedTXDetail ref="CashRedTXDetail" :filter="CashRedTXDetail.filter" style="height:600px;">
                </CashRedTXDetail>
            </div>
        </el-dialog>
        <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import { getAllList as getAllShopList, getDirectorGroupList } from '@/api/operatemanage/base/shop';
import { getFinancialReportList } from '@/api/financial/yyfy'
import { exportFinancialReport } from '@/api/monthbookkeeper/financialreport'
import { postJdSelfCalcTask } from '@/api/monthbookkeeper/financialDetail'
import { formatWarehouse, formatTime, formatYesornoBool, formatLinkProCode, platformlist } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import calcdetails from "@/views/bookkeeper/reportmonth/calcdetails";
import CashRedTXDetail from "@/views/bookkeeper/reportmonth/CashRedTXDetail";
import { Loading } from 'element-ui';
import buschar from '@/components/Bus/buschar'
import { getAnalysisCommonResponse } from '@/api/admin/common'
import inputYunhan from "@/components/Comm/inputYunhan";
let loading;
const startLoading = () => {  // 使用Element loading-start 方法
    loading = Loading.service({
        lock: true,
        text: '加载中……',
        background: 'rgba(0, 0, 0, 0.7)'
    });
};
const tableCols = [
    { istrue: true, fixed: true, prop: 'yearMonth', label: '年月', sortable: 'custom', width: '75' },
    { istrue: true, fixed: true, prop: 'proCode', fix: true, label: '商品ID', width: '120', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
    { istrue: true, fixed: true, prop: 'groupId', label: '小组', sortable: 'custom', width: '80', formatter: (row) => row.groupName || ' ' },
    { istrue: true, fixed: true, prop: 'operateSpecialUserName', label: '专员', sortable: 'custom', width: '80', formatter: (row) => row.operateSpecialUserName },
    { istrue: true, fixed: true, prop: 'assistantUserName', label: '助理', sortable: 'custom', width: '80', formatter: (row) => row.assistantUserName },
    {
        istrue: true, prop: '', label: `账单`, merge: true, prop: 'mergeField',
        cols: [
            { istrue: true, prop: 'createdTime', label: '统计日期', sortable: 'custom', width: '120' },
            { istrue: true, prop: 'proName', label: '产品名称', sortable: 'custom', width: '100' },
            { istrue: true, prop: 'goodsCodes', label: '商品编码', sortable: 'custom', width: '120' },
            { istrue: true, prop: 'styleCode', label: '系列编码', width: '100', sortable: 'custom' },
            { istrue: true, prop: 'shopCode', label: '店铺编码', width: '100', sortable: 'custom' },
            { istrue: true, prop: 'shopCode', label: '店铺名称', sortable: 'custom', width: '150', formatter: (row) => row.shopName || ' ' },
            { istrue: true, prop: 'pinPai', label: '品牌', width: '80', sortable: 'custom' },
            { istrue: true, prop: 'orderCount', label: '订单数', sortable: 'custom', width: '85' },
            { istrue: true, prop: 'count', label: 'ID计数', sortable: 'custom', width: '90' },
            { istrue: true, prop: 'saleCount', label: '销售数量', sortable: 'custom', width: '100' },
            { istrue: true, prop: 'amountSettlement', label: '结算收入', sortable: 'custom', width: '100' },
            { istrue: true, prop: 'salePrice', label: '成本单价', sortable: 'custom', width: '100' },
            { istrue: true, prop: 'amountCost', label: '结算成本', sortable: 'custom', width: '100' },
            { istrue: true, prop: 'amountOut', label: '退款', sortable: 'custom', width: '75' },
            
            { istrue: true, prop: 'outCost', label: '出库成本', sortable: 'custom', width: '100' },
            { istrue: true, prop: 'amountExceptionCost', label: '异常成本', sortable: 'custom', width: '100' },
            { istrue: true, prop: 'amontFreightfee', label: '采购运费', sortable: 'custom', width: '100' },
            { istrue: true, prop: 'grossProfit', label: '销售毛利', sortable: 'custom', width: '110' },
        ]
    },
    {
        istrue: true, prop: '', label: `账单费用`, merge: true, prop: 'mergeField1',
        cols: [
            { istrue: true, prop: 'penaltyAmount', sortable: 'custom', label: '违规金', width: '85' },
            { istrue: true, prop: 'thirdPartyShippingFee', sortable: 'custom', label: 'TC运费', width: '90' },
            { istrue: true, prop: 'rebateOrder', sortable: 'custom', label: '返点单', width: '85' },
            { istrue: true, prop: 'rebateInvoice', sortable: 'custom', label: '返利单', width: '85' },
            { istrue: true, prop: 'dkTotalAmont', sortable: 'custom', label: '账单费用合计', width: '130' }
        ]
    },
    {
        istrue: true, prop: '', label: `运营费用`, merge: true, prop: 'mergeField2',
        cols: [
            { istrue: true, prop: 'jdAlliance', label: '京东联盟', sortable: 'custom', width: '100' },
            { istrue: true, prop: 'wholeStation', label: '全站', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'expressTrain', label: '快车', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'tesudanyongjin', label: '特殊单佣金', sortable: 'custom', width: '130' },
            { istrue: true, prop: 'advFee', label: '运营费用合计', sortable: 'custom', width: '130' },
        ]
    },
    {
        istrue: true, prop: '', label: `产品费用`, merge: true, prop: 'mergeField5',
        cols: [
            { istrue: true, prop: 'amontPick', sortable: 'custom', label: '产品运费', width: '100' },
            { istrue: true, prop: 'amontSampleBX', sortable: 'custom', label: '样品费', width: '90' },
            { istrue: true, prop: 'amontSampleGrop', sortable: 'custom', label: '运营拿样', width: '100' },
            { istrue: true, prop: 'amontSampleMG', sortable: 'custom', label: '美工拿样', width: '100' },
            { istrue: true, prop: 'amontShoot', sortable: 'custom', label: '拍摄道具', width: '100' },
            { istrue: true, prop: 'cuishou', sortable: 'custom', label: '催收', width: '80' },
            { istrue: true, prop: 'totalProductCost', sortable: 'custom', label: '产品费用合计', width: '130' },
        ]
    },
    {
        istrue: true, prop: '', label: `工资`, merge: true, prop: 'mergeField6',
        cols: [
            { istrue: true, prop: 'amontCommissionMG', sortable: 'custom', label: '美工提成', width: '100' },
            { istrue: true, prop: 'amontCommissionXMT', label: '新媒体提成', sortable: 'custom', width: '110' },
            { istrue: true, prop: 'amontCommissionCG', sortable: 'custom', label: '采购提成', width: '100' },
            { istrue: true, prop: 'amontMachineGZ', label: '加工工资', sortable: 'custom', width: '100' },
            { istrue: true, prop: 'amontWagesGroup', label: '运营工资', sortable: 'custom', width: '100' },
            { istrue: true, prop: 'totalWagesCost', label: '工资合计', sortable: 'custom', width: '100' }
        ]
    },
    { istrue: true, prop: 'freightFee', label: '物流费', sortable: 'custom', width: '90' },
    { istrue: true, prop: 'amontOffLinefee', label: '运营下架', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'amontStoreLossfee', label: '仓储损耗', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'saleProfit', label: '产品利润', sortable: 'custom', width: '120' },
    
];
export default {
    name: "JdSelfMonthlyReport",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, calcdetails, CashRedTXDetail, buschar,inputYunhan },
    data() {
        return {
            that: this,
            filter: {
                proCode: null,
                platform: 74,
                yearMonth: null,
                shopCode: null,
                groupId: null,
                productName: null,
                version: "v1"
            },
            shopList: [],
            grouplist: [],
            financialreportlist: [],
            tableCols: tableCols,
            tableHandles: [],
            total: 0,
            // summaryarry:{count_sum:10},
            pager: { OrderBy: " AmountSettlement ", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            calcdetails: { visible: false },
            exportloading: false,
            CashRedTXDetail: {
                visible: false,
                filter: {
                    proCode: null,
                    shopCode: null,
                    yearMonth: null
                }
            },
            analysisFilter: {
                searchName: "View_FinancialMonthReport_JD",
                extype: 5,
                selectColumn: "Count",
                filterTime: "YearMonth",
                isYearMonthDay: false,
                filter: null,
                columnList: [{ columnNameCN: '订单数', columnNameEN: 'Count' }]
            },
            buscharDialog: { visible: false, title: "", data: [] },
        };
    },
    async mounted() {
        await this.getShopList();
    },
    methods: {
        //获取全部的店铺和运营组成员
        async getShopList() {
            const res1 = await getAllShopList({ platforms: [74] });
            this.shopList = [];
            res1.data?.forEach(f => {
                if (f.isCalcSettlement && f.shopCode)
                    this.shopList.push(f);
            });

            let res2 = await getDirectorGroupList();
            this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
        },
        //排序改变
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        //搜索
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList().then(res => { });
        },
        //查询数据
        async getList() {
            let that = this;
            let pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter, };
            startLoading();
            const res = await getFinancialReportList(params).then(res => {
                loading.close();
                that.total = res.data?.total
                that.financialreportlist = res.data?.list;
                that.summaryarry = res.data?.summary;
            });
        },
            //导出月报
            async onClacYB(v) {
            if (!this.filter.yearMonth) {
                this.$message({ message: "请先选择月份！", type: "warning" });
                return;
            }
          
            let res = await postJdSelfCalcTask({version: v , yearMonth: this.filter.yearMonth});
            if (!res?.success) return
            this.$message({ type: 'success', message: '正在计算中,请稍候...' });
        },
        //导出月报
        async onExport() {
            if (!this.filter.yearMonth) {
                this.$message({ message: "请先选择月份！", type: "warning" });
                return;
            }
            let pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            this.exportloading = true;
            let res = await exportFinancialReport(params);
            this.exportloading = false;
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '财务账单数据' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        ///显示计算详细信息
        // async showcalcdetails(row, column) {
        //     this.calcdetails.visible = true;
        //     let calc = this.$refs.calcdetails;
        //     let that = this;
        //     await this.$nextTick(async () => {
        //         await that.$refs.calcdetails.onSearch(column, this.filter.version, row.shopCode, row.yearMonth, row.proCode)
        //     });
        // },
        ///点击总金额和数量
        async onsummaryClick(property) {
            let that = this;
            this.analysisFilter.filter = {
                proCode: [that.filter.proCode, 0],
                yearMonth: [that.filter.yearMonth, 0],
                shopCode: [that.filter.shopCode, 0],
                groupId: [that.filter.groupId, 0],
                productName: [that.filter.productName, 5],
                version: [that.filter.version, 0],
            };
            this.analysisFilter.selectColumn = property;
            let cn = "金额";
            if (property == "orderCount" || property == "count") {
                cn = "数量";
            }
            this.analysisFilter.columnList = [{ columnNameCN: cn, columnNameEN: property }];

            const res = await getAnalysisCommonResponse(this.analysisFilter).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data
                that.buscharDialog.title = res.data.legend[0]
            });
        },
        ///商品ID
        proCodeBack(val) {
            let that = this;
            that.filter.proCode = val;
        }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
