<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-select v-model="ListInfo.shopCode" placeholder="店铺" class="publicCss" filterable clearable>
          <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
        </el-select>
        <el-select v-model="ListInfo.isRight" placeholder="验算结果" class="publicCss" clearable>
          <el-option key="正确" label="正确" :value="1" />
          <el-option key="错误" label="错误" :value="0" />
        </el-select>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
      </div>
    </template>
    <vxetablebase :id="'advertisingCostCheck202503091701'" :tablekey="'advertisingCostCheck202503091701'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getSignUpRecordList, importSignUpRecordAsync } from '@/api/bookkeeper/reportdayV2'
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: '200', align: 'center', prop: 'yearMonthDay', label: '日期', },
  { sortable: 'custom', width: '250', align: 'center', prop: 'shopName', label: '店铺名称', },
  { sortable: 'custom', width: '200', align: 'center', prop: 'proCode', label: '商品ID', },
  { sortable: 'custom', width: '200', align: 'center', prop: 'totalCount', label: '验算', },
  { sortable: 'custom', width: '200', align: 'center', prop: 'isRight', label: '验算结果', formatter: (row) => row.isRight == 1 ? '正确' : '错误', },
]
export default {
  name: "advertisingCostCheck",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      dialogVisible: false,
      fileList: [],
      fileparm: {},
      uploadLoading: false,
      shopList: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startUseDate: null,//开始时间
        endUseDate: null,//结束时间
        shopCode: null,
        isRight: null,
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
    await this.init()
  },
  methods: {
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importSignUpRecordAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async init() {
      const res1 = await getAllShopList({ platforms: [8] });
      this.shopList = res1.data?.filter(f => f.isCalcSettlement && f.shopCode) || [];
    },
    async changeTime(e) {
      this.ListInfo.startUseDate = e ? e[0] : null
      this.ListInfo.endUseDate = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        this.ListInfo.startUseDate = dayjs().format('YYYY-MM-DD')
        this.ListInfo.endUseDate = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startUseDate, this.ListInfo.endUseDate]
      }
      this.loading = true
      const { data, success } = await getSignUpRecordList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.yearMonthDay = dayjs(item.yearMonthDay).format('YYYY-MM-DD')
        })
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
