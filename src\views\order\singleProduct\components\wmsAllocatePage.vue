<template>
    <MyContainer>
        <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :isRemoteSort="false" :is-select-column="true" :is-index-fixed="false" :isNeedExpend="false"
            style="width: 100%; margin: 0;height: 500px;" @cellStyle="cellStyle" cellStyle
            @footerCellStyle="footerCellStyle" :showsummary="data.summary ? true : false" :summaryarry="data.summary"
            @sortchange="sortchange">
            <template #preAllotQty="{ row }">
                <el-input-number v-model="row.preAllotQty" :precision="0" :min="0"
                    :max="row.sellStock - row.totalPreAllotQty" :controls="false" style="width: 100%;"
                    placeholder="调拨数量" @blur="computedTotal" />
            </template>
            <template #preAllotQty_footer>
                {{ data.summary.preAllotQty_sum }}
            </template>
            <template #allotInWmsName="{ row, index }">
                <div v-if="type == 0">
                    {{ row.allotInWmsName }}
                </div>
                <chooseWareHouse v-model="row.allotInWmsId" class="publicCss" placeholder="调入仓" v-else
                    @chooseWms="chooseWms($event, row)" :clearable="false" />
            </template>
            <template #inventoryDay="{ row }">
                <div>
                    {{ row.inventoryDay != null ? row.inventoryDay.toFixed(1) : row.inventoryDay }}
                </div>
            </template>
            <template #totalPreAllotQty_footer>
                {{ data.summary.totalPreAllotQty_sum }}
            </template>
            <template #allotInventoryDay="{ row }">
                <div style="color: red;">{{ row.allotInventoryDay }}</div>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange"
                style="margin-top: 10px;" />
            <div class="btnGroup">
                <el-button @click="$emit('close')">关闭</el-button>
                <el-button type="primary" @click="submit" v-throttle="2000">确定</el-button>
            </div>
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/SaleItems/GoodsAllot_Create/'
import { mergeTableCols } from '@/utils/getCols'
// import decimal from '@/utils/decimal'
import decimal1 from '@/utils/decimalToFixed'
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, chooseWareHouse
    },
    props: {
        query: {
            type: Object,
            default: {}
        },
        type: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            api,
            platformlist,
            that: this,
            data: {},
            ListInfo: {
                goodsCode: this.query.goodsCode,
                goodsName: this.query.goodsName,
                styleCode: this.query.styleCode,
                brandId: this.query.brandId,
                brandName: this.query.brandName,
                wmsId: this.query.wmsId,
                type: this.type,
                wmsSellStock: this.query.wmsSellStock,
                wmsLackQty2: this.query.wmsLackQty2,
                picture: this.query.picture,
            },
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            sumCols: []
        }
    },
    async mounted() {
        await this.getCol();
        await this.getList()
    },
    methods: {
        async chooseWms(e, row) {
            this.$set(row, 'allotInWmsName', e.name)
            const { data, success } = await request.post(`${this.api}TotalPreAllotQty`, {
                allotOutWmsId: row.allotOutWmsId,
                allotInWmsId: e.wms_co_id,
                goodsCode: this.query.goodsCode,
                type: this.type
            })
            if (success) {
                this.$set(row, 'totalPreAllotQty', data)
            }
            this.computedTotal()
        },
        async cellStyle(row, column, callback) {
            let cols = []
            this.tableCols.forEach(item => {
                if (item.cols && item.cols.length > 0) {
                    item.cols.forEach(item1 => {
                        cols.push(item1)
                    })
                } else {
                    cols.push(item)
                }
            })
            const res = cols.find(item => item.prop == column.field)
            callback({ backgroundColor: res ? res.headerBgColor : '' })
        },
        footerCellStyle(row, callback) {
            let cols = []
            this.tableCols.forEach(item => {
                if (item.cols && item.cols.length > 0) {
                    item.cols.forEach(item1 => {
                        cols.push(item1)
                    })
                } else {
                    cols.push(item)
                }
            })
            const res = cols.find(item => item.prop == row.column.field)
            for (let i = 0; i <= cols.length; i++) {
                if (res.headerBgColor) {
                    callback({ backgroundColor: res.headerBgColor })
                }
            }
        },
        async computedTotal() {
            const res = this.data.list.reduce((total, item) => {
                return decimal1(total, item.preAllotQty, '+')
            }, 0)
            this.$set(this.data.summary, 'preAllotQty_sum', res.toFixed(0))

            const res1 = this.data.list.reduce((total, item) => {
                return decimal1(total, item.totalPreAllotQty, '+')
            }, 0)
            this.$set(this.data.summary, 'totalPreAllotQty_sum', res1.toFixed(0))

            this.data.list.forEach(item => {
                item.allotInventoryDay = this.roundToPositiveInfinity(decimal1(decimal1(decimal1(item.sellStock, item.totalPreAllotQty, '-'), item.preAllotQty, '-'), item.amendDayNew, '/'), 1)
            })
            const res2 = this.data.list.reduce((total, item) => {
                return decimal1(total, item.allotInventoryDay, '+')
            }, 0)
            this.$set(this.data.summary, 'allotInventoryDay_sum', res2)
        },
        roundToPositiveInfinity(value, decimalPlaces) {
            const multiplier = Math.pow(10, decimalPlaces);
            const scaledValue = value * multiplier;
            // 使用Math.ceil向上取整，实现朝正无穷方向舍入的效果
            const roundedValue = Math.ceil(scaledValue);
            // 再将结果除以之前的乘数，还原到相应的小数量级
            return roundedValue / multiplier;
        },
        async submit() {
            if (!this.data.list || this.data.list?.length == 0) return this.$message.error('没有可用的数据')
            this.data.list.forEach((item, i) => {
                if (!item.allotInWmsId) {
                    this.$message.error(`请选择第${i + 1}行的调入仓`)
                    throw new Error(`请选择第${i + 1}行的调入仓`)
                }
            })
            const res = this.data?.list?.filter(item => item.preAllotQty > 0)
            if (res?.length == 0) {
                return this.$message.error('没有可用的数据,若想调拨请填写调拨数量')
            }
            const { success } = await request.post(`${this.api}Create`, res)
            if (!success) return
            this.$message.success('操作成功')
            this.$emit('close')
            this.$emit('getList')
        },
        proCodeCallback(val) {
            this.ListInfo.proCode = val
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                this.sumCols = data
                data.forEach(item => {
                    if (this.type == 1) {
                        item.mergeName = item.mergeName == '本仓' ? '云仓' : item.mergeName == '云仓' ? '本仓' : item.mergeName
                        if (item.prop == 'allotInWmsSellStock' || item.prop == 'allotInWmsLackQty2') {
                            item.enabled = false
                        }
                        if (item.prop == 'preAllotQty' || item.prop == 'allotInWmsName' || item.prop == 'totalPreAllotQty') {
                            item.width = '150'
                            item.align = 'center'
                        }
                    } else {
                        if (item.prop == 'preAllotQty') {
                            item.width = '150'
                            item.align = 'center'
                        }
                    }
                    if (item.mergeName == '本仓') {
                        item.headerBgColor = '#d6f9f1'
                    }
                    if (item.mergeName == '云仓') {
                        item.headerBgColor = '#d6e7f9'
                    }
                })
                this.tableCols = mergeTableCols(data)
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    this.data = data;
                }
            } catch (error) {
                console.log(error);
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0;
    }
}

.btnGroup {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>

<style lang="scss">

.vxe-table--tooltip-wrapper {
    z-index: 10000 !important;
}
</style>

