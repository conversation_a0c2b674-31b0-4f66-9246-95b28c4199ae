<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-button-group>
        <el-button style="padding: 0; margin: 0">
          <el-date-picker style="width: 210px" v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
        </el-button>
        <el-button style="padding: 0; margin: 0">
          <el-input v-model.trim="filter.proCode" :maxlength="40" clearable placeholder="商品ID" style="width: 130px" />
        </el-button>
        <el-button style="padding: 0; margin: 0">
          <el-input v-model.trim="filter.goodsCode" :maxlength="40" clearable placeholder="商品编码" style="width: 130px" />
        </el-button>
        <el-button style="padding: 0; margin: 0">
          <el-input v-model.trim="filter.shopGoodsCode" :maxlength="40" clearable placeholder="店铺商品编码"
            style="width: 130px" />
        </el-button>
        <el-button style="padding: 0; margin: 0">
          <el-select filterable v-model="filter.platform" clearable placeholder="平台" style="width: 120px">
            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-button>
        <el-button style="padding: 0">
          <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组" style="width: 120px">
            <el-option key="无运营组" label="无运营组" :value="0"></el-option>
            <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-button>
        <!-- <el-button style="padding: 0;">
          <el-select v-model="filter.dtlGroupBy" placeholder="运营组" style="width: 120px">
            <el-option label="汇总查看" :value="2"></el-option>
            <el-option label="明细查看" :value="0"></el-option>
          </el-select>
        </el-button> -->
        <el-button style="padding: 0">
          <el-select v-model="filter.exitProfitDes" placeholder="出仓利润" style="width: 120px">
            <el-option key="全部" label="所有出仓利润" :value="null"></el-option>
            <el-option key="出仓正利润>0" label="出仓正利润" value="出仓正利润>0"></el-option>
            <el-option key="出仓负利润<0" label="出仓负利润" value="出仓负利润<0"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0">
          <el-select v-model="filter.payAmountDiffZero" placeholder="付款金额" style="width: 120px">
            <el-option key="全部" label="所有付款金额" :value="null"></el-option>
            <el-option key="付款金额>0" label="付款金额>0" :value="1"></el-option>
            <el-option key="付款金额≤0" label="付款金额≤0" :value="-1"></el-option>
          </el-select>
        </el-button>
        <el-button type="primary" @click="onSearch">查询</el-button>
      </el-button-group>
    </template>

    <vxetablebase :id="'brandPriceComparison2025032111381'" :tablekey="'brandPriceComparison2025032111381'"
      :border="true" :align="'center'" ref="table" :that="that" :isIndex="true" :hasexpand="false"
      @sortchange="sortchange" :isSelectColumn="true" :showsummary="true" :tablefixed="true" :summaryarry="summaryarry"
      :tableData="productReportPddGoodsList" :tableCols="tableCols" :tableHandles="tableHandles" :loading="listLoading"
      style="width: 100%; height: 100%; margin: 0" :xgt="9999"
      :treeProp="{ rowField: 'rowId', parentField: 'parentId', transform: true, }">
    </vxetablebase>

    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>

    <el-dialog :title="dialogGoodVisible.title" :visible.sync="dialogGoodVisible.visible" width="80%" v-dialogDrag
      append-to-body :close-on-click-modal="false">
      <my-container>
        <span>
          <buschar v-if="dialogGoodVisible.visible" :analysisData="dialogGoodVisible.data"
            :loading="dialogGoodVisible.loading">
          </buschar>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogGoodVisible.visible = false">关闭</el-button>
        </span>
      </my-container>
    </el-dialog>

    <el-dialog :title="dialogDetailVisible.title" :visible.sync="dialogDetailVisible.visible" width="80%" v-dialogDrag
      append-to-body :close-on-click-modal="false">
      <my-container>
        <span>
          <el-button-group>
            <el-button style="padding: 0; margin: 0">
              <inputYunhan ref="dialogDetailVisiblefilterproCode" v-model="dialogDetailVisible.filter.proCode"
                :inputt.sync="dialogDetailVisible.filter.proCode" placeholder="商品ID" :clearable="true" width="400px"
                @callback="callbackProCode" title="商品ID">
              </inputYunhan>
            </el-button>

            <el-button style="padding: 0; margin: 0">
              <el-input v-model.trim="dialogDetailVisible.filter.shopGoodsCode" :maxlength="40" clearable disabled
                placeholder="店铺商品编码" style="width: 130px" />
            </el-button>
            <el-button type="primary" @click="showdetailchartsearch">查询</el-button>
          </el-button-group>
        </span>
        <span>
          <buschar ref="dialogDetailVisiblebuschar" v-if="dialogDetailVisible.visible"
            :analysisData="dialogDetailVisible.data" :loading="dialogDetailVisible.loading">
          </buschar>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogDetailVisible.visible = false">关闭</el-button>
        </span>
      </my-container>
    </el-dialog>
  </my-container>
</template>
<script>
import MyConfirmButton from '@/components/my-confirm-button';
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import buschar from '@/components/Bus/buschar';
import middlevue from "@/store/middle.js"
import { formatPlatform, formatTime, platformlist, formatLinkProCode, formatProCodeStutas3 } from "@/utils/tools";
import { getDirectorGroupList, getDirectorList } from '@/api/operatemanage/base/shop'
import { getProductReportGoodsProfitTreePageList, getProductReportGoodsProfitChart, getProductReportDtlProfitChart } from '@/api/bookkeeper/reportdayV2'
const tableCols = [//fixed: 'left',
  { istrue: true, prop: 'fsYearMonthDay', label: '年月日', sortable: 'custom', width: '100', treeNode: true, formatter: (row) => row.fsYearMonthDay },
  { istrue: true, prop: 'platform', fix: true, label: '平台', width: '50', sortable: 'custom', formatter: (row) => formatPlatform(row.platform), type: 'custom' },
  { istrue: true, label: '小组头像', width: '70', type: 'ddAvatar', ddInfo: { type: 1, prop: 'groupId' } },
  { istrue: true, prop: 'groupId', label: '小组', sortable: 'custom', width: '70', formatter: (row) => row.groupName, type: 'ddTalk', ddInfo: { type: 1, prop: 'groupId', name: 'groupName' }, },
  { istrue: true, prop: 'orderNo', label: '原始线上单号', width: '140', type: 'custom' },//, type: 'click', handle: (that, row) => that.toLinkTmDemo(row)
  { istrue: true, prop: 'orderNoInner', label: '内部单号', width: '80', type: 'custom' },//, type: 'click', handle: (that, row) => that.toLinkTmDemo(row)
  { istrue: true, prop: 'proCode', fix: true, label: '商品ID', width: '90', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
  { istrue: true, prop: 'goodsCode', fix: true, label: '商品编码', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'goodsPic', label: '图片', width: '60', type: 'images' },
  { istrue: true, prop: 'onlineColorSpecification', label: '名称', width: '150' },
  // {
  //     istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: "45",
  //     formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showgoodchart(row)
  // },
  { istrue: true, prop: 'pinPai', label: '品牌', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'danJia', label: '单价', width: '80' },
  { istrue: true, prop: 'samePinPai7DayMinPrice', label: '同品牌7日最低价', sortable: 'custom', width: '130', formatter: (row) => row.samePinPai7DayMinPrice !== null ? row.samePinPai7DayMinPrice.toFixed(3) : ' ' },
  { istrue: true, prop: 'samePinPai7DayMinProCode', label: '同品牌7日最低ID', sortable: 'custom', width: '120', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.samePinPai7DayMinProCode) },
  { istrue: true, prop: 'samePinPai7DayMaxPrice', label: '同品牌7日最高价', sortable: 'custom', width: '130', formatter: (row) => row.samePinPai7DayMaxPrice !== null ? row.samePinPai7DayMaxPrice.toFixed(3) : ' ' },
  { istrue: true, prop: 'samePinPai7DayMaxProCode', label: '同品牌7日最高ID', sortable: 'custom', width: '120', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.samePinPai7DayMaxProCode) },
  { istrue: true, prop: 'noSamePinPai7DayMinPrice', label: '不同品牌7日最低价', sortable: 'custom', width: '135', formatter: (row) => row.noSamePinPai7DayMinPrice !== null ? row.noSamePinPai7DayMinPrice.toFixed(3) : ' ' },
  { istrue: true, prop: 'noSamePinPai7DayMinProCode', label: '不同品牌7日最低ID', sortable: 'custom', width: '135', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.noSamePinPai7DayMinProCode) },
  { istrue: true, prop: 'noSamePinPai7DayMaxPrice', label: '不同品牌7日最高价', sortable: 'custom', width: '135', formatter: (row) => row.noSamePinPai7DayMaxPrice !== null ? row.noSamePinPai7DayMaxPrice.toFixed(3) : ' ' },
  { istrue: true, prop: 'noSamePinPai7DayMaxProCode', label: '不同品牌7日最高ID', sortable: 'custom', width: '135', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.noSamePinPai7DayMaxProCode) },
  { istrue: true, prop: 'noSamePinPai7DayMinPinPai', label: '不同品牌7日最低品牌', sortable: 'custom', width: '140' },
  { istrue: true, prop: 'noSamePinPai7DayMaxPinPai', label: '不同品牌7日最高品牌', sortable: 'custom', width: '140' },
  { istrue: true, prop: 'payAmont_1', label: '付款金额', sortable: 'custom', width: '80', formatter: (row) => row.payAmont_1 },
  { istrue: true, prop: 'saleAmont', label: '销售金额', sortable: 'custom', width: '80', formatter: (row) => row.saleAmont },
  { istrue: true, prop: 'saleCost_1', label: '销售成本', sortable: 'custom', width: '80', formatter: (row) => row.saleCost_1 },
  { istrue: true, prop: 'freightFee_1', label: '快递费', sortable: 'custom', width: '70', formatter: (row) => row.freightFee_1 },
  { istrue: true, prop: 'exitCost_1', label: '出仓成本', sortable: 'custom', width: '80', formatter: (row) => row.exitCost_1 },
  { istrue: true, prop: 'exitProfit_1', label: '出仓利润', sortable: 'custom', width: '80', formatter: (row) => row.exitProfit_1 },
  { istrue: true, prop: 'exitProfit_1_1', label: '出仓正利润', sortable: 'custom', width: '90', formatter: (row) => row.exitProfit_1_1 },
  { istrue: true, prop: 'negativeExitProfit', label: '出仓负利润', sortable: 'custom', width: '90', type: 'clickLink', handle: (that, row) => that.onGoodsProfitShow(row), formatter: (row) => row.negativeExitProfit },
  { istrue: true, prop: 'replacementOrderCost_1', label: '补发订单成本', width: '80', formatter: (row) => row.replacementOrderCost_1 },
  //{ istrue: true, prop: 'goodOrderCount_1', fix: true, label: '订单数', width: '70', sortable: 'custom' },
  //{ istrue: true, prop: 'goodOrderRate_1', fix: true, label: '订单占比', width: '80', sortable: 'custom' },
  //{ istrue: true, prop: 'keDanJia_1', fix: true, label: '客单价', width: '70', sortable: 'custom' },
  { istrue: true, prop: 'packageFee_1', label: '包装费', sortable: 'custom', width: '70', formatter: (row) => row.packageFee_1 },
  { istrue: true, prop: 'replacementCost_1', label: '补发成本', sortable: 'custom', width: '80', formatter: (row) => row.replacementCost_1 },
  { istrue: true, prop: 'shopGoodsCode', fix: true, label: '店铺商品编码', width: '120', sortable: 'custom', type: 'click', handle: (that, row) => that.showdetailchart(row) },
];
const tableHandles = [];
export default {
  name: "brandPriceComparison",
  components: {
    MyContainer, vxetablebase, buschar, MyConfirmButton, MySearch, MySearchWindow, inputYunhan,
  },
  props: {
    grouplist: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      tableCols: tableCols,
      tableHandles: tableHandles,
      listLoading: false,
      pager: { OrderBy: "payAmont", IsAsc: false },
      productReportPddGoodsList: [],
      total: 0,
      summaryarry: {},
      fxData: {},
      selids: [],
      sels: [], // 列表选中列
      filter: {
        timerange: [],
        proCode: "",
        goodsCode: "",
        shopGoodsCode: "",
        exitProfitDes: null,
        dtlGroupBy: 2,
        payAmountDiffZero: 1
      },
      platformlist: platformlist,
      dialogGoodVisible: { visible: false, title: "", loading: false, data: {} },
      dialogDetailVisible: { visible: false, title: "", loading: false, filter: {}, data: {} },
      plat: null,
    }
  },
  async mounted() {
  },
  async created() {
  },
  methods: {
    toLinkTmDemo(row) {
      if (row.exitProfit_1 >= 0) {
        let params = {
          orderNo: row.orderNo,
          orderNoInner: row.orderNoInner,
          startDate: this.filter.startDate,
          endDate: this.filter.endDate,
          isTrue: true,
          plat: this.plat
        }
        setTimeout(() => {
          middlevue.$emit('toLinkTxDetailDayReport', params);
        }, 500);
        this.$emit('close')
      } else {
        let params = {
          startDate: this.filter.startDate,
          endDate: this.filter.endDate,
          proCode: row.proCode,
          isTrue: false,
          plat: 'tm'
        }
        setTimeout(() => {
          middlevue.$emit('toLinkTxDetailDayReport', params);
        }, 500);
        this.$emit('close')
      }
    },
    async onGoodsProfitShow(row) {
      if (!this.filter.dtlGroupBy || this.filter.dtlGroupBy == 0) {
        this.$message({
          message: '已经是明细状态时，不支持再次跳转',
          type: 'warning'
        });
        return;
      }
      let request = { ...this.filter };
      request.dtlGroupBy = 0;
      request.goodsCode = row.goodsCode;
      this.$showDialogform({
        path: `@/views/bookkeeper/reportday/productReportPddGoods.vue`,
        title: '编码利润',
        args: { row, request, vala: 2 },
        height: '650px',
        width: '90%',
      })
    },
    async loadData(args) {
      this.plat = args.request.plat
      this.filter.timerange = [args.request.startDate, args.request.endDate];
      this.filter.startDate = args.request.startDate;
      this.filter.endDate = args.request.endDate;
      this.filter.proCode = args.request.proCode;
      this.filter.goodsCode = args.request.goodsCode;
      if (args.vala == 1) {
        this.filter.exitProfitDes = '出仓正利润>0'
      } else if (args.vala == 2) {
        this.filter.exitProfitDes = '出仓负利润<0'
      } else {
        this.filter.exitProfitDes = null
      }
      if (args.request.dtlGroupBy || args.request.dtlGroupBy === 0)
        this.filter.dtlGroupBy = args.request.dtlGroupBy;
      await this.onSearch();
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = { OrderBy: "", IsAsc: false };
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      await this.onSearch();
    },
    async onSearch() {
      this.$refs.pager.setPage(1);
      await this.getList();
    },
    getCondition() {
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      else return false;
      let pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };
      if (!this.filter.startDate || !this.filter.endDate || !this.filter.proCode) {
        return false;
      }
      return params;
    },
    async getList() {
      const params = this.getCondition();
      if (params == false) {
        this.$message({ type: 'warning', message: '请填写日期和商品ID再查询' });
        return;
      }
      this.listLoading = true;
      const res = await getProductReportGoodsProfitTreePageList(params);
      this.listLoading = false;
      if (res?.success) {
        this.total = res.data?.total;
        this.productReportPddGoodsList = res.data?.list;
        this.summaryarry = res.data?.summary;
        this.fxData = res.data?.extData;
        this.$emit('transmit', this.fxData)
      }
      else {
        this.total = 0;
        this.productReportPddGoodsList = [];
        this.summaryarry = {};
        this.fxData = {};
      }
    },
    async showgoodchart(row) {
      this.pageLoading = true;
      const params = this.getCondition();
      if (params == false) {
        this.$message({ type: 'warning', message: '请填写日期和商品ID' });
        return;
      }
      params.shopGoodsCode = row.shopGoodsCode;
      this.dialogGoodVisible.loading = true;
      const res = await getProductReportGoodsProfitChart(params);
      this.dialogGoodVisible.loading = false;
      this.dialogGoodVisible.title = "【" + row.shopGoodsCode + "】编码利润";
      this.dialogGoodVisible.data = res;
      this.pageLoading = false;
      this.dialogGoodVisible.visible = true;
    },
    async showdetailchartsearch() {
      let spl = this.dialogDetailVisible.filter.proCode.split(",");
      if (spl.length > 3) {
        this.$message({ type: 'warning', message: '最多输入3个商品ID' });
        return;
      }
      const params = this.getCondition();
      if (params == false) {
        this.$message({ type: 'warning', message: '请填写日期和商品ID' });
        return;
      }
      params.proCode = this.dialogDetailVisible.filter.proCode;
      params.shopGoodsCode = this.dialogDetailVisible.filter.shopGoodsCode;
      this.dialogDetailVisible.loading = true;
      const res = await getProductReportDtlProfitChart(params);
      this.dialogDetailVisible.loading = false;
      this.dialogDetailVisible.data = res;
      if (this.$refs.dialogDetailVisiblebuschar) {
        this.$refs.dialogDetailVisiblebuschar.initcharts();
      }
    },
    async showdetailchart(row) {
      this.dialogDetailVisible.filter.proCode = this.filter.proCode;
      this.dialogDetailVisible.filter.shopGoodsCode = row.shopGoodsCode;
      this.dialogDetailVisible.title = "编码详情-毛三利润";
      this.pageLoading = true;
      await this.showdetailchartsearch();
      this.pageLoading = false;
      this.dialogDetailVisible.visible = true;
    },
    async callbackProCode(val) {
      this.dialogDetailVisible.filter.proCode = val;
    },
  },
}
</script>
<style lang="scss" scoped></style>
