<template>
    <MyContainer>
        <vxetablebase ref="table" :tableData="viewLogsData" :tableCols="viewsLogCols" :is-index="true" :that="that"
            style="width: 100%;height: 300px;  margin: 0" @sortchange='sortchange' class="detail">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import dayjs from 'dayjs'
import { getCustomMadeRecordDtlLogAsync } from '@/api/inventory/customNormsGoods'
const viewsLogCols = [
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'opearName', label: '操作人', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'opearTime', label: '时间', formatter: (row) => row.opearTime ? dayjs(row.opearTime).format('YYYY-MM-DD HH:mm:ss') : '' },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'logContent', label: '操作内容', },
]
export default {
    name: "viewLogs",
    components: {
        MyContainer, vxetablebase
    },
    props: {
        parentId: {
            type: String,
            default: ''
        },
        tableName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            viewsLogCols,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'opearTime',
                isAsc: false,
                parentId: null,
                tableName: ''
            },
            viewLogsData: [],
            total: 0,
            loading: false,
        }
    },
    async mounted() {
        this.ListInfo.parentId = this.parentId
        this.ListInfo.tableName = this.tableName
        await this.getList()
    },
    methods: {
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data: { list, total }, success } = await getCustomMadeRecordDtlLogAsync(this.ListInfo)
                if (success) {
                    this.viewLogsData = list
                    this.total = total
                } else {
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.$message.error('获取列表失败')
            } finally {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>