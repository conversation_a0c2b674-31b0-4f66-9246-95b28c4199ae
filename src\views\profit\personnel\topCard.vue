<template>
  <div>
    <el-row :gutter="5">
      <el-col :span="14">
        <el-card>
          <el-form class="ad-form-query" :inline="true">
            <el-form-item label="">

              <el-select ref="selectUpResId" v-model="chooseName" placeholder="请选择" :clearable="true"
                :collapse-tags="true" @clear="() => { filter.deptId = null }">
                <!-- <el-option v-for="item in deptList" :key="item" :label="item" :value="item" /> -->
                <el-option hidden value="一级菜单" :label="chooseName"></el-option>
                <el-tree :data="deptList" :props="defaultProps" :expand-on-click-node="false" :check-on-click-node="true"
                  @node-click="handleNodeClick">
                </el-tree>
              </el-select>
            </el-form-item>
            <el-form-item label="">
              <el-select v-model="filter.position" ref="selectPosit" placeholder="招聘岗位" clearable style="width: 100px"
                size="mini">
                <!-- <el-option v-for="item in postList" :label="item.positionName" :disabled="item.disabled" :value="item.positionName"></el-option> -->
                <el-option hidden value="一级菜单" :label="filter.position"></el-option>
                <el-tree style="width: 200px;" :data="postList" :props="postProps" :expand-on-click-node="false"
                  :check-on-click-node="true" @node-click="handleNodePostClick">
                </el-tree>
              </el-select>
            </el-form-item>
            <el-form-item>
              <!-- <el-button type="text" class="my-txtbtn dateBtn">周
                <el-date-picker v-model="filter.dateModel.week" @change="clickWeekSum" type="week"
                  :picker-options="pickOptionsWeek" format="yyyy-MM-dd">
                </el-date-picker>
              </el-button>
              <el-button type="text" class="my-txtbtn dateBtn">月
                <el-date-picker v-model="filter.dateModel.month" @change="clickMonthSum" type="month"
                  :picker-options="pickOptions" format="yyyy-MM-dd">
                </el-date-picker>
              </el-button> -->

              <el-date-picker v-model="filter.timeRange" type="daterange" unlink-panels range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                :picker-options="pickerOptions" style="width: 200px; margin-left: 10px;">
              </el-date-picker>

            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="onSearch(1)">查询</el-button>
            </el-form-item>
          </el-form>
          <el-card shadow="never">
            <el-row>
              <el-col :span="4">
                <div class="card-box">
                  <div class="box-value">{{ formattedTotalWaitOrderNum1 }}</div>
                  <div class="box-title">招聘计划</div>
                </div>
              </el-col>
              <el-col :span="4" :offset="1">
                <div class="card-box">
                  <div class="box-value">{{ formattedTotalWaitOrderNum2 }}</div>
                  <div class="box-title">招聘部门</div>
                </div>
              </el-col>
              <el-col :span="4" :offset="1">
                <div class="card-box">
                  <div class="box-value">{{ formattedTotalWaitOrderNum3 }}</div>
                  <div class="box-title">招聘岗位</div>
                </div>
              </el-col>
              <el-col :span="4" :offset="1">
                <div class="card-box">
                  <div class="box-value">{{ formattedTotalWaitOrderNum4 }}</div>
                  <div class="box-title">招聘专员</div>
                </div>
              </el-col>
              <el-col :span="4" :offset="1">
                <div class="card-box">
                  <div class="box-value">{{ formattedTotalWaitOrderNum5 }}%</div>
                  <div class="box-title">入职率</div>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">
                <div class="card-box">
                  <div class="box-value">{{ formattedTotalWaitOrderNum6 }}</div>
                  <div class="box-title">预计招聘人数</div>
                </div>
              </el-col>
              <!-- <el-col :span="4" :offset="1">
                <div class="card-box">
                  <div class="box-value">{{ cardData.yaoYueCountSum }}</div>
                  <div class="box-title">实际邀约人数</div>
                </div>
              </el-col> -->
              <el-col :span="4" :offset="1">
                <div class="card-box">
                  <div class="box-value">{{ formattedTotalWaitOrderNum7 }}</div>
                  <div class="box-title">实际到面人数</div>
                </div>
              </el-col>
              <el-col :span="4" :offset="1">
                <div class="card-box">
                  <div class="box-value">{{ formattedTotalWaitOrderNum8 }}</div>
                  <div class="box-title">面试通过人数</div>
                </div>
              </el-col>
              <el-col :span="4" :offset="1">
                <div class="card-box">
                  <div class="box-value">{{ formattedTotalWaitOrderNum9 }}</div>
                  <div class="box-title">入职人数</div>
                </div>
              </el-col>
              <el-col :span="4" :offset="1">
                <div class="card-box">
                  <div class="box-value">{{ formattedTotalWaitOrderNum10 }}</div>
                  <div class="box-title">当月离职人数</div>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-card>
      </el-col>
      <el-col :span="10">
        <el-card>
          <div id="top-card-colunm" style="width: 100%; height:281px; "></div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="10" style="margin-top: 5px;">
      <el-col :span="24">
        <el-card style="width: 100%;">
          <div style="text-align: right;">
            <el-button type="text" @click="() => { filter.dateGroupType = 0; onSearch() }">日</el-button>
            <el-button type="text" @click="() => { filter.dateGroupType = 1; onSearch() }">月</el-button>
          </div>
          <div id="top-card-line" style="width: 100%; height:300px; "></div>
        </el-card>
      </el-col>
    </el-row>
    <!-- <department ref="department"></department>
    <recruit ref="recruit"></recruit> -->
  </div>
</template>

<script>
import * as echarts from "echarts";
import { formatTime1 } from "@/utils";
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import {  getDingRolesTree ,AllDDDeptTreeNcWh} from '@/api/profit/personnel'
import { chartsHrHomePageTotal, employeeDimissionLineCharts, getPageRecruitmentPlan } from '@/api/profit/hr'
import recruit from "@/views/profit/personnel/recruit";
import department from "@/views/profit/personnel/department";
const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");
import bus from './bus.js';

export default {
  name: "topCard",//首页顶部内容
  components: {
    department, recruit
  },
  data () {
    return {
      filter: {
        position: null,//
        deptId: null,//
        startDate: null,//
        endDate: null,//
        timeRange: [startTime, endTime],
        dateGroupType: 0,
        dateModel: {
          month: null,
          week: null,
          day: null
        }
      },
      postProps: {
        children: 'roles',
        label: 'name'
      },
      cardData: {
      },
      lineData: {},
      // 下拉框选中节点id与名称
      chooseId: '',
      chooseName: '',
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      deptList: [],
      pickOptions: {
        disabledDate (time) {
          return time.getTime() > Date.now()
        }
      },
      pickOptionsWeek: {
        disabledDate (time) {
          return time.getTime() > Date.now()
        },
        firstDayOfWeek: 1
      },
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [{
          text: '今日',
          onClick (picker) {
            picker.$emit('pick', [new Date(), new Date()]);
          }
        }, {
          text: '今年至今',
          onClick (picker) {
            const end = new Date();
            const start = new Date(new Date().getFullYear(), 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近六个月',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setMonth(start.getMonth() - 6);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      colunmChart: null,
      colunmOptions: null,
      lineChart: null,
      lineOptions: null,
      postList: [],//岗位列表
      defaultProps: {
        children: 'childDeptList',
        label: 'name'
      },
      // testData: [{ w: 0, v: 'a' }, { w: 1, v: 'b' }, { w: 2, v: 'c' }, { w: 3, v: 'd' }, { w: 4, v: 'e' }, { w: 5, v: 'f' },]
    };
  },
  created () {
    // let w = this.testData.map((item) => {
    //   return item.w;
    // }) w只获取test中w的值
    // console.log(w)
  },
  destroyed () {
    window.removeEventListener('resize', this.handleResizeChart);
  },
  async mounted () {
    window.addEventListener('resize', this.handleResizeChart);
    await this.setDeptCondition();
    this.getDataList();
    this.initChart();
    this.onSearch(1);

  },
  computed: {
        formattedTotalWaitOrderNum1() {
            return this.cardData.planCountSum !== undefined && this.cardData.planCountSum !== null ? this.formatNumber(this.cardData.planCountSum) : '';
        },
        formattedTotalWaitOrderNum2() {
            return this.cardData.deptCountSum !== undefined && this.cardData.deptCountSum !== null ? this.formatNumber(this.cardData.deptCountSum) : '';
        },  
        formattedTotalWaitOrderNum3() {
            return this.cardData.positionCountSum !== undefined && this.cardData.positionCountSum !== null ? this.formatNumber(this.cardData.positionCountSum) : '';
        },
        formattedTotalWaitOrderNum4() {
            return this.cardData.recruiterCountSum !== undefined && this.cardData.recruiterCountSum !== null ? this.formatNumber(this.cardData.recruiterCountSum) : '';
        },
        formattedTotalWaitOrderNum5() {
            return this.cardData.employeeRate !== undefined && this.cardData.employeeRate !== null ? this.formatNumber(this.cardData.employeeRate) : '';
        },
        formattedTotalWaitOrderNum6() {
            return this.cardData.recruitmentCountSum !== undefined && this.cardData.recruitmentCountSum !== null ? this.formatNumber(this.cardData.recruitmentCountSum) : '';
        },
        formattedTotalWaitOrderNum7() {
            return this.cardData.comeCountSum !== undefined && this.cardData.comeCountSum !== null ? this.formatNumber(this.cardData.comeCountSum) : '';
        },
        formattedTotalWaitOrderNum8() {
            return this.cardData.testPassCountSum !== undefined && this.cardData.testPassCountSum !== null ? this.formatNumber(this.cardData.testPassCountSum) : '';
        },
        formattedTotalWaitOrderNum9() {
            return this.cardData.employeeCountSum !== undefined && this.cardData.employeeCountSum !== null ? this.formatNumber(this.cardData.employeeCountSum) : '';
        },
        formattedTotalWaitOrderNum10() {
            return this.cardData.dimissionCountSum !== undefined && this.cardData.dimissionCountSum !== null ? this.formatNumber(this.cardData.dimissionCountSum) : '';
        },
   
  },
  methods: {
    formatNumber(num) {
            // 去掉小数位并添加千位分隔符
            return num > 100 ? Math.floor(num).toLocaleString() : num.toLocaleString();
        },
    handleNodePostClick (data) {
      if (!data.disabled) {
        this.filter.position = data.name;
        this.$refs.selectPosit.blur();
      }
    },
    async getDataList (ddDeptId) {
      const params = {
        closeStatus: 0,//计划状态:-1删除、0进行中、1已完成
        ddDeptId: ddDeptId,//招聘部门
        currentPage: 1,
        pageSize: 50,
        positionName: null,//岗位名称
        recruiterIds: [],//招聘专员
        closeReason: null,//完成原因
      };
      const res = await getDingRolesTree();
      this.postList = res.data;
      this.postList.forEach(item => {
        item.disabled = true;
      })
    },
    // 节点点击事件
    handleNodeClick (data) {
      // 配置树形组件点击节点后，设置选择器的值，配置组件的数据
      this.chooseName = data.name
      this.filter.deptId = data.dept_id
      // 选择器执行完成后，使其失去焦点隐藏下拉框效果
      this.$refs.selectUpResId.blur()
    },
    // 选择器配置可以清空选项，用户点击清空按钮触发
    handleClear () {
      // 将选择器的值置空
      this.chooseName = ''
      this.filter.v1 = ''
    },
    //设置部门下拉
    async setDeptCondition (val) {
      await AllDDDeptTreeNcWh().then(res => {
        if (res.success) {
          this.deptList = res.data.childDeptList;
        } else {
          this.$message({ message: res.msg, type: "danger" });
        }
      })
    },
    //单击周
    async clickWeekSum () {
      var start = dayjs(this.filter.dateModel.week).subtract(1, 'day');
      var end = dayjs(start).add(6, "day");
      start = formatTime1(start, "yyyy-MM-dd")
      end = formatTime1(end, "yyyy-MM-dd")
      this.filter.timeRange = [start, end];
      await this.onSearch();
    },
    //单击月
    async clickMonthSum () {
      var start = this.filter.dateModel.month;
      var end = dayjs(start).endOf("month");
      start = formatTime1(start, "yyyy-MM-dd")
      end = formatTime1(end, "yyyy-MM-dd")
      this.filter.timeRange = [start, end];
      await this.onSearch();
    },
    // 获取数据
    onSearch (e) {
      if (this.filter.timeRange) {
        this.filter.startDate = this.filter.timeRange[0];
        this.filter.endDate = this.filter.timeRange[1]
      }
      this.getCardData();
      this.getChartData();
      if (e) {
        bus.$emit('filter', this.filter);
      }
    },
    getCardData () {
      chartsHrHomePageTotal(this.filter).then(res => {
        if (res.success) {
          this.cardData = res.data;
          let i = 0;
          for (const prop in this.cardData) {
            this.colunmOptions.series[0].data[i] = this.cardData[prop];
            i++;
          }
          this.colunmChart.setOption(this.colunmOptions);
        }
      })
    },
    getChartData () {
      employeeDimissionLineCharts(this.filter).then(res => {
        if (res.success) {
          this.lineData = res.data;
          this.lineOptions.xAxis.data = res.data.xAxis;
          this.lineOptions.series = res.data.series
          this.lineChart.setOption(this.lineOptions);
        }
      })
    },
    initChart () {
      let that = this
      //柱状图
      this.colunmChart = echarts.init(document.getElementById('top-card-colunm'))
      // 配置参数
      this.colunmOptions = {
        tooltip: {
          trigger: 'axis',
        },
        grid: {
          top: "4%",
          right: "2%",
          bottom: "15%",
          left: "5%",
        },
        xAxis: {
          type: 'category',
          data: ['招聘计划', '招聘部门', '招聘岗位', '招聘专员', '入职率%', '预计招聘', '实际邀约', '面试通过', '入职', '离职'],
          axisLabel: {
            interval: 0,
            rotate: 20,
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [],
            type: 'bar'
          }
        ]
      }
      // 使用 setOption 方法将配置项设置为实例的属性
      this.colunmChart.setOption(this.colunmOptions);

      //双折线图
      this.lineChart = echarts.init(document.getElementById('top-card-line'));
      // 配置参数
      this.lineOptions = {
        tooltip: {
          trigger: 'axis',
        },
        title: {
          show: true,    // 是否显示标题组件,（true/false）
          // text: '入职/离职人数曲线图',   // 主标题文本，支持使用\n换行
          subtext: '入职/离职人数曲线图',
          bottom: '2%',
          left: '40%'
        },
        grid: {
          left: "2%",
          top: "2%",
          right: "2%",
          bottom: '15%'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: [],
        },
        yAxis: {
          type: 'value',
        },
        series: [],
      };
      // 使用 setOption 方法将配置项设置为实例的属性
      this.lineChart.setOption(this.lineOptions);

    },
    // 重绘
    handleResizeChart () {
      if (this.colunmChart) {
        this.colunmChart.resize();
      }
      if (this.lineChart) {
        this.lineChart.resize();
      }
    },
  }

}
</script>

<style  lang="scss" scoped>
.card-box {
  min-width: 100px;
  height: 50px;
  background-color: #f5faff;
  padding: 20px;
  text-align: center;
  float: left;
  margin: 5px;
  border-radius: 8px;

  .box-value {
    font-size: 22px;
    color: #409eff;
  }

  .box-title {
    font-size: 14px;
    color: #409eff;
  }

  .my-txtbtn {
    margin-left: 15px;
    margin-right: 15px;
  }

  .dateBtn {
    position: relative !important;
  }

  .dateBtn .el-date-editor {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
  }

  .row-condition {
    margin-top: 10px;
    margin-bottom: 20px;
  }
}

.dateBtn {
  position: relative !important;
}

.dateBtn .el-date-editor {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
}
</style>