<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <!-- <el-date-picker v-model="ListInfo.calculateMonth" unlink-panels range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期"  type="month" style="width: 250px;margin-right: 5px;" :clearable="false"
            :value-format="'yyyy-MM'" >
          </el-date-picker> -->
                <el-date-picker v-model="ListInfo.year" unlink-panels range-separator="至" placeholder="年份"
                    end-placeholder="结束日期" type="year" style="width: 250px;margin-right: 5px;" :clearable="false"
                    :value-format="'yyyy'">
                </el-date-picker>

                <el-select v-model="ListInfo.regionArr" style="width: 200px;" multiple collapse-tags placeholder="区域" class="publicCss" clearable>
                    <el-option v-for="item in districtList" :key="item" :label="item" :value="item" />
                </el-select>

                <el-select v-model="ListInfo.deptArr" style="width: 200px;" multiple collapse-tags placeholder="部门" class="publicCss" clearable>
                    <el-option v-for="item in districtList2" :key="item" :label="item" :value="item" />
                </el-select>

                <!-- <el-input v-model.trim="ListInfo.orderNo" placeholder="退件快递单号" maxlength="50" clearable class="publicCss" />
          <el-input v-model.trim="ListInfo.orderNoInner" placeholder="原订单号" maxlength="50" clearable class="publicCss" /> -->
                <el-button type="primary" @click="getList('search')">查询</el-button>
                <el-button type="primary" @click="startImport">导入</el-button>
                <el-button type="primary" @click="downExcel">模板下载</el-button>
                <el-button type="primary" @click="exportExcel('search')">导出</el-button>
                <!-- <el-button type="primary" @click="saveBane('search')">存档</el-button>
          <div style="color: red; margin-left: 5px;">
            存档时间：{{timeCundang??'-'}}
          </div> -->

            </div>
        </template>
        <!-- :footer-method="footerMethod"  :footer-data="footerData" -->
        <vxe-table v-show="!exportloading ? true : false" border show-footer width="100%" height="100%" ref="newtable"
            :row-config="{ height: 40 }" show-overflow :loading="loading" :column-config="{ resizable: true }"
            :merge-footer-items="mergeFooterItems" :footer-data="footerData" :span-method="mergeRowMethod"
            :row-class-name="rowClassName" :footer-cell-class-name="footerCellClassName" :data="tableData">


            <vxe-column field="region" max-width="100%" title="区域"></vxe-column>
            <vxe-column field="dept" max-width="100%" title="部门"></vxe-column>

            <vxe-column field="violationDesc" width="115" title="具体违纪情况"></vxe-column>

            <vxe-column field="year" width="80" title="年份"></vxe-column>
            <vxe-column field="oneMonthCount" width="80" title="1月份"></vxe-column>
            <vxe-column field="twoMonthCount" width="80" title="2月份"></vxe-column>
            <vxe-column field="threeMonthCount" width="80" title="3月份"></vxe-column>
            <vxe-column field="fourMonthCount" width="80" title="4月份"></vxe-column>
            <vxe-column field="fiveMonthCount" width="80" title="5月份"></vxe-column>
            <vxe-column field="sixMonthCount" width="80" title="6月份"></vxe-column>
            <vxe-column field="sevenMonthCount" width="80" title="7月份"></vxe-column>
            <vxe-column field="eightMonthCount" width="80" title="8月份"></vxe-column>
            <vxe-column field="nighMonthCount" width="80" title="9月份"></vxe-column>
            <vxe-column field="tenMonthCount" width="80" title="10月份"></vxe-column>
            <vxe-column field="elevenMonthCount" width="80" title="11月份"></vxe-column>
            <vxe-column field="twelveMonthCount" width="80" title="12月份"></vxe-column>

            <vxe-column field="totalCount" width="80" title="总计"></vxe-column>

            <vxe-column title="操作" footer-align="left" width="120px" fixed="right">
                <template slot-scope="scope">
                    <el-button type="text" size="mini"
                    @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
                    <el-button type="text" size="mini" style="color:red"

                    @click="handleRemove(scope.$index, scope.row)">删除</el-button>
                </template>
            </vxe-column>
        </vxe-table>
        <vxe-table v-show="!exportloading ? false : true" border show-footer width="100%" height="100%" ref="newtable"
            :row-config="{ height: 40 }" show-overflow :loading="loading" :column-config="{ resizable: true }"
            :merge-footer-items="mergeFooterItems" :footer-method="footerMethod" :span-method="mergeRowMethod"
            :row-class-name="rowClassName" :footer-cell-class-name="footerCellClassName" :data="tableData">
            <vxe-column field="region" width="110" title="区域"></vxe-column>
            <vxe-column field="dept" width="85" title="部门"></vxe-column>

            <vxe-column field="violationDesc" width="115" title="具体违纪情况"></vxe-column>

            <vxe-column field="year" width="55" title="年份"></vxe-column>
            <vxe-column field="oneMonthCount" width="60" title="1月份"></vxe-column>
            <vxe-column field="twoMonthCount" width="60" title="2月份"></vxe-column>
            <vxe-column field="threeMonthCount" width="60" title="3月份"></vxe-column>
            <vxe-column field="fourMonthCount" width="60" title="4月份"></vxe-column>
            <vxe-column field="fiveMonthCount" width="60" title="5月份"></vxe-column>
            <vxe-column field="sixMonthCount" width="60" title="6月份"></vxe-column>
            <vxe-column field="sevenMonthCount" width="60" title="7月份"></vxe-column>
            <vxe-column field="eightMonthCount" width="60" title="8月份"></vxe-column>
            <vxe-column field="nighMonthCount" width="60" title="9月份"></vxe-column>
            <vxe-column field="tenMonthCount" width="60" title="10月份"></vxe-column>
            <vxe-column field="elevenMonthCount" width="60" title="11月份"></vxe-column>
            <vxe-column field="twelveMonthCount" width="60" title="12月份"></vxe-column>

            <vxe-column field="totalCount" width="80" title="总计"></vxe-column>


        </vxe-table>
        <el-drawer title="编辑" :visible.sync="dialogVisibleEdit" size="25%">
            <departmentEdit ref="departmentEdit" v-if="dialogVisibleEdit" :editInfo="editInfo" @search="closeGetlist"
                @cancellationMethod="dialogVisibleEdit = false" />
        </el-drawer>
        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
            <span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                    :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { downloadLink } from "@/utils/tools.js";
// import {
//     warehouseInspectionSituationPage,
//     warehouseInspectionSituationImport, warehouseInspectionSituationRemove
// } from '@/api/people/peoplessc.js';
import {
    warehouseInspectionSituationPage, dimissionManageArchive,
    warehouseInspectionSituationImport, warehouseInspectionSituationRemove
} from '@/api/people/peoplessc.js';
import departmentEdit from "./departmentEdit.vue";
import checkPermission from '@/utils/permission'
const tableCols = [
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '审批状态', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNoInner', label: '商品编码', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'importTime', label: '商品名称', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'planSendTime', label: '原价', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'timePay', label: '现价', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight', label: '进货数量', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderStatus', label: '涨价原因', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'seriesName', label: '添加日期', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCount', label: '涨价日期', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '添加人', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '岗位', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '分公司', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人组长', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, departmentEdit
    },
    data() {
        const tableData = [
            //   { id: 10001, calculateMonth: '12月', regionName: '义务', name: 'Test1', nickname: 'T1', role: 'Develop', sex: 'Man', age: 28, address: 'test abc' },
            //   { id: 10002, calculateMonth: '12月', regionName: '义务', name: 'Test2', nickname: 'T2', role: 'Test', sex: 'Women', age: 22, address: 'Guangzhou' },
            //   { id: 10003, calculateMonth: '12月', regionName: '南昌', name: 'Test3', nickname: 'T3', role: 'PM', sex: 'Man', age: 32, address: 'Shanghai' },
            //   { id: 10004, calculateMonth: '12月', regionName: '南昌', name: 'Test4', nickname: 'T4', role: 'Designer', sex: 'Women', age: 23, address: 'test abc' },
            //   { id: 10005, calculateMonth: '12月', regionName: '北京', name: 'Test5', nickname: 'T5', role: 'Develop', sex: 'Women', age: 30, address: 'Shanghai' },
            //   { id: 10006, calculateMonth: '12月', regionName: '北京', name: 'Test6', nickname: 'T6', role: 'Designer', sex: 'Women', age: 21, address: 'test abc' },
            //   { id: 10007, calculateMonth: '12月', regionName: '深圳', name: 'Test7', nickname: 'T7', role: 'Test', sex: 'Man', age: 29, address: 'test abc' },
            //   { id: 10008, calculateMonth: '12月', regionName: '深圳', name: 'Test8', nickname: 'T8', role: 'Develop', sex: 'Man', age: 35, address: 'test abc' }
        ]
        const footerData = [
            //   { calculateMonth: '12月', regionName: '办公室合计', name: '办公室合计', role: '33', rate: '56' },
            //   { calculateMonth: '12月', regionName: '仓储合计', name: '仓储合计', role: 'bb', rate: '56' },
            //   { calculateMonth: '12月', regionName: '全区域合计', name: '全区域合计', role: 'bb', rate: '1235' }
        ]
        const mergeFooterItems = [
            //   { row: 0, col: 0, rowspan: 3, colspan: 1 },
            //   { row: 0, col: 1, rowspan: 0, colspan: 2 },
            //   { row: 1, col: 1, rowspan: 1, colspan: 2 },
            //   { row: 2, col: 1, rowspan: 2, colspan: 2 }
        ]
        return {
            downloadLink,
            dialogVisibleEdit: false,
            editInfo: {},
            fileList: [],
            dialogVisible: false,
            districtList: [],
            districtList2: [],

            timeCundang: '',
            tableData,
            footerData,
            mergeFooterItems,
            somerow: 'dept,region,area,violationType',
            that: this,
            exportloading: false,
            ListInfo: {
                regionArr: [],
                calculateMonthArr: [
                    dayjs().subtract(0, 'month').format('YYYY-MM'),
                    dayjs().subtract(0, 'month').format('YYYY-MM')],
                year: dayjs().subtract(0, 'year').format('YYYY')
                //   currentPage: 1,
                //   pageSize: 50,
                //   orderBy: null,
                //   isAsc: false,
                //   startTime: null,//开始时间
                // ledgerDate: dayjs().subtract(0, 'month').format('YYYY-MM'),//结束时间
            },
            timeRanges: [],
            tableCols,
            summaryarry: {},
            total: 0,
            loading: false,
            pickerOptions,
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        footerMethod({ columns, data }) {
            const sums = [];
            if (!this.footerData)
                return sums
            let newfield = columns.map(item => item.field)
            let newfooterdata = [];
            this.footerData.forEach((item, index) => {
                let newarr2 = [];
                newfield.forEach((item2, index2) => {
                    newarr2.push(item[item2])
                })
                newfooterdata.push(newarr2)
            })

            return newfooterdata;
        },
        rowClassName(event) {

            if (event.row.violationType == '考勤小计' ||
                event.row.violationType == '纪律小计' ||
                event.row.violationType == '违纪次数合计'
            ) {
                return 'row-green'
            }
            if (event.row.violationType == '违纪人数合计' ||
                event.row.violationType == '人均违纪次数' ||
                event.row.violationType == '当月考勤人数'
            ) {
                return 'row-yellow1'
            }
            if (event.row.violationType == '当月违纪人数占比'
            ) {
                return 'row-yellow2'
            }

            return null
        },
        footerCellClassName(event) {
            if (event.row.violationDesc == '违规违纪占比' ||
                event.row.violationDesc == '违规违纪'
            ) {
                return 'row-yellow3'
            }
            if (event.row.violationDesc == '卫生问题占比' ||
                event.row.violationDesc == '卫生问题'
            ) {
                return 'row-yellow4'
            }
            if (event.row.violationDesc == '消防问题占比' ||
                event.row.violationDesc == '消防问题'
            ) {
                return 'row-yellow7'
            }
            if (event.row.violationDesc == '整理整顿占比' ||
                event.row.violationDesc == '整理整顿'
            ) {
                return 'row-yellow6'
            }
            if (event.row.region == '合计'
            ) {
                return 'row-yellow5'
            }

            return null
        },
        //上传文件
        onUploadRemove(file, fileList) {
            this.fileList = []
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            this.dialogVisible = false;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            const form = new FormData();
            form.append("file", item.file);
            form.append("isArchive", checkPermission("ArchiveStatusEditing"));
            form.append("calculateMonth", this.ListInfo.calculateMonth);
            var res = await warehouseInspectionSituationImport(form);
            if (res?.success) {
                this.$message({ message: res.msg, type: "success" });
            }
            this.uploadLoading = false
            this.dialogVisible = false;
            await this.getList()
        },
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        //导入弹窗
        startImport() {
            this.fileList = []
            this.dialogVisible = true;
        },
        downExcel() {
            //下载excel
            downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250514/1922544182334242816.xlsx', '稽查情况数据导入模板.xlsx');
        },
        async saveBane() {
            this.$confirm('是否存档？存档后不可修改！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { data, success } = await dimissionManageArchive(this.ListInfo)
                if (!success) {
                    return;
                }
                this.getList();
                this.$message.success('保存存档成功！')

            }).catch(() => {
                // this.$message.error('取消')
            });
        },
        async exportExcel() {
            await this.$nextTick(() => {
                this.exportloading = true;
                this.$refs.newtable.exportData({ filename: '稽查情况数据', sheetName: 'Sheet1', type: 'xlsx' })
            })
            await this.$nextTick(() => {
                this.exportloading = false;
            })
        },
        closeGetlist() {
            this.dialogVisibleEdit = false;
            this.getList()
        },
        handleEdit(index, row) {
            this.editInfo = row;
            this.dialogVisibleEdit = true;
        },
        async handleRemove(index, row) {
            this.$confirm('是否删除！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.editInfo = row;
                // this.editInfo.calculateMonthStart = this.ListInfo.calculateMonthArr[0]
                // this.editInfo.calculateMonthEnd = this.ListInfo.calculateMonthArr[1]
                this.loading = true
                const { data, success } = await warehouseInspectionSituationRemove({ ids: row.id })
                this.loading = false
                if (success) {
                    this.$message.success('删除成功')
                    this.getList();
                } else {
                    this.$message.error('删除失败')
                }
            }).catch(() => {

            });
        },
        // 通用行合并函数（将相同多列数据合并为一行）
        mergeRowMethod({ row, _rowIndex, column, visibleData }) {
          const fields = this.somerow.split(',')
          const cellValue = row[column.property]
          if (cellValue && fields.includes(column.property)) {
            const prevRow = visibleData[_rowIndex - 1]
            let countRowspan = 1
            // 【核心】如果当前字段是 dept，还需要同时判断 region 相等
            const isSameGroup = (a, b) => {
              if (!a || !b) return false
              if (column.property === 'dept') {
                return a.dept === b.dept && a.region === b.region
              }
              return a[column.property] === b[column.property]
            }
            // 判断上一行是否属于同一组，如果是则 return 0 合并到上一行
            if (isSameGroup(row, prevRow)) {
              return { rowspan: 0, colspan: 0 }
            }
            // 向下查找有多少行可合并
            for (let i = _rowIndex + 1; i < visibleData.length; i++) {
              const nextRow = visibleData[i]
              if (isSameGroup(row, nextRow)) {
                countRowspan++
              } else {
                break
              }
            }
            if (countRowspan > 1) {
              return { rowspan: countRowspan, colspan: 1 }
            }
          }
        },
        async changeTime(e) {
            this.ListInfo.startTime = e ? e[0] : null
            this.ListInfo.endTime = e ? e[1] : null
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        // async exportProps() {
        //     const { data } = await exportStatData(this.ListInfo)
        //     const aLink = document.createElement("a");
        //     let blob = new Blob([data], { type: "application/vnd.ms-excel" })
        //     aLink.href = URL.createObjectURL(blob)
        //     aLink.setAttribute('download', '汇总数据' + new Date().toLocaleString() + '.xlsx')
        //     aLink.click()
        // },
        async getList(type) {
            // if (type == 'search') {
            //   this.ListInfo.currentPage = 1
            //   this.$refs.pager.setPage(1)
            // }
            // if (this.ListInfo.calculateMonthArr && this.ListInfo.calculateMonthArr.length > 0) {
            //     this.ListInfo.monthDate = this.ListInfo.calculateMonthArr.join(',')
            // }
            if (this.ListInfo.deptArr && this.ListInfo.deptArr.length > 0) {
                this.ListInfo.dept = this.ListInfo.deptArr.join(',')
            } else {
                this.ListInfo.dept = ''
            }
            if (this.ListInfo.regionArr && this.ListInfo.regionArr.length > 0) {
                this.ListInfo.region = this.ListInfo.regionArr.join(',')
            } else {
                this.ListInfo.region = ''
            }
            this.loading = true
            const { data, success } = await warehouseInspectionSituationPage(this.ListInfo)
            if (success) {
                //   data.list.map((row)=>{
                //     row.twoYearRate =  row.twoYearRate === 100 ? "100%" : row.twoYearRate ? Number(row.twoYearRate).toFixed(2) + "%" : ''
                //     row.notAdaptedWorkRate =  row.notAdaptedWorkRate === 100 ? "100%" : row.notAdaptedWorkRate ? Number(row.notAdaptedWorkRate).toFixed(2) + "%" : ''
                //     row.hasWorkRate =  row.hasWorkRate === 100 ? "100%" : row.hasWorkRate ? Number(row.hasWorkRate).toFixed(2) + "%" : ''

                //     row.familyReasonsRate =  row.familyReasonsRate === 100 ? "100%" : row.familyReasonsRate ? Number(row.familyReasonsRate).toFixed(2) + "%" : ''
                //     row.bodyReasonsRate =  row.bodyReasonsRate === 100 ? "100%" : row.bodyReasonsRate ? Number(row.bodyReasonsRate).toFixed(2) + "%" : ''
                //     row.eliminateRate =  row.eliminateRate === 100 ? "100%" : row.eliminateRate ? Number(row.eliminateRate).toFixed(2) + "%" : ''
                //   })
                // let aaa = ['specificViolations', 'violationType', 'region','year'];
                // data.list.map((item) => {
                //     if (item.violationType.indexOf('占比') > -1) {
                //         Object.keys(item).map((index) => {
                //             aaa.indexOf(index) > -1 || item[index] == 0 || !item[index] ? '' : item[index] = item[index] + '%'
                //         })
                //     }
                // })


                this.tableData = data.list
                //   this.total = data.total
                //   this.summaryarry = data.summary
                // data.summary.regionName = '合计';
                // data.summary.calculateMonth = data.list.length>0? data.list[0].calculateMonth : '';
                // this.timeCundang = data.list.length>0?data.list[0].archiveTime:''
                // if (data.summary) {
                //   this.timeCundang = data.summary.archiveTime
                // }

                // let zhanbi = ['eliminateRate','bodyReasonsRate','familyReasonsRate','hasWorkRate','notAdaptedWorkRate'];

                // zhanbi.map((item)=>{
                //     data.summary[item] = data.summary[item]?data.summary[item]+'%': ''
                // })


                // data.summary.map((item) => {
                //     if (item.violationType.indexOf('占比') > -1) {
                //         Object.keys(item).map((index) => {
                //             aaa.indexOf(index) > -1 || item[index] == 0 || !item[index] ? '' : item[index] = item[index] + '%'
                //         })
                //     }
                // })

                this.footerData = data.summary
                const fieldsToFormat = [
                    "violationDesc",
                    "region",
                    "dept",
                    'year',
                ];
                data.summary.map((itemm) => {
                    Object.keys(itemm).forEach((key) => {
                        if ((!fieldsToFormat.includes(key))&&itemm['violationDesc']&&itemm['violationDesc'].indexOf('占比')>-1) {
                            itemm[key] = itemm[key] + '%'
                        }
                    });
                })



                //

                //取列表中的区域
                const newDistricts = this.tableData.map(item => item.region).filter(district => district !== undefined && district !== null && district.indexOf('小计') == -1 && district.indexOf('占比') == -1)
                this.districtList = Array.from(new Set([...this.districtList, ...newDistricts]));

                const newDistricts1 = this.tableData.map(item => item.dept).filter(district => district !== undefined && district !== null && district.indexOf('小计') == -1 && district.indexOf('占比') == -1)
                this.districtList2 = Array.from(new Set([...this.districtList2, ...newDistricts1]));

                this.loading = false
            } else {
                this.loading = false
                this.$message.error('获取列表失败')
            }
        },
        formatNumberWithThousandSeparator(value) {
            if (value === null || value === undefined) return value;
            return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 150px;
        margin-right: 5px;
    }
}

:deep(.vxe-header--column) {
    background: #00937e;
    color: white;
    font-weight: 600;
}

// :deep(.vxe-footer--row){
//     background: #00937e;
//     color: white;
//     font-weight: 600;
// }
:deep(.row-green) {
    background-color: #e0eed6;
    // color: #fff;
}

:deep(.row-yellow1) {
    background-color: #f8e2d3;
}

:deep(.row-yellow2) {
    background-color: #d1f1ef;
    // color: #fff;
}

:deep(.vxe-footer--row){
    height: 30px !important;
}
:deep(.vxe-footer--column) {
    padding: 0 !important;
}

:deep(.row-yellow3) {
    background-color: #f7d7dc;
}


:deep(.row-yellow4) {
    background-color: #f9e6e9;
}

:deep(.row-yellow5) {
    background-color: #00937e;
    color: white;
}
:deep(.row-yellow6) {
    background-color: #e3f1d9;
}
:deep(.row-yellow7) {
    background-color: #d3f4f1;
}
</style>
