<template>
    <my-container v-loading="pageLoading">
      <template #header>
         <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
           <!-- <el-form-item label="创建时间:">
              <el-date-picker style="width: 330px"
                  v-model="filter.timerange"
                  type="datetimerange"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
             ></el-date-picker>
           </el-form-item>  -->
           <el-form-item label="产品ID:">
            <el-input clearable   maxlength="300" v-model.trim="filter.ProCode" placeholder="产品ID" style="width: 240px"/>
           </el-form-item>
          <!-- <el-form-item label="是否已处理:">
            <el-select v-model="filter.isCurrentSelf" placeholder="请选择" style="width: 100px">
              <el-option label="全部" value></el-option>
              <el-option label="是" value="false"></el-option>
              <el-option label="否" value="true"></el-option>
            </el-select>
          </el-form-item>
           <el-form-item label="是否已归档:">
            <el-select v-model="filter.isFinish" placeholder="请选择" style="width: 100px">
              <el-option label="全部" value></el-option>
              <el-option label="是" value="true"></el-option>
              <el-option label="否" value="false"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否合并:">
            <el-select v-model="filter.isMerge" placeholder="请选择" style="width: 100px">
              <el-option label="全部" value></el-option>
              <el-option label="是" value="true"></el-option>
              <el-option label="否" value="false"></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-form-item>
                <el-button type="danger" @click="addMonitoring">添加监控产品</el-button>
              </el-form-item>
          </el-form-item>
        </el-form> 
      </template>
      <div v-if="cestable" style="height: 750px;">
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='true'
        :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" :rowkey="keynum"
        >
      </ces-table>
       </div>  
      <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
      </template>
      <el-drawer title="添加监控" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="addVisible" 
      direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
<form-create style="height: 90px;" :rule="autoform.rule1" v-model="autoform.fApi" :option="autoform.options"/>
<div class="drawer-footer">
<el-button @click.native="addVisible = false">取消</el-button>
<my-confirm-button type="submit" :loading="editLoading" @click="onaddSubmit" />
</div>
</el-drawer>

<el-drawer title="编辑监控产品" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editVisible" 
                direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
       <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
       <div class="drawer-footer">
        <el-button @click.native="editVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="editLoading" @click="onEditSubmit" />
      </div>
    </el-drawer>

<el-dialog title="趋势图" :visible.sync="dialogMapVisible.visible" width="80%">
          <span>
            <template>
              <el-date-picker
                v-model="filter1.timerange1"
                type="datetimerange" 
                :picker-options="pickerOptions1"
                format="yyyyMMdd"
                value-format="yyyyMMdd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="onSearch1"
              ></el-date-picker>
           
        </template> 
        </span>

    <span>     
       <buschar v-if="dialogMapVisible.visible" ref="buschar" :analysisData="dialogMapVisible.data"></buschar>
   </span>
 
   <span slot="footer" class="dialog-footer">
     <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
   </span>
 </el-dialog>

 <el-dialog title="详情" :visible.sync="dialogVisible" height="300px" v-dialogDrag>
  <el-row>
  
  </el-row>
   <el-row>
      <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="24">
       
         <gcgoodsmonitoringdetail ref="gcgoodsmonitoringdetail" :filter="gcgoodsmonitoringdetail.filter" style="height:600px;"></gcgoodsmonitoringdetail>
      </el-col>
   
  </el-row>
 
</el-dialog>
     
    </my-container>
  </template>
  
  <script>
   //import { Descriptions } from 'element-ui';
  //import ElementUI from 'element-ui';
  import formCreate from '@form-create/element-ui'
  import FcEditor from "@form-create/component-wangeditor";
  //import {createReport,createMergeReport,flowReport,finishReport,getReport,getReportMerge,pageReport} from '@/api/operatemanage/monitorreport'
  import {getGoodsMonitoring,editGoodsMonitoring,changeGoodsMonitoringStatus,getGoodsMonitoringChart,deleteGongCompeteGoods} from '@/api/operatemanage/competegoods'
  //getDirectorAndGroupUserList
  import gcgoodsmonitoringdetail from '@/views/operatemanage/gcgoodsmonitoringdetail'
  import buschar from '@/components/Bus/buschar'
  import MyContainer from '@/components/my-container'
  import MyConfirmButton from '@/components/my-confirm-button'
  import cesTable from "@/components/Table/table.vue";
  import {formatYesornoBool,formatLink,htmlDecode,formatLinkProCode} from "@/utils/tools";
  import {ruleDirectorUser,ruleDirectorGroupUser} from '@/utils/formruletools'
  const tableCols =[
        {istrue:true,prop:'proCode',label:'产品ID', width:'120',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(8,row.proCode)},
        {istrue:true,prop:'title',label:'产品名称', width:'auto'},
        {istrue:true,prop:'pic',label:'产品图片', width:'80',type:"image"},
        {istrue:true,prop:'isCompute',label:'是否监控', width:'100',sortable:'custom',type:'switch', 
        change:(row,that)=>that.changeStatus(row)},
         {istrue:true,prop:'createdUserName',label:'监控人', width:'100'},
        {istrue:true,prop:'controllerName',label:'抄送人', width:'160'},
        {istrue:true,prop:'warningMinNum',label:'预警最小值', width:'100'},
        {istrue:true,prop:'warningMaxNum',label:'预警最大值', width:'100'},
        {istrue:true,type:'button', width:'55',label:'操作', width:'100',btnList:[{label:"编辑",handle:(that,row)=>that.onHand(row)},{label:"删除",handle:(that,row)=>that.deleteprocode(row)}]},
        // {istrue:true,prop:'currentUserName',label:'支付买家数', width:'100'},
        // {istrue:true,prop:'arriveDate',label:'日期时间', width:'160'},
        {istrue:true,display:true,label:'趋势图', style:"color:red;cursor:pointer;",width:70,formatter:(row)=>'趋势图', type:'click',handle:(that,row)=>that.showchart(row)},  
        {istrue:true,display:true,label:'数值表格', style:"color:blue;cursor:pointer;",width:80,formatter:(row)=>'数值表格', type:'click',handle:(that,row)=>that.showTable(row)}, 
       
       ];
  const tableHandles1=[
       
        ];
  export default {
    name: 'Roles',
    components: {cesTable, MyContainer, MyConfirmButton,buschar,gcgoodsmonitoringdetail},
    data() {
      return {
        cestable: false,
        keynum: '0',
        tablecss: {
          height: '620px',
          color: 'red'
        },
        dialogVisible:false,
        gcgoodsmonitoringdetail:{
          filter:{
            proCode:''
          },
      },
      filter1:{
            timerange1:[],
            startDate:null,
            endDate:null,
            Procode:''
          },
        pickerOptions1: {
          shortcuts: [{
            text: '近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },{
            text: '近十五天',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }]
        },

        dialogMapVisible:{visible:false,title:"",data:[]},
        addVisible:false,
        that:this,
        filter: {
         
        },
        editVisible:false,
        list: [],
        pager:{OrderBy:"id",IsAsc:false},
        tableCols:tableCols,
        tableHandles:tableHandles1,
        uploadfilelist:[],
        fileList:[],
        uploadimagelist:[],
        imageList:[],
        autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[]
        },
        total: 0,
        sels: [], 
        listLoading: false,
        pageLoading: false,
        // handtype:1,
        // handVisible: false,
        // handLoading: false,
        // infoVisible: false,
        // reportsingle:{processList:[]},
        // formtitle:"新增",
        // collapseactiveName: '1',
        // groupList:[],
        editLoading:false,
        selids:[],
        csoptions:[]
      }
    },
    mounted() {
        this.cestable = false;
        this.initform();
        formCreate.component('editor', FcEditor);
        this.getlist();
    },
    // beforeUpdate() {
    //   console.log('update')
    // },
    methods: {
      //删除
      deleteprocode(row){
        var that = this;
        this.$confirm("此操作将删除此产品数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          await deleteGongCompeteGoods({ ProCode: row.proCode });
          that.$message({ message: "已删除", type: "success" });
          that.onSearch();
        });
    
      },
      //编辑
     async onHand(row){
      this.formtitle='编辑';
      this.editVisible = true
    
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
         this.autoform.fApi.resetFields()
          this.$nextTick(async() =>{
      await this.autoform.fApi.setValue(row)
      })
    },
    async onEditSubmit() {
      this.editLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          if(formData.controllerName.length>4&&/[^\u4e00-\u9fa5,]/.test(formData.controllerName)==true||/[\u4e00-\u9fa5]$/.test(formData.controllerName)==false)
         {
          this.$message.error('多个抄送人必须以英文逗号隔开！！！！！');
         
          this.autoform.fApi.resetFields()
          this.editVisible=false;
          return;
         }
        //  if(formData.warningMinNum>formData.warningMaxNum)
        //  {
        //   this.$message.error('最小预警值不可以大于最大预警值！！！！！');
        //   this.autoform.fApi.resetFields()
        //   this.addVisible=false;
        //   return;

        //  }
          const res = await editGoodsMonitoring(formData);
          if(res.code==1){
            this.$message.success('修改成功！');
             this.getlist(); 
            this.editVisible=false;        
          }
        }else{}
     })
     this.editLoading=false;
    },
      async showTable(row){
        this.gcgoodsmonitoringdetail.filter.proCode=row.proCode;
       
        this.dialogVisible=true;
        setTimeout(async () => {
        await this.$refs.gcgoodsmonitoringdetail.onSearch(); 
      }, 100);
              
      },
         //添加监控信息
    addMonitoring(){
      this.addVisible=true;

    },
    async changeStatus(row){
      this.$confirm('将改变产品ID的监控状态，是否继续?', '提示', {confirmButtonText: '是',cancelButtonText: '否',type: 'warning'
        }).then(async () => {
            var params={
              Procode:row.proCode,
              IsCompute:row.isCompute,
            };
           console.log("我是最终参数呀！！！",params)
            const res= await changeGoodsMonitoringStatus(params);
            if (!res?.success) return ;
            this.$message({message:"重置成功",type:"success"});
        }).catch(() => {
           row.isCompute=!row.isCompute;
        });    
    },
    async onaddSubmit(){
      this.editLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          console.log("我是添加的数据",formData)

         if(formData.controllerName.length>4&&/[^\u4e00-\u9fa5,]/.test(formData.controllerName)==true||/[\u4e00-\u9fa5]$/.test(formData.controllerName)==false)
         {
          //^[a-zA-Z0-9,\u4e00-\u9fa5]+$
          this.$message.error('多个抄送人必须以英文逗号隔开！！！！！');
          await this.autoform.fApi.resetFields()
          this.addVisible=false;
          return;
         }
        //  if(formData.warningMinNum>formData.warningMaxNum)
        //  {
        //   this.$message.error('最小预警值不可以大于最大预警值！！！！！');
        //   await this.autoform.fApi.resetFields()
        //   this.addVisible=false;
        //   return;

        //  }
          const res = await editGoodsMonitoring(formData);
          await this.autoform.fApi.resetFields()
          if(res.code==1){
            this.$message.success('添加成功！');
            await this.autoform.fApi.resetFields()
            this.getlist();
            this.addVisible=false;
          }
         
        }else{}
     })
     this.editLoading=false;
        

     },
     async onSearch1(){
      this.filter1.startDate = null;
      this.filter1.endDate = null;
      if (this.filter1.timerange1) {
           this.filter1.startDate = this.filter1.timerange1[0];
           this.filter1.endDate = this.filter1.timerange1[1]
         }
        
         var params = {
          ... this.filter1
        };
         let that = this;
         
         
         const res = await getGoodsMonitoringChart(params).then(res=>{                    
             that.dialogMapVisible.visible=true;
             that.dialogMapVisible.data=res.data
             that.dialogMapVisible.title=res.data.legend[0]
         });
         await this.$refs.buschar.initcharts();
      
         
     },
     async showchart(row){
         
        
         this.filter1.Procode=row.proCode;
         var params = {
          ... this.filter1
        };
         let that = this;
         
         const res = await getGoodsMonitoringChart(params).then(res=>{                    
             that.dialogMapVisible.visible=true;
             that.dialogMapVisible.data=res.data
             that.dialogMapVisible.title=res.data.legend[0]
         })
         
   
      },
      async onSearch() {      
        this.$refs.pager.setPage(1)
        this.getlist()
      },
      async getlist() {
        var pager = this.$refs.pager.getPager()
        const params = {
          ...pager,
          ...this.pager,
          ... this.filter
        }
        this.listLoading = true
      
        const res = await getGoodsMonitoring(params)
        this.listLoading = false
        if (!res?.code) {
          return
        }
        this.total = res.data.total
        const data = res.data.list
        data.forEach(d => {
          d._loading = false
        })
        this.list = data
       this.$nextTick(()=>{
        this.cestable = true;
       })
      },
     async sortchange(column){
        if(!column.order)
          this.pager={};
        else
          this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
        this.onSearch();
      },
    
      async initform(){
         let that=this;
         this.autoform.rule1= [
                    
                     {type:'hidden',field:'isAdd',title:'isAdd',value: '1'},
                     {type:'input',field:'proCode',title:'产品ID',value: '',validate: [{type: 'string', required: true, message:'请输入产品ID',},{pattern:/^[0-9]*$/,message:"请输入正确的产品ID(数字)"}],col:{span:6},props: { maxlength:30}},
                     {type:'input',field:'title',title:'产品名称',value: '',col:{span:6} ,props: { maxlength:100},
                    
                  },
                    //  {type:'input',field:'inKuAmount',title:'监控人',value: '',props:{maxlength:10},col:{span:6}},
                    {type:'input',field:'controllerName',title:'抄送人',value: '',col:{span:6} ,props: { placeholder: '多个抄送人用英文逗号隔开,且和钉钉用户名相同',maxlength:50},
                    // validate:[{pattern:/,/,message:'请输入英文逗号',trigger:'blur'}]
                  },
                     {type:'input',field:'warningMinNum',title:'预警最小值',value: '',validate: [{ required: true, message:'请输入预警最小值',trigger: 'blur'},{pattern:/^(-|)?\d+$/,message:"请输入整数"}],col:{span:6},props: { maxlength:7}},
                     {type:'input',field:'warningMaxNum',title:'预警最大值',value: '',validate: [{ required: true, message:'请输入预警最大值',trigger: 'blur'},{pattern:/^(-|)?\d+$/,message:"请输入整数"}],col:{span:6},props: { maxlength:7}},                           
                    ],
                    this.autoform.rule= [
                    {type:'hidden',field:'id',title:'id',value: ''},
                     {type:'input',field:'proCode',title:'产品ID',value: '',validate: [{type: 'string', required: true, message:'请输入产品ID',},{pattern:/^[0-9]*$/,message:"请输入正确的产品ID(数字)"}],col:{span:6},props: { maxlength:30}},
                     {type:'input',field:'title',title:'产品名称',value: '',col:{span:6} ,props: { maxlength:100},
                    // validate:[{pattern:/,/,message:'请输入英文逗号',trigger:'blur'}]
                  },
                    //  {type:'input',field:'inKuAmount',title:'监控人',value: '',props:{maxlength:10},col:{span:6}},
                    {type:'input',field:'controllerName',title:'抄送人',value: '',col:{span:6} ,props: { placeholder: '多个抄送人用英文逗号隔开,且和钉钉用户名相同',maxlength:50},
                    
                    // validate:[{pattern:/,/,message:'请输入英文逗号',trigger:'blur'}]
                  },

                 
                     {type:'input',field:'warningMinNum',title:'预警最小值',value: '',validate: [{ required: true, message:'请输入预警最小值',trigger: 'blur'},{pattern:/^(-|)?\d+$/,message:"请输入整数"}],col:{span:6},props: { maxlength:7}},
                     {type:'input',field:'warningMaxNum',title:'预警最大值',value: '',validate: [{ required: true, message:'请输入预警最大值',trigger: 'blur'},{pattern:/^(-|)?\d+$/,message:"请输入整数"}],col:{span:6},props: { maxlength:7}},  
                  //    {type:'input',field:'controllerName',title:'抄送人',value: '',col:{span:6} ,props: { placeholder: '多个抄送人用英文逗号隔开,且和钉钉用户名相同',maxlength:50},
                  //   // validate:[{pattern:/,/,message:'请输入英文逗号',trigger:'blur'}]
                  // },                         
                    ]
       
      },
      
      selsChange: function(sels) {
        this.sels = sels
      },
      selectchange:function(rows,row) {
        this.selids=[];
        rows.forEach(f=>{
          this.selids.push(f.id);
        })
      },
    }
  }
  </script>
  <style>
  ._fc-upload .fc-upload-btn, ._fc-upload .fc-files {
      display: block;
      width: 58px;
      height: 20px;
      text-align: center;
      line-height: 1px;
      border: 1px solid #c0ccda;
      border-radius: 4px;
      overflow: hidden;
      background: #fff;
      position: relative;
      -webkit-box-shadow: 2px 2px 5px rgb(0 0 0 / 10%);
      box-shadow: 2px 2px 5px rgb(0 0 0 / 10%);
      /* margin-right: 4px; */
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      padding: 2px;
  }
  
  .drawer-footer {
      position: relative;
      left: 0px;
      bottom: 0px;
      border-top: 1px solid #e9e9e9;
      padding: 10px 30px 10px 0;
      /* padding: 16px; */
      background: white;
      text-align: right;
      z-index: 1;
  }
  
  .table-wrapper {
      width: 100%;
      height: calc(100% - 35px);
      margin: 0;
   }
  </style>
  <style lang="scss" scoped> 
  .el-drawer{
    height: 80%; 
    overflow: auto;
  }
  .el-scrollbar__wrap {
    height: 100%;
    margin: 15px;
  }
  /* table 样式 */
   table {
    border-top: 1px solid #ccc;
    border-left: 1px solid #ccc;
  }
  table td,
  table th {
    border-bottom: 1px solid #ccc;
    border-right: 1px solid #ccc;
    padding: 3px 5px;
  }
  table th {
    border-bottom: 2px solid #ccc;
    text-align: center;
  }
  
  /* blockquote 样式 */
  blockquote {
    display: block;
    border-left: 8px solid #d0e5f2;
    padding: 5px 10px;
    margin: 10px 0;
    line-height: 1.4;
    font-size: 100%;
    background-color: #f1f1f1;
  }
  
  /* code 样式 */
  code {
    display: inline-block;
    *display: inline;
    *zoom: 1;
    background-color: #f1f1f1;
    border-radius: 3px;
    padding: 3px 5px;
    margin: 0 3px;
  }
  pre code {
    display: block;
  }
  
  /* ul ol 样式 */
  ul, ol {
    margin: 10px 0 10px 20px;
  }

  ::v-deep .el-table .is-scrolling-left{
         min-height: 600px !important;
         background-color: red;
  }
</style>
