<template>
    <my-container v-loading="pageLoading">
      <template #header>
        <div>
        <el-button-group>
            <!-- <el-button style="padding: 0;margin: 0;">
              <inputYunhan  ref="proCode" v-model="filter.proCode" :inputt.sync="filter.proCode" :maxlength="150" placeholder="商品编码" :clearable="true" @callback="callbackProCode" title="商品编码" ></inputYunhan>
            </el-button> -->
            <el-button style="padding: 0;margin: 0;">
               <el-input v-model.trim="filter.productName" :maxlength="150" clearable placeholder="商品名称" style="width:160px;"/>
            </el-button>
            <!-- <el-button style="padding: 0;width: 270px;">
              <el-select v-model.trim="styleCode" multiple collapse-tags filterable remote reserve-keyword placeholder="系列编码" clearable :remote-method="remoteMethod"
              style="width: 270px" :loading="searchloading">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" > </el-option>
              </el-select>
            </el-button> -->
            <el-button style="padding: 0;margin: 0;">
              <el-date-picker style="width: 210px" v-model="filter.timerange" type="daterange" :clearable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期">
              </el-date-picker>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="filter.distributor" :maxlength="150" clearable placeholder="分销商" style="width:160px;"/>
           </el-button>
           <!-- <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="filter.orderSource" :maxlength="150" clearable placeholder="订单来源" style="width:160px;"/>
            </el-button> -->
            <!-- <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="filter.shopName" :maxlength="150" clearable placeholder="店铺" style="width:160px;"/>
            </el-button> -->

          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.profit1UnZero" collapse-tags clearable placeholder="毛利1" style="width: 90px">
              <el-option label="正利润" :value="false"/>
              <el-option label="负利润" :value="true"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.profit2UnZero" collapse-tags clearable placeholder="毛利2" style="width: 90px">
              <el-option label="正利润" :value="false"/>
              <el-option label="负利润" :value="true"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.profit3UnZero" collapse-tags clearable placeholder="毛利3" style="width: 90px">
              <el-option label="正利润" :value="false"/>
              <el-option label="负利润" :value="true"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.profit6UnZero" collapse-tags clearable placeholder="毛利6" style="width: 90px">
              <el-option label="正利润" :value="false"/>
              <el-option label="负利润" :value="true"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.exitProfitUnZero" collapse-tags clearable placeholder="出仓利润" style="width: 90px">
              <el-option label="正利润" :value="false"/>
              <el-option label="负利润" :value="true"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.orderLxList" multiple collapse-tags clearable placeholder="类型" style="width: 70px">
              <el-option label="聚水潭" :value="2"/>
              <el-option label="独立站" :value="1"/>
              <el-option label="旺店通" :value="5"/>
              <el-option label="售后" :value="3"/>
              <el-option label="普通" :value="4"/>
            </el-select>
          </el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-button-group>
        </div>
     </template>
      <vxetablebase
        :id="'fenXiaoShangDayReport202302031421'" :border="true" :align="'center'" :tablekey="'fenXiaoShangDayReport202302031421'"
        ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' @summaryClick='onsummaryClick'
        :isSelectColumn="true" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :cstmExportFunc="onExport"
        :tableData='financialreportlist'  :tableCols='tableCols' @cellClick="cellClick"
        :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:95%;margin: 0"   :xgt="9999" :showheaderoverflow="false">
      </vxetablebase>
      <!--分页-->
      <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
      </template>

      <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
        <span>
          <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="buscharDialog.visible = false">关闭</el-button>
        </span>
      </el-dialog>
    </my-container>
  </template>
  <script>
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
  import { getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
  import {getDirectorGroupList,getDirectorList} from '@/api/operatemanage/base/shop'
  import EveryDayrefund from '@/views/bookkeeper/reportday/EveryDayrefund'
  import {pageProductDayReport,queryDayReportAnalysis,exportProductDayReport,getParm,setParm,calDayRepoty, insertDailyReportConfirmList} from '@/api/bookkeeper/reportday'
  import {importProductDayReport} from '@/api/bookkeeper/import'
  import {getAllProBrand} from '@/api/inventory/warehouse'
  import productdrchart from '@/views/bookkeeper/reportday/productdrchart'
  import ProductADPDD from '@/views/bookkeeper/reportday/ProductADPDD'
  import {formatPlatform,formatTime,formatYesornoBool,formatLinkProCode} from "@/utils/tools";
  import cesTable from "@/components/Table/table.vue";
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  import InputMult from "@/components/Comm/InputMult";
  import { Loading } from 'element-ui';
  import { ruleDirectorGroup } from '@/utils/formruletools'
  import freightDetail from '@/views/bookkeeper/reportday/freightDetail'
  import expressfreightanalysis from '@/views/express/expressfreightanalysis'
  import buschar from '@/components/Bus/buschar'
  import importmodule from '@/components/Bus/importmodule'
  import ordergiftdetail from '@/views/order/gift/ordergiftDetail'
  import { getListByStyleCode } from "@/api/inventory/basicgoods"
  import inputYunhan from "@/components/Comm/inputYunhan";
  let loading;
  const startLoading = () => {
    loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
    });
  };
  const tableCols =[
          {istrue:true,fixed: 'left',prop:'yearMonthDay',label:'年月日',sortable:'custom', width:'60',type:'custom'},
        //   {istrue:true,fixed: 'left',prop:'platform',fix:true,label:'平台', width:'45',sortable:'custom',formatter:(row)=>formatPlatform(row.platform),type:'custom'},
          //{ istrue: true, fixed: 'left', prop: 'orderNo', label: '内部单号',  width: '80' ,type:'custom'},
        //   { istrue: true, fixed: 'left', prop: 'orderSource', label: '订单来源',  width: '80' ,type:'custom'},
        //   { istrue: true, fixed: 'left', prop: 'orderStatus', label: '订单状态',  width: '80' ,type:'custom'},
          { istrue: true, fixed: 'left', prop: 'orderType', label: '订单类型',  width: '80' ,type:'custom',formatter:(row)=>row.orderType==0?"普通订单":row.orderType==1?"补发订单":row.orderType==2?"供销Plus":"其他"},
          {istrue:true,fixed: 'left',prop:'orderLx',label:'类型', width:'70',type:'custom'},
          { istrue: true, fixed: 'left', prop: 'distributor', label: '分销商',  width: '80' ,type:'click',handle:(that,row)=>that.JumpDetailOrderDayReport(row)},
          // {istrue:true,fixed: 'left',prop:'shopName',label:'店铺',sortable:'custom', width:'70',formatter:(row)=> row.shopName,type:'custom'},
          //{istrue:true,fixed: 'left',prop:'proCode',fix:true,label:'商品编码', width:'80',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
          {istrue:true,fixed: 'left',prop:'goodsName',label:'商品名称',sortable:'custom', width:'80'},
          // {istrue:true,fixed: 'left',prop:'styleCode',label:'系列编码',sortable:'custom', type:'custom', width:'80',formatter:(row)=> row.styleCode || ' '},
          { istrue: true, summaryEvent: true, prop: 'orderCount', label: '订单量', sortable: 'custom', width: '65', tipmesg: '（付款订单量）此字段仅供参考；如果一个订单中有3种商品，这3行商品的销售订单数均为1，所以不能把此字段的合计值作为订单数量。因为订单就只有1个。但是销售订单数合计值为3。', formatter: (row) => !row.orderCount ? " " : row.orderCount },
          {istrue:true,summaryEvent:true,prop:'payAmont',label:'付款金额',sortable:'custom', width:'80',formatter:(row)=> !row.payAmont?" ": row.payAmont},
          { istrue: true, summaryEvent: true, prop: 'saleAmont', label: '销售金额', sortable: 'custom', width: '80', type: 'custom', tipmesg: '付款金额-刷单金额-发货前退款-发货后退款', formatter: (row) => !row.saleAmont ? " " : row.saleAmont },
  {istrue:true,summaryEvent:true,prop:'exitProfit',label:'出仓利润',sortable:'custom', width:'80',type:'custom',tipmesg:'付款金额-总销售成本-出仓成本-快递费',formatter:(row)=> !row.exitProfit?" ": row.exitProfit},
  { istrue: true, summaryEvent: true, prop: 'saleCost', label: '总销售成本', sortable: 'custom', width: '80', tipmesg: '付款金额对应的销售成本', style: (that, row) => that.renderCost(row), handle: (that, row) => that.showCost(row), formatter: (row) => !row.saleCost ? " " : row.saleCost },
  { istrue: true, summaryEvent: true, prop: 'goodProfitRate', label: '商品毛利率', sortable: 'custom', width: '80', tipmesg: '（付款金额-（销售成本+赠品成本+赠品链接成本+代发成本差））/付款金额', formatter: (row) => !row.goodProfitRate ? " " : row.goodProfitRate + '%' },
  {istrue:true,summaryEvent:true,prop:'freightFeeTotal',label:'快递费',sortable:'custom', width:'80',type:'custom',tipmesg:'实际快递费+预估快递费',formatter:(row)=> !row.freightFeeTotal?" ": row.freightFeeTotal},
  {istrue:true,summaryEvent:true,prop:'freightAvgFee',label:'快递均价',sortable:'custom', width:'80',formatter:(row)=> !row.freightAvgFee?" ": row.freightAvgFee},
  {istrue:true,summaryEvent:true,prop:'freightFee',label:'实际快递费',sortable:'custom', width:'80',type:'custom'},
          {istrue:true,summaryEvent:true,prop:'freightFeeVirtual',label:'预估快递费',sortable:'custom', width:'80',formatter:(row)=> !row.freightFeeVirtual?" ": row.freightFeeVirtual},

  { istrue: true, summaryEvent: true, prop: 'exitCost', label: '出仓成本', sortable: 'custom', width: '60', formatter: (row) => row.exitCost == 0 ? " " : row.exitCost, tipmesg: '出仓成本' },
  { istrue: true, summaryEvent: true, prop: 'exitCostAvg', label: '出仓成本均价', sortable: 'custom', width: '60', formatter: (row) => row.exitCostAvg == 0 ? " " : row.exitCostAvg },
  {istrue:true,summaryEvent:true,prop:'giftAmont',label:'赠品成本',sortable:'custom', width:'80',formatter:(row)=> !row.giftAmont?' ': row.giftAmont,type:'custom'},
          // {istrue:true,summaryEvent:true,prop:'brushAmont',label:'刷单金额',sortable:'custom', width:'80',tipmesg:'刷单金额',formatter:(row)=> !row.brushAmont?" ": row.brushAmont},
          {istrue:true,summaryEvent: true,prop:'',label:'退款', merge:true, width: '80',
              cols:[
            //   {istrue:true,summaryEvent:true,prop:'cancelOrderCount',label:'发货前退款单量',sortable:'custom', width:'80',type:'custom'},
              {istrue:true,summaryEvent:true,prop:'refundAmontBefore',label:'发货前退款',sortable:'custom', width:'80',type:'custom'},
               {istrue:true,summaryEvent:true,prop:'refundAmontBeforeRate',label:'发货前退款率',sortable:'custom', width:'80',type:'custom',formatter:(row)=> !row.refundAmontBeforeRate?" ": (row.refundAmontBeforeRate* 100)?.toFixed(0)+'%'},
              {istrue:true,summaryEvent:true,prop:'refundAmontAfter',label:'发货后退款',sortable:'custom', width:'80',type:'custom'},
             {istrue:true,summaryEvent:true,prop:'refundAmontAfterRate',label:'发货后退款率',sortable:'custom', width:'80',type:'custom',formatter:(row)=> !row.refundAmontAfterRate?" ": (row.refundAmontAfterRate* 100)?.toFixed(0)+'%'},
             {istrue:true,summaryEvent:true,prop:'refundAmont',label:'总退款金额',sortable:'custom', width:'80',tipmesg:'当日发生的退款金额，包括历史订单',formatter:(row)=> !row.refundAmont?" ": row.refundAmont},
            ]},

            {istrue:true,summaryEvent:true,prop:'cancelCost',label:'取消单返还成本',sortable:'custom', width:'80',tipmesg:'返还客服已确认的取消单成本,以确认时间统计',formatter:(row)=> !row.cancelCost?" ": row.cancelCost,type:'custom'},
            {istrue:true,summaryEvent:true,prop:'purchaseFreight',label:'采购成本差价',sortable:'custom', width:'100',formatter:(row)=> row.purchaseFreight==0?0: row.purchaseFreight,tipmesg:'入库单的采购运费分摊到该入库单的商品编码对应的ID上'},
            {istrue:true,summaryEvent:true,prop:'replaceSendCost',label:'代发成本差',sortable:'custom', width:'80',tipmesg:'每日杂费-代发成本差',formatter:(row)=> !row.replaceSendCost?" ": row.replaceSendCost,type:'custom'},
            {istrue:true,summaryEvent:true,prop:'profit1',label:'毛一利润',sortable:'custom', width:'80',type:'custom',tipmesg:'付款金额-总销售成本+取消单返还成本-采购成本差',formatter:(row)=> !row.profit1?" ": row.profit1},
            {istrue:true,summaryEvent:true,prop:'profit1Rate',label:'毛一利润率',sortable:'custom', width:'80',type:'custom',tipmesg:'毛一/销售金额',formatter:(row)=> !row.profit1Rate?" ": row.profit1Rate  + '%'},
            {istrue:true,summaryEvent:true,prop:'packageFee',label:'包装费用',sortable:'custom',tipmesg:'辅料+包材', width:'80',formatter:(row)=> !row.packageFee?" ": row.packageFee},
            { istrue: true, summaryEvent: true, prop: 'packageAvgFee', label: '包装均价', sortable: 'custom', width: '70', formatter: (row) => !row.packageAvgFee ? " " : row.packageAvgFee },

        {istrue:true,summaryEvent: true,prop:'ygfy',label:`预估费用(正式)`, merge:true, width: '80',
            cols:[
              {istrue:true,summaryEvent:true,prop:'productFreight',label:'产品运费',sortable:'custom', width:'80',formatter:(row)=> row.productFreight==0?" ": row.productFreight,tipmesg:'（总销售成本-取消单返还成本）*2.5%'},
         ]},
         {istrue:true,summaryEvent:true,prop:'profit2',label:'毛二利润',sortable:'custom', width:'80',type:'custom',tipmesg:'毛一-包装费-快递费合计-产品运费',
                                                formatter:(row)=>!row.profit2?" ": row.profit2},
        {istrue:true,summaryEvent:true,prop:'profit2Rate',label:'毛二利润率',sortable:'custom', width:'80',type:'custom',tipmesg:'毛二/销售金额',formatter:(row)=> !row.profit2Rate?" ": row.profit2Rate + '%'},
        {istrue:true,summaryEvent:true,prop:'wages',label:'运营工资',sortable:'custom', width:'80',formatter:(row)=> row.wages==0?" ": row.wages,tipmesg:'参考各组工资占比'},
        { istrue: true, summaryEvent: true, prop: 'processingCost', label: '加工费', sortable: 'custom', width: '70', formatter: (row) => row.processingCost == 0 ? " " : row.processingCost, tipmesg: '参考日报数据维护-加工费（工价*（订单量-发货前退款单量））' },
        {istrue:true,summaryEvent:true,prop:'profit3',label:'毛三利润',sortable:'custom', width:'80',type:'custom',tipmesg:'毛二利润-预估费用',permission:"lirunprsi,profit3rsi",formatter:(row)=> !row.profit3?" ": row.profit3},
        {istrue:true,summaryEvent:true,prop:'profit3Rate',label:'毛三利润率',sortable:'custom', width:'80',type:'custom',tipmesg:'毛三/销售金额',formatter:(row)=> !row.profit3Rate?" ": row.profit3Rate  + '%'},
        {istrue:true,summaryEvent:true,prop:'profit33',label:'毛四利润',sortable:'custom', width:'80',type:'custom',tipmesg:'毛三利润-出仓成本',permission:"lirunprsi,profit3rsi",formatter:(row)=> !row.profit33?" ": row.profit33},
        {istrue:true,summaryEvent:true,prop:'profit33Rate',label:'毛四利润率',sortable:'custom', width:'80',type:'custom',tipmesg:'毛四/销售金额',formatter:(row)=> !row.profit33Rate?" ": row.profit33Rate  + '%'},
        { istrue: true, summaryEvent: true, prop: 'profit5', label: '毛五利润', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛四利润-仓库薪资', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit5 ? " " : row.profit5 },
        { istrue: true, summaryEvent: true, prop: 'profit5Rate', label: '毛五利润率', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛五利润/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + '%' },
        { istrue: true, summaryEvent: true, prop: 'realFreightFee', label: '外仓快递费', sortable: 'custom', width: '80', type: 'custom', permission: "DistributionExpensePermissions", formatter: (row) => !row.realFreightFee ? " " : row.realFreightFee },
        { istrue: true, summaryEvent: true, prop: 'realPackageFee', label: '外仓包装费', sortable: 'custom', width: '80', type: 'custom', permission: "DistributionExpensePermissions", formatter: (row) => !row.realPackageFee ? " " : row.realPackageFee },
        { istrue: true, summaryEvent: true, prop: 'profit6', label: '毛六利润', sortable: 'custom', width: '80', type: 'custom', permission: "DistributionExpensePermissions", formatter: (row) => !row.profit6 ? " " : row.profit6 },
        { istrue: true, summaryEvent: true, prop: 'profit6Rate', label: '毛六利润率', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛六利润/销售金额', permission: "DistributionExpensePermissions", formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + '%' },
        ];
  const tableHandles=[
          //{label:"导入胜算", handle:(that)=>that.onstartImport()},
          //{label:"参数设置", handle:(that)=>that.onShowEditParm()},
          { label: "导出", handle: (that) => that.onDerivationMethod() },
         // {label:"每日快递", handle:(that)=>that.showexpressfreightanalysis()},
          //{ label: "计算日报", handle: (that) => that.showCalDayRepoty() },
         // {label:"刷新", handle:(that)=>that.onRefresh()},
        ];

  export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable,productdrchart,InputMult,freightDetail, buschar, expressfreightanalysis,importmodule,ordergiftdetail,ProductADPDD,EveryDayrefund,inputYunhan,vxetablebase},
    data() {
      return {
        dialogConfirmdata: false,
        // dialogConfirmdata2: false,
        confirmDate:'',
        confirmDate2:'',
        searchloading:'',
        dialogCalDayRepotVis:false,
        calDayRepotyDate:null,
        that:this,
        filter: {
          refundType: 1,
          OrderDayReportType:5,
          reportType:0,
          platform:11,
          shopCode:null,
          proCode:null,
          styleCode:null,
          productName:null,
          brandId:null,
          groupId:null,
          startTime: null,
          endTime: null,
          timerange:null,
          // 运营助理
          userId :null,
          // 车手
          userId2:null,
          // 备用
          userId3:null,
          // 运营专员 ID
          operateSpecialUserId:null,
          shopName:null,
          orderSource:null,
          distributor:null,
          profit1UnZero:null,
          profit2UnZero:null,
          profit3UnZero :null,
          profit6UnZero: null,
          profit4UnZero:null,
          groupType: null,
          timerange1:null,
          listingStartTime: null,
          listingEndTime: null,
          noProfitDay:null
        },
        onimportfilter:{
          yearmonthday:null,
        },
        styleCode:null,
        shopList:[],
        userList:[],
        brandlist:[],
        grouplist:[],
        directorlist:[],
        financialreportlist: [],
        tableCols:tableCols,
        tableHandles:tableHandles,
        total: 0,
        pager:{OrderBy:"YearMonthDay",IsAsc:false},
        sels: [], // 列表选中列
        options:[],
        listLoading: false,
        pageLoading: false,
        earchloading:false,
        summaryarry:{},
        selids:[],
        fileList:[],
        dialogVisible:false,
        uploadLoading:false,
        importFilte:{},
        fileList:[],
        fileparm:{},
        editparmVisible:false,
        editLoading:false,
        editparmLoading:false,
        drawervisible:false,
        dialogDrVisible:false,
        expressfreightanalysisVisible:false,
        drparamProCode:'',
        autoformparm:{
                 fApi:{},
                 options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
                 rule:[]
          },
        freightDetail:{
          visible:false,
          filter:{
            proCode:null,
            timeRange:[]
          },
        },
        EveryDayrefund:{
             visible:false,
          filter:{
            proCode:null,
            timeRange:[],
            afterSaleType:"2",
            orderStatus:"已取消",
            goodsStatus:"",
            timeRange1:[],
            platform:11
          }
          },
        giftDetail:{visible:false},
        buscharDialog:{visible:false,title:"",data:[]},
        drawervisible:false,
      };
    },
    async mounted() {
      this.init();
    },
    async created() {
      await this.getShopList();
      if (this.$route.query && this.$route.query.dayCount) {

        this.filter.noProfitDay = parseInt(this.$route.query.dayCount);
        this.filter.shopCode = this.$route.query.shopCode;
        this.filter.groupId = this.$route.query.groupId;
        this.filter.operateSpecialUserId=this.$route.query.operateSpecialUserId;
        let dateStr = this.$route.query.yearMonthDay.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
        this.filter.timerange=[dateStr,dateStr];
        this.filter.refundType=1;
        this.onSearch();
      }
    },
    methods: {
      async onsummaryClick(property){
        let a = {}
        let YH_EXT_ExportColumns = []
        let YH_EXT_ExportCnColumns = []
        a = this.$refs.table.getColumnsInfo();
        a?.fullColumn.forEach((item)=>{
          if(item.title && item.field){
            YH_EXT_ExportColumns.push(item.field)
            YH_EXT_ExportCnColumns.push(item.title)
          }
        })
        this.filter.startTime =null;
        this.filter.endTime =null;
        if (this.filter.timerange) {
          this.filter.startTime = this.filter.timerange[0];
          this.filter.endTime = this.filter.timerange[1];
        }
        var pager = this.$refs.pager.getPager();
        const params = {...pager,...this.pager,...this.filter};
        params.column=property;
        params.YH_EXT_ExportColumns = YH_EXT_ExportColumns
        params.YH_EXT_ExportCnColumns = YH_EXT_ExportCnColumns
        let that=this;
        that.buscharDialog.loading= true;
        that.listLoading = true;
        const res = await queryDayReportAnalysis(params).then(res=>{
          that.buscharDialog.loading= false;
          that.listLoading = false;
          that.buscharDialog.data=res.data
          that.buscharDialog.title=res.data.legend[0]
          that.buscharDialog.visible=true;
        });
        await that.$refs.buschar.initcharts();
      },
      JumpDetailOrderDayReport(row){
        this.$emit("ChangeActiveName2",row.distributor,1)
      },
      onDerivationMethod(){
        let that = this
        if(that.filter.refundType < 3){
          that.$refs.table.setExportCols()
        }
      },
      async onExport(opt){
        this.filter.startTime =null;
        this.filter.endTime =null;
        if (this.filter.timerange) {
          this.filter.startTime = this.filter.timerange[0];
          this.filter.endTime = this.filter.timerange[1];
        }
        var pager = this.$refs.pager.getPager();
        const params = {  ...pager,   ...this.pager,   ...this.filter, ...opt};
        var res= await exportProductDayReport(params);
        if(!res?.data) {
           return
        }
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','分销商日报数据' +  new Date().toLocaleString() + '_.xlsx' )
        aLink.click()
      },
      cellClick(prms){
        if(prms?.column?.field   && prms?.column?.field==='profit3IncreaseGoOnDays'  ){
          let row=prms.row;
          this.showprchart2(row.proCode, row.platform) ;
        }

      },
      datetostr(date) {
        var y = date.getFullYear();
        var m = ("0" + (date.getMonth() + 1)).slice(-2);
        var d = ("0" + date.getDate()).slice(-2);
        return y + "-" + m + "-" + d;
      },
      async init(){
          var date1 = new Date(); date1.setDate(date1.getDate()-1);
          var date2 = new Date(); date2.setDate(date2.getDate()-1);
          this.filter.timerange=[];
          this.filter.timerange[0]=this.datetostr(date1);
          this.filter.timerange[1]=this.datetostr(date2);
          console.log(this.filter)
        },
     async showprchart2(prcode,platform){
        window['lastseeprcodedrchart']=prcode
        window['lastseeprcodedrchart1']= platform
        window['lastseeprcodedrchart2'] = this.filter.refundType
        this.drparamProCode=prcode
        this.dialogDrVisible=true
     } ,
      //系列编码远程搜索
      async remoteMethod(query){
        if (query !== ''){
            this.searchloading == true;
            this.options=[];
            setTimeout(async () => {
                const res = await getListByStyleCode({currentPage:1,pageSize:50, styleCode: query})
                console.log("系列编码远程搜索",res);
                this.searchloading = false
                res?.data?.forEach(f=>{
                this.options.push({value:f.styleCode,label:f.styleCode})
                });
            }, 200)
        }
        else{
            this.options = []
        }
      },
     async getShopList(){
        const res1 = await getAllShopList({platforms:[11]});
        this.shopList=res1.data?.map(item=>{return{value:item.shopCode,label:item.shopName};});
          // res1.data?.forEach(f => {
          //   if(f.isCalcSettlement&&f.shopCode)
          //       this.shopList.push(f);
          // });
          var res2= await getDirectorGroupList();
          this.grouplist = res2.data?.map(item => {return { value: item.key, label: item.value };});

          var res3= await getDirectorList();
          this.directorlist = res3.data?.map(item => {return { value: item.key, label: item.value };});

          var res4= await getAllProBrand();
          this.brandlist = res4.data.map(item => {
              return { value: item.key, label: item.value };
          });
      },
     async sortchange(column){
        if(!column.order)
          this.pager={};
        else
          this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
       await this.onSearch();
      },
      onRefresh(){
        this.onSearch()
      },
      async onSearch(){
      this.$refs.table.changecolumn_setTrue(["yearMonthDay"]);
      if(this.filter.groupType == 1 || this.filter.groupType == 2){
        this.$refs.table.changecolumn(["yearMonthDay"]);
      }
      this.$refs.pager.setPage(1);
      await this.getList().then(res=>{  });

      // loading.close();
    },
      async getList(){
        this.filter.startTime =null;
        this.filter.endTime =null;
        this.filter.listingStartTime = null;
      this.filter.listingEndTime = null;
        if (this.filter.timerange) {
          this.filter.startTime = this.filter.timerange[0];
          this.filter.endTime = this.filter.timerange[1];
        }
        if (this.filter.timerange1) {
        this.filter.listingStartTime = this.filter.timerange1[0];
        this.filter.listingEndTime = this.filter.timerange1[1];
      }
      if(this.filter.starNumber!=null&&this.filter.endNumber!=null)
      {
        if(this.filter.starNumber>this.filter.endNumber)
        {
          this.$message({ type: 'warning', message: '开始订单量值不能大于结束订单量值!' });
         return
        }
      }
        var that=this;
        var pager = this.$refs.pager.getPager();
        this.filter.orderLx = this.filter.orderLxList.join(',')
        const params = {...pager,...this.pager,...this.filter};
       // this.listLoading = true;
        startLoading();
        const res = await pageProductDayReport(params).then(res=>{
            loading.close();
            if(res?.data?.list&&res?.data?.list.length>0){
              for (var i in res.data.list) {
                if (!res.data.list[i].freightFee) {
                  res.data.list[i].freightFee =" ";
                }
               if(that.filter.refundType == 2){
                res.data.list[i].RefundAmont = res.data.list[i].RefundAmontByPay;
                res.data.list[i].Profit3 = res.data.list[i].Profit3ByPay;
                res.data.list[i].Profit3Rate = res.data.list[i].Profit3RateByPay;
              }
              }
            }
            if(that.filter.refundType==2){
              res.data.summary.Profit3Rate_sum = res.data?.summary?.Profit3RateByPay_sum;
              res.data.summary.RefundAmontByPay = res.data?.summary?.RefundAmontByPay_sum;
              res.data.summary.Profit3ByPay = res.data?.summary?.Profit3ByPay_sum;
            }
            that.total = res.data?.total;
            that.financialreportlist = res.data?.list;
            that.$refs.table.loadRowEcharts();
            that.summaryarry=res.data?.summary;
        });
      },
      selectchange:function(rows,row) {
        this.selids=[];
        rows.forEach(f=>{
          this.selids.push(f.id);
        })
      },
     onRefresh(){
          this.onSearch()
      },
    renderCost(row){
      if(row.replaceSendCost >0){
        return "color:blue;cursor:pointer;";
      }
      else{
        return "";
      }
    },
    async callbackProCode(val) {
          this.filter.proCode = val;
      },
    }
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>

