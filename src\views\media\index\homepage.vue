<template>
    <div>
        <!-- <el-tabs v-model="activeName" style="height:98%;"  >
            <el-tab-pane label="新品拍摄" name="tab0" style="height: 100%;" :lazy="true" >
                <shootingIndex  />
            </el-tab-pane>
            <el-tab-pane label="短视频拍摄" name="tab1" style="height: 100%;" :lazy="true" >
                <videoIndex />
            </el-tab-pane>
            <el-tab-pane label="微。视频" name="tab2" style="height: 100%;" :lazy="true" >
                <microvideoIndex  />
            </el-tab-pane>
            <el-tab-pane label="直通车图" name="tab13" style="height: 100%;" :lazy="true"  >
                <directimgIndex />
            </el-tab-pane>
            <el-tab-pane label="日常改图" name="tab3" style="height: 100%;" :lazy="true" >
                <changeimgIndex />

            </el-tab-pane>

            <el-tab-pane label="店铺装修" name="tab4" style="height: 100%;" :lazy="true" >

            </el-tab-pane>

            <el-tab-pane label="包装设计" name="tab6" style="height: 100%;" :lazy="true"  >
                <packdesginIndex  />
            </el-tab-pane>

        </el-tabs> -->
         <div class="sydh">
            <div style="width: 1060px; height: 50px; margin: 0 auto">
                <a :class="menu==1?'sydhflatvice':'sydhfl'" href="#" @click="menu=1"  @dblclick="toResultmatter(1)"><span>新品拍摄</span></a><span class="fgf">|</span>
                <a :class="menu==2?'sydhflatvice':'sydhfl'" href="#" @click="menu=2"  @dblclick="toResultmatter(2)"><span>短视频拍摄</span></a><span class="fgf">|</span>
                <a :class="menu==3?'sydhflatvice':'sydhfl'" href="#" @click="menu=3"  @dblclick="toResultmatter(3)"><span>微。视频</span></a><span class="fgf">|</span>
                <a :class="menu==4?'sydhflatvice':'sydhfl'" href="#" @click="menu=4"  @dblclick="toResultmatter(4)"><span>直通车图</span></a><span class="fgf">|</span>
                <a :class="menu==5?'sydhflatvice':'sydhfl'" href="#" @click="menu=5"  @dblclick="toResultmatter(5)"><span>日常改图</span></a><span class="fgf">|</span>
                <a :class="menu==6?'sydhflatvice':'sydhfl'" href="#" @click="menu=6"  @dblclick="toResultmatter(6)"><span>店铺装修</span></a><span class="fgf">|</span>
                <a :class="menu==7?'sydhflatvice':'sydhfl'" href="#" @click="menu=7"  @dblclick="toResultmatter(7)"><span>包装设计</span></a><span class="fgf">|</span>
                <a :class="menu==8?'sydhflatvice':'sydhfl'" href="#" @click="menu=8"  @dblclick="toResultmatter(8)"><span>包装加工</span></a>
            </div>
        </div>
        <shootingIndex v-show="menu==1"/>
        <videoIndex v-show="menu==2"/>
        <microvideoIndex v-show="menu==3"/>
        <directimgIndex v-show="menu==4"/>
        <changeimgIndex v-show="menu==5"/>

        <packdesginIndex v-show="menu==7"/>
        <statisticsHome v-if="menu==8" />
    </div>
</template>
<script>

import shootingIndex from '@/views/media/index/shootingIndex'
import videoIndex from '@/views/media/index/videoIndex'
import packdesginIndex from '@/views/media/index/packdesginIndex'
import microvideoIndex from '@/views/media/index/microvideoIndex'
import directimgIndex from '@/views/media/index/directimgIndex'
import changeimgIndex from '@/views/media/index/changeimgIndex'
import statisticsHome from '@/views/media/packagework/statisticsHome.vue';

export default {
    name:'frostat',
    components: { shootingIndex,packdesginIndex,videoIndex ,microvideoIndex,directimgIndex,changeimgIndex,statisticsHome},
    data() {
        return {
            activeName:'tab0',
            menu:1,
            savenum: null,
        }
    },
    async mounted() {
        this.savenum = this.menu;
    },
    methods: {
        toResultmatter(index){
            var tourl = "1";
            switch(index){
                case 1:
                    //新品拍摄
                    tourl="/media/shooting/shootingindex";
                    break;
                case 2:
                     //短视频拍摄
                     tourl="/media/video/videoindex";
                    break;
                case 3:
                     //微。视频
                     tourl="/media/shooting/microvedio/microvedioindex";
                    break;
                case 4:
                     //直通车图
                    tourl="/media/shooting/directImg/directImgindex";
                    break;
                case 5:
                     //日常改图
                    tourl="/media/shooting/changeImg/changeimgindex";
                    break;
                case 6:
                     //店铺装修
                    tourl="/media/shooting/shopdecoration/shopdecorationindex";
                    break;
                case 7:
                     //包装设计
                    tourl="/media/shooting/packdesgin/packdesginindex";
                    break;
                case 8:
                     //包装加工
                    tourl="/media/packagework/packagingindex";
                    break;
            }
            if(tourl !=="1")
                this.$router.push({path: tourl})
        },
    },
};
</script>

<style lang="scss" scoped>
 * {
    font-size: 14px;
}

.sybj {
    min-width: 1100px;
    background-color: #f3f4f6;
    padding: 5px;
    height:calc(100vh - 280px);
    overflow-y: auto;
}

.tjldh {
    width: 785px;
    margin: 0 auto;
    box-shadow: 0px 3px 8px #cacaca;
    /* position: fixed; */
    z-index: 999;
}

.el-menu-demo {
    text-align: center;
}

.rqxz {
    width: 1000px;
    height: 80px;
    background-color: rgb(0, 54, 36);
    margin: 0 auto;
    line-height: 50px;
}

.tjbt {
    /* background-color: aquamarine; */
    /* font-weight: bold; */
    color: #333;
    line-height: 30px;
}

.sztjk {
    min-width: 75px;
    height: 50px;
    background-color: #f5faff;
    padding: 20px;
    text-align: center;
    float: left;
    margin: 5px;
    border-radius: 8px;
}

.sztjk .tjsz {
    font-size: 22px;
    color: #409eff;
}

.sztjk .tjmc {
    font-size: 14px;
    color: #409eff;
}

.tjnrk1 {
    width: 65%;
    /* background-color: rgb(255, 0, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk2 {
    width: 35%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk3 {
    width: 30%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk4 {
    width: 70%;
    /* background-color: rgb(255, 0, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk5 {
    width: 100%;
    /* background-color: rgb(255, 0, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk6 {
    width: 70%;
    /* background-color: rgb(255, 0, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk7 {
    width: 30%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk8 {
    width: 30%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk9 {
    width: 70%;
    /* background-color: rgb(255, 0, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk10 {
    width: 100%;
    /* background-color: rgb(255, 0, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrnk {
    width: 100%;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 15px 20px;
    float: left;
    border-radius: 6px;
}

.ptsx {
    width: 85%;
    height: 90px;
    background-color: #f7f7f7;
    border-radius: 8px;
    margin: 10px auto;
    box-sizing: border-box;
    padding: 0 35px;
    line-height: 90px;
}

.ptsx span {
    font-size: 16px;
    color: #555;
}

/* .nrqk{
    background-color: #000;

  } */

.sydh {
    width: 100%;
    min-width: 1100px;
    height: 50px;
    z-index: 999;
}

.sydhfl {
    width: 120px;
    height: 50px;
    display: inline-block;
    /* margin: 2px; */
    text-align: center;
    line-height: 50px;
    color: #555;
    text-decoration: none;
}

.sydhflatvice{
    width: 120px;
    height: 50px;
    background: linear-gradient(#f5f5f5 96%, #409eff 96%);
    display: inline-block;
    /* margin: 2px; */
    text-align: center;
    line-height: 50px;
    color: #409eff;
}

.sydhfl :hover {
    width: 120px;
    height: 50px;
    background: linear-gradient(#f5f5f5 96%, #409eff 96%);
    display: inline-block;
    /* margin: 2px; */
    text-align: center;
    line-height: 50px;
    color: #409eff;
}

.sydhfl i {
    font-size: 19px;
    color: #409eff;
    margin-right: 5px;
    position: relative;
    top: 1px;
    line-height: 50px;
}

.sydh .fgf {
    margin: 0 5px;
    color: #a6a6a6;
    line-height: 50px;
}

.sydhsx {
    width: 100%;
    height: 125px;
    background-color: #f3f4f6;
    margin: 0 auto;
    box-sizing: border-box;
    padding: 15px;
}

::v-deep .el-tabs__nav-scroll{
	width:40%;
	margin:0 auto;

}
.el-tabs__item{

    height: 60px;
    line-height: 60px;
    font-size: 14px;
}
::v-deep  .el-main{
    overflow: hidden;
}
</style>
