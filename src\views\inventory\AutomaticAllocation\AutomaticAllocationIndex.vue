<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <el-tabs v-model="activeName" style="height:94%;">
      <el-tab-pane label="库存巡检" name="tab1" style="height: 100%;">
          <InventoryInspection :filter="filter" ref="InventoryInspection" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane label="调拨记录" name="tab2" style="height: 100%;">
        <ToBeTransferred :filter="filter" ref="ToBeTransferred" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane label="仓位解绑记录" name="tab3" style="height: 100%;">
        <UnBindRecord :filter="filter" ref="UnBindRecord" style="height: 100%;"/>
      </el-tab-pane>
      <!-- <el-tab-pane label="调拨记录" name="tab3" style="height: 100%;" v-if="checkPermission('TransferRecord')">
        <TransferRecord :filter="filter" ref="TransferRecord" style="height: 100%;"/>
      </el-tab-pane> -->
    </el-tabs>
    </my-container >
   </template>
  <script>
  import MyContainer from "@/components/my-container";
  import InventoryInspection from './InventoryInspection.vue'
  // import TransferRecord from './TransferRecord.vue'
  import ToBeTransferred from './ToBeTransferred.vue'
  import UnBindRecord from './UnBindRecord.vue'
  export default {
    name: "Users",
    components: { MyContainer,InventoryInspection,ToBeTransferred,UnBindRecord},
    data() {
      return {
        that:this,
        pageLoading:'',
        filter: {
        },
        shopList:[],
        userList:[],
        groupList:[],
        selids:[],
        dialogVisibleSyj:false,
        fileList:[],
        activeName:'tab2'
      };
    },
    mounted() { 
      window.showtab4=this.showtab4
    },
    methods: {
      showtab4(){
       this.activeName=""
      }
    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
  