<template>
    <MyContainer>
      <template #header>
        <div class="top">
            <el-date-picker v-model="timeRanges" type="monthrange" align="right" unlink-panels range-separator="至"
            start-placeholder="开始月份" end-placeholder="结束月份" style="width: 250px; margin: 0 10px"
            :picker-options="pickerOptions" :value-format="'yyyyMM'"  :clearable="false">
          </el-date-picker>
          <el-select v-model="ListInfo.expressCompanyName" clearable filterable placeholder="快递公司" class="publicCss" >
            <el-option v-for="(item, i) in expressCompany" :key="item.value + '-' + i" :label="item.label" :value="item.label" />
          </el-select>
            <el-select filterable v-model="ListInfo.ComputationalLogic" placeholder="请选择计算逻辑"  multiple clearable style="width: 130px">
                <el-option label="三日结" value="三日结" />
                <el-option label="预付款" value="预付款" />
                <el-option label="月结" value="月结" />
                <el-option label="面单" value="面单" />
            </el-select>
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" @click="exportProps">导出</el-button>
          <el-button type="primary" @click="startClac">计算快递余额表</el-button>
        </div>
      </template>
      <vxetablebase :id="'ExpressCompanyMonthlySummary202412260907'" :tablekey="'ExpressCompanyMonthlySummary202412260907'" ref="table" :that='that'
        :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
        :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
        style="width: 100%;  margin: 0" :loading="loading" :height="'100%'" :border="true">
        <template #mergeField3>
          <vxe-colgroup title="本月付款/补贴(应收)">
            <vxe-colgroup title="付面单款">
              <vxe-column field="sheetQuantity" title="面单数量" width="80"></vxe-column>
              <vxe-column field="paymentSheetAmount" title="金额" width="80"></vxe-column>
            </vxe-colgroup>
            <vxe-column field="otherSubsidyAmount" title="其他/补贴" width="80">
              <template #default="{ row }">
                  <div v-if="row.verify">
                    <el-input v-model.trim="row.otherSubsidyAmount" placeholder="请输入"
                      :controls="false" style="width: 100%;" />
                  </div>
                  <div v-else>
                    {{ (row.otherSubsidyAmount) }}
                  </div>
              </template>
            </vxe-column>
            <vxe-column field="paidAdditionalWeightOrSettlementAmount" title="已付续重费" width="110"></vxe-column>
          </vxe-colgroup>
        </template>
        <template #currentPayables>
          <vxe-colgroup title="本月份应付">
            <vxe-colgroup title="运费">
              <vxe-column field="labelQuantity" title="单数" width="80"></vxe-column>
              <vxe-column field="freightAmount" title="金额" width="80"></vxe-column>
              <vxe-column field="yfMeanValue" title="均值" width="80"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="其中:面单">
              <vxe-column field="payableAdditionalWeightOrderCount" title="单数" width="80"></vxe-column>
              <vxe-column field="labelSubAmount" title="金额" width="80"></vxe-column>
            </vxe-colgroup>
            <vxe-column field="payableAdditionalWeightAmount" title="应付续重费" width="110"></vxe-column>
          </vxe-colgroup>
        </template>
        <template #remarks="{ row }">
            <div v-if="row.verify">
              <el-input v-model.trim="row.remarks" placeholder="请输入"
                :controls="false" style="width: 100%;" />
            </div>
            <div v-else>
              {{ (row.remarks) }}
            </div>
          </template>
        <template #systemLabelQuantity="{ row }">
            <div v-if="row.verify">
              <el-input v-model.trim="row.systemLabelQuantity" placeholder="请输入"
                :controls="false" style="width: 100%;" />
            </div>
            <div v-else>
              {{ (row.systemLabelQuantity) }}
            </div>
          </template>
        <template #labelAmount="{ row }">
            <div v-if="row.verify">
              <el-input v-model.trim="row.labelAmount" placeholder="请输入"
                :controls="false" style="width: 100%;" />
            </div>
            <div v-else>
              {{ (row.labelAmount) }}
            </div>
          </template>
        <template #paidLabelAmount="{ row }">
            <div v-if="row.verify">
              <el-input v-model.trim="row.paidLabelAmount" placeholder="请输入"
                :controls="false" style="width: 100%;" />
            </div>
            <div v-else>
              {{ (row.paidLabelAmount) }}
            </div>
          </template>
        <template slot="right">
          <vxe-column title="操作" width="90" fixed="right">
            <template #default="{ row, $index }">
                <div style="display: flex;justify-content: center;align-items: center;">
                  <div v-if="!row.verify">
                    <el-button type="text" @click="editProduct(row, $index)">编辑</el-button>
                  </div>
                  <div v-else>
                    <el-button type="text" @click="saveRowEvent(row, 1)">保存</el-button>
                    <el-button type="text" @click="saveRowEvent(row, 0)">取消</el-button>
                  </div>
                </div>
              </template>
          </vxe-column>
        </template>
      </vxetablebase>
      <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template>

      <el-dialog title="计算快递余额表" :visible.sync="dialogVisible2" width="30%" v-dialogDrag :close-on-click-modal="false">
        <div style="height: 50px;">
          <!-- <el-select filterable v-model="platform" placeholder="请选择平台"  collapse-tags
                clearable style="width: 160px">
                <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
              </el-select> -->

              <el-date-picker v-model="yearmonth" type="month" :clearable="false" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"  ></el-date-picker>

        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible2 = false">关闭</el-button>
          <el-button type="primary" @click="onBatchConfirmation(1,null)">提交确认</el-button>
        </span>
      </el-dialog>
    </MyContainer>
  </template>

  <script>
  import MyContainer from "@/components/my-container";
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
  import { warehouselist, formatWarehouseNew,formatTime ,platformlist} from "@/utils/tools";
  import { getExpressComanyAll ,getExpressCompanyMonthlySummaryList as getExpressInfoData_Month, deleteExpressInfoData,getExpressComanyStationName, exportExpressCompanyMonthlySummary as exportExpressInfoData_Month ,calcExpressCompanyMonthlySummary as batchIntoMonthExpressFreight,editExpressCompanyMonthlySummary as addOrEditMonthBillExpressAddFee, getExpressBankInfoList} from "@/api/express/express";
  import dayjs from 'dayjs'
  import queryCondition from "../../dailyCourierFee/components/queryCondition.vue";

  const tableCols = [
  { width: '110', align: 'center', prop: 'yearMonth', label: '月份'},
  {  width: '110', align: 'center', prop: 'computationalLogic', label: '计算逻辑',  },
  { sortable: 'custom', width: '110', align: 'center', prop: 'expressCompanyFullName', label: '快递公司', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'expressionSheetFee', label: '面单费', },

  {
    istrue: true, prop: '', label: '本月余额', merge: true, prop: 'mergeField1', permission: "cgcoltxpddprsi", width: '60',
    cols: [
    { sortable: 'custom', width: '210', align: 'center', prop: 'payableAmount', label: '应付款', },
    { sortable: 'custom', width: '210', align: 'center', prop: 'actualBalance', label: '实际余额', },
    { sortable: 'custom', width: '110', align: 'center', prop: 'differenceAmount', label: '差额（面单差异）',  },
    ]
  },

  {
    istrue: true, prop: '', label: '上月余额', merge: true, prop: 'mergeField2', permission: "cgcoltxpddprsi", width: '60',
    cols: [
    { sortable: 'custom', width: '110', align: 'center', prop: 'systemLabelQuantity', label: '系统面单数量',  },
    { sortable: 'custom', width: '110', align: 'center', prop: 'labelAmount', label: '面单金额',  },
    { sortable: 'custom', width: '110', align: 'center', prop: 'paidLabelAmount', label: '应付款',  },
    ]
  },

  { istrue: true, prop: 'mergeField3', label: '本月付款/补贴(应收)', type: 'slot' },
  { istrue: true, prop: 'currentPayables', label: '本月份应付', type: 'slot' },
  {
    istrue: true, prop: '', label: '本月面单余量', merge: true, prop: 'mergeField3', permission: "cgcoltxpddprsi", width: '60',
    cols: [
      { sortable: 'custom', width: '110', align: 'center', prop: 'labelAllowance', label: '面单余量',  },
      { sortable: 'custom', width: '110', align: 'center', prop: 'labelBalance', label: '面单余额',  },
      { sortable: 'custom', width: '110', align: 'center', prop: 'systemAllowance', label: '系统余量',  },
    ]
  },
  { sortable: 'custom', width: '130', align: 'center', prop: 'monthActualAmount', label: '本月份实际余额',  },
  { sortable: 'custom', width: '90', align: 'center', prop: 'remarks', label: '备注',  },
  ]
  export default {
    name: "ExpressCompanyMonthlySummaryByMonth",
    components: {
      MyContainer, vxetablebase,queryCondition
    },
    data() {
      return {
        expressCompany: [],
        topfilter: {
          expressCompanyId: null,
          prosimstateId: null,
          warehouseId: null,
        },
        formatWarehouseNew:formatWarehouseNew,
        platformlist:platformlist,
        timeRanges: [],
        that: this,
        ListInfo: {
          yearmonth:null,
          currentPage: 1,
          pageSize: 50,
          orderBy: 'yearMonth',
          isAsc: false,
         //noUseCatch: false,//是否使用缓存
         shopName:null,//店铺
         YearMonthStartlong: null, //开始时间
         YearMonthEndlong: null, //结束时间
        },
        yearMonthStart:null,
        yearMonthEnd:null,
        tableCols,
        tableData: [],
        summaryarry: {},
        total: 0,
        loading: false,
        platform:null,
        yearmonth:null,
        dialogVisible2:false,
      }
    },
    async mounted() {

      //await this.getList()
      await this.init()
    },
    methods: {
      startClac() {
   this.dialogVisible2 = true;
  },
      async init() {
        this.$nextTick(() => {
          this.$refs.refqueryCondition.init()
        })
        const res = await getExpressComanyAll({});
        if (!res?.success) return
        this.expresscompanylist = res.data;
        const { data, success } = await getExpressBankInfoList({});
        if (!success) return
        this.expressCompany = Array.from(new Set(data.list.map(item => ({ label: item.expressCompanyFullName, value: item.expressCompanyId }))));
      },
      //导出
      async exportProps() {
        this.loading = true
        const res = await exportExpressInfoData_Month({...this.ListInfo,...this.topfilter})
        this.loading = false
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '快递余额表数据' + new Date().toLocaleString() + '.xlsx')
        aLink.click()
      },
    //   async changeTime(e) {
    //     this.ListInfo.startTime = e ? e[0] : null
    //     this.ListInfo.endTime = e ? e[1] : null
    //   },
      async getList(type) {
        if (type == 'search') {
          this.ListInfo.currentPage = 1
          this.$refs.pager.setPage(1)
        }
        if (this.timeRanges) {
          this.ListInfo.YearMonthStartlong = this.timeRanges[0];
          this.ListInfo.YearMonthEndlong = this.timeRanges[1];
        }
        this.loading = true
        const { data, success } = await getExpressInfoData_Month({...this.ListInfo,...this.topfilter})
          if (success) {
          this.tableData = data.list
          this.tableData.forEach(item => {
            item.verify = false;
            item.systemLabelQuantitySave = item.systemLabelQuantity
            item.labelAmountSave = item.labelAmount
            item.paidLabelAmountSave = item.paidLabelAmount
            item.remarksSave = item.remarks
            item.otherSubsidyAmountSave = item.otherSubsidyAmount
          });
          this.total = data.total
          this.summaryarry = data.summary
          let summary = data.summary || {}

  const resultsum = {};
  Object.entries(summary).forEach(([key, value]) => {
      resultsum[key] = formatNumber(value);
  });
  function formatNumber(number) {
      const options = {
          useGrouping: true,
      };
      return new Intl.NumberFormat('zh-CN', options).format(number);
  }
  this.summaryarry = resultsum
          this.loading = false
        } else {
          this.$message.error('获取列表失败')
        }
      },
      async onBatchConfirmation(lx,row) {
        let platform=this.platform
        let yearmonth=this.yearmonth
        let shopCode=null
        if(lx==2){
          platform = row.platform
          yearmonth = row.yearMonth
          shopCode = row.shopCode
        }

        // if(!platform){
        //   this.$message.error('请选择平台')
        //   return
        // }

        if(!yearmonth){
          this.$message.error('请选择月份')
          return
        }
        this.$confirm('是否计算该'+(lx==1?'月份':'店铺')+'?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const { success } =  await batchIntoMonthExpressFreight({ yearmonth:yearmonth })
          if (success) {
            this.$message.success('计算中,请到任务管理中查看计算进度')
          } else {
            this.$message.error('计算失败')
          }
        }).catch(() => {
          this.$message.info('已取消计算')
        });
      },
      async saveRowEvent(row, type) {
      this.$nextTick(async () => {
        if (type == 1) {
          row.remarksSave = row.remarks;
          const setValue = (field) => {
            // row[`${field}`] = row[field] ? Number(row[field]) : null;
            row[`${field}Save`] = row[field];
          };
          ['systemLabelQuantity', 'labelAmount', 'paidLabelAmount', 'otherSubsidyAmount'].forEach(setValue);
        //  row.noAddFeeAmount = decimal(row.totalFreight, row.addFee, 2, '-');
          //row.noAddFeeAvg = decimal(row.noAddFeeAmount, row.noAddFeeCount, 2, '/');
          const { data, success } = await addOrEditMonthBillExpressAddFee({ ...row });
          if (!success) return;
          row.verify = false;
          this.$message.success('保存成功');
          this.getList();
        } else {
          row.remarks = row.remarksSave;
          row.systemLabelQuantity = row.systemLabelQuantitySave;
          row.labelAmount = row.labelAmountSave;
          row.paidLabelAmount = row.paidLabelAmountSave;
          row.otherSubsidyAmount = row.otherSubsidyAmountSave;
          row.verify = false;
        }
      })
      this.$forceUpdate()
    },
    editProduct(row) {
      let index = this.tableData.findIndex(item => item === row)
      this.$nextTick(async () => {
        this.tableData.findIndex((item, i) => {
          if (i == index) {
            item.verify = true
          } else {
            item.verify = false
          }
        })
      })
      this.$forceUpdate()
    },



      //每页数量改变
      Sizechange(val) {
        this.ListInfo.currentPage = 1;
        this.ListInfo.pageSize = val;
        this.getList()
      },
      //当前页改变
      Pagechange(val) {
        this.ListInfo.currentPage = val;
        this.getList()
      },
      sortchange({ order, prop }) {
        if (prop) {
          this.ListInfo.orderBy = prop
          this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
    }
  }
  </script>

  <style scoped lang="scss">
  .top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
      width: 150px;
      margin-right: 5px;
    }
  }
  </style>
