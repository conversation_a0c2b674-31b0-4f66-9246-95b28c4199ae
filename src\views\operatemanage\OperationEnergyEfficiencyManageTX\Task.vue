<template>
    <my-container>
        <template #header>
        </template>
        <vxe-table
          border
          show-overflow
          ref="table"
          :loading="listLoading"
          :data="list"
          :edit-config="{trigger: 'click', mode: 'cell'}"
          height="550px"
          :row-config="{height: 251}"
          :column-config="{resizable: true}"

          >
          <vxe-column type="seq" width="60"></vxe-column>
          <vxe-column  field="proCode" title="任务商品ID" width="120">

          </vxe-column>
          <vxe-column field="title" title="任务商品名称" width="180">

          </vxe-column>
          <vxe-column field="shopName" title="店铺" width="160">

          </vxe-column>
          <vxe-column field="onTime" title="上架时间" width="100">

          </vxe-column>
          <vxe-column field="newPattern" title="上新模式" width="80">

          </vxe-column>

          <vxe-column field="images" title="爆款商品图片" width="120" height="120" >
            <!-- <template #image="{ row }">
                <img :src="row.images" alt="图片">
              </template> -->

              <template #default="{ row }">
                <img  @click="showImg(row.images)" v-if="row.images" :src="row.images" style="width: 100px;">
                <span v-else>无</span>
              </template>


          </vxe-column>
          <vxe-column field="hotCake" title="状态" width="170" >
            <template #default="{ row }">
                <template v-if="row.hotCake==22&&row.attention==11&&row.productType==33">
                 <span >爆款、关注、任务中心</span>
                </template>
                <template v-else-if="row.hotCake==22&&row.attention==11">
                    <span >爆款、关注</span>
                </template>
                <template v-else-if="row.hotCake==22&&row.productType==33">
                    <span >爆款、任务中心</span>
                </template>
                <template v-else-if="row.attention==11&&row.productType==33">
                    <span >关注、任务中心</span>
                </template>
                <template v-else-if="row.hotCake==22">
                    <span >爆款</span>
                </template>
                <template v-else-if="row.attention==11">
                    <span >关注</span>
                </template>
                <template v-else-if="row.productType==33">
                    <span >任务中心</span>
                </template>
                <template v-else>
                  <span ></span>
                </template>
              </template>
          </vxe-column>
          <vxe-column field="createUserName" title="发起人" width="80" >

          </vxe-column>
            <vxe-column field="appointUserName" width="80" title="接收人" >
            </vxe-column>
          <vxe-column field="relevantData" title="相关资料" width="300" >
            <template #default="{ row }">
                <div  class="vhtml">
                    <div v-html="row.relevantData"
                    @click="editorClick"></div>
                </div>

              </template>
          </vxe-column>
          <vxe-column field="taskStatus" width="100" title="任务状态" >
            <template #default="{ row }">
                <template v-if="row.taskStatus === 1">
                 <span style="color: red;">待处理</span>
                </template>
                <template v-else-if="row.taskStatus === 2">
                    <span style="color:blue;">处理中</span>
                </template>
                <template v-else>
                  <span style="color:green;">已完结</span>
                </template>
              </template>
          </vxe-column>
          <vxe-column  width="340" title="操作" >
            <template  #default="{ row }">


                <vxe-button style="color: green;" type="text" @click="UploadDatas(row)" >上传资料并查看</vxe-button>
                <vxe-button :disabled="row.taskStatus === 3" style="color: rebeccapurple;" type="text" @click="UploadSuccessMessage(row)" >通知发起人</vxe-button>
                <vxe-button :disabled="row.taskStatus === 3" style="color: blue;" type="text" @click="successTask(row)" >完成</vxe-button>
              </template>



          </vxe-column>


        </vxe-table>
        <!-- </ces-table> -->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <template>
                    <el-form class="ad-form-query" :model="detailfilter" @submit.native.prevent label-width="100px">
                        <el-row>
                            <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
                                <el-form-item label="日期:">
                                    <el-date-picker style="width: 260px" v-model="detailfilter.timerange"
                                        type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                                        :clearable="false"></el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                                <el-form-item>
                                    <el-button type="primary" @click="getecharts">刷新</el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </template>
            </span>
            <span>
                <buschar v-if="buscharDialog.visible" ref="buschar" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-image-viewer v-if="showGoodsImage" :url-list="imgList" :on-close="closeFunc" style="z-index:9999;" />
        <el-dialog title="上传资料" :visible.sync="resourcedialogVisible" width="30%"  height="50%" v-dialogDrag>
            <span>
        <UploadNeedDatas ref="UploadNeedDatas">

              </UploadNeedDatas>
            </span>
            <span slot="footer" class="dialog-footer">
              <el-button @click="resourcedialogVisible = false">关闭</el-button>
            </span>
          </el-dialog>
          <el-image-viewerplus v-if="showGoodsImageplus" :initialIndex="imgindex" :url-list="imgList" :wrapperClosable="false"
      :on-close="closeFuncplus" style="z-index:9999;" />

    </my-container>
</template>

<script>
import { getDirectorList, getDirectorGroupList, getProductBrandPageList, getList as getshopList } from '@/api/operatemanage/base/shop'
import { platformlist } from '@/utils/tools'
import { formatLinkProCode } from "@/utils/tools";
import { getAllProBrand } from '@/api/inventory/warehouse'
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import * as echarts from 'echarts'
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { pageProductNewAsync, queryGuardProductNewsis, getProductStateName, getProductAdveClick ,pageProductBattlefieldReport,pageProductTaskReport,editProductAttention,editProductAttentionstate,sendMessageToCreateUserName} from '@/api/operatemanage/base/product'
import { getRateDetailList } from "@/api/customerservice/productconsulting";
import { addtrainplan } from "@/api/customerservice/trainplan";
import { getcusgroups, } from "@/api/customerservice/customergroup";
import cesTable from "@/components/Table/table.vue";
import buschar from '@/components/Bus/buschar'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import personstatistics from '../../customerservice/personstatistics.vue'
import trainresourceupload from "@/views/customerservice/trainresourceupload.vue"
import UploadNeedDatas from './UploadNeedDatas.vue'

import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import ElImageViewerplus from '@/views/media/shooting/imageviewer.vue'//图片查看
const tableCols = [
    // { istrue: true, prop: 'proCode', label: '任务商品ID', width: '115', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
    // { istrue: true, prop: 'title', label: '任务商品名称', width: '300', sortable: 'custom' },
    // { istrue: true, prop: 'shopName', label: '店铺', width: '150', },
    // // { istrue: true, prop: 'groupId', label: '组长', width: '70', sortable: 'custom', formatter: (row) => row.groupName || ' ' },
    // // { istrue: true, prop: 'operateSpecialUserId', label: '运营专员', width: '80', sortable: 'custom', formatter: (row) => row.operateSpecialUserName || ' ' },
    // // { istrue: true, prop: 'userId', label: '运营助理', width: '80', sortable: 'custom', formatter: (row) => row.userRealName || ' ' },
    // //{istrue:true,prop:'userId2',label:'车手', width:'70',sortable:'custom',permission:"productpermis",formatter:(row)=> row.userRealName2||' '},
    // // { istrue: true, prop: 'userId3', label: '备用', width: '60', sortable: 'custom', formatter: (row) => row.userRealName3 || ' ' },
    // { istrue: true, prop: 'onTime', label: '上架时间', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.onTime, "YYYY-MM-DD") },
    //  { istrue: true, prop: 'newPattern', label: '上新模式', sortable: 'custom', width: '90', type: 'custom', type: 'click', handle: (that, row) => that.setprostate(row) },
    // // { istrue: true, prop: 'renInquire', label: '人工转化率', width: '70', formatter: (row) => !row.renInquire ? "0%" : (row.renInquire * 100).toFixed(2) + '%' },
    // { istrue: true, prop: 'images', label: '任务商品图片', width: '120', type: "image" },
    // { istrue: true, prop: 'hotCake', label: '状态', width: '80', formatter: (row) =>row.hotCake==22&&row.attention==11&&row.task==33?"爆款、关注、任务中心":row.hotCake==22&&row.attention==11?"爆款、关注":row.hotCake==22&&row.task==33?"爆款、任务中心":row.attention==11&&row.task==33?"关注、任务中心":row.hotCake==22?"爆款":row.attention==11?"关注":row.task==33?"任务中心":"" },
    // // { istrue: true, prop: 'onTime', label: '接收人', width: '100', sortable: 'custom', formatter: (row) => formatTime(row.onTime, "YYYY-MM-DD") },
    // // { istrue: true, prop: 'onTime', label: '相关资料', width: '140', sortable: 'custom', formatter: (row) => formatTime(row.onTime, "YYYY-MM-DD") },
    // // { istrue: true, prop: 'onTime', label: '状态', width: '120', sortable: 'custom', formatter: (row) => formatTime(row.onTime, "YYYY-MM-DD") },
    // // { istrue: true, prop: 'onTime', label: '操作', width: '120', sortable: 'custom', formatter: (row) => formatTime(row.onTime, "YYYY-MM-DD") },
];
const tableHandles1 = [];
const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");
const star = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");

export default {
    name: 'YunhanAdminProductnew',
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, buschar, personstatistics, trainresourceupload,vxetablebase,ElImageViewer,UploadNeedDatas, ElImageViewerplus},
    filters: {
        myTime(val) {
            return formatTime(val, "YYYY-MM-DD")
        }
    },
    props: {
        filter: {},
        filterDetail: {}
    },
    data() {
        return {



            that: this,
            AppointUserName:null,
            searchOrder: "",
            searchIndex: -1,
            searchColumn: [
                { text: '搜索', value: 'searchVisitorNumber' },
                { text: '总访客量', value: 'orderCount' },
                { text: '广告访客量', value: 'adveNumber' },
                { text: '净利', value: 'profit4' },
                { text: '毛利', value: 'profit3' },
                { text: '支付买家数', value: 'payBuyNumber' },
            ],
            detailfilter: {
                procode: null,
                platform: null,
                startTime: null,
                endTime: null,
                timerange: [star, endTime]
            },
            Filter: {
                StartDate: null,
                EndDate: null,
                timerange: [startTime, endTime]
            },
            personpager: {
                OrderBy: "rensuccess",
                pageSize: 200,
                pageIndex: 1,
                IsAsc: false,
            },
            amontDialog: { visible: false, rows: [] },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "", IsAsc: false },
            tableCols: tableCols,
            tableHandles: tableHandles1,
            platformlist: platformlist,
            platformList: [],
            showGoodsImage: false,
            showGoodsImageplus: false,
            resourcedialogVisible:false,
            imgList: [],
            grouplist: [],
            brandlist: [],
            directorlist: [],
            productnewList: [],
            cusgroupslist: [],
            shopList: [],
            directorGroupList: [],
            opList: [],
            total: 0,
            sels: [],
            selids: [],
            listLoading: false,
            echartsLoading: false,
            isshowstate: false,
            everyPersonVisible: false,
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                },
            },
            buscharDialog: { visible: false, title: "", data: [] },
        };
    },

    async mounted() {
        await this.getlist();
        // await this.getDirectorlist()
        await this.getGroupList()
        await this.init()
        await this.getPruductNewState()
        await this.getProductCustomer()
    },

    methods: {
        async editorClick(e) {
        if (e.target.nodeName.toLocaleLowerCase() == 'img') {
            this.showGoodsImage = true;
            this.imgList = [];
            if (e.target.src) {
            this.imgList.push(e.target.src);
            }
            else {
            this.imgList.push(this.imagedefault);
            }
        }
        },
       async UploadSuccessMessage(row){
        const params = {
          proCode:row.proCode,
          appointUserName:row.appointUserName,
          createUserName:row.createUserName,
          title:row.title,
        };
        const res= await sendMessageToCreateUserName(params);

      // console.log("我已经发送消息了呵呵哈哈哈",res)
       if(res.code==1)
       {

        this.$message.success('已通知发起人');
       }
      },
        async UploadDatas(row){

        this.resourcedialogVisible=true;
        this.$nextTick(() => {
            this.$refs.UploadNeedDatas.init(row.proCode,row.createUserName,row.appointUserName,row.title,row.taskStatus);
        });


        },
        async successTask(row){
            this.$confirm('确定要执行此操作吗, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
            }).then(async() => {

            const res = await editProductAttentionstate(row);

            if(res.code==1){
                this.$message.success('保存成功！');
                this.getlist();

                this.addVisible=false;
            }else{

            }
            }).catch(() => {

            this.$message({
                type: 'info',
                message: '已取消操作'
            });
            });

        },
        // async UpdateSendMessage(row){
        //      if(row.relevantData==null)
        //      {
        //         this.$message.error('请输入相关资料！！！！！！');
        //         return;
        //      }
        //      if(row.appointUserName==null||row.appointUserName==""||row.appointUserName==0)
        //      {
        //         this.$message.error('请选择接收人！！！！！！');
        //         return;
        //      }
        //     this.$confirm('确定要执行此操作吗, 是否继续?', '提示', {
        //     confirmButtonText: '确定',
        //     cancelButtonText: '取消',
        //     type: 'warning'
        //     }).then(async() => {

        //     const res = await editProductAttention(row);

        //     if(res.code==1){
        //         this.$message.success('保存成功！');
        //         this.getlist();
        //         this.list.map((data)=>{
        //             if(data == val){
        //                 data.relevantData = ''
        //                 data.appointUserName = ''
        //             }
        //         })
        //         this.addVisible=false;
        //     }else{
        //         this.list.map((data)=>{
        //             if(data == val){
        //                 data.relevantData = ''
        //                 data.appointUserName = ''
        //             }
        //         })
        //     }
        //     }).catch(() => {
        //     this.list.map((data)=>{
        //         if(data == val){
        //             data.relevantData = ''
        //             data.appointUserName = ''
        //         }
        //     })
        //     this.$message({
        //         type: 'info',
        //         message: '已取消操作'
        //     });
        //     });




        // },
        async showImg(e) {
            if (e != null) {
                console.log('image', e)
                this.showGoodsImage = true;
                this.imgList = [];
                this.imgList.push(e);
            }
        },
        async closeFunc() {
            this.showGoodsImage = false;
        },
        closeFuncplus(){
            this.showGoodsImageplus = false;
        },
        async moveProductType(val){

            this.$confirm('确定要移至此吗, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
            }).then(async() => {

            const res = await editProductAttention(val);
            console.log("111",res)
            if(res.code==1){
                this.$message.success('添加成功！');
                this.getlist();
                this.list.map((data)=>{
                    if(data == val){
                        data.yearMonthDay = ''
                    }
                })
                this.addVisible=false;
            }else{
                this.list.map((data)=>{
                    if(data == val){
                        data.yearMonthDay = ''
                    }
                })
            }
            }).catch(() => {
            this.list.map((data)=>{
                if(data == val){
                    data.yearMonthDay = ''
                }
            })
            this.$message({
                type: 'info',
                message: '已取消操作'
            });
            });
},

    async getGroupList() {

      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });


    },
        async getPruductNewState() {
            var res = await getProductStateName();
            if (res?.code) {
                this.productnewList = res.data.map(function (item) {
                    var ob = new Object();
                    ob.state = item;
                    return ob;
                })
            }
        },
        async getProductCustomer() {
            var g = await getcusgroups({});

            this.cusgroupslist = g.data.list.map(function (item) {
                var ob = new Object();
                ob.state = item;
                return ob;
            });
        },
        async onchangeplatform(val) {
            this.categorylist = []
            const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
        },
        async init() {
            var res = await getAllProBrand();
            this.brandlist = res.data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            this.filter.ProductType=33;
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await pageProductTaskReport(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            data.forEach(d => {
                d._loading = false
            })
            this.list = data
            this.getEcharts()
        },
        getEcharts() {
            setTimeout(_ => {
                this.list.forEach(e => {
                    let myChart = echarts.init(this.$refs['echarts' + e.proCode]);
                    var series = []
                    this.echartsLoading = true
                    e.series.forEach(s => {
                        if (s.name != '日期')
                            series.push({ smooth: true, showSymbol: false, ...s })
                    })
                    this.echartsLoading = false
                    myChart.setOption({
                        legend: {
                            show: false
                        },
                        grid: {
                            left: "0",
                            top: "6",
                            right: "0",
                            bottom: "0",
                            containLabel: true,
                        },
                        xAxis: {
                            type: 'category',
                            //不显示x轴线
                            show: false,
                            data: e.xAxis
                        },
                        yAxis: {
                            type: 'value',
                            show: false,
                        },
                        series: series
                    });
                    window.addEventListener("resize", () => {
                        myChart.resize();
                    });
                })
            }, 1000)
        },
        async cellclick(row, column, cell, event) {
            if (column.label == '图表') {
                this.detailfilter.procode = row.proCode
                this.detailfilter.platform = row.platform
                this.getecharts()
            }
        },
        async getecharts() {
            this.detailfilter.startTime = null;
            this.detailfilter.endTime = null;
            if (this.detailfilter.timerange) {
                this.detailfilter.startTime = this.detailfilter.timerange[0];
                this.detailfilter.endTime = this.detailfilter.timerange[1];
            }
            var params = { ...this.detailfilter, isMedia: false }
            let that = this;
            const res = await queryGuardProductNewsis(params).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data
                that.buscharDialog.title = res.data.legend[0]
            })
            await this.$refs.buschar.initcharts()
        },
        async changelist(e) {
            this.list = e
        },
        async clicknum(num, ii) {
            var res = await getProductAdveClick({ proCode: num.yearMonthDay })
            this.amontDialog.visible = true;
            this.amontDialog.rows = res.data;
        },
        tableColumnClick(orderColumn, index) {
            let column = {};
            column.prop = orderColumn;
            let currentNode = this.$refs.tableColumn.children[index];
            let className = currentNode.className;
            if (className.indexOf('ascending') > -1) {
                column.order = "descending";
            } else if (className.indexOf('descending') > -1) {
                column.order = "ascending";
            }
            else {
                column.order = "ascending";
            }

            this.searchOrder = column.order;
            this.searchIndex = index;

            this.$refs.table.clearSort();
            this.sortchange(column);
        },
        sortchange(column) {
            if(column.column){
                this.searchIndex=-1;
            }
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selsChange: function (sels) {
            this.sels = sels
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
    },
};
</script>

<style lang="scss" scoped>
.table-div {
    display: inline-block;
    text-align: center;
    width: 100%;
}

.table-div>a {
    padding: 0 10px;
}

.table-div .el-link--inner span {
    left: -9px;
}
.vhtml{
    overflow-x: hidden;
    overflow-y: auto;
    word-break: break-all;
    word-wrap:break-word;
    width: 280px;
    white-space: pre-wrap !important;
}
</style>
