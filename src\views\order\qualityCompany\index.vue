<template>
  <MyContainer>
      <el-tabs v-model="activeName" style="height: 95%; width:100%;" @tab-click="tabclick">
          <el-tab-pane label="质检报告列表" name="first" style="height: 100%;">
              <qualistList ref="refqualistList" />
          </el-tab-pane>
          <el-tab-pane label="设置" v-if="checkPermission('api:operatemanage:StyleCodeManage:GetQualityCheckCompanyList')" name="second" style="height: 100%; overflow: auto;">
              <qualitySetting ref="refqualitySetting" />
          </el-tab-pane>
          
      </el-tabs>
  </MyContainer>
</template>

<script>
 import MyContainer from "@/components/my-container";
import qualistList from '@/views/order/qualityCompany/qualistList.vue'
import qualitySetting from '@/views/order/qualityCompany/qualitySetting.vue'
import checkPermission from '@/utils/permission'
import orderDetails from '@/views/profit/orderDetails.vue'
export default {
  name: 'DingDingShow',
  components: { qualitySetting, qualistList,  },
  data () {
      return {
          activeName: 'first',

      }
  },
  async mounted () {


  },
  methods: {
      async tabclick () {
          this.$nextTick(async () => {
              if (this.activeName == 'first') {
                  // this.$refs.refqualistList.getOrderMenuType();
                  // this.$refs.refqualitySetting.onSearch();
              }
              else if (this.activeName == 'second') {
                  // this.$refs.refqualistList.onSearch();
              }
          })
      }
  }
}
</script>

