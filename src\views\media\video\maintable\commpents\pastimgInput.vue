<template>
    <div :id="keyid" class="my-reply my-comment-reply" :key="keyid">
        <div class="reply-info" style="max-height: 200px;overflow-y: auto;">
            <section tabindex="0" id="replyInput" :style="{minHeight:minHeight}" @paste="changeContent"  contenteditable="true" spellcheck="false" :placeholder="placeholder"
            @input="onDivInput($event)" class="reply-input" :class="'reply-comment-input'+keyid" @blur="onblur">
        </section>
        </div>
    </div>
</template>

<script>
 const clickoutside = {
        bind(el, binding, vnode) {
            function documentHandler(e) {
                if (el.contains(e.target)) {
                    return false;
                }
                if (binding.expression) {
                    binding.value(e);
                }
            }

            el.vueClickOutside = documentHandler;
            document.addEventListener('click', documentHandler);
        },
        // update() {
        // },
        unbind(el, binding) {
            document.removeEventListener('click', el.vueClickOutside);
            delete el.vueClickOutside;
        },
    };
export default {
    name: 'pastimginput',
    props: {
        placeholder: {default:'输入评论...'},
        minHeight: {default:'150px'},
        type: {default:'image'},
        imageWH: {with:'50px',height:'50px'},
        keyid:{default:'videobind'}
    },
    data() {
        return {
            activeNames: ['1'],
            replyopenclose: false,
            btnShow: false,
            index: '0',
            replyComment: '',
            tworeplyComment: '',
            myName: '',
            myHeader: '',
            htmltext:'',
            myId: 19870621,
            to: '',
            toId: -1,
            imgList: [],
            showGoodsImage: false,
            puttext: '',
            comments: this.commendList,
            fortime: 0,
            outthree: [],
            replyCommentt: '',
            imgurllist: [],
            changeContentis: false,
            nomaltext:''
        };
    },

    mounted() {
        // let _this = this;
        // let alltext=document.getElementsByClassName('reply-comment-input'+_this.keyid);
        // if(this.type == 'image') alltext.replyInput.innerHTML = ""
        // this.$forceUpdate();
    },

    methods: {
        focusInput(){
            this.bingval = this.bingval;
        },
        async onblur(){
            await this.suanfa();
            if(this.type == 'image')this.$emit('input',this.bingval);
        },
        suanfa(){
            let _this = this;
            if(this.replyComment?.length>0&&this.imgurllist?.length>0){
                let mapnew = _this.replyComment.map((item,index)=>{
                    return item = item.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi, function (match, capture) {
                        _this.bingval = _this.bingval.replace(capture,_this.imgurllist[index])
                        return  _this.bingval
                    });
                })
            } 
        },
        uploadToServer(file, callback) {
            var xhr = new XMLHttpRequest()
            var formData = new FormData()
            formData.append('file', file)
            xhr.open('post', '/api/uploadnew/file/UploadCommonFileAsync')
            xhr.withCredentials = true
            xhr.responseType = 'json'
            xhr.send(formData)
            xhr.onreadystatechange = () => {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    callback(xhr.response)
                }
            }
        },
        changeContent(e){
                let _this = this;
                const dataTransferItemList = e.clipboardData.items;

                const items = [].slice.call(dataTransferItemList).filter(function (item) {
                    return item.type.indexOf('image') !== -1;
                });
 
                if (items.length === 0) {
                    return;
                }
                const dataTransferItem = items[0];
                const file = dataTransferItem.getAsFile();
                _this.uploadToServer(file, (res) => {
                    this.imgurllist.push(res.data.url)
                    _this.changeContentis = true;
                    if(this.type == 'image')this.suanfa()
                    

                    // if(this.replyComment.length>0){
                    //     this.replyComment.map((item,i)=>{

                    //         let srcReg = /src=[\'\"]?([^\'\"]*)[\'\"]?/i;
                    //         let imgurl=item.match(srcReg)[i];
                    //         _this.replyCommentt=_this.replyComment.replace(imgurl,res.data.url);
                            
                    //     })
                    // } 
                })
                
                     
            },
            onDivInput (e) {
                let _this = this;

                let alltext=document.getElementsByClassName('reply-comment-input'+_this.keyid).length>1?document.getElementsByClassName('reply-comment-input'+_this.keyid)[1]:document.getElementsByClassName('reply-comment-input'+_this.keyid)[0];
                let DomList = document.getElementsByClassName('reply-comment-input'+this.keyid).length>1?document.getElementsByClassName('reply-comment-input'+this.keyid)[1].querySelectorAll('img'):document.getElementsByClassName('reply-comment-input'+this.keyid)[0].querySelectorAll('img');

                alltext.style.color = "black"
                let filterImgContent = e.target.innerHTML.replace(/\<img/g,"<img style='width:50px;height:50px;'")
                this.bingval = filterImgContent;
                // this.replyComment = filterImgContent;
                        // let newmsg = e.target.innerHTML.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi, function (match, capture) {
                        //     let newarr = e.target.innerHTML.replace(capture,_this.imgurllist[index])
                        //     // _this.replyCommentt = newarr
                        //     return newarr
                        // });
                        

                // let DomList=document.getElementById(_this.keyid).querySelectorAll('img')
                for(let i in  DomList){
                        if( DomList[i].style){
                            DomList[i].style.width='50px';
                            DomList[i].style.height='50px';
                        }
                    }

                this.replyComment = filterImgContent.match(/<img.*?>/g);
                if(this.type == 'text') this.$emit('input',e.target.innerText);
                this.htmltext = e.target.innerHTML;
                this.nomaltext = e.target.innerText;
                
            },
    },
};
</script>

<style scoped lang="scss">
    .my-reply {
        padding: 10px;
        background-color: #fafbfc;
    }

    .my-reply .header-img {
        display: inline-block;
        vertical-align: top;
    }

    .my-reply .reply-info {
        display: inline-block;
        margin-left: 5px;
        width: 98%;
    }

    @media screen and (max-width: 1200px) {
        .my-reply .reply-info {
            width: 80%;
        }
    }

    .my-reply .reply-info .reply-input {
        min-height: 20px;
        line-height: 22px;
        padding: 10px 10px;
        color: #ccc;
        background-color: #fff;
        border-radius: 5px;
    }

    .my-reply .reply-info .reply-input:empty:before {
        content: attr(placeholder);
    }

    .my-reply .reply-info .reply-input:focus:before {
        content: none;
    }

    .my-reply .reply-info .reply-input:focus {
        padding: 8px 8px;
        border: 2px solid #409EFF;
        box-shadow: none;
        outline: none;
    }

    .my-reply .reply-btn-box {
        height: 25px;
        margin: 10px 0;

        padding: 8px 8px;
        border: 2px solid #409EFF;
    }

    .my-reply .reply-btn-box .reply-btn {
        position: relative;
        float: right;
        margin-right: 15px;

        padding: 8px 8px;
        border: 2px solid #409EFF;
    }

    .my-comment-reply {
        // margin-left: 50px;
        border: 1px solid #409EFF;
        border-radius: 5px;
    }

    .my-comment-reply .reply-input {
        // width: flex;
    }

    .author-title:not(:last-child) {
        border-bottom: 1px solid rgba(178, 186, 194, 0.3);
    }

    .author-title {
        /* margin-top: 1rem; */
        padding: 10px;
    }

    .author-title .header-img {
        display: inline-block;
        vertical-align: top;
    }

    .author-title .author-info {
        display: inline-block;
        margin-left: 5px;
        width: 60%;
        /* height: 40px; */
        line-height: 20px;
    }

    .author-title .author-info > span {
        display: block;
        cursor: pointer;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .author-title .author-info .author-name {
        color: #409EFF;
        font-size: 18px;
        font-weight: bold;
    }

    .author-title .author-info .author-time {
        font-size: 14px;
    }

    .author-title .icon-btn {
        width: 30%;
        padding: 0 !important;
        float: right;
    }

    @media screen and (max-width: 1200px) {
        .author-title .icon-btn {
            width: 20%;
            padding: 7px;
        }
    }

    .author-title .icon-btn > span {
        cursor: pointer;
    }

    .author-title .icon-btn .iconfont {
        margin: 0 5px;
    }

    .author-title .talk-box {
        margin: 0 0;
    }

    .author-title .talk-box > p {
        margin: 0;
    }

    .author-title .talk-box .reply {
        font-size: 16px;
        color: #000;
        overflow:hidden;
        word-break:break-all;
        word-wrap:break-word;
    }

    .author-title .reply-box {
        margin: 10px 0 0 50px;
        background-color: #efefef;
    }

    .textsmall{
        font-size: 5px;
        color: #909399;
    }

    .position{
        position: relative;
    }

    .iconsize{
        font-size: 24px;
        position: absolute;
        right: 1px;
        top: 10px;
    }

    .twotalk-box{
        margin-left: 50px;
        margin-top: 10px;
        padding: 10px;
        border: 1px solid #fff;
    }

</style>