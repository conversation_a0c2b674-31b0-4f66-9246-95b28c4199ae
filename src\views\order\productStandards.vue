<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss" />
                <div class="serchDay">
                    <div>尺寸</div>
                    <el-select v-model="ListInfo.sizeType" placeholder="符号" style="width: 60px;" class="publicCss"
                        clearable @change="e => {
                            ListInfo.sizeMax = undefined
                            ListInfo.sizeMin = undefined
                        }">
                        <el-option :label="item.label" :value="item.value" v-for="item in operatorList"
                            :key="item.value + '历史平均在途天数'" />
                    </el-select>
                    <el-input-number style="width: 60px;" v-model.trim="ListInfo.sizeMin" placeholder="尺寸(mm)"
                        maxlength="50" clearable :max="999999" :min="0" class="publicCss" :precision="2"
                        :controls="false" v-if="ListInfo.sizeType != '介于'" />
                    <number-range v-else :min.sync="ListInfo.sizeMin" :maxNumber="999999" :max.sync="ListInfo.sizeMax"
                        min-label=" 最小值" max-label="最大值" class="publicCss" />
                </div>
                <div class="serchDay">
                    <div>克重</div>
                    <el-select v-model="ListInfo.grammageType" placeholder="符号" style="width: 60px;" class="publicCss"
                        clearable @change="e => {
                            ListInfo.grammageMax = undefined
                            ListInfo.grammageMin = undefined
                        }">
                        <el-option :label="item.label" :value="item.value" v-for="item in operatorList"
                            :key="item.value + '历史平均在途天数'" />
                    </el-select>
                    <el-input-number style="width: 60px;" v-model.trim="ListInfo.grammageMin" placeholder="克重(g)"
                        maxlength="50" clearable :max="999999" :min="0" class="publicCss" :precision="2"
                        :controls="false" v-if="ListInfo.grammageType != '介于'" />
                    <number-range v-else :min.sync="ListInfo.grammageMin" :maxNumber="999999"
                        :max.sync="ListInfo.grammageMax" min-label=" 最小值" max-label="最大值" class="publicCss" />
                </div>
                <div class="serchDay">
                    <div>厚度</div>
                    <el-select v-model="ListInfo.thicknessType" placeholder="符号" style="width: 60px;" class="publicCss"
                        clearable @change="e => {
                            ListInfo.thicknessMax = undefined
                            ListInfo.thicknessMin = undefined
                        }">
                        <el-option :label="item.label" :value="item.value" v-for="item in operatorList"
                            :key="item.value + '厚度'" />
                    </el-select>
                    <el-input-number style="width: 60px;" v-model.trim="ListInfo.thicknessMin" placeholder="厚度(mm)"
                        maxlength="50" clearable :max="999999" :min="0" class="publicCss" :precision="2"
                        :controls="false" v-if="ListInfo.thicknessType != '介于'" />
                    <number-range v-else :min.sync="ListInfo.thicknessMin" :maxNumber="999999"
                        :max.sync="ListInfo.thicknessMax" min-label=" 最小值" max-label="最大值" class="publicCss" />
                </div>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.optimizeUserNames"
                    v-model="ListInfo.optimizeUserNames" placeholder="优化人/若输入多条请按回车" :clearable="true"
                    :clearabletext="true" :maxRows="100" :maxlength="1000000" @callback="proCodeCallback" title="优化人"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0;height: 400px;" :loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import numberRange from "@/components/number-range/index.vue";
import { GetYWOptimizeSizeChangeLogList } from '@/api/inventory/YWOptimizeGoods'
const operatorList = [
    { label: '大于', value: '大于' },
    { label: '小于', value: '小于' },
    { label: '等于', value: '等于' },
    { label: '介于', value: '介于' },
]
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'length', label: '尺寸(mm)', formatter: (row) => '长 ' + row.length + '(mm)' + ' 宽 ' + row.width + '(mm)' + ' 高 ' + row.height + '(mm)' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'grammage', label: '克重(g)', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'thickness', label: '厚度(mm)', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '优化时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdUserName', label: '优化人', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, inputYunhan, numberRange
    },
    props: {
        editRow: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            that: this,
            operatorList,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
                endDate: dayjs().format('YYYY-MM-DD'),
                goodsCode: this.editRow.goodsCode,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        proCodeCallback(val) {
            this.ListInfo.optimizeUserNames = val
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetYWOptimizeSizeChangeLogList(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }

    .serchDay {
        display: flex;
        align-items: center;
        font-size: 14px;
        gap: 5px;
    }
}
</style>
