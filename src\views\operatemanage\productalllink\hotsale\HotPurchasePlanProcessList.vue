<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                <el-button-group style="padding-bottom:2px;margin-top:-4px;">
                     <el-button style="padding: 0;margin: 0;border:none;">
                        <el-date-picker v-model="generationTime" type="daterange" unlink-panels range-separator="至" start-placeholder="生成开始时间"
                          end-placeholder="生成结束时间" style="width: 250px;" :value-format="'yyyy-MM-dd'" @change="changeTime($event,1)">
                        </el-date-picker>
                        <el-date-picker v-model="allocateTime" type="daterange" unlink-panels range-separator="至" start-placeholder="分配开始时间"
                          end-placeholder="分配结束时间" style="width: 250px;" :value-format="'yyyy-MM-dd'" @change="changeTime($event,2)">
                        </el-date-picker>
                        <el-select v-model="Filter.distributeState"  clearable placeholder="分配采购状态" style="width:110px;">
                            <el-option v-for="item in  distributeStateOpts" :key="'distributeState'+item.label"
                            :label="item.label" :value="item.value"></el-option>
                        </el-select>
                        <el-select v-model="Filter.purchaseOrderGenerateState"  clearable placeholder="生成采购单状态" style="width:130px;">
                            <el-option v-for="item in  generateStateOpts" :key="'purchaseOrderGenerateState'+item.label"
                            :label="item.label" :value="item.value"></el-option>
                        </el-select>
                        <el-select v-model="Filter.purchaseOrderStatus"  clearable placeholder="采购单状态" style="width:100px;">
                            <el-option v-for="item in  purOrderStatusList" :key="'purchaseOrderStatus'+item"
                            :label="item" :value="item"></el-option>
                        </el-select>

                        <el-select v-model="Filter.warehouse"  clearable placeholder="仓库" style="width:130px;">
                            <!-- <el-option v-for="item in  warehouselist" :key="'warehouse'+item.label"
                            :label="item.label" :value="item.value"></el-option> -->
                            <el-option v-for="item in warehouselist" :key="item.name" :label="item.name" :value="item.wms_co_id" />
                        </el-select>

                        <el-button style="padding: 0;margin: 0;border:none">
                          <el-select v-model="Filter.chooseGroupName" placeholder="选品小组" style="width: 130px" clearable filterable>
                            <el-option v-for="item in directorGroupList" :key="item.value" :label="item.value" :value="item.value" />
                          </el-select>
                        </el-button>

                        <el-button style="padding: 0;margin: 0;border:none">
                            <el-input v-model="Filter.chooseUserName" type="text" maxlength="20" clearable placeholder="选品人"
                                style="width:100px;" />
                        </el-button>

                        <el-input v-model.trim="Filter.keywords" type="text" maxlength="100" clearable placeholder="请输入关键字..." style="width:200px;" >
                            <el-tooltip slot="suffix" class="item" effect="dark" :content="keywordsTip" placement="bottom">
                                <i  class="el-input__icon el-icon-question"></i>
                            </el-tooltip>
                        </el-input>

                    </el-button>

                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="()=>{Filter={};}">清空条件</el-button>

                </el-button-group>
            </el-form>
        </template>

        <vxetablebase :id="'HotPurchasePlanProcessList202212281757'"
            :tableData='tbdatalist' :tableCols='tableCols' v-loading="listLoading"
            :border='true'
            :treeProp="{ rowField: 'oid', parentField: 'parentId' }"
             @sortchange='sortchange'
            >
                <!-- <template slot="right">
                    <vxe-column title="操作"  :field="'col'+(tableCols.length+1)"  width="90" fixed="right">
                        <template #default="{ row }">
                            <el-button type="text" v-if="!(row.isGoodsCompete==1)" @click="onEditSkuOrder(row,true)">编辑</el-button>
                            <el-button type="text" v-if="!(row.isGoodsCompete==1)" @click="onEditSkuOrder(row,false)">查看</el-button>
                        </template>
                    </vxe-column>
                </template> -->

            </vxetablebase>


        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettbdatalist1" />
        </template>

    </my-container>
</template>
<script>

    import {
        GetPurchasePlanProcessRateList
    } from '@/api/operatemanage/productalllink/alllink'
    import dayjs from "dayjs";

    import { formatmoney, formatPercen, getUrlParam, platformlist, formatPlatform,
    formatNoLink,formatTime, setStore, getStore, formatLinkProCode , formatWarehouse
    } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import { getAllWarehouse } from '@/api/inventory/warehouse'

    import vxetablebase from "@/components/VxeTable/vxetablebase.vue";
    import { getDirectorGroupList } from '@/api/operatemanage/base/shop'


    const distributeStateOpts=[
        {label:'待分配',value:0},
        {label:'已分配',value:1},
        {label:'已归档',value:-1}
    ];
    const distributeStateFmt=(val)=>{
        let opt=distributeStateOpts.find(x=>x.value==val);
        if(opt)
            return opt.label;

        return val;
    }

    const generateStateOpts=[
        {label:'未生成',value:0},
        {label:'已生成',value:1}
    ];
    const generateStateFmt=(val)=>{
        let opt=generateStateOpts.find(x=>x.value==val);
        if(opt)
            return opt.label;

        return val;
    }


    const auditStateOpts=[
        {label:'已拒绝',value:-1},
        {label:'未发起',value:0},
        {label:'审核中',value:1},
        {label:'已审核',value:2},
    ]
    const auditStateFmt=(val)=>{
        let opt=auditStateOpts.find(x=>x.value==val);
        if(opt)
            return opt.label;

        return val;
    }




    const tableCols = [

        { istrue: true, prop: 'goodsCompeteShortName',label: '产品简称/供应商/商品名称', width: '120', sortable: 'custom' ,fixed:'left',treeNode:true, fixed:'left'},
        { istrue: true, prop: 'goodsImageUrl', label: '商品图片', width: '80',  type:'images',fixed:'left' },
        { istrue: true, prop: 'styleCode',label: '款式编码/商品编码', width: '160', sortable: 'custom',fixed:'left' },
        { istrue: true, prop: 'groupName',label: '小组', width: '100', sortable: 'custom',fixed:'left' },
        { istrue: true, prop: 'userNickName',label: '添加人', width: '80', sortable: 'custom',fixed:'left' },
        //{ istrue: true, prop: 'goodsCode',label: '商品编码', width: '120'},
        //{ istrue: true, prop: 'goodsName',label: '商品名称', width: '120'},
        { istrue: true, prop: 'buyNo',label: '采购单号', width: '94', sortable: 'custom',align:'center',}, // type: 'html', formatter: (row) => formatNoLink(row.buyNo == '0' ? ' ' :  row.buyNo)
        { istrue: true, prop: 'purchaseOrderIndexNo',label: 'ERP单号', width: '90', sortable: 'custom',align:'center', }, //type: 'html', formatter: (row) => formatNoLink(row.purchaseOrderIndexNo == '0' ? ' ' :  row.purchaseOrderIndexNo)
        { istrue: true, prop: 'purchaseOrderStatus',label: '采购单状态', width: '110', sortable: 'custom',align:'center'},
        { istrue: true, prop: 'planCount',label: '采购数量', width: '100', sortable: 'custom',align:'right'},
        { istrue: true, prop: 'wareInCount',label: '入库数量', width: '100', sortable: 'custom',align:'right'},
        { istrue: true, prop: 'processPercent',label: '入库占比', width: '120', sortable: 'custom',type:'progress'},

        { istrue: true, prop: 'warehouse', label: '仓库', width: '120', sortable: 'custom', formatter: (row) => row.warehouseName  },
        { istrue: true, prop: 'supplierName', label: '供应商名称', width: '120', sortable: 'custom' },

        { istrue: true, prop: 'goodsCompeteId', label: '竞品ID', width: '130', sortable: 'custom',  type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.goodsCompeteId) },
        { istrue: true, prop: 'goodsCompeteName', label: '竞品标题', width: '160', sortable: 'custom' },
        { istrue: true, prop: 'goodsCompeteImgUrl', label: '竞品图片', width: '80', type: 'images' },

        { istrue: true, prop: 'createdTime', label: '生成时间', width: '140', align:'center',sortable: 'custom' },
        { istrue: true, prop: 'distributeState', label: '分配状态', width: '98',align:'center', sortable: 'custom', formatter: (row) => distributeStateFmt(row.distributeState) },
        { istrue: true, prop: 'distributeTime', label: '分配时间', width: '140', align:'center',sortable: 'custom' },
        { istrue: true, prop: 'brandName', label: '采购人员', width: '100', align:'center',sortable: 'custom' },
       // { istrue: true, prop: 'purchaseApplyState', label: '计划审核状态', width: '120',align:'center', sortable: 'custom', formatter: (row) => auditStateFmt(row.purchaseApplyState) },
        { istrue: true, prop: 'purchaseOrderGenerateTime', label: '生成采购单时间', width: '140', align:'center',sortable: 'custom' },

        { istrue: true, prop: 'purchaseOrderCheckDate', label: '采购单审核时间', width: '140', align:'center',sortable: 'custom' },

        { istrue: true, prop: 'purchaseOrderGenerateReason', label: '生成失败原因', width: '140', align:'center',sortable: 'custom' },

        { istrue: true, prop: 'inspectionReportImgUrl', label: '质检报告', width: '80', type: 'images' },
        { istrue: true, prop: 'patentQualificationImgUrls', label: '专利资质', width: '80', type: 'images' },
        { istrue: true, prop: 'patentQualificationPdfUrls', label: '专利PDF', width: '80', type: 'files' },
        { istrue: true, prop: 'packingImgUrls', label: '包装图片', width: '80', type: 'images' },

        // { istrue: true, prop: 'supplierPlatForm', label: '供应商平台', width: '120', sortable: 'custom' },

        // { istrue: true, prop: 'supplierLink', label: '供应商链接', width: '120'},
        // { istrue: true, prop: 'supplierGoodLink', label: '供应商产品链接', width: '140'},
    ];

    const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
    const endTime = formatTime(new Date(), "YYYY-MM-DD");
    const star = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");

    export default {
        name: "HotPurchasePlanProcessList",
        components: { MyContainer,     vxetablebase },
        data() {
            return {
                generationTime: [],//生成时间
                allocateTime: [],//分配时间
                directorGroupList: [],
                distributeStateOpts,
                generateStateOpts,
                auditStateOpts,
                warehouselist: [],
                purOrderStatusList:['待下单','待审核','已确认','作废','完成','已确定'],
                that: this,
                Filter: {
                    createdTimeStart: null,//开始生成时间
                    createdTimeEnd: null,//结束生成时间
                    distributeTimeStart: null,//开始分配时间
                    distributeTimeEnd: null,//结束分配时间
                    distributeState:null,
                    purchaseOrderGenerateState:null,
                    warehouse:null,
                    keywords:'',
                    chooseGroupName:null,
                    chooseUserName:null
                },
                tbdatalist: [],
                tableCols: tableCols,
                total: 0,
                pager: { OrderBy: "", IsAsc: false },
                listLoading: false,
                pageLoading: false,
                selfInfo: {

                },
                sels:[]   ,
                keywordsTip:'支持搜索的内容：商品简称、供应商、采购、分配人、竞品标题、采购单号、ERP单号、生成采购单失败原因'
            };
        },
        async mounted() {
            //默认给近30天时间
            this.Filter.createdTimeStart = dayjs().subtract(30, 'day').format('YYYY-MM-DD')
            this.Filter.createdTimeEnd = dayjs().format('YYYY-MM-DD')
            this.generationTime = [this.Filter.createdTimeStart, this.Filter.createdTimeEnd]
            const userInfoName = "hotsalegoods_selfuserinfo";
            let selfInfo4Store = getStore(userInfoName);
            if (selfInfo4Store) {
                this.selfInfo = selfInfo4Store;
            }
            var res3 = await getAllWarehouse();
            this.warehouselist = res3.data;
            this.onSearch();
            const res2 = await getDirectorGroupList({})
            this.directorGroupList = [{ key: '未知', value: '未知' }].concat(res2.data || []);
        },
        methods: {
            changeTime(e,val){
              if(val==1){
                  this.Filter.createdTimeStart = e ? e[0] : null;
                  this.Filter.createdTimeEnd = e ? e[1] : null;
              }else if(val==2){
                  this.Filter.distributeTimeStart = e ? e[0] : null;
                  this.Filter.distributeTimeEnd = e ? e[1] : null;
              }
            },
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderField = column.prop;
                    // var bFields = [];// ['skuDataState', 'skuDataTime', 'cmRefInfoState', 'cmRefInfoLastOkTime'];
                    // if (orderField == "orgPlatformName") {
                    //     orderField = "PlatformName";
                    // } else if (bFields.indexOf(orderField) > -1) {
                    //     orderField = "b." + orderField;
                    // }

                    this.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                }
                this.onSearch();
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.gettbdatalist1();
            },
            async gettbdatalist1() {
                if (this.Filter.gDate) {
                    this.Filter.startGDate = this.Filter.gDate[0];
                    this.Filter.endGDate = this.Filter.gDate[1];
                }
                else {
                    this.Filter.startGDate = null;
                    this.Filter.endGDate = null;

                }
                const para = { ...this.Filter };
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...para,
                };

                this.listLoading = true;
                const res = await GetPurchasePlanProcessRateList(params);

                this.listLoading = false;

                this.total = res.data.total;

                this.tbdatalist = res.data.list;


            }
        },
    };
</script>
