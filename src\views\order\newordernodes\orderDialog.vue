<template >
    <my-container v-loading="pageLoading" style="height:100%">
        <ces-table style="height:90%" :ref="tableKey" :key="tableKey" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='newOrderList' :isSelection='false' :summaryarry="summaryarry"
            :tableCols='tableCols' :isSelectColumn="true">
            <template slot='extentbtn' style="height:100%">
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input placeholder="内部订单号" v-model="filter.orderNoInner" style="width: 100px"
                            clearable></el-input>
                    </el-button>
                    <el-button style="padding: 0;">
                        <el-select filterable v-model="filter.platform" placeholder="平台" @change="onchangeplatform"
                            clearable style="width: 70px">
                            <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;">
                        <el-select filterable v-model="filter.shopCode" placeholder="店铺" clearable style="width: 100px">
                            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                                :value="item.shopCode" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select v-model="filter.warehouse" clearable filterable placeholder="发货仓" style="width: 100px">
                            <el-option v-for="item in warehouselist" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input type="number" placeholder="最小拣货次数" v-model="filter.minPickCount" style="width: 130px"
                            @input="onInput()"
                            oninput="if(value){value=value.replace(/[^\d]/g,'')} if(value<=0){value=0} if(value>**********){value=**********}"
                            clearable>
                        </el-input>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input type="number" placeholder="最大拣货次数" v-model="filter.maxPickCount" style="width: 130px"
                            @input="onInput()"
                            oninput="if(value){value=value.replace(/[^\d]/g,'')} if(value<=0){value=0} if(value>**********){value=**********}"
                            clearable>
                        </el-input>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input placeholder="订单标签包含" v-model="filter.label" style="width: 120px" clearable
                            maxlength="200"></el-input>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input placeholder="订单标签不包含" v-model="filter.labelNo" style="width: 140px" clearable
                            maxlength="200"></el-input>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button v-if="checkPermission('PressNewOrderNodeExPort')" type="primary"
                        @click="ExPortExecl">导出</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getNewOrderListAsync" />
        </template>
        <el-dialog title="订单日志信息" append-to-body v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%"
            height="600px" v-dialogDrag>
            <orderLogPage ref="orderLogPage" :orderNoInner="sendOrderNoInner" style="z-index:10000;height:600px" />
        </el-dialog>
    </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import { platformlist, orderpositionlist } from '@/utils/tools';//enmSendWarehouse as warehouselist,
import { getTbWarehouseList, getNewOrderNodeDialogList, exportOrderNodeDialogAsync } from '@/api/order/newordernodes';
import orderLogPage from "@/views/order/logisticsWarning/orderLogPage.vue";
const tableCols = [
    { istrue: true, prop: 'orderNoInner', label: '内部订单号', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showLogDetail(row) },
    { istrue: true, prop: 'orderNo', label: '线上订单号', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'shopCode', label: '店铺名称', width: '110', sortable: 'custom', formatter: (row) => row.shopName },
    { istrue: true, prop: 'payTime', label: '付款时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'confirmTime', label: '审单时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'printTime', label: '打单时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'pickTime', label: '配货时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'deliveryTime', label: '发货时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'weighTime', label: '称重时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'batchStartTime', label: '领批次时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'batchEndTime', label: '批次结束时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'inWarehouseTimeHH', label: '在仓时长', width: '100', sortable: 'custom', sortable: 'custom', tipmesg: '小时:发货时间-付款时间' },//(如果付款时间在上午8点到下午4点前 则以当前付款时间作为计算 否则以第二天上午8点作为计算时间)
    { istrue: true, prop: 'confirmSpanTimeHH', label: '审单时长', width: '100', sortable: 'custom', tipmesg: '小时:审单时间-付款时间' },//(付款时间大于下午4点则为付款时间加32小时否则判断是不是在上午8点前是的话则为付款时间加8小时否则获取真实付款时间)
    { istrue: true, prop: 'printSpanTimeHH', label: '打单时长', width: '100', sortable: 'custom', tipmesg: '小时:打单时间-审单时间' },
    { istrue: true, prop: 'batchWaitSpanTimeHH', label: '波次等待时长', width: '120', sortable: 'custom', tipmesg: '小时:领批次时间-打单时间(审单时间)' },//波次等待时长
    { istrue: true, prop: 'batchSpanTimeHH', label: '波次时长', width: '100', sortable: 'custom', tipmesg: '小时:批次结束时间-领批次时间' },//波次时长  ,tipmesg:'分钟'
    { istrue: true, prop: 'pickSpanTimeHH', label: '配货时长', width: '100', sortable: 'custom', tipmesg: '小时:配货时间(发货时间)-领批次时间或者为:配货时间(发货时间)-打单时间' },
    { istrue: true, prop: 'deliverySpanTimeHH', label: '打包时长', width: '100', sortable: 'custom', tipmesg: '小时:发货时间-配货时间' },
    { istrue: true, prop: 'confirmer', label: '审单人员', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'printer', label: '打单人员', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'picker', label: '配货人', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'batchPicker', label: '波次操作人', width: '90', sortable: 'custom' },//领波次人
    { istrue: true, prop: 'delivery', label: '发货人', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'pickCount', label: '拣货次数', width: '85', sortable: 'custom' },
    { istrue: true, prop: 'label', label: '订单标签', width: '240', sortable: 'custom' },
    { istrue: true, prop: 'orderType', label: '单据类型', width: '85', sortable: 'custom', formatter: (row) => row.orderType == 1 ? 'PDA' : '纸质' },
];
export default {
    name: "orderDialog",
    components: { cesTable, MyContainer, orderLogPage },
    props: {
        filter: { type: Object, default: null },
    },
    data() {
        return {
            tableKey: "orderDialog",
            pageLoading: false,
            tableCols: tableCols,
            shopList: [],
            platformlist: platformlist,
            warehouselist: [],//warehouselist,
            orderpositionlist: orderpositionlist,
            that: this,
            sels: [], // 列表选中列
            newOrderList: [],
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "orderNoInner", IsAsc: false },
            dialogHisVisible: false,
            sendOrderNoInner: "",
        }
    },
    async mounted() {
        await this.getInventoryWareHouseList();
        // this.filter.minPickCount = 0;
        // this.filter.maxPickCount = 0;
        if (this.filter.shopCode) {
            if (this.filter.shopCode != null) {
                await this.onchangeplatform(this.filter.platform);
            }
        }
        this.onSearch();
    },
    methods: {
        async getInventoryWareHouseList() {
            if (this.warehouselist.length <= 0) {
                let wares = await getTbWarehouseList();
                if (wares?.success && wares?.data && wares?.data.length > 0) {
                    wares?.data.forEach(f => {
                        if (f.name.indexOf("代发") <= -1 &&
                            f.name.indexOf("罗兵邮邮仓") <= -1 &&
                            f.name.indexOf("JD-昀晗义乌仓") <= -1 &&
                            f.name.indexOf("昀晗-包装厂") <= -1
                        )
                            this.warehouselist.push({ value: f.wms_co_id, label: f.name });
                    });
                }
            }
        },
        showLogDetail(row) {
            this.dialogHisVisible = true;
            this.sendOrderNoInner = row.orderNoInner;
        },
        onInput() {
            this.$forceUpdate();
        },
        async onchangeplatform(val) {
            this.categorylist = []
            const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
        },
        //排序查询
        sortchange(column) {
            if (!column.order) {
                this.pager = {};
            }
            else {
                var orderBy = column.prop;
                if (orderBy.endsWith("HH")) {
                    orderBy = orderBy.replace("HH", "");
                }
                this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            this.onSearch();
        },
        onRefresh() {
            this.onSearch()
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getNewOrderListAsync();
        },
        async getNewOrderListAsync() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = { ...pager, ...page, ... this.filter }
            this.pageLoading = true;
            let res;
            res = await getNewOrderNodeDialogList(params);
            this.pageLoading = false;
            this.total = res.total
            this.newOrderList = res.list;
            this.summaryarry = res.summary;
        },
        async ExPortExecl() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = { ...pager, ...page, ... this.filter }
            var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: true });
            var res;
            res = await exportOrderNodeDialogAsync(params);
            loadingInstance.close();
            if (res?.data?.type == 'application/json') {
                return;
            }
            const aLink = document.createElement("a");
            var blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '全部订单_' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
    }
}
</script>
