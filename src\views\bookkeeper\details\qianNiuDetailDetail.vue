<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="filter"
        @submit.native.prevent>
        <el-form-item  label="年月:">
            <el-date-picker style="width: 110px" v-model="filter.yearMonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
        </el-form-item>
        <el-form-item label="所属店铺:" label-position="right" >
          <el-select filterable v-model="filter.shopCode" placeholder="请选择" class="el-select-content">
            <el-option key="所有" label="所有" value></el-option>
            <el-option 
              v-for="item in shopList"
              :key="item.shopCode"
              :label="item.shopName"
              :value="item.shopCode">             
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订单编号:" label-position="right" >
            <el-input v-model="filter.serialNumberOrder" style="width:183px;"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :tableData='ZTCKeyWordList' 
          @select='selectchange' :isSelection='false' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" :isSelectColumn='true'>
      <el-table-column type="expand">
        <template slot-scope="props">
        <div>
          <el-table :data="props.row.detaildata" style="width: 100%">
            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
            </el-table-column>
          </el-table>
        </div>
      </template>
      </el-table-column>
       <template slot='extentbtn'>
          <el-button-group>            
            <el-button type="primary" @click="onRefresh">刷新</el-button>
          </el-button-group>
        </template>
    </ces-table>    
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
    </template>

     <el-dialog title="导入" :visible.sync="dialogImportVisible" width="40%" v-dialogDrag>
      <span>
          <el-row>
            <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker style="width: 100%" v-model="importfilter.yearmonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
         </el-col>
         </el-row>
          <el-row>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                 <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx"
                     :on-change="uploadChange" :on-remove="uploadRemove" :http-request="uploadFile" :data="fileparm">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
              </el-upload>
            </el-col>
          </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogImportVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { formatPlatform,formatLink} from "@/utils/tools";
import {getQianNiuDetailPageList } from '@/api/bookkeeper/financialDetail'
import {importFirstNewQianNiuDetailV2} from '@/api/bookkeeper/import'

const tableCols =[
      {istrue:true,prop:'orderNumber',label:'订单id',sortable:'custom', width:'auto'},
      {istrue:true,prop:'amountFore',label:'预估新客拉新费用',sortable:'custom', width:'auto',type:'html',formatter:(row)=>{return row.amountFore?.toFixed(2)}},
      {istrue:true,prop:'amountPayCus',label:'新客支付金额',sortable:'custom', width:'auto',type:'html',formatter:(row)=>{return row.amountPayCus?.toFixed(2)}},
      {istrue:true,prop:'amountPayReal',label:'实际新客拉新费用',sortable:'custom', width:'auto',type:'html',formatter:(row)=>{return row.amountPayReal?.toFixed(2)}},
      {istrue:true,prop:'amountReturn',label:'退回新客拉新费用',sortable:'custom', width:'auto',type:'html',formatter:(row)=>{return row.amountReturn?.toFixed(2)}},
      {istrue:true,prop:'yearMonth',label:'月',sortable:'custom', width:'auto'},
     ];
const tableHandles=[
        {label:"导入", handle:(that)=>that.onstartImport()}
      ];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
  data() {
    return {
      that:this,
      filter: {
        yearMonth:null,
        shopCode:null,
      },
      importfilter:{yearmonth:null},
      fileparm:{},
      shopList:[],
      userList:[],
      groupList:[],
      ZTCKeyWordList: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      total: 0,
      pager:{OrderBy:"id",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids:[],
      dialogImportVisible:false,
      uploadLoading:false,
      fileList:[],
    };
  },
  async mounted() {
    await this.getShopList();
  },
  methods: {
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    async getShopList(){
      const res1 = await getAllShopList();
      this.shopList=[];
        res1.data?.forEach(f => {
          if(f.isCalcSettlement&&f.shopCode)
              this.shopList.push(f);
        });
    },
    onRefresh(){
        this.onSearch()
    },
    onSearch(){
       this.$refs.pager.setPage(1);
       this.getList();
    },
    async getList(){
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter};
      this.listLoading = true;
      const res = await getQianNiuDetailPageList(params);
      this.listLoading = false;
      this.total = res.data?.total
      this.ZTCKeyWordList = res.data?.list;
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
    async onstartImport(){
      this.dialogImportVisible=true;
    },
    uploadChange(file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for(var i=0;i<fileList.length;i++){
          if(fileList[i].status=="success")
            list.push(fileList[i]);
          else
            list.push(fileList[i].raw);
        }
        this.fileList = list;
      }
    },
    uploadRemove(file, fileList){
       this.uploadChange(file, fileList);
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      if (!this.importfilter.yearmonth) {
       this.$message({type: 'warning',message: '请选择年月!'});
       return;
      }
      console.log('this.fileList',this.fileList)
      if(!this.fileList||this.fileList.length==0){
        this.$message({message: "请先选择文件", type: "warning" });
        return false;
      }
      this.uploadLoading=true
      this.$refs.upload.submit();
    },
   async uploadFile(item) {
     if(!item||!item.file||!item.file.size){
        this.$message({message: "请先选择文件", type: "warning" });
        this.uploadLoading=false
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("yearmonth", this.importfilter.yearmonth);
      var res= await importFirstNewQianNiuDetailV2(form);
      if (res.code==1) this.$message({message: "上传成功,正在导入中...", type: "success" });
      else this.$message({message: res.msg, type: "warning" });
      this.uploadLoading=false
      this.$refs.upload.clearFiles();
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>