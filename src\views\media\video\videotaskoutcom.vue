<template>
 <my-container>
<el-form  ref="UploadSuccessVideo" label-width="120px" >
    <el-row style="height:200px">
            <el-col :span="8" >
                <el-row>
                    <el-form-item label="成品视频一">
                        <!-- <el-upload ref="upload1"
                                :auto-upload="true" 
                                :multiple="true" 
                                :data="1"
                                :show-file-list="false" 
                                :http-request="UpSuccesslaod"
                                >
                                <el-button size="small" type="primary">点击上传<i class="el-icon-upload el-icon--right"></i></el-button>
                        </el-upload> -->
                    </el-form-item>
                </el-row>
                <el-row>
                    <ul  style="height:160px;overflow-y:scroll;" >
                        <li v-for="(i,index) in UpSuccesslaodList1" class="infinite-list-item" :key="index">
                            <el-link  @click="playVideo(i.url)">{{ i.fileName }}</el-link> <span>|</span>
                            <el-link type="primary"  @click="downfile(i)" >下载</el-link> 
                        </li>
                    </ul>
                </el-row>
            </el-col>
            <el-col :span="8">
                <el-row>
                 <el-form-item label="成品视频二">
                         <!--   <el-upload ref="upload2"
                                :auto-upload="true" 
                                :multiple="true" 
                                :data="2"
                                :show-file-list="false" 
                                :http-request="UpSuccesslaod"
                                >
                                <el-button size="small" type="primary">点击上传<i class="el-icon-upload el-icon--right"></i></el-button>
                        </el-upload>-->
                    </el-form-item> 
                </el-row>
                <el-row>
                    <ul  style="height:160px;overflow-y:scroll;" >
                        <li v-for="(i,index) in UpSuccesslaodList2" :key="index">
                            <el-link  @click="playVideo(i.url)">{{ i.fileName }}</el-link> <span>|</span>
                            <el-link type="primary"   @click="downfile(i)" >下载</el-link> 
                        </li>
                    </ul>
                </el-row>
            </el-col>
            <el-col :span="8">
                <el-row>
                  <el-form-item label="成品视频三">
                     <!--      <el-upload ref="upload3"
                                :auto-upload="true" 
                                :multiple="true" 
                                :data="3" 
                                :show-file-list="false" 
                                :http-request="UpSuccesslaod"
                                >
                                <el-button size="small" type="primary">点击上传<i class="el-icon-upload el-icon--right"></i></el-button>
                        </el-upload>-->
                    </el-form-item> 
                </el-row>
                <el-row  style="height: 100; overflow:auto">
                    <ul  style="height:160px;overflow-y:scroll;" >
                        <li v-for="(i,index) in UpSuccesslaodList3" class="infinite-list-item" :key="index">
                            <el-link  @click="playVideo(i.url)">{{ i.fileName }}</el-link> <span>|</span>
                            <el-link type="primary"   @click="downfile(i)" >下载</el-link> 
                        </li>
                    </ul>
                </el-row>
            </el-col> 
        </el-row>
        <el-row style="height:200px">
            <el-col :span="8" >
                <el-row>
                    <el-form-item label="成品视频四">
                       <!--  <el-upload ref="upload4" 
                                :auto-upload="true" 
                                :multiple="true" 
                                :data="4"
                                :show-file-list="false" 
                                :http-request="UpSuccesslaod"
                            >
                            <el-button size="small" type="primary">点击上传<i class="el-icon-upload el-icon--right"></i></el-button>
                        </el-upload> -->
                    </el-form-item>
                </el-row>
                <el-row>
                    <ul  style="height:160px;overflow-y:scroll;" >
                        <li v-for="(i,index) in UpSuccesslaodList4" class="infinite-list-item" :key="index">
                            <el-link  @click="playVideo(i.url)">{{ i.fileName }}</el-link> <span>|</span>
                            <el-link type="primary"   @click="downfile(i)" >下载</el-link> 
                        </li>
                    </ul>
                </el-row>
            </el-col>
            <el-col :span="8">
                <el-row>
                    <el-form-item label="成品视频五">
                       <!--  <el-upload ref="upload5"
                                :auto-upload="true" 
                                :multiple="true" 
                                :data="5"
                                :show-file-list="false" 
                                :http-request="UpSuccesslaod"
                                >
                                <el-button size="small" type="primary">点击上传<i class="el-icon-upload el-icon--right"></i></el-button>
                        </el-upload> -->
                    </el-form-item>
                </el-row>
                <el-row>
                    <ul  style="height:160px;overflow-y:scroll;" >
                        <li v-for="(i,index) in UpSuccesslaodList5" class="infinite-list-item" :key="index">
                            <el-link  @click="playVideo(i.url)">{{ i.fileName }}</el-link> <span>|</span>
                            <el-link type="primary"   @click="downfile(i)">下载</el-link>
                        </li>
                    </ul>
                </el-row>
            </el-col>
            <el-col :span="8">
           
            </el-col>
        </el-row> 
</el-form>
<el-dialog title="视频播放" :visible.sync="dialogVisible" width="50%" @close="closeVideoPlyer" append-to-body>
            <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
</my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
//播放器
import videoplayer from '@/views/media/video/videoplayer'
import { getOutcomeVideo }from '@/api/media/vediotask';

export default {
        props:["row"],
        components: { MyContainer,  videoplayer },
        data() {
            return {
                dialogVisible :false,
                 videoplayerReload:false,
                UpSuccesslaodTaskId:null,
                UpSuccesslaodList1:[],
                UpSuccesslaodList2:[],
                UpSuccesslaodList3:[],
                UpSuccesslaodList4:[],
                UpSuccesslaodList5:[],
                UpSuccesslaodTaskId:0,
            };
        },
        watch: {
        },
        async mounted() {
           await this.UploadComplatVideoTask();
        },
        methods: {
        playVideo(videoUrl) {
            this.videoplayerReload = false;
            this.videoplayerReload = true;
            this.dialogVisible = true;
            this.videoUrl = videoUrl;
        },
        async closeVideoPlyer() {
            this.videoplayerReload = false;
        },
         //上传成品视频视频
            async UploadComplatVideoTask(){
               //获取成品视频视频
               this.UpSuccesslaodTaskId = this.row.videoTaskId;
               console.log(this.row);
               const res = await getOutcomeVideo({ videoTaskId:this.UpSuccesslaodTaskId});
               this.UpSuccesslaodList1=[];
                this.UpSuccesslaodList2=[];
                this.UpSuccesslaodList3=[];
                this.UpSuccesslaodList4=[];
                this.UpSuccesslaodList5=[];
                this.UpSuccesslaodTaskId=0;
               if (res?.success) {
                
                    this.UpSuccesslaodList1=res.data[0].details;
                    this.UpSuccesslaodList2=res.data[1].details;
                    this.UpSuccesslaodList3=res.data[2].details;
                    this.UpSuccesslaodList4=res.data[3].details;
                    this.UpSuccesslaodList5=res.data[4].details;
                } 
            }, 
          /*   //关闭成品视频视频
            closeUploadSuccessVideo(){
                this.UpSuccesslaodList1=[];
                this.UpSuccesslaodList2=[];
                this.UpSuccesslaodList3=[];
                this.UpSuccesslaodList4=[];
                this.UpSuccesslaodList5=[];
                this.addloadingText =null;
                this.UpSuccesslaodTaskId=0;
            }, */
     
          /*   async UpSuccesslaod(item){
                this.addloading = true;
                this.atfterUplaodData = null;
                var vindex  = item.data;
                var filename = item.file.name;
                this.addloadingText = "正在上传:"+item.file.name;
                await this.AjaxFile(item.file, 0,"");
                if(this.atfterUplaodData !=null)
                {
                    await this.AfterUpSuccesslaod(vindex,filename);
                }
                this.addloadingText = null;
                this.addloading = false;
            },
            //上传成果
            async AfterUpSuccesslaod(index,filename){
                const form = new FormData();
                var cururl = this.atfterUplaodData.url;
                this.atfterUplaodData.fileName =filename;
                form.append("upfile",  JSON.stringify(this.atfterUplaodData)); 
                form.append("index", index);
                form.append("taskId",  this.UpSuccesslaodTaskId);
                const res = await uploadOutcomeVideo(form);
                if (res?.success) {
                    switch(index){
                        case 1:
                            this.UpSuccesslaodList1.push({ fileName :res.data.fileName,upLoadvedioid : res.data.uploadId ,url:cururl});
                        break;
                        case 2:
                            this.UpSuccesslaodList2.push({ fileName :res.data.fileName,upLoadvedioid : res.data.uploadId ,url:cururl});
                        break;
                        case 3:
                            this.UpSuccesslaodList3.push({ fileName :res.data.fileName,upLoadvedioid : res.data.uploadId ,url:cururl});
                        break;
                        case 4:
                            this.UpSuccesslaodList4.push({ fileName :res.data.fileName,upLoadvedioid : res.data.uploadId ,url:cururl});
                        break;
                        case 5:
                            this.UpSuccesslaodList5.push({ fileName :res.data.fileName,upLoadvedioid : res.data.uploadId ,url:cururl});
                        break;
                        
                    }
                } 

            },
            async AjaxFile(file,i,batchnumber) {
                var name = file.name; //文件名
                var size = file.size; //总大小shardSize = 2 * 1024 * 1024,
                var shardSize = 15 * 1024 * 1024;
                var shardCount = Math.ceil(size / shardSize); //总片数
                if (i >= shardCount) {
                    return;
                }
                //计算每一片的起始与结束位置
                var start = i * shardSize;
                var end = Math.min(size, start + shardSize);
                //构造一个表单，FormData是HTML5新增的
                i=i+1;
                var form = new FormData();
                form.append("data", file.slice(start, end)); //slice方法用于切出文件的一部分
                form.append("batchnumber", batchnumber);
                form.append("fileName", name);
                form.append("total", shardCount); //总片数
                form.append("index", i); //当前是第几片
                const res = await xMTVideoUploadBlockAsync(form);
                if (res?.success) {
                    if(i == shardCount){
                        this.atfterUplaodData = res.data;
                    }else{
                        await this.AjaxFile(file, i,res.data);
                    }
                }else{
                    this.$message({ message: res?.msg, type: "warning" });

                }
            },
            //移除上传文件
            async removeUpload(uploadrow,index){
                this.addloading = true;
                const res = await delOutcomeVideo({ upLoadvedioid:uploadrow.upLoadvedioid});
                if (res?.success) {
                    switch(index){
                        case 1:
                            for(let num in this.UpSuccesslaodList1){
                                if(this.UpSuccesslaodList1[num].upLoadvedioid==uploadrow.upLoadvedioid){
                                    this.UpSuccesslaodList1.splice(num,1)
                                }
                            }
                            break;
                        case 2:
                             for(let num in this.UpSuccesslaodList2){
                                if(this.UpSuccesslaodList2[num].upLoadvedioid==uploadrow.upLoadvedioid){
                                    this.UpSuccesslaodList2.splice(num,1)
                                }
                            }
                            break;
                        case 3:
                        for(let num in this.UpSuccesslaodList3){
                                if(this.UpSuccesslaodList3[num].upLoadvedioid==uploadrow.upLoadvedioid){
                                    this.UpSuccesslaodList3.splice(num,1)
                                }
                            }
                            break;
                        case 4:
                        for(let num in this.UpSuccesslaodList4){
                                if(this.UpSuccesslaodList4[num].upLoadvedioid==uploadrow.upLoadvedioid){
                                    this.UpSuccesslaodList4.splice(num,1)
                                }
                            }
                            break;
                        case 5:
                        for(let num in this.UpSuccesslaodList5){
                                if(this.UpSuccesslaodList5[num].upLoadvedioid==uploadrow.upLoadvedioid){
                                    this.UpSuccesslaodList5.splice(num,1)
                                }
                            }
                            break;
                    }
                } 
               this.addloading = false;
            }, */

            async downfile(row){
                var filetype= 'video/mp4'
                var xhr = new XMLHttpRequest();
                xhr.open('GET', row.url, true);
                xhr.responseType = 'arraybuffer'; // 返回类型blob
                debugger;
                xhr.onload = function() {
                    if (xhr.readyState === 4 && xhr.status === 200) {
                        let blob = this.response;
                        // 转换一个blob链接
                        // 注: URL.createObjectURL() 静态方法会创建一个 DOMString(DOMString 是一个UTF-16字符串)，
                        // 其中包含一个表示参数中给出的对象的URL。这个URL的生命周期和创建它的窗口中的document绑定
             
                        let downLoadUrl = window.URL.createObjectURL(new Blob([blob], {type: filetype}));
                        // 视频的type是video/mp4，图片是image/jpeg
                        // 01.创建a标签
                        let a = document.createElement('a');
                        // 02.给a标签的属性download设定名称
                        a.download = row.fileName;
                        // 03.设置下载的文件名
                        a.href = downLoadUrl;
                        // 04.对a标签做一个隐藏处理
                        a.style.display = 'none';
                        // 05.向文档中添加a标签
                        document.body.appendChild(a);
                        // 06.启动点击事件
                        a.click();
                        // 07.下载完毕删除此标签
                        a.remove();
                    };
                    };
                xhr.send();
            },
            
          
           
        },
    }
</script>