<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="日期:">
          <el-date-picker style="width: 220px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束" :clearable="false"
            :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
        </el-form-item>
        <el-form-item label="平台">
          <el-select v-model="filter.platform" placeholder="请选择" :clearable="true" :collapse-tags="true" filterable
            @change="onchangeplatformMain" style="width: 100px">
            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="店铺:">
          <el-select v-model="filter.shopCode" style="width: 150px" placeholder="请选择" @change="onSearch" :clearable="true"
            :collapse-tags="true" filterable>
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
          </el-select>
        </el-form-item>
        <el-form-item label="运营组:">
          <el-select v-model="filter.groupId" style="width: 110px" placeholder="请选择" :clearable="true"
            :collapse-tags="true" filterable @change="onSearch">
            <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item>
        <el-form-item label="系列编码:">
          <el-input v-model.trim="filter.goodsCode" placeholder="系列编码" style="width: 130px" @change="onSearch" />
        </el-form-item>
        <el-form-item label="产品ID:">
          <el-input v-model.trim="filter.proCode" placeholder="产品ID" style="width: 130px" @change="onSearch" />
        </el-form-item>

        <el-form-item label="状态:" v-if="checkPermission('prosameprofit')">
          <el-select v-model="filter.state" placeholder="请选择" clearable :collapse-tags="true" filterable
            style="width: 100px">
            <el-option v-for="item in prosimstatelist" :key="item.state" :label="item.state" :value="item.state" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
    <ces-table ref="table" :that="that" :isIndex="true" :hasexpand="true" @sortchange="sortchange"
      :summaryarry="summaryarryMain" :tableData.sync="list" :tableCols="tableCols" :tablefixed="true"
      :tableHandles="tableHandles" :headerCellStyle="headerCellStyle" @summaryClick='onsummaryClick'
      :loading="listLoading">
      <template slot="extentbtn">
        <el-button-group v-if="lastUpdateTime != null">
          <el-button style="margin: 0" @click="getimportlist"> {{ lastUpdateTime }} </el-button>
        </el-button-group>
      </template>
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>
    <el-dialog :visible.sync="detail.visible" :show-close="false" width="90%" v-dialogDrag>
      <el-button type="primary" @click="onSearchDetail">查询</el-button>
      <el-button type="primary" @click="showNext">下一个</el-button>
      <div style="margin-bottom: 10px">
        <el-row>
          <el-col :span="20">
            <el-descriptions :column="3" size="mini" border>
              <el-descriptions-item label="系列编码">{{ detail.selRow.goodsCode }}</el-descriptions-item>
              <el-descriptions-item label="系列名称">{{ detail.selRow.goodsName }}</el-descriptions-item>
              <el-descriptions-item label="运营组">{{ selGroupName }}</el-descriptions-item>
              <el-descriptions-item label="主链接">
                <div v-html="
                  myformatLinkProCode(
                    detail.selRow.platform,
                    detail.selRow.proCode
                  )
                "></div>
              </el-descriptions-item>
              <el-descriptions-item label="近30天销量">{{ detail.selRow.salesQty }}</el-descriptions-item>
              <el-descriptions-item label="编码数">{{ detail.selRow.goodsCodeQty }}</el-descriptions-item>
            </el-descriptions>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-image style="width: 60px; height: 60px" :src="detail.selRow.goodsImage"
              :preview-src-list="detail.srcList">
              <template #error>
                <div class="image-slot">
                  <el-icon>
                    <picture />
                  </el-icon>
                </div>
              </template>
            </el-image>
          </el-col>
        </el-row>
      </div>
      <el-tabs type="card" style="margin-bottom: 0px" @tab-click="onTabClick" v-model="detail.tabName">

        <el-tab-pane label="主链接编码" name="tabMain"  >
          <procodesimilarityshootPackDesgin   :codefilter="detail.filter" :codeinfo="detail.selRow"
          :platformList="platformListpack"  :warehouselist="warehouselist"
                :dockingPeopleList="dockingPeopleList"  :fpPhotoLqNameList="fpPhotoLqNameList"
                :taskUrgencyList="taskUrgencyList" :packclasslist="packclasslist"
                :brandList="brandList"
                :groupList="groupListpack"
          ref="mainProGoods" style="height: 480px">
          </procodesimilarityshootPackDesgin>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
    <div v-show="isshowstate">
      <procodesimilaritystate ref="procodesimilaritystate" @changelist="changelist" @onSearch="onSearch">
      </procodesimilaritystate>
    </div>
    <!-- 订单量图表 -->
    <el-dialog :visible.sync="dialoganalysisVisible" width="80%" v-dialogDrag :show-close="false">
      <proCodeSimilarityAnalysis ref="proCodeSimilarityAnalysis" style="height: 550px"></proCodeSimilarityAnalysis>
    </el-dialog>
    <!-- 系列编码趋势图 -->
    <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%">
      <span>
        <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
      </span>
      <span v-if="buessness.visible">
        <buschar v-if="buscharDialog.visible" :analysisData="buessness.data"></buschar>
        <!-- <ProCodeBusinessStaffPlatForm v-if="buessness.visible" ref="ProCodeBusinessStaffPlatForm"></ProCodeBusinessStaffPlatForm> -->
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>
    <!-- 周转天数详情 -->
    <el-dialog :visible.sync="dialodayssisVisible" width="50%" v-dialogDrag :show-close="false">
      <Daysturnoverdetail ref="Daysturnoverdetail" :filter="detail.filter" style="height: 550px"></Daysturnoverdetail>
    </el-dialog>
    <el-drawer title="耗材费用" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
      :visible.sync="editparmVisible" direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
      <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options" />
      <div class="drawer-footer">
        <el-button @click.native="editparmVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="editparmLoading" @click="onSetEditParm" />
      </div>
    </el-drawer>
    <!-- 周转天数详情 -->
    <el-dialog :visible.sync="dialogDayZZRatedetailVisible" width="60%" v-dialogDrag :show-close="false">
      <DayZZRateDetail ref="DayZZRatedetail" :filter="dayZZRatedetail.filter" style="height: 550px"></DayZZRateDetail>
    </el-dialog>
    <!-- 时间线弹框 -->
    <el-dialog title="" :visible.sync="importtimedialogVisible" v-dialogDrag width="30%">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span></span>
        </div>
        <div style="height:400px;overflow-y:auto" class="text item">
          <el-alert v-for="item in importtimelist" :key="item" title="" type="success" :closable="false"> 计算时间 : {{ item
          }} </el-alert>
        </div>
      </el-card>
    </el-dialog>
  </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import { getList as getshopList } from "@/api/operatemanage/base/shop";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
import { formatPlatform, formatLinkProCode, formatmoney } from "@/utils/tools";
import { rulePlatform } from "@/utils/formruletools";
import { getGroupKeyValue } from "@/api/operatemanage/base/product";
import { getListByStyleCode } from "@/api/inventory/basicgoods"
import { batchAddProCodeSimilarity, getListByStyleCodeCost } from '@/api/operatemanage/base/product'
import { queryProCodeSimilarityAnalysis } from "@/api/order/procodesimilarity"
import buschar from '@/components/Bus/buschar'
import ProCodeBusinessStaffPlatForm from '@/views/order/ProCodeBusinessStaffPlatForm'
import { getProCodeBusinessStaffPlatForm } from '@/api/bookkeeper/reportday'
import {
  getLastUpdateTime,
  pageProCodeSimilarity,
  exportProCodeSimilarity,
  exportProCodeSimilarity1,
  getProCodeSimilaritySummary,
  getProCodeSimilarityStateName,
  addTbProCodeSimilarityGrowSet,
  getProCodeSimilarityImportLogTime
} from "@/api/order/procodesimilarity"
import SeriesGoods from './procodesimilarity/SeriesGoods.vue';
import MainProGoods from './procodesimilarity/MainProGoods.vue';
import SameProDetail from './procodesimilarity/SameProDetail.vue';
import proCodeSimilarityAnalysis from './procodesimilarity/ProCodeSimilarityAnalysis.vue';
import Daysturnoverdetail from './procodesimilarity/daysTurnoverDetail.vue'
import DayZZRateDetail from './procodesimilarity/dayZZRateDetail.vue'
import procodesimilaritystate from './procodesimilarity/procodesimilaritystate.vue'
import procodesimilarityshootPackDesgin from './procodesimilarityshootPackDesgin.vue';


//格式化money列：大于1 时，去掉小数点，小于1时保留小数点
var myformatmoney = function (value) {
  var money = formatmoney(
    Math.abs(value) > 1 ? Math.round(value, 2) : Math.round(value, 1)
  );
  return money
};
//日报列
const dayReportCols = [
  { istrue: true, summaryEvent: true, prop: 'orderCountDayReport', label: '订单量', sortable: 'custom', width: '70', permission: "prosameprofit", formatter: (row) => !row.orderCountDayReport ? " " : row.orderCountDayReport, handle: (that, row) => that.Pananysis(row) },
  { istrue: true, summaryEvent: true, prop: 'saleAmont', label: '销售金额', sortable: 'custom', permission: "prosameprofit", width: '100', formatter: (row) => !row.saleAmont ? " " : row.saleAmont.toFixed(2) },
  { istrue: true, summaryEvent: true, prop: 'saleCost', label: '销售成本', sortable: 'custom', permission: "prosameprofit", width: '80', formatter: (row) => !row.saleCost ? " " : row.saleCost.toFixed(2) },
  { istrue: true, summaryEvent: true, prop: 'profit1', label: '订单毛利', sortable: 'custom', permission: "prosameprofit", width: '80', type: 'custom', tipmesg: '销售金额-销售成本', formatter: (row) => !row.profit1 ? " " : row.profit1.toFixed(2) },
  { istrue: true, summaryEvent: true, prop: 'profit1Rate', label: '毛利率', sortable: 'custom', permission: "prosameprofit", width: '80', type: 'custom', tipmesg: '订单毛利/销售金额', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate * 100).toFixed(2) + '%' },
  { istrue: true, summaryEvent: true, prop: 'dkAmont', label: '平台扣点', sortable: 'custom', width: '80', type: 'custom', permission: "prosameprofit", tipmesg: '支付宝账单费用扣点，新上链接天猫-7.5%；C店2%；已有ID-上月月报百分比', formatter: (row) => !row.dkAmont ? " " : row.dkAmont.toFixed(2) },
  { istrue: true, summaryEvent: true, prop: 'refundAmont', label: '总退款金额', sortable: 'custom', width: '70', permission: "prosameprofit", tipmesg: '当日发生的总退款金额，包括历史订单', formatter: (row) => !row.refundAmont ? " " : row.refundAmont.toFixed(2) },
  { istrue: true, summaryEvent: true, prop: 'expressDeductAmount', label: '违规总扣款', sortable: 'custom', permission: "prosameprofit", width: '80', type: 'custom', tipmesg: '违规总扣款', formatter: (row) => !row.expressDeductAmount ? " " : row.expressDeductAmount.toFixed(2) },
  { istrue: true, summaryEvent: true, prop: 'allMarketingCost', label: '总广告费', sortable: 'custom', permission: "prosameprofit", width: '80', formatter: (row) => !row.allMarketingCost ? " " : row.allMarketingCost?.toFixed(2) },
  { istrue: true, summaryEvent: true, prop: 'packageFee', label: '包装材料', sortable: 'custom', permission: "prosameprofit", width: '80', formatter: (row) => !row.packageFee ? " " : row.packageFee?.toFixed(2) },
  { istrue: true, summaryEvent: true, prop: 'freightFeeTotal', label: '快递费', sortable: 'custom', permission: "prosameprofit", width: '80', formatter: (row) => !row.freightFeeTotal ? " " : row.freightFeeTotal?.toFixed(2) },
  { istrue: true, summaryEvent: true, prop: 'freightAvgWeight', label: '快递均重', sortable: 'custom', permission: "prosameprofit", width: '80', formatter: (row) => !row.freightAvgWeight ? " " : row.freightAvgWeight?.toFixed(2) },

  {
    istrue: true, summaryEvent: true, prop: 'profit2', label: '毛二利润', sortable: 'custom', permission: "prosameprofit", width: '80', type: 'custom', tipmesg: '销售金额-销售成本-平台扣点-延迟发货扣款-营销费用-淘宝客-首单礼金-特殊单(刷单、补单、大灰熊)-包装材料-快递费',
    formatter: (row) => !row.profit2 ? " " : row.profit2?.toFixed(2)
  },
  { istrue: true, summaryEvent: true, prop: 'profit2Rate', label: '毛二利润率', sortable: 'custom', permission: "prosameprofit", width: '80', type: 'custom', tipmesg: '毛二利润/销售金额', formatter: (row) => !row.profit2Rate ? " " : (row.profit2Rate * 100).toFixed(2) + '%' },
  {
    istrue: true, summaryEvent: true, prop: 'profit3PredictRate', label: '毛三预估比例', type: 'custom', permission: "prosameprofit", tipmesg: '(空白链接ID成本+异常成本+补发成本+代发成本+采购运费+产品费用+工资+损耗)/销售金额',
    sortable: 'custom', width: '80', formatter: (row) => !row.profit3PredictRate ? " " : (row.profit3PredictRate * 100).toFixed(2) + '%'
  },
  { istrue: true, summaryEvent: true, prop: 'profit3PredictFee', label: '预估费用', type: 'custom', tipmesg: '毛三预估比例*销售金额', permission: "prosameprofit", sortable: 'custom', width: '80', formatter: (row) => !row.profit3PredictFee ? " " : row.profit3PredictFee?.toFixed(2) },
  { istrue: true, summaryEvent: true, prop: 'profit3', label: '毛三利润', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛二利润-预估费用', permission: "prosameprofit", formatter: (row) => !row.profit3 ? " " : row.profit3?.toFixed(2) },
  { istrue: true, summaryEvent: true, prop: 'earningsRate', label: '盈亏平衡率', width: '80', sortable: 'custom', type: 'custom', tipmesg: '销售成本/毛三利润', permission: "prosameprofit", formatter: (row) => !row.earningsRate ? " " : (row.earningsRate).toFixed(2) },
  { istrue: true, summaryEvent: true, prop: 'growRate', label: '系列增长率', type: 'custom', tipmesg: '系列增长率由人工手动设置，毛三利润率小于设定的增长率则会同步到系列编码增长率模块', sortable: 'custom', width: '80', permission: "prosameprofit", formatter: (row) => !row.growRate ? " " : (row.growRate).toFixed(2) + '%' },
  { istrue: true, summaryEvent: true, prop: 'profit3Rate', label: '毛三利润率', type: 'custom', tipmesg: '毛三利润/销售金额', sortable: 'custom', width: '80', permission: "prosameprofit", formatter: (row) => !row.profit3Rate ? " " : (row.profit3Rate * 100).toFixed(2) + '%' },
  { istrue: true, summaryEvent: true, prop: 'shareRate', label: '公摊费率', type: 'custom', tipmesg: '明细2/销售金额', sortable: 'custom', width: '80', permission: "prosameprofit", formatter: (row) => !row.shareRate ? " " : (row.shareRate * 100).toFixed(2) + '%' },
  { istrue: true, summaryEvent: true, prop: 'shareFee', label: '公摊费', type: 'custom', tipmesg: '公摊费(%)*销售金额', sortable: 'custom', width: '80', permission: "prosameprofit", formatter: (row) => !row.shareFee ? " " : row.shareFee.toFixed(2) },
  { istrue: true, summaryEvent: true, prop: 'profit4', label: '净利润', type: 'custom', tipmesg: '毛三利润-公摊费', sortable: 'custom', width: '80', permission: "prosameprofit", formatter: (row) => !row.profit4 ? " " : row.profit4.toFixed(2) },
  { istrue: true, summaryEvent: true, prop: 'profit4Rate', label: '净利率', type: 'custom', tipmesg: '净利润/销售金额', sortable: 'custom', width: '80', permission: "prosameprofit", formatter: (row) => !row.profit4Rate ? " " : (row.profit4Rate * 100).toFixed(2) + '%' },


];

//商品客户咨询列
const customerCols = [
  { istrue: true, summaryEvent: true, prop: 'inquiries', label: '咨询量', type: 'custom', tipmesg: '系列编码所有链接在查询日期范围内的顾客咨询量之和', sortable: 'custom', permission: "", width: '80', formatter: (row) => !row.inquiries ? " " : row.inquiries },
  { istrue: true, summaryEvent: true, prop: 'inquiriesSuccessRate', label: '咨询量转化率', type: 'custom', tipmesg: '成功的咨询量/总咨询量', sortable: 'custom', permission: "", width: '80', formatter: (row) => !row.inquiriesSuccessRate ? " " : (row.inquiriesSuccessRate * 100).toFixed(2) + '%' },
  { istrue: true, summaryEvent: true, prop: 'refundAmontBeforeRate', label: '发货前退款率', sortable: 'custom', permission: "prosameprofit", width: '80', formatter: (row) => !row.refundAmontBeforeRate ? "0.00%" : (row.refundAmontBeforeRate * 100).toFixed(2) + '%' },
  { istrue: true, summaryEvent: true, prop: 'refundAmontAfterRate', label: '发货后退款率', sortable: 'custom', permission: "prosameprofit", width: '80', formatter: (row) => !row.refundAmontAfterRate ? "0.00%" : (row.refundAmontAfterRate * 100).toFixed(2) + '%' },
];

const tableCols = [
  { istrue: true, fixed: true, prop: 'goodsCode', label: '系列编码', width: '200', sortable: 'custom', type: 'click', handle: (that, row) => that.showDetail(row, "tabMain") },
  { istrue: true, fixed: true, prop: 'goodsImage', label: '图片', width: '60', type: 'imageGoodsCode', goods: { code: 'goodsCode', name: 'goodsName' } },
  { istrue: true, fixed: true, prop: 'platform', label: '平台', width: '200', type: 'custom', tipmesg: '淘宝、拼多多盈利率', sortable: 'custom', formatter: (row) => "淘宝:" + (row.taoEarningsRate * 100).toFixed(2) + "%，" + "拼多多:" + (row.pinEarningsRate * 100).toFixed(2) + "%" },
  // { istrue: true, fixed: true, prop: 'proCode1', fix: true, label: '趋势图', permission: "prosameprofit", style: "color:red;cursor:pointer;", width: '70', formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showprchart(row.goodsCode) },

  { istrue: true, fixed: true, prop: 'groupId', label: '运营组', type: 'custom', tipmesg: '主链接ID的所属运营组', width: '100', sortable: 'custom', formatter: (row) => row.groupName },
  { istrue: true, fixed: true, prop: 'brandName', label: '采购', type: 'custom', tipmesg: '对应产品下的采购人员', width: '100', formatter: (row) => row.brandName },
  {istrue:true,prop:'orderCount',label:'近30天订单数', permission:"prosameprofit",width:'120',sortable:'custom',formatter:(row)=>myformatmoney(row.orderCount)},
  { istrue: true, prop: 'oneDayZZRate', label: '1天周转天数',  width: '100',type: 'custom', permission: "prosameprofit", tipmesg: '', formatter: (row) => row.oneDayZZRate.toFixed(2), handle: (that, row) => that.showDayZZRateDetail(row, 1) },
  { istrue: true, prop: 'threeDayZZRate', label: '3天周转天数',  width: '100',type: 'custom', permission: "prosameprofit", tipmesg: '', formatter: (row) => row.threeDayZZRate.toFixed(2), handle: (that, row) => that.showDayZZRateDetail(row, 3) },
  { istrue: true, prop: 'salesQty', label: '近30天销量',width: '100', permission: "prosameprofit", idth: '90', sortable: 'custom', formatter: (row) => myformatmoney(row.salesQty) },
  { istrue: true, summaryEvent: true, prop: 'orderCountDayReport', label: '订单量', sortable: 'custom', width: '100', permission: "prosameprofit", formatter: (row) => !row.orderCountDayReport ? " " : row.orderCountDayReport, handle: (that, row) => that.Pananysis(row) },

];
const tableHandles1 = [];
const startDate = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");
const cityOptions = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];
export default {
  name: 'Roles',
  components: { cesTable, MyContainer, MyConfirmButton,
    SeriesGoods, MainProGoods, SameProDetail, proCodeSimilarityAnalysis, buschar,
     Daysturnoverdetail, procodesimilaritystate, ProCodeBusinessStaffPlatForm, DayZZRateDetail,
     procodesimilarityshootPackDesgin
    },
    props:{
        taskUrgencyList:{ type: Array, default:[] }, //平台
        platformListpack:{ type: Array, default:[] }, //平台
        warehouselist:{ type: Array, default:[] }, //仓库
        packclasslist:{ type: Array, default:[] }, //仓库
        dockingPeopleList:{ type: Array, default:[] }, //对接人
        fpPhotoLqNameList:{ type: Array, default:[] }, //分配查询
        groupListpack:{ type: Array, default:[] }, //运营组
        tabkey:{ type: String, default:"packdesgintask" }, //平台
        brandList:{ type: Array, default:[] }, //
    },
  data() {
    return {
      buessness: { visible: false, title: "", data: [] },
      that: this,
      formtitle: "耗材费用",
      filter: {
        platform: null,
        shopCode: "",
        proCode: null,
        goodsCode: null,
        groupId: null,
        similarity: 0,
        days: null,
        profit2UnZero: null,
        profit3UnZero: null,
        state: null,
        startDate: null,
        endDate: null,
        timerange: [startDate, endDate],
      },
      list: [],
      summaryarry: {},
      pager: { OrderBy: "orderCount", IsAsc: false },
      tableCols: tableCols,
      tableHandles: tableHandles1,
      cities: cityOptions,
      checkboxGroup1: [],
      platformList: [],
      prosimstatelist: [],
      shopList: [],
      groupList: [],
      importtimelist: [],
      total: 0,
      sels: [],
      deletegroupdialogVisible: false,
      importtimedialogVisible: false,
      listLoading: false,
      pageLoading: false,
      dialogDrVisible: false,
      dialoganalysisVisible: false,
      dialodayssisVisible: false,
      addFormVisible: false,
      addLoading: false,
      editparmVisible: false,
      editparmLoading: false,
      isshowstate: false,
      isshowtime: false,
      isshowmonth: false,
      lastUpdateTime: null,
      drparamProCode: '',
      handletype: 0,
      detail: {
        visible: false,
        filter: {
          parentId: null,
          proCode: null,
          status: null,
          platform: null,
          shopId: "",
          similarity: null,

          seriesCode: null,
          goodsCode: null,
          startDate: null,
          endDate: null,
          timerange: [startDate, endDate]
        },
        selRow: {},
        srcList: [],
        tabName: "tabMain",
        customRowStyle: function (data) {
          if (data.row.isMain) {
            return { color: 'red' };
          }
        },
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },

      autoform: {
        fApi: {},
        rule: [],
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } }
      },
      buscharDialog: { visible: false, title: "", data: [] },
      dialogDayZZRatedetailVisible: false,
      dayZZRatedetail: {
        filter: {
          type: 1,
          seriesCode: null
        }
      }

    };
  },
  async mounted() {
    if (this.$route.query && this.$route.query.styleCode) {
      this.filter.goodsCode = this.$route.query.styleCode
    }
    await this.setPlatform();
    await this.setGroupSelect();
    await this.getLastUpdateTime();
    await this.getlist();
    await this.getprosimstatelist()
  },
  methods: {
    //获取状态信息
    async getprosimstatelist() {
      var res = await getProCodeSimilarityStateName();
      if (res?.code) {
        this.prosimstatelist = res.data.map(function (item) {
          var ob = new Object();
          ob.state = item;
          return ob;
        })
      }
    },
    async remoteSearchStyleCode(parm) {
      if (!parm) {
        //this.$message({ message: this.$t('api.sync'),type: 'warn'})
        return;
      }
      var options = [];
      const res = await getListByStyleCodeCost({ currentPage: 1, pageSize: 50, styleCode: parm })
      res?.data?.forEach(f => {
        options.push({ value: f.cost + ',' + f.goodsCode, label: f.goodsName + ' — ' + f.cost })
      })
      this.autoform.fApi.getRule('packCost1').options = options;
    },
    async setcustomergroup(e) {
      this.isshowstate = true
      await this.$refs.procodesimilaritystate.setcustomergroup(e, this.list)
    },
    async changelist(e) {
      this.list = e
    },
    //设置系列状态
    async setprostate(e) {
      this.isshowstate = true
      await this.$refs.procodesimilaritystate.OnSearch(e.goodsCode, this.list)
    },
    async setpromonth(e) {
      this.isshowmonth = true
    },
    //设置平台下拉
    async setPlatform() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;
    },
    //设置店铺下拉
    async onchangeplatform(val) {
      const res = await getshopList({
        platform: val,
        CurrentPage: 1,
        PageSize: 10000,
      });
      this.shopList = res.data.list || [];
      this.filter.shopCode = "";
    },
    //设置店铺下拉
    async onchangeplatformMain(val) {
      await this.onchangeplatform(val);
      await this.onSearch();
    },
    //设置店铺下拉
    async onchangeplatformDetail(val) {
      await this.onchangeplatform(val);
      await this.onSearchDetail();
    },
    //设置运营组下拉
    async setGroupSelect() {
      const res = await getGroupKeyValue({});
      this.groupList = res.data;
    },

    //导出
    async onExport() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      var res = await exportProCodeSimilarity(params);
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute(
        "download",
        "链接相似度_" + new Date().toLocaleString() + ".xlsx"
      );
      aLink.click();
    },
    //导出
    async newonExport() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      var res = await exportProCodeSimilarity1(params);
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute(
        "download",
        "系列编码报表" + new Date().toLocaleString() + ".xlsx"
      );
      aLink.click();
    },
    async remoteSearchUser(parm) {
      if (!parm) {
        //this.$message({ message: this.$t('api.sync'),type: 'warn'})
        return;
      }
      var options = [];
      const res = await getListByStyleCode({ currentPage: 1, pageSize: 50, styleCode: parm })
      res?.data?.forEach(f => {
        options.push({ value: f.styleCode, label: f.styleCode })
      })
      this.autoform.fApi.getRule('styleCode').options = options;
    },
    //修改
    async onHand(val) {
      if (val == 1) {
        this.handletype = val
        this.formtitle = "耗材费用设置"
        this.editparmVisible = true
        this.autoform.rule = [{ type: 'hidden', field: 'id', title: 'id', value: '', col: { span: 12 } },
        {
          type: 'select', field: 'styleCode', title: '系列编码', validate: [{ type: 'string', required: true, message: '请选择系列编码' }], value: "", options: [],
          props: { filterable: true, allowCreate: false, clearable: true, remote: true, remoteMethod: (parm) => this.remoteSearchUser(parm) }
        },
        {
          type: 'select', field: 'packCost1', title: '包装费', validate: [{ type: 'string', required: true, message: '请选择包装费' }], value: "", options: [],
          props: { filterable: true, allowCreate: false, clearable: true, remote: true, remoteMethod: (parm) => this.remoteSearchStyleCode(parm) }
        },
        ]
        var arr = Object.keys(this.autoform.fApi);
        if (arr.length > 0)
          this.autoform.fApi.reload()

        var model = { id: '', }
        this.$nextTick(async () => {
          var arr = Object.keys(this.autoform.fApi)
          if (arr.length > 0)
            await this.autoform.fApi.resetFields()
          await this.autoform.fApi.setValue(model)
        })
      } else if (val == 2) {
        this.handletype = val
        this.formtitle = "增长率设置"
        this.editparmVisible = true
        var that = this
        this.autoform.rule = [{ type: 'hidden', field: 'id', title: 'id', value: '', col: { span: 1 } },
        {
          type: 'select', field: 'styleCode', title: '系列编码', value: "", options: [],
          props: { multiple: true, filterable: true, allowCreate: false, clearable: true, remote: true, remoteMethod: (parm) => this.remoteSearchUser(parm) }
        },
        { type: 'radio', field: 'isAll', title: '是否设置全部', value: true, update(val) { { that.changeIsStyleCode(val) } }, options: [{ value: true, label: "批量", disabled: false }, { value: false, label: "全部" },], col: { span: 6 }, },
        { type: 'InputNumber', field: 'growRate', title: '增长率', props: { precision: 0 }, validate: [{ required: true, message: '请输入' }] },
        ]

        var arr = Object.keys(this.autoform.fApi);
        if (arr.length > 0)
          this.autoform.fApi.reload()

        var model = { id: '', }
        this.$nextTick(async () => {
          var arr = Object.keys(this.autoform.fApi)
          if (arr.length > 0)
            await this.autoform.fApi.resetFields()
          await this.autoform.fApi.setValue(model)
        })
      }


    },
    async changeIsStyleCode(val) {
      this.$nextTick(async () => {
        console.log('来了', val)
        var arr = Object.keys(this.autoform.fApi)
        if (arr.length > 0) {
          if (val == false)
            await this.autoform.fApi.hidden(true, 'styleCode')
          else if (val == true) {
            await this.autoform.fApi.hidden(false, 'styleCode')
          }
        }

      })
    },
    async onSetEditParm() {
      this.editparmLoading = true;
      if (this.handletype == 1) {
        await this.autoform.fApi.validate(async (valid, fail) => {
          if (valid) {
            const formData = this.autoform.fApi.formData()
            const res = await batchAddProCodeSimilarity(formData)
            if (res.code == 1) {
              this.editparmLoading = false;
              this.editparmVisible = false;
            }
          }
          else {
            this.editparmLoading = false;
          }
        })
      } else if (this.handletype == 2) {
        this.$nextTick(async () => {
          await this.autoform.fApi.validate(async (valid, fail) => {
            if (valid) {
              const formData = this.autoform.fApi.formData()
              if (formData.isAll == true) {
                formData.styleCode = formData.styleCode.join()
              }

              const res = await addTbProCodeSimilarityGrowSet(formData)
              this.editparmLoading = false;
              this.editparmVisible = false;
            }
            else {
              this.editparmLoading = false;
            }
          })
        })
      }
    },
    //获取查询条件
    getCondition() {
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      this.filter.startDate = null;
      this.filter.endDate = null;
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      const params = {
        ...pager,
        ...page,
        ...this.filter,
      };

      return params;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1);
      await this.getlist();
    },
    //分页查询
    async getlist() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await pageProCodeSimilarity(params);
      this.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.total = res.data.total;
      const data = res.data.list;
      //this.summaryarry = res.data.summary;
      data.forEach((d) => {
        d._loading = false;
      });
      this.list = data;
      await this.getSummary();
    },
    async getSummary() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }

      const res = await getProCodeSimilaritySummary(params);
      if (!res?.success) {
        return;
      }
      this.summaryarry = res.data;
    },
    //排序查询
    async sortchange(column) {
      if (!column.order) this.pager = {};
      else {
        this.pager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      }
      await this.onSearch();
    },
    selsChange: function (sels) {
      this.sels = sels;
    },
    async getLastUpdateTime() {
      const res = await getLastUpdateTime({});
      if (!res?.success) {
        return;
      }
      if (res.data) this.lastUpdateTime = "最晚更新时间：" + res.data;
    },
    ///==明细 Start==========================================
    async showDetail(row, tabName) {
      this.detail.visible = true;
      this.detail.selRow = row;
      this.detail.srcList = [row.goodsImage];
      this.detail.tabName = tabName;
      this.clearDetailFilter();
      this.detail.filter.parentId = row.id;
      this.detail.filter.seriesCode = row.goodsCode;
      this.detail.filter.similarity = this.filter.similarity;
      this.detail.filter.shopCode = this.filter.shopCode;
      this.detail.filter.groupId = this.filter.groupId;
      this.detail.filter.proCode = this.filter.proCode;
      this.detail.filter.platform = this.filter.platform;

      this.detail.pager = { OrderBy: "id", IsAsc: false };
      await this.onTabClick({ label: "相似产品" });
    },
    async showTurnover(row) {
      this.dialodayssisVisible = true
      this.clearDetailFilter();
      this.detail.filter.goodsCode = row.goodsCode
      this.$nextTick(async () => {
        await this.$refs.Daysturnoverdetail.onSearch()
      })

    },
    clearDetailFilter() {
      this.detail.filter = {
        parentId: null,
        proCode: null,
        status: null,
        platform: null,
        shopId: "",
        similarity: null,
        seriesCode: null,
        goodsCode: null,
        timerange: this.filter.timerange,
        startDate: null,
        endDate: null,
      };
    },
    //分页查询
    async onSearchDetail() {

        await this.$refs.mainProGoods.onSearch();
    },
    //订单量图表
    async Pananysis(row) {
      this.dialoganalysisVisible = true;
      let para = { proCode: row.proCode, timerange: this.filter.timerange }

      this.$nextTick(() => {
        this.$refs.proCodeSimilarityAnalysis.onSearch(para);
      });
    },
    //趋势图
    async showprchart(goodsCode) {
      //  window['lastseeprcodedrchart']=goodsCode
      //  this.buessness.data= window['lastseeprcodedrchart1']


      this.filter.startDate = null;
      this.filter.endDate = null;
      var startDate = null, endDate = null;
      if (this.filter.timerange) {
        startDate = this.filter.timerange[0];
        endDate = this.filter.timerange[1]
      }

      var params = { goodsCode: goodsCode, similarity: this.filter.similarity, goodsCode: goodsCode, isGroupBy: true }
      let that = this;
      const res = await queryProCodeSimilarityAnalysis(params).then(res => {
        that.buscharDialog.data = res.data
        that.buscharDialog.title = res.data.legend[0]
      })
      this.$nextTick(async () => {
        await getProCodeBusinessStaffPlatForm({ goodsCode: goodsCode }).then(res => {
          that.buscharDialog.visible = true;
          that.buessness.visible = true
          that.buessness.data = res.data
        })
      })

      //await this.$refs.ProCodeBusinessStaffPlatForm.getanalysisdata(goodsCode)
    },
    //汇总趋势图
    async onsummaryClick(property) {
      // this.$message({message:property,type:"warning"});
      this.filter.startDate = null;
      this.filter.endDate = null;
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = { ...this.filter };
      params.column = property;
      let that = this;
      const res = await queryProCodeSimilarityAnalysis(params).then(res => {
        that.buessness.visible = false;
        that.buscharDialog.visible = true;
        that.buscharDialog.data = res.data
        that.buscharDialog.title = res.data.legend[0]
      });
    },
    async getimportlist() {
      var res = await getProCodeSimilarityImportLogTime();
      if (!res?.success) return
      this.importtimelist = res.data
      this.importtimedialogVisible = true;
    },
    showNext() {
      if (this.list && this.list.length > 0) {
        var nextRow = this.list[0];
        var findCur = false;
        this.list.forEach(item => {
          if (findCur) {
            findCur = false;
            nextRow = item;
          }
          if (item.id == this.detail.selRow.id) {
            findCur = true;
          }
        });
        this.detail.selRow = nextRow;
        this.showDetail(nextRow, this.detail.tabName);
      }
    },
    onTabClick(tab, event) {
      setTimeout(async () => {
        await this.onSearchDetail();
      }, 500);
    },
    ///==明细 End  ==========================================
    myformatLinkProCode(platform, proCode) {
      return formatLinkProCode(platform, proCode);
    },
    myformatPlatform(platform) {
      return formatPlatform(platform);
    },
    //表头样式
    headerCellStyle(data) {
      if (data && data.column) {
        var isDayReportCol = dayReportCols.find(
          (a) => a.prop == data.column.property
        );
        if (isDayReportCol) {
          return { color: "#F56C6C" };
        }
      }
      return null;
    },
    showDayZZRateDetail(row, type) {
      this.dayZZRatedetail.filter.type = type;
      this.dayZZRatedetail.filter.seriesCode = row.goodsCode;
      this.dialogDayZZRatedetailVisible = true;
      this.$nextTick(async () => {
        await this.$refs.DayZZRatedetail.onSearch()
      })
    }
  },
  computed: {
    selGroupName() {
      var name = this.groupList?.find(
        (a) => a.key == this.detail.selRow.groupId
      )?.value;
      return name || "未知";
    },
    selPlatformName() {
      var name = this.platformList?.find(
        (a) => a.value == this.detail.selRow.platform
      )?.label;
      return name;
    },
    summaryarryMain() {
      return this.summaryarry;
    },
  },
};
</script>
<style scoped>
::v-deep .el-link.el-link--primary {
  margin-right: 7px;
}

::v-deep .el-table__fixed {
  pointer-events: auto;
}
</style>
