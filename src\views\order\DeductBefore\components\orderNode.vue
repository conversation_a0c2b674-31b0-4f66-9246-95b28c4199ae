<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTakeTime" :endDate.sync="ListInfo.endTakeTime"
                    :clearable="false" class="publicCss" startPlaceholder="接单时间" endPlaceholder="接单时间" />
                <dateRange :startDate.sync="ListInfo.startSendTime" :endDate.sync="ListInfo.endSendTime"
                    class="publicCss" startPlaceholder="发货时间" endPlaceholder="发货时间" />
                <dateRange :startDate.sync="ListInfo.startPayTime" :endDate.sync="ListInfo.endPayTime" class="publicCss"
                    startPlaceholder="付款时间" endPlaceholder="付款时间" />
                <dateRange :startDate.sync="ListInfo.startCollectTime" :endDate.sync="ListInfo.endCollectTime"
                    class="publicCss" startPlaceholder="揽收时间" endPlaceholder="揽收时间" />
                <chooseWareHouse v-model="ListInfo.wmsIds" class="publicCss" multiple :filter="sendWmsesFilter" />
                <inputYunhan :inputt.sync="ListInfo.orderNoInners" v-model="ListInfo.orderNoInners"
                    placeholder="内部订单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="orderNoInnersCallback($event, 1)" title="内部订单号"
                    style="width: 200px;margin:0 10px 0 0;" />
                <inputYunhan :inputt.sync="ListInfo.orderNoOnLines" v-model="ListInfo.orderNoOnLines"
                    placeholder="线上订单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="orderNoInnersCallback($event, 2)" title="线上订单号"
                    style="width: 200px;margin:0 10px 0 0;" />
                <inputYunhan :inputt.sync="ListInfo.proCodes" v-model="ListInfo.proCodes" placeholder="宝贝ID/若输入多条请按回车"
                    :clearable="true" :clearabletext="true" :maxRows="500" :maxlength="1000000"
                    @callback="orderNoInnersCallback($event, 3)" title="宝贝ID" style="width: 200px;margin:0 10px 0 0;" />
                <inputYunhan :inputt.sync="ListInfo.waveIds" v-model="ListInfo.waveIds" placeholder="批次号/若输入多条请按回车"
                    :clearable="true" :clearabletext="true" :maxRows="500" :maxlength="1000000"
                    @callback="orderNoInnersCallback($event, 4)" title="批次号" style="width: 200px;margin:0 10px 0 0;" />
                <el-input v-model.trim="ListInfo.expressNo" placeholder="快递单号" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.expressCompanyName" placeholder="快递公司" maxlength="50" clearable
                    class="publicCss" />
                <el-select v-model="ListInfo.platforms" placeholder="平台" multiple filterable class="publicCss"
                    clearable>
                    <el-option v-for="item in platformlist" :label="item.label" :value="item.value" :key="item.value" />
                </el-select>
                <el-select v-model="ListInfo.steps" placeholder="状态" multiple filterable class="publicCss" clearable>
                    <el-option v-for="item in stepList" :label="item.label" :value="item.value" :key="item.value" />
                </el-select>
                <el-select v-model="ListInfo.pickType" placeholder="拣货类型" class="publicCss" clearable>
                    <el-option label="组团" value="组团" />
                    <el-option label="杂单" value="杂单" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%"
            :showsummary="data.summary ? true : false" :summaryarry="data.summary" @sortchange="sortchange" />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/Orders/'
import { mergeTableCols } from '@/utils/getCols'
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
const stepList = [
    { label: '已接单', value: 0 },
    { label: '已审批', value: 1 },
    { label: '已生成批次', value: 2 },
    { label: '已领取拣货', value: 3 },
    { label: '拣货完成', value: 4 },
    { label: '已打包', value: 5 },
    { label: '已发货', value: 6 },
    { label: '已揽收', value: 7 },
    { label: '被拆分', value: 96 },
    { label: '被组合', value: 97 },
    { label: '第三方发货', value: 98 },
    { label: '取消', value: 99 },
    { label: '签收', value: 100 }
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, chooseWareHouse
    },
    data() {
        return {
            api,
            stepList,
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false,
                summarys: [],
                startTakeTime: dayjs().format('YYYY-MM-DD'),
                endTakeTime: dayjs().format('YYYY-MM-DD'),
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        await this.getCol();
        await this.getList()
    },
    methods: {
        sendWmsesFilter(wmses) {
            this.wmsesList = wmses;
            return wmses.filter((a) => a.isSendWarehouse == '是');
        },
        orderNoInnersCallback(val, type) {
            if (type == 1) {
                this.ListInfo.orderNoInners = val
            } else if (type == 2) {
                this.ListInfo.orderNoOnLines = val
            } else if (type == 3) {
                this.ListInfo.proCodes = val
            } else if (type == 4) {
                this.ListInfo.waveIds = val
            }
        },
        proCodeCallback(val) {
            this.ListInfo.proCode = val
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                data.forEach(item => {
                    if (item.prop == 'orderNoInner') {
                        item.type = 'orderLogInfo'
                        item.orderType = 'orderNoInner'
                    }
                    if (item.prop == 'proCode') {
                        item.type = 'html'
                        item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
                    }
                })
                this.tableCols = mergeTableCols(data)
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    data.list.forEach(item => {
                        item.waveId = item.waveId ? String(item.waveId) : item.waveId
                    })
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 5px;
    flex-wrap: wrap;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0;
    }
}

::v-deep .el-select__tags-text {
    max-width: 40px;
}
</style>
