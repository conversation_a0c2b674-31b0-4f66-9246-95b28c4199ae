<template>
    <div class="body">
        <my-container v-loading="pageLoading" :style="minheight?{minHeight:'750px'}:{height:'750px'}">
        <div style="display: flex; flex-direction: row;align-items: center; justify-content: center;" v-if="main&&!isfath">
        <span style="font-weight: 600;">任务标题：</span>
        <el-input maxlength="100" :disabled="!ispaste"  @change="ischange(true)" v-model="main.taskName" style="width: auto; display: flex;" clearable></el-input>
        </div>
        <!-- :class="[skushow?'box':resultshow?'gubox':'box']" -->
        <div style="display: flex;" v-for="(item,i) in list" :key="i">
            <div style="flex:1; flex-direction: column; display: flex; min-height: 780px;" v-if="resultshow">
                <slideshow @getlist="getlistt" :ref="refname" @changefuc="ischange" :key="num" :i="i" :item="name == 'skuimg'?item.photo:item" :list="alllist" :bannum="bannum" :name="name"></slideshow>
            </div>
            <div style="flex:3;" class="gubox"  @click="indexval(i)" :id="'quanmodule'+i">
            <div class="imgbox">
                <div style="width: 100%; height: 100%; " :style="skushow?{marginTop: '0px'}:resultshow?{}:{marginTop: '-5px'}">
                    <div style="margin: 6px 30px;">
                        <div style="width:100%; height:100%;" class="flexrow">
                            <div class="flex2 flexrow" style="align-items: center;" v-if="!disabled">
                                <el-button type="danger" :disabled="disabled" size="mini" style="height: 27px; width: 80px; margin-left: 5px;" @click="delemodule(i)">删除模块</el-button>
                                <div style="width: 22px; height: 22px;background-color: #409EFF; margin-left: 5px;" @click="whitchmodule(i,1)"></div>
                                <div style="width: 22px; height: 22px; margin-left: 5px;" class="flexrow" @click="whitchmodule(i,2)" v-if="duimoudle">
                                    <div style="background-color: #409EFF;margin-right: 1px; width: 10px; height: 22px;" v-for="i in 2" :key="i"></div>
                                </div>
                                <div style="width: 22px; height: 22px; margin-left: 5px;" @click="whitchmodule(i,22)">
                                    <div style="background-color: #409EFF;margin-bottom: 1px; width: 22px; height: 10px;" v-for="i in 2" :key="i"></div>
                                </div>
                                <div style="margin-left: 5px; display: flex; flex-wrap: wrap; flex-direction: row;" @click="whitchmodule(i,4)" v-if="duimoudle">
                                    <i class="el-icon-menu" style="font-size: 27px; margin-top: -1px; color: #409EFF;" ></i>
                                </div>
                                <div style="width: 22px; height: 22px; margin-left: 5px;" class="flexcolumn" @click="whitchmodule(i,6)" v-if="duimoudle">
                                    <div style="flex:1;width: 100%; background-color: #409EFF; " class="flexrow">
                                        <div style="flex:1;height: 100%; background-color: #409EFF; "></div>
                                        <span style="height: 100%; width: 1px; background-color: white;"></span>
                                        <div style="flex:1;height: 100%; background-color: #409EFF; "></div>
                                        <span style="height: 100%; width: 1px; background-color: white;"></span>
                                        <div style="flex:1;height: 100%; background-color: #409EFF; "></div>
                                    </div>
                                    <span style="height: 1px; width: 100%; background-color: white;"></span>
                                    <div style="flex:1;width: 100%; background-color: #409EFF; " class="flexrow">
                                        <div style="flex:1;height: 100%; background-color: #409EFF; "></div>
                                        <span style="height: 100%; width: 1px; background-color: white;"></span>
                                        <div style="flex:1;height: 100%; background-color: #409EFF; "></div>
                                        <span style="height: 100%; width: 1px; background-color: white;"></span>
                                        <div style="flex:1;height: 100%; background-color: #409EFF; "></div>
                                    </div>
                                </div>
                                <div style="margin-left: 5px; display: flex; flex-wrap: wrap; flex-direction: row;" @click="whitchmodule(i,9)" v-if="duimoudle">
                                    <i class="el-icon-s-grid" style="font-size: 27px; margin-top: -1px; color: #409EFF;"></i>
                                </div>
                                <div style="width: 22px; height: 22px; margin-left: 5px;" @click="whitchmodule(i,3)" v-if="!duimoudle">
                                    <div style="background-color: #409EFF;margin-bottom: 1px; width: 22px; height: 6.5px;" v-for="i in 3" :key="i"></div>
                                </div>
                            </div>
                            <div class="flex1">
                                <div class="flexrow">
                                    <span style="line-height: 30px; font-weight: 600; width: 50px;">要求：</span>
                                    <el-button-group style="width: 150px;" v-if="requirementshow">
                                        <el-button :disabled="disabled" :type="item.requirement==1?'primary':''" @click="isbutton(i,1)"  style="height: 27px; width: 65px; margin-top: 2px;">拍摄</el-button>
                                        <el-button :disabled="disabled" :type="item.requirement==2?'primary':''" @click="isbutton(i,2)"  style="height: 27px; width: 65px; margin-top: 2px;">建模</el-button>
                                    </el-button-group>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="width: 100%;  position: relative; display: flex; flex-direction: column;" id="allmoubvle" class="flexcenter">
                        <!-- 一 -->
                        <div style=" width: 520px; min-height: 520px; background-color: #eee;" v-if="item.ninePalacesType == 1||item.ninePalacesType == 0&&showban">
                            <div style="margin: 0;" v-for="(item5,i5) in item.mainPhotoInfo" :key="i5" >
                                <pastimgVue :ispaste="ispaste"  :toimage="[i,item5.url]" :name="'mainPhotoInfo'" :hownum="'one'" @childtoimg="childtoimgg" :keyy="[i,i5]" ></pastimgVue>
                            </div>
                        </div>
                        <!-- 二1 @click="changecolor(i5,'two')" -->
                        <div style="height: 520px; width: 520px;" class="flexrow" v-else-if="item.ninePalacesType == 2&&showban">
                            <div style="height: 100%; width: 258px; border: 1px solid white;" class="flex1" v-for="(item5,i5) in item.mainPhotoInfo" :key="i5">
                                <pastimgVue :ispaste="ispaste"  :toimage="[i,item5.url]" :name="'mainPhotoInfo'" :hownum="hownum" @childtoimg="childtoimgg" :keyy="[i,i5]" ></pastimgVue>
                            </div>
                        </div>
                        <!-- 二2 -->
                        <div style="min-height: 520px; width: 520px;" class="flexcolumn" v-else-if="item.ninePalacesType == 22&&showban">
                            <div style="width: 100%; min-height: 258px; border: 1px solid white;" class="flex1" v-for="(item5,i5) in item.mainPhotoInfo" :key="i5">
                                <pastimgVue :ispaste="ispaste" :toimage="[i,item5.url]" :name="'mainPhotoInfo'" :hownum="'tworow'" @childtoimg="childtoimgg" :keyy="[i,i5]" ></pastimgVue>
                            </div>
                        </div>
                        <!-- 四 -->
                        <div style="height: 520px; width: 520px; flex-wrap: wrap; word-break: break-all; display: flex; flex-direction: row;" v-else-if="item.ninePalacesType == 4&&showban">
                            <div style="height: 258px; width: 258px; border: 1px solid white;" v-for="(item5,i5) in item.mainPhotoInfo" :key="i5">
                                <pastimgVue :ispaste="ispaste" :toimage="[i,item5.url]" :name="'mainPhotoInfo'" :hownum="hownum" @childtoimg="childtoimgg" :keyy="[i,i5]" ></pastimgVue>
                            </div>
                        </div>
                        <!-- 六 -->
                        <div style="height: 520px; width: 520px; flex-wrap: wrap; word-break: break-all; display: flex; flex-direction: row;" class="flexcolumn" v-else-if="item.ninePalacesType == 6&&showban">
                                <div style="height: 258px; width: 170px; border: 1px solid white; " v-for="(item5,i5) in item.mainPhotoInfo" :key="i5">
                                    <pastimgVue :ispaste="ispaste" :toimage="[i,item5.url]" :name="'mainPhotoInfo'" :hownum="hownum" @childtoimg="childtoimgg" :keyy="[i,i5]" ></pastimgVue>
                                </div>
                        </div>
                         <!-- 九 -->
                         <div style="height: 520px; width: 520px; flex-wrap: wrap; word-break: break-all; display: flex; flex-direction: row;"  class="flexcolumn" v-else-if="item.ninePalacesType == 9&&showban">
                            <div style="height: 170px; width: 170px; border: 1px solid white;" v-for="(item5,i5) in item.mainPhotoInfo" :key="i5">
                                    <pastimgVue :ispaste="ispaste" :toimage="[i,item5.url]" :name="'mainPhotoInfo'" :hownum="hownum" @childtoimg="childtoimgg" :keyy="[i,i5]" ></pastimgVue>
                            </div>
                        </div>
<!-- 三  -->
                        <div style="width: 520px;" :style="duimoudle?{height: '520px'}:{minHeight: '520px'}" class="flexcolumn" v-else-if="item.ninePalacesType == 3&&showban">
                            <div style="width: 100%; min-height: 170px; border: 1px solid white;" class="flex1"  v-for="(item5,i5) in item.mainPhotoInfo" :key="i5">
                                <pastimgVue :ispaste="ispaste" :toimage="[i,item5.url]" :name="'mainPhotoInfo'" :hownum="'three'" @childtoimg="childtoimgg" :keyy="[i,i5]" ></pastimgVue>
                            </div>
                        </div>
                       
                    </div>

<!-- 添加备0注:style="skushow?{marginBottom: '0px'}:{marginBottom: '30px'}" -->
                <div style="margin: 0; align-items: center; margin-bottom: 30px;" :style="skushow?{marginBottom: '10px'}:{marginBottom: '30px'}" class="flexcolumn">
                    <div style="width: 520px;" class="flexcolumn">
                        <el-button type="primary" style="margin-top: 5px;" @click="addmainmask(i)" :disabled="disabled">添加备注</el-button>
                        <div style="width: 100%;"  :style="skushow?{height: '135px',overflowY: 'auto'}:resultshow?{height: '160px',overflowY: 'auto'}:{minHeight: '120px'}" class="flexcolumn">
                            <div class="flexrow" style="align-items: center; margin-top: 5px;" v-for="(item2,i2) in item.mainmarkInfo" :key="i2">
                                <el-input maxlength="500" v-model="item2.mainMarkInfo" placeholder="请输入内容" :disabled="disabled"></el-input>
                                <i class="el-icon-error" style="color:red;" @click="delmainmask(i,i2)" v-if="!disabled"></i>
                            </div>
                        </div>
                    </div>
                </div>

                </div>
            </div>
            <div class="msgbox">
                <div style="height:45px;  width: 100%;display: flex; overflow-y: auto;">
                    <div style="flex: 7; height: 100%; display: flex; flex-direction: column;">
                        <div style="width: 100%; z-index: 5; height: 100%; align-items: center;" class="flexrow">
                            <span style="width: 50px; font-weight: 600;">风格：</span>
                            <el-input maxlength="500" @change="styleinfofunc(i,item.styleInfo)" v-model="item.styleInfo" placeholder="按图一风格设计" :disabled="disabled" style="width: 205px;"></el-input>
                            <el-button @click="addimgmsg(i)" style="margin-left: 10px; width: auto; height: 28px; margin-top: 2px;" :disabled="disabled">添加图文</el-button>
                        </div>
                    </div>
                    <div style="flex: 3; height: 100%; text-align: center; display: flex; flex-direction: column; align-items: center;">
                        <div style="width: 100%; height: 100%; z-index: 5; align-items: center; justify-content: center;" class="flexrow">
                            <el-button style="width: 110px" @click="addcanimg(i)" :disabled="disabled">添加图片</el-button>
                        </div>
                    </div>
                </div>
                <div style="width: 100%;display: flex;" :style="resultshow?{overflowY: 'auto',height: '700px'}:{height:'94%'}">
                    <div style="flex: 7; height: 96%; display: flex; flex-direction: column;">
                        <div class="flexrow" style="margin-bottom: 15px;" v-for="(item3,i3) in item.marks" :key="i3">
                            <i class="el-icon-error" style="color:red; display: flex; align-self: center;" @click="deltalkmsg(i,i3)" v-if="!disabled"></i>
                            <div style="height:103px; flex: 3; border: 1px solid #eee;">
                                <pastimgVue :inputedit="false" :ispaste="ispaste" :id="i" :toimage="[null,item3.url]" :name="'marks'" @childtoimg="childtoimgg" :keyy="[i,i3]"></pastimgVue>
                            </div>
                            <div style="min-height:100px; flex: 7; border: 1px solid #eee;">
                                <el-input
                                :disabled="disabled"
                                type="textarea"
                                maxlength="500"
                                :autosize="{ minRows: 5, maxRows: 99}"
                                placeholder="请输入内容"
                                v-model="item3.markinfo">
                                </el-input>
                            </div>
                        </div>
                        
                    </div>
                    <div style="flex: 3; height: 96%; text-align: center; display: flex; flex-direction: column; align-items: center; ">
                        <div style="width: 110px; height: 110px;  margin-bottom: 15px; border: 1px solid #eee; position: relative;" v-for="(item4,i4) in item.assPhotoInfo" :key="i4">
                            <i class="el-icon-error" style="color:red; top: -5px; right: -5px; z-index: 99; position: absolute;" @click="delassPhoto(i,i4)" v-if="!disabled"></i>
                            <pastimgVue :inputedit="false" :ispaste="ispaste" :id="i" :toimage="['',item4.url]" :name="'assPhotoInfo'" @childtoimg="childtoimgg" :keyy="[i,i4]"></pastimgVue>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
        </div>
        
       

        <el-dialog
        title="请输入保存参考名称"
        :visible.sync="dialogVisible"
        @close="dialogclose(closeth)"
        append-to-body
        width="30%">
        <el-input
        placeholder="请输入保存参考名称"
        maxlength="100"
        v-model="titleval">
        </el-input>
        <el-row>
            <el-col :span="22"><div class="grid-content bg-purple-dark"></div></el-col>
            <el-col :span="2"><div class="grid-content bg-purple-dark"><el-button style="margin-left: auto;" type="primary" @click="finsubmit">保存</el-button></div></el-col>
        </el-row>
        </el-dialog>
    </my-container>
</div>
</template>

<!-- <script src="https://cdn.bootcss.com/html2canvas/0.5.0-beta4/html2canvas.js"></script> -->
<script>
import slideshow from '@/views/media/shooting/fuJianmanage/slideshow';
import drawImg from '@/views/media/shooting/fuJianmanage/drawimgs'
import MyContainer from "@/components/my-container";
import pastimgVue from './pastimg.vue';
import {getReferenceMainReferencs,saveReferenceMainReferencs,saveReferenceMainReferencsForImg,saveReferenceMainReferencsForSku} from '@/api/media/referencemanage';
export default {
    name: 'Shootingcreateindex1',
    props:{
        name: {type: String,default: ''},
        btnshow: {type: Boolean,default: true},
        minheight: {type: Boolean,default: true},
        ispaste: {type: Boolean,default: true},
        bannum: {type: Number,default: 1},
        alllist: {type: Array, default: function() {
			return []
		}},
        main: {type: Object,default: null},
        listid: {default: 0},
        duimoudle: {type: Boolean,default: true},
        sku: {type: Array,default: function() {
			return []
		}},
        disabled: {type: Boolean,default: false},
        isfath: {type: Boolean,default: false},
        resultshow: {type: Boolean,default: false},
        skushow: {type: Boolean,default: false},
        refname: {type: String,default: ''},
    },
    components: {drawImg,MyContainer,pastimgVue,slideshow},
    data() {
        return {
            hownum: null,
            showban: true,
            requirementshow: true,
            textarea: '',
            pageLoading: false,
            bigimg: '',
            titleval: '',
            dialogVisible: false,
            istomessage: false,
            drawnum: -1,
            imgUrl: '',
            tomsg: [],
            cutImgSrc: '',
            sizeinput2: '',
            draw:false,
            isshow: false,
            startdemo: null,
            placetext: '请按Ctrl+v粘贴图片...',
            list:[],
            // chulist: [],
            canvasimg: [],
            watname: '',
            input: '',
            sizeinput: '',
            num: -1,
            beiinput: '',
            demohtml: [],
            textarea2: null,
            tableData: [],
            urlimg: null,
            mainTask: null,
            indexx: null,
            listname: '任务名称',
            listidd: null,
            time: 0,
            closeth: false,
            modulenum: null,
            skuu: null,
            arraylist: [],
            nummber: 0,
        };
    },
    watch: {
        sku: 'skuchange',
        alllist: 'alllistchange'
    },
    // watch: {
    //     handler(newValue, oldValue) {
    //         // if(!this.listid){
    //         //     if(oldValue!=newValue){
    //         //         this.$emit("changefuc",true)
    //         //         this.time = 0;
    //         //     }
    //         // } 
    //     },
    //     deep: true
    // },
    // computed: {
    //     handler(){
    //         return [...this.list]
    //     }
    // },

    mounted() {
            let _this = this;
            _this.nummber = _this.bannum-1;
            if(_this.name == 'skuimg'){
                _this.arraylist = _this.alllist.photo;
            }else{
                _this.arraylist = _this.alllist;
            }

            if(this.name){
                this.watname = this.name;
            }
            if(this.sku){
                this.skuu = this.sku;
            }
            if(this.alllist.length>0){
                this.alllist.map((item)=>{
                    if(!item.marks){
                        item.marks=[];
                    }
                    if(item.assPhotoInfo == null){
                        item.assPhotoInfo = [];
                    }
                    if(item.ninePalacesType == 1){
                    this.hownum = 'one'
                    }else if(item.ninePalacesType == 2){
                        this.hownum = 'twocolumn'
                    }else if(item.ninePalacesType == 22){
                        this.hownum = 'tworow'
                    }else if(item.ninePalacesType == 4){
                        this.hownum = 'four'
                    }else if(item.ninePalacesType == 6){
                        this.hownum = 'six'
                    }else if(item.ninePalacesType == 9){
                        this.hownum = 'nine'
                    }else if(item.ninePalacesType == 3){
                        this.hownum = 'three'
                    }
                })

                this.list = this.alllist;
            }
    },

    methods: {
        alllistchange(val){
            this.recash(val);
        },
        recash(val){
            let _this = this;
            _this.nummber = _this.bannum-1;
            if(_this.name == 'skuimg'){
                _this.arraylist = val.photo;
            }else{
                _this.arraylist = val;
            }

            if(this.name){
                this.watname = this.name;
            }
            if(this.sku){
                this.skuu = this.sku;
            }
            if(val.length>0){
                val.map((item)=>{
                    if(!item.marks){
                        item.marks=[];
                    }
                    if(item.assPhotoInfo == null){
                        item.assPhotoInfo = [];
                    }
                    if(item.ninePalacesType == 1){
                    this.hownum = 'one'
                    }else if(item.ninePalacesType == 2){
                        this.hownum = 'twocolumn'
                    }else if(item.ninePalacesType == 22){
                        this.hownum = 'tworow'
                    }else if(item.ninePalacesType == 4){
                        this.hownum = 'four'
                    }else if(item.ninePalacesType == 6){
                        this.hownum = 'six'
                    }else if(item.ninePalacesType == 9){
                        this.hownum = 'nine'
                    }else if(item.ninePalacesType == 3){
                        this.hownum = 'three'
                    }
                })

                this.list = val;
            }
        },
        changecolor(index,val){
            var a = document.getElementById(val)[index]
            // var a = document.getElementById(val)
        },
        delassPhoto(i,index){
            this.list[i].assPhotoInfo.splice(index,1)
            this.ischange(true);
        },
        deltalkmsg(i,index){
            this.list[i].marks.splice(index,1)
            this.ischange(true);
        },
        skuchange(val){
            let _this = this;
            _this.skuu = val;
        },
        async childtoimgg(name,arrval){
            // this.ispastimg = true;
            await this.creimgfuc(name,arrval)
            this.ischange(true);
        },
        creimgfuc(val,arrval){
            if(val&&arrval!=null){
                this.list[arrval[0][0]][val][arrval[0][1]].fileName = arrval[1].fileName;
                this.list[arrval[0][0]][val][arrval[0][1]].filePath = arrval[1].relativePath;
                this.list[arrval[0][0]][val][arrval[0][1]].url = arrval[1].url;

            }
        },
        styleinfofunc(index,val){
            this.list[index].styleInfo = val
        },
        addcanimg(val){
            let param = {fileName:'',filePath:'',url:''};
            this.list[val].assPhotoInfo.push(param)
        },
        addimgmsg(val){
            let param = {fileName:'',filePath:'',url:'',markinfo:''};
            this.list[val].marks.push(param)
        },
        delmainmask(i1,i2){
            this.list[i1].mainmarkInfo.splice(i2,1)
            this.ischange(true);
        },
        addmainmask(val){
            if(this.list[val].mainmarkInfo == null){
                this.list[val].mainmarkInfo = [];
            }
            let mainMarkInfo = {"mainMarkInfo":''}
            this.list[val].mainmarkInfo.push(mainMarkInfo)

        },
        whitchmodule(index,val){
            this.showban = false;
            this.showban = true;
            this.list[index].ninePalacesType = val;
            this.list[index].mainPhotoInfo = []
            let param = {"fileName":"","filePath":"","url":""};
            if(val == 22){
                var b = 2;
            }else{
                var b = val;
            }

            if(val==1){
                this.hownum = 'one'
            }else if(val == 2){
                this.hownum = 'twocolumn'
            }else if(val == 22){
                this.hownum = 'tworow'
            }else if(val == 4){
                this.hownum = 'four'
            }else if(val == 6){
                this.hownum = 'six'
            }else if(val == 9){
                this.hownum = 'nine'
            }else if(val == 3){
                this.hownum = 'three'
            }

            for(var i =0; i<b; i++){
                this.list[index].mainPhotoInfo.push(Object.assign({},param))
            }
        },
        isbutton(index,num){
            this.num = num;
            this.requirementshow =false;
            this.list[index].requirement = num;
            this.requirementshow =true;
        },
        dialogclose(val){
            if(!val){
                this.$emit('cleardig',false)
            }
        },
        ischange(val){
            if(val){
                this.$emit("changefuc",val)
            }
        },
        delmsaage(i,ind){
            this.list[i].marks.splice(ind,1)
            this.ischange(true)
        },
        delemodule(index){
            this.list.splice(index,1)
            this.ischange(true);
        },
        indexval(val){
            this.indexx = val
        },
        // inputchange(val){
        //     let _this = this;
        //     _this.list[_this.indexx].markinfo = val;
        //     _this.ischange(true);
        // },
        submitt(){
            let _this = this;
            _this.dialogVisible = true;
        },
        getlistt(){
            this.$emit("getlist") 
        },
        submitty(){
            let _this = this;
            _this.$nextTick(()=>{
                _this.$refs[`${_this.refname}`][0].submitt();
            })
        },
        async tosubmitt(){
            let _this = this;
            _this.mainTask = {
                referenceManageTaskId: _this.listid? _this.listid:0,
                taskName: _this.titleval?_this.titleval:_this.main.taskName,
            };


            await _this.getlist();
            await _this.$emit('getalllist');
        },
        async finsubmit(){
            let _this = this;
            if(_this.titleval==''){
                this.$message("标题不能为空！")
                return;
            }


            _this.mainTask = {
                referenceManageTaskId: _this.listid? _this.listid:0,
                taskName: _this.titleval?_this.titleval:_this.main.taskName,
            };
            _this.closeth = true;
            _this.$emit('cleardig',true)

            await this.getlist();
            await _this.$emit('getalllist',_this.listidd);
        },
        async getlist(){
            let _this = this;
            let params = {
                type: _this.bannum,
                mainTask: _this.mainTask,
                photoInfo: _this.list,
                sku: _this.skuu?_this.skuu:[]
            }
        // 校验为空
        // for(var i = 0; i<_this.list.length; i++){
        //     console.log("_this.list",_this.list[i].url)
        //     if(_this.list[i].markinfo==''){
        //         this.$message("内容不能为空，请输入再保存！")
        //         return
        //     }
        // }

             _this.pageLoading= true;
             if(this.isfath){
                var res = await saveReferenceMainReferencsForSku(params);
             }else{
                var res = await saveReferenceMainReferencsForImg(params);
             }
           
           if(res.success){
                this.$message.success({
                    message: "保存成功！",
                    offset: 150,
                    duration: 2000
                })
                _this.listidd = res.data.referenceManageTaskId;
                _this.dialogVisible = false;
           }
            _this.pageLoading= false;
        },
        addlist(){
            let _this = this;
            let param = {
                type: _this.bannum,
                markinfo: '',
                ninePalacesType: 0,
                referenceManageTaskId: _this.listid? _this.listid:0,
                marks: [{fileName:'',filePath:'',url:'',markinfo: ''}],
                mainmarkInfo: [{mainMarkInfo:''}],
                assPhotoInfo: [],
                mainPhotoInfo: [{"fileName":"","filePath":"","url":""}]
            }
                this.list.push(param);
                this.ischange(true);
        },
        changeContent(e,index){
            let _this = this;
            _this.draw = true;
            const dataTransferItemList = e.clipboardData.items;
            // 过滤非图片类型
            const items = [].slice.call(dataTransferItemList).filter(function (item) {
                return item.type.indexOf('image') !== -1;
            });
            if (items.length === 0) {
                this.$message('请粘贴正确图片');
                return;
            }
                if(_this.istomessage){
                    _this.draw = false;
                    // this.$message('请先取消，再粘贴图片操作！');
                    // return;
                }
                _this.ischange(true)

                const dataTransferItem = items[0];
                const file = dataTransferItem.getAsFile();
                //请求图片接口
                _this.uploadToServer(file, (res) => {
                    // _this.canvasimg.push(res.data.url);
                    _this.bigimg = res.data;
                    _this.list[index].fileName= res.data.fileName;
                    _this.list[index].filePath= res.data.relativePath;
                    _this.list[index].url= res.data.url;
                    // _this.list[index] = {...res.data}
                })


                var event = event || window.event;
                // var file = event.target.files[0];
                var reader = new FileReader(); 
                reader.onload = function(e) {
                    _this.canvasimg[index] = e.target.result
                }
                reader.readAsDataURL(file);
            
        },
        imgclickk(){
            console.log("点击图片")
        },
        onDivInput(e,index) {
                let _this = this;
                if(e.target.innerText){
                    e.target.innerText=''
                    return;
                }
                    let filterImgContent = e.target.innerHTML.replace(/\<img/g,"<img style='width:50px;height:50px;'")
                    this.bingval = filterImgContent;
                    _this.demohtml = filterImgContent.match(/<img.*?>/g);
                    
                    let DomList=document.getElementById('replyInput'+index).querySelectorAll('img')
                    for(let i in  DomList){
                            if( DomList[i].style){
                                DomList[i].style.width='auto'
                                DomList[i].style.height='auto'
                                DomList[i].style.maxWidth = '100%'
                                DomList[i].style.maxHeight = '100%'
                                DomList[i].id = "imgid"+index

                                // DomList[i].onmousedown = function(e){
                                //     _this.inputcli(index);
                                // };
                                // DomList[i].style.
                                // DomList[i].mode = 'aspectFit'
                                // DomList[i].style= 'cover';
                            }
                        }
                        



                let demo = document.getElementById('replyInput'+index)
                // let demo = document.getElementsByClassName('pastimg')
                
                if(_this.demohtml.length>1){
                    if(DomList.item(0)==_this.startdemo){
                        demo.removeChild(DomList.item(0));
                        _this.startdemo = DomList.item(1);
                    }else{
                        demo.removeChild(DomList.item(1));
                        _this.startdemo = DomList.item(0);
                    }
                }else{
                    _this.startdemo = DomList.item(0);
                }
                
                    
        },
        inputcli(e){
            document.getElementById('replyInput'+e).focus();
        },
        deletecanvas(){
            this.draw = false;
        },
        dataURLtoBlob(dataurl) {
        var arr = dataurl.split(','),
            mime = arr[0].match(/:(.*?);/)[1],
            bstr = atob(arr[1]),
            n = bstr.length,
            u8arr = new Uint8Array(n);
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }
        return new Blob([u8arr], { type: mime });
        },
        blobToFile(theBlob, fileName){
        theBlob.lastModifiedDate = new Date();
        theBlob.name = fileName;
        return new File([theBlob], fileName, {type: theBlob.type, lastModified: Date.now()});
        },
        offsubmit(i){
            this.draw = false;
        },
        uploadToServer(file, callback) {
            var xhr = new XMLHttpRequest()
            var formData = new FormData()
            formData.append('file', file)
            xhr.open('post', '/api/uploadnew/file/UploadCommonFileAsync')
            xhr.withCredentials = true
            xhr.responseType = 'json'
            xhr.send(formData)
            xhr.onreadystatechange = () => {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    // debugger;
                    callback(xhr.response)
                }
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.body{
    // padding: 10px;
    position: relative;
}
.box{
    // padding: 10px 0;
    // margin: 20px 0;
    min-height: 780px;
    border: 1px solid #eee;
    display: flex;
    flex-direction: row;
}
.gubox{
    // padding: 10px 0;
    // margin: 30px 0;
    min-height: 780px;
    // margin: 0 auto;
    border: 1px solid #eee;
    display: flex;
    flex-direction: row;
}
.flex2{
    flex: 2;
}
.flex1{
    flex: 1;
}
.twobox{
    padding: 10px 0;
    margin: 20px 0;
    min-height: 500px;
    display: flex;
    flex-direction: row;
}
.fourbox{
    padding: 20px;
    margin: 20px 0;
    min-height: 500px;
    border: 1px solid #eee;
    display: flex;
    flex-direction: column;
    .bottombox{
        width: 100%;
        height: 100%;
        // border: 1px solid #aaa;
        padding: 20px 0 20px 0;
        display: flex;
        .bottombox-left{
            flex: 4;
            display: flex;
            align-items: center;
        }
        .bottombox-right{
            flex: 6;
            display: flex;
            flex-direction: column;
            .right-bot{
                flex: 8;
                padding: 20px;
                margin: 0;
                // border: 1px solid #eee;
            }
            .right-top{
                display: flex;
                flex-direction: row;
                flex: 2;
                padding: 20px;
                margin: 0;
                // border: 1px solid #eee;
                .top-right{
                    width: auto;
                    margin-left: 20px;
                }

                .top-left{
                    width: 200px;
                }

            }
        }
    }
}
.imgbox{
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 5;
    position: relative;

}
.flexcenter{
    display: flex;
    justify-content: center;
    align-items: center;
}
.msgbox{
    flex: 4;
    max-width: 600px;
    // padding: 50px 0;
}
.fontwei{
    font-weight: 600;
}
.flexcolumn{
    display: flex;
    flex-direction: column;
}
.flexrow{
    display: flex;
    flex-direction: row;
    width: 100%;
}
.pastimg{
    width: 520px;
    height: 520px;
    background-color: #eee;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}
.ispointer{
    cursor: pointer;
}
div{
    caret-color: transparent;
}
.point{
    cursor: crosshair;
}
.module{
    // background-color: #eee;
    margin-top: 30px;
    width: 100%;
    min-height: 400px;
}
.overfolw{
    height: 95%;
    overflow-y: auto;
}
.borderr{
    border: 1px solid red;
}
</style>