<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始时间"
          end-placeholder="结束时间" :picker-options="pickerOptions" style="width: 300px;margin-right: 10px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-input v-model="ListInfo.supplierName" placeholder="供应商名称" maxlength="50" clearable class="publicCss" />

        <el-select v-model="ListInfo.variationTypes" placeholder="变动类型" clearable multiple filterable collapse-tags
          class="publicCss">
          <el-option :key="'盘盈'" label="盘盈" :value="'盘盈'" />
          <el-option :key="'盘亏'" label="盘亏" :value="'盘亏'" />
          <el-option :key="'退供'" label="退供" :value="'退供'" />
          <el-option :key="'库存报废'" label="库存报废" :value="'库存报废'" />
          <el-option :key="'客单发货'" label="客单发货" :value="'客单发货'" />
          <el-option :key="'平台客单发货'" label="平台客单发货" :value="'平台客单发货'" />
          <el-option :key="'备货订单入库'" label="备货订单入库" :value="'备货订单入库'" />
          <el-option :key="'客单退货'" label="客单退货" :value="'客单退货'" />
        </el-select>

        <el-input v-model="ListInfo.sKC" placeholder="SKC" maxlength="50" clearable class="publicCss" />
        <!-- <el-input v-model="ListInfo.sKU" placeholder="SKU" maxlength="50" clearable class="publicCss" /> -->
        <el-input v-model="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button @click="startImport"
          type="primary" icon="el-icon-share" @command="handleCommand"> 导入
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a">下载模版</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button type="primary" @click="onExport" style="margin-left: 5px;" v-if="checkPermission('shein_SheInInventoryLossDetail_export')">导出</el-button>
      </div>
    </template>
    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" v-loading="loading"
      :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="45%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 100px;">
        <el-date-picker style="width: 200px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay" type="date"
          placeholder="选择日期" :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
        </el-date-picker>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action=""
          accept=".xlsx" :file-list="fileList" :http-request="onUploadFile" :on-success="onUploadSuccess"
          :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary" style="width: 90px;">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px;width: 90px;" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import { getNewSheInInventoryDepletion, importAfterSalesSubjectAnalysis } from '@/api/bookkeeper/reportdayV2'
import { exportInventorySheIn } from '@/api/bookkeeper/crossBorderV2'
import { formatTime } from "@/utils/tools";

const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopCode', label: '店铺ID', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'changeType', label: '变动类型', },
  { sortable: 'custom', width: '150', align: 'center', prop: 'businessOrderNumber', label: '业务单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'documentNumber', label: '单据号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'occurrenceTime', label: '发生时间', formatter: (row) => formatTime(row.occurrenceTime, 'YYYY-MM-DD') },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'addTime', label: '添加时间', formatter: (row) => formatTime(row.addTime, 'YYYY-MM-DD') },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'supplierId', label: '供应商ID', },
  { sortable: 'custom', width: '150', align: 'center', prop: 'supplierName', label: '供应商名称', },
  // { sortable: 'custom', width: 'auto', align: 'center', prop: 'supplierProductNumber', label: '供方货号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: '150', align: 'center', prop: 'skc', label: 'SKC', },
  { sortable: 'custom', width: '150', align: 'center', prop: 'sku', label: 'SKU', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'attributeSet', label: '属性集', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'quantity', label: '数量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'unitPrice', label: '单价', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'amount', label: '金额', },
  // { sortable: 'custom', width: 'auto', align: 'center', prop: 'currency', label: '币种', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'cost', label: '单个成本', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'saleCost', label: '总成本', },
]
export default {
  name: "SheInInventoryLossDetail",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      summaryarry: {},
      dialogVisible: false,//导入弹窗
      uploadLoading: false,//上传loading
      fileList: [],//上传文件列表
      yearMonthDay: null,//导入日期
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        supplierName: null,//供应商名称
        variationTypes: null,//变动类型
        sKU: null,//SKU
        goodsCode: null,//商品编码
        sKC: null,//SKC
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("yearMonthDay", this.yearMonthDay);
      form.append("platForm", 12);
      var res = await importAfterSalesSubjectAnalysis(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (!this.yearMonthDay) {
        this.$message({ message: "请选择日期", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
      await this.getList()
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
      }
      const replaceArr = ['supplierName', 'variationType', 'sKU', 'goodsCode', 'sKC'] //替换空格的方法,该数组对应str类型的input双向绑定的值
      this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
      this.ListInfo.variationType=this.ListInfo.variationTypes.join(",")
      
      this.loading = true
      const { data, success } = await getNewSheInInventoryDepletion(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async handleCommand(command) {
      switch (command) {
        //下载模版
        case 'a':
          await this.downLoadFile()
          break;
      }
    },
    async downLoadFile() {
      window.open("/static/excel/KjBillCharges/希音全托库存损耗明细.xlsx", "_blank");
    },
    async onExport() {//导出列表数据；

      const replaceArr = ['supplierName', 'variationType', 'sKU', 'sKC'] //替换空格的方法,该数组对应str类型的input双向绑定的值
      this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
      this.ListInfo.variationType=this.ListInfo.variationTypes.join(",")
      var res = await exportInventorySheIn(this.ListInfo);
      if (res?.data.success) {
        this.$message({ message: res.data.msg, type: "success" });
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 45px;
}
</style>
