<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-select v-model="ListInfo.ddUserIds" placeholder="提交人" clearable filterable remote multiple collapse-tags
          :remote-method="getprosimstatelist" @change="oncorporation" class="publicCss">
          <el-option v-for="item in submitterList" :key="item.ddUserId"
            :label="`${item.userName} ${item.title ? ' - ' : ''} ${item.title ? item.title : ''}`"
            :value="item.ddUserId" />
        </el-select>
        <el-select v-model="ListInfo.deptId" placeholder="所在部门" clearable filterable remote
          :remote-method="getdepartmentlist" @change="ondepartment" class="publicCss">
          <el-option v-for="item in departmentList" :key="item.dept_id" :label="item.full_name" :value="item.dept_id" />
        </el-select>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
        <el-button v-if="checkPermission('api:Inventory:ExtraIncome:SaveExtraIncomeSet')" type="primary"
          @click="configurationMethod">提成配置</el-button>
      </div>
    </template>
    <vxetablebase :id="'redEnvelopeIncome202408041900'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <template slot="right">
        <vxe-column title="附件" width="120">
          <template #default="{ row, $index }">
            <div style="position: relative; display: inline-block;" v-if="row.annexUrl">
              <el-image class="custom-image" slot="reference" :src="row.annexUrl[0] || ''" fit="fill"
                :preview-src-list="row.annexUrl != '' ? row.annexUrl : ''" style="width: 40px; height: 38px;">
              </el-image>
              <span class="circle-badge">
                {{ row.annexUrl.length }}
              </span>
            </div>
          </template>
        </vxe-column>
        <vxe-column title="操作" width="120">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;align-items: center;">
              <el-button v-if="checkPermission('api:Inventory:ExtraIncome:DelExtraIncome')" type="text"
                @click="onListDeletion(row)">删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog :visible.sync="royaltiesVisible" width="40%" v-dialogDrag>
      <template #title>
        <div style="display: flex;justify-content: center;margin-top: 15px;">
          <span style="font-size: 19px;font-weight: bold;">提成配置</span>
        </div>
      </template>
      <div style="height: 300px;">
        <div style="margin: 10px 0;">
          <span>提成比例：</span>
          <el-input-number v-model="disposition.commissionRatio" placeholder="请输入" :min="0.01" :max="100" :precision="2"
            :controls="false" class="publicCss" />
          <span style="margin-left: 20%;">生效日期：</span>
          <el-date-picker v-model="disposition.effectiveTime" type="date" placeholder="请选择日期" style="width: 150px;"
            :value-format="'yyyy-MM-dd'">
          </el-date-picker>
        </div>
        <el-table :data="dispositionData" style="width: 100%;max-height: 300px; overflow: auto;" height="200">
          <el-table-column type="index" width="50">
          </el-table-column>
          <el-table-column label="提成比例" width="150" :show-overflow-tooltip="true">
            <template slot-scope="{row}">
              <span>{{ row.commissionRatio }}</span>
              %
            </template>
          </el-table-column>
          <el-table-column label="生效日期" width="200" :show-overflow-tooltip="true">
            <template slot-scope="{row}">
              <span>{{ row.effectiveTime }}</span>
            </template>
          </el-table-column>
          <el-table-column label="添加人" width="150" :show-overflow-tooltip="true">
            <template slot-scope="{row}">
              <span>{{ row.createdUserName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="添加时间" :show-overflow-tooltip="true">
            <template slot-scope="{row}">
              <span>{{ row.createdTime }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div style="display: flex;justify-content: center;margin-top: 10px;">
          <el-button @click="royaltiesVisible = false">取 消</el-button>
          <el-button type="primary" @click="configurationSaving">保 存</el-button>
        </div>
      </template>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getExtraIncomePage, delExtraIncome, getDDUserList, getDeptViews, getExtraIncomeSetPage, saveExtraIncomeSet, exportExtraIncome } from '@/api/inventory/extraIncome'
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'userName', label: '提交人', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'deptName', label: '所在部门', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'businessId', label: '审批编号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'applicationReason', label: '申请事由', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'fundsSource', label: '款项来源', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'incomeAmount', label: '收入金额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'paymentMethod', label: '收款方式', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'receivingAccount', label: '收款账户+账号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'paymentDate', label: '收款日期', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'commissionAmount', label: '提成金额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'remarks', label: '备注', },
]
export default {
  name: "redEnvelopeIncome",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      disposition: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        commissionRatio: undefined,
        effectiveTime: null,
      },
      dispositionData: [],
      royaltiesVisible: false,
      timer: null,//定时器
      timing: null,//定时器
      submitterList: [],
      departmentList: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        deptId: null,
        ddUserIds: [],
      },
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    this.getdepartmentlist('')
    this.getprosimstatelist('', 1)
    await this.getList()
  },
  methods: {
    async onListDeletion(row) {
      this.$confirm('是否删除该条数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await delExtraIncome({ id: row.id })
        if (success) {
          this.$message.success('删除成功')
          this.getList()
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
      });
    },
    async configurationSaving() {
      if (this.disposition.commissionRatio > 100) {
        this.$message.error('提成比例不能大于100')
        return
      }
      const { success } = await saveExtraIncomeSet(this.disposition)
      if (success) {
        this.disposition.commissionRatio = undefined
        this.disposition.effectiveTime = null
        this.getList()
        this.$message.success('保存成功')
      } else {
        this.$message.error('保存失败')
      }
      this.royaltiesVisible = false
    },
    async configurationMethod() {
      const { data, success } = await getExtraIncomeSetPage(this.disposition)
      if (success) {
        this.dispositionData = data.list
      }
      this.disposition.commissionRatio = undefined
      this.disposition.effectiveTime = null
      this.royaltiesVisible = true
    },
    //所在部门为空,则初始化所在部门列表
    ondepartment(e) {
      if (!e) {
        this.ListInfo.deptId = null
        this.getdepartmentlist('')
      }
    },
    //模糊获取所在部门数据
    async getdepartmentlist(value) {
      clearTimeout(this.timing);
      this.timing = setTimeout(() => {
        this.departmentList = []
        getDeptViews({ deptName: value })
          .then(res => {
            if (res.success) {
              this.departmentList = res.data;
            }
          })
          .catch(err => {
          });
      }, 500);
    },
    //提交人为空,则初始化提交人列表
    oncorporation(e) {
      if (e.length == 0) {
        this.ListInfo.ddUserIds = []
        this.getprosimstatelist('', 1)
      }
    },
    //模糊获取提交人数据
    async getprosimstatelist(value, type) {
      if (!value && !type) {
        return
      }
      clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.submitterList = []
        getDDUserList({ userName: value })
          .then(res => {
            if (res.success) {
              this.submitterList = res.data;
            }
          })
          .catch(err => {
          });
      }, 500);
    },
    async exportProps() {
      this.loading = true
      const { data } = await exportExtraIncome(this.ListInfo)
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '红包收入数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      // 使用时将下面的方法替换成自己的接口
      const { data, success } = await getExtraIncomePage(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.loading = false
        this.tableData.forEach(item => {
          if (item.annexUrl) {
            item.annexUrl = item.annexUrl.split(',')
          }
        })
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 5px;
  }
}

::v-deep .custom-image img {
  max-width: 40px !important;
  max-height: 40px !important;
}

.circle-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: red;
  color: white;
  font-size: 12px;
  width: 13px;
  height: 13px;
  line-height: 13px;
  text-align: center;
  border-radius: 50%;
}

::v-deep .el-select__tags-text {
  max-width: 75px;
}
</style>
