<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="支付时间:">
            <el-date-picker style="width: 330px;" 
                v-model="filter.timerange"
                type="datetimerange"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                start-placeholder="开始"
                end-placeholder="结束"
           ></el-date-picker>
        </el-form-item>
         <el-form-item label="平台:">
          <el-select v-model="filter.platform" placeholder="请选择平台" style="width: 100%" @change='onchangeplatform'>
            <el-option 
              v-for="item in platformList"
              :key="item.value"
              :label="item.label"
              :value="item.value">             
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属店铺:">
          <el-select v-model="filter.shopCode" placeholder="请选择店铺" style="width: 100%" >
            <el-option label="所有" value></el-option>  
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"/>
          </el-select>
        </el-form-item>
        <el-form-item prop="parentIds" label="类目:">
          <el-cascader 
            v-model="filter.categoryids" :options="categorylist" :props="{ checkStrictly: false, value: 'id' }" clearable
            filterable  style="width:100%;"/>
        </el-form-item>
        <el-form-item label="快递公司:" >
          <el-select v-model="filter.expressCompany"  placeholder="请选择" style="width: 183px;">
            <el-option label="所有" value=""/>
            <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id"/>
          </el-select>
        </el-form-item>
        <el-form-item label="运营组:">
           <el-select v-model="filter.groupId"   placeholder="请选择运营组" style="width: 100%">
              <el-option label="所有" value=""/>
              <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key"/>
          </el-select>
        </el-form-item>
        <el-form-item label="采购员:">
           <el-select v-model="filter.brandId"   placeholder="请选择采购员" style="width: 100%">
              <el-option label="所有" value=""/>
              <el-option v-for="item in bandList" :key="item.key" :label="item.value" :value="item.key"/>
          </el-select>
        </el-form-item>
         <el-form-item label="宝贝ID:">
             <el-input v-model="filter.proCode" />
          </el-form-item>
          <el-form-item label="商品编码:">
             <el-input v-model="filter.proBianMa" />
          </el-form-item>
          <el-form-item label="订单来源:" >
            <el-select v-model="filter.orderSource"  placeholder="请选择" style="width: 150px;">
              <el-option label="所有" value/>
              <el-option label="合并" value="0"/>
              <el-option label="拆分" value="1"/>
              <el-option label="补发" value="2"/>
            </el-select>
          </el-form-item>
          <el-form-item label="发货仓:">
          <el-select v-model="filter.sendWarehouse"  placeholder="请选择" style="width: 150px;">
            <el-option label="所有" value/>
            <el-option v-for="item in sendWarehouseList" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>  
        <el-form-item label="宝贝数:" >
            <el-select v-model="filter.proCountType"  placeholder="请选择" style="width: 150px;">
              <el-option label="所有" value/>
              <el-option label="一单一品" value="1"/>
              <el-option label="一单多品" value="2"/>
            </el-select>
        </el-form-item>
        <el-form-item label="状态:">
          <el-select v-model="filter.status"  placeholder="请选择" style="width: 130px;">
            <el-option label="所有" value/>
            <el-option label="已发货" value="已发货"/>
            <el-option label="被拆分" value="被拆分"/>
            <el-option label="被合并" value="被合并"/>
            <el-option label="取消" value="取消"/>
            <el-option label="已客审待财审" value="已客审待财审"/>
          </el-select>
        </el-form-item> 
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <el-row>
            <el-alert
                title="温馨提示：选择不同的分组，可按日、周、月查看销量排行、利润排行。例如支付时间范围为5月，选择月、运营组，以销售件数倒序排序，即可查看5月运营组销量排行。"
                type="warning"
                show-icon
                :closable="false">
            </el-alert>
          </el-row>
     </el-form>
    </template>
     <!-- :summaryarry='summaryarry' -->
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='list'  :isSelection='false'
         :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" @cellclick='cellclick'>
       <template slot='extentbtn'>
          <el-button-group>
            <el-button type="text" size="medium" disabled>分组:</el-button>
            <el-button style="padding: 4px;margin: 0;">             
              <el-checkbox-group v-model="filter.groupList" @change="groupchange">
                <el-checkbox label="日" checked></el-checkbox>
                <el-checkbox label="周"></el-checkbox>
                <el-checkbox label="月"></el-checkbox>
                <el-checkbox label="平台"></el-checkbox>
                <el-checkbox label="店铺"></el-checkbox>
                <el-checkbox label="运营组"></el-checkbox>
                <el-checkbox label="采购组"></el-checkbox>
                <el-checkbox label="类目"></el-checkbox>
                <el-checkbox label="商品ID"></el-checkbox>
                <el-checkbox label="商品编码"></el-checkbox>
                <el-checkbox label="快递公司"></el-checkbox>
                <el-checkbox label="发货仓"></el-checkbox>
              </el-checkbox-group>
            </el-button>
            <!-- <el-button type="primary" @click="onshowdanalysis">查看图表</el-button> -->
            <el-button type="primary" @click="onExport">导出</el-button>
          </el-button-group>
        </template>
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
    </template>
    <el-dialog title="分析" :visible.sync="dialogVisible" width="80%">
      <div>
        <el-form class="ad-form-query" :inline="true" :model="filter1" @submit.native.prevent>
          <el-row>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="周期:" style="padding: 4px;margin: 0;">
                <el-radio-group v-model="filter1.groupper">
                  <el-radio label="0">日</el-radio>
                  <el-radio label="1">周</el-radio>
                  <el-radio label="2">月</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="16" :md="16" :lg="16" :xl="16">
              <el-form-item label="分组:" style="padding: 4px;margin: 0;">
                <el-radio-group v-model="filter1.groupbus">
                  <el-radio label="0">平台</el-radio>
                  <el-radio label="1">店铺</el-radio>
                  <el-radio label="2">运营组</el-radio>
                  <el-radio label="3">采购组</el-radio>
                  <el-radio label="4">类目</el-radio>
                  <el-radio label="5">商品ID</el-radio>
                  <el-radio label="6">商品编码</el-radio>
                  <el-radio label="7">快递公司</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="时间:">
                <el-date-picker style="width: 330px;" v-model="filter1.timerange" type="datetimerange"   
                    format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至"
                    start-placeholder="开始" end-placeholder="结束"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
              <el-form-item label="左边轴显示:">
                <el-select v-model="filter1.leftY" placeholder="请选择" class="el-select-content">
                    <el-option v-for="item in Ylist" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
              <el-form-item label="右边轴显示:">
                <el-select v-model="filter1.rightY" placeholder="请选择" class="el-select-content">
                    <el-option v-for="item in Ylist" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="4" :md="4" :lg="4" :xl="4">
                <el-button type="primary" @click="onSearchAnalysis">查询</el-button>
            </el-col>
          </el-row>         
      </el-form>
          <br>
      <div id="echartorder" style="width: 100%;height: 489px; box-sizing:border-box; line-height: 489px;">
      </div>
     </div>
    </el-dialog>    
    <el-dialog title="退货详情" :visible.sync="dialogVisibleReturnRate" width="15%">
      <div style="padding-left:5px;letter-spacing:2px;">
            <div>
              &emsp;&emsp;&emsp;买家数：<span class="linkspan" @click="redirectToDetail('return')">{{returnRateData.buyers||0}}</span>
            </div>
            <div>
              &emsp;&emsp;退款数量：<span class="linkspan" @click="redirectToDetail('return')">{{returnRateData.returnQty||0}}</span>
            </div>
            <div>
              &emsp;&emsp;退款金额：<span class="linkspan" @click="redirectToDetail('return')">{{returnRateData.returnAmount||0}}</span>
            </div>
            <div>
              &emsp;退款客单价：<span class="linkspan" @click="redirectToDetail('return')">{{returnRateData.buyerPrice||0}}</span>
            </div>
            <div>
              &emsp;售前退货率：<span class="linkspan" @click="redirectToDetail('return')">{{returnRateData.preSaleRate||0}}%</span>
            </div>
            <!-- <div>
              &emsp;售中退货率：<span class="linkspan" @click="redirectToDetail('return')">{{returnRateData.inSaleRate||0}}%</span>
            </div> -->
            <div>
              &emsp;售后退货率：<span class="linkspan" @click="redirectToDetail('return')">{{returnRateData.afterSaleRate||0}}%</span>
            </div>
            <div>
              售前退款数量：<span class="linkspan" @click="redirectToDetail('return')">{{returnRateData.preSaleCount||0}}</span>
            </div>
            <!-- <div>
              售中退款数量：<span class="linkspan" @click="redirectToDetail('return')">{{returnRateData.inSaleCount||0}}</span>
            </div> -->
            <div>
              售后退款数量：<span class="linkspan" @click="redirectToDetail('return')">{{returnRateData.afterSaleCount||0}}</span>
            </div>
            <div>
              售前退款金额：<span class="linkspan" @click="redirectToDetail('return')">{{returnRateData.preSaleAmount||0}}</span>
            </div>
            <!-- <div>
              售中退款金额：<span class="linkspan" @click="redirectToDetail('return')">{{returnRateData.inSaleAmount||0}}</span>
            </div> -->
            <div>
              售后退款金额：<span class="linkspan" @click="redirectToDetail('return')">{{returnRateData.afterSaleAmount||0}}</span>
            </div>
     </div>
    </el-dialog>
    <el-dialog title="快递单详情" :visible.sync="dialogVisibleExpNumber" width="15%">
      <div style="padding-left:30px;letter-spacing:2px;">
            <div>
              补发单数：<span class="linkspan" @click="redirectToDetail('reissued')">{{curRow.reissuedNumber||0}}</span>
            </div>
            <div>
              拆分单数：<span class="linkspan" @click="redirectToDetail('split')">{{curRow.splitNumber||0}}</span>
            </div>           
     </div>
    </el-dialog>
  </my-container>
</template>
<script>
import * as echarts from 'echarts'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import {getExpressComanyAll} from "@/api/express/express";
import { getDirectorList, getDirectorGroupList,getProductBrandPageList,getList as getshopList } from '@/api/operatemanage/base/shop'
import {getAllProBrand} from '@/api/inventory/warehouse'
import { getList as getcategorylist } from '@/api/operatemanage/base/category'
import {pageMonitorOrderGoods,monitorOrderGoodsAnalysis,queryOrderGoodsRefund,exportOrderAnalysis,queryOrdersSplit } from "@/api/order/ordergoods"
import {formatTime, formatPass,formatExpressCompany,formatPlatform,formatLink,formatSecondToHour,formatWarehouse,jsonToQueryString,formatSendWarehouse} from "@/utils/tools";
import { treeToList, listToTree } from '@/utils'
import { ruleSendWarehouse,rulePlatform} from "@/utils/formruletools";

const tableCols =[
      {istrue:true,display:true,prop:'payTime',label:'支付时间', width:'150',sortable:'custom',formatter:(row)=>formatTime(row.payTime,'YYYY-MM-DD HH:mm')},
      {istrue:true,display:false,prop:'timeStr',label:'周期', width:'120',sortable:'custom'},
      {istrue:true,display:true,prop:'platform',label:'平台', width:'80',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
      {istrue:true,display:true,prop:'shopName',label:'店铺', width:'150'},      
      {istrue:true,display:true,prop:'groupName',label:'运营组', width:'100'},
      {istrue:true,display:true,prop:'brandName',label:'采购组', width:'100'},
      {istrue:true,display:true,prop:'categoryName',label:'类目', width:'150'},
      {istrue:true,display:true,prop:'proCode',label:'商品ID', width:'120',sortable:'custom',type:'html',formatter:(row)=>formatLink(row.proCode,`https://detail.tmall.com/item.htm?id=${row.proCode}`)},
      {istrue:true,display:true,prop:'proName',label:'商品名称', width:'310',sortable:'custom'},
      {istrue:true,display:true,prop:'proBianMa',label:'商品编码', width:'120',sortable:'custom'},
      {istrue:true,display:true,prop:'expressCompany',label:'快递公司', width:'130',sortable:'custom',formatter:(row)=>formatExpressCompany(parseInt(row.expressCompany||0))},
      {istrue:true,display:true,prop:'sendWarehouse',label:'发货仓', width:'130',sortable:'custom',formatter:(row)=>formatSendWarehouse(parseInt(row.sendWarehouse||0))},
      {istrue:true,display:true,prop:'qty',label:'数量', width:'80',sortable:'custom'},
      {istrue:true,display:true,prop:'avgFreightMoneyByGroup',label:'平均快递费', width:'120',sortable:'custom'},
      // {istrue:true,display:true,prop:'avgFreightMoney',label:'平均快递费（快递单）', width:'120',sortable:'custom'},
      {istrue:true,display:true,prop:'avgTimeRang',label:'平均发货时效(小时)', width:'180',sortable:'custom',formatter:(row)=>formatSecondToHour(row.avgTimeRang)},       
      {istrue:true,display:true,prop:'profitRate',label:'毛利率(%)', width:'120',sortable:'custom'},
      {istrue:true,display:true,prop:'saleCount',label:'销售件数', width:'110',sortable:'custom'},
      {istrue:true,display:true,prop:'saleAmont',label:'销售金额', width:'110',sortable:'custom'},
      {istrue:true,display:true,prop:'returnRate',label:'退货率(%)',type:"canclick",width:'120',sortable:'custom'},
      {istrue:true,display:true,prop:'returnAmountRate',label:'退款率(%)',type:"canclick",width:'120',sortable:'custom'},
      {istrue:true,display:true,prop:'returnQty',label:'退款数量',width:'110',sortable:'custom'},
      {istrue:true,display:true,prop:'returnAmount',label:'退款金额',width:'110',sortable:'custom'},
      {istrue:true,display:true,prop:'expNumber',label:'快递单数',type:"canclick",width:'120',sortable:'custom'},
      {istrue:true,display:true,prop:'orderNumber',label:'订单数', width:'100',sortable:'custom'},
      {istrue:true,display:true,prop:'proBianMaNumber',label:'编码数', width:'100',sortable:'custom'},
      {istrue:true,display:true,prop:'buyers',label:'买家数', width:'120',sortable:'custom'},
      {istrue:true,display:true,prop:'buyerPrice',label:'客单价', width:'100',sortable:'custom'},
      {istrue:true,display:true,prop:'sumFreightByGroup',label:'总快递费', width:'120',sortable:'custom'},
      // {istrue:true,display:true,prop:'sumFreight',label:'总快递费（快递单）', width:'120',sortable:'custom'},
      {istrue:true,display:true,prop:'sumProfits',label:'总利润', width:'120',sortable:'custom'},
      //{istrue:true,display:true,prop:'analysis',label:' ',type:'button',width:'180px',btnList:[{label:"分析",handle:(that,row)=>that.onanalysis(row)}]}
     ];
const tableHandles1=[
        // {label:"导出", handle:(that)=>that.onExport()},
        // {label:'', handle:(that)=>that.startImport()},
        // {label:'导入运费月账单', handle:(that)=>that.startImportMonth()},
        // {label:'重新核算运费', handle:(that)=>that.startExamExpressFreight()},
        // {label:'核算其他收支', handle:(that)=>that.startExamExpressFreightExin()}
      ]; 
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow,cesTable },
  data() {
    return {
      that:this,
      filter: {
        startPayTime: null,
        endPayTime: null,
        platform: null,
        categoryId: null,
        categoryids:[],
        shopCode: "",
        timerange: null,
        groupId: null,
        brandId: null,
        proCode: null,
        proBianMa: null,
        groupList:[],
        groupByDay:false,
        groupByWeek:false,
        groupByMonth:false,
        groupByPlatform:false,
        groupByShop:false,
        groupByCategory:false,
        groupByGroup:false,
        groupByBrand:false,
        groupByProCode:false,
        groupByProBianMa:false,
        groupByExpressComany:false,
        groupBySendWarehouse:false,
        isGroupBy:true,
        orderSource:null,
        sendWarehouse:null,
        proCountType:null,
        status:""
      },
      filter1:{
        startPayTime: null,
        endPayTime: null,
        timerange:null,
        minY:null,
        maxY:null,
        leftY:0,
        rightY:1 ,
        groupper:"0",
        groupbus:"0",
        product:null,
        groupbusFirst:null,
      },
      Ylist:[
          {value:0,unit:"",label:"平均快递费"},
          {value:1,unit:"",label:"平均发货时效"},
          {value:2,unit:"",label:"毛利率"},
          {value:3,unit:"",label:"销售数量"},
          {value:4,unit:"",label:"销售金额"},
          {value:5,unit:"",label:"退货率"}
         ],
      directorList:[],
      directorGroupList:[],
      shopList:[],
      bandList:[],
      categorylist:[],
      pager:{OrderBy:"id",IsAsc:false},
      list: [],
      summaryarry:{difference_sum:10},
      total: 0,
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      userNameReadonly: true,
      dialogVisible: false,
      tableCols:tableCols,
      tableHandles:tableHandles1,
      expresscompanylist:[],
      sendWarehouseList:[],
      platformList:[],
      dialogVisibleReturnRate:false,
      dialogVisibleExpNumber:false,
      returnRateData:{
        buyers:null,
        returnQty:null,
        returnAmount:null,
        buyerPrice:null,
        preSaleCount:null,
        inSaleCount:null,
        afterSaleCount:null,
        preSaleAmount:null,
        inSaleAmount:null,
        afterSaleAmount:null,
        preSaleRate:null,
        inSaleRate:null,
        afterSaleRate:null
      },
      curRow:{},
      preGroupList:[],
    };
  },
  async mounted() {
     //await this.onSearch();
     await this.defaultDate();
     await this.getDirectorlist();
     await this.getExpressComanyList();
     await this.setPlatform();
     await this.groupchange(['日'])
     var whrule =await ruleSendWarehouse();
     this.sendWarehouseList=whrule.options;
  },
  methods: {
    async defaultDate(){
        let date = new Date()
        let year = date.getFullYear().toString()   //'2019'
        let month = date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1).toString():(date.getMonth()+1).toString()  //'04'
        let da = date.getDate() < 10 ? '0'+date.getDate().toString():date.getDate().toString()  //'12'
        let end = year + '-' + month + '-' + da + ' 23:59:59'  //当天'2019-04-12'
        let beg = year + '-' + month + '-01 00:00:00'    //当月第一天'2019-04-01'
        //this.filter.timerange = ['2021-05-01 00:00:00','2021-05-03 00:00:00']
        this.filter.timerange=[beg,end];
        //console.log('this.filter',this.filter)
    },
    async setPlatform(){
      var pfrule =await rulePlatform();
      this.platformList=pfrule.options;
    },
    async getcategorylist(platform) {
      const res = await getcategorylist({platform:platform })
      if (!res?.code) {
        return
      }
      const list=[];
      res.data.forEach(f=>{
         f.label=f.categoryName;
         list.push(f)
      })
      this.categorylist = listToTree(_.cloneDeep(list), {
        id: '',
        parentId: '',
        label: '所有'
      })
    },
  async getExpressComanyList() {
       const res = await getExpressComanyAll({});
      if (!res?.success) {
        return;
      } 
      const data = res.data;
      this.expresscompanylist = data;
    },
  async getDirectorlist() {
    // const res1 = await getDirectorList({})
      const res2 = await getDirectorGroupList({})
      const res3 = await getAllProBrand()      
     // this.directorList = res1.data
      this.directorGroupList = res2?.data
      this.bandList = res3?.data
    },
  async onSearch() {
      this.$refs.pager.setPage(1);
      this.getList();
    },
  getParams(ispager){
      if (!this.filter.timerange||this.filter.timerange.length<2){
        this.$message({message: "请先选择日期！",type: "warning",});
        return false;
      }
     if (this.filter.timerange) {
        this.filter.startPayTime = this.filter.timerange[0];
        this.filter.endPayTime = this.filter.timerange[1];
      }
      var params = {
          ... this.filter
      };
      if(ispager)
      {
        var pager = this.$refs.pager.getPager()
        var params = {
          ...pager,
          ...this.pager,
          ... this.filter
        };
      }

      params.categoryId=null;
      if(this.filter.categoryids){
        params.categoryId=this.filter.categoryids[this.filter.categoryids.length-1];
      }
      return params;
  },
  async getList(){
      const params=this.getParams(true);
      if(params===false){
        return false;
      }
      this.listLoading = true
      this.list=[]
      const res = await pageMonitorOrderGoods(params)
      this.listLoading = false
      if (!res?.code) return
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
  },
  async groupchange(arry){
     this.$refs.table.clearSort();
     this.pager={OrderBy:"id",IsAsc:false}
     this.filter.groupByDay=false;
     this.filter.groupByWeek=false;
     this.filter.groupByMonth=false;
     this.filter.groupByPlatform=false;
     this.filter.groupByShop=false;
     this.filter.groupByCategory=false;
     this.filter.groupByGroup=false;
     this.filter.groupByBrand=false;
     this.filter.groupByProCode=false;
     this.filter.groupByProBianMa=false;
     this.filter.groupByExpressComany=false;
     this.filter.groupBySendWarehouse=false;
   if (arry.length==0) 
      this.filter.isGroupBy=false;
   else{
    this.filter.isGroupBy=true; 
    //获取当前新选中的项
    var curSel="";
    for (const key in this.filter.groupList) {
      if (!Object.hasOwnProperty.call(this.preGroupList, key)) {
        curSel=this.filter.groupList[key];           
      }
    }
    //日 周 月单选
    var rdList=["日","月","周"];
    if(rdList.indexOf(curSel)>-1){
      for (const key in this.filter.groupList) {
        if (rdList.indexOf(this.filter.groupList[key])>-1) {
            if(this.filter.groupList[key]!=curSel){
              this.filter.groupList.splice(key,1);
            }
        }
      }
    }
    arry.forEach(f=>{
      if (f=="日") this.filter.groupByDay=true;
      else if (f=="周") this.filter.groupByWeek=true;
      else if (f=="月") this.filter.groupByMonth=true;
      else if (f=="平台") this.filter.groupByPlatform=true;
      else if (f=="店铺") this.filter.groupByShop=true;
      else if (f=="运营组") this.filter.groupByGroup=true;
      else if (f=="采购组") this.filter.groupByBrand=true;
      else if (f=="类目") this.filter.groupByCategory=true;
      else if (f=="商品ID") this.filter.groupByProCode=true;
      else if (f=="商品编码") this.filter.groupByProBianMa=true;
      else if (f=="快递公司") this.filter.groupByExpressComany=true;
      else if(f=="发货仓") this.filter.groupBySendWarehouse=true;
    })}
    console.info(this.filter)
    this.preGroupList= this.filter.groupList;
    await this.filtercols();
  },
  async filtercols(){
    this.tableCols.forEach(f=>{
         if (f.prop=='timeStr')  f.display=false
         else  f.display=true; 
       })
    if (this.filter.isGroupBy==true) {
       this.tableCols.forEach(f=>{
         f.display=false;
         if ((this.filter.groupByDay||this.filter.groupByWeek||this.filter.groupByMonth)&&f.prop=='timeStr') f.display=true 
         if (this.filter.groupByPlatform&&f.prop=='platform')  f.display=true 
         if (this.filter.groupByShop&&f.prop=='shopName')  f.display=true 
         if (this.filter.groupByGroup&&f.prop=='groupName')  f.display=true 
         if (this.filter.groupByBrand&&f.prop=='brandName')  f.display=true 
         if (this.filter.groupByCategory&&f.prop=='categoryName')  f.display=true 
         if (this.filter.groupByProBianMa&&f.prop=='proBianMa')  f.display=true 
         if (this.filter.groupByExpressComany&&f.prop=='expressCompany')  f.display=true
         if (this.filter.groupBySendWarehouse&&f.prop=='sendWarehouse') f.display=true;
         if (this.filter.groupByProCode) {
           if (f.prop=='proCode') f.display=true 
           else if (f.prop=='proName') f.display=true
          }
         //if (f.prop=='qty') f.display=true 
         if (f.prop=='avgFreightMoney') f.display=true 
         else if (f.prop=='avgTimeRang') f.display=true 
         else if (f.prop=='profitRate') f.display=true 
         else if (f.prop=='saleCount') f.display=true 
         else if (f.prop=='saleAmont') f.display=true 
         else if (f.prop=='returnRate') f.display=true
         else if (f.prop=='returnAmountRate') f.display=true;
         else if (f.prop=='returnQty') f.display=true;
         else if (f.prop=='returnAmount') f.display=true;
         else if (f.prop=='analysis') f.display=true
         else if (f.prop=='expNumber') f.display=true
         else if (f.prop=='orderNumber') f.display=true
         else if (f.prop=='proBianMaNumber') f.display=true
         else if (f.prop=='buyers') f.display=true
         else if (f.prop=='buyerPrice') f.display=true
         else if (f.prop=='sumFreight') f.display=true
         else if (f.prop=='sumProfits') f.display=true
         else if (f.prop=='avgFreightMoneyByGroup') f.display=true
         else if (f.prop=='sumFreightByGroup') f.display=true
       })}
    },
   async onchangeplatform(val){
      const res1 = await getshopList({platform:val,CurrentPage:1,PageSize:300});
      this.shopList=res1.data.list
      if (val) this.getcategorylist(val)
    },
   async onshowdanalysis() {
      this.dialogVisible=true;
      this.filter1.timerange=this.filter.timerange;
      this.filter1.startPayTime=this.filter.startPayTime
      this.filter1.endPayTime=this.filter.endPayTime
      this.filter1.product=null;
      this.filter1.groupbusFirst=null;
      await this.onSearchAnalysis()
    },
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
  async onSearchAnalysis(){      
      var parm={...this.filter, ...this.filter1};
      var hasparm=false;
      var arry= Object.keys(parm)
      if (arry.length==0)  return;
      for (let key of Object.keys(parm)) {
        if(parm[key])
            hasparm=true;
      }
      if(!hasparm) return;
      const res = await monitorOrderGoodsAnalysis(parm);
      if (!res?.code) return;
      var chartDom = document.getElementById('echartorder');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      if (!res.data) {
         this.$message({message: "没有数据!",type: "warning",});
         return;
       }
      var option =await this.Getoptions(res.data);
      option && myChart.setOption(option); 
    },
  async onanalysis(row){
      this.dialogVisible=true;
      this.filter1.timerange=this.filter.timerange;
      this.filter1.startPayTime=this.filter.startPayTime
      this.filter1.endPayTime=this.filter.endPayTime
      if (this.filter.groupByProBianMa){ this.filter1.product=row.proBianMa;this.filter1.groupbusFirst=6}
      else if (this.filter.groupByExpressComany){ this.filter1.product=row.expressCompany;this.filter1.groupbusFirst=7}
      else if (this.filter.groupByProCode){ this.filter1.product=row.proCode;this.filter1.groupbusFirst=5}
      else if (this.filter.groupByCategory){ this.filter1.product=row.categoryId;this.filter1.groupbusFirst=4}
      else if (this.filter.groupByBrand){this.filter1.product=row.brandId;this.filter1.groupbusFirst=3}
      else if (this.filter.groupByGroup) {this.filter1.product=row.groupId;this.filter1.groupbusFirst=2}
      else if (this.filter.groupByShop) {this.filter1.product=row.shopCode;this.filter1.groupbusFirst=1}
      else if (this.filter.groupByPlatform) { this.filter1.product=row.platform;this.filter1.groupbusFirst=0}
      else {this.filter1.product=row.proBianMa;this.filter1.groupbusFirst=6}
      console.log('this.filter1',this.filter1)      
      await this.onSearchAnalysis()
   },
  async Getoptions(element){
     var series=[]
     element.series.forEach(s=>{
       series.push({  smooth: true, ...s})
     })
    var yAxis=[]
    this.Ylist.forEach(s=>{
       if (s.value==this.filter1.leftY)
          yAxis.push({ type: 'value',name: s.label,axisLabel: {formatter: '{value}'+s.unit}})
     })
    this.Ylist.forEach(s=>{
       if (s.value==this.filter1.rightY)
          yAxis.push({ type: 'value',name: s.label,axisLabel: {formatter: '{value}'+s.unit}})
     })
     var option = {
        title: {text: element.title},
        tooltip: {trigger: 'axis'},
        legend: {
            formatter: function (name) {
              return echarts.format.truncateText(name, 120, '10px Microsoft Yahei', '...');
            },
            tooltip: { show: true  },
            data: element.legend
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {feature: {
            magicType: {show: true, type: ['line', 'bar']},
            //restore: {show: true},
        }},
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis: yAxis,
        series:  series
    };
    return option;
   },
   //单元格点击事件
   async cellclick(row, column, cell, event){
        var params=this.getParams(false);
        if(params===false){
          return false;
        }
        const param={
          ...params,
          groupTimeValue:row.timeStr,
          groupPlatformValue:row.platform,
          groupShopCodeValue:row.shopCode,
          groupCategoryIdValue:row.categoryId,
          groupGroupIdValue:row.groupId,
          groupBrandIdValue:row.brandId,
          groupProCodeValue:row.proCode,
          groupProBianMaValue:row.proBianMa,
          groupExpressComanyIdValue:row.expressCompany,
          groupBySendWarehouseValue:row.sendWarehouse
        };
        //清空数据
        this.returnRateData={
          buyers:null,
          returnQty:null,
          returnAmount:null,
          buyerPrice:null,
          preSaleCount:null,
          inSaleCount:null,
          afterSaleCount:null,
          preSaleAmount:null,
          inSaleAmount:null,
          afterSaleAmount:null,
          preSaleRate:null,
          inSaleRate:null,
          afterSaleRate:null
        };
        
     if (column.property=='returnRate' || column.property=='returnAmountRate') {       
        var loadingInstance = this.$loading({text:"加载中，请稍后",fullscreen:false});
        const res = await queryOrderGoodsRefund(param);
        this.dialogVisibleReturnRate=true;
        loadingInstance.close();
        if (!res?.code) return false;

        this.returnRateData=res.data;
     }
     if (column.property=='expNumber') {
        var loadingInstance = this.$loading({text:"加载中，请稍后",fullscreen:false});
        const res = await queryOrdersSplit(param);
        loadingInstance.close();
        this.dialogVisibleExpNumber=true;
        if (!res?.code) return false;
        row.splitNumber=res.data;
        this.curRow=row;//queryOrdersSplit
     }
   },
   redirectToDetail(typeName){//typeName 类型：reissued补发，split拆分，return退货
     var conditions={
          groupTimeValue:this.curRow.timeStr,
          groupPlatformValue:this.curRow.platform,
          groupShopCodeValue:this.curRow.shopCode,
          groupCategoryIdValue:this.curRow.categoryId,
          groupGroupIdValue:this.curRow.groupId,
          groupBrandIdValue:this.curRow.brandId,
          groupProCodeValue:this.curRow.proCode,
          groupProBianMaValue:this.curRow.proBianMa,
          groupExpressComanyIdValue:this.curRow.expressCompany,
          groupBySendWarehouseValue:this.curRow.sendWarehouse,
          rowStartPayTime:this.curRow.rowStartPayTime,
          rowEndPayTime:this.curRow.rowEndPayTime
     };
     var params=jsonToQueryString({typeName,...this.filter,...conditions});
     window.open("/order/orderrecord?"+params);

     this.dialogVisibleReturnRate=false;
     this.dialogVisibleExpNumber=false;
   },
   async onExport(){
        const params=this.getParams(true);
        if(params===false){
          return false;
        }
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportOrderAnalysis(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','订单分析_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click()
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
.linkspan{
  color:#409EFF;
  cursor: pointer;
}
</style>
