<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;">
            <el-date-picker v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 220px"
                 range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-select v-model="filter.warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 120px">
               <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </el-button>
           <el-button style="padding: 0;margin: 0;">
             <el-select v-model="filter.CompanyId" placeholder="快递公司" style="width: 120px">
              <el-option label="所有" value=""/>
              <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id"/>
            </el-select>
           </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter.ProductAxis" placeholder="分析对象">
                <!-- <el-option label="汇总" value="-1"></el-option> -->
                <el-option label="快递公司" value="0"></el-option>
                <el-option label="发货仓库" value="1"></el-option>
              </el-select>
           </el-button>
            <!-- <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter.TimeAxis" placeholder="时间周期" style="width: 100px">
                <el-option label="月" value="0"></el-option>
                <el-option label="周" value="1"></el-option>
                <el-option label="日" value="2"></el-option>
              </el-select>
           </el-button> -->
            <!-- <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter.YAxis" placeholder="Y轴值">
                <el-option label="账单费" value="0"></el-option>
                <el-option label="运费核算差额" value="1"></el-option>
                <el-option label="快递单数量" value="2"></el-option>
              </el-select>
           </el-button> -->
           <el-button type="primary" @click="onSearch">查询</el-button>
        </el-button-group>
    </template>
    <div>
      <!-- <el-alert title="温馨提示:运费核算差额=校验费用-实收费用" type="warning" show-icon :closable="false"></el-alert> -->
      <div id="yhecharts" style="width: 100%;height: 389px; box-sizing:border-box; line-height: 389px;"></div>
    </div>
    <template #footer>
    </template>
  </my-container>
</template>
<script>
import {warehouselist} from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import * as echarts from 'echarts/core';
import { DatasetComponent,TooltipComponent,GridComponent,LegendComponent,TitleComponent,ToolboxComponent,DataZoomComponent,
    VisualMapComponent,TimelineComponent,CalendarComponent } from 'echarts/components';
import {BarChart,PieChart} from 'echarts/charts';
import {CanvasRenderer} from 'echarts/renderers';
echarts.use(
    [PieChart ,DatasetComponent, TooltipComponent, GridComponent, LegendComponent, BarChart, CanvasRenderer,TitleComponent,
     ToolboxComponent,DataZoomComponent,VisualMapComponent,TimelineComponent,CalendarComponent]
);
import { getExpressComanyAll, expressFreightAnalysis} from "@/api/express/express";
const SPLIT_NUMBER = 4; // 分割段数
let myChart ={};
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow,cesTable },
  data() {
    return {
      filter: {
        timerange:[],
        StartTime: "",
        EndTime: "",
        Warehouse: null,
        CompanyId: null,
        ProductAxis: "0",
        TimeAxis:"2",
        YAxis:"0"
      },
      warehouselist:warehouselist,
      ExpressList: [],
      expresscompanylist:[],
      listLoading: false,
      pageLoading: false,
    };
  },
  async mounted() {
    await this.init()
  },
  methods: {
    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init(){
        var date1 = new Date(); date1.setDate(date1.getDate()-10);
        var date2 = new Date(); date2.setDate(date2.getDate()-1);
        this.filter.timerange=[];
        this.filter.timerange[0]=this.datetostr(date1);
        this.filter.timerange[1]=this.datetostr(date2);
        this.getExpressComanyList();
      },
    async getExpressComanyList() {
       const res = await getExpressComanyAll({});
      if (!res?.success)  return; 
      const data = res.data;
      this.expresscompanylist = data;
     },
    onSearch() {
      this.getExpressList(this);
    },
    async getExpressList(that) {
      const para = {};
      if (that.filter.timerange) {
        para.StartTime = that.filter.timerange[0];
        para.EndTime = that.filter.timerange[1];
      }
      if(!para.StartTime|| !para.EndTime){
        that.$message({type: "warning", message: "时间必选"});
        return;
      }
      const params = {...that.Filter,...para};
      const res = await expressFreightAnalysis(params);
      if (!res?.code) return;
      var chartDom = document.getElementById('yhecharts'); 
      myChart = echarts.init(chartDom);
      myChart.clear(); 
      var option = this.Getoptions(res.data);
      option && myChart.setOption(option);
    },
    Getoptions(element){
       var series=[]
       element.series.forEach(s=>{
         series.push({smooth: true, ...s})
       })
     var yAxis=[]
     element.yAxis.forEach(s=>{
        yAxis.push({type: 'value',minInterval:10,offset:s.offset,splitLine:s.splitLine,position:s.position,name: s.name,axisLabel: {formatter: '{value}'+s.unit}})
     })
     var selectedLegend={};
     if(element.selectedLegend){
       element.legend.forEach(f=>{
          if(!element.selectedLegend.includes(f)) selectedLegend[f]=false
        })
     }
      var option = {
        title: {text: element.title},
        tooltip: {trigger: 'axis'},
        legend: {
           selected:selectedLegend,
           data: element.legend
         },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {feature: {
            magicType: {show: true, type: ['line', 'bar']},
        }},
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis:  yAxis,
        series:  series
    };

    //  option.yAxis.forEach((y, index) => {
    //   const { min, max} = this.getMaxValue(index,option.series);
    //       y.type='value'
    //       y.max= value => {
    //           const { max, min } = value;
    //           const absMax = Math.max(Math.abs(max), Math.abs(min));
    //           return Math.floor(absMax * 1.0);
    //       }
    //       y.min= min
    //   });

    // option.yAxis.forEach((y, index) => {
    //      y.type='value'
    //      y.max= value => {
    //           const { max, min } = value;
    //           const absMax = Math.max(Math.abs(max), Math.abs(min));
    //           return Math.floor(absMax * 1.0);
    //       }
    //     //  y.min= value => {
    //     //       const { max, min } = value;
    //     //       const absMax = Math.max(Math.abs(max), Math.abs(min));
    //     //       return Math.floor(-absMax * 1.0);
    //     //  }
    // });


    // option.yAxis.forEach((y, index) => {
    //     // leftMax, rightMax的值，请自行根据自身数据计算，即：默认配置下的最大值
    //      const { leftMax, rightMax} = this.getMaxValue(index,option.series);
    //     // index === 0时是左轴，1是右轴
    //     let max = !index ? rightMax + '' : leftMax + '';
    //     max = value === 'null' ? 0 : this.computeMax(max);
    //     y.splitNumber = SPLIT_NUMBER;
    //     y.interval = max / SPLIT_NUMBER;
    //     y.max = max;
    //   });

    return option;
   },
  computeMax(value) {
    if (Math.floor(value) === 0) {  // 是小数
      let { result, digit } = this.getNumberFromDecimal(Number(value));
      result = result.substr(0, 2);    // 取前两位
      return Number(Math.ceil(value / SPLIT_NUMBER) * SPLIT_NUMBER) / Math.pow(10, digit - 1 + 2);
    } else {
      const roundValue = Math.floor(value).toString();
      if (roundValue.length > 2) { // 两位以上
        const topTwo = roundValue.substr(0, 2);
        const len = roundValue.length;
        return Number(Math.ceil(topTwo / SPLIT_NUMBER) * SPLIT_NUMBER) * Math.pow(10, len - 2);
      } else {
        return Number(Math.ceil(value / SPLIT_NUMBER) * SPLIT_NUMBER);
      }
    }
  },
  getNumberFromDecimal(decimal) {
    let str = decimal.toString();
    if (typeof decimal !== 'number' || !str.includes('.')) {
      throw '参数不是小数';
    }
    if (decimal >= 1) {
      throw '参数大于等于1';
    }
    for (let i = 0; i < str.length; i ++) {
      if (str[i] !== '0' && str[i] !== '.') {
        return { result: str.substring(i, str.length), digit: i - 2 };
      }
    }
  },
  getMaxValue(index,series){
    //debugger
    let min=0;let max=0;
    var data = series.filter(function(item){ return item.yAxisIndex==index; });
    data.forEach(f=>{
     var _min=  Math.min.apply(null, f.data)
     var _max=  Math.max.apply(null, f.data)
     if(_min<min) min=_min;
     if(_max>max) max=_max;
    })
    return {min,max}
   }
  },
};
</script>

<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
