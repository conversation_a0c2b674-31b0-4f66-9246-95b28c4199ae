<template>
    <div>
      <!--顶部操作-->
      <el-form class="ad-form-query" :inline="true" @submit.native.prevent>
        <el-form-item label="商品编码:"> 
          <el-input v-model="filter1.goodsCode" style="width: 150px" placeholder="商品编码" maxlength="40" clearable></el-input>
        </el-form-item>
        <el-form-item label="操作人:" label-position="right" label-width="72px">
          <el-input v-model="filter1.createdUserName" style="width: 150px" placeholder="操作人" maxlength="20" clearable></el-input> 
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>

    <div style="height:480px;">
      <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' 
              :hasexpand='false' @sortchange='sortchange' :tableData='list' :isSelection='false'
         :tableCols='tableCols' :loading="listLoading" style="height:440px;">
      <el-table-column type="expand">
        <template slot-scope="props">
        <div>
          <el-table :data="props.row.detaildata" style="width: 100%">
            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
            </el-table-column>
          </el-table>
        </div>
      </template>
      </el-table-column>
    </ces-table>    
    </div>

    <div>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getlist"
      />
    </div>
    </div>
</template>

<script>
import { formatTime } from "@/utils";
import { queryPurchaseOrderLogByIndexNoAsync} from '@/api/inventory/purchase'
import myContainer from "@/components/my-container";
import cesTable from "@/components/Table/table.vue";
const tableCols =[
      //{istrue:true,prop:'id',label:'编号', width:'200',sortable:'custom'},
      {istrue:true,prop:'logType',label:'主题', width:'150'},
      {istrue:true,prop:'logInfo',label:'备注'},
      {istrue:true,prop:'createdUserName',label:'操作人', width:'100'},
      {istrue:true,prop:'createTime',label:'操作时间', width:'160',sortable:'custom',formatter:(row)=>formatTime(row.createTime,'YYYY-MM-DD HH:mm:ss')} 
     ]; 

export default {
  name: 'PurchaseOrderLog',
  components: {cesTable, myContainer },
  props:{
       filter:{}
  },
  data() {
    return {
      that:this,
      filter1: {
        createdUserName:null,
        goodsCode:null
      },
      list: [],
      tableCols:tableCols,
      pager:{OrderBy:"createTime",IsAsc:false},
      total:0,
      sels: [],
      selids: [],  
      pageLoading: false,
      listLoading: false
    }
  },
  mounted() {
     this.onSearch()
  },
  methods: {
    async onFirstSearch(){
      this.filter1.createdUserName=null;
      this.filter1.goodsCode=null;
      await this.onSearch()
    },
    async onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() { 
      if (!this.pager.OrderBy){
        this.pager.OrderBy="createTime";
        this.pager.IsAsc=false;
      }
      let pager = this.$refs.pager.getPager()
      const params = {
        ...pager,
        ...this.pager,
        ... this.filter,
        ... this.filter1
      }
      this.listLoading = true
      let res = await queryPurchaseOrderLogByIndexNoAsync(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total 
      this.list = res.data.list 
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    }
  }
}
</script>

