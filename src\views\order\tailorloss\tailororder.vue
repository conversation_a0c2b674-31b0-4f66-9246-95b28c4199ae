<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="发货时间：">
                    <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始发货时间" end-placeholder="结束发货时间"
                        :picker-options="pickerOptions">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.goodsCode" style="width: 140px" placeholder="成品编码"
                        @keyup.enter.native="onSearch" clearable maxlength="40" />
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.goodsCodeHalf" style="width: 140px" placeholder="半成品编码"
                        @keyup.enter.native="onSearch" clearable maxlength="40" />
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.picker" style="width: 140px" placeholder="拣货补入人"
                        @keyup.enter.native="onSearch" clearable maxlength="50" />
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.orderNoInner" style="width: 140px" placeholder="内部单号"
                        @keyup.enter.native="onSearch" clearable maxlength="50" />
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.orderRemarkStatus" clearable filterable placeholder="备注状态" style="width: 100px">
                        <el-option key="1" label="格式正确" value="1" />
                        <el-option key="2" label="格式错误" value="2" />
                        <el-option key="3" label="未备注" value="3" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.orderRemark" style="width: 200px" placeholder="备注"
                        @keyup.enter.native="onSearch" clearable maxlength="200" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
                :loading="listLoading" :tableHandles="tableHandles1">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
        <el-dialog title="导入" :visible.sync="dialogVisibleUpload" width="40%" v-dialogDrag :close-on-click-modal="false">
            <span>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                            accept=".xlsx" :http-request="onUploadFile" :on-change="uploadChange" :on-remove="uploadRemove"
                            :on-success="onUploadSuccess">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleUpload = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
            v-dialogDrag>
            <orderLogPage ref="orderLogPage" :orderNoInner="sendOrderNoInner" style="z-index:10000;height:600px" />
        </el-dialog>

        <el-dialog title="编辑订单备注" v-if="dialogOrderRemarkVisible" :visible.sync="dialogOrderRemarkVisible" width="60%"
            v-dialogDrag :close-on-click-modal="false">
            <span>
                <div style="width: 100%">
                    <el-input v-model.trim="dialogOrderRemark" style="width: 100%" placeholder="备注" clearable
                        maxlength="1000" />
                </div>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="onOrderRemarkSave" :loading="onOrderRemarkSaveLoading">保存&重算面积</el-button>
                <el-button @click="dialogOrderRemarkVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getTailorOrderPageList, importTailorOrder, updateOrderRemark } from '@/api/order/tailorloss';
import orderLogPage from "@/views/order/logisticsWarning/orderLogPage.vue";
const tableCols = [
    { istrue: true, prop: 'goodsCode', label: '成品编码', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'goodsCodeHalf', label: '半成品编码', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'goodsName', label: '成品名称', width: '260' },
    { istrue: true, prop: 'orderNoInner', label: '内部订单号', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showLogDetail(row) },
    { istrue: true, prop: 'payTime', label: '支付时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'deliveryTime', label: '发货时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'pickTime', label: '拣货补入时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'picker', label: '拣货补入人', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'goodsCount', label: '商品数量', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'spec', label: '商品规格(长*宽)m', width: '130', sortable: 'custom' },
    { istrue: true, prop: 'orderRemarkSpec', label: '客服自定义规格(长*宽)m', width: '170', sortable: 'custom' },
    { istrue: true, prop: 'saleTotalArea', label: '售出面积㎡', width: '100', sortable: 'custom' },
    {
        istrue: true, prop: 'orderRemarkStatus', label: '备注状态', width: '100', sortable: 'custom',
        formatter: (row) => row.orderRemarkStatus == 1 ? "格式正确" : row.orderRemarkStatus == 2 ? "格式错误" : row.orderRemarkStatus == 3 ? "未备注" : ""
    },
    { istrue: true, prop: 'orderRemark', label: '订单备注', width: '1000', sortable: 'custom' },
    {
        istrue: true, type: 'button', label: '操作', width: '140', fixed: 'right',
        btnList: [
            {
                label: "编辑订单备注", handle: (that, row) => that.onOrderRemark(row)
            },
        ]
    }
]
const tableHandles1 = [
    { label: "导入", handle: (that) => that.onImport() },
];
export default {
    name: 'tailororder',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, orderLogPage },
    props: {

    },
    data() {
        return {
            that: this,
            filter: {
                orderNoInner: null,
                operator: null,
                remark: null,
                logName: null,
                timerange: [
                    formatTime(dayjs().subtract(1, "month"), "YYYY-MM-DD"),
                    formatTime(new Date(), "YYYY-MM-DD"),
                ],
                startDate: null,
                endDate: null,
                orderRemarkStatus:null,
                orderRemark:null,
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "deliveryTime", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            pickerOptions: {
                shortcuts: [
                    {
                        text: '昨天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime());
                            picker.$emit('pick', [start, start]);
                        }
                    }, {
                        text: '近三天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime());
                            const end = new Date(new Date(tdate.toLocaleDateString()));
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一周',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 5);
                            const end = new Date(new Date(tdate.toLocaleDateString()).getTime() + 3600 * 1000 * 24 * 5);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一个月',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 31);
                            console.log("获取前一个月的时间", tdate.getDay());
                            const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
            },
            tableHandles1: tableHandles1,
            dialogVisibleUpload: false,
            fileList: [],
            fileparm: {},
            uploadLoading: false,
            dialogHisVisible: false,
            sendOrderNoInner: "",

            dialogOrderRemarkVisible: false,
            dialogOrderOrderNoInner: 0,
            dialogOrderGoodsCode: "",
            dialogOrderRemark: "",
            onOrderRemarkSaveLoading: false,
        };
    },
    async mounted() {
        await this.onSearch()
    },
    methods: {
        //查询第一页
        async onSearch() {
            console.log(this.filter)
            if (!this.filter.timerange) {
                this.$message({ message: "请选择发货时间", type: "warning", });
                return;
            }
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.$message({ message: "请先选择时间", type: "warning" });
                return false;
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const res = await getTailorOrderPageList(params)
            this.listLoading = false;
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            data.forEach(d => {
                d._loading = false;
            })
            this.list = data;
        },
        //排序查询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onImport() {
            this.dialogVisibleUpload = true;
        },
        async uploadChange(file, fileList) {
            var list = [];
            if (fileList && fileList.length > 0) {
                for (var i = 0; i < fileList.length; i++) {
                    if (fileList[i].status == "success")
                        list.push(fileList[i]);
                    else
                        list.push(fileList[i].raw);
                }
            }
            this.fileList = list;
        },
        uploadRemove(file, fileList) {
            this.uploadChange(file, fileList);
        },
        async onUploadFile(item) {
            debugger
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            var res = await importTailorOrder(form);
            if (res?.success)
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
            this.uploadLoading = false
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.dialogVisibleUpload = false;
        },
        onSubmitUpload() {
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请先选取文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        showLogDetail(row) {
            this.dialogHisVisible = true;
            this.sendOrderNoInner = row.orderNoInner;
        },
        async onOrderRemark(row) {
            this.dialogOrderOrderNoInner = row.orderNoInner;
            this.dialogOrderGoodsCode = row.goodsCode;
            this.dialogOrderRemark = row.orderRemark;
            this.dialogOrderRemarkVisible = true;
        },
        async onOrderRemarkSave() {
            this.$confirm('确定要执行保存并重算面积吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                this.onOrderRemarkSaveLoading = true;
                var res = await updateOrderRemark({
                    orderNoInner: this.dialogOrderOrderNoInner,
                    goodsCode: this.dialogOrderGoodsCode,
                    newOrderRemark: this.dialogOrderRemark
                });
                this.onOrderRemarkSaveLoading = false;
                if (res?.success) {
                    this.$message({ message: "保存&重算面积成功", type: "success" });
                    this.dialogOrderRemarkVisible = false;
                    await this.onSearch();
                }
            }).catch(() => { });
        },
    },
};
</script>

<style lang="scss" scoped></style>
