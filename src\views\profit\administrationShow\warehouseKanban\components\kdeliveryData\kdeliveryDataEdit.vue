<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="180px" class="demo-ruleForm">
        <div style="font-size: 15px; font-weight: 600; margin-left: 20px">发货数据</div>

        <el-form-item label="月度日平均在职人数：" prop="monthlyAvgDailyInService">
          <inputNumberYh v-model="ruleForm.monthlyAvgDailyInService" :placeholder="'月度日平均在职人数'" class="publicCss" />
        </el-form-item>

        <el-form-item label="月度日平均出勤人数：" prop="monthlyAvgDailyAttendance">
          <inputNumberYh v-model="ruleForm.monthlyAvgDailyAttendance" :placeholder="'月度日平均出勤人数'" class="publicCss" />
        </el-form-item>

        <el-form-item label="月度日均发货量：" prop="monthlyAvgDailyShipmentVolume">
          <inputNumberYh v-model="ruleForm.monthlyAvgDailyShipmentVolume" :placeholder="'月度日均发货量'" class="publicCss" />
        </el-form-item>

        <el-form-item label="内仓人均发货：" prop="innerWarehousePerCapitaShipment">
          <inputNumberYh v-model="ruleForm.innerWarehousePerCapitaShipment" :placeholder="'内仓人均发货'" class="publicCss" />
        </el-form-item>

        <el-form-item label="月均日发货涨幅率：" prop="monthlyAvgDailyShipmentGrowthRate">
          <div style="display: flex; align-items: center;">
            <inputNumberYh v-model="ruleForm.monthlyAvgDailyShipmentGrowthRate" :placeholder="'月均日发货涨幅率'"
              class="publicCss" />
            <span style="margin-left: 8px; font-size: 14px;">%</span>
          </div>
        </el-form-item>

      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { warehouseShipmentDataSubmit } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission'
export default {
  name: 'kdeliveryDataEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      ruleForm: {
        monthlyAvgDailyInService: null,
        monthlyAvgDailyAttendance: null,
        monthlyAvgDailyShipmentVolume: null,
        innerWarehousePerCapitaShipment: null,
        monthlyAvgDailyShipmentGrowthRate: null
      },
      rules: {
        monthlyAvgDailyInService: [
          { required: true, message: '请输入月度日平均在职人数', trigger: 'blur' },
          { type: 'integer', min: 0, message: '请输入有效的整数', trigger: 'blur' }
        ],
        monthlyAvgDailyAttendance: [
          { required: true, message: '请输入月度日平均出勤人数', trigger: 'blur' },
          { type: 'integer', min: 0, message: '请输入有效的整数', trigger: 'blur' }
        ],
        monthlyAvgDailyShipmentVolume: [
          { required: true, message: '请输入月度日均发货量', trigger: 'blur' },
          { type: 'integer', min: 0, message: '请输入有效的整数', trigger: 'blur' }
        ],
        innerWarehousePerCapitaShipment: [
          { required: true, message: '请输入内仓人均发货', trigger: 'blur' },
          { type: 'integer', min: 0, message: '请输入有效的整数', trigger: 'blur' }
        ],
        monthlyAvgDailyShipmentGrowthRate: [
          { required: true, message: '请输入月均日发货涨幅率', trigger: 'blur' },
          { type: 'number', min: 0, max: 100, message: '请输入0-100之间的有效百分比', trigger: 'blur' }
        ]
      }
    }
  },
  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    console.log(this.editInfo, 'editInfo');
    this.ruleForm = { ...this.editInfo };
  },
  methods: {
    cancellationMethod() {
      this.$emit('cancellationMethod');
    },
    async submitForm(formName) {
      try {
        const valid = await this.$refs[formName].validate();
        if (valid) {
          this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing");
          const { success, msg } = await warehouseShipmentDataSubmit(this.ruleForm);

          if (success) {
            this.$message.success(msg || '保存成功');
            this.$emit("search");
          } else {
            this.$message.error(msg || '保存失败');
          }
        }
      } catch (error) {
        console.error('表单提交失败:', error);
        this.$message.error('保存失败，请重试');
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    }
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}
</style>
