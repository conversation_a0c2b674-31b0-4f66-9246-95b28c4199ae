<template>
  <div style="height: auto;">
       <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
               @select='selectchange' :isSelection='true'  @cellclick='cellclick'
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='false' 
              :loading="listLoading">
         <template slot='extentbtn'>
          <el-button-group>
            <el-button type="text" size="medium" disabled>指标:</el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter1.Y" placeholder="请选择指标">
                  <el-option v-for="item in Ylist" :key="item.value" :label="item.label" :value="item.value"/>
             </el-select>
            </el-button>
            <el-button type="text" size="medium" disabled>关键词:</el-button>
            <el-button style="padding: 0;margin: 0;">
                 <el-input v-model="filter1.keyWords" placeholder="输入关键词，多个关键词用,隔开" style="width:500px"/>
            </el-button>
            <el-button type="primary" @click="onfresh">刷新</el-button>
            <el-button type="primary" @click="onpk">PK</el-button>
            <el-button type="primary" @click="onshowdrawer(20)">查看分析图表</el-button>
          </el-button-group>
        </template>
     </ces-table>
    <el-dialog title="分析" :visible.sync="showdrawer" width="80%">
       <template slot='title'>
         <div>
           <span>分析</span>
           <span style="right: 10px;float: right;margin: 20px;">
            <el-button-group>
              <el-button type="primary" @click="onshowdrawer(10)">前10</el-button>
              <el-button type="primary" @click="onshowdrawer(20)">前20</el-button>
           </el-button-group>
           </span>
         </div>
       </template>
      <div>
        <div id="echartmonitAllrank" style="width: 100%;height: 650px; box-sizing:border-box; line-height: 650px;"/>
      </div>
    </el-dialog>

    <el-dialog title="初期占比分析" :visible.sync="dialogVisible">
      <div>
        <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="日期:">
            <el-date-picker v-model="filter2.timerange" type="datetimerange"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" 
                start-placeholder="开始日期" end-placeholder="结束日期"
           ></el-date-picker>
        </el-form-item>
        <el-form-item label="商品ID:">
          <el-input v-model="filter2.proCode" :disabled="true"/>
        </el-form-item>
        <el-form-item label="指标:">
          <el-select v-model="filter2.Y" placeholder="请选择指标">
              <el-option v-for="item in Ylist" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearchAnalysis">查询</el-button>
        </el-form-item>
      </el-form>
          <br>
        <div id="echartmonitrank" style="width: 100%;height: 389px; box-sizing:border-box; line-height: 389px;">     
      </div>
     </div>
    </el-dialog>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import {rankListProductMonitor,rankListProductMonitorAnalysis,exportProductMonitor,rankListProductAllMonitorAnaly} from '@/api/operatemanage/operate'
import MyContainer from '@/components/my-container/nofooter'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import {formatUpNumber,formatTimerang,formatLink,formatNoLink,formatmoney} from "@/utils/tools"; 
const tableCols =[
      {istrue:true,prop:'upnumber',label:'排名变动', width:'60',sortable:'custom',type:'html', formatter:(row)=>formatUpNumber(row.upnumber)},
      {istrue:true,prop:'number',label:'排名', width:'60',sortable:'custom'},
      {istrue:true,prop:'shopName',label:'店铺名', width:'150'},
      {istrue:true,prop:'proCode',label:'商品ID', width:'130',type:'html', formatter:(row)=>formatLink(row.proCode,`https://detail.tmall.com/item.htm?id=${row.proCode}`)},
      {istrue:true,prop:'proName',label:'商品信息', width:'400'},
      //{istrue:true,prop:'yValue',label:'指标值', width:'110'},
      //{istrue:true,prop:'startTime',label:'日期', width:'200', formatter:(row)=>formatTimerang(row.startTime,row.endTime,'YYYY-MM-DD')},
      {istrue:true,prop:'totalValue',label:'总值', width:'100',sortable:'custom',formatter:(row)=>formatmoney(row.totalValue)},
      {istrue:true,prop:'secondValue',label:'近期值', width:'100', sortable:'custom',formatter:(row)=>formatmoney(row.secondValue)},
      {istrue:true,prop:'firstValue',label:'初期值', width:'100', sortable:'custom',formatter:(row)=>formatmoney(row.firstValue)},
      {istrue:true,prop:'diff',label:'近期-初期', width:'110', sortable:'custom',formatter:(row)=>formatmoney(row.diff)},
      {istrue:true,prop:'diffRate',label:'初期占比%', width:'110',sortable:'custom',type:'html',formatter:(row)=>formatNoLink(row.diffRate)},
      //{istrue:true,prop:'diffRate',label:'初期占比%', width:'110',formatter:(row)=>formatmoney(row.diffRate)},
     ];
const tableHandles=[
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
   props:{
       filter: { }
     },
  data() {
    return {
      that:this,
      showdrawer:false,
      list: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      total:0,
      sels: [],
      selids: [],
      filter1:{
        Y:0,
        keyWords:""
      },
      filter2:{
        Y:0,
        proCode:'',
        startDate:null,
        endDate:null,
        timerange:"",
      },
      Ylist:[
          {value:0,unit:"",label:"交易金额"},
          {value:1,unit:"",label:"访客人数"},
          {value:2,unit:"",label:"搜索人数"},
          {value:3,unit:"%",label:"搜索占比"},
          {value:4,unit:"%",label:"支付人数"},
          {value:5,unit:"%",label:"支付转化率"},
          {value:6,unit:"",label:"收藏人数"},
          {value:7,unit:"%",label:"收藏率"},
          {value:8,unit:"",label:"加购人数"},
          {value:9,unit:"%",label:"加购率"},
          {value:10,unit:"",label:"客单价"},
          {value:11,unit:"",label:"uv价值"}
         ],
      listLoading: false,
      pageLoading: false,
      dialogVisible:false,
      pager:{orderBy:"id",isAsc:false},
    }
  },
  mounted() {
     this.onSearch()
  },
  beforeUpdate() { },
  methods: {
    onSearch() {
      this.getlist()
    },
   async onfresh() {
    //  if (!this.filter1.Y) {
    //     this.$message({message: "请先选择指标！",type: "warning",});
    //     return;
    //   }
      this.getlist()
    },
   async getlist() {
      // if (!this.filter1.Y) return; 
      var params={...this.filter, ...this.filter1,...this.pager};
      var hasparm=false;
      var arry= Object.keys(params)
      if (arry.length==0)  return;

      for (let key of Object.keys(params)) {
        if(params[key])
            hasparm=true;
      }
      if(!hasparm) return;
     
      this.listLoading = true
      const res = await rankListProductMonitor(params)
      this.listLoading = false
      if (!res?.code) {
        return
      }
      if (!res?.data) return
      this.total = res.data.total
      const data = res.data
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
  async onSearchAnalysis(params){
     if (this.filter2.timerange) {
        this.filter2.startDate = this.filter2.timerange[0];
        this.filter2.endDate = this.filter2.timerange[1];
      }
      var parm={...this.filter2};
      if (!parm.startDate) return;
      if (!parm.endDate) return;
      var hasparm=false;
      var arry= Object.keys(parm)
      if (arry.length==0)  return;
      for (let key of Object.keys(parm)) {
        if(parm[key])
            hasparm=true;
      }
      if(!hasparm) return;
      const res = await rankListProductMonitorAnalysis(parm);      
      if (!res?.code)  return;       
      var chartDom = document.getElementById('echartmonitrank');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      if (!res.data) {
         this.$message({message: "没有数据!",type: "warning",});
         return;
       }
      var option =await this.Getoptions(res.data);
      //console.log(JSON.stringify(option))
      option && myChart.setOption(option); 
    },
  async initAllAnalysis(params){
      const res = await rankListProductAllMonitorAnaly(params);      
      if (!res?.code)  return;       
      var chartDomall = document.getElementById('echartmonitAllrank');
      var myChartall = echarts.init(chartDomall);
      myChartall.clear();
      if (!res.data) {
         this.$message({message: "没有数据!",type: "warning",});
         return;
       }
      var option =await this.Getoptions(res.data);
      //console.log(JSON.stringify(option))
      option && myChartall.setOption(option); 
    },
   async onpk(){
    if (this.selids.length==0) {
        this.$message({message: "请先选择竞品！",type: "warning",});
        return;
      }
     this.$emit('onpktype1',this.selids);
    },
   async onshowdrawer(rank){
       this.showdrawer=true;
      var params={...this.filter, ...this.filter1};

      var hasparm=false;
      var arry= Object.keys(params)
      if (arry.length==0)  return;

      for (let key of Object.keys(params)) {
        if(params[key])
            hasparm=true;
      }
      if(!hasparm) return;
       params.rank=rank;
       this.initAllAnalysis(params);
   },
  async cellclick(row, column, cell, event){
     if (column.property=='diffRate') {
       this.dialogVisible=true;
       this.filter2.proCode=row.proCode;
       this.filter2.timerange = this.filter.timerange
       await this.onSearchAnalysis();
     }
    },
  async Getoptions(element){
     var series=[]
     element.series.forEach(s=>{
       series.push({  smooth: true, ...s})
     })
     var yAxis=[]
     yAxis.push({ type: 'value',name: '(近期-初期)',axisLabel: {formatter: '{value}'},
         max: function(value) {
            if(Math.abs(value.max) > Math.abs(value.min))return (Math.abs(value.max)*1.2).toFixed(2);
            else return (Math.abs(value.min)*1.2).toFixed(2); 
        },
         min: function(value) {
            if(Math.abs(value.max) > Math.abs(value.min))return (-Math.abs(value.max) * 1.2).toFixed(2);
            else return (-Math.abs(value.min) * 1.2).toFixed(2);
        }})
     yAxis.push({ type: 'value',name: '(初期占比)',axisLabel: {formatter: '{value}'},
         max: function(value) {
            if(Math.abs(value.max) > Math.abs(value.min)) return (Math.abs(value.max)*1.2).toFixed(2);
            else return (Math.abs(value.min)*1.2).toFixed(2);
        },
         min: function(value) {
            if(Math.abs(value.max) > Math.abs(value.min)) return (-Math.abs(value.max) * 1.2).toFixed(2);
            else return (-Math.abs(value.min) * 1.2).toFixed(2);
        }})
     var option = {
        title: {text: element.title},
        tooltip: {trigger: 'axis'},
        legend: {
            formatter: function (name) {
              return echarts.format.truncateText(name, 120, '10px Microsoft Yahei', '...');
            },
            tooltip: { show: true  },
            data: element.legend
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {feature: {
            magicType: {show: true, type: ['line', 'bar']},
            //restore: {show: true},
        }},
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis: yAxis,
        series:  series
    };
    //var maxIntervalAmount=getMaxAndInterval(resp.data.map(function(r) {return r.total_amount}));
	//var maxIntervalVol=getMaxAndInterval(resp.data.map(function(r) {return r.total_vol})); 
    // option.yAxis[0].max=Math.ceil(Math.max.apply(null,option.series[0].data)/5)*5;
    //option.yAxis[0].interval=Math.ceil(Math.max.apply(null,option.series[0].data)/5);
  //  option.yAxis[1].max=Math.ceil(Math.max.apply(null,option.series[1].data)/5)*5;
    //option.yAxis[1].interval=Math.ceil(Math.max.apply(null,option.series[1].data)/5);
   // option.yAxis[0].min=Math.ceil(Math.min.apply(null,option.series[0].data));
   // option.yAxis[1].min=Math.ceil(Math.min.apply(null,option.series[1].data));
    return option;
   },
  maxfunction(value) {
        if(Math.abs(value.max) > Math.abs(value.min)){
            return (Math.abs(value.max)*1.2).toFixed(2);
        }else{
            return (Math.abs(value.min)*1.2).toFixed(2);
        }
    },
  minfunction(value) {
        if(Math.abs(value.max) > Math.abs(value.min)){
            return (-Math.abs(value.max) * 1.2).toFixed(2);
        }else{
            return (-Math.abs(value.min) * 1.2).toFixed(2);
        }
    },
  getMaxAndInterval(arr) {
      var max=1.0; //默认值
      var interval=0.2; //默认值
      if(arr&&arr.length>0) {
        var maxValue=arr.reduce(function(v1,v2) {return v1>v2?v1:v2})-0;//获取最大值
        if(maxValue>0) {
              maxValue=Math.ceil(maxValue);//向上取整
                interval=Math.ceil(maxValue/SPLIT_NUMBER);//向上取整
                max=SPLIT_NUMBER*interval;//重设最大值刻度值
        }
      }
      return [max,interval];
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    selsChange: function(sels) {
      this.sels = sels
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.proCode);
      })
    }
  },
 function (params) {
    _this.temperatureAny.yAxis[0].max=Math.ceil(Math.max.apply(null,_this.temperatureAny.series[0].data)/5)*5;
    _this.temperatureAny.yAxis[0].interval=Math.ceil(Math.max.apply(null,_this.temperatureAny.series[0].data)/5);
    _this.temperatureAny.yAxis[1].max=Math.ceil(Math.max.apply(null,_this.temperatureAny.series[1].data)/5)*5;
    _this.temperatureAny.yAxis[1].interval=Math.ceil(Math.max.apply(null,_this.temperatureAny.series[1].data)/5);
    _this.temperatureAny.yAxis[0].min=0;
    _this.temperatureAny.yAxis[1].min=0;

 }  
}
</script>

<style>
 /* import '@element-theme-chalk/lib/drawer.css' */
</style>
