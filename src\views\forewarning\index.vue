<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="揽收超时预警" name="first" style="height: 100%" lazy>
        <pickupOvertimeWarning ref="forewarningtobeprocessed" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="揽收已超时明细" name="second" style="height: 100%" lazy>
        <pickupOvertimeDetails ref="forewarningtobeprocessed" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="揽收趋势汇总" name="third" style="height: 100%" lazy>
        <pickupOvertimeCharts style="height: 100%" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import forewarningtobeprocessed from "@/views/forewarning/forewarningtobeprocessed";
import forewarningrefunded from "@/views/forewarning/forewarningrefunded";
import forewarninghavenotified from "@/views/forewarning/forewarninghavenotified";
import forewarningreceived from "@/views/forewarning/forewarningreceived";
import pickupOvertimeWarning from "@/views/forewarning/pickupOvertimeWarning";
import pickupOvertimeDetails from "@/views/forewarning/pickupOvertimeDetails";
import pickupOvertimeCharts from "@/views/forewarning/pickupOvertimeCharts";
export default {
  name: 'Vue2',
  components: {
    MyContainer, forewarningrefunded, forewarningtobeprocessed, forewarninghavenotified, forewarningreceived, pickupOvertimeWarning, pickupOvertimeDetails, pickupOvertimeCharts
  },

  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first",
    };
  },

  async mounted() {

  },

  methods: {

  },
};
</script>

<style lang="scss" scoped></style>
