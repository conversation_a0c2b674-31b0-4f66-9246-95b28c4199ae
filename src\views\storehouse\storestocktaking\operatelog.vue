<template>
  <container>
    <template #header style="height: 500px;">
      <ces-table style="height: 500px;" :tablekey="'StoreTocktaking202305091124'" ref="tablePro" :showsummary='true'
        :summaryarry='proSummaryarry' :that='that' :isIndex='true' :tablefixed='true' :hasexpand='false'
        :isSelectColumn="false" :tableData='proTableList' :tableCols='proTableCols' :tableHandles='tableHandles'
        @sortchange="sortchange" :loading="listLoading"   :border="true">
      </ces-table>
    </template>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>
  </container>
</template>
<script>
import { pageStoreStockTakingLog } from '@/api/inventory/storestocktaking'
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import { Loading } from 'element-ui';
let loading;
const startLoading = () => {
  loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
  });
};

const tableColsPro = [
  { istrue: true, prop: 'operateType', label: '操作', width: '120' },
  { istrue: true, prop: 'operateInfo', label: '备注'  },
  { istrue: true, prop: 'operateName', label: '操作人', width: '100' },
  { istrue: true, prop: 'operateTime', label: '操作时间', width: '200' }
];

const tableHandles = [

];

export default {
  name: "StoreStockTakingLog",
  components: { container, cesTable },
  props: {
    filter: {
      io_id:0
    }
  },
  data() {
    return {
      that: this,
      proTableCols: tableColsPro,
      tableHandles: tableHandles,
      total: 0,
      proPager: { OrderBy: null, IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      proTableList: [],
      proSummaryarry: {},
      selids: []
    };
  },
  methods: {
    async sortchange(column) {
      if (!column.order) this.proPager = {};
      else
        this.proPager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      await this.onSearch();
    },
    async onSearch() {
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      let pager = this.$refs.pager.getPager()
      this.listLoading = true;
      let params = { ...pager, ...this.proPager, ... this.filter }
      let res = await pageStoreStockTakingLog(params);
      this.listLoading = false;
      this.proTableList = res.data?.list;
      this.total = res.data?.total;
      this.proSummaryarry = res.data?.summary;
    }
  }
}

</script>
<style></style>
