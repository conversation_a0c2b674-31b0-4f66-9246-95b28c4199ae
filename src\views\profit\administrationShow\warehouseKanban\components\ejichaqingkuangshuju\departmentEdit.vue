<template>
    <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
      <el-scrollbar style="height: 100%">
        <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="140px" class="demo-ruleForm">
          <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">1月</div>
          <el-form-item label="义乌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.januaryYiwuWarehouse" :placeholder="'义乌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="南昌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.januaryNanchangWarehouse" :placeholder="'南昌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="西安仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.januaryXianWarehouse" :placeholder="'西安仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="武汉仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.januaryWuhanWarehouse" :placeholder="'武汉仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="东莞仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.januaryDongguanWarehouse" :placeholder="'东莞仓'" class="publicCss"  :fixed="2" />
          </el-form-item>

          <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">2月</div>
          <el-form-item label="义乌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.februaryYiwuWarehouse" :placeholder="'义乌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="南昌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.februaryNanchangWarehouse" :placeholder="'南昌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="西安仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.februaryXianWarehouse" :placeholder="'西安仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="武汉仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.februaryWuhanWarehouse" :placeholder="'武汉仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="东莞仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.februaryDongguanWarehouse" :placeholder="'东莞仓'" class="publicCss"  :fixed="2" />
          </el-form-item>

          <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">3月</div>
          <el-form-item label="义乌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.marchYiwuWarehouse" :placeholder="'义乌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="南昌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.marchNanchangWarehouse" :placeholder="'南昌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="西安仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.marchXianWarehouse" :placeholder="'西安仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="武汉仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.marchWuhanWarehouse" :placeholder="'武汉仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="东莞仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.marchDongguanWarehouse" :placeholder="'东莞仓'" class="publicCss"  :fixed="2" />
          </el-form-item>

          <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">4月</div>
          <el-form-item label="义乌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.aprilYiwuWarehouse" :placeholder="'义乌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="南昌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.aprilNanchangWarehouse" :placeholder="'南昌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="西安仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.aprilXianWarehouse" :placeholder="'西安仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="武汉仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.aprilWuhanWarehouse" :placeholder="'武汉仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="东莞仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.aprilDongguanWarehouse" :placeholder="'东莞仓'" class="publicCss"  :fixed="2" />
          </el-form-item>

          <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">5月</div>
          <el-form-item label="义乌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.mayYiwuWarehouse" :placeholder="'义乌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="南昌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.mayNanchangWarehouse" :placeholder="'南昌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="西安仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.mayXianWarehouse" :placeholder="'西安仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="武汉仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.mayWuhanWarehouse" :placeholder="'武汉仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="东莞仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.mayDongguanWarehouse" :placeholder="'东莞仓'" class="publicCss"  :fixed="2" />
          </el-form-item>


          <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">6月</div>
          <el-form-item label="义乌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.juneYiwuWarehouse" :placeholder="'义乌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="南昌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.juneNanchangWarehouse" :placeholder="'南昌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="西安仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.juneXianWarehouse" :placeholder="'西安仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="武汉仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.juneWuhanWarehouse" :placeholder="'武汉仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="东莞仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.juneDongguanWarehouse" :placeholder="'东莞仓'" class="publicCss"  :fixed="2" />
          </el-form-item>

          <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">7月</div>
          <el-form-item label="义乌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.julyYiwuWarehouse" :placeholder="'义乌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="南昌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.julyNanchangWarehouse" :placeholder="'南昌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="西安仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.julyXianWarehouse" :placeholder="'西安仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="武汉仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.julyWuhanWarehouse" :placeholder="'武汉仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="东莞仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.julyDongguanWarehouse" :placeholder="'东莞仓'" class="publicCss"  :fixed="2" />
          </el-form-item>


          <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">8月</div>
          <el-form-item label="义乌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.augustYiwuWarehouse" :placeholder="'义乌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="南昌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.augustNanchangWarehouse" :placeholder="'南昌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="西安仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.augustXianWarehouse" :placeholder="'西安仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="武汉仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.augustWuhanWarehouse" :placeholder="'武汉仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="东莞仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.augustDongguanWarehouse" :placeholder="'东莞仓'" class="publicCss"  :fixed="2" />
          </el-form-item>


          <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">9月</div>
          <el-form-item label="义乌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.septemberYiwuWarehouse" :placeholder="'义乌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="南昌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.septemberNanchangWarehouse" :placeholder="'南昌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="西安仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.septemberXianWarehouse" :placeholder="'西安仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="武汉仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.septemberWuhanWarehouse" :placeholder="'武汉仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="东莞仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.septemberDongguanWarehouse" :placeholder="'东莞仓'" class="publicCss"  :fixed="2" />
          </el-form-item>


          <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">10月</div>
          <el-form-item label="义乌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.octoberYiwuWarehouse" :placeholder="'义乌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="南昌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.octoberNanchangWarehouse" :placeholder="'南昌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="西安仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.octoberXianWarehouse" :placeholder="'西安仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="武汉仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.octoberWuhanWarehouse" :placeholder="'武汉仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="东莞仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.octoberDongguanWarehouse" :placeholder="'东莞仓'" class="publicCss"  :fixed="2" />
          </el-form-item>


          <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">11月</div>
          <el-form-item label="义乌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.novemberYiwuWarehouse" :placeholder="'义乌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="南昌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.novemberNanchangWarehouse" :placeholder="'南昌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="西安仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.novemberXianWarehouse" :placeholder="'西安仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="武汉仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.novemberWuhanWarehouse" :placeholder="'武汉仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="东莞仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.novemberDongguanWarehouse" :placeholder="'东莞仓'" class="publicCss"  :fixed="2" />
          </el-form-item>


          <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">12月</div>
          <el-form-item label="义乌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.decemberYiwuWarehouse" :placeholder="'义乌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="南昌仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.decemberNanchangWarehouse" :placeholder="'南昌仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="西安仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.decemberXianWarehouse" :placeholder="'西安仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="武汉仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.decemberWuhanWarehouse" :placeholder="'武汉仓'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="东莞仓：">
              <inputNumberYh @input="computedone" v-model="ruleForm.decemberDongguanWarehouse" :placeholder="'东莞仓'" class="publicCss"  :fixed="2" />
          </el-form-item>

          
  
        </el-form>
      </el-scrollbar>
      <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
        <el-button @click="cancellationMethod">取消</el-button>
        <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
      </div>
    </div>
  </template>
  
  <script>
  import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
  import MyConfirmButton from '@/components/my-confirm-button'
  import { warehouseExpenseDetailsSubmit } from '@/api/people/peoplessc.js';
  import checkPermission from '@/utils/permission'
  import decimal from '@/utils/decimal'
  export default {
    name: 'departmentEdit',
    components: {
      inputNumberYh, MyConfirmButton
    },
    props: {
      editInfo: {
        type: Object,
        default: () => {
          return {}
        }
      },
      districtList: {
        type: Object,
        default: () => {
          return {}
        }
      },
      typeList: {
        type: Object,
        default: () => {
          return {}
        }
      }
    },
    data() {
      return {
        selectProfitrates: [],
        ruleForm: {
          label: '',
          name: ''
        },
        rules: {
          name: [
            { required: true, message: '请输入活动名称', trigger: 'blur' }
          ],
          label: [
            { required: true, message: '请输入活动名称', trigger: 'blur' }
          ]
        }
      }
    },
  
    async mounted() {
      this.$nextTick(() => {
        this.$refs.refruleForm.clearValidate();
      });
      this.ruleForm = { ...this.editInfo };
    },
      methods: {
      computedone(){
          let a = this.ruleForm.totalNumWorkers ? this.ruleForm.totalNumWorkers :  0;
          let b = this.ruleForm.usedWorkstations ? this.ruleForm.usedWorkstations :  0;
  
          this.ruleForm.remainingWorkstations = decimal(a, b, 2, '-').toFixed(0);
          return decimal(a, b, 2, '-').toFixed(0);
      },
      cancellationMethod() {
        this.$emit('cancellationMethod');
      },
      submitForm(formName) {
          console.log(this.ruleForm.label, 'this.ruleForm.label');
        if (this.ruleForm.remainingWorkstations<0) {
          return this.$message.warning('剩余工位不能为负数');
        }
        this.$refs[formName].validate(async(valid) => {
            if (valid) {
              this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing")
              const { data, success } = await warehouseExpenseDetailsSubmit(this.ruleForm)
              if(!success){
                  return
              }
              await this.$emit("search");
  
            } else {
              console.log('error submit!!');
              return false;
            }
          });
      //   this.$confirm('是否保存?', '提示', {
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //     type: 'warning'
      //   }).then(async () => {
      //     this.$refs[formName].validate(async(valid) => {
      //       if (valid) {
      //         const { data, success } = await warehouseExpenseDetailsSubmit(this.ruleForm)
      //         if(!success){
      //             return
      //         }
      //         await this.$emit("search");
  
      //       } else {
      //         console.log('error submit!!');
      //         return false;
      //       }
      //     });
      //   }).catch(() => {
      //   });
      },
      resetForm(formName) {
        this.$refs[formName].resetFields();
      },
    }
  }
  </script>
  <style scoped lang="scss">
  .publicCss {
    width: 80%;
  }
  </style>
  