<template>
  <my-container v-loading="pageLoading">
    <div ref="oneboxx" style="position: relative;">
      <div v-if="isMaskVisible" class="mask" @click="onMaskClick">
        <div style="display: flex; justify-content: center;  height: 100%; width: 100%; font-size: 40px; position: relative;">
          <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; position: absolute; width: 400px; top: 140px; left: 50%; transform: translate(-50%,-50%)">
            <div style="color: red; font-size: 23px;">正在处理批量打印中，请稍后...</div>
            <div style="margin-top: 100px;"><i class="el-icon-loading"></i></div>
          </div>
        </div>
      </div>
      <div>

      <div :id="'allbonbody'+i" v-for="(item,i) in alltableData" :key="i">
       <div style="box-sizing: border-box;padding: 25px 60px;"  >

        <!-- <div ref="allbonbody" id="allbonbody" style="padding: 32px 60px;">
          <div  style="box-sizing: border-box;" v-for="(item,i) in alltableData" :key="i"> -->


          <div ref="onheard">
            <div class="jxbt">
              视觉设计部人员月度绩效考核
            </div>
            <div class="jxan">
              <div style="width:20%;font-weight:bold;font-size:14px;">
                <span>被考核人：</span>
                <span v-text="item.performAssess.curCandidateName"></span>
              </div>
              <div style="width:20%;font-weight:bold;font-size:14px;margin-top: -3px;">
                <span>考核月份：</span>
                <span>
                  <el-select filterable v-model="item.performAssess.monthDay" placeholder="" style="width: 55px"
                  :disabled="myValue==1">
                    <el-option label="1" :value="1"></el-option>
                    <el-option label="2" :value="2" :key="2"></el-option>
                    <el-option label="3" :value="3" :key="3"></el-option>
                    <el-option label="4" :value="4" :key="4"></el-option>
                    <el-option label="5" :value="5" :key="5"></el-option>
                    <el-option label="6" :value="6" :key="6"></el-option>
                    <el-option label="7" :value="7" :key="7"></el-option>
                    <el-option label="8" :value="8" :key="8"></el-option>
                    <el-option label="9" :value="9" :key="9"></el-option>
                    <el-option label="10" :value="10" :key="10"></el-option>
                    <el-option label="11" :value="11" :key="11"></el-option>
                    <el-option label="12" :value="12" :key="12"></el-option>
                  </el-select>
                </span>
                <span style="margin-left:10px;">月</span>
              </div>
              <div style="width:75%;text-align: right;">
                <span style="margin-right:10px;">
                  <el-select size="mini" v-model="item.performAssess.workPostName" placeholder="请选择考核岗位" :disabled="myValue === 2" clearable>
                    <el-option v-for="item in result" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </span>
                <span style="margin-right:10px;"><el-button size="mini" type="primary"
                    @click="getlist">刷新</el-button></span>
                <span style="margin-right:40px;"><el-button style="width:100px;" size="mini" @click="performancesave"
                    type="primary" :disabled="isConfirm || historicalversion" v-show="myValue != 1">保存</el-button></span>
                <!-- <el-button @click="newtarget" size="mini" type="primary" plain v-show="myValue != 2"><i
                    class="el-icon-plus"></i>&nbsp;新增目标</el-button> -->
                <span style="float:right;margin-left:5px">
                  <el-button size="mini" :loading="waitShowPrinter" type="primary" v-show="myValue != 1"
                    @click="tocreateimg"><i class="el-icon-printer"></i></el-button>

                </span>
                <!-- <span style="float:right;margin-left:5px" v-if="tsmergedPdf">
                  <el-button size="mini" type="primary" v-show="myValue != 1"
                    @click="openMergedPDF(tsmergedPdf)"><i class="el-icon-s-claim"></i></el-button>

                </span> -->
                <span style="float:right">
                  <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-success="handleSuccess" :file-list="picFileList" multiple :show-file-list="false"
                    accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                    <el-button size="mini" type="primary" v-show="myValue != 1" :disabled="historicalversion"><i
                        class="el-icon-picture-outline"></i></el-button>
                  </el-upload>
                </span>
              </div>
            </div>
          </div>
          <!-- :height="myValue == 1 ? '550' : '290'" -->
          <div style="margin-bottom:20px;" >
            <vxe-table :data="item.performAssessDetialList" show-summary="ture" stripe border  ref="xTable2"
              @edit-closed="saveRowEvent" :summary="true"
              :footer-method="footerMethod" :show-footer="true"
              @checkbox-all="checkmethod" @checkbox-change="checkmethod"
              :edit-config="{ trigger: 'click', mode: 'cell', showIcon: false }">
              <vxe-table-column field="assessTarget" title="考核目标" :width="myValue == 1 ? '240' : '160'">
              </vxe-table-column>
              <vxe-table-column field="assessPostId" title="考核岗位" :width="myValue == 1 ? '140' : '100'">
                <template slot="default" slot-scope="{ row }">
                  <div v-if="row.assessPostId">
                    <span>{{ findSceneCode(row.assessPostId) }}</span>
                  </div>
                </template>
              </vxe-table-column>
              <vxe-table-column field="assessWeight" title="权重" :width="myValue == 1 ? '110' : '80'">
                <template #default="{ row }">
                  {{ row.assessWeight }}%
                </template>
              </vxe-table-column>
              <vxe-table-column field="assessDesc" title="描述"> </vxe-table-column>
              <vxe-table-column field="assessScore" title="得分" :width="myValue == 1 ? '110' : '70'" :edit-render="{}">
                <template #edit="{ row }">
                  <vxe-input v-model="row.assessScore" type="text" placeholder="请输入数值" :disabled="isConfirm"
                    ></vxe-input>
                  <!-- <span v-else>{{ row.assessScore }}</span>v-if="row.isSaved" -->
                </template>
              </vxe-table-column>

              <vxe-table-column v-if="myValue == 1" field="operate" title="操作" :width="'220'">
                <template slot="default" slot-scope="{ row }">
                  <vxe-button size="mini" @click="editRow(row)">编辑</vxe-button>
                  <vxe-button size="mini" type="danger" @click="handleDelete(row)">删除</vxe-button>
                </template>
              </vxe-table-column>
              <vxe-column v-else title="评分人" :width="'200'" fixed="right">
                <template #default="{ row }">
                  <template v-if="!row.showSaveButton">
                    <span>{{ row.scorer }}{{row.scoreTime}}</span>
                  </template>
                </template>
              </vxe-column>

            </vxe-table>
          </div>
          <div ref="onbottom">
            <div style="display: flex;font-size:14px; margin-top: 30px;" :style="{'margin-top': isMaskVisible?'60px':''}">
              <div style="width:90%;">
                <div class="jxsm">
                  1、考核期内有重大贡献或者其他重大事迹的，主管及以上管理可酌情考虑加分；
                </div>
                <div class="jxsm">
                  2、考核初始分数为100分，得分96分以上绩效为100%，96分及以下，每0.1分为对应绩效的0.1%；
                </div>
                <div class="jxsm">
                  3、70分及以下，绩效薪资按标准0元算（降级/换人/劝退处理）；
                </div>
                <div class="jxsm">
                  4、绩效金额跟据考核得分按出勤天数进行折算；
                </div>
              </div>
              <div v-show="myValue != 1"
                style="width:10%;justify-content: right;text-align: right; align-items: center;display: flex;background-color: #f5f5f5;">
                <div class="imageList_box">
                  <div class="imageList" v-for="(item, i) in picFileList" :key="i">
                    <el-image class="imgcss" style="width: 100px; height: 100px" :src="item" lazy
                      :preview-src-list="picFileList">
                    </el-image>
                    <span class="del" @click="delImg(item, i)">x</span>
                  </div>
                </div>
              </div>
            </div>
            <div style="margin:20px 0;" v-show="myValue != 1">
              <div class="jxan">
                <div style="width:33%;line-height:30px;font-size:14px;display: flex;">
                  <div style="width:35%;line-height:35px;font-weight:bold;">被考核人确认：</div>
                  <div style="width:65%;" class="box1">
                    <div @click="assessorverify(1)" v-if="item.performAssess.candidateName == null">
                      <span class="summitCss" :class="{ 'disabledsion': historicalversion }">点击确认</span>
                    </div>
                    <div v-else class="nameBox">
                      <span class="summitCss" :class="{ 'disabledsion': historicalversion }" @click="handleClickName(1)">{{
                        item.performAssess.candidateName }}</span>
                      <div>{{ dataAllInfo.assessorDate }}</div>
                    </div>
                  </div>
                </div>
                <div style="width:33%;line-height:30px;font-size:14px;display: flex;">
                  <div style="width:35%;line-height:35px;font-weight:bold;">直属主管确认：</div>
                  <div style="width:65%;" class="box1">
                    <div @click="assessorverify(2)" v-if="nameAllInfo.directManagerName == null">
                      <span class="summitCss" :class="{ 'disabledsion': historicalversion }">点击确认</span>
                    </div>
                    <div v-else class="nameBox">
                      <span class="summitCss" :class="{ 'disabledsion': historicalversion }" @click="handleClickName(2)">{{
                        nameAllInfo.directManagerName }}</span>
                      <div>{{ dataAllInfo.directManagerConfirmTime }}</div>
                    </div>
                  </div>
                </div>
                <div style="width:33%;line-height:30px;font-size:14px;display: flex;">
                  <div style="width:35%;line-height:35px;font-weight:bold;">部门经理确认：</div>
                  <div style="width:65%;" class="box1">
                    <div @click="assessorverify(3)" v-if="nameAllInfo.deptManagerDUserName == null">
                      <span class="summitCss" :class="{ 'disabledsion': historicalversion }">点击确认</span>
                    </div>
                    <div v-else class="nameBox">
                      <span class="summitCss" :class="{ 'disabledsion': historicalversion }" @click="handleClickName(3)">{{
                        nameAllInfo.deptManagerDUserName }}</span>
                      <div>{{ dataAllInfo.deptManagerConfirmTime }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          </div>


          <el-drawer title="新增考核目标" size="35%" :append-to-body="true" :before-close="handleClose"
            :visible.sync="innerDrawer">
            <div style="box-sizing: border-box;padding:10px 50px;">
              <!-- 表单 star -->
              <div style="width:100%;">
                <div class="jxkhcj">
                  <el-form style="width:100%" :model="ruleForm" :rules="rules" ref="ruleForm" label-width="12px"
                    class="demo-ruleForm">
                    <div style="display: flex;width:100%;">
                      <div class="lxwz">考核目标</div>
                      <div style="width:100%;">
                        <el-form-item label=" " prop="assessTarget">
                          <span><el-input v-model="ruleForm.assessTarget" style="width:80%;" size="mini" maxlength="50"
                              placeholder="请输入考核目标"></el-input></span>
                        </el-form-item>
                      </div>
                    </div>

                    <div style="display: flex;width:100%;">
                      <div class="lxwz">考核岗位</div>
                      <div style="width:100%;">
                        <el-form-item label=" " prop="assessPostId">
                          <span>
                            <el-select size="mini" v-model="ruleForm.assessPostId" multiple placeholder="请选择考核岗位"  @change="changearr($event)">
                              <el-option v-for="item in result" :key="item.value" :label="item.label"
                                :value="item.value">
                              </el-option>
                            </el-select>
                          </span>
                        </el-form-item>
                      </div>
                    </div>

                    <div style="display: flex;width:100%;">
                      <div class="lxwz">权重</div>
                      <div style="width:100%;">
                        <el-form-item label=" " prop="assessWeight">
                          <span><el-input v-model="ruleForm.assessWeight" size="mini" @input="handleInput"
                              style="width:15%"></el-input>&nbsp;&nbsp;%</span>
                        </el-form-item>
                      </div>
                    </div>

                    <div style="display: flex;width:100%;">
                      <div class="lxwz">目标描述</div>
                      <div style="width:100%;">
                        <el-form-item label=" " prop="assessDesc">
                          <span><el-input type="textarea" v-model="ruleForm.assessDesc" size="mini" maxlength="300"
                              placeholder="请填写目标描述"></el-input></span>
                        </el-form-item>
                      </div>
                    </div>

                    <div style="display: flex;width:100%;">
                      <div class="lxwz">默认得分</div>
                      <div style="width:100%;">
                        <el-form-item label=" " prop="assessScore">
                          <span><el-input v-model="ruleForm.assessScore" style="width:50%;" size="mini"
                              @input="scoreInput" placeholder="请输入默认得分"></el-input></span>
                        </el-form-item>
                      </div>
                    </div>

                    <div style="text-align:right;width:600px;height:60px;z-index: 999;position: fixed;bottom:35px;">
                      <el-form-item>
                        <el-button size="mini" @click="innerDrawer = false">取消</el-button>
                        <el-button size="mini" type="primary" @click="submitForm('ruleForm')">立即创建</el-button>
                      </el-form-item>
                    </div>
                  </el-form>
                </div>
              </div>
              <!-- 表单 end -->
            </div>
          </el-drawer>

          <!-- <print-preview ref="preView" /> -->
          <el-dialog :visible.sync="innerDialog" :title="'编辑考核目标'" width="50%" :modal="false" v-dialogDrag>
            <el-form :model="ruleForm" ref="form" :rules="rules" label-width="120px">
              <el-form-item label="考核目标" prop="assessTarget">
                <el-input v-model="ruleForm.assessTarget" placeholder="请输入考核目标" maxlength="50"></el-input>
              </el-form-item>
              <el-form-item label="考核岗位" prop="assessPostId">
                <el-select v-model="ruleForm.assessPostId" placeholder="请选择考核岗位">
                  <el-option v-for="item in result" :key="item.value" :label="item.label"
                    :value="item.value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="权重">
                <el-input v-model="ruleForm.assessWeight" placeholder="请输入权重" @input="handleInput"></el-input>
              </el-form-item>
              <el-form-item label="目标描述">
                <el-input type="textarea" v-model="ruleForm.assessDesc" placeholder="请输入目标描述" maxlength="300"></el-input>
              </el-form-item>
              <el-form-item label="默认得分">
                <el-input v-model="ruleForm.assessScore" placeholder="请输入默认得分" @input="scoreInput"></el-input>
              </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button @click="innerDialog = false">取消</el-button>
              <el-button type="primary" @click="saveForm('form')">保存</el-button>
            </div>
          </el-dialog>
        </div>
      </div>
    </div>
  </my-container>
</template>

<script>
import html2canvas from 'html2canvas'//打印相关组件
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
// import VXETable from 'vxe-table'
// import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
// import ExcelJS from 'exceljs'
// VXETable.use(VXETablePluginExportXLSX, {
//       ExcelJS
//     })
    
import MyContainer from "@/components/my-container";
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import { getPerformTempInfo, getPersonnelPositionAsync, editPackProcessPerformanceTemplate, getPerformAssessInfo,
  savePackProcessPerformanceAssess, confirmPerformAssess, unConfirmPerformAssess, getHistoryPerformAssessInfo,
   getShootingSetData,addOrUpdatePackProcessPerformanceTemplate,delPackProcessPerformanceTemplate, printPerformAssessAllList } from '@/api/media/shootingset';
import { getBasic } from '@/api/admin/user'//获取当前用户信息
import { getLoginInfo} from '@/api/admin/auth'
import dayjs from 'dayjs'//获取日期时间
import { formatTime } from "@/utils";
import performanceConfig from "@/views/media/packagework/common/performance.js"//打印相关组件
import { defaultElementTypeProvider, hiprint } from '@/views/media/packagework/common/index'//打印相关组件
import fontSize from "@/views/media/packagework/common/font-size.js";//打印相关组件
import scale from "@/views/media/packagework/common/scale.js";//打印相关组件
import panel from '@/views/media/packagework/common/panel'//打印相关组件
import JSPDF from "jspdf";
import { PDFDocument,  } from 'pdf-lib';
import axios from 'axios'


let hiprintTemplate;
export default {
  name: 'perforManceTemplatenewshoot',
  components: { MyContainer, cesTable },
  props: ['myValue', 'rowc', 'historicalversion', 'versionId', 'postselwork'],
  //myValue：区分模板打分页 rowc：行数据 historicalversion：区分历史版本打分页（部分功能禁用） versionId：历史版本打分页所需参数 postselwork:考核岗位选择
  data() {
    return {
      tableheight: 290,
      tsmergedPdf: null,
      versionld: 1723506300968828928,
      // historicalversion: false,//区分历史版本打分页
      index: 0,//行
      isMaskVisible: false,//打印遮罩层
      name: null,
      waitShowPrinter: false,//打印加载
      displayText: null,//点击确认姓名时间
      isSaved: false,//标记
      perfromAssId: 0,//点击确认和姓名时间切换
      currentDateTime: '',//时间戳
      isConfirm: false,//保存禁用
      nameAllInfo: {
        nickName: null,//保存姓名
        directManagerName: null,//主管姓名
        deptManagerDUserName: null//经理姓名
      },
      dataAllInfo: {
        assessorDate: '',//被考核人确认时间
        directManagerConfirmTime: null,//主管时间
        deptManagerConfirmTime: null,//经理时间
      },
      showEditButton: true,//得分列编辑后隐藏编辑按钮
      showSaveButton: false,//得分列编辑保存隐藏保存按钮
      nickid: null,//id
      userName: null,//姓名
      editIndex: 0, // 当前编辑的行索引
      innerDialog: false,//编辑弹窗
      picFileList: [],//图片上传列表
      result: [],//考核岗位
      setType: 11,//考核岗位接口传参
      pageLoading: false,//加载
      filter: {
        perfromTempId: 0,
        monthDay: null,//日期
        workPostId: 0,
        detialList: [],
        imgUrl: '',
      },
      allfile: [],
      workPosition: null,//考核岗位
      innerDrawer: false,//新增考核目标
      //考核目标数据
      ruleForm: {
        assessScore: 0,
        assessTarget: null,
        assessWeight: null,
        assessPostId: 0,
        assessDesc: null,
        assessPostIds:[]
      },
      allimgs: [],
      rules: {
        assessTarget: [
          { required: true, message: '请输入考核目标', trigger: 'blur' }
        ],
        assessPostId: [
          { required: true, message: '请选择考核岗位', trigger: 'blur' }
        ],
        assessWeight: [
          { required: true, message: '请输入权重', trigger: 'blur' }
        ],
        assessDesc: [
          { required: true, message: '请输入目标描述', trigger: 'blur' }
        ],
        assessScore: [
          { required: true, message: '请输入默认得分', trigger: 'blur' }
        ]
      },
      tableData: [],//表格数据
      tabledatanew: [],
      alltableData: [],
    };
  },

  mounted() {
    this.getlist();
    this.assessmentpost();
    this.myValue==1?this.tableheight=550:this.tableheight=290;
    // this.$refs.xTable.setAllCheckboxRow(true)
  },
  computed: {
    //表格宽高
    tableStyle() {
      if (this.myValue !== 1) {
        return { height: '290px', width: '100%' };
      } else {
        return { height: '550px', width: '100%' };
      }
    }
  },
  methods: {
    async forgetlist(row) {
      let _this = this;
      let ddUserIds = [];
      row.map((item) => {
        ddUserIds.push(item.userId);
      });
      const params = {
        userIdList: ddUserIds,
      };
      const res = await printPerformAssessAllList(params);
      if (!res.success) {
        _this.$emit("close")
        // this.$message(msg);
        return;
      }
      _this.alltableData = res.data;
      // res.data.map((item)=>{
      //   _this.alltableData.push(item)
      // })
      _this.$nextTick(() => {
        _this.tocreateimg();
      });
      // this.tocreateimg();
      console.log(res.data, "success");
    },
    checkmethod(row){
      console.log("打印行事件",row.records)
      this.tabledatanew = row.records;
      this.waitShowPrinter = false;
    },
    //模板页抽屉关闭，清除考核岗位
    performanFilterMonthDay() {
      this.value = null;
      this.getlist();
    },
    //打分页弹窗关闭，清除月份
    closeFilterMonthDay() {
      this.filter.monthDay = null
    },
    // 进行合计
    footerMethod({ columns, data }) {
      return [
        columns.map((column, columnIndex) => {
          if (columnIndex === 0) {
            return '合计'
          }
          if (['assessWeight', 'assessScore'].includes(column.property)) {
            return this.sumNum(data, column.property)
          }
          return null
        })
      ]
    },
    sumNum(costForm, type) {
      let total = 0
      let newtotal = ''
      if (type == 'assessWeight') {
        for (let i = 0; i < costForm.length; i++) {
          total += Number(costForm[i].assessWeight)
        }
        newtotal = total.toFixed(2) + '%'
      } else {
        for (let i = 0; i < costForm.length; i++) {
          total += Number(costForm[i].assessScore)
        }
        newtotal = total.toFixed(0)
      }
      return newtotal
    },
    //默认得分限制输入值
    scoreInput() {
      if (this.ruleForm.assessScore < 0) {
        this.ruleForm.assessScore = 0;
      } else if (this.ruleForm.assessScore > 100) {
        this.ruleForm.assessScore = 100;
      }
    },
    //权重输入值
    handleInput() {
      if (this.ruleForm.assessWeight < 0) {
        this.ruleForm.assessWeight = 0;
      } else if (this.ruleForm.assessWeight > 100) {
        this.ruleForm.assessWeight = 100;
      }
    },
    changearr(val)
    {
      this.ruleForm.assessPostIds = val;
    },
    //打印功能相关配置
    init() {
      hiprint.init({
        providers: [new defaultElementTypeProvider()]
      });
      // 还原配置
      hiprint.setConfig()
      // 替换配置
      hiprint.setConfig({
        optionItems: [
          fontSize,
          scale,
          function () {
            function t() {
              this.name = "zIndex";
            }
            return t.prototype.css = function (t, e) {
              if (t && t.length) {
                if (e) return t.css('z-index', e);
              }
              return null;
            }, t.prototype.createTarget = function () {
              return this.target = $('<div class="hiprint-option-item">\n        <div class="hiprint-option-item-label">\n        元素层级2\n        </div>\n        <div class="hiprint-option-item-field">\n        <input type="number" class="auto-submit"/>\n        </div>\n    </div>'), this.target;
            }, t.prototype.getValue = function () {
              var t = this.target.find("input").val();
              if (t) return parseInt(t.toString());
            }, t.prototype.setValue = function (t) {
              this.target.find("input").val(t);
            }, t.prototype.destroy = function () {
              this.target.remove();
            }, t;
          }(),
        ],
        movingDistance: 2.5,
        text: {
          tabs: [
            // 隐藏部分
            {
              // name: '测试', // tab名称 可忽略
              options: [] // 必须包含 options
            },// 当修改第二个 tabs 时,必须把他之前的 tabs 都列举出来.
            {
              name: '样式', options: [
                {
                  name: 'scale',
                  after: 'transform', // 自定义参数，插入在 transform 之后
                  hidden: false
                },
              ]
            }
          ],
          supportOptions: [
            {
              name: 'styler',
              hidden: true
            },
            {
              name: 'scale', // 自定义参数，supportOptions 必须得添加
              after: 'transform', // 自定义参数，插入在 transform 之后
              hidden: false
            },
            {
              name: 'formatter',
              hidden: true
            },
          ]
        },
        image: {
          tabs: [
            {
              // 整体替换
              replace: true,
              name: '基本', options: [
                {
                  name: 'field',
                  hidden: false
                },
                {
                  name: 'src',
                  hidden: false
                },
                {
                  name: 'fit',
                  hidden: false
                }
              ]
            },
          ],
        }
      })
      // eslint-disable-next-line no-undef
      hiprint.PrintElementTypeManager.buildByHtml($('.ep-draggable-item'));
      $('#hiprint-printTemplate').empty()
      let that = this;
      this.template = hiprintTemplate = new hiprint.PrintTemplate({
        template: panel,
        // 图片选择功能
        onImageChooseClick: (target) => {
          // 测试 3秒后修改图片地址值
          setTimeout(() => {
            // target.refresh(url,options,callback)
            // callback(el, width, height) // 原元素,宽,高
            // target.refresh(url,false,(el,width,height)=>{
            //   el.options.width = width;
            //   el.designTarget.css('width', width + "pt");
            //   el.designTarget.children('.resize-panel').trigger($.Event('click'));
            // })
            target.refresh("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAtAAAAIIAQMAAAB99EudAAAABlBMVEUmf8vG2O41LStnAAABD0lEQVR42u3XQQqCQBSAYcWFS4/QUTpaHa2jdISWLUJjjMpclJoPGvq+1WsYfiJCZ4oCAAAAAAAAAAAAAAAAAHin6pL9c6H/fOzHbRrP0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0u/SY9LS0tLS0tLS0tLS0n+edm+UlpaWlpaWlpaWlpaW/tl0Ndyzbno7/+tPTJdd1wal69dNa6abx+Lq6TSeYtK7BX/Diek0XULSZZrakPRtV0i6Hu/KIt30q4fM0pvBqvR9mvsQkZaW9gyJT+f5lsnzjR54xAk8mAUeJyMPwYFH98ALx5Jr0kRLLndT7b64UX9QR/0eAAAAAAAAAAAAAAAAAAD/4gpryzr/bja4QgAAAABJRU5ErkJggg==", {
              // auto: true, // 根据图片宽高自动等比(宽>高?width:height)
              // width: true, // 按宽调整高
              // height: true, // 按高调整宽
              real: true // 根据图片实际尺寸调整(转pt)
            })
          }, 3000)
          // target.getValue()
          // target.refresh(url)
        },
        // 自定义可选字体
        // 或者使用 hiprintTemplate.setFontList([])
        // 或元素中 options.fontList: []
        fontList: [
          { title: '微软雅黑', value: 'Microsoft YaHei' },
          { title: '黑体', value: 'STHeitiSC-Light' },
          { title: '思源黑体', value: 'SourceHanSansCN-Normal' },
          { title: '王羲之书法体', value: '王羲之书法体' },
          { title: '宋体', value: 'SimSun' },
          { title: '华为楷体', value: 'STKaiti' },
          { title: 'cursive', value: 'cursive' },
        ],
        dataMode: 1, // 1:getJson 其他：getJsonTid 默认1
        history: true, // 是否需要 撤销重做功能
        onDataChanged: (type, json) => {
          console.log(type); // 新增、移动、删除、修改(参数调整)、大小、旋转
          console.log(json); // 返回 template
        },
        onUpdateError: (e) => {
          console.log(e);
        },
        settingContainer: '#PrintElementOptionSetting',
        paginationContainer: '.hiprint-printPagination'
      });
      hiprintTemplate.design('#hiprint-printTemplate', { grid: true });
      // 获取当前放大比例, 当zoom时传true 才会有
      this.scaleValue = hiprintTemplate.editingPanel.scale || 1;
    },
    uploadToServer(file, callback) {
      var xhr = new XMLHttpRequest()
      var formData = new FormData()
      formData.append('file', file)
      xhr.open('post', '/api/uploadnew/file/UploadCommonFileAsync')
      xhr.withCredentials = true
      xhr.responseType = 'json'
      xhr.send(formData)
      xhr.onreadystatechange = () => {
        if (xhr.readyState === 4 && xhr.status === 200) {
          // debugger;
          callback(xhr.response)
        }
      }
    },
    blobToFile(theBlob, fileName) {
      theBlob.lastModifiedDate = new Date();
      theBlob.name = fileName;
      return new File([theBlob], fileName, { type: theBlob.type, name: fileName, lastModified: Date.now() });
    },
    dataURLtoBlob(dataurl) {
      var arr = dataurl.split(','),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new Blob([u8arr], { type: mime });
    },
    //打印
    tocreateimg(val) {
      let _this = this;
      _this.tableheight = "";
      _this.isMaskVisible = true;


      // setTimeout(() => {
      //   const childElement = document.getElementById("allbonbody");
      //   childElement.style.width = "1000px";
      //   _this.print(childElement)
      // }, 1000);


      let newdemo = [];

      for (var i = 0; i < _this.alltableData.length; i++) {
        let itemdemo = document.getElementById("allbonbody" + i);
        console.log("打印数据",itemdemo)

        itemdemo.style.width = "1000px";
        itemdemo.style.fontWeight = 600;
        newdemo.push(itemdemo);
      }

      setTimeout(() => {
        newdemo.map((item) => {
          _this.print(item);
        });
      }, 1000);
    },
    print(dom) {
      let _this = this;
      const copyDom = dom.cloneNode(true);

      copyDom.style.height = "auto";
      document.body.appendChild(copyDom);
      html2canvas(copyDom, {
        dpi: window.devicePixelRatio * 4,
        scale: 4,
        logging: false,
        useCORS: true,
      }).then(function(canvas) {
        var direction = "p";
        var size = "a4";
        let printW;
        let printH;
        var ctx = canvas.getContext("2d");
        var a4w = printW ? printW - 20 : 190;
        var a4h = printH ? printH - 20 : 277;
        var imgHeight = Math.floor((a4h * canvas.width) / a4w);
        var renderedHeight = 0;

        var pdf = new JSPDF(direction, "mm", size);
        while (renderedHeight < canvas.height) {
          var page = document.createElement("canvas");
          page.width = canvas.width;
          page.height = Math.min(imgHeight, canvas.height - renderedHeight);
          page
            .getContext("2d")
            .putImageData(
              ctx.getImageData(
                0,
                renderedHeight,
                canvas.width,
                Math.min(imgHeight, canvas.height - renderedHeight)
              ),
              0,
              0
            );
          pdf.addImage(
            page.toDataURL("image/jpeg", 1.0),
            "JPEG",
            10,
            10,
            a4w,
            Math.min(a4h, (a4w * page.height) / page.width)
          );
          // pdf.addImage(page.toDataURL('image/jpeg', 1), 'JPEG', 10, 10, a4w, Math.min(a4h * 1, a4w * 1 * page.height / page.width));
          renderedHeight += imgHeight;
          if (renderedHeight < canvas.height) {
            pdf.addPage();
          }
          page.remove();
        }
        document.body.removeChild(copyDom);

        _this.allfile.push(pdf);
        if (_this.allfile.length === _this.alltableData.length) {
          const pdfBlobs = _this.allfile.map((pdf) => {
            const pdfBytes = pdf.output("arraybuffer");
            return new Blob([pdfBytes], { type: "application/pdf" });
          });

          let urllist = [];
          pdfBlobs.map((item) => {
            const url = window.URL.createObjectURL(item);
            urllist.push(url);
            if (urllist.length == _this.alltableData.length) {
              _this.mergePDFs(urllist);
            }
          });
        }
      });
    },

     winjump(pdf){
      const link = window.URL.createObjectURL(pdf.output('blob'));
      window.open(link);
     },

     async mergePDFs(urllist) {
      let _this = this;
      const pdfBytesArray = await Promise.all(
        urllist.map(async (blobUrl) => {
          const response = await fetch(blobUrl);
          const arrayBuffer = await response.arrayBuffer();
          return new Uint8Array(arrayBuffer);
        })
      );

      const mergedPdf = await this.mergePDFBytes(pdfBytesArray);

      // _this.isMaskVisible = false;

      _this.allfile = [];
      _this.$emit("close");
      //移除对象
      await this.openMergedPDF(mergedPdf);
    },
    openMergedPDF(mergedPdf) {
      let _this = this;
      if (mergedPdf) {
        const blob = new Blob([mergedPdf], { type: "application/pdf" });
        const url = URL.createObjectURL(blob);
        // 打开新标签页显示 PDF
        window.open(url, "_blank");
      } else {
        console.error("Merged PDF is not available.");
      }
    },
    async mergePDFBytes(pdfBytesArray) {
      const pdfDoc = await PDFDocument.create();

      for (const pdfBytes of pdfBytesArray) {
        const pdf = await PDFDocument.load(pdfBytes);
        const copiedPages = await pdfDoc.copyPages(pdf, pdf.getPageIndices());
        copiedPages.forEach((page) => {
          pdfDoc.addPage(page);
        });
      }

      return await pdfDoc.save();
    },
    async downloadMergedPDF(files) {
      const mergedPdfBytes = files;
      const blob = new Blob([mergedPdfBytes], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);

      const downloadLink = document.createElement('a');
      downloadLink.href = url;
      downloadLink.download = 'merged.pdf';

      downloadLink.click();

      // 释放 URL 对象
      URL.revokeObjectURL(url);
    },
    //遮罩层
    onMaskClick() {
      this.isMaskVisible = false;
    },
    //点击返回点击确认
    async handleClickName(val) {
      if (this.historicalversion == true) {
        return;
      }
      this.$confirm('确定取消确认？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const params = { confirmType: val, perfromAssId: this.perfromAssId };
        const { success } = await unConfirmPerformAssess(params);
        if (!success) {
          return
        };
        if (val == 1) {
          this.nameAllInfo.nickName = null
          this.dataAllInfo.assessorDate = null
        } else if (val == 2) {
          this.nameAllInfo.directManagerName = null
          this.dataAllInfo.directManagerConfirmTime = null
        } else {
          this.nameAllInfo.deptManagerDUserName = null
          this.dataAllInfo.deptManagerConfirmTime = null
        }
        this.getlist();
        this.$emit("onSearch");
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作'
        });
      });
    },
    //点击确认
    async assessorverify(val) {
      console.log(this.historicalversion, 'historicalversion');
      if (this.historicalversion == true) {
        return;
      }
      if (this.displayText == null) {
        this.$message({ message: "请先进行评分", type: "warning" });
        return
      }
      const params = { confirmType: val, perfromAssId: this.perfromAssId };
      const { success } = await confirmPerformAssess(params);
      if (!success) {
        return
      };
      const { data } = await getLoginInfo();
      const date = new Date().toISOString().split('T')[0];
      if (val == 1) {
        this.dataAllInfo.assessorDate = date
        this.nameAllInfo.nickName =data?.user?.userName
      } else if (val == 2) {
        this.dataAllInfo.directManagerConfirmTime = date
        this.nameAllInfo.directManagerName =data?.user?.userName
      } else {
        this.dataAllInfo.deptManagerConfirmTime = date
        this.nameAllInfo.deptManagerDUserName = data?.user?.userName
      }
      this.getlist();
      this.$emit("onSearch");
    },
    //图片
    delImg(item, i) {
      this.picFileList.splice(i, 1)
    },
    //取消对得分列的编辑
    cancelRowEvent(row) {
      const $table = this.$refs.xTable
      $table.clearActived().then(() => {
        // 还原行数据
        $table.revertData(row)
      })
    },
    //保存编辑得分
    async saveRowEvent({ row, rowIndex }) {
      if (this.isConfirm == true) {
        return
      }
      const { data } = await getLoginInfo()
      this.$set(this.tableData[rowIndex], "scorer", data?.user?.userName);
      this.nickid =0;
      //获取时间
      this.$set(this.tableData[rowIndex], "scoreTime", dayjs().format('YYYY-MM-DD HH:mm:ss'));
      this.$set(this.tableData[rowIndex], "isSaved", true); // 添加一个属性来标识是否已保存
      this.$set(this.tableData[rowIndex], "displayText", row.scorer + ' ' + row.scoreTime);
      this.displayText = row.scorer + ' ' + row.scoreTime;
      this.$set(this.tableData[rowIndex], "showSaveButton", false)
    },
    //开启对得分列的编辑
    editRowEvent(row) {
      this.$refs.xTable.setActiveRow(row)
      row.showSaveButton = true;
      row.showEditButton = false;
    },
    async assessmentpost() {
      const { data } = await getShootingSetData({settype:this.setType})
      console.log(data, 'res');
      this.result = data.data.map(obj => ({ value: obj.setId, label: obj.sceneCode }));
      console.log(this.result, 'this.result');
    },
    //绩效考核模板保存
    async performancesave() {
      if (this.myValue == 1) {
        // if (this.workPosition == null) {
        //   this.$message({ message: "请选择考核岗位", type: "warning" });
        //   return
        // } else {
        //   this.filter.workPostId = this.workPosition
        // }
        this.tableData.forEach((item) => {
          item.assessScore = parseInt(item.assessScore);
          item.assessWeight = parseFloat(item.assessWeight);
          // item.assessScore = Number(item.assessScore).toFixed(0);
          // item.assessWeight = Number(item.assessWeight).toFixed(2);
          delete item.monthDay;
        });
        this.filter.detialList = [...this.tableData];
        if (this.filter.imgUrl === '') {
          delete this.filter.imgUrl;
        };
        const params = {
          ...this.filter,
        };
        const { data, success } = await editPackProcessPerformanceTemplate(params);
        if (success) {
          this.$message({ message: "保存成功", type: "success" });
          return
        };
      } else {
        this.filter.detialList = this.filter.detialList.map(item => {
          return {
            ...item,
            scorer: this.nameAllInfo.nickName,
            scoreTime: this.currentDateTime,
          };
        });
        this.filter.workPostId = this.rowc.workPosition;
        this.tableData = this.tableData.map(item => {
          return {
            ...item,
            scorerId: this.nickid,
          };
        });
        this.filter.detialList = [...this.tableData];
        this.filter.imgUrl = this.picFileList.join()
        const params = {
          ...this.filter,
          curCandidateDUserName: this.rowc.userName,
          curCandidateUserId: this.rowc.userId,
        }
        const { data, success } = await savePackProcessPerformanceAssess(params)
        if (success) {
          this.$message({ message: "保存成功", type: "success" });
          this.getlist();
          this.$emit("onSearch");
          return
        };
      }
    },
    //考核岗位列
    findSceneCode(value) {
      const item = this.result.find(item => item.value === value);
      return item ? item.label : '';
    },
    //刷新（查询）
    async getlist() {
      // let versionld = 1723506300968828928
      // const params=versionld
      // const { data } = await getHistorytPersonnelPositionInfo({ versionId: this.versionId })
      // console.log(data, 'data34589999');
      // console.log(this.rowc, 'rowc');
      // 获取当前日期
      var date = new Date();
      var month = date.getMonth();
      var day = date.getDate();
      // 判断是否需要将月份加1
      if (day > 19) {
        month++;
      }
      if (month < 0) {
        month = 11;
      }
      if (this.filter.monthDay == null) {
        this.filter.monthDay = parseInt(month);
      }
      //myValue判断模板和打分页
      if (this.myValue == 1) {
        const params = this.workPosition
        const { data } = await getPerformTempInfo({ workPostId: params });
        this.ruleForm.monthDay = data.performTemp.monthDay;
        this.filter.monthDay = data.performTemp.monthDay;
        this.tableData = data.performTempDetialList
      } else {
        this.userName = this.rowc.userName;//获取被考核人姓名
        const params = {
          userId: this.rowc.userId,
          workPostId: this.rowc.workPosition,
          monthDay: this.filter.monthDay,
        }
         //点击确认人
         this.nameAllInfo = {
          nickName: null,
          directManagerName: null,
          deptManagerDUserName: null
          }
          //点击确认时间
          this.dataAllInfo = {
            assessorDate: null,
            directManagerConfirmTime: null,
            deptManagerConfirmTime: null
          }
        this.workPosition = this.rowc.workPosition;
        //historicalversion判断是否是从历史版本打开
        if (this.historicalversion == 3) {
          //versionId历史版本所需参数
          const newParams = { ...params, versionId: this.versionId };
          const { data, success } = await getHistoryPerformAssessInfo(newParams)
          if (!success) {
            this.tableData = null
            return
          }else
          {
          this.isConfirm = data.performAssess.isConfirm
          this.getInfo(data.performAssess.candidateName, data.performAssess.createdTime, 1)
          this.getInfo(data.performAssess.directManagerName, data.performAssess.directManagerConfirmTime, 2)
          this.getInfo(data.performAssess.deptManagerDUserName, data.performAssess.deptManagerConfirmTime, 3)
          this.filter.perfromTempId = data.performAssess.perfromTempId;
          this.tableData = data.performAssessDetialList;
          this.perfromAssId = data.performAssess.perfromAssId;
          if (data.performAssess.imgUrl != null && data.performAssess.imgUrl != "") {
            this.picFileList = data.performAssess.imgUrl.split()//图片回显
          } else {
            this.picFileList = []
          }
        }
          //通过historicalversion将历史版本打开的打分页，显示图片上传及点击确认
          if (this.historicalversion == 3) {
            this.historicalversion = true
          }
        } else {
          const { data, success } = await getPerformAssessInfo(params)
          if (!success) {
            this.nameAllInfo = {
            nickName: null,
            directManagerName: null,
            deptManagerDUserName: null
            }
            this.dataAllInfo = {
              assessorDate: null,
              directManagerConfirmTime: null,
              deptManagerConfirmTime: null
            }
            this.tableData = null
            return
          }else
          {
            this.isConfirm = data.performAssess.isConfirm
            this.getInfo(data.performAssess.candidateName, data.performAssess.createdTime, 1)
            this.getInfo(data.performAssess.directManagerName, data.performAssess.directManagerConfirmTime, 2)
            this.getInfo(data.performAssess.deptManagerDUserName, data.performAssess.deptManagerConfirmTime, 3)
            console.log(data.performAssess.perfromTempId, 'data.performAssess.perfromTempId');
            this.filter.perfromTempId = data.performAssess.perfromTempId;
            console.log(this.filter.perfromTempId, 'this.filter.perfromTempId');
            this.tableData = data.performAssessDetialList;
            this.perfromAssId = data.performAssess.perfromAssId;
            if (data.performAssess.imgUrl != null && data.performAssess.imgUrl != "") {
              this.picFileList = data.performAssess.imgUrl.split()//图片回显
            } else {
              this.picFileList = []
            }
            if (this.historicalversion == 3) {
              this.historicalversion = true
            }
          }
        }
      }
      //点击确认将姓名和时间拼接
      this.tableData.map((item) => {
        if (item.scoreTime) {
          item.displayText = item.scorer + ' ' + item.scoreTime;
          this.displayText = item.scorer + ' ' + item.scoreTime;
          item.showEditButton = false;
          item.showSaveButton = false;
        } else {
          item.showEditButton = true;
          item.showSaveButton = false;
        }

      })
    },
    //点击确认获取人员时间信息
    getInfo(name, time, num) {
      if (name != null && name.length != 0 && name != undefined) {
        if (num == 1) {
          this.nameAllInfo.nickName = name
          this.dataAllInfo.assessorDate = dayjs(time).format('YYYY-MM-DD')
        } else if (num == 2) {
          this.nameAllInfo.directManagerName = name
          this.dataAllInfo.directManagerConfirmTime = dayjs(time).format('YYYY-MM-DD')
        } else {
          this.nameAllInfo.deptManagerDUserName = name
          this.dataAllInfo.deptManagerConfirmTime = dayjs(time).format('YYYY-MM-DD')
        }
      }
    },
    //新增目标
    newtarget() {
      this.picFileList = []//清空图片上传列表
      this.ruleForm.assessScore = null;
      this.ruleForm.assessTarget = null;
      this.ruleForm.assessWeight = null;
      this.ruleForm.assessPostId = null;
      this.ruleForm.assessDesc = null;
      this.ruleForm.assessPostIds=[];
      this.innerDrawer = true;
    },
    //图片上传成功回调
    async handleSuccess({ data }) {
      console.log(this.picFileList, 'this.picFileList');
      this.picFileList.push(data.url)
    },
    //模板页删除行
   async handleDelete(row) {
      this.$confirm('确定确认吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        var res = await delPackProcessPerformanceTemplate({tempDetialId:row.perfromTempDetialId});
        if(res?.success)
        {
          this.$message({ message: "删除成功", type: "success" });
          this.tableData.splice(this.tableData.indexOf(row), 1);
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作'
        });
      });
    },
    // 编辑按钮点击事件
    editRow(row) {
      this.editIndex = this.tableData.indexOf(row); // 设置当前编辑的行索引
      this.innerDialog = true; // 打开编辑对话框
      this.ruleForm = { ...row };
    },
    // 保存按钮点击事件
    async saveForm(ruleForm) {
      var isValid=false;
      this.$refs[ruleForm].validate((valid) => {
        if (!valid)
          {
            this.$message.error('请填写完整信息');
            return false;
          }else
          {
            isValid=true;
          }
        })
        if(isValid)
        {
          var res = await addOrUpdatePackProcessPerformanceTemplate({ ...this.ruleForm });
          if(res?.success)
          {
            this.$message({ message: "保存成功", type: "success" });
            this.tableData.splice(this.editIndex, 1, { ...this.ruleForm });
            this.editIndex = null;
            this.innerDialog = false; // 关闭编辑对话框
          }
        }
    },
   async submitForm(ruleForm) {
      var isValid=false;
      this.$refs[ruleForm].validate((valid) => {
        if (valid) {
          // // 将表单数据添加到表格数据中
          // if (this.tableData.some(item => item.assessTarget === this.ruleForm.assessTarget) && this.tableData.some(item => item.assessPostId === this.ruleForm.assessPostId)) {
          //  this.$message.error('该考核目标已存在');
          //   this.innerDrawer = true;
          //   return
          // } else {
          //   isValid=true;
          // }
          isValid=true;
        } else {
          this.$message.error('请填写完整信息');
          this.innerDrawer = true;
          return false;
        }
      });
      if(isValid)
      {
        this.ruleForm.assessPostId=0;
        var res =  await addOrUpdatePackProcessPerformanceTemplate({ ...this.ruleForm });
        if(res?.success)
        {
          this.$message({ message: "保存成功", type: "success" });
          this.tableData.push({ ...this.ruleForm });
          this.getlist();
          this.innerDrawer = false;
        }else
        {
          this.ruleForm.assessPostId=this.ruleForm.assessPostIds;
        }
      }
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done();
        })
        .catch(_ => { });
    },
  }
};
</script>

<style lang="scss" scoped>
/* .el-dialog__body {
  padding-top: 5px !important;
} */
.el-drawer__header {
  padding: 25px 30px 20px 30px !important;
  border: 1px solid #dcdfe6 !important;
  border-top: 0px !important;
  border-right: 0px !important;
  border-left: 0px !important;
  font-size: 18px !important;
  margin-bottom: 10px !important;
}

.jxbt {
  width: 100%;
  height: 80px;
  font-size: 30px;
  font-weight: bold;
  letter-spacing: 2px;
  /* background-color: aquamarine; */
  text-align: center;
}

.jxan {
  width: 100%;
  height: 38px;
  /* background-color: aquamarine; */
  display: flex;
}

.jxsm {
  width: 100%;
  margin: 5px 0;
}

.jxkhcj {
  width: 100%;
  height: 35px;
  box-sizing: border-box;
  display: flex;
}

//字体样式
.lxwz {
  width: 80px;
  font-size: 14px; //字体大小
  color: #666; //颜色
  line-height: 42px; //行高
}

.el-dialog__wrapper {
  height: 100%;
}

//图片
.imageList_box {
  display: flex;
  flex-wrap: wrap;

  .imageList {
    position: relative;
    width: 100px;
    height: 100px;

    .imgcss ::v-deep img {
      min-width: 100px !important;
      min-height: 100px !important;
      width: 100px !important;
      height: 100px !important;
    }


    .del {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 16px;
      width: 15px;
      height: 15px;
      border-radius: 50%;
      color: black;
      text-align: center;
      line-height: 15px;
      cursor: pointer;
    }
  }
}

.summitCss {
  color: #409EFF;
  cursor: pointer;
  margin-right: 10px;
}

.nameBox {
  width: 150px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.box1 {
  display: flex;
  align-items: center;
}

//遮罩层
.mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.vxe-table {
  // min-height: 400px;
  /* 设置表格高度 */
  position: relative;
  /* 设置表格位置为相对定位 */
}

.vxe-table .vxe-table-footer--fixed {
  position: absolute;
  /* 设置汇总行位置为绝对定位 */
  bottom: 0;
  /* 将汇总行放置在表格底部 */
}

.disabledsion {
  color: #c0c4cc;
  cursor: not-allowed;
  opacity: 0.5;
  border: none;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 8px;
  margin-right: 8px;
}
</style>
