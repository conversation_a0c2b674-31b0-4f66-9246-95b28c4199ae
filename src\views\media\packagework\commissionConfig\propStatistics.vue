<template>
    <!-- 道具统计 -->
    <div>
        <vxetablebase :id="'mediaNCCommission'" 
        :hasSeq="false"
        :border="true" 
        :hasexpand='true' 
        :hascheck="false"
        :height="'720'"
        :align="'center'"
        ref="table" 
        :that='that'  
        :tableData='tasklist'  
        :tableCols='tableCols'  
        :loading="listLoading" 
        >
        <template slot="right">
                <vxe-column title="操作"   width="140" fixed="right">
                    <template #default="{ row }">
                        <el-button type="text" @click="onEditAdd(row)">编辑</el-button> 
                        <el-button type="text" @click="onEditAdd(row)">删除</el-button> 
                    </template>
                </vxe-column>
        </template>    
        </vxetablebase>
    </div>
</template>

<script>
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
const tableCols = [
    { istrue: true,  label: '#', width: '60', align: 'center' },
    { istrue: true, prop: 'product_PropStatistics', label: '产品简称', width: '140', align: 'center', },
    { istrue: true, prop: 'store_PropStatistics', label: '店铺', width: '140', align: 'center',},
    { istrue: true, prop: 'tools_PropStatistics', label: '道具', width: '140', align: 'center', },
    { istrue: true, prop: 'monet_PropStatistics', label: '金额', width: '140', align: 'center',},
    { istrue: true, prop: 'user_PropStatistics', label: '使用人', width: '140', align: 'center', },
    { istrue: true, prop: 'marks_PropStatistics', label: '备注', width: '140', align: 'center',},
    { istrue: true, prop: 'order1_PropStatistics', label: '订单凭证', type:'image', width: '140', align: 'center' },
    { istrue: true, prop: 'order2_PropStatistics', label: '订单凭证', type:'image', width: '140', align: 'center' },

    { istrue: true, prop: 'order3_PropStatistics', label: '支付凭证', type:'image', width: '140', align: 'center'},
    { istrue: true, prop: 'order4_PropStatistics', label: '支付凭证', type:'image', width: '140', align: 'center'},
];
export default {
    name: 'PropStatistics',
    components: {vxetablebase},
    data() {
        return {
            that: this,  
            tableCols: tableCols, 
            tasklist:[], 
            listLoading: false,
        };
    },

    mounted() {
        
    },

    methods: {
        
    },
};
</script>

<style lang="scss" scoped>
// ::v-deep .vxe-tools--operate{
//     margin-top: -35px !important;
// }
</style>