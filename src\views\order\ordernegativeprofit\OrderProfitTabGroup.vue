<template>
  <el-container style="height:100%;">
      <my-container style="width:99%;">
        <template #header>
          <el-checkbox-group v-model="checkList" @change="onSearch(true)">
              <el-checkbox label="销售额"/>
              <el-checkbox label="成本"/>
              <el-checkbox label="快递费"/>
              <el-checkbox label="利润"/>
          </el-checkbox-group>
        </template>
        <div style="margin-top:20px;margin-left:50px;"
          v-loading="chartsLoading"
          element-loading-text="加载中"
          element-loading-spinner="el-icon-loading">
            <div id="chartsGroup"></div>
        </div>
      </my-container>
  </el-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import { 
  getNegativeProfitGroupCharts,
} from "@/api/order/ordernegativeprofit";
import * as echarts from "echarts";
export default {
  name: 'Roles',
  components: { MyContainer, MyConfirmButton },
  props:{
    filter:{}
  },
  data() {
    return {
      that:this,
      pageLoading: false,   
      chartsLoading:false,
      chartsGroup:null,
      chartsData:null,
      Ylist: [
        { value: 0, unit: "元", label: "销售额" },
        { value: 1, unit: "元", label: "成本" },
        { value: 2, unit: "元", label: "快递费" },
        { value: 3, unit: "元", label: "利润" },
      ],
      checkList:["利润"],
    }
  },
  async mounted() {
    await this.onSearch();
  },
  methods: {    
    //获取查询条件
    getCondition(){
      if (this.filter.timerange&&this.filter.timerange.length>1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      else {
        this.$message({message:"请先选择日期",type:"warning"});
        return false;
      }

      return this.filter;
    },
    //图表搜索 - 运营组
    async onSearch (useCache) {
      var params=this.getCondition();
      if(params===false){
        return false;
      }
      if(!useCache || !this.chartsData){
        this.chartsLoading=true;
        const res = await getNegativeProfitGroupCharts(params);
        this.chartsLoading=false;
        if (!res?.code) {
          return false;
        }
        else{
          this.chartsData=res.data;
        }
      }
      var chartDom = document.getElementById("chartsGroup");
      this.chartsGroup && this.chartsGroup.clear();
      this.chartsGroup = this.chartsGroup ?? echarts.init(chartDom);

      var option = await this.Getoptions(this.chartsData,'运营组分析：');
      await option && this.chartsGroup.setOption(option);
    },
    //折线/柱形图图表配置
    async Getoptions(element,title) {
      var colors = [
        "#5470C6",
        "#c77eb5",
        "#EE6666",
        "#409EFF",
        "#00ae9d",
        "#67C23A",
      ];
      var series = [];
      element.series.forEach((s) => {
        if(this.checkList.indexOf(s.name)<0){
          return;
        }
        series.push({ smooth: true, ...s });          
      });
      var legendData = element.legend||[];
      var yAxis = [];
      var left = true;
      var leftOffset = 0;
      var rightOffet = 0;
      var ii = 0;
      this.Ylist.forEach((s) => {
        var isShow=this.checkList.indexOf(s.label)!=-1;
        yAxis.push({
          type: "value",
          name: s.label,
          show: isShow,
          axisLabel: {
            formatter: "{value}" + s.unit,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: colors[ii++],
            },
          },
          position: left ? "left" : "right",
          offset: left ? leftOffset : rightOffet,
        });
        left ? (leftOffset += 50) : (rightOffet += 50);
        left = !left;
      });
      var option = {
        title: { text: title },
        tooltip: {
          trigger: "axis",
          textStyle: { align: "left" },
        },
        legend: {
          formatter: function (name) {
            return echarts.format.truncateText(
              name,
              200,
              "10px Microsoft Yahei",
              "..."
            );
          },
          tooltip: {
            show: true,
          },
          data: legendData,
          type: "scroll",
          pageIconColor: "#409EFF",
          pageIconInactiveColor: "#909399",
          width: "75%",
        },
         grid: {
          left: "5%",
          right: "5%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            magicType: { show: true, type: ["line", "bar"] },
            //restore: { show: false },
          },
        },
        xAxis: {
          type: "category",
          data: element.xAxis,
          axisLabel: {
            interval:0,
            rotate:40  
          },
        },
        yAxis: yAxis,
        series: series,
      };
      return option;
    },  
  }
}
</script>
<style scoped>
  #chartsGroup{
    width:1200px;
    height: 600px;
    margin-top:20px;
  }
</style>
