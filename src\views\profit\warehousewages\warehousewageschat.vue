<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :model="filterwarehouseWages" @submit.native.prevent :inline="true">
                <el-form-item style="padding: 0;margin: 0;">
                    <el-date-picker style="width: 210px" v-model="filterwarehouseWages.timerange"
                        :picker-options="pickerOptions" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        @change="showchartWarehouseWages" :clearable="false"></el-date-picker>
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0;">
                    <el-radio-group v-model="filterwarehouseWages.wareDataType" @input="onWareDataTypeChange">
                        <el-radio-button label="分仓" name="wareDataType">分仓</el-radio-button>
                        <el-radio-button label="各仓对比" name="wareDataType">各仓对比</el-radio-button>
                    </el-radio-group>
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0;" v-if="(filterwarehouseWages.wareDataType == '分仓')">
                    <el-select v-model="filterwarehouseWages.warehouseCode" style="width: 200px" size="mini"
                        @change="showchartWarehouseWages" placeholder="仓库" clearable>
                        <el-option v-for="item in warehouseWagesWarehouseList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0;" v-if="(filterwarehouseWages.wareDataType == '各仓对比')">
                    <el-select v-model="filterwarehouseWages.lineName" style="width: 110px"
                        @change="showchartWarehouseWages" size="mini" clearable filterable placeholder="线条">
                        <el-option label="出勤数量" value="出勤数量" />
                        <el-option label="仓发件量" value="仓发件量" />
                        <el-option label="工资总和" value="工资总和" />
                        <el-option label="出仓成本" value="出仓成本" />
                        <el-option label="计件计时比(人数)" value="计件计时比(人数)" />
                    </el-select>
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0;">
                    <el-select v-model="filterwarehouseWages.workType" style="width: 80px" size="mini"
                        @change="showchartWarehouseWages" clearable placeholder="班次">
                        <el-option label="白班" value="白班"></el-option>
                        <el-option label="晚班" value="晚班"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0;">
                    <el-select v-model="filterwarehouseWages.postName" style="width: 110px"
                        @change="showchartWarehouseWages" size="mini" clearable filterable placeholder="一级岗位">
                        <el-option v-for="item in warehouseWagesOnePostNameList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0;">
                    <el-input v-model.trim="filterwarehouseWages.workItem" placeholder="二级岗位名称" style="width:110px;"
                        clearable maxlength="20" @change="showchartWarehouseWages" />
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0;">
                    <el-select v-model="filterwarehouseWages.showType" style="width: 100px" size="mini"
                        @change="showchartWarehouseWages" clearable placeholder="计件计时">
                        <el-option label="计件" value="计件"></el-option>
                        <el-option label="计时" value="计时"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0;">
                    <el-select v-model="filterwarehouseWages.userPostType" style="width: 100px" size="mini"
                        @change="showchartWarehouseWages" clearable placeholder="员工岗位类型">
                        <el-option label="普通员工" value="普通员工"></el-option>
                        <el-option label="管理员" value="管理员"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item style="padding: 0;margin-left: 100px;">
                    <span
                        style="margin-right: 20px;font-weight: bold; font-size: 18px;color: #333333;font-family: PingFang SC;">
                        仓库薪资
                    </span>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <buscharr v-if="warehouseWagesData.visible" :analysisData="warehouseWagesData.data"
                ref="warehouseWagesDataBuscharr">
            </buscharr>
        </template>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MySearch from "@/components/my-search";
import buschar from '@/components/Bus/buschar';
import buscharr from '@/components/Bus/buscharforShooting.vue';
import MySearchWindow from "@/components/my-search-window";
import { getTbWarehouseList, getOnePostNameList, getWarehouseWagesComputeChat3, getWarehouseWagesComputeChat4 } from '@/api/profit/warehousewages';

const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");
export default {
    name: 'warehousewageschat',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, buschar, buscharr },
    props: ['myWarehouseList', 'myOnePostNameList'],
    data() {
        return {
            that: this,
            pickerOptions: {
                shortcuts: [
                    {
                        text: '昨天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime());
                            picker.$emit('pick', [start, start]);
                        }
                    }, {
                        text: '近三天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime());
                            const end = new Date(new Date(tdate.toLocaleDateString()));
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一周',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 5);
                            const end = new Date(new Date(tdate.toLocaleDateString()).getTime() + 3600 * 1000 * 24 * 5);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一个月',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 31);
                            console.log("获取前一个月的时间", tdate.getDay());
                            const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
            },
            listLoading: false,
            pageLoading: false,
            filterwarehouseWages: {
                startDate: null,
                endDate: null,
                timerange: [startTime, endTime],
                wareDataType: "分仓",
                warehouseCode: null,
                postName: null,
                lineName: null,
            },
            warehouseWagesWarehouseList: [],
            warehouseWagesOnePostNameList: [],
            warehouseWagesData: { visible: true, title: "", data: {} },
        };
    },
    async mounted() {
        //仓库薪资
        await this.getWarehouseList();
        await this.getOnePostNameList();
        await this.showchartWarehouseWages();
    },
    computed: {
    },
    methods: {
        async getWarehouseList() {
            this.warehouseWagesWarehouseList = [];
            const res = await getTbWarehouseList();
            if (res && res.length > 0) {
                for (let i = 0; i < res.length; i++) {
                    this.warehouseWagesWarehouseList.push({ label: res[i].name, value: res[i].wms_co_id })
                }
            }
        },
        async getOnePostNameList() {
            this.warehouseWagesOnePostNameList = [];
            const res = await getOnePostNameList();
            if (res && res.length > 0) {
                for (let i = 0; i < res.length; i++) {
                    this.warehouseWagesOnePostNameList.push({ label: res[i], value: res[i] })
                }
            }
        },
        onWareDataTypeChange(e) {
            this.filterwarehouseWages.wareDataType = e;
            if (e == "分仓") {
                this.filterwarehouseWages.lineName = null;
            }
            else if (e == "各仓对比") {
                this.filterwarehouseWages.warehouseCode = null;
            }
            this.showchartWarehouseWages();
        },
        async showchartWarehouseWages() {
            // this.filterwarehouseWages.startDate = null;
            // this.filterwarehouseWages.endDate = null;
            if (this.filterwarehouseWages.timerange && this.filterwarehouseWages.timerange.length > 1) {
                this.filterwarehouseWages.startDate = this.filterwarehouseWages.timerange[0];
                this.filterwarehouseWages.endDate = this.filterwarehouseWages.timerange[1];
            }
            else {
                this.$message({ message: "请先选择日期", type: "warning" });
                return false;
            }
            console.log(this.filterwarehouseWages, 'showchartWarehouseWages-params')
            let that = this;
            if (this.filterwarehouseWages.wareDataType == "分仓") {
                this.filterwarehouseWages.lineName = null;
                const res = await getWarehouseWagesComputeChat3(this.filterwarehouseWages).then(res => {
                    that.warehouseWagesData.visible = true;
                    that.warehouseWagesData.data = res;
                    that.$refs.warehouseWagesDataBuscharr.initcharts();
                    console.log(res);
                })
            }
            else if (this.filterwarehouseWages.wareDataType == "各仓对比") {
                this.filterwarehouseWages.warehouseCode = null;
                if (!this.filterwarehouseWages.lineName) {
                    this.filterwarehouseWages.lineName = '出仓成本'
                }
                const res = await getWarehouseWagesComputeChat4(this.filterwarehouseWages).then(res => {
                    that.warehouseWagesData.visible = true;
                    that.warehouseWagesData.data = res;
                    that.$refs.warehouseWagesDataBuscharr.initcharts();
                    console.log(res);
                })
            }
        },
    },
};
</script>

<style lang="scss" scoped></style>
