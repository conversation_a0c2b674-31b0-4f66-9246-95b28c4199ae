<template>
  <div style="height: 100%;">
       <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' @cellclick='cellclick'
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='false'
              :loading="listLoading">
          <template slot='extentbtn'>
            <el-button-group>
              <el-button type="text" size="medium" disabled>5天内是否更改标题:</el-button>
              <el-button style="padding: 0;margin: 0;">
                 <el-select v-model="filter1.hasEditProName" placeholder="请选择">
                   <el-option value label="请选择"/>
                   <el-option value="false" label="否"/>
                   <el-option value="true" label="是"/>
                 </el-select>
              </el-button>
              <el-button type="text" size="medium" disabled>关键词:</el-button>
              <el-button style="padding: 0;margin: 0;">
                  <el-input v-model="filter1.keyWords" placeholder="输入关键词，多个关键词用,隔开" style="width:500px"/>
              </el-button>
              <el-button type="primary" @click="onfresh">刷新</el-button>
              <el-button type="primary" @click="onExport">导出</el-button>
            </el-button-group>
         </template>
       </ces-table>
      <div height style="padding:0px 0px 5px 5px;">
          <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
       </div>
 
    <el-dialog title="宝贝更改记录" :visible.sync="dialogTableVisible" width="80%">
      <div>
        <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="日期:">
            <el-date-picker v-model="filter2.timerange" type="datetimerange"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" 
                start-placeholder="开始日期" end-placeholder="结束日期"
           ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearchProChange">查询</el-button>
        </el-form-item>
      </el-form>
          <el-table :data="changelist" height="300px">
            <el-table-column property="date" label="日期" width="100">
              <template slot-scope="scope">
                <span>{{formatTime1(scope.row.date,'YYYY-MM-DD')}}</span>
              </template>
            </el-table-column>
            <el-table-column property="proCode" label="宝贝ID" width="120"></el-table-column>
            <el-table-column property="typeStr" label="类型" width="130"></el-table-column>
            <el-table-column property="oldName" label="更改前名称"></el-table-column>
            <el-table-column property="newName" label="更改后名称"></el-table-column>
          </el-table>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {pageProductMonitor,exportProductMonitor,productChangeList} from '@/api/operatemanage/operate'
import MyContainer from '@/components/my-container/nofooter'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/tablev2.vue";
import {formatTime,formatLink,formatProName} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'proName',label:'商品信息', width:'350',type:'html', formatter:(row)=>formatProName(row.proName,row.editProNameCount)},
      {istrue:true,prop:'shopName',label:'店铺名', width:'150'},
      {istrue:true,prop:'proCode',label:'商品ID', width:'130',type:'html', formatter:(row)=>formatLink(row.proCode,`https://detail.tmall.com/item.htm?id=${row.proCode}`)},
      {istrue:true,prop:'date',label:'日期', width:'100',formatter:(row)=>formatTime(row.date,'YYYY-MM-DD')},
      {istrue:true,prop:'amont',label:'交易金额', width:'110',sortable:'custom'},
      {istrue:true,prop:'collectors',label:'访客人数', width:'110',sortable:'custom'},
      {istrue:true,prop:'searches',label:'搜索人数', width:'110',sortable:'custom'},
      {istrue:true,prop:'collectors',label:'收藏人数', width:'110',sortable:'custom'},
      {istrue:true,prop:'addBuyers',label:'加购人数', width:'110'},
      {istrue:true,prop:'paymentRate',label:'支付转化率', width:'120',sortable:'custom'},
      {istrue:true,prop:'payments',label:'支付人数', width:'110',sortable:'custom'},
      {istrue:true,prop:'price',label:'客单价', width:'100',sortable:'custom'},
      {istrue:true,prop:'uv',label:'uv价值', width:'100',sortable:'custom'},
      {istrue:true,prop:'searchRate',label:'搜索占比', width:'110',sortable:'custom'},
      {istrue:true,prop:'collectRate',label:'收藏率', width:'100',sortable:'custom'},
      {istrue:true,prop:'addBuyRate',label:'加购率', width:'100',sortable:'custom'}
     ];
const tableHandles=[
        //{label:"批量删除", handle:(that)=>that.onbacthDelete()},
        //{label:"导出", handle:(that)=>that.onExport()},
        //{label:"刷新", handle:(that)=>that.getlist()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
   props:{
       filter: { }
     },
  data() {
    return {
      formatTime1:formatTime,
      that:this,
      filter1:{
        keyWords:"",
        hasEditProName:null
      },
      filter2:{
        proCode:"",
        startDate: null,
        endDate: null,
        timerange:null,
      },
      list: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"id",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [], 
      listLoading: false,
      pageLoading: false,
      dialogTableVisible:false,
      changelist:[]
    }
  },
  mounted() {
     this.onSearch()
  },
  beforeUpdate() {
   // console.log('update')
  },
  methods: {
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async onfresh() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var params0={...this.filter, ...this.filter1};
      var hasparm=false;
      var arry= Object.keys(params0)
      if (arry.length==0)  return;

      for (let key of Object.keys(params0)) {
        if(params0[key])
            hasparm=true;
      }
      if(!hasparm) return;

      var pager = this.$refs.pager.getPager()
      const params = {
        ...pager,
        ...this.pager,
        ... params0
      }
      this.listLoading = true
      const res = await pageProductMonitor(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      //this.summaryarry=res.data.summary;
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   async onbacthDelete() {
      if (this.selids.length==0) {
       this.$message({type: 'warning',message: '请先选择!'});
       return;
      } 
      this.$confirm('确认删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
            const res = await batchDeleteExpressFreightAP({ids:this.selids.join()})
            if (!res?.success) {return }
            this.$message({type: 'success',message: '删除成功!'});
            this.getlist()
        }).catch(() => {
          this.$message({type: 'info',message: '已取消删除'});
        });
    },
    selsChange: function(sels) {
      this.sels = sels
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
  async cellclick(row, column, cell, event){
     if (column.property=='proName'&& row.editProNameCount>0) {
       this.dialogTableVisible=true;
       this.filter2.proCode=row.proCode;
        let dateend = new Date(row.date)
        var end =  formatTime(dateend,'YYYY-MM-DD')
        var datestart = new Date(dateend.setDate(dateend.getDate()-5));
        var beg = formatTime(datestart,'YYYY-MM-DD')    
        this.filter2.timerange = [beg,end]
        await this.onSearchProChange();
     }
    },
  async onSearchProChange(){
    if (this.filter2.timerange) {
        this.filter2.startDate = this.filter2.timerange[0];
        this.filter2.endDate = this.filter2.timerange[1];
      }
      var res=await  productChangeList(this.filter2);
      if (!res?.success) {
        return
      }
      const data = res.data
      this.changelist = data
    },
  async onExport() {
      var hasparm=false;
      var arry= Object.keys(this.filter)
      if (arry.length==0)  return;
      for (let key of Object.keys(this.filter)) {
        if(this.filter[key])
          hasparm=true;
      }
      if(!hasparm) {
        this.$message({message: "请先选择查询条件！",type: "warning",});
        return;
      }
      const params = {...this.pager, ... this.filter}
      var res= await exportProductMonitor(params);
      if(!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download','市场排行数据_' + new Date().toLocaleString() + '.xlsx' )
      aLink.click()
     }
  }
}
</script>
<style>
.leftbadge {
    background-color: #9c9797;
    border-radius: 12px;
    color: #fff;
    display: inline-block;
    font-size: 12px;
    height: 18px;
    line-height: 12px;
    padding: 0 6px;
    text-align: center;
    white-space: nowrap;
    border: 1px solid #fff;
}
.redcolor {
  color: red;
}
.greencolor {
  color:green;
}
</style>