<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select v-model="ListInfo.goodsState" clearable placeholder="产品状态" class="publicCss">
                    <el-option v-for="item in goodsStatelist" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <el-select v-model="ListInfo.internationalType" clearable placeholder="国内跨境" class="publicCss">
                    <el-option key="0" label="国内" value="0"></el-option>
                    <el-option key="1" label="跨境" value="1"></el-option>
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" :disabled="isExport" @click="onExport">导出</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="table.list" :table-cols="tableCols" :is-selection="false"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%;height: 400px; margin: 0;"
            :isNeedExpend="false" @sortchange="sortchange" />
        <template #footer>
            <my-pagination ref="pager" :total="table.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>

import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import request from '@/utils/request'
import { formatLinkProCode } from "@/utils/tools";

const tableCols = [
    { sortable: 'custom', istrue: true, width: '160', align: 'center', label: '竞品Id', prop: 'goodsCompeteId', type: 'html', formatter: (row) => formatLinkProCode(0, row.goodsCompeteId) },
    { sortable: 'custom', istrue: true, width: '140', align: 'center', label: '产品状态', prop: 'goodsStateStr' },
    { sortable: 'custom', istrue: true, width: '140', align: 'center', label: '国内跨境', prop: 'internationalTypeStr' },
    { sortable: 'custom', istrue: true, width: 'auto', align: 'center', label: '竞品标题', prop: 'goodsCompeteName' },
    { sortable: 'custom', istrue: true, width: '180', align: 'center', label: '审核备注', prop: 'spReason' },
    { sortable: 'custom', istrue: true, width: '180', align: 'center', label: '审核时间', prop: 'spTime' },
];


export default {
    name: 'WarehousingOrderVideoRejectRecord',
    components: {MyContainer,vxetablebase},
    props: {
        api: {
            type: String,
            default: '/api/inventory/PurchaseSummary/InWms/'
        },
        detailsInfo: {
            type: Object,
            default: {}
        }
    },
    data() {
        return {
            that: this,
            tableCols: tableCols,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false,
                goodsState: null,
                internationalType: null
            },
            total: 0,
            loading: true,
            table: {
                list: [],
                total: 0
            },
            formatLinkProCode: formatLinkProCode,
            goodsStatelist: [
                { label: '新品', value: 1 },
                { label: '老品补SKU', value: 2 },
                { label: '代拍', value: 3 },
            ],
            isExport: false,
        }
    },
    async mounted() {
        await this.getList();
    },
    methods: {
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }

            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}GetHotSaleGoodsBuildDocAuditRecord`, { ...this.ListInfo, ...this.detailsInfo })
                if (success) {
                    this.table = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList();
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList();
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList();
            }
        },
        async onExport() {

            var param = {...this.ListInfo, ...this.detailsInfo};

            this.isExport = true
            await request.post(`${this.api}ExportHotSaleGoodsBuildDocAuditRecord`, param, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        }
    }
}

</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
