<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                
            </el-button-group>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn="false"  
          :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='tableData' 
          :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:98%;margin: 0">
        </ces-table>
        <!--分页-->
        <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
        </template>
    </my-container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import MyContainer from '@/components/my-container';
import cesTable from '@/components/Table/table.vue';
import {formatPlatform,formatYesornoBool,formatLinkProCode,platformlist} from "@/utils/tools";
import { 
    combineGoodsRecord
} from "@/api/operatemanage/base/basicgoods"

const tableCols =[

      {istrue:true,prop:'combineCode',label:'组合编码', width:'300',sortable:'custom'},
      {istrue:true,prop:'operType',label:'执行的操作',sortable:'custom', width:'200',formatter:(row)=> row.operType==0?"创建":row.operType==1?"申请调拨":row.operType==2?"调拨成功":row.operType==3?"检测":row.operType==4?"同步组合编码":""},
      {istrue:true,prop:'operer',fix:true,label:'操作人', width:'200',sortable:'custom'},
      {istrue:true,prop:'operTime',label:'操作时间', width:'200',sortable:'custom'},
      {istrue:true,prop:'reMark',label:'操作备注',sortable:'custom', width:'400'},
      
];

const tableHandles=[
       // {label:"导出", handle:(that)=>that.onExport()},
      ];

const startDate = formatTime(dayjs().subtract(1,'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default ({
    name:"Users",
    components:{MyContainer,cesTable},
    data(){
        return {
          
            that:this, 
            tableCols:tableCols,
            tableHandles:tableHandles,
            tableData:[],
            total: 0,
            pager:{OrderBy:"combineCode",IsAsc:false},
            listLoading: false,
            pageLoading: false,
            summaryarry:{},    
            sels:[],

        };
    },
    props:{
        filter:{
            combineCode:null,
        },   
    },

    methods:{
        async sortchange(column){
            if(!column.order)
                this.pager={};
            else
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
            await this.onSearch();
            },
            async onSearch(){
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        async getList(){
            var that=this;
            this.listLoading=true;
            var pager = this.$refs.pager.getPager();
           
            const params = {...pager,...this.pager,...this.filter};
            const res = await combineGoodsRecord(params).then(res=>{
                that.total = res.data?.total;
               
                that.tableData = res.data?.list;
                that.summaryarry=res.data?.summary;               
            });
            this.listLoading=false;
        },
        
    }
})
</script>
<style scoped>
    ::v-deep .el-table__fixed-footer-wrapper tbody td{
        color:blue;
    }
</style>

