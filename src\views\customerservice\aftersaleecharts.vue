<template>
  <div>
    <el-row>
      <el-col :span="6">
        <div id="saleafterretundanalysispie" :style="{height:height,width:width}" /> 
      </el-col>
      <el-col :span="18">  
        <div id="saleafterretundanalysisline" :style="{height:height,width:width}" />
     </el-col>
    </el-row>
  </div>
</template>
<script>
import {GetSaleAfterResonLevel1AnalysisResponse,GetSaleAfterResonLevel2AnalysisResponse} from '@/api/customerservice/saleafteranalysis';
import container from '@/components/my-container/noheader'
import buschar from '@/components/Bus/buschar'
import pieChart from '@/views/admin/homecomponents/PieChart'
import * as echarts from 'echarts';
export default {
  name: 'Roles',
  components: {container,buschar,pieChart},
  props:{
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
  },
  data() {
    return {
        parms:{},
        buscharData:{},
        righttext:'售后分析原因'
      }
  },
  async mounted() {
  },
  beforeUpdate() { },
  methods: {
    async onSearch() {
      await this.getAnalysis(this.parms)
    },
    async getAnalysis(parms) {
      this.parms=parms
      let res = await GetSaleAfterResonLevel1AnalysisResponse(this.parms);
      await this.initchartsspie(res.data);
      await this.initchartsline(res.data);
    },
    async GetSaleAfterResonLevel2AnalysisResponse(resonlevel1) {
      console.log(this)
      console.log(this.parms)
      this.parms.resonLevel1=resonlevel1
      let res = await GetSaleAfterResonLevel2AnalysisResponse(this.parms);
      await this.initchartsline(res.data);
    },
    async initchartsspie(analysisData) {
      let that=this;
      this.$nextTick(() => {
        var chartDom1 = document.getElementById('saleafterretundanalysispie');
        var myChart2 = echarts.init(chartDom1);
        myChart2.clear();
        var option1 = that.getoptionspie(analysisData);
        myChart2.setOption(option1);
        myChart2.off('click')
        //添加点击事件
        myChart2.on('click',(a,b)=>{
          let index=0;
          if(a.name=='仓库部原因') index=0;
          else if(a.name=='运营部原因') index=1;
          else if(a.name=='采购部原因') index=2;
          else if(a.name=='产品问题') index=3;
          else if(a.name=='公司承担') index=4;
          else if(a.name=='快递员因') index=5;
          else if(a.name=='厂家原因') index=6;
          else if(a.name=='客服部原因') index=7;
          else if(a.name=='其他原因') index=8;
          this.righttext=a.name;
          that.GetSaleAfterResonLevel2AnalysisResponse(a.index)
        })
      });
    },
   async initchartsline(analysisData) {
      let that=this;
     this.$nextTick(() => {
        var chartDom1 = document.getElementById('saleafterretundanalysisline');
        var myChart1 = echarts.init(chartDom1);
        myChart1.clear();
        var option1 = that.getoptionsline(analysisData);
        myChart1.setOption(option1);
      });
    },
    getoptionsline(element){
      var series=[]
      element.series.forEach(s=>{
         series.push({smooth: true, ...s})
      })
      var yAxis=[]
      element.yAxis.forEach(s=>{
        yAxis.push({type: 'value',minInterval:10,offset:s.offset,splitLine:s.splitLine,position:s.position,name: s.name,axisLabel: {formatter: '{value}'+s.unit}})
      }) 
      var selectedLegend={};
      if(element.selectedLegend){
       element.legend.forEach(f=>{
          if(!element.selectedLegend.includes(f)) selectedLegend[f]=false
        })
      }
      var option = {
        title: {text: element.title},
        tooltip: {trigger: 'axis'},
        legend: {
           selected:selectedLegend,
           data: element.legend,
           top: '7%'
         },
        grid: {
            top: '20%',
            left:'5%',
            right: '4%',
            bottom: '5%',
            containLabel: false
        },
        title: {
          left: 'center',
          text: this.righttext
        },
        toolbox: {feature: {
        }},
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis:  yAxis,
        series:  series
    };
    return option;
   },
   getoptionspie(element){
        var option={tooltip:{trigger: 'item',formatter: '{a} <br/>{b} : {c} ({d}%)'},
        title: {
          left: 'center',
          text: '售后分析原因'
        },
        series: [
              {
                name: '售后分析原因占比',
                type: 'pie',
                radius: [15, 100],
                center: ['45%', '55%'],
                data: [
                ],
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                }
              }
            ],
        };
        element.pieSeries.forEach(s=>{
          option.series[0].data.push({value: s.value, name: s.name})
        })
        return option;
    },     
  }
}
</script>
