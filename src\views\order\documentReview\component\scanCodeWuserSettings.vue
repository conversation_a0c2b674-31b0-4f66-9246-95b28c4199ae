<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-select v-model="filterInfo.scanUserId" placeholder="请模糊输入并选择用户" clearable filterable remote
          :filter-method="searchReferrer" class="publicCss">
          <el-option v-for="item in ReferrerOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="filterInfo.wmsId" clearable filterable placeholder="请选择仓库" class="publicCss">
          <el-option v-for="item in warehouselist" :key="item.name" :label="item.name" :value="item.wms_co_id" />
        </el-select>
        <!-- <el-button type="primary" @click="getList('search')">搜索</el-button> -->
        <el-button type="primary" @click="onAddUser">新增用户</el-button>
        <el-button type="primary" @click="onBatchDelete">批量删除</el-button>
      </div>
    </template>
    <vxetablebase ref="table" :id="'scanCodeWuserSettings202502131850'" :tablekey="'scanCodeWuserSettings202502131850'"
      :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true" :has-seq="false"
      :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false" :is-select-column="true"
      :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%" :showsummary="data.summary ? true : false"
      :summaryarry="data.summary" @sortchange="sortchange" @select="selectChexkBox" :isNeedExpend="false" />
    <template #footer>
      <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/WmsExpressScan/User/'
import { mergeTableCols } from '@/utils/getCols'
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser'
import { getAllWarehouse } from '@/api/inventory/warehouse'
export default {
  name: "scanCodeWuserSettings",
  components: {
    MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan,
  },
  data() {
    return {
      selectList: [],
      filterInfo: {
        scanUserId: '',
        scanUserName: '',
        wmsId: '',
        wmsName: '',
        id: '',
      },
      defaultId: [],
      ReferrerOptions: [],
      warehouselist: [],
      api,
      platformlist,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        summarys: [],
      },
      data: {},
      chatProp: {
        chatDialog: false, // 趋势图弹窗
        chatTime: null, // 趋势图时间
        chatLoading: true, // 趋势图loading
        data: [], // 趋势图数据
      },
      timeRanges: [],
      tableCols: [],
      tableData: [],
      total: 0,
      loading: true,
      pickerOptions,
      isExport: false
    }
  },
  async mounted() {
    this.getWareHouse()
    await this.getCol();
    await this.getList()
  },
  methods: {
    onBatchDelete() {
      if (this.selectList.length == 0) {
        this.$message.error('请选择要删除的项')
        return
      }
      this.$confirm('确定删除选中项？?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = this.selectList.map(item => item.id)
        request.post(`${this.api}DeleteDatas`, params).then(res => {
          if (res.success) {
            this.$message.success('删除成功')
            this.selectList = []
            this.getList()
          } else {
            this.$message.error('删除失败')
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    async getWareHouse() {
      const res = await getAllWarehouse({ orderBy: 'name' })
      var data = res.data.filter((x) => x.name.indexOf('代发') < 0)
      data.sort((a, b) => {
        return a.name.localeCompare(b.name, 'zh')
      })
      this.warehouselist = data
    },
    async onAddUser() {
      if (!this.filterInfo.scanUserId) {
        this.$message.error('请选择用户')
        return
      }
      if (!this.filterInfo.wmsId) {
        this.$message.error('请选择仓库')
        return
      }
      const params = {
        ...this.filterInfo,
        scanUserName: this.ReferrerOptions.find(item => item.value == this.filterInfo.scanUserId)?.name || '',
        wmsName: this.warehouselist.find(item => item.wms_co_id == this.filterInfo.wmsId)?.name || '',
      }
      const { data, success } = await request.post(`${this.api}AddData`, params)
      if (!success) return
      this.$message.success('新增成功')
      this.filterInfo.scanUserId = ''
      this.filterInfo.wmsId = ''
      this.getList()
    },
    async searchReferrer(e) {
      //如果e的长度大于200,就提示
      if (e.length > 200) {
        this.$message.error('最多输入200个字符')
        return
      }
      // 如果输入为空，清空下拉框
      if (e === '' || e === null || e === undefined) {
        this.ReferrerOptions = []
        return
      }
      const { data } = await QueryAllDDUserTop100({ keywords: e })
      if (data) {
        this.ReferrerOptions = data.map(item => {
          return {
            value: item.userId,
            label: item.userName + ' - ' + `(${item.deptName})`,
            name: item.userName
          }
        })
      }
    },
    async getCol() {
      const { data, success } = await request.post(`${this.api}GetColumns`)
      if (success) {
        data.unshift({ type: 'checkbox', label: '' })
        data.forEach(item => {
          if (item.prop == 'orderNoInner') {
            item.type = 'orderLogInfo'
            item.orderType = 'orderNoInner'
          }
          if (item.prop == 'proCode') {
            item.type = 'html'
            item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
          }
        })
        this.tableCols = mergeTableCols(data)
        this.ListInfo.summarys = data
          .filter((a) => a.summaryType)
          .map((a) => {
            return { column: a["sort-by"], summaryType: a.summaryType };
          });
      }
    },
    selectChexkBox(val) {
      this.selectList = val
    },
    async getList(type) {
      if (type === "search") {
        this.ListInfo.currentPage = 1;
        this.$refs.pager.setPage(1);
      }
      this.loading = true;
      // 使用时将下面的方法替换成自己的接口
      try {
        const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
        if (success) {
          this.data = data;
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.loading = false;
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 180px;
    margin: 0 5px 0 0;
  }
}
</style>
