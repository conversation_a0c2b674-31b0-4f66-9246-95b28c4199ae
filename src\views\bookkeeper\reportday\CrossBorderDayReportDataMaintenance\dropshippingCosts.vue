<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.dataStartDate" :endDate.sync="ListInfo.dataEndDate"
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.shopName" placeholder="店铺" maxlength="50" clearable
                    class="publicCss" />
                <el-button style="padding: 0;border: none;float: left;">
                    <inputYunhan :key="'1'" :keys="'one'" :width="'220px'" ref="" :inputt.sync="ListInfo.orderNos"
                        v-model.trim="ListInfo.orderNos" placeholder="订单号/若输入多条请按回车" :clearable="true"
                        @callback="callbackOrderNos" title="订单号" @entersearch="entersearch" :maxRows="100">
                    </inputYunhan>
                </el-button>
                <el-button style="padding: 0;border: none;float: left;">
                    <inputYunhan :key="'2'" :keys="'two'" :width="'220px'" ref="" :inputt.sync="ListInfo.subOrderNos"
                        v-model.trim="ListInfo.subOrderNos" placeholder="子订单号/若输入多条请按回车" :clearable="true"
                        @callback="callbackSubOrderNos" title="子订单号" @entersearch="entersearch" :maxRows="100">
                    </inputYunhan> 
                </el-button>
                <el-button style="padding: 0;border: none;float: left;">
                    <inputYunhan :key="'3'" :keys="'three'" :width="'220px'" ref="" :inputt.sync="ListInfo.goodsCodes"
                        v-model.trim="ListInfo.goodsCodes" placeholder="商品编码/若输入多条请按回车" :clearable="true"
                        @callback="callbackGoodsCodes" title="商品编码" @entersearch="entersearch" :maxRows="100">
                    </inputYunhan>
                </el-button>
                <el-button style="padding: 0;border: none;float: left;">

                    <inputYunhan :key="'4'" :keys="'four'" :width="'220px'" ref="" :inputt.sync="ListInfo.SKCIDs"
                        v-model.trim="ListInfo.SKCIDs" placeholder="货品SKC ID/若输入多条请按回车" :clearable="true"
                        @callback="callbackSKCIDs" title="货品SKC ID" @entersearch="entersearch" :maxRows="100">
                    </inputYunhan>
                </el-button>

                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button
                    @click="importProps" type="primary" icon="el-icon-share" @command="handleCommand"> 导入
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item class="Batcoperation" style="padding: 0 25px"
                            command="a">下载模版</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
                <el-button type="primary" @click="onExport" style="margin-left: 5px;">导出</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :showsummary='true'
            :summaryarry='summaryarry' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
            :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog :visible.sync="visible" width="400px" title="是否预估尾程" center>
            <el-form ref="editNoteForm" :model="editNoteForm">
                <div style="text-align: center;">
                    <el-select v-model="editNoteForm.isEstimateLastFee" placeholder="是否预估尾程"
                        style="width:120px; margin: 0 auto;" class="el-select-content">
                        <el-option v-for="item in depletionTypeList" :key="item.value" :label="item.name"
                            :value="item.value"  style="text-align: center;"/>
                    </el-select>
                </div>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="visible = false">取 消</el-button>
                <el-button type="primary" @click="saveEstimateLastFee()">确 定</el-button>
            </div>
        </el-dialog>

        <!-- 查看日志 -->
        <el-dialog title="操作日志" center :visible.sync="logVisible" v-dialogDrag>
            <vxetablebase :id="'Log20241209'" :tablekey="'Log20241209'" :tableData='logData' :tableCols='logTableCols'
                :loading='logLoading' :border='true' :that="that" height="440px" ref="logtable" :showsummary='false'
                @sortchange='logSortchange'>
            </vxetablebase>
            <my-pagination ref="logPage" :total="logTotal" @get-page="logListGet()" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { GetIssuingCost_BanTuo_TemuAsync, ImportIncidentalExpensesAsync } from '@/api/bookkeeper/reportdayV2'
import { updateIssuingCost_BanTuo_TemuIsEstimateLastFeeAsync, getKJ_OperateLogPageList,issuingCost_BanTuo_Temu_Export } from '@/api/bookkeeper/crossBorderV2'
import inputYunhan from "@/components/Comm/inputYunhan";
import { formatTime } from "@/utils/tools";
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'date', label: '日期',formatter: (row) => formatTime(row.date, "YYYY-MM-DD")  },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopCode', label: '店铺ID', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '订单号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'subOrderNo', label: '子订单号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'trackingNumber', label: '运单号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'skcid', label: '货品SKC ID', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'productQuantity', label: '货品件数', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'dropShippingCost', label: '代发成本', },
    {
        sortable: 'custom', width: 'auto', align: 'center', prop: 'isEstimateLastFee', label: '是否预估尾程', type: 'click',
        handle: (that, row) => that.renderStatus(row),
    },
    {
        type: 'button', label: '操作', width: '150px', btnList: [
            { label: "查看日志", handle: (that, row) => { that.logVisible = true; that.currentId = row.id; that.logListGet('search') } },
        ]
    },
]
const logTableCols = [
    { istrue: true, sortable: 'custom', prop: 'createdTime', label: '操作时间', width: 'auto' },
    { istrue: true, sortable: 'custom', prop: 'content', label: '操作内容', width: 'auto' },
    { istrue: true, sortable: 'custom', prop: 'userName', label: '操作人', width: 'auto' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, inputYunhan
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            fileList: [],
            importLoading: false,
            importVisible: false,
            file: null,
            summaryarry: {},
            editNoteForm: {},
            saveLoading: false,
            visible: false,
            editNoteForm: {
                id: '',
                isEstimateLastFee: '' // 默认值为空
            },
            depletionTypeList: [
                { value: '是', name: '是' },
                { value: '否', name: '否' }
            ],
            logVisible: false,
            //查看日志
            logVisible: false,
            logData: [],
            logTableCols: logTableCols,
            logLoading: false,
            logTotal: 0,
            logEnum: {
                orderBy: '',
                isAsc: true,
            },
            currentId: null,//当前行id
        }
    },
    async mounted() {
        await this.getList();
    },
    methods: {
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            //没有时间就提示
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            form.append("billingType", 3);
            this.importLoading = true
            await ImportIncidentalExpensesAsync(form).then(({ success }) => {
                if (success) {
                    this.$message.success('导入成功')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetIssuingCost_BanTuo_TemuAsync(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.summaryarry = data.summary
                    this.loading = false
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            } 
        },
        async handleCommand(command) {
            switch (command) {
                //下载模版
                case 'a':
                    await this.downLoadFile()
                    break;
            }
        },
        async downLoadFile() {
            window.open("/static/excel/CrossBorderDailyDataMaintenance/代发成本导入模版.xlsx", "_blank");
        },
        //多条查询部分
        async entersearch(val) {
            this.getList('search');
        },
        async callbackOrderNos(val) {
            this.ListInfo.orderNos = val;
        },
        async callbackSubOrderNos(val) {
            this.ListInfo.orderNos = val;
        },
        async callbackGoodsCodes(val) {
            this.ListInfo.goodsCodes = val;
        },
        async callbackSKCIDs(val) {
            this.ListInfo.SKCIDs = val;
        },

        renderStatus(row) {
            this.editNoteForm.id = row.id;
            this.editNoteForm.isEstimateLastFee = row.isEstimateLastFee;
            this.visible = true
        },
        async saveEstimateLastFee() {
            //行内修改预估尾程接口
            if (this.editNoteForm.isEstimateLastFee) {
                let params = {
                    id: this.editNoteForm.id,
                    isEstimateLastFee: this.editNoteForm.isEstimateLastFee,
                }
                this.saveLoading = true
                let res = await updateIssuingCost_BanTuo_TemuIsEstimateLastFeeAsync(params)
                this.saveLoading = false
                this.visible = false
                this.$refs.editNoteForm.resetFields()
                this.getList('search')
                if (res.success) {
                    this.$message({ message: '修改成功', type: "success" });
                } else {
                    this.$message({ message: '修改失败', type: "error" });
                }
            } else {
                this.$message({ message: '请选择需要修改的预估尾程', type: "warning" });
            }
        },
        //查看日志
        async logListGet(type) {
            this.$nextTick(async () => {
                if (type == 'search') {
                    this.$refs.logPage.setPage(1)
                }
                let pager = this.$refs.logPage.getPager()

                let params = {
                    id: this.currentId,
                    typeInt: 1,
                    ...pager,
                    ...this.logEnum,
                }
                this.logLoading = true
                const { data } = await getKJ_OperateLogPageList(params) //该接口
                this.logLoading = false

                this.logTotal = data.total
                this.logData = data.list
            })
        },
        logSortchange({ order, prop }) {
            if (prop) {
                this.logEnum.orderBy = prop
                this.logEnum.isAsc = order.indexOf("descending") == -1 ? true : false
                this.logListGet()
            }
        },
        async onExport() {//导出列表数据；
            var res = await issuingCost_BanTuo_Temu_Export(this.ListInfo);
            if (res?.success) {
                this.$message({ message: res.msg, type: "success" });
            }
    },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>
