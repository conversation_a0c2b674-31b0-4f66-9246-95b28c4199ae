<template>
  <container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="运营组:">
          <el-select v-model="filter.groupId" multiple collapse-tags clearable placeholder="请选择运营组" style="width: 140px"
            filterable>
            <!-- <el-option label="所有" value=""/> -->
            <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="分公司:">
          <el-select filterable v-model="filter.company" @change="changeSetCompany" collapse-tags clearable
            placeholder="分公司" style="width: 100px">
            <el-option key="义乌" label="义乌" value="义乌"></el-option>
            <el-option key="南昌" label="南昌" value="南昌"></el-option>
            <el-option key="其他" label="其他" value="其他"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="采购员:">
          <el-select v-model="filter.brandId" multiple collapse-tags clearable placeholder="请选择采购员" style="width: 140px"
            filterable>
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="审单状态">
          <el-select filterable clearable v-model="filter.isCheckError" placeholder="请选择" style="width: 110px">
            <el-option label="跟进" value="0"></el-option>
            <el-option label="审错" value='1'></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商品编码:">
          <el-input v-model.trim="filter.goodsCode" clearable style="width: 110px" maxlength="500" />
        </el-form-item>
        <el-form-item label="商品名称:">
          <el-input v-model.trim="filter.goodsName" clearable style="width: 120px" maxlength="500" />
        </el-form-item>
        <el-form-item label="架构">
          <el-select filterable v-model.trim="filter.deptIds" clearable multiple collapse-tags style="width: 175px">
            <el-option v-for="item in purchasegrouplist" :key="item.dept_id" :label="item.full_name" :value="item.dept_id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="synchronousOperations">一键同步采购单跟进</el-button>
          <el-button type="primary" @click="exportAbnormalOrder">导出</el-button>
        </el-form-item>
        <el-form-item>
            <el-radio-group v-model="filter.realBrand">
                <el-radio :label="0">全部</el-radio>
                <el-radio :label="1">采购名下</el-radio>
                <el-radio :label="2">非采购名下</el-radio>
            </el-radio-group>
        </el-form-item>
      </el-form>
    </template>
    <ces-table ref="table" :that='that' :isIndex='true' @sortchange='sortchange' @select='selectchange'
      :isSelection='true' @cellclick='cellclick' :hasexpand='true' :tableData='list' :tableCols='tableCols'
      :tableHandles='tableHandles' :loading="listLoading" :showsummary='true' :summaryarry='summaryarry'>
      <template slot='extentbtn'>
        <el-button-group>
          <el-button style="margin: 0;" @click="getimportlist">
            {{ lastUpdateTime }}
          </el-button>
          <el-button style="margin: 0;">
            {{ reasonRate }}
          </el-button>
          <el-button style="margin: 0;">
            {{ summaryAll }}
          </el-button>
        </el-button-group>
      </template>
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <el-drawer title="处理" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
      :visible.sync="editVisible" direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
      <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options" />
      <div class="drawer-footer">
        <el-button @click.native="editVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="editLoading" @click="onEditSubmit" />
      </div>
    </el-drawer>

    <el-dialog :visible.sync="visiblepopover" v-dialogDrag width="60%">
      <orderabnormalgoodscoderecord ref="orderabnormalgoodscoderecord" style="height: 420px">
      </orderabnormalgoodscoderecord>
    </el-dialog>

    <el-popover ref="detailPopover" placement="bottom-end" v-model="visiblepopoverdetail" :reference="prevTarget"
      :key="('detail' + popperFlagdetail.toString())">
      <el-table :data="detaillist">
        <el-table-column width="150" property="firstOrderTime" label="压单日期"></el-table-column>
        <el-table-column width="150" property="warehouseAreaName" label="仓储" show-overflow-tooltip></el-table-column>
        <el-table-column width="120" property="goodsCode" label="商品编码"></el-table-column>
        <el-table-column width="75" property="isCheckError" label="审单状态">
          <template slot-scope="scope">
            <div class="wendang" v-html="formatIsCheckError(scope.row['isCheckError'])"></div>
          </template>
        </el-table-column>
        <el-table-column label="压单数" width="65" property="waitOrderNum">
          <template slot-scope="scope">
            <el-button @click="showOrderDetail(scope.row['goodsCode'], scope.row['wms_id'])" type="text" size="small">{{
              scope.row["waitOrderNum"] }}</el-button>
          </template>
        </el-table-column>
        <el-table-column width="70" property="waitGoodNum" label="压品数"></el-table-column>
        <!-- <el-table-column width="100" property="totalWaitOrderNum" label="累计压单数"></el-table-column> -->
        <!-- <el-table-column width="100" property="totalWaitGoodNum" label="累计压品数"></el-table-column> -->
        <!-- <el-table-column width="80" property="waitDays" label="压单天数"></el-table-column>  -->
      </el-table>

    </el-popover>

    <el-dialog :visible.sync="dialogOrderDetailVisible">
      <orderabnormalorderdetail ref="boxorder"></orderabnormalorderdetail>
    </el-dialog>

    <div class="imgDolg" v-show="imgPreview.show" @click.stop="imgPreview.show = false">
      <i class="el-icon-close" id="imgDolgClose" @click.stop="imgPreview.show = false"></i>
      <img @click.stop="imgPreview.show = true" :src="imgPreview.img" />
    </div>

    <!-- 时间线弹框 -->
    <el-dialog title="" :visible.sync="importtimedialogVisible" v-dialogDrag width="30%">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>当天导入时间记录</span>
        </div>
        <div style="height:400px;overflow-y:auto" class="text item">
          <el-alert v-for="item in importtimelist" :key="item" title="" type="success" :closable="false">
            导入时间 : {{ item.createdTime }}
          </el-alert>
        </div>
      </el-card>
    </el-dialog>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="40%">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="4" action
          accept=".xlsx" :http-request="uploadFile" :on-remove="uploadRemove" :on-change="uploadChange"
          :file-list="fileList">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="submitUpload">{{
              (uploadLoading ? '上传中' : '上传') }} </el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </container>
</template>
<script>
import {
  pageOutOfStockSectionReport,
  queryTimeOutOfStockSectionReportDtlAsync,
  getOutOfStockSectionReportLastTimeAsync,
  getLastUpdateTimeOutOfStockSectionReportAsync,
  getAllOutOfStockSectionReportOderNoAsync,
  getAllOutOfStockSectionReportGoodsCodeAsync,
  editOutOfStockSectionReportFollowRemarkAsync,
  getAllOutOfStockSectionReportReasonRate,
  purchaseOrderFollowUpAsync,
  exportOutOfStockSection
} from '@/api/inventory/abnormal'
import { getPurchaseNewPlanTurnDayDeptList } from '@/api/inventory/purchaseordernew'
import {
  importAbnormalOrderNewAsync
} from '@/api/inventory/abnormalImport'
import { getAllProBrand } from '@/api/inventory/warehouse'
import { formatTime, formatNoLink, formatIsCheckError } from "@/utils/tools";
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import orderabnormalgoodscoderecord from '@/views/inventory/orderabnormalgoodscoderecord'
import orderabnormalorderdetail from '@/views/inventory/orderabnormalOrderDetailnew';
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";
import dayjs from 'dayjs';
const tableCols = [
  { istrue: true, prop: 'outOfStockTimeCurrent', label: '出现负库存数时间', width: '120',  type: 'html', sortable: 'custom', formatter: (row) => formatNoLink(formatTime(row.outOfStockTimeCurrent, 'YYYY-MM-DD HH:mm:ss')) },
  { istrue: true, prop: 'goodsImage', label: '图片', width: '60', type: 'image' },
  { istrue: true, prop: 'sellStock', label: '库存数', width: '60', sortable: 'custom', type: 'html', formatter: (row) => formatNoLink(row.sellStock) },
  { istrue: true, prop: 'goodsCode', label: '商品编码', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'goodsName', label: '商品名称', sortable: 'custom' },
  {
    istrue: true, prop: 'isCheckError', label: '审单状态', width: '70', type: 'html', formatter: (row) =>
      row.isCheckError == 0 ? '<a href="javascript:void(0);" style="color:#606266;">跟进</a>' :
        row.isCheckError == 1 ? '<a href="javascript:void(0);" style="color:#dc0909">审错</a>' : " "
  },
  { istrue: true, prop: 'waitOrderNumCurrent', label: '压单数', width: '60', sortable: 'custom', type: 'html', formatter: (row) => formatNoLink(row.waitOrderNumCurrent) },
  { istrue: true, prop: 'waitGoodNumCurrent', label: '压品数', width: '60', sortable: 'custom' },
  { istrue: true, prop: 'buyPerson', label: '采购人', width: '60' },
  {
    istrue: true, prop: 'buyNo', label: '采购单号', width: '80', type: 'html', formatter: (row) => {
      if (row.buyNo) {
        const res = row.buyNo.split(',');
        let str = '';
        console.log(res, 'buyNo');
        res.forEach(item => {
          str += `<div><a class="color1688">${item}</a></div>`;
        });
        return str;
      } else {
        return '';
      }
    }
  },
  {
    istrue: true, prop: 'aliOrderLink', label: '1688详情', width: '80', type: 'html', formatter: (row) => {
      if (row.abnormalExtList && row.abnormalExtList.length > 0) {
        let str = '';
        row.abnormalExtList.forEach(item => {
          str += item.aliOrderNo ? `<div><a href="https://trade.1688.com/order/new_step_order_detail.htm?orderId=${item.aliOrderNo}#logisticsTabTitle" class="color1688"  target='_blank'>${item.buyNo}</a></div>` : '';
        });
        return str;
      }
    }
  },
  {
    istrue: true, type: 'button', width: '65', btnList: [{
      label: "编辑",
      htmlformatter: (row) => { return `<i class="el-icon-star-on" style="color:${(row.isReportToday == true ? "green" : "red")}"></i>` },
      display: (row) => { return row.isHandle == true; }, handle: (that, row) => that.onHand(row)
    }]
  },
  { istrue: true, prop: 'planArrivalTime', label: '预计到货日期', width: '100', sortable: 'custom', formatter: (row) => formatTime(row.planArrivalTime, 'YYYY-MM-DD') },
  { istrue: true, prop: 'reason', label: '原因', width: '80' },
  { istrue: true, prop: 'remark', label: '解决方案', type: 'editor', width: '300' },
  { istrue: true, prop: 'changeTime', label: '更新时间', width:'auto' },
  { istrue: true, type: 'button', label: '日志', width:'60', btnList: [
    {
      htmlformatter: row => { return `<i class="el-icon-document" style="cursor: pointer;"></i>` },
      handle: (that, row) => that.getrecordlist(row.goodsCode)
    }
  ] },
];
const tableHandles = [
  { label: "导入", handle: (that) => that.startImport() },
  { label: "复制所有审单异常订单号", handle: (that) => that.copyAllAbnormalOderNo() },
  { label: "复制所有审单异常商品编码", handle: (that) => that.copyAllAbnormalGoodsCode() },
  { label: "批量操作", handle: (that) => that.onHandBatch() }
]
export default {
  name: "Users",
  components: { container, cesTable, MyConfirmButton, orderabnormalgoodscoderecord, orderabnormalorderdetail },
  data() {
    return {
      that: this,
      formatIsCheckError: formatIsCheckError,
      filter: {
        timerange: null,
        goodsName: null,
        goodsCode: null,
        brandId: null,
        company: null,
        isCheckError: null,
        realBrand: 0,
        deptIds: []
      },
      goodsCodes: null,
      imgPreview: { img: "", show: false },
      lastUpdateTime: '',
      reasonRate: '',
      summaryAll: '',
      grouplist: [],
      brandlist: [],
      brandlist1: [],
      //parentid: 0,
      list: [],
      detaillist: [],
      importtimelist: [],
      visiblepopover: false,
      prevTarget: null, // 编辑 Popover 的 Reference （参照），用于 popover.js 对齐两个元素
      visiblepopoverdetail: false,
      dialogOrderDetailVisible: false,
      popperFlagdetail: false,
      importtimedialogVisible: false,
      tableCols: tableCols,
      tableHandles: tableHandles,
      pager: { OrderBy: "waitOrderNumCurrent", IsAsc: false },
      summaryarry: {},
      detailstotal: 0,
      total: 0,
      sels: [],
      selids: [],
      fileList: [],
      listLoading: false,
      dialogVisible: false,
      pageLoading: false,
      editVisible: false,
      editLoading: false,
      uploadLoading: false,
      autoform: {
        fApi: {},
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 8 } } } },
        rule: []
      },
      selectList: [],
      purchasegrouplist: []
    };
  },
  watch: {
    value(n) {
      if (n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
      }
    }
  },
  async mounted() {
    if (this.$route.query && this.$route.query.goodsCode) {
      this.filter.goodsCode = this.$route.query.goodsCode
    }
    formCreate.component('editor', FcEditor);
    await this.initform();
    await this.init();
    await this.onSearch();
  },
  methods: {
    formatTime(val) {
      return val ? dayjs(val).format('YYYY-MM-DD') : ''
    },
    synchronousOperations() {
      if (this.selectList.length == 0) return this.$message.error('请选择要操作的数据');
      const arr = this.selectList.map(item => { return { goodsCode: item.goodsCode, buyNos: item.buyNo } });
      console.log(arr, 'buyNos');
      this.$confirm('确定将这些数据同步采购单跟进吗, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success, msg } = await purchaseOrderFollowUpAsync({ followUp: arr });
        if (success) {
          this.$message.success(msg ? msg : '操作成功');
          this.onSearch();
          this.selectList = []
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    },
    async initform() {
      let that = this;
      this.autoform.rule = [{ type: 'hidden', field: 'id', title: 'id', value: '' },
      { type: 'input', field: 'goodsCode', title: '商品编码', value: '', props: { readonly: true } },
      { type: 'input', field: 'goodsName', title: '商品名称', value: '', props: { readonly: true } },
      { type: 'input', field: 'buyPerson', title: '采购负责人', value: '', props: { maxlength: 10, readonly: true } },
      //  {type:'select',field:'isCheckError',title:'审单状态',value:'',validate: [{type: 'boolean', required: true, message:'请选择'}],
      //       options: [{value:null, label:'请选择'},{value:false, label:'正常'},{value:true, label:'异常'}]},
      {
        type: "cascader", field: "_reason", title: "原因", value: [], props: {
          options: [
            { value: '采购原因', label: '采购原因', children: [{ value: '进货（时效、数量）判断失误', label: '进货（时效、数量）判断失误' }, { value: '厂家（改价、发货、物流）异常', label: '厂家（改价、发货、物流）异常' }, { value: '特殊（定制产品、专利产品）', label: '特殊（定制产品、专利产品）' }, { value: '其它原因', label: '其它原因' }] },
            { value: '运营原因', label: '运营原因', children: [{ value: '产品冲量、活动（未告知采购）', label: '新品进货（前两次采购单）' }, { value: '运营给量（判断错误）', label: '运营给量（判断错误）' }, { value: '下架产品出单', label: '下架产品出单' }, { value: '更换厂家衔接问题', label: '更换厂家衔接问题' }] },
            { value: '仓库原因', label: '仓库原因', children: [{ value: '审单原因', label: '审单原因' }, { value: '到货提货不及时', label: '到货提货不及时' }, { value: '到货入库不及时', label: '到货入库不及时' }, { value: '质检入库数量型号错误', label: '质检入库数量型号错误' }, { value: '加工时效超时', label: '加工时效超时' }, { value: '质量问题返厂更换', label: '质量问题返厂更换' }, { value: '仓库盘点、其他出库', label: '仓库盘点、其他出库' }] },
            { value: '市场原因', label: '市场原因', children: [{ value: '疫情原因', label: '疫情原因' }, { value: '天气灾害', label: '天气灾害' }, { value: '其他市场波动', label: '其他市场波动' }] },
            { value: '财务原因', label: '财务原因', children: [{ value: '付款不及时', label: '付款不及时' }, { value: '其他', label: '其他' }] },
          ]
        }
      },
      { type: 'DatePicker', field: 'planArrivalTime', title: '预计到货日期', value: '', validate: [{ type: 'string', required: true, message: '请输入预计到货日期' }], props: { type: 'datetime', format: 'yyyy-MM-dd', placeholder: '预计到货日期', } },
      {
        type: 'editor', field: 'remark', title: '解决方案', value: '', col: { span: 20 }, validate: [{ type: 'string', required: true, message: '请输入解决方案' }],
        props: { maxlength: 400, init: async (editor) => { await that.initeditor(editor) } }
      }
      ]
      this.autoform.rule.forEach(f => {
        if (f.field == 'toUserId1') f.validate = []
        if (f.field == 'toUserId2') f.validate = []
      })
    },
    async initeditor(editor) {
      editor.config.uploadImgMaxSize = 3 * 1024 * 1024
      editor.config.excludeMenus = ['emoticon', 'video']
      editor.config.uploadImgAccept = []
      editor.config.customUploadImg = async function (resultFiles, insertImgFn) {
        var xhr = new XMLHttpRequest()
        var formData = new FormData()
        formData.append('file', resultFiles[0])
        xhr.open('post', '/api/uploadnew/file/UploadCommonFileAsync')
        xhr.withCredentials = true
        xhr.responseType = 'json'
        xhr.send(formData)
        xhr.onreadystatechange = () => {
          if (xhr.readyState === 4 && xhr.status === 200) {
            console.log('url', xhr.response.data.url)
            insertImgFn(xhr.response.data.url)
          }
        }

      }
    },
    async removeEditPopoverListener(flag) {  // 监听滚动，用于编辑框的滚动移除
      let timer = setTimeout(() => {
        let scrollElement = this.$refs.table.$el.querySelector('.el-table__body-wrapper');
        console.log('监听滚动，用于编辑框的滚动移除', flag, scrollElement);
        let scrollHandle = () => {
          console.log('执行--->', this.visibleEditOpinions);
          if (this.visibleEditOpinions) {
            this.clearEditPopperComponent();
          }
        }
        if (flag) {
          // 滚动节流
          scrollElement.addEventListener('scroll', throttle(500, scrollHandle));
        } else {
          scrollElement.removeEventListener('scroll', scrollHandle);
        }
        clearTimeout(timer);
      }, 0);
    },
    // 复选框选中的数据
    async changeSelection(row) {
      this.selectData = row;
      this.seqs = this.selectData.map((el) => { return el.seq; }).toString();
    },
    async clearEditPopperComponent() {
      this.prevTarget = null;
      this.popperFlag = !this.popperFlag;
      this.popperFlagdetail = !this.popperFlagdetail;
      this.visiblepopover = false;
      this.visiblepopoverdetail = false;
    },
    async copyAllAbnormalOderNo() {
      var res = await getAllOutOfStockSectionReportOderNoAsync();
      if (!res.data) {
        this.$message({ message: "没有获取到订单号", type: "warning" });
        return;
      }
      this.doCopy(res.data)
    },
    async copyAllAbnormalGoodsCode() {
      var res = await getAllOutOfStockSectionReportGoodsCodeAsync();
      if (!res.data) {
        this.$message({ message: "没有获取到商品编码", type: "warning" });
        return;
      }
      this.doCopy(res.data)
    },
    doCopy: function (val) {
      let that = this;
      this.$copyText(val).then(function (e) {
        that.$message({ message: "内容已复制到剪切板！", type: "success" });
      }, function (e) {
        that.$message({ message: "抱歉，复制失败！", type: "warning" });
      })
    },
    async onHand(row) {
      this.formtitle = '编辑';
      this.editVisible = true;
      this.$nextTick(async () => {
        var arr = Object.keys(this.autoform.fApi);
        if (arr.length > 0) {
          await this.autoform.fApi.hidden(false, 'goodsCode')
          await this.autoform.fApi.hidden(false, 'goodsName')
          await this.autoform.fApi.hidden(false, 'buyPerson')
          let setData = {
            goodsCode: row.goodsCode,
            goodsName: row.goodsName,
            buyPerson: row.buyPerson
          };
          this.autoform.fApi.resetFields();
          await this.autoform.fApi.setValue(setData)
          this.goodsCodes = row.goodsCode;
        }
      })
    },
    async onHandBatch() {
      if (this.selids.length <= 0) {
        this.$message.error("请选择要操作的编码！");
        return;
      }
      this.formtitle = '编辑';
      this.editVisible = true;
      var _this = this;
      this.$nextTick(async () => {
        var arr = Object.keys(this.autoform.fApi);
        if (arr.length > 0) {
          await this.autoform.fApi.hidden(true, 'goodsCode')
          await this.autoform.fApi.hidden(true, 'goodsName')
          await this.autoform.fApi.hidden(true, 'buyPerson')
          this.autoform.fApi.resetFields()
          _this.goodsCodes = _this.selids.join();
        }
      })
    },
    async onEditSubmit() {
      this.editLoading = true;
      let that = this;
      await this.autoform.fApi.validate(async (valid, fail) => {
        if (valid) {
          const formData = this.autoform.fApi.formData();
          if (formData._reason.length > 0) {
            formData.reason = `${formData._reason[0]}-${formData._reason[1]}`;
          }
          formData.goodsCode = this.goodsCodes;
          const res = await editOutOfStockSectionReportFollowRemarkAsync(formData);

          if (res.code == 1) {
            this.getlist();
            this.selids = [];
            this.editVisible = false;
          }
        } else {
          that.editLoading = false;
        }
      })
      this.editLoading = false;
    },
    async init() {
      var res1 = await getDirectorGroupList();
      if (res1?.data)
        this.grouplist = res1.data.map(item => { return { value: item.key, label: item.value }; });
      else
        this.grouplist = [];

      var res2 = await getAllProBrand();
      this.brandlist1 = res2?.data ?? [];
      if (res2?.data)
        this.brandlist = res2.data.map(item => { return { value: item.key, label: item.value }; });
      else
        this.brandlist = [];

      var res3 = await getOutOfStockSectionReportLastTimeAsync();
      this.lastUpdateTime = "最晚更新时间" + res3?.data ?? "";

      var res4 = await getPurchaseNewPlanTurnDayDeptList();
      if (res4?.data)
        this.purchasegrouplist = res4.data;
      else
        this.purchasegrouplist = [];
    },
    async changeSetCompany() {
      if (this.filter.company === '义乌' || this.filter.company === '南昌') {
        this.brandlist = this.brandlist1.filter(f => f.company === this.filter.company).map(item => {
          return { value: item.key, label: item.value };
        });
      } else if (this.filter.company === '其他') {
        this.brandlist = this.brandlist1.filter(f => f.company !== '南昌' && f.company !== '义乌').map(item => {
          return { value: item.key, label: item.value };
        });
      } else {
        this.brandlist = this.brandlist1.map(item => {
          return { value: item.key, label: item.value };
        });
      }
      this.filter.brandId = null;
    },
    async onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      if (!this.pager.OrderBy) this.pager.OrderBy = "";
      var pager = this.$refs.pager.getPager()
      const params = { ...pager, ...this.pager, ... this.filter }
      this.listLoading = true
      const res = await pageOutOfStockSectionReport(params)
      this.listLoading = false
      if (!res?.success) return
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => { d._loading = false })
      this.list = data
      this.summaryarry = res.data.summary;
      this.summaryAll = "总压单数：" + res.data.summary.waitOrderNumCurrent_All_sum + ",总压品数：" + res.data.summary.waitGoodNumCurrent_All_sum;

      var res2 = await getAllOutOfStockSectionReportReasonRate(params);
      this.reasonRate = res2.data;

      var res3 = await getOutOfStockSectionReportLastTimeAsync();
      this.lastUpdateTime = "最晚更新时间" + res3.data
    },
    async cellclick(row, column, cell, event) {
      if (column.property == 'outOfStockTimeCurrent') {
        this.$showDialogform({
          path: `@/views/inventory/AllAbnormalTimeLog.vue`,
          title: '出现负库存数时间',
          autoTitle: false,
          args: { goodsCode: row.goodsCode },
          height: "500px",
          width: '40%',
          //callOk: self.onSearch
        })
      }
      else if (column.property == 'buyNo') {
        if (row.buyNo)
          this.$router.push({ path: '/inventory/purchaseindex', query: { buyNo: row.buyNo } })
      }
      else if (column.property == "remark"){
        if(row.remark){
          this.onCopyRemark(event.target.innerText);
        }
      }
      else if (column.property == 'goodsCode'
        || column.property == 'goodsName'
        || column.property == 'waitOrderNumCurrent'
        || column.property == 'waitGoodNumCurrent'
        || column.property == 'waitDaysc'
        || column.property == 'ninetyDaysNum'
      ) {
        await this.getdetaillist(row.goodsCode);
        if (event.stopPropagation) {
          event.stopPropagation();
        } else if (window.event) {
          window.event.cancelBubble = true;
        }
        let currentTarget = event.target;
        this.editData = row;
        if (this.prevTarget === currentTarget) {
          this.visiblepopoverdetail = !this.visiblepopoverdetail;
        } else {
          if (this.prevTarget) {
            this.clearEditPopperComponent();
            this.$nextTick(() => {
              this.prevTarget = currentTarget;
              this.visiblepopoverdetail = true;
            });
          } else {
            this.prevTarget = currentTarget;
            this.visiblepopoverdetail = true;
          }
        }
      } else if (column.property == 'sellStock') {
        this.$showDialogform({
          path: `@/views/inventory/AllAbnormalsellStocknew.vue`,
          title: '详情',
          autoTitle: false,
          args: { goodsCode: row.goodsCode },
          height: 700,
          width: '40%',
          //callOk: self.onSearch
        })
      }
    },
    async showOrderDetail(goodsCode, wms_id) {
      this.dialogOrderDetailVisible = true;
      this.$nextTick(async () => {
        this.$refs.boxorder.reloadParentId(goodsCode, wms_id);
        this.$refs.boxorder.onSearch();
      });
    },
    async getrecordlist(goodscode) {
      this.visiblepopover = true;

      this.$nextTick(() => {
        this.$refs.orderabnormalgoodscoderecord.onSearch(goodscode);
      });
    },
    async getdetaillist(goodsCode) {
      this.detaillist = [];
      const res = await queryTimeOutOfStockSectionReportDtlAsync({ goodsCode: goodsCode })
      if (!(res.code == 1 && res.data)) return
      this.detaillist = res.data;
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.goodsCode);
      })
      this.selectList = rows;
    },
    // 图片点击放大
    showImg(e) {
      // console.log(e.target)
      if (e.target.tagName == 'IMG') {
        this.imgPreview.img = e.target.src
        this.imgPreview.show = true
      }
    },
    async getimportlist() {
      var res = await getLastUpdateTimeOutOfStockSectionReportAsync();
      if (!res?.success) return
      this.importtimelist = res.data
      this.importtimedialogVisible = true;
    },
    //开始导入
    startImport() {
      this.uploadLoading = false;
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.upload.clearFiles();
        this.fileList = [];
      })
    },
    //取消导入
    cancelImport() {
      this.dialogVisible = false;
    },
    async submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.fileHasSubmit = true;
      this.uploadLoading = true;
      this.$refs.upload.submit();
    },
    async uploadFile(item) {
      if (!this.fileHasSubmit) {
        return false;
      }
      this.fileHasSubmit = false;
      this.uploadLoading = true;
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      const res = await importAbnormalOrderNewAsync(form);
      if (res.code == 1) {
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
        this.dialogVisible = false;
      }
      this.uploadLoading = false;
      this.$refs.upload.clearFiles();
    },
    async uploadChange(file, fileList) {
      let files = [];
      files.push(file)
      this.fileList = files;
    },
    uploadRemove(file, fileList) {
      this.fileList = [];
    },
    handleExceed(files, fileList) {
      this.$message.warning(`本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件,当前限制只能选择 2 个文件!`);
    },
    onCopyRemark(val){
      if(val)
        this.doCopy(val);
    },
    async exportAbnormalOrder(){
      this.listLoading = true;
      var res = await exportOutOfStockSection(this.filter);
      if(res?.success){
        this.$message({ message: "请先选取文件", type: "success"});
      }
      this.listLoading = false;
    }
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-select__tags-text {
  max-width: 35px;
}

.imgDolg {
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: 9999;
  background-color: rgba(140, 134, 134, 0.6);
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  #imgDolgClose {
    position: fixed;
    top: 35px;
    cursor: pointer;
    right: 7%;
    font-size: 50px;
    color: white;
  }

  img {
    width: 80%;
  }
}

::v-deep .color1688 {
  color: blue !important;
}
</style>
