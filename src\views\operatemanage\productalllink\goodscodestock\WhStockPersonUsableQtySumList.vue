<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-row>
                <el-col :span="24" style="height:442px">
                    <ces-table ref="table" :that='that' :isIndex='false' :hasexpandRight="true" 
                        :hasexpand='true' :tableData='sellist' :tableCols='tableCols' 
                        :isSelection="false"
                        :isSelectColumn='false' :loading="sellistLoading">                       
                    </ces-table>
                </el-col>
            </el-row>
        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;padding-top:10px;">
                    <el-button @click="onClose">关闭</el-button>                  
                </el-col>
            </el-row>
        </template>
    </my-container>
</template>
<script>
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime, formatSecondNewToHour } from "@/utils/tools";
import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";

import { WhStockPersonUsableQtySumByGoodsCode } from "@/api/inventory/goodscodestock"
import { formatNoLink } from "@/utils/tools";
import store from '@/store'

var formatSecondToHour1 = function (time) {
    return formatSecondNewToHour(time);
}
const tableCols = [    
    //组       数量     类型    时间
    { istrue: true, prop: 'groupName', label: '运营组', width: '200', sortable: true},
    { istrue: true, prop: 'qty', label: '数量', width: '200', sortable: true }
]

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");


export default {
    name: "WhStockPersonUsableQtySumList",
    components: { cesTable, MyContainer, MyConfirmButton },
    data() {
        return {
            that: this,
            batchExpireTime: '',
            sellist: [],
            detaillist: [],
            sellistLoading: false,
            visiblepopoverdetail: false,
            popoverdetailloding: false,
            tableCols: tableCols,
            mode: 3,
            form: {
            },
            //summaryarry: {},
            pageLoading: false,
            curRow: null,
            formEditMode: true,//是否编辑模式  

        };
    },
    async mounted() {
    },
    computed: {

    },
    methods: {      
        async loadData(goodsCode) {
            this.pageLoading = true;
            var rlt=await WhStockPersonUsableQtySumByGoodsCode(goodsCode);

            this.sellist = rlt.data;
            this.total =0;
            if(rlt.data && rlt.data.length>0)
                this.total=rlt.data[0].total;
            this.pageLoading = false;
          
        },
        onClose() {
            this.$emit('close');
        },
        async onSave(isClose) {
            if (await this.save()) {
                this.$emit('afterSave');
                if (isClose)
                    this.$emit('close');
            }
        },
        async save() {
           return true;
        },

    },
};
</script>
