<template>
    <my-container v-loading="pageLoading">
        <!--列表----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='datalist' @select='selectchange' :isSelection="true" :tableCols='tableCols' :isSelectColumn='true'
            :customRowStyle="customRowStyle" :loading="listLoading" :summaryarry="summaryarry" :selectColumnHeight="'0px'"
            :isBorder="false" style="height: 100%;">
            <template slot='extentbtn'> </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getDataList" />
        </template>
        <!-- 新增岗位/编辑岗位 -->
        <el-dialog :title="diologTitle" :visible.sync="showDialog" width="30%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag>
            <postionDialogform v-if="showDialog" ref="postionDialogform" :isCheck="false" @subSucce="subSucce">
            </postionDialogform>
            <!-- <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showDialog = false">取 消</el-button>
                    <my-confirm-button type="submit" @click="onSubmit" v-show="!islook" />
                </span>
            </template> -->
        </el-dialog>
        <!-- 完成 -->
        <el-dialog title="是否确认完成该岗位招聘" :visible.sync="showFinishDialog" width="20%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag>
            <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" placeholder="请输入计划完成备注" v-model="finishReason"
                maxlength="80" show-word-limit>
            </el-input>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showFinishDialog = false">取 消</el-button>
                    <!-- <my-confirm-button type="submit" @click="closePlan" /> -->
                    <el-button type="primary" @click="closePlan" :loading="subLoad">保存</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 已招聘 -->
        <el-dialog :title="'招聘人数(' + count + ')'" :visible.sync="showHeadcount" width="60%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag>
            <headcount :planId="planId" v-if="showHeadcount"></headcount>
        </el-dialog>
    </my-container>
</template>
<script>
import cesTable from "@/components/Table/tableforvedio.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import postionDialogform from "@/views/profit/PersonnelRecruiting/postionDialogform";
import headcount from "@/views/profit/PersonnelRecruiting/headcount";
import { getPageRecruitmentPlan, closeRecruitmentPlan, delRecruitmentPlan, closeRecruitmentPlanBatch } from '@/api/profit/hr'
import { formatTime } from "@/utils/tools";

const tableCols = [
    { istrue: true, prop: 'department', label: '招聘部门', width: "140", sortable: 'custom', },
    { istrue: true, prop: 'positionName', align: 'left', label: '招聘岗位', width: "280", sortable: 'custom', },
    {
        istrue: true, prop: 'recruitmentCount', align: 'left', label: '招聘人数', width: "100", type: "click", handle: (that, row) => {
            that.showHeadcount = true, that.planId = row.planId,
                that.count = row.onboardCount + '/' + row.recruitmentCount
        },
        formatter: (row) => row.onboardCount + '/' + row.recruitmentCount, sortable: 'custom',
    },
    {
        istrue: true, prop: 'recruitmentStartDate', align: 'left', label: '招聘时间', width: "300", formatter: (row) => formatTime(row.recruitmentStartDate, 'YYYY-MM-DD')
            + '至' + formatTime(row.recruitmentEndDate, 'YYYY-MM-DD'), sortable: 'custom',
    },
    { istrue: true, prop: 'recruiters', align: 'left', label: '招聘专员', sortable: 'custom', },
    { istrue: true, prop: 'addedDate', align: 'left', label: '添加时间', width: "160", sortable: 'custom', },
    { istrue: true, prop: 'addedBy', align: 'left', label: '添加人', width: "100", sortable: 'custom', },
    { istrue: true, prop: 'applicant', align: 'left', label: '申请人', width: "100", sortable: 'custom', },
    {
        istrue: true, type: "button", label: '操作', width: "240",
        btnList: [
            { label: "编辑", permission: "", handle: (that, row) => that.editPostion(row.planId) },
            { label: "完成", permission: "", handle: (that, row) => that.finishPostion(row.planId) },
            { type: "danger", permission: "", label: "删除", handle: (that, row) => that.deletePostion(row.planId) }
        ]
    }
];

export default {
    name: "onGoing",//进行中
    components: {
        MyContainer, postionDialogform, MyConfirmButton
        , cesTable, headcount,
    },
    watch: {
    },
    props: {
        ddDeptId:{ type: String, default: '' },
    },
    data () {
        return {
            subLoad: false,
            count: '',
            planId: 0,
            showDialog: false,
            diologTitle: '',
            istLoading: false,
            summaryarry: {},
            datalist: [
            ],
            islook: false,
            total: 0,
            sels: [], // 列表选中列
            listLoading: false,
            that: this,
            pageLoading: false,
            pager: {},
            tableCols: tableCols,
            finishReason: null,//完成原因
            showFinishDialog: false,//显示完成弹窗
            showHeadcount: false,//显示招聘人数列表
            filter: {
                closeStatus: 0,//计划状态:-1删除、0进行中、1已完成
                ddDeptId: null,//招聘部门
                positionName: null,//岗位名称
                recruiterIds: [],//招聘专员
                closeReason: null,//完成原因
            },
            planId: 0,
        };
    },
    watch: {
    },
    created () {
    },
    mounted () {
        this.onSearch();
    },
    methods: {
        // 完成招聘
        closePlan () {
            if (this.finishReason == null) {
                this.$message({ message: '请输入完成备注', type: "warning" });
                return
            }
            this.subLoad = true;
            let params = {
                planId: this.planId,
                planStatus: 0,
                closeReason: this.finishReason
            }
            closeRecruitmentPlan(params).then(res => {
                this.subLoad = false;
                if (res.success) {
                    this.$message({ message: '该岗位招聘完成', type: "success" });
                    this.showFinishDialog = false;
                    this.onSearch();
                }
            })
        },
        // 提交成功
        subSucce () {
            this.showDialog = false;
            this.onSearch();
        },
        // 新增岗位
        onAdd () {
            this.showDialog = true;
            this.diologTitle = '新增岗位';
            setTimeout(() => {
                this.$refs.postionDialogform.reset();
            }, 1);
        },
        // 刷新方法
        onSearch (filter) {
            if (filter) {
                this.filter = filter
            }
            this.$refs.pager.setPage(1);
            this.getDataList();
        },
        //删除
        deletePostion (planId) {
            this.$confirm('是否确认删除该条岗位招聘?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning',
            }).then(async () => {
                const res = await delRecruitmentPlan({ planId: planId })
                if (!res?.success) { return }
                this.$message({ type: 'success', message: '删除成功!' });
                this.onSearch()
            }).catch(() => {
                this.$message({ type: 'info', message: '已取消删除' });
            });
        },
        //完成
        finishPostion (planId) {
            this.finishReason = null;
            this.showFinishDialog = true;
            this.planId = planId;
        },
        // 编辑
        editPostion (planId) {
            this.showDialog = true;
            this.diologTitle = '修改岗位';
            setTimeout(() => {
                this.$refs.postionDialogform.getInfo(planId);
            }, 1);

        },

        //获取数据
        async getDataList () {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter,
                ddDeptId: this.ddDeptId
            };
            this.listLoading = true;
            const res = await getPageRecruitmentPlan(params);
            this.listLoading = false;
            this.total = res.data.total
            this.datalist = res.data.list;
            this.summaryarry = res.data.summary;
        },

        //列表排序
        sortchange (column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        //多选事件
        selectchange: function (rows, row) {
            this.sels = [];
            rows.forEach(f => {
                let plan = {
                    planId: f.planId,
                    planStatus: 22,
                    closeReason: '批量手动关闭',
                }
                this.sels.push(plan);
            })
        },
        //批量
        async onAll () {
            if (this.sels.length == 0) {
                this.$message({ type: 'warning', message: "请选择一个岗位" });
                return;
            }
            this.$confirm("选中的招聘计划将会移动到完成列表，是否确定", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                // return
                const res = await closeRecruitmentPlanBatch(this.sels)// 批量完成方法
                if (res?.success) {
                    this.$message({ message: '操作成功！', type: "success" });
                    this.sels = [];
                    this.onSearch();
                    var self = this;
                    setTimeout(() => { self.reload(); }, 100);
                }
            });
        },

        customRowStyle (row, index) {
            if (row.row?.isend && row.row.isend == 1) {
                let styleJson = {};
                styleJson.color = "rgb(216 216 216)";
                return styleJson
            } else {
                return null
            }

        },
    },
};
</script>
