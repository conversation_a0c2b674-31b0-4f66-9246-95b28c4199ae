<template>
  <my-container v-loading="pageLoading" style="height:100%;">
        <template #header>
          <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>      
          <el-form-item label="商品编码:">
                <el-input v-model="filter.goodsCode" placeholder="商品编码"/>         
            </el-form-item>
            <el-form-item label="商品名称:">
                <el-input v-model="filter.goodsName" placeholder="商品名称"/>         
            </el-form-item>
            <el-form-item label="是否已重拍:" >
              <el-select v-model="filter.isRephotography"   placeholder="请选择" style="width:178px;">
                <el-option label="所有" value=""/>
                <el-option v-for="item in yesOrNoBoolList" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="onSearch">查询</el-button>
            </el-form-item> 
          </el-form>
        </template>
    
        <ces-table ref="table" :that='that' :isIndex='false' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
          :tableData='list'    :tableCols='tableCols' :isSelection="true" @select="selectchange"
          :tableHandles='tableHandles'
          :loading="listLoading">
        </ces-table>
        
        <template #footer>
          <my-pagination
            ref="pager"
            :total="total"
            :checked-count="sels.length"
            @get-page="getlist"
          />
        </template>

        <el-drawer
          :title="formtitle"
          :modal="false"
          :wrapper-closable="true"
          :modal-append-to-body="false"
          :visible.sync="addFormVisible"
          direction="btt"
          size="'auto'"
          class="el-drawer__wrapper"
          style="position:absolute;"
        >
        <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options" style="padding-top:10px;"/>
          <div class="drawer-footer">
            <el-button @click.native="addFormVisible = false">取消</el-button>
            <my-confirm-button type="submit"  :loading="addLoading" @click="onAddSubmit" />
          </div>
        </el-drawer>       
  </my-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatYesornoBool} from "@/utils/tools";
import { 
  getList,
  getById,
  addOrUpdate,
  deleteData,
  deleteDataBatch,
  saveRephotography
} from "@/api/inventory/goodsimagerephotography"
const tableCols =[
       {istrue:true,label:"操作",width:"52",type:'button',btnList:[
          //{label:"修改 ",handle:(that,row)=>that.onEdit(row)},
          {label:" 删除",handle:(that,row)=>that.onDelete(row)}
        ]},
       {istrue:true,prop:'goodsCode',label:'商品编码', width:'130',sortable:'custom'},
       {istrue:true,prop:'goodsName',label:'商品名称', width:'auto'}, 
       {istrue:true,prop:'goodsImage',label:'原图', width:'60',type:'image'},
       {istrue:true,prop:'isRephotography',label:'是否已重拍', width:'110',sortable:'custom',type:'switch',change:async (row)=>{
            var res = await saveRephotography(row);
         }
       },
      //  {istrue:true,prop:'goodsImageNew',label:'新图', width:'80',type:'image'},
       {istrue:true,prop:'createdTime',label:'创建时间', width:'160',sortable:'custom'},
       {istrue:true,prop:'createdUserName',label:'创建人', width:'100',sortable:'custom'},
       {istrue:true,prop:'modifiedTime',label:'修改时间', width:'160',sortable:'custom'},
       {istrue:true,prop:'modifiedUserName',label:'修改人', width:'100',sortable:'custom'}
     ];
const tableHandles1=[
        // {label:"新增", handle:(that)=>that.onAdd()},
        {label:"批量删除", handle:(that)=>that.onDeleteBatch()}
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: {
        goodsCode:null,
        goodsName:null,
        isRephotography:null, //是否已重拍
      },
      yesOrNoBoolList:[
        {value:true,label:"是"},
        {value:false,label:"否"},
      ],
      list: [],
      summaryarry:{},
      pager:{OrderBy:"createdTime",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      dialogVisible: false,
      autoform:{
               fApi:{},
               rule:[],
               options:{submitBtn:false},
        },
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,     
      deleteLoading: false,
      formtitle:"新增",
      fileList:[],
      selids:[],//选择的id
    }
  },
  async mounted() {
    await this.getlist();  
  },
  methods: {
    //获取查询条件
    getCondition(){
      var pager = this.$refs.pager.getPager();
      var page  = this.pager;
      const params = {
        ...pager,
        ...page,
        ... this.filter
      }

      return params;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    //分页查询
    async getlist() {
      var params=this.getCondition();
      if(params===false){
            return;
      }

      this.listLoading = true
      const res = await getList(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry=res.data.summary;
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
    //排序查询
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else{
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearch();
    },
    async onEdit(row) {
        var arr = Object.keys(this.autoform.fApi);
        if(arr.length ==0)
              await this.onAdd()
      this.formtitle='编辑';
      this.addFormVisible = true
      const res = await getById(row.id )
      if (res.data&&res.data.platform==0) res.data.platform=null
      await this.autoform.fApi.setValue(res.data)
    },
   async onAdd() {
      this.formtitle='新增';
      this.addFormVisible = true;
      let that=this;
      this.autoform.rule=[{type:'hidden',field:'id',title:'id',value: 0},
                      //{type:'input',field:'goodsCode',title:'商品编码',width:200,validate:[{type:"string",required:true,message:"请填写商品编码"}]},
                      //{type:'input',field:'goodsName',title:'商品名称',width:200,validate:[{type:"string",required:true,message:"请填写商品名称"}]},
                      {type:'select',field:'isRephotography',title:'是否已重拍', //validate: [{type: 'bool', required: true, message:'请选择'}],
                        value: true,options: [{value:true, label:'是'},{value:false, label:'否'}]},
                 ];
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
          this.autoform.fApi.reload()
    },
    async onEditSubmit() {
      this.addFormVisible = true
      await onAddSubmit();
    },
    async onAddSubmit() {
       this.addFormVisible=true;
       this.addLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();

          const res = await addOrUpdate(formData);
          this.onSearch();
          
          this.addFormVisible=false;
        }else{
          //todo 表单验证未通过
        }
     })
      this.addLoading=false;
    },   
    async onDelete(row) {
      row._loading = true
      this.$confirm('确定删除吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
            const res = await deleteData({id:row.id})
            row._loading = false
            if (!res?.success) {return }
            this.$message({
                type: 'success',
                message: '删除成功!'
            });
            this.onSearch();
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
           row._loading = false
        });
    },
    async onDeleteBatch() {
      if(!this.selids||this.selids.length==0){
        this.$message({
                type: 'warning',
                message: '请勾选删除的行'
            });
        return;
      }
      this.$confirm('确定删除吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
            const res = await deleteDataBatch({ids:this.selids.join(",")})
            if (!res?.success) {return }
            this.$message({
                type: 'success',
                message: '删除成功!'
            });
            this.onSearch();
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    },
    selsChange: function(sels) {
      this.sels = sels;
    },
    selectchange:function(rows,row) {
      this.selids=[];console.log(rows)
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary{
  margin-right: 7px;
}
</style>
