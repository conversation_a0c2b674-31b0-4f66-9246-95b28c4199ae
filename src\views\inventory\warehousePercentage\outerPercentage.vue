<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="ListInfo.date" type="date" placeholder="选择日期" :value-format="'yyyy-MM-dd'"
          :clearable="false" class="publicCss">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.styleCode" placeholder="款式编码" maxlength="50" clearable class="publicCss" />
        <div class="publicCss">
          <inputYunhan :width="'180px'" ref="goodsCode" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
            placeholder="商品编码" :clearable="true" @callback="callbackGoodsCode" title="商品编码">
          </inputYunhan>
        </div>
        <el-select class="publicCss" v-model="ListInfo.brandNames" placeholder="请选择品牌" :clearable="true" multiple
          filterable :collapse-tags="true">
          <el-option v-for="item in brandList" :key="item.key" :label="item.value" :value="item.value">
          </el-option>
        </el-select>
        <el-select v-model="ListInfo.wmsIds" multiple clearable filterable :collapse-tags="true" placeholder="请选择仓库"
          class="publicCss">
          <el-option v-for="item in warehouselist.filter(item => item.isSendWarehouse == '是')" :key="item.name"
            :label="item.name" :value="item.wms_co_id" />
        </el-select>
        <el-input-number v-model.trim="ListInfo.wmsSaleRateMin_Pay" placeholder="占比(0-100)" :min="0" :max="100"
          :precision="0" :controls="false" class="publicCss" />
        <el-button type="primary" size="mini" @click="getList('search')">搜索</el-button>
        <el-button type="primary" size="mini" @click="onsetupMethod"
          v-if="checkPermission('outerPercentageBatchEdit')">批量编辑</el-button>
        <el-button type="primary" size="mini" @click="onBatchDeletion"
          v-if="checkPermission('outerPercentageBatchDel')">批量删除</el-button>
        <el-button type="primary" size="mini" @click="startImport">导入</el-button>
        <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
        <el-button type="primary" size="mini" @click="onBatchEditing"
          v-if="checkPermission('outerPercentageSet')">设置</el-button>
      </div>
    </template>
    <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
      :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
      @select="checkboxRangeEnd" :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;"
      height="100%" :showsummary="false" :summaryarry="data.summary" @sortchange="sortchange"
      @onTrendChart="trendChart">
      <template #usableQty="{ row }">
        <div :style="{ color: row.colorType == 1 ? 'red' : '' }">
          {{ row.usableQty }}
        </div>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div slot="title" class="header-title">
        <span class="title-text"><span>导入数据</span></span>
        <span class="title-close"><el-button @click="downLoadFile">下载模版</el-button></span>
      </div>
      <div>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog title="批量编辑" :visible.sync="setup.setupDialog" width="20%" v-dialogDrag :close-on-click-modal="false">
      <div class="dialog_top" v-loading="setup.loading">
        <el-form :model="setup" :rules="setup.setupRules" ref="setup" label-width="80px" class="demo-ruleForm">
          <el-form-item label="仓库" prop="name">
            <el-select v-model="setup.name" clearable filterable placeholder="请选择仓库">
              <el-option v-for="item in warehouselist.filter(item => item.isSendWarehouse == '是')" :key="item.name"
                :label="item.name" :value="item.name" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="dialog_bottom">
        <el-button @click="setup.setupDialog = false">取 消</el-button>
        <el-button type="primary" @click="onEditingMethod">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="设置" :visible.sync="editing.editingDialog" width="20%" v-dialogDrag :close-on-click-modal="false">
      <div class="dialog_top" v-loading="editing.loading">
        <el-form :model="editing" :rules="editing.editingRules" ref="editing" label-width="80px" class="demo-ruleForm">
          <el-form-item label="占比" prop="rate">
            <el-input-number v-model.trim="editing.rate" placeholder="占比(0-100)" :min="0" :max="100" :precision="0"
              :controls="false" style="width: 200px;" />%
          </el-form-item>
        </el-form>
      </div>
      <div class="dialog_bottom">
        <el-button @click="editing.editingDialog = false">取 消</el-button>
        <el-button type="primary" @click="onSettingsMethod">确 定</el-button>
      </div>
    </el-dialog>

    <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false" direction="btt">
      <div v-if="!chatProp.chatLoading">
        <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd" :picker-options="pickerOptions" style="margin: 10px" @change="
            trendChart({
              ...chatPropOption,
              startDate: $event[0],
              endDate: $event[1],
            })
            " />
        <buschar v-if="!chatProp.chatLoading" :analysis-data="chatProp.data" />
      </div>
      <div v-else v-loading="chatProp.chatLoading" />
    </el-drawer>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
import { getAllWarehouse } from '@/api/inventory/warehouse'
import { getAllProBrand } from '@/api/inventory/warehouse';
const api = '/api/verifyOrder/OutWms/'
export default {
  name: "outerPercentage",
  components: {
    MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan
  },
  data() {
    return {
      chatProp: {
        chatDialog: false, // 趋势图弹窗
        chatTime: null, // 趋势图时间
        chatLoading: true, // 趋势图loading
        data: [], // 趋势图数据
      },
      editing: {
        loading: false,
        rate: undefined,
        editingDialog: false,
        editingRules: {
          rate: [{ required: true, message: '请输入占比', trigger: 'blur' }],
        },
      },
      setup: {
        loading: false,
        setupDialog: false,
        name: null,
        setupRules: {
          name: [{ required: true, message: '请选择仓库', trigger: 'blur' }],
        },
      },
      dialogVisible: false,
      fileList: [],
      uploadLoading: false,
      fileparm: {},
      api,
      platformlist,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'date',
        isAsc: false,
        date: null,
        brandNames: [],
        goodsCodes: null,
        styleCode: null,
        wmsIds: [],
        wmsSaleRateMin_Pay: undefined,
      },
      data: {
        list: [],
        summary: null
      },
      chatProp: {
        chatDialog: false, // 趋势图弹窗
        chatTime: null, // 趋势图时间
        chatLoading: true, // 趋势图loading
        data: [], // 趋势图数据
      },
      checkboxGroup: [],
      timeRanges: [],
      brandList: [],
      warehouselist: [],
      tableCols: [],
      tableData: [],
      total: 0,
      loading: true,
      pickerOptions,
      isExport: false
    }
  },
  async mounted() {
    await this.init();
    await this.getCol();
    await this.getList()
  },
  methods: {
    async callbackGoodsCode(val) {
      this.ListInfo.goodsCodes = val;
    },
    async trendChart(option) {
      var endDate = null;
      var startDate = null;
      if (option.startDate && option.endDate) {
        startDate = option.startDate;
        endDate = option.endDate;
      } else {
        endDate = option.date;
        startDate = new Date(option.date);
        startDate.setDate(option.date.getDate() - 30);
        startDate = dayjs(startDate).format("YYYY-MM-DD");
        endDate = dayjs(endDate).format("YYYY-MM-DD");
      }
      option.startDate = startDate;
      option.endDate = endDate;
      this.chatProp.chatTime = [startDate, endDate];
      this.chatProp.chatLoading = true;
      const { data, success } = await request.post(`${this.api}Day/GetTrendChart`, { ...option })
      if (success) {
        this.chatProp.data = data;
      }
      this.chatProp.chatLoading = false;
      this.chatProp.chatDialog = true;
      this.chatPropOption = option;
    },
    async onBatchDeletion() {
      if (this.checkboxGroup.length == 0) {
        this.$message.error('请选择需要操作的数据行')
        return
      }
      this.checkboxGroup.forEach(item => {
        item.isDelete = true
      })
      this.$confirm('是否删除选中数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await request.post(`${this.api}DeleteWmsGoods`, this.checkboxGroup)
        if (success) {
          this.$message.success('删除成功')
          this.getList()
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
      });
    },
    async onSettingsMethod() {
      if (!this.editing.rate) {
        this.$message.error('请输入占比')
        return
      }
      this.editing.loading = true
      const { success } = await request.post(`${this.api}WmsSaleRateMin/set`, null, { params: { rate: this.editing.rate } })
      this.editing.loading = false
      if (success) {
        this.$message.success('设置成功')
        this.editing.editingDialog = false
        this.getList()
      } else {
        this.$message.error('设置失败')
      }
    },
    async onBatchEditing() {
      const { data, success } = await request.post(`${this.api}WmsSaleRateMin/Get`)
      if (!success) return
      this.editing.rate = data
      this.editing.editingDialog = true
    },
    async onEditingMethod() {
      if (!this.setup.name) {
        this.$message.error('请选择仓库')
        return
      }
      this.checkboxGroup.forEach(item => {
        item.newWmsName = this.setup.name
      })
      this.setup.loading = true
      const { data, success } = await request.post(`${this.api}ChangeWms`, this.checkboxGroup)
      this.setup.loading = false
      if (!success) return
      this.$message.success('设置成功')
      this.setup.setupDialog = false
      this.getList()
    },
    onsetupMethod() {
      if (this.checkboxGroup.length == 0) {
        this.$message.error('请选择需要操作的数据行')
        return
      }
      this.setup.name = null
      this.setup.setupDialog = true
    },
    downLoadFile() {
      window.open("../../static/excel/外仓占比导入模版.xlsx", "_self");
    },
    async init() {
      this.ListInfo.date = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
      var res = await getAllProBrand();
      if (!res?.success) return;
      this.brandList = res.data;
      let res1 = await getAllWarehouse();
      if (!res1.success) return;
      this.warehouselist = res1.data.filter((x) => x.name.indexOf('代发') < 0);
    },
    checkboxRangeEnd(list) {
      this.checkboxGroup = []
      list.forEach(item => {
        this.checkboxGroup.push({
          wmsName: item.wmsName,
          goodsCode: item.goodsCode,
          goodsName: item.goodsName,
          newWmsName: item.newWmsName
        })
      })
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("file", item.file);
      var res = await request.post(`${this.api}ImportGoodsAsync`, form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    proCodeCallback(val) {
      this.ListInfo.proCode = val
    },
    // 导出数据,这里前端可以封装一个方法
    async exportProps() {
      this.isExport = true
      await request.post(`${this.api}Day/ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
        this.isExport = false
      })
    },
    async getCol() {
      const { data, success } = await request.post(`${this.api}Day/GetColumns`)
      if (success) {
        data.unshift({
          label: "",
          type: "checkbox",
        });
        data.forEach(item => {
          if (item.prop == 'orderNoInner') {
            item.type = 'orderLogInfo'
            item.orderType = 'orderNoInner'
          }
          if (item.prop == 'proCode') {
            item.type = 'html'
            item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
          }
          if (item.format == "0.00%") {
            item.formatter = (row) => row[item.prop] !== null ? row[item.prop] + '%' : '';
          }
        })
        this.tableCols = data;
        this.ListInfo.summarys = data
          .filter((a) => a.summaryType)
          .map((a) => {
            return { column: a["sort-by"], summaryType: a.summaryType };
          });
      }
    },
    async getList(type) {
      if (type === "search") {
        this.ListInfo.currentPage = 1;
        this.$refs.pager.setPage(1);
      }
      this.loading = true;
      // 使用时将下面的方法替换成自己的接口
      try {
        const { data, success } = await request.post(`${this.api}Day/PageGetData`, this.ListInfo)
        if (success) {
          this.data = data;
          this.ListInfo.wmsSaleRateMin_Pay = data.extData.wmsSaleRateMin_Pay ? data.extData.wmsSaleRateMin_Pay : undefined;
        } else {
          this.$message.error("获取列表失败");
        }
      } finally {
        this.loading = false;
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 180px;
    margin-right: 10px;
  }
}

::v-deep .el-select__tags-text {
  max-width: 40px;
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px 0 0;

  // height: 40px;
  // background-color: #f5f7fa;
  // border-bottom: 1px solid #ebeef5;
  .title-text {
    display: flex;
    align-items: center;

    .title-close {
      margin-left: 10px;
    }
  }
}

.dialog_top {
  height: 150px;
  display: flex;
  align-items: center;
  padding-bottom: 20px;
}

.dialog_bottom {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}
</style>
