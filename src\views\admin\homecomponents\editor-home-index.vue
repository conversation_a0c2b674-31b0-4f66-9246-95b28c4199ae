<template>
    <div class="dashboard-editor-container">
        <el-row :gutter="15" >
            <el-col :xs="24" :sm="24" :lg="19">
                <div class="chart-wrapper11">
                    <!-- <editorindex class="editor"></editorindex> -->
                </div>          
            </el-col>
            <el-col :xs="24" :sm="24" :lg="5">
                <div class="chart-wrapper11">
                    <div class="chart-wrapper">
                        <div style="margin: 1px 10px 0;font-size:20px">
                        <span>版本升级记录</span>
                        </div>
                        <div class="timecard">
                        <el-timeline>
                            <el-timeline-item v-for="item in list" :key="item.id"  @click.native="clicktimelist(item)" placement="top" type="primary">
                            <el-link :underline="false" style="margin-top:-5%">{{item.title}}</el-link>
                            </el-timeline-item>
                        </el-timeline>
                        </div> 
                    </div>
                </div>
            </el-col>
        </el-row>

        <el-dialog :visible.sync="dialodayssisVisible" ref="elDialog" width="75%" v-dialogDrag :lock-scroll='false' :center="true" :show-close="false">   
            <div class="updatediv">
            <updateindex ref="updateindex" class="updateindex" ></updateindex> 
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getLogTimeAsync } from '@/api/admin/opration-log'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import updateindex from './home-updatelog.vue'
import editorindex from "./editor-index.vue";

export default {
    name: 'YunhanAdminEditorHomeIndex',
    components: {updateindex, editorindex, ElImageViewer},

    data() {
        return {
            that:this,
            list:[],
            dialodayssisVisible:false,
        };
    },

    async mounted() {
        await this.getlist()
    },

    methods: {
        async getlist(){
            const res = await getLogTimeAsync();
            
            if (!res?.success){
                return
            }
            const data = res.data
            this.list = data
        },
        async clicktimelist(row){
            this.dialodayssisVisible = true
            this.$nextTick(async () =>{
                await this.$refs.updateindex.getlist(row)
            })
        },
    },
};
</script>

<style lang="scss" scoped>
.editor{
    background-color: #fff;
}
.timecard {
    margin: 10px;
    height: 280px;
    overflow-y: auto;
}
.updateindex {
    margin: 10px;
    height: 550px;
    overflow-y: auto;
}

.dashboard-editor-container {
  padding: 20px;
  background-color: rgb(240, 242, 245);
  position: relative;
  height: 95%;

    .chart-wrapper {
    background: #fff;
    padding: 10px 16px 0;
    //margin-bottom: 10px;
    height: 400px;
  }

  .chart-wrapper11 {
    background: rgb(240, 242, 245);
    padding: 10px 16px 0;
    margin-bottom: 1px;
    height: 450px;
  }
}
</style>