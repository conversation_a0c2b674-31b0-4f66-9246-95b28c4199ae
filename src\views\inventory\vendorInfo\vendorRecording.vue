<template>
  <!-- 供应商记录 -->
  <my-container v-loading="pageLoading">
    <template #header>
      <div style="margin-bottom: 10px;">
        <el-input placeholder="供应商名称" v-model="ListInfo.providerName" maxlength="50" clearable
          style="width: 150px;margin-right: 10px;" />
        <el-input placeholder="联系电话" v-model="ListInfo.phone" maxlength="50" clearable
          style="width: 150px;margin-right: 10px;" />
        <el-select v-model="ListInfo.IsExitProvider" placeholder="重复供应商" style="width: 220px" class="publicMargin"
          clearable>
          <el-option label="未重复" :value="2" />
          <el-option label="重复" :value="1" />
        </el-select>
        <el-button type="primary" @click="RecordingList('click')">搜索</el-button>
      </div>
    </template>
    <!--列表-->
    <vxetablebase :id="'vendorRecording202408041612_1'" ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tablefixed='true'
      :tableData='taobleData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :loading="listLoading"
      style="width: 100%; height: 700px; margin: 0">
    </vxetablebase>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="detailPagechange" @size-change="detailSizechange" />
    </template>

    <!-- 弹层部分 -->
    <el-dialog title="搜索记录" :visible.sync="dialogFormVisible" :close-on-click-modal="false" v-dialogDrag>
      <vxetablebase :id="'vendorRecording202408041612_2'" ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange2'
        :tablefixed='true' :tableData='serachList' :tableCols='tableCols2' :isSelection="false" :isSelectColumn="false"
        style="width: 100%; height: 300px; margin: 0">
      </vxetablebase>
      <my-pagination ref="pager" :total="searchTotal" @page-change="searchPagechange" @size-change="searchSizechange" />
    </el-dialog>
    <el-dialog title="浏览记录" :visible.sync="dialogisible" :close-on-click-modal="false" v-dialogDrag>
      <vxetablebase :id="'vendorRecording202408041612_3'" ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange1'
        :tablefixed='true' :tableData='serachList' :tableCols='tableCols1' :isSelection="false" :isSelectColumn="false"
        style="width: 100%; height: 300px; margin: 0">
      </vxetablebase>
      <my-pagination ref="pager" :total="browseTotal" @page-change="browsePagechange" @size-change="browseSizechange" />
    </el-dialog>
    <!-- 点击供应商名字 -->
    <el-dialog title="采购记录" :visible.sync="nameVisible" width="40%" :before-close="handleClose" v-dialogDrag>
      <vxetablebase :id="'vendorRecording202408041612_4'" ref="detailTable" :tableData="nameTableData" :tableCols="tableCols5" :is-index="true" :that="that"
        :showsummary="true" :summaryarry="nameSummary" style="width: 100%; height: 500px" @sortchange='sortchange3' class="detail"  >
      </vxetablebase>
      <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="nameTotal"
        @page-change="namePagechange" @size-change="nameSizechange" style="margin-top: 40px;" />
    </el-dialog>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pagePurchaseOrderByProviderNameAsync } from '@/api/inventory/purchase'
import { getProviderPersonSetPageList, getProviderVisitLogPageList, getProviderSearchLogPageList } from '@/api/openPlatform/ProviderQuotation'
const tableCols = [
  { istrue: true, prop: 'providerName', label: '供应商名称', sortable: 'custom', type: 'treeStar1', style: "color: rgb(72, 132, 243);cursor:pointer;", handle: (that, row) => that.openNameDialog(row) },
  { istrue: true, prop: 'phone', label: '联系电话', sortable: 'custom' },
  { istrue: true, prop: 'sourceName', label: '推荐人', sortable: 'custom' },
  { istrue: true, prop: 'sourceType', label: '来源', sortable: 'custom' },
  { istrue: true, summaryEvent: true, prop: 'searchCount', label: '搜索记录', sortable: 'custom', type: 'click', handle: (that, row) => that.getSearchList(row) },
  { istrue: true, summaryEvent: true, prop: 'visitCount', label: '浏览记录', sortable: 'custom', type: 'click', handle: (that, row) => that.getHistoryList(row) },
]

const tableCols1 = [
  { istrue: true, prop: 'createdTime', label: '浏览日期', sortable: 'custom' },
  { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom' },
]

const tableCols2 = [
  { istrue: true, prop: 'createdTime', label: '搜索日期', sortable: 'custom' },
  { istrue: true, prop: 'key', label: '内容', sortable: 'custom' },
]
//采购记录
const tableCols5 = [
  { istrue: true, prop: 'purchaseDate', label: '采购时间', sortable: 'custom' },
  { istrue: true, prop: 'buyNo', label: '采购单号', sortable: 'custom' },
  { istrue: true, prop: 'totalAmont', label: '采购金额', sortable: 'custom' },
  { istrue: true, prop: 'count', label: '采购量', sortable: 'custom' },
]
export default {
  name: "vendorRecording",
  components: { MyContainer, vxetablebase, MyConfirmButton },
  data() {
    return {
      listLoading: true,
      ListInfo: {
        currentPage: 1,//当前页
        pageSize: 50,//每页条数
        orderBy: null,//排序字段
        isAsc: true,//是否升序
        styleCode: null,//系列编码
        categoryId: null,//品类id
        openId: null,//openId
        goodCode: null,//商品编码
        isBY: null,//是否包邮
        isContaisTax: null,//是否含税
        providerName: null,//供应商名称
        phone: null,//联系电话
      },
      searchInfo: {
        currentPage: 1,//当前页
        pageSize: 50,//每页条数
        openId: null,//openId
        orderBy: null,//排序字段
        isAsc: true,//是否升序
      },
      nameInfo: {
        currentPage: 1,//当前页
        pageSize: 50,//每页条数
        orderBy: null,//排序字段
        isAsc: true,//是否升序
        supplier: null,//供应商名称
      },
      nameVisible: false,//点击供应商名字弹层
      serachList: [],
      summaryarry: {},
      tableCols,
      tableCols1,
      tableCols2,
      tableCols5,
      that: this,
      total: 0,//总条数
      searchTotal: 0,//搜索记录总数
      browseTotal: 0,//浏览记录总数
      taobleData: [],
      dialogFormVisible: false,
      dialogisible: false,
      pageLoading: false,
      nameTotal: 0,
      nameTableData:[],
      nameSummary:null
    };
  },
  mounted() {
    this.RecordingList()
  },
  methods: {
    handleClose() {
      this.nameVisible = false
    },
    //点击供应商名称打开弹层
    async openNameDialog(row) {
      this.nameInfo.supplier = row.providerName ? row.providerName : row.supplier
      const { data, success } = await pagePurchaseOrderByProviderNameAsync(this.nameInfo)
      if (success) {
        this.nameTableData = data.list
        this.nameTotal = data.total
        this.nameSummary = data.summary
        this.nameVisible = true
        this.nameInfo.orderBy = null
      } else {
        this.$message.error('获取供应商采购记录失败')
      }
    },
    //对接记录弹层每页数量改变
    nameSizechange(val) {
      this.nameInfo.currentPage = 1;
      this.nameInfo.pageSize = val;
      this.openNameDialog(this.nameInfo)
    },
    //对接记录弹层当前页改变
    namePagechange(val) {
      this.nameInfo.currentPage = val;
      this.openNameDialog(this.nameInfo)
    },
    //浏览记录当前页改变
    browsePagechange(val) {
      this.searchInfo.currentPage = val;
      this.getHistoryList(this.searchInfo)
    },
    //浏览记录数量改变
    browseSizechange(val) {
      this.searchInfo.currentPage = 1;
      this.searchInfo.pageSize = val;
      this.getHistoryList(this.searchInfo)
    },
    //搜索记录当前页改变
    searchPagechange(val) {
      this.searchInfo.currentPage = val;
      this.getSearchList(this.searchInfo)
    },
    //搜索记录数量改变
    searchSizechange(val) {
      this.searchInfo.currentPage = 1;
      this.searchInfo.pageSize = val;
      this.getSearchList(this.searchInfo)
    },
    //明细页面数量改变
    detailSizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.RecordingList();
    },
    //明细当前页改变
    detailPagechange(val) {
      this.ListInfo.currentPage = val;
      this.RecordingList();
    },
    //获取供应商搜索记录
    async getSearchList(row) {
      this.searchInfo.openId = row.openId
      const { data, success } = await getProviderSearchLogPageList(this.searchInfo)
      if (success) {
        this.serachList = data.list
        this.searchTotal = data.total
        this.searchInfo.orderBy = null
        this.dialogFormVisible = true
      } else {
        this.$message.error('获取搜索记录失败')
      }
    },
    //获取供应商浏览记录
    async getHistoryList(row) {
      this.searchInfo.openId = row.openId
      const { data, success } = await getProviderVisitLogPageList(this.searchInfo)
      if (success) {
        this.serachList = data.list
        this.browseTotal = data.total
        this.searchInfo.orderBy = null
        this.dialogisible = true
      } else {
        this.$message.error('获取浏览记录失败')
      }
    },
    //分页查询
    async RecordingList(type) {
      if (type == 'click') {
        this.ListInfo.currentPage = 1
      }
      //如果有providerName就清除空格
      if (this.ListInfo.providerName) {
        this.ListInfo.providerName = this.ListInfo.providerName ? this.ListInfo.providerName.replace(/\s+/g, "") : null;
      }
      //如果有phone就清除空格
      if (this.ListInfo.phone) {
        this.ListInfo.phone = this.ListInfo.phone ? this.ListInfo.phone.replace(/\s+/g, "") : null;
      }
      const { data, success } = await getProviderPersonSetPageList(this.ListInfo)
      if (success) {
        this.taobleData = data.list
        this.total = data.total
        this.listLoading = false
      }
    },
    //排序查询
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.RecordingList()
      }
    },
    //浏览记录排序
    sortchange1({ order, prop }) {
      if (prop) {
        this.searchInfo.orderBy = prop
        this.searchInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getHistoryList(this.searchInfo)
      }
    },
    sortchange2({ order, prop }) {
      if (prop) {
        this.searchInfo.orderBy = prop
        this.searchInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getSearchList(this.searchInfo)
      }
    },
    sortchange3({ order, prop }) {
      if (prop) {
        if (prop == 'count') {
          this.nameInfo.orderBy = 'totalCount'
        } else {
          this.nameInfo.orderBy = prop
        }
        this.nameInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.openNameDialog(this.nameInfo)
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-badge__content {
  padding: 0 4px;
  top: 7px;
  right: 0;
}
</style>
