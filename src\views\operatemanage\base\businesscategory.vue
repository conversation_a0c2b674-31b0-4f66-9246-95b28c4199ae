<template>
  <container>
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="经营大类:">
          <el-select style="width: 175px;" v-model="filter.bussinessCategoryName" placeholder="类型" :collapse-tags="true"
          remote  :remote-method="remoteMethodBusinessCategory" clearable filterable>
            <el-option v-for="(item, i) in filterList.bussinessCategoryNames" :key="'bussinessCategoryNames' + i + 1"
              :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="一级类目:">
          <el-select style="width: 175px;" v-model="filter.categoryName1" placeholder="类型" :collapse-tags="true"
          remote  :remote-method="remoteMethodCategoryName1s" clearable filterable>
            <el-option v-for="(item, i) in filterList.categoryName1s" :key="'categoryName1Level' + i + 1" :label="item"
              :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="二级类目:">
          <el-select style="width: 175px;" v-model="filter.categoryName2" placeholder="类型" :collapse-tags="true"
          remote  :remote-method="remoteMethodCategoryName2s" clearable filterable>
            <el-option v-for="(item, i) in filterList.categoryName2s" :key="'categoryName2Level' + i + 1" :label="item"
              :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="匹配类目:">
          <el-input v-model.trim="filter.platformCategoryName" placeholder="匹配类目" maxlength="50" style="width: 120px"
            clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-dropdown style="margin-left: 10px;" @command="onExport">
            <el-button type="primary">
              导出<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="1">经营类目导出</el-dropdown-item>
              <el-dropdown-item command="2">拼多多导出</el-dropdown-item>
              <el-dropdown-item command="7">京东导出</el-dropdown-item>
              <el-dropdown-item command="6">抖音导出</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-form-item>
        <el-form-item>
          <el-dropdown style="margin-left: 10px;" @command="startImport">
            <el-button type="primary">
              导入<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="1">经营类目导入</el-dropdown-item>
              <el-dropdown-item command="2">其他平台匹配导入</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-form-item>
      </el-form>
    </template>
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='list'
      :tableCols='tableCols' :isSelection="false" :tableHandles='tableHandles' :loading="listLoading"
      :summaryarry="summaryarry">
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <el-dialog :title="importType == 2 ? '导入平台匹配' : '导入经营类目'" :visible.sync="dialogVisible" width="40%">
      <div v-if="importType == 1">
        <el-alert type="warning" show-icon :closable="false" title="温馨提示：导入使用覆盖形式，需要全量导入 ！"></el-alert>
      </div>
      <div v-if="importType == 2">
        <el-alert type="warning" show-icon :closable="false">
          <div>温馨提示：导入使用覆盖形式，需要全量导入</div>
          <div>导入文件名称需要包含‘拼多多’、‘京东’、‘抖音’关键字 </div>
        </el-alert>
      </div>
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="4" action
          accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :file-list="fileList">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="submitUpload">{{
        (uploadLoading ? '上传中' : '上传') }} </el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import {
  getPlatformBusinessCategoryList, importPlatformBusinessCategory, exportPlatformBusinessCategory, getBusinessCategorySelectData
} from '@/api/operatemanage/base/category'
import dayjs from "dayjs";
import { formatTime } from "@/utils";

const tableCols = [
  { istrue: true, prop: 'businessCategory', label: '经营类目', tipmesg: '', width: '200', sortable: 'custom', },
  { istrue: true, prop: 'categoryName1', label: '一级类目', tipmesg: '', width: '300', sortable: 'custom', },
  { istrue: true, prop: 'categoryName2', label: '二级类目', tipmesg: '', width: '300', sortable: 'custom', },
  { istrue: true, prop: 'pddCategorys', label: '拼多多匹配', width: '300', tipmesg: '' },
  { istrue: true, prop: 'jdCategorys', label: '京东匹配', width: '300', tipmesg: '' },
  { istrue: true, prop: 'dyCategorys', label: '抖音匹配', tipmesg: '' }
]

const tableHandles = [
  //{ label: "导入", handle: (that) => that.startImport() }
];

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
  name: 'YunHanBusinessCategory',
  components: { container, cesTable, MyConfirmButton },
  data() {
    return {
      that: this,
      filter: {
        bussinessCategoryName: null,
        categoryName1: null,
        categoryName2: null,
        platformCategoryName: null
      },
      filterList: {
        bussinessCategoryNames: [],
        categoryName1s: [],
        categoryName2s: []
      },
      list: [],
      shopList: [],
      directorGroupList: [],
      pager: { OrderBy: "BusinessCategory", IsAsc: false },
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      sels: [],
      uploadLoading: false,
      importType: 0,
      dialogVisible: false,
      listLoading: false,
      fileList: [],
      summaryarry: {}
    };
  },

  async mounted() { 
    await this.onSearch()
  },

  methods: {
    remoteMethodBusinessCategory(query) {
      this.searchloading == true;
      this.options = [];
      setTimeout(async () => {
        const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 1, categoryName: query })
        this.searchloading = false
        this.filterList.bussinessCategoryNames = res.data
      }, 200)
    },
    remoteMethodCategoryName1s(query) {
      this.searchloading == true;
      this.options = [];
      setTimeout(async () => {
        const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 2, categoryName: query })
        this.searchloading = false
        this.filterList.categoryName1s = res.data
      }, 200)
    },
    remoteMethodCategoryName2s(query) {
      this.searchloading == true;
      this.options = [];
      setTimeout(async () => {
        const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 3, categoryName: query })
        this.searchloading = false
        this.filterList.categoryName2s = res.data
      }, 200)
    }, 
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    async getlist() {
      if (this.pager.OrderBy == null) {
        this.pager.OrderBy = "businessCategory";
        this.pager.IsAsc = false;
      }
      let pager = this.$refs.pager.getPager();
      let page = this.pager;
      const params = { ...pager, ...page, ... this.filter }
      if (params === false) {
        return;
      }
      this.listLoading = true
      const res = await getPlatformBusinessCategoryList(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.list = data;
    },
    async nSearch() {
      await this.getlist()
    },
    //开始导入
    startImport(importType) {
      this.importType = importType;
      this.uploadLoading = false;
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.upload.clearFiles();
      })
    },
    //取消导入
    cancelImport() {
      this.dialogVisible = false;
    },
    async submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.fileHasSubmit = true;
      this.uploadLoading = true;
      this.$refs.upload.submit();
    },
    async uploadFile(item) {
      if (!this.fileHasSubmit) {
        return false;
      }
      let platform = 0;
      if (this.importType == 1) {
        platform = 1;
      } else {
        if (item.file.name.indexOf("拼多多") > -1) {
          platform = 2;
        }
        else if (item.file.name.indexOf("京东") > -1) {
          platform = 7;
        }
        else if (item.file.name.indexOf("抖音") > -1) {
          platform = 6;
        }
        else {
          this.$message.error("请上传文件名包含拼多多、京东、天猫、淘宝的导入文件！");
          return false;
        }
      }
      this.fileHasSubmit = false;
      this.uploadLoading = true;
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      form.append("platform", platform);
      const res = await importPlatformBusinessCategory(form);
      if (res.code == 1) {
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
        this.dialogVisible = false;
      }
      this.uploadLoading = false;
      this.$refs.upload.clearFiles();
    },
    async uploadChange(file, fileList) {
      let files = [];
      files.push(file)
      this.fileList = files;
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    },
    async onExport(platform) {
      if (this.pager.OrderBy == null) {
        this.pager.OrderBy = "date";
        this.pager.IsAsc = false;
      }

      let filename = '经营类目匹配';
      if (platform == 2) {
        filename = "拼多多经营类目匹配";
      }
      else if (platform == 7) {
        filename = "京东经营类目匹配";
      }
      else if (platform == 6) {
        filename = "抖音经营类目匹配";
      }

      let pager = this.$refs.pager.getPager();
      this.filter.platform = platform;
      let params = { ...pager, ...this.pager, ...this.filter };
      let res = await exportPlatformBusinessCategory(params);
      if (!res?.data) {
        this.$message({ message: "没有数据", type: "warning" });
        return
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', filename + '_' + new Date().toLocaleString() + '_.xlsx')
      aLink.click()
    }
  }
};
</script>

<style lang="scss" scoped></style>
