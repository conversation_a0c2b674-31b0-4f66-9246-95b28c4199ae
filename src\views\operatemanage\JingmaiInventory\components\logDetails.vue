<template>
    <MyContainer>
        <vxetablebase ref="table" id="20241021132955" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;height: 400px;  margin: 0" :loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { jdSkuSyncStockRecordPage } from '@/api/bladegateway/worcestorejk'
const changeStatusList = [
    {
        label: '未调整',
        value: 0
    },
    {
        label: '已申请',
        value: 1
    },
    {
        label: '申请成功',
        value: 2
    },
    {
        label: '申请失败',
        value: 3
    },
]
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'updateTime', label: '操作时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'updateUserName', label: '操作人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'skuStock', label: '操作前值', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'changeStock', label: '操作后值', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'callbackStock', label: '实际修改值', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'sendTime', label: '请求修改时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'responseTime', label: '修改成功时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'status', label: '状态', formatter: (row) => changeStatusList.find(item => item.value == row.status)?.label },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'responseMsg', label: '结果', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    props: {
        id: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                jdSkuRecordId: this.id
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await jdSkuSyncStockRecordPage(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
