<template>
    <container v-loading="pageLoading">
        <!-- <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>      
                <el-form-item label="日期:">
                    <el-date-picker style="width:240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" 
                    start-placeholder="开始" end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="系列编码">
                <el-select v-model="styleCode" multiple filterable remote reserve-keyword placeholder="请输入关键词" clearable :remote-method="remoteMethod" :loading="searchloading"> 
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
                </el-form-item>
                <el-form-item label="商品编码:">
                    <el-input v-model="filter.goodsCode" @keyup.enter.native="onSearch" clearable/>
                </el-form-item>  
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>            
            </el-form> 
        </template>-->
        <ces-table ref="table" :that='that' :isIndex='true'  @sortchange='sortchange' :isSelection='false' @select='selectchange' @cellclick='cellclick' 
         :hasexpand='false' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" :isSelectColumn='false'
         :showsummary='true' :summaryarry='summaryarry'>
        </ces-table>
        <template #footer>
          <my-pagination
            ref="pager"
            :total="total"
            :checked-count="sels.length"
            @get-page="getlist"
          />
        </template>
       
    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import { getProductPackHistory } from '@/api/operatemanage/base/product'
import { number } from 'echarts';
import { getListByStyleCode } from "@/api/inventory/basicgoods"

const tableCols =[
        {istrue:true,prop:'yearMonthDay',label:'日期', width:'120',sortable:'custom',formatter:(row)=> formatTime(row.yearMonthDay,"YYYY-MM-DD")},
        {istrue:true,prop:'styleCode',label:'系列编码', width:'150',sortable:'custom',formatter:(row)=> !row.styleCode? " " : row.styleCode},
        {istrue:true,prop:'goodsCode',label:'商品编码', width:'150',sortable:'custom',formatter:(row)=> !row.goodsCode?" " : row.goodsCode,}, 
        {istrue:true,prop:'goodsName',label:'商品名称', width:'380',formatter:(row)=> !row.goodsName? "" : row.goodsName},       
        //{istrue:true,prop:'costType',label:'分类', width:'80',formatter:(row)=> !row.costType? " " : row.costType},  
        {istrue:true,prop:'cost',label:'包装费', width:'100',sortable:'custom',formatter:(row)=> !row.cost? " " : row.cost},     
        {istrue:true,prop:'costPrice',label:'成本价', width:'auto',formatter:(row)=> !row.costPrice? " " : row.costPrice},
]

const tableHandles=[
        
      ];

export default {
    name: 'YunhanAdminProductpackcost',
    components :{container, MyConfirmButton, cesTable},
    props:{
        filter:{}
    },
    data() {
        return {
            that:this,
            filter1: {
                startDate:null,
                endDate:null,
                goodsCode:null,
                styleCode:null,
                timerange:[formatTime(dayjs().subtract(7,"day"), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")]
            },
            list: [],
            summaryarry:{},
            pager:{OrderBy:"yearMonthDay",IsAsc:false},
            tableCols:tableCols,
            tableHandles:tableHandles,
            styleCode:null,
            total: 0,
            sels: [], 
            selids: [],
            selcodes: [],
            options: [],
            onHandNumber: null,
            onimportfilter:{
                yearmonthday:formatTime(dayjs().subtract(1,"day"), "YYYY-MM-DD"),
            },
            searchloading: false,
            listLoading: false,
            pageLoading: false,
            dialogVisible: false,
            uploadLoading: false,
            editparmVisible: false,
            editparmLoading: false,
            pickerOptions:{
                disabledDate(time){
                return time.getTime()>Date.now();
               }
            },
        };
    },

    async mounted() {
        //await this.onSearch()
    },

    methods: {
        //查询第一页
        async onSearch() {
            if (!this.filter1.timerange) {this.$message({message: "请选择日期",type: "warning",});return;}
            this.filter1.goodsCode = this.filter.goodsCode
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        async getlist(){
            if (this.filter1.timerange) {
                this.filter1.startDate = this.filter1.timerange[0];
                this.filter1.endDate = this.filter1.timerange[1];
            }
            //this.filter.styleCode = this.styleCode.join()
            var pager = this.$refs.pager.getPager();
            var params = {...this.pager, ...pager, ...this.filter1}
            this.listLoading = true
            const res = await getProductPackHistory(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            // this.summaryarry=res.data.summary;
            // if(this.summaryarry)
            //     this.summaryarry.amountPaid_sum=parseFloat(this.summaryarry.amountPaid_sum.toFixed(6));
            data.forEach(d => {
                d._loading = false
            })
            this.list = data
        },
        //排序查询
        async sortchange(column){
            if(!column.order)
                this.pager={};
            else{
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
            }
            await this.onSearch();
        },  
        selectchange:function(rows,row) {
            this.selids=[];
            this.selcodes=[];
            console.log(rows)
            rows.forEach(f=>{
                this.selids.push(f.goodsCode);
                this.selcodes.push(f.styleCode)
            })
        },
        cellclick(row, column, cell, event){
        
        },
    },
};
</script>

<style lang="scss" scoped>

</style>