<template>
    <my-container>
    <div class="body">
        <div style="display: flex; flex-direction: row;align-items: center; justify-content: center;" v-if="main">
            <span style="font-weight: 600;">任务标题：</span>
            <el-input maxlength="100" :disabled="!ispaste"  @change="ischange(true)" v-model="main.taskName" style="width: auto; display: flex;" clearable ></el-input>
        </div>
        <div class="flexcolumn" :class="[!resultshow?'twobox':'twoboxgu']">
            <div class="flexcolumn" v-if="!resultshow">
                <div class="flexrow" > 
                    <span style="font-weight: 600;">产品编码:</span>
                    <el-button type="text" style="margin-left: auto;" v-if="btnshow" @click="onSelctOrderGoods(1)">添加商品明细</el-button>
                </div>
                <el-table
                    :data="tableData"
                    stripe
                    style="width: 100%"
                    @cell-click="handleCurrentChange">
                    <el-table-column
                    type="index"
                    width="40">
                    </el-table-column>
                    <el-table-column
                    prop="sku"
                    label="sku"
                    width="150">
                    <template slot-scope="scope">
                        <div v-if="scope.row.isOK&&ispaste"><el-input :disabled="btnshow"  maxlength="500" v-model="input" @change="ischange(true)" ref="gain" placeholder="请输入内容" @blur="inputblur(scope,1)"></el-input></div>
                        <span v-else style="width: 100%; padding: 5px;" :style="(scope.row.sku)?{}:{color: '#409EFF'}">{{ scope.row.sku?scope.row.sku:'点击输入sku' }}</span>
                    </template>
                    </el-table-column>
                    <el-table-column
                    prop="prouductCode"
                    label="商品编码"
                    width="250">
                    </el-table-column>
                    <el-table-column
                    prop="prouductName"
                    label="商品名称"
                    width="auto">
                    </el-table-column>
                    <el-table-column width="80">
                    <template slot-scope="scope">
                        <el-button
                        size="mini"
                        type="danger"
                        v-if="btnshow"
                        @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                    </template>
                    </el-table-column>
                </el-table>
                <div class="flexrow"  style="margin: 10px 0; font-size: 600;" > 
                    <span style="font-weight: 600;">赠品编码:</span>
                    <el-button type="text" style="margin-left: auto;" v-if="btnshow" @click="onSelctOrderGoods(2)">添加商品明细</el-button>
                </div>
                <el-table
                    :data="tableData1"
                    stripe
                    @cell-click="handleCurrentChange"
                    style="width: 100%">
                    <el-table-column
                    type="index"
                    width="40">
                    </el-table-column>
                    <el-table-column
                    prop="sku"
                    label="赠品"
                    width="150">
                        <template slot-scope="scope">
                            <div v-if="scope.row.isOK&&ispaste"><el-input :disabled="btnshow" maxlength="500" v-model="input1" @change="ischange(true)" ref="gain1" placeholder="请输入内容" @blur="inputblur(scope,2)"></el-input></div>
                            <span v-else style="width: 100%; padding: 5px;" :style="(scope.row.sku)?{}:{color: '#409EFF'}">{{ scope.row.sku?scope.row.sku:'点击输入赠品' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                    prop="prouductCode"
                    label="商品编码"
                    width="250">
                    </el-table-column>
                    <el-table-column
                    prop="prouductName"
                    label="商品名称"
                    width="auto">
                    </el-table-column>
                    <el-table-column width="80">
                    <template slot-scope="scope">
                        <el-button
                        size="mini"
                        type="danger"
                        v-if="btnshow"
                        @click="handleDeletetwo(scope.$index, scope.row)">删除</el-button>
                    </template>
                    </el-table-column>
                </el-table>
            </div>

            <el-dialog title="选择商品编码" :visible.sync="orderGoodschoiceVisible" width='85%' height='500px' append-to-body v-dialogDrag :close-on-click-modal="false">
                <goodschoice v-if="orderGoodschoiceVisible" :ischoice="true" ref="orderGoodschoice" style="z-index:10000;height:500px" />
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="orderGoodschoiceVisible = false">取 消</el-button>
                        <el-button type="primary" @click="onQuerenOrderGoods()">确 定</el-button>
                    </span>
                </template>
            </el-dialog>

            <div class="module" :style="ispaste?{}:{marginTop: '19px'}">
                <shootingcreateindex1 v-if="liutt!=null" @getlist="getlistt" @getalllist="getalllist" @ischange="ischange" :skushow="true"  :btnshow="false" :resultshow="resultshow" :disabled="disabled" :listid="listid" :main="main" :ispaste="ispaste" :alllist="liutt" ref="createindex3" :key="bannum" :bannum="bannum" :isfath="true" :sku="tableDataa"  :name="'carimg'" />
                <!-- <drawImg @getalllist="getalllist" @ischange="ischange" :btnshow="btnshow" :minheight="minheight" :main="main" :ispaste="ispaste" :listid="listid"  ref="createindex3" :alllist="liutt" :name="name" :key="bannum" :bannum="bannum" :sku="tableDataa"></drawImg> -->
            </div>
        </div>
    </div>
</my-container>
</template>

<script>
import shootingcreateindex1 from '@/views/media/shooting/fuJianmanage/shootingcreateindex1';
import drawImg from '@/views/media/shooting/fuJianmanage/drawimgs'
import goodschoice from "@/views/base/goods/goods4.vue";
import MyContainer from "@/components/my-container";
import { saveReferenceMainReferencsForSku} from '@/api/media/referencemanage';

export default {
    name: 'DEMOShootingcreateindex2',
    props:{
        name: {type: String,default: ''},
        btnshow: {type: Boolean,default: true},
        minheight: {type: Boolean,default: true},
        ispaste: {type: Boolean,default: true},
        bannum: {type: Number,default: 1},
        alllist: {type: Object,default: null},
        main: {type: Object,default: null},
        listid: {default: 0},
        resultshow: {type: Boolean,default: false},
        disabled: {type: Boolean,default: false},
    },
    components: {drawImg,goodschoice,MyContainer,shootingcreateindex1},
    data() {
        return {
            drawnum: -1,
            imgUrl: '',
            tomsg: [],
            cutImgSrc: '',
            sizeinput2: '',
            draw:false,
            isshow: false,
            startdemo: null,
            contenteditable: true,
            placetext: '请按Ctrl+v粘贴图片...',
            list:[1],
            canvasimg: [],
            watname: '',
            input: '',
            input1: '',
            sizeinput: '',
            num: -1,
            beiinput: '',
            demohtml: [],
            orderGoodschoiceVisible: false,//选择下单任务商品窗口
            tableData: [],
            tableData1: [],
            btncli: 0,
            liutt:null,
            tableDataa: null,
        };
    },

    async mounted() {
        if(this.name){
            this.watname = this.name;
        }
       
        let _this = this;
        if(_this.alllist){
         _this.liutt = _this.alllist.photo;
        }
        if(_this.alllist){
            _this.tableDataa = _this.alllist.skus;
            let aa = _this.alllist.skus;
            await aa.map((item,i)=>{
                if(item.type===0){
                    _this.tableData.push(item)
                }else if(item.type===1){
                    _this.tableData1.push(item)
                }
            })
        }
        
    },

    methods: {
        getlistt(){
            this.$emit("getlist") 
        },
        ischange(val){
            if(val){
                this.$emit("changefuc",val)
            }
        },
        async getalllist(val){
            let _this = this;
            if(val){
                await _this.$emit('getalllist',val);
            }
            await _this.$emit('getalllist');
        },
        addlist(){
            this.$refs.createindex3.addlist();
            this.ischange(true);
        },
        submitt(){
            this.$refs.createindex3.submitt();
        },
        tosubmitt(){
            this.$refs.createindex3.tosubmitt();
        },
        submitty(){
            this.$refs.createindex3.submitty();
        },
        async onSelctOrderGoods(val) {
            this.orderGoodschoiceVisible = true;
            this.btncli = val;
            // this.$refs.orderGoodschoice.removeSelData();
        },
        //下单发货：选择商品确定
        async onQuerenOrderGoods() {
            var choicelist = await this.$refs.orderGoodschoice.getchoicelist();
            if (choicelist && choicelist.length > 0) {
                //已存在的不添加
                
                if(this.btncli ==1){
                    var temp = this.tableData;
                }else if(this.btncli ==2){
                    var temp = this.tableData1;
                }
                var isNew = true;
                choicelist.forEach((item) => {
                    isNew = true;
                    temp.forEach(old => {
                        if (old.prouductCode == item.goodsCode) {
                            isNew = false;
                        }
                    });
                    if(this.btncli ==1){
                        if (isNew) {
                            this.tableData.push({
                                // prouductCode: item.goodsCode, prouductName: item.goodsName,
                                shopCode: item.shopId, shopName: item.shopName, proCode: item.shopStyleCode,
                                goodsPrice: item.costPrice ?? 0,
                                goodsQty: 1,
                                goodsAmount: item.costPrice ?? 0,

                                referenceManageSkuId : 0,
                                referenceManageTaskId : 0,
                                prouductName : item.goodsName,
                                prouductCode : item.goodsCode,
                                sku : item.inputvalue,
                                type : 0,
                            });
                        }
                    }else if(this.btncli ==2){
                        if (isNew) {
                            this.tableData1.push({
                                // prouductCode: item.goodsCode, prouductName: item.goodsName,
                                shopCode: item.shopId, shopName: item.shopName, proCode: item.shopStyleCode,
                                goodsPrice: item.costPrice ?? 0,
                                goodsQty: 1,
                                goodsAmount: item.costPrice ?? 0,

                                referenceManageSkuId : 0,
                                referenceManageTaskId : 0,
                                prouductName : item.goodsName,
                                prouductCode : item.goodsCode,
                                sku : item.inputvalue,
                                type : 1,
                            });
                        }
                    }
                   
                });
                this.orderGoodschoiceVisible = false;
                this.ischange(true);
                let _this = this;
                _this.changearray();
            }
        },
        changearray(){
            let _this = this;
            let a = _this.tableData;
            let b = _this.tableData1;
            let c = [];
            c.push(...a);
            c.push(...b);
            _this.tableDataa = c;
        },
        handleDelete(index,row){
            let _this = this;
            _this.tableData.splice(index,1);
            _this.ischange(true);
            _this.changearray();
            _this.$message.success("移除成功！");

        },
        handleDeletetwo(index,row){
            let _this = this;
            _this.tableData1.splice(index,1);
            _this.ischange(true);
            _this.changearray();
            _this.$message.success("移除成功！");

        },
        handleCurrentChange(row, event, column) {
            if (event.label === 'sku') {
                this.$set(row, 'isOK', true)
                this.$nextTick(() => {
                    if(row.sku){
                        this.input = row.sku;
                    }
                    this.$refs.gain.focus()
                })
            }else if(event.label === '赠品'){
                this.$set(row, 'isOK', true)
                this.$nextTick(() => {
                    if(row.sku){
                        this.input1 = row.sku;
                    }
                    this.$refs.gain1.focus()
                })
            }else{
                return false
            }
        },
        async inputblur(val,num){
            let _this = this;
            if(num ==1){
                await _this.$nextTick(()=>{
                    _this.tableData[val.$index].sku = _this.input;
                    _this.$set(val.row, 'isOK', false)
                })
                
                _this.input = '';
            }else if(num==2){
                await _this.$nextTick(()=>{
                    _this.tableData1[val.$index].sku = _this.input1;
                    _this.$set(val.row, 'isOK', false)
                })
                
                _this.input1 = '';
            }
        },
    }
        
};
</script>

<style lang="scss" scoped>
.body{
    padding: 10px;
    position: relative;
}
.box{
    padding: 10px 0;
    margin: 20px 0;
    min-height: 500px;
    border: 1px solid #eee;
    display: flex;
    flex-direction: row;
}
.twobox{
    padding: 10px 0;
    margin: 20px 0;
    min-height: 750px;
    display: flex;
    flex-direction: row;
}
.twoboxgu{
    min-height: 750px;
    margin-top: -49px;
    display: flex;
    flex-direction: row;
}
.fourbox{
    padding: 20px;
    margin: 20px 0;
    min-height: 500px;
    border: 1px solid #eee;
    display: flex;
    flex-direction: column;
    .bottombox{
        width: 100%;
        height: 100%;
        // border: 1px solid #aaa;
        padding: 20px 0 20px 0;
        display: flex;
        .bottombox-left{
            flex: 4;
            display: flex;
            align-items: center;
        }
        .bottombox-right{
            flex: 6;
            display: flex;
            flex-direction: column;
            .right-bot{
                flex: 8;
                padding: 20px;
                margin: 0;
                // border: 1px solid #eee;
            }
            .right-top{
                display: flex;
                flex-direction: row;
                flex: 2;
                padding: 20px;
                margin: 0;
                // border: 1px solid #eee;
                .top-right{
                    width: auto;
                    margin-left: 20px;
                }

                .top-left{
                    width: 200px;
                }

            }
        }
    }
}
.imgbox{
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 5;
    position: relative;

}
.flexcenter{
    display: flex;
    justify-content: center;
    align-items: center;
}
.msgbox{
    flex: 5;
    // padding: 50px 0;
}
.fontwei{
    font-weight: 600;
}
.flexcolumn{
    display: flex;
    flex-direction: column;
}
.flexrow{
    display: flex;
    flex-direction: row;
    width: 100%;
}
.pastimg{
    width: 400px;
    height: 400px;
    background-color: #eee;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}
div{
    caret-color: transparent;
}
.point{
    cursor: crosshair;
}
.module{
    // background-color: #eee;
    margin-top: 30px;
    width: 100%;
    min-height: 400px;
}
</style>