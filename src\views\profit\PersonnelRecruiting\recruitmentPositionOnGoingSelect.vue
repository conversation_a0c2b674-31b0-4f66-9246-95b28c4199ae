<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="">
                    <el-select ref="selectUpResId" v-model="chooseName" style="width: 100px" size="mini" clearable
                        placeholder="招聘部门" @clear="() => { filter.ddDeptId = null }">
                        <el-option hidden value="一级菜单" :label="chooseName"></el-option>
                        <el-tree style="width: 200px;" :data="deptList" :props="defaultProps" :expand-on-click-node="false"
                            :check-on-click-node="true" @node-click="handleNodeClick">
                        </el-tree>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.positionName" placeholder="请输入岗位" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.recruiterIds" placeholder="招聘专员" multiple style="width: 150px" size="mini" collapse-tags>
                        <el-option v-for="item in recruiterList" :key="item.ddUserId" :label="item.userName"
                            :value="item.ddUserId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-input  v-model.trim="filter.keywords" style="width: 160px"
                        :maxLength="100" placeholder="关键字查询" clearable>
                        <el-tooltip slot="suffix" class="item" effect="dark"
                            content="部门、岗位、专员、申请人" placement="bottom">
                            <i class="el-input__icon el-icon-question"></i>
                        </el-tooltip>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <ces-table ref="selectTable" row-key="planId" :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='datalist' :isSelection="true" :tableCols='tableCols' :isSelectColumn='true'
            :customRowStyle="customRowStyle" :loading="listLoading"  :selectColumnHeight="'0px'" @select="handleSelectionChange"
            :isBorder="false">
            <template slot='extentbtn'> </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getDataList" />
        </template>
    </my-container>
</template>

<script>
import cesTable from "@/components/Table/tableforvedio.vue";
import MyContainer from "@/components/my-container";
import { formatTime } from "@/utils/tools";
import { getALLDDDeptTree, getDeptUsers } from '@/api/profit/personnel'
import { getPageRecruitmentPlan } from '@/api/profit/hr'

const tableCols = [
    { istrue: true, prop: 'department', label: '招聘部门', width: "120", sortable: 'custom', },
    { istrue: true, prop: 'positionName', align: 'left', label: '招聘岗位', width: "120", sortable: 'custom', },
    {
        istrue: true, prop: 'recruitmentCount', align: 'left', label: '招聘人数', width: "80",
        formatter: (row) => row.onboardCount + '/' + row.recruitmentCount, sortable: 'custom',
    },
    {
        istrue: true, prop: 'recruitmentStartDate', align: 'left', label: '招聘时间', width: "200", formatter: (row) => formatTime(row.recruitmentStartDate, 'YYYY-MM-DD')
            + '至' + formatTime(row.recruitmentEndDate, 'YYYY-MM-DD'), sortable: 'custom',
    },
    { istrue: true, prop: 'recruiters', align: 'left', label: '招聘专员', sortable: 'custom', },
    { istrue: true, prop: 'addedDate', align: 'left', label: '添加时间', width: "120", sortable: 'custom', },
    { istrue: true, prop: 'addedBy', align: 'left', label: '添加人', width: "100", sortable: 'custom', },
    { istrue: true, prop: 'applicant', align: 'left', label: '申请人', width: "100", sortable: 'custom', }
];
export default {
    name: "recruitmentPositionOnGoingSelect", // 招聘岗位
    components: {
        MyContainer,cesTable
    },
    data () {
        return {
            selectRow: null,
            subLoad: false,
            count: '',
            planId: 0,
            diologTitle: '',
            istLoading: false,
            datalist: [
            ],
            islook: false,
            total: 0,
            sels: [], // 列表选中列
            listLoading: false,
            that: this,
            pageLoading: false,
            pager: {},
            tableCols: tableCols,
            finishReason: null,//完成原因
            showFinishDialog: false,//显示完成弹窗
            showHeadcount: false,//显示招聘人数列表
            filter: {
                closeStatus: 0,//计划状态:-1删除、0进行中、1已完成
                ddDeptId: null,//招聘部门
                positionName: null,//岗位名称
                recruiterIds: [],//招聘专员
                closeReason: null,//完成原因
            },
            filterBak: {
                closeStatus: 0,//计划状态:-1删除、0进行中、1已完成
                ddDeptId: null,//招聘部门
                positionName: null,//岗位名称
                recruiterIds: [],//招聘专员
                closeReason: null,//完成原因
            },

            recruiterList: [],
            pageLoading: false,
            // 下拉框选中节点id与名称
            chooseId: '',
            chooseName: '',
            defaultProps: {
                children: 'childDeptList',
                label: 'name'
            },
            deptList: [],
            // dingtalkDate:null
        };
    },
    created () {

    },
    mounted () {
        this.getDeptList();
        this.getRecruiters();
        
        this.onSearch();
        console.log('mounted')
    },
    methods: {
        handleSelectionChange(selection, row){
            if(selection.length > 1){
                this.$refs.selectTable.clearSelection();
                this.$refs.selectTable.toggleRowSelection(row,true);
            }
            if(row)
            {
                this.selectRow = row;
            }
            else
            {
                this.selectRow = null;
            }
        },
        getSelectRow(){
            return this.selectRow;
        },
        // 刷新方法
        onSearch () {
            this.$refs.pager.setPage(1);
            this.getDataList();
        },
        //获取招聘专员
        getRecruiters () {
            let params = {
                deptName: '人事组',
                includeAllChildDpt: 1,
            }
            getDeptUsers(params).then(res => {
                if (res.success) {
                    this.recruiterList = res.data;
                } else {
                    this.$message({ message: res.msg, type: "danger" });
                }
            })
        },
        // 获取部门列表
        async getDeptList () {
            await getALLDDDeptTree().then(res => {
                if (res.success) {
                    this.deptList = res.data.childDeptList;
                } else {
                    this.$message({ message: res.msg, type: "danger" });
                }
            })
        },
        // 节点点击事件
        handleNodeClick (data) {
            // 配置树形组件点击节点后，设置选择器的值，配置组件的数据
            this.chooseName = data.name;
            this.filter.ddDeptId = data.dept_id;
            // 选择器执行完成后，使其失去焦点隐藏下拉框效果
            this.$refs.selectUpResId.blur();
        },
        //获取数据
        async getDataList () {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            this.listLoading = true;
            const res = await getPageRecruitmentPlan(params);
            this.listLoading = false;
            this.total = res.data.total
            this.datalist = res.data.list;
        },

        //列表排序
        sortchange (column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        customRowStyle (row, index) {
            if (row.row?.isend && row.row.isend == 1) {
                let styleJson = {};
                styleJson.color = "rgb(216 216 216)";
                return styleJson
            } else {
                return null
            }

        },
        // 清空查询条件
        resetQuery() {
            this.datalist = [];
            this.selectRow = null;
            this.chooseId = null;
            this.chooseName = null;
            this.filter = Object.assign({}, this.filterBak);
        }
    },
};
</script>
  
<style lang="scss" scoped></style>