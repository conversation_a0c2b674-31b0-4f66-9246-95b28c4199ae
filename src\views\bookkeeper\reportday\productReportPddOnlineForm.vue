<template>
    <my-container>
        <template #header>
            <el-form>
                <el-form-item>
                    <el-button-group>
                        <el-button style="padding: 0;margin: 0;">
                            <el-input v-model.trim="filter.shopName" maxlength="50" placeholder="请输入店铺名称"
                                style="width:160px;" clearable />
                        </el-button>
                    </el-button-group>
                    <el-button-group>
                        <el-button type="primary" @click="loadData" style="margin-left: 5px;">搜索</el-button>
                        <el-button type="primary" @click="reset(true)" style="margin-left: 5px;">重置</el-button>
                    </el-button-group>
                    <el-button-group style="float: right;">
                        展示产品信息 <el-switch v-model="hideProInfo" @change="hidePro"></el-switch>
                    </el-button-group>
                </el-form-item>
                <el-form-item label="已选择店铺:">
                    <div style="max-height: 100px; overflow: auto;">
                        <el-tag :key="tag" v-for="(tag, index) in chooseShops" closable :disable-transitions="true"
                            @close="handleClose(tag, index)">
                            {{ tag }}
                        </el-tag>
                    </div>
                </el-form-item>
            </el-form>
        </template>

        <div style="height: 400px;">
            <vxetablebase :id="'productReportPddOnlineForm20240841438'" :hasSeq="false" :border="true" :align="'center'" ref="table" :that='that' :hasexpand='false'
                :tableData='shopData' :tableCols='tableCols' :isSelectColumn="true" :enableCheckRange="false"
                :showoverflow="'ellipsis'" @checkbox-range-end="callback"   :loading="listLoading"
                :toolbarshow="false">
                <template slot="right">
                    <vxe-column width="170" title="店铺名称" column-key="shopCode">
                        <template #default="{ row, $index }">
                            {{ row.shopName }}
                        </template>
                    </vxe-column>

                    <vxe-column width="100" title="SKU" column-key="shopCode">
                        <template #default="{ row, $index }">
                            <div
                                style="height: 30px; padding-top: 5px; margin-left: 4px; display: flex; flex-direction: row; align-items: center;">
                                <el-badge :value="row.skuSetCount" :hidden="row.skuSetCount <= 0" :max="99" class="item"
                                    type="primary">
                                    <el-link @click="setSku(row)" :underline="false" type="primary">{{ row.skuSetCount > 0 ?
                                        '已设置' : '设置' }}</el-link>
                                </el-badge>
                                <el-button style="margin-left: 20px;font-size: 14px;margin-top: 2px;" type="text"
                                    @click="batchSetRowColVal(row, 'skus')">
                                    批量
                                </el-button>
                            </div>
                        </template>
                    </vxe-column>

                    <vxe-column width="90" title="快递包装费">
                        <template #default="{ row, $index }">
                            <el-input-number @blur="blurNumber($event, row, 'packCost')"
                                @focus="focusNumber($event, row, 'packCost')" type="number" :precision="2" :min="0"
                                :max="10000" :controls="false" v-model.number="row.packCost" style="width:50px;">
                            </el-input-number>
                        </template>
                    </vxe-column>

                    <vxe-column width="110" title="利润率%">
                        <template #default="{ row, $index }">
                            <el-input-number type="number" :precision="2" :min="0" :max="99.99" :controls="false"
                                v-model.number="row.profitRate" style="width:50px;"
                                @blur="blurNumber($event, row, 'profitRate')"
                                @focus="focusNumber($event, row, 'profitRate')">
                            </el-input-number>
                            <el-button type="text" @click="batchSetRowColVal(row, 'profitRate')">
                                批量
                            </el-button>
                        </template>
                    </vxe-column>

                    <vxe-column width="180" title="运费模板">
                        <template #default="{ row, $index }">
                            <el-select v-model="row.freight" placeholder="运费模板" style="width: 140px"
                                @change="changeAmount(row)">
                                <el-option label="机器人专用" value="机器人专用" />
                                <el-option label="默认模板" value="默认模板" />
                                <el-option label="平台默认模板" value="平台默认模板" />
                            </el-select>
                            <el-button type="text" @click="batchSetRowColVal(row, 'freight')">
                                批量
                            </el-button>
                        </template>
                    </vxe-column>

                    <vxe-column width="210" title="优惠券">
                        <template #default="{ row, $index }">
                            <el-select multiple collapse-tags clearable v-model="row.coupon" placeholder="优惠券"
                                style="width: 160px" @change="changeAmount(row)">
                                <el-option label="商品立减券" value="商品立减券" />
                                <el-option label="店铺关注券" value="店铺关注券" />
                                <el-option label="直播专享券" value="直播专享券" />
                                <el-option label="新客立减券" value="新客立减券" />
                            </el-select>
                            <el-button type="text" @click="batchSetRowColVal(row, 'coupon')">
                                批量
                            </el-button>
                        </template>
                    </vxe-column>

                    <vxe-column width="130" title="优惠券金额">
                        <template #default="{ row, $index }">
                            <el-input-number type="number" :precision="2" :min="0" :max="10000" :controls="false"
                                v-model.number="row.couponAmount" style="width:50px;"
                                @blur="blurNumber($event, row, 'couponAmount')"
                                @focus="focusNumber($event, row, 'couponAmount')">
                            </el-input-number>
                            <el-button type="text" @click="batchSetRowColVal(row, 'couponAmount')">
                                批量
                            </el-button>
                        </template>
                    </vxe-column>

                    <vxe-column width="140" title="折扣">
                        <template #default="{ row, $index }">
                            <el-select v-model="row.discount" placeholder="优惠券" style="width: 90px"
                                @change="changeAmount(row)">
                                <el-option label="无" value="无" />
                                <el-option label="限时限量购" value="限时限量购" />
                            </el-select>
                            <el-button type="text" @click="batchSetRowColVal(row, 'discount')">
                                批量
                            </el-button>
                        </template>
                    </vxe-column>

                    <vxe-column width="130" title="折扣值">
                        <template #default="{ row, $index }">
                            <el-input-number type="number" :precision="2" :min="0.01" :max="1" :controls="false"
                                v-model.number="row.discountAmount" style="width:50px;"
                                @blur="blurNumber($event, row, 'discountAmount')"
                                @focus="focusNumber($event, row, 'discountAmount')">
                            </el-input-number>
                            <el-button type="text" @click="batchSetRowColVal(row, 'discountAmount')">
                                批量
                            </el-button>
                        </template>
                    </vxe-column>

                    <vxe-column width="130" title="建议投产">
                        <template #default="{ row, $index }">
                            <el-input-number type="number" :precision="2" :min="0" :max="10000" :controls="false"
                                v-model.number="row.invest" style="width:50px;" @blur="blurNumber($event, row, 'invest')"
                                @focus="focusNumber($event, row, 'invest')">
                            </el-input-number>
                            <el-button type="text" @click="batchSetRowColVal(row, 'invest')">
                                批量
                            </el-button>
                        </template>
                    </vxe-column>

                    <vxe-column width="130" title="日限额">
                        <template #default="{ row, $index }">
                            <el-input-number type="number" :precision="2" :min="0" :max="10000" :controls="false"
                                v-model.number="row.dayLimitAmount" style="width:50px;"
                                @blur="blurNumber($event, row, 'dayLimitAmount')"
                                @focus="focusNumber($event, row, 'dayLimitAmount')">
                            </el-input-number>
                            <el-button type="text" @click="batchSetRowColVal(row, 'dayLimitAmount')">
                                批量
                            </el-button>
                        </template>
                    </vxe-column>

                    <vxe-column field="hide1" width="100" title="星星">
                        <template #default="{ row, $index }">
                            <el-select v-model="row.star" clearable placeholder="星星" style="width: 70px"
                                @change="changeAmount(row)">
                                <el-option label="空白" :value="9"><span>空白</span></el-option>
                                <el-option label="灰色" style="color:gray;size: 20px;" :value="8"><i
                                        class="el-icon-star-on">灰色</i></el-option>
                                <el-option label="红色" style="color:red;size: 20px;" :value="1"><i
                                        class="el-icon-star-on">红色</i></el-option>
                                <el-option label="橙色" style="color:orange;size: 20px;" :value="2"><i
                                        class="el-icon-star-on">橙色</i></el-option>
                                <el-option label="黄色" style="color:yellow;size: 10px;" :value="3"><i
                                        class="el-icon-star-on">黄色</i></el-option>
                                <el-option label="绿色" style="color:green" :value="4"><i
                                        class="el-icon-star-on">绿色</i></el-option>
                                <el-option label="蓝色" style="color:blue" :value="5"><i
                                        class="el-icon-star-on">蓝色</i></el-option>
                                <el-option label="靛色" style="color:indigo" :value="6"><i
                                        class="el-icon-star-on">靛色</i></el-option>
                                <el-option label="紫色" style="color:purple" :value="7"><i
                                        class="el-icon-star-on">紫色</i></el-option>
                            </el-select>
                            <el-button type="text" @click="batchSetRowColVal(row, 'star')">
                                批量
                            </el-button>
                        </template>
                    </vxe-column>

                    <vxe-column field="hide2" width="100" title="旗帜">
                        <template #default="{ row, $index }">
                            <el-select v-model="row.flag" clearable placeholder="旗帜" style="width: 70px;"
                                @change="changeAmount(row)" disabled >
                                <el-option label="空白" :value="9"><span>空白</span></el-option>
                                <el-option label="灰色" style="color:gray" :value="8"><i
                                        class="el-icon-s-flag">灰色</i></el-option>
                                <el-option label="红色" style="color:red" :value="1"><i
                                        class="el-icon-s-flag">红色</i></el-option>
                                <el-option label="橙色" style="color:orange" :value="2"><i
                                        class="el-icon-s-flag">橙色</i></el-option>
                                <el-option label="黄色" style="color:yellow" :value="3"><i
                                        class="el-icon-s-flag">黄色</i></el-option>
                                <el-option label="绿色" style="color:green" :value="4"><i
                                        class="el-icon-s-flag">绿色</i></el-option>
                                <el-option label="蓝色" style="color:blue" :value="5"><i
                                        class="el-icon-s-flag">蓝色</i></el-option>
                                <el-option label="靛色" style="color:indigo" :value="6"><i
                                        class="el-icon-s-flag">靛色</i></el-option>
                                <el-option label="紫色" style="color:purple" :value="7"><i
                                        class="el-icon-s-flag">紫色</i></el-option>
                            </el-select>
                        </template>
                    </vxe-column>

                    <vxe-column field="hide3" width="120" title="运营组">
                        <template #default="{ row, $index }">
                            <el-select filterable clearable v-model="row.groupId" placeholder="运营组" style="width: 80px"
                                @change="changeAmount(row)" disabled>
                                <el-option v-for="item in directorGroupList" :key="row.shopName + 'group-' + item.value"
                                    :label="item.value" :value="item.key"></el-option>
                            </el-select>
                        </template>
                    </vxe-column>

                    <vxe-column field="hide4" width="120" title="运营专员">
                        <template #default="{ row, $index }">
                            <el-select filterable clearable v-model="row.operateSpecialUserId" placeholder="运营专员"
                                style="width: 80px" @change="changeAmount(row)">
                                <el-option v-for="item in directorList" :key="row.shopName + 'osu-' + item.value"
                                    :label="item.value" :value="item.key"></el-option>
                            </el-select>
                            <el-button type="text" @click="batchSetRowColVal(row, 'operateSpecialUserId')">
                                批量
                            </el-button>
                        </template>
                    </vxe-column>

                    <vxe-column field="hide5" width="120" title="运营助理">
                        <template #default="{ row, $index }">
                            <el-select filterable clearable v-model="row.userId" placeholder="运营助理" style="width: 80px"
                                @change="changeAmount(row)">
                                <el-option v-for="item in directorList" :key="row.shopName + 'u1-' + item.value"
                                    :label="item.value" :value="item.key"></el-option>
                            </el-select>
                            <el-button type="text" @click="batchSetRowColVal(row, 'userId')">
                                批量
                            </el-button>
                        </template>
                    </vxe-column>

                    <vxe-column field="hide6" width="120" title="备用">
                        <template #default="{ row, $index }">
                            <el-select filterable clearable v-model="row.userId3" placeholder="备用" style="width: 80px"
                                @change="changeAmount(row)">
                                <el-option v-for="item in directorList" :key="row.shopName + 'u3-' + item.value"
                                    :label="item.value" :value="item.key"></el-option>
                            </el-select>
                            <el-button type="text" @click="batchSetRowColVal(row, 'userId3')">
                                批量
                            </el-button>
                        </template>
                    </vxe-column>

                </template>
            </vxetablebase>
        </div>
        <template #footer>
            <span class="dialog-footer" style="float: right;">
                <el-button type="primary" :loading="saveLoading" v-throttle="1000" @click="save">提 交</el-button>
                <el-button @click="close">取 消</el-button>
            </span>
        </template>


        <vxe-modal title="SKU设置" v-model="sku.visible" :width="1200"  marginSize='-500'>
          <template  #default>
            <my-container>
                <template #header>

                </template>
                <div style="height: 400px;">
                    <vxetablebase :id="'productReportPddOnlineForm20240841438_2'" :tablekey="'15141185543151311511'" :hasSeq="false" :border="true" :align="'center'" ref="skuDetailTable" :that='that'
                        :hasexpand='false' :tableData='skuData' :tableCols='skuTableCols' :isSelectColumn="false"
                        :enableCheckRange="false" :showoverflow="'ellipsis'"   :toolbarshow="false">
                        <template slot="right">
                            <vxe-column width="100" title="到手价" :title-help="{message: '默认：\r\n 入口价：原链接到手价 \r\n 非入口价：(拼单价 * 新折扣) - 新优惠券'}">
                                <template #default="{ row, $index }">
                                    <el-input-number type="number" :precision="2" :min="0" :max="999999.99"
                                        :controls="false" v-model.number="row.price" style="width:70px;"
                                        @blur="blurSkuNumber($event, row, 'price')"
                                        @focus="focusSkuNumber($event, row, 'price')">
                                    </el-input-number>
                                </template>
                            </vxe-column>
                        </template>
                    </vxetablebase>
                </div>
                <template #footer>
                    <span class="dialog-footer" style="float: right;">
                        <el-button type="primary" v-throttle="1000" @click="saveSku">保 存</el-button>
                        <el-button @click="closeSku">取 消</el-button>
                    </span>
                </template>
            </my-container>

        </template>
        </vxe-modal>

    </my-container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import { getDirectorList, getDirectorGroupList, getShopListByCurUserGroup } from "@/api/operatemanage/base/shop"
import { distributionGoodsOnlineAsync, getProCodeSkuInfos } from "@/api/operatemanage/distributiongoodsOnline.js"
import { getProductByProCode } from '@/api/operatemanage/base/product'
import { getUserInfo } from '@/api/operatemanage/productalllink/alllink'
const tableCols = [
    { istrue: true, label: '', width: '100', type: "checkbox", },
]

const skuTableCols = [
    { istrue: true, prop: 'shopGoodsCode', label: 'SKU ID', width: '100', },
    { istrue: true, prop: 'goodsImage', label: '图片', width: '80', type: "images", },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '100', },
    //{ istrue: true, prop: 'goodsName', label: '商品名称', width: '180', },
    { istrue: true, prop: 'norms', label: '款式' },
    { istrue: true, prop: 'size', label: '型号' },
    { istrue: true, prop: 'costPrice', label: '成本价', width: '100', },
    { istrue: true, prop: 'oldPrice', label: '原链接到手价', width: '100', align:"center", tipmesg:'活动价 - 优惠券 \r\n 红色：入口价 \r\n 黑色：非入口价', type:'html',formatter: (row) =>{return "<span style='color:"+(row.isMinPrice?"red":"#606266")+"'>"+row.oldPrice+"</span>"}},
    { istrue: true, prop: 'profitRate', label: '原链接利润率', width: '100', tipmesg:'（原链接到手价-成本-新快递） /  原链接到手价',formatter: (row) => row.profitRate + '%' },
    { istrue: true, prop: 'newUnitPrice', label: '拼单价', width: '100', tipmesg:'默认: \r\n 入口价：（原链接到手价 + 新优惠券） / 新折扣 \r\n 非入口价：（（（成本+新快递）/（1-新利润率））+新优惠券）/新折扣 \r\n\r\n  手动：\r\n（到手价+新优惠券）/新折扣'}
]

export default {
    name: "productReportPddOnlineForm",
    components: { MyContainer, MyConfirmButton, vxetablebase },
    props: {
        filter1: {
            type: Object,
            default: () => { return { productCode: null, groupId: null } }
        }
    },
    data() {
        return {
            that: this,
            shopData: null,
            skuData: null,
            saveLoading: false,
            listLoading: false,
            chooseShops: [],
            tableCols: tableCols,
            skuTableCols: skuTableCols,
            selrows: [],
            skuSelrows: [],
            chooseSkus: [],
            filter: {
                shopName: null
            },
            directorList: [],
            directorGroupList: [],
            sku: {
                visible: false,
                filter: {
                    goodsCode: null
                }
            },
            hideProInfo: false,
            packCost: 0,
            skuTempData: {
                skuShopName: [],
                packCost: 0,
                profitRate: 0,
                couponAmount: 0,
                discountAmount: 0
            },
            skuInfoData: [],
            oldBlurNum: null,
            selGroupId: null
        };
    },
    async mounted() {
        this.loadSelect();
    },
    methods: {
        async loadData() {
            if (this.skuInfoData.length==0) {
                this.listLoading = false;
                this.$message({ type: 'error', message: "未获取到SKU数据，请关闭界面后重试，如若一直无法获取，请联系IT部门解决！",duration:5000});
                return;
            }
            this.listLoading = true;
            let param = { platform: 2, shopName: this.filter.shopName, groupId: this.filter1.groupId };
            let res = await getShopListByCurUserGroup(param);
            if (!res.success) {
                this.listLoading = false;
                return;
            } else {
                let res1 = await getProductByProCode(this.filter1.productCode);
                this.shopData = res.data.list.map(item => {
                    return {
                        shopCode: item.shopCode,
                        shopName: item.shopName,
                        profitRate: 0,
                        freight: "机器人专用",
                        packCost: this.packCost,
                        coupon: [],
                        couponAmount: 0,
                        discount: "无",
                        discountAmount: 1,
                        invest: 0.5,
                        dayLimitAmount: 100,
                        star: res1.data?.star,
                        flag: 7,
                        groupId: this.selGroupId ==null?null:(this.selGroupId +""),
                        operateSpecialUserId: res1.data?.operateSpecialUserId,
                        userId: res1.data?.userId,
                        userId3: res1.data?.userId3,
                        skuSetCount: 0,
                        skus:JSON.parse(JSON.stringify(this.skuInfoData))
                    };
                });
                let selectShop = [];
                this.shopData.forEach(item => {
                    let index = this.selrows.findIndex((v) => {
                        return v.shopName == item.shopName
                    });
                    if (index != -1) {
                        selectShop.push(item);
                        item.packCost = this.packCost;
                        item.profitRate = this.selrows[index].profitRate;
                        item.freight = this.selrows[index].freight;
                        item.coupon = this.selrows[index].coupon;
                        item.couponAmount = this.selrows[index].couponAmount;
                        item.discount = this.selrows[index].discount;
                        item.discountAmount = this.selrows[index].discountAmount;
                        item.invest = this.selrows[index].invest;
                        item.dayLimitAmount = this.selrows[index].dayLimitAmount;
                        item.star = this.selrows[index].star;
                        item.flag = this.selrows[index].flag;
                        item.groupId = this.selrows[index].groupId;
                        item.operateSpecialUserId = this.selrows[index].operateSpecialUserId;
                        item.userId = this.selrows[index].userId;
                        item.userId3 = this.selrows[index].userId3;
                        item.skuSetCount = this.selrows[index].skuSetCount;
                        item.skus = this.selrows[index].skus;
                    }
                });
                this.$refs.table.toggleRowSelection(selectShop, true);
                this.listLoading = false;
            }
        },
        async loadSelect() {
            await this.getDirectorlist();
        },
        handleClose(tag, index) {
            this.chooseShops.splice(index, 1);
            this.selrows.splice(index, 1);
            this.$refs.table.toggleRowSelection(this.selrows, false);
        },
        //全选
        callback(val) {
            //先把当前也的数据全部移除
            this.shopData.forEach(f => {
                let index = this.chooseShops.findIndex((v) => {
                    return v == f.shopName
                });
                if (index != -1) {
                    this.chooseShops.splice(index, 1);
                    this.selrows.splice(index, 1);
                }
            });
            //把选中的添加
            val.forEach(f => {
                let index = this.chooseShops.findIndex((v) => (v == f.shopName));
                if (index == -1) {
                    this.chooseShops.push(f.shopName);
                    this.selrows.push(f);
                }
            });
        },
        changeAmount(row, col) {
            if (col == 'profitRate') {
                row.invest = row.profitRate == 0 ? 0.5 : ((1 / (row.profitRate / 100))+0.5);
                this.setSkuPrice(row.skus, row);
            } else if (col == 'packCost') {
                this.packCost = row.packCost;
                this.selrows.forEach(item => {
                    item.packCost = row.packCost;
                    this.setSkuPrice(item.skus, item);
                });
                this.shopData.forEach(item => {
                    item.packCost = row.packCost;
                    this.setSkuPrice(item.skus, item);
                });
            } else if (col == 'discountAmount') {
                this.setSkuPrice(row.skus, row);
            } else if (col == 'couponAmount') {
                this.setSkuPrice(row.skus, row);
            }
            let index = this.chooseShops.findIndex((v) => {
                return v == row.shopName
            });
            if (index != -1) {
                this.chooseShops.splice(index, 1);
                this.selrows.splice(index, 1);
                this.chooseShops.push(row.shopName);
                this.selrows.push(row);
            }
        },
        async firstLoad() {
            this.reset(false);
            this.listLoading = true;
            this.skuInfoData=[];
            let userRes = await getUserInfo();
            if (userRes?.success) {
                this.selGroupId = userRes.data.groupId;
            }
            let param = { proCode: this.filter1.productCode }
            let res = await getProCodeSkuInfos(param);
            if (res?.success) {
                res.data.forEach(item => {
                    item.profitRate = item.oldPrice == 0 ? 0 : (Math.round((((item.oldPrice - item.costPrice - 0) / item.oldPrice)) * 100 * 100) / 100).toFixed(2);
                    item.setNewPrice = null;
                    if (!item.isMinPrice) {
                        //(((成本价+快递包装费)/(1-利润率)) + 券 ) / 折扣
                        item.newUnitPrice = (Math.round((((item.costPrice + 0) / (1 - 0)) + 0) / 1 * 100) / 100).toFixed(2)
                    } else {
                        item.newUnitPrice = (Math.round((((item.unitPrice - item.couponAmount)) + 0) / 1 * 100) / 100).toFixed(2);
                    }
                    item.price = (item.newUnitPrice * 1) - 0;
                });
                this.skuInfoData = res.data;
            }
            this.loadData();
        },
        reset(isSearch) {
            this.oldBlurNum=null;
            this.shopData = [];
            this.filter = {};
            this.selrows = [];
            this.chooseShops = [];
            this.sku.filter = {};
            this.saveChooseSkus = [];
            this.$refs.table.toggleRowSelection([]);
            this.hideProInfo = false;
            this.hidePro();
            this.packCost = 0;
            if (isSearch) {
                this.loadData();
            }
        },
        batchSetRowColVal(row, col, isSku) {
            let self = this;
            self.$confirm('此操作将覆盖其他行当前列数据, 确定继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let currentVal = row[col];
                if (isSku == null) {
                    self.shopData.forEach((x) => {
                        if (row["shopCode"] != x["shopCode"]) {

                            if (col == 'profitRate') {
                                x[col] = currentVal;
                                //重新计算投产
                                x["invest"] = row["invest"];
                                this.setSkuPrice(x.skus, x);
                            } else if (col == 'skus') {
                                let newPrice = row["skus"].filter(x => x.setNewPrice != null);
                                newPrice.forEach(item => {
                                    x["skus"].forEach(s => {
                                        if (s.shopGoodsCode == item.shopGoodsCode) {
                                            s.price = item.price;
                                            s.setNewPrice = item.setNewPrice;
                                            s.newUnitPrice = (Math.round((s.price + x["couponAmount"]) / x["discountAmount"] * 100) / 100).toFixed(2);
                                        }
                                    })
                                });
                                x["skuSetCount"] = x["skus"].filter(x => x.setNewPrice != null).length;
                            } else if (col == 'couponAmount' || col == 'discountAmount') {
                                x[col] = currentVal;
                                this.setSkuPrice(x.skus, x);
                            }
                            else {
                                x[col] = currentVal;
                            }
                            let index = this.chooseShops.findIndex((v) => {
                                return v == row.shopName
                            });
                            if (index != -1) {
                                this.chooseShops.splice(index, 1);
                                this.selrows.splice(index, 1);
                                this.chooseShops.push(row.shopName);
                                this.selrows.push(row);
                            }
                        }
                    });
                }
            }).catch(() => {

            });
        },
        //保存SKU
        async save() {
            if (this.selrows.length <= 0) {
                this.$message({ message: '请选择需要铺货的店铺', type: "waring" });
                return;
            }
            if (this.selrows.length > 20) {
                this.$message({ message: '一次铺货最多选择20个店铺，请调整', type: "waring" });
                return;
            }
            let param = { groupId: this.filter1.groupId, ProductCode: this.filter1.productCode, ShopData: this.selrows };
            let res = await distributionGoodsOnlineAsync(param);
            if (!res?.data) {
                this.$message({ message: '发起同步失败！', type: "error" });
            } else {
                this.$message({ message: '发起同步成功，请稍后查看同步结果....', type: "success" });
                this.$emit('close');
            }
            this.saveLoading = false;
        },
        async close() {
            this.$emit('close');
        },
        async getDirectorlist() {
            const res1 = await getDirectorList({})
            const res2 = await getDirectorGroupList({})
            this.directorList = [{ key: '0', value: '未知' }].concat(res1.data || []);
            this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);
        },





        async setSku(row) {
            this.skuTempData.skuShopName = row.shopName;
            this.skuTempData.packCost = row.packCost;
            this.skuTempData.profitRate = row.profitRate;
            this.skuTempData.couponAmount = row.couponAmount;
            this.skuTempData.discountAmount = row.discountAmount;
            this.skuData = JSON.parse(JSON.stringify(row.skus));
            this.sku.visible = true;
        },
        focusSkuNumber(e, row, col) {
            this.oldBlurNum = row[col];

        },
        blurSkuNumber(e, row, col) {
            if (row[col] == null) {
                row[col] = 0;
            }
            if (this.oldBlurNum != row[col]) {
                this.changeSkuAmount(row);
            }
        },
        changeSkuAmount(row) {
            row.setNewPrice = row.price;
            row.newUnitPrice = (Math.round((row.price + this.skuTempData.couponAmount) / this.skuTempData.discountAmount * 100) / 100).toFixed(2);
        },
        async closeSku() {
            this.sku.visible = false;
        },
        async saveSku() {
            let newUnitPriceNoPass = this.skuData.filter(x => x.newUnitPrice < 0);
            if (newUnitPriceNoPass.length > 0) {
                this.$message.error('SKUID:' + newUnitPriceNoPass[0].shopGoodsCode + '，拼单价小于0，请调整！');
                return;
            }
            this.shopData.forEach(item => {
                if (item.shopName == this.skuTempData.skuShopName) {
                    item.skus = this.skuData;
                    let count = this.skuData.filter(x => x.setNewPrice != null);
                    item.skuSetCount = count.length;
                    let index = this.chooseShops.findIndex((v) => {
                        return v == this.skuTempData.skuShopName
                    });
                    if (index != -1) {
                        this.chooseShops.splice(index, 1);
                        this.selrows.splice(index, 1);
                        this.chooseShops.push(this.skuTempData.skuShopName);
                        this.selrows.push(item);
                    }
                }
            })
            this.sku.visible = false;
        },
        hidePro() {
            if (!this.hideProInfo) {
                this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('hide1'))
                this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('hide2'))
                this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('hide3'))
                this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('hide4'))
                this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('hide5'))
                this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('hide6'))
            } else {
                this.$refs.table.$refs.xTable.showColumn(this.$refs.table.$refs.xTable.getColumnByField('hide1'))
                this.$refs.table.$refs.xTable.showColumn(this.$refs.table.$refs.xTable.getColumnByField('hide2'))
                this.$refs.table.$refs.xTable.showColumn(this.$refs.table.$refs.xTable.getColumnByField('hide3'))
                this.$refs.table.$refs.xTable.showColumn(this.$refs.table.$refs.xTable.getColumnByField('hide4'))
                this.$refs.table.$refs.xTable.showColumn(this.$refs.table.$refs.xTable.getColumnByField('hide5'))
                this.$refs.table.$refs.xTable.showColumn(this.$refs.table.$refs.xTable.getColumnByField('hide6'))
            }
        },
        setSkuPrice(skus, row) {
            skus.forEach(s => {
                if (!s.isMinPrice) {
                    if (1 - (row.profitRate / 100) == 0) {
                        s.newUnitPrice = 0;
                    } else {
                        s.newUnitPrice = row.discountAmount == 0 ? 0 : (Math.round((((s.costPrice + row.packCost) / (1 - (row.profitRate / 100))) + row.couponAmount) / row.discountAmount * 100) / 100).toFixed(2);
                    }
                } else {
                    s.newUnitPrice = (Math.round(((s.unitPrice - s.couponAmount) + row.couponAmount) / row.discountAmount * 100) / 100).toFixed(2);
                }
                s.profitRate = s.oldPrice == 0 ? 0 : (Math.round((((s.oldPrice - s.costPrice - row.packCost) / s.oldPrice)) * 100 * 100) / 100).toFixed(2);
                s.price = (Math.round(((s.newUnitPrice * row.discountAmount) - row.couponAmount) * 100) / 100).toFixed(2);
                s.setNewPrice = null;
            })
            row.skuSetCount = 0;
        },
        focusNumber(e, row, col) {
            this.oldBlurNum = row[col];
        }
        , blurNumber(e, row, col) {
            let defaultNum = 0;
            if (col == 'discountAmount') {
                defaultNum = 0.01;
            }
            if (row[col] == null) {
                row[col] = defaultNum;
            }
            if (this.oldBlurNum != row[col]) {
                this.changeAmount(row, col);
            }
        }
    },
};
</script>
