<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 94%; width:100%;" @tab-click="tabclick">
            <el-tab-pane label="点餐菜单管理" name="first" style="height: 100%;" lazy>
                <baseMenuManage ref="baseMenuManage" />
            </el-tab-pane>
            <el-tab-pane label="每日订餐" name="second" style="height: 100%;" lazy>
                <orderMenuManage ref="orderMenuManage" />
            </el-tab-pane>
            <el-tab-pane label="汇总订餐" name="third" style="height: 100%;" lazy>
                <orderMenuManageall ref="orderMenuManageall" />
            </el-tab-pane>
            <el-tab-pane label="点餐详情" name="orderDetails" style="height: 100%;" lazy>
                <orderDetails ref="reforderDetails" />
            </el-tab-pane>
            <el-tab-pane label="基础设置" name="settings" style="height: 100%;" lazy>
                <settingsMenuManage ref="settingsMenuManage" />
            </el-tab-pane>
            <el-tab-pane label="公告管理" name="fotrh" style="height: 100%;" lazy>
                <announcementManagement ref="announcementManagement" />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import baseMenuManage from '@/views/profit/baseMenuManage.vue'
import orderMenuManage from '@/views/profit/orderMenuManage.vue'
import settingsMenuManage from '@/views/profit/settingsMenuManage.vue'
import orderMenuManageall from '@/views/profit/orderMenuManageall.vue'
import announcementManagement from '@/views/profit/announcementManagement.vue'

import orderDetails from '@/views/profit/orderDetails.vue'
export default {
    name: 'DingDingShow',
    components: { baseMenuManage, orderMenuManage, settingsMenuManage, orderMenuManageall, orderDetails,MyContainer, announcementManagement },
    data () {
        return {
            activeName: 'first',

        }
    },
    async mounted () {


    },
    methods: {
        async tabclick () {
            this.$nextTick(async () => {
                if (this.activeName == 'first') {
                    this.$refs.baseMenuManage.getOrderMenuType();
                    this.$refs.baseMenuManage.onSearch();
                }
                else if (this.activeName == 'second') {
                    // this.$refs.orderMenuManage.onSearch();
                }
            })
        }
    }
}
</script>

