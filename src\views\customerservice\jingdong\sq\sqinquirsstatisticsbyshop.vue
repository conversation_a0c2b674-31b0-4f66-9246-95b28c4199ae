<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' style="height:93%;" :summaryarry="summaryarry" :isIndex='true'
                :hasexpand='false' @sortchange='sortchange' :tableData='inquirsstatisticslist' @select='selectchange'
                :isSelection='false' :tableCols='tableCols' :loading="listLoading">
                <template slot='extentbtn'>
                    <el-input v-model="filter.sname" v-model.trim="filter.sname" placeholder="姓名" style="width:120px;"
                        disabled="true" :maxlength="50" />
                    <el-input v-model="filter.startDate" style="width:120px;" disabled="true" />至
                    <el-input v-model="filter.endDate" style="width:120px;" disabled="true" />
                </template>
            </ces-table>
        </template>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import {
    getJingDongShopPersonalEfficiencyPageList
} from '@/api/customerservice/jingdonginquirs'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

const tableCols = [
  { istrue: true, prop: 'shopName', label: '店铺名称', width: '200', sortable: 'custom' },
  { istrue: true, prop: 'sname', label: '姓名', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'visitTotalCount', label: '咨询量', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'receptionCount', label: '接待量', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'responseTime', label: '平均响应时间', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'responseRate', label: '30s应答率', width: '80', sortable: 'custom', formatter: (row) => (row.responseRate).toFixed(2) + "%" },

  { istrue: true, prop: 'inquirs', label: '售前接待人数', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'orderers', label: '促成下单人数', width: '80', sortable: 'custom' },
  // { istrue: true, prop: 'orderSalesvol', label: '促成下单商品金额', width: '80', sortable: 'custom' },
  // { istrue: true, prop: 'outSalesvol', label: '促成出库商品金额', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'orderConversionRate', label: '咨询->下单转化率', width: '80', sortable: 'custom', formatter: (row) => (row.orderConversionRate || 0).toFixed(2) + "%" },
  // { istrue: true, prop: 'responseIn30sCount', label: '30s回复人数', width: '100', sortable: 'custom' },

  { istrue: true, prop: 'veryGoodCount', label: '非常满意', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'goodCount', label: '满意', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'normalCount', label: '一般', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'badCount', label: '不满意', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'veryBadCount', label: '非常不满意', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'evaluateCount', label: '总评价数', width: '80', sortable: 'custom' },

  { istrue: true, prop: 'satisfactionRate', label: '满意率', width: '80', sortable: 'custom', formatter: (row) => (row.satisfactionRate || 0).toFixed(2) + "%" },
  { istrue: true, prop: 'unsatisfactionRate', label: '不满意率', width: '80', sortable: 'custom', formatter: (row) => (row.unsatisfactionRate || 0).toFixed(2) + "%" },
  { istrue: true, prop: 'invitationEvaluationCount', label: '邀评数', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'invitationEvaluationRate', label: '邀评率', width: '80', sortable: 'custom', formatter: (row) => (row.invitationEvaluationRate || 0).toFixed(2) + "%" },
  { istrue: true, prop: 'attendanceDays', label: '出勤天数', width: '90', sortable: 'custom' },
  { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '90', sortable: 'custom' },

];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, datepicker, cesTable },
    data() {
        return {
            that: this,
            filter: {
                groupType: 0,
                inquirsType: 0,
                sname: "",
                sdate: [],
                endDate: "",
                startDate: ""
            },
            shopList: [],
            userList: [],
            groupList: [],
            inquirsstatisticslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "shopName", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
        };
    },
    async mounted() {

    },
    created() {

    },
    methods: {
        async dialogOpenAfter(data) {
            this.filter.sdate[0] = data.startDate;
            this.filter.sdate[1] = data.endDate;
            this.filter.startDate = data.startDate;
            this.filter.endDate = data.endDate;
            this.filter.sname = data.sname;
            this.onSearch();
        },
        onSearch() {
            this.getinquirsstatisticsList();
        },
        async getinquirsstatisticsList() {
            const para = { ...this.filter };
            const params = {
                ...this.pager,
                ...para,
            };
            this.listLoading = true;
            const res = await getJingDongShopPersonalEfficiencyPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.inquirsstatisticslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
