<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      :tableData='dahuixionglist' @select='selectchange' :isSelection='false' :showsummary='true'
      :summaryarry='summaryarry' :tableCols='tableCols' :loading="listLoading">
      <el-table-column type="expand">
        <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <template slot='extentbtn'>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="filter.productID" clearable maxlength="20" placeholder="商品ID"
              style="width:150px;" />
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="filter.ShopName" clearable maxlength="30" placeholder="店铺名" style="width:150px;" />
          </el-button>
          <!-- <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="filter.BatchNumber" clearable maxlength="19" placeholder="导入批次"
              style="width:150px;" />
          </el-button> -->
          <!-- <el-button style="padding: 0;margin: 0;">
            <el-select v-model="filter.type" clearable filterable placeholder="类型" class="publicCss">
              <el-option label="佣金率计费" value="佣金率计费" />
              <el-option label="预算计费" value="预算计费" />
            </el-select>
          </el-button> -->
          <el-button style="padding: 0;margin: 0;">
            <el-date-picker style="width:280px" v-model="filter.UseDate" type="datetimerange" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期"></el-date-picker>
          </el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onImportSyj">导入</el-button>
        </el-button-group>
      </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getJdExpressList" />
    </template>

    <el-dialog title="淘宝品牌新享-后台" :visible.sync="dialogVisibleSyj" width="30%" v-dialogDrag>
      <span>
        <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <my-confirm-button style="margin-left: 10px;" size="small" type="success"
            @click="onSubmitupload2">上传</my-confirm-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyj = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>

import { importhPinPaiNewEnjoy_TaoBaoAsync, getPinPaiNewEnjoyList, deletePinPaiNewEnjoyAsync } from '@/api/bookkeeper/reportdayV2'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
const tableCols = [
  { istrue: true, prop: 'shopName', label: '店铺名称', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'shopCode', label: '店铺编码', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'parentOrderId', label: '父订单ID', width: '140', sortable: 'custom' },
  { istrue: true, prop: 'proCode', label: '商品ID', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'proName', label: '商品名称', width: '140', sortable: 'custom' },
  { istrue: true, prop: 'paymentAmount', label: '支付金额', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'superNewCustomerConversionAcceleration', label: '淘宝新客礼金', width: '140', sortable: 'custom' },
  { istrue: true, prop: 'superNewCustomerExtremeAcceleration', label: '淘宝新品礼金', width: '140', sortable: 'custom' },
  // { istrue: true, prop: 'superNewProductConversionAcceleration', label: '超级新品孵化-转化加速', width: '140', sortable: 'custom' },
  { istrue: true, prop: 'estimatedOldCustomerAccelerationCost', label: '淘宝老客礼金', width: '140', sortable: 'custom' },
  { istrue: true, prop: 'totalAmount', label: '总金额', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'yearMonthDay', label: '日期', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'createdTime', label: '导入时间', width: 'auto', sortable: 'custom' },
  // { istrue: true, prop: 'batchNumber', label: '导入批次', width: 'auto', sortable: 'custom' },
  { istrue: true, type: "button", label: '操作', width: "auto", btnList: [{ label: "当日删除", handle: (that, row) => that.deleteBatch(row) }] }
];

const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");

export default {
  name: "investmentSubsidy",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
  data() {
    return {
      that: this,
      filter: {
        ProCode: '',
        PromotePlan: '',
        ShopName: '',
        BatchNumber: null,
        UseDate: [startDate, endDate],
        UseDstartAccountDateate: '',
        endAccountDate: '',
        startUseDate: '',
        endUseDate: '',
        productID: '',
      },
      shopList: [],
      userList: [],
      groupList: [],
      dahuixionglist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: {},
      pager: { OrderBy: "yearMonthDay", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
      platform: 9,
      yearMonth: ""
    };
  },
  async mounted() {
    this.onSearch();
  },
  methods: {
    setplatform(platform) {
      this.platform = platform;
    },
    async deleteBatch(row) {
      var that = this;
      this.$confirm("此操作将删除此批次导入费用数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          await deletePinPaiNewEnjoyAsync({ yearMonthDay: row.yearMonthDay, platform: 9 })
          that.$message({ message: '已删除', type: "success" });
          that.onRefresh()
        });
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    onImportSyj() {
      this.dialogVisibleSyj = true
    },
    async uploadFile2(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      const res = importhPinPaiNewEnjoy_TaoBaoAsync(form);
      this.$message({ message: '上传成功,正在导入中...', type: "success" });
      this.dialogVisibleSyj = false;
    },
    async uploadSuccess2(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
    },
    async onSubmitupload2() {
      this.$refs.upload2.submit()
    },
    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getJdExpressList();
    },
    async getJdExpressList() {
      this.filter.startUseDate = null;
      this.filter.endUseDate = null;
      if (this.filter.UseDate) {
        this.filter.startUseDate = this.filter.UseDate[0];
        this.filter.endUseDate = this.filter.UseDate[1];
      }
      if (/^\d+$/.test(this.filter.BatchNumber) == false && this.filter.BatchNumber != null && this.filter.BatchNumber != "") {
        this.$message.error('请输入正确的批次号！！！！！');
        this.filter.BatchNumber = null;
        return;
      }
      const para = { ...this.filter };
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
        platform: 9,
      };
      this.listLoading = true;
      const res = await getPinPaiNewEnjoyList(params);
      this.listLoading = false;
      this.total = res.data.total
      this.dahuixionglist = res.data.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
