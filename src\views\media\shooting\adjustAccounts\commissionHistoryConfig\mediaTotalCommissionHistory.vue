<template>
    <!-- 薪资核算 历史版本 -->
    <my-container>
        <template #header>
            <el-button-group>
                <el-button style="height: 28px;" type="primary" @click="onSearch">刷新</el-button>
                <el-button style="height: 28px;" type="primary" @click="onExeprotShootingTask">导出</el-button>
            </el-button-group>
        </template>
            <vxetablebase :id="'mediaTotalCommissionHistory'"
            :hasSeq="false"
            :border="true"
            :align="'center'"
            ref="tablex" :that='that' :hasexpand='true'
            :tableCols='tableCols'
            :tableData='tasklist'
            :loading="listLoading"
            :checkbox-config="{labelField: 'id', highlight: true, range: true}"
            >
            </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList"  :page-size="300" />
        </template>
      <!-- 打分页 -->
      <el-dialog :visible.sync="performancesdialogtencil" width="1150px" height="20px" v-dialogDrag  @close="closeDialog">
        <perforManceTemplatenewshoot ref="perforManceTemplatenewshoot" :myValue="parentValue" :historicalversion ="historical" :ddUserId="parentDdUserId"
         :workPosition="parentWorkPosition" :rowc="rows" :versionId="performanceversionId" :postselwork="selwork">
          </perforManceTemplatenewshoot>
      </el-dialog>
      <el-dialog  :visible.sync="percentagecommissions" element-loading-text="拼命加载中" v-dialogDrag
          :append-to-body="true" v-loading="editLoading">
          <div class="bzbjbt">
            <span style="float: left">提成考核</span>
          </div>
          <div class="xptcblbj2">
            <div>
            <div :style="{ 'font-weight': 'bold', 'font-size': '15px' }">及时完成率对应提成</div>
            <el-table :data="inTimeRateList" style="width: 100%;" max-height="250">
              <el-table-column v-if="showStyleNameColumn" prop="styleName" label="款式类型" width="180"></el-table-column>
              <el-table-column prop="equalOrGreaterScore" label="大于或等于" width="180"></el-table-column>
              <el-table-column prop="lessScore" label="小于" width="180"></el-table-column>
              <el-table-column prop="commissionReduced" label="提成比例减" ></el-table-column>
            </el-table>
          </div>
          <div style="padding: 20px 0 0 0;">
            <div :style="{ 'font-weight': 'bold', 'font-size': '15px' }">质量+配合度对应提成</div>
            <el-table :data="adaptabilityDeduct" style="width: 100%;" max-height="250">
              <el-table-column prop="equalOrGreaterScore" label="大于或等于" width="180"></el-table-column>
              <el-table-column prop="lessScore" label="小于" width="220"></el-table-column>
              <el-table-column prop="commissionReduced" label="提成比例减"></el-table-column>
            </el-table>
          </div>
          </div>
      </el-dialog>
   </my-container>
</template>

<script>
import { getHistorytPersonnelPositionInfo,exportPersonnelSalaryHistory, getCommCommissionAssessHistory } from '@/api/media/shootingset'
import MyConfirmButton from "@/components/my-confirm-button";
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import MyContainer from "@/components/my-container";
import perforManceTemplatenewshoot from "@/views/media/shooting/adjustAccounts/commissionConfig/perforManceTemplatenewshoot.vue";

const tableCols = [
    { istrue: true, prop: 'userName', label: '姓名', width: '70',fixed:'left' },
    { istrue: true, prop: 'companyName', label: '公司', width: '65',fixed:'left'  ,fixed:'left'  },
    { istrue: true, prop: 'workPositionStr', label: '工作岗位', width: '85',fixed:'left'  },
    { istrue: true, prop: 'commissionPositionStr', label: '提成岗位', width: '85' ,fixed:'left' },
    // { istrue: true, prop: 'classtype', label: '类型', width: '55'  },
    { istrue: true, prop: 'jycqts', label: '应出勤', width: '70'  },
    { istrue: true, prop: 'ycqts', label: '实际出勤', width: '70'  },
    { istrue: true, prop: 'jBasepay', label: '当月底薪', width: '70'  },
    { istrue: true, prop: 'basepay', label: '应发底薪', width: '70'  },
    { istrue: true, prop: 'jAchievement', label: '当月绩效', width: '70'  },
    { istrue: true, prop: 'achievement', label: '应发绩效', width: '70'  },
    { istrue: true, prop: 'shootingAjustCount', label: '新品', width: '70'},
    { istrue: true, prop: 'vedioTaskAjustCount', label: '短视频拍摄', width: '85'},
    { istrue: true, prop: 'directImgAjustCount', label: '车图', width: '70',  align: 'center' },
    { istrue: true, prop: 'kuangJinFee', label: '跨境提成', width: '70',  align: 'center' },
    { istrue: true, prop: 'douYinFee', label: '抖音提成', width: '70',  align: 'center' },
    { istrue: true, prop: 'microDetailAjustCount', label: '微详情', width: '70'},
    { istrue: true, prop: 'shopDecorationAjustCount', label: '店铺装修', width: '70'},
    { istrue: true, prop: 'packingDesignAjustCount', label: '包装设计', width: '70'},
    { istrue: true, prop: 'dayModImgAjustCount', label: '日常改图', width: '70'},
    { istrue: true, prop: 'quanqingAward', label: '全勤奖', width: '70'  },
    { istrue: true, prop: 'overTimePay', label: '加班补贴', width: '70'  },
    { istrue: true, prop: 'sbSubsidy', label: '设备补助', width: '55'  },
    { istrue: true, prop: 'supplyAgain', label: '福利', width: '55'  },
    { istrue: true, prop: 'subsidy', label: '补贴', width: '55'  },
    { istrue: true, prop: 'fuLiFee', label: '补发', width: '55'  },
    { istrue: true, prop: 'otherAjustCount', label: '其他津贴', width: '70'},
    { istrue: true, prop: 'shouldFee', label: '应发金额', width: '70'},
    { istrue: true, prop: 'shuiDianFee', label: '宿舍水电费', width: '80'},
    { istrue: true, prop: 'cutPayment', label: '责任扣款', width: '70'},
    { istrue: true, prop: 'chiDaoFee', label: '迟到', width: '70'},
    { istrue: true, prop: 'zhaoTuiFee', label: '早退', width: '70'},
    { istrue: true, prop: 'queKaFee', label: '缺卡', width: '70'},
    { istrue: true, prop: 'kuangGongFee', label: '旷工', width: '70'},
    { istrue: true, prop: 'yuZhiFee', label: '预支工资', width: '70'},
    { istrue: true, prop: 'socialSecurityFee', label: '社保扣款', width: '70'},
    { istrue: true, prop: 'otherFee', label: '其他减', width: '70'},
    { istrue: true, prop: 'commissionRate', type: 'pointsclick', style: (row) => ({ color: 'black', cursor: 'pointer'}), fixed:'right' , label: '比例', width: '65', align: 'center', formatter: (row) => row.commissionRate  + "%", handle:(that,row)=>that.handlecommissionClick(row)  },
    { istrue: true, prop: 'performanceStr', type: 'pointsclick', style: (row) => ({ color: row.jAchievement > 0 ? (row.isAssess == true ? '#67c23a' : '#409eff') : 'black', cursor: row.jAchievement > 0 ? 'pointer' : 'text' }), fixed: 'right', label: '绩效分', width: '70', formatter: (row) => row.performanceStr, handle: (that, row) => that.handleClick(row) },
    { istrue: true, prop: 'isCyCommissionbool',fixed:'right' , label: '是否核算', width: '50', align: 'center', formatter: (row) => row.isCyCommission==1  ? "是" : "否"    },
    { istrue: true, prop: 'actualFee', fixed:'right' , label: '实发金额', width: '70'},
    { istrue: true, prop: 'joinedDate', label: '入职日期', width: '98' },
    { istrue: true, prop: 'leaveDate', label: '离职日期', width: '98' },
];
export default {
   components: { MyContainer,vxetablebase ,MyConfirmButton,perforManceTemplatenewshoot },
   props:{
        versionId:{type:String,default:"0"},
    },
   data() {
       return {
           inTimeRateList: [],
           adaptabilityDeduct: [],
           percentagecommissions: false,
           postselwork:null,
           selwork:null,
           performanceversionId:null,//历史版本打分页所需数据
           rowc:null,//打分页所需数据
           rows:null,//打分页所需数据
           parentDdUserId: null,//打分页所需数据
           parentWorkPosition: null,//打分页所需数据
           parentValue: null,//传值区分模板打分页
           performancesdialogtencil: false,//打分页
           historical:3,//区分历史版本打分页和薪资核算打分页
           that: this,
           listLoading: false,
           tableCols: tableCols,
           tasklist:[],
           fileList:[],
           total:0,
           sels: [], // 列表选中列
           pager: { OrderBy: "orderNum", IsAsc: true },
           editformdialog:false,
           editLoading:false,
           archivedialogVisible:false,
           dialogVisibleSyj:false,
           editformTitle:null,
           archiveform:{
            versionName:null,
           },
           editform:{
                openType:2,
                commissionRate:0,
                isCyCommission:1,
                ycqts:1,
                basepay:1
           },
           filter: {
                needCacl:1,
           },
           editformRules: {
                commissionRate: [{ required: true, message: '请填写', trigger: 'blur' }],
                isCyCommission: [{ required: true, message: '请选择', trigger: 'blur' }],
            },
       };
   },
   //向子组件注册方法
   provide () {
       return {
       }
   },
   computed: {
      showStyleNameColumn() {
        return this.inTimeRateList.every(item => item.typeId !== 1)
      }
    },
   async mounted() {
      await this.onSearch();
   },
   methods: {
    async handlecommissionClick(row){
      if(row.userId == '-1' || (row.workPositionStr == "部门经理" && row.commissionPositionStr == "部门经理")){
        return
      }//等于经理就返回
      this.adaptabilityDeduct = []
      this.inTimeRateList = []
      const { data } = await getCommCommissionAssessHistory({userId:row.userId,versionId:this.versionId})
      if(data.inTimeRateList != null && data.inTimeRateList !== undefined && data.inTimeRateList.length > 0){
        this.inTimeRateList = data.inTimeRateList.filter(item => item !== null);
      }
      this.adaptabilityDeduct = data.adaptabilityDeduct
      if(this.adaptabilityDeduct.length > 0 || this.inTimeRateList.length > 0){
        this.percentagecommissions = true
      }
    },
    //打分页关闭
    closeDialog() {
      this.performancesdialogtencil = false;
      this.$nextTick(() => {
          this.$refs.perforManceTemplatenewshoot.closeFilterMonthDay();
        });
    },
    //绩效分页
    handleClick(row) {
      if(row.jAchievement == 0){
          return
      }
      this.performanceversionId = this.versionId;//历史版本所需参数
      this.parentValue = 2;//区分模板和打分页
      this.performancesdialogtencil = true;
      this.rows = row;//行数据传子组件
      this.$nextTick(() => {
        this.$refs.perforManceTemplatenewshoot.getlist();
      });
    },
    async onExeprotShootingTask() {
            this.listLoading = true;
            var res = await exportPersonnelSalaryHistory({versionId:this.versionId});
            if (res?.data?.type == 'application/json') { this.listLoading = false; return;}
            const aLink = document.createElement("a");
            var blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '薪资核算历史版本导出.xlsx')
            aLink.click()
            this.listLoading = false;
    },
    exportDataEvent () {
        this.$refs.tablex.exportData()
    },

    async onSearch() {
        this.$refs.pager.setPage(1);
        this.getTaskList();
    },
    async getTaskList()
    {
        var pager = this.$refs.pager.getPager();
        const params = {
            ...this.filter,
            ...pager,
            ...this.pager,
        };
        this.listLoading = true;
        var res = await getHistorytPersonnelPositionInfo({versionId:this.versionId});
        this.listLoading = false;
        if(!res?.success) return;
        this.tasklist = res.data.list;
        this.total = res.data.total;

        let uniquePostselwork  = [];
        this.tasklist.map(item => {
          let newObj = {
            label: item.workPositionStr,
            value: item.workPosition
          };
          uniquePostselwork .push(newObj);
        });

        this.selwork  = uniquePostselwork.reduce((accumulator, currentItem) => {
          if (!accumulator.some(item => item.value === currentItem.value)) {
            accumulator.push(currentItem);
          }
          return accumulator;
        }, []);
    },

   },
};
</script>
<style lang="scss" scoped>
.content{
   display: flex;
   flex-direction: row;
}
::v-deep .vxetoolbar20221212 {
    position: absolute;
    top: 30px;
    right: 5px;
    padding-top: 0;
    padding-bottom: 0;
    z-index: 999;
    background-color: rgb(255 255 255 / 0%);
}
::v-deep .bzbjbt {
  height: 35px;
  background-color: rgb(255, 255, 255);
  font-size: 18px;
  color: #666;
  border-bottom: 1px solid #dcdfe6;
  box-sizing: border-box;
}
.xptcblbj2 {
  width: 100%;
  box-sizing: border-box;
  padding: 25px;
}
</style>

