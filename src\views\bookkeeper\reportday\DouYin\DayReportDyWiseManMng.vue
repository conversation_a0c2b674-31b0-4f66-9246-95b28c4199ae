<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-row>
                <el-col :span="24">
                    达 人:
                    <el-input v-model.trim="form.wiseManAccountName" style="width:140px;" maxlength="50"
                        clearable></el-input>
                    &nbsp;&nbsp;
                    抖音号:
                    <el-input v-model.trim="form.wiseManAccountCode" style="width:140px;" maxlength="50"
                        clearable></el-input>
                    &nbsp;&nbsp;
                    达人UID:
                    <el-input v-model.trim="form.wiseManUID" style="width:140px;" maxlength="50" clearable></el-input>
                    &nbsp;&nbsp;
                    <el-button type="primary" @click="loadData">查询</el-button>
                    <el-button type="primary" @click="addWiseMan">添加达人</el-button>
                    <el-button type="primary" @click="onImport">导入</el-button>
                    <el-button type="primary" @click="onModel">下载模板</el-button>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24" style="height:450px">
                    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' :tableData='sellist'
                        @sortchange='sortchange' :hasexpandRight="true" :tableCols='tableCols' :isSelection="false"
                        :isSelectColumn='false' :loading="sellistLoading" style="height:430px">
                        <template slot="right">
                            <el-table-column label="操作" width="160">
                                <template #default="{ $index, row }">
                                    <el-button type="text" @click="onEditShow(row)">
                                        编辑
                                    </el-button>
                                    <el-button type="text" @click="onLogShow(row)">
                                        历史记录
                                    </el-button>
                                    <my-confirm-button type="text" @click="delRow(row)">
                                        删除
                                    </my-confirm-button>
                                </template>
                            </el-table-column>
                        </template>
                    </ces-table>
                </el-col>
            </el-row>
            <el-row>
                <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
            </el-row>
        </template>

        <el-dialog title="导入" :visible.sync="dialogUploadData.visible" width="30%" v-dialogDrag append-to-body
            :close-on-click-modal="false">
            <span>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                            action accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success"
                                :loading="dialogUploadData.uploadLoading" @click="onSubmitUpload">{{
                                    (dialogUploadData.uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
                <el-row v-if="dialogUploadData.showError" style="color:darkorange;">
                    <span v-for="(err, errIndex) in dialogUploadData.showErrorList" :key="errIndex">{{ err
                        }};<br /></span>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogUploadData.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="编辑" :visible.sync="dialogEditData.visible" width="40%" v-dialogDrag append-to-body
            :close-on-click-modal="false">
            <el-row>
                <el-col :span="24" style="color: chocolate;">
                    提醒：点击提交后将通过达人UID更新“商务与达人关系”和“业绩（历史业绩保持原达人原抖音号）”
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24">
                    达 人:
                    <el-input v-model.trim="dialogEditData.editForm.wiseManAccountName" style="width:140px;"
                        maxlength="50" clearable></el-input>
                    &nbsp;&nbsp;
                    抖音号:
                    <el-input v-model.trim="dialogEditData.editForm.wiseManAccountCode" style="width:140px;"
                        maxlength="50" clearable></el-input>
                    &nbsp;&nbsp;
                    达人UID:
                    <el-input v-model.trim="dialogEditData.editForm.wiseManUID" style="width:140px;" maxlength="50"
                        disabled clearable></el-input>
                </el-col>
            </el-row>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogEditData.visible = false">取 消</el-button>
                    <my-confirm-button type="submit" :loading="dialogEditData.saveLoading" @click="onEditOk" />
                </div>
            </template>
        </el-dialog>

        <el-dialog title="历史记录" :visible.sync="dialogLogData.visible" width="50%" v-dialogDrag append-to-body
            :close-on-click-modal="false">
            <el-table :data="dialogLogData.logList" height="360" border>
                <el-table-column label="序号" width="50">
                    <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                </el-table-column>
                <el-table-column prop="wiseManUID" label="达人UID" width="120" />
                <el-table-column prop="wiseManAccountName" label="达人（原）" />
                <el-table-column prop="wiseManAccountCode" label="达人抖音号（原）" />
                <el-table-column prop="newWiseManAccountName" label="达人（新）" />
                <el-table-column prop="newWiseManAccountCode" label="达人抖音号（新）" />
                <el-table-column prop="createdUserName" label="操作人" width="80" />
                <el-table-column prop="createdTime" label="操作时间" width="150" />
            </el-table>
        </el-dialog>
    </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode, DeductOrderZrDeptActions } from "@/utils/tools";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import {
    GetAllWiseManList, SaveDouYinWiseManAsync, GetAllWiseManPageList, ImportAllWiseMan,
    GetProductDyWiseManLogList
} from "@/api/bookkeeper/reportdayDouYin";
import SaleThemeAnalysisDetail from "../../details/saleThemeAnalysisDetail.vue";

const tableCols = [
    { istrue: true, prop: 'wiseManAccountName', label: '达人', sortable: true },
    { istrue: true, prop: 'wiseManAccountCode', label: '抖音号', sortable: true },
    { istrue: true, prop: 'wiseManUID', label: '达人UID', sortable: true },
]
export default {
    name: "DayReportDyWiseManMng",
    components: { cesTable, MyContainer, MyConfirmButton },
    data() {
        return {
            that: this,
            platform: 0,
            depts: [],
            userOpts: [],
            form: {
                id: 0,
                dept: '',
                deptUserId: null,
                deptUserName: null
            },
            sellist: [],
            total: 0,

            summaryarry: {},
            pager: { OrderBy: "createdtime", IsAsc: false },
            sels: [],
            selids: [],
            sellistLoading: false,
            tableCols: tableCols,
            mode: 3,
            zrDeptActions: DeductOrderZrDeptActions,

            //summaryarry: {},
            pageLoading: false,
            curRow: null,
            formEditMode: true,//是否编辑模式          


            fileList: [],
            fileparm: {},

            dialogUploadData: {
                title: "",
                visible: false,
                uploadLoading: false,
                showError: false,
                showErrorList: [],
            },

            dialogEditData: {
                visible: false,
                saveLoading: false,
                selRow: {},
                editForm: {},
            },
            dialogLogData: {
                visible: false,
                logList: [],
            },
        };
    },
    async mounted() {
    },
    computed: {

    },
    methods: {
        async addWiseMan() {
            this.pageLoading = true;
            let dto = { ...this.form };
            let rsp = await SaveDouYinWiseManAsync(dto);
            if (rsp && rsp.success) {
                this.$message.success('操作成功！');
                this.loadData();
            }

            this.pageLoading = false;
        },
        onDeptUserIdChg() {
            let opt = this.userOpts.find(x => x.value == this.form.deptUserId);
            if (opt) {
                this.form.deptUserName = opt.label;
            }
            else {
                this.form.deptUserId = null;
                this.form.deptUserName = null;
            }

        },
        async remoteSearchUser(parm) {
            this.userOpts = [];
            if (!parm) {

                return;
            }
            var dynamicFilter = { field: 'nickName', operator: 'Contains', value: parm }
            var options = [];
            const res = await getUserListPage({ currentPage: 1, pageSize: 50, dynamicFilter: dynamicFilter })
            res?.data?.list.forEach(f => {
                this.userOpts.push({ value: f.id, label: f.nickName })
            })

        },
        async delRow(row) {
            let delDto = { ...row };
            delDto.id = -delDto.id;
            let rsp = await SaveDouYinWiseManAsync(delDto);
            if (rsp && rsp.success) {
                this.$message.success('操作成功！');
                this.loadData();
            }
        },
        async loadData() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        async getList() {
            this.pageLoading = true;
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.form };
            let rsp = await GetAllWiseManPageList(params);
            if (rsp && rsp.success) {
                this.sellist = rsp.data.list;
                this.total = rsp.data.total;
            }
            this.pageLoading = false;
        },
        onClose() {
            this.$emit('close');
        },
        async onSave(isClose) {
            if (await this.save()) {
                this.$emit('afterSave');
                if (isClose)
                    this.$emit('close');
            }
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = { OrderBy: "createdtime", IsAsc: false };
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.loadData();
        },

        async onModel() {
            window.open("/static/excel/dayreport/抖音达人导入模板.xlsx", "_blank");
        },
        async onImport() {
            this.dialogUploadData.showError = false;
            this.dialogUploadData.showErrorList = [];
            this.dialogUploadData.visible = true;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.dialogUploadData.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            var res = await ImportAllWiseMan(form);
            if (res?.success) {
                this.$message({ message: "导入成功", type: "success" });
                this.dialogUploadData.showError = false;
                this.dialogUploadData.visible = false;
            }
            else {
                if (res?.data) {
                    this.dialogUploadData.showErrorList = res?.data;
                    this.dialogUploadData.showError = true;
                }
            }
            this.dialogUploadData.uploadLoading = false;
        },
        async onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            if (this.dialogUploadData.showError == false) {
                this.dialogUploadData.visible = false;
                await this.loadData();
            }
        },
        onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadRemove(file, fileList) {
            this.fileList = [];
        },
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        onEditShow(row) {
            this.dialogEditData.visible = true;
            this.dialogEditData.selRow = row;
            let con = {
                id: row.id,
                wiseManAccountCode: row.wiseManAccountCode,
                wiseManAccountName: row.wiseManAccountName,
                wiseManUID: row.wiseManUID,
            }
            this.dialogEditData.editForm = con;
        },
        async onEditOk() {
            this.dialogEditData.saveLoading = true;
            let rsp = await SaveDouYinWiseManAsync(this.dialogEditData.editForm);
            this.dialogEditData.saveLoading = false;
            if (rsp && rsp.success) {
                this.dialogEditData.selRow.wiseManAccountCode = this.dialogEditData.editForm.wiseManAccountCode;
                this.dialogEditData.selRow.wiseManAccountName = this.dialogEditData.editForm.wiseManAccountName;
                this.$message.success('操作成功！');
                //this.loadData();
                this.dialogEditData.visible = false;
            }
        },
        async onLogShow(row) {
            this.dialogLogData.visible = true;
            let rsp = await GetProductDyWiseManLogList({ oldId: row.id });
            if (rsp && rsp.success) {
                this.dialogLogData.logList = rsp.data;
            }
        },

    },
};
</script>
<style lang="scss" scoped>
.tempdiv ::v-deep img {
    width: auto;
    max-width: 1000px;
}
</style>