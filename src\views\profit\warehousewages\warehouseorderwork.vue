<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <!-- <el-form-item label="仓库">
                    <el-select v-model="filter.warehouseName" style="width: 100px" size="mini" @change="onSearch">
                        <el-option label="诚信仓" value="诚信仓"></el-option>
                        <el-option label="爆款仓" value="爆款仓"></el-option>
                        <el-option label="孵化仓" value="孵化仓"></el-option>
                    </el-select>
                </el-form-item> -->
                <el-form-item label="时间">
                    <el-date-picker style="width: 280px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间"
                        :picker-options="pickerOptions">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.account" placeholder="账号" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <!-- <el-form-item label="">
                    <el-input v-model.trim="filter.accountPostCode" placeholder="一级岗位编码" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.accountPostName" placeholder="一级岗位名称" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.accountTwoPostName" placeholder="二级岗位名称" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item> -->
                <el-form-item label="">
                    <el-input v-model.trim="filter.orderNoInner" placeholder="内部单号" style="width:120px;" clearable
                        oninput="if(value){value=value.replace(/[^\-\d]/g,'')} if(value>**********){value=**********} if(value<0){value=0}"
                        maxlength="20" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
                :isSelectColumn="false" :loading="listLoading" :tableHandles="tableHandles1">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getWarehouseWagesOrderWorkPageList } from '@/api/profit/warehousewages';
const tableCols = [
    { istrue: true, prop: 'occurTime', label: '时间', width: '150', sortable: 'custom', formatter: (row) => formatTime(row.occurTime, 'YYYY-MM-DD HH:mm:ss') },
    //{ istrue: true, prop: 'warehouseName', label: '仓库', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'account', label: '账号', width: '130', sortable: 'custom' },
    //{ istrue: true, prop: 'accountUserName', label: '真实姓名', width: '100', sortable: 'custom' },
    //{ istrue: true, prop: 'accountPostCode', label: '一级岗位编码', width: '120', sortable: 'custom' },
    //{ istrue: true, prop: 'accountPostName', label: '一级岗位名称', width: '120', sortable: 'custom' },
    //{ istrue: true, prop: 'accountTwoPostName', label: '二级岗位名称', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'orderNoInner', label: '内部单号', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'goodsCount', label: '数量', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'getValueName', label: '取值名称', width: '100', sortable: 'custom' },
    //{ istrue: true, prop: 'occurWages', label: '当时工价', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'computeTime', label: '计算时间', width: '150', sortable: 'custom', formatter: (row) => row.computeTime == null ? null : formatTime(row.computeTime, 'YYYY-MM-DD HH:mm:ss') },
    //{ istrue: true, prop: 'errorInfo', label: '描述', width: '200', sortable: 'custom' },
];
const tableHandles1 = [
    //{ label: "导入", handle: (that) => that.onImport() },
    // { label: "导出", handle: (that) => that.onExport() },
];
export default {
    name: 'warehouseorderwork',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow },
    props: {

    },
    data() {
        return {
            that: this,
            filter: {
                timerange: [
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                ],
                startDate: null,
                endDate: null,
                //warehouseName: "诚信仓",
                isNotUserName: false,
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "occurTime", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            pickerOptions: {
                shortcuts: [
                    {
                        text: '昨天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime());
                            picker.$emit('pick', [start, start]);
                        }
                    }, {
                        text: '近三天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime());
                            const end = new Date(new Date(tdate.toLocaleDateString()));
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一周',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 5);
                            const end = new Date(new Date(tdate.toLocaleDateString()).getTime() + 3600 * 1000 * 24 * 5);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一个月',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 31);
                            console.log("获取前一个月的时间", tdate.getDay());
                            const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
            },
            tableHandles1: tableHandles1,
        };
    },
    async mounted() {
        await this.onSearch()
    },
    methods: {
        //查询第一页
        async onSearch() {
            console.log(this.filter)
            if (!this.filter.timerange) {
                this.$message({ message: "请选择时间", type: "warning", });
                return;
            }
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.$message({ message: "请先选择时间", type: "warning" });
                return false;
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const res = await getWarehouseWagesOrderWorkPageList(params)
            this.listLoading = false;
            if (!res?.success) {
                return
            }
            console.log(res.data, 'res.data')
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            data.forEach(d => {
                d._loading = false;
            })
            this.list = data;
        },
        //排序查 询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            console.log(rows)
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
    },
};
</script>

<style lang="scss" scoped></style>
