<template>
    <!-- 员工设置 + 行内编辑 -->
    <my-container v-loading="pageLoading">
            <vxetablebase :id="'accountmanage202301291318001'" :that='that' height="98%" border="defalut" ref="xtable"
                class="draggable-table" :isIndex='true' :hasexpand='false' :isSelectColumn='true' :tableData='tasklist'
                :showsummary='true' :summaryarry="summaryarry"
                :tableCols='tableCols' tablekey="accountmanage" :loading="listLoading" :isBorder="false" :editconfig="{
                    trigger: 'manual', mode: 'row', showStatus: true, showIcon: false
                    , autoClear: false
                }" :validRules="validRules" @sortchange='sortchange'>
                <template slot='right'>
                    <vxe-column title="操作" width="160" fixed="right">
                        <template #default="{ row }">
                            <template v-if="row.editstatus && row.editstatus == 1">
                                <vxe-button size="mini" @click="saveRowEvent(row)">保存</vxe-button>
                                <vxe-button size="mini" @click="cancelRowEvent(row)">取消</vxe-button>
                            </template>
                            <template v-else>
                                <vxe-button size="mini" @click="editRowEvent(row)">编辑</vxe-button>
                                <vxe-button size="mini" @click="onDel(row)">删除</vxe-button>
                            </template>
                        </template>
                    </vxe-column>
                </template>
                <template slot='extentbtn'>
                    <div style="margin:10px 5px 5px 0;">
                        <span style="padding: 0;margin-right:2px;">
                            <el-input style="width:35%;" v-model="filter.userName" v-model.trim="filter.userName"
                                :maxlength=100 placeholder="姓名" @keyup.enter.native="onSearch" clearable />
                        </span>
                        <span style="padding: 0;margin-right:5px;">
                            <el-button type="primary" @click="onSearch">查询</el-button>
                        </span>
                        <el-button-group style="padding: 0;margin-right:5px;">
                            <el-button type="primary" @click="onOpenAction">引入员工</el-button>
                            <el-button type="primary" @click="saveOrders">排序保存</el-button>
                        </el-button-group>
                        <span style="padding: 0;margin-right:5px;">
                            <el-button type="primary" @click="addNew">新增员工</el-button>
                        </span>
                    </div>
                </template>
            </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList"
                :page-size="100" />
        </template>
         <el-dialog :title="editformTitle" top="20px" :visible.sync="editformdialog" width="50%" :close-on-click-modal="false"
            v-loading="editLoading" element-loading-text="拼命加载中" v-dialogDrag :append-to-body="true">
            <shootYuanGongList v-if="editformdialog" style="height: 480px;" ref="shootYuanGongList"></shootYuanGongList>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="editformdialog = false">取 消</el-button>
                    <my-confirm-button type="submit" @click="onSubmit" />
                </span>
            </template>
        </el-dialog>

        <el-dialog :visible.sync="showAddnew" width="750px" v-dialogDrag :close-on-click-modal="true">
            <el-form :model="addForm" ref="addForm" label-width="120px" :rules="addFormRules">
              <div class="bzjzcjrw">
                  <div class="bt">
                      <span style="float: left">新增员工</span>
                  </div>
                  <div class="bzccjlx">
                    <div class="lxwz">员工姓名</div>
                    <div class="lxwz2 formtop">
                        <el-form-item prop="employeeName"  label=" " label-width="12px">
                          <el-select v-model="addForm.userId" filterable placeholder="请选择员工姓名" :filter-method="remoteMethod">
                            <el-option
                              v-for="(item, index) in optionslist"
                              :key="item.value + index"
                              :label="item.label"
                              :value="item.value">
                              <span>{{item.label}}</span>
                              <span  style=" color: #8492a6; ">({{item.extData.position}},{{item.extData.empStatusText}}{{item.extData.jstUserName ? ","+item.extData.jstUserName:""}})</span>
                              <span style=" color: #8492a6; "> {{item.extData.deptName}}</span>
                            </el-option>
                          </el-select>
                        </el-form-item>
                    </div>
                  </div>
              </div>
            </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="showAddnew = false">取 消</el-button>
            <el-button type="primary" @click="addemployee">提 交</el-button>
          </span>
        </el-dialog>
    </my-container>
</template>
<script>
import YhUserelector from '@/components/YhCom/yh-userselector.vue'
import vxetablebase from "@/components/VxeTable/vxetablemedianew.vue";
import MyContainer from "@/components/my-container";
// import { formatTime } from "@/utils";
import shootYuanGongList from '@/views/media/shooting/shootAutoAssign/shootYuanGongList';
import { addShootingFpSet, editShootingFpSet, deleteShootingFpSet, getShootingFpInfo ,sortShootingFpSet,addShootingFpSetTemp} from '@/api/media/shootingset'
import { getDirectorGroupList } from '@/api/operatemanage/base/shop';
import MyConfirmButton from "@/components/my-confirm-button";
import { getShootOperationsGroup} from '@/api/media/mediashare';
import Sortable from 'sortablejs'
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser'
export default {
    props: {
        listtype: { type: Number, default: 99 },
        //1 任务,2 已完成,3 确认信息,4 已使用,5 统计列表,6 信息存档,7存档,8回收站
    },
    components: { MyContainer, MyConfirmButton, shootYuanGongList, vxetablebase, YhUserelector },
    data() {
        return {
            that: this,
            pageLoading: false,
            showAddnew : false,
            addForm: {
              userId: '',
            },
            orgOptions: [], // 原始选项数据（用于回退）
            optionslist: [],
            positionOrder: false,
            summaryarry: {},
            tasklist: [],
            retdata: [],
            sels: [], // 列表选中列
            brandList :[],
            operatorList :[],
            listLoading: false,
            //人员编辑模块
            editLoading: false,
            editformdialog: false,
            editformTitle: null,

            total: 0,
            pager: { OrderBy: "orderNum", IsAsc: true },
            filter: {},

            //表格编辑校验
            validRules: {
                // companyName: [
                //     { required: true, message: '必填' }
                // ],
                // workPosition: [
                //     { required: true, message: '必填' }
                // ],
                // commissionPosition: [
                //     { required: true, message: '必填' }
                // ],
                // classtype: [
                //     { required: true, message: '必填' }
                // ],
            },
            addFormRules: {
              postNames: [
                { required: true, message: '请输入员工姓名', trigger: 'blur' }
              ],
            }
        };
    },
    computed: {
        tableCols() {
            let tableCols = [
                { istrue: true, type: '', prop: 'userName', label: '姓名', width: '70', fixed:"left" },
                { istrue: true, type: '', prop: 'companyName', label: '公司',   width: '50' , fixed:"left"},
                { istrue: true, type: '', prop: 'workPositionStr', label: '工作岗位', width: '80' , fixed:"left" },
                { istrue: true, type: '', prop: 'commissionPositionStr', label: '提成岗位', width: '80'  , fixed:"left"},

            ];
            switch (this.listtype) {
                case 1:
                    //新品拍摄
                    tableCols= tableCols.concat([
                        { istrue: true, type: 'split'   },
                        { istrue: true, type: 'switch', prop: 'shootFpTaskPhotoIsCyFpBool', label: '照片分配', width: '80' },
                        { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'shootFpTaskPhotoFpRate', label: '照片比例%', width: '55' , formatter: (row) => row.shootFpTaskPhotoFpRate + "%"},
                        { istrue: true, type: '', prop: 'shootFpTaskPhoto', label: '分配任务', width: '50' },
                        { istrue: true, type: '', prop: 'shootFpTaskPhotoOver', label: '完成任务', width: '50' },

                        { istrue: true, type: 'split'  },
                        { istrue: true, type: 'switch', prop: 'shootFpTaskVedioIsCyFpBool', label: '视频分配', width: '80' },
                        { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'shootFpTaskVedioFpRate', label: '视频比例%', width: '55' , formatter: (row) => row.shootFpTaskVedioFpRate + "%"},
                        { istrue: true, type: '', prop: 'shootFpTaskVedio', label: '分配任务', width: '50' },
                        { istrue: true, type: '', prop: 'shootFpTaskVedioOver', label: '完成任务', width: '50' },
                        { istrue: true, type: 'split'  },
                        { istrue: true, type: 'switch', prop: 'shootFpTaskMeiGongIsCyFpBool', label: '详情分配', width: '80' },
                        { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'shootFpTaskMeiGongFpRate', label: '详情比例%', width: '55' , formatter: (row) => row.shootFpTaskMeiGongFpRate + "%"},
                        { istrue: true, type: '', prop: 'shootFpTaskMeiGong', label: '分配任务', width: '50' },
                        { istrue: true, type: '', prop: 'shootFpTaskMeiGongOver', label: '完成任务', width: '50' },
                        { istrue: true, type: 'split'   },
                        { istrue: true, type: 'switch', prop: 'shootFpTaskModelIsCyFpBool', label: '建模分配', width: '80' },
                        { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'shootFpTaskModelFpRate', label: '建模比例%', width: '55' , formatter: (row) => row.shootFpTaskModelFpRate + "%"},
                        { istrue: true, type: '', prop: 'shootFpTaskModel', label: '分配任务', width: '50' },
                        { istrue: true, type: '', prop: 'shootFpTaskModelOver', label: '完成任务', width: '50' },
                        { istrue: true, type: 'split'   },
                    ]);
                    break;
                case 2:
                    //短视频
                    tableCols=   tableCols.concat([
                        { istrue: true, type: 'split'   },
                        { istrue: true, type: 'switch', prop: 'shortVedioIsCyFpBool', label: '拍摄分配', width: '80' },
                        { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'shortVedioFpRate', label: '拍摄比例%', width: '85' , formatter: (row) => row.shortVedioFpRate + "%"},
                        { istrue: true, type: '', prop: 'shortVedioFpTask', label: '分配任务', width: '80' },
                        { istrue: true, type: '', prop: 'shortVedioFpTaskOver', label: '完成任务', width: '80' },
                        { istrue: true, type: 'split'   },
                        // { istrue: true, type: 'switch', prop: 'shortCuteIsCyFpBool', label: '剪辑分配', width: '80' },
                        // { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'shortCuteFpRate', label: '剪辑比例%', width: '85', formatter: (row) => row.shortCuteFpRate + "%" },
                    ]);
                    break;
                case 3:
                    //直通车图
                    tableCols=   tableCols.concat([
                        { istrue: true, type: 'editselectmore', prop: 'directImgOperatorLiostInfo', label: '运营小组', width: '80'  ,options: this.operatorList},
                        { istrue: true, type: 'split'   },
                        { istrue: true, type: 'switch', prop: 'directImgPhotoIsCyFpBool', label: '照片分配', width: '80' },
                        { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'directImgPhotoFpRate', label: '照片比例%', width: '85' , formatter: (row) => row.directImgPhotoFpRate + "%"},
                        { istrue: true, type: '', prop: 'directImgPhotoFpTask', label: '分配任务', width: '80' },
                        { istrue: true, type: '', prop: 'directImgPhotoFpTaskOver', label: '完成任务', width: '80' },
                        { istrue: true, type: 'split'   },
                        { istrue: true, type: 'switch', prop: 'directImgDetailIsCyFpBool', label: '美工分配', width: '80' },
                        { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'directImgDetailFpRate', label: '美工比例%', width: '85', formatter: (row) => row.directImgDetailFpRate + "%" },
                        { istrue: true, type: '', prop: 'directImgDetailFpTask', label: '分配任务', width: '80' },
                        { istrue: true, type: '', prop: 'directImgDetailFpTaskOver', label: '完成任务', width: '80' },
                        { istrue: true, type: 'split'   },
                        { istrue: true, type: 'switch', prop: 'directImgModelIsCyFpBool', label: '建模分配', width: '80' },
                        { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'directImgModelFpRate', label: '建模比例%', width: '85' , formatter: (row) => row.directImgModelFpRate + "%"},
                        { istrue: true, type: '', prop: 'directImgModelFpTask', label: '分配任务', width: '80' },
                        { istrue: true, type: '', prop: 'directImgModelFpTaskOver', label: '完成任务', width: '80' },
                        { istrue: true, type: 'split'   },
                    ]);
                    break;
                case 4:
                    //微详情
                    tableCols=   tableCols.concat([
                        { istrue: true, type: 'split'   },
                        { istrue: true, type: 'switch', prop: 'microVideoIsCyFpBool', label: '视频分配', width: '80' },
                        { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'microVideoFpRate', label: '视频比例%', width: '85' , formatter: (row) => row.microVideoFpRate + "%"},
                        { istrue: true, type: '', prop: 'microVideoFpTask', label: '分配任务', width: '80' },
                        { istrue: true, type: '', prop: 'microVideoFpTaskOver', label: '完成任务', width: '80' },
                        { istrue: true, type: 'split'   },
                        { istrue: true, type: 'switch', prop: 'microModelIsCyFpBool', label: '建模分配', width: '80' },
                        { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'microModelFpRate', label: '建模比例%', width: '85' , formatter: (row) => row.microModelFpRate + "%"},
                        { istrue: true, type: '', prop: 'microModelFpTask', label: '分配任务', width: '80' },
                        { istrue: true, type: '', prop: 'microModelFpTaskOver', label: '完成任务', width: '80' },
                        { istrue: true, type: 'split'   },
                    ]);
                    break;
                case 5:
                    //日常改图
                    tableCols=  tableCols.concat([
                        { istrue: true, type: 'editselectmore', prop: 'changeImgOperatorLiostInfo', label: '运营小组', width: '80'  ,options: this.operatorList},
                        { istrue: true, type: 'split'   },
                        { istrue: true, type: 'switch', prop: 'changeImgPhotoIsCyFpBool', label: '照片分配', width: '80' },
                        { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'changeImgPhotoFpRate', label: '照片比例%', width: '55' , formatter: (row) => row.changeImgPhotoFpRate + "%"},

                        { istrue: true, type: '', prop: 'changeImgPhotoFpTask', label: '分配任务', width: '50' },
                        { istrue: true, type: '', prop: 'changeImgPhotoFpTaskOver', label: '完成任务', width: '50' },
                        { istrue: true, type: 'split'   },
                        { istrue: true, type: 'switch', prop: 'changeImgVideoIsCyFpBool', label: '视频分配', width: '80' },
                        { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'changeImgVideoFpRate', label: '视频比例%', width: '55', formatter: (row) => row.changeImgVideoFpRate + "%" },
                        { istrue: true, type: '', prop: 'changeImgVideoFpTask', label: '分配任务', width: '50' },
                        { istrue: true, type: '', prop: 'changeImgVideoFpTaskOver', label: '完成任务', width: '50' },
                        { istrue: true, type: 'split'   },
                        { istrue: true, type: 'switch', prop: 'changeImgDetailIsCyFpBool', label: '详情分配', width: '80' },
                        { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'changeImgDetailFpRate', label: '详情比例%', width: '55', formatter: (row) => row.changeImgDetailFpRate + "%" },
                        { istrue: true, type: '', prop: 'changeImgDetailFpTask', label: '分配任务', width: '50' },
                        { istrue: true, type: '', prop: 'changeImgDetailFpTaskOver', label: '完成任务', width: '50' },
                        { istrue: true, type: 'split'   },
                        { istrue: true, type: 'switch', prop: 'changeImgModelIsCyFpBool', label: '建模分配', width: '80' },
                        { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'changeImgModelFpRate', label: '建模比例%', width: '55', formatter: (row) => row.changeImgModelFpRate + "%" },
                        { istrue: true, type: '', prop: 'changeImgModelFpTask', label: '分配任务', width: '50' },
                        { istrue: true, type: '', prop: 'changeImgModelFpTaskOver', label: '完成任务', width: '50' },
                        { istrue: true, type: 'split'   },
                    ]);
                    break;
                case 6:
                    //店铺装修
                    tableCols=  tableCols.concat([
                        { istrue: true, type: 'switch', prop: 'shopDecorationDetailIsCyFpBool', label: '美工分配', width: '80' },
                        { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'shopDecorationDetailFpRate', label: '美工比例%', width: '85', formatter: (row) => row.shopDecorationDetailFpRate + "%" },
                        { istrue: true, type: '', prop: 'shopDecorationDetailFpTask', label: '分配任务', width: '80' },
                        { istrue: true, type: '', prop: 'shopDecorationDetailFpTaskOver', label: '完成任务', width: '80' },
                    ]);
                    break;
                case 7:
                    //包装设计
                    tableCols=  tableCols.concat([
                        { istrue: true, type: 'switch', prop: 'packIsCyFpBool', label: '设计分配', width: '80' },
                        { istrue: true, type: 'editselectmore', prop: 'brandListinfo', label: '品牌', width: '200', options: this.brandList },
                        { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'packFpRate', label: '照片比例%', width: '85' , formatter: (row) => row.packFpRate + "%"},
                        { istrue: true, type: '', prop: 'packFpTask', label: '分配任务', width: '80' },
                        { istrue: true, type: '', prop: 'packFpTaskOver', label: '完成任务', width: '80' },
                    ]);
                    break;
                case 8:
                //拼多多日常改图
                tableCols=  tableCols.concat([
                    { istrue: true, type: 'editselectmore', prop: 'operatorLiostInfo', label: '运营小组', width: '80'  ,options: this.operatorList},
                    { istrue: true, type: 'split'   },
                    { istrue: true, type: 'switch', prop: 'pddchangeImgPhotoIsCyFpBool', label: '照片分配', width: '80' },
                    { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'pddChangeImgPhotoFpRate', label: '照片比例%', width: '55' , formatter: (row) => row.pddChangeImgPhotoFpRate + "%"},

                    { istrue: true, type: '', prop: 'pddChangeImgPhotoFpTask', label: '分配任务', width: '50' },
                    { istrue: true, type: '', prop: 'pddChangeImgPhotoFpTaskOver', label: '完成任务', width: '50' },
                    { istrue: true, type: 'split'   },
                    { istrue: true, type: 'switch', prop: 'pddchangeImgDetailIsCyFpBool', label: '排版分配', width: '80' },
                    { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'pddChangeImgDetailFpRate', label: '排版比例%', width: '55', formatter: (row) => row.pddChangeImgDetailFpRate + "%" },
                    { istrue: true, type: '', prop: 'pddChangeImgDetailFpTask', label: '分配任务', width: '50' },
                    { istrue: true, type: '', prop: 'pddChangeImgDetailFpTaskOver', label: '完成任务', width: '50' },
                    { istrue: true, type: 'split'   },
                    { istrue: true, type: 'switch', prop: 'pddchangeImgModelIsCyFpBool', label: '建模分配', width: '80' },
                    { istrue: true, type: 'editNumber', min: '-1', max: '100', prop: 'pddChangeImgModelFpRate', label: '建模比例%', width: '55', formatter: (row) => row.pddChangeImgModelFpRate + "%" },
                    { istrue: true, type: '', prop: 'pddChangeImgModelFpTask', label: '分配任务', width: '50' },
                    { istrue: true, type: '', prop: 'pddChangeImgModelFpTaskOver', label: '完成任务', width: '50' },
                    { istrue: true, type: 'split'   }
                ]);
                break;
            }
            return tableCols;
        }
    },
    async mounted() {
        await this.onSearch();
        var res =await  getShootOperationsGroup({type:13});
        this.brandList =res?.map(item => { return { value: item.id, label: item.label }; });

        this.rowDrop();

        this.functiongit();
    },
    methods: {
          async remoteMethod(query) {
            if (query !== '') {
              let rlt = await QueryAllDDUserTop100({ keywords: query });
              if (rlt && rlt.success) {
                this.optionslist = rlt.data?.map(item => {
                  return { label: item.userName, value: item.userId, extData: item }
                });
              }
            }
            else {
              this.optionslist = [...this.orgOptions];
            }
            this.addForm.userId = '';
          },
          async addemployee(){
            if(this.addForm.userId == 0){
              this.$message({ message: '该员工未登录ERP系统，请登录ERP再试', type: 'warning' });
              return
            }
            var param  = [this.addForm.userId];
            const {success} = await addShootingFpSetTemp(param)
            if(success){
              this.$message({ message: '新增员工成功!', type: "success" });
              await this.onSearch();
              this.showAddnew = false
            }
          },
          addNew () {
            this.addForm = {
              userId: '',
            },
            this.optionslist = [];
            this.showAddnew = true;
          },
          async functiongit(){
            var resInfo = await getDirectorGroupList();
            this.operatorList = resInfo.data?.map(item => { return { value: Number(item.key), label: item.value }; });
          },
           //下拉排序
           rowDrop() {
            const tbody = document.querySelector('.draggable-table .vxe-table--body-wrapper tbody')
            const _this = this
            Sortable.create(tbody, {
                onEnd({ newIndex, oldIndex }) {
                    if (newIndex == oldIndex) return
                    _this.tasklist.splice(
                        newIndex,
                        0,
                        _this.tasklist.splice(oldIndex, 1)[0]
                    )
                    var newArray = _this.tasklist.slice(0)
                    _this.tasklist = []
                    _this.$nextTick(function () {
                        _this.tasklist = newArray
                    })
                }
            })
        },
        async saveOrders(){
            this.listLoading = true;
            var res = await sortShootingFpSet(this.tasklist);
            if (res?.success)
                this.$message({ message: this.$t('操作成功'), type: 'success' });
            this.listLoading = false;
        },
        //编辑保存
        async saveRowEvent(row) {
            const $table = this.$refs.xtable;
            if (! await $table.validate())
                return;
            if ($table.isUpdateByRow(row)) {
                if(row.brandListinfo ==null){
                    row.brandListinfo=[];
                }
                if(row.operatorLiostInfo ==null){
                    row.operatorLiostInfo=[];
                }
                if(row.changeImgOperatorLiostInfo ==null){
                    row.changeImgOperatorLiostInfo=[];
                }
                if(row.directImgOperatorLiostInfo ==null){
                    row.directImgOperatorLiostInfo=[];
                }
                var res = await editShootingFpSet(row);
                if (res?.success) {
                    this.$message({ message: '保存成功!', type: "success" });
                }
                $table.reloadRow(row);
                $table.clearActivedRowEvent(row);
            } else {
                this.$message({ message: '数据未发生变化!', type: "info" });
                $table.clearActivedRowEvent(row);
            }
        },
        //开启编辑
        editRowEvent(row) {
            this.$refs.xtable.editRowEvent(row);
        },
        //取消编辑
        cancelRowEvent(row) {
            this.$refs.xtable.cancelRowEvent(row);
        },
        //导出
        exportDataEvent() {
            this.$refs.xtable.exportData("员工信息");
        },

        //删除数据
        async onDel(row) {
            this.$confirm("将进行删除操作，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var param  = [];
                param.push(this.listtype);
                param = param.concat([row.shootAutoAssignId]);
                var res = await deleteShootingFpSet(param);
                if (!res?.success) { return; }
                this.$message({ message: this.$t('操作成功'), type: 'success' });
                await this.onSearch()
            });
        },
        //是否核算
        async CyCommission(row) {
            var res = await setPersonnelIsHsAsync({ positionId: row.positionId, isCyCommission: row.isCyCommissionbool ? 1 : 0 });
            if (!res?.success) { return; }
            this.$message({ message: this.$t('保存成功'), type: 'success' });
        },
        //打开新增窗口
        async onOpenAction() {
            this.editformTitle = "引入员工";
            this.editformdialog= true;
        },
        async  onSubmit(){
            var selids = this.$refs.shootYuanGongList.getReturnSelect();
            var param  = [];
            param.push(this.listtype);
            param = param.concat(selids);
            this.editLoading =true;

            var res = await addShootingFpSet(param);
            this.editLoading =false;
            if(res?.success){
                this.$message({ message: this.$t('操作成功'), type: 'success' });
                this.onSearch();
                this.editformdialog= false;
            }
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            this.getTaskList();
        },
        async getTaskList() {
            var pager = this.$refs.pager.getPager();
            let params = {
                ...this.filter,
                ...pager,
                ...this.pager,
                fptype:this.listtype
            };
            this.listLoading = true;
            let res = await getShootingFpInfo(params);
            this.listLoading = false;
            this.total = res.data.total;
            res.data.list.forEach((item, index) => {
                item.editstatus = 0;
                item.fpType = this.listtype;
            });
            this.tasklist = res.data.list;
            this.summaryarry= res.data.summary;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
    },
};
</script>
<style lang="scss" scoped>
::v-deep .bzjzcjrw .bzccjlx {
    box-sizing: border-box;
    padding: 0 30px;
    display: flex;
}

::v-deep .bzjzcjrw .bzccjlx {
    width: 100%;
    height: 35px;
    box-sizing: border-box;
    padding: 0 30px;
    display: flex;
}

::v-deep .bzjzcjrw .bzccjlx .lxwz {
    width: 20%;
    font-size: 14px;
    color: #666;
    vertical-align: top;
    line-height: 26px;
    /* background-color: rgb(204, 204, 255); */
}

::v-deep .bzjzcjrw .bzccjlx .lxwz2 {
    width: 80%;
}

::v-deep .bzjzcjrw .bt {
    height: 40px;
    /* background-color: aquamarine; */
    font-size: 18px;
    color: #666;
    margin-bottom: 20px;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
    box-sizing: border-box;
    padding: 0 35px;
}

::v-deep .mycontainer{
    overflow-y: hidden;
}
</style>

