<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent label-position="right"
        label-width="120px">
        <el-form-item label="运营组:">
          <el-select v-model="filter.groupId" placeholder="请选择" :clearable="true" :collapse-tags="true" filterable
            @change="onSearch">
            <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态:">
          <el-select v-model="filter.status" placeholder="待处理" @change="onSearch" :clearable="true" :collapse-tags="true"
            filterable>
            <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="商品编码:">
          <el-input v-model.trim="filter.goodsCode" placeholder="商品编码" style="width:193px;" @change="onSearch" />
        </el-form-item>
        <el-form-item label="产品ID:">
          <el-input v-model.trim="filter.proCode" placeholder="产品ID" style="width:193px;" @change="onSearch" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>

    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
      :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="createData.hasCreatePermission"
      :tableHandles='tableHandles' :isSelectColumn="!isHistory" @select="selsChange" :loading="listLoading">
    </ces-table>

    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <el-dialog :visible.sync="createData.visible" v-dialogDrag title="生成下架通知" width="42%">
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent label-position="right"
        label-width="90px">
        <el-form-item label="商品编码:">
          <el-input v-model.trim="createData.filter.goodsCodes" placeholder="多个商品编码使用,分隔" type="textarea" autosize
            style="width:600px;" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="createData.visible = false">取消</el-button>
          <el-button type="primary" @click="createSoldOutGoods">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog :visible.sync="detail.visible" v-dialogDrag title="编辑明细" width="55%">
      <div style="margin-bottom:20px;">
        <el-row>
          <el-col :span="20">
            <el-descriptions :column="3" size="mini" border>
              <el-descriptions-item label="商品编码">{{ detail.selRow.goodsCode }}</el-descriptions-item>
              <el-descriptions-item label="商品名称">{{ detail.selRow.goodsName }}</el-descriptions-item>
              <el-descriptions-item label="运营组">{{ selGroupName }}</el-descriptions-item>
              <el-descriptions-item label="状态">{{ selStatusName }}</el-descriptions-item>
              <el-descriptions-item label="备注">{{ detail.selRow.remark }}</el-descriptions-item>
            </el-descriptions>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-image style="width: 60px; height: 60px" :src="detail.selRow.goodsImage"
              :preview-src-list="detail.srcList">
              <template #error>
                <div class="image-slot">
                  <el-icon>
                    <picture />
                  </el-icon>
                </div>
              </template>
            </el-image>
          </el-col>
        </el-row>
      </div>
      <el-form class="ad-form-query" :inline="true" :model="detail.filter" @submit.native.prevent>
        <el-form-item label="平台:">
          <el-select v-model="detail.filter.platform" style="width:110px;" placeholder="请选择" :clearable="true"
            :collapse-tags="true" filterable @change="onchangeplatform">
            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="店铺:">
          <el-select style="width:200px;" v-model="detail.filter.shopCode" placeholder="请选择" @change="onSearchDetail"
            :clearable="true" :collapse-tags="true" filterable>
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态:">
          <el-select v-model="detail.filter.status" style="width:90px;" placeholder="请选择" @change="onSearchDetail"
            :clearable="true" :collapse-tags="true" filterable>
            <el-option v-for="item in statusList0" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="产品ID:">
          <el-input v-model.trim="detail.filter.proCode" placeholder="产品ID" style="width:150px;"
            @change="onSearchDetail" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearchDetail">查询</el-button>
        </el-form-item>
        <!-- <el-form-item>
                <el-button type="primary" @click="showChangeStatus">修改</el-button>
            </el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="showNext">下一个</el-button>
        </el-form-item>
      </el-form>

      <ces-table ref="tableDetail" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchangeDetail'
        :tableData='detail.list' :tableCols='detail.tableCols' :isSelection="true" :tableHandles='detail.tableHandles'
        :isSelectColumn="false" @select="selsChangeDetail" style="height:360px;" :loading="detail.listLoading">
      </ces-table>

      <my-pagination ref="pagerDetail" :total="detail.total" :checked-count="detail.sels.length"
        @get-page="getlistDetail" />
    </el-dialog>

    <el-dialog :visible.sync="detail.formData.visible" v-dialogDrag title="修改状态" width="650px">
      <el-form class="ad-form-query" :inline="true" :model="detail.formData.filter" @submit.native.prevent
        label-position="right" label-width="50px">
        <el-form-item label="状态:">
          <el-select v-model="detail.formData.status" placeholder="请选择" :clearable="false" :collapse-tags="true">
            <el-option v-for="item in statusList0" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注:">
          <el-input v-model.trim="detail.formData.remark" placeholder="备注" maxlength="50" style="width:520px;" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detail.formData.visible = false">取消</el-button>
          <el-button type="primary" @click="onChangeStatus">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog :visible.sync="visibleRedeploy" v-dialogDrag title="转派信息" width="650px">
      <el-form class="ad-form-query" :inline="true" :model="detail.formData.filter" @submit.native.prevent
        label-position="right" label-width="100px">
        <el-form-item label="运营组:">
          <el-select v-model="groupId" placeholder="请选择" :clearable="true" :collapse-tags="true" filterable>
            <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="visibleRedeploy = false">取消</el-button>
          <el-button type="primary" @click="onChangeRedeploy">确定</el-button>
        </div>
      </template>
    </el-dialog>

  </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatPlatform, formatLinkProCode } from "@/utils/tools";
import { rulePlatform } from "@/utils/formruletools";
import { getGroupKeyValue } from '@/api/operatemanage/base/product';
import checkPermission from '@/utils/permission'
import {
  createSoldOutGoods,
  pageSoldOutGoods,
  getLoginUserDirectorGroup,
  deleteSoldOutGoods,
  pageSoldOutGoodsDetail,
  changeSoldOutGoodsStatus,
  exportSoldOutGoods,
  exportSoldOutGoodsDetail,
  ChangeSoldOutGoodsRedeploy
} from "@/api/operatemanage/goodssoldout";

var statusList0 = [
  { label: '待处理', value: 0 },
  { label: '卖完下架', value: 1 },
  { label: '不下架', value: 2 },
];
var statusList = statusList0.concat({ label: '部分已选择', value: 3 });

const tableCols = [
  { istrue: true, prop: 'goodsCode', label: '商品编码', width: '160', sortable: 'custom', type: 'click', handle: (that, row) => that.showDetail(row) },
  { istrue: true, prop: 'goodsImage', label: '图片', width: '60', type: 'image' },
  { istrue: true, prop: 'groupId', label: '运营组', width: '120', sortable: 'custom', formatter: (row) => row.groupName },
  { istrue: true, prop: 'status', label: '状态', width: '120', sortable: 'custom', formatter: (row) => statusList.find(a => a.value == row.status)?.label },
  { istrue: true, prop: 'goodsName', label: '商品名称', width: 'auto', },
  { istrue: true, prop: 'remark', label: '备注', width: 'auto', },
  { istrue: true, prop: 'modifiedUserId', label: '修改人', width: '100', sortable: 'custom', formatter: (row) => row.modifiedUserName },
  { istrue: true, prop: 'modifiedTime', label: '修改时间', width: '160', sortable: 'custom', formatter: (row) => row.modifiedTime && formatTime(row.modifiedTime, 'YYYY-MM-DD HH:mm:ss') },
  { istrue: true, prop: 'createdUserId', label: '创建人', width: '100', sortable: 'custom', formatter: (row) => row.createdUserName },
  { istrue: true, prop: 'createdTime', label: '创建时间', width: '160', sortable: 'custom', formatter: (row) => row.createdTime && formatTime(row.createdTime, 'YYYY-MM-DD HH:mm:ss') },
];

const tableColsDetail = [
  { istrue: true, prop: 'platform', label: '平台', width: '100', formatter: (row) => formatPlatform(row.platform) },
  { istrue: true, prop: 'shopCode', label: '店铺', width: 'auto', sortable: 'custom', formatter: (row) => row.shopName },
  { istrue: true, prop: 'proCode', label: '产品ID', width: '160', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
  { istrue: true, prop: 'status', label: '状态', width: '120', sortable: 'custom', formatter: (row) => statusList.find(a => a.value == row.status)?.label },
  { istrue: true, prop: 'remark', label: '备注', width: 'auto', },
  { istrue: true, prop: 'modifiedUserId', label: '修改人', width: '100', sortable: 'custom', formatter: (row) => row.modifiedUserName },
  { istrue: true, prop: 'modifiedTime', label: '修改时间', width: '160', sortable: 'custom', formatter: (row) => row.modifiedTime && formatTime(row.modifiedTime, 'YYYY-MM-DD HH:mm:ss') },
];

var hasCreatePermission = checkPermission(['api:operatemanage:soldoutgoods:CreateSoldOutGoodsAsync']);

const tableHandles1 = [
  { label: "导出", handle: (that) => that.onExport() },
  { label: "导出明细", handle: (that) => that.onExportDetail() },
  { label: "修改", handle: (that) => that.showChangeStatus() },
  { label: "转派", handle: (that) => that.showChangeRedeploy() }
];
if (hasCreatePermission) {
  tableHandles1.push({ label: "生成", handle: (that) => that.showCreateData() });
  tableHandles1.push({ label: "删除", handle: (that) => that.deleteSoldOutGoods() });
}

export default {
  name: 'Roles',
  components: { cesTable, MyContainer, MyConfirmButton },
  props: {
    isHistory: false,
  },
  data() {
    return {
      that: this,
      filter: {
        platform: null,
        shopId: "",
        groupId: null,
        status: 0,
        goodsCode: null,
        proCode: null
      },
      groupId: null,
      list: [],
      summaryarry: {},
      pager: { OrderBy: "goodsCode", IsAsc: false },
      tableCols: tableCols,
      tableHandles: tableHandles1,
      directorGroupList: [],
      platformList: [],
      shopList: [],
      groupList: [],
      total: 0,
      sels: [],
      listLoading: false,
      pageLoading: false,
      visibleRedeploy: false,
      statusList0: statusList0,
      statusList: statusList,
      createData: {
        visible: false,
        filter: {
          goodsCodes: null,
        },
        hasCreatePermission: hasCreatePermission,
      },
      detail: {
        list: [],
        pager: { OrderBy: "status", IsAsc: true },
        tableCols: tableColsDetail,
        tableHandles: [],
        total: 0,
        sels: [],
        listLoading: false,
        visible: false,
        formData: {
          visible: false,
          ids: null,
          status: null,
          remark: null,
        },
        filter: {
          parentId: null,
          proCode: null,
          status: null,
          platform: null,
          shopId: "",
        },
        selRow: {},
        srcList: [],
      }
    }
  },
  async mounted() {
    await this.setLoginGroup();
    await this.setGroupSelect();
    await this.setPlatform();
    await this.getlist();
  },
  methods: {
    showCreateData() {
      this.createData.visible = true;
    },
    async createSoldOutGoods() {
      if (!this.createData.filter.goodsCodes) {
        this.$message({ type: 'warning', message: '请填写商品编码' });
        return;
      }
      var params = { ...this.createData.filter };
      var res = await createSoldOutGoods(params);
      if (res?.code == 1) {
        this.$message({ type: 'success', message: '生成成功' });
        await this.onSearch();
        this.createData.visible = false;
      }
    },
    async deleteSoldOutGoods() {
      if (!this.sels || !this.sels.length) {
        this.$message({ type: 'warning', message: '请选择删除的行' });
        return;
      }
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定", cancelButtonText: "取消", type: "warning",
      }).then(async () => {
        var ids = this.sels.map(a => a.id).join(",");
        var params = { ids: ids };
        var res = await deleteSoldOutGoods(params);
        if (res?.code == 1) {
          this.$message({ type: 'success', message: '删除成功' });
          await this.onSearch();
        }
      })
        .catch(() => { });
    },
    //设置平台下拉
    async setPlatform() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;
      if (this.platformList && this.platformList.length)
        this.platformList.push({ label: '未知', value: 0 });
    },
    //设置店铺下拉
    async onchangeplatform(val) {
      const res = await getshopList({ platform: val, CurrentPage: 1, PageSize: 1000 });
      this.shopList = res.data.list || [];
      this.filter.shopCode = "";
      await this.onSearchDetail();
    },
    //设置运营组下拉
    async setGroupSelect() {
      const res = await getGroupKeyValue({});
      this.groupList = res.data;
    },
    //设置登录用户运营组
    async setLoginGroup() {
      var userName = this.$store.getters.userName;
      const res = await getLoginUserDirectorGroup({ userName });
      var resgroupId = res?.data?.groupId || null;
      resgroupId = resgroupId?.toString()
      this.filter.groupId = resgroupId;
    },
    async onExport() {
      await this.onExportPublic(false, "下架通知");
    },
    async onExportDetail() {
      await this.onExportPublic(true, "下架通知明细");
    },
    //导出
    async onExportPublic(isDetail, title) {
      var params = this.getCondition();
      if (params === false) {
        return;
      }

      var res;
      if (isDetail) {
        res = await exportSoldOutGoodsDetail(params);
      }
      else {
        res = await exportSoldOutGoods(params);
      }

      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', title + '_' + new Date().toLocaleString() + '.xlsx')
      aLink.click();
    },
    //获取查询条件
    getCondition() {
      if (this.filter.timerange && this.filter.timerange.length > 1) {
        this.filter.modifiedStart = this.filter.timerange[0];
        this.filter.modifiedEnd = this.filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = {
        ...pager,
        ...page,
        ... this.filter
      }

      return params;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    //分页查询
    async getlist() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }

      this.listLoading = true
      var res = await pageSoldOutGoods(params);;
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry = res.data.summary;
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
    //排序查询
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        var orderBy = column.prop;
        this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    },
    selsChange: function (sels) {
      this.sels = sels
    },
    ///==明细 Start==========================================
    showDetail(row) {
      // if(row.remark=="商品编码无链接"){
      //   this.$message({type:"warning",message:"商品编码无链接"});
      //   return;
      // }
      this.detail.visible = true;
      this.detail.selRow = row;
      this.detail.srcList = [row.goodsImage];
      this.clearDetailFilter();
      this.detail.filter.parentId = row.id;
      setTimeout(async () => {
        await this.onSearchDetail();
      }, 500);
    },
    clearDetailFilter() {
      this.detail.filter = {};
    },
    selsChangeDetail: function (sels) {
      this.detail.sels = sels;
    },
    async sortchangeDetail(column) {
      if (!column.order)
        this.detail.pager = {};
      else {
        var orderBy = column.prop;
        this.detail.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearchDetail();
    },
    //获取查询条件
    getConditionDetail() {
      var pager = this.$refs.pagerDetail.getPager();
      var page = this.detail.pager;
      const params = {
        ...pager,
        ...page,
        ... this.detail.filter
      }

      return params;
    },
    //查询第一页
    async onSearchDetail() {
      this.$refs.pagerDetail.setPage(1)
      await this.getlistDetail()
    },
    //分页查询
    async getlistDetail() {
      var params = this.getConditionDetail();
      if (params === false) {
        return;
      }

      this.detail.listLoading = true
      var res = await pageSoldOutGoodsDetail(params);;
      this.detail.listLoading = false
      if (!res?.success) {
        return
      }
      this.detail.total = res.data.total;
      const data = res.data.list;
      data.forEach(d => {
        d._loading = false
      })
      this.detail.list = data
    },
    // showChangeStatus(){
    //   if(!this.detail.sels||!this.detail.sels.length){
    //     this.$message({type:"warning",message:"请先选择行"});
    //     return;
    //   }
    //   this.detail.formData.visible=true;
    //   this.detail.formData.ids=this.detail.sels.map(a=>a.id).join(",");
    //   var status = Array.from(new Set(this.detail.sels.map(a=>a.status)));
    //   var remark = Array.from(new Set(this.detail.sels.map(a=>a.remark)));
    //    this.detail.formData.remark=remark.length>1?null:remark[0];
    //   this.detail.formData.status=status.length>1?null:status[0];
    //   console.log(status);console.log(remark);console.log(this.detail.sels)
    // },
    showChangeStatus() {
      if (!this.sels || !this.sels.length) {
        this.$message({ type: 'warning', message: '请选择删除的行' });
        return;
      }
      this.detail.formData.remark = null;
      this.detail.formData.status = null;
      this.detail.formData.visible = true;
    },
    // async onChangeStatus(){
    //     if(!this.detail.formData.ids||!this.detail.formData.ids.length){
    //       this.$message({type:'warning',message:'请选择行'});
    //       return;
    //     }
    //     if(this.detail.formData.status===null||typeof(this.detail.formData.status)=="undefind"){
    //       this.$message({type:'warning',message:'请选择状态'});
    //       return;
    //     }
    //     if(this.detail.formData.status==2 && !this.detail.formData.remark){
    //       this.$message({type:'warning',message:'请在备注中填写不下架的原因'});
    //       return;
    //     }
    //     this.$confirm("确定修改吗？","提示",{
    //       confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",
    //     }).then(async ()=>{
    //       var params={
    //         ...this.detail.formData,
    //         parentId:this.detail.filter.parentId
    //       };
    //       var res = await changeSoldOutGoodsStatus(params);
    //       if(res?.code==1){
    //         this.detail.selRow.status=res.data;
    //         this.$message({type:'success',message:'修改成功'});
    //         this.detail.formData.visible=false;
    //         await this.onSearchDetail();
    //         await this.onSearch();

    //       }
    //     })
    //     .catch(()=>{});
    // },
    async onChangeStatus() {
      if (!this.sels || !this.sels.length) {
        this.$message({ type: 'warning', message: '请选择数据' });
        return;
      }
      if (this.detail.formData.status === null || typeof (this.detail.formData.status) == "undefind") {
        this.$message({ type: 'warning', message: '请选择状态' });
        return;
      }
      if (this.detail.formData.status == 2 && !this.detail.formData.remark) {
        this.$message({ type: 'warning', message: '请在备注中填写不下架的原因' });
        return;
      }
      this.$confirm("确定修改吗？", "提示", {
        confirmButtonText: "确定", cancelButtonText: "取消", type: "warning",
      }).then(async () => {
        var params = {
          ...this.detail.formData,
          ids: this.sels.map(a => a.id).join(",")
          //parentId:this.detail.filter.parentId
        };
        var res = await changeSoldOutGoodsStatus(params);
        if (res?.success) {
          this.detail.selRow.status = res.data;
          this.$message({ type: 'success', message: '修改成功' });
          this.detail.formData.visible = false;
          await this.onSearchDetail();
          await this.getlist();

        }
      })
        .catch(() => { });
    },
    async showChangeRedeploy() {
      if (!this.sels || !this.sels.length) {
        this.$message({ type: 'warning', message: '请选择数据' });
        return;
      }
      this.visibleRedeploy = true;
    },
    async onChangeRedeploy() {
      var _this = this;
      this.$confirm("确定转派吗，转派之后编码数据将会到对应负责任名下？", "提示", {
        confirmButtonText: "确定", cancelButtonText: "取消", type: "warning",
      }).then(async () => {
        var params = {
            groupId: this.groupId,
            ids: this.sels.map(a => a.id).join(",")
        };
        var res = await ChangeSoldOutGoodsRedeploy(params);
        if (res?.code == 1) {
          this.$message({ type: 'success', message: '转派成功' });
          this.visibleRedeploy = false;
          _this.onSearch();
        }
      })
        .catch(() => { });
    },
    ///==明细 End  ==========================================
    showNext() {
      if (this.list && this.list.length > 0) {
        var nextRow = this.list[0];
        var findCur = false;
        this.list.forEach(item => {
          if (findCur) {
            findCur = false;
            nextRow = item;
          }
          if (item.id == this.detail.selRow.id) {
            findCur = true;
          }
        });
        this.detail.selRow = nextRow;
        this.showDetail(nextRow);
      }
    },
  },
  computed: {
    selGroupName() {
      var name = this.groupList?.find(a => a.key == this.detail.selRow.groupId)?.value;
      return name || "未知";
    },
    selStatusName() {
      var name = this.statusList?.find(a => a.value == this.detail.selRow.status)?.label;
      return name || "请选择";
    }
  },
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary {
  margin-right: 7px;
}
</style>
