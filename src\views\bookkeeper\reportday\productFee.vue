<template>
  <MyContainer>
    <el-tabs v-model="activeName" style="height: 94%;">
      <el-tab-pane label="产品运费（旧）" name="first1" style="height: 100%;">
        <ProductFreight />
      </el-tab-pane>
      <el-tab-pane label="产品运费（新）" name="first2" style="height: 100%;" lazy>
        <productFreightNew />
      </el-tab-pane>
    </el-tabs>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import ProductFreight from './ProductFreight.vue'
import productFreightNew from './productFreightNew.vue'

export default {
  components: {
    MyContainer, ProductFreight, productFreightNew
  },
  data() {
    return {
      activeName: 'first1'
    };
  },
  methods: {

  }
};
</script>

<style lang="scss" scoped></style>
