<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="ListInfo.shopName" placeholder="店铺" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.sceneName" placeholder="场景名字" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.productID" placeholder="计划id" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.productName" placeholder="计划名称" maxlength="50" clearable class="publicCss" />
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
      </div>
    </template>
    <vxetablebase :id="'liveSiteWide202504181732'" :tablekey="'liveSiteWide202504181732'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'" @select='selectchange' border>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { getTuijianAllSiteList, allSiteDeleteBatchAsync, importTuijianallsiteAsync } from '@/api/financial/yyfyday'
const tableCols = [
  { istrue: true, prop: 'useDate', label: '日期', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'shopName', label: '店铺', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'sceneId', label: '场景ID', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'sceneName', label: '场景名字', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'originalSceneId', label: '原二级场景ID', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'originalSceneName', label: '原二级场景名字', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'productID', label: '计划ID', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'productName', label: '计划名字', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'useMoney', label: '花费', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'useMoneyDecimal', label: '成交花费', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'transactionAmount', label: '全站成交金额', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'roi', label: '成交投产比', width: '100', sortable: 'custom', formatter: (row) => row.roi ? row.roi + '%' : '0%' },
  { istrue: true, prop: 'totalSiteCostRatio', label: '全站费比', width: '100', sortable: 'custom', formatter: (row) => row.totalSiteCostRatio ? row.totalSiteCostRatio + '%' : '0%' },
  { istrue: true, prop: 'viewCount', label: '全站观看次数', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'clickCount', label: '全站宝贝点击量', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'preSaleAmount', label: '全站预售成交金额', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'preSaleCount', label: '全站预售成交笔数', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'transactionCount', label: '全站成交笔数', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'createdTime', label: '导入时间', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'batchNumber', label: '导入批次', width: '140', sortable: 'custom' },
  { istrue: true, type: "button", width: "80", align: 'center', label: '操作', btnList: [{ label: "删除批次", handle: (that, row) => that.deleteBatch(row) }], fixed: 'right' }
]
export default {
  name: "liveSiteWide",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      dialogVisible: false,
      fileList: [],
      uploadLoading: false,
      fileparm: {},
      selectData: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'sceneName',
        isAsc: false,
        startAccountDate: null,//开始时间
        endAccountDate: null,//结束时间
        startUseDate: null,//开始时间
        endUseDate: null,//结束时间
        sceneName: null,
        shopName: null,
        productName: null,
        productID: null,
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importTuijianallsiteAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      // await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async deleteBatch(row) {
      this.$confirm("此操作将删除此批次数据,是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          await allSiteDeleteBatchAsync({ batchNumber: row.batchNumber })
          this.$message({ message: '已删除', type: "success" });
          this.getList()
        });
    },
    selectchange(e) {
      this.selectData = e
    },
    async changeTime(e) {
      this.ListInfo.startAccountDate = e ? e[0] : null
      this.ListInfo.endAccountDate = e ? e[1] : null
      this.ListInfo.startUseDate = e ? e[0] : null
      this.ListInfo.endUseDate = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        this.ListInfo.startUseDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endUseDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startUseDate, this.ListInfo.endUseDate]
      }
      this.loading = true
      const { data, success } = await getTuijianAllSiteList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.useDate = dayjs(item.useDate).format('YYYY-MM-DD')
        })
        this.total = data.total
        let a = data.summary
        a.useMoney_sum = String(a.useMoney_sum)
        this.summaryarry = a
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
