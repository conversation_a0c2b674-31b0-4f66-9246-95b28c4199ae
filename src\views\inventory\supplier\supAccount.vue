<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-button type="primary" @click="batchDel">批量删除</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            :isRemoteSort="false" @select="selectCheckBox" @sortchange='sortchange' :tableData='tableData'
            :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0"
            :loading="loading" :height="'100%'">
        </vxetablebase>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { pageSupplierAll, GetSupplierAccountBankList, AddSupplierAccountBank, DeleteSupplierAccountBank } from '@/api/inventory/supplier'

import dateRange from "@/components/date-range/index.vue";
const tableCols = [
    { label: '', type: 'checkbox' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '创建时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'publicPayType', label: '对私/对公', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'bankType', label: '开户行', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'accountName', label: '账户名', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'account', label: '账号', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    props: {
        supplier_id: {
            type: Number,
            default: null
        }
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 1000000,
                id: this.supplier_id
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            selectList: []
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        batchDel() {
            if (this.selectList.length == 0) return this.$message.warning('请选择要删除的数据')
            this.$confirm('此操作将删除选中的数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await DeleteSupplierAccountBank({ ids: this.selectList.map(item => item.id) })
                if (success) {
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                    this.getList()
                    this.selectList = []
                }

            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        selectCheckBox(val) {
            this.selectList = val
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetSupplierAccountBankList(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
