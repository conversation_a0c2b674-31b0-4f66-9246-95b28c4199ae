<template>
  <div  style="width: 100%; height: 100%; overflow: hidden;">
    <div v-show="routeshow" style="width: 100%; height: 100%; overflow: hidden;" >
      <div class="condition">
      <div style="border-bottom: 1px solid #eee; width: 100%; overflow: hidden; max-height: 110px;overflow: hidden;" class="flexwrap top">
        <div style="font-size: 12px; padding: 15px 0 5px 10px;" class="flexcloumn">展架分类：</div>
        <el-button style="margin-left: 25px; margin-top: 10px;" size="mini"  @click="getCategoryGoods(null)">全部</el-button>
        <!-- <div class="top"> -->
          <div v-for="(item,i) in CategoryList" :key="i" style="margin-left: 25px; margin-top: 10px"  @click="getCategoryGoods(item,i)" >
            <el-button :type="i == tagindex?'text':'text'" plain :class="i == tagindex?'bluetext':'blacktext'" size="mini">{{item}}</el-button>
          </div>
        <!-- </div> -->
          <div>
        </div>
      </div>

      <div class="flexrow" style="width: 80%; min-height: 30px;">
          <el-input placeholder="请输入资料编码" v-model.trim="filter.ProCode" class="input-with-select" :maxlength="100"  clearable>
            <!-- <el-select v-model="select" slot="prepend" placeholder="请选择" style="width: 100px;">
              <el-option label="模糊查询" value="1"></el-option>
              <el-option label="精准查询" value="2"></el-option>
            </el-select> -->
            <!-- <el-button slot="append" icon="el-icon-search"></el-button> -->
          </el-input>

          <!-- <el-input placeholder="请输入商品编码" v-model.trim="filter.spuId" class="input-with-select" clearable> -->
            <!-- <el-select v-model="select" slot="prepend" placeholder="请选择" style="width: 100px;">
              <el-option label="模糊查询" value="1"></el-option>
              <el-option label="精准查询" value="2"></el-option>
            </el-select> -->
            <!-- <el-button slot="append" icon="el-icon-search"></el-button> -->
          <!-- </el-input> -->

          <el-input v-model.trim="filter.ProName" placeholder="商品名称" class="input-with-select" :maxlength="200"  clearable></el-input>
          <div class="input-with-select">
            <el-select v-model="filter.channel" style="width:100px;" placeholder="请选择渠道" clearable>
              <el-option value="Jushuitan" label="聚水潭"></el-option>
              <el-option value="DouYin" label="抖音"></el-option>
            </el-select>
          </div>
          <div class="input-with-select">
            <el-input v-model.trim="filter.MinBaseSalePrice" placeholder="最小供货价" type="number" class="nopinput" style="width:100px;" clearable @input="minPriceChange" @blur="minPriceBlur"></el-input>
          </div>
          <div class="input-with-select">
            <el-input v-model.trim="filter.MaxBaseSalePrice" placeholder="最大供货价" type="number" class="nopinput" style="width:100px;" clearable @input="maxPriceChange" @blur="maxPriceBlur"></el-input>
          </div>
          <div class="input-with-select">
            <el-button type="primary" size="mini" style="width: auto; margin: auto;" @click="getgoods">查询</el-button>
          </div>
          <div class="input-with-select" v-if="checkPermission('api:distribution:Distribution:UpdateSkuBaseSale')">
            <el-button type="primary" size="mini" style="margin-left:10px;" @click="onShowEditPrice()">批量编辑供货价</el-button>
          </div>
          <div class="input-with-select" v-if="checkPermission('api:distribution:Distribution:UpdateSkuBaseSale')">
            <el-button type="text" @click="changeCheck('all')">全选</el-button>
          </div>
          <div class="input-with-select" v-if="checkPermission('api:distribution:Distribution:UpdateSkuBaseSale')">
            <el-button type="text" @click="changeCheck('none')">全不选</el-button>
          </div>
          <div class="input-with-select" v-if="checkPermission('api:distribution:Distribution:UpdateSkuBaseSale')">
            <el-button type="text" @click="changeCheck('other')">反选</el-button>
          </div>
        </div>
      </div>

        <div class="input-with-select flexwrap" style="width: 98%; height: 91%; overflow-y: auto;" v-if="DistributionList && DistributionList.length > 0">
          <div style="width: 220px; height: 420px; margin: 0 0 10px 10px;" v-for="(i,ii) in DistributionList" :key="ii" @click="jumpstaion(i)">
            <div style="border: 1px solid #eee;position:relative;padding-bottom:5px;">
              <div class="list-check" v-if="checkPermission('api:distribution:Distribution:UpdateSkuBaseSale')">
                <el-checkbox v-model="i.isCheck"></el-checkbox>
              </div>
              <div style="width: 218px; height: 260px; overflow-y: hidden; display: flex; justify-content: center;">
                <el-image class="goodsimg" :src="getFirstImage(i.itemMainImagelist)" ></el-image>
              </div>
              <div style="z-index: 99; background-color: white; position: relative; padding: 5px;">
                <div class="flexrow">
                    <div style="flex: 2; font-family: -apple-system,BlinkMacSystemFont; 
                      color: rgba(0,0,0,.85); font-size: 14px; font-weight: 600;" class="orderTitle">{{i.itemName}}</div>
                      <!-- <div style="flex: 3;">
                      <div style="border: 1px solid #fa6400; color: #fa6400; border-radius: 2px; padding: 2px 5px; margin: 0 5px 10px 5px; font-size: 12px;">所有人可见</div>
                      </div> -->
                    </div>

                    <div style="" class="bmzl">资料编码:{{i.proCode}}</div>
                    <div class="flexrow commonsize">
                    <div style="margin-right: auto; color: #000;">供货价：
                    <span style="font-weight: 600;font-size: 18px; color: rgb(245, 33, 45);">￥{{i.minBaseSalePrice}}起</span>
                  </div>
              </div>
            <!-- <div style="margin-left: auto;">未关联素材包</div> -->
            </div>

            
            <div class="flexrow commonsize" style="padding: 0 5px;">
            <div style="margin-right: auto; color: #000;">近七天销量 <span style="color: rgb(245, 33, 45); font-weight: 600;">{{i.saleSeven}}</span></div>
            <div style="margin-left: auto; color: #000;">月销量<span style="color: rgb(245, 33, 45); font-weight: 600;">{{i.saleThirty}}</span></div>
            </div>

            <div class="flexrow commonsize" style="padding: 0 5px;">
              <el-tooltip effect="dark" :content="getPlatformTypes(i.platformTypesList)" placement="top">
                <div style="margin-right: auto;overflow:hidden;text-overflow: ellipsis;padding-right:10px;">
                  资料已完善:
                  <span v-for=" (iii,index) in i.platformTypesList" :key="index">{{getPlatformType(iii)}}</span>
                </div>
              </el-tooltip>
            <div style="margin-left: auto;">库存<span>{{i.stockQuantity}}</span></div>
            </div>
          </div>
          </div>
        </div>
        <div v-else class="input-with-select flexwrap" style="width: 98%; height: 91%; overflow-y: auto;display: flex;justify-content: center;align-items: center;font-size: 14px;color: ccc;">
          暂无数据
        </div>
    <!--分页-->
      <my-pagination
        ref="pager"
        :total="total"
      
        @get-page="getgoods"
      />
      </div>

    <div v-show="!routeshow" style="width: 100%; height: 100%; position: relative;">
        <div style="width: 100%; border: 1px solid #eee; padding: 15px 20px;">
          <i class="el-icon-back" @click="jumpstaion">商品详情</i> 
        </div>
        <div style="height: 95%;">
          <distributionstation :dandata="dandata" v-if="!routeshow"></distributionstation>
        </div>
    </div>
  </div>
</template>
<script>
import {DistributionDetailsList, getDistributionCategoryList} from '@/api/distribution/distribution.js';
import distributionstation from "@/views/distribution/distributionstation.vue"
export default {
 components: {distributionstation},
 data() {
  return {
    dandata: null,
    routeshow: true,
    tagindex: -1,
    CategoryList:[],
    DistributionList:[],
    filter:{
      CategoryName:null,
      ProName:null,
      ProCode:null,
      spuId:null,
      channel:null
    },
    order:{OrderBy:"dataImportTime",IsAsc:false},
    total:null,
    platformTypes:[
      {value:'Tmall',label:'天猫'},
      {value:'Pinduoduo',label:'拼多多'},
      {value:'douyin',label:'抖音'},
      {value:'taobao',label:'淘宝'},
      {value:'Xiaohs',label:'小红书'}
    ]
  };
 },
 created(){
  console.log("打印路由数据",this.$router)
 },

 async mounted() {
   await this.gettagname();
   await this.getgoods();
   
 },

 methods: {
  jumpstaion(e){
    console.log("打印传值数据",e)
    this.dandata = e;
    this.routeshow = !this.routeshow;
  },
  tabclick(e){
    this.tagindex = e;
  },
  async getCategoryGoods(item,i){
    this.filter.CategoryName=item;
    this.tagindex = i;
    await this.getgoods();
  },
  async getgoods(){//商品信息
    var pager = this.$refs.pager.getPager();
    var params={...pager,...this.filter,...this.order}
   let res = await DistributionDetailsList(params);
   if(res.data.list){
    res.data.list.forEach(item => {
      item.isCheck = false;
      item.minBaseSalePrice=item.minBaseSalePrice||0;
    });
   }
   this.DistributionList=res.data.list;
   this.total=res.data.total
   console.log("打印商品信息数据",res)
  },
  async gettagname(){//类目信息
   let res = await getDistributionCategoryList();
   this.CategoryList=res.data
  },
  onShowEditPrice(){
    let self=this;    
    
    var data = this.DistributionList.filter(i=>i.isCheck==true);
    if(!data || data.length<=0){
      this.$message({ message: "请先选择商品", type: "error" });
      return;
    }

    var list = [];
    data.forEach(item=>{
      list = [...list,...item.skulist];
    });
    if(!list || list.length<=0){
      this.$message({ message: "所选商品未找到SKU", type: "error" });
      return;
    }
    this.$showDialogform({
        path: `@/views/distribution/goodseditpriceindex.vue`,
        title: '修改基本售价(供货价)',
        autoTitle:false,
        args: {list:list},
        height: 600,
        width: '1000px',
        callOk: self.getgoods
    })
  },
  getPlatformType(type){
    var tmp = this.platformTypes.filter(p=>p.value==type);
    if(!tmp || tmp.length<=0) return type;

    return tmp[0].label;
  },
  getPlatformTypes(types){
    var re = []
    types.forEach(type=>{
      re.push(this.getPlatformType(type));
    });
    return re.join(',');
  },
  changeCheck(type){
    this.DistributionList.forEach(item=>{
      item.isCheck = type=='all'?true:type=='none'?false:!item.isCheck;
    });
  },
  // 获取图片列表里不为视频的第一张图片
  getFirstImage(imgs){
    var imgUrl = '';
    if(imgs!=null && imgs!=undefined && imgs.length>0){
      imgUrl = imgs[1];
      imgs.forEach(item=>{
        if(!this.isExt(['mp4','mov'],item)){
          imgUrl = item;
          return;
        }
      });
    }
    return imgUrl;
  },
  isExt(exts, fileName){
    // 判断是否是指定后缀
    const ext = this.getExt(fileName);
    var flag = exts.indexOf(ext.toLowerCase())>-1;
    return flag;
  },
  getExt(fileName){
    if(!fileName) return 'nofilename';
    // 获取文件扩展名
    return /[^.]+$/.exec(fileName)[0];
  },
  minPriceBlur(){
    if(this.filter.MaxBaseSalePrice!='' && this.filter.MaxBaseSalePrice<this.filter.MinBaseSalePrice){
      this.filter.MinBaseSalePrice = this.filter.MaxBaseSalePrice
    }
  },
  minPriceChange(val){
    this.filter.MinBaseSalePrice = this.getNewPrice(val);
  },
  maxPriceBlur(){
    if(this.filter.MinBaseSalePrice!='' && this.filter.MaxBaseSalePrice<this.filter.MinBaseSalePrice){
      this.filter.MaxBaseSalePrice = this.filter.MinBaseSalePrice
    }
  },
  maxPriceChange(val){
    this.filter.MaxBaseSalePrice = this.getNewPrice(val);
  },
  getNewPrice(val){
    if(!val) val='';
    if(val>99999999) val = 99999999;
    if(val<-999999) val=-999999;
    var isMinus = false;
    if(val<0){
      val = val*-1;
      isMinus = true;
    }

    var match = val.toString().match(/^\d+(?:\.\d{0,4})?/);
    if(match && match.length>0){
      val = Number.parseFloat(match[0]);
    }
    if(isMinus) val = val*-1;
    return val;
  }
 },
};
</script>

<style lang="scss" scoped>
.condition {
  height: 35px;
  max-height: 35px;
  overflow: hidden;
  position: relative;
  &:hover {
    overflow: visible;

    .top {
      box-shadow: 2px 2px 2px 1px rgba(0, 0, 0, 0.2);
    }
  }
}
.flexwrap{
 display: flex; 
 flex-direction: row; 
 flex-wrap: wrap; 
 overflow: hidden; 
 white-space: nowrap;
 position: relative;
}

.top {
    position: absolute;
    background: white;
    z-index: 100;
    top: 0;
    width: 100vw;
    .el-col {
      height: 30px;
      margin-bottom: 5px;

      .publicCss {
        width: 100%;
      }
    }
  }

.flexcloumn{
 display: flex;
 justify-content: center;
 align-items: center;
}

.flexrow{
 display: flex;
 flex-direction: row;
}
.bluetext{
 color: #409EFF;
 padding: 5px;
}

.blacktext{
 color: #1c252e;
}

.input-with-select{
 margin-left: 15px;
 margin-top: 5px;
}

::v-deep .input-with-select .nopinput .el-input__inner {
  padding-right: 0 !important;
}

::v-deep .input-with-select .nopinput .el-input__suffix {
  margin-right: 10px;
}

::v-deep .goodsimg img{
 max-height: 260px !important; 
 max-width: 220px !important; 
}

.el-image.goodsimg{
  width:100%;
}

.orderTitle{
    color: #333333;
    height: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
    height: 2.5rem;
  }

  .bmzl{
   font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";
   color: rgba(0,0,0,.5);
    font-size: 12px;
    white-space: nowrap;
  }

  .commonsize{
   margin-top: 10px;
   font-size: 12px;
   color: #838383;
   font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";
  }

  .list-check{
    position:absolute;
    margin-left:5px;
  }

</style>