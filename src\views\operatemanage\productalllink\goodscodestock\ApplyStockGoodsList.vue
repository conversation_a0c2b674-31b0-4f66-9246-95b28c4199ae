<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-button-group >
                <el-button style="padding: 0;margin: 0;border:none;">
                    <el-date-picker style="width: 260px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        :picker-options="pickerOptions" :clearable="false"></el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;border:none;">
                    <inputYunhan :key="'3'" :width="'150px'" ref="childGoodsCode" v-model="filter.goodsCode"
                        placeholder="商品编码" :inputt.sync="filter.goodsCode" :clearable="true" @callback="callbackGoodsCode" title="商品编码"></inputYunhan>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;border:none;">
                    <el-select v-model="filter.auditState" style="width:120px;" clearable aria-placeholder="申报状态">
                        <el-option :value="1" label="申报中"></el-option>
                        <el-option :value="2" label="申报成功"></el-option>
                        <el-option :value="0" label="申报失败"></el-option>
                        <el-option :value="3" label="驳回"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;border:none;">
                    <el-input v-model.trim="filter.keywords" placeholder="关键字查询" maxLength="20" clearable >
                        <el-tooltip slot="suffix"  effect="dark" content="模糊查询：商品编码、商品名称、申报人。" placement="bottom">
                            <i class="el-input__icon el-icon-question"></i>
                        </el-tooltip>
                    </el-input>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;border:none;">
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="exportSelectEvent">导出选中数据</el-button>
                </el-button>

            </el-button-group>
        </template>
        <!--列表-->
        <vxetablebase :id="'ApplyStockGoodsList202304211314001'" :isIndex='true'  @select='selectchange'
            :tableData='list' :tableCols='tableCols' :tablefixed='true' :loading='listLoading' :border='true' :that="that"
            ref="vxetable" @cellClick='cellclick' @sortchange='sortchange' :checkbox-config="{labelField: 'id', highlight: true, range: true}"
            @checkbox-range-end="callback"   :tablekey="'applyStockGoodsList202304221441'">
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <!-- 编码明细 -->
        <el-dialog :visible.sync="dialogVisible" title="详情信息" width="1200" v-dialogDrag>
            <procodedetail :filter="filterdetail" ref="procodedetail"></procodedetail>
        </el-dialog>

        <!-- 销量趋势 -->
        <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="75%" height='700px'
            v-dialogDrag>
            <span>
                <template>
                    <el-form class="ad-form-query" :model="filterchart" @submit.native.prevent label-width="100px">
                        <el-row>
                            <el-col :xs="24" :sm="3" :md="3" :lg="3" :xl="3">
                                <el-radio-group v-model="timeType"
                                    @change="getbirchartOnSearch(goodsCode, timeNum, timeType)">
                                    <el-radio-button label="0">付款维度</el-radio-button>
                                    <el-radio-button label="1">发货维度</el-radio-button>
                                </el-radio-group>
                            </el-col>
                            <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
                                <el-form-item label="日期:">
                                    <el-date-picker style="width: 260px" v-model="filterchart.timerange" type="daterange"
                                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                                        start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptionspie"
                                        :clearable="false"></el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="9" :md="9" :lg="9" :xl="9">
                                <el-form-item>
                                    <el-button type="primary"
                                        @click="getbirchartOnSearch(goodsCode, timeNum, timeType)">刷新</el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </template>
            </span>
            <!-- <span>
                <el-radio-group v-model="timeType" @change="getbirchart(goodsCode, timeNum, timeType)">
                    <el-radio-button label="0">付款维度</el-radio-button>
                    <el-radio-button label="1">发货维度</el-radio-button>
                </el-radio-group>
            </span> -->
            <span>
                <buschar v-if="buscharDialog.visible" ref="buschar" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <!-- 编码进货 -->
        <el-dialog :visible.sync="dialogVisiblecodedpurchase" width="75%" v-dialogDrag>
            <el-tabs v-model="activeName" style="height: calc(100% - 40px);" @tab-click="handleClick">
                <el-tab-pane label="主动申报" name="tab1" style="height: 100%;">
                    <codedpurchase ref="codedpurchase" style="z-index:1000;"></codedpurchase>
                </el-tab-pane>
                <el-tab-pane label="认领数量" name="tab2" style="height: 100%;" :lazy="true">
                    <applycodedpurchase ref="applycodedpurchase" style="z-index:1000;"></applycodedpurchase>
                </el-tab-pane>
            </el-tabs>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisiblecodedpurchase = false">取消</el-button>
                    <my-confirm-button type="submit" :loading="onFinishLoading" @click="onFinish(false)">保存&关闭
                    </my-confirm-button>
                    <my-confirm-button type="submit" :loading="onFinishLoading" @click="onFinish(true)">保存&发起审批
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>

        <!-- 历史进货数据 -->
        <el-dialog :visible.sync="dialogVisiblecodedsearch" width="80%" v-dialogDrag>
            <el-tabs v-model="activeNameNew" style="height: calc(100% - 40px);" @tab-click="handleClickNew">
                <el-tab-pane label="主动申报" name="tab1" style="height: 100%;">
                    <codedpurchasehistory :goodsCodeFilter="goodsCodeFilter" ref="codedpurchasehistory" style="z-index:1000;"></codedpurchasehistory>
                </el-tab-pane>
                <el-tab-pane label="认领数量" name="tab2" style="height: 100%;" :lazy="true">
                    <codedpurchasehistory :goodsCodeFilter="goodsCodeFilter" ref="codedpurchasehistory" style="z-index:1000;"></codedpurchasehistory>
                </el-tab-pane>

            </el-tabs>
        </el-dialog>


        <!-- 毛三趋势 -->
        <el-dialog :title="profit3BuscharDialog.title" :visible.sync="profit3BuscharDialog.visible" width="80%"
            v-dialogDrag>
            <span>
                <buschar v-if="profit3BuscharDialog.visible" ref="profit3Buschar" :analysisData="profit3BuscharDialog.data">
                </buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="profit3BuscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>

    </container>
</template>
<script>
import { Loading } from 'element-ui';
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import inputYunhan from "@/components/Comm/inputYunhan";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { PageApplyStockGoods, getPurOrderAnalysisForOperate, importGoodsCodeSellAnalyst, getProfit3RateAnalysis } from "@/api/inventory/goodscodestock"
import procodedetail from './procodedetail.vue'
import codedpurchase from './codedpurchase.vue'
import applycodedpurchase from './applycodedpurchase.vue'
import codedpurchasehistory from './codedpurchasehistory.vue'
import buschar from '@/components/Bus/buschar'
import { formatNoLink,formatSecondNewToHour } from "@/utils/tools";
var formatSecondToHour1 = function(time) {
    return formatSecondNewToHour(time);
}
const tableCols = [
    {type:'checkbox',field:'checkbox',width:50},
{ istrue: true, prop: 'picture', label: '图片', width: '50', type: 'images', fixed:'left' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '120', type:'html', fixed:'left', sortable: 'custom', formatter: (row) => formatNoLink(row.goodsCode) },
    { istrue: true, prop: 'goodsName', label: '商品名称', minwidth: '140', sortable: 'custom' },

    { istrue: true, prop: 'styleCode', label: '系列编码', width: '120', sortable: 'custom'  },
    { istrue: true, prop: 'personUsableQty', label: '个人可用数', width: '100' },
    { istrue: true, prop: 'purchaseInTransitQty', label: '采购在途数', width: '100', sortable: 'custom'},
    { istrue: true, prop: 'purchaseInTransitAvgMin', label: '平均在途时长', width: '100', sortable: 'custom', formatter:(row)=>formatSecondToHour1(row.purchaseInTransitAvgMin)  },
    {
        istrue: true, label: `销量`, merge: true, prop: 'mergeField',
        cols: [
            { istrue: true, prop: 'salesDay', label: '昨天', width: '80', sortable: 'custom', },
            { istrue: true, prop: 'salesDay7', label: '7天', width: '80', sortable: 'custom', },
            { istrue: true, prop: 'salesDay15', label: '15天', width: '80', sortable: 'custom', },
            { istrue: true, prop: 'salesDay30', label: '30天', width: '80', sortable: 'custom', },
        ]
    },
    { istrue: true, prop: 'turnoverDays', label: '1天周转天数', width: '90', sortable: 'custom',  formatter: (row) => (row.turnoverDays).toFixed(2) },
    { istrue: true, prop: 'turnoverDays3', label: '3天周转天数', width: '90', sortable: 'custom',  formatter: (row) => (row.turnoverDays3).toFixed(2) },

    { istrue: true, prop: 'applyUserName', label: '申报人', width: '80', sortable: 'custom'  },
    { istrue: true, prop: 'groupName', label: '运营组', width: '80', sortable: 'custom'  },
    { istrue: true, prop: 'remark', label: '驳回原因', width: '80', sortable: 'custom'  },
    { istrue: true, prop: 'purImageUrl', label: '图片', width: '100', type:'images'},

    { istrue: true, prop: 'applyTime', label: '申报时间', width: '140', sortable: 'custom'  , fixed:'right',formatter:(row) => row.applyTime ? dayjs(row.applyTime).format('YYYY-MM-DD HH:mm') : ''},
    { istrue: true, prop: 'warehouseName', label: '仓库', width: '140', sortable: 'custom'  , fixed:'right'},
    { istrue: true, prop: 'stockInQtyDaily', label: '日常进货量', width: '90', sortable: 'custom', fixed:'right' },
    { istrue: true, prop: 'stockInQtyPromotion', label: '活动进货量', width: '90', sortable: 'custom', fixed:'right'  },
    { istrue: true, prop: 'stockInQtyTotal', label: '申报总数量', width: '90', sortable: 'custom' , fixed:'right' },

    { istrue: true, prop: 'auditState', label: '状态', width: '70', sortable: 'custom', fixed:'right',formatter:(row)=>row.auditStateText  },
];

const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");

export default {
    name: 'ApplyStockGoodsList',
    components: { container, MyConfirmButton, vxetablebase, inputYunhan, procodedetail, codedpurchase, applycodedpurchase, codedpurchasehistory, buschar },
    // props: {
    //     filter: {}
    // },
    data() {
        return {
            that: this,
            activeName: 'tab1',
            activeNameNew: 'tab1',
            filter: {
                startDate: null,
                endDate: null,
                goodsCode: null,
                auditState:null,
                keywords:null,
                timerange: [startTime, endTime],
            },
            filterdetail: {
                goodsCode: null,
                startDate: null,
                endDate: null
            },
            filterchart: {
                startDate: null,
                endDate: null,
                timerange: [startDate, endDate]
            },
            list: [],
            tableCols: tableCols,
            pager: { OrderBy: "id", IsAsc: false },
            total: 0,
            sels: [],
            selrows: [],
            chooseTags: [],
            addForm: {
                id: null,
                goodsCode: null,
                picture: null,
                profit3Rate: null,
                turnoverDays: null,
                turnoverDays3: null,
                expectedSale: null,
                appStatus: null,
                salesDay: 0,
                salesDay7: 0,
                salesDay15: 0,
            },
            timeType: 0, // 时间类型 0：付款维度，1：发货维度
            timeNum: null,
            goodsCodeFilter: {
                goodsCode: null,
                appStatus: null,
            },
            listLoading: false,
            pageLoading: false,
            uploadLoading: false,
            dialogVisibleimport: false,
            dialogVisiblecodedpurchase: false,
            dialogVisiblecodedsearch: false,
            onFinishLoading: false,
            dialogVisible: false,
            buscharDialog: { visible: false, title: "", data: [] },
            profit3BuscharDialog: { visible: false, title: "", data: [] },
            pickerOptions: {
                onPick: ({ maxDate, minDate }) => {
                    this.selectDate = minDate.getTime()
                    if (maxDate) {
                        this.selectDate = ''
                    }
                },
                disabledDate: (time) => {
                    if (this.selectDate !== '') {
                        const one = 30 * 24 * 3600 * 1000
                        const minTime = this.selectDate - one
                        const maxTime = this.selectDate + one
                        return time.getTime() < minTime || time.getTime() > maxTime
                    }
                }
            },
            pickerOptionspie: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近半个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            selectList:[]
        };
    },

    async mounted() {
        await this.onSearch();
    },

    methods: {
        exportSelectEvent(){
            if (this.selectList.length ==0) return this.$message.error('请选择导出数据')
            const res = this.selectList.map(item =>{
               return {
                    picture:item.picture,
                    goodsCode:item.goodsCode,
                    goodsName:item.goodsName,
                    styleCode:item.styleCode,
                    personUsableQty:item.personUsableQty,
                    purchaseInTransitQty:item.purchaseInTransitQty,
                    purchaseInTransitAvgMin:item.purchaseInTransitAvgMin,
                    salesDay:item.salesDay,
                    salesDay7:item.salesDay7,
                    salesDay15:item.salesDay15,
                    salesDay30:item.salesDay30,
                    turnoverDays:item.turnoverDays,
                    turnoverDays3:item.turnoverDays3,
                    applyUserName:item.applyUserName,
                    groupName:item.groupName,
                    remark:item.remark,
                    purImageUrl:item.purImageUrl,
                    applyTime:item.applyTime ? dayjs(item.applyTime).format('YYYY-MM-DD HH:mm:ss') : '',
                    warehouseName:item.warehouseName,
                    stockInQtyDaily:item.stockInQtyDaily,
                    stockInQtyPromotion:item.stockInQtyPromotion,
                    stockInQtyTotal:item.stockInQtyTotal,
                    auditState:item.auditStateText
                }
            })
            this.$refs.vxetable.$refs.xTable.exportData({
                data:  res,
                columnFilterMethod({column }){
                    return ['picture', 'goodsCode', 'goodsName', 'styleCode', 'personUsableQty', 'purchaseInTransitQty', 'purchaseInTransitAvgMin', 'salesDay', 'salesDay7', 'salesDay15','salesDay30', 'turnoverDays', 'turnoverDays3', 'applyUserName', 'groupName', 'remark', 'purImageUrl', 'applyTime', 'warehouseName', 'stockInQtyDaily', 'stockInQtyPromotion', 'stockInQtyTotal', 'auditState'].includes(column.field)
                }
              })
        },
        //批量申报
        async batchApply() {
                let self = this;
                if (!self.selrows || self.selrows.length <= 0) {
                    this.$message({ message: "请勾选至少一行数据", type: "warning" });
                    return;
                }

                let selData=self.selrows.map((item)=>{
                    let newItem={...item};

                    return newItem;
                })

                this.$showDialogform({
                    path: `@/views/operatemanage/productalllink/goodscodestock/ApplyStockGoodsForm.vue`,
                    title: '批量申报',
                    autoTitle: false,
                    args: { selRows: selData },
                    height: 700,
                    width: '80%',
                    //callOk: self.onSearch
                })
            },
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await PageApplyStockGoods(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.total = res.data.total;
            const data = res.data.list;
            this.list = data
        },
        async onFinish(isEnd) {
            this.onFinishLoading = true;
            var flag = false;
            if (this.activeName == 'tab1') {
                flag = await this.$refs.codedpurchase.onFinish(isEnd);
            }else if (this.activeName == 'tab2') {
                flag = await this.$refs.applycodedpurchase.onFinish(isEnd);
            }
            console.log('数据输出',flag)
            this.onFinishLoading = false;
            if (flag) {
                this.dialogVisiblecodedpurchase = false;
            }
        },
        async clickProfit(row) {
            this.dialogVisible = true;
            this.filterdetail.goodsCode = row.goodsCode
            this.filterdetail.startDate = null;
            this.filterdetail.endDate = null;
            if (this.filter.timerange) {
                this.filterdetail.startDate = this.filter.timerange[0];
                this.filterdetail.endDate = this.filter.timerange[1];
            }
            this.$nextTick(() => {
                this.$refs.procodedetail.clearData();
                this.$refs.procodedetail.onSearch();
            })
        },
        async callbackGoodsCode(val) {
            // this.inputedit = true;
            this.filter.goodsCode = val;
            this.onSearch();
        },
        async getbirchart(goodsCode, number, type) {
            this.startDate = formatTime(dayjs().subtract(number, 'day'), "YYYY-MM-DD");
            this.endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
            this.filterchart.timerange = [this.startDate, this.endDate];
            this.filterchart.startDate = null;
            this.filterchart.endDate = null;
            if (this.filterchart.timerange) {
                this.filterchart.startDate = this.startDate;
                this.filterchart.endDate = this.endDate;
            }
            this.goodsCode = goodsCode;
            this.timeNum = number;
            this.timeType = type;
            let loadingInstance = Loading.service();
            Loading.service({ fullscreen: true });
            var that = this;
            const params = { goodsCode: goodsCode, day: number, timeType: type, ...this.filterchart };
            //console.log('数据来了', params);
            await getPurOrderAnalysisForOperate(params).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data;
                that.buscharDialog.title = '商品编码：' + goodsCode;
            });
            await this.$refs.buschar.initcharts()
            loadingInstance.close();
        },
        async getbirchartOnSearch(goodsCode, number, type) {
            this.filterchart.startDate = null;
            this.filterchart.endDate = null;
            if (this.filterchart.timerange) {
                this.filterchart.startDate = this.filterchart.timerange[0];
                this.filterchart.endDate = this.filterchart.timerange[1];
            }
            this.goodsCode = goodsCode;
            this.timeNum = number;
            this.timeType = type;
            let loadingInstance = Loading.service();
            Loading.service({ fullscreen: true });
            var that = this;
            const params = { goodsCode: goodsCode, day: number, timeType: type, ...this.filterchart };
            //console.log('数据来了', params);
            await getPurOrderAnalysisForOperate(params).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data;
                that.buscharDialog.title = '商品编码：' + goodsCode;
            });
            await this.$refs.buschar.initcharts()
            loadingInstance.close();
        },
        async handclick(row) {
            this.activeName = 'tab1';
            this.dialogVisiblecodedpurchase = true;
            const { goodsCode, picture, profit3Rate, turnoverDays, turnoverDays3, expectedSale, salesDay, salesDay7, salesDay15, } = row;
            this.addForm = {
                ...this.addForm,
                goodsCode,
                picture,
                profit3Rate,
                turnoverDays,
                turnoverDays3,
                expectedSale,
                salesDay,
                salesDay7,
                salesDay15
            };
            this.$nextTick(async () => {
                await this.$refs.codedpurchase.onSearch(this.addForm);
            })
        },
        async handleClick() {
            if (this.activeName == 'tab1') {
                this.$nextTick(async () => {
                    this.addForm.appStatus = 0;
                    await this.$refs.codedpurchase.onSearch(this.addForm);
                })
            }else if (this.activeName == 'tab2') {
                this.$nextTick(async () => {
                    this.addForm.appStatus = 1;
                    await this.$refs.applycodedpurchase.onSearch(this.addForm);
                })
            }
        },
        async handsearchclick(row) {
            this.activeNameNew = 'tab1';
            this.dialogVisiblecodedsearch = true;
            this.goodsCodeFilter.goodsCode = row.goodsCode;
            this.goodsCodeFilter.appStatus = 0;
            this.$nextTick(async () => {
                await this.$refs.codedpurchasehistory.onSearch();
            })
        },
        async handleClickNew() {
            if (this.activeNameNew == 'tab1') {
                this.$nextTick(async () => {
                    this.goodsCodeFilter.appStatus = 0;
                    await this.$refs.codedpurchasehistory.onSearch();
                })
            }else if (this.activeNameNew == 'tab2') {
                this.$nextTick(async () => {
                    this.goodsCodeFilter.appStatus = 1;
                    await this.$refs.codedpurchasehistory.onSearch();
                })
            }
        },
        //新增时提交验证
        finishFormValidate: function () {
            let isValid = false
            this.$refs.addForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        startImport() {
            this.dialogVisibleimport = true;
        },
        cancelImport() {
            this.dialogVisibleimport = false;
        },
        beforeRemove() {
            return false;
        },
        uploadSuccess(response, file, fileList) {
            if (response.code == 200) {
            } else {
                fileList.splice(fileList.indexOf(file), 1);
            }
        },
        async submitUpload() {
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请先选取文件", type: "warning" });
                return false;
            }
            this.fileHasSubmit = true;
            this.uploadLoading = true;
            this.$refs.upload.submit();
        },
        async uploadFile(item) {
            if (!this.fileHasSubmit) {
                return false;
            }
            this.fileHasSubmit = false;
            const form = new FormData();
            form.append("token", this.token);
            form.append("upfile", item.file);
            const res = await importGoodsCodeSellAnalyst(form);
            if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
            else this.$message({ message: res.msg, type: "warning" });
            this.uploadLoading = false;
        },
        async uploadChange(file, fileList) {
            if (fileList && fileList.length > 0) {
                var list = [];
                for (var i = 0; i < fileList.length; i++) {
                    if (fileList[i].status == "success")
                        list.push(fileList[i]);
                    else
                        list.push(fileList[i].raw);
                }
                this.fileList = list;
            }
        },
        uploadRemove(file, fileList) {
            this.uploadChange(file, fileList);
        },
        async cellclick({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }) {
            if (column?.property == 'goodsCode')
                await this.clickProfit(row)
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        // selectchange: function (rows, row) {
        //     this.selids = [];
        //     rows.forEach(f => {
        //         this.selids.push(f);
        //     })
        // },
        callback(val){
            this.selrows=[...val];

            this.tablelist = [];
            this.tablelist = val;
            var goodsCode = val.map((item)=>{
                return item.goodsCode;
            })
            this.chooseTags = goodsCode;
            console.log("goods返回值",this.chooseTags)
        },
        selectchange: function (rows, row) {
            console.log(rows, 'rows')
            console.log(row, 'row')
            this.selectList = rows
        //先把当前也的数据全部移除
        this.list.forEach(f => {
            let index = this.chooseTags.findIndex((v) => (v === f.goodsCode));
            if (index !== -1) {
            this.chooseTags.splice(index, 1);
            this.selrows.splice(index, 1);
            }
        });
        //把选中的添加
        rows.forEach(f => {
            let index = this.chooseTags.findIndex((v) => (v === f.goodsCode));
            if (index === -1) {
            this.chooseTags.push(f.goodsCode);
            this.selrows.push(f);
            console.log("选中数据",this.selrows);
            }
        });

        ///
        let _this = this;
            if(rows.length>0){
                var a = [];
                rows.forEach(element => {
                    let b = _this.list.indexOf(element);
                    a.push(b+1);
                });

                let d = _this.list.indexOf(row);

                var b = Math.min(...a)
                var c = Math.max(...a)

                a.push(d);
                if(d<b){
                    var b = _this.list.indexOf(row);
                    var c = Math.max(...a)
                }else if(d>c){
                    var b = Math.min(...a)-1
                    var c = Math.max(...a)
                }else{
                    var b = Math.min(...a)-1
                    var c = _this.list.indexOf(row)+1;
                }

                let neww = [b,c];
                _this.selids = neww;
            }
            console.log('选择的数据',this.selids)
        },
        async showProfit3RateChat(goodsCode) {
            let loadingInstance = Loading.service();
            let params = { goodsCode: goodsCode, startDate: this.filter.startDate, endDate: this.filter.endDate }
            let that = this;
            await getProfit3RateAnalysis(params).then(res => {
                that.profit3BuscharDialog.visible = true;
                that.profit3BuscharDialog.data = res.data;
                that.profit3BuscharDialog.title = '商品编码：' + goodsCode;
            });
            await this.$refs.profit3Buschar.initcharts()
            loadingInstance.close();
        }
    }
};
</script>

<style lang="scss" scoped></style>
