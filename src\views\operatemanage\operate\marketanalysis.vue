<template>
  <container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="日期:">
            <el-date-picker @change="ondatechange"
                v-model="filter.timerange"
                type="datetimerange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
           ></el-date-picker>
        </el-form-item>
        <el-form-item label="类目:">
          <el-cascader @change="oncategorychange" 
            v-model="filter.categoryids"
            placeholder="请选择，支持搜索功能"
            :options="categorylist"
            :props="{ checkStrictly: false, value: 'id',multiple:false }"
            style="width:100%;"/>
        </el-form-item>
         <el-form-item label="组长:">
            <el-select v-model="filter.groupId" placeholder="请选择" class="el-select-content" @change="ongroupchange">
              <el-option label="请选择" value></el-option>
              <el-option
                  v-for="item in grouplist"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key">
              </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="商品:">
            <el-select v-model="filter.selfProCode" placeholder="请选择" class="el-select-content">
               <el-option label="请选择" value></el-option>
              <el-option
                  v-for="item in productlist"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key">
                  <span style="float: left">{{ item.key+":" }}</span>
                  <span style="float: left; color: #8492a6; font-size: 13px">{{ item.value }}</span>
              </el-option>
            </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
  <el-tabs v-model="activeName" style="height: 94%;">
    <el-tab-pane label="市场排行分析" name="first" style="height: 100%;">
        <promonitoranalysis :filter="filter" ref="promonitoranalysis"/>
      </el-tab-pane>
       <el-tab-pane label="市场分析" name="first2" style="height: 100%;">
        <promonitoranalysis2 :filter="filter" ref="promonitoranalysis2"/>
      </el-tab-pane>
      <el-tab-pane label="热搜词产值分析" name="first3" style="height: 100%;">
        <hotwordsmonitoranalysis :filter="filter" ref="hotwordsmonitoranalysis"/>
      </el-tab-pane>
      <el-tab-pane label="市场排行榜" name="first1" style="height: 94%;">
        <promonitorranklist :filter="filter" ref="promonitorranklist" @onpktype1="onpktype1" style="height: calc(100% + 50px);"/>
      </el-tab-pane>
      <el-tab-pane label="市场排行详情" name="second" style="height: 100%;">
        <promonitordata :filter="filter" ref="promonitordata"/>
      </el-tab-pane>
  </el-tabs>
  </container>
</template>
<script>
 import {seachProCode} from '@/api/operatemanage/operate'
 import { getList as getcategorylist} from '@/api/operatemanage/base/category'
 import { getProductKeyValueByGroup,getGroupKeyValueByCategory} from '@/api/operatemanage/base/product'
 import promonitoranalysis from '@/views/operatemanage/operate/promonitoranalysis'
 import promonitoranalysis2 from '@/views/operatemanage/operate/promonitoranalysis2'
 import hotwordsmonitoranalysis from '@/views/operatemanage/operate/hotwordsmonitoranalysis' 
 import promonitorranklist from '@/views/operatemanage/operate/promonitorranklist'
 import promonitordata from '@/views/operatemanage/operate/promonitordata'
 import container from '@/components/my-container/nofooter'
 import { treeToList, listToTree, getTreeParents } from '@/utils'
export default {
  name: 'Roles',
  components: { container,promonitoranalysis,promonitoranalysis2,hotwordsmonitoranalysis,promonitorranklist,promonitordata },
  data() {
    return {
      activeName: 'first',
      filter: {
        startDate: null,
        endDate: null,
        proCodeKey:null,
        selfProCode:null,
        timerange:null,
        categoryids:[],
        groupId:null,
        //procode:null
      },
      categoryKey: 1,
      options:[],
      categorylist:[],
      grouplist:[],
      productlist:[],
      pageLoading: false,    
      dialogVisible: false,
      selectloading:false,
    }
  },
  mounted() {
    this.getcategorylist();
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async getcategorylist() {
      const res = await getcategorylist({platform:1 })
      if (!res?.code) {
        return
      }
      const list=[];
      res.data.forEach(f=>{
        var item=  {label:f.categoryName,id:f.id,parentId:f.parentId}
        list.push(item)
      })
      this.categorylist = listToTree(_.cloneDeep(list), {
        id: '',
        parentId: '',
        label: '所有'
      })
    },
  async ondatechange(){
     // this.filter.selfProCode=""
    },
  async oncategorychange(value) {
    this.filter.selfProCode=null;
    this.filter.groupId=null;
    console.log('value',value)
    this.grouplist=[]
    this.productlist=[]
    if (value.length>1) {
      const res = await getGroupKeyValueByCategory({categoryId:value[value.length-1]})
      if (!res?.code) return
      this.grouplist=res.data

      const res1 = await getProductKeyValueByGroup({categoryId:value[value.length-1]})
      if (!res1?.code) return
      this.productlist=res1.data
    }
  },
async ongroupchange(value) {
    this.filter.selfProCode=null;
    var parm={categoryId:null, groupId:null};
    if (this.filter.categoryids&&this.filter.categoryids.length>0) parm.categoryId=this.filter.categoryids[this.filter.categoryids.length-1]
    this.productlist=[]
    if (value) parm.groupId=value
    else parm.groupId=null
    const res = await getProductKeyValueByGroup(parm)
    if (!res?.code) return
    this.productlist=res.data
  },
async remoteSeachProCode(key){
     if (!this.filter.timerange||this.filter.timerange.length<2){
        this.$message({message: "请先选择日期！",type: "warning",});
        return;
      }
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      if (key !== '') {
          this.filter.proCodeKey=key
          this.selectloading = true;
          setTimeout(async () => {
            this.selectloading = false;
            var res= await  seachProCode(this.filter);
            if (!res?.success) return;
            this.options = res.data.map(item => {
               return { value: item, label: item };
            });
          }, 200);
        } else {
          this.options = [];
        }
    },
async onSearch() {
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      if (!this.filter.timerange||this.filter.timerange.length<2){
        this.$message({message: "请先选择日期！",type: "warning",});
        return;
      }
      // if (!this.filter.selfProCode){
      //   this.$message({message: "请先选择商品ID！",type: "warning",});
      //   return;
      // }
      if (this.activeName=='first'){
         this.$refs.promonitoranalysis.initdata();
         this.$refs.promonitoranalysis.onSearch();
      }
      else if (this.activeName=='first2')
         this.$refs.promonitoranalysis2.onSearch();
      else if (this.activeName=='first1')
         this.$refs.promonitorranklist.onSearch();
      else if (this.activeName=='first3')
         this.$refs.hotwordsmonitoranalysis.onSearch();
      else if (this.activeName=='second')
         this.$refs.promonitordata.onSearch();
    },
    onpktype1(ids){
       this.activeName="first"
       this.$refs.promonitoranalysis.initdata();
       this.$refs.promonitoranalysis.onpk(ids);
    },
  }
}
</script>

<style>
/* .el-tabs__content {
    overflow: hidden;
    position: relative;
    height: 100%;
}
.el-select-content { 
    width: calc(100% - 10px);
    margin: 0;
 } */
.bottombadge {
    position: absolute;    
    transform: translateY(40%) translateX(100%);
    /* margin: 0 0px -5px 15px; */
}
.bigbadge {
  font-size: initial;
}
.redcolor {
  color: red;
}
.greencolor {
  color:green;
}
</style>
