<template>
    <my-container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="揽收时间">
                    <el-date-picker style="width: 200px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                        range-separator="至" start-placeholder="开始" end-placeholder="结束" :clearable="false" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>        
                <el-form-item label="发货仓库:">
                    <el-select v-model="filter.warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 120px">
                        <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value"/>
                    </el-select>
                </el-form-item> 
                <el-form-item label="快递公司:">
                    <el-select v-model="filter.companyId" placeholder="请选择快递公司" style="width: 130px">
                        <el-option label="所有" value=""/>
                        <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id"/>
                    </el-select>
                </el-form-item>   
                <el-form-item>
                <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>

        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
            :tableData='list'  :tableCols='tableCols' :isSelection="false" @select="selectchange"
            :tableHandles='tableHandles' @cellclick="cellclick" :showsummary='true' @summaryClick='onsummaryClick'
            :loading="listLoading">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"  @get-page="getlist"/>
        </template>

        <el-dialog :visible.sync="dialodayssisVisible" width="80%" :show-close="false" v-dialogDrag>
            <div style="height:650px;"> 
                <ecpresscheckorderdetail ref="ecpresscheckorderdetail" style="height:100%;"></ecpresscheckorderdetail>
            </div>
        </el-dialog>

        <!-- 系列编码趋势图 -->
        <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
        <span>
        <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
        </span>
        <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
        </span>
        </el-dialog>

    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import { getExpressCheckOrder, getExpressComanyAll } from "@/api/express/express"
import ecpresscheckorderdetail from "./ecpresscheckorderdetail.vue"
import {formatPass,formatRule,formatWarehouse,warehouselist} from "@/utils/tools";
import buschar from '@/components/Bus/buschar'
import { getAnalysisCommonResponse } from '@/api/admin/common'

const tableCols =[
        {istrue:true,prop:'receiveTime',label:'揽收时间', tipmesg:'', width:'100',sortable:'custom',},
        {istrue:true,prop:'expressNo',label:'快递单号', tipmesg:'',width:'180',sortable:'custom',}, 
        {istrue:true,prop:'expressCompanyName',label:'快递公司', width:'100'},
        {istrue:true,prop:'warehouse',label:'发货仓', width:'75',sortable:'custom',formatter:(row)=>formatWarehouse(row.warehouse)},
        {istrue:true,summaryEvent: true,prop:'weight',label:'发往省份重量', tipmesg:'', width:'110',sortable:'custom',formatter:(row)=>!row.weight? '' : row.weight.toFixed(2)},  
        {istrue:true,prop:'province',label:'发往省份', tipmesg:'', width:'110',},         
        {istrue:true,summaryEvent: true,prop:'totalFee',label:'账单金额', tipmesg:'', width:'120',sortable:'custom',}, 
        //{istrue:true,prop:'hasVolune',label:'重量类型', tipmesg:'', width:'200',},      
        {istrue:true,summaryEvent: true,prop:'jsTotalFee',label:'计算金额', tipmesg:'', width:'120', sortable:'custom',},               
        {istrue:true,summaryEvent: true,prop:'differenceFee',label:'审单差额', tipmesg:'', width:'120', sortable:'custom',},               
        {istrue:true,prop:'computeNum',label:'拆分单数', tipmesg:'',width:'auto',sortable:'custom',type:'click',handle:(that,row)=>that.showdetail(row)},  
        //{istrue:true,prop:'goodsName',label:'拆分重量', width:'125',sortable:'custom'},     
        // {istrue:true,prop:'orderAmount',label:'订单金额', tipmesg:'聚水潭订单付款金额', width:'100',sortable:'custom',},
        // {istrue:true,prop:'qty',label:'数量',sortable:'custom', tipmesg:'订单对应编码的下单数量', width:'80',},       
]

const tableHandles=[
        
      ];


const startTime = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunhanAdminEcpresscheckorder',
    components :{MyContainer, MyConfirmButton, cesTable, MySearch, MySearchWindow, ecpresscheckorderdetail,buschar },

    data() {
        return {
            that: this,
            filter:{
                startTime: null,
                endTime: null,
                timerange:[startTime, endTime],
                procode:null,
                title:null,
                platform:null,
                shopCode:null,
                groupId:null,
                operateSpecialId:null,
                operateName:null,
                warehouse:null,
                companyId:null,
                newPattern:null,
                customer:null,
            },
            warehouselist:warehouselist,
            expresscompanylist: [],
            list:[],
            summaryarry:{},
            tableCols:tableCols,
            tableHandles:tableHandles,
            total: 0,
            sels: [],
            pager:{OrderBy:"receiveTime",IsAsc:false},
            dialogVisible: false,
            dialodayssisVisible: false,
            listLoading: false,
            pickerOptions:{
                disabledDate(time){
                return time.getTime()>Date.now();
                }
            },   
            analysisFilter: {
                    searchName: "ExpressCheckOrder",
                    isYearMonthDay:true,
                    isTimeFormat:true,
                    extype: 2,
                    selectColumn: "",
                    filterTime: "receiveTime",
                    filter: null,
                    columnList: [],
                },
            buscharDialog: { visible: false, title: "", data: [] },     
        };
    },

    async mounted() {
        await this.onSearch()
        await this.getExpressComanyList();
    },

    methods: {
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page  = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...pager,...page,... this.filter}
            if(params===false){
                return;
            }
            this.listLoading = true
            const res = await getExpressCheckOrder(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry=res.data.summary;
            data.forEach(d => {
                d._loading = false
            })
            this.list = data
       },
       async showdetail(row){
            this.dialodayssisVisible = true
            var para = {id : row.id}
            this.$nextTick(async ()=>{
                await this.$refs.ecpresscheckorderdetail.onSearch1(para)
            })     
       },
       async getExpressComanyList() {
            const res = await getExpressComanyAll({});
            if (!res?.success) {
                return;
            } 
            const data = res.data;
            this.expresscompanylist = data;
        },
        async sortchange(column){
        if(!column.order)
            this.pager={};
        else{
            this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
        }
        await this.onSearch();
        },  
        selectchange:function(rows,row) {
            this.selids=[];console.log(rows)
            rows.forEach(f=>{
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event){
        
        },
        async onsummaryClick(property) {
                this.analysisFilter.columnList = [];
                this.analysisFilter.filter = null;
                let that = this;
                let summaryEventList = this.tableCols.filter(f => f.summaryEvent);
                summaryEventList.forEach(element => {
                    this.analysisFilter.columnList.push({ columnNameCN: element.label, columnNameEN: element.prop });
                });

                this.analysisFilter.filter = {
                    warehouse: [this.filter.warehouse, 0],
                    expressCompanyId: [this.filter.companyId, 0],
                }
                if (this.filter.timerange) {
                    this.analysisFilter.filter.receiveTime =  [this.filter.timerange[0], this.filter.timerange[1],0];
                }
                this.analysisFilter.selectColumn = property;
                const res = await getAnalysisCommonResponse(that.analysisFilter).then(res => {
                    that.buscharDialog.visible = true;
                    that.buscharDialog.data = res.data
                    that.buscharDialog.title = res.data.legend[0]
                });
        },
        }
};
</script>

<style lang="scss" scoped>

</style>