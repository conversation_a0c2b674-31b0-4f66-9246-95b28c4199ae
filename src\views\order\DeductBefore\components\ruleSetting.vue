<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select v-model="ListInfo.platforms" placeholder="平台" class="publicCss" clearable multiple>
                    <el-option v-for="item in platformlist" :label="item.label" :value="item.value" :key="item.value" />
                </el-select>
                <chooseWareHouse v-model="ListInfo.wmsIds" class="publicCss" multiple :filter="sendWmsesFilter" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="handleAdd(true)">新增</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :isNeedExpend="false" :is-select-column="true" :is-index-fixed="false" @sortchange="sortchange"
            style="width: 100%; margin: 0;height: 600px;" id="20250402134550" :showsummary="data.summary ? true : false"
            :summaryarry="data.summary">
            <template slot="right">
                <vxe-column title="操作" width="120">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="handleAdd(false, row)">编辑</el-button>
                            <el-button type="text" @click="handleDel(row)">删除</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog :title="isAdd ? '新增' : '编辑'" :visible.sync="addVisible" width="40%" :close-on-click-modal="false"
            append-to-body v-dialogDrag>

            <addRule v-if="addVisible" :isAdd="isAdd" @close="addVisible = false" @getList="getList"
                :editForm="editForm" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/Orders/Rule/'
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import { mergeTableCols } from '@/utils/getCols'
import addRule from "./addRule.vue";
const nodeList = [
    { label: '支付时间', value: 'PayTime' },
    { label: '接单时间', value: 'TakeTime' },
    { label: '审核时间', value: 'VerifyTime' },
    { label: '批次生成时间', value: 'PickGenTime' },
    { label: '拣货开始时间', value: 'PickTime' },
    { label: '拣货完成时间', value: 'PickCptTime' },
    { label: '打包时间', value: 'PackTime' },
    { label: '计划发货时间', value: 'PlanSendTime' },
    { label: '发货时间', value: 'SendTime' },
    { label: '揽收时间', value: 'CollectTime' },
    { label: '审单计算时间', value: 'VerifyCptTime' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, addRule, chooseWareHouse
    },
    data() {
        return {
            api,
            nodeList,
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false,
                summarys: [],
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            isAdd: false,
            addVisible: false,
            wmsesList: [],
            editForm: {
                id: ''
            },
        }
    },
    async mounted() {
        await this.getCol();
        await this.getList()
    },
    methods: {
        sendWmsesFilter(wmses) {
            this.wmsesList = wmses;
            return wmses
        },
        handleAdd(type, row) {
            if (!type) {
                this.editForm.id = row.id
            }
            this.isAdd = type
            this.addVisible = true
        },
        handleDel(row) {
            this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await request.post(`${this.api}DeleteDatas`, [row.id])
                if (!success) return
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
                setTimeout(() => {
                    this.getList()
                }, 100)
            }).catch((error) => {
                console.log(error, 'error');
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                data.forEach(item => {
                    if (item.prop == 'beginNode' || item.prop == 'endNode') {
                        item.formatter = row => nodeList.find(x => x.value == row[item.prop])?.label
                    }
                    if (item.prop == 'platforms') {
                        item.formatter = row => platformlist.filter(item => row.platforms.includes(item.value)).map(x => x.label).join(',')
                    }
                    if (item.prop == 'wmsIds') {
                        item.formatter = row => this.wmsesList.filter(item => row.wmsIds.includes(item.wms_co_id)).map(x => x.name).join(',')
                    }
                })
                this.tableCols = mergeTableCols(data)
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0;
    }
}
</style>
