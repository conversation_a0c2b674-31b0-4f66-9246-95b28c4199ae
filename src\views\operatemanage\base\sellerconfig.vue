<template>
 <el-tabs type="border-card">
  <el-tab-pane>
    <template #label>
      <span><i class="el-icon-date"></i>扣除费用参数设置</span>
    </template>
    <el-form class="ad-form-query"   :model="entity"  label-width="180px" size="mini">
      <el-form-item label="包装均值(元/单):">
         <el-input-number v-model="entity.baoZhuangPer" :min="0"/>
      </el-form-item>
      <el-form-item label="快递均值(元/单):">
         <el-input-number v-model="entity.kuaidiPer" :min="0"/>
      </el-form-item>
      <el-form-item label="仓库工资均值(元/单):">
        <el-input-number v-model="entity.ckgzFeePer" :min="0"/>
      </el-form-item>
      <el-form-item label="拼多多服务费比率(%):">
        <el-input-number v-model="entity.pddServicePer" :min="0"/>
      </el-form-item>
      <el-form-item>
        <my-confirm-button type="primary" @click="onSave">保存</my-confirm-button>
        <el-button type="primary" @click="onreFresh">刷新</el-button>
         <!-- <my-confirm-button @click="onreFresh">刷新</my-confirm-button> -->
      </el-form-item>
    </el-form>
  </el-tab-pane>
  <el-tab-pane>
    <template #label>
      <span><i class="el-icon-setting"></i>运营参数设置(OperateManage)</span>
    </template>
    <el-form class="ad-form-query"   :model="opEntity"  label-width="300px" size="mini">
      <p></p>
      <el-form-item label="热销选品-SKU询价报价悬赏初始金额(元):">
         <el-input-number v-model="opEntity.AllLinkHotGoodsSKUEnquiryRewardAmount" :min="0" :step="5" :precision="2" :max="100000"/>
      </el-form-item>
      <el-form-item label="热销选品-SKU询价报价悬赏日增长额(元):">
         <el-input-number v-model="opEntity.AllLinkHotGoodsSKUEnquiryRewardAmountIncr" :min="0" :step="5" :precision="2" :max="10000"/>
      </el-form-item>
      
      <el-form-item>
        <my-confirm-button type="primary" @click="onSaveBase('OpManage')">保存</my-confirm-button>
        <el-button type="primary" @click="onRefresBase('OpManage')" style="margin-left:10px;">刷新</el-button>
         <!-- <my-confirm-button @click="onreFresh">刷新</my-confirm-button> -->
      </el-form-item>
    </el-form>
  </el-tab-pane>
</el-tabs>
</template>

<script>
import { updateSellerConfig, getSellerConfig} from '@/api/operatemanage/base/shop'

import { GetDictionariesAsync, SaveDictionariesAsync} from '@/api/admin/yhdictionary'

import MyConfirmButton from '@/components/my-confirm-button'
export default {
  components: {MyConfirmButton },
  data() {
    return {
      entity:
       {
        baoZhuangPer:0,
        kuaidiPer:0,
        ckgzFeePer:0,
        pddServicePer:0,
        },
        opEntity:{
          AllLinkHotGoodsSKUEnquiryRewardAmount:0,
          AllLinkHotGoodsSKUEnquiryRewardAmountIncr:0,
        }

    }
  },
  mounted() {
    this.onreFresh();

    this.onRefresBase('OpManage');
  },
  methods: { 
    async onreFresh() {
      const res = await getSellerConfig()
      if (!res?.success&&!res.data) {
        return
      }
      else if (res.data==null) {
        return
      }
      this.entity = res.data
    },
    async onSave() {
      const res = await updateSellerConfig(this.entity)
    },
    async onSaveBase(sysCode){     
      let self=this;
      var dtos=[];
      let rlt=null;
      if(sysCode=='OpManage'){
        dtos.push({
          keyCol:'OpManage:AllLinkHotGoodsSKUEnquiryRewardAmount',
          valueCol:self.opEntity.AllLinkHotGoodsSKUEnquiryRewardAmount,
          description:'热销选品-SKU询价报价悬赏初始金额(元)'
        });
        dtos.push({
          keyCol:'OpManage:AllLinkHotGoodsSKUEnquiryRewardAmountIncr',
          valueCol:self.opEntity.AllLinkHotGoodsSKUEnquiryRewardAmountIncr,
          description:'热销选品-SKU询价报价悬赏日增长额(元):'
        });   
        
        rlt=await SaveDictionariesAsync(dtos);
      }

      if(rlt&& rlt.success){
        self.$message.success('保存成功！');
        self.onRefresBase(sysCode);
      }
      
    },
    async onRefresBase(sysCode){
      let self=this;
      let res=await GetDictionariesAsync({start:sysCode});        

      if(res && res.success){
         
        if(sysCode=='OpManage'){
          self.opEntity.AllLinkHotGoodsSKUEnquiryRewardAmount=0;
          self.opEntity.AllLinkHotGoodsSKUEnquiryRewardAmountIncr=0;       
        }
        
        res.data.forEach(element => {
          if(sysCode=='OpManage'){
            self.opEntity[element.keyCol.replace(sysCode+':','')]=element.valueCol;
            // if(element.keyCol==sysCode+":AllLinkHotGoodsSKUEnquiryRewardAmount")
            //   self.opEntity.AllLinkHotGoodsSKUEnquiryRewardAmount=element.valueCol;
            // if(element.keyCol==sysCode+":AllLinkHotGoodsSKUEnquiryRewardAmountIncr")
            //   self.opEntity.AllLinkHotGoodsSKUEnquiryRewardAmountIncr=element.valueCol;
          }                 
        });
      }
    }
  }
}
</script>
