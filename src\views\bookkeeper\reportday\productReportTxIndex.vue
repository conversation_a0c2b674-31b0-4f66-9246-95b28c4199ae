<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="天猫日报" name="first1" style="height: 100%">
        <productReportTx ref="productReportTx" style="height: 100%"></productReportTx>
      </el-tab-pane>
      <el-tab-pane label="订单日报" name="first2" :lazy="true" style="height: 100%"
        v-if="checkPermission('TxOrderDayReport')">
        <TxOrderDayReport @ChangeActiveName2="ChangeActiveName2" :orderNoInner="orderNoInner" ref="TxOrderDayReport"
          style="height: 100%"></TxOrderDayReport>
      </el-tab-pane>
      <el-tab-pane label="编码日报" name="first3" :lazy="true" style="height: 100%"
        v-if="checkPermission('TxGoodCodeDayReport')">
        <TxGoodCodeDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="TxGoodCodeDayReport" style="height: 100%"></TxGoodCodeDayReport>
      </el-tab-pane>
      <el-tab-pane label="ID日报" name="first4" :lazy="true" style="height: 100%" v-if="checkPermission('TxIdDayReport')">
        <TxIdDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName" ref="TxIdDayReport"
          style="height: 100%"></TxIdDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺日报" name="first5" :lazy="true" style="height: 100%"
        v-if="checkPermission('TxShopDayReport')">
        <TxShopDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="TxShopDayReport" style="height: 100%"></TxShopDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺SKU日报" name="first7" :lazy="true" style="height: 100%"
        v-if="checkPermission('TxCommodityDayReport')">
        <TxCommodityDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="TxCommodityDayReport" style="height: 100%"></TxCommodityDayReport>
      </el-tab-pane>
      <el-tab-pane label="明细日报" name="first6" :lazy="true" style="height: 100%"
        v-if="checkPermission('TxDetailDayReport')">
        <TxDetailDayReport @ChangeActiveName="ChangeActiveName" @ChangeActiveName2="ChangeActiveName2"
          ref="TxDetailDayReport" style="height: 100%"></TxDetailDayReport>
      </el-tab-pane>
      <el-tab-pane label="出仓负利润ID订单明细" name="first8" :lazy="true" style="height: 100%"
        v-if="checkPermission('TxOutgoingprofitIDorderdetail')">
        <TxOutgoingprofitIDorderdetail @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="TxOutgoingprofitIDorderdetail" style="height: 100%"></TxOutgoingprofitIDorderdetail>
      </el-tab-pane>

      <el-tab-pane label="SKUS日报" name="first9" :lazy="true" style="height: 100%"
        v-if="checkPermission('TxOrderDayReport')">
        <TxSkusDayReport @ChangeActiveName2="ChangeActiveName2" :orderNoInner="orderNoInner" ref="TxSkusDayReport"
          style="height: 100%"></TxSkusDayReport>
      </el-tab-pane>

    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import productReportTx from "./productReportTx.vue";
import TxOrderDayReport from "./TxOrderDayReport.vue";
import TxSkusDayReport from "./TxSkusDayReport.vue";
import TxGoodCodeDayReport from "./TxGoodCodeDayReport.vue";
import TxIdDayReport from "./TxIdDayReport.vue";
import TxCommodityDayReport from "./TxCommodityDayReport.vue";
import TxOutgoingprofitIDorderdetail from "./TxOutgoingprofitIDorderdetail.vue";
import TxShopDayReport from "./TxShopDayReport.vue";
import TxDetailDayReport from "./TxDetailDayReport.vue";
import middlevue from "@/store/middle.js"
export default {
  props: ['orderNo', 'No'],
  name: "productReportTxIndex",
  components: {
    MyContainer, productReportTx, TxOrderDayReport, TxSkusDayReport, TxGoodCodeDayReport, TxIdDayReport, TxShopDayReport, TxDetailDayReport, TxCommodityDayReport, TxOutgoingprofitIDorderdetail
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
      orderNoInner: null,
    };
  },
  async mounted() {
    middlevue.$on('toLinkTxDetailDayReport', (data) => {
      if (data.isTrue && data.plat == 'tm') {
        this.activeName = 'first6';
        setTimeout(() => {
          middlevue.$emit('toLinkTxDetailDayReportQuery', data)
        }, 500);

      } else {
        this.activeName = 'first8';
        setTimeout(() => {
          middlevue.$emit('toLinkTxOutgoingprofitIDorderdetailQuery', data)
        }, 500);
      }
    })
  },
  //销毁事件
  beforeDestroy() {
    middlevue.$off('toLinkTxDetailDayReport')
  },
  methods: {
    ChangeActiveName(activeName) {
      this.activeName = 'first2';
      this.$refs.TxOrderDayReport.TxGoodCodeDayReportArgument(activeName)
    },
    ChangeActiveName2(activeName, No, Time) {
      this.activeName = 'first6';
      this.$refs.TxDetailDayReport.TxDetailDayReportArgument(activeName, No, Time)
    }
  },
};
</script>

<style lang="scss" scoped></style>
