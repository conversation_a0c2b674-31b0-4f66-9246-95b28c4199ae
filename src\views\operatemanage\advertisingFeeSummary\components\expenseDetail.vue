<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 220px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" :clearable="false" @change="changeTime($event, 1)">
        </el-date-picker>
        <el-date-picker style="width: 275px;margin-right: 5px;" v-model="shelvestimeranges" type="datetimerange"
          format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" range-separator="至" start-placeholder="上架开始时间"
          end-placeholder="上架结束时间" :picker-options="pickerOptions" @change="changeTime($event, 2)">
        </el-date-picker>
        <!-- <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss"
          style="width: 260px;" :isOtherPicker="true" />
        <dateRange :startDate.sync="ListInfo.onstartTime" :endDate.sync="ListInfo.onendTime" class="publicCss"
          style="width: 260px;" :isOtherPicker="true" /> -->
        <el-select filterable v-model="ListInfo.productName" placeholder="场景" class="publicCss" clearable>
          <el-option v-for="item in scenarioList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select filterable v-model="ListInfo.groupId" placeholder="运营组长" class="publicCss" clearable>
          <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key" />
        </el-select>
        <el-select filterable v-model="ListInfo.operateSpecialUserId" placeholder="运营专员" clearable class="publicCss">
          <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
        </el-select>
        <el-select filterable v-model="ListInfo.userId" placeholder="运营助理" clearable class="publicCss">
          <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
        </el-select>
        <el-input v-model.trim="ListInfo.proCode" placeholder="商品ID" maxlength="50" clearable class="publicCss" />
        <number-range :min.sync="ListInfo.returnOnInvestmentMin" :max.sync="ListInfo.returnOnInvestmentMax"
          min-label="投产值-最小值" max-label="投产值 - 最大值" class="publicCss" />
        <number-range :min.sync="ListInfo.useMoneyMin" :max.sync="ListInfo.useMoneyMax" min-label="花费-最小值"
          max-label="花费 - 最大值" class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <!-- <el-button type="primary" @click="startImport">导入</el-button> -->
        <el-button type="primary" @click="exportProps">导出</el-button>
        <el-button type="primary" @click="notifications" v-if="checkPermission('OneClickNotification')">一键通知</el-button>
        <el-button type="primary" @click="oticeMethod"
          v-if="checkPermission('NotificationConfiguration')">通知配置</el-button>
      </div>
    </template>
    <vxetablebase :id="'productReportTx202302031421'" :tablekey="'productReportTx202302031421'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData' border
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      @select="checkboxRangeEnd" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="通知配置" :visible.sync="noticeVisible" width="30%" v-dialogDrag>
      <div style="padding: 30px 5px 0 5px;">
        <el-form :model="ruleForm" :rules="editrules" ref="ruleForm" label-width="100px" class="demo-ruleForm"
          v-if="noticeVisible">
          <el-form-item label="有花费无成交" :label-width="'125px'" prop="maxUseMoney">
            <number-range :min.sync="ruleForm.minUseMoney"   :max.sync="ruleForm.maxUseMoney" min-label="最小值"
              max-label="最大值" class="editCss" />
              <el-switch
              style="display: block"
              v-model="ruleForm.onUseMoney"
              active-color="#13ce66"
              inactive-color="#ff4949"
              active-text="开启"
              inactive-text="关闭" width='50px'>
            </el-switch>
          </el-form-item>
          <el-form-item label="投产介于" :label-width="'125px'" prop="maxReturnOnInvestment">
            <number-range :min.sync="ruleForm.minReturnOnInvestment" :max.sync="ruleForm.maxReturnOnInvestment"
              min-label="最小值" max-label="最大值" class="editCss" />
              <el-switch
              style="display: block"
              v-model="ruleForm.onReturnOnInvestment"
              active-color="#13ce66"
              inactive-color="#ff4949"
              active-text="开启"
              inactive-text="关闭">
            </el-switch>
          </el-form-item>
          <el-form-item label="成交成本介于" :label-width="'125px'" prop="maxTotalTransactionCost">
            <number-range :min.sync="ruleForm.minTotalTransactionCost" :max.sync="ruleForm.maxTotalTransactionCost"
              min-label="最小值" max-label="最大值" class="editCss" />
              <el-switch
              style="display: block"
              v-model="ruleForm.onTotalTransactionCost"
              active-color="#13ce66"
              inactive-color="#ff4949"
              active-text="开启"
              inactive-text="关闭">
            </el-switch>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="noticeVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSingleSave">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="orderAnalysisChar.visible" width="80%" v-dialogDrag>
      <div style="height: 650px;" v-loading="orderLoading">
        <buschar v-if="orderAnalysisChar.visible" ref="detailtrendchartref" :analysisData="orderAnalysisChar.data">
        </buschar>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getDirectorList, getDirectorGroupList, getDirectorGroupList2 } from '@/api/operatemanage/base/shop'
import { getProductAdv_TXList, exportProductAdv_TX, getProductAdv_TXChartList, addNotificationConfigurationAdv_TX, getNotificationConfigurationAdv_TX, notificationConfigurationAdv_TX } from '@/api/bookkeeper/reportdayV2'
import { pickerOptions } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import dayjs from 'dayjs'
const scenarioList = [
  '多目标直投', '关键词推广', '货品运营', '全站推广', '人群推广'
]
const tableCols = [
  { width: '100', istrue: true, width: '60', type: "checkbox" },
  { width: '100', sortable: 'custom', align: 'center', prop: 'useDate', label: '日期', },
  { width: '100', sortable: 'custom', align: 'center', prop: 'groupName', label: '组长', },
  { width: '100', sortable: 'custom', align: 'center', prop: 'operateSpecialUserId', label: '运营专员', formatter: (row) => row.operateSpecialUserName },
  { width: '100', sortable: 'custom', align: 'center', prop: 'userId', label: '运营助理', formatter: (row) => row.userName },
  { width: '100', sortable: 'custom', align: 'center', prop: 'productName', label: '场景名称', },
  { width: '100', sortable: 'custom', align: 'center', prop: 'productName2', label: '原场景名字', },
  { width: '100', sortable: 'custom', align: 'center', prop: 'planID', label: '计划ID', },
  { width: '140', sortable: 'custom', align: 'center', prop: 'planName', label: '计划名称', },
  { width: '100', sortable: 'custom', align: 'center', prop: 'proCode', label: '商品ID', },
  { width: '140', sortable: 'custom', align: 'center', prop: 'proCodeName', label: '商品名称', },
  { width: '160', sortable: 'custom', align: 'center', prop: 'onTime', label: '上架时间', },
  { width: '100', sortable: 'custom', align: 'center', prop: 'onTimeCount', label: '上架天数', },
  { width: '100', type: 'click', handle: (that, row) => that.openChart(row), sortable: 'custom', align: 'center', prop: 'impressions', label: '展现量', },
  { width: '100', type: 'click', handle: (that, row) => that.openChart(row), sortable: 'custom', align: 'center', prop: 'clicks', label: '点击量', },
  { width: '100', type: 'click', handle: (that, row) => that.openChart(row), sortable: 'custom', align: 'center', prop: 'useMoney', label: '花费', },
  { width: '100', type: 'click', handle: (that, row) => that.openChart(row), sortable: 'custom', align: 'center', prop: 'clickThroughRate', label: '点击率', tipmesg: '点击量/展现量', formatter: (row) => !row.clickThroughRate ? " " : (row.clickThroughRate * 100).toFixed(2) + "%", },
  { width: '100', type: 'click', handle: (that, row) => that.openChart(row), sortable: 'custom', align: 'center', prop: 'averageCostPerClick', label: '平均点击花费', tipmesg: '花费/点击量', },
  { width: '100', type: 'click', handle: (that, row) => that.openChart(row), sortable: 'custom', align: 'center', prop: 'costPerThousandImpressions', label: '千次展现花费', tipmesg: '(花费/展现量)*1000', },
  { width: '100', type: 'click', handle: (that, row) => that.openChart(row), sortable: 'custom', align: 'center', prop: 'directTransactionAmount', label: '直接成交金额', },
  { width: '100', type: 'click', handle: (that, row) => that.openChart(row), sortable: 'custom', align: 'center', prop: 'indirectTransactionAmount', label: '间接成交金额', },
  { width: '100', type: 'click', handle: (that, row) => that.openChart(row), sortable: 'custom', align: 'center', prop: 'transactionAmount', label: '总成交金额', },
  { width: '100', type: 'click', handle: (that, row) => that.openChart(row), sortable: 'custom', align: 'center', prop: 'totalTransactionCount', label: '总成交笔数', },
  { width: '100', type: 'click', handle: (that, row) => that.openChart(row), sortable: 'custom', align: 'center', prop: 'directTransactionCount', label: '直接成交笔数', },
  { width: '100', sortable: 'custom', align: 'center', prop: 'indirectTransactionCount', label: '间接成交笔数', },
  { width: '100', type: 'click', handle: (that, row) => that.openChart(row), sortable: 'custom', align: 'center', prop: 'clickConversionRate', label: '点击转化率', tipmesg: '总成交笔数/点击量', formatter: (row) => !row.clickConversionRate ? " " : (row.clickConversionRate * 100).toFixed(2) + "%", },
  { width: '100', type: 'click', handle: (that, row) => that.openChart(row), sortable: 'custom', align: 'center', prop: 'returnOnInvestment', label: '投入产出比', tipmesg: '总成交金额/花费', formatter: (row) => !row.returnOnInvestment ? " " : (row.returnOnInvestment ).toFixed(2) , },
  { width: '100', type: 'click', handle: (that, row) => that.openChart(row), sortable: 'custom', align: 'center', prop: 'totalTransactionCost', label: '总成交成本', tipmesg: '花费/成交笔数', formatter: (row) => !row.totalTransactionCost ? " " : (row.totalTransactionCost), },
  { width: '100', type: 'click', handle: (that, row) => that.openChart(row), sortable: 'custom', align: 'center', prop: 'totalCartCount', label: '总购物车数', },
  { width: '100', sortable: 'custom', align: 'center', prop: 'directCartCount', label: '直接购物车数', },
  { width: '100', sortable: 'custom', align: 'center', prop: 'indirectCartCount', label: '间接购物车数', },
  { width: '100', sortable: 'custom', align: 'center', prop: 'addToCartRate', label: '加购率', tipmesg: '总购物车数/点击量', formatter: (row) => !row.addToCartRate ? " " : (row.addToCartRate * 100).toFixed(2) + "%", },
  { width: '100', sortable: 'custom', align: 'center', prop: 'addToCartCost', label: '加购成本', tipmesg: '花费/总购物车数', formatter: (row) => !row.addToCartCost ? " " : (row.addToCartCost), },
  { width: '100', sortable: 'custom', align: 'center', prop: 'newCustomerTransactionCount', label: '成交新客数', },
  { width: '100', sortable: 'custom', align: 'center', prop: 'newCustomerTransactionRatio', label: '成交新客占比', tipmesg: '成交新客数/成交人数', formatter: (row) => !row.newCustomerTransactionRatio ? " " : (row.newCustomerTransactionRatio * 100).toFixed(2) + "%", },
  { width: '100', sortable: 'custom', align: 'center', prop: 'transactionCustomerCount', label: '成交人数', },
  { width: '130', sortable: 'custom', align: 'center', prop: 'organicTrafficConversionAmount', label: '自然流量转化金额', },
  { width: '120', sortable: 'custom', align: 'center', prop: 'organicTrafficImpressions', label: '自然流量曝光量', },
  { width: '100', sortable: 'custom', align: 'center', prop: 'saleAmount', label: '销售金额', },
  { width: '100', sortable: 'custom', align: 'center', prop: 'profit3', label: '利润', },
  { width: '130', sortable: 'custom', align: 'center', prop: 'payIdAdvRate', label: '付费链接广告占比', tipmesg: '花费/销售额', formatter: (row) => !row.payIdAdvRate ? " " : (row.payIdAdvRate) + "%", },
]
export default {
  name: "scanCodePage",
  components: {
    MyContainer, vxetablebase, numberRange, dateRange, buschar
  },
  data() {
    return {
      orderLoading: false,
      orderAnalysisChar: { visible: false, title: "", data: {} },
      editrules: {
        maxUseMoney: [
          { required: true, message: "请输入", trigger: "blur" },
        ],
        maxReturnOnInvestment: [
          { required: true, message: "请输入", trigger: "blur" },
        ],
        maxTotalTransactionCost: [
          { required: true, message: "请输入", trigger: "blur" },
        ],
      },
      ruleForm: {
        maxUseMoney: undefined,
        minUseMoney: undefined,
        maxReturnOnInvestment: undefined,
        minReturnOnInvestment: undefined,
        maxTotalTransactionCost: undefined,
        minTotalTransactionCost: undefined,
        onUseMoney: undefined,
        onReturnOnInvestment: undefined,
        onTotalTransactionCost: undefined,
      },
      noticeVisible: false,
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      superviselist: [],//运营主管列表
      directorGroupList: [],//运营组长列表
      directorList: [],//运营专员列表
      scenarioList,
      checkboxData: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'useMoney',
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        onstartTime: null,//上架开始时间
        onendTime: null,//上架结束时间
        productName: null,//场景
        superviseId: null,//运营主管
        groupId: null,//运营组长
        operateSpecialUserId: null,//运营专员
        userId: null,//运营助理
        returnOnInvestmentMin: null,//投产值-最小值
        returnOnInvestmentMax: null,//投产值 - 最大值
        useMoneyMin: null,//花费-最小值
        useMoneyMax: null,//花费 - 最大值
        proCode: null,//商品ID
      },
      timeRanges: [],
      shelvestimeranges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.init()
    await this.getList()
  },
  methods: {
    async openChart(row) {
      this.loading = true
      const { data, success } = await getProductAdv_TXChartList({ proCode: row.proCode, productName: row.productName })
      this.loading = false
      if (!success) return
      data.series.map((item) => {
        item.itemStyle = {
          normal: {
            label: {
              show: true,
              position: "top",
              textStyle: {
                fontSize: 14,
              },
            },
          },
        };
        item.emphasis = {
          focus: "series",
        };
        item.smooth = false;
      });
      this.orderAnalysisChar.visible = true;
      this.orderAnalysisChar.data = data;
    },
    onSingleSave() {
      const fieldsToCheck = [
        'maxUseMoney', 'minUseMoney',
        'maxReturnOnInvestment', 'minReturnOnInvestment',
        'maxTotalTransactionCost', 'minTotalTransactionCost',
      ];
      let verify = fieldsToCheck.some(field => !this.ruleForm[field]);
      if (verify) {
        this.$message.error('请填写完整信息');
        return;
      }
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          const { success } = await addNotificationConfigurationAdv_TX(this.ruleForm)
          if (success) {
            this.$message.success('操作成功')
            this.noticeVisible = false
            this.getList()
          } else {
            this.$message.error('操作失败')
          }
        }
      })
    },
    onCleardataMethod() {
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
        this.$refs.ruleForm.clearValidate();
      });
      this.ruleForm = {
        maxUseMoney: undefined,
        minUseMoney: undefined,
        maxReturnOnInvestment: undefined,
        minReturnOnInvestment: undefined,
        maxTotalTransactionCost: undefined,
        minTotalTransactionCost: undefined,
        onUseMoney: undefined,
        onReturnOnInvestment: undefined,
        onTotalTransactionCost: undefined,
      }
    },
    async oticeMethod() {
      this.loading = true
      const { data, success } = await getNotificationConfigurationAdv_TX({})
      this.loading = false
      if (!success) return
      let a = data[0]
      this.onCleardataMethod()
      this.ruleForm = { ...a }
      this.noticeVisible = true
    },
    notifications() {
      if (this.checkboxData.length == 0) {
        this.$message({ message: "请先选择数据", type: "warning" });
        return
      }
      this.$confirm('是否一键通知?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let ids = this.checkboxData.map(item => item.id);
        const { success } = await notificationConfigurationAdv_TX({ ids, startTime: this.ListInfo.startTime, endTime: this.ListInfo.endTime })
        if (!success) return
        this.$message({
          type: 'success',
          message: '一键通知成功!'
        });
        this.getList()
      }).catch(() => {
      });
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importTianMaoComplainAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async init() {
      //获取运营主管列表
      // this.superviselist = [];
      // let res2 = await getDirectorGroupList2();
      // console.log(res2, 'res2');
      // let sIds = res2.data?.filter(f => f.superviseId > 0).map(m => m.superviseId);
      // let sIds2 = [...new Set(sIds)];
      // if (sIds2.length > 0) {
      //   this.superviselist = sIds2
      //     .filter(id => res2.data.some(x => x.id === id))
      //     .map(id => res2.data.find(x => x.id === id))
      //     .map(cur => ({ value: cur.id, label: cur.userName }));
      // }
      //获取运营专员列表
      const res1 = await getDirectorList({})
      this.directorList = res1.data || [];
      this.directorList.push({"key":"0","value":"空"});
      //获取运营组长列表
      const res3 = await getDirectorGroupList({})
      this.directorGroupList = res3.data || [];
    },
    checkboxRangeEnd(val) {
      this.checkboxData = val
    },
    async changeTime(e, val) {
      if (val == 1) {
        this.ListInfo.startTime = e ? e[0] : null
        this.ListInfo.endTime = e ? e[1] : null
      } else {
        this.ListInfo.onstartTime = e ? e[0] : null
        this.ListInfo.onendTime = e ? e[1] : null
      }
    },
    async exportProps() {
      this.loading = true
      const { data } = await exportProductAdv_TX(this.ListInfo)
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '汇总数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      const { data, success } = await getProductAdv_TXList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.useDate = dayjs(item.useDate).format('YYYY-MM-DD')
        })
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 130px;
    margin-right: 3px;
  }
}

.editCss {
  width: 90%;
  margin-right: 5px;
}

::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>
