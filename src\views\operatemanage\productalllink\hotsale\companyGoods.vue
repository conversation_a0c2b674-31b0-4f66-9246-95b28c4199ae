<template>
    <my-container>
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="系列编码" name="tab1" style="height: 100%;">
                <companyStyleCode ref="companyStyleCode" @toGoodCode="toGoodCode" />
            </el-tab-pane>
            <el-tab-pane label="商品编码" name="tab2" style="height: 100%;">
                <companyGoodCode ref="companyGoodCode" />
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import companyGoodCode from './companyGoodCode.vue'
import companyStyleCode from './companyStyleCode.vue'
export default {
    name: "companyGoods",
    components: {
        MyContainer, companyGoodCode, companyStyleCode
    },
    data() {
        return {
            activeName: 'tab1',
        };
    },
    methods: {
        toGoodCode() {
            this.activeName = 'tab2'
        }
    }
}

</script>
