<template>
    <my-container>
        <!-- 查询过滤条件 -->
        <template #header>
            <el-button-group>
                <el-button style="padding: 0; margin-left: 7px; margin-bottom: 10px;">
                    <el-select v-model.trim="styleCode" placeholder="款式编码" multiple collapse-tags clearable filterable
                        reserve-keyword remote :remote-method="remoteMethod" style="width: 200px" :loading="searchloading"
                        maxlength="50">
                        <el-option v-for="item in styleCodeList" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-button>
                <!-- 商品编码模糊查询使用" 编码 + * "，支持单条模糊查询 -->
                <el-tooltip class="item" effect="dark" content="默认为精确匹配在开始或者结尾输入*进行模糊匹配" placement="top">
                    <el-button style="padding: 0; margin-left: 7px; margin-bottom: 10px;">
                        <inputYunhan ref="goodsCode" :inputt.sync="filter.goodsCode" v-model="filter.goodsCode"
                            placeholder="商品编码/若输入多条请按回车" :clearable="true" :rows="50" @callback="callbackGoodsCode"
                            title="商品编码"></inputYunhan>
                    </el-button>
                </el-tooltip>
                <el-button style="padding: 0; margin-left: 7px; margin-bottom: 10px;">
                    <el-input v-model.trim="filter.goodsName" placeholder="商品名称" clearable style="width: 200px"
                        maxlength="100"></el-input>
                </el-button>
                <el-button style="padding: 0; margin-left: 7px; margin-bottom: 10px;">
                    <el-select v-model="filter.salesDate" placeholder="销量" clearable style="width: 90px">
                        <el-option label="昨日" :value="0" />
                        <el-option label="7日" :value="7" />
                        <el-option label="15日" :value="15" />
                        <el-option label="30日" :value="30" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0; margin-bottom: 10px;">
                    <el-select v-model="filter.condition" placeholder="条件" clearable style="width:90px">
                        <el-option label="大于等于" :value="0" />
                        <el-option label="小于等于" :value="1" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0; margin-bottom: 10px;">
                    <el-input v-model="filter.sales" type="text" placeholder="请输入数量" maxlength="7" show-word-limit
                        @input="(v) => (filter.sales = v.replace(/^(0+)|[^\d]+/g, ''))" clearable style="width: 120px" />
                </el-button>
                <!-- <el-button @click="showseldialog('yyzu')"></el-button> -->
                <el-button style="padding: 0; margin-left: 7px; margin-bottom: 10px;">
                    <!-- @focus="showseldialog('yyzu')" -->
                    <el-select v-model="groupId" placeholder="运营组" :multiple-limit="100" clearable filterable style="width: 120px"  multiple collapse-tags>
                        <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                    <el-tag @click="showseldialog('yyzu')"><i class="el-icon-sunset"></i></el-tag>
                    <!-- <el-input v-model="groupId" type="text" placeholder="运营组" maxlength="300" @focus="showseldialog('yyzu')"
                     clearable style="width: 100px" /> -->
                </el-button>
                <el-button style="padding: 0; margin-left: 7px; margin-bottom: 10px;">
                    <el-select v-model="getAllProBrandId" placeholder="采购" :multiple-limit="100" clearable filterable style="width: 120px" multiple collapse-tags>
                        <el-option v-for="item in getAllProBrandList" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                    <el-tag @click="showseldialog('caigou')"><i class="el-icon-sunset"></i></el-tag>
                    <!-- <el-input v-model="getAllProBrandListstr" type="text" placeholder="采购" maxlength="300" @focus="showseldialog('caigou')"
                     clearable style="width: 100px" /> -->
                </el-button>
                <el-button style="padding: 0; margin-left: 7px; margin-bottom: 10px;">
                    <el-select v-model="saleSpecialUserName" placeholder="售卖专员" :multiple-limit="100" clearable filterable
                        style="width: 180px"  multiple collapse-tags>
                        <el-option v-for="item in saleSpecialUserNameList" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                    <el-tag @click="showseldialog('smzhuanyuan')"><i class="el-icon-sunset"></i></el-tag>
                    <!-- <el-input v-model="saleSpecialUserName" type="text" placeholder="售卖专员" maxlength="300" @focus="showseldialog('smzhuanyuan')"
                     clearable style="width: 180px" /> -->
                </el-button>
                <el-button style="padding: 0; margin-left: 7px; margin-bottom: 10px;">
                    <el-select v-model="submitUserId" placeholder="编码创建人" :multiple-limit="100"  multiple collapse-tags clearable filterable style="width: 100px" >
                        <el-option v-for="item in submitUserList" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                    <el-tag @click="showseldialog('cjbianmapeo')"><i class="el-icon-sunset"></i></el-tag>
                    <!-- <el-input v-model="submitUserId" type="text" placeholder="编码创建人" maxlength="300"  @focus="showseldialog('cjbianmapeo')"
                     clearable style="width: 100px" /> -->
                </el-button>
                <el-button style="padding: 0; margin-left: 7px; margin-bottom: 10px;">
                    <el-button type="primary" @click="onSearch">搜索</el-button>
                </el-button>
            </el-button-group>
        </template>

        <!-- 数据列显示 -->
        <vxetablebase :id="'goodsFuHuaCang20230831'" :border="true" :algin="'center'" :tablekey="'goodsFuHuaCang20230831'"
            ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn="true"
            :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols'
            :tableHandles='tableHandles' :loading="listLoading" style="width:100%; height:100%; margin: 0"
            :xgt="9999">
        </vxetablebase>

        <!-- 分页 -->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

        <!-- 操作 -->
        <el-dialog title="操作" :visible.sync="ctrlDialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
            <span>初始回车可多行输入：</span>
            <inputYunhan ref="saleSpecialUser" :inputt.sync="ctrlFilter.saleSpecialUser"
                v-model="ctrlFilter.saleSpecialUser" placeholder="请输入售卖专员" clearable title="售卖专员" style="margin-top: 10px;">
            </inputYunhan>
            <span slot="footer" class="dialog-footer">
                <el-button @click="ctrlDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="onSave" :loading="ctrlDialogLoading">保 存</el-button>
            </span>
        </el-dialog>

        <!-- 日志 -->
        <el-dialog :title="warehouseEntry.title" :visible.sync="warehouseEntry.dialogVisibleSyj" width="50%"
            element-loading-text="拼命加载中" v-dialogDrag v-loading="warehouseEntry.dialogLoadingSyj"
            :close-on-click-modal="false">
            <template>
                <el-tabs v-model="logCtrl">
                    <el-tab-pane label="库存进出明细流水" key="tab1" name="tab1">
                        <my-container>
                            <template #header>
                                <span>业务类型：</span>
                                <el-select v-model="logFilter.businessType" placeholder="业务类型" clearable
                                    style="width: 120px">
                                    <el-option label="调拨出" :value="0" />
                                    <el-option label="调拨入" :value="1" />
                                </el-select>
                                <el-button type="primary" @click="getLogListSearch"
                                    style="margin-left: 50px;">查询</el-button>
                            </template>
                            <el-main style="height:440px;">
                                <ces-table1 ref="logTable" :that='that' :isIndex='true' :hasexpand='false'
                                    style="height: 400px;" :tableData='logList' :isSelection="false"
                                    @sortchange='sortchangeLog' :tableCols='tableColslog' :isSelectColumn='true'
                                    :customRowStyle="customRowStyle" :selectColumnHeight="'0px'" :isBorder="false"
                                    :loading="logListLoading">
                                </ces-table1>
                            </el-main>
                            <template #footer>
                                <my-pagination ref="pagerlog" :total="totalLog" @get-page="getLogList" />
                            </template>
                        </my-container>
                    </el-tab-pane>
                    <el-tab-pane label="操作日志" key="tab2" name="tab2">
                        <my-container>
                            <el-main style="height:500px;">
                                <ces-table1 ref="ctrlTable" :that='that' :isIndex='true' :hasexpand='false'
                                    style="height: 460px;" :tableData='ctrlList' :isSelection="false"
                                    :tableCols='tableColsCtrl' :isSelectColumn='true' :customRowStyle="customRowStyle"
                                    :selectColumnHeight="'0px'" :isBorder="false" :loading="ctrlListLoading">
                                </ces-table1>
                            </el-main>
                        </my-container>
                    </el-tab-pane>
                </el-tabs>
            </template>

            <span slot="footer" class="dialog-footer">
                <el-button @click="warehouseEntry.dialogVisibleSyj = false">关 闭</el-button>
            </span>
        </el-dialog>


        <el-dialog
        :title="seltitle"
        :visible.sync="seldialogVisible"
        width="60%"
        v-dialogDrag
        >
        <el-row>
            <el-col :span="12">
                <el-checkbox v-model="allsel" @change="allchange">全选</el-checkbox>
                <el-checkbox v-model="fansel" @change="fanchang">反选</el-checkbox>
            </el-col>
            <el-col :span="12">
                <el-row type="flex" class="row-bg" justify="end">
                    <el-select v-model="checkList" placeholder="请输入名称"  clearable filterable multiple
                        style="width: 200px;" @change="selchangename" collapse-tags>
                        <el-option v-for="(item,i) in selnamelist" :key="i" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-row>
            </el-col>
        </el-row>

        <div style="max-height: 500px; overflow-y: auto; ">
            <el-checkbox-group v-model="checkList" @change="groupchange">
                <el-row type="flex" class="row-bg" style="flex-wrap: wrap; white-space: normal;">
                    <el-col :span="3" v-for="(item,i) in selnamelist" :key="i" style="margin: 10px 40px 0 0;">
                        <el-checkbox :label="item.value" :value="item.value">{{ item.label }}</el-checkbox>
                    </el-col>
                </el-row>
            </el-checkbox-group>
        </div>


        <span slot="footer" class="dialog-footer">
            <el-button @click="seldialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="issubmitsel">确 定</el-button>
        </span>
        </el-dialog>

    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import cesTable1 from "@/components/Table/table.vue";

import {
    GetAllGoodsDetailList, GetPageFuHuaCangList, GetAllSubmitUserList, GetLogAllocateDetailList,
    SaveOpSpecialUser, GetCtrlUserLog, GetSpecialUser, getAllProBrand
} from '@/api/inventory/warehouse';
import { getDirectorGroupList } from '@/api/operatemanage/base/shop';

const tableCols = [
    { istrue: true, prop: 'warehouse', label: '仓储方', sortable: false, width: '120', formatter: (row) => row.warehouse == 12532813 ? "【义乌圆通孵化仓】" : row.warehouse },
    { istrue: true, prop: 'styleCode', label: '款式编码', sortable: false, width: '120' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: false, width: '120' },
    { istrue: true, prop: 'goodsName', label: '商品名称', sortable: false, width: '120' },
    { istrue: true, prop: 'sellStock', label: '主仓实际库存', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'cost', label: '成本价', sortable: 'custom', width: '100', formatter: (row) => !row.cost ? "" : row.cost.toFixed(2) },
    { istrue: true, prop: 'occupiedAmount', label: '占用金额', sortable: false, width: '100' },
    { istrue: true, prop: 'salesYesterday', label: '昨日销量', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'salesDay7', label: '7日销量', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'salesDay15', label: '15日销量', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'salesDay30', label: '30日销量', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'groupId', label: '运营组', sortable: false, width: '120', formatter: (row) => row.groupId == 0 ? " " : row.groupName },
    { istrue: true, prop: 'brandName', label: '采购', sortable: false, width: '120', },
    { istrue: true, prop: 'operateSpecialUserName', label: '售卖专员', sortable: false, width: '120' },
    { istrue: true, prop: 'submitUserName', label: '编码创建人', sortable: false, width: '120' },
    {
        istrue: true, type: "button", label: '', align: 'center', sortable: false, width: "100", fixed: "right",
        btnList: [{ label: "操作", handle: (that, row) => that.onCtrl(row) },
        { label: "日志", handle: (that, row) => that.onSearchLogFirst(row) }]
    }
];
const tableColslog = [
    { istrue: true, prop: 'type', align: 'center', label: '业务类型', sortable: "custom", width: '140' },
    { istrue: true, prop: 'io_date', align: 'center', label: '日期', sortable: "custom", width: '140' },
    { istrue: true, prop: 'qty', align: 'center', label: '库存增减数', sortable: "custom", width: '100', formatter: (row) => row.inventoryIcreaseOrDecrease },
    {
        istrue: true, prop: 'wms_co_id', align: 'center', label: '仓库信息', sortable: "custom",
        formatter: (row) => row.type == "调拨出" ? "调入仓：" + row.warehouseInfo : "调出仓：" + row.warehouseInfo
    },
];
const tableColsCtrl = [
    { istrue: true, prop: 'beforeModifiedName', align: 'center', label: '售卖专员修改内容（前）' },
    { istrue: true, prop: 'afterModifiedName', align: 'center', label: '售卖专员修改内容（后）' },
    { istrue: true, prop: 'modifiedName', align: 'center', label: '操作人', width: '140' },
    { istrue: true, prop: 'modifiedDate', align: 'center', label: '日期', width: '200' },
];
const tableHandles = [];

export default {
    components: { MyContainer, vxetablebase, inputYunhan, cesTable1 },
    data() {
        return {
            that: this,
            input: '',
            seldialogVisible: false,
            checked: true,
            checkList: [],
            selnamelist: [],
            truelabel:[],
            falselabel: [],
            noselist: [],
            // getAllProBrandList: [],
            fansel: false,
            allsel: false,
            nowmoudle: '',
            groupId: [],
            saleSpecialUserName: [],
            submitUserId: [],
            getAllProBrandList: [],
            getAllProBrandId: [],

            radio: '',
            seltitle: '',
            styleCode: "",
            searchloading: false,
            styleCodeList: [],
            groupList: [],
            saleSpecialUserNameList: [],
            submitUserList: [],
            filter: {
                styleCode: null,
                goodsCode: null,
                goodsName: null,
                salesDate: null,
                condition: null,
                sales: null,
                groupId: null,
                saleSpecialUserName: null,
                submitUserId: null,
                getAllProBrandId: null
            },
            total: 0,
            pager: {},
            datalist: [],
            summaryarry: {},
            tableCols: tableCols,
            tableHandles: tableHandles,
            listLoading: false,
            sels: [],
            selids: [],
            selGoodsCode: null,
            //操作弹窗
            ctrlDialogVisible: false,
            ctrlDialogLoading: false,
            //日志弹窗
            logCtrl: "tab1",
            warehouseEntry: {
                title: "", dialogVisibleSyj: false,
            },
            //调拨出调拨入
            logFilter: {
                //status:"Confirmed",
                businessType: null,
            },
            logListLoading: false,
            logList: [],
            totalLog: 0,
            tableColslog: tableColslog,
            logPager: { OrderBy: "io_date", IsAsc: false },
            //日志
            ctrlFilter: {
                saleSpecialUser: null,
            },
            tableColsCtrl: tableColsCtrl,
            ctrlListLoading: false,
            ctrlList: [],
            totalCtrl: 0,
            handleMousedown: -1,
        };
    },

    async mounted() {
        await this.setGroupAndSubmitUserSelect();
        await this.onSearch();
    },

    methods: {
        issubmitsel(){
            let e = this.nowmoudle;

            if(e=='yyzu'){//运营组
                this.groupId = this.checkList;
            }else if(e == 'caigou'){//采购
                this.getAllProBrandId = this.checkList;
            }else if(e == 'smzhuanyuan'){//售卖专员
                this.saleSpecialUserName = this.checkList;
            }else if(e == 'cjbianmapeo'){//编码创建人
                this.submitUserId = this.checkList;
            }
            this.seldialogVisible = false;
        },
        selchangename(e){
        },

        filterArr(arr1, arr2) {
            var uid1=[]
            var uid2=[]
            var arr = []
            arr1.map((item)=>{
                arr.push(item.value)
            })
            const newArr = arr.filter(item => {
                return !(arr2.includes(item));
            });
            return newArr;
        },
        groupchange(e){
        },
        danselchange(){
            var newarr = [];
            this.selnamelist.map((item)=>{
                newarr.push(item.value)
            })

            if(this.allsel){
                this.checkList = this.allsel?newarr:[];
            }else if(this.fansel){
                this.checkList = this.filterArr(this.selnamelist,this.checkList)
            }

        },
        fanchang(e){
            this.checkList = this.filterArr(this.selnamelist,this.checkList);
            this.allsel = false;
        },
        allchange(e){
            var newarr = [];
            this.selnamelist.map((item)=>{
                newarr.push(item.value)
            });
            this.fansel = false;
            this.checkList = this.allsel?newarr:[];
        },
        showseldialog(e){
            this.nowmoudle = e;
            this.selnamelist = [];
            this.checkList = [];
            if(e=='yyzu'){//运营组
                this.seltitle = '运营组'
                this.selnamelist = this.groupList;
                if(this.groupId.length>0){
                    this.checkList = this.groupId;
                }
            }else if(e == 'caigou'){//采购
                this.seltitle = '采购'
                this.selnamelist = this.getAllProBrandList;
                if(this.getAllProBrandId.length>0){
                    this.checkList = this.getAllProBrandId;
                }
            }else if(e == 'smzhuanyuan'){//售卖专员
                this.seltitle = '售卖专员'
                this.selnamelist = this.saleSpecialUserNameList;
                if(this.saleSpecialUserName.length>0){
                    this.checkList = this.saleSpecialUserName;
                }
            }else if(e == 'cjbianmapeo'){//编码创建人
                this.seltitle = '编码创建人'
                this.selnamelist = this.submitUserList;
                if(this.submitUserId.length>0){
                    this.checkList = this.submitUserId;
                }
            }
            this.seldialogVisible = true;
        },
        async setGroupAndSubmitUserSelect() {
            //获取运营组
            const res = await getDirectorGroupList();
            this.groupList = res.data.map(item => { return { value: item.key, label: item.value }; });
            //获取建编码提交人
            const resSubmit = await GetAllSubmitUserList();
            this.submitUserList = resSubmit.data.map(item => { return { value: item.submitUserId, label: item.submitUserName }; });
            //取售卖专员
            const resSpecialUser = await GetSpecialUser();
            this.saleSpecialUserNameList = resSpecialUser.data.map(item => { return { value: item, label: item }; });

            const getAllProBrandt = await getAllProBrand();
            this.getAllProBrandList = getAllProBrandt.data.map(item => { return { value: item.key, label: item.value }; });
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        async getList() {
            this.filter.styleCode = this.styleCode.join(",");
            let pager = this.$refs.pager.getPager();
            this.filter.groupIds = this.groupId.join();
            this.filter.brandIds = this.getAllProBrandId.join();
            this.filter.saleSpecialUserNames = this.saleSpecialUserName.join();
            this.filter.submitUserIds = this.submitUserId.join();


            const params = { ...pager, ...this.pager, ...this.filter };
            const res = await GetPageFuHuaCangList(params);
            this.total = res.data?.total;
            this.datalist = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        //系列编码远程搜索
        async remoteMethod(query) {
            if (query !== '') {
                this.searchloading == true;
                this.styleCodeList = [];
                setTimeout(async () => {
                    const res = await GetAllGoodsDetailList({ currentPage: 1, pageSize: 50, styleCode: query });
                    this.searchloading = false
                    res?.data?.forEach(f => {
                        this.styleCodeList.push({ value: f.styleCode, label: f.styleCode })
                    });
                }, 200)
            }
            else {
                this.styleCodeList = []
            }
        },
        //排序
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        //首次点击日志查询
        async onSearchLogFirst(row) {
            this.selGoodsCode = row.goodsCode
            if (!this.selGoodsCode) {
                this.$message({ type: 'error', message: '数据加载失败，请刷新后重试!' });
                return;
            }
            this.logFilter.businessType = null;
            this.warehouseEntry.title = "商品编码" + this.selGoodsCode;
            this.warehouseEntry.dialogVisibleSyj = true;
            this.$nextTick(() => {
                //加载调拨
                this.getLogListSearch();
                //加载操作日志
                this.getCtrlListSearch();
            });
        },

        async getLogListSearch() {
            this.$refs.pagerlog.setPage(1);
            await this.getLogList();
        },
        //查询结果
        async getLogList() {
            let pager = this.$refs.pagerlog.getPager();
            const params = { ...pager, ...this.logPager, ...this.logFilter, ...{ GoodsCode: this.selGoodsCode } };
            console.log(params);
            this.logListLoading = true;
            const { data } = await GetLogAllocateDetailList(params);
            this.logListLoading = false;
            this.logList = data.list;
            this.totalLog = data.total;
        },
        //排序
        async sortchangeLog(column) {
            if (!column.order)
                this.logPager = { OrderBy: "io_date", IsAsc: false };
            else
                this.logPager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.getLogListSearch();
        },

        async getCtrlListSearch() {
            await this.getCtrlList();
        },
        //查询售卖专员操作日志
        async getCtrlList() {
            const ctrlPager = { OrderBy: "modifiedDate", IsAsc: false };
            const params = { ...ctrlPager, ...{ GoodsCode: this.selGoodsCode } };
            this.ctrlListLoading = true;
            const { data } = await GetCtrlUserLog(params);
            this.ctrlListLoading = false;
            this.ctrlList = data.list;
            this.totalCtrl = data.total;
        },


        //操作弹窗
        async onCtrl(row) {
            this.selGoodsCode = row.goodsCode;
            this.ctrlDialogVisible = true;
            this.$nextTick(() => {
                if (row.operateSpecialUserName) {
                    this.$refs.saleSpecialUser.inputtfuc(row.operateSpecialUserName);
                    this.ctrlFilter.saleSpecialUser = row.operateSpecialUserName;
                }
                else {
                    this.$refs.saleSpecialUser.clear();
                    this.ctrlFilter.saleSpecialUser = "";
                }
            })
        },
        //保存售卖专员操作信息
        async onSave() {
            var modifiedContent = this.ctrlFilter.saleSpecialUser;
            console.log(this.ctrlFilter);
            const params = { modifiedContent, ...{ GoodsCode: this.selGoodsCode } };
            this.ctrlDialogLoading = true;
            await SaveOpSpecialUser(params);
            this.ctrlDialogVisible = false;
            this.ctrlDialogLoading = false;

            var that = this;
            let pager = this.$refs.pager.getPager();
            const params1 = { ...pager, ...this.pager, ...this.filter };
            const res = await GetPageFuHuaCangList(params1);
            that.total = res.data?.total;
            that.datalist = res.data?.list;
            that.summaryarry = res.data?.summary;
            this.ctrlFilter.saleSpecialUser = null;
        },
        async callbackGoodsCode(val) {
            this.filter.goodsCode = val;
        },
        async callbackSaleSpecialUser(val) {
            this.ctrlFilter.saleSpecialUser = val;
        },
        //多选事件
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        customRowStyle(row, index) {
            if (row.row?.isend && row.row.isend == 1) {
                let styleJson = {};
                styleJson.color = "rgb(216 216 216)";
                return styleJson
            } else {
                return null
            }

        },

    }
}
</script>

<style lang="scss" scoped>

</style>
