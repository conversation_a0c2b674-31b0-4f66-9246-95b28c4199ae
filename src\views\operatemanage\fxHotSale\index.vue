<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="ListInfo.yearMonthDay" type="date" placeholder="选择日期" :clearable="false"
                    value-format="yyyy-MM-dd" class="publicCss">
                </el-date-picker>
                <el-input v-model.trim="ListInfo.shopName" placeholder="店铺名称" maxlength="100" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="100" clearable
                    class="publicCss" />
                <el-select v-model="ListInfo.hasSupplyPrice" placeholder="是否有供货价" class="publicCss" clearable>
                    <el-option label="无" :value="1" />
                    <el-option label="有供货价" :value="2" />
                    <el-option label="无供货价" :value="3" />
                </el-select>
                <number-range :min.sync="ListInfo.minSupplyPrice" :max.sync="ListInfo.maxSupplyPrice"
                    min-label="供货价 - 最小值" max-label="供货价 - 最大值" class="publicCss" :precision="4" :minNumber="0.0001"
                    :maxNumber="99999" />
                <number-range :min.sync="ListInfo.minGrowthRate" :max.sync="ListInfo.maxGrowthRate"
                    min-label="增长率 - 最小值" max-label="增长率 - 最大值" class="publicCss" :precision="4" :minNumber="-999999999"
                    :maxNumber="999999999" />
                <el-select v-model="ListInfo.orderBy" placeholder="按店铺" class="publicCss" clearable>
                    <el-option v-for="item in shopOrderBy" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :rowHeight="120"
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false" showSymbol
            @echartsTooltip="echartsTooltip" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
            :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { GetHotSellingDataPage } from '@/api/operatemanage/operateManageFxSite'
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import numberRange from "@/components/number-range/index.vue";
const tableCols = [
    { width: 'auto', align: 'center', prop: 'yearMonthDay', label: '日期', formatter: (row) => row.yearMonthDay ? dayjs(row.yearMonthDay).format('YYYY-MM-DD') : row.yearMonthDay },
    { width: 'auto', align: 'center', prop: 'shopName', label: '店铺名称', },
    { width: 'auto', align: 'center', prop: 'picture', label: '图片', type: 'images' },
    { width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', },
    { width: 'auto', align: 'center', prop: 'supplyPrice', label: '供货价', },
    { width: 'auto', align: 'center', prop: 'days7Sales', label: '近七日销量', },
    { width: 'auto', align: 'center', prop: 'days7SalesGrowthRate', label: '近七日销量增长率', formatter: (row) => row.days7SalesGrowthRate !== null ? row.days7SalesGrowthRate + '%' : row.days7SalesGrowthRate },
    { width: '400', align: 'center', type: 'echarts', prop: 'profit3IncreaseGoOnDays', chartProp: 'analysis', label: '近15日销量', },
]
const shopOrderBy = [
    { label: '按店铺供货价降序', value: '按店铺供货价降序' },
    { label: '按店铺供货价升序', value: '按店铺供货价升序' },
    { label: '按店铺近7日销量降序', value: '按店铺近7日销量降序' },
    { label: '按店铺近7日销量升序', value: '按店铺近7日销量升序' },
    { label: '按店铺近7日销量增长率降序', value: '按店铺近7日销量增长率降序' },
    { label: '按店铺近7日销量增长率升序', value: '按店铺近7日销量增长率升序' },
    { label: '供货价降序', value: '供货价降序' },
    { label: '供货价升序', value: '供货价升序' },
    { label: '近7日销量降序', value: '近7日销量降序' },
    { label: '近7日销量升序', value: '近7日销量升序' },
    { label: '近7日销量增长率降序', value: '近7日销量增长率降序' },
    { label: '近7日销量增长率升序', value: '近7日销量增长率升序' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, numberRange
    },
    data() {
        return {
            shopOrderBy,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: '按店铺供货价降序',
                isAsc: false,
                yearMonthDay: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
                shopName: '',
                goodsName: '',
                hasSupplyPrice: null,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        echartsTooltip({ params, xAxis, series }, callback) {
            let str = `${xAxis[params[0].dataIndex]}<br/>销量:${series[0].data[params[0].dataIndex]}`
            callback(str)
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetHotSellingDataPage(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                    this.$nextTick(() => {
                        this.$refs.table.loadRowEcharts();
                        this.$forceUpdate()
                    })
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
