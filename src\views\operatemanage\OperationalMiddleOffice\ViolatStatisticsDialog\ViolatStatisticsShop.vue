<template>
  <my-container>
    <template #header>
      限制开始时间
      <el-date-picker style="width: 280px" v-model="timeRange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" 
        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" 
        @change="changeTime" unlink-panels>
      </el-date-picker>

      <el-select v-model="filterShop.groupId" placeholder="小组" multiple clearable collapse-tags filterable style="width:190px;" >
        <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key">
        </el-option>
      </el-select>
      
      <el-select v-model="filterShop.shopCode" placeholder="店铺" multiple clearable collapse-tags filterable style="width:190px;" >
        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
        </el-option>
      </el-select>
      
      <el-button type="primary" icon="el-icon-search" @click="onSearch">查询</el-button>
      <el-button type="primary" icon="el-icon-download" @click="onExport">导出</el-button>
    </template>
    
    <vxetablebase :id="'violatStatisticsShop202501141751'" ref="table" :that='that' :isIndex='false' 
      @sortchange='sortchangeShop' :tableData='tableData' :tableCols='tableColsShop' style="width: 100%; margin: 0"
      :loading="listLoading" :height="'100%'" :treeProp="{ rowField: 'id', parentField: 'parentId' }" :hasSeq="true">
      <template #questionName="{ row }">
        <el-button v-if="row.punishBeginTime" type="text" @click="showDetailDialog(row)" :disabled="!row.punishBeginTime">{{ row.questionName }}</el-button>
        <span v-else>{{ row.questionName }}</span>
      </template>
    </vxetablebase>

    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <!-- 违规类型详情 -->
    <el-dialog title="违规详情" :visible.sync="detailVisible" width="940px" v-dialogDrag >
      <vxetablebase2 :id="'violatStatisticsShopDetail202501141857'" ref="table2" :that='that' :index='false' 
        @sortchange='sortchangeDetail' :tableData='tableDataDetail' :tableCols='tableColsDetail' style="width: 100%; margin: 0; height: 400px;"
        :loading="listLoading" :height="'100%'" :hasSeq="true">
      </vxetablebase2>
    </el-dialog>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/vxetablebase.vue";
import vxetablebase2 from "@/components/VxeTable/yh_vxetable.vue";
import { getDirectorGroupList } from '@/api/operatemanage/base/shop';
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { getViolatStatisticsShopData, exportViolatStatisticsShopData, getViolatStatisticsShopDetailData } from '@/api/operatemanage/OperationalMiddleOfficeManage/ViolatStatistics.js';

const tableColsShop = [
  { sortable: 'custom', istrue: true, width: '280', align: "center", prop: "shopName", label: "店铺名称", 
    formatter: (row) => {
      if (0 != row.parentId) return '';
      else return row.shopName;
    } 
  },
  { sortable: 'custom', istrue: true, width: '250', align: "center", prop: "groupName", label: "运营组" },
  { sortable: 'custom', istrue: true, width: '250', align: "center", prop: "questionNum", label: "问题出现次数", treeNode: true },
  { istrue: true, width: '280', align: "center", prop: "questionName", label: "问题名称" },
  { istrue: true, width: '270', align: "center", prop: "punishBeginTime", label: "限制开始时间" },
  { istrue: true, width: '270', align: "center", prop: "punishEndTime", label: "限制结束时间" },
];

const tableColsDetail = [
  { sortable: 'custom', istrue: true, width: '180', align: "center", prop: "shopName", label: "店铺名称" },
  { sortable: 'custom', istrue: true, width: '120', align: "center", prop: "groupName", label: "运营组" },
  { sortable: 'custom', istrue: true, width: '150', align: "center", prop: "pddViolationId", label: "违规编号" },
  { sortable: 'custom', istrue: true, width: '150', align: "center", prop: "punishBeginTime", label: "限制开始时间" },
  { sortable: 'custom', istrue: true, width: '150', align: "center", prop: "punishEndTime", label: "限制结束时间" },
  { sortable: 'custom', istrue: true, width: '120', align: "center", prop: "punishStatus", label: "处罚状态" },
];

export default {
  name: "ViolatStatisticsShop",
  components: { MyContainer, vxetablebase, vxetablebase2 },
  data() {
    return {
      that: this,
      listLoading: false,
      timeRange: [],
      groupList: [],//运营组ID-小组
      shopList: [],//店铺编码-店铺(拼多多)

      filterShop: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        //过滤条件
        startPunishBeginTime: null,//起始限制开始时间
        endPunishBeginTime: null,//结束限制开始时间
        groupId: [],//运营组ID-小组
        shopCode: [],//店铺编号
      },
      total: 0,
      tableData: [],
      tableColsShop: tableColsShop,
      pickerOptions: {
        shortcuts: [{
          text: '前一天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            end.setTime(end.getTime());
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近一个月',
          onClick(picker) {
            const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
            const date2 = new Date(); date2.setDate(date2.getDate());
            picker.$emit('pick', [date1, date2]);
          }
        }]
      },

      filterDetail: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        //过滤条件
        startPunishBeginTime: null,//起始限制开始时间
        endPunishBeginTime: null,//结束限制开始时间
        shopCode: [],//店铺编号
        shopName: null,//店铺名称
        punishInfo: null,//处罚内容
        questionName: null,//违规类型
      },
      detailVisible: false,//详情弹窗
      tableDataDetail: [],
      tableColsDetail: tableColsDetail,
    }
  },
  async mounted() {
    this.init();
    this.getList();
  },
  methods: {
    async init() {
      //获取运营组ID-小组
      const group = await getDirectorGroupList({});
      this.groupList = [{ key: 0, value: '空' }].concat(group.data || []);
      //获取店铺编码-店铺(拼多多)
      const { data: data } = await getAllShopList({ platforms: [2] });
      data?.forEach(f => {
        if (f.isCalcSettlement && f.shopCode != null) {
            this.shopList.push(f);
        }
      })
    },
    async changeTime(e) {
      this.filterShop.startPunishBeginTime = e? e[0] : null;
      this.filterShop.endPunishBeginTime = e? e[1] : null;
    },
    // 每页数量改变
    Sizechange(val) {
      this.listLoading = true;
      this.filterShop.currentPage = 1;
      this.filterShop.pageSize = val;
      this.getList();
      this.listLoading = false;
    },
    // 当前页改变
    Pagechange(val) {
      this.filterShop.currentPage = val;
      this.getList();
    },
    sortchangeShop(column) {
      this.filterShop.orderBy = column.prop;
      this.filterShop.isAsc = column.order.indexOf("descending") == -1 ? true : false;
      this.onSearch();
    },
    // 查询
    onSearch() {
      //点击查询时才将页数重置为1
      this.filterShop.currentPage = 1;
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      this.listLoading = true;
      let data, success;
      ({ data, success } = await getViolatStatisticsShopData(this.filterShop));
      this.listLoading = false;
      if (success) {
        this.tableData = data.list;
        this.total = data.total;
      } else {
        this.$message.error("获取数据失败！");
      }
    },
    // 导出
    async onExport() {
      this.listLoading = true;
      const res = await exportViolatStatisticsShopData(this.filterShop);
      this.listLoading = false;
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute("download", '违规数据统计店铺维度导出_' + new Date().toLocaleString() + '.xlsx');
      aLink.click();
    },
    async getDetailList() {
      this.listLoading = true;
      const { data, success } = await getViolatStatisticsShopDetailData(this.filterDetail);
      this.listLoading = false;
      if (success) {
        this.tableDataDetail = data.list;
      }
    },
    // 显示违规类型详情
    async showDetailDialog(row) {
      this.filterDetail = {
        orderBy: '',
        startPunishBeginTime: this.filterShop.startPunishBeginTime,
        endPunishBeginTime: this.filterShop.endPunishBeginTime,
        shopCode: [row.shopCode],
        shopName: row.shopName,
        punishInfo: row.punishInfo,
        questionName: row.questionName,
      };
      this.detailVisible = true;
      this.getDetailList();
    },
    // 排序
    async sortchangeDetail(column) {
      this.filterDetail.orderBy = column.prop;
      this.filterDetail.isAsc = column.order.indexOf("descending") == -1 ? true : false;
      this.getDetailList();
    }

  }

}
</script>
<style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend
  {
      background-color: #fff;
  }

  ::v-deep .inner-container::-webkit-scrollbar
  {
      display: none;
  }

  ::v-deep .mycontainer
  {
      position: relative;
  }

  .uptime
  {
      font-size: 14px;
      position: absolute;
      right: 30px;
  }

  //解决下拉菜单多选由文字太长导致样式问题
  ::v-deep .el-select__tags-text
  {
      max-width: 45px;
  }
</style>
