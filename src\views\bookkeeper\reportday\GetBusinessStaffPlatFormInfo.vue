<template>
  <my-container v-loading="pageLoading">
    <template #header>
     <div>
       <el-button-group>
          <el-button style="padding: 0;margin: 0;">
             <el-input v-model="filter.proCode" placeholder="商品ID" style="width:150px;"/>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
             <el-input v-model="filter.goodsName" placeholder="商品名称" style="width:160px;"/>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
              <el-date-picker style="width: 210px" v-model="filter.timeCalc" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </el-button>
        <el-button style="padding: 0;">
          <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组" style="width: 90px">
            <el-option key="无运营组" label="无运营组" :value="0"></el-option>
            <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-button>
        <el-button type="primary" @click="onSearch">查询</el-button>
       </el-button-group>
       <div class="el-backtop" style="right: 5px;" @click="showupload"><i class="el-icon-caret-right"></i></div>
     </div>
    </template>
    <!-- <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn="true"
          :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='financialreportlist' @summaryClick='onsummaryClick'
          :tableCols='tableCols' :tableHandles='tableHandles'  :loading="listLoading" style="width:100%;height:96%;margin: 0">
       <template slot='extentbtn'>

       </template>
    </ces-table> -->
    <vxetablebase
      :id="'GetBusinessStaffPlatFormInfo202302031421'" :border="true" :align="'center'" :tablekey="'GetBusinessStaffPlatFormInfo202302031421'"
      ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      :isSelectColumn="true" :showsummary='false' :tablefixed='true' :summaryarry='summaryarry'
      :tableData='financialreportlist' @summaryClick='onsummaryClick' :tableCols='tableCols'
      :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:96%;margin: 0"   :xgt="9999">
      </vxetablebase>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
    </template>

<el-dialog title="导入数据" :visible.sync="dialogVisibleData" width="30%" v-dialogDrag>
      <span>
         <!--  <el-row>
            <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker style="width: 100%" v-model="onimportfilter.yearmonthday" type="date" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
         </el-col>
         </el-row> -->
          <el-row>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
              <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action accept=".xlsx"
              :http-request="uploadFile1" :on-change="uploadChange" :on-remove="uploadRemove">
              <template #trigger>
                  <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
            </el-upload>
            </el-col>
          </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleData = false">关闭</el-button>
      </span>
    </el-dialog>

  </my-container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
import {getDirectorGroupList,getDirectorList} from '@/api/operatemanage/base/shop'
import {GetBusinessStaffPlatFormData,getParm,setParm,exportFinancialDayReport,queryTXDayReportAnalysis} from '@/api/bookkeeper/reportday'
import {importProductDayReport,ImportBusinessStaffPlatForm} from '@/api/bookkeeper/import'
import productdrchart from '@/views/bookkeeper/reportday/productdrchart'
import BusinessStaffPlatForm from '@/views/bookkeeper/reportday/BusinessStaffPlatForm'
import {formatPlatform,formatTime,formatYesornoBool,formatLinkProCode} from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import InputMult from "@/components/Comm/InputMult";
import { Loading } from 'element-ui';
import { ruleDirectorGroup } from '@/utils/formruletools'
import freightDetail from '@/views/bookkeeper/reportday/freightDetail'
import buschar from '@/components/Bus/buschar'
import importmodule from '@/components/Bus/importmodule'
import expressfreightanalysis from '@/views/express/expressfreightanalysis'
import ordergiftdetail from '@/views/order/gift/ordergiftDetail'
let loading;
const startLoading = () => {
  loading = Loading.service({
  lock: true,
  text: '加载中……',
  background: 'rgba(0, 0, 0, 0.7)'
  });
};
const tableCols =[
         {istrue:true,fixed: 'left',prop:'timeCalc',label:'统计日期',sortable:'custom', width:'80'},
        {istrue:true,fixed: 'left',prop:'proCode',fix:true,label:'商品ID', width:'120',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
        {istrue:true,fixed: 'left',prop:'goodsName',label:'商品名称',sortable:'custom', width:'100'},
        {istrue:true,fixed: 'left',prop:'goodsNo',label:'货号',sortable:'custom', width:'100'},
        {istrue:true,fixed: 'left',prop:'status',label:'商品状态',sortable:'custom', width:'60'},
        {istrue:true,fixed: 'left',prop:'goodslabel',label:'商品标签',sortable:'custom', width:'60'},
        {istrue:true,fixed: 'left',prop:'orderCount',label:'商品访客数',sortable:'custom', width:'80'},
        {istrue:true,fixed: 'left',prop:'goodsPageView',label:'商品浏览量',sortable:'custom', width:'80'},
        {istrue:true,fixed: 'left',prop:'averageStayTime',label:'平均停留时长',sortable:'custom', width:'65'},
        {istrue:true,fixed: 'left',prop:'goodsJumpRate',label:'商品详情页跳出率(%)',sortable:'custom', width:'80',formatter:(row)=>row.goodsJumpRate+"%"},
        {istrue:true,fixed: 'left',prop:'goodscollectnumber',label:'商品收藏人数', width:'150',sortable:'custom'},
        {istrue:true,fixed: 'left',prop:'goodsPurchasedNumber',label:'商品加购件数',sortable:'custom', width:'120'},
        {istrue:true,summaryEvent:true,prop:'goodsPurchasedPeopleNumber',label:'商品加购人数',sortable:'custom', width:'70'},
        {istrue:true,summaryEvent:true,prop:'orderBuyNumber',label:'下单买家数',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'orderNumber',label:'下单件数',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'orderMoney',label:'下单金额',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'orderRate',label:'下单转化率(%)',sortable:'custom', width:'80',formatter:(row)=>row.orderRate+"%"},
        {istrue:true,summaryEvent:true,prop:'payBuyNumber',label:'支付买家数',sortable:'custom', width:'80',type:'custom'},
        {istrue:true,summaryEvent:true,prop:'payNumber',label:'支付件数',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'payMoney',label:'支付金额',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'goodsPayRate',label:'商品支付转化率(%)',sortable:'custom', width:'80',formatter:(row)=>row.goodsPayRate+"%"},
        {istrue:true,summaryEvent:true,prop:'payNewBuyNumber',label:'支付新买家数',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'payOlderBuyNumber',label:'支付老买家数',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'oldBuyPayMoney',label:'老买家支付金额',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'juHostSalePayMoney',label:'聚划算支付金额',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'visitorAverage',label:'访客平均价值%',sortable:'custom', width:'80',formatter:(row)=>row.visitorAverage+"%"},
        {istrue:true,summaryEvent:true,prop:'successRefundMoney',label:'成功退款金额',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'competitiveness',label:'竞争力评分',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'yearPayMoney',label:'年累计支付金额',sortable:'custom', width:'80',type:'custom'},
        {istrue:true,summaryEvent:true,prop:'monthPayMoney',label:'月累计支付金额',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'monthPayNumber',label:'月累计支付件数',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'searchPayRate',label:'搜索引导支付转化率',sortable:'custom', width:'80',formatter:(row)=>row.searchPayRate+"%"},
        {istrue:true,summaryEvent:true,prop:'searchVisitorNumber',label:'搜索引导访客数',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'searchPayBuyNumber',label:'搜索引导支付买家数',sortable:'custom', width:'80'},
        {istrue:true,summaryEvent:true,prop:'searchVisitorsRate',label:'搜索访客占比',sortable:'custom', width:'80',formatter:(row)=>row.searchVisitorsRate+"%"}
        ]
const tableHandles=[
        {label:"导入", handle:(that)=>that.startImport()},
        {label:"模板-生意参谋平台", handle:(that)=>that.downloadTemplate()},
         {label:"刷新", handle:(that)=>that.onRefresh()}
      ];

export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable,productdrchart,InputMult,freightDetail,buschar,expressfreightanalysis,importmodule,ordergiftdetail,BusinessStaffPlatForm,vxetablebase},
  data() {
    return {
      that:this,
      filter: {
        proCode:null,
        goodsName:null,
        brandId:null,
        groupId:null,
        startTime: null,
        endTime: null,
        timeCalc:null
      },
      onimportfilter:{
        yearmonthday:null,
      },
      shopList:[],
      userList:[],
      brandlist:[],
      grouplist:[],
      directorlist:[],
      financialreportlist: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      total: 0,
      pager:{OrderBy:" timeCalc",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      summaryarry:{},
      selids:[],
      fileList:[],
      dialogVisibleData:false,
      dialogVisible:false,
      uploadLoading:false,
      importFilte:{},
      fileList:[],
      fileparm:{},
      editparmVisible:false,
      editLoading:false,
      editparmLoading:false,
      drawervisible:false,
      /* dialogDrVisibleShengYi:false, */
      dialogDrVisible:false,
      expressfreightanalysisVisible:false,
      drparamProCode:'',
      autoformparm:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[]
        },
      freightDetail:{
        visible:false,
        filter:{
          proCode:null,
          timeCalc:[]
        }
      },
      giftDetail:{visible:false},
      costDialog:{visible:false,rows:[]},
      buscharDialog:{visible:false,title:"",data:[]},
      drawervisible:false,
    };
  },
  async mounted() {
  },
  async created() {
    await this.init()
    await this.getShopList();
    /* await this.initformparm(); */
  },
  methods: {
     //下载生意参谋平台导入模板
   downloadTemplate(){
        window.open("../../static/excel/生意参谋平台数据模板.xlsx","_self");
    },
     //导入
    async uploadFile1(item) {
      /* if(!this.fileHasSubmit){
        return false;
      } */
      this.fileHasSubmit=false;
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      form.append("yearmonthday", this.onimportfilter.yearmonthday);
      console.log('111',form)
      const res =await ImportBusinessStaffPlatForm(form);
      if (res.code==1)   this.$message({ message: "上传成功,正在导入中...", type: "success" });
      else  this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading=false;
      },
    async uploadChange(file, fileList) {
    if (fileList && fileList.length > 0) {
      var list = [];
      for(var i=0;i<fileList.length;i++){
        if(fileList[i].status=="success")
          list.push(fileList[i]);
        else
          list.push(fileList[i].raw);
      }
      this.fileList = list;
    }
    },
    uploadRemove(file, fileList){
       this.uploadChange(file, fileList);
    },
    //导入数据弹框
    startImport() {
      this.dialogVisibleData = true;
    },

    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init(){
        var date1 = new Date(); date1.setDate(date1.getDate()-10);
        var date2 = new Date(); date2.setDate(date2.getDate()-1);
        this.filter.timeCalc=[];
        this.filter.timeCalc[0]=this.datetostr(date1);
        this.filter.timeCalc[1]=this.datetostr(date2);
      },
    /* async initformparm(){
       let that=this;
       this.autoformparm.rule= [{type:'hidden',field:'id',title:'id',value: '',col:{span:12}},
                     {type:'select',field:'groupId',title:'组长',value:'', update(val, rule){{that.updateruleGroup(val)}},...await ruleDirectorGroup(),props:{filterable:true}},
                     {type:'InputNumber',field:'Profit3PredictRate',title:'毛三预估比例%',value: null,props:{min:0,precision:3},col:{span:6}},
                     {type:'InputNumber',field:'ShareRate',title:'公摊费率%',value: null,props:{min:0,precision:3},col:{span:6}}]
    }, */
   async getShopList(){
      const res1 = await getAllShopList();
      this.shopList=[];
        res1.data?.forEach(f => {
          if(f.isCalcSettlement&&f.shopCode&&(f.platform==1||f.platform==8))
              this.shopList.push(f);
        });
        var res2= await getDirectorGroupList();
        this.grouplist = res2.data?.map(item => {return { value: item.key, label: item.value };});

        var res3= await getDirectorList();
        this.directorlist = res3.data?.map(item => {return { value: item.key, label: item.value };});

       /*  var res4= await getAllProBrand(); */
        /* this.brandlist = res4.data.map(item => {
            return { value: item.key, label: item.value };
        }); */
    },
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
     await this.onSearch();
    },
    onRefresh(){
      this.onSearch()
    },
    async onSearch(){
      this.$refs.pager.setPage(1);
      await this.getList().then(res=>{  });
      // loading.close();
    },
    async getList(){
      this.filter.startTime =null;
      this.filter.endTime =null;
      if (this.filter.timeCalc) {
        this.filter.StartDate = this.filter.timeCalc[0];
        this.filter.EndDate = this.filter.timeCalc[1];
      }
      var that=this;
      var pager = this.$refs.pager.getPager();
      const params = {...pager,...this.pager,...this.filter};
     // this.listLoading = true;
      startLoading();
      const res = await GetBusinessStaffPlatFormData(params).then(res=>{
          loading.close();
          that.total = res.data?.total;
          if(res?.data?.list&&res?.data?.list.length>0){
            for (var i in res.data.list) {
              if (!res.data.list[i].freightFee) {
                res.data.list[i].freightFee =" ";
              }
            }
          }
          that.financialreportlist = res.data?.list;
          that.summaryarry=res.data?.summary;
      });
    },
    showFreightDetail(row){
        if(row.freightFee>=1){
          this.freightDetail.filter.proCode = row.proCode;
          if (row.yearMonthDay != null){
            var dayStr =row.yearMonthDay.substr(0,4)+'-'+row.yearMonthDay.substr(4,2)+'-'+row.yearMonthDay.substr(6,2)
            this.freightDetail.filter.timeCalc = [dayStr,dayStr];
          }
          else {
            this.freightDetail.filter.timeCalc = this.filter.timeCalc
          }
          this.freightDetail.visible=true;
          setTimeout(async () => {
            await this.$refs.freightDetail.onSearch();
          }, 100);
        }
    },
   async showGiftDetail(row){
      this.giftDetail.visible=true;
      let _th=this;
      await this.$nextTick(async () => {  await _th.$refs.ordergiftdetail.onShow(row.yearMonthDay,row.proCode); });
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
   onRefresh(){
        this.onSearch()
    },
  async updateruleGroup(groupid) {
     if(!groupid)
        this.autoformparm.fApi.resetFields()
     else{
       const res = await getParm({groupId:groupid})
       var arr = Object.keys(this.autoformparm.fApi);
       res.data.groupId=groupid;
       if(!res.data?.Profit3PredictRate) res.data.Profit3PredictRate=0;
       if(!res.data?.ShareRate) res.data.ShareRate=0;
       await this.autoformparm.fApi.setValue(res.data)
      }
    },
  async showprchart2(prcode){
      window['lastseeprcodedrchart']=prcode

      this.drparamProCode=prcode
      this.dialogDrVisible=true
      /* this.dialogDrVisibleShengYi=true */
   },
  async onstartImport(){
      this.dialogVisible=true;
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      /* if (!this.onimportfilter.yearmonthday) {
       this.$message({type: 'warning',message: '请选择年月!'});
       return;
      } */
      this.uploadLoading=true
      this.$refs.upload.submit();
    },
   async uploadFile(item) {
     if(!item||!item.file||!item.file.size){
        this.$message({message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("platform", 1);
      form.append("yearmonthday", this.onimportfilter.yearmonthday);
      var res = await importProductDayReport(form);
      if (res.code==1) this.$message({message: "上传成功,正在导入中...", type: "success" });
      else this.$message({message: res.msg, type: "warning" });
      this.uploadLoading=false
    },
  async onExport(){
      this.filter.startTime =null;
      this.filter.endTime =null;
      if (this.filter.timeCalc) {
        this.filter.startTime = this.filter.timeCalc[0];
        this.filter.endTime = this.filter.timeCalc[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = {  ...pager,   ...this.pager,   ...this.filter};
      var res= await exportFinancialDayReport(params);
      if(!res?.data) {
         this.$message({message:"没有数据",type:"warning"});
         return
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download','财务账单数据' +  new Date().toLocaleString() + '_.xlsx' )
      aLink.click()
    },
  async onShowEditParm(){
      this.editparmVisible = true
      const res = await getParm()
      var arr = Object.keys(this.autoformparm.fApi);
      if(arr.length >0)
         this.autoformparm.fApi.resetFields()
      await this.autoformparm.fApi.setValue(res.data)
    },
  async onSetEditParm(){
      this.editparmLoading=true;
      await this.autoformparm.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoformparm.fApi.formData();
          const res = await setParm(formData);
          if(res.code==1) this.editparmVisible=false;
        }else{}
     })
     this.editparmLoading=false;
  },
  async onsummaryClick(property){
    // this.$message({message:property,type:"warning"});
      this.filter.startTime =null;
      this.filter.endTime =null;
      if (this.filter.timeCalc) {
        this.filter.startTime = this.filter.timeCalc[0];
        this.filter.endTime = this.filter.timeCalc[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = {...pager,...this.pager,...this.filter};
      params.column=property;
      let that=this;
      const res = await queryTXDayReportAnalysis(params).then(res=>{
        that.buscharDialog.visible=true;
        that.buscharDialog.data=res.data
        that.buscharDialog.title=res.data.legend[0]
      });
  },
  showCost(row){
    if(row.replaceSendCost >0){
      this.costDialog.visible =true;
      this.costDialog.rows=[row];
    }
  },
  showexpressfreightanalysis() {
    this.expressfreightanalysisVisible=true;
    this.$nextTick(() => { this.$refs.expressfreightanalysis.onSearch()});
  },
  renderCost(row){
    if(row.replaceSendCost >0){
      return "color:blue;cursor:pointer;";
    }
    else{
      return "";
    }
  },
  showupload() {
     this.drawervisible=true;
  }
},

};
</script>
<style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
</style>

