<template>
  <div v-loading="loading" style="height: 100%;">
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
      <div class="top">
        <div class="publicCss">
          <el-form-item label="仓库" prop="warehouse">
            <el-select v-model="ruleForm.warehouse" placeholder="请选择仓库" clearable filterable
              @change="handleWarehouseChange">
              <el-option v-for="item in stashList" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </div>
        <div class="publicCss">
          <el-form-item label="区域" prop="warehouseArea">
            <el-select v-model="ruleForm.warehouseArea" placeholder="请选择区域" clearable filterable
              @change="handleWarehouseAreaChange">
              <el-option v-for="item in regionList" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </div>
      </div>
      <div class="dynamic-border" style="--left-width:7%; --right-width:89%; --gap:20px; --border-color:#ccc">
        <span class="gap-label">通道</span>
        <el-form-item label="流水号位数" prop="aisleDigits">
          <el-input v-model.trim="ruleForm.aisleDigits" placeholder="请输入流水号位数" maxlength="50" clearable class="digitCss"
            @blur="handleWarehouseAreablur" />
        </el-form-item>
        <el-form-item label="起始流水号" prop="startAisleNumber">
          <el-input v-model.trim="ruleForm.startAisleNumber" placeholder="请输入起始流水号" maxlength="50" clearable
            class="digitCss" @blur="handleWarehouseAreablur" />
        </el-form-item>
        <el-form-item label="生成通道数" prop="numberOfAisles">
          <el-input v-model.trim="ruleForm.numberOfAisles" placeholder="请输入生成通道数" maxlength="50" clearable
            class="digitCss" @blur="handleWarehouseAreablur" />
        </el-form-item>
      </div>
      <div class="dynamic-border" style="--left-width:7%; --right-width:89%; --gap:20px; --border-color:#ccc">
        <span class="gap-label">架号</span>
        <el-form-item label="流水号位数" prop="rackDigits">
          <el-input v-model.trim="ruleForm.rackDigits" placeholder="请输入流水号位数" maxlength="50" clearable class="digitCss"
            @blur="handleWarehouseAreablur" />
        </el-form-item>
        <el-form-item label="起始流水号" prop="startRackNumber">
          <el-input v-model.trim="ruleForm.startRackNumber" placeholder="请输入起始流水号" maxlength="50" clearable
            class="digitCss" @blur="handleWarehouseAreablur" />
        </el-form-item>
        <el-form-item label="生成货架数" prop="numberOfRacks">
          <el-input v-model.trim="ruleForm.numberOfRacks" placeholder="请输入生成货架数" maxlength="50" clearable
            class="digitCss" @blur="handleWarehouseAreablur" />
        </el-form-item>
      </div>
      <div class="dynamic-border"
        style="--left-width:7%; --right-width:89%; --gap:20px; --border-color:#ccc;display: block;">
        <span class="gap-label">层位</span>
        <div class="row">
          <div class="form-item">
            <el-form-item label="起始层位号" prop="startLevelNumber">
              <el-input v-model.trim="ruleForm.startLevelNumber" placeholder="请输入起始层位号" maxlength="50" clearable
                class="digitCss" @blur="handleWarehouseAreablur" />
            </el-form-item>
          </div>
          <div class="form-item">
            <el-form-item label="生成层位数" prop="numberOfLevels">
              <el-input v-model.trim="ruleForm.numberOfLevels" placeholder="请输入生成层位数" maxlength="50" clearable
                class="digitCss" @blur="handleWarehouseAreablur" />
            </el-form-item>
          </div>
        </div>
        <div class="row">
          <div class="form-item">
            <el-form-item label="流水号位数" prop="locationDigits">
              <el-input v-model.trim="ruleForm.locationDigits" placeholder="请输入流水号位数" maxlength="50" clearable
                class="digitCss" @blur="handleWarehouseAreablur" />
            </el-form-item>
          </div>
          <div class="form-item">
            <el-form-item label="起始流水号" prop="startLocationNumber">
              <el-input v-model.trim="ruleForm.startLocationNumber" placeholder="请输入起始流水号" maxlength="50" clearable
                class="digitCss" @blur="handleWarehouseAreablur" />
            </el-form-item>
          </div>
          <div class="form-item">
            <el-form-item label="每层库位数" prop="locationsPerLevel">
              <el-input v-model.trim="ruleForm.locationsPerLevel" placeholder="请输入每层库位数" maxlength="50" clearable
                class="digitCss" @blur="handleWarehouseAreablur" />
            </el-form-item>
          </div>
        </div>
      </div>
      <div style="color: red;margin: 20px 0;">
        温馨提示：生成库位总数=通道数*货架数*每层库位数*层位数
      </div>
    </el-form>
    <div style="display: flex;justify-content: center;">
      <el-button @click="cancelClick">取消</el-button>
      <el-button type="primary" @click="handleClick">生成</el-button>
    </div>
  </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { batchAddWarehouseLocationManagement } from '@/api/inventory/sampleGoods';
import dayjs from 'dayjs'
export default {
  name: "batchStorageLocation",
  components: {
    MyContainer, vxetablebase
  },
  props: {
    result: {
      type: Array,
      default: () => []
    },
    stashlt: {
      type: Array,
      default: () => []
    },
    //仓库
    stashList: {
      type: Array,
      default: () => []
    },

    //区域
    regionList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      rules: {
        warehouse: [
          { required: true, message: '请选择仓库', trigger: 'blur' }
        ],
        warehouseArea: [
          { required: true, message: '请选择区域', trigger: 'blur' }
        ],
        aisleDigits: [
          { required: true, message: '请输入流水号位数', trigger: 'blur' }
        ],
        startAisleNumber: [
          { required: true, message: '请输入起始流水号', trigger: 'blur' }
        ],
        numberOfAisles: [
          { required: true, message: '请输入生成通道数', trigger: 'blur' }
        ],
        rackDigits: [
          { required: true, message: '请输入流水号位数', trigger: 'blur' }
        ],
        startRackNumber: [
          { required: true, message: '请输入起始流水号', trigger: 'blur' }
        ],
        numberOfRacks: [
          { required: true, message: '请输入生成货架数', trigger: 'blur' }
        ],
        startLevelNumber: [
          { required: true, message: '请输入起始层位号', trigger: 'blur' }
        ],
        numberOfLevels: [
          { required: true, message: '请输入生成层位数', trigger: 'blur' }
        ],
        locationDigits: [
          { required: true, message: '请输入流水号位数', trigger: 'blur' }
        ],
        startLocationNumber: [
          { required: true, message: '请输入起始流水号', trigger: 'blur' }
        ],
        locationsPerLevel: [
          { required: true, message: '请输入每层库位数', trigger: 'blur' }
        ]
      },
      ruleForm: {
        warehouse: '',//仓库
        warehouseArea: '',//区域
        aisleDigits: undefined,//通道流水号位数
        startAisleNumber: undefined,//起始通道流水号
        numberOfAisles: undefined,//生成通道数
        rackDigits: undefined,//架号流水号位数
        startRackNumber: undefined,//起始架号流水号
        numberOfRacks: undefined,//生成货架数
        startLevelNumber: undefined,//起始层位号
        numberOfLevels: undefined,//生成层位数
        locationDigits: undefined,//层位流水号位数
        startLocationNumber: undefined,//起始层位流水号
        locationsPerLevel: undefined,//每层库位数
        warehouseAreaCode: null,//区域编码
        warehouseCode: null,//仓库编码
      },
      value: '',
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
      },
      timeRanges: [],
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    this.$nextTick(() => {
      this.$refs.ruleForm.resetFields();
      this.$refs.ruleForm.clearValidate();
    });
  },
  methods: {
    handleWarehouseAreablur() {
      const numberRegex = /^\d+$/;
      const fields = [
        { key: 'aisleDigits', label: '流水号位数' },
        { key: 'startAisleNumber', label: '起始流水号' },
        { key: 'numberOfAisles', label: '生成通道数' },
        { key: 'channelNumber', label: '通道号' },
        { key: 'rackDigits', label: '流水号位数' },
        { key: 'startRackNumber', label: '起始流水号' },
        { key: 'numberOfRacks', label: '生成货架数' },
        { key: 'startLevelNumber', label: '起始层位号' },
        { key: 'numberOfLevels', label: '生成层位数' },
        { key: 'locationDigits', label: '流水号位数' },
        { key: 'startLocationNumber', label: '起始流水号' },
        { key: 'locationsPerLevel', label: '每层库位数' }
      ];
      for (const field of fields) {
        const value = this.ruleForm[field.key];
        if (value && !numberRegex.test(value)) {
          this.$message.warning(`${field.label}必须为数字`);
          this.ruleForm[field.key] = '';
          return;
        }
      }
    },
    handleWarehouseChange(value) {
      const selectedWarehouse = this.stashlt.find(warehouse => warehouse.warehouse === value);
      this.ruleForm.warehouseCode = selectedWarehouse ? selectedWarehouse.warehouseCode : null
      this.ruleForm.warehouseArea = ''
      this.regionList = this.result.filter(item => item.warehouseCode === this.ruleForm.warehouseCode).map(item => item.warehouseArea)
    },
    handleWarehouseAreaChange(value) {
      const selectedArea = this.result.find(area => area.warehouseArea === value);
      this.ruleForm.warehouseAreaCode = selectedArea ? selectedArea.warehouseAreaCode : null
    },
    async handleClick() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          this.loading = true
          const { success, data } = await batchAddWarehouseLocationManagement(this.ruleForm)
          this.loading = false
          if (success) {
            this.$message.success('生成成功')
            this.$emit('successClick')
          }
        }
      })
    },
    cancelClick() {
      this.$emit('cancelClick')
    },

  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    margin-right: 20px;
  }
}

.digitCss {
  width: 140px;
}

.row {
  display: flex;
  gap: 20px;
  /* 控制表单项间距 */
  // margin-bottom: 15px;
  /* 行间距 */
}

.form-item {
  display: flex;
  align-items: center;
  gap: 8px;
  /* 标签和输入框间距 */
  // flex: 1;
  /* 自动平分剩余空间 */
}

.dynamic-border {
  width: 100%;
  position: relative;
  margin-top: 20px;
  padding-top: 20px;
  // padding: 20px 15px 15px;
  /* 根据实际内容间距需要保留 */
  display: flex;
  gap: var(--gap);
}

/* 上边框左段 */
.dynamic-border::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: var(--left-width);
  height: 1px;
  background: var(--border-color);
}

/* 上边框右段 */
.dynamic-border::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: var(--right-width);
  height: 1px;
  background: var(--border-color);
}

.gap-label {
  position: absolute;
  left: calc(var(--left-width) + var(--gap)/2);
  transform: translateX(-50%);
  top: -10px;
  padding: 0 15px;
  background: #fff;
  font-size: 14px;
  color: #666;
}
</style>
