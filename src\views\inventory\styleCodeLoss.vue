<template>
    <MyContainer>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :toolbarshow="false"
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { getStyleCodeContinuLossesList } from '@/api/operatemanage/continuLosses' //持续亏损
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'styleCode', label: '系列编码', },
    { width: 'auto', align: 'center', prop: 'productCategoryName', label: '类目', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'day30OrderNum', label: '近30天订单数', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'day30SalesRevenue', label: '近30天销售额', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'day30Profit3', label: '毛三利润', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'profit3Rate', label: '毛三利率', formatter: (row) => row.profit3Rate + '%' },
]
export default {
    name: "styleCodeLoss",
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'styleCode',
                isAsc: false,
                listType: 0,
                queryType: 0,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            position: {
                right: "15px"
            }
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async getList() {
            const { data: { list, total }, success } = await getStyleCodeContinuLossesList(this.ListInfo)
            if (success) {
                this.tableData = list
                this.total = total
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss"></style>