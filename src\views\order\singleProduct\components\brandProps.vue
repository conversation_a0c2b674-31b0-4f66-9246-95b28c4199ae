<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="ListInfo.date" type="date" placeholder="选择日期" class="publicCss"
                    :clearable="false" value-format="yyyy-MM-dd" />
                <number-range :min.sync="ListInfo.minAvgOrderQty" :max.sync="ListInfo.maxAvgOrderQty" :precision="0"
                    min-label="平均订单 - 最小值" max-label="平均订单 - 最大值" class="publicCss" style="width: 200px;" />
                <number-range :min.sync="ListInfo.minAvgGoodQty" :max.sync="ListInfo.maxAvgGoodQty" :precision="0"
                    min-label="平均销量 - 最小值" max-label="平均销量 - 最大值" class="publicCss" style="width: 200px;" />
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="proCodeCallback($event, 1)" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.styleCodes" v-model="ListInfo.styleCodes"
                    placeholder="系列编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="proCodeCallback($event, 2)" title="系列编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable
                    class="publicCss" />
                <el-select v-model="ListInfo.isSure" placeholder="是否确定" class="publicCss" clearable>
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                </el-select>
                <el-input-number v-model="ListInfo.minAvgOrder" :min="0" :max="999999999" class="publicCss"
                    :controls="false" :precision="0" placeholder="3日最小订单数"></el-input-number>
                <number-range :min.sync="ListInfo.minInvQty" :max.sync="ListInfo.maxInvQty" :precision="0"
                    min-label="库存数 - 最小值" max-label="库存数 - 最大值" class="publicCss" style="width: 200px;" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
                    <el-button type="primary" size="mini" :disabled="isExport" @click="brandPropsSetting">设置</el-button>
                    <el-button type="primary" size="mini" :disabled="isExport" @click="batchSure">批量确定</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%"
            :condition="ListInfo" @onTrendChart="trendChart" @select="select" :showsummary="data.summary ? true : false"
            :summaryarry="data.summary" @sortchange="sortchange" />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="设置" :visible.sync="dialogVisible" width="20%"  v-dialogDrag>
            <el-form :model="setInfo" status-icon :rules="rules" ref="ruleForm" label-width="100px"
                class="demo-ruleForm">
                <el-form-item label="最小订单数" prop="orderMinQty">
                    <el-input-number v-model="setInfo.orderMinQty" :min="0" :max="999999999" placeholder="最小订单数"
                        style="width: 180px;" :controls="false" :precision="0" />
                </el-form-item>
                <el-form-item label="仓库" prop="wmsId">
                    <chooseWareHouse v-model="setInfo.wmsId" style="width: 180px;" placeholder="调出仓" class="publicCss"
                        @chooseWms="(e) => setInfo.wmsName = e.name" />
                </el-form-item>
                <el-form-item label="品牌标签" prop="packMtls1">
                   <el-input v-model="setInfo.packMtls1" placeholder="品牌标签" rows="5" resize="none" type="textarea" maxlength="99999999"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" style="display: flex;justify-content: center;align-items: center;">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="状态设置" :visible.sync="batchSetStatusVisible" width="20%"  v-dialogDrag>
            <el-form :model="batchSureInfo" status-icon :rules="rules1" ref="ruleForm" label-width="100px" class="demo-ruleForm" v-if="batchSetStatusVisible">
                <el-form-item label="确认状态" prop="isSure">
                     <el-select v-model="batchSureInfo.isSure" placeholder="是否确定" class="publicCss" clearable>
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                </el-select>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input  v-model="batchSureInfo.remark" type="textarea" maxlength="500" resize="none"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" style="display: flex;justify-content: center;align-items: center;">
                <el-button @click="batchSetStatusVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitBatchSetStatus">确 定</el-button>
            </span>
        </el-dialog>

        <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false"
            direction="btt" v-dialogDrag>
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
                    :picker-options="pickerOptions" style="margin: 10px" @change="
                        trendChart({
                            ...chatPropOption,
                            startDate: $event[0],
                            endDate: $event[1],
                        })
                        " />
                <buschar v-if="!chatProp.chatLoading" :analysis-data="chatProp.data" />
            </div>
            <div v-else v-loading="chatProp.chatLoading" />
        </el-drawer>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/SaleItems/BrandGoodsSale/'
import { mergeTableCols } from '@/utils/getCols'
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, chooseWareHouse
    },
    data() {
        return {
            api,
            platformlist,
            that: this,
            ListInfo: {
                date: dayjs().subtract(1, 'day').format('YYYY-MM-DD'), // 日期
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false,
                summarys: [],
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            setInfo: {
                id: "",
                orderMinQty: undefined,
                wmsId: null,
                wmsName: "",
                packMtls:[],
                packMtls1: '',
            },
            dialogVisible: false,
            rules: {
                orderMinQty: [
                    { required: true, message: "请输入最小订单数", trigger: "blur" },
                    { type: "number", min: 0, message: "最小订单数必须大于等于 0", trigger: "blur" }
                ],
                wmsId: [
                    { required: true, message: "请选择仓库", trigger: "blur" }
                ]
            },
            rules1:{
                isSure: [
                    { required: true, message: "请选择确认状态", trigger: "blur" }
                ],
            },
            selectList: [],
            batchSureInfo: {
                isSure: null,
                remark: '',
                goods: []
            },
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: {}, // 趋势图数据
            },
            batchSetStatusVisible: false,
        }
    },
    async mounted() {
        await this.getCol();
        await this.getList()
    },
    methods: {
        async trendChart(option) {
            var endDate = null;
            var startDate = null;

            if (option.startDate && option.endDate) {
                startDate = option.startDate;
                endDate = option.endDate;
            } else {
                endDate = option.date;
                startDate = new Date(option.date);
                startDate.setDate(option.date.getDate() - 30);

                startDate = dayjs(startDate).format("YYYY-MM-DD");
                endDate = dayjs(endDate).format("YYYY-MM-DD");
            }
            option.filter.filters = option.filter.filters.filter((item) => item.field !== option.dateField);
            option.filter.filters.push({
                field: option.dateField,
                operator: "GreaterThanOrEqual",
                value: startDate,
            });
            option.filter.filters.push({
                field: option.dateField,
                operator: "LessThanOrEqual",
                value: endDate,
            });

            option.startDate = startDate;
            option.endDate = endDate;

            this.chatProp.chatTime = [startDate, endDate];

            this.chatProp.chatLoading = true;

            const { data, success } = await request.post(`${this.api}GetTrendChart`, option);
            if (success) {
                this.chatProp.data = data;
            }

            this.chatProp.chatLoading = false;
            this.chatProp.chatDialog = true;

            this.chatPropOption = option;
        },
        async submitBatchSetStatus() {
            if (this.batchSureInfo.isSure === null) return this.$message.warning("请选择确认状态");
            this.batchSureInfo.goods = this.selectList.map(item => {
                return {
                    goodsCode: item.goodsCode,
                    goodsName: item.goodsName,
                    sureTime :item.sureTime 
                }
            });
            const { success } = await request.post(`${this.api}Sure`, this.batchSureInfo);
            if (success) {
                this.$message.success("操作成功");
                this.batchSetStatusVisible = false;
                this.selectList = [];
                this.batchSureInfo = {
                     isSure: null,
                     remark: '',
                     goods: []
                };
                await this.getList();
            }
        },
        batchSure() {
            if (this.selectList.length === 0) return this.$message.warning("请至少选择一条数据");
            this.batchSureInfo = {
                isSure: null,
                remark: '',
                goods: []
            };
            this.batchSetStatusVisible = true;
        },
        select(val) {
            this.selectList = val;
        },
        submitForm() {
            this.$refs.ruleForm.validate(async (valid) => {
                if (valid) {
                    this.setInfo.packMtls = this.setInfo.packMtls1.split(/[,，\n]/).filter(item => item.trim() !== '')
                    const { success } = await request.post(`${this.api}SaveSetting`, this.setInfo)
                    if (success) {
                        this.$message.success("设置成功");
                        this.dialogVisible = false;
                        await this.getList();
                    } else {
                        this.$message.error("设置失败");
                    }
                } else {
                    this.$message.error("请填写完整信息");
                }
            });
        },
        async brandPropsSetting() {
            const { data } = await request.post(`${this.api}GetSetting`)
            data.packMtls1 = data.packMtls.join('\n')
            this.setInfo = data;
            this.dialogVisible = true
        },
        proCodeCallback(val, type) {
            if (type == 1) {
                this.ListInfo.goodsCodes = val
            } else {
                this.ListInfo.styleCodes = val
            }
        },
        async exportProps() {
            this.isExport = true
            await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                data.forEach(item => {
                    item.width = 'auto'
                })
                data.unshift({ label: '', type: 'checkbox' })
                this.tableCols = mergeTableCols(data)
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 5px;
    flex-wrap: wrap;

    .publicCss {
        width: 150px;
        margin: 0 5px 5px 0;
    }
}
</style>
