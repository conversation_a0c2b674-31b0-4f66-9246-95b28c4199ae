<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-input v-model.trim="ListInfo.batchNo" placeholder="批次号" maxlength="50" clearable class="publicCss">
                    <el-button slot="append" icon="el-icon-refresh" v-throttle="1000" @click="refresh"></el-button>
                </el-input>
                <el-select v-model="ListInfo.types" placeholder="类型" class="publicCss" clearable multiple collapse-tags>
                    <el-option label="滞销调出" value="滞销调出" />
                    <el-option label="调回热销" value="调回热销" />
                    <el-option label="热销补货" value="热销补货" />
                    <el-option label="热销备货" value="热销备货" />
                    <el-option label="配件调拨" value="配件调拨" />
                </el-select>
                <chooseWareHouse v-model="ListInfo.outWmsId" placeholder="请选择调出仓" class="publicCss" />
                <chooseWareHouse v-model="ListInfo.inWmsId" placeholder="请选择调入仓" class="publicCss" />
                <el-select v-model="ListInfo.invQtyStatus" placeholder="库存状态" class="publicCss" clearable>
                    <el-option label="正常" value="正常" />
                    <el-option label="不足" value="不足" />
                </el-select>
                <el-select v-model="ListInfo.approveStatus" placeholder="审批状态" class="publicCss" clearable>
                    <el-option label="未发起" value="未发起" />
                    <el-option label="已发起" value="已发起" />
                    <el-option label="已通过" value="已通过" />
                    <el-option label="已拒绝" value="已拒绝" />
                </el-select>
                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss" />
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.styleCodes" v-model="ListInfo.styleCodes"
                    placeholder="款式编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="Callback($event, 1)" title="款式编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="Callback($event, 2)" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable
                    class="publicCss" />
                <number-range :min.sync="ListInfo.outWmsInvMin" :max.sync="ListInfo.outWmsInvMax" min-label="调出仓库存最小值"
                    max-label="调出仓库存最大值" class="publicCss" />
                <number-range :min.sync="ListInfo.inWmsInvMin" :max.sync="ListInfo.inWmsInvMax" min-label="调入仓库存最小值"
                    max-label="调入仓库存最大值" class="publicCss" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-tooltip class="item" effect="dark" content="导出全部" placement="top-start">
                        <el-button type="primary" size="mini" :disabled="isExport"
                            @click="exportProps(false)">导出</el-button>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="剔除所有调拨数量为0的数据" placement="top-start">
                        <el-button type="primary" size="mini" :disabled="isExport1"
                            @click="exportProps(true)">导出2</el-button>
                    </el-tooltip>
                    <el-button type="primary" size="mini" @click="wmsSet"
                        v-if="checkPermission(['api:inventory:GetAllBianMaBrandAsync'])">设置</el-button>
                    <el-button type="primary" @click="startApprover">发起审批</el-button>

                </div>
            </div>
        </template>
        <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            @select="selectChexkBox" :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;"
            height="100%" :showsummary="data.summary ? true : false" :summaryarry="data.summary"
            @sortchange="sortchange" />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog v-dialogDrag :visible.sync="wmsSetVisible" width="70%" :close-on-click-modal="false">
            <setDetails ref="setDetails" @close="close" v-if="wmsSetVisible" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { getAllProBrand, getBianManPositionListV2 } from '@/api/inventory/warehouse'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
import setDetails from "./setDetails.vue";
const api = '/api/verifyOrder/WmsAllot/'
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import { type } from "jquery";
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, setDetails, chooseWareHouse
    },
    props: {
        styleCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            api,
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false,
                summarys: [],
                batchNo: '',
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            ruleForm: {},
            wmsSetVisible: false,
            selectList: [],
            isExport1: false,
        }
    },
    async mounted() {
        this.init()
        this.getCol();
        await this.getLastBatchNo()
        await this.getList()
    },
    methods: {
        selectChexkBox(val) {
            this.selectList = val
        },
        startApprover() {
            if (this.selectList.length == 0) {
                this.$message.error('请选择需要发起审批的数据')
                return
            }
            this.$confirm('此操作将发起审批, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const res = this.selectList.map(item => item.id)
                const { success } = await request.post(`${this.api}triggerApply`, res)
                if (success) {
                    this.$message({
                        type: 'success',
                        message: '发起审批成功!'
                    });
                    this.getList()
                    this.selectList = []
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消'
                });
            });
        },
        async refresh() {
            await this.getLastBatchNo()
            await this.getList()
        },
        async getLastBatchNo() {
            const res = await request.get(`${this.api}GetLastBatchNo`)
            this.ListInfo.batchNo = res ? res.batchNo : ''
        },
        async init() {
            var res2 = await getAllProBrand();
            this.brandlist = res2.data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        close() {
            this.wmsSetVisible = false
        },
        wmsSet() {
            this.wmsSetVisible = true
        },
        Callback(e, val) {
            if (val == 1) {
                this.ListInfo.styleCodes = e
            } else {
                this.ListInfo.goodsCodes = e
            }
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps(isFilterNoneAllocation) {
            if (isFilterNoneAllocation) {
                this.isExport1 = true
            } else {
                this.isExport = true
            }
            await request.post(`${this.api}ExportData`, { ...this.ListInfo, isFilterNoneAllocation }, { responseType: 'blob' }).then(download).finally(() => {
                if (isFilterNoneAllocation) {
                    this.isExport1 = false
                } else {
                    this.isExport = false
                }
            })
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                data.unshift({ type: 'checkbox', label: '' })
                data.forEach(item => {
                    if (item.prop == 'orderNoInner') {
                        item.type = 'orderLogInfo'
                        item.orderType = 'orderNoInner'
                    }
                    if (item.prop == 'proCode') {
                        item.type = 'html'
                        item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
                    }
                })
                this.tableCols = data;
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 5px;
    flex-wrap: wrap;

    .publicCss {
        width: 200px;
        margin: 0 10px 5px 0;
    }
}
</style>
