<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="同步日期">
                    <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" 
                    range-separator="至" start-placeholder="开始同步日期" end-placeholder="结束同步日期" :picker-options="pickerOptions">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.orderNoInner" style="width: 140px" placeholder="内部单号" @keyup.enter.native="onSearch" clearable maxlength="80"/>
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.orderNoOnline" style="width: 140px" placeholder="线上订单号" @keyup.enter.native="onSearch" clearable maxlength="100" />
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.waybillNo" style="width: 140px" placeholder="快递单号" @keyup.enter.native="onSearch" clearable maxlength="100" />
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.platformSite" style="width: 140px" placeholder="站点" @keyup.enter.native="onSearch" clearable maxlength="100" />
                </el-form-item>
                <el-form-item label="称重日期">
                    <el-date-picker style="width: 240px" v-model="filter.timerange2" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" 
                    range-separator="至" start-placeholder="开始称重日期" end-placeholder="结束称重日期" :picker-options="pickerOptions">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.weigher" style="width: 140px" placeholder="称重人" @keyup.enter.native="onSearch" clearable maxlength="100" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false" :loading="listLoading" :tableHandles="tableHandles1">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
        <el-dialog title="导入" :visible.sync="dialogVisibleUpload" width="40%" v-dialogDrag>
            <span>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile" :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="onSubmitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleUpload = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px" v-dialogDrag>
            <orderLogPage ref="orderLogPage" :orderNoInner="sendOrderNoInner" style="z-index:10000;height:600px" />
        </el-dialog>
    </my-container>
</template>

<script>
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import dayjs from "dayjs";
    import { formatTime } from "@/utils";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window"; 
    import orderLogPage from "@/views/order/logisticsWarning/LogisticsEarlyWarLog.vue";
    import {getDeliveryFailOrderPageListAsync,importDeliveryFailOrderAsync } from '@/api/order/orderdeliveryfail';
    const tableCols = [
        { istrue: true, prop: 'syncTime', label: '同步日期', width: '150', sortable: 'custom'},
        { istrue: true, prop: 'deliveryWarehouseName', label: '发货仓', width: '160', sortable: 'custom'},
        { istrue: true, prop: 'platformSite', label: '平台站点', width: '80', sortable: 'custom'},
        { istrue: true, prop: 'orderNoInner', label: '内部单号', width: '110', sortable: 'custom',type:'click',handle:(that,row)=>that.showLogDetail(row) },
        { istrue: true, prop: 'orderNoOnline', label: '线上订单号', width: '150', sortable: 'custom'},
        // { istrue: true, prop: 'payMoney', label: '支付金额', width: '100', sortable: 'custom'},
        { istrue: true, prop: 'waybillNo', label: '快递单号', width: '150', sortable: 'custom',type:'click',handle:(that,row)=>that.onShowLogistics(row)},
        { istrue: true, prop: 'deliveryType', label: '发货类型', width: '80',sortable: 'custom'},
        { istrue: true, prop: 'weighingTime', label: '称重时间', width: '150',sortable: 'custom'},
        { istrue: true, prop: 'weigher', label: '称重人', width: '120',sortable: 'custom'},
        { istrue: true, prop: 'deliveryTime', label: '揽收时间', width: '100',sortable: 'custom'},
        { istrue: true, prop: 'hasExpressFollowUp', label: '是否有物流后续',sortable: 'custom', width: '80', formatter: (row) => { return (row.hasExpressFollowUp == 1 ? "是" : "否") }}, 
        { istrue: true, prop: 'remark', label: '备注', width: '180',sortable: 'custom'}
    ]
    const tableHandles1 = [
     { label: "导入", handle: (that) => that.onImport() }
    ];
    export default {
        name: 'orderdeliveryfail',
        components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow,orderLogPage },
        props: {

        },
        data() {
            return {
                that: this,
                shopList: [],
                filter: {
                    timerange: [
                        formatTime(dayjs().subtract(3, "day"), "YYYY-MM-DD"),
                        formatTime(new Date(), "YYYY-MM-DD"),
                    ],
                    orderdeliveryfail: null,
                    startSyncTime: null,
                    endSyncTime:null,
                    chatUser:null,
                    isKf:null,
                    saleAfterNo:null,
                    waybillNo:null,
                    platformSite:null,
                    startWeighingTime:null,
                    endWeightTime:null,
                    weigher:null,
                    timerange2:[]
                },
                list: [],
                summaryarry: {},
                pager: { OrderBy: "orderNoInner", IsAsc: false },
                tableCols: tableCols,
                total: 0,
                sels: [],
                listLoading: false,
                pageLoading: false,
                pickerOptions: {
                    shortcuts: [
                        {
                            text: '昨天',
                            onClick(picker) {
                                const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
                                const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                                start.setTime(start.getTime());
                                picker.$emit('pick', [start, start]);
                            }
                        }, {
                            text: '近三天',
                            onClick(picker) {
                                const tdate = new Date(new Date().getTime());
                                const end = new Date(new Date(tdate.toLocaleDateString()));
                                const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                                end.setTime(end.getTime() - 3600 * 1000 * 24);
                                picker.$emit('pick', [start, end]);
                            }
                        }, {
                            text: '近一周',
                            onClick(picker) {
                                const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 5);
                                const end = new Date(new Date(tdate.toLocaleDateString()).getTime() + 3600 * 1000 * 24 * 5);
                                const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
                                end.setTime(end.getTime() - 3600 * 1000 * 24);
                                picker.$emit('pick', [start, end]);
                            }
                        }, {
                            text: '近一个月',
                            onClick(picker) {
                                const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 31);
                                console.log("获取前一个月的时间", tdate.getDay());
                                const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
                                const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                                start.setTime(start.getTime() - 3600 * 1000 * 24);
                                end.setTime(end.getTime() - 3600 * 1000 * 24);
                                picker.$emit('pick', [start, end]);
                            }
                        }]
                },
                tableHandles1: tableHandles1,
                dialogVisibleUpload: false,
                dialogVisibleUpload: false,
                fileList: [],
                fileparm: {},
                uploadLoading: false,
                sendOrderNoInner: null,
                dialogHisVisible:false

            };
        },
        async mounted() {
            await this.onSearch()
        },
        methods: {
            //查询第一页
            async onSearch() {
                console.log(this.filter)
                if (!this.filter.timerange) {
                    this.$message({ message: "请选择日期", type: "warning", });
                    return;
                }
                this.$refs.pager.setPage(1)
                await this.getlist();
            },
            //获取查询条件
            getCondition() {
                this.filter.startSyncTime = null;
                this.filter.endSyncTime = null;
                if (this.filter.timerange && this.filter.timerange.length > 1) {
                    this.filter.StartSyncTime = this.filter.timerange[0];
                    this.filter.endSyncTime = this.filter.timerange[1];
                }
                else {
                    this.$message({ message: "请先选择日期", type: "warning" });
                    return false;
                }
                this.filter.startWeighingTime = null;
             this.filter.endWeightTime =null;
                if (this.filter.timerange2 && this.filter.timerange2.length > 1) {
                    this.filter.startWeighingTime = this.filter.timerange2[0];
                    this.filter.endWeightTime = this.filter.timerange2[1];
                }

                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }
                return params;
            },
            //分页查询
            async getlist() {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                console.log(params, 'params')
                this.listLoading = true;
                const res = await getDeliveryFailOrderPageListAsync(params)
                this.listLoading = false;
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data.summary;
                data.forEach(d => {
                    d._loading = false;
                })
                this.list = data;
            },
            //排序查询
            async sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            selectchange: function (rows, row) {
                this.selids = []; console.log(rows)
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },async onImport() {
                this.fileList = [];
                this.dialogVisibleUpload = true;
            } ,async onUploadFile(item) {
                debugger
                if (!item || !item.file || !item.file.size) {
                    this.$message({ message: "请先上传文件", type: "warning" });
                    return false;
                }
                this.uploadLoading = true
                const form = new FormData();
                form.append("upfile", item.file);
                var res = await importDeliveryFailOrderAsync(form);
                if (res?.success)
                    this.$message({ message: "上传成功,正在导入中...", type: "success" });
                this.uploadLoading = false
            },
            onUploadSuccess(response, file, fileList) {
                debugger
                fileList.splice(fileList.indexOf(file), 1);
                this.dialogVisibleUpload = false;
            },
            async onUploadChange(file, fileList) {
                let list = [];
                list.push(file);
                this.fileList = list;
            },
            onUploadRemove(file, fileList) {
                this.fileList = []
            },
            onSubmitUpload() {
                if(this.fileList.length==0)
                {
                    this.$message({ message: "请先上传文件", type: "warning" });
                    return false;
                }
                this.$refs.upload.submit();
            },
            showLogDetail (row) {
                this.dialogHisVisible = true;
                this.sendOrderNoInner = String(row.orderNoInner);
            },
            onShowLogistics (row) {
                let self = this;
                this.$showDialogform({
                    path: `@/views/order/logisticsWarning/DbLogisticsRecords.vue`,
                    title: '物流明细',
                    args: { expressNos:row.waybillNo },
                    height: 300,
               });
            },
        },
    };
</script>

<style lang="scss" scoped></style>
