# yh_vxetable 组件 mergeColumn 功能使用说明

## 功能概述

`mergeRowMethod` 方法已经修改为支持从父组件传入 `mergeColumn` 配置对象，用于指定需要合并的列和合并后的显示值。

## 配置结构

```javascript
mergeColumn: {
  column: ['calculateMonth', 'type', 'regionName', 'deptType', 'deptName'], // 需要合并的列数组
  default: 'type' // 合并后展示的字段，若为空则显示空
}
```

### 参数说明

- `column`: 数组类型，包含需要进行合并的列字段名
- `default`: 字符串类型，指定合并后显示的字段名
  - 如果指定了字段名，合并后会显示该字段的值
  - 如果字段为空或不存在，合并后显示空字符串
  - 如果未指定 default 字段，则显示第一个合并列的值

## 使用示例

### 在父组件中使用

```vue
<template>
  <vxetablebase
    :table-data="tableData"
    :table-cols="tableCols"
    :merge-column="mergeColumn"
    :some-row="somerow"
  />
</template>

<script>
export default {
  data() {
    return {
      // 新的 mergeColumn 配置方式（推荐）
      mergeColumn: {
        column: ['calculateMonth', 'type', 'regionName', 'deptType', 'deptName'],
        default: 'type'
      },
      
      // 旧的 somerow 配置方式（向后兼容）
      somerow: 'regionName,calculateMonth,type',
      
      tableData: [
        {
          calculateMonth: '2024-01',
          type: '类型A',
          regionName: '华东',
          deptType: '销售部',
          deptName: '销售一部',
          value: 100
        },
        {
          calculateMonth: '2024-01',
          type: '类型A',
          regionName: '华东',
          deptType: '销售部',
          deptName: '销售二部',
          value: 200
        }
        // ... 更多数据
      ]
    }
  }
}
</script>
```

## 功能特点

### 1. 行合并逻辑
- `mergeRowMethod` 针对表格的数据行进行合并
- 当配置的多个列的值都相同时，这些行会被合并
- 合并时会检查所有指定列的值是否完全一致

### 2. 汇总行合并逻辑
- `footerSpanMethod` 针对汇总行的列进行合并
- 将指定的多个列在汇总行中合并为一个单元格
- 合并后显示 `default` 字段指定的值

### 3. 向后兼容
- 如果没有配置 `mergeColumn`，会自动使用 `somerow` 配置
- 保持与现有代码的兼容性

### 4. 显示值处理
- 如果配置了 `default` 字段且该字段有值，显示该字段的值
- 如果 `default` 字段为空或不存在，显示空字符串
- 如果没有配置 `default` 字段，显示第一个合并列的值

## 注意事项

1. `mergeColumn.column` 数组中的字段名必须与表格列的 `prop` 属性一致
2. 合并逻辑要求所有指定列的值都相同才会进行合并
3. `default` 字段可以是任意表格数据中存在的字段名
4. 如果数据中某个字段值为 `null` 或 `undefined`，会显示为空字符串
