<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss" />
                <el-tooltip class="item" effect="dark" content="摘品时间" placement="top-start">
                    <i class="el-icon-question" style="margin-right: 10px; padding-top: 5px;"></i>
                </el-tooltip>
                <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="失败开始日期" end-placeholder="失败结束日期" :picker-options="pickerOptions"
                    class="publicCss" :value-format="'yyyy-MM-dd'" @change="changeTime($event, 1)">
                </el-date-picker>
                <el-tooltip class="item" effect="dark" content="失败时间" placement="top-start">
                    <i class="el-icon-question" style="margin-right: 10px; padding-top: 5px;"></i>
                </el-tooltip>
                <el-date-picker v-model="losetimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="无效开始日期" end-placeholder="无效结束日期" :picker-options="pickerOptions"
                    class="publicCss" :value-format="'yyyy-MM-dd'" @change="changeTime($event, 2)">
                </el-date-picker>
                <el-tooltip class="item" effect="dark" content="无效时间" placement="top-start">
                    <i class="el-icon-question" style="margin-right: 10px; padding-top: 5px;"></i>
                </el-tooltip>
                <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss_item" clearable>
                    <el-option v-for="item in statusList" :label="item.label" :value="item.value" :key="item.value" />
                </el-select>
                <el-input v-model.trim="ListInfo.brandName" placeholder="摘品人" maxlength="10" clearable
                    class="publicCss_item" />
                <el-input v-model.trim="ListInfo.styleCode" placeholder="款式编码" maxlength="20" clearable
                    class="publicCss_item" />
                <el-select v-model="ListInfo.deptIds" placeholder="架构" class="publicCss_item" filterable clearable
                    multiple collapse-tags>
                    <el-option v-for="item in deptList" :key="item.deptId" :label="item.fullName" :value="item.deptId">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.title" placeholder="岗位" class="publicCss_item" filterable clearable>
                    <el-option v-for="item in postList" :key="item.label" :label="item.label" :value="item.label">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.region" placeholder="区域" class="publicCss" filterable clearable>
                    <el-option label="义乌" value="义乌" />
                    <el-option label="南昌" value="南昌" />
                    <el-option label="武汉" value="武汉" />
                    <el-option label="深圳" value="深圳" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <div style="margin: 0 5px;">
                    <el-dropdown @command="commandClick">
                        <el-button type="primary">
                            导出<i class="el-icon-arrow-down el-icon--right"></i>
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="导出总表" :disabled="isExport">导出总表</el-dropdown-item>
                            <el-dropdown-item command="导出明细" :disabled="isExport1">导出明细</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
                <!-- <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button> -->
                <el-button type="primary" @click="openSet">提成配置</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :id="'allIdPickingRewards20240807'" :that="that" :isIndex="true" :hasexpand="true"
            :tablefixed="true" :tree-config="{}" @sortchange="sortchange" :tableData="tableData" :tableCols="tableCols"
            :isSelection="false" :isSelectColumn="false" style="width: 100%; margin: 0" :loading="loading"
            :showsummary='true' :summaryarry='summaryarry' :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="120">
                    <template #default="{ row, $index }">
                        <div style="display: flex">
                            <el-button type="text" @click="voidOperate(row.applyId, 1)"
                                v-if="row.applyId && row.status == 1 && checkPermission('Api:Inventory:GoodsCostChg:SetGoodsCostApplyReject')">失败</el-button>
                            <el-button type="text" @click="voidOperate(row.applyId, 2)"
                                v-if="row.applyId && row.status == 1 && checkPermission('Api:Inventory:GoodsCostChg:SetGoodsCostApplyInvalid')">无效</el-button>
                            <el-button type="text" @click="openLogs(row.applyId)" v-if="row.applyId">日志</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog v-dialogDrag title="日志" :visible.sync="openLogsVisable" width="50%">
            <pickingLog v-if="openLogsVisable" :applyId="applyId" />
        </el-dialog>

        <el-dialog v-dialogDrag title="提成配置" :visible.sync="openSetVisable" width="40%">
            <el-row :gutter="20">
                <el-col :span="12">
                    <div style="margin-bottom: 15px">摘品奖励:</div>
                    <div style="display: flex; align-items: center">
                        <div>{{ setInfo.pickingNum }}个编码</div>
                        <!-- 30 -->
                        <el-input-number v-model="setInfo.pickingAmount" :min="0" :max="9999" :controls="false"
                            placeholder="请输入" :precision="0" style="margin: 0 10px" />
                        <div>金额</div>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div style="margin-bottom: 15px">摘品无效:</div>
                    <div style="display: flex; align-items: center">
                        <div>摘品/循环奖励的</div>
                        <el-input-number v-model="setInfo.pickingInvalidMultiple" :min="1" :max="99" :controls="false"
                            placeholder="请输入" :precision="0" style="margin: 0 10px" />
                        <div>倍数</div>
                    </div>
                </el-col>
            </el-row>
            <el-row :gutter="20" style="margin-top: 20px">
                <el-col :span="12">
                    <div style="margin-bottom: 15px">循环摘品奖励:</div>
                    <div style="display: flex; align-items: center">
                        <div>1个编码</div>
                        <!-- 10 -->
                        <el-input-number v-model="setInfo.loopPickingAmount" :min="0" :max="9999" :controls="false"
                            placeholder="请输入" :precision="0" style="margin: 0 10px" />
                        <div>金额</div>
                    </div>
                </el-col>
            </el-row>
            <div class="btnGroup">
                <el-button @click="openSetVisable = false">取消</el-button>
                <el-button type="primary" @click="submit" v-throttle="1000">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import pickingLog from "./pickingLog.vue";
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from "@/utils/tools";
import {
    getGoodsCostPackingTaskRewardPage,
    setGoodsCostApplyReject,
    getGoodsCostApplyPickingCommissionConfig,
    saveGoodsCostApplyPackCommissionConfig,
    exportGoodsCostPackingTaskReward,
    getPurchaseDeptList,
    setGoodsCostApplyInvalid
} from "@/api/inventory/goodscostpricechg";
import dayjs from "dayjs";
import dateRange from "@/components/date-range/index.vue";
const statusList = [
    {
        label: "正常",
        value: 1,
    },
    {
        label: "失败",
        value: -1,
    },
    {
        label: "无效",
        value: -2,
    },
]
const postList = [
    {
        value: '1',
        label: '采购助理'
    },
    {
        value: '6',
        label: '正式采购助理'
    },
    {
        value: '2',
        label: '采购专员'
    },
    {
        value: '3',
        label: '预备采购组长'
    },
    {
        value: '4',
        label: '采购组长'
    },
    {
        value: '5',
        label: '采购主管'
    }
]
const tableCols = [
    { sortable: "custom", width: "auto", align: "center", prop: "brandName", label: "摘品人", treeNode: true, },
    { width: 'auto', align: 'center', prop: 'region', label: '区域', },
    { width: 'auto', align: 'center', prop: 'deptName', label: '架构', },
    { width: 'auto', align: 'center', prop: 'title', label: '岗位', },
    { width: "auto", align: "center", prop: "styleCode", label: "款式编码", },
    { sortable: "custom", width: "auto", align: "center", prop: "packingCodeNum", label: "摘品编码数", },
    { sortable: "custom", width: "auto", align: "center", prop: "bePackingCodeNum", label: "被摘编码数", },
    { width: "auto", align: "center", prop: "cptTime", label: "摘品时间", },
    { width: "auto", align: "center", prop: "failureTime", label: "失败时间", },
    { width: "auto", align: "center", prop: "invalidTime", label: "无效时间", },
    { sortable: "custom", width: "auto", align: "center", prop: "rewardAmount", label: "奖励金额", },
    { sortable: "custom", width: "auto", align: "center", prop: "penaltyAmount", label: "惩罚金额", },
    { sortable: "custom", width: "auto", align: "center", prop: "totalAmount", label: "金额汇总", },
    { width: "auto", align: "center", prop: "status", label: "状态", formatter: (row) => row.status ? statusList.find(item => item.value == row.status).label : '' },
];
export default {
    name: "scanCodePage",
    components: {
        MyContainer,
        vxetablebase,
        dateRange,
        pickingLog,
    },
    data() {
        return {
            postList:postList,
            statusList,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: dayjs().startOf('month').format('YYYY-MM-DD'),
                endDate: dayjs().format('YYYY-MM-DD'),
                failureStartTime: null,
                failureEndTime: null,
                invalidStartTime: null,
                invalidEndTime: null,
                deptIds: [],
                region: null,
                status: 1
            },
            timeRanges: [],
            losetimeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            openLogsVisable: false,
            openSetVisable: false,
            applyId: null,
            summaryarry: {},
            isExport: false,
            isExport1: false,
            setInfo: {
                loopPickingAmount: 10,//循环摘品金额
                loopPickingNum: 0,//循环摘品数量
                pickingAmount: 30,//摘品金额
                pickingInvalidMultiple: 1,//摘品无效倍数
                pickingNum: 1,//摘品数量
            },
            deptList: []
        };
    },
    async mounted() {
        this.getDptList()
        await this.getList();
    },
    methods: {
        async commandClick(e) {
            const exportType = e == '导出总表' ? 0 : e == '导出明细' ? 1 : ''
            if (e == '导出总表') {
                this.isExport = true
            } else if (e == '导出明细') {
                this.isExport1 = true
            }
            const deptIds = this.ListInfo.deptIds?.length ? this.ListInfo.deptIds.join(',') : '';
            await exportGoodsCostPackingTaskReward({ ...this.ListInfo, exportType, deptIds }).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', `摘品奖励${e}` + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    if (e == '导出总表') {
                        this.isExport = false
                    } else if (e == '导出明细') {
                        this.isExport1 = false
                    }
                }
            }).catch(() => {
                if (e == '导出总表') {
                    this.isExport = false
                } else if (e == '导出明细') {
                    this.isExport1 = false
                }
            })
        },
        changeTime(e, val) {
            if (val == 1) {
                this.ListInfo.failureStartTime = e ? e[0] : null
                this.ListInfo.failureEndTime = e ? e[1] : null
            } else if (val == 2) {
                this.ListInfo.invalidStartTime = e ? e[0] : null
                this.ListInfo.invalidEndTime = e ? e[1] : null
            }
        },
        async getDptList() {
            const { data } = await getPurchaseDeptList()
            this.deptList = data
        },
        render(originalBrand, applyId) {
            return (originalBrand === null && applyId !== undefined) ? 'color: red' : 'color: #000'
        },
        async voidOperate(applyId, val) {
            this.$confirm(`此操作将${val == 1 ? '失败' : '无效'}该数据, 是否继续?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let success;
                if (val == 1) {
                    const { success: success1 } = await setGoodsCostApplyReject({ applyId });
                    success = success1
                } else if (val == 2) {
                    const { success: success2 } = await setGoodsCostApplyInvalid({ applyId });
                    success = success2
                }
                if (success) {
                    this.getList();
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
        },
        async submit() {
            const { success } = await saveGoodsCostApplyPackCommissionConfig(this.setInfo);
            if (success) {
                this.$message.success("保存成功");
                this.openSetVisable = false;
                this.getList();
            }
        },
        async openSet() {
            const { data, success } = await getGoodsCostApplyPickingCommissionConfig();
            if (success) {
                this.setInfo = data
            }
            this.openSetVisable = true;
        },
        openLogs(applyId) {
            this.applyId = applyId;
            this.openLogsVisable = true;
        },
        async getList(type) {
            if (type == "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const deptIds = this.ListInfo.deptIds?.length ? this.ListInfo.deptIds.join(',') : '';
                const params = { ...this.ListInfo, deptIds };
                const { data, success } = await getGoodsCostPackingTaskRewardPage(params);
                if (success) {
                    this.tableData = data.list;
                    this.total = data.total;
                    this.summaryarry = data.summary;
                    this.loading = false;
                } else {
                    //获取列表失败
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList();
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList();
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop;
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false;
                this.getList();
            }
        },
    },
};
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 210px;
        margin-right: 5px;
    }

    .publicCss_item {
        width: 160px;
        margin-right: 5px;
    }
}

.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

::v-deep .el-select__tags-text {
    max-width: 35px;
}
</style>
