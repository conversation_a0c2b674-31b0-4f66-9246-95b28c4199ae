<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <el-tabs v-model="activeName" style="height:94%;">
    <el-tab-pane label="成本差商品" name="tab0" style="height: 100%;">
        <costdiffproduct :filter="filter" ref="costdiffproduct" style="height: 100%;"/>
    </el-tab-pane>
    <!-- <el-tab-pane label="成本差订单" name="tab1" style="height: 100%;">
        <costdifforder :filter="filter" ref="costdifforder" style="height: 100%;"/>
    </el-tab-pane> -->
    <el-tab-pane label="成本差分摊表" name="tab2" style="height: 100%;">
        <costdiffdivide :filter="filter" ref="costdiffdivide" style="height: 100%;"/>
    </el-tab-pane>

 

  </el-tabs>

  


  </my-container >

  

 </template>
<script>
import MyContainer from "@/components/my-container";

 import costdiffproduct from '@/views/financial/costdiffproduct'

 import costdifforder from '@/views/financial/costdifforder'

 import costdiffdivide from '@/views/financial/costdiffdivide'
 


export default {
  name: "Users",
  components: { MyContainer,costdiffproduct,costdifforder,costdiffdivide},
  data() {
    return {
      that:this,
      Filter: {
      },
      shopList:[],
      userList:[],
      groupList:[],
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
    };
  },
  async mounted() {
  },
  methods: {
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
