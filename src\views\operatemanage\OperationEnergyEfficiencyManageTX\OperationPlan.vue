<template>
  <my-container v-loading="pageLoading">
  <el-tabs v-model="activeName" style="height:850px;">
    <el-tab-pane label="淘系季度计划" name="tab1" style="height: 100%;"v-if="checkPermission('QuarterlyPlanTx')">
      <QuarterlyPlanTx  ref="QuarterlyPlanTx" style="height: 100%;"/>
  </el-tab-pane>
  <!-- <el-tab-pane label="淘系季度计划详情" name="tab2" style="height: 100%;">
    <QuarterlyPlanDetailsTx ref="QuarterlyPlanDetailsTx" style="height: 100%;"/>
</el-tab-pane> -->
  <el-tab-pane label="淘系季度计划详情--专员" name="tab3" style="height: 100%;" v-if="checkPermission('QuarterlyPlanDetailsCommissionerTx')">
      <QuarterlyPlanDetailsCommissionerTx  ref="QuarterlyPlanDetailsCommissionerTx" style="height: 100%;"/>
  </el-tab-pane>
  <el-tab-pane label="淘系季度计划详情--助理" name="tab4" style="height: 100%;" v-if="checkPermission('QuarterlyPlanDetailsAssistantTx')">
    <QuarterlyPlanDetailsAssistantTx  ref="QuarterlyPlanDetailsAssistantTx" style="height: 100%;"/>
</el-tab-pane>
</el-tabs>
  </my-container >

 </template>
<script>
import MyContainer from "@/components/my-container";
import QuarterlyPlanTx from "./QuarterlyPlanTx.vue";
import QuarterlyPlanDetailsTx from "./QuarterlyPlanDetailsTx.vue";
import QuarterlyPlanDetailsCommissionerTx from "./QuarterlyPlanDetailsCommissionerTx.vue";
import QuarterlyPlanDetailsAssistantTx from "./QuarterlyPlanDetailsAssistantTx.vue";



export default {
  name: "Users",
  components: { MyContainer,QuarterlyPlanTx,QuarterlyPlanDetailsTx,QuarterlyPlanDetailsCommissionerTx,QuarterlyPlanDetailsAssistantTx},
  data() {
    return {
      that:this,
      pageLoading:'',
      filter: {
      },
      shopList:[],
      userList:[],
      groupList:[],
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
      activeName:'tab1'
    };
  },
  mounted() { 
    window.showtab4=this.showtab4
  },
  methods: {
    showtab4(){
     this.activeName=""
    }

  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
