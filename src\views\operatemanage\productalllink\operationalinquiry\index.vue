<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <div style="display: flex; flex-flow: row;">
                    <el-date-picker v-model="filter.inquiryTime" type="daterange" range-separator="至"
                        start-placeholder="询价开始时间" end-placeholder="询价结束时间" :picker-options="pickerOptions"
                        style="width:230px;margin-right: 5px;" :value-format="'yyyy-MM-dd'">
                    </el-date-picker>
                    <el-input v-model="filter.rivalId" clearable placeholder="竞品ID" style="width:150px;margin-right: 5px;" />
                    <el-select v-model="filter.status" clearable placeholder="数据状态" style="width: 140px;margin-right: 5px;" >
                        <el-option label="无" value="0"></el-option>
                        <el-option label="新增询价" value="1"></el-option>
                        <el-option label="询价被驳回" value="98"></el-option>
                        <el-option label="采购已认领" value="6"></el-option>
                        <el-option label="已指派采购" value="7"></el-option>
                        <el-option label="采购已计算利润" value="10"></el-option>
                        <el-option label="已分配运营" value="15"></el-option>
                        <el-option label="已添加到选品" value="20"></el-option>
                    </el-select>
                    <el-select v-model="filter.inquiryOperationsId" clearable filterable placeholder="运营" style="width: 140px;margin-right: 5px;">
                        <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                    <el-select v-model="filter.groupId" clearable filterable placeholder="运营组" style="width: 140px;margin-right: 5px;">
                        <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key" />
                    </el-select>
                    <el-select v-model="filter.platform" clearable filterable placeholder="平台" style="width: 120px;margin-right: 5px;">
                        <el-option v-for="(item) in platformlist" :key="'query-' + item.value" :label="item.label" :value="item.value" />
                    </el-select>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport">导出</el-button>
                    <el-button type="primary" @click="onAddShow">新增询价</el-button>
                </div>
            </el-form>
        </template>

         <vxetablebase :id="'operationalinquiryList20250226'" ref="table" v-loading="listLoading" :that='that'
            :tableData='tableList' :tableCols='tableCols' @sortchange='sortchange' style="width: 100%;  margin: 0" height="100%">
            <template slot="right">
                <vxe-column title="操作" width="160" fixed="right">
                    <template #default="{ row }">
                        <el-button type="text" v-if="!row.hotSaleBrandPushNewId" @click="onEditShow(row)">编辑</el-button>
                        <el-button type="text" v-if="!row.hotSaleBrandPushNewId" @click="onDelete(row)">删除</el-button>
                        <el-button type="text" v-if="!row.hotSaleBrandPushNewId" @click="onConfirm(row)">确认询价</el-button>
                        <el-button type="text" v-if="row.hotSaleBrandPushNewId" @click="showLog(row)">日志</el-button>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="pagechange" @size-change="sizechange" />
        </template>

        <el-dialog :title="saveType" :visible.sync="dialogSaveVisible" width='65%' :close-on-click-modal="false" v-dialogDrag 
            v-loading="dialogSaveLoading" element-loading-text="拼命加载中">
            <el-form ref="addForm" :model="addForm" :rules="addFormRules" label-width="120px">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="rivalId" label="竞品Id">
                            <el-input v-model="addForm.rivalId" auto-complete="off" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="internationalType" label="国内/跨境">
                            <el-select v-model="addForm.internationalType" placeholder="请选择国内/跨境" style="width:100%;">
                                <el-option key="国内" label="国内" :value="0" />
                                <el-option key="跨境" label="跨境" :value="1" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="inquiryPlatform" label="询价平台">
                            <el-select v-model="addForm.inquiryPlatform" placeholder="请选择询价平台" style="width:100%;">
                                <el-option v-for="(item) in platformlist" :key="'save-' + item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="expectedPriceMin" label="期望最低价格">
                            <el-input-number v-model="addForm.expectedPriceMin" type="number" clearable :controls="false"
                                :precision="2" :max="9999999" :min="0" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="expectedPriceMax" label="期望最高价格">
                            <el-input-number v-model="addForm.expectedPriceMax" type="number" clearable :controls="false"
                                :precision="2" :max="9999999" :min="0" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="goodsImg" label="商品图片">
                            <yh-img-upload :limit="1" :value.sync="addForm.goodsImg" @change="goodsImgChange" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    竞品平台
                </el-row>
                <vxe-table ref="platformTable" :data="tablelist">
                    <vxe-column field="platform" width="150" title="平台"></vxe-column>
                    <vxe-column field="price" width="200" title="价格">
                        <template #default="{ row }">
                            <el-input-number v-model="row.price" type="number" clearable :controls="false"
                                :precision="2" :max="9999999" :min="0" />
                        </template>
                    </vxe-column>
                    <vxe-column field="link" title="链接">
                        <template #default="{ row }">
                            <el-input maxlength="300" v-model="row.link" @blur='pasteDescription(row)'></el-input>
                        </template>
                    </vxe-column>
                </vxe-table>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="primary" @click="onSaveData" :loading="saveLoading" >保 存</el-button>
                    <el-button @click="dialogSaveVisible = false">取 消</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="日志" :visible.sync="dialogShowLog" width='80%' :close-on-click-modal="false" v-dialogDrag
            v-loading="logTableLoding" element-loading-text="拼命加载中">
            <el-row style="line-height:35px; font-size: 14px;color: black;margin-bottom: 2px;">
                <el-col :span="6" style="float: left; margin-left: 20px;">
                    <span style="font-weight: bold;">竞品Id：</span>
                    <span>{{ showLogData.goodsCompeteId }}</span>
                </el-col>
            </el-row>
            <el-row>
                <vxetablebase v-if="dialogShowLog" ref="tableLog" :that='that' :isIndex='false' :hasexpand='true'
                    :tableData='showLogData.logList' :tableCols='logTableCols' style="height:450px;"
                    :isSelectColumn="false">
                </vxetablebase>
                <my-pagination ref="logpager" :total="showLogData.logtotal" @get-page="changepage" />
            </el-row>
        </el-dialog>
    </my-container>
</template>
<script>  
    import { 
        getOperationalInquiryPage,
        exportOperationalInquiry,
        saveOperationalInquiry,
        confirmOperationalInquiry,
        delOperationalInquiry,
        rejectOperationalInquiry
    } from '@/api/operatemanage/productalllink/alllink'
    import dayjs from "dayjs";
    import { formatTime, formatLinkProCode } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
    import { pickerOptions, platformlist } from '@/utils/tools';
    import YhImgUpload from '@/components/upload/yh-img-upload.vue';
    import {
        pageHotSaleBrandPushNewLog
    } from '@/api/operatemanage/productalllink/LogisticsAnalyse.js';
    import { getGroupKeyValue } from '@/api/operatemanage/base/product';
    import { getDirectorList } from '@/api/operatemanage/base/shop'

    const formatLinkProCodeurl = (value, url) => {
        var proBaseUrl = url;
        if (proBaseUrl)
            return formatLink(value, proBaseUrl);
        return value;
    }

    const formatLink = (value, url) => {
        if (!value) return ''
        if (!url) return value;

        var html = `<a href="${url}" target="_blank" style="color: #1000ff;">${value}</a>`;
        return html
    }

    const logTableCols = [
        // { istrue: true, prop: 'id', label: '日志主键', width: '160', display: false },
        { istrue: true, prop: 'logContent', label: '事件', align: 'center' },
        { istrue: true, prop: 'opearTime', label: '日期', align: 'center', formatter: (row) => formatTime(row.opearTime, 'YYYY-MM-DD HH:mm:ss') },
        { istrue: true, prop: 'opearName', label: '操作人', align: 'center' }
    ]

    const tableCols = [
        { istrue: true, prop: 'inquiryDate', label: '询价时间', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'rivalId', label: '竞品ID', width: '160', sortable: 'custom', fixed:'left', 
            type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.rivalId) 
        },
        { istrue: true, prop: 'internationalType', label: '国内跨境', width: '150', sortable: 'custom', formatter: (row) => {
            return row.internationalType == 1 ? '跨境' : '国内'
        } },
        { istrue: true, prop: 'statusName', label: '数据状态', width: '120', sortable: 'custom' },
        { istrue: true, prop: 'inquiryOperations', label: '询价运营', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'groupName', label: '运营组', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'inquiryPlatformStr', label: '询价平台', width: '100' },
        { istrue: true, prop: 'expectedPrice', label: '期望价格', width: '150', sortable: 'custom', formatter: (row) => {
            var expectedPrice = '';
            if (row.expectedPriceMin)
                expectedPrice += row.expectedPriceMin;
            if (row.expectedPriceMax)
            {
                if (expectedPrice)
                    expectedPrice += '～';
                if (row.expectedPriceMax)
                    expectedPrice += row.expectedPriceMax;
            }
            return expectedPrice;
        } },
        { istrue: true, prop: 'goodsImg', label: '商品图片', width: '150', type: 'images' },
        { istrue: true, prop: 'pddPrice', label: '拼多多', width: '80', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCodeurl(row.pddPrice, row.pddLink) },
        { istrue: true, prop: 'dyPrice', label: '抖音', width: '80', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCodeurl(row.dyPrice, row.dyLink) },
        { istrue: true, prop: 'tbPrice', label: '淘宝', width: '80', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCodeurl(row.tbPrice, row.tbLink) },
        { istrue: true, prop: 'rejectRemark', label: '驳回备注', width: '120' },
        { istrue: true, prop: 'rejector', label: '驳回人', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'rejectTime', label: '驳回时间', width: '150', sortable: 'custom' }
    ];

    const startTime = formatTime(new Date(), "YYYY-MM-DD");
    const endTime = formatTime(new Date(), "YYYY-MM-DD");
    export default {
        name: "operationalInquiryList",
        components: { MyContainer, vxetablebase, YhImgUpload },
        data() {
            return {
                that: this,
                pickerOptions,
                tableCols,
                logTableCols,
                groupList: [],
                directorlist: [],
                filter: {
                    inquiryTime: [startTime, endTime],
                    startDate: null,
                    endDate: null,
                    rivalId: null,
                    status: null,
                    inquiryOperationsId: null,
                    groupId: null,
                    platform: null,
                    currentPage: 1,
                    pageSize: 50,
                    orderBy: 'createdTime',
                    isAsc: false
                },
                tableList: [],
                total: 0,
                listLoading: false,
                saveType: '新增询价',
                dialogSaveVisible: false,
                dialogSaveLoading: false,
                saveLoading: false,
                addForm: {
                    id: null,
                    rivalId: null,
                    internationalType: null,
                    inquiryPlatform: null,
                    expectedPriceMin: undefined,
                    expectedPriceMax: undefined,
                    goodsImg: null,
                    pddPrice: null,
                    pddLink: null,
                    dyPrice: null,
                    dyLink: null,
                    tbPrice: null,
                    tbLink: null
                },
                addFormRules: {
                    rivalId: [{ required: true, message: '请输入竞品Id', trigger: 'blur' }],
                    internationalType: [{ required: true, message: '请选择国内/跨境', trigger: 'blur' }],
                    inquiryPlatform: [{ required: true, message: '请选择询价平台', trigger: 'blur' }],
                    goodsImg: [{ required: true, message: '请上传商品图片', trigger: 'blur' }]
                },
                tablelist: [
                    {
                        platform: '拼多多',
                    },
                    {
                        platform: '抖音',
                    },
                    {
                        platform: '淘宝',
                    },
                ],
                platformlist,
                logTableLoding: false,
                dialogShowLog: false,
                logfliter: {
                    "currentPage": 1,
                    "pageSize": 50,
                    "isAsc": true,
                },
                showLogData: {
                    goodsCompeteId: null,
                    groupName: null,
                    hotSaleBrandPushNewId: null,
                    logList: [],
                    logtotal: 0
                }
            };
        },
        async mounted() {
            const res = await getGroupKeyValue({});
                this.groupList = res.data;
            
            var res3 = await getDirectorList();
            this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });

            await this.onSearch();
        },
        methods: {
            async onSearch() {
                await this.getList();
            },
            async getList() {
                if (this.filter.inquiryTime && this.filter.inquiryTime.length > 1){
                    this.filter.startDate = this.filter.inquiryTime[0];
                    this.filter.endDate = this.filter.inquiryTime[1];
                }else{
                    this.filter.startDate = null;
                    this.filter.endDate = null;
                }

                var params = {
                    ...this.filter
                }
                this.listLoading = true;
                var res = await getOperationalInquiryPage(params);
                this.tableList = res?.data?.list;
                this.total = res?.data?.total;
                this.listLoading = false;
            },
            async onExport() {
                if (this.filter.inquiryTime.length > 1){
                    this.filter.startDate = this.filter.inquiryTime[0];
                    this.filter.endDate = this.filter.inquiryTime[1];
                }else{
                    this.filter.startDate = null;
                    this.filter.endDate = null;
                }

                var params = {
                    ...this.filter
                }
                this.listLoading = true;
                var res = await exportOperationalInquiry(params);
                this.listLoading = false;
            },
            onAddShow() {
                this.saveType = '新增询价';
                this.addForm.id = null;
                this.addForm.rivalId = null;
                this.addForm.internationalType = null;
                this.addForm.inquiryPlatform = null;
                this.addForm.expectedPriceMin = null;
                this.addForm.expectedPriceMax = null;
                this.addForm.goodsImg = null;
                this.addForm.pddPrice = undefined;
                this.addForm.pddLink = null;
                this.addForm.dyPrice = undefined;
                this.addForm.dyLink = null;
                this.addForm.tbPrice = undefined;
                this.addForm.tbLink = null;
                this.tablelist = [
                    {platform: '拼多多'},
                    {platform: '抖音'},
                    {platform: '淘宝'},
                ];
                // this.$refs.platformTable.reloadData();
                this.dialogSaveVisible = true;
            },
            onEditShow(row) {
                this.saveType = '编辑询价';
                this.addForm.id = row.id;
                this.addForm.rivalId = row.rivalId;
                this.addForm.internationalType = row.internationalType;
                this.addForm.inquiryPlatform = row.inquiryPlatform;
                this.addForm.expectedPriceMin = row.expectedPriceMin;
                this.addForm.expectedPriceMax = row.expectedPriceMax;
                this.addForm.goodsImg = row.goodsImg;
                this.addForm.pddPrice = row.pddPrice;
                this.addForm.pddLink = row.pddLink;
                this.addForm.dyPrice = row.dyPrice;
                this.addForm.dyLink = row.dyLink;
                this.addForm.tbPrice = row.tbPrice;
                this.addForm.tbLink = row.tbLink;
                this.tablelist = [
                    {platform: '拼多多',price:row.pddPrice,link:row.pddLink },
                    {platform: '抖音',price:row.dyPrice,link:row.dyLink},
                    {platform: '淘宝',price:row.tbPrice,link:row.tbLink},
                ];
                // this.$refs.platformTable.reloadData();
                this.dialogSaveVisible = true;
            },
            onDelete(row) {
                this.$confirm("确定要删除吗?", "提示", { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning" })
                .then(async () => {
                    let param = { id: row.id };
                    let res = await delOperationalInquiry(param);
                    if (res?.success == true) {
                        this.onSearch();
                        this.$message({ type: "success", message: "删除成功!" });
                    }
                });
            },
            onConfirm(row) {
                this.$confirm("确定询价吗?", "提示", { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning" })
                .then(async () => {
                    let param = { id: row.id };
                    let res = await confirmOperationalInquiry(param);
                    if (res?.success == true){
                        this.onSearch();
                        this.$message({ type: "success", message: "确定成功!" });
                    }
                });
            },
            async pagechange(val) {
                this.filter.currentPage = val;
                await this.getList();
            },
            async sizechange(val) {
                this.filter.currentPage = 1;
                this.filter.pageSize = val;
                await this.getList();
            },
            async sortchange({ order, prop }) {
                if (prop) {
                    this.filter.orderBy = prop
                    this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
                    await this.getList();
                }
            },
            async onSaveData() {
                this.saveLoading = true;
                this.tablelist.map(item => {
                    if(item.platform == "拼多多"){
                        this.addForm.pddPrice = item.price;
                        this.addForm.pddLink = item.link;
                    }else if(item.platform == "抖音"){
                        this.addForm.dyPrice = item.price;
                        this.addForm.dyLink = item.link;
                    }else if(item.platform == "淘宝"){
                        this.addForm.tbPrice = item.price;
                        this.addForm.tbLink = item.link;
                    }
                });

                var param = {
                    ...this.addForm
                };
                // console.log('param',param);
                var res = await saveOperationalInquiry(param);
                if (res?.success == true) {
                    this.$message({ type: 'success', message: '保存成功' });
                    this.dialogSaveVisible = false;
                    await this.getList();
                }
                this.saveLoading = false;
            },
            pasteDescription(val) {
                let _this = this;
                // setTimeout(() => {
                    if (val.link) {
                        let ischese = _this.containsChinese(val.link);
                        if (!ischese) {
                            val.link = '';
                            this.$message({ type: 'error', message: '请填写正确的链接' });
                        }
                    }
                // }, 600);
            },
            containsChinese(str) {
                // const regex = /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/;
                // return regex.test(str);
                // const urlPattern = new RegExp('^(https?):\\/\\/[^\s/$.?#].[^\s]*$');
                // return urlPattern.test(str);
                try {
                    new URL(str);
                    return true;
                } catch (error) {
                    return false;
                }
            },
            async showLog(row) {
                this.showLogData.goodsCompeteId = row.rivalId;
                this.showLogData.groupName = row.groupName;
                this.showLogData.logList = [];
                this.dialogShowLog = true;
                let params = {
                    ...this.logfliter,
                    "hotSaleBrandPushNewId": row.hotSaleBrandPushNewId
                };
                this.showLogData.hotSaleBrandPushNewId = row.hotSaleBrandPushNewId;

                this.logTableLoding = true;
                let res = await pageHotSaleBrandPushNewLog(params);
                this.logTableLoding = false;
                this.showLogData.logList = res?.data?.list;
                this.showLogData.logtotal = res?.data?.total;
                console.log('showLogData', this.showLogData)
                console.log('data',res?.data);
            },
            async changepage() {
                this.dialogShowLog = true;
                var pager = this.$refs.logpager.getPager();
                let params = {
                    ...this.logfliter,
                    ...pager,
                    "hotSaleBrandPushNewId": this.showLogData.hotSaleBrandPushNewId
                };

                this.logTableLoding = true;
                let res = await pageHotSaleBrandPushNewLog(params);
                this.logTableLoding = false;
                this.showLogData.logList = res?.data?.list;
                this.showLogData.logtotal = res?.data?.total;
            },
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
<style >
    .el-image__inner {
        max-width: 50px;
        max-height: 50px;
    }
</style>