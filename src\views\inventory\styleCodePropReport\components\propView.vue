<template>
    <MyContainer>
        <template #header>
            <div class="header_top">
                <el-radio-group v-model="ListInfo.popupType" @input="onDimensionSwitching">
                    <el-radio :label="1">系列编码维度</el-radio>
                    <el-radio :label="6">商品编码维度</el-radio>
                    <el-radio :label="3">运营平台维度</el-radio>
                    <el-radio :label="4">一级类目维度</el-radio>
                    <el-radio :label="5">二级类目维度</el-radio>
                </el-radio-group>
                <el-button type="primary" style="margin-left: 10px;" @click="getList('search')">搜索</el-button>
            </div>
            <div class="top">
                <el-select v-if="ListInfo.popupType == 1" v-model="ListInfo.styleCodeList" @change="selectedMethod($event, 'styleCodeList')"
                    filterable remote reserve-keyword placeholder="系列编码" clearable :remote-method="remoteMethod"
                    :loading="searchloading" class="publicCss" multiple collapse-tags :key="styleCodeListKey">
                    <el-option v-for="(item, i) in options" :key="'styleCodeList' + i + 1" :label="item" :value="item">
                    </el-option>
                </el-select>
                <el-select v-if="ListInfo.popupType == 6" v-model="ListInfo.styleCodeing" @change="selectedMethod($event, 'styleCodeing')"
                    filterable remote reserve-keyword placeholder="系列编码" clearable :remote-method="remoteMethod"
                    :loading="searchloading" class="publicCss" :key="styleCodeingKey">
                    <el-option v-for="(item, i) in options" :key="'styleCodeing' + i + 1" :label="item" :value="item">
                    </el-option>
                </el-select>
                <el-select v-if="ListInfo.popupType == 6" v-model="ListInfo.goodsCode" multiple collapse-tags @change="selectedMethod($event, 'goodsCode')"
                    filterable remote reserve-keyword placeholder="商品编码" clearable
                    :loading="searchloading" class="publicCss" :key="goodsCodeListKey">
                    <el-option v-for="(item, i) in filterList.goodsCodeList" :key="'goodsCode' + i + 1" :label="item" :value="item">
                    </el-option>
                </el-select>
                <el-select class="publicCss" v-model="ListInfo.bzCategory1" placeholder="一级类目" :collapse-tags="true" @change="selectedMethod($event, 'bzCategory1')"
                  remote  :remote-method="remoteMethodCategoryName1s" clearable filterable multiple v-if="ListInfo.popupType == 4">
                  <el-option v-for="(item, i) in filterList.categoryName1s" :key="'categoryName1Level' + i + 1" :label="item"
                    :value="item" />
                </el-select>
                <el-select class="publicCss" v-model="ListInfo.bzCategory2" placeholder="二级类目" :collapse-tags="true" @change="selectedMethod($event, 'bzCategory2')"
                remote  :remote-method="remoteMethodCategoryName2s" clearable filterable multiple v-if="ListInfo.popupType == 5">
                  <el-option v-for="(item, i) in filterList.categoryName2s" :key="'categoryName2Level' + i + 1" :label="item"
                    :value="item" />
                </el-select>
                <el-select v-model="ListInfo.platform" placeholder="平台" :collapse-tags="true" filterable multiple @change="selectedMethod($event, 'platform')"
                    class="publicCss" clearable v-if="ListInfo.popupType == 3">
                    <el-option v-for="item in platformdata" :label="item.label" :value="item.value" :key="item.value" />
                </el-select>
                <!-- <el-select filterable v-model="ListInfo.groupIds" collapse-tags clearable placeholder="运营组"
                    style="width: 200px" multiple v-if="ListInfo.popupType == 3">
                    <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select> -->
            </div>
            <!-- <div class="father_bottom">
              <div class="father_bottom_topWord">已选择</div>
              <div style="width: 100%">
                  <el-tag v-for="(item, index) in selectedData" :key="index" closable type="" style="margin: 5px;"
                      @close="delprops(item, index)">{{
                          item
                      }}</el-tag>
              </div>
            </div> -->
        </template>
        <div v-loading="chatProp.chatLoading" style="height: 100%;">
        <buschar v-if="!chatProp.chatLoading" :analysis-data="chatProp.data" />
        </div>
    </MyContainer>
</template>

<script>
import { getBusinessCategorySelectData } from '@/api/operatemanage/base/category'
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import MyContainer from "@/components/my-container";
import { formatPlatform, platformlist } from '@/utils/tools'
import buschar from '@/components/Bus/buschar'
import {
    getContinuLossesStyleCode,
    getGoodsCodeByStyleCode,
    getContinuLossesStyleCodeReportAnalysis
} from '@/api/operatemanage/continuLosses'
import dayjs from 'dayjs'
export default {
    name: "scanCodePage",
    components: {
        MyContainer, buschar
    },
    data() {
        return {
            styleCodeListKey: 0,
            goodsCodeListKey: 0,
            styleCodeingKey: 0,
            selectedData: [],
            platformlist,
            platformdata: [],
            ListInfo: {
                styleCodeList: [],
                styleCodeing: null,
                popupType: 1,
                goodsCode: [],
                bzCategory1: [],
                bzCategory2: [],
                platform: [],
                // groupIds: [],
                styleCode: [],
            },
            loading: true,
            grouplist: [],
            options: [],
            searchloading: false,
            filterList: {
                bussinessCategoryNames: [],
                categoryName1s: [],
                categoryName2s: [],
                goodsCodeList: [],
            },
            // chatProp: [],
            chatLoading: true,
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: []// 趋势图数据
            },
        }
    },
    async mounted() {
        this.init()
        await this.getList()
    },
    methods: {
      // 切换维度,清空数据
      async onDimensionSwitching(e) {
        this.selectedData = []
        this.ListInfo.styleCodeList = []
        this.ListInfo.styleCodeing = null
        this.ListInfo.styleCode = []
        this.ListInfo.goodsCode = []
        this.ListInfo.bzCategory1 = []
        this.ListInfo.bzCategory2 = []
        this.ListInfo.platform = []
        this.ListInfo.popupType = e
        this.options = []
        this.$forceUpdate();
        await this.getList('search')
      },
      async selectedMethod(e, type) {
        if (type === 'styleCodeList') {
          this.styleCodeListKey += 1;
          this.ListInfo.styleCodeList = e;
          this.selectedData = e;
        } else if (type === 'styleCodeing') {
          this.styleCodeingKey += 1;
          const params = { styleCode: e }
          const { data,success } = await getGoodsCodeByStyleCode(params)
          if(success) {
            this.filterList.goodsCodeList = data
          }
          this.ListInfo.goodsCode = []
        } else if (type === 'goodsCode') {
          this.goodsCodeListKey += 1;
          this.selectedData = [...new Set([...this.selectedData, ...e])];
          if (e.ob && this.selectedData.ob) {
            this.selectedData.ob = mergeObservers(e.ob, this.selectedData.ob);
          } else if (e.ob) {
            this.selectedData.ob = e.ob;
          }
        } else if (type === 'bzCategory1') {
          this.selectedData = e;
        } else if (type === 'bzCategory2') {
          this.selectedData = e;
        } else if (type === 'platform') {
          this.selectedData = e.map(item => formatPlatform(item))
        }
      },
      remoteMethodCategoryName1s(query) {
        if (!query) {
            return;
          }
          setTimeout(async () => {
            const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 2, categoryName: query })
            this.filterList.categoryName1s = res.data
          }, 200)
        },
        remoteMethodCategoryName2s(query) {
          if (!query) {
            return;
          }
          setTimeout(async () => {
            const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 3, categoryName: query })
            this.filterList.categoryName2s = res.data
          }, 200)
        },
        delprops(item, i) {
            this.selectedData.splice(i, 1)
            if (this.ListInfo.popupType == 3) {
                this.ListInfo.platform = this.selectedData.map(item => platformlist.find(f => f.label == item).value)
            }
        },
        goodScodeRemoteMethod(styleCode) {
            if (!this.ListInfo.styleCode) return this.$message.error('请先选择系列编码')
            this.searchloading == true;
            setTimeout(async () => {
                const params = { styleCode: styleCode }
                const res = await getBusinessCategorySelectData(params)
                this.searchloading = false
                this.filterList.goodsCodeList = res.data
            }, 200)
        },
        remoteMethod(styleCode) {
            if (styleCode !== '') {
                this.searchloading == true
                this.options = [];
                setTimeout(async () => {
                    const params = {styleCode: styleCode}
                    const { data } = await getContinuLossesStyleCode(params)
                    this.searchloading = false
                    this.options = data
                    this.ListInfo.styleCode = this.ListInfo.styleCode ? this.ListInfo.styleCode : []
                    this.ListInfo.styleCodeing = null
                    this.filterList.goodsCodeList = []
                    this.ListInfo.goodsCode = []
                    this.$forceUpdate();
                }, 200)
            }
            else {
                this.options = []
            }
        },
        async init() {
            this.platformdata = this.platformlist.filter(item => item.label !== '希音' && item.label !== '拼多多跨境')
            const { data } = await getDirectorGroupList();
            this.grouplist = data?.map(item => { return { value: item.key, label: item.value }; });
        },
        async getList() {
            this.chatProp.chatLoading = true
            let bzCategory2 = this.ListInfo.bzCategory2
            let styleCodeList = this.ListInfo.styleCodeList
            let bzCategory1 = this.ListInfo.bzCategory1
            let platform = this.ListInfo.platform
            let groupIdList = this.ListInfo.groupIdList
            let goodsCode = this.ListInfo.goodsCode
            let styleCode = null
            //直接使用join方法会转为字符串，因为双向绑定了数据会回显失败，所以使用了let变量
            if(this.ListInfo.popupType == 1){
              styleCode = styleCodeList?.length ? styleCodeList.join(',') : null
            } else if(this.ListInfo.popupType == 6){
              styleCode = this.ListInfo.styleCodeing
            }
            bzCategory1 = bzCategory1?.length ? bzCategory1.join(',') : null
            bzCategory2 = bzCategory2?.length ? bzCategory2.join(',') : null
            this.ListInfo.groupId = groupIdList?.length ? groupIdList.join(',') : null
            platform = platform?.length ? platform.join(',') : null
            goodsCode = goodsCode ? goodsCode.join(',') : null
            const params = { ...this.ListInfo, bzCategory1, bzCategory2, platform, goodsCode, styleCode}
            const { data, success } = await getContinuLossesStyleCodeReportAnalysis(params)
            if (success) {
              this.$nextTick(() => {
                this.chatProp.data = data
              })
            }
            this.chatProp.chatLoading = false
        }
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.header_top {
    margin-bottom: 10px;
    display: flex;
    padding-right: 10px;
    align-items: center;
}
.father_bottom {
    height: 80px;
    overflow: auto;
    border: 1px solid #ccc;
    margin: 10px 0;

    .father_bottom_topWord {
        text-align: center;
    }
}
::v-deep .el-select__tags-text {
  max-width: 70px;
}
</style>
