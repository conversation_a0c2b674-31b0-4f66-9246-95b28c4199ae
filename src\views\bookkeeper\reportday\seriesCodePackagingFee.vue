<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-select filterable v-model="ListInfo.platForm" placeholder="请选择平台"  collapse-tags
            clearable style="width: 160px">
              <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-select filterable v-model="ListInfo.feeType" placeholder="请选择类型"  collapse-tags
            clearable style="width: 160px">
              <el-option  label="日报" value="1" />
              <el-option  label="月报" value="2" />
            </el-select>
        <el-input v-model.trim="ListInfo.serialCoding" placeholder="系列编码" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.wmsName" placeholder="仓库" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.goodsCode" placeholder="包材" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" @click="onAddnewMethod">新增</el-button>
      </div>
    </template>
    <vxetablebase ref="table" :id="'seriesCodePackagingFee202111241616'" tablekey="'seriesCodePackagingFee202111241616'"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="120" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;align-items: center;">
              <el-button type="text" @click="onEditMethod(row)">编辑</el-button>
              <el-button type="text" style="color: red;" @click="onDeleteMethod(row)">删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="editName" :visible.sync="editdialogVisible" width="25%" v-dialogDrag>
      <div style="height: 280px;width: 100%;" v-loading="editloading">
        <el-form :model="editform" ref="editform" :rules="editrules" label-width="100px" class="demo-ruleForm">
          <el-form-item label="系列编码" prop="serialCoding">
            <el-input v-model.trim="editform.serialCoding" placeholder="请输入系列编码" maxlength="50" clearable
              :disabled="editName == '编辑'" class="editCss" />
          </el-form-item>
          <el-form-item label="仓库" prop="wmsName">
            <el-input v-model.trim="editform.wmsName" placeholder="请输入仓库" maxlength="50" clearable
              :disabled="editName == '编辑'" class="editCss" />
          </el-form-item>
          <el-form-item label="包材" prop="goodsCode">
            <el-input v-model.trim="editform.goodsCode" placeholder="请输入包材" maxlength="50" clearable
              :disabled="editName == '编辑'" class="editCss" />
          </el-form-item>
          <el-form-item label="均价" prop="packAvg">
            <el-input-number v-model="editform.packAvg" placeholder="请输入均价" :min="0" :max="99999999999999"
              :controls="false" class="editCss" />
          </el-form-item>
          <el-form-item label="平台" prop="packAvg">
            <el-select filterable v-model="editform.platForm" placeholder="请选择平台"  :disabled="editName == '编辑'"   collapse-tags
            clearable style="width: 160px">
              <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="类型" prop="packAvg">
            <el-select filterable v-model="editform.feeType" placeholder="请选择类型"  :disabled="editName == '编辑'"   collapse-tags
            clearable style="width: 160px">
              <el-option  label="日报" value="1" />
              <el-option  label="月报" value="2" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editdialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSaveMethod">确 定</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import dayjs from 'dayjs'
import { importSeriesCodePackFeesync, getSeriesCodePackFeeList, addorEditSeriesCodePackFee, deleteSeriesCodePackFeeBatchAsync } from '@/api/bookkeeper/reportdayV2'
import { formatPlatform,platformlist} from "@/utils/tools";

const tableCols = [
{ istrue: true, prop: 'platForm', fix: true, label: '平台', width: '60', sortable: 'custom', formatter: (row) => formatPlatform(row.platForm), type: 'custom' },
{ sortable: 'custom', width: '300', align: 'center', prop: 'feeType', label: '类型', formatter: (row) => row.feeType==1?'日报':'月报' },
{ sortable: 'custom', width: '300', align: 'center', prop: 'serialCoding', label: '系列编码', },
  { sortable: 'custom', width: '300', align: 'center', prop: 'wmsName', label: '仓库', },
  { sortable: 'custom', width: '300', align: 'center', prop: 'goodsCode', label: '包材', },
  { sortable: 'custom', width: '300', align: 'center', prop: 'packAvg', label: '均价', },
]
export default {
  name: "seriesCodePackagingFee",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      editloading: false,
      platformlist:platformlist,
      editform: {
        serialCoding: null,
        wmsName: null,
        packAvg: null,
        goodsCode: null,
        id: 0,
      },
      editdialogVisible: false,
      editName: '新增',
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        wmsName: null,
        serialCoding: null,
        goodsCode: null,
      },
      editrules: {
        serialCoding: [{ required: true, message: '请输入系列编码', trigger: 'blur' }],
        wmsName: [{ required: true, message: '请输入仓库', trigger: 'blur' }],
        packAvg: [{ required: true, message: '请输入均价', trigger: 'blur' }],
        goodsCode: [{ required: true, message: '请输入包材', trigger: 'blur' }],
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async onSaveMethod() {
      if (!this.editform.serialCoding || !this.editform.wmsName || !this.editform.packAvg || !this.editform.goodsCode) {
        this.$message({ message: "请填写完整信息", type: "warning" });
        return false;
      }
      this.editloading = true
      var res = await addorEditSeriesCodePackFee(this.editform)
      this.editloading = false
      if (res?.success) {
        this.$message({ message: "保存成功", type: "success" });
        this.editdialogVisible = false
        await this.getList()
      }
    },
    onAddnewMethod() {
      this.openEditDialog('新增', {
        serialCoding: null,
        wmsName: null,
        packAvg: null,
        goodsCode: null,
        id: 0,
      });
    },
    onEditMethod(row) {
      const clonedRow = JSON.parse(JSON.stringify(row));
      this.openEditDialog('编辑', clonedRow);
    },
    openEditDialog(editName, formData) {
      this.editName = editName;
      this.editform = { ...formData };
      this.editform.feeType=formData.feeType+'';
      this.editdialogVisible = true;
    },
    onDeleteMethod(row) {
      this.$confirm('是否删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        var res = await deleteSeriesCodePackFeeBatchAsync({ ...row })
        if (res?.success) {
          this.$message({ message: "删除成功", type: "success" });
          await this.getList()
        }
      }).catch(() => { });
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importSeriesCodePackFeesync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getSeriesCodePackFeeList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 5px;
  }
}

.editCss {
  width: 80%;
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

::v-deep .el-input-number.is-without-controls .el-input__inner {
  padding-left: 5px;
}
</style>
