<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <template #header>
        <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent></el-form>
      </template>
      <!--列表-->
      <ces-table v-if="showtable" ref="table" :that="that" :isIndex="true" :hasexpand="false" @sortchange="sortchange"
        :tableData="pddcontributeinfolist" @select="selectchange" :isSelection="false" :tableCols="tableCols"
        :loading="listLoading" :summaryarry='summaryarry'>
        <el-table-column type="expand">
          <template slot-scope="props">
            <div>
              <el-table :data="props.row.detaildata" style="width: 100%">
                <el-table-column v-for="col in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <template slot="extentbtn">
          <el-button-group>
           
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="Filter.proCode" maxlength="50" clearable placeholder="商品编码" style="width:150px;" />
            </el-button>
           
            <el-button type="primary" @click="onSearch">查询</el-button>
            <!-- <el-button type="primary" @click="onstartImport">导入</el-button>
            <el-button type="primary" @click="onExportType">导出</el-button> -->
          </el-button-group>
        </template>
      </ces-table>
      <!--分页-->
      <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
      </template>
    </my-container>
  </template>
  <script>
  
  import { getSaleAfterTxList ,exportSaleAfterTxList} from '@/api/bookkeeper/reportday'
  import { importSaleAfterTx } from '@/api/bookkeeper/import'
  import dayjs from "dayjs";
  import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
  import cesTable from "@/components/Table/table.vue";
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  import { formatPlatform, formatLinkProCode, formatTime } from "@/utils/tools";
  import { getListByStyleCode } from "@/api/inventory/basicgoods"
    
  const startDate = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
  const endDate = formatTime(new Date(), "YYYY-MM-DD");
  
  export default {
    name: "AfterSaleTx",
    components: {
      MyContainer,
      MyConfirmButton,
      MySearch,
      MySearchWindow,
      cesTable,
    },
    data() {
      return {
        onExportDialogVisible: false,
        importfilter: {
          version: ''
        },
        that: this,
        Filter: {
          platform: 1,
          proCode: null,
          startTime: null,
          timerange: [startDate, endDate],
          endTime: null,
          selectType: "1",
          ExportType: '',
          styleCode: null,
          SumType:null
        },
        shopList: [],
        styleCode: null,
        userList: [],
        groupList: [],
        pddcontributeinfolist: [],
        tableCols: this.gettableCols(),
        total: 0,
        summaryarry: {},
        pager: { OrderBy: "procode", IsAsc: false },
        sels: [], // 列表选中列
        listLoading: false,
        pageLoading: false,
        selids: [],
        fileList: [],
        dialogVisible: false,
        uploadLoading: false,
        showtable: true
      };
    },
    async created() { 
    //   await this.getShopList();
      
    },  
    methods: {
      gettableCols() {
        return [
        { istrue: true,  prop: 'sku_ids', label: '调拨编码', sortable: 'custom', width: '150' },
  { istrue: true,  prop: 'qtytotal',  label: '调拨数量', width: '100',sortable: 'custom' },
  { istrue: true,  prop: 'expiration_date', label: '有效期', sortable: 'custom', width: '150' },
  { istrue: true,  prop: 'warehouseName', label: '调出仓', sortable: 'custom', width: '150'},
  { istrue: true,  prop: 'link_warehouseName', label: '调入仓', sortable: 'custom', width: '150' },
  { istrue: true,  prop: 'receiver_name', label: '收货人', sortable: 'custom', width: '150',},
  { istrue: true,  prop: 'applicant', label: '申请人', sortable: 'custom', width: '150', },
  { istrue: true,  prop: 'io_date', label: '申请时间', sortable: 'custom', width: '150', },
  { istrue: true,  prop: 'status', label: '状态', sortable: 'custom', width: '150' ,formatter:(row)=>row.status=="-1"?"拒绝":row.status=="0"?"待申请":row.status=="1"?"申请中":row.status=="2"?"调拨已撤销":row.status=="3"?"调拨成功":row.status=="4"?"调拨部分成功":row.status=="5"?"确认调拨失败":row.status=="6"?"创建调拨失败":row.status=="7"?"撤销":""},
        ]
      },
      showClo(){
        return this.Filter.startTime==this.Filter.endTime;
      },
      changeSelectType() { 
        this.getList();
      },
      sortchange(column) {
        if (!column.order) this.pager = {};
        else
          this.pager = {
            OrderBy: column.prop,
            IsAsc: column.order.indexOf("descending") == -1 ? true : false,
          };
        this.onSearch();
      },
      onSearch() {
        this.$refs.pager.setPage(1);
        this.getList();
      },
      async getList() {
          this.Filter.StartTime = null;
          this.Filter.EndTime = null;
        
          if (this.Filter.timerange) {
            this.Filter.StartTime = this.Filter.timerange[0];
            this.Filter.EndTime = this.Filter.timerange[1];
          }
       
        const para = { ...this.Filter };
        let pager = this.$refs.pager.getPager();
        const params = {
          ...pager,
          ...this.pager,
          ...para,
        };
        this.listLoading = true;
        const res = await getSaleAfterTxList(params);
        if (!res.success) {
          this.listLoading = false;
          return;
        }
        this.listLoading = false;
        this.total = res.data.total;
        this.pddcontributeinfolist = res.data.list;
        this.summaryarry = res.data.summary;
      },
      selectchange: function (rows, row) {
        this.selids = [];
        rows.forEach((f) => {
          this.selids.push(f.id);
        });
      },
    },
  };
  
  
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
    