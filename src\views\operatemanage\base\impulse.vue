<template>
  <container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
         <el-form-item label="商品编码:">
          <el-input v-model="filter.goodsCode" placeholder="店铺编码"/>
        </el-form-item>
        <el-form-item label="申请人:">
          <el-input v-model="filter.createdUserName" placeholder="店铺编码"/>
        </el-form-item>
        <el-form-item label="平台:">
          <el-select v-model="filter.platform" placeholder="请选择平台" style="width: 100%" @change='onchangeplatform'>
            <el-option v-for="item in platFormList" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="所属店铺:">
          <el-select v-model="filter.shopCode" placeholder="请选择店铺" style="width: 100%">
            <el-option label="所有" value></el-option>  
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.id"/>
          </el-select>
        </el-form-item>
       <el-form-item label="状态:">
          <el-select v-model="filter.Status" placeholder="请选择" style="width: 100%">
            <el-option label="所有" value></el-option>
            <el-option label="待审核" value="0"></el-option>
            <el-option label="审核通过" value="1"></el-option>
            <el-option label="待审核不通过" value="2"></el-option>
            <el-option label="废弃" value="-1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
 
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
       :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
    </ces-table>
    
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>

    <el-drawer :title="formtitle" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="addFormVisible"
          direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
    <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
      <div class="drawer-footer">
        <el-button @click.native="addFormVisible = false">取消</el-button>
        <my-confirm-button type="submit"  :loading="addLoading" @click="onAddSubmit" />
      </div>
    </el-drawer>
  </container>
</template>

<script>
import { addoreditImpulse, examImpulse,cancelImpulse, deleteimpulsebyid, pageImpulsedeclaration,getImpulseDeclaration} from '@/api/operatemanage/base/impulse'
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatPlatform,formatYesorno,formatTime} from "@/utils/tools";
import { rulePlatform,ruleShopCode} from '@/utils/formruletools'
const tableCols =[
      {istrue:true,prop:'goodsCode',label:'商品编码', width:'100',sortable:'custom'},
      {istrue:true,prop:'goodsName',label:'商品名称', width:'100',sortable:'custom'},
      {istrue:true,prop:'createdUserName',label:'申请人', width:'80',sortable:'custom'},
      {istrue:true,prop:'proCode',label:'宝贝ID', width:'80',sortable:'custom'},
      {istrue:true,prop:'shopName',label:'店铺', width:'110',sortable:'custom'},
      {istrue:true,prop:'platform',label:'平台', width:'50',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
      {istrue:true,prop:'status',label:'状态', width:'70',sortable:'custom',type:'format',formatter:(row)=>{if (row.status==-1) return "作废";else if (row.status==0) return "待审核";
           else if (row.status==1) return "审核通过";else if (row.status==2) return "审核不通过"; else return "";}},
      {istrue:true,prop:'number',label:'支撑数量', width:'80',sortable:'custom'},
      {istrue:true,prop:'days',label:'支撑天数	', width:'80',sortable:'custom'},
      {istrue:true,prop:'startDate',label:'开始日期',width:'105',sortable:'custom',formatter:(row)=>formatTime(row.startDate,'YYYY-MM-DD')},
      {istrue:true,type:'button',btnList:[{label:"编辑",handle:(that,row)=>that.onEdit(row)},
                              {label:"作废",handle:(that,row)=>that.onExam(row.id,-1)},
                              {label:"审核通过",handle:(that,row)=>that.onExam(row.id,1)},
                              {label:"审核不通过",handle:(that,row)=>that.onExam(row.id,2)}]},
     ];
const tableHandles1=[{label:"新增", handle:(that)=>that.onAdd()},];
export default {
  name: 'Roles',
  components: {cesTable, container, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: {
        goodsCode:null,
        proCode:null,
        shopCode:null,
        createdUserName:null, 
        startStartDate:null,
        endStartDate:null,
        /// 状态 0：待审核 1：已审核
        status:null,
      },
      list: [],
      pager:{OrderBy:"id",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      platFormList:[],
      shopList:[],
      autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 8 }}}},
               rule:[]
        },
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,     
      deleteLoading: false,
      formtitle:"新增",
      shopdialogVisible: false,
    }
  },
  async mounted() {
     var pfrule =await rulePlatform();
     this.platFormList=pfrule.options;
    this.getlist();
    this.initautoform();
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async initautoform(){
       let that=this;
       this.autoform.rule=[{type:'hidden',field:'id',title:'id',value: ''},
                      {type:'input',field:'goodsCode',title:'商品编码',validate: [{type: 'string', required: true, message:'请输入店铺编码'}]},
                      {type:'input',field:'goodsName',title:'商品名称',validate: [{type: 'string', required: true, message:'请输入店铺编码'}]},
                      {type:'input',field:'proCode',title:'宝贝ID',validate: [{type: 'string', required: false, message:'请输入平台店铺ID'}]},
                      {type:'select',field:'platform',title:'平台', value: null, update(val, rule){ if (val) {that.updateruleshop(val)}}, ...await rulePlatform()},
                      {type:'select',field:'shopCode',title:'店铺',value: '', ...await ruleShopCode()},                      
                      {type:'InputNumber',field:'number',title:'支撑数量',validate: [{type: 'number', required: true, message:'请输入支撑数量'}]},
                      {type:'InputNumber',field:'days',title:'支撑天数',validate: [{type: 'number', required: true, message:'请输入支撑天数'}]},
                      {type:'DatePicker',field:'startDate',title:'开始时间',validate: [{type: 'string', required: true, message:'请输入开始时间'}]},
                ] 
     },
    async updateruleshop(platformid) {
        await this.autoform.fApi.setValue({shopCode:''})
        await this.autoform.fApi.updateRule('shopCode',{... await ruleShopCode(platformid)})
        await this.autoform.fApi.sync('shopCode')
    },
   async onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {...this.pager, ...pager,... this.filter}
      this.listLoading = true
      const res = await pageImpulsedeclaration(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {d._loading = false})
      this.list = data
    },
   async onchangeplatform(val){
      this.categorylist =[]
      const res1 = await getshopList({platform:val,CurrentPage:1,PageSize:300});
      this.shopList=res1.data.list
    },
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    async onEdit(row) {
      this.formtitle='编辑';
      this.addFormVisible = true
      const res = await getImpulseDeclaration({id:row.id})
      await this.autoform.fApi.setValue(res.data)
      await this.autoform.fApi.setValue({shopCode:res.data.shopCode})
    },
   async onAdd() {
      this.formtitle='新增';
      this.addFormVisible = true
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
         this.autoform.fApi.resetFields()
    },
    async onEditSubmit() {
      this.addFormVisible = true
      await onAddSubmit();
    },
    async onAddSubmit() {
      this.addLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          formData.id=formData.id?formData.id:0;
          formData.Enabled=true;
          const res = await addoreditImpulse(formData);
          if(res.code==1){
            this.getlist();
            this.addFormVisible=false;
          }
        }else{
          //todo 表单验证未通过
        }
     })
     this.addLoading=false;
    },
    deleteValidate(row) {
      let isValid = true
      if (row && row.name === 'admin') {
        this.$message({message: row.description + '，禁止删除！',type: 'warning'})
        isValid = false
      }
      return isValid
    },
    async onDelete(row) {
      row._loading = true
      const res = await deleteimpulsebyid(row.id)
      row._loading = false
      if (!res?.success) {
        return
      }
      this.$message({message: this.$t('admin.deleteOk'),type: 'success'})
      this.getlist()
    },
   async onExam(id,status){
       var message=status==-1?"作废":status==1?"审核通过":status==2?"审核不通过":"";
       this.$confirm(`确认${message}, 是否继续?`, '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'})
                      .then(async () => {
                         let res ={};
                         if (status==-1)  
                           res= await cancelImpulse({id:id,status:status})
                         else
                           res= await examImpulse({id:id,status:status})
                          if (!res?.code) {return }
                          if(res.code==1){
                              this.$message({type: 'success',message: `${message}成功!`});
                              await this.getlist();             
                             }                         
                        }).catch(() => {
                            this.$message({type: 'info',message: `已取消${message}`});
                        });
    },
    selsChange: function(sels) {
      this.sels = sels
    }
  }
}
</script>
