<template>
    <MyContainer>
        <!-- 查询条件 -->
      <template #header>
        <div class="top">
            <el-date-picker class="publicCss" v-model="ListInfo.yearMonthDay" type="date" format="yyyyMMdd"
            value-format="yyyyMMdd" placeholder="请选择日期" :clearable="false" style="width: 130px;">
            </el-date-picker>
          <el-input v-model.trim="ListInfo.shopName" placeholder="店铺" maxlength="50" clearable class="publicCss" />
          <el-input v-model.trim="ListInfo.shopId" placeholder="店铺ID" maxlength="50" clearable class="publicCss" />
          <el-input v-model.trim="ListInfo.orderNumber" placeholder="订单编号" maxlength="50" clearable class="publicCss" />
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" @click="startImport">导入</el-button>
          <el-button type="primary" class="top_button" @click="exportProps" :disabled="isExport">导出</el-button>
        </div>
      </template>
      <!-- table表单 -->
      <vxetablebase :id="'premiumDeductionRpt202505251630'" :tablekey="'premiumDeductionRpt202505251630'"
        ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
        :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
        :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
        :height="'100%'">
      </vxetablebase>
      <!-- 页签 -->
      <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template>
  
      <el-dialog title="导入数据" :visible.sync="dialogVisible" width="35%" v-dialogDrag :close-on-click-modal="false">
        <div class="upload-section">
            <div class="upload-row">
            <label class="required-label">
                <span class="required-mark">*</span> 日期选择：
            </label>
            <el-date-picker class="upload-month" v-model="yearMonthDay" type="date" format="yyyyMMdd" value-format="yyyyMMdd"
                placeholder="请选择日期" :clearable="false" />
            </div>
            <div class="upload-row">
            <el-upload ref="upload" class="upload-area" :auto-upload="false" :multiple="false" :limit="1" action
                accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <el-button size="small" type="success" :loading="uploadLoading" @click="onSubmitUpload" class="upload-btn">
                {{ uploadLoading ? '上传中' : '上传' }}
                </el-button>
            </el-upload>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </MyContainer>
  </template>
  
  <script>
  import MyContainer from "@/components/my-container";
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
  import { importPremiumDeduction, getPremiumDeductionPageList, exportPremiumDeduction } from '@/api/bookkeeper/reportdayV2'
  import dayjs from 'dayjs'
  const tableCols = [
    { sortable: 'custom', width: '100', align: 'center', prop: 'yearMonthDay', label: '日期', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopId', label: '店铺ID', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'policyNumber', label: '投保单号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNumber', label: '订单编号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderTime', label: '下单时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'movingAccountingTime', label: '动账时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'dynamicAccountSerialNumber', label: '动账流水号'},
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'insuranceName', label: '保险名称'},
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'payPremium', label: '支付保费', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'remark', label: '备注', },
  ]
  export default {
    name: "premiumDeductionRpt",
    components: {
      MyContainer, vxetablebase
    },
    data() {
      return {
        yearMonthDay: null,//导入时间
        dialogVisible: false,//导入弹窗
        fileList: [],//上传文件列表
        uploadLoading: false,//上传按钮loading
        fileparm: {},//上传文件参数
        that: this,
        ListInfo: {
          currentPage: 1,
          pageSize: 50,
          orderBy: null,
          isAsc: false,
          yearMonthDay: dayjs().subtract(1, 'day').format('YYYYMMDD'),//日期
          shopName: '',//店铺
          shopId: '',//店铺ID
          orderNumber: ''//订单编号
        },
        tableCols,
        tableData: [],
        summaryarry: {},
        total: 0,
        loading: false,
        isExport:false,
      }
    },
    async mounted() {
      await this.getList()
    },
    methods: {
      //查询
      async getList(type) {
        if (type == 'search') {
          this.ListInfo.currentPage = 1
          this.$refs.pager.setPage(1)
        }
        this.loading = true
        const { data, success } = await getPremiumDeductionPageList(this.ListInfo)
        if (success) {
          this.tableData = data.list
          this.tableData.forEach(item => {
            item.orderTime = item.orderTime ? dayjs(item.orderTime).format('YYYY/M/D') : ''
            item.movingAccountingTime = item.movingAccountingTime ? dayjs(item.movingAccountingTime).format('YYYY/M/D') : ''
          })
          this.total = data.total
          this.summaryarry = data.summary
          this.loading = false
        } else {
          this.$message.error('获取列表失败')
        }
      },
      //导出
      async exportProps() {
        if (!this.ListInfo.yearMonthDay) {
                this.$message({ message: "请先选择日期！", type: "warning" });
                return;
        }
        let pager = this.$refs.pager.getPager();
        const params = { ...pager, ...this.pager, ...this.ListInfo };
        this.isExport = true
        let res = await exportPremiumDeduction(params);
        this.isExport = false
        if (!res?.data) {
            this.$message({ message: "没有数据", type: "warning" });
            return
        }
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '保费扣除_' + new Date().toLocaleString() + '_.xlsx')
        aLink.click()
      },
      //上传文件
      onUploadRemove(file, fileList) {
        this.fileList = []
      },
      async onUploadChange(file, fileList) {
        this.fileList = fileList;
      },
      onUploadSuccess(response, file, fileList) {
        fileList.splice(fileList.indexOf(file), 1);
        this.fileList = [];
        this.dialogVisible = false;
      },
      onSubmitUpload() {
        if (!this.yearMonthDay) {
            this.$message({ message: "请选择日期", type: "warning" });
            return false;
        }
        if (this.fileList.length == 0) {
            this.$message({ message: "请先上传文件", type: "warning" });
            return false;
        }
        this.$refs.upload.submit();
      },
      //导入弹窗
      startImport() {
        this.fileList = []
        this.yearMonthDay = dayjs().subtract(1, 'day').format('YYYYMMDD')//日期
        this.dialogVisible = true;
      },
      async onUploadFile(item) {
        if (!item || !item.file || !item.file.size) {
            this.$message({ message: "请先上传文件", type: "warning" });
            return false;
        }
        this.uploadLoading = true
        const form = new FormData();
        form.append("upfile", item.file);
        form.append("yearMonthDay", this.yearMonthDay);
        var res = await importPremiumDeduction(form);
        if (res?.success)
            this.$message({ message: "上传成功,正在导入中...", type: "success" });
        this.uploadLoading = false
        this.dialogVisible = false;
        await this.getList()
      },
      //每页数量改变
      Sizechange(val) {
        this.ListInfo.currentPage = 1;
        this.ListInfo.pageSize = val;
        this.getList()
      },
      //当前页改变
      Pagechange(val) {
        this.ListInfo.currentPage = val;
        this.getList()
      },
      //排序
      sortchange({ order, prop }) {
        if (prop) {
          this.ListInfo.orderBy = prop
          this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
    }
  }
  </script>
  
<style scoped lang="scss">
.top {
display: flex;
margin-bottom: 10px;

.publicCss {
    width: 150px;
    margin-right: 5px;
}
}
.upload-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 12px 0;
}

.upload-row {
    display: flex;
    align-items: center;
    gap: 12px;
}

.required-label {
    font-weight: 500;
    color: #333;
}

.required-mark {
    color: red;
    margin-right: 4px;
}

.upload-month {
    width: 200px;
}

.upload-area {
    display: flex;
    align-items: center;
    gap: 10px;
}

.upload-btn {
    margin-left: 0;
}
</style>
  