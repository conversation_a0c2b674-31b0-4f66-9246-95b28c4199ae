<template>
  <container v-loading="pageLoading">
      <!-- :tableHandles='tableHandles'  -->
     <ces-table ref="table" :that='that' :isIndex='true'   
         :hasexpand='true' :tableData='list' :tableCols='tableCols' @sortchange='sortchange' :loading="listLoading"
         :showsummary='true' :summaryarry='summaryarry'>
      
         <template slot='extentbtn'>
      <el-button-group>
    <el-button type="primary" @click="onSearch">刷新</el-button> 
      </el-button-group>
       </template>
      </ces-table>
      
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
  
  </container>
</template>
<script>
import {getShopProfitAnalysisSum,getOperatingProfitAnalysisSum,getDetail1Tx}from '@/api/bookkeeper/financialreport'
import {formatTime,formatYesornoBool,formatWarehouseArea,formatIsOutStock,formatSecondToHour,formatPlatform} from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import logistics from '@/components/Comm/logistics'
import goodscoderecord from '@/views/inventory/goodscoderecord'
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";

const tableCols =[
      {istrue:true,prop:'version',label:'类型', width:'50',formatter:(row)=>!row.version?"": row.version=='v1'?"工资月报":"参考月报"},
      {istrue:true,prop:'yearMonth',label:'年月',sortable:'custom', width:'80',},
      {istrue:true,prop:'productID',label:'产品ID',sortable:'custom', width:'80',},
      {istrue:true,prop:'operating',label:'运营',sortable:'custom', width:'80',},
      {istrue:true,prop:'operatingFee',label:'运营提成', width:'80',},
      {istrue:true,prop:'shopName',label:'店铺',sortable:'custom', width:'60',},
      {istrue:true,prop:'goodsCode',label:'商品编码',sortable:'custom', width:'80',},
      {istrue:true,prop:'seriesCoding',label:'系列编码',sortable:'custom', width:'80',},
      {istrue:true,prop:'productName',label:'产品名称', width:'80',sortable:'custom'},
      {istrue:true,prop:'idNumber',label:'ID计数', width:'80',sortable:'custom'},
      {istrue:true,prop:'settlementIncome',label:'结算收入', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountSettlement_2',label:'2月之前月份收入', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountTaoKeNot',label:'淘客不计', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountShare',label:'参与公摊金额', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountEmptyIdCost',label:'空白链接成本', width:'80',sortable:'custom'},
      {istrue:true,prop:'replacementAmount',label:'补发成本', width:'80',sortable:'custom'},
      {istrue:true,prop:'abnormalAmount',label:'异常成本', width:'80',sortable:'custom'},
      {istrue:true,prop:'replacePoor',label:'代发成本差', width:'80',sortable:'custom'},
      {istrue:true,prop:'cornerBalance',label:'定制护墙角差额', width:'80',sortable:'custom'},
      {istrue:true,prop:'purchaseFreight',label:'采购运费', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountGrossProfitSale',label:'销售毛利', width:'80',sortable:'custom'},
      {istrue:true,prop:'deposit',label:'保证金', width:'80',sortable:'custom'},
      {istrue:true,prop:'insurance',label:'保险承保', width:'80',sortable:'custom'},
      {istrue:true,prop:'villageServiceFee',label:'村淘平台服务费', width:'80',sortable:'custom'},
      {istrue:true,prop:'withholdingPostrings',label:'代扣返点积分', width:'80',sortable:'custom'},
      {istrue:true,prop:'withholdingReturned',label:'代扣交易退回积分', width:'80',sortable:'custom'},
      {istrue:true,prop:'publicDonation',label:'公益捐款', width:'80',sortable:'custom'},
      {istrue:true,prop:'hbServiceFee',label:'花呗服务费', width:'80',sortable:'custom'},
      {istrue:true,prop:'enjoymentFee',label:'品牌新享新品直降礼金软件服务费', width:'80',sortable:'custom'},
      {istrue:true,prop:'taobaoCash',label:'淘宝现金红包', width:'80',sortable:'custom'},
       {istrue:true,prop:'taobaoAllianceRefund',label:'淘宝联盟推广佣金返还', width:'80',sortable:'custom'},
       {istrue:true,prop:'tmallCommission',label:'天猫佣金', width:'80',sortable:'custom'},
       {istrue:true,prop:'creditCardServiceFee',label:'信用卡服务费', width:'80',sortable:'custom'},
       {istrue:true,prop:'quickPaymentServiceFee',label:'极速回款担保服务费', width:'80',sortable:'custom'},
       {istrue:true,prop:'insuranceReturn',label:'保险返还', width:'80',sortable:'custom'},
       {istrue:true,prop:'enjoymentFirstPlan',label:'品牌新享-首单拉新计划', width:'80',sortable:'custom'},
       {istrue:true,prop:'enjoymentFirstPlan11',label:'品牌新享-双11激励', width:'80',sortable:'custom'},
       {istrue:true,prop:'taobaoSpecial',label:'淘宝特价版联合营销计划系统服务费', width:'80',sortable:'custom'},
       {istrue:true,prop:'afterSalesPayment',label:'售后支付/赔付', width:'80',sortable:'custom'},
       {istrue:true,prop:'yxkk',label:'营销扣款', width:'80',sortable:'custom'},
       {istrue:true,prop:'directJointPromotion',label:'直营 联营 营促销', width:'80',sortable:'custom'},
       {istrue:true,prop:'c2mReturn',label:'C2M退货包运费代扣', width:'80',sortable:'custom'},
       {istrue:true,prop:'dayBuy',label:'每日必买', width:'80',sortable:'custom'},
       {istrue:true,prop:'deductionHand',label:'手淘直播佣金扣费', width:'80',sortable:'custom'},
       {istrue:true,prop:'avgFee',label:'账单费用公摊', width:'80',sortable:'custom'},
       {istrue:true,prop:'billTotalAmont',label:'账单费用合计', width:'80',sortable:'custom'},
       {istrue:true,prop:'courierFees',label:'快递费', width:'80',sortable:'custom'},
       {istrue:true,prop:'courierFeesWgkk',label:'快递违规扣款', width:'80',sortable:'custom'},
       {istrue:true,prop:'packageFee',label:'包装费', width:'80',sortable:'custom'},
       {istrue:true,prop:'orderTotalAmount',label:'订单费用合计', width:'80',sortable:'custom'},
       {istrue:true,prop:'zhiTongChe',label:'直通车', width:'80',sortable:'custom'},
       {istrue:true,prop:'chaoJiTuiJian',label:'超级推荐', width:'80',sortable:'custom'},
       {istrue:true,prop:'taobaoke',label:'淘宝客', width:'80',sortable:'custom'},
       {istrue:true,prop:'wangXiangTai',label:'万相台', width:'80',sortable:'custom'},
       {istrue:true,prop:'taolijing',label:'淘礼金', width:'80',sortable:'custom'},
       {istrue:true,prop:'taoTeTuiGuang',label:'淘特推广', width:'80',sortable:'custom'},
       {istrue:true,prop:'teShuDanFee',label:'特殊单费用', width:'80',sortable:'custom'},
       {istrue:true,prop:'teshudanyongjin',label:'特殊单佣金', width:'80',sortable:'custom'},
       {istrue:true,prop:'teshudanben',label:'特殊单成本', width:'80',sortable:'custom'},
       {istrue:true,prop:'operatingFeeTotal',label:'运营费合计', width:'80',sortable:'custom'},
       {istrue:true,prop:'advRate',label:'广告费占比', width:'80',sortable:'custom'},
       {istrue:true,prop:'productFreightfee',label:'产品运费', width:'80',sortable:'custom'},
       {istrue:true,prop:'amontSample',label:'样品费', width:'80',sortable:'custom'},
       {istrue:true,prop:'operatingSample',label:'运营拿样', width:'80',sortable:'custom'},
       {istrue:true,prop:'amontSampleMG',label:'美工拿样', width:'80',sortable:'custom'},
       {istrue:true,prop:'shootingFeeMG',label:'美工拍摄费用', width:'80',sortable:'custom'},
       {istrue:true,prop:'shopFee',label:'店铺费用', width:'80',sortable:'custom'},
       {istrue:true,prop:'cuiShouFee',label:'催收费用', width:'80',sortable:'custom'},
       {istrue:true,prop:'laXin',label:'拉新', width:'80',sortable:'custom'},
       {istrue:true,prop:'totalProductCost',label:'产品费用合计', width:'80',sortable:'custom'},
       {istrue:true,prop:'amontCommissionMG',label:'美工提成', width:'80',sortable:'custom'},
       {istrue:true,prop:'amontCommissionCG',label:'采购提成', width:'80',sortable:'custom'},
       {istrue:true,prop:'amontWagesGroup',label:'小组运营工资', width:'80',sortable:'custom'},
       {istrue:true,prop:'amontMachineGZ',label:'加工工资', width:'80',sortable:'custom'},
       {istrue:true,prop:'amontCommissionXMT',label:'新媒体提成', width:'80',sortable:'custom'},
       {istrue:true,prop:'totalWagesCost',label:'工资合计', width:'80',sortable:'custom'},
       {istrue:true,prop:'storeLossfee',label:'仓储损耗', width:'80',sortable:'custom'},
       {istrue:true,prop:'operatingDown',label:'运营下架', width:'80',sortable:'custom'},
       {istrue:true,prop:'grossProfit',label:'产品利润', width:'80',sortable:'custom'},
       {istrue:true,prop:'deductions',label:'扣款', width:'80',sortable:'custom'},
     ];


export default {
  name: "Users",
  components: {container,cesTable,MyConfirmButton,logistics},
   props:{
       filter: { }
     },
  data() {
    return {
    
      uploadLoading:false,
      dialogVisible: false,
      that:this,
      // filter: {
      //   timerange:'',   
      // },
      list: [],
      drawervisible:false,
      tableCols:tableCols,
      pager:{OrderBy:"",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      listLoading: false,
      pageLoading: false,
    };
  },
  watch: {
    value(n) {
      if(n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
      }
    }
  },
 async mounted() {
    await this.onSearch();
    await this.getlist();
  },
 methods: {
   onSelsChangeReturnGoods(sels){
    this.sels2 = sels
   },

  
   async onSearch() {
       this.$refs.pager.setPage(1)
       this.getlist()
    },
   async getlist() {
     this.filter.startTime =null;
       this.filter.endTime =null;
       if (this.filter.yearMonth && this.filter.yearMonth.length>0) {
                this.filter.startTime = this.filter.yearMonth[0];
                this.filter.endTime = this.filter.yearMonth[1];
            }
     
      if (!this.pager.OrderBy) this.pager.OrderBy="";
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      this.listLoading = true
      params.SeriesCoding=this.filter.SeriesCoding.join();
      const res = await getDetail1Tx(params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {d._loading = false})
      this.list = data
      this.summaryarry=res.data.summary;
    },

    beforeRemove() {
      return false;
    },
    
   doCopy: function (val) {       
      let that=this;                         
      this.$copyText(val).then(function (e) {
          that.$message({ message: "内容已复制到剪切板！", type: "success" });
      }, function (e) {
          that.$message({ message: "抱歉，复制失败！", type: "warning" });
      })
    },
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   
  },
};
</script>


