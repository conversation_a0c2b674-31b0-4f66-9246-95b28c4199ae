<template>
    <MyContainer>
        <!-- 查询条件 -->
      <template #header>
        <div class="top">
            <el-date-picker style="width: 249px" v-model="ListInfo.timerange" type="daterange" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" :clearable="false"
              end-placeholder="结束" :picker-options="pickerOptions">
            </el-date-picker>
            <el-input v-model.trim="ListInfo.MainOrderNumber" placeholder="主订单编号" maxlength="50" clearable class="publicCss" />
            <el-input v-model.trim="ListInfo.ChildOrderNumber" placeholder="子订单编号" maxlength="50" clearable class="publicCss" />
            <el-input v-model.trim="ListInfo.ProCode" placeholder="商品ID" maxlength="50" clearable class="publicCss" />
            <el-input v-model.trim="ListInfo.TalentID" placeholder="达人ID" maxlength="50" clearable class="publicCss" />
            <el-select filterable clearable v-model="ListInfo.shopCode" placeholder="店铺" style="width: 160px" multiple collapse-tags>
              <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"></el-option>
            </el-select>
            <el-select v-model="ListInfo.OrderStatus" filterable placeholder="订单状态" multiple collapse-tags clearable>
              <el-option :key="已发货" :label="已发货" value="已发货"></el-option>
              <el-option :key="已完成" :label="已完成" value="已完成"></el-option>
              <el-option :key="已关闭" :label="已关闭" value="已关闭"></el-option>
            </el-select>
            <el-select v-model="ListInfo.OrderType" filterable placeholder="订单类型" multiple collapse-tags clearable>
                <el-option :key="普通订单" :label="普通订单" value="普通订单"></el-option>
                <el-option :key="补发订单" :label="补发订单" value="补发订单"></el-option>
                <el-option :key="分销Plus" :label="分销Plus" value="分销Plus"></el-option>
            </el-select>
            <el-select v-model="ListInfo.PlatformSubsidyAmount" filterable placeholder="平台补贴" multiple collapse-tags clearable>
                <el-option label="是" value="1"></el-option>
                <el-option label="否" value="0"></el-option>
            </el-select>
            <el-button type="primary" @click="getList('search')">搜索</el-button>
            <el-button type="primary" @click="startImport">导入</el-button>
            <el-button type="primary" class="top_button" @click="exportProps" :disabled="isExport">导出</el-button>
        </div>
      </template>
      <!-- table表单 -->
      <vxetablebase :id="'OrderManagementDY202507111110'" :tablekey="'OrderManagementDY202507111110'"
        ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
        :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
        :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
        :height="'100%'">
      </vxetablebase>
      <!-- 页签 -->
      <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template>
  
      <el-dialog title="导入数据" :visible.sync="dialogVisible" width="35%" v-dialogDrag :close-on-click-modal="false">
        <div class="upload-section">
            <div class="upload-row">
            <label class="required-label">
                <span class="required-mark">*</span> 日期选择：
            </label>
            <el-date-picker class="upload-month" v-model="yearMonthDay" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                placeholder="请选择日期" :clearable="false" :picker-options="pickerOption" />
            </div>
            <div class="upload-row">
            <el-upload ref="upload" class="upload-area" :auto-upload="false" :multiple="false" :limit="1" action
                accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <el-button size="small" type="success" :loading="uploadLoading" @click="onSubmitUpload" class="upload-btn">
                {{ uploadLoading ? '上传中' : '上传' }}
                </el-button>
            </el-upload>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </MyContainer>
</template>
<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { importOrderManagement, getOrderManagementPageList, exportOrderManagement } from '@/api/bookkeeper/reportdayV2'
import dayjs from 'dayjs'
import { formatLinkProCode, pickerOptions } from "@/utils/tools";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';

const tableCols = [
    { sortable: 'custom', width: '100', align: 'center', prop: 'yearMonthDay', label: '日期', formatter: (row) => dayjs(row.yearMonthDay).format('YYYY-MM-DD') },
    { sortable: 'custom', width: '150', align: 'center', prop: 'shopCode', label: '店铺名', formatter: (row) => row.shopName || '' },
    { sortable: 'custom', width: '80', align: 'center', prop: 'platformSubsidyAmount', label: '平台补贴', formatter: (row) => row.platformSubsidyAmount == "1" ? '是' : row.platformSubsidyAmount == "0" ? '否' : "" },
    { sortable: 'custom', width: '160', align: 'center', prop: 'mainOrderNumber', label: '主订单编号', },
    { sortable: 'custom', width: '160', align: 'center', prop: 'childOrderNumber', label: '子订单编号', },
    { sortable: 'custom', width: '300', align: 'center', prop: 'shoppingGoods', label: '选购商品', },
    { sortable: 'custom', width: '200', align: 'center', prop: 'productSpecification', label: '商品规格', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'qty', label: '商品数量', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'proCode', label: '商品ID', type: 'html', formatter: (row) => formatLinkProCode(6, row.proCode)},
    { sortable: 'custom', width: '120', align: 'center', prop: 'goodsCode', label: '商家编码'},
    { sortable: 'custom', width: '120', align: 'center', prop: 'unitAmount', label: '商品单价', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'payAmount', label: '订单应付金额', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'fare', label: '运费', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'totalDiscountAmount', label: '优惠总金额', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'merchantChangesPrice', label: '商家改价', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'paymentDiscountAmount', label: '支付优惠', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'redEnvelopeDeductionAmount', label: '红包抵扣', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'handlingFee', label: '手续费', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'paymentCompletionTime', label: '支付完成时间', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'orderStatus', label: '订单状态', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'orderType', label: '订单类型', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'talentID', label: '达人ID', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'deliveryTime', label: '发货时间', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'discountedPrice', label: '降价类优惠', },
    { sortable: 'custom', width: '180', align: 'center', prop: 'platformRealDiscountAmount', label: '平台实际承担优惠金额', },
    { sortable: 'custom', width: '180', align: 'center', prop: 'merchantRealDiscountAmount', label: '商家实际承担优惠金额', },
    { sortable: 'custom', width: '180', align: 'center', prop: 'talentRealDiscountAmount', label: '达人实际承担优惠金额', },
  ]		
export default {
    name: "OrderManagementDY",
    components: {
      MyContainer, vxetablebase
    },
    data() {
      return {
        yearMonthDay: null,//导入时间
        dialogVisible: false,//导入弹窗
        fileList: [],//上传文件列表
        uploadLoading: false,//上传按钮loading
        fileparm: {},//上传文件参数
        that: this,
        ListInfo: {
          currentPage: 1,
          pageSize: 50,
          orderBy: 'yearMonthDay',
          isAsc: false,
          timerange: [],
          startTime: '',
          endTime: '',
          MainOrderNumber:'',//主订单编号
          ChildOrderNumber:'',//子订单编号
          ProCode:'',//商品ID
          TalentID:'',//达人ID
          shopCode: '',//店铺编码
          OrderStatus: '',//订单状态
          OrderType: '',//订单类型
          PlatformSubsidyAmount:''//平台补贴
        },
        shopList: [],
        tableCols,
        tableData: [],
        summaryarry: {},
        total: 0,
        loading: false,
        isExport:false,
        pickerOptions,
        pickerOption: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        },
      }
    },
    async mounted() {
      this.shopList = (await getAllShopList({ platforms: [6] })).data;
      this.getList('search')
    },
    methods: {
      //查询
      async getList(type) {
        if (type == 'search') {
          this.ListInfo.currentPage = 1
          this.$refs.pager.setPage(1)
        }
        if(this.ListInfo.timerange){
          this.ListInfo.startTime = this.ListInfo.timerange[0]
          this.ListInfo.endTime = this.ListInfo.timerange[1]
        }
        let pager = this.$refs.pager.getPager();
        const params = { ...pager, ...this.pager, ...this.ListInfo };
        this.loading = true
        const { data, success } = await getOrderManagementPageList(params)
        if (success) {
          this.tableData = data.list
          this.total = data.total
          this.summaryarry = data.summary
          this.loading = false
        } else {
          this.$message.error('获取列表失败')
        }
      },
      //导出
      async exportProps() {
        if(this.ListInfo.timerange){
          this.ListInfo.startTime = this.ListInfo.timerange[0]
          this.ListInfo.endTime = this.ListInfo.timerange[1]
        }
        let pager = this.$refs.pager.getPager();
        const params = { ...pager, ...this.pager, ...this.ListInfo };
        this.isExport = true
        let res = await exportOrderManagement(params);
        this.isExport = false
        if (!res?.data) {
            this.$message({ message: "没有数据", type: "warning" });
            return
        }
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '订单管理_' + new Date().toLocaleString() + '_.xlsx')
        aLink.click()
      },
      //上传文件
      onUploadRemove(file, fileList) {
        this.fileList = []
      },
      async onUploadChange(file, fileList) {
        this.fileList = fileList;
      },
      onUploadSuccess(response, file, fileList) {
        fileList.splice(fileList.indexOf(file), 1);
        this.fileList = [];
        this.dialogVisible = false;
      },
      onSubmitUpload() {
        if (!this.yearMonthDay) {
            this.$message({ message: "请选择日期", type: "warning" });
            return false;
        }
        if (this.fileList.length == 0) {
            this.$message({ message: "请先上传文件", type: "warning" });
            return false;
        }
        this.$refs.upload.submit();
      },
      //导入弹窗
      startImport() {
        this.fileList = []
        this.yearMonthDay = dayjs().subtract(1, 'day').format('YYYY-MM-DD')//日期
        this.dialogVisible = true;
      },
      async onUploadFile(item) {
        if (!item || !item.file || !item.file.size) {
            this.$message({ message: "请先上传文件", type: "warning" });
            return false;
        }
        this.uploadLoading = true
        const form = new FormData();
        form.append("upfile", item.file);
        form.append("yearMonthDay", this.yearMonthDay);
        var res = await importOrderManagement(form);
        if (res?.success)
            this.$message({ message: res.message || "上传成功,正在导入中...", type: "success" });
        this.uploadLoading = false
        this.dialogVisible = false;
        await this.getList()
      },
      //每页数量改变
      Sizechange(val) {
        this.ListInfo.currentPage = 1;
        this.ListInfo.pageSize = val;
        this.getList()
      },
      //当前页改变
      Pagechange(val) {
        this.ListInfo.currentPage = val;
        this.getList()
      },
      //排序
      sortchange({ order, prop }) {
        if (prop) {
          this.ListInfo.orderBy = prop
          this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
    }
  }												
</script>
<style scoped lang="scss">
.top {
display: flex;
margin-bottom: 10px;

.publicCss {
    width: 150px;
    margin-right: 5px;
}
}
.upload-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 12px 0;
}

.upload-row {
    display: flex;
    align-items: center;
    gap: 12px;
}

.required-label {
    font-weight: 500;
    color: #333;
}

.required-mark {
    color: red;
    margin-right: 4px;
}

.upload-month {
    width: 200px;
}

.upload-area {
    display: flex;
    align-items: center;
    gap: 10px;
}

.upload-btn {
    margin-left: 0;
}
::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>