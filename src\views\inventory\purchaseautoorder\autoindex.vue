<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-date-picker style="width: 220px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束" clearable
                        @change="onSearch"></el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select v-model="filter.status" multiple clearable collapse-tags placeholder="请选择状态"
                        style="width: 150px">
                        <el-option label="待审核" value="待审核" />
                        <el-option label="完成" value="完成" />
                        <el-option label="已确认" value="已确认" />
                        <el-option label="作废" value="作废" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select v-model="filter.brandId" multiple collapse-tags filterable clearable placeholder="请选择采购员"
                        style="width: 130px">
                        <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select v-model="filter.version" clearable placeholder="请选择开单状态状态" style="width: 130px">
                        <el-option label="版本一" value="1" />
                        <el-option label="版本二" value="2" />
                        <el-option label="版本三" value="3" />
                        <el-option label="版本四" value="4" />
                        <el-option label="版本五" value="5" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-input v-model.trim="filter.keywords" type="text" maxlength="100" clearable placeholder="请输入关键字..."
                        style="width:200px;">
                        <el-tooltip slot="suffix" class="item" effect="dark" :content="keywordsTip" placement="bottom">
                            <i class="el-input__icon el-icon-question"></i>
                        </el-tooltip>
                    </el-input>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button>
            </el-button-group>
        </template>
        <!--列表-->
        <vxetablebase :id="'goodsCostPriceChgList202301031318001'" :tableData='list' :tableCols='tableCols' @cellClick='cellclick'
            :tableHandles='tableHandles' :loading='listLoading' :border='true' :that="that" ref="vxetable"
            @sortchange='sortchange' />

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </container>
</template>
  
<script>
import { getAllProBrand } from '@/api/inventory/warehouse'
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { getPurchaseAutoOrderDetail } from "@/api/inventory/purchaseordernew"
import { formatTime, formatYesornoBool, formatWarehouseArea, formatNoLink, formatIsError, formatIsOutStock, formatSecondToHour, warehouselist } from "@/utils/tools";

const tableCols = [
    { istrue: true, prop: 'buyNo', label: '采购单号', width: '120', sortable: 'custom',  },
    { istrue: true, prop: 'indexNo', label: 'Erp编号', width: '120', sortable: 'custom', type: 'html', formatter: (row) => formatNoLink(row.indexNo) },
    { istrue: true, prop: 'brandName', label: '采购员', width: '63', },
    { istrue: true, prop: 'purchaseDate', label: '采购日期', width: '130', sortable: 'custom', formatter: (row) => formatTime(row.purchaseDate, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'checkDate', label: '审核日期', width: '130', sortable: 'custom', formatter: (row) => formatTime(row.checkDate, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'status', label: '状态', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'supplier', label: '供应商', width: '*', sortable: 'custom' },
    { istrue: true, prop: 'warehouse', label: '第三方物流和分仓', width: '*', sortable: 'custom', formatter: (row) => row.warehouseName },
    { istrue: true, prop: 'version', label: '开单版本', width: '120', sortable: 'custom', formatter: (row) => 
        {
            const handle = versionHandles.find(item => item.version == row.version);
            return handle ? handle.label : ''
        }
    },
    { istrue: true, prop: 'timer', label: '通知运营截止时间段', width: 'auto', sortable: 'custom', formatter: (row) => row.timer + "分钟" },
    { istrue: true, prop: 'createdTime', label: '创建时间', width: 'auto', sortable: 'custom', },
];
const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
];

const versionHandles = [
    {version:1,label:'版本一'},
    {version:2,label:'版本二'},
    {version:3,label:'版本三'},
    {version:4,label:'版本四'},
    {version:5,label:'版本五'}
]

const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: '数据界面',
    components: { MyConfirmButton, container, vxetablebase },
    data() {
        return {
            that: this,
            filter: {
                startDate: null,
                endDate: null,
                indexNo: null,
                brandId:null,
                keywords: null,
                version: null,
                eywords: null,
                status:"所有",
                timerange: [startTime, endTime],
            },
            keywordsTip: '支持搜索的内容：采购单号、Erp单号',
            brandlist: [],
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "createdTime", IsAsc: false },
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
        }
    },
    async mounted() {
        await this.onSearch();
        await this.init();
    },
    methods: {
        async init() {
            var res2 = await getAllProBrand();
            this.brandlist = res2.data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await getPurchaseAutoOrderDetail(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.total = res.data.total;
            const data = res.data.list;
            this.list = data
        },
        async cellclick({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }) {
            if (column.property == 'indexNo') {
                this.$router.push({ path: '/inventory/purchaseindex', query: { indexNo: row.indexNo } })
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    }
}
</script>
  