<template>
   <MyContainer>
      <div style="height: 400px;">
         <el-table :data="tableData" style="width: 100%" max-height="400">
            <el-table-column prop="combineCode" label="商品编码" show-overflow-tooltip align="left" width="100" />
            <el-table-column prop="combineName" label="商品名称" show-overflow-tooltip align="left" width="80" />
            <el-table-column prop="prePackWmsName" label="仓库" show-overflow-tooltip align="left" />
            <el-table-column prop="skus" label="skus" show-overflow-tooltip align="left" width="100" />
            <el-table-column prop="status" label="状态" show-overflow-tooltip align="left" width="80" />
            <el-table-column prop="turnoverDay" label="周转天数" show-overflow-tooltip align="center" width="80" />
            <el-table-column prop="planQty" label="计划数量" show-overflow-tooltip align="left">
               <template #default="{ row }">
                  <el-input-number v-model="row.planQty" :min="0" :max="9999" placeholder="计划数量" :precision="0"
                     :controls="false" />
               </template>
            </el-table-column>
         </el-table>
      </div>
      <div class="btnGroup">
         <el-button @click="close">取消</el-button>
         <el-button type="primary" @click="submit">提交</el-button>
      </div>
   </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import {
   getWaitPrePacks, createPackManifest
} from '@/api/vo/prePack'
export default {
   name: "scanCodePage",
   components: {
      MyContainer
   },
   props: {
      goodsCodes: {
         type: Array,
         default: () => []
      }
   },
   data() {
      return {
         that: this,
         timeRanges: [],
         tableData: [],
         total: 0,
         loading: false,
         isExport: false
      }
   },
   async mounted() {
      await this.getList()
   },
   methods: {
      async submit() {
         const { success } = await createPackManifest(this.tableData)
         if (success) {
            this.$message.success('提交成功')
         } else {
            this.$message.error('提交失败')
         }
         this.$emit('close')
      },
      close() {
         this.$emit('close')
      },
      async getList() {
         // 使用时将下面的方法替换成自己的接口
         try {
            const { data, success } = await getWaitPrePacks({ goodsCodes: this.goodsCodes })
            if (success) {
               this.tableData = data
            } else {
               this.$message.error('获取列表失败')
            }
         } catch (error) {
            this.$message.error('获取列表失败')
         } finally {
            this.loading = false
         }
      },
   }
}
</script>

<style scoped lang="scss">
.top {
   display: flex;
   margin-bottom: 10px;

   .publicCss {
      width: 200px;
      margin-right: 10px;
   }
}

.btnGroup {
   display: flex;
   justify-content: end;
   margin-top: 20px;

   .el-button {
      margin-left: 10px;
   }
}
</style>