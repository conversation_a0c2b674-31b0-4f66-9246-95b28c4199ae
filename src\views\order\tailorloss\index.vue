<template>
    <my-container v-loading="pageLoading" style="height: 100%">
        <el-tabs v-model="activeName" style="height: 94%">
            <el-tab-pane label="损耗统计" name="first1" style="height: 100%">
                <lossstatistics ref="lossstatistics" style="height: 100%"></lossstatistics>
            </el-tab-pane>
            <el-tab-pane label="裁剪订单" name="first2" style="height: 100%" lazy>
                <tailororder ref="tailororder" style="height: 100%"></tailororder>
            </el-tab-pane>
            <el-tab-pane label="原材料出库" name="first3" style="height: 100%" lazy>
                <materialoutstore ref="materialoutstore" style="height: 100%"></materialoutstore>
            </el-tab-pane>
            <el-tab-pane label="成品半成品设置" name="first4" style="height: 100%" lazy>
                <tailorlossgoodsset ref="tailorlossgoodsset" style="height: 100%"></tailorlossgoodsset>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
  
  <script>
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import cesTable from "@/components/Table/table.vue";
    import dayjs from "dayjs";
    import { formatTime } from "@/utils";
    import tailororder from "@/views/order/tailorloss/tailororder.vue";
    import materialoutstore from "@/views/order/tailorloss/materialoutstore.vue";
    import lossstatistics from "@/views/order/tailorloss/lossstatistics.vue";
    import tailorlossgoodsset from "@/views/order/tailorloss/tailorlossgoodsset.vue";
    export default {
        name: "tailorlossindex",
        components: {
            cesTable, MyContainer, MyConfirmButton, tailororder, materialoutstore, lossstatistics, tailorlossgoodsset
        },
        data() {
            return {
                that: this,
                pageLoading: false,
                activeName: "first1",
            };
        },
        async mounted() {

        },
        methods: {
        },
    };
  </script>
  
  <style lang="scss" scoped>
</style>
  