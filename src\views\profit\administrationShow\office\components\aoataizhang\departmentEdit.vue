<template>
    <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
        <el-scrollbar style="height: 100%">
            <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="140px" class="demo-ruleForm">
                <!-- <el-form-item label="类型：">
            <el-select v-model="ruleForm.type" placeholder="类型" class="publicCss" clearable>
                <el-option v-for="item in typeList" :key="item" :label="item" :value="item" />
            </el-select>
        </el-form-item>
        <el-form-item label="区域：">
            <el-select v-model="ruleForm.regionName" placeholder="区域" class="publicCss" clearable>
                <el-option v-for="item in districtList" :key="item" :label="item" :value="item" />
            </el-select>
        </el-form-item> -->
                <el-form-item label="南昌：" prop="ncCost">
                    <inputNumberYh v-model="ruleForm.ncCost" :placeholder="'南昌'" class="publicCss" :fixed="0" />
                </el-form-item>
                <el-form-item label="武汉：" prop="whCost">
                    <inputNumberYh v-model="ruleForm.whCost" :placeholder="'武汉'" class="publicCss" :fixed="0" />
                </el-form-item>
                <el-form-item label="义乌：" prop="ywCost">
                    <inputNumberYh v-model="ruleForm.ywCost" :placeholder="'义乌'" class="publicCss" :fixed="0" />
                    <!-- <el-input style="width:80%;" v-model.trim="ruleForm.sessionAndType" :maxlength="50" placeholder="场次&类型" clearable /> -->
                </el-form-item>
                <el-form-item label="深圳：" prop="szCost">
                    <inputNumberYh v-model="ruleForm.szCost" :placeholder="'深圳'" class="publicCss" :fixed="0" />
                </el-form-item>
                <el-form-item label="选品中心：" prop="xpCost">
                    <inputNumberYh v-model="ruleForm.xpCost" :placeholder="'选品中心'" class="publicCss" :fixed="0" />
                </el-form-item>

            </el-form>
        </el-scrollbar>
        <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
            <el-button @click="cancellationMethod">取消</el-button>
            <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
        </div>
    </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { oALedgerSubmit } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission'
export default {
    name: 'departmentEdit',
    components: {
        inputNumberYh, MyConfirmButton
    },
    props: {
        editInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
        districtList: {
            type: Object,
            default: () => {
                return {}
            }
        },
        typeList: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data() {
        return {
            selectProfitrates: [],
            ruleForm: {
                label: '',
                xpCost: '',
                ncCost: '',
                whCost: '',
                ywCost: '',
                szCost: '',
                isArchive: false,
                id: '',
                name: ''
            },
            rules: {
                szCost: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                ywCost: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                whCost: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                ncCost: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                xpCost: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ]
            }
        }
    },

    async mounted() {
        this.$nextTick(() => {
            this.$refs.refruleForm.clearValidate();
        });
        this.ruleForm = { ...this.editInfo };
    },
    methods: {
        cancellationMethod() {
            this.$emit('cancellationMethod');
        },
        submitForm(formName) {
            console.log(this.ruleForm.label, 'this.ruleForm.label');
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing")
                    const { data, success } = await oALedgerSubmit(this.ruleForm)
                    if (!success) {
                        return
                    }
                    await this.$emit("search");

                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
            //   this.$confirm('是否保存?', '提示', {
            //     confirmButtonText: '确定',
            //     cancelButtonText: '取消',
            //     type: 'warning'
            //   }).then(async () => {
            //     this.$refs[formName].validate(async(valid) => {
            //       if (valid) {
            //         const { data, success } = await oALedgerSubmit(this.ruleForm)
            //         if(!success){
            //             return
            //         }
            //         await this.$emit("search");

            //       } else {
            //         console.log('error submit!!');
            //         return false;
            //       }
            //     });
            //   }).catch(() => {
            //   });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
    }
}
</script>
<style scoped lang="scss">
.publicCss {
    width: 80%;
}
</style>
