<template>
  <MyContainer>
    <template #header>
      <div >
        <!-- <el-date-picker v-model="ListInfo.yearmonth" type="month" :clearable="false" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"  ></el-date-picker> -->
        <el-button style="padding: 0;margin: 0;width: 250px; border: none;">
          <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="导入开始日期" end-placeholder="导入结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime" :clearable="false">
        </el-date-picker>
      </el-button>
      <el-button style="padding: 0;margin: 0;width: 500px; border: none;">
          <queryCondition ref="refqueryCondition" :valueChanged.sync="topfilter" />
        </el-button>
        <!-- <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
            <el-input v-model.trim="ListInfo.batchNumber" placeholder="批次号" maxlength="50" clearable class="" />
          </el-button> -->
          <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
              <inputYunhan ref="productmailNumber" :inputt.sync="ListInfo.mailNumberList" v-model="ListInfo.mailNumberList" width="160px"
              placeholder="邮件号(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
              :maxlength="21000" @callback="mailNumberCallback" title="邮件号">
            </inputYunhan>
            </el-button>
            <!-- <el-button style="padding: 0;margin: 0;border: none;" >
            <el-select filterable v-model="ListInfo.platform" placeholder="请选择平台" multiple collapse-tags
              clearable style="width: 160px">
              <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button> -->
            <el-button style="padding: 0;margin: 0;width: 200px; border: none;">
                <!-- <el-input v-model.trim="ListInfo.originalOnlineOrderNo" placeholder="原始线上订单号" maxlength="50" clearable class="" /> -->
                <div  class="publicCss" >
          <inputYunhan ref="productCode" :inputt.sync="ListInfo.originalOnlineOrderNo" v-model="ListInfo.originalOnlineOrderNo" width="200px"
              placeholder="原始线上订单号(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
              :maxlength="21000" @callback="productCodeCallback" title="原始线上订单号">
            </inputYunhan>
         </div>
          </el-button>

          <el-button style="padding: 0;margin: 0;width: 200px; border: none;">
                  <!-- <el-input v-model.trim="ListInfo.orderNo" placeholder="内部单号" maxlength="50" clearable class="" /> -->
           <inputYunhan ref="productCode" :inputt.sync="ListInfo.orderNo" v-model="ListInfo.orderNo" width="200px"
              placeholder="内部单号(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
              :maxlength="21000" @callback="productCodeCallback2" title="内部单号">
            </inputYunhan>
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
            <el-select  ref="goodsCodeInput" v-model="ListInfo.goodsCodeList" multiple collapse-tags  clearable filterable placeholder="商品编码" @blur="productSelect($event,1)" allow-create >
                <el-option v-for="(item,index) in goodsCodeList" :key="index" :label="item" :value="item" />
            </el-select>
          </el-button>
          <!-- <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
            <el-input v-model.trim="ListInfo.id" placeholder="ID" maxlength="50" clearable class="" />
          </el-button> -->
          <!-- <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
            <el-select  v-model="ListInfo.goodsCount" clearable filterable placeholder="品类数" allow-create >
                <el-option  label="一单一品" value="1" />
                <el-option  label="一单多品" value="2" />
            </el-select>
          </el-button> -->
          <el-button style="padding: 0;border: none;margin-left: 10px;">
            <el-input-number v-model="ListInfo.minDiffWeight" :min="0" :max="9999999" :precision="0" :controls="false" style="width: 110px;"
              placeholder=">=重量差额最小值"></el-input-number>
          </el-button>
          <el-button style="padding: 0;border: none;margin-left: 10px;">
            <el-input-number v-model="ListInfo.maxDiffWeight" :min="0" :max="99999999" :precision="0" :controls="false" style="width: 110px;"
              placeholder="≤重量差额最大值"></el-input-number>
          </el-button>
          <el-button style="padding: 0;border: none;margin-left: 10px;">
            <el-input-number v-model="ListInfo.minDiffExpressFee" :min="0" :max="9999999" :precision="0" :controls="false" style="width: 110px;"
              placeholder=">=金额差额最小值"></el-input-number>
          </el-button>
          <el-button style="padding: 0;border: none;margin-left: 10px;">
            <el-input-number v-model="ListInfo.maxDiffExpressFee" :min="0" :max="99999999" :precision="0" :controls="false" style="width: 110px;"
              placeholder="≤金额差额最大值"></el-input-number>
          </el-button>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startClac">计算</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'erpExportall202410131003'" :tablekey="'erpExportall202410131003'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="计算" :visible.sync="dialogVisible2" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 100px;">
        <el-select v-model="expressCompanyId" placeholder="快递公司"
          style="width: 200px;margin-right: 10px;margin-bottom: 10px;" clearable  @change="getprosimstatelist(1)" >
          <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-select v-model="prosimstate" placeholder="请选择快递站点" clearable style="width: 130px">
              <el-option label="暂无站点" value="" />
              <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
          </el-select>
        <el-select v-model="warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 130px">
                        <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
        <el-date-picker style="width: 230px; float: left;margin-right: 10px;" v-model="createTime" type="month" format="yyyy-MM"
        value-format="yyyy-MM" placeholder="选择月份"></el-date-picker>

          <el-input v-model="ruleBatchNumber" placeholder="规则批次号" style="width:230px;" />
        <span style="color: red;"> <br>温馨提示：如需指定计算规则批次号，则填写规则批次号</span>

        <!-- <el-input v-model="dayBatchNumber" placeholder="账单数据批次号" style="width:230px;" />
        <span style="color: red;"> <br>温馨提示：如需指定账单数据批次号，则填写账单数据批次号</span> -->
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible2 = false">关闭</el-button>
        <el-button type="primary" @click="clacDay">确认计算</el-button>
      </span>
    </el-dialog>

    
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { warehouselist,formatTime,pickerOptions ,formatWarehouseNew, platformlist } from "@/utils/tools";
import { getExpressComanyAll,getExpressComanyStationName, getExpressInfoData_MonthAllWeight, exportExpressInfoData_MonthALLWeight,monthExpressCompanyFeeWeightAllCalculate} from "@/api/express/express";
import dayjs from 'dayjs'
import queryCondition from "../../dailyCourierFee/components/queryCondition.vue";
import inputYunhan from "@/components/Comm/inputYunhan";


const goodsCodeList = [
  'PPK',
  'TM-2021',
]
const labelsList = [
  '发货前售后',
  '压力罐',
]
const statusList = [
  '被拆分',
  '被合并',
]
const remarkList = [
  '星河',
]
const tableCols = [
{ sortable: 'custom', width: '80', align: 'center', prop: 'inportDate', label: '导入日期', formatter: (row) => formatTime(row.inportDate,"YYYY-MM-DD") },
{ sortable: 'custom', width: '80', align: 'center', prop: 'expressCompanyName', label: '快递公司', },
{ sortable: 'custom', width: '80', align: 'center', prop: 'prosimstateId', label: '快递站点',  formatter: (row) => row.prosimstate, type: 'custom' },
{ sortable: 'custom', width: '80', align: 'center', prop: 'warehouseId', label: '发货仓库', formatter: (row) => formatWarehouseNew(row.warehouseId)  },
  // { sortable: 'custom', width: '110', align: 'center', prop: 'batchNumber', label: '批次号', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'mailNumber', label: '邮件号', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'jsCount', label: '计数', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'billingWeight', label: '快递公司重量', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'totalFreight', label: '运费合计', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'province', label: '省份', },
  { sortable: 'custom', width: '160', align: 'center', prop: 'originalOnlineOrderNo', label: '原始线上订单号 ', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'orderNo', label: '内部单号',  formatter: (row) => row.orderNo ? row.orderNo : '', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'goodsName', label: '商品名称', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'goodsWeight', label: '商品重量', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'calculatedWeight', label: '聚水潭重量', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'quantity', label: '数量', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'goodsAvgWeight', label: '平均重量', tipmesg:'（计数为1，快递公司重量对应商品编码的均值）' },
  { sortable: 'custom', width: '80', align: 'center', prop: 'tsWeight', label: '推算重量之和', tipmesg:'（以快递单号为维度，所有商品编码平均重量*数量之和）'},
  { sortable: 'custom', width: '80', align: 'center', prop: 'hsWeight', label: '核算重量', tipmesg:'（聚水潭重量与推算重量之和取最大值）'},
  { sortable: 'custom', width: '80', align: 'center', prop: 'hsDiffWeight', label: '重量差额', tipmesg:'（快递公司重量-核算重量）' },
  { sortable: 'custom', width: '80', align: 'center', prop: 'hsExpressFee', label: '核算金额', tipmesg:'（根据核算重量运算快递规则）'},
  { sortable: 'custom', width: '80', align: 'center', prop: 'diffExpressFee', label: '金额差额', tipmesg:'（运费合计-核算金额）'},
 
]
export default {
  name: "erpExportall202410131003",
  components: {
    MyContainer, vxetablebase,queryCondition,inputYunhan
  },
  data() {
    return {
      goodsCodeList,
      labelsList,
      statusList,
      remarkList,
      topfilter: {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
      },
      dialogVisible: false,
      fileList: [],
      uploadLoading: false,
      fileparm: {},
      that: this,
      hsTitle:'计算',
      hsType:1,
      formatWarehouseNew:formatWarehouseNew,
      expressCompanyId: null,
      warehouselist: warehouselist,
      warehouse: null,
      platformlist: platformlist,
      pickerOptions,
      expresscompanylist: [],
      dialogVisible2:false,
      prosimstate: null,
      prosimstatelist: [],
      ListInfo: {
        DataType:3,
        currentPage: 1,
        pageSize: 50,
        orderBy: 'inportDate',
        isAsc: false,
        yearmonth:null,
        startTime: null,//开始时间
        endTime: null,//结束时间
        remark: null,//备注留言
        goodsCode: null,//商品编码
        orderNo: null,//内部单号
        labels: null,//标签
        status: null,//状态
        shopName: null,//店铺名称
        id: null,//ID
        isempty: null,//空数据
        batchNumber: null,//批次号
        mailNumberList: null,//邮件号
        noUseCatch: false,//非缓存
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      createTime:null,
      creatTime:null,
      ruleBatchNumber:'',
    }
  },
  async mounted() {
   // this.ListInfo.yearmonth= formatTime(new Date(),'YYYYMM');
    if (this.timeRanges && this.timeRanges.length == 0) {
      //默认给当前月第一天至今天
      this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
      this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
      this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
    }
    // await this.getList()
    await this.init()
  },
  methods: {
    productSelect(e,val) {
      this.$nextTick(() => {
      let value = e.target.value; // 输入框值
        if(value) { // 你输入才有这个值 不为空，如果你下拉框选择的话 这个值为空
          if(val == 1) {
            this.ListInfo.goodsCodeList = value
          } else if(val == 2) {
            this.ListInfo.labels = value
          } else if(val == 3) {
            this.ListInfo.status = value
          } else if(val == 4) {
            this.ListInfo.remark = value
          }
        }
      });
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    productCodeCallback(val) {
      this.ListInfo.originalOnlineOrderNo = val;
    },
    productCodeCallback2(val) {
      this.ListInfo.orderNo = val;
    },
    mailNumberCallback(val) {
      this.ListInfo.mailNumberList = val;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("creatTime", this.creatTime);
      // form.append("yearMonthDay", this.yearMonthDay);
      var res = await importExpressFeeCalculateReview(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
    },
    onSubmitUpload() {
      // if (!this.yearMonthDay) {
      //   this.$message({ message: "请选择日期", type: "warning" });
      //   return false;
      // }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
      //导入弹窗
      startImport() {
      this.fileList = []
      this.creatTime = null
      this.dialogVisible = true;
    },
    startClac() {
      this.hsTitle = "计算"
      this.hsType=1
    this.expressCompanyId = null
    this.prosimstate = null
    this.warehouse = null
    this.createTime = null
    this.expressCompanyId = this.topfilter.expressCompanyId ? (this.topfilter.expressCompanyId).toString() : null
    setTimeout(async() => {
      this.getprosimstatelist(1)
      this.prosimstate = this.topfilter.prosimstateId
      this.warehouse = this.topfilter.warehouseId
    }, 100);
 this.dialogVisible2 = true;
},
async clacDay(row) {
      if(!this.createTime){
        this.$message({ message: "请选择时间", type: "warning" });
        return false;
      }
      this.$confirm('确认计算?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.$message.success('计算提交成功，请稍后到账单明细页签查询')

        await monthExpressCompanyFeeWeightAllCalculate({ yearmonth: this.createTime,expressCompanyId:this.expressCompanyId,warehouse:this.warehouse
          ,prosimstate:this.prosimstate,ruleBatchNumber:this.ruleBatchNumber,dayBatchNumber:this.dayBatchNumber })
      }).catch(() => {
        this.$message.info('已取消计算')
      });
    },


async getprosimstatelist (val) {

var id;
if (val == 1)
   {
    id = this.expressCompanyId
    this.prosimstate=null
   }
else if (val == 2) {

}

var res = await getExpressComanyStationName({ id: id });
if (res?.code) {
    this.prosimstatelist = res.data
}
},
    async init() {
      this.$nextTick(() => {
        this.$refs.refqueryCondition.init()
      })
      const res = await getExpressComanyAll({});
      if (!res?.success) return
      this.expresscompanylist = res.data;
    },
    //导出
     async exportProps() {
      this.loading = true
      const res = await exportExpressInfoData_MonthALLWeight({...this.ListInfo,...this.topfilter})
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '重量差账单数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getExpressInfoData_MonthAllWeight({...this.ListInfo,...this.topfilter})
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary

        let summary = data.summary || {}

const resultsum = {};
Object.entries(summary).forEach(([key, value]) => {
    resultsum[key] = formatNumber(value);
});
function formatNumber(number) {
    const options = {
        useGrouping: true,
    };
    return new Intl.NumberFormat('zh-CN', options).format(number);
}
this.summaryarry = resultsum

        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
