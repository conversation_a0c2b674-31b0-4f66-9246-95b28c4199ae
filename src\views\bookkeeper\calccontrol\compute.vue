<template>
    <container>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            @select='selectchange' :isSelection='true' :isSelectColumn='true' :tableData='list' :tableCols='tableCols'
            :tableHandles='tableHandles' :showsummary='false' :loading="listLoading" />
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </container>
</template>
<script>
import { getDataCalcList, postCalcTask, postCalcTaskBatch, postCalcTaskShopCode } from '@/api/monthbookkeeper/financialDetail'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
const tableCols = [
    { istrue: true, prop: 'yearMonth', label: '月份', width: '70' },
    { istrue: true, prop: 'shopCode', label: '店铺编码', width: '80' },
    { istrue: true, prop: 'nameShop', label: '店铺名称', width: '300' },
    { istrue: true, prop: 'calcStatus', label: '计算状态', width: '80' },
    { istrue: true, type: 'button', label: '计算', width: '80', btnList: [{ label: "老计算", display: (row) => false, handle: (that, row) => that.onCalcHand(row) }] },
    //{ istrue: true, type: 'button', label: '新计算', width: '60', btnList: [{ label: "计算", display: (row) => false, handle: (that, row) => that.onCalcHand2(row) }] },
    { istrue: true, prop: 'clickTime', label: '点击计算时间', width: '150' },
    { istrue: true, prop: 'calcBegin', label: '开始计算时间', width: '150' },
    { istrue: true, prop: 'calcEnd', label: '结束计算时间', width: '150' },
    { istrue: true, prop: 'errorMsg', label: '错误信息', width: '200' },
    { istrue: true, prop: 'progress', label: '进度', type: "progress" },
];
const tableHandles = [
    { label: "批量老计算", handle: (that) => that.onCalcHandBatch() },
]
export default {
    name: 'Roles',
    components: { cesTable, container },
    props: {
        version: '',
        filter: {}
    },
    data() {
        return {
            that: this,
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "id", IsAsc: false },
            summaryarry: {},
            total: 0,
            sels: [],
            selids: [],
            selShopCodes: [],
            listLoading: false,
            pageLoading: false
        }
    },
    mounted() {

    },
    beforeUpdate() { },
    methods: {
        onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        async getlist() {
            this.selShopCodes = [];
            var pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }
            params.version = this.version;
            this.listLoading = true
            const res = await getDataCalcList(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res.data?.total
            const data = res.data?.list
            data?.forEach(d => {
                d._loading = false
            })
            this.list = data
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selsChange: function (sels) {
            this.sels = sels
        },
        selectchange: function (rows, row) {
            this.selids = [];
            this.selShopCodes = [];
            rows.forEach(f => {
                this.selids.push(f.id);
                this.selShopCodes.push(f.shopCode);
            })
        },
        async onCalcHand(row) {
            let that = this;
            if (!row.shopCode) {
                this.$message({ type: 'warning', message: '店铺错误!' });
                return;
            }
            if (!row.yearMonth) {
                this.$message({ type: 'warning', message: '月份错误!' });
                return;
            }
            var para = { shopCode: row.shopCode, yearMonth: row.yearMonth, version: this.version }
            if (row.calcStatus.indexOf("计算完成") >= 0) {
                this.$confirm(`${row.nameShop}已计算过, 是否重新计算?`, '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    await onCalcHand1(para)
                }).catch(() => {
                    //this.$message({ type: 'info', message: '已取消计算' });
                });
            }
            else if (row.calcStatus.indexOf("排队中") >= 0) {
                this.$confirm(`${row.nameShop}排队中, 是否重新计算?`, '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    await onCalcHand1(para)
                }).catch(() => {
                    //this.$message({ type: 'info', message: '已取消计算' });
                });
            }
            else if (row.calcStatus.indexOf("计算中") >= 0) {
                this.$confirm(`${row.nameShop}计算中, 是否重新计算?`, '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    await onCalcHand1(para)
                }).catch(() => {
                    //this.$message({ type: 'info', message: '已取消计算' });
                });
            }
            else
                await onCalcHand1(para);

            async function onCalcHand1(para) {
                await postCalcTask(para).then(async res => {
                    if (res.success) {
                        that.$message({ type: 'success', message: '提交完成，正在后台计算...!' });
                        await that.getList();
                    }
                    else
                        that.$message({ type: 'error', message: '提交失败!' });
                });
            }
        },
        async onCalcHandBatch() {
            let that = this;
            if (this.selShopCodes.length <= 0) {
                this.$message({ type: 'error', message: '未勾选' });
                return;
            }
            var para = { shopCodes: this.selShopCodes, yearMonth: this.filter.yearMonth, version: this.version };
            console.log(para);
            this.$confirm('确定要计算所有被勾选的店铺吗', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                await postCalcTaskBatch(para).then(async res => {
                    if (res.success) {
                        that.$message({ type: 'success', message: '提交完成，正在后台计算...!' });
                        await that.getList();
                    }
                    else
                        that.$message({ type: 'error', message: '提交失败!' });
                });
            }).catch(() => {
                //this.$message({ type: 'info', message: '已取消计算' });
            });
        },
        onCalcHand2(row) {
            let para = {
                yearMonth: row.yearMonth,
                shopCode: row.shopCode,
                platform: this.filter.platform,
                version: this.version
            };
            var me = this;
            this.$confirm('确定要计算吗', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                let res = await postCalcTaskShopCode(para);
                if (res.success) {
                    me.$message({ type: 'success', message: '任务创建完成，请稍后查询计算状态...!' });
                    //await me.getList();
                }
            }).catch(() => {
                //this.$message({ type: 'info', message: '已取消计算' });
            });
        },
    }
}
</script>