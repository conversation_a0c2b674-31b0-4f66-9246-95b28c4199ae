<template>
    <container>
        <template #header>
            <el-form :inline="true">
                <el-form-item>
                    <span>添加时间 </span>
                    <el-date-picker style="width: 210px" v-model="timerange" :picker-options="pickerOptions"
                        type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                        start-placeholder="开始" end-placeholder="结束" @change="changeTime"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <span>第一次采购日期 </span> 
                    <el-date-picker v-model="firstDate" type="daterange" unlink-panels range-separator="至"
                        start-placeholder="开始" end-placeholder="结束" style="width: 210px;"
                        class="publicCss" :value-format="'yyyy-MM-dd'" @change="changeFirstTime">
                    </el-date-picker>
                </el-form-item>
                <el-form-item style="width:90px;">
                    <YhUserselectormulti :value.sync="filter.addedIds" clearable placeholder="添加人">
                    </YhUserselectormulti>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.groups" clearable placeholder="小组" multiple collapse-tags
                        style="width: 150px;margin-right:3px">
                        <el-option v-for="item in groups" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.areas" clearable placeholder="区域" multiple collapse-tags
                        style="width: 140px;margin-right:3px">
                        <el-option v-for="item in areas" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.posts" clearable placeholder="职位" multiple collapse-tags
                        style="width: 150px;margin-right:3px">
                        <el-option v-for="item in posts" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.seriesCode" collapse-tags filterable remote reserve-keyword
                        placeholder="系列编码" clearable :remote-method="seriesCodesSearch" style="width: 90px"
                        :loading="seriesCodesLoading">
                        <el-option v-for="item in seriesCodes" :key="item.value" :label="item.label"
                            :value="item.value"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.productCode" collapse-tags filterable remote reserve-keyword
                        placeholder="成品编码" clearable :remote-method="productCodesSearch" style="width: 90px"
                        :loading="productCodesLoading">
                        <el-option v-for="item in productCodes" :key="item.value" :label="item.label"
                            :value="item.value"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.semiCode" collapse-tags filterable remote reserve-keyword
                        placeholder="半成品编码" clearable :remote-method="semiCodesSearch" style="width: 110px"
                        :loading="semiCodesLoading">
                        <el-option v-for="item in semiCodes" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="getList('Search')">查询</el-button>
                    <el-button type="primary" @click="onAddOrEdit('add')">新增</el-button>
                </el-form-item>
            </el-form>
            <el-radio-group v-model="tag" @change="changeTag">
                <el-radio-button label="成品转半成品"></el-radio-button>
                <el-radio-button label="半成品转成品"></el-radio-button>
            </el-radio-group>
        </template>

        <vxetablebase ref="table1" :id="'SemiAndFinishTable120240720'" v-if="tag == '成品转半成品'" :border="true"
            :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :showsummary="true"
            @sortchange='sortchange' :tableData='tableData1' :tablekey="'SemiAndFinishTable120240720'"
            :tableCols='tableCols1' :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0"
            key="SemiAndFinishTable120240720" :treeProp="{ rowField: 'id', parentField: 'parentId' }" :loading="loading" :height="'100%'">
        </vxetablebase>
        <vxetablebase ref="table2" v-if='tag == "半成品转成品"' :id="'SemiAndFinishTable220240720'" :that='that'
            :isIndex='true' :hasexpand='true' :tablefixed='true' :showsummary="true" @sortchange='sortchange'
            :tableData='tableData2' :tablekey="'SemiAndFinishTable220240720'" :tableCols='tableCols2'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0"
            key="SemiAndFinishTable220240720" :treeProp="{ rowField: 'id', parentField: 'parentId' }" :loading="loading" :border="true" :height="'100%'">

        </vxetablebase>

        <el-dialog title="新增" class="dialog" :visible.sync="visiable" width="850px" v-dialogDrag
            :close-on-click-modal="false">
            <el-form>
                <el-form-item v-if="type == 'add'">
                    <span class="required-field"> 系列编码 <el-select v-model="form.seriesCode" collapse-tags filterable
                            remote reserve-keyword clearable :remote-method="seriesCodesSearch"
                            style="width: 130px;margin-right: 30px;" :loading="seriesCodesLoading">
                            <el-option v-for="item in seriesCodes" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </span>
                    <span class="required-field">成品编码 <el-select v-model="form.productCode" collapse-tags filterable
                            remote reserve-keyword clearable :remote-method="productCodesSearch"
                            style="width: 130px;margin-right: 30px;" :loading="productCodesLoading">
                            <el-option v-for="item in productCodes" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </span>
                    <span class="required-field">半成品编码 <el-select v-model="form.semiCode" collapse-tags filterable
                            remote reserve-keyword clearable :remote-method="semiCodesSearch" style="width: 130px;"
                            :loading="semiCodesLoading">
                            <el-option v-for="item in semiCodes" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </span>
                </el-form-item>
                <el-form-item>
                    <span style="margin-right: 30px;" class="required-field">{{ tag == '成品转半成品' ? '成品原价' : '半成品原价' }}
                        <el-input-number v-model="form.originalCost" :precision="3" :step="1" :min="0"
                            :max="9999"></el-input-number></span>
                    <span style="margin-right: 30px;" class="required-field">降价额度 <el-input-number
                            v-model="form.discount" :precision="3" :step="1" :max="9999" :min="0"></el-input-number></span>
                    <span class="required-field">{{ tag == '成品转半成品' ? '半成品现价' : '成品现价' }} <el-input-number
                            v-model="form.currentPrice" :precision="3" :step="1" :max="9999" :min="0"></el-input-number></span>
                </el-form-item>
            </el-form>
            <div>
            <div style="margin: 10px 0 5px 0;">
              <el-link style="font-size: 14px;color: #409EFF;" :underline="false"  @click="onAddRow">新增一行</el-link>
            </div>
            <el-scrollbar style="height: 200px;">
              <div v-for="(item, i) in form.rows" :key="i"
                style="display: flex;align-items: center;margin-top: 5px;">
                <span style="margin-right: 30px;" class="required-field">{{ tag == '成品转半成品' ? '半成品采购量' : '成品采购量' }}
                        <el-input-number v-model="item.qty" :precision="0" :step="1" style="width: 120px;" :min="0"
                            :max="999999"></el-input-number></span>
                    <span style="margin-right: 30px;" class="required-field">采购单号
                        <el-input v-model="item.orderNo" placeholder="请输入内容" style="width: 120px;" maxlength="30"></el-input>
                    </span>
                    <span style="margin-right: 30px;" class="required-field">采购单时间
                        <el-date-picker v-model="item.firstTime" type="datetime" style="width: 180px;" placeholder="请选择"format="yyyy-MM-dd HH:mm:ss" :value-format="'yyyy-MM-dd HH:mm:ss'"
                            align="right">
                        </el-date-picker>
                    </span>
                <i class="el-icon-remove" style="font-size: 25px;margin-right: 10px;cursor: pointer;"
                  @click="onDelRow(item)"></i>
              </div>
            </el-scrollbar>
          </div> 
          <div>
            <div style="height: 130px;">
                    <uploadimgFile :accepttyes="accepttyes" :isImage="true" :uploadInfo="pictures" :keys="[1, 1]"
                        @callback="getImg" :imgmaxsize="10" :limit="10" :multiple="true" v-if="visiable">
                    </uploadimgFile>
                </div>
                <div style="text-align: right;">
                    <el-button type="plain" @click="onCancel">取消</el-button>
                    <el-button type="primary" @click="onSubmit">确定</el-button>
                </div>
          </div>    
        </el-dialog>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </container>
</template>

<script>

import container from "@/components/my-container";
import YhUserselectormulti from "@/components/YhCom/yh-userselectormulti.vue";
import { getGroupsList, getAreasList, getPostsList, getGoodCodes, addSemiPurchase, addProductPurchase, getSemiPurchase, getProductPurchase, deleteProductPurchase, deleteSemiPurchase, editProductPurchase, editSemiPurchase } from "@/api/inventory/SemiAndFinish.js"
import { getListByStyleCode } from "@/api/inventory/basicgoods.js"
import { pickerOptions } from '@/utils/tools'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import vxetablebase from  "@/components/VxeTable/vxetablebase.vue";

function generateDatesForCurrentMonth() {
  const today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth(); // 当前月份（0-11）

  // 获取本月的第一天
  const firstDayOfMonth = new Date(year, month, 2);

  // 格式化日期
  const formattedFirstDay = firstDayOfMonth.toISOString().split('T')[0];
  const formattedToday = today.toISOString().split('T')[0];

  // 返回数组
  return [formattedFirstDay, formattedToday];
}

const tableCols1 = [
    { istrue: true, prop: 'addedBy', label: '添加人', width: '90', sortable: 'custom', align: 'center' ,treeNode: true,fixed: 'left',},
    { istrue: true, prop: 'groupName', label: '小组', width: '240', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'area', label: '区域', width: '60', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'position', label: '职位', width: '80', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'seriesCode', label: '系列编码', width: '120', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'productCode', label: '成品编码', width: '120', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'semiCode', label: '半成品编码', width: '120', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'productName', label: '成品名称', width: '240', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'semiName', label: '半成品名称', width: '240', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'originalCost', label: '原成本', width: '80', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'discount', label: '降价额度', width: '120', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'discountedPrice', label: '降价后成本', width: '120', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'currentPrice', label: '半成品现价', width: '120', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'addedTime', label: '添加时间', width: '150', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'firstTime', label: '第一次采购时间', width: '150',  align: 'center' },
    { istrue: true, prop: 'orderNo', label: '采购单号', width: '120', align: 'center' },
    { istrue: true, prop: 'qty', label: '采购数量（汇总）', sortable: 'custom', width: '150', align: 'center' },
    { istrue: true, prop: 'performance', label: '未计算业绩（汇总）', width: '180', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'picture', label: '附件', type: 'images', width: '80', align: 'left' },
    {
        istrue: true, type: "button", label: '操作', fixed: 'right', width: '90',
        btnList: [
            { label: "编辑", handle: (that, row) => that.onAddOrEdit('edit', row) ,ishide: (that, row) => { return row.parentId!=0 }},
            { label: "删除", handle: (that, row) => that.onDelete(row) ,ishide: (that, row) => { return row.parentId!=0 }},
        ]
    },
];
const tableCols2 = [
    { istrue: true, prop: 'addedBy', label: '添加人', width: '90', sortable: 'custom', align: 'center',treeNode: true,fixed: 'left', },
    { istrue: true, prop: 'groupName', label: '小组', width: '240', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'area', label: '区域', width: '60', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'position', label: '职位', width: '80', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'seriesCode', label: '系列编码', width: '120', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'semiCode', label: '半成品编码', width: '120', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'productCode', label: '成品编码', width: '120', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'semiName', label: '半成品名称', width: '240', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'productName', label: '成品名称', width: '240', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'originalCost', label: '原成本', width: '80', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'discount', label: '降价额度', width: '120', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'discountedPrice', label: '降价后成本', width: '120', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'currentPrice', label: '成品现价', width: '120', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'addedTime', label: '添加时间', width: '150', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'firstTime', label: '第一次采购时间', width: '150',align: 'center' },
    { istrue: true, prop: 'orderNo', label: '采购单号', width: '120',  align: 'center' },
    { istrue: true, prop: 'qty', label: '采购数量（汇总）', sortable: 'custom', width: '150', align: 'center' },
    { istrue: true, prop: 'performance', label: '未计算业绩(汇总)', width: '180', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'picture', label: '附件', type: 'images', width: '80', align: 'left' },
    {
        istrue: true, type: "button", label: '操作', fixed: 'right', width: '90',
        btnList: [
            { label: "编辑", handle: (that, row) => that.onAddOrEdit('edit', row) ,ishide: (that, row) => { return row.parentId!=0 }},
            { label: "删除", handle: (that, row) => that.onDelete(row) ,ishide: (that, row) => { return row.parentId!=0 }},
        ]
    },
]

export default {
    name: 'SemiAndFinish',
    components: { container, YhUserselectormulti, pickerOptions, uploadimgFile, vxetablebase },

    data() {
        return {
            that: this,
            filter: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,
                endTime: null,
                addedIds: null,
                groups: null,
                areas: null,
                posts: null,
                seriesCode: null,
                productCode: null,
                semiCode: null,
                firstStartTime: null,
                firstEndTime: null
            },
            form: {
                id: null,
                seriesCode: null,
                productCode: null,
                semiCode: null,
                originalCost: 0,
                discount: 0,
                currentPrice: 0,
                rows:[{qty: null,orderNo: null,firstTime: null,}],
                attachment: "",
            },
            pickerOptions: pickerOptions,
            timerange: generateDatesForCurrentMonth(),
            groups: [],
            areas: [],
            posts: [],
            seriesCodes: [],
            seriesCodesLoading: false,
            productCodes: [],
            productCodesLoading: false,
            semiCodes: [],
            semiCodesLoading: false,
            tag: null,
            type: null,
            visiable: false,
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            pictures: [],
            tableData1: [],
            tableData2: [],
            tableCols1,
            tableCols2,
            loading: false,
            total: 0,
            firstDate: []
        };
    },

    async mounted() {
        this.tag = "成品转半成品";
        this.groups = await getGroupsList();
        this.areas = await getAreasList();
        this.posts = await getPostsList();
        this.filter.orderBy = 'addedTime';
        await this.getList();
    },

    methods: {
        changeFirstTime(e){
            this.filter.firstStartTime = e ? e[0] : null
            this.filter.firstEndTime = e ? e[1] : null
        },
        //改变时间
        async changeTime(e) {
            // this.filter.startTime = e ? e[0] : null;
            // this.filter.endTime = e ? e[1] : null;
        },
        //搜索系列编码
        async seriesCodesSearch(query) {
            if (query && query.length > 40) return this.$message.error("输入内容过长");
            if (query !== '') {
                this.seriesCodesLoading == true
                this.seriesCodes = [];
                setTimeout(async () => {
                    const res = await getListByStyleCode({ currentPage: 1, pageSize: 50, styleCode: query })
                    this.searchloading = false
                    res?.data?.forEach(f => {
                        this.seriesCodes.push({ value: f.styleCode, label: f.styleCode })
                    });
                }, 200)
            }
            else {
                this.seriesCodes = []
            }
        },
        //搜索产品编码
        async productCodesSearch(query) {
            if (query && query.length > 40) return this.$message.error("输入内容过长");
            if (query !== '') {
                this.productCodesLoading == true
                this.productCodes = [];
                setTimeout(async () => {
                    let res
                    if (this.visiable == false) {
                        res = await getGoodCodes({ query: query, type: 1, goodCode: this.filter.semiCode })
                    } else {
                        res = await getGoodCodes({ query: query, type: 1, goodCode: this.form.semiCode })
                    }
                    this.productCodesLoading = false
                    res?.forEach(f => {
                        this.productCodes.push({ value: f, label: f })
                    });
                }, 200)
            }
            else {
                this.productCodes = []
            }
        },
        //搜索半成品编码
        async semiCodesSearch(query) {
            if (query && query.length > 40) return this.$message.error("输入内容过长");
            if (query !== '') {
                this.semiCodesLoading == true
                this.semiCodes = [];
                setTimeout(async () => {
                    let res
                    if (this.visiable == false) {
                        res = await getGoodCodes({ query: query, type: 0, goodCode: this.filter.productCode })
                    } else {
                        res = await getGoodCodes({ query: query, type: 0, goodCode: this.form.productCode })
                    }
                    this.semiCodesLoading = false
                    res?.forEach(f => {
                        this.semiCodes.push({ value: f, label: f })
                    });
                }, 200)
            }
            else {
                this.semiCodes = []
            }
        },
        //获取列表
        async getList(type) {
            this.loading = true
            if (type == "Search") {
                this.filter.currentPage = 1;
                this.$refs.pager.setPage(1)
            }
            this.filter.startTime = this.timerange ? this.timerange[0] : null;
            this.filter.endTime = this.timerange ? this.timerange[1] : null;
            if (this.tag == "成品转半成品") {
                const { success, data } = await getSemiPurchase(this.filter);
                if (success) {
                    data.list.forEach(item => {
                        item.picture = item.attachment ? JSON.stringify(item.attachment.split(',').map(item => {
                            return {
                                url: item,
                                name: item
                            }
                        })) : []
                    })
                    this.tableData1 = data.list;
                    this.total = data.total;
                } else {
                    this.$message.error("获取列表失败!");
                }
            } else {
                const { success, data } = await getProductPurchase(this.filter);
                if (success) {
                    data.list.forEach(item => {
                        item.picture = item.attachment ? JSON.stringify(item.attachment.split(',').map(item => {
                            return {
                                url: item,
                                name: item
                            }
                        })) : []
                    })
                    this.tableData2 = data.list;
                    this.total = data.total;
                } else {
                    this.$message.error("获取列表失败!");
                }
            }
            this.loading = false
        },
        clear() {
            this.form = {
                id: null,
                seriesCode: null,
                productCode: null,
                semiCode: null,
                originalCost: 0,
                discount: 0,
                currentPrice: 0,
                rows:[{qty: null,orderNo: null,firstTime: null,}],
                attachment: "",
            }
            this.semiCodes = [];
            this.productCodes = [];
            this.pictures = [];
        },
        //新增或编辑
        onAddOrEdit(type, row) {
            if (type == "add") {
                this.clear()
            } else {
                this.pictures = !Array.isArray(row.picture) ? JSON.parse(row.picture) : []
                this.form.id = row.id;
                this.form.seriesCode=row.seriesCode;
                this.form.productCode=row.productCode,
                this.form.semiCode=row.semiCode;
                this.form.originalCost = row.originalCost;
                this.form.discount = row.discount;
                this.form.currentPrice = row.currentPrice;
                this.form.rows = row.rows;
                this.form.attachment = row.attachment;
            }
            this.type = type
            this.visiable = true;
        },
        //关闭弹出框
        onCancel() {
            this.visiable = false;
            this.clear()
        },
        //校验单号
        validOrderNo(val) {
            const Regex = /^[0-9]*$/;
            if (Regex.test(val)) {
                return true;
            } else {
                this.form.orderNo = null;
                this.$message.error("采购单号必须为数字!");
                return false;
            }
        },
        //获取图片
        getImg(data) {
            if (data) {
                this.pictures = data ? data : []
                this.form.attachment = data.map(item => item.url).join(',')
            }
        },
        //提交表单
        async onSubmit() {
            const form = this.form;
            let tag =false;
            if(!(form.originalCost&&form.discount&&form.currentPrice&&form.seriesCode&&form.productCode&&form.semiCode))
                {
                    tag=true;
                }

            form.rows.forEach(row=>{
                if(!(row.qty&&row.orderNo&&row.firstTime))
                {
                    tag = true;
                }
            });
            if(tag) 
            {
                this.$message.error("除附件外均为必填项!");
                return;
            }

            if (this.type == "add") {
                if (this.tag == "成品转半成品") {
                    const { success } = await addSemiPurchase(this.form);
                    if (success) {
                        this.$message.success("添加成功!");
                        this.visiable = false
                        this.getList('Search')
                    } 
                } else {
                    const { success } = await addProductPurchase(this.form);
                    if (success) {
                        this.$message.success("添加成功!");
                        this.visiable = false
                        this.getList('Search')
                    }
                }
            } else {
                if (this.tag == "成品转半成品") {
                    const { success } = await editSemiPurchase(this.form);
                    if (success) {
                        this.$message.success("编辑成功!");
                        this.visiable = false;
                        this.getList();
                    }
                } else {
                    const { success } = await editProductPurchase(this.form);
                    if (success) {
                        this.$message.success("编辑成功!");
                        this.visiable = false;
                        this.getList();
                    }
                }
            }
        },
        //改变查询的表格
        async changeTag() {
            this.filter.orderBy = 'addedTime';
            this.filter.currentPage = 1;
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        async onDelete(row) {
            var that = this;
            this.$confirm("确定删除此记录?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                if (this.tag == "成品转半成品") {
                    const { success } = await deleteSemiPurchase({ Id: row.id });
                    if (success) {
                        this.$message.success("删除成功!");
                        this.getList()
                    }
                }
                else {
                    const { success } = await deleteProductPurchase({ Id: row.id });
                    if (success) {
                        this.$message.success("删除成功!");
                        this.getList()
                    }
                }
            });

        },
        //排序
        async sortchange(column) {
            this.filter.orderBy = column.prop
            this.filter.isAsc = column.order.indexOf("descending") == -1 ? true : false
            await this.getList();
        },
        //改变页面大小
        async Sizechange(val) {
            this.filter.pageSize = val;
            this.filter.currentPage = 1;
            this.getList()
        },
        //改变当前页
        async Pagechange(val) {
            this.filter.currentPage = val;
            this.getList()
        },
        onAddRow() {
            this.form.rows.push({ qty: null, orderNo: null, firstTime: null, });
        },
        onDelRow(item) {
            if (this.form.rows.length > 1) {
                this.form.rows = this.form.rows.filter((row) => row != item);
            } else {
                this.$message.error("最后一个元素不允许删除");
            }

        }
    },
};
</script>

<style lang="scss" scoped>
::v-deep .el-select__tags-text {
    max-width: 40px;
}

.required-field:before {
    content: "*";
    color: red;
    margin-right: 2px;
}
</style>