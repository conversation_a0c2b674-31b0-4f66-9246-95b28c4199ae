<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <dateRange :startDate.sync="query.startDate" :endDate.sync="query.endDate" class="publicCss" />
        <inputYunhan ref="goodsCode" :inputt.sync="query.goodsCode" v-model="query.goodsCode" width="180px"
          placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
          @callback="goodsCodeCallback" title="商品编码" style="margin: 0 5px 5px 0;">
        </inputYunhan>
        <el-input v-model.trim="query.proCode" placeholder="宝贝ID" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="query.title" placeholder="标题" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="query.seriesName" placeholder="系列编码" maxlength="50" clearable class="publicCss" />
        <el-select v-model="query.isLossQty" placeholder="是否掉量" class="publicCss" clearable>
          <el-option label="是" :value="true" />
          <el-option label="否" :value="false" />
        </el-select>
        <number-range class="publicCss1" :min.sync="query.lossQtyMin" :max.sync="query.lossQtyMax" min-label="掉量数量最小值"
          max-label="量数量最大值" />
        <number-range class="publicCss1" :min.sync="query.lossRateMin" :max.sync="query.lossRateMax" min-label="掉量比例最小值"
          max-label="掉量比例最大值" />
        <div>
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" :disabled="isExport" @click="exportProps">导出</el-button>
        </div>
      </div>
    </template>
    <vxetablebase :id="'prepackDrop202408041852'" ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
      :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
      :is-select-column="false" :is-index-fixed="false" style="width: 100%;  margin: 0" :height="'100%'"
      :showsummary="data.summary ? true : false" :summaryarry="data.summary" @sortchange="sortchange"
      @onTrendChart="trendChart">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false" direction="btt">
      <div v-if="!chatProp.chatLoading">
        <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="margin: 10px;" @change="
            trendChart({
              ...chatPropOption,
              startDate: $event[0],
              endDate: $event[1],
            })
            " />
        <buschar v-if="!chatProp.chatLoading" :analysis-data="chatProp.data" />
      </div>
      <div v-else v-loading="chatProp.chatLoading" />
    </el-drawer>
  </MyContainer>
</template>
<script>
import MyContainer from '@/components/my-container'
import numberRange from '@/components/number-range/index.vue'
import vxetablebase from '@/components/VxeTable/yh_vxetable.vue'
import dynamicSelecter from '@/components/customQuery/selecter.vue'
import { download } from '@/utils/download'
import buschar from '@/components/Bus/buschar'
import dayjs from 'dayjs'
import dateRange from '@/components/date-range/index.vue'
import inputYunhan from "@/components/Comm/inputYunhan";
import {
  getColumns, pageGetData, exportData, getTrendChart
} from '@/api/vo/prePackLossQtyReason'
import { formatLinkProCode, pickerOptions } from '@/utils/tools'
export default {
  name: 'prepackDrop',
  components: {
    MyContainer, vxetablebase, dynamicSelecter, numberRange, buschar, dateRange, inputYunhan
  },
  data() {
    return {
      chatPropOption: {},
      pickerOptions,
      rules: {
      },
      that: this,
      query: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
        endDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
        isLossQty: true,
      },
      data: { total: 0, list: [], summary: {} },
      tableCols: [],
      loading: false,
      isExport: false,
      ruleForm: {},
      chatProp: {
        chatDialog: false, // 趋势图弹窗
        chatTime: null, // 趋势图时间
        chatLoading: true, // 趋势图loading
        data: []// 趋势图数据
      },
    }
  },
  async mounted() {
    await this.getCol()
    this.getList()
  },
  methods: {
    //商品编码
    goodsCodeCallback(val) {
      this.query.goodsCode = val;
    },
    async trendChart(option) {
      var endDate = null;
      var startDate = null;

      if (option.startDate && option.endDate) {
        startDate = option.startDate;
        endDate = option.endDate;
      } else {
        endDate = option.date;
        startDate = new Date(option.date);
        startDate.setDate(option.date.getDate() - 30);

        startDate = dayjs(startDate).format("YYYY-MM-DD");
        endDate = dayjs(endDate).format("YYYY-MM-DD");
      }
      option.filter.filters = option.filter.filters.filter((item) => item.field !== option.dateField);
      option.filter.filters.push({
        field: option.dateField,
        operator: "GreaterThanOrEqual",
        value: startDate,
      });
      option.filter.filters.push({
        field: option.dateField,
        operator: "LessThanOrEqual",
        value: endDate,
      });

      option.startDate = startDate;
      option.endDate = endDate;

      this.chatProp.chatTime = [startDate, endDate];

      this.chatProp.chatLoading = true;

      const { data, success } = await getTrendChart(option);
      if (success) {
        this.chatProp.data = data;
      }

      this.chatProp.chatLoading = false;
      this.chatProp.chatDialog = true;

      this.chatPropOption = option;
    },
    async getCol() {
      const { data, success } = await getColumns()
      if (success) {
        let modifiedData = data.map(item => {
          if (item.prop == 'proCode') {
            return {
              ...item,
              type: 'html',
              formatter: (row) => formatLinkProCode(row.platform, row.proCode)
            };
          }
          return item;
        });
        modifiedData = modifiedData.filter(item => item.prop !== 'reason' && item.prop !== 'createTime' && item.prop !== 'createUName');
        this.tableCols = modifiedData;
        this.query.summarys = data.filter(a => a.summaryType).map(a => { return { column: a['sort-by'], summaryType: a.summaryType } })
      }
    },
    // 导出数据,这里前端可以封装一个方法
    async exportProps() {
      this.isExport = true
      await exportData(this.query).then(download).finally(() => {
        this.isExport = false
      })
    },
    async getList(type) {
      if (type === 'search') {
        this.query.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      // 使用时将下面的方法替换成自己的接口
      try {
        const { data, success } = await pageGetData(this.query)
        if (success) {
          this.data = data
        } else {
          this.$message.error('获取列表失败')
        }
      } catch (error) {
        this.$message.error('获取列表失败')
      } finally {
        this.loading = false
      }
    },
    // 每页数量改变
    Sizechange(val) {
      this.query.currentPage = 1
      this.query.pageSize = val
      this.getList()
    },
    // 当前页改变
    Pagechange(val) {
      this.query.currentPage = val
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.query.orderBy = prop
        this.query.isAsc = order.indexOf('descending') === -1
        this.getList()
      }
    }
  }
}
</script>
<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
  flex-wrap: wrap;

  .publicCss {
    width: 180px;
    margin: 0 5px 5px 0;
  }

  .publicCss1 {
    height: 28px;
    width: 210px;
    margin: 0 5px 5px 0;
  }
}
</style>
