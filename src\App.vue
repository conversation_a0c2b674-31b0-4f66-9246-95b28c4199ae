<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
import {  PageReqLog } from '@/api/admin/login-log.js'
export default {
  name: 'App',
  data () {
      return {
          pagedata: {}
      }
  },
  mounted() {
    let _this = this;
    window.addEventListener("mousedown", _this.downclilog);
 },
  methods: {
    downclilog(res){
      let _this = this;
        if(res.target.textContent){
          _this.pagedata.action =  res.target?.textContent;
          setTimeout(async()=>{
            for(var i = 0; i<document.getElementsByClassName('el-breadcrumb__inner').length; i++){
              if(document.getElementsByClassName('el-breadcrumb__inner').length==i+1){
                _this.pagedata.modul = document.getElementsByClassName('el-breadcrumb__inner')[0]?.innerText;
                _this.pagedata.pageName = document.getElementsByClassName('el-breadcrumb__inner')[document.getElementsByClassName('el-breadcrumb__inner').length-1]?.innerText;
              }
            }
            if(_this.pagedata.pageName){
              for(var i = 0; i<document.getElementsByClassName('is-active').length; i++){
                  if(document.getElementsByClassName('is-active')[i].textContent==_this.pagedata.pageName){
                    _this.pagedata.pageTab =  document.getElementsByClassName('is-active')[i + 2]?.textContent;
                  }
              }
            }

            if(_this.checkAndTrimString(_this.pagedata.action)){
              await PageReqLog(_this.pagedata.modul,_this.pagedata.pageName,_this.pagedata.pageTab?_this.pagedata.pageTab:_this.pagedata.pageName,_this.checkAndTrimString(_this.pagedata.action));
            }
          }, 500)
        }
      },
      checkAndTrimString(str) {
        let _this = this;
          if(_this.pagedata.action==_this.pagedata.pageTab){//点击一级页签 放行
            return str;
          }
          if(_this.pagedata.action==_this.pagedata.pageName){ //点击左侧菜单归动作查询 放行
            return '查询';
          }
          if (str.length > 10) {   //超过10长度 限行
              return false;  
          }
          let isnext = this.nameisExitfuc(str);
          if(!isnext){ //不包含于关键字 限行
            return false;
          }
          const trimmedStr = str.replace(/\s/g, '');  
          return trimmedStr;  
      },
      nameisExitfuc(name){ //判断字符串包含
        let nameisExit = false;
        let namearr = ["取",'确','打','增','添','新','删','改','修','提','审','保','编','撤','批','上','操','采','日','查','导'];
        namearr.map((item)=>{
          if(name.indexOf(item) != -1){
            nameisExit = true;
          }
        })
        return nameisExit;
      }
  },
  beforeDestroy() { // 销毁
      let _this = this;
      window.removeEventListener('mousedown', _this.downclilog())
  },
  
}
</script>
<style lang="scss">
.vxe-table--tooltip-wrapper {
    z-index: 10000 !important;
}
</style>
