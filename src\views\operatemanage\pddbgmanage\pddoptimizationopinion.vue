<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="日期">
                    <el-date-picker style="width: 230px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        :picker-options="pickerOptions"></el-date-picker>
                </el-form-item>
                <el-form-item label="运营组：">
                    <el-select filterable v-model="filter.groupId" placeholder="运营组" style="width: 110px" clearable>
                        <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value"
                            :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item label="店铺名称:">
                    <el-select filterable v-model="filter.shopCode" placeholder="请选择店铺" clearable style="width: 120px">
                        <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.shopCode" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='list'
            :tableCols='tableCols' :isSelection="false" :loading="listLoading" :summaryarry="summaryarry">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <template>
                    <el-date-picker style="width: 410px" v-model="buscharDialog.filter.timerange" type="daterange"
                        @change="similarityDateChange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false"
                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        :picker-options="pickerOptions"></el-date-picker>
                </template>
            </span>
            <span>
                <buschar v-if="buscharDialog.visible" ref="buschar" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { getPingduoduoOptimizationOpinionList, getPingduoduoOptimizationOpinionAnalysisAsync,exportPingduoduoOptimizationOpinionList } from '@/api/operatemanage/pddbgmanage/pddbgmanage'
import { getDirectorGroupList, getList as getshopList } from '@/api/operatemanage/base/shop'
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { Loading } from 'element-ui';
import buschar from '@/components/Bus/buschar'

const tableCols = [
    { istrue: true, prop: 'date', label: '日期', width: '200', sortable: 'custom', formatter: (row) => formatTime(row.date, "YYYY-MM-DD") },
    { istrue: true, prop: 'groupId', label: '运营组', tipmesg: '', width: '200', sortable: 'custom', formatter: (row) => row.groupName },
    { istrue: true, prop: 'shopName', label: '店铺名称', tipmesg: '', width: '250', sortable: 'custom', },
    { istrue: true, prop: 'consumerPoints', label: '消费者服务体验分', tipmesg: '', sortable: 'custom' },
    { istrue: true, prop: 'attitudePoints', label: '服务态度体验分', tipmesg: '', sortable: 'custom' },
    { istrue: true, prop: 'basePoints', label: '基础服务体验分', tipmesg: '', sortable: 'custom' },
    { istrue: true, prop: 'goodPoints', label: '商品服务体验分', tipmesg: '', sortable: 'custom' },
    { istrue: true, prop: 'sendGoodPoints', label: '发货服务体验分', tipmesg: '', sortable: 'custom' },
    { istrue: true, prop: 'logisticsPoints', label: '物流服务体验分', tipmesg: '', sortable: 'custom' },
    { istrue: true, prop: 'shopCreateTime', label: '趋势图', tipmesg: '', width: '200', formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
]

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunHanAdminPddOptimizationOpinion',
    components: { container, cesTable, MyConfirmButton,buschar},
    data() {
        return {
            that: this,
            filter: {
                startTime: null,
                endTime: null,
                groupId: null,
                shopCode: null,
                timerange: [startDate, endDate],
                timerange2: []
            },
            list: [],
            shopList: [],
            directorGroupList: [],
            pager: { OrderBy: "date", IsAsc: false },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            pickOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            tableCols: tableCols,
            total: 0,
            sels: [],
            uploadLoading: false,
            dialogVisible: false,
            listLoading: false,
            fileList: [],
            summaryarry: {},
            buscharDialog: {
                title: "趋势图",
                visible: false,
                data: [],
                filter: {
                    shopName: null,
                    startTime: null,
                    endTime: null,
                    timerange: null
                }
            },
        };
    },

    async mounted() {
        await this.onSearch()
        await this.loadData()
    },

    methods: {
        //获取店铺
        async loadData() {
            this.categorylist = []
            const res1 = await getshopList({ platform: 2, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list;
            let res3 = await getDirectorGroupList({})
            this.directorGroupList = res3.data
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            if (this.pager.OrderBy == null) {
                this.pager.OrderBy = "date";
                this.pager.IsAsc = false;
            }
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await getPingduoduoOptimizationOpinionList(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.list = data
            this.summaryarry = res.data.summary;
        },
        async nSearch() {
            await this.getlist()
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        async onExport() {
            if (this.pager.OrderBy == null) {
                this.pager.OrderBy = "date";
                this.pager.IsAsc = false;
            }
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            } else {
                this.$message({ message: "请选择时间！", type: "warning" });
                return;
            }
            let pager = this.$refs.pager.getPager();
            let params = { ...pager, ...this.pager, ...this.filter };
            let res = await exportPingduoduoOptimizationOpinionList(params);
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }

            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '拼多多消费体验指标_' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        async showchart(row) {
            let loadingInstance = Loading.service();
            Loading.service({ fullscreen: true });
            let that = this;
            let end = new Date();
            let start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            end.setTime(end.getTime());

            this.buscharDialog.filter.timerange = [start, end];
            if (this.buscharDialog.filter.timerange) {
                this.buscharDialog.filter.startTime = this.buscharDialog.filter.timerange[0];
                this.buscharDialog.filter.endTime = this.buscharDialog.filter.timerange[1];
            }
            this.buscharDialog.filter.shopName = row.shopName;
            let params = { shopName: row.shopName, startTime: this.buscharDialog.filter.startTime, endTime: this.buscharDialog.filter.endTime };
            await getPingduoduoOptimizationOpinionAnalysisAsync(params).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data;
            });
            await this.$refs.buschar.initcharts()
            loadingInstance.close();
        },
        async similarityDateChange() {
            this.buscharDialog.filter.startTime = null;
            this.buscharDialog.filter.endTime = null;
            if (this.buscharDialog.filter.timerange) {
                this.buscharDialog.filter.startTime = this.buscharDialog.filter.timerange[0];
                this.buscharDialog.filter.endTime = this.buscharDialog.filter.timerange[1];
            }
            let params = { shopName: this.buscharDialog.filter.shopName, startTime: this.buscharDialog.filter.startTime, endTime: this.buscharDialog.filter.endTime };
            let that = this;
            await getPingduoduoOptimizationOpinionAnalysisAsync(params).then(res => {
                that.buscharDialog.data = res.data;
            });
            await this.$refs.buschar.initcharts()
        },
    }
};
</script>

<style lang="scss" scoped></style>
