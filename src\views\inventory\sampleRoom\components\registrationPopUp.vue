<template>
  <div v-loading="loadingList">
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="110px" class="demo-ruleForm">
      <div>
        样品基础信息
      </div>
      <el-row>
        <el-col :span="6">
          <el-form-item label="样品来源" prop="sampleSource">
            <el-select v-model="ruleForm.sampleSource" placeholder="请选择样品来源" clearable class="formCss">
              <el-option label="对手样品" value="对手样品"></el-option>
              <el-option label="商家样品" value="商家样品"></el-option>
              <el-option label="公司样品" value="公司样品"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="公司商品编码" :prop="ruleForm.sampleSource == '公司样品' ? 'yhGoodsCode' : undefined">
            <el-select v-model="ruleForm.yhGoodsCode" placeholder="模糊搜索公司商品编码" :remote-method="remoteCompanyCodeMethod"
              remote clearable filterable class="formCss" @change="onYhGoodsMothod">
              <div slot="prefix" v-if="companyCodeloading">
                <i class="el-icon-loading" style="color: #409EFF" />
              </div>
              <el-option v-for="(item, i) in companyCodeList" :key="item.goodsCode + i" :label="item.goodsCode"
                :value="item.goodsCode" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品编码" prop="goodsCode">
            <el-input v-model="ruleForm.goodsCode" placeholder="请输入商品编码" clearable class="formCss" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品名称" prop="goodsName">
            <el-input v-model="ruleForm.goodsName" placeholder="请输入商品名称" clearable class="formCss" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="商品编号" prop="goodsNumber">
            <el-input v-model="ruleForm.goodsNumber" placeholder="请输入商品编号" clearable class="formCss" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="对手名称" prop="opponentName">
            <el-input v-model="ruleForm.opponentName" placeholder="请输入对手名称" clearable class="formCss" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="对手店铺" prop="opponentShopName">
            <el-input v-model="ruleForm.opponentShopName" placeholder="请输入对手店铺" clearable class="formCss" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="对手链接" prop="opponentLink">
            <el-input v-model="ruleForm.opponentLink" placeholder="请输入对手链接" clearable class="formCss" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="厂家名称" prop="manufacturerName">
            <el-input v-model="ruleForm.manufacturerName" placeholder="请输入厂家名称" clearable class="formCss" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="厂家地址" prop="manufacturerAddress">
            <el-input v-model="ruleForm.manufacturerAddress" placeholder="请输入厂家地址" clearable class="formCss" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="厂家链接" prop="manufacturerLink">
            <el-input v-model="ruleForm.manufacturerLink" placeholder="请输入厂家链接" clearable class="formCss" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="仓库" prop="warehouse">
            <el-select v-model="ruleForm.warehouse" placeholder="请选择仓库" clearable class="formCss"
              @change="changeWarehouse">
              <el-option v-for="item in stashList" :key="item" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="库位" prop="bitNumber">
            <el-select v-model="ruleForm.bitNumber" placeholder="请模糊搜索并选择库位" :remote-method="remoteMethod" remote
              clearable filterable :loading="searchloading" class="formCss" :disabled="ruleForm.storageStatus == 3">
              <el-option v-for="(item, i) in locationList" :key="item.warehouseBitCode + i"
                :label="item.warehouseBitCode" :value="item.warehouseBitCode" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="快递单号/标识" prop="expressNo">
            <el-input v-model="ruleForm.expressNo" placeholder="请输入快递单号/标识" clearable class="formCss" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="款式名称" prop="styleCode">
            <el-input v-model="ruleForm.styleCode" placeholder="请输入款式名称" clearable class="formCss" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="参考商品编码" prop="ckGoodsCode">
            <el-input v-model="ruleForm.ckGoodsCode" placeholder="请输入参考商品编码" clearable class="formCss" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="采购单号" prop="buyNo">
            <el-input v-model="ruleForm.buyNo" placeholder="请输入采购单号" clearable class="formCss" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="拍摄次数" prop="numberShots">
            {{ ruleForm.numberShots }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="拍摄时间" prop="shootingTime">
            {{ ruleForm.shootingTime }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-form-item label="备注信息" prop="remarks">
          <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" placeholder="请输入备注信息"
            v-model="ruleForm.remarks" maxlength="100" show-word-limit>
          </el-input>
        </el-form-item>
      </el-row>
      <el-row>
        <el-col :span="16">
          <el-form-item>
            <template #label>
              <span style="color: #F56C6C; margin-right: 2px">*</span>
              商品图片
            </template>
            <uploadimgFile ref="tupianuploadimgFile" v-if="logVisible1" :disabled="isView" :ispaste="!isView"
              :noDel="isView" :reveal="true" :accepttyes="accepttyes" :isImage="true" :uploadInfo="ruleForm.images"
              :keys="[1, 1]" @callback="getImg($event, 'images')" @beforeUpload="beforeUpload($event, 'images')"
              :imgmaxsize="1" :limit="1" :multiple="true">
            </uploadimgFile>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="样品条码" prop="sampleCode">
            <div id="print">
              <div class="print_item">
                <div v-if="ruleForm.goodsNumber">
                  <img :id="'barcode1' + ruleForm.goodsNumber" style="width: 150px; height: 100px;" />
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <div>
        商品参数信息
      </div>
      <el-row style="margin-bottom: 17px;">
        <el-col :span="6">
          <el-form-item label="折叠单边是否超40CM" prop="isChao40CM" label-width="163px">
            <el-select v-model="ruleForm.isChao40CM" placeholder="请选择折叠单边是否超40CM" clearable class="append_unit"
              @change="onFoldingMethod()">
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="0"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否可折叠" prop="isZheDie">
            <el-select v-model="ruleForm.isZheDie" placeholder="请选择是否可折叠" clearable class="append_unit"
              @change="onFoldingMethod()">
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="0"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否检测">
            <el-select v-model="ruleForm.isJianCe" placeholder="请选择" clearable class="append_unit">
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="颜色">
            <el-input v-model="ruleForm.colour" placeholder="请输入颜色" class="append_unit" maxlength="100" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6" v-if="foldingVerification">
          <el-form-item label="打包长(mm)" prop="measuredChang">
            <el-input-number v-model="ruleForm.measuredChang" :precision="2" :controls="false" :min="0"
              class="append_unit" data-unit="mm" :max="1000000">
            </el-input-number>
            <YhImgUpload v-if="logVisible" :value.sync="ruleForm.measuredChangImages" :limit="10" :ismultiple="true"
              ref="img2">
            </YhImgUpload>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-if="foldingVerification">
          <el-form-item label="打包宽(mm)" prop="measuredWidth">
            <el-input-number v-model="ruleForm.measuredWidth" :precision="2" :controls="false" class="append_unit"
              :min="0" data-unit="mm" :max="1000000">
            </el-input-number>
            <YhImgUpload v-if="logVisible" :value.sync="ruleForm.measuredWidthImages" :limit="10" :ismultiple="true"
              ref="img3">
            </YhImgUpload>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-if="foldingVerification">
          <el-form-item label="打包高(mm)" prop="measuredHeight">
            <el-input-number v-model="ruleForm.measuredHeight" :precision="2" :controls="false" :min="0"
              class="append_unit" data-unit="mm" :max="1000000">
            </el-input-number>
            <YhImgUpload v-if="logVisible" :value.sync="ruleForm.measuredHeightImages" :limit="10" :ismultiple="true"
              ref="img4">
            </YhImgUpload>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="材质">
            <el-input v-model="ruleForm.material" placeholder="材质" class="append_unit" maxlength="100" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="展开长(mm)" prop="zhankaiMeasuredChang">
            <el-input-number v-model="ruleForm.zhankaiMeasuredChang" :precision="2" :controls="false" :min="0"
              class="append_unit" data-unit="mm" :max="1000000">
            </el-input-number>
            <YhImgUpload v-if="logVisible" :value.sync="ruleForm.zhankaiMeasuredChangImages" :limit="10"
              :ismultiple="true" ref="img2">
            </YhImgUpload>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="展开宽(mm)" prop="zhankaiMeasuredWidth">
            <el-input-number v-model="ruleForm.zhankaiMeasuredWidth" :precision="2" :controls="false"
              class="append_unit" :min="0" data-unit="mm" :max="1000000">
            </el-input-number>
            <YhImgUpload v-if="logVisible" :value.sync="ruleForm.zhankaiMeasuredWidthImages" :limit="10"
              :ismultiple="true" ref="img3">
            </YhImgUpload>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="展开高(mm)" prop="zhankaiMeasuredHeight">
            <el-input-number v-model="ruleForm.zhankaiMeasuredHeight" :precision="2" :controls="false" :min="0"
              class="append_unit" data-unit="mm" :max="1000000">
            </el-input-number>
            <YhImgUpload v-if="logVisible" :value.sync="ruleForm.zhankaiMeasuredHeightImages" :limit="10"
              :ismultiple="true" ref="img4">
            </YhImgUpload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="测量重(g)" prop="measuredWeight">
            <el-input-number v-model="ruleForm.measuredWeight" :precision="2" :controls="false" :min="0"
              class="append_unit" data-unit="g" :max="1000000">
            </el-input-number>
            <YhImgUpload v-if="logVisible" :value.sync="ruleForm.measuredWeightImages" :limit="10" :ismultiple="true"
              ref="img1">
            </YhImgUpload>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="测量厚(mm)" prop="measuringThickness">
            <el-input-number v-model="ruleForm.measuringThickness" :precision="2" :controls="false" class="append_unit"
              :min="0" data-unit="mm" :max="1000000">
            </el-input-number>
            <YhImgUpload v-if="logVisible" :value.sync="ruleForm.measuringThicknessImages" :limit="10"
              :ismultiple="true" ref="img5">
            </YhImgUpload>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="箱规长(mm)">
            <el-input-number v-model="ruleForm.cartonLength" :precision="2" :controls="false" class="append_unit"
              :min="0" data-unit="mm" :max="1000000">
            </el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="箱规宽(mm)">
            <el-input-number v-model="ruleForm.cartonGaugeWidth" :precision="2" :controls="false" class="append_unit"
              :min="0" data-unit="mm" :max="1000000">
            </el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="箱规高(mm)">
            <el-input-number v-model="ruleForm.boxGaugeHeight" :precision="2" :controls="false" class="append_unit"
              :min="0" data-unit="mm" :max="1000000">
            </el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="外包装" prop="outerPackingPicture">
            <YhImgUpload3 v-if="logVisible" :value.sync="ruleForm.outerPackingPicture" :isImg="false"
              accept=".jpg,.jpeg,.png,.gif" :limit="3" :ismultiple="true"></YhImgUpload3>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="实物图" prop="physicalPicture">
            <YhImgUpload v-if="logVisible" :value.sync="ruleForm.physicalPicture" :limit="3" :ismultiple="true"
              ref="physicalPicture">
            </YhImgUpload>
          </el-form-item>
        </el-col>
        <el-col :span="6" style="height: 85px;">
          <el-form-item label="商品视频" prop="goodsVido">
            <viodeUpload v-if="logVisible" :minisize="false" ref="uploadexl" :limit="1" accepttyes=".mp4"
              :uploadprogress="true" @uploadFinish="uploadFinish" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div style="display: flex; justify-content: center; margin-top: 10px;">
      <el-button @click="closingMethod">取消</el-button>
      <el-button type="primary" @click="storageMethod">保存</el-button>
    </div>
  </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
import YhImgUpload3 from "@/components/upload/yh-img-upload3.vue";
import viodeUpload from "@/views/media/shooting/uploadfile1.vue";
import { addOrEditSampleRegistration, getWarehouseLocationManagement } from '@/api/inventory/sampleGoods';
import { getGoodsDocCgProvidersDto, pageGoodsDocRecordCgList } from "@/api/inventory/basicgoods"
import dayjs from 'dayjs'
import JsBarcode from 'jsbarcode'
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '审批状态', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNoInner', label: '商品编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'importTime', label: '商品名称', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'planSendTime', label: '原价', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'timePay', label: '现价', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight', label: '进货数量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderStatus', label: '涨价原因', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'seriesName', label: '添加日期', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCount', label: '涨价日期', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '添加人', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '岗位', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '分公司', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人组长', },
]
export default {
  name: "registrationPopUp",
  components: {
    MyContainer, vxetablebase, uploadimgFile, YhImgUpload, YhImgUpload3, viodeUpload
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    interactiveData: {
      type: Object,
      default: () => ({})
    },
    stashList: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      foldingVerification: true,
      searchloading: false,
      locationList: [],
      companyCodeList: [],
      companyCodeloading: false,
      rules: {
        goodsName: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
        sampleSource: [{ required: true, message: '请选择样品来源', trigger: 'blur' }],
        goodsCode: [{ required: true, message: '请输入商品编码', trigger: 'blur' }],
        yhGoodsCode: [{ required: true, message: '请输入公司商品编码', trigger: 'blur' }],
        warehouse: [{ required: true, message: '请选择仓库', trigger: 'blur' }],
        images: [{ required: true, message: '请上传商品图片', trigger: 'change' }],
        isChao40CM: [{ required: true, message: '请选择折叠单边是否超40CM', trigger: 'change' }],
        isZheDie: [{ required: true, message: '请选择是否可折叠', trigger: 'change' }],
      },
      subLoad: null,
      accepttyes: ['.jpg', '.jpeg', '.png', '.gif'],
      lastData: {
        splitPins: []
      },
      logVisible: false,
      logVisible1: false,
      ruleForm: {
        sampleSource: '',
        goodsCode: '',
        goodsName: '',
        goodsNumber: '',
        yhGoodsCode: '',
        opponentName: '',
        opponentShopName: '',
        opponentLink: '',
        manufacturerName: '',
        manufacturerAddress: '',
        manufacturerLink: '',
        warehouse: '',
        bitNumber: '',
        expressNo: '',
        remarks: '',
        images: [],
        sampleCode: '',
        measuredChang: '',
        measuredWidth: '',
        measuredHeight: '',
        zhankaiMeasuredChang: '',
        zhankaiMeasuredWidth: '',
        zhankaiMeasuredHeight: '',
        material: '',
        measuredWeight: '',
        measuringThickness: '',
        cartonLength: '',
        cartonGaugeWidth: '',
        boxGaugeHeight: '',
        outerPackingPicture: [],
        physicalPicture: [],
        goodsVido: [],
        measuredWidthImages: [],
        measuredHeightImages: [],
        measuredChangImages: [],
        measuredWeightImages: [],
        measuringThicknessImages: [],
        zhankaiMeasuredWidthImages: [],
        zhankaiMeasuredHeightImages: [],
        zhankaiMeasuredChangImages: [],
        colour: null,
        isJianCe: null,
        styleCode: null,
        ckGoodsCode: null,
        buyNo: null,
        numberShots: null,
        shootingTime: null,
      },
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loadingList: false,
      pickerOptions,
    }
  },
  async mounted() {
    this.logVisible = true
    this.$nextTick(() => {
      this.ruleForm = {
        sampleSource: '',
        goodsCode: '',
        goodsName: '',
        goodsNumber: '',
        yhGoodsCode: '',
        opponentName: '',
        opponentShopName: '',
        opponentLink: '',
        manufacturerName: '',
        manufacturerAddress: '',
        manufacturerLink: '',
        warehouse: '',
        bitNumber: '',
        expressNo: '',
        remarks: '',
        images: [],
        sampleCode: '',
        measuredChang: '',
        measuredWidth: '',
        measuredHeight: '',
        zhankaiMeasuredChang: '',
        zhankaiMeasuredWidth: '',
        zhankaiMeasuredHeight: '',
        material: '',
        measuredWeight: '',
        measuringThickness: '',
        cartonLength: '',
        cartonGaugeWidth: '',
        boxGaugeHeight: '',
        outerPackingPicture: [],
        physicalPicture: [],
        colour: null,
        isJianCe: null,
        goodsVido: []
      }
      this.$refs.ruleForm.clearValidate()
      this.$refs.ruleForm.resetFields();
      this.ruleForm = { ... this.interactiveData }
      if (this.ruleForm.goodsNumber && this.ruleForm.goodsNumber !== undefined) {
        this.generateBarcode()
      }
      this.onFoldingMethod()
      if (this.interactiveData.goodsVido) {
        let vi = this.interactiveData.goodsVido.split(',').map(item => ({
          url: item,
          fileName: item,
          relativePath: null,
          domain: null
        }))
        this.ruleForm.goodsVido = vi;
      } else {
        this.ruleForm.goodsVido = []
      }
      this.$refs.uploadexl.setData(this.ruleForm.goodsVido);
      ['outerPackingPicture', 'physicalPicture', 'measuredWeightImages', 'measuredWidthImages', 'measuredHeightImages', 'measuredChangImages', 'measuringThicknessImages', 'zhankaiMeasuredChangImages', 'zhankaiMeasuredWidthImages', 'zhankaiMeasuredHeightImages'].forEach(key => {
        let value = this.interactiveData[key]; // 直接获取值
        try {
          if (typeof value === 'string') {
            value = JSON.parse(value); // 解析 JSON 字符串
          }
        } catch (error) {
          // console.error(`JSON 解析失败: ${key}`, error);
          value = []; // 解析失败时，赋值为空数组
        }
        if (Array.isArray(value)) {
          this.ruleForm[key] = JSON.stringify(value.map(item => ({
            url: item.url,
            name: item.url
          })));
        } else {
          this.ruleForm[key] = '[]'; // 确保格式一致
        }
      });
    })
    this.logVisible1 = false
    this.$nextTick(() => {
      if (this.interactiveData.images) {
        try {
          const imageArray = JSON.parse(this.interactiveData.images); // 解析 JSON 字符串
          if (Array.isArray(imageArray)) {
            this.ruleForm.images = imageArray.map((item, i) => ({
              url: item.url,
              name: `商品图片${i + 1}`
            }));
          } else {
            this.ruleForm.images = [];
          }
        } catch (error) {
          console.error("图片数据解析错误:", error);
          this.ruleForm.images = [];
        }
      } else {
        this.ruleForm.images = [];
      }
      this.logVisible1 = true;
    });
  },
  methods: {
    onYhGoodsMothod(e) {
      if (e) {
        let index = this.companyCodeList.findIndex(item => item.goodsCode == e)
        if (index !== -1) {
          this.ruleForm.goodsCode = this.companyCodeList[index].goodsCode
          this.ruleForm.goodsName = this.companyCodeList[index].goodsName
        }
      }
    },
    onFoldingMethod() {
      if (this.ruleForm.isZheDie == 1 && this.ruleForm.isChao40CM == 0) {
        this.foldingVerification = false;
        // this.ruleForm.measuredHeight = null;
        // this.ruleForm.measuredWidth = null;
        // this.ruleForm.measuredChang = null;
        // this.ruleForm.measuredHeightImages = [];
        // this.ruleForm.measuredWidthImages = [];
        // this.ruleForm.measuredChangImages = [];
      } else {
        this.foldingVerification = true;
      }
    },
    changeWarehouse(val) {
      if (this.locationList && this.locationList.length > 0) {
        const filteredList = this.locationList.filter(item => item.warehouse === val);
        this.$set(this, 'locationList', filteredList);
      }
    },
    async remoteCompanyCodeMethod(query) {
      if (query !== '') {
        this.companyCodeloading = true
        const res = await pageGoodsDocRecordCgList({ currentPage: 1, pageSize: 20, goodsCode: query + '*' })
        this.companyCodeloading = false
        let a = res?.data?.list
        this.companyCodeList = a.slice(0, 20)
      }
      else {
        this.companyCodeList = []
      }
    },
    async remoteMethod(query) {
      if (query !== '') {
        this.searchloading == true
        const res = await getWarehouseLocationManagement({ currentPage: 1, pageSize: 50, warehouseBitCode: query })
        this.searchloading = false
        let a = res?.data?.list.filter(item =>
          !this.ruleForm.warehouse || item.warehouse == this.ruleForm.warehouse
        )
        this.$set(this, 'locationList', a)
      }
      else {
        this.$set(this, 'locationList', [])
      }
    },
    // 计算尺寸的方法
    calculateSize(size, ratio = 37.795, addition = 0) {
      const baseSize = (size * ratio).toFixed(0)
      return (Number(baseSize) + addition) + 'px'
    },
    extractUrls(data) {
      try {
        if (!data) return '';
        const arr = Array.isArray(data)
          ? data
          : typeof data === 'string'
            ? JSON.parse(data)
            : [];
        return Array.isArray(arr) ? arr.map(item => item.url).join(',') : '';
      } catch (e) {
        return '';
      }
    },

    async storageMethod() {
      let videoRes = this.$refs.uploadexl.getReturns();
      if (videoRes.data?.length > 0) {
        this.ruleForm.goodsVido = videoRes.data.map(item => item.url).join(',');
      } else {
        this.ruleForm.goodsVido = '';
      }
      if (this.ruleForm.sampleSource == '公司样品' && !this.ruleForm.yhGoodsCode) {
        this.$message.error('请输入公司商品编码')
        return
      }
      //表单校验
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          if (this.ruleForm.images.length == 0 || !this.ruleForm.images) {
            this.$message.error('请上传商品图片')
            return
          }
          const params = {
            ...this.ruleForm,
            images: this.extractUrls(this.ruleForm.images),
            outerPackingPicture: this.extractUrls(this.ruleForm.outerPackingPicture),
            physicalPicture: this.extractUrls(this.ruleForm.physicalPicture),
            measuredWeightImages: this.extractUrls(this.ruleForm.measuredWeightImages),
            measuredWidthImages: this.extractUrls(this.ruleForm.measuredWidthImages),
            measuredHeightImages: this.extractUrls(this.ruleForm.measuredHeightImages),
            measuredChangImages: this.extractUrls(this.ruleForm.measuredChangImages),
            measuringThicknessImages: this.extractUrls(this.ruleForm.measuringThicknessImages),
            zhankaiMeasuredChangImages: this.extractUrls(this.ruleForm.zhankaiMeasuredChangImages),
            zhankaiMeasuredWidthImages: this.extractUrls(this.ruleForm.zhankaiMeasuredWidthImages),
            zhankaiMeasuredHeightImages: this.extractUrls(this.ruleForm.zhankaiMeasuredHeightImages),
          };
          this.loadingList = true;
          const { data, success } = await addOrEditSampleRegistration(params)
          this.loadingList = false;
          if (success) {
            if (data.success) {
              this.$message.success('保存成功')
              this.$emit('closingMethodClose')
            } else {
              this.$message.error(data.msg ? data.msg : '保存失败')
            }
          } else {
            this.$message.error(data.msg ? data.msg : '保存失败')
          }
        }
      })
    },
    closingMethod() {
      this.$emit('close')
      this.logVisible = false
    },
    uploadFinish(data) {
      this.subLoad = data
    },
    generateBarcode() {
      if (!this.ruleForm.goodsNumber) {
        console.error("条码数据为空")
        return
      }

      // 生成条形码的元素ID
      const barcodeId = `barcode1${this.ruleForm.goodsNumber}`

      // 确保DOM渲染完成后再生成条形码
      this.$nextTick(() => {
        const barcodeElement = document.getElementById(barcodeId)
        if (barcodeElement) {
          barcodeElement.style.width = "150px"
          barcodeElement.style.height = "100px"

          JsBarcode(`#${barcodeId}`, this.ruleForm.goodsNumber, {
            displayValue: false,
            width: 2, // 控制条形码线条宽度
            height: 50, // 控制条形码总高度
            onComplete: () => {
              this.$nextTick(() => {
                const printElement = document.getElementById('print')
                if (printElement) {
                  printElement.style.display = 'flex'
                  setTimeout(() => {
                    printJS({
                      printable: printElement.innerHTML,
                      type: 'html',
                      targetStyles: ['*'],
                      documentTitle: '样品条码',
                      style: `
                    #print {
                      display: flex;
                      flex-wrap: wrap;
                      justify-content: center;
                      align-items: center;
                      width: 200px;
                      height: 115px;
                    }
                    .print_item {
                      page-break-inside: avoid;
                    }
                    img {
                      width: 150px;
                      height: 100px;
                    }
                  `
                    })
                  }, 500)
                }
              })
            }
          })
        } else {
          console.error(`未找到条形码元素: ${barcodeId}`)
        }
      })
    },
    beforeUpload(data, type) {
      if (data.length > 0) {
        const target = type == 'images' ? 'images' : type == 'zhuanliImage' ? 'zhuanliImage' : 'image';
        this.ruleForm[target] = this.ruleForm[target].concat(
          data.map(item => ({
            url: item.url,
            name: item.name
          }))
        );
      }
    },
    getImg(data, type) {
      if (data) {
        const target = type == 'images' ? 'images' : type == 'zhuanliImage' ? 'zhuanliImage' : 'image';
        this.ruleForm[target] = data.map(item => ({
          url: item.url,
          name: item.fileName
        }));
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

.formCss {
  width: 100%;
}

#print {
  // line-height: 30px;
  // padding: 20px;
  // box-sizing: border-box;
  text-align: center;
  width: 200px;
  height: 115px;
  display: flex;
  flex-wrap: wrap;
  page-break-inside: avoid;
  justify-content: center;
  align-items: center;

  // scale: 2;
  //防止跨页
  .print_item {
    page-break-inside: avoid;
  }

  img {
    width: 100%;
    // height: 20px;
    height: 100%;
  }
}





.append_unit {
  width: 100%;
}



::v-deep .el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 7px
}
</style>
