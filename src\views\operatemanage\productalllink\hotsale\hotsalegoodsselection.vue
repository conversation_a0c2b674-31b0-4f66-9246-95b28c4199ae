<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <div style="height: 40px; margin-top: 5px;">
        <span style="margin-right:0.4%;">
          <el-input style="width:100px;" v-model.trim="filter.seatNo" :maxlength='50' placeholder="库位号"
            @keyup.enter.native="onSearch" clearable />
        </span>

        <span style="margin-right:0.4%;">
          <el-input style="width:130px;" v-model.trim="filter.contrastPrincipalName" :maxlength='50' placeholder="比样平台负责人"
            @keyup.enter.native="onSearch" clearable />
        </span>

        <span style="margin-right:0.4%;">
          <el-input style="width:100px;" v-model.trim="filter.contrastName" :maxlength='50' placeholder="比样人"
            @keyup.enter.native="onSearch" clearable />
        </span>

        <span style="margin-right:0.4%;">
          <el-input style="width:100px;" v-model.trim="filter.productCode" :maxlength='50' placeholder="产品编码"
            @keyup.enter.native="onSearch" clearable />
        </span>

        <span style="margin-right:0.4%;">
          <el-input style="width:130px;" v-model.trim="filter.ownExpress" :maxlength='50' placeholder="自仓快递单号"
            @keyup.enter.native="onSearch" clearable />
        </span>

        <span style="margin-right:0.4%;">
          <el-input style="width:140px;" v-model.trim="filter.rivalExpress" :maxlength='50' placeholder="对手采样快递单号"
            @keyup.enter.native="onSearch" clearable />
        </span>

        <span style="margin-right:0.4%;">
          <el-select filterable v-model.trim="filter.contrastResult" collapse-tags clearable placeholder="比样结果"
            maxlength="50" style="width: 150px">
            <el-option label="基本一致（略有差异）" value="基本一致（略有差异）"></el-option>
            <el-option label="不一致" value="不一致"></el-option>
          </el-select>
        </span>

        <span style="margin-right:0.4%;">
          <el-select filterable v-model.trim="filter.contrastProcessing" collapse-tags clearable placeholder="比样进度"
            maxlength="50" style="width: 100px">
            <el-option label="待比样" value="待比样"></el-option>
            <el-option label="比样中" value="比样中"></el-option>
            <el-option label="比样完成" value="比样完成"></el-option>
          </el-select>
        </span>

        <span style="margin-left:5px;">
          <el-button type="primary" @click="onSearch" style="width: 70px;">查询</el-button>
        </span>

        <span style="margin-left:5px;">
          <el-button type="primary" @click="onAddselect" style="width: 90px;">新增比样</el-button>
        </span>
      </div>
    </template>

    <ces-table :id="'hotsalegoodsselection202408041711'" ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
      :summaryarry='summaryarry' :showsummary='true' :tableData='list' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" :loading="listLoading" style="width:100%;height:680px;margin: 0">
    </ces-table>
    <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />

    <el-dialog title="新增比样" :visible.sync="newsampledialog" width="55%" v-dialogDrag>
      <div>
        <span>
          <el-button type="text" style="color: 409EFF;" @click="newline">新增一行</el-button>
        </span>
      </div>
      <vxe-table border resizable show-overflow :data="contrastSampleList"
        :edit-config="{ mode: 'cell', showIcon: false }" :edit-rules="validRules">
        <vxe-column field="seatNo" title="库位号" :edit-render="{}" width="140">
          <template #default="{ row }">
            <vxe-input v-model="row.seatNo" type="text" placeholder="请输入库位号" maxlength="50"></vxe-input>
          </template>
        </vxe-column>
        <vxe-column field="contrastPrincipalDUserId" title="比样平台负责人" :edit-render="{}" width="140">
          <template #default="{ row }">
            <YhUserelector :value.sync="row.contrastPrincipalDUserId" maxlength="50"
              :text.sync="row.contrastPrincipalName" style="width:80%;" placeholder="请输入比样平台负责人">
            </YhUserelector>
          </template>
        </vxe-column>
        <vxe-column field="contrastDUserId" title="比样人" :edit-render="{}" width="115">
          <template #default="{ row }">
            <YhUserelector :value.sync="row.contrastDUserId" :text.sync="row.contrastName" maxlength="50"
              style="width:80%;" placeholder="请输入比样人">
            </YhUserelector>
          </template>
        </vxe-column>
        <vxe-column field="productCode" title="产品编码" :edit-render="{}" width="120">
          <template #default="{ row }">
            <vxe-input v-model="row.productCode" type="text" placeholder="请输入产品编码" maxlength="50"></vxe-input>
          </template>
        </vxe-column>
        <vxe-column field="ownExpress" title="自仓快递单号" :edit-render="{}" width="125">
          <template #default="{ row }">
            <vxe-input v-model="row.ownExpress" type="text" placeholder="请输入自仓快递单号" maxlength="50"></vxe-input>
          </template>
        </vxe-column>
        <vxe-column field="rivalExpress" title="对手采样快递单号" :edit-render="{}" width="150">
          <template #default="{ row }">
            <vxe-input v-model="row.rivalExpress" type="text" placeholder="请输入对手采样快递单号" maxlength="50"></vxe-input>
          </template>
        </vxe-column>
        <vxe-column field="consigneeDUserId" title="收货人" :edit-render="{}">
          <template #default="{ row }">
            <YhUserelector :value.sync="row.consigneeDUserId" :text.sync="row.consigneeName" maxlength="50"
              style="width:80%;" placeholder="请输入收货人">
            </YhUserelector>
          </template>
        </vxe-column>
        <vxe-column field="button" title="操作" :edit-render="{}">
          <template #default="{ row }">
            <el-button type="danger" @click="deletecomparison(row)">删除</el-button>
          </template>
        </vxe-column>
      </vxe-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="newsampledialog = false">关闭</el-button>
        <el-button @click="addedcomparisonsamplesaving" type="primary" v-loading="btnloading"
          v-throttle="2000">保存</el-button>
      </span>
    </el-dialog>

    <el-dialog title="新增比样数据" :visible.sync="addedcomparisondata" width="50%" v-dialogDrag>
      <div style="box-sizing: border-box;padding:10px 50px;" v-if="addedcomparisondata">
        <div style="width:100%;">
          <div class="jxkhcj">
            <el-form style="width:100%" :model="addForm" :rules="rules" ref="addForm" label-width="12px"
              class="demo-ruleForm">
              <div style="display: flex;width:100%;">
                <div class="lxwz">库位号</div>
                <div style="width:100%;">
                  <el-form-item label=" " prop="seatNo">
                    <span><el-input v-model="addForm.seatNo" style="width:80%;" size="mini" maxlength="50"
                        placeholder="请输入库位号"></el-input></span>
                  </el-form-item>
                </div>
              </div>

              <div style="display: flex;width:100%;">
                <div class="lxwz">比样平台负责人</div>
                <div style="width:100%;">
                  <el-form-item label=" " prop="contrastPrincipalName">
                    <span>
                      <YhUserelector :value.sync="addForm.contrastPrincipalDUserId" maxlength="50"
                        :text.sync="addForm.contrastPrincipalName" style="width:80%;" placeholder="请输入比样平台负责人">
                      </YhUserelector>
                    </span>
                  </el-form-item>
                </div>
              </div>

              <div style="display: flex;width:100%;">
                <div class="lxwz">比样人</div>
                <div style="width:100%;">
                  <el-form-item label=" " prop="contrastName">
                    <YhUserelector :value.sync="addForm.contrastDUserId" :text.sync="addForm.contrastName" maxlength="50"
                      style="width:80%;" placeholder="请输入比样人">
                    </YhUserelector>
                  </el-form-item>
                </div>
              </div>

              <div style="display: flex;width:100%;">
                <div class="lxwz">产品编码</div>
                <div style="width:100%;">
                  <el-form-item label=" " prop="productCode">
                    <span><el-input v-model="addForm.productCode" style="width:80%;" size="mini" maxlength="50"
                        placeholder="请输入产品编码"></el-input></span>
                  </el-form-item>
                </div>
              </div>

              <div style="display: flex;width:100%;">
                <div class="lxwz">自仓快递单号</div>
                <div style="width:100%;">
                  <el-form-item label=" " prop="ownExpress">
                    <span><el-input v-model="addForm.ownExpress" style="width:80%;" size="mini" maxlength="50"
                        placeholder="请输入自仓快递单号"></el-input></span>
                  </el-form-item>
                </div>
              </div>

              <div style="display: flex;width:100%;">
                <div class="lxwz">对手采样快递单号</div>
                <div style="width:100%;">
                  <el-form-item label=" " prop="rivalExpress">
                    <span><el-input v-model="addForm.rivalExpress" style="width:80%;" size="mini" maxlength="50"
                        placeholder="请输入对手采样快递单号"></el-input></span>
                  </el-form-item>
                </div>
              </div>

              <div style="display: flex;width:100%;">
                <div class="lxwz">收货人</div>
                <div style="width:100%;">
                  <el-form-item label=" " prop="consigneeName">
                    <YhUserelector :value.sync="addForm.consigneeDUserId" :text.sync="addForm.consigneeName"
                      maxlength="50" style="width:80%;" placeholder="请输入收货人">
                    </YhUserelector>
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addedcomparisondata = false">关闭</el-button>
        <el-button @click="submitthesampledata('addForm')" type="primary">提交</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="comparesampleeditingoperation" v-if="comparesampleeditingoperation" width="45%" v-dialogDrag
      @close="editactionoff" :loading="listLoading" style="margin-top: -10vh;">
      <el-form :model="ruleForm" ref="ruleForm" label-width="100px" style="height: 100%;overflow-y: auto;">
        <div class="bzjzcjrw">
          <div class="bt" style="display: flex; align-items: center;">
            <span style="flex: 1;">编辑/操作</span>
            <div style="display: flex; flex: 1; justify-content: flex-end;">
              <el-button type="primary" @click="completecomparison"
                :disabled="disableall ? true : (!checkPermission('contrastsample:complete') && !editdisable)"
                v-loading="btnCompleteLoading">完成</el-button>
              <el-button type="primary" @click="editsaveoperation" :disabled="disableall"
                v-loading="btnSaveLoading">保存</el-button>
              <el-button @click="comparesampleeditingoperation = false">关闭</el-button>
            </div>
          </div>

          <div class="scrollable-div">
            <div class="sampleborder">
              <div class="bzccjlx">
                <div class="lxwz">运营采样信息编辑</div>
                <div class="lxwz2">
                  <span style="color: red;">仅有三次编辑机会,请谨慎使用</span>
                </div>
              </div>

              <div class="bzccjlx">
                <div class="lxwz">库位号:</div>
                <div class="lxwz2">
                  <span style="color: #F56C6C; margin: 0 7px 0 -13px">*</span>
                  <span><el-input v-model="ruleForm.contrastSample.seatNo" style="width:80%;" size="mini" maxlength="50"
                      :disabled="!editdisable || disableall" placeholder="请输入库位号"></el-input></span>
                </div>
              </div>

              <div class="bzccjlx">
                <div class="lxwz">比样平台负责人:</div>
                <div class="lxwz2">
                  <span style="color: #F56C6C; margin: 0 7px 0 -13px">*</span>
                  <span>
                    <el-input v-if="!editdisable || disableall" :disabled="!editdisable || disableall"
                      v-model="ruleForm.contrastSample.contrastPrincipalName" style="width:80%;" size="mini"
                      maxlength="50" placeholder="请输入比样平台负责人"></el-input>
                    <YhUserelector v-else :value.sync="ruleForm.contrastSample.contrastPrincipalDUserId" maxlength="50"
                      :disabled="!editdisable || disableall" :text.sync="ruleForm.contrastSample.contrastPrincipalName"
                      style="width:80%;" placeholder="请输入比样平台负责人">
                    </YhUserelector>
                  </span>
                </div>
              </div>

              <div class="bzccjlx">
                <div class="lxwz">比样人:</div>
                <div class="lxwz2">
                  <span style="color: #F56C6C; margin: 0 7px 0 -13px">*</span>
                  <span>
                    <el-input v-if="!editdisable || disableall" :disabled="!editdisable || disableall"
                      v-model="ruleForm.contrastSample.contrastName" style="width:80%;" size="mini" maxlength="50"
                      placeholder="请输入比样人"></el-input>
                    <YhUserelector v-else :value.sync="ruleForm.contrastSample.contrastDUserId" :disabled="editdisable"
                      maxlength="50" :text.sync="ruleForm.contrastSample.contrastName" style="width:80%;"
                      placeholder="请输入比样人">
                    </YhUserelector>
                  </span>
                </div>
              </div>

              <div class="bzccjlx">
                <div class="lxwz">产品编码:</div>
                <div class="lxwz2">
                  <span style="color: #F56C6C; margin: 0 7px 0 -13px">*</span>
                  <span><el-input v-model="ruleForm.contrastSample.productCode" style="width:80%;" size="mini"
                      :disabled="!editdisable || disableall" maxlength="50" placeholder="请输入产品编码"></el-input></span>
                </div>
              </div>

              <div class="bzccjlx">
                <div class="lxwz">自仓快递单号:</div>
                <div class="lxwz2">
                  <span><el-input v-model="ruleForm.contrastSample.ownExpress" style="width:80%;" size="mini"
                      :disabled="!editdisable || disableall" maxlength="50" placeholder="请输入自仓快递单号"></el-input></span>
                </div>
              </div>

              <div class="bzccjlx">
                <div class="lxwz">对手采样快递单号:</div>
                <div class="lxwz2">
                  <span><el-input v-model="ruleForm.contrastSample.rivalExpress" style="width:80%;" size="mini"
                      :disabled="!editdisable || disableall" maxlength="50" placeholder="请输入对手采样快递单号"></el-input></span>
                </div>
              </div>

              <div class="bzccjlx">
                <div class="lxwz">收货人:</div>
                <div class="lxwz2">
                  <span style="color: #F56C6C; margin: 0 7px 0 -13px">*</span>
                  <span>
                    <el-input v-if="!editdisable || disableall" disabled v-model="ruleForm.contrastSample.consigneeName"
                      style="width:80%;" size="mini" maxlength="50" placeholder="请输入收货人"></el-input>
                    <YhUserelector v-else :value.sync="ruleForm.contrastSample.consigneeDUserId" disabled maxlength="50"
                      :text.sync="ruleForm.contrastSample.consigneeName" style="width:80%;" placeholder="请输入收货人">
                    </YhUserelector>
                  </span>
                </div>
              </div>
            </div>

            <div class="sampleborder">
              <div class="sample">
                <span style="float: left">对样详细参数</span>
                <div style="float: right; padding: 3px 0; display: flex; flex-direction: row;">
                  <el-button type="primary" @click="addnew" :disabled="!editdisable || disableall">新增一行</el-button>
                </div>
              </div>

              <div style="margin: 15px;">
                <vxe-table border resizable show-overflow :data="ruleForm.contrastSampleSetDetailList"
                  :edit-config="{ mode: 'cell', showIcon: false }" :tooltip-config="{ zIndex: 9999 }"
                  :disabled="!checkPermission('contrastsample:set') || disableall">
                  <vxe-table-column field="contrastCategoryName" title="对比类目" class="text-center"></vxe-table-column>

                  <vxe-table-column field="ownAttr" title="自家" width=110 :edit-render="{}"
                    :disabled="!checkPermission('contrastsample:set') || disableall">
                    <template #default="{ row }">
                      <!-- <vxe-input v-model="row.avatar" type="text" placeholder="请输入"></vxe-input> -->
                      <el-input v-show="!row.isShowProp" v-model="row.ownAttr" type="text" placeholder="请输入"
                        maxlength="50" :disabled="!checkPermission('contrastsample:set') || disableall"></el-input>
                      <el-button v-show="row.isShowProp" type="text" @click="ownpicturesvideos(row)">查看</el-button>
                    </template>
                  </vxe-table-column>

                  <vxe-table-column field="rivalAttr" title="对家" width=110 :edit-render="{}"
                    :disabled="!checkPermission('contrastsample:set') || disableall">
                    <template #default="{ row }">
                      <el-input v-show="!row.isShowProp" v-model="row.rivalAttr" type="text" placeholder="请输入"
                        maxlength="50" :disabled="!checkPermission('contrastsample:set') || disableall"></el-input>
                      <el-button v-show="row.isShowProp" type="text" @click="onpicturevideo(row)">查看</el-button>
                    </template>
                  </vxe-table-column>

                  <vxe-table-column field="contrastResultId" title="对比结果" class="text-center" width=384
                    :disabled="!checkPermission('contrastsample:set') || disableall">
                    <template #default="{ row }">
                      <el-radio-group v-model="row.contrastResultId" @change="comparisonresultchange($event, row)"
                        :disabled="!checkPermission('contrastsample:set') || disableall">
                        <el-radio :label="1">基本一致</el-radio>
                        <el-radio :label="2">自家</el-radio>
                        <el-radio :label="3">对家</el-radio>
                        <el-radio :label="4">忽略</el-radio>
                      </el-radio-group>
                    </template>
                  </vxe-table-column>
                </vxe-table>
              </div>
              <div style="margin: 15px 15px;">
                <span>对比补充说明</span>
              </div>
              <div style="margin: 15px;">
                <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" placeholder="对比补充说明" maxlength="500"
                  :disabled="!checkPermission('contrastsample:set') || disableall"
                  v-model="ruleForm.contrastSample.contrastReplenishDesc">
                </el-input>
              </div>
              <div style="margin: 15px 15px;">
                <span>比样结果</span>
              </div>
              <div style="margin: 0px 15px;">
                <!-- <el-radio-group v-model="ruleForm.contrastSampleSetDetailList.settingId"> -->
                <el-radio v-model="ruleForm.contrastSample.contrastResult" label="基本一致（略有差异）"
                  :disabled="!checkPermission('contrastsample:set') || disableall">基本一致（略有差异）</el-radio>
                <el-radio v-model="ruleForm.contrastSample.contrastResult" label="不一致"
                  :disabled="!checkPermission('contrastsample:set') || disableall">不一致</el-radio>
                <!-- </el-radio-group> -->
              </div>
            </div>

            <div class="sampleborder">
              <div style="margin: 15px 15px;">
                <span>操作</span>
              </div>
              <div style="margin: 0px 15px;">
                <el-button type="primary" @click="notificationoperation"
                  :disabled="!checkPermission('contrastsample:notify') || disableall">一键通知比样运营</el-button>
                <el-button type="primary" @click="notificationpurchase"
                  :disabled="!checkPermission('contrastsample:notify') || disableall">一键通知采购</el-button>
                <el-button type="primary" @click="customizenotifier"
                  :disabled="!checkPermission('contrastsample:notify') || disableall">自定义通知人</el-button>
                <el-button type="primary" @click="communicationfeedback" v-if="catefeedback"
                  :disabled="disableall">比样沟通反馈</el-button>
              </div>
            </div>

            <div class="sampleborder">
              <div style="margin: 15px 15px;">
                <el-tabs v-model="activeName" @tab-click="handleClick">
                  <el-tab-pane label="日志" name="first"></el-tab-pane>
                  <!-- <el-tab-pane label="沟通结果" name="second"></el-tab-pane> -->
                </el-tabs>
              </div>

              <div class="bzbjfgx" v-if="activeName === 'first'" style="min-height:160px">
                <!-- <div class="rzxgsj" style="margin-left: 20px;">操作日志</div> -->
                <div class="bzczrzx" v-for="(item, index ) in ruleForm.contrastSampleLogList " :key="index"
                  style="height:100px">
                  <div>
                    <!-- <div class="rztx">
                      <el-avatar :size="25" fit="cover" :src="item.avatar"></el-avatar>
                    </div> -->
                    <div class="rzmz">
                      <span>{{ item.name }}</span>
                      <span>{{ item.time }}</span>
                      <span> {{ item.changeinfo }}</span>
                    </div>
                    <!-- <div class="rzxgx">
                      <span> {{ item.changeinfo }}</span>
                    </div>
                    <el-tooltip class="item" effect="dark" :content="item.time" placement="top-start">
                      <div class="rzxgsj"> {{ item.time }}</div>
                    </el-tooltip> -->
                  </div>
                  <div>
                    <div class="rzbox">
                      <div class="rzxgq">修改前：</div>
                      <div class="rzxgnr">
                        <!-- <el-tooltip class="item" effect="dark" :content="item.before" placement="top-start"> -->
                        <!-- <span>{{ item.before }}</span> -->
                        <!-- </el-tooltip> -->
                        <el-tooltip v-if="!hasImgTag(item.before)" class="item" effect="dark"
                          :content="item.before.replace(/<p>/g, '').replace(/<\/p>/g, '')" placement="top-start">
                          <div v-html="item.before" />
                        </el-tooltip>
                        <div v-else v-html="item.before" />
                      </div>
                    </div>
                  </div>
                  <div>
                    <div class="rzbox">
                      <div class="rzxgq">修改后：</div>
                      <div class="rzxgnr">
                        <!-- <el-tooltip class="item" effect="dark" :content="item.after" placement="top-start">
                        <span>{{ item.after }}</span>
                        </el-tooltip> -->
                        <el-tooltip v-if="!hasImgTag(item.after)" class="item" effect="dark"
                          :content="item.after.replace(/<p>/g, '').replace(/<\/p>/g, '')" placement="top-start">
                          <div v-html="item.after" class="ellipsis-content"
                            style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; max-width: 650px;" />
                        </el-tooltip>
                        <div v-else v-html="item.after" class="ellipsis-content"
                          style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; max-width: 650px;" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="bzbjfgx" v-if="activeName === 'second'" style="min-height:160px">
                <!-- <div class="rzxgsj" style="margin-left: 20px;">沟通日志</div> -->
                <div class="bzczrzx" v-for="(item, index ) in ruleForm.contrastSampleSendLogList " :key="index">
                  <div>
                    <!-- <div class="rztx">
                      <el-avatar :size="25" fit="cover" :src="item.avatar"></el-avatar>
                    </div> -->
                    <div class="rzmz">
                      <span>{{ item.createdUserName }}</span>
                      <span>{{ item.createdTime }}</span>
                      <span>更新了沟通</span>
                    </div>
                    <!-- <div class="rzxgx">
                      <span> {{ item.changeinfo }}</span>
                    </div>
                    <el-tooltip class="item" effect="dark" :content="item.time" placement="top-start">
                      <div class="rzxgsj"> {{ item.time }}</div>
                    </el-tooltip> -->
                  </div>
                  <div>
                    <div class="rzbox">
                      <div class="rzxgq">重点内容描述：</div>
                      <div class="rzxgnr">
                        <el-tooltip class="item" effect="dark" :content="item.pointMsg" placement="top-start">
                          <div v-html="item.pointMsg" />
                        </el-tooltip>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div class="rzbox">
                      <div class="rzxgq">详细内容描述：</div>
                      <div class="rzxgnr">
                        <el-tooltip v-if="!hasImgTag(item.detailMsg)" class="item" effect="dark"
                          :content="item.detailMsg.replace(/<p>/g, '').replace(/<\/p>/g, '')" placement="top-start">
                          <div v-html="item.detailMsg" />
                        </el-tooltip>
                        <div v-else v-html="item.detailMsg" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
      </span>
    </el-dialog>

    <el-dialog title="提示" :visible.sync="dialogVisiblew" width="30%" append-to-body v-dialogDrag>
      <div class="bzccjlx">
        <!-- <div class="lxwz">自定义名称</div> -->
        <div class="lxwz2">
          <el-input controls-position="right" style="width:70%" :clearable="true" placeholder="请输入类目" maxlength="50"
            v-model="categorytitle" :maxlength="200"></el-input>
          <!-- <el-input v-model="categorytitle" style="width:80%;" size="mini" maxlength="50" placeholder="请输入收货人"></el-input> -->
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisiblew = false">取 消</el-button>
        <el-button type="primary" plain @click="onAddMarks">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="自定义通知人" :visible.sync="customizethenotifier" width="25%" v-dialogDrag @close="customihandleClose">
      <div style="margin: 15px 15px;">
        <span>人员选择：</span>
      </div>
      <div style="margin: 15px 15px;">
        <el-select v-model="notifier.customyyList" filterable collapse-tags clearable placeholder="运营专员" multiple
          style="width: 300px;">
          <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div style="margin: 15px 15px;">
        <el-select v-model="notifier.customcgList" filterable collapse-tags clearable placeholder="采购专员" multiple
          style="width: 300px;">
          <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div style="margin: 15px 15px;">
        <el-select v-model="notifier.customhjList" filterable collapse-tags clearable placeholder="核价专员" multiple
          style="width: 300px;">
          <el-option v-for="item in pricinglist" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </div>
      <div style="margin: 35px 0px 0px 15px;">
        <span>通知内容:</span>
      </div>
      <div style="margin: 15px;">
        <el-input type="textarea" style="width: 300px;" :autosize="{ minRows: 2, maxRows: 4 }" placeholder="请输入钉钉通知内容"
          maxlength="300" v-model="notifier.customMsg">
        </el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="customizethenotifier = false">关闭</el-button>
        <el-button type="primary" plain @click="notifierAddMarks">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="沟通反馈" :visible.sync="sampleCommunicationfeedback" width="25%" v-dialogDrag @close="handleClose">
      <div style="margin: 15px 15px;">
        <span style="color: #F56C6C; margin: 0 7px 0 -13px">*</span>
        <span>重点内容描述:</span>
        <span style="margin: 10px "> <el-input controls-position="right" style="width:70%" :clearable="true"
            maxlength="200" placeholder="请输入重点内容描述" v-model="feedback.pointMsg"></el-input></span>
      </div>
      <div style="margin: 25px 0px 0px 15px;">
        <span style="color: #F56C6C; margin: 0 7px 0 -13px">*</span>
        <span>详情内容描述:</span>
      </div>
      <div>
        <yh-quill-editor :value.sync="feedback.detailMsg" :maxCharacters="300"></yh-quill-editor>
        <!-- <div v-html="feedback.detailMsg" class="tempdivv"></div> -->
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="sampleCommunicationfeedback = false">关闭</el-button>
        <el-button type="primary" plain @click="feedbackpreservation">保存</el-button>
      </span>
    </el-dialog>


    <el-dialog title="查看图片" :visible.sync="ownonpicturesvideo" width="27%" v-dialogDrag>
      <div id="key1" key="key1">
        <span>自家</span>
        <yh-img-upload :value.sync="oneselfpictureownPropDetailList" ref="supplier_idd" :limit="6"
          keys="four"></yh-img-upload>
      </div>
      <div id="key2" key="key2">
        <span>对家</span>
        <yh-img-upload :value.sync="homepictureownPropDetailList" ref="supplier_idd2" :limit="6"
          keys="five"></yh-img-upload>
      </div>
      <!-- <uploadfile :minisize="false" ref="uploadimg" :islook="islook" :uploadInfo="homepictureownPropDetailList"
        :limit="10000" :accepttyes="'.image/jpg,image/jpeg,image/png'" :delfunction="deluplogimg" /> -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="ownonpicturesvideo = false">关闭</el-button>
        <el-button @click="submitsave" :disabled="!checkPermission('contrastsample:set') || disableall">保存</el-button>
      </span>
    </el-dialog>

    <el-dialog title="查看视频" :visible.sync="familyonpicturesvideo" width="27%" v-dialogDrag>
      <div>
        <span>自家</span>
        <uploadvideo :retdatafa.sync="oneselfretdatafa" ref="uploadvideo" :limit="6" style="height:200px"></uploadvideo>
      </div>
      <div>
        <span>对家</span>
        <uploadvideo :retdatafa.sync="familretdatafa" ref="uploadvideoe" :limit="6" style="height:200px"></uploadvideo>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="familyonpicturesvideo = false">关闭</el-button>
        <el-button @click="familsubmitsave"
          :disabled="!checkPermission('contrastsample:set') || disableall">保存</el-button>
      </span>
    </el-dialog>


    <el-dialog title="沟通结果查看" :visible.sync="communicationresultview" width="45%" v-dialogDrag>
      <div class="bzbjfgx" style="min-height:160px;">
        <div style="max-height: 300px; overflow: auto;">
          <div class="bzczrzx" v-for="(item, index ) in sultviewlist " :key="index">
            <div>
              <!-- <div class="rztx">
              <el-avatar :size="25" fit="cover" :src="item.avatar"></el-avatar>
            </div> -->
              <div class="rzmz">
                <span>{{ item.createdUserName }}</span>
                <span>{{ item.createdTime }}</span>
                <span>更新了沟通</span>
              </div>
              <!-- <div class="rzxgx">
                      <span> {{ item.changeinfo }}</span>
                    </div>
                    <el-tooltip class="item" effect="dark" :content="item.time" placement="top-start">
                      <div class="rzxgsj"> {{ item.time }}</div>
                    </el-tooltip> -->
            </div>
            <div>
              <div class="rzbox">
                <div class="rzxgq">重点内容描述：</div>
                <div class="rzxgnr">
                  <el-tooltip class="item" effect="dark" :content="item.pointMsg" placement="top-start">
                    <div v-html="item.pointMsg" />
                  </el-tooltip>
                </div>
              </div>
            </div>
            <div>
              <div class="rzbox">
                <div class="rzxgq">详细内容描述：</div>
                <div class="rzxgnr">
                  <el-tooltip v-if="!hasImgTag(item.detailMsg)" class="item" effect="dark"
                    :content="item.detailMsg.replace(/<p>/g, '').replace(/<\/p>/g, '')" placement="top-start">
                    <div v-html="item.detailMsg" />
                  </el-tooltip>
                  <div v-else v-html="item.detailMsg" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="communicationresultview = false">关闭</el-button>
      </span>
    </el-dialog>

  </my-container>
</template>

<script>
import { VXETable } from 'vxe-table';
import uploadfile from '@/views/media/shooting/uploadfile';
// import YhImgUpload from "@/views/profit/uploadfile.vue";
import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
import MyContainer from "@/components/my-container";
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'//文字编辑器
import uploadvideo from '@/views/operatemanage/productalllink/hotsale/common/uploadvideo.vue'
import YhImgUpload from '@/views/operatemanage/productalllink/hotsale/common/uploadhotsalefile.vue'
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
import { v4 as uuidv4 } from 'uuid';
import {
  addContrastSampleCommunicate,
  contrastSampleCommunicateList,
  contrastSampleSendBrandOrGroup,
  completeContrastSample,
  editContrastSample,
  addContrastSample,
  getContrastSampleDetail,
  getContrastSampleList,
  getCurUser,
  getSendUserList,
  getBrandUserList,
  getDirectorList,
  delContrastSample,
} from '@/api/operatemanage/productalllink/alllink'
import { QueryAllDDUserTop100, getPriceuserList } from '@/api/admin/deptuser'
import YhUserelector from '@/components/YhCom/yh-userselector.vue'
import { getAllWarehouse, getAllProBrand } from '@/api/inventory/warehouse'
import { getDirectorGroupList, getList as getshopList, } from '@/api/operatemanage/base/shop'

const tableCols = [
  { istrue: true, prop: 'seatNo', label: '库位号', width: '90', sortable: 'custom' },
  { istrue: true, prop: 'contrastPrincipalName', label: '比样平台负责人', width: '140', sortable: 'custom' },
  { istrue: true, prop: 'contrastName', label: '比样人', width: '140', sortable: 'custom' },
  { istrue: true, prop: 'productCode', label: '产品编码', width: '160', sortable: 'custom' },
  { istrue: true, prop: 'ownExpress', label: '自仓快递单号', width: '160', sortable: 'custom' },
  { istrue: true, prop: 'rivalExpress', label: '对手采样快递单号', width: '160', sortable: 'custom' },
  { istrue: true, prop: 'consigneeName', label: '收货人', width: '160', sortable: 'custom' },
  { istrue: true, type: 'button', label: '对样工单', width: '110', btnList: [{ label: "编辑", handle: (that, row) => that.onEditoperation(row), },] },
  { istrue: true, prop: 'contrastResult', label: '比样结果', width: '160', sortable: 'custom' },
  { istrue: true, prop: 'contrastProcessing', label: '比样进度', width: '160', sortable: 'custom' },
  { istrue: true, type: 'button', label: '最终沟通结果', width: '160', btnList: [{ label: "查看", handle: (that, row) => that.finalcommunicationresult(row) }] },
  { istrue: true, type: 'button', label: '操作', btnList: [{ label: "删除", handle: (that, row) => that.operationdelete(row), permission: 'contrastsample:del', },] },
];

export default {
  name: 'hotsalegoodsselection',
  components: { MyContainer, cesTable, YhQuillEditor, uploadfile, YhUserelector, YhImgUpload, videoplayer, uploadvideo },
  data() {
    return {
      btnCompleteLoading: false,
      btnSaveLoading: false,
      btnloading: false,
      catefeedback: false,
      contuserId: null,
      contddUserId: null,
      disableall: false,
      editdisable: false,
      feedback: {
        pointMsg: '',
        detailMsg: '',
      },
      sultviewlist: [],
      communicationresultview: false,
      pricinglist: [],
      brandlist: [],//采购选择器数据
      directorlist: [],//运营专员选择器数据
      notifier: {
        customMsg: '',
        customhjList: [],
        customcgList: [],
        customyyList: [],
      },
      oneselfretdatafa: [],
      oneselfpictureownPropDetailList: [],
      familyonpicturesvideo: false,
      accepttyes: "*",
      uptype: "other",
      limit: 4,
      delfunction: null,
      islook: false,
      contentheight: null,
      // retdatafa: {},
      familretdatafa: {},
      keys: 'four',
      homepictureownPropDetailList: [],
      familhomepictureownPropDetailList: [],
      familpicturehomeview: 'familhomecomparisonpicture',
      picturehomeview: 'homecomparisonpicture',
      ownonpicturesvideo: false,
      ReferrerOptions: [],
      sampleCommunicationfeedback: false,
      customizethenotifier: false,
      activeName: 'first',
      categorytitle: '',
      ruleForm: {
        contrastSample: {
          seatNo: null,
          contrastPrincipalDUserId: null,
          contrastPrincipalName: null,
          contrastName: null,
          contrastDUserId: null,
          productCode: null,
          ownExpress: null,
          rivalExpress: null,
          consigneeName: null,
          consigneeDUserId: null,
        },//编辑行内容
        // contrastSample: {},
        contrastSampleLogList: [],//操作日志
        contrastSampleSendLogList: [],//沟通日志
        contrastSampleSetDetailList: [],//对样详细参数
      },
      comparesampleeditingoperation: false,//编辑/操作
      addForm: {
        contrastPrincipalName: null,
        contrastName: null,
        productCode: null,
        consigneeName: null,
        contrastPrincipalDUserId: null,
        contrastDUserId: null,
        seatNo: null,
        productCode: null,
        consigneeDUserId: null,
      },
      contrastSampleList: [],
      dialogVisiblew: false,
      addedcomparisondata: false,//新增比样数据
      newsampledialog: false,//新增比样
      listLoading: false,
      sels: [],
      that: this,//赋值
      list: [],//表格数据
      total: null,
      tableCols: tableCols,
      summaryarry: {},
      pager: { orderBy: '', isAsc: false },
      filter: {
        seatNo: null,
        contrastPrincipalName: null,
        contrastName: null,
        productCode: null,
        ownExpress: null,
        rivalExpress: null,
        contrastResult: null,
        contrastProcessing: null,
      },
      rules: {
        seatNo: [
          { required: true, message: '请输入库位号', trigger: 'blur' }
        ],
        contrastPrincipalName: [
          { required: true, message: '请选择比样平台负责人', trigger: 'blur' }
        ],
        contrastName: [
          { required: true, message: '请输入比样人', trigger: 'blur' }
        ],
        productCode: [
          { required: true, message: '请输入产品编码', trigger: 'blur' }
        ],
        consigneeName: [
          { required: true, message: '请输入收货人', trigger: 'blur' }
        ],
      },
      validRules: {
        seatNo: [
          { required: true, message: '请输入库位号' }
        ],
        contrastPrincipalDUserId: [
          { required: true, message: '请输入比样平台负责人' }
        ],
        contrastDUserId: [
          { required: true, message: '请输入比样人' }
        ],
        productCode: [
          { required: true, message: '请输入产品编码' }
        ],
        consigneeDUserId: [
          { required: true, message: '请输入收货人' }
        ]
      },
      pageLoading: false,
      uploading: false,
      comparisonid: null,
      rowlist: {},
    };
  },

  async mounted() {
    await this.onSearch();
    await this.init();
    VXETable.setup({
      tooltip: {
        zIndex: 999999
      }
    });
  },

  methods: {
    //列表删除操作
    async operationdelete(row) {
      this.$confirm('确定要执行删除吗?', '提示', {
        confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
      }).then(async () => {
        const { data, success } = await delContrastSample({ id: row.id })
        if (success) {
          this.$message({ message: '删除成功！', type: 'success' });
          await this.onSearch();
        }
      }).catch(() => {
      });
    },
    hasImgTag(detailMsg) {
      // const withoutPTags = detailMsg.replace(/<p>/g, '').replace(/<\/p>/g, '');
      return /<img.*?>/.test(detailMsg);
    },
    async editactionoff() {
      this.contrastSampleSetDetailList = []
      this.contrastSample = []
      this.ruleForm = {}
      this.disableall = false
      await this.onSearch()
    },
    getback(val) {
      if (val == 1) {
        this.ruleForm.contrastSampleSetDetailList.forEach((obj) => {
          if (obj.contrastCategoryName === '图片') {
            obj.ownPropDetailList = this.$refs.supplier_idd.callback();
          }
        })
      } else if (val == 2) {
        this.ruleForm.contrastSampleSetDetailList.forEach((obj) => {
          if (obj.contrastCategoryName === '图片') {
            obj.rivalPropDetailList = this.$refs.supplier_idd2.callback();
          }
        })
      }
    },
    //完成比样
    async completecomparison() {
      this.$confirm("是否确认完成？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          this.btnCompleteLoading = true;
          const { data, success } = await completeContrastSample({ id: this.comparisonid })
          if (success) {
            this.$message({ message: '保存成功！', type: 'success' });
            this.disableall = true
            await this.onSearch()
            await this.refreshpopup()
          }
          setTimeout(() => {
            this.btnCompleteLoading = false;
          }, 2000);
        });
    },
    customihandleClose() {
      this.notifier.customhjList = [];
      this.notifier.customcgList = [];
      this.notifier.customyyList = [];
      this.notifier.customMsg = '';
    },
    handleClose() {
      this.feedback.pointMsg = '';
      this.feedback.detailMsg = '';
    },
    //沟通反馈保存
    async feedbackpreservation() {
      if (this.feedback.pointMsg == '' || this.feedback.detailMsg == '') {
        this.$message.error('请填写完整信息！');
        return
      }
      const params = {
        contrastSampleId: this.comparisonid,
        ...this.feedback
      }
      const { data, success } = await addContrastSampleCommunicate(params)
      if (success) {
        this.$message({ message: '保存成功！', type: 'success' });
        this.sampleCommunicationfeedback = false
        await this.refreshpopup()
        // await this.onEditoperation()
      }
    },
    //最终沟通结果
    async finalcommunicationresult(row) {
      const { data } = await contrastSampleCommunicateList({ id: row.id })
      this.sultviewlist = data
      this.communicationresultview = true
    },
    //对比结果
    comparisonresultchange(val, row) {
      var consistentlist = [];
      var inconformitylist = [];
      this.ruleForm.contrastSampleSetDetailList.forEach(function (item) {
        if (item.contrastResultId === 1) {
          consistentlist.push(item);
        } else if (item.contrastResultId === 2 || item.contrastResultId === 3 || item.contrastResultId === 4) {
          inconformitylist.push(item);
        }
      });
      if (consistentlist.length >= inconformitylist.length) {
        this.ruleForm.contrastSample.contrastResult = '基本一致（略有差异）'
      } else {
        this.ruleForm.contrastSample.contrastResult = '不一致'
      }
    },
    //编辑操作保存
    async editsaveoperation() {
      //校验图片或视频存在内容，需要填写对比结果
      for (const item of this.ruleForm.contrastSampleSetDetailList.slice(0, 2)) {
        if (item.ownPropDetailList !== null && item.rivalPropDetailList !== null) {
          if ((item.ownPropDetailList.length !== 0 || item.rivalPropDetailList.length !== 0) && item.contrastResultId === 0) {
            this.$message.error('图片或视频已有内容，请填写对比结果');
            return true;
          }
        } else {
          if (item.ownPropDetailList !== null && item.rivalPropDetailList !== null && item.contrastResultId === 0) {
            this.$message.error('图片或视频已有内容，请填写对比结果');
            return true;
          }
        }
      }
      //校验除图片、视频后的内容在填写自家对家后是否选择对比结果
      //去掉图片视频行
      const contrastSampleSetDetailList = this.ruleForm.contrastSampleSetDetailList;
      const selectedItems = contrastSampleSetDetailList.filter((item, index) => {
        return index >= 2;
      });
      //校验剩下行
      const hasIncompleteInfo = selectedItems.some(item => {
        if (
          ((item.ownAttr !== null && item.ownAttr !== '') || (item.rivalAttr !== null && item.rivalAttr !== '')) &&
          item.contrastResultId === 0
        ) {
          this.$message.error('自家、对家在填写内容后，需填写对比结果');
          return true;
        }
        if ((item.rivalAttr !== '' || item.ownAttr !== '') && item.contrastResultId === '') {
          this.$message.error('自家、对家在填写内容后，需填写对比结果');
          return true;
        }
      });
      if (hasIncompleteInfo) {
        return;
      }
      //保存
      this.btnSaveLoading = true;
      if (this.ruleForm.contrastSample.seatNo == "" || this.ruleForm.contrastSample.contrastPrincipalName == "" || this.ruleForm.contrastSample.contrastName == "" || this.ruleForm.contrastSample.productCode == "" || this.ruleForm.contrastSample.consigneeName == "") {
        this.$message.error('请填写完整信息！');
        this.btnSaveLoading = false;
        return
      }
      this.listLoading = true;
      const { data, success } = await editContrastSample({ ...this.ruleForm })
      this.listLoading = false;
      if (success) {
        this.$message({ message: '保存成功！', type: 'success' });
        this.comparesampleeditingoperation = false
        await this.onSearch()
        this.listLoading = false;
        // await this.onEditoperation()
      }
      setTimeout(() => {
        this.btnSaveLoading = false;
      }, 2000);
    },
    async init() {
      //运营专员
      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.userId, label: item.userName }; });
      //采购专员
      var res1 = await getBrandUserList();
      this.brandlist = res1.data.map(item => { return { value: item.userId, label: item.brandName }; });
      //核价专员
      const { data } = await getPriceuserList()
      this.pricinglist = data.map(item => { return { value: item.ddUserId, label: item.userName }; });
    },
    //自定义通知人保存
    async notifierAddMarks() {
      // const params = { ...this.notifier, typeId: 3 }
      const { success } = await contrastSampleSendBrandOrGroup({ ...this.notifier, id: this.comparisonid, typeId: 3 })
      if (success) {
        this.$message({ message: '通知成功！', type: 'success' });
        await this.refreshpopup()
      }
    },
    //一键通知采购
    async notificationpurchase() {
      const { success } = await contrastSampleSendBrandOrGroup({ id: this.comparisonid, typeId: 2 })
      if (success) {
        this.$message({ message: '通知成功！', type: 'success' });
        await this.refreshpopup()
      }
    },
    //一键通知比样运营
    async notificationoperation() {
      const { success } = await contrastSampleSendBrandOrGroup({ id: this.comparisonid, typeId: 1 })
      if (success) {
        this.$message({ message: '通知成功！', type: 'success' });
        await this.refreshpopup()
      }
    },
    //视频保存
    async familsubmitsave() {
      this.ruleForm.contrastSampleSetDetailList.forEach((obj) => {
        if (obj.contrastCategoryName === '视频') {
          obj.ownPropDetailList = this.familretdatafa;
          obj.rivalPropDetailList = this.oneselfretdatafa;
        }
      });
      this.$message({ message: '保存成功！', type: 'success' });
      // await this.onEditoperation()
      this.familyonpicturesvideo = false
    },
    async submitsave() {
      this.ruleForm.contrastSampleSetDetailList.forEach((obj) => {
        if (obj.contrastCategoryName === '图片') {
          obj.ownPropDetailList = this.oneselfpictureownPropDetailList.map((item) => item);
          obj.rivalPropDetailList = this.homepictureownPropDetailList.map((item) => item);
        }
      });
      this.$message({ message: '保存成功！', type: 'success' });
      // await this.onEditoperation()
      this.ownonpicturesvideo = false
    },
    //上传超出提出
    exceed() {
      this.$message({ message: "超出上传数量限制", type: "warning" });
    },
    showvideo(val) {
      // this.videoplayerReload = false;
      this.videoplayerReload = true;
      this.dialogVisible = true;
      this.videoUrl = val;
    },
    //对家
    onpicturevideo(row) {
      if (row.contrastCategoryName == '视频') {
        // this.oneselfretdatafa = row.ownPropDetailList
        // this.familretdatafa = row.rivalPropDetailList
        this.familretdatafa = row.ownPropDetailList ? row.ownPropDetailList : []
        this.oneselfretdatafa = row.rivalPropDetailList ? row.rivalPropDetailList : []
        this.$nextTick(() => {
          this.$refs.uploadvideo.getval(this.oneselfretdatafa)
          this.$refs.uploadvideoe.getval(this.familretdatafa)
        })
        this.familyonpicturesvideo = true
      } else {
        // this.oneselfpictureownPropDetailList = row.ownPropDetailList
        // this.homepictureownPropDetailList = row.rivalPropDetailList
        this.oneselfpictureownPropDetailList = row.ownPropDetailList ? row.ownPropDetailList : []
        this.homepictureownPropDetailList = row.rivalPropDetailList ? row.rivalPropDetailList : []
        this.ownonpicturesvideo = true
      }
    },
    changee(e) {
      this.$forceUpdate()
    },
    //自家
    ownpicturesvideos(row) {
      if (row.contrastCategoryName == '视频') {
        this.familretdatafa = row.ownPropDetailList ? row.ownPropDetailList : []
        this.oneselfretdatafa = row.rivalPropDetailList ? row.rivalPropDetailList : []

        this.$nextTick(() => {
          this.$refs.uploadvideo.getval(this.oneselfretdatafa)
          this.$refs.uploadvideoe.getval(this.familretdatafa)
        })
        this.familyonpicturesvideo = true
      } else {
        // this.picturehomeview = 'homecomparisonpicture'
        this.oneselfpictureownPropDetailList = row.ownPropDetailList ? row.ownPropDetailList : []
        this.homepictureownPropDetailList = row.rivalPropDetailList ? row.rivalPropDetailList : []
        this.ownonpicturesvideo = true
      }
    },
    onAddMarks() {
      const newContrastResult = {
        contrastCategoryName: this.categorytitle,
        rivalAttr: '',
        ownAttr: '',
        isShowProp: false,
        contrastResultId: '',
      };
      this.ruleForm.contrastSampleSetDetailList.push(newContrastResult);
      this.categorytitle = '';
      this.dialogVisiblew = false;
    },
    async addedcomparisonsamplesaving() {
      this.btnloading = true
      console.log(this.contrastSampleList, 'this.contrastSampleList');
      if (this.contrastSampleList == []) {
        this.$message.error('请填写完整信息！');
        this.btnloading = false
        return;
      }
      this.contrastSampleList.forEach(item => {
        if (item.consigneeName === "" || item.contrastName === "" || item.contrastPrincipalName === "" || item.seatNo === "" || item.productCode === "") {
          this.$message.error('请填写完整信息！');
          this.btnloading = false
          return;
        }
      });
      //删除id操作
      const sampleListWithoutIds = this.contrastSampleList.map(item => {
        const { id, ...rest } = item;
        return rest;
      });
      const { success } = await addContrastSample({ contrastSampleList: sampleListWithoutIds })
      if (success) {
        this.$message({ message: '保存成功', type: 'success' });
        this.newsampledialog = false
        this.contrastSampleList = []
        await this.onSearch()
      }
      this.btnloading = false
    },
    communicationfeedback() {
      this.sampleCommunicationfeedback = true;
    },
    customizenotifier() {
      this.customizethenotifier = true;
    },
    handleClick(tab, event) {
      if (tab.name === 'first') {
        this.activeName = 'first';
      } else {
        this.activeName = 'second';
      }
    },
    addnew() {
      this.dialogVisiblew = true;
    },
    //刷新编辑弹窗
    async refreshpopup() {
      const { data } = await getContrastSampleDetail({ id: this.comparisonid })
      console.log(data, 'data');
      this.ruleForm = data
    },
    //编辑操作
    async onEditoperation(row) {
      this.disableall = false
      this.contrastSampleSetDetailList = []
      this.contrastSample = []
      this.ruleForm = {}
      this.editdisable = false
      this.catefeedback = false;
      if (row === undefined) {
        row = this.rowlist
        if (row.contrastProcessing === "比样完成") {
          this.disableall = true
        }
        const res = await getCurUser()
        this.contddUserId = res.data.ddUserId
        this.contuserId = res.data.userId
        if (row.contrastDUserId === this.contddUserId) {
          this.editdisable = true
        }
        const res1 = await getSendUserList({ id: row.id })
        var sendList = res1.data
        sendList.forEach((item) => {
          if (item == this.contuserId || item == this.contddUserId) {
            this.catefeedback = true;
          }
        });
        console.log(row, 'row');
        this.comparisonid = row.id
        const { data } = await getContrastSampleDetail({ id: row.id })
        console.log(data.contrastSample, 'data.contrastSample111111111111');
        this.ruleForm.contrastSample = data.contrastSample
        this.ruleForm.contrastSampleSetDetailList = data.contrastSampleSetDetailList
        this.ruleForm.contrastSampleLogList = data.contrastSampleLogList
        this.ruleForm.contrastSampleSendLogList = data.contrastSampleSendLogList
        this.comparesampleeditingoperation = true;
      } else {
        this.rowlist = row
        if (row.contrastProcessing === "比样完成") {
          this.disableall = true
        }
        const res = await getCurUser()
        this.contddUserId = res.data.ddUserId
        this.contuserId = res.data.userId
        if (row.contrastDUserId === this.contddUserId) {
          this.editdisable = true
        }
        const res1 = await getSendUserList({ id: row.id })
        var sendList = res1.data
        sendList.forEach((item) => {
          if (item == this.contuserId || item == this.contddUserId) {
            this.catefeedback = true;
          }
        });
        console.log(row, 'row');
        this.comparisonid = row.id
        const { data } = await getContrastSampleDetail({ id: row.id })
        console.log(data, 'data');
        this.ruleForm = data
        this.comparesampleeditingoperation = true;
      }

    },
    submitthesampledata() {
      if (this.addForm.contrastPrincipalDUserId === null || this.addForm.contrastDUserId === null || this.addForm.consigneeDUserId === null || this.addForm.productCode === null || this.addForm.seatNo === null) {
        this.$message.error('请填写完整信息！');
        return
      }
      this.contrastSampleList.push(this.addForm);
      this.$nextTick(() => {
        this.addForm = {}
        this.addForm.contrastPrincipalName = null
        this.addForm.contrastPrincipalDUserId = null
        this.addForm.contrastName = null
        this.addForm.contrastDUserId = null
        this.addForm.consigneeName = null
        this.addForm.consigneeDUserId = null
      })
      this.addedcomparisondata = false
    },
    //删除行
    deletecomparison(row) {
      const index = this.contrastSampleList.findIndex(item => item.id === row.id);
      if (index !== -1) {
        this.contrastSampleList.splice(index, 1);
      }
    },
    //新增比样行，生成一个id，在保存时删除id字段
    newline() {
      const newObject = {
        id: uuidv4(),
        consigneeName: '',
        rivalExpress: '',
        ownExpress: '',
        productCode: '',
        contrastName: '',
        contrastPrincipalName: '',
        seatNo: '',
      };
      this.contrastSampleList.push(newObject);
    },
    //新增比样
    onAddselect() {
      this.contrastSampleList = []
      this.btnloading = false
      this.newsampledialog = true
    },
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist();
    },
    async getlist() {
      this.sels = [];
      this.listLoading = true
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = { ...pager, ...page, ...this.filter };
      const { data } = await getContrastSampleList(params)
      this.list = data.list
      this.total = data.total
      this.listLoading = false
    },
    //排序查询
    async sortchange(column) {
      if (!column.order) {
        this.filter.orderBy = 'yearMonthDay';
        this.filter.isAsc = false;
      } else {
        this.filter.orderBy = column.prop
        this.filter.isAsc = column.order.indexOf("descending") == -1 ? true : false
        this.getlist();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.ellipsis-content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 650px;
}

.rzmz span:not(:last-child) {
  margin-right: 10px;
}

.text-center {
  text-align: center;
}

.bzbjfgx {
  // border: 1px solid #dcdfe6;
  border-top: 1px solid #dcdfe6;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  font-size: 12px;
  padding: 25px 0;
  margin-top: 20px;
}

.rzxgsj {
  height: 30px;
  display: inline-block;
  font-size: 14px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.bzczrzx {
  box-sizing: border-box;
  padding: 10px 60px;
}

.rztx {
  width: 25px;
  height: 25px;
  background-color: #f5f5f5;
  border-radius: 50%;
  margin-right: 15px;
}

.rzxgx {
  max-width: 200px;
  margin-right: 10px;
  color: #999;
}

.rzxgsj {
  height: 30px;
  display: inline-block;
  font-size: 14px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.rzbox {
  display: flex;
}

.rzxgnr {
  max-width: 450px;
  // line-height: 15px;
  font-size: 12px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-top: 5px;
}

.rzxgq {
  // width: 50px;
  // margin-left: 43px;
  margin-right: 2px;
  margin-top: 5px;
}

.rzmz {
  width: 700px;
  margin-right: 5px;
}

.scrollable-div {
  height: 660px;
  overflow-y: auto;
}

.sample {
  height: 40px;
  /* background-color: aquamarine; */
  // font-size: 18px;
  color: #666;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 0 35px;
  margin: 15px;
}

.sampleborder {
  border: 1px solid #dcdfe6;
  padding-top: 10px;
  padding-bottom: 10px;
}

::v-deep .bzjzcjrw {
  width: 100%;
  margin-top: 15px;
  background-color: #fff;
}


::v-deep .bzjzcjrw .bt {
  height: 40px;
  /* background-color: aquamarine; */
  font-size: 18px;
  color: #666;
  // border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 0 35px;
  // margin-bottom: 20px;
}

::v-deep .bzjzcjrw .rwmc {
  margin-bottom: 20px;
}

::v-deep .bzjzcjrw .bt i {
  color: #999;
}

::v-deep .bzjzcjrw .bt i {
  margin-left: 8px;
  line-height: 26px;
}

::v-deep .bzjzcjrw .bt i:hover {
  margin-left: 8px;
  line-height: 26px;
  color: #409eff;
  position: relative;
  top: -2px;
}

::v-deep .bzjzcjrw .bzccjlx {
  box-sizing: border-box;
  padding: 0 60px;
  display: flex;
}

::v-deep .bzjzcjrw .bzccjlx {
  width: 100%;
  height: 40px;
  box-sizing: border-box;
  padding: 0 60px;
  display: flex;
}

::v-deep .bzjzcjrw .bzccjlx .lxwz {
  width: 21%;
  font-size: 14px;
  color: #666;
  vertical-align: top;
  line-height: 26px;
  text-align: right;
  /* background-color: rgb(204, 204, 255); */
}

::v-deep .bzjzcjrw .bzccjlx .lxwz2 {
  width: 80%;
  margin-left: 20px;
  margin-top: 3px;
}

.jxkhcj {
  width: 100%;
  height: 300px;
  box-sizing: border-box;
  display: flex;
}

//字体样式
.lxwz {
  width: 130px;
  font-size: 14px; //字体大小
  color: #666; //颜色
  line-height: 30px; //行高
}
</style>
