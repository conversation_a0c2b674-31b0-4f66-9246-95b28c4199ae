<template>
    <MyContainer style="height: 100%;box-sizing: border-box;" class="tableBox">
        <div class="top">
            <el-input v-model="idIptValue" placeholder="请输入编号" style="width: 200px;margin-right: 10px;" clearable
                maxlength="30" />
            <el-input v-model="nameIptValue" placeholder="请输入名称" style="width: 200px;margin-right: 10px;" clearable
                maxlength="30" />
            <el-select v-model="statusIptValue" placeholder="请选择" style="margin-right: 10px;" clearable>
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.label">
                </el-option>
            </el-select>
            <el-button type="primary" style="margin-right: 10px;" @click="searchDis">搜索</el-button>
        </div>
        <div style="margin-top: 20px;;height: 70vh;">
            <el-table :data="tableData" style="width: 100%" v-loading="distributorsLoading" highlight-current-row>
                <el-table-column type="index" label="#">
                </el-table-column>
                <el-table-column prop="drp_co_id" label="分销商编号">
                </el-table-column>
                <el-table-column prop="name" label="分销商名称">
                </el-table-column>
                <el-table-column prop="status" label="状态">
                </el-table-column>
                <el-table-column label="">
                    <template slot-scope="scope">
                        <span style="margin-right: 10px;color: rgb(72, 132, 243);cursor:pointer"
                            @click="openGenusDialog(scope.row)">上下级管理</span>
                        <span style="color: rgb(72, 132, 243);cursor:pointer" @click="openLogDialog(scope.row)">日志</span>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="DistributorsTotal"
            @get-page="getDistributor" @page-change="Dispagechange" @size-change="Dissizechange" class="pageBox" />

        <el-dialog title="上下级管理" :visible.sync="genusDialog" width="25%" :before-close="handleClose" v-dialogDrag>
            <div class="genusManage publicMargin">
                <div class="publicMargin">
                    <span style="margin-right: 10px;">分销商编号: </span>
                    <el-input v-model="distributorsInfo.drp_co_id" placeholder="请输入分销商编号"
                        style="width: 300px;margin-right: 10px;" disabled />
                </div>
                <div class="publicMargin">
                    <span style="margin-right: 10px;">分销商名称: </span>
                    <el-input v-model="distributorsInfo.name" placeholder="请输入分销商名称"
                        style="width: 300px;margin-right: 10px;" disabled />
                </div>
                <div class="publicMargin">
                    <el-radio-group v-model="firmValue" @input="changeRadio">
                        <el-radio :label="'公司内部'">公司内部</el-radio>
                        <el-radio :label="'公司外部'">公司外部</el-radio>
                        <el-radio :label="'外部推荐人'">外部推荐人</el-radio>
                    </el-radio-group>
                </div>
                <div class="publicMargin">
                    <div v-show="firmValue == '公司内部'">
                        <span style="margin-right: 40px;">推荐人: </span>
                        <el-select v-model="distributorsInfo.tuijianrenname" filterable placeholder="请输入推荐人"
                            :filter-method="searchReferrer" @change="changeSelectValue">
                            <el-option v-for="item in ReferrerOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </div>
                    <div v-show="firmValue == '公司外部'">
                        <div class="publicMargin">
                            <span style="margin-right: 10px;">分销商编号: </span>
                            <el-input v-model="distributorsInfo.outertuijianrenid" placeholder="请输入分销商编号"
                                style="width: 300px;margin-right: 10px;" @input="outInput" maxlength="200" clearable />
                        </div>
                        <div>
                            <span style="margin-right: 10px;">分销商名称: </span>
                            <el-select v-model="distributorsInfo.outertuijianrenname" filterable placeholder="请输入分销商名称"
                                :filter-method="searchDistributors" @change="changeDistributors"
                                @visible-change="searcRefereehDetail" clearable>
                                <el-option v-for="item in DistributorsOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </div>
                    </div>
                    <div v-show="firmValue == '外部推荐人'">
                        <div>
                            <span style="margin-right: 10px;">推荐人名称: </span>
                            <el-select v-model="distributorsInfo.outsideruijianrenname" filterable placeholder="外部推荐人姓名"
                                style="width:150px;margin-right:10px" clearable @change="changeOuter">
                                <el-option v-for="item in options" :key="item.id" :label="item.userName"
                                    :value="item.userName">
                                </el-option>
                            </el-select>
                        </div>
                    </div>
                </div>
            </div>
            <div style="display: flex;justify-content: space-evenly;">
                <el-button type="primary" @click="submitGenus">确定</el-button>
                <el-button @click="handleClose">取消</el-button>
            </div>
        </el-dialog>

        <el-dialog title="日志" :visible.sync="logValue" width="50%" :before-close="handleClose" v-loading="logValueLoading"
            v-dialogDrag>
            <div style="display: flex;" class="publicMargin">
                <el-date-picker v-model="dailyPickValue" type="datetimerange" :picker-options="pickerOptions"
                    range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right"
                    style="width: 300px;margin-right: 10px;" @change="pickDate">
                </el-date-picker>
                <el-input v-model="logInfo.name" placeholder="请输入操作人" style="width: 200px;margin-right: 10px;" clearable
                    maxlength="200" />
                <el-button type="primary" @click="searchLog">搜索</el-button>
            </div>
            <el-table :data="logTableData" style="width: 100%">
                <el-table-column type="index" label="#" width="180">
                </el-table-column>
                <el-table-column prop="oldValue" label="操作前值" width="180">
                </el-table-column>
                <el-table-column prop="newValue" label="操作后值">
                </el-table-column>
                <el-table-column prop="addedDate" label="操作日期" sortable>
                </el-table-column>
                <el-table-column prop="addedBy" label="操作人">
                </el-table-column>
            </el-table>
            <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="total" @get-page="logSearch"
                @page-change="pagechange" @size-change="sizechange" />
        </el-dialog>
    </MyContainer>
</template>

<script>
//导入dayjs
import dayjs from 'dayjs'
import MyContainer from "@/components/my-container";
import { getDistributor, getDistributorLog, updateDistributor, queryAllDistributorTop100, GetDistributorInfo,queryAllDistributorOutSiders } from '@/api/customerservice/Distributor'
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser'
export default {
    name: "distributionDetails",
    components: {
        MyContainer
    },
    data () {
        return {
            options:[],
            pageList: {
                currentPage: 1,
                pageSize: 20,
            },
            idIptValue: null,//编号
            nameIptValue: null,//名称
            statusIptValue: '生效',//状态
            ReferrerOptions: [],//推荐人下拉框选项tuijianrenId
            DistributorsOptions: [],//分销商下拉框选项
            logValueLoading: true,//日志加载
            distributorsLoading: true,//分销商加载
            distributorsInfo: {
                drp_co_id: null,//分销商id
                name: null,//分销商名称
                status: null,//分销商状态
                tuijianrenid: null,//推荐人id
                tuijianrentype: null,//推荐人类型
                tuijianrenname: null,//推荐人名称
                CurrentPage: 1,//当前页
                PageSize: 50,//每页条数
                OrderBy: null,//排序字段
                IsAsc: null,//排序方式
                outertuijianrenid: null,//公司外部推荐人id
                outertuijianrenname: null,//公司外部推荐人名称
                outsideruijianrenid: null,//外部推荐人id
                outsideruijianrenname: null,//外部推荐人名称
            },
            dailyPickValue: '',//日志时间
            pickerOptions: {
                shortcuts: [
                    {
                        text: '近一周',
                        onClick (picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    },
                    {
                        text: '近一个月',
                        onClick (picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    },
                    {
                        text: '近三个月',
                        onClick (picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        },

                    }

                ],
                // 自定义日期禁用函数
                disabledDate (date) {
                    // 获取当前日期
                    const currentDate = new Date();
                    // 如果选中日期在当前日期之后，则禁用它
                    return date > currentDate;
                }
            },
            firmValue: '公司内部',//公司选项组
            genusDialog: false,//上下级管理弹窗
            logValue: false,//日志弹窗
            options: [
                {
                    value: '1',
                    label: '生效'
                },
                {
                    value: '2',
                    label: '作废'
                },
                {
                    value: '3',
                    label: '等待审核通过'
                },
                {
                    value: '4',
                    label: '审核被拒绝'
                }
            ],//下拉框选项
            selectValue: '',//下拉框选中值
            tableData: [],//表格数据
            logTableData: [],//日志表格数据
            total: null,
            DistributorsTotal: null,
            logInfo: {
                drp_co_id: null,//分销商id
                name: null,//分销商名称
                startDate: null,//开始时间
                endDate: null,//结束时间
                CurrentPage: 1,//当前页
                PageSize: 50,//每页条数
                OrderBy: null,//排序字段
                IsAsc: null,//排序方式
            }
        }
    },
    computed: {
        isSearch () {
            let id = this.distributorsInfo.drp_co_id
            let name = this.distributorsInfo.name
            let status = this.distributorsInfo.status
            return (id != null && id != '') || (name != null && name != '') || (status != null && status != '')
        }
    },
    mounted () {
        this.getDistributor()
    },
    methods: {
        changeOuter (e) {
            console.log(e, 'e');
            console.log(this.options,'options');
            if (e == null || e == undefined || e == '') {
                this.distributorsInfo.outsideruijianrenname = null
                this.distributorsInfo.outsideruijianrenid = null
                return
            }
            //找出选中的推荐人
            let obj = this.options.find(item => item.userName === e)
            this.distributorsInfo.outsideruijianrenid = obj.id
            this.distributorsInfo.outsideruijianrenname = obj.userName
        },
         //获取外部推荐人下拉数据
         async getOutSelectInfo () {
            const { data, success } = await queryAllDistributorOutSiders()
            if (success) {
                this.options = data
            } else {
                this.$message.error('获取外部推荐人下拉数据失败')
            }
        },
        //点击明细下拉框先拉一次前100条数据
        async searcRefereehDetail (e) {
            console.log(e, '出现');
            //如果this.DistributorsOptions有数据就不拉了
            if (this.DistributorsOptions.length > 0) {
                return
            }
            if (e) {
                const { data } = await queryAllDistributorTop100()
                if (data) {
                    this.DistributorsOptions = data.map(item => {
                        return {
                            value: item.drp_co_id,
                            label: item.name
                        }
                    })
                }
            }
        },
        //外部分销商input
        outInput () {
            if (this.distributorsInfo.outertuijianrenid == null || this.distributorsInfo.outertuijianrenid == '' || this.distributorsInfo.outertuijianrenid == undefined) {
                this.distributorsInfo.outertuijianrenname = null
            }
        },
        //搜索日志
        searchLog () {
            //将页码重置为1
            this.logInfo.CurrentPage = 1;
            //先清空数据
            this.logSearch()
        },
        pickDate () {
            console.log(this.dailyPickValue, 'this.dailyPickValue');
            //使用dayjs将this.dailyPickValue[0],this.dailyPickValue[1]转换为YYYY-MM-DD格式
            if (this.dailyPickValue == null || this.dailyPickValue == undefined || this.dailyPickValue == '') {
                //将时间重置为null
                this.logInfo.startDate = null
                this.logInfo.endDate = null
                console.log(this.logInfo, '进来了');
                //将页码重置为1
                this.logInfo.CurrentPage = 1;
                //先清空数据
                this.logSearch()
            } else {
                this.logInfo.startDate = dayjs(this.dailyPickValue[0]).format('YYYY-MM-DD')
                this.logInfo.endDate = dayjs(this.dailyPickValue[1]).format('YYYY-MM-DD')
                this.logSearch()
            }

        },
        //搜索分销商
        searchDis () {
            this.distributorsInfo.drp_co_id = this.idIptValue
            this.distributorsInfo.name = this.nameIptValue
            this.distributorsInfo.status = this.statusIptValue
            //清除掉distributorsInfo.drp_co_id和distributorsInfo.name的空格
            this.distributorsInfo.drp_co_id = this.distributorsInfo.drp_co_id == null ? null : this.distributorsInfo.drp_co_id.trim()
            this.distributorsInfo.name = this.distributorsInfo.name == null ? null : this.distributorsInfo.name.trim()
            console.log(this.distributorsInfo, 'this.distributorsInfo');
            this.distributorsInfo.CurrentPage = 1;
            //如果isSearch为true,就拉原始数据
            if (this.isSearch) {
                this.getDistributor()
            } else {
                //否则就拉全部数据
                this.distributorsInfo.drp_co_id = null
                this.distributorsInfo.name = null
                this.distributorsInfo.status = null
                this.getDistributor()
            }
        },
        //分销商分页数量改变
        Dissizechange (val) {
            this.distributorsInfo.CurrentPage = 1;
            this.distributorsInfo.PageSize = val;
            this.getDistributor();
        },
        //分销商分页页码改变
        Dispagechange (val) {
            this.distributorsInfo.CurrentPage = val;
            this.getDistributor();
        },
        //日志分页数量改变
        sizechange (val) {
            this.logInfo.CurrentPage = 1;
            this.logInfo.PageSize = val;
            this.logSearch();
        },
        //日志分页页码改变
        pagechange (val) {
            this.logInfo.CurrentPage = val;
            this.logSearch();
        },
        async changeDistributors (e) {
            console.log(e, 'e');
            if (e == null || e == undefined || e == '') {
                this.distributorsInfo.outertuijianrenname = null
                this.distributorsInfo.outertuijianrenid = null
                return
            }
            //找出选中的推荐人
            let obj = this.DistributorsOptions.find(item => item.value === e)
            this.distributorsInfo.outertuijianrenid = obj.value
            this.distributorsInfo.outertuijianrenname = obj.label
        },
        async searchDistributors (e) {
            //如果e的长度大于200,就提示
            if (e.length > 200) {
                this.$message.error('最多输入200个字符')
                return
            }
            const { data } = await queryAllDistributorTop100({ keywords: e })
            if (data) {
                this.DistributorsOptions = data.map(item => {
                    return {
                        value: item.drp_co_id,
                        label: item.name
                    }
                })
            }
        },
        //上下级管理确定
        async submitGenus () {
            //如果this.distributorsInfo.outertuijianrenid或者this.distributorsInfo.tuijianrenid有一个为空,就提示
            // if (this.distributorsInfo.outertuijianrenid == null && this.distributorsInfo.tuijianrenid == null) {
            //     this.$message.error('请输入完整数据')
            //     return
            // }
            this.distributorsInfo.tuijianrentype = this.firmValue
            //如果是公司内部,就清空外部推荐人
            if (this.firmValue === '公司内部') {
                this.distributorsInfo.outertuijianrenid = null
                this.distributorsInfo.outertuijianrenname = null
            } else {
                this.distributorsInfo.tuijianrenid = null
                this.distributorsInfo.tuijianrenname = ''
            }
            const res = await updateDistributor(this.distributorsInfo)
            if (res.success) {
                this.$message.success('修改成功')
                this.genusDialog = false
                this.clear()
            }
            //关闭弹层
        },
        //推荐人下拉框值改变
        changeSelectValue (e) {
            //找出选中的推荐人
            let obj = this.ReferrerOptions.find(item => item.value === e)
            this.distributorsInfo.tuijianrenid = obj.value
            this.distributorsInfo.tuijianrenname = obj.name
            this.distributorsInfo.tuijianrentype = this.firmValue
        },
        //搜索推荐人
        async searchReferrer (e) {
            //如果e的长度大于200,就提示
            if (e.length > 200) {
                this.$message.error('最多输入200个字符')
                return
            }
            // 如果输入为空，清空下拉框
            if (e === '' || e === null || e === undefined) {
                this.ReferrerOptions = []
                return
            }
            const { data } = await QueryAllDDUserTop100({ keywords: e })
            if (data) {
                console.log(data, 'data1231231212');
                this.ReferrerOptions = data.map(item => {
                    return {
                        value: item.ddUserId,
                        label: item.userName + ' - ' + `(${item.deptName})`,
                        name: item.userName
                    }
                })
            }
        },
        // 获取分销商列表
        async getDistributor () {
            if (this.statusIptValue) {
                this.distributorsInfo.status = this.statusIptValue
            }
            const { data } = await getDistributor(this.distributorsInfo)
            if (data) {
                console.log(data, 'data');
                this.tableData = data.list
                this.DistributorsTotal = data.total
                this.distributorsLoading = false
            }
        },
        // 抽离日志搜索
        async logSearch () {
            const { data } = await getDistributorLog(this.logInfo)
            if (data) {
                this.logTableData = data.list
                this.logValueLoading = false
                this.total = data.total
            }
        },
        // 日志弹窗
        async openLogDialog (row) {
            this.logValueLoading = true
            this.logInfo.drp_co_id = row.drp_co_id
            this.logValue = true
            this.logSearch()
        },
        // 上下级管理弹窗
        async openGenusDialog (row) {
            this.distributorsInfo = row
            this.genusDialog = true
            const { data,success } = await GetDistributorInfo(row.drp_co_id)
            if (success) {
                this.distributorsInfo = data
                if (this.distributorsInfo.tuijianrentype) {
                    this.firmValue = this.distributorsInfo.tuijianrentype
                } else {
                    this.firmValue = '公司内部'
                }
            }
            this.getOutSelectInfo()
        },
        //清除数据
        clear () {
            this.genusDialog = false
            this.logValue = false
            //清空推荐人搜索列表
            this.ReferrerOptions = []
            //清空数据
            this.distributorsInfo = {
                drp_co_id: null,//分销商id
                name: null,//分销商名称
                status: null,//分销商状态
                referrerDingding: null,//推荐人钉钉
                referrerType: null,//推荐人类型
                referrerName: '',//推荐人名称
                CurrentPage: 1,//当前页
                PageSize: 20,//每页条数
                OrderBy: null,//排序字段
                IsAsc: null,//排序方式
            }
            //清空日志时间
            this.dailyPickValue = null
        },
        handleClose () {
            this.clear()
        },
        changeRadio () {
            console.log(this.firmValue, 'this.firmValue');

        }
    }
}
</script>

<style scoped lang="scss">
.genusManage {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-top: 20px;
}

.publicMargin {
    margin-bottom: 25px;
}

.tableBox {
    position: relative;
    padding: 30px 0 40px;

    .top {
        display: flex;
        align-items: center;
        position: fixed;
        top: 150px;
        z-index: 999;
    }

    .pageBox {
        width: 88%;
        position: fixed;
        bottom: 20px;
        z-index: 999;
    }
}
</style>