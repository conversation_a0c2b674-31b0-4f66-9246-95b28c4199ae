<template>
    <div style="height:100%;">
        <el-row :gutter="15">
            <el-col :xs="24" :sm="24" :lg="8" v-for="thisVideo in videoList" :key="thisVideo">
                <el-card class="video-box">
                    <video :src="thisVideo" controls controlslist="nodownload nofullscreen noremoteplayback" oncontextmenu="return false;"></video>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>


<script>   
    //import $ from 'jquery'

    export default {
        components: { buschar },
        props: {
            videoList: { type: Object, default: null }
        },
        data () {
            return {

            };
        },

        mounted () {


        },
        methods: {

        }
    };
</script>

<style lang="scss" scoped>
    .video-box {
        padding-bottom: 56.25%;
        width: 100%;
        position: relative;
    }

    .video-box video {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        object-fit: cover;
    }
</style>