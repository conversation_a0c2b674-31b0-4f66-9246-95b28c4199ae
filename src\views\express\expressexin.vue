<template>
  <container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="运单号:">
             <el-input v-model.trim="filter.billnumber" />
        </el-form-item>
        <el-form-item label="结算时间:">
            <el-date-picker style="width: 240px" v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item label="导入时间:">
            <el-date-picker style="width: 240px" v-model="filter.timerange1" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item label="快递公司:">
          <el-select v-model="filter.companyId" placeholder="请选择快递公司" style="width: 110px">
              <el-option label="请选择" value></el-option>
              <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id"/>
          </el-select>
        </el-form-item>
        <el-form-item label="发货仓库:">
          <el-select v-model="filter.warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 100px">
             <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onDown">下载导入模板</el-button>
          <el-button type="primary" @click="startImport">导入</el-button>
        </el-form-item>
      </el-form>
    </template>

  <el-tabs v-model="activeName" style="height: 94%;">
    <el-tab-pane label="抛货" name="first" style="height: 100%;">
       <expressvolume :filter="filter" :tablekey="tableKeyCol.tableKey1" ref="expvolume" @onDelete="showOnDelete"/>
    </el-tab-pane>
    <el-tab-pane label="理赔" name="second" style="height: 100%;">
      <expressclaim :filter="filter" :tablekey="tableKeyCol.tableKey2" ref="expclaim" @onDelete="showOnDelete"/>
    </el-tab-pane>
    <el-tab-pane label="违规" name="third" style="height: 100%;">
      <expressviolation :filter="filter" :tablekey="tableKeyCol.tableKey3" ref="expviolation" @onDelete="showOnDelete"/>
    </el-tab-pane>
    <el-tab-pane label="退件" name="fourth" style="height: 100%;">
      <expressrefund :filter="filter" :tablekey="tableKeyCol.tableKey4" ref="exprefund" @onDelete="showOnDelete"/>
    </el-tab-pane>
    <el-tab-pane label="转件" name="five" style="height: 100%;">
       <expresstransfer :filter="filter" :tablekey="tableKeyCol.tableKey5" ref="exptransfer" @onDelete="showOnDelete"/>
    </el-tab-pane>
    <el-tab-pane label="舟山岛屿" name="six" style="height: 100%;">
       <expressfaraway :filter="filter" :tablekey="tableKeyCol.tableKey6" ref="expfaraway" @onDelete="showOnDelete"/>
    </el-tab-pane>
    <el-tab-pane label="其他" name="seven" style="height: 100%;">
       <expresseother :filter="filter" :tablekey="tableKeyCol.tableKey7" ref="expother" @onDelete="showOnDelete"/>
    </el-tab-pane>
  </el-tabs>

   <el-dialog title="导入" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
      <span>
        <el-row>
           <el-alert type="warning" show-icon :closable="false"
              title="温馨提示:导入运费前请确认是否导入了对应的账单！">
          </el-alert>
        </el-row>
        <br>
        <el-row>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-select v-model="importFilte.companyId" placeholder="请选择快递公司" style="width: 100%">
                  <el-option
                    v-for="item in expresscompanylist"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"/>
               </el-select>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                  <el-select v-model="importFilte.warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 100%">
                    <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value"/>
                  </el-select>
              </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                <el-upload
                  ref="upload"
                  class="upload-demo"
                  :auto-upload="false"
                  :multiple="false"
                  :limit="1"
                  action
                  accept=".xlsx"
                  :http-request="uploadFile"
                  :file-list="fileList"
                  :data="fileparm">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template>
                  <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
                </el-upload>
            </el-col>
          </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="批量删除" :visible.sync="dialogdeletebatchNumberVisible" width="500px" v-dialogDrag>
      <el-row>
        <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">             
            <el-select v-model="deletefilter.aType" :disabled="true" placeholder="....." style="width: 100%">                 
              <el-option label="抛货" :value="-1"/>
              <el-option label="违规" :value="0"/>
              <el-option label="舟山岛屿" :value="1"/>
              <el-option label="退件" :value="2"/>
              <el-option label="转件" :value="3"/>
              <el-option label="理赔" :value="4"/>
              <el-option label="其他" :value="5"/>
            </el-select>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-select v-model="deletefilter.companyId" placeholder="请选择快递公司" style="width: 100%">
                <el-option label="所有" value=""/>
                <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id"/>
              </el-select>
          </el-col>
           <el-col :xs="24" :sm="11" :md="11" :lg="11" :xl="11">
              <el-input placeholder="请输入批次号" v-model="deletefilter.batchNumber" style="width: 100%"></el-input>
          </el-col>
      </el-row>
      <el-row>
          <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
            <el-button type="primary" @click="deleteByBatch">删除</el-button>
          </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogdeletebatchNumberVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import {getExpressComanyAll,importExpressFreightMultiple,batchDeleteOtherExpress} from '@/api/express/express'
import {warehouselist} from "@/utils/tools";
import expressvolume from '@/views/express/expressevolume'
import expressclaim from '@/views/express/expresseclaim'
import expressfaraway from '@/views/express/expressefaraway'
import expressrefund from '@/views/express/expresserefund'
import expresstransfer from '@/views/express/expressetransfer'
import expressviolation from '@/views/express/expresseviolation'
import expresseother from '@/views/express/expresseother'
import MyConfirmButton from '@/components/my-confirm-button'
import container from '@/components/my-container/nofooter'

const startDate = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
  name: 'Roles',
  components: { container, MyConfirmButton,expressvolume,expressclaim,expressfaraway,expressrefund,expresstransfer,expressviolation,expresseother },
  data() {
    return {
      activeName: 'first',
      filter: {
        startTime: null,
        endTime: null,
        companyId:null,
        warehouse:null,
        timerange:null,
        timerange1:[startDate, endDate]
      },
      warehouselist:warehouselist,
      deletefilter:{companyId:null,batchnumber:null,aType:null},
      expresscompanylist: [],
      pageLoading: false,    
      dialogVisible: false,
      uploadLoading:false,
      importFilte:{},
      fileList:[],
      fileparm:{},
      dialogdeletebatchNumberVisible:false,
      tableKeyCol:{
        tableKey1:"tableKey1",
        tableKey2:"tableKey2",
        tableKey3:"tableKey3",
        tableKey4:"tableKey4",
        tableKey5:"tableKey5",
        tableKey6:"tableKey6",
        tableKey7:"tableKey7",
      }
    }
  },
  async mounted() {
    await this.getExpressComanyList();
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async getExpressComanyList() {
      if(this.warehouselist&&this.warehouselist.length>0)
      {
        if(!this.warehouselist.some(a => a.label ==="昌北"))
          this.warehouselist.push({ label: '昌北', value: 2 });
      }

      const res = await getExpressComanyAll({});
      if (!res?.success) {
        return;
      } 
      const data = res.data;
      this.expresscompanylist = data;
    },
    onSearch() {
       if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      if (this.filter.timerange1) {
        this.filter.startImpotTime = this.filter.timerange1[0];
        this.filter.endImpotTime = this.filter.timerange1[1];
      }
      if (this.activeName=='first') this.$refs.expvolume.onSearch();
      else if (this.activeName=='second') this.$refs.expclaim.onSearch();
      else if (this.activeName=='third') this.$refs.expviolation.onSearch();
      else if (this.activeName=='fourth') this.$refs.exprefund.onSearch();
      else if (this.activeName=='five') this.$refs.exptransfer.onSearch();
      else if (this.activeName=='six') this.$refs.expfaraway.onSearch();
      else if (this.activeName=='seven') this.$refs.expother.onSearch();
    },
    onDown() {
       var alink = document.createElement("a");
       alink.href =`../static/excel/financial/快递费其他收支模板.xlsx`;
       alink.click();
    },
    startImport(){
      this.dialogVisible=true;
    },
    //上传成功
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
        //状态码为200时则上传成功
      } else {
        //状态码不是200时上传失败 从列表中删除
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      if(!this.importFilte.companyId) {
           this.$message({message: "请选择快递公司！",type: "warning",});
            return;
        }
      else if(this.importFilte.warehouse==null) {
           this.$message({message: "请选择发货仓！",type: "warning",});
            return;
        }
      this.uploadLoading=true
      this.$refs.upload.submit();
    },
   async uploadFile(item) {
      const form = new FormData();
      form.append("companyid", this.importFilte.companyId);
      form.append("warehouse", this.importFilte.warehouse);
      form.append("upfile", item.file);
      var res= await importExpressFreightMultiple(form);
      if (res.code==1) this.$message({message: "上传成功,正在导入中...", type: "success" });
      else this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading=false
    },
  async showOnDelete(aType){
     this.deletefilter={companyId:null,batchnumber:null,aType:aType};
     this.dialogdeletebatchNumberVisible=true;
   },
  async deleteByBatch() {
      var res=await batchDeleteOtherExpress(this.deletefilter);
      if (res.code==1) {
        this.$message({message: "删除成功！", type: "success" });
        this.dialogdeletebatchNumberVisible=false;
      }
      else this.$message({ message: res.msg, type: "warning" });
    },
  }
}
</script>
