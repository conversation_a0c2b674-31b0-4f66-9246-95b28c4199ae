<template>
    <container style="height: 98%;">
        <template #header>
            <el-form :inline="true">
                <el-form-item label="责任人:">
                    <YhUserelector  :value.sync="filter.userId" :text.sync="filter.userName"></YhUserelector>
                </el-form-item>
                <el-form-item label="数据库:">
                    <el-select v-model="filter.db" placeholder="请选择数据库" clearable filterable >
                        <el-option v-for="item in dbList" :key="item" :label="item" :value="item">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="组:">
                    <el-input v-model="filter.owned" style="width:120px;" maxlength="100" clearable></el-input>
                </el-form-item>
                <el-form-item label="标题:">
                    <el-input v-model="filter.title" style="width:120px;" maxlength="100" clearable></el-input>
                </el-form-item>
                <el-form-item label="描述:">
                    <el-input v-model="filter.desc" style="width:120px;" maxlength="100" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="getList('Search')">查询</el-button>
                    <el-button type="primary" @click="openForm(true, null)">新增</el-button>
                </el-form-item>
            </el-form>
        </template>
        <vxetablebase ref="table" :id="'PatrolConfig202040907'" :that='that' :isIndex='true' @sortchange='sortchange'
            :isSelection='true' :hasexpand='true' :tableData='list' :tableCols='tableCols' :loading="loading" style="height: 100%">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
        <el-dialog :title="addOrEdit ? '新增配置' : '编辑配置'" :visible.sync="formVisible" v-dialogDrag width="60%">
            <el-form style="width: 90%;margin: 0 auto;margin-top: 30px;height: 500px;overflow: scroll;" v-if="formVisible">
                <el-form-item style="margin-left: 37px;">
                    <span style="margin-right: 30px;">组：<el-input v-model="form.owned" style="width: 200px;"></el-input></span>
                    <span style="margin-right: 30px;">标题：<el-input v-model="form.title" style="width: 200px;"></el-input></span>
                </el-form-item>
                <el-form-item style="margin-left:7px;">
                    <span style="margin-right: 30px;"> 数据库：<el-select v-model="form.db" placeholder="请选择数据库" style="width: 200px;" clearable filterable >
                        <el-option v-for="item in dbList" :key="item" :label="item" :value="item">
                        </el-option>
                    </el-select></span>
                    <span>
                        责任人：<YhUserelector  :value.sync="form.userId" :text.sync="form.userName"></YhUserelector>
                    </span>
                </el-form-item>
                <el-form-item label="Sql语句:">
                    <el-input type="textarea" v-model="form.sql" maxlength="4000" style="width: 600px;" show-word-limit :autosize="{ minRows: 5, maxRows: 10}"></el-input>
                </el-form-item>
                <el-form-item label="描述:" style="margin-left: 20px;">
                    <el-input type="textarea" v-model="form.desc" maxlength="200" show-word-limit style="width: 600px;" :autosize="{ minRows: 5, maxRows: 10}"></el-input>
                </el-form-item>
            </el-form>
            <div style="width: 80%;margin: 0 auto;text-align: right;margin-top: 30px;">
                <el-button type="primary" @click="submitForm">提交</el-button>
                <el-button type="primary" @click="formVisible = false">取消</el-button>
            </div>
        </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { QuerySql, InsertOrUpdateSql, DeleteSql } from '@/api/bookkeeper/DataPatrol';
import { getDbNames } from '@/api/bookkeeper/reporter'
import YhUserelector from '@/components/YhCom/yh-userselector.vue'

const tableCols = [
    { istrue: true, prop: 'owned', label: '组', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'title', label: '标题', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'userName', label: '责任人', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'db', label: '数据库', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'sql', label: 'Sql', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'desc', label: '描述', sortable: 'custom', align: 'center' },
    {
        istrue: true, type: "button", label: '操作', width: "120", btnList: [
            { label: "编辑", handle: (that, row) => that.openForm(false, row) },
            { label: "删除", handle: (that, row) => that.onDelete(row) },
        ]
    }
]

export default {
    name: 'PatrolConfig',
    components: { container, vxetablebase,YhUserelector },
    data() {
        return {
            that: this,
            filter: {
                owned: null,
                title: null,
                db: null,
                desc: null,
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
            form: {
                owned: null,
                title: null,
                db: null,
                sql: null,
                desc: null,
                userId:null,
                userName:null,
            },
            tableCols,
            list: [],
            loading: false,
            total: null,
            formVisible: false,
            dbList: null,
        };
    },

    async mounted() {
        const { data, success } = await getDbNames()
        if (success) {
            this.dbList = data
        } else {
            this.$message.error('获取数据库名称失败')
        }
        await this.getList();
    }, 
    methods: {
        //获取列表
        async getList(val) {
            if (val == "Search") {
                this.filter.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            const { success, data } = await QuerySql(this.filter);
            if (success) {
                this.list = data?.list;
                this.total = data?.total;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.filter.currentPage = 1;
            this.filter.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.filter.currentPage = val;
            this.getList()
        },
        //排序查询
        sortchange({ order, prop }) {
            if (prop) {
                this.filter.orderBy = prop
                this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        //打开表单
        openForm(addOrEdit, row) {
            this.addOrEdit = addOrEdit;
            this.form = addOrEdit ? {} :JSON.parse(JSON.stringify(row));
            this.formVisible = true;
        },
        async submitForm() {
            const { success } = await InsertOrUpdateSql(this.form);
            if (success) {
                this.formVisible = false;
                this.$message.success("数据更新成功")
                this.getList();
            }
        },
        onDelete(row) {
            this.$confirm('确定删除吗, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                .then(async () => {
                    const { success } = await DeleteSql(row)
                    if (success) {
                        this.$message({ type: 'success', message: '删除成功!' });
                        this.getList();
                    }
                }).catch(() => {
                    this.$message({ type: 'info', message: '已取消' });
                });
        },

    },
};
</script>

<style lang="scss" scoped>
</style>