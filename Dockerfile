#FROM node:12.19.1-alpine3.12 AS build
#FROM node:12.22.12 AS build
FROM node:bullseye AS build
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list
WORKDIR /app
COPY package*.json ./
COPY .env ./
RUN npm cache clean --force
RUN npm config set registry https://registry.npm.taobao.org
#RUN npm install -g npm@9.7.2
#RUN npm install
RUN npm install -g pnpm
RUN pnpm config set registry https://registry.npmmirror.com/
RUN pnpm i
COPY . .
#RUN npm i increase-memory-limit --save-dev
#RUN npm i cross-env --save-dev
#RUN npm install cross-env
#RUN npm install -g increase-memory-limit
#RUN increase-memory-limit
#RUN npm run fix-memory-limit
#RUN npm run build:prod
RUN pnpm run build:prov3

FROM nginx:alpine
ARG APP_DOMAIN
ENV APP_DOMAIN ${APP_DOMAIN}
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
RUN adduser -D -H -u 5000 -s /bin/sh www
RUN rm /etc/nginx/conf.d/default.conf
ADD scripts/nginx.conf /etc/nginx/
ADD scripts/webdev.conf /etc/nginx/conf.d/
ADD scripts/webpro.conf /etc/nginx/conf.d/
RUN mkdir -p /var/www
RUN rm -rf /var/www
#ADD dist/ /var/www
#VOLUME /var/www
#VOLUME /etc/nginx/conf.d
RUN mkdir -p /usr/local/nginx/html
RUN rm  -rf /usr/local/nginx/html/*
COPY --from=build /app/dist/ /var/www/

EXPOSE 80
CMD ["nginx"]

#  "scripts": {
#     "serve": "vue-cli-service serve",
#     "build": "vue-cli-service build",
#     "install:pkg": "npm install --registry https://registry.npm.taobao.org --legacy-peer-deps",
#     "lint": "vue-cli-service lint"
#     //"fix-memory-limit": "cross-env LIMIT=4096 increase-memory-limit"
#   },