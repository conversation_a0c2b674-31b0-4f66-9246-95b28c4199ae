<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent>
        <el-form-item label="时间:">
          <el-date-picker style="width:320px"
            v-model="Filter.timerange"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="从："
            end-placeholder="到："
            :picker-options="pickerOptions"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="汇总日期:">
        <el-switch :width="40" @change="summTypeChange"
          v-model="Filter.isSumm"
          inactive-color="#228B22"
          active-text="是"
          inactive-text="否">
        </el-switch>
        </el-form-item>
        <el-form-item label="平台:" label-position="right" label-width="72px">
                <el-select clearable filterable  v-model="Filter.platform" placeholder="请选择" class="el-select-content">
                  <el-option 
                    v-for="item in platformList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">             
                  </el-option>
                </el-select>
          </el-form-item>
        <el-form-item  label="所属店铺:" label-position="right" label-width="72px">
                <el-select clearable filterable v-model="Filter.idShop" placeholder="请选择" class="el-select-content">
                <el-option 
                  v-for="item in shopList"
                  :key="item.id"
                  :label="item.shopName"
                  :value="item.id">             
                </el-option>
              </el-select>
        </el-form-item>
        <el-form-item label="组长:" label-position="right" label-width="72px">
                <el-select clearable filterable  v-model="Filter.idGroup" placeholder="请选择" class="el-select-content">
                  <el-option 
                  v-for="item in groupList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key">             
                  </el-option>
                </el-select>
        </el-form-item>
        <el-form-item  label="运营专员:" label-position="right" label-width="72px">
                <el-select filterable clearable v-model="Filter.idOperateSpecial" placeholder="请选择" class="el-select-content">
                  <el-option 
                  v-for="item in userList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key">             
                  </el-option>
                </el-select>
          </el-form-item>
            <el-form-item prop="parentIds" label="类目:">
          <el-cascader 
            v-model="categoryids"  :options="categorylist" :props="{ checkStrictly: true, value: 'id' }"
            filterable  style="width:100%;"/>
        </el-form-item>
           <el-form-item label="产品id" label-position="right" label-width="72px">
              <el-input v-model="Filter.idProduct" style="width:200px;"/>
          </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' 
              :hasexpand='false' @sortchange='sortchange' :tableData='ProfitList' 
              @select='selectchange' :isSelection='false'  @cellclick='cellclick'
         :tableCols='tableCols' :loading="listLoading" :summaryarry='summaryarry' style="height:800px">
      <el-table-column type="expand">
        <template slot-scope="props">
        <div>
          <el-table :data="props.row.detaildata" style="width: 100%">
            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
            </el-table-column>
          </el-table>
        </div>
      </template>
      </el-table-column>
       <template slot='extentbtn'>
          <el-button-group>            
            <el-button type="primary" @click="onRefresh">刷新</el-button>
          </el-button-group>
        </template>
    </ces-table>    
    <el-popover
      placement="bottom-end"
      :width="600"
      :height="700"
      v-model="visiblepopover"
      :reference="prevTarget"
      :key="popperFlag"
      >
      <el-row>
          <el-table ref="chatTable" :data="chatList" :border=true style="width: 100%;">
              <el-table-column type="index" :fixed=true>
              </el-table-column>
              <template v-for="(item, index) in chatTableCols">
          <el-table-column :sortable ="(item.sortable != null)"  show-overflow-tooltip
          :key="index"
          :prop="item.prop" 
          :label="item.label"
          :width="item.width"
          align="left">
            <template slot-scope="scope" >
                    <span v-if="item.type==='html'" v-html="(item.formatter && item.formatter(scope.row))"></span>
                    <span v-if="item.type==='format'">{{  }} </span>
                        <!-- 默认 -->
                    <span v-if="!item.type" 
                          :style="item.itemStyle && item.itemStyle(scope.row)" 
                          :class="item.itemClass && item.item.itemClass(scope.row)">{{(item.formatter && item.formatter(scope.row)) || scope.row[item.prop]}}</span>
            </template>
          </el-table-column>
        </template>
          </el-table>
      </el-row>
    </el-popover>
    <!--分页-->
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getProfitList"
      />
    </template>  
  </my-container>
</template>
<script>
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList,
         getDirectorGroupList as getDirectorGroupList,
         getDirectorList as getDirectorList
       } from '@/api/operatemanage/base/shop';
import { formatPlatform,formatLink} from "@/utils/tools";
import { TimelineComponent } from 'echarts/components';
import {getPageList, getChatList} from '@/api/olderp/profit'
import { getList as getcategorylist } from '@/api/operatemanage/base/category'
import { treeToList, listToTree, getTreeParents } from '@/utils'

const tableCols =[
      //{istrue:true,prop:'id',label:'编号', width:'200',sortable:'custom'},
      {istrue:true,prop:'date',label:'日期', width:'100',sortable:'custom'},
      {istrue:true,prop:'platform',label:'平台', width:'50', formatter:(row)=>formatPlatform(row.platform)},
      {istrue:true,prop:'nameShop',label:'店铺', width:'80',},
      {istrue:true,prop:'nameAllProductCategory',label:'类目', width:'80',},
      {istrue:true,prop:'idProduct',label:'产品id', width:'80', sortable:'custom'},
      {istrue:true,prop:'nameProduct',label:'产品名称', width:'80',type:'html', formatter:(row)=>{
            var  proBaseUrl="https://detail.tmall.com/item.htm?id="+row.idProduct;
            return formatLink(row.nameProduct,proBaseUrl);
      }},
      {istrue:true,prop:'nameGroup',label:'小组', width:'50',},
      {istrue:true,prop:'nameOperator',label:'运营专员', width:'50',},
      {istrue:true,prop:'nameUser1',label:'运营助理', width:'50',},
      {istrue:true,prop:'nameUser2',label:'车手', width:'50',},
      {istrue:true,prop:'nameUser3',label:'备用', width:'50',},
      {istrue:true,prop:'nameStatus',label:'状态', width:'50',},
     
      //客服
       {istrue:true,prop:'countReceive',label:'接待量', width:'50', sortable:'custom'},
      {istrue:true,prop:'countReceiveDeal',label:'成交', width:'50', sortable:'custom'},
      {istrue:true,prop:'countReceiveNotDeal',label:'未成交', width:'50', sortable:'custom'},
      {istrue:true,prop:'rateDealConversion',label:'成交转化率', width:'50', sortable:'custom', type:'html',formatter:(row)=>{
        var html = '<span style="color:#0000FF">' + (row.rateDealConversion?((row.rateDealConversion*100).toFixed(2) + "%"):"") + '</span>'
        return html }},
      {istrue:true,prop:'countOrder',label:'订单量', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountSale',label:'销售金额', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountSale?.toFixed(2)}},
      {istrue:true,prop:'amountSaleCost',label:'销售成本', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountSaleCost?.toFixed(2)}},
      {istrue:true,prop:'amountGrossProfit',label:'订单毛利', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountGrossProfit?.toFixed(2)}},
      {istrue:true,prop:'rateROIAll',label:'总ROI', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.rateROIAll?.toFixed(2)}},
      {istrue:true,prop:'amountService',label:'服务费', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountService?.toFixed(2)}},
      {istrue:true,prop:'amountCostZTC',label:'直通车花费', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountCostZTC?.toFixed(2)}},
      {istrue:true,prop:'amountCostSuperRecomm',label:'超级推荐花费', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountCostSuperRecomm?.toFixed(2)}},
      {istrue:true,prop:'amountAdvAll',label:'总广告费', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountAdvAll?.toFixed(2)}},
      {istrue:true,prop:'rateAdv',label:'广告占比', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.rateAdv?((row.rateAdv*100).toFixed(2) + "%"):""}},
      {istrue:true,prop:'amountDealZTC',label:'直通车成交金额', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountDealZTC?.toFixed(2)}},
      {istrue:true,prop:'amountDealSuperRecomm',label:'超级推荐成交金额', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountDealSuperRecomm?.toFixed(2)}},
      {istrue:true,prop:'amountDealAll',label:'总成交', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountDealAll?.toFixed(2)}},
      {istrue:true,prop:'rateROIZTC',label:'直通车ROI', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.rateROIZTC?.toFixed(2)}},
      {istrue:true,prop:'rateROISuperRecomm',label:'超级推荐ROI', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.rateROISuperRecomm?.toFixed(2)}},
      {istrue:true,prop:'amountTaoBaoKe',label:'淘宝客', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountTaoBaoKe?.toFixed(2)}},
      {istrue:true,prop:'rateService',label:'服务费比率', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.rateService?.toFixed(4)}},
      {istrue:true,prop:'amountPackage',label:'包装材料', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountPackage?.toFixed(2)}},
      {istrue:true,prop:'amountExpress',label:'快递费', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountExpress?.toFixed(2)}},
      {istrue:true,prop:'amountWarehouse',label:'仓库工资', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountWarehouse?.toFixed(2)}},
      {istrue:true,prop:'amountProfit',label:'利润', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountProfit?.toFixed(2)}},
      {istrue:true,prop:'rateProfit',label:'利润率', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.rateProfit?((row.rateProfit*100).toFixed(2) + "%"):""}},
      {istrue:true,prop:'amountPromotion',label:'推广盈亏', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountPromotion?.toFixed(2)}},
      //{istrue:true,prop:'countOrder',label:'订单量', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.rateClick?.toFixed(2)+'%'}},
      //{istrue:true,prop:'amountClickAvg',label:'平均点击花费', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountClickAvg?.toFixed(2)}},
      {istrue:true,prop:'idProduct',label:'产品id2', width:'80', sortable:'custom'},
      {istrue:true,prop:'nameProduct',label:'产品名称2', width:'80',type:'html', formatter:(row)=>{
            var  proBaseUrl="https://detail.tmall.com/item.htm?id="+row.idProduct;
            return formatLink(row.nameProduct,proBaseUrl);
      }},
     ];

      //客服
const chatTableCols =[
      {istrue:true,prop:'nameServer',label:'客服', width:'100'},
      {istrue:true,prop:'countReceive',label:'接待量', width:'100', sortable:'custom'},
      {istrue:true,prop:'countReceiveDeal',label:'成交', width:'100', sortable:'custom'},
      {istrue:true,prop:'countReceiveNotDeal',label:'未成交', width:'100', sortable:'custom'},
      {istrue:true,prop:'rateDealConversion',label:'成交转化率', width:'100', sortable:'custom', type:'html',formatter:(row)=>{
        var html = row.rateDealConversion?((row.rateDealConversion*100).toFixed(2) + "%"):""
        return html }},
     ];

export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
  data() {
    return {
      that:this,
      Filter: {
        timerange: [formatTime(dayjs().startOf("month"), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")],
        
      },
      categoryids:[],
      shopList:[],
      groupList:[],
      userList:[],
      categorylist:[],
      ProfitList: [],
      platformList:[{id:1, name:"淘系"}, {id:2, name:"拼多多"}],
      tableCols:tableCols,
      chatTableCols:chatTableCols,
      total: 0,
      summaryarry:{countOrder_sum:10},
      pager:{OrderBy:"date",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      //
      pickerOptions:{
        shortcuts: [{
            text: '今天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date().setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '昨天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 1).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '3天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 2).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '7天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 6).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '15天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 14).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '30天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 29).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }]
      },
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
      prevTarget:null,
      visiblepopover:false,
      popperFlag:false,
      chatList:[]
    };
  },
  async mounted() {
     await this.getShopList()
     await this.getGroupList()
     await this.getUserList()
     await this.getcategorylist(1)
  },
  methods: {
    async getcategorylist(platform) {
      const res = await getcategorylist({platform:platform })
      if (!res?.code) {
        return
      }
      const list=[];
      res.data.forEach(f=>{
         f.label=f.categoryName;
         list.push(f)
      })

      this.categorylist = listToTree(_.cloneDeep(list), {
        id: '',
        parentId: '',
        label: '所有'
      })
    },
    async cellclick(row, column, cell, event){
      if(column.property == 'rateDealConversion')
      {
        let currentTarget = event.target
        if(currentTarget === this.prevTarget)
        {
           this.prevTarget = null
           this.visiblepopover = false
           return
        }

        this.visiblepopover = false
        this.prevTarget = null
        this.popperFlag = !this.popperFlag

        this.$nextTick( async () => {
             this.prevTarget = currentTarget
             this.visiblepopover = true
             this.Filter.row = row
             console.log(row.idProduct)

              const para = {...this.Filter};
              if (this.Filter.timerange) {
                para.dateStart = this.Filter.timerange[0];
                para.dateEnd = this.Filter.timerange[1];
              }

              if(!(para.dateStart&&para.dateEnd)){
                this.$message({message:"请先选择日期！",type:"warning"});
                return;
              }

               const params = {
                        ...para,
                      };

              console.log(params)
              const res = await getChatList(params);
              console.log(res.data)
              this.chatList = res.data;

          }
        )

      }

      return
    },
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
     summTypeChange:function(val){
        console.log(val)
    },
  //所属店铺列表
    async getShopList(){
      const res1 = await getAllShopList();
      this.shopList=res1.data;
      return;
    },
     //组长列表
    async getGroupList(){
      const res = await getDirectorGroupList();
      this.groupList = res.data;
      return;
    },
     //负责人列表
    async getUserList(){
      const res = await getDirectorList();
      this.userList = res.data;
      return;
    },
    onRefresh(){
        this.onSearch()
    },
    onSearch(){
       this.$refs.pager.setPage(1);
       this.getProfitList();
    },
    async getProfitList(){

      if(this.categoryids != null && this.categoryids.length > 0)
      {
        this.Filter.idProductCategory = this.categoryids[this.categoryids.length-1]
      }

      const para = {...this.Filter};
      if (this.Filter.timerange) {
        para.dateStart = this.Filter.timerange[0];
        para.dateEnd = this.Filter.timerange[1];
      }

      if(!(para.dateStart&&para.dateEnd)){
        this.$message({message:"请先选择日期！",type:"warning"});
        return;
      }
      
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };

      console.log(para)

      this.listLoading = true;
      const res = await getPageList(params);
      this.listLoading = false;
      console.log(res.data.list)
      console.log(res.data.summary)

      this.total = res.data.total
      this.ProfitList = res.data.list;
      this.summaryarry=res.data.summary;
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>