<template>
    <my-container v-loading="pageLoading" style="height: 100%;">
        <template #header>
            <span style="font-size:14px; color:#303133">导入时间：</span>
            <el-button style="padding: 0;margin:0px 10px 0px 0px;">
                <el-date-picker style="width:130px" v-model="filter.importTime" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="导入时间" clearable></el-date-picker>
            </el-button>
            <el-button style="padding: 0;margin: 0px 10px 0px 0px;">
                <el-select v-model="filter.warehouse" placeholder="选择发货仓" @change="findMainData()" clearable style="width:120px">
                    <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0px 10px 0px 0px;">
                <el-date-picker style="width:260px" v-model="filter.stopSendTimerange" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="停发开始时间" end-placeholder="停发结束时间" clearable></el-date-picker>
            </el-button>
            <el-button style="padding: 0;margin: 0px 10px 0px 0px;">
                <el-input style="width:88px" v-model="filter.province" v-model.trim="filter.province" placeholder="省" clearable></el-input>
            </el-button>
            <el-button style="padding: 0;margin: 0px 10px 0px 0px;">
                <el-input style="width:88px" v-model="filter.city" v-model.trim="filter.city" placeholder="市" clearable></el-input>
            </el-button>
            <el-button style="padding: 0;margin:0px 10px 0px 0px;">
                <el-input style="width:88px" v-model="filter.area" v-model.trim="filter.area" placeholder="区" clearable></el-input>
            </el-button>
            <el-button style="padding: 0;margin: 0px 10px 0px 0px;">
                <el-input style="width:120px" v-model="filter.street" v-model.trim="filter.street" placeholder="街道" clearable></el-input>
            </el-button>
            <el-button-group>
                <el-button type="primary" @click="onStopSearch">查询</el-button>
                <el-button type="primary" @click="startImport" v-if="checkPermission(['api:order:CovidStopSend:ImportExpressStopSendAsync'])">导入停发快递公司</el-button>
                <el-button type="primary" @click="downloadTemplate">下载导入模板</el-button>
                <el-button type="primary" @click="onExport" v-if="checkPermission(['api:order:CovidStopSend:ExportStopExpressDataAsync'])">导出</el-button>
            </el-button-group>
        </template>
        <el-row style="height: 100%;">
            <el-col :span="7" style="height: 100%;">
                <expressStopSendPage @changeDetailData="changeDetailData" ref="main" :filter="filter"></expressStopSendPage>
            </el-col>
            <el-col :span="17" style="height: 100%;">
                <expressStopSendPageDetail ref="detail" :filter="filter"></expressStopSendPageDetail>
            </el-col>
        </el-row>
        <template #footer>
        </template>
        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%">
            <span v-loading="importLoading">
                <el-button style="padding: 0;margin: 0;">
                    <el-input style="width:240px" v-model="importExpressName" v-model.trim="importExpressName" placeholder="快递公司名称" clearable></el-input>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-select v-model="importWarehouse" placeholder="选择导入发货仓" required="true">
                        <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-button>
                <el-upload ref="upload" class="upload-demo" :on-change="fileChange" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx" :on-success="uploadSuccess" :http-request="uploadFile" :file-list="fileList">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传</el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
    import { formatTime } from "@/utils";
    import MyContainer from "@/components/my-container";
    import expressStopSendPage from '@/views/order/covid/ExpressStopSendPage'
    import expressStopSendPageDetail from '@/views/order/covid/ExpressStopSendPageDetail'
    import { importExpressStopSendAsync, exportStopExpressDataAsync } from "@/api/order/covidStopSendPage";
    export default {
        name: 'expressStopSendPageIndex',
        components: { MyContainer, expressStopSendPage, expressStopSendPageDetail },
        props: {

        },
        data () {
            return {
                dialogVisible: false,
                filter: {
                    warehouse: "",
                    stopSendTimerange: [],
                    startTime: "",
                    endTime: "",
                    expressId: 0,
                    importTime: formatTime(new Date(), "YYYY-MM-DD"),
                },
                warehouselist: [{ label: "南昌", value: "南昌" }, { label: "义乌", value: "义乌" }],
                importExpressName: "",
                importWarehouse: "",
                importLoading: false,
                pageLoading: false,
                fileList: [],
            }
        },
        async mounted () {

        },
        methods: {
            //下载导入模板
            downloadTemplate () {
                window.open("../../static/excel/order/停发快递公司导入模板.xlsx", "_self");
            },
            //开始导入
            startImport () {
                this.dialogVisible = true;
            },
            //取消导入
            cancelImport () {
                this.dialogVisible = false;
                this.fileList = [];
                this.importExpressName = "";
                this.importWarehouse = "";
            },
            fileChange (file, fileList) {
                if (file.status == "ready") {
                    var fileName = file.name.substring(0, file.name.lastIndexOf("."))
                    this.importExpressName = fileName;
                }
                if (file.status == "success") {
                    this.importExpressName = "";
                }
            },
            //上传成功
            uploadSuccess (response, file, fileList) {
                this.fileList.splice(this.fileList.indexOf(file), 1);
                this.dialogVisible = false;
                this.fileList = [];
                this.importExpressName = "";
                this.importWarehouse = "";
            },
            //提交导入
            submitUpload () {
                if (this.importExpressName == "") {
                    this.$message({ message: '请输入快递公司名称！', type: 'error' })
                    return;
                }
                if (this.importWarehouse == "") {
                    this.$message({ message: '请选择发货仓！', type: 'error' })
                    return;
                }
                this.$refs.upload.submit();
            },
            //上传文件
            async uploadFile (item) {
                const form = new FormData();
                form.append("token", this.token);
                form.append("upfile", item.file);
                form.append("expressName", this.importExpressName);
                form.append("warehouse", this.importWarehouse);
                this.importLoading = true;
                const res = await importExpressStopSendAsync(form);
                this.importLoading = false;
                if (res?.success) {
                    this.$message({ message: '上传成功,正在导入中...', type: "success" });
                }
            },
            //导出
            async onExport () {
                if (this.filter.stopSendTimerange && this.filter.stopSendTimerange.length > 1) {
                    this.filter.startTime = this.filter.stopSendTimerange[0];
                    this.filter.endTime = this.filter.stopSendTimerange[1];
                } else {
                    this.filter.startTime = null;
                    this.filter.endTime = null;
                }
                const params = {
                    ... this.filter
                }
                if (params === false) {
                    return;
                }
                var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
                var res = await exportStopExpressDataAsync(params);
                loadingInstance.close();
                if (!res?.data) return
                var fileName = res.headers['content-disposition'].split("=")[2];
                fileName = decodeURIComponent(fileName.split("'")[2]);
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', fileName);
                aLink.click();
            },
            async findMainData () {
                await this.changeDetailData(0);
                await this.$refs.main.getlist();
            },
            async changeDetailData (expressId) {
                this.filter.expressId = expressId;
                await this.$refs.detail.getlist();
            },
            async onStopSearch () {
                if (this.filter.expressId) {
                    this.changeDetailData(this.filter.expressId);
                }
                // else {
                //     let ml = this.$refs.main.list;
                //     if (ml && ml.length > 0) {
                //         this.changeDetailData(ml[0].id);
                //     }
                // }
            },
        }
    }
</script>
