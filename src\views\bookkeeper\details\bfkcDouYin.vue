<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="年月:">
                    <el-date-picker style="width: 110px" v-model="filter.yearMonth" type="month" format="yyyyMM"
                        value-format="yyyyMM" placeholder="选择月份">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="平台:">
                    <el-select filterable v-model="filter.platform" placeholder="请选择平台" disabled clearable
                        style="width:110px">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="所属店铺:" label-position="right">
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺" style="width: 180px">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="店铺ID:" label-position="right">
                    <el-input v-model="filter.shopIdImp" placeholder="店铺ID" style="width:160px;" />
                </el-form-item>
                <el-form-item label="订单号:" label-position="right">
                    <el-input v-model="filter.orderNumber" placeholder="订单号" style="width:160px;" />
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onSumExport" :loading="dialogShopSumLoading">汇总导出</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='ZTCKeyWordList' :showsummary='true' :summaryarry='summaryarry' @select='selectchange'
            :isSelection='false' :tableCols='tableCols' :loading="listLoading" :isSelectColumn='false'>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList, getList as getshopList } from '@/api/operatemanage/base/shop';
import { formatPlatform, formatLink, platformlist } from "@/utils/tools";
import { GetOtherDeduction_DY_Bfkc, ExportOtherDeduction_DY_Bfkc } from '@/api/monthbookkeeper/financialDetail'
const tableCols = [
    { istrue: true, prop: 'yearMonth', label: '年月', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'insureNo', label: '投保单号', sortable: 'custom', width: '200', },
    { istrue: true, prop: 'orderNumber', label: '订单号', sortable: 'custom', width: '180' },
    { istrue: true, prop: 'timeOfOrder', label: '下单时间', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'timeOfAccount', label: '动账时间', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'mobileAccountSerialNumber', label: '动帐流水号', sortable: 'custom', width: '180' },
    { istrue: true, prop: 'insureName', label: '保险名称', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'insureFee', label: '支付保费', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'shopNameImp', label: '店铺', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'shopIdImp', label: '店铺ID', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'remark', label: '备注', sortable: 'custom', width: '200' },
];
export default {
    name: "bfkcDouYin",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
    data() {
        return {
            that: this,
            filter: {
                platform: 6,
                yearMonth: null,
                shopCode: null,
            },
            shopList: [],
            userList: [],
            groupList: [],
            platformlist: platformlist,
            ZTCKeyWordList: [],
            tableCols: tableCols,
            summaryarry: {},
            total: 0,
            pager: { OrderBy: "id", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            dialogShopSumLoading: false,
        };
    },
    async mounted() {
        this.onchangeplatform();
    },
    methods: {
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async onchangeplatform() {
            const res1 = await getshopList({ platform: 6, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
        },
        // async getShopList(){
        //   const res1 = await getAllShopList();
        //   this.shopList=[];
        //     res1.data?.forEach(f => {
        //       if(f.isCalcSettlement&&f.shopCode)
        //           this.shopList.push(f);
        //     });
        // },
        onRefresh() {
            this.onSearch()
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getList();
        },
        async getList() {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter,
            };
            this.listLoading = true;
            const res = await GetOtherDeduction_DY_Bfkc(params);
            this.listLoading = false;
            this.total = res.data?.total
            this.ZTCKeyWordList = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },

        async onSumExport(curpage) {
            if (!this.filter.yearMonth) {
                this.$message({ message: "请输入年月", type: "warning" });
                return
            }
            this.$confirm('确认要导出吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...this.filter,
                };
                console.log(params, "params");
                var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
                this.dialogShopSumLoading = true;
                var res = await ExportOtherDeduction_DY_Bfkc(params);
                this.dialogShopSumLoading = false;
                loadingInstance.close();
                if (!res?.data) {
                    this.$message({ message: "没有数据", type: "warning" });
                    return
                }
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '抖音保费扣除店铺汇总_' + new Date().toLocaleString() + '.xlsx')
                aLink.click();
            }).catch(() => {
            });
        }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
