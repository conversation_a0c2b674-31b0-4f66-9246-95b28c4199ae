<template>
    <my-container>
      <template #header>
        <div class="top">
          <el-date-picker style="width: 240px" v-model="timeRange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" 
            range-separator="至" start-placeholder="起始日期" end-placeholder="结束日期" :picker-options="pickerOptions" clearable >
          </el-date-picker>
          <el-input v-model="filter.productID" v-model.trim="filter.productID" placeholder="商品ID(多个ID，隔开)" style="width:180px;" clearable ></el-input>
          <el-select v-model="filter.groupName" placeholder="运营组" multiple clearable collapse-tags filterable style="width:200px;" >
            <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.label"></el-option>
          </el-select>
          <el-input v-model="filter.cannonFodderID" v-model.trim="filter.cannonFodderID" placeholder="炮灰ID(多个ID，隔开)" style="width:180px;" clearable ></el-input>
          <el-select v-model="filter.progress" placeholder="进度" clearable filterable style="width:200px;" >
            <el-option label="已下单" value="已下单"></el-option>
            <el-option label="未下单" value="未下单"></el-option>
          </el-select>
          <el-select v-model="filter.isTakeDown" placeholder="炮灰是否下架" clearable filterable style="width:200px;" >
            <el-option label="是" value="是"></el-option>
            <el-option label="否" value="否"></el-option>
          </el-select>
          <el-select v-model="filter.shop" placeholder="店铺" multiple clearable collapse-tags filterable style="width:200px;" >
            <el-option v-for="item in shopList" :key="item" :label="item" :value="item"></el-option>
          </el-select>
          <el-select v-model="filter.claimUser" placeholder="认领人" multiple clearable collapse-tags filterable style="width:200px;" >
            <el-option label="空白" value=null></el-option>
            <el-option v-for="item in claimUserList" :key="item" :label="item" :value="item"></el-option>
          </el-select>
          <el-select v-model="filter.createUser" placeholder="创建人" multiple clearable collapse-tags filterable style="width:200px;" >
            <el-option v-for="item in createUserList" :key="item" :label="item" :value="item"></el-option>
          </el-select>
          <el-date-picker style="width: 240px" v-model="finishTimeRange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" 
            range-separator="至" start-placeholder="起始完成日期" end-placeholder="结束完成日期" :picker-options="pickerOptions" clearable >
          </el-date-picker>
        </div>
        <div class="top" style="justify-content: flex-end; margin-right: 20px;">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport">导出</el-button>
          <el-button type="primary" @click="onBatchClaim">批量认领</el-button>
          <el-button type="primary" @click="onCreateBrushOrder">创建刷单</el-button>
          <el-button type="primary" @click="onFinishBrushOrderList">批量完成</el-button>
          <el-button type="primary" @click="onDeleteBrushOrderList" v-if="checkPermission('api:operatemanage:operate:batchdeletebrushorder')">批量删除</el-button>
        </div>
      </template>

      <vxetablebase :id="'BrushOrderProcess202410081315'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' 
        @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" 
        :summaryarry="summaryarry" :showsummary='true' @select="selectchange" style="width: 100%;margin: 0" v-loading="listLoading" 
        :height="'100%'" >
      </vxetablebase>
      
      <!--分页-->
      <template #footer>
          <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template>

      <el-dialog title="创建刷单" :visible.sync="createVisable" width="420px" height="40%" @closeDialog="closeCreateDialog" 
        v-dialogDrag @close="createCancle">
        <el-form ref="formCreate" :model="formCreate" label-width="100px" :rules="rules" >
          <el-form-item label="店铺名称：" prop="shop">
            <el-select v-model="formCreate.shop" clearable filterable style="width:280px;" >
              <el-option v-for="item in shopList" :key="item" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="宝贝ID：" prop="productID">
            <el-input v-model="formCreate.productID" placeholder="宝贝ID(使用,，、。；; 分隔)" v-model.trim="formCreate.productID" type="textarea" :autosize="{ minRows: 2, maxRows: 20}">
            </el-input>
          </el-form-item>
          <el-form-item label="单量：" prop="changeedOrderAmount">
            <el-input v-model="formCreate.changeedOrderAmount" maxlength="10" @input="changeedOrderAmountInput" ></el-input>
          </el-form-item>
        </el-form>
        <div style="text-align: center;">
          <el-button type="primary" @click="createCancle">取消</el-button>
          <el-button type="primary" @click="createSubmit">确认</el-button>
        </div>
      </el-dialog>

      <el-dialog title="完成刷单" :visible.sync="finishVisable" width="420px" height="40%" @closeDialog="closeFinishDialog" 
        v-dialogDrag @close="finishCancle">
        <el-form ref="formFinish" :model="formFinish" label-width="130px" :rules="rules" >
          <el-form-item label="炮灰ID：" prop="cannonFodderID">
            <el-input v-model="formFinish.cannonFodderID" v-model.trim="formFinish.cannonFodderID" ></el-input>
          </el-form-item>
          <el-form-item label="进度：" prop="progress">
            <el-select v-model="formFinish.progress" clearable filterable style="width:250px;" >
              <el-option label="已下单" value="已下单"></el-option>
              <el-option label="未下单" value="未下单"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="炮灰是否下架：" prop="isTakeDown">
            <el-select v-model="formFinish.isTakeDown" clearable filterable style="width:250px;" >
              <el-option label="是" value="是"></el-option>
              <el-option label="否" value="否"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注：">
            <el-input v-model="formFinish.remark"></el-input>
          </el-form-item>
        </el-form>
        <div style="text-align: center;">
          <el-button type="primary" @click="finishCancle">取消</el-button>
          <el-button type="primary" @click="finishSubmit">确认</el-button>
        </div>
      </el-dialog>

    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import inputYunhan from '@/components/Comm/inputYunhan.vue';
import { getBrushOrderProcess, exportBrushOrderProcess, batchClaimOrder, createBrushOrder, 
    batchFinishBrushOrder, deleteBrushOrder, finishBrushOrder, getClaimUserList, getCreateUserList, batchDeleteBrushOrder } from "@/api/operatemanage/OperationalMiddleOfficeManage/BrushOrderProcess";
import { getDirectorGroupList } from '@/api/operatemanage/base/shop';
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import { formatLinkProCode } from "@/utils/tools";

const tableCols = [
  { istrue: true, width: '60', align: 'center', type: "checkbox" },
  { sortable: 'custom', iistrue: true, width: '100', align: 'center', label: '商品ID', prop: 'productID', type: 'html', formatter: (row) => formatLinkProCode(2, row.productID)  },
  { sortable: 'custom', istrue: true, width: '145', align: 'center', label: '店铺', prop: 'shop' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '小组', prop: 'groupName' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '炮灰ID', prop: 'cannonFodderID' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '改成单量', prop: 'changeedOrderAmount' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '创建人', prop: 'createUser' },
  { sortable: 'custom', istrue: true, width: '140', align: 'center', label: '创建时间', prop: 'createTime' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '认领人', prop: 'claimUser' },
  { sortable: 'custom', istrue: true, width: '140', align: 'center', label: '认领时间', prop: 'claimTime' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '进度', prop: 'progress' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '炮灰是否下架', prop: 'isTakeDown' },
  { sortable: 'custom', istrue: true, width: '140', align: 'center', label: '完成时间', prop: 'finishTime' },
  { sortable: 'custom', istrue: true, width: '125', align: 'center', label: '备注', prop: 'remark' },
  {
      istrue: true, type: 'button', width: '100', label: '操作', btnList: [
      { label: "删除", handle: (that, row) => that.onDelete(row) },
      { label: "完成", handle: (that, row) => that.onFinish(row) }
      ]
  },
];
  
  export default {
    name: "BrushOrderProcess",
    components: { MyContainer, vxetablebase, inputYunhan, getBrushOrderProcess, exportBrushOrderProcess, batchClaimOrder,
        createBrushOrder, batchFinishBrushOrder,deleteBrushOrder,  finishBrushOrder, getClaimUserList, getCreateUserList },
    data() {
      return {
        //行数据
        row: {
          mesgID: null,//主键ID
          productID: null,//商品ID
          shop: null,//店铺
          groupName: null,//小组
          cannonFodderID: null,//炮灰ID
          changeedOrderAmount: null,//改成单量
          createUser: null,//创建人
          createTime: null,//创建时间
          claimUser: null,//认领人
          claimTime: null,//认领时间
          progress: null,//进度
          isTakeDown: null,//炮灰是否下架
          remark: null,//备注
        },
        selectList: [],//复选框选中数据
        that: this,
        listLoading: false,//查询过程
        createLoading: false,//创建过程
        createVisable: false,//创建弹窗
        finishLoading: false,//完成过程
        finishVisable: false,//完成弹窗
        timeRange: [],//日期
        finishTimeRange: [],//完成日期
        groupList: [],//运营组列表
        shopList: [],//店铺列表
        claimUserList: [],//认领人列表
        createUserList: [],//创建人列表
        //过滤条件
        filter: {
          currentPage: 1,
          pageSize: 50,
          orderBy: 'createTime',
          isAsc: false,
          //过滤条件
          startDate: null,//开始日期
          endDate: null,//结束日期
          productID: null,//商品ID
          groupName: [],//店铺对应运营组
          cannonFodderID: null,//炮灰ID
          progress: null,//进度
          shop: [],//店铺
          isTakeDown: null,//炮灰是否下架
          claimUser: [],//认领人
          createUser: [],//创建人
          startFinishDate: null,//开始完成时间
          endFinishDate: null,//结束完成时间
        },
        //创建刷单
        formCreate: {
          shop: null,//店铺名称
          productID: null,//宝贝ID
          changeedOrderAmount: null,//改成单量
        },
        //完成刷单
        formFinish: {
          mesgID: null,//雪花ID
          cannonFodderID: null,//炮灰ID
          progress: "已下单",//进度
          isTakeDown: null,//炮灰是否下架
          remark: null,//备注
          mesgIDList: [],//批量雪花ID
        },
        tableCols: tableCols,
        tableData: [],
        summaryarry: {},
        total: 0,
        pager: { orderBy: "productID", isAsc: false },
        pickerOptions: {
          shortcuts: [{
          text: '前一天',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
              end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
              picker.$emit('pick', [start, end]);
            }
          }, {
          text: '近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              end.setTime(end.getTime());
              picker.$emit('pick', [start, end]);
            }
          }, {
          text: '近一个月',
            onClick(picker) {
              const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
              const date2 = new Date(); date2.setDate(date2.getDate());
              picker.$emit('pick', [date1, date2]);
            }
          }]
        },
        rules: {
          shop: [
            { required: true, message: '请选择店铺名称', trigger: 'change' }
          ],
          productID: [
            { required: true, message: '请输入宝贝ID', trigger: 'change' }
          ],
          changeedOrderAmount: [
            { required: true, message: '请输入单量', trigger: 'blur' },
            { min: 1, max: 10, message: '长度在 1 到 10 个字符', trigger: 'blur' }
          ],
          cannonFodderID: [
            { required: true, message: '请输入炮灰ID', trigger: 'change' },
          ],
          progress: [
            { required: true, message: '请选择进度', trigger: 'change' },
          ],
          isTakeDown: [
            { required: true, message: '请选择是否下架炮灰', trigger: 'change' },
          ],
        },
      }
    },
    async mounted() {
      this.init();
      this.getList();
    },
    methods: {
      async init() {
        //默认当天日期
        let end = new Date();
        let start = new Date();
        start.setTime(start.getTime());
        end.setTime(end.getTime());
        this.timeRange = [start, end];
        this.filter.startDate = start;
        this.filter.endDate = end;
        // this.finishTimeRange = [start, end];
        // this.filter.startFinishDate = start;
        // this.filter.endFinishDate = end;

        //获取店铺列表
        const shopName  = await getshopList({ platform: null, CurrentPage: 1, PageSize: 100000 });
        this.shopList = Array.from(
            new Set(
            shopName.data.list
            .map(x => x.shopName)
            .filter(name => name.includes('拼多多-'))
            )
        );//去重，取拼多多店铺

        //获取运营组列表
        const groupName = await getDirectorGroupList({})
        this.groupList = groupName.data.map(item => { return { value: item.key, label: item.value } });

        var { data } = await getClaimUserList()
        this.claimUserList = data;
        var { data } = await getCreateUserList();
        this.createUserList = data;
      },
      // 排序查询
      sortchange(column) {
        if (column.order) {
          this.filter.orderBy = column.prop;
          this.filter.isAsc = column.order.indexOf("descending") == -1 ? true : false;
          this.getList();
        }
      },
      // 复选框数据
      selectchange:function(rows,row) {
          this.selectList = [];
          rows.forEach(f => {
              this.selectList.push(f);
          })
      },
      // 每页数量改变
      Sizechange(val) {
        this.listLoading = true;
        this.filter.currentPage = 1;
        this.filter.pageSize = val;
        this.getList();
        this.listLoading = false;
      },
      // 当前页改变
      Pagechange(val) {
        this.filter.currentPage = val;
        this.getList();
      },
      // 查询
      onSearch() {
        //点击查询时才将页数重置为1
        this.filter.currentPage = 1;
        this.$refs.pager.setPage(1);
        this.getList();
      },
      async getList() {
        this.filter.startDate = this.timeRange ? this.timeRange[0] : null;
        this.filter.endDate = this.timeRange ? this.timeRange[1] : null;
        this.filter.startFinishDate = this.finishTimeRange ? this.finishTimeRange[0] : null;
        this.filter.endFinishDate = this.finishTimeRange ? this.finishTimeRange[1] : null;
        this.listLoading = true;
        const { data, success } = await getBrushOrderProcess(this.filter);
        this.listLoading = false;
        if (success) {
          this.tableData = data.list;
          this.total = data.total;
        } else {
          this.$message.error("获取刷单处理数据失败");
        }
      },
      // 导出
      async onExport() {
        this.listLoading = true;
        const res = await exportBrushOrderProcess(this.filter);
        this.listLoading = false;
        if (!res?.data) return;
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
        aLink.href = URL.createObjectURL(blob);
        aLink.setAttribute("download", '刷单处理_' + new Date().toLocaleString() + '.xlsx');
        aLink.click();
      },
      // 批量认领
      async onBatchClaim() {
        if (this.selectList.length == 0) {
          this.$message.warning("请选择要认领的ID!");
          return;
        }
        this.$confirm('是否认领这些ID?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
          }
        ).then(() => {
            batchClaimOrder(this.selectList)
              .then(response => {
                if (response.data) {
                  // 删除成功的处理逻辑
                  this.$message.success('认领成功!');
                  this.getList();
                }
              })
            }
        ).catch(() => {
            this.$message.info('已取消认领');
        });
      },
      // 创建刷单-改成单量不能输入小数
      changeedOrderAmountInput(value) {
        // 只允许整数输入，移除非数字字符
        const newValue = value.replace(/[^\d]/g, '');
        // 更新值，防止以0开头
        this.formCreate.changeedOrderAmount = (newValue.startsWith('0') && newValue.length > 1) ? newValue.slice(1) : newValue;
      },
      // 显示创建刷单对话框
      onCreateBrushOrder() {
        this.createVisable = true;
        this.$nextTick(() => {
        this.$refs.formCreate.clearValidate(); // 清除上次的校验结果
      });
      },
      //获取宝贝ID
      skucallback(val) {
        this.formCreate.productID = val;
        console.log(val);
        // 使用正则表达式保留数字和逗号，去除其他非必要字符
        let value_productID = this.formCreate.productID.replace(/[^\d,，]/g, '');
        console.log(value_productID);
        // 按逗号分割输入值
        let segments_productID = value_productID.split(/[,，]/);
        // 更新输入框的值，只保留有效的数字和逗号部分
        this.formCreate.productID = segments_productID.join(',');
        console.log(this.formCreate.productID);
      },
      // 取消创建刷单
      createCancle() {
        this.createVisable = false;
        this.formCreate = {
          shop: [],//店铺名称
          productID: null,//宝贝ID
          changeedOrderAmount: null,//改成单量
        };
      },
      // 关闭创建刷单对话框
      closeCreateDialog() {
        this.createVisable = false;
        this.formCreate = {
          shop: [],//店铺名称
          productID: null,//宝贝ID
          changeedOrderAmount: null,//改成单量
        };
      },
      // 提交创建刷单
      async createSubmit() {
        var admitSubmit = false;
        this.$refs.formCreate.validate((valid) => {
          if (valid) {
            admitSubmit = true;
          } else {
            // 表单验证失败，提示用户
            this.$message.error('请检查填写的信息!');
          }
        });
        if (!admitSubmit) return; 

        this.$refs.formCreate.validate(async valid => {
          if (valid) {
            const res = await createBrushOrder(this.formCreate);
            if (res.success) {
              this.$message.success('创建成功！');
              this.createVisable = false;
              this.getList();
            } else {
              this.$message.error('创建失败！');
            }
          }
        });
      },
      // 删除
      async onDelete(row) {
        if (null != row.finishTime) {
          this.$message.error("刷单已完成不能删除!");
          return;
        }

        this.$confirm('确定要删除吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteBrushOrder(row).then(response => {
          if (response.data) {
            // 删除成功的处理逻辑
            this.$message.success('删除成功!');
            this.getList();
          } else {
            this.$message.error("删除失败");
          }
        })
        }).catch(() => {
          this.$message.info('已取消删除');
        });
      },
      //显示批量完成刷单对话框
      onFinishBrushOrderList() {
        if (this.selectList.length == 0) {
          this.$message.warning("请选择要批量完成的刷单ID!");
          return;
        }
        this.finishVisable = true;
        this.$nextTick(()=>{
          this.$refs.formFinish.resetFields(); // 清空表单数据
        })
        if (this.selectList.length > 0) {
          this.formFinish.mesgIDList = this.selectList.map(x => x.mesgID);
        }
      },
      // 显示完成刷单对话框
      onFinish(row) {
        this.finishVisable = true;
        this.formFinish.mesgID = row.mesgID;
        this.$nextTick(()=>{
          this.$refs.formFinish.resetFields(); // 清空表单数据
        })
        this.row = {
          mesgID: row.mesgID,//主键ID
          productID: row.productID,//商品ID
          shop: row.shop,//店铺
          groupName: row.groupName,//小组
          cannonFodderID: row.cannonFodderID,//炮灰ID
          changeedOrderAmount: row.changeedOrderAmount,//改成单量
          createUser: row.createUser,//创建人
          createTime: row.createTime,//创建时间
          claimUser: row.claimUser,//认领人
          claimTime: row.claimTime,//认领时间
          progress: row.progress,//进度
          isTakeDown: row.isTakeDown,//炮灰是否下架
          remark: row.remark,//备注
        };
      },
      // 取消完成刷单
      finishCancle() {
        this.finishVisable = false;
        this.formFinish = {
          cannonFodderID: null,//炮灰ID
          progress: null,//进度
          isTakeDown: null,//炮灰是否下架
          remark: null,//备注
        };
      },
      // 关闭完成刷单对话框
      closeFinishDialog() {
        this.finishVisable = false;
        this.formFinish = {
          cannonFodderID: null,//炮灰ID
          progress: null,//进度
          isTakeDown: null,//炮灰是否下架
          remark: null,//备注
        };
      },
      // 提交完成刷单
      async finishSubmit() {
        var admitSubmit = false;
        this.$refs.formFinish.validate((valid) => {
          if (valid) {
            admitSubmit = true;
          } else {
            // // 表单验证失败，提示用户
            // this.$message.error('请检查填写的信息');
            admitSubmit = false;
          }
        });
        if (!admitSubmit) {
            this.$message.error('请检查填写的信息');
            return;
        } 
        
        if (this.formFinish.mesgID != null) {
          if (this.row.claimUser === null || this.row.claimTime === null) {
            this.$message.error("无法完成未认领的刷单任务！");
            admitSubmit = false;
            return;
          }
          if (this.row.cannonFodderID !== null || this.row.progress !== "未下单" || this.row.isTakeDown !== null)
          {
            this.$message.error("无法完成已完成的刷单任务！");
            admitSubmit = false;
            return;
          }
          // 是否通过校验
          this.$refs.formFinish.validate(async valid => {
            if (valid) {
              const res = await finishBrushOrder(this.formFinish);
              if (res.success) {
                this.$message.success('完成刷单成功！');
                this.finishVisable = false;
                this.getList();
              }
            }
          });
        } else {
          this.selectList.forEach(element => {
            if (element.claimUser === null || element.claimTime === null) {
              this.$message.error("无法完成未认领的刷单任务！");
              admitSubmit = false;
              return;
            }
            if (element.cannonFodderID !== null || element.progress !== "未下单" || element.isTakeDown !== null)
            {
              this.$message.error("无法完成已完成的刷单任务！");
              admitSubmit = false;
              return;
            }
          });
          // 是否通过校验
          if (admitSubmit) {
            this.$refs.formFinish.validate(async valid => {
              if (valid) {
                const res = await batchFinishBrushOrder(this.formFinish);
                if (res.success) {
                  this.$message.success('完成刷单成功！');
                  this.finishVisable = false;
                  this.getList();
                }
              }
            });
          }
        }

      },
      // 批量删除刷单
      async onDeleteBrushOrderList() {
        var data = this.selectList.filter(x => x.finishTime != null);
        if (data.length > 0) {
          this.$message.error("无法删除已完成的刷单任务!");
          return;
        }
        this.$confirm('确定要删除吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
        batchDeleteBrushOrder(this.selectList).then(response => {
          if (response.data) {
            // 删除成功的处理逻辑
            this.$message.success('删除成功!');
            this.getList();
          } else {
            this.$message.error("删除失败");
          }
        })
        }).catch(() => {
          this.$message.info('已取消删除');
        });
      },
    }
  }
</script>
<style lang="scss" scoped>
  .top {
    display: flex;
    margin-bottom: 10px;
  }
  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
  //解决下拉菜单多选由文字太长导致样式问题
  ::v-deep .el-select__tags-text {
    max-width: 60px;
  }
</style>
