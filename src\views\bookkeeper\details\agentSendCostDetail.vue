<template>
  <container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="filter"
        @submit.native.prevent>
      <el-form-item  label="年月:">
         <el-date-picker style="width: 110px" v-model="filter.settMonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
        </el-form-item>
        <el-form-item label="店铺名称:" label-position="right" >
            <el-input v-model="filter.shopName" style="width:183px;"/>
        </el-form-item>
        <el-form-item label="产品ID:" label-position="right" >
            <el-input v-model="filter.proCode" style="width:183px;"/>
        </el-form-item>
        <el-form-item label="商品编码:" label-position="right" >
            <el-input v-model="filter.goodsCode" style="width:183px;"/>
        </el-form-item>
        <el-form-item label="线上订单号:" label-position="right" >
            <el-input v-model="filter.orderNo" style="width:183px;"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true'  :hasexpand='false' @sortchange='sortchange' :tableData='ZTCKeyWordList' 
           :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry'
              @select='selectchange' :isSelection='false' :tableCols='tableCols' :loading="listLoading" :isSelectColumn='true'>
    </ces-table>
    
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length"  @get-page="getList"/>
    </template>
    <el-dialog title="导入" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
      <span>
          <el-row>
            <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker style="width: 100%" v-model="onimportfilter.yearmonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
         </el-col>
         </el-row>
          <el-row>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                <el-upload
                  ref="upload"
                  class="upload-demo"
                  :auto-upload="false"
                  :multiple="false"
                  :limit="1"
                  action
                  accept=".xlsx"
                  :http-request="uploadFile"
                  :file-list="fileList"
                  :data="fileparm">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
              </el-upload>
            </el-col>
          </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="批量删除" :visible.sync="dialogdeletebatchNumberVisible" width="500px" v-dialogDrag>
      <el-row>
           <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
              <el-input placeholder="请输入批次号" v-model="deletefilter.batchNumber" style="width: 100%"></el-input>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
             <el-button type="primary" @click="deleteByBatch">删除</el-button>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogdeletebatchNumberVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
import {pageAgentSendCost,deleteAgentSendCost} from '@/api/bookkeeper/reportday'
import {importAgentSendCost} from '@/api/bookkeeper/import'

const tableCols =[
      {istrue:true,prop:'yearMonth',label:'年月', width:'60',sortable:'custom'},
      {istrue:true,prop:'proCode',label:'商品ID', width:'120',sortable:'custom'},
      {istrue:true,prop:'shopName',label:'店铺名', width:'130',sortable:'custom'},
      {istrue:true,prop:'goodsCode',label:'商品编码', width:'110',sortable:'custom'},
      {istrue:true,prop:'goodsName',label:'商品名称', width:'auto',sortable:'custom'},
      {istrue:true,prop:'count',label:'数量',sortable:'custom', width:'60'},
      {istrue:true,prop:'payAmont',label:'买家支付金额',sortable:'custom', width:'110',},
      {istrue:true,prop:'orderNoOrigin',label:'线上订单号',sortable:'custom', width:'120'},
      {istrue:true,prop:'agentAmont',label:'代拍金额',sortable:'custom', width:'auto'},
      {istrue:true,prop:'jstCost',label:'聚水潭成本',sortable:'custom', width:'auto'},
      {istrue:true,prop:'diffAmont',label:'差额',sortable:'custom', width:'auto'},
      {istrue:true,prop:'batchNumber',label:'批次号',sortable:'custom', width:'auto'},
     ];
 const tableHandles=[
        {label:"导入", handle:(that)=>that.onstartImport()},
        {label:"批次号删除", handle:(that)=>that.ondeleteByBatch()},
        {label:"刷新", handle:(that)=>that.onRefresh()},
      ];
export default {
  name: "Users",
  components: { container, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
  data() {
    return {
      that:this,
      filter: {
        yearMonth:null,
        shopName:null,
        proCode:null,
        goodsCode:null,
        orderNo:null,
      },
      onimportfilter:{
        yearmonth:null,
      },
      deletefilter:{
        batchNumber:null,
      }, 
      summaryarry:{},
      shopList:[],
      userList:[],
      groupList:[],
      ZTCKeyWordList: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      total: 0,
      pager:{OrderBy:"id",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids:[],
      fileList:[],
      dialogVisible:false,
      uploadLoading:false,
      importFilte:{},
      fileList:[],
      fileparm:{},
      dialogdeletebatchNumberVisible:false,
    };
  },
  async mounted() {
    await this.getShopList();
  },
  methods: {
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    async getShopList(){
      const res1 = await getAllShopList();
      this.shopList=[];
      res1.data?.forEach(f => {
        if(f.isCalcSettlement&&f.shopCode)
          this.shopList.push(f);
      });
    },
    onRefresh(){
        this.onSearch()
    },
    onSearch(){
       this.$refs.pager.setPage(1);
       this.getList();
    },
    async getList(){
      var pager = this.$refs.pager.getPager();
      const params = {...pager,...this.pager,...this.filter};
      console.log(params)
      this.listLoading = true;
      const res = await pageAgentSendCost(params);
      this.listLoading = false;
      this.total = res.data?.total
      this.ZTCKeyWordList = res.data?.list;

       this.summaryarry= res.data?.summary;
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
    async deleteByBatch() {
      if (!this.deletefilter.batchNumber) {
       this.$message({type: 'warning',message: '请输入批次号!'});
       return;
      } 
      this.$confirm('确认删除, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
        }).then(async () => {
            const res = await deleteAgentSendCost(this.deletefilter.batchNumber)
            if (!res?.success) {return }
            this.$message({type: 'success',message: '删除成功!'});
            this.onSearch()
        }).catch(() => {
          this.$message({type: 'info',message: '已取消删除'});
        });
    },
   async onstartImport(){
      this.dialogVisible=true;
    },
   async ondeleteByBatch() {
        this.dialogdeletebatchNumberVisible=true;
        this.deletefilter.batchNumber='';
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      if (!this.onimportfilter.yearmonth) {
       this.$message({type: 'warning',message: '请选择年月!'});
       return;
      }
      this.uploadLoading=true
      this.$refs.upload.submit();
    },
   async uploadFile(item) {
     if(!item||!item.file||!item.file.size){
        this.$message({message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("yearmonth", this.onimportfilter.yearmonth);
      var res = await importAgentSendCost(form);
      if (res.code==1) this.$message({message: "上传成功,正在导入中...", type: "success" });
      else this.$message({message: res.msg, type: "warning" });
      this.uploadLoading=false
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>