<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                    <el-date-picker style="width: 280px" v-model="filter.daterange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="爆款开始日期"
                        end-placeholder="爆款结束日期" :clearable="true" :picker-options="pickerOptions"></el-date-picker>
                </el-button>

                <el-button style="padding: 0;margin: 0;">
                    <!-- <el-input v-model.trim="filter.proCode" placeholder="产品ID" style="width:120px;" clearable
                        maxlength="40" /> -->

                    <inputYunhan ref="burststyleprotectproCode" v-model="filter.proCode" :inputt.sync="filter.proCode"
                        placeholder="产品ID" :clearable="true" width="200px" @callback="callbackProCode" title="产品ID">
                    </inputYunhan>

                </el-button>

                <el-button style="padding: 0;margin: 0;">
                    <el-select filterable clearable v-model="filter.productProPerty" placeholder="属性"
                        style="width: 120px">
                        <el-option label="日销" :value="1"></el-option>
                        <el-option label="季节" :value="2"></el-option>
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model.trim="filter.proName" placeholder="商品名称" style="width:120px;" clearable
                        maxlength="50" />
                </el-button>

                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model.trim="filter.styleCode" placeholder="系列编码" style="width:120px;" clearable
                        maxlength="40" />
                </el-button>

                <el-button style="padding: 0;margin: 0;">
                    <el-select v-model="filter.platform" placeholder="平台" @change="getloadshopselect" clearable
                        filterable style="width: 80px">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;">
                    <el-select v-model="filter.shopCode" style="width: 240px" size="mini" placeholder="店铺" clearable
                        filterable>
                        <el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;">
                    <el-select filterable v-model="filter.groupId" placeholder="小组" style="width: 100px" clearable>
                        <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value"
                            :value="item.key" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;">
                    <el-select filterable v-model="filter.operateSpecialUserId" collapse-tags clearable
                        placeholder="运营专员" style="width: 90px">
                        <el-option v-for="item in directorlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;">
                    <el-select filterable v-model="filter.userId" collapse-tags clearable placeholder="运营助理"
                        style="width: 90px">
                        <el-option v-for="item in directorlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-button>

                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onExport">导出</el-button>
            </el-button-group>
        </template>

        <vxetablebase :id="'burststyleprotectloss20231019'" :border="true" :align="'center'"
            :tablekey="'burststyleprotectloss20231019'" ref="table2" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :isSelectColumn="true" :showsummary='false' :tablefixed='true'
            :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols' :tableHandles='tableHandles'
            :loading="listLoading" style="width:100%;height:100%;margin: 0" :xgt="9999">
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </my-container>
</template>
<script>
import dayjs from "dayjs";
import { platformlist } from '@/utils/tools'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatPlatform, formatTime, pickerOptions, formatLinkProCode } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import inputYunhan from "@/components/Comm/inputYunhan";

import { getDirectorGroupList, getList as getshopList, getDirectorList } from '@/api/operatemanage/base/shop'
import { GetBurstStyleProtectPageList, ExportBurstStyleProtect, deleteBurstStyleProtect, revokeLossBurstStyleProtect } from '@/api/operatemanage/burststyleprotect'

const tableCols = [
    { istrue: true, prop: 'platform', label: '平台', sortable: 'custom', width: '80', formatter: (row) => formatPlatform(row.platform) },
    { istrue: true, prop: 'shopCode', label: '店铺', sortable: 'custom', width: '200', formatter: (row) => row.shopName },
    { istrue: true, prop: 'productCategoryId', label: '类目', sortable: 'custom', width: '120', formatter: (row) => row.productCategoryName },
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'proCode', label: '产品ID', sortable: 'custom', width: '120', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
    { istrue: true, prop: 'burstType', label: '类型', sortable: 'custom', width: '120', formatter: (row) => (row.burstType == 0 ? "爆款" : (row.burstType == 1 ? "趋势款" : "")) },
    { istrue: true, prop: 'productProPerty', label: '属性', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'title', label: '产品名称', sortable: 'custom', width: '300' },
    { istrue: true, prop: 'groupId', label: '小组', sortable: 'custom', width: '80', formatter: (row) => row.groupName },
    { istrue: true, prop: 'operateSpecialUserId', label: '专员', sortable: 'custom', width: '80', formatter: (row) => row.operateSpecialUserName },
    { istrue: true, prop: 'userId', label: '助理', sortable: 'custom', width: '80', formatter: (row) => row.userName },
    { istrue: true, prop: 'burstStyleTime', label: '爆款时间', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'lossMan', label: '流失操作人', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'lossTime', label: '流失时间', sortable: 'custom', width: '150' },
    {
        istrue: true, type: 'button', label: '操作', width: '107', align: 'center',
        btnList: [
            { label: "撤回", handle: (that, row) => that.oneditsalaryuser(row.id) },
            { label: "删除", handle: (that, row) => that.ondeletesalaryuser(row.id) },
        ]
    },
];
const tableHandles = [
    //{ label: "编辑", handle: (that, row) => that.onEdit(row) },
];
const startDate = formatTime(dayjs().subtract(1, 'month'), "YYYY-MM-DD");
const endDate = formatTime(dayjs(), "YYYY-MM-DD");

export default {
    name: "burststyleprotect",
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, vxetablebase, inputYunhan
    },
    data() {
        return {
            that: this,
            filter: {
                burstType: -1,
                daterange: [startDate, endDate],
                shopCode: "",
                isLoss: true,
                productProPerty: null,
            },
            directorlist: [],
            pickerOptions: pickerOptions,
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            datalist: [],
            pager: { OrderBy: "burstStyleTime", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            selrows: [],

            platformlist: platformlist,
            shopList: [],
            directorGroupList: [],
        };
    },
    async mounted() {
        await this.getloadgroupselect();
        await this.onSearch();
    },
    async created() {
    },
    methods: {
        async oneditsalaryuser(rowid) {
            this.$confirm("确定要执行撤回吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                const { data, success } = await revokeLossBurstStyleProtect({ id: rowid })
                if (success) {
                    this.$message({ type: "success", message: "撤回成功!" });
                    await this.getList();
                }
            }).catch(() => {
            });
        },
        async ondeletesalaryuser(rowid) {
            this.$confirm('确定要执行删除吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await deleteBurstStyleProtect({ id: rowid, burstType: -1 });
                if (res?.success) {
                    this.$message({ type: 'success', message: '删除成功!' });
                    await this.getList();
                }
            }).catch(() => {
            });
        },
        async getloadshopselect() {
            this.filter.shopCode = "";
            const res1 = await getshopList({ platform: this.filter.platform, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list.map(item => { return { value: item.shopCode, label: item.shopName }; });
        },
        async getloadgroupselect() {
            const res2 = await getDirectorGroupList({});
            this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);

            let res3 = await getDirectorList();
            this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getCondition() {
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.daterange) {
                this.filter.startDate = this.filter.daterange[0];
                this.filter.endDate = this.filter.daterange[1];
            }
            else {
                this.filter.startDate = '2020-01-01';
                this.filter.endDate = '2030-01-01';
            }
            let pager = this.$refs.pager.getPager();
            switch (this.filter.productProPerty) {
                case 1: this.filter.productProPerty = "日销"; break;
                case 2: this.filter.productProPerty = "季节"; break;
            }
            const params = { ...pager, ...this.pager, ...this.filter };

            return params;
        },
        async getList() {
            const params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params);
            this.listLoading = true;
            const res = await GetBurstStyleProtectPageList(params);
            this.listLoading = false;
            console.log(res);
            this.total = res.data?.total;
            this.datalist = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        async onExport() {
            const params = this.getCondition();
            if (params === false) {
                return;
            }
            this.listLoading = true;
            const res = await ExportBurstStyleProtect(params)
            this.listLoading = false;
            if (!res?.data) {
                this.$message({ type: 'error', message: '没有数据可导出或导出失败!' });
                return;
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', "爆款流失_" + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        async callbackProCode(val) {
            this.filter.proCode = val;
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
