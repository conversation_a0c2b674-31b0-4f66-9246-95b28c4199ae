<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="工作日期:">
                    <el-date-picker style="width: 240px"
                        v-model="filter.timerange"
                        type="datetimerange"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        range-separator="至"
                        start-placeholder="开始"
                        end-placeholder="结束"
                ></el-date-picker>
                </el-form-item>
                <el-form-item label="人员姓名:">
                <el-input v-model="filter.UserName" placeholder="人员姓名" style="width: 230px" @keyup.enter.native="onSearch" clearable/>
                </el-form-item>
                <el-form-item>
                <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
            </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='true'
        :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
            <template #column>
            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
        </template>

    </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import {upLoadFile,upLoadImage} from '@/api/upload/file'
// import { addLogUpdate, getUpdateLog, getUpdate } from '@/api/admin/opration-log'
import { getAttendanceOrderNodesList, exportAttendanceOrderNodesList} from '@/api/storehouse/storerecords'

const tableCols =[
      {istrue:true,prop:'userName',label:'用户', width:'280',sortable:'custom'},
      {istrue:true,prop:'workDuration',label:'工作时长', type:'editor',width:'200', sortable:'custom'},
      {istrue:true,prop:'workTime',label:'工作时间', style: 'center', width:'auto',sortable:'custom'},
      {istrue:true,prop:'workDate',label:'日期', style: 'center', width:'300',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD')},
      {istrue:true,prop:'pickCount',label:'拣货单数', width:'200', sortable:'custom'},
      {istrue:true,prop:'deliveryCount',label:'发货单数', width:'200', sortable:'custom'},
     ];
const tableHandles1=[
         {label:"导出考勤", handle:(that)=>that.onExport()},
         // {label:'导入考勤', handle:(that)=>that.onExport()},
         // {label:'查看', handle:(that)=>that.onHand(2)},

      ];
const StartTime = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
const EndTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunhanAdminUpdatelog',
    components: {cesTable, MyContainer, MyConfirmButton},
    data() {
        return {
            that:this,
            filter: {
                UserName:'',
                timerange: [StartTime,EndTime],
                StartTime:null,
                EndTime:null,
            },
            list: [],
            pager:{OrderBy:"workDate",IsAsc:false},
            tableCols:tableCols,
            tableHandles:tableHandles1,
            uploadfilelist:[],
            fileList:[],
            uploadimagelist:[],
            imageList:[],
            handtype:1,
            formtitle:null,
            autoform:{
                    fApi:{},
                    options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 16 }},
                                                    upload: {props: {onError: function(r){alert('上传失败')}}}}},
                    rule:[]
                },
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            handVisible: false,
            handLoading: false,
            reportsingle:{processList:[]},
            collapseactiveName: '1',
        };
    },
    async mounted() {
        formCreate.component('editor', FcEditor);
        await this.onSearch()
    },
    methods: {

        async initform(){
            let that = this
            this.autoform.rule= [{type:'hidden',field:'id',title:'id',value: ''},
                            {type:'input',field:'title',title:'标题',value: '',col:{span:24},validate: [{type: 'string', required: true, message:'请输入标题'}]},
                            {type:'editor',field:'content',title:'更新内容',value:'',col:{span:24},validate: [{type: 'string', required: true, message:'必填'}],
                            props:{init:async(editor)=>{await that.initeditor(editor) }}}]
            this.autoform.rule.forEach(f=>{
                    if (f.field=='toUserId1')  f.validate=[]
                    if (f.field=='toUserId2')  f.validate=[]
                })
        },
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist()
            },
            async getlist() {
            var pager = this.$refs.pager.getPager()
            // pager.CurrentPage = pager.currentPage
            // pager.PageSize = pager.pageSize
            const params = {
                ...pager,
                ...this.pager,
                ... this.filter
            }

            // const params = {
            //     ...pager,
            //     ... this.filter
            // }
            this.listLoading = true
            if (this.filter.timerange) {
                params.StartTime = this.filter.timerange[0];
                params.EndTime = this.filter.timerange[1];
            }
            console.log("请求数据",this.pager)
            const res = await getAttendanceOrderNodesList(params)
            console.log("打印返回数据",res);
            this.listLoading = false
            if (!res?.code) {
                return
            }
            this.total = res.data.total
            const data = res.data.list
            data.forEach(d => {
                d._loading = false
            })
            this.list = data
        },


        //获取查询条件
        getCondition(){

          const params = {

            ... this.filter
          }
          return params;
        },


        async onExport(){
          if (!this.pager.OrderBy) this.pager.OrderBy="workDate";

          const params = {...this.pager,... this.filter}
          if (params.timerange) {
            params.StartTime = params.timerange[0];
            params.EndTime = params.timerange[1];
          }
          console.log("最终参数",params);
          var res= await exportAttendanceOrderNodesList(params);
          if(!res?.data) return
          const aLink = document.createElement("a");
          let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
          aLink.href = URL.createObjectURL(blob)
          aLink.setAttribute('download','考勤记录_' + new Date().toLocaleString() + '.xlsx' )
          aLink.click()
        },
        async exportIn(){
          console.log("导入数据");
        },
        async initeditor(editor){
            editor.config.uploadImgMaxSize = 3 * 1024 * 1024
            editor.config.excludeMenus = ['emoticon','video']
            // editor.config.uploadImgAccept = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
            editor.config.uploadImgAccept = []
            // editor.customConfig.debug = true;
            editor.config.customUploadImg =async function (resultFiles, insertImgFn) {
                console.log('resultFiles',resultFiles)
                const form = new FormData();
                form.append("image", resultFiles[0]);
                const res =await upLoadImage(form);
                var url=`${res.data}`
                console.log('url',url)
                insertImgFn(url)
            }
        },
        async sortchange(column){
            if(!column.order)
                this.pager={};
            else
            this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
            this.onSearch();
            // console.log("触发条件",this.pager);
        },
        selectchange:function(rows,row) {
            this.selids=[];
            rows.forEach(f=>{
                this.selids.push(f.id);
            })
        },
    },
};
</script>

<style lang="scss" scoped>

</style>
