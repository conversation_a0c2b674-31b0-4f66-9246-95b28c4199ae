<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='inquirsstatisticslist' @select='selectchange' :isSelection='false' :tableCols='tableCols'
            :loading="listLoading" :summaryarry="summaryarry">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.groupNameList"   placeholder="分组" clearable filterable multiple :collapse-tags="true">
                            <el-option v-for="item in filterGroupList" :key="item" :label="item"
                                :value="item">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.sname" placeholder="姓名" style="width:120px;" clearable
                            :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <datepicker v-model="filter.sdate"></datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <!-- <el-button type="primary" @click="onExport" style="margin-left: 10px;">导出</el-button> -->
                </el-button-group>
            </template>
        </ces-table>
<!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"
                @get-page="getinquirsstatisticsList" />
        </template>
<!-- 个人效率店效率 -->
        <el-dialog title="个人效率按店统计" :visible.sync="dialogVisibleSyj" width="50%" :close-on-click-modal="false" v-dialogDrag>
            <span>
                <dypersonshopcomp v-if="dialogVisibleSyj" ref="sqinquirsstatisticsbyshop" style="height: 500px;" />
            </span>
            <!-- <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span> -->
        </el-dialog>
<!-- 人数弹框 -->
        <el-dialog title="" :visible.sync="dialogVisibleMY" width="50%" :close-on-click-modal="false" v-dialogDrag>
            <span>
                <dyservicecomp v-if="dialogVisibleMY" ref="dyservicecomp" style="height: 500px;" />
            </span>
            <!-- <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleMY = false">关闭</el-button>
            </span> -->
        </el-dialog>

    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import buschar from '@/components/Bus/buschar'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

import {
    getOSGroupByList,
    getODDouYinPersonalEfficiencyPageList,
    ExportDouYinByPerson
    } from "@/api/customerservice/waibao";

import dypersonshopcomp from '@/views/customerservice/waibao/douyin/dypersonshopcomp'
import dyservicecomp from '@/views/customerservice/waibao/douyin/dyservicecomp'

const tableCols = [
    { istrue: true, prop: 'groupName', label: '组名称',sortable: 'custom' },
    { istrue: true, prop: 'sname', label: '姓名', type: "click", handle: (that, row, column, cell) => that.canclick(row, column, cell), formatter: (row) => row.sname },
    { istrue: true, prop: 'receiveds', label: '评价数量', sortable: 'custom'},
    { istrue: true, prop: 'noSatisfactionRate', label: '不满意率',  sortable: 'custom', formatter: (row) => (row.noSatisfactionRate ).toFixed(2) + "%" },
    { istrue: true, prop: 'noSatisfactionCount', label: '不满意人数',  sortable: 'custom', type: "click", handle: (that, row, column, cell) => that.personclick(row,"不满意"),},
    { istrue: true, prop: 'satisfactionRate', label: '满意率',ortable: 'custom', formatter: (row) => (row.satisfactionRate ).toFixed(2) + "%" },
    { istrue: true, prop: 'satisfactionCount', label: '满意人数',sortable: 'custom',type: "click", handle: (that, row, column, cell) => that.personclick(row, "满意"),},
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar, dypersonshopcomp,dyservicecomp },
    data() {
        return {
            that: this,
            filter: {
            },
            shopList: [],
            filterGroupList: [],
            userList: [],
            inquirsstatisticslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: {},
            pager: { OrderBy: "receiveds", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            dialogVisibleMY:false,
            fileList: [],
        };
    },
    async mounted() {
        await this.getGroupNameList();
        window.showlist55dy = this.showlist55dy;
    },
    methods: {
       async getGroupNameList() {
            let groups = await getOSGroupByList({ platform: 6 });
            this.filterGroupList=groups.data
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getinquirsstatisticsList();
        },
        getParam() {
            if (this.filter.sdate) {
                this.filter.timeStart = this.filter.sdate[0];
                this.filter.timeEnd = this.filter.sdate[1];
            }
            else {
                this.filter.timeStart = null;
                this.filter.timeEnd = null;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            return params;
        },
        async getinquirsstatisticsList() {
            let params = this.getParam();
            console.log(params, "params")
            this.listLoading = true;
            const res = await getODDouYinPersonalEfficiencyPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.inquirsstatisticslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async canclick(row, column, cell) {
            var fstartsdate = "";
            var fendsdate = "";
            if (this.filter.sdate) {
                var d = new Date(this.filter.sdate[0])
                fstartsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
                d = new Date(this.filter.sdate[1])
                fendsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
            }
            if (fstartsdate == "NaN-NaN-NaN") {
                fstartsdate = "";
                fendsdate = "";
            }
            var fsname = row.sname;
            this.dialogVisibleSyj = true;
            this.$nextTick(() => {
                this.$refs.sqinquirsstatisticsbyshop.dialogOpenAfter({
                    startDate: fstartsdate,
                    endDate: fendsdate,
                    name: fsname,
                    isService:true,
                });
            });
        },
        async personclick(row,isManYi){
            var fstartsdate = "";
            var fendsdate = "";
            if (this.filter.sdate) {
                var d = new Date(this.filter.sdate[0])
                fstartsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
                d = new Date(this.filter.sdate[1])
                fendsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
            }
            if (fstartsdate == "NaN-NaN-NaN") {
                fstartsdate = "";
                fendsdate = "";
            }
            var fsname = row.sname;
            var fsgroupName = row.groupName;
            this.dialogVisibleMY = true;
            this.$nextTick(() => {
                this.$refs.dyservicecomp.dialogOpenAfter({
                    startDate: fstartsdate,
                    endDate: fendsdate,
                    name: fsname,
                    groupName:fsgroupName,
                    isManYi:isManYi,
                });
            });
        },
        async onExport() {
            let params = this.getParam();
            this.listLoading = true
            const res = await ExportDouYinByPerson(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '抖音服务数据统计(外包)_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 60px;
}
.footer {
    border: 1px solid red;
}
</style>
