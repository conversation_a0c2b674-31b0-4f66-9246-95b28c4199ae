<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-button type="primary" @click="getList(true)">刷新</el-button>
                <el-button type="primary" v-if="checkPermission('costSettings')"
                    @click="handleAdd(true)">成本设置</el-button>
                <el-button type="primary" v-if="checkPermission('sellingPriceSettings')"
                    @click="handleAdd(false)">售价设置</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading" :height="'99%'">
        </vxetablebase>
        <!-- 阳光隔热膜设置 -->
        <el-dialog :title="isCbSet ? '成本设置' : '售价设置'" :visible.sync="drawer" width="86%" :close-on-click-modal="false"
            v-dialogDrag>
            <el-button type="text" @click="addProps" v-if="isCbSet">新增一行</el-button>
            <el-table :data="formData" style="width: 100%;height:95%" max-height="400">
                <el-table-column label="#" type="index" width="40" />
                <el-table-column prop="norms" label="规格" width="75">
                    <template #header="{ column }">
                        <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>规格
                    </template>
                    <template #default="{ row }">
                        <el-select v-model="row.norms" placeholder="型号" @change="changeName" :disabled="!isCbSet">
                            <el-option key="6mm" label="6mm" value="6mm" />
                            <el-option key="8mm" label="8mm" value="8mm" />
                            <el-option key="祥云" label="祥云" value="祥云" />
                            <el-option key="绵羊" label="绵羊" value="绵羊" />
                            <el-option key="小树林" label="小树林" value="小树林" />
                            <el-option key="蓝天白云" label="蓝天白云" value="蓝天白云" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column prop="bbType" label="是否包边" width="80">
                    <template #header="{ column }">
                        <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>是否包边
                    </template>
                    <template #default="{ row }">
                        <el-select v-model="row.bbType" placeholder="包边" @change="changeName" :disabled="!isCbSet">
                            <el-option key="不包边" label="不包边" value="不包边" />
                            <el-option key="包黑边" label="包黑边" value="包黑边" />
                            <el-option key="包灰边" label="包灰边" value="包灰边" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column prop="isDK" label="是否打扣" width="50">
                    <template #default="{ row }">
                        <div style="display: flex;justify-content: center;">
                            <el-checkbox v-model="row.isDK" @change="changeName" :disabled="!isCbSet" />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="isNeedXP" label="是否需要吸盘" width="60">
                    <template #default="{ row }">
                        <div style="display: flex;justify-content: center;">
                            <el-checkbox v-model="row.isNeedXP" @change="changeName" :disabled="!isCbSet" />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="isNeedMST" label="是否送魔术贴" width="80">
                    <template #default="{ row }">
                        <div style="display: flex;justify-content: center;">
                            <el-checkbox v-model="row.isNeedMST" @change="changeName" :disabled="!isCbSet" />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="fullNormsName" label="规格全称" width="180">
                    <template #default="{ row }">
                        <el-input v-model="row.fullNormsName" placeholder="规格全称" disabled />
                    </template>
                </el-table-column>
                <el-table-column prop="sheetSquareCost" label="单张平方价格" width="80">
                    <template #default="{ row }">
                        <el-input-number v-model="row.sheetSquareCost" :max="10000" :precision="3" :controls="false"
                            label="单张平方价格" class="iptCss" :disabled="!isCbSet" :min="0" />
                    </template>
                </el-table-column>
                <el-table-column prop="metreBBClothCost" label="包边布1米价格" width="80">
                    <template #default="{ row }">
                        <el-input-number v-model="row.metreBBClothCost" :max="10000" :precision="3" :controls="false"
                            label="包边布一米价格" class="iptCss" :disabled="!isCbSet" :min="0" />
                    </template>
                </el-table-column>
                <el-table-column prop="sheetBBPackProcessCost" label="包边加工费" width="80">
                    <template #default="{ row }">
                        <el-input-number v-model="row.sheetBBPackProcessCost" :max="10000" :precision="3"
                            :controls="false" label="包边加工费" class="iptCss" :disabled="!isCbSet" :min="0" />
                    </template>
                </el-table-column>
                <el-table-column prop="sheetSixIronbuckleCost" label="单张铁扣" width="80">
                    <template #default="{ row }">
                        <el-input-number v-model="row.sheetSixIronbuckleCost" :max="10000" :precision="3"
                            :controls="false" label="单张铁扣" class="iptCss" :disabled="!isCbSet" :min="0" />
                    </template>
                </el-table-column>
                <el-table-column prop="sheetDKPackProcessCost" label="打扣加工费" width="80">
                    <template #default="{ row }">
                        <el-input-number v-model="row.sheetDKPackProcessCost" :max="10000" :precision="3"
                            :controls="false" label="打扣加工费" class="iptCss" :disabled="!isCbSet" :min="0" />
                    </template>
                </el-table-column>
                <el-table-column prop="sheetSixXPCost" label="单张吸盘" width="80">
                    <template #default="{ row }">
                        <el-input-number v-model="row.sheetSixXPCost" :max="10000" :precision="3" :controls="false"
                            label="单张吸盘" class="iptCss" :disabled="!isCbSet" :min="0" />
                    </template>
                </el-table-column>
                <el-table-column prop="sheetMSTCost" label="单张魔术贴" width="80">
                    <template #default="{ row }">
                        <el-input-number v-model="row.sheetMSTCost" :max="10000" :precision="3" :controls="false"
                            label="单张魔术贴" class="iptCss" :disabled="!isCbSet" :min="0" />
                    </template>
                </el-table-column>
                <el-table-column prop="sheetDishClothcCost" label="抹布" width="75">
                    <template #default="{ row }">
                        <el-input-number v-model="row.sheetDishClothcCost" :max="10000" :precision="3" :controls="false"
                            label="抹布" style="width: 65px;" :disabled="!isCbSet" :min="0" />
                    </template>
                </el-table-column>
                <el-table-column prop="sheetSprayKettleCost" label="喷壶" width="75">
                    <template #default="{ row }">
                        <el-input-number v-model="row.sheetSprayKettleCost" :max="10000" :precision="3"
                            :controls="false" label="喷壶" style="width: 65px;" :disabled="!isCbSet" :min="0" />
                    </template>
                </el-table-column>
                <el-table-column prop="sheetCroppingCost" label="单张裁剪" width="80">
                    <template #default="{ row }">
                        <el-input-number v-model="row.sheetCroppingCost" :max="10000" :precision="3" :controls="false"
                            label="单张裁剪" class="iptCss" :disabled="!isCbSet" :min="0" />
                    </template>
                </el-table-column>
                <el-table-column prop="orderFreightBagFee" label="快递袋费" width="80">
                    <template #default="{ row }">
                        <el-input-number v-model="row.orderFreightBagFee" :max="10000" :precision="2" :controls="false"
                            label="快递袋费" class="iptCss" :disabled="!isCbSet" :min="0" />
                    </template>
                </el-table-column>
                <el-table-column prop="orderPackProcessFee" label="打包费" width="80">
                    <template #default="{ row }">
                        <el-input-number v-model="row.orderPackProcessFee" :max="10000" :precision="2" :controls="false"
                            label="打包费" class="iptCss" :disabled="!isCbSet" :min="0" />
                    </template>
                </el-table-column>
                <el-table-column prop="sheetSquareSaleAmount" label="单张平方售价" width="80">
                    <template #default="{ row }">
                        <el-input-number v-model="row.sheetSquareSaleAmount" :max="10000" :precision="2"
                            :controls="false" label="单张平方售价" class="iptCss" :disabled="isCbSet" :min="0" />
                    </template>
                </el-table-column>
                <el-table-column prop="goodsCode" label="商品编码" width="80">
                    <template #default="{ row }">
                        <el-tooltip class="item" effect="dark" :content="row.goodsCode" placement="top">
                            <el-input v-model="row.goodsCode" maxlength="20" :controls="false" label="商品编码"
                                class="iptCss" :disabled="isCbSet" />
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column prop="sheetSquareSaleAmount" label="操作" width="80" v-if="isCbSet">
                    <template #default="{ row, $index }">
                        <el-button type="danger" @click="formData.splice($index, 1)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="btnGroup">
                <el-button style="margin-right: 10px;" @click="drawer = false">取消</el-button>
                <el-button type="primary" @click="submit" v-throttle="3000">保存</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { getCostSetByYGGRM, saveCostSetByYGGRM, saveSalePriceSetByYGGRM } from "@/api/inventory/customNormsGoods";
import { log } from "mathjs";
const ys = {
    norms: '规格',
    bbType: '包边',
    isDK: '打扣',
    isNeedXP: '吸盘',
    fullNormsName: '规格全称',
    sheetSquareCost: '单张平方价格',
    sheetSquareSaleAmount: '单张平方售价',
    metreBBClothCost: '包边布一米价格',
    sheetBBPackProcessCost: '包边加工费',
    sheetSixIronbuckleCost: '单张铁扣',
    sheetDKPackProcessCost: '打扣加工费',
    sheetSixXPCost: '单张吸盘',
    sheetDishClothcCost: '抹布',
    sheetSprayKettleCost: '喷壶',
    sheetCroppingCost: '单张裁剪',
    orderFreightBagFee: '快递袋费',
    orderPackProcessFee: '打包费',
    goodsCode: '商品编码',
}
const tableCols = [
    { sortable: 'custom', width: '60', align: 'left', prop: 'norms', label: '规格', },
    { sortable: 'custom', width: '80', align: 'left', prop: 'bbType', label: '是否包边' },
    { sortable: 'custom', width: '80', align: 'left', prop: 'isDK', label: '是否打扣', formatter: (row) => row.isDK ? '打扣' : row.isDK == false ? '不打扣' : row.isDK },
    { sortable: 'custom', width: '110', align: 'left', prop: 'isNeedXP', label: '是否需要吸盘', formatter: (row) => row.isNeedXP ? '需要吸盘' : row.isNeedXP == false ? '不需要吸盘' : row.isNeedXP },
    { sortable: 'custom', width: '130', align: 'left', prop: 'isNeedMST', label: '是否送魔术贴', formatter: (row) => row.isNeedMST ? '送魔术贴' : row.isNeedMST == false ? '不送魔术贴' : row.isNeedMST },
    { sortable: 'custom', width: '200', align: 'left', prop: 'fullNormsName', label: '规格全称', },
    { sortable: 'custom', width: '110', align: 'center', prop: 'sheetSquareCost', label: '单张平方价格', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'metreBBClothCost', label: '包边布一米价格', },
    { sortable: 'custom', width: '95', align: 'center', prop: 'sheetBBPackProcessCost', label: '包边加工费', },
    { sortable: 'custom', width: '115', align: 'center', prop: 'sheetSixIronbuckleCost', label: '单张铁扣', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'sheetDKPackProcessCost', label: '打扣加工费', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'sheetSixXPCost', label: '单张吸盘', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'sheetMSTCost', label: '单张魔术贴', },
    { sortable: 'custom', width: '60', align: 'center', prop: 'sheetDishClothcCost', label: '抹布', },
    { sortable: 'custom', width: '60', align: 'center', prop: 'sheetSprayKettleCost', label: '喷壶', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'sheetCroppingCost', label: '单张裁剪', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'orderFreightBagFee', label: '快递袋费', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'orderPackProcessFee', label: '打包费', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'sheetSquareSaleAmount', label: '单张平方售价', },
    { sortable: 'custom', width: '95', align: 'center', prop: 'goodsCode', label: '商品编码', formatter: (row) => row.goodsCode !== null ? row.goodsCode : '' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            ys,
            that: this,
            ListInfo: {
                orderBy: null,
                isAsc: false,
            },
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            drawer: false,
            formData: [
                {
                    norms: null,//规格
                    bbType: null,//是否包边
                    isDK: null,//是否打扣
                    isNeedXP: null,//是否需要吸盘
                    fullNormsName: null,//规格全称
                }
            ],
            isCbSet: true,
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async submit() {
            this.formData.forEach((item, i) => {
                for (const key in item) {
                    if ((key == 'norms' || key == 'bbType' || key == 'isDK' || key == 'isNeedXP') && (item[key] === null || item[key] === '')) {
                        this.$message.error(`第${i + 1}行${ys[key]}为空的数据,请检查`)
                        throw new Error(`第${i + 1}行${ys[key]}为空的数据,请检查`)
                    }
                    if (this.isCbSet) {
                        if ((key.includes('Amount') || key.includes('Cost') || key.includes('Fee')) && key != 'sheetSquareSaleAmount' && key != 'goodsCode') {
                            if (item[key] === null || item[key] === '' || item[key] === undefined || item[key] < 0) {
                                //根据key的值,提示对应的字段
                                this.$message.error(`第${i + 1}行${ys[key]}有为空或小于0的数据,请检查`)
                                throw new Error(`第${i + 1}行${ys[key]}有为空的或小于0数据,请检查`)
                            }
                        }
                    } else {
                        if ((key.includes('Amount') || key.includes('Cost') || key.includes('Fee')) && key != 'sheetSquareCost' || key == 'goodsCode') {
                            if (item[key] === null || item[key] === '' || item[key] === undefined || item[key] < 0) {
                                //根据key的值,提示对应的字段
                                this.$message.error(`第${i + 1}行${ys[key]}有为空或小于0的数据,请检查`)
                                throw new Error(`第${i + 1}行${ys[key]}有为空的或小于0数据,请检查`)
                            }
                        }
                    }
                }
            })
            if (this.isCbSet) {
                const { success } = await saveCostSetByYGGRM(this.formData)
                if (success) {
                    await this.getList()
                    this.$message.success('保存成功')
                    this.drawer = false
                } else {
                    this.$message.error('保存失败')
                }
            } else {
                const { success } = await saveSalePriceSetByYGGRM(this.formData)
                if (success) {
                    await this.getList()
                    this.$message.success('保存成功')
                    this.drawer = false
                } else {
                    this.$message.error('保存失败')
                }
            }
        },
        changeName() {
            //如果有规格,是否包边,是否打扣,是否需要吸盘,则拼接规格全称
            this.formData.forEach(item => {
                item.fullNormsName = `${item.norms ? item.norms : ''},${item.bbType ? item.bbType : ''},${item.isDK ? '打扣' : '不打卡扣'},${item.isNeedXP ? '要吸盘' : '不要吸盘'},${item.isNeedMST ? '送魔术贴' : '不送魔术贴'}`
            })
        },
        addProps() {
            this.formData.push({
                norms: null,//规格
                bbType: null,//是否包边
                isDK: false,//是否打扣
                isNeedXP: false,//是否需要吸盘
                fullNormsName: null,//规格全称
                sheetSquareCost: null,//单张平方价格
                metreBBClothCost: null,//包边布一米价格
                sheetBBPackProcessCost: null,//包边加工费
                sheetSixIronbuckleCost: null,//单张铁扣
                sheetDKPackProcessCost: null,//打扣加工费
                sheetSixXPCost: null,//单张吸盘
                sheetDishClothcCost: null,//抹布
                sheetSprayKettleCost: null,//喷壶
                sheetCroppingCost: null,//单张裁剪
                orderFreightBagFee: null,//快递袋费
                orderPackProcessFee: null,//打包费
                sheetSquareSaleAmount: null,//单张平方售价
            })
        },
        async handleAdd(isCbSet) {
            this.isCbSet = isCbSet
            const { data, success } = await getCostSetByYGGRM(this.ListInfo)
            if (success) {
                this.formData = data
                this.drawer = true
            } else {
                this.$message.error('获取数据失败')
                this.drawer = true
            }
        },
        async getList(isFresh) {
            this.ListInfo.orderBy = isFresh ? null : this.ListInfo.orderBy
            this.ListInfo.isAsc = isFresh ? false : this.ListInfo.isAsc
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            const { data, success } = await getCostSetByYGGRM(this.ListInfo)
            if (success) {
                data.forEach((item, i) => {
                    item.isDKStr = item.isDK ? '打扣' : '不打扣'
                    item.isNeedXPStr = item.isNeedXP ? '需要吸盘' : '不需要吸盘'
                    item.isNeedMSTStr = item.isNeedMST ? '送魔术贴' : '不送魔术贴'
                })
                this.tableData = data
                this.loading = false
            } else {
                //获取列表失败
                this.loading = false
                this.$message.error('获取数据失败')
            }
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.iptCss {
    width: 70px;
}

.btnGroup {
    margin-top: 80px;
    display: flex;
    justify-content: end;
}

::v-deep .el-table__body-wrapper {
    min-height: 300px !important;
    max-height: 400px !important;
}

::v-deep .cell {
    padding-left: 3px;
}
</style>
