<template>
    <MyContainer v-loading="loading">
        <template #header>
            <div class="top">
                <el-select v-model="ListInfo.dbName" placeholder="请选择数据库" class="publicCss" clearable>
                    <el-option v-for="item in nameList" :key="item" :label="item" :value="item">
                    </el-option>
                </el-select>
                <el-input v-model="ListInfo.title" placeholder="标题" class="publicCss" clearable maxlength="200" />
                <el-input v-model="ListInfo.remark" placeholder="描述" class="publicCss" clearable maxlength="200" />
                <el-input v-model="ListInfo.sql" placeholder="sql" class="publicCss" clearable maxlength="200" />
                <el-button type="primary" @click="getList('true')">查询</el-button>
                <el-button type="primary" @click="addDb">新增</el-button>
            </div>
        </template>
        <vxetablebase :id="'selfServicelnquiries202408041736'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" v-loading="loading"
            style="width: 100%; height: 100%; margin: 0" />
        <el-drawer :title="isEdit ? '编辑报表' : '新增报表'" :visible.sync="drawer" direction="rtl" :size="'60%'"
            :wrapperClosable="false">
            <SelfServiceTable v-if="drawer" ref="SelfServiceTable" :nameList="nameList" :close="close" @getList="getList"
                :editPropsList="editPropsList" :isEdit="isEdit" />
        </el-drawer>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { replaceSpace } from '@/utils/getCols'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getDbNames, pageGetReports, mergeReporter, getReporterData, deleteReporter } from '@/api/bookkeeper/reporter'
import SelfServiceTable from './selfServiceTable.vue'
const tableCols = [
    { istrue: true, prop: 'createdTime', label: '创建时间', width: 'auto' },
    { istrue: true, prop: 'title', label: '标题', width: 'auto' },
    { istrue: true, prop: 'dbName', label: '数据库名称', width: 'auto' },
    { istrue: true, prop: 'sql', label: 'sql语句', width: 'auto' },
    { istrue: true, prop: 'remark', label: '描述', width: 'auto' },
    { istrue: true, prop: 'updateTime', label: '更新时间', width: 'auto' },
    {
        istrue: true, label: '操作', width: 'auto', type: 'button', btnList: [
            { label: "编辑", handle: (that, row) => that.editProps(row) },
            { label: "删除", handle: (that, row) => that.delProps(row) }
        ]
    },
]
export default {
    components: { MyContainer, vxetablebase, SelfServiceTable },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 10000,
                orderBy: null,
                isAsc: true,
                title: null,//标题
                remark: null,//备注
                dbName: null,//数据库名称
                sql: null,//sql语句
                routeUrl: null,//路由地址
            },
            total: 0,//总条数
            textarea: '',
            loading: false,//加载中
            tableCols,//动态表头
            tableData: [],//表格数据
            showsummary: false,//是否汇总
            summaryarry: {},//汇总字段
            nameList: [],//数据库名称列表
            drawer: false,//抽屉
            editPropsList: null,//编辑数据
            isEdit: false,//是否编辑
        }
    },
    async mounted() {
        await this.getDbNameList()
        this.getList()
    },
    methods: {
        async editProps(row) {
            this.isEdit = true
            const { data, success } = await getReporterData(row.id)
            if (success) {
                // data.userIds = data.userIds ? data.userIds.split(',') : []
                this.editPropsList = data
                this.drawer = true
            }
        },
        delProps(row) {
            console.log(row, 'row');
            console.log(row.id, 'row.id');
            this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await deleteReporter(row.id)
                if (success) {
                    await this.getList()
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        close() {
            this.$refs.SelfServiceTable.clear()
            this.drawer = false
        },
        addDb() {
            this.isEdit = false
            this.drawer = true
        },
        //获取数据库名称
        async getDbNameList() {
            const { data, success } = await getDbNames()
            if (success) {
                this.nameList = data
            } else {
                this.$message.error('获取数据库名称失败')
            }
        },
        async getList(isSearch) {
            isSearch ? this.ListInfo.currentPage = 1 : null
            const replaceArr = ['title', 'remark']
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            const { data, success } = await pageGetReports(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 20px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }

    .btnCss {
        width: 100px;
    }
}

.main {
    display: flex;
    width: 100%;
    height: 100%;

    .main_left {
        box-sizing: border-box;
        padding: 10px;
        width: 20%;
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 88vh;
        overflow: auto;
        border: 1px solid #ebeef5;
        margin-right: 10px;

        .main_left_tableName {
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .main_right {
        flex: 1;
        height: 88vh;
    }
}
</style>