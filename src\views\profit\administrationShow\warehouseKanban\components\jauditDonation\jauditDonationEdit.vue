<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" ref="refruleForm" label-width="140px" class="demo-ruleForm">
        <div style="font-size: 15px; font-weight: 600; margin-left: 20px">仓储6s稽查问题数量</div>
        <el-form-item label="员工违纪：" prop="employeeViolation">
          <inputNumberYh v-model="ruleForm.employeeViolation" :placeholder="'员工违纪数量'" class="publicCss" />
        </el-form-item>
        <el-form-item label="仓储卫生：" prop="employeeCount">
          <inputNumberYh v-model="ruleForm.employeeCount" :placeholder="'仓储卫生问题数量'" class="publicCss" />
        </el-form-item>
        <el-form-item label="仓储消防：" prop="donationAmount">
          <inputNumberYh v-model="ruleForm.donationAmount" :placeholder="'仓储消防问题数量'" class="publicCss" />
        </el-form-item>
        <el-form-item label="仓储整理整顿：" prop="warehouseOrganization">
          <inputNumberYh v-model="ruleForm.warehouseOrganization" :placeholder="'仓储整理整顿问题数量'" class="publicCss" />
        </el-form-item>
        <el-form-item label="掉货/错货：" prop="goodsLossOrError">
          <inputNumberYh v-model="ruleForm.goodsLossOrError" :placeholder="'掉货/错货数量'" class="publicCss" />
        </el-form-item>
        <el-form-item label="其他问题：" prop="otherIssues">
          <inputNumberYh v-model="ruleForm.otherIssues" :placeholder="'其他问题数量'" class="publicCss" />
        </el-form-item>
        <el-form-item label="乐捐总金额：" prop="totalDonationAmount">
          <inputNumberYh v-model="ruleForm.totalDonationAmount" :fixed="2" :placeholder="'乐捐总金额'" class="publicCss" />
        </el-form-item>
        <el-form-item label="备注：" prop="remarks">
          <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 4 }" placeholder="请输入内容" maxlength="100"
            show-word-limit v-model="ruleForm.remarks" class="publicCss">
          </el-input>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { warehouseInspectionDonationSubmit } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission'
export default {
  name: 'jauditDonationEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      ruleForm: {
        employeeViolation: null,
        employeeCount: null,
        donationAmount: null,
        warehouseOrganization: null,
        goodsLossOrError: null,
        otherIssues: null,
        totalDonationAmount: null,
        remarks: ''
      },
      rules: {
        employeeViolation: [
          { required: true, message: '请输入员工违纪数量', trigger: 'blur' },
          { type: 'integer', min: 0, message: '请输入有效的整数', trigger: 'blur' }
        ],
        employeeCount: [
          { required: true, message: '请输入仓储卫生问题数量', trigger: 'blur' },
          { type: 'integer', min: 0, message: '请输入有效的整数', trigger: 'blur' }
        ],
        donationAmount: [
          { required: true, message: '请输入仓储消防问题数量', trigger: 'blur' },
          { type: 'integer', min: 0, message: '请输入有效的整数', trigger: 'blur' }
        ],
        warehouseOrganization: [
          { required: true, message: '请输入仓储整理整顿问题数量', trigger: 'blur' },
          { type: 'integer', min: 0, message: '请输入有效的整数', trigger: 'blur' }
        ],
        goodsLossOrError: [
          { required: true, message: '请输入掉货/错货数量', trigger: 'blur' },
          { type: 'integer', min: 0, message: '请输入有效的整数', trigger: 'blur' }
        ],
        otherIssues: [
          { required: true, message: '请输入其他问题数量', trigger: 'blur' },
          { type: 'integer', min: 0, message: '请输入有效的整数', trigger: 'blur' }
        ],
        totalDonationAmount: [
          { required: true, message: '请输入乐捐总金额', trigger: 'blur' },
          { type: 'number', min: 0, message: '请输入有效的金额', trigger: 'blur' }
        ],
        remarks: [
          { max: 100, message: '备注内容不能超过100个字符', trigger: 'blur' }
        ]
      }
    }
  },
  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };
  },
  methods: {
    cancellationMethod() {
      this.$emit('cancellationMethod');
    },
    async submitForm(formName) {
      try {
        const valid = await this.$refs[formName].validate();
        if (valid) {
          this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing");
          const { success, msg } = await warehouseInspectionDonationSubmit(this.ruleForm);

          if (success) {
            this.$message.success(msg || '保存成功');
            this.$emit("search");
          } else {
            this.$message.error(msg || '保存失败');
          }
        }
      } catch (error) {
        console.error('表单提交失败:', error);
        this.$message.error('保存失败，请重试');
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    }
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}
</style>
