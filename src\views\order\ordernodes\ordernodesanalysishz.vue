<template>
  <div>
    <el-table :data="tableData" :span-method="objectSpanMethod" border style="width: 100%; margin-top: 20px">
      <el-table-column prop="jieDian" label="节点" width="180"></el-table-column>
      <el-table-column prop="gan<PERSON>ei" label="岗位"></el-table-column>
      <el-table-column prop="spanTime" label="平均时长(分钟)">
        <template slot-scope="scope">
            <el-link type="primary" @click="showprchart(scope.row)">{{scope.row.spanTime}}趋势图</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="count" label="订单量/补货任务">
        <template slot-scope="scope">
            <el-link type="primary" @click="showprchart(scope.row)">{{scope.row.count}}趋势图</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="perCount" label="人员数量"> 
        <template slot-scope="scope">
            <el-link type="primary" @click="showprchart(scope.row)">{{scope.row.perCount}}趋势图</el-link>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
        <span>
            <template>
                <el-form class="ad-form-query" :model="filter" @submit.native.prevent label-width="100px">
                    <el-row>
                        <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
                            <el-form-item label="日期:">
                              <el-date-picker style="width: 210px" v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd"
                                   value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                            <el-form-item>
                                <el-button type="primary" @click="getechart">刷新</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
        </template> 
        </span>
        <span>
            <buschar v-if="buscharDialog.visible" ref="buschar" :analysisData="buscharDialog.data"></buschar>
        </span>
        <span slot="footer" class="dialog-footer">
            <el-button @click="buscharDialog.visible = false">关闭</el-button>
        </span>
    </el-dialog>
  </div>
</template>

<script>
import {getOrderPositionByPositionAnalysis,getOrderPositionByPositionlist} from '@/api/order/ordernodes'
import buschar from '@/components/Bus/buschar'
  export default {
    components: {buschar},
    props:{ 
      filter:{}
    },
    data() {
      return {
         selectrow:{},
         tableData:[],
         buscharDialog:{visible:false,title:"",data:[]},
      };
    },
  async created() {
    // await this.onSearch()
   },
    methods: {
      async onSearch() {
          const params = {...this.filter};
          params.orderError=null;
          var res= await getOrderPositionByPositionlist(this.filter)
          if(res.code==1)
             this.tableData=res.data;
      },
      objectSpanMethod({ row, column, rowIndex, columnIndex }) {
        if (columnIndex === 0) {
          if (rowIndex  === 0||rowIndex  === 3) {
            return { rowspan: 2, colspan: 1 };
          }
          else if (rowIndex  === 2) {
            return { rowspan: 1, colspan: 1 };
          } 
          else if (rowIndex  === 5) {
            return { rowspan: 4, colspan: 1 };
          }else {
            return {rowspan: 0,colspan: 0};
          }
        }
      },
      async getechart(){
       await this.showprchart(this.selectrow)
      },
      async showprchart(row){
          this.selectrow=row;
          this.filter.startTime = null
          this.filter.endTime =  null
          if (this.filter.timerange&&this.filter.timerange.length>1) {
              this.filter.startTime = this.filter.timerange[0];
              this.filter.endTime = this.filter.timerange[1];
          }
          else {
              this.$message({message:"请先选择日期",type:"warning"});
              return false;
          }
          const params = {...this.filter};
          params.nodeIndex = row.nodeIndex
          params.orderPosition= row.position
          if(row.ganWei=='异常订单') params.orderError=true;
          else if(row.ganWei=='正常订单') params.orderError=false;
          else params.orderError=null;
          let that=this;
          const res = await getOrderPositionByPositionAnalysis(params).then(res=>{
          that.buscharDialog.visible=true;
          that.buscharDialog.data=res.data
          that.buscharDialog.title=row.jieDian+'_'+row.ganWei;
          });
          await this.$refs.buschar.initcharts()
        },
    }
  };
</script>