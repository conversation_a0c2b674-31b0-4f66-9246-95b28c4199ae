<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="ListInfo.po_Ids" placeholder="采购单号" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.sku_ids" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.adder" placeholder="添加人" maxlength="5" clearable class="publicCss" />
        <el-date-picker v-model="ListInfo.addTime" type="date" placeholder="选择日期" :value-format="'yyyy-MM-dd'"
          style="width: 200px;margin-right: 5px;">
        </el-date-picker>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="onSignMethod">登记</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'refundModule202302031421'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='false' style="width: 100%;  margin: 0"
      :loading="loading" :height="'100%'" :treeProp="{ rowField: 'id', parentField: 'parentId' }">
      <template slot="right">
        <vxe-column title="操作" width="80" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;" v-if="row.parentId == 0">
              <el-button type="text" @click="handleEdit(row)" :disabled="row.status == '已审核'">编辑</el-button>
              <!-- <el-button type="text" @click="handleCancel(row)">撤销</el-button> -->
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <vxe-modal v-model="dialogMapVisible" title="登记" width="80%" :mask-closable="true" marginSize='-500'
      style="margin-top: -10vh;">
      <div style="height: 700px;">
        <registration ref="registration" v-if="dialogMapVisible" @onStorageMethod="onStorageMethod"
          :purchaseNumber="purchaseNumber" :verify="verify" @onUnpackParent="onUnpackParent"
          @onClosesublevel="onClosesublevel" />
      </div>
      <div style="display: flex; justify-content: center; gap: 20px;margin-top: 10px;">
        <el-button @click="dialogMapVisible = false">取 消</el-button>
        <el-button type="primary" @click="onStorageMethodDebounced(1)" :loading="sublevelLoading">
          {{ (sublevelLoading ? '保存中' : '保 存') }}
        </el-button>
      </div>
    </vxe-modal>
    <!-- <el-dialog title="登记" :visible.sync="dialogMapVisible" width="80%" v-dialogDrag style="margin-top: -10vh;"
      :close-on-click-modal="false">
    </el-dialog> -->
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/vxetablebase.vue";
import { pickerOptions } from '@/utils/tools'
import registration from "./registration.vue";
import { getPurchaseOrderRefundDetails, cancelPurchaseOrderRefundDetails, exportPurchaseOrderRefundDetailsERP, getPurchaseOrderRefundDetailsERP } from '@/api/inventory/purchase'
import dayjs from 'dayjs'
const tableCols = [
  // { sortable: 'custom', width: '100', align: 'center', prop: 'batchNumber', label: '批次号', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'status', label: '状态', treeNode: true, },
  { sortable: 'custom', width: '100', align: 'center', prop: 'po_Id', label: '采购单号', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isPay', label: '是否付款', },
  // { sortable: 'custom', width: '100', align: 'center', prop: 'sku_id', label: '商品编码', },
  // { sortable: 'custom', width: '100', align: 'center', prop: 'price', label: '单价', },
  // { sortable: 'custom', width: '100', align: 'center', prop: 'buy_qty', label: '采购数量', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'buy_Amount', label: '采购金额', },
  // { sortable: 'custom', width: '100', align: 'center', prop: 'qty', label: '已入库数量', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'cost_Amount2', label: '已入库金额', },//cost_Amount
  // { sortable: 'custom', width: '100', align: 'center', prop: 'pending_qty', label: '未入库数量', },
  // { sortable: 'custom', width: '100', align: 'center', prop: 'cost_Amount', label: '退货数量', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'pendingAmount', label: '未入库金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'returnAmount', label: '退货款', },
  // { sortable: 'custom', width: '100', align: 'center', prop: 'adjustAmount', label: '调整金额', },
  // { sortable: 'custom', width: '100', align: 'center', prop: 'adjustType', label: '调整类型', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'returnFreight', label: '退运费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'totalRefunsds', label: '总退款', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'refundDatetime', label: '退款时间', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'collectType', label: '收款方式', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'collectUser', label: '收款人', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'collectAccount', label: '收款账号', },
  { width: '100', align: 'center', prop: 'annexUrl', label: '附件', type: "images", },
  { sortable: 'custom', width: '100', align: 'center', prop: 'adder', label: '添加人', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'addTime', label: '添加时间', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'remark', label: '备注', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'deductAmount', label: '扣除金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'payAmount', label: '付款金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'rejectReason', label: '驳回原因', },
]
export default {
  name: "refundModule",
  components: {
    MyContainer, vxetablebase, registration
  },
  data() {
    return {
      sublevelLoading: false,
      verify: false,
      purchaseNumber: [],
      dialogMapVisible: false,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'addTime',
        isAsc: false,
        addTime: null,//时间
        po_Ids: '',//采购单号
        sku_ids: '',//商品编码
        adder: '',//添加人
      },
      timeRanges: '',
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async exportProps() {
      this.loading = true
      const { data } = await exportPurchaseOrderRefundDetailsERP(this.ListInfo)
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '采购单退款导出数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    handleCancel(row) {
      this.$confirm('是否确认撤销？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let poid = ''
        this.tableData.forEach(item => {
          if (item.parentId == row.id) {
            poid = item.po_Id
          }
        })
        let params = []
        params.push(poid)
        const { data, success } = await cancelPurchaseOrderRefundDetails(params)
        if (success) {
          this.getList()
          this.$message({
            type: 'success',
            message: '撤销成功!'
          });
        } else {
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消撤销'
        });
      });
    },
    handleEdit(row) {
      this.purchaseNumber = []
      this.tableData.forEach(item => {
        if (item.parentId == row.id) {
          this.purchaseNumber.push(item.po_Id)
          this.purchaseNumber = Array.from(new Set(this.purchaseNumber))
        }
      })
      this.verify = false
      this.dialogMapVisible = true
    },
    onStorageMethod() {
      this.dialogMapVisible = false
      this.ListInfo.orderBy = 'addTime'
      this.ListInfo.isAsc = false
      this.getList()
    },
    //防抖
    onStorageMethodDebounced: _.debounce(function (param) {
      this.onSortSave(param);
    }, 2000),
    onClosesublevel() {
      this.sublevelLoading = false
    },
    onUnpackParent() {
      this.sublevelLoading = true
    },
    onSortSave() {
      this.$nextTick(() => {
        this.$refs.registration.onSave()
      })
    },
    onSignMethod() {
      this.verify = true
      this.purchaseNumber = []
      this.dialogMapVisible = true
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getPurchaseOrderRefundDetailsERP(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.addTime = item.addTime ? dayjs(item.addTime).format('YYYY-MM-DD') : ""
          item.refundDatetime = item.refundDatetime ? dayjs(item.refundDatetime).format('YYYY-MM-DD') : ""
          item.annexUrl = JSON.stringify(item.annexUrl ? item.annexUrl.map(url => ({ url })) : []);
        })
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
refundModule
