<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" @submit.native.prevent>
                <el-form-item label="操作人:" label-position="right" label-width="72px">
                    <el-input v-model.trim="filter.createdUserName" style="width: 150px" placeholder="操作人" maxlength="20"
                        clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>

        <ces-table :tablekey="'purchasequalitylog20230811z1124'" ref="tableCols" :hasexpandRight='true' :showsummary='true'
            :that='that' :isIndex='true' :tablefixed='true' :hasexpand='false' :isSelectColumn="false" :tableData='list'
            :tableCols='tableCols' :tableHandles='tableHandles' @sortchange="sortchange" :loading="listLoading"
              :border="true">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

    </container>
</template>

<script>
import { formatTime } from "@/utils";
import { getGoodsCostChgInfoLogAsync } from "@/api/inventory/basicgoods"
import container from "@/components/my-container";
import cesTable from "@/components/Table/table.vue";

const tableCols = [
    { istrue: true, prop: 'logType', label: '主题', width: '150' },
    { istrue: true, prop: 'logInfo', label: '备注' },
    { istrue: true, prop: 'createdUserName', label: '操作人', width: '100' },
    { istrue: true, prop: 'createTime', label: '操作时间', width: '160', sortable: 'custom', formatter: (row) => formatTime(row.createTime, 'YYYY-MM-DD HH:mm:ss') }];
const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
];



export default {
    name: 'YunHanAdminGoodscostchginfolog',
    components: { container, cesTable },
    props: {
        filter: {}
    },

    data() {
        return {
            that: this,
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "createTime", IsAsc: false },
            total: 0,
            sels: [],
            selids: [],
            pageLoading: false,
            listLoading: false
        };
    },

    async mounted() {
        //await this.onSearch();
    },

    methods: {
        async onSearch() {
            console.log('参数来了', this.filter)
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        async getlist() {
            if (!this.pager.OrderBy) {
                this.pager.OrderBy = "createTime";
                this.pager.IsAsc = false;
            }
            let pager = this.$refs.pager.getPager()
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter,
            }
            this.listLoading = true
            let res = await getGoodsCostChgInfoLogAsync(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total
            this.list = res.data.list
        },

        async cellclick(row, column, cell, event) {

        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>


</style>
