<template>
  <my-container v-loading="pageLoading">
    <template #header> </template>
    <template>
      <div style="padding: 0px 20px 100px 0px;margin-left:-30px;">
        <el-row :gutter="0" class="row-condition">
          <el-col :span="2" :offset="1">
            <div class="my-title">数据概况</div>
          </el-col>
          <el-col :span="3" :offset="9">
            <el-button
              type="text"
              class="my-txtbtn dateBtn"
              >昨天
              <el-date-picker
                v-model="filter.dateModel.day" @change="clickYesterdaySum"
                :picker-options="pickOptions"
                type="date"  
                format="yyyy-MM-dd">
            </el-date-picker>
            </el-button>

            <el-button
              type="text"
              class="my-txtbtn dateBtn"
              >周
              <el-date-picker
                v-model="filter.dateModel.week" @change="clickWeekSum"
                type="week" 
                :picker-options="pickOptionsWeek"       
                format="yyyy-MM-dd">
            </el-date-picker>
            </el-button>
            <el-button
              type="text"
              class="my-txtbtn dateBtn"
              >月
              <el-date-picker
                v-model="filter.dateModel.month" @change="clickMonthSum"
                type="month"      
                :picker-options="pickOptions"         
                format="yyyy-MM-dd">
            </el-date-picker>
            </el-button>
          </el-col>
          <el-col :span="5" :offset="0">
            <el-date-picker
              v-model="filter.timeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="onSearch"
              format="yyyy-MM-dd"
              :picker-options="pickOptions"
            >
            </el-date-picker>
          </el-col>
        </el-row>
        <el-form
          class="ad-form-query2"
          :inline="true"
          :model="filter"
          @submit.native.prevent
          label-position="right"
          label-width="80px"
        >
          <el-form-item label="仓库:">
            <el-select
              v-model="filter.sendWarehouse"
              placeholder="请选择"
              multiple
              :clearable="true" :collapse-tags="true"
            >
              <el-option
                v-for="item in sendWarehouseList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="平台:">
            <el-select
              v-model="filter.platform"
              placeholder="请选择"
              @change="onchangeplatform"
              multiple
              :clearable="true" :collapse-tags="true"
            >
              <el-option
                v-for="item in platformList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="店铺:">
            <el-select
              v-model="filter.shopCode"
              placeholder="请选择"
              multiple
              :clearable="true" :collapse-tags="true"  filterable
            >
              <el-option
                v-for="item in shopList"
                :key="item.shopCode"
                :label="item.shopName"
                :value="item.shopCode"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="运营组:">
            <el-select
              v-model="filter.groupId"
              placeholder="请选择"
              multiple
              :clearable="true" :collapse-tags="true" filterable
            >
              <el-option
                v-for="item in directorGroupList"
                :key="item.key"
                :label="item.value"
                :value="item.key"/>
            </el-select>
          </el-form-item>
         

         
          <el-form-item label="运营:">
            <el-select
              v-model="filter.directorId"
              placeholder="请选择"
              multiple
              :clearable="true" :collapse-tags="true" filterable
            >
              <el-option
                v-for="item in directorList"
                :key="item.key"
                :label="item.value"
                :value="item.key"/>
            </el-select>
          </el-form-item>
         <el-form-item>
            <el-button type="primary" @click="onSearch">刷新</el-button>
          </el-form-item>
           <br/>

          <el-form-item label="宝贝ID">
            <el-input v-model="filter.proCode" style="width:193px;"></el-input>
          </el-form-item>
          <el-form-item label="商品编码" >
            <el-input v-model="filter.goodsCode" style="width:193px;"></el-input>
          </el-form-item>
          
        </el-form>
        <el-row :gutter="20">
          <el-col :span="4" :offset="1">
            <div class="grid-header">
              总订单
              <el-tooltip
                class="item"
                effect="dark"
                content="内部订单号数量"
                placement="top-end"
              >
                <span><i class="el-icon-question"></i></span>
              </el-tooltip>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="grid-header">
              已发货
              <el-tooltip
                class="item"
                effect="dark"
                content="有快递单的内部订单号数量"
                placement="top-end"
              >
                <span><i class="el-icon-question"></i></span>
              </el-tooltip>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="grid-header">
              未发货
              <el-tooltip
                class="item"
                effect="dark"
                content="无快递单的内部订单号数量"
                placement="top-end"
              >
                <span><i class="el-icon-question"></i></span>
              </el-tooltip>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="grid-header">
              发货时长
              <el-tooltip
                class="item"
                effect="dark"
                content="（【发货日期】-【付款日期】）之和 / 【内部订单号数量】"
                placement="top-end"
              >
                <span><i class="el-icon-question"></i></span>
              </el-tooltip>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="grid-header">
              延迟发货扣款
              <el-tooltip
                class="item"
                effect="dark"
                content="来源于订单违规扣款模块"
                placement="top-end"
              >
                <span><i class="el-icon-question"></i></span>
              </el-tooltip>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="row-value">
          <el-col :span="4" :offset="1">
            <div class="grid-content" v-loading="loadingDataSum.orderCount">{{ formattedTotalWaitOrderNum1 }}</div>
          </el-col>
          <el-col :span="4">
            <div class="grid-content" v-loading="loadingDataSum.orderCountSended">{{ formattedTotalWaitOrderNum2 }}</div>
          </el-col>
          <el-col :span="4">
            <div class="grid-content canclick" v-loading="loadingDataSum.orderCountNotSend" @click="showDialogOrderCountNotSend">{{ formattedTotalWaitOrderNum3 }}</div>
          </el-col>
          <el-col :span="4">
            <div class="grid-content " v-loading="loadingDataSum.timeRange" @click="showDialogTimeRange">
              {{ formattedTotalWaitOrderNum4 }}<span class="content-unit"> 小时</span>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="grid-content canclick" v-loading="loadingDataSum.deductMoneyDelay"  @click="showDialogDeductMoney">
              {{ formattedTotalWaitOrderNum5 }}<span class="content-unit"> 元</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="4" :offset="1">
            <div class="grid-header">
              &emsp;总平均快递费
              <el-tooltip
                class="item"
                effect="dark"
                content="总快递费/快递单量"
                placement="top-end"
              >
                <span><i class="el-icon-question"></i></span>
              </el-tooltip>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="grid-header">
              &emsp;总快递费
              <el-tooltip
                class="item"
                effect="dark"
                content="按重量分摊到编码上的快递费之和"
                placement="top-end"
              >
                <span><i class="el-icon-question"></i></span>
              </el-tooltip>
            </div>
          </el-col>
          <el-col :span="4" :offset="0">
            <div class="grid-header">
              &emsp;面单费
              <el-tooltip
                class="item"
                effect="dark"
                content="按重量分摊到编码上的面单费之和"
                placement="top-end"
              >
                <span><i class="el-icon-question"></i></span>
              </el-tooltip>
            </div>
          </el-col>          
          <el-col :span="4">
            <div class="grid-header">
              总续重费
              <el-tooltip
                class="item"
                effect="dark"
                content="按重量分摊到编码上的续重费之和"
                placement="top-end"
              >
                <span><i class="el-icon-question"></i></span>
              </el-tooltip>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="grid-header">
              &emsp;快递罚款
              <el-tooltip
                class="item"
                effect="dark"
                content="快递违规罚款之和"
                placement="top-end"
              >
                <span><i class="el-icon-question"></i></span>
              </el-tooltip>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="row-value">
          <el-col :span="4" :offset="1">
            <div class="grid-content canclick" v-loading="loadingDataSum.avgFreightMoney" @click="showDialogExpress">
              {{ formattedTotalWaitOrderNum6 }}<span class="content-unit"> 元</span>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="grid-content" v-loading="loadingDataSum.sumFreightMoney" >
              {{ formattedTotalWaitOrderNum7 }}<span class="content-unit"> 元</span>
            </div>
          </el-col>
          <el-col :span="4" :offset="0">
            <div class="grid-content" v-loading="loadingDataSum.faceFee">
              {{ formattedTotalWaitOrderNum8 }}<span class="content-unit"> 元</span>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="grid-content" v-loading="loadingDataSum.continuedHeavyFee" >
              {{ formattedTotalWaitOrderNum9
              }}<span class="content-unit"> 元</span>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="grid-content canclick" v-loading="loadingDataSum.deductMoneyTaboo" @click="showDialogDeductMoneyTaboo">
              {{ formattedTotalWaitOrderNum10
              }}<span class="content-unit"> 元</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="4" :offset="1">
            <div class="grid-header">
              分摊后平均快递费
              <el-tooltip
                class="item"
                effect="dark"
                content="分摊总快递费/快递单量"
                placement="top-end"
              >
                <span><i class="el-icon-question"></i></span>
              </el-tooltip>
            </div>
          </el-col>          
          <el-col :span="4" :offset="0">
            <div class="grid-header">
              编码退货率
              <el-tooltip
                class="item"
                effect="dark"
                content="退货订单商品编码个数之和/所有订单商品编码个数之和"
                placement="top-end"
              >
                <span><i class="el-icon-question"></i></span>
              </el-tooltip>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="grid-header">
              ID退货率
              <el-tooltip
                class="item"
                effect="dark"
                content="ID退货订单数量之和/ID订单数量之和"
                placement="top-end"
              >
                <span><i class="el-icon-question"></i></span>
              </el-tooltip>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="grid-header">
              总毛利率
              <el-tooltip
                class="item"
                effect="dark"
                content="（销售金额-成本）/销售金额"
                placement="top-end"
              >
                <span><i class="el-icon-question"></i></span>
              </el-tooltip>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="grid-header">
              代发金额
              <el-tooltip
                class="item"
                effect="dark"
                content="代拍商品代拍金额之和"
                placement="top-end"
              >
                <span><i class="el-icon-question"></i></span>
              </el-tooltip>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="row-value">   
          <el-col :span="4" :offset="1">
            <div class="grid-content" v-loading="loadingDataSum.avgFreightMoneyShare" >
              {{ formattedTotalWaitOrderNum11 }}<span class="content-unit"> 元</span>
            </div>
          </el-col>      
          <el-col :span="4" :offset="0">
            <div class="grid-content" v-loading="loadingDataSum.returnRate" >
              {{ formattedTotalWaitOrderNum12 }}<span class="content-unit"> %</span>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="grid-content" v-loading="loadingDataSum.returnRateProcode">
              {{ formattedTotalWaitOrderNum13
              }}<span class="content-unit"> %</span>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="grid-content" v-loading="loadingDataSum.profitRate" >
              {{ formattedTotalWaitOrderNum14 }}<span class="content-unit"> %</span>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="grid-content canclick"  v-loading="loadingDataSum.agentAmount" @click="showDialogAgentAmount">
              {{ formattedTotalWaitOrderNum15
              }}<span class="content-unit"> 元</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="0" class="row-condition row-report">
          <el-col :span="2" :offset="1">
            <div class="my-title">整体看板</div>
          </el-col>
          <el-col :span="3" :offset="9">
            <el-button
              type="text"
              class="my-txtbtn dateBtn"
              >日
              <el-date-picker
                v-model="filterCharts.dateModel.day" @change="clickYesterdayCharts"
                type="date"
                :picker-options="pickOptions"
                format="yyyy-MM-dd">
            </el-date-picker>
            </el-button>

            <el-button
              type="text"
              class="my-txtbtn dateBtn"
              >周
              <el-date-picker
                v-model="filterCharts.dateModel.week" @change="clickWeekCharts"
                type="week"
                :picker-options="pickOptionsWeek"        
                format="yyyy-MM-dd">
            </el-date-picker>
            </el-button>

            <el-button
              type="text"
              class="my-txtbtn dateBtn"
              >月
              <el-date-picker
                v-model="filterCharts.dateModel.month" @change="clickMonthCharts"
                type="month"
                :picker-options="pickOptions"           
                format="yyyy-MM-dd">
            </el-date-picker>
            </el-button>
          </el-col>
          <el-col :span="5" :offset="0">
            <el-date-picker
              v-model="filterCharts.timeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              :picker-options="pickOptions"
              @change="onSearchCharts"
            >
            </el-date-picker>
          </el-col>

          <el-col :span="2" :offset="0">
            <el-button
              type="text"
              :class="noClickChartsClass"
              @click="clickSelCharts"
              >图表</el-button
            ><span class="my-split"> | </span>
            <el-button
              type="text"
              :class="noClickTableClass"
              @click="clickSelTable"
              >表格</el-button
            >
          </el-col>
        </el-row>

        <el-form
          class="ad-form-query2"
          :inline="true"
          :model="filter"
          @submit.native.prevent
          label-position="right"
          label-width="80px"
        >
          <el-form-item label="仓库:">
            <el-select
              v-model="filterCharts.sendWarehouse"
              placeholder="请选择"
              multiple
              :clearable="true" :collapse-tags="true"
            >
              <el-option
                v-for="item in sendWarehouseList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="平台:">
            <el-select
              v-model="filterCharts.platform"
              placeholder="请选择"
              @change="onchangeplatform"
              multiple
              :clearable="true" :collapse-tags="true"
            >
              <el-option
                v-for="item in platformList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="店铺:">
            <el-select
              v-model="filterCharts.shopCode"
              placeholder="请选择"
              multiple
              :clearable="true" :collapse-tags="true"  filterable
            >
              <el-option
                v-for="item in shopList"
                :key="item.shopCode"
                :label="item.shopName"
                :value="item.shopCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="运营组:">
            <el-select
              v-model="filterCharts.groupId"
              placeholder="请选择"
              multiple
              :clearable="true" :collapse-tags="true" filterable
            >
              <el-option
                v-for="item in directorGroupList"
                :key="item.key"
                :label="item.value"
                :value="item.key"/>
            </el-select>
          </el-form-item>
          
          <el-form-item label="内容项:">
            <el-select
              v-model="filterCharts.keyWord"
              placeholder="请选择"
              multiple
              :clearable="true" :collapse-tags="true"
            >
              <el-option
                v-for="item in Ylist"
                :key="item.value"
                :label="item.label"
                :value="item.value"/>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearchCharts">刷新</el-button>
          </el-form-item>
          <el-form-item v-show="showTable">
            <el-button type="primary" @click="onExport">导出</el-button>
          </el-form-item>
        </el-form>

      <el-row v-show="showTable" style="max-width: 88%; height: 300px;margin-left:30px;">
          <el-col :span="23" :offset="1">
            <el-table :data="tableData" stripe border highlight-current-row :fit="false" height="300" v-loading="loadingMyTable">
              <el-table-column prop="dayStr" label="日期" width="100">
              </el-table-column>
              <el-table-column prop="sendWarehouseName" v-if="filterCharts.sendWarehouse.length>0" label="仓库" width="180">
              </el-table-column>
              <el-table-column prop="platformName" v-if="filterCharts.platform.length>0" label="平台" width="160">
              </el-table-column>
              <el-table-column prop="shopName" v-if="filterCharts.shopCode.length>0" label="店铺" width="160">                
              </el-table-column>
              <el-table-column prop="groupName" v-if="filterCharts.groupId.length>0" label="运营组" width="160">                
              </el-table-column>
              <el-table-column prop="orderCount" v-if="filterCharts.keyWord.indexOf(0) != -1" label="订单数" width="110">
              </el-table-column>
              <el-table-column prop="timeRange" v-if="filterCharts.keyWord.indexOf(1) != -1" label="发货时间（小时）" width="130">
              </el-table-column>
              <el-table-column prop="avgFreightMoney" v-if="filterCharts.keyWord.indexOf(2) != -1" label="平均快递费（元）" width="130">
              </el-table-column>
              <el-table-column prop="profitRate" v-if="filterCharts.keyWord.indexOf(3) != -1" label="毛利率（%）" width="120">
              </el-table-column>
              <el-table-column prop="returnRate" v-if="filterCharts.keyWord.indexOf(4) != -1" label="退货率（%）" width="120">
              </el-table-column>
              <el-table-column prop="avgWeight" v-if="filterCharts.keyWord.indexOf(5) != -1" label="均重（kg）" width="120">
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>

        <el-row v-show="showCharts" v-loading="loadingMyChart"
          element-loading-text="加载中"
          element-loading-spinner="el-icon-loading">
          <el-col :span="23" :offset="1">
            <div>
              <div
                id="echartReportNoData"
                style="color: #909399; font-size: 12px"
              >
                暂无数据
              </div>
              <div id="echartReport" style="width: 100%; height: 600px"></div>
            </div>
          </el-col>
        </el-row>      
      </div>
    </template>
    <!-- <template #footer> </template> -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="55%"> 
        <el-form
        class="ad-form-query"
        :inline="true"
        :model="dialogFilter"
        @submit.native.prevent
        v-show="dialogFilterShow">
        <!-- <el-form-item label="发货时长:" >
          <el-select v-model="dialogFilter.timeRangeCondition" placeholder="请选择" class="el-select-content" style="width:100px;"> 
            <el-option v-for="item in timeRangeConditionList" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
          <el-input-number v-model="dialogFilter.timeRange" :step="10" class="el-select-content" style="width:100px;"> 
          </el-input-number>
        </el-form-item>        -->
        <el-form-item>
          <el-button type="primary" @click="searchOrderNotSend">刷新</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onExportNotSend">导出</el-button>
        </el-form-item>
      </el-form>   
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :tableData='dialogTableData' 
         :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" :isSelectColumn="false"  style="height:520px;">
        </ces-table>    
        <!--分页-->
        <my-pagination
            ref="pager"
            :total="total"
            :checked-count="sels.length"
            @get-page="getOrderNotSend"
          />              
    </el-dialog>

    <el-dialog :title="orderNotSendDetail.title" :visible.sync="orderNotSendDetail.visible" width="44%" append-to-body> 
        <el-form
        class="ad-form-query3"
        :inline="true"
        @submit.native.prevent>
        <el-form-item>
          <el-button type="primary" @click="searchOrderNotSendDetail">刷新</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onExportNotSendDetail">导出</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" size="mini" @click="copyOrderNotSendDetailOrderNos">一键复制所有订单号</el-button>  
        </el-form-item>
      </el-form>   
      <ces-table ref="tableDetail" :that='orderNotSendDetail.that' :isIndex='true' :hasexpand='false' @sortchange='sortchangeOrderNotSendDetail' :tableData='orderNotSendDetail.tableData'
         :tableCols='orderNotSendDetail.tableCols' :loading="orderNotSendDetail.listLoading" :isSelectColumn="false"  style="height:510px;">
        </ces-table>    
        <!--分页-->
        <my-pagination
            ref="pagerDetail"
            :total="orderNotSendDetail.total"
            @get-page="getOrderNotSendDetail"
          />              
    </el-dialog>

    <!--延迟发货扣款弹窗 Start-->
    <el-dialog :title="deductMoney.title" :visible.sync="deductMoney.visible" width="44%"> 
      <el-form
        class="ad-form-query"
        :inline="true"
        @submit.native.prevent>
        <el-form-item>
          <el-button type="primary" @click="searchDeductMoney">刷新</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onExportDeductMoney">导出</el-button>
        </el-form-item>        
      </el-form>   
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchangeDeductMoney' :tableData='deductMoney.tableData' 
         :tableCols='deductMoney.tableCols' :loading="deductMoney.listLoading" :isSelectColumn="false"  style="height:520px;">
        </ces-table>    
        <!--分页-->
        <my-pagination
            ref="deductMoneyPager"
            :total="deductMoney.total"
            :checked-count="deductMoney.sels.length"
            @get-page="getDeductMoney"
          />              
    </el-dialog>

    <el-dialog :title="deductMoneyDetail.title" :visible.sync="deductMoneyDetail.visible" width="44%" append-to-body> 
        <el-form
        class="ad-form-query3"
        :inline="true"
        @submit.native.prevent>
        <el-form-item>
          <el-button type="primary" @click="searchDeductMoneyDetail">刷新</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onExportDeductMoneyDetail">导出</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" size="mini" @click="copyDeductMoneyDetailOrderNos">一键复制所有订单号</el-button>  
        </el-form-item>        
      </el-form>   
      <ces-table ref="tableDetail" :that='deductMoneyDetail.that' :isIndex='true' :hasexpand='false' @sortchange='sortchangeDeductMoneyDetail' :tableData='deductMoneyDetail.tableData'
         :tableCols='deductMoneyDetail.tableCols' :loading="deductMoneyDetail.listLoading" :isSelectColumn="false"  style="height:510px;">
        </ces-table>    
        <!--分页-->
        <my-pagination
            ref="deductMoneyDetailPager"
            :total="deductMoneyDetail.total"
            @get-page="getDeductMoneyDetail"
          />              
    </el-dialog>
    <!--延迟发货扣款弹窗 End-->

    <!--快递违禁品扣款弹窗 Start-->
    <el-dialog :title="deductMoneyTaboo.title" :visible.sync="deductMoneyTaboo.visible" width="44%"> 
      <el-form
        class="ad-form-query"
        :inline="true"
        @submit.native.prevent>
        <el-form-item>
          <el-button type="primary" @click="searchDeductMoneyTaboo">刷新</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onExportDeductMoneyTaboo">导出</el-button>
        </el-form-item>
      </el-form>   
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchangeDeductMoneyTaboo' :tableData='deductMoneyTaboo.tableData'
         :tableCols='deductMoneyTaboo.tableCols' :loading="deductMoneyTaboo.listLoading" :isSelectColumn="false"  style="height:520px;">
        </ces-table>    
        <!--分页-->
        <my-pagination
            ref="deductMoneyTabooPager"
            :total="deductMoneyTaboo.total"
            :checked-count="deductMoneyTaboo.sels.length"
            @get-page="getDeductMoneyTaboo"
          />              
    </el-dialog>

    <el-dialog :title="deductMoneyTabooDetail.title" :visible.sync="deductMoneyTabooDetail.visible" width="64%" append-to-body> 
        <el-form
        class="ad-form-query3"
        :inline="true"
        @submit.native.prevent>
        <el-form-item>
          <el-button type="primary" @click="searchDeductMoneyTabooDetail">刷新</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onExportDeductMoneyTabooDetail">导出</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" size="mini" @click="copyDeductMoneyTabooDetailOrderNos">一键复制所有订单号</el-button>  
        </el-form-item>
      </el-form>     
      <ces-table ref="tableDetail" :that='deductMoneyTabooDetail.that' :isIndex='true' :hasexpand='false' @sortchange='sortchangeDeductMoneyTabooDetail' :tableData='deductMoneyTabooDetail.tableData'
         :tableCols='deductMoneyTabooDetail.tableCols' :loading="deductMoneyTabooDetail.listLoading" :isSelectColumn="false"  style="height:510px;">
        </ces-table>    
        <!--分页-->
        <my-pagination
            ref="deductMoneyTabooDetailPager"
            :total="deductMoneyTabooDetail.total"
            @get-page="getDeductMoneyTabooDetail"
          />              
    </el-dialog>
    <!--快递违禁品扣款弹窗 End-->

    <!--代拍订单弹窗 Start-->
    <el-dialog :title="agentAmount.title" :visible.sync="agentAmount.visible" width="44%"> 
      <el-form
        class="ad-form-query"
        :inline="true"
        @submit.native.prevent>
        <el-form-item>
          <el-button type="primary" @click="searchAgentAmount">刷新</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onExportAgentAmount">导出</el-button>
        </el-form-item>
      </el-form>   
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchangeAgentAmount' :tableData='agentAmount.tableData' 
         :tableCols='agentAmount.tableCols' :loading="agentAmount.listLoading" :isSelectColumn="false"  style="height:520px;">
        </ces-table>    
        <!--分页-->
        <my-pagination
            ref="agentAmountPager"
            :total="agentAmount.total"
            :checked-count="agentAmount.sels.length"
            @get-page="getAgentAmount"
          />              
    </el-dialog>

    <el-dialog :title="agentAmountDetail.title" :visible.sync="agentAmountDetail.visible" width="64%" append-to-body> 
        <el-form
        class="ad-form-query3"
        :inline="true"
        @submit.native.prevent>
        <el-form-item>
          <el-button type="primary" @click="searchAgentAmountDetail">刷新</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onExportAgentAmountDetail">导出</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" size="mini" @click="copyAgentAmountDetailOrderNos">一键复制所有订单号</el-button>  
        </el-form-item>
      </el-form>     
      <ces-table ref="tableDetail" :that='agentAmountDetail.that' :isIndex='true' :hasexpand='false' 
         @sortchange='sortchangeAgentAmountDetail'
         :tableData='agentAmountDetail.tableData'
         :tableCols='agentAmountDetail.tableCols' :loading="agentAmountDetail.listLoading" :isSelectColumn="false"  style="height:510px;">
        </ces-table>    
        <!--分页-->
        <my-pagination
            ref="agentAmountDetailPager"
            :total="agentAmountDetail.total"
            @get-page="getAgentAmountDetail"
          />              
    </el-dialog>
    <!--代拍订单弹窗 End-->

    <!--平均快递费弹窗 Start-->
    <el-dialog :visible.sync="expressCharts.visible" width="54%" append-to-body title="快递分析">
          <div style="margin-top:20px;"
               v-loading="expressCharts.loading"
               element-loading-text="加载中"
               element-loading-spinner="el-icon-loading">
                <div id="expressCharts"></div>
          </div>         
    </el-dialog> 
    <!--平均快递费弹窗 End-->

  </my-container>
</template>

<script>
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import * as echarts from "echarts";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getList as getshopList,getDirectorList,getDirectorGroupList } from "@/api/operatemanage/base/shop";
import {
  getOrderSum,
  getOrderCharts,
  getOrderChartsList,
  exportOrderChartsList,
  getOrderNotSend,
  exportOrderNotSend,
  getOrderCountNotSendDetail,
  getOrderCountNotSendDetailOrderNos,
  exportOrderCountNotSendDetail,
  getOrderNoCount,
  getOrderDeductMoneySum,
  getOrderExpressSum,
  getOrderAvgFreightMoney,
  getOrderReturnRate,
  getOrderProCodeReturnRate,
  getOrderTimeRange,
  getOrderDeductMoneyTaboo,
  //代拍订单
  getOrderAgentAmountSum,
  exportOrderReportAgentAmount,
  getOrderReportAgentAmount,
  exportOrderReportAgentAmountDetail,
  getOrderReportAgentAmountDetail,
  getOrderReportAgentAmountDetailOrderNos,
  //快递分析
  getOrderExpressAnalysis,
} from "@/api/order/ordergoods";
import {
  exportDeductMoneySum,
  getDeductMoneySum,
  getDeductMoneyDetail,
  exportDeductMoneyDetail,
  getDeductMoneyDetailOrderNos
} from "@/api/order/orderdeductmoney";
import {
  exportDeductMoneyTabooSum,
  getDeductMoneyTabooSum,
  getDeductMoneyTabooDetail,
  exportDeductMoneyTabooDetail,
  getDeductMoneyTabooDetailOrderNos
} from "@/api/order/orderdeductmoneytaboo";
import { ruleSendWarehouse, rulePlatform } from "@/utils/formruletools";
import { formatTime1 } from "@/utils";
import { formatLink,formatSendWarehouse,formatPlatform} from "@/utils/tools";
import dayjs from "dayjs";

var weekday = require("dayjs/plugin/weekday");
dayjs.extend(weekday);

const tableCols =[
      //{display:false,istrue:true,prop:'timeRang',label:'发货时长(小时)', width:'150',sortable:'custom'}, 
      //{display:false,istrue:true,prop:'sendWarehouse',label:'仓库', width:'180',sortable:'custom',formatter:(row)=>row.sendWarehouseName}, 
      {istrue:true,prop:'platformName',label:'平台', width:'110',sortable:'custom'},     
      {istrue:true,prop:'shopName',label:'店铺', width:'180',sortable:'custom'},  
      {istrue:true,display:true,prop:'proCode',label:'宝贝ID', width:'150',sortable:'custom',type:'html',formatter:(row)=>{
            var proBaseUrl="";
            switch (row.platformDesc)
            {
              case "淘系":
                proBaseUrl="https://detail.tmall.com/item.htm?id="+row.proCode;
                break;
              case "拼多多":
                proBaseUrl="https://mobile.yangkeduo.com/goods2.html?goods_id="+row.proCode;
                break;
            }
            if(row.proCode && proBaseUrl)
              return formatLink(row.proCode,proBaseUrl);
            return row.proCode;
         }
      },         
      {istrue:true,prop:'goodsName',label:'商品名称', width:'auto',sortable:'custom'},
      {istrue:true,prop:'orderCountNotSend',label:'未发货订单数', width:'140',sortable:'custom',type:'click',handle:(that,row,column,cell)=>that.cellclick(row,column,cell)}, 
      //{display:false,istrue:true,prop:'goodsCode',label:'商品编码', width:'140',sortable:'custom'}, 
      //{display:false,istrue:true,prop:'classify',label:'运营组', width:'110',sortable:'custom'},  
      //{display:false,istrue:true,prop:'brandName',label:'采购组', width:'110',sortable:'custom'},      
  ];

const tableColsOrderNotSendDetail=[
      {istrue:true,prop:'orderNoInner',label:'订单号', width:'120',sortable:'custom'}, 
      {istrue:true,prop:'goodsCode',label:'商品编码', width:'120',sortable:'custom'}, 
      {istrue:true,prop:'goodsName',label:'商品名称', width:'auto',sortable:'custom'},      
      {istrue:true,prop:'brandName',label:'采购员', width:'100',sortable:'custom'},  
];

export default {
  name: "Users",
  components: { MyContainer, MySearch, MySearchWindow,cesTable },
  data() {
    return {
      pageLoading: false,
      filter: {
        timeRange: [],
        sendWarehouse: [],
        platform: [],
        shopCode: [],
        groupId:[],
        directorId:[],
        proCode:"",
        goodsCode:"",
        dateModel:{
          month:null,
          week:null,
          day:null
        }
      },
      filterCharts: {
        timeRange: [],
        sendWarehouse: [],
        platform: [],
        shopCode: [],
        keyWord: [],
        groupId:[],
        dateModel:{
          month:null,
          week:null,
          day:null
        },
        groupByDay:true,
        groupByWeek:false,
        groupByMonth:false,
      },
      dataSum: {
        orderCount: 0, //总订单
        orderCountSended: 0, //已发货
        orderCountNotSend: 0, //未发货
        timeRange: 0, //发货时长
        deductMoneyDelay: 0, //延迟发货扣款
        avgFreightMoney: 0, //总平均快递费
        sumFreightMoney: 0, //总快递费
        returnRate: 0, //编码退货率
        profitRate: 0, //总毛利率
        continuedHeavyFee: 0, //总续重费
        faceFee: 0, //面单费
        deductMoneyTaboo: 0, //快递违禁品罚款
        returnRateProcode: 0, //ID退货率
        agentAmount:0,//代拍金额
        avgFreightMoneyShare:0,//分摊平均快递费
      },
      loadingDataSum:{
        orderCount: false, //总订单
        orderCountSended: false, //已发货
        orderCountNotSend: false, //未发货
        timeRange: false, //发货时长
        deductMoneyDelay: false, //延迟发货扣款
        avgFreightMoney: false, //总平均快递费
        sumFreightMoney: false, //总快递费
        returnRate: false, //编码退货率
        profitRate: false, //总毛利率
        continuedHeavyFee: false, //总续重费
        faceFee: false, //面单费
        deductMoneyTaboo: false, //快递违禁品罚款
        returnRateProcode: false, //ID退货率
        agentAmount:false,//代拍金额
        avgFreightMoneyShare: false, //分摊平均快递费
      },
      sendWarehouseList: [],
      platformList: [],
      shopList: [],
      directorList:[],
      directorGroupList:[],
      myChart: null,
      myChartOptions: null,
      loadingMyChart:false,
      loadingMyTable:false,
      Ylist: [
        { value: 0, unit: "", label: "订单数" },
        { value: 1, unit: "小时", label: "发货时间" },
        { value: 2, unit: "元", label: "平均快递费" },
        { value: 3, unit: "%", label: "毛利率" },
        { value: 4, unit: "%", label: "退货率" },
        { value: 5, unit: "kg", label: "均重" },
        { value: 6, unit: "元", label: "分摊后平均快递费" },
      ],
      noClickChartsClass: "",
      noClickTableClass: "my-btn-noclick",
      showCharts: true,
      showTable: false,
      tableData: [],
      timeRangeConditionList:[
        {label:"大于",value:1},
        {label:"大于等于",value:2},
        {label:"等于",value:3},
        {label:"小于",value:4},
        {label:"小于等于",value:5}     
      ],
      dialogVisible:false,//弹窗是否弹出
      dialogTitle:"详情",//弹窗标题
      dialogFilterShow:false,//条件是否显示
      dialogFilter:{
          timeRangeCondition:null,//发货时长，大于，大于等于，小于，小于等于
          timeRange:0,//发货时长
          IsSend:null,//是否发货
          startDate:null,
          endDate:null,
      },//弹窗查询条件
      dialogTableData:[],//弹窗表格数据
      tableCols:tableCols,
      total: 0,
      summaryarry:{},
      pager:{OrderBy:"platform",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      that:this,
      tableHandles:[],     
      orderNotSendDetail:{ //未发货订单明细弹窗
          visible:false,//弹窗是否弹出
          title:"未发货订单明细",//弹窗标题
          tableData:[],//弹窗表格数据
          total: 0,
          summaryarry:{},
          pager:{OrderBy:"orderNoInner",IsAsc:false},
          tableCols:tableColsOrderNotSendDetail,
          sels: [], // 列表选中列
          listLoading: false,
          pageLoading: false,
          that:this,
          filter:{
            proCode:null
          },          
      },
      pickOptions:{
        disabledDate(time){
          return time.getTime() > Date.now()
        }
      },
      pickOptionsWeek:{
        disabledDate(time){
          return time.getTime() > Date.now()
        },
        firstDayOfWeek:1
      },
      deductMoney:{
        title:"",
        visible:false,
        tableCols:[
          {istrue:true,prop:'platformName',label:'平台', width:'180',sortable:'custom'}, 
          {istrue:true,prop:'shopName',label:'店铺', width:'auto',sortable:'custom'}, 
          {istrue:true,prop:'amount',label:'扣款金额（元）', width:'180',sortable:'custom',type:'click',handle:(that,row,column,cell)=>that.cellclickDeductMoney(row,column,cell)},
        ],
        tableData:[],
        total:0,
        sels:[],
        listLoading:false,
        pager:{OrderBy:"platform",IsAsc:false},
        pageLoading: false,
        that:this,
        filter:{
           //platform:null,
        },        
      },
      deductMoneyDetail:{
        title:"",
        visible:false,
        tableCols:[
          {istrue:true,prop:'occurrenceTime',label:'发生日期', width:'100',sortable:'custom'},
          { istrue: true, prop: 'platform', label: '平台', width: '100', sortable: 'custom', formatter: (row) => row.platformName || ' ' },
          {istrue:true,prop:'shopCode',label:'店铺', sortable:'custom', width:'220',formatter:(row)=>row.shopName}, 
          {istrue:true,prop:'orderNo',label:'订单号', width:'200',sortable:'custom'},
          {istrue:true,prop:'amount',label:'扣款金额（元）', width:'150',sortable:'custom'},
        ],
        tableData:[],
        total:0,
        sels:[],
        listLoading:false,
        pager:{OrderBy:"platform",IsAsc:false},
        pageLoading: false,
        that:this,
        filter:{
           //platform:null,
           shopCode:null
        },
      },
      deductMoneyTaboo:{
        title:"",
        visible:false,
        tableCols:[
          {istrue:true,prop:'sendWarehouse',label:'仓库', width:'100',sortable:'custom',formatter:(row)=>formatSendWarehouse(row.sendWarehouse)}, 
          {istrue:true,prop:'platform',label:'平台', width:'100',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)}, 
          {istrue:true,prop:'shopCode',label:'店铺', width:'auto',sortable:'custom',formatter:(row)=>row.shopName}, 
          //{istrue:true,prop:'groupId',label:'运营组', width:'110',sortable:'custom',formatter:(row)=>row.groupName}, 
          {istrue:true,prop:'deductMoneyTaboo',label:'罚款金额（元）', width:'180',sortable:'custom',type:'click',handle:(that,row,column,cell)=>that.cellclickDeductMoneyTaboo(row,column,cell)},
        ],
        tableData:[],
        total:0,
        sels:[],
        listLoading:false,
        pager:{OrderBy:"platform",IsAsc:false},
        pageLoading: false,
        that:this,
        filter:{
           //platform:null,
        },        
      },
      deductMoneyTabooDetail:{
        title:"",
        visible:false,
        tableCols:[
          {istrue:true,prop:'occurrenceTime',label:'日期', width:'100',sortable:'custom',},
          {istrue:true,prop:'sendWarehouse',label:'仓库', width:'90',sortable:'custom',formatter:(row)=>formatSendWarehouse(row.sendWarehouse)},
          {istrue:true,prop:'platform',label:'平台', width:'80',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
          {istrue:true,prop:'shopCode',label:'店铺', width:'180',sortable:'custom',formatter:(row)=>row.shopName}, 
          //{istrue:true,prop:'groupId',label:'运营组', width:'100',sortable:'custom',formatter:(row)=>row.groupName}, 
          {istrue:true,prop:'orderNo',label:'订单号', width:'160',sortable:'custom',},
          {istrue:true,prop:'expressNo',label:'快递单号', width:'150',sortable:'custom',},
          //{istrue:true,prop:'proCode',label:'宝贝ID', width:'150',sortable:'custom',},
          {istrue:true,prop:'deductMoneyTaboo',label:'罚款金额（元）', width:'150',sortable:'custom',}, 
          {istrue:true,prop:'remark',label:'备注', width:'auto',sortable:'custom',},
        ],
        tableData:[],
        orderNoInners:"",
        total:0,
        sels:[],
        listLoading:false,
        pager:{OrderBy:"platform",IsAsc:false},
        pageLoading: false,
        that:this,
        filter:{
           platform:null,
           shopCode:null,
           sendWarehouse:null,
           groupId:null,
        },
      },
      //代拍金额店铺统计
      agentAmount:{
        title:"",
        visible:false,
        tableCols:[
          {istrue:true,prop:'platform',label:'平台', width:'120',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)}, 
          {istrue:true,prop:'shopCode',label:'店铺', width:'auto',sortable:'custom',formatter:(row)=>row.shopName}, 
          {istrue:true,prop:'orderCount',label:'订单数', width:'100',sortable:'custom'}, 
          {istrue:true,prop:'agentAmount',label:'代拍金额（元）', width:'180',sortable:'custom',type:'click',handle:(that,row,column,cell)=>that.cellclickAgentAmount(row,column,cell)},
        ],
        tableData:[],
        total:0,
        sels:[],
        listLoading:false,
        pager:{OrderBy:"agentAmount",IsAsc:false},
        pageLoading: false,
        that:this,
        filter:{
           //platform:null,
        },        
      },
      //代拍金额店铺明细
      agentAmountDetail:{
        title:"",
        visible:false,
        tableCols:[
          {istrue:true,prop:'agentOrderTime',label:'代拍时间', width:'90',sortable:'custom'},
          {istrue:true,prop:'purchaser',label:'采购员', width:'80',sortable:'custom',}, 
           {istrue:true,prop:'platform',label:'平台', width:'80',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
          {istrue:true,prop:'shopName',label:'店铺名', width:'150',sortable:'custom',},
          {istrue:true,prop:'goodsCode',label:'商品编码', width:'130',sortable:'custom',},
          {istrue:true,prop:'goodsName',label:'商品名称', width:'auto',sortable:'custom',},
          {istrue:true,prop:'qty',label:'数量', width:'70',sortable:'custom',},
          //{istrue:true,prop:'buyerAmount',label:'买家支付金额', width:'120',sortable:'custom',},
          {istrue:true,prop:'agentAmount',label:'代拍金额', width:'100',sortable:'custom',},
          {istrue:true,prop:'orderNo',label:'线上订单号', width:'180',sortable:'custom',},
          //{istrue:true,prop:'payOrderNo',label:'支付订单编号', width:'180',sortable:'custom',},
          //{istrue:true,prop:'expressCompany',label:'快递公司', width:'110',sortable:'custom',},
          //{istrue:true,prop:'expressNo',label:'快递单号', width:'130',sortable:'custom',},       
          //{istrue:true,prop:'purchasingChannel',label:'采购渠道', width:'110',sortable:'custom',},
          //{istrue:true,prop:'buyerInfo',label:'客户信息', width:'110',sortable:'custom',}
        ],
        tableData:[],
        orderNoInners:"",
        total:0,
        sels:[],
        listLoading:false,
        pager:{OrderBy:"platform",IsAsc:false},
        pageLoading: false,
        that:this,
        filter:{
           platform:null,
           shopCode:null,
           sendWarehouse:null,
           groupId:null,
        },
      },
      expressCharts:{
        visible:false,
        loading:false,
        myChart: null,
        Ylist: [
          { value: 0, unit: "元", label: "平均快递费" },
          { value: 1, unit: "元", label: "总快递费" },
          { value: 2, unit: "", label: "总快递单量" },
        ],
      }
    };
  },
  async mounted() {
    await this.setPlatform();
    await this.setWarehouse();
    await this.setDirectorList();
    await this.setDirectorGroupList();
    await this.clickYesterdaySum(true);
    this.filterCharts.keyWord = [1];
    await this.clickYesterdayCharts(true);
    
  },
  computed: {
        formattedTotalWaitOrderNum1() {
            return this.dataSum.orderCount !== undefined && this.dataSum.orderCount !== null ? this.formatNumber(this.dataSum.orderCount) : '';
        },
        formattedTotalWaitOrderNum2() {
            return this.dataSum.orderCountSended !== undefined && this.dataSum.orderCountSended !== null ? this.formatNumber(this.dataSum.orderCountSended) : '';
        },
        formattedTotalWaitOrderNum3() {
            return this.dataSum.orderCountNotSend !== undefined && this.dataSum.orderCountNotSend !== null ? this.formatNumber(this.dataSum.orderCountNotSend) : '';
        },
        formattedTotalWaitOrderNum4() {
            return this.dataSum.timeRange !== undefined && this.dataSum.timeRange !== null ? this.formatNumber(this.dataSum.timeRange) : '';
        },
        formattedTotalWaitOrderNum5() {
            return this.dataSum.deductMoneyDelay !== undefined && this.dataSum.deductMoneyDelay !== null ? this.formatNumber(this.dataSum.deductMoneyDelay) : '';
        },
        formattedTotalWaitOrderNum6() {
            return this.dataSum.avgFreightMoney !== undefined && this.dataSum.avgFreightMoney !== null ? this.formatNumber(this.dataSum.avgFreightMoney) : '';
        },
        formattedTotalWaitOrderNum7() {
            return this.dataSum.sumFreightMoney !== undefined && this.dataSum.sumFreightMoney !== null ? this.formatNumber(this.dataSum.sumFreightMoney) : '';
        },
        formattedTotalWaitOrderNum8() {
            return this.dataSum.faceFee !== undefined && this.dataSum.faceFee !== null ? this.formatNumber(this.dataSum.faceFee) : '';
        },
        formattedTotalWaitOrderNum9() {
            return this.dataSum.continuedHeavyFee !== undefined && this.dataSum.continuedHeavyFee !== null ? this.formatNumber(this.dataSum.continuedHeavyFee) : '';
        },
        formattedTotalWaitOrderNum10() {
            return this.dataSum.deductMoneyTaboo !== undefined && this.dataSum.deductMoneyTaboo !== null ? this.formatNumber(this.dataSum.deductMoneyTaboo) : '';
        },
        formattedTotalWaitOrderNum11() {
            return this.dataSum.avgFreightMoneyShare !== undefined && this.dataSum.avgFreightMoneyShare !== null ? this.formatNumber(this.dataSum.avgFreightMoneyShare) : '';
        },
        formattedTotalWaitOrderNum12() {
            return this.dataSum.returnRate !== undefined && this.dataSum.returnRate !== null ? this.formatNumber(this.dataSum.returnRate) : '';
        },
        formattedTotalWaitOrderNum13() {
            return this.dataSum.returnRateProcode !== undefined && this.dataSum.returnRateProcode !== null ? this.formatNumber(this.dataSum.returnRateProcode) : '';
        },
        formattedTotalWaitOrderNum14() {
            return this.dataSum.profitRate !== undefined && this.dataSum.profitRate !== null ? this.formatNumber(this.dataSum.profitRate) : '';
        },
        formattedTotalWaitOrderNum15() {
            return this.dataSum.agentAmount !== undefined && this.dataSum.agentAmount !== null ? this.formatNumber(this.dataSum.agentAmount) : '';
        },
      },
  methods: {
    formatNumber(num) {
            // 去掉小数位并添加千位分隔符
            return num > 100 ? Math.floor(num).toLocaleString() : num.toLocaleString();
        },
    //设置平台下拉
    async setPlatform() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;
    },
    //设置仓库下拉
    async setWarehouse() {
      var whrule = await ruleSendWarehouse();
      this.sendWarehouseList = whrule.options;
      var selWrh = [];
      if (this.sendWarehouseList && this.sendWarehouseList.length > 0) {
        this.sendWarehouseList.forEach((obj) => {
          selWrh.push(obj.value);
        });
      }
      this.filterCharts.sendWarehouse = selWrh;
    },
    //设置运营下拉
    async setDirectorList(){
        var res=await getDirectorList();
        this.directorList=res.data;
    },
    //设置运营组下拉
    async setDirectorGroupList(){
        var res=await getDirectorGroupList();
        this.directorGroupList=res.data;
    },
    //获取上周一
    startOfLastWeek(d) {
      d = d || new Date();
      if (dayjs(d).day() === 0) {
        return dayjs(d).weekday(-7 + 1 - 7);
      }
      return dayjs(d).weekday(-7 + 1);
    },
    //获取上周日
    endOfLastWeek(d) {
      d = d || new Date();
      if (dayjs(d).day() === 0) {
        return dayjs(d).weekday(-7);
      }
      return dayjs(d).weekday(0);
    },
    //上月1号
    startOfLastMonth(d) {
      d = d || new Date();
      return dayjs(d).startOf("month").subtract(1, "month");
    },
    //上月最后一天
    endOfLastMonth(d) {
      d = d || new Date();
      return dayjs(d).endOf("month").subtract(1, "month");
    },
    //设置店铺下拉
    async onchangeplatform(val) {
      const res1 = await getshopList({
        platform: val,
        CurrentPage: 1,
        PageSize: 10000,
      });
      this.shopList = [];
      if (
        val &&
        val.length > 0 &&
        res1.data.list &&
        res1.data.list.length > 0
      ) {
        val.forEach((platform) => {
          res1.data.list.forEach((obj) => {
            if (platform == obj.platform) {
              this.shopList.push(obj);
            }
          });
        });
      }
    },
    //单击昨天
    async clickYesterdaySum(isYesterday) {
      var startDate;
      if(isYesterday===true){
        startDate = dayjs(new Date()).subtract(1, "day");       
      }
      else{
        startDate=this.filter.dateModel.day;
      }
      startDate = formatTime1(startDate, "yyyy-MM-dd");
      this.filter.timeRange = [startDate, startDate];
      await this.onSearch();
    },
    //单击周
    async clickWeekSum() {
      //var start = this.startOfLastWeek();
      //var end = this.endOfLastWeek();
      var start=dayjs(this.filter.dateModel.week).subtract(1,'day');
      var end=dayjs(start).add(6,"day");
      this.filter.timeRange = [start, end];
      await this.onSearch();
    },
    //单击月
    async clickMonthSum() {
      //var start = this.startOfLastMonth();
      //var end = this.endOfLastMonth();
      var start=this.filter.dateModel.month;
      var end=dayjs(start).endOf("month");
      this.filter.timeRange = [start, end];
      await this.onSearch();
    },
    getOrderSumCondition(){
        if (!this.filter.timeRange || this.filter.timeRange.length != 2) return false;
        var startDate = dayjs(this.filter.timeRange[0]).format("YYYY-MM-DD");
        var endDate = dayjs(this.filter.timeRange[1]).format("YYYY-MM-DD");
        var para = { startDate, endDate,
          sendWarehouses: this.filter.sendWarehouse.join(","),
          platforms: this.filter.platform.join(","),
          shopCodes: this.filter.shopCode.join(","),
          directorGroupIds: this.filter.groupId.join(","),
          directorIds: this.filter.directorId.join(","),
          proCode:this.filter.proCode,
          goodsCode:this.filter.goodsCode
        };
        return para;
    },
    //清空统计
    clearDataSum(){
      this.dataSum={
        orderCount: 0, //总订单
        orderCountSended: 0, //已发货
        orderCountNotSend: 0, //未发货
        timeRange: 0, //发货时长
        deductMoneyDelay: 0, //延迟发货扣款
        avgFreightMoney: 0, //总平均快递费
        sumFreightMoney: 0, //总快递费
        returnRate: 0, //编码退货率
        profitRate: 0, //总毛利率
        continuedHeavyFee: 0, //总续重费
        faceFee: 0, //面单费
        deductMoneyTaboo: 0, //快递违禁品罚款
        returnRateProcode: 0, //ID退货率
        agentAmount:0,//代拍金额
        avgFreightMoneyShare:0,//分摊平均快递费
      };
    },
    //统计搜索
    async onSearch() {
      var para=this.getOrderSumCondition();
      if(!para) return false;
      this.clearDataSum();
      setTimeout(async() => {      
        await this.getOrderNoCount(para);
      }, 5);
      setTimeout(async() => {
        await this.getOrderDeductMoneySum(para);
      }, 10);
      setTimeout(async() => {
        await this.getOrderExpressSum(para);
      }, 15);
      setTimeout(async() => {
        await this.getOrderReturnRate(para);
      }, 20);
      setTimeout(async() => {
        await this.getOrderProCodeReturnRate(para);
      }, 25);
      setTimeout(async() => {
        await this.getOrderTimeRange(para);
      }, 30);
      setTimeout(async() => {
        await this.getOrderDeductMoneyTaboo(para);
      }, 35);
      setTimeout(async() => {
        await this.getOrderAgentAmountSum(para);
      }, 40);
      setTimeout(async() => {
        await this.getOrderAvgFreightMoney(para);
      }, 45);

      this.dialogFilter.startDate=para.startDate;
      this.dialogFilter.endDate=para.endDate;
    },
    //订单数，已发货，未发货，毛利率
    async getOrderNoCount(para){
      this.loadingDataSum.orderCount=true;
      this.loadingDataSum.orderCountSended=true;
      this.loadingDataSum.orderCountNotSend=true;
      this.loadingDataSum.profitRate=true;

      var res = await getOrderNoCount(para);
      if (!res?.code) {
        return false;
      }
      this.dataSum.orderCount=res.data.orderCount;
      this.dataSum.orderCountSended=res.data.orderCountSended;
      this.dataSum.orderCountNotSend=res.data.orderCountNotSend;
      this.dataSum.profitRate=res.data.profitRate;

      this.loadingDataSum.orderCount=false;
      this.loadingDataSum.orderCountSended=false;
      this.loadingDataSum.orderCountNotSend=false;
      this.loadingDataSum.profitRate=false;
    },
    //延迟发货扣款
    async getOrderDeductMoneySum(para){
      this.loadingDataSum.deductMoneyDelay=true;

      var res = await getOrderDeductMoneySum(para);
      if (!res?.code) {
        return false;
      }
      this.dataSum.deductMoneyDelay=res.data.deductMoneyDelay;

      this.loadingDataSum.deductMoneyDelay=false;
    },
    //总快递费，分摊平均快递费，总续重费，面单费
    async getOrderExpressSum(para){
      this.loadingDataSum.avgFreightMoneyShare=true;
      this.loadingDataSum.sumFreightMoney=true;
      this.loadingDataSum.continuedHeavyFee=true;
      this.loadingDataSum.faceFee=true;

      var res = await getOrderExpressSum(para);
      if (!res?.code) {
        return false;
      }
      this.dataSum.avgFreightMoneyShare=res.data.avgFreightMoneyShare;
      this.dataSum.sumFreightMoney=res.data.sumFreightMoney;
      this.dataSum.continuedHeavyFee=res.data.continuedHeavyFee;
      this.dataSum.faceFee=res.data.faceFee;

      this.loadingDataSum.avgFreightMoneyShare=false;
      this.loadingDataSum.sumFreightMoney=false;
      this.loadingDataSum.continuedHeavyFee=false;
      this.loadingDataSum.faceFee=false;
    },
    //总平均快递费
    async getOrderAvgFreightMoney(para){
      this.loadingDataSum.avgFreightMoney=true;

      var res = await getOrderAvgFreightMoney(para);
      if (!res?.code) {
        return false;
      }
      this.dataSum.avgFreightMoney=res.data.avgFreightMoney;

      this.loadingDataSum.avgFreightMoney=false;
    },
    //编码退货率
    async getOrderReturnRate(para){
      this.loadingDataSum.returnRate=true;

      var res = await getOrderReturnRate(para);
      if (!res?.code) {
        return false;
      }
      this.dataSum.returnRate=res.data.returnRate;

      this.loadingDataSum.returnRate=false;
    },
    //ID退货率
    async getOrderProCodeReturnRate(para){
      this.loadingDataSum.returnRateProcode=true;

      var res = await getOrderProCodeReturnRate(para);
      if (!res?.code) {
        return false;
      }
      this.dataSum.returnRateProcode=res.data.returnRateProcode;

      this.loadingDataSum.returnRateProcode=false;
    },
    //发货时长
    async getOrderTimeRange(para){
      this.loadingDataSum.timeRange=true;

      var res = await getOrderTimeRange(para);
      if (!res?.code) {
        return false;
      }
      this.dataSum.timeRange=res.data.timeRange;

      this.loadingDataSum.timeRange=false;
    },
    //快递违禁品扣款
    async getOrderDeductMoneyTaboo(para){
      this.loadingDataSum.deductMoneyTaboo=true;

      var res = await getOrderDeductMoneyTaboo(para);
      if (!res?.code) {
        return false;
      }
      this.dataSum.deductMoneyTaboo=res.data.deductMoneyTaboo;

      this.loadingDataSum.deductMoneyTaboo=false;
    },
    //代拍订单
    async getOrderAgentAmountSum(para){
      this.loadingDataSum.agentAmount=true;

      var res = await getOrderAgentAmountSum(para);
      if (!res?.code) {
        return false;
      }
      this.dataSum.agentAmount=res.data.agentAmount;

      this.loadingDataSum.agentAmount=false;
    },
    //单击日
    async clickYesterdayCharts(isYesterday) {
      var startDate;
      if(isYesterday===true){
        startDate = dayjs(new Date()).subtract(1, "day");       
      }
      else{
        startDate=this.filterCharts.dateModel.day;
      }
      //日期范围时30天
      var firstDate=dayjs(startDate).subtract(29,"day");
      startDate=formatTime1(startDate, "yyyy-MM-dd");
      firstDate = formatTime1(firstDate, "yyyy-MM-dd");
      this.filterCharts.timeRange = [firstDate, startDate];
      this.filterCharts.groupByDay=true;
      this.filterCharts.groupByWeek=false;
      this.filterCharts.groupByMonth=false;
      await this.onSearchCharts();
    },
    //单击周
    async clickWeekCharts() {
      //var start = this.startOfLastWeek();
      //var end = this.endOfLastWeek();
      var start=dayjs(this.filterCharts.dateModel.week).subtract(1,'day');
      var end=dayjs(start).add(6,"day");
      //日期范围是12个周
      var firstStart=dayjs(start).subtract(11*7,"day");
      this.filterCharts.timeRange = [firstStart, end];
      this.filterCharts.groupByDay=false;
      this.filterCharts.groupByWeek=true;
      this.filterCharts.groupByMonth=false;
      await this.onSearchCharts();
    },
    //单击月
    async clickMonthCharts() {
      //var start = this.startOfLastMonth();
      //var end = this.endOfLastMonth();
      var start=this.filterCharts.dateModel.month;
      var end=dayjs(start).endOf("month");
      //日期范围是12个月
      var firstStart=dayjs(start).subtract(11,"month");
      this.filterCharts.timeRange = [firstStart, end];
      this.filterCharts.groupByDay=false;
      this.filterCharts.groupByWeek=false;
      this.filterCharts.groupByMonth=true;
      await this.onSearchCharts();
    },
    //显示图表
    async clickSelCharts() {
      this.noClickChartsClass = "";
      this.noClickTableClass = "my-btn-noclick";
      this.showCharts = true;
      this.showTable = false;
      await this.onSearchCharts();
    },
    //显示表格
    async clickSelTable() {
      this.noClickChartsClass = "my-btn-noclick";
      this.noClickTableClass = "";
      this.showCharts = false;
      this.showTable = true;
      this.onSearchTable();
    },
    getCondition(){
      if (
        !this.filterCharts.timeRange ||
        this.filterCharts.timeRange.length != 2
      ) {
        this.$message({ message: "请选择日期范围", type: "warning" });
        return false;
      }
      var startDate = dayjs(this.filterCharts.timeRange[0]).format(
        "YYYY-MM-DD"
      );
      var endDate = dayjs(this.filterCharts.timeRange[1]).format("YYYY-MM-DD");
      //if (!this.filterCharts.keyWord || !this.filterCharts.keyWord.length) {
        //this.$message({ message: "请选择内容项", type: "warning" });
        //return false;
      //}
      var parm = {
        startDate,
        endDate,
        sendWarehouses: this.filterCharts.sendWarehouse.join(","),
        platforms: this.filterCharts.platform.join(","),
        shopCodes: this.filterCharts.shopCode.join(","),
        keyWords: this.filterCharts.keyWord.join(","),
        groupIds: this.filterCharts.groupId.join(","),
        groupByDay:this.filterCharts.groupByDay,
        groupByWeek:this.filterCharts.groupByWeek,
        groupByMonth:this.filterCharts.groupByMonth,
      };
      return parm;
    },
    //图表搜索
    async onSearchCharts() {
      if(this.showTable){
        this.onSearchTable();
        return;
      }
      var parm=this.getCondition();
      if(parm===false){
        return false;
      }
      this.loadingMyChart=true;
      const res = await getOrderCharts(parm);
      this.loadingMyChart=false;
      if (!res?.code) {
        return false;
      }
      var chartDom = document.getElementById("echartReport");
      document.getElementById("echartReportNoData").innerHTML = "暂无数据";
      document.getElementById("echartReportNoData").hidden = true;
      this.myChart && this.myChart.clear();
      this.myChartOptions = res.data;
      if (!res.data) {
        this.$message({ message: "没有数据!", type: "warning" });
        document.getElementById("echartReportNoData").hidden = false;
        return;
      }

      this.myChart = this.myChart ?? echarts.init(chartDom);

      var option = await this.Getoptions(res.data);
      await option && this.myChart.setOption(option);
    },
    //图表配置
    async Getoptions(element) {
      var colors = [
        "#5470C6",
        "#c77eb5",
        "#EE6666",
        "#409EFF",
        "#00ae9d",
        "#67C23A",
      ];
      var series = [];
      element.series.forEach((s) => {
        if (!this.filterCharts.keyWord.length || this.filterCharts.keyWord.indexOf(s.yAxisIndex) != -1)
          series.push({ smooth: true, ...s });
      });
      var legendData = [];
      element.legend.forEach((s) => {
        var sVal = this.Ylist.find(
          (item) => s.substring(s.length - item.label.length) == item.label
        )?.value;
        if (!this.filterCharts.keyWord.length ||this.filterCharts.keyWord.indexOf(sVal) != -1) legendData.push(s);
      });
      var yAxis = [];
      var left = true;
      var leftOffset = 0;
      var rightOffet = 0;
      var ii = 0;
      this.Ylist.forEach((s) => {
        var isShow = !this.filterCharts.keyWord.length || this.filterCharts.keyWord.indexOf(s.value) != -1;
        yAxis.push({
          type: "value",
          name: s.label,
          show: isShow,
          axisLabel: {
            formatter: "{value}" + s.unit,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: colors[ii++],
            },
          },
          position: left ? "left" : "right",
          offset: left ? leftOffset : rightOffet,
        });
        if (isShow) {
          left ? (leftOffset += 50) : (rightOffet += 50);
          left = !left;
        }
      });
      var option = {
        title: { text: element.title },
        tooltip: {
          trigger: "axis",
          textStyle: { align: "left" },
          /*formatter:function(params){
              var relList=[];
              for (var i = 0, l = params.length; i < l; i++) {  
                var relVal = params[i].marker + params[i].seriesName + ' : ' + params[i].value+"件";
                relList.push(relVal);  console.log(params[i])
              }  
           return relList.join("<br/>");
          }*/
        },
        legend: {
          formatter: function (name) {
            return echarts.format.truncateText(
              name,
              200,
              "10px Microsoft Yahei",
              "..."
            );
          },
          tooltip: {
            show: true,
          },
          data: legendData,
          type: "scroll",
          pageIconColor: "#409EFF",
          pageIconInactiveColor: "#909399",
          width: "90%",
        },
        grid: {
          left: "3%",
          right: "3%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            magicType: { show: true, type: ["line", "bar"] },
          },
        },
        xAxis: {
          type: "category",
          data: element.xAxis,
        },
        yAxis: yAxis,
        series: series,
      };
      return option;
    },
    async onSearchTable() {     
      var parm=this.getCondition();
      if(parm===false){
            return;
      }
      this.loadingMyTable=true;
      const res = await getOrderChartsList(parm);
      this.loadingMyTable=false;
      if (!res?.code) {
        return false;
      }
      this.tableData = res.data;
    },
    async onExport(){
        var params=this.getCondition();
        if(params===false){
            return;
        }
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportOrderChartsList(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','订单整体看板_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click();
    },

    /*==未发货===Start===============================================================================================*/
    //未发货导出
    async onExportNotSend(){
      var para=this.getOrderSumCondition();
      if(!para) return false;
      var pager =this.$refs.pager.getPager();
      var para2 = { 
        ...para,
        ...this.dialogFilter
      };
      const params = {
        ...pager,
        ...this.pager,
        ...para
      };
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportOrderNotSend(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','订单未发货_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click();
    },
    //未发货统计查询
    async searchOrderNotSend(){
      this.$refs.pager.setPage(1);
      await this.getOrderNotSend();
    },
    //未发货统计排序
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
        if(this.pager){
           this.pager.OrderBy = this.pager.OrderBy=="shopName"?"shopCode":this.pager.OrderBy;
           this.pager.OrderBy = this.pager.OrderBy=="platformName"?"platform":this.pager.OrderBy;
        }
      await this.searchOrderNotSend();
    },
    //获取未发货统计
    async getOrderNotSend() {     
      var para=this.getOrderSumCondition();
      if(!para) return false;
      var pager =this.$refs.pager.getPager();
      var para2 = { 
        ...para,
        ...this.dialogFilter
      };
      const params = {
        ...pager,
        ...this.pager,
        ...para
      };
      this.listLoading = true;
      const res = await getOrderNotSend(params);
      this.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.total = res.data.total;
      const data = res.data.list;
      data.forEach((d) => {
        d._loading = false;
      });
      this.dialogTableData = data;
      this.summaryarry=res.data.summary||{};
    },
    //显示未发货统计弹窗
    async showDialogOrderCountNotSend(){
        await this.showDialog("orderCountNotSend");
    },
    //显示发货时长弹窗
    showDialogTimeRange(){
        //await this.showDialog("timeRange");
    },
    //显示弹窗通用
    async showDialog(typeName){
      //清空条件
      this.dialogFilter.timeRangeCondition=null;
      this.dialogFilter.timeRange=null;
      this.dialogFilter.IsSend=null;

      //显示弹窗
      this.dialogVisible=true;
      if(typeName=="orderCountNotSend"){           
        this.dialogFilterShow=true;
        this.dialogTitle="未发货统计";
        this.dialogFilter.IsSend = null;//未发货
        setTimeout(async () => {
          await this.searchOrderNotSend();
        }, 500);
      }
      else if (typeName=="timeRange"){
        this.dialogFilterShow=true;
        this.dialogTitle="发货时长明细";
        this.dialogFilter.timeRangeCondition=1;
        this.dialogFilter.timeRange=10;
      }
           
    },
    //未发货统计 - 单元格点击事件
   async cellclick(row, column, cell, event){        
     if (column.prop=='orderCountNotSend') {              
        this.orderNotSendDetail.visible=true;
        this.orderNotSendDetail.title="未发货明细_"+row.proCode;
        this.orderNotSendDetail.filter.proCode=row.proCode;
        setTimeout(async() => {
          await this.searchOrderNotSendDetail();
        }, 500);       
     }    
   },
   //搜索未发货明细
   async searchOrderNotSendDetail(){   
      this.$refs.pagerDetail.setPage(1);
      await this.getOrderNotSendDetail();
   },
   //导出未发货明细
   async onExportNotSendDetail(){
      var para=this.getOrderSumCondition();
      if(!para) return false;
      var pager =this.$refs.pagerDetail.getPager();
      var para2 = { 
        ...para,
        ...this.dialogFilter
      };
      const params = {
        ...pager,
        ...this.orderNotSendDetail.pager,
        ...para,
        proCode:this.orderNotSendDetail.filter.proCode
      };
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportOrderCountNotSendDetail(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','未发货明细_'+this.orderNotSendDetail.filter.proCode+'_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click();
   },
   //排序 未发货明细
   async sortchangeOrderNotSendDetail(column){
      if(!column.order)
        this.orderNotSendDetail.pager={};
      else
        this.orderNotSendDetail.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
        if(this.orderNotSendDetail.pager){
           this.orderNotSendDetail.pager.OrderBy = this.orderNotSendDetail.pager.OrderBy=="brandName"?"brandId":this.orderNotSendDetail.pager.OrderBy;
      }
      await this.searchOrderNotSendDetail();
   },
   //获取未发货明细
   async getOrderNotSendDetail(){
      var para=this.getOrderSumCondition();
      if(!para) return false;
      var pager =this.$refs.pagerDetail.getPager();
      var para2 = { 
        ...para,
        ...this.dialogFilter
      };
      const params = {
        ...pager,
        ...this.orderNotSendDetail.pager,
        ...para,
        proCode:this.orderNotSendDetail.filter.proCode
      };
      this.orderNotSendDetail.listLoading=true;
      const res = await getOrderCountNotSendDetail(params);
      this.orderNotSendDetail.listLoading = false;
      if (!res?.success) {
        this.orderNotSendDetail.total = 0;
        this.orderNotSendDetail.tableData=[];
        return;
      }
      this.orderNotSendDetail.total = res.data.total;
      const data = res.data.list;
      data.forEach((d) => {
        d._loading = false;
      });
      this.orderNotSendDetail.tableData=data;
   },
   async copyOrderNotSendDetailOrderNos(){
      var para=this.getOrderSumCondition();
      if(!para) return false;
      var pager =this.$refs.pagerDetail.getPager();
      var para2 = { 
        ...para,
        ...this.dialogFilter
      };
      const params = {
        ...pager,
        ...this.orderNotSendDetail.pager,
        ...para,
        proCode:this.orderNotSendDetail.filter.proCode
      };
      const res = await getOrderCountNotSendDetailOrderNos(params);
      if (!res?.success) {
        return;
      }
      this.doCopy(res.data);
   },
   /*==未发货===End===============================================================================================*/

    /*==延迟扣款===Start===============================================================================================*/
    //延迟扣款导出
    async onExportDeductMoney(){
      var para=this.getOrderSumCondition();
      if(!para) return false;
      var pager =this.$refs.deductMoneyPager.getPager();
      var para2 = { 
        ...para,
        ...this.dialogFilter
      };
      const params = {
        ...pager,
        ...this.pager,
        ...para
      };
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportDeductMoneySum(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','订单延迟发货扣款统计_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click();
    },
    //延迟扣款统计查询
    async searchDeductMoney(){
      this.$refs.deductMoneyPager.setPage(1);
      await this.getDeductMoney();
    },
    //延迟扣款统计排序
    async sortchangeDeductMoney(column){
      if(!column.order)
        this.deductMoney.pager={};
      else
        this.deductMoney.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
        if(this.deductMoney.pager){
           this.deductMoney.pager.OrderBy = this.deductMoney.pager.OrderBy=="shopName"?"shopCode":this.deductMoney.pager.OrderBy;
           this.deductMoney.pager.OrderBy = this.deductMoney.pager.OrderBy=="platformName"?"platform":this.deductMoney.pager.OrderBy;
        }
      await this.searchDeductMoney();
    },
    //获取延迟扣款统计
    async getDeductMoney() {     
      var para=this.getOrderSumCondition();
      if(!para) return false;
      var pager =this.$refs.deductMoneyPager.getPager();
      var para2 = { 
        ...para,
        ...this.dialogFilter
      };
      const params = {
        ...pager,
        ...this.deductMoney.pager,
        ...para
      };
      this.deductMoney.listLoading = true;
      const res = await getDeductMoneySum(params);
      this.deductMoney.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.deductMoney.total = res.data.total;
      const data = res.data.list;
      data.forEach((d) => {
        d._loading = false;
      });
      this.deductMoney.tableData = data;
      this.deductMoney.summaryarry=res.data.summary||{};
    },
    //显示延迟发货扣款弹窗
    async showDialogDeductMoney(){
        //清空条件
      this.dialogFilter.timeRangeCondition=null;
      this.dialogFilter.timeRange=null;
      this.dialogFilter.IsSend=null;

      //显示弹窗
      this.deductMoney.visible=true;
      this.deductMoney.title="延迟发货扣款统计";
      setTimeout(async () => {
          await this.searchDeductMoney();
      }, 500);
    },
    //延迟扣款统计 - 单元格点击事件
   async cellclickDeductMoney(row, column, cell, event){        
     if (column.prop=='amount') {              
        this.deductMoneyDetail.visible=true;
        this.deductMoneyDetail.title="延迟发货扣款明细_"+row.shopName;
        this.deductMoneyDetail.filter.shopCode=row.shopCode;
        setTimeout(async() => {
          await this.searchDeductMoneyDetail();
        }, 500);
     }    
   },
   //搜索延迟发货扣款明细
   async searchDeductMoneyDetail(){   
      this.$refs.deductMoneyDetailPager.setPage(1);
      await this.getDeductMoneyDetail();
   },
   //导出延迟发货扣款明细
   async onExportDeductMoneyDetail(){
        var para=this.getOrderSumCondition();
        if(!para) return false;
        var pager =this.$refs.deductMoneyDetailPager.getPager();
        var para2 = { 
          ...para,
          ...this.dialogFilter
        };
        const params = {
          ...pager,
          ...this.deductMoneyDetail.pager,
          ...para,
          shopCodes:this.deductMoneyDetail.filter.shopCode
        };
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportDeductMoneyDetail(params);
        loadingInstance.close();
        if(!res?.data) return;
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','延迟发货扣款明细_'+this.deductMoneyDetail.filter.shopName+'_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click();
   },
   //排序 未发货明细
   async sortchangeDeductMoneyDetail(column){
      if(!column.order)
        this.deductMoneyDetail.pager={};
      else
        this.deductMoneyDetail.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
        if(this.deductMoneyDetail.pager){
           this.deductMoneyDetail.pager.OrderBy = this.deductMoneyDetail.pager.OrderBy=="brandName"?"brandId":this.deductMoneyDetail.pager.OrderBy;
      }
      await this.searchDeductMoneyDetail();
   },
   //获取未发货明细
   async getDeductMoneyDetail(){
      var para=this.getOrderSumCondition();
      if(!para) return false;
      var pager =this.$refs.deductMoneyDetailPager.getPager();
      var para2 = { 
        ...para,
        ...this.dialogFilter
      };
      const params = {
        ...pager,
        ...this.deductMoneyDetail.pager,
        ...para,
        shopCodes:this.deductMoneyDetail.filter.shopCode
      };
      this.deductMoneyDetail.listLoading=true;
      const res = await getDeductMoneyDetail(params);
      this.deductMoneyDetail.listLoading = false;
      if (!res?.success) {
        this.deductMoneyDetail.total = 0;
        this.deductMoneyDetail.tableData=[];
        return;
      }
      this.deductMoneyDetail.total = res.data.total;
      const data = res.data.list;
      data.forEach((d) => {
        d._loading = false;
      });
      this.deductMoneyDetail.tableData=data;
   },
   async copyDeductMoneyDetailOrderNos(){
      var para=this.getOrderSumCondition();
      if(!para) return false;
      var pager =this.$refs.deductMoneyDetailPager.getPager();
      var para2 = { 
        ...para,
        ...this.dialogFilter
      };
      const params = {
        ...pager,
        ...this.deductMoneyDetail.pager,
        ...para,
        shopCodes:this.deductMoneyDetail.filter.shopCode
      };

      const res = await getDeductMoneyDetailOrderNos(params);
      if (!res?.success) {
        return;
      }
      this.doCopy(res.data);
   },
   /*==延迟扣款===End===============================================================================================*/

    /*==快递违禁品罚款===Start===============================================================================================*/
    //快递罚款导出
    async onExportDeductMoneyTaboo(){
      var para=this.getOrderSumCondition();
      if(!para) return false;
      var pager =this.$refs.deductMoneyTabooPager.getPager();
      var para2 = { 
        ...para,
        // ...this.dialogFilter
      };
      const params = {
        ...pager,
        ...this.deductMoneyTaboo.pager,
        ...para
      };
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportDeductMoneyTabooSum(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','快递违禁品扣款统计_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click();
    },
    //快递违禁品扣款统计查询
    async searchDeductMoneyTaboo(){
      this.$refs.deductMoneyTabooPager.setPage(1);
      await this.getDeductMoneyTaboo();
    },
    //快递违禁品扣款统计排序
    async sortchangeDeductMoneyTaboo(column){
      if(!column.order)
        this.deductMoneyTaboo.pager={};
      else
        this.deductMoneyTaboo.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
        if(this.deductMoneyTaboo.pager){
           this.deductMoneyTaboo.pager.OrderBy = this.deductMoneyTaboo.pager.OrderBy=="shopName"?"shopCode":this.deductMoneyTaboo.pager.OrderBy;
           this.deductMoneyTaboo.pager.OrderBy = this.deductMoneyTaboo.pager.OrderBy=="platformName"?"platform":this.deductMoneyTaboo.pager.OrderBy;
        }
      await this.searchDeductMoneyTaboo();
    },
    //获取快递违禁品扣款统计
    async getDeductMoneyTaboo() {     
      var para=this.getOrderSumCondition();
      if(!para) return false;
      var pager =this.$refs.deductMoneyTabooPager.getPager();
      var para2 = { 
        ...para,
        // ...this.dialogFilter
      };
      const params = {
        ...pager,
        ...this.deductMoneyTaboo.pager,
        ...para
      };
      this.deductMoneyTaboo.listLoading = true;
      const res = await getDeductMoneyTabooSum(params);
      this.deductMoneyTaboo.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.deductMoneyTaboo.total = res.data.total;
      const data = res.data.list;
      data.forEach((d) => {
        d._loading = false;
      });
      this.deductMoneyTaboo.tableData = data;
      this.deductMoneyTaboo.summaryarry=res.data.summary||{};
    },
    //显示快递违禁品扣款弹窗
    async showDialogDeductMoneyTaboo(){
        //清空条件
      this.dialogFilter.timeRangeCondition=null;
      this.dialogFilter.timeRange=null;

      //显示弹窗
      this.deductMoneyTaboo.visible=true;
      this.deductMoneyTaboo.title="快递违禁品扣款统计";
      setTimeout(async () => {
          await this.searchDeductMoneyTaboo();
      }, 500);
    },
    //快递违禁品扣款统计 - 单元格点击事件
   async cellclickDeductMoneyTaboo(row, column, cell, event){        
     if (column.prop=='deductMoneyTaboo') {              
        this.deductMoneyTabooDetail.visible=true;
        this.deductMoneyTabooDetail.title="快递违禁品扣款明细_"+row.shopName;
        this.deductMoneyTabooDetail.filter.shopCode=row.shopCode;
        this.deductMoneyTabooDetail.filter.sendWarehouse=row.sendWarehouse.toString();
        this.deductMoneyTabooDetail.filter.platform=row.platform.toString();
        this.deductMoneyTabooDetail.filter.groupId=row.groupId.toString();
        setTimeout(async() => {
          await this.searchDeductMoneyTabooDetail();
        }, 500);
     }    
   },
   //搜索快递违禁品扣款明细
   async searchDeductMoneyTabooDetail(){   
      this.$refs.deductMoneyTabooDetailPager.setPage(1);
      await this.getDeductMoneyTabooDetail();
   },
   //导出快递违禁品扣款明细
   async onExportDeductMoneyTabooDetail(){
        var para=this.getOrderSumCondition();
        if(!para) return false;
        var pager =this.$refs.deductMoneyTabooDetailPager.getPager();
        var para2 = { 
          ...para,
          ...this.dialogFilter
        };
        const params = {
          ...pager,
          ...this.deductMoneyTabooDetail.pager,
          ...para,
          sendWarehouses:this.deductMoneyTabooDetail.filter.sendWarehouse,
          platforms:this.deductMoneyTabooDetail.filter.platform,
          shopCodes:this.deductMoneyTabooDetail.filter.shopCode,
          directorGroupIds:this.deductMoneyTabooDetail.filter.groupId
        };
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportDeductMoneyTabooDetail(params);
        loadingInstance.close();
        if(!res?.data) return;
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','快递违禁品扣款款明细_'+this.deductMoneyTabooDetail.filter.shopName+'_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click();
   },
   //排序 快递违禁品扣款明细
   async sortchangeDeductMoneyTabooDetail(column){
      if(!column.order)
        this.deductMoneyTabooDetail.pager={};
      else
        this.deductMoneyTabooDetail.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
        if(this.deductMoneyTabooDetail.pager){
           this.deductMoneyTabooDetail.pager.OrderBy = this.deductMoneyTabooDetail.pager.OrderBy=="brandName"?"brandId":this.deductMoneyTabooDetail.pager.OrderBy;
      }
      await this.searchDeductMoneyTabooDetail();
   },
   //获取快递违禁品扣款明细
   async getDeductMoneyTabooDetail(){
      var para=this.getOrderSumCondition();
      if(!para) return false;
      var pager =this.$refs.deductMoneyTabooDetailPager.getPager();
      var para2 = { 
        ...para,
        ...this.dialogFilter
      };
      const params = {
        ...pager,
        ...this.deductMoneyTabooDetail.pager,
        ...para,
        sendWarehouses:this.deductMoneyTabooDetail.filter.sendWarehouse,
        platforms:this.deductMoneyTabooDetail.filter.platform,
        shopCodes:this.deductMoneyTabooDetail.filter.shopCode,
        directorGroupIds:this.deductMoneyTabooDetail.filter.groupId
      };
      this.deductMoneyTabooDetail.listLoading=true;
      const res = await getDeductMoneyTabooDetail(params);
      this.deductMoneyTabooDetail.listLoading = false;
      if (!res?.success) {
        this.deductMoneyTabooDetail.total = 0;
        this.deductMoneyTabooDetail.tableData=[];
        return;
      }
      this.deductMoneyTabooDetail.total = res.data.total;
      const data = res.data.list;
      data.forEach((d) => {
        d._loading = false;
      });
      this.deductMoneyTabooDetail.tableData=data;
   },
   async copyDeductMoneyTabooDetailOrderNos(){
      var para=this.getOrderSumCondition();
      if(!para) return false;
      var pager =this.$refs.deductMoneyTabooDetailPager.getPager();
      var para2 = { 
        ...para,
        ...this.dialogFilter
      };
      const params = {
        ...pager,
        ...this.deductMoneyTabooDetail.pager,
        ...para,
        sendWarehouses:this.deductMoneyTabooDetail.filter.sendWarehouse,
        platforms:this.deductMoneyTabooDetail.filter.platform,
        shopCodes:this.deductMoneyTabooDetail.filter.shopCode,
        directorGroupIds:this.deductMoneyTabooDetail.filter.groupId
      };

      const res = await getDeductMoneyTabooDetailOrderNos(params);
      if (!res?.success) {
        return;
      }
      this.doCopy(res.data);
   },
   /*==快递违禁品明细===End===============================================================================================*/

   /*==代拍订单===Start===============================================================================================*/
    //代拍订单统计导出
    async onExportAgentAmount(){
      var para=this.getOrderSumCondition();
      if(!para) return false;
      var pager =this.$refs.agentAmountPager.getPager();
      var para2 = { 
        ...para,
        // ...this.dialogFilter
      };
      const params = {
        ...pager,
        ...this.agentAmount.pager,
        ...para
      };
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportOrderReportAgentAmount(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','代拍订单统计_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click();
    },
    //代拍订单统计查询
    async searchAgentAmount(){
      this.$refs.agentAmountPager.setPage(1);
      await this.getAgentAmount();
    },
    //代拍订单统计排序
    async sortchangeAgentAmount(column){
      if(!column.order)
        this.agentAmount.pager={};
      else
        this.agentAmount.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      await this.searchAgentAmount();
    },
    //获取代拍订单统计
    async getAgentAmount() {     
      var para=this.getOrderSumCondition();
      if(!para) return false;
      var pager =this.$refs.agentAmountPager.getPager();
      var para2 = { 
        ...para,
        // ...this.dialogFilter
      };
      const params = {
        ...pager,
        ...this.agentAmount.pager,
        ...para
      };
      this.agentAmount.listLoading = true;
      const res = await getOrderReportAgentAmount(params);
      this.agentAmount.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.agentAmount.total = res.data.total;
      const data = res.data.list;
      data.forEach((d) => {
        d._loading = false;
      });
      this.agentAmount.tableData = data;
      this.agentAmount.summaryarry=res.data.summary||{};
    },
    //显示代拍订单弹窗
    async showDialogAgentAmount(){
        //清空条件
      this.dialogFilter.timeRangeCondition=null;
      this.dialogFilter.timeRange=null;

      //显示弹窗
      this.agentAmount.visible=true;
      this.agentAmount.title="代拍订单统计";
      setTimeout(async () => {
          await this.searchAgentAmount();
      }, 500);
    },
    //代拍订单统计 - 单元格点击事件
   async cellclickAgentAmount(row, column, cell, event){        
     if (column.prop=='agentAmount') {              
        this.agentAmountDetail.visible=true;
        this.agentAmountDetail.title="代拍订单明细_"+row.shopName;
        this.agentAmountDetail.filter.shopCode=row.shopCode;
        this.agentAmountDetail.filter.platform=row.platform.toString();
        //this.agentAmountDetail.filter.groupId=row.groupId.toString();
        setTimeout(async() => {
          await this.searchAgentAmountDetail();
        }, 500);
     }    
   },
   //搜索代拍订单明细
   async searchAgentAmountDetail(){   
      this.$refs.agentAmountDetailPager.setPage(1);
      await this.getAgentAmountDetail();
   },
   //导出代拍订单明细
   async onExportAgentAmountDetail(){
        var para=this.getOrderSumCondition();
        if(!para) return false;
        var pager =this.$refs.agentAmountDetailPager.getPager();
        var para2 = { 
          ...para,
          ...this.dialogFilter
        };
        const params = {
          ...pager,
          ...this.agentAmountDetail.pager,
          ...para,
          sendWarehouses:this.agentAmountDetail.filter.sendWarehouse,
          platforms:this.agentAmountDetail.filter.platform,
          shopCodes:this.agentAmountDetail.filter.shopCode,
          directorGroupIds:this.agentAmountDetail.filter.groupId
        };
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportOrderReportAgentAmountDetail(params);
        loadingInstance.close();
        if(!res?.data) return;
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','代拍订单明细_'+this.agentAmountDetail.filter.shopName+'_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click();
   },
   //排序 代拍订单明细
   async sortchangeAgentAmountDetail(column){
      if(!column.order)
        this.agentAmountDetail.pager={};
      else
        this.agentAmountDetail.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}

      await this.searchAgentAmountDetail();
   },
   //获取代拍订单明细
   async getAgentAmountDetail(){
      var para=this.getOrderSumCondition();
      if(!para) return false;
      var pager =this.$refs.agentAmountDetailPager.getPager();
      var para2 = { 
        ...para,
        ...this.dialogFilter
      };
      const params = {
        ...pager,
        ...this.agentAmountDetail.pager,
        ...para,
        sendWarehouses:this.agentAmountDetail.filter.sendWarehouse,
        platforms:this.agentAmountDetail.filter.platform,
        shopCodes:this.agentAmountDetail.filter.shopCode,
        directorGroupIds:this.agentAmountDetail.filter.groupId
      };
      this.agentAmountDetail.listLoading=true;
      const res = await getOrderReportAgentAmountDetail(params);
      this.agentAmountDetail.listLoading = false;
      if (!res?.success) {
        this.agentAmountDetail.total = 0;
        this.agentAmountDetail.tableData=[];
        return;
      }
      this.agentAmountDetail.total = res.data.total;
      const data = res.data.list;
      data.forEach((d) => {
        d._loading = false;
      });
      this.agentAmountDetail.tableData=data;
   },
   async copyAgentAmountDetailOrderNos(){
      var para=this.getOrderSumCondition();
      if(!para) return false;
      var pager =this.$refs.agentAmountDetailPager.getPager();
      var para2 = { 
        ...para,
        ...this.dialogFilter
      };
      const params = {
        ...pager,
        ...this.agentAmountDetail.pager,
        ...para,
        sendWarehouses:this.agentAmountDetail.filter.sendWarehouse,
        platforms:this.agentAmountDetail.filter.platform,
        shopCodes:this.agentAmountDetail.filter.shopCode,
        directorGroupIds:this.agentAmountDetail.filter.groupId
      };

      const res = await getOrderReportAgentAmountDetailOrderNos(params);
      if (!res?.success) {
        return;
      }
      this.doCopy(res.data);
   },
   /*==代拍订单===End===============================================================================================*/

   /*==总平均快递费===Start===============================================================================================*/
    //获取代拍订单统计
    async searchExpress() {     
      var para=this.getOrderSumCondition();
      if(!para) return false;

      this.expressCharts.loading = true;
      const res = await getOrderExpressAnalysis(para);
      this.expressCharts.loading = false;
      if (!res?.success) {
        return;
      }

      var chartDom = document.getElementById("expressCharts");
      this.expressCharts.myChart && this.expressCharts.myChart.clear();
      this.expressCharts.myChart = this.expressCharts.myChart ?? echarts.init(chartDom);

      var option = await this.GetoptionsExpress(res.data,'','bar');
      await option && this.expressCharts.myChart.setOption(option);
    },
    //显示弹窗
    async showDialogExpress(){
      //显示弹窗
      this.expressCharts.visible=true;
      this.expressCharts.title="快递分析";
      setTimeout(async () => {
          await this.searchExpress();
      }, 500);
    },
    //折线/柱形图图表配置
    async GetoptionsExpress(element,title,type) {
      var colors = [
        "#5470C6",
        "#c77eb5",
        "#EE6666",
        "#409EFF",
        "#00ae9d",
        "#67C23A",
      ];
      var series = [];
      element.series.forEach((s) => {
        series.push({ smooth: true, ...s });          
      });
      var legendData = element.legend||[];
      var yAxis = [];
      var left = true;
      var leftOffset = 0;
      var rightOffet = 0;
      var ii = 0;
      this.expressCharts.Ylist.forEach((s) => {
        yAxis.push({
          type: "value",
          name: s.label,
          show: true,
          axisLabel: {
            formatter: "{value}" + s.unit,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: colors[ii++],
            },
          },
          position: left ? "left" : "right",
          offset: left ? leftOffset : rightOffet,
        });
        left ? (leftOffset += 70) : (rightOffet += 70);
        left = !left;
      });
      var option = {
        title: { text: title },
        tooltip: {
          trigger: "axis",
          textStyle: { align: "left" },
        },
        legend: {
          formatter: function (name) {
            return echarts.format.truncateText(
              name,
              200,
              "10px Microsoft Yahei",
              "..."
            );
          },
          tooltip: {
            show: true,
          },
          data: legendData,
          type: "scroll",
          pageIconColor: "#409EFF",
          pageIconInactiveColor: "#909399",
          width: "75%",
        },
        grid: {
          left: "3%",
          right: "3%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            magicType: { show: true, type: ["line", "bar"] },
            //restore: { show: false },
          },
        },
        xAxis: {
          type: "category",
          data: element.xAxis,
          axisLabel: {
            interval:0,
            rotate:0  
          },
        },
        yAxis: yAxis,
        series: series,
      };
      return option;
    },
    /*==总平均快递费===End===============================================================================================*/

    doCopy: function (val) {       
      let that=this;                         
      this.$copyText(val).then(function (e) {
          that.$message({ message: "内容已复制到剪切板！", type: "success" });
      }, function (e) {
          that.$message({ message: "抱歉，复制失败！", type: "warning" });
      })
    },
  },
  watch: {
    //选择内容项不需要刷新即可查看
    async "filterCharts.keyWord"(val) {
      if (this.myChartOptions && this.myChart) {
        this.myChart.clear();
        var option = await this.Getoptions(this.myChartOptions);
        await option && this.myChart.setOption(option);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
.row-condition {
  margin-top: 10px;
  margin-bottom: 20px;
}
.row-value {
  color: #303133;
  font-size: 16px;
  font-weight: 500;
  margin-top: 10px;
  margin-bottom: 30px;
}
.content-unit {
  font-size: 10px;
  color: #909399;
}
.my-title {
  font-weight: 600;
  font-size: 15px;
}
.my-txtbtn {
  margin-left: 15px;
  margin-right: 15px;
}
.grid-header {
  color: #606266;
  font-size: 14px;
}
.el-col {
  text-align: center;
}
.el-row {
  margin-right: 50px;
}
.el-icon-question {
  color: #909399;
}
.row-report {
  margin-top: 0px;
}
.ad-form-query2 {
  margin-left: 108px;
  margin-bottom: 8px;
}
.my-btn-noclick {
  color: #303133;
}
.my-split {
  font-size: 10px;
  color: #909399;
}
::v-deep.el-table th > .cell{
  padding-left: 10px;
  padding-right: 14px;
}
.canclick{
  color:#409EFF;
  cursor: pointer;
}

::v-deep.el-form-item--mini.el-form-item, .el-form-item--small.el-form-item{
  margin-bottom: 5px;
}

.dateBtn{
  position: relative !important;
}
.dateBtn .el-date-editor{
  position: absolute;
  top:0;
  left:0;
  opacity: 0;
}

#expressCharts{
  width:95%;
  height:420px;
}
</style>