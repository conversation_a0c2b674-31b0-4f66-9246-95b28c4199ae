<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss" />
                <el-select v-model="ListInfo.costTypes" placeholder="成本" class="publicCss" clearable filterable multiple
                    collapse-tags>
                    <el-option label="涨价" value="涨价" />
                    <el-option label="降价" value="降价" />
                    <el-option label="不变" value="不变" />
                </el-select>
                <el-select v-model="ListInfo.qualitys" placeholder="品质" class="publicCss" clearable filterable multiple
                    collapse-tags>
                    <el-option label="提质" value="提质" />
                    <el-option label="降质" value="降质" />
                    <el-option label="不变" value="不变" />
                </el-select>
                <el-select v-model="ListInfo.packageChange" placeholder="包装变化" class="publicCss" clearable>
                    <el-option label="是" value="是" />
                    <el-option label="否" value="否" />
                </el-select>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.optimizeUserNames"
                    v-model="ListInfo.optimizeUserNames" placeholder="优化人/若输入多条请按回车" :clearable="true"
                    :clearabletext="true" :maxRows="100" :maxlength="1000000" @callback="proCodeCallback" title="优化人"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0;height: 400px;" :loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import { GetYWOptimizeCostChangeLogList } from '@/api/inventory/YWOptimizeGoods'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'costType', label: '成本', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'quality', label: '品质', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'packageChange', label: '包装变化', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '优化时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdUserName', label: '优化人', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, inputYunhan
    },
    props: {
        editRow: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
                endTime: dayjs().format('YYYY-MM-DD'),
                goodsCode: this.editRow.goodsCode,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        proCodeCallback(val) {
            this.ListInfo.optimizeUserNames = val
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetYWOptimizeCostChangeLogList(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
