<template>
    <div>
      <div style="height: 90vh; box-sizing: border-box; padding-top: 20px;">
        <el-tabs :tab-position="tabPosition" style="height: 100%">
          <el-tab-pane >
            <span slot="label"><i class="el-icon-user"></i>&nbsp;员工设置</span>
            <div style="width: 100%">
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="所有员工" name="a" v-if="checkPermission('api:Inventory:PackagesProcessing:AllEmployees')">
                    <div style="height: 84vh;">
                        <accountsManage ref="refaccountsManage"></accountsManage>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="加工组" name="b" v-if="checkPermission('api:Inventory:PackagesProcessing:ProcessEmployees')">
                  <div style="height: 84vh;">
                        <packageaccounts key="one" :workTeamType="0" ref="refpackageaccounts0"></packageaccounts>
                    </div>
                </el-tab-pane>
                <!-- <el-tab-pane label="包装组" name="c" v-if="checkPermission('api:Inventory:PackagesProcessing:PackagesEmployees')">
                    <div style="height: 84vh;">
                        <packageaccountss key="two" :workTeamType="1" ref="refpackageaccounts1"></packageaccountss>
                    </div>
                </el-tab-pane> -->
                <el-tab-pane label="入库组" name="d" v-if="checkPermission('api:Inventory:PackagesProcessing:rkEmployees')">
                    <div style="height: 84vh;">
                        <packageaccountss key="three" :workTeamType="2" ref="refpackageaccounts2"></packageaccountss>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="文员组" name="e" v-if="checkPermission('api:Inventory:PackagesProcessing:wyEmployees')">
                    <div style="height: 84vh;">
                        <packageaccountss key="four" :workTeamType="3" ref="refpackageaccounts3"></packageaccountss>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="其他" name="f" v-if="checkPermission('api:Inventory:PackagesProcessing:qtEmployees')">
                    <div style="height: 84vh;">
                        <packageaccountss key="five" :workTeamType="4" ref="refpackageaccounts4"></packageaccountss>
                    </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-tab-pane>
          <el-tab-pane >
            <span slot="label"><i class="el-icon-service"></i>&nbsp;岗位设置</span>
              <accountsWorkPostionManage :tichengshow="false" @changework="changework"></accountsWorkPostionManage>
            </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </template>

  <script>
//   import accountsManage from '@/views/media/shooting/adjustAccounts/accountsManageedit';
  import accountsManage from '@/views/media/packagework/peoplesetfile/accountsManage.vue'
  import packageaccounts from '@/views/media/packagework/peoplesetfile/packageaccounts.vue'
  import packageaccountss from '@/views/media/packagework/peoplesetfile/packageaccountss.vue'
  import checkPermission from '@/utils/permission'
  import postset from '@/views/media/packagework/peoplesetfile/postset.vue'
  import accountsWorkPostionManage from '@/views/media/packagework/peoplesetfile/accountsWorkPostionManage.vue';
  export default {
    name: "OneHelloWorld",
    components: {accountsManage,packageaccounts,postset, accountsWorkPostionManage,packageaccountss},
    data() {
      return {
        tabPosition: "left",
        activeName: "a"
      };
    },

    mounted() {
      if (checkPermission('api:Inventory:PackagesProcessing:AllEmployees')) 
      {
        this.activeName='a';
        this.handleClick();
      }else if(checkPermission('api:Inventory:PackagesProcessing:ProcessEmployees'))
      {
        this.activeName='b';
        this.handleClick();
      }else if(checkPermission('api:Inventory:PackagesProcessing:rkEmployees'))
      {
        this.activeName='d';
        this.handleClick();
      }else if(checkPermission('api:Inventory:PackagesProcessing:wyEmployees'))
      {
        this.activeName='e';
        this.handleClick();
      }else if(checkPermission('api:Inventory:PackagesProcessing:qtEmployees'))
      {
        this.activeName='f';
        this.handleClick();
      }
    },

    methods: {
      handleClick(){
        if(this.activeName == "a"){
          this.$refs.refaccountsManage.getWorkPostList();
          this.$refs.refaccountsManage.onSearch();
        }else if(this.activeName == "b"){
          this.$refs.refpackageaccounts0.getWorkPostList();
          this.$refs.refpackageaccounts0.onSearch();
        }else if(this.activeName == "c"){
          this.$refs.refpackageaccounts1.getWorkPostList();
          this.$refs.refpackageaccounts1.onSearch();
        }else if(this.activeName == "d"){
          this.$refs.refpackageaccounts2.getWorkPostList();
          this.$refs.refpackageaccounts2.onSearch();
        }else if(this.activeName == "e"){
          this.$refs.refpackageaccounts3.getWorkPostList();
          this.$refs.refpackageaccounts3.onSearch();
        }else if(this.activeName == "f"){
          this.$refs.refpackageaccounts4.getWorkPostList();
          this.$refs.refpackageaccounts4.onSearch();
        }
      },
      changework(){
        this.handleClick();
      },
    },
  };
  </script>

  <style >
  /* .el-tabs__nav-wrap::after ,.el-tabs__active-bar {
    height: 1px;
  } */
  </style>
