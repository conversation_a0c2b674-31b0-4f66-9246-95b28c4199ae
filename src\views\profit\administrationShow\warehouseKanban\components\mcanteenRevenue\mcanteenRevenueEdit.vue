<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="120px" class="demo-ruleForm">
        <el-form-item label="支出金额：" prop="expenseAmount">
          <inputNumberYh v-model="ruleForm.expenseAmount" :fixed="2" :placeholder="'支出金额'" class="publicCss" />
        </el-form-item>
        <el-form-item label="收入金额：" prop="incomeAmount">
          <inputNumberYh v-model="ruleForm.incomeAmount" :fixed="2" :placeholder="'收入金额'" class="publicCss" />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { warehouseCanteenRevenueDataSubmit } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission'
export default {
  name: 'mcanteenRevenueEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      ruleForm: {
        incomeAmount: null,
        expenseAmount: null
      },
      rules: {
        incomeAmount: [
          { required: true, message: '请输入收入金额', trigger: 'blur' },
          { type: 'number', min: 0, message: '请输入有效的金额', trigger: 'blur' }
        ],
        expenseAmount: [
          { required: true, message: '请输入支出金额', trigger: 'blur' },
          { type: 'number', min: 0, message: '请输入有效的金额', trigger: 'blur' }
        ]
      }
    }
  },
  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    console.log(this.editInfo, 'editInfo');
    this.ruleForm = { ...this.editInfo };
  },
  methods: {
    cancellationMethod() {
      this.$emit('cancellationMethod');
    },
    async submitForm(formName) {
      try {
        const valid = await this.$refs[formName].validate();
        if (valid) {
          this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing");
          const { success, msg } = await warehouseCanteenRevenueDataSubmit(this.ruleForm);

          if (success) {
            this.$message.success(msg || '保存成功');
            this.$emit("search");
          } else {
            this.$message.error(msg || '保存失败');
          }
        }
      } catch (error) {
        console.error('表单提交失败:', error);
        this.$message.error('保存失败，请重试');
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    }
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}
</style>
