<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="快递公司:">
                    <el-select v-model="filter.CompanyId" clearable filterable placeholder="请选择快递公司" @change="getprosimstatelist(2)" style="width: 110px">
                        <el-option label="所有" value></el-option>
                        <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="快递站点:">
                    <el-select v-model="filter.prosimstate" clearable filterable placeholder="请选择快递站点" style="width: 130px">
                        <el-option label="暂无站点" value="" />
                        <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="发货仓库:">
                    <el-select v-model="filter.Warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 110px">
                        <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="发往省份:">
                    <el-input v-model="filter.Province" clearable placeholder="请输入发往省份" style="width: 110px" />
                </el-form-item>
                <el-form-item label="规则:">
                    <el-select v-model="filter.FreightRuleType" clearable filterable placeholder="状态" style="width: 110px">
                        <el-option label="所有" value></el-option>
                        <el-option label="通票1" value="0"></el-option>
                        <el-option label="通票2" value="1"></el-option>
                        <el-option label="拉均重" value="2"></el-option>
                        <el-option label="阶梯" value="3"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="状态:">
                    <el-select v-model="filter.Enabled" clearable filterable placeholder="状态" style="width: 110px">
                        <el-option label="所有" value></el-option>
                        <el-option label="启用" value="true"></el-option>
                        <el-option label="禁用" value="false"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="月份:">
                    <el-date-picker style="width: 110px" v-model="filter.yearmonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
                </el-form-item>
                <el-form-item label="时间:">
                    <el-date-picker v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
                <!-- <el-form-item v-if="checkPermission(['api:admin:api:add'])">
          <el-button type="primary" @click="onAdd">新增</el-button>
        </el-form-item> -->
                <el-form-item>
                    <a href="../static/excel/financial/快递费用规则导入模板(每个快递公司一个文件).xlsx">
                        <el-button type="primary">下载导入模板</el-button>
                    </a>
                </el-form-item>
                <el-form-item>
                    <el-button size="small" type="primary" @click="startImport">导入运费规则</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button size="small" type="primary" @click="startEnable">批量启用</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button size="small" type="primary" @click="startDelete">批量删除</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button size="small" type="primary" @click="startClone">克隆规则</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表-->
        <el-table ref="elTableExpand" v-loading="listLoading" :data="ExpressList" highlight-current-row :lazy="true" height="'100%'" style="width: 100%; height: 100%" @selection-change="onSelsChange" :default-expand-all="false" :row-class-name="getRowClass">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <!--通票1\2    -->
                        <el-table :header-cell-style="{background:'#eef1f6',color:'#606266'}" :data="props.row.ruleDetails" v-if="props.row.freightRuleType==0||props.row.freightRuleType==1">
                            <el-table-column prop="minWeight" label="最小重量" width="80">
                                <template slot-scope="scope">
                                    <div>
                                        <el-input v-model="scope.row.minWeight" v-if="!props.row.enabled" :maxlength="12" oninput="if(value){value=value.replace(/[^\d.]/g,'')}"></el-input>
                                        <span v-else>{{scope.row.minWeight}}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="maxWeight" label="最大重量" width="100">
                                <template slot-scope="scope">
                                    <div>
                                        <el-input v-model="scope.row.maxWeight" v-if="!props.row.enabled" :maxlength="12" oninput="if(value){value=value.replace(/[^\d.]/g,'')}"></el-input>
                                        <span v-else>{{scope.row.maxWeight}}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="freightMoney" label="快递费" width="80">
                                <template slot-scope="scope">
                                    <div>
                                        <el-input v-model="scope.row.freightMoney" v-if="!props.row.enabled" :maxlength="12" oninput="if(value){value=value.replace(/[^\d.]/g,'')}"></el-input>
                                        <span v-else>{{scope.row.freightMoney}}</span>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                        <!--拉均重-->
                        <el-table :data="props.row.ruleDetails" v-else-if="props.row.freightRuleType==2">
                            <el-table-column prop="minWeight" label="最小重量" width="80">
                                <template slot-scope="scope">
                                    <div>
                                        <el-input v-model="scope.row.minWeight" v-if="!props.row.enabled" :maxlength="12" oninput="if(value){value=value.replace(/[^\d.]/g,'')}"></el-input>
                                        <span v-else>{{scope.row.minWeight}}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="maxWeight" label="最大重量" width="100">
                                <template slot-scope="scope">
                                    <div>
                                        <el-input v-model="scope.row.maxWeight" v-if="!props.row.enabled" :maxlength="12" oninput="if(value){value=value.replace(/[^\d.]/g,'')}"></el-input>
                                        <span v-else>{{scope.row.maxWeight}}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="freightMoney" label="快递费" width="80">
                                <template slot-scope="scope">
                                    <div>
                                        <el-input v-model="scope.row.freightMoney" v-if="!props.row.enabled" :maxlength="12" oninput="if(value){value=value.replace(/[^\d.]/g,'')}"></el-input>
                                        <span v-else>{{scope.row.freightMoney}}</span>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                        <!--阶梯-->
                        <el-table :data="props.row.ruleDetails" v-else-if="props.row.freightRuleType==3">
                            <el-table-column prop="minWeight" label="最小重量" width="80">
                                <template slot-scope="scope">
                                    <div>
                                        <el-input v-model="scope.row.minWeight" v-if="!props.row.enabled" :maxlength="12" oninput="if(value){value=value.replace(/[^\d.]/g,'')}"></el-input>
                                        <span v-else>{{scope.row.minWeight}}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="maxWeight" label="最大重量" width="100">
                                <template slot-scope="scope">
                                    <div>
                                        <el-input v-model="scope.row.maxWeight" v-if="!props.row.enabled" :maxlength="12" oninput="if(value){value=value.replace(/[^\d.]/g,'')}"></el-input>
                                        <span v-else>{{scope.row.maxWeight}}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="firstWeight" label="首重(kg)" width="100">
                                <template slot-scope="scope">
                                    <div>
                                        <el-input v-model="scope.row.firstWeight" v-if="!props.row.enabled" :maxlength="12" oninput="if(value){value=value.replace(/[^\d.]/g,'')}"></el-input>
                                        <span v-else>{{scope.row.firstWeight}}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="firstWeightMoney" label="首重快递费(元)" width="180">
                                <template slot-scope="scope">
                                    <div>
                                        <el-input v-model="scope.row.firstWeightMoney" v-if="!props.row.enabled" :maxlength="12" oninput="if(value){value=value.replace(/[^\d.]/g,'')}"></el-input>
                                        <span v-else>{{scope.row.firstWeightMoney}}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="unitWeight" label="超首重后单元(kg)" width="180">
                                <template slot-scope="scope">
                                    <div>
                                        <el-input v-model="scope.row.unitWeight" v-if="!props.row.enabled" :maxlength="12" oninput="if(value){value=value.replace(/[^\d.]/g,'')}"></el-input>
                                        <span v-else>{{scope.row.unitWeight}}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="perUnitWeightMoney" label="每单元费(元)" width="180">
                                <template slot-scope="scope">
                                    <div>
                                        <el-input v-model="scope.row.perUnitWeightMoney" v-if="!props.row.enabled" :maxlength="12" oninput="if(value){value=value.replace(/[^\d.]/g,'')}"></el-input>
                                        <span v-else>{{scope.row.perUnitWeightMoney}}</span>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <!-- <el-table-column type="selection" width="40" /> -->
            <el-table-column type="index" width="60"></el-table-column>
            <el-table-column prop="batchNumber" label="批次号" width="180" />
            <el-table-column prop="enabled" label="状态" width="60">
                <template #default="{ row }">
                    <el-tag :type="row.enabled ? 'success' : 'danger'" disable-transitions>{{ row.enabled ? "正常" : "禁用" }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="yearMonth" label="月份" width="80" />
            <el-table-column prop="startDate" label="起始日期" width="80">
                <template #default="{ row }">
                    {{ formatCreatedTime(row,row,row.startDate) }}
                </template>
            </el-table-column>
            <el-table-column prop="endDate" label="结束日期" width="80">
                <template #default="{ row }">
                    {{ formatCreatedTime(row,row,row.endDate) }}
                </template>
            </el-table-column>
            <el-table-column prop="expressCompany" label="快递公司" width="150" />
            <el-table-column prop="prosimStateName" label="快递站点" width="150" />
            <el-table-column prop="warehouse" label="发货仓库" width="150">
                <template #default="{ row }">
                    <span>{{ formatWarehouse(row.warehouse) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="province" label="发往省份" width="140" />
            <el-table-column prop="freightRuleType" label="快递费规则" width="100">
                <template #default="{ row }">
                    <el-tag>
                        {{ row.freightRuleType==0 ? "通票1" :
                row.freightRuleType==1 ? "通票2":
                row.freightRuleType==2 ? "拉均重":
                row.freightRuleType==3 ? "阶梯":"阶梯"
             }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="createdTime" label="创建时间" width="160" />
            <el-table-column prop="createdUserName" label="创建者" width="100" />
            <el-table-column v-show="checkPermission(['api:admin:api:update', 'api:admin:api:softdelete'])" label="操作" width="120">
                <template slot-scope="scope">
                    <el-button v-show="!scope.row.enabled&&scope.row.edit&&checkPermission(['api:admin:api:update'])" type="text" size="small" icon="el-icon-circle-check-outline" @click="confirmEdit(scope.row)">保存
                    </el-button>
                    <el-button v-show="!scope.row.enabled&&!scope.row.edit&&checkPermission(['api:admin:api:update'])" type="text" size="small" @click="startEdit(scope.row)">编辑
                    </el-button>
                    <el-button v-show="!scope.row.enabled&&scope.row.edit&&checkPermission(['api:admin:api:update'])" type="text" size="small" @click="cancelEdit(scope.row)">取消
                    </el-button>
                    <el-button v-show="!scope.row.enabled&&checkPermission(['api:admin:api:softdelete'])" @click="onDelete(scope.row)" type="text" size="small">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getExpressList" />
        </template>

        <!--新增快递费规则-->
        <el-drawer v-if="checkPermission(['api:admin:user:add'])" title="新增快递费规则" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="addFormVisible" direction="btt" size="auto" class="el-drawer__wrapper" style="position: absolute" @close="closeAddForm">
            <section style="padding: 24px 48px 74px 24px">
                <el-form ref="addForm" :model="addForm" :rules="addFormRules" label-width="150px" :inline="false">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="18" :xl="18">
                            <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6">
                                <el-form-item label="发货仓库" required prop="Warehouse">
                                    <el-select v-model="addForm.Warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 110px">
                                        <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6">
                                <el-form-item label="发往省份" required prop="Province">
                                    <el-input v-model="addForm.Province" autocomplete="off" />
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6">
                                <el-form-item label="是否续重" required prop="HasContinuation">
                                    <el-select v-model="addForm.HasContinuation">
                                        <el-option label="是" value="是"></el-option>
                                        <el-option label="否" value="否"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6">
                                <el-form-item label="首续重(kg)" required prop="FirstContinuWeight">
                                    <el-input type="number" minlength="0" v-model="addForm.FirstContinuWeight" />
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6">
                                <el-form-item label="首续重费(元)" required prop="FirstContinuWeightMoney">
                                    <el-input type="number" minlength="0" v-model="addForm.FirstContinuWeightMoney" />
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6">
                                <el-form-item label="续重单元(kg)" required prop="ContinuWeight">
                                    <el-input type="number" minlength="0" v-model="addForm.ContinuWeight" />
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6">
                                <el-form-item label="每续重单元费(元)" required prop="PerContinuWeightMoney">
                                    <el-input type="number" minlength="0" v-model="addForm.PerContinuWeightMoney" />
                                </el-form-item>
                            </el-col>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6">
                            <el-form-item label="阶梯1起(kg)" required prop="FirstStart">
                                <el-input type="number" minlength="0" v-model="addForm.FirstStart" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6">
                            <el-form-item label="阶梯1终含(kg)" required prop="FirstEnd">
                                <el-input type="number" minlength="0" v-model="addForm.FirstEnd" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6">
                            <el-form-item label="阶梯1费用(元)" required prop="FirstMoney">
                                <el-input type="number" minlength="0" v-model="addForm.FirstMoney" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6">
                            <el-form-item label="阶梯2起(kg)" required prop="SecondStart">
                                <el-input type="number" minlength="0" v-model="addForm.SecondStart" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6">
                            <el-form-item label="阶梯2终含(kg)" required prop="SecondEnd">
                                <el-input type="number" minlength="0" v-model="addForm.SecondEnd" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6">
                            <el-form-item label="阶梯2费用(元)" required prop="SecondMoney">
                                <el-input type="number" minlength="0" v-model="addForm.SecondMoney" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6">
                            <el-form-item label="阶梯3起(kg)" required prop="ThirdStart">
                                <el-input type="number" minlength="0" v-model="addForm.ThirdStart" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6">
                            <el-form-item label="阶梯3终含(kg)" required prop="ThirdEnd">
                                <el-input type="number" minlength="0" v-model="addForm.ThirdEnd" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6">
                            <el-form-item label="阶梯3费用(元)" required prop="ThirdMoney">
                                <el-input type="number" minlength="0" v-model="addForm.ThirdMoney" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </section>
            <div class="drawer-footer">
                <el-button @click.native="addFormVisible = false">取消</el-button>
                <my-confirm-button type="submit" :validate="addFormvalidate" :loading="addLoading" @click="onAddSubmit" />
            </div>
        </el-drawer>

        <el-dialog title="导入运费规则" :visible.sync="dialogVisible" width="50%" v-dialogDrag>
            <span>
                <el-row>
                    <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="6">
                        <el-select v-model="importFilte.companyid" placeholder="请选择快递公司" @change="getprosimstatelist(1)" style="width: 100%">
                            <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
                        </el-select>
                    </el-col>
                    <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="6">
                        <el-select v-model="importFilte.prosimstate" placeholder="请选择快递公司站点" style="width: 100%">
                            <el-option label="暂无站点" value="28" />
                            <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
                        </el-select>
                    </el-col>
                    <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="6">
                        <el-select v-model="importFilte.warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 100%">
                            <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-col>
                    <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="6">
                        <el-date-picker v-model="importFilte.yearmonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
                    </el-col>
                    <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="6">
                        <el-date-picker v-model="importFilte.startDate" type="date" format="yyyy-MM-dd" :value-format="'yyyy-MM-dd'"  placeholder="选择起始日期"></el-date-picker>
                    </el-col>
                    <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="6">
                        <el-date-picker v-model="importFilte.endDate" type="date" format="yyyy-MM-dd" :value-format="'yyyy-MM-dd'"  placeholder="选择结束日期"></el-date-picker>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx" :http-request="uploadFile" :on-change="uploadchange" :file-list="fileList" :data="fileparm">
                            <template #trigger>
                                <el-button size="small" type="primary">选取运费规则文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="批量启用" :visible.sync="dialogbatchNumberVisible" width="400px" v-dialogDrag>
            <el-row>
                <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                    <el-input placeholder="请输入批次号" v-model="batchNumber" width="200px" :maxlength="19" oninput="if(value){value=value.replace(/[^\d]/g,'')} if(value<0){value=''}">
                        <template #prepend>批次号:</template>
                    </el-input>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
                    <el-button type="primary" @click="onEnablebatchRule">启用</el-button>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogbatchNumberVisible = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog title="批量删除" :visible.sync="dialogdeletebatchNumberVisible" width="400px" v-dialogDrag>
            <el-row>
                <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                    <el-input placeholder="请输入批次号" v-model="deletebatchNumber" width="200px"  :maxlength="19" oninput="if(value){value=value.replace(/[^\d]/g,'')} if(value<0){value=''}">
                        <template #prepend>批次号:</template>
                    </el-input>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
                    <el-button type="primary" @click="onDeletebatchRule">删除</el-button>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogdeletebatchNumberVisible = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog title="克隆规则" :visible.sync="dialogCloneVisible" width="400px" v-dialogDrag>
            <el-form ref="form" label-width="100px">
                <el-form-item label="克隆旧批次号:">
                    <el-input v-model="cloneparm.oldBatchnumber"  :maxlength="19" oninput="if(value){value=value.replace(/[^\d]/g,'')} if(value<0){value=''}"></el-input>
                </el-form-item>
                <el-form-item label="克隆到新月份:">
                    <el-date-picker v-model="cloneparm.newYearMonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月" />
                </el-form-item>
            </el-form>
            <div style="text-align: center;">
                <el-button type="primary" @click="onClone">克隆</el-button>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogCloneVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>

<script>
    import { formatTime } from "@/utils";
    import { warehouselist,formatWarehouseNew} from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";

    import {
        deleteExpressRule, updateExpressRule, addExpressRule, getPageExpressRuleList, getExpressComanyStationName,
        importExpressRule, exportExpressRule, enablebatchRule, cloneExpressRule, deleteBatchRule, getExpressComanyAll
    } from "@/api/express/express";
    export default {
        name: "Users",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow },
        data () {
            return {
                filter: {
                    StartCreatedTime: "",
                    EndCreatedTime: "",
                    Warehouse: null,
                    prosimstate: null,
                    Province: null,
                    Enabled: null,
                    FreightRuleType: null,
                    timerange: "",
                    yearmonth: null
                },
                formatWarehouse: formatWarehouseNew,
                warehouselist: warehouselist,
                deletebatchNumber: null,
                batchNumber: null,
                dynamicFilter: null,
                ExpressList: [],
                total: 0,
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                addDialogFormVisible: false,
                editFormVisible: false, // 编辑界面是否显示
                editLoading: false,
                editFormRules: {
                    userName: [
                        { required: true, message: "请输入用户名", trigger: "blur" },
                    ],
                },
                userNameReadonly: true,
                // 编辑界面数据
                editForm: {},
                addFormVisible: false, // 新增界面是否显示
                addLoading: false,
                addFormRules: {
                    userName: [
                        { required: true, message: "请输入用户名", trigger: "blur" },
                    ],
                    password: [{ required: true, message: "请输入密码", trigger: "blur" }],
                },
                // 新增界面数据
                addForm: {},
                deleteLoading: false,
                importFilte: { companyid: null, prosimstate: null, warehouse: null, yearmonth: null , startDate: null , endDate: null },
                expresscompanylist: [],
                dialogVisible: false,
                dialogbatchNumberVisible: false,
                dialogdeletebatchNumberVisible: false,
                dialogCloneVisible: false,
                uploadLoading: false,
                fileList: [],
                prosimstatelist: [],//快递站点
                fileparm: {},
                cloneparm: {
                    oldBatchnumber: "",
                    newYearMonth: ""
                }
            };
        },
        async mounted () {
            await this.onSearch();
            await this.getExpressComanyList();
        },
        methods: {
            //获取状态信息
            async getprosimstatelist (val) {
                var id;
                if (val == 1)
                    id = this.importFilte.companyid
                else if (val == 2) {
                    id = this.filter.CompanyId
                    this.filter.prosimstate = null
                }

                var res = await getExpressComanyStationName({ id: id });
                if (res?.code) {
                    this.prosimstatelist = res.data
                }
            },
            formatCreatedTime (row, column, time) {
                return formatTime(time, "YYYY-MM-DD");
            },
            getRowClass ({ row, rowIndex }) {
                if (row.ruleDetails.length == 0) {
                    return 'row-expand-cover';
                } else {
                    return '';
                }
            },
            downmoban () {
                let url = "后台提供给你下载模板的接口";
                let blob = new Blob([url], {
                    type: "application/vnd.ms-excel",
                });
                let requestUrl = URL.createObjectURL(blob);
                let link = document.createElement("a");
                let fileName = "模板" + ".xlsx";
                document.body.appendChild(link);
                link.href = requestUrl;
                link.dowmload = fileName;
                link.click();
                link.remove();
            },
            startImport () {
                this.dialogVisible = true;
            },
            cancelImport () {
                this.dialogVisible = false;
            },
            beforeRemove () {
                return false;
            },
            //上传成功
            uploadSuccess (response, file, fileList) {
                if (response.code == 200) {
                    //状态码为200时则上传成功
                } else {
                    //状态码不是200时上传失败 从列表中删除
                    fileList.splice(fileList.indexOf(file), 1);
                }
            },
            uploadchange () {
                // this.$refs.upload.clearFiles();
            },
            submitUpload () {
                if (!this.importFilte.companyid) {
                    this.$message({ message: "请选择快递公司！", type: "warning", });
                    return;
                }
                if (!this.importFilte.prosimstate) {
                    this.$message({ message: "请选择快递公司站点！", type: "warning", });
                    return;
                }
                if (this.importFilte.warehouse == null) {
                    this.$message({ message: "请选择发货仓库！", type: "warning", });
                    return;
                }
                if (!this.importFilte.yearmonth) {
                    this.$message({ message: "请选择月份！", type: "warning", });
                    return;
                }
                if (!this.importFilte.startDate) {
                    this.$message({ message: "请选择起始日期！", type: "warning", });
                    return;
                }
                if (!this.importFilte.endDate) {
                    this.$message({ message: "请选择结束日期！", type: "warning", });
                    return;
                }
                this.uploadLoading = true;
                this.$refs.upload.submit();
            },
            async uploadFile (item) {
                const form = new FormData();
                form.append("token", this.token);
                form.append("yearmonth", this.importFilte.yearmonth);
                form.append("startDate", this.importFilte.startDate);
                form.append("endDate", this.importFilte.endDate);
                form.append("companyid", this.importFilte.companyid);
                form.append("prosimstate", this.importFilte.prosimstate);
                form.append("warehouse", this.importFilte.warehouse);
                form.append("upfile", item.file);
                const res = await importExpressRule(form);
                if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
                else this.$message({ message: res.msg, type: "warning" });
                this.uploadLoading = false;
            },
            startEnable () {
                this.dialogbatchNumberVisible = true;
            },
            startDelete () {
                this.dialogdeletebatchNumberVisible = true;
            },
            async onEnablebatchRule () {
                if (!this.batchNumber) {
                    this.$message({ message: "请输入批次号！", type: "warning" });
                    return;
                }
                const para = { Batchnumber: this.batchNumber };
                const res = await enablebatchRule(para);
                this.dialogbatchNumberVisible = false;
                if (!res?.success) {
                    return;
                }
                this.$message({ message: "已批量启用", type: "success" });
                this.getExpressList();
            },
            async onDeletebatchRule () {
                if (!this.deletebatchNumber) {
                    this.$message({ message: "请输入批次号！", type: "warning" });
                    return;
                }
                const para = { Batchnumber: this.deletebatchNumber };
                const res = await deleteBatchRule(para);
                this.dialogdeletebatchNumberVisible = false;
                if (!res?.success) return;
                this.$message({ message: "已批量删除", type: "success" });
                this.getExpressList();
            },
            async startClone () {
                this.dialogCloneVisible = true;
            },
            async onClone () {
                if (!this.cloneparm.oldBatchnumber) {
                    this.$message({ message: "请输入批次号！", type: "warning" });
                    return;
                }
                else if (!this.cloneparm.newYearMonth) {
                    this.$message({ message: "请输入年月！", type: "warning" });
                    return;
                }
                var params = {};
                const res = await cloneExpressRule(this.cloneparm);
                if (res?.success) {
                    this.$message({ message: "克隆成功！", type: "success" });
                    this.getExpressList();
                    this.dialogCloneVisible = false;
                }

                this.listLoading = false;
                if (!res?.success) {
                    return;
                }
            },
            async getExpressComanyList () {
                const res = await getExpressComanyAll({});
                if (!res?.success) {
                    return;
                }
                const data = res.data;
                this.expresscompanylist = data;
            },
            // 查询
            async onSearch (dynamicFilter) {
                this.$refs.pager.setPage(1);
                this.dynamicFilter = dynamicFilter;
                this.getExpressList();
            },
            async onExport () {
                const para = {};
                if (this.filter.Enabled == "true") para.enabled = true;
                else if (this.filter.Enabled == "false") para.enabled = false;
                para.warehouse = this.filter.Warehouse;
                para.province = this.filter.Province;
                para.companyId = this.filter.CompanyId;
                para.freightRuleType = this.filter.FreightRuleType;
                para.yearmonth = this.filter.yearmonth;
                console.log(this.filter);
                //debugger
                if (this.filter.timerange) {
                    para.startCreatedTime = this.filter.timerange[0];
                    para.endCreatedTime = this.filter.timerange[1];
                }
                const params = { ...para };
                var hasparm = false;
                for (let key of Object.keys(params)) {
                    if (params[key])
                        hasparm = true;
                }
                if (!hasparm) {
                    this.$message({ message: "请选择条件后导出！", type: "warning", });
                    return;
                }
                var res = await exportExpressRule(params);
                if (!res?.data) return
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '快递规则_' + new Date().toLocaleString() + '.xlsx')
                aLink.click()
            },
            async getExpressList () {
                const para = {};
                if (this.filter.Enabled == "true") para.enabled = true;
                else if (this.filter.Enabled == "false") para.enabled = false;
                para.warehouse = this.filter.Warehouse;
                para.prosimstate = this.filter.prosimstate;
                para.province = this.filter.Province;
                para.companyId = this.filter.CompanyId;
                para.freightRuleType = this.filter.FreightRuleType;
                para.yearmonth = this.filter.yearmonth;
                //debugger
                if (this.filter.timerange) {
                    para.startCreatedTime = this.filter.timerange[0];
                    para.endCreatedTime = this.filter.timerange[1];
                }
                var pager = this.$refs.pager.getPager();
                const params = { ...pager, ...para };
                this.listLoading = true;
                const res = await getPageExpressRuleList(params);
                this.listLoading = false;
                if (!res?.success) return;
                this.total = res.data.total;
                const data = res.data.list;
                data.forEach((d) => { d._loading = false; });
                this.ExpressList = data;
            },
            async startEdit (row) {
                row.edit = true;
                this.$nextTick(() => { //在数据加载完，重新渲染表格
                    this.$refs['elTableExpand'].doLayout();
                })
            },
            async cancelEdit (row) {
                row.edit = false;
                if (row.id === undefined) {
                    // 重新加载该页面
                }
                this.$nextTick(() => { //在数据加载完，重新渲染表格
                    this.$refs['elTableExpand'].doLayout();
                })
            },
            // 保存
            async confirmEdit (row) {
                row.edit = false;
                const res = await updateExpressRule(row);
                if (!res?.success) {
                    this.$nextTick(() => { //在数据加载完，重新渲染表格
                        this.$refs['elTableExpand'].doLayout();
                    })
                    return;
                }
                this.$message({
                    message: this.$t("admin.updateOk"),
                    type: "success",
                });
                if (row.id === undefined) {
                    // 重新加载该页面
                }
                this.$nextTick(() => { //在数据加载完，重新渲染表格
                    this.$refs['elTableExpand'].doLayout();
                })
            },
            // 显示新增界面
            async onAdd () {
                this.addFormVisible = true;
            },
            closeAddForm () {
                this.$refs.addForm.resetFields();
            },
            // 编辑验证
            editFormvalidate () {
                let isValid = false;
                this.$refs.editForm.validate((valid) => {
                    isValid = valid;
                });
                return isValid;
            },
            // 新增验证
            addFormvalidate () {
                let isValid = false;
                this.$refs.addForm.validate((valid) => {
                    isValid = valid;
                });
                return isValid;
            },
            // 新增
            async onAddSubmit () {
                this.addLoading = true;
                const para = _.cloneDeep(this.addForm);
                const res = await addExpressRule(para);
                this.addLoading = false;
                if (!res?.success) {
                    return;
                }
                this.$message({
                    message: this.$t("admin.addOk"),
                    type: "success",
                });
                this.$refs["addForm"].resetFields();
                this.addFormVisible = false;
                this.getExpressList();
            },
            // 删除验证
            deleteValidate (row) {
                let isValid = true;
                if (row && row.userName === "admin") {
                    this.$message({
                        message: row.nickName + "，禁止删除！",
                        type: "warning",
                    });
                    isValid = false;
                }
                return isValid;
            },
            // 删除
            async onDelete (row) {
                await this.$confirm("确定删除吗?, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(async () => {
                        const para = { id: row.id };
                        const res = await deleteExpressRule(row.id);
                        if (!res?.success) {
                            return;
                        }
                        this.$message({
                            message: this.$t("admin.deleteOk"),
                            type: "success",
                        });
                        this.getExpressList();
                    })
                    .catch(() => {
                        this.$message({
                            type: "info",
                            message: "已取消删除",
                        });
                    });
            },
            batchDeleteValidate () {
                let isValid = true;
                var row = this.sels && this.sels.find((s) => s.userName === "admin");
                if (row && row.userName === "admin") {
                    this.$message({
                        message: row.nickName + "，禁止删除！",
                        type: "warning",
                    });
                    isValid = false;
                }
                return isValid;
            },
            // 批量删除
            async onBatchDelete () {
                const para = { ids: [] };
                para.ids = this.sels.map((s) => {
                    return s.id;
                });

                this.deleteLoading = true;
                const res = await batchRemoveUser(para.ids);
                this.deleteLoading = false;

                if (!res?.success) {
                    return;
                }
                this.$message({
                    message: this.$t("admin.batchDeleteOk"),
                    type: "success",
                });

                this.getExpressList();
            },
            // 选择
            onSelsChange (sels) {
                this.sels = sels;
            },
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
