<template>
    <MyContainer>
        <div style="padding: 10px;">
            <el-form class="ad-form-query" :inline="true">
                <!-- <el-form-item label="">
                    <el-select ref="selectdepartmentId" v-model="filter.department" clearable style="width: 200px"
                        size="mini" placeholder="招聘部门" @clear="() => { filter.departmentId = null }">
                        <el-option hidden value="一级菜单" :label="filter.department"></el-option>
                        <el-tree style="width: 200px;" :data="deptList" :props="defaultProps" :expand-on-click-node="false"
                            :check-on-click-node="true" @node-click="handledeptNodeClick">
                        </el-tree>
                    </el-select>
                </el-form-item> -->
                <!-- <el-form-item label="">
                    <el-select ref="selectPosit" placeholder="请选择岗位" clearable v-model="filter.position" style="width: 200px" size="mini">
                        <el-option hidden value="一级菜单" :label="filter.position"></el-option>
                        <el-tree style="width: 200px;" :data="postList" :props="postProps" :expand-on-click-node="false"
                            :check-on-click-node="true" @node-click="handleNodePostClick">
                        </el-tree>
                    </el-select>
                </el-form-item> -->
                <el-form-item label="">
                    <el-input v-model.trim="filter.keywords" placeholder="输入姓名、模板名称或岗位名称" style="width:200px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.employeeStatus" placeholder="请选择员工状态" style="width: 120px" size="mini"
                        clearable>
                        <el-option label="正式" :value="3"></el-option>
                        <el-option label="试用" :value="2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">筛选</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onEditAll">批量修改员工薪资</el-button>
                </el-form-item>
            </el-form>
        </div>
        <el-row :gutter="10">
            <el-col :span="6" style="width:20%">
                <el-card>
                    <el-table max-height="690px" :indent="10" :show-header="false" :data="deptList" border
                        :default-expand-all="false" :tree-props="{ children: 'childDeptList', hasChildren: 'hasChildren' }"
                        row-key="dept_id" highlight-current-row style="width: 100%;" @row-click="deptSelect">
                        <el-table-column prop="name" />
                    </el-table>
                </el-card>
            </el-col>
            <el-col :span="18" style="height:700px;width: 80%;">
                <container>
                    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                        @select='selectchange' :isSelection='true' :isSelectColumn='true' :tableData='datalist'
                        :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='false' :summaryarry='summaryarry'
                        :loading="listLoading">
                    </ces-table>
                    <template #footer>
                        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
                    </template>
                </container>
            </el-col>
        </el-row>
        <!-- 薪资，修改 -->
        <el-dialog :title="isSolo?'修改员工薪资结构':'批量修改员工薪资结构'" :visible.sync="showFinishDialog" width="30%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag>
            <div style="height: 200px;margin-bottom:20px" v-if="!isSolo">
                <ces-table :height="'200'" ref="tableselect" rowkey="ddUserId" :that='that' :isIndex='true' :hasexpand='true'
                         :isSelection='false' :tableData='selectList'  :isSelectColumn='false'
                        :tableCols='selectCols' 
                       >
                    </ces-table>
            </div>
            <el-form class="ad-form-query" label-width="80px" :model="loseForm">
                <!-- <el-form-item label="薪资模板名称">
                    <el-input placeholder="名称" :disabled="loseForm.templateId != 0" v-model="loseForm.templateName"
                        maxlength="30">
                    </el-input>
                </el-form-item> -->
                <el-form-item label="部门"  v-if="isSolo">
                    <el-select  style="width: 100%" ref="selectDept" disabled v-model="loseForm.department" placeholder="部门"
                        size="mini">
                        <el-option hidden value="一级菜单" :label="loseForm.department"></el-option>
                        <el-tree style="width: 200px;" :data="deptList" :props="defaultProps" :expand-on-click-node="false"
                            :check-on-click-node="true" @node-click="handleNodeClick">
                        </el-tree>
                    </el-select>
                </el-form-item>
                <el-form-item label="岗位"  v-if="isSolo">
                    <el-select style="width: 100%" ref="selectPositForm" v-model="loseForm.position" placeholder="岗位"
                        disabled size="mini">
                        <el-option hidden value="一级菜单" :label="loseForm.position"></el-option>
                        <el-tree style="width: 200px;" :data="postList" :props="postProps" :expand-on-click-node="false"
                            :check-on-click-node="true" @node-click="handleNodePostClickForm">
                        </el-tree>
                    </el-select>
                </el-form-item>
                <el-form-item label="薪资模板">
                    <el-select style="width: 100%" v-model="loseForm.templateId" filterable placeholder="薪资模板" size="mini"
                        @change="changeTemple">
                        <el-option label="自定义" :value="0"></el-option>
                        <el-option v-for="item in templateList" :label="item.templateName"
                            :value="item.templateId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="基本工资">
                    <el-input-number placeholder="基本工资" :disabled="loseForm.templateId != 0" :controls="false" :min="1" :max="99999999" style="width: 100%" :precision="2"
                        v-model="loseForm.baseSalary">
                   </el-input-number>
                </el-form-item>
                <el-form-item label="岗位工资">
                    <el-input-number placeholder="岗位工资"  :controls="false" :min="0" :max="99999999" style="width: 100%" :precision="2"
                        v-model="loseForm.positionSalary">
                    </el-input-number>
                </el-form-item>
                <el-form-item label="绩效工资">
                    <el-input-number  placeholder="绩效工资" :controls="false" :min="0" :max="99999999" style="width: 100%" :precision="2"
                        v-model="loseForm.performanceSalary">
                   </el-input-number>
                </el-form-item>
                <el-form-item label="小时工资">
                    <el-input-number placeholder="小时工资" :controls="false" :min="0" :max="99999999" style="width: 100%" :precision="2"
                        v-model="loseForm.hourlyWage">
                   </el-input-number>
                </el-form-item>
                <el-form-item label="竞业补贴">
                    <el-select v-model="loseForm.competitionAllowanceType" :disabled="loseForm.templateId != 0"
                        placeholder="竞业补贴" size="mini" @change="oncompetitionAllowanceType" clearable style="width: 100%"
                        @clear="() => { loseForm.competitionAllowanceAmount = 0 }">
                        <el-option label="无" value="无"></el-option>
                        <el-option label="运营专员" value="运营专员"></el-option>
                        <el-option label="IT部" value="IT部"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="社保补贴">
                    <el-select v-model="loseForm.socialSecurityType" :disabled="loseForm.templateId != 0" placeholder="社保补贴"
                        size="mini" @change="onsocialSecurityType" clearable style="width: 100%"
                        @clear="() => { loseForm.socialSecurityAmount = 0 }">
                        <el-option label="毕业" value="毕业"></el-option>
                        <el-option label="实习生" value="实习生"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="保密费">
                    <el-input-number placeholder="保密费" :controls="false" :min="0" :max="99999999"  style="width: 100%" :precision="2"
                        v-model="loseForm.secrecyFee">
                    </el-input-number>
                </el-form-item>
                <el-form-item label="合计薪资">
                    <!-- <el-input-number placeholder="合计薪资" disabled :controls="false"  style="width: 100%" :precision="2"
                        v-model="totalSalary">
                    </el-input-number> -->
                    {{ totalSalary }}元
                </el-form-item>
                <!-- <el-form-item label="是否启用">
                    <el-radio-group v-model="loseForm.isEnabled" :disabled="loseForm.templateId != 0">
                        <el-radio :label="true">是</el-radio>
                        <el-radio :label="false">否</el-radio>
                    </el-radio-group>
                </el-form-item> -->
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showFinishDialog = false">取 消</el-button>
                    <el-button type="primary"  @click="loseSubmit" :loading="subLoad">保存</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 记录 -->
        <el-dialog title="记录" :visible.sync="showRecordTable" width="50%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag>
            <ces-table ref="recordtable" :that='that' :isIndex='false' :hasexpand='false' style="height: 500px;"
                :tableData='recordList' :tableCols='recordTableCols' :customRowStyle="customRowStyle"
                :selectColumnHeight="'0px'" :isBorder="false" :hasexpandRight="true">
                <!-- <template slot="right">
                    <el-table-column prop="chgContent" label="操作内容"></el-table-column>

                    <el-table-column prop="totalSalary" label="合计薪资"></el-table-column>
                </template> -->
            </ces-table>
        </el-dialog>
    </MyContainer>
</template>
<script>
 import MyContainer from "@/components/my-container";
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import { AllDDDeptTreeNcWh, getDingRolesTree } from '@/api/profit/personnel'
import {
    pageEmployeeSalary, getEmployeeSalary, pageSalaryTemplate, 
    getSalaryTemplate, editEmployeeSalary, deleteEmployeeSalary, 
    employeeSalaryChgLogs,ExportEmployeeSalary
} from '@/api/profit/hr'

const tableCols = [
    { istrue: true, prop: 'userName', label: '姓名', width: '100',sortable: 'custom', },
    { istrue: true, prop: 'position', label: '岗位名称', width: '160',sortable: 'custom', },
    { istrue: true, prop: 'department', label: '部门', width: '160',sortable: 'custom', },
    { istrue: true, prop: 'employeeStatus', label: '员工状态', width: '100', formatter: (row) => row.employeeStatus == null ?'':row.employeeStatus == 2 ? "试用" : "正式",sortable: 'custom', },
    { istrue: true, prop: 'templateName', label: '薪资模板',sortable: 'custom', },
    { istrue: true, prop: 'totalSalary', label: '合计薪资', width: '160', sortable: 'custom', tipmesg: '元' },
    {
        istrue: true, type: "button", label: '操作', width: "240",
        btnList: [
            { label: "记录", permission: "", handle: (that, row) => that.losePostion(row.ddUserId) },
            { label: "修改", permission: "", handle: (that, row) => that.editPostion(row) },
            { type: "danger", permission: "", label: "删除", handle: (that, row) => that.deletePostion(row.ddUserId) }
        ]
    }
];
const selectCols = [
    { istrue: true, prop: 'userName', label: '姓名', width: '100',},
    { istrue: true, prop: 'position', label: '岗位名称', width: '100',},
    { istrue: true, prop: 'department', label: '部门', width: '100', },
    { istrue: true, prop: 'templateName', label: '薪资模板', },
    { istrue: true, prop: 'totalSalary', label: '合计薪资', width: '100',  tipmesg: '元',},

]
const recordTableCols = [
    { istrue: true, prop: 'logTime', align: 'left', label: '操作时间', width: '160' },
    { istrue: true, prop: 'opUserName', align: 'left', label: '操作人', width: '100' },
    { istrue: true, prop: 'chgContent', align: 'left', label: '操作内容' },
    { istrue: true, prop: 'totalSalary', align: 'left', label: '合计薪资', width: '100' },
];
const tableHandles = []
export default {
    name: 'payStructure',//
    components: { container, cesTable,MyContainer },
    data () {
        return {
            isSolo: false,
            selectList: [],
            selectCols:selectCols,
            subLoad: false,
            deptList: [],
            defaultProps: {
                children: 'childDeptList',
                label: 'name'
            },
            postProps: {
                children: 'roles',
                label: 'name'
            },
            postList: [],
            templateList: [],
            that: this,
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "", IsAsc: false },
            summaryarry: {},
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            showFinishDialog: false,
            recordTableCols: recordTableCols,
            filter: {
                departmentId: null,//
                department: null,
                position: null,//
                templateName: null,//
                employeeStatus: null,
                keywords: null,
            },
            loseForm: {
                templateId: 0,
                templateName: null,
                departmentId: 0,
                department: null,
                position: null,
                baseSalary: 1850,
                positionSalary: 0,
                performanceSalary: 0,
                hourlyWage: 0,
                competitionAllowanceType: null,
                competitionAllowanceAmount: 0,
                socialSecurityType: null,
                socialSecurityAmount: 0,
                secrecyFee: 100,
                totalSalary: 0,
                isEnabled: null,
                ddUserId: null,
            },
            datalist: [
            ],
            recordList: [],
            showRecordTable: false,
        }
    },
    computed: {
        totalSalary () {
            return this.loseForm.baseSalary + this.loseForm.positionSalary +this.loseForm.performanceSalary
                + this.loseForm.hourlyWage + this.loseForm.competitionAllowanceAmount +
                this.loseForm.socialSecurityAmount + this.loseForm.secrecyFee;
        }
    },
    mounted () {
        this.getDeptList();
        this.getPositList();
        this.onSearch();
        this.getTempleList();
    },
    beforeUpdate () { },
    methods: {
        loseSubmit () {
            this.loseForm.totalSalary = this.totalSalary;
            if (this.loseForm.templateId == 0) {
                this.loseForm.templateName = '自定义';
            }
            this.subLoad = true;
            if (this.isSolo) {
                this.loseForm.ddUserIds = null;
            } else {
                this.loseForm.ddUserId = null;
                this.loseForm.ddUserIds = this.sels;
            }
            editEmployeeSalary(this.loseForm).then(res => {
                this.subLoad = false;
                if (res.success) {
                    this.$message({ message: '修改薪资模板成功', type: "success" })
                }
                this.onSearch()
                this.showFinishDialog = false;
            })
        },
        changeTemple (e) {
            if (e != 0) {
                getSalaryTemplate({ templateId: e }).then(res => {
                    if (res.success) {
                        for (const prop in res.data) {
                            if (prop in this.loseForm) {
                                this.loseForm[prop] = res.data[prop];
                            }
                        }
                    }
                })
            }
        },
        // 获取模板
        getTempleList (departmentId,position) {
            let params = {
                // departmentId: departmentId,
                // position:position,
                isEnable: true,
            }
            pageSalaryTemplate(params).then(res => {
                if (res.success) {
                    this.templateList = res.data.list
                }
            })
        },
        // 获取招聘岗位列表
        async getPositList (ddDeptId) {
            const params = {
                closeStatus: 0,//计划状态:-1删除、0进行中、1已完成
                ddDeptId: ddDeptId,//招聘部门
                currentPage: 1,
                pageSize: 50,
                positionName: null,//岗位名称
                recruiterIds: [],//招聘专员
                closeReason: null,//完成原因
            };
            const res = await getDingRolesTree();
            this.postList = res.data;
        },
        oncompetitionAllowanceType (e) {
            if (e) {
                if (e == '无') {
                    this.loseForm.competitionAllowanceAmount = 0;
                } else if (e == '运营专员') {
                    this.loseForm.competitionAllowanceAmount = 1000;
                } else if (e == 'IT部') {
                    this.loseForm.competitionAllowanceAmount = 2000;
                }
            }
        },
        onsocialSecurityType (e) {
            if (e) {
                if (e == '毕业') {
                    this.loseForm.socialSecurityAmount = 800;
                } else {
                    this.loseForm.socialSecurityAmount = 300;
                }
            }

        },
        // 节点点击事件
        handleNodeClick (data) {
            // 表单
            // 配置树形组件点击节点后，设置选择器的值，配置组件的数据
            this.loseForm.departmentId = data.dept_id;
            this.loseForm.department = data.name;
            // this.loseForm.position = null,
            // 选择器执行完成后，使其失去焦点隐藏下拉框效果
            this.$refs.selectDept.blur();
        },
        handledeptNodeClick (data) {
            // 筛选
            this.filter.department = data.name;
            this.filter.departmentId = data.dept_id;
            this.$refs.selectdepartmentId.blur();
        },
        handleNodePostClick (data) {
            this.filter.position = data.name;
            this.loseForm.position = data.name;
            this.$refs.selectPosit.blur();
        },
        handleNodePostClickForm () {
            this.loseForm.position = data.name;
            this.$refs.selectPositForm.blur();
        },
        deptSelect (row, column) {
            this.filter.departmentId = row.dept_id;
            this.onSearch()
        },

        // 获取部门列表
        async getDeptList () {
            await AllDDDeptTreeNcWh().then(res => {
                if (res.success) {
                    this.deptList = res.data.childDeptList;
                } else {
                    this.$message({ message: res.msg, type: "danger" });
                }
            })
        },
        losePostion (ddUserId) {
            this.showRecordTable = true;
            employeeSalaryChgLogs({ ddUserId: ddUserId }).then(res => {
                this.recordList = res.data;
            })
        },
        // 修改
        editPostion (row) {
            getEmployeeSalary({ ddUserId: row.ddUserId }).then(res => {
                if (res.success) {
                    this.loseForm = res.data;
                    this.loseForm.ddUserIds = null;
                    this.showFinishDialog = true;
                    this.isSolo = true;
                }
            })
        },
        //删除
        deletePostion (ddUserId) {
            this.$confirm('是否确认删除该条员工薪资?', '删除员工薪资', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await deleteEmployeeSalary({ ddUserId: ddUserId })
                if (!res?.success) { return }
                this.$message({ type: 'success', message: '删除成功!' });
                this.onSearch()
            }).catch(() => {
                this.$message({ type: 'info', message: '已取消删除' });
            });
        },
        async onExport () {           
            var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
            const params = { ... this.filter }

            if (params === false) {
                return;
            }
            var res = await ExportEmployeeSalary(params);//导出
            loadingInstance.close();
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '员工薪资结构_' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        onSearch () {
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        async getlist () {
            var pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... { ...this.filter } }
            this.listLoading = true
            const res = await pageEmployeeSalary(params);
            this.listLoading = false
            if (!res?.success) return
            this.total = res?.data?.total;
            this.datalist = res.data.list;
            this.summaryarry = res.data?.summary;
        },
        sortchange (column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = [];
            this.selectList = rows;
            rows.forEach(f => {
                this.sels.push(f.ddUserId);
            })
            
        },
        // 批量修改
        onEditAll () {
            if (this.sels.length == 0) {
                this.$message({ type: 'warning', message: "请选择至少一个员工" });
                return;
            }
            this.loseForm = {
                templateId: 0,
                templateName: null,
                departmentId: 0,
                department: null,
                position: null,
                baseSalary: 1850,
                positionSalary: 0,
                performanceSalary: 0,
                hourlyWage: 0,
                competitionAllowanceType: null,
                competitionAllowanceAmount: 0,
                socialSecurityType: null,
                socialSecurityAmount: 0,
                secrecyFee: 100,
                totalSalary: 0,
                isEnabled: null,
                ddUserIds: this.sels,
            }
            this.isSolo = false;
            this.showFinishDialog = true;
        },
        customRowStyle (row, index) {
            if (row.row?.isend && row.row.isend == 1) {
                let styleJson = {};
                styleJson.color = "rgb(216 216 216)";
                return styleJson
            } else {
                return null
            }

        },
    }
}
</script>
<style scoped lang="scss" >
::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: rgb(233, 233, 248);
    border-radius: 3px;
}
::v-deep .el-input-number.is-without-controls .el-input__inner {
    padding-left: 5px;
    padding-right: 2px;
    text-align: left;
}
::v-deep .el-table__body-wrapper {
    overflow: auto;
}</style>
  