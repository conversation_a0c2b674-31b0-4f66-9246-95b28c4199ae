<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form  class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent ></el-form>
    </template>
    <!--列表-->
    <ces-table      ref="table"
      :that="that"
      :isIndex="true"
      :hasexpand="false"
      @sortchange="sortchange"
      :tableData="pddcontributeinfolist"
      @select="selectchange"
      :isSelection="false"
      :tableCols="tableCols"
      :loading="listLoading"
    >
      <el-table-column type="expand">
        <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column
                v-for="col in props.row.detailcols"
                :prop="col.prop"
                :label="col.label"
                :key="col"
              >
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <template slot="extentbtn">
        <el-button-group>
          <el-button style="padding: 0; margin: 0">
            <el-input
              v-model="Filter.Shopname"
              placeholder="店铺名"
              style="width: 120px"
            />
          </el-button>
          <el-button style="padding: 0; margin: 0">
            <el-input
              v-model="Filter.Title"
              placeholder="视频标题"
              style="width: 120px"
            />
          </el-button>
          <el-button style="padding: 0; margin: 0">
            <el-input
              v-model="Filter.Procode"
              placeholder="产品ID"
              style="width: 120px"
            />
          </el-button>
          <el-button style="padding: 0; margin: 0">
            <datepicker v-model="Filter.UploadDate"></datepicker>
          </el-button>
          <el-button style="padding: 0; margin: 0">
            <el-input
              v-model="Filter.Sname"
              placeholder="投稿人"
              style="width: 120px"
            />
          </el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onImportSyj">导入</el-button>
        </el-button-group>
      </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getpddcontributeinfoList"
      />
    </template>
    <el-dialog
      title="多多投稿记录"
      :visible.sync="dialogVisibleSyj"
      width="30%"
    >
      <span>
        <el-upload
          ref="upload2"
          class="upload-demo"
          :auto-upload="false"
          :multiple="false"
          :limit="1"
          action
          accept=".xlsx"
          :http-request="uploadFile2"
          :on-success="uploadSuccess2"
        >
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <my-confirm-button
            style="margin-left: 10px"
            size="small"
            type="success"
            @click="onSubmitupload2"
            >上传</my-confirm-button
          >
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyj = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>
import datepicker from "@/views/operatemanage/newmedia/datepicker";
import {
  importPddcontributeinfoAsync,
  getPddcontributeinfoList,
  deletePddcontributeinfoBatch,
} from "@/api/operatemanage/newmedia/pddcontributeinfo";
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
const tableCols = [
  {
    istrue: true,
    prop: "shopname",
    label: "店铺名",
    width: "200",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "title",
    label: "视频标题",
    width: "200",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "procode",
    label: "产品ID",
    width: "200",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "uploadDate",
    label: "投稿日期",
    width: "200",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "sname",
    label: "投稿人",
    width: "200",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "createdTime",
    label: "导入时间",
    width: "200",
    sortable: "custom",
  },
   
  
];
export default {
  name: "Users",
  components: {
    MyContainer,
    MyConfirmButton,
    MySearch,
    MySearchWindow,
    cesTable,
    datepicker,
  },
  data() {
    return {
      that: this,
      Filter: {},
      shopList: [],
      userList: [],
      groupList: [],
      pddcontributeinfolist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: { count_sum: 10 },
      pager: { OrderBy: "id", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      //
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
    };
  },
  async mounted() {},
  methods: {
    async deleteBatch(row) {
      var that = this;
      this.$confirm("此操作将删除此批次多多投稿记录数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        await deletePddcontributeinfoBatch({ batchNumber: row.batchNumber });
        that.$message({ message: "已删除", type: "success" });
        that.onRefresh();
      });
    },
    sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      this.onSearch();
    },
    onImportSyj() {
      this.dialogVisibleSyj = true;
    },
    async uploadFile2(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      const res = importPddcontributeinfoAsync(form);
      this.$message({ message: "上传成功,正在导入中...", type: "success" });
    },
    async uploadSuccess2(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
    },
    async onSubmitupload2() {
      this.$refs.upload2.submit();
    },
    onRefresh() {
      this.onSearch();
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getpddcontributeinfoList();
    },
    async getpddcontributeinfoList() {
      if (this.Filter.UseDate) {
        this.Filter.startAccountDate = this.Filter.UseDate[0];
        this.Filter.endAccountDate = this.Filter.UseDate[1];
      }
      if (this.Filter.UploadDate) {
        this.Filter.startUploadDate = this.Filter.UploadDate[0];
        this.Filter.endUploadDate = this.Filter.UploadDate[1];
      }
      const para = { ...this.Filter };
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };

      console.log(para);

      this.listLoading = true;
      const res = await getPddcontributeinfoList(params);
      console.log(res);
      this.listLoading = false;
      console.log(res.data.list);
      //console.log(res.data.summary)

      this.total = res.data.total;
      this.pddcontributeinfolist = res.data.list;
      //this.summaryarry=res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach((f) => {
        this.selids.push(f.id);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
