<template>
    <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
        <el-scrollbar style="height: 100%">
            <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="160px" class="demo-ruleForm">
                <el-form-item label="区域：" prop="regionName" :rules="[{ required: true, message: '区域不能为空', trigger: 'blur' }]">
                    <el-select v-model="ruleForm.regionName" placeholder="区域" class="publicCss" collapse-tags>
                        <el-option v-for="item in regionNameList" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item label="数据库：" prop="databaseName" :rules="[{ required: true, message: '数据库不能为空', trigger: 'blur' }]">
                    <el-input style="width:80%;"
                     show-word-limit
                    :rows="4" v-model.trim="ruleForm.databaseName"
                    :maxlength="50" placeholder="数据库" clearable />
                </el-form-item>
                <el-form-item label="表名：" prop="tableName" :rules="[{ required: true, message: '表名不能为空', trigger: 'blur' }]">
                    <el-input style="width:80%;"
                     show-word-limit
                    :rows="4" v-model.trim="ruleForm.tableName"
                    :maxlength="50" placeholder="表名" clearable />
                </el-form-item>
                <el-form-item label="表中文名：" prop="tableZhName">
                    <el-input style="width:80%;"
                     show-word-limit
                    :rows="4" v-model.trim="ruleForm.tableZhName"
                    :maxlength="50" placeholder="表名" clearable />
                </el-form-item>
                <el-form-item label="字段名：" prop="fieldName" :rules="[{ required: true, message: '字段名不能为空', trigger: 'blur' }]">
                    <el-input style="width:80%;"
                     show-word-limit
                    :rows="4" v-model.trim="ruleForm.fieldName"
                    :maxlength="50" placeholder="字段名" clearable />
                </el-form-item>
                <el-form-item label="字段类型：" prop="fieldType" :rules="[{ required: true, message: '字段类型不能为空', trigger: 'blur' }]">
                    <el-input style="width:80%;"
                     show-word-limit
                    :rows="4" v-model.trim="ruleForm.fieldType"
                    :maxlength="50" placeholder="字段类型" clearable />
                </el-form-item>
                <el-form-item label="语义标签：" prop="semanticTags" :rules="[{ required: true, message: '语义标签不能为空', trigger: 'blur' }]">
                    <el-input style="width:80%;"
                     show-word-limit
                    :rows="4" v-model.trim="ruleForm.semanticTags"
                    :maxlength="100" placeholder="语义标签" clearable />
                </el-form-item>
                <el-form-item label="备注：" prop="remark">
                    <el-input style="width:80%;"
                     type="textarea"
                     show-word-limit
                    :autosize="{ minRows: 4, maxRows: 11 }"
                    :rows="4" v-model.trim="ruleForm.remark"
                    placeholder="备注" clearable />
                </el-form-item>


            </el-form>
        </el-scrollbar>
        <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
            <el-button @click="cancellationMethod">取消</el-button>
            <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
        </div>
    </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { aiTableFieldConfigSubmit } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission'
export default {
    name: 'departmentEdit',
    components: {
        inputNumberYh, MyConfirmButton
    },
    props: {
        editInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
        districtList: {
            type: Object,
            default: () => {
                return {}
            }
        },
        typeList: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data() {
        return {
            sectionList1: ['local','online'],
            sectionList2:['Ollama','Deepseek','OpenAI','硅基流动','文心一言','阿里云','阿里千问','Gemini' ],
            regionNameList: ['南昌','深圳', '武汉'],
            selectProfitrates: [],
            ruleForm: {
                label: '',
                name: ''
            },
            rules: {
                attendanceFigures: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                totalAmount: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                foreignObjects: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                totalOrders: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
            }
        }
    },

    async mounted() {
        this.$nextTick(() => {
            this.$refs.refruleForm.clearValidate();
        });
        this.ruleForm = { ...this.editInfo };
    },
    methods: {
        cancellationMethod() {
            this.$emit('cancellationMethod');
        },
        submitForm(formName) {
            console.log(this.ruleForm.label, 'this.ruleForm.label');
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing")
                    const { data, success } = await aiTableFieldConfigSubmit(this.ruleForm)
                    if (!success) {
                        return
                    }
                    this.$message.success('操作成功')
                    await this.$emit("search");

                } else {
                    console.log('error submit!!');
                    this.$message.error('操作失败')
                    return false;
                }
            });
            //   this.$confirm('是否保存?', '提示', {
            //     confirmButtonText: '确定',
            //     cancelButtonText: '取消',
            //     type: 'warning'
            //   }).then(async () => {
            //     this.$refs[formName].validate(async(valid) => {
            //       if (valid) {
            //         const { data, success } = await aiTableFieldConfigSubmit(this.ruleForm)
            //         if(!success){
            //             return
            //         }
            //         await this.$emit("search");

            //       } else {
            //         console.log('error submit!!');
            //         return false;
            //       }
            //     });
            //   }).catch(() => {
            //   });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
    }
}
</script>
<style scoped lang="scss">
.publicCss {
    width: 80%;
}
</style>
