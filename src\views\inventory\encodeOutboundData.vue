<template>
  <MyContainer style="height: 98%;" v-loading="loading">
    <template #header>
      <div class="top">
        <div style="display: flex; flex-wrap: wrap;">
          <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
            style="width: 250px;margin-right: 5px;" :clearable="false" :value-format="'yyyy-MM-dd'"
            @change="changeTime">
          </el-date-picker>
          <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="100" clearable class="publicCss" />
          <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="100" clearable class="publicCss" />
          <el-select v-model="ListInfo.changeType" placeholder="业务类型" class="publicCss" clearable>
            <el-option :key="'调拨出'" label="调拨出" :value=1 />
            <el-option :key="'盘点'" label="盘点" :value=2 />
            <el-option :key="'其它出库'" label="其它出库" :value=3 />
            <el-option :key="'加工出仓'" label="加工出仓" :value=4 />
          </el-select>
          <el-select v-model="ListInfo.wmsCoId" placeholder="仓储方" class="publicCss" filterable clearable>
            <el-option v-for="item in storagelist" :key="item.id" :label="item.name" :value="item.wms_co_id" />
          </el-select>
          <el-select v-model="ListInfo.brandId" placeholder="采购员" class="publicCss" filterable clearable>
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <div style="border: 1px solid #ccc; border-radius: 5px; display: flex; margin-right: 10px;width:250px">
            <el-input v-model.number="ListInfo.startChangeCount" placeholder="开始库存增减数" maxlength="10" clearable
              type="number"
              oninput="value=value.replace(/[^\d]/g,''); if(value.replace('-', '').length > 10) value = value.slice(0, 10)"
              class="pass_input" style="width: 125px;" />
            <span style="margin: 0 0px;">-</span>
            <el-input v-model.number="ListInfo.endTimeChangeCount" placeholder="结束库存增减数" maxlength="10" clearable
              type="number"
              oninput="value=value.replace(/[^\d]/g,''); if(value.replace('-', '').length > 10) value = value.slice(0, 10)"
              class="pass_input" style="width: 125px;" />
          </div>
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button @click="onReset">重置</el-button>
        </div>
        <div style="margin-right: 15px;">
          <el-button type="primary" @click="onWarehouseSetup"
            v-if="checkPermission('preWarningWareSetting:preWarningWareHouseList')">预警仓库设置</el-button>
          <el-button type="primary" @click="onNotificationSetting"
            v-if="checkPermission('businessSetting:businessSettingList')">库存预警通知设置</el-button>
        </div>
      </div>
    </template>
    <vxetablebase :id="'encodeOutboundData202408041536'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' style="width: 100%;margin: 0"
      :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="预警仓库管理" :visible.sync="stashdialogVisible" width="20%" v-dialogDrag>
      <div class="stashcontainer">
        <el-form ref="ruleForm" :model="ruleForm" label-width="80px" v-loading="stashloading">
          <el-form-item label="添加仓库">
            <el-select style="margin-left: 5px;width:150px" v-model="ruleForm.wmsCoId" clearable :collapse-tags="true"
              placeholder="请选择仓库" filterable>
              <el-option v-for="item in storagelist" :key="item.id" :label="item.name" :value="item.wms_co_id" />
            </el-select>
            <el-button type="primary" icon="el-icon-plus" @click="onAddwarehouse" style="margin-left: 5px;"></el-button>
          </el-form-item>
          <div class="stashselect-label">当前选择仓库: </div>
          <div class="stashscrollable-div">
            <div v-for="(item, index) in warehouseData" :key="index" class="stashitem">
              <span class="stashitem-text">{{ item.wmsCoName }}</span>
              <i class="el-icon-remove-outline stashremove-icon" @click="removeProps(item)" />
            </div>
          </div>
        </el-form>
      </div>
    </el-dialog>

    <el-dialog title="库存预警通知设置" :visible.sync="notificationdialogVisible" width="25%" v-dialogDrag>
      <div class="nocontainer">
        <div class="noitem">
          <el-checkbox v-model="notification.inventoryIncrease">库存增加数超</el-checkbox>
          <el-input v-model.trim="notification.additions" placeholder="请输入" maxlength="5" clearable type="number"
            class="noinput"
            oninput="value=value.replace(/[^\d]/g,''); if(value.replace('-', '').length > 5) value = value.slice(0, 5)" />
          <span>通知</span>
        </div>
        <div class="noitem">
          <el-checkbox v-model="notification.averageDaily">日均修正量超</el-checkbox>
          <el-input v-model.trim="notification.correction" placeholder="请输入" maxlength="5" clearable type="number"
            class="noinput"
            oninput="value=value.replace(/[^\d]/g,''); if(value.replace('-', '').length > 5) value = value.slice(0, 5)" />
          <span>天预警通知</span>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="notificationdialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="onSetupConfirmation">
          {{ (saveLoading ? '保存中' : '确 认') }}</el-button>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { selectPage } from '@/api/bladegateway/goodsChangeWarningLog.js';
import { businessSettingList, businessSettingSubmit } from '@/api/bladegateway/businessSetting.js';
import { preWarningWareHouseList, preWarningWareHouseRemove, preWarningWareHouseSave } from '@/api/bladegateway/preWarningWareSetting.js';
import { getAllWarehouse } from '@/api/inventory/warehouse'
import { getPurchaseNewPlanTurnDayBrandList } from '@/api/inventory/purchaseordernew'

const tableCols = [
  { istrue: true, prop: 'goodsCode', label: '商品编码', width: 'auto', align: 'center', },
  { istrue: true, prop: 'goodsName', label: '商品名称', width: 'auto', align: 'center', },
  { istrue: true, prop: 'changeType', label: '业务类型', width: 'auto', align: 'center', formatter: (row) => row.changeType == 1 ? "调拨出" : row.changeType == 2 ? "盘点" : row.changeType == 3 ? "其它出库" : row.changeType == 4 ? "加工出仓" : '', },
  { istrue: true, prop: 'changeTime', label: '日期', width: 'auto', align: 'center', sortable: 'custom', },
  { istrue: true, prop: 'changeCount', label: '库存增减数', width: 'auto', align: 'center', sortable: 'custom', formatter: (row) => row.changeCount == null || row.changeCount == undefined ? '' : row.changeCount },
  { istrue: true, prop: 'wmsCoName', label: '仓储方', width: 'auto', align: 'center', },
  { istrue: true, prop: 'ioId', label: '单号', width: 'auto', align: 'center', },
  { istrue: true, prop: 'brandName', label: '采购员', width: 'auto', align: 'center', },
]
export default {
  name: "encodeOutboundData",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      saveLoading: false,//保存按钮loading
      settingsList: [],//设置列表
      storagelist: [],//仓库列表
      stashloading: false,//添加仓库loading
      notification: {
        inventoryIncrease: false,//库存增加数超
        averageDaily: false,//日均修正量超
        additions: null,//库存增加数值
        correction: null,//日均修正量
      },
      notificationdialogVisible: false,//库存预警通知设置dialog
      ruleForm: {
        wmsCoId: null,//仓库id
      },
      warehouseData: [],//预警仓库列表
      stashdialogVisible: false,//预警仓库dialog
      brandlist: [],//采购员列表
      summaryarry: {},//汇总
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'changeTime',
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        goodsCode: null,//商品编码
        goodsName: null,//商品名称
        changeType: null,//业务类型
        wmsCoId: null,//仓储方
        brandId: null,//采购员
        endTimeChangeCount: null,//结束计数值
        startChangeCount: null,//开始计数值
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.init()
    await this.getList()
  },
  methods: {
    handleInput(value) {
      const intValue = Math.floor(value);
      if (intValue < 0 || intValue !== parseFloat(value)) {
        this.notification.correction = '';
      }
    },
    //初始化
    async init() {
      //采购
      var { data } = await getPurchaseNewPlanTurnDayBrandList();
      this.brandlist = data.map(item => { return { value: item.id, label: item.brandName }; });
      //仓库
      const res = await getAllWarehouse();
      if (!res?.success) {
        return
      }
      this.storagelist = res.data;
    },
    //库存预警通知设置
    async onSetupConfirmation() {
      if (this.notification.inventoryIncrease == true && (this.notification.additions == null || this.notification.additions == '')) {
        this.$message({ type: 'warning', message: '请输入库存增加数值!' });
        return
      }
      if (this.notification.averageDaily == true && (this.notification.correction == null || this.notification.correction == '')) {
        this.$message({ type: 'warning', message: '请输入日均修正量!' });
        return
      }
      if (this.notification.correction == 0) {
        this.$message({ type: 'warning', message: '日均修正量不能为0' });
        return
      }
      let list = [];
      this.settingsList.forEach(item => {
        if (item.subType == 0) {
          list.push({
            id: item.id,
            settingValue: this.notification.additions
          });
        } else if (item.subType == 1) {
          list.push({
            id: item.id,
            settingValue: this.notification.correction
          });
        } else if (item.subType == 2) {
          list.push({
            id: item.id,
            settingValue: this.notification.inventoryIncrease ? 1 : 0
          });
        } else if (item.subType == 3) {
          list.push({
            id: item.id,
            settingValue: this.notification.averageDaily ? 1 : 0
          });
        }
      });
      this.saveLoading = true
      const { success } = await businessSettingSubmit(list)
      if (success) {
        this.$message({ type: 'success', message: '设置成功!' });
        this.notificationdialogVisible = false
        this.saveLoading = false
      }
    },
    //添加仓库
    async onAddwarehouse() {
      if (this.ruleForm.wmsCoId == null || this.ruleForm.wmsCoId == '') {
        this.$message({ type: 'warning', message: '请选择仓库!' });
        return
      }
      this.stashloading = true
      const { data, success } = await preWarningWareHouseSave({ wmsCoId: this.ruleForm.wmsCoId })
      if (success) {
        this.onWarehouseSetup()
        this.$message({ type: 'success', message: '添加成功!' });
        this.ruleForm.wmsCoId = ''
      }
    },
    //库存预警通知设置
    async onNotificationSetting() {
      this.loading = true
      const { data, success } = await businessSettingList()
      if (success) {
        this.settingsList = data
        data.forEach(item => {
          if (item.subType == 0) {
            this.notification.additions = item.settingValue;
          } else if (item.subType == 1) {
            this.notification.correction = item.settingValue;
          } else if (item.subType == 2) {
            if (item.settingValue == 0) {
              this.notification.inventoryIncrease = false
            } else if (item.settingValue == 1) {
              this.notification.inventoryIncrease = true
            }
          } else if (item.subType == 3) {
            if (item.settingValue == 0) {
              this.notification.averageDaily = false
            } else if (item.settingValue == 1) {
              this.notification.averageDaily = true
            }
          }
        });
      }
      this.notificationdialogVisible = true
      this.loading = false
    },
    //删除
    async removeProps(item) {
      this.$confirm('此操作将执行删除操作, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const params = { id: item.id }
        const { data, success } = await preWarningWareHouseRemove(params)
        if (success) {
          this.$message({ type: 'success', message: '删除成功!' });
          this.onWarehouseSetup()
        }
      }).catch(() => {
      });
    },
    //预警仓库设置
    async onWarehouseSetup() {
      this.stashloading = true
      this.loading = true
      const { data, success } = await preWarningWareHouseList()
      if (success) {
        this.warehouseData = data
        this.stashdialogVisible = true
        this.ruleForm.wmsCoId = ''
        this.stashloading = false
        this.loading = false
      }
    },
    //重置
    onReset() {
      this.ListInfo.goodsCode = null//商品编码
      this.ListInfo.goodsName = null//商品名称
      this.ListInfo.changeType = null//业务类型
      this.ListInfo.wmsCoId = null//仓储方
      this.ListInfo.brandId = null//采购员
      this.ListInfo.endTimeChangeCount = null//结束计数值
      this.ListInfo.startChangeCount = null//开始计数值
    },
    //时间改变
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    //获取列表
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges != null && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      const { data, success } = await selectPage(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    //排序
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
  justify-content: space-between;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

.nocontainer {
  display: flex;
  flex-direction: column;
  margin-top: 10px;
}

.noitem {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.noinput {
  width: 80px;
  margin: 0 10px 0 10px;
}

.stashcontainer {
  height: 300px;
}

.stashselect-label {
  margin-left: 10px;
}

.stashscrollable-div {
  height: 220px;
  overflow: auto;
  padding-top: 5px;
}

.stashitem {
  margin-left: 10px;
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.stashitem-text {
  font-size: 15px;
  font-weight: bold;
}

.stashremove-icon {
  color: red;
  margin-left: 10px;
  cursor: pointer;
  font-size: 15px;
}

::v-deep .noinput input::-webkit-outer-spin-button,
::v-deep .noinput input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

::v-deep .noinput input[type="number"] {
  appearance: textfield;
  -moz-appearance: textfield;
}

::v-deep .pass_input input::-webkit-outer-spin-button,
::v-deep .pass_input input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

::v-deep .pass_input input[type="number"] {
  appearance: textfield;
  -moz-appearance: textfield;
}

//去掉input的边框
.pass_input {
  float: left;
  width: 215px;
  margin-left: 10px;

  // el-input__inner是el-input的input类名
  ::v-deep .el-input__inner {
    border: none;
  }
}
</style>
