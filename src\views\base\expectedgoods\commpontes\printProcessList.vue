<template>
    <MyContainer>
        <el-table :data="tableData" style="width: 100%" :height="500" max-height="600">
            <el-table-column prop="combineCode" label="组合编码" width="180">
            </el-table-column>
            <el-table-column prop="skus" label="skus" width="180">
            </el-table-column>
            <el-table-column prop="goodsInvBoxCount" label="子箱库存">
            </el-table-column>
            <el-table-column prop="inventoryDay" label="周转天数">
            </el-table-column>
            <el-table-column prop="quantity" label="加工数量">
                <template #default="{ row }">
                    <div>
                        <el-input-number v-model="row.quantity" :max="9999" label="加工数量" :controls="false"
                            :precision="0" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="操作">
                <template #default="{ row }">
                    <div>
                        <el-button type="text" @click="batchCopy(row.quantity)">批量</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <template #footer>
            <div class="btngroup">
                <el-button type="primary" @click="submit(true)">保存并打印</el-button>
                <el-button type="primary" @click="submit(false)">保存</el-button>
                <el-button @click="colse">取消</el-button>
            </div>
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { saveManifests } from '@/api/inventory/prepack'
export default {
    name: 'printProcessList',
    components: { MyContainer },
    props: {
        printData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            tableData: []
        }
    },
    mounted() {
        this.tableData = this.printData
    },
    methods: {
        batchCopy(quantity) {
            if (!quantity) return this.$message.error('加工数量不能为空或0')
            if (quantity < 0) return this.$message.error('加工数量不能小于0')
            this.$confirm('此操作将覆盖其他行当前列加工数量, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.tableData.forEach((item) => {
                    item.quantity = quantity
                })
                this.$message({
                    type: 'success',
                    message: '操作成功!'
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });

        },
        colse() {
            this.$emit('close')
        },
        async submit(isPrint) {
            this.tableData.forEach((item, i) => {
                if (!item.quantity) {
                    this.$message.error(`第${i + 1}行加工数量不能为空或0`)
                    throw new Error(`第${i + 1}行加工数量不能为空或0`)
                }
                if (item.quantity < 0) {
                    this.$message.error(`第${i + 1}行加工数量不能小于0`)
                    throw new Error(`第${i + 1}行加工数量不能小于0`)
                }
            })
            const { success } = await saveManifests(this.tableData)
            if (success) {
                this.$message.success('保存成功')
                if (isPrint) {
                    this.$emit('close', this.tableData[0].batchNo)
                }
                this.$emit('close')
            }

        }

    }
}
</script>

<style scoped lang="scss">
.btngroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;

    .el-button {
        margin-left: 10px;
    }
}

table,
th,
tr,
td {
    border: 1px solid #000;
    border-collapse: collapse;
}

td {
    line-height: 25px;
    padding-left: 5px;
    padding-right: 5px;
    width: auto;
    height: 50px;
    line-height: 50px;
    table-layout: fixed !important;
}

table {
    width: 100%;
}
</style>