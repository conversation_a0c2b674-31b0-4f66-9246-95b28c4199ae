<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true'
              :hasexpand='false' @sortchange='sortchange' :tableData='dahuixionglist'
              @select='selectchange' :isSelection='false' :showsummary='true'  :summaryarry='summaryarry'
         :tableCols='tableCols' :loading="listLoading">
      <el-table-column type="expand">
        <template slot-scope="props">
        <div>
          <el-table :data="props.row.detaildata" style="width: 100%">
            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
            </el-table-column>
          </el-table>
        </div>
      </template>
      </el-table-column>
       <template slot='extentbtn'>
          <el-button-group>
                           <el-button style="padding: 0;margin: 0;">
                                <el-input v-model.trim="Filter.ProCode" clearable maxlength="30" placeholder="ID" style="width:120px;"/>
                            </el-button>  
                            <el-button style="padding: 0;margin: 0;">
                               <el-date-picker style="width:280px" v-model="Filter.UseDate" type="monthrange" format="yyyy-MM " value-format="yyyy-MM "
                                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" ></el-date-picker>
                            </el-button>
                            <el-button style="padding: 0;margin: 0;">
                              <el-select filterable v-model="Filter.platform" placeholder="请选择平台" @change="onchangeplatform" clearable style="width: 130px">
                                <el-option label="淘系" value='1'></el-option>
                                <el-option label="拼多多" value="2"></el-option>
                                <el-option label="阿里巴巴" value="4"></el-option>
                                <el-option label="抖音" value="6"></el-option>
                                <el-option label="京东" value="7"></el-option>
                                <el-option label="淘工厂" value="8"></el-option>
                                <el-option label="天猫" value="9"></el-option>
                                <el-option label="苏宁" value="10"></el-option>
                                </el-select>
                            </el-button>
                            <el-button style="padding: 0;margin: 0;">
                              <el-select filterable v-model="Filter.shopCode" placeholder="请选择店铺" clearable style="width: 130px">
                                  <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"/>
                                </el-select>
                            </el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="onImportSyj">导入</el-button>
            <el-button type="primary" @click="DownLoadImportSyj">下载导入模板</el-button>
          </el-button-group>
        </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getJdExpressList"
      />
    </template>

    <el-dialog title="导入毛三和净利润" :visible.sync="dialogVisibleSyj" width="30%" v-dialogDrag>
      <span>
          <el-upload ref="upload2" class="upload-demo"
                  :auto-upload="false"
                  :multiple="false"
                  :limit="1"
                  action
                  accept=".xlsx"
                  :http-request="uploadFile2"
                  :on-change="uploadChange" :on-remove="uploadRemove">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <el-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">上传</el-button>
          </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyj = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>

import {importGrossThirdAndProfit,getGrossThirdAndProfitList } from '@/api/financial/yyfy'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import {platformlist} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'time',label:'年月', width:'100',sortable:'custom'},
      {istrue:true,prop:'platformName',label:'平台', width:'150',sortable:'custom'},
      {istrue:true,prop:'store',label:'店铺', width:'150',sortable:'custom'},
      {istrue:true,prop:'proCode',label:'ID', width:'150',sortable:'custom'},
      {istrue:true,prop:'netProfit',label:'净利', width:'200',sortable:'custom'},
      {istrue:true,prop:'grossProfit',label:'毛三', width:'200',sortable:'custom'},
     ];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
  data() {
    return {
      that:this,
      Filter: {
          platform:'',
          shopCode:'',
          UseDate:[],
        UseDstartAccountDateate:'',
        endAccountDate:'',
      },
      shopList:[],
      userList:[],
      groupList:[],
      dahuixionglist: [],
      tableCols:tableCols,
      total: 0,
      summaryarry:{},
      pager:{OrderBy:"id",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      //
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
      platform:0,
      yearMonth:"",
      platformlist:platformlist,
    };
  },
  async mounted() {
    this.onSearch();
  },
  methods: {
    DownLoadImportSyj() {
                window.open("/static/excel/financial/毛三和净利润模板.xlsx", "_blank");
            },
  async onchangeplatform(val){
    if(val==null||val=='')
    {
      this.Filter.shopCode = "";
    }
    const res1 = await getshopList({platform:val,CurrentPage:1,PageSize:300});
    this.shopList=res1.data.list
  },
    setplatform(platform){
this.platform=platform;
},
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    onImportSyj(){
      this.dialogVisibleSyj = true
    },
    async onSubmitupload2() {
      if (!this.fileList || this.fileList.length == 0) {
      this.$message({ message: "请先选取文件", type: "warning" });
      return false;
    }
      this.$refs.upload2.submit()
    },
    async uploadFile2(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      const res = importGrossThirdAndProfit(form);
      this.$message({message: '上传成功,正在导入中...', type: "success"});
      this.fileList = []
      this.$refs.upload2.clearFiles();
      this.dialogVisibleSyj = false;
    },
    async uploadChange (file, fileList) {
    if (fileList && fileList.length > 0) {
      var list = [];
      for (var i = 0; i < fileList.length; i++) {
        if (fileList[i].status == "success") list.push(fileList[i]);
        else list.push(fileList[i].raw);
      }
      this.fileList = list;
    } else {
      this.fileList = [];
    }
  },
  uploadRemove (file, fileList) {
    this.uploadChange(file, fileList);
  },
    onRefresh(){
        this.onSearch()
    },
    onSearch(){
       this.$refs.pager.setPage(1);
       this.getJdExpressList();
    },
    async getJdExpressList(){
      this.Filter.proCode=null;
     
      this.Filter.promotePlan=null;
      this.Filter.startAccountDate=null;
         this.Filter.endAccountDate=null;

      if(this.Filter.UseDate){
        this.Filter.startAccountDate=this.Filter.UseDate[0];
         this.Filter.endAccountDate=this.Filter.UseDate[1];
       }
      const para = {...this.Filter};
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,

      };
      this.listLoading = true;
      const res = await getGrossThirdAndProfitList(params);
      this.listLoading = false;
      this.total = res.data.total
      this.dahuixionglist = res.data.list;
      this.summaryarry=res.data.summary;
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
