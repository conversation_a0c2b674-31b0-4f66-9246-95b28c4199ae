<template>
  <el-popover title="宝贝详情" :width="width" trigger="click" @show="init">
    <MyContainer>
      <vxetablebase :id="'day7202408041846'"
        ref="table"
        v-loading="loading"
        :is-index="true"
        :hasexpand="true"
        :tablefixed="true"
        :has-seq="false"
        :border="true"
        :table-data="data.list"
        :table-cols="tableCols"
        :is-selection="false"
        :is-select-column="false"
        :is-index-fixed="false"
        style="width: 100%;  margin: 0"
        :height="height"
        :showsummary="data.summary ? true : false"
        :summaryarry="data.summary"
        @sortchange="sortchange"
      />
    </MyContainer>
    <el-button slot="reference" type="text">
      <slot />
    </el-button>
  </el-popover>
</template>
<script>
import MyContainer from '@/components/my-container'
import vxetablebase from '@/components/VxeTable/yh_vxetable.vue'
import { formatLinkProCode } from '@/utils/tools'
import {
  day7GetColumns, day7PageGetData
} from '@/api/vo/orderStat'
export default {
  name: 'InventoryPopover',
  components: {
    MyContainer, vxetablebase
  },
  props: {
    condition: {
      type: Object,
      default: () => { return {} }
    },
    columns: {
      type: Array,
      default: () => { return [] }
    },
    width: {
      type: Number,
      default: 600
    },
    height: {
      type: String,
      default: '600'
    }
  },
  data() {
    return {
      rules: {
      },
      query: {
        total: 0,
        currentPage: 1,
        pageSize: 500,
        orderBy: null,
        isAsc: false
      },
      tableCols: [],
      data: { total: 0, list: [], summary: {}},
      loading: false,
      isExport: false
    }
  },
  mounted() {
  },
  methods: {
   async init() {
     await this.getColumns()
      this.getList()
    },
    async getColumns() {
      const { data, success } = await day7GetColumns()
      if (success) {
        data.forEach(d => {
          d.isSeriesField = false
          if (d.prop === 'proCode') {
            d.type = 'html'
            d.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
          }
        })

        var cols = this.columns
        this.tableCols = data.filter(a => cols.indexOf(a.field) > -1).sort((a, b) => {
          return cols.indexOf(a.field) - cols.indexOf(b.field)
        })
        this.query.summarys = data.filter(a => a.summaryType).map(a => { return { column: a['sort-by'], summaryType: a.summaryType } })
      }
    },
    async getList(type) {
      if (type === 'search') {
        this.query.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      // 使用时将下面的方法替换成自己的接口
      try {
        const { data, success } = await day7PageGetData({ ...this.query, ...this.condition })
        if (success) {
          this.data = data
        } else {
          this.$message.error('获取列表失败')
        }
      } catch (error) {
        this.$message.error('获取列表失败')
      } finally {
        this.loading = false
      }
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.query.orderBy = prop
        this.query.isAsc = order.indexOf('descending') === -1
        this.getList()
      }
    }
  }
}
</script>
<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;

    .publicCss {
        width: 200px;
        margin: 0 10px 5px 0;
    }
}
</style>
