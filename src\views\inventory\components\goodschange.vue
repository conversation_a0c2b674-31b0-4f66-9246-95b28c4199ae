<template>
    <container v-loading="pageLoading" style="height: 100%;">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        :clearable="false" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始"
                        end-placeholder="结束">
                    </el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-tooltip class="item" effect="dark" content="默认为精确匹配在开始或者结尾输入*进行模糊匹配" placement="top">
                        <inputYunhan :key="'3'" :keys="'three'" :width="'180px'" ref="childGoodsCode"
                            v-model="filter.goodsCode" :inputt.sync="filter.goodsCode" placeholder="商品编码" :clearable="true" @callback="callbackGoodsCode"
                            title="商品编码"></inputYunhan>
                    </el-tooltip>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-input v-model.trim="filter.goodsName" type="text" maxlength="100" clearable placeholder="请输入商品名称..."
                        style="width:180px;">
                    </el-input>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button>
            </el-button-group>
        </template>
        <vxetablebase :id="'YunHanAdminOnSetTimeRangelist20230701'" :tableData='list' :tableCols='tableCols' style="height: 90%;"
            :tableHandles='tableHandles' :loading='listLoading' :border='true' :that="that" ref="vxetable" @sortchange='sortchange'  />
        <span style="text-align: right;">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" 
                :page-size="10" layout="total, prev, pager, next, jumper" :total="total">
            </el-pagination>
        </span>
        
    </container>
</template>

<script>
import dayjs from "dayjs";
import container from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { formatTime, formatNoLink } from "@/utils/tools";
import { getGoodsChangeListAsync } from '@/api/inventory/goodscostpricechg'
import inputYunhan from "@/components/Comm/inputYunhan";

const tableCols = [
    { istrue: true, align: 'center', prop: 'goodsCode', label: '商品编码', width: 'auto', sortable: 'custom',},
    { istrue: true, align: 'center', prop: 'goodsName', label: '商品名称', width: 'auto', sortable: 'custom',},
    { istrue: true, align: 'center', prop: 'lastCostPrice', label: '原成本', width: 'auto', sortable: 'custom',},
    { istrue: true, align: 'center', prop: 'costPrice', label: '新成本', width: 'auto', sortable: 'custom',},
    { istrue: true, align: 'center', prop: 'modified', label: '变更日期', width: 'auto', sortable: 'custom',},
];

const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
];

const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");


export default {
    name: 'YunHanAdminGoodschange',
    components: { MyConfirmButton, container, vxetablebase, inputYunhan },

    data() {
        return {
            that: this,
            filter: {
                currentPage: 1,
                pageSize: 10,
                goodsCode: null,
                goodsName: null,
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime],
            },
            list: [],
            pager: { OrderBy: "modified", IsAsc: false },
            total: 0,
            tableCols: tableCols,
            tableHandles: tableHandles,
            listLoading: false,
            pageLoading: false,
            dialogVisible: false,
            onFinishLoading: false
        };
    },

    async mounted() {
        await this.onSearch();
    },

    methods: {
        async onSearch() {
            this.filter.currentPage = 1;
            this.getlist();
        },
        async getlist() {
            this.pageLoading = true
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ... this.filter, ... this.pager }
            var res = await getGoodsChangeListAsync(params);
            this.pageLoading = false
            if (!res?.success) {
                return
            }
            const data = res.data.list;
            this.list = data;
            this.total = res.data.total;
        },
        async callbackGoodsCode(val) {
            this.filter.goodsCode = val;
            //this.onSearch();
        },
        async handleSizeChange(val) {
            this.filter.pageSize = val;
            await this.getlist();
            console.log(`每页 ${val} 条`);
        },
        async handleCurrentChange(val) {
            this.filter.currentPage = val;
            await this.getlist();
            console.log(`当前页: ${val}`);
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
    },
};
</script>

<style lang="scss" scoped></style>