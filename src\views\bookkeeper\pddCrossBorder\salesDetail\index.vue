<template>
  <MyContainer>
    <el-tabs v-model="activeName" style="height: 95%;">
      <el-tab-pane label="TEMU-全托销售明细" name="first1" style="height: 98%;">
        <allSalesDetails />
      </el-tab-pane>
      <el-tab-pane label="TEMU-半托销售明细" name="first2" style="height: 98%;" lazy>
        <halfSalesDetails />
      </el-tab-pane>
      <el-tab-pane label="TEMU-半托活动价" name="first7" style="height: 98%;" lazy>
        <halfSalePrice />
      </el-tab-pane>
      <el-tab-pane label="TEMU-半托售后明细" name="first3" style="height: 98%;" lazy>
        <AfterSaleDetails_Ban_Temu />
      </el-tab-pane>
      <el-tab-pane label="TEMU-半托取消订单" name="first6" style="height: 98%;" lazy>
        <halfOfTheOrderCancelled />
      </el-tab-pane>
      <el-tab-pane label="SHEIN-全托销售明细" name="first4" style="height: 98%;" lazy>
        <sheInSalesDetails />
      </el-tab-pane>
      <el-tab-pane label="SHEIN-自营销售明细" name="first5" style="height: 98%;" lazy>
        <sheInSelfSalesDetails />
      </el-tab-pane>
      <el-tab-pane label="SHEIN-半托销售明细" name="first9" style="height: 98%;" lazy>
        <sheInHalfSalesDetails />
      </el-tab-pane>
      <el-tab-pane label="SHEIN-半托售后明细" name="first10" style="height: 98%;" lazy>
        <sheInHalfAfterSalesDetails />
      </el-tab-pane>
      <el-tab-pane label="跨境分销-销售明细" name="first8" style="height: 98%;" lazy>
        <distributionSalesDetails />
      </el-tab-pane>
      
    </el-tabs> 
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import halfSalesDetails from './halfSalesDetails.vue'
import AfterSaleDetails_Ban_Temu from './AfterSaleDetails_Ban_Temu.vue'
import allSalesDetails from './allSalesDetails.vue'
import sheInSalesDetails from './sheInSalesDetails.vue'
import sheInSelfSalesDetails from './sheInSelfSalesDetails.vue'
import halfOfTheOrderCancelled from './halfOfTheOrderCancelled.vue'
import halfSalePrice from './halfSalePrice.vue'
import distributionSalesDetails from './distributionSalesDetails.vue'
import sheInHalfSalesDetails from './sheInHalfSalesDetails.vue'
import sheInHalfAfterSalesDetails from './sheInHalfAfterSalesDetails.vue'

export default {
  components: {
    MyContainer, halfSalesDetails, AfterSaleDetails_Ban_Temu, allSalesDetails, sheInSalesDetails, sheInSelfSalesDetails, halfOfTheOrderCancelled,halfSalePrice,distributionSalesDetails
    ,sheInHalfSalesDetails,sheInHalfAfterSalesDetails
  },
  data() {
    return {
      activeName: 'first1'
    };
  },
  methods: {

  }
};
</script>

<style lang="scss" scoped></style>
