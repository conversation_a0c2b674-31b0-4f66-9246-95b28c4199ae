<template>
    <div style="height:100%;">
        <el-row :gutter="15" style="width:100% ;">
            <div v-for="cutvideo in cutVideoList" :key="cutvideo">
                <el-col :xs="24" :sm="24" :lg="24">
                 参考视频: <a href="javascript:;" style="color: blue; cursor: pointer;"  @click="playerVideo(cutvideo.url)"> {{cutvideo.url}} </a>
                </el-col>
                <el-card class="video-box" v-for="thisVideo in cutvideo.videoList" :key="thisVideo" style="margin-top:10px;float:left;margin-left:20px;">
                    <el-row > 
                        <div style="position: relative;text-align:center;width:60px; height: 70px;">
                            <el-image :src="thisVideo.imgPath" style="max-width: 60px; max-height: 60px;" fit="fill" :lazy="true"></el-image>
                            <span style="display: block;position: absolute;top: 13px;left: 40%;">
                                <a size="mini" class="el-link el-link--primary is-underline" @click="playerVideo(thisVideo.videoPath)" style="margin-left: 3px;color:#ccc;font-size: 23px;">
                                    <i class="el-icon-video-play"></i>
                                </a></span>
                        </div>
                    </el-row>
                    <el-row >场景：{{thisVideo.senceName}} </el-row>
                    <el-row >备注：{{thisVideo.remark}} </el-row>
                    <el-row >段：{{thisVideo.title}} </el-row>
                    <el-row >
                        <el-button   v-if="checkPermission('delCuteVideo')" @click="delData(thisVideo.id)">删除</el-button>
                    </el-row>
                </el-card>
                
            </div>
        </el-row>
    </div>
</template>


<script>   
    //import $ from 'jquery'
    import { getTaskCuteVideoList ,delTaskCuteVideo} from '@/api/media/video'
    export default {
        props: {
            taskid: 0,
            ckindex: 0
        },
        data() {
            return {
                cutVideoList: [],
            };
        },

        async mounted() {
            await this.getData();
        },
        methods: {
            async playerVideo(url) {
                this.$emit("playVideo", url);
            },
            async getData() {
                this.cutVideoList = [];
                var res = await getTaskCuteVideoList({ videoTaskId: this.taskid, ckindex: this.ckindex, type: 1 });
                if (!res?.success) {
                    return
                }
                this.cutVideoList = res.data.list;
            },
            //删除剪辑视频
            async delData(index) {
                this.$confirm('确认删除, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                     cancelButtonText: '取消', 
                     type: 'warning'
                }).then(async () => {
                    const res = await delTaskCuteVideo({ cuteId: index});                   
                    if (res?.success) {
                        this.$message({ type: 'success', message: '删除成功!' });
                        this.getData();
                    }
                });

            }
        }
    };
</script>
