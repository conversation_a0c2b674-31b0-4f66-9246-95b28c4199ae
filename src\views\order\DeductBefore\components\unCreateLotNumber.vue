<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTakeTime" :endDate.sync="ListInfo.endTakeTime"
                    :clearable="false" class="publicCss" startPlaceholder="接单时间" endPlaceholder="接单时间" />
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="codeCallback($event, 1)" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.styleCodes" v-model="ListInfo.styleCodes"
                    placeholder="款式编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="codeCallback($event, 2)" title="款式编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <el-select v-model="ListInfo.invSendTypes" placeholder="可发货类型" class="publicCss" collapse-tags clearable
                    multiple>
                    <el-option label="全部发" value="全部发" />
                    <el-option label="部分发" value="部分发" />
                    <el-option label="不可发" value="不可发" />
                </el-select>
                <chooseWareHouse v-model="ListInfo.wmsIds" class="publicCss" multiple :filter="sendWmsesFilter" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%"
            :showsummary="data.summary ? true : false" :summaryarry="data.summary" @sortchange="sortchange" />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog :title="logQuery.title" :visible.sync="dialogVisible" width="70%" height="600px" v-dialogDrag>
            <codeLog v-if="dialogVisible" :logQuery="logQuery" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/Orders/InWmsUnBatch/'
import { mergeTableCols } from '@/utils/getCols'
import codeLog from './codeLog.vue'
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, codeLog, chooseWareHouse
    },
    data() {
        return {
            api,
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false,
                summarys: [],
                startTakeTime: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
                endTakeTime: dayjs().format('YYYY-MM-DD'),
                wmsIds: [],
                invSendTypes: ['部分发', '不可发']
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            dialogVisible: false,
            logQuery: {
                resource: null,
                unBatch_GoodsCode: null,
                title: null,
            }
        }
    },
    async mounted() {
        await this.getCol();
        await this.getList()
    },
    methods: {
        sendWmsesFilter(wmses) {
            return wmses.filter((a) => a.isSendWarehouse == '是' && a.isWc != 1);
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        showLog(row, prop) {
            this.logQuery = {
                resource: prop,
                unBatch_GoodsCode: row.goodsCode,
                title: row.goodsCode + row.goodsName,
                startTakeTime: this.ListInfo.startTakeTime,
                endTakeTime: this.ListInfo.endTakeTime,
                wmsIds: [row.wmsId]
            }
            this.dialogVisible = true
        },
        codeCallback(e, val) {
            if (val == 1) {
                this.ListInfo.goodsCodes = e
            } else {
                this.ListInfo.styleCodes = e
            }
        },
        proCodeCallback(val) {
            this.ListInfo.proCode = val
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                const obj = {
                    orderCount: 'unBatch',
                    goodsQty: 'unBatch',
                    orderCount0_1: 'unBatch_0_1',
                    goodsQty0_1: 'unBatch_0_1',
                    orderCount2_3: 'unBatch_2_3',
                    goodsQty2_3: 'unBatch_2_3',
                    orderCount3_6: 'unBatch3_6',
                    goodsQty3_6: 'unBatch3_6',
                    orderCount6_: 'unBatch6_',
                    goodsQty6_: 'unBatch6_',
                    orderCountOverTime: 'unBatch_overTime',
                    goodsQtyOverTime: 'unBatch_overTime',
                }
                data.forEach(item => {
                    if (obj[item.prop]) {
                        item.type = 'click'
                        item.handle = (that, row) => that.showLog(row, obj[item.prop])
                    }
                })
                this.tableCols = mergeTableCols(data)
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0;
    }
}
</style>
