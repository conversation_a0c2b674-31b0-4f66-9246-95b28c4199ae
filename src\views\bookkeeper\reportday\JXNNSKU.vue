<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-select filterable clearable v-model="ListInfo.shopCodeList" placeholder="店铺" style="width: 160px" multiple collapse-tags>
              <el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
        <el-input v-model.trim="ListInfo.goodsCode" placeholder="店铺商品编码" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
<!--        <el-button type="primary" @click="batchApply">一键申报</el-button>-->

      </div>
    </template>
    <vxetablebase ref="table" :border='true' :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="true"  :isSelectColumn="true" @select='selectchange'
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
      :checkbox-config="{labelField: 'id', highlight: true, range: true}"
      @checkbox-range-end="callback"
      :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="120" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;align-items: center;">
              <!-- <el-button type="text" @click="onEditMethod(row)">编辑</el-button> -->
              <el-button type="text" @click="onDel(row)">删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
          <!-- 添加数据导入按钮 -->
          <el-button style="margin-left: 10px" size="small" type="warning" @click="confirmErpImport">ERP接口导入</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="editName" :visible.sync="editdialogVisible" width="25%" v-dialogDrag>
      <div style="height: 180px;width: 100%;" v-loading="editloading">
        <el-form :model="editform" ref="editform" :rules="editrules" label-width="100px" class="demo-ruleForm">
          <el-form-item label="快递公司" prop="expressCompany">
            <el-input v-model="editform.expressCompany" style="width: 80%;"></el-input>
          </el-form-item>
          <el-form-item label="地区" prop="area">
            <el-input v-model="editform.area" style="width: 80%;"></el-input>
          </el-form-item>
          <el-form-item label="最低快递费" prop="expressFee">
            <el-input v-model="editform.expressFee" style="width: 80%;"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editdialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSaveMethod">确 定</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from 'dayjs'
import { importJXNNSKUAsync,getJXNNSKUList,deleteJXNNSKU } from '@/api/bookkeeper/reportdayV2'
import {generateJXNNSKUData, taskStatusJXNNSKU} from '@/api/bladegateway/worcestorejk'
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import {pageGoodsCodeStock} from "@/api/inventory/goodscodestock";
import {formatTime} from "@/utils";


export default {
  name: "JXNNSKU",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      editloading: false,
      editform: {
        expressCompany: null,
        area: null,
        expressFee: null,
        id: 0,
      },
      editdialogVisible: false,
      editName: '新增',
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        goodsCode: null,
        shopCodeList: null,
      },
      editrules: {
        expressCompany: [{ required: true, message: '请输入快递公司', trigger: 'blur' }],
        area: [{ required: true, message: '请输入地区', trigger: 'blur' }],
        expressFee: [{ required: true, message: '请输入最低快递费', trigger: 'blur' }],
      },
      tableCols: [
        { istrue: true, fixed: 'left', width: '40', type: "checkbox" },
        { sortable: 'custom', width: '300', align: 'center', prop: 'shopCode', label: '店铺编码', },
        { sortable: 'custom', width: '300', align: 'center', prop: 'shopName', label: '店铺名称', },
        { sortable: 'custom', width: '300', align: 'center', prop: 'goodsCode', label: '商品编码', },
        { sortable: 'custom', width: '300', align: 'center', prop: 'skuId', label: 'skuId', },
      ],
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      selectedRows: [], // 新增选中行数据
    }
  },
  async mounted() {
    await this.getList()
  },
  async created() {
    const res1 = await getAllShopList({ platforms: [7] });
    this.shopList = res1.data?.map(item => { return { value: item.shopCode, label: item.shopName }; });
  },
  methods: {
    async onSaveMethod() {
      if (!this.editform.expressCompany || !this.editform.area || !this.editform.expressFee) {
        this.$message({ message: "请填写完整信息", type: "warning" });
        return false;
      }
      this.editloading = true
      // var res = await editSpecAreaExpressFee(this.editform)
      // this.editloading = false
      // if (res?.success) {
      //   this.$message({ message: "保存成功", type: "success" });
      //   this.editdialogVisible = false
      //   await this.getList()
      // }
    },
    onAddnewMethod() {
      this.setEditForm('新增', {
        expressCompany: null,
        area: null,
        expressFee: null,
        id: 0
      });
    },
    onEditMethod(row) {
      this.setEditForm('编辑', row);
    },
    onDel(row) {
      this.$confirm("确定删除吗?", "提示", { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning", }).then(
        async () => {

          const res = await deleteJXNNSKU({ id: row.id})
                    if (!res?.success) {
                    } else {
                        this.$message({ type: 'success', message: '删除成功!' });
                    }
                    this.getList()
          });



    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    setEditForm(editName, formData) {
      this.editName = editName;
      this.editform = {
        expressCompany: formData.expressCompany,
        area: formData.area,
        expressFee: formData.expressFee,
        id: formData.id
      };
      this.editdialogVisible = true;
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importJXNNSKUAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      if(this.ListInfo.shopCodeList && this.ListInfo.shopCodeList.length > 0){
          this.ListInfo.shopCodes = this.ListInfo.shopCodeList.join(",")
      }else{
          this.ListInfo.shopCodes = null
      }
      const { data, success } = await getJXNNSKUList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async batchApply() { // 新增一键申报方法
        if (this.selectedRows.length === 0) {
            this.$message.warning("还未勾选申报数据，请勾选数据申报");
            return;
        }
      this.$showDialogform({
        path: `@/views/operatemanage/productalllink/goodscodestock/index.vue`,
        title: '一键申报',
        autoTitle: false,
        args: { selectedRows: this.selectedRows },
        height: '700px',
        width: '80%',
      })
     /* var goodsCodes = this.selectedRows.map((item) => {
        return item.goodsCode
      })
      var goodsCode = goodsCodes.join(',');
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      var startDate = formatTime(dayjs().subtract(60, 'day'), "YYYY-MM-DD");
      var endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
      var timerange = [this.startDate, this.endDate];
      var res = await pageGoodsCodeStock({...pager, ...page,goodsCode:goodsCode,OrderBy: 'goodsCode',startDate: startDate,endDate: endDate,timerange: timerange});
      if (res?.success) {
        this.$showDialogform({
          path: `@/views/operatemanage/productalllink/goodscodestock/ApplyStockGoodsForm.vue`,
          title: '一键申报',
          autoTitle: false,
          args: { selRows: res.data.list },
          height: '500px',
          width: '80%',
        })
      }*/


    },
    confirmErpImport() {
      this.$confirm('是否通过ERP接口导入数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.erpImportData();
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消导入'
        });
      });
    },
    async erpImportData() {
      // 调用ERP接口的逻辑
      console.log('调用ERP接口进行数据导入');
      // 这里添加实际的ERP接口调用代码
      const res = await generateJXNNSKUData();
      console.log("5555", res)
      if (res?.success) {
        this.$message({ message: '任务正在进行,请稍后... ', type: "success" });
        this.pollStatus(res.data);
      }
    },
    pollStatus(taskId) {
      let params = {
        "taskId": taskId,
      }
      const interval = setInterval(() => {
        taskStatusJXNNSKU(params).then(res => {
          console.log("res",res);
          if (res?.success) {
            if (res.data == "completed") {
              this.$message({ message: '导入成功', type: "success" });
              this.dialogVisible = false;
              this.getList()
            } else if (res.data == "error") {
              this.$message({ message: '导入失败', type: "error" });
            }
            clearInterval(interval);
          }
        })
      }, 10000); // 每10秒查询一次
    },
    selectchange(rows, row) { // 选中行变化时更新selectedRows
      this.selectedRows = rows;
    },
    callback(val) {
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 5px;
  }
}
</style>
