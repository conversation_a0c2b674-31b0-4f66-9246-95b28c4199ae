<template>
  <section class="wrap">

        <div>
            <el-card style="width:60%" v-for="item in list" :key="item.id" v-loading="updatelogisshow">
                <div slot="header" class="clearfix">
                    <span >{{item.title}}</span>
                </div>
                <div class="text item">
                    <span v-html="item.content" @click="showImg($event)">{{item.content}}</span>
                </div>
            </el-card>
        </div> 
    <el-backtop target=".container .main" :visibility-height="200" class="backtop" />

    <el-image-viewer v-if="showGoodsImage" :url-list="imgList" :on-close="closeFunc" style="z-index:9999;"/>
  </section>
</template>

<script>
import { formatTime } from "@/utils";
import { getAsync } from '@/api/admin/opration-log'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import cescolumnmerge from "@/components/Table/yhcolumnmerge.vue";
import cescolumn from "@/components/Table/yhcolumn.vue";

export default {
  name: 'Welcome',
  components: {cescolumnmerge,cescolumn,ElImageViewer},
  data() {
    return {
      filter:{
            id:null,
            title:null        
            },
      pager:{OrderBy:"createdTime",IsAsc:false},
      list: [],
      imgList:[],
      showGoodsImage:false,
      updatelogisshow:false
    }
  },
  async mounted() {
    //每两小时更新一次
    // this.$nextTick(async () =>{
    //   setInterval(this.getlist,7200000)
    // })
    
  },
  methods:{
    async getlist(row){
      this.filter.title = row.title
      this.filter.id = row.id
      var params = {...this.filter, ...this.pager}
      this.updatelogisshow = true
      const res = await getAsync(params)
      if (!res?.success){
        return
      }
      this.updatelogisshow = false
      const data = res.data
      // data.forEach(f =>{
      //   f.createdTime = formatTime(f.createdTime, "YYYY-MM-DD")
      // })
      this.list = data
    },
    async showImg(e){
      if (e.target.tagName == 'IMG') {
        console.log('image',e)
        this.showGoodsImage = true;
        this.imgList = [];
        this.imgList.push(e.target.src);
      }
    },
    async clicktimelist(row){
      console.log('时间',row)
    },
    async handleSetLineChartData(type){

    },
    async closeFunc(){
      this.showGoodsImage = false;
    },
  },
  }
</script>
<style scoped>
.clearfix{
  height: 20px;
}
</style>