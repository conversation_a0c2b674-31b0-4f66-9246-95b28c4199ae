<template>
    <container v-loading="pageLoading">
        <!--列表-->
        <div style="height: 400px;">
            <vxetablebase :id="'purchasegoodsList202301031318001'" :isIndex='true' :tableHandles='tableHandles'
            :tableData='list' :tableCols='tableCols' :tablefixed='true' :loading='listLoading' :border='true' :that="that"
            ref="vxetable" @cellClick='cellclick' @sortchange='sortchange' >   
            </vxetablebase>
        </div>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </container>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { getProductGoodsCodeHistory, addOrUpdateProductGoodsCode } from "@/api/inventory/goodscodestock"

const tableCols = [
    { istrue: true, prop: 'status', label: '状态', width: '120', align: 'center',},
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '120', align: 'center',},
    { istrue: true, prop: 'goodsName', label: '商品名称', width: '400', },
    { istrue: true, prop: 'count', label: '进货数量', width: '120', sortable: 'custom',},
    { istrue: true, prop: 'createdTime', label: '申请时间', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'warehouseDate', label: '入库时间', width: 'auto', sortable: 'custom', },
];
const tableHandles = [
    
];

export default {
    name: 'YunHanAdminCodedpurchasehistory',
    components: { container, MyConfirmButton, vxetablebase, cesTable },
    props: {
        goodsCodeFilter: {},
    },

    data() {
        return {
            that: this,
            // filter: {
            //     goodsCode: null,
            //     appStatus: 0,
            //     isSuccess: 0
            // },
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "goodsCode", IsAsc: false },
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            showGoodsImage: false,
        };
    },

    async mounted() {

    },

    methods: {
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            debugger
            const params = { ...pager, ...page, ... this.goodsCodeFilter}
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await getProductGoodsCodeHistory(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.total = res.data.total;
            const data = res.data.list;
            this.list = data
        },
        async showImg(e) {
            this.showGoodsImage = true;
            this.imgList = [];
            this.imgList.push(e);
        },
        async closeFunc() {
            this.showGoodsImage = false;
        },
        async cellclick(row, column, cell, event) {

        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>

<style lang="scss" scoped></style>