<template>
  <my-container>
    <!--顶部操作-->
    <el-tabs v-model="activeName" style="height:94%;">
      <el-tab-pane label="系列编码" name="tab0" style="height: 100%;">
          <StyleCode  ref="ViolatStatisticsStyleCode" lazy></StyleCode>
      </el-tab-pane>
      <el-tab-pane label="宝贝ID" name="tab1" style="height: 100%;" lazy>
          <PreCode ref="ViolatStatisticsProCode" lazy></PreCode>
      </el-tab-pane>
      <el-tab-pane label="店铺" name="tab2" style="height: 100%;" lazy>
          <Shop ref="ViolatStatisticsShop" lazy></Shop>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import StyleCode from "@/views/operatemanage/OperationalMiddleOffice/ViolatStatisticsDialog/ViolatStatisticsStyleCode";
import PreCode from "@/views/operatemanage/OperationalMiddleOffice/ViolatStatisticsDialog/ViolatStatisticsProCode";
import Shop from "@/views/operatemanage/OperationalMiddleOffice/ViolatStatisticsDialog/ViolatStatisticsShop";

export default {
  name: 'ViolatStatistics',
  components: { MyContainer, StyleCode, PreCode, Shop },
  data() {
    return {
      that: this,
      activeName: 'tab0',
      pageLoading: false,
      infos: null,
      preInfos: null,
    }
  },
  async mounted() {
    // this.init();
    // this.getList();

    // //根据权限来判断显示哪个tab
    // if (this.checkPermission('refundData')){
    //     this.activeName = 'tab0';
    // }
    // await this.onSearch();
  },
  methods: {

    onSearch() {
    }
  },
}
</script>