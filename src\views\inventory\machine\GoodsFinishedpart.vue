<template>
    <container v-loading="pageLoading">
        <el-tabs v-model="activeName" style="height: 94%;">
            <el-tab-pane lazy label="编码" name="first" style="height: 100%;">
                <index ref="index"></index>
            </el-tab-pane>
            <el-tab-pane lazy label="加工记录" name="second" style="height: 100%;">
                <finishedpartVue ref="finishedpartVue"></finishedpartVue>
            </el-tab-pane>

        </el-tabs>
    </container>
</template>

<script>

import container from "@/components/my-container";
import index from "./index.vue";
import finishedpartVue from './finishedpart.vue';


export default {
    name: 'YunHanAdminGoodsFinishedpart',
    components: { container, index, finishedpartVue },

    data() {
        return {
            that: this,
            activeName: 'first',
            listLoading: false,
            pageLoading: false,
        };
    },

    async mounted() {
        await this.onSearch();
    },

    methods: {
        async onSearch() {
            this.$nextTick(() => {
                if (this.activeName == 'first') this.$refs.index.onSearch();
                if (this.activeName == 'second') this.$refs.finishedpartVue.onSearch();
            })
        },
        
    },
};
</script>
 

</style>