<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query no-label" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="店铺编码:">
                    <el-input v-model="filter.shopcode" placeholder="店铺编码" class="publicCss" clearable  maxlength="50" />
                </el-form-item>
                <el-form-item label="平台店铺ID:">
                    <el-input v-model.trim="filter.platformShopID" placeholder="平台店铺ID" maxlength="50"
                        class="publicCss" clearable />
                </el-form-item>
                <el-form-item label="店铺名称:">
                    <el-input v-model="filter.shopName" placeholder="店铺名称" clearable  maxlength="100"/>
                </el-form-item>
                <el-form-item v-if="checkPermission('shopManage_list_showEmail')" label="邮箱:">
                    <el-input v-model="filter.email" placeholder="邮箱" clearable maxlength="50" />
                </el-form-item>
                <el-form-item label="公司类目:">
                    <el-select v-model="filter.mainCategories" placeholder="请选择主营类目" collapse-tags clearable filterable
                        class="publicCss">
                        <el-option v-for="item in mainCategoriesList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="属性:">
                    <el-select v-model="filter.property" placeholder="请选择属性" collapse-tags clearable filterable
                        class="publicCss">
                        <el-option v-for="item in propertyList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="平台:">
                    <el-select v-model="filter.platform" placeholder="请选择平台" class="publicCss" @change="getShopList" clearable>
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="店铺负责人:">
                    <el-select v-model="filter.shopManager" placeholder="请选择店铺负责人" collapse-tags clearable filterable
                        class="publicCss">
                        <el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.label" />
                    </el-select>
                </el-form-item>
                <el-form-item label="运营组:">
                    <el-select v-model="filter.groupId" placeholder="请选择运营组" collapse-tags clearable filterable
                        class="publicCss">
                        <el-option v-for="item in groupIds" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="是否开启:">
                    <el-select v-model="filter.isOpen" placeholder="是否开启" class="publicCss">
                        <el-option label="所有" value></el-option>
                        <el-option label="已开启" value="1"></el-option>
                        <el-option label="未开启" value="0"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="是否授权:">
                    <el-select v-model="filter.isAuthSelf" placeholder="是否授权" class="publicCss">
                        <el-option label="所有" value></el-option>
                        <el-option label="已授权" value="1"></el-option>
                        <el-option label="未授权" value="0"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="项目:">
                    <el-select v-model="filter.projName" filterable clearable placeholder="项目" class="publicCss">
                        <el-option v-for="item in projectList" :key="item.projName" :label="item.projName"
                            :value="item.projName"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="是否绑定店铺:">
                    <el-select v-model="filter.isBindShopPlatformId" filterable clearable placeholder="是否绑定店铺" class="publicCss">
                        <el-option label="是" :value="1"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="细分平台:">
                  <el-select v-model="filter.xiFenPlatformList" filterable clearable placeholder="细分平台" multiple collapse-tags style="width: 170px" v-if="checkPermission('SegmentationPlatform')" >
                    <el-option v-for="item in segmentationList" :key="item" :label="item" :value="item" />
                  </el-select>
                </el-form-item>
                <el-form-item label="状态:">
                  <el-select v-model="filter.shopStatusList" filterable clearable placeholder="状态" multiple collapse-tags style="width: 170px" >
                    <el-option v-for="item in statusSelection" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="主账号手机号:">
                  <el-input v-model="filter.mainPhone" placeholder="主账号手机号" clearable maxlength="11" class="publicCss" />
                </el-form-item>
                <el-form-item label="验证码接收人:">
                  <el-input v-model="filter.codeReceiver" placeholder="验证码接收人" clearable maxlength="20" class="publicCss" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="canclick">拼多多店铺授权</el-button>
                    <el-button type="primary" @click="editPlatId"
                        v-if="checkPermission('editPlatId')">修改平台店铺ID</el-button>
                    <el-button type="primary" @click="editEmail"
                        v-if="checkPermission('shopManage_list_editEmail')">修改邮箱</el-button>
                    <el-button type="primary" @click="editStatus"
                        v-if="checkPermission('shopManage_list_editStatus')">修改状态</el-button>
                    <el-button type="primary" @click="setJDBusinessCode">设置京东商家编码</el-button>
                </el-form-item>
            </el-form>
        </template>

        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            :isSelectColumn="false" :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles'
            :loading="listLoading" @select="selectCheckBox" isSelection>
        </ces-table>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <!-- <el-drawer :title="formtitle" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
            :visible.sync="addFormVisible" direction="btt" size="'auto'" class="el-drawer__wrapper"
            style="position:absolute;">
            <form-create ref="autoform" :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options" />
            <div class="drawer-footer">
                <el-button @click.native="addFormVisible = false">取消</el-button>
                <my-confirm-button type="submit" :loading="addLoading" @click="onAddSubmit" />
            </div>
        </el-drawer> -->

        <el-drawer :title="formtitle" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
            :visible.sync="addFormVisible" direction="btt" size="'auto'" class="el-drawer__wrapper"
            style="position: absolute;">
            <el-form :inline="true" ref="editDialog" :model="editDialog" :rules="rule.editDialog">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="店铺编码" prop="shopCode">
                            <el-input v-model="editDialog.shopCode" :disabled=true></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="店铺名称" prop="shopName">
                            <el-input v-model="editDialog.shopName" :disabled=true></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="平台店铺ID" prop="platformShopID">
                            <el-input v-model="editDialog.platformShopID" :disabled=true></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="8">
                        <el-form-item label="店铺负责人" prop="shopManager">
                            <el-select clearable filterable v-model="editDialog.shopManager" placeholder="请选择店铺负责人">
                                <el-option v-for="item in shopManagers" :key="item.value" :label="item.label"
                                    :value="item.label"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="运营组" prop="groupId">
                            <el-select clearable filterable v-model="editDialog.groupId" placeholder="请选择运营组">
                                <el-option v-for="item in groupIds" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="平台" prop="platform">
                            <el-select clearable filterable v-model="editDialog.platform" placeholder="请选择平台">
                                <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="8">
                        <el-form-item label="是否开启" prop="isOpen">
                            <el-select clearable filterable v-model="editDialog.isOpen" placeholder="请选择是否开启">
                                <el-option v-for="item in isOpen" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                                <!-- <el-option value="1" label="是">是</el-option>
                                <el-option value="0" label="否">否</el-option> -->
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="是否参与月度计算" prop="isCalcSettlement">
                            <el-select clearable filterable v-model="editDialog.isCalcSettlement"
                                placeholder="请选择是否参与月度计算">
                                <el-option v-for="item in isCalcSettlement" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                                <!-- <el-option value="true" label="是">是</el-option>
                                <el-option value="false" label="否">否</el-option> -->
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="月度结算类型" prop="settlementCalcType">
                            <el-select clearable filterable v-model="editDialog.settlementCalcType"
                                placeholder="请选择月度结算类型">
                                <el-option v-for="item in settlementCalcType" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                                <!-- <el-option :value=null :label="无"></el-option>
                                <el-option :value=0 :label="普通天猫"></el-option>
                                <el-option :value=1 :label="普通拼多多"></el-option>
                                <el-option :value=2 :label="淘工厂菜鸟驿站"></el-option>
                                <el-option :value=3 :label="头条抖音"></el-option>
                                <el-option :value=4 :label="京东"></el-option>
                                <el-option :value=5 :label="苏宁"></el-option>
                                <el-option :value=6 :label="阿里巴巴"></el-option>
                                <el-option :value=7 :label="快手"></el-option> -->

                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="8">
                        <el-form-item label="是否开启提前收款" prop="isPreceipt">
                            <el-select clearable filterable v-model="editDialog.isPreceipt" placeholder="请选择是否开启提前收款">
                                <el-option v-for="item in isPreceipt" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                                <!-- <el-option value="true" label="是">是</el-option>
                                <el-option value="false" label="否">否</el-option> -->
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="是否开启首单拉新" prop="isFirstNew">
                            <el-select clearable filterable v-model="editDialog.isFirstNew" placeholder="请选择是否开启首单拉新">
                                <el-option v-for="item in isFirstNew" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                                <!-- <el-option value="true" label="是">是</el-option>
                                <el-option value="false" label="否">否</el-option> -->
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="主体公司" prop="company">
                            <el-select v-model="editDialog.company" placeholder="请选择主体公司" filterable>
                                <el-option v-for="item in companylist" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                            <el-button type="primary" @click="onAddCompany"
                                v-if="checkPermission('api:OperateManage:Base:Shop:AddNewMainWarehouseList')">添加</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="8" v-if="editDialog.platform == 2">
                        <el-form-item label="公司类目" prop="mainCategories">
                            <el-select clearable filterable v-model="editDialog.mainCategories" placeholder="请选择公司类目">
                                <el-option v-for="item in mainCategoriesList" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" v-if="editDialog.platform == 6">
                        <el-form-item label="属性" prop="property">
                            <el-select clearable filterable v-model="editDialog.property" placeholder="请选择属性">
                                <el-option v-for="item in propertyList" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-form-item :span="8" label="项目" prop="projName">
                        <el-select v-model="editDialog.projName" filterable clearable placeholder="项目">
                            <el-option v-for="item in projectList" :key="item.projName" :label="item.projName"
                                :value="item.projName"></el-option>
                        </el-select>
                    </el-form-item>
                </el-row>
                <el-row>
                  <el-form-item :span="8" label="细分平台" prop="xiFenPlatform">
                    <el-select v-model="editDialog.xiFenPlatform" filterable clearable placeholder="细分平台">
                      <el-option v-for="item in segmentationList" :key="item" :label="item" :value="item" />
                    </el-select>
                  </el-form-item>
                </el-row>
            </el-form>

            <div class="drawer-footer">
                <el-button @click.native="addFormVisible = false">取消</el-button>
                <my-confirm-button type="submit" :loading="addLoading" @click="onAddSubmit" />
            </div>
        </el-drawer>

        <el-dialog title="添加主体公司" :visible.sync="showAddCompany" :width="'380px'" v-dialogDrag>
            <el-form ref="editDialog2" :model="editDialog2" :rules="rule.editDialog2" @submit.native.prevent
                label-width="80px">
                <el-form-item label="主体公司" prop="addCompany">
                    <!-- <el-col :span="11"> -->
                    <el-input v-model="editDialog2.addCompany" placeholder="请输入主体公司" maxlength="20"
                        style="width: 260px;"></el-input>
                    <!-- </el-col> -->
                </el-form-item>

                <span style="float: right; position: absolute; bottom: 10px; right: 20px;">
                    <el-button type="info" @click="onCancel">取消</el-button>
                    <el-button type="primary" @click="addCompany">添加</el-button>
                </span>
            </el-form>
        </el-dialog>

        <el-dialog title="修改平台店铺ID" :visible.sync="editPlatIdVisible" :width="'380px'" v-dialogDrag>
            <div style="display: flex;justify-content: center;">
                <el-input v-model="platformShopID" placeholder="请输入" style="width: 200px;" clearable maxlength="50" />
            </div>
            <div class="btnGroup">
                <el-button @click="editPlatIdVisible = false">取消</el-button>
                <el-button type="primary" @click="editPlatSubmit">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog title="修改店铺邮箱" :visible.sync="editShopEmailVisible" :width="'380px'" v-dialogDrag>
            <div style="display: flex;justify-content: center;">
                <el-input v-model="email" placeholder="请输入" style="width: 200px;" clearable maxlength="50" />
            </div>
            <div class="btnGroup">
                <el-button @click="editShopEmailVisible = false">取消</el-button>
                <el-button type="primary" @click="editShopEmailSubmit">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog title="客服" :visible.sync="KFlogVisible" width="50%" v-dialogDrag >
        <customerLogs :customerInfo="customerInfo" v-if="KFlogVisible"/>
        </el-dialog>

        <el-dialog title="修改状态" :visible.sync="editStatusVisible" width="30%" v-dialogDrag v-loading="editStatusLoading">
          <el-form ref="editStatusForm" :model="editStatusForm" label-width="130px">
            <el-form-item label="状态" prop="shopStatus">
                <el-select v-model="editStatusForm.shopStatus" placeholder="请选择状态" clearable filterable multiple style="width: 80%;">
                    <el-option v-for="item in statusSelection" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="主账号手机号" prop="mainPhone">
                <inputNumberYh v-model="editStatusForm.mainPhone" :placeholder="'请输入主账号手机号'" :maxlength="11" :max="99999999999" style="width: 80%;"/>
            </el-form-item>
            <el-form-item label="验证码接收人" prop="codeReceiver">
                <el-input v-model="editStatusForm.codeReceiver" placeholder="请输入验证码接收人" maxlength="20" clearable style="width: 80%;" />
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="editStatusVisible = false">取消</el-button>
            <el-button type="primary" @click="editStatusSubmit">确定</el-button>
          </span>
        </el-dialog>

        <el-dialog title="设置京东商家编码" :visible.sync="setJDBusinessCodeVisible" :width="'380px'" v-dialogDrag>
            <div style="display: flex;justify-content: center;">
                <el-input v-model="code" placeholder="请输入京东商家编码" maxlength="20"
                           style="width: 260px;"></el-input>
            </div>
            <div class="btnGroup">
                <el-button @click="setJDBusinessCodeVisible = false">取消</el-button>
                <el-button type="primary" @click="JDsetCodeSubmit">确定</el-button>
            </div>
        </el-dialog>
    </my-container>
</template>

<script>
import {
    getbyid, deletebyid, addoredit, getList, GetAllShopManager,UpdateShopCustomCode,
    getDirectorList, getDirectorGroupList, getMainWarehouseList, addNewMainWarehouseList, updateShopPlatformId, updateShopEmail, updateShopStatus, getAllShopXiFenPlatform
} from '@/api/operatemanage/base/shop'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatPlatform, formatYesorno, platformlist, companylist } from "@/utils/tools";
import { rulePlatform, ruleDirectorGroup, ruleDirectorName, ruleDirectorBackup } from "@/utils/formruletools";
import { getProductProjectList } from "@/api/operatemanage/productmanager"
import customerLogs from './customerLogs.vue'
import { getUserInfo } from '@/api/operatemanage/productalllink/alllink';
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";

const statusSelection = [
    { value: '在用', label: '在用' },
    { value: '退店中', label: '退店中' },
    { value: '已退店', label: '已退店' },
]
//是否开启
const isOpens = [
    { value: 1, label: '是' },
    { value: 0, label: '否' },
];
//是否参与月度计算
const isCalcSettlements = [
    { value: true, label: '是' },
    { value: false, label: '否' },
];
//月度结算类型
const settlementCalcTypes = [
    // { value: null, label: "无"},
    { value: 0, label: "普通天猫" },
    { value: 1, label: "普通拼多多" },
    { value: 2, label: "淘工厂菜鸟驿站" },
    { value: 3, label: "头条抖音" },
    { value: 4, label: "京东" },
    { value: 5, label: "苏宁" },
    { value: 6, label: "阿里巴巴" },
    { value: 7, label: "快手" },
    { value: 8, label: "视频号" },
    { value: 9, label: "小红书" },
]
//是否开启提前收款
const isPreceipts = [
    { value: true, label: '是' },
    { value: false, label: '否' },
];
//是否开启首单拉新
const isFirstNews = [
    { value: true, label: '是' },
    { value: false, label: '否' },
];

//公司类目
const mainCategoriesListConst = [
    { value: 1, label: '服饰箱包' },
    { value: 2, label: '家纺家具家装' },
    { value: 3, label: '家居生活' },
    { value: 4, label: '健康医药' },
    { value: 5, label: '美容个护' },
    { value: 6, label: '母婴玩具' },
    { value: 7, label: '汽配摩托' },
    { value: 8, label: '食品保健' },
    { value: 9, label: '数码电器' },
    { value: 10, label: '水果生鲜' },
    { value: 11, label: '虚拟海淘医药' },
    { value: 12, label: '运动户外' }
]
//属性
const propertyListConst = [
    { value: 1, label: '达播' },
    { value: 2, label: '商品卡' },
]
//列表输出列
const tableCols = [
    { istrue: true, prop: 'shopCode', label: '店铺编码', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'shopName', label: '店铺名称	', width: '200' },
    {
    istrue: true,  label: '客服', width: '100', type: 'button', btnList: [
            {
                label: '查看',
                handle: (that, row) => that.openKFlog(row),
            }
        ]
    },
    {istrue: true,  permission: "shopManage_list_showEmail", prop: 'email', label: '邮箱', width: '200'},
    {
        istrue: true, prop: 'mainCategories', label: '公司类目', width: '80',
        formatter: (row) => { if (!row.mainCategories) return ""; else return mainCategoriesListConst.find(f => f.value == row.mainCategories)?.label; }
    },
    {
        istrue: true, prop: 'property', label: '属性', width: '60',
        formatter: (row) => { if (!row.property) return ""; else return propertyListConst.find(f => f.value == row.property)?.label; }
    },
    { istrue: true, prop: 'shopManager', label: '店铺负责人', width: '100' },
    { istrue: true, prop: 'platformShopID', label: '平台店铺ID', width: '90', },
    { istrue: true, prop: 'platformShopID2', label: '平台店铺ID2', width: '100', },
    { istrue: true, prop: 'platform', label: '平台', width: '70', sortable: 'custom', formatter: (row) => formatPlatform(row.platform) },
    { istrue: true, prop: 'customerCode', label: '商家编码', width: '100', },
    { istrue: true, prop: 'groupId', label: '运营组', width: '90', sortable: 'custom', formatter: (row) => row.groupName },
    { istrue: true, prop: 'projName', label: '项目', width: '70', sortable: 'custom' },
    { istrue: true, prop: 'xiFenPlatform', label: '细分平台', width: '100', sortable: 'custom', permission: "SegmentationPlatform" },
    { istrue: true, prop: 'companyName', label: '主体公司', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'shopStatus', label: '状态', width: '125', sortable: 'custom' },
    { istrue: true, prop: 'mainPhone', label: '主账号手机号', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'codeReceiver', label: '验证码接收人', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'statusModifiedUserName', label: '修改者', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'statusModifiedTime', label: '修改时间', width: '135', sortable: 'custom' },
    { istrue: true, prop: 'isOpen', label: '是否开启', width: '100', formatter: (row) => formatYesorno(row.isOpen) },
    {
        istrue: true, prop: 'isCalcSettlement', label: '参与月度计算', width: '130', sortable: 'custom', formatter: (row) => {
            if (row.isCalcSettlement == null) return '';
            if (row.isCalcSettlement == true) return '是';
            else return '否';
        }
    },
    {
        istrue: true, prop: 'settlementCalcType', label: '月度结算类型', width: '130', sortable: 'custom', formatter: (row) => {
            if (row.settlementCalcType == null) return '';
            if (row.settlementCalcType == 0) return '普通天猫';
            else if (row.settlementCalcType == 1) return '普通拼多多';
            else if (row.settlementCalcType == 2) return '淘工厂菜鸟驿站';
            else if (row.settlementCalcType == 3) return '头条抖音';
            else if (row.settlementCalcType == 4) return '京东';
            else if (row.settlementCalcType == 5) return '苏宁';
            else if (row.settlementCalcType == 6) return '阿里巴巴';
            else if (row.settlementCalcType == 7) return '快手';
            else if (row.settlementCalcType == 8) return '视频号';
            else if (row.settlementCalcType == 9) return '小红书';
            else return '';
        }
    },
    {
        istrue: true, prop: 'isPreceipt', label: '开启提前收款', width: '130', sortable: 'custom', formatter: (row) => {
            if (row.isPreceipt == null) return '';
            if (row.isPreceipt == true) return '是';
            else return '否';
        }
    },
    {
        istrue: true, prop: 'isFirstNew', label: '开启首单拉新', width: '130', sortable: 'custom', formatter: (row) => {
            if (row.isFirstNew == null) return '';
            if (row.isFirstNew == true) return '是';
            else return '否';
        }
    },
    { istrue: true, sortable: 'custom', prop: 'shopCreateTime', label: '店铺创建时间', width: '130', formatter: (row) => row.shopCreateTime },
    { istrue: true, sortable: 'custom', prop: 'timeOfAuthSelfOpenPlatform', label: '平台授权时间', width: '130', formatter: (row) => row.timeOfAuthSelfOpenPlatform },
    { istrue: true, type: 'button', btnList: [{ label: "编辑", handle: (that, row) => that.onEdit(row) }] },
    // {type:'button',btnList:[{label:"编辑",handle:(that,row)=>that.onEdit(row)},{label:"删除",handle:(that,row)=>that.onDelete(row),isDisabled:true}],},
];

const tableHandles1 = [
    //{label:"新增", handle:(that)=>that.onAdd()},
    // {label:'编辑', handle:(that)=>that.onEdit()}
];
export default {
    name: 'ShopList',
    components: { cesTable, MyContainer, MyConfirmButton,customerLogs,inputNumberYh },
    data() {
        return {
            that: this,
            filter: {
                name: null,
                shopManager: null,
                groupId: null,
                mainCategories: null,
                property: null,
                isAuthSelf: null,
                platformShopID: null,
                projName: null,
                shopStatusList: [],
                mainPhone: null,
                codeReceiver: null,
                xiFenPlatformList: [],
            },
            list: [],
            pager: { OrderBy: "id", IsAsc: false },
            tableCols: tableCols,
            tableHandles: tableHandles1,
            platformlist: platformlist,
            editStatusVisible: false,
            editStatusLoading: false,
            editStatusForm: {
                shopStatus: [],
                mainPhone: null,
                codeReceiver: null,
                shopId: null,
            },
            // companylist: companylist,
            autoform: {
                fApi: {},
                //options:{onSubmit:(formData)=>{alert(JSON.stringify(formData))}},
                options: { submitBtn: false, form: { labelWidth: '145px' }, global: { '*': { props: { disabled: false }, col: { span: 8 } } } },
                rule: [
                    // {type:'hidden',field:'id',title:'id',value: ''},
                    //     {type:'input',field:'shopCode',title:'店铺编码',validate: [{type: 'string', required: true, message:'请输入店铺编码'}]},
                    //     {type:'input',field:'shopName',title:'店铺名称',validate: [{type: 'string', required: true, message:'请输入店铺名称'}]},
                    //     {type:'input',field:'platformShopID',title:'平台店铺ID',validate: [{type: 'string', required: false, message:'请输入平台店铺ID'}]},
                    //     {type:'select',field:'groupId',title:'组长', value: '', ...await ruleDirectorGroup(), props:{filterable:true}},
                    //     {type:'select',field:'platform',title:'平台', validate: [{type: 'number', required: true, message:'请选择平台'}],value: 1,options: []},
                    //     {type:'select',field:'isOpen',title:'是否开启', validate: [{type: 'number', required: true, message:'请选择'}],value: 1,options: [{value:0, label:'否'},{value:1, label:'是'}]},
                    //     {type:'select',field:'isCalcSettlement',title:'是否参与月度计算', validate: [{type: 'boolean', required: true, message:'请选择'}],value: false,options: [{value:false, label:'否'},{value:true, label:'是'}]},
                    //     {type:'select',field:'settlementCalcType',title:'月度结算类型', value: null,options: [{value:null, label:'无'},{value:0, label:'普通天猫'},{value:1, label:'普通拼多多'},{value:2, label:'淘工厂菜鸟驿站'}]},
                    //     {type:'select',field:'isPreceipt',title:'是否开启提前收款', validate: [{type: 'boolean', required: true, message:'请选择'}],value: false,options: [{value:false, label:'否'},{value:true, label:'是'}]},
                    //     {type:'select',field:'isFirstNew',title:'是否开启首单拉新', validate: [{type: 'boolean', required: true, message:'请选择'}],value: false,options: [{value:false, label:'否'},{value:true, label:'是'}]},
                ]
            },
            total: 0,
            sels: [],
            projectList: [],//项目选项列表
            segmentationList: [],//细分平台选项列表
            listLoading: false,
            pageLoading: false,
            addFormVisible: false,
            addLoading: false,
            deleteLoading: false,
            formtitle: "新增",
            shopList: [],

            statusSelection,//状态
            isOpen: isOpens,//是否开启
            isCalcSettlement: isCalcSettlements,//是否参与月度计算
            settlementCalcType: settlementCalcTypes,//月度结算类型
            isPreceipt: isPreceipts,//是否开启提前收款
            isFirstNew: isFirstNews,//是否开启首单
            mainCategoriesList: mainCategoriesListConst,//公司类目
            propertyList: propertyListConst,//属性

            // dialogEditVisible: false,
            showAddCompany: false,//显示新增主体公司对话框
            editDialog: {
                shopCode: "",//店铺编码
                shopName: "",//店铺名称
                platformShopID: "",//平台店铺ID
                shopManager: "",//店铺负责人
                groupId: null,//运营组
                platform: null,//平台
                isOpen: null,//是否开启
                isCalcSettlement: null,//是否参与月度计算
                settlementCalcType: null,//月度结算类型
                isPreceipt: null,//是否开启提前收款
                isFirstNew: null,//是否开启首单
                company: null,//主体公司
                mainCategories: null,//类目
                property: null,//属性
                projName: null,//项目
                xiFenPlatform: null,//细分平台

                res: {},//编辑店铺信息进行提交
            },
            editDialog2: {
                addCompany: "",//新增主体公司
            },
            rule: {
                editDialog: {
                    shopCode: { required: true, message: '请输入店铺编码', trigger: 'blur' },
                    shopName: { required: true, message: '请输入店铺名称', trigger: 'blur' },
                    // platformShopID: { required: true, message: '请输入平台店铺ID', trigger: 'blur' },
                    // shopManager: { required: true, message: '请选择店铺负责人', trigger: 'blur' },
                    // groupId: { required: true, message: '请选择运营组', trigger: 'blur' },
                    platform: { required: true, message: '请选择平台', trigger: 'blur' },
                    isOpen: { required: true, message: '请选择是否开启', trigger: 'blur' },
                    isCalcSettlement: { required: true, message: '请选择是否参与月度计算', trigger: 'blur' },
                    // settlementCalcType: { required: true, message: '请选择月度结算类型', trigger: 'blur' },
                    isPreceipt: { required: true, message: '请选择是否开启提前收款', trigger: 'blur' },
                    isFirstNew: { required: true, message: '请选择是否开启首单', trigger: 'blur' },
                    company: { required: true, message: '请选择主体公司', trigger: 'blur' },
                    mainCategories: { required: true, message: '请选择类目', trigger: 'blur' },
                    property: { required: true, message: '请选择属性', trigger: 'blur' },
                },
                editDialog2: {
                    addCompany: { required: true, message: '请输入主体公司', trigger: 'change' },
                }

            },
            shopManagers: [],//店铺负责人采集器
            groupIds: [],//运营组采集器
            ddUser: null,//钉钉用户
            // addCompany: "",
            companylist: [],//主体公司选择器
            selectList: [],//选中的数据
            editPlatIdVisible: false,
            platformShopID: '',
            editShopEmailVisible: false,
            email: '',
            customerInfo:{
                platform:null,
                shopName:null,
                shopCode:null,
                groupType:null,
            },
            KFlogVisible:false,
            setJDBusinessCodeVisible: false,
            code:''
        }
    },
    async beforeCreate() {

    },
    async mounted() {
        this.filter.platformShopID = this.$route.query.ownerId;
        await this.init();
        await this.getformaddsinglerule();
        await this.getlist();


    },
    beforeUpdate() {

    },
    methods: {
        async JDsetCodeSubmit() {
            if (!this.code) return this.$message.error('请输入商家编码')
            const { success } = await UpdateShopCustomCode({ id: this.selectList[0].id, platform: this.selectList[0].platform, customerCode: this.code });
            if (!success) return
            this.$message.success('设置成功')
            this.selectList = [];
            this.setJDBusinessCodeVisible = false;
            this.getlist();
        },
        async setJDBusinessCode() {
            if (this.selectList.length == 0) return this.$message.error('请选择要设置的店铺');
            if (this.selectList.length > 1) return this.$message.error('只能选择一条数据');
            this.code = ''
            this.setJDBusinessCodeVisible = true;
        },
        openKFlog(row){
            this.customerInfo = {
                platform:row.platform,
                shopName:row.shopName,
                shopCode:row.shopCode,
            }
            this.KFlogVisible= true;
        },
        async editPlatSubmit() {
            if (!this.platformShopID) return this.$message.error('请输入平台店铺ID');
            const { data, success } = await updateShopPlatformId({ id: this.selectList[0].id, platformShopID: this.platformShopID, platform: this.selectList[0].platform });
            if (success) {
                this.$message.success('保存成功');
                this.editPlatIdVisible = false;
                this.getlist();
                this.selectList = [];
            }
        },
        editPlatId() {
            if (this.selectList.length == 0) return this.$message.error('请选择要修改的店铺');
            if (this.selectList.length > 1) return this.$message.error('只能选择一个店铺');
            this.platformShopID = null
            this.editPlatIdVisible = true;
            //天猫1、淘宝9、京东7、快手14   使用platformShopID2回显
            if (this.selectList[0].platform == 1 || this.selectList[0].platform == 9 || this.selectList[0].platform == 7 || this.selectList[0].platform == 14) {
                this.platformShopID = this.selectList[0].platformShopID2;
            } else {
                this.platformShopID = this.selectList[0].platformShopID;
            }

        },
        async editShopEmailSubmit() {
            if (!this.email) return this.$message.error('请输入店铺邮箱');
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if(!emailRegex.test(this.email)){
                return this.$message.error('请输入合法的邮箱');
            }
            let ids = [];
            this.selectList.forEach((item, index) => {
                ids.push(item.id);
            });
            const { data, success } = await updateShopEmail({ shopIds: ids, email: this.email });
            if (success) {
                this.$message.success('保存成功');
                this.editShopEmailVisible = false;
                this.getlist();
                this.selectList = [];
            }
        },
        editEmail() {
            if (this.selectList.length == 0) return this.$message.error('请选择要修改的店铺');
            this.email = '';
            this.selectList.forEach((item, index) => {
                if(item.email && !this.email){
                    this.email = item.email;
                }
            });
            let that = this;
            this.$nextTick(function (){
                that.editShopEmailVisible = true;
            });
        },
        editStatus(){
          if (this.selectList.length !== 1) {
            this.$message.error(this.selectList.length === 0 ? '请选择要修改的店铺' : '只能选择一个店铺');
            return;
          }
          let a = this.selectList[0];
          this.editStatusForm = {
            shopStatus: a.shopStatus?.split(',') || [],
            mainPhone: a.mainPhone || null,
            codeReceiver: a.codeReceiver || null,
            shopId: a.id,
          };
          this.editStatusVisible = true;
        },
        async editStatusSubmit(){
          this.editStatusLoading = true;
          const { data, success } = await updateShopStatus({...this.editStatusForm,shopStatus:this.editStatusForm.shopStatus?.join(',') || null});
          this.editStatusLoading = false;
          if (success) {
            this.$message.success(data || '保存成功');
            this.editStatusVisible = false;
            this.getlist();
            this.selectList = [];
          }
        },
        selectCheckBox(val) {
            this.selectList = val;
        },
        async init() {
            //店铺负责人
            var { data } = await getDirectorList();
            this.shopManagers = data?.map(item => { return { value: item.key, label: item.value }; });
            //运营组
            var { data } = await getDirectorGroupList();
            this.groupIds = data?.map(item => { return { value: Number(item.key), label: item.value }; });
            //主体公司
            var { data } = await getMainWarehouseList();
            this.companylist = data?.map(item => { return { value: item.id, label: item.mainWarehouse }; });
            //当前使用者
            var { data } = await getUserInfo();
            this.ddUser = data.nickName;
            const { data: data1, success: success1 } = await getProductProjectList({ projName: '' });
            if (success1) {
                this.projectList = data1;
                this.projectList = this.projectList.filter(item => item.projName == '大马美甲');
            }
            const { data: data2 } = await getAllShopXiFenPlatform();
            this.segmentationList = data2;
        },
        canclick() {
            let url = window.location.origin;
            let loginurl = process.env.VUE_APP_BASE_API_PddAccredit;
            this.$nextTick(() => {
                window.open(`${loginurl}/api/bladegateway/yunhan-gis-info/pdd/auth?backUri=${url}/operatemanage/base/shop`);
            });

        },
        async getformaddsinglerule() {
            this.autoform.rule = [
                { type: 'hidden', field: 'id', title: 'id', value: '' },
                { type: 'input', field: 'shopCode', title: '店铺编码', validate: [{ type: 'string', required: true, message: '请输入店铺编码' }], props: { disabled: true } },
                { type: 'input', field: 'shopName', title: '店铺名称', validate: [{ type: 'string', required: true, message: '请输入店铺名称' }], props: { disabled: true } },
                { type: 'input', field: 'platformShopID', title: '平台店铺ID', validate: [{ type: 'string', required: false, message: '请输入平台店铺ID' }], props: { disabled: true } },
                { type: 'select', field: 'shopManager', title: '店铺负责人', value: '', ...await ruleDirectorName(), props: { filterable: true, clearable: true } },
                { type: 'select', field: 'groupId', title: '运营组', value: '', ...await ruleDirectorGroup(), props: { filterable: true } },
                {
                    type: 'select', field: 'platform', title: '平台', value: null, ...await rulePlatform(), props: { filterable: true },
                    on: { change: (row) => { this.onplatformchange(row) } }
                },
                { type: 'select', field: 'isOpen', title: '是否开启', validate: [{ type: 'number', required: true, message: '请选择' }], value: 1, options: [{ value: 0, label: '否' }, { value: 1, label: '是' }] },
                { type: 'select', field: 'isCalcSettlement', title: '是否参与月度计算', validate: [{ type: 'boolean', required: true, message: '请选择' }], value: false, options: [{ value: false, label: '否' }, { value: true, label: '是' }] },
                {
                    type: 'select', field: 'settlementCalcType', title: '月度结算类型', value: null,
                    options: [
                        { value: null, label: '无' },
                        { value: 0, label: '普通天猫' },
                        { value: 1, label: '普通拼多多' },
                        { value: 2, label: '淘工厂菜鸟驿站' },
                        { value: 3, label: '头条抖音' },
                        { value: 4, label: '京东' },
                        { value: 5, label: '苏宁' },
                        { value: 6, label: '阿里巴巴' },
                        { value: 7, label: '快手' },
                        { value: 8, label: '视频号' },
                        { value: 9, label: '小红书' }
                    ]
                },
                { type: 'select', field: 'isPreceipt', title: '是否开启提前收款', validate: [{ type: 'boolean', required: true, message: '请选择' }], value: false, options: [{ value: false, label: '否' }, { value: true, label: '是' }] },
                { type: 'select', field: 'isFirstNew', title: '是否开启首单拉新', validate: [{ type: 'boolean', required: true, message: '请选择' }], value: false, options: [{ value: false, label: '否' }, { value: true, label: '是' }] },
                { type: 'select', field: 'company', title: '主体公司', validate: [{ type: 'number', required: true, message: '请选择' }], value: null, options: companylist },
                {
                    type: 'select', field: 'mainCategories', title: '公司类目', value: null,
                    options: this.mainCategoriesList, display: false, props: { filterable: true, clearable: true },
                    validate: [{ type: 'number', required: true, message: '请选择' }]
                },
                {
                    type: 'select', field: 'property', title: '属性', value: null,
                    options: this.propertyList, display: false, props: { filterable: true, clearable: true }
                },
            ];
        },
        onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        //获取数据列表
        async getlist() {
            var pager = this.$refs.pager.getPager()
            let shopStatusList = this.filter.shopStatusList?.join(',') || null;
            const params = { ...pager, ...this.pager, ... this.filter, shopStatusList }
            this.listLoading = true
            const res = await getList(params)

            this.listLoading = false
            if (!res?.success) return
            this.total = res.data?.total
            const data = res.data?.list
            data.forEach(d => {
                d._loading = false
            })
            this.list = data

            //编辑列表后，实时更新过滤条件中对应平台的店铺负责人列表
            this.getShopList();
        },
        async getShopList() {
            this.shopList = [];

            let platforms = [];
            if (this.filter.platform) {
                platforms.push(this.filter.platform);
            }

            const { data } = await GetAllShopManager();
            this.shopList = data.map((item) => ({ label: item, value: item }));

        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        //编辑按钮，点击显示下拉抽屉
        async onEdit(row) {
            this.formtitle = '编辑';
            this.addFormVisible = true;
            const res = await getbyid(row.id);
            this.editDialog = { ...res.data };

            // const plat = res.data.platform;
            // this.onplatformchange(plat);
        },
        onAdd() {
            this.formtitle = '新增';
            this.addFormVisible = true
            var arr = Object.keys(this.autoform.fApi);
            if (arr.length > 0)
                this.autoform.fApi.resetFields()

            // const plat = this.autoform.fApi.formData().platform;
            // this.onplatformchange(plat);
        },
        async onEditSubmit() {
            this.addFormVisible = true
            await onAddSubmit();
        },
        //提交编辑修改的数据
        async onAddSubmit() {
            this.addLoading = true;
            //因缺少部分字段，使用查询时返回的数据，修改数据后传递

            if (this.editDialog.company) {
                //主体公司
                for (let i = 0; i < this.companylist.length; i++) {//主体公司
                    if (this.editDialog.company === this.companylist[i].value) {
                        this.editDialog.companyName = this.companylist[i].label;
                        break;
                    }
                }
            }

            if (this.editDialog.platform === null || this.editDialog.platform === '') {
                this.addLoading = false;
                this.$message({
                    message: '请选择平台',
                    type: 'warning'
                })
                return;
            } else if (this.editDialog.isOpen === null || this.editDialog.isOpen === '') {
                this.addLoading = false;
                this.$message({
                    message: '请选择是否开启',
                    type: 'warning'
                })
                return;
            } else if (this.editDialog.isCalcSettlement === null || this.editDialog.isCalcSettlement === '') {
                this.addLoading = false;
                this.$message({
                    message: '请选择是否参与月度计算',
                    type: 'warning'
                })
                return;
            } else if (this.editDialog.isFirstNew === null || this.editDialog.isFirstNew === '') {
                this.addLoading = false;
                this.$message({
                    message: '请选择是否开启首单拉新',
                    type: 'warning'
                })
                return;
            } else if (this.editDialog.company === null || this.editDialog.company === '') {
                this.addLoading = false;
                this.$message({
                    message: '请选择主体公司',
                    type: 'warning'
                })
                return;
            } else if (this.editDialog.platform === 2 && this.editDialog.mainCategories === null || this.editDialog.mainCategories === '') {
                this.addLoading = false;
                this.$message({
                    message: '请选择公司类目',
                    type: 'warning'
                })
                return;
            } else if (this.editDialog.platform === 6 && this.editDialog.property === null || this.editDialog.property === '') {
                this.addLoading = false;
                this.$message({
                    message: '请选择属性',
                    type: 'warning'
                })
                return;
            }
            // const formData = this.editDialog;
            const formData = { ...this.editDialog };//因缺少部分字段，使用查询时返回的数据，修改数据后传递
            formData.id = formData.id ? formData.id : 0;
            formData.Enabled = true;
            const res = await addoredit(formData);
            if (res.code == 1) {
                this.getlist();
                this.addFormVisible = false;
                this.$message.success("提交成功！");
            }
            this.addLoading = false;
        },
        deleteValidate(row) {
            let isValid = true
            if (row && row.name === 'admin') {
                this.$message({
                    message: row.description + '，禁止删除！',
                    type: 'warning'
                })
                isValid = false
            }
            return isValid
        },
        async onDelete(row) {
            row._loading = true
            const res = await deletebyid(row.id)
            row._loading = false
            if (!res?.success) {
                return
            }
            this.$message({
                message: this.$t('admin.deleteOk'),
                type: 'success'
            })
            this.getlist()
        },
        selsChange: function (sels) {
            this.sels = sels
        },
        onplatformchange(plat) {
            if (plat == 2) {
                this.autoform.fApi.getRule('mainCategories').display = true;
                this.autoform.fApi.getRule('mainCategories').validate = [{ type: 'number', required: true, message: '请选择' }];
            }
            else {
                this.autoform.fApi.getRule('mainCategories').display = false;
                this.autoform.fApi.getRule('mainCategories').validate = [];
            }

            if (plat == 6) {
                this.autoform.fApi.getRule('property').display = true;
            }
            else {
                this.autoform.fApi.getRule('property').display = false;
            }
        },
        getMainCategoriesStr(k) {
            if (!k)
                return "";
            let v = this.mainCategoriesList.find(f => f.value == k)?.label;
            return v;
        },
        getPropertyStr(k) {
            if (!k)
                return "";
            let v = this.propertyList.find(f => f.value == k)?.label;
            return v;
        },
        //新增主体公司
        onAddCompany() {
            this.showAddCompany = true;
            this.$nextTick(() => {
                this.$refs['editDialog2'].resetFields();
            })
            // this.editDialog2.addCompany = "";
            // this.$refs['editDialog2'].clearValidate();
        },
        //取消新增主体公司
        onCancel() {
            this.showAddCompany = false;
            // this.$refs['editDialog2'].resetFields();
            // this.editDialog2.addCompany = "";
            // this.$refs.editDialog2.clearValidate();
            this.$nextTick(() => {
                this.$refs['editDialog2'].resetFields();
            })
        },
        //提交新增主体公司
        async addCompany() {
            // if (this.editDialog2.addCompany !== "") {
            const response = await addNewMainWarehouseList({ id: this.companylist.length + 11, mainWarehouse: this.editDialog2.addCompany });
            if (response.data === true) {
                const newItem = { value: this.companylist.length + 11, label: this.editDialog2.addCompany };
                this.companylist.push(newItem);
                this.$message.success("添加主体公司成功！");
                this.showAddCompany = false;
                // this.editDialog2.addCompany = "";
                // this.$refs.editDialog2.clearValidate();
                this.$nextTick(() => {
                    this.$refs['editDialog2'].resetFields();
                })
            }

            else if (request.data === false) {
                this.$message.error("添加主体公司失败！");
                this.editDialog2.addCompany = "";
            }
            // } else {
            //     this.$message.error("设置主体公司失败，主体公司不能为空！");
            // }
        }
    }
}
</script>
<style scoped lang="scss">
.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
::v-deep .el-select__tags-text {
  max-width: 40px;
}

.publicCss {
    width: 135px;
}

//隐藏label
.no-label {
  ::v-deep .el-form-item__label {
    display: none !important;
  }
}
</style>
