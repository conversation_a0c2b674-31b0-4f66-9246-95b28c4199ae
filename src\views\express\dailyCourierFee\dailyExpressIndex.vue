<template>
  <my-container>
    <el-tabs v-model="activeName" style="height: 95%">
      <el-tab-pane label="日账单" name="first1" style="height: 99%">
        <dailyBills ref="refdailyBills" />
      </el-tab-pane>
      <el-tab-pane label="账单明细" name="first2" style="height: 99%" lazy>
        <billingDetails ref="refbillingDetails" />
      </el-tab-pane>
      <el-tab-pane label="账单复核" name="first3" style="height: 99%" lazy>
        <billReview ref="refbillReview" />
      </el-tab-pane>
      <el-tab-pane label="日账单汇总" name="first4" style="height: 99%" lazy>
        <dailyBillSummary ref="refdailyBillSummary" />
      </el-tab-pane>
      <el-tab-pane label="月账单汇总" name="first41" style="height: 99%" lazy>
        <monthBillSummary ref="refmonthBillSummary" />
      </el-tab-pane>
      <!-- <el-tab-pane label="理赔" name="first5" style="height: 99%" lazy>
        <settlementClaims ref="refsettlementClaims" />
      </el-tab-pane>
      <el-tab-pane label="平台扣款" name="first6" style="height: 99%" lazy>
        <platformDeductions ref="refplatformDeductions" />
      </el-tab-pane> -->
      <el-tab-pane label="账单充值" name="first8" style="height: 99%" lazy>
        <bankMaintenance ref="refbankMaintenance" />
      </el-tab-pane>
      <el-tab-pane label="充值记录" name="first7" style="height: 99%" lazy>
        <billRecharge ref="refbillRecharge" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import dailyBills from "./components/dailyBills.vue";
import billingDetails from "./components/billingDetails.vue";
import billReview from "./components/billReview.vue";
import dailyBillSummary from "./components/dailyBillSummary.vue";
import monthBillSummary from "./components/monthBillSummary.vue";
import settlementClaims from "./components/settlementClaims.vue";
import platformDeductions from "./components/platformDeductions.vue";
import bankMaintenance from "./components/bankMaintenance.vue";
import billRecharge from "./components/billRecharge.vue";
export default {
  name: "dailyExpressIndex",
  components: {
    MyContainer, dailyBills, billingDetails, billReview, dailyBillSummary, settlementClaims, platformDeductions, bankMaintenance, billRecharge,monthBillSummary
  },
  data() {
    return {
      activeName: "first1",
    };
  },
  methods: {},
};
</script>

<style lang="scss" scoped></style>
