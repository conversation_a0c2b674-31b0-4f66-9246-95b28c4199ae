<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="时间:">
                    <el-date-picker style="width: 110px" v-model="filter.yearMonth" type="month" format="yyyyMM"
                        value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
                </el-form-item>
                <el-form-item label="平台:">
                    <el-select filterable v-model="filter.platform" placeholder="请选择平台" @change="onchangeplatform"
                        clearable style="width: 130px">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="所属店铺:" label-position="right">
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="请选择"
                        class="el-select-content" style="width:300px">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="版本:">
                    <el-select filterable clearable v-model="filter.version" placeholder="请选择">
                        <el-option label="工资月报" value="v1" key="v1"></el-option>
                        <el-option label="参考月报" value="v2" key="v2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>

                    <el-button type="primary" @click="onComputePlatform('v1')">计算平台工资月报</el-button>
                    <el-button type="primary" @click="onComputePlatform('v2')">计算平台参考月报</el-button>

                    <el-button type="primary" @click="onComputeOtherShow">计算其他费用</el-button>

                </el-form-item>
            </el-form>
        </template>

        <el-tabs v-model="activeName" style="height: 94%;">
            <el-tab-pane label="已上传" name="first" style="height: 100%;">
                <uploaded :filter="filter" ref="uploaded" />
            </el-tab-pane>
            <!-- <el-tab-pane label="未上传" name="second" style="height: 100%;">
      <noupload :filter="filter" ref="noupload" />
    </el-tab-pane> -->
            <el-tab-pane label="工资月报计算" name="third" style="height: 100%;">
                <compute :filter="filter" version="v1" ref="computev1" />
            </el-tab-pane>
            <el-tab-pane label="参考月报计算" name="forth" style="height: 100%;">
                <compute :filter="filter" version="v2" ref="computev2" />
            </el-tab-pane>
            <el-tab-pane label="缺失ID" name="loseprocode" style="height: 100%;">
                <loseprocode :filter="filter" ref="loseprocode" />
            </el-tab-pane>
            <el-tab-pane label="月报仓库薪资" name="cangkuxinzi" style="height: 100%;">
                <cangkuxinzi :filter="filter" ref="cangkuxinzi" />
            </el-tab-pane>
            <el-tab-pane label="包装费" name="packingFee" style="height: 100%;">
                <packingFee :filter="filter" ref="packingFee" />
            </el-tab-pane>
            <el-tab-pane label="平台计算结果" name="platformCalculationResults" style="height: 100%;">
                <platformCalculationResults :filter="filter" ref="platformCalculationResults"/>
            </el-tab-pane>
            <el-tab-pane label="仓库查询" name="packageAndOutwarehousefeeWarehouseMain" style="height: 100%;">
                <packageAndOutwarehousefeeWarehouseMain :filter="filter" ref="packageAndOutwarehousefeeWarehouseMain"/>
            </el-tab-pane>
            <el-tab-pane label="ID+仓库查询" name="packageAndOutwarehousefeeProcode" style="height: 100%;">
                <packageAndOutwarehousefeeProcode :filter="filter" ref="packageAndOutwarehousefeeProcode"/>
            </el-tab-pane>
            <el-tab-pane label="系列编码+仓库查询" name="packageAndOutwarehousefeeStylecode" style="height: 100%;">
                <packageAndOutwarehousefeeStylecode :filter="filter" ref="packageAndOutwarehousefeeStylecode"/>
            </el-tab-pane>
            <el-tab-pane label="明细查询" name="baozhuangMonth" style="height: 100%;">
                <baozhuangMonth :filter="filter" ref="baozhuangMonth"/>
            </el-tab-pane>
        </el-tabs>




        <el-dialog title="计算其他费用" :visible.sync="dialogdata.dialogVisible" width="40%" v-dialogDrag
            :close-on-click-modal="false">
            <span>
                <el-select filterable v-model="dialogdata.version" placeholder="版本" clearable style="width: 100px">
                    <el-option label="工资月报" value="v1" />
                    <el-option label="参考月报" value="v2" />
                </el-select>
            </span>
            <span slot="footer" class="dialog-footer">
              
                <div>
                 <el-button type="primary" @click="onComputeOtherSave(5)">计算出仓成本</el-button>   <el-button type="primary" @click="onComputeOtherSave(1)">计算销退仓返还/产品运费/采购运费/未计算数据</el-button>
                </div>
                <div style="padding-top: 20px;">
                    <div style="color:red;font-size: 12px;">注意：以下费用需要在快递费/外仓快递费计算完成并核算无误后再点击</div>
                    <el-button type="primary" @click="onComputeOtherSave(2)">计算包装费/外仓包装费</el-button>
                    <el-button type="primary" @click="onComputeOtherSave(3)">计算出仓成本（运）</el-button>
                    <el-button type="primary" @click="onComputeOtherSave(4)">计算月报仓库薪资</el-button>
                    <el-button type="primary" @click="onComputeOtherSave(6)">提取月报包装、出仓、仓库薪资</el-button>
                    <el-button @click="dialogdata.dialogVisible = false">关闭</el-button>
                </div>

            </span>
        </el-dialog>


    </container>
</template>

<script>
import { getAllListInCalc as getAllShopList, getList as getshopList } from '@/api/operatemanage/base/shop';
import { platformlist } from '@/utils/tools'
import uploaded from '@/views/bookkeeper/calccontrol/uploaded'
import noupload from '@/views/bookkeeper/calccontrol/noupload'
import compute from '@/views/bookkeeper/calccontrol/compute'
import loseprocode from '@/views/bookkeeper/calccontrol/loseprocode'
import cangkuxinzi from '@/views/bookkeeper/calccontrol/cangkuxinzi'
import packingFee from '@/views/bookkeeper/calccontrol/packingFee'
import platformCalculationResults from '@/views/bookkeeper/calccontrol/platformCalculationResults'
import packageAndOutwarehousefeeWarehouseMain from '@/views/bookkeeper/calccontrol/packageAndOutwarehousefeeWarehouseMain'
import packageAndOutwarehousefeeProcode from '@/views/bookkeeper/calccontrol/packageAndOutwarehousefeeProcode'
import packageAndOutwarehousefeeStylecode from '@/views/bookkeeper/calccontrol/packageAndOutwarehousefeeStylecode'
import baozhuangMonth from '@/views/bookkeeper/calccontrol/baozhuangMonth'
import container from '@/components/my-container/nofooter'
import { postCalcTaskPlatform, computeOtherSave } from '@/api/monthbookkeeper/financialDetail'
export default {
    name: 'Roles',
    components: { container, uploaded, noupload, compute, loseprocode, cangkuxinzi, packingFee, platformCalculationResults, packageAndOutwarehousefeeWarehouseMain, 
        packageAndOutwarehousefeeProcode, packageAndOutwarehousefeeStylecode, baozhuangMonth
     },
    data() {
        return {
            activeName: 'first',
            filter: {
                yearMonth: null,
                shopCode: null,
                version: null
            },
            platformlist: platformlist,
            pageLoading: false,
            dialogVisible: false,
            shopList: [],

            dialogdata: {
                dialogVisible: false,
                version: null,
            },
        }
    },
    mounted() { },
    beforeUpdate() {
        console.log('update')
    },
    async mounted() {
        this.platformlist.push({ label: '其他费用', value: 111 });
        // await this.getShopList();
    },
    methods: {
        async onchangeplatform(val) {
            this.filter.shopCode = null;
            this.shopList = [];
            const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
        },
        onSearch() {
            console.log(this.activeName);
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            if (this.filter.timerange1) {
                this.filter.startImpotTime = this.filter.timerange1[0];
                this.filter.endImpotTime = this.filter.timerange1[1];
            }
            if (this.activeName == 'first') this.$refs.uploaded.onSearch();
            else if (this.activeName == 'second') this.$refs.noupload.onSearch();
            else if (this.activeName == 'third') this.$refs.computev1.onSearch();
            else if (this.activeName == 'forth') this.$refs.computev2.onSearch();
            else if (this.activeName == 'loseprocode') this.$refs.loseprocode.onSearch();
            else if (this.activeName == 'cangkuxinzi') this.$refs.cangkuxinzi.onSearch();
            else if (this.activeName == 'packingFee') this.$refs.packingFee.onSearch();
            else if (this.activeName == 'platformCalculationResults') this.$refs.platformCalculationResults.onSearch();
            else if (this.activeName == 'packageAndOutwarehousefeeWarehouseMain') this.$refs.packageAndOutwarehousefeeWarehouseMain.onSearch();
            else if (this.activeName == 'packageAndOutwarehousefeeProcode') this.$refs.packageAndOutwarehousefeeProcode.onSearch();
            else if (this.activeName == 'packageAndOutwarehousefeeStylecode') this.$refs.packageAndOutwarehousefeeStylecode.onSearch();
            else if (this.activeName == 'baozhuangMonth') this.$refs.baozhuangMonth.onSearch();
        },
        async onComputePlatform(versionstr) {
            let para = {
                yearMonth: this.filter.yearMonth,
                platform: this.filter.platform,
                version: versionstr
            };
            var me = this;
            this.$confirm('确定要计算平台的所有店铺吗', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                let res = await postCalcTaskPlatform(para);
                if (res.success) {
                    me.$message({ type: 'success', message: '任务创建完成，请稍后查询计算状态...!' });
                    //await me.getList();
                }
            }).catch(() => {
                //this.$message({ type: 'info', message: '已取消计算' });
            });
        },
        onComputeOtherShow() {
            if (!this.filter.yearMonth) {
                this.$message({ type: 'error', message: '请选择月份' });
                return;
            }
            this.dialogdata.dialogVisible = true;
        },
        onComputeOtherSave(type) {
            if (!this.filter.yearMonth) {
                this.$message({ type: 'error', message: '请选择月份' });
                return;
            }
            if (!this.dialogdata.version) {
                this.$message({ type: 'error', message: '请选择版本' });
                return;
            }

            let me = this;
            this.$confirm('确定要开始计算吗', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                let res = await computeOtherSave({
                    yearmonth: this.filter.yearMonth,
                    version: this.dialogdata.version,
                    type: type,
                });
                if (res.success) {
                    me.$message({ type: 'success', message: '正在后台计算中...!' });
                    this.dialogdata.dialogVisible = false;
                }
            }).catch(() => {
                //this.$message({ type: 'info', message: '已取消计算' });
            });

        },
    }
}
</script>
