<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                <el-button-group style="border:none;">
                    
                    <el-button style="padding: 0;margin: 0;border:none;">
                        时间范围
                        <el-date-picker style="width:200px;margin-right:5px;" :clearable="false"
                            v-model="Filter.gDate"
                            type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                            :picker-options="pickerOptions">
                        </el-date-picker>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none;">
                        阶段1:
                        <el-date-picker style="width:200px;margin-right:5px;" :clearable="false"
                            v-model="Filter.gDate1"
                            type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                            :picker-options="pickerOptions">
                        </el-date-picker>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none;">
                        阶段2:
                        <el-date-picker style="width:200px;margin-right:5px;" :clearable="false"
                            v-model="Filter.gDate2"
                            type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                            :picker-options="pickerOptions">
                        </el-date-picker>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none;">
                        阶段3:
                        <el-date-picker style="width:200px;margin-right:5px;" :clearable="false"
                            v-model="Filter.gDate3"
                            type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                            :picker-options="pickerOptions">
                        </el-date-picker>
                    </el-button>

                    
                    <el-button style="padding: 0;margin: 0;border:none;">
                        阶段4:
                        <el-date-picker style="width:200px;margin-right:5px;" :clearable="false"
                            v-model="Filter.gDate4"
                            type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                            :picker-options="pickerOptions">
                        </el-date-picker>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none;padding-top: 3px;padding-bottom: 3px;">
                        <el-switch 
                            v-model="Filter.notShowLeaveB" active-text="不含离职"
                            active-color="#13ce66"
                            inactive-color="#ff4949">
                            </el-switch>
                    </el-button>

                    <el-button style="padding: 0;border: none;" >
                        <el-select filterable v-model="Filter.platforms" placeholder="平台" multiple collapse-tags
                       clearable style="width: 170px">
                            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none;">
                        <el-input v-model.trim="Filter.keywords" clearable placeholder="关键字查询" style="width:100px;"  :maxlength="10">
                            <el-tooltip slot="suffix"   effect="dark" content="仅支持勾选的分组及人员信息" placement="bottom">
                                <i class="el-input__icon el-icon-question"></i>
                            </el-tooltip>
                        </el-input>
                    </el-button>


                   
                    <el-button type="primary" @click="onSearch"  >查询</el-button>
                   
                    <el-button type="primary" @click="exportProps">导出</el-button>              

                </el-button-group>

                <el-button style="padding: 0;margin: 0;border:none;">
                        <el-checkbox-group v-model="groupProps" @change="groupPropsChange">
                            <!-- "yyGDeptName3","yyGroupName","yyGUserName","YyDept2","YyDeptRegion","YyUserName" -->
                            <el-checkbox label="yyGDeptName3">团队</el-checkbox>
                            <el-checkbox label="yyGroupName">小组</el-checkbox>
                            <el-checkbox label="yyGUserName">组长</el-checkbox>
                            <el-checkbox label="YyDept2">人员团队</el-checkbox>
                            <el-checkbox label="YyDeptRegion" >区域</el-checkbox>
                            <el-checkbox label="YyUserName" >人员</el-checkbox>
                            <el-checkbox label="Platform" >平台</el-checkbox>
                            

                        </el-checkbox-group>
                    </el-button>
            </el-form>
        </template>

         <vxetablebase :id="'TeamRptSum202505121954'" :border="true"
            ref="xTable" :showsummary="true"
            :summaryarry="summaryarry"
            v-if="tbdatalist.length>0"
            :that='that' :loading="listLoading"
            :tableData='tbdatalist' :tableCols='calcTableCols'
            @sortchange='sortchange'
            >
           
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettbdatalist1" />
        </template>


    </my-container>
</template>
<script>

    import {
        PageTeamBkRptList,ExportTeamBkRptList
        
    } from "@/api/bookkeeper/styleCodeRptData";

    import dayjs from "dayjs";

    import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";

    import { formatmoney, formatPercen, getUrlParam, platformlist, formatPlatform, formatTime, setStore, getStore, formatLinkProCode,formatNoLink } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";


    const tableCols = [
        { istrue: true, prop: 'yyGDeptName3', label: '团队',minwidth:'70', sortable: 'custom' ,fixed:'left'},
        { istrue: true, prop: 'yyGroupName', label: '小组',minwidth:'70', sortable: 'custom' ,fixed:'left'},
        { istrue: true, prop: 'yyGUserName', label: '组长',minwidth:'70', sortable: 'custom' ,fixed:'left'},
        { istrue: true, prop: 'yyDept2', label: '人员团队',minwidth:'70', sortable: 'custom' ,fixed:'left'},
        { istrue: true, prop: 'yyDeptRegion', label: '人员区域',minwidth:'70', sortable: 'custom' ,fixed:'left'},
        { istrue: true, prop: 'yyUserName', label: '人员',minwidth:'70', sortable: 'custom' ,fixed:'left'},
        { istrue: true, prop: 'onDays', label: '在职天数',minwidth:'70', sortable: 'custom' },
        { istrue: true, prop: 'groupCount', label: '小组数',minwidth:'70', sortable: 'custom' },
        { istrue: true, prop: 'yyUserCount', label: '人员数',minwidth:'70', sortable: 'custom' },
        { istrue: true, prop: 'platformCount', label: '平台数',minwidth:'70', sortable: 'custom' },
        { istrue: true, prop: 'platformStr', label: '平台',minwidth:'70', sortable: 'custom' ,formatter:(row)=>row.platformNameStr} ,
        { istrue: true, prop: 'orderCount', label: '订单数',minwidth:'80',align:'right', sortable: 'custom' },
        { istrue: true, prop: 'saleAmont_0', label: '销售额',minwidth:'80',align:'right', sortable: 'custom' },
        { istrue: true, prop: 'profit1_0', label: '毛1',minwidth:'80',align:'right', sortable: 'custom' },
        { istrue: true, prop: 'profit4_0', label: '毛4',minwidth:'80',align:'right', sortable: 'custom' },
        { istrue: true, prop: 'profit4_0Rate', label: '毛4率',minwidth:'80',align:'right', sortable: 'custom',formatter:(row)=>row.profit4_0Rate+' %'  },
        { istrue: true, prop: 'profit6_0', label: '毛6',minwidth:'80',align:'right', sortable: 'custom'  },
        { istrue: true, prop: 'profit6_0Rate', label: '毛6率',minwidth:'80',align:'right', sortable: 'custom',
            type:'html',
            formatter:(row)=>'<span style="'+(row.profit6_0Rate>=10 ? "color:red": row.profit6_0Rate<=6  ? "color:limegreen": "")+'">'+row.profit6_0Rate+' %'+'</span>'        
         },

         {
            istrue: true,  rop: '', label: `4阶段`, merge: true, prop: 'mergeField', width: '70',
            cols: [
                { istrue: true, prop: 'saleAmont_4', label: '销售额',minwidth:'80',align:'right', sortable: 'custom' },
                { istrue: true, prop: 'profit1_4', label: '毛1',minwidth:'80',align:'right', sortable: 'custom' },
                { istrue: true, prop: 'profit4_4', label: '毛4',minwidth:'80',align:'right', sortable: 'custom' },
                { istrue: true, prop: 'profit4_4Rate', label: '毛4率',minwidth:'80',align:'right', sortable: 'custom' ,formatter:(row)=>row.profit4_4Rate+' %' },
                { istrue: true, prop: 'profit6_4', label: '毛6',minwidth:'80',align:'right', sortable: 'custom' },
                { istrue: true, prop: 'profit6_4Rate', label: '毛6率',minwidth:'80',align:'right', sortable: 'custom' ,
                    type:'html',
                    formatter:(row)=>'<span style="'+(row.profit6_4Rate>=10 ? "color:red": row.profit6_4Rate<=6  ? "color:limegreen": "")+'">'+row.profit6_4Rate+' %'+'</span>' 
                 },
            ]
        },       

        {
            istrue: true,  rop: '', label: `3阶段`, merge: true, prop: 'mergeField', width: '70',
            cols: [
                { istrue: true, prop: 'saleAmont_3', label: '销售额',minwidth:'80',align:'right', sortable: 'custom' },
                { istrue: true, prop: 'profit1_3', label: '毛1',minwidth:'80',align:'right', sortable: 'custom' },
                { istrue: true, prop: 'profit4_3', label: '毛4',minwidth:'80',align:'right', sortable: 'custom' },
                { istrue: true, prop: 'profit4_3Rate', label: '毛4率',minwidth:'80',align:'right', sortable: 'custom',formatter:(row)=>row.profit4_3Rate+' %'  },
                { istrue: true, prop: 'profit6_3', label: '毛6',minwidth:'80',align:'right', sortable: 'custom' },
                { istrue: true, prop: 'profit6_3Rate', label: '毛6率',minwidth:'80',align:'right', sortable: 'custom' ,
                    type:'html',
                    formatter:(row)=>'<span style="'+(row.profit6_3Rate>=10 ? "color:red": row.profit6_3Rate<=6  ? "color:limegreen": "")+'">'+row.profit6_3Rate+' %'+'</span>' 
                },
            ]
        },

        {
            istrue: true,  rop: '', label: `2阶段`, merge: true, prop: 'mergeField', width: '70',
            cols: [
                { istrue: true, prop: 'saleAmont_2', label: '销售额',minwidth:'80',align:'right', sortable: 'custom' },
                { istrue: true, prop: 'profit1_2', label: '毛1',minwidth:'80',align:'right', sortable: 'custom' },
                { istrue: true, prop: 'profit4_2', label: '毛4',minwidth:'80',align:'right', sortable: 'custom' },
                { istrue: true, prop: 'profit4_2Rate', label: '毛4率',minwidth:'80',align:'right', sortable: 'custom',formatter:(row)=>row.profit4_2Rate+' %'  },
                { istrue: true, prop: 'profit6_2', label: '毛6',minwidth:'80',align:'right', sortable: 'custom' },
                { istrue: true, prop: 'profit6_2Rate', label: '毛6率',minwidth:'80',align:'right', sortable: 'custom' ,
                    type:'html',
                    formatter:(row)=>'<span style="'+(row.profit6_2Rate>=10 ? "color:red": row.profit6_2Rate<=6  ? "color:limegreen": "")+'">'+row.profit6_2Rate+' %'+'</span>' 
                },
            ]
        },

        {
            istrue: true,  rop: '', label: `1阶段`, merge: true, prop: 'mergeField', width: '70',
            cols: [
                { istrue: true, prop: 'saleAmont_1', label: '销售额',minwidth:'80',align:'right', sortable: 'custom' },
                { istrue: true, prop: 'profit1_1', label: '毛1',minwidth:'80',align:'right', sortable: 'custom' },
                { istrue: true, prop: 'profit4_1', label: '毛4',minwidth:'80',align:'right', sortable: 'custom' },
                { istrue: true, prop: 'profit4_1Rate', label: '毛4率',minwidth:'80',align:'right', sortable: 'custom',formatter:(row)=>row.profit4_1Rate+' %'  },
                { istrue: true, prop: 'profit6_1', label: '毛6',minwidth:'80',align:'right', sortable: 'custom' },
                { istrue: true, prop: 'profit6_1Rate', label: '毛6率',minwidth:'80',align:'right', sortable: 'custom',
                    type:'html',
                    formatter:(row)=>'<span style="'+(row.profit6_1Rate>=10 ? "color:red": row.profit6_1Rate<=6  ? "color:limegreen": "")+'">'+row.profit6_1Rate+' %'+'</span>'  
                 },
            ]
        },

        { istrue: true, prop: 'title', label: '岗位',minwidth:'70', sortable: 'custom' },
        { istrue: true, prop: 'hireDate', label: '入职时间', width: '80',align:'right', sortable: 'custom' , formatter: (row) => formatTime(row.hireDate, "YYYY-MM-DD")},
        { istrue: true, prop: 'leaveDate', label: '离职时间', width: '80',align:'right', sortable: 'custom', formatter: (row) => formatTime(row.leaveDate, "YYYY-MM-DD") },

    ];


    const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
    const endTime = formatTime(new Date(), "YYYY-MM-DD");
    const star = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");

    export default {
        name: "TeamRptSum",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, vxetablebase },
        data() {
            return {
                that: this,
                secr:'',
                platformlist:platformlist,
                Filter: {                 
                    keywords:'',
                    notShowLeave:false,
                    platforms:[],
                    gDate: [
                        formatTime(dayjs().subtract(4, "month"), "YYYY-MM-01"),
                        formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                    ],
                    gDate1: [
                        formatTime(dayjs().subtract(4, "month"), "YYYY-MM-01"),
                        formatTime(dayjs().subtract(3, "month"), "YYYY-MM-01"),
                    ],
                    gDate2: [
                        formatTime(dayjs().subtract(3, "month"), "YYYY-MM-01"),
                        formatTime(dayjs().subtract(2, "month"), "YYYY-MM-01"),
                    ],
                    gDate3: [
                        formatTime(dayjs().subtract(2, "month"), "YYYY-MM-01"),
                        formatTime(dayjs().subtract(1, "month"), "YYYY-MM-01"),
                    ],
                    gDate4: [
                        formatTime(dayjs().subtract(1, "month"), "YYYY-MM-01"),
                        formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                    ],

                },
                lastFilter:{},
                summaryarry:{},
                groupProps:["yyGDeptName3"], //,"yyGroupName","yyGUserName","YyDept2","YyDeptRegion","YyUserName"              

                tbdatalist: [],
                tableCols: tableCols,
                total: 0,
                pager: { OrderBy: "", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,

                curRow: {},

                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一月',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三月',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近半年',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 182);
                        picker.$emit('pick', [start, end]);
                        }
                    }]
                },

            };
        },
        computed:{
            calcTableCols(){
                var rltCols=this.tableCols.filter(x=> {                   

                    if("YyUserCount,PlatformCount,PlatformStr,OrderCount,SaleAmont_0,Profit1_0,Profit4_0,Profit4_0Rate,Profit6_0,Profit6_0Rate,SaleAmont_1,Profit4_1,Profit4_1Rate,Profit6_1,Profit6_1Rate,SaleAmont_2,Profit4_2,Profit4_2Rate,Profit6_2,Profit6_2Rate,SaleAmont_3,Profit4_3,Profit4_3Rate,Profit6_3,Profit6_3Rate,SaleAmont_4,Profit4_4,Profit4_4Rate,Profit6_4,Profit6_4Rate".toUpperCase().indexOf(x.prop.toUpperCase())>=0)
                    {
                        return true;
                    }

                    let tempPByUser=this.groupProps.find(y=> y.toUpperCase()== "YyUserName".toUpperCase());

                    if(x.prop.toUpperCase()=="GroupCount".toUpperCase()){                       
                        if(tempPByUser )
                            return false;
                        else
                            return true;
                    }
                   
                    if(this.groupProps!=null && this.groupProps.length>0){
                        let tempP=this.groupProps.find(y=> y.toUpperCase()== x.prop.toUpperCase());
                        if(tempP )
                            return true;
                    }

                    if(tempPByUser){
                        if("OnDays,HireDate,LeaveDate,Title".toUpperCase().indexOf( x.prop.toUpperCase())>=0)
                            return true;
                        else 
                            return false;   
                    }                    

                    if(x.prop=="mergeField")
                        return true;

                    return false;
                });

                console.log(rltCols);
                return rltCols;
            }
        },
        async mounted() {
            // let fds=await GetPageReqLogFilters();
            // if(fds && fds.success){
            //     this.appTypes=fds.data.appType;
            //     this.moduleNames=fds.data.moduleName;
            //     this.pageNames=fds.data.pageName;
            //     this.pageTabs=fds.data.pageTab;
            //     this.actionNames=fds.data.actionName;
            //     this.dept1s=fds.data.dept1;
            //     this.dept2s=fds.data.dept2;
            //     this.dept3s=fds.data.dept3;
            //     this.dept4s=fds.data.dept4;

            //     this.xqDept1s = fds.data.xqDept1;
            //     this.xqDept2s = fds.data.xqDept2;
            // }

            //this.onSearch();
        },
        methods: {
          
            //导出
            async exportProps() {
                let StageTimes=[];
                if (this.Filter.gDate) {
                    this.Filter.startDate = this.Filter.gDate[0];
                    this.Filter.endDate = this.Filter.gDate[1];
                }
                else {
                    this.$message.error("请选择时间范围");
                    return;
                }


                if (this.Filter.gDate1) {
                    StageTimes.push( this.Filter.gDate1[0]);
                    StageTimes.push( this.Filter.gDate1[1]);
                }
                else {
                    this.$message.error("请选择阶段1时间范围");
                    return;
                }
                if (this.Filter.gDate2) {
                    StageTimes.push( this.Filter.gDate2[0]);
                    StageTimes.push( this.Filter.gDate2[1]);
                }
                else {
                    this.$message.error("请选择阶段2时间范围");
                    return;
                }
                if (this.Filter.gDate3) {
                    StageTimes.push( this.Filter.gDate3[0]);
                    StageTimes.push( this.Filter.gDate3[1]);
                }
                else {
                    this.$message.error("请选择阶段3时间范围");
                    return;
                }
                if (this.Filter.gDate4) {
                    StageTimes.push( this.Filter.gDate4[0]);
                    StageTimes.push( this.Filter.gDate4[1]);
                }
                else {
                    this.$message.error("请选择阶段4时间范围");
                    return;
                }



                const para = { ...this.Filter };
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...para,
                };

                params.groupTypes=[...this.groupProps];
                params.notShowLeave=this.Filter.notShowLeaveB?1:0;
                params.stageTimes=StageTimes;

                this.lastFilter={...para};

                await ExportTeamBkRptList(params).then(({ data }) => {
                    if (data) {
                        const aLink = document.createElement("a");
                        let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                        aLink.href = URL.createObjectURL(blob)
                        aLink.setAttribute('download', '团队业绩导出_' + new Date().toLocaleString() + '.xlsx')
                        aLink.click()
                        this.isExport = false
                    }
                }).catch(() => {
                
                })
          
            //   const res = await ExportTeamBkRptList(params)
          
            //   const aLink = document.createElement("a");
            //   let blob = new Blob([res], { type: "application/vnd.ms-excel" })
            //   aLink.href = URL.createObjectURL(blob)
            //   aLink.setAttribute('download', '团队业绩导出_' + new Date().toLocaleString() + '.xlsx');
            //   aLink.click()
            },
            groupPropsChange(){
                this.total = 0;
                this.tbdatalist = [];
                this.onSearch();
                this.$nextTick(()=>{
                    this.$refs["xTable"].$refs.xTable.resetColumn(true);
                    //this.$refs["xTable"].$refs.xTable.recalculate();
                });
            }  ,
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderField = column.prop;

                    this.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                }
                this.onSearch();
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.gettbdatalist1();
            },
            async gettbdatalist1() {
                let StageTimes=[];
                if (this.Filter.gDate) {
                    this.Filter.startDate = this.Filter.gDate[0];
                    this.Filter.endDate = this.Filter.gDate[1];
                }
                else {
                    this.$message.error("请选择时间范围");
                    return;
                }


                if (this.Filter.gDate1) {
                    StageTimes.push( this.Filter.gDate1[0]);
                    StageTimes.push( this.Filter.gDate1[1]);
                }
                else {
                    this.$message.error("请选择阶段1时间范围");
                    return;
                }
                if (this.Filter.gDate2) {
                    StageTimes.push( this.Filter.gDate2[0]);
                    StageTimes.push( this.Filter.gDate2[1]);
                }
                else {
                    this.$message.error("请选择阶段2时间范围");
                    return;
                }
                if (this.Filter.gDate3) {
                    StageTimes.push( this.Filter.gDate3[0]);
                    StageTimes.push( this.Filter.gDate3[1]);
                }
                else {
                    this.$message.error("请选择阶段3时间范围");
                    return;
                }
                if (this.Filter.gDate4) {
                    StageTimes.push( this.Filter.gDate4[0]);
                    StageTimes.push( this.Filter.gDate4[1]);
                }
                else {
                    this.$message.error("请选择阶段4时间范围");
                    return;
                }



                const para = { ...this.Filter };
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...para,
                };

                params.groupTypes=[...this.groupProps];
                params.notShowLeave=this.Filter.notShowLeaveB?1:0;
                params.stageTimes=StageTimes;

                this.lastFilter={...para};

                this.pageLoading = true;
                const res = await PageTeamBkRptList(params);

                this.pageLoading = false;

                this.total = res.data.total
                this.tbdatalist = res.data.list;
                this.summaryarry=res.data.summary;

            },
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>

