<template>
  <MyContainer style="height: 98%;">
    <template #header>
      <div class="top">
        <span style="margin-left: 1%;">省份/城市： </span>
        <div class="publicCss">
          <el-cascader :options="options" :props="props" collapse-tags clearable style="width: 350px;"
            v-model="ListInfo.provinceCity" @change="handleCasChange"></el-cascader>
        </div>
        <el-button type="primary" @click="getList('search')">查询</el-button>
        <div class="center-content">
          <span>商品子编码：</span>
          <span>{{ ListInfo.skuCode }}</span>
        </div>
      </div>
    </template>
    <template #default>
      <el-scrollbar style="height: 100%;width: 100%;" v-loading="loading" v-if="dataList.length > 0">
        <div style="display: flex; flex-wrap: wrap; height: 100%;padding: 0 1%;">
          <div v-for="(item, index) in dataList" :key="index" v-if="item.list"
            style="display: flex; justify-content: space-between; align-items: flex-start; border:0.75pt solid #dedede;border-radius: 7px; width: 100%; padding: 1%; box-sizing: border-box; margin: 10px 0;height: 400px;">
            <div style="width: 49%;">
              <div>
                <span>{{ item.list.province ? item.list.province : '暂无地名' }}</span>
                <span v-show="item.list.city">-</span>
                <span>{{ item.list.city }}</span>
                <span style="margin-left: 20px;">波动率：</span>
                <span>{{ item.list.volatility }}</span>
                <span v-show="item.list.volatility">%</span>
              </div>
              <buschar v-if="buscharshow" :analysisData="item.list.res" ref="dialogMapVisible2Buscher"
                :thisStyle="{ width: '99%', height: '340px', 'box-sizing': 'border-box', 'line-height': '360px' }">
              </buschar>
            </div>

            <div style="display: flex; flex-wrap: wrap; width: 49%; height: 100%;"
              v-if="item.weatherForecastList.length > 0">
              <div v-for="(items, index) in item.weatherForecastList" :key="index" class="card-container">
                <div class="cardStyle_clickable" style="position: relative;">
                  <span v-show="items.realForecast.date == currentDate"
                    style="position: absolute; top: 1px; right: -25px;background-color: red;color: white;font-size: 16px;border-radius: 20px;height: 20px; width: 20px;display: flex;align-items: center;justify-content: center;">今</span>
                  {{ items.realForecast.dayOfWeek }}
                </div>
                <div class="cardStyle_clickable"
                  style="font-weight: bold;text-shadow: 2px 2px 4px rgba(245, 240, 240, 0.5);">{{
                    items.realForecast.date }}</div>
                <div
                  v-if="items.realForecast.weaDay && items.realForecast.weaNight && items.realForecast.winDay && items.realForecast.winNight">
                  <div class="cardStyle">
                    {{ items.realForecast.weaDay }}
                    -
                    {{ items.realForecast.weaNight }}
                  </div>
                  <div class="cardStyle" style="text-align: center;margin-top: 10%;">
                    {{ items.realForecast.winDay }}
                    -
                    {{ items.realForecast.winNight }}
                  </div>
                  <div class="cardStyle">
                    {{ items.realForecast.winSpeedDay }}
                    -
                    {{ items.realForecast.winSpeedNight }}
                  </div>
                  <div class="cardStyle">
                    {{ items.realForecast.temDay }}
                    ~
                    {{ items.realForecast.temNight }}
                  </div>
                </div>
                <div v-else style="height: 100%;width: 100%;display: flex;align-items: center;justify-content: center;">
                  <span style="font-size: 15px;">暂无天气数据</span>
                </div>
              </div>
            </div>
            <div v-else style="height: 100%;width: 49%;display: flex;align-items: center;justify-content: center;">
              <span style="font-size: 30px;letter-spacing: 6px;">暂无天气数据</span>
            </div>
          </div>
        </div>
      </el-scrollbar>
      <div v-else style="height: 100%;width: 100%;display: flex;align-items: center;justify-content: center;"
        v-loading="loading">
        <span style="font-size: 30px;letter-spacing: 6px;">暂无数据</span>
      </div>
    </template>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" :pageSize="20" :sizes="[10, 20, 30, 40]"
        @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import buschar from '@/components/Bus/buschar';
import { getProvinceCityList, queryWeatherData } from "@/api/order/weather";
import { todaySkuTimeFrameByCity } from "@/api/order/orderData";
import YhCityselector from '@/components/YhCom/yh-cityselector.vue';
import { future7DayList } from "@/api/order/weather";
import middlevue from "@/store/middle.js"
import { getRegion } from '@/api/admin/business'

export default {
  name: "waveAnalysis",
  components: {
    MyContainer, vxetablebase, buschar, YhCityselector
  },

  data() {
    return {
      buscharshow: true,
      props: {
        multiple: true,
        lazy: true,
        // checkStrictly: true,
        lazyLoad: (async (node, resolve) => {
          let pcode = 0;
          if (node.level > 0)
            pcode = node.data.extObj.code;
          let res = await getRegion({ parentcode: pcode });
          if (res.data.length == 0) {
            res = { success: true, data: [{ "code": 0, "name": "市辖区", "parentcode": 0, "level": 2 }] }
          }
          if (res && res.success) {
            let nodes = res.data.map(item => ({
              value: item.name == "市辖区" ? node.value : item.name,
              label: item.name == "市辖区" ? node.value : item.name,
              leaf: node.level + 1 >= 2,
              extObj: item,
            }));
            resolve(nodes);
          }
        }),
      },
      delayProvince: [],
      currentDate: this.formatDate(),
      provinceCityList: [],
      options: [],
      cascaderProps: { multiple: true, label: 'name', value: 'code', children: 'childList' },
      provinceCityCodeList: [],
      value2: true,
      dialogMapVisible: { visible: false, title: "", data: {} },
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 20,
        orderBy: null,
        isAsc: false,
        orderByUndulate: true,//波动率排序
        skuCode: null,//sku编号
        undulate: null,//波动率
        provinceCity: [],
      },
      timeRanges: [],
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
      dataList: [],
      debounceTimer: null,
      query: {
        date: this.getLastDayOfCurrentMonth(),
        province: "",
        city: ""
      },
    }
  },
  activated() {
    this.loading = true
    let _this = this;
    if (_this.$route.query && _this.$route.query.skuCode) {
      _this.ListInfo.skuCode = _this.$route.query.skuCode;
      _this.ListInfo.orderByUndulate = _this.$route.query.orderByUndulate;
      _this.undulate = _this.$route.query.undulate;
      setTimeout(() => {
        _this.getList('search');
      }, 500);
    }
  },
  async mounted() {

  },
  beforeUpdate() {
    middlevue.$on('waveAnalysis', function (msg) {
      _this.dataList = [];
      _this.buscharshow = false;
      _this.ListInfo.skuCode = msg.skuCode;
      _this.ListInfo.orderByUndulate = msg.orderByUndulate;
      _this.undulate = msg.undulate;
      _this.ListInfo.provinceCity = [];
      _this.delayProvince = [];
      _this.getList('search');
    })
  },
  updated() {
    middlevue.$off('waveAnalysis');
  },
  methods: {
    handleCasChange(val) {
      this.delayProvince = []
      this.delayProvince = val.map(item => `${item[0]}-${item[1]}`);
    },
    formatDate() {
      const now = new Date();
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const day = now.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    getLastDayOfCurrentMonth() {
      const now = new Date();
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const day = 1;
      return `${year}-${month}-${day}`;
    },
    async getList(type) {
      let _this = this;
      _this.buscharshow = false
      if (type == 'search') {
        _this.ListInfo.currentPage = 1
        _this.$refs.pager.setPage(1)
      }
      if (!_this.ListInfo.skuCode) {
        _this.$message.error('请从商品波动分析选择子编码跳转')
        return
      }
      let provinceCity = _this.delayProvince
      let orderByUndulate = _this.ListInfo.orderByUndulate ? 1 : 2
      const params = { ..._this.ListInfo, orderByUndulate, provinceCity }
      _this.loading = true
      let dataListArr = [];
      const { data, success } = await todaySkuTimeFrameByCity(params)
      if (success) {
        if (data.list) {
          let cityList = data.list.map((item) => {
            if (item.province && item.city) {
              return `${item.province}-${item.city}`
            }
          });
          //去重
          cityList = cityList.filter(item => item !== undefined && item !== null);
          cityList = Array.from(new Set(cityList));
          const { data: data1, success: success1 } = await future7DayList({ cityNames: cityList })
          if (success1) {
            dataListArr = data1;
          }
          _this.tableData = _this.onTrendChart(data.list)
          _this.tableData.forEach(tableItem => {
            if (tableItem.province && tableItem.city) {
              const cityName = `${tableItem.province}-${tableItem.city}`;
              // 查找this.dataList数组中匹配的对象
              let matchingDataItem = dataListArr.find(dataItem => dataItem.cityName === cityName);
              // 如果找到匹配项，则将tableItem添加到对应的list字段中
              if (matchingDataItem) {
                if (!matchingDataItem.list) {
                  matchingDataItem.list = {};
                }
                matchingDataItem.list = tableItem;
                if(matchingDataItem.weatherForecastList && matchingDataItem.weatherForecastList.length == 0){
                  matchingDataItem.weatherForecastList = _this.onAcquisitionDate()
                }
              } else {
                // 如果没有找到匹配项，创建一个新的对象并添加到this.dataList中
                matchingDataItem = {
                  cityName: cityName,
                  list: tableItem,
                  weatherForecastList: _this.onAcquisitionDate()
                };
                dataListArr.push(matchingDataItem);
              }
            } else {
              dataListArr.push({
                list: tableItem,
                weatherForecastList: _this.onAcquisitionDate()
              });
            }
          });
          _this.total = data.total;
          _this.dataList = dataListArr;
        } else {
          _this.dataList = []
          _this.total = 0;
          _this.$forceUpdate();
        }
        _this.loading = false
        _this.buscharshow = true;
        _this.$forceUpdate();
      } else {
        //获取列表失败
        _this.$message.error('获取列表失败')
      }
    },
    onAcquisitionDate() {
      // 获取当前日期
      const today = new Date();
      // 创建一个空数组用于存放天气预报对象
      const weatherForecastList = [];
      // 生成前七天和后七天的日期（包括今天）
      for (let i = -7; i <= 6; i++) {
        // 计算新的日期
        const newDate = new Date(today);
        newDate.setDate(today.getDate() + i);
        // 获取日期并格式化为 YYYY-MM-DD
        const year = newDate.getFullYear();
        // 月份从0开始，所以加1；使用padStart保证月份和日期为两位数
        const month = (newDate.getMonth() + 1).toString().padStart(2, '0');
        const day = newDate.getDate().toString().padStart(2, '0');
        const date = `${year}-${month}-${day}`;
        const dayOfWeek = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][newDate.getDay()];
        weatherForecastList.push({
          realForecast: {
            date: date,
            dayOfWeek: dayOfWeek
          }
        });
      }
      return weatherForecastList;
    },
    onTrendChart(dataArray) {
      let ChartArray = dataArray.map(list => {
        let res = {
          xAxis: list.times,
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '7天前',
              type: 'bar',
              emphasis: {
                focus: 'series'
              },
              data: list.before7Counts
            },
            {
              name: '3天前',
              type: 'bar',
              emphasis: {
                focus: 'series'
              },
              data: list.before3Counts
            },
            {
              name: '昨天',
              type: 'bar',
              emphasis: {
                focus: 'series'
              },
              data: list.yesterdayCounts
            },
            {
              name: '今天',
              type: 'line',
              stack: 'Total',
              data: list.todayCounts
            },
          ]
        };
        res.series.map((item) => {
          item.itemStyle = {
            "normal": {
              "label": {
                "show": true,
                "position": "top",
                "textStyle": {
                  "fontSize": 14
                }
              }
            }
          }

          item.emphasis = {
            "focus": "series"
          }
          item.smooth = false;
        })
        list.volatility = this.ListInfo.orderByUndulate ? list.todayUndulate : list.yesterdayUndulate;
        // 清除原对象中的相关字段
        delete list.before3Counts;
        delete list.before7Counts;
        delete list.yesterdayCounts;
        delete list.todayCounts;
        // 将res对象添加到原对象中
        list.res = res;
        return list;
      });
      return ChartArray;
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
::v-deep .publicCss .el-cascader__search-input {
  margin: 0 0 0 6px !important;
}

.top {
  display: flex;
  margin: 10px 0;
  align-items: center;

  .publicCss {
    width: 20%;
    margin-right: 20px;
  }

  .center-content {
    margin-left: 12%;
    margin-right: auto;
    font-weight: bold;
    font-size: 19px;
  }
}

.tendency {
  display: flex;
  margin-bottom: 5px;

  .tendency_item {
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(245, 240, 240, 0.5);
  }
}

.card-container {
  border: 0.75pt solid grey;
  border-radius: 10px;
  width: 13%;
  height: 49%;
  margin-right: 10px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .cardStyle {
    display: flex;
    justify-content: center;
    font-size: 14px;
    margin: 2px 0;
  }

  .cardStyle_clickable {
    font-size: 15px;
    margin: 3px 0;
  }

  .cardStyle_clickable {
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(245, 240, 240, 0.5);
  }
}
</style>
