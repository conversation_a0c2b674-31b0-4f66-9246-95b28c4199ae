<template>
    <MyContainer>
        <el-form :model="ruleForm" status-icon ref="ruleForm" label-width="100px" class="demo-ruleForm">
            <el-form-item label="系列编码:">
                <el-input  v-model="ruleForm.styleCode" autocomplete="off" maxlength="50" clearable
                    placeholder="系列编码" style="width: 200px;" disabled />
            </el-form-item>
            <el-form-item label="备注内容:">
                <el-input type="textarea" v-model="ruleForm.remark" autocomplete="off" maxlength="3000" show-word-limit
                    placeholder="备注内容" rows="9" />
            </el-form-item>
            <el-form-item label="图片:">
                <uploadimgFile ref="uploadimgFile" :accepttyes="accepttyes" :isImage="true" :uploadInfo="chatUrls"
                    :keys="[1, 1]" @callback="getImg" :imgmaxsize="9" :limit="9" :multiple="true">
                </uploadimgFile>
            </el-form-item>
            <div style="display: flex;justify-content: center;align-items: center;">
                <el-button @click="$emit('close')">关闭</el-button>
                <el-button type="primary" @click="submitForm">提交</el-button>
            </div>
        </el-form>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import { addStyleCodeUserTrackLog } from '@/api/bookkeeper/styleCodeRptData'
export default {
    name: "scanCodePage",
    components: {
        MyContainer, uploadimgFile
    },
    props: {
        styleCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            ruleForm: {
                id: 0,
                styleCode: '',
                images: '',
                styleCode: this.styleCode,
            },
            chatUrls: [],
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            editPriceVisible: false,
        }
    },
    async mounted() {
    },
    methods: {
        getImg(data) {
            if (data) {
                this.chatUrls = data
                this.ruleForm.images = data.map(item => item.url).join(',')
            }
        },
        async submitForm() {
            const { success } = await addStyleCodeUserTrackLog(this.ruleForm)
            if (success) {
                this.$message.success('操作成功')
                this.$emit('close')
            }
        }
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
