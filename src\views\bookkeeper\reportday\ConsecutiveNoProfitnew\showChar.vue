<template>
  <div style="height: 600px; overflow-y: auto; overflow-x: hidden;">
    <div @click="buscharclick(1,'reftm')">
    <buschar :charid="'charNoProfit1'" ref="reftm" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :chartsdata="chartsdata1" v-if="this.isselplatform.indexOf('天猫')!=-1&&chartsdata1" ></buschar>
    </div>
    <div @click="buscharclick(4,'refalbb')">
    <buschar :charid="'charNoProfit2'" ref="refalbb" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :chartsdata="chartsdata2" v-if="this.isselplatform.indexOf('阿里巴巴')!=-1&&chartsdata2" ></buschar>
    </div>

    <div @click="buscharclick(6,'refdy')">
    <buschar :charid="'charNoProfit3'" ref="refdy" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :chartsdata="chartsdata3" v-if="this.isselplatform.indexOf('抖音')!=-1&&chartsdata3" ></buschar>
    </div>

    <div @click="buscharclick(7,'refjd')">
    <buschar :charid="'charNoProfit4'" ref="refjd" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :chartsdata="chartsdata4" v-if="this.isselplatform.indexOf('京东')!=-1&&chartsdata4" ></buschar>
    </div>

    <div @click="buscharclick(8,'reftgc')">
    <buschar :charid="'charNoProfit5'" ref="reftgc" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :chartsdata="chartsdata5" v-if="this.isselplatform.indexOf('淘工厂')!=-1&&chartsdata5" ></buschar>
    </div>

    <div @click="buscharclick(9,'reftb')">
    <buschar :charid="'charNoProfit6'" ref="reftb" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :chartsdata="chartsdata6" v-if="this.isselplatform.indexOf('淘宝')!=-1&&chartsdata6" ></buschar>
    </div>

    <div @click="buscharclick(10,'refsn')">
    <buschar :charid="'charNoProfit7'" ref="refsn" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :chartsdata="chartsdata7" v-if="this.isselplatform.indexOf('苏宁')!=-1&&chartsdata7" ></buschar>
    </div>

    <div @click="buscharclick(2,'refpdd')">
    <buschar :charid="'charNoProfit8'" ref="refpdd" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :chartsdata="chartsdata8" v-if="this.isselplatform.indexOf('拼多多')!=-1&&chartsdata8" ></buschar>
    </div>
  </div>
</template>
<script>
import buschar from '@/views/bookkeeper/reportday/ConsecutiveNoProfitnew/buschar.vue'
import MyContainer from "@/components/my-container";
import * as echarts from 'echarts/core';
import { getContinuousNoProfit, newGetContinuousNoProfitAnalysisAsync } from "@/api/bookkeeper/continuousprofitanalysis"
export default {
  name: "ConsecutiveNoProfitShowChar",
  components: {
    MyContainer, buschar
  },
  props: {
    filter: {
      
    },
    isselplatformlr: [],
    isselplatform: ['天猫',
        '阿里巴巴',
        '抖音',
        '京东',
        '淘工厂',
        '淘宝',
        '苏宁',]
  },
  data() {
    return {
      that: this,
      emphasisStyle: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0,0,1,0.3)'
        }
      },
      filter1: {
        currentPage: 1,
        pageSize: 50
      },
      chartsdata: {},
      chartsdata1: null,
      chartsdata2: null,
      chartsdata3: null,
      chartsdata4: null,
      chartsdata5: null,
      chartsdata6: null,
      chartsdata7: null,
      chartsdata8: null,


    };
  },
  async mounted() {
    
    await this.showChar();
  },
  // watch: {
  //   isselplatformlr: {
  //     handler(newVal){
  //       console.log("打印变化数据1",newVal)
  //     },
  //     deep: true
  //   }
  // },
  methods: {
    buscharclick(e, name){
      

      
        let queryObj = {
         
          isPositive: false
        }

        queryObj.platform = e;
        // queryObj.shopCode = (that.filter.shopCode==""?null:that.filter.shopCode);
        // queryObj.groupId =  (that.filter.groupId==""?null:that.filter.groupId);
        // queryObj.operateSpecialUserId =  (that.filter.operateSpecialUserId==""?null:that.filter.operateSpecialUserId);
        if(!JSON.parse(localStorage.getItem('isselplatformlr'))){
          this.$message.info('请只选择一个标签负利润后才能跳转平台！');
          return
        }
        let seriesName = JSON.parse(localStorage.getItem('isselplatformlr'))[0];
        if (seriesName == "3天负利润") {
          queryObj.dayCount = 3;
        } else if(seriesName == "5天负利润"){
          queryObj.dayCount = 5;
        }else if (seriesName == "7天负利润") {
          queryObj.dayCount = 7;
        } else if(seriesName == "10天负利润"){
          queryObj.dayCount = 10;
        } else if (seriesName == "15天负利润") {
          queryObj.dayCount = 15;
        } else if (seriesName == "30天负利润") {
          queryObj.dayCount = 30;
        }
        // queryObj.dayCount = JSON.parse(localStorage.getItem('isselplatformlr'))[0];

        let patams = {}
        patams.query = queryObj;
        patams.path = "/bookkeeper/reportday/productReportAllIndex";
        if (e == 2) {
          patams.path = "/bookkeeper/reportday/productReportPddIndex";
          this.$refs.refpdd.routerpush(patams);
        } else if (e == 4) {
          patams.path = "/bookkeeper/reportday/productReportAlibabaIndex";
          this.$refs.refalbb.routerpush(patams);
        } else if (e == 6) {
          patams.path = "/bookkeeper/reportday/productReportDyIndex";
          this.$refs.refdy.routerpush(patams);
        } else if (e == 7) {
          patams.path = "/bookkeeper/reportday/productReportJDIndex";
          this.$refs.refjd.routerpush(patams);
        } else if (e == 8) {
          patams.path = "/bookkeeper/reportday/productReportGCIndex";
          this.$refs.reftgc.routerpush(patams);
        } else if ( e == 1) {
          patams.path = "/bookkeeper/reportday/productReportTxIndex";
          this.$refs.reftm.routerpush(patams);
        } else if (e == 9 ) {
          patams.path = "/bookkeeper/reportday/productReportTaoBaoIndex";
          this.$refs.reftb.routerpush(patams);
        } else if (e == 10) {
          patams.path = "/bookkeeper/reportday/productReportSuNingIndex";
          this.$refs.refsn.routerpush(patams);
        }

        
        

        // that.$router.push({ path: url, query: queryObj })
    },
    async showChar() {
      this.chartsdata1 = null;
      this.chartsdata2 = null;
      this.chartsdata3 = null;
      this.chartsdata4 = null;
      this.chartsdata5 = null;
      this.chartsdata6 = null;
      this.chartsdata7 = null;
      this.chartsdata8 = null;
      // let chartDom = document.getElementById('charNoProfit');
      // echarts.dispose(chartDom);

      // let data1 = [];
      // let data2 = [];
      // let data3 = [];
      // let data4 = [];
      // let xAxis = [];
      // let values = [];

      // let param = { ...this.filter, ...this.filter1 };

      this.filter.profitTypes = [];

      let arr =  JSON.parse(localStorage.getItem('isselplatformlr'));
      var newass = [];
      if(arr.length>0){
        arr.map((item)=>{
          newass.push(item.slice('0',item.indexOf('天')))
        })
        this.filter.profitTypes = newass;
      }else{
        this.filter.profitTypes = null
      }
      


      let param = { 
        // "currentPage": 1,
        // "pageSize": 50,
        // "orderBy": "",
        // "isAsc": true,
        // "startTime": "2023-10-25",
        // "endTime": "2023-11-03",
        // "platforms": [
          
        // ],
        // "profitTypes": [
          
        // ]
        ...this.filter
       };

      let res = await newGetContinuousNoProfitAnalysisAsync(param);

      // this.chartsdata = res.data;
      res.data.map((item)=>{
        if(item.platformName == '天猫'){
          this.chartsdata1 = item;
        }else if(item.platformName == '阿里巴巴'){
          this.chartsdata2 = item;
        }else if(item.platformName == '抖音'){
          this.chartsdata3 = item;
        }else if(item.platformName == '京东'){
          this.chartsdata4 = item;
        }else if(item.platformName == '淘工厂'){
          this.chartsdata5 = item;
        }else if(item.platformName == '淘宝'){
          this.chartsdata6= item;
        }else if(item.platformName == '苏宁'){
          this.chartsdata7 = item;
        }else if(item.platformName == '拼多多'){
          this.chartsdata8 = item;
        }
      })
      console.log("获取数据1",res.data)

      console.log("获取数据2",this.chartsdata1)
      // res.data.list.forEach(item => {
      //   let total = item.consecutiveDay3 + item.consecutiveDay7 + item.consecutiveDay15 + item.consecutiveDay30;
      //   if (this.filter.groupType == "platform") {
      //     xAxis.push(item.platformName + "\n" + total);
      //     values.push(item.platform);
      //   } else if (this.filter.groupType == "shopcode") {
      //     xAxis.push(item.shopName + "\n" + total);
      //     values.push(item.shopCode);
      //   } else if (this.filter.groupType == "groupid") {
      //     xAxis.push(item.groupName + "\n" + total);
      //     values.push(item.groupId);
      //   } else if (this.filter.groupType == "operatespecialuserid") {
      //     xAxis.push(item.operateSpecialUserName + "\n" + total);
      //     values.push(item.operateSpecialUserId);
      //   }
      //   data1.push(item.consecutiveDay3);
      //   data2.push(item.consecutiveDay7);
      //   data3.push(item.consecutiveDay15);
      //   data4.push(item.consecutiveDay30);
      // });

      // let myChart = echarts.init(chartDom);
      // let option = {
      //   legend: {
      //     data: ["3天负利润", "7天负利润", "15天负利润", "30天负利润"],
      //     left: '40%'
      //   },
      //   toolbox: {},
      //   tooltip: {},
      //   xAxis: {
      //     data: xAxis,
      //     axisLine: { onZero: true },
      //     splitLine: { show: false },
      //     splitArea: { show: false },
      //     axisLabel: {
      //       interval: 0,      //坐标轴刻度标签的显示间隔(在类目轴中有效) 0:显示所有  1：隔一个显示一个 :3：隔三个显示一个...
      //       rotate: 30
      //     }
      //   },
      //   yAxis: {
      //     name: '个数'
      //   },
      //   grid: {
      //     bottom: 100
      //   },
      //   series: [
      //     {
      //       name: '3天负利润',
      //       type: 'bar',
      //       stack: 'one',
      //       emphasis: this.emphasisStyle,
      //       data: data1,
      //       label: {
      //         normal: {
      //           show: true,//开启显示
      //           position: 'inside',//柱形上方
      //           textStyle: { //数值样式
      //             color: '#31484f'
      //           },
      //           // 使用函数模板，函数参数分别为刻度数值（类目），刻度的索引
      //           formatter: function (value, index) {
      //             return value.data == 0 ? "" : value.data;
      //           }
      //         }
      //       }
      //     },
      //     {
      //       name: '7天负利润',
      //       type: 'bar',
      //       stack: 'one',
      //       emphasis: this.emphasisStyle,
      //       data: data2,
      //       label: {
      //         normal: {
      //           show: true,//开启显示
      //           position: 'inside',//柱形上方
      //           textStyle: { //数值样式
      //             color: '#31484f'
      //           },
      //           // 使用函数模板，函数参数分别为刻度数值（类目），刻度的索引
      //           formatter: function (value, index) {
      //             return value.data == 0 ? "" : value.data;
      //           }
      //         }
      //       }
      //     },
      //     {
      //       name: '15天负利润',
      //       type: 'bar',
      //       stack: 'one',
      //       emphasis: this.emphasisStyle,
      //       data: data3,
      //       label: {
      //         normal: {
      //           show: true,//开启显示
      //           position: 'inside',//柱形上方
      //           textStyle: { //数值样式
      //             color: '#31484f'
      //           },
      //           // 使用函数模板，函数参数分别为刻度数值（类目），刻度的索引
      //           formatter: function (value, index) {
      //             return value.data == 0 ? "" : value.data;
      //           }
      //         }
      //       }
      //     },
      //     {
      //       name: '30天负利润',
      //       type: 'bar',
      //       stack: 'one',
      //       emphasis: this.emphasisStyle,
      //       data: data4,
      //       label: {
      //         normal: {
      //           show: true,//开启显示
      //           position: 'inside',//柱形上方
      //           textStyle: { //数值样式
      //             color: '#31484f'
      //           },
      //           // 使用函数模板，函数参数分别为刻度数值（类目），刻度的索引
      //           formatter: function (value, index) {
      //             return value.data == 0 ? "" : value.data;
      //           }
      //         }
      //       }
      //     }
      //   ]
      // };

      // let that = this;

      // myChart.on('click', function (params) {



      //   let url = "/bookkeeper/reportday/productReportAllIndex";
      //   if (that.filter.platform == 2) {
      //     url = "/bookkeeper/reportday/financialReportPdd";
      //   } else if (that.filter.platform == 4) {
      //     url = "/bookkeeper/reportday/productReportAlibabaIndex";
      //   } else if (that.filter.platform == 6) {
      //     url = "/bookkeeper/reportday/productReportDyIndex";
      //   } else if (that.filter.platform == 7) {
      //     url = "/bookkeeper/reportday/productReportJDIndex";
      //   } else if (that.filter.platform == 8) {
      //     url = "/bookkeeper/reportday/productReportGCIndex";
      //   } else if ( that.filter.platform == 1) {
      //     url = "/bookkeeper/reportday/productReportTxIndex";
      //   } else if (that.filter.platform == 9 ) {
      //     url = "/bookkeeper/reportday/productReportTaoBao";
      //   } else if (that.filter.platform == 10) {
      //     url = "/bookkeeper/reportday/productReportSuNingIndex";
      //   }
      //   let queryObj = {
      //     yearMonthDay: that.filter.yearMonthDay,
      //     isPositive: false
      //   }
      //   // let index = xAxis.findIndex(x => x == params.name);
      //   // if (that.filter.groupType == "platform") {
      //   //   queryObj.platform = values[index];
      //   // } else if (that.filter.groupType == "shopcode") {
      //   //   queryObj.shopCode = values[index];
      //   // } else if (that.filter.groupType == "groupid") {
      //   //   queryObj.groupId = values[index];
      //   // } else if (that.filter.groupType == "operatespecialuserid") {
      //   //   queryObj.operateSpecialUserId = values[index];
      //   // } 
      //   queryObj.platform = (that.filter.platform==""?null:that.filter.platform);
      //   queryObj.shopCode = (that.filter.shopCode==""?null:that.filter.shopCode);
      //   queryObj.groupId =  (that.filter.groupId==""?null:that.filter.groupId);
      //   queryObj.operateSpecialUserId =  (that.filter.operateSpecialUserId==""?null:that.filter.operateSpecialUserId);
      //   if (params.seriesName == "3天负利润") {
      //     queryObj.dayCount = 3;
      //   } else if (params.seriesName == "7天负利润") {
      //     queryObj.dayCount = 7;
      //   } else if (params.seriesName == "15天负利润") {
      //     queryObj.dayCount = 15;
      //   } else if (params.seriesName == "30天负利润") {
      //     queryObj.dayCount = 30;
      //   }
      //   that.$router.push({ path: url, query: queryObj })
      // });

      // myChart.setOption(option);
    }
  }
};
</script>
<style>
.a{
  overflow-y: auto;
  position: relative;
  
}
</style>