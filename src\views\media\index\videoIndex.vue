<template>
  <div>
    <div class="sydhsx">
      <div>
        <div style="width: 325px; margin: 0px auto">
          <el-checkbox-group v-model="mainviewcheckList">
            <el-checkbox style="margin: 5px 6px" label="任务列表">任务列表</el-checkbox>
            <el-checkbox style="margin: 5px 6px" label="已完成">已完成</el-checkbox>
            <el-checkbox style="margin: 5px 6px" label="存档">存档</el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <div style="width: 620px; margin: 8px auto">
        <el-select size="mini" style="width: 120px" filterable v-model="mainviewfilter.platform" :clearable="true"
          placeholder="平台" @change="onchangeplatfor">
          <el-option v-for="item in platformList" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-select size="mini" style="width: 200px" filterable v-model="mainviewfilter.shopName" :clearable="true"
          placeholder="店铺">
          <el-option v-for="item in shopListmain" :key="item.id" :label="item.shopName" :value="item.id"> </el-option>
        </el-select>
        <el-date-picker size="mini" style="position: relative; top: 1px; width: 35%" type="daterange" align="right"
          unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
          :picker-options="pickerOptions" v-model="mainviewfilter.timerange"> </el-date-picker>
        <span> <el-button type="primary" @click="initsearch">查询</el-button></span>
      </div>
    </div>
    <div class="sybj">
      <div class="tjnrk1">
        <div class="tjnrnk">
          <div class="tjbt">
            <span>任务状态</span>
          </div>
          <div class="nrqk" style="height: 230px">
            <div
              style=" width: 760px;   margin: 0 auto;  position: relative;  top: 6px; background-color: rgb(255, 0, 0);  ">
              <div class="sztjk">
                <div class="tjsz">{{ tasktotal }}</div>
                <div class="tjmc">视频条数</div>
              </div>
              <div class="sztjk">
                <div class="tjsz">{{ taskovertotal }}</div>
                <div class="tjmc">完成条数</div>
              </div>
              <div class="sztjk">
                <div class="tjsz">{{ taskdstotal }}</div>
                <div class="tjmc">待审条数</div>
              </div>
              <div class="sztjk">
                <div class="tjsz">{{ taskbptotal }}</div>
                <div class="tjmc">补拍条数</div>
              </div>
              <div class="sztjk">
                <div class="tjsz">{{ tasktgtotal }}</div>
                <div class="tjmc">通过条数</div>
              </div>
              <div class="sztjk">
                <div class="tjsz">{{ taskcuttotal }}</div>
                <div class="tjmc">已剪条数</div>
              </div>
              <div class="sztjk">
                <div class="tjsz">{{ maintasktotal }}</div>
                <div class="tjmc">任务总数</div>
              </div>
              <div class="sztjk">
                <div class="tjsz">{{ maintaskovertotal }}</div>
                <div class="tjmc">完成任务</div>
              </div>
              <div class="sztjk">
                <div class="tjsz">{{ maintaskdstotal }}</div>
                <div class="tjmc">待审任务</div>
              </div>
              <div class="sztjk">
                <div class="tjsz">{{ maintaskbptotal }}</div>
                <div class="tjmc">补拍任务</div>
              </div>
              <div class="sztjk">
                <div class="tjsz">{{ maintasktgtotal }}</div>
                <div class="tjmc">通过任务</div>
              </div>
              <div class="sztjk">
                <div class="tjsz">{{ maintaskcuttotal }}</div>
                <div class="tjmc">已剪任务</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="tjnrk2">
        <div class="tjnrnk">
          <div class="tjbt">
            <span>部门公告</span>
          </div>
          <div style="height: 230px" class="nrqk">
            <div
              style="width: 500px; margin: 0 auto;  position: relative;  top: 15px;  background-color: rgb(255, 0, 0); ">
            </div>
          </div>
        </div>
      </div>
      <div class="tjnrk3">
        <div class="tjnrnk">
          <div class="tjbt">
            <span>拍摄进度</span>
          </div>
          <div style="height: 360px" class="nrqk">
            <div style="padding: 8px 25px; color: #666">
              <span>拍摄总数：</span>
              <span style="margin-right: 20px">{{ shootpstotalsum }}</span>
              <span>通过总数：</span>
              <span style="margin-right: 20px">{{ shoottgtotalsum }}</span>
              <span>补拍总数：</span>
              <span style="margin-right: 20px">{{ shootbptotalsum }}</span>
              <span>待拍总数：</span>
              <span style="margin-right: 20px">{{ shootdptotalsum }}</span>
              <span>待审总数：</span>
              <span style="margin-right: 20px">{{ shootdstotalsum }}</span>
            </div>
            <div id="shortvideosxtjtb" style="width: 85vw; height: 355px; margin: 0 auto"></div>
          </div>
        </div>
      </div>
      <div class="tjnrk7">
        <div class="tjnrnk">
          <div class="tjbt">
            <span>剪辑进度</span>
          </div>
          <div style="height: 360px" class="nrqk">
            <div style="width: 100%; float: left; margin: 5px 0"></div>
            <div style="padding: 8px 25px; color: #666">
              <span>剪辑总数：</span>
              <span style="margin-right: 20px">{{ cuttaskjqtotal }}</span>
              <span>待剪总数：</span>
              <span style="margin-right: 20px">{{ cuttaskdjtotal }}</span>
              <!-- <span>待审总数：</span>
              <span style="margin-right: 20px"></span> -->
            </div>
            <div id="shortvideojjtjtb" style="width: 85vw; height: 355px; margin: 0 auto"></div>
          </div>
        </div>
      </div>
      <div class="tjnrk8">
        <div class="tjnrnk">
          <div class="tjbt">
            <span>每日更新</span>
          </div>
          <div style="height: 360px" class="nrqk">
            <div style="width: 90vw; float: left; margin: 5px 0"></div>
            <div style="padding: 8px 25px; color: #666">
              <span>日均新建：</span>
              <span style="margin-right: 20px">{{ dayxjtotalsum }}</span>
              <span>日均拍摄：</span>
              <span style="margin-right: 20px">{{ daypstotalsum }}</span>
              <span>日均剪辑：</span>
              <span style="margin-right: 20px">{{ dayjjtotalsum }}</span>
            </div>
            <div id="shortvideorwtjtb" style="width: 85vw; height: 355px; margin: 0 auto"></div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="个人数据趋势" :visible.sync="view5show" width="80%" element-loading-text="拼命加载中"
      :close-on-click-modal="true" :append-to-body="true">
      <div style="display:inline-block">
        <el-button style="padding: 0;">
          <el-select filterable v-model="view5filter.groupName" placeholder="人员" clearable :collapse-tags="true">
            <el-option v-for="item in groupNames" :key="item" :label="item" :value="item" />
          </el-select>
        </el-button>
        <el-date-picker style="width:310px;margin-left:13px; margin-top: 10px " type="daterange" format="yyyy-MM-dd"
          value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
          v-model="view5filter.timerange" :picker-options="pickerOptions" />
        <el-button type="primary" @click="showViewforper">查询</el-button>
      </div>
      <div>
        <buschar ref="view5" :analysisData="viewData5" :thisStyle="thisStyleView5" :gridStyle="gridStyleView5">
        </buschar>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import { rulePlatform } from "@/utils/formruletools";
import buschar from '@/components/Bus/buscharforShooting.vue';
import {
  taskStatusStatistics, shootingProgressStatistics, cuteProgressStatistics
  , allProgressStatisticsDetail, cuteProgressStatisticsDetail, shootingProgressStatisticsDetail
} from '@/api/media/video';
import { getErpUserInfoView as getShootingViewPersonAsync } from '@/api/media/mediashare';
export default {
  components: { buschar },
  data() {
    return {
      groupNames: [],
      viewData5: [],
      view5show: false,
      listLoading: false,
      thisStyleView5: { width: '100%', height: '400px', 'box-sizing': 'border-box', 'line-height': '240px' },
      gridStyleView5: { left: '1%', right: 15, bottom: 20, top: '10%', containLabel: true },
      view5filter: {
        startTime: null,
        endTime: null,
        timerange: [],
        groupName: null
      },
      maintasktotal: null,
      maintaskovertotal: null,
      maintaskdstotal: null,
      maintaskbptotal: null,
      maintasktgtotal: null,
      maintaskcuttotal: null,
      tasktotal: null,
      taskovertotal: null,
      taskdstotal: null,
      taskbptotal: null,
      tasktgtotal: null,
      taskcuttotal: null,
      platformList: [],
      shopListmain: [],
      mainviewcheckList: ['任务列表', '已完成'],
      mainviewfilter: {
        startTime: null,
        endTime: null,
        timerange: [],
        shopName: null,
        platform: null,
      },
      pickerOptions: { disabledDate(time) { return time.getTime() > Date.now() } },
      cheecktype: 0,
      shootpstotalsum: null,
      shoottgtotalsum: null,
      shootbptotalsum: null,
      shootdptotalsum: null,
      shootdstotalsum: null,
      cuttaskjqtotal: null,
      cuttaskdjtotal: null,

      dayxjtotalsum: null,
      daypstotalsum: null,
      dayjjtotalsum: null,
    }
  },
  async mounted() {

    await this.onGetdrowList();
    await this.getShootingViewPer();
    this.initsearch();
    // this.fourchart();
    // this.fivechart();
    // this.sixchart();
    // this.sevenchart();
    // this.eightchart();
  },

  methods: {
    async initsearch() {
      this.gettotaltaskInfo();
      this.onechart();
      this.threechart();
      this.twochart();
    },
         // 格式化数值的函数
     formatNumber(value){
            const absNumber = Math.abs(value);
            const isInteger =Number.isInteger(value);
            const options ={
            minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
            maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
            useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value);     
                },     
    //获取分配人下拉，对接人下啦
    async getShootingViewPer() {
      var res = await getShootingViewPersonAsync();
      if (res) {
        this.groupNames = res.map(item => { return item.label });
      }
    },
    //获取下拉数据
    async onGetdrowList() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;
    },
    //切换平台
    async onchangeplatfor(val) {
      var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
      this.mainviewfilter.shopName = null;
      this.shopListmain = res1.data.list;
    },
    async OpenDetail(params, type) {
      this.view5filter.timerange = this.mainviewfilter.timerange;
      this.view5filter.groupName = params.name;
      this.view5filter.shopName = this.mainviewfilter.shopName;
      this.view5filter.platform = this.mainviewfilter.platform;
      await this.showViewforper();
    },
    async showViewforper() {
      //获取数据
      if (this.mainviewcheckList.length == 0) {
        this.$message({ message: '必须选择一种任务列表', type: "error" });
        return;
      }

      //获取数据
      if (this.view5filter.timerange) {
        this.view5filter.startTime = this.view5filter.timerange[0];
        this.view5filter.endTime = this.view5filter.timerange[1];
      } else {
        this.view5filter.startTime = null;
        this.view5filter.endTime = null;
      }
      this.addLoading = true;
      this.view5show = true;
      let res1 = {};
      if (this.cheecktype == 1) {
        res1 = await shootingProgressStatisticsDetail({
          "startTime": this.view5filter.startTime,
          "endTime": this.view5filter.endTime,
          "checkTaskList": this.mainviewcheckList,
          "groupName": this.view5filter.groupName
        })
      } else
        if (this.cheecktype == 2) {
          res1 = await cuteProgressStatisticsDetail({
            "startTime": this.view5filter.startTime,
            "endTime": this.view5filter.endTime,
            "checkTaskList": this.mainviewcheckList,
            "groupName": this.view5filter.groupName
          })
        }
      this.setOptions(res1);
      this.viewData5 = res1;
      await this.$refs.view5.initcharts();
      this.addLoading = false;
    },
    //获取确认列表信息
    async gettotaltaskInfo() {
      //获取数据
      if (this.mainviewfilter.timerange) {
        this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
        this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
      } else {
        this.mainviewfilter.startTime = null;
        this.mainviewfilter.endTime = null;
      }
      this.listLoading = true;
      var res = await taskStatusStatistics({
        "startTime": this.mainviewfilter.startTime,
        "endTime": this.mainviewfilter.endTime,
        "CheckTaskList": this.mainviewcheckList,
        "shopName": this.mainviewfilter.shopName,
        "platform": this.mainviewfilter.platform,
        ...this.pager,
      });
      this.listLoading = false;

      if (res.success) {
        this.maintasktotal = this.formatNumber(res.data.maintasktotal);
        this.maintaskovertotal = this.formatNumber(res.data.maintaskovertotal);
        this.maintaskdstotal = this.formatNumber(res.data.maintaskdstotal);
        this.maintaskbptotal = this.formatNumber(res.data.maintaskbptotal);
        this.maintasktgtotal = this.formatNumber(res.data.maintasktgtotal);
        this.maintaskcuttotal = this.formatNumber(res.data.maintaskcuttotal);

        this.tasktotal = this.formatNumber(res.data.tasktotal);
        this.taskovertotal = this.formatNumber(res.data.taskovertotal);
        this.taskdstotal = this.formatNumber(res.data.taskdstotal);
        this.taskbptotal = this.formatNumber(res.data.taskbptotal);
        this.tasktgtotal = this.formatNumber(res.data.tasktgtotal);
        this.taskcuttotal = this.formatNumber(res.data.taskcuttotal);
      }
    },
    //  拍摄进度统计图表
    async onechart() {
      if (this.mainviewfilter.timerange) {
        this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
        this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
      } else {
        this.mainviewfilter.startTime = null;
        this.mainviewfilter.endTime = null;
      }
      var res1 = await shootingProgressStatistics({
        "startTime": this.mainviewfilter.startTime,
        "endTime": this.mainviewfilter.endTime,
        "CheckTaskList": this.mainviewcheckList,
        "shopName": this.mainviewfilter.shopName,
        "platform": this.mainviewfilter.platform
      });
      this.shootpstotalsum = res1.summuydata[0].seriestotal;
      this.shoottgtotalsum = res1.summuydata[1].seriestotal;
      this.shootbptotalsum = res1.summuydata[2].seriestotal;
      this.shootdptotalsum = res1.summuydata[3].seriestotal;
      this.shootdstotalsum = res1.summuydata[4].seriestotal;
      var myChart = echarts.init(document.getElementById("shortvideosxtjtb"));
      var option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999",
            },
          },
          formatter: function (params) {
            let result = '';
            params.forEach(param => {
              const value = param.value;
              const formattedValue = formatValue(value);
              result += `${param.seriesName}: ${formattedValue}<br/>`;
            });
            return result;
          }
        },
        toolbox: {
          show: true,
          orient: "vertical",
          left: "right",
          top: "center",
          feature: {
            mark: { show: true },
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ["line", "bar", "stack"] },
            restore: { show: true },
            saveAsImage: { show: true },
          },
        },
        legend: {
          top: 5,
          selected: { "工作时长": false, "完成率": false },
          data: ["分配条数", "已拍条数", "剩余条数", "补拍条数", "工作时长", "完成率"],
        },
        xAxis: [
          {
            type: "category",
            data: res1.xAxis,
            axisPointer: {
              type: "shadow",
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "",
            axisLine: { show: false },
            axisLabel: {
              formatter: "{value} ",
            },
          },
          {
            type: "value",
            name: "",
            axisLine: { show: false },
            axisLabel: {
              formatter: "{value} ",
            },
          },
        ],
        series: [
          {
            name: "分配条数",
            type: "bar",
            id: "sales",
            barGap: "5%",
            label: {
              show: true,
              position: "top",
              formatter: function (params) {
                return formatValue(params.value);
              },
            },
            emphasis: {
              focus: "series",
            },
            tooltip: {
              valueFormatter: function (value) {
                return formatValue(value);
              },
            },
            data: res1.series[0].data
          },

          {
            name: "已拍条数",
            type: "bar",
            id: "sales2",
            label: {
              show: true,
              position: "top",
              formatter: function (params) {
                return formatValue(params.value);
              },
            },
            emphasis: {
              focus: "series",
            },
            tooltip: {
              valueFormatter: function (value) {
                return formatValue(value);
              },
            },
            data: res1.series[1].data
          },

          {
            name: "剩余条数",
            type: "bar",
            label: {
              show: true,
              position: "top",
              formatter: function (params) {
                return formatValue(params.value);
              },
            },
            emphasis: {
              focus: "series",
            },
            tooltip: {
              valueFormatter: function (value) {
                return formatValue(value);
              },
            },
            data: res1.series[2].data
          },

          {
            name: "补拍条数",
            type: "bar",
            label: {
              show: true,
              position: "top",
              formatter: function (params) {
                return formatValue(params.value);
              },
            },
            emphasis: {
              focus: "series",
            },
            tooltip: {
              valueFormatter: function (value) {
                return formatValue(value);
              },
            },
            data: res1.series[3].data
          },

          {
            name: "工作时长",
            type: "bar",
            yAxisIndex: "1",
            label: {
              show: true,
              position: "top",
              formatter: function (params) {
                return formatValue(params.value);
              },
            },
            emphasis: {
              focus: "series",
            },
            tooltip: {
              valueFormatter: function (value) {
                return formatValue(value);
              },
            },
            data: res1.series[4].data
          },
          {
            name: "完成率",
            type: "line",
            yAxisIndex: "1",
            label: {
              show: true,
              position: "top",
              formatter: function (params) {
                return formatValue(params.value) + '%';
              },
            },
            emphasis: {
              focus: "series",
            },
            tooltip: {
              valueFormatter: function (value) {
                return formatValue(value) + '%';
              },
            },
            data: res1.series[5].data
          },
        ],
      };

            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            }

      myChart.on("click", async params => {
        if (params && params.name != "" && res1.xAxis.includes(params.name)) {
          // 弹出新的窗口，获取人员的工作时长和对应的
          this.cheecktype = 1;
          await this.OpenDetail(params);
        }
      });

      myChart.setOption(option);
    },
    setOptions(element) {
      element.series.forEach(s => {
        s.barMaxWidth = '50';
        if (s.name.indexOf("率") > -1) {
          s.itemStyle = { normal: { label: { show: true, position: 'top', textStyle: { fontSize: 14 }, formatter: '{c}%' }, color: s.backColor } };
        } else {
          s.itemStyle = { normal: { label: { show: true, position: 'top', textStyle: { fontSize: 14 } } } };
        }
        s.emphasis = { focus: 'series' };
        s.smooth = false;
      })
    },

    //  拍摄进度统计图表

    //  剪辑进度统计图表

    async threechart() {

      if (this.mainviewfilter.timerange) {
        this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
        this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
      } else {
        this.mainviewfilter.startTime = null;
        this.mainviewfilter.endTime = null;
      }
      var res1 = await cuteProgressStatistics({
        "startTime": this.mainviewfilter.startTime,
        "endTime": this.mainviewfilter.endTime,
        "CheckTaskList": this.mainviewcheckList,
        "shopName": this.mainviewfilter.shopName,
        "platform": this.mainviewfilter.platform
      });
      this.cuttaskjqtotal = this.formatNumber(res1.summuydata[0].seriestotal);
      this.cuttaskdjtotal = this.formatNumber(res1.summuydata[1].seriestotal);
      var myChart = echarts.init(document.getElementById("shortvideojjtjtb"));

      var option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999",
            },
          },
          formatter: function (params) {
            let result = '';
            params.forEach(function (param) {
              let value = param.value;
              // 格式化值
              if (value < 100) {
                // 如果是整数，直接使用，否则保留两位小数
                    value = Number.isInteger(value) ? value.toString() : value.toFixed(2);
                } else {
                    // 高于100的情况，使用千位符，不保留小数
                    value = Math.floor(value).toLocaleString();
                }
              result += param.seriesName + ': ' + value + '<br/>';
            });
            return result;
          }
        },
        toolbox: {
          show: true,
          orient: "vertical",
          left: "right",
          top: "center",
          feature: {
            mark: { show: true },
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ["line", "bar", "stack"] },
            restore: { show: true },
            saveAsImage: { show: true },
          },
        },
        legend: {
          top: 5,
          selected: { "工作时长": false, "完成率": false },
          data: ["负责条数", "已剪条数", "待剪条数", "工作时长", "完成率"],
        },
        xAxis: [
          {
            type: "category",
            data: res1.xAxis,
            axisPointer: {
              type: "shadow",
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "",
            axisLabel: {
              formatter: function (value) {
                if (value < 100) {
                // 如果是整数，直接使用，否则保留两位小数
                    value = Number.isInteger(value) ? value.toString() : value.toFixed(2);
                } else {
                    // 高于100的情况，使用千位符，不保留小数
                    value = Math.floor(value).toLocaleString();
                }
              },
            },
          },
          {
            type: "value",
            name: "",
            axisLabel: {
              formatter: function (value) {
                if (value < 100) {
                // 如果是整数，直接使用，否则保留两位小数
                    value = Number.isInteger(value) ? value.toString() : value.toFixed(2);
                } else {
                    // 高于100的情况，使用千位符，不保留小数
                    value = Math.floor(value).toLocaleString();
                }
              },
            },
          },
        ],
        grid: {
          top: "55px",
          bottom: "80px",
          left: "60px",
          right: "75px",
        },
        series: [
          {
            name: "负责条数",
            type: "bar",
            id: "sales",
            barGap: "5%",
            label: {
              show: true,
              position: "top",
              formatter: function (params) {
                return formatValue(params.value);
              }
            },
            emphasis: {
              focus: "series",
            },
            tooltip: {
              valueFormatter: function (value) {
                return formatValue(value);
              },
            },
            data: res1.series[0].data
          },

          {
            name: "已剪条数",
            type: "bar",
            id: "sales2",
            label: {
              show: true,
              position: "top",
              formatter: function (params) {
                return formatValue(params.value);
              }
            },
            emphasis: {
              focus: "series",
            },
            tooltip: {
              valueFormatter: function (value) {
                return formatValue(value)
              },
            },
            data: res1.series[1].data
          },

          {
            name: "待剪条数",
            type: "bar",
            label: {
              show: true,
              position: "top",
              formatter: function (params) {
                return formatValue(value)
              }
            },
            emphasis: {
              focus: "series",
            },

            data: res1.series[2].data
          },

          {
            name: "工作时长",
            type: "bar",
            yAxisIndex: "1",
            label: {
              show: true,
              position: "top",
            },
            emphasis: {
              focus: "series",
            },
            tooltip: {
              valueFormatter: function (value) {
                if (value < 100) {
                // 如果是整数，直接使用，否则保留两位小数
                    value = Number.isInteger(value) ? value.toString() : value.toFixed(2);
                } else {
                    // 高于100的情况，使用千位符，不保留小数
                    value = Math.floor(value).toLocaleString();
                }
              },
            },
            data: res1.series[3].data
          },
          {
            name: "完成率",
            type: "line",
            yAxisIndex: "1",
            label: {
              show: true,
              position: "top",
              formatter: function (value) {
                if (value < 100) {
                // 如果是整数，直接使用，否则保留两位小数
                    value = Number.isInteger(value) ? value.toString() : value.toFixed(2);
                } else {
                    // 高于100的情况，使用千位符，不保留小数
                    value = Math.floor(value).toLocaleString();
                }
              }
            },
            emphasis: {
              focus: "series",
            },
            tooltip: {
              valueFormatter: function (value) {
                if (value < 100) {
                // 如果是整数，直接使用，否则保留两位小数
                    value = Number.isInteger(value) ? value.toString() : value.toFixed(2);
                } else {
                    // 高于100的情况，使用千位符，不保留小数
                    value = Math.floor(value).toLocaleString();
                }
              },
            },
            data: res1.series[4].data
          },
        ],
      };
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            }
      myChart.on("click", async params => {
        if (params && params.name != "" && res1.xAxis.includes(params.name)) {
          //弹出新的窗口，获取人员的工作时长和对应的
          this.cheecktype = 2;
          await this.OpenDetail(params);
        }
      });
      myChart.setOption(option);
    },

    //  剪辑进度统计图表

    //  每日更新统计图表

    async twochart() {

      if (this.mainviewfilter.timerange) {
        this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
        this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
      } else {
        this.mainviewfilter.startTime = null;
        this.mainviewfilter.endTime = null;
      }
      var res1 = await allProgressStatisticsDetail({
        "startTime": this.mainviewfilter.startTime,
        "endTime": this.mainviewfilter.endTime,
        "CheckTaskList": this.mainviewcheckList,
        "shopName": this.mainviewfilter.shopName,
        "platform": this.mainviewfilter.platform
      });
      this.dayxjtotalsum = this.formatNumber(res1.summuydata[0].seriestotal);
      this.daypstotalsum = this.formatNumber(res1.summuydata[1].seriestotal);
      this.dayjjtotalsum = this.formatNumber(res1.summuydata[2].seriestotal);

      var myChart = echarts.init(document.getElementById("shortvideorwtjtb"));

      var option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999",
            },
          },
        },
        toolbox: {
          show: true,
          orient: "vertical",
          left: "right",
          top: "center",
          feature: {
            mark: { show: true },
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ["line", "bar", "stack"] },
            restore: { show: true },
            saveAsImage: { show: true },
          },
        },
        legend: {
          top: 5,
          selected: { "拍摄数量": false, "剪辑数量": false },
          data: ["新建数量", "拍摄数量", "剪辑数量"],
        },
        xAxis: [
          {
            type: "category",
            data: res1.xAxis,
            axisPointer: {
              type: "shadow",
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "",

            axisLabel: {
              formatter: "{value} ",
            },
          },
          {
            type: "value",
            name: "",

            axisLabel: {
              formatter: "{value} ",
            },
          },
        ],

        grid: {
          top: "55px",
          bottom: "80px",
          left: "60px",
          right: "75px",
        },

        series: [
          {
            name: "新建数量",
            type: "line",
            id: "sales",
            barGap: "5%",
            label: {
              show: true,
              position: "top",
              formatter: function (params) {
                return formatValue(params.value);
              }
            },
            emphasis: {
              focus: "series",
            },
            tooltip: {
              valueFormatter: function (value) {
                return formatValue(value);
              },
            },
            data: res1.series[0].data
          },

          {
            name: "拍摄数量",
            type: "line",
            id: "sales2",
            label: {
              show: true,
              position: "top",
              formatter: function (params) {
                return formatValue(params.value);
              }
            },
            emphasis: {
              focus: "series",
            },
            tooltip: {
              valueFormatter: function (value) {
                return formatValue(value);
              },
            },
            data: res1.series[1].data
          },

          {
            name: "剪辑数量",
            type: "line",
            label: {
              show: true,
              position: "top",
              formatter: function (params) {
                return formatValue(params.value);
              }
            },
            emphasis: {
              focus: "series",
            },
            tooltip: {
              valueFormatter: function (value) {
                return formatValue(value);
              },
            },
            data: res1.series[2].data
          }
        ],
      };
      // 格式化函数
      function formatValue(value) {
        const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
      }
      myChart.setOption(option);
    },


    //  每日更新统计图表
  },

};
</script>

<style lang="scss" scoped>
* {
  font-size: 14px;
}

.sybj {
  min-width: 1100px;
  background-color: #f3f4f6;
  padding: 5px;
  height: calc(100vh - 280px);
  overflow-y: auto;
}

.tjldh {
  width: 785px;
  margin: 0 auto;
  box-shadow: 0px 3px 8px #cacaca;
  /* position: fixed; */
  z-index: 999;
}

.el-menu-demo {
  text-align: center;
}

.rqxz {
  width: 1000px;
  height: 80px;
  background-color: rgb(0, 54, 36);
  margin: 0 auto;
  line-height: 50px;
}

.tjbt {
  /* background-color: aquamarine; */
  /* font-weight: bold; */
  color: #333;
  line-height: 30px;
}

.sztjk {
  min-width: 75px;
  height: 50px;
  background-color: #f5faff;
  padding: 20px;
  text-align: center;
  float: left;
  margin: 5px;
  border-radius: 8px;
}

.sztjk .tjsz {
  font-size: 22px;
  color: #409eff;
}

.sztjk .tjmc {
  font-size: 14px;
  color: #409eff;
}

.tjnrk1 {
  width: 60%;
  /* background-color: rgb(255, 0, 0); */
  box-sizing: border-box;
  padding: 3px 5px;
  display: inline-block;
}

.tjnrk2 {
  width: 40%;
  /* background-color: rgb(255, 187, 0); */
  box-sizing: border-box;
  padding: 3px 5px;
  display: inline-block;
}

.tjnrk3 {
  width: 100%;
  /* background-color: rgb(255, 0, 0); */
  box-sizing: border-box;
  padding: 3px 5px;
  display: inline-block;
}

.tjnrk7 {
  width: 100%;
  /* background-color: rgb(255, 0, 0); */
  box-sizing: border-box;
  padding: 3px 5px;
  display: inline-block;
}

.tjnrk8 {
  width: 100%;
  /* background-color: rgb(255, 0, 0); */
  box-sizing: border-box;
  padding: 3px 5px;
  display: inline-block;
}

.tjnrnk {
  width: 100%;
  background-color: rgb(255, 255, 255);
  box-sizing: border-box;
  padding: 15px 20px;
  float: left;
  border-radius: 6px;
}

.ptsx {
  width: 85%;
  height: 90px;
  background-color: #f7f7f7;
  border-radius: 8px;
  margin: 10px auto;
  box-sizing: border-box;
  padding: 0 35px;
  line-height: 90px;
}

.ptsx span {
  font-size: 16px;
  color: #555;
}

/* .nrqk{
    background-color: #000;

  } */

.sydh {
  width: 100%;
  min-width: 1100px;
  height: 175px;
  border: 1px solid rgb(209, 209, 209);
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  z-index: 999;
}

.sydhfl {
  width: 120px;
  height: 50px;
  display: inline-block;
  /* margin: 2px; */
  text-align: center;
  line-height: 50px;
  color: #555;
  text-decoration: none;
}

.sydhfl :hover {
  width: 120px;
  height: 50px;
  background: linear-gradient(#f5f5f5 96%, #409eff 96%);
  display: inline-block;
  /* margin: 2px; */
  text-align: center;
  line-height: 50px;
  color: #409eff;
}

.sydhfl i {
  font-size: 19px;
  color: #409eff;
  margin-right: 5px;
  position: relative;
  top: 1px;
  line-height: 50px;
}

.sydh .fgf {
  margin: 0 5px;
  color: #a6a6a6;
  line-height: 50px;
}

.sydhsx {
  width: 100%;
  height: 125px;
  background-color: #f3f4f6;
  margin: 0 auto;
  box-sizing: border-box;
  padding: 15px;
}
</style>
