<template>
    <my-container v-loading="pageLoading" style="height: 100%">
      <el-tabs v-model="activeName" style="height: 94%">
        <el-tab-pane label="淘工厂日报" name="first1" style="height: 100%">
          <productReportGC ref="productReportGC" style="height: 100%"></productReportGC>
        </el-tab-pane>
         <el-tab-pane label="订单日报" name="first2" :lazy="true" style="height: 100%" v-if="checkPermission('GCOrderDayReport')">
          <GCOrderDayReport @ChangeActiveName2="ChangeActiveName2"  ref="GCOrderDayReport" style="height: 100%"></GCOrderDayReport>
        </el-tab-pane>
        <el-tab-pane label="编码日报" name="first3" :lazy="true" style="height: 100%" v-if="checkPermission('GCGoodCodeDayReport')">
          <GCGoodCodeDayReport @ChangeActiveName2="ChangeActiveName2"  @ChangeActiveName="ChangeActiveName"  ref="GCGoodCodeDayReport" style="height: 100%"></GCGoodCodeDayReport>
        </el-tab-pane>
        <el-tab-pane label="ID日报" name="first4" :lazy="true" style="height: 100%" v-if="checkPermission('GCIdDayReport')">
          <GCIdDayReport @ChangeActiveName2="ChangeActiveName2"  @ChangeActiveName="ChangeActiveName"  ref="GCIdDayReport" style="height: 100%"></GCIdDayReport>
        </el-tab-pane>
        <el-tab-pane label="店铺日报" name="first5" :lazy="true" style="height: 100%" v-if="checkPermission('GCShopDayReport')">
          <GCShopDayReport @ChangeActiveName2="ChangeActiveName2"  @ChangeActiveName="ChangeActiveName"  ref="GCShopDayReport" style="height: 100%"></GCShopDayReport>
        </el-tab-pane>
        <el-tab-pane label="店铺SKU日报" name="first7" :lazy="true" style="height: 100%" v-if="checkPermission('GCCommodityDayReport')">
          <GCCommodityDayReport @ChangeActiveName2="ChangeActiveName2"  @ChangeActiveName="ChangeActiveName"  ref="GCCommodityDayReport" style="height: 100%"></GCCommodityDayReport>
        </el-tab-pane>
        <el-tab-pane label="明细日报" name="first6" :lazy="true" style="height: 100%" v-if="checkPermission('GCDetailDayReport')">
          <GCDetailDayReport @ChangeActiveName2="ChangeActiveName2"  @ChangeActiveName="ChangeActiveName"  ref="GCDetailDayReport" style="height: 100%"></GCDetailDayReport>
        </el-tab-pane>
        <el-tab-pane label="出仓负利润ID订单明细" name="first8" :lazy="true" style="height: 100%" v-if="checkPermission('GCOutgoingprofitIDorderdetail')">
          <GCOutgoingprofitIDorderdetail @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName" ref="GCOutgoingprofitIDorderdetail" style="height: 100%"></GCOutgoingprofitIDorderdetail>
        </el-tab-pane>
        <el-tab-pane label="SKUS日报" name="first9" :lazy="true" style="height: 100%" v-if="checkPermission('GCOrderDayReport')">
          <GCSkusDayReport @ChangeActiveName2="ChangeActiveName2"  ref="GCSkusDayReport" style="height: 100%"></GCSkusDayReport>
        </el-tab-pane>
      </el-tabs>
    </my-container>
  </template>

  <script>
  import MyContainer from "@/components/my-container";
  import productReportGC from "./productReportGC.vue";
  import GCOrderDayReport from "./GCOrderDayReport.vue";
  import GCSkusDayReport from "./GCSkusDayReport.vue";
  import GCGoodCodeDayReport from "./GCGoodCodeDayReport.vue";
  import GCIdDayReport from "./GCIdDayReport.vue";
  import GCCommodityDayReport from "./GCCommodityDayReport.vue";
  import GCOutgoingprofitIDorderdetail from "./GCOutgoingprofitIDorderdetail.vue";
  import GCShopDayReport from "./GCShopDayReport.vue";
  import GCDetailDayReport from "./GCDetailDayReport.vue";
  import middlevue from "@/store/middle.js"  

  export default {
    name: "productReportGCIndex",
    components: {
      MyContainer, productReportGC,GCOrderDayReport,GCSkusDayReport,GCGoodCodeDayReport,GCIdDayReport,GCShopDayReport,GCDetailDayReport,GCCommodityDayReport,GCOutgoingprofitIDorderdetail
    },
    data() {
      return {
        that: this,
        pageLoading: false,
        activeName: "first1",
      };
    },
    async mounted() {
      middlevue.$on('toLinkTxDetailDayReport', (data) => {
      if (data.isTrue && data.plat == 'tgc') {
        this.activeName = 'first6';
        setTimeout(() => {
          middlevue.$emit('toLinkTxDetailDayReportQuery', data)
        }, 500);

      } else {
        this.activeName = 'first8';
        setTimeout(() => {
          middlevue.$emit('toLinkTxOutgoingprofitIDorderdetailQuery', data)
        }, 500);
      }
    })
    },
    methods: {
      ChangeActiveName(activeName){
        this.activeName = 'first2';
        this.$refs.GCOrderDayReport.TgcGoodCodeDayReportArgument(activeName)
      },
      ChangeActiveName2(activeName,No,Time){
        this.activeName = 'first6';
        this.$refs.GCDetailDayReport.TgcDetailDayReportArgument(activeName,No,Time)
      }
    },
  };
  </script>

  <style lang="scss" scoped></style>
