<template>
    <container>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='false' tablekey="ordernoexpressfee"
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry' :isSelectColumn="true"
              :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                    <el-select
                    v-model="filter.platform"
                    placeholder="平台" style="width: 100px"  @change="changePlatform"
                    :clearable="true" :collapse-tags="true"  filterable>
                    <el-option
                        v-for="item in platformList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"/>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-select
                    v-model="filter.shopCode" @change="onSearch"
                    placeholder="店铺" style="width: 250px"
                    :clearable="true" :collapse-tags="true"  filterable>
                    <el-option
                        v-for="item in shopList"
                        :key="item.shopCode"
                        :label="item.shopName"
                        :value="item.shopCode"/>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-date-picker style="width: 240px"
                        v-model="filter.timerange"
                        type="daterange"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        range-separator="至"
                        start-placeholder="导入时间"
                        end-placeholder="导入时间"
                        :picker-options="pickOptions" @change="onSearch"
                    ></el-date-picker>
                </el-button>            
                <el-button style="padding: 0;margin: 0;"><el-input style="width: 200px" v-model="filter.batchNumber"  placeholder="批次号" @change="onSearch"/></el-button>
                <el-button style="padding: 0;margin: 0;"><el-input style="width: 200px" v-model="filter.orderNo"  placeholder="原始订单号" @change="onSearch"/></el-button>             
                </el-button-group>
            </template>
        </ces-table> 
        <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
        </template>
    </container>
</template>

<script>
import {pageOrderWithholdFee} from '@/api/financial/ordercost'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import {formatPlatform,formatTime} from "@/utils/tools"

const tableCols =[
      {istrue:true,prop:'setMonth',label:'结算月份', width:'120',sortable:'custom'},
      {istrue:true,prop:'platform',label:'平台', width:'100',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
      {istrue:true,prop:'shopCode',label:'店铺', width:'180',sortable:'custom',formatter:(row)=>row.shopName},
      {istrue:true,prop:'expressNo',label:'快递单号', width:'200',sortable:'custom'},
      {istrue:true,prop:'orderNo',label:'原始订单号', width:'200',sortable:'custom'},
      {istrue:true,prop:'withholdFee',label:'扣款金额', width:'120',sortable:'custom'},
      {istrue:true,prop:'computeStatus',label:'工资月报计算状态', width:'110',sortable:'custom',formatter:(row)=>{return row.computeStatus==0?'未计算':row.computeStatus==1?'已计算':'预估'}},
      {istrue:true,prop:'referComputeStatus',label:'参考月报计算状态', width:'110',sortable:'custom',formatter:(row)=>{return row.referComputeStatus==0?'未计算':row.referComputeStatus==1?'已计算':'预估'}},
      {istrue:true,prop:'createdTime',label:'导入时间', width:'150',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD HH:mm:ss')},
      {istrue:true,prop:'batchNumber',label:'批次号', width:'180',sortable:'custom'}
     ];
const tableHandles=[
        {label:"导入", handle:(that)=>that.onimport()},
        {label:"导出", handle:(that)=>that.onExport()},
        {label:"删除", handle:(that)=>that.onbatchDelete()},
        {label:"刷新", handle:(that)=>that.getlist()},
      ];
export default {
    name: 'YunhanAdminWithholdfree',
    components: {container, cesTable},
    props:{
        filter: {},
        shopList:[],
        platformList:[]
    },

    data() {
        return {
            shareFeeType:0,
            that:this,
            list: [],
            tableCols:tableCols,
            tableHandles:tableHandles,
            pager:{OrderBy:"id",IsAsc:false},
            summaryarry:{},
            total:0,
            sels: [],
            selids: [], 
            listLoading: false,
            pageLoading: false,
            pickOptions:{
                disabledDate(time){
                return time.getTime() > Date.now()
                }
            },
        };
    },

    async mounted() {
        await this.onSearch()
    },

    methods: {
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        async getlist() {
            if(!this.filter.settMonth){
                this.$message({message: "请选择结算月份", type: "warning" });
                return false;
            }
            this.filter.startTime=null;
            this.filter.endTime=null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            var pager = this.$refs.pager.getPager()
            this.filter.shareFeeType=this.shareFeeType;
            const params = {...pager, ...this.pager, ... this.filter}
            this.listLoading = true
            const res = await pageOrderWithholdFee(params)
            this.listLoading = false
            if (!res?.success) return 
            this.total = res.data.total
            const data = res.data.list
            data.forEach(d => {
                d._loading = false
            })
            this.list = data
            this.summaryarry=res.data.summary;
        },
        sortchange(column){
            if(!column.order)
                this.pager={};
            else
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
            this.onSearch();
        },
        async onimport(){
            await this.$emit('onstartImport',this.shareFeeType);
        },
        selsChange: function(sels) {
            this.sels = sels
        },
        selectchange:function(rows,row) {
            this.selids=[];
            rows.forEach(f=>{
                this.selids.push(f.id);
            })
        },
        changePlatform(){
            this.$emit("changePlatform",this.filter.platform);
            this.onSearch();
        },
        onExport(){
            if(!this.filter.shopCode){
                this.$message({message:"请先选择店铺",type:"warning"});
                return false;
            }
            this.$emit("onExport");
        },
        async onbatchDelete() {
            await this.$emit('ondeleteByBatch',this.shareFeeType);
        },
    },
};
</script>

<style lang="scss" scoped>

</style>