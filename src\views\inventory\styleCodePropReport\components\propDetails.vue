<template>
    <MyContainer >
        <template #header>
            <div class="header_top">
                <el-radio-group v-model="ListInfo.queryType" @change="changeQueryType">
                    <el-radio-button :label="0">全部</el-radio-button>
                    <el-radio-button :label="1">系列编码</el-radio-button>
                    <el-radio-button :label="2">平台</el-radio-button>
                    <el-radio-button :label="3">运营组</el-radio-button>
                    <el-radio-button :label="4">一级类目</el-radio-button>
                    <el-radio-button :label="5">二级类目</el-radio-button>
                </el-radio-group>
            </div>
            <div class="top">
                <el-select v-model="ListInfo.styleCodeList" multiple collapse-tags filterable remote reserve-keyword @change="selectedMethod"
                    placeholder="系列编码" clearable :remote-method="remoteMethod" :loading="searchloading" :key="styleCodeListKey"
                    class="publicCss">
                    <el-option v-for="(item, i) in options" :key="i" :label="item" :value="item">
                    </el-option>
                </el-select>
                <el-select class="publicCss" v-model="ListInfo.bzCategory1" placeholder="一级类目" :collapse-tags="true" v-if="ListInfo.queryType == 4"
                    remote :remote-method="remoteMethodCategoryName1s" clearable filterable multiple>
                    <el-option v-for="(item, i) in filterList.categoryName1s" :key="'categoryName1Level' + i + 1" :value="item" />
                </el-select>
                <el-select class="publicCss" v-model="ListInfo.bzCategory2" placeholder="二级类目" :collapse-tags="true" v-if="ListInfo.queryType == 5"
                    remote :remote-method="remoteMethodCategoryName2s" clearable filterable multiple>
                    <el-option v-for="(item, i) in filterList.categoryName2s" :key="'categoryName2Level' + i + 1" :value="item" />
                </el-select>
                <el-select v-model="ListInfo.platforms" placeholder="平台" :collapse-tags="true" filterable multiple v-if="ListInfo.queryType == 2"
                    class="publicCss" clearable>
                    <el-option v-for="item in platformdata" :label="item.label" :value="item.value" :key="item.value" />
                </el-select>
                <el-select filterable v-model="ListInfo.groupIdList" collapse-tags clearable placeholder="运营组" v-if="ListInfo.queryType == 3"
                    class="publicCss" multiple>
                    <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="exportProps">导出</el-button>
            </div>
        </template>
        <div style="height: 100%;">
          <vxetablebase :id="'propDetails202408041611'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :toolbarshow="false"
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false" v-loading="loading"
            :showsummary="true" :summaryarry="summary" :isSelectColumn="false" style="width: 100%;  margin: 0"
             :height="'100%'">
          </vxetablebase>
        </div>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog :title="title" :visible.sync="dialogVisable" width="60%" v-dialogDrag>
            <logDetails :popupType="popupType" v-if="dialogVisable" :queryType="this.ListInfo.queryType" :logDetailsQuery="logDetailsQuery" />
        </el-dialog>

        <el-dialog :title="trendChartProp.trendCharttitle" :visible.sync="trendChartProp.trendChartDialog" width="60%" v-dialogDrag>
          <div style="height: 100%;">
            <buschar v-if="!trendChartProp.trendChartLoading" :analysis-data="trendChartProp.trendChartdata" />
          </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatPlatform, platformlist } from '@/utils/tools'
import logDetails from './logDetails.vue'
import { getListByStyleCode } from "@/api/inventory/basicgoods"
import { pageContinuLossesStyleCodeReport, getContinuLossesStyleCode, getSalesSalesAmountAnalysis, exportContinuLossesStyleCodeReport } from '@/api/operatemanage/continuLosses'
import { getBusinessCategorySelectData } from '@/api/operatemanage/base/category'
import buschar from '@/components/Bus/buschar'
import dayjs from 'dayjs'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'styleCode', label: '系列编码', type: 'click', handle: (that, row) => that.opendialog(row, '系列编码', 1) },
    { width: 'auto', align: 'center', prop: 'platformStr', label: '平台', formatter: (row) => formatPlatform(row.platform), type: 'click', handle: (that, row) => that.opendialog(row, '平台', 3)},
    { width: 'auto', align: 'center', prop: 'groupName', label: '运营组', type: 'click', handle: (that, row) => that.opendialog(row, '运营组', 2) },
    { width: 'auto', align: 'center', prop: 'bzCategory1', label: '一级类目', type: 'click', handle: (that, row) => that.opendialog(row, '一级类目', 4) },
    { width: 'auto', align: 'center', prop: 'bzCategory2', label: '二级类目', type: 'click', handle: (that, row) => that.opendialog(row, '二级类目', 5) },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'day30OrderCount', label: '近30天订单数', type: 'click', handle: (that, row, column, cell) => that.trichosansell(row, column, cell)},
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'day30SalesAmount', label: '近30天销售额', type: 'click', handle: (that, row, column, cell) => that.trichosansell(row, column, cell)},
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'day30Profit3', label: '毛三利润', type: 'click', handle: (that, row, column, cell) => that.trichosansell(row, column, cell)},
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'day30Profit3Rate', label: '毛三率', formatter: (row) => row.day30Profit3Rate ? row.day30Profit3Rate + '%' : null, type: 'click', handle: (that, row, column, cell) => that.trichosansell(row, column, cell)},
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, logDetails, buschar
    },
    data() {
        return {
            trendChartProp: {
                trendCharttitle: '',
                trendChartDialog: false, // 趋势图弹窗
                trendChartLoading: true, // 趋势图loading
                trendChartdata: {}// 趋势图数据
            },
            styleCodeListKey: 0,
            platformlist,
            platformdata: [],
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                queryType: 0,
                styleCode: null,
                // bzCategory1: null,
                // bzCategory2: null,
                platform: null,
                groupId: null,
                styleCodeList: [],
                bzCategory1: [],
                bzCategory2: [],
                platforms: [],
                groupIdList: []
            },
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            isExport: false,
            grouplist: [],
            dialogVisable: false,
            title: '',
            options: [],
            searchloading: false,
            filterList: {
                bussinessCategoryNames: [],
                categoryName1s: [],
                categoryName2s: []
            },
            summary: {},
            logDetailsQuery: {
                popupType: null,//弹窗类型
                styleCode: null,//系列编码
                groupId: null,//运营组
                platform: null,//平台
                bzCategory1: null,//一级类目
                bzCategory2: null,//二级类目
            }
        }
    },
    async mounted() {
        this.init()
        await this.getList()
    },
    methods: {
        //在选择器模糊搜索输入的值，在选择数据后，清空输入框（）
        selectedMethod(e) {
          this.styleCodeListKey += 1;
        },
        async exportProps() {
          this.loading = true
          const params = this.onTypeConversion()
          for (const key in params) {
            if (Array.isArray(params[key])) {
              params[key] = params[key].join(',')
            }
          }
          const { data } = await exportContinuLossesStyleCodeReport(params)
          const aLink = document.createElement("a");
          let blob = new Blob([data], { type: "application/vnd.ms-excel" })
          aLink.href = URL.createObjectURL(blob)
          aLink.setAttribute('download', '数据明细数据' + new Date().toLocaleString() + '.xlsx')
          aLink.click()
          this.loading = false
        },
        async trichosansell(row, column, cell) {
          const params  = {...row, selectColumn : column.prop}
          this.trendChartProp.trendChartLoading = true
          const { data, success } = await getSalesSalesAmountAnalysis(params)
          if (success) {
            this.trendChartProp.trendCharttitle = column.label + '趋势图'
            this.trendChartProp.trendChartdata = data
            this.trendChartProp.trendChartDialog = true
          }
          this.trendChartProp.trendChartLoading = false
        },
        async changeQueryType(e) {
            // 3.1全部：显示列表全部字段
            // 3.2系列编码：列：隐藏平台、运营组、一级类目、二级类目
            // 3.3平台：列：隐藏运营组、一级类目、二级类目，
            // 3.4运营组：列：隐藏一级类目、平台、二级类目
            // 3.5一级类目：列：隐藏平台、运营组、二级类目
            // 3.6二级类目：列：隐藏平台、运营组、一级类目
            const columns = {
                platformStr: 'platformStr',
                groupName: 'groupName',
                bzCategory1: 'bzCategory1',
                bzCategory2: 'bzCategory2'
            };
            const hideColumns = {
                0: [],
                1: ['platformStr', 'groupName', 'bzCategory1', 'bzCategory2'],
                2: ['groupName', 'bzCategory1', 'bzCategory2'],
                3: ['platformStr', 'bzCategory1', 'bzCategory2'],
                4: ['platformStr', 'groupName', 'bzCategory2'],
                5: ['platformStr', 'groupName', 'bzCategory1']
            };
            const showColumns = {
                0: ['platformStr', 'groupName', 'bzCategory1', 'bzCategory2'],
                1: [],
                2: ['platformStr'],
                3: ['groupName'],
                4: ['bzCategory1'],
                5: ['bzCategory2']
            };
            this.$nextTick(() => {
                //隐藏列
                hideColumns[e].forEach(field => {
                    this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField(columns[field]));
                });
                // 显示列
                showColumns[e].forEach(field => {
                    this.$refs.table.$refs.xTable.showColumn(this.$refs.table.$refs.xTable.getColumnByField(columns[field]));
                });
            })
            await this.getList()
        },
        remoteMethod(query) {
            if (query !== '') {
                this.searchloading == true
                this.options = [];
                setTimeout(async () => {
                    const params = {styleCode: query}
                    const { data } = await getContinuLossesStyleCode(params)
                    this.searchloading = false
                    this.options = data
                }, 200)
            }
            else {
                this.options = []
            }
        },
        remoteMethodCategoryName2s(query) {
            if (!query) {
              return;
            }
            query = query.replace(/(^\s*)|(\s*$)/g, "");
            this.searchloading == true;
            setTimeout(async () => {
                const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 3, categoryName: query })
                this.searchloading = false
                this.filterList.categoryName2s = res.data
            }, 200)
        },
        remoteMethodCategoryName1s(query) {
            if (!query) {
              return;
            }
            query = query.replace(/(^\s*)|(\s*$)/g, "");
            this.searchloading == true;
            setTimeout(async () => {
                const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 2, categoryName: query })
                this.searchloading = false
                this.filterList.categoryName1s = res.data
            }, 200)
        },
        opendialog(row, title, popupType) {
            const map = {
                1: 'styleCode',
                2: 'groupId',
                3: 'platform',
                4: 'bzCategory1',
                5: 'bzCategory2'
            }
            for (const key in this.logDetailsQuery) {
                if (key == map[popupType]) {
                    this.logDetailsQuery[key] = row[map[popupType]]
                } else {
                  if(key != 'styleCode'){
                    this.logDetailsQuery[key] = null
                  }
                }
            }
            this.logDetailsQuery.popupType = popupType
            console.log(row.styleCode, 'row.styleCode');
            this.logDetailsQuery.styleCode = row.styleCode
            this.popupType = popupType
            this.title = title
            this.dialogVisable = true
        },
        async init() {
            this.platformdata = this.platformlist.filter(item => item.label !== '希音' && item.label !== '拼多多跨境')
            const { data } = await getDirectorGroupList();
            this.grouplist = data?.map(item => { return { value: item.key, label: item.value }; });
        },
        onTypeConversion(){
            let bzCategory2 = this.ListInfo.bzCategory2
            let styleCodeList = this.ListInfo.styleCodeList
            let bzCategory1 = this.ListInfo.bzCategory1
            let platforms = this.ListInfo.platforms
            let groupIdList = this.ListInfo.groupIdList
            //直接使用join方法会转为字符串，因为双向绑定了数据会回显失败，所以使用了let变量
            this.ListInfo.styleCode = styleCodeList?.length ? styleCodeList.join(',') : null
            this.ListInfo.platform =platforms?.length ?platforms.join(',') : null
            this.ListInfo.groupId = groupIdList?.length ? groupIdList.join(',') : null
            bzCategory1 = this.ListInfo.bzCategory1?.length ? this.ListInfo.bzCategory1.join(',') : null
            bzCategory2 = bzCategory2?.length ? bzCategory2.join(',') : null
            return {...this.ListInfo,bzCategory2,bzCategory1}
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            const params = this.onTypeConversion()
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data: { list, total, summary }, success } = await pageContinuLossesStyleCodeReport(params)
                if (success) {
                    this.tableData = list
                    this.total = total
                    this.summary = summary

                } else {
                    this.$message.error('获取列表失败')
                }
                this.loading = false
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 220px;
        margin-right: 10px;
    }
}

.header_top {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    padding-right: 10px;
}
::v-deep .el-select__tags-text {
  max-width: 70px;
}
</style>
