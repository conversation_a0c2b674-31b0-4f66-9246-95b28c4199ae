<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.goodsNumber" placeholder="商品编号" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.ddBzId" placeholder="审批编号" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.applicantUser" placeholder="申请人" maxlength="50" clearable class="publicCss" />
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="申请开始时间" end-placeholder="申请结束时间" :picker-options="pickerOptions"
          style="width: 230px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-select v-model="ListInfo.applicantUserDept" placeholder="申请部门" class="publicCss" clearable filterable>
          <el-option v-for="item in deptList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="ListInfo.status" placeholder="审批状态" class="publicCss" clearable filterable>
          <el-option :key="'1'" label="发起" value="1" />
          <el-option :key="'2'" label="通过" value="2" />
          <el-option :key="'0'" label="打回或取消" value="0" />
        </el-select>
        <el-select v-model="ListInfo.claimStatus" placeholder="领取状态" class="publicCss" clearable filterable>
          <el-option :key="'1'" label="待领取" :value="1" />
          <el-option :key="'2'" label="已领取" :value="2" />
          <el-option :key="'3'" label="发起中" :value="3" />
          <el-option :key="'4'" label="已归还" :value="4" />
          <el-option :key="'0'" label="取消/驳回" :value="0" />
        </el-select>
        <el-select v-model="ListInfo.isYuQi" placeholder="是否逾期" class="publicCss" clearable filterable>
          <el-option :key="'1'" label="是" :value="1" />
          <el-option :key="'0'" label="否" :value="0" />
        </el-select>
        <number-range :min.sync="ListInfo.startDaysOverdue" :max.sync="ListInfo.endLendEndTime" min-label="逾期开始天数"
          max-label="逾期结束天数" class="publicCss" style="width: 165px;" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
      </div>
    </template>
    <vxetablebase :id="'receiptRecord202503251030'" :tablekey="'receiptRecord202503251030'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getSampleReceiveApplication } from '@/api/inventory/sampleGoods';
import { pickerOptions } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'applicantUserDept', label: '申请部门', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'applicantUser', label: '申请人', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'applicantUseDept', label: '使用部门', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouse', label: '样品所在仓库', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsNumber', label: '商品编号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'bitNumber', label: '库位', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'estimatedServiceLife', label: '预计归还时间', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'instanceId', label: '审批编号', },
  {
    sortable: 'custom', width: 'auto', align: 'center', prop: 'status', label: '审批状态', formatter: (row) => {
      return row.status == 1 ? '发起' : row.status == 2 ? '通过' : row.status == 0 ? '打回或取消' : ''
    }
  },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '发起时间', },
  {
    sortable: 'custom', width: 'auto', align: 'center', prop: 'claimStatus', label: '领取状态', formatter: (row) => {
      return row.claimStatus == 1 ? '待领取' : row.claimStatus == 2 ? '已领取' : row.claimStatus == 3 ? '发起中' : row.claimStatus == 4 ? '已归还' : row.claimStatus == 0 ? '取消/驳回' : ''
    }
  },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'claimTime', label: '领取时间', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'actualReturnTime', label: '归还时间', },
]
export default {
  name: "receiptRecord",
  components: {
    MyContainer, vxetablebase, numberRange
  },
  props: {
    deptList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'createdTime',
        isAsc: false,
        createdTimeStar: null,//开始时间
        createdTimeEnd: null,//结束时间
        goodsCode: null,//商品编码
        goodsName: null,//商品名称
        goodsNumber: null,//商品编号
        ddBzId: null,//审批编号
        applicantUser: null,//申请人
        applicantUserDept: null,//申请部门
        status: null,//审批状态
        claimStatus: null,//领取状态
        endLendEndTime: null,//逾期结束天数
        startDaysOverdue: null,//逾期开始天数
        isYuQi: null,//是否逾期
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async changeTime(e) {
      this.ListInfo.createdTimeStar = e ? e[0] : null
      this.ListInfo.createdTimeEnd = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getSampleReceiveApplication(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 120px;
    margin-right: 5px;
  }
}
</style>
