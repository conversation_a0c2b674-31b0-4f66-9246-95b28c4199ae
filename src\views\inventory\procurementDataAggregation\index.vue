<template>
  <container>
    <el-tabs v-model="activeName" style="height: 95%;">
      <el-tab-pane label="行政数据汇总" name="first" style="height: 100%;">
        <inboundShootingDataSum />
      </el-tab-pane>
      <el-tab-pane label="商品编码汇总" name="second" style="height: 100%;" lazy>
        <productSum />
      </el-tab-pane>
      <el-tab-pane label="降价数据汇总" name="third" style="height: 100%;" lazy>
        <markdownDataSum />
      </el-tab-pane>
      <el-tab-pane label="专员明细" name="four" style="height: 100%;" lazy>
        <profit3CommissionPercentage />
      </el-tab-pane>
      <el-tab-pane label="小组明细" name="five" style="height: 100%;" lazy
        v-if="checkPermission('ProcurementDataAggregationGroupDetails')">
        <groupDetail />
      </el-tab-pane>
      <el-tab-pane label="缺货次数汇总" name="six" style="height: 100%;" lazy>
        <outOfStockFrequencySum />
      </el-tab-pane>
    </el-tabs>
  </container>
</template>

<script>
import container from "@/components/my-container";
import productSum from './components/productSum.vue';
import markdownDataSum from './components/markdownDataSum.vue';
import inboundShootingDataSum from './components/inboundShootingDataSum.vue';
import profit3CommissionPercentage from './components/profit3CommissionPercentage.vue';
import groupDetail from './components/groupDetail.vue';
import outOfStockFrequencySum from './components/outOfStockFrequencySum.vue';
export default {
  name: 'procurementDataAggregationIndex',
  components: { container, productSum, markdownDataSum, inboundShootingDataSum, profit3CommissionPercentage, groupDetail, outOfStockFrequencySum },

  data() {
    return {
      activeName: 'first'
    };
  },

  async mounted() {

  },

  methods: {
    handleClick(tab, event) {
    }
  },
};
</script>


<style lang="scss" scoped></style>
