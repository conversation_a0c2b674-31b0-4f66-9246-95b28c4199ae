<template>
  <my-container v-loading="pageLoading">
    <template #header>
     <el-form :inline="true" :model="filter" @submit.native.prevent>
      <el-form-item label="商品编码:"><el-input v-model="filter.goodsCode" /></el-form-item>
      <el-form-item label="商品名称:"><el-input v-model="filter.goodsName" /></el-form-item>
      <el-form-item label="发货仓库:">
          <el-select v-model="filter.sendWarehouse" placeholder="发货仓库">
            <el-option label="所有" value></el-option>
            <el-option label="本仓" value="1"></el-option>
            <el-option label="义乌四楼" value="2"></el-option>
            <el-option label="昌东" value="3"></el-option>
            <el-option label="安徽" value="7"></el-option>
            <el-option label="跨境" value="5"></el-option>
          </el-select>
        </el-form-item>
      <el-form-item label="仓库区域:">
          <el-select v-model="filter.warehouseArea" placeholder="仓库区域">
            <el-option label="所有" value></el-option>
            <el-option label="义乌" value="0"></el-option>
            <el-option label="昌东" value="1"></el-option>
            <el-option label="安徽" value="3"></el-option>
            <el-option label="跨境" value="5"></el-option>
          </el-select>
        </el-form-item>
      <el-form-item><el-button type="primary" @click="onSearch">查询</el-button></el-form-item>
    </el-form>
  </template>
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
        :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>

   <el-dialog title="导入数据" :visible.sync="importVisible" width="30%">
      <span>
        <el-upload ref="upload" class="upload-demo"
          :auto-upload="false" :multiple="false" action accept=".xlsx"
          :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}   </el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importVisible = false">关闭</el-button>
      </span>
    </el-dialog>
 
  </my-container>
</template>

<script>
import {pageWarehouseGoods,importWarehouseGoodsCode} from '@/api/inventory/abnormal'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatSendWarehouse,formatNoLink,formatTime,formatWarehouseArea} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'goodsCode',label:'商品编码', width:'120',sortable:'custom'},
      {istrue:true,prop:'goodsName',label:'商品名称', width:'200',sortable:'custom'},
      {istrue:true,prop:'sendWarehouse',label:'发货仓', width:'125',sortable:'custom',formatter:(row)=>formatSendWarehouse(row.sendWarehouse)},
      {istrue:true,prop:'warehouseArea',label:'仓库区域', width:'125',sortable:'custom',formatter:(row)=>formatWarehouseArea(row.warehouseArea)},
      {istrue:true,prop:'createdTime',label:'导入日期', width:'165',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD HH:mm:ss')},
     ];
const tableHandles=[{label:"导入", handle:(that)=>that.startImport()}];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton},
  data() {
    return {
      that:this,
      filter: {
        goodsCode:null, 
        goodsName:null,
        sendWarehouse:null,
        warehouseArea:null,
      },
      list: [],
      detaillist:[],
      brandlist:[],
      tableCols:tableCols,
      tableHandles:tableHandles,
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      importVisible: false,
      pager:{OrderBy:"",IsAsc:false},
      summaryarry:{},
      selids: [],
      fileList:[],
      listLoading: false, 
      pageLoading: false,
      uploadLoading:false
    }
  },
  async mounted() {
    this.getlist();
  },
  beforeUpdate() {
    console.log('update')
  },
  watch: {
    value(n) {
      if(n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);
      }
    }
  },
  methods: {
    async onSearch() {      
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      this.listLoading = true
      const res = await pageWarehouseGoods(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
  async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
  async onEdit(row) {
      this.formtitle='编辑';
      this.addFormVisible = true
      const res = await getWarehousesigle({id:row.id})
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
         this.autoform.fApi.resetFields()
      await this.autoform.fApi.setValue(res.data)
    },
    startImport() {
      this.importVisible = true;
    },
    cancelImport() {
      this.importVisible = false;
    },
    beforeRemove() {
      return false;
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
   async submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.fileHasSubmit=true;
      this.uploadLoading=true;
      this.$refs.upload.submit();
    },
   async uploadFile(item) {
      if(!this.fileHasSubmit){
        return false;
      }
      this.fileHasSubmit=false;
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      const res =await importWarehouseGoodsCode(form);
      if (res.code==1)   this.$message({ message: "上传成功,正在导入中...", type: "success" });
      else  this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading=false; 
    },
   async uploadChange(file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for(var i=0;i<fileList.length;i++){
          if(fileList[i].status=="success")
            list.push(fileList[i]);
          else
            list.push(fileList[i].raw);
        }
        this.fileList = list;
      }
    },
   async uploadRemove(file, fileList){
       this.uploadChange(file, fileList);
    },
    selsChange: function(sels) {
      this.sels = sels
    }
  }
}
</script>
