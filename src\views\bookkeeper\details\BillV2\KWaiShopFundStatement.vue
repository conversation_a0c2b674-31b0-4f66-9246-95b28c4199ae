<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" :clearable="false"
          start-placeholder="数据开始时间" end-placeholder="数据结束时间" :picker-options="pickerOptions"
          style="width: 300px;margin-right: 10px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-input v-model="ListInfo.shopName" placeholder="店铺" maxlength="50" clearable class="publicCss" />
        <el-input v-model="ListInfo.billType" placeholder="账单项目" maxlength="50" clearable class="publicCss" />
        <el-input v-model="ListInfo.orderNos" placeholder="关联业务单号" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" style="margin-left: 10px;" @click="exportProps">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'KWaiShopFundStatement202408041359'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0"
      :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="display: flex; align-items: baseline; height: 75px;margin-top: 10px;">
        <el-date-picker style="width: 150px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay" type="date"
          placeholder="选择日期" :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
        </el-date-picker>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getKsFundsStatementDetail, importKsFundsStatementDetailAsync ,exportKWaiShopFundStatementList} from '@/api/bookkeeper/reportdayV2'
import { formatTime } from "@/utils/tools";
import dayjs from 'dayjs'

const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'accountingSerialNumber', label: '账务流水号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '关联业务单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'dateTime', label: '入账时间', formatter: (row) => formatTime(row.dateTime ? row.dateTime : row.entryTime ? row.entryTime : '', 'YYYY-MM-DD'), },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'accountingDirection', label: '账务方向', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'accrual', label: '发生额（元）', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'endaccrual', label: '期末余额（元）', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'businessType', label: '业务类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'billType', label: '账单项目', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'description', label: '描述', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'remark', label: '备注', },
]
export default {
  name: "KWaiShopFundStatement",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      yearMonthDay: null,
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      summaryarry: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startDate: null,//开始时间
        endDate: null,//结束时间
        shopName: null,//店铺
        billType: null,//账单项目
        orderNos: null,//关联业务单号
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async exportProps() {
      this.loading = true
      const { data } = await exportKWaiShopFundStatementList(this.ListInfo)
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '快手资金账单费用数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("YearMonthDay", dayjs(this.yearMonthDay).format('YYYYMMDD'));
      form.append("upfile", item.file);
      var res = await importKsFundsStatementDetailAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (!this.yearMonthDay) {
        this.$message({ message: "请选择日期", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async changeTime(e) {
      this.ListInfo.startDate = e ? e[0] : null
      this.ListInfo.endDate = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给昨天时间
        this.ListInfo.startDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
      }
      this.loading = true
      const { data, success } = await getKsFundsStatementDetail(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}
</style>
