<template>
    <MyContainer>
        <el-form ref="form" :model="data" label-width="80px">
            <el-form-item label="打包人">
                <el-input placeholder="请输入内容" v-model="data.prePackUserName" style="width: 200px;" maxlength="20"
                    disabled>
                    <el-button slot="append" icon="el-icon-edit" @click="handleEdit(false)"></el-button>
                </el-input>
            </el-form-item>
            <el-form-item label="完成时间">
                <el-date-picker v-model="data.prePackTime" type="datetime" placeholder="选择日期时间"
                    value-format="yyyy-MM-dd HH:mm:ss" style="width: 200px;">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="加工数量">
                <el-input-number v-model="data.prePackQty" :max="9999" label="加工数量" :controls="false" :precision="0"
                    style="width: 200px;" />
            </el-form-item>
        </el-form>
        <div class="btnGroup">
            <el-button type="primary" @click="submit">保存</el-button>
            <el-button @click="close">取消</el-button>
        </div>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { getManifestInfo, cptManifestInfo } from '@/api/inventory/prepack'
import dayjs from 'dayjs'
export default {
    components: { MyContainer },
    props: {
        id: {
            type: String,
            default: null
        }
    },
    data() {
        return {
            data: { prePackQty: 0 }
        }
    },
    async mounted() {
        await this.getInfo(this.id)
    },
    methods: {
        close() {
            this.$emit('close')
        },
        async submit() {
            if (this.data.prePackQty < 0) return this.$message.error('加工数量不能小于0')
            if (!this.data.prePackQty)
                this.data.prePackQty = 0
            const { success } = await cptManifestInfo(this.data)
            if (success) {
                this.$message.success('保存成功')
                this.$emit('getList')
                this.$emit('close')
            }
        },
        async afterSave(afterSave) {
            const val = afterSave.map((item) => {
                return {
                    prePackDDId: item.ddUserId,
                    prePackUserName: item.userName
                }
            })
            if (val.length > 1) return this.$message.error('只能选一个用户')
            this.data.prePackUserName = val[0].prePackUserName
            this.data.prePackDDId = val[0].prePackDDId
        },
        handleEdit(isMore) {
            console.log(isMore, 'isMore');
            let _this = this
            this.$showDialogform({
                path: `@/views/base/arrPublicDialogPage.vue`,
                title: '选择人员',
                autoTitle: false,
                args: { isMore },
                height: 300,
                width: '1300px',
                callOk: _this.afterSave
            })
        },
        async getInfo(id) {
            const { data, success } = await getManifestInfo({ id })
            if (success) {
                if (!data.prePackTime) {
                    data.prePackTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
                }
                this.data = data
            }
        }
    }
}
</script>

<style scoped lang="scss">
.btnGroup {
    display: flex;
    justify-content: end;
}
</style>