<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-button-group> 
                    <el-button style="padding: 0;">
                        <el-input-number style=" width: 100px;" v-model="filter.shootingTaskId"   v-model.trim="filter.shootingTaskId" :min="1" :max="10000000" :controls="false" :precision="0"  placeholder="任务编号"  clearable></el-input-number>
                    </el-button>
                    <el-button style="padding: 0;width: 90px;"> 
                        <el-select v-model="filter.hasOverTime" placeholder="是否完成" style="width:100%;" clearable>
                            <el-option label="是" value="1"></el-option>
                            <el-option label="否" value="0"></el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;">
                            <el-input  style=" width: 120px;" v-model="filter.productShortName" v-model.trim="filter.productShortName"    placeholder="产品简称" @keyup.enter.native="onSearch" clearable />
                    </el-button>
                    <el-button style="padding: 0;">
                        <el-select    filterable v-model="filter.platform" placeholder="平台" clearable :collapse-tags="true" @change="onchangeplatform" style="width: 80px">
                            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding:0;">
                        <el-select  filterable v-model="filter.shopName" placeholder="店铺" clearable style="width: 130px">
                            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
                        </el-select>
                    </el-button> 
                    <el-button style="padding: 0;width: 160px;">
                       <el-select  v-model="filter.fpPhotoLqName"   multiple  collapse-tags="true"   placeholder="分配人" filterable clearable style="width:100px" >  
                            <el-option v-for="item in fpPhotoLqNameList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;width: 180px;"> 
                        <el-select v-model="filter.warehouse" placeholder="大货仓"  multiple  collapse-tags="true"  style="width:100%;" clearable>
                            <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button> 
                    <el-button style="padding: 0;">
                        <el-select  v-model="filter.operationGroup" :clearable="true"  placeholder="运营组"  filterable  style="width:100px">
                            <el-option v-for="item in groupList" :key="item.value" :label="item.label"  :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;width: 100px;">
                      <el-select  v-model="filter.dockingPeople" :clearable="true"  placeholder="对接人"  filterable  style="width:100px">
                            <el-option v-for="item in dockingPeopleList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button> 
                    <el-button type="primary" @click="onExeprotShootingTask"   v-if="checkPermission('api:media:shootingvideo:ExportShootingTaskReport')" >导出</el-button>
                    <el-button v-if="tasktype == 1 && checkPermission('api:media:shootingvideo:TaskShopActionAsync')" style="margin-left: 20px" @click="onMoveTask" icon="el-icon-share" >批量存档</el-button>
                    <el-button v-if="tasktype == 2 && checkPermission('api:media:shootingvideo:TaskRestartActionAsync')" style="margin-left: 20px" @click="onMoveTask" icon="el-icon-share" >批量重启</el-button>
                    <el-button v-if="tasktype == 3 && checkPermission('api:media:shootingvideo:TaskRestartActionAsync')" style="margin-left: 20px" @click="onMoveTask" icon="el-icon-share" >批量恢复</el-button>
                    

                    <el-button style=" margin-left: 1; padding: 0 1 0 1 ;width: 100px;" type="primary" v-if="checkPermission('shootingDropDownList')" >
                        <el-dropdown @command="handleCommand">
                            <span class="el-dropdown-link" style="font-size: 14;color: #fff;">
                                更多列表<i class="el-icon-arrow-down el-icon--right"></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="a">显示全部</el-dropdown-item>
                                <el-dropdown-item command="b">显示默认</el-dropdown-item>
                                <el-dropdown-item command="c">查看分配</el-dropdown-item>
                                <el-dropdown-item command="d">查看小组</el-dropdown-item>
                                <el-dropdown-item command="e">寄样时效</el-dropdown-item>
                                <el-dropdown-item command="f">拍摄时效</el-dropdown-item>
                                <el-dropdown-item command="g">隐藏操作</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </el-button>
                   
                    <!--下拉（完成时间，创建时间，到货日期，申请日期，）-->
                    <el-button style="padding: 0;margin: 0;"> 
                        <el-select v-model="filter.searchTimeType" placeholder="选择时间" style="width:100px;"  >
                            <el-option label="完成时间" value="1"></el-option>
                            <el-option label="创建时间" value="2"></el-option>
                        </el-select> 
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-date-picker style="width:240px;"
                            type="daterange" format="yyyy-MM-dd"  value-format="yyyy-MM-dd"   range-separator="至" start-placeholder="开启日期"  end-placeholder="结束日期"  
                            v-model="filter.createdtimerange" />
                    </el-button>
            </el-button-group>
        </template>
        <!--列表-->
        <ces-table :ref="tablekey" :that='that' :isIndex='false' :hasexpand='false' @select='selectchange'  @sortchange='sortchange'
            :tableData='tasklist'  :isSelection="checkPermission('shootSelect')" :tableCols='tableCols' :isSelectColumn ='true'
             :tablekey="tablekey" :selectColumnHeight="'0px'"  :customRowStyle="customRowStyle"
            :loading="listLoading" :summaryarry="summaryarry" :isBorder="false">
        
            <template slot='extentbtn'>
            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList"  :page-size="300" />
        </template>
        <!--编辑创建任务--> 
        <el-dialog :title="taskPageTitle"  :visible.sync="addTask" width="60%" :close-on-click-modal="false" :key="opentime"
            element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <shootingvideotaskeditfrom :key="opentime" ref="shootingvideotaskeditfrom"   :taskUrgencyList="taskUrgencyList"  
            :groupList="groupList"
             :warehouseList="warehouseList"
             :platformList="platformList" :islook='true'  :onCloseAddForm="onCloseAddForm"></shootingvideotaskeditfrom>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCloseAddForm()">取 消</el-button>
                    <my-confirm-button type="submit"  @click="onSubmit" v-show="!islook" />
                </span>
            </template>  
        </el-dialog>
        <!--上传文件-->
        <el-drawer title="上传成果文件" :key="opentime" :visible.sync="successfiledrawer" direction="rtl" 
            :wrapperClosable="true"  :close-on-press-escape ="true"
            :before-close="successfiledrawerClose" size="60%"  > 
            <shootinguploadaction  ref="successfileinfo" 
            :islook="islook"
            :rowinfo="selectRowKey"   style="height: 92%;width:100%"></shootinguploadaction>
            <div class="drawer-footer" >
                <el-button @click="successfiledrawer = false">取 消</el-button> 
            </div>
        </el-drawer>

         <!--查看上传文件并打分-->
         <el-drawer title="查看成果文件"  :key="opentime" :visible.sync="successfileshow" direction="ltr" 
         :wrapperClosable="true"  :close-on-press-escape ="true"
         size="50%"   > 
            <shootingvideotaskuploadsuccessfilesocre 
             ref="shootingvideotaskuploadsuccessfilesocre"
             :isOverList="tasktype == 1"
            :islook="islook"
            :clostfun ="successfiledrawerscoreClose"  :rowinfo="selectRowKey"  
             style="height: 92%;width:100%"></shootingvideotaskuploadsuccessfilesocre>
             <div class="drawer-footer" >
                <el-button  :loading="filesocreLoading" @click="successfileshow=false">关 闭</el-button> 
                <my-confirm-button type="submit"   :loading="filesocreLoading"   @click="onSubComputOutInfo" v-show="islook"/>
            </div> 
        </el-drawer>

        <!--查看上传附件-->
        <el-dialog title="查看参考" :key="opentime"  :visible.sync="viewReference" width="60%" :close-on-click-modal="true" 
            element-loading-text="拼命加载中"  v-dialogDrag v-loading="addLoading">
            <shootingvideotaskuploadfile  ref="shootingvideotaskuploadfile" :rowinfo="selectRowKey"></shootingvideotaskuploadfile>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="viewReference = false">关 闭</el-button> 
                </span>
            </template>
        </el-dialog>
          <!--查看详情-->
          <el-dialog title="查看备注" :key="opentime"  :visible.sync="viewReferenceRemark" width="60%" :close-on-click-modal="true" 
            element-loading-text="拼命加载中"  v-dialogDrag v-loading="addLoading">
            <shootingTaskRemark  ref="shootingTaskRemark" :rowinfo="selectRowKey"   :islook="true"></shootingTaskRemark>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="viewReferenceRemark = false">关 闭</el-button> 
               </span>
            </template>
        </el-dialog>

        <el-dialog title="任务下单记录" :visible.sync="dialogOrderDtlVisible" width='88%' v-dialogDrag :close-on-click-modal="false" :v-loading="dialogOrderDtlLoading" @close="dialogOrderDtlColsed">
            <el-row style="height:35px;line-height:35px; font-size: 16px;font-weight: bold;color: black;margin-bottom: 2px;">
                <span>下单发货信息：</span>
            </el-row>
            <el-container>
                <el-main style="height:192px;">
                    <ces-table ref="tablexdfhmain" :that='that' :isIndex='true' :hasexpand='true' :tableData='xdfhmainlist' :tableCols='xdfhmainTableCols' :loading="xdfhmainLoading" style="height:190px" :isSelectColumn="false" @cellclick="onxdfhmainCellClick">
                    </ces-table>
                </el-main>
            </el-container>
            <el-row style="height:35px;line-height:35px; font-size: 16px;font-weight: bold;color: black; margin-top: 10px;margin-bottom: 2px;">
                <span>下单发货商品明细信息：</span>
            </el-row>
            <el-container>
                <el-main style="height:262px;">
                    <ces-table ref="tablexdfhdtl" :that='that' :isIndex='true' :hasexpand='true' :tableData='xdfhdtllist' :tableCols='xdfhdtlTableCols' :loading="xdfhdtlLoading" style="height:260px" :isSelectColumn="false">
                    </ces-table>
                </el-main>
            </el-container>
        </el-dialog>

        <el-drawer title="物流跟踪" :visible.sync="drawervisible" direction="rtl" :append-to-body="true">
            <logistics ref="logistics"></logistics>
        </el-drawer>

        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px" v-dialogDrag>
            <orderLogPage ref="orderLogPage" :orderNoInner="sendOrderNoInner" style="z-index:10000;height:600px" />
        </el-dialog>
    </my-container>
</template>
<script> 
import cesTable from "@/components/Table/tableforvedio.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import 
{ 
    pageShootingViewTaskAsync,deleteShootingTaskActionAsync,taskShopActionAsync,taskRestartActionAsync,deleteTaskActionAsync,
    getShootingTaskOrderListById,exportShootingTaskReport
} from '@/api/media/ShootingVideo';
import uploadfile from '@/views/media/shooting/uploadfile' 
import shootinguploadaction from '@/views/media/shooting/shootinguploadaction' 
import shootingTaskRemark from '@/views/media/shooting/ShootingTaskRemark' 

import shootingvideotaskuploadfile from '@/views/media/shooting/shootingvideotaskuploadfile' 
import shootingvideotaskuploadsuccessfilesocre from '@/views/media/shooting/shootingvideotaskuploadsuccessfilesocre'
import { getTaskUrgencyList} from '@/api/media/vediotask';
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import { formatTime} from "@/utils"; 
import logistics from '@/components/Comm/logistics'
import { rulePlatform } from "@/utils/formruletools";  
import { getDirectorGroupList } from '@/api/operatemanage/base/shop';
import shootingvideotaskeditfrom from '@/views/media/shooting/shootingvideotaskeditfrom'
import orderLogPage from "@/views/order/logisticsWarning/orderLogPage.vue";

function detailstyle(that,row,column,prop)
{  
    if(row.markCssName=="0"){
        return "height: 18px; width: 18px; text-align: center; line-height: 18px; border-radius: 18px;  background: gray; color: white; display: inline-block;font-size: 10px";
    }else{
        return "height: 18px; width: 18px; text-align: center; line-height: 18px; border-radius: 18px;  background: green; color: white; display: inline-block;font-size: 10px";
    }
};

const tableCols = [
{ istrue: true,permission:'shootTaskId', prop: 'shootingTaskId', label: '编号', width: '50' , fixed: true },
    { istrue: true,permission:'shootProudctName', prop: 'productShortName', label: '产品简称', width: '160',  fixed: true  , type: "click", handle: (that, row) => that.openComputOutInfo(row) },
    { istrue: true,permission:'shootUrgency', prop: 'taskUrgencyName', label: '紧急程度', width: '80', fixed: true ,type: 'UrgencyButton'
        , handle: (that, row) => that.shootUrgencyCilck(row.taskUrgencyName ,row.shootingTaskId)    
    }, 
    { istrue: true, permission:'shootRemarks',prop: 'bz', label: '备注', width: '50' ,  type: "clickflag", fixed: true , handle: (that, row) => that.openTaskRmarkInfo(row) }, 
    
    { istrue: true,permission:'shootWarehouse',prop: 'warehouseStr', label: '大货仓', width: '100'   , fixed: true },
 
   
    { istrue: true, permission:'shootReference', type: "button", width: "50", label: '参考'   ,  
        btnList: [
            { label: "查看", handle: (that, row) => that.videotaskuploadfileDetal(row)   },
        ]
    },
  
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'},  
    //照片
    { istrue: true, summaryEvent: true, permission:'shootPhoto',prop: 'photoLqNameStr', label: '照片',  width: '65'    },
    

    { istrue: true, permission:'shootDays', prop: 'photoDaysStr', label: '天数', width: '53'  , }, 
    { istrue: true, permission:'shootCompleteDate', prop: 'photoOverTimeStr', width: '75',  label: '完成日期' },
 
    //视频 
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'},  
    { istrue: true,summaryEvent: true, permission:'shootVideo', prop: 'vedioLqNameStr', label: '视频',  width: "65" ,  },
    

    { istrue: true, permission:'shootDays', prop: 'vedioDaysStr', label: '天数', width: '53' }, 
    { istrue: true, permission:'shootCompleteDate',prop: 'vedioOverTimeStr',  label: '完成日期', width: '75', },
    { istrue: true, permission:'shootConfirmationPersion',prop: 'vedioConfirmNameStr', label: '确认人',  width: "65" , },
   { istrue: true, permission:'shootConfirm', prop: 'vedioConfirmBtnStr', label: '确认', width: '60', type: "click"
        , handle: (that, row) => that.ConfirmTaskInfo(row.vedioConfirmBtnStr,"2",row.shootingTaskId) }, 
    { istrue: true, permission:'shootConfirmationDate',prop: 'vedioConfirmTimeStr',  label: '确认日期', width: '75',},
    //微详情 
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'},  
    { istrue: true, summaryEvent: true, permission:'shootMicrodetails',prop: 'microDetailLqNameStr', label: '微详情',  width: "65"   , },
    

    { istrue: true, permission:'shootDays', prop: 'microDetailDaysStr', label: '天数', width: '53',    },

    { istrue: true, permission:'shootCompleteDate' ,prop: 'microDetailOverTimeStr', width: '75',    label: '完成日期' },
    { istrue: true, permission:'shootQuantity', prop: 'microDetailVedioCounts', width: '54',   label: '数量' },

    { istrue: true, permission:'shootConfirmationPersion', prop: 'microDetailConfirmNameStr', label: '确认人',  width: "65" ,  },
     { istrue: true, permission:'shootConfirm', prop: 'microDetailConfirmBtnStr', label: '确认', width: '60',   type: "click"
        , handle: (that, row) => that.ConfirmTaskInfo(row.microDetailConfirmBtnStr,"3",row.shootingTaskId) }, 
    { istrue: true, permission:'shootConfirmationDate', prop: 'microDetailConfirmTimeStr',  label: '确认日期', width: '75', }, 
    //详情页 
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'}, 
    { istrue: true, summaryEvent: true, permission:'shootDetailsPage', prop: 'detailLqNameStr', label: '详情页', width: "65", },

  
    { istrue: true, permission:'shootDays', prop: 'detailDaysStr', label: '天数', width: '53',  },
    { istrue: true, permission:'shootCompleteDate',prop: 'detailOverTimeStr', width: '75',  label: '完成日期' , },
    { istrue: true, permission:'shootConfirmationPersion', prop: 'detailConfirmNameStr', label: '确认人',  width: "65" ,  },
    { istrue: true, permission:'shootConfirm', prop: 'detailConfirmBtnStr', label: '确认', width: '60',   type: "click"
        , handle: (that, row) => that.ConfirmTaskInfo(row.detailConfirmBtnStr,"4",row.shootingTaskId) }, 
    { istrue: true, permission:'shootConfirmationDate',  prop: 'detailConfirmTimeStr',  label: '确认日期', width: '75', },

    //照片建模  
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'}, 
    { istrue: true, summaryEvent: true,  permission:'shootPhotoModeling', prop: 'modelPhotosLqNameStr',  width: "53",  label: '照片建模'},
 
    { istrue: true, permission:'shootDays', prop: 'modelPhotosDaysStr', label: '天数', width: '53' , },
    { istrue: true, permission:'shootCompleteDate', prop: 'modelPhotosOverTimeStr', width: '75',    label: '完成日期'},
    { istrue: true, permission:'shootSheets', prop: 'modelPhotoCounts', width: '53',    label: '张数'  },


    //视频建模 
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'},  
    { istrue: true,  summaryEvent: true, permission:'shootVideoModeling',prop: 'modelVideoLqNameStr', label: '视频建模',   width: "53" },
 

    { istrue: true, permission:'shootDays', prop: 'modelVideoDaysStr', label: '天数',   width: '53'  },
    { istrue: true, permission:'shootCompleteDate', prop: 'modelVideoOverTimeStr', width: '80',    label: '完成日期'  },
    { istrue: true, permission:'shootGs', prop: 'modelVedioCounts', width: '53',    label: '个数'  },
  
    //分配
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'}, 
    { istrue: true, summaryEvent: true, permission:'shootFpPhoto',  prop: 'fpPhotoLqNameStr',  width: '54',   label: '分配照片'},
    { istrue: true, summaryEvent: true, permission:'shootFpVedio',  prop: 'fpVideoLqNameStr',  width: '54',   label: '分配视频'},
    { istrue: true, summaryEvent: true, permission:'shootFpDetail',  prop: 'fpDetailLqNameStr', width: '54',   label: '分配详情'}, 
    { istrue: true, summaryEvent: true, permission:'shootFpModel',  prop: 'fpModelLqNameStr', width: '54',   label: '分配建模'}, 
    // 
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'},  
    { istrue: true, permission:'shootOperate', prop: 'operationGroupstr', align:'left', width: '80', label: '运营小组'},
    { istrue: true, permission:'shootCounter', prop: 'dockingPeople', align:'left',  width: '65', label: '对接人'},

    { istrue: true, permission:'shootPlatform', prop: 'platformStr', align:'left', width: '100', label: '平台'},
    { istrue: true, permission:'shootShop', prop: 'shopNameStr', align:'left', width: '180', label: '店铺'},

    { istrue: true, summaryEvent: true, permission:'shootTaskOverTime', prop: 'taskOverTimeStr',    width: '100',  label: '完成时间'  },
    { istrue: true, permission:'shootArrivalTime', prop: 'arrivalTimeStr',    width: '75',  label: '到货日期'},
    { istrue: true, permission:'shootArrivalDays', prop: 'arrivalTimeDays',    width: '53',  label: '到货天数'},
    { istrue: true, permission:'shootDeliverTime', prop: 'deliverTimeStr',   width: '75',  label: '发货日期' },
    { istrue: true, permission:'shootDeliverDays', prop: 'deliverTimeDays',    width: '53',  label: '发货天数' },
    { istrue: true, permission:'shootApplicationTime', prop: 'applyTimeStr',   width: '75',  label: '申请日期' },
    { istrue: true, permission:'shootApplicationDay', prop: 'applyTimeDays',    width: '53',  label: '申请天数' },
    { istrue: true, permission:'shootCreationDate', prop: 'createdTime',  width: '85',  label: '创建日期', sortable: 'custom' ,formatter: (row) => row.createdTimeStr },    
    
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'}, 
    { istrue: true, permission:'shootorderNo', prop: 'orderNoInner',  width: '100', label: '内部单号', type: 'click', handle: (that, row, column) => that.onShowOrderDtl(row)},
    { istrue: true, permission:'shootorderExpressNumber', prop: 'expressNo',  width: '135',  label: '快递单号', type: 'click', handle: (that, row, column) => that.onShowExproessHttp(row)},
    { istrue: true, permission:'shootorderNo', prop: 'shootOrderTrack', width: '80',  label: '拿样跟踪', type: 'click', handle: (that, row, column) => that.onShowOrderDtl(row)},
    {
        istrue: true, permission:'shootAction', type: "button", label: '操作', fixed: 'right', width: "300",
        btnList: [
            { label: "详情", handle: (that, row) => that.detailTask(row) }, 
            // { label: "成果", permission: "shootingUploadOutComFile", handle: (that, row) => that.onUploadSuccessFile(row) },
            { label: "成果", permission: "shootingUploadOutComFile", handle: (that, row) => that.toResultmatter(row) },
            { label: "存档", permission: "api:media:shootingvideo:TaskShopActionAsync", 
                ishide: (that, row) => { return  that.tasktype ==2 ||that.tasktype ==3}, handle: (that, row) => that.onTaskShopAction(row) },

            { label: "重启", permission: "api:media:shootingvideo:TaskRestartActionAsync", 
                ishide: (that, row) => { return  that.tasktype ==3}, handle: (that, row) => that.onTaskRestartAction(row) },

            { type: "danger", permission: "api:media:shootingvideo:deleteshootingtaskActionasync", label: "删除",
                ishide: (that, row) => { return row.isdel == 1 }, handle: (that, row) => that.OnDeleteShootingTaskAction(row) },
      
            { type:'danger', label: "彻底删除", permission: "api:media:shootingvideo:DeleteTaskActionAsync", 
                ishide:  (that, row) => { return  that.tasktype ==1 ||that.tasktype ==2}, handle: (that, row) => that.deleteTaskAction(row) },
        ]
    }
];
const xdfhmainTableCols = [
    { istrue: true, prop: 'shootingTaskId', label: '当前任务', width: '80' },
    { istrue: true, prop: 'shootingTaskIds', label: '涉及任务', width: '100' },
    { istrue: true, prop: 'shootingTaskOrderId', label: '下单号', width: '70' },
    { istrue: true, prop: 'createdUserName', label: '下单人', width: '70' },
    { istrue: true, prop: 'receiverName', label: '收货人', width: '70' },
    { istrue: true, prop: 'receiverPhone', label: '收货电话', width: '80' },
    { istrue: true, prop: 'receiverState', label: '收货省', width: '70' },
    { istrue: true, prop: 'receiverCity', label: '收货市', width: '80' },
    { istrue: true, prop: 'receiverDistrict', label: '收货区', width: '80' },
    { istrue: true, prop: 'receiverAddress', label: '收货地址' },
    { istrue: true, prop: 'sampleRrderNo', label: '聚水潭内部单号', width: '120', type: 'click', handle: (that, row) => that.showLogDetail(row)},
    { istrue: true, prop: 'sampleExpressNo', label: '快递单号', width: '120', type: 'click', handle: (that, row, column) => that.onShowExproessHttp(row) },
    { istrue: true, prop: 'sampleExpressCom', label: '物流公司', width: '80' },
    { istrue: true, prop: 'arrivalDate', label: '到货日期', width: '100', formatter: (row) => row.arrivalDate == null ? '2022-10-11' : formatTime(row.arrivalDate, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'isDy', label: '是否到样', width: '80', formatter: (row) => row.isDy == 1 ? "是" : "否" },
    { istrue: true, prop: 'isZt', label: '是否自提', width: '80', formatter: (row) => row.isZt == 1 ? "是" : "否" },
    { istrue: true, prop: 'approveStateName', label: '状态', width: '80'},
    { istrue: true, prop: 'createdTime', label: '创建时间', width: '150', formatter: (row) => row.createdTime == null ? null : formatTime(row.createdTime, 'YYYY-MM-DD HH:mm:ss') },
];
const xdfhdtlTableCols = [
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '200' },
    { istrue: true, prop: 'goodsName', label: '商品名称' },
    { istrue: true, prop: 'goodsPrice', label: '单价', width: '120', display: false },
    { istrue: true, prop: 'goodsQty', label: '数量', width: '120' },
    { istrue: true, prop: 'goodsAmount', label: '总额', width: '120', display: false },
];
export default {
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable,logistics,uploadfile,shootinguploadaction,shootingTaskRemark
    ,shootingvideotaskuploadfile,shootingvideotaskuploadsuccessfilesocre,shootingvideotaskeditfrom, orderLogPage},

    inject:[ 'getUserListRelod','getwarehouseListRelod', 'getShootingViewPersonRelod'],
    props:["tasktype","tablekey",'role'],
    watch: {},
    data() {
        return { 
            Oncommand:"a",
            viewReferenceRemark:false,
            filesocreLoading:false,
            shootingvediotask:'shootingvediotask',
            opentime:null,
            outconfilekey:null,
            //上传成果文件
            successfiledrawer:false,
            successfileshow:false,
            //查看参考
            viewReference:false,
            //选中的行
            selectRowKey:null,
            that: this,
            pageLoading :false,
            islook:true,
            filter: {
                searchTimeType:"1",
                createdtimerange:[], 
                fpPhotoLqName:[],
                warehouse:[],
             },
            tasklist:[], 
            taskPageTitle: "创建任务",
            referenceVideoList: [],
            multipleSelection: [],
            addLoading :false,
            warehouselist: [],
            shopList: [],
            userList: [],
            groupList: [],

            fpDetailLqNameList: [],
            fpModelLqNameList: [],
            fpPhotoLqNameList: [],
            fpVideoLqNameList: [],
            dockingPeopleList: [],

            taskUrgencyList :[{value:1,label:"加急"},{value:0,label:"紧急"},{value:2,label:"确认"},{value:9,label:"正常"}],
            platformList :[],
            tableCols: tableCols,
            total: 0,
            //选中的行id
            selids : [],
            taskPhotofileList:[],
            taskExeclfileList:[],
            addTask: false,
            loginfo:null, 
            summaryarry: {},
            pager: {  },
            sels: [], // 列表选中列
            listLoading: false,
            addFormRules: {
                productShortName: [{ required: true, message: '请填写', trigger: 'blur' }],
                shopName: [{ required: true, message: '请选择', trigger: 'blur' }],
                operationGroup: [{ required: true, message: '请选择', trigger: 'blur' }], 
                dockingPeople: [{ required: true, message: '请填写', trigger: 'blur' }], 
                warehouse: [{ required: true, message: '请选择', trigger: 'blur' }], 
                isReissue: [{ required: true, message: '请选择', trigger: 'blur' }],
                shootingTaskPickList: [{ required: true, message: '请选择', trigger: 'blur' }],
                platform: [{ required: true, message: '请选择', trigger: 'blur' }],
            },
            
            dialogOrderDtlVisible: false,
            dialogOrderDtlLoading: false,

            xdfhmainlist: [],
            xdfhmainTableCols: xdfhmainTableCols,
            xdfhmainLoading: false,

            xdfhdtllist: [],
            xdfhdtlTableCols: xdfhdtlTableCols,
            xdfhdtlLoading: false,  

            drawervisible: false,  

            sendOrderNoInner: "",
            dialogHisVisible: false,
        };
    },
    watch: {
    },
    async created() {

    },
    async mounted() {
    
        await this.onSearch();
        await this.onGetdrowList();
        await this.onGetdrowList2();
        this.pageLoading =true;
        this.Oncommand = this.role;
        this.ShowHideonSearch();
        this.pageLoading =false;
        await this.getShootingViewPer();

    },
    methods: {
        toResultmatter(row){
            if (row != null) {
                let routeUrl = this.$router.resolve({
                    path: '/resultmatterno',
                    query: {id:row.shootingTaskId}
                });
                window.open(routeUrl.href, '_blank');
            }
        },
        async onExeprotShootingTask(){
            switch(this.tasktype){
                //完成
                case 1 :  
                    this.filter.isShop = 0;
                    this.filter.isdel = 0;
                    this.filter.isComplate = 1;
                    break;
                //暂存
                case 2 :  
                    this.filter.isShop = 1;
                    this.filter.isdel = 0;
                    this.filter.isComplate = null;
                    break;
                //删除
                case 3 : 
                    this.filter.isShop = null;
                    this.filter.isdel = 1;
                    this.filter.isComplate = null; 
                    break;
            }
            
            if (this.filter.createdtimerange) {
                this.filter.createdstartTime = this.filter.createdtimerange[0];
                this.filter.createdendTime = this.filter.createdtimerange[1];
            }else{
                this.filter.createdstartTime =  null;
                this.filter.createdendTime = null;
            } 
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            this.pageLoading = true;
            var res = await exportShootingTaskReport(params);
            if (res?.data?.type == 'application/json') {return;}
            this.pageLoading = false;
            const aLink = document.createElement("a");
            var blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '新品拍摄导出.xlsx')
            aLink.click()

        },
        detailstyle: detailstyle,
        //批量操作
        customRowStyle(row,index){
            if(row.row?.isend && row.row.isend==1){ 
                let styleJson = {};
                styleJson.color = "rgb(216 216 216)"; 
                return styleJson
            }else{
                return null
            }
        },
        async onMoveTask()
        {
            if(this.selids.length==0){
                this.$message({ type: 'warning', message: "请选择一个任务" });
                return;
            }
            switch(this.tasktype){
                //完成-暂存
                case 1 :  
                    await this.onTaskShopActionShared(this.selids)  
                    break;
                //暂存-重启
                case 2 :  
                    await this.onTaskRestartActionShared(this.selids); 
                    break;
                //删除-彻底
                case 3 : 
                    await this.onTaskRestartActionShared(this.selids) 
                    break;
            }
        },
        // 删除操作
        async OnDeleteShootingTaskAction(row)
        { 
            this.$confirm("选中的任务会移动到回收站，是否确定执行", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            }).then(async () => {
                var res =  await deleteShootingTaskActionAsync([row.shootingTaskId]);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await  this.onRefresh(); 
                } 
            });
        }, 
        //存档操作
        async onTaskShopAction(row)
        {
            await this.onTaskShopActionShared([row.shootingTaskId]); 
        },
        async onTaskShopActionShared(array)
        {
            this.$confirm("选中的任务移动到存档列表，是否确定存档", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            }).then(async () => {
                var res =  await taskShopActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await  this.onRefresh();
                    this.selids = [];
                } 
            });
        },
        //重新启动
        async onTaskRestartAction(row)
        { 
            await this.onTaskRestartActionShared([row.shootingTaskId]); 
        },
        //重新启动
        async onTaskRestartActionShared(array)
        {
            this.$confirm("选中的任务将会移动至任务列表，是否确定执行", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            }).then(async () => {
                var res =  await taskRestartActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await  this.onRefresh();
                    this.selids = [];
                } 
            });
        },
        //回收站彻底删除操作
        async deleteTaskAction(row)
        { 
            await this.deleteTaskActionShared([row.shootingTaskId]); 
        },
        //回收站彻底删除操作
        async deleteTaskActionShared(array)
        { 
            this.$confirm("选中的任务会彻底删除，是否确定执行", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            }).then(async () => {
                var res =  await deleteTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await  this.onRefresh();
                    this.selids = [];
                } 
            });
        }, 
        //获取下拉数据
        async onGetdrowList()
        { 
           /*  var res = await getTaskUrgencyList();
            this.taskUrgencyList = res.data || [];  */
            var pfrule = await rulePlatform();
            this.platformList = pfrule.options;   
            var res = await getDirectorGroupList(); 
            this.groupList = res.data?.map(item => { return { value: item.key, label: item.value }; });
       
        },
          //获取分配人下拉，对接人下啦
          async getShootingViewPer() {
            var res = await this.getShootingViewPersonRelod();
            if(res){
                /* this.fpDetailLqNameList=res.fpDetailLqNameList;
                this.fpModelLqNameList=res.fpModelLqNameList;
                this.fpPhotoLqNameList=res.fpPhotoLqNameList;
                this.fpVideoLqNameList=res.fpVideoLqNameList; */
                this.dockingPeopleList=res.dockingPeopleList;
                this.fpPhotoLqNameList=res.fpallList;
            }
        },
        async onGetdrowList2()
        { 
           var   res =await this.getwarehouseListRelod(); 
            this.warehouselist =res?.map(item => { return { value: item.id, label: item.label }; });

        },
        async onchangeplatform(val) {
            var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100 });
            this.shopList = res1.data.list;
        },
        async ShowHideonSearch(){
            this.pageLoading =true;
            var temp = this.$refs[this.tablekey].checkBoxGroup;
            var checkedColumnsFora= temp; 
            switch (this.Oncommand) {
                //显示全部
                case "a":
                    checkedColumnsFora= 
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注', '参考',
                    '确认','确认人','确认日期', '完成日期', 
                    '照片', '视频', '微详情', '详情页','照片建模','视频建模',  '张数','数量', '个数', '天数',
                    '分配照片', '分配视频', '分配详情', '分配建模' ,
                    '运营小组','对接人', '平台', '店铺','完成时间','到货日期','到货天数','发货日期','发货天数','发货日期', '申请日期', '申请天数', 
                    '创建日期','内部单号','快递单号','拿样跟踪','操作'];  
                    break;
                //显示默认
                case "b":
                    checkedColumnsFora= 
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注', '参考',
                    '照片', '视频', '微详情', '详情页','照片建模','视频建模', 
                    '分配照片', '分配视频', '分配详情', '分配建模' ,
                    '完成时间','到货日期','申请日期', 
                    '创建日期','内部单号','快递单号','拿样跟踪','操作'];  
                    break;
                //查看分配
                case "c":
                    checkedColumnsFora= ['编号', '产品简称', '大货仓', '紧急程度', '备注', '参考', 
                    '照片','视频', '微详情', '详情页', '照片建模','视频建模',
                    '分配照片', '分配视频', '分配详情','分配建模' ,
                    '照片建模', '视频建模', '运营小组','对接人','创建日期','操作'];
                    break;
                //查看小组
                case "d":
                    checkedColumnsFora= ['编号', '产品简称', '大货仓', '紧急程度', '备注', 
                     '分配照片', '分配视频',  '分配详情','分配建模' ,
                    '运营小组', '对接人', '店铺', '创建日期', '内部单号', '快递单号','拿样跟踪','操作'];
                    break;
                //寄样实效
                case "e":
                    checkedColumnsFora= ['编号', '产品简称', '大货仓', '紧急程度', '备注', 
                     '分配照片', '分配视频', '分配详情' , '分配建模' , 
                     '到货日期', '到货天数', '发货日期', '发货天数', '申请日期', '申请天数','创建日期', '内部单号', '快递单号','拿样跟踪','操作'];
                    break;
                //拍摄实效
                case "f":
                    checkedColumnsFora= 
                    [ '编号', '产品简称', '大货仓', '紧急程度', '备注', 
                    '确认','确认人','确认日期','完成日期',
                    '照片', '视频', '微详情', '详情页','照片建模','视频建模',  '张数','数量', '个数',  
                    '分配照片', '分配视频', '分配详情', '分配建模' ,  
                    '到货日期','创建日期','操作'];
                    break;
                //隐藏操作
                case "g":
                    checkedColumnsFora= 
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注', '参考',
                    '确认','确认人','确认日期', '完成日期', 
                    '照片', '视频', '微详情', '详情页','照片建模','视频建模',  '张数','数量', '个数', '天数',
                    '分配照片', '分配视频', '分配详情', '分配建模' ,
                    '运营小组','对接人', '平台', '店铺','完成时间','到货日期','到货天数','发货日期','发货天数','发货日期', '申请日期', '申请天数', 
                    '创建日期','内部单号','快递单号' ,'拿样跟踪'];
                    break;
             
                default: 
                    break;
            } 
            this.$refs[this.tablekey].checkedColumns = checkedColumnsFora;   
            this.pageLoading =false;
        },
        handleCommand(command) {
            this.Oncommand = command;
            this.ShowHideonSearch()
        },
       
        //查询
        async onSearch(){
            this.$refs.pager.setPage(1);
            this.getTaskList();
        },
        //刷新当前页
        async onRefresh(){
             await  this.getTaskList();
        },
        //获取数据
        async getTaskList() {
            var pager = this.$refs.pager.getPager();
            switch(this.tasktype){
                //完成
                case 1 :  
                    this.filter.isShop = 0;
                    this.filter.isdel = 0;
                    this.filter.isComplate = 1;
                    break;
                //暂存
                case 2 :  
                    this.filter.isShop = 1;
                    this.filter.isdel = 0;
                    this.filter.isComplate = null;
                    break;
                //删除
                case 3 : 
                    this.filter.isShop = null;
                    this.filter.isdel = 1;
                    this.filter.isComplate = null; 
                    break;
            } 
            if (this.filter.createdtimerange) {
                this.filter.createdstartTime = this.filter.createdtimerange[0];
                this.filter.createdendTime = this.filter.createdtimerange[1];
            }else{
                this.filter.createdstartTime =  null;
                this.filter.createdendTime = null;
            } 
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            this.listLoading = true;
            const res = await pageShootingViewTaskAsync(params);
            this.listLoading = false;
            this.total = res.data.total
            this.tasklist = res.data.list;
            this.summaryarry = { shootingTaskId_sum: " _" };
        },
        //列表排序
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        //多选事件
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.shootingTaskId);
            })
        },
        //任务详情
        async detailTask(row) {
            this.addLoading =true;
            this.opentime = this.opentime +1;
            this.taskPageTitle = "任务详情";
            this.addTask = true;
            this.$nextTick(function () { 
                this.$refs.shootingvideotaskeditfrom.editTask(row);
                this.$refs.shootingvideotaskeditfrom.setShootO(this.warehouselist);
                
            });
            this.addLoading =false;
        },
        //关闭窗口，初始化数
        onCloseAddForm(type){  
            this.addTask = false;
        },
        //查看详情备注页
        openTaskRmarkInfo(row){
            this.opentime = this.opentime +1;
            this.selectRowKey = row.shootingTaskId;
            this.viewReferenceRemark = true;
        },
        //成果文件上传
        onUploadSuccessFile(row){
            this.opentime = this.opentime +1;
            this.selectRowKey =row.shootingTaskId;
            this.outconfilekey = row.shootingTaskId;
            this.successfiledrawer = true;
        },   
        //关闭成果文件上传
        successfiledrawerClose(){
            this.successfiledrawer = false;
        },
        //查看参考附件
        videotaskuploadfileDetal(row){
            this.opentime = this.opentime +1;
            this.selectRowKey = row.shootingTaskId;
            // this.viewReference = true;

            if (row != null) {
                let routeUrl = this.$router.resolve({
                    path: '/seereference',
                    query: {id:row.shootingTaskId,refid:row.referenceId}
                });
                window.open(routeUrl.href, '_blank');
            }
            
        },
        //查看成果文件
        openComputOutInfo(row){
            this.opentime = this.opentime +1;
            this.selectRowKey =row.shootingTaskId;
            this.successfileshow = true;
        },
         
        //成果文件提交
        async onSubComputOutInfo(){ 
            this.filesocreLoading = true;
            await  this.$refs.shootingvideotaskuploadsuccessfilesocre.onSubComputOutInfo();
            this.filesocreLoading = false;
        },
        successfiledrawerscoreClose(){
            this.successfileshow = false;
        },
        successfiledrawerscoreClose(){
            this.successfileshow = false;
        },
        async dialogOrderDtlColsed() {
            this.xdfhmainlist = [];
            this.xdfhdtllist = [];
        },
        async onShowOrderDtl(row) {
            this.dialogOrderDtlVisible = true;
            this.xdfhmainLoading = true;
            this.xdfhdtlLoading = true;
            var ret = await getShootingTaskOrderListById({ shootingTaskId: row.shootingTaskId });
            this.xdfhmainLoading = false;
            this.xdfhdtlLoading = false;
            if (ret?.success && ret.data.length > 0) {
                ret.data.forEach(f => f.shootingTaskId = row.shootingTaskId);
                this.xdfhmainlist = ret.data;
                this.xdfhdtllist = ret.data[0].dtlEntities;
            }
        },
        async onxdfhmainCellClick(row) {
            this.xdfhmainlist.forEach(
                f => {
                    if (f.shootingTaskOrderId == row.shootingTaskOrderId) {
                        this.xdfhdtllist = f.dtlEntities;
                    }
                }
            );
        },
        async onShowExproessHttp(row) {
            this.drawervisible = true;
            let expressNo=row.expressNo;
            if(!expressNo)
            {
                expressNo=row.sampleExpressNo;
            }
            this.$nextTick(function () {
                this.$refs.logistics.showlogistics("",expressNo);
            })
        },
        showLogDetail (row) {
            this.dialogHisVisible = true;
            let sampleRrderNo=row.sampleRrderNo;
            if(!sampleRrderNo)
            {
                sampleRrderNo=row.orderNoInner;
            }
            this.sendOrderNoInner = sampleRrderNo;
        },
    }, 
};
</script>

