<template>
  <my-container v-loading="pageLoading" :element-loading-text="upmsginfo">
    <el-form :model="addForm" :rules="calcAddFormRules" ref="addForm" label-width="100px" class="demo-addForm"
      :disabled="islook">
      <div class="dspbjrw">
        <div style="position: fixed; z-index: 999">
          <div class="dspbjbt">
            <span style="float: left">编辑/操作</span>
            <span style="float: right" v-if="!islook">
              <a href="#" v-if="checkPermission('api:media:vediotask:EndTaskActionAsync')" @click="endTaskAction">
                <i class="el-icon-document-delete" title="点击终止"></i></a>
              <a href="#" v-if="checkPermission('api:media:vediotask:UnEndTaskActionAsync')" @click="unEndTaskAction">
                <i class="el-icon-document-checked" title="点击重启"></i></a>
              <a href="#" v-if="checkPermission('api:media:vediotask:SignTaskActionAsync')" @click="signTaskAction">
                <i class="el-icon-news" title="点击标记"></i></a>
              <a href="#" v-if="checkPermission('api:media:vediotask:UnSignTaskActionAsync')" @click="unSignTaskAction">
                <i class="el-icon-odometer" title="取消标记"></i></a>
              <a href="#" v-if="checkPermission('api:media:vediotask:DeleteTaskActionAsync')" @click="deleteTaskAction">
                <i class="el-icon-delete" title="删除任务"></i></a>
              <a href="#"><i class="el-icon-more" title="更多操作"></i></a>
            </span>
          </div>
          <div style="height: 70px">
            <div class="rwmc">
              <div class="xh" style="width: 55px">{{ addForm.videoTaskId }}</div>
              <div class="mc" style="height: 66px">|</div>
              <div class="mc" style="width: 360px">
                <el-tooltip v-if="inputshow" class="item" effect="dark"
                  :content="addForm.productShortName ? addForm.productShortName : ''" placement="top">
                  <div style="margin: 0; width: 100%; height: 50px;" class="linecs" @click="inputshowfunc">
                    <span>{{ addForm.productShortName ? addForm.productShortName : '请点击输入产品简称' }} </span>
                  </div>
                </el-tooltip>
                <el-input v-else-if="!inputshow" @blur="inputshow = true" size="medium"
                  style="margin: 0; width: 100%; margin-top: -3px; font-size: 18px;"
                  v-model.trim="addForm.productShortName" :maxlength=100 placeholder="产品简称" clearable />
              </div>
              <div class="icon" style="float: right; width: 70px">
                <el-button size="mini" type="primary" @click="submitForm('addForm')">&nbsp;保&nbsp;存&nbsp;</el-button>
              </div>
            </div>
          </div>
        </div>
        <!-- 表单 star -->
        <div style="padding-top: 150px">
          <div class="dspbjlx">
            <div class="lxwz">产品ID</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="productId">
                <el-input size="mini" style="width: 100%" v-model="addForm.productId" :disabled="true"></el-input>
              </el-form-item>
            </div>
            <a href="#" @click="onSelctProduct">
              <i style="  font-size: 18px;  color: #409eff;  font-weight: bold; position: relative; top: 3px; left: 20px;  "
                title="选择Id" class="el-icon-circle-plus-outline"></i></a>
          </div>
          <div class="dspbjlx">
            <div class="lxwz">商品编码</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="goodCode">
                <el-tooltip class="item" effect="dark" :content="addForm.goodCode" placement="top">
                  <el-input size="mini" style="width: 200%" v-model.trim="addForm.goodCode" :maxlength="255"
                    :clearable="true"></el-input>
                </el-tooltip>
              </el-form-item>
            </div>
            <a href="#" @click="onSelctCp">
              <i style="  font-size: 18px;  color: #409eff;  font-weight: bold; position: relative; top: 3px; left: 190px;  "
                title="选择Id" class="el-icon-circle-plus-outline"></i></a>
          </div>
          <div class="dspbjlx">
            <div class="lxwz">平台</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="platform">
                <el-select size="mini" style="width: 60%" v-model="addForm.platform" placeholder="请选择平台" :disabled="true">
                  <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="dspbjlx">
            <div class="lxwz">店铺</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="shopName">
                <el-select size="mini" style="width: 100%" v-model="addForm.shopName" placeholder="请选择店铺"
                  :disabled="true">
                  <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                    :value="item.shopCode" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="dspbjlx">
            <div class="lxwz">运营小组</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="operationsGroup" :disabled="true">
                <el-select size="mini" style="width: 60%" v-model="addForm.operationsGroup" placeholder="请选择小组">
                  <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="dspbjlx" v-if="checkPermission('vedioTask-jjcd')">
            <div class="lxwz">紧急程度</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="taskUrgency" :disabled="true">
                <el-select size="mini" style="width: 60%" v-model="addForm.taskUrgency" placeholder="请选择小组">
                  <el-option v-for="item in taskUrgencyList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="dspbjlx">
            <div class="lxwz">拍摄样品</div>
            <div style="display: inline-block">
              <el-form-item label="" label-width="12px" label-position="left" prop="warehouse">
                <el-select size="mini" style="width: 80%" v-model="addForm.warehouse" placeholder="请选择仓库"
                  :clearable="true">
                  <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="dspbjlx">
            <div class="lxwz">订单号</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="sampleRrderNo">
                <el-input size="mini" style="width: 70%" v-model="addForm.sampleRrderNo" placeholder="请填写订单号">
                </el-input>
              </el-form-item>
            </div>
          </div>
          <div class="dspbjlx">
            <div class="lxwz">快递单号</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="sampleExpressNo">
                <el-input size="mini" style="width: 100%" v-model="addForm.sampleExpressNo"
                  placeholder="请填写快递单号"></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="dspbjlx">
            <div class="lxwz">到货日期</div>
            <div style="display: inline-block">
              <el-form-item prop="arrivalDate" label=" " label-width="12px">
                            <el-date-picker v-model="addForm.arrivalDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                type="date" size="mini" style="width:76%" placeholder="到货日期">
                            </el-date-picker>
              </el-form-item>
            </div>
          </div>
          <div class="dspbjlx">
            <div class="lxwz">负责人</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="">
                <el-select size="mini" style="width:50%" v-model="addForm.cuteLqName" filterable :clearable="true" remote reserve-keyword :remote-method="remoteMethod1">
                  <el-option v-for="item in useroptions1" :key="item.id" :label="item.label" :value="item.label" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="dspbjlx" v-if="checkPermission('vedioTask-pcps')">
            <div class="lxwz">排除拍摄</div>
            <div style="display: inline-block">
              <el-form>
                <el-form-item label=" " label-width="12px" prop="">
                  <el-select size="mini" style="width:50%" v-model="addForm.unCuteLqName" filterable :clearable="true"
                    :disabled="true">
                    <el-option v-for="item in fpPhotoLqNameList" :key="item.id" :label="item.label" :value="item.label" />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
          </div>
          <div class="dspbjlx" style="margin-top: 10px;">
            <div class="lxwz">拍摄条数</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="taskPickList">
                <el-checkbox-group v-model="addForm.taskPickList" @change="taskpickchange">
                  <el-checkbox style="margin-right: -5px" size="mini" label="1" border
                    :disabled="addForm.claimantId1 > 0 || pdArrayCk1.length > 0">视频一</el-checkbox>
                  <el-checkbox style="margin-right: -5px" size="mini" label="2" border
                    :disabled="addForm.claimantId2 > 0 || pdArrayCk2.length > 0">视频二</el-checkbox>
                  <el-checkbox style="margin-right: -5px" size="mini" label="3" border
                    :disabled="addForm.claimantId3 > 0 || pdArrayCk3.length > 0">视频三</el-checkbox>
                  <el-checkbox style="margin-right: -5px" size="mini" label="4" border
                    :disabled="addForm.claimantId4 > 0 || pdArrayCk4.length > 0">视频四</el-checkbox>
                  <el-checkbox style="margin-right: -5px" size="mini" label="5" border
                    :disabled="addForm.claimantId5 > 0 || pdArrayCk5.length > 0">视频五</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </div>
          </div>
          <div class="dspbjlx" v-if="checkPermission('api:media:vediotask:AssignshootingSave')" style="display: flex; flex-direction: row;">
            <div class="lxwz">分配拍摄</div>
            <div style="display: inline-block;">
              <el-form-item label=" " label-width="12px" prop="">
                <el-select size="mini" style="width:80px" v-model="addForm.dockingPeopleStr"  placeholder="拍摄一" filterable :clearable="true"
                  :disabled ="!addForm.taskPickList.includes('1')">
                  <el-option v-for="item in fpPhotoLqNameList" :key="item.id+'1'" :label="item.label" :value="item.label" />
                </el-select>
                 <el-select size="mini" style="width:80px;margin-left: 5px;" v-model="addForm.dockingPeopleStr2" placeholder="拍摄二" filterable :clearable="true"
                 :disabled ="!addForm.taskPickList.includes('2')">
                  <el-option v-for="item in fpPhotoLqNameList" :key="item.id+'2'" :label="item.label" :value="item.label" />
                </el-select>
                <el-select size="mini" style="width:80px;margin-left: 5px;" v-model="addForm.dockingPeopleStr3" placeholder="拍摄三"  filterable :clearable="true"
                :disabled ="!addForm.taskPickList.includes('3')">
                  <el-option v-for="item in fpPhotoLqNameList" :key="item.id+'3'" :label="item.label" :value="item.label" />
                </el-select>
                <el-select size="mini" style="width:80px;margin-left: 5px;" v-model="addForm.dockingPeopleStr4" placeholder="拍摄四"  filterable :clearable="true"
                :disabled ="!addForm.taskPickList.includes('4')">
                  <el-option v-for="item in fpPhotoLqNameList" :key="item.id+'4'" :label="item.label" :value="item.label" />
                </el-select>
                <el-select size="mini" style="width:80px;margin-left: 5px;" v-model="addForm.dockingPeopleStr5" placeholder="拍摄五" filterable :clearable="true"
                :disabled ="!addForm.taskPickList.includes('5')">
                  <el-option v-for="item in fpPhotoLqNameList" :key="item.id+'5'" :label="item.label" :value="item.label" />
                </el-select>
              </el-form-item>
            </div>
          </div>

          <div class="dspbjlx" style="margin: 20px auto 40px auto">
            <el-tooltip class="item" effect="dark" :content="addForm.remark" placement="top">
              <el-input type="textarea" :rows="2" :maxlength="800" show-word-limit placeholder="请输入内容"
                v-model="addForm.remark">
              </el-input>
            </el-tooltip>
          </div>
        </div>
        <!-- 表单 end -->
        <div class="dspczxx">
          <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal" collapse-transition
            active-text-color="#409eff">
            <el-menu-item style="margin-left: 36px; line-height: 65px" index="1" @click="activeIndex = '1'"><i
                class="el-icon-set-up"></i>操作任务</el-menu-item>
            <el-menu-item style="line-height: 65px" index="2" @click="activeIndex = '2'"><i
                class="el-icon-document"></i>添加参考</el-menu-item>
            <el-menu-item style="line-height: 65px" index="3" @click="activeIndex = '3'"><i
                class="el-icon-star-off"></i>评分</el-menu-item>
            <el-menu-item style="line-height: 65px" index="4" @click="activeIndex = '4'"><i
                class="el-icon-video-camera"></i>成品视频</el-menu-item>
          </el-menu>
        </div>
        <!-- 上传片段相关-------------------------------------------------------------------------------------------------------------------------------------- -->
        <div v-show="activeIndex == '2'">
          <!-- 上传片段 star -->
          <div class="dspscpd"><el-progress :text-inside="true" :stroke-width="3" :percentage="upprocess"></el-progress>
          </div>
          <div class="dspbjfgx">
            <!--参考块一-->
            <div v-if="addForm.taskPickList.includes('1')" class="dspscpd">
              <div style="height: 38px">
                <div class="scpd">参考视频一</div>
                <div class="zjck">
                  <el-button size="mini" type="primary" plain @click="addpdarray(1)"
                    :disabled="islook || (addForm.claimantId1 > 0)">+&nbsp;增加片段</el-button>
                </div>
              </div>
              <!--行开始-->
              <div v-for="(item, index) in pdArrayCk1" :key="item.pdId">
                <div style="margin: 2px 5px 2px 0; width: 75%; display: inline-block">
                  <el-tooltip class="item" effect="dark" :content="item.url" placement="top">
                    <el-input size="mini" placeholder="请上传" v-model="item.url" :disabled="true"></el-input>
                  </el-tooltip>
                </div>
                <div style=" float: right;  display: inline-block; position: relative;  top: 2px; ">
                  <el-upload :file-list="fileList" style="display: inline-block; margin:0 5px;" class="avatar-uploader"
                    :accept="videotype" action="#" :limit="1" :on-success="uploadpdSuccess"
                    :disabled="islook || item.isover == 1" :http-request="uploadpdFile"
                    :data="{ 'pdarrayindex': 1, 'index': index }" :show-file-list="false">
                    <el-button size="mini" type="primary"
                      :disabled="islook || item.isover == 1">上传片段</el-button></el-upload>
                  <el-button size="mini" type="danger" @click="removepdarray(index, 1, item.pdId)"
                    :disabled="islook || item.isover == 1 || !checkPermission('bjczscts')"><i class="el-icon-delete"></i></el-button>
                </div>
                <!-- 备注 -->
                <div style="margin: 2px 5px 2px 0; width: 100%; display: inline-block">
                  <el-tooltip class="item" effect="dark" :content="item.title" placement="top">
                    <el-input size="mini" placeholder="请输入备注" v-model="item.title" label=""
                      :disabled="islook || item.isover == 1 || item.iscue == 1"></el-input>
                  </el-tooltip>
                </div>
              </div>
              <!--行end-->
            </div>
            <!--参考块一-->
            <!--参考块二-->
            <div v-if="addForm.taskPickList.includes('2')" class="dspscpd">
              <div style="height: 38px">
                <div class="scpd">参考视频二</div>
                <div class="zjck">
                  <el-button size="mini" type="primary" plain @click="addpdarray(2)"
                    :disabled="islook || addForm.claimantId2 > 0">+&nbsp;增加片段</el-button>
                </div>
              </div>
              <div v-for="(item, index) in pdArrayCk2" :key="item.pdId">
                <div style="margin: 2px 5px 2px 0; width: 75%; display: inline-block">
                  <el-tooltip class="item" effect="dark" :content="item.url" placement="top">
                    <el-input size="mini" placeholder="请上传" v-model="item.url" :disabled="true"></el-input>
                  </el-tooltip>
                </div>
                <div style=" float: right;  display: inline-block; position: relative;  top: 2px; ">
                  <el-upload :file-list="fileList" style="display: inline-block; margin:0 5px;" class="avatar-uploader"
                    :accept="videotype" action="#" :limit="1" :on-success="uploadpdSuccess"
                    :disabled="islook || item.isover == 1" :http-request="uploadpdFile"
                    :data="{ 'pdarrayindex': 2, 'index': index }" :show-file-list="false">
                    <el-button size="mini" type="primary"
                      :disabled="islook || item.isover == 1">上传片段</el-button></el-upload>
                  <el-button size="mini" type="danger" @click="removepdarray(index, 2, item.pdId)"
                    :disabled="islook || item.isover == 1 ||  !checkPermission('bjczscts')"><i class="el-icon-delete"></i></el-button>
                </div>
                <!-- 备注 -->
                <div style="margin: 2px 5px 2px 0; width: 100%; display: inline-block">
                  <el-tooltip class="item" effect="dark" :content="item.title" placement="top">
                    <el-input size="mini" placeholder="请输入备注" v-model="item.title"
                      :disabled="islook || item.isover == 1 || item.iscue == 1"></el-input>
                  </el-tooltip>
                </div>
              </div>
            </div>
            <!--参考块二-->
            <!--参考块三-->
            <div v-if="addForm.taskPickList.includes('3')" class="dspscpd">
              <div style="height: 38px">
                <div class="scpd">参考视频三</div>
                <div class="zjck">
                  <el-button size="mini" type="primary" plain @click="addpdarray(3)"
                    :disabled="islook || addForm.claimantId3 > 0">+&nbsp;增加片段</el-button>
                </div>
              </div>
              <div v-for="(item, index) in pdArrayCk3" :key="item.pdId">
                <div style="margin: 2px 5px 2px 0; width: 75%; display: inline-block">
                  <el-tooltip class="item" effect="dark" :content="item.url" placement="top">
                    <el-input size="mini" placeholder="请上传" v-model="item.url" :disabled="true"></el-input>
                  </el-tooltip>
                </div>
                <div style=" float: right;  display: inline-block; position: relative;  top: 2px; ">
                  <el-upload :file-list="fileList" style="display: inline-block; margin:0 5px;" class="avatar-uploader"
                    :accept="videotype" action="#" :limit="1" :on-success="uploadpdSuccess"
                    :disabled="islook || item.isover == 1" :http-request="uploadpdFile"
                    :data="{ 'pdarrayindex': 3, 'index': index }" :show-file-list="false">
                    <el-button size="mini" type="primary"
                      :disabled="islook || item.isover == 1">上传片段</el-button></el-upload>
                  <el-button size="mini" type="danger" @click="removepdarray(index, 3, item.pdId)"
                    :disabled="islook || item.isover == 1  || !checkPermission('bjczscts')"><i class="el-icon-delete"></i></el-button>
                </div>
                <!-- 备注 -->
                <div style="margin: 2px 5px 2px 0; width: 100%; display: inline-block">
                  <el-tooltip class="item" effect="dark" :content="item.title" placement="top">
                    <el-input size="mini" placeholder="请输入备注" v-model="item.title"
                      :disabled="islook || item.isover == 1 || item.iscue == 1"></el-input>
                  </el-tooltip>
                </div>
              </div>
            </div>
            <!--参考块三-->
            <!--参考块四-->
            <div v-if="addForm.taskPickList.includes('4')" class="dspscpd">
              <div style="height: 38px">
                <div class="scpd">参考视频四</div>
                <div class="zjck">
                  <el-button size="mini" type="primary" plain @click="addpdarray(4)"
                    :disabled="islook || addForm.claimantId4 > 0">+&nbsp;增加片段</el-button>
                </div>
              </div>
              <div v-for="(item, index) in pdArrayCk4" :key="item.pdId">
                <div style="margin: 2px 5px 2px 0; width: 75%; display: inline-block">
                  <el-tooltip class="item" effect="dark" :content="item.url" placement="top">
                    <el-input size="mini" placeholder="请上传" v-model="item.url" :disabled="true"></el-input>
                  </el-tooltip>
                </div>
                <div style=" float: right;  display: inline-block; position: relative;  top: 2px; ">
                  <el-upload :file-list="fileList" style="display: inline-block; margin:0 5px;" class="avatar-uploader"
                    :accept="videotype" action="#" :limit="1" :on-success="uploadpdSuccess"
                    :disabled="islook || item.isover == 1" :http-request="uploadpdFile"
                    :data="{ 'pdarrayindex': 4, 'index': index }" :show-file-list="false">
                    <el-button size="mini" type="primary"
                      :disabled="islook || item.isover == 1">上传片段</el-button></el-upload>
                  <el-button size="mini" type="danger" @click="removepdarray(index, 4, item.pdId)"
                    :disabled="islook || item.isover == 1 || !checkPermission('bjczscts')"><i class="el-icon-delete"></i></el-button>
                </div>
                <!-- 备注 -->
                <div style="margin: 2px 5px 2px 0; width: 100%; display: inline-block">
                  <el-tooltip class="item" effect="dark" :content="item.title" placement="top">
                    <el-input size="mini" placeholder="请输入备注" v-model="item.title"
                      :disabled="islook || item.isover == 1 || item.iscue == 1"></el-input>
                  </el-tooltip>
                </div>
              </div>
            </div>
            <!--参考块四-->
            <!--参考块五-->
            <div v-if="addForm.taskPickList.includes('5')" class="dspscpd">
              <div style="height: 38px">
                <div class="scpd">参考视频五</div>
                <div class="zjck">
                  <el-button size="mini" type="primary" plain @click="addpdarray(5)"
                    :disabled="islook || addForm.claimantId5 > 0">+&nbsp;增加片段</el-button>
                </div>
              </div>
              <div v-for="(item, index) in pdArrayCk5" :key="item.pdId">
                <div style="margin: 2px 5px 2px 0; width: 75%; display: inline-block">
                  <el-tooltip class="item" effect="dark" :content="item.url" placement="top">
                    <el-input size="mini" placeholder="请上传" v-model="item.url" :disabled="true"></el-input>
                  </el-tooltip>
                </div>
                <div style=" float: right;  display: inline-block; position: relative;  top: 2px; ">
                  <el-upload :file-list="fileList" style="display: inline-block; margin:0 5px;" class="avatar-uploader"
                    :accept="videotype" action="#" :limit="1" :on-success="uploadpdSuccess"
                    :disabled="islook || item.isover == 1" :http-request="uploadpdFile"
                    :data="{ 'pdarrayindex': 5, 'index': index }" :show-file-list="false">
                    <el-button size="mini" type="primary"
                      :disabled="islook || item.isover == 1">上传片段</el-button></el-upload>
                  <el-button size="mini" type="danger" @click="removepdarray(index, 5, item.pdId)"
                    :disabled="islook || item.isover == 1 || !checkPermission('bjczscts')"><i class="el-icon-delete"></i></el-button>
                </div>
                <!-- 备注 -->
                <div style="margin: 2px 5px 2px 0; width: 100%; display: inline-block">
                  <el-tooltip class="item" effect="dark" :content="item.title" placement="top">
                    <el-input size="mini" placeholder="请输入备注" v-model="item.title"
                      :disabled="islook || item.isover == 1 || item.iscue == 1"></el-input>
                  </el-tooltip>
                </div>
              </div>
            </div>
            <!--参考块五-->
          </div>
          <!-- 上传片段 end -->
        </div>
        <!-- 完成取消操作相关-------------------------------------------------------------------------------------------------------------------------------------- -->
        <div v-show="activeIndex == '1'">
          <!-- 操作项 star -->
          <el-form :disabled="isoverlist">
            <div class="dspbjfgx">
              <div class="dspsccg">
                <el-button style="width: 100%" size="small" type="primary" @click="activeIndex = '4'">+上传成品视频</el-button>
              </div>
              <div class="dspczx">
                <div class="spczk">
                  <div class="spczksps">
                    <div class="spczs">视频一</div>
                  </div>
                  <div class="spczkzy">
                    <div class="spczan">
                      <el-button v-if="addForm.taskPickids.indexOf('1') < 0" size="mini" type=""
                        :disabled="true">无此任务</el-button>
                      <el-button v-if="addForm.taskPickids.indexOf('1') > -1 && addForm.claimantId1 == 0" size="mini"
                        type="primary" @click="pickTask(1)">完成拍摄</el-button>
                      <el-button v-if="addForm.taskPickids.indexOf('1') > -1 && addForm.claimantId1 > 0" size="mini"
                        type="" @click="unPickTask(1)">取消完成</el-button>
                    </div>
                    <div class="spczmz">{{ addForm.claimant1 }}</div>
                    <div class="spczsj">{{ isformatTime(addForm.claimTime1) }}</div>
                  </div>
                  <div class="spczkzy">
                    <div class="spczan">
                      <el-button size="mini" v-if="addForm.taskPickids.indexOf('1') < 0" type=""
                        :disabled="true">无此任务</el-button>
                      <el-button size="mini" v-if="addForm.taskPickids.indexOf('1') > -1 && addForm.cutClaimantId1 == 0"
                        @click="cutPickTask(1)" type="primary">完成剪辑</el-button>
                      <el-button size="mini" v-if="addForm.taskPickids.indexOf('1') > -1 && addForm.cutClaimantId1 > 0"
                        @click="unCutPickTask(1)" type="">取消完成</el-button>
                    </div>
                    <div class="spczmz">{{ addForm.cutClaimant1 }}</div>
                    <div class="spczsj">{{ isformatTime(addForm.cutClaimTime1) }}</div>
                  </div>
                </div>
              </div>
              <div class="dspczx">
                <div class="spczk">
                  <div class="spczksps">
                    <div class="spczs">视频二</div>
                  </div>
                  <div class="spczkzy">
                    <div class="spczan">
                      <el-button v-if="addForm.taskPickids.indexOf('2') < 0" size="mini" type=""
                        :disabled="true">无此任务</el-button>
                      <el-button v-if="addForm.taskPickids.indexOf('2') > -1 && addForm.claimantId2 == 0" size="mini"
                        type="primary" @click="pickTask(2)">完成拍摄</el-button>
                      <el-button v-if="addForm.taskPickids.indexOf('2') > -1 && addForm.claimantId2 > 0" size="mini"
                        type="" @click="unPickTask(2)">取消完成</el-button>
                    </div>
                    <div class="spczmz">{{ addForm.claimant2 }}</div>
                    <div class="spczsj">{{ isformatTime(addForm.claimTime2) }}</div>
                  </div>
                  <div class="spczkzy">
                    <div class="spczan">
                      <el-button size="mini" v-if="addForm.taskPickids.indexOf('2') < 0" type=""
                        :disabled="true">无此任务</el-button>
                      <el-button size="mini" v-if="addForm.taskPickids.indexOf('2') > -1 && addForm.cutClaimantId2 == 0"
                        @click="cutPickTask(2)" type="primary">完成剪辑</el-button>
                      <el-button size="mini" v-if="addForm.taskPickids.indexOf('2') > -1 && addForm.cutClaimantId2 > 0"
                        @click="unCutPickTask(2)" type="">取消完成</el-button>
                    </div>
                    <div class="spczmz">{{ addForm.cutClaimant2 }}</div>
                    <div class="spczsj">{{ isformatTime(addForm.cutClaimTime2) }}</div>
                  </div>
                </div>
              </div>
              <div class="dspczx">
                <div class="spczk">
                  <div class="spczksps">
                    <div class="spczs">视频三</div>
                  </div>
                  <div class="spczkzy">
                    <div class="spczan">
                      <el-button v-if="addForm.taskPickids.indexOf('3') < 0" size="mini" type=""
                        :disabled="true">无此任务</el-button>
                      <el-button v-if="addForm.taskPickids.indexOf('3') > -1 && addForm.claimantId3 == 0" size="mini"
                        type="primary" @click="pickTask(3)">完成拍摄</el-button>
                      <el-button v-if="addForm.taskPickids.indexOf('3') > -1 && addForm.claimantId3 > 0" size="mini"
                        type="" @click="unPickTask(3)">取消完成</el-button>
                    </div>
                    <div class="spczmz">{{ addForm.claimant3 }}</div>
                    <div class="spczsj">{{ isformatTime(addForm.claimTime3) }}</div>
                  </div>
                  <div class="spczkzy">
                    <div class="spczan">
                      <el-button size="mini" v-if="addForm.taskPickids.indexOf('3') < 0" type=""
                        :disabled="true">无此任务</el-button>
                      <el-button size="mini" v-if="addForm.taskPickids.indexOf('3') > -1 && addForm.cutClaimantId3 == 0"
                        @click="cutPickTask(3)" type="primary">完成剪辑</el-button>
                      <el-button size="mini" v-if="addForm.taskPickids.indexOf('3') > -1 && addForm.cutClaimantId3 > 0"
                        @click="unCutPickTask(3)" type="">取消完成</el-button>
                    </div>
                    <div class="spczmz">{{ addForm.cutClaimant3 }}</div>
                    <div class="spczsj">{{ isformatTime(addForm.cutClaimTime3) }}</div>
                  </div>
                </div>
              </div>
              <div class="dspczx">
                <div class="spczk">
                  <div class="spczksps">
                    <div class="spczs">视频四</div>
                  </div>
                  <div class="spczkzy">
                    <div class="spczan">
                      <el-button v-if="addForm.taskPickids.indexOf('4') < 0" size="mini" type=""
                        :disabled="true">无此任务</el-button>
                      <el-button v-if="addForm.taskPickids.indexOf('4') > -1 && addForm.claimantId4 == 0" size="mini"
                        type="primary" @click="pickTask(4)">完成拍摄</el-button>
                      <el-button v-if="addForm.taskPickids.indexOf('4') > -1 && addForm.claimantId4 > 0" size="mini"
                        type="" @click="unPickTask(4)">取消完成</el-button>
                    </div>
                    <div class="spczmz">{{ addForm.claimant4 }}</div>
                    <div class="spczsj">{{ isformatTime(addForm.claimTime4) }}</div>
                  </div>
                  <div class="spczkzy">
                    <div class="spczan">
                      <el-button size="mini" v-if="addForm.taskPickids.indexOf('4') < 0" type=""
                        :disabled="true">无此任务</el-button>
                      <el-button size="mini" v-if="addForm.taskPickids.indexOf('4') > -1 && addForm.cutClaimantId4 == 0"
                        @click="cutPickTask(4)" type="primary">完成剪辑</el-button>
                      <el-button size="mini" v-if="addForm.taskPickids.indexOf('4') > -1 && addForm.cutClaimantId4 > 0"
                        @click="unCutPickTask(4)" type="">取消完成</el-button>
                    </div>
                    <div class="spczmz">{{ addForm.cutClaimant4 }}</div>
                    <div class="spczsj">{{ isformatTime(addForm.cutClaimTime4) }}</div>
                  </div>
                </div>
              </div>
              <div class="dspczx">
                <div class="spczk">
                  <div class="spczksps">
                    <div class="spczs">视频五</div>
                  </div>
                  <div class="spczkzy">
                    <div class="spczan">
                      <el-button v-if="addForm.taskPickids.indexOf('5') < 0" size="mini" type=""
                        :disabled="true">无此任务</el-button>
                      <el-button v-if="addForm.taskPickids.indexOf('5') > -1 && addForm.claimantId5 == 0" size="mini"
                        type="primary" @click="pickTask(5)">完成拍摄</el-button>
                      <el-button v-if="addForm.taskPickids.indexOf('5') > -1 && addForm.claimantId5 > 0" size="mini"
                        type="" @click="unPickTask(5)">取消完成</el-button>
                    </div>
                    <div class="spczmz">{{ addForm.claimant5 }}</div>
                    <div class="spczsj">{{ isformatTime(addForm.claimTime5) }}</div>
                  </div>
                  <div class="spczkzy">
                    <div class="spczan">
                      <el-button size="mini" v-if="addForm.taskPickids.indexOf('5') < 0" type=""
                        :disabled="true">无此任务</el-button>
                      <el-button size="mini" v-if="addForm.taskPickids.indexOf('5') > -1 && addForm.cutClaimantId5 == 0"
                        @click="cutPickTask(5)" type="primary">完成剪辑</el-button>
                      <el-button size="mini" v-if="addForm.taskPickids.indexOf('5') > -1 && addForm.cutClaimantId5 > 0"
                        @click="unCutPickTask(5)" type="">取消完成</el-button>
                    </div>
                    <div class="spczmz">{{ addForm.cutClaimant5 }}</div>
                    <div class="spczsj">{{ isformatTime(addForm.cutClaimTime5) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-form>
        </div>
        <!-- 成品视频 star--------------------------------------------------------------------------------------------------------------------- -->
        <div v-show="activeIndex == '4'">
          <el-form>
            <div class="dspscpd"><el-progress :text-inside="true" :stroke-width="3" :percentage="upprocess"></el-progress>
            </div>
            <div class="dspbjfgx">
              <!-- 一-->
              <div v-if="addForm.taskPickList.includes('1')" class="dspscpd">
                <div style="height: 38px">
                  <div class="scpd">成品视频一</div>
                  <div class="zjck">
                    <el-button size="mini" type="primary" plain @click="addcparray(1)"
                      :disabled="isoverlist">+&nbsp;增加视频</el-button>
                  </div>
                </div>
                <!--行开始-->
                <div v-for="(item, index) in successArrayCk1" :key="item.upLoadvedioid">
                  <div style="margin: 2px 5px 2px 0; width: 65%; display: inline-block">
                    <el-tooltip class="item" effect="dark" :content="item.url" placement="top">
                      <el-input size="mini" placeholder="请上传" v-model="item.url" :disabled="true"></el-input>
                    </el-tooltip>
                  </div>
                  <div style=" float: right;  display: inline-block; position: relative;  top: 2px; ">
                    <el-upload :file-list="fileList" style="display: inline-block; margin:0 0px;" class="avatar-uploader"
                      :accept="videotype" action="#" :limit="1" :on-success="uploadcpSuccess1"
                      :http-request="uploadcpSuccess" :disabled="isoverlist"
                      :data="{ 'cparrayindex': 1, 'index': index, 'upLoadvedioid': item.upLoadvedioid }"
                      :show-file-list="false">
                      <el-button size="mini" type="primary" :disabled="isoverlist"><i
                          class="el-icon-upload"></i></el-button>
                    </el-upload>
                    <el-button-group>
                      <el-button size="mini" type="primary" @click="playVideo(item.url)"><i
                          class="el-icon-video-play"></i></el-button>
                      <el-button size="mini" type="primary" @click="downinfo(item.url, item.fileName)"><i
                          class="el-icon-download"></i></el-button>
                      <el-button size="mini" type="danger" @click="removecparray(index, 1, item.upLoadvedioid)"
                        :disabled="isoverlist"><i class="el-icon-delete"></i></el-button>
                    </el-button-group>
                  </div>
                </div>
                <!--行end-->
              </div>
              <!--参考块一-->
              <!--参考块二-->
              <div v-if="addForm.taskPickList.includes('2')" class="dspscpd">
                <div style="height: 38px">
                  <div class="scpd">成品视频二</div>
                  <div class="zjck">
                    <el-button size="mini" type="primary" plain @click="addcparray(2)"
                      :disabled="isoverlist">+&nbsp;增加视频</el-button>
                  </div>
                </div>
                <!--行开始-->
                <div v-for="(item, index) in successArrayCk2" :key="item.upLoadvedioid">
                  <div style="margin: 2px 5px 2px 0; width: 65%; display: inline-block">
                    <el-tooltip class="item" effect="dark" :content="item.url" placement="top">
                      <el-input size="mini" placeholder="请上传" v-model="item.url" :disabled="true"></el-input>
                    </el-tooltip>
                  </div>
                  <div style=" float: right;  display: inline-block; position: relative;  top: 2px; ">
                    <el-upload :file-list="fileList" style="display: inline-block; margin:0 0px;" class="avatar-uploader"
                      :accept="videotype" action="#" :limit="1" :on-success="uploadcpSuccess1"
                      :http-request="uploadcpSuccess" :disabled="isoverlist"
                      :data="{ 'cparrayindex': 2, 'index': index, 'upLoadvedioid': item.upLoadvedioid }"
                      :show-file-list="false">
                      <el-button size="mini" type="primary" :disabled="isoverlist"><i
                          class="el-icon-upload"></i></el-button>
                    </el-upload>
                    <el-button-group>
                      <el-button size="mini" type="primary" @click="playVideo(item.url)"><i
                          class="el-icon-video-play"></i></el-button>
                      <el-button size="mini" type="primary" @click="downinfo(item.url, item.fileName)"><i
                          class="el-icon-download"></i></el-button>
                      <el-button size="mini" type="danger" @click="removecparray(index, 2, item.upLoadvedioid)"
                        :disabled="isoverlist"><i class="el-icon-delete"></i></el-button>
                    </el-button-group>
                  </div>
                </div>
                <!--行end-->
              </div>
              <!--参考块二-->
              <!--参考块三-->
              <div v-if="addForm.taskPickList.includes('3')" class="dspscpd">
                <div style="height: 38px">
                  <div class="scpd">成品视频三</div>
                  <div class="zjck">
                    <el-button size="mini" type="primary" plain @click="addcparray(3)"
                      :disabled="isoverlist">+&nbsp;增加视频</el-button>
                  </div>
                </div>
                <!--行开始-->
                <div v-for="(item, index) in successArrayCk3" :key="item.upLoadvedioid">
                  <div style="margin: 2px 5px 2px 0; width: 65%; display: inline-block">
                    <el-tooltip class="item" effect="dark" :content="item.url" placement="top">
                      <el-input size="mini" placeholder="请上传" v-model="item.url" :disabled="true"></el-input>
                    </el-tooltip>
                  </div>
                  <div style=" float: right;  display: inline-block; position: relative;  top: 2px; ">
                    <el-upload :file-list="fileList" style="display: inline-block; margin:0 0px;" class="avatar-uploader"
                      :accept="videotype" action="#" :limit="1" :on-success="uploadcpSuccess1"
                      :http-request="uploadcpSuccess" :disabled="isoverlist"
                      :data="{ 'cparrayindex': 3, 'index': index, 'upLoadvedioid': item.upLoadvedioid }"
                      :show-file-list="false">
                      <el-button size="mini" type="primary" :disabled="isoverlist"><i
                          class="el-icon-upload"></i></el-button>
                    </el-upload>
                    <el-button-group>
                      <el-button size="mini" type="primary" @click="playVideo(item.url)"><i
                          class="el-icon-video-play"></i></el-button>
                      <el-button size="mini" type="primary" @click="downinfo(item.url, item.fileName)"><i
                          class="el-icon-download"></i></el-button>
                      <el-button size="mini" type="danger" @click="removecparray(index, 3, item.upLoadvedioid)"
                        :disabled="isoverlist"><i class="el-icon-delete"></i></el-button>
                    </el-button-group>
                  </div>
                </div>
                <!--行end-->
              </div>
              <!--参考块三-->
              <!--参考块四-->
              <div v-if="addForm.taskPickList.includes('4')" class="dspscpd">
                <div style="height: 38px">
                  <div class="scpd">成品视频四</div>
                  <div class="zjck">
                    <el-button size="mini" type="primary" plain @click="addcparray(4)"
                      :disabled="isoverlist">+&nbsp;增加视频</el-button>
                  </div>
                </div>
                <!--行开始-->
                <div v-for="(item, index) in successArrayCk4" :key="item.upLoadvedioid">
                  <div style="margin: 2px 5px 2px 0; width: 65%; display: inline-block">
                    <el-tooltip class="item" effect="dark" :content="item.url" placement="top">
                      <el-input size="mini" placeholder="请上传" v-model="item.url" :disabled="true"></el-input>
                    </el-tooltip>
                  </div>
                  <div style=" float: right;  display: inline-block; position: relative;  top: 2px; ">
                    <el-upload :file-list="fileList" style="display: inline-block; margin:0 0px;" class="avatar-uploader"
                      :accept="videotype" action="#" :limit="1" :on-success="uploadcpSuccess1"
                      :http-request="uploadcpSuccess" :disabled="isoverlist"
                      :data="{ 'cparrayindex': 4, 'index': index, 'upLoadvedioid': item.upLoadvedioid }"
                      :show-file-list="false">
                      <el-button size="mini" type="primary" :disabled="isoverlist"><i
                          class="el-icon-upload"></i></el-button>
                    </el-upload>
                    <el-button-group>
                      <el-button size="mini" type="primary" @click="playVideo(item.url)"><i
                          class="el-icon-video-play"></i></el-button>
                      <el-button size="mini" type="primary" @click="downinfo(item.url, item.fileName)"><i
                          class="el-icon-download"></i></el-button>
                      <el-button size="mini" type="danger" @click="removecparray(index, 4, item.upLoadvedioid)"
                        :disabled="isoverlist"><i class="el-icon-delete"></i></el-button>
                    </el-button-group>
                  </div>
                </div>
                <!--行end-->
              </div>
              <!--参考块四-->
              <!--参考块五-->
              <div v-if="addForm.taskPickList.includes('5')" class="dspscpd">
                <div style="height: 38px">
                  <div class="scpd">成品视频五</div>
                  <div class="zjck">
                    <el-button size="mini" type="primary" plain @click="addcparray(5)"
                      :disabled="isoverlist">+&nbsp;增加视频</el-button>
                  </div>
                </div>
                <!--行开始-->
                <div v-for="(item, index) in successArrayCk5" :key="item.upLoadvedioid">
                  <div style="margin: 2px 5px 2px 0; width: 65%; display: inline-block">
                    <el-tooltip class="item" effect="dark" :content="item.url" placement="top">
                      <el-input size="mini" placeholder="请上传" v-model="item.url" :disabled="true"></el-input>
                    </el-tooltip>
                  </div>
                  <div style=" float: right;  display: inline-block; position: relative;  top: 2px; ">
                    <el-upload :file-list="fileList" style="display: inline-block; margin:0 0px;" class="avatar-uploader"
                      :accept="videotype" action="#" :limit="1" :on-success="uploadcpSuccess1"
                      :http-request="uploadcpSuccess" :disabled="isoverlist"
                      :data="{ 'cparrayindex': 5, 'index': index, 'upLoadvedioid': item.upLoadvedioid }"
                      :show-file-list="false">
                      <el-button size="mini" type="primary" :disabled="isoverlist"><i
                          class="el-icon-upload"></i></el-button>
                    </el-upload>
                    <el-button-group>
                      <el-button size="mini" type="primary" @click="playVideo(item.url)"><i
                          class="el-icon-video-play"></i></el-button>
                      <el-button size="mini" type="primary" @click="downinfo(item.url, item.fileName)"><i
                          class="el-icon-download"></i></el-button>
                      <el-button size="mini" type="danger" @click="removecparray(index, 5, item.upLoadvedioid)"
                        :disabled="isoverlist"><i class="el-icon-delete"></i></el-button>
                    </el-button-group>
                  </div>
                </div>
                <!--行end-->
              </div>
              <!--参考块五-->
            </div>
          </el-form>
          <!-- 上传片段 end -->
        </div>
        <!-- 操作项 end -->
        <!-- 操作日志 star--------------------------------------------------------------------------------------------------------------------- -->
        <div class="dspczrz"></div>
        <div class="dspczrzx" v-for="(item, index ) in loginfoList " :key="index">
          <div>
            <div class="rztx"></div>
            <div class="rzmz">{{ item.name }}</div>
            <div class="rzxgx"> {{ item.changeinfo }}</div>
            <div class="rzxgsj"> {{ item.time }}</div>
          </div>
          <div>
            <div class="rzxgq">修改后：</div>
            <el-tooltip class="item" effect="dark" :content="item.before" placement="top">
              <div class="rzxgnr"> {{ item.before }}</div>
            </el-tooltip>
          </div>
          <div>
            <div class="rzxgq">修改前：</div>
            <el-tooltip class="item" effect="dark" :content="item.after" placement="top">
              <div class="rzxgnr"> {{ item.after }}</div>
            </el-tooltip>
          </div>
        </div>
        <!-- 操作日志 end---------------------------------------------------------------------------------------------------------------------------------- -->
        <div class="qxtj"></div>
      </div>
    </el-form>
    <!--选择商品-->
    <el-dialog title="选择产品" :visible.sync="productVisible" width='95%' v-dialogDrag append-to-body>
      <productselect :ischoice="true" ref="productselect" style="z-index:10000;height:500px" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="productVisible = false">取 消</el-button>
          <el-button type="primary" @click="onQuerenProduct()">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!--选择商品-->
    <el-dialog title="选择商品编码" :visible.sync="goodschoiceVisible" width='95%' height='500px' append-to-body v-dialogDrag>
      <goodschoice :ischoice="true" ref="goodschoice" style="z-index:10000;height:500px" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="goodschoiceVisible = false">取 消</el-button>
          <el-button type="primary" @click="onQueren()">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!--视频播放-->
    <el-dialog title="视频播放" :visible.sync="dialogVisible" width="50%" @close="closeVideoPlyer" :append-to-body="true"
      v-dialogDrag>
      <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeVideoPlyer">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>

<script>
import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
import MyContainer from "@/components/my-container";
import productselect from "@/views/operatemanage/base/productselect";
import goodschoice from "@/views/base/goods/goods3.vue";
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import {
  addOrUpdateVideoTaskAsync, getVideoTask2PdListInfo, uplodPdVideoAsync, deleteTaskActionAsync, unSignTaskActionAsync, signTaskActionAsync
  , unEndTaskActionAsync, endTaskActionAsync, getTaskChangeAsync,
  delVideoTaskPdInfo, pickVideoTaskAsync, unPickVideoTaskAsync, getOutcomeVideo, delOutcomeVideo,
  uploadOutcomeVideo, cutPickVideoTaskAsync, unCutPickVideoTaskAsync
} from '@/api/media/vediotask';
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew';
import { formatTime } from "@/utils";
export default {
  props: {
    fpPhotoLqNameList: { type: Array, default: () => { return []; } },
    erpUserInfoList: { type: Array, default: () => { return []; } },
    taskUrgencyList: { type: Array, default: () => { return []; }  },
    groupList: { type: Array, default: () => { return []; } },
    platformList: { type: Array, default: () => { return []; }  },
    warehouselist: { type: Array, default:() => { return []; }  },
    islook: { type: Boolean, default: false },
    isoverlist: { type: Boolean, default: false },
    onCloseAddForm: { type: Function, default: null }
  },
  components: { productselect, MyContainer, goodschoice, videoplayer },
  data() {
    return {
      dialogVisible: false,
      videoplayerReload: false,
      videoUrl: null,
      videotype: ".mp4,.mov,.vedio,.av,.wmv,.mpg,.mpeg,.rm,.flv,.swf",
      addForm: {
        dockingPeopleStr:null,
        dockingPeopleStr2:null,
        dockingPeopleStr3:null,
        dockingPeopleStr4:null,
        dockingPeopleStr5:null,
        videoTaskId: null,
        productShortName: null,
        goodCode: null,
        productId: null,
        platform: null,
        shopName: null,
        operationGroup: null,
        dockingPeople: null,
        taskUrgency: 9,
        warehouse: null,
        sampleRrderNo: null,
        sampleExpressNo: null,
        taskPickList: [],
        taskPickids: "",
        taskRemark: null,
        claimantId1: null,
        claimantId2: null,
        claimantId3: null,
        claimantId4: null,
        claimantId5: null,
        claimTime1: null,
        claimTime2: null,
        claimTime3: null,
        claimTime4: null,
        claimTime5: null,
        claimant1: null,
        claimant2: null,
        claimant3: null,
        claimant4: null,
        claimant5: null,

        cutClaimantId1: null,
        cutClaimantId2: null,
        cutClaimantId3: null,
        cutClaimantId4: null,
        cutClaimantId5: null,
        cutClaimTime1: null,
        cutClaimTime2: null,
        cutClaimTime3: null,
        cutClaimTime4: null,
        cutClaimTime5: null,
        cutClaimant1: null,
        cutClaimant2: null,
        cutClaimant3: null,
        cutClaimant4: null,
        cutClaimant5: null,
        //分配人
        CuteLqName: null,
        pdArray: [],

      },
      shopList: [],
      productVisible: false,
      activeIndex: "1",
      productVisible: false,
      goodschoiceVisible: false,
      pageLoading: false,
      inputshow: true,
      pdArrayCk1: [],
      pdArrayCk2: [],
      pdArrayCk3: [],
      pdArrayCk4: [],
      pdArrayCk5: [],
      successArrayCk1: [],
      successArrayCk2: [],
      successArrayCk3: [],
      successArrayCk4: [],
      successArrayCk5: [],
      pdArrayUpInfo: {},
      cpArrayUpInfo: {},
      upprocess: 0,
      atfterUplaodData: null,
      fileList: [],
      loginfoList: [],
      upmsginfo: '',
      useroptions1:[],
    };
  },
  computed: {
    calcAddFormRules() {
      return {
        productShortName: [{ required: true, message: '请填写', trigger: 'blur' }],

        operationGroup: [{ required: true, message: '请选择', trigger: 'blur' }],
        taskPickList: [{ required: true, message: '请选择', trigger: 'blur' }],
        platform: [{ required: true, message: '请选择', trigger: 'blur' }],
        warehouse: [{ required: true, message: '请选择', trigger: 'blur' }],
        taskUrgency: [{ required: true, message: '请选择', trigger: 'blur' }],
        productId: [{ required: true, message: '请选择', trigger: 'blur' }],
        productCode: [{ required: true, message: '请选择', trigger: 'blur' }],
        fpDetailLqName: [{ required: true, message: '请选择', trigger: 'blur' }],
        packClass: [{ required: true, message: '请选择', trigger: 'blur' }],
        brand: [{ required: true, message: '请选择', trigger: 'blur' }],
        izcjdz: [{ required: true, message: '请选择', trigger: 'blur' }],
      }
    }
  },
  async mounted() {
    if (this.addForm.dockingPeople == null)
      this.addForm.dockingPeople = this.$store.getters.userName?.split("-")[0].trim();
  },
  methods: {
     // 负责人分配过滤
     remoteMethod1 (query) {
        if (query !== '') {
          setTimeout(() => {
            this.useroptions1 = this.erpUserInfoList.filter(item => {
              return item.label.toLowerCase()
                .indexOf(query.toLowerCase()) > -1;
            });
          }, 200);
        } else {
          this.useroptions1 = [];
        }
    },
    //批量终止重启--------------------------------------------------------------------------------------------------------------------
    async endTaskAction() {
      this.$confirm("终止任务，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await endTaskActionAsync([this.addForm.videoTaskId]);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
        }
      });
    },
    //批量终止
    async unEndTaskAction() {
      this.$confirm("重启任务，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await unEndTaskActionAsync([this.addForm.videoTaskId]);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
        }
      });
    },
    //批量标记
    async signTaskAction() {
      this.$confirm("标记任务，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await signTaskActionAsync([this.addForm.videoTaskId]);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
        }
      });
    },
    //取消标记
    async unSignTaskAction() {
      this.$confirm("取消标记，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await unSignTaskActionAsync([this.addForm.videoTaskId]);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
        }
      });
    },
    //批量删除
    async deleteTaskAction() {
      this.$confirm("删除任务，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await deleteTaskActionAsync([this.addForm.videoTaskId]);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onCloseAddForm(3);
        }
      });
    },
    async closeVideoPlyer() {
      this.dialogVisible = false;
      this.videoplayerReload = false;
    },
    playVideo(videoUrl) {
      this.videoplayerReload = false;
      this.videoplayerReload = true;
      this.dialogVisible = true;
      this.videoUrl = videoUrl;
    },
    //完成操作开始-------------------------------------------------------------------------------------------------------------------------------
    //拍摄完成任务
    async pickTask(index) {
      var that = this;
      var msg = "确认拍摄完成, 是否继续?";
      if (index == 0)
        msg = "确认领取, 是否继续?";
      this.$confirm(msg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await pickVideoTaskAsync({ videoTaskId: this.addForm.videoTaskId, index: index })
        if (res?.success) {
          that.$message({ message: '操作成功', type: "success" });
          var name = this.$store.getters.userName?.split("-")[0].trim();
          switch (index) {
            case 1:
              this.addForm.claimant1 = name;
              this.addForm.claimantId1 = 10;
              this.addForm.claimTime1 = new Date();
              break;
            case 2:
              this.addForm.claimant2 = name;
              this.addForm.claimantId2 = 10;
              this.addForm.claimTime2 = new Date();
              break;
            case 3:
              this.addForm.claimant3 = name;
              this.addForm.claimantId3 = 10;
              this.addForm.claimTime3 = new Date();
              break;
            case 4:
              this.addForm.claimant4 = name;
              this.addForm.claimantId4 = 10;
              this.addForm.claimTime4 = new Date();
              break;
            case 5:
              this.addForm.claimant5 = name;
              this.addForm.claimantId5 = 10;
              this.addForm.claimTime5 = new Date();
              break;
          }
          await this.editTask({ platform: this.addForm.platform, videoTaskId: this.addForm.videoTaskId });
        }
      });
    },
    //取消拍摄完成任务
    async unPickTask(index) {
      var that = this;
      var msg = "确认取消拍摄完成, 是否继续?";
      this.$confirm(msg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await unPickVideoTaskAsync({ videoTaskId: this.addForm.videoTaskId, index: index })
        if (res?.success) {
          that.$message({ message: '取消成功', type: "success" });
          switch (index) {
            case 1:
              this.addForm.claimant1 = null;
              this.addForm.claimantId1 = 0;
              this.addForm.claimTime1 = null;
              break;
            case 2:
              this.addForm.claimant2 = null;
              this.addForm.claimantId2 = 0;
              this.addForm.claimTime2 = null;
              break;
            case 3:
              this.addForm.claimant3 = null;
              this.addForm.claimantId3 = 0;
              this.addForm.claimTime3 = null;
              break;
            case 4:
              this.addForm.claimant4 = null;
              this.addForm.claimantId4 = 0;
              this.addForm.claimTime4 = null;
              break;
            case 5:
              this.addForm.claimant5 = null;
              this.addForm.claimantId5 = 0;
              this.addForm.claimTime5 = null;
              break;
          }
          await this.editTask({ platform: this.addForm.platform, videoTaskId: this.addForm.videoTaskId });
        }
      });
    },
    //取消拍摄领取任务
    async unCutPickTask(index) {
      var that = this;
      this.$confirm("确认取消完成剪辑, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await unCutPickVideoTaskAsync({ videoTaskId: this.addForm.videoTaskId, index: index })
        if (res?.success) {
          that.$message({ message: '取消成功', type: "success" });
          switch (index) {
            case 1:
              this.addForm.cutClaimant1 = null;
              this.addForm.cutClaimantId1 = 0;
              this.addForm.cutClaimTime1 = null;
              break;
            case 2:
              this.addForm.cutClaimant2 = null;
              this.addForm.cutClaimantId2 = 0;
              this.addForm.cutClaimTime2 = null;
              break;
            case 3:
              this.addForm.cutClaimant3 = null;
              this.addForm.cutClaimantId3 = 0;
              this.addForm.cutClaimTime3 = null;
              break;
            case 4:
              this.addForm.cutClaimant4 = null;
              this.addForm.cutClaimantId4 = 0;
              this.addForm.cutClaimTime4 = null;
              break;
            case 5:
              this.addForm.cutClaimant5 = null;
              this.addForm.cutClaimantId5 = 0;
              this.addForm.cutClaimTime5 = null;
              break;
          }
        }
      });
    },
    //拍摄领取任务
    async cutPickTask(index) {
      var that = this;
      this.$confirm("确认完成剪辑, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await cutPickVideoTaskAsync({ videoTaskId: this.addForm.videoTaskId, index: index })
        if (res?.success) {
          that.$message({ message: '操作成功', type: "success" });
          var name = that.$store.getters.userName?.split("-")[0].trim();
          switch (index) {
            case 1:
              this.addForm.cutClaimant1 = name;
              this.addForm.cutClaimantId1 = 10;
              this.addForm.cutClaimTime1 = new Date();
              break;
            case 2:
              this.addForm.cutClaimant2 = name;
              this.addForm.cutClaimantId2 = 10;
              this.addForm.cutClaimTime2 = new Date();
              break;
            case 3:
              this.addForm.cutClaimant3 = name;
              this.addForm.cutClaimantId3 = 10;
              this.addForm.cutClaimTime3 = new Date();
              break;
            case 4:
              this.addForm.cutClaimant4 = name;
              this.addForm.cutClaimantId4 = 10;
              this.addForm.cutClaimTime4 = new Date();
              break;
            case 5:
              this.addForm.cutClaimant5 = name;
              this.addForm.cutClaimantId5 = 10;
              this.addForm.cutClaimTime5 = new Date();
              break;
          }
        }
      });
    },

    //完成操作结束-------------------------------------------------------------------------------------------------------------------------------
    //添加参考片段-------------------------------------------------------------------------------------------------------------------------------
    addpdarray(ckindex) {
      var tempdata = { pdId: 0, url: null, ckVideoIndex: ckindex };
      switch (ckindex) {
        case 1:
          this.pdArrayCk1.push(tempdata);
          break;
        case 2:
          this.pdArrayCk2.push(tempdata);
          break;
        case 3:
          this.pdArrayCk3.push(tempdata);
          break;
        case 4:
          this.pdArrayCk4.push(tempdata);
          break;
        case 5:
          this.pdArrayCk5.push(tempdata);
          break;
      }
    },
    //移除参考片段
    async removepdarray(index, ckindex, pdId) {
      this.$confirm("该删除会一并删除剪切片段视频,是否确定删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        if (pdId > 0) {
          //删除校验
          var res = await delVideoTaskPdInfo({ videoTaskId: this.addForm.videoTaskId, pdId: pdId });
          if (!res?.success) return;
        }
        //校验是否可以删除，如果已剪切，或已完成不可删除（23.10.08已剪切一起删除）
        switch (ckindex) {
          case 1:
            this.pdArrayCk1.splice(index, 1);
            break;
          case 2:
            this.pdArrayCk2.splice(index, 1);
            break;
          case 3:
            this.pdArrayCk3.splice(index, 1);
            break;
          case 4:
            this.pdArrayCk4.splice(index, 1);
            break;
          case 5:
            this.pdArrayCk5.splice(index, 1);
            break;
        }
      });

    },
    //参考片段结束-------------------------------------------------------------------------------------------------------------------------------
    //任务视频数量改变校验
    taskpickchange() {

    },
    isformatTime(val) {
      return val == null ? null : formatTime(val, "YY-MM-DD")
    },
    //下拉改变值
    inputshowfunc() {
      if (this.islook) return;
      this.inputshow = false;
    },
    //选择商品------------------------------------------------------------------------------------------------------------------------------
    onSelctCp() {
      if (this.islook) return;
      this.goodschoiceVisible = true;
    },
    //打开选择产品id窗口------------------------------------------------------------------------------------------------------------------------------
    onSelctProduct() {
      if (this.islook) return;
      this.productVisible = true;
    },
    //选择商品确定------------------------------------------------------------------------------------------------------------------------------
    async onQueren() {
      var choicelist = await this.$refs.goodschoice.getchoicelist();
      if (choicelist) {
        var goodCodeList = [];
        choicelist.forEach((item) => {
          goodCodeList.push(item.goodsCode);
        });
        this.addForm.goodCode = goodCodeList.join(',');
        this.goodschoiceVisible = false;
      }
    },
    //选择产品id------------------------------------------------------------------------------------------------------------------------------
    async onQuerenProduct() {
      var choicelist = await this.$refs.productselect.getchoicelistOnly();
      if (choicelist && choicelist.length == 1) {
        this.addForm.productId = choicelist[0].proCode;
        this.addForm.productShortName = choicelist[0].styleCode;
        this.addForm.shopId = choicelist[0].shopId;
        this.addForm.shopName = choicelist[0].shopName;
        this.addForm.platform = choicelist[0].platform;
        this.addForm.operationsGroup = choicelist[0].groupId;
        this.productVisible = false;
      }
    },
    //切换平台------------------------------------------------------------------------------------------------------------------------------
    async onchangeplatform(val) {
      var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 10000 });

      this.shopList = res1.data.list;
    },
    //编辑页面
    async editTask(row) {
      this.pageLoading = true;
      await this.onchangeplatform(row.platform);
      //获取拍摄上传的附件,和任务信息
      var res = await getVideoTask2PdListInfo({ videoTaskId: row.videoTaskId });
      if (res?.success) {
        this.addForm = res.data.task;
        this.addForm.pdArray = [],
          this.pdArrayCk1 = res.data.pdArrayCk1;
        this.pdArrayCk2 = res.data.pdArrayCk2;
        this.pdArrayCk3 = res.data.pdArrayCk3;
        this.pdArrayCk4 = res.data.pdArrayCk4;
        this.pdArrayCk5 = res.data.pdArrayCk5;
      } else {
        this.addForm = row;
      }
      var rest = await getOutcomeVideo({ videoTaskId: row.videoTaskId });
      if (rest?.success) {
        this.successArrayCk1 = rest.data[0].details;
        this.successArrayCk2 = rest.data[1].details;
        this.successArrayCk3 = rest.data[2].details;
        this.successArrayCk4 = rest.data[3].details;
        this.successArrayCk5 = rest.data[4].details;
      } else {
      }
      var rest = await getTaskChangeAsync({ taskid: row.videoTaskId });
      if (rest?.success) {
        this.loginfoList = rest.data
      } else {
        this.loginfoList = []
      }
      this.pageLoading = false;
    },

    //提交保存
    async submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          //保存前收集片段信息
          if (this.addForm.taskPickList.includes('1')) {
            for (let num in this.pdArrayCk1) {
              this.addForm.pdArray.push(this.pdArrayCk1[num]);
            }
          }
          //保存前收集片段信息
          if (this.addForm.taskPickList.includes('2')) {
            for (let num in this.pdArrayCk2) {
              this.addForm.pdArray.push(this.pdArrayCk2[num]);
            }
          }
          //保存前收集片段信息
          if (this.addForm.taskPickList.includes('3')) {
            for (let num in this.pdArrayCk3) {
              this.addForm.pdArray.push(this.pdArrayCk3[num]);
            }
          }
          //保存前收集片段信息
          if (this.addForm.taskPickList.includes('4')) {
            for (let num in this.pdArrayCk4) {
              this.addForm.pdArray.push(this.pdArrayCk4[num]);
            }
          }
          //保存前收集片段信息
          if (this.addForm.taskPickList.includes('5')) {
            for (let num in this.pdArrayCk5) {
              this.addForm.pdArray.push(this.pdArrayCk5[num]);
            }
          }
          if(!this.addForm.taskPickList.includes('1') )
          this.addForm.dockingPeopleStr =null;
          if(!this.addForm.taskPickList.includes('2') )
          this.addForm.dockingPeopleStr2 =null;
          if(!this.addForm.taskPickList.includes('3') )
              this.addForm.dockingPeopleStr3 =null;
          if(!this.addForm.taskPickList.includes('4') )
              this.addForm.dockingPeopleStr4 =null;
          if(!this.addForm.taskPickList.includes('5') )
              this.addForm.dockingPeopleStr5 =null;
          let para = _.cloneDeep(this.addForm);
          this.pageLoading = true;
          var res = await addOrUpdateVideoTaskAsync(para);
          this.pageLoading = false;
          if (!res?.success) { return; }
          this.$message({ message: this.$t('保存成功'), type: 'success' });
          this.onCloseAddForm(1);
          await this.editTask({ platform: this.addForm.platform, videoTaskId: this.addForm.videoTaskId });
        } else {
          return false;
        }
      });
    },
    //上传成品视频-------------------------------------------------------------------------------------------------------------------------------
    addcparray(ckindex) {
      var tempdata = { upLoadvedioid: 0, url: null, ckVideoIndex: ckindex };
      switch (ckindex) {
        case 1:
          this.successArrayCk1.push(tempdata);
          break;
        case 2:
          this.successArrayCk2.push(tempdata);
          break;
        case 3:
          this.successArrayCk3.push(tempdata);
          break;
        case 4:
          this.successArrayCk4.push(tempdata);
          break;
        case 5:
          this.successArrayCk5.push(tempdata);
          break;
      }
    },
    removecparray(index, ckindex, upLoadvedioid) {
      this.$confirm("是否确定删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        if (upLoadvedioid > 0) {
          //删除校验
          const res = await delOutcomeVideo({ upLoadvedioid: upLoadvedioid });
          if (!res?.success) return;
        }
        //校验是否可以删除，如果已剪切，或已完成不可删除
        switch (ckindex) {
          case 1:
            this.successArrayCk1.splice(index, 1);
            break;
          case 2:
            this.successArrayCk2.splice(index, 1);
            break;
          case 3:
            this.successArrayCk3.splice(index, 1);
            break;
          case 4:
            this.successArrayCk4.splice(index, 1);
            break;
          case 5:
            this.successArrayCk5.splice(index, 1);
            break;
        }
      });
    },
    uploadcpSuccess1() {
      this.fileList = [];
    },
    async uploadcpSuccess(item) {
      if (this.upprocess > 0 && this.upprocess < 100) {
        this.$message({ message: '正在上传，请勿操作', type: "warning" });
        return;
      }
      this.upprocess = 0;
      this.cpArrayUpInfo = item.data;
      this.pageLoading = true;
      await this.AjaxFile(item.file, 0, "").then(async x => {
        this.upmsginfo='';
        if (this.atfterUplaodData != null) {
          await this.AfterUpSuccesslaod(item.file.name).then(x => {
            this.pageLoading = false;
          }).catch(err => {
            this.$message({ message: '网络错误！请稍后', type: "warning" });
            this.pageLoading = false;
            this.upmsginfo='';
          });
        }
      }).catch(err => {
        this.$message({ message: '网络错误！请稍后', type: "warning" });
        this.pageLoading = false;
        this.upmsginfo='';
      });
      this.pageLoading = false;
    },
    //上传成果
    async AfterUpSuccesslaod(filename) {
      const form = new FormData();
      var cururl = this.atfterUplaodData.url;
      this.atfterUplaodData.fileName = filename;
      form.append("upfile", JSON.stringify(this.atfterUplaodData));
      form.append("index", this.cpArrayUpInfo.cparrayindex);
      form.append("taskId", this.addForm.videoTaskId);
      form.append("upLoadvedioid", this.cpArrayUpInfo.upLoadvedioid);
      let ckindexofarray = this.cpArrayUpInfo.index;
      const res = await uploadOutcomeVideo(form);

      switch (this.cpArrayUpInfo.cparrayindex) {
        case 1:
          this.successArrayCk1[ckindexofarray].url = cururl;
          this.successArrayCk1[ckindexofarray].fileName = filename;
          this.successArrayCk1[ckindexofarray].upLoadvedioid = res.data.uploadId;
          break;
        case 2:
          this.successArrayCk2[ckindexofarray].url = cururl;
          this.successArrayCk2[ckindexofarray].upLoadvedioid = res.data.uploadId;
          this.successArrayCk2[ckindexofarray].fileName = filename;
          break;
        case 3:
          this.successArrayCk3[ckindexofarray].url = cururl;
          this.successArrayCk3[ckindexofarray].upLoadvedioid = res.data.uploadId;
          this.successArrayCk3[ckindexofarray].fileName = filename;
          break;
        case 4:
          this.successArrayCk4[ckindexofarray].url = cururl;
          this.successArrayCk4[ckindexofarray].upLoadvedioid = res.data.uploadId;
          this.successArrayCk4[ckindexofarray].fileName = filename;
          break;
        case 5:
          this.successArrayCk5[ckindexofarray].url = cururl;
          this.successArrayCk5[ckindexofarray].upLoadvedioid = res.data.uploadId;
          this.successArrayCk5[ckindexofarray].fileName = filename;
          break;
      }

    },
    //上传片段-------------------------------------------------------------------------------------------------------------------------------
    async uploadpdFile(item) {
      if (this.upprocess > 0 && this.upprocess < 100) {
        this.$message({ message: '正在上传，请勿操作', type: "warning" });
        return;
      }
      this.upprocess = 0;
      this.pdArrayUpInfo = item.data;
      this.pageLoading = true;
      await this.AjaxFile(item.file, 0, "").then(async x => {
        this.upmsginfo='';
        if (this.atfterUplaodData != null) {
          await this.AfteruploadpdFile().then(x => {
            this.pageLoading = false;
          }).catch(err => {
            this.$message({ message: '网络错误！请稍后', type: "warning" });
            this.upmsginfo='';
            this.pageLoading = false;
          });
        }
      }).catch(err => {
        this.$message({ message: '网络错误！请稍后', type: "warning" });
        this.upmsginfo='';
        this.pageLoading = false;
      });


    },
    async AjaxFile(file, i, batchnumber) {
      var name = file.name; //文件名
      this.upmsginfo='正在上传 ‘'+ name +'’' +this.upprocess  +'%';
      var size = file.size; //总大小shardSize = 2 * 1024 * 1024,
      var shardSize = 1 *1024* 1024;
      var shardCount = Math.ceil(size / shardSize); //总片数
      if (i >= shardCount) {
        return;
      }
      //计算每一片的起始与结束位置
      var start = i * shardSize;
      var end = Math.min(size, start + shardSize);
      //构造一个表单，FormData是HTML5新增的
      i = i + 1;
      var form = new FormData();
      form.append("data", file.slice(start, end)); //slice方法用于切出文件的一部分
      form.append("batchnumber", batchnumber);
      form.append("fileName", name);
      form.append("total", shardCount); //总片数
      form.append("index", i); //当前是第几片

      const res = await xMTVideoUploadBlockAsync(form,{headers: {'Content-Type': 'multipart/form-data'}});
      if (res?.success) {
        this.upprocess = (i * 100 / shardCount).toFixed(2);

        if (i == shardCount) {
          this.atfterUplaodData = res.data;
        } else {
          await this.AjaxFile(file, i, res.data);
        }
      } else {
        this.$message({ message: res?.msg, type: "warning" });
      }
    },

    async AfteruploadpdFile() {
      var ckindexofarray = this.pdArrayUpInfo.index;
      var pdid = 0;
      switch (this.pdArrayUpInfo.pdarrayindex) {
        case 1:
          pdid = this.pdArrayCk1[ckindexofarray].pdId ?? 0;
          break;
        case 2:
          pdid = this.pdArrayCk2[ckindexofarray].pdId ?? 0;
          break;
        case 3:
          pdid = this.pdArrayCk3[ckindexofarray].pdId ?? 0;
          break;
        case 4:
          pdid = this.pdArrayCk4[ckindexofarray].pdId ?? 0;
          break;
        case 5:
          pdid = this.pdArrayCk5[ckindexofarray].pdId ?? 0;
          break;
      }
      var form = new FormData();
      form.append("upfile", JSON.stringify(this.atfterUplaodData));
      form.append("index", this.pdArrayUpInfo.pdarrayindex);
      form.append("pdId", pdid);
      form.append("videotaskid", this.addForm.videoTaskId);
      this.addLoading = true;
      const res = await uplodPdVideoAsync(form);
      if (res?.success) {
        switch (this.pdArrayUpInfo.pdarrayindex) {
          case 1:
            this.pdArrayCk1[ckindexofarray].url = res.data.videoFullPath;
            this.pdArrayCk1[ckindexofarray].pdId = res.data.pdId;
            this.pdArrayCk1[ckindexofarray].cuteId = res.data.cuteId;
            break;
          case 2:
            this.pdArrayCk2[ckindexofarray].url = res.data.videoFullPath;
            this.pdArrayCk2[ckindexofarray].pdId = res.data.pdId;
            this.pdArrayCk2[ckindexofarray].cuteId = res.data.cuteId;
            break;
          case 3:
            this.pdArrayCk3[ckindexofarray].url = res.data.videoFullPath;
            this.pdArrayCk3[ckindexofarray].pdId = res.data.pdId;
            this.pdArrayCk3[ckindexofarray].cuteId = res.data.cuteId;
            break;
          case 4:
            this.pdArrayCk4[ckindexofarray].url = res.data.videoFullPath;
            this.pdArrayCk4[ckindexofarray].pdId = res.data.pdId;
            this.pdArrayCk4[ckindexofarray].cuteId = res.data.cuteId;
            break;
          case 5:
            this.pdArrayCk5[ckindexofarray].url = res.data.videoFullPath;
            this.pdArrayCk5[ckindexofarray].pdId = res.data.pdId;
            this.pdArrayCk5[ckindexofarray].cuteId = res.data.cuteId;
            break;
        }
        this.$message({ message: '上传成功', type: "success" });
        this.addLoading = false;
      } else {
        this.addLoading = false;
        this.$message({ message: '上传失败', type: "warning" });
      }


    },

    async uploadpdSuccess(response, file, fileList) {
      this.fileList = [];
    },
    //上传片段结束-------------------------------------------------------------------------------------------------------------------------------

    async downinfo(url, fileName) {
      if (url == "" || url == null) {
        this.$message({ message: '无效链接', type: "warning" });
        return;
      }
      var xhr = new XMLHttpRequest();
      xhr.open('GET', url, true);
      xhr.responseType = 'arraybuffer'; // 返回类型blob
      xhr.onload = function () {
        debugger
        if (xhr.readyState === 4 && xhr.status === 200) {
          let blob = this.response;
          console.log(blob);
          // 转换一个blob链接
          // 注: URL.createObjectURL() 静态方法会创建一个 DOMString(DOMString 是一个UTF-16字符串)，
          // 其中包含一个表示参数中给出的对象的URL。这个URL的生命周期和创建它的窗口中的document绑定
          let downLoadUrl = window.URL.createObjectURL(new Blob([blob], { type: 'video/mp4' }));
          // 视频的type是video/mp4，图片是image/jpeg
          // 01.创建a标签
          let a = document.createElement('a');
          // 02.给a标签的属性download设定名称
          a.download = fileName;
          // 03.设置下载的文件名
          a.href = downLoadUrl;
          // 04.对a标签做一个隐藏处理
          a.style.display = 'none';
          // 05.向文档中添加a标签
          document.body.appendChild(a);
          // 06.启动点击事件
          a.click();
          // 07.下载完毕删除此标签
          a.remove();
        } else {

          this.$message({ message: '无效链接', type: "warning" });
        }
      };
      xhr.send();
    },
  },
};
</script>
<style  lang="scss" scoped>
::v-deep .el-form-item {
  margin: 0px !important;
}

::v-deep .el-input--mini {
  margin: 0 !important;
}

::v-deep .el-form-item__error {
  position: absolute !important;
  top: 30% !important;
  left: 400px !important;
  width: 60px !important;
}

::v-deep .dspbjrw {
  width: 700px;
  background-color: #fff;
}

::v-deep .dspbjrw .dspbjbt {
  height: 60px;
  background-color: rgb(255, 255, 255);
  font-size: 18px;
  color: #666;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 20px 35px;
}

::v-deep .dspbjrw .dspbjbt i {
  color: #999;
}

::v-deep .dspbjrw .dspbjbt i {
  margin-left: 8px;
  line-height: 26px;
}

::v-deep .dspbjrw .dspbjbt i:hover {
  margin-left: 8px;
  line-height: 26px;
  color: #409eff;
  position: relative;
  top: -2px;
}

::v-deep .dspbjrw .rwmc {
  width: 700px;
  height: 70px;
  background-color: rgb(255, 255, 255);
  float: left;
  box-shadow: 0px 3px 5px #eeeeee;
  box-sizing: border-box;
  padding: 0 56px;
}

::v-deep .dspbjrw .rwmc .xh {
  height: 65px;
  font-size: 18px;
  line-height: 68px;
  box-sizing: border-box;
  padding: 0 2px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #666;
}

::v-deep .dspbjrw .rwmc .mc,
.icon {
  height: 65px;
  font-size: 18px;
  line-height: 68px;
  box-sizing: border-box;
  margin-left: 10px;
  padding: 0 2px;
  display: inline-block;
  color: #666;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::v-deep .dspbjrw .dspbjlx {
  width: 100%;
  height: 35px;
  /* background-color: bisque; */
  box-sizing: border-box;
  padding: 0 60px;
}

::v-deep .dspbjrw .dspbjlxf {
  width: 100%;
  /* background-color: bisque; */
  box-sizing: border-box;
  padding: 0 60px;
}

::v-deep .dspbjrw .dspbjlx .lxwz {
  width: 112px;
  font-size: 14px;
  color: #666;
  line-height: 26px;
  /* background-color: rgb(204, 204, 255); */
  display: inline-block;
}

::v-deep .dspbjrw .scpd {
  width: 100px;
  font-size: 14px;
  color: #666;
  line-height: 36px;
  /* background-color: rgb(204, 204, 255); */
  float: left;
}

::v-deep .dspbjrw .zjck {
  display: inline-block;
  float: right;
  position: relative;
  top: 3px;
}

::v-deep .dspbjrw .dspczxx {
  width: 100%;
  box-sizing: border-box;
  /* padding: 0 60px; */
  text-align: center;
  border: 1px solid #dcdfe6;
  border-right: 0px;
  border-bottom: 0px;
  border-left: 0px;
  margin: 0 0;
}

::v-deep .dspscpd {
  width: 100%;
  /* background-color: bisque; */
  box-sizing: border-box;
  padding: 5px 60px;
}

::v-deep .dspbjrw .qxtj {
  height: 30px;
  /* background-color: aquamarine; */
  box-sizing: border-box;
  padding: 10px 60px;
}

::v-deep .dspbjrw .dspsccg {
  width: 100%;
  box-sizing: border-box;
  padding: 30px 60px;
}

::v-deep .dspbjrw .dspczx {
  box-sizing: border-box;
  padding: 5px 60px;
  font-size: 14px;
  color: #666;
}

::v-deep .dspbjrw .spscpd .spczan,
.spczs,
.spczan,
.spczmz,
.spczsj {
  min-width: 50px;
  max-width: 80px;
  display: inline-block;
  margin-right: 12px;
  /* background-color: aquamarine; */
}

::v-deep .dspbjrw .spczsj {
  color: #999;
}

::v-deep .dspbjrw .spczk {
  height: 35px;
}

::v-deep .dspbjrw .spczksps {
  width: 14%;
  display: inline-block;
}

::v-deep .dspbjrw .spczkzy {
  width: 43%;
  /* background-color: aqua; */
  display: inline-block;
}

::v-deep .dspbjrw .spczs {
  width: 65px;
}

::v-deep .dspbjrw .dspbjfgx {
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 10px 0;
  padding-bottom: 35px;
}

::v-deep .dspczx {
  box-sizing: border-box;
  padding: 5px 60px;
  font-size: 14px;
  color: #666;
}

::v-deep .dspbjrw .dspczrz {
  box-sizing: border-box;
  padding: 15px 60px;
}

::v-deep .dspbjrw .dspczrzx {
  box-sizing: border-box;
  padding: 10px 60px;
}

::v-deep .dspbjrw .rztx,
.rzmz,
.rzxgx,
.rzxgsj {
  height: 30px;
  display: inline-block;
  font-size: 14px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::v-deep .dspbjrw .rztx {
  width: 25px;
  height: 25px;
  background-color: #f5f5f5;
  border-radius: 50%;
  margin-right: 15px;
}

::v-deep .dspbjrw .rzmz {
  width: 50px;
  margin-right: 5px;
}

::v-deep .dspbjrw .rzxgx {
  max-width: 200px;
  margin-right: 10px;
  color: #999;
}

::v-deep .dspbjrw .rzxgsj {
  max-width: 200px;
  color: #999;
}

::v-deep .dspbjrw .rzxgq,
.rzxgnr {
  max-width: 450px;
  line-height: 15px;
  display: inline-block;
  font-size: 12px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::v-deep .dspbjrw .rzxgq {
  width: 50px;
  margin-left: 43px;
  margin-right: 2px;
}

::v-deep ::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}
</style>
