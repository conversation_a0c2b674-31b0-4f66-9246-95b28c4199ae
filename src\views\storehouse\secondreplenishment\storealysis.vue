<template>
  <container>

    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='list'
      :tableCols='tableCols' :isSelection="false" :tableHandles='tableHandles' :loading="listLoading"
      :summaryarry="summaryarry">
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template> 

  </container>
</template>

<script>
import container from '@/components/my-container'
import cesTable from "@/components/Table/table.vue";
import { getSecondReplenishmentDataGroupStore } from '@/api/inventory/secondreplenishment'

function getReplenishmentTime(second)
{
  if (second == null || second == 0) {
    return '-';
  } else {
    //多少天
    let day = parseInt(second / (60 * 60 * 24))
    //多少小时
    let hour = parseInt((second / (60 * 60))) % 24
    //多少分钟
    let min = parseInt((second / 60)) % 60
    return day + "天" + hour + "时" + min + "分";
  }
}

const tableCols = [
  { istrue: true, prop: 'name', label: '仓库', tipmesg: '', sortable: 'custom', }, 
  { istrue: true, prop: 'count', label: '补货次数', tipmesg: '',  sortable: 'custom', },
  { istrue: true, prop: 'replenishmentAvgTime', label: '平均时长', tipmesg: '', sortable: 'custom',formatter: (row) => getReplenishmentTime(row.replenishmentAvgTime)  }
]

const tableHandles = [ 
]; 

export default {
  name: 'YunHanSecondReplenishmentCountAlysis',
  components: { container, cesTable },
  props:{
    filter: {
        startTime: null,
        endTime: null,
        timerange: [null, null], 
      },
  },
  data() {
    return {
      that: this, 
      replenishmentTime: null,
      list: [], 
      pager: { OrderBy: "count", IsAsc: false },
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      sels: [], 
      listLoading: false, 
      summaryarry: {}
    };
  },

  async mounted() {
    //await this.onSearch() 
  },

  methods: { 
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    async getlist() {
      if (this.pager.OrderBy == null) {
        this.pager.OrderBy = "count";
        this.pager.IsAsc = false;
      }
      let pager = this.$refs.pager.getPager();
      let page = this.pager;
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      const params = { ...pager, ...page, ... this.filter }
      if (params === false) {
        return;
      }
      this.listLoading = true
      const res = await getSecondReplenishmentDataGroupStore(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.list = data
      this.summaryarry = res.data.summary;
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    }
  }
};
</script>

<style lang="scss" scoped></style>