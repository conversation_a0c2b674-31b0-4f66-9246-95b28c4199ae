<template>
  <MyContainer v-loading="loading">
    <template #header>
      <div class="top">
        <span class="publicCss">商品子编码：</span>
        <span class="publicCss">{{ ListInfo.skuCode }}</span>
        <span class="publicCss" style="margin-left: 30px;">库存总量：</span>
        <span class="publicCss">{{ ListInfo.totalCount }}</span>
      </div>
    </template>
    <template #default>
      <buschar v-if="dialogMapVisible.visible" ref="buschar" :analysisData="dialogMapVisible.data"
        :thisStyle="{ width: '99%', height: '750px', 'box-sizing': 'border-box', 'line-height': '360px' }">
      </buschar>
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import buschar from '@/components/Bus/buschar';
import { skuInventory } from "@/api/order/orderData";

export default {
  name: "inventoryAnalysis",
  components: {
    MyContainer, vxetablebase, buschar
  },
  data() {
    return {
      dialogMapVisible: { visible: false, title: "", data: {} },
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        skuCode: null,//sku编号
        totalCount: null,//库存总量
      },
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  activated() {
    this.loading = true
    let _this = this;
    if (_this.$route.query && _this.$route.query.skuCode) {
      _this.ListInfo.skuCode = _this.$route.query.skuCode;
      _this.ListInfo.orderByUndulate = _this.$route.query.orderByUndulate;
      _this.undulate = _this.$route.query.undulate;
      setTimeout(() => {
        _this.getList();
      }, 500);
    }
  },
  async mounted() {

  },
  beforeUpdate() {
    middlevue.$on('inventoryAnalysis', function (msg) {
      _this.dataList = [];
      _this.buscharshow = false;
      _this.ListInfo.skuCode = msg.skuCode;
      _this.ListInfo.orderByUndulate = msg.orderByUndulate;
      _this.undulate = msg.undulate;
      _this.ListInfo.provinceCity = [];
      _this.delayProvince = [];
      _this.getList();
    })
  },
  updated() {
    middlevue.$off('inventoryAnalysis');
  },
  methods: {
    async getList() {
      this.loading = true
      const params = { skuCode: this.ListInfo.skuCode }
      const { data, success } = await skuInventory(params)
      this.loading = false
      if (success) {
        this.ListInfo.totalCount = data.totalCount
        let res = {
          xAxis: data.warehouses,
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '库存量',
              type: 'bar',
              emphasis: {
                focus: 'series'
              },
              data: data.orderUseQtys
            },
            {
              name: '订单占用量',
              type: 'bar',
              emphasis: {
                focus: 'series'
              },
              data: data.usableQtys
            },
            {
              name: '聚水潭可用量',
              type: 'bar',
              emphasis: {
                focus: 'series'
              },
              data: data.jstUsableQty
            },
          ]
        };
        res.series.map((item) => {
          item.itemStyle = {
            "normal": {
              "label": {
                "show": true,
                "position": "top",
                "textStyle": {
                  "fontSize": 14
                }
              }
            }
          }
          item.emphasis = {
            "focus": "series"
          }
          item.smooth = false;
        })
        this.dialogMapVisible.data = res
        this.dialogMapVisible.visible = true;
      } else {
        //获取列表失败
        this.$message.error('获取趋势图失败')
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin: 10px 0 10px 15px;

  .publicCss {
    margin-right: 5px;
    font-size: 16px;
    color: #333;
    font-weight: bold;
  }
}
</style>
