<template>
    <div>
        <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="任务单号" v-if="editData.id">
            <el-input v-model="form.shootNo" disabled></el-input>
        </el-form-item>
        <el-form-item label="采购单号1" >
            <el-select v-model="form.buyNo1" filterable
                remote
                reserve-keyword
                :remote-method="remoteMethod"
                @change="handleChange"
                :loading="loading" placeholder="请选择">
                <el-option
                v-for="(item,index) in buyNoList"
                :key="index"
                :label="item.buyNo"
                :value="item.buyNo">
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="采购单号2">
            <el-input v-model="form.buyNo2"></el-input>
        </el-form-item>
        <el-form-item label="入库单号">
            <el-input v-model.trim="form.warehousingNo" clearable placeholder="请输入入库单号"></el-input>
        </el-form-item>
        <el-form-item label="仓库">
            <el-select v-model="form.warehouse" filterable placeholder="请选择">
                <el-option v-for="item in warehouselist" :label="item.name" :value="item.wms_co_id"></el-option>
            </el-select>
        </el-form-item>

        <el-form-item label="托运费" >
            <el-input-number @change="allAddSum" v-model="form.haulage" :min="0" :max="999999" :controls="false"
            :precision="4" placeholder="请输入" style="width: 100%;"></el-input-number>
        </el-form-item>
        <el-form-item label="送货费">
            <el-input-number @change="allAddSum" v-model="form.deliveryFee" :min="0" :max="999999" :controls="false"
            :precision="4" placeholder="请输入" style="width: 100%;"></el-input-number>
        </el-form-item>
        <el-form-item label="提货费">
            <el-input-number @change="allAddSum" v-model="form.pickUpFee" :min="0" :max="999999" :controls="false"
            :precision="4" placeholder="请输入" style="width: 100%;"></el-input-number>
        </el-form-item>
        <el-form-item label="货拉拉">
            <el-input-number @change="allAddSum" v-model="form.huoLaLa" :min="0" :max="999999" :controls="false"
            :precision="4" placeholder="请输入" style="width: 100%;"></el-input-number>
        </el-form-item>
        <el-form-item label="装车费">
            <el-input-number @change="allAddSum" v-model="form.loadingFee" :min="0" :max="999999" :controls="false"
            :precision="4" placeholder="请输入" style="width: 100%;"></el-input-number>
        </el-form-item>
        <el-form-item label="合计费用">
            <el-input-number v-model="sumnum" disabled :min="0" :max="999999" :controls="false"
            :precision="4" placeholder="请输入" style="width: 100%;"></el-input-number>
        </el-form-item>
        <el-form-item label="">
            <el-row>
                <el-col :span="7">
                    <el-button style="margin-left: 10px;" :disabled="form.id" type="primary" @click="onSubmit">生成二维码</el-button>
                    <div style="" v-if="logVisible">
                        <canvas :id="'QRCode1'"></canvas>
                        <div style="font-size: 11px;">{{ editData.id }}</div>
                    </div>
                </el-col>
                <el-col :span="17" v-if="codeImg">

                    <el-form-item label="附件:" label-width="85px" :prop="isEditPi ? '' : 'image'">
                        <uploadimgFile ref="imageuploadimgFile" v-if="logVisible" :disabled="isView" :ispaste="!isView"
                        :noDel="isView" :reveal="true" :accepttyes="accepttyes" :isImage="true" :uploadInfo="form.imgUrls"
                        :keys="[1, 1]" @callback="getImg($event, 'imgUrls')" @beforeUpload="beforeUpload($event, 'image')"
                        :imgmaxsize="4" :limit="4" :multiple="true">
                        </uploadimgFile>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form-item>

        <div style="display: flex; flex-direction: row; justify-content: space-around; margin: 0 200px">
            <el-button @click="closeDia">取消</el-button>
            <el-button type="primary" @click="onSubmitend">保存</el-button>
        </div>
        </el-form>
    </div>
</template>

<script>
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import {
    GetPurchaseCostVerifyBuyNo,
    AddPurchaseCostVerify,
    EditPurchaseCostVerify
} from '@/api/inventory/purchaseCostVerify.js'
import { getAllWarehouse } from '@/api/inventory/warehouse'
import QRCode from "qrcode";
import middlevue from "@/store/middle.js"
import Decimal from 'decimal.js';
export default {
    name: '',
    components: { uploadimgFile },
    props: {
        editData: {
            type: Object,
            default: {}
        },
    },
    data() {
        return {
            loading: false,
            that: this,
            codeImg: null,
            // sumnum: 0,
            logVisible: true,
            isView: false,
            list: [],
            buyNoList: [],
            form: {
                id: null,
                warehousingNo: null,
                warehouse: null
            },
            warehouselist: [],
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
        }
    },
    watch: {
        editData: {
            async handler(value) {
                this.logVisible = false;
                //深度拷贝
                this.form = JSON.parse(JSON.stringify(value));
                // this.form = value;
                this.codeImg = value.id;

                // console.log("打印数据this.form", this.form);

                setTimeout(() => {
                    this.logVisible = true;
                    if(value.id){
                        this.allAddSum();
                    }
                    this.$nextTick(() => {
                        if(value.id&&document.getElementById("QRCode1")) {
                            const element = document.getElementById("QRCode1");
                            const options = {
                                width: 128,
                            };
                            const url = value.id;
                            QRCode.toCanvas(element, url, options);

                        }
                        this.$forceUpdate();
                    });
                }, 10);

                var res3 = await getAllWarehouse();
                this.warehouselist = res3.data;
            },
            immediate: true,
            deep: true
        },
    },
    mounted() {
        let me = this;
        middlevue.$on('Inventory_PurchaseCostVerify_UpdateProof', async (data) => {
            console.log("打印数据resws2222", data);
            let res = data;
            console.log("打印数据resws", res);
            if(me.editData && (res.Id == me.editData.id)) {
                me.searchList();
            }

        })


    },
    methods: {
        searchList() {
            let me = this;
            this.$emit('wsSearch', {id: me.editData.id});
        },
        onSubmit() {
            let me = this;
            this.$refs.form.validate(async (valid) => {
                if (valid) {
                    let res = await AddPurchaseCostVerify(me.form);
                    if (!res.success) {
                        return;
                    }

                    me.codeImg = res.data;
                    me.editData.id = res.data;
                    // me.$forceUpdate();
                    if (!res.data) {
                        me.$message({
                            message: "返回id为空，生成二维码失败",
                            type: "warning",
                        });
                        return;
                    }
                    const element = document.getElementById("QRCode1");
                    const options = {
                        width: 128,
                    };

                    const url = res.data;
                    QRCode.toCanvas(element, url, options);
                }
            });
        },
        onSubmitend() {
            this.$refs.form.validate(async (valid) => {
                if (valid) {
                    if (!this.codeImg) {
                        this.$message({
                            message: "请先点击生成二维码",
                            type: "warning",
                        });
                        return;
                    }
                    let params = {
                        ...this.form,
                        id: this.codeImg,
                        buyNo: this.form.buyNo1,
                        // imgUrls: this.form.image? this.form.image.map(item => item.url) : [],
                    }
                    let res = await EditPurchaseCostVerify(params);
                    if (!res.success) {
                        return;
                    }
                    this.$emit('successClose');
                    console.log("打印数据", res);
                }
            });
        },
        allAddSum() {
            let aa = this.form.haulage ? new Decimal(this.form.haulage) : new Decimal(0);
            let bb = this.form.deliveryFee? new Decimal(this.form.deliveryFee) : new Decimal(0);
            let cc = this.form.pickUpFee ? new Decimal(this.form.pickUpFee) : new Decimal(0);
            let dd = this.form.huoLaLa? new Decimal(this.form.huoLaLa) : new Decimal(0);
            let ee = this.form.loadingFee? new Decimal(this.form.loadingFee) : new Decimal(0);
            this.sumnum =  aa.add(bb).add(cc).add(dd).add(ee).toNumber();
        },
        async handleChange(val) {
            if(this.buyNoList.filter((item) => item.buyNo == val).length == 0) {
                return;
            }
            let res = this.buyNoList.filter((item) => item.buyNo == val)[0]
            this.$set(this.form,'warehouse', res.warehouse || null)
            this.$set(this.form,'warehousingNo', res.warehousingNo || null)
            this.$set(this.form,'buyNo1', res.buyNo)
        },
        async remoteMethod(val) {
            this.loading = true;
            let res = await GetPurchaseCostVerifyBuyNo({
                buyNo: val,
            });
            if (!res.success) {
                return;
            }
            this.loading = false;
            this.buyNoList = res.data;
            console.log("111111", res)
        },
        closeDia() {
            this.$emit('close');
        },
        beforeUpload() {

        },
        getImg(data, target) {
            if (data) {
                // const target = type == 'zizhiImage' ? 'zizhiImage' : type == 'zhuanliImage' ? 'zhuanliImage' : 'image';
                // this.form[target] = data.map(item => ({
                //     url: item.url,
                //     // name: item.fileName
                // }));
                // this.form[target] = data.map(item => (item.url));
                this.form[target] = data;

            }
        },
    }
}
</script>

<style>
</style>