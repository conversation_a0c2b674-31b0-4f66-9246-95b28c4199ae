<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
        <el-form-item label="付款日期:">
          <el-date-picker style="width:300px" :clearable="false" v-model="timeFilter.timerange" type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item label="平台:">
          <el-select v-model="Filter.platform" placeholder="请选择" class="el-select-content" @change="changePlatform"
            style="width:100px;">
            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="店铺:">
          <el-select v-model="Filter.shopCode" placeholder="请选择" class="el-select-content" style="width:100px;">
            <el-option label="所有" value></el-option>
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
          </el-select>
        </el-form-item>
        <el-form-item label="运营组:">
          <el-select v-model="Filter.groupId" placeholder="请选择" class="el-select-content" style="width:100px;">
            <el-option label="所有" value></el-option>
            <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item>
        <el-form-item label="采购组:">
          <el-select v-model="Filter.brandId" placeholder="请选择" style="width:100px;">
            <el-option label="所有" value="" />
            <el-option v-for="item in brandlist" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item>
        <el-form-item prop="parentIds" label="商品类别:">
          <el-cascader v-model="Filter.categoryId" :options="categorylist" :props="{ checkStrictly: false, value: 'id' }"
            clearable filterable style="width:220px;" />
        </el-form-item>
        <el-form-item label="快递公司:">
          <el-select v-model="Filter.expressCompany" placeholder="请选择" style="width:120px;">
            <el-option label="所有" value="" />
            <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单号:">
          <el-input v-model="Filter.orderNo" style="width: 100%" />
        </el-form-item>
        <el-form-item label="内部订单号:">
          <el-input v-model="Filter.orderNoInner" style="width: 100%" />
        </el-form-item>
        <el-form-item label="商品编码:">
          <el-input v-model="Filter.goodsCode" style="width: 100%" />
        </el-form-item>
        <el-form-item label="宝贝ID:">
          <el-input v-model="Filter.proCode" style="width: 100%" />
        </el-form-item>
        <el-form-item label="订单来源:">
          <el-select v-model="Filter.orderSource" placeholder="请选择" style="width: 100px;">
            <el-option label="所有" value />
            <el-option label="合并" value="0" />
            <el-option label="拆分" value="1" />
            <el-option label="补发" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="发货仓:">
          <el-select v-model="Filter.sendWarehouse" placeholder="请选择" style="width: 180px;">
            <el-option label="所有" value />
            <el-option v-for="item in sendWarehouseList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="宝贝数:">
          <el-select v-model="Filter.proCountType" placeholder="请选择" style="width: 110px;">
            <el-option label="所有" value />
            <el-option label="一单一品" value="1" />
            <el-option label="一单多品" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态:">
          <el-select v-model="Filter.status" placeholder="请选择" style="width: 100px;">
            <el-option label="所有" value />
            <el-option label="已发货" value="已发货" />
            <el-option label="被拆分" value="被拆分" />
            <el-option label="被合并" value="被合并" />
            <el-option label="取消" value="取消" />
            <el-option label="已客审待财审" value="已客审待财审" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :tableData='OrderList'
      :summaryarry="summaryarry" :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
      <el-table-column type="expand">
        <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <template slot='extentbtn'>
        <el-button-group>
          <el-button type="primary" @click="onExport">导出</el-button>
          <el-button type="primary" @click="downloadTemplate">下载导入模板</el-button>
          <el-button type="primary" @click="startImport">导入订单</el-button>
          <el-button type="primary" @click="startSendImport">导入发货订单</el-button>
          <!-- <el-button type="primary" @click="syncData">同步快递信息</el-button> -->
        </el-button-group>
      </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getOrderList" />
    </template>

    <!-- 付款时间订单导入 -->
    <el-dialog title="导入订单数据" :visible.sync="dialogVisible" width="30%">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :http-request="uploadFile" :file-list="fileList">
          <template #trigger>
            <el-button size="small" type="primary">选取订单数据文件</el-button>
          </template>
          <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 发货时间订单导入 -->
    <el-dialog title="导入订单数据" :visible.sync="dialogSendVisible" width="30%">
      <span>
        <el-upload ref="uploadSend" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :http-request="uploadSendFile" :on-change="uploadChange"
          :on-remove="uploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取订单数据文件</el-button>
          </template>
          <el-button style="margin-left: 10px;" size="small" type="success" @click="submitSendUpload">上传</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogSendVisible = false">关闭</el-button>
      </span>
    </el-dialog>

  </my-container>
</template>
<script>
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { pageOrders, importOrderAndGoodsData, syncNewOrderMonitAsync, exportOrder, importOrderAndGoodsDataTX } from "@/api/order/ordergoods";
import { getGroupKeyValue } from '@/api/operatemanage/base/product';
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import { getList as getcategorylist } from '@/api/operatemanage/base/category';
import { listToTree } from '@/utils';
import { getAllProBrand } from '@/api/inventory/warehouse';
import { formatExpressCompany, formatLink, formatYesornoBool, formatLinkProCode } from "@/utils/tools";
import { getExpressComanyAll } from "@/api/express/express";
import { rulePlatform, ruleSendWarehouse } from "@/utils/formruletools";

const tableCols = [
  { istrue: true, prop: 'orderNo', label: '线上订单号', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'orderNoInner', label: '内部订单号', width: '120', sortable: 'custom',type:'orderLogInfo',orderType:'orderNoInner'  },
  { istrue: true, prop: 'status', label: '状态', width: '60', sortable: 'custom' },
  { istrue: true, prop: 'orderTime', label: '订单日期', width: '150', sortable: 'custom' },
  { istrue: true, prop: 'payTime', label: '付款日期', width: '150', sortable: 'custom' },
  { istrue: true, prop: 'timeRang', label: '发货时长(小时)', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'goodFreightMoney', label: '分摊快递费(元)', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'shopCode', label: '店铺编号', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'shopMainAccount', label: '店铺主账号', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'orderSource', label: '订单来源', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'isMerge', label: '是否合并', width: '80', sortable: 'custom', formatter: (row) => formatYesornoBool(row.isMerge) },
  { istrue: true, prop: 'isSplit', label: '是否拆分', width: '80', sortable: 'custom', formatter: (row) => formatYesornoBool(row.isSplit) },
  { istrue: true, prop: 'isReissued', label: '是否补发', width: '80', sortable: 'custom', formatter: (row) => formatYesornoBool(row.isReissued) },
  { istrue: true, prop: 'proCountTypeName', label: '宝贝数', width: '70', sortable: 'custom' },
  { istrue: true, prop: 'province', label: '省份', width: '60', sortable: 'custom' },
  { istrue: true, prop: 'city', label: '城市', width: '60', sortable: 'custom' },
  { istrue: true, prop: 'label', label: '标签', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'platformDesc', label: '平台站点', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'classify', label: '运营组', width: '70', sortable: 'custom' },
  { istrue: true, prop: 'brandName', label: '采购组', width: '70', sortable: 'custom' },
  { istrue: true, prop: 'expressCompany', label: '快递公司', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'sendWarehouse', label: '发货仓', width: '70', sortable: 'custom', formatter: (row) => row.sendWarehouseName },
  { istrue: true, prop: 'expressNo', label: '快递单号', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'childrenOrderNo', label: '子订单编号', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'goodsCode', label: '商品编码', width: '100', sortable: 'custom' },
  //{istrue:true,display:true,prop:'proCode',label:'商品ID', width:'80',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
  {
    istrue: true, display: true, prop: 'proCode', label: '商品ID', width: '80', sortable: 'custom', type: 'html', formatter: (row) => {
      var proBaseUrl = "";
      switch (row.platformDesc) {
        case "淘系":
          proBaseUrl = "https://detail.tmall.com/item.htm?id=" + row.proCode;
          break;
        case "拼多多":
          proBaseUrl = "https://mobile.yangkeduo.com/goods2.html?goods_id=" + row.proCode;
          break;
      }
      if (row.proCode && proBaseUrl)
        return formatLink(row.proCode, proBaseUrl);
      return row.proCode;
    }
  },
  { istrue: true, prop: 'qty', label: '数量', width: '60', sortable: 'custom' },
  { istrue: true, prop: 'goodsAmount', label: '商品金额', width: '80' },
  { istrue: true, prop: 'goodsCost', label: '成本价', width: '70', sortable: 'custom' },
  { istrue: true, prop: 'sendTime', label: '发货日期', width: '150', sortable: 'custom' },
  { istrue: true, prop: 'deductionAmount', label: '抵扣金额', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'freightMoney', label: '运费', width: '60', sortable: 'custom' },
  { istrue: true, prop: 'distributionSettlementAmount', label: '分销结算金额', width: '110' },
  { istrue: true, prop: 'currency', label: '币种', width: '60', sortable: 'custom' },
  { istrue: true, prop: 'shopStatus', label: '店铺状态', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'errorType', label: '异常类型', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'actualWeight', label: '实称重量', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'orderGoodsWeight', label: '订单商品重量', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'calcWeight', label: '计算重量', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'confirmReceivingTime', label: '确认收货时间', width: '150', sortable: 'custom' },
  { istrue: true, prop: 'refundStatus', label: '退款状态', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'refundStatus', label: '退款类型', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'returnQty', label: '实退数量', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'returnAmount', label: '订单退款金额', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'returnAmount', label: '退款时间', width: '150', sortable: 'custom' },
  { istrue: true, prop: 'gjExpressNo', label: '国际物流单号', width: '120', sortable: 'custom' },
];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
  data() {
    return {
      that: this,
      timeFilter:{
        timerange: [formatTime(dayjs().startOf("month"), "YYYY-MM-DD HH:mm:ss"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")],
      },
      Filter: {
        //timerange: [formatTime(dayjs().startOf("month"), "YYYY-MM-DD HH:mm:ss"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")],
        startCreatedTime: null,
        endCreatedTime: null,
        platform: null,
        shopCode: "",
        groupId: null,
        productCategoryId: null,
        goodsCode: null,
        proCode: null,
        brandId: null,
        expressCompany: null,
        orderNo: null,
        orderNoInner: null,
        orderSource: null,
        sendWarehouse: null,
        proCountType: null,
        status: null,
        groupByDay: false,
        groupByWeek: false,
        groupByMonth: false,
        groupByPlatform: false,
        groupByShop: false,
        groupByCategory: false,
        groupByGroup: false,
        groupByBrand: false,
        groupByProCode: false,
        groupByProBianMa: false,
        groupByExpressComany: false,
        groupBySendWarehouse: false,
        isGroupBy: false,
        rowStartPayTime: null,
        rowEndPayTime: null,
        statusName: null, //状态名称  拆分，补发，退货
      },
      OrderList: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: {},
      pager: { OrderBy: "orderTime", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      dialogVisible: false,
      dialogSendVisible: false,
      userNameReadonly: true,
      fileList: [],
      fileSendList: [],
      importFilte: { companyid: null, warehouse: null },
      batchNumber: "",
      tableHandles: [],
      platformList: [],
      shopList: [],
      groupList: [],
      categorylist: [],
      brandlist: [],
      expresscompanylist: [],
      sendWarehouseList: []
    };
  },
  async mounted() {
    //await this.onSearch();
    await this.setGroupSelect();
    await this.setBandSelect();
    await this.getExpressComanyList();
    await this.setPlatform();
    await this.setWarehouse();
    await this.setFilter();
  },
  methods: {
    async setFilter() {
      if (this.$route.query.startPayTime) {
        // 
        this.timeFilter.timerange = [this.$route.query.startPayTime || formatTime(dayjs().startOf("month"), "YYYY-MM-DD HH:mm:ss"),
        this.$route.query.endPayTime || formatTime(new Date(), "YYYY-MM-DD 23:59:59")];
        this.Filter.startCreatedTime = this.$route.query.startPayTime || null;
        this.Filter.endPayTime = this.$route.query.endCreatedTime || null;
        this.Filter.platform = this.$route.query.platform === "" ? null : parseInt(this.$route.query.platform);
        this.Filter.shopCode = this.$route.query.shopCode || "";
        this.Filter.groupId = this.$route.query.groupId || null;
        this.Filter.productCategoryId = this.$route.query.productCategoryId || null;
        this.Filter.goodsCode = this.$route.query.proBianMa || null;
        this.Filter.proCode = this.$route.query.proCode || null;
        this.Filter.brandId = this.$route.query.brandId === "" ? null : parseInt(this.$route.query.brandId);
        this.Filter.expressCompany = this.$route.query.expressCompany || null;
        this.Filter.orderNo = this.$route.query.orderNo || null;
        this.Filter.orderNoInner = this.$route.query.orderNoInner || null;
        this.Filter.orderSource = this.$route.query.orderSource || null;
        this.Filter.sendWarehouse = this.$route.query.sendWarehouse === "" ? null : parseInt(this.$route.query.sendWarehouse);
        this.Filter.proCountType = this.$route.query.proCountType;//===""?null:parseInt(this.$route.query.proCountType);
        this.Filter.status = this.$route.query.status || null;
        //分组条件
        this.Filter.groupByDay = this.$route.query.groupByDay == 'true' ? true : false;
        this.Filter.groupByWeek = this.$route.query.groupByWeek == 'true' ? true : false;
        this.Filter.groupByMonth = this.$route.query.groupByMonth == 'true' ? true : false;
        this.Filter.groupByPlatform = this.$route.query.groupByPlatform == 'true' ? true : false;
        this.Filter.groupByShop = this.$route.query.groupByShop == 'true' ? true : false;
        this.Filter.groupByCategory = this.$route.query.groupByCategory == 'true' ? true : false;
        this.Filter.groupByGroup = this.$route.query.groupByGroup == 'true' ? true : false;
        this.Filter.groupByBrand = this.$route.query.groupByBrand == 'true' ? true : false;
        this.Filter.groupByProCode = this.$route.query.groupByProCode == 'true' ? true : false;
        this.Filter.groupByProBianMa = this.$route.query.groupByProBianMa == 'true' ? true : false;
        this.Filter.groupByExpressComany = this.$route.query.groupByExpressComany == 'true' ? true : false;
        this.Filter.groupBySendWarehouse = this.$route.query.groupBySendWarehouse == 'true' ? true : false;
        this.Filter.isGroupBy = this.$route.query.isGroupBy == 'true' ? true : false;
        //行值条件
        this.Filter.groupTimeValue = this.$route.query.groupTimeValue;
        this.Filter.groupPlatformValue = this.$route.query.groupPlatformValue;
        this.Filter.groupShopCodeValue = this.$route.query.groupShopCodeValue;
        this.Filter.groupCategoryIdValue = this.$route.query.groupCategoryIdValue;
        this.Filter.groupGroupIdValue = this.$route.query.groupGroupIdValue;
        this.Filter.groupBrandIdValue = this.$route.query.groupBrandIdValue;
        this.Filter.groupProCodeValue = this.$route.query.groupProCodeValue;
        this.Filter.groupProBianMaValue = this.$route.query.groupProBianMaValue;
        this.Filter.groupExpressComanyIdValue = this.$route.query.groupExpressComanyIdValue;
        this.Filter.groupBySendWarehouseValue = this.$route.query.groupBySendWarehouseValue;
        this.Filter.rowStartPayTime = this.$route.query.rowStartPayTime;
        this.Filter.rowEndPayTime = this.$route.query.rowEndPayTime;
        this.Filter.statusName = this.$route.query.typeName;

        //合并条件
        if (this.Filter.groupByPlatform) {
          this.Filter.platform = this.Filter.groupPlatformValue;
        }
        if (this.Filter.groupByShop) {
          this.Filter.shopCode = this.Filter.groupShopCodeValue;
        }
        if (this.Filter.groupByGroup) {
          this.Filter.groupId = this.Filter.groupGroupIdValue;
        }
        if (this.Filter.groupByCategory) {
          this.Filter.productCategoryId = this.Filter.groupCategoryIdValue;
        }
        if (this.Filter.groupByProBianMa) {
          this.Filter.goodsCode = this.Filter.groupProBianMaValue;
        }
        if (this.Filter.groupByProCode) {
          this.Filter.proCode = this.Filter.groupProCodeValue;
        }
        if (this.Filter.groupByBrand) {
          this.Filter.brandId = this.Filter.groupBrandIdValue;
        }
        if (this.Filter.groupByExpressComany) {
          this.Filter.expressCompany = this.Filter.groupExpressComanyIdValue;
        }
        if (this.Filter.groupBySendWarehouse) {
          this.Filter.sendWarehouse = this.Filter.groupBySendWarehouseValue;
        }

        if (this.Filter.statusName == "split") {
          this.Filter.orderSource = "1";//拆分
        }
        else if (this.Filter.statusName == "reissued") {
          this.Filter.orderSource = "2";//补发
        }
        await this.changePlatform(this.Filter.platform);
        await this.getOrderList();
      }
    },
    async setPlatform() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;
    },
    async setWarehouse() {
      var whrule = await ruleSendWarehouse();
      this.sendWarehouseList = whrule.options;
    },
    formatCreatedTime(row, column, time) {
      return formatTime(time, "YYYY-MM-DD HH:mm");
    },
    startImport() {
      this.dialogVisible = true;
    },
    cancelImport() {
      this.dialogVisible = false;
    },
    beforeRemove() {
      return false;
    },
    //上传成功
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    uploadFile(item) {
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      const res = importOrderAndGoodsData(form);
      this.clearFiles();
      this.$message({
        message: '上传成功,正在导入中...',
        type: "success",
      });
    },
    clearFiles() {
      this.$refs['upload'].clearFiles();
    },

    startSendImport() {
      this.dialogSendVisible = true;
    },
    cancelSendImport() {
      this.dialogSendVisible = false;
    },
    submitSendUpload() {
      if (!this.fileSendList || this.fileSendList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.$refs.uploadSend.submit();
    },
    uploadSendFile(item) {
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      const res = importOrderAndGoodsDataTX(form);
      this.clearSendFiles();
      this.$message({
        message: '上传成功,正在导入中...',
        type: "success",
      });
      this.dialogSendVisible = false;
      this.fileSendList = [];
    },
    clearSendFiles() {
      this.$refs['uploadSend'].clearFiles();
    },
    uploadChange(file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for (var i = 0; i < fileList.length; i++) {
          if (fileList[i].status == "success")
            list.push(fileList[i]);
          else
            list.push(fileList[i].raw);
        }
        this.fileSendList = list;
      }
    },
    uploadRemove(file, fileList) {
      this.uploadChange(file, fileList);
    },


    // 查询
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getOrderList();
    },
    getCondition() {
      const para = { ...this.Filter };
      if (this.timeFilter.timerange) {
        para.startCreatedTime = this.timeFilter.timerange[0];
        para.endCreatedTime = this.timeFilter.timerange[1];
      }
      if (!(para.startCreatedTime && para.endCreatedTime)) {
        this.$message({ message: "请先选择日期！", type: "warning" });
        return false;
      }
      if (this.Filter.categoryId) {
        para.productCategoryId = this.Filter.categoryId[this.Filter.categoryId.length - 1];
      }
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      return params;
    },
    async getOrderList() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await pageOrders(params);
      this.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.total = res.data.total;
      const data = res.data.list;
      data.forEach((d) => {
        d._loading = false;
      });
      this.OrderList = data;
      this.summaryarry = res.data.summary || {};
    },
    async onExport() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      var d1 = dayjs(params.startCreatedTime);
      var d2 = dayjs(params.endCreatedTime);
      if (d2.diff(d1, "day") > 5) {
        this.$message({ message: "最多只能导出5天的数据", type: "warning" });
        return false;
      }
      var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
      var res = await exportOrder(params);
      loadingInstance.close();
      if (!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '订单_' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    // 选择
    onSelsChange(sels) {
      this.sels = sels;
    },
    async changePlatform(val) {
      const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 1000 });
      this.shopList = res1.data.list
      if (val) this.getcategorylist(val)
    },
    async setGroupSelect() {
      const res = await getGroupKeyValue({});
      this.groupList = res.data;
    },
    async getcategorylist(platform) {
      const res = await getcategorylist({ platform: platform })
      if (!res?.code) {
        return
      }
      const list = [];
      res.data.forEach(f => {
        f.label = f.categoryName;
        list.push(f)
      })
      this.categorylist = listToTree(_.cloneDeep(list), {
        id: '',
        parentId: '',
        label: '所有'
      })
    },
    async setBandSelect() {
      var res = await getAllProBrand();
      if (!res?.success) return;
      this.brandlist = res.data;//.map(item => {
      //return { value: item.id, label: item.brandName };
      //});
    },
    downloadTemplate() {
      window.open("../static/excel/订单数据导入模板.xlsx", "_self");
    },
    async syncData() {
      const res = await syncNewOrderMonitAsync();
      if (!res?.success) return;
      this.$message({ message: "正在同步，请稍后查看结果", type: "success" });
      this.onSearch();
    },
    sortchange(column) {
      if (column.prop == "shopMainAccount") {
        column.prop = "shopCode";
      } else if (column.prop == "classify") {
        column.prop = "groupId";
      }

      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }


      if (this.pager) {
        this.pager.OrderBy = this.pager.OrderBy == "brandName" ? "brandId" : this.pager.OrderBy;
        this.pager.OrderBy = this.pager.OrderBy == "platformDesc" ? "platform" : this.pager.OrderBy;
        this.pager.OrderBy = this.pager.OrderBy == "proCountTypeName" ? "proCountType" : this.pager.OrderBy;
      }
      this.onSearch();
    },
    async getExpressComanyList() {
      const res = await getExpressComanyAll({});
      if (!res?.success) {
        return;
      }
      const data = res.data;
      this.expresscompanylist = data;
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
