<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.labels" placeholder="标签" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" size="mini" @click="getList('search')">搜索</el-button>
        <el-button type="primary" size="mini" :disabled="isExport" @click="exportData">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'unpackingStat202408041756'"
      ref="table"
      v-loading="loading"
      :that="that"
      :is-index="true"
      :hasexpand="true"
      :tablefixed="true"
      :border="true"
      :table-data="tableData"
      :table-cols="tableCols"
      :is-selection="false"
      :is-select-column="false"
      :is-index-fixed="false"
      style="width: 100%;  margin: 0"
      :height="'100%'"
      @sortchange="sortchange"
    />
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>
<script>
import MyContainer from '@/components/my-container'
import vxetablebase from '@/components/VxeTable/yh_vxetable.vue'
import { download } from '@/utils/download'
import { unpackingGetColumns, unpackingPageGetData, unpackingExportData } from '@/api/vo/ReturnOrderScan'
export default {
  name: 'ScanCodePage',
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      rules: {
      },
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'TotalCost',
        isAsc: false,
        goodsCode: null, // 商品编码
        goodsName: null, // 商品名称
        labels: null // 是否组合商品
      },
      tableCols: [],
      tableData: [],
      total: 0,
      loading: false,
      isExport: false,
      allotResultStatus: [],
      ruleForm: {}
    }
  },
  mounted() {
    this.getCol()
    this.getList()
  },
  methods: {
    async getCol() {
      const { data, success } = await unpackingGetColumns()
      if (success) {
        this.tableCols = data
      }
    },
    // 导出数据,这里前端可以封装一个方法
    async exportData() {
      this.isExport = true
      await unpackingExportData(this.ListInfo).then(download).finally(() => {
        this.isExport = false
      })
    },
    async getList(type) {
      if (type === 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      // 使用时将下面的方法替换成自己的接口
      try {
        const { data: { list, total, summary }, success } = await unpackingPageGetData(this.ListInfo)
        if (success) {
          list.forEach(item => {
            item.canPackWmsesList = item.canPackWmsesList ? item.canPackWmsesList.sort((a, b) => b.subGoodsUsableQty - a.subGoodsUsableQty) : []
          })
          this.tableData = list
          this.total = total
          this.summaryarry = summary
        } else {
          this.$message.error('获取列表失败')
        }
      } catch (error) {
        this.$message.error('获取列表失败')
      } finally {
        this.loading = false
      }
    },
    // 每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1
      this.ListInfo.pageSize = val
      this.getList()
    },
    // 当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf('descending') == -1
        this.getList()
      }
    }
  }
}
</script>
<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
  flex-wrap: wrap;

  .publicCss {
    width: 200px;
    margin: 0 10px 5px 0;
  }
}
</style>
