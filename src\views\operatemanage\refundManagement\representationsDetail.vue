<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>

                <el-button style="padding: 0; margin: 0; margin-right: 10px;">
                    <!-- <el-input v-model="filter.masterOrderNumber" v-model.trim="filter.masterOrderNumber" placeholder="主订单编号(多个单号，隔开)"></el-input> -->
                    <inputYunhan :width="'220px'" ref="" :inputt.sync="filter.masterOrderNumber" :clearabletext="true" :valuedOpen="true"
                        v-model.trim="filter.masterOrderNumber" placeholder="主订单编号/若输入多条请摁回车" :clearable="true" @callback="callback1"
                        title="主订单编号" :maxRows="100" v-dialogDrag>
                    </inputYunhan>
                </el-button>
                <el-button style="padding: 0; margin: 0; margin-right: 10px;">
                    <!-- <el-input v-model="filter.refundOrderNumber" v-model.trim="filter.refundOrderNumber" placeholder="退款单编号(多个单号，隔开)"></el-input> -->
                    <inputYunhan ref="" :inputt.sync="filter.refundOrderNumber" v-model.trim="filter.refundOrderNumber" :width="'220px'"
                        placeholder="关联退款单号/处罚单号/若输入多条请摁回车" :clearable="true" @callback="callback2" :clearabletext="true" :valuedOpen="true"
                        title="关联退款单号/处罚单号" :maxRows="100" v-dialogDrag>
                    </inputYunhan>
                </el-button>
                <el-button style="padding: 0; margin: 0; margin-right: 10px;">
                    <!-- <el-input v-model="filter.representOrderNumber" v-model.trim="filter.representOrderNumber" placeholder="申诉单编号(多个单号，隔开)"></el-input> -->
                    <inputYunhan :width="'220px'" ref="" :inputt.sync="filter.representOrderNumber" :clearabletext="true" :valuedOpen="true"
                        v-model.trim="filter.representOrderNumber" placeholder="申诉单编号/若输入多条请摁回车" :clearable="true" @callback="callback3"
                        title="申诉单编号" :maxRows="100" v-dialogDrag>
                    </inputYunhan>
                </el-button>
                <!-- <el-button style="padding: 0; margin: 0; margin-right: 10px;">
                    <el-select v-model="filter.refundType" placeholder="退款类型" multiple clearable collapse-tags filterable>
                        <el-option v-for="item in refundTypeList" :key="item" :value="item" :label="item"></el-option>
                    </el-select>
                </el-button> -->
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-date-picker style="width: 320px" v-model="orderPayTimeRange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" 
                    range-separator="至" start-placeholder="付款时间" end-placeholder="结束时间" :picker-options="orderPayTimePickerOptions" >
                    </el-date-picker>
                </el-button>
                <el-button style="padding: 0; margin: 0; margin-right: 10px;">
                    <!-- <el-input v-model="filter.productID" placeholder="商品ID(多个ID，隔开)"></el-input> -->
                    <inputYunhan :width="'220px'" ref="" :inputt.sync="filter.productID" :clearabletext="true" :valuedOpen="true"
                        v-model.trim="filter.productID" placeholder="商品ID/若输入多条请摁回车" :clearable="true" @callback="callback4"
                        title="商品ID" :maxRows="100" v-dialogDrag>
                    </inputYunhan>
                </el-button>
                <!-- <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-date-picker style="width: 320px" v-model="initiationTimeRange" type="daterange" 
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="发起时间" 
                        end-placeholder="结束时间" :picker-options="initiationTimePickerOptions" >
                    </el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-date-picker style="width: 320px" v-model="payoutTimeRange" type="daterange" 
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="打款时间" 
                        end-placeholder="结束时间" :picker-options="payoutTimePickerOptions" >
                    </el-date-picker>
                </el-button> -->
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-select v-model="filter.shop" placeholder="供应商名称（店铺）" multiple clearable collapse-tags filterable style="width:200px;">
                        <el-option v-for="item in shopList" :key="item" :value="item" :label="item"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-select v-model="filter.leader" placeholder="组长" multiple clearable collapse-tags filterable style="width:200px;">
                        <el-option v-for="item in leaderList" :key="item" :value="item" :label="item"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-select v-model="filter.commissioner" placeholder="专员" multiple clearable collapse-tags filterable style="width:200px;">
                        <el-option v-for="item in commissionerList" :key="item" :value="item" :label="item"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-select v-model="filter.assistant" placeholder="助理" multiple clearable collapse-tags filterable style="width:200px;">
                        <el-option v-for="item in assistantList" :key="item" :value="item" :label="item"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-date-picker style="width: 320px" v-model="representTimeRange" type="daterange" 
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="申述成功时间" 
                        end-placeholder="结束时间" :picker-options="representTimePickerOptions" >
                    </el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-date-picker style="width: 320px" v-model="finalTimeRange" type="daterange" 
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="结算时间" 
                        end-placeholder="结束时间" :picker-options="finalTimePickerOptions" >
                    </el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-select v-model="filter.representationPerson" placeholder="申诉人" multiple clearable collapse-tags filterable>
                        <el-option label="空" value="空"></el-option>
                        <el-option v-for="item in representationPersonList" :key="item" :value="item" :label="item"></el-option>
                    </el-select>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onExport">导出</el-button>
                <el-button type="primary" @click="onClaim" v-if="checkPermission('api:operatemanage:operate:setrepresentationperson')">设置申诉人</el-button>
                <el-button type="primary" @click="ImportDistOrderData">商家申述明细数据导入</el-button>
            </el-button-group>
        </template>
        
        <vxetablebase :id="'representationDetail202408111325'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='allRefundDataList' :tableCols='tableCols' :isSelection='false' :isSelectColumn="false"
            :summaryarry="summaryarry" :showsummary='true' style="width: 100%;margin: 0" v-loading="listLoading" @select="selectchange" 
            :height="'100%'" >
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" 
            @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="设置申述人" :visible.sync="setVisible" width="300px" height="70%" @closeDialog="closeSetDialog"
            v-dialogDrag @close="setCancle">
            <div style="text-align: center; margin-top: 20px;">
                <el-select v-model="representationPersonID" filterable placeholder="申述人" :filter-method="remoteMethod">
                    <el-option
                        v-for="(item, index) in optionslist"
                        :key="item.value + index"
                        :label="item.label"
                        :value="item.value">
                        <span>{{item.label}}</span>
                        <span  style=" color: #8492a6; ">({{item.extData.position}},{{item.extData.empStatusText}}{{item.extData.jstUserName ? ","+item.extData.jstUserName:""}})</span>
                        <span style=" color: #8492a6; "> {{item.extData.deptName}}</span>
                    </el-option>
                </el-select>
            </div>

            <div style="text-align: center; margin-top: 20px;">
                <el-button type="primary" @click="setCancle">取消</el-button>
                <el-button type="primary" @click="setSubmit">确认</el-button>
            </div>
        </el-dialog>

        <el-dialog title="商家申述明细数据导入" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
            <el-row>
                <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                    <el-upload ref="upload" :auto-upload="false" :multiple="false" action
                        accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                        <template #trigger>
                            <el-button size="small" type="primary">选取文件</el-button>
                        </template>
                        <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                            @click="submitupload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                    </el-upload>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import cesTable from "@/components/Table/table.vue";
import { getRefundType, getRepresentationDetail, exportRepresentationDetail, getShopNameInProductEntity, getLeaderNameInProductEntity, 
    getCommissionerNameInProductEntity, getAssistantNameInProductEntity, getRepresentationPerson, setRepresentationPerson, importTaoFactoryShopRepresentData } 
from "@/api/operatemanage/refundData";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser';

const tableCols = [
    { istrue: true, width: '60', align: 'center', type: "checkbox" },
    { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '申述单号', prop: 'representOrderNumber' },
    { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '主订单编号', prop: 'masterOrderNumber' },
    { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '关联退款单号/处罚单号', prop: 'refundOrderNumber' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '退款类型', prop: 'refundType' },
    { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '付款时间', prop: 'orderPayTime' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '商品ID', prop: 'productID' },
    { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '宝贝标题', prop: 'proTitle' },
    { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '供应商名称（店铺）', prop: 'shop' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '组长', prop: 'leader' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '专员', prop: 'commissioner' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '助理', prop: 'assistant' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '申诉人', prop: 'representationPerson' },
    
    { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '申述成功时间', prop: 'representTime' },
    { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '结算金额（元）', prop: 'finalAmount' },
    { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '结算时间', prop: 'finalTime' },
    
    // { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '申诉单号', prop: 'representOrderNumber' },
    // { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '发起时间', prop: 'initiationTime' },
    // { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '申诉原因', prop: 'representReason' },
    // { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '申诉状态', prop: 'representStatus' },
    // { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '申诉金额', prop: 'representAmount' },
    
    // { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '打款金额', prop: 'payoutAmount' },
    // { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '打款差额', prop: 'payoutDifference' },
    // { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '打款时间', prop: 'payoutTime' },
    // { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '申诉打款状态', prop: 'representPayoutStatus' },
    
    // { sortable: 'custom', istrue: true, width: '140', align: 'center', label: '小二答复', prop: 'xiaoErReplied' },
    // { sortable: 'custom', istrue: true, width: '140', align: 'center', label: '申诉描述', prop: 'representationDescription' },
];

export default {
    components: { MyContainer, cesTable, vxetablebase, inputYunhan },
    data() {
        return {
            pageLoading: false,
            listLoading: false,
            setVisible: false,
            initiationTimeRange: [],//发起时间
            payoutTimeRange: [],//打款时间
            orderPayTimeRange: [],//付款时间
            representTimeRange: [],//申诉成功时间
            finalTimeRange: [],//结算时间
            filter: {
                currentPage: 1,
                pageSize: 50,
                orderBy: "representTime",
                isAsc: false,
                //过滤条件
                masterOrderNumber: null,//主订单编号
                refundOrderNumber: null,//退款单编号
                representOrderNumber: null,//申诉单编号
                refundType: [],//退款类型
                productID: null,//商品ID
                // beginInitiationTime: null,//发起时间开始
                // endInitiationTime: null,//发起时间结束
                // beginPayoutTime: null,//打款时间开始
                // endPayoutTime: null,//打款时间结束
                beginOrderPayTime: null,//付款时间开始
                endOrderPayTime: null,//付款时间结束
                beginRepresentTime: null,//申诉成功时间开始
                endRepresentTime: null,//申诉成功时间结束
                beginFinalTime: null,//结算时间开始
                endFinalTime: null,//结算时间结束
                shop: [],//店铺
                leader: [],//组长
                commissioner: [],//专员
                assistant: [],//助理
                finalTimePicker: null,//付款时间
                representationPerson: [],//申诉人
            },

            refundTypeList: [],//退款类型
            shopList: [],//店铺
            leaderList: [],//组长
            commissionerList: [],//专员
            assistantList: [],//助理
            representationPersonList: [],//申诉人
            representationPersonID: null,//设置申述人
            that: this,

            allRefundDataList: [],//列表数据
            tableCols: tableCols,//输出列表
            total: 0,
            summaryarry: { count_sum: 10},
            pager: { OrderBy: "id", IsAsc: false },
            sels: [], // 列表选中列
            selectList: [],
            // selids: [],
            
            orderPayTimePickerOptions: {
                shortcuts: [{
                text: '前一天',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
                        end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
                        picker.$emit('pick', [start, end]);
                        // window.setshowprogress(false);
                    }
                }, {
                    text: '近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        end.setTime(end.getTime());
                        picker.$emit('pick', [start, end]);
                        // window.setshowprogress(false);
                    }
                }, {
                    text: '近一个月',
                    onClick(picker) {
                        const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
                        const date2 = new Date(); date2.setDate(date2.getDate());
                        picker.$emit('pick', [date1, date2]);
                        // window.setshowprogress(false);
                    }
                }]
            },
            representTimePickerOptions: {
                shortcuts: [{
                text: '前一天',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
                        end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
                        picker.$emit('pick', [start, end]);
                        // window.setshowprogress(false);
                    }
                }, {
                    text: '近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        end.setTime(end.getTime());
                        picker.$emit('pick', [start, end]);
                        // window.setshowprogress(false);
                    }
                }, {
                    text: '近一个月',
                    onClick(picker) {
                        const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
                        const date2 = new Date(); date2.setDate(date2.getDate());
                        picker.$emit('pick', [date1, date2]);
                        // window.setshowprogress(false);
                    }
                }]
            },
            finalTimePickerOptions: {
                shortcuts: [{
                text: '前一天',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
                        end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
                        picker.$emit('pick', [start, end]);
                        // window.setshowprogress(false);
                    }
                }, {
                    text: '近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        end.setTime(end.getTime());
                        picker.$emit('pick', [start, end]);
                        // window.setshowprogress(false);
                    }
                }, {
                    text: '近一个月',
                    onClick(picker) {
                        const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
                        const date2 = new Date(); date2.setDate(date2.getDate());
                        picker.$emit('pick', [date1, date2]);
                        // window.setshowprogress(false);
                    }
                }]
            },
            dialogVisible: false,
            uploadLoading: false,
            fileList: [],
            optionslist: [],//组织架构单选
            addForm: {
              userId: '',
            },
        }
    },
    async mounted() {
        let end = new Date();
        let start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 10);
        end.setTime(end.getTime());

        // this.initiationTimeRange = [start, end];
        // this.filter.beginInitiationTime = start;
        // this.filter.endInitiationTime = end;
        
        // this.payoutTimeRange = [start, end];
        // this.filter.beginPayoutTime = start;
        // this.filter.endPayoutTime = end;

        // this.filter.representTimeRange = [start, end];
        // this.filter.beginRepresentTime = start;
        // this.filter.endRepresentTime = end;

        // this.filter.finalTimeRange = [start, end];
        // this.filter.beginFinalTime = start;
        // this.filter.endFinalTime = end;

        this.init();
        this.onSearch();
    },
    methods: {
        async remoteMethod(query) {
            if (query !== '') {
                let rlt = await QueryAllDDUserTop100({ keywords: query });
                if (rlt && rlt.success) {
                this.optionslist = rlt.data?.map(item => {
                    return { label: item.userName, value: item.userId, extData: item }
                });
                }
            }
            else {
                this.optionslist = [...this.orgOptions];
            }
            this.addForm.userId = '';
        },
        async init() {
            //退款类型
            var { data } = await getRefundType();
            this.refundTypeList = data;
            //店铺
            var { data } = await getShopNameInProductEntity();
            this.shopList = data;
            //组长
            var { data } = await getLeaderNameInProductEntity();
            this.leaderList = data;
            //专员
            var { data } = await getCommissionerNameInProductEntity();
            this.commissionerList = data;
            //助理
            var { data } = await getAssistantNameInProductEntity();
            this.assistantList = data;
            //申诉人
            var { data } = await getRepresentationPerson();
            this.representationPersonList = data;
        },
        //排序
        sortchange(column) {
            if (column.order) {
                this.filter.orderBy = column.prop;
                this.filter.isAsc = column.order.indexOf("descending") == -1 ? true : false;
                this.getList();
            }
        },
        //复选框数据
        selectchange:function(rows,row) {
            this.selectList = [];
            rows.forEach(f => {
                this.selectList.push(f);
            })
        },
        // selectchange: function (rows, row) {
        //     this.selids = [];
        //     rows.forEach(f => {
        //         this.selids.push(f.id);
        //     })
        // },
        //发起时间
        changeInitiationTime(e) {
            const formatDate = date => {
                if (!date) return null;
                const localDate = new Date(date);
                // 获取时间偏差（分钟）
                const offset = localDate.getTimezoneOffset();
                // 转换为 UTC 时间
                return new Date(localDate.getTime() + offset * 60000).toISOString().split('T')[0];
            };
            this.filter.beginInitiationTime = e ? formatDate(e[0]) : null;
            this.filter.endInitiationTime = e ? formatDate(e[1]) : null;
        },
        //打款时间
        changePayoutTime(e) {
            const formatDate = date => {
                if (!date) return null;
                const localDate = new Date(date);
                // 获取时间偏差（分钟）
                const offset = localDate.getTimezoneOffset();
                // 转换为 UTC 时间
                return new Date(localDate.getTime() + offset * 60000).toISOString().split('T')[0];
            };
            this.filter.beginPayoutTime = e ? formatDate(e[0]) : null;
            this.filter.endPayoutTime = e ? formatDate(e[1]) : null;
        },
        //每页数量改变
        Sizechange(val) {
            this.listLoading = true;
            this.filter.currentPage = 1;
            this.filter.pageSize = val;
            this.getList();
            this.listLoading = false;
        },
        //当前页改变
        Pagechange(val) {
            this.pageLoading = true;
            this.filter.currentPage = val;
            this.getList();
            this.pageLoading = false;
        },
        //查询
        onSearch() {
            //点击查询时才将页数重置为1
            this.filter.currentPage = 1;
            this.$refs.pager.setPage(1);
            this.getList();
        },
        async getList() {
            // this.filter.beginInitiationTime = this.initiationTimeRange ? this.initiationTimeRange[0] : null;
            // this.filter.endInitiationTime = this.initiationTimeRange ? this.initiationTimeRange[1] : null;
            // this.filter.beginPayoutTime = this.payoutTimeRange ? this.payoutTimeRange[0] : null
            // this.filter.endPayoutTime = this.payoutTimeRange ? this.payoutTimeRange[1] : null;
            this.filter.beginOrderPayTime = this.orderPayTimeRange ? this.orderPayTimeRange[0] : null;
            this.filter.endOrderPayTime = this.orderPayTimeRange ? this.orderPayTimeRange[1] : null;
            this.filter.beginRepresentTime = this.representTimeRange ? this.representTimeRange[0] : null;
            this.filter.endRepresentTime = this.representTimeRange ? this.representTimeRange[1] : null;
            this.filter.beginFinalTime = this.finalTimeRange ? this.finalTimeRange[0] : null;
            this.filter.endFinalTime = this.finalTimeRange ? this.finalTimeRange[1] : null;
            this.listLoading = true;
            const { data, success } = await getRepresentationDetail(this.filter);
            this.listLoading = false;
            if (success) {
                this.allRefundDataList = data.list;
                this.total = data.total;
                this.summaryarry = data.summary;
            } else {
                this.$message.error('获取申诉明细数据失败！');
            }
        },
        //导出
        async onExport() {
            // if (this.initiationTimeRange) {
            //     this.filter.beginInitiationTime = this.initiationTimeRange[0];
            //     this.filter.endInitiationTime = this.initiationTimeRange[1];
            // }
            // if (this.payoutTimeRange) {
            //     this.filter.beginPayoutTime = this.payoutTimeRange[0];
            //     this.filter.endPayoutTime = this.payoutTimeRange[1];
            // }
            if (this.orderPayTimeRange) {
                this.filter.beginOrderPayTime = this.orderPayTimeRange ? this.orderPayTimeRange[0] : null;
                this.filter.endOrderPayTime = this.orderPayTimeRange ? this.orderPayTimeRange[1] : null;
            }
            if (this.representTimeRange) {
                this.filter.beginRepresentTime = this.representTimeRange ? this.representTimeRange[0] : null;
                this.filter.endRepresentTime = this.representTimeRange ? this.representTimeRange[1] : null;
            }
            if (this.finalTimeRange) {
                this.filter.beginFinalTime = this.finalTimeRange ? this.finalTimeRange[0] : null;
                this.filter.endFinalTime = this.finalTimeRange ? this.finalTimeRange[1] : null;
            }

            this.listLoading = true;
            const res = await exportRepresentationDetail(this.filter);
            this.listLoading = false;
            if (!res?.data) return;
            const aLink = document.createElement('a');
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            aLink.setAttribute('download', '申诉明细数据_' + new Date().toLocaleString() + '.xlsx');
            aLink.click();
        },
        //打开设置申诉人弹窗
        onClaim() {
            if (!this.selectList || this.selectList.length == 0) {
                this.$message({ message: "请先选择数据！", type: "warning"} );
                return;
            }
            this.setVisible = true;
        },
        //取消设置申诉人弹窗
        setCancle() {
            this.setVisible = false;
            this.representationPersonID = [];
        },
        //关闭设置申诉人弹窗
        closeSetDialog() {
            this.setVisible = false;
            this.representationPersonID = [];
        },
        //提交设置申诉人
        async setSubmit() {
            if (this.representationPersonID.length == 0) {
                this.$message({ message: "请先设置申述人！", type: "warning"} );
                return;
            }
            const params = { row: this.selectList, representationPersonID: this.representationPersonID};
            this.$confirm('确认设置申述人?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                .then(async () => {
                    const res = await setRepresentationPerson(params);
                    if (!res) { return; }
                    this.$message({ type: 'success', message: '设置申诉成功!' });
                    this.setVisible = false;
                    this.representationPersonID = [];
                    this.selectList = [];
                    this.getList();
                }).catch(() => {
                    this.$message({ type: 'info', message: '设置申诉失败' });
                    this.selectList = [];
                    this.representationPersonID = [];
                });
        },
        //打开上传弹窗
        ImportDistOrderData() {
            this.dialogVisible = true;
            this.uploadLoading = false;
            this.$nextTick(() => {
                if (this.$refs.upload) {
                    this.$refs.upload.clearFiles();
                }
            });
            this.fileList.splice(0, 1);
        },
        //上传文件
        async uploadFile(item) {
            const form = new FormData();
            form.append("upfile", item.file);
            const res = importTaoFactoryShopRepresentData(form);
            this.$message({message: '上传成功,正在导入中...', type: "success"});
        },
        //更改上传文件
        async uploadChange(file, fileList) {
            if (fileList.length == 2) {
                fileList.splice(1, 1);
                this.$message({ message: "只允许单文件导入", type: "warning" });
                return false;
            }
            this.fileList.push(file);
        },
        //移除上传文件
        uploadRemove() {
            this.fileList.splice(0, 1);
        },
        //提交上传文件
        async submitupload() {
            if (this.fileList.length == 0) {
                this.$message.warning('您没有选择任何文件！');
                return;
            }
            this.$refs.upload.submit();
            this.$refs.upload.clearFiles();
            this.fileList.splice(0, 1);
            this.importVisible = false;
        },
        async callback1(val) {
            this.filter.masterOrderNumber = val;
        },
        async callback2(val) {
            this.filter.refundOrderNumber = val;
        },
        async callback3(val) {
            this.filter.representOrderNumber = val;
        },
        async callback4(val) {
            this.filter.productID = val;
        },
    }
}
</script>
<style lang="scss" scoped>
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 60px;
}
</style>