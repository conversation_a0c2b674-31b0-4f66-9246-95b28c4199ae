<template>
    <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
        <el-scrollbar style="height: 100%">
            <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="160px" class="demo-ruleForm">
                <el-form-item label="区域：" prop="regionName" :rules="[{ required: true, message: '区域不能为空', trigger: 'blur' }]">
                    <el-select v-model="ruleForm.regionName" placeholder="区域" class="publicCss" collapse-tags>
                        <el-option v-for="item in regionNameList" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item label="类型：" prop="llmType" :rules="[{ required: true, message: '类型不能为空', trigger: 'blur' }]">
                    <el-select v-model="ruleForm.llmType" placeholder="类型" class="publicCss" collapse-tags>
                        <el-option v-for="item in sectionList1" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item label="模型提供商：" prop="llmProvider" :rules="[{ required: true, message: '模型提供商不能为空', trigger: 'blur' }]">
                    <el-select v-model="ruleForm.llmProvider" placeholder="模型提供商" class="publicCss" collapse-tags>
                        <el-option v-for="item in sectionList2" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item label="模型名称：" prop="defaultModel" :rules="[{ required: true, message: '模型名称不能为空', trigger: 'blur' }]">
                    <el-input style="width:80%;"
                     show-word-limit
                    :rows="4" v-model.trim="ruleForm.defaultModel"
                    :maxlength="100" placeholder="模型名称" clearable />
                </el-form-item>

                <el-form-item label="url地址：" prop="baseUrl" :rules="[{ required: true, message: 'url地址不能为空', trigger: 'blur' }]">
                    <el-input style="width:80%;"
                     show-word-limit
                    :rows="4" v-model.trim="ruleForm.baseUrl"
                    :maxlength="100" placeholder="url地址" clearable />
                </el-form-item>

                <el-form-item label="apikey：" prop="apiKey" :rules="[{ required: true, message: 'apikey不能为空', trigger: 'blur' }]">
                    <el-input style="width:80%;"
                     show-word-limit
                    :rows="4" v-model.trim="ruleForm.apiKey"
                    :maxlength="100" placeholder="apikey" clearable />
                </el-form-item>

                <el-form-item label="失败最大重试次数：" prop="maxRetries">
                    <inputNumberYh v-model="ruleForm.maxRetries" :placeholder="'失败最大重试次数'" class="publicCss" />
                </el-form-item>
                <el-form-item label="温度：" prop="temperature">
                    <div style="display: flex; align-items: center;">
                        <div style="width: 100px;">
                            <inputNumberYh max="10" disabled v-model="ruleForm.temperature" :placeholder="'温度'" class="publicCss" />
                        </div>
                        <div style="width: 65%;">
                            <el-slider
                            v-model="ruleForm.temperature"
                            :step="1"
                            max="10"
                            show-stops>
                            </el-slider>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="最大token数：" prop="maxTokens">
                    <inputNumberYh v-model="ruleForm.maxTokens" :placeholder="'最大token数'" class="publicCss" />
                </el-form-item>



            </el-form>
        </el-scrollbar>
        <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
            <el-button @click="cancellationMethod">取消</el-button>
            <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
        </div>
    </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { aiLlmConfigSubmit } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission'
export default {
    name: 'departmentEdit',
    components: {
        inputNumberYh, MyConfirmButton
    },
    props: {
        editInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
        districtList: {
            type: Object,
            default: () => {
                return {}
            }
        },
        typeList: {
            type: Object,
            default: () => {
                return {}
            }
        },
        lableList: {
            type: Object,
            default: () => {
                return []
            }
        }
    },
    data() {
        return {
            regionNameList: ['南昌','深圳', '武汉'],
            sectionList1: ['local','online'],
            sectionList2:['Ollama','Deepseek','OpenAI','硅基流动','文心一言','阿里云','阿里千问','Gemini' ],
            selectProfitrates: [],
            ruleForm: {
                label: '',
                name: ''
            },
            rules: {
                attendanceFigures: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                totalAmount: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                foreignObjects: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                totalOrders: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
            }
        }
    },

    async mounted() {
        this.$nextTick(() => {
            this.$refs.refruleForm.clearValidate();
        });
        this.ruleForm = { ...this.editInfo };
    },
    methods: {
        cancellationMethod() {
            this.$emit('cancellationMethod');
        },
        submitForm(formName) {
            console.log(this.ruleForm.label, 'this.ruleForm.label');
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing")
                    const { data, success } = await aiLlmConfigSubmit(this.ruleForm)
                    if (!success) {
                        return
                    }
                    this.$message.success('操作成功')
                    await this.$emit("search");

                } else {
                    console.log('error submit!!');
                    this.$message.error('操作失败')
                    return false;
                }
            });
            //   this.$confirm('是否保存?', '提示', {
            //     confirmButtonText: '确定',
            //     cancelButtonText: '取消',
            //     type: 'warning'
            //   }).then(async () => {
            //     this.$refs[formName].validate(async(valid) => {
            //       if (valid) {
            //         const { data, success } = await aiLlmConfigSubmit(this.ruleForm)
            //         if(!success){
            //             return
            //         }
            //         await this.$emit("search");

            //       } else {
            //         console.log('error submit!!');
            //         return false;
            //       }
            //     });
            //   }).catch(() => {
            //   });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
    }
}
</script>
<style scoped lang="scss">
.publicCss {
    width: 80%;
}
</style>
