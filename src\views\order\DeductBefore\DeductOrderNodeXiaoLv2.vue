<template>
    <my-container v-loading="pageLoading">
        <template #header>
            发货时间:
            <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" range-separator="至" :start-placeholder="'开始时间'" :end-placeholder="'结束时间'"
                :picker-options="pickerOptions">
            </el-date-picker>
            揽收时间:
            <el-date-picker style="width: 240px" v-model="filter.timerange2" type="daterange" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" range-separator="至" :start-placeholder="'开始时间'" :end-placeholder="'结束时间'"
                :picker-options="pickerOptions">
            </el-date-picker>

            <el-input v-model.trim="filter.orderNoInner" placeholder="内部单号" style="width:120px;" clearable
                oninput="if(value){value=value.replace(/[^\-\d]/g,'')} if(value>2147483647){value=2147483647} if(value<0){value=0}" />

            <el-select v-model="filter.platform" clearable placeholder="平台" style="width: 100px" filterable>
                <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <!-- <el-input v-model.trim="filter.orderNo" placeholder="线上单号" style="width:160px;" clearable /> -->

            <el-select v-model="filter.shiXiaoRemark2" style="width:120px;" placeholder="备注" :clearable="true">
                <el-option label="未知" value="未知"></el-option>
                <el-option label="时效正常" value="时效正常"></el-option>
                <el-option label="分析快递公司" value="分析快递公司"></el-option>
            </el-select>

            <el-select v-model="filter.maxWmsNameList" clearable filterable placeholder="发货仓库" style="width: 180px"
                multiple collapse-tags>
                <el-option v-for="item in warehouselist" :key="'fhck' + item" :label="item" :value="item" />
            </el-select>

            <el-select v-model="filter.maxWmsNameNoList" clearable filterable placeholder="排除仓库" style="width: 180px"
                multiple collapse-tags>
                <el-option v-for="item in warehouselist" :key="'fhckno' + item" :label="item" :value="item" />
            </el-select>

            <!-- <el-input v-model.trim="filter.maxWmsName" clearable placeholder="发货仓库" style="width:100px;"
                :maxlength="20" /> -->

            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="onExport" :loading="onExportLoading">导出</el-button>
        </template>
        <template style="margin-top: 10px;">
            <el-row style="height: 100%;">
                <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16" style="height: 100%;">

                    <vxetablebase :id="'DeductOrderNodeXiaoLv220241009001'" ref="table" :that='that' :isIndex='true'
                        :cstmExportFunc="onExport" :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
                        :showsummary="true" @select="selectchange" :tableData='list' :tableCols='tableCols'
                        :isSelection="true" :loading="listLoading">
                    </vxetablebase>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                    <pie-chart :pie-data="pieData" />
                </el-col>
            </el-row>
        </template>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="selids.length" @get-page="getlist" />
        </template>



        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
            v-dialogDrag>
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNoInner="orderNoInner"
                style="z-index:10000;height:600px" />
        </el-dialog>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import PieChart from '@/views/admin/homecomponents/PieChart2.vue'
import { pickerOptions, platformlist } from '@/utils/tools'
import { PageDeductOrderNodeInfoList, DeductOrderNodeInfoPie, ExportDeductOrderNodeInfoXiaoLv2 } from "@/api/order/deductbefore";
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";

const tableCols = [
    { istrue: true, prop: 'orderNoInner', fixed: 'left', label: '内部单号', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showLogDetail(row) },
    { istrue: true, prop: 'platform', fixed: 'left', label: '平台', width: '80', sortable: 'custom', formatter: (row) => row.platformName },
    { istrue: true, prop: 'orderNo', fixed: 'left', label: '线上单号', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'faHuoTime', label: '发货时间', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.faHuoTime, "YYYY") == '2050' ? '' : formatTime(row.faHuoTime, "MM-DD HH:mm") },
    { istrue: true, prop: 'lanShouTime', label: '揽收时间', width: '90', sortable: 'custom', formatter: (row) => row.lanShouTime == null ? '' : formatTime(row.lanShouTime, "YYYY") == '2050' ? '' : formatTime(row.lanShouTime, "MM-DD HH:mm") },
    { istrue: true, prop: 'faHuo_LanShou', label: '发货-揽收耗时(h)', width: '160', sortable: 'custom', type: 'html', formatter: (row) => row.faHuo_LanShou_Html },
    { istrue: true, prop: 'shiXiaoRemark2', label: '备注', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'maxWmsName', label: '发货仓库', width: '140', sortable: 'custom' },
]
const baseWareList = [
    '义乌市昀晗供应链管理有限公司',
    '【南昌芒果仓】',
    '【义乌邮政仓】',
    '【义乌跨境仓】',
    '【西安港务分仓】',
    '【昀晗-NC】',
    '【昀晗-YY】',
    '【昀晗-SZ】',
    '【昀晗-DG】',
    '【昀晗-JD】',
    '昀晗-退件仓',
    '【昀晗-WH】',
    '【南昌定制仓】',
    '【昀晗-JM】',
    '【义乌圆通5楼】',
    '【昀晗-礼品仓】',
    '【南昌裁剪仓】',
    '【大马美甲仓】',
    '【昀晗-FZ】',
    '【西安分仓】',
    '【南昌全品类仓】',
    '【义乌圆通爆款仓】',
    '【昀晗-金华仓】',
    '【天天2楼仓】',
    '【义乌圆通2楼】',
    '【首力供应链】',
    '【昀晗-AH】',
    '【昀晗-JS】',
    '【邮政仓配中心】',
    '【昀晗-三方仓储】',
    '【昀晗-CS】',
    '【昀晗-云仓】',
    'JD-昀晗义乌仓',
    '外仓加工-半成品仓',
    '【义乌加工仓】',
    '大马对接浅深专用分仓',
    '外仓加工-成品仓',
    '【罗小曼仓】',
    '昀晗-中转仓',
    '铝合金踢脚线代发仓',
    '【小茶日记】',
    '涡轮洗衣机代发'
];

export default {
    name: 'DeductOrderNodeXiaoLv2',
    components: { MyContainer, MyConfirmButton, vxetablebase, PieChart, OrderActionsByInnerNos },
    data() {
        return {
            that: this,
            pickerOptions: pickerOptions,
            platformlist: platformlist,
            warehouselist: baseWareList,
            filter: {
                timerange: [
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD")],
                timerange2: [],
                selectDateType: "FaHuoTime",
                selectDateType2: "LanShouTime",
                shiXiaoRemarkType: "2",
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "pay_Package", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            selids: [],
            listLoading: false,
            pageLoading: false,
            onExportLoading: false,
            pieData: {},

            dialogHisVisible: false,
        };
    },
    async mounted() {
        //await this.onSearch()
    },
    methods: {
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist();
            await this.getPie();
        },
        //获取查询条件
        getCondition() {
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.submitDateStart = this.filter.timerange[0];
                this.filter.submitDateEnd = this.filter.timerange[1];
            } else {
                this.$message({ message: "请先选择付款日期", type: "warning" });
                return false;
            }
            if (this.filter.timerange2 && this.filter.timerange2.length > 1) {
                this.filter.otherDateStart = this.filter.timerange2[0];
                this.filter.otherDateEnd = this.filter.timerange2[1];
            } else {
                this.filter.otherDateStart = null;
                this.filter.otherDateEnd = null;
            }

            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            console.log(params);
            return params;
        },
        //分页查询
        async getlist() {
            let params = this.getCondition();
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await PageDeductOrderNodeInfoList(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.selids = [];
            this.sels = [];

            this.total = res.data.total;
            const data = res.data.list;
            this.list = data;
            this.summaryarry = res.data.summary;
        },
        async getPie() {
            let params = this.getCondition();
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await DeductOrderNodeInfoPie(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }

            let pieList = res.data.map(f => {
                return {
                    name: f.name,
                    value: f.value,
                    ratio: f.ratio,
                }
            });

            this.pieData = {
                title: "发货时效",
                legend: res.data.map(f => f.name),
                pieSeries: pieList
            };
        },
        //排序查询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            this.sels = [];
            //console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
                this.sels.push(f);
            })
        },
        async onExport(opt) {
            let pars = this.getCondition();
            if (pars === false) {
                return;
            }
            const params = { ...pars, ...opt };
            let res = await ExportDeductOrderNodeInfoXiaoLv2(params);
            if (!res?.data) {
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '违规前置分析_揽收时效_' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },

        showLogDetail(row) {
            this.orderNoInner = row.orderNoInner;
            this.dialogHisVisible = true;
        },
    },
};
</script>

<style lang="scss" scoped>
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>
