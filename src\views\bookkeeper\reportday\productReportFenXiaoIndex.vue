<template>
    <my-container v-loading="pageLoading" style="height: 100%">
      <el-tabs v-model="activeName" style="height: 94%">
         <el-tab-pane label="分销订单日报" name="first1" style="height: 100%" v-if="checkPermission('FenXiaoOrderDayReport')">
          <FenXiaoOrderDayReport @ChangeActiveName2="ChangeActiveName2"  ref="FenXiaoOrderDayReport" @seriesCodingJump="seriesCodingJump" style="height: 100%"></FenXiaoOrderDayReport>
        </el-tab-pane>
        <el-tab-pane label="分销编码日报" name="first2" style="height: 100%" v-if="checkPermission('FenXiaoGoodCodeDayReport')">
          <FenXiaoGoodCodeDayReport @ChangeActiveName2="ChangeActiveName2"  @ChangeActiveName="ChangeActiveName"  ref="FenXiaoGoodCodeDayReport" @seriesCodingJump="seriesCodingJump" style="height: 100%"></FenXiaoGoodCodeDayReport>
        </el-tab-pane>
        <el-tab-pane label="分销商日报" name="first3" style="height: 100%" v-if="checkPermission('FenXiaoShangDayReport')">
          <FenXiaoShangDayReport @ChangeActiveName2="ChangeActiveName2"  @ChangeActiveName="ChangeActiveName"  ref="FenXiaoShangDayReport" style="height: 100%"></FenXiaoShangDayReport>
        </el-tab-pane>
        <el-tab-pane label="分销明细日报" name="first4" style="height: 100%">
          <productReportFenXiao @ChangeActiveName2="ChangeActiveName2"  ref="productReportFenXiao" style="height: 100%"></productReportFenXiao>
        </el-tab-pane>
      </el-tabs>
    </my-container>
  </template>

  <script>
  import MyContainer from "@/components/my-container";
  import productReportFenXiao from "./productReportFenXiao.vue";
  import FenXiaoOrderDayReport from "./FenXiaoOrderDayReport.vue";
  import FenXiaoGoodCodeDayReport from "./FenXiaoGoodCodeDayReport.vue";
  import FenXiaoShangDayReport from "./FenXiaoShangDayReport.vue";
  export default {
    name: "productReportFenXiaoIndex",
    components: {
      MyContainer, productReportFenXiao,FenXiaoOrderDayReport,FenXiaoGoodCodeDayReport,FenXiaoShangDayReport
    },
    data() {
      return {
        that: this,
        pageLoading: false,
        activeName: "first1",
      };
    },
    async mounted() {
    },
    methods: {
      seriesCodingJump(row) {
        this.activeName = 'first4';
        this.$refs.productReportFenXiao.onSeriesCoding(row)
      },
      ChangeActiveName(activeName){
        this.activeName = 'first1';
        this.$refs.FenXiaoOrderDayReport.FenXiaoGoodCodeDayReportArgument(activeName)
      },
      ChangeActiveName2(orde, No, Timerange, row){
      this.$nextTick(() => {
        this.activeName = 'first4';
        this.$refs.productReportFenXiao.FenXiaoDayReportArgument(orde, No, Timerange, row)
      });
      }
    },
  };
  </script>

  <style lang="scss" scoped></style>
