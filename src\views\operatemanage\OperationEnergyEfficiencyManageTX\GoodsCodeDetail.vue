<template>
    <my-container >
        <template #header>
            <el-form
        class="ad-form-query"
        :inline="true"
        :model="filter"
        @submit.native.prevent
      >
            <el-input v-model.trim="filter.GoodsCode" clearable style="width: 150px" :maxlength="25" placeholder="商品编码"/>       
            <el-button type="primary" @click="onSearch">查询</el-button>
           </el-form>
        </template>
        <ces-table 
        :loading="listLoading"
        ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn="true"  
          :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='tableData' 
          :tableCols='tableCols' :tableHandles='tableHandles'  style="width:100%;height:92%;margin: 0">
        </ces-table>
        <!--分页-->
        <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
        </template>
        <el-dialog title="编码到货" :visible.sync="GoodsCodePredict.visible" width="80%" v-dialogDrag>
            <div>
              
              <GoodsCodePredict ref="GoodsCodePredict" :filter="GoodsCodePredict.filter" style="height:600px;"></GoodsCodePredict>
            </div>
          </el-dialog>
          <el-dialog title="编码对应ID" :visible.sync="GoodsCodeCorrespondToId.visible" width="80%" v-dialogDrag>
            <div>
              
              <GoodsCodeCorrespondToId ref="GoodsCodeCorrespondToId" :filter="GoodsCodeCorrespondToId.filter" style="height:600px;"></GoodsCodeCorrespondToId>
            </div>
          </el-dialog>

    </my-container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import MyContainer from '@/components/my-container';
import cesTable from '@/components/Table/table.vue';
import {formatLinkProCode} from '@/utils/tools'
import {
    getOrderDetail, exportOrderDetail
} from '@/api/order/ordergoods';
import {queryPurchaseOrderDetail,importPurchaseReturnGoods,getpurchaseReturnGoodsList,deletePurchaseReturnGoods} from '@/api/inventory/purchase'
import {getGoodsCodeDetail,GoodsCodeDetailExport} from  '@/api/operatemanage/base/product'
import GoodsCodePredict from "./GoodsCodePredict.vue";
import GoodsCodeCorrespondToId from "./GoodsCodeCorrespondToId.vue";





const tableCols =[
    {istrue:true,prop:'goodsCode',label:'商品编码', width:'210', type: 'click', handle: (that, row) => that.showGoodsCodeDetail(row)},
    {istrue:true,prop:'onlineGoodsName',label:'线上商品名称', width:'330'},
    {istrue:true,prop:'groupName',label:'运营组', width:'220'},
    {istrue:true,prop:'brandName',label:'采购', width:'120', type: 'click', handle: (that, row) => that.showPredictGoodsCodeDetail(row)},
    {istrue:true,prop:'goodsInventory',label:'库存', sortable:'custom',width:'120'},
    {istrue:true,prop:'payQty',label:'昨日销量', sortable:'custom',width:'120'},
    {istrue:true,prop:'salesDay7',label:'七天销量', sortable:'custom',width:'120'},
    {istrue:true,prop:'inTransitNum',label:'采购在途', sortable:'custom',width:'120'},
    //{istrue:true,prop:'inTransitNum',label:'到货日期/方案', width:'120'},
    // {istrue:true,prop:'salesDay7',label:'解决方案', width:'120'},
   
];

const tableHandles=[
        {label:"导出", handle:(that)=>that.onExport()},
      ];

// const startDate = formatTime(dayjs().subtract(1,'day'), "YYYY-MM-DD");
// const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default ({
    name:"Users",
    components:{MyContainer,cesTable,GoodsCodePredict,GoodsCodeCorrespondToId},
    
    data(){
        return {
            
            
    GoodsCodePredict: {
        visible: false,
        filter: {
          GoodsCode: null,
        }
      },
      GoodsCodeCorrespondToId: {
        visible: false,
        filter: {
          GoodsCode: null,
        }
      },

    // deletefilter:{
    //     batchNumber:''
    //   },
            that:this, 
            uploadLoading:false,        
            tableCols:tableCols,
             tableHandles:tableHandles,
            tableData:[],
            total: 0,
            pager:{OrderBy:"",IsAsc:false},
            // pageLoading: false,
            summaryarry:{},    
            sels:[],
             filter:{
            GoodsCode:null,
            // proCode:null,
            platform:null,
            //orderNo:null,
            //orderNoInner:null
        }, 
        filter2:{
            GoodsCode:null,
        },
        listLoading: false,
        };
    },
   
    async mounted() {
      if(this.$route.query){
      this.filter.platform =  this.$route.query.Platform;
    }
   
    await this.getList();
   
  },
    methods:{
    async showPredictGoodsCodeDetail(row){
        this.GoodsCodePredict.filter.GoodsCode=row.goodsCode;
        this.GoodsCodePredict.visible = true;
        this.$nextTick(async () => {
            await this.$refs.GoodsCodePredict.onSearch();
          });


    },
    async showGoodsCodeDetail(row){
        this.GoodsCodeCorrespondToId.filter.GoodsCode=row.goodsCode;
        this.GoodsCodeCorrespondToId.visible = true;

        this.$nextTick(async () => {
            
            await this.$refs.GoodsCodeCorrespondToId.onSearch();
          });


        },
     async onExport() {
     if (this.onExporting) return;
     try{
        this.uploadLoading = true;
        const params = {...this.filter}
        var res= await GoodsCodeDetailExport(params);
       if(!res) return
        const aLink = document.createElement("a");
        let blob = new Blob([res], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','淘系编码详情_' + new Date().toLocaleString() + '.xlsx' )
        this.uploadLoading = false;

        aLink.click()
        }catch(err){
          console.log("错误信息",err)
          console.log("错误信息",err.message);
        }
      this.onExporting=false;
     },
          //查询第一页
          async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
//         //按批次号删除
//    async deleteByBatchNumber(){
//           if (!this.deletefilter.batchNumber) {
//        this.$message({type: 'warning',message: '请输入批次号!'});
//        return;
//       } 
//       this.$confirm('确认删除, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
//         }).then(async () => {
//             const res = await deletePurchaseReturnGoods(this.deletefilter)
//             if (!res?.success) {return }
//             this.$message({type: 'success',message: '删除成功!'});
//             this.onSearch()
//         }).catch(() => {
//           this.$message({type: 'info',message: '已取消删除'});
//         });
              
//    },
        async sortchange(column){
            if(!column.order)
                this.pager={};
            else
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
            await this.onSearch();
            },
            async onSearch(){
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        async getList(){
            // this.filter.startDate =null;
            // this.filter.endDate =null;
            // if (this.filter.timeRange && this.filter.timeRange.length>0) {
            //     this.filter.startDate = this.filter.timeRange[0];
            //     this.filter.endDate = this.filter.timeRange[1];
            // }
            this.listLoading = true;
            var pager = this.$refs.pager.getPager();
            var page=this.pager;
            const params = {...page,...pager,...this.filter};
            const res = await getGoodsCodeDetail(params)
            .then(res=>{
                this.total = res.data?.total;
                this.tableData = res.data?.list;
                //this.summaryarry=res.data?.summary; 
               
                   
            });
       
            this.listLoading=false; 
        },
       
    }
})
</script>
<style scoped>
    ::v-deep .el-table__fixed-footer-wrapper tbody td{
        color:blue;
    }
</style>

