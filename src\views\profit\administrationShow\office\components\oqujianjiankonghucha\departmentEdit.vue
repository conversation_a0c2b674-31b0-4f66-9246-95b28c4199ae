<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="140px" class="demo-ruleForm">
        <el-form-item label="武汉、深圳→南昌" prop="wsNc">
          <inputNumberYh v-model="ruleForm.wsNc" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
        <el-form-item label="深圳→义乌" prop="szYw">
          <inputNumberYh v-model="ruleForm.szYw" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
        <el-form-item label="南昌→武汉" prop="ncWh">
          <inputNumberYh v-model="ruleForm.ncWh" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
        <el-form-item label="义乌→深圳" prop="ywSz">
          <inputNumberYh v-model="ruleForm.ywSz" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { regionalMonitoringMutualInspectionSubmit } from '@/api/people/peoplessc.js';
export default {
  name: 'departmentEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  data() {
    return {
      ruleForm: {
        wsNc: '',
        szYw: '',
        ncWh: '',
        ywSz: '',
      },
      rules: {
        wsNc: [
          { required: true, message: '请输入武汉、深圳→南昌', trigger: 'blur' },
        ],
        ncWh: [
          { required: true, message: '请输入南昌→武汉', trigger: 'blur' },
        ],
        szYw: [
          { required: true, message: '请输入深圳→义乌', trigger: 'blur' },
        ],
        ywSz: [
          { required: true, message: '请输入义乌→深圳', trigger: 'blur' },
        ],
      }
    }
  },

  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };
  },
  methods: {
    cancellationMethod() {
      this.$emit('cancellationMethod');
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          const { data, success } = await regionalMonitoringMutualInspectionSubmit(this.ruleForm)
          if (!success) {
            return
          }
          this.$emit("search");
        } else {
          console.error('submit failed, reason: ', valid);
          return false;
        }
      });
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}
</style>
