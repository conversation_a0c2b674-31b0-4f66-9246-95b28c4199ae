<template>
  <my-container v-loading="pageLoading">
    <div style="position: absolute; top: -5px;left: 230px;font-weight: bold;">售卖订单：{{ fxData.saleCount_sum }}
      售卖金额：{{ fxData.payAmont_1_sum }} 亏损订单：{{ fxData.minusSaleCount_sum ? fxData.minusSaleCount_sum.toFixed(0) : "0" }}
      亏损金额：{{ fxData.negativeExitProfit_sum }} 亏损订单占比：{{ fxData.saleCountRate }} 亏损金额占比：{{ fxData.saleAmontRate }}
      <span style="color: red;">（温馨提示：付款金额-销售成本-1.3小于等于0判定为亏损）</span>
    </div>
    <el-tabs v-model="activeName" style="height: 93%" @tab-click="handleClick">
      <el-tab-pane label="编码利润" name="first1" class="tab-pane">
        <productReportPddGoods ref="refproductReportPddGoods" style="height: 100%" @transmit="transmit"
          :grouplist="grouplist" />
      </el-tab-pane>
      <el-tab-pane label="品牌比价" name="first2" class="tab-pane">
        <brandPriceComparison ref="refbrandPriceComparison" style="height: 100%" :grouplist="grouplist" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import productReportPddGoods from "./productReportPddGoods.vue";
import brandPriceComparison from "./brandPriceComparison.vue";
import { getDirectorGroupList, getDirectorList } from '@/api/operatemanage/base/shop'
export default {
  name: "productCodedProfitIndex",
  components: {
    MyContainer, productReportPddGoods, brandPriceComparison,
  },
  props: {
    info: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
      fxData: {},
      grouplist: [],
    };
  },
  async mounted() {
    this.handleClick();
    this.getfilterdate();
  },
  methods: {
    async getfilterdate() {
      let groups = await getDirectorGroupList();
      this.grouplist = groups.data?.map(item => { return { value: item.key, label: item.value }; });
    },
    transmit(data) {
      this.fxData = data;
      console.log(this.fxData, 'this.fxData');
    },
    handleClick(tab, event) {
      console.log(this.info, 'this.info');
      this.$nextTick(() => {
        if (this.activeName === 'first1') {
          this.$refs.refproductReportPddGoods.loadData(this.info);
        } else if (this.activeName === 'first2') {
          this.$refs.refbrandPriceComparison.loadData(this.info);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.tab-pane {
  height: 590px;
}
</style>
