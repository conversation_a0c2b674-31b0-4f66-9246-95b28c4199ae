<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent></el-form>
            <div style="display:flex;flex-direction:column;">
                <el-button-group>
                    <el-button style="padding: 0;margin: 0; border: none;">
                        <el-date-picker style="width: 250px" v-model="filter.timerange" type="daterange"
                            format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false" range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期"
                            :picker-options="pickerOptions" @change="changeTime($event,1)"></el-date-picker>
                    </el-button>
                    <el-button style="padding: 0;margin: 0; border: none;">
                        <el-date-picker style="width: 250px" v-model="filter.timerangeOnTime" type="daterange"
                            format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="true" range-separator="至"
                            start-placeholder="上架开始日期" end-placeholder="上架结束日期"
                            :picker-options="pickerOptions" @change="changeTime($event,2)"></el-date-picker>
                    </el-button>
                    <el-button v-show="(userType == 2 || userType == 1) && !visiblePersonal"
                        style="padding: 0;margin: 0; border: none;">
                        <el-date-picker style="width: 300px" v-model="filter.timerangeEntry" type="daterange"
                            format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="true" range-separator="至"
                            start-placeholder="入职开始日期" end-placeholder="入职结束日期"
                            :picker-options="pickerOptions" @change="changeTime($event,3)"></el-date-picker>
                    </el-button>
                    <el-button v-show="userType == 4 && !visiblePersonal" style="padding: 0;margin: 0; border: none;">
                        <el-select filterable v-model="filter.shopManager" collapse-tags clearable placeholder="店铺负责人"
                            style="width: 95px">
                            <el-option v-for="item in shopList" :key="item.value" :label="item.label"
                                :value="item.label" />
                        </el-select>
                    </el-button>
                    <el-button v-show="userType == 4 && !visiblePersonal" style="padding: 0;margin: 0; border: none;">
                        <el-select v-model="filter.mainCategories" placeholder="公司类目" collapse-tags clearable filterable
                            style="width: 80px">
                            <el-option v-for="item in mainCategoriesList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button v-show="userType != 8" style="padding: 0; border: none;">
                        <el-select filterable v-model="filter.groupIds" collapse-tags clearable multiple
                            placeholder="运营组" style="width: 150px">
                            <el-option key="无运营组" label="无运营组" :value="0"></el-option>
                            <el-option v-for="item in grouplist" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button v-show="userType == 8 || userType == 3" style="padding: 0;border: none;">
                        <el-select filterable v-model="filter.superviseIds" collapse-tags clearable placeholder="运营主管"
                            multiple style="width: 150px">
                            <el-option v-for="item in superviselist" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button v-show="userType != 8" style="padding: 0; border: none;">
                        <el-select filterable v-model="filter.company" collapse-tags clearable placeholder="分公司"
                            style="width: 80px">
                            <el-option v-for="item in childcompany" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button v-show="userType != 8" style="padding: 0; border: none;">
                        <el-select filterable v-model="filter.IsCompete" collapse-tags clearable placeholder="是否竞价"
                            style="width: 85px">
                            <el-option label="是" value="1"></el-option>
                            <el-option label="否" value="0"></el-option>
                        </el-select>
                    </el-button>
                    <el-button v-show="userType != 8" style="padding: 0; border: none;">
                        <el-select filterable v-show="userType == 1 || userType == 2 || userType == 5 || userType == 6"
                            v-model="filter.onlineStatus" collapse-tags clearable placeholder="员工状态"
                            style="width: 80px">
                            <el-option key="正式" label="正式" :value="3"></el-option>
                            <el-option key="试用" label="试用" :value="2"></el-option>
                            <el-option key="离职" label="离职" :value="1"></el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0; border: none;">
                        <el-select filterable v-model="filter.Profit3Lose" collapse-tags clearable placeholder="毛三利润"
                            style="width: 80px">
                            <el-option key="正利润" label="正利润" :value="0"></el-option>
                            <el-option key="负利润" label="负利润" :value="1"></el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0; border: none;">
                        <el-select filterable v-model="filter.Profit33Lose" collapse-tags clearable placeholder="毛四利润"
                            style="width: 80px">
                            <el-option key="正利润" label="正利润" :value="0"></el-option>
                            <el-option key="负利润" label="负利润" :value="1"></el-option>
                        </el-select>
                    </el-button>
                    <!-- <el-button style="padding: 0; border: none;">
                        <el-select filterable v-model="filter.Profit4Lose" collapse-tags clearable placeholder="净利润" style="width: 80px">
                          <el-option key="正利润" label="正利润" :value="0"></el-option>
                          <el-option key="负利润" label="负利润" :value="1"></el-option>
                        </el-select>
                      </el-button> -->
                    <button v-show="userType != 8" style="padding: 0; border: none;float: left;">
                        <el-select v-model="filter.star" placeholder="星星" clearable filterable style="width: 70px">
                            <el-option label="空白" :value="9"><span>空白</span></el-option>
                            <el-option label="灰色" style="color:gray;size: 20px;" :value="8"><i
                                    class="el-icon-star-on">灰色</i></el-option>
                            <el-option label="红色" style="color:red;size: 20px;" :value="1"><i
                                    class="el-icon-star-on">红色</i></el-option>
                            <el-option label="橙色" style="color:orange;size: 20px;" :value="2"><i
                                    class="el-icon-star-on">橙色</i></el-option>
                            <el-option label="黄色" style="color:yellow;size: 10px;" :value="3"><i
                                    class="el-icon-star-on">黄色</i></el-option>
                            <el-option label="绿色" style="color:green" :value="4"><i
                                    class="el-icon-star-on">绿色</i></el-option>
                            <el-option label="蓝色" style="color:blue" :value="5"><i
                                    class="el-icon-star-on">蓝色</i></el-option>
                            <el-option label="靛色" style="color:indigo" :value="6"><i
                                    class="el-icon-star-on">靛色</i></el-option>
                            <el-option label="紫色" style="color:purple" :value="7"><i
                                    class="el-icon-star-on">紫色</i></el-option>
                        </el-select>
                    </button>
                    <button v-show="userType != 8" style="padding: 0; border: none;float: left;">
                        <el-select v-model="filter.flag" placeholder="旗帜" clearable filterable style="width: 70px; ">
                            <el-option label="空白" :value="9"><span>空白</span></el-option>
                            <el-option label="灰色" style="color:gray" :value="8"><i
                                    class="el-icon-s-flag">灰色</i></el-option>
                            <el-option label="红色" style="color:red" :value="1"><i
                                    class="el-icon-s-flag">红色</i></el-option>
                            <el-option label="橙色" style="color:orange" :value="2"><i
                                    class="el-icon-s-flag">橙色</i></el-option>
                            <el-option label="黄色" style="color:yellow" :value="3"><i
                                    class="el-icon-s-flag">黄色</i></el-option>
                            <el-option label="绿色" style="color:green" :value="4"><i
                                    class="el-icon-s-flag">绿色</i></el-option>
                            <el-option label="蓝色" style="color:blue" :value="5"><i
                                    class="el-icon-s-flag">蓝色</i></el-option>
                            <el-option label="靛色" style="color:indigo" :value="6"><i
                                    class="el-icon-s-flag">靛色</i></el-option>
                            <el-option label="紫色" style="color:purple" :value="7"><i
                                    class="el-icon-s-flag">紫色</i></el-option>
                        </el-select>
                    </button>
                    <el-button v-show="userType == 4 && !visiblePersonal" style="padding: 0;margin: 0; border: none;">
                        <el-date-picker style="width: 200px" v-model="filter.timerange2" type="daterange"
                            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                            start-placeholder="店铺创建时间开始" end-placeholder="店铺创建时间结束"
                            :picker-options="pickerOptions" @change="changeTime($event,4)"></el-date-picker>
                    </el-button>
                    <el-button style="padding: 0;margin: 0; border: none;">
                      <el-select v-model="filter.xiFenPlatformList" filterable clearable placeholder="细分平台" multiple collapse-tags style="width: 170px" v-if="checkPermission('SegmentationPlatform')" >
                        <el-option v-for="item in segmentationList" :key="item" :label="item" :value="item" />
                      </el-select>
                    </el-button>
                    <el-button type="primary" @click="queryReport" style="margin-left: 10px;">查询</el-button>
                    <el-button type="primary" @click="onExportClick">导出</el-button>
                    <el-button type="primary" @click="onClear">清空</el-button>
                </el-button-group>
                <el-button-group style="margin-top:3px">
                    <el-button :type="buttonStyle[0]" @click="onShowZl">运营助理</el-button>
                    <el-button :type="buttonStyle[1]" @click="onShowZy">运营专员</el-button>
                    <el-button :type="buttonStyle[21]" @click="onShowDj">运营带教</el-button>
                    <el-button :type="buttonStyle[7]" v-if="checkPermission('onShowProductZl')"
                        @click="onShowProductZl">产品助理</el-button>
                    <el-button :type="buttonStyle[8]" v-if="checkPermission('onShowProductZy')"
                        @click="onShowProductZy">产品专员</el-button>
                    <el-button :type="buttonStyle[4]" @click="onShowGp">运营组</el-button>
                    <el-button :type="buttonStyle[10]" @click="onShowSupervise">运营主管</el-button>
                    <el-button :type="buttonStyle[5]" @click="onShowShop" v-if="checkPermission('StorePerformanceStatistics')">店铺</el-button>
                    <el-button :type="buttonStyle[9]" v-if="checkPermission('onShowDepartment')"
                        @click="onShowDepartment">部门</el-button>

                    <el-button type="info" @click="editZlparam">助理目标参数</el-button>
                    <el-button type="info" @click="editZyparam">专员目标参数</el-button>
                </el-button-group>
                <el-button-group style="margin-top:3px;">
                    <el-button style="margin-top:1px;" v-if="checkPermission('PddPerformance_PddPerformanceProfit3')"
                        :type="buttonStyle[2]" @click="onShowTarget(1)">毛三利润</el-button>
                    <el-button style="margin-top:1px;" v-if="checkPermission('PddPerformance_PddPerformanceNetProfit')"
                        :type="buttonStyle[3]" @click="onShowTarget(2)">净利润</el-button>
                    <!-- <el-button style="margin-top:1px;" v-if="checkPermission('PddPerformance_PddPerformanceOutProfit')"
                        :type="buttonStyle[6]" @click="onShowTarget(3)">负利润</el-button> -->
                    <el-radio-group v-model="filter.refundType" size="small" style="margin-left:5px;"
                        @change="refundTypeChange();">
                          <el-radio-button :label="1">发生维度</el-radio-button>
                          <!-- <el-radio-button :label="2">付款维度</el-radio-button> -->
                          <!-- <el-radio-button :label="3">运营维度</el-radio-button> -->
                    </el-radio-group>
                    <el-button style="margin-top:1px;" :type="buttonStyle[11]" v-show="userType == 3" @click="onShowGroupSum" >团队汇总</el-button>
                </el-button-group>
            </div>
        </template>
        <vxetablebase v-show="userType == 1 && !visiblePersonal && showreportList.length>0"  tablekey="tablekey1" ref="table1" :showsummary='true' :id="'202408290948pddtablekey1'" :showheaderoverflow="false"
            :summaryarry='summaryarry' :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="true"
            :tableData='showreportList' :tableCols='tableCols_1' :tableHandles='tableHandles' :isvirtual="true"
            @summaryClick='onsummaryClick' :loading="listLoading" @sortchange="sortchange" style="height:97%" :border="true" >
        </vxetablebase>

        <vxetablebase v-show="userType == 2 && !visiblePersonal && showreportList.length>0" tablekey="tablekey2" ref="table2" :showsummary='true' :id="'202408290949pddtablekey2'" :showheaderoverflow="false"
            :summaryarry='summaryarry' :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="true"
            :tableData='showreportList' :tableCols='tableCols_2' :tableHandles='tableHandles'
            @summaryClick='onsummaryClick' :loading="listLoading" @sortchange="sortchange" style="height:97%" :border="true" >
        </vxetablebase>

        <vxetablebase v-show="userType == 21 && !visiblePersonal && showreportList.length>0" tablekey="tablekey21" ref="table21" :showsummary='true' :id="'202410261207pddtablekey21'" :showheaderoverflow="false"
            :summaryarry='summaryarry' :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="true"
            :tableData='showreportList' :tableCols='tableCols_21' :tableHandles='tableHandles'
            @summaryClick='onsummaryClick' :loading="listLoading" @sortchange="sortchange" style="height:97%" :border="true" >
        </vxetablebase>

        <vxetablebase v-show="userType == 3 && !visiblePersonal && showreportList.length>0" tablekey="tablekey3" ref="table3" :showsummary='true' :id="'202409021900pddtablekey3'" :showheaderoverflow="false"
            :summaryarry='summaryarry' :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="true"
            :tableData='showreportList' :tableCols='tableCols_3' :tableHandles='tableHandles'
            @summaryClick='onsummaryClick' :loading="listLoading" @sortchange="sortchange" style="height:97%" :border="true" >
        </vxetablebase>

        <vxetablebase v-show="userType == 4 && !visiblePersonal && showreportList.length>0" tablekey="tablekey4" ref="table4" :showsummary='true' :id="'202409021908pddtablekey4'" :showheaderoverflow="false"
          :summaryarry='summaryarry' :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="true"
          :tableData='showreportList' :tableCols='tableCols_4' :tableHandles='tableHandles' @sortchange="sortchange"
          @summaryClick='onsummaryClick' :loading="listLoading" style="height:97%" :border="true" >
        </vxetablebase>
        <vxetablebase v-show="userType == 5 && !visiblePersonal && showreportList.length>0" tablekey="tablekey5" ref="table5" :showsummary='true' :id="'202408290952pddtablekey5'" :showheaderoverflow="false"
            :summaryarry='summaryarry' :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="true"
            :tableData='showreportList' :tableCols='tableCols_5' :tableHandles='tableHandles'
            @summaryClick='onsummaryClick' :loading="listLoading" @sortchange="sortchange" style="height:97%" :border="true" >
        </vxetablebase>
        <vxetablebase v-show="userType == 6 && !visiblePersonal && showreportList.length>0" tablekey="tablekey6" ref="table6" :showsummary='true' :id="'202408290953pddtablekey6'" :showheaderoverflow="false"
            :summaryarry='summaryarry' :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="true"
            :tableData='showreportList' :tableCols='tableCols_6' :tableHandles='tableHandles'
            @summaryClick='onsummaryClick' :loading="listLoading" @sortchange="sortchange" style="height:97%" :border="true" >
        </vxetablebase>
        <vxetablebase v-show="userType == 7 && !visiblePersonal && showreportList.length>0" tablekey="tablekey7" ref="table7" :showsummary='true' :id="'202408290954pddtablekey7'" :showheaderoverflow="false"
            :summaryarry='summaryarry' :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="true"
            :tableData='showreportList' :tableCols='tableCols_7' :tableHandles='tableHandles'
            @summaryClick='onsummaryClick' :loading="listLoading" @sortchange="sortchange" style="height:97%" :border="true" >
        </vxetablebase>

        <vxetablebase v-show="userType == 8 && !visiblePersonal && showreportList.length>0" tablekey="tablekey8" ref="table8" :showsummary='true' :id="'202409021721pddtablekey8'" :showheaderoverflow="false"
            :summaryarry='summaryarry' :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="true"
            :tableData='showreportList' :tableCols='tableCols_8' :tableHandles='tableHandles'
            @summaryClick='onsummaryClick' :loading="listLoading" @sortchange="sortchange" style="height:97%" :border="true" >
        </vxetablebase>

        <el-dialog title="设置助理目标参数" :visible.sync="dialogSetParamZlVisible" width="40%" v-dialogDrag>
            <span>
                <el-row>毛三目标(元)
                    <el-input v-model="targetData" />
                </el-row>
                <el-row>净利目标(元)
                    <el-input v-model="targetData1" />
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="saveZl()">确定</el-button>
            </span>
        </el-dialog>
        <el-dialog title="设置专员目标参数" :visible.sync="dialogSetParamZyVisible" width="40%" v-dialogDrag>
            <span>
                <el-row>毛三目标(元)
                    <el-input v-model="targetData" />
                </el-row>
                <el-row>净利目标(元)
                    <el-input v-model="targetData1" />
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <!-- <el-button @click="dialogSetParamZyVisible = false">确定</el-button> -->
                <el-button @click="saveZy()">确定</el-button>
            </span>
        </el-dialog>
        <el-dialog title="趋势图" :visible.sync="dialogMapVisible.visible" width="80%" v-dialogDrag>
            <div>
                <span>
                    <template>
                        <el-date-picker style="width: 410px" v-model="dialogMapVisible.filter.timerange"
                            type="daterange" @change="similarityDateChange" format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd" :clearable="false" range-separator="至" start-placeholder="开始日期"
                            end-placeholder="结束日期" :picker-options="pickerOptions"></el-date-picker>
                    </template>
                </span>
                <span>
                    <buschar ref="dialogMapVisibleBuschar" v-if="dialogMapVisible.visible"
                        :analysisData="dialogMapVisible.data"></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>
            <!-- 导出dialog -->
    <el-dialog title="导出设置" :visible.sync="outerVisible" v-dialogDrag @close='handleCancle'>

        <div   class="el-dialog__body" >

          <span style="margin-top: 20;">
                连续
                <el-input v-model.trim="filter.exportdayCount" placeholder="请输入" maxlength="5" clearable type="number"
                  class="pass_input" style="width:100px"
                  oninput="value=value.replace(/[^\d]/g,''); if(value == 0 || value.replace('-', '').length > 5) value = ''" />
                日
                <el-select v-model="filter.exportCompare" style="width:70px" placeholder="比较" clearable filterable>
                  <el-option label="大于" :value="1"></el-option>
                  <el-option label="小于" :value="2"></el-option>
                </el-select>
                <el-select v-model="filter.exportSelectProfitRate" style="width:100px" placeholder="利润率" clearable filterable>
                  <el-option label="毛三利润率" :value="'Profit3_rate'"></el-option>
                  <el-option label="毛四利润率" :value="'Profit33Rate'"></el-option>
                  <el-option label="净利率" :value="'Profit4_rate'"></el-option>
                </el-select>
                <el-input v-model.trim="filter.exportDynamicProfit" placeholder="请输入" maxlength="5" clearable type="number"
                  class="pass_input" style="width:100px" oninput="if(value.replace('-', '').length > 5) value = value.slice(0, 5)" @input="handleEdit" />%
                <el-checkbox v-model="filter.exportisPositiveEndBeginProfit">以结束时间为起点</el-checkbox>
          </span>

        </div>
        <div slot="footer" class="dialog-footer" >

          <el-button @click="outerVisible = false">取 消</el-button>
          <el-button type="primary" @click="onExport">导出</el-button>
        </div>
      </el-dialog>
        <div v-show="visiblePersonal">
            <financialReportStaticsByOneUser ref="financialReportStaticsByOneUser" />
        </div>
    </container>
</template>
<script>
const mainCategoriesListConst = [
    { value: 1, label: '服饰箱包' },
    { value: 2, label: '家纺家具家装' },
    { value: 3, label: '家居生活' },
    { value: 4, label: '健康医药' },
    { value: 5, label: '美容个护' },
    { value: 6, label: '母婴玩具' },
    { value: 7, label: '汽配摩托' },
    { value: 8, label: '食品保健' },
    { value: 9, label: '数码电器' },
    { value: 10, label: '水果生鲜' },
    { value: 11, label: '虚拟海淘医药' },
    { value: 12, label: '运动户外' }
]
import { childcompany } from '@/utils/tools';
import { getDirectorGroupList, getDirectorGroupList2, getAllList as getAllShopList, getAllShopXiFenPlatform } from '@/api/operatemanage/base/shop'
import {
    SetPerformanceTarget, getPerformanceTarget, getPerformanceStaticticsByGroup, getPerformanceStaticticsByUserAnalysis
    , getPerformanceStaticticsByGroupAnalysis, getPerformanceStaticticsByShop, exportPerformanceStaticticsByGroup, exportPerformanceStaticticsByShop
} from '@/api/bookkeeper/pddstaticsreport'
import {
    getPerformanceStaticticsByUserMap, getPerformanceStaticticsByGroupMap, getPerformanceStaticticsByUser, getPerformanceStaticticsByShopMap,
    getPerformanceStaticticsByonSuperviseMap, exportPerformanceStaticticsByUser
} from '@/api/bookkeeper/reportday'
import financialReportStaticsByOneUser from '@/views/bookkeeper/pddstaticsreport/financialReportStaticsByOneUser'
import vxetablebase from "@/components/VxeTable/yh_vxetablevisable.vue";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container/nofooter";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import InputMult from "@/components/Comm/InputMult";
import { Loading } from 'element-ui';
import freightDetail from '@/views/bookkeeper/reportday/freightDetail'
import buschar from '@/components/Bus/buschar'
import checkPermission from '@/utils/permission'
import middlevue from "@/store/middle.js"

let loading;
const startLoading = () => {
    loading = Loading.service({
        lock: true,
        text: '加载中……',
        background: 'rgba(0, 0, 0, 0.7)'
    });
};

const tableCols1 = [
    { istrue: true, display: true, prop: 'userName', label: '运营助理', width: '80', type: "click", handle: (that, row, column, cell) => that.canclick(row, column, cell) },
  { istrue: true, label: '小组头像', width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
    { istrue: true, sortable: 'custom', display: true, prop: 'groupId', exportField: 'groupName', label: '所在组', width: '70', formatter: (row) => row.groupName, type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'} },
    { istrue: true, sortable: 'custom', prop: 'xiFenPlatform', label: '细分平台', width: '100', permission: "SegmentationPlatform" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'orderCount', label: '订单量', width: '80', formatter: (row) => !row.orderCount ? "0" : row.orderCount },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'payAmont', label: '付款金额', width: '80', formatter: (row) => !row.payAmont ? "0" : row.payAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleAmont', label: '销售金额', width: '70', formatter: (row) => !row.saleAmont ? "0" : row.saleAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmont', exportField: 'refundAmont', label: '退款', width: '80', formatter: (row) => !row.refundAmont ? "0" : row.refundAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontRate', exportField: 'refundAmontRateStr', label: '退款率', width: '80', formatter: (row) => !row.refundAmontRate ? "0%" : (row.refundAmontRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleCost', label: '成本', width: '80', formatter: (row) => !row.saleCost ? " " : row.saleCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit1After', label: '运营毛一(减退款)', width: '80', formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1', label: '毛一利润(发生)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit11', label: '毛一利润(付款)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate', exportField: 'profit1RateStr', label: '毛一利润率(发生)', width: '80', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate1', label: '毛一利润率(付款)', width: '90', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv', label: '广告费', width: '80', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv_rate', exportField: 'alladv_rateStr', label: '广告占比', width: '80', formatter: (row) => (row.alladv_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit2After', label: '运营毛二(减退款)', width: '80', formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3After', label: '运营毛三(减退款)', width: '80', formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3', label: '毛三利润(发生)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit31', label: '毛三利润(付款)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3AfterRate', label: '毛3率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit3AfterRate ? " " : (row.yyProfit3AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate', exportField: 'profit3_rateStr', label: '毛三利润率(发生)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate1', label: '毛三利润率(付款)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4After', label: '运营毛四(减退款)', width: '80', formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5After', label: '运营毛五(减退款)', width: '80', formatter: (row) => !row.yyProfit5After ? " " : row.yyProfit5After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6After', label: '运营毛六(减退款)', permission: "profit6SixtyCents", width: '80', formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5', label: '毛五利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5 ? " " : row.profit5 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6', label: '毛六利润(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6 ? " " : row.profit6 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4AfterRate', label: '毛4率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit4AfterRate ? " " : (row.yyProfit4AfterRate).toString() +  "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5AfterRate', label: '毛5率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit5AfterRate ? " " : (row.yyProfit5AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6AfterRate', label: '毛6率(运营减退)', permission: "profit6SixtyCents", width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit6AfterRate ? " " : (row.yyProfit6AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate', exportField: 'profit33RateStr', label: '毛四利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate1', label: '毛四利润率(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5Rate', exportField: 'profit5Rate', label: '毛五利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6Rate', exportField: 'profit6Rate', label: '毛六利润率(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost', label: '出仓成本(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost1', label: '出仓成本(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '90', formatter: (row) => !row.warehouseSalary ? 0 : row.warehouseSalary },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCostRate', exportField: 'exitCostRateStr', label: '出仓成本占比', sortable: 'custom', width: '100', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4', label: '净利润(发生)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit41', label: '净利润(付款)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate', exportField: 'profit4_rateStr', label: '净利率(发生)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate1', label: '净利率(付款)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'negativeExitProfitIdDetail', label: '出仓负利润', sortable: 'custom', width: '90', formatter: (row) => !row.negativeExitProfitIdDetail ? " " : row.negativeExitProfitIdDetail },
    { istrue: true, summaryEvent: true, display: true, prop: 'deductAmount', label: '违规扣款金额', width: '100', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
    { istrue: true, display: true, prop: 'progress', label: '完成比例', type: "progress" },
    { istrue: true, sortable: 'custom', display: true, prop: 'onlineStatus', label: '员工状态', width: '80', formatter: (row) => row.onlineStatus },
    { istrue: true, display: true, prop: 'createTime', label: '入职时间', width: '100' },
    { istrue: true, display: true, sortable: 'custom', prop: 'workTime', label: '上班时长', width: '80', formatter: (row) => (row.workTime).toString() },
    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

const tableCols2 = [
{ istrue: true, label: '专员头像', width: '70',type:'ddAvatar',ddInfo:{type:2,prop:'userId'} },
    { istrue: true, display: true, prop: 'userName', label: '运营专员', width: '80',type:'ddTalk',ddInfo:{type:2,prop:'userId',name:'userName'}, handle: (that, row, column, cell) => that.canclick(row, column, cell) },
  { istrue: true, label: '小组头像', width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
    { istrue: true, sortable: 'custom', display: true, prop: 'groupId', exportField: 'groupName', label: '所在组',type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, width: '70', formatter: (row) => row.groupName },
    { istrue: true, sortable: 'custom', prop: 'xiFenPlatform', label: '细分平台', width: '100', permission: "SegmentationPlatform" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'orderCount', label: '订单量', width: '80', formatter: (row) => !row.orderCount ? "0" : row.orderCount },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'payAmont', label: '付款金额', width: '80', formatter: (row) => !row.payAmont ? "0" : row.payAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleAmont', label: '销售金额', width: '80', formatter: (row) => !row.saleAmont ? "0" : row.saleAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmont', exportField: 'refundAmont', label: '退款', width: '80', formatter: (row) => !row.refundAmont ? "0" : row.refundAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontRate', exportField: 'refundAmontRateStr', label: '退款率', width: '120', formatter: (row) => !row.refundAmontRate ? "0%" : (row.refundAmontRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleCost', label: '成本', width: '120', formatter: (row) => !row.saleCost ? " " : row.saleCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit1After', label: '运营毛一(减退款)', width: '80', formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1', label: '毛一利润(发生)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit11', label: '毛一利润(付款)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate', exportField: 'profit1RateStr', label: '毛一利润率(发生)', width: '80', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate1', label: '毛一利润率(付款)', width: '90', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv', label: '广告费', width: '100', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv_rate', exportField: 'alladv_rateStr', label: '广告占比', width: '80', formatter: (row) => (row.alladv_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit2After', label: '运营毛二(减退款)', width: '80', formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3After', label: '运营毛三(减退款)', width: '80', formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3', label: '毛三利润(发生)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit31', label: '毛三利润(付款)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3AfterRate', label: '毛3率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit3AfterRate ? " " : (row.yyProfit3AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate', exportField: 'profit3_rateStr', label: '毛三利润率(发生)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate1', label: '毛三利润率(付款)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4After', label: '运营毛四(减退款)', width: '80', formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5After', label: '运营毛五(减退款)', width: '80', formatter: (row) => !row.yyProfit5After ? " " : row.yyProfit5After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6After', label: '运营毛六(减退款)', permission: "profit6SixtyCents", width: '80', formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5', label: '毛五利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5 ? " " : row.profit5 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6', label: '毛六利润(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6 ? " " : row.profit6 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4AfterRate', label: '毛4率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit4AfterRate ? " " : (row.yyProfit4AfterRate).toString() +  "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5AfterRate', label: '毛5率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit5AfterRate ? " " : (row.yyProfit5AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6AfterRate', label: '毛6率(运营减退)', permission: "profit6SixtyCents", width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit6AfterRate ? " " : (row.yyProfit6AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate', exportField: 'profit33RateStr', label: '毛四利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate1', label: '毛四利润率(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5Rate', exportField: 'profit5Rate', label: '毛五利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6Rate', exportField: 'profit6Rate', label: '毛六利润率(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost', label: '出仓成本(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost1', label: '出仓成本(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '90', formatter: (row) => !row.warehouseSalary ? 0 : row.warehouseSalary },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCostRate', exportField: 'exitCostRateStr', label: '出仓成本占比', sortable: 'custom', width: '100', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4', label: '净利润(发生)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit41', label: '净利润(付款)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate', exportField: 'profit4_rateStr', label: '净利率(发生)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate1', label: '净利率(付款)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'negativeExitProfitIdDetail', label: '出仓负利润', sortable: 'custom', width: '90', formatter: (row) => !row.negativeExitProfitIdDetail ? " " : row.negativeExitProfitIdDetail },
    { istrue: true, summaryEvent: true, display: true, prop: 'deductAmount', label: '违规扣款金额', width: '100', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
    { istrue: true, display: true, prop: 'progress', label: '完成比例', type: "progress" },
    { istrue: true, sortable: 'custom', display: true, prop: 'onlineStatus', label: '员工状态', width: '80', formatter: (row) => row.onlineStatus },
    { istrue: true, display: true, prop: 'createTime', label: '入职时间', width: '100' },
    { istrue: true, display: true, sortable: 'custom', prop: 'workTime', label: '上班时长', width: '80', formatter: (row) => (row.workTime).toString() },
    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

const tableCols21 = [
{ istrue: true, label: '带教头像', width: '70',type:'ddAvatar',ddInfo:{type:2,prop:'userId'} },
    { istrue: true, display: true, prop: 'userName', label: '运营带教', width: '80', type:'ddTalk',ddInfo:{type:2,prop:'userId',name:'userName'}, handle: (that, row, column, cell) => that.canclick(row, column, cell) },
  { istrue: true, label: '小组头像', width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
    { istrue: true, sortable: 'custom', display: true, prop: 'groupId', exportField: 'groupName', label: '所在组',type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, width: '70', formatter: (row) => row.groupName },
    { istrue: true, sortable: 'custom', prop: 'xiFenPlatform', label: '细分平台', width: '100', permission: "SegmentationPlatform" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'orderCount', label: '订单量', width: '80', formatter: (row) => !row.orderCount ? "0" : row.orderCount },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'payAmont', label: '付款金额', width: '80', formatter: (row) => !row.payAmont ? "0" : row.payAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleAmont', label: '销售金额', width: '80', formatter: (row) => !row.saleAmont ? "0" : row.saleAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmont', exportField: 'refundAmont', label: '退款', width: '80', formatter: (row) => !row.refundAmont ? "0" : row.refundAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontRate', exportField: 'refundAmontRateStr', label: '退款率', width: '120', formatter: (row) => !row.refundAmontRate ? "0%" : (row.refundAmontRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleCost', label: '成本', width: '120', formatter: (row) => !row.saleCost ? " " : row.saleCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit1After', label: '运营毛一(减退款)', width: '80', formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1', label: '毛一利润(发生)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit11', label: '毛一利润(付款)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate', exportField: 'profit1RateStr', label: '毛一利润率(发生)', width: '80', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate1', label: '毛一利润率(付款)', width: '90', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv', label: '广告费', width: '100', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv_rate', exportField: 'alladv_rateStr', label: '广告占比', width: '80', formatter: (row) => (row.alladv_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit2After', label: '运营毛二(减退款)', width: '80', formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3After', label: '运营毛三(减退款)', width: '80', formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3', label: '毛三利润(发生)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit31', label: '毛三利润(付款)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3AfterRate', label: '毛3率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit3AfterRate ? " " : (row.yyProfit3AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate', exportField: 'profit3_rateStr', label: '毛三利润率(发生)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate1', label: '毛三利润率(付款)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4After', label: '运营毛四(减退款)', width: '80', formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5After', label: '运营毛五(减退款)', width: '80', formatter: (row) => !row.yyProfit5After ? " " : row.yyProfit5After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6After', label: '运营毛六(减退款)', permission: "profit6SixtyCents", width: '80', formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5', label: '毛五利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5 ? " " : row.profit5 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6', label: '毛六利润(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6 ? " " : row.profit6 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4AfterRate', label: '毛4率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit4AfterRate ? " " : (row.yyProfit4AfterRate).toString() +  "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5AfterRate', label: '毛5率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit5AfterRate ? " " : (row.yyProfit5AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6AfterRate', label: '毛6率(运营减退)', permission: "profit6SixtyCents", width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit6AfterRate ? " " : (row.yyProfit6AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate', exportField: 'profit33RateStr', label: '毛四利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate1', label: '毛四利润率(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5Rate', exportField: 'profit5Rate', label: '毛五利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6Rate', exportField: 'profit6Rate', label: '毛六利润率(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost', label: '出仓成本(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost1', label: '出仓成本(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '90', formatter: (row) => !row.warehouseSalary ? 0 : row.warehouseSalary },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCostRate', exportField: 'exitCostRateStr', label: '出仓成本占比', sortable: 'custom', width: '100', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4', label: '净利润(发生)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit41', label: '净利润(付款)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate', exportField: 'profit4_rateStr', label: '净利率(发生)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate1', label: '净利率(付款)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'negativeExitProfitIdDetail', label: '出仓负利润', sortable: 'custom', width: '90', formatter: (row) => !row.negativeExitProfitIdDetail ? " " : row.negativeExitProfitIdDetail },
    { istrue: true, summaryEvent: true, display: true, prop: 'deductAmount', label: '违规扣款金额', width: '100', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
    { istrue: true, display: true, prop: 'progress', label: '完成比例', type: "progress" },
    { istrue: true, sortable: 'custom', display: true, prop: 'onlineStatus', label: '员工状态', width: '80', formatter: (row) => row.onlineStatus },
    { istrue: true, display: true, prop: 'createTime', label: '入职时间', width: '100' },
    { istrue: true, display: true, sortable: 'custom', prop: 'workTime', label: '上班时长', width: '80', formatter: (row) => (row.workTime).toString() },
    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

const tableCols5 = [
    { istrue: true, display: true, prop: 'userName', label: '产品助理', width: '80', type: "click", handle: (that, row, column, cell) => that.canclick(row, column, cell) },
  { istrue: true, label: '小组头像', width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
    { istrue: true, sortable: 'custom', display: true, prop: 'groupId', exportField: 'groupName', label: '所在组',type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, width: '70', formatter: (row) => row.groupName },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'orderCount', label: '订单量', width: '80', formatter: (row) => !row.orderCount ? "0" : row.orderCount },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'payAmont', label: '付款金额', width: '80', formatter: (row) => !row.payAmont ? "0" : row.payAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleAmont', label: '销售金额', width: '80', formatter: (row) => !row.saleAmont ? "0" : row.saleAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmont', exportField: 'refundAmont', label: '退款', width: '80', formatter: (row) => !row.refundAmont ? "0" : row.refundAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontRate', exportField: 'refundAmontRateStr', label: '退款率', width: '120', formatter: (row) => !row.refundAmontRate ? "0%" : (row.refundAmontRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleCost', label: '成本', width: '120', formatter: (row) => !row.saleCost ? " " : row.saleCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit1After', label: '运营毛一(减退款)', width: '80', formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1', label: '毛一利润(发生)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit11', label: '毛一利润(付款)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate', exportField: 'profit1RateStr', label: '毛一利润率(发生)', width: '80', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate1', label: '毛一利润率(付款)', width: '90', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv', label: '广告费', width: '100', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv_rate', exportField: 'alladv_rateStr', label: '广告占比', width: '80', formatter: (row) => (row.alladv_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit2After', label: '运营毛二(减退款)', width: '80', formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3After', label: '运营毛三(减退款)', width: '80', formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3', label: '毛三利润(发生)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit31', label: '毛三利润(付款)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3AfterRate', label: '毛3率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit3AfterRate ? " " : (row.yyProfit3AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate', exportField: 'profit3_rateStr', label: '毛三利润率(发生)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate1', label: '毛三利润率(付款)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4After', label: '运营毛四(减退款)', width: '80', formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5After', label: '运营毛五(减退款)', width: '80', formatter: (row) => !row.yyProfit5After ? " " : row.yyProfit5After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6After', label: '运营毛六(减退款)', permission: "profit6SixtyCents", width: '80', formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5', label: '毛五利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5 ? " " : row.profit5 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6', label: '毛六利润(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6 ? " " : row.profit6 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4AfterRate', label: '毛4率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit4AfterRate ? " " : (row.yyProfit4AfterRate).toString() +  "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5AfterRate', label: '毛5率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit5AfterRate ? " " : (row.yyProfit5AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6AfterRate', label: '毛6率(运营减退)', permission: "profit6SixtyCents", width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit6AfterRate ? " " : (row.yyProfit6AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate', exportField: 'profit33RateStr', label: '毛四利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate1', label: '毛四利润率(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5Rate', exportField: 'profit5Rate', label: '毛五利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6Rate', exportField: 'profit6Rate', label: '毛六利润率(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost', label: '出仓成本(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost1', label: '出仓成本(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '90', formatter: (row) => !row.warehouseSalary ? 0 : row.warehouseSalary },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCostRate', exportField: 'exitCostRateStr', label: '出仓成本占比', sortable: 'custom', width: '100', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4', label: '净利润(发生)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit41', label: '净利润(付款)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate', exportField: 'profit4_rateStr', label: '净利率(发生)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate1', label: '净利率(付款)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'negativeExitProfitIdDetail', label: '出仓负利润', sortable: 'custom', width: '90', formatter: (row) => !row.negativeExitProfitIdDetail ? " " : row.negativeExitProfitIdDetail },
    { istrue: true, summaryEvent: true, display: true, prop: 'deductAmount', label: '违规扣款金额', width: '100', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
    { istrue: true, display: true, prop: 'progress', label: '完成比例', type: "progress" },
    { istrue: true, sortable: 'custom', display: true, prop: 'onlineStatus', label: '员工状态', width: '80', formatter: (row) => row.onlineStatus },
    { istrue: true, display: true, prop: 'createTime', label: '入职时间', width: '100' },
    { istrue: true, display: true, sortable: 'custom', prop: 'workTime', label: '上班时长', width: '80', formatter: (row) => (row.workTime).toString() },
    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];
const tableCols6 = [
{ istrue: true, label: '专员头像', width: '70',type:'ddAvatar',ddInfo:{type:2,prop:'userId'} },
    { istrue: true, display: true, prop: 'userName', label: '产品专员', width: '80', type:'ddTalk',ddInfo:{type:2,prop:'userId',name:'userName'}, handle: (that, row, column, cell) => that.canclick(row, column, cell) },
  { istrue: true, label: '小组头像', width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
    { istrue: true, sortable: 'custom', display: true, prop: 'groupId', exportField: 'groupName', label: '所在组', width: '70',type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, formatter: (row) => row.groupName },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'orderCount', label: '订单量', width: '60', formatter: (row) => !row.orderCount ? "0" : row.orderCount },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'payAmont', label: '付款金额', width: '80', formatter: (row) => !row.payAmont ? "0" : row.payAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleAmont', label: '销售金额', width: '80', formatter: (row) => !row.saleAmont ? "0" : row.saleAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmont', exportField: 'refundAmont', label: '退款', width: '60', formatter: (row) => !row.refundAmont ? "0" : row.refundAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontRate', exportField: 'refundAmontRateStr', label: '退款率', width: '80', formatter: (row) => !row.refundAmontRate ? "0%" : (row.refundAmontRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleCost', label: '成本', width: '60', formatter: (row) => !row.saleCost ? " " : row.saleCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit1After', label: '运营毛一(减退款)', width: '80', formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1', label: '毛一利润(发生)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit11', label: '毛一利润(付款)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate', exportField: 'profit1RateStr', label: '毛一利润率(发生)', width: '80', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate1', label: '毛一利润率(付款)', width: '90', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv', label: '广告费', width: '60', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv_rate', exportField: 'alladv_rateStr', label: '广告占比', width: '80', formatter: (row) => (row.alladv_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit2After', label: '运营毛二(减退款)', width: '80', formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3After', label: '运营毛三(减退款)', width: '80', formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3', label: '毛三利润(发生)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit31', label: '毛三利润(付款)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3AfterRate', label: '毛3率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit3AfterRate ? " " : (row.yyProfit3AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate', exportField: 'profit3_rateStr', label: '毛三利润率(发生)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate1', label: '毛三利润率(付款)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4After', label: '运营毛四(减退款)', width: '80', formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5After', label: '运营毛五(减退款)', width: '80', formatter: (row) => !row.yyProfit5After ? " " : row.yyProfit5After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6After', label: '运营毛六(减退款)', permission: "profit6SixtyCents", width: '80', formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5', label: '毛五利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5 ? " " : row.profit5 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6', label: '毛六利润(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6 ? " " : row.profit6 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4AfterRate', label: '毛4率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit4AfterRate ? " " : (row.yyProfit4AfterRate).toString() +  "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5AfterRate', label: '毛5率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit5AfterRate ? " " : (row.yyProfit5AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6AfterRate', label: '毛6率(运营减退)', permission: "profit6SixtyCents", width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit6AfterRate ? " " : (row.yyProfit6AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate', exportField: 'profit33RateStr', label: '毛四利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate1', label: '毛四利润率(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5Rate', exportField: 'profit5Rate', label: '毛五利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6Rate', exportField: 'profit6Rate', label: '毛六利润率(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost', label: '出仓成本(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost1', label: '出仓成本(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '90', formatter: (row) => !row.warehouseSalary ? 0 : row.warehouseSalary },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCostRate', exportField: 'exitCostRateStr', label: '出仓成本占比', sortable: 'custom', width: '100', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4', label: '净利润(发生)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit41', label: '净利润(付款)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate', exportField: 'profit4_rateStr', label: '净利率(发生)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate1', label: '净利率(付款)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'negativeExitProfitIdDetail', label: '出仓负利润', sortable: 'custom', width: '90', formatter: (row) => !row.negativeExitProfitIdDetail ? " " : row.negativeExitProfitIdDetail },
    { istrue: true, summaryEvent: true, display: true, prop: 'deductAmount', label: '违规扣款金额', width: '100', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
    { istrue: true, display: true, prop: 'progress', label: '完成比例', type: "progress" },
    { istrue: true, sortable: 'custom', display: true, prop: 'onlineStatus', label: '员工状态', width: '80', formatter: (row) => row.onlineStatus },
    { istrue: true, display: true, prop: 'createTime', label: '入职时间', width: '100' },
    { istrue: true, display: true, sortable: 'custom', prop: 'workTime', label: '上班时长', width: '80', formatter: (row) => (row.workTime).toString() },
    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];
const tableCols7 = [
    { istrue: true, display: true, prop: 'userName', label: '姓名', width: '60', type: "click", handle: (that, row, column, cell) => that.canclick(row, column, cell) },
    { istrue: true, label: '小组头像', width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
    { istrue: true, sortable: 'custom', display: true, prop: 'groupId', exportField: 'groupName', label: '所在组',type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, width: '70', formatter: (row) => row.groupName },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'orderCount', label: '订单量', width: '80', formatter: (row) => !row.orderCount ? "0" : row.orderCount },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'payAmont', label: '付款金额', width: '80', formatter: (row) => !row.payAmont ? "0" : row.payAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleAmont', label: '销售金额', width: '80', formatter: (row) => !row.saleAmont ? "0" : row.saleAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmont', exportField: 'refundAmont', label: '退款', width: '80', formatter: (row) => !row.refundAmont ? "0" : row.refundAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontRate', exportField: 'refundAmontRateStr', label: '退款率', width: '80', formatter: (row) => !row.refundAmontRate ? "0%" : (row.refundAmontRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleCost', label: '成本', width: '80', formatter: (row) => !row.saleCost ? " " : row.saleCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit1After', label: '运营毛一(减退款)', width: '80', formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1', label: '毛一利润(发生)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit11', label: '毛一利润(付款)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate', exportField: 'profit1RateStr', label: '毛一利润率(发生)', width: '80', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate1', label: '毛一利润率(付款)', width: '90', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv', label: '广告费', width: '80', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv_rate', exportField: 'alladv_rateStr', label: '广告占比', width: '80', formatter: (row) => (row.alladv_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit2After', label: '运营毛二(减退款)', width: '80', formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3After', label: '运营毛三(减退款)', width: '80', formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3', label: '毛三利润(发生)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit31', label: '毛三利润(付款)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3AfterRate', label: '毛3率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit3AfterRate ? " " : (row.yyProfit3AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate', exportField: 'profit3_rateStr', label: '毛三利润率(发生)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate1', label: '毛三利润率(付款)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4After', label: '运营毛四(减退款)', width: '80', formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5After', label: '运营毛五(减退款)', width: '80', formatter: (row) => !row.yyProfit5After ? " " : row.yyProfit5After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6After', label: '运营毛六(减退款)', permission: "profit6SixtyCents", width: '80', formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5', label: '毛五利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5 ? " " : row.profit5 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6', label: '毛六利润(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6 ? " " : row.profit6 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4AfterRate', label: '毛4率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit4AfterRate ? " " : (row.yyProfit4AfterRate).toString() +  "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5AfterRate', label: '毛5率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit5AfterRate ? " " : (row.yyProfit5AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6AfterRate', label: '毛6率(运营减退)', permission: "profit6SixtyCents", width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit6AfterRate ? " " : (row.yyProfit6AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate', exportField: 'profit33RateStr', label: '毛四利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate1', label: '毛四利润率(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5Rate', exportField: 'profit5Rate', label: '毛五利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6Rate', exportField: 'profit6Rate', label: '毛六利润率(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost', label: '出仓成本(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost1', label: '出仓成本(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '90', formatter: (row) => !row.warehouseSalary ? 0 : row.warehouseSalary },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCostRate', exportField: 'exitCostRateStr', label: '出仓成本占比', sortable: 'custom', width: '100', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4', label: '净利润(发生)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit41', label: '净利润(付款)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate', exportField: 'profit4_rateStr', label: '净利率(发生)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate1', label: '净利率(付款)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'negativeExitProfitIdDetail', label: '出仓负利润', sortable: 'custom', width: '90', formatter: (row) => !row.negativeExitProfitIdDetail ? " " : row.negativeExitProfitIdDetail },
    { istrue: true, summaryEvent: true, display: true, prop: 'deductAmount', label: '违规扣款金额', width: '100', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
    { istrue: true, display: true, prop: 'progress', label: '完成比例', type: "progress" },
    { istrue: true, sortable: 'custom', display: true, prop: 'onlineStatus', label: '员工状态', width: '80', formatter: (row) => row.onlineStatus },
    { istrue: true, display: true, prop: 'createTime', label: '入职时间', width: '100' },
    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

const tableCols3 = [
{ istrue: true, label: '小组头像', width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
    { istrue: true, sortable: 'custom', display: true, prop: 'groupId', exportField: 'groupName', label: '所在组',type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, width: '70', formatter: (row) => row.groupName },
    { istrue: true, sortable: 'custom', prop: 'xiFenPlatform', label: '细分平台', width: '100', permission: "SegmentationPlatform" },
    // { istrue: true, summaryEvent: true, prop: 'orderCount1', label: '', width: '1',  },//替换订单量汇总行不可点击
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'orderCount', label: '订单量', width: '100', formatter: (row) => !row.orderCount ? "0" : row.orderCount },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'payAmont', label: '付款金额', width: '100', formatter: (row) => !row.payAmont ? "0" : row.payAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleAmont', label: '销售金额', width: '120', formatter: (row) => !row.saleAmont ? "0" : row.saleAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmont', exportField: 'refundAmont', label: '退款', width: '120', tipmesg: '发货前退款', formatter: (row) => !row.refundAmont ? "0" : row.refundAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontRate', exportField: 'refundAmontRateStr', label: '退款率', width: '120', tipmesg: '发货前退款率', formatter: (row) => !row.refundAmontRate ? "0%" : row.refundAmontRate.toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleCost', label: '成本', width: '120', formatter: (row) => !row.saleCost ? " " : row.saleCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit1After', label: '运营毛一(减退款)', width: '80', formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1', label: '毛一利润(发生)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit11', label: '毛一利润(付款)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate', exportField: 'profit1RateStr', label: '毛一利润率(发生)', width: '80', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate1', label: '毛一利润率(付款)', width: '90', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv', label: '广告费', width: '100', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv_rate', exportField: 'alladv_rateStr', label: '广告占比', width: '80', formatter: (row) => (row.alladv_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit2After', label: '运营毛二(减退款)', width: '80', formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3After', label: '运营毛三(减退款)', width: '80', formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3', label: '毛三利润(发生)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit31', label: '毛三利润(付款)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3AfterRate', label: '毛3率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit3AfterRate ? " " : (row.yyProfit3AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate', exportField: 'profit3_rateStr', label: '毛三利润率(发生)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate1', label: '毛三利润率(付款)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4After', label: '运营毛四(减退款)', width: '80', formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5After', label: '运营毛五(减退款)', width: '80', formatter: (row) => !row.yyProfit5After ? " " : row.yyProfit5After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6After', label: '运营毛六(减退款)', permission: "profit6SixtyCents", width: '80', formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5', label: '毛五利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5 ? " " : row.profit5 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6', label: '毛六利润(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6 ? " " : row.profit6 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4AfterRate', label: '毛4率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit4AfterRate ? " " : (row.yyProfit4AfterRate).toString() +  "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5AfterRate', label: '毛5率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit5AfterRate ? " " : (row.yyProfit5AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6AfterRate', label: '毛6率(运营减退)', permission: "profit6SixtyCents", width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit6AfterRate ? " " : (row.yyProfit6AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate', exportField: 'profit33RateStr', label: '毛四利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate1', label: '毛四利润率(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5Rate', exportField: 'profit5Rate', label: '毛五利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6Rate', exportField: 'profit6Rate', label: '毛六利润率(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost', label: '出仓成本(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost1', label: '出仓成本(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '90', formatter: (row) => !row.warehouseSalary ? 0 : row.warehouseSalary },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCostRate', exportField: 'exitCostRateStr', label: '出仓成本占比', sortable: 'custom', width: '100', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + "%"},
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4', label: '净利润(发生)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit41', label: '净利润(付款)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate', exportField: 'profit4_rateStr', label: '净利率(发生)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate1', label: '净利率(付款)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'negativeExitProfitIdDetail', label: '出仓负利润', sortable: 'custom', width: '90', formatter: (row) => !row.negativeExitProfitIdDetail ? " " : row.negativeExitProfitIdDetail },
    { istrue: true, summaryEvent: true, display: true, prop: 'deductAmount', label: '违规扣款金额', width: '100', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

const tableCols4 = [
    { istrue: true, display: true, sortable: 'custom', prop: 'shopCode', exportField: 'shopName', label: '店铺', width: '60', formatter: (row) => row.shopName },
    { istrue: true, display: true, sortable: 'custom', prop: 'shopManager', label: '店铺负责人', width: '105', },
    { istrue: true, display: true, sortable: 'custom', prop: 'mainCategories', label: '公司类目', width: '80', },
    { istrue: true, display: true, sortable: 'custom', prop: 'groupId', exportField: 'groupName', label: '所在组', width: '70', formatter: (row) => row.groupName },
    { istrue: true, sortable: 'custom', prop: 'xiFenPlatform', label: '细分平台', width: '100', permission: "SegmentationPlatform" },
    { istrue: true, display: true, sortable: 'custom', prop: 'shopCreateTime', label: '店铺创建时间', width: '105', formatter: (row) => row.shopCreateTime },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'orderCount', label: '订单量', width: '80', formatter: (row) => !row.orderCount ? "0" : row.orderCount },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'payAmont', label: '付款金额', width: '80', formatter: (row) => !row.payAmont ? "0" : row.payAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleAmont', label: '销售金额', width: '80', formatter: (row) => !row.saleAmont ? "0" : row.saleAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmont', exportField: 'refundAmont', label: '退款', width: '80', formatter: (row) => !row.refundAmont ? "0" : row.refundAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontRate', exportField: 'refundAmontRateStr', label: '退款率', width: '80', formatter: (row) => !row.refundAmontRate ? "0%" : (row.refundAmontRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleCost', label: '成本', width: '80', formatter: (row) => !row.saleCost ? " " : row.saleCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit1After', label: '运营毛一(减退款)', width: '80', formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1', label: '毛一利润(发生)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit11', label: '毛一利润(付款)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate', exportField: 'profit1RateStr', label: '毛一利润率(发生)', width: '80', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate1', label: '毛一利润率(付款)', width: '90', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv', label: '广告费', width: '80', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv_rate', exportField: 'alladv_rateStr', label: '广告占比', width: '80', formatter: (row) => (row.alladv_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit2After', label: '运营毛二(减退款)', width: '80', formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3After', label: '运营毛三(减退款)', width: '80', formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3', label: '毛三利润(发生)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit31', label: '毛三利润(付款)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3AfterRate', label: '毛3率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit3AfterRate ? " " : (row.yyProfit3AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate', exportField: 'profit3_rateStr', label: '毛三利润率(发生)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate1', label: '毛三利润率(付款)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4After', label: '运营毛四(减退款)', width: '80', formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5After', label: '运营毛五(减退款)', width: '80', formatter: (row) => !row.yyProfit5After ? " " : row.yyProfit5After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6After', label: '运营毛六(减退款)', permission: "profit6SixtyCents", width: '80', formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5', label: '毛五利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5 ? " " : row.profit5 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6', label: '毛六利润(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6 ? " " : row.profit6 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4AfterRate', label: '毛4率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit4AfterRate ? " " : (row.yyProfit4AfterRate).toString() +  "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5AfterRate', label: '毛5率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit5AfterRate ? " " : (row.yyProfit5AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6AfterRate', label: '毛6率(运营减退)', permission: "profit6SixtyCents", width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit6AfterRate ? " " : (row.yyProfit6AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate', exportField: 'profit33RateStr', label: '毛四利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate1', label: '毛四利润率(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5Rate', exportField: 'profit5Rate', label: '毛五利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6Rate', exportField: 'profit6Rate', label: '毛六利润率(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost', label: '出仓成本(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost1', label: '出仓成本(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '90', formatter: (row) => !row.warehouseSalary ? 0 : row.warehouseSalary },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCostRate', exportField: 'exitCostRateStr', label: '出仓成本占比', sortable: 'custom', width: '100', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4', label: '净利润(发生)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit41', label: '净利润(付款)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate', exportField: 'profit4_rateStr', label: '净利率(发生)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate1', label: '净利率(付款)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'negativeExitProfitIdDetail', label: '出仓负利润', sortable: 'custom', width: '90', formatter: (row) => !row.negativeExitProfitIdDetail ? " " : row.negativeExitProfitIdDetail },
    { istrue: true, summaryEvent: true, display: true, prop: 'deductAmount', label: '违规扣款金额', width: '100', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

const tableCols8 = [
    { istrue: true, prop: 'superviseId', label: '运营主管', exportField: 'superviseName', width: '80', formatter: (row) => row.superviseName },//, type: 'click', handle: (that, row) => that.showSuperviseDetail(row)
    { istrue: true, sortable: 'custom', prop: 'xiFenPlatform', label: '细分平台', width: '100', permission: "SegmentationPlatform" },
    // { istrue: true, display: true, prop: 'superviseId', label: '运营主管', exportField: 'superviseName', width: '80', formatter: (row) => row.superviseName ? row.superviseName : '' },//, type: 'click', handle: (that, row) => that.showSuperviseDetail(row)
    // { istrue: true, summaryEvent: true, prop: 'orderCount1', label: '', width: '1',  },//替换订单量汇总行不可点击
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'orderCount', label: '订单量', width: '100', formatter: (row) => !row.orderCount ? "0" : row.orderCount },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'payAmont', label: '付款金额', width: '100', formatter: (row) => !row.payAmont ? "0" : row.payAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleAmont', label: '销售金额', width: '120', formatter: (row) => !row.saleAmont ? "0" : row.saleAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmont', exportField: 'refundAmont', label: '退款', width: '120', formatter: (row) => !row.refundAmont ? "0" : row.refundAmont },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontRate', exportField: 'refundAmontRateStr', label: '退款率', width: '120', formatter: (row) => !row.refundAmontRate ? "0%" : (row.refundAmontRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleCost', label: '成本', width: '120', formatter: (row) => !row.saleCost ? " " : row.saleCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit1After', label: '运营毛一(减退款)', width: '80', formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1', label: '毛一利润(发生)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit11', label: '毛一利润(付款)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate', exportField: 'profit1RateStr', label: '毛一利润率(发生)', width: '80', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate1', label: '毛一利润率(付款)', width: '90', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv', label: '广告费', width: '100', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv_rate', exportField: 'alladv_rateStr', label: '广告占比', width: '80', formatter: (row) => (row.alladv_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit2After', label: '运营毛二(减退款)', width: '80', formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3After', label: '运营毛三(减退款)', width: '80', formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3', label: '毛三利润(发生)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit31', label: '毛三利润(付款)', width: '120', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3AfterRate', label: '毛3率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit3AfterRate ? " " : (row.yyProfit3AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate', exportField: 'profit3_rateStr', label: '毛三利润率(发生)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate1', label: '毛三利润率(付款)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4After', label: '运营毛四(减退款)', width: '80', formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5After', label: '运营毛五(减退款)', width: '80', formatter: (row) => !row.yyProfit5After ? " " : row.yyProfit5After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6After', label: '运营毛六(减退款)', permission: "profit6SixtyCents", width: '80', formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5', label: '毛五利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5 ? " " : row.profit5 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6', label: '毛六利润(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6 ? " " : row.profit6 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4AfterRate', label: '毛4率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit4AfterRate ? " " : (row.yyProfit4AfterRate).toString() +  "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5AfterRate', label: '毛5率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit5AfterRate ? " " : (row.yyProfit5AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6AfterRate', label: '毛6率(运营减退)', permission: "profit6SixtyCents", width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit6AfterRate ? " " : (row.yyProfit6AfterRate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate', exportField: 'profit33RateStr', label: '毛四利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate1', label: '毛四利润率(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5Rate', exportField: 'profit5Rate', label: '毛五利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6Rate', exportField: 'profit6Rate', label: '毛六利润率(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost', label: '出仓成本(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost1', label: '出仓成本(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '90', formatter: (row) => !row.warehouseSalary ? 0 : row.warehouseSalary },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCostRate', exportField: 'exitCostRateStr', label: '出仓成本占比', sortable: 'custom', width: '100', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4', label: '净利润(发生)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit41', label: '净利润(付款)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate', exportField: 'profit4_rateStr', label: '净利率(发生)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate1', label: '净利率(付款)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
    { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'negativeExitProfitIdDetail', label: '出仓负利润', sortable: 'custom', width: '90', formatter: (row) => !row.negativeExitProfitIdDetail ? " " : row.negativeExitProfitIdDetail },
    { istrue: true, summaryEvent: true, display: true, prop: 'deductAmount', label: '违规扣款金额', width: '100', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
    { istrue: true, display: true, label: '趋势图', fixed: 'right', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

const tableHandles = [
];
export default {
    name: "Users",
    components: { container, MyConfirmButton, MySearch, MySearchWindow, cesTable, InputMult, freightDetail, financialReportStaticsByOneUser, buschar, vxetablebase },
    data() {
        return {
            outerVisible: false,
            childcompany: childcompany,
            dialogMapVisible: {
                visible: false,
                title: "",
                data: [],
                filter: {
                    timerange: []
                },
                params: {},
                type: 0//1趋势图汇总专员、助理 2汇总运营组、店铺  3助理 4专员 5运营组 6店铺
            },
            that: this,
            segmentationList: [],
            filter: {
                platform: 2,
                isMerge:null,
                shopCode: null,
                proCode: null,
                productName: null,
                groupId: null,
                groupIds: [],
                startTime: null,
                endTime: null,
                nickName: '',
                timerange: null,
                // 运营助理
                userId: null,
                // 车手
                userId2: null,
                // 备用
                userId3: null,
                // 运营专员 ID
                operateSpecialUserId: null,
                Profit3Lose: null,
                Profit33Lose: null,
                //分公司
                company: null,
                //付款维度
                refundType: 1,
                timerange2: [],
                onlineStatus: null,
                star: null,
                flag: null,
                timerangeOnTime: [],
                timerangeEntry: [],
                startTimeEntry: null,
                endTimeEntry: null,
                startTime3: null,
                endTime3: null,
                startTime2: null,
                endTime2: null,
                timerange2: [],
                shopManager: null,
                superviseIds: [],
                exportCompare:null,//导出设置传参,
                exportSelectProfitRate:null,//导出设置传参,
                exportisPositiveEndBeginProfit:null,//导出设置传参,
                exportdayCount: null,//导出设置传参,
                exportDynamicProfit: null,//导出设置传参,
                xiFenPlatformList: [],
            },
            onimportfilter: {
                yearmonthday: null,
            },
            //销售额	毛3利润	净利	广告费
            //成本	毛一利润	毛一利润率

            hahaha: 111,
            shopList: [],
            userList: [],
            grouplist: [],
            superviselist: [],
            directorlist: [],
            financialreportlist: [],
            visiblePersonal: false,

            showreportList: [],
            dialogSetParamZlVisible: false,//设置助理
            dialogSetParamZyVisible: false,//设置专员
            pageType: '',
            buttonStyle: ["info", "info", "info", "info", "info"],
            buttonStyle: Array(22).fill("info"),
            targetData: 0,
            targetData1: 0,
            userType: 1,
            targetType: 0,
            tableCols_1: tableCols1,
            tableCols_2: tableCols2,
            tableCols_21: tableCols21,
            tableCols_3: tableCols3,
            tableCols_4: tableCols4,
            tableCols_5: tableCols5,
            tableCols_6: tableCols6,
            tableCols_7: tableCols7,
            tableCols_8: tableCols8,
            tableHandles: tableHandles,
            total: 0,
            pager: { OrderBy: null, IsAsc: false, pageSize: 1000 },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            fileList: [],
            dialogVisible: false,
            uploadLoading: false,
            importFilte: {},
            fileList: [],
            fileparm: {},
            showprogress: true,
            editparmVisible: false,
            editLoading: false,
            editparmLoading: false,
            drawervisible: false,
            dialogDrVisible: false,
            drparamProCode: '',
            autoformparm: {
                fApi: {},
                options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
                rule: []
            },
            pickerOptions: {
                shortcuts: [{
                    text: '前一天',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
                        end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
                        picker.$emit('pick', [start, end]);
                        window.setshowprogress(false);
                    }
                }, {
                    text: '近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
                        picker.$emit('pick', [start, end]);
                        window.setshowprogress(false);
                    }
                }, {
                    text: '近一个月',
                    onClick(picker) {
                        const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate() - 1);
                        const date2 = new Date(); date2.setDate(date2.getDate() - 1);
                        picker.$emit('pick', [date1, date2]);
                        window.setshowprogress(false);
                    }
                }, {
                    text: '近三个月',
                    onClick(picker) {
                        const date1 = new Date(); date1.setMonth(date1.getMonth() - 3); date1.setDate(date1.getDate() - 1);
                        const date2 = new Date(); date2.setDate(date2.getDate() - 1);
                        picker.$emit('pick', [date1, date2]);
                        window.setshowprogress(true);
                    }
                }]
            },
            freightDetail: {
                visible: false,
                filter: {
                    proCode: null,
                    timeRange: []
                }
            },
            mainCategoriesList: mainCategoriesListConst,
        };
    },
    mounted() {
        this.init();
        this.onShowupMethod()
        let _this = this;
        if (localStorage.getItem("refundTypepdd")) {
            _this.refundTypeChange();
        }
        middlevue.$on('refundTypepdd', function (msg) {
            _this.filter.refundType = msg;
            _this.refundTypeChange();
        })
        middlevue.$on('timerangePdd', function (msg) {
            _this.filter.timerange = msg;
            // _this.filter.refundType = msg;
            _this.refundTypeChange();
        })
    },
    async created() {
        let res1 = await getDirectorGroupList();
        this.grouplist = res1.data?.map(item => { return { value: item.key, label: item.value }; });

        this.superviselist = [];
        let res2 = await getDirectorGroupList2();
        let sIds = res2.data?.filter(f => f.superviseId > 0).map(m => m.superviseId);
        let sIds2 = [...new Set(sIds)];
        if (sIds2.length > 0) {
            sIds2.forEach(f => {
                let cur = res2.data.find(x => x.id == f);
                if (cur) {
                    this.superviselist.push({ value: cur.id, label: cur.userName })
                }
            });
        }
    },
    methods: {
        //日期选择器赋值
        changeTime(e, val) {
          const [startKey, endKey] = val === 1 ? ['startTime', 'endTime'] : val === 2 ? ['startTime3', 'endTime3'] : val === 3 ? ['startTimeEntry', 'endTimeEntry'] : ['startTime2', 'endTime2'];
          this.filter[startKey] = e ? e[0] : null;
          this.filter[endKey] = e ? e[val === 1 ? 1 : 0] : null;
        },
        onShowupMethod(){
          let that = this;
          that.$nextTick(() => {
          const takePlace = ['profit1', 'profit1Rate', 'profit3', 'profit3_rate', 'profit33', 'profit33Rate', 'exitCost', 'profit4', 'profit4_rate'];
          const payment = ['profit11', 'profit1Rate1', 'profit31', 'profit3_rate1', 'profit331', 'profit33Rate1', 'exitCost1', 'profit41', 'profit4_rate1'];
              if (!that.visiblePersonal) {
                const tableIndex = that.userType;
                const tableRef = that.$refs[`table${tableIndex}`]; // 获取相应的表格引用
                if (tableRef) {
                  if (that.filter.refundType === 1) {
                    tableRef.changecolumn_setTrue(takePlace);
                    tableRef.changecolumn(payment);
                  } else {
                    tableRef.changecolumn(takePlace);
                    tableRef.changecolumn_setTrue(payment);
                  }
                }
              }
          });
        },
        //团队汇总
        onShowGroupSum(){
            this.filter.isMerge=1;
            this.queryReport()
            //查询完就清除
            this.filter.isMerge=null
        },
        //关闭导出设置则清空数据
        handleCancle(){
          this.filter.exportdayCount = null;
          this.filter.exportCompare = null;
          this.filter.exportSelectProfitRate = null;
          this.filter.exportDynamicProfit = null;this.filter.timerangeEntry
          this.filter.exportisPositiveEndBeginProfit = null;
        },
        handleEdit(e){
          e = String(e).replace(/^(0+)|(\.0+)$/g, '');//将输入的值转换为字符串，并删除开头的所有0和末尾的所有0
          //检查处理后的值是否为整数（包括负整数）
          if (/^-?\d+$/.test(e)) {
            //如果处理后的值不等于0，将其转换为整数并赋值给filter.exportDynamicProfit
            if (parseInt(e, 10) !== 0) {
              this.filter.exportDynamicProfit = parseInt(e, 10);
            } else {
              //如果处理后的值等于0，将其赋值为null
              this.filter.exportDynamicProfit = this.filter.exportDynamicProfit || null;
            }
          } else {
            this.filter.exportDynamicProfit = null;
          }
        },
        onClear() {
            this.filter.timerangeOnTime = [];
            this.filter.startTime3 = null;
            this.filter.endTime3 = null;
            this.filter.timerangeEntry = [];
            this.filter.startTimeEntry = null;
            this.filter.endTimeEntry = null;
            this.filter.shopManager = null;
            this.filter.mainCategories = null;
            this.filter.groupIds = [];
            this.filter.company = null;
            this.filter.IsCompete = null;
            this.filter.onlineStatus = null;
            this.filter.Profit3Lose = null;
            this.filter.Profit33Lose = null;
            this.filter.star = null;
            this.filter.flag = null;
            this.filter.timerange2 = [];
            this.filter.startTime2 = null;
            this.filter.startTime2 = null;
            this.filter.superviseIds = [];
        },
        async showchart(row) {
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1]
            }
            let startTime3 = null
            let endTime3 = null
            if(this.filter.timerangeOnTime && this.filter.timerangeOnTime.length>0){
                startTime3 = this.filter.timerangeOnTime[0]
                endTime3 = this.filter.timerangeOnTime[1]
            } else{
              startTime3 = null
              endTime3 = null
            }
            // let date1 = new Date();
            // date1.setMonth(date1.getMonth() - 1);
            // date1.setDate(date1.getDate() - 1);
            // let date2 = new Date();
            // date2.setDate(date2.getDate() - 1);

            let date1 = new Date(this.filter.timerange[1]);
            date1.setMonth(date1.getMonth() - 1);
            date1.setDate(date1.getDate() - 1);
            let date2 = new Date(this.filter.timerange[1]);

            let data3 = new Date(this.filter.timerange[0]).getTime();
            if (date1.getTime() > data3) {
                date1 = new Date(this.filter.timerange[0]);
            }

            this.dialogMapVisible.filter.timerange = [date1, date2];
            if (this.userType == 1) {
                var params = {
                    userId: row.userId, startTime: this.dialogMapVisible.filter.timerange[0],
                    endTime: this.dialogMapVisible.filter.timerange[1],
                    userType: this.userType, platform: this.filter.platform,
                    Profit3Lose: this.filter.Profit3Lose,
                    Profit33Lose: this.filter.Profit33Lose, RefundType: this.filter.refundType,
                    groupId: row.groupId,startTime3,endTime3,
                    star: this.filter.star, flag: this.filter.flag
                }
                let that = this;
                that.dialogMapVisible.type = 3;
                that.dialogMapVisible.params = params;
                const res = await getPerformanceStaticticsByUserMap(params).then(res => {
                    that.dialogMapVisible.visible = true;
                    that.dialogMapVisible.data = res.data
                    that.dialogMapVisible.title = res.data.legend[0]
                })

                this.dialogMapVisible.visible = true
            }
            else if (this.userType == 2 || this.userType == 21) {
                var params = {
                    operateSpecialUserId: row.operateSpecialUserId,
                    startTime: this.dialogMapVisible.filter.timerange[0],
                    endTime: this.dialogMapVisible.filter.timerange[1],
                    userType: this.userType, platform: this.filter.platform,
                    Profit3Lose: this.filter.Profit3Lose,
                    Profit33Lose: this.filter.Profit33Lose, RefundType: this.filter.refundType,
                    groupId: row.groupId,startTime3,endTime3,
                    star: this.filter.star, flag: this.filter.flag
                }
                let that = this;
                that.dialogMapVisible.type = 4;
                that.dialogMapVisible.params = params;
                const res = await getPerformanceStaticticsByUserMap(params).then(res => {
                    that.dialogMapVisible.visible = true;
                    that.dialogMapVisible.data = res.data
                    that.dialogMapVisible.title = res.data.legend[0]
                })
                this.dialogMapVisible.visible = true
            }
            else if (this.userType == 3) {
                if(this.buttonStyle[11]=='primary')
                {
                    this.filter.isMerge=1
                    this.filter.userId=row.userId
                }
                var params = {
                    groupId: row.groupId, startTime: this.dialogMapVisible.filter.timerange[0],
                    endTime: this.dialogMapVisible.filter.timerange[1], platform: this.filter.platform,
                    Profit3Lose: this.filter.Profit3Lose,
                    Profit33Lose: this.filter.Profit33Lose, Company: this.filter.company,
                    RefundType: this.filter.refundType,
                    groupId: row.groupId,startTime3,endTime3,
                    star: this.filter.star, flag: this.filter.flag,isMerge:this.filter.isMerge,userId:this.filter.userId
                }
                let that = this;
                that.dialogMapVisible.type = 5;
                that.dialogMapVisible.params = params;
                const res = await getPerformanceStaticticsByGroupMap(params).then(res => {
                    that.dialogMapVisible.visible = true;
                    that.dialogMapVisible.data = res.data
                    that.dialogMapVisible.title = res.data.legend[0]
                })
                this.dialogMapVisible.visible = true
            }
            else if (this.userType == 4) {
                var params = {
                    shopCode: row.shopCode, startTime: this.dialogMapVisible.filter.timerange[0],
                    endTime: this.dialogMapVisible.filter.timerange[1], userType: this.userType,
                    platform: this.filter.platform, Profit3Lose: this.filter.Profit3Lose,
                    Profit33Lose: this.filter.Profit33Lose,
                    Company: this.filter.company, RefundType: this.filter.refundType,
                    groupId: row.groupId,startTime3,endTime3,
                    star: this.filter.star, flag: this.filter.flag
                }
                let that = this;
                that.dialogMapVisible.type = 6;
                that.dialogMapVisible.params = params;
                const res = await getPerformanceStaticticsByShopMap(params).then(res => {
                    that.dialogMapVisible.visible = true;
                    that.dialogMapVisible.data = res.data
                    that.dialogMapVisible.title = res.data.legend[0]
                })
                this.dialogMapVisible.visible = true
            }
            else if (this.userType == 7) {
                this.userType = 7;
                var params = {
                    userId: row.userId, startTime: this.dialogMapVisible.filter.timerange[0],
                    endTime: this.dialogMapVisible.filter.timerange[1],
                    userType: this.userType, platform: this.filter.platform,
                    Profit3Lose: this.filter.Profit3Lose,
                    Profit33Lose: this.filter.Profit33Lose, RefundType: this.filter.refundType,
                    groupId: row.groupId,startTime3,endTime3,
                    star: this.filter.star, flag: this.filter.flag
                }
                let that = this;
                that.dialogMapVisible.type = 3;
                that.dialogMapVisible.params = params;
                const res = await getPerformanceStaticticsByUserMap(params).then(res => {
                    that.dialogMapVisible.visible = true;
                    that.dialogMapVisible.data = res.data
                    that.dialogMapVisible.title = res.data.legend[0]
                })

                this.dialogMapVisible.visible = true
            }
            else if (this.userType == 5 || row.isProductUseId == 1) {
                this.userType = 5;
                var params = {
                    userId: row.userId, startTime: this.dialogMapVisible.filter.timerange[0],
                    endTime: this.dialogMapVisible.filter.timerange[1],
                    userType: this.userType, platform: this.filter.platform,
                    Profit3Lose: this.filter.Profit3Lose,
                    Profit33Lose: this.filter.Profit33Lose, RefundType: this.filter.refundType,
                    groupId: row.groupId,startTime3,endTime3,
                    star: this.filter.star, flag: this.filter.flag,
                    groupIds:this.filter.groupIds
                }
                let that = this;
                that.dialogMapVisible.type = 3;
                that.dialogMapVisible.params = params;
                const res = await getPerformanceStaticticsByUserMap(params).then(res => {
                    that.dialogMapVisible.visible = true;
                    that.dialogMapVisible.data = res.data
                    that.dialogMapVisible.title = res.data.legend[0]
                })

                this.dialogMapVisible.visible = true
            }
            else if (this.userType == 6 || row.isProductUseId == 2) {
                this.userType = 6;
                var params = {
                    userId: row.userId, startTime: this.dialogMapVisible.filter.timerange[0],
                    endTime: this.dialogMapVisible.filter.timerange[1],
                    userType: this.userType, platform: this.filter.platform,
                    Profit3Lose: this.filter.Profit3Lose,
                    Profit33Lose: this.filter.Profit33Lose, RefundType: this.filter.refundType,
                    groupId: row.groupId,startTime3,endTime3,
                    star: this.filter.star, flag: this.filter.flag,
                    groupIds:this.filter.groupIds
                }
                let that = this;
                that.dialogMapVisible.type = 3;
                that.dialogMapVisible.params = params;
                const res = await getPerformanceStaticticsByUserMap(params).then(res => {
                    that.dialogMapVisible.visible = true;
                    that.dialogMapVisible.data = res.data
                    that.dialogMapVisible.title = res.data.legend[0]
                })

                this.dialogMapVisible.visible = true
            }
            else if (this.userType == 8) {
                let params = {
                    superviseIds: !row.superviseId ? [-1] : [row.superviseId],
                    startTime: this.dialogMapVisible.filter.timerange[0],
                    endTime: this.dialogMapVisible.filter.timerange[1],
                    platform: this.filter.platform,
                    refundType: this.filter.refundType,
                    profit3Lose: this.filter.Profit3Lose,
                    profit33Lose: this.filter.Profit33Lose,
                    groupId: row.groupId,startTime3,endTime3,
                }
                if (this.filter.timerangeOnTime&&this.filter.timerangeOnTime.length>0) {
                    params.startTime3 = this.filter.timerangeOnTime[0];
                    params.endTime3 = this.filter.timerangeOnTime[1];
                }
                let that = this;
                that.dialogMapVisible.type = 8;
                that.dialogMapVisible.params = params;
                const res = await getPerformanceStaticticsByonSuperviseMap(params).then(res => {
                    that.dialogMapVisible.visible = true;
                    that.dialogMapVisible.data = res.data
                    that.dialogMapVisible.title = res.data.legend[0]
                })
                this.dialogMapVisible.visible = true
            }
        },
        datetostr(date) {
            var y = date.getFullYear();
            var m = ("0" + (date.getMonth() + 1)).slice(-2);
            var d = ("0" + date.getDate()).slice(-2);
            return y + "-" + m + "-" + d;
        },
        async changeDate() {
            this.queryReport();
        },
        async init() {
            var date1 = new Date();
            //date1.setMonth(date1.getMonth() - 3);
            date1.setDate(date1.getDate() - 1);
            var date2 = new Date(); date2.setDate(date2.getDate() - 1);
            this.$set(this.buttonStyle, 0, 'primary');
            this.$set(this.buttonStyle, 1, 'info');
            this.$set(this.buttonStyle, 4, 'info');
            this.$set(this.buttonStyle, 5, 'info');
            this.$set(this.buttonStyle, 7, 'info');
            this.$set(this.buttonStyle, 8, 'info');
            this.$set(this.buttonStyle, 9, 'info');
            this.$set(this.buttonStyle, 10, 'info');
            this.$set(this.buttonStyle, 11, 'info');
            this.$set(this.buttonStyle, 21, 'info');

            this.filter.timerange = [];
            this.filter.timerange[0] = this.datetostr(date1);
            this.filter.timerange[1] = this.datetostr(date2);
            var that = this;
            this.userType = 1;
            this.pageType = "运营助理"
            if (this.targetType == 0 && checkPermission('PddPerformance_PddPerformanceProfit3')) {
                this.targetType = 1
            } else if (this.targetType == 0 && checkPermission('PddPerformance_PddPerformanceNetProfit')) {
                this.targetType = 2
            } else if (this.targetType == 0 && checkPermission('PddPerformance_PddPerformanceOutProfit')) {
                this.targetType = 3
            }
            this.targetData = (await getPerformanceTarget({ targetName: '运营助理毛三目标' })).data;
            this.targetData1 = (await getPerformanceTarget({ targetName: '运营助理净利目标' })).data;
            window.setshowprogress = function (status) {
                that.showprogress = status;
            }
            const { data: data2 } = await getAllShopXiFenPlatform();
            this.segmentationList = data2;

            this.changeDate();
        },
        async onShowZl() {
            this.targetData = (await getPerformanceTarget({ targetName: '运营助理毛三目标' })).data;
            this.targetData1 = (await getPerformanceTarget({ targetName: '运营助理净利目标' })).data;

            this.pageType = "运营助理"
            this.userType = 1;
            this.pager.OrderBy = null;
            this.onShowupMethod()
            this.queryReport()
        },
        async onShowDj() {
            this.targetData = (await getPerformanceTarget({ targetName: '运营助理毛三目标' })).data;
            this.targetData1 = (await getPerformanceTarget({ targetName: '运营助理净利目标' })).data;
            this.pageType = "运营带教"
            this.userType = 21;
            this.pager.OrderBy = null;
            this.onShowupMethod()
            this.queryReport()
        },
        async onShowZy() {
            this.targetData = (await getPerformanceTarget({ targetName: '运营专员毛三目标' })).data;
            this.targetData1 = (await getPerformanceTarget({ targetName: '运营专员净利目标' })).data;

            this.pageType = "运营专员"
            this.userType = 2;
            this.pager.OrderBy = null;
            this.onShowupMethod()
            this.queryReport()
        },

        async onShowGp() {
            this.targetData = (await getPerformanceTarget({ targetName: '运营专员毛三目标' })).data;
            this.targetData1 = (await getPerformanceTarget({ targetName: '运营专员净利目标' })).data;

            this.pageType = "运营组"
            this.userType = 3;
            this.pager.OrderBy = null;
            this.onShowupMethod()
            this.queryReport()
        },

        async onShowSupervise() {
            this.targetData = (await getPerformanceTarget({ targetName: '运营专员毛三目标' })).data;
            this.targetData1 = (await getPerformanceTarget({ targetName: '运营专员净利目标' })).data;

            this.pageType = "运营主管"
            this.userType = 8;
            this.pager.OrderBy = null;
            this.onShowupMethod()
            this.queryReport()
        },
        async onShowShop() {
            this.targetData = (await getPerformanceTarget({ targetName: '运营专员毛三目标' })).data;
            this.targetData1 = (await getPerformanceTarget({ targetName: '运营专员净利目标' })).data;

            this.pageType = "店铺"
            this.userType = 4;
            this.pager.OrderBy = null;
            this.onShowupMethod()
            this.queryReport()
        },
        async onShowProductZl() {
            this.targetData = (await getPerformanceTarget({ targetName: '产品专员毛三目标' })).data;
            this.targetData1 = (await getPerformanceTarget({ targetName: '产品专员净利目标' })).data;

            this.pageType = "产品助理"
            this.userType = 5;
            this.pager.OrderBy = null;
            this.onShowupMethod()
            this.queryReport()
        },
        async onShowProductZy() {
            this.targetData = (await getPerformanceTarget({ targetName: '产品助理毛三目标' })).data;
            this.targetData1 = (await getPerformanceTarget({ targetName: '产品助理净利目标' })).data;

            this.pageType = "产品专员"
            this.userType = 6;
            this.pager.OrderBy = null;
            this.onShowupMethod()
            this.queryReport()
        },
        async onShowDepartment() {
            this.targetData = (await getPerformanceTarget({ targetName: '运营专员毛三目标' })).data;
            this.targetData1 = (await getPerformanceTarget({ targetName: '运营专员净利目标' })).data;
            this.pageType = "部门"
            this.userType = 7;
            this.pager.OrderBy = null;
            this.onShowupMethod()
            this.queryReport()
        },

        async getShopList() {
            const { data } = await getAllShopList({ platforms: [this.filter.platform] });
            this.shopList = data.filter((item) => item.shopManager !== null).map(
                (item) => ({ label: item.shopManager, value: item.shopCode })
            );
        },
        async queryReport() {
            if (this.targetType == 0) {
                return;
            }
            this.getShopList();
            this.visiblePersonal = false;
            startLoading();
            this.showreportList = [];
            this.total = 0;
            this.summaryarry = {};
            this.filter.startTime = null;
            this.filter.endTime = null;
            this.filter.startTime3 = null;
            this.filter.endTime3 = null;
            this.filter.startTimeEntry = null;
            this.filter.endTimeEntry = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            if (this.filter.timerangeOnTime) {
                this.filter.startTime3 = this.filter.timerangeOnTime[0];
                this.filter.endTime3 = this.filter.timerangeOnTime[1];
            }
            if (this.filter.timerangeEntry) {
                this.filter.startTimeEntry = this.filter.timerangeEntry[0];
                this.filter.endTimeEntry = this.filter.timerangeEntry[1];
            }
            if (!this.filter.groupId) {
                this.filter.groupId = null
            }
            var that = this;
            var pager = this.pager;
            this.filter.startTime2 = null;
            this.filter.endTime2 = null;
            if (this.userType == 1) {
                this.$set(this.buttonStyle, 0, 'primary');
                this.$set(this.buttonStyle, 1, 'info');
                this.$set(this.buttonStyle, 4, 'info');
                this.$set(this.buttonStyle, 5, 'info');
                this.$set(this.buttonStyle, 7, 'info');
                this.$set(this.buttonStyle, 8, 'info');
                this.$set(this.buttonStyle, 9, 'info');
                this.$set(this.buttonStyle, 10, 'info');
            }
            if (this.userType == 2) {
                this.$set(this.buttonStyle, 1, 'primary');
                this.$set(this.buttonStyle, 0, 'info');
                this.$set(this.buttonStyle, 4, 'info');
                this.$set(this.buttonStyle, 5, 'info');
                this.$set(this.buttonStyle, 6, 'info');
                this.$set(this.buttonStyle, 7, 'info');
                this.$set(this.buttonStyle, 8, 'info');
                this.$set(this.buttonStyle, 10, 'info');
                this.$set(this.buttonStyle, 21, 'info');
            }
            if (this.userType == 21){
                this.$set(this.buttonStyle, 1, 'info');
                this.$set(this.buttonStyle, 0, 'info');
                this.$set(this.buttonStyle, 4, 'info');
                this.$set(this.buttonStyle, 5, 'info');
                this.$set(this.buttonStyle, 6, 'info');
                this.$set(this.buttonStyle, 7, 'info');
                this.$set(this.buttonStyle, 8, 'info');
                this.$set(this.buttonStyle, 10, 'info');
                this.$set(this.buttonStyle, 21, 'primary');
            }
            if (this.userType == 3) {
                this.$set(this.buttonStyle, 4, 'primary');
                this.$set(this.buttonStyle, 0, 'info');
                this.$set(this.buttonStyle, 1, 'info');
                this.$set(this.buttonStyle, 5, 'info');
                this.$set(this.buttonStyle, 7, 'info');
                this.$set(this.buttonStyle, 8, 'info');
                this.$set(this.buttonStyle, 9, 'info');
                this.$set(this.buttonStyle, 10, 'info');
                this.$set(this.buttonStyle, 11, 'info');
                this.$set(this.buttonStyle, 21, 'info');
                if(this.filter.isMerge==1)
                {
                    this.$set(this.buttonStyle, 11, 'primary');
                }
            }
            if (this.userType == 4) {
                this.$set(this.buttonStyle, 5, 'primary');
                this.$set(this.buttonStyle, 0, 'info');
                this.$set(this.buttonStyle, 1, 'info');
                this.$set(this.buttonStyle, 4, 'info');
                this.$set(this.buttonStyle, 7, 'info');
                this.$set(this.buttonStyle, 8, 'info');
                this.$set(this.buttonStyle, 9, 'info');
                this.$set(this.buttonStyle, 10, 'info');
                this.$set(this.buttonStyle, 21, 'info');

                if (this.filter.timerange2) {
                    this.filter.startTime2 = this.filter.timerange2[0];
                    this.filter.endTime2 = this.filter.timerange2[1];
                }
            }
            if (this.userType == 5) {
                this.$set(this.buttonStyle, 7, 'primary');
                this.$set(this.buttonStyle, 0, 'info');
                this.$set(this.buttonStyle, 1, 'info');
                this.$set(this.buttonStyle, 5, 'info');
                this.$set(this.buttonStyle, 4, 'info');
                this.$set(this.buttonStyle, 8, 'info');
                this.$set(this.buttonStyle, 9, 'info');
                this.$set(this.buttonStyle, 10, 'info');
                this.$set(this.buttonStyle, 21, 'info');

                if (this.filter.timerange2) {
                    this.filter.startTime2 = this.filter.timerange2[0];
                    this.filter.endTime2 = this.filter.timerange2[1];
                }
            }
            if (this.userType == 6) {
                this.$set(this.buttonStyle, 8, 'primary');
                this.$set(this.buttonStyle, 0, 'info');
                this.$set(this.buttonStyle, 1, 'info');
                this.$set(this.buttonStyle, 2, 'info');
                this.$set(this.buttonStyle, 5, 'info');
                this.$set(this.buttonStyle, 4, 'info');
                this.$set(this.buttonStyle, 6, 'info');
                this.$set(this.buttonStyle, 7, 'info');
                this.$set(this.buttonStyle, 9, 'info');
                this.$set(this.buttonStyle, 10, 'info');
                this.$set(this.buttonStyle, 21, 'info');

                if (this.filter.timerange2) {
                    this.filter.startTime2 = this.filter.timerange2[0];
                    this.filter.endTime2 = this.filter.timerange2[1];
                }
            }
            if (this.userType == 7) {
                this.$set(this.buttonStyle, 9, 'primary');
                this.$set(this.buttonStyle, 0, 'info');
                this.$set(this.buttonStyle, 1, 'info');
                this.$set(this.buttonStyle, 2, 'info');
                this.$set(this.buttonStyle, 3, 'info');
                this.$set(this.buttonStyle, 4, 'info');
                this.$set(this.buttonStyle, 5, 'info');
                this.$set(this.buttonStyle, 6, 'info');
                this.$set(this.buttonStyle, 7, 'info');
                this.$set(this.buttonStyle, 8, 'info');
                this.$set(this.buttonStyle, 10, 'info');
                this.$set(this.buttonStyle, 21, 'info');

                if (this.filter.timerange2) {
                    this.filter.startTime2 = this.filter.timerange2[0];
                    this.filter.endTime2 = this.filter.timerange2[1];
                }
            }
            if (this.userType == 8) {
                this.$set(this.buttonStyle, 10, 'primary');
                this.$set(this.buttonStyle, 0, 'info');
                this.$set(this.buttonStyle, 1, 'info');
                this.$set(this.buttonStyle, 4, 'info');
                this.$set(this.buttonStyle, 5, 'info');
                this.$set(this.buttonStyle, 7, 'info');
                this.$set(this.buttonStyle, 8, 'info');
                this.$set(this.buttonStyle, 9, 'info');
                this.$set(this.buttonStyle, 21, 'info');
            }


            if (this.targetType == 1) {
                if (pager.OrderBy == null) {
                    pager.OrderBy = "profit3"
                    this.pager.OrderBy = "profit3"
                }
                this.$set(this.buttonStyle, 2, 'primary');
                this.$set(this.buttonStyle, 3, 'info');
                this.$set(this.buttonStyle, 6, 'info');
                this.filter.Profit3Lose = this.filter.Profit3Lose
                this.filter.Profit33Lose = this.filter.Profit33Lose
            }
            else if (this.targetType == 3) {
                if (pager.OrderBy == null) {
                    pager.OrderBy = "profit3"
                    this.pager.OrderBy = "profit3"
                }
                this.filter.Profit3Lose = this.filter.Profit3Lose
                this.filter.Profit33Lose = this.filter.Profit33Lose
                this.$set(this.buttonStyle, 6, 'primary');
                this.$set(this.buttonStyle, 3, 'info');
                this.$set(this.buttonStyle, 2, 'info');
            }
            else if (this.targetType == 2) {
                if (pager.OrderBy == null) {
                    pager.OrderBy = "profit4"
                    this.pager.OrderBy = "profit4"
                }
                this.$set(this.buttonStyle, 3, 'primary');
                this.$set(this.buttonStyle, 2, 'info');
                this.$set(this.buttonStyle, 6, 'info');
                this.filter.Profit3Lose = this.filter.Profit3Lose
                this.filter.Profit33Lose = this.filter.Profit33Lose
            }

            const params = { ...pager, ...this.pager, ...this.filter };
            params.UserType = this.userType
            var data = [];
            if (this.userType == 3)
                data = (await getPerformanceStaticticsByUser(params));
            else if (this.userType == 4)
                data = (await getPerformanceStaticticsByUser(params));
            else
                data = (await getPerformanceStaticticsByUser(params));

            loading.close();

            if (data.code == 403) {

                this.showreportList = [];
                return;
            }





            for (var i in data.data.list) {
                if (this.targetType == 1) {

                    if (data.data.list[i].profit3 > 0 && this.targetData != 0)
                        data.data.list[i].progress = (data.data.list[i].profit3 * 100 / this.targetData);
                    else
                        data.data.list[i].progress = 0;
                }
                else if (this.targetType == 2) {

                    if (data.data.list[i].profit4 > 0 && this.targetData1 != 0)
                        data.data.list[i].progress = (data.data.list[i].profit4 * 100 / this.targetData1);
                    else {
                        data.data.list[i].progress = 0;
                    }
                }

            }
            this.showreportList = data.data.list;
            if(this.userType == 4 && !this.visiblePersonal){
              console.log(this.showreportList,'this.showreportList');
            }
            that.total = data.data?.total;
            that.summaryarry = data.data?.summary;
              for (const key in that.summaryarry) {
                that.summaryarry.profit4_rate1_sum = that.summaryarry.profit4_rate_sum;
                that.summaryarry.profit41_sum = that.summaryarry.profit4_sum;
                that.summaryarry.exitCost1_sum = that.summaryarry.exitCost_sum;
                that.summaryarry.profit331_sum = that.summaryarry.profit33_sum;
                that.summaryarry.profit33Rate1_sum = that.summaryarry.profit33Rate_sum;
                that.summaryarry.profit31_sum = that.summaryarry.profit3_sum;
                that.summaryarry.profit1Rate1_sum = that.summaryarry.profit1Rate_sum;
                that.summaryarry.profit11_sum = that.summaryarry.profit1_sum;
                that.summaryarry.profit3_rate1_sum = that.summaryarry.profit3_rate_sum;
              }
            that.summaryarry.alladv_rate_sum =  that.summaryarry.alladv_rate_sum.toString() + "%"
            that.summaryarry.profit3_rate_sum = that.summaryarry.profit3_rate_sum.toString() + "%"
            that.summaryarry.profit4_rate_sum = that.summaryarry.profit4_rate_sum.toString() + "%"
            that.summaryarry.profit1Rate_sum = that.summaryarry.profit1Rate_sum.toString() + "%"
            that.summaryarry.profit1Rate1_sum = that.summaryarry.profit1Rate1_sum.toString() + "%"
            that.summaryarry.profit3_rate1_sum = that.summaryarry.profit3_rate1_sum.toString() + "%"
            that.summaryarry.profit33Rate_sum = that.summaryarry.profit33Rate_sum.toString() + "%"
            that.summaryarry.refundAmontRate_sum = that.summaryarry.refundAmontRate_sum.toString() + "%"


        },
        async onShowGroup() {
            const count = this.financialreportlist.filter(item => item.status === '0').length; // 6
        },
        async showprchart2(prcode) {
            window['lastseeprcodedrchart'] = prcode
            this.drparamProCode = prcode
            this.dialogDrVisible = true
        },

        async changegroup() {

            this.queryReport();
        },

        async saveZl() {

            SetPerformanceTarget({ targetName: '运营助理毛三目标', targetData: this.targetData });
            SetPerformanceTarget({ targetName: '运营助理净利目标', targetData: this.targetData1 });
            this.dialogSetParamZlVisible = false;

        },
        async saveZy() {
            SetPerformanceTarget({ targetName: '运营专员毛三目标', targetData: this.targetData });
            SetPerformanceTarget({ targetName: '运营专员净利目标', targetData: this.targetData1 });
            this.dialogSetParamZyVisible = false;
        },
        async editZlparam() {
            this.targetData = (await getPerformanceTarget({ targetName: '运营助理毛三目标' })).data;
            this.targetData1 = (await getPerformanceTarget({ targetName: '运营助理净利目标' })).data;
            this.dialogSetParamZlVisible = true;
        },
        async editZyparam() {
            this.targetData = (await getPerformanceTarget({ targetName: '运营专员毛三目标' })).data;
            this.targetData1 = (await getPerformanceTarget({ targetName: '运营专员净利目标' })).data;
            this.dialogSetParamZyVisible = true;
        },
        async onShowTarget(ttype) {
            this.targetType = ttype;
            this.queryReport()
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async canclick(row, column, cell) {
            this.filter.startTime3 = null;
            this.filter.endTime3 = null;
            this.filter.startTime = null;
            this.filter.endTime = null;
            this.visiblePersonal = true;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            if (this.filter.timerangeOnTime) {
                this.filter.startTime3 = this.filter.timerangeOnTime[0] ? this.filter.timerangeOnTime[0] : null;
                this.filter.endTime3 = this.filter.timerangeOnTime[1] ? this.filter.timerangeOnTime[1] : null;
            }
            this.filter.Profit3Lose = this.filter.Profit3Lose
            this.filter.Profit33Lose = this.filter.Profit33Lose
            window.setFinancialFilterTime(this.filter.startTime, this.filter.endTime, row.isProductUseId, row.userId, row.operateSpecialUserId, row.groupId, this.filter.refundType, this.targetType == 3 ? 1 : 0, this.filter.star, this.filter.flag, this.filter.startTime3, this.filter.endTime3, this.filter.Profit3Lose, this.filter.Profit33Lose, this.userType);
        },
        //汇总趋势图
        async onsummaryClick(property) {
            startLoading();
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            if (!this.filter.groupId) {
                this.filter.groupId = null
            }
            var that = this;
            var pager = this.pager;



            const params = { ...pager, ...this.pager, ...this.filter };
            params.UserType = this.userType;
            params.column = this.mapColumnProp(property);
            // let date1 = new Date();
            // date1.setMonth(date1.getMonth() - 1);
            // date1.setDate(date1.getDate() - 1);
            // let date2 = new Date();
            // date2.setDate(date2.getDate() - 1);

            let date1 = new Date(this.filter.timerange[1]);
            date1.setMonth(date1.getMonth() - 1);
            date1.setDate(date1.getDate() - 1);
            let date2 = new Date(this.filter.timerange[1]);

            let data3 = new Date(this.filter.timerange[0]).getTime();
            if (date1.getTime() > data3) {
                date1 = new Date(this.filter.timerange[0]);
            }

            this.dialogMapVisible.filter.timerange = [date1, date2];
            if (this.dialogMapVisible.filter.timerange) {
                params.startTime = this.dialogMapVisible.filter.timerange[0];
                params.endTime = this.dialogMapVisible.filter.timerange[1];
            }
            if (this.pageType == '运营助理' || this.pageType == '运营专员' || this.pageType == '产品专员' || this.pageType == '产品助理' || this.pageType == '部门' || this.pageType == '运营带教') {
                await getPerformanceStaticticsByUserAnalysis(params).then(res => {
                    that.dialogMapVisible.type = 1;
                    that.dialogMapVisible.params = params;
                    that.dialogMapVisible.visible = true;
                    that.dialogMapVisible.data = res.data
                    that.dialogMapVisible.title = res.data.legend[0]
                });
            }
            else if (this.pageType == '运营组' || this.pageType == '店铺') {
                await getPerformanceStaticticsByGroupAnalysis(params).then(res => {
                    that.dialogMapVisible.type = 2;
                    that.dialogMapVisible.params = params;
                    that.dialogMapVisible.visible = true;
                    that.dialogMapVisible.data = res.data
                    that.dialogMapVisible.title = res.data.legend[0]
                });
            }
            else if (this.pageType == '运营主管') {
                if (this.filter.timerangeOnTime) {
                    params.startTime3 = this.filter.timerangeOnTime[0];
                    params.endTime3 = this.filter.timerangeOnTime[1];
                }
                let that = this;
                that.dialogMapVisible.type = 8;
                that.dialogMapVisible.params = params;
                const res = await getPerformanceStaticticsByonSuperviseMap(params).then(res => {
                    that.dialogMapVisible.visible = true;
                    that.dialogMapVisible.data = res.data
                    that.dialogMapVisible.title = res.data.legend[0]
                })
                this.dialogMapVisible.visible = true
            }

            loading.close();
        },

       async onExportClick(){

        this.outerVisible=true;

       },
        //导出
        async onExport() {
            if(this.filter.exportDynamicProfit == 0){
              this.$message.error('百分比输入条件不能为0!');
              return;
            }
            if (this.filter.exportDynamicProfit || this.filter.exportSelectProfitRate || this.filter.exportCompare || this.filter.exportdayCount) {
              if (!this.filter.exportDynamicProfit || !this.filter.exportSelectProfitRate || !this.filter.exportCompare || !this.filter.exportdayCount) {
                this.$message.error('所有筛选条件必填(结束时间起点可空)!!!!!!!!');
                return;
              }
            }

            if (this.filter.exportisPositiveEndBeginProfit) {
              if (!this.filter.exportDynamicProfit || !this.filter.exportSelectProfitRate || !this.filter.exportCompare || !this.filter.exportdayCount) {
                this.$message.error('所有筛选条件必填(结束时间起点可空)!!!!!!!!');
                return;
              }
            }

            if (this.targetType == 0) {
                return;
            }
            this.listLoading = true;
            this.outerVisible = false;
            let pager = this.pager;
            if (this.targetType == 1) {
                if (pager.OrderBy == null) {
                    pager.OrderBy = "profit3"
                    this.pager.OrderBy = "profit3"
                }
            }

            else if (this.targetType == 3) {
                if (pager.OrderBy == null) {
                    pager.OrderBy = "profit3"
                    this.pager.OrderBy = "profit3"
                }
                this.filter.Profit3Lose = this.filter.Profit3Lose
                this.filter.Profit33Lose = this.filter.Profit33Lose
            }

            else if (this.targetType == 2) {
                if (pager.OrderBy == null) {
                    pager.OrderBy = "profit4"
                    this.pager.OrderBy = "profit4"
                }
            }
            let exportCompares = this.filter.exportCompare == 1 ? true : this.filter.exportCompare == 2 ? false: null;
            if(this.buttonStyle[11]=='primary')
            {
                    this.filter.isMerge=1
           }
            const params = { "targetType": this.targetType, ...pager, ...this.pager, ...this.filter };
            params.exportCompare = exportCompares
            params.UserType = this.userType
              let exportCnColumns = [];
              let exportColumns = [];
              const processTableColumns = (tableCols) => {
              tableCols.forEach(item => {
                  if (item.label && item.prop) {
                    const updatedLabel = this.filter.refundType == 2
                        ? (item.label.includes('(发生)') ? item.label.replace('(发生)', '(付款)')
                          : item.label.includes('(付款)') ? item.label.replace('(付款)', '(发生)')
                          : item.label)
                        : item.label;
                    exportCnColumns.push(updatedLabel);
                    exportColumns.push(item.exportField ? item.exportField : item.prop);
                  }
                });
              };

              if (this.userType == 3) {
                  processTableColumns(this.tableCols_3);
              } else if (this.userType == 1) {
                  processTableColumns(this.tableCols_1);
              } else if (this.userType == 2) {
                  processTableColumns(this.tableCols_2);
              } else if (this.userType == 4) {
                  processTableColumns(this.tableCols_4);
              } else if (this.userType == 5) {
                  processTableColumns(this.tableCols_5);
              } else if (this.userType == 6) {
                  processTableColumns(this.tableCols_6);
              } else if (this.userType == 7) {
                  processTableColumns(this.tableCols_7);
              }  else if (this.userType == 8) {
                  processTableColumns(this.tableCols_8);
              }  else if (this.userType == 21) {
                  processTableColumns(this.tableCols_21);
              }
              params.YH_EXT_ExportCnColumns = exportCnColumns;
              params.YH_EXT_ExportColumns = exportColumns;
            let res;
            let name = "";
            if (this.userType == 3) {

                res = (await exportPerformanceStaticticsByUser(params));
                name = "运营组";
            }
            else if (this.userType == 4) {
                res = (await exportPerformanceStaticticsByUser(params));
                name = "店铺";
            }
            else if (this.userType == 8) {
                res = (await exportPerformanceStaticticsByUser(params));
                name = "运营主管";
            }
            else {
                if (this.userType == 1) {
                    name = "运营助理";
                } else if(this.userType == 2) {
                    name = "运营专员";
                } else if (this.userType == 21){
                    name = "运营带教";
                } else if(this.userType == 5) {
                    name = "产品助理";
                } else if(this.userType == 6) {
                    name = "产品专员";
                } else if(this.userType == 7) {
                    name = "部门";
                } else {
                    name = "拼系运营人员业绩导出";
                }
                res = (await exportPerformanceStaticticsByUser(params));
            }
            if (!res?.data) return
            this.filter.exportDynamicProfit = null;
            this.filter.exportCompare = null;
            this.filter.exportSelectProfitRate = null;
            this.filter.exportisPositiveEndBeginProfit = null;
            this.filter.exportdayCount = null;
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '拼系人员业绩统计_' + name + '_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
            this.listLoading = false;
        },
        async refundTypeChange() {
            this.onShowupMethod();
            this.queryReport();
        },
        mapColumnProp(prop) {
            const propMapping = {
                'profit4_rate1': 'profit4_rate',
                'profit41': 'profit4',
                'profit11': 'profit1',
                'profit1Rate1': 'profit1Rate',
                'profit31': 'profit3',
                'profit3_rate1': 'profit3_rate',
                'profit331': 'profit33',
                'profit33Rate1': 'profit33Rate',
                'exitCost1': 'exitCost',
            };
            return propMapping[prop] || prop; // 返回映射的属性，或者原属性
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                column.prop = this.mapColumnProp(column.prop),
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            }
            this.queryReport();
        },
        async similarityDateChange() {
            if (this.dialogMapVisible.filter.timerange) {
                this.dialogMapVisible.filter.startTime = this.dialogMapVisible.filter.timerange[0];
                this.dialogMapVisible.filter.endTime = this.dialogMapVisible.filter.timerange[1];
            }
            let params = { ...this.dialogMapVisible.params, ...this.dialogMapVisible.filter }
            let that = this;
            if (this.dialogMapVisible.type == 1) {
                await getPerformanceStaticticsByUserAnalysis(params).then(res => {
                    that.dialogMapVisible.data = res.data
                    that.dialogMapVisible.title = res.data.legend[0]
                });
            } else if (this.dialogMapVisible.type == 2) {
                await getPerformanceStaticticsByGroupAnalysis(params).then(res => {
                    that.dialogMapVisible.data = res.data
                    that.dialogMapVisible.title = res.data.legend[0]
                });
            } else if (this.dialogMapVisible.type == 3 || this.dialogMapVisible.type == 4) {
                await getPerformanceStaticticsByUserMap(params).then(res => {
                    that.dialogMapVisible.data = res.data
                    that.dialogMapVisible.title = res.data.legend[0]
                })
            } else if (this.dialogMapVisible.type == 5) {
                await getPerformanceStaticticsByGroupMap(params).then(res => {
                    that.dialogMapVisible.data = res.data
                    that.dialogMapVisible.title = res.data.legend[0]
                })
            } else if (this.dialogMapVisible.type == 6) {
                await getPerformanceStaticticsByShopMap(params).then(res => {
                    that.dialogMapVisible.data = res.data
                    that.dialogMapVisible.title = res.data.legend[0]
                })
            }
            await this.$refs.dialogMapVisibleBuschar.initcharts()
        }
    }
};
</script>
<style>
.el-progress-bar__innerText {
    padding-right: 100%;
}
</style>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
::v-deep .pass_input input::-webkit-outer-spin-button,
::v-deep .pass_input input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

::v-deep .pass_input input[type="number"] {
  appearance: textfield;
  -moz-appearance: textfield;
}
</style>
