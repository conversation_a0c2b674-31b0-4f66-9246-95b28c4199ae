<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable
                    class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="exportProps">导出</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :showsummary='true' :summaryarry='summaryarry' :isSelectColumn="false" :toolbarshow="false"
            style="width: 100%; height:680px; margin: 0" v-loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pageGetNoScan, exportNoScan } from '@/api/vo/prePackScan'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'wmsName', label: '仓库', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'goodsName', label: '商品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'qty', label: '数量', },
]
export default {
    name: "exceptionCoding",
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                goodsCode: null
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            summaryarry: null
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async exportProps() {
            const { data } = await exportNoScan(this.ListInfo)
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/zip" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '异常编码' + new Date().toLocaleString() + '.zip')
            aLink.click()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            const { data: { list, total, summary }, success } = await pageGetNoScan(this.ListInfo)
            if (success) {
                this.tableData = list
                this.total = total
                this.summaryarry = summary
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>