 <template>
  <div style="height: 100%;">
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='true'
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry'
              :loading="listLoading">
        <template slot='extentbtn'>
          <el-button-group>
            <el-button type="text" size="medium" disabled style="color: red;">周转A:</el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter1.turnovera" placeholder="请选择">
                  <el-option v-for="item in turnoverlist" :key="item.value" :label="item.label" :value="item.value"/>
             </el-select>
            </el-button>
            <el-button type="text" size="medium" disabled style="color: red;">周转B:</el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter1.turnoverb" placeholder="请选择">
                  <el-option v-for="item in turnoverlist" :key="item.value" :label="item.label" :value="item.value"/>
             </el-select>
            </el-button>
            <el-button type="text" size="medium" disabled style="color: red;">周转对比:</el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input-number v-model="filter1.startdiff"></el-input-number>至<el-input-number v-model="filter1.enddiff"></el-input-number>
            </el-button>
            <el-button type="primary" @click="onfresh">刷新</el-button>
            <el-button type="primary" @click="onpk">分析</el-button>
            <el-button type="primary" @click="onExport">导出</el-button>
          </el-button-group>
        </template>
     </ces-table>
     <div height style="padding:0px 0px 5px 5px;">
          <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
       </div>
  </div>
</template>
<script>
import {pageWarehouseRecord,exportWarehouseRecord} from '@/api/inventory/warehouse'
import myContainer from '@/components/my-container/noheader'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/tablev2.vue";
import {formatTime,formatYesornoBool,formatmoney} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'date',label:'日期',  width:'100',sortable:'custom',formatter:(row)=>formatTime(row.date,'YYYY-MM-DD')},
      {istrue:true,prop:'groupName',label:'运营组', width:'100',},
      {istrue:true,prop:'brandName',label:'采购员', width:'130',},
      {istrue:true,prop:'proBianMa',label:'编码', width:'150',sortable:'custom'},
      {istrue:true,prop:'proBianMaName',label:'编码名称', width:'220',sortable:'custom'},
      {istrue:true,prop:'endNum',label:'期末数量', width:'105',sortable:'custom',formatter:(row)=>formatmoney(row.endNum)},
      {istrue:true,prop:'endAmont',label:'期末金额', width:'105',sortable:'custom',formatter:(row)=>formatmoney(row.endAmont)},
      {istrue:true,prop:'price',label:'成本', width:'80',sortable:'custom',formatter:(row)=>formatmoney(row.price)},
      {istrue:true,prop:'saleCount',label:'销量', width:'100',sortable:'custom',formatter:(row)=>formatmoney(row.saleCount)},
      {istrue:true,prop:'turnovera',label:'周转A', width:'100',sortable:'custom'},
      {istrue:true,prop:'turnoverb',label:'周转B', width:'100',sortable:'custom'},
      {istrue:true,prop:'turnoverDiff',label:'周转对比', width:'120',sortable:'custom'},
      {istrue:true,prop:'turnoverRate1',label:'1天销量平均周转天数', width:'105',sortable:'custom'},
      {istrue:true,prop:'turnoverRate3',label:'3天销量平均周转天数', width:'105',sortable:'custom'},
      {istrue:true,prop:'turnoverRate7',label:'7天销量平均周转天数', width:'105',sortable:'custom'},
      {istrue:true,prop:'turnoverRate15',label:'15天销量平均周转天数', width:'105',sortable:'custom'},
      {istrue:true,prop:'turnoverRate30',label:'30天销量平均周转天数', width:'105',sortable:'custom'},
      {istrue:true,prop:'turnoverRate45',label:'45天销量平均周转天数', width:'105',sortable:'custom'},
     ];
const tableHandles=[
        // {label:"导出", handle:(that)=>that.onExport()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, myContainer, MyConfirmButton },
   props:{
       filter: {}
     },
  data() {
    return {
      that:this,
      filter1: {turnovera:1, turnoverb:3,startdiff:undefined,enddiff:undefined},
      turnoverlist:[
        {value:1,label:"1天销量平均周转天数"},
        {value:3,label:"3天销量平均周转天数"},
        {value:7,label:"7天销量平均周转天数"},
        {value:15,label:"15天销量平均周转天数"},
        {value:30,label:"30天销量平均周转天数"},
        {value:45,label:"45天销量平均周转天数"}
      ],
      list: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"id",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [], 
      listLoading: false,
      pageLoading: false
    }
  },
  mounted() {
     this.onSearch()
  },
  beforeUpdate() {
   // console.log('update')
  },
  methods: {
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async onfresh() {
    if (this.filter1.turnoverb<=this.filter1.turnovera) {
      this.$message({type: 'warning',message: '周转B一定要比周转A大!'});
      return;
    }
     this.getlist()
    },
    async getlist() {
      var hasparm=false;
      var arry= Object.keys(this.filter)
      if (arry.length==0)  return;

      for (let key of Object.keys(this.filter)) {
        if(this.filter[key])
            hasparm=true;
      }
      if(!hasparm) return;
      if (!this.pager.OrderBy) this.pager.OrderBy="id";
      var pager = this.$refs.pager.getPager()
      const params = {
        ...pager,
        ...this.pager,
        ... this.filter,
        ... this.filter1
      }
      this.listLoading = true
      const res = await pageWarehouseRecord(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
   async onpk(){
    if (this.selids.length==0) {
        this.$message({message: "请先选择！",type: "warning",});
        return;
      }
      this.$emit('onpkbianma',this.selids);
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   async onbacthDelete() {
      if (this.selids.length==0) {
       this.$message({type: 'warning',message: '请先选择!'});
       return;
      } 
      this.$confirm('确认删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
            const res = await batchDeleteExpressFreightAP({ids:this.selids.join()})
            if (!res?.success) {return }
            this.$message({type: 'success',message: '删除成功!'});
            this.getlist()
        }).catch(() => {
          this.$message({type: 'info',message: '已取消删除'});
        });
    },
    selsChange: function(sels) {
      this.sels = sels
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.proBianMa);
      })
    },
  async onExport() {
    if (this.filter1.turnoverb<=this.filter1.turnovera) {
      this.$message({type: 'warning',message: '周转B一定要比周转A大!'});
      return;
    }
      var hasparm=false;
      var arry= Object.keys(this.filter)
      if (arry.length==0)  return;

      for (let key of Object.keys(this.filter)) {
        if(this.filter[key])
            hasparm=true;
      }
      if(!hasparm) return;
      if (!this.pager.OrderBy) this.pager.OrderBy="id";
      const params = {
        ...this.pager,
        ... this.filter,
        ... this.filter1
      }
       
      var res= await exportWarehouseRecord(params);
      if(!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download','编码数据导出_' + new Date().toLocaleString() + '.xlsx' )
      aLink.click()
     }
  }
}
</script>
