<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <!-- <el-date-picker v-model="ListInfo.fatEffectMonth" type="month" value-format="yyyy-MM" placeholder="日期"
          class="publicCss" /> -->
        <!-- <el-date-picker v-model="ListInfo.calculateMonth" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" type="month" style="width: 250px;margin-right: 5px;" :clearable="false"
          :value-format="'yyyy-MM'">
        </el-date-picker> -->
        <el-date-picker v-model="ListInfo.calculateMonthArr" unlink-panels range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期"   type="monthrange" style="width: 250px;margin-right: 5px;" :clearable="false"
            :value-format="'yyyy-MM'" >
          </el-date-picker>
        <el-select v-model="ListInfo.type" placeholder="类型" class="publicCss" clearable @change="changeType">
          <el-option v-for="item in typeList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="ListInfo.regionName" @change="changeRegionName" placeholder="区域" class="publicCss" clearable>
          <el-option v-for="item in districtList" :key="item" :label="item" :value="item" />
        </el-select>
        <!-- <el-input v-model.trim="ListInfo.name" placeholder="招聘专员" maxlength="50" clearable class="publicCss" /> -->
        <el-select v-model="ListInfo.nameArr" placeholder="招聘专员" class="publicCss" clearable filterable collapse-tags multiple>
          <el-option v-for="item in districtNameList.filter(item => item != '小计')" :key="item" :label="item" :value="item" />
        </el-select>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <!-- <el-button type="primary" @click="startImport">导入</el-button> -->
        <el-button type="primary" @click="startImport" v-if="checkPermission('ArchiveStatusEditing')">导入</el-button>
        <el-button type="primary" @click="startImport" v-else :disabled="lastUpdateTime">导入</el-button>
        <el-button type="primary" @click="downExcel">模板下载</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
        <el-button type="primary" @click="saveBane('search')">存档</el-button>
        <div style="display: flex;align-items: center;margin: 0 10px;font-size: 15px;color: red;">
          存档时间: {{ lastUpdateTime ?? '-' }}
        </div>
      </div>
    </template>
    <vxetablebase :id="'recruitmentDimensionality202412161330'" border :tablekey="'recruitmentDimensionality202412161330'"
      ref="table" :that='that' :somerow="somerow" :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :footerDataArray='summaryarry' :showsummary='true'  style="width: 100%;  margin: 0" :loading="loading"
      :height="'100%'" :cellClassName="cellClassName" :mergeColumn="mergeColumn">
      <template slot="right">
        <vxe-column title="操作" width="80" fixed="right">
          <template #default="{ row }">
            <div style="display: flex;justify-content: center;width: 100%;">
              <!-- <el-button type="text" @click="handleEdit(row)" :disabled="row.status == '1'">编辑</el-button> -->
              <!-- <el-button type="text" size="mini" v-if="checkPermission('ArchiveStatusEditing')" @click="handleEdit(row)">编辑</el-button> -->
              <el-button type="text" size="mini" v-if="row.name.indexOf('小计')==-1" @click="handleEdit(row)">编辑</el-button>
              <el-button type="text"  size="mini" v-if="row.name.indexOf('小计')==-1"  @click="handleDelete(row.id)">删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-drawer title="编辑" :visible.sync="dialogVisibleEdit" size="25%">
      <recruitmentEdit ref="recruitmentEdit" v-if="dialogVisibleEdit" :editInfo="editInfo" :typeList="typeList"
        @cancellationMethod="cancellationMethod" />
    </el-drawer>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/views/profit/sscManager/ePeoplefx/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import recruitmentEdit from "./recruitmentEdit.vue";
import { downloadLink } from "@/utils/tools.js";
import checkPermission from '@/utils/permission'
import { recruitDirectorPage, recruitDirectorArchive, recruitDirectorExport, recruitDirectorImport, recruitDirectorRemove,
    recruitDirectorListValue } from '@/api/people/peoplessc.js';
import dayjs from 'dayjs'
const tableCols = [
  { width: '100', align: 'center', prop: 'calculateMonth', label: '月份', },
  { width: '100', align: 'center', prop: 'type', label: '类型', },
  { width: '100', align: 'center', prop: 'regionName', label: '区域', },
  { width: '100', align: 'center', prop: 'name', label: '招聘人员', },
  {
        istrue: true, prop: '', label: `招聘软件数据(仓库选填)`, merge: true, prop: 'mergeField1',
        cols: [
            { width: '100', align: 'center', prop: 'viewed', label: '被查看', },
            { width: '100', align: 'center', prop: 'saw', label: '我看过', },
            { width: '100', align: 'center', prop: 'expertNewGreeting', label: '牛人新招呼', },
            { width: '100', align: 'center', prop: 'myCommunication', label: '我沟通', },

            { width: '100', align: 'center', prop: 'phoneCount', label: '获取简历/联系方式', },
            { width: '100', align: 'center', prop: 'inviteCount', label: '邀约人数', },
            { width: '100', align: 'center', prop: 'interviewAccept', label: '接受面试', },

        ]
  },
  {
        istrue: true, prop: '', label: `线下面试数据(全区域填报)`, merge: true, prop: 'mergeField2',
        cols: [
        { width: '100', align: 'center', prop: 'interviewAcceptCount', label: '到面人数', },
        { width: '100', align: 'center', prop: 'interviewAcceptRate', label: '到面转换率', formatter: (row) => row.interviewAcceptRate ? row.interviewAcceptRate + '%' : '0%' },
        { width: '100', align: 'center', prop: 'interviewPassCount', label: '复试通过人数', },
        { width: '100', align: 'center', prop: 'interviewPassRate', label: '复试通过率', formatter: (row) => row.interviewPassRate ? row.interviewPassRate + '%' : '0%' },
        { width: '100', align: 'center', prop: 'newHiresCount', label: '入职人数', },
        { width: '100', align: 'center', prop: 'newHiresRate', label: '入职率', formatter: (row) => row.newHiresRate ? row.newHiresRate + '%' : '0%' },
        { width: '100', align: 'center', prop: 'resignationsCount', label: '离职人数', },
        { width: '100', align: 'center', prop: 'retainCount', label: '留存人数', },
        { width: '100', align: 'center', prop: 'retainRate', label: '留存率', formatter: (row) => row.retainRate ? row.retainRate + '%' : '0%' },

        ]
  },
  {
        istrue: true, prop: '', label: `招聘排名`, merge: true, prop: 'mergeField2',
        cols: [
        { width: '100', align: 'center', prop: 'entryRank', label: '入职排名', },
        { width: '100', align: 'center', prop: 'dailyAverage', label: '日均人效', formatter: (row) => row.dailyAverage },
        { width: '100', align: 'center', prop: 'dailyAverageRank', label: '人效排名', },
        ]
  },

//   { width: '100', align: 'center', prop: 'invitationRank', label: '邀约排名', },
//   { width: '100', align: 'center', prop: 'lossRate', label: '流失率', formatter: (row) => row.lossRate ? row.lossRate + '%' : "" },
]
export default {
  name: "recruitmentDimensionality",
  components: {
    MyContainer, vxetablebase, recruitmentEdit
  },
  data() {
    return {
      someColumn: 'name,regionName,type,calculateMonth',
      somerow: 'regionName,type,calculateMonth',
      mergeColumn: {
        column: ['calculateMonth', 'type', 'regionName', 'name'], // 需要合并的列
        default: 'type' // 默认显示字段，如果为空则显示第一个列的值
      },
      downloadLink,
      editInfo: {},
      dialogVisibleEdit: false,
      lastUpdateTime: '',
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      districtList: ['南昌','武汉','深圳','义乌','义乌仓','南昌仓','西安仓','1688选品中心'],
      allDistrictList: ['南昌','武汉','深圳','义乌','义乌仓','南昌仓','西安仓','1688选品中心'], // 存储总数据
      districtNameList: [],
      typeList: [],
      that: this,
      ListInfo: {
        // calculateMonth: dayjs().subtract(0, 'month').format('YYYY-MM'),
        calculateMonthArr: [dayjs().subtract(0, 'month').format('YYYY-MM'), dayjs().subtract(0, 'month').format('YYYY-MM')],
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        name: ''
        // fatEffectMonth: dayjs().format('YYYY-MM'),
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    changeType(e){
      if (!e) {
        this.districtList = [...this.allDistrictList];
        return;
      }
      if (e === '办公室') {
        this.districtList = this.allDistrictList.filter(item => !item.includes('仓'));
      } else {
        this.districtList = this.allDistrictList.filter(item => item.includes('仓'));
      }
    },
    cellClassName(val){
        if(val.row.name.indexOf("小计")>-1){
            return 'coloryellow'
        }
    },
    async changeRegionName(val){
        this.ListInfo.name = '';
        let res = await recruitDirectorListValue({
            fieldName: 'name',
            regionName: val
        })
        if(!res.success){
            return;
        }
        this.districtNameList = res.data;
    },
    downExcel() {
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250712/1943850651964907521.xlsx', '招聘数据汇总导入模板.xlsx');
    },
    async saveBane() {
      this.$confirm('是否存档？存档后不可修改！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { data, success } = await recruitDirectorArchive(this.ListInfo)
        if (!success) {
          return;
        }
        this.getList();
        this.$message.success('保存存档成功！')

      }).catch(() => {
        // this.$message.error('取消')
      });
    },
    cancellationMethod(val) {
      this.dialogVisibleEdit = false
      if (val == 1) {
        this.getList('search')
      }
    },
    handleEdit(row) {
      this.editInfo = JSON.parse(JSON.stringify(row))
      this.dialogVisibleEdit = true
    },
    async handleDelete(id) {
        this.$confirm('是否删除！', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.loading = true
          const {data, success} = await recruitDirectorRemove(id)
          this.loading = false
          if (success) {
            this.$message.success('删除成功')
            this.getList();
          } else {
            this.$message.error('删除失败')
          }
        }).catch(() => {

        });


      },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("file", item.file);
      form.append("isArchive", checkPermission("ArchiveStatusEditing"));
      var res = await recruitDirectorImport(form);
      if (res?.success) {
        this.$message({ message: res.msg, type: "success" });
      }
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async exportProps() {
      this.$nextTick(()=>{
        this.$refs.table.exportDataMethod({filename:'招聘数据汇总'+ new Date().toLocaleString(),    sheetName: 'Sheet1',type: 'xlsx' })
      })
      return
      this.loading = true
      const data = await recruitDirectorExport(this.ListInfo)
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '招聘数据汇总' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.ListInfo.calculateMonthArr && this.ListInfo.calculateMonthArr.length > 0) {
            this.ListInfo.calculateMonthStart = this.ListInfo.calculateMonthArr[0]
            this.ListInfo.calculateMonthEnd = this.ListInfo.calculateMonthArr[1]
        }
        this.ListInfo.name =  this.ListInfo.nameArr.join(',');
      this.loading = true
      const { data, success } = await recruitDirectorPage(this.ListInfo)
      if (success) {
        this.tableData = data.list
        //取列表中的区域
        const newDistricts = this.tableData.map(item => item.regionName).filter(district => district !== undefined && district !== null)
        this.allDistrictList = Array.from(new Set([...this.allDistrictList, ...newDistricts]));
        if (!this.ListInfo.type) {
          this.districtList = [...this.allDistrictList];
        }
        const newDistrictsName = this.tableData.map(item => item.name).filter(district => district !== undefined && district !== null)
        this.districtNameList = Array.from(new Set([...this.districtNameList, ...newDistrictsName]));
        const newTypes = this.tableData.map(item => item.type).filter(district => district !== undefined && district !== null)
        this.typeList = Array.from(new Set([...this.typeList, ...newTypes]));
          this.total = data.total
        if (data.summary) {
            data.summary.dailyAverage_sum = data.summary['dailyAverage_sum']? Number(data.summary['dailyAverage_sum']).toFixed(1) +' ': data.summary.dailyAverage_sum

        }
        this.summaryarry = data.summary
        const keysToUpdate = ['retainRate', 'newHiresRate', 'interviewPassRate', 'interviewAcceptRate'];
        if (this.summaryarry) {
          // 检查 summaryarry 是数组还是对象
          if (Array.isArray(this.summaryarry)) {
            // 如果是数组，遍历每个元素
            this.summaryarry.forEach(item => {
              keysToUpdate.forEach(key => {
                if (item[key] !== null && item[key] !== undefined) {
                  item[key] = item[key] + '%';
                }
              });
            });
          } else {
            // 如果是对象，直接处理
            keysToUpdate.forEach(key => {
              if (this.summaryarry[key] !== null && this.summaryarry[key] !== undefined) {
                this.summaryarry[key] = this.summaryarry[key] + '%';
              }
            });
          }
        }


        // this.lastUpdateTime = data.list.length > 0 ? data.list[0].archiveTime : ''

        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

:deep(.vxe-header--column){
    background: #00937e;
    color: white;
    font-weight: 600;
}
:deep(.vxe-footer--row){
    background: #00937e;
    color: white;
    font-weight: 600;
}

:deep(.coloryellow){
    background: #fff2cc;
    color: black;
    font-weight: 600;
}

</style>
