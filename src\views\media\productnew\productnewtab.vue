<template>
    <my-container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="上架时间">
                    <el-date-picker style="width: 200px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                        range-separator="至" start-placeholder="开始" end-placeholder="结束" clearable :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>    
                <el-form-item label="商品咨询时间">
                    <el-date-picker style="width: 200px" v-model="Filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                        range-separator="至" start-placeholder="开始" end-placeholder="结束" clearable :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>          
                <el-form-item label="宝贝ID:">
                    <el-input v-model="filter.procode" style="width: 150px" placeholder="宝贝ID" @keyup.enter.native="onSearch" clearable/>
                </el-form-item>
                <el-form-item label="上新模式:">
                <el-select v-model="filter.newPattern" placeholder="请选择" clearable :collapse-tags="true" filterable style="width: 100px">
                    <el-option label="未知" value="未知" />
                    <el-option v-for="item in productnewList" :key="item.state" :label="item.state" :value="item.state" />
                </el-select>
                </el-form-item>
                <el-form-item label="平台:">
                    <el-select filterable v-model="filter.platform" placeholder="请选择平台" @change="onchangeplatform" clearable style="width: 130px">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
                    </el-select>
                </el-form-item>
                <el-form-item label="所属店铺:">
                    <el-select filterable v-model="filter.shopId" placeholder="请选择店铺" clearable style="width: 130px">
                        <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.id"/>
                    </el-select>
                </el-form-item>
                <el-form-item label="组长:">
                <el-select filterable v-model="filter.groupId" placeholder="请选择组长" style="width: 100px" clearable>
                    <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key"/>
                </el-select>
                </el-form-item>
                <el-form-item label="运营专员:">
                <el-select filterable v-model="filter.operateSpecialId" placeholder="请选择负责人" clearable style="width: 100px">
                    <el-option label="所有" value=""/>
                    <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key"/>
                </el-select>
                </el-form-item>
                <el-form-item label="运营助理:">
                <el-select filterable v-model="filter.user1Id"  placeholder="请选择负责人" clearable style="width: 100px">
                    <el-option label="所有" value=""/>
                    <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key"/>
                </el-select>
                </el-form-item>
                <el-form-item label="备用:">
                    <el-select filterable v-model="filter.user3Id" placeholder="请选择负责人" clearable style="width: 100px">
                        <el-option label="所有" value=""/>
                        <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key"/>
                    </el-select>
                </el-form-item>
                <!-- <el-form-item label="客服组:">
                <el-select v-model="filter.customer" placeholder="请选择" clearable :collapse-tags="true" filterable style="width: 100px">
                    <el-option label="未知" value="未知" />
                    <el-option v-for="item in cusgroupslist" :key="item.state" :label="item.state" :value="item.state" />
                </el-select>
                </el-form-item> -->
                <el-form-item>
                <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>

        <el-tabs v-model="activeName" style="height: 93%;">
          <el-tab-pane label="新品监控" name="first" style="height:100%;">
            <productnew :filter="filter" :filterDetail="Filter" ref="productnew" style="height:100%"></productnew>
        </el-tab-pane>
        <el-tab-pane label="新品监控图片看板" name="second" style="height:100%;">
            <productnewimg :filter="filter" ref="productnewimg" style="height:100%;"></productnewimg>
        </el-tab-pane>
      </el-tabs>

    </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import { getDirectorList, getDirectorGroupList,getProductBrandPageList,getList as getshopList } from '@/api/operatemanage/base/shop'
import { platformlist} from '@/utils/tools'
import {getAllProBrand} from '@/api/inventory/warehouse'
import {pageProductNewAsync, queryGuardProductNewsis, getProductStateName, getProductAdveClick} from '@/api/operatemanage/base/product'
import {getcusgroups,} from "@/api/customerservice/customergroup";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import productnew from "./productnew.vue";
import productnewimg from "./productnewimg.vue"

const startTime = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");
const star = formatTime(dayjs().subtract(7,'day'), "YYYY-MM-DD");
export default {
    name: 'YunhanAdminProductnewtab',
    components : {MyContainer, MyConfirmButton, MySearch, MySearchWindow, productnew, productnewimg},

    data() {
        return {
            that:this,
            activeName: 'first',    
            filter: {
                startTime: null,
                endTime: null,
                timerange:[startTime, endTime],
                procode:null,
                title:null,
                platform:null,
                shopCode:null,
                groupId:null,
                operateSpecialId:null,
                user1Id:null,
                user3Id:null,
                shopId:null,
                platform:null,
                newPattern:null,
                customer:null,
            },
            detailfilter: {
                procode: null,
                platform:null,
                startTime: null,
                endTime: null,
                timerange:[star, endTime]
            },
            Filter:{
                StartDate:null,
                EndDate:null,
                timerange:[startTime, endTime]
            },
            personpager: {
                OrderBy: "rensuccess",
                pageSize: 200,
                pageIndex: 1,
                IsAsc: false,
            },
            platformlist:platformlist,
            platformList: [],
            grouplist: [],    
            brandlist: [],
            directorList:[], 
            productnewList:[],
            cusgroupslist:[],
            shopList:[],
            directorGroupList:[],
            opList:[],
            total: 0,
            sels: [], 
            selids: [], 
            pickerOptions: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                    picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近半个月',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                    picker.$emit('pick', [start, end]);
                    }
                },{
                    text: '最近一个月',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                    picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                    picker.$emit('pick', [start, end]);
                    }
                }]
            },
        };
    },

    async mounted() {
        await this.onSearch()
        await this.getDirectorlist()
        await this.init()
        await this.getPruductNewState()
        await this.getProductCustomer()
    },

    methods: {
        async getPruductNewState() {
            var res = await getProductStateName();
            if (res?.code){
                this.productnewList = res.data.map(function (item) {
                var ob = new Object();
                ob.state = item;
                return ob;
                })
            } 
        },
        async getProductCustomer() {
            var g = await getcusgroups({});

            this.cusgroupslist = g.data.list.map(function (item) {
                var ob = new Object();
                ob.state = item;
                return ob;
            });
        },
        async getDirectorlist() {
            const res1 = await getDirectorList({})
            const res2 = await getDirectorGroupList({})     
            const res3 = await getProductBrandPageList()
            
            this.directorList = res1.data
            this.directorGroupList =[{key:'0',value:'未知'}].concat(res2.data ||[]);
            this.bandList = res3.data?.list
        },
        async onchangeplatform(val){
            this.categorylist =[]
            const res1 = await getshopList({platform:val,CurrentPage:1,PageSize:300});
            this.shopList=res1.data.list
        },
        async init(){
            var res= await getAllProBrand();
            this.brandlist = res.data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        async onSearch(){
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange&&this.filter.timerange.length>1) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            if (this.Filter.timerange) {
                this.Filter.StartDate = this.Filter.timerange[0];
                this.Filter.EndDate = this.Filter.timerange[1];
            }
            
            this.$nextTick(async () =>{
                
                if(this.activeName == 'first')
                    await this.$refs.productnew.onSearch()
                else if (this.activeName == 'second')
                    await this.$refs.productnewimg.onSearch()
            })
        }
    },
};
</script>

<style lang="scss" scoped>

</style>