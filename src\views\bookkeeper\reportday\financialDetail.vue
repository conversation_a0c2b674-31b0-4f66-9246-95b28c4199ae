<template>
  <container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="时间:">
          <!-- <el-date-picker style="width: 130px" v-model="filter.yearMonthDay" type="date" format="yyyyMMdd"   value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker> -->
          <el-date-picker style="width: 260px" v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item label="财务流水号:">
          <el-input v-model="filter.serialNumberFinacial" placeholder="财务流水号"></el-input>
        </el-form-item>
        <el-form-item label="订单编号:">
          <el-input v-model="filter.originOrderNo" placeholder="订单编号"></el-input>
        </el-form-item>
        <el-form-item label="所属店铺:">
          <el-select filterable v-model="filter.shopCode" placeholder="请选择" class="el-select-content" style="width:150px">
            <el-option key="所有" label="所有" value></el-option>
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onstartImport" v-if="false">导入</el-button>
        </el-form-item>
      </el-form>
    </template>
    <el-tabs v-model="activeName" style="height: 94%;">
      <el-tab-pane label="订单收支" name="first" style="height: 100%;">
        <financialcustomer :filter="filter" :tablekey='tablekey1' ref="financialcustomer" />
      </el-tab-pane>
      <el-tab-pane label="订单扣点" name="second" style="height: 100%;">
        <financialdeduction :filter="filter" :tablekey='tablekey2' ref="financialdeduction" />
      </el-tab-pane>
    </el-tabs>
    <el-dialog title="导入" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
      <span>
        <el-row>
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <!--            
           <el-date-picker style="width: 100%" v-model="onimportfilter.shopCode" type="date" format="yyyyMMdd" value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
           -->
            <el-select filterable v-model="onimportfilter.shopCode" placeholder="请选择店铺" class="el-select-content"
              style="width:150px">
              <el-option key="所有" label="所有" value></el-option>
              <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
            <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
              accept=".xlsx,.csv" :http-request="uploadFile" :file-list="fileList" :data="fileparm">
              <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
            </el-upload>
          </el-col>
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </container>
</template>

<script>
import { getAllListInCalc as getAllShopList } from '@/api/operatemanage/base/shop';
import { importFinacialZFBDay } from '@/api/bookkeeper/import'
import financialcustomer from '@/views/bookkeeper/reportday/financialcustomer'
import financialdeduction from '@/views/bookkeeper/reportday/financialdeduction'
import container from '@/components/my-container/nofooter'
export default {
  name: 'Roles',
  components: { container, financialcustomer, financialdeduction },
  data() {
    return {
      activeName: 'first',
      filter: {
        shopCode: null,
        proCode: null,
        productName: null,
        groupId: null,
        startDate: null,
        endDate: null,
        timerange: null,
      },
      onimportfilter: { shopCode: null },
      fileparm: {},
      fileList: [],
      pageLoading: false,
      dialogVisible: false,
      uploadLoading: false,
      shopList: [],
      tablekey1: "tablekey1",
      tablekey2: "tablekey2",
    }
  },
  beforeUpdate() {
    console.log('update')
  },
  async mounted() {
    await this.getShopList();
  },
  methods: {
    onSearch() {
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      if (this.filter.timerange1) {
        this.filter.startImpotTime = this.filter.timerange1[0];
        this.filter.endImpotTime = this.filter.timerange1[1];
      }
      if (this.activeName == 'first') this.$refs.financialcustomer.onSearch();
      else if (this.activeName == 'second') this.$refs.financialdeduction.onSearch();
    },
    async getShopList() {
      const res1 = await getAllShopList();
      this.shopList = [];
      res1.data?.forEach(f => {
        if (f.isOpen == 1 && f.shopCode)
          this.shopList.push(f);
      });
    },
    async onstartImport() {
      this.dialogVisible = true;
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      //onimportfilter.shopCode
      if (!this.onimportfilter.shopCode) {
        this.$message({ type: 'warning', message: '请选择店铺!' });
        return;
      }
      this.uploadLoading = true
      this.$refs.upload.submit();
    },
    async uploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("shopCode", this.onimportfilter.shopCode);
      var res = await importFinacialZFBDay(form);
      if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
      else this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading = false
    },
  }
}
</script>
