<template>
    <my-container>
        <template #header>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles'
            :isSelectColumn="true" :loading="listLoading" @cellclick="cellclick">
            <el-table-column v-if="checkPermission('productnewpermission')" :width="500" label="表格" fixed="right">
                <template slot="header" slot-scope="scope">
                    <div class="table-div" ref="tableColumn">

                        <el-link v-for="(todo, index) in searchColumn" :underline="false" v-bind:class="{
                            ascending: (index == searchIndex && searchOrder == 'ascending'),
                            descending: (index == searchIndex && searchOrder == 'descending')
                        }"
                            @click="tableColumnClick(todo.value, index)">{{ todo.text }}
                            <span class="caret-wrapper">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                        </el-link>

                    </div>
                </template>
                <template slot-scope="props">
                    <el-table :data="props.row.series" :show-header="false" border style="width: 100%">
                        <el-table-column prop="name" label="姓名" width="80"></el-table-column>
                        <el-table-column label="7天数据" width="auto">
                            <template slot-scope="scope">
                                <el-col :span="3" v-for="(ii, num) in scope.row.data" :key="num">
                                    <template>
                                        <div v-if="scope.row['name'] == '广告访客量'" @click="clicknum(scope.row, ii)"
                                            style="color: red;">{{ ii }}</div>
                                        <div v-else> {{ ii }} </div>
                                    </template>
                                </el-col>
                                <!-- <span v-for="(ii,num) in scope.row.data" :key="num">
                                    <span v-if="num <= 2" >{{ii}}</span>
                                    <el-button :span="3" type="text" size="mini" style="padding: 8px;" @click="clicknum(ii)">{{ii}}</el-button>
                                </span> -->

                            </template>
                        </el-table-column>
                    </el-table>
                </template>
            </el-table-column>
            <el-table-column v-if="checkPermission('productnewpermission')" :width="180" label="图表" fixed="right">
                <template slot-scope="scope">
                    <div style="height: 120px;width:100%;margin-left: -20px;" :ref="'echarts' + scope.row.proCode"
                        v-loading="echartsLoading"></div>
                </template>
            </el-table-column>
            <!-- <el-table-column width="auto" label="操作" fixed="right">
                <template slot-scope="scope">
                    <el-button type="text" @click="showperson(scope.row)">按人统计</el-button><br>
                    <el-button type="text" @click="addtrainplan(scope.row)">添加培训计划</el-button><br>
                    <el-button type="text" @click="manageresource(scope.row)">管理培训资料</el-button><br>
                </template>
            </el-table-column> -->
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <template>
                    <el-form class="ad-form-query" :model="detailfilter" @submit.native.prevent label-width="100px">
                        <el-row>
                            <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
                                <el-form-item label="日期:">
                                    <el-date-picker style="width: 260px" v-model="detailfilter.timerange"
                                        type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                                        :clearable="false"></el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                                <el-form-item>
                                    <el-button type="primary" @click="getecharts">刷新</el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </template>
            </span>
            <span>
                <buschar v-if="buscharDialog.visible" ref="buschar" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <div v-show="isshowstate">
            <productnewstate ref="productnewstate" @changelist="changelist" @onSearch="onSearch"></productnewstate>
        </div>

        <el-dialog title="每人转化统计" :visible.sync="everyPersonVisible" width="60%" v-dialogDrag>
            <span>
                <personstatistics ref="personstatistics" :Filter="filterDetail" :personpager="personpager">
                </personstatistics>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="everyPersonVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="培训资料" :visible.sync="resourcedialogVisible" width="30%" height="50%" v-dialogDrag>
            <span>
                <trainresourceupload ref="trainres"></trainresourceupload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="resourcedialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog :visible.sync="amontDialog.visible" v-dialogDrag :show-close="false" width="500px">
            <el-table :data="amontDialog.rows" border style="width: 100%">
                <el-table-column prop="timeCalc" label="日期">
                    <template slot-scope="scope">
                        {{ scope.row.yearMonthDay}}
                    </template>
                </el-table-column>
                <el-table-column prop="clickNumber1" label="万象台" />
                <el-table-column prop="clickNumber2" label="引力魔方" />
                <el-table-column prop="clickNumber3" label="直通车" />
                <el-table-column prop="adveNumber" label="广告总访客" />
            </el-table>
        </el-dialog>

    </my-container>
</template>

<script>
import { getDirectorList, getDirectorGroupList, getProductBrandPageList, getList as getshopList } from '@/api/operatemanage/base/shop'
import { platformlist } from '@/utils/tools'
import { formatLinkProCode } from "@/utils/tools";
import { getAllProBrand } from '@/api/inventory/warehouse'
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import * as echarts from 'echarts'
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { pageLoseProfitAnalysis as  pageProductNewAsync, queryGuardProductNewsis, getProductStateName, getProductAdveClick } from '@/api/operatemanage/base/product'
import { getRateDetailList } from "@/api/customerservice/productconsulting";
import { addtrainplan } from "@/api/customerservice/trainplan";
import { getcusgroups, } from "@/api/customerservice/customergroup";
import cesTable from "@/components/Table/table.vue";
import productnewecharts from "./productnewstate.vue"
import buschar from '@/components/Bus/buschar'
import productnewstate from './productnewstate.vue'
import personstatistics from '../../customerservice/personstatistics.vue'
import trainresourceupload from "@/views/customerservice/trainresourceupload.vue"

const tableCols = [
    { istrue: true, prop: 'proCode', label: '产品ID', width: '105', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
    { istrue: true, prop: 'title', label: '产品名称', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'shopName', label: '店铺', width: '90', },
    { istrue: true, prop: 'groupId', label: '组长', width: '70', sortable: 'custom', formatter: (row) => row.groupName || ' ' },
    { istrue: true, prop: 'operateSpecialUserId', label: '运营专员', width: '80', sortable: 'custom', formatter: (row) => row.operateSpecialUserName || ' ' },
    { istrue: true, prop: 'userId', label: '运营助理', width: '80', sortable: 'custom', formatter: (row) => row.userRealName || ' ' },
    //{istrue:true,prop:'userId2',label:'车手', width:'70',sortable:'custom',permission:"productpermis",formatter:(row)=> row.userRealName2||' '},
    { istrue: true, prop: 'userId3', label: '备用', width: '60', sortable: 'custom', formatter: (row) => row.userRealName3 || ' ' },
    { istrue: true, prop: 'onTime', label: '上架时间', width: '90', sortable: 'custom', formatter: (row) => !row.onTime ? " ":row.onTime  },
    { istrue: true, prop: 'week1', label: '第一周', width: '90', sortable: 'custom',  },
    { istrue: true, prop: 'week2', label: '第二周', width: '90', sortable: 'custom',  },
    { istrue: true, prop: 'week3', label: '第三周', width: '90', sortable: 'custom', },
    { istrue: true, prop: 'week4', label: '第四周', width: '90', sortable: 'custom', },
    // { istrue: true, prop: 'newPattern', label: '上新模式', sortable: 'custom', width: '70', type: 'custom', type: 'click', handle: (that, row) => that.setprostate(row) },
    // { istrue: true, prop: 'renInquire', label: '人工转化率', width: '70', formatter: (row) => !row.renInquire ? "0%" : (row.renInquire * 100).toFixed(2) + '%' },
    { istrue: true, prop: 'images', label: '产品图片', width: '80', type: "image" },
    // { istrue: true, prop: 'customerGroups', label: '客服组', sortable: 'custom', width: '80', type: 'custom', tipmesg: '选择客服组', type: 'click', handle: (that, row) => that.setcustomergroup(row) },
];
const tableHandles1 = [];
const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");
const star = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");

export default {
    name: 'YunhanAdminProductnew',
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, productnewecharts, buschar, productnewstate, personstatistics, trainresourceupload },
    filters: {
        myTime(val) {
            return formatTime(val, "YYYY-MM-DD")
        }
    },
    props: {
        filter: {},
        filterDetail: {}
    },
    data() {
        return {
            that: this,

            searchOrder: "",
            searchIndex: -1,
            searchColumn: [
                { text: '搜索', value: 'searchVisitorNumber' },
                { text: '总访客量', value: 'orderCount' },
                { text: '广告访客量', value: 'adveNumber' },
                { text: '净利', value: 'profit4' },
                { text: '毛利', value: 'profit3' },
                { text: '支付买家数', value: 'payBuyNumber' },
            ],
            detailfilter: {
                procode: null,
                platform: null,
                startTime: null,
                endTime: null,
                timerange: [star, endTime]
            },
            Filter: {
                StartDate: null,
                EndDate: null,
                timerange: [startTime, endTime]
            },
            personpager: {
                OrderBy: "rensuccess",
                pageSize: 200,
                pageIndex: 1,
                IsAsc: false,
            },
            amontDialog: { visible: false, rows: [] },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "orderCount", IsAsc: false },
            tableCols: tableCols,
            tableHandles: tableHandles1,
            platformlist: platformlist,
            platformList: [],
            grouplist: [],
            brandlist: [],
            directorList: [],
            productnewList: [],
            cusgroupslist: [],
            shopList: [],
            directorGroupList: [],
            opList: [],
            total: 0,
            sels: [],
            selids: [],
            listLoading: false,
            echartsLoading: false,
            isshowstate: false,
            everyPersonVisible: false,
            resourcedialogVisible: false,
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                },
            },
            buscharDialog: { visible: false, title: "", data: [] },
        };
    },

    async mounted() {
        //await this.getlist();
        //await this.getDirectorlist()
        //await this.init()
        //await this.getPruductNewState()
        //await this.getProductCustomer()
    },

    methods: {
        async getPruductNewState() {
            var res = await getProductStateName();
            if (res?.code) {
                this.productnewList = res.data.map(function (item) {
                    var ob = new Object();
                    ob.state = item;
                    return ob;
                })
            }
        },
        async getProductCustomer() {
            var g = await getcusgroups({});

            this.cusgroupslist = g.data.list.map(function (item) {
                var ob = new Object();
                ob.state = item;
                return ob;
            });
        },
        async getDirectorlist() {
            const res1 = await getDirectorList({})
            const res2 = await getDirectorGroupList({})
            const res3 = await getProductBrandPageList()

            this.directorList = res1.data
            this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);
            this.bandList = res3.data?.list
        },
        async onchangeplatform(val) {
            this.categorylist = []
            const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
        },
        async init() {
            var res = await getAllProBrand();
            this.brandlist = res.data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            if (this.filterDetail.timerange) {
                this.filterDetail.StartDate = this.filterDetail.timerange[0];
                this.filterDetail.EndDate = this.filterDetail.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter, ...this.filterDetail }
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await pageProductNewAsync(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            data.forEach(d => {
                d._loading = false
            })
            this.list = data
            this.getEcharts()
        },
        getEcharts() {
            setTimeout(_ => {
                this.list.forEach(e => {
                    let myChart = echarts.init(this.$refs['echarts' + e.proCode]);
                    var series = []
                    this.echartsLoading = true
                    e.series.forEach(s => {
                        if (s.name != '日期')
                            series.push({ smooth: true, showSymbol: false, ...s })
                    })
                    this.echartsLoading = false
                    myChart.setOption({
                        legend: {
                            show: false
                        },
                        grid: {
                            left: "0",
                            top: "6",
                            right: "0",
                            bottom: "0",
                            containLabel: true,
                        },
                        xAxis: {
                            type: 'category',
                            //不显示x轴线
                            show: false,
                            data: e.xAxis
                        },
                        yAxis: {
                            type: 'value',
                            show: false,
                        },
                        series: series
                    });
                    window.addEventListener("resize", () => {
                        myChart.resize();
                    });
                })
            }, 1000)
        },
        async cellclick(row, column, cell, event) {
            if (column.label == '图表') {
                this.detailfilter.procode = row.proCode
                this.detailfilter.platform = row.platform
                this.getecharts()
            }
        },
        async getecharts() {
            this.detailfilter.startTime = null;
            this.detailfilter.endTime = null;
            if (this.detailfilter.timerange) {
                this.detailfilter.startTime = this.detailfilter.timerange[0];
                this.detailfilter.endTime = this.detailfilter.timerange[1];
            }
            var params = { ...this.detailfilter, isMedia: false }
            let that = this;
            const res = await queryGuardProductNewsis(params).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data
                that.buscharDialog.title = res.data.legend[0]
            })
            await this.$refs.buschar.initcharts()
        },
        //设置状态
        async setprostate(e) {
            this.isshowstate = true
            this.$nextTick(async () => {
                await this.$refs.productnewstate.OnSearch(e.proCode, this.list)
            })
        },
        async setcustomergroup(e) {
            this.isshowstate = true
            this.$nextTick(async () => {
                await this.$refs.productnewstate.setcustomergroup(e.proCode, this.list)
            })
        },
        //按人统计
        showperson(row) {
            const para = { productID: row.proCode, highrate: row.highrate }
            this.everyPersonVisible = true;
            this.$nextTick(() => {
                this.$refs.personstatistics.OnSearch(para)
            })
        },
        //添加培训计划
        addtrainplan(row) {
            var that = this;

            const params = {
                ...this.personpager,
                ...this.filterDetail,
                //dynamicFilter: this.dynamicFilter
            };
            params.ProductID = row.proCode;

            var listtrainee = "";

            var maxseller = "";
            var maxrate = 0;

            getRateDetailList(params).then((res) => {
                that.persondata = res.data.list;
                that.persondata.forEach(function (item, index) {
                    var success = item.rensuccess + item.jiqisuccess;
                    var fail = item.renfail + item.jiqifail;

                    item.rate = success / (fail + success);
                    if (item.rate < row.ren) {
                        listtrainee += item.seller + ","
                    }
                    if (item.rate > maxrate) {
                        maxrate = item.rate;
                        maxseller = item.seller;
                    }
                });
                var traindata = {};
                traindata.productID = row.proCode;
                traindata.coach = maxseller;
                traindata.trainees = listtrainee;

                this.$confirm("此操作将发布培训指令, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {

                        addtrainplan(traindata).then(res => {

                            if (res.data == true) {
                                that.$message({
                                    type: "success",
                                    message: "已添加成功",
                                });
                            }
                        });
                    })
                    .catch(() => {
                        //几点取消的提示
                    });
            });
        },
        //管理培训资料
        manageresource(row) {
            var that = this;
            that.resourcedialogVisible = true;
            var productID = row.proCode
            setTimeout(function () {

                that.$refs.trainres.init(productID);
            }, 200);
        },
        async changelist(e) {
            this.list = e
        },
        async clicknum(num, ii) {
            var res = await getProductAdveClick({ proCode: num.yearMonthDay })
            this.amontDialog.visible = true;
            this.amontDialog.rows = res.data;
        },
        tableColumnClick(orderColumn, index) {
            let column = {};
            column.prop = orderColumn;
            let currentNode = this.$refs.tableColumn.children[index];
            let className = currentNode.className;
            if (className.indexOf('ascending') > -1) {
                column.order = "descending";
            } else if (className.indexOf('descending') > -1) {
                column.order = "ascending";
            }
            else {
                column.order = "ascending";
            }

            this.searchOrder = column.order;
            this.searchIndex = index;

            this.$refs.table.clearSort();
            this.sortchange(column);
        },
        sortchange(column) {
            if(column.column){
                this.searchIndex=-1;
            }
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selsChange: function (sels) {
            this.sels = sels
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
    },
};
</script>

<style lang="scss" scoped>
.table-div {
    display: inline-block;
    text-align: center;
    width: 100%;
}

.table-div>a {
    padding: 0 10px;
}

.table-div .el-link--inner span {
    left: -9px;
}
</style>
