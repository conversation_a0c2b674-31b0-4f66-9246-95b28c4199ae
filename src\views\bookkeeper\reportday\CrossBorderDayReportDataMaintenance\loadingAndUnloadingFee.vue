<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>

        <el-form-item label="">
          <el-date-picker style="width: 320px" v-model="Filter.timerange" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>

        <el-button style="padding: 0;margin-right: 10px;border: none;">
          <inputYunhan :key="'1'" :keys="'one'" :width="'220px'" ref="" :inputt.sync="Filter.goodsCodes"
            v-model.trim="Filter.goodsCodes" placeholder="商品编码/若输入多条请按回车" :clearable="true"
            @callback="callbackGoodsCode" title="商品编码" @entersearch="entersearch" :maxRows="100">
          </inputYunhan>
        </el-button>

        <el-form-item label="">
          <el-input v-model.trim="Filter.containerCode" placeholder="批次号" maxlength="50" clearable filterable
            class="publicCss" />
        </el-form-item>


        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport">导出</el-button>
        </el-form-item>
      </el-form>
    </template>
    <!--列表-->
    <vxetablebase ref="table" :id="'crossBorderCourierFeeAverage202408310425'" :that='that' :isIndex='true'
      :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='dahuixionglist' :tableCols='tableCols'
      :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getjSpeedDriveList" />
    </template>

  </my-container>
</template>
<script>

import { pageShelvesHandlingResult, shelvesHandling_Export } from '@/api/bookkeeper/crossBorderV2'
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatTime } from '@/utils'
import inputYunhan from "@/components/Comm/inputYunhan";
const tableCols = [
  { istrue: true, prop: 'containerCode', label: '批次号', sortable: 'custom', },
  { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', },
  { istrue: true, prop: 'shelfTime', label: '费用日期', sortable: 'custom', formatter: (row) => formatTime(row.shelfTime, 'YYYY-MM-DD') },
  { istrue: true, prop: 'quantity', label: '数量', sortable: 'custom', },
  { istrue: true, prop: 'unloadingFee', label: '卸货上架费', sortable: 'custom', },
  { istrue: true, prop: 'unloadingFee_Price', label: '卸货上架费单价', sortable: 'custom', },
];

export default {
  name: "crossBorderCourierFeeAverage",
  components: { MyContainer, vxetablebase, inputYunhan },
  data() {
    return {
      that: this,
      editLoading: false,
      addVisible: false,
      Filter: {
        timerange: [],
      },
      dahuixionglist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: {},
      // pager: { OrderBy: "id", IsAsc: false },
      sels: [], // 列表选中列
      pageLoading: false,
      loading: false,
      selids: [],
      defaultDate: new Date(),
      pickerOptions: {
        disabledDate(date) {
          // 设置禁用日期
          const start = new Date("1970/1/1");
          const end = new Date("9999/12/31");
          return date < start || date > end;
        },
      },
    };
  },
  async mounted() {
    this.onSearch();
  },
  methods: {
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getjSpeedDriveList();
    },
    async getjSpeedDriveList() {
      this.loading = true
      const para = { ...this.Filter };
      if (this.Filter.timerange) {
        para.shelfTimeStart = this.Filter.timerange[0];
        para.shelfTimeEnd = this.Filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      const res = await pageShelvesHandlingResult(params);
      this.loading = false
      this.total = res.data.total
      this.dahuixionglist = res.data.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    //多条查询部分
    async entersearch(val) {
      this.getjSpeedDriveList();
    },
    async callbackGoodsCode(val) {
      this.Filter.goodsCodes = val;
    },
    async onExport() {//导出列表数据；
      const para = { ...this.Filter };
      if (this.Filter.timerange) {
        para.shelfTimeStart = this.Filter.timerange[0];
        para.shelfTimeEnd = this.Filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager(); 
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      var res = await shelvesHandling_Export(params);
      if (res?.success) {
        this.$message({ message: res.msg, type: "success" });
      }
    },

  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>