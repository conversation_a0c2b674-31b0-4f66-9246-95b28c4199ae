<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                <el-button-group style="border:none;">
                  
                    <el-button style="padding: 0;margin: 0;border:none;width:140px;">
                        <el-select v-model="Filter.expressCompany"  clearable placeholder="快递公司" >
                            <el-option v-for="item in expressCompanys" :label="item" :value="item"></el-option>                           
                        </el-select>
                    </el-button>

                    
                    <el-button style="padding: 0;margin: 0;border:none;width:140px;">
                        <yh-cityselector :clearable="true" :placeStr="'请选择省市'" :MaxLvl="2"    :orgProps="{ checkStrictly: true }"
                        :Address="[Filter.delayProvince,Filter.delayCity]" @change="(v)=>{Filter.delayProvince=v[0];Filter.delayCity=v[1]}" />
                    </el-button>
                    
                    <el-button style="padding: 0;margin: 0;border:none;width:90px;">
                        <el-select v-model="Filter.opName"  clearable placeholder="物流状态" >
                            <el-option v-for="item in opNames" :label="item" :value="item"></el-option>                           
                        </el-select>
                    </el-button>
                    
                
                    <el-button style="padding: 0;margin: 0;border:none;width:140px;">
                        <el-select v-model="Filter.delayType"  clearable placeholder="滞留类型" >
                            <el-option v-for="item in delayTypes" :label="item.label" :value="item.value"></el-option>                           
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none;">
                        滞留小时数
                        <el-input-number class="numberNoLeftPadding20230223" v-model.trim="Filter.delayHoursStart" controls-position="right" :min="0" :max="10000" :step="24" clearable style="width:80px;" :maxlength="10"/>
                        -
                        <el-input-number class="numberNoLeftPadding20230223"  v-model.trim="Filter.delayHoursEnd" controls-position="right" :min="0" :max="10000" :step="24" clearable style="width:80px;" :maxlength="10"/>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="Filter.timeType" style="width:100px;">
                            <el-option label="按违规时间" :value="1"></el-option>
                            <el-option label="按物流时间" :value="0"></el-option>
                        </el-select>
                        <el-date-picker style="width:220px" v-model="Filter.gDate" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"></el-date-picker>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none;">
                        <el-input v-model.trim="Filter.orderNo" clearable placeholder="线上单号" style="width:180px;" :maxlength="40"/>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none;">
                        <el-input v-model.trim="Filter.orderNoInner" clearable placeholder="内部单号" style="width:100px;" :maxlength="20"/>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none;">
                        <el-input v-model.trim="Filter.keywords" clearable placeholder="关键字查询" style="width:160px;"  :maxlength="40">
                            <el-tooltip slot="suffix" class="item" effect="dark" :content="keywordsTip" placement="bottom">
                                <i  class="el-input__icon el-icon-question"></i>
                            </el-tooltip>           
                        </el-input>
                    </el-button>

                    <el-button type="primary" @click="onSearch" >查询</el-button>
                    <el-button type="primary" @click="()=>{Filter={};}">清空条件</el-button>
                    <el-button type="primary" @click="newonExport">导出</el-button>

                </el-button-group>
            </el-form>
        </template>

         <vxetablebase :id="'OrderLogisticsDelayList2023022615210001'"
            :that='that' :loading="listLoading"
            :tableData='tbdatalist' :tableCols='tableCols'           
            @sortchange='sortchange'
            >           
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettbdatalist1" />
        </template>
       

        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px" append-to-body
                v-dialogDrag>
                <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo"
                    style="z-index:10000;height:600px" />
            </el-dialog>
            
        <el-dialog title="快递物流信息" v-if="drawerVisible" :visible.sync="drawerVisible" width="70%" height="600px" v-dialogDrag  append-to-body>
            <orderLogisticsPage ref="orderLogisticsPage" :ticketNumber="ticketNumber" :ticketCompanyName="ticketCompanyName" style="z-index:10000;height:600px" />
        </el-dialog>

    </my-container>
</template>
<script>  

    import {
        PageCalcOrderLogisticsDelayDetail, FilterDatas,exportCalcOrderLogisticsDelayDetail
    } from '@/api/order/LogisticsAnalyse' 
    import dayjs from "dayjs";
    import cesTable from "@/components/Table/table.vue";
    import vxetablebase from "@/components/VxeTable/vxetablebase.vue";

    import { formatmoney, formatPercen, getUrlParam, platformlist, formatPlatform, formatTime, setStore, getStore, formatLinkProCode,formatNoLink } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";

    import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
    import orderLogisticsPage from "@/views/order/logisticsWarning/orderLogisticsPage.vue";
    import YhCityselector from '@/components/YhCom/yh-cityselector.vue';
   

    const tableCols = [
        { istrue: true, prop: 'orderNo', label: '线上单号', width: '180', sortable: 'custom',  formatter: (row) => !row.orderNo ? " " : row.orderNo, type: 'clickLink', handle: (that, row) => that.showOutOrderDetail(row)  },        
        { istrue: true, prop: 'orderNoInner', label: '内部单号', width: '100', sortable: 'custom'  },

        { istrue: true, prop: 'expressCompany', label: '快递公司', width: '100', sortable: 'custom' },
        //{ istrue: true, prop: 'expressNo', label: '快递单号', width: '120', sortable: 'custom', type: 'clickLink', handle: (that, row) => that.showLogistics(row)  },
        { istrue: true, prop: 'expressNo', label: '快递单号', width: '125', sortable: 'custom',type: 'clickLink', handle: (that, row) => that.onShowLogistics(row)  },
        { istrue: true, prop: 'delayProvince', label: '滞留省', width: '90', sortable: 'custom' },
        { istrue: true, prop: 'delayCity', label: '滞留市', width: '90', sortable: 'custom' },
        { istrue: true, prop: 'opName', label: '状态', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'delayHours', label: '滞留小时', width: '110', sortable: 'custom'  },
        { istrue: true, prop: 'opRemark', label: '物流内容', minwidth: '120', sortable: 'custom' },
        { istrue: true, prop: 'delayType', label: '滞留类型', width: '110', sortable: 'custom', formatter: (row) => {return row.delayTypeText; }},
        { istrue: true, prop: 'opTime', label: '物流时间', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'deductTime', label: '扣款时间', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'toProvince', label: '下站省', width: '90', sortable: 'custom' },
        { istrue: true, prop: 'toCity', label: '下站市', width: '90', sortable: 'custom' },

        { istrue: true, prop: 'orderProvince', label: '收件省', width: '90', sortable: 'custom' },
        { istrue: true, prop: 'orderCity', label: '收件市', width: '90', sortable: 'custom' },        
      
    ];

   
    const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
    const endTime = formatTime(new Date(), "YYYY-MM-DD");
    const star = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");

    export default {
        name: "OrderLogisticsDelayDetailList",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable,vxetablebase, OrderActionsByInnerNos, orderLogisticsPage , YhCityselector},
        data() {
            return {
                that: this,
                Filter: {
                    delayType:99,
                    timeType:1
                },
                keywordsTip:'可查内容：省、市、单号、快递号、物流内容'     ,   
                expressCompanys:[],  
                delayTypes:[],  
                opNames:[],         
                tbdatalist: [],
                tableCols: tableCols,
                total: 0,               
                pager: { OrderBy: "", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
              
                curRow: {},   
                
                dialogHisVisible:false,
                orderNo:"",

                drawerVisible:false,
                ticketNumber:"",
                ticketCompanyName:"",

                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一月',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三月',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                        }
                    }]
                },

            };
        },
        async mounted() {
            let fds=await FilterDatas();
            if(fds && fds.success){
                this.expressCompanys=fds.data.companys;
                this.delayTypes=fds.data.delayTypes;
                this.opNames=fds.data.opNames;
            }

            //this.onSearch();
        },
        methods: {   
            onShowLogistics(row) {
                let self = this;
                this.$showDialogform({
                    path: `@/views/order/logisticsWarning/DbLogisticsRecords.vue`,
                    title: '物流明细',
                    args: { expressNos:row.expressNo },
                    height: 300,
                });
            },
            showOutOrderDetail (row) {
                this.dialogHisVisible = true;
                this.orderNo = row.orderNo;
            },       
            showLogistics (row) {
                this.drawerVisible = true;
                this.ticketCompanyName = row.expressCompany;
                this.ticketNumber = row.expressNo;
            },
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderField = column.prop;                   

                    this.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                }
                this.onSearch();
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.gettbdatalist1();
            },
            async gettbdatalist1() {
                if (this.Filter.gDate) {
                    this.Filter.opTimeStart = this.Filter.gDate[0];
                    this.Filter.opTimeEnd = this.Filter.gDate[1];
                }
                else {
                    this.Filter.startGDate = null;
                    this.Filter.endGDate = null;

                }
                const para = { ...this.Filter };
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...para,
                };

                this.listLoading = true;
                const res = await PageCalcOrderLogisticsDelayDetail(params);

                this.listLoading = false;

                this.total = res.data.total
                this.tbdatalist = res.data.list;
            },
            loadData(filter){
                this.Filter={...filter};

                this.onSearch();
            },
            //导出
        async newonExport() {
            if (this.Filter.gDate) {
                    this.Filter.opTimeStart = this.Filter.gDate[0];
                    this.Filter.opTimeEnd = this.Filter.gDate[1];
            }
            else {
                    this.Filter.startGDate = null;
                    this.Filter.endGDate = null;

            }
            const para = { ...this.Filter };
            const params = {
                    ...para,
            };

            var res = await exportCalcOrderLogisticsDelayDetail(params);
            if (!res?.data) return;
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            aLink.setAttribute("download","物流异常分析-订单详情" + new Date().toLocaleString() + ".xlsx");
            aLink.click();
            },
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
<style >
    .el-image__inner {
        max-width: 50px;
        max-height: 50px;
    }

    .numberNoLeftPadding20230223 input{
        padding-left: 1px !important;
        padding-right: 30px !important;
    }
</style>