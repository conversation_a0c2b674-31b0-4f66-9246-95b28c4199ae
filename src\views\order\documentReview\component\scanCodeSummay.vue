<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" :clearable="false"
                    style="width: 220px;margin-right: 10px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker>
                <el-select v-model="ListInfo.wmsIds" placeholder="仓库" class="publicCss" clearable multiple
                    collapse-tags>
                    <el-option v-for="item in wareHouseList" :key="item.wms_co_id" :label="item.name"
                        :value="item.wms_co_id" />
                </el-select>
                <el-select v-model="ListInfo.types" placeholder="类型" class="publicCss" clearable multiple collapse-tags>
                    <el-option v-for="item in type" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-input v-model="ListInfo.scaner" placeholder="请输入扫码人" maxlength="20" clearable class="publicCss" />
                <el-input v-model="ListInfo.printer" placeholder="请输入贴单人" maxlength="20" clearable class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
            </div>
        </template>
        <vxetablebase :id="'scanCodeSummary202408041753'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" style="width: 100%; height: 680px; margin: 0"
            v-loading="loading" :summaryarry='summaryarry' :showsummary='true' @summaryClick='onsummaryClick' />
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />

        <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false"
            direction="btt">
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至" :clearable="false"
                    start-placeholder="开始日期" end-placeholder="结束日期" @change="chatSearch" :picker-options="pickerOptions"
                    style="margin: 10px;" />
                <el-select v-model="chatInfo.type" placeholder="类型" class="publicCss" clearable @change="chatSearch">
                    <el-option v-for="item in type" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <buschar :analysisData="chatProp.data" v-if="!chatProp.chatLoading"></buschar>
            </div>
            <div v-else v-loading="chatProp.chatLoading"></div>
        </el-drawer>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import buschar from '@/components/Bus/buschar'
import dayjs from 'dayjs'
import middlevue from "@/store/middle.js"
import { getScanPrintStat, getStatTrendChart } from '@/api/vo/ReturnOrderScan'
import { pageGetTbWarehouseAsync } from "@/api/inventory/prepack.js"
const type = [
    {
        label: '退件',
        value: 0
    },
    {
        label: '商品编码',
        value: 1
    },
    {
        label: '售后',
        value: 2
    }
]
const tableCols = [
    { istrue: true, prop: 'dayItem', align: 'center', label: '年月日', sortable: 'custom', width: 'auto', formatter: (row) => row.dayItem ? dayjs(row.dayItem).format('YYYY-MM-DD') : '' },
    { istrue: true, prop: 'userName', label: '姓名', sortable: 'custom', width: 'auto', },
    { istrue: true, prop: 'wmsName', label: '仓库', sortable: 'custom', width: 'auto', },
    { istrue: true, prop: 'type', label: '类型', sortable: 'custom', width: 'auto', formatter: (row) => type.find(item => item.value == row.type).label },
    { istrue: true, prop: 'scanCount', label: '扫码工作量', summaryEvent: true, sortable: 'custom', width: 'auto', type: 'click', handle: (that, row) => that.linkToDetails(row, 'sm') },
    { istrue: true, prop: 'printScanCount', label: '贴单扫码量', summaryEvent: true, sortable: 'custom', width: 'auto', type: 'click', handle: (that, row) => that.linkToDetails(row, 'td') },
    { istrue: true, prop: 'printCount', label: '贴单工作量', summaryEvent: true, sortable: 'custom', width: 'auto', type: 'click', handle: (that, row) => that.linkToDetails(row, 'tdgzl') },
    { istrue: true, prop: 'printRate', label: '贴单率', summaryEvent: true, sortable: 'custom', width: 'auto', formatter: (row) => row.printRate ? parseFloat((Number(row.printRate) * 100).toFixed(2)) + '%' : '' },
    { istrue: true, prop: 'rejectCount', label: '撤销订单量', sortable: 'custom', width: 'auto', },
    { istrue: true, prop: 'realCount', label: '真实发单量', sortable: 'custom', width: 'auto', },
    { istrue: true, prop: 'repeatedlyCount1', label: '退件转售后', summaryEvent: true, sortable: 'custom', width: 'auto', type: 'click', handle: (that, row) => that.linkToError(row, '退件转售后') },
    { istrue: true, prop: 'repeatedlyCount2', label: '售后转退件', summaryEvent: true, sortable: 'custom', width: 'auto', type: 'click', handle: (that, row) => that.linkToError(row, '售后转退件') },
    { istrue: true, prop: 'repeatedlyCount3', label: '二次退件', summaryEvent: true, sortable: 'custom', width: 'auto', type: 'click', handle: (that, row) => that.linkToError(row, '二次退件') },
    { istrue: true, prop: 'repeatedlyCount4', label: '二次售后', summaryEvent: true, sortable: 'custom', width: 'auto', type: 'click', handle: (that, row) => that.linkToError(row, '二次售后') },
    {
        istrue: true, label: '趋势图', width: 'auto', type: 'button', btnList: [
            { label: '趋势图', handle: (that, row) => that.openChat(row.userId, row.wmsIds, row.type) }
        ]
    },
]
export default {
    name: "scanCodeSummay",
    components: {
        MyContainer, vxetablebase, buschar
    },
    data() {
        return {
            type,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: null,//开始时间
                endDate: null,//结束时间
                scaner: null,//扫码人
                printer: null,//贴单人
                wmsIds: [],//仓库id
                types: [],//类型
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            summaryarry: {},
            chatProp: {
                chatDialog: false,//趋势图弹窗
                chatTime: null,//趋势图时间
                chatLoading: true,//趋势图loading
                data: [],//趋势图数据
            },
            chatInfo: {
                startDate: null,//开始时间
                endDate: null,//结束时间
                userId: null,//用户id
                type: null,//类型
            },
            wareHouseList: [],
        }
    },
    async mounted() {
        this.init()
        await this.getWareHouse()
        await this.getList()
    },
    methods: {
        linkToError(row, type) {
            this.$emit('toDetails', 'third')
            const params = {
                startDate: dayjs(row.dayItem).format('YYYY-MM-DD'),
                endDate: dayjs(row.dayItem).format('YYYY-MM-DD'),
                printer: row.userName ? row.userName : null,
                repeatedlyType: type,
            }
            middlevue.$emit('queryErrorsList', params)
        },
        async getWareHouse() {
            const params = {
                currentPage: 1,
                pageSize: 1000,
                orderBy: null,
                isAsc: false,
            }
            const { data: { list } } = await pageGetTbWarehouseAsync(params)
            this.wareHouseList = list
        },
        async onsummaryClick() {
            this.chatInfo = {
                startDate: this.timeRanges[0],//开始时间
                endDate: this.timeRanges[1],//结束时间
                type: null,//类型
            }
            this.chatProp.chatTime = [this.chatInfo.startDate, this.chatInfo.endDate]
            await this.getCharts(this.chatInfo)
        },
        linkToDetails(row, type) {
            this.$emit('toDetails', 'second')
            const params = {
                startDate: dayjs(row.dayItem).format('YYYY-MM-DD'),
                endDate: dayjs(row.dayItem).format('YYYY-MM-DD'),
                scaner: type == 'sm' ? row.userName : null,
                printer: type == 'td' || type == 'tdgzl' ? row.userName : null,
                status: type == 'tdgzl' ? 1 : null,
            }
            setTimeout(() => {
                middlevue.$emit('queryDetailsList', params)
            }, 500)
        },
        init() {
            //默认七天时间
            this.timeRanges = [dayjs().subtract(6, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
            this.ListInfo.startDate = this.timeRanges[0]
            this.ListInfo.endDate = this.timeRanges[1]
        },
        //趋势图时间改变
        async chatSearch() {
            this.chatInfo.startDate = dayjs(this.chatProp.chatTime[0]).format('YYYY-MM-DD')
            this.chatInfo.endDate = dayjs(this.chatProp.chatTime[1]).format('YYYY-MM-DD')
            await this.getCharts(this.chatInfo)
        },
        clearInfo() {
            this.chatInfo = {
                startDate: null,//开始时间
                endDate: null,//结束时间
                userId: null,//用户id
            }
        },
        async getCharts(queryInfo) {
            this.chatProp.chatLoading = true
            const { data, success } = await getStatTrendChart(queryInfo)
            if (success) {
                this.chatProp.data = data
                this.chatProp.chatDialog = true
                this.chatProp.chatLoading = false
            }
        },
        async openChat(userId, wmsIds, type) {
            this.clearInfo()
            //默认给7天时间
            this.chatInfo = {
                startDate: dayjs().subtract(6, 'day').format('YYYY-MM-DD'),//开始时间
                endDate: dayjs().format('YYYY-MM-DD'),//结束时间
                userId,//用户id
                wmsIds,
                type: null,
            }
            this.chatProp.chatTime = [this.chatInfo.startDate, this.chatInfo.endDate]
            await this.getCharts(this.chatInfo)
        },
        async changeTime(e) {
            this.ListInfo.startDate = e ? e[0] : null
            this.ListInfo.endDate = e ? e[1] : null
            await this.getList()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
            }
            const replaceArr = ['scaner', 'printer'] //替换空格的方法,该数组对应str类型的input双向绑定的值
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            const { data, success } = await getScanPrintStat(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.summaryarry = data.summary
                this.summaryarry.printRate_sum = parseFloat((Number(this.summaryarry.printRate_sum) * 100).toFixed(2)) + '%'
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 220px;
        margin-right: 10px;
    }
}
</style>
