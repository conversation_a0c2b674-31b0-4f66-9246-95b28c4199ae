<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent label-position="right"
        label-width="90px">
        <el-form-item label="商品编码">
          <button style="padding: 0; border: none;">
            <inputYunhan title="商品编码" placeholder="商品编码" :maxRows="2000" :inputshow="0" :clearable="true"
              :clearabletext="true" :showRowCount="true" :showBlank="false" @callback="multiGoodsCodeCallBack"
              :inputt.sync="filter.goodsCode" :maxlength="30000"></inputYunhan>
          </button>
        </el-form-item>
        <el-form-item label="商品名称:">
          <el-input v-model.trim="filter.goodsName" placeholder="商品名称" clearable :maxlength="200" @change="onSearch" />
        </el-form-item>
        <el-form-item label="款式编码:">
          <button style="padding: 0; border: none;">
            <inputYunhan title="款式编码" placeholder="款式编码" :maxRows="2000" :inputshow="0" :clearable="true"
              :clearabletext="true" :showRowCount="true" :showBlank="false" @callback="multiStyleCodeCallBack"
              :inputt.sync="filter.styleCode" :maxlength="30000"></inputYunhan>
          </button>
        </el-form-item>
        <el-form-item label="运营组:">
          <el-select style="width:160px;" v-model="filter.groupId" placeholder="请选择" :clearable="true" multiple
            filterable :collapse-tags="true" @change="onSearch">
            <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="采购组:">
          <el-select style="width:160px;" v-model="filter.brandId" placeholder="请选择" :clearable="true" multiple
            filterable :collapse-tags="true" @change="onSearch">
            <el-option v-for="item in brandList" :key="item.key" :label="item.value" :value="item.key">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排除采购组:">
          <el-select style="width:160px;" v-model="filter.unBrandId" placeholder="请选择" :clearable="true" multiple
            filterable :collapse-tags="true" @change="onSearch">
            <el-option v-for="item in brandList" :key="item.key" :label="item.value" :value="item.key">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开款人:">
          <el-input v-model="filter.firstUserName" placeholder="开款人" maxlength="20" />
        </el-form-item>
        <el-form-item label="属性:">
          <el-select v-model="filter.attributeTag" style="width: 154px ;height: 29px" placeholder="属性" multiple
            filterable collapse-tags clearable>
            <el-option label="常规款" :value="'常规款'" />
            <el-option label="定制款" :value="'定制款'" />
          </el-select>
        </el-form-item>
        <el-form-item label="季节/节日:">
          <el-select v-model="filter.seasonOrFestivalTag" style="width: 320px ;height: 29px" placeholder="季节/节日"
            multiple filterable collapse-tags clearable>
            <el-option v-for="item in seasonList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="启用/禁用:">
          <el-select v-model="filter.isEnabled" style="width: 154px ;height: 29px" placeholder="启用/禁用" filterable
            clearable>
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="-1" />
          </el-select>
        </el-form-item>
        <el-form-item label="天气:">
          <el-select v-model="filter.weatherTag" style="width: 154px ;height: 29px" placeholder="天气" multiple filterable
            collapse-tags clearable>
            <el-option label="阳光" value="阳光" />
            <el-option label="雨水" value="雨水" />
            <el-option label="冰雪" value="冰雪" />
            <el-option label="台风" value="台风" />
            <el-option label="无" value="无" />
          </el-select>
        </el-form-item>
        <el-form-item label="温度:">
          <el-select v-model="filter.temperatureTag" style="width: 220px ;height: 29px" placeholder="温度" multiple
            filterable collapse-tags clearable>
            <el-option v-for="item in temperatureList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="虚拟分类">
          <el-select v-model="filter.virtualType" style="width: 220px; height: 29px;" placeholder="虚拟分类" filterable clearable>
            <el-option v-for="item in virtualTypeList" :key="item" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
         <el-form-item label="有无白底图">
          <el-select v-model="filter.HasWhiteBackImg" style="width: 100px; height: 29px;" placeholder="有无白底图" filterable clearable>
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onCustomizeSearchConfig">自定义查询配置</el-button>
          <el-button type="primary" @click="onCustomizeSearch">自定义查询</el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="showAlter" :disabled="!recordSelRows||recordSelRows.length<1">修改</el-button>
          <el-button   @click="syncJstSku">从聚水潭同步搜索框内编码</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="showNote">操作日志</el-button>
        </el-form-item>
      </el-form>
    </template>

    <vxetablebase ref="table" :id="'YunHanAdminGoods20230808'" :that='that' :isIndex='true' :hasexpand='true'
      @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols'
      @select='selectchange' :isSelection.sync="ischoice" :border='true'
      :checkbox-config="{ labelField: 'id', highlight: true, range: true }" @checkbox-range-end="callback"
      :tableHandles='tableHandles' :isSelectColumn="!ischoice" :loading="listLoading">
      <template slot='extentbtn'>
        <el-button-group>
          <el-button style="margin: 0;" type="primary" v-if="!ischoice" @click="startImport">导入</el-button>
        </el-button-group>
      </template>
      <template #packCount="{row}">
        <div v-show="!row.isEditPackCount" @click="openIpt(row)" style="width: 100;height:40px;line-height: 40px;">{{ row.packCount }}</div>
        <el-input-number v-show="row.isEditPackCount" style="width: 100px;" v-model="row.packCount" @blur="editPackCount(row,`${row.goodsCode}packCount`)"
            :controls="false" :min="0" :max="100000" placeholder="标准装箱数" width="120" :precision="0" :ref="`${row.goodsCode}packCount`">
          >
        </el-input-number>
      </template>
      <template slot='right'>
            <vxe-column title="操作" width="160" fixed="right" v-if="checkPermission(['spzlk-bj'])">
                <template #default="{ row }">
                    <template>
                        <!-- checkPermission(['spzlk-bj']) -->
                        <vxe-button size="mini" @click="editrow(row)">编辑</vxe-button>
                    </template>
                </template>
            </vxe-column>
        </template>
    </vxetablebase>

    <template #footer>
      <my-pagination ref="pager" :pageSize="2000" :sizes=[50,100,200,300,1000,2000] :total="total" :checked-count="sels.length"
        @get-page="onPageChange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag>
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange">
          <template #trigger>
            <el-button size="small" type="primary">选取数据文件</el-button>
          </template>
          <el-button style="margin-left: 10px;" size="small" type="success" :loading="uploadLoading"
            @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择品牌" :visible.sync="alterVisible" v-dialogDrag @close="closealrt">
      <div style="text-align: right;margin:15px;">
        <el-input style="width: 30%;" v-model="queryBrand" @input="brandFilter">
          <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>
      </div>
      <div style="height: 400px; overflow: scroll;">
        <span v-for="item in brandRadioList" :key="item.key" :label="item.value" :value="item.key" class="vForBr">
          <el-tooltip :content="item.value">
            <el-radio v-model="setBrand" :label="item.key">{{
              item.value.length>10?item.value.slice(0,10)+"...":item.value }}</el-radio>
          </el-tooltip>
        </span>
      </div>
      <div style="text-align: right;">
        <el-button @click="closealrt">取消</el-button>
        <el-button type="primary" @click="submitChange">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="showNoteVisible" v-dialogDrag>
      <div style="margin-bottom: 30px;">
        <button style="padding: 0; border: none;">
          <inputYunhan title="请分行输入商品编码" placeholder="商品编码" :maxRows="100" :inputshow="0" :showRowCount="true" :clearable="true"
        @callback="goodsCodeCallBack" :inputt.sync="queryRecord.goodsCodes"></inputYunhan>
        </button>
        <el-button type="primary" @click="getRecordList">搜索</el-button>
      </div>

      <el-tabs v-model="switchTable" type="card" @tab-click="RecordSwitch">
        <el-tab-pane label="人为修改" name="人为修改">
          <vxetablebase ref="recordTable1" :id="'YunHanAdminGoodsRecordTable20240820'" :that='that' :isIndex='true'
            :hasexpand='true' :toolbarshow="false" :tableData='recordList' :tableCols='recordCols' :border='true'
            :loading="recordListLoading" style="height: 400px;" @sortchange='recordSortChange'>
          </vxetablebase>
        </el-tab-pane>
        <el-tab-pane label="非人为修改" name="非人为修改">
          <vxetablebase ref="recordTable2" :id="'YunHanAdminGoodsSyncRecordTable20240823'" :that='that' :isIndex='true'
            :hasexpand='true' :toolbarshow="false" :tableData='syncRecordList' :tableCols='syncRecordCols'
            :border='true' :loading="syncRecordListLoading" style="height: 400px;" @sortchange='syncRecordSortChange'>
          </vxetablebase>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <my-pagination ref="pager2" :total="recordTotal" @page-change="Pagechange" @size-change="Sizechange" />
      </template>
    </el-dialog>

    <el-dialog title="自定义查询" :visible.sync="customizeVisible" width="45%" v-dialogDrag>
      <div style="display: flex;flex-direction: column;justify-content: center;">
        <el-form>
          <el-form-item>
            <el-button type="primary" @click="onAddNewCustomize">新增一行</el-button>
          </el-form-item>
          <el-form-item>
            <el-table :data="customize.list" style="width: 100%" v-loading="loading" max-height="600" height="400">
              <el-table-column width="190">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.fieldCode" placeholder="查询字段" filterable collapse-tags clearable @change="onFieldChange(scope.row)">
                    <el-option v-for="item in customizeConfigs.fieldList" :key="item.fieldLabel" :value="item.fieldName" :label="item.fieldLabel"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column width="130">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.conditionType" placeholder="对比方式" @change="onChangeConditionType(scope.row)" filterable collapse-tags clearable style="width: 120px;">
                    <el-option v-for="item in scope.row.symbolList" :key="item.title" :value="item.key" :label="item.title"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column>
                <template slot-scope="scope">
                  <el-input v-if="scope.row.componentType!='dropdown' && scope.row.fieldType!='date'" v-model.trim="scope.row.queryValue" placeholder="查询值" clearable
                    :maxlength="200" style="width: 280px;"></el-input>
                  <el-select v-if="scope.row.componentType=='dropdown' && (scope.row.conditionType!='BeIncluded' && scope.row.conditionType!='NotBeIncluded') && isShow
                    && scope.row.fieldType!='date'"
                    v-model="scope.row.queryValue" placeholder="查询值" clearable filterable collapse-tags style="width: 280px;">
                    <el-option v-for="kv in scope.row.valList" :key="kv.Key" :value="kv.Val" :label="kv.Key"></el-option>
                  </el-select>
                  <el-select v-if="scope.row.componentType=='dropdown' && (scope.row.conditionType=='BeIncluded' || scope.row.conditionType=='NotBeIncluded') && isShow
                    && scope.row.fieldType!='date'"
                    v-model="scope.row.queryValues" placeholder="查询值" multiple filterable collapse-tags clearable style="width: 280px;">
                    <el-option v-for="kv in scope.row.valList" :key="kv.Key" :value="kv.Val" :label="kv.Key"></el-option>
                  </el-select>
                  <el-date-picker v-if="scope.row.fieldType=='date'" v-model="scope.row.queryValue" type="datetime" placeholder="选择时间"
                    value-format="yyyy-MM-dd HH:mm:ss" clearable style="width: 280px;"/>
                </template>
              </el-table-column>
              <el-table-column width="110">
                <template slot-scope="scope">
                  <el-input-number v-model.trim="scope.row.conditionSort" placeholder="排序" :min="0" :max="999" :precision="0" style="width: 100px;"></el-input-number>
                </template>
              </el-table-column>
              <el-table-column width="90">
                <template slot-scope="scope">
                  <el-button type="primary" @click="onDelCustomize(scope.row,scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <span style="margin-right: 5px;color: #E50000;">说明:</span>
        <el-select v-model="exampleSymbol" style="width: 105px ;" placeholder="对比方式" filterable collapse-tags clearable @change="onExampleSymbolChange">
          <el-option v-for="item in customizeConfigs.symbol" :key="item.title" :label="item.title" :value="item.key" />
        </el-select>
        <span style="margin-left: 5px;color: #E50000;">{{ exampleSymbolTip }}</span>
      </div>
      <div style="text-align: right;">
        <el-button @click="customizeVisible = false">取消</el-button>
        <el-button type="primary" @click="onSaveCustomize" :loading="customizeSaveBtnLoading">确定</el-button>
      </div>
    </el-dialog>



    <el-dialog title="上传白底图" :visible.sync="whitedialogVisible" width="30%" v-dialogDrag>
      <span>
        <YhImgUpload :value.sync="editForm.goodFrontBackImgs" v-if='whitedialogVisible' :limit="3" :ismultiple="true"
            ref="goodFrontBackImgs">
        </YhImgUpload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="whitedialogVisible = flase">关闭</el-button>
        <el-button type="primary" @click="savewhitePic">保存</el-button>

      </span>
    </el-dialog>


  </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetableVirtualScroll.vue";
import { getGroupKeyValue } from '@/api/operatemanage/base/product';
import { getAllProBrand } from '@/api/inventory/warehouse';
import inputYunhan from '@/views/base/goods/inputYunhan.vue';
import { SyncGoodsByGoodCodes } from '@/api/hangfire';
import {ChangePackCount} from '@/api/inventory/purchase'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import checkPermission from '@/utils/permission'

import YhImgUpload from "@/components/upload/yh-img-upload3.vue";
import {
  //分页查询店铺商品资料
  getList,
  //导入
  importData,
  changeBrandAndJstSync,
  getBrandChangeLog,
  getBrandChangeSyncLog,
  getGoodsCustomizeQuery,
  saveGoodsCustomizeQuery,
  getCustomizeConfig,
  getGoodsListByCustomizeQuery,
  SetGoodsWhiteBackImg
} from "@/api/inventory/basicgoods";
import request from '@/utils/request'
const api = '/api/order/shipperFxOrder/';
const seasonList = [
  { label: '四季款（常年）', value: '四季款（常年）' },
  { label: '春季款（1月1日-4月底）', value: '春季款（1月1日-4月底）' },
  { label: '夏季款（3月15日-9月底）', value: '夏季款（3月15日-9月底）' },
  { label: '秋季款（8月15日-10月底）', value: '秋季款（8月15日-10月底）' },
  { label: '冬季款（9月15日-1月底）', value: '冬季款（9月15日-1月底）' },
  { label: '开学季（1月1日至2月底）', value: '开学季（1月1日至2月底）' },
  { label: '开学季（7月1日至8月底）', value: '开学季（7月1日至8月底）' },
  { label: '清明节（3月1日至3月底）', value: '清明节（3月1日至3月底）' },
  { label: '端午节（农历四月初五至四月底）', value: '端午节（农历四月初五至四月底）' },
  { label: '中秋节（农历七月十五至八月初十）', value: '中秋节（农历七月十五至八月初十）' },
  { label: '国庆节（9月1日至9月25日）', value: '国庆节（9月1日至9月25日）' },
  { label: '圣诞节（不允许进货）', value: '圣诞节（不允许进货）' },
  { label: '元旦节（农历十一月初一至腊月十五）', value: '元旦节（农历十一月初一至腊月十五）' },
  { label: '春节（农历十一月初一至腊月十五）', value: '春节（农历十一月初一至腊月十五）' }
]
const temperatureList = [
  { label: '酷热（38度以上）', value: '酷热（38度以上）' },
  { label: '炎热（35到37度）', value: '炎热（35到37度）' },
  { label: '闷热（28到35度）', value: '闷热（28到35度）' },
  { label: '温暖（10到27度）', value: '温暖（10到27度）' },
  { label: '凉爽（0到9度）', value: '凉爽（0到9度）' },
  { label: '寒冷（-1到-9度）', value: '寒冷（-1到-9度）' },
  { label: '严寒（低于-10度）', value: '严寒（低于-10度）' },
  { label: '无', value: '无' }
]
const tableCols = [
  { istrue: true, label: '', width: '100', type: "checkbox", },
  { istrue: true, prop: 'goodsCode', label: '商品编码', width: '100', sortable: 'custom', },
  { istrue: true, prop: 'goodsName', label: '商品名称', width: '230', },
  { istrue: true, prop: 'firstUserName', label: '开款人', width: '100', sortable: 'custom', },
  { istrue: true, prop: 'pictureBig', label: '图片', width: '60', type: 'images', goods: { code: 'goodsCode', name: 'goodsName' } },
  { istrue: true, prop: 'whiteBackImg', label: '白底图片', width: '70', type: 'imagess', },

  { istrue: true, prop: 'styleCode', label: '款式编码', width: '130', sortable: 'custom', },
  { istrue: true, prop: 'shortName', label: '简称', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'costPrice', label: '成本价', width: '80', sortable: 'custom', },

  { istrue: true, prop: 'masterStock', label: '实际库存数', width: '130', sortable: 'custom', },
  { istrue: true, prop: 'purchaseQty', label: '采购在途数', width: '130', sortable: 'custom', },
  { istrue: true, prop: 'inQty', label: '进货仓库存', width: '130', sortable: 'custom', },
  { istrue: true, prop: 'orderLock', label: '订单占有数', width: '130', sortable: 'custom', },
  { istrue: true, prop: 'allocateTransitNum', label: '调拨在途数', width: '130', sortable: 'custom', },
  { istrue: true, prop: 'inventoryDay', label: '库存周转天数', width: '130', sortable: 'custom', },
  { istrue: true, prop: 'inventoryFunds', label: '库存资金', width: '130', sortable: 'custom', },
  { istrue: true, prop: 'totalFunds', label: '合计资金', width: '130', sortable: 'custom', },

  { istrue: true, prop: 'groupId', label: '运营组', width: '80', sortable: 'custom', formatter: (row) => row.groupId == 0 ? " " : row.groupName },
  { istrue: true, prop: 'packCount', label: '标准装箱数', width: '150', sortable: 'custom', },
  { istrue: true, prop: 'modified', label: '修改时间', width: '150', sortable: 'custom', formatter: (row) => formatTime(row.modified, 'YYYY-MM-DD HH:mm:ss') },
  { istrue: true, prop: 'isEnabled', label: '是否启用', width: '80', sortable: 'custom', formatter: (row) => row.isEnabled == 0 ? "备用" : (row.isEnabled == 1 ? "启用" : (row.isEnabled == -1 ? "禁用" : "")) },
  { istrue: true, prop: 'weight', label: '重量', width: '60', sortable: 'custom' },
  { istrue: true, prop: 'expressWeight', label: '快递账单重量', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'computeRatio', label: '计算重量占比', width: '80', sortable: 'custom', formatter: (row) => !row.computeRatio ? " " : row.computeRatio * 100 + '%' },
  { istrue: true, prop: 'weightDifference', label: '重量差', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'brandId', label: '采购组', width: '80', sortable: 'custom', formatter: (row) => row.brandId == 0 ? " " : row.brandName },
  { istrue: true, prop: 'supplierCode', label: '供应商编码', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'supplierName', label: '供应商名称', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'barcode', label: '国际条码', width: '90', sortable: 'custom' },
  { istrue: true, prop: 'color', label: '颜色', width: '60', sortable: 'custom' },
  { istrue: true, prop: 'supplierGoodsCode', label: '供应商商品编码', width: '130', sortable: 'custom' },
  { istrue: true, prop: 'supplierStyle', label: '供应商商品款号', width: '130', sortable: 'custom' },
  { istrue: true, prop: 'virtualType', label: '虚拟分类', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'remark', label: '备注', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'itemType', label: '商品属性', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'stockDisabled', label: '是否禁止同步', width: '120', sortable: 'custom', formatter: (row) => row.stockDisabled == 0 ? "启用同步" : (row.stockDisabled == 1 ? "禁用同步" : (row.stockDisabled == -1 ? "部分禁用" : "")) },
  { istrue: true, prop: 'unit', label: '单位', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'labels', label: '标签', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'attributeTag', label: '属性', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'seasonOrFestivalTag', label: '季节/节日', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'weatherTag', label: '天气', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'temperatureTag', label: '温度', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'stockType', label: '链接同步状态', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'skuCodes', label: '辅助码', width: '100', sortable: 'custom' }
];

const recordCols = [
{ istrue: true, prop: 'goodsCode', label: '商品编码',sortable: 'custom' },
{ istrue: true, prop: 'record', label: '操作记录', width:'380',sortable: 'custom' },
{ istrue: true, prop: 'operator', label: '操作人' ,sortable: 'custom' },
{ istrue: true, prop: 'time', label: '操作时间' ,sortable: 'custom' },
];

const syncRecordCols = [
{ istrue: true, prop: 'goodsCode', label: '商品编码',sortable: 'custom' },
{ istrue: true, prop: 'record', label: '操作记录', width:'380',sortable: 'custom' },
{ istrue: true, prop: 'time', label: '操作时间' ,sortable: 'custom' },
];


const tableHandles1 = [
  // {label:"导入", handle:(that)=>that.startImport()},
  // {label:"确定选择", handle:(that)=>that.onselected()},
];
export default {
  name: 'goods',
  components: { cesTable, vxetablebase, dateRange, MyContainer, MyConfirmButton,inputYunhan, YhImgUpload },
  props: {
    ischoice: { type: Boolean, default: false },
  },
  data() {
    return {
      api,
      temperatureList,
      seasonList,
      that: this,
      whitedialogVisible: false,
      editForm: {},
      filter: {
        styleCode: null,
        goodsCode: null,
        goodsName: null,
        groupId: null,
        brandId: null,
        attributeTag:[],
        seasonOrFestivalTag:[],
        weatherTag:[],
        temperatureTag:[],
        isEnabled:1,
        unBrandId: ["23","12","141","150","10101"],
        virtualType: null
      },
      list: [],
      summaryarry: {},
      pager: { OrderBy: "modified", IsAsc: false },
      tableCols: tableCols,
      tableHandles: tableHandles1,
      platformList: [],
      shopList: [],
      groupList: [],
      brandList: [],
      dialogVisible: false,
      total: 0,
      sels: [],
      listLoading: false,
      pageLoading: false,
      uploadLoading: false,
      fileList: [],
      yesnoList: [
        { value: true, label: "是" },
        { value: false, label: "否" }
      ],
      selrows: [],
      alterVisible:false,
      setBrand:null,
      brandRadioList:[],
      queryBrand:null,
      queryRecord:{
        goodsCodes:null,
        orderBy:null,
        isAsc:false,
        currentPage:1,
        pageSize:50
      },
      recordListLoading:false,
      recordShow:false,
      recordCols,
      recordList:null,
      recordTotal:null,
      brandRequest:{
        orginList:null,
        brandIdStr:null,
      },
      showNoteVisible:false,
      recordSelRows:null,
      switchTable:"人为修改",
      syncRecordList:[],
      syncRecordCols,
      syncRecordListLoading:false,
      virtualTypeList:[],
      //查询操作  1列表查询 2自定义查询
      queryOperateType: 1,
      customizeVisible: false,
      customize: {
        list: [],
      },
      customizeConfigs: {
        symbol: [],
        fieldList: [],
        extList: []
      },
      customizeType: 1,
      exampleSymbol: null,
      exampleSymbolTip: null,
      isShow: true,
      customizeSaveBtnLoading: false
    }
  },
  async mounted() {
    await this.setGroupSelect();
    await this.setBandSelect();
    await this.getVirtualTypeList();
    // await this.getlist();
    await this.getCustomizeConfig();
  },
  methods: {
    async savewhitePic(){
        let newarr = [];
        if(this.editForm.goodFrontBackImgs){
            let imgarr = JSON.parse(this.editForm.goodFrontBackImgs);
            imgarr.map((item)=>{
                newarr.push(item.url)
            })
        }
        this.editForm.whiteBackImg = newarr.join(',');

        let params = {
            ...this.editForm
        }
        let res = await SetGoodsWhiteBackImg(params);
        console.log("上传成功",res)
        if(!res.success){
            return;
        }
        this.whitedialogVisible = false;
        this.getlist();
        this.list.map((item)=>{
            if(item.goodsCode == this.editForm.goodsCode){
                item.whiteBackImg = this.editForm.whiteBackImg;
            }
        })
        this.$message({
            message: '上传成功',
            type: 'success'
        });
        // this.getlist();

    },
    editrow(row){
        this.editForm.goodsCode = row.goodsCode;
        let newarr = [];
        if(row.whiteBackImg){
            let nowarr = row.whiteBackImg
            nowarr.forEach((item, index)=>{
                newarr.push({
                    url: item,
                    name: index
                })
            })
        }

        this.editForm.goodFrontBackImgs = JSON.stringify(newarr);
        this.whitedialogVisible = true;
        console.log("打印行", this.editForm.goodFrontBackImgs)

    },
    openIpt(row) {
      row.isEditPackCount = !row.isEditPackCount
      this.$nextTick(() => {
        this.$refs[`${row.goodsCode}packCount`].focus()
      })
    },
    async editPackCount(row) {
      const { success } = await ChangePackCount([{
        sku_id: row.goodsCode,
        pack_qty: row.packCount
      }])
      if (!success) return this.$message.error(`${row.goodsCode}修改标准装箱数失败`)
      this.$message.success(`${row.goodsCode}修改标准装箱数成功`)
      row.isEditPackCount = false;
    },
    async syncJstSku(){
      if(this.filter.goodsCode){
        const resp=await SyncGoodsByGoodCodes({codes:this.filter.goodsCode}) ;
        if(resp&&resp.success)
          this.$message.success("正在同步中，请稍等查看");
        else
          this.$message.error("同步失败");
      }
      else{
        this.$message.error("请输入有效的商品编码");
      }
    },
    closealrt(){
      this.queryBrand = "";
      this.alterVisible = false;
    },
    async setGroupSelect() {
      const res = await getGroupKeyValue({});
      this.groupList = res.data;
    },
    async setBandSelect() {
      var res = await getAllProBrand();
      if (!res?.success) return;
      this.brandList = res.data;
    },
    async getVirtualTypeList() {
      var res = await request.post(`${this.api}GetShipperFxNameList`)
      if (res?.success) {
        this.virtualTypeList = res?.data ?? [];
      } else {
        this.virtualTypeList = [];
      }
    },
    //获取查询条件
    getCondition() {
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = { ...pager, ...page, ... this.filter }
      return params;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    onShowChoice() {
      // this.ischoice=true;
      this.$refs.table.clearSelection();
    },
    //分页查询
    async getlist() {
      this.customizeType = 1;

      var params = this.getCondition();
      if (params === false) {
        return;
      }
      this.pageLoading = true;
      var res = await getList(params);
      this.pageLoading = false;
      if (!res?.success) {
        return
      }
      res.data.list.map((item)=>{
        if(item.whiteBackImg){
            item.whiteBackImg = item.whiteBackImg.split(',');
        }else{
            item.whiteBackImg = [];
        }
      })
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry = res.data.summary;
      data.forEach(d => {
        d._loading = false;
        d.id = d.goodsCode;
      })
      data.forEach(item =>{
        item.isEditPackCount = false;
      })
      this.list = data;
      this.recordSelRows=[];
    },
    async onPageChange() {
      if (this.customizeType == 1)
        await this.onSearch();
      else
        await this.onCustomizeSearch();
    },
    //排序查询
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        var orderBy = column.prop;
        this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      if (this.customizeType == 1)
        await this.onSearch();
      else
        await this.onCustomizeSearch();
    },
    selsChange: function (sels) {
      this.sels = sels
    },
    selectchange: function (rows, row) {
      this.recordSelRows=[]
      rows.forEach(f => {
        this.selrows.push(f);
        this.recordSelRows.push(f);
      })
      let _this = this;
      if (rows.length > 0) {
        var a = [];
        rows.forEach(element => {
          let b = _this.list.indexOf(element);
          a.push(b + 1);
        });

        let d = _this.list.indexOf(row);

        var b = Math.min(...a)
        var c = Math.max(...a)

        a.push(d);
        if (d < b) {
          var b = _this.list.indexOf(row);
          var c = Math.max(...a)
        } else if (d > c) {
          var b = Math.min(...a) - 1
          var c = Math.max(...a)
        } else {
          var b = Math.min(...a) - 1
          var c = _this.list.indexOf(row) + 1;
        }

        let neww = [b, c];
        _this.selectarray = neww;
      }
    },
    callback(val) {
      this.selrows = [...val];
    },
    async getchoicelist() {
      if (!this.selrows || this.selrows.length == 0)
        this.$message({ message: "你还没有选择", type: "warning", });
      return this.selrows
    },
    async getchoicelistOnly() {
      if (!this.selrows || this.selrows.length == 0)
        this.$message({ message: "请选择一条数据，", type: "warning", });
      if (!this.selrows || this.selrows.length > 1)
        this.$message({ message: "只能选择一条数据", type: "warning", });
      return this.selrows
    },
    startImport() {
      this.dialogVisible = true;
    },
    cancelImport() {
      this.dialogVisible = false;
    },
    beforeRemove() {
      return false;
    },
    //上传成功
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      debugger
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    async uploadFile(item) {
      debugger
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      this.uploadLoading = true;
      const res = await importData(form);
      if (res?.success) {
        this.$message({ message: '上传成功,正在导入中...', type: "success", });
      }
      else {
        this.$message({ message: res.message, type: "warning", });
      }
      this.uploadLoading = false;
      this.fileList = [];
    },
    uploadChange(file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for (var i = 0; i < fileList.length; i++) {
          if (fileList[i].status == "success")
            list.push(fileList[i]);
          else
            list.push(fileList[i].raw);
        }
        this.fileList = list;
      }
    },
    showAlter()
    {

      if(this.recordSelRows==null||this.recordSelRows.length<1)
      {
        this.$message.error("请先选择商品");
        return;
      }
      this.brandRadioList=this.deepClone(this.brandList);
      this.brandSort(this.brandRadioList);
      this.setBrand=null;
      if(this.recordSelRows.length==1)
      {
        this.setBrand=this.recordSelRows[0].brandId?String(this.recordSelRows[0].brandId):null;
      }
      this.alterVisible=true;
    },
   async showNote()
    {
      this.queryRecord.goodsCodes=null;
      this.queryRecord.currentPage=1;
      this.$refs.pager2.setPage(1);
      this.getRecordList();
      this.showNoteVisible=true;
    },

    deepClone(arr)
    {
      let tmp = [];
      arr.forEach(item=>{
        tmp.push(item);
      });
      return tmp;
    },
    brandSort(arr)
    {
      for (let i = 0; i< arr.length; i++) {
        for (let j = i+1; j < arr.length; j++) {
          if(arr[j].value=="★季节款-隔年卖-不进货")
          {
            let tmp = arr[0];
            arr[0]=arr[j];
            arr[j]=tmp;
          }
          if(arr[j].value=="★卖完下架-不进货")
          {
            let tmp = arr[1];
            arr[1]=arr[j];
            arr[j]=tmp;
          }
          if(arr[j].value=="★清理滞销-不进货")
          {
            let tmp = arr[2];
            arr[2]=arr[j];
            arr[j]=tmp;
          }
          if(arr[j].value=="★待下架-处理")
          {
            let tmp = arr[3];
            arr[3]=arr[j];
            arr[j]=tmp;
          }
          if(arr[j].value=="★已下架-禁用")
          {
            let tmp = arr[4];
            arr[4]=arr[j];
            arr[j]=tmp;
          }
          if(arr[j].value=="☆无人认领")
          {
            let tmp = arr[5];
            arr[5]=arr[j];
            arr[j]=tmp;
          }
          if(arr[j].value=="☆代拍产品")
          {
            let tmp = arr[6];
            arr[6]=arr[j];
            arr[j]=tmp;
          }
          if(arr[j].value=="☆耗材包材")
          {
            let tmp = arr[7];
            arr[7]=arr[j];
            arr[j]=tmp;
          }
          if(arr[j].value=="☆系统挂靠")
          {
            let tmp = arr[8];
            arr[8]=arr[j];
            arr[j]=tmp;
          }
          if(arr[j].value=="☆供应链代发商品")
          {
            let tmp = arr[9];
            arr[9]=arr[j];
            arr[j]=tmp;
          }
        }
      }
    },
    brandFilter()
    {
      let tmp = [];
      if(this.queryBrand)
      {
         this.brandList.forEach(item=>{
          if(item.value.includes(this.queryBrand))
          {
            tmp.push(item)
          }
         });
      }else{
        tmp = this.brandList;
      }
       this.brandSort(tmp);
      this.brandRadioList=tmp;
    },
    async submitChange() {
      var validates = this.filter.goodsCode == null ? [] : this.filter.goodsCode.split(",");
      var isValidate = false;
      this.recordSelRows.forEach(row => {
        row.id = 0
        if (!validates.includes(row.goodsCode)) {
          isValidate = true;
        }
      });
      if (isValidate) {
        this.$confirm('所选商品编码不在商品编码搜索框内，是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            this.brandRequest.orginList = this.recordSelRows.map(item => ({
              ...item,
              whiteBackImg: Array.isArray(item.whiteBackImg) ? item.whiteBackImg.join(',') : item.whiteBackImg
            }));
            this.brandRequest.brandIdStr = this.setBrand;

            let filterAndPages=this.getCondition();

            var { success, msg } = await changeBrandAndJstSync({...filterAndPages,...this.brandRequest});
            if (success) {
              this.alterVisible = false;
              this.$message.success("修改成功");
              this.getlist();
            }
          })
          .catch(() => {
          })
      } else {
        this.brandRequest.orginList = this.recordSelRows.map(item => ({
          ...item,
          whiteBackImg: Array.isArray(item.whiteBackImg) ? item.whiteBackImg.join(',') : item.whiteBackImg
        }));
        this.brandRequest.brandIdStr = this.setBrand;
        let filterAndPages=this.getCondition();
        var { success, msg } = await changeBrandAndJstSync({...filterAndPages,...this.brandRequest});
        if (success) {
          this.alterVisible = false;
          this.$message.success("修改成功");
          this.getlist();
        }
      }
    },
   goodsCodeCallBack(val)
   {
      // this.queryRecord.goodsCodes = val;
      // let values= this.queryRecord.goodsCodes.replace(/[^\w,+，-]/g, '');
      // // 按逗号分割输入值
      // let segments= values.split(/[,，]/);
      // // 更新输入框的值，只保留有效的数字和逗号部分
      // this.queryBrand.goodsCodes = segments.join(',');
      this.queryRecord.goodsCodes = val;
      // this.queryBrand.goodsCodes = val.join(',');

   },
   async getRecordList()
   {
      if(this.switchTable=="人为修改")
      {
        this.getErpRecordList();
      }else{
        this.getSyncRecordList();
      }
   },
   //erp同步到聚水潭的操作记录
   async getErpRecordList()
    {
      this.recordListLoading=true;
      const {data,success} = await getBrandChangeLog(this.queryRecord);
      if(success)
      {
        this.recordList = data?.list;
        this.recordTotal = data?.total;
      }
      this.recordListLoading=false;
    },
    //聚水潭同步到erp的操作记录
    async getSyncRecordList()
    {
      this.syncRecordListLoading=true;
      const {data,success} = await getBrandChangeSyncLog(this.queryRecord);
      if(success)
      {
        this.syncRecordList = data?.list;
        this.recordTotal = data?.total;
      }
      this.syncRecordListLoading=false;
    },
       //每页数量改变
       Sizechange(val) {
      this.queryRecord.currentPage = 1;
      this.queryRecord.pageSize = val;
      this.getRecordList();
    },
    //当前页改变
    Pagechange(val) {
      this.queryRecord.currentPage = val;
      this.getRecordList();
    },
    async recordSortChange({ order, prop }) {
      if (prop) {
        this.queryRecord.orderBy = prop
        this.queryRecord.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getRecordList();
      }
    },
  async syncRecordSortChange({ order, prop }) {
      if (prop) {
        this.queryRecord.orderBy = prop
        this.queryRecord.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getRecordList();
      }
    },
    multiGoodsCodeCallBack(val)
    {
      // this.filter.goodsCode = val;
      // let values= this.filter.goodsCode.replace(/[^\w,+，-]/g, '');
      // // 按逗号分割输入值
      // let segments= values.split(/[,，]/);
      // // 更新输入框的值，只保留有效的数字和逗号部分
      // this.filter.goodsCode = segments.join(',');

      this.filter.goodsCode = val;
    },
    multiStyleCodeCallBack(val)
    {
      // this.filter.styleCode = val;
      // let values= this.filter.styleCode.replace(/[^\w,+，-]/g, '');
      // // 按逗号分割输入值
      // let segments= values.split(/[,，]/);
      // // 更新输入框的值，只保留有效的数字和逗号部分
      // this.filter.styleCode = segments.join(',');

      this.filter.styleCode = val;
    },
    RecordSwitch()
    {
      this.queryRecord.orderBy = null;
      this.queryRecord.isAsc = false;
      this.queryRecord.currentPage=1;
      this.$refs.pager2.setPage(1);
      this.getRecordList();
    },
    async onCustomizeSearchConfig() {
      this.customize.list = [];
      await this.getCustomizeFactor();
      this.customizeVisible = true;
    },
    async getCustomizeFactor() {
      var res = await getGoodsCustomizeQuery();
      if (res?.success){
        var list = [];
        res.data.forEach(item => {
          var row = {
            conditionSort: item.conditionSort,
            fieldName: item.fieldName,
            fieldCode: item.fieldCode,
            fieldType: item.fieldType,
            conditionType: item.conditionType,
            queryValue: item.queryValue,
            queryValues: [],
            componentType: item.componentType,
            valList: [],
            symbolList: []
          }
          row.symbolList = this.customizeConfigs.symbol;

          this.onFieldChange(row);
          console.log('mod',row);
          if (row.componentType == 'dropdown' && (row.conditionType == 'BeIncluded' || row.conditionType == 'NotBeIncluded')){
            row.queryValues = item.queryValue.split(',');
          }else
          row.queryValue = item.queryValue;
          list.push(row);
        });
        this.customize.list = list;
      }
      else
        this.customize.list = [];
    },
    async getCustomizeConfig() {
      var res = await getCustomizeConfig();
      if (res?.success)
        this.customizeConfigs = res.data;
      else
        this.customizeConfigs = new {
          symbol: [],
          fieldList: [],
          extList: []
        };
    },
    onAddNewCustomize () {
      const maxSort = this.customize.list.reduce((max, item) => item.conditionSort > max ? item.conditionSort : max, 0);
      var newRow = {
        conditionSort: maxSort + 1,
        fieldName: null,
        fieldCode: null,
        fieldType: null,
        conditionType: null,
        queryValue: null,
        queryValues: [],
        componentType: null,
        valList: [],
        symbolList: []
      };
      newRow.symbolList = this.customizeConfigs.symbol;
      this.customize.list.push(newRow);
    },
    onDelCustomize(row,index) {
      this.customize.list.splice(index, 1)
    },
    onFieldChange(row) {
      var exts = this.customizeConfigs.extList.filter(ext => ext.key == row.fieldCode);
      if (exts && exts.length >= 1){
        var ext = exts[0];
        var fieldItem = JSON.parse(ext.value);
        row.componentType = fieldItem.Type;
        if (fieldItem.Key && fieldItem.Key != 'none'){
          if (fieldItem.Key == "dropdowngroup"){
            var list = [];
            this.groupList.forEach(item => {
              list.push({
                Key: item.value,
                Val: item.key
              });
            });
            row.valList = list;
          } else if (fieldItem.Key == "dropdownbrand"){
            var list = [];
            this.brandList.forEach(item => {
              list.push({
                Key: item.value,
                Val: item.key
              });
            });
            row.valList = list;
          }
        } else if (fieldItem.KvList && fieldItem.KvList.length > 1) {
          row.valList = fieldItem.KvList;
        }
      }else{
        row.componentType = "none";
        row.valList = [];
      }

      var field = this.customizeConfigs.fieldList.filter(item => item.fieldName == row.fieldCode)[0];
      row.fieldName = field.fieldLabel;
      row.fieldType = field.fieldType;

      if (row.componentType == 'dropdown'){
        var symbols = [];
        this.customizeConfigs.symbol.forEach(item => {
          if (item.key == 'Equal' || item.key == 'BeIncluded' || item.key == 'NotBeIncluded')
            symbols.push(item);
        });
        row.symbolList = symbols;
      } else if (row.fieldType == 'number' || row.fieldType == 'date') {
        var symbols = [];
        this.customizeConfigs.symbol.forEach(item => {
          if (item.key == 'MoreThan' || item.key == 'MoreThanOrEqual' || item.key == 'LessThan' || item.key == 'LessThanOrEqual' || item.key == 'Equal')
            symbols.push(item);
        });
        row.symbolList = symbols;
      } else {
        row.symbolList = this.customizeConfigs.symbol;
      }

      row.queryValue = null;
      row.queryValues = [];
    },
    onChangeConditionType(row) {
      this.isShow = false;
      row.queryValue = null;
      row.queryValues = [];
      setTimeout(()=>{
        this.isShow = true;
        this.$forceUpdate();
      },50)
    },
    async onSaveCustomize() {
      var params = [];
      var verifyFlag = true;
      if (this.customize.list.length > 0) {
        this.customize.list.forEach((item,index) => {
          if (!item.fieldCode) {
            this.$message.error("请选择第"+(index+1)+"行查询字段！");
            verifyFlag = false;
          }
          if (!item.conditionType) {
            this.$message.error("请选择第"+(index+1)+"行对比方式！");
            verifyFlag = false;
          }
          if (item.queryValue == null && item.queryValues.length == 0) {
            this.$message.error("请添加第"+(index+1)+"行查询值！");
            verifyFlag = false;
          }
        });
        this.customize.list.map((item,index) => {
          params.push({
            conditionSort: item.conditionSort,
            fieldName: item.fieldName,
            fieldCode: item.fieldCode,
            fieldType: item.fieldType,
            conditionType: item.conditionType,
            queryValue: item.queryValue,
            queryValues: item.queryValues,
            componentType: item.componentType
          });
        });
      }

      if (!verifyFlag)
        return;

      var param = {customizeList:params};
      this.customizeSaveBtnLoading = true;
      var res = await saveGoodsCustomizeQuery(param);
      this.customizeSaveBtnLoading = false;
      if (res?.success) {
        this.$message({ message: '保存成功！', type: "success" });
        this.customizeVisible = false;
      }
    },
    async onCustomizeSearch() {
      this.$refs.pager.setPage(1);
      await this.getListByCustomize();
    },
    async getListByCustomize(){
      this.customizeType = 2;

      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = { ...pager, ...page };
      this.pageLoading = true;
      var res = await getGoodsListByCustomizeQuery(params);
      this.pageLoading = false;
      if (!res?.success) {
        return;
      }

      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry = res.data.summary;
      data.forEach(d => {
        d._loading = false;
        d.id = d.goodsCode;
      })
      data.forEach(item =>{
        item.isEditPackCount = false;
      })
      this.list = data;
      this.recordSelRows=[];
    },
    onExampleSymbolChange() {
      var tip = null;
      this.customizeConfigs.symbol.forEach(item => {
        if (item.key == this.exampleSymbol){
          tip = item.value;
        }
      });
      this.exampleSymbolTip = tip;
    }
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary {
  margin-right: 7px;
}
.vForBr{
  display: inline-block;
  width:20%;
}
::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>
