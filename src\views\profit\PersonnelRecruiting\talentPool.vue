<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="">
                    <el-select ref="selectUpResId" v-model="chooseName" clearable style="width: 100px" size="mini"
                        @clear="() => { filter.ddDeptId = null }" placeholder="招聘部门">
                        <el-option hidden value="一级菜单" :label="chooseName"></el-option>
                        <el-tree style="width: 200px;" :data="deptList" :props="defaultProps" :expand-on-click-node="false"
                            :check-on-click-node="true" @node-click="handleNodeClick">
                        </el-tree>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.recruiterDDUserId" placeholder="招聘专员" style="width: 100px" size="mini"
                        clearable filterable>
                        <el-option v-for="item in recruiterList" :key="item.ddUserId" :label="item.userName"
                            :value="item.ddUserId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.initialTestResult" placeholder="初始结果" style="width: 100px" size="mini"
                        clearable>
                        <el-option label="通过" :value="true"></el-option>
                        <el-option label="未通过" :value="false"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.finalTestResult" placeholder="复试结果" style="width: 100px" size="mini"
                        clearable>
                        <el-option label="通过" :value="true"></el-option>
                        <el-option label="未通过" :value="false"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.positionName" placeholder="请输入岗位名称" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.name" placeholder="请输入人才名称" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.candidateFrom" placeholder="类型" style="width: 80px" size="mini" clearable>
                        <el-option label="全部" :value="null"></el-option>
                        <el-option label="社招" :value="0"></el-option>
                        <el-option label="内推" :value="1"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="">
                    <el-input v-model.trim="filter.keywords" style="width: 160px" :maxLength="100" placeholder="关键字查询"
                        clearable>
                        <el-tooltip slot="suffix" class="item" effect="dark" content="姓名、电话、部门、岗位、专员、初试部门、复试部门"
                            placement="bottom">
                            <i class="el-input__icon el-icon-question"></i>
                        </el-tooltip>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">筛选</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onAll(1)">一键入职</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onAll(0)">一键流失</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onAdd">新增人才</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onImportDrz">导入</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onImportModelDrz">下载模板</el-button>
                </el-form-item>
                
            </el-form>
        </template>
        <!--列表----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            style="height: calc(100vh - 230px);" :tableData='datalist' @select='selectchange' :isSelection="true"
            :tableCols='tableCols' :isSelectColumn='true' :customRowStyle="customRowStyle" :loading="listLoading"
            :summaryarry="summaryarry" :selectColumnHeight="'0px'" :isBorder="false">
            <template slot='extentbtn'> </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getDataList" />
        </template>
        <!-- 新增/编辑 -->
        <el-dialog :title="dialogTitle" :visible.sync="showDialogSon" width="50%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag>
            <div style="height: 65vh; overflow-x: hidden;">
                <talentInformation v-if="showDialogSon" ref="talentInformation" :candidateInfo="candidateInfo"
                    :isEdit="isEdit" @closeDialog="closeDialog" @closeLoading="() => { subLoad = false }"
                    @openLoading="() => { subLoad = true }"></talentInformation>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showDialogSon = false">取 消</el-button>
                    <!-- <my-confirm-button type="submit" @click="submitTalent" /> -->
                    <el-button type="primary" :loading="subLoad" @click="submitTalent">保存</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 人才流失 -->
        <el-dialog title="请输入人才流失原因" :visible.sync="showFinishDialog" width="20%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag>
            <el-form ref="loseForm" label-width="60px" :model="loseForm" :rules="loseRules">
                <el-form-item label="原因" prop="lostReason">
                    <el-select v-model="loseForm.lostReason" placeholder="流失原因" size="mini">
                        <el-option label="通勤时间过长" value="通勤时间过长"></el-option>
                        <el-option label="公司福利不好" value="公司福利不好"></el-option>
                        <el-option label="薪资太低" value="薪资太低"></el-option>
                        <el-option label="氛围不喜欢" value="氛围不喜欢"></el-option>
                        <el-option label="工作压力大" value="工作压力大"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="备注">
                    <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" placeholder="请输入流失备注"
                        v-model="loseForm.lostRemark" maxlength="80" show-word-limit>
                    </el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showFinishDialog = false">取 消</el-button>
                    <!-- <my-confirm-button type="submit" @click="loseSubmit" content="保存" title="保存" /> -->
                    <el-button type="primary" :loading="subLoad" @click="loseSubmit">保存</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 查看人才信息 -->
        <el-drawer title="人才信息" size="50%" :visible.sync="drawer" :direction="direction">
            <talentInformation v-if="drawer" :isEdit="isEdit" :candidateInfo="candidateInfo"></talentInformation>
        </el-drawer>
        <!-- 导入待入职 -->
        <el-dialog title="导入" :visible.sync="uploadDialogInfo.visible" width="30%" v-dialogDrag
            :close-on-click-modal="false">
            <span>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-upload ref="uploadDrz" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                            action accept=".xlsx" :file-list="uploadDialogInfo.fileList" :data="uploadDialogInfo.fileparm"
                            :http-request="onUploadFileDrz" :on-success="onUploadSuccessDrz" :on-change="onUploadChangeDrz"
                            :on-remove="onUploadRemoveDrz">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success"
                                :loading="uploadDialogInfo.uploadLoading" @click="onSubmitUploadDrz">{{
                                    (uploadDialogInfo.uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="uploadDialogInfo.visible = false">关闭</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>
<script>
import cesTable from "@/components/Table/tableforvedio.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import postionDialogform from "@/views/profit/PersonnelRecruiting/postionDialogform";
import talentInformation from "@/views/profit/PersonnelRecruiting/talentInformation";
import { AllDDDeptTreeNcWh, getDeptUsers } from '@/api/profit/personnel'
import {
    pageCandidateRc, getCandidateInfo, candidateLostBatch, joinEmployees, joinEmployee, exportCandidateRc, delCandidate,
    importHrCandidate
} from '@/api/profit/hr'

const tableCols = [
    { istrue: true, prop: 'positionName', align: 'left', label: '岗位名称', sortable: 'custom', width: "280" },
    { istrue: true, prop: 'department', align: 'left', label: '招聘部门', sortable: 'custom', },
    { istrue: true, prop: 'name', align: 'left', label: '姓名', type: "click", handle: (that, row) => that.editPostion(row.candidateId, 0), sortable: 'custom', },
    { istrue: true, prop: 'gender', align: 'left', label: '性别', formatter: (row) => row.gender == null ? '' : row.gender == 1 ? '男' : '女', sortable: 'custom' },
    { istrue: true, prop: 'phone', align: 'left', label: '联系电话', sortable: 'custom', },
    { istrue: true, prop: 'initialTestResult', align: 'left', label: '初试结果', formatter: (row) => row.initialTestResult == null ? '' : row.initialTestResult ? '通过' : '未通过' },
    { istrue: true, prop: 'finalTestResult', align: 'left', label: '复试结果', formatter: (row) => row.finalTestResult == null ? '' : row.finalTestResult ? '通过' : '未通过' },
    { istrue: true, prop: 'recruiter', align: 'left', label: '招聘专员', sortable: 'custom', },
    { istrue: true, prop: 'candidateFrom', align: 'center', label: '类型', formatter: (row) => row.candidateFromTxt, sortable: 'custom' },
    {
        istrue: true, type: "button", label: '操作', width: "240",
        btnList: [
            { label: "入职", permission: "", handle: (that, row) => that.finishPostion(row.candidateId) },
            { label: "流失", permission: "", handle: (that, row) => that.losePostion(row.candidateId) },
            { label: "编辑", permission: "", handle: (that, row) => that.editPostion(row.candidateId, 1) },
            { type: "danger", permission: "", label: "删除", handle: (that, row) => that.deletePostion(row.candidateId) }
        ]
    }
];

export default {
    name: "talentPool",//人才库
    components: {
        MyContainer, postionDialogform, MyConfirmButton
        , cesTable, talentInformation,
    },
    props: {
        showDialog: {
            type: Boolean,
            default: () => { return false; }
        },
        diologTitle: {
            type: String,
            default: () => { return ''; }
        },
    },
    watch: {
    },
    data() {
        return {
            subLoad: false,
            showDialogSon: this.showDialog,
            chooseId: '',
            chooseName: '',
            defaultProps: {
                children: 'childDeptList',
                label: 'name'
            },
            deptList: [],
            recruiterList: [],
            filter: {
                ddDeptId: null,//
                recruiterDDUserId: null,//
                name: null,//
                initialTestResult: null,//
                finalTestResult: null,//
                positionName: null,//
                queryType: 1,//1面试人才库、2预备人才库、3试用人才库、4正式人才库、5离职人才库
                candidateFrom: null,
                keywords: null,
            },
            loseForm: {
                candidateId: 0,
                lostReason: null,
                lostRemark: null
            },
            istLoading: false,
            summaryarry: {},
            datalist: [
            ],
            islook: false,
            total: 0,
            sels: [], // 列表选中列
            listLoading: false,
            that: this,
            pageLoading: false,
            pager: {},
            tableCols: tableCols,
            isEdit: false,
            showFinishDialog: false,//显示完成弹窗
            drawer: false,
            direction: 'rtl',
            candidateInfo: {},
            dialogTitle: null,
            loseRules: {
                lostReason: [
                    { required: true, message: '请选择流失原因', trigger: 'change' }
                ]
            },

            uploadDialogInfo: {
                visible: false,
                uploadLoading: false,
                fileList: [],
                fileparm: {},
            }
        };
    },
    watch: {
    },
    async created() {

    },
    async mounted() {
        this.getDeptList();
        this.getRecruiters();
        this.onSearch()
    },
    methods: {

        //获取招聘专员
        getRecruiters() {
            let params = {
                deptName: '招聘组,人事组,SSC&员工关系组,培训组',
                includeAllChildDpt: 1,
            }
            getDeptUsers(params).then(res => {
                if (res.success) {
                    this.recruiterList = res.data;
                } else {
                    this.$message({ message: res.msg, type: "danger" });
                }
            })
        },
        // 节点点击事件
        handleNodeClick(data) {
            // 配置树形组件点击节点后，设置选择器的值，配置组件的数据
            this.chooseName = data.name;
            this.filter.ddDeptId = data.dept_id;
            // 选择器执行完成后，使其失去焦点隐藏下拉框效果
            this.$refs.selectUpResId.blur();
        },
        // 获取部门列表
        async getDeptList() {
            await AllDDDeptTreeNcWh().then(res => {
                if (res.success) {
                    this.deptList = res.data.childDeptList;
                } else {
                    this.$message({ message: res.msg, type: "danger" });
                }
            })
        },
        // 保存人才信息
        async submitTalent() {
            await this.$refs.talentInformation.submitForm();
        },
        //关闭弹窗
        closeDialog() {
            this.showDialogSon = false;
            this.$refs.talentInformation.resetFrom();
            this.onSearch();
        },
        // 导出
        async onExport() {
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            var res = await exportCandidateRc(params);// 导出接口
            if (!res?.data) {
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '人才库' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        // 新增人才
        onAdd() {
            this.candidateInfo = {};
            this.showDialogSon = true;
            this.isEdit = true;
            this.dialogTitle = '新增人才';
        },
        // 筛选
        onSearch(filter) {
            this.$refs.pager.setPage(1);
            this.getDataList();
        },
        // 流失
        losePostion(candidateId) {
            this.showFinishDialog = true;
            this.loseForm.candidateId = candidateId;
        },
        loseSubmit() {
            let params = []
            params.push(this.loseForm);
            this.$refs.loseForm.validate((valid) => {
                if (valid) {
                    this.subLoad = true
                    candidateLostBatch(params).then(res => {
                        this.subLoad = false
                        if (res?.success) {
                            this.$message({ type: 'success', message: '已流失该条人才!' });
                        }
                        this.onSearch()
                        this.showFinishDialog = false;
                        this.loseForm = {
                            candidateId: 0,
                            lostReason: null,
                            lostRemark: null
                        }
                    })
                }
            })
        },
        //删除
        deletePostion(candidateId) {
            this.$confirm('是否确认删除该条人才资料?', '删除人才信息', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await delCandidate({ candidateId: candidateId })
                if (!res?.success) { return }
                this.$message({ type: 'success', message: '删除成功!' });
                this.onSearch()
            }).catch(() => {
                this.$message({ type: 'info', message: '已取消删除' });
            });
        },
        //完成
        finishPostion(candidateId) {
            this.$confirm('是否确认该员通过面试考核?', '入职申请', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await joinEmployee({ candidateId: candidateId })
                if (!res?.success) { return }
                if (res.success) {
                    this.$message({ type: 'success', message: '入职成功!请尽快完善员工资料。' });
                }
                this.onSearch()
            }).catch(() => {
                this.$message({ type: 'info', message: '取消操作!' });
            });
        },
        // 编辑
        async editPostion(candidateId, number) {
            await getCandidateInfo({ candidateId: candidateId }).then(res => {
                if (res.success) {
                    this.candidateInfo = res.data;
                }
            })
            if (number) {
                this.showDialogSon = true;
                this.dialogTitle = '编辑人才';
                this.isEdit = true;
            } else {
                this.drawer = true;
                this.isEdit = false;
            }

        },

        //获取数据
        async getDataList() {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            this.listLoading = true;
            const res = await pageCandidateRc(params);
            this.listLoading = false;
            this.total = res.data.total
            this.datalist = res.data.list;
            this.summaryarry = res.data.summary;
        },

        //列表排序
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        //多选事件
        selectchange: function (rows, row) {
            this.sels = [];
            rows.forEach(f => {
                let candidate = {
                    candidateId: f.candidateId,
                    lostReason: '批量流失',
                    lostRemark: null
                }
                this.sels.push(candidate);
            })
        },
        //批量
        async onAll(statue) {
            if (this.sels.length == 0) {
                this.$message({ type: 'warning', message: "请选择一个人员" });
                return;
            }
            if (statue) {
                this.$confirm("是否确定通过所选人员面试考核", "一键通过", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(async () => {
                    let ids = [];
                    ids = this.sels.map((item) => {
                        return item.candidateId;
                    })
                    joinEmployees(ids).then(res => {
                        if (res?.success) {
                            this.$message({
                                type: 'info',
                                dangerouslyUseHTMLString: true,
                                message: '<p>入职成功：' + res.data.successCount + '</p>' + '<p>入职失败：' + res.data.failCount + '</p>'
                            });
                            // this.$message({ type: 'success', message: '已流失选中人员!' });
                            this.sels = [];
                            this.onSearch();
                            var self = this;
                            setTimeout(() => { self.reload(); }, 100);

                        }

                    })

                });
            } else {
                this.$confirm("是否确定流失所选人员", "一键流失", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(async () => {
                    candidateLostBatch(this.sels).then(res => {
                        if (res?.success) {
                            this.$message({ type: 'success', message: '已流失选中人员!' });
                        }
                        this.sels = [];
                        this.onSearch();
                        var self = this;
                        setTimeout(() => { self.reload(); }, 100);

                    })

                });
            }

        },
        customRowStyle(row, index) {
            if (row.row?.isend && row.row.isend == 1) {
                let styleJson = {};
                styleJson.color = "rgb(216 216 216)";
                return styleJson
            } else {
                return null
            }

        },

        async onImportModelDrz() {
            window.open("/static/excel/profit/人事招聘待入职导入模板.xlsx", "_blank");
        },
        async onImportDrz() {
            this.uploadDialogInfo.visible = true;
        },
        async onUploadFileDrz(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadDialogInfo.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            var res = await importHrCandidate(form);
            if (res?.success) {
                this.$message({ message: "导入成功", type: "success" });
                this.uploadDialogInfo.visible = false;
                this.onSearch();
            }
            this.uploadDialogInfo.uploadLoading = false;
        },
        onUploadSuccessDrz(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.uploadDialogInfo.fileList = [];
        },
        async onUploadChangeDrz(file, fileList) {
            this.uploadDialogInfo.fileList = fileList;
        },
        onUploadRemoveDrz(file, fileList) {
            this.uploadDialogInfo.fileList = [];
        },
        onSubmitUploadDrz() {
            if (this.uploadDialogInfo.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.uploadDrz.submit();
        },
    },
};
</script>
<style scoped></style>
