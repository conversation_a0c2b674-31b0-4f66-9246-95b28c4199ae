<template>
  <div>
       <!-- 弹窗 star       -->
       <el-drawer title="我是标题" :visible.sync="drawer" size="590px;"  direction="rtl" :with-header="false">
        <el-form  :model="addForm"  ref="addForm"   label-width="100px" class="demo-ruleForm"  >
                      <div style="position: fixed; width: 650px; z-index: 999">
                      <div class="bzbjbt">
                          <span class="bjczbt" style="position: fixed; top: 30px;">编辑/操作</span>
                        <div style="float: right; width: 80px; height: 75px; line-height: 35px; margin-right: 60px;">
                          <el-button type="primary" @click="onSubmit">&nbsp;保&nbsp;存&nbsp;</el-button>

                        </div>
                        <!-- <div v-if="!islook" class="icon"
                          style="width: 80px;height: 70px; line-height: 70px; float: right;margin-right: 60px;">
                        </div> -->
                      </div>

                      <div class="bzbjlxx">
                      <div class="rwmcc">照片</div>
                      <!-- <el-select v-model="value1" placeholder="请选择" style="width: 270px;"> -->
                                  <el-image
                                  class="rwmc"
                                    style="width: 50px; height: 50px;display: inline-block"
                                    :src="addform.avatar"
                                    :preview-src-list="[addform.avatar]">
                                  </el-image>
                    <!-- </el-select> -->
                    </div>
                    <div class="bzbjlx" v-show="false" >
                        <div class="lxwz">id</div>
                        <div style="display: inline-block">
                            <el-input v-model="addform.userId"  placeholder="id" style="width: 170px;"></el-input>
                        </div>
                    </div>
                    <div class="bzbjlx" >
                        <div class="lxwz">姓名</div>
                        <div style="display: inline-block">
                            <span>{{ addform.userName }}</span>
                        </div>
                    </div>
                    <div class="bzbjlx">
                      <div class="lxwz">公司</div>
                      <div style="display: inline-block">
                            <span>{{ addform.companyName }}</span>
                        </div>
                    </div>

                    <div class="bzbjlx">
                      <div class="lxwz">工作岗位</div>
                        <div style="display: inline-block">
                            <span>{{ addform.workPositionStr }}</span>
                        </div>
                    </div>
                    <div class="bzbjlx">
                        <div class="lxwz">入职时间</div>
                        <!-- <div class="block" style="display: inline-block">
                          <span class="demonstration"></span>
                          <el-date-picker
                            v-model="value3"
                            type="date"
                            placeholder="选择日期"
                            style="width: 270px;">
                          </el-date-picker>
                        </div> -->
                        <div style="display: inline-block">
                            <span>{{ addform.joinedDateStr }}</span>
                        </div>
                    </div>
                    <div class="bzbjlx" >
                        <div class="lxwz">姓名（拼音）</div>
                        <div style="display: inline-block">
                            <el-input v-model="addform.cnUserName"  placeholder="姓名（拼音）" maxlength="50" style="width: 368px;"></el-input>
                        </div>
                    </div>
                    <div class="bzbjlx">
                      <div class="lxwz">性别</div>
                      <el-select v-model="addform.gender" placeholder="请选择" style="width: 368px;">
                      <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                    </div>
                    <div class="bzbjlx">
                        <div class="image" style="display: inline-block">岗位经验(年)</div>
                        <div class="block" style="width: 368px; display: inline-block">
                          <el-slider
                            v-model="addform.experiential"
                            show-input
                            :input-size="sliderInputSize"
                            @change="slichange(addform.experiential,1)"
                            :min="0" :max="50">
                          </el-slider>
                        </div>
                      </div>
                    <div class="bzbjlx">
                        <div class="image" style="display: inline-block">质量</div>
                        <div class="block" style="width: 368px; display: inline-block" >
                          <el-slider
                            v-model="addform.qualityRate"
                            show-input
                            :input-size="sliderInputSize"
                            @change="slichange(addform.qualityRate,2)"
                            :min="0" :max="100">
                          </el-slider>
                        </div>
                    </div>

                    <div class="bzbjlx">
                        <div class="image" style="display: inline-block">时效</div>
                        <div class="block" style="width: 368px; display: inline-block">
                          <el-slider
                            v-model="addform.timelinessRate"
                            show-input
                            :input-size="sliderInputSize"
                            @change="slichange(addform.timelinessRate,3)"
                            :min="0" :max="100">
                          </el-slider>
                        </div>
                    </div>

                    <div class="bzbjlx">
                        <div class="image" style="display: inline-block">沟通</div>
                        <div class="block" style="width: 368px; display: inline-block">
                          <el-slider
                            v-model="addform.communicateRate"
                            @change="slichange(addform.communicateRate,4)"
                            show-input
                            :input-size="sliderInputSize"
                            :min="0" :max="100">
                          </el-slider>
                        </div>
                    </div>

                    <div class="bzbjlx">
                        <div class="lxwz">岗位评分：</div>
                        <div class="block" style="display: inline-block">
                        <el-rate
                          v-model="addform.postScore"
                          show-score
                          text-color="#ff9900"
                          allow-half="true">
                        </el-rate>
                        </div>
                    </div>


                    <div class="bzbjlx" style="margin-top:20px">
                      <el-button type="danger" plain :size="'mini'" style="width: 480px;height:40px" @click="deleteEmployee(addform)">删除员工</el-button>
                    </div>
                  </div>
        </el-form>
                </el-drawer>
                  <!-- 弹窗 end -->

        <div class="xpzlshsp">
          <div class="xpzlshspbt"><span>个人资料</span></div>
          <div class="xpzlshmc">
            <div style="width: 1540px; margin: 0 auto; color: #555">
              <div class="checkbox-wrapper">
                <div class="checkbox-group">
                  <template>
                    <el-radio-group v-model="ck" @change="groupchange">
                      <el-radio :label="0">全部</el-radio>
                      <el-radio :label="1">照片拍摄</el-radio>
                      <el-radio :label="2">视频拍摄</el-radio>
                      <el-radio :label="3">详情排版</el-radio>
                      <el-radio :label="4">3D建模</el-radio>
                      <el-radio :label="5">短视频拍摄</el-radio>
                    </el-radio-group>
                  </template>
                </div>
                <!-- <el-checkbox :indeterminate="isIndeterminate" v-model="checkedAll" @change="handleCheckAll">全部</el-checkbox>
                <el-radio-group v-model="filter.checkedItems" @change="groupchange">
                  <el-radio v-for="item in workItems" :label="item.value" :key="item.value" :value="item.value">{{item.label}}</el-radio>
                </el-radio-group> -->
              </div>
            <div>

              </div>
            </div>
            <el-button type="primary" v-if="filter.checkedItems.length == 1" class="addBtn" @click="addEmployee">添加员工</el-button>

          </div>
          <div class="xpzlpdkz">
            <div class="pdzh">
              <div class="pdkh" style="padding-bottom: 6px" v-for="item in tableData" :key="item">
                <div class="pdbt">
                  <span>{{ item.workPositionStr }}</span>
                  <span style="float: right;" @click="openChat(item.workCategoryType)">
                    <el-link type="primary" :underline="false" style="color:#666;"><i class="el-icon-s-data"></i></el-link>
                  </span>
                </div>
                <div style="box-sizing: border-box; padding: 15px">
                  <div class="shcz">
                    <!-- <div class="ckpdvv"> -->
                      <el-image
                                  class="rwmc"
                                    style="width: 200px;
                                    height: 100px;
                                    display: inline-block;"
                                    :src="item.avatar"
                                    :preview-src-list="[item.avatar]">
                                  </el-image>

                    <!-- </div> -->
                    <div style="width:205px;height:50px;display: flex;justify-content: right;">
                      <div>
                        <div style="margin-bottom:6px;display: flex;justify-content: right;">
                        <span><el-tag size="mini">{{ item.companyName }}</el-tag></span>
                        <template v-if="item.gender==1">
                        <span style="margin-left:5px;"><el-tag size="mini"><i class="el-icon-male"></i></el-tag></span>
                      </template>
                      <template v-else-if="item.gender==2">
                        <span style="margin-left:5px;"><el-tag size="mini" type="danger"><i class="el-icon-female"></i></el-tag></span>
                      </template>
                      <template v-else>
                        <span style="margin-left:5px;"><el-tag size="mini"><i class="el-icon-male"></i></el-tag></span>
                        <span style="margin-left:5px;"><el-tag size="mini" type="danger"><i class="el-icon-female"></i></el-tag></span>
                      </template>
                        </div>
                        <div style="margin:2px 0;font-size:14px;color:#888;text-align:right;">
                        <span>岗位经验：{{ item.experiential }}年</span>
                        </div>
                      </div>
                    </div>
                  </div>
                <div style="box-sizing: border-box;padding:15px 0;">
                  <div style="font-size:16px;font-weight:bold;color:#666;margin:5px 0 5px 2px;"><span>{{ item.userName }}</span></div>
                  <div style="font-size:12px;color:#999;margin:0 0 0 2px;"><span>{{ item.cnUserName }}</span></div>
                </div>
                <div style="display: flex;">
                  <div style="width:163px;font-size:10px;color:#666;border-radius:3.5px;box-sizing:border-box;padding:3px 10px;background-color:#f5f5f5;;margin-right:5px">
                   <div><i class="el-icon-date">&nbsp;</i>入职时间：{{ item.joinedDateStr }}</div>
                </div>
                <el-button type="warning" plain size="mini" style="padding:2px 5px" v-if="item.employeeStatus == 2">离职</el-button>
                <el-button type="success" plain size="mini" style="padding:2px 5px" v-else>在职</el-button>
                </div>
                <div style="font-size:14px;font-weight:bold;color:#888;margin:15px 0 8px 2px;">
                  <span>工作能力</span>
                </div>
                <div style="display:flex;justify-content: center;margin-top:12px;" >
                  <div style="margin:0 5px;">
                    <div style="display:flex;justify-content: center;">
                    <span><el-progress type="circle" :percentage="item.qualityRate"  width="50" stroke-width="3"></el-progress></span>
                    </div>
                    <div style="width:60px;font-size:12px;color:#999;text-align:center;overflow:hidden;text-overflow:ellipsis;">
                      <span>质量</span>
                    </div>
                  </div>
                  <div style="margin:0 5px;">
                    <div style="display:flex;justify-content: center;">
                    <span><el-progress type="circle" :percentage="item.timelinessRate"  width="50" stroke-width="3"></el-progress></span>
                    </div>
                    <div style="width:60px;font-size:12px;color:#999;text-align:center;overflow:hidden;text-overflow:ellipsis;">
                      <span>时效</span>
                    </div>
                  </div>
                  <div style="margin:0 5px;">
                    <div style="display:flex;justify-content: center;">
                    <span><el-progress type="circle" :percentage="item.communicateRate"  width="50" stroke-width="3"></el-progress></span>
                    </div>
                    <div style="width:60px;font-size:12px;color:#999;text-align:center;overflow:hidden;text-overflow:ellipsis;">
                      <span>沟通</span>
                    </div>
                  </div>
                  <!-- <div style="margin:0 5px;">
                    <div style="display:flex;justify-content: center;">
                    <span><el-progress type="circle" :percentage="15"  width="45" stroke-width="3"></el-progress></span>
                    </div>
                    <div style="width:50px;font-size:12px;color:#999;text-align:center;overflow:hidden;text-overflow:ellipsis;">
                      <span>照片</span>
                    </div>
                  </div> -->
                  </div>
                  <div style="display:flex;margin-top:20px;">
                    <el-link :underline="false" style="width:105px;font-size:10px;text-align:center;border-radius:6px;box-sizing:border-box;padding:8px 5px;background-color:#f5f5f5;">
                      <div>发送钉钉消息</div>
                    </el-link>
                    <el-link :underline="false" style="width:105px;margin:0 5px;font-size:10px;text-align:center;border-radius:6px;box-sizing:border-box;padding:8px 5px;background-color:#f5f5f5;">
                      <div>岗位评分：{{ item.postScore }}</div>
                    </el-link>
                    <el-link  :underline="false" @click="editDraw(item.userId)"  type="primary" style="width:50px;font-size:14px;text-align:center;border-radius:6px;box-sizing:border-box;padding:8px 5px;background-color:#f5f5f5;">
                      <div><i class="el-icon-more"></i></div>
                    </el-link>


              <!-- ////////// -->
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- <div class="qxtj">
            <span style="float: left"></span>
            <span style="float: right">
              <el-button size="mini">取消</el-button>
              <el-button size="mini" type="primary">提交</el-button>
            </span>
          </div> -->
        </div>
      <!-- </el-form>
    </el-drawer> -->
    <el-dialog :title="editformTitle" top="20px" :visible.sync="editformdialog" width="50%" :close-on-click-modal="false"
            v-loading="editLoading" element-loading-text="拼命加载中" v-dialogDrag :append-to-body="true">
            <shootYuanGongList v-if="editformdialog" style="height: 480px;" ref="shootYuanGongList"></shootYuanGongList>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="editformdialog = false">取 消</el-button>
                    <my-confirm-button type="submit" @click="AddSubmit" />
                </span>
            </template>
        </el-dialog>

        <!-- 图表 -->
        <el-drawer title="评分统计" :visible.sync="buscharDialog.visible"   direction="btt" size="75%"
             class="drawer">
            <el-radio-group v-model="chartsType" class="chatType" @change="openChat(chartsType)"> 
                      <el-radio :label="1">照片拍摄</el-radio>
                      <el-radio :label="2">视频拍摄</el-radio>
                      <el-radio :label="3">详情排版</el-radio>
                      <el-radio :label="4">3D建模</el-radio>
                      <el-radio :label="5">短视频拍摄</el-radio>
            </el-radio-group>
            <span>
                <buschar v-if="!chatLoading" :analysisData="buscharDialog.data" style="height: 570px;"></buschar>
                <div v-else v-loading="chatLoading"> </div>
            </span>
        </el-drawer>
  </div>
</template>

<script>
const cityOptions = ['上海', '北京', '广州', '深圳'];
import { getEmployeeInfoList, editEmployeeInfo, getEmployeeInfoDetial, getShootingSetData,editEmployeeInfoCategory,delEmployeeInfoCategory,getEmployeeStatList } from '@/api/media/shootingset'
import shootYuanGongList from '@/views/media/shooting/shootAutoAssign/shootYuanGongList';
import MyConfirmButton from "@/components/my-confirm-button";
import checkPermission from '@/utils/permission'
import buschar from '@/components/Bus/buschar'
export default {
  name: "OneHelloWorld",
  components: { shootYuanGongList,MyConfirmButton,buschar },
  data() {
    return {
      checkedAll: false,
      ck:0,
      filter:{
        checkedItems: null,
      },
      buscharDialog: {
        visible: false,
        title: "",
        data: []
      },
      workItems: [],
      checkAll: false,
      checkedCities: ['上海', '北京'],
      cities: cityOptions,
      isIndeterminate: true,
      drawer: false,
      addLoading:false,
      sliderValue: 50,
      sliderInputSize: 'mini',
      pager: { OrderBy: "createdTime", IsAsc: false },
      tableData: { type: Array, default: () => [] },
      options: [{
          value: 1,
          label: '男'
        }, {
          value: 2,
          label: '女'
        }],
      addform: {
          userId: 0,
          userName:null,
          avatar: null,
          cnUserName: null,
          companyName: null,
          workPositionStr: null,
          qualityRate: 0,
          timelinessRate: 0,
          communicateRate: 0,
          experiential: 0,
          gender: null,
          postScore: 0
        },
      labelPosition: "left",
      ruleForm: {
        name: "",
        region: "",
        date1: "",
        date2: "",
        delivery: false,
        type: [],
        resource: "",
        desc: "",
      },
      editformdialog: false,//添加员工弹窗
      editformTitle: null,//弹窗标题
      workCategoryType: null,//工作类型
      editLoading: false,
      review:null,//删除员工之后的选中状态
      chartsType: null,//图表工作类型
      chatLoading: true,
    };
  },
  async mounted() {
   this.onSearch();
    },
  methods: {
    //打开图表
   async openChat(workCategoryType){
      this.chatLoading = true
      this.buscharDialog.visible = true
      const data = await getEmployeeStatList({workCategoryType})
      data.series.map((item) => {
        item.itemStyle = {
          "normal": {
            "label": {
              "show": true,
              "position": "top",
              "textStyle": {
                "fontSize": 14
              }
            }
          }
        }
        item.emphasis = {
          "focus": "series"
        }
        item.smooth = false;
      });
      this.buscharDialog.data = data
      this.chartsType = workCategoryType
      // this.buscharDialog.visible = true
      this.chatLoading = false
    },
    //删除员工
    async deleteEmployee (addform) {

      const params = {
        userId: addform.userId,
        workCategoryTypeList: this.filter.checkedItems
      };
      this.review = this.filter.checkedItems;
      this.$confirm('此操作将永久删除该员工, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
      }).then(async () => {
        const res = await delEmployeeInfoCategory(params)
        if (res.success) {
           //关闭弹窗
        this.drawer = false;
        //重新获取列表
          this.onSearch();
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
        }

        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    },
    //添加员工的提交按钮
    async AddSubmit () {
      this.editLoading = true;
      //从子组件中获取选中的员工id
      var selids = this.$refs.shootYuanGongList.getReturnSelect();
      const params = {
        userIdList: selids,
        workCategoryType: this.workCategoryType
      };
      const { data } = await editEmployeeInfoCategory(params);
      //本来想断言一下,但是后端返回的是null,那就算了
        this.editLoading = false;
      this.editformdialog = false;
      //重新获取列表
      this.onSearch();
    },
    //添加员工
    addEmployee () {
      //取出this.filter.checkedItems的第一个值
      this.workCategoryType = this.filter.checkedItems[0];
      console.log(this.workCategoryType,"this.workCategoryType");
      this.editformdialog = true;
      this.editformTitle = '添加员工';
    },
  slichange(e,i){
    var  num = Number(e.toString().match(/^\d+(?:\.\d{0,2})?/));
    if(i==1)
    {
      this.addform.experiential = num
    }else if(i==2)
    {
      this.addform.qualityRate = num
    }else if(i==3)
    {
      this.addform.timelinessRate = num
    }else if(i==4)
    {
      this.addform.communicateRate = num
    }

  },
    handleCheckAll(checked) {
      this.filter.checkedItems = [];
      if (checked) {
        this.filter.checkedItems = this.workItems.map(item => item.value);
        if (this.review) {
          this.filter.checkedItems = this.review
        }
      } else {
        this.filter.checkedItems = [];
      }
      this.getTaskList();
    },
 async groupchange() {
       this.getTaskList();
     },
 async  onSearch() {
      var res = await getShootingSetData({ setType: 11 });
      if(res?.success)
      {
        // this.workItems = res.data?.data?.map(item => { return { value: item.setId, label: item.sceneCode }; });
        this.workItems = [
          { label: '照片拍摄', value: 1 },
          { label: '视频拍摄', value: 2 },
          { label: '详情排版', value: 3 },
          { label: '3D建模', value: 4 },
          { label: '短视频拍摄', value: 5 }
        ]
        console.log(this.filter.checkedItems, 'filter.checkedItems');
      }
      this.handleCheckAll(true);
      //this.getTaskList();
  },
  async getTaskList(){
      if(this.ck==0){
        this.filter.checkedItems = [1,2,3,4,5];
      }else{
          this.filter.checkedItems = [this.ck];
        }
      const params = {
         "currentPage":1,
         "pageSize":200,
          ...this.pager,
          ...this.filter
      };
      this.listLoading = true;
      const {data} = await getEmployeeInfoList(params);
      this.listLoading = false;
      this.tableData = data.list;
  },
    async editDraw (uid) {
   if (!checkPermission(['sjsjb-ycglbj'])) {
      this.$message({
        message: '你没有权限编码信息!',
        type: 'warning'
      });
     return;
   }
      this.drawer=true;
      var res = await getEmployeeInfoDetial(uid);
    if(res?.success)
    {
      this.addform=res.data;
    }
  },
  async  onSubmit() {
          this.addLoading=true;
          var res = await editEmployeeInfo(this.addform);
            this.addLoading = false;
            if (!res?.success) { this.$message({ message: res.msg, type: 'error' }); return; }
            else {
                this.$message({ message: '编辑成功！', type: 'success' });
                this.addLoading = false;
                this.onSearch();
                this.drawer=false;
            }
      },

    open() {
        this.$alert('这是一段内容', '标题名称', {
          confirmButtonText: '确定',
          callback: action => {
            this.$message({
              type: 'info',
              message: `action: ${ action }`
            });
          }
        });
      },

    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          alert("submit!");
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  },
};

</script>

<style lang="scss" scoped>
.xpzlshsp {
  background-color: #fff;
}
.xpzlshsp .xpzlshspbt {
  height: 55px;
  background-color: rgb(255, 255, 255);
  box-sizing: border-box;
  padding: 20px 35px;
  font-size: 16px;
  color: #666;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
}

.xpzlshsp .xpzlshmc {
  min-width: 1650px;
  height: 55px;
  background-color: rgb(255, 255, 255);
  box-sizing: border-box;
  padding: 20px 0;
  font-size: 16px;
  color: #666;
  position:relative;
  left:-6px;
  box-shadow: 0px 3px 5px #eeeeee;
  z-index: 999;
  position: relative;
  .addBtn{
    position: absolute;
    right: 100px;
    top: 15px;
  }
}

.xpzlpdkz {
  min-width: 1650px;
  /* height: 630px; */
  background-color: #f3f6f7;
  box-sizing: border-box;
  padding: 15px 0px;
  overflow: auto;
}
.xpzlshsp .pdzh {
  width: 1550px;
  flex-wrap: wrap;
  overflow: hidden;

  padding: 0 35px;
  margin: 0 auto;
  display: flex;
}

.xpzlshsp .pdkh {
  width: 300px;
  background-color: rgb(255, 255, 255);
  margin: 5px;

}

.pdbt {
  width: 300px;
  height: 38px;
  line-height: 20px;
  font-size: 14px;
  color: #666;
  box-sizing: border-box;
  padding: 10px 12px 5px 12px;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  margin-bottom: 5px;
  background-color: rgb(255, 255, 255);
}

.pdsx {
  width: 300px;
  line-height: 20px;
  font-size: 14px;
  color: #888;
  box-sizing: border-box;
  padding: 5px 12px;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  background-color: rgb(255, 255, 255);
}

.xpzlshsp .ckpd,
.pspd {
  width: 80px;
  margin: 0 5px;
  display: inline-block;
}

.xpzlshsp .shcz {
  width: 100%;
  display: flex;
}

.xpzlshsp .ckpdv {
  width: 100%;
  height: 120px;
  margin-bottom: 5px;
  box-sizing: border-box;
  border: 1px solid #dcdfe6;
}

.xpzlshsp .ckpdvv {
  width: 65px;
  height: 65px;
  border-radius:8px;
  background-color: rgb(196, 255, 204);
}

.xpzlshsp .qxtj {
  height: 80px;
  background-color: rgb(255, 255, 255);
  box-sizing: border-box;
  padding: 20px 60px;
  border: 1px solid #dcdfe6;
  border-right: 0px;
  border-bottom: 0px;
  border-left: 0px;
}

.bzbjbt {
  height: 75px;
  width: 100%;
  background-color: rgb(255, 255, 255);
  font-size: 18px;
  color: #666;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 20px 35px;
}

.image{
  width: 112px;
  font-size: 14px;
  color: #666;
  line-height: 26px;
  display: inline-block;
  padding: 0px 0px;

}

.bzbjlx {
  width: 100%;
  height: 35px;
  box-sizing: border-box;
  padding: 0px 60px;
  display: flex;
  align-items: center;
}

.lxwz {
  width: 112px;
  font-size: 14px;
  color: #666;
  line-height: 26px;
  display: inline-block;
}
::v-deep  .bzbjrw .rwmc {
  width: 750px;
  height: 70px;
  background-color: rgb(255, 255, 255);
  float: left;
  box-shadow: 0px 3px 5px #eeeeee;
  box-sizing: border-box;
  padding: 0 56px;
}
::v-deep .bzbjrw .rwmc .xh {
  height: 65px;
  font-size: 18px;
  line-height: 68px;
  box-sizing: border-box;
  padding: 0 2px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #666;
}
.bjcz .bjczbt {
    height: 30px;
    font-size: 18px;
    line-height: 30px;
    color: #666;
    box-sizing: border-box;
    float: left;
    margin: 0 30px;
}
.rwmc {
    height: 65px;
    font-size: 18px;
    line-height: 68px;
    box-sizing: border-box;
    // padding: 0 2px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #666;
    // border-radius: 10px;
    border-top-left-radius:10px;
    border-top-right-radius:10px;
    border-bottom-left-radius:10px;
    border-bottom-right-radius:10px;
    max-width: 100px;
    max-height: 100px;
  }
  .rwmcc{
    width: 112px;
    font-size: 14px;
    color: #666;
    line-height: 26px;
    display: inline-block;
  }
  .bzbjlxx{
    width: 100%;
    height: 60px;
    box-sizing: border-box;
    padding: 0px 60px;
    display: flex;
    align-items: center;
  }
  .icon {
    height: 70px;
    font-size: 18px;
    line-height: 68px;
    box-sizing: border-box;
    margin-left: 10px;
    padding: 0 2px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #666;
}
.checkbox-wrapper {
  display: flex;
  align-items: center;
}
.checkbox-group {
  margin-left: 20px;
}
.rwmc ::v-deep img{
  max-height: 81px !important;
  max-width: 81px !important;
  border-radius: 8px !important;
}

.drawer{
  box-sizing: border-box;
}
.chatType{
  display: flex;
  justify-content: flex-end;
  margin: 20px 10px;
  box-sizing: border-box;
}
</style>
