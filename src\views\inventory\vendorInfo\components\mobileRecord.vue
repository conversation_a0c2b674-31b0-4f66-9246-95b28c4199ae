<template>
    <MyContainer>
        <template #header>
            <div class="header">
                <el-input placeholder="系列编码" v-model="ListInfo.styleCode" maxlength="50" class="publicMargin"
                    style="width: 220px;" clearable></el-input>
                <el-input placeholder="供应商名称" v-model="ListInfo.providerName" maxlength="50" class="publicMargin"
                    style="width: 220px;" clearable></el-input>
                <el-input placeholder="微信名称" v-model="ListInfo.wXNickName" maxlength="50" class="publicMargin"
                 style="width: 180px;" clearable></el-input>
                <el-select v-model="ListInfo.dockingUserId" placeholder="对接人" class="publicMargin" style="width: 220px;" clearable
                    filterable>
                    <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.dockingStatus" placeholder="对接状态" style="width: 220px" class="publicMargin"
                    clearable>
                    <el-option v-for="item in status" :key="item.label" :label="item.label" :value="item.label" />
                </el-select>
                <el-select v-model="ListInfo.position" placeholder="职位" class="publicMargin" style="width: 150px;"
                    clearable>
                    <el-option v-for="item in positionType" :key="item.label" :label="item.label" :value="item.label">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.IsExitProvider" placeholder="重复供应商" style="width: 170px" class="publicMargin"
                    clearable>
                    <el-option label="未重复" :value="2" />
                    <el-option label="重复" :value="1" />
                </el-select>
   
                <el-date-picker :picker-options="pickerOptions" style="width: 220px" v-model="timerange1" type="daterange" format="yyyy-MM-dd" @change="changedate('one')"
                  value-format="yyyy-MM-dd" range-separator="至" start-placeholder="初次咨询时间" end-placeholder="初次咨询时间" clearable>
                </el-date-picker> 
                <el-date-picker :picker-options="pickerOptions" style="width: 220px" v-model="timerange2" type="daterange" format="yyyy-MM-dd" @change="changedate('two')"
                  value-format="yyyy-MM-dd" range-separator="至" start-placeholder="最新咨询时间" end-placeholder="最新咨询时间" clearable>
                </el-date-picker> 
               
                <el-button type="primary" @click="searchList">查询</el-button>
            </div>
        </template>
        <vxetablebase :id="'mobileRecord202408041614_1'" ref="doesnTable" :tableData="notTableData" :tableCols="tableCols3" :is-index="true" :that="that"
            style="width: 100%; height: 650px; margin: 0" @sortchange='sortchange'
             :loading="listLoading" class="doesn">
        </vxetablebase>
        <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="detailTotal"
            @page-change="detailPagechange" @size-change="detailSizechange" />
   
        <!-- 弹层部分 -->
        <el-dialog title="采购记录" :visible.sync="nameVisible" width="40%" :before-close="handleClose" v-dialogDrag>
            <vxetablebase :id="'mobileRecord202408041614_2'" ref="detailTable" :tableData="nameTableData" :tableCols="tableCols5" :is-index="true" :that="that"
                :showsummary="true"  :summaryarry="nameSummary"  style="width: 100%; height: 500px" @sortchange='sortchange3'>
            </vxetablebase>
            <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="nameTotal"
                @page-change="namePagechange" @size-change="nameSizechange" style="margin-top: 40px;" />
        </el-dialog>
        <el-dialog title="对接记录" :visible.sync="RecordsVisible" width="60%" :before-close="()=>{RecordsVisible = false}"  v-dialogDrag>
           <vxetablebase :id="'mobileRecord202408041614_3'" ref="detailTable" :tableData="doTableData" :tableCols="tableCols4" :is-index="true" :that="that"
               :showsummary="true" style="width: 100%; height: 500px" @sortchange='sortchange2' class="detail">
           </vxetablebase>
           <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="recordsTotal"
               @page-change="dockingRecordsPagechange" @size-change="dockingRecordsSizechange" style="margin-top: 40px;" />
       </el-dialog>
   
        <el-dialog title="操作" :visible.sync="operateVisible" width="50%" :before-close="handleClose" v-dialogDrag>
           <el-select v-model="RecordsInfo.dockingStatus" placeholder="对接状态" style="width: 220px;margin-bottom: 10px;">
               <el-option v-for="item in status" :key="item.label" :label="item.label" :value="item.label" />
           </el-select>
           <el-input type="textarea" placeholder="请输入内容" v-model="RecordsInfo.result" maxlength="300" show-word-limit
               rows="5" />
           <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" v-if="RecordsInfo.picLists.length!=9" :limit="9"
               :on-success="handleSuccess" :file-list="picFileList" multiple :show-file-list="false"
               accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
               <el-button class="addsc" type="text">上传图片</el-button>
           </el-upload>
           <div style="color: red; font-size: 14px;" v-if="RecordsInfo.picLists.length==9" >最多可上传9张图片！</div>
           <div v-if="RecordsInfo.picLists.length > 0">
               <div class="imageList_box">
                   <div class="imageList" v-for="(item,i) in RecordsInfo.picLists" :key="i">
                       <el-image class="imgcss" style="width: 100px; height: 100px" :src="item"
                           :preview-src-list="RecordsInfo.picLists">
                       </el-image>
                       <span class="del" @click="delImg(item, i)">x</span>
                   </div>
               </div>
   
           </div>
           <div style="text-align: right;margin-top: 20px;">
               <el-button type="primary" @click="operateVisible = false">取消</el-button>
               <el-button type="primary" @click="operateSubmit">提交</el-button>
           </div>
       </el-dialog>
    </MyContainer>
   </template>
   
   <script>
   import MyContainer from "@/components/my-container";
   import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
   import { pickerOptions } from "@/utils/tools";
   import {
    getBrandUsers,
    getProviderQuotationCGRecordPageList,
    getPhoneConsultRecordPageList,
    addPhoneConsultDockingResult,
    getPhoneConsultDockingResultList
   }
    from '@/api/openPlatform/ProviderQuotation'

    // import {
    // getProviderQuotationCGRecordPageList,
    // getPhoneConsultRecordPageList,
    // }
    // from '@/api/openPlatform/ProviderQuotation'
   import { pagePurchaseOrderByProviderNameAsync } from '@/api/inventory/purchase'
   import { getAllProBrand } from '@/api/inventory/warehouse'
   const options = [
    {
        value: '1',
        label: '是'
    },
    {
        value: '0',
        label: '否'
    }
   ]
   
   const positionType = [
    {
        label: '老板'
    },
    {
        label: '业务员'
    },
    {
        label: '经理'
    }
   ]
   
   const sourceType = [
    {
        label: '朋友圈'
    },
    {
        label: '聊天'
    },
    {
        label: '其他'
    }
   ]
   const status = [
       {
           label: '待沟通',
           value: '待沟通'
       },
       {
           label: '沟通中',
           value: '沟通中'
       },
       {
           label: '寄样中',
           value: '寄样中'
       },
       {
           label: '采购中',
           value: '采购中'
       },
       {
           label: '采购完成',
           value: '采购完成'
       },
       {
           label: '不适合',
           value: '不适合'
       },
   ]
   //未提交
   const tableCols3 = [
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: 95, treeNode: true },
    {
        istrue: true, prop: 'setStylePic', label: '系列编码图片', type: 'treeimages', isImgtree: true, width: 100, formatter: (row) => {
            if (row.setStylePic) {
                return row.setStylePic
            } else {
                return row.stylePic
            }
        }
    },
    { istrue: true, prop: 'providerName', label: '供应商名称', sortable: 'custom', width: 130, },
    { istrue: true, prop: 'position', label: '职位', width: 70, sortable: 'custom' },
   
    // { istrue: true, prop: 'brandName', label: '采购', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'sourceName', label: '推荐人', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'sourceType', label: '来源', sortable: 'custom', width: 75, },
    ////////////////////////////////////
    { istrue: true, prop: 'wxNickName', label: '微信名称', sortable: 'custom', width: 130, },
    { istrue: true, prop: 'wxPic', label: '微信头像', type: 'images', width: 130, },
    //对接状态
    { istrue: true, prop: 'dockingStatus', label: '对接状态', sortable: 'custom', width: 95, },
    
    { istrue: true, prop: 'firstConsultDate', label: '初次咨询时间', sortable: 'custom', width: 125, },
    { istrue: true, prop: 'lastConsultDate', label: '最新咨询时间', sortable: 'custom', width: 125, },
    //  { istrue: true, prop: 'dockingCount', label: '对接次数', sortable: 'custom', width: 95, type: 'click', handle: (that, row) => that.operate(row) },
    { istrue: true, prop: 'dockingCount', label: '对接次数', sortable: 'custom', width: 75, type: 'click',  handle: (that, row) => that.dockingRecords(row) },

    { istrue: true, prop: 'dockUserName', label: '对接人', width: 95, },
    {
        istrue: true, prop: 'address', label: '归属地', sortable: 'custom', width: 200, formatter: (row) => {
            if (row.address != null || row.ip != null) {
                return row.address + `(${row.ip})`
            } else {
                return ''
            }
        }
    },
    { istrue: true, display: true, label: '操作', style: "color: rgb(72, 132, 243);cursor:pointer;", fixed: 'right', width: 'auto', formatter: (row) => '变更对接状态', type: 'click', handle: (that, row) => that.operate(row) },
   
   ]
   //采购记录
   const tableCols5 = [
    { istrue: true, prop: 'purchaseDate', label: '采购时间', sortable: 'custom' },
    { istrue: true, prop: 'buyNo', label: '采购单号', sortable: 'custom' },
    { istrue: true, prop: 'totalAmont', label: '采购金额', sortable: 'custom' },
    { istrue: true, prop: 'count', label: '采购量', sortable: 'custom' },
   ]
   
   //对接记录
   const tableCols4 = [
       { istrue: true, prop: 'createdUserName', label: '对接人', sortable: 'custom', width: 130, },
       { istrue: true, prop: 'createdTime', label: '对接时间', sortable: 'custom', width: 130, },
       { istrue: true, prop: 'dockingStatus', label: '对接状态', sortable: 'custom', width: 130, },
       { istrue: true, prop: 'result', label: '对接结果', sortable: 'custom', width: 130, },
       { istrue: true, prop: 'picJsons', label: '图片', type: 'images', width: 'auto', },
   ]
   
   export default {
    components: { MyContainer, vxetablebase, },
    name: "vendorNotSubmitted",
    data() {
        return {
            that: this,
            timerange1: [],
            timerange2: [],
            neiTableData: [],
            doTableData: [],//对接记录
            ListInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: '',//排序字段
                isAsc: false,//是否升序
                styleCode: null,//系列编码
                categoryId: null,//品类id
                openId: null,//openId
                goodCode: null,//商品编码
                isBY: null,//是否包邮
                isContaisTax: null,//是否含税
                providerName: null,//供应商名称
                dockingStatus: null,//对接状态
                sourceType: null,//来源
                brandId: null,//采购人员id
                position: null,//职位
                timerange: []
            },
            logDetail: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                openId: null,//openId
                styleCode: null,//系列编码
                orderBy: null,//排序字段
                isAsc: true,//是否升序
            },
            RecordsInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                recordId: null,//记录id
                newGoodsRecordId: null,//新品记录id
                isDockingCG: 0,//是否对接采购 0已提交 1未提交
                styleCode: null,//系列编码
                openId: null,//openId
                result: null,//对接结果
                dockingStatus: null,//对接状态
                pics: null,
                picLists: []//图片列表
            },//对接记录请求参数
            nameInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                supplier: null,//供应商名称
            },
            tableCols4: tableCols4,
            sourceType,//来源
            positionType,//职位
            tableCols3,//未提交
            tableCols5,//点击供应商名字
            notTableData: [],//已提交
            nameTableData: [],//点击供应商名字
            brandList: [],//采购列表
            timerange: [],
            detailTotal: 0,//已提交总数
            nameTotal: 0,//供应商报价详情总数
            listLoading: true,//加载中
            nameVisible: false,//点击供应商名字弹层
            recordsTotal: 0,//对接记录总数
            options,//是否包邮,是否含税
            status,//状态
            operateVisible: false,//操作弹层
            pickerOptions: pickerOptions,
            nameSummary:null,
            picFileList: [],
            RecordsVisible: false,
        };
    },
    mounted() {
        this.getNotCommitList()
        this.getBrandList()
    },
    methods: {
       //对接记录弹层当前页改变
       dockingRecordsPagechange(val) {
           this.RecordsInfo.currentPage = val;
           this.dockingRecords(this.RecordsInfo)
       },
       //对接记录弹层每页数量改变
       dockingRecordsSizechange(val) {
           this.RecordsInfo.currentPage = 1;
           this.RecordsInfo.pageSize = val;
           this.dockingRecords(this.RecordsInfo)
       },
       sortchange2({ order, prop }) {
           if (prop) {
               this.RecordsInfo.orderBy = prop
               this.RecordsInfo.isAsc = order.indexOf("descending") == -1 ? true : false
               this.dockingRecords(this.RecordsInfo)
           }
       },
       //已提交对接记录
       async dockingRecords(row) {
           this.RecordsInfo.picLists = []
           if (!this.RecordsInfo.recordId) {
               this.RecordsInfo.recordId = row.id
           }
           this.RecordsInfo.openId = row.openId
           this.RecordsInfo.styleCode = row.styleCode
           const { data, success } = await getPhoneConsultDockingResultList(this.RecordsInfo)
           if (success) {
               this.doTableData = data.list
               this.recordsTotal = data.total
               this.RecordsInfo.orderBy = null
               this.RecordsVisible = true
           } else {
               this.$message.error('获取对接记录失败')
           }
       },
       async operateSubmit() {
           if (this.RecordsInfo.result == null || this.RecordsInfo.result == '') {
               this.$message.error('请输入内容')
               return
           }
           if (this.RecordsInfo.picLists.length == 0) {
               this.$message.error('请上传图片')
               return
           }
           if (this.RecordsInfo.dockingStatus == null || this.RecordsInfo.dockingStatus == '') {
               this.$message.error('请选择对接状态')
               return
           }
           this.RecordsInfo.recordId = null
           const { success } = await addPhoneConsultDockingResult(this.RecordsInfo)
           if (success) {
               this.$message.success('操作成功')
           } else {
               this.$message.error('操作失败')
           }
           this.getNotCommitList()
           this.operateVisible = false
       },
       delImg(item, i) {
           this.RecordsInfo.picLists.splice(i, 1)
       },
       //图片上传成功回调
       async handleSuccess({ data }) {
           this.RecordsInfo.picLists.push(data.url)
       },
       //操作
       async operate(row) {
           this.RecordsInfo.picLists = []
           this.picFileList = []//清空图片上传列表
           this.RecordsInfo.result = null//清空对接结果
           this.RecordsInfo.dockingStatus = null//清空对接状态
           this.RecordsInfo.newGoodsRecordId = row.id
           this.RecordsInfo.recordId = row.id
           this.RecordsInfo.openId = row.openId
           this.RecordsInfo.styleCode = row.styleCode
           this.operateVisible = true
       },
        changedate(type){
          if(type == 'one'){
           this.ListInfo.firstConsultStartDate = this.timerange1[0]
           this.ListInfo.firstConsultEndDate = this.timerange1[1]
   
          }else if(type== 'two'){
           this.ListInfo.lastConsultStartDate = this.timerange2[0]
           this.ListInfo.lastConsultEndDate = this.timerange2[1]
          }
        },
        //获取采购列表
        async getBrandList() {
            const { data, success } = await getBrandUsers({ userName: null })
            if (success) {
                this.brandList = data.map(item => {
                    return {
                        label: item.value,
                        value: item.key
                    }
                })
            } else {
                this.$message.error('获取采购人员列表失败')
            }
        },
        //点击供应商名称打开弹层
        async openNameDialog(row) {
            this.nameInfo.supplier = row.providerName ? row.providerName : row.supplier
            const { data, success } = await pagePurchaseOrderByProviderNameAsync(this.nameInfo)
            if (success) {
                this.nameTableData = data.list
                this.nameTotal = data.total
                this.nameSummary = data.summary
                this.nameVisible = true
                this.nameInfo.orderBy = null
            } else {
                this.$message.error('获取供应商采购记录失败')
            }
        },
        handleClose() {
            this.operateVisible = false
        },
        //对接记录弹层每页数量改变
        nameSizechange(val) {
            this.nameInfo.currentPage = 1;
            this.nameInfo.pageSize = val;
            this.openNameDialog(this.nameInfo)
        },
        //对接记录弹层当前页改变
        namePagechange(val) {
            this.nameInfo.currentPage = val;
            this.openNameDialog(this.nameInfo)
        },
        //页面数量改变
        detailSizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getNotCommitList();
        },
        //当前页改变
        detailPagechange(val) {
            this.ListInfo.currentPage = val;
            this.getNotCommitList();
        },
        searchList() {
            //清除styleCode,goodCode,providerName的空格
            this.ListInfo.styleCode = this.ListInfo.styleCode ? this.ListInfo.styleCode.replace(/\s+/g, "") : null;
            this.ListInfo.goodCode = this.ListInfo.goodCode ? this.ListInfo.goodCode.replace(/\s+/g, "") : null;
            this.ListInfo.providerName = this.ListInfo.providerName ? this.ListInfo.providerName.replace(/\s+/g, "") : null;
            this.getNotCommitList()
        },
        //获取供应商未提交列表
        async getNotCommitList() {
            this.listLoading = true;
            const { data, success } = await getPhoneConsultRecordPageList(this.ListInfo);
            if (success) {
                this.notTableData = data.list;
                this.detailTotal = data.total;
                this.listLoading = false;
                this.$forceUpdate();
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getNotCommitList()
            }
        },
        sortchange3({ order, prop }) {
            if (prop) {
                if (prop == 'count') {
                    this.nameInfo.orderBy = 'totalCount'
                } else {
                    this.nameInfo.orderBy = prop
                }
                this.nameInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.openNameDialog(this.nameInfo)
            }
        },
    }
   };
   </script>
   
   <style lang="scss" scoped>
   .header {
    display: flex;
    margin-bottom: 10px;
   }
   
   .publicMargin {
    margin-right: 20px;
   }
   
   ::v-deep .vxetoolbar20221212 {
    display: none !important;
   }
   
   ::v-deep .el-badge__content {
    padding: 0 4px;
    top: 7px;
    right: 0;
   }
   
   .doesn ::v-deep .vxe-tools--operate {
    display: block !important;
    position: absolute;
    top: -25px !important;
    left: -36px !important;
    padding-top: 0;
    padding-bottom: 0;
    z-index: 999;
    background-color: rgb(255 255 255 / 0%);
   }
   
   .imageList_box {
       display: flex;
       flex-wrap: wrap;
   
       .imageList {
           position: relative;
           width: 100px;
           height: 100px;
   
           .imgcss ::v-deep img {
               min-width: 100px !important;
               min-height: 100px !important;
               width: 100px !important;
               height: 100px !important;
           }
   
   
           .del {
               position: absolute;
               top: 0;
               right: 0;
               font-size: 16px;
               width: 15px;
               height: 15px;
               border-radius: 50%;
               color: black;
               text-align: center;
               line-height: 15px;
               cursor: pointer;
           }
       }
   }
   </style>
   