<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <div class="top_one">
          <span class="spanCss">商品ID:</span>
          <div class="publicCss">
            <inputYunhan ref="productCode" :inputt.sync="ListInfo.proCodes" v-model="ListInfo.proCodes" width="250px"
              placeholder="请输入(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
              :maxlength="21000" @callback="productCodeCallback" title="商品ID">
            </inputYunhan>
          </div>
          <el-button type="primary" @click="getList('search')" style="margin-right: 20px;">搜索</el-button>
          <el-button type="primary" @click="batchRack(1)">批量上架</el-button>
          <el-button type="primary" @click="batchRack(3)">批量下架</el-button>

          <el-button type="primary" @click="onExport">导出</el-button>
          <el-button type="primary" @click="bulkLoadingUnLog">查看日志</el-button>
          <el-button type="primary" v-if="checkPermission('api:operatemanage:productmanager:DeletePDDProduct')"  @click="DeletePDDProduct">删除链接</el-button>
        </div>
      </div>
    </template>
    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
    </vxetablebase>
    <!-- <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template> -->
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { formatPlatform, formatTime, formatYesornoBool, formatLinkProCode } from "@/utils/tools";
import inputYunhan from "@/components/Comm/inputYunhan";
import { getProductUpDownList, batchProductUpDown,ExportProductUpDownList,deletePDDProduct } from "@/api/operatemanage/productmanager"

const tableCols = [
  { prop: 'proCode', label: '商品ID', sortable: 'custom', width: '120' },
  { prop: 'title', label: '产品名称', sortable: 'custom', width: '200', formatter: (row) => row.proName },
  { prop: 'shopCode', label: '店铺', sortable: 'custom', width: '150', formatter: (row) => row.shopName, type: 'custom' },
  { prop: 'status', label: '当前状态', sortable: 'custom', width: '80', align: 'center', formatter: (row) =>  row.proStatus == 0 ? '未知' : row.proStatus == 1 ? '上架' :  row.proStatus == 3 ? '下架' :'', },
  { prop: 'onTime', label: '上架时间', sortable: 'custom', width: '150', formatter: (row) => row.upTime },
  { prop: 'offTime', label: '下架时间', sortable: 'custom', width: '150', formatter: (row) => row.downTime },
  { prop: 'isDel', label: '是否删除', width: '90', sortable: 'custom', align: 'center', formatter: (row) => row.isDel?"是":"否"},
  { prop: 'delTime', label: '删除时间', sortable: 'custom', width: '150', formatter: (row) => row.delTime },
  { prop: 'upDownStatus', label: '操作状态', sortable: 'custom', width: '80', align: 'center', formatter: (row) => row.upDownStatus == 1 ? '上架中' : row.upDownStatus == 2 ? '下架中' : row.upDownStatus == 3 ? '已上架' : row.upDownStatus == 4 ? '已下架' : row.upDownStatus == 5 ? '上架失败' : row.upDownStatus == 6 ? '下架失败' : row.upDownStatus == 7 ? '已删除':row.upDownStatus == 8 ? '删除失败':'' },
  { prop: 'opTime', label: '操作时间', sortable: 'custom', width: '150' },
  { prop: 'opEndTime', label: '操作完成时间', sortable: 'custom', width: '150' },
  { prop: 'opUserName', label: '操作人', sortable: 'custom', width: '80' },
  { prop: 'opResult', label: '操作结果', sortable: 'custom', width: '120' },
]
export default {
  name: "batchListingDelist",
  components: {
    MyContainer, vxetablebase, inputYunhan
  },
  data() {
    return {
      checkdata: [],
      that: this,
      ListInfo: {
        // currentPage: 1,
        // pageSize: 50,
        orderBy: null,
        isAsc: false,
        proCodes: '',//商品ID
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    // await this.getList()
  },
  methods: {
    //批量上下架
    async batchRack(val) {
      let action = val == 1 ? '上架' : '下架';

      //校验状态
      var check = false;
      this.tableData.forEach((row)=>{
          if(row.isDel)
           check = true;
      });
      if(check)
      {
        this.$message.error("存在已删除的产品,不可继续操作");
        return;
      }

      let content = `此操作将批量“${action}”, 是否继续?`;
      this.$confirm(content, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await batchProductUpDown({ proList: this.tableData, proStatus: val })
        if (success) {
          this.$message({ type: 'success', message: `操作成功，请关注钉钉消息接收上下架成功数量!` });
          this.getList()
        }
      }).catch(() => {
        this.$message({ type: 'info', message: '已取消操作' });
      });
    },
    //商品ID数据回调
    productCodeCallback(val) {
      this.ListInfo.proCodes = val;
    },
    //加载父组件过来的数据
    async loadData({ checkdata }) {
      this.checkdata = checkdata
      this.ListInfo.proCodes = checkdata.map(item => item.proCode).join(',');
      await this.getList()
    },
    //获取列表
    async getList(type) {
      this.loading = true
      const { data, success } = await getProductUpDownList({ ...this.ListInfo })
      if (success) {
        this.tableData = data
        this.total = data.length;
      } else {
        //获取列表失败
        //this.$message.error('获取列表失败')
      }
      this.loading = false
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    //排序
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    bulkLoadingUnLog(){
      this.$showDialogform({
          path: `@/views/base/batchListingDelistLog.vue`,
          title: '上下架日志',
          autoTitle: false,
          args: {
            proCodes:this.ListInfo.proCodes,
          },
          height: '650px',
          width: '1000px',
      })
    },
    async onExport() {
        this.loading = true
        var res = await ExportProductUpDownList({ ...this.ListInfo });
        this.loading = false
        if (!res?.data) {
            this.$message({ message: "没有数据", type: "warning" });
            return
        }
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '产品管理-批量上下架列表_' + new Date().toLocaleString() + '_.xlsx')
        aLink.click()
    },
    async DeletePDDProduct()
    {
      var selprocodes = [];
      this.tableData.forEach(f=>{
          selprocodes.push(f.proCode);
      });
      this.$confirm("确定删除吗?", "提示", { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning", }).then(
          async () => {
            let res = await deletePDDProduct(selprocodes);
            if (res?.success) {
              this.getList();
              this.$message({ message: '删除成功', type: "success" });
            }
          });
    }
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 3px;

  .spanCss {
    display: flex;
    align-items: center;
    margin-right: 5px;
  }

  .publicCss {
    width: 250px;
    margin-right: 10px;
  }
}

.top_one {
  display: flex;
  width: 100%;
  margin-bottom: 10px;
}
</style>
