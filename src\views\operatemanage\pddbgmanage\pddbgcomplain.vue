<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="运营组：">
                    <el-select filterable v-model="filter.groupId" placeholder="运营组" style="width: 110px" clearable>
                        <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value"
                            :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item label="店铺名称:">
                    <el-select filterable v-model="filter.shopCode" placeholder="请选择店铺" clearable style="width: 120px">
                        <el-option v-for="item in shopList" :key="item.id" :label="item.shopName"
                            :value="item.shopCode" />
                    </el-select>
                </el-form-item>
                <el-form-item label="商品ID">
                     <el-input v-model="filter.pddProductId" placeholder="商品ID" style="width: 120px" maxlength="50" clearable></el-input>
                </el-form-item>
                <el-form-item label="处罚状态">
                    <el-select filterable v-model="filter.punishStatus" placeholder="处罚状态" style="width: 120px" clearable>
                        <el-option label="待处理" value="待处理"></el-option>
                        <el-option label="生效中" value="生效中"></el-option>
                        <el-option label="已结束" value="已结束"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="限制开始时间">
                    <el-date-picker style="width: 200px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                         ></el-date-picker>
                </el-form-item>
                <el-form-item label="限制结束时间">
                    <el-date-picker style="width: 200px" v-model="filter.timerange2" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                         ></el-date-picker>
                </el-form-item>
                <el-form-item label="重要:">
                        <el-select  v-model="filter.isImportant" placeholder="重要"
                            :collapse-tags="true" clearable >
                            <el-option label="重要" :value="true"></el-option>
                        </el-select>
               </el-form-item>
                <el-form-item label="是否处理:">
                        <el-select v-model="filter.isHandel" placeholder="是否处理"
                            :collapse-tags="true" clearable >
                            <el-option label="未处理" :value="false"></el-option>
                            <el-option label="已处理" :value="true"></el-option>
                        </el-select>
               </el-form-item>
               <el-form-item label="处理人:">
                    <el-select filterable v-model="filter.handeler" placeholder="请选择处理人" clearable style="width: 120px">
                        <el-option v-for="(item,i) in handelerList" :key="i" :label="item.msg"
                            :value="item.haderUserId" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSetImportant">设置重要</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='list'
            :tableCols='tableCols' :isSelection="false" :loading="listLoading">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
        <el-dialog title="设置重要" :show-close="false" :visible.sync="pddbdemailImportantVisible" width="420px" close-on-click-modal
      element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
        <div style="width:100%;margin:15px auto 70px auto;">

            <span style="margin-left:5px">违规类型:</span>
            <el-select filterable v-model="pddbdemailImportant.OperateManageIds" placeholder="请选择违规类型" multiple clearable style="width: 260px">
                        <el-option v-for="(item,i) in pddEmailTyleList" :key="i" :label="item.label"
                            :value="item.label" />
             </el-select>
             <p></p>
             <span style="margin-left:5px">处罚内容:</span>
             <el-select filterable v-model="pddbdemailImportant.operateManagePunishs" placeholder="处罚内容" multiple style="width: 260px" clearable>
                <el-option v-for="(item,i) in pddPubishList" :key="i" :label="item.label"
                            :value="item.label" />
            </el-select>
          </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="pddbdemailImportantVisible = false">取消</el-button>
          <el-button @click="submitSetImportant" v-throttle="3000" type="primary" v-loading="btnloading">保存</el-button>
        </span>
      </template>
    </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { getPingduoduoBGComplainList,exportPingduoduoBGComplainList,setPddBackGroundViolationImportant,
    handlePddBackGroundViolation,getPddEmailDetial,getPddBackGroundShopViolationList,getHandelerList  } from '@/api/operatemanage/pddbgmanage/pddbgmanage'
import { getDirectorGroupList,getList as getshopList } from '@/api/operatemanage/base/shop'
import dayjs from "dayjs";
import { formatTime } from "@/utils";

const tableCols = [
    { istrue: true, prop: 'groupId', label: '运营组', tipmesg: '', width: '120', sortable: 'custom', formatter: (row) =>row.groupName},
    { istrue: true, prop: 'shopName', label: '店铺名称', tipmesg: '', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'pddViolationId', label: '违规编号', tipmesg: '', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'punishObject', label: '处罚对象',width: '80',  tipmesg: '' },
    { istrue: true, prop: 'pddProductId', label: '商品Id',width: '200',  tipmesg: '', sortable: 'custom' },
    { istrue: true, prop: 'pddProductName', label: '商品名称',  tipmesg: '', sortable: 'custom' },
    { istrue: true, prop: 'punishInfo', label: '处罚内容',width: '150',  tipmesg: '', sortable: 'custom' },
    { istrue: true, prop: 'isImportant', label: '重要', width: '100', sortable: 'custom', formatter: (row) => {return row.isImportant?"重要":"不重要"} },
    { istrue: true, prop: 'violationType', label: '违规类型', width: '150', tipmesg: '', sortable: 'custom' },
    { istrue: true, prop: 'punishBeginTime', label: '限制开始时间',width: '150',  tipmesg: '', sortable: 'custom', formatter: (row) =>row.punishBeginTime == null ? '--': formatTime(row.punishBeginTime, "YYYY-MM-DD HH:mm:ss") },
    { istrue: true, prop: 'punishEndTime', label: '限制结束时间',width: '150',  tipmesg: '', sortable: 'custom', formatter: (row) =>row.punishEndTime == null ? '--': formatTime(row.punishEndTime, "YYYY-MM-DD HH:mm:ss") },
    { istrue: true, prop: 'punishStatus', label: '处罚状态', width: '100', tipmesg: '', sortable: 'custom' },
    { istrue: true, prop: 'punishStatus', label: '处罚状态', width: '100', tipmesg: '', sortable: 'custom' },
    {  istrue: true, type: 'isHandel', label: '是否处理', width: '100',style:(that,row)=>{return row.isHandel==true?"color:gray;":"color: blue;cursor: pointer"},  formatter: (row) =>{ return row.isHandel==true?"已处理":"未处理"},type:'click',handle: (that, row) => that.handelOp(row)},
    { istrue: true, prop: 'handlerUserName', label: '处理人', sortable: 'custom'},
    { istrue: true, prop: 'handlerTime', label: '处理时间', sortable: 'custom'}
]

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunHanAdminPddBGShopViolation',
    components: { container, cesTable, MyConfirmButton },
    data() {
        return {
            that: this,
            filter: {
                startTime: null,
                endTime: null,
                groupId: null,
                shopCode: null,
                pddProductId:null,
                timerange: null,
                startTime2: null,
                endTime2: null,
                timerange2: null,
                punishStatus:null,
                isImportant:true,
                isHandel:false,
                handeler:null
            },
            list: [],
            shopList: [],
            directorGroupList:[],
            pager: { OrderBy: "punishBeginTime", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            uploadLoading: false,
            dialogVisible: false,
            listLoading: false,
            fileList: [],
             //设置重要
           pddEmailTyleList:[],
           pddPubishList:[],
           pddbdemailImportantVisible:false,
            pddbdemailImportant: {
                OperateManageIds: [],
                operateManagePunishs:[]
            },
            addLoading: true,
            btnloading:false,
            handelerList:[]
        };
    },

    async mounted() {
        await this.onSearch()
        await this.onchangeplatform()
    },

    methods: {
        //获取店铺
        async onchangeplatform() {
            this.categorylist = []
            const res1 = await getshopList({ platform: 2, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
            let res3 = await getDirectorGroupList({})
            this.directorGroupList = res3.data

            var resValiationList = await getPddBackGroundShopViolationList({typeId:4,flag:0});
            this.pddEmailTyleList=resValiationList?.data;

            var respunishList = await getPddBackGroundShopViolationList({typeId:4,flag:1});
            this.pddPubishList=respunishList?.data;

            var resEmailType = await getPddEmailDetial({typeId:4,flag:0});
            var pddTyleList=resEmailType?.data;
            this.pddbdemailImportant.OperateManageIds=[];
            pddTyleList.forEach(item => {
                if (item.isImportant) {
                    this.pddbdemailImportant.OperateManageIds.push(item.label);
                }
            })

            var respunishType = await getPddEmailDetial({typeId:4,flag:1});
            var respunishTypeList=respunishType?.data;
            this.pddbdemailImportant.operateManagePunishs=[];
            respunishTypeList.forEach(item => {
                if (item.isImportant) {
                    this.pddbdemailImportant.operateManagePunishs.push(item.label);
                }
            })

            await this.getHandelList();
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            if(this.pager.OrderBy==null){
                this.pager.OrderBy="punishBeginTime";
                this.pager.IsAsc=false;
            }
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            this.filter.startTime2 = null;
            this.filter.endTime2 = null;
            if (this.filter.timerange2) {
                this.filter.startTime2 = this.filter.timerange2[0];
                this.filter.endTime2 = this.filter.timerange2[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await getPingduoduoBGComplainList(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.list = data
        },
        async nSearch() {
            await this.getlist()
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        async onExport(){
            if(this.pager.OrderBy==null){
                this.pager.OrderBy="punishBeginTime";
                this.pager.IsAsc=false;
            }
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            this.filter.startTime2 = null;
            this.filter.endTime2 = null;
            if (this.filter.timerange2) {
                this.filter.startTime2 = this.filter.timerange2[0];
                this.filter.endTime2 = this.filter.timerange2[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            let res = await exportPingduoduoBGComplainList(params);
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }

            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '拼多多违规申诉_' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        async submitSetImportant () {
         var res = await setPddBackGroundViolationImportant({typeId:4,operateManageIds:this.pddbdemailImportant.OperateManageIds,operateManagePunishs:this.pddbdemailImportant.operateManagePunishs});
         if (!res?.success) {
            this.btnloading = false;
            return
        }
        this.$message({ type: 'success', message: "设置成功" });
        this.btnloading = false;
        this.pddbdemailImportantVisible = false;
        await  this.onSearch();

        },
        onSetImportant () {
          this.pddbdemailImportantVisible = true;
        },
        async handelOp(row)
        {
            if(row.isHandel)
            {
                this.$message({type: 'info',message: '已处理不可重复处理'});
                return;
            }
            this.$confirm('确认处理吗, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
            }).then(async () => {
                const res = await handlePddBackGroundViolation({bid:row.id,typeId:4});
                if (!res?.success) {return }
                this.$message({type: 'success',message: '操作成功!'});
                await this.getHandelList();
                await this.onSearch();
            }).catch(() => {
            this.$message({type: 'info',message: '已取消操作'});
            });
        },
        async getHandelList()
        {
            var resHandeler = await getHandelerList({typeId:4});
            this.handelerList = resHandeler?.data;
        }
    }
};
</script>

<style lang="scss" scoped>

</style>
