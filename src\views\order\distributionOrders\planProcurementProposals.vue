<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select v-model="ListInfo.shipperFxName" placeholder="货主分销" class="publicCss" clearable>
                    <el-option v-for="item in fxUserNames" :label="item" :value="item" />
                </el-select>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="goodsCodesCallback" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                    <el-button type="primary" @click="tocreateimgMethod" :disabled="loadingUrl">生成图片</el-button>
                </div>
            </div>
        </template>
        <div style="width: 100%;height: 100%;" v-loading="loadingUrl">
            <!-- 大列表 -->
            <div id="YunHanAdminGoods202501181511" v-show="!dataVisible" style="width: 100%;height: 100%;">
                <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
                    id="20250222145751" @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
                    :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading">
                    <template #stockCount="{ row }">
                        <el-input-number v-model="row.purchasePlanCount" :min="0" :max="9999999" label="建议采购数量"
                            placeholder="建议采购数量" :controls="false" :precision="0"
                            @blur="handleSubmit(row)"></el-input-number>
                    </template>
                </vxetablebase>
            </div>
            <div id="YunHanAdminGoods202501181511" v-show="dataVisible" style="width: 100%;height: 100%;">
                <div v-show="dataVisible" class="dialogcss" id="oneboxx" style="width: 100%;height: auto;">
                    <vxetablebase id="'20250222154258'" ref="table1" :that='that' :isIndex='true' :hasexpand='true'
                        :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
                        :isSelection="false" :isSelectColumn="false" style="width: 100%;margin: 0" :loading="loading"
                        @cellStyle="cellStyle" cellStyle :hasSeq="hasSeq">
                    </vxetablebase>
                </div>
            </div>
        </div>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import html2canvas from 'html2canvas'
import dateRange from "@/components/date-range/index.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import decimal from '@/utils/decimal'
import { download } from "@/utils/download";
import { getShipperFxNameList,GetShipperFxGoodsPurchaseNewPlanList, ExportShipperFxGoodsPurchaseNewPlanList, UpdateShipperFxGoodsStockCount } from '@/api/order/shipperFxOrder'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'images', label: '图片', type: 'images', },
    {
        width: 'auto', align: 'center', prop: 'styleCode', label: '商品信息', showoverflow: false, type: 'html', formatter: (row) => {
            return `
        <div>${row.goodsName} </div>
        <div style="font-size: 15px;color: black;">${row.styleCode}</div>
        <div>${row.warehouseName}</div>
        `
        }
    },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'masterStock', label: '主仓库存数', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'inTransitNum', label: '采购在途数', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderWaitSend', label: '订单待发', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'sellStock', label: '可售库存', },
    {
        width: 'auto', align: 'center', prop: 'seriesName', showoverflow: false, label: '总销量/总退货', type: 'html', formatter: (row) => {
            return `
        <div>3日: ${row.salesDay3} / ${row.refundDay3}</div>
        <div>7日: ${row.salesDay7} / ${row.refundDay7}</div>
        <div>15日: ${row.salesDay15} / ${row.refundDay15}</div>
        <div>30日: ${row.salesDay30} / ${row.refundDay30}</div>
        `
        }
    },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'salesYesterday', label: '昨日销量', },
    {
        width: '150', align: 'center', prop: 'avgDay3', showoverflow: false, label: '日均销量/日均退货', type: 'html', formatter: (row) => {
            return `
        <div>3日: ${row.avgDay3} / ${row.avgRefundDay3}</div>
        <div>7日: ${row.avgDay7} / ${row.avgRefundDay7}</div>
        <div>15日: ${row.avgDay15} / ${row.avgRefundDay15}</div>
        <div>30日: ${row.avgDay30} / ${row.avgRefundDay30}</div>
        `
        }
    },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'amendDay', label: '日均修正量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'stockCount', label: '建议采购数量', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, inputYunhan
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
            timeRanges: [],
            tableCols,
            tableData: [], 
            fxUserNames: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            dataVisible: false,
            loadingUrl: false
        }
    },
    async mounted() {
        await this.getList()
        await getShipperFxNameList()
        .then(({ data }) => {
            this.fxUserNames = data;
        })
    },
    methods: {
        goodsCodesCallback(val) {
            this.ListInfo.goodsCodes = val
        },
        tocreateimgMethod() {
            this.loadingUrl = true;
            this.dataVisible = true;
            setTimeout(() => {
                this.tocreateimg();
            }, 100);
        },
        async tocreateimg() {
            setTimeout(async () => {
                const element = document.querySelector('#oneboxx');
                const originalWidth = element.style.width;  // 保留原始宽度
                if (!element) return;
                let computeWidth = 0;
                this.tableCols.forEach(item => {
                    computeWidth = decimal(computeWidth, item.width == 'auto' ? 40 : item.width ? Number(item.width) : 0, 0, '+');
                });
                computeWidth = computeWidth + 30
                if (computeWidth < element.scrollWidth) {
                    computeWidth = element.scrollWidth
                }
                element.style.width = `${computeWidth}px`;  // 设置宽度
                await this.$nextTick();
                // 生成图片
                html2canvas(element, {
                    allowTaint: true,
                    useCORS: true,
                    scrollX: 0,
                    scrollY: 0,
                    width: computeWidth,  // 确保捕获元素的完整宽度
                    height: element.scrollHeight,  // 捕获元素的完整高度
                    scale: 2,  // 提高图像质量（分辨率）
                }).then(async (canvas) => {
                    const imgData = canvas.toDataURL('image/png')
                    const img = new Image()
                    img.src = imgData
                    const a = document.createElement('a')
                    a.href = imgData
                    a.download = `计划采购建议${new Date().getTime()}.png`
                    a.click()
                }).catch((error) => {
                    console.log(error)
                }).finally(() => {
                    element.style.width = originalWidth;
                    element.style.height = 'auto'; // 恢复原始高度
                    this.loadingUrl = false;
                    this.dataVisible = false;
                });
            }, 100);
        },
        async handleSubmit(row) {
            const { success } = await UpdateShipperFxGoodsStockCount({
                goodsCodes: row.goodsCode,
                stockCount: row.purchasePlanCount
            })
            if (!success) return this.$message.error('保存失败')
            this.$message.success('保存成功')
            row.showIpt = false
        },
        proCodeCallback(val) {
            this.ListInfo.goodsCodes = val
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await ExportShipperFxGoodsPurchaseNewPlanList(this.ListInfo).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetShipperFxGoodsPurchaseNewPlanList(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}

::v-deep .vxe-cell {
    white-space: normal;
}

.dialogcss ::v-deep .body--wrapper {
    height: auto !important;
}
</style>
