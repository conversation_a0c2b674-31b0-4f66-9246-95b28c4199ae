<template>
  <!-- 岗位设置 -->
  <div style="width: 100%; height: 700px; margin: 0;">
    <!-- <div style="height: 50px;">
      <el-button-group style="margin: 5px;">
        <el-button type="primary">新增</el-button>
        <el-button type="primary">保存排序</el-button>
        <el-button type="primary">恢复</el-button>
        <el-button type="primary" @click="OpenAllfuc">全部展开</el-button>
        <el-button type="primary" @click="CloseAllfuc">全部收起</el-button>
      </el-button-group>
    </div> -->
    <div style="display: flex;margin:6px 0;">
      <span><el-button style="margin-right:5px;" size="mini" type="primary">新增岗位</el-button></span>
      <span><el-button style="margin-right:5px;" size="mini" type="primary">保存排序</el-button></span>
      <span><el-button style="margin-right:5px;" size="mini" type="primary">刷新</el-button></span>
    </div>
    <dragTreeTable onlySameLevelCanDrag style="margin-top: -10px;" ref="table" :data="treeData" @drag="onTreeDataChange"
      :OpenAll="true" resize fixed :isdraggable="true">
      <template #selection="{ row }">
        {{ row.date }}
      </template> 
      <template #edit="{ row }">
        <el-button type="text" icon="el-icon-edit" @click.stop.prevent="handleUpdate(row)">编辑</el-button>
        <el-button type="text" icon="el-icon-plus" @click.stop.prevent="handleAdd(row)">新增</el-button>
        <el-button class="btn-delete" type="text" icon="el-icon-delete"
          @click.stop.prevent="handleDelete(row)">删除</el-button>
      </template>
    </dragTreeTable>

    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
      <el-input v-model="rows.date" placeholder="请输入公司"></el-input>
      <el-input v-model="rows.name" placeholder="请输入公司"></el-input>
      <el-input v-model="rows.address" placeholder="请输入公司"></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import dragTreeTable from "drag-tree-table";

export default {
  components: {
    dragTreeTable
  },
  data() {
    return {
      OpenAll: false,
      dialogVisible: false,
      rows: {},
      // 树状
      treeData: {
        custom_field: {
          id: 'id',
          order: 'sort',
          lists: 'children',
          parent_id: 'parent_id'
        },
        columns: [
          { type: 'selection', title: '公司', field: 'date', width: 165 },
          { title: '工作岗位', field: 'name', width: 160 },
          {
            title: '提成岗位',
            field: 'address',
            width: 230
          },
          {
            type: 'edit',
            title: '操作',
            field: '',
            width: 460,
            align: 'center'
          },
        ],
        lists: [{
          open: false,
          id: "1",
          parent_id: '0',
          date: "义乌",
          name: "...",
          address: "...",
          children: [{
            id: "1-1",
            open: false,
            parent_id: '1',
            date: "",
            name: "部门经理",
            address: "部门经理",
            children: []
          }, {
            id: "1-2",
            open: false,
            parent_id: '1',
            date: "",
            name: "摄影组长",
            address: "摄影组长",
            children: []
          }, {
            id: "1-3",
            parent_id: '1',
            open: false,
            date: "",
            name: "摄影师",
            address: "...",
            children: [{
              id: "1-3-1",
              parent_id: '1',
              open: false,
              date: "",
              name: "",
              address: "视觉摄影",
              children: []
            }, {
              id: "1--3-2",
              parent_id: '1',
              open: false,
              date: "",
              name: "",
              address: "新品摄影",
              children: []
            },],
          }, {
            id: "1-4",
            parent_id: '1',
            open: false,
            date: "",
            name: "视频组长",
            address: "视频组长",
            children: []
          },]
        },
        {
          id: "2",
          date: "南昌",
          open: false,
          name: "...",
          address: "...",
          children: []
        },]
      },
    };
  },
  mounted() {
  },
  methods: {
    handleClose(done) {
      done();
      // this.$confirm('确认关闭？')
      //     .then(_ => {
      //     done();
      //     })
      //     .catch(_ => {});
    },
    OpenAllfuc(val) {
      this.allopen(this.treeData.lists, true);
    },
    CloseAllfuc() {
      this.allopen(this.treeData.lists, false);
    },
    onTreeDataChange(list) {
      this.treeData.lists = list;
    },
    handleAdd(row) {
      this.wxforeach(this.treeData.lists, row);
    },
    handleDelete(row) {
      this.wxdelte(this.treeData.lists, row)
    },
    handleUpdate(row) {
      console.log("更新的行数据", row)
      this.rows = row;
      this.dialogVisible = true;
    },
    //   新增
    wxforeach(arrayy, row) {
      arrayy.forEach((item) => {
        var params = {
          id: "2",
          date: '',
          name: "加入数据",
          address: "...",
          children: [],
          open: true,
        };
        if (item != row) {
          this.wxforeach(item.children, row)
        } else {
          item.children.push(params)
        }
      })
    },
    //   删除
    wxdelte(arrayy, row) {
      arrayy.forEach((item) => {
        if (item != row) {
          this.wxdelte(item.children, row)
        } else {
          var index = arrayy.indexOf(row)
          arrayy = arrayy.splice(index, 1)
        }
      })
    },
    //展开
    allopen(array, istrue) {
      array.forEach((item) => {
        item.open = istrue;
        if (item.children != null) {
          this.allopen(item.children, istrue)
        }
      })
    },
    updatelist() {

    },
  }
};
</script>
<style lang="scss" scoped>
::v-deep .drag-tree-table-body {
  height: 650px !important;
}
</style>