<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="日期:">
                    <el-date-picker style="width: 260px" v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                </el-form-item>
                <el-form-item label="用户:">
                    <el-input v-model="filter.userName" />
                </el-form-item>
                <el-form-item label="任务名称:">
                    <el-input v-model="filter.title" />
                </el-form-item>
                <el-form-item label="平台:">
                    <el-select v-model="filter.platform" placeholder="平台" style="width: 100px" clearable :collapse-tags="true" filterable>
                        <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="类型:">
                    <el-select v-model="filter.fileType" placeholder="类型" style="width: 100px" clearable :collapse-tags="true" filterable>
                        <el-option v-for="item in fileTypeList" :key="item.key" :label="item.value" :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' @sortchange='sortchange' @select='selectchange' :hasexpand='true' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" />
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </container>
</template>
<script>
    import { queryIsAdmin } from '@/api/admin/user'
    import { queryPageTaskLog, queryEnmImportFileTypes } from '@/api/admin/login-log'
    import { formatTime, formatPlatform,downloadLink } from "@/utils/tools";
    import { rulePlatform } from "@/utils/formruletools";
    import cesTable from "@/components/Table/table.vue";
    import container from "@/components/my-container";
    import dayjs from "dayjs";
    const tableCols = [
        { istrue: true, prop: 'createdTime', label: '时间', width: '150', sortable: 'custom', formatter: (row) => formatTime(row.createdTime, 'YYYY-MM-DD HH:mm:ss') },
        { istrue: true, prop: 'createdUserName', label: '用户', width: '60', },
        { istrue: true, prop: 'platform', label: '平台', width: '60', sortable: 'custom', formatter: row => formatPlatform(row.platform) },
        { istrue: true, prop: 'fileType', label: '类型', width: '80', sortable: 'custom', formatter: row => row.fileTypeStr },
        { istrue: true, prop: 'title', label: '任务名称', width: '500', },        
        { istrue: true, prop: 'totalCount', label: '总数', width: '80', },
        { istrue: true, prop: 'count', label: '完成数', width: '80', },
        { istrue: true, prop: 'progress', label: '进度', type: "progress" },
        { istrue: true, prop: 'fileUrl', label: '下载', width: '70', type:'button',  btnList:[           
            {
                label:'下载',
                handle:(that,row)=>{
                    //window.open(row.fileUrl);
                    that.downloadLink(row.fileUrl,row.title);
                },
                permission:'taskDownLoad',
                ishide:(that,row)=> !row.fileUrl 
            },           
        ]} ,
        { istrue: true, prop: 'modifiedTime', label: '进度时间', width: '150', sortable: 'custom', formatter: (row) => formatTime(row.modifiedTime, 'YYYY-MM-DD HH:mm:ss')},
    ];
    const tableHandles = [{ label: "刷新", handle: (that) => that.refresh() }];
    export default {
        name: "Users",
        components: { container, cesTable },
        data () {
            return {
                that: this,
                isAdmin: false,
                filter: {
                    timerange: [
                        formatTime(dayjs().subtract(2, "day"), "YYYY-MM-DD"),
                        formatTime(new Date(), "YYYY-MM-DD"),
                    ],
                    startTime: null,
                    endTime: null,
                    userName: null,
                    title: null,
                    platform: null,
                    fileType: null,
                },
                list: [],
                platformList: [],
                fileTypeList: [],
                tableCols: tableCols,
                tableHandles: tableHandles,
                pager: { OrderBy: "createdTime", IsAsc: false },
                total: 0,
                sels: [],
                selids: [],
                listLoading: false,
                pageLoading: false,
            };
        },
        async mounted () {
            await this.init();
            await this.onSearch();
        },
        methods: {
            downloadLink:downloadLink,
            async init () {
                var res = await queryIsAdmin();
                if (!res?.success) return;
                this.isAdmin = (res.data == "true");

                var pfrule = await rulePlatform();
                this.platformList = pfrule.options;

                var fileres = await queryEnmImportFileTypes();
                this.fileTypeList = fileres.data;
            },

            async refresh () {
                await this.getlist();
            },
            async onSearch () {
                this.$refs.pager.setPage(1)
                this.getlist()
            },
            async getlist () {
                if (!this.pager.OrderBy) this.pager.OrderBy = "";
                var pager = this.$refs.pager.getPager()
                const params = { ...pager, ...this.pager, ... this.filter }
                if (params.timerange) {
                    params.startTime = params.timerange[0];
                    params.endTime = params.timerange[1];
                }
                this.listLoading = true
                const res = await queryPageTaskLog(params)
                this.listLoading = false
                if (!res?.success) return
                this.total = res.data.total
                const data = res.data.list
                data.forEach(d => { d._loading = false })
                this.list = data
                this.summaryarry = res.data.summary;
            },
            sortchange (column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch();
            },
            selsChange: function (sels) {
                this.sels = sels
            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.proBianMa);
                })
            },
        },
    };
</script>

