<template>
    <my-container class="content">
        <el-row :gutter="5">
            <el-col :span="24">
                <el-card class="card">
                    <el-descriptions class="margin-top" title="当前版本" :column="5" border>
                        <template slot="extra">
                            <el-button type="primary" size="small"
                                @click="getdatainfo">刷新</el-button>&nbsp;&nbsp;&nbsp;&nbsp;
                            <el-tag type="success">注：自动开单前后五分钟不允许操作！</el-tag>&nbsp;&nbsp;&nbsp;&nbsp;
                            <el-button type="primary" size="small" @click="clickIsSet">操作</el-button>
                        </template>
                        <el-descriptions-item>
                            <template slot="label">
                                <i class="el-icon-tickets"></i>
                                数据X值：
                            </template>
                            {{ dataInfo.diffDays }}
                        </el-descriptions-item>
                        <!-- <el-descriptions-item>
                            <template slot="label">
                                <i class="el-icon-tickets"></i>
                                系列X值：
                            </template>
                            {{ dataInfo.styleDays }}
                        </el-descriptions-item> -->
                        <el-descriptions-item>
                            <template slot="label">
                                <i class="el-icon-tickets"></i>
                                过滤最低进货量：
                            </template>
                            {{ dataInfo.quantity }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                <i class="el-icon-tickets"></i>
                                过滤最大订单待发量：
                            </template>
                            {{ dataInfo.maxInventoryQty }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                <i class="el-icon-tickets"></i>
                                参考列：
                            </template>
                            {{ dataInfo.selColumn == 1 ? "最近在途" : dataInfo.selColumn == 2 ? "历史在途" : "" }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                <i class="el-icon-tickets"></i>
                                版本：
                            </template>
                            {{ versionArr[dataInfo.version] }}
                        </el-descriptions-item>
                        <el-descriptions-item
                            :content-class-name="{ 'background': dataInfo.isAuto ? '#E1F3D8' : '#FDE2E2' }">
                            <template slot="label">
                                <i class="el-icon-tickets"></i>
                                自动通知运营截止时间段：
                            </template>
                            <span>
                                {{ dataInfo.timer }} 分钟
                            </span>

                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                <i class="el-icon-tickets"></i>
                                同编码最大进货比例(%)：
                            </template>
                            <span>
                                {{ dataInfo.maxQuantityRatio }} %
                            </span>
                        </el-descriptions-item>
                        <el-descriptions-item
                            :content-class-name="{ 'background': dataInfo.isAuto ? '#E1F3D8' : '#FDE2E2' }">
                            <template slot="label">
                                <i class="el-icon-tickets"></i>
                                是否自动开单：
                            </template>
                            <span>
                                {{ dataInfo.isAuto == true ? "运行" : dataInfo.isAuto == false ? "停止开单" : "" }}
                            </span>

                        </el-descriptions-item>
                        <el-descriptions-item
                            :content-class-name="{ 'background': dataInfo.isAuto ? '#E1F3D8' : '#FDE2E2' }">
                            <template slot="label">
                                <i class="el-icon-tickets"></i>
                                是否自动通知运营：
                            </template>
                            <span>
                                {{ dataInfo.isAutoGroup == true ? "通知" : dataInfo.isAutoGroup == false ? "停止通知" : "" }}
                            </span>

                        </el-descriptions-item>
                        <el-descriptions-item
                            :content-class-name="{ 'background': dataInfo.isContainAuditQty ? '#E1F3D8' : '#FDE2E2' }">
                            <template slot="label">
                                <i class="el-icon-tickets"></i>
                                是否包含审核中数量：
                            </template>
                            <span>
                                {{ dataInfo.isContainAuditQty == true ? "包含" : dataInfo.isContainAuditQty == false ? "不包含" : "" }}
                            </span>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-card>
            </el-col>
        </el-row>

        <el-row>
            <el-col :span="24">
                <el-card class="card" style="height: 220px;" v-loading='listLoading'>
                    <div slot="header" class="clearfix">
                        <span>操作界面</span>
                    </div>
                    <div class="form">
                        <el-form ref="form" :inline="true" label-position="right" :rules="rules" :model="formData"
                            v-loading="onFinishLoading">
                            <el-form-item prop="diffDays" label="数据X值：">
                                <el-input-number v-model="formData.diffDays" :disabled="isSet" :precision="0" :min="1"
                                    :max="10" label="请输入X值"></el-input-number>
                            </el-form-item>
                            <el-form-item prop="styleDays" label="系列X值：">
                                <el-input-number v-model="formData.styleDays" :disabled="isSet" :precision="0" :min="1"
                                    :max="10" label="请输入系列X值"></el-input-number>
                            </el-form-item>
                            <el-form-item prop="quantity" label="过滤最低进货量：">
                                <el-input-number v-model="formData.quantity" :disabled="isSet" :precision="0" :min="0"
                                    :max="1000" label="请输入过滤最低进货量："></el-input-number>
                            </el-form-item>
                            <el-form-item prop="maxInventoryQty" label="过滤最大订单待发量：">
                                <el-input-number v-model="formData.maxInventoryQty" :disabled="isSet" :precision="0"
                                    :min="5000" :max="1000000" label="请输入最大订单待发量：："></el-input-number>
                            </el-form-item>
                            <el-form-item prop="selColumn" label="列选择：">
                                <el-select v-model="formData.selColumn" :disabled="isSet" placeholder="请选择"
                                    style="width: 100%;">
                                    <el-option label="最近在途" value="1"></el-option>
                                    <el-option label="历史在途" value="2"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item prop="version" label="版本选择：">
                                <el-select v-model="formData.version" :disabled="isSet" placeholder="请选择"
                                    style="width: 100%;">
                                    <el-option label="版本一" value="1"></el-option>
                                    <el-option label="版本二" value="2"></el-option>
                                    <el-option label="版本三" value="3"></el-option>
                                    <el-option label="版本四" value="4"></el-option>
                                    <el-option label="版本五" value="5"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item prop="version" label="自动通知运营截止时间段：">
                                <el-select v-model="formData.timer" :disabled="isSet" placeholder="请选择"
                                    style="width: 100%;">
                                    <el-option label="30分钟" value="30"></el-option>
                                    <el-option label="60分钟" value="60"></el-option>
                                    <el-option label="120分钟" value="120"></el-option>
                                    <el-option label="180分钟" value="180"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item prop="maxQuantityRatio" label="同编码最大进货比例(%)">
                                <el-input-number type="number" v-model="formData.maxQuantityRatio" :disabled="isSet" 
                                    :precision="0" :min="0" :max="9999" placeholder="同编码最大进货比例(%)"></el-input-number>
                            </el-form-item>
                            <el-form-item label="是否自动开单：">
                                <span>
                                    <el-switch v-model="formData.isAuto" :disabled="isSet"></el-switch>
                                </span>
                            </el-form-item>
                            <el-form-item label="是否自动通知运营：">
                                <span>
                                    <el-switch v-model="formData.isAutoGroup" :disabled="isSet"></el-switch>
                                </span>
                            </el-form-item>
                            <el-form-item label="是否包含审核中数量：">
                                <span>
                                    <el-switch v-model="formData.isContainAuditQty" :disabled="isSet"></el-switch>
                                </span>
                            </el-form-item>
                            <el-form-item label="">
                                <span v-if="!isSet" style="float: right;">
                                    <el-button type="primary" @click="resetForm()">取消</el-button>
                                    <my-confirm-button type="submit" :loading="onFinishLoading"
                                        :validate="finishFormValidate" @click="submitForm()">保存</my-confirm-button>
                                </span>
                            </el-form-item>
                        </el-form>
                    </div>
                </el-card>
            </el-col>
            <!-- <el-col :span="12">
                <el-card class="card" :body-style="{ height: '400px' }" style="height: 520px;" v-loading='listLoading'>
                    <div slot="header" class="clearfix">
                        <span>仓库标签</span>
                        <el-button v-if="!isSet" style="float: right; padding: 3px 0;" @click="onShowdrawer = true"
                            type="text">添加信息</el-button>
                    </div>
                    <el-table :data="tableData" border style="width: 100%" height="490px">
                        <el-table-column prop="warehouseName" label="ERP仓库" width="auto">
                        </el-table-column>
                        <el-table-column prop="jstWarehouseName" label="聚水潭仓库标签" width="auto">
                        </el-table-column>
                    </el-table>
                </el-card>
            </el-col> -->
        </el-row>

        <el-row :gutter="5">
            <el-col :span="0">
            </el-col>
            <el-col :span="24">
                <el-card class="card" :body-style="{ height: '400px', padding: '0px' }" style="height: 480px;"
                    v-loading='listLoading'>
                    <div slot="header" class="clearfix">
                        <span>采购信息</span>
                        <span style="float: right; padding: 3px 100px;">
                            <el-button style="padding: 0;margin-left: 10px;">
                                <el-select filterable v-model="filter.company" collapse-tags clearable placeholder="分公司"
                                    style="width: 100px">
                                    <el-option key="义乌" label="义乌" value="义乌"></el-option>
                                    <el-option key="南昌" label="南昌" value="南昌"></el-option>
                                </el-select>
                            </el-button>
                            <el-button style="padding: 0;margin-left: 10px;">
                                <el-select filterable v-model="filter.isAutoOrder" collapse-tags clearable
                                    placeholder="是否自动开单" style="width: 100px">
                                    <el-option key="是" label="是" :value="true"></el-option>
                                    <el-option key="否" label="否" :value="false"></el-option>
                                </el-select>
                            </el-button>
                            <el-button style="padding: 0;margin-left: 10px;">
                                <el-input v-model.trim="filter.name" placeholder="姓名" :maxlength="100" clearable
                                    style="width: 100px;" />
                            </el-button>
                            <el-button style="padding: 0;margin-left: 10px;">
                                <el-button @click="getlist()" type="primary">刷新</el-button>
                            </el-button>
                        </span>

                    </div>
                    <my-container>
                        <el-table :data="list" border style="width: 100%" height="350">
                            <el-table-column prop="brandName" label="姓名" width="auto">
                            </el-table-column>
                            <el-table-column prop="company" label="分公司" width="auto">
                            </el-table-column>
                            <el-table-column prop="dDeptName" label="岗位名称" width="auto">
                            </el-table-column>
                            <el-table-column prop="enabled" label="状态" width="auto">
                                <template slot-scope="scope">
                                    {{ scope.row.enabled == true ? "启用" : "禁用" }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="enabled" label="是否自动开单" width="auto">
                                <template slot-scope="scope">
                                    <el-switch v-model="scope.row.isAutoOrder" @change="changeAutoOrder(scope.row)">
                                    </el-switch>
                                </template>
                            </el-table-column>
                        </el-table>

                        <template #footer>
                            <my-pagination ref="pager" :total="total" :checked-count="sels.length"
                                @get-page="getlist" />
                        </template>
                    </my-container>

                </el-card>
            </el-col>
        </el-row>

        <el-drawer title="添加信息" :visible.sync="onShowdrawer" direction="btt">
            <div class="demo-drawer__content">
                <el-form :model="form" :inline="true" ref="formrule" :rules="formrules" label-width="150px">
                    <el-row>
                        <el-col :span="8">
                            <el-form-item prop="warehouseName" label="ERP仓库">
                                <el-select v-model="form.warehouseName" clearable placeholder="请选择分仓"
                                    style="width: 150px">
                                    <el-option label="全仓" :value="-1" />
                                    <el-option v-for="item in warehouselist" :key="item.label" :label="item.label"
                                        :value="item.label" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item prop="jstWarehouseName" label="聚水潭仓库标签">
                                <el-input v-model.trim="form.jstWarehouseName" maxlength="50"
                                    autocomplete="off"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <div class="demo-drawer__footer">
                                <el-button @click="onShowdrawer = false">取 消</el-button>
                                <my-confirm-button type="submit" :loading="onFinishWareLoading"
                                    :validate="finishForm11Validate" @click="submitWareForm()">保存</my-confirm-button>
                            </div>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </el-drawer>

    </my-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import { taskSetPurchaseAutoOrder, getPurchaseAutoOrder, getPurchaseOrderWarehouse, addPurchaseOrderWarehouse } from "@/api/inventory/purchaseordernew";
import { pageBianMaBrand, updateBianMaBrandIsAuto } from '@/api/inventory/warehouse'
import { warehouselist } from "@/utils/tools";
import { stringify } from 'querystring';

export default {
    name: '操作界面',
    components: { MyConfirmButton, MyContainer },
    computed: {

    },
    data() {
        return {
            versionArr: {
                1: "版本一",
                2: "版本二",
                3: "版本三",
                4: "版本四",
                5: "版本五",
            },
            filter: { name: '', company: null, isAutoOrder: null },
            formData: {
                id: null,
                diffDays: null,
                styleDays: null,
                quantity: null,
                selColumn: null,
                version: null,
                timer: null,
                isAuto: false,
                isContainAuditQty: false,
                isAutoGroup: false
            },
            form: {
                warehouseName: null,
                jstWarehouseName: null
            },
            warehouselist: warehouselist,
            list: [],
            pager: { OrderBy: "", IsAsc: true },
            total: 0,
            sels: [],
            dataInfo: {},
            isSet: true,
            rules: {
                diffDays: [
                    { required: true, message: '请输入X值', trigger: 'blur' },
                ],
                styleDays: [
                    { required: true, message: '请输入X值', trigger: 'blur' },
                ],
                quantity: [
                    { required: true, message: '请输入最低进货量', trigger: 'blur' },
                ],
                maxInventoryQty: [
                    { required: true, message: '请输入最大进货量', trigger: 'blur' },
                ],
                selColumn: [
                    { required: true, message: '请选择要设置参考标准的列', trigger: 'change' },
                ],
                timer: [
                    { required: true, message: '请选择自动通知运营截止时间段', trigger: 'change' },
                ],
                maxQuantityRatio: [
                    { required: true, message: '请输入同编码最大进货比例', trigger: 'blur' },
                ],
                version: [
                    { required: true, message: '请选择要自动开单的版本', trigger: 'change' },
                ],
            },
            formrules: {
                warehouseName: [
                    { required: true, message: '请选择要添加的仓库信息', trigger: 'change' },
                ],
                jstWarehouseName: [
                    { required: true, message: '请输入仓库标签信息', trigger: 'blur' },
                ],
            },
            tableData: [],
            onFinishLoading: false,
            onFinishWareLoading: false,
            onShowdrawer: false,
            listLoading: false,
        };
    },
    async mounted() {
        await this.getdatainfo();
    },

    methods: {
        async getdatainfo() {
            this.listLoading = true;
            var res = await getPurchaseAutoOrder();
            this.dataInfo = res.data;
            this.formData.id = this.dataInfo.id;
            this.formData.diffDays = this.dataInfo.diffDays;
            this.formData.styleDays = this.dataInfo.styleDays;
            this.formData.quantity = this.dataInfo.quantity;
            this.formData.maxInventoryQty = this.dataInfo.maxInventoryQty;
            this.formData.maxQuantityRatio = this.dataInfo.maxQuantityRatio;
            this.formData.selColumn = String(this.dataInfo.selColumn);
            this.formData.version = String(this.dataInfo.version);
            this.formData.timer = String(this.dataInfo.timer);
            this.formData.isAuto = this.dataInfo.isAuto;
            this.formData.isAutoGroup = this.dataInfo.isAutoGroup;
            this.formData.isContainAuditQty = this.dataInfo.isContainAuditQty;

            var warehouseData = await getPurchaseOrderWarehouse();
            this.tableData = warehouseData.data;
            await this.getlist();
            this.listLoading = false;
        },
        async getlist() {
            var pager = this.$refs.pager.getPager()
            const params = { ...pager, ... this.pager, ... this.filter }
            this.listLoading = true
            const res = await pageBianMaBrand(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res.data.total
            const data = res.data.list
            data.forEach(d => {
                d._loading = false
            })
            this.list = data
        },

        //新增采购单时提交验证
        finishFormValidate: function () {
            let isValid = false
            this.$refs.form.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        finishForm11Validate: function () {
            let isValid = false
            this.$refs.formrule.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        async submitForm() {
            this.onFinishLoading = true;
            var para = { ...this.formData };
            var res = await taskSetPurchaseAutoOrder(para);
            this.onFinishLoading = false;
            if (res?.success) {
                this.$message({ message: '成功', type: "success" });
                await this.getdatainfo();
                this.isSet = true;
                return true;
            } else {
                //this.$message({ message: res.msg, type: "warning" });
                return false;
            }
        },
        async submitWareForm() {
            var para = { ...this.form };
            var res = await addPurchaseOrderWarehouse(para);
            this.onFinishWareLoading = true;
            if (res?.success) {
                this.$message({ message: '成功', type: "success" });
                await this.getdatainfo();
                this.isSet = true;
                this.onFinishWareLoading = false;
                this.onShowdrawer = false;
                return true;
            } else {
                //this.$message({ message: res.msg, type: "warning" });
                this.onFinishWareLoading = false;
                this.onShowdrawer = false;
                return false;
            }
        },
        resetForm() {
            this.isSet = true;
            //this.$refs.form.resetFields();
        },
        clickIsSet() {
            // 获取当前时间的分钟数
            const now = new Date();
            const minutes = now.getMinutes();

            // 如果当前时间在整点前后五分钟内，则返回 true
            if (minutes >= 55 || minutes <= 5) {
                this.$message({ message: '自动开单前后五分钟不允许操作！', type: "warning" });
                return false;
            }
            this.formData.id = this.dataInfo.id;
            this.formData.diffDays = this.dataInfo.diffDays;
            this.formData.styleDays = this.dataInfo.styleDays;
            this.formData.quantity = this.dataInfo.quantity;
            this.formData.maxInventoryQty = this.dataInfo.maxInventoryQty;
            this.formData.maxQuantityRatio = this.dataInfo.maxQuantityRatio;
            this.formData.selColumn = String(this.dataInfo.selColumn);
            this.formData.version = String(this.dataInfo.version);
            this.formData.timer = String(this.dataInfo.timer);
            this.formData.isAuto = this.dataInfo.isAuto;
            this.formData.isAutoGroup = this.dataInfo.isAutoGroup;
            this.formData.isContainAuditQty = this.dataInfo.isContainAuditQty;
            this.isSet = !this.isSet;
        },
        async changeAutoOrder(row) {
            // 获取当前时间的分钟数
            const now = new Date();
            const minutes = now.getMinutes();

            // 如果当前时间在整点前后五分钟内，则返回 true
            if (minutes >= 55 || minutes <= 5) {
                this.$message({ message: '自动开单前后五分钟不允许操作！', type: "warning" });
                row.isAutoOrder = !row.isAutoOrder;
                return false;
            }
            this.$confirm('将当前采购设置开单状态，是否继续?', '提示', {
                confirmButtonText: '是', cancelButtonText: '否', type: 'warning'
            }).then(async () => {
                var params = {
                    id: row.id,
                    IsAutoOrder: row.isAutoOrder,
                };
                const res = await updateBianMaBrandIsAuto(params);
                if (!res?.success) return;
                this.$message({ message: "设置成功", type: "success" });
            }).catch(() => {
                row.isAutoOrder = !row.isAutoOrder;
            });
        },
    },
};
</script>


<style lang="scss" scoped>
// ::v-deep .el-card__body {
//     height: 350px;
// }
.clearfix:before,
.clearfix:after {
    display: table;
    content: "";
}

.clearfix:after {
    clear: both
}

.content {
    //max-width: 1400px;
    width: 100%;
    //display: flex;
    //flex-direction: column;
    height: 1000px;
    overflow-y: auto;
}

.card {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    margin-bottom: 15px;
}

.form {
    padding: 15px;
}

.demo-form {
    display: flex;
    flex-wrap: wrap;
}

// .el-form-item {
//     width: 100%;
//     margin-bottom: 15px;
// }

.el-input,
.el-select,
.el-switch {
    width: 100%;
}
</style>
