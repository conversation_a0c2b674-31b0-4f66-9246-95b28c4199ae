<template>
    <MyContainer>
        <el-table :data="tableData" style="width: 100%" max-height="250">
            <el-table-column type="index" width="50" />
            <el-table-column prop="name" label="仓库名称" />
            <el-table-column prop="address" label="是否参与蓄单">
                <template #default="{ row, $index }">
                    <el-select v-model="row.notMobilize" placeholder="是否参与蓄单" @change="changeMobilize($event, $index)">
                        <el-option key="是" label="是" :value="false" />
                        <el-option key="否" label="否" :value="true" />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column prop="address" label="最晚放单时间" width="230">
                <template #default="{ row, $index }">
                    <el-time-select v-model="row.lastSendTimeStr" :picker-options="{
                        start: '00:00',
                        step: '00:30',
                        end: '23:30'
                    }" placeholder="最晚放单时间" @change="changeTime($event, $index)" :disabled="row.notMobilize"
                        :clearable="false">
                    </el-time-select>
                </template>
            </el-table-column>
            <el-table-column prop="address" label="是否库存同步">
                <template #default="{ row, $index }">
                    <el-select v-model="row.isSyncInventory" placeholder="是否库存同步">
                        <el-option key="是" label="是" :value="true" />
                        <el-option key="否" label="否" :value="false" />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column prop="address" label="是否审单仓库">
                <template #default="{ row, $index }">
                    <el-select v-model="row.isCptVerifyOrder" placeholder="是否审单仓库">
                        <el-option key="是" label="是" :value="true" />
                        <el-option key="否" label="否" :value="false" />
                    </el-select>
                </template>
            </el-table-column>
        </el-table>
        <div class="btnGroup">
            <el-button @click="closeDialog">取消</el-button>
            <el-button type="primary" @click="submit" v-throttle="2000">确认</el-button>
        </div>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { getWareHouses, setWareHouses } from '@/api/vo/VerifyOrder'
export default {
    components: { MyContainer },
    data() {
        return {
            tableData: []
        }
    },
    async mounted() {
        await this.getProps()
    },
    methods: {
        changeMobilize(e, i) {
            if (e == true) {
                this.tableData[i].lastSendTimeStr = ''
                this.tableData[i].lastSendTime = ''
            }
        },
        async getProps() {
            const { data, success } = await getWareHouses()
            if (success) {
                data.forEach(item => {
                    if (item.lastSendTime != null) {
                        var lastSendTimeStr = "0000" + item.lastSendTime ?? "0"
                        item.lastSendTimeStr = lastSendTimeStr.substring(lastSendTimeStr.length - 4, lastSendTimeStr.length - 2) + ":" + lastSendTimeStr.substring(lastSendTimeStr.length - 2, lastSendTimeStr.length)
                    }
                })
                this.tableData = data
            }
        },
        closeDialog() {
            this.$emit('close')
        },
        async submit() {
            this.tableData.forEach(item => {
                if (item.lastSendTimeStr) {
                    item.lastSendTime = item.lastSendTimeStr.replace(":", '')
                }
                if (item.notMobilize === null || item.notMobilize === undefined || item.notMobilize === '') {
                    item.notMobilize = false
                }
            })
            const { success } = await setWareHouses(this.tableData)
            if (success) {
                this.$emit('close')
                this.$emit('getList', true)
                this.$message.success('设置成功')
            }
        }
    }
}
</script>

<style scoped lang="scss">
.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 20px;

    button {
        width: 100px;
    }
}
</style>