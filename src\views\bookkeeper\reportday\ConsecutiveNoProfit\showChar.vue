<template>
  <div :id="'charNoProfit'" ref="charNoProfit" style="height:80%;width:100%"></div>
</template>
<script>
import MyContainer from "@/components/my-container";
import * as echarts from 'echarts/core';
import { getContinuousNoProfit } from "@/api/bookkeeper/continuousprofitanalysis"
export default {
  name: "ConsecutiveNoProfitShowChar",
  components: {
    MyContainer
  },
  props: {
    filter: {}
  },
  data() {
    return {
      that: this,
      emphasisStyle: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0,0,1,0.3)'
        }
      },
      filter1: {
        currentPage: 1,
        pageSize: 50
      }
    };
  },
  async mounted() {
    await this.showChar();
  },
  methods: {
    async showChar() {
      let chartDom = document.getElementById('charNoProfit');
      echarts.dispose(chartDom);

      let data1 = [];
      let data2 = [];
      let data3 = [];
      let data4 = [];
      let xAxis = [];
      let values = [];

      let param = { ...this.filter, ...this.filter1 };
      let res = await getContinuousNoProfit(param);
      res.data.list.forEach(item => {
        let total = item.consecutiveDay3 + item.consecutiveDay7 + item.consecutiveDay15 + item.consecutiveDay30;
        if (this.filter.groupType == "platform") {
          xAxis.push(item.platformName + "\n" + total);
          values.push(item.platform);
        } else if (this.filter.groupType == "shopcode") {
          xAxis.push(item.shopName + "\n" + total);
          values.push(item.shopCode);
        } else if (this.filter.groupType == "groupid") {
          xAxis.push(item.groupName + "\n" + total);
          values.push(item.groupId);
        } else if (this.filter.groupType == "operatespecialuserid") {
          xAxis.push(item.operateSpecialUserName + "\n" + total);
          values.push(item.operateSpecialUserId);
        }
        data1.push(item.consecutiveDay3);
        data2.push(item.consecutiveDay7);
        data3.push(item.consecutiveDay15);
        data4.push(item.consecutiveDay30);
      });

      let myChart = echarts.init(chartDom);
      let option = {
        legend: {
          data: ["3天负利润", "7天负利润", "15天负利润", "30天负利润"],
          left: '40%'
        },
        toolbox: {},
        tooltip: {},
        xAxis: {
          data: xAxis,
          axisLine: { onZero: true },
          splitLine: { show: false },
          splitArea: { show: false },
          axisLabel: {
            interval: 0,      //坐标轴刻度标签的显示间隔(在类目轴中有效) 0:显示所有  1：隔一个显示一个 :3：隔三个显示一个...
            rotate: 30
          }
        },
        yAxis: {
          name: '个数'
        },
        grid: {
          bottom: 100
        },
        series: [
          {
            name: '3天负利润',
            type: 'bar',
            stack: 'one',
            emphasis: this.emphasisStyle,
            data: data1,
            label: {
              normal: {
                show: true,//开启显示
                position: 'inside',//柱形上方
                textStyle: { //数值样式
                  color: '#31484f'
                },
                // 使用函数模板，函数参数分别为刻度数值（类目），刻度的索引
                formatter: function (value, index) {
                  return value.data == 0 ? "" : value.data;
                }
              }
            }
          },
          {
            name: '7天负利润',
            type: 'bar',
            stack: 'one',
            emphasis: this.emphasisStyle,
            data: data2,
            label: {
              normal: {
                show: true,//开启显示
                position: 'inside',//柱形上方
                textStyle: { //数值样式
                  color: '#31484f'
                },
                // 使用函数模板，函数参数分别为刻度数值（类目），刻度的索引
                formatter: function (value, index) {
                  return value.data == 0 ? "" : value.data;
                }
              }
            }
          },
          {
            name: '15天负利润',
            type: 'bar',
            stack: 'one',
            emphasis: this.emphasisStyle,
            data: data3,
            label: {
              normal: {
                show: true,//开启显示
                position: 'inside',//柱形上方
                textStyle: { //数值样式
                  color: '#31484f'
                },
                // 使用函数模板，函数参数分别为刻度数值（类目），刻度的索引
                formatter: function (value, index) {
                  return value.data == 0 ? "" : value.data;
                }
              }
            }
          },
          {
            name: '30天负利润',
            type: 'bar',
            stack: 'one',
            emphasis: this.emphasisStyle,
            data: data4,
            label: {
              normal: {
                show: true,//开启显示
                position: 'inside',//柱形上方
                textStyle: { //数值样式
                  color: '#31484f'
                },
                // 使用函数模板，函数参数分别为刻度数值（类目），刻度的索引
                formatter: function (value, index) {
                  return value.data == 0 ? "" : value.data;
                }
              }
            }
          }
        ]
      };

      let that = this;

      myChart.on('click', function (params) {



        let url = "/bookkeeper/reportday/productReportAllIndex";
        if (that.filter.platform == 2) {
          url = "/bookkeeper/reportday/financialReportPdd";
        } else if (that.filter.platform == 4) {
          url = "/bookkeeper/reportday/productReportAlibabaIndex";
        } else if (that.filter.platform == 6) {
          url = "/bookkeeper/reportday/productReportDyIndex";
        } else if (that.filter.platform == 7) {
          url = "/bookkeeper/reportday/productReportJDIndex";
        } else if (that.filter.platform == 8) {
          url = "/bookkeeper/reportday/productReportGCIndex";
        } else if ( that.filter.platform == 1) {
          url = "/bookkeeper/reportday/productReportTxIndex";
        } else if (that.filter.platform == 9 ) {
          url = "/bookkeeper/reportday/productReportTaoBao";
        } else if (that.filter.platform == 10) {
          url = "/bookkeeper/reportday/productReportSuNingIndex";
        }
        let queryObj = {
          yearMonthDay: that.filter.yearMonthDay,
          isPositive: false
        }
        // let index = xAxis.findIndex(x => x == params.name);
        // if (that.filter.groupType == "platform") {
        //   queryObj.platform = values[index];
        // } else if (that.filter.groupType == "shopcode") {
        //   queryObj.shopCode = values[index];
        // } else if (that.filter.groupType == "groupid") {
        //   queryObj.groupId = values[index];
        // } else if (that.filter.groupType == "operatespecialuserid") {
        //   queryObj.operateSpecialUserId = values[index];
        // } 
        queryObj.platform = (that.filter.platform==""?null:that.filter.platform);
        queryObj.shopCode = (that.filter.shopCode==""?null:that.filter.shopCode);
        queryObj.groupId =  (that.filter.groupId==""?null:that.filter.groupId);
        queryObj.operateSpecialUserId =  (that.filter.operateSpecialUserId==""?null:that.filter.operateSpecialUserId);
        if (params.seriesName == "3天负利润") {
          queryObj.dayCount = 3;
        } else if (params.seriesName == "7天负利润") {
          queryObj.dayCount = 7;
        } else if (params.seriesName == "15天负利润") {
          queryObj.dayCount = 15;
        } else if (params.seriesName == "30天负利润") {
          queryObj.dayCount = 30;
        }
        that.$router.push({ path: url, query: queryObj })
      });

      myChart.setOption(option);
    }
  }
};
</script>
<style></style>