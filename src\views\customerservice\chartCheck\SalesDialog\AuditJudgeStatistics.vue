<template>
    <my-container>
        <!--列表-->
        <Ces-table ref="table" :that="that" :isIndex="true" @sortchange="sortchange" :tableData="tableData"
            :showsummary="true" :tableCols="tableCols" :loading="listLoading" style="width: 100%;
    height: calc(100% - 10%); margin: 0">
        </Ces-table>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getPageList" />
        </template>

    </my-container>
</template>

<script>
//审核总量
import {
    getDataStatisticsPopupPageList,
    getJudgmentDataStatisticsPopupPageList
} from "@/api/customerservice/chartCheck";
import MyContainer from "@/components/my-container";
import CesTable from "@/components/Table/table.vue";
const platformList = [
    { name: "拼多多", value: 2 },
    { name: "抖音", value: 6 },
    { name: "天猫", value: 1 },
    // { name: "淘工厂", value: 8 },
    { name: "淘宝", value: 9 },
]
const tableCols = [
    {
        istrue: true,
        prop: "orderNo",
        label: "线上订单号",
        sortable: "custom",
        // width: "180",
    }, {
        istrue: true,
        display: true,
        prop: "proId",
        label: "宝贝ID",
        sortable: "custom",
        type: "html",
        // width: "150",
    }, {
        istrue: true,
        prop: "platform",
        label: "平台",
        // width: "60",
        sortable: "custom",
        formatter: (row) => {
            return platformList.filter(item => item.value == row.platform)[0].name
        },
    },
    {
        istrue: true,
        prop: "shopName",
        label: "店铺",
        // width: "120",
        sortable: "custom",
    },
    {
        istrue: false,
        prop: "userName",
        label: "使用账号人",
        // width: "120",
        sortable: "custom",
    },
    {
        istrue: false,
        prop: "groupName",
        label: "分组",
        // width: "120",
        sortable: "custom",
    },
    {
        istrue: false,
        prop: "judgmentAuditType",
        label: "评判结果",
        // width: "80",
        sortable: "custom",
        formatter: (row) => {
            let name = ''
            if (row.judgmentAuditType == 1) {
                name = '合格'
            } else if (row.judgmentAuditType == 2) {
                name = '不合格'
            }
            return name;
        },
    },
    {
        istrue: false,
        prop: "refuseJudgmentAuditType",
        label: "评判类型",
        // width: "80",
        sortable: "custom",
        formatter: (row) => {
            return row.refuseJudgmentAuditType;
        },
    },
];

export default {
    props: {
        JudgeROW: { type: Object, default: () => { } },
    },
    components: { MyContainer, CesTable },
    data() {
        return {
            that: this,
            tableData: [],
            summaryarry: null,
            tableCols: [],
            listLoading: false,
            total: 0,
            platformList: platformList,
        }
    },
    mounted() {

        this.onSearch();

    },
    methods: {
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getPageList();
        },

        sortchange(column) {    //表头排序
            if (!column.order) this.pager = {};
            else
                this.pager = {
                    orderBy: column.prop,
                    isAsc: column.order.indexOf("descending") == -1 ? true : false,
                };
            this.onSearch();
        },
        async getPageList() {
            this.tableCols = tableCols
            var pager = this.$refs.pager.getPager();
            var paramsPre = {
                ...pager,
                ...this.pager,
                switchshow: this.JudgeROW.switchshow,
                timeStart: this.JudgeROW.timeStart,
                timeEnd: this.JudgeROW.timeEnd,
                userName: this.JudgeROW.operator,
                groupName: this.JudgeROW.groupName,
                auditType:this.JudgeROW.auditType,
                reviewType : 3,
                // isPdd:this.JudgeROW.isPdd

            }
            var paramsAfter = {
                ...pager,
                ...this.pager,
                switchshow: this.JudgeROW.switchshow,
                timeStart: this.JudgeROW.timeStart,
                timeEnd: this.JudgeROW.timeEnd,
                userName: this.JudgeROW.operator,
                groupName: this.JudgeROW.groupName,
                auditType:this.JudgeROW.auditType,
                reviewType : 3,
                // isPdd:this.JudgeROW.isPdd
            }
            this.listLoading = true;
            const res = this.JudgeROW.switchshow ? await getDataStatisticsPopupPageList(paramsPre) : await getJudgmentDataStatisticsPopupPageList(paramsAfter);
            this.listLoading = false;
            this.total = res.data.total
            this.tableData = res.data.list;
        },

    },


}
</script>

<style></style>