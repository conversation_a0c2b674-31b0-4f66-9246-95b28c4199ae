<template>
  <container v-loading="pageLoading" class="containerBox">
    <div style="height: 40px; margin-top: 10px;">
      <span style="margin-right:0.4%;">
        <el-date-picker v-model="timerange" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="选择日期"
          :clearable="false"></el-date-picker>
      </span>
      <span style="margin-left:0.4%;">
        <el-button type="primary" @click="onSearch" style="width: 70px;">搜索</el-button>
      </span>
      <span style="margin-left:0.4%;">
        <el-button style="margin: 0;">
          最近更新时间:
          {{ lastupdatedtime }}
        </el-button>
      </span>
    </div>
    <div class="myheader">
      <template>
        <div class="borderCss">
          <div class="borderCss_item" v-for="(item, i) in warehouselist"
            :class="{ 'highlighted': item.warehouse === 88888888 }">
            <div>
              <span>{{ item.warehouse === 0 ? "未知" : item.warehouse === 88888888 ? "采购资金" : item.warehouseName }}</span>
              <div class="borderCss_itemCss">
                <div>{{ item.totalMoney.toLocaleString() }}</div>
                <i class="el-icon-s-data cursor-pointer" @click="wrehousechart(item.warehouse, item.warehouseName)"></i>
              </div>
            </div>
            <div>
              <span>在仓</span>
              <div class="borderCss_itemCss">
                <div style="color: #409eff;" class="cursor-pointer" @click="detailsintransit(item.warehouseName)">{{
                  item.stockMoney.toLocaleString() }}</div>
                <div :style="{ color: item.stockBalance > 0 ? 'red' : item.stockBalance < 0 ? 'green' : 'gray' }">{{
                  item.stockBalance.toLocaleString() }}</div>
              </div>
            </div>
            <div>
              <div class="warehouseCss_itemCss">
                <span>在仓正：</span>
                <div style="color: #409eff;" class="cursor-pointer" @click="detailsintransit(item.warehouseName)">{{
                  item.isStockMoney.toLocaleString() }}
                </div>
                <div
                  :style="{ color: item.isStockBalanceMoney > 0 ? 'red' : item.isStockBalanceMoney < 0 ? 'green' : 'gray', 'margin-left': 'auto' }">
                  {{ item.isStockBalanceMoney.toLocaleString() }}</div>
              </div>
            </div>
            <div>
              <div class="warehouseCss_itemCss">
                <span>在仓负：</span>
                <div style="color: #409eff;" class="cursor-pointer" @click="detailsintransit(item.warehouseName)">{{
                  item.negativeStockMoney.toLocaleString() }}
                </div>
                <div
                  :style="{ color: item.negativeStockBalanceMoney > 0 ? 'red' : item.negativeStockBalanceMoney < 0 ? 'green' : 'gray', 'margin-left': 'auto' }">
                  {{ item.negativeStockBalanceMoney.toLocaleString() }}
                </div>
              </div>
            </div>
            <div>
              <span>在途</span>
              <div class="borderCss_itemCss">
                <div style="color: #409eff;" class="cursor-pointer" @click="detailsintransit(item.warehouseName)">{{
                  item.onPassageMoney.toLocaleString() }}
                </div>
                <div :style="{ color: item.onPassageBalance > 0 ? 'red' : item.onPassageBalance < 0 ? 'green' : 'gray' }">
                  {{ item.onPassageBalance.toLocaleString() }}</div>
              </div>
            </div>
            <div>
              <span>历史在途</span>
              <div class="borderCss_itemCss">
                <div style="color: #409eff;" class="cursor-pointer" @click="detailsintransit(item.warehouseName)">{{
                  item.historyFundsTransit.toLocaleString() }}
                </div>
                <!-- <div :style="{ color: item.onPassageBalance > 0 ? 'red' : item.onPassageBalance < 0 ? 'green' : 'gray' }">
                  {{ item.onPassageBalance.toLocaleString() }}</div> -->
              </div>
            </div>
          </div>
        </div>

        <div class="borderCss">
          <div class="borderCss_item" v-for="(item, i) in platFormlist"
            :class="{ 'highlighted': item.platForm == 88888888 }">
            <div>
              <span>{{ findLabelByValue(item.platForm) }}</span>
              <div class="borderCss_itemCss">
                <div>{{ item.totalMoney.toLocaleString() }}</div>
                <i class="el-icon-s-data cursor-pointer" @click="platFormchart(item.platForm)"></i>
              </div>
            </div>
            <div>
              <span>在仓</span>
              <div class="borderCss_itemCss">
                <div style="color: #409eff;" class="cursor-pointer" @click="platintransit(item.platForm)">{{
                  item.stockMoney.toLocaleString() }}</div>
                <div :style="{ color: item.stockBalance > 0 ? 'red' : item.stockBalance < 0 ? 'green' : 'gray' }">{{
                  item.stockBalance.toLocaleString() }}</div>
              </div>
            </div>
            <div>
              <div class="warehouseCss_itemCss">
                <span>在仓正：</span>
                <div style="color: #409eff;" class="cursor-pointer" @click="detailsintransit(item.warehouseName)">{{
                  item.isStockMoney.toLocaleString() }}
                </div>
                <div
                  :style="{ color: item.isStockBalanceMoney > 0 ? 'red' : item.isStockBalanceMoney < 0 ? 'green' : 'gray', 'margin-left': 'auto' }">
                  {{ item.isStockBalanceMoney.toLocaleString() }}</div>
              </div>
            </div>
            <div>
              <div class="warehouseCss_itemCss">
                <span>在仓负：</span>
                <div style="color: #409eff;" class="cursor-pointer" @click="detailsintransit(item.warehouseName)">{{
                  item.negativeStockMoney.toLocaleString() }}
                </div>
                <div
                  :style="{ color: item.negativeStockBalanceMoney > 0 ? 'red' : item.negativeStockBalanceMoney < 0 ? 'green' : 'gray', 'margin-left': 'auto' }">
                  {{ item.negativeStockBalanceMoney.toLocaleString() }}
                </div>
              </div>
            </div>
            <div>
              <span>在途</span>
              <div class="borderCss_itemCss">
                <div style="color: #409eff;" class="cursor-pointer" @click="platintransit(item.platForm)">{{
                  item.onPassageMoney.toLocaleString() }} </div>
                <div :style="{ color: item.onPassageBalance > 0 ? 'red' : item.onPassageBalance < 0 ? 'green' : 'gray' }">
                  {{ item.onPassageBalance.toLocaleString() }}</div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>

    <el-dialog :title="warehousetrendname" :visible.sync="dialogVisible" width="80%" v-dialogDrag>
      <div>
        <span>
          <buschar v-if="warehousesummarytrendchart.visible" ref="dialogwarehousebuschar"
            :analysisData="warehousesummarytrendchart.data">
          </buschar>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="platFormetrendname" :visible.sync="dialogplatformVisible" width="80%" v-dialogDrag>
      <div>
        <span>
          <buschar v-if="platformtrendchart.visible" ref="dialogplatformbuschar" :analysisData="platformtrendchart.data">
          </buschar>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogplatformVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </container>
</template>

<script>
import { formatTime, formatPlatform } from "@/utils/tools";
import buschar from '@/components/Bus/buschar'
import container from '@/components/my-container'
import { getPurchaseFundsSumWarehouseChart, getPurchaseFundsSumPlatFormChart, getPurchaseFundsSumWarehouse, getPurchaseFundsSumPlatForm } from '@/api/inventory/purchase'
export default {
  name: 'purchasefundscollect',
  components: { container, buschar },
  data() {
    return {
      // pickerOptions: pickerOptions,
      platForm: '00',//平台趋势图传参
      warehouse: '00',//采购资金趋势图传参
      warehouseName: '采购资金',//采购资金趋势图名
      platFormetrendname: '',
      warehousetrendname: '',
      lastupdatedtime: '',
      dialogplatformVisible: false,
      warehousesummary: {},
      platFormsummary: {},
      dialogVisible: false,
      warehousesummarytrendchart: { visible: false, title: "", data: {} },
      platformtrendchart: { visible: false, title: "", data: {} },
      warehouselist: [],
      platFormlist: [],
      listLoading: false,
      pageLoading: false,
      timerange: null,
      filter: {
        startDate: null,
        endDate: null,
      },
    };
  },

  mounted() {
    let end = new Date();
    // let start = new Date();
    end.setDate(end.getDate() - 1);
    //start.setDate(start.getDate() - 30);
    this.timerange = formatTime(end, "YYYY-MM-DD");
    this.onSearch();
  },

  methods: {
    detailsintransit(warehouseName) {
      this.$emit('update-templatepageclose', warehouseName, this.timerange);
    },
    platintransit(platform) {
      this.$emit('platform-templatepageclose', platform, this.timerange);
    },
    //仓库趋势图
    async wrehousechart(warehouse, warehouseName) {
      if (warehouse == 88888888) {
        warehouseName = "采购资金"
      }
      this.warehousetrendname = warehouseName + '趋势图'//仓库趋势图名
      this.filter.startDate = null;
      this.filter.endDate = null;
      if (this.timerange) {
        this.filter.startDate = this.timerange;
        this.filter.endDate = this.timerange;
      } this.listLoading = true
      const params = {
        ...this.filter,
        warehouse: warehouse
      }
      const res = await getPurchaseFundsSumWarehouseChart(params)
      res.series.map((item) => {
        item.itemStyle = {
          "normal": {
            "label": {
              "show": true,
              "position": "top",
              "textStyle": {
                "fontSize": 14
              }
            }
          }
        }
        item.emphasis = {
          "focus": "series"
        }
        item.smooth = false;
      })
      this.warehousesummarytrendchart.visible = true;
      this.warehousesummarytrendchart.data = res
      this.warehousesummarytrendchart.title = res.legend[0]
      this.$nextTick(() => {
        this.$refs.dialogwarehousebuschar.initcharts();
      });
      this.dialogVisible = true;
    },
    async platFormchart(platform) {
      //平台趋势图
      const platformschart = [
        { label: '未知', value: 0 },
        { label: '淘系', value: 1 },
        { label: '拼多多', value: 2 },
        { label: '阿里巴巴', value: 4 },
        { label: '抖音', value: 6 },
        { label: '京东', value: 7 },
        { label: '平台汇总', value: 88888888 },
        // { label: '淘工厂', value: 8 },
        // { label: '淘宝', value: 9 },
        // { label: '苏宁', value: 10 },
      ];
      let a = (platformschart.find(item => item.value === platform) || {}).label;
      if (a === undefined) {
        a = '平台汇总'
      }
      this.platFormetrendname = a + '趋势图'//平台趋势图名
      this.filter.startDate = null;
      this.filter.endDate = null;
      if (this.timerange) {
        this.filter.startDate = this.timerange;
        this.filter.endDate = this.timerange;
      } this.listLoading = true
      const params = {
        ...this.filter,
        platform: platform
      }
      const res = await getPurchaseFundsSumPlatFormChart(params)
      res.series.map((item) => {
        item.itemStyle = {
          "normal": {
            "label": {
              "show": true,
              "position": "top",
              "textStyle": {
                "fontSize": 14
              }
            }
          }
        }
        item.emphasis = {
          "focus": "series"
        }
        item.smooth = false;
      })
      this.platformtrendchart.visible = true;
      this.platformtrendchart.data = res
      this.platformtrendchart.title = res.legend[0]
      this.$nextTick(() => {
        this.$refs.dialogplatformbuschar.initcharts();
      });
      this.dialogplatformVisible = true;
    },
    //平台名
    findLabelByValue(value) {
      const platforms = [
        { label: '未知', value: 0 },
        { label: '淘系', value: 1 },
        { label: '拼多多', value: 2 },
        { label: '阿里巴巴', value: 4 },
        { label: '抖音', value: 6 },
        { label: '京东', value: 7 },
        { label: '平台汇总', value: 88888888 },
        // { label: '淘工厂', value: 8 },
        // { label: '淘宝', value: 9 },
        // { label: '苏宁', value: 10 },
      ];

      const platform = platforms.find((platform) => platform.value === value);
      return platform ? platform.label : '平台合计';
    },
    async onSearch() {
      await this.getlist();
    },
    async getlist() {
      this.filter.startDate = null;
      this.filter.endDate = null;
      if (this.timerange) {
        this.filter.startDate = this.timerange;
        this.filter.endDate = this.timerange;
      }
      this.listLoading = true
      const params = {
        ...this.filter
      }
      //仓库数据
      console.log(params, 'parmes');
      const { data, success } = await getPurchaseFundsSumWarehouse(params)
      if (!success) {
        return
      }
      this.warehouselist = data.list//仓库数据
      this.lastupdatedtime = data.summary.syncCreateTime//最晚更新时间
      this.warehousesummary = data.summary//仓库趋势图
      //平台数据
      const res = await getPurchaseFundsSumPlatForm(params)
      console.log(res, 'res');
      this.platFormlist = res.data.list
      this.platFormsummary = res.data.summary
      this.listLoading = false
    },
  },
};
</script>

<style lang="scss" scoped>
.warehouseCss_itemCss {
  display: flex;
  // justify-content: space-between;
  margin-bottom: 10px;
  font-size: 9px
}

.highlighted {
  background-color: #c6e2ff;
}

.myheader {
  overflow: auto;
  // height: 690px;
  height: 722px;
}

.cursor-pointer {
  cursor: pointer;
}

.text {
  font-size: 10px;
}

::v-deep .col-one {
  background-color: #efebf0da !important;
  // color: #eeeeeeda !important;
}

::v-deep .col-two {
  background-color: #d6e6d1e8 !important;
}

::v-deep .col-thr {
  background-color: #f1efdc !important;
}

::v-deep .col-four {
  background-color: #d9e8f3 !important;
}

.item {
  font-size: 14px;
}

.box-card {
  height: 120px;
  width: 210px;
  margin-right: 10px;
  margin-bottom: 10px;
}

.bgcolor {
  background-color: rgb(183, 216, 199);
}

::v-deep .el-card__header {
  padding: 9px 10px;
}

.titlecss {
  font-size: 15px;
  font-weight: 600;
  font-family: 'Courier New', Courier, monospace;
}

.borderCss {
  border-radius: 0;
  padding: 0px 0px 25px 0px;
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  overflow: auto;

  .borderCss_item {
    width: 240px;
    height: 200px;
    border: 1px solid #ebeef5;
    padding: 15px 10px;
    display: flex;
    flex-direction: column;
    font-size: 13px;
    margin: 8px;

    .borderCss_itemCss {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      font-size: 9px
    }
  }
}

.flexcenter {
  display: flex;
  justify-content: center;
  align-items: center;
}

::v-deep .el-card__body {
  padding: 5px !important;
}

::v-deep .el-button {
  user-select: unset;
}

// .containerBox {}
</style>
