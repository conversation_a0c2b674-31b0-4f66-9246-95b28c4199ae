<template>
  <MyContainer style="height: 98%;">
    <template #header>
      <div class="top">
        <div style="display: flex;margin-top: 10px;">

          <inputYunhan ref="productIDCode" :inputt.sync="ListInfo.procodes" v-model="ListInfo.procodes"
            class="publicCss" placeholder="产品SKC/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300"
            :maxlength="6000" @callback="orderNoInnerBack" title="产品SKC">
          </inputYunhan>
          <inputYunhan ref="commodityCode" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
            class="publicCss" placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300"
            :maxlength="6000" @callback="commodityCodeCallback" title="商品编码">
          </inputYunhan>
          <inputYunhan ref="productSKCCode" :inputt.sync="ListInfo.sku_Id" v-model="ListInfo.sku_Id" class="publicCss"
            placeholder="产品SKU/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
            @callback="productSKCCodeCallback" title="产品SKU">
          </inputYunhan>
        </div>
        <div style="display: flex;margin-top: 10px;">
          <el-select v-model="ListInfo.platform" placeholder="平台" clearable filterable class="publicCss"
            @change="onchangeplatform">
            <el-option :key="'希音-全托'" label="希音-全托" :value="12" />
            <el-option :key="'希音-自营'" label="希音-自营" :value="16" />
            <el-option :key="'Temu-半托'" label="Temu-半托" :value="'15'" />
            <el-option :key="'Temu-全托'" label="Temu-全托" :value="13" />
            <el-option :key="'TikTok-全托'" label="TikTok-全托" :value="18" />
            <el-option :key="'希音-半托'" label="希音-半托" :value="19" />
          </el-select>

          <el-select v-model="ListInfo.status" placeholder="状态" clearable filterable class="publicCss">
            <el-option :key="'未知'" label="未知" :value="0" />
            <el-option :key="'上架'" label="上架" :value="1" />
            <el-option :key="'下架'" label="下架" :value="'3'" />
          </el-select>

          <el-select v-model="ListInfo.shopCode" placeholder="请选择店铺" @change="getList" :clearable="true"
            :collapse-tags="true" filterable>
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
          </el-select>


          <!-- <el-input v-model.trim="ListInfo.shopId" placeholder="店铺ID" maxlength="30" clearable class="publicCss" /> -->
          <button style="padding: 0;width: 135px; border: none;">
            <el-input v-model="ListInfo.title" placeholder="标题" @keyup.enter.native="onSearch" clearable />
          </button>
          <button style="padding: 0; border: none;">
            <el-select filterable v-model="ListInfo.groupId" placeholder="运营组长" style="width: 100px" clearable
              @change="selectfuc">
              <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key" />
            </el-select>
          </button>
          <button style="padding: 0; border: none;">
            <el-select filterable v-model="ListInfo.operateSpecialId" placeholder="运营专员" clearable style="width: 100px"
              @change="selectfuc">
              <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
            </el-select>
          </button>
          <button style="padding: 0; border: none;">
            <el-select filterable v-model="ListInfo.userId" placeholder="运营助理" clearable style="width: 100px"
              @change="selectfuc">
              <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
            </el-select>
          </button>
        </div>

        <div style="display: flex;margin-top: 10px;">
          <el-date-picker v-model="ListInfo.auditTime" type="datetimerange" :picker-options="pickerOptions"
            range-separator="至" start-placeholder="更新开始日期" end-placeholder="更新结束日期" align="right">
          </el-date-picker>

          <el-date-picker v-model="ListInfo.onTime" type="datetimerange" :picker-options="pickerOptions"
            range-separator="至" start-placeholder="上架开始日期" end-placeholder="上架结束日期" align="right">
          </el-date-picker>

          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" @click="batchEditing">批量编辑</el-button>
          <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button @click="startImport"
            type="primary" icon="el-icon-share" @command="handleCommand"> 导入
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a">下载模版</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button type="primary" @click="onExport"  style="margin-left: 5px;" v-if="checkPermission('api:operatemanage:productmanager:ExportProductTemuList')">导出</el-button>
          
        </div>
      </div>
    </template>
    <vxetablebase :id="pddCrossBorderProductManage202408041644" ref="table" :that='that' :isIndex='true'
      :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
      :isSelection="false" :summaryarry='summaryarry' :showsummary='false' :isSelectColumn="false"
      style="width: 100%;margin: 0" v-loading="loading" :height="'100%'" @select='chooseCode'>
      <template slot="right">
        <vxe-column title="操作" width="150" :align="'center'" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex; justify-content: center; align-items: center;">
              <el-button type="text" @click="onEditor(row)">编辑</el-button>
              <el-button type="text" @click="onShowLog(row)">查看日志</el-button>
              <el-button type="text" style="color: #F56C6C;" @click="onDelete(row)">批次删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="日志" :visible.sync="logDialogVisible" width="60%" v-dialogDrag>
      <pddoperationLog ref="refpddoperationLog" style="height: 480px" :proCodeLogs="proCodeLogs"></pddoperationLog>
    </el-dialog>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 50px;">
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="editName ? '编辑' : '批量编辑'" :visible.sync="editPopupVisible" width="25%" v-dialogDrag>
      <div style="height: 240px;">
        <el-form :model="ruleForm" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-form-item label="店铺" prop="shopName">
            <el-input v-model.trim="ruleForm.shopName" placeholder="店铺" maxlength="50" clearable class="publicCss"
              disabled />
          </el-form-item>
          <el-form-item label="运营小组" prop="groupId">
            <el-select v-model="ruleForm.groupId" style="width: 100%;" placeholder="请选择运营小组" filterable clearable>
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="运营专员" prop="OperateSpecialUserId">
            <el-select v-model="ruleForm.OperateSpecialUserId" style="width: 100%;" placeholder="请选择运营专员" filterable
              clearable>
              <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="运营助理" prop="userId">
            <el-select v-model="ruleForm.userId" style="width: 100%;" placeholder="请选择运营助理" filterable clearable>
              <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div style="display: flex; justify-content: center;">
        <el-button @click="editPopupVisible = false">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="onEditsave">{{ (saveLoading ? '保存中' : '保存')
          }}</el-button>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, formatPlatform } from '@/utils/tools'
import dayjs from 'dayjs'
import pddoperationLog from './pddoperationLog.vue'
import inputYunhan from "@/components/Comm/inputYunhan";
import { getProductTemuList, importProductInfo_PddAsync, batchUpdateProductTemu, batchDeleteProductTemuAsync,ExportProductTemuList } from "@/api/operatemanage/productmanager"
import { getDirectorGroupList, getDirectorList, getList as getshopList } from '@/api/operatemanage/base/shop'
import { row } from "mathjs";
const tableCols = [
  { istrue: true, width: '30', type: "checkbox", },
  { istrue: true, prop: 'sku_Id', label: 'SkuId', align: 'center', width: 'auto', sortable: 'custom' },
  { istrue: true, prop: 'platform', fix: true, label: '平台', width: '90', sortable: 'custom', formatter: (row) => formatPlatform(row.platform) },
  { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom', align: 'center', width: 'auto' },
  { istrue: true, prop: 'shopCode', sortable: 'custom', label: '店铺ID', align: 'center', width: 'auto' },
  { istrue: true, prop: 'groupName', label: '运营小组', sortable: 'custom', align: 'center', width: 'auto' },
  { istrue: true, prop: 'operateSpecialUserName', label: '运营专员', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'userName', label: '运营助理', width: 'auto', sortable: 'custom' },
  { istrue: true, prop: 'proCode', label: '产品SKC', sortable: 'custom', align: 'center', width: 'auto' },
  { istrue: true, prop: 'title', label: '产品标题', align: 'center', width: 'auto', sortable: 'custom' },
  { istrue: true, prop: 'goodsCode', label: '商品编码', align: 'center', width: 'auto', sortable: 'custom' },
  { istrue: true, prop: 'styleCode', label: '系列编码', align: 'center', width: 'auto', sortable: 'custom' },
  { istrue: true, prop: 'specificationCategory', label: '规格品类', align: 'center', width: 'auto', sortable: 'custom' },
  { istrue: true, prop: 'specificationQuantity', label: '规格数量', align: 'center', width: 'auto', sortable: 'custom' },
  { istrue: true, prop: 'onTime', label: '上架时间', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'declaredPrice', label: '站点申报价格', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'images', label: '图片', width: 'auto', type: 'images', sortable: 'custom' },
  { istrue: true, prop: 'updateTime', label: '更新时间', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'status', label: '状态', sortable: 'custom', width: 'auto', formatter: (row) => row.status == 1 ? '上架' : row.status == 3 ? '下架' : '' },
  { istrue: true, prop: 'batchNumber', label: '批次号', sortable: 'custom', width: 'auto' },
]
export default {
  name: "pddCrossBorderProductManage",
  components: {
    MyContainer, vxetablebase, inputYunhan, pddoperationLog,
  },
  data() {
    return {
      saveLoading: false,
      editName: false,
      ruleForm: {
        shopName: null,
        groupId: null,
        OperateSpecialUserId: null,
        userId: null,
        ids: [],
      },
      editPopupVisible: false,
      rowIds: [],
      rowbatchNumbers: [],
      logDialogVisible: false,//日志弹窗
      dialogVisible: false,//导入弹窗
      uploadLoading: false,//上传loading
      fileList: [],//上传文件列表
      fileparm: {},//上传文件参数
      directorlist: [],
      grouplist: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'proCode',
        isAsc: false,
        shopCode: null,//店铺名称
        procodes: null,//产品SKC
        // skcs: null,//产品SKU
        goodsCodes: null,//商品编码
        startTime: null,
        endTime: null,
        sku_Id: null,
        onTimeStartTime: null,
        onTimeEndTime: null,
        // auditTime: [
        //   startTime(dayjs().startOf("month"), "YYYY-MM-DD HH:mm:ss"),
        //   endTime(dayjs().startOf("month"), "YYYY-MM-DD HH:mm:ss"),
        // ],
      },
      proCodeLogs: null,
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      summaryarry: {},//汇总
      pickerOptions,
      shopList: [],
      auditTime: [],
      pickerOptions: {
        disabledDate(date) {
          // 设置禁用日期
          const start = new Date("1970/1/1");
          const end = new Date("9999/12/31");
          return date < start || date > end;
        },
      },
      directorGroupList: [],
      directorList: [],
    }
  },
  async mounted() {
    await this.init()
    await this.getList()
    this.getDirectorlist()
    const res = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
    this.shopList = res.data.list;
  },
  methods: {
    async onEditsave() {
      let ids = []
      if (this.rowIds.length == 0) {
        ids = this.ruleForm.ids
      } else {
        ids = this.rowIds;
      }
      const params = { ...this.ruleForm, skuIds: ids }

      this.saveLoading = true
      const { data, success } = await batchUpdateProductTemu(params)
      if (success) {
        this.$message.success('保存成功')
        this.saveLoading = false
        this.editPopupVisible = false
        await this.getList()
      } else {
        this.$message.error('保存失败')
        this.saveLoading = false
      }
    },
    async init() {
      let { data, success } = await getDirectorList();
      if (success) {
        this.directorlist = data?.map(item => { return { value: item.key, label: item.value }; });
      }
      let { data: data1, success: success1 } = await getDirectorGroupList();
      if (success1) {
        this.grouplist = data1?.map(item => { return { value: item.key, label: item.value }; });
      }
    },
    //删除
    async onDelete(row) {
      this.$confirm('是否删除该条数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const params = { batchNumber: row.batchNumber }
        const { success } = await batchDeleteProductTemuAsync(params)
        if (success) {
          this.$message.success('删除成功')
          await this.getList()
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
      });
    },
    batchEditing() {
      if (this.rowIds.length == 0) {
        this.$message.error('请选择要编辑的数据')
        return
      }
      this.ruleForm = {}
      this.editName = false
      this.editPopupVisible = true
    },
    onEditor(row) {
      if (!row) return
      this.ruleForm.shopName = row.shopName
      this.ruleForm.groupId = (row.groupId && row.groupId !== '0') ? row.groupId.toString() : null
      this.ruleForm.OperateSpecialUserId = (row.operateSpecialId && row.operateSpecialId !== '0') ? row.operateSpecialId.toString() : null
      this.ruleForm.userId = (row.userId && row.userId !== '0') ? row.userId.toString() : null;
      this.ruleForm.ids = row.sku_Id ? row.sku_Id.split(",") : []
      this.editName = true
      this.editPopupVisible = true
    },
    //复选框数据
    chooseCode(row) {
      this.rowIds = row.map(item => item.sku_Id)
      this.rowbatchNumbers = row.map(item => item.batchNumber)
    },
    //产品SKC回调
    productSKCCodeCallback(e) {
      this.ListInfo.sku_Id = e
    },
    //商品编码回调
    commodityCodeCallback(e) {
      this.ListInfo.goodsCodes = e
    },
    //产品ID回调
    orderNoInnerBack(e) {
      this.ListInfo.procodes = e
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importProductInfo_PddAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    //查看日志
    async onShowLog(row) {
      this.proCodeLogs = row.proCode;
      this.logDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.refpddoperationLog.clearnFilter();
        this.$refs.refpddoperationLog.onSearch();
      })
    },
    async getList(type) {

      // this.ListInfo.auditTime = [];
      this.ListInfo.startTime = null;
      this.ListInfo.endTime = null;
      if (this.ListInfo.auditTime) {
        this.ListInfo.startTime = dayjs(this.ListInfo.auditTime[0]).format('YYYY-MM-DD HH:mm:ss');
        this.ListInfo.endTime = dayjs(this.ListInfo.auditTime[1]).format('YYYY-MM-DD HH:mm:ss');
      }

      this.ListInfo.onTimeStartTime = null;
      this.ListInfo.onTimeEndTime = null;
      if (this.ListInfo.onTime) {
        this.ListInfo.onTimeStartTime = dayjs(this.ListInfo.onTime[0]).format('YYYY-MM-DD HH:mm:ss');
        this.ListInfo.onTimeEndTime = dayjs(this.ListInfo.onTime[1]).format('YYYY-MM-DD HH:mm:ss');
      }

      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getProductTemuList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async handleCommand(command) {
      switch (command) {
        //下载模版
        case 'a':
          await this.downLoadFile()
          break;
      }
    },
    async downLoadFile() {
      window.open("/static/excel/KjBillCharges/跨境产品管理.xlsx", "_blank");
    },
    //设置店铺下拉
    async onchangeplatform(val) {
      const res = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
      this.shopList = res.data.list || [];
      this.shopList.push({ shopCode: "{线下}", shopName: "{线下}" });
      this.filter.shopCode = "";
      await this.getList();
    },
    async getDirectorlist() {
      const res1 = await getDirectorList({})
      const res2 = await getDirectorGroupList({})
      // const res3 = await getProductBrandPageList()

      this.directorList = [{ key: '0', value: '未知' }].concat(res1.data || []);
      this.directorListUserType = [];
      res1.data.forEach(f => {
        if (f.userType)
          this.directorListUserType.push(f);
      });

      this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);
      // this.bandList = res3.data?.list
    },
    async onExport() {//导出列表数据；
        this.loading = true
        const { data } = await ExportProductTemuList(this.ListInfo);
        this.loading = false
        const aLink = document.createElement("a");
        let blob = new Blob([data], { type: "application/vnd.ms-excel;charset=utf-8"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '跨境产品管理' + new Date().toLocaleString() + '.xlsx')
        aLink.click()
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
  flex-wrap: wrap;
  align-items: center;

  .publicCss {
    width: 200px;
    margin: 0 5px 0 0;
    align-items: center;
  }
}

::v-deep .el-select__tags-text {
  max-width: 75px;
}
</style>
