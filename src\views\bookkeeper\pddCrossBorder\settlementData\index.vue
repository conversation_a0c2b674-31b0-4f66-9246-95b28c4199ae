<template>
  <MyContainer>
    <el-tabs v-model="activeName" style="height: 95%;" >
      <el-tab-pane label="TEMU-全托交易收入" name="first1" style="height: 98%;">
        <transactionIncome />
      </el-tab-pane>
      <el-tab-pane label="TEMU-全托售后预留/释放金额" name="first2" :lazy="true" style="height: 98%;">
        <reserveAmountAfterSale />
      </el-tab-pane>
      <el-tab-pane label="TEMU-半托交易收入" name="first3" :lazy="true" style="height: 98%;">
        <halfPalletTemu :billingType="1" ref="halfPalletTemu1" />
      </el-tab-pane>
      <el-tab-pane label="TEMU-半托售后退款" name="first4" :lazy="true" style="height: 98%;">
        <halfPalletTemu :billingType="2" ref="halfPalletTemu2" />
      </el-tab-pane>
      <el-tab-pane label="TEMU-半托运费收入" name="first5" :lazy="true" style="height: 98%;">
        <halfPalletTemu :billingType="3" ref="halfPalletTemu3" />
      </el-tab-pane>
      <el-tab-pane label="TEMU-半托运费退款" name="first6" :lazy="true" style="height: 98%;">
        <halfPalletTemu :billingType="4" ref="halfPalletTemu4" />
      </el-tab-pane>
      <el-tab-pane label="TEMU-半托运输费用" name="first7" :lazy="true" style="height: 98%;">
        <halfPalletTemu :billingType="5" ref="halfPalletTemu5" />
      </el-tab-pane>
    </el-tabs>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import transactionIncome from './transactionIncome.vue'
import reserveAmountAfterSale from './reserveAmountAfterSale.vue'
import halfPalletTemu from './halfPalletTemu.vue'

export default {
  components: {
    MyContainer, transactionIncome, reserveAmountAfterSale, halfPalletTemu,
  },
  data() {
    return {
      activeName: 'first1'
    };
  },
  methods: {
   
  }
};
</script>

<style lang="scss" scoped></style>
