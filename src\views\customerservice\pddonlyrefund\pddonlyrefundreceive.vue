<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="维度">
                    <el-select v-model="filter.dimensionality" filterable @change="getdimensionalityChange"
                        style="width: 100px">
                        <el-option label="接待账号" :value="1" aria-selected="true" />
                        <el-option label="真实姓名" :value="2" />
                        <el-option label="店铺" :value="3" />
                        <el-option label="天" :value="4" />
                    </el-select>
                </el-form-item>
                <el-form-item label="申请日期" style="padding: 0;margin: 0; ">
                    <el-date-picker style="width: 280px" v-model="filter.timerange" type="datetimerange"
                        format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" range-separator="至"
                        start-placeholder="开始接待时间" end-placeholder="结束接待时间" :picker-options="pickerOptions" :default-value="defaultDate">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="" style="padding: 0;margin: 0; ">
                    <el-date-picker style="width: 280px" v-model="filter.timerange2" type="datetimerange"
                        format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" range-separator="至"
                        start-placeholder="同意退款开始时间" end-placeholder="同意退款结束时间" :picker-options="pickerOptions" :default-value="defaultDate">
                    </el-date-picker>
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0; ">
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0; ">
                    <el-input v-model.trim="filter.chatUser" style="width: 140px" placeholder="接待账号"
                        @keyup.enter.native="onSearch" clearable maxlength="100" />
                </el-form-item>
                <el-form-item v-if="(filter.dimensionality == 2)" style="padding: 0;margin: 0; ">
                    <el-input v-model.trim="filter.sname" style="width: 100px" placeholder="真实姓名"
                        @keyup.enter.native="onSearch" clearable maxlength="100" />
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0; ">
                    <el-select v-model="filter.kfWorkDateValue" style="width: 180px" size="mini"
                        @change="onKfWorkDateChange">
                        <el-option label="客服上班时间(退款时间)" :value="1"></el-option>
                        <el-option label="非客服上班时间(退款时间)" :value="2"></el-option>
                        <el-option label="全部" :value="0"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.partValue" style="width: 100px" size="mini" @change="onPartValueChange">
                        <el-option label="全额退款" :value="1"></el-option>
                        <el-option label="部分退款" :value="2"></el-option>
                        <el-option label="全部" :value="0"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table2" :that="that" :isIndex="true" :hasexpand="true" @sortchange="sortchange"
                :summaryarry="summaryarry" :tableData="list" :tableCols="tableCols" :isSelection="false"
                :loading="listLoading" :tableHandles="tableHandles1" tablekey="table2" :isSelectColumn="false">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
        <el-dialog title="趋势图" :visible.sync="dialogMapVisible.visible" width="80%" v-dialogDrag>
            <my-container>
                <span>
                    <el-select v-model="filter.selMonth" placeholder="选择月" @change="getMapListChange" clearable=""
                        filterable style="width: 120px">
                        <el-option label="近一个月" :value="1" />
                        <el-option label="近二个月" :value="2" />
                        <el-option label="近三个月" :value="3" />
                    </el-select>
                </span>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data">
                    </buschar>
                </span>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
                </span>
            </my-container>
        </el-dialog>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList } from "@/api/operatemanage/base/shop";
import { getPddOnlyRefundReceivePageList, getPddOnlyRefundReceivePageMapList } from "@/api/customerservice/pddonlyrefund";
import buschar from '@/components/Bus/buschar';
const tableCols = [
    {
        istrue: true,
        prop: "receiveDate",
        label: "日期",
        width: "150",
        sortable: "custom",
    },
    {
        istrue: true,
        prop: "sName",
        label: "真实姓名",
        width: "150",
        sortable: "custom",
    },
    {
        istrue: false,
        prop: "receiveUser",
        label: "接待账号",
        width: "240",
        sortable: "custom",
    },
    {
        istrue: false,
        prop: "shopName",
        label: "所属店铺",
        width: "200",
        sortable: "custom",
    },
    // { istrue: true, prop: 'receiveUserUp', label: '接待人上级', width: '150'},
    {
        istrue: true,
        prop: "manualReply",
        label: "接待量",
        width: "150",
        sortable: "custom", //summaryEvent: true,
    },
    {
        istrue: true,
        prop: "receiveOrderTotalCount",
        label: "仅退款单量",
        width: "150",
        sortable: "custom", //summaryEvent: true,
    },
    {
        istrue: true,
        prop: "receiveOrderTotalMoney",
        label: "仅退款金额",
        width: "150",
        sortable: "custom", //summaryEvent: true,
    },
    {
        istrue: true,
        prop: "receiveOrderRate",
        label: "仅退款率",
        width: "150",
        sortable: "custom", //summaryEvent: true,
        formatter: (row) => { return row.receiveOrderRate ? row.receiveOrderRate + " %" : 0 + " %" }
    },
    {
        istrue: true,
        prop: "jrQty",
        label: "介入单量",
        width: "150",
        sortable: "custom", //summaryEvent: true,
    },
    {
        istrue: true,
        prop: "jrMoney",
        label: "介入金额",
        width: "150",
        sortable: "custom", //summaryEvent: true,
    },
    // {
    //     istrue: true,
    //     prop: "savedOrderTotalCount",
    //     label: "挽救总订单数",
    //     width: "150",
    //     sortable: "custom",
    // },
    // {
    //     istrue: true,
    //     prop: "savedOrderTotalMoney",
    //     label: "挽救总金额",
    //     width: "150",
    //     sortable: "custom",
    // },
    {
        istrue: true,
        display: true,
        label: "趋势图",
        style: "color:red;cursor:pointer;",
        width: 70,
        formatter: (row) => "趋势图",
        type: "click",
        handle: (that, row) => that.showchart(row),
    },
];
const tableHandles1 = [
    //{ label: "导出", handle: (that) => that.onExport() },
];
export default {
    name: "pddonlyrefundreceive",
    components: {
        cesTable,
        MyContainer,
        MyConfirmButton,
        MySearch,
        MySearchWindow,
        buschar
    },
    props: {},
    data() {
        return {
          date: '',
            dialogMapVisible: { visible: false, title: "", data: [] },
            that: this,
            shopList: [],
            filter: {
                timerange: [
                    formatTime(dayjs().subtract(3, "day"), "YYYY-MM-DD HH:mm"),
                    formatTime(new Date(), "YYYY-MM-DD HH:mm"),
                ],
                applyStartDate: null,
                applyEndDate: null,
                timerange2: [],
                agreeStartDate: null,
                agreeEndDate: null,
                dimensionality: 2,
                chatUser: null,
                sname: null,
                shopName: null,
                selMonth: null,
                chatContion: null,
                selDate: null,
                partValue: 1,
                kfWorkDateValue: 1,
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "receiveDate", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            pickerOptions: {
                shortcuts: [
                    {
                        text: "昨天",
                        onClick(picker) {
                            const tdate = new Date(
                                new Date().getTime() - 3600 * 1000 * 24 * 1
                            );
                            const start = new Date(
                                new Date(tdate.toLocaleDateString()).getTime()
                            );
                            start.setTime(start.getTime());
                            picker.$emit("pick", [start, start]);
                        },
                    },
                    {
                        text: "近三天",
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime());
                            const end = new Date(new Date(tdate.toLocaleDateString()));
                            const start = new Date(
                                new Date(tdate.toLocaleDateString()).getTime()
                            );
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit("pick", [start, end]);
                        },
                    },
                    {
                        text: "近一周",
                        onClick(picker) {
                            const tdate = new Date(
                                new Date().getTime() - 3600 * 1000 * 24 * 5
                            );
                            const end = new Date(
                                new Date(tdate.toLocaleDateString()).getTime() +
                                3600 * 1000 * 24 * 5
                            );
                            const start = new Date(
                                new Date(tdate.toLocaleDateString()).getTime()
                            );
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit("pick", [start, end]);
                        },
                    },
                    {
                        text: "近一个月",
                        onClick(picker) {
                            const tdate = new Date(
                                new Date().getTime() - 3600 * 1000 * 24 * 31
                            );
                            console.log("获取前一个月的时间", tdate.getDay());
                            const end = new Date(
                                new Date(new Date().toLocaleDateString()).getTime()
                            );
                            const start = new Date(
                                new Date(tdate.toLocaleDateString()).getTime()
                            );
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit("pick", [start, end]);
                        },
                    },
                ],
                    disabledDate(date) {
                    // 设置禁用日期
                    const start = new Date('1970/1/1');
                    const end = new Date('9999/12/31');
                    return date < start || date > end;
                    },
              defaultDate: new Date('1970/1/1')
            },
            tableHandles1: tableHandles1,
        };
    },
    async mounted() {
        await this.getShopList();
        await this.onSearch();
    },
    methods: {
        async getShopList() {
            const res1 = await getAllShopList({ platforms: [2] });
            this.shopList = [];
            res1.data?.forEach((f) => {
                if (f.isCalcSettlement && f.shopCode) this.shopList.push(f);
            });
        },
        async getdimensionalityChange() {
            if (!this.filter.timerange) {
                this.$message({ message: "请选择日期", type: "warning" });
                this.filter.dimensionality = 2;
                return;
            }
            this.tableCols.forEach((col) => {
                if (this.filter.dimensionality == 3) {
                    if (col.prop == "receiveUser") {
                        col.istrue = false;
                    }
                    if (col.prop == "shopName") {
                        col.istrue = true;
                    }
                    if (col.prop == "sName") {
                        col.istrue = false;
                    }
                } else if (this.filter.dimensionality == 2) {
                    if (col.prop == "receiveUser") {
                        col.istrue = false;
                    }
                    if (col.prop == "shopName") {
                        col.istrue = false;
                    }
                    if (col.prop == "sName") {
                        col.istrue = true;
                    }
                } else if (this.filter.dimensionality == 4) {
                    if (col.prop == "receiveUser") {
                        col.istrue = false;
                    }
                    if (col.prop == "shopName") {
                        col.istrue = false;
                    }
                    if (col.prop == "sName") {
                        col.istrue = false;
                    }
                }
                else {
                    if (col.prop == "receiveUser") {
                        col.istrue = true;
                    }
                    if (col.prop == "shopName") {
                        col.istrue = false;
                    }
                    if (col.prop == "sName") {
                        col.istrue = false;
                    }
                }
            });
            this.pager = { OrderBy: "receiveDate", IsAsc: false };
            this.onSearch();
        },
        async showchart(row, isSumChrt) {
            this.filter.isSumChrt = isSumChrt;
            this.filter.selMonth = null;
            if (isSumChrt != true) {
                this.filter.selDate = row.receiveDate;
                if (this.filter.dimensionality == 4) { this.filter.chatContion = row.receiveDate };
                if (this.filter.dimensionality == 3) { this.filter.chatContion = row.shopName };
                if (this.filter.dimensionality == 2) { this.filter.chatContion = row.sName };
                if (this.filter.dimensionality == 1) { this.filter.chatContion = row.receiveUser };
            }
            var params = { ...this.filter }
            if (isSumChrt == true) {
                params.dimensionality = 4;
                params.selDate = this.filter.applyEndDate;
            }
            console.log(params)
            let that = this;
            const res = await getPddOnlyRefundReceivePageMapList(params).then(res => {
                that.dialogMapVisible.visible = true;
                that.dialogMapVisible.data = res
                that.dialogMapVisible.title = res.legend[0]
            })
            this.dialogMapVisible.visible = true

        },
        async getMapListChange() {
            var params = { ...this.filter }
            if (this.filter.isSumChrt == true) {
                params.dimensionality = 4;
                params.selDate = this.filter.applyEndDate;
            }
            console.log(params)
            let that = this;
            this.dialogMapVisible.visible = false;
            const res = await getPddOnlyRefundReceivePageMapList(params).then(res => {
                that.dialogMapVisible.visible = true;
                that.dialogMapVisible.data = res
                that.dialogMapVisible.title = res.legend[0]
            })
            this.dialogMapVisible.visible = true

        },
        async onKfWorkDateChange() {
            await this.onSearch();
        },
        async onPartValueChange() {
            await this.onSearch();
        },
        //查询第一页
        async onSearch() {
            console.log(this.filter);
            if (!this.filter.timerange) {
                this.$message({ message: "请选择日期", type: "warning" });
                return;
            }
            this.$refs.pager.setPage(1);
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            this.filter.applyStartDate = null;
            this.filter.applyEndDate = null;
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.applyStartDate = this.filter.timerange[0];
                this.filter.applyEndDate = this.filter.timerange[1];
            }
            else {
                this.$message({ message: "请先选择日期", type: "warning" });
                return false;
            }
            this.filter.agreeStartDate = null;
            this.filter.agreeEndDate = null;
            if (this.filter.timerange2 && this.filter.timerange2.length > 1) {
                this.filter.agreeStartDate = this.filter.timerange2[0];
                this.filter.agreeEndDate = this.filter.timerange2[1];
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ...this.filter,
            };
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, "params");
            this.listLoading = true;
            const res = await getPddOnlyRefundReceivePageList(params);
            this.listLoading = false;
            if (!res?.success) {
                return;
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            data.forEach((d) => {
                d._loading = false;
            });
            this.list = data;
        },
        //排序查询
        async sortchange(column) {
            if (!column.order) this.pager = {};
            else {
                this.pager = {
                    OrderBy: column.prop,
                    IsAsc: column.order.indexOf("descending") == -1 ? true : false,
                };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            console.log(rows);
            rows.forEach((f) => {
                this.selids.push(f.id);
            });
        },
    },
};
</script>

<style lang="scss" scoped></style>
