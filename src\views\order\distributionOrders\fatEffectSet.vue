<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item>
          <el-date-picker v-model="filter.fatEffectMonth" type="month" value-format="yyyy-MM"
            placeholder="坪效时间"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-select v-model="filter.shipperFxName" placeholder="货主分销" class="publicCss" clearable>
            <el-option key="自卖" label="自卖" value="自卖" />
            <el-option v-for="item in fxUserNames" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onAdd">新增</el-button>
        </el-form-item>
      </el-form>
    </template>

    <!--列表-->
    <vxetablebase :id="'fatEffectSet202412131623'" ref="table" :that='that' :isIndex='true' @sortchange='sortchange'
      :tableData='list' :tableCols='tableCols' :loading="listLoading" :isSelectColumn="false">
    </vxetablebase>

    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>

    <!--新增-->
    <el-dialog :title="dialogSaveForm.title" :visible.sync="dialogSaveForm.visiable" width="20%" v-dialogDrag
      append-to-body>
      <div>
        <el-form ref="saveForm" :model="dialogSaveForm.form" label-width="120px">
          <el-row>
            <el-form-item label="系列编码" prop="fatEffectMonth">
              <el-date-picker style="width:200px" v-model="dialogSaveForm.form.fatEffectMonth" value-format="yyyy-MM"
                type="month" placeholder="坪效时间"></el-date-picker>
            </el-form-item>
            <el-form-item label="系列编码" prop="shipperFxName">
              <el-select style="width:200px" v-model="dialogSaveForm.form.shipperFxName" placeholder="货主分销"
                class="publicCss" clearable>
                <el-option key="自卖" label="自卖" value="自卖" />
                <el-option key="甲米" label="甲米" value="甲米" />
                <el-option key="微鸿工作室" label="微鸿工作室" value="微鸿工作室" />
                <el-option key="罗小曼" label="罗小曼" value="罗小曼" />
                <el-option key="优凡帽业" label="优凡帽业" value="优凡帽业" />
              </el-select>
            </el-form-item>
            <el-form-item label="坪效面积" prop="shipperFxName">
              <el-input-number style="width:200px" v-model="dialogSaveForm.form.fatEffectArea" placeholder="坪效面积"
                :precision="2" :min="0" :max="9999999" :controls="false" />
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="savefatEffectSet">保存</el-button>
        <el-button @click="dialogSaveForm.visiable = false">取消</el-button>
      </div>
    </el-dialog>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {getShipperFxNameList, getfatEffectSetList, savefatEffectSet, delfatEffectSetById, getfatEffectSetById } from '@/api/order/shipperFxOrder';
const tableCols = [
  { istrue: true, prop: 'shipperFxName', label: '货主分销', sortable: 'custom' },
  { istrue: true, prop: 'fatEffectMonth', label: '坪效时间', sortable: 'custom' },
  { istrue: true, prop: 'fatEffectArea', label: '坪效面积', sortable: 'custom' },
  {
    istrue: true, type: "button", label: '操作', width: "120", btnList: [
      { label: "编辑", handle: (that, row) => that.onEdit(row) },
      { label: "删除", handle: (that, row) => that.onDel(row) },
    ]
  }
];
export default {
  name: "productcreategroupchatlog",
  components: { MyContainer, MyConfirmButton, vxetablebase },
  data() {
    return {
      that: this,
      filter: {
        shipperFxName: null,
        fatEffectMonth: ''
      },
      tableCols: tableCols,
      list: [], 
      fxUserNames: [],
      total: 0,
      pager: { OrderBy: "fatEffectMonth", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,

      dialogSaveForm: {
        title: null,
        visiable: false,
        form: {
          id: 0,
          fatEffectMonth: null,
          shipperFxName: null,
          fatEffectArea: null
        }
      },
    };
  },
  async mounted() {
    await this.loadData();
    await getShipperFxNameList()
      .then(({ data }) => {
        this.fxUserNames = data;
      })
  },
  methods: {
    async loadData() {
      this.onSearch();
    },
    //获取查询条件
    getCondition() {
      let pager = this.$refs.pager.getPager();
      let page = this.pager;
      const params = {
        ...pager,
        ...page,
        ...this.filter,
      };
      return params;
    },
    async getList() {
      let params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await getfatEffectSetList(params);
      if (res.success) {
        this.total = res.data.total
        this.list = res.data.list;
      }
      this.listLoading = false;
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getList();
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    async onAdd() {
      this.dialogSaveForm.title = "新增坪效设置";
      this.dialogSaveForm.form.id = 0;
      this.dialogSaveForm.form.fatEffectMonth = '';
      this.dialogSaveForm.form.shipperFxName = '';
      this.dialogSaveForm.form.fatEffectArea = 0;
      this.dialogSaveForm.visiable = true;
    },
    async onEdit(row) {
      this.dialogSaveForm.title = "编辑坪效设置";
      const res = await getfatEffectSetById(row.id);
      if (res.success && res.data != null) {
        this.dialogSaveForm.form.id = res.data.id;
        this.dialogSaveForm.form.fatEffectMonth = res.data.fatEffectMonth;
        this.dialogSaveForm.form.shipperFxName = res.data.shipperFxName;
        this.dialogSaveForm.form.fatEffectArea = res.data.fatEffectArea;
        this.dialogSaveForm.visiable = true;
      }
    },
    async onDel(row) {
      this.$confirm("是否删除数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const res = await delfatEffectSetById(row.id);
          if (res.success) {
            this.$message({ type: "success", message: "删除成功" });
            await this.getList();
          }
        })
    },
    async savefatEffectSet() {
      const res = await savefatEffectSet(this.dialogSaveForm.form);
      if (res.success) {
        this.$message({ type: "success", message: "保存成功" });
        this.dialogSaveForm.visiable = false;
        this.onSearch();
      }
    }
  },
};
</script>
<style lang="scss" scoped></style>
