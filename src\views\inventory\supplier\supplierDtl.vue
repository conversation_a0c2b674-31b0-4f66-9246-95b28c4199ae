<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss" />
                <inputYunhan title="商品编码" placeholder="商品编码/多行输入请按回车" :maxRows="2000" :inputshow="0" :clearable="true"
                    :clearabletext="true" :showRowCount="true" :showBlank="false" @callback="multiGoodsCodeCallBack"
                    :inputt.sync="ListInfo.goodsCodes" :maxlength="30000" class="publicCss"></inputYunhan>
                <inputYunhan title="款式编码" placeholder="款式编码/多行输入请按回车" :maxRows="2000" :inputshow="0" :clearable="true"
                    :clearabletext="true" :showRowCount="true" :showBlank="false" @callback="multiStyleCodeCallBack"
                    :inputt.sync="ListInfo.styleCodes" :maxlength="30000" class="publicCss"></inputYunhan>
                <el-select v-model="ListInfo.labels" style="width: 320px ;height: 29px" placeholder="季节/节日" multiple
                    filterable collapse-tags clearable class="publicCss">
                    <el-option v-for="item in seasonList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.isBY" placeholder="是否包邮" class="publicCss" clearable>
                    <el-option label="是" :value="1" />
                    <el-option label="否" :value="0" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false" showsummary
            :summaryarry="summary" :isSelectColumn="false" style="width: 100%;  margin: 0;height: 400px;"
            :loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import inputYunhan from '@/views/base/goods/inputYunhan.vue';
import { getSupplierTagGoodsInfos } from '@/api/inventory/supplier'
const seasonList = [
    { label: '无标签', value: '无标签' },
    { label: '四季款（常年）', value: '四季款（常年）' },
    { label: '春季款（1月1日-4月底）', value: '春季款（1月1日-4月底）' },
    { label: '夏季款（3月15日-9月底）', value: '夏季款（3月15日-9月底）' },
    { label: '秋季款（8月15日-10月底）', value: '秋季款（8月15日-10月底）' },
    { label: '冬季款（9月15日-1月底）', value: '冬季款（9月15日-1月底）' },
    { label: '开学季（1月1日至2月底）', value: '开学季（1月1日至2月底）' },
    { label: '开学季（7月1日至8月底）', value: '开学季（7月1日至8月底）' },
    { label: '清明节（3月1日至3月底）', value: '清明节（3月1日至3月底）' },
    { label: '端午节（农历四月初五至四月底）', value: '端午节（农历四月初五至四月底）' },
    { label: '中秋节（农历七月十五至八月初十）', value: '中秋节（农历七月十五至八月初十）' },
    { label: '国庆节（9月1日至9月25日）', value: '国庆节（9月1日至9月25日）' },
    { label: '圣诞节（不允许进货）', value: '圣诞节（不允许进货）' },
    { label: '元旦节（农历十一月初一至腊月十五）', value: '元旦节（农历十一月初一至腊月十五）' },
    { label: '春节（农历十一月初一至腊月十五）', value: '春节（农历十一月初一至腊月十五）' }
]
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'styleCode', label: '款式编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'seasonOrFestivalTag', label: '商品标签', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'qty', label: '采购数量', tipmesg: '取值采购单管理-采购单的已确认、完成状态的不同采购单同一商品编码累加的数量' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'cost', label: '采购资金', tipmesg: '单价*采购数量' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderCount', label: '订单量', tipmesg: '取值国内全平台日报 - 编码日报对应商品编码的订单量，只绑定在采购数量最大的供应商上' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'saleCount', label: '销售数量', tipmesg: '取国内全平台日报 - 编码日报对应商品编码的销售数量，只绑定在采购数量最大的供应商上' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'isBY', label: '是否包邮', formatter: (row) => row.isBY == true ? '是' : row.isBY == false ? '否' : '' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, inputYunhan
    },
    props: {
        supInfo: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            seasonList,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: this.supInfo.startTime,//开始时间
                endTime: this.supInfo.endTime,//结束时间
                supplier_id: this.supInfo.supplier_id,//供应商id
                goodsCodes: null,
                styleCodes: null,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            summary: {}
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        multiGoodsCodeCallBack(val) {
            this.ListInfo.goodsCodes = val;
        },
        multiStyleCodeCallBack(val) {
            this.ListInfo.styleCodes = val;
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getSupplierTagGoodsInfos(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.summary = data.summary
                    this.loading = false
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
