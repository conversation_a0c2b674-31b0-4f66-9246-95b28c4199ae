<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
            <div class="top">
                <el-button-group>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期" clearable :picker-options="pickerOptions"
                            style="width: 240px;" :value-format="'yyyy-MM-dd'" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.createdUserId" clearable filterable placeholder="跨境选品人"
                            style="width: 150px">
                            <el-option v-for="item in createdUserList" :key="item.key" :label="item.value" :value="item.key" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.status" clearable filterable placeholder="状态"
                            style="width: 120px">
                            <el-option label="待抓明细" :value="4" />
                            <el-option label="可认领" :value="9" />
                            <el-option label="已认领" :value="10" />
                            <el-option label="已推新" :value="20" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-input v-model.trim="filter.goodsCompeteId" type="text" maxlength="20" clearable placeholder="竞品ID" style="width:150px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.platform" clearable filterable placeholder="平台" style="width: 140px"  >
                          <el-option v-for="(item) in platformlist" :key="'p1p-'+item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-input v-model.trim="filter.goodsCompeteName" type="text" maxlength="40" clearable placeholder="产品简称/竞品标题" style="width:150px;" />
                    </el-button>
                    
                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.pushNewUserId" clearable filterable placeholder="推荐人"
                            style="width: 150px">
                            <el-option v-for="item in pushNewUserList" :key="item.key" :label="item.value" :value="item.key" />
                        </el-select>
                    </el-button>

                    <el-button type="primary" @click="onSearch()">查询</el-button>
                </el-button-group>
            </div>
        </template>
        <vxetablebase :id="'HotSaleBrandPushNewKJSel202408041708'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :border="true"
            @sortchange='sortchange' @select='selectchange' :tableData='tableData' :tableCols='tableCols'
            :showsummary='true' :summaryarry='summaryarry' :isSelection="false" :isSelectColumn="false"
            style="width: 100%;  margin: 0" v-loading="listLoading" :height="'100%'">
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import datepicker from '@/views/customerservice/datepicker';
import { platformlist,pickerOptions } from '@/utils/tools';
import dayjs from 'dayjs';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    GetHotSaleBrandPushNewKJTemuUsers,GetHotSaleBrandPushNewKJPushUsers,
    GetHotSaleBrandPushNewKJSelPageList,SetHotSaleBrandPushNewKJSelPushNewUser,SaveHotSaleBrandPushNewCheck,

} from '@/api/operatemanage/productalllink/alllink'
const tableCols = [
    { sortable: 'custom', width: '150', align: 'center', prop: 'goodsCompeteId', label: '竞品ID', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'platform', label: '平台', formatter: (row) =>(row.platform==12?"希音":(row.platform==13?"拼多多跨境":""))  },
    { sortable: 'custom', width: '260', align: 'center', prop: 'goodsCompeteShortName', label: '产品简称', },
    { sortable: 'custom', width: '300', align: 'center', prop: 'goodsCompeteName', label: '竞品标题', },
    { type: 'images', width: '80', align: 'center', prop: 'goodsCompeteImgUrl', label: '竞品图片' },
    { sortable: 'custom', width: '100', align: 'center', prop: 'status', label: '状态', formatter: (row) =>row.statusName  },
    { sortable: 'custom', width: '100', align: 'center', prop: 'goodsCompetePrice', label: '商品售价($)', formatter: (row) =>row.goodsCompetePrice+"$"},
    { sortable: 'custom', width: '100', align: 'center',prop: 'createdUserId', label: '跨境选品人', formatter: (row) =>row.createdUserName },
    { sortable: 'custom', width: '150', align: 'center', prop: 'createdTime', label: '创建时间', },
    { sortable: 'custom', width: '100', align: 'center',prop: 'pushNewUserId', label: '推荐人', formatter: (row) =>row.pushNewUserName },
    {
        istrue: true, type: 'button', label: '操作', width: '220', align: 'center', 
        btnList: [
            { label: "认领", display: (row) => { return row.status !=9; }, handle: (that, row) => that.onRenLingYesOrNo(row,10) },
            { label: "取消认领", display: (row) => { return row.status !=10; }, handle: (that, row) => that.onRenLingYesOrNo(row,9) },
            { label: "推新", display: (row) => { return row.status !=10; }, handle: (that, row) => that.onPushNew(row,1) },
            { label: "详情",  handle: (that, row) => that.onPushNew(row,3) },
            { label: "日志",  handle: (that, row) => that.onSeeLog(row) },
        ]
    },
];
export default {
    name: "HotSaleBrandPushNewKJSel",
    components: {
        MyContainer, datepicker, vxetablebase
    },
    data() {
        return {
            that: this,
            auditVisible: false,
            activities: [],
            timeRanges: [],
            pickerOptions,
            filter: {
                timerange: [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
                createdStartDate: null,
                createdEndDate: null,
            },
            pager: { OrderBy: "createdTime", IsAsc: false },
            tableCols,
            tableData: [],
            total: 0,
            listLoading: false,
            pageLoading: false,
            sels: [],
            selids: [],
            summaryarry: {},
            platformlist: [{ value: 12, label: '希音' }, { value: 13, label: '拼多多跨境' }],

            createdUserList: [],
            pushNewUserList: [],
        }
    },
    async mounted() {
        await this.getSelectData();
        this.onSearch();
    },
    computed: {
    },
    methods: {
        async getSelectData() {
            let ret = await GetHotSaleBrandPushNewKJTemuUsers();
            this.createdUserList = ret.data;

            let ret2 = await GetHotSaleBrandPushNewKJPushUsers();
            this.pushNewUserList = ret2.data;

            
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getParam() {
            //推品日期
            if (this.filter.timerange && this.filter.timerange.length == 2) {
                this.filter.createdStartDate = this.filter.timerange[0];
                this.filter.createdEndDate = this.filter.timerange[1];
            }
            else {
                this.filter.createdStartDate = null;
                this.filter.createdEndDate = null;
            }
            let pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            return params;
        },
        async getList() {
            let param = this.getParam();
            this.listLoading = true
            const res = await GetHotSaleBrandPushNewKJSelPageList(param)
            this.listLoading = false
            console.log(res);
            if (res?.success) {
                this.tableData = res.data.list;
                this.total = res.data.total;
                this.summaryarry = res.data.summary;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onRenLingYesOrNo(row,status){
            let confimstr="";
            if(status==10){
                confimstr="认领";
            }
            else if(status==9){
                confimstr="取消认领";
            }
            else{
                this.$message({ type: 'error', message: '状态错误，请刷新后重试!' });
                return;
            }
            this.$confirm('确认要'+confimstr+'吗？', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await SetHotSaleBrandPushNewKJSelPushNewUser({ id: row.id,isGet:(status==10?true:false) });
                if (res?.success) {
                    this.$message({ type: 'success', message: confimstr+'成功' });
                    this.onSearch();
                }
            }).catch(() => { });
        },
        async onPushNew(row,mode){
            if(mode==1)
            {
                this.listLoading = true
                const res = await SaveHotSaleBrandPushNewCheck({ id: row.id});
                this.listLoading = false
                if(!res?.success){
                    return;
                }
            }
            let self = this;
            self.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsale/HotSaleBrandPushNewKJSelEdit.vue`,
                title: (mode==1?"推新":"查看"),
                args: { id: row.id,mode:mode,hotSaleBrandPushNewId:row.hotSaleBrandPushNewId},
                height: 700,
                callOk: self.afterSave
            });
        },
        afterSave(){
            this.onSearch();
        },
        async onSeeLog(row){
            let self = this;
            self.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsale/HotSaleBrandPushNewKJSelLog.vue`,
                title: '日志',
                args: { id: row.id},
                width: '%40',
                height: '600px',
            });
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}
</style>
