<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select v-model="ListInfo.warehousesArr" multiple clearable filterable :collapse-tags="true"
                    placeholder="请选择仓库" class="publicCss">
                    <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                        :value="item.wms_co_id" />
                </el-select>
                <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable
                    class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            :summaryarry="summaryarry" @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading"
            :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace } from '@/utils/getCols'
import { getAllWarehouse } from '@/api/inventory/warehouse'
import { getGoodsInventoryListAsync } from '@/api/inventory/abnormal'
const tableCols = [
    { width: '100', align: 'center', prop: 'picture', label: '商品图片', type: 'images' },
    { sortable: 'custom', width: '150', align: 'left', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: '150', align: 'left', prop: 'goodsName', label: '商品名称', },
    { sortable: 'custom', width: '120', align: 'left', prop: 'styleCode', label: '款式编码', },
    { sortable: 'custom', width: '150', align: 'left', prop: 'wmsName', label: '仓库', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'color', label: '规格', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'standard', label: '规格1', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'jstUsableQty', label: '聚水潭可用数', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'quantity', label: '数量', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'orderUseQty', label: '订单占有数', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'usableQty', label: '可用数', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'publicUsableQty', label: '公有可用数', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'wmsWaitSendQty', label: '仓库待发数量', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'safeLowerLimit', label: '安全库存下限', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'safeUpperLimit', label: '安全库存上限', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'safeDayLowerLimit', label: '安全天数下限', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'safeDayUpperLimit', label: '安全天数上限', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'inWmsInventory', label: '进货仓库存', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'rejectInventory', label: '次品库存', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'returnWmsInventory', label: '销退仓库存', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'allotQty', label: '调拨在途数', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'inTransitQty', label: '采购在途数', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'virtualQty', label: '虚拟库存', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'modifyTime', label: '库存更新时间', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                goodsCode: null,//商品编码
                goodsName: null,//
                warehousesArr: [],//仓库
                warehouses: '',//仓库
            },
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            warehouselist: [],
            summaryarry: {}
        }
    },
    async mounted() {
        await this.getWareHouse()
        await this.getList()
    },
    methods: {
        async getWareHouse() {
            const { data, success } = await getAllWarehouse()
            this.warehouselist = data.filter((x) => x.name.indexOf('代发') < 0);
            this.warehouselist.unshift({ name: "全仓", co_id: 10361546, wms_co_id: 11793337, is_main: false, remark1: null, remark2: null, wms_co_id: -1 });
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.ListInfo.warehouses = this.ListInfo.warehousesArr.length > 0 ? this.ListInfo.warehousesArr.join(',') : ''
            const replaceArr = ['goodsCode'] //替换空格的方法,该数组对应str类型的input双向绑定的值
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data: { list, total, summary }, success } = await getGoodsInventoryListAsync(this.ListInfo)
                if (success) {
                    this.tableData = list
                    this.total = total
                    this.summaryarry = summary
                } else {
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.$message.error('获取列表失败')
            } finally {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>