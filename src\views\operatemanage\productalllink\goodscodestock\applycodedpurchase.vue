<template>
    <container v-loading="pageLoading">
        <!--列表-->
        <div style="height: 200px;">
            <el-tag type="danger" effect="dark">有限期：{{ addForm.offTime }} 前，未认领则默认</el-tag>
            <el-descriptions :column="8" direction="vertical" v-loading="listLoading" border>
                <el-descriptions-item label="图片" label-class-name="my-label" content-class-name="my-content">
                    <el-image :src="addForm.picture" @click="showImg(addForm.picture)"
                        style="max-width: 60px; max-height: 60px;" fit="fill" :lazy="true"
                        loading="https://via.placeholder.com/80x80?text=Loading"
                        error="https://via.placeholder.com/80x80?text=Error"></el-image>
                </el-descriptions-item>
                <el-descriptions-item label="商品编码">
                    {{ addForm.goodsCode }}
                </el-descriptions-item>
                <el-descriptions-item label="毛三利率">
                    {{ addForm.profit3Rate?.toFixed(2) }}
                </el-descriptions-item>
                <el-descriptions-item label="1天周转天数">
                    {{ addForm.turnoverDays?.toFixed(2) }}
                </el-descriptions-item>
                <el-descriptions-item label="3天周转天数">
                    {{ addForm.turnoverDays3?.toFixed(2) }}
                </el-descriptions-item>
                <el-descriptions-item label="进货数量">
                    <el-input-number v-model="addForm.count" :precision="0" :step="1" :min="0" :max="100000000"
                        :controls="false" label="描述文字"></el-input-number>
                </el-descriptions-item>
                <el-descriptions-item label="卖完下架">
                    <el-switch v-model="addForm.isSale" active-color="#13ce66" inactive-color="#ff4949" style="margin-right: 15px;"></el-switch>
                </el-descriptions-item>
            </el-descriptions>
        </div>
        <el-image-viewer v-if="showGoodsImage" :url-list="imgList" :on-close="closeFunc" style="z-index:9999;" />
    </container>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { getProductGoodsCode, addOrUpdateProductGoodsCode, approvalPurchaseGoods } from "@/api/inventory/goodscodestock"
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'

const tableCols = [];
const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
];

export default {
    name: 'YunHanAdminCodedpurchase',
    components: { container, MyConfirmButton, vxetablebase, cesTable, ElImageViewer },
    props: {
        //filter: {}
        title: {
            type: String,
            default: '编码进货'
        },
    },

    data() {
        return {
            that: this,
            filter: {
                goodsCode: null,
                appStatus: 0,
                isSuccess: 0
            },
            list: [],
            addForm: {
                id: null,
                goodsCode: null,
                picture: null,
                profit3Rate: null,
                turnoverDays: null,
                turnoverDays3: null,
                expectedSale: 0,
                count: 0,
                salesDay: 0,
                salesDay7: 0,
                salesDay15: 0,
                isSale: null,
                offTime: null,
            },
            
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "goodsCode", IsAsc: false },
            total: 0,
            sels: [],
            dayNum: 0,
            listLoading: false,
            pageLoading: false,
            showGoodsImage: false,
        };
    },

    async mounted() {

    },

    methods: {
        async onSearch(row) {
            debugger
            console.log('接收数据', row)
            this.addForm.offTime = null;
            this.listLoading = true;
            if (row) {
                const { goodsCode, picture, profit3Rate, turnoverDays, turnoverDays3, expectedSale, salesDay, salesDay7, salesDay15 } = row;
                this.addForm = {
                    ...this.addForm,
                    goodsCode,
                    picture,
                    profit3Rate,
                    turnoverDays,
                    turnoverDays3,
                    expectedSale,
                    salesDay,
                    salesDay7,
                    salesDay15,
                };             
                this.dayNum = 0
                try {
                    var para = { goodsCode, appStatus: row.appStatus }
                    const res = await getProductGoodsCode(para);
                    const { data } = res;
                    this.addForm.count = data?.count || 0;
                    this.addForm.id = data?.id || 0;
                    this.addForm.offTime = data.offTime;
                    this.addForm.isSale = data.isSale;
                } catch (error) {
                    this.addForm.count = 0;
                    this.addForm.id = 0;
                    this.addForm.offTime = null;
                    this.addForm.isSale = false;
                }
            }
            this.listLoading = false;
        },
        async onFinish(isEnd) {
            if (this.addForm.count == null || this.addForm.count == 0) {
                this.$message({ message: '请填写进货量！', type: "warning" });
                return false;
            }
            // if (this.addForm.hotCount == null || this.addForm.hotCount == 0) {
            //     this.$message({ message: '请填写活动预估量！', type: "warning" });
            //     return false;
            // }
            var para = { ... this.addForm, IsEnd: isEnd, appStatus: 1 };
            var res = await addOrUpdateProductGoodsCode(para);
            if (res?.success) {
                this.addForm.id = res?.data || 0;
                this.$message({ message: '成功', type: "success" });
                return true;
            } else {
                this.addForm.count = 0;
                //this.$message({ message: res.msg, type: "warning" });
                return false;
            }
        },
        async showImg(e) {
            this.showGoodsImage = true;
            this.imgList = [];
            this.imgList.push(e);
        },
        async closeFunc() {
            this.showGoodsImage = false;
        },
        async cellclick(row, column, cell, event) {

        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>

<style lang="scss" scoped></style>