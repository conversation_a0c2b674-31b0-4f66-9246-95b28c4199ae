{"name": "yunhan.admin", "version": "1.5.1", "private": true, "description": "ERP前端解决方案", "author": "", "scripts": {"serve": "node --max_old_space_size=8192 node_modules/@vue/cli-service/bin/vue-cli-service.js serve", "servev3": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build": "vue-cli-service build --max_old_space_size=16384 --open", "build:pro": "vue-cli-service build", "build:prov3": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "build:prod": "node --max_old_space_size=8192 node_modules/@vue/cli-service/bin/vue-cli-service.js build", "install:pkg": "npm install --registry https://registry.npmmirror.com --legacy-peer-deps", "lint": "vue-cli-service lint", "fix-memory-limit": "cross-env LIMIT=8192 increase-memory-limit"}, "dependencies": {"@better-scroll/core": "^2.4.2", "@better-scroll/mouse-wheel": "^2.4.2", "@better-scroll/observe-dom": "^2.4.2", "@better-scroll/observe-image": "^2.4.2", "@better-scroll/pull-down": "^2.4.2", "@better-scroll/scroll-bar": "^2.4.2", "@form-create/component-wangeditor": "2.5.5", "@form-create/element-ui": "2.5.5", "@microsoft/signalr": "^7.0.2", "@toast-ui/editor": "3.2.0", "@toast-ui/editor-plugin-code-syntax-highlight": "3.1.0", "@toast-ui/editor-plugin-color-syntax": "3.1.0", "@toast-ui/editor-plugin-table-merged-cell": "3.1.0", "@vue/shared": "^3.2.2", "@vxe-ui/plugin-export-xlsx": "^3.0.6", "axios": "0.27.2", "canvg": "^4.0.1", "codemirror": "5.64.0", "compressorjs": "^1.2.1", "core-js": "^3.33.3", "crypto-js": "4.1.1", "dayjs": "1.11.5", "decimal.js": "^10.4.3", "default-passive-events": "^2.0.0", "dom-zindex": "^1.0.6", "drag-tree-table": "^2.2.0", "echarts": "^5.3.3", "element-ui": "2.15.10", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "html2canvas": "^1.4.1", "jquery": "^3.6.1", "js-cookie": "3.0.1", "jsbarcode": "^3.11.5", "json-bigint": "^1.0.0", "jspdf": "^2.5.1", "jszip": "^3.10.1", "lodash": "4.17.21", "markdown-it": "^14.1.0", "mathjs": "^12.4.2", "oidc-client": "1.11.5", "pdf-lib": "^1.17.1", "print-js": "^1.6.0", "qrcode": "^1.5.4", "socket.io-client": "^4.6.1", "sortablejs": "1.15.0", "throttle-debounce": "^3.0.1", "vue": "2.7.10", "vue-awesome": "^4.3.1", "vue-clipboard2": "^0.3.1", "vue-count-to": "^1.0.13", "vue-i18n": "8.26.7", "vue-plugin-hiprint": "^0.0.52", "vue-quill-editor": "^3.0.6", "vue-router": "3.5.3", "vuedraggable": "^2.24.3", "vuex": "3.6.2", "vxe-table-plugin-export-xlsx": "^2.2.2", "wechat-emoji-parser": "^1.1.0", "xe-utils": "^3.5.7", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "4.5.15", "@vue/cli-plugin-eslint": "4.5.15", "@vue/cli-plugin-unit-jest": "4.5.11", "@vue/cli-service": "4.5.15", "@vue/test-utils": "1.1.3", "autoprefixer": "10.4.8", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "compression-webpack-plugin": "5.0.2", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "html-webpack-plugin": "5.2.0", "mockjs": "1.1.0", "quill-image-extend-module": "^1.1.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "video.js": "7.20.3", "vue-template-compiler": "2.7.10", "webpack": "^4.0.0"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "license": "MIT"}