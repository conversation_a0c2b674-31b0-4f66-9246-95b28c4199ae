<template>
    <!-- 评分设置 -->
    <div>
        <vxetablebase :id="'AccountGradeSet'" 
        :hasSeq="false"
        :border="true" 
        :hasexpand='true' 
        :hascheck="false"
        :height="'720'"
        :align="'center'"
        ref="table" 
        :that='that'  
        :tableData='tasklist'  
        :tableCols='tableCols'  
        :loading="listLoading" 
        >
        <template slot="right">
                <vxe-column title="操作"   width="140" fixed="right">
                    <template #default="{ row }">
                        <el-button type="text" @click="onEditAdd(row)">编辑</el-button> 
                        <el-button type="text" @click="onEditAdd(row)">删除</el-button> 
                    </template>
                </vxe-column>
        </template>    
        </vxetablebase>
    </div>
</template>

<script>
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
const tableCols = [
    { istrue: true, prop: 'product_AccountGradeSet', label: '照片', width: '70', align: 'center', },
    { istrue: true, prop: 'store_AccountGradeSet', label: '天数', width: '70', align: 'center',},
    { istrue: true, prop: 'tools_AccountGradeSet', label: '评分', width: '70', align: 'center', },
    { istrue: true, prop: 'monet_AccountGradeSet', label: '比例', width: '70', align: 'center',},
    { istrue: true, prop: 'user_AccountGradeSet', label: '', width: '20', align: 'center', },
    { istrue: true, prop: 'marks_AccountGradeSet', label: '视频', width: '70', align: 'center',},
    { istrue: true, prop: 'order1_AccountGradeSet', label: '天数', type:'image', width: '70', align: 'center' },
    { istrue: true, prop: 'order2_AccountGradeSet', label: '评分', type:'image', width: '70', align: 'center' },
    { istrue: true, prop: 'order3_AccountGradeSet', label: '比例', type:'image', width: '70', align: 'center'},
    { istrue: true, prop: 'order4_AccountGradeSet', label: '', width: '20', align: 'center'},

    { istrue: true, prop: 'marks_AccountGradeSet', label: '微。视频', width: '80', align: 'center',},
    { istrue: true, prop: 'order1_AccountGradeSet', label: '天数', type:'image', width: '70', align: 'center' },
    { istrue: true, prop: 'order2_AccountGradeSet', label: '评分', type:'image', width: '70', align: 'center' },
    { istrue: true, prop: 'order3_AccountGradeSet', label: '比例', type:'image', width: '70', align: 'center'},
    { istrue: true, prop: 'order4_AccountGradeSet', label: '', width: '20', align: 'center'},

    { istrue: true, prop: 'marks_AccountGradeSet', label: '详情页', width: '75', align: 'center',},
    { istrue: true, prop: 'order1_AccountGradeSet', label: '天数', type:'image', width: '70', align: 'center' },
    { istrue: true, prop: 'order2_AccountGradeSet', label: '评分', type:'image', width: '70', align: 'center' },
    { istrue: true, prop: 'order3_AccountGradeSet', label: '比例', type:'image', width: '70', align: 'center'},
    { istrue: true, prop: 'order4_AccountGradeSet', label: '', width: '20', align: 'center'},

    { istrue: true, prop: 'marks_AccountGradeSet', label: '照片建模', width: '80', align: 'center',},
    { istrue: true, prop: 'order1_AccountGradeSet', label: '天数', type:'image', width: '70', align: 'center' },
    { istrue: true, prop: 'order2_AccountGradeSet', label: '评分', type:'image', width: '70', align: 'center' },
    { istrue: true, prop: 'order3_AccountGradeSet', label: '比例', type:'image', width: '70', align: 'center'},
    { istrue: true, prop: 'order4_AccountGradeSet', label: '', width: '20', align: 'center'},

    { istrue: true, prop: 'marks_AccountGradeSet', label: '视频建模', width: '80', align: 'center',},
    { istrue: true, prop: 'order1_AccountGradeSet', label: '天数', type:'image', width: '70', align: 'center' },
    { istrue: true, prop: 'order2_AccountGradeSet', label: '评分', type:'image', width: '70', align: 'center' },
    { istrue: true, prop: 'order3_AccountGradeSet', label: '比例', type:'image', width: '70', align: 'center'},
];
export default {
    name: 'AccountGradeSet',

    components: {vxetablebase},
    data() {
        return {
            that: this,  
            tableCols: tableCols, 
            tasklist:[], 
            listLoading: false,
        };
    },

    mounted() {
        
    },

    methods: {
        
    },
};
</script>

<style lang="scss" scoped>

</style>