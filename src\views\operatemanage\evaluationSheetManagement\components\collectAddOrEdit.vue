<template>
    <div>
        <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="150px" class="demo-ruleForm">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="ID:" prop="proCode">
                        <el-input v-model="ruleForm.proCode" placeholder="请输入ID" maxlength="20" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="产品简称:" prop="proName">
                        <el-input v-model="ruleForm.proName" placeholder="请输入产品简称" maxlength="30" clearable />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="单量:" prop="orderCount">
                        <el-input-number v-model="ruleForm.orderCount" :min="1" :max="9999999" placeholder="请输入单量"
                            :controls="false" :precision="0" style="width: 100%;" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="是否需要评价:" prop="isNeedEvaluation">
                        <el-select v-model="ruleForm.isNeedEvaluation" placeholder="是否需要评价" clearable
                            style="width: 100%;">
                            <el-option label="是" :value="true" />
                            <el-option label="否" :value="false" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="放单小组:" prop="groupId">
                        <el-select filterable v-model="ruleForm.groupId" placeholder="放单小组" clearable
                            style="width: 100%;">
                            <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value"
                                :value="item.key" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="小组对接人:" prop="groupContactId">
                        <el-select filterable v-model="ruleForm.groupContactId" clearable placeholder="小组对接人"
                            style="width: 100%;">
                            <el-option v-for="item in operationsCenterList" :key="item.userId" :label="item.userName"
                                :value="item.userId" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="放单链接:" prop="releaseLink">
                        <uploadimgFile v-if="editPriceVisible" ref="uploadimgFile" :accepttyes="accepttyes"
                            :isImage="true" :uploadInfo="chatUrls" :keys="[1, 1]" @callback="getImg" :imgmaxsize="1"
                            :limit="1" :multiple="true">
                        </uploadimgFile>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="特殊注意事项:" prop="specialPrecautions">
                        <el-input v-model="ruleForm.specialPrecautions" type="textarea" placeholder="请输入特殊注意事项"
                            maxlength="50" clearable />
                    </el-form-item>
                </el-col>
            </el-row>
            <div style="display: flex;justify-content: center;">
                <el-button @click="$emit('close')">关闭</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')" v-throttle="2000">提交</el-button>
            </div>
        </el-form>
    </div>
</template>

<script>
import { saveEvaluationFormManage, getOperationsCenterList, getEvaluationFormManageDtl } from '@/api/operatemanage/EvaluationFormManage'
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
export default {
    components: {
        uploadimgFile
    },
    props: {
        id: {
            type: String,
            default: null
        }
    },
    data() {
        return {
            ruleForm: {
                releaseLink: '',
                pictures: [],
                proCode: '',
                proName: '',
                orderCount: undefined,
                isNeedEvaluation: '',
                groupId: '',
                groupContactId: '',
                specialPrecautions: ''
            },
            rules: {
                proCode: [
                    { required: true, message: '请输入ID', trigger: 'blur' },
                ],
                proName: [
                    { required: true, message: '请输入产品简称', trigger: 'blur' },
                ],
                orderCount: [
                    { required: true, message: '请输入单量', trigger: 'blur' },
                ],
                isNeedEvaluation: [
                    { required: true, message: '请选择是否需要评价', trigger: 'change' },
                ],
                groupId: [
                    { required: true, message: '请输入放单小组', trigger: 'change' },
                ],
                groupContactId: [
                    { required: true, message: '请输入小组对接人', trigger: 'change' },
                ],
                releaseLink: [
                    { required: true, message: '请输入放单链接', trigger: 'change' },
                ],
                specialPrecautions: [
                    { required: true, message: '请输入特殊注意事项', trigger: 'blur' },
                ],
            },
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            editPriceVisible: false,
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            chatUrls: [],
            directorGroupList: [],
            operationsCenterList: []
        }
    },
    async mounted() {
        if (this.id) {
            await this.getDtl(this.id)
        }
        this.editPriceVisible = true
        await this.getDirectorlist1()
    },
    methods: {
        async getDtl(id) {
            this.editPriceVisible = false
            const { data } = await getEvaluationFormManageDtl({ id })
            data.groupId = data.groupId !== null ? String(data.groupId) : data.groupId
            this.ruleForm = data
            this.chatUrls = data.releaseLink.split(',').map(item => ({ url: item }))
        },
        async getDirectorlist1() {
            const res2 = await getDirectorGroupList()
            this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);
            const { data } = await getOperationsCenterList()
            this.operationsCenterList = data
        },
        getImg(data) {
            if (data) {
                this.chatUrls = data
                this.ruleForm.releaseLink = data.map(item => item.url).join(',')
            }
        },
        submitForm(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    const { success } = await saveEvaluationFormManage(this.ruleForm)
                    if (success) {
                        this.$message.success('操作成功')
                        this.$emit('close')
                        this.$emit('getList')
                    }
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
    }
}
</script>

<style scoped lang="scss"></style>