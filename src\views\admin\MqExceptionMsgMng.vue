<!-- 异常队列消息处理  -->
<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
      
            <!--表单-->
             <!--列表-->
        <ces-table ref="table" :that='that'  :tableData='list' :tableCols='tableCols' >
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button type="primary" @click="loadData" >刷新</el-button>
                </el-button-group>
            </template>
        </ces-table>

       

    </my-container>
</template>
<script>  
   
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import cesTable from "@/components/Table/table.vue";
    import request from '@/utils/request'

    const tableCols = [
        { istrue: true, prop: 'queueName', label: '队列名称', width: '160', sortable: true },
        { istrue: true, prop: 'deliveryTag', label: '消息标识', width: '70', sortable: true },
        { istrue: true, prop: 'queueMessage', label: '消息内容' },
        { istrue: true, prop: 'exceptionContent', label: '异常内容'},  
        {
            istrue: false, type: 'button', label: '操作', width: '140',
            btnList: [
                {
                    label: "重回队列", handle: (that, row) => that.execMsg(row,1),                   
                },
                // {
                //     label: "驳回丢弃", handle: (that, row) => that.execMsg(row,2),                   
                // },
                {
                    label: "默认成功", handle: (that, row) => that.execMsg(row,3),                   
                },
            ]
        },  
    ];


    const mqUrls=[
    `${process.env.VUE_APP_BASE_API}`,
    //`${process.env.VUE_APP_BASE_API_Express}`,
    `${process.env.VUE_APP_BASE_API_Financial}`,
    `${process.env.VUE_APP_BASE_API_Order}`,
    `${process.env.VUE_APP_BASE_API_OperateManage}`,
    `${process.env.VUE_APP_BASE_API_Inventory}`,
    `${process.env.VUE_APP_BASE_API_BookKeeper}`,
    //`${process.env.VUE_APP_BASE_API_MonthBookKeeper}`,
    `${process.env.VUE_APP_BASE_API_CustomerService}`,
    `${process.env.VUE_APP_BASE_API_Media}`,
    `${process.env.VUE_APP_BASE_API_PddOperateManage}`,
    `${process.env.VUE_APP_BASE_API_Profit}`,
    `${process.env.VUE_APP_BASE_API_PackProcess}`
    
    ];
  
    export default {
        name: "MqExceptionMsgMng",
        components: { MyContainer, MyConfirmButton ,cesTable,request},
        data() {
            return {
                that: this,             
                pageLoading: false,
                formEditMode: true,//是否编辑模式              
                mode: 1,
                tableCols:tableCols,
                list:[],
                mqUrls:mqUrls,
            };
        },
        async mounted() {
            this.loadData();
        },
        computed: {          
        },
        methods: {         
            async loadData() {
                let self=this;
                this.list=[];
                this.pageLoading=true;

                this.mqUrls.forEach(async (apiPrefix)=>{
                    let  func1 = (params, config = {}) => { return request.get(apiPrefix + '/Mq/GetExceptionConsumDtos', { params: params, ...config }) }

                    try{
                        let tempRlt=await func1();
                        if(tempRlt && tempRlt.success){
                            tempRlt.data.forEach(d=>{
                              self.list.push({...d,apiPrefix:apiPrefix})  
                            })
                        }
                    }
                    catch(ex){

                    }
                })


                this.pageLoading=false;
            },   
            /*
                @execType 1驳回重新归队、2驳回不归队、3默认为成功
            */
            async execMsg(row,execType){
                let self=this;
                let  func1 = (params, config = {}) => { return request.get(row.apiPrefix + '/Mq/ExecExceptionConsum', { params: params, ...config }) }

                let tempRlt=await func1({queueName:row.queueName,tag:row.deliveryTag,execType:execType});
                if(tempRlt && tempRlt.success){
                    this.$message.success('操作成功！');
                    this.loadData();                    
                }              
            }
        },
    };
</script>
