<template>
    <MyContainer>
        <el-tabs v-model="activeName" @tab-click="tabclick">
            <el-tab-pane label="报价记录" name="one">
                <vendorOrders ref="order" v-loading="listLoading" />
            </el-tab-pane>
            <el-tab-pane label="采购价格区间" name="seven">
                <purchaseOrderPriceInterval ref="purchaseOrderPriceInterval" v-loading="listLoading" />
            </el-tab-pane> 
            <el-tab-pane label="汇总" name="two">
                <vendorSumIndex ref="vendorSumIndex" v-loading="listLoading" />
            </el-tab-pane>
            <el-tab-pane label="未提交" name="three" v-loading="listLoading">
                <vendorNotSubmitted />
            </el-tab-pane>
            <el-tab-pane label="新品提交" name="four" v-loading="listLoading">
                <vendorNewSubmitted />
            </el-tab-pane>
            <el-tab-pane label="咨询记录" name="five" v-loading="listLoading">
                <searchRecord />
            </el-tab-pane>
            <el-tab-pane label="电话记录" name="six" v-loading="listLoading">
                <mobileRecord />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import vxetablebase from "@/components/VxeTable/vxetablebase.vue";
import vendorSummary from "./components/vendorSummary.vue"
import vendorSumIndex from "./components/vendorSumIndex.vue"
import vendorOrders from "./components/vendorOrders.vue"
import vendorNewSubmitted from "./components/vendorNewSubmitted.vue"
import vendorNotSubmitted from "./components/vendorNotSubmitted.vue"
import mobileRecord from "./components/mobileRecord.vue"
import searchRecord from "./components/searchRecord.vue"

import purchaseOrderPriceInterval from "@/views/inventory/purchaseOrderPriceInterval.vue"

export default {
    components: { MyContainer, cesTable, vxetablebase, vendorSummary, vendorOrders, vendorNotSubmitted, vendorNewSubmitted, vendorSumIndex, searchRecord, mobileRecord , purchaseOrderPriceInterval},
    name: "vendorQuote",
    data() {
        return {
            activeName: 'one',//tab切换
            listLoading: true,
        };
    },
    mounted() {
        setTimeout(() => {
            this.listLoading = false
        }, 300)
    },
    methods: {
        tabclick(e) {
            this.listLoading = true
            this.$refs.vendorSumIndex.clear()
            //延迟加载
            setTimeout(() => {
                this.listLoading = false
            }, 500)
        }
    }
};
</script>

<style lang="scss" scoped></style>
