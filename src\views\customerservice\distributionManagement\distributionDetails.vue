<template>
    <my-container v-loading="summaryLoading">
        <div class=".top">
            <el-input v-model="summaryInfo.key" placeholder="请输入推荐人" style="width: 200px;margin-right: 10px;" clearable
                maxlength="30" />
            <el-date-picker v-model="summaryPickValue" type="month" placeholder="选择月" style="margin-right: 10px;"
                @change="handleChooseDate" />
            <el-button type="primary" style="margin-right: 10px;" @click="searchSummary">搜索</el-button>
            <el-button type="primary" @click="openComouteDialog">一键计算</el-button>
        </div>
        <cesTable ref="detailTable" :table-data="tableData" :table-cols="tableCols" :is-index="true" :that="that"
            :showsummary="true" :summaryarry="summaryarry" style="width: 100%; height: 90%; margin: 0"  @sortchange='sortchange'></cesTable>
            <template #footer>
                <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="summaryTotal"
            @get-page="getSummaryInfo" @page-change="summaryPagechange" @size-change="summarySizechange" />
        </template>


        <!-- 一键计算弹窗 -->
        <el-dialog title="一键计算" :visible.sync="comouteDialogValue" width="30%" :before-close="handleClose" v-dialogDrag>
            <div class="computeBox">
                <el-date-picker v-model="computeValue" type="month" placeholder="选择月" style="margin-right: 10px;"
                    @change="chooseComputeDate" />
                <div class="btnBox">
                    <el-button type="primary" style="margin-right: 10px;" @click="handleClick">开始计算</el-button>
                    <el-button style="margin-right: 10px;" @click="handleClose">取消</el-button>
                </div>
            </div>
        </el-dialog>

        <!-- 明细弹窗 -->
        <el-dialog title="推荐人明细" :visible.sync="detailDialog" width="70%" :before-close="handleClose" v-dialogDrag>
            <div>
                <el-select v-model="detailInfo.keys" filterable placeholder="请输入被推荐人" :filter-method="searchDistributors"
                    @change="changeDistributors" multiple collapse-tags style="margin-right: 10px;">
                    <el-option v-for="item in DistributorsOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-date-picker v-model="detailPickerValue" type="datetimerange" :picker-options="pickerOptions"
                    range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right"
                    style="margin-right: 10px;width: 220px;">
                </el-date-picker>
                <el-button type="primary" style="margin-right: 10px;" @click="searchDetail">搜索</el-button>
            </div>
            <div style="margin-top: 20px;">
                <el-table :data="detailTableData" style="width: 100%" show-summary :summary-method="getDetailSummaries">
                    <el-table-column type="index" label="#" width="50">
                    </el-table-column>
                    <el-table-column prop="yearMonth" label="日期" width="100" sortable>
                        <template slot-scope="scope">
                            <span>{{ scope.row.yearMonth.slice(0, 4) + '-' + scope.row.yearMonth.slice(4) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="tuiJianRenName" label="推荐人">
                    </el-table-column>
                    <el-table-column prop="beiTuiJianrenName" label="被推荐人">
                    </el-table-column>
                    <el-table-column prop="saleMoney" label="被推荐人销售额" sortable>
                    </el-table-column>
                    <el-table-column prop="profit3" label="被推荐人毛三" sortable>
                    </el-table-column>
                    <el-table-column prop="money" label="推荐人提成点" sortable>
                    </el-table-column>
                </el-table>
                <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="detailTotal"
                    @get-page="publicDetailInfo" @page-change="detailPagechange" @size-change="detailSizechange" />
            </div>
        </el-dialog>

    </my-container>
</template>

<script>
//导入dayjs
import dayjs from 'dayjs'
import MyContainer from "@/components/my-container";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getDistributorTCSummary, calculateSummary, getDistributorTCDetail, queryAllDistributorTop100, queryAllDistributorByTJR } from '@/api/customerservice/Distributor'
import cesTable from "@/components/Table/table.vue";
const tableCols = [
    { istrue: true, prop: 'yearMonth', label: '日期', sortable: 'custom', formatter: (row) => row.yearMonth.slice(0, 4) + '-' + row.yearMonth.slice(4) },
    { istrue: true, prop: 'tuiJianRenName', label: '推荐人', sortable: 'custom' },
    { istrue: true, prop: 'tuiJianRenType', label: '推荐人类型', sortable: 'custom' },
    { istrue: true, prop: 'money', label: '推荐人提成点',  sortable: 'custom' },
    {istrue:true,display:true,label:'', style:"color: rgb(72, 132, 243);cursor:pointer;",width:70,formatter:(row)=>'明细', type:'click',handle:(that,row)=>that.openDetail(row)}, 
]
export default {
    name: "distributionDetails1",
    components: {
        MyContainer, cesTable, MySearch, MySearchWindow
    },
    data () {
        return {
            that: this,
            tableCols: tableCols,
            selectDetailValue: null,//明细下拉框
            detailSummaryarry: null,//明细汇总数据
            summaryarry: null,//汇总数据
            detailPickerValue: null,//明细时间选择器
            DistributorsOptions: [],//明细推荐人下拉框
            detailRefferName: '',//明细推荐人
            detailTotal: null,//明细总数
            detailTableData: [],//明细表格
            summaryLoading: true,//汇总loading
            summaryPickValue: '',//汇总时间时间选择器
            summaryInfo: {
                key: null,//推荐人
                startDate: null,//开始时间
                endDate: null,//结束时间
                CurrentPage: 1,//当前页
                PageSize: 50,//每页条数
                OrderBy: null,//排序字段
                IsAsc: false//是否升序
            },//汇总信息
            detailInfo: {
                tuiJianRenId: null,//推荐人id
                keys: [],//推荐人组
                startDate: null,//开始时间
                endDate: null,//结束时间
                CurrentPage: 1,//当前页
                PageSize: 50,//每页条数
                OrderBy: null,//排序字段
                IsAsc: false,//是否升序
            },//推荐人明细信息
            detailDialog: false,//明细弹窗
            computeValue: '',//一键计算时间
            comouteDialogValue: false,//一键计算弹窗
            tableData: [],
            summaryTotal: null,
            pickerOptions: {
                shortcuts: [{
                    text: '近一周',
                    onClick (picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近一个月',
                    onClick (picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近三个月',
                    onClick (picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }],
                disabledDate (time) {
                    return time.getTime() > Date.now();
                }
            },
        }
    },
    mounted () {
        this.getSummaryInfo()
    },
    methods: {
        sortchange (column) {
            console.log(column, 'column');
            if (column.order) {
                var orderField = column.prop;
                this.summaryInfo.OrderBy = orderField;
                this.summaryInfo.IsAsc = column.order.indexOf("descending") == -1 ? true : false
                this.getSummaryInfo()
            }
        },
        getDetailSummaries (param) {
            const sums = [];
            if (!this.detailSummaryarry)
                return sums
            var arr = Object.keys(this.detailSummaryarry);
            if (arr.length == 0)
                return sums
            const { columns, data } = param;
            var hashj = false;
            columns.forEach((column, index) => {
                if (this.detailSummaryarry.hasOwnProperty(column.property + '_sum')) {
                    //把this.detailSummaryarry赋值到对应的列上
                    var sum = this.detailSummaryarry[column.property + '_sum'];
                    if (sum == null) return;
                    else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
                    else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum.toFixed(2)
                    else sums[index] = sum.toFixed(0)
                }
                else sums[index] = ''
            });
            return sums
        },
        //q:我怎么将这个汇总行放到页面的最下面去
        //a:在el-table上加上show-summary属性
        getSummaries (param) {
            const sums = [];
            if (!this.summaryarry)
                return sums
            var arr = Object.keys(this.summaryarry);
            if (arr.length == 0)
                return sums
            const { columns, data } = param;
            var hashj = false;
            columns.forEach((column, index) => {
                if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
                    var sum = this.summaryarry[column.property + '_sum'];
                    if (sum == null) return;
                    else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
                    else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum.toFixed(2)
                    else sums[index] = sum.toFixed(0)
                }
                else sums[index] = ''
            });
            return sums
        },
        changeDistributors (e) {
            console.log(e, 'e');
            this.selectDetailValue = e
            console.log(this.detailInfo.keys, 'detailInfo.keys');
            // 找出数组e和this.DistributorsOptions相同的值，并取出他们的label，添加到this.detailInfo.keys中
            this.detailInfo.keys = this.DistributorsOptions.filter(item => e.includes(item.value)).map(item => item.value)
        },
        //明细被推荐人搜索方法
        async searchDistributors (e) {
            if (e.length > 200) {
                //给提示
                this.$message.error('最多只能输入200个字符')
                return
            }
            const { data } = await queryAllDistributorByTJR({ keywords: e, drp_co_id: this.detailInfo.tuiJianRenId })
            if (data) {
                this.DistributorsOptions = data.map(item => {
                    return {
                        value: item.drp_co_id,
                        label: item.name
                    }
                })
            }
        },
        //搜索明细
        searchDetail () {
            if (this.detailPickerValue) {
                //使用dayjs将时间转化为年月日时分秒
                this.detailInfo.startDate = dayjs(this.detailPickerValue[0]).startOf('month').format('YYYY-MM-DD HH:mm:ss')
                this.detailInfo.endDate = dayjs(this.detailPickerValue[1]).endOf('month').format('YYYY-MM-DD HH:mm:ss')
            } else {
                this.detailInfo.startDate = null
                this.detailInfo.endDate = null
            }
            //发请求
            this.publicDetailInfo()
        },
        //选择一键计算时间
        chooseComputeDate () {
            this.clear()
            console.log(this.computeValue, 'computeValue');
            //如果选择的月份大于当前月份，就提示
            if (this.computeValue) {
                if (dayjs(this.computeValue).format('YYYY-MM') > dayjs().format('YYYY-MM')) {
                this.$message.error('不能选择大于当前月份的时间')
                this.computeValue = null
                return
            }
           }
            if (this.computeValue) {
                this.summaryInfo.startDate = dayjs(this.computeValue).startOf('month').format('YYYY-MM-DD HH:mm:ss')
            }
        },
        //选择汇总月
        handleChooseDate () {
            console.log(this.summaryPickValue, 'summaryPickValue');
            //如果选择的月份大于当前月份，就提示
            if (this.summaryPickValue) {
                if (dayjs(this.summaryPickValue).format('YYYY-MM') > dayjs().format('YYYY-MM')) {
                this.$message.error('不能选择大于当前月份的时间')
                this.summaryPickValue = null
                return
            }
            }
          
            //如果选择的时间为空，就拉原始数据
            if (!this.summaryPickValue) {
                this.summaryInfo.startDate = null
                this.getSummaryInfo()
            } else {
                this.summaryInfo.startDate = dayjs(this.summaryPickValue).startOf('month').format('YYYY-MM-DD HH:mm:ss')
                this.getSummaryInfo()
            }
        },
        //搜索汇总  
        searchSummary () {
            //去掉推荐人输入框的空格
            this.summaryInfo.key = this.summaryInfo.key.replace(/\s+/g, "")
            //清空summaryInfo.startDate
            this.summaryInfo.startDate = null
            //将当前页变为1
            this.summaryInfo.CurrentPage = 1;
            if (this.summaryPickValue) {
                //使用dayjs将时间转化为年月日时分秒
                this.summaryInfo.startDate = dayjs(this.summaryPickValue).startOf('month').format('YYYY-MM-DD HH:mm:ss')
            }
            this.getSummaryInfo()
        },
        //明细页面数量改变
        detailSizechange (val) {
            this.detailInfo.CurrentPage = 1;
            this.detailInfo.PageSize = val;
            this.publicDetailInfo();
        },
        //明细当前页改变
        detailPagechange (val) {
            this.detailInfo.CurrentPage = val;
            this.publicDetailInfo();
        },
        //汇总页面数量改变
        summarySizechange (val) {
            this.summaryInfo.CurrentPage = 1;
            this.summaryInfo.PageSize = val;
            this.getSummaryInfo();
        },
        //汇总当前页改变
        summaryPagechange (val) {
            this.summaryInfo.CurrentPage = val;
            this.getSummaryInfo();
        },
        //获取汇总当前数据
        async getSummaryInfo () {
            const { data } = await getDistributorTCSummary(this.summaryInfo)
            if (data) {
                this.summaryLoading = false
                this.tableData = data.list
                this.summaryTotal = data.total
                this.summaryarry = data.summary
            }
        },
        //清空数据
        clear () {
            this.summaryInfo = {
                key: null,//推荐人
                startDate: null,//开始时间
                endDate: null,//结束时间    
                CurrentPage: 1,//当前页
                PageSize: 20,//每页条数
                OrderBy: null,//排序字段
                IsAsc: false//是否升序
            }

        },
        //抽离获取明细信息
        async publicDetailInfo () {
            console.log(this.detailInfo, 'detailInfo');
            const { data, success } = await getDistributorTCDetail(this.detailInfo)
            if (success) {
                if (data) {
                    this.detailTableData = data.list
                    this.detailTotal = data.total
                    this.detailSummaryarry = data.summary
                    console.log(this.detailSummaryarry, 'detailSummaryarry');
                } else {
                    this.detailTableData = data
                }
                this.detailDialog = true
            }
        },
        //明细弹窗
        async openDetail (row) {
            //清空选择器中的值
            this.detailInfo.keys = []
            this.detailInfo.startDate = null
            this.detailInfo.endDate = null
            //清空日期
            this.detailPickerValue = null
            if (this.summaryInfo.startDate != null && this.summaryInfo.startDate != '' && this.summaryInfo.startDate != undefined) {
                this.detailInfo.startDate = this.summaryInfo.startDate
            }
            this.detailInfo.tuiJianRenId = row.tuiJianRenId
            console.log(this.detailInfo, 'detailInfo');
            const { data } = await queryAllDistributorByTJR({ keywords: this.selectDetailValue, drp_co_id: row.tuiJianRenId })
            if (data) {
                this.DistributorsOptions = data.map(item => {
                    return {
                        value: item.drp_co_id,
                        label: item.name
                    }
                })
            }
            this.publicDetailInfo()
        },
        //一键计算确定
        async handleClick () {
            //先清空一下数据
            this.clear()
            ///将.publicDetailInfolue转换成年月日时分秒
            this.summaryInfo.startDate = dayjs(this.computeValue).startOf('month').format('YYYY-MM-DD HH:mm:ss')
            const { data, success } = await calculateSummary(this.summaryInfo)
            if (success) {
                if (data == null) {
                    this.tableData = data
                } else {
                    this.tableData = data.list
                }
                this.comouteDialogValue = false
                this.$message.success('计算成功')
            } else {
                this.$message.error('已超过当前月份，计算失败')
            }
        },
        //一键计算弹窗
        openComouteDialog () {
            this.clear()
            this.comouteDialogValue = true
        },
        //一键计算关闭
        handleClose () {
            // this.clear()
            this.comouteDialogValue = false
            this.detailDialog = false
        }
    }
}
</script>

<style scoped lang="scss">
.computeBox {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .btnBox {
        display: flex;
        margin-top: 20px;
        justify-content: space-evenly;
    }
}

.tableBox {
    position: relative;
    padding: 30px 0 40px;

    .top {
        display: flex;
        align-items: center;
        position: fixed;
        top: 150px;
        z-index: 999;
    }

    .pageBox {
        width: 88%;
        position: fixed;
        bottom: 20px;
        z-index: 999;
    }
}
</style>