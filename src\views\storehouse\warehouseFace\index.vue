<template>
  <my-container v-loading="pageLoading">
      <template #header>
          <el-date-picker style="width: 220px; margin-right: 10px;" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" range-separator="至" :start-placeholder="'提交开始时间'" :end-placeholder="'提交结束时间'"
              :picker-options="pickerOptions">
          </el-date-picker>

          <el-select v-model="filter.status" style="width:120px; margin-right: 10px;" placeholder="通知状态" :clearable="true">
              <el-option label="未通知" :value="0"></el-option>
              <el-option label="已通知" :value="1"></el-option>
          </el-select>

          <YhUserelector :value.sync="filter.notifyUserIds" maxlength="50" @change="getUserList($event, $index)"
              :text.sync="filter.notifyUserNames" style="width:170px; margin-right: 10px;" placeholder="请输入通知人">
          </YhUserelector>

          <el-select v-model="filter.warehouseId" placeholder="请选择仓库" filterable style="width:120px; margin-right: 10px;" clearable  >
            <el-option v-for="item in Warehouseslist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>

          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="dialogHisVisible = true">配置</el-button>
      </template>

      <template style="margin-top: 10px;">
          <vxetablebase :id="'DeductBeforeZrDtlList202408041744'"  ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
              :summaryarry="summaryarry" @select="selectchange" :tableData='list' :tableCols='tableCols'
              :isSelection="true" :loading="listLoading">
          </vxetablebase>
      </template>

      <template #footer>
          <my-pagination ref="pager" :total="total" :checked-count="selids.length" @get-page="getlist" />
      </template>

      <el-dialog title="监控配置" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="60%" height="600px"
          v-dialogDrag>
          <jiankongdialog ref="jiankongdialog" @closeDialog="dialogHisVisible = false" :orderNoInner="orderNoInner" :Warehouseslist="Warehouseslist"
              style="z-index:10000;height:600px" />
      </el-dialog>
  </my-container>
</template>

<script>
import MyConfirmButton from '@/components/my-confirm-button'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import YhUserelector from '@/components/YhCom/yh-userselector.vue'
import dayjs from "dayjs";
import { formatTime, formatdate, } from "@/utils";
import { pickerOptions } from '@/utils/tools'
import { PageDeductBeforeZrList, ExportDeductBeforeZrList } from "@/api/order/deductbefore"
import { equipmentAlarmLogPage } from "@/api/bladegateway/worcestorejk.js"

import MyContainer from "@/components/my-container";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import jiankongdialog from "@/views/storehouse/warehouseFace/jiankongdialog.vue";
import { getWarehouses } from "@/api/storehouse/storehouse";


const tableCols = [
  // { istrue: true, prop: 'orderNoInner', fixed: 'left', label: '内部单号', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showLogDetail(row) },

  { istrue: true, prop: 'deviceName', label: '设备名称', width: '150', sortable: 'custom',  },
  { istrue: true, prop: 'deviceMac', label: 'MAC地址', width: '150', sortable: 'custom',  },
  { istrue: true, prop: 'deviceCode', label: '设备号', width: '130', sortable: 'custom',  },
  { istrue: true, prop: 'deviceLocation', label: '设备位置', width: '150', sortable: 'custom',  },
  { istrue: true, prop: 'warehouseName', label: '仓库', width: '160', sortable: 'custom',  },
  { istrue: true, prop: 'alarmType', label: '事件', width: '120', sortable: 'custom',  },
  { istrue: true, prop: 'alarmTime', label: '报警时间', width: '160', sortable: 'custom', formatter: (row) => formatdate(row.alarmTime)  },
  { istrue: true, prop: 'pic', label: '图片', width: '120', type: 'images'  },
  { istrue: true, prop: 'notifyUserNames', label: '通知人',width: 'auto', sortable: 'custom', formatter: (row) => row.notifyUserNames  },

  { istrue: true, prop: 'status', label: '通知状态', width: '120', sortable: 'custom', formatter: (row) => row.status == 1?'已通知':row.status == 0?'未通知':'', },
 
  // { istrue: true, prop: 'node3NoPass', label: '团打单超时2', width: '80', sortable: 'custom', formatter: (row) => row.node3NoPass == 1 && !row.node3ZrMemberName ? 'x' : row.node3ZrMemberName },
  // { istrue: true, prop: 'node4NoPass', label: '团配货超时2', width: '80', sortable: 'custom', formatter: (row) => row.node4NoPass == 1 && !row.node4ZrMemberName ? 'x' : row.node4ZrMemberName },
  // { istrue: true, prop: 'node5NoPass', label: '打包超时8', width: '80', sortable: 'custom', formatter: (row) => row.node5NoPass == 1 && !row.node5ZrMemberName ? 'x' : row.node5ZrMemberName },
  // { istrue: true, prop: 'node6NoPass', label: '称重超时10', width: '80', sortable: 'custom', formatter: (row) => row.node6NoPass == 1 && !row.node6ZrMemberName ? 'x' : row.node6ZrMemberName },

  // { istrue: true, prop: 'noPassSum', label: '违规合计', width: '70', sortable: 'custom' },


  // { istrue: true, prop: 'submitTime', label: '提交发货', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.submitTime, "MM-DD HH:mm") },
  // { istrue: true, prop: 'bindWaveTime', label: '绑定批次', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.bindWaveTime, "YYYY") == '2050' ? '' : formatTime(row.bindWaveTime, "MM-DD HH:mm") },
  // { istrue: true, prop: 'printMdTime', label: '打印面单', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.printMdTime, "YYYY") == '2050' ? '' : formatTime(row.printMdTime, "MM-DD HH:mm") },
  // { istrue: true, prop: 'phEndTime', label: '配货完成', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.phEndTime, "YYYY") == '2050' ? '' : formatTime(row.phEndTime, "MM-DD HH:mm") },
  // { istrue: true, prop: 'packageTime', label: '打包完成', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.packageTime, "YYYY") == '2050' ? '' : formatTime(row.packageTime, "MM-DD HH:mm") },
  // { istrue: true, prop: 'weightTime', label: '称重时间', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.weightTime, "YYYY") == '2050' ? '' : formatTime(row.weightTime, "MM-DD HH:mm") },
  // { istrue: true, prop: 'maxWmsName', label: '发货仓库', width: '120', sortable: 'custom' },
  // { istrue: true, prop: 'maxWaveId', label: '批次号', width: '70', sortable: 'custom' },
  // { istrue: true, prop: 'maxExpressCompanyName', label: '快递公司', width: '120', sortable: 'custom' },
  // { istrue: true, prop: 'waveOperator', label: '批次操作', width: '100', sortable: 'custom' },
  // { istrue: true, prop: 'waveRemark', label: '批次备注', width: '100', sortable: 'custom' },



]

export default {
  name: 'DeductBeforeZrDtlList',
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, jiankongdialog, vxetablebase, YhUserelector },
  props: {
      outFilter: {
          type: Object,
          default: () => { return {}; }
      },
  },
  data() {
      return {
          that: this,
          filter: {
              submitDateStart: null,
              submitDateEnd: null,
              orderNoInner: null,
              timerange: [
                  formatTime(dayjs().subtract(30, "day"), "YYYY-MM-DD"),
                  formatTime(dayjs().subtract(0, "day"), "YYYY-MM-DD"),
              ],
              keywords: null,
          },
          pickerOptions: pickerOptions,
          dialogHisVisible: false,
          orderNo: '',
          orderNoInner: 0,
          list: [],
          summaryarry: {},
          pager: { OrderBy: "", IsAsc: false },
          tableCols: tableCols,
          total: 0,
          sels: [],
          selids: [],
          listLoading: false,
          pageLoading: false,
          dialogVisible: false,
          setWareHouseList: [
                {
                    wmsId: null,//仓库id
                    wmsName: null,//仓库名称
                    headUid: null,//负责人id
                    headDuid: null,//负责人钉钉id
                    headName: null //负责人名称
                }
            ],
          Warehouseslist: []
      };
  },
  async mounted() {
      await this.onSearch()
      await this.getWarehousesList();
  },
  methods: {
      async getWarehousesList() {
        var res3 = await getWarehouses();
        this.Warehouseslist = res3.data?.map(item => { return { value: item.wms_co_id, label: item.name }; });
      },
      getUserList(val, i) {
            this.setWareHouseList[i].headUid = val ? val[0].extData.userId : null
            this.setWareHouseList[i].headDuid = val ? val[0].value : null
            this.setWareHouseList[i].headName = val ? val[0].label : null
        },
      showLogDetail(row) {
          this.orderNoInner = row.orderNoInner;
          this.dialogHisVisible = true;
      },
      onShowLogistics(row) {
          let self = this;
          this.$showDialogform({
              path: `@/views/order/logisticsWarning/DbLogisticsRecords.vue`,
              title: '物流明细',
              args: { expressNos: row.expressNo },
              height: 300,
          });
      },
      //查询第一页
      async onSearch() {
          this.$refs.pager.setPage(1)
          await this.getlist();
      },
      //获取查询条件
      getCondition() {
          if (this.filter.timerange && this.filter.timerange.length > 1) {
              this.filter.startQueryTime = this.filter.timerange[0];
              this.filter.endQueryTime = this.filter.timerange[1];
          } else {
              this.filter.startQueryTime = null;
              this.filter.endQueryTime = null;
              // this.$message({ message: "请先选择日期", type: "warning" });
              // return false;
          }
          var pager = this.$refs.pager.getPager();
          var page = this.pager;
          console.log(this.outFilter);
          const params = {
              ...pager,
              ...page,
              ...this.outFilter,
              ... this.filter,
          }
          console.log(params);
          return params;
      },
      //分页查询
      async getlist() {
          var params = this.getCondition();
          if (params === false) {
              return;
          }
          // this.list = [{}];
          this.listLoading = true
          const res = await equipmentAlarmLogPage(params)
          this.listLoading = false
          if (!res?.success) {
              return
          }

          this.selids = [];
          this.sels = [];

          res.data.list.map((item)=>{
              let newarra = [];
              if(item.notifyUserMap){
                  JSON.parse(item.notifyUserMap).filter(itemm=>{return itemm.label}).map((item2)=>{
                      newarra.push(item2.label)
                  });
                  item.notifyUserNames = newarra.join(',');
              }
          })

          this.total = res.data.total;
          const data = res.data.list;
          this.list = data;
          

          this.summaryarry = res.data.summary;
      },
      //排序查询
      async sortchange(column) {
          if(column.prop=='notifyUserNames'){
            column.prop = 'notifyUserIds';
          }

          if (!column.order)
              this.pager = {};
          else {
              this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
          }
          await this.onSearch();
      },
      selectchange: function (rows, row) {
          this.selids = [];
          this.sels = [];
          //console.log(rows)
          rows.forEach(f => {
              this.selids.push(f.id);
              this.sels.push(f);
          })
      },
      async onExport() {
          var params = this.getCondition();
          if (params === false) {
              return;
          }
          this.listLoading = true
          const res = await ExportDeductBeforeZrList(params)
          this.listLoading = false
          if (!res?.data) return
          const aLink = document.createElement("a");
          let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
          aLink.href = URL.createObjectURL(blob)
          aLink.setAttribute('download', new Date().toLocaleString() + '导出-违规明细.xlsx');
          aLink.click()
      },
  },
};
</script>

<style lang="scss" scoped></style>
