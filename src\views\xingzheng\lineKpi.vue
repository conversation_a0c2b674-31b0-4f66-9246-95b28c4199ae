<template>
    <div style="height:100%;">
        <el-form class="ad-form-query" :inline="true" :model="kpifilter" @submit.native.prevent>
            <el-row>
                <el-col>
                    <el-form-item>
                        <el-select v-model="kpifilter.cycle" placeholder="请选择周期" style="width:50px;" @change="getKpiData()" :disabled="pageLoading">
                            <el-option v-for="item in cycleList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>

                    <el-form-item v-if="kpifilter.cycle && kpifilter.cycle>0">
                        <el-date-picker :disabled="pageLoading" style="width: 210px" 
                        v-model="kpifilter.timerange" :type="calcDatePickerType" 
                        :picker-options="pickerOptions"
                        format="yyyy-MM-dd" 
                        value-format="yyyy-MM-dd" 
                        range-separator="至" 
                        start-placeholder="开始日期" end-placeholder="结束日期" 
                        :clearable="false" 
                        @change="getKpiData()"></el-date-picker>
                    </el-form-item>

                    <template v-else>                        
                        <el-form-item>
                            <el-date-picker :disabled="pageLoading" style="width:90px" 
                            v-model="kpifilter.startYear" type="year" 
                            :picker-options="pickerOptions"                          
                            value-format="yyyy-MM-dd" 
                            placeholder="开始年份"                      
                            :clearable="false" 
                            @change="getKpiData()"></el-date-picker>
                            -
                            <el-date-picker :disabled="pageLoading" style="width: 90px" 
                            v-model="kpifilter.endYear" type="year"  
                            :picker-options="pickerOptions"                       
                            value-format="yyyy-MM-dd" 
                            placeholder="结束年份"                      
                            :clearable="false" 
                            @change="getKpiData()"></el-date-picker>                           
                        </el-form-item>

                    </template>




                    <el-form-item>
                        <el-switch v-model="kpifilter.isYy" active-text="运营部门" inactive-text="其他部门" :active-value='1' :inactive-value='0' @change="changeDeptType()">
                        </el-switch>
                    </el-form-item>
                    <el-form-item>
                        <el-cascader @change="getKpiData()" ref="cascaderRef" :disabled="pageLoading" placeholder="请选择" :options="deptTreeList" v-model="selectDeptList" style="width:520px;" :props="{value: 'id',label: 'name',parentId: 'parentId',children: 'children' ,multiple:true,checkStrictly: true }" :show-all-levels="true" filterable clearable />
                    </el-form-item>
                    <el-form-item>
                        <el-select :disabled="pageLoading" v-model="kpifilter.platform" placeholder="请选择平台" @change="getKpiData()" style="width:100px;" clearable>
                            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-select :disabled="pageLoading" v-model="kpifilter.kpiTypeEnm" placeholder="请选择业绩类别" style="width:80px;" @change="getKpiCacheData">
                            <el-option v-for="item in kpiTypeList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="info" @click="onSyncCalcSum">同步数据({{ lastSyncTxt }})</el-button>                        
                    </el-form-item>

                    <el-form-item>
                        
                        <el-alert style="height:30px;" :title="alertTitle" type="warning" show-icon :closable="false">
                        </el-alert>                        
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div>
            <buschar ref="kpiExpress" :analysisData="expressData" :thisStyle="thisStyle" :GridStyle="gridStyle" v-loading="pageLoading"></buschar>
        </div>
    </div>
</template>


<script>       
    import { getDeptTypeTreeInfo } from '@/api/operatemanage/base/dingdingShow'
    import { listToTree } from '@/utils'
    import { getDayReportKpiAdminAsync , GetLastCalcSumDayReportKpiAdminTxt,CalcSumDayReportKpiAdminAsync } from '@/api/bookkeeper/reportday'
    import buschar from '@/components/Bus/buschar'
    import { formatTime } from "@/utils";
    import dayjs from "dayjs";

    const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
    const endTime = formatTime(new Date(), "YYYY-MM-DD");

    export default {
        components: {
            buschar
        },
        data () {
            return {
                pickerOptions: {
                    disabledDate (time) {
                        return time.getTime() < new Date(2021, 9, 1); 
                    },
                },
                kpifilter: {
                    isYy: 1,
                    hierarchyCodeList: [],
                    reportType: 1,//0:具体平台   1:所有平台
                    platform: 1, //EnmPlatform后台枚举
                    startTime: null,
                    endTime: null,
                    timerange: [startTime, endTime],
                    startYear:startTime,
                    endYear:endTime,
                    kpiTypeEnm: 2,//绩效默认净利润
                    cycle: 4//周期 默认日
                },

                selectDeptList: [],
                deptTypeName: "运营部",
                pageLoading: true,
                alertTitle: "温馨提示:人效=净利润/出勤人数(日：出勤人数=当天出勤人的工作小时数/10)",
                thisStyle: { width: '100%', height: '750px', 'box-sizing': 'border-box', 'line-height': '260px' },
                gridStyle: {
                    left: 0,
                    right: 0,
                    bottom: 20,
                    top: '12%',
                    containLabel: true
                },
                dataList: {},
                deptTreeList: [],
                deptList: [],
                cycleList: [{ label: '年', value: 0 },{ label: '月', value: 2 }, { label: '周', value: 3 }, { label: '日', value: 4 }],
                platformList: [{ label: '全平台', value: 0 }, { label: '天猫', value: 1 }, { label: '淘宝', value: 9 }, { label: '淘工厂', value: 8 }, { label: '拼多多', value: 2 }, { label: '头条放心购', value: 6 }, { label: '京东', value: 7 }],
                kpiTypeList: [{ label: '销售额', value: 0 }, { label: '毛三', value: 1 }, { label: '净利润', value: 2 }, { label: '订单量', value: 3 }],
                expressData: {},
                styleColor: [{ color: '#5470c6' }, { color: '#91cc75' }, { color: '#fac858' }, { color: '#ee6666' }, { color: '#73c0de' }, { color: '#3ba272' }, { color: '#fc8452' }, { color: '#9a60b4' }, { color: '#ea7ccc' }, { color: '#b97a57' }, { color: '#400080' }, { color: '#808000' }],

                lastSyncTxt:''
            };
        },
        computed:{
            calcDatePickerType() {
                // if(this.kpifilter.cycle==0)
                //     return 'yearrange';
                return this.kpifilter.cycle<=2? 'monthrange':'daterange';
            }
        },
        updated () {
            this.fixCascader()
        },
        async mounted () {

            let self=this;
            this.getLastCalcSumDayReportKpiAdminTxt();            

            await this.changeDeptType();
            await this.getKpiData();
        },
        methods: {
            fixCascader () {
                const cascader = document.querySelectorAll('.el-input__inner')
                for (let index = 0; index < cascader.length; index++) {
                    cascader[index].setAttribute('placeholder', '')
                }
            },
            async getKpiData () {
                var flag = false;
                this.kpifilter.hierarchyCodeList = [];
                if (this.kpifilter.platform != "" && this.kpifilter.platform != null) {
                    this.kpifilter.reportType = 0
                } else {
                    this.kpifilter.reportType = 1
                }
                this.kpifilter.startTime = null;
                this.kpifilter.endTime = null;
                if (this.kpifilter.timerange) {
                    this.kpifilter.startTime = this.kpifilter.timerange[0];
                    this.kpifilter.endTime = this.kpifilter.timerange[1];
                }

                //如果选择的是年，那么日期使用年的控件数据
                if(this.kpifilter.cycle==0){
                    this.kpifilter.startTime = this.kpifilter.startYear;
                    this.kpifilter.endTime = this.kpifilter.endYear;

                    if(this.kpifilter.startTime >this.kpifilter.endTime  ){
                        flag=true;
                        this.$message({
                                    message:  "开始年份不允许大于结束年份！",
                                    type: 'error'
                                })
                    }
                }

                //获取所有选中节点的层级code 后端进行数据过滤
                if (this.selectDeptList) {
                    let selectNodes = this.selectDeptList,
                        i = 0,
                        length = selectNodes.length;
                    for (; i < length; i++) {
                        if (i != 0) {
                            if (selectNodes[i].length != selectNodes[i - 1].length) {
                                var node = this.deptList.filter(f => f.hierarchyCode == selectNodes[i - 1].join("."));
                                this.$message({
                                    message: node[0].name + "其他节点不在同一级别 不允许选择！",
                                    type: 'error'
                                })
                                flag = true;
                                break;
                            }
                        }
                        this.kpifilter.hierarchyCodeList.push(selectNodes[i].join("."));
                    };
                }
                if (flag) {
                    return;
                }
                this.pageLoading = true;
                const params = { ...this.kpifilter };
                var res = await getDayReportKpiAdminAsync(params)
                this.dataList = res.data;
                this.changeAlertTitle();
                this.setOptions(this.dataList[this.kpifilter.kpiTypeEnm])
                this.expressData = this.dataList[this.kpifilter.kpiTypeEnm]
                await this.$refs.kpiExpress.initcharts();
                this.pageLoading = false;
            },
            async getKpiCacheData () {
                this.changeAlertTitle()
                this.setOptions(this.dataList[this.kpifilter.kpiTypeEnm])
                this.expressData = this.dataList[this.kpifilter.kpiTypeEnm]
                await this.$refs.kpiExpress.initcharts();
            },
            async setDeptList () {
                const data = await getDeptTypeTreeInfo({ isYy: this.kpifilter.isYy });
                if (data) {
                    this.deptList = data;
                    this.deptTreeList = listToTree(_.cloneDeep(data))
                }
            },
            async changeDeptType () {
                await this.setDeptList();
                this.selectDeptList = [];
                this.kpifilter.hierarchyCodeList = [];
                await this.getKpiData();
            },
            changeAlertTitle () {
                var cycleName = "";
                if (this.kpifilter.cycle == 2) {
                    cycleName = "（月：出勤人数=部门下当月出勤天数/(月份天数-4)）";
                }
                if (this.kpifilter.cycle == 3) {
                    cycleName = "（周：出勤人数=部门下当周内每天的出勤人数的考勤小时汇总/7/10）";
                }
                if (this.kpifilter.cycle == 4) {
                    cycleName = "（日：出勤人数=部门下当天出勤人的工作小时数/10）";
                }
                var thisItem = this.kpiTypeList.filter(l => l.value == this.kpifilter.kpiTypeEnm);
                this.alertTitle = "温馨提示:人效=" + thisItem[0].label + "/出勤人数" + cycleName;
            },
            setOptions (element) {
                var isNegative = false;
                var i = 0;
                element.series.forEach(s => {
                    s.barMaxWidth = '25';
                    s.itemStyle = this.styleColor[i];
                    if (isNegative == false) {
                        if (Math.min(...s.data) < 0) {
                            isNegative = true;
                        }
                    }
                    i++;
                })
                if (isNegative) {
                    element.yAxis.forEach(s => {
                        s.max = value => {
                            if (Math.abs(value.max) > Math.abs(value.min)) {
                                return (Math.abs(value.max) * 1.1).toFixed(0);
                            } else {
                                return (Math.abs(value.min) * 1.1).toFixed(0);
                            }
                        };
                        s.min = value => {
                            if (Math.abs(value.max) > Math.abs(value.min)) {
                                return (-Math.abs(value.max) * 1.1).toFixed(0);
                            } else {
                                return (-Math.abs(value.min) * 1.1).toFixed(0);
                            }
                        }
                    })
                }
            },
            async getLastCalcSumDayReportKpiAdminTxt(){
                let self=this;
                GetLastCalcSumDayReportKpiAdminTxt().then((rlt)=>{
                    if(rlt && rlt.success){

                        self.lastSyncTxt=rlt.data;
                        //this.$message.error(rlt.data);
                    }
                })
            },
            /**
             * 同步统计数据，方便快速查询              
             */
            async onSyncCalcSum(){
                let rlt =await CalcSumDayReportKpiAdminAsync();
                if(rlt && rlt.success){
                    if(rlt.data){
                        this.$message.error(rlt.data);
                    }else{
                        this.$message.success('同步成功');
                        this.getLastCalcSumDayReportKpiAdminTxt();
                    }
                }
            }
        }
    };
</script>

<style lang="scss" scoped>
</style>