<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
            <div class="top">
                <el-button-group>
                    
                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.chooseDimension" placeholder="维度"
                            style="width: 150px">
                            <el-option label="选品平台"  value="选品平台" />
                            <el-option label="选品小组"  value="选品小组" />
                            <el-option label="选品运营"  value="选品运营" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="选品开始日期" end-placeholder="选品结束日期" clearable :picker-options="pickerOptions"
                            style="width: 240px;" :value-format="'yyyy-MM-dd'" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.choosePlatforms" placeholder="选品平台" style="width: 150px" clearable  multiple collapse-tags>
                            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.chooseGroupIds" clearable filterable placeholder="选品小组"  multiple collapse-tags
                            style="width: 150px">
                            <el-option v-for="item in chooseGroupList" :key="item.chooseGroupId" :label="item.chooseGroupName" :value="item.chooseGroupId" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.chooseUserIds" clearable filterable placeholder="选品人"  multiple collapse-tags
                            style="width: 150px">
                            <el-option v-for="item in chooseUserList" :key="item.chooseUserId" :label="item.chooseUserName" :value="item.chooseUserId" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.chooseUserRoles" clearable filterable placeholder="职位"  multiple collapse-tags
                            style="width: 250px">
                            <el-option v-for="item in chooseUserRoleList" :key="item.chooseUserRole" :label="item.chooseUserRole" :value="item.chooseUserRole" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.chooseUserDeptNames" clearable filterable placeholder="架构"  multiple collapse-tags
                            style="width: 250px">
                            <el-option v-for="item in chooseUserDeptNameList" :key="item.chooseUserDeptName" :label="item.chooseUserDeptName" :value="item.chooseUserDeptName" />
                        </el-select>
                    </el-button>

                    <el-button type="primary" @click="onSearch()">查询</el-button>
                </el-button-group>
            </div>
        </template>
        <vxetablebase :id="'HotSaleBrandPushNewChoose0202408041713'" ref="vxetable" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :border="true"
            @sortchange='sortchange' @select='selectchange' :tableData='tableData' :tableCols='tableCols'
            :showsummary='true' :summaryarry='summaryarry' :isSelection="false" :isSelectColumn="false" @summaryClick='onsummaryClick'
            style="width: 100%;  margin: 0" v-loading="listLoading" :height="'100%'">
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

        <el-dialog :title="indexChatDialog.title"  :visible.sync="indexChatDialog.visible" width="80%" :close-on-click-modal="false" 
            element-loading-text="拼命加载中" v-dialogDrag v-loading="indexChatDialog.loading">       
            <buschar ref="indexchatbuschar1" v-if="!indexChatDialog.loading" :analysisData="indexChatData1" :legendChanges="legendChanges1">
            </buschar>
        </el-dialog>    

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import datepicker from '@/views/customerservice/datepicker';
import {  platformlist, pickerOptions } from '@/utils/tools';
import dayjs from 'dayjs';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    GetHotSaleBrandPushNewChoosePageList, GetHotSaleBrandPushNewChooseSearch,GetHotSaleBrandPushNewChooseChartData,GetHotSaleBrandPushNewChooseChartData2
} from '@/api/operatemanage/productalllink/alllink'
import buschar from '@/components/Bus/buschar';
const tableCols = [
    { sortable: 'custom', width: '120', align: 'center', prop: 'choosePlatform', label: '选品平台', formatter: (row) => row.choosePlatformName },
    { sortable: 'custom', width: '120', align: 'center', prop: 'chooseGroupId', label: '选品小组', formatter: (row) => row.chooseGroupName },
    { sortable: 'custom', width: '100', align: 'center', prop: 'chooseUserId', label: '选品运营', formatter: (row) => row.chooseUserName  },
    { sortable: 'custom', width: '200', align: 'center', prop: 'chooseUserRole', label: '职位', },
    { sortable: 'custom', width: '500', align: 'center', prop: 'chooseUserDeptName', label: '架构', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'chooseCount', label: '选品次数',type:'click',handle:(that, row)=>that.JumpChat1(row) ,summaryEvent:true},
    { sortable: 'custom', width: '280', align: 'center', prop: 'chooseMinMaxTimeStr', label: '选品时间', },
    { width: '100', align: 'center', prop: 'mx', label: '选品明细', formatter: (row) => '查看',type:'click',handle:(that, row)=>that.JumpDetail(row)},
];
export default {
    name: "HotSaleBrandPushNewChoose0",
    components: {
        MyContainer, datepicker, vxetablebase,buschar
    },
    data() {
        return {
            that: this,
            auditVisible: false,
            activities: [],
            timeRanges: [],
            platformlist: platformlist,
            pickerOptions,
            filter: {
                chooseDimension:"选品运营",
                timerange: [(dayjs().subtract(1, 'day').format('YYYY-MM') + '-01'), dayjs().subtract(1, 'day').format('YYYY-MM-DD')],
                chooseStartDate: null,
                chooseEndDate: null,
                choosePlatforms:[],
                chooseGroupIds:[],
                chooseUserIds:[],
                chooseUserRoles:[],
                chooseUserDeptNames:[],
            },
            pager: { OrderBy: "chooseCount", IsAsc: false },
            tableCols:tableCols,
            tableData: [],
            total: 0,
            listLoading: false,
            pageLoading: false,
            sels: [],
            selids: [],
            summaryarry: {},

            chooseGroupList: [],
            chooseUserList: [],
            chooseUserRoleList: [],
            chooseUserDeptNameList: [],

            indexChatDialog:{
                title:"",
                visible:false,
                loading:false,
            },
            indexChatData1: {},
            indexChatSelectedLegend1: [],
        }
    },
    async mounted() {
        await this.getSelectData();
        this.onSearch();
    },
    computed: {
    },
    methods: {
        async getSelectData() {
            let ret = await GetHotSaleBrandPushNewChooseSearch({ type: 1 });
            this.chooseGroupList = ret.data;

            let ret2 = await GetHotSaleBrandPushNewChooseSearch({ type: 2 });
            this.chooseUserList = ret2.data;

            let ret3 = await GetHotSaleBrandPushNewChooseSearch({ type: 3 });
            this.chooseUserRoleList = ret3.data;

            let ret4 = await GetHotSaleBrandPushNewChooseSearch({ type: 4 });
            this.chooseUserDeptNameList = ret4.data;
        },
        async onSearch() {
            this.$nextTick(()=>{
                if(this.filter.chooseDimension=="选品运营") {
                    this.$refs.vxetable.$refs.xTable.showColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('chooseGroupId'))
                    this.$refs.vxetable.$refs.xTable.showColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('chooseUserId'))
                    this.$refs.vxetable.$refs.xTable.showColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('chooseUserRole'))
                    this.$refs.vxetable.$refs.xTable.showColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('chooseUserDeptName'))
                }
                else if(this.filter.chooseDimension=="选品小组"){
                    this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('chooseUserId'))
                    this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('chooseUserRole'))
                    this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('chooseUserDeptName'))
                }
                else if(this.filter.chooseDimension=="选品平台"){
                    this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('chooseGroupId'))
                    this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('chooseUserId'))
                    this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('chooseUserRole'))
                    this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('chooseUserDeptName'))
                }
                this.$refs.pager.setPage(1);
            });
            await this.getList();
        },
        getParam() {
            //选品日期
            if (this.filter.timerange && this.filter.timerange.length == 2) {
                this.filter.chooseStartDate = this.filter.timerange[0];
                this.filter.chooseEndDate = this.filter.timerange[1];
            }
            else {
                this.filter.chooseStartDate = null;
                this.filter.chooseEndDate = null;
            }
            let pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            return params;
        },
        async getList() {
            let param = this.getParam();
            this.listLoading = true
            const res = await GetHotSaleBrandPushNewChoosePageList(param)
            this.listLoading = false
            console.log(res);
            if (res?.success) {
                this.tableData = res.data.list;
                this.total = res.data.total;
                // Object.keys(res.data.summary).forEach(f=>{
                //     res.data.summary[f]=res.data.summary[f].toString();
                // });
                this.summaryarry = res.data.summary;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport() {
            let params = this.getParam();
            this.listLoading = true
            const res = await ExportPurchaseOrderNewApprovePageList(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '新品采购单审批_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },

        async JumpDetail(row) {
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBrandPushNewChoose1.vue`,
                title: '选品明细',
                args: {
                    choosePlatform: row.choosePlatform,
                    chooseGroupId: row.chooseGroupId,
                    chooseUserId: row.chooseUserId,
                    timerange: this.filter.timerange,
                },
                height: '650px',
                width: '1000px',
                callOk: this.afterSave
            });
        },
        afterSave() {
           
        },
        async legendChanges1(selected) {
            this.indexChatSelectedLegend1 = selected;
        },
        async JumpChat1(row){
            this.indexChatDialog.visible=true;
            let param = this.getParam();
            param.choosePlatform=row.choosePlatform;
            this.indexChatDialog.title= row.choosePlatformName+"选品次数";
            if(param.chooseDimension=="选品小组"){
                param.chooseGroupId=row.chooseGroupId;
                this.indexChatDialog.title= row.chooseGroupName+"选品次数";
            }
            if(param.chooseDimension=="选品运营"){
                param.chooseUserId=row.chooseUserId;
                this.indexChatDialog.title= row.chooseUserName+"选品次数";
            }

            this.indexChatDialog.loading = true;
            let res= await GetHotSaleBrandPushNewChooseChartData(param);
            this.indexChatDialog.loading=false;
            this.indexChatData1 =  res;
        },
        async onsummaryClick(property){
            this.indexChatDialog.visible=true;
            this.indexChatDialog.title= "平台选品次数按天占比";
            let param = this.getParam();
            this.indexChatDialog.loading = true;
            let res= await GetHotSaleBrandPushNewChooseChartData2(param);
            this.indexChatDialog.loading=false;
            this.indexChatData1 =  res;
        }
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}
::v-deep .el-select__tags-text {
  max-width: 120px;
}
</style>
