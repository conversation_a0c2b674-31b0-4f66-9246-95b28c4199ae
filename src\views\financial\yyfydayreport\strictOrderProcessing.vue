<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="支付开始时间" end-placeholder="支付结束时间" :picker-options="pickerOptions"
          style="width: 220px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.shopName" placeholder="店铺" maxlength="50" clearable class="publicCss" />
        <div style="margin-right: 5px;">
          <inputYunhan ref="mainOrderNumber" :inputt.sync="ListInfo.mainOrderNumber" v-model="ListInfo.mainOrderNumber"
            width="150px" placeholder="主订单编号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="50"
            :maxlength="1000" @callback="callbackMainOrderNumber" title="主订单编号">
          </inputYunhan>
        </div>
        <el-input v-model.trim="ListInfo.taobaoProductId" placeholder="淘宝商品ID" maxlength="50" clearable
          class="publicCss" />
        <el-input v-model.trim="ListInfo.skuMerchantCode" placeholder="SKU商家编码" maxlength="50" clearable
          class="publicCss" />
        <el-input v-model.trim="ListInfo.paymentStatus" placeholder="支付状态" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.refundType" placeholder="退款类型" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'strictOrderProcessing20211231112'" :tablekey="'strictOrderProcessing20211231112'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'" :cstmExportFunc="onExport">
      <template slot="right">
        <vxe-column title="操作" width="120">
          <template #default="{ row, $index }">
            <div style="display: flex">
              <el-button type="text" style="color: red;" @click="deletionMethod(row)">删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="35%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 100px;">
        <el-date-picker style="width: 190px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay" type="date"
          placeholder="选择日期" :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
        </el-date-picker>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action=""
          accept=".xlsx" :file-list="fileList" :http-request="onUploadFile" :on-success="onUploadSuccess"
          :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary" style="width: 90px;">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px;width: 90px;" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import inputYunhan from "@/components/Comm/inputYunhan";
import { importYanxuanOrdersAsync, getYanxuanOrdersList, deleteYanxuanOrdersAsync, exportanxuanOrders } from '@/api/bookkeeper/reportdayV2'
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'yearMonthDayDate', label: '支付时间', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'mainOrderNumber', label: '主订单编号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'subOrderNumber', label: '子订单编号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'taobaoProductId', label: '淘宝商品ID', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'skuMerchantCode', label: 'sku商家编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderSettlementAmount', label: '订单结算金额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'logisticsStatus', label: '物流状态', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'paymentStatus', label: '支付状态', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderQuantity', label: '下单数量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'refundOrderNumber', label: '退款单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'refundType', label: '退款类型', },
]
export default {
  name: "strictOrderProcessing",
  components: {
    MyContainer, vxetablebase, inputYunhan
  },
  data() {
    return {
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      yearMonthDay: null,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startUseDate: null,//开始时间
        endUseDate: null,//结束时间
        shopName: null,//店铺
        mainOrderNumber: null,//主订单编号
        taobaoProductId: null,//淘宝商品ID
        skuMerchantCode: null,//sku商家编码
        paymentStatus: null,//支付状态
        refundType: null,//退款类型
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    callbackMainOrderNumber(val) {
      this.ListInfo.mainOrderNumber = val
    },
    deletionMethod(row) {
      this.$confirm('是否删除该条数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await deleteYanxuanOrdersAsync({ yearMonthDayDate: row.yearMonthDayDate })
        this.getList()
      }).catch(() => {
      });
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("yearMonthDay", this.yearMonthDay);
      var res = await importYanxuanOrdersAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (!this.yearMonthDay) {
        this.$message({ message: "请选择日期", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async changeTime(e) {
      this.ListInfo.startUseDate = e ? e[0] : null
      this.ListInfo.endUseDate = e ? e[1] : null
    },
    async exportProps() {
      this.$nextTick(() => {
        this.$refs.table.setExportCols()
      })
    },
    async onExport(opt) {
      const params = { ...this.ListInfo, ...opt };
      this.loading = true
      let res = await exportanxuanOrders(params);
      this.loading = false
      if (!res?.data) return
      return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '严选订单处理数据' + new Date().toLocaleString() + '_.xlsx')
      aLink.click()
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startUseDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.endUseDate = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startUseDate, this.ListInfo.endUseDate]
      }
      this.loading = true
      const { data, success } = await getYanxuanOrdersList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
