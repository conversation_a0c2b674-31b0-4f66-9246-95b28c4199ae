<template>
  <MyContainer>
    <el-tabs v-model="current" style="height: 95%;">
      <el-tab-pane label="尾程费用汇总" name="tab1" :lazy="true" style="height: 98%;">
        <LastMileCostSummary type="尾程费用汇总" ref="" name="尾程费用汇总" />
      </el-tab-pane>
      <el-tab-pane label="财务类型维护" name="tab11" :lazy="true" style="height: 98%;">
        <FinancialTypeMaintain name="财务类型维护" :financeFeeNameType="financeFeeNameType"  @getfinanceTypeList="getfinanceTypeList"/>
      </el-tab-pane>
      <el-tab-pane label="佳速达尾程费用" name="tab2" style="height: 98%;" >
        <CostItemShipOut :type="'ShipOut'" name="佳速达尾程费用" :financeFeeNameType="financeFeeNameType"/>
      </el-tab-pane>
      <el-tab-pane label="佳速达尾程费用(同步)" name="tab9" style="height: 98%;" v-if="checkPermission('shipOut_cost_sync')">
        <CostItemShipOutCopy :type="'ShipOut'" name="佳速达尾程费用" :financeFeeNameType="financeFeeNameType"/>
      </el-tab-pane>
      <el-tab-pane label="九方尾程费用" name="tab3" :lazy="true" style="height: 98%;">
        <CostItemTofba :type="'Tofba'" name="九方尾程费用" :financeFeeNameType="financeFeeNameType"/>
      </el-tab-pane>
      <el-tab-pane label="九方尾程费用(同步)" name="tab10" :lazy="true" style="height: 98%;" v-if="checkPermission('tofba_cost_sync')">
        <CostItemTofbaCopy :type="'Tofba'" name="九方尾程费用" :financeFeeNameType="financeFeeNameType"/>
      </el-tab-pane>
      <el-tab-pane label="亚吉达尾程费用" name="tab4" :lazy="true" style="height: 98%;">
        <CostItemElt :type="'Elt'" name="亚吉达尾程费用" :financeFeeNameType="financeFeeNameType"/>
      </el-tab-pane>
      <el-tab-pane label="赤道尾程费用" name="tab5" :lazy="true" style="height: 98%;">
        <CostItemSogoodseller :type="'Sogoodseller'" name="赤道尾程费用" :financeFeeNameType="financeFeeNameType"/>
      </el-tab-pane>
      <el-tab-pane label="左海尾程费用" name="tab6" :lazy="true" style="height: 98%;">
        <CostItemZuoHai :type="'ZuoHai'" name="左海尾程费用" :financeFeeNameType="financeFeeNameType"/>
      </el-tab-pane>
      <el-tab-pane label="环世尾程费用" name="tab8" :lazy="true" style="height: 98%;">
        <CostItemWwlcargo :type="'Wwlcargo'" name="环世尾程费用" :financeFeeNameType="financeFeeNameType"/>
      </el-tab-pane>
      <!-- <el-tab-pane label="头程费用-九方" name="tab7" :lazy="true" style="height: 98%;">
        <InitialCost :type="'Tofba'" name="头程费用-九方" />
      </el-tab-pane> -->
    </el-tabs>
  </MyContainer> 
</template>
<script>
import MyContainer from "@/components/my-container";
import CostItemShipOut from './tab/CostItemShipOut.vue'
import CostItemTofba from './tab/CostItemTofba.vue'
import CostItemElt from './tab/CostItemElt.vue'
import CostItemSogoodseller from './tab/CostItemSogoodseller.vue'
// import InitialCost from './tab/InitialCost.vue'
import CostItemZuoHai from './tab/CostItemZuoHai.vue'
import LastMileCostSummary from './tab/LastMileCostSummary.vue'
import CostItemWwlcargo from './tab/CostItemWwlcargo.vue'
import FinancialTypeMaintain from './tab/FinancialTypeMaintain.vue'
import CostItemShipOutCopy from './tab/CostItemShipOutCopy.vue'
import CostItemTofbaCopy from './tab/CostItemTofbaCopy.vue'
import {  GetFinanceFeeTypes } from '@/api/kj/cost.js';

export default {
  components: {
    MyContainer, CostItemShipOut, CostItemTofba, CostItemElt, CostItemSogoodseller, CostItemZuoHai, LastMileCostSummary,CostItemWwlcargo,FinancialTypeMaintain,CostItemShipOutCopy,CostItemTofbaCopy,
    // InitialCost
  },
  data() {
    return {
      current: 'tab1',
      financeFeeNameType:[]
    };
  },
  async mounted() {
    await this.getfinanceTypeList()
  },
  methods: {
    async getfinanceTypeList() {
      const { data } = await GetFinanceFeeTypes()
      this.financeFeeNameType = data;

    },
  }
};
</script>

<style lang="scss" scoped></style>
