<template>
  <container>
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
      :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true'
      :summaryarry='summaryarry' :loading="listLoading">
      <template slot='extentbtn'>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;"><el-input style="width: 100px" v-model="filter1.proCode"  placeholder="产品ID"/></el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-select filterable v-model="filter1.platform" placeholder="平台" clearable style="width: 80px">
              <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;margin: 0;"><el-input style="width: 80px" v-model="filter1.importor" clearable placeholder="导入人"/></el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-date-picker style="width: 210px" v-model="filter1.timeimportrange" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
              start-placeholder="开始导入时间" end-placeholder="结束导入时间" :clearable="true" class="date_picker"></el-date-picker>
          </el-button>
          <el-button type="primary" @click="onSearch">刷新</el-button>
          <el-button type="primary" @click="onExport">导出</el-button>
        </el-button-group>
      </template>
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>
  </container>
</template>
<script>
import { pageDaySpecialOrder,deleteSpecialOrderBatch ,exportDaySpecialOrder} from '@/api/financial/daycost'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue"
import { platformlist} from '@/utils/tools'
import {formatFeeShareOper,formatTime,formatYesornoBool,companylist} from "@/utils/tools";
const tableCols = [
  { istrue: true, prop: 'yearMonthDay', label: '付款日期', width: '100', sortable: 'custom',formatter:(row)=>formatTime(row.yearMonthDay,'YYYY-MM-DD')},
  { istrue: true, prop: 'orderNo', label: '订单号', width: '110', sortable: 'custom' },
  { istrue: true, prop: 'proCode', label: '产品ID', width: '110', sortable: 'custom' },
  { istrue:true,prop:'platform',label:'平台', width:'65',sortable:'custom',formatter:(row)=> row.enmPlatform},
  { istrue: true, prop: 'amont', label: '佣金', width: '65', sortable: 'custom', },
  { istrue:true,prop:'createdUserId',label:'导入人', width:'70',sortable:'custom',formatter:(row)=>row.createdUserName},
  { istrue:true,prop:'createdTime',label:'导入时间', width:'auto',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD HH:mm:ss')},
  {istrue:true,prop:'batchNumber',label:'导入批次号', width:'200',sortable:'custom'},
  {istrue: true,type: "button",label:'操作',width: "430",btnList: [{ label: "删除批次", handle: (that, row) => that.deleteBatch(row)}]}
];
const tableHandles = [
  { label: "导入", handle: (that) => that.onimport() },
  { label: "下载导入模板", handle: (that) => that.ondownloadmb('特殊单导入模板') },
];
export default {
  name: 'YunHanspecialorder',
  components: { cesTable, container },
  props: {
    filter: {}
  },
  data() {
    return {
      filter1: {
          proCode:null,
          platform:null,
          importor:null,
          startImportTime:null,
          endImportTime:null,
          timeimportrange:null,
        },
      platformlist:platformlist,
      shareFeeType: 7, 
      that: this,
      list: [],
      tableCols: tableCols,
      tableHandles: tableHandles,
      pager: { OrderBy: "id", IsAsc: false },
      summaryarry: {},
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      onExporting:false,
    }
  },
  mounted() {

  },
  methods: {
    async deleteBatch(row){
        var that=this;
        this.$confirm("此操作将删除此批次导入费用数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async() => {
        await deleteSpecialOrderBatch({batchNumber:row.batchNumber})
        that.$message({message: '已删除', type: "success"});
        that.onSearch()

          });

      },
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      if (this.filter1.timeimportrange) {
          this.filter1.startImportTime = this.filter1.timeimportrange[0];
          this.filter1.endImportTime = this.filter1.timeimportrange[1];
      }
      let pager = this.$refs.pager.getPager()
      const params = {...pager, ...this.pager, ... this.filter, ... this.filter1}
      this.listLoading = true
      const res = await pageDaySpecialOrder(params)
      this.listLoading = false
      if (!res?.success) return
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry = res.data.summary; 
    },
    async onExport() {
     if (this.onExporting) return;
     try{
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      if (this.filter1.timeimportrange) {
          this.filter1.startImportTime = this.filter1.timeimportrange[0];
          this.filter1.endImportTime = this.filter1.timeimportrange[1];
      }
      const params = { ... this.filter, ... this.filter1}
      this.listLoading = true
      const res = await exportDaySpecialOrder(params)
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','特殊单_' + new Date().toLocaleString() + '.xlsx' )
        this.uploadLoading = false;

        aLink.click()
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
      this.onExporting=false;
     },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    async onbatchDelete() {
      await this.$emit('ondeleteByBatch', this.shareFeeType);
    },
    async oncomput() {
      this.$emit('onstartcomput', this.shareFeeType);
    },
    async onimport() {
      await this.$emit('onstartImport', this.shareFeeType);
    },
    async ondownloadmb(name) {
      await this.$emit('ondownloadmb', name);
    },
  }
}
</script>
  