<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="拼多多账单费用" v-if="checkPermission('BillingFeesPermissionsPdd')"
            name="tab1" style="height: 100%;">
            <PddBill :filter="filter" ref="PddBill" style="height: 100%;" />
          </el-tab-pane>
            <el-tab-pane label="淘系/工厂账单费用" v-if="checkPermission('BillingFeesPermissionsTx')" :lazy="true"
                name="tab2" style="height: 100%;">
                <TXBill :filter="filter" ref="TXBill" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="阿里巴巴账单费用" v-if="checkPermission('BillingFeesPermissionsAlbb')" :lazy="true"
            name="tab3" style="height: 100%;">
            <AlibabaBill :filter="filter" ref="AlibabaBill" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="抖音账单费用" v-if="checkPermission('BillingFeesPermissionsDy')" :lazy="true"
            name="tab4" style="height: 100%;">
            <DYBill :filter="filter" ref="DYBill" style="height: 100%;" />
            </el-tab-pane>

            <el-tab-pane label="京东账单费用" v-if="checkPermission('BillingFeesPermissionsJd')" :lazy="true"
            name="tab6" style="height: 100%;">
            <JDBill :filter="filter" ref="JDBill" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="视频号账单费用" v-if="checkPermission('BillingFeesPermissionsSph')" :lazy="true"
            name="tab20" style="height: 100%;">
            <videoBill :filter="filter" ref="videoBill" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="快手账单费用" v-if="checkPermission('BillingFeesPermissionsKs')" :lazy="true"
            name="tab12" style="height: 100%;">
            <KWaiShopBill :filter="filter" ref="KWaiShopBill" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="小红书费用" v-if="checkPermission('BillingFeesPermissionsXhs')" :lazy="true"
                name="tab5" style="height: 100%;">
                <redBookBill :filter="filter" ref="DYredBookBill" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="日报账单费用汇总" v-if="checkPermission('BillingFeesPermissionsDailySummary')" :lazy="true"
              name="tab16" style="height: 100%;">
              <billingChargesSummaryNew ref="billingChargesSummaryNew" style="height: 100%;" />
            </el-tab-pane>
            <!-- <el-tab-pane label="店铺资金账单费用汇总" v-if="checkPermission('BillingFeesPermissionsStoreFundsSummary')" :lazy="true"
            name="tab9" style="height: 100%;">
            <BillingSummary :filter="filter" ref="BillingSummary" style="height: 100%;" />
            </el-tab-pane> -->
            <el-tab-pane label="未处理账单项目" v-if="checkPermission('BillingFeesPermissionsNotProcessed')" :lazy="true"
              name="tab17" style="height: 100%;">
              <billingChargesUnClacNew ref="billingChargesUnClacNew" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="拼多多日账单明细" v-if="checkPermission('BillingFeesPermissionsDailyBillsPdd')" :lazy="true"
            name="tab15" style="height: 100%;">
            <PddDayBillDetail :filter="filter" ref="PddDayBillDetail" style="height: 100%;" />
            </el-tab-pane>
        </el-tabs>
    </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";
import PddBill from './PddBill.vue'
import DYBill from './DyBillIndex.vue'
import TXBill from './TxBillIndex.vue'
import AlibabaBill from './AlibabaBill.vue'
import JDBill from './JDBillIndex.vue'
import BillingSummary from './BillingSummary.vue'
import KWaiShopBill from './KWaiShopBillIndex.vue'

import PddDayBillDetail from './PddDayBillDetail.vue'
import billingChargesUnClacNew from './billingChargesUnClacNew.vue'
import billingChargesSummaryNew from './billingChargesSummaryNew.vue'
import videoBill from '@/views/bookkeeper/details/BillV2/videoBillFile/videoBillIndex.vue'
import redBookBill from '@/views/bookkeeper/reportday/littleRedBookFees/redBookExpensesIndex.vue'

export default {
    name: "Users",
    components: { MyContainer,PddBill,DYBill,TXBill,AlibabaBill,JDBill,BillingSummary, KWaiShopBill,
          PddDayBillDetail,billingChargesUnClacNew, billingChargesSummaryNew, videoBill,redBookBill },
    data() {
        return {
            that: this,
            filter: {
        },
            shopList: [],
            pageLoading: false,
            userList: [],
            selids: [],
            activeName:'tab1'
        };
    },
    mounted() {
      window.showtab4=this.showtab
    },
    methods: {
        showtab(){
          this.activeName=""
      }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
