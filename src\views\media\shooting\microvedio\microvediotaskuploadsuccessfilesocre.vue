<template> 
 <my-container v-loading="pageLoading">
        <el-row>
            <el-col :span="14"  >
                &nbsp;
            </el-col>
            <el-col :span="10"   >
                <el-row style ="height: 160px;"> </el-row> 
              <!--   <el-row style ="height: 60px; width: 390px; text-align: center;" >
                     <el-button type="primary" style="width: 200px;height: 40px;" @click="tomsgshow">效果预览</el-button> 
                </el-row> -->

                <el-row style ="height: 80px;  width: 390px;text-align: center;"  > 
                     <el-button type="primary" style="width: 200px;height: 40px;" @click="showOutComInfo">查看成果</el-button> 
                </el-row> 

                <el-row style ="height: 80px;  width: 390px;text-align: center;"   > 
                     <el-button type="primary" style="width: 200px;height: 40px;" @click="onPackagingCompression">开始打包</el-button> 
                </el-row> 
                <el-row  v-if="isPackOver"  style ="height: 100px;  width: 390px;text-align: center;" > 
                     <el-button type="primary" style="width: 200px;height: 40px;"  @click="downPackagingCompression">下载文件</el-button><br/> 
                     <span>上次包时间：{{packTime}}</span>    
                </el-row> 
                <el-row style ="height: 40px;">  </el-row>  
                <el-row v-if="isOverList"> 
                
                    <el-row style ="height: 40px;  width: 400px;  " v-if="hasphoto==1" >  
                         <el-col :span="10" style =" text-align: right;"  > 拍摄照片评分： </el-col>
                         <el-col :span="12"> <el-rate v-model="value1" allow-half="true"   show-score  ></el-rate></el-col> 
                    </el-row> 
                    <el-row style ="height: 40px; width: 400px; align:right;" v-if="hasvedio==1">  
                         <el-col :span="10" style =" text-align: right;">主图视频评分：</el-col>
                         <el-col :span="12"> <el-rate v-model="value3" allow-half="true"   show-score ></el-rate></el-col> 
                    </el-row>
                    <el-row style ="height: 40px;  width: 400px;  align:right;" v-if="hasmicroDetail==1">  
                         <el-col :span="10" style =" text-align: right;"> 微详情评分：</el-col>
                         <el-col :span="12"> <el-rate v-model="value4" allow-half="true"   show-score ></el-rate></el-col> 
                    </el-row>
                    <el-row style ="height: 40px;  width: 400px;  align:right;" v-if="hasdetail==1">  
                         <el-col :span="10" style =" text-align: right;"> 详情页评分：</el-col>
                         <el-col :span="12"> <el-rate v-model="value5" allow-half="true"    show-score  ></el-rate></el-col> 
                    </el-row>
                    <el-row style ="height: 40px;  width: 400px;   align:right;" v-if="hasmodelPhoto==1">  
                         <el-col :span="10" style =" text-align: right;"> 建模照片评分： </el-col>
                         <el-col :span="12"> <el-rate v-model="value2" allow-half="true"   show-score ></el-rate></el-col> 
                    </el-row>
                    <el-row style ="height: 40px;  width: 400px;   align:right;" v-if="hasmodelVideo==1">  
                         <el-col :span="10" style =" text-align: right;"> 建模视频评分： </el-col>
                         <el-col :span="12"> <el-rate v-model="value6" allow-half="true"   show-score  ></el-rate></el-col> 
                    </el-row>
               </el-row> 
                <el-row style ="height: 40px;">  </el-row> 
            </el-col> 
        </el-row> 
        <!--c成果信息查看-->
        <el-drawer title="查看成果"   :visible.sync="OutComInfodrawer" direction="ltr" 
               :wrapperClosable="true"  :close-on-press-escape ="false"
               size="30%"   :append-to-body="true">  
             <shootingoutcomfilelook :rowinfo="rowinfo" :timee = "Date.now()"/>
        </el-drawer>
    </my-container>
</template>
<script>
import "@/router/index";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import shootingoutcomfilelook from '@/views/media/shooting/microvedio/microvediooutcomfilelook'
import { subSuccessAttachmentScore,getUploadSuccessAttachmentScore,getPackagingCompressionTaskStatus,packagingCompressionTask } from '@/api/media/microvedio';
import directImgViewshow from '@/views/media/shooting/microvedio/microvedioViewshow';
export default {
     components: { MyContainer,MyConfirmButton,shootingoutcomfilelook,directImgViewshow},
     props:{
     rowinfo:{ type: Number, default:0}, 
     clostfun:{ type: Function, default: null  },
     islook:{ type: Boolean, default:false },
     isOverList:{ type: Boolean, default:false },
     },
     data() {
          return {
               value1:0,
               value2:0,
               value3:0,
               value4:0,
               value5:0,
               value6:0,

               hasphoto:0,
               hasvedio:0,
               hasmicroDetail:0,
               hasdetail:0,
               hasmodelPhoto:0,
               hasmodelVideo:0,
               OutComInfodrawer:false,
               isPackOver:false,
               downUrl:null,
               packTime:null,
               pageLoading:false,  
               ////////////////////////////
               radioselcet:"链接套图"
          };
     },

     async mounted() { 
          this.getSubScoreInfo();
     }, 
     methods: {
          //打开成果界面
          async showOutComInfo(){
               this.OutComInfodrawer=true;
          },
          //获取评分
          async getSubScoreInfo(){
            this.pageLoading=true;
            var res =await getUploadSuccessAttachmentScore({microVedioTaskId:this.rowinfo});
            if(res?.success){ 
                this.value1=res?.data?.photosScore;
                this.value2=res?.data?.modelPhotosScore;
                this.value3=res?.data?.mainVideoScore;
                this.value4=res?.data?.microVideoScore;
                this.value5=res?.data?.detailPageScore;
                this.value6=res?.data?.modelVedioScore;
               if(res?.data?.shootingTaskPick.indexOf("1")<0){
                    this.hasphoto = 0;
               }else{
                    this.hasphoto = 1;
               }

               if(res?.data?.shootingTaskPick.indexOf("2")<0){
                    this.hasvedio = 0;
               }else{
                    this.hasvedio = 1;
               }

               if(res?.data?.shootingTaskPick.indexOf("3")<0){
                    this.hasmicroDetail = 0;
               }else{ 
                    this. hasmicroDetail = 1;
               }
               if(res?.data?.shootingTaskPick.indexOf("4")<0){
                    this.hasdetail = 0;
               }else{
                    this.hasdetail = 1;
               }
               if(res?.data?.shootingTaskPick.indexOf("5")<0){
                    this.hasmodelPhoto = 0;
               }else{
                    this.hasmodelPhoto = 1;
               } 
               if(res?.data?.shootingTaskPick.indexOf("6")<0){
                    this.hasmodelVideo = 0;
               }else{
                    this.hasmodelVideo = 1;
               } 
            }
            res = await getPackagingCompressionTaskStatus({microVedioTaskId:this.rowinfo});
            if(res?.success)
            {  
               if(res.data !=null)
               {
                    this.isPackOver =true;
                    this.packTime= res.data.createdTime;
                    this.downUrl = res.data.pathOrErr;
               }
            }
            this.pageLoading=false;
        },
        //提交保存
        async onSubComputOutInfo(){
            this.pageLoading=true;
            var res =await subSuccessAttachmentScore(
                {microVedioTaskId:this.rowinfo
                ,PhotosScore:this.value1
                ,ModelPhotosScore:this.value2
                ,MainVideoScore:this.value3
                ,MicroVideoScore:this.value4
                ,DetailPageScore:this.value5
                ,modelVedioScore:this.value6 
                });
            if(res?.success){
                this.$message({ message: '操作成功', type: "success" });
            }
            this.pageLoading=false;
        },
        async onPackagingCompression()
        {
          this.$confirm('本次打包将会,覆盖上次的打包文件！是否执行打包操作')
                .then(  async() =>   
                {
                    this.pageLoading=true;
                    var res = await packagingCompressionTask({microVedioTaskId:this.rowinfo});
                         if(res?.success){
                              this.$message({ message: res.data, type: "success" });
                         }
                         this.isPackOver = false;
                    this.pageLoading =false;
               }).catch(async()=> { 
                    this.pageLoading =false;
               });
        
        },
        //
        downPackagingCompression(){
          const domain = "/api/media/statics/"  + this.downUrl;
          window.open(domain,"_blank")  
        },
        tomsgshow(){
          let routeUrl = this.$router.resolve({
               path: '/shooting/directImgView',
               query: {id:this.rowinfo}
          });
          window.open(routeUrl.href, '_blank');
        }

   },
};
</script>
<style lang="scss" scoped>
 .rowclass {
    width: 500px;
    height: 60px;
    margin-bottom: 20px;
 }
</style>
