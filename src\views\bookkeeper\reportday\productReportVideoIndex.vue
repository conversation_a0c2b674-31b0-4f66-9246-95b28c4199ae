<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="视频号日报" name="first1" style="height: 100%">
        <productReportVideo ref="productReportVideo" style="height: 100%"></productReportVideo>
      </el-tab-pane>
      <el-tab-pane label="订单日报" name="first2" :lazy="true" style="height: 100%"
        v-if="checkPermission('VideoOrderDayReport')">
        <VideoOrderDayReport @ChangeActiveName2="ChangeActiveName2" :orderNoInner="orderNoInner"
          ref="VideoOrderDayReport" style="height: 100%"></VideoOrderDayReport>
      </el-tab-pane>
      <el-tab-pane label="编码日报" name="first3" :lazy="true" style="height: 100%"
        v-if="checkPermission('VideoGoodCodeDayReport')">
        <VideoGoodCodeDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="VideoGoodCodeDayReport" style="height: 100%"></VideoGoodCodeDayReport>
      </el-tab-pane>
      <el-tab-pane label="ID日报" name="first4" :lazy="true" style="height: 100%"
        v-if="checkPermission('VideoIdDayReport')">
        <VideoIdDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="VideoIdDayReport" style="height: 100%"></VideoIdDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺日报" name="first5" :lazy="true" style="height: 100%"
        v-if="checkPermission('VideoShopDayReport')">
        <VideoShopDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="VideoShopDayReport" style="height: 100%"></VideoShopDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺SKU日报" name="first7" :lazy="true" style="height: 100%"
        v-if="checkPermission('VideoCommodityDayReport')">
        <VideoCommodityDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="VideoCommodityDayReport" style="height: 100%"></VideoCommodityDayReport>
      </el-tab-pane>
      <el-tab-pane label="明细日报" name="first6" :lazy="true" style="height: 100%"
        v-if="checkPermission('VideoDetailDayReport')">
        <VideoDetailDayReport @ChangeActiveName="ChangeActiveName" @ChangeActiveName2="ChangeActiveName2"
          ref="VideoDetailDayReport" style="height: 100%"></VideoDetailDayReport>
      </el-tab-pane>
      <el-tab-pane label="出仓负利润ID订单明细" name="first8" :lazy="true" style="height: 100%"
        v-if="checkPermission('VideoOutgoingprofitIDorderdetail')">
        <VideoOutgoingprofitIDorderdetail @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="VideoOutgoingprofitIDorderdetail" style="height: 100%"></VideoOutgoingprofitIDorderdetail>
      </el-tab-pane>
      <el-tab-pane label="SKUS日报" name="first9" :lazy="true" style="height: 100%"
        v-if="checkPermission('VideoOrderDayReport')">
        <VideoSkusDayReport @ChangeActiveName2="ChangeActiveName2" :orderNoInner="orderNoInner" ref="VideoSkusDayReport"
          style="height: 100%"></VideoSkusDayReport>
      </el-tab-pane>
      <el-tab-pane label="商务日报" name="first10" style="height: 100%;" lazy v-if="checkPermission('VideoDetailDayReport')">
        <BusinessDaily/>
      </el-tab-pane>
      <el-tab-pane label="商务达人" name="first11" style="height: 100%;" lazy v-if="checkPermission('VideoDetailDayReport')">
        <BusinessExpert/>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import productReportVideo from "./productReportVideo.vue";
import VideoOrderDayReport from "./VideoOrderDayReport.vue";
import VideoSkusDayReport from "./VideoSkusDayReport.vue";
import VideoGoodCodeDayReport from "./VideoGoodCodeDayReport.vue";
import VideoIdDayReport from "./VideoIdDayReport.vue";
import VideoCommodityDayReport from "./VideoCommodityDayReport.vue";
import VideoOutgoingprofitIDorderdetail from "./VideoOutgoingprofitIDorderdetail.vue";
import VideoShopDayReport from "./VideoShopDayReport.vue";
import VideoDetailDayReport from "./VideoDetailDayReport.vue";
import BusinessDaily from "./BusinessDaily.vue"
import BusinessExpert from "./BusinessExpert.vue"
export default {
  name: "productReportVideoIndex",
  components: {
    MyContainer, productReportVideo, VideoOrderDayReport, VideoSkusDayReport, VideoGoodCodeDayReport, VideoIdDayReport, VideoShopDayReport, 
    VideoDetailDayReport, VideoCommodityDayReport, VideoOutgoingprofitIDorderdetail, BusinessDaily, BusinessExpert
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },
  async mounted() {
    middlevue.$on('toLinkTxDetailDayReport', (data) => {
      if (data.isTrue && data.plat == 'tm') {
        this.activeName = 'first6';
        setTimeout(() => {
          middlevue.$emit('toLinkTxDetailDayReportQuery', data)
        }, 500);

      } else {
        this.activeName = 'first8';
        setTimeout(() => {
          middlevue.$emit('toLinkVideoOutgoingprofitIDorderdetailQuery', data)
        }, 500);
      }
    })
  },
  //销毁事件
  beforeDestroy() {
    middlevue.$off('toLinkTxDetailDayReport')
  },
  methods: {
    ChangeActiveName(activeName) {
      this.activeName = 'first2';
      this.$refs.VideoOrderDayReport.VideoGoodCodeDayReportArgument(activeName)
    },
    ChangeActiveName2(activeName, No, Time) {
      this.activeName = 'first6';
      this.$refs.VideoDetailDayReport.VideoDetailDayReportArgument(activeName, No, Time)
    }
  },
};
</script>

<style lang="scss" scoped></style>
