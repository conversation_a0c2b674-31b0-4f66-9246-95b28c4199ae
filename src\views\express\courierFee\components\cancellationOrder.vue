<template>
  <MyContainer>
    <template #header>
      <div >
        <!-- <el-date-picker v-model="ListInfo.yearmonth" type="month" :clearable="false" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"  ></el-date-picker> -->
        <el-button style="padding: 0;margin: 0;width: 250px; border: none;">
          <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="导入开始日期" end-placeholder="导入结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime" :clearable="false">
        </el-date-picker>
      </el-button>
      <el-button style="padding: 0;margin: 0;width: 500px; border: none;">
          <queryCondition ref="refqueryCondition" :valueChanged.sync="topfilter" />
        </el-button>
        <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
            <el-input v-model.trim="ListInfo.batchNumber" placeholder="批次号" maxlength="50" clearable class="" />
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
            <inputYunhan ref="productmailNumber" :inputt.sync="ListInfo.mailNumberList" v-model="ListInfo.mailNumberList" width="160px"
              placeholder="邮件号(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
              :maxlength="21000" @callback="mailNumberCallback" title="邮件号">
            </inputYunhan>
            </el-button>
            <el-button style="padding: 0;margin: 0;border: none;" >
            <el-select filterable v-model="ListInfo.platform" placeholder="请选择平台" multiple collapse-tags
              clearable style="width: 160px">
              <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 200px; border: none;">
                <!-- <el-input v-model.trim="ListInfo.originalOnlineOrderNo" placeholder="原始线上订单号" maxlength="50" clearable class="" /> -->
                <div  class="publicCss" >
          <inputYunhan ref="productCode" :inputt.sync="ListInfo.originalOnlineOrderNo" v-model="ListInfo.originalOnlineOrderNo" width="200px"
              placeholder="原始线上订单号(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
              :maxlength="21000" @callback="productCodeCallback" title="原始线上订单号">
            </inputYunhan>
         </div>
          </el-button>

          <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
                  <!-- <el-input v-model.trim="ListInfo.orderNo" placeholder="内部单号" maxlength="50" clearable class="" /> -->
                  <inputYunhan ref="productCode" :inputt.sync="ListInfo.orderNo" v-model="ListInfo.orderNo" width="200px"
              placeholder="内部单号(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
              :maxlength="21000" @callback="productCodeCallback2" title="内部单号">
            </inputYunhan>
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
                    <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="" />
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
                      <el-input v-model.trim="ListInfo.labels" placeholder="标签" maxlength="50" clearable class="" />
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
                        <el-input v-model.trim="ListInfo.status" placeholder="状态" maxlength="50" clearable class="" />
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
            <el-input v-model.trim="ListInfo.shopName" placeholder="店铺名称" maxlength="50" clearable class="" />
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 55px; border: none;">
            <el-checkbox v-model="ListInfo.noUseCatch">非缓存</el-checkbox>
          </el-button>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'cancellationOrder202410131100'" :tablekey="'cancellationOrder202410131100'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatTime ,formatWarehouseNew,platformlist} from "@/utils/tools";
import { getExpressInfoData_Month, exportExpressInfoData_Month } from "@/api/express/express";
import dayjs from 'dayjs'
import queryCondition from "../../dailyCourierFee/components/queryCondition.vue";

import inputYunhan from "@/components/Comm/inputYunhan";

const tableCols = [
{ sortable: 'custom', width: '80', align: 'center', prop: 'inportDate', label: '导入日期', formatter: (row) => formatTime(row.inportDate,"YYYY-MM-DD") },
{ sortable: 'custom', width: '80', align: 'center', prop: 'expressCompanyName', label: '快递公司', },
{ sortable: 'custom', width: '80', align: 'center', prop: 'prosimstateId', label: '快递站点',  formatter: (row) => row.prosimstate, type: 'custom' },
{ sortable: 'custom', width: '80', align: 'center', prop: 'warehouseId', label: '发货仓库', formatter: (row) => formatWarehouseNew(row.warehouseId)  },
  { sortable: 'custom', width: '110', align: 'center', prop: 'batchNumber', label: '批次号', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'mailNumber', label: '邮件号', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'jsCount', label: '计数', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'additionalWeightFee', label: '续重费', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'waybill', label: '面单', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'totalFreight', label: '运费合计', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'province', label: '省份', },
  { sortable: 'custom', width: '160', align: 'center', prop: 'originalOnlineOrderNo', label: '原始线上订单号 ', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'orderNo', label: '内部单号',  formatter: (row) => row.orderNo ? row.orderNo : '', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'platform', label: '平台',  formatter: (row) => row.platformStr  },
  { sortable: 'custom', width: '110', align: 'center', prop: 'shopName1', label: '店铺名称1', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'shopName', label: '店铺名称', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'goodsName', label: '商品名称', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'goodsWeight', label: '商品重量', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'orderStatus', label: '订单状态', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'shopStatus', label: '店铺状态', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'customerPayment', label: '客户实付', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'label', label: '标签', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'quantity', label: '数量', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'billingWeight', label: '快递重量', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'auditAmount', label: 'ERP核算运费', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'remark', label: '备注留言',  formatter: (row) => row.remark+row.buyer_message },
]
export default {
  name: "cancellationOrder",
  components: {
    MyContainer, vxetablebase,queryCondition,inputYunhan
  },
  data() {
    return {
      topfilter: {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
      },
      that: this,
      platformlist: platformlist,
      timeRanges: [],
      ListInfo: {
        DataType:5,
        yearmonth:null,
        startTime: null,//开始时间
        endTime: null,//结束时间
        currentPage: 1,
        pageSize: 50,
        orderBy: 'inportDate',
        isAsc: false,
        mailNumberList: null,
        noUseCatch: false,//是否使用缓存
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      formatWarehouseNew:formatWarehouseNew,

    }
  },
  async mounted() {
    this.ListInfo.yearmonth= formatTime(new Date(),'YYYYMM');
    if (this.timeRanges && this.timeRanges.length == 0) {
      //默认给当前月第一天至今天
      this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
      this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
      this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
    }
    // await this.getList()
    await this.init()

  },
  methods: {
    async init() {
      this.$nextTick(() => {
        this.$refs.refqueryCondition.init()
      })
      const res = await getExpressComanyAll({});
      if (!res?.success) return
      this.expresscompanylist = res.data;
    },
    productCodeCallback(val) {
      this.ListInfo.originalOnlineOrderNo = val;
    },
    productCodeCallback2(val) {
      this.ListInfo.orderNo = val;
    },
    mailNumberCallback(val) {
      this.ListInfo.mailNumberList = val;
    },
    //导出
    async exportProps() {
      this.loading = true
      const res = await exportExpressInfoData_Month({...this.ListInfo,...this.topfilter})
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', 'ERP导出月账单数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getExpressInfoData_Month({...this.ListInfo,...this.topfilter})

      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary

        let summary = data.summary || {}

const resultsum = {};
Object.entries(summary).forEach(([key, value]) => {
    resultsum[key] = formatNumber(value);
});
function formatNumber(number) {
    const options = {
        useGrouping: true,
    };
    return new Intl.NumberFormat('zh-CN', options).format(number);
}
this.summaryarry = resultsum

        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
