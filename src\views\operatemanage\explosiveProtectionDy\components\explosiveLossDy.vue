<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <div class="top">
        <!-- <el-date-picker style="width: 230px" class="publicCss" v-model="daterange" type="daterange" format="yyyy-MM-dd"
          value-format="yyyy-MM-dd" range-separator="至" start-placeholder="爆款开始日期" end-placeholder="爆款结束日期"
          :clearable="true" :picker-options="pickerOptions">
        </el-date-picker> -->
        <el-input v-model.trim="filter.styleCode" placeholder="系列编码" class="publicCss" clearable maxlength="40" />
        <el-input v-model.trim="filter.productID" placeholder="产品ID" class="publicCss" clearable maxlength="50" />
        <!-- <el-select v-model="filter.stats" clearable filterable placeholder="类型" allow-create default-first-option
          class="publicCss">
          <el-option v-for="item in statsList" :key="item" :label="item" :value="item" />
        </el-select> -->
        <el-input v-model.trim="filter.title" placeholder="产品名称" class="publicCss" clearable maxlength="50" />
        <el-select v-model="filter.shopCode" class="publicCss" size="mini" placeholder="店铺" clearable filterable>
          <el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select filterable v-model="filter.groupId" placeholder="小组" class="publicCss" clearable>
          <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key" />
        </el-select>
        <el-select filterable v-model="filter.operateSpecialUserId" collapse-tags clearable placeholder="运营专员"
          class="publicCss">
          <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select filterable v-model="filter.userId" collapse-tags clearable placeholder="运营助理" class="publicCss">
          <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="filter.brands" clearable filterable placeholder="品牌" multiple collapse-tags
          class="publicCss" style="width: 150px;">
          <el-option v-for="item in brandList" :key="item" :label="item" :value="item" />
        </el-select>
        <div>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport">导出</el-button>
        </div>
      </div>
    </template>

    <vxetablebase :id="'explosiveLossDy202502271059'" :border="true" :align="'center'"
      :tablekey="'explosiveLossDy202502271059'" ref="table" :that='that' :isIndex='true' :hasexpand='false'
      @sortchange='sortchange' :isSelectColumn="true" :showsummary='false' :tablefixed='true' :summaryarry='summaryarry'
      :tableData='datalist' :tableCols='tableCols' :loading="listLoading" style="width:100%;height:100%;margin: 0"
      :xgt="9999">
      <template slot="right">
        <vxe-column title="操作" width="120" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;align-items: center;">
              <el-button type="text" @click="onOneKeyLoss(1, row)"
                v-if="checkPermission('DyExplosiveProtectionOneKeyTrend')">转趋势</el-button>
              <el-button type="text" @click="onOneKeyLoss(2, row)"
                v-if="checkPermission('DyExplosiveProtectionOneKeyHit')">转爆款</el-button>
              <el-button type="text" @click="onDeletionMethod(row)"
                v-if="checkPermission('DyExplosiveProtectionOneKeyDelete')">删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>
  </my-container>
</template>
<script>
import dayjs from "dayjs";
import { platformlist } from '@/utils/tools'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatPlatform, formatTime, pickerOptions, formatLinkProCode } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import inputYunhan from "@/components/Comm/inputYunhan";
import { getExplosiveProtectionDyAsyncList, importExplosiveProtectionDyAsync, deleteExplosiveProtectionDyAsync, updateExplosiveProtectionDyAsync, exportExplosiveProtectionDy } from '@/api/bookkeeper/reportdayV2'
import buschar from '@/components/Bus/buschar'
import { downloadLink } from "@/utils/tools.js";

const tableCols = [
  { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom', width: '200' },
  { istrue: true, prop: 'brand', label: '品牌', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'productCategoryName', label: '类目', sortable: 'custom', width: '120' },
  { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: '120' },
  { istrue: true, prop: 'proCode', label: '产品ID', sortable: 'custom', width: '120', type: 'html', formatter: (row) => formatLinkProCode(6, row.proCode) },
  //{ istrue: true, prop: 'stats', label: '类型', sortable: 'custom', width: '120' },
  { istrue: true, prop: 'attributeTag', label: '属性', sortable: 'custom', width: '120' },
  { istrue: true, prop: 'title', label: '产品名称', sortable: 'custom', width: '300' },
  { istrue: true, prop: 'groupId', label: '小组', sortable: 'custom', width: '80', formatter: (row) => row.groupName },
  { istrue: true, prop: 'operateSpecialUserId', label: '专员', sortable: 'custom', width: '80', formatter: (row) => row.operateSpecialUserName },
  { istrue: true, prop: 'userId', label: '助理', sortable: 'custom', width: '80', formatter: (row) => row.userName },
  { istrue: true, prop: 'yearMonthDayDate', label: '爆款时间', sortable: 'custom', width: '150' },
  { istrue: true, prop: 'createdUserName', label: '流失操作人', sortable: 'custom', width: '150' },
  { istrue: true, prop: 'operatingTime', label: '操作时间', sortable: 'custom', width: '150' },
];
const startUseDate = formatTime(dayjs(), "YYYY-MM-DD");
const endUseDate = formatTime(dayjs(), "YYYY-MM-DD");

export default {
  name: "explosiveLossDy",
  components: {
    MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, vxetablebase, inputYunhan, buschar
  },
  props: {
    directorGroupList: {
      type: Array,
      default: () => []
    },
    shopList: {
      type: Array,
      default: () => []
    },
    directorlist: {
      type: Array,
      default: () => []
    },
    statsList: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      that: this,
      daterange: [startUseDate, endUseDate],
      filter: {
        shopCode: "",
        attributeTag: null,
        startUseDate: null,
        endUseDate: null,
        productID: "",
        styleCode: "",
        groupId: "",
        operateSpecialUserId: "",
        userId: "",
        title: "",
        dataType: '流失',
        stats: "",
        brands: [],
      },
      pickerOptions: pickerOptions,
      tableCols: tableCols,
      total: 0,
      datalist: [],
      brandList: [],
      pager: { OrderBy: "", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      summaryarry: {},
      selids: [],
      selrows: [],// 列表选中行
    };
  },
  async mounted() {
    await this.onSearch();
  },
  methods: {
    async onSearch() {
      this.$refs.pager.setPage(1);
      await this.getList();
    },
    // 获取条件
    getCondition() {
      this.filter.startUseDate = null;
      this.filter.endUseDate = null;
      // if (this.daterange) {
      //   this.filter.startUseDate = this.daterange[0];
      //   this.filter.endUseDate = this.daterange[1];
      // } else {
      //   this.filter.startUseDate = '2020-01-01';
      //   this.filter.endUseDate = '2030-01-01';
      // }
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };
      return params;
    },
    // 获取列表
    async getList() {
      const params = this.getCondition();
      if (params === false) {
        return;
      }
      console.log(params);
      this.listLoading = true;
      const res = await getExplosiveProtectionDyAsyncList(params);
      this.listLoading = false;
      console.log(res);
      this.total = res.data?.total;
      this.datalist = res.data?.list;
      const newBrands = new Set(this.datalist.map(f => f.brand)); // 本次获取的品牌
      this.brandList = Array.from(new Set([...this.brandList, ...newBrands]));
      this.summaryarry = res.data?.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    // 排序
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      await this.onSearch();
    },
    onDeletionMethod(row) {
      this.$confirm('确定要删除该数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.pageLoading = true;
        var res = await deleteExplosiveProtectionDyAsync({ id: row.id });
        this.pageLoading = false;
        if (res?.success) {
          this.$message({ type: 'success', message: '删除成功!' });
          await this.onSearch();
        }
      }).catch(() => {
      });
    },
    // 修改
    async onOneKeyLoss(val, row) {
      let ids = [];
      ids.push(row.id);
      const oldEntityStatsList = [{ id: row.id, oldStats: row.stats }];
      this.$confirm(`确定要${val == 1 ? '一键转趋势' : '一键转爆款'}当前数据吗?`, '提示', {
        confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
      }).then(async () => {
        this.pageLoading = true;
        var res = await updateExplosiveProtectionDyAsync({ oldEntityStatsList: oldEntityStatsList, dataType: val == 1 ? '趋势款' : '爆款' });
        this.pageLoading = false;
        if (res?.success) {
          this.$message({ type: 'success', message: '操作成功' });
          this.selrows = [];
          await this.onSearch();
        }
      }).catch(() => {
      });
    },
    // 导出
    async onExport() {
      const params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await exportExplosiveProtectionDy(params)
      this.listLoading = false;
      if (!res?.data) {
        this.$message({ type: 'error', message: '没有数据可导出或导出失败!' });
        return;
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', "抖音爆款流失_" + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
  }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 121px;
    margin-right: 3px;
  }
}

::v-deep .el-button+.el-button,
.el-checkbox.is-bordered+.el-checkbox.is-bordered {
  margin-left: 3px;
}
</style>
