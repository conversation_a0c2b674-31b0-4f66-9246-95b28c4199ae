<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-select v-model="filter.selectDateType" placeholder="" style="width: 100px" filterable>
                <el-option label="违规时间" value="WeiGuiTime" />
                <el-option label="付款时间" value="PayTime" />
                <el-option label="发货时间" value="FaHuoTime" />
            </el-select>

            <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" range-separator="至" :start-placeholder="'开始时间'" :end-placeholder="'结束时间'"
                :picker-options="pickerOptions">
            </el-date-picker>

            <el-select v-model="filter.noPassTypeList" style="width:200px;" placeholder="违规环节" :clearable="true"
                multiple collapse-tags>
                <el-option label="发货-批次耗时(h)" value=1></el-option>
                <el-option label="批次-面单耗时(h)" value=2></el-option>
                <el-option label="面单-出库耗时(h)" value=3></el-option>
                <el-option label="出库-打包耗时(h)" value=4></el-option>
                <el-option label="打包-发货耗时(h)" value=5></el-option>
                <el-option label="发货-称重耗时(h)" value=6></el-option>
                <el-option label="称重-揽收耗时(h)" value=7></el-option>
                <el-option label="揽收-中转耗时(h)" value=8></el-option>
                <el-option label="中转-派送耗时(h)" value=9></el-option>
                <el-option label="派送-签收耗时(h)" value=10></el-option>
            </el-select>
            <el-input v-model.trim="filter.noPassUserName" clearable placeholder="违规人" style="width:140px;"
                :maxlength="20" v-show="(activeName == 'tabname1' || activeName == 'tabname2')" />
            <el-input v-model.trim="filter.noPassWmsName" clearable placeholder="发货仓" style="width:140px;"
                :maxlength="20" />
            <el-input v-model.trim="filter.noPassExpressCompanyName" clearable placeholder="快递公司" style="width:140px;"
                :maxlength="20" />

            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="onExport" :loading="onExportLoading">导出</el-button>
        </template>

        <el-tabs v-model="activeName" style="height: 94%;">
            <el-tab-pane label="违规人" name="tabname1" style="height: 100%;">
                <DeductOrderNodeDtlRept1 :filter="filter" ref="DeductOrderNodeDtlRept1" />
            </el-tab-pane>

            <el-tab-pane label="发货仓" name="tabname2" style="height: 100%;">
                <DeductOrderNodeDtlRept2 :filter="filter" ref="DeductOrderNodeDtlRept2" />
            </el-tab-pane>

            <el-tab-pane label="快递公司" name="tabname3" style="height: 100%;">
                <DeductOrderNodeDtlRept3 :filter="filter" ref="DeductOrderNodeDtlRept3" />
            </el-tab-pane>

            <el-tab-pane label="违规环节" name="tabname4" style="height: 100%;">
                <DeductOrderNodeDtlRept4 :filter="filter" ref="DeductOrderNodeDtlRept4" />
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import dayjs from "dayjs";
import { pickerOptions } from '@/utils/tools'
import { formatTime, } from "@/utils";
import DeductOrderNodeDtlRept1 from "./DeductOrderNodeDtlRept1.vue";
import DeductOrderNodeDtlRept2 from "./DeductOrderNodeDtlRept2.vue";
import DeductOrderNodeDtlRept3 from "./DeductOrderNodeDtlRept3.vue";
import DeductOrderNodeDtlRept4 from "./DeductOrderNodeDtlRept4.vue";
import { ExportDeductNodeNoPassRpt } from "@/api/order/deductbefore"

export default {
    name: 'DeductOrderNodeDtlRept',
    components: {
        MyContainer, MyConfirmButton, DeductOrderNodeDtlRept1, DeductOrderNodeDtlRept2, DeductOrderNodeDtlRept3, DeductOrderNodeDtlRept4
    },
    data() {
        return {
            pickerOptions: pickerOptions,
            activeName: 'tabname1',
            filter: {
                selectDateType: "WeiGuiTime",
                timerange: [
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD")],
                noPassTypeList: []
            },
            pageLoading: false,
            onExportLoading: false,
        }
    },
    async mounted() {
    },
    methods: {
        onSearch() {
            console.log(this.activeName);
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            if (this.activeName == 'tabname1') {
                this.$refs.DeductOrderNodeDtlRept1.onSearch();
            }
            else if (this.activeName == 'tabname2') {
                this.$refs.DeductOrderNodeDtlRept2.onSearch();
            }
            else if (this.activeName == 'tabname3') {
                this.$refs.DeductOrderNodeDtlRept3.onSearch();
            }
            else if (this.activeName == 'tabname4') {
                this.$refs.DeductOrderNodeDtlRept4.onSearch();
            }
        },

        async onExport() {
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            } else {
                this.$message({ message: "请先选择日期", type: "warning" });
                return false;
            }
            this.onExportLoading = true
            const res = await ExportDeductNodeNoPassRpt(this.filter)
            this.onExportLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', new Date().toLocaleString() + '导出-违规统计.xlsx');
            aLink.click()
        },
    }
}
</script>

<style lang="scss" scoped>
::v-deep .el-select__tags-text {
    max-width: 80px;
}
</style>
