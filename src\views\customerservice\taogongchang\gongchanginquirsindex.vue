<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" :before-leave="beforeLeave" style="height:94%;">
            <el-tab-pane name="switch">
                <span slot="label">
                    <el-switch v-model="switchshow" @change="changeShowgroup" :disabled="isAllcheck" active-text="售后管理"
                        inactive-text="售前管理">
                    </el-switch>
                </span>
            </el-tab-pane>

            <!-- 售前 -->
            <el-tab-pane v-if="showSq" label="分组管理(售前组)" name="tab30" style="height: 100%;">
                <sqgroup :filter="filter" ref="sqgroup" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane v-if="showSq" label="咨询数据导入(售前组)" name="tab31" style="height: 100%;" lazy>
                <sqinquirs :filter="filter" ref="sqinquirs" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane v-if="showSq" label="组效率统计(售前组)" name="tab32" style="height: 100%;" lazy>
                <sqgroupinquirsstatistics :filter="filter" ref="sqgroupinquirsstatistics" style="height: 100%;"
                    @clickgroupname="clickgroupname" />
            </el-tab-pane>
            <el-tab-pane v-if="showSq" label="店效率统计(售前组)" name="tab33" style="height: 100%;" lazy>
                <sqshopinquirsstatistics :filter="filter" ref="sqshopinquirsstatistics" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane v-if="showSq" label="个人效率统计(售前组)" name="tab34" style="height: 100%;">
                <sqinquirsstatistics :filter="filter" ref="sqinquirsstatistics" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane v-if="showSq" label="未匹配咨询数据" name="tab35" style="height: 100%;" lazy>
                <gongchanginquirsno ref="gongchanginquirsno" style="height: 100%;" />
            </el-tab-pane>

            <!-- 售后 -->
            <el-tab-pane v-if="showSh" label="分组管理(售后组)" name="tab40" style="height: 100%;">
                <shgroup :filter="filter" ref="shgroup" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="咨询数据导入(售后组)" name="tab41" style="height: 100%;" lazy>
                <shinquirs :filter="filter" ref="shinquirs" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="组效率统计(售后组)" name="tab42" style="height: 100%;" lazy>
                <shgroupinquirsstatistics :filter="filter" ref="shgroupinquirsstatistics" style="height: 100%;"
                    @clickgroupname="clickgroupname" />
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="店效率统计(售后组)" name="tab43" style="height: 100%;" lazy>
                <shshopinquirsstatistics :filter="filter" ref="shshopinquirsstatistics" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="个人效率统计(售后组)" name="tab44" style="height: 100%;">
                <shinquirsstatistics :filter="filter" ref="shinquirsstatistics" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="未匹配咨询数据" name="tab45" style="height: 100%;" lazy>
                <gongchanginquirsno1 ref="gongchanginquirsno1" style="height: 100%;" />
            </el-tab-pane>

            <el-tab-pane label="退款满意度" name="tab46" style="height: 100%;" lazy>
                <RefundSatisfaction ref="satisfaction" style="height: 100%;" />
            </el-tab-pane>

        </el-tabs>
    </my-container>
</template>
<script>
import checkPermission from '@/utils/permission';
import MyContainer from "@/components/my-container";

import sqgroup from '@/views/customerservice/taogongchang/sq/sqgroup';
import sqinquirs from '@/views/customerservice/taogongchang/sq/sqinquirs';
import sqinquirsstatistics from '@/views/customerservice/taogongchang/sq/sqinquirsstatistics';
import sqgroupinquirsstatistics from '@/views/customerservice/taogongchang/sq/sqgroupinquirsstatistics';
import sqshopinquirsstatistics from '@/views/customerservice/taogongchang/sq/sqshopinquirsstatistics';
import gongchanginquirsno from '@/views/customerservice/taogongchang/gongchanginquirsno';

import shgroup from '@/views/customerservice/taogongchang/sh/shgroup';
import shinquirs from '@/views/customerservice/taogongchang/sh/shinquirs';
import shinquirsstatistics from '@/views/customerservice/taogongchang/sh/shinquirsstatistics';
import shgroupinquirsstatistics from '@/views/customerservice/taogongchang/sh/shgroupinquirsstatistics';
import shshopinquirsstatistics from '@/views/customerservice/taogongchang/sh/shshopinquirsstatistics';
import gongchanginquirsno1 from '@/views/customerservice/taogongchang/gongchanginquirsno1';
import RefundSatisfaction from "@/views/customerservice/taogongchang/gongchanginquirsindex/refundSatisfaction";

export default {
    name: "gongchanginquirsindex",
    provide() {
        return {
            reload: this.reload
        }
    },
    components: {
        MyContainer,
        sqgroup, sqinquirs, sqinquirsstatistics, sqgroupinquirsstatistics, sqshopinquirsstatistics, gongchanginquirsno,
        shgroup, shinquirs, shinquirsstatistics, shgroupinquirsstatistics, shshopinquirsstatistics, gongchanginquirsno1,
        RefundSatisfaction
    },

    data() {
        return {
            that: this,
            pageLoading: '',
            filter: {
            },
            shopList: [],
            userList: [],
            groupList: [],
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            activeName: 'tab30',
            //判断权限用
            IsSq: true,

            switchshow: false,
            //默认展示售前
            showSq: true,
            //默认展示售前
            showSh: false,
            isAllcheck: false,
            isShowTj: false,
            infoBool: false,//是否包含离组
            infoBoolH: false,
            infoBool2: false,
        };
    },
    mounted() {

    },
    methods: {
        beforeLeave(visitName, currentName) {
            if (visitName == "switch")
                return false;
        },
        changeShowgroup() {
            if (this.switchshow) {
                this.activeName = 'tab40';
                this.showSh = true;
                this.showSq = false;
            } else {
                this.activeName = 'tab30';
                this.showSh = false;
                this.showSq = true;
            }
        },
        clickgroupname(params) {
            if (this.switchshow) {
                this.activeName = 'tab44';
                this.$refs.shinquirsstatistics.loadData2(params)
            } else {
                this.activeName = 'tab34';
                this.$refs.sqinquirsstatistics.loadData2(params)
            }
        }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
