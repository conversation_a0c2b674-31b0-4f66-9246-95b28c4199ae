<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select v-model="ListInfo.dateType" placeholder="日期类型" class="publicCss" clearable>
                    <el-option label="提交日期" value="提交日期" />
                    <el-option label="新价日期" value="新价日期" />
                    <el-option label="截止日期" value="截止日期" />
                </el-select>
                <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
                  end-placeholder="结束日期" style="width: 230px;margin-right: 5px;" :clearable="false"
                  :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="proCodeCallback" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <el-input v-model.trim="ListInfo.applyUserName" placeholder="提交人" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.businessIds" placeholder="钉钉编号" maxlength="100" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="100" clearable
                    class="publicCss" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                    <!-- <el-button type="primary" @click="disposition">配置</el-button> -->
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' border
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false" showsummary
            :summaryarry="summaryarry" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
            :height="'100%'">
            <template slot="right">
              <vxe-column title="操作" width="100" fixed="right">
                <template #default="{ row, rowIndex }">
                    <div style="display: flex;justify-content: center;">
                        <el-button type="text" @click="onEdit(row)">编辑</el-button>
                    </div>
                </template>
              </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="配置" :visible.sync="dialogVisible" width="60%" append-to-body>
            <dispositionPage />
        </el-dialog>
        <el-dialog title="编辑" :visible.sync="editInfo.visible" width="25%" append-to-body v-dialogDrag :close-on-click-modal="false">
            <editPage v-if="editInfo.visible" :peizhidata="editInfo.rowData" @closedialog="editInfo.visible = false" @successClosedialog="successClosedialog" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import dispositionPage from './dispositionPage.vue'
import editPage from './editPage.vue'
import { GetCostMaintenanceManageList, ExportCostMaintenanceManageList } from '@/api/cwManager/costMaintenanceManager'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'applyTime', label: '提交日期', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'applyUserName', label: '提交人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'businessId', label: '钉钉编号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'price', label: '历史成本价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'cost', label: '成本价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'newPriceStartDate', label: '新价日期', formatter: (row) => row.newPriceStartDate ? dayjs(row.newPriceStartDate).format('YYYY-MM-DD') : row.newPriceStartDate },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'newPriceEndDate', label: '截止日期', formatter: (row) => row.newPriceEndDate ? dayjs(row.newPriceEndDate).format('YYYY-MM-DD') : row.newPriceEndDate },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'mainStock', label: '实际库存', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'mainStockMarginAmount', label: '库存损失', },
]
const time = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, dispositionPage, inputYunhan, editPage
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: time,
                endDate: time,
                dateType: '提交日期',
            },
            timeRanges: [time, time],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            dialogVisible: false,
            editInfo: {
              visible: false,
              rowData: {},
            },
            summaryarry: {}
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async changeTime(e) {
          this.ListInfo.startDate = e ? e[0] : null
          this.ListInfo.endDate = e ? e[1] : null
        },
        successClosedialog() {
          this.editInfo.visible = false
          this.getList()
        },
        onEdit(row) {
          this.editInfo.rowData = row
          this.editInfo.visible = true
        },
        proCodeCallback(val) {
            this.ListInfo.goodsCodes = val
        },
        disposition() {
            this.dialogVisible = true
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await ExportCostMaintenanceManageList(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '成本维护_成本维护' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetCostMaintenanceManageList(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.summaryarry = data.summary
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
