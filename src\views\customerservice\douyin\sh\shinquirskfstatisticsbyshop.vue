<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' style="height:93%;" :summaryarry="summaryarry" :isIndex='true'
                :hasexpand='false' @sortchange='sortchange' :tableData='inquirsstatisticslist' @select='selectchange'
                :isSelection='false' :tableCols='tableCols' :loading="listLoading">
                <template slot='extentbtn'>
                    <el-input v-model.trim="filter.groupName" placeholder="组" style="width:120px;" clearable disabled
                            :maxlength="50" />
                    <el-input v-model="filter.sname" v-model.trim="filter.sname" placeholder="姓名" style="width:120px;"
                        disabled="true" :maxlength="50" />
                    <el-input v-model="filter.startDate" style="width:120px;" disabled="true" />至
                    <el-input v-model="filter.endDate" style="width:120px;" disabled="true" />
                </template>
            </ces-table>
        </template>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import {
    getDouYinShopPersonalEfficiencyKfPageList
} from '@/api/customerservice/douyininquirs'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

const tableCols = [
    { istrue: true, prop: 'shopName', label: '店铺名称', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'sname', label: '姓名', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'allCount', label: '评价数量', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'buManYiRate', label: '不满意率', width: '100', sortable: 'custom', formatter: (row) => row.buManYiRate + "%" },
    { istrue: true, prop: 'buManYiCount', label: '不满意人数', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'manYiRate', label: '满意率', width: '100', sortable: 'custom', formatter: (row) => row.manYiRate + "%" },
    { istrue: true, prop: 'manYiCount', label: '满意人数', width: '100', sortable: 'custom' },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, datepicker, cesTable },
    data() {
        return {
            that: this,
            filter: {
                groupType: 1,
                inquirsType: 1,
                sname: "",
                groupName: "",
                sdate: [],
                endDate: "",
                startDate: ""
            },
            shopList: [],
            userList: [],
            groupList: [],
            inquirsstatisticslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "shopName", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
        };
    },
    async mounted() {

    },
    created() {

    },
    methods: {
        async dialogOpenAfter(data) {
            console.log(data);
            this.filter.sdate[0] = data.startDate;
            this.filter.sdate[1] = data.endDate;
            this.filter.startDate = data.startDate;
            this.filter.endDate = data.endDate;
            this.filter.sname = data.sname;
            this.filter.groupName = data.groupName;
            this.onSearch();
        },
        onSearch() {
            this.getinquirsstatisticsList();
        },
        async getinquirsstatisticsList() {
            const para = { ...this.filter };
            const params = {
                ...this.pager,
                ...para,
            };
            this.listLoading = true;
            const res = await getDouYinShopPersonalEfficiencyKfPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.inquirsstatisticslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
