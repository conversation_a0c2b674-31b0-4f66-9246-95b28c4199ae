<template>
    <MyContainer>
        <div style="color: red;margin: 10px 0;">
            此页面只针对参与蓄单后满足蓄单时间不满足放单单量的订单
        </div>
        <el-button type="text" @click="addProps">新增一行</el-button>
        <el-table :data="tableData" style="width: 100%" max-height="250">
            <el-table-column type="index" width="50" />
            <el-table-column prop="name" label="仓库名称">
                <template #default="{ row, $index }">
                    <el-select v-model="row.wmsId" placeholder="仓库名称" @change="changeWareHouse($event, $index)"
                        clearable>
                        <el-option v-for="item in wareHousesList" :key="item.value" :label="item.label"
                            :value="item.value" :disabled="item.notMobilize || item.notMobilize === null" />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column prop="address" label="通道">
                <template #default="{ row, $index }">
                    <el-select v-model="row.thoroughfare" placeholder="通道" clearable collapse-tags filterable multiple>
                        <el-option v-for="item1 in row.thoroughfareList" :key="item1.thoroughfare"
                            :label="item1.thoroughfare" :value="item1.thoroughfare" />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column prop="address" label="蓄单时间（小时）">
                <template #default="{ row }">
                    <el-input-number v-model="row.hour" :min="1" :max="9999999" label="描述文字" :precision="1"
                        :controls="false" />
                </template>
            </el-table-column>
            <el-table-column prop="address" label="蓄单量">
                <template #default="{ row }">
                    <el-input-number v-model="row.orderCount" :min="1" :max="9999999" label="描述文字" :precision="0"
                        :controls="false" />
                </template>
            </el-table-column>
            <el-table-column prop="address" label="删除">
                <template #default="{ row, $index }">
                    <el-button type="danger" @click="tableData.splice($index, 1)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="btnGroup">
            <el-button @click="closeDialog">取消</el-button>
            <el-button type="primary" @click="submit" v-throttle="2000">确认</el-button>
        </div>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import {
    getSecondaryMobilize,
    getWareHouses,
    setSecondaryMobilize,
    getThorougnfares
} from '@/api/vo/VerifyOrder'
export default {
    name: "setGroup",
    data() {
        return {
            tableData: [],
            wareHousesList: []
        }
    },
    async mounted() {
        await this.getSecondGroup()
        await this.getProps()
    },
    methods: {
        async getThorought(wmsId, index) {
            const { data, success } = await getThorougnfares({ wmsId })
            if (success) {
                this.$nextTick(() => {
                    this.$set(this.tableData[index], 'thoroughfareList', data)//vue不能检测到数组的变化，所以需要手动触发
                })
            }
        },
        async getSecondGroup() {
            const { data, success } = await getSecondaryMobilize()
            if (success) {
                data.forEach((item, i) => {
                    item.thoroughfare = item.thoroughfare ? item.thoroughfare.split(',') : []
                    if (item.wmsId) {
                        this.getThorought(item.wmsId, i)
                    }
                })
                this.tableData = data
                if (this.tableData == null || this.tableData.length == 0) {
                    this.addProps()
                }
            }
        },
        async changeWareHouse(value, index) {
            this.tableData[index].thoroughfare = null
            if (value) {
                this.tableData[index].wmsName = this.wareHousesList.find(item => item.value === value).label
                await this.getThorought(value, index)
            } else {
                this.tableData[index].wmsName = null
            }
        },
        async getProps() {
            const { data, success } = await getWareHouses()
            if (success) {
                this.wareHousesList = data.map(item => {
                    return {
                        label: item.name,
                        value: item.wms_co_id,
                        notMobilize: item.notMobilize
                    }
                })
            }
        },
        addProps() {
            this.tableData.push({ wmsName: null, wmsId: null, thoroughfare: [], thoroughfareList: [] })
        },
        closeDialog() {
            this.$emit('close')
        },
        groupBySameNames(tableData) {
            const groupedData = tableData.reduce((acc, item) => {
                if (!acc[item.wmsName]) {
                    acc[item.wmsName] = [];
                }
                acc[item.wmsName].push(item);
                return acc;
            }, []);
            // 找出 thoroughfare 中有相同数据的项
            const result = [];
            for (let id in groupedData) {
                const group = groupedData[id];
                const thoroughfare = group
                    .reduce((acc, item) => acc.concat(item.thoroughfare), [])
                    .filter((value, index, self) => self.indexOf(value) !== self.lastIndexOf(value));
                if (thoroughfare.length > 0) {
                    result.push({
                        wmsName: id,
                        thoroughfare: [...new Set(thoroughfare)],
                    });
                }
            }
            return result;
        },
        async submit() {
            if (this.tableData.length == 0) return this.$message.error('请至少添加一行')
            this.tableData.forEach((item, index) => {
                if (!item.hour || !item.orderCount) {
                    this.$message.error(`第${index + 1}行蓄单时间和蓄单量不能为空`)
                    throw new Error('error')
                } else if (item.thoroughfare && item.thoroughfare.length > 0 && !item.wmsName) {
                    this.$message.error(`第${index + 1}行有通道时，仓库名称不能为空`)
                    throw new Error('error')
                }  else if(!item.wmsId && (item.thoroughfare == null || item.thoroughfare.length == 0)){
                    this.$message.error(`仓库名称和通道不能为空`)
                    throw new Error('error')
                } else if(!item.wmsId && item.thoroughfare && item.thoroughfare.length > 0){
                    this.$message.error(`第${index + 1}行有通道时，仓库名称不能为空`)
                    throw new Error('error')
                } else if (item.wmsId && (item.thoroughfare == null || item.thoroughfare.length == 0)) {
                    this.$message.error(`第${index + 1}行有仓库名称时，通道不能为空`)
                    throw new Error('error')
                }
            })
            //判断是否相同的仓库有相同的通道
            const result = this.groupBySameNames(this.tableData)
            //如果有就提示
            if (result && result.length > 0) {
                const message = result.map(item => {
                    return `${item.wmsName} 有相同的通道 ${item.thoroughfare.join(', ')}`
                }).join('；')
                return this.$message.error(message)
            }
            this.tableData.forEach(item => {
                item.thoroughfare = item.thoroughfare ? item.thoroughfare.join(',') : ''
            })
            const { success } = await setSecondaryMobilize(this.tableData)
            if (success) {
                this.$emit('close')
                this.$emit('getList', true)
                this.$message.success('设置二次组团成功')
            } else {
                console.log();
                this.$message.error('设置二次组团失败')

            }
        }
    }
}
</script>

<style lang="scss" scoped>
.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 20px;

    button {
        width: 100px;
    }
}
</style>
