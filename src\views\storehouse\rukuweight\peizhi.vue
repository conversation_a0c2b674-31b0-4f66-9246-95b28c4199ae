<template>
  <div>
    <el-form label-position="right" label-width="140px" :model="formLabelAlign" :rules="addFormRules">
    <el-form-item label="纸箱重量（g）:" prop="cartonWeight">
        <!-- <el-input v-model="formLabelAlign.cartonWeight" type="number"></el-input> -->
        <el-input-number v-model="formLabelAlign.cartonWeight" controls-position="right" precision="2" :min="0" ></el-input-number>
    </el-form-item>
    <el-form-item label="袋子重量（g）:" prop="bagWeight">
        <!-- <el-input v-model="formLabelAlign.bagWeight" type="number"></el-input> -->
        <el-input-number v-model="formLabelAlign.bagWeight" controls-position="right" precision="2" :min="0" ></el-input-number>

    </el-form-item>
    <el-form-item label="重量上下幅度：" prop="extentValue">
        <el-row style="width: 100%;">
            <el-col :span="10">
                <el-select v-model="formLabelAlign.extentType" placeholder="请选择"
                    clearable>
                    <el-option label="重量(g)" :value="1" />
                    <el-option label="百分比(%)" :value="2" />
                </el-select>
            </el-col>
            <el-col :span="14">
                <div style="display: flex;flex-direction: row;" v-show="formLabelAlign.extentType == 1">
                    <el-input-number  v-model="formLabelAlign.extentValue" controls-position="right" precision="2" :min="0" ></el-input-number>
                </div>
                <div style="display: flex;flex-direction: row;" v-show="formLabelAlign.extentType == 2">
                    <el-input-number  v-model="formLabelAlign.extentValue" controls-position="right" precision="2" :min="0" ></el-input-number>
                </div>
                <!-- <el-input v-model="formLabelAlign.extentValue" v-show="formLabelAlign.extentType == 1" type="number"><template slot="append">g</template></el-input>
                <el-input v-model="formLabelAlign.extentValue" v-show="formLabelAlign.extentType == 2" type="number"><template slot="append">%</template></el-input> -->
            </el-col>
        </el-row>

    </el-form-item>
    </el-form>
    <div style="display: flex; justify-content: space-between; width: 100%; padding: 0 100px; box-sizing: border-box;">
        <el-button type="" @click="closefuc">取消</el-button>
        <el-button type="primary" @click="savesubmit">确定</el-button>
    </div>
  </div>
</template>

<script>
import { saveWarehousingOrderVideoWeightCompareConfig } from '@/api/inventory/purchasequality.js'
export default {
    // components: { MyContainer, cesTable, vxetablebase, vendorSummary, peizhi  },
    name: "vendorSumIndex",
    props: ['peizhidata'],
    // data() {
    //     return {
    //         formLabelAlign: {}
    //     },
    // },
    data() {
        return {
            formLabelAlign: {},
            addFormRules: {
                extentValue: [{ required: true, message: '请输入', trigger: 'blur' }],
                bagWeight: [{ required: true, message: '请输入', trigger: 'blur' }],
                cartonWeight: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
        };
    },
    mounted() {
        this.formLabelAlign = this.peizhidata;
    },
    methods: {
        closefuc(){
            this.$emit('closedialog');
        },
        async savesubmit(){
            let params  = {
                ...this.formLabelAlign
            }
            let res  = await saveWarehousingOrderVideoWeightCompareConfig(params);
            if(!res.success){
                return;
            }
            this.$message.success("保存成功");
            this.$emit('closedialog');
        }
    }
}
</script>
<style scoped lang="scss">
.el-form-item {
    justify-content: space-between;
    box-sizing: border-box;
}
</style>

