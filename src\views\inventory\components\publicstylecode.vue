<template>
  <container v-loading="pageLoading" style="height: 100%;">
    <div v-if="list.length>0">
      <template  v-for="item in list">
      <el-tag type="success" class="tag">{{item.styleCode  }}</el-tag>
    </template>
    </div>
    <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
  </container>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { formatTime,formatSecondToHour,formatmoney} from "@/utils/tools";
import {
 getPublicStyleCodeAsync
} from '@/api/order/procodesimilarity'
export default {
  name: 'publicstylecode',
  components: { container, MyConfirmButton,vxetablebase} ,
  data() {
      return {
          that:this,
          list:[],
          sels:[],
          total:0,
          listLoading:true,
          pageLoading:false,
      };
  },
async  mounted(){
   await this.onSearch()
  },
  created(){

  },
  methods: {
    async onSearch() {
            this.$refs.pager.setPage(1)
          await  this.getlist();
        },
     //请求列表数据//
        async getlist() {
          var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = { ...pager, ...page, ... this.filter }
            this.listLoading = true
            const res = await getPublicStyleCodeAsync(params)
            this.listLoading = false
            this.total = res.data.total
            this.list = res.data.list
        },
  }
}
</script>

<style lang="scss" scoped>
.ad-form-query{
  text-align: left;
}
.tag{
  margin-top: 1%;
  margin-left: 1%;
}
</style>>
