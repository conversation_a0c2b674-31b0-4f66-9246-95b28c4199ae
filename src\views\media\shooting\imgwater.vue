<template>
 <div>
   <canvas :ref="canvasmore" width="800" :height="imgheight"></canvas>
 </div>
</template>

<script>
export default {
 props: ['imgsrc', 'canvasmore'],
 data(){
  return {
   imgheight: 400,
  }
 },
 mounted() {
   this.drawCanvas();
 },
 methods: {
   drawCanvas() {
     const canvas = this.$refs[this.canvasmore];
     const ctx = canvas.getContext("2d");
     // 创建
     const img = new Image();
     img.src = this.imgsrc;
     // 加载完成
     img.onload = () => {
       this.imgheight = img.height; // 更新

       setTimeout(()=>{
         // 绘制图像
         ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        // 设置文本样式
        ctx.font = "32px Arial";
        ctx.fillStyle = 'rgba(0,0,0,0.1)';
        // ctx.fontWeight = 600;


        const watermarkText = '昀晗贸易   昀晗贸易    昀晗贸易    昀晗贸易    昀晗贸易   昀晗贸易';
        const watermarkWidth = ctx.measureText(watermarkText).width;

        const angle = -0.4; // 水印的旋转角度，单位为弧度
        const margin = 20; // 水印与边缘的距离

        // 绘制
        this.drawWatermark(ctx, watermarkText, watermarkWidth, angle, margin+margin*0.2, margin+margin*10); 
        this.drawWatermark(ctx, watermarkText, watermarkWidth, angle, canvas.width - watermarkWidth - margin+margin*0.2, margin+margin*25);
        this.drawWatermark(ctx, watermarkText, watermarkWidth, angle, margin, margin+margin*40); // 左下角
        this.drawWatermark(ctx, watermarkText, watermarkWidth, angle, canvas.width - watermarkWidth - margin, margin+margin*50); // 右下角

        this.drawWatermark(ctx, watermarkText, watermarkWidth, angle, canvas.width - watermarkWidth - margin, canvas.height - margin-margin*25); // 右下角


        this.drawWatermark(ctx, watermarkText, watermarkWidth, angle, canvas.width - watermarkWidth - margin, canvas.height - margin+margin*15); // 右下角

        // this.drawWatermark(ctx, watermarkText, watermarkWidth, angle, canvas.width / 2 - watermarkWidth / 2, canvas.height / 2); // 中间
       },200)

      
     };
   },
   drawWatermark(ctx, text, width, angle, x, y) {
     const lines = text.split('\n');
     const lineHeight = 30;

     ctx.save();
     ctx.fillStyle = 'rgba(0,0,0,0.3)';
     ctx.translate(x, y);
     ctx.rotate(angle);

     for (let i = 0; i < lines.length; i++) {
       ctx.fillText(lines[i], 0, i * lineHeight);
     }

     ctx.restore();
   },
 },
};
</script>
