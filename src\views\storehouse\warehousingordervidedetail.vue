<template>
    <container>
        <el-row :v-loading="listLoading">
            <el-col :span="6" v-for="item in list" :key="item.id">
                <el-card style="height: 320px; width: 300px; margin-bottom: 20px; box-shadow: 20px 0px 10px 0px rgba(0,0,0,0.4)" body-style="padding: 0px">
                    <div style="height: 270px; width: 300px;  position: relative;">
                        <img :src="item.imgPath" class="sacimg" @click="onshowMv(item)" style="max-width: 420PX; max-height: 235PX;">
                    </div>
                    <div style="margin-top: 20px;">
                        <el-button type="text" @click="onshowMv(item)">{{item.fileTypeName}}</el-button>
                    </div>
                </el-card>
                <!-- <el-card :body-style="{ padding: '0px' }" style="position: relative; height: 250px; width: 250px;">
                    <img :src="item.imgPath" class="sacimg" @click="onshowMv(item)" style="max-width: 420PX; max-height: 235PX;">
                    <div style="padding: 14px;">
                        <el-button type="text" @click="onshowMv(item)">{{item.fileTypeName}}</el-button>
                    </div>
                </el-card> -->
            </el-col>
        </el-row>

        <!--视频播放-->
        <el-dialog title="视频播放" :visible.sync="dialogVisible" width="50%" @close="closeVideoPlyer"
            :append-to-body="true">
            <videoplayer :autoplay="true" v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeVideoPlyer">关闭</el-button>
            </span>
        </el-dialog>
        <!-- 图片信息 -->
        <el-image-viewer v-if="showGoodsImage" :url-list="imgList" :on-close="closeVideoPlyer" style="z-index:9999;" />
    </container>
</template>

<script>
import container from "@/components/my-container"
import MyConfirmButton from "@/components/my-confirm-button";
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
import { pageWarehousingOrderVideoDetail } from "@/api/inventory/warehousingordervide"

export default {
    name: 'YunHanAdminWarehousingordervidedetail',
    components: {container, MyConfirmButton, ElImageViewer, videoplayer},
    props: {
        filter: {}
    },

    data() {
        return {
            that: this,
            list: [],
            listLoading: true,
            dialogVisible: false,
            videoplayerReload: false,
            showGoodsImage: false
        };
    },

    async mounted() {

    },

    methods: {
        async onSearch(warehousNo) {
            this.getlist(warehousNo);
        },
        async getlist(warehousNo) {
            const params = { warehousNo: warehousNo }
            this.listLoading = true
            var res = await pageWarehousingOrderVideoDetail(params);
            setTimeout(() => {
                this.listLoading = false
            }, 1000)

            if (!res?.success) {
                return
            }
            const data = res.data;
            data.map((item) => {
                item.imgPath = this.matchUrl(item.imgPath)
            })
            this.list = data
        },
        matchUrl(value) {
          let res = JSON.parse(JSON.stringify(value));
          if (!res) return res;
          res = res.replace(/(\.\w+)$/, '_300x300$1');
          return res;
        },
        async onshowMv(row) {
            this.videoUrl = row.videoPath;
            this.fileType = row.fileType;
            if (row.fileType == 0) {
                this.videoplayerReload = true;
                this.dialogVisible = true;
            } else {
                this.showGoodsImage = true;
                this.imgList = [];
                this.imgList.push(row.videoPath);
            }
        },
        async closeVideoPlyer() {
            let _this = this;
            _this.dialogVisible = false;
            _this.videoplayerReload = false;
            _this.showGoodsImage = false;
        },
    },
};
</script>

<style lang="scss" scoped>
.time {
    font-size: 13px;
    color: #999;
  }

  .bottom {
    margin-top: 13px;
    line-height: 12px;
  }

  .button {
    padding: 0;
    float: right;
  }

  .image {
    width: 100%;
    display: block;
  }

  .clearfix:before,
  .clearfix:after {
      display: table;
      content: "";
  }

  .clearfix:after {
      clear: both
  }
  .sacimg{
    height: 250px;
    width: 250px;
    position: absolute;
    clip: rect(0,220px, 200px,0);
    transform: scale(2,2);
}
</style>
