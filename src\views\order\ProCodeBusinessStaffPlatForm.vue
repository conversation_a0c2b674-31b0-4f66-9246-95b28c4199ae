<template>
 <div style="height:100%;padding:10px;overflow: auto;">
    <div id="echartturnoverranganalysis1111" style="width: 100%;height: 489px; box-sizing:border-box; line-height: 360px;"/>
    <div id="chart"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
import container from '@/components/my-container/nofooter'
import cesTable from "@/components/Table/table.vue";
import {getProCodeUnsalableAnalysisByDate,getProCodeUnsalableAnalysisByGroup} from '@/api/inventory/unsalable'
import {getProCodeBusinessStaffPlatForm} from '@/api/bookkeeper/reportday'
//getProCodeBusinessStaffPlatForm
export default {
  name: 'Roles',
  components: {container,cesTable},
   props:{
       filter: { }
     },
  data() {
    return {
      that:this,
      period:0,
      pageLoading: false,
      listLoading:false,
      goodsCode:'',
    }
  },
//  async mounted() {
// this.goodsCode=window['lastseeprcodedrchart'];
// await  this.getanalysisdata();
//   },
//   beforeUpdate() {
//   },
//  async onload(){
// this.goodsCode=window['lastseeprcodedrchart'];
// await this.getanalysisdata();
//   },
methods: {
   async getanalysisdata(goodsCode) {
        var p={goodsCode:goodsCode};
      const respr=await getProCodeBusinessStaffPlatForm(p);
      var chartDom = document.getElementById('echartturnoverranganalysis1111');
      
      var myChart = echarts.init(chartDom);
      myChart.clear();
      if (!respr.data) {
        //  this.$message({message: "没有数据!",type: "warning",});
        const dom = document.getElementById('chart');
        dom.innerHTML = '-暂无相关数据-';
        dom.style.cssText = 'text-align:center; color: #999; border: none;line-height: 300px';
        dom.removeAttribute('_echarts_instance_');
        /* chartDom.innerHTML = '暂无数据'
        dom.style.cssText = 'text-align:center; color: #999; border: none;line-height: 300px';
        dom.removeAttribute('_echarts_instance_');
          return; */
       } 
    //console.log(respr.data.series[0].data);
    var max1=Math.max.apply(Math,respr.data.series[0].data);
     var max2=Math.max.apply(Math,respr.data.series[1].data);
    var max3=Math.max.apply(Math,respr.data.series[3].data);
    var max4=Math.max.apply(Math,respr.data.series[4].data);
    var max5=Math.max.apply(Math,respr.data.series[5].data);
    var max=Math.max.apply(Math,[max1,max2,max3,max4,max5]); 
    var min1=Math.min.apply(Math,respr.data.series[0].data);
    var min2=Math.min.apply(Math,respr.data.series[1].data);
    var min3=Math.min.apply(Math,respr.data.series[3].data);
    var min4=Math.min.apply(Math,respr.data.series[4].data);
    var min5=Math.min.apply(Math,respr.data.series[5].data);
    var min=Math.min.apply(Math,[min1,min2,min3,min4,min5]);
    var minmaolilv=Math.min.apply(Math,respr.data.series[2].data); 
    //respr.data.series[2].type='line'
  // if(max<200)
    //  max=200;
  max=(max/10)*10;
  respr.data.yAxis= [{position: "left", splitLine: {show: true}, min:0,max:max,unit: "", name: "", offset: 0},{position: "right",splitLine: {show: false}, unit: "%", name: "占比(%)", offset: 0}];
      var option = this.Getoptions(respr.data);
    //option.yAxis[1].max=100;
   if(min<0)
    {
    option.yAxis[0].splitNumber=10;
    option.yAxis[1].splitNumber=10;
    var minx=Math.abs(min)/(Math.abs(min)+max);
    var yx=minx*10;
     if(minmaolilv>-Math.ceil(yx)*10)
    option.yAxis[1].min=-Math.ceil(yx)*10; 
    }
    else
    {
      option.yAxis[0].splitNumber=10;
      option.yAxis[1].splitNumber=10;
      option.yAxis[1].min=0; 
      option.yAxis[1].max=100; 
    }
    option && myChart.setOption(option);
    let that=this;
    },
    Getoptions(element){
     var series=[]
     element.series.forEach(s=>{
       series.push({smooth: true, ...s})
     })
     var yAxis=[]
     element.yAxis.forEach(s=>{
       yAxis.push({type: 'value',minInterval:10,offset:s.offset,splitLine:s.splitLine,position:s.position,name: s.name,axisLabel: {formatter: '{value}'+s.unit}})
     })
 
     var option = {
        title: {text:element.title},
        tooltip: {trigger: 'axis'},
        legend: {
            
            padding: [
                    30,  // 上
                    10, // 右
                    50,  // 下
                    10, // 左
                ],
           // top: '-10',
           height:'100px',
           selected: {
                    // 选中'系列1'
                    '商品访客数': true,
                    // 不选中'系列2'
                    '商品浏览量': false,
                    '平均停留时长': false,
                    '商品收藏人数': false,
                    '商品加购件数': false,
                    '商品加购人数': false,
                    '下单买家数': false,
                    '下单件数': false,
                    '下单金额': false,
                    '支付买家数': false,
                    '支付件数': false,
                    '支付金额': false,
                    '支付新买家数': false,
                    '支付老买家数': false,
                    '老买家支付金额': false,
                    '聚划算支付金额': false,
                    '访客平均价值': false,
                    '成功退款金额': false,
                    '竞争力评分': false,
                    '年累计支付金额': false,
                    '月累计支付金额': false,
                    '月累计支付件数': false,
                    '搜索引导访客数': false,
                    '搜索引导支付买家数': false,
                    '搜索访客占比': false,
                    '商品详情页跳出率': false,
                    '下单转化率': false,
                    '商品支付转化率': false,
                    '搜索引导支付转化率': false,
                      '总广告费': false
                   },
           data:element.legend
         },
         
        grid: {
            left: '3%',
            right: '4%',
            bottom: '13%',
           top: '25%',
            containLabel: true
        },
        toolbox: {feature: {
            magicType: {show: true, type: ['line', 'bar']},
            //restore: {show: true},
        }},
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis: yAxis,
        series:  series
    };
    return option;
   },
  }
}
</script>
<style>
.el-select-content { 
    width: calc(100% - 10px);
    margin: 0;
 }
 
</style>
