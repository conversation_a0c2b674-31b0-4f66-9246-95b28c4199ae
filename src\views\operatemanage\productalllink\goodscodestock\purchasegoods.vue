<template>
    <container v-loading="pageLoading">
        <template #header>
            <div class="top">
                <el-date-picker style="width: 260px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                    :picker-options="pickerOptions" :clearable="false" class="publicCss" />
                <inputYunhan :key="'3'" ref="childGoodsCode" v-model="filter.goodsCode" :inputt.sync="filter.goodsCode"
                    :maxlength="500" placeholder="商品编码" :clearable="true" @callback="callbackGoodsCode" title="商品编码"
                    width="150px" style="margin:0 5px 5px 0 ;">
                </inputYunhan>
                <el-select v-model="filter.isIndexNo" clearable placeholder="是否生成采购单" class="publicCss">
                    <el-option :key="'isIndexNo1'" label="未生成" :value="0" />
                    <el-option :key="'isIndexNo2'" label="已生成" :value="1" />
                </el-select>
                <el-select v-model="filter.purchaseOrderStatus" clearable placeholder="采购单状态" class="publicCss">
                    <el-option v-for="item in purOrderStatusList" :key="'purchaseOrderStatus' + item" :label="item"
                        :value="item" />
                </el-select>
                <el-select filterable v-model="filter.groupId" placeholder="运营组长" class="publicCss" clearable>
                    <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value"
                        :value="item.key" />
                </el-select>
                <el-select filterable v-model="filter.directorId" placeholder="运营" clearable class="publicCss">
                    <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
                </el-select>
                <el-select v-model="filter.brandId" filterable clearable placeholder="请选择采购员" class="publicCss">
                    <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select filterable v-model="filter.company" collapse-tags clearable placeholder="请选择分公司"
                    class="publicCss">
                    <el-option label="义乌" value="义乌" />
                    <el-option label="南昌" value="南昌" />
                    <el-option label="其他" value="其他" />
                </el-select>
                <el-select v-model="filter.deptId" clearable filterable multiple collapse-tags placeholder="请选择架构"
                    class="publicCss">
                    <el-option v-for="item in purchasegrouplist" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-select v-model="filter.titleName" class="publicCss" :clearable="true" filterable placeholder="岗位">
                    <el-option v-for="item in positionList" :key="item.titleName" :label="item.titleName"
                        :value="item.titleName" />
                </el-select>
                <el-input v-model.trim="filter.keywords" maxlength="50" type="text" clearable placeholder="请输入关键字..."
                    class="publicCss">
                    <el-tooltip slot="suffix" class="item" effect="dark" :content="keywordsTip" placement="bottom">
                        <i class="el-input__icon el-icon-question"></i>
                    </el-tooltip>
                </el-input>
                <div>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button v-if="isBrand" type="primary" @click="applyProcurement">同供应商申请采购</el-button>
                </div>
            </div>
        </template>
        <!--列表-->
        <vxetablebase :id="'purchasegoodsList202301031318001'" :isIndex='true' :tableHandles='tableHandles'
            :treeProp="{ transform: true, rowField: 'id', parentField: 'parentId' }" :tableData='list'
            :tableCols='isBrand?tableCols:tableCols2' :tablefixed='true' :loading='listLoading' :border='true' :that="that" ref="vxetable"
            @cellClick='cellclick' @sortchange='sortchange' @select="callback" :isIndexFixed="false"
            :isDisableCheckBox="false" @checCheckboxkMethod="checCheckboxkMethod"
            :tablekey="'purchasegoods202304221441'">
            <template slot="right"  v-if="isBrand" >
                <vxe-column title="操作" :field="'col_opratorcol'" width="160" fixed="right">
                    <template #default="{ row }">
                        <template v-if="row.parentId == '0'">
                            <el-button type="text" v-if='row.indexNo == null && row.isIndexNo == 0'
                                @click="handclick(row)">生成采购单信息</el-button>
                            <!-- <el-button type="text" v-if="row.indexNo == null && row.isIndexNo == 0 && checkPermission('api:Inventory:goodscodestock:SetPurchaseGoodsBrandAsync')"
                                @click="onSetDistribute(row, 1)">分配采购</el-button> -->
                            <el-button type="text"
                                v-if="row.indexNo == null && row.isIndexNo == 0 && checkPermission('api:Inventory:goodscodestock:RejectPurchaseGoods')"
                                @click="ShowRejectPurchaseGoods(row)">驳回</el-button>
                        </template>
                        <!-- <template v-else>
                            <el-button type="text"
                                v-if="row.isIndexNo != 1 && row.isReject != true && checkPermission('api:Inventory:goodscodestock:RejectPurchaseGoods')"
                                @click="ShowRejectPurchaseGoods(row)">驳回</el-button>
                        </template> -->
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <!-- 时差 -->
        <el-dialog :visible.sync="dialogVisiblecodedsearch" width="30%" v-dialogDrag>
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column prop="goodsCode" label="商品编码" width="120">
                </el-table-column>
                <el-table-column prop="purchaseTimeSpan" label="采购时长" width="100">
                </el-table-column>
                <el-table-column prop="inboundTimeSpan" label="财审时长">
                </el-table-column>
            </el-table>
        </el-dialog>

        <!-- 建采购单 -->
        <el-dialog :visible.sync="dialogAddVisible" v-dialogDrag width='50%' height='700px'>
            <el-form ref="addForm" :model="addForm" :rules="addFormRules" label-width="100px">
                <el-row :hidden="true">
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item field="id" label="id">
                            <el-input v-model="addForm.id" auto-complete="off" :disabled="true" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item field="indexNo" label="序号">
                            <el-input v-model="addForm.indexNo" auto-complete="off" :disabled="true"
                                style="width:88%" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="warehouse" label="仓库">
                            <!-- <el-select v-model="addForm.warehouse" placeholder="请选择分仓" style="width: 250px">
                                <el-option v-for="item in warehouselist" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select> -->
                            <el-select v-model="addForm.warehouse" placeholder="请选择分仓" style="width: 130px">
                                <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                                    :value="item.wms_co_id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="supplier_id" label="供应商">
                            <el-select v-model="addForm.supplier_id" ref="supplier_id" filterable remote reserve-keyword
                                placeholder="请输入供应商" :remote-method="remoteSearchSupplier" :loading="supplierLoading"
                                style="width:250px">
                                <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <div style="height: 400px;">
                            <vxe-table :data="buyNoList" height="100%" style="width: 100%;" border stripe>
                                <vxe-column title="采购单号" field="buyNo" width="130">
                                    <template slot-scope="scope">
                                        <el-tag v-if="scope.row.isFirst" type="success">{{ scope.row.buyNo
                                            }}(最近一单)</el-tag>
                                        <span v-else>{{ scope.row.buyNo }}</span>
                                    </template>
                                </vxe-column>
                                <vxe-column title="商品编码" field="goodsCode" width="120"></vxe-column>
                                <vxe-column title="供应商" field="supplier" width="200"></vxe-column>
                                <vxe-column title="进货仓" field="warehouseName" width="120"></vxe-column>
                                <vxe-column title="供应商链接" field="address1688" width="120">
                                    <template #default="{ row }">
                                        <el-link v-if="row.address1688" :href="row && row.address1688" type="primary"
                                            target="_blank">供应商链接</el-link>
                                    </template>
                                </vxe-column>
                                <vxe-column title="操作" width="100">
                                    <template slot-scope="scope">
                                        <el-button @click="handleClick(scope.row)" type="text"
                                            size="small">选择</el-button>
                                    </template>
                                </vxe-column>
                            </vxe-table>
                        </div>


                    </el-col>
                </el-row>
            </el-form>

            <template #footer>
                <span class="dialog-footer">
                    <el-link :underline="false" type="primary" @click="addsupplier"
                        style="margin-right: 20px;">添加供应商</el-link>

                    <el-button @click="dialogAddVisible = false">取 消</el-button>
                    <my-confirm-button type="submit" :validate="finishFormValidate" :loading="onFinishLoading"
                        @click="onFinish()">
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>

        <!-- 添加供应商 -->
        <el-dialog title="添加供应商" :visible.sync="dialogAddSuppVisible" :close-on-click-modal="false" append-to-body
            v-dialogDrag width='30%' height='500px'>
            <el-form ref="addSuppForm" :model="addSuppForm" :rules="addSuppFormRules" label-width="100px">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item field="name" title="供应商名称">
                            <el-input v-model="addSuppForm.name" placeholder="供应商名称" :maxlength="40"
                                clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogAddSuppVisible = false">取 消</el-button>
                    <my-confirm-button type="submit" :validate="finishSuppFormValidate" :loading="onFinishSuppLoading"
                        @click="onSave()">
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>

        <!-- 编辑手动采购单 -->
        <el-dialog title="驳回" :visible.sync="dialogAppVisible" width='60%' height='500px' v-dialogDrag>
            <el-form ref="appForm" :model="appForm" :rules="appFormRules" label-width="100px">
                <el-row>
                    <el-form-item prop="reMark" label="驳回原因">
                        <el-input type="textarea" ref="reMark" :rows="2" placeholder="请输入内容" maxlength="200"
                            v-model="appForm.reMark"></el-input>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item prop="purImageUrl" label="图片">
                            <yh-img-upload :value.sync="appForm.purImageUrl" ref="supplier_id"
                                :limit="4"></yh-img-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogAppVisible = false">取 消</el-button>
                    <my-confirm-button type="submit" :validate="finishappFormValidate" :loading="onFinishappLoading"
                        @click="RejectPurchaseGoods()">
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="同供应商申请采购" :visible.sync="applyDialogVisable" width="30%" height='500px'
            :close-on-click-modal="false" v-dialogDrag append-to-body>
            <el-form :model="ruleForm" status-icon ref="ruleForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="供应商:" prop="supplierName">
                    <el-input v-model="ruleForm.supplierName" disabled style="width: 200px;" />
                </el-form-item>
                <el-form-item label="仓库:" prop="warehouseName">
                    <el-input v-model="ruleForm.warehouseName" disabled style="width: 200px;" />
                </el-form-item>
                <el-form-item label="采购人员:" prop="brandName">
                    <el-input v-model="ruleForm.brandName" disabled style="width: 200px;" />
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="applyDialogVisable = false">取 消</el-button>
                <el-button type="primary" @click="applySubmit" v-throttle="1000">确 定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="选择供应商&仓库" :visible.sync="propsVisable" width="30%" height='500px'
            :close-on-click-modal="false" v-dialogDrag append-to-body>
            <el-form :model="ruleForm" status-icon ref="ruleForm" label-width="100px" class="demo-ruleForm"
                :rules="propsRules">
                <el-form-item prop="warehouse" label="仓库">
                    <el-select v-model="ruleForm.warehouse" placeholder="请选择分仓" style="width:250px"
                        @change="changeProps($event, '仓库')">
                        <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                            :value="item.wms_co_id" />
                    </el-select>
                </el-form-item>
                <el-form-item prop="supplierId" label="供应商">
                    <el-select v-model="ruleForm.supplierId" ref="supplier_id" filterable remote reserve-keyword
                        placeholder="请输入供应商" :remote-method="remoteSearchSupplier" :loading="supplierLoading"
                        style="width:250px" @change="changeProps($event, '供应商')">
                        <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="propsVisable = false">取 消</el-button>
                <el-button type="primary" @click="propsSubmit">确 定</el-button>
            </span>
        </el-dialog>
    </container>
</template>
<script>
import { Loading } from 'element-ui';
import { getDirectorList, getDirectorGroupList, } from '@/api/operatemanage/base/shop'
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import { batchPlanCreatePurchaseOrder } from '@/api/operatemanage/productalllink/alllink'
import inputYunhan from "@/components/Comm/inputYunhan";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { getPurchaseGoods,GetYyPurchaseGoodsAsync, generatePurchaseOrder, RejectPurchaseGoods, existsButchGeneratePurchaseOrder } from "@/api/inventory/goodscodestock"
import { TaskGetPurchaseOrder } from "@/api/inventory/purchaseordernew";
import { pageSupplierAll, getPurNameSimilarity, addSupplier } from '@/api/inventory/supplier'
import procodedetail from './procodedetail.vue'
import codedpurchase from './codedpurchase.vue'
import codedpurchasehistory from './codedpurchasehistory.vue'
import buschar from '@/components/Bus/buschar'
import { formatNoLink, warehouselist } from "@/utils/tools";
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
import { getAllWarehouse, getAllProBrand, getBianManPositionListV2 } from '@/api/inventory/warehouse'
import { getPurchaseNewPlanTurnDayDeptList } from '@/api/inventory/purchaseordernew'

const tableCols = [
    { istrue: true, type: 'checkbox' },
    { istrue: true, treeNode: true, prop: 'goodsCode', label: '系列编码/商品编码', width: '110', align: 'center', },
    { istrue: true, prop: 'goodsName', label: '产品名称', width: '130', },
    { istrue: true, prop: 'labels', label: '商品标签', width: '120' },
    { istrue: true, prop: 'picture', label: '产品图', width: '80', type: 'images', },
    { istrue: true, prop: 'createdTime', label: '生成时间', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'brandId', label: '采购人员', width: '80', formatter: (row) => row.brandName },
    { istrue: true, prop: 'company', label: '分公司', width: '80', },
    { istrue: true, prop: 'purDept', label: '架构', width: '80', },
    { istrue: true, prop: 'titleName', label: '岗位', width: '80', },
    { istrue: true, prop: 'purchaseOrderCreateTime', label: '生成采购单时间', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'purchaseOrderAuditTime', label: '采购单审核时间', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'supplier_id', label: '最近供应商', width: '120', formatter: (row) => row.supplier },
    { istrue: true, prop: 'buyNo', label: '采购单号', width: '80', },
    { istrue: true, prop: 'indexNo', label: 'ERP单号', width: '80', sortable: 'custom', },
    { istrue: true, prop: 'purchaseTimeSpan', label: '总时长', width: '120', type: 'click', formatter: (row) => (row.purchaseTimeSpan + row.inboundTimeSpan), handle: (that, row) => that.showDetail(row) },
    { istrue: true, prop: 'count', label: '数量', width: '80', sortable: 'custom', },
    { istrue: true, prop: 'isSuccess', label: '采购单状态', width: '100', formatter: (row) => row.status },
    { istrue: true, prop: 'directorId', label: '运营', width: '80', formatter: (row) => row.directorName },
    { istrue: true, prop: 'groupId', label: '运营组', width: '80', formatter: (row) => row.groupName },
    { istrue: true, prop: 'firstInboundTime', label: '首次入库时间', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'wms_co_id', label: '仓库', width: '120', formatter: (row) => row.warehouseName },
    // {
    //     istrue: true, type: "button", label: '操作', width: "160", fixed: "right", btnList: [{ label: "详情", handle: (that, row) => that.handclick(row) },
    //     { label: "生成采购单信息", handle: (that, row) => that.handsearchclick(row) }]
    // }
];
const tableCols2 = [
    { istrue: true, type: 'checkbox' },
    { istrue: true, treeNode: true, prop: 'goodsCode', label: '系列编码/商品编码', width: '110', align: 'center', },
    { istrue: true, prop: 'goodsName', label: '产品名称', width: '130', },
    { istrue: true, prop: 'labels', label: '商品标签', width: '120' },
    { istrue: true, prop: 'picture', label: '产品图', width: '80', type: 'images', },
    { istrue: true, prop: 'createdTime', label: '生成时间', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'brandId', label: '采购人员', width: '80', formatter: (row) => row.brandName },
    { istrue: true, prop: 'company', label: '分公司', width: '80', },
    { istrue: true, prop: 'purDept', label: '架构', width: '80', },
    { istrue: true, prop: 'titleName', label: '岗位', width: '80', },
    { istrue: true, prop: 'purchaseOrderCreateTime', label: '生成采购单时间', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'purchaseOrderAuditTime', label: '采购单审核时间', width: '120', sortable: 'custom', },
    //{ istrue: true, prop: 'supplier_id', label: '最近供应商', width: '120', formatter: (row) => row.supplier },
    { istrue: true, prop: 'buyNo', label: '采购单号', width: '80', },
    { istrue: true, prop: 'indexNo', label: 'ERP单号', width: '80', sortable: 'custom', },
    { istrue: true, prop: 'purchaseTimeSpan', label: '总时长', width: '120', type: 'click', formatter: (row) => (row.purchaseTimeSpan + row.inboundTimeSpan), handle: (that, row) => that.showDetail(row) },
    { istrue: true, prop: 'count', label: '数量', width: '80', sortable: 'custom', },
    { istrue: true, prop: 'isSuccess', label: '采购单状态', width: '100', formatter: (row) => row.status },
    { istrue: true, prop: 'directorId', label: '运营', width: '80', formatter: (row) => row.directorName },
    { istrue: true, prop: 'groupId', label: '运营组', width: '80', formatter: (row) => row.groupName },
    { istrue: true, prop: 'firstInboundTime', label: '首次入库时间', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'wms_co_id', label: '仓库', width: '120', formatter: (row) => row.warehouseName },
    // {
    //     istrue: true, type: "button", label: '操作', width: "160", fixed: "right", btnList: [{ label: "详情", handle: (that, row) => that.handclick(row) },
    //     { label: "生成采购单信息", handle: (that, row) => that.handsearchclick(row) }]
    // }
];
const tableHandles = [
];

const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunHanAdminPurchasegoods',
    components: { container, MyConfirmButton, vxetablebase, inputYunhan, procodedetail, codedpurchase, codedpurchasehistory, buschar, YhImgUpload },
    props: {
        isBrand: { type: Boolean, default: true },       
    },
    data() {
        return {
            that: this,
            activeName: 'tab1',
            filter: {
                startDate: null,
                endDate: null,
                goodsCode: null,
                keywords: null,
                groupId: null,
                directorId: null,
                purchaseOrderStatus: null,
                brandId: null,
                company: null,
                titleName: null,
                deptId: [],
                timerange: [startTime, endTime],
                isIndexNo: 0
            },
            filterdetail: {
                goodsCode: null,
                startDate: null,
                endDate: null
            },
            //warehouselist: warehouselist,
            warehouselist: [],
            tableData: [],
            list: [],
            tableCols:  tableCols,
            tableCols2:  tableCols2,
            tableHandles: tableHandles,
            pager: { OrderBy: "goodsCode", IsAsc: false },
            total: 0,
            sels: [],
            rejectSels: [],
            selectIds: [],
            brandlist: [],
            positionList: [],
            purchasegrouplist: [],
            supplierOptions: [],
            buyNoList: [],
            directorList: [],
            directorGroupList: [],
            addForm: {
                id: 0,
                warehouse: null,
                supplier_id: null,
                supplier: '',
            },
            addSuppForm: {
                name: null
            },
            addFormRules: {
                warehouse: [{ required: true, message: '请输入仓库', trigger: 'blur' }],
                supplier_id: [{ required: true, message: '请输入供应商', trigger: 'blur' }],
            },
            addSuppFormRules: {
                name: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }],
            },
            appForm: {
                id: null,
                reMark: null,
                purImageUrl: null,
            },
            appFormRules: {
                reMark: [{ required: true, message: '请输入驳回原因', trigger: 'blur' }],
            },
            keywordsTip: '支持搜索的内容：采购单号、Erp单号',
            purOrderStatusList: ['待审核', '已确认', '作废', '完成',],
            timeType: 0, // 时间类型 0：付款维度，1：发货维度
            timeNum: null,
            goodsCode: null,
            listLoading: false,
            pageLoading: false,
            uploadLoading: false,
            dialogAddVisible: false,
            supplierLoading: false,
            dialogAddSuppVisible: false,
            onFinishSuppLoading: false,
            dialogVisibleimport: false,
            dialogVisiblecodedpurchase: false,
            dialogVisiblecodedsearch: false,
            dialogAppVisible: false,
            onFinishappLoading: false,
            onFinishLoading: false,
            dialogVisible: false, buscharDialog: { visible: false, title: "", data: [] },
            pickerOptions: {
                onPick: ({ maxDate, minDate }) => {
                    this.selectDate = minDate.getTime()
                    if (maxDate) {
                        this.selectDate = ''
                    }
                },
                disabledDate: (time) => {
                    if (this.selectDate !== '') {
                        const one = 30 * 24 * 3600 * 1000
                        const minTime = this.selectDate - one
                        const maxTime = this.selectDate + one
                        return time.getTime() < minTime || time.getTime() > maxTime
                    }
                }
            },
            applyProcurementList: [],
            applyDialogVisable: false,
            ruleForm: {
                supplierName: '',//供应商
                supplierId: '',//供应商id
                warehouse: '',//仓库 
                warehouseName: '',//仓库名称
                brandId: '',//采购员
                brandName: '',//采购员名称
                purchaseGoodsIds: [],//采购计划id
            },
            propsVisable: false,
            propsRules: {
                warehouse: [{ required: true, message: '请选择仓库', trigger: 'blur' }],
                supplierId: [{ required: true, message: '请选择供应商', trigger: 'blur' }],
            },
        };
    },

    async mounted() {
        await this.onSearch();
        await this.getDirectorlist()
    },

    methods: {
        propsSubmit() {
            if (!this.ruleForm.warehouse) return this.$message.error('请选择仓库');
            if (!this.ruleForm.supplierId) return this.$message.error('请选择供应商');
            this.ruleForm.purchaseGoodsIds = this.applyProcurementList.map(item => item.id);
            this.propsVisable = false;
            this.applyDialogVisable = true;
        },
        changeProps(e, type) {
            if (type == '仓库') {
                this.ruleForm.warehouseName = e ? this.warehouselist.find(x => x.wms_co_id == e).name : '';
            } else {
                this.ruleForm.supplierName = e ? this.supplierOptions.find(x => x.value == e).label : '';
            }
        },
        async applySubmit() {
            const { success } = await existsButchGeneratePurchaseOrder(this.ruleForm);
            if (!success) return this.$message.error('申请采购失败');
            this.applyDialogVisable = false;
            await this.onSearch();
            this.$message.success('申请采购成功');
        },
        applyProcurement() {
            if (this.applyProcurementList.length == 0) {
                return this.$message.error('请选择需要申请采购的数据');
            }
            //清空供应商
            this.supplierOptions = []
            this.ruleForm = {
                supplierName: '',//供应商
                supplierId: '',//供应商id
                warehouse: '',//仓库 
                warehouseName: '',//仓库名称
                brandId: '',//采购员
                brandName: '',//采购员名称
                purchaseGoodsIds: [],//采购计划id
            }
            let brandName = this.applyProcurementList[0].brandName;
            let brandId = this.applyProcurementList[0].brandId;
            this.applyProcurementList.forEach(item => {
                if (!item.brandId) {
                    this.$message.error('选中数据中存在未分配采购员的数据，请分配后再操作！');
                    throw new Error('选中数据中存在未分配采购员的数据，请分配后再操作！');
                }
                if(item.purchaseOrderCreateTime){
                    this.$message.error('选中数据中存在已生成采购单的数据，请重新选择！');
                    throw new Error('选中数据中存在已生成采购单的数据，请重新选择！');
                }
            })
            let flag = this.applyProcurementList.every(item => {
                return item.brandId == brandId && brandName == item.brandName;
            });
            if (!flag) {
                return this.$message.error('请选择同一采购员的数据');
            }
            this.ruleForm.brandName = brandName;
            this.ruleForm.brandId = brandId;
            this.propsVisable = true
        },
        async getDirectorlist() {
            const res1 = await getDirectorList({})
            const res2 = await getDirectorGroupList({})

            this.directorList = res1.data
            this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);

            var res3 = await getAllWarehouse();
            var warehouselist1 = res3.data.filter((x) => x.name.indexOf('代发') < 0);
            warehouselist.unshift({ name: "全仓", co_id: 10361546, wms_co_id: 11793337, is_main: false, remark1: null, remark2: null, wms_co_id: -1 });
            this.warehouselist = warehouselist1;
            //采购员
            var res3 = await getAllProBrand();
            this.brandlist = res3.data.map(item => {
                return { value: item.key, label: item.value };
            });
            //架构
            let { data: deptList, success } = await getPurchaseNewPlanTurnDayDeptList();
            if (success) {
                this.purchasegrouplist = deptList.map(item => { return { value: item.dept_id, label: item.full_name }; });
            }
            //岗位
            var resPosition = await getBianManPositionListV2();
            this.positionList = resPosition?.data;
        },
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = this.isBrand? await getPurchaseGoods(params) :await GetYyPurchaseGoodsAsync(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.total = res.data.total;
            let dtList = [];
            console.log('原始数据', res.data.list)
            res.data.list.forEach(x => {
                dtList.push(x);
                if (x.dtList && x.dtList.length > 0) {
                    dtList = dtList.concat([...x.dtList]);
                }
            })
            this.list = dtList
            console.log('数据数据', dtList)
        },
        async callbackGoodsCode(val) {
            // this.inputedit = true;
            this.filter.goodsCode = val;
            this.onSearch();
        },
        async handclick(row) {
            let id = row.id;
            if (this.sels.length <= 0) {
                this.$message.error('请选择商品编码！');
                return;
            }
            let selectIds = [];
            let goodsCodes = [];
            let suppliers = [];
            let styleCode = "";
            let allStyleCodeValidate = true;
            this.sels.forEach(f => {
                if (styleCode == "") {
                    styleCode = f.styleCode;
                } else {
                    if (styleCode != f.styleCode) {
                        allStyleCodeValidate = false;
                    }
                }
                if (f.parentId == id && f.isParent == false) {
                    selectIds.push(f.id);
                    goodsCodes.push(f.goodsCode);
                    suppliers.push({ supplier_id: f.supplier_id, supplier: f.supplier, warehouse: f.warehouse })
                }
            })
            if (selectIds.length <= 0) {
                this.$message.error('请选择商品编码！');
                return;
            }
            if (!allStyleCodeValidate) {
                this.$message.error('请选择同一系列下的编码');
                return;
            }
            this.selectIds = [];
            this.selectIds = selectIds;
            // if (row.brandId == '' || row.brandId == null || row.brandId == 0) {
            //   this.$message({ message: '当前单据无采购员，请分配采购后发起申请！', type: "warning" });
            // return false;
            // }


            var buyNoInfo = await TaskGetPurchaseOrder({ goodsCode: goodsCodes.join() });
            if (!buyNoInfo?.success) return
            this.dialogAddVisible = true;
            this.buyNoList = buyNoInfo.data;
            this.addForm.warehouse = suppliers[0]?.warehouse;
            this.addForm.supplier_id = null;
            this.supplierOptions = [];
            console.log('id', suppliers)

            for (let i = 0; i < suppliers.length; i++) {
                if (suppliers[i].supplier_id != null && suppliers[i].supplier_id !== 0 && suppliers[i].supplier != null && suppliers[i].supplier !== '') {
                    this.supplierOptions = [
                        {
                            value: suppliers[i].supplier_id,
                            label: suppliers[i].supplier
                        }
                    ];
                    this.addForm.supplier_id = suppliers[i].supplier_id;
                    break; // 退出循环
                }
            }
        },
        async onFinish() {
            var _this = this;
            _this.onFinishLoading = true;
            var para = { ids: _this.selectIds, ..._this.addForm };
            let obj = {};
            obj = _this.supplierOptions.find((item) => {
                return item.value === para.supplier_id; //筛选出匹配的数据
            });
            para.supplier = obj.label;
            var res = await generatePurchaseOrder(para);
            _this.onFinishLoading = false;
            if (res?.success) {
                _this.$message({ message: '发起生成采购单信息成功，正在生成中……', type: "success" });
                _this.dialogAddVisible = false;
                await _this.getlist();
                _this.list.forEach(f => {
                    if (f.id == row.id) {
                        f.isIndexNo = 1
                        f.dtList.forEach(d => {
                            if (_this.sels.some(sel => sel.id === d.id)) {
                                d.isIndexNo = 1
                                d.isReject = true
                            }
                        })
                    }

                })
            } else {
                //this.$message({ message: '生成失败，请刷新界面后重试！', type: "warning" });
            }
        },
        //绑定供应商选择
        async remoteSearchSupplier(parm) {
            this.supplierOptions = [];
            if (!parm) {
                return;
            }
            var options = [];
            const res = await pageSupplierAll({ currentPage: 1, pageSize: 50, name: parm });
            res?.data?.list.forEach(f => {
                options.push({ value: f.supplier_id, label: f.name })
            });
            this.supplierOptions = options;
        },
        onSetDistribute(row, mode) {
            let id = row.id;
            if (this.sels.length <= 0) {
                this.$message.error('请选择商品编码！');
                return;
            }
            let selectIds = [];
            let goodsCodes = [];
            this.sels.forEach(f => {
                if (f.parentId == id && f.isParent == false) {
                    selectIds.push(f.id);
                    if (goodsCodes.find(x => x == f.goodsCode) == null) {
                        goodsCodes.push(f.goodsCode);
                    }
                }
            })
            if (selectIds.length <= 0) {
                this.$message.error('请选择商品编码！');
                return;
            }
            let self = this;
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/goodscodestock/setpurchasegoodsbrand.vue`,
                args: { ...row, ids: selectIds, styleCode: row.goodsCode, goodsCode: goodsCodes, brandId: null },
                title: "分配采购",
                width: '400px',
                height: 400,
                callOk: () => {
                    self.onRefresh();
                }
            });
        },
        onRefresh() {
            this.onSearch()
        },
        async cellclick({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }) {
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        callback(val) {
            this.sels = [];
            val.forEach(f => {
                let ob = new Object();
                ob.id = f.id;
                ob.parentId = f.parentId == 0 ? f : f.parentId;
                ob.isParent = f.parentId == 0 ? true : false;
                ob.goodsCode = f.goodsCode;
                ob.styleCode = f.parentId == 0 ? f.goodsCode : f.styleCode;
                ob.supplier = f.supplier;
                ob.supplier_id = f.supplier_id;
                ob.warehouse = f.warehouse;
                ob.isIndex = f.isIndex;
                this.sels.push(ob);
            })
            this.applyProcurementList = val.filter(item => item.parentId != 0);
        },
        checCheckboxkMethod: function ({ row }, callback) {

            callback((row.indexNo == "" || row.indexNo == null) ? true : false);
        },
        showProfit3RateChat: function (goodsCode) {

        },
        async ShowRejectPurchaseGoods(row) {
            let id = row.id;
            if (this.sels.length <= 0) {
                this.$message.error('请选择商品编码！');
                return;
            }
            let selectIds = [];
            let goodsCodes = [];
            let suppliers = [];
            let styleCode = "";
            this.sels.forEach(f => {
                if (styleCode == "") {
                    styleCode = f.styleCode;
                } else {
                    if (styleCode != f.styleCode) {

                    }
                }
                if (f.parentId == id && f.isParent == false) {
                    selectIds.push(f.id);
                }
            })
            if (selectIds.length <= 0) {
                this.$message.error('请选择商品编码！');
                return;
            }
            var ids = [];
            this.sels.forEach(f => {
                if (f.isIndexNo == 1) {
                    this.$message({ message: '存在已生成采购的单据，禁止操作，请刷新界面后重试', type: "warning" });
                    return;
                }
                ids.push(f.id);
            })
            this.appForm = {
                id: null,
                reMark: null,
                purImageUrl: null,
            };
            this.appForm.ids = ids.join();
            this.dialogAppVisible = true;
        },
        //新增采购单时提交验证
        finishappFormValidate: function () {
            let isValid = false
            this.$refs.appForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        // 驳回
        async RejectPurchaseGoods() {
            var para = { ...this.appForm };
            var res = await RejectPurchaseGoods(para);
            if (res?.success) {
                this.$message({ message: '操作成功', type: "success" });
                this.dialogAppVisible = false;
                //await this.getlist();
                this.list.forEach(f => {
                    if (this.sels.some(item => item.id === f.id)) {
                        f.isIndexNo = 1;
                        f.isReject = true;
                    }
                });
                return true;
            } else {
                //this.$message({ message: '生成失败，请刷新界面后重试！', type: "warning" });
                return false;
            }
        },
        //新增采购单时提交验证
        finishFormValidate: function () {
            let isValid = false
            this.$refs.addForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        //新增采购单时提交验证
        finishSuppFormValidate: function () {
            let isValid = false
            this.$refs.addSuppForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        async showDetail(row) {
            this.dialogVisiblecodedsearch = true;
            this.tableData = [];
            this.tableData.push(row);
        },
        async addsupplier() {
            var _this = this;
            _this.addSuppForm.name = null;
            _this.dialogAddSuppVisible = true;
        },
        configSave() {
            let that = this;
            that.$confirm('确定保存吗, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                .then(() => {
                    addSupplier(that.addSuppForm)
                        .then(function (res) {
                            if (!res?.success) {
                                that.$message({ type: 'warn', message: '保存失败' });
                                that.onFinishSuppLoading = false;
                                that.dialogAddSuppVisible = false;
                            }
                            else {
                                that.$message({ type: 'success', message: '保存成功' });
                                that.onFinishSuppLoading = false;
                                that.dialogAddSuppVisible = false;
                                that.$emit("addcallback", res.data);
                            }
                        })
                        .catch(function (err) { console.error(err); });
                }).catch(() => {
                    that.$message({ type: 'info', message: '已取消' });
                    that.onFinishSuppLoading = false;
                });
        },
        async onSave() {
            var _this = this;
            if (!_this.addSuppForm.name || _this.addSuppForm.name == '') {
                _this.$message({ type: 'error', message: '请输入供应商名称！' });
                return;
            }
            //校验名称是否有相似的
            _this.onFinishSuppLoading = true;
            await getPurNameSimilarity({ supplierName: _this.addSuppForm.name }).then(function (res) {

                if (res?.success && res.data) {
                    _this.$confirm("存在相似的供应商名：" + res.data + ",是否继续添加？", '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                        .then(function () {
                            _this.configSave();

                        })
                        .catch(() => {
                            _this.onFinishSuppLoading = false;
                            //取消操作
                            return;
                        });
                } else if (!res?.success && (res?.data == null || res?.data == "")) {
                    _this.onFinishSuppLoading = false;
                    //取消操作
                    return;
                } else {
                    _this.configSave();
                }
            });
        },
        async handleClick(row) {
            let loadingInstance = Loading.service();
            Loading.service({ fullscreen: true });
            var res = await this.remoteSearchSupplierasync({ supplier_id: row.supplier_id, name: row.supplier });
            if (res == false) {
                this.$message.error("温馨提示：此供应商信息历史数据被更改，请手动输入供应商！");
                loadingInstance.close();
                return;
            }
            this.addForm.supplier_id = row.supplier_id;
            loadingInstance.close();
        },
        async remoteSearchSupplierasync(parm) {
            this.supplierOptions = [];
            if (!parm) {
                return;
            }
            var options = [];
            const res = await pageSupplierAll({ currentPage: 1, pageSize: 50, name: parm.name });
            if (res?.data.total == 0) {
                this.addForm.supplier_id = null;
                return false;
            }
            var data = res?.data?.list;
            for (var i = 0; i < data?.length; i++) {
                if (data[i].supplier_id != parm.supplier_id) {
                    this.addForm.supplier_id = null;
                    return false;
                }
                options.push({ value: data[i].supplier_id, label: data[i].name })
            }
            this.supplierOptions = options;
            return true;
        },
    },
};
</script>

<style lang="scss" scoped>
.relativebox {
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    -webkit-box-orient: vertical;
}

//控制选择器多选-标签宽度
::v-deep .el-select__tags-text {
    max-width: 30px;
}

.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;

    .publicCss {
        width: 150px;
        margin: 0 5px 5px 0;
    }
}
</style>
