<template>
    <!-- <my-container id="style" style="height: 100%;" v-loading="pageLoading"> -->
        <my-container id="style" :style="{height: height, backgroundColor: backgroundColor}" v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;border: 0">
                    <!-- <el-select v-model="warehouses" multiple clearable filterable :collapse-tags="true"
                        placeholder="请选择分仓" style="width: 150px">
                        <el-option label="全仓" :value="-1" />
                        <el-option v-for="item in warehouselist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select> -->
                    <!-- <el-select v-model="warehouses" multiple clearable filterable :collapse-tags="true" placeholder="请选择仓库" style="width: 130px">
                        <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                            :value="item.wms_co_id" />
                    </el-select> -->
                    <YhWarehouseSelector :names="['全仓']" :values="[-1]" :checkboxOrRadio="'checkbox'" :addAllWarehouseConcept="true" @onChange="(v)=>{warehouses=v[0]; }">
                    </YhWarehouseSelector>
                </el-button>

                <el-button style="padding: 0;margin-left: 10px;border: 0">
                    <el-select v-model="brandIds" multiple clearable filterable placeholder="采购员" style="width: 130px" :collapse-tags="true">
                        <el-option v-for="item in brandlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 10px;border: 0">
                    <el-tooltip class="item" effect="dark" content="按住alt+单击=批量输入" placement="top">
                        <el-input v-model.trim="filter.goodsCode" id="goodsCode11" @focus="onpurchseInfo"  maxlength="100" placeholder="采购信息（按住ctrl+单击=直接输入）" @clear="purchaseClear"
                        style="width: 230px" clearable />
                    </el-tooltip>

                </el-button>
                <!-- <el-form-item label="系列编码:">
                    <el-input v-model.trim="filter.styleCode" maxlength="100" placeholder="系列编码" style="width: 150px"
                        @keyup.enter.native="onSearch" clearable />
                </el-form-item> -->
                <!-- <el-form-item label="商品名称:">
                    <el-input v-model.trim="filter.goodsName" maxlength="100" placeholder="商品名称" style="width: 150px"
                        @keyup.enter.native="onSearch" clearable />
                </el-form-item> -->
                <el-button style="padding: 0;margin-left: 10px;border: 0">
                    <el-select v-model="filter.groupId" clearable filterable placeholder="商品分类" style="width: 110px">
                        <el-option label="所有" value="" />
                        <el-option v-for="item in grouplist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 10px;border: 0">
                    <el-select v-model="filter.isEnabled" clearable filterable placeholder="状态" style="width: 90px">
                        <el-option label="备用" value="0" />
                        <el-option label="启用" value="1" />
                        <el-option label="禁用" value="-1" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 10px;border: 0">
                    <el-button-group>
                        <el-button style="padding: 0;margin: 0;border: 0">
                            <el-input-number placeholder="原建议采购数" :min=0 :max=9999999 v-model="filter.minPurchasePlanCount"
                                style="width: 110px" @keyup.enter.native="onSearch"></el-input-number>
                        </el-button>
                        <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;border: 0">至</el-button>
                        <el-button style="padding: 0;margin: 0;border: 0">
                            <el-input-number placeholder="原建议采购数"  :min=0 :max=9999999 v-model="filter.maxPurchasePlanCount"
                                style="width: 110px" @keyup.enter.native="onSearch"></el-input-number>
                        </el-button>
                    </el-button-group>
                </el-button>
                <el-button style="padding: 0;margin-left: 10px;border: 0">
                    <el-button-group>
                        <el-button style="padding: 0;margin: 0;border: 0">
                            <el-input-number placeholder="新建议采购数" :min=0 :max=9999999 v-model="filter.minPurchasePlanCount3"
                                style="width: 110px" @keyup.enter.native="onSearch"></el-input-number>
                        </el-button>
                        <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;border: 0">至</el-button>
                        <el-button style="padding: 0;margin: 0;border: 0">
                            <el-input-number placeholder="新建议采购数"  :min=0 :max=9999999 v-model="filter.maxPurchasePlanCount3"
                                style="width: 110px" @keyup.enter.native="onSearch"></el-input-number>
                        </el-button>
                    </el-button-group>
                </el-button>
                <el-button style="padding: 0;margin-left: 10px;border: 0">
                    <el-button-group>
                        <el-button style="padding: 0;margin: 10;border: 0">
                            <el-input-number placeholder="周库存转天数" :min=0 :max=9999999 v-model="filter.minTurnoverDay"
                                style="width: 110px" @keyup.enter.native="onSearch"></el-input-number>
                        </el-button>
                        <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;border: 0">至</el-button>
                        <el-button style="padding: 0;margin: 0;border: 0">
                            <el-input-number placeholder="周库存转天数"  :min=0 :max=9999999 v-model="filter.maxTurnoverDay"
                                style="width: 110px" @keyup.enter.native="onSearch"></el-input-number>
                        </el-button>
                    </el-button-group>
                </el-button>
                <el-button style="padding: 0;margin-left: 10px;border: 0">
                    <el-input v-model="filter.firstUserName" placeholder="开款人" maxlength="20" style="width:80px ;" />
                </el-button>
                <el-button style="padding: 0;margin-left: 5px;margin-right: 5px;border: 0">
                    <!-- <el-switch
                    v-model="filter.markerColor"
                    active-text="标黄"
                    inactive-text="不标黄">
                    </el-switch> -->
                    <el-select v-model="filter.markerColor" filterable placeholder="状态" style="width: 90px;">
                        <el-option label="全部" value="0" />
                        <el-option label="标黄" value="1" />
                        <el-option label="不标黄" value="2" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;border: 0">
                    <el-select v-model="filter.virtualType" placeholder="虚拟分类" filterable clearable style="width: 140px;">
                        <el-option v-for="item in virtualTypeList" :key="item" :label="item" :value="item"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 5px;border: 0">
                    <el-button type="primary" v-throttle="3000" @click="onSearch">查询</el-button>
                </el-button>
            </el-button-group>
        </template>

        <div ref="btns" style="height: 30px">
            <el-button-group>
                <template v-for='item in tableHandles'>
                    <el-button v-if="(!item.permission || (item.permission && checkPermission(item.permission)))"
                        :key="item.label" :type="item.type" @click="item.handle(that)">{{ item.label }}</el-button>
                </template>

                <el-dropdown @command="handleCommand">
                    <el-button type="primary">
                        导出方式<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown" >
                        <el-dropdown-item command="a">根据勾选导出Excel</el-dropdown-item>
                        <el-dropdown-item command="b">根据搜索结果导出excel</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
                <slot name="extentbtn" />
            </el-button-group>
            <el-button-group>
                <el-button type="primary" v-throttle="3000" @click="onSearch">刷新</el-button>
                <el-button type="primary" v-throttle="3000" @click="settingFuc" v-if="checkPermission('caigou:manager:suggestion')">配置</el-button>
                <el-button style="margin: 0;" @click="getimportlist">
                    {{lastUpdateTime}}
                </el-button>
          </el-button-group>
        </div>
        <div style="height: 95%">
            <vxe-toolbar ref="xToolbar" custom class="vxetoolbar20221212">
                <template #buttons>
                    <slot name="tbHeader" />
                </template>
            </vxe-toolbar>
            <vxe-table ref="table" :data="list" row-key="id" border :row-config="{height: 140}" style="width: 100%" height="100%" @sort-change='sortchange'
                :show-summary="true" :summary-method="getSummaries" id="hotsalegoodsbuildgoodsdoclist20221212" :column-config="{resizable: true,maxFixedSize:10}" :show-header-overflow="true"
                v-loading="listLoading"  show-overflow @checkbox-change="selectChangeEvent" @checkbox-all="selectAll" :checkbox-config="{labelField: '', highlight: true, range: true, checkMethod: checCheckboxkMethod,}"
                :edit-config="{trigger: 'dblclick', mode: 'cell'}" :custom-config="customConfig">
                <vxe-column title="#" type="seq" :align="'center'" width="30"></vxe-column>
                <vxe-column type="checkbox" :align="'center'" width="40" id="columnselection"></vxe-column>
                <vxe-column title="图片" field="images" :show-overflow="true" width="55" :align="'center'">
                    <teleport slot-scope="scope">
                        <el-image :src="scope.row.images" @click="showImg(scope.row.images)" class="imgcss"
                            style="margin-left: -10px;" fit="fill" :lazy="true"></el-image>
                    </teleport>
                </vxe-column>
                <!-- <vxe-column title="分仓" field="warehouse" width="100" sortable :show-overflow="true">
                    <template slot-scope="scope">
                        {{ scope.row.warehouse | myWarehouse}}
                    </template>
                </vxe-column> -->
                <vxe-column title="商品信息" field="styleCode" width="200" sortable :show-overflow="true">
                    <template #header>
                        <span class="grid-header">
                            <span>
                                <el-tooltip class="item" effect="dark" content="商品信息，款式编码，分仓" placement="top-end">
                                    <span><i class="el-icon-question"></i></span>
                                </el-tooltip>
                            </span>
                            <span>商品信息</span>
                        </span>
                    </template>
                    <template slot-scope="scope">
                        <div class="divline">
                            <span style="color: #757575;" class="linebreak">{{ scope.row.goodsName }}</span>
                        </div>
                        <div>
                            <span style="font-size: 15px;color: black;">{{ scope.row.styleCode }}</span>
                        </div>
                        <div>
                            <span style="color: #757575;">{{ scope.row.warehouseName }}</span>
                        </div>
                        <div>
                            <span style="color: #757575;">{{ scope.row.virtualType }}</span>
                        </div>
                    </template>
                </vxe-column>
                <vxe-column title="商品编码" field="goodsCode" width="100" sortable :show-overflow="true">
                    <template slot-scope="scope">
                        <div>
                            <a @click="handleLinkClick(scope.row, { prop: 'goodsCode', label: '商品编码' })" style="color: blue; cursor: pointer;" class="linebreak"> {{ scope.row.goodsCode }} </a><br/>
                            <span class="linebreak" v-if="scope.row.isNotStock == 1" style="color:red;">暂不支持进货！</span>
                        </div>
                    </template>
                </vxe-column>
                <vxe-column title="商品标签" field="goodsLable"  width="90" sortable :show-overflow="true">
                    <template slot-scope="scope">
                        <div>
                            <span style="color: #757575;" class="linebreak">{{ scope.row.goodsLable }}</span>
                        </div>
                    </template>
                </vxe-column>
                <vxe-column title="开款人" field="firstUserName"  width="90" sortable :show-overflow="true">
                </vxe-column>
                <vxe-column title="商品分类" field="groupId" width="135" :show-overflow="true">
                    <template #header>
                        <div class="table-div" ref="tableColumn4">
                            <div v-for="(todo, index) in searchColumn4" :key="index" v-bind:class="{
                                ascending: (index == searchIndex && searchOrder == 'ascending'),
                                descending: (index == searchIndex && searchOrder == 'descending')
                            }" @click="tableColumnClick(todo.value, index, 'tableColumn4',4)" style="display: flex; flex-direction: row; justify-content: center; line-height: 25px;">
                                <span class="grid-header">
                                    <span>
                                        <el-tooltip class="item" effect="dark" :content="todo.tooltip"
                                            placement="top-end">
                                            <span><i class="el-icon-question"></i></span>
                                        </el-tooltip>
                                    </span>
                                    <span>{{ todo.text }}</span>
                                </span>
                                <!-- <span class="caret-wrapper">
                                    <i class="sort-caret ascending" color="red"></i>
                                    <i class="sort-caret descending" color="green"></i>
                                </span> -->
                                <span class="caret-wrapper" style="margin-left: 5px;font-size: 16px;align-self: center; display: flex; flex-direction: column;">
                                    <i class="el-icon-caret-top" :class="[(index == searchIndex && searchOrder == 'ascending' && wrappername == 4)?'iconcolor':'startcol']"></i>
                                    <i class="el-icon-caret-bottom" style="margin-top: -10px;" :class="[(index == searchIndex && searchOrder == 'descending' && wrappername == 4)?'iconcolor':'startcol']"></i>
                                </span>
                            </div>
                        </div>
                    </template>
                    <teleport slot-scope="scope">
                        <div>
                            {{ scope.row.groupName }}
                        </div>
                        <br />
                        <div>
                            {{ scope.row.maxRatioGroupName }}
                        </div>
                    </teleport>
                </vxe-column>

                <vxe-column title="品牌" field="brandId"  width="70" sortable :show-overflow="true">
                    <template slot-scope="scope">
                        <div>
                            {{ scope.row.brandName == null ? '' : scope.row.brandName }}
                        </div>
                    </template>
                </vxe-column>
                <!-- <vxe-column title="商品备注" field="goodsRemark"  width="60">

                </vxe-column> -->
                <vxe-column field="goodsRemark" title="商品备注" :edit-render="{}" width="120">
                    <template #edit="{ row }">
                        <div class="linebreak">
                            <vxe-textarea v-model.trim="row.goodsRemark" type="text" placeholder="请输入商品备注" :maxlength="1000" @blur="updateRemark(row)"></vxe-textarea>
                        </div>
                    </template>
                </vxe-column>
                <vxe-column title="建议采购信息" width="180" field="purchasePlanCount3" sortable>
                    <teleport slot-scope="scope">
                        <div>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span style="color: red;">{{ scope.row.purchasePlanCount3 }}({{ scope.row.packCount }})</span>
                        </div>
                        <!-- //匡注释 -->
                        <!-- <div>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span style="color: red;">{{ scope.row.purchasePlanCount }}({{ scope.row.packCount }})</span>
                        </div> -->
                        <div style="display: flex; flex-direction: row;">
                            <div>
                                ￥{{ scope.row.cost }} *
                            </div>
                            <inputnumberYunhan v-if="isRouterShow" ref="inputnumberYunhan" :scoperow="scope.row" :disabled="scope.row.isNotStock == 1"
                                @changesafeDay="changesafeDay"></inputnumberYunhan>
                            <div>
                                ={{ (scope.row.purchasePlanCount3 * scope.row.cost).toFixed(1) }}
                            </div>

                        </div>
                        <div style="margin-top: 5px;display: flex;flex-flow: row;justify-content: center;">
                            <el-input-number v-model="scope.row.packNum" controls-position="right" :precision="1" :disabled="!scope.row.packCount"  @blur="handlePackCountChange(scope.row.packNum,scope.row)"
                                style="width: 50px;" :controls="false" :min="0" :max="999999999" size="mini"></el-input-number>
                            <span>箱</span>
                            <!-- {{scope.row.packNum}} -->
                            <!-- {{ scope.row.packCount == 0 ? scope.row.purchasePlanCount3 + '件' : Math.floor(scope.row.purchasePlanCount3 / scope.row.packCount) == 0 ?
                                scope.row.purchasePlanCount3 + '件' : Math.floor(scope.row.purchasePlanCount3 / scope.row.packCount) + '箱' +
                                ((scope.row.purchasePlanCount3 % scope.row.packCount) == 0 ? '' : (scope.row.purchasePlanCount3 % scope.row.packCount) + '件')
                            }} -->
                        </div>
                    </teleport>
                </vxe-column>
                <vxe-column title="库存周转天数" field="inventoryDay" column-key="inventoryDay" width="135" :show-overflow="true">
                    <template #header>
                        <div class="table-div" ref="tableColumn2">
                            <div v-for="(todo, index) in searchColumn2" :key="index" v-bind:class="{
                                ascending: (index == searchIndex && searchOrder == 'ascending'),
                                descending: (index == searchIndex && searchOrder == 'descending')
                            }" @click="tableColumnClick(todo.value, index, 'tableColumn2',2)" style="display: flex; flex-direction: row; justify-content: center; line-height: 25px;">
                                <span class="grid-header">
                                    <span>
                                        <el-tooltip class="item" effect="dark" :content="todo.tooltip"
                                            placement="top-end">
                                            <span><i class="el-icon-question"></i></span>
                                        </el-tooltip>
                                    </span>
                                    <span>{{ todo.text }}</span>
                                </span>
                                <!-- <span class="caret-wrapper">
                                    <i class="sort-caret ascending" color="red"></i>
                                    <i class="sort-caret descending" color="green"></i>
                                </span> -->
                                <span class="caret-wrapper" style="margin-left: 5px;font-size: 16px;align-self: center; display: flex; flex-direction: column;">
                                    <i class="el-icon-caret-top" :class="[(index == searchIndex && searchOrder == 'ascending' && wrappername == 2)?'iconcolor':'startcol']"></i>
                                    <i class="el-icon-caret-bottom" style="margin-top: -10px;" :class="[(index == searchIndex && searchOrder == 'descending' && wrappername == 2)?'iconcolor':'startcol']"></i>
                                </span>
                            </div>
                        </div>
                    </template>
                    <teleport slot-scope="scope">
                        <div :style="scope.row.markerColor == 1?{backgroundColor: 'yellow'}:{}">
                            {{ scope.row.inventoryDay }}
                        </div>
                        <br />
                        <div style="color: blue;">
                            {{ scope.row.purchaseDay }}
                        </div>
                    </teleport>
                </vxe-column>
                <vxe-column title="剩余库存可用天数" field="remainingAvailableDays" width="140" :show-overflow="true">
                </vxe-column>
                <vxe-column title="主仓库存" field="masterStock" column-key="masterStock" width="135" :show-overflow="true">
                    <template #header>
                        <div class="table-div" ref="tableColumn1">
                            <div v-for="(todo, index) in searchColumn1" :key="index" v-bind:class="{
                                ascending: (index == searchIndex && searchOrder == 'ascending'),
                                descending: (index == searchIndex && searchOrder == 'descending')
                            }" @click="tableColumnClick(todo.value, index, 'tableColumn1',1)" style="display: flex; flex-direction: row; justify-content: center; line-height: 25px;">
                                <span class="grid-header">
                                    <span>
                                        <el-tooltip class="item" effect="dark" :content="todo.tooltip"
                                            placement="top-end">
                                            <span><i class="el-icon-question"></i></span>
                                        </el-tooltip>
                                    </span>
                                    <span>{{ todo.text }}</span>
                                </span>
                                <!-- <span class="caret-wrapper">
                                    <i class="sort-caret ascending"></i>
                                    <i class="sort-caret descending"></i>
                                </span> -->
                                <span class="caret-wrapper" style="margin-left: 5px;font-size: 16px;align-self: center; display: flex; flex-direction: column;">
                                    <i class="el-icon-caret-top" :class="[(index == searchIndex && searchOrder == 'ascending' && wrappername == 1)?'iconcolor':'startcol']"></i>
                                    <i class="el-icon-caret-bottom" style="margin-top: -10px;" :class="[(index == searchIndex && searchOrder == 'descending' && wrappername == 1)?'iconcolor':'startcol']"></i>
                                </span>
                            </div>
                        </div>
                    </template>
                    <teleport slot-scope="scope">
                        <div>
                            {{ scope.row.masterStock }}
                        </div>
                        <br />
                        <div>
                            {{ scope.row.warehouseStock }}
                        </div>
                    </teleport>
                </vxe-column>
                <!-- <vxe-column title="今日销量" field="salesToday" width="120" :show-overflow="true">
                </vxe-column> -->
                <vxe-column title="今日销量" field="salesToday"  width="110" sortable :show-overflow="true">
                    <template slot-scope="scope">
                        <div>
                            {{ scope.row.salesToday == null ? '' : scope.row.salesToday }}
                        </div>
                    </template>
                </vxe-column>
                <vxe-column title="进货仓库存" field="purchaseStock" column-key="purchaseStock" width="135" :show-overflow="true">
                    <template #header>
                        <div class="table-div" ref="tableColumn3">
                            <div v-for="(todo, index) in searchColumn3" :key="index" v-bind:class="{
                                ascending: (index == searchIndex && searchOrder == 'ascending'),
                                descending: (index == searchIndex && searchOrder == 'descending')
                            }" @click="tableColumnClick(todo.value, index, 'tableColumn3',3)" style="display: flex; flex-direction: row; justify-content: center; line-height: 25px;">
                                <span class="grid-header">
                                    <span>
                                        <el-tooltip class="item" effect="dark" :content="todo.tooltip"
                                            placement="top-end">
                                            <span><i class="el-icon-question"></i></span>
                                        </el-tooltip>
                                    </span>
                                    <span>{{ todo.text }}</span>
                                </span>
                                <!-- <span class="caret-wrapper">
                                    <i class="sort-caret ascending" :class="{iconcolor:(index == searchIndex && searchOrder == 'ascending' && wrappername == 3)}"></i>
                                    <i class="sort-caret descending" :class="{iconcolor:(index == searchIndex && searchOrder == 'descending'&& wrappername == 3)}"></i>
                                </span> -->
                                <span class="caret-wrapper" style="margin-left: 5px;font-size: 16px;align-self: center; display: flex; flex-direction: column;">
                                    <i class="el-icon-caret-top" :class="[(index == searchIndex && searchOrder == 'ascending' && wrappername == 3)?'iconcolor':'startcol']"></i>
                                    <i class="el-icon-caret-bottom" style="margin-top: -10px;" :class="[(index == searchIndex && searchOrder == 'descending' && wrappername == 3)?'iconcolor':'startcol']"></i>
                                </span>
                            </div>
                        </div>
                    </template>
                    <teleport slot-scope="scope">
                        <div>
                            开单量： <span class="birspan" @click="linkpurchaseorder(scope.row.indexNos)">{{ scope.row.indexNoCount }}</span>
                        </div>
                        <div style="color: blue;">
                            在 途：{{ scope.row.inTransitNum }}
                        </div>
                        <div style="color: blue;">
                            调拨在途：{{ scope.row.allocateTransitNum }}
                        </div>
                        <div>
                            进货仓：{{ scope.row.purchaseStock }}
                        </div>
                        <div>
                            审核中：{{ scope.row.inReviewNum }}
                        </div>
                        <div>
                            待审核：{{ scope.row.checkNum }}
                        </div>
                    </teleport>
                </vxe-column>
                <!-- <vxe-column title="采购在途数" field="inTransitNum" width="70" sortable :show-overflow="true">
                </vxe-column> -->
                <vxe-column title="订单待发" field="orderWaitSend" width="110" sortable :show-overflow="true">
                </vxe-column>
                <vxe-column title="可售库存" field="sellStock" width="110" sortable :show-overflow="true">
                    <template #header>
                        <span class="grid-header">
                            <span>
                                <el-tooltip class="item" effect="dark" content="计算：主仓库存-订单占有" placement="top-end">
                                    <span><i class="el-icon-question"></i></span>
                                </el-tooltip>
                            </span>
                            <span>可售库存</span>
                        </span>
                    </template>
                </vxe-column>
                <vxe-column title="安全天数上~下 安全库存上~下" width="110" field="safeDayUp2">
                    <template #header>
                        <span class="grid-header">
                            <!-- <span>
                                <el-tooltip class="item" effect="dark" content="商品信息，款式编码，分仓" placement="top-end">
                                    <span><i class="el-icon-question"></i></span>
                                </el-tooltip>
                            </span> -->
                            <span>安全天数上~下<br /> 安全库存上~下</span>
                        </span>
                    </template>
                    <template slot-scope="scope">
                        <!-- <div>
                           <span> {{ scope.row.safeDayUp }}</span>  / {{ scope.row.safeDayDown }}
                        </div> -->
                        <div style="display: flex; flex-direction: row;">
                            <div :style="scope.row.markerColor == 1?{backgroundColor: 'yellow'}:{}">
                                {{ scope.row.safeDayUp2 }}
                            </div>
                             / {{ scope.row.safeDayDown2 }}
                        </div>
                        <!-- <div>
                            {{ scope.row.safeStockUp }} / {{ scope.row.safeStockDown }}
                        </div> -->
                        <div>
                            {{ scope.row.safeStockUp2 }} / {{ scope.row.safeStockDown2 }}
                        </div>
                    </template>
                </vxe-column>
                <vxe-column title="最近在途时长 历史平均在途" width="120" field="lastInTransitTime">
                    <template #header>
                        <span class="grid-header">
                            <span>
                                <el-tooltip class="item" effect="dark" content="计算逻辑：小时数/24，例：98/24=4.0" placement="top-end">
                                    <span><i class="el-icon-question"></i></span>
                                </el-tooltip>
                            </span>
                            <span>最近在途时长<br/>历史平均在途</span>
                        </span>
                    </template>
                    <teleport slot-scope="scope">
                        <div>
                            {{ scope.row.lastInTransitTime | myformatSecondToHour }}
                        </div>
                        <br />
                        <div>
                            {{ scope.row.avgInTransitTime | myformatSecondToHour }}
                        </div>
                    </teleport>
                </vxe-column>
                <vxe-column title="总销量/总退货" width="140" field="refundDay3">
                    <teleport slot-scope="scope">
                        <div class="birchart">
                            <div v-if="scope.row.warehouse == -1">
                                3日： <span class="birspan" @click="getbirchart(scope.row.goodsCode ,3)">{{ scope.row.salesDay3 }}</span>
                                        / {{ scope.row.refundDay3 }}
                            </div>
                            <div v-else>
                                3日： {{ scope.row.salesDay3 }} / {{ scope.row.refundDay3 }}
                            </div>
                        </div>
                        <div>
                            <div v-if="scope.row.warehouse == -1">
                                7日： <span class="birspan" @click="getbirchart(scope.row.goodsCode ,7)">{{ scope.row.salesDay7 }}</span>
                                / {{ scope.row.refundDay7 }}
                            </div>
                            <div v-else>
                                7日： {{ scope.row.salesDay7 }} / {{ scope.row.refundDay7 }}
                            </div>
                        </div>
                        <div>
                            <div v-if="scope.row.warehouse == -1">
                                15日： <span class="birspan" @click="getbirchart(scope.row.goodsCode ,15)">{{ scope.row.salesDay15 }}</span>
                                / {{ scope.row.refundDay15 }}
                            </div>
                            <div v-else>
                                15日： {{ scope.row.salesDay15 }} / {{ scope.row.refundDay15 }}
                            </div>
                        </div>
                        <div>
                            <div v-if="scope.row.warehouse == -1">
                                30日： <span class="birspan" @click="getbirchart(scope.row.goodsCode ,30)">{{ scope.row.salesDay30 }}</span>
                                / {{ scope.row.refundDay30 }}
                            </div>
                            <div v-else>
                                30日： {{ scope.row.salesDay30 }} / {{ scope.row.refundDay30 }}
                            </div>
                        </div>
                    </teleport>
                </vxe-column>
                <vxe-column title="昨日销量" field="salesYesterday" width="100" sortable :show-overflow="true">
                </vxe-column>
                <vxe-column title="日均销量/日均退货" width="140" field="avgDay3">
                    <teleport slot-scope="scope">
                        <div>
                            3日： {{ scope.row.avgDay3 }} / {{ scope.row.avgRefundDay3 }}
                        </div>
                        <div>
                            7日： {{ scope.row.avgDay7 }} / {{ scope.row.avgRefundDay7 }}
                        </div>
                        <div>
                            15日： {{ scope.row.avgDay15 }} / {{ scope.row.avgRefundDay15 }}
                        </div>
                        <div>
                            30日： {{ scope.row.avgDay30 }} / {{ scope.row.avgRefundDay30 }}
                        </div>
                    </teleport>
                </vxe-column>
                <vxe-column title="日均修正量" field="amendDay" width="120" sortable :show-overflow="true">
                </vxe-column>
                <vxe-column title="总销量(排除下架)" width="140" field="salesDay3_2">
                    <teleport slot-scope="scope">
                        <div>
                            3日： {{ scope.row.salesDay3_2 }}
                        </div>
                        <div>
                            7日： {{ scope.row.salesDay7_2 }}
                        </div>
                        <div>
                            15日： {{ scope.row.salesDay15_2 }}
                        </div>
                        <div>
                            30日： {{ scope.row.salesDay30_2 }}
                        </div>
                    </teleport>
                </vxe-column>
                <vxe-column title="新日均修正量" field="amendDayNew" width="140" sortable :show-overflow="true">
                    <template #header>
                        <span class="grid-header">
                            <span>
                                <el-tooltip class="item" effect="dark" content="总销量(排除下架)3日/3" placement="top-end">
                                    <span><i class="el-icon-question"></i></span>
                                </el-tooltip>
                            </span>
                            <span>新日均修正量</span>
                        </span>
                    </template>
                </vxe-column>
            </vxe-table>
        </div>


        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <!-- 上传数据 -->
        <el-dialog title="导入数据" :visible.sync="dialogVisible" v-dialogDrag width="30%">
            <div>
                <el-alert type="warning" show-icon :closable="false"
                    title="温馨提示:导入文件，各需要包含‘全仓’、‘义乌仓’、‘南昌昌东’、‘南昌定制’、‘圆通孵化’‘义乌圆通仓’‘圆通5楼仓’‘西安分仓’‘杭州分仓’‘南昌裁剪仓’‘义乌邮政仓’‘西安港务分仓’‘义乌圆通3楼’‘义乌圆通2楼’‘【昀晗-JS】’‘天天2楼仓’‘预包加工仓’‘跨境仓’‘南昌芒果仓’‘【昀晗-WH】’‘昀晗-YY’‘圆通2栋6楼’‘昀晗-DG’‘金咖3号库3楼首力供应链’‘【昀晗-金华仓】【大马美甲仓】【昀晗-NC】【昀晗-JD】【昀晗-JM】【首力供应链】【昀晗-CS】【昀晗-FZ】【义乌加工仓】【东莞全品类仓】【1688-天天仓】【昀晗-DY】’关键字 ！"></el-alert>
            </div>
            <span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="12" action
                    accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }} </el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <!-- 建采购单 -->
        <el-dialog :title="addEditTitle" :visible.sync="dialogAddVisible" v-dialogDrag width='50%' height='700px'>
            <el-form ref="addForm" :model="addForm" :rules="addFormRules" label-width="100px">
                <el-row :hidden="true">
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item field="id" label="id">
                            <el-input v-model="addForm.id" auto-complete="off" :disabled="true" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item field="indexNo" label="序号">
                            <el-input v-model="addForm.indexNo" auto-complete="off" :disabled="true"
                                style="width:88%" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="warehouse" label="仓库">
                            <!-- <el-select v-model="addForm.warehouse" placeholder="仓库" style="width:88%;">
                                <el-option v-for="item in warehouseList" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select> -->
                            <el-select v-model="addForm.warehouse" placeholder="请选择分仓" filterable style="width: 250px">
                                <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                                    :value="item.wms_co_id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="supplier_id" label="供应商">
                            <!-- <el-select v-model="addForm.supplier_id" ref="supplier_id" filterable remote reserve-keyword placeholder="请输入供应商" :remote-method="remoteSearchSupplier" :loading="supplierLoading" style="width:88%">
                                <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select> -->
                            <el-select v-model="addForm.supplier_id" ref="supplier_id" filterable remote reserve-keyword
                                placeholder="请输入供应商" :remote-method="remoteSearchSupplier" :loading="supplierLoading"
                                style="width:250px">
                                <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <div style="height: 400px;">
                            <vxe-table :data="buyNoList" height="100%" style="width: 100%;" border stripe>
                                <vxe-column title="采购单号" field="buyNo" width="130" >
                                    <template slot-scope="scope">
                                        <el-tag v-if="scope.row.isFirst" type="success">{{scope.row.buyNo}}(最近一单)</el-tag>
                                        <span v-else>{{scope.row.buyNo}}</span>
                                    </template>
                                </vxe-column>
                                <vxe-column title="商品编码" field="goodsCode" width="120" ></vxe-column>
                                <vxe-column title="供应商" field="supplier" width="200" ></vxe-column>
                                <vxe-column title="进货仓" field="warehouseName" width="120" ></vxe-column>
                                <vxe-column title="供应商链接" field="address1688" width="120">
                                    <template #default="{ row }">
                                        <el-link v-if="row.address1688" :href="row && row.address1688" type="primary" target="_blank">供应商链接</el-link>
                                    </template>
                                </vxe-column>
                                <vxe-column title="操作" width="100">
                                    <template slot-scope="scope">
                                        <el-button @click="handleClick(scope.row)" type="text" size="small">选择</el-button>
                                    </template>
                                </vxe-column>
                            </vxe-table>
                        </div>


                  </el-col>
                </el-row>
                <!-- <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item field="startDept" title="发起部门">
                            <el-select v-model="addForm.startDept" placeholder="发起部门" style="width:88%;">
                                <el-option title="采购正常进货" value="采购正常进货" />
                                <el-option title="运营给量进货" value="运营给量进货" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item field="payWay" title="支付方式">
                            <el-select v-model="addForm.payWay" placeholder="支付方式" style="width:88%;">
                                <el-option title="阿里巴巴" value="阿里巴巴" />
                                <el-option title="银行卡" value="银行卡" />
                                <el-option title="支付宝" value="支付宝" />
                                <el-option title="微信" value="微信" />
                                <el-option title="现金" value="现金" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item field="payAccount" label="账号">
                            <el-input v-model="addForm.payAccount" placeholder="请输入内容" :maxlength="50" @input="changetextarea($event)"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item field="payReMark" label="钉钉备注">
                            <el-input v-model="addForm.payReMark" placeholder="请输入内容" :maxlength="200" @input="changetextarea($event)"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                  <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                    <el-form-item field="groupId" label="审批运营组:">
                        <el-select v-model="addForm.groupId" clearable filterable placeholder="请选择审批运营组" style="width: 140px">
                            <el-option v-for="item in grouplist" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item field="purImageUrl" label="图片">
                        <yh-img-upload :value.sync="addForm.purImageUrl" :limit="9" ></yh-img-upload>
                    </el-form-item>
                    </el-col>
                </el-row> -->
            </el-form>

            <template #footer>
                <span class="dialog-footer">
                    <el-link :underline="false" type="primary" @click="addsupplier" style="margin-right: 20px;">添加供应商</el-link>

                    <el-button @click="dialogAddVisible = false">取 消</el-button>
                    <my-confirm-button type="submit" :validate="finishFormValidate" :loading="onFinishLoading" @click="onFinish()">
                    </my-confirm-button>
                    <my-confirm-button type="submit" :validate="finishFormValidate" v-if="false">同步到聚水潭
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>

        <!-- 添加供应商 -->
        <el-dialog title="添加供应商" :visible.sync="dialogAddSuppVisible" :close-on-click-modal="false" append-to-body v-dialogDrag width='30%' height='500px'>
            <el-form ref="addSuppForm" :model="addSuppForm" :rules="addSuppFormRules" label-width="100px">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item field="name" title="供应商名称">
                            <el-input v-model="addSuppForm.name" placeholder="供应商名称"  :maxlength="40" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogAddSuppVisible = false">取 消</el-button>
                    <my-confirm-button type="submit" :validate="finishSuppFormValidate" :loading="onFinishSuppLoading" @click="onSave()">
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="配置" :visible.sync="settingshow" :close-on-click-modal="false" append-to-body v-dialogDrag width='30%' height='500px'>
            <el-form ref="settingForm" v-if="settingshow" :model="settingForm" :rules="settingRules" label-width="100px">
                <!-- <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24"> -->
                        <span>配置</span>
                        <el-form-item prop="globalSwitch" label="全局开关">
                            <el-switch
                                v-model="settingForm.globalSwitch">
                            </el-switch>
                        </el-form-item>
                        <el-row>
                            <span>配置条件</span>
                            <el-tooltip class="item" effect="dark" placement="top-start">
                                <div slot="content">
                                    按编码维度，分两种情况&nbsp;&nbsp;&nbsp;&nbsp;(2B+C)均向上取整，新日均修正量为0为空不标黄
                                    <br/>
                                    1.月销大于1000
                                    <br/>
                                    &nbsp;&nbsp;&nbsp;&nbsp;库存现有数+进货仓+调拨在途+采购在途+审批中>=(2B+C)*新日均修正量（标黄）
                                    <br/>
                                    2.月销小于等于1000
                                    <br/>
                                    &nbsp;&nbsp;&nbsp;&nbsp;1）如果2B+C小于等于D，那么库存现有数+进货仓+调拨在途+采购在途+审批中>=D*新日均修正量（标黄）
                                    <br/>
                                    &nbsp;&nbsp;&nbsp;&nbsp;2）如果2B+C大于D，那么库存现有数+进货仓+调拨在途+采购在途+审批中>=(2B+C)*新日均修正量（标黄）
                                </div>
                                <i class="el-icon-question" style="margin-right: 10px;padding-top: 5px;"></i>
                            </el-tooltip>
                        </el-row>
                        <el-form-item prop="conditionA" label="A:">
                            <el-row>

                                <el-col span="10">
                                    <el-select v-model="settingForm.conditionA" placeholder="销量选择">
                                        <el-option label="3天均销量" value="3天均销量"></el-option>
                                        <el-option label="7天均销量" value="7天均销量"></el-option>
                                        <el-option label="15天均销量" value="15天均销量"></el-option>
                                        <el-option label="30天均销量" value="30天均销量"></el-option>

                                        <el-option label="3天总销量" value="3天总销量"></el-option>
                                        <el-option label="7天总销量" value="7天总销量"></el-option>
                                        <el-option label="15天总销量" value="15天总销量"></el-option>
                                        <el-option label="30天总销量" value="30天总销量"></el-option>

                                    </el-select>
                                </el-col>
                                <el-col span="4">
                                    小于等于
                                </el-col>
                                <el-col span="10">
                                    <el-form :model="settingForm" @submit.native.prevent>
                                        <el-form-item prop="conditionAVal" label="" :rules="{ required: true, message: '请输入', trigger: 'blur' }">
                                            <el-input maxlength="8" type="number" @change="inputchange($event,'conditionAVal')" v-model="settingForm.conditionAVal" placeholder="请输入1-9999999" @keyup.enter.native="()=>{return}"></el-input>
                                        </el-form-item>
                                    </el-form>
                                </el-col>
                            </el-row>
                        </el-form-item>
                        <el-form-item prop="conditionB" label="B:">
                            <el-row>
                                <el-col span="10">
                                    <el-select v-model="settingForm.conditionB" placeholder="在途时长选择">
                                        <el-option label="最近在途时长" value="最近在途时长"></el-option>
                                        <el-option label="平均在途时长" value="平均在途时长"></el-option>
                                        <el-option label="自动选择最大" value="自动选择最大"></el-option>

                                    </el-select>
                                </el-col>
                            </el-row>
                        </el-form-item>
                        <el-form-item prop="conditionC" label="C:">
                            <el-row>
                                <el-col span="10" >
                                    <el-input maxlength="8" type="number" @change="inputchange($event,'conditionC')" v-model="settingForm.conditionC" placeholder="请输入1-9999999"></el-input>
                                </el-col>
                            </el-row>
                        </el-form-item>
                        <!-- <el-row>
                            <el-span>1.(A)如大于所选条件输入值</el-span>
                        </el-row>
                        <el-row>
                            <el-span>判断</el-span>
                        </el-row>
                        <el-row>
                            <el-span>(2B+C)结果向上取整作为判断条件。安全天数上值就标黄</el-span>
                        </el-row> -->
                        <el-form-item prop="conditionD" label="D:">
                            <el-row>
                                <el-col span="10" >
                                    <el-input maxlength="8" type="number" @change="inputchange($event,'conditionD')" v-model="settingForm.conditionD" placeholder="请输入1-9999999"></el-input>
                                </el-col>
                                <el-col span="10">
                                    最小值设置为多少天
                                </el-col>
                            </el-row>
                        </el-form-item>
                    <!-- </el-col>
                </el-row> -->
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="settingshow = false">取 消</el-button>
                    <my-confirm-button type="submit" :validate="settingFormValidate" :loading="onFinishSuppLoading" @click="onSavesetting()">
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>

        <el-image-viewer v-if="showGoodsImage" :url-list="imgList" :on-close="closeFunc" style="z-index:9999;" />

        <!-- 搜索界面 -->
        <el-dialog :visible.sync="dialogSearchInfoVisible" v-dialogDrag width='78%' :append-to-body="true" >
            <goods-info ref="goodsInfo" :ischoice="true" :chooseTags ="goodsCodeArr" style="overflow:auto;height:700px" @searchClick = "searchClick" >

            </goods-info>
        </el-dialog>

        <!-- 采购单建立成功提示 -->
        <el-dialog title="温馨提示：请前往采购单处理" :visible.sync="messagedialogVisible" v-dialogDrag  width="30%">
            <el-alert :title="buyNoMessage" type="success" :closable="false"></el-alert>
            <br/> <br/> <br/> <br/>
            <span slot="footer" class="dialog-footer">
                <div style="float: left;">
                    前往处理
                    <el-button type="text" size="mini" @click="linkstockout">
                        采购单管理
                    </el-button>
                </div>
                <el-button @click="messagedialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="messagedialogVisible = false">确 定</el-button>
            </span>
        </el-dialog>

        <!-- 时间线弹框 -->
        <el-dialog title="" :visible.sync="importtimedialogVisible" v-dialogDrag  width="30%">
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <span>当天导入时间记录</span>
                </div>
                <div style="height:400px;overflow-y:auto"  class="text item">
                    <el-alert v-for="item in importtimelist" :key="item" title="" type="success" :closable="false">
                        导入时间 : {{item.createdTime}}
                    </el-alert>
                </div>
            </el-card>
        </el-dialog>

        <el-dialog
            title="标题"
            :visible.sync="dialogVisibletextarea"
            append-to-body
            width="30%">
            <el-input
                type="textarea"
                :rows="12"
                placeholder="请分行输入"
                @input="changetextarea($event)"
                v-model="textarea">
            </el-input>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibletextarea = false">取 消</el-button>
                <el-button type="primary" @click="submittextarea">确认</el-button>
            </span>
        </el-dialog>

        <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>

<script>
 import { Loading } from 'element-ui';
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import { warehouselist, formatWarehouse as formatWarehousePublic, formatSecondNewToHour, onJumpLink, canJump } from "@/utils/tools";
import { getAllProBrand, getAllBianMaBrandByFilter, getCurBianMaBrand, getAllWarehouse } from '@/api/inventory/warehouse'
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import goodsInfo from "./components/goodsInfo.vue";
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import inputnumberYunhan from '@/components/Comm/inputnumberYunhan.vue'
import { savePurchaseManual, savePurchase2NewPlanManual,getLastUpdateTimePurchaseNewPlanAsync,
    getLastUpdateTimePurchaseNewPlanListAsync } from '@/api/inventory/purchase'
    import {importNewPurchasePlan2} from '@/api/inventory/purchaseImport'
import { pagePurchaseNewPlan2, BatchUpdatePurchaseNewPlan2, exportPurchaseNewPlan2, TaskGetPurchaseOrder, batchUpdateGoodsRemark, getPurchaseNewPlan2MarkerColorSet, savePurchaseNewPlan2MarkerColorSet } from "@/api/inventory/purchaseordernew";
import { getPurOrderAnalysis, } from "@/api/inventory/goodscodestock"
import { pageSupplierAll, getPurNameSimilarity, addSupplier } from '@/api/inventory/supplier'
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
import buschar from '@/components/Bus/buschar'
import { getTableColumnCache, setTableColumnCache, GetVxeTableColumnCacheAsync, SetVxeTableColumnCacheAsync } from '@/api/admin/business'
import YhWarehouseSelector from "@/components/YhCom/YhWarehouseSelector";

import request from '@/utils/request'
const api = '/api/order/shipperFxOrder/';

var formatWarehouse = function (warehouse) {
    if (warehouse == -1) return "全仓";
    return formatWarehousePublic(warehouse);
};

var formatSecondToHour1 = function(time) {
    return formatSecondNewToHour(time);
}

//formatter:(row)=> !row.orderNo? " " : row.orderNo

// const tableCols = [
//     { istrue: true, field: 'images', label: '图片', type: 'imageGoodsCode', width: '120', goods: { code: 'goodsCode', name: 'goodsName' }, sortable: 'custom', },
//     { istrue: true, field: 'warehouse', label: '分仓', width: '75', fixed: true, sortable: 'custom', formatter: (row) => formatWarehouse(row.warehouse) },
//     { istrue: true, field: 'styleCode', label: '系列编码', width: '120', sortable: 'custom', },
//     { istrue: true, field: 'goodsCode', label: '商品编码', width: '120', sortable: 'custom', },
//     { istrue: true, field: 'goodsCode', label: '商品编码', width: '100', sortable: 'custom', },
//     { istrue: true, field: 'goodsLable', label: '商品标签', width: '100', sortable: 'custom', },
//     { istrue: true, field: 'firstUserName', label: '开款人', width: '100', sortable: 'custom', },
//     { istrue: true, field: 'brandId', label: '品牌', width: '100', sortable: 'custom', },
//     { istrue: true, field: 'goodsRemark', label: '品牌备注', width: '100', sortable: 'custom', },
//     { istrue: true, field: 'shopId', label: '建议采购信息', width: '100', sortable: 'custom', },
//     { istrue: true, field: 'shopId', label: '库存周转天数', width: '100', sortable: 'custom', },
//     { istrue: true, field: 'masterStock', label: '主仓库存', width: '100', sortable: 'custom', },
//     { istrue: true, field: 'salesToday', label: '今日销量', width: '80', sortable: 'custom', },
//     { istrue: true, field: 'purchaseStock', label: '进货仓库存', width: '100', sortable: 'custom', },
//     { istrue: true, field: 'inTransitNum', label: '采购在途数', width: '100', sortable: 'custom', },
//     { istrue: true, field: 'orderWaitSend', label: '订单待发', width: '100', sortable: 'custom', },
//     { istrue: true, field: 'shopId', label: '可售库存数', width: '100', sortable: 'custom', },
//     //{istrue:true,field:'safeNum',label:'安全天数上~下，安全库存上~下', width:'130',sortable:'custom',},
//     //{istrue:true,field:'salesQty',label:'总销量/总退货', width:'120',sortable:'custom',},
//     //{istrue:true,field:'salesQtyAvg',label:'日均销量/日均退货', width:'120',sortable:'custom',},
//     { istrue: true, field: 'amendDay', label: '日均修正量', width: '120', sortable: 'custom', },
// ]

const tableHandles = [
    { label: "导入", type: "primary", handle: (that) => that.startImport() },
    //{ label: "导出", type: "primary", handle: (that) => that.onExport() },
    { label: "以采购建议数生成（选择供应商）", type: "primary", loading:"openAddloading", handle: (that) => that.openAdd() },
    //{ label: "刷新", type: "primary", handle: (that) => that.onSearch() },
];

const TimeDate = formatTime(new Date(), "YYYY-MM-DD HH:mm:ss");
const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");

export default {
    name: 'YunHanAdminPurchasenewplan2',
    components: { cesTable, MyContainer, MyConfirmButton, ElImageViewer, inputnumberYunhan, goodsInfo, YhImgUpload, buschar, YhWarehouseSelector },
    filters: {
        myWarehouse(val) {
            return formatWarehouse(val)
        },
        myformatSecondToHour(val) {
            return formatSecondToHour1(val)
        }
    },

    data() {
        return {
            api,
            that: this,
            settingshow: false,
            settingRules: {
                conditionA: [{ required: true, message: '请选择销量', trigger: 'blur' }],
                conditionB: [{ required: true, message: '请选择在途时长', trigger: 'blur' }],
                conditionC: [{ required: true, message: '请输入', trigger: 'blur' }],
                conditionD: [{ required: true, message: '请输入', trigger: 'blur' }],
                conditionAVal: [{ required: true, message: '请输入', trigger: 'blur' }]

            },
            settingForm: {},
            virtualTypeList: [],


            wrappername: '',
            textarea: '',
            filter: {
                markerColor: "0",
                warehouses: null,
                goodsCode: null,
                styleCode: null,
                brandId: null,
                brandIds: null,
                groupId: null,
                goodsName: null,
                warnStatus: null,
                isedit: null,
                isEnabled: "1",
                virtualType: null
                // minPurchasePlanCount: null,
                // maxPurchasePlanCount: null,
                // minTurnoverDay: null,
                // maxTurnoverDay: null
            },
            filterchart: {
                startDate: null,
                endDate: null,
                timerange: [startDate, endDate]
            },
            height: '100%',
            backgroundColor: 'white',
            warehouses: [-1],
            brandIds:[],
            supplierOptions: [],
            list: [],
            selectarray: [],
            buyNoList: [],
            selids: [],
            goodsCodes: [],
            platformList: [],
            illegalTypeList: [],
            brandlist: [],
            brandOptions: [],
            grouplist: [],
            summaryarry: {},
            addEditTitle: "",
            buyNoMessage: '',
            pager: { OrderBy: "cost", IsAsc: false },
            searchOrder: "",
            searchIndex: -1,
            searchColumn1: [
                { text: '主仓库存数', value: 'masterStock', tooltip:'主仓库存数' },
                { text: '仓库库存数', value: 'warehouseStock', tooltip:'仓库库存数' },
            ],
            searchColumn2: [
                { text: '库存周转天数', value: 'inventoryDay', tooltip:'（仓库库存数 - 订单占有）/新日均修正量' },
                { text: '采购周转天数', value: 'purchaseDay', tooltip:'采购周转天数：（仓库库存数 - 订单占有+建议采购数“采购自己填的框内数据”）/新日均修正量' },
            ],
            searchColumn3: [
                { text: '采购在途数', value: 'inTransitNum', tooltip:'采购在途数' },
                { text: '进货仓库存', value: 'purchaseStock', tooltip:'进货仓库存' },
                //{ text: '待审核', value: 'inTransitNum', tooltip:'' },
            ],
            searchColumn4: [
                { text: '商品分类', value: 'groupName', tooltip:'运营组' },
                { text: '主售分类', value: 'maxRatioGroupName', tooltip:'销售数最多的运营组' },
                //{ text: '待审核', value: 'inTransitNum', tooltip:'' },
            ],
            filterImport: {
                platform: 1,
                purchasePlanCount: '',
                occurrenceTime: formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD")
            },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            //tableCols: tableCols,
            tableHandles: tableHandles,
            //warehouselist: warehouselist,
            warehouselist: [],
            total: 0,
            sels: [],
            imgList: [],
            fileList: [],
            importtimelist: [],
            byid: false,
            dialogVisibletextarea: false,
            openAddloading: false,
            showGoodsImage: false,
            listLoading: false,
            pageLoading: false,
            dialogVisible: false,
            uploadLoading: false,
            isRouterShow: true,
            dialogAddVisible: false,
            dialogAddSuppVisible: false,
            supplierLoading: false,
            brandLoading: false,
            onFinishLoading: false,
            onFinishSuppLoading: false,
            messagedialogVisible: false,
            importtimedialogVisible: false,
            buscharDialog: { visible: false, title: "", data: [] },
            addFormRules: {
                startDept: [{ required: true, message: '请输入发起部门', trigger: 'blur' }],
                warehouse: [{ required: true, message: '请输入仓库', trigger: 'blur' }],
                supplier_id: [{ required: true, message: '请输入供应商', trigger: 'blur' }],
                payWay: [{ required: true, message: '请输入支付方式', trigger: 'blur' }],
                payAccount: [{ required: true, message: '请输入账号', trigger: 'blur' }],
                payReMark: [{ required: true, message: '请输入钉钉备注', trigger: 'blur' }],
                purImageUrl: [{ required: true, message: '请上传图片', trigger: 'blur' }],
                groupId: [{ required: true, message: '请选择审批运营组', trigger: 'blur' }],
            },
            addSuppFormRules: {
                name: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }],
            },
            //新增编辑界面数据
            addForm: {
                id: 0,
                indexNo: 0,
                warehouse: null,
                purchaseDate: null,
                supplier_id: null,
                supplier: '',
                brandId: null,
                brandName: '',
                dtlGoods: [],
                purImageUrl: null,
                startDept: null,
                payWay: null,
                payReMark: null,
                payAccount: null,
                groupId: null
            },
            addSuppForm: {
                name:null
            },
            dialogSearchInfoVisible:false,
            infoFilter: {
            },
            goodsCodeArr:[],
            lastUpdateTime:'',
            columnKeyword: '',
            customConfig: {
                storage: { fixed: true, sort: true, resizable: true, visible: true },
                restoreStore: this.restoreStore,
                updateStore: this.updateStore,
                visibleMethod: ({ column }) => { if (!this.columnKeyword || !column.title) return true; return column.title.indexOf(this.columnKeyword) > -1 },
                slots: {
                    top: (params) => {
                        return this.$createElement('div', {
                            class: 'custom_top'
                        }, [this.$createElement('input', {
                            style: { margin: '10px', padding: '5px', width: '230px', 'border': 'solid 1px #f2f2f2' },
                            attrs: { placeholder: "关键字" },
                            domProps: { value: this.columnKeyword },
                            on: {
                                input: (event) => {
                                    this.columnKeyword = event.target.value
                                }
                            }
                        }
                        )]);
                    }
                }
            }
        };
    },
    created () {
        this.$nextTick(() => {
            this.$refs.table.connect(this.$refs.xToolbar)
        })
    },

    async mounted() {
        await this.init();
        await this.onSearch()
        this.shifttoclick();
        setInterval(()=>{
            this.getlasttime();
        },1000*60*5);
    },

    methods: {
        async restoreStore({ id, type, storeData }) {
            let resp = await GetVxeTableColumnCacheAsync({ tableId: id });
            let store = null;
            if (resp && resp.success && resp.data) {
                store = JSON.parse(resp.data);
            }
            return store ?? storeData;
        },
        async updateStore({ id, type, storeData }) {
            await SetVxeTableColumnCacheAsync({ tableId: id, ColumnConfig: JSON.stringify(storeData) });
        },
        canJump,
        async handleLinkClick(row, column) {
          if (!this.canJump(row, column)) return;
          try {
            await onJumpLink(row[column.prop], column.prop);
          } catch (err) {
            this.$message.error('小昀工具箱不在线，请开启后使用！');
          }
        },
        inputchange(val, name){
            if(val<1){
                this.settingForm[name] = 1;
                this.$message.error('不能小于1');
                return false;
            }else if(val>99999999){
                this.settingForm[name] = 99999999;
                this.$message.error('不能大于99999999');
                return false;
            }
        },
        async getVirtualTypeList() {
            var res = await request.post(`${this.api}GetShipperFxNameList`)
            if (res?.success) {
                this.virtualTypeList = res?.data ?? [];
            } else {
                this.virtualTypeList = [];
            }
        },
        async onSavesetting(){
            this.settingshow = true;
            let params  = {
                ...this.settingForm
            }
            var res= await savePurchaseNewPlan2MarkerColorSet(params);
            if (!res?.success) return
            this.settingshow = false
            this.$message.success('操作成功');
        },
        async settingFuc(){
            this.settingshow = true;
            var res= await getPurchaseNewPlan2MarkerColorSet();
            console.log('导入时间',res.data)
            if (!res?.success) return
            this.settingForm = res.data
        },
        checCheckboxkMethod({ row }){
            return row.isNotStock != 1
        },
        async init() {
            var res1 = await getDirectorGroupList();
            this.grouplist = res1.data.map(item => {
                return { value: item.key, label: item.value };
            });

            var res2 = await getAllProBrand();
            this.brandlist = res2.data.map(item => {
                return { value: item.key, label: item.value };
            });
            var res3 = await getAllWarehouse();
            var warehouselist1 = res3.data.filter((x) => x.name.indexOf('代发') < 0);
            warehouselist1.unshift({ name: "全仓", co_id: 10361546, wms_co_id: 11793337, is_main: false, remark1: null, remark2: null, wms_co_id: -1 });
            this.warehouselist = warehouselist1;

            await this.getVirtualTypeList();
            //this.getlasttime();
        },
        async getlasttime(){
            var res3= await getLastUpdateTimePurchaseNewPlanAsync();
            this.lastUpdateTime="最晚更新时间"+res3.data.time;
            var diff = res3.data.isImport;
            console.log('diff',diff)
            if (diff == 0)
                this.backgroundColor = 'white';
            else if (diff == 1)
                this.backgroundColor = 'yellow';
            else if (diff == 2)
                this.backgroundColor = 'red';
        },
        async getimportlist() {
            var res= await getLastUpdateTimePurchaseNewPlanListAsync();
            console.log('导入时间',res.data)
            if (!res?.success) return
            this.importtimelist = res.data
            this.importtimedialogVisible = true;
        },
        //开始导入
        startImport() {
            this.dialogVisible = true;
        },
        //取消导入
        cancelImport() {
            this.dialogVisible = false;
        },
        beforeRemove() {
            return true;
        },
        //上传成功
        uploadSuccess(response, file, fileList) {
            if (response.code == 200) {
            } else {
                fileList.splice(fileList.indexOf(file), 1);
            }
        },
        async submitUpload() {
            if (!this.filterImport.occurrenceTime) {
                this.$message({ message: "请先选择日期", type: "warning" });
                return false;
            }
            if (!this.filterImport.platform) {
                this.$message({ message: "请先选择平台", type: "warning" });
                return false;
            }
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请先选取文件", type: "warning" });
                return false;
            }
            this.fileHasSubmit = true;
            this.$refs.upload.submit();
        },
        clearFiles() {
            this.$refs['upload'].clearFiles();
        },
        async uploadFile(item) {
            if (!this.fileHasSubmit) {
                return false;
            }

            console.log('fileItem1111',item)

            let fileyiwu = this.fileList.filter((item, index, arr) => { return item.name.indexOf("义乌仓") > -1; });
            let filecdong = this.fileList.filter((item, index, arr) => { return item.name.indexOf("南昌昌东") > -1; });
            let filequancang = this.fileList.filter((item, index, arr) => { return item.name.indexOf("全仓") > -1; });
            let filencdingzhi = this.fileList.filter((item, index, arr) => { return item.name.indexOf("南昌定制仓") > -1; });
            let fileytfuhua = this.fileList.filter((item, index, arr) => { return item.name.indexOf("圆通孵化") > -1; });
            let fileyiwuyt = this.fileList.filter((item, index, arr) => { return item.name.indexOf("义乌圆通仓") > -1; });
            let fileyt5th = this.fileList.filter((item, index, arr) => { return item.name.indexOf("圆通5楼仓") > -1; });
            let filexian = this.fileList.filter((item, index, arr) => { return item.name.indexOf("西安分仓") > -1; });
            let filehangz = this.fileList.filter((item, index, arr) => { return item.name.indexOf("杭州分仓") > -1; });
            let filenancj = this.fileList.filter((item, index, arr) => { return item.name.indexOf("南昌裁剪仓") > -1; });
            let fileyiwuyz = this.fileList.filter((item, index, arr) => { return item.name.indexOf("义乌邮政仓") > -1; });
            let filexiangw = this.fileList.filter((item, index, arr) => { return item.name.indexOf("西安港务分仓") > -1; });
            let fileyt = this.fileList.filter((item, index, arr) => { return item.name.indexOf("义乌圆通3楼") > -1; });
            let fileyt2 = this.fileList.filter((item, index, arr) => { return item.name.indexOf("义乌圆通2楼") > -1; });

            let fileyhcsfc = this.fileList.filter((item, index, arr) => { return item.name.indexOf("【昀晗-JS】") > -1; });

            let filett2 = this.fileList.filter((item, index, arr) => { return item.name.indexOf("天天2楼") > -1; });

            let fileybjgc = this.fileList.filter((item, index, arr) => { return item.name.indexOf("预包加工仓") > -1; });

            let filekjc = this.fileList.filter((item, index, arr) => { return item.name.indexOf("跨境仓") > -1; });

            let filencmgc = this.fileList.filter((item, index, arr) => { return item.name.indexOf("南昌芒果仓") > -1; });

            let fileahc = this.fileList.filter((item, index, arr) => { return item.name.indexOf("【昀晗-WH】") > -1; });

            let fileayy = this.fileList.filter((item, index, arr) => { return item.name.indexOf("昀晗-YY") > -1; });

            let fileyt26 = this.fileList.filter((item, index, arr) => { return item.name.indexOf("圆通2栋6楼") > -1; });

            let fileyhdg = this.fileList.filter((item, index, arr) => { return item.name.indexOf("昀晗-DG") > -1; });

            let filejk3h3l = this.fileList.filter((item, index, arr) => { return item.name.indexOf("金咖3号库3楼首力供应链") > -1; });


            let fileyhjhc = this.fileList.filter((item, index, arr) => { return item.name.indexOf("【昀晗-金华仓】") > -1; });

            let filedmmj = this.fileList.filter((item, index, arr) => { return item.name.indexOf("【大马美甲仓】") > -1; });

            let fileyhnc = this.fileList.filter((item, index, arr) => { return item.name.indexOf("【昀晗-NC】") > -1; });
            let fileyhjd = this.fileList.filter((item, index, arr) => { return item.name.indexOf("【昀晗-JD】") > -1; });
            let fileyhjm = this.fileList.filter((item, index, arr) => { return item.name.indexOf("【昀晗-JM】") > -1; });
            let fileslgyl = this.fileList.filter((item, index, arr) => { return item.name.indexOf("【首力供应链】") > -1; });

            let fileyhcs = this.fileList.filter((item, index, arr) => { return item.name.indexOf("【昀晗-CS】") > -1; });
            let fileyhfz = this.fileList.filter((item, index, arr) => { return item.name.indexOf("【昀晗-FZ】") > -1; });

            let fileywjgc = this.fileList.filter((item, index, arr) => { return item.name.indexOf("【义乌加工仓】") > -1; });

            let filedgqpl = this.fileList.filter((item, index, arr) => { return item.name.indexOf("【东莞全品类仓】") > -1; });

            let file1688tt = this.fileList.filter((item, index, arr) => { return item.name.indexOf("【1688-天天仓】") > -1; });

            let fileyhdy = this.fileList.filter((item, index, arr) => { return item.name.indexOf("【昀晗-DY】") > -1; });

            let hasFile=false;
            if(fileyiwu.length>0||filecdong.length>0||filequancang.length>0||filencdingzhi.length>0||fileytfuhua.length>0||fileyiwuyt.length>0
            ||fileyt5th.length>0||filexian.length>0||filehangz.length>0||filenancj.length>0||fileyiwuyz.length>0||filexiangw.length>0
            ||fileyt.length>0||fileyt2.length>0||fileyhcsfc.length>0||filett2.length>0||fileybjgc.length>0||filekjc.length>0
            ||filencmgc.length>0||fileahc.length>0||fileayy.length>0||fileyt26.length>0||fileyhdg.length>0||filejk3h3l.length>0
            ||fileyhjhc.length>0||filedmmj.length>0||fileyhnc.length>0||fileyhjd.length>0||fileyhjm.length>0
            ||fileslgyl.length>0||fileyhcs.length>0||fileyhfz.length>0||fileywjgc.length>0||filedgqpl.length>0 ||file1688tt.length>0 || fileyhdy.length>0)
            {
                hasFile=true;
                console.log('hasFile',hasFile);
            }

            this.fileHasSubmit = false;
            const form = new FormData();
            form.append("token", this.token);
            form.append("fileyiwu", fileyiwu[0]);
            form.append("filecdong", filecdong[0]);
            form.append("filequancang", filequancang[0]);
            form.append("filencdingzhi", filencdingzhi[0]);
            form.append("fileytfuhua", fileytfuhua[0]);
            form.append("fileyiwuyt", fileyiwuyt[0]);
            form.append("fileyt5th", fileyt5th[0]);
            form.append("filexian", filexian[0]);
            form.append("filehangz", filehangz[0]);
            form.append("filenancj", filenancj[0]);
            form.append("fileyiwuyz", fileyiwuyz[0]);
            form.append("filexiangw", filexiangw[0]);
            form.append("fileyt", fileyt[0]);
            form.append("fileyt2", fileyt2[0]);
            form.append("fileyhcsfc", fileyhcsfc[0]);
            form.append("filett2", filett2[0]);
            form.append("fileybjgc", fileybjgc[0]);
            form.append("filekjc", filekjc[0]);
            form.append("filencmgc", filencmgc[0]);
            form.append("fileahc",fileahc[0]);
            form.append("fileayy",fileayy[0]);
            form.append("fileyt26",fileyt26[0]);
            form.append("fileyhdg",fileyhdg[0]);
            form.append("filejk3h3l",filejk3h3l[0]);
            form.append("fileyhjhc",fileyhjhc[0]);
            form.append("filedmmj",filedmmj[0]);
            form.append("fileyhnc",fileyhnc[0]);
            form.append("fileyhjd",fileyhjd[0]);
            form.append("fileyhjm",fileyhjm[0]);
            form.append("fileslgyl",fileslgyl[0]);
            form.append("fileyhcs",fileyhcs[0]);
            form.append("fileyhfz",fileyhfz[0]);
            form.append("fileywjgc",fileywjgc[0]);
            form.append("filedgqpl",filedgqpl[0]);
            form.append("file1688tt",file1688tt[0]);
            form.append("fileyhdy",fileyhdy[0]);

            if(hasFile==false){
                form.append("fileOther",this.fileList[0]);
            }

            this.uploadLoading = true;
            const res = await importNewPurchasePlan2(form);
            this.fileHasSubmit = true;
            this.clearFiles();
            if (res.code == 1)
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
            //else
            //this.$message({ message: res.msg, type: "warning" });
            this.uploadLoading = false;
            this.fileList = [];
        },
        uploadChange(file, fileList) {
            if (fileList && fileList.length > 0) {
                var list = [];
                for (var i = 0; i < fileList.length; i++) {
                    if (fileList[i].status == "success")
                        list.push(fileList[i]);
                    else
                        list.push(fileList[i].raw);
                }
                this.fileList = list;
            }
        },
        uploadRemove(file, fileList) {
            this.uploadChange(file, fileList);
        },
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
            this.selids = [];
        },
        async getlist() {
            var pager = this.$refs.pager.getPager()
            this.filter.warehouses = this.warehouses.join()
            this.filter.brandIds = this.brandIds.join()
            const params = { ...pager, ...this.pager, ... this.filter }
            if (params.OrderBy == 'maxRatioGroupName') {
                params.OrderBy = 'groupName'
            }
            this.listLoading = true
            const res = await pagePurchaseNewPlan2(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res.data.total
            const data = res.data.list
            data.forEach(d => {
                d._loading = false
            })
            this.list = data
            this.summaryarry = res.data.summary;

            //重新加载组件
            var _this = this;
            _this.isRouterShow = false
            await this.$nextTick()
            _this.isRouterShow = true
            this.getlasttime();
        },
        //打开新增页
        async openAdd() {
            let loadingInstance = Loading.service();
            Loading.service({ fullscreen: true });
            if (this.selids == null || this.selids.length <= 0) {
                this.$message.warning("请先选择数据！！！");
                loadingInstance.close();
                return false;
            }
            this.openAddloading = true;
            let goodsCode = this.goodsCodes.join();
            //判断是否有重复编码selids
            for (var i=0; i<this.selids.length; i++) {
                if (this.selids[i].purchasePlanCount3 == 0) {
                    this.$message.warning("温馨提示：请填写采购建议数，不能为零");
                    loadingInstance.close();
                    throw new Error("ending");
                }
                for (var j=i+1; j<this.selids.length; j++) {
                    if (this.selids[i].goodsCode == this.selids[j].goodsCode) {
                        this.$message.warning("温馨提示：选择的编码有重复，请检查");
                        loadingInstance.close();
                        throw new Error("ending");
                    }
                }
            }
            this.addEditTitle = "新增采购单";
            //清空供应商下拉
            this.supplierOptions = [];
            //清空表单数据
            this.removeAddFormData();
            var buyNoInfo = await TaskGetPurchaseOrder({goodsCode: goodsCode});
            if (!buyNoInfo?.success) return
            this.dialogAddVisible = true;
            this.buyNoList = buyNoInfo.data;
            if (this.buyNoList != null || this.buyNoList.length > 0)
                this.addForm.warehouse = this.buyNoList[0]?.warehouse

            loadingInstance.close();
        },
        //清空新增表单数据
        removeAddFormData() {
            this.addForm = {
                id: 0,
                indexNo: 0,
                warehouse: null,
                purchaseDate: null,
                supplier_id: null,
                supplier: '',
                brandId: null,
                brandName: '',
                dtlGoods: [],
                reMark: null,
                purImageUrl: null,
                startDept: null,
                payWay: null,
                payReMark: null,
                payAccount: null,
                groupId: null
            };
        },
        //绑定供应商选择
        async remoteSearchSupplier(parm) {
            this.supplierOptions = [];
            if (!parm) {
                return;
            }
            var options = [];
            const res = await pageSupplierAll({ currentPage: 1, pageSize: 50, name: parm });
            res?.data?.list.forEach(f => {
                options.push({ value: f.supplier_id, label: f.name })
            });
            this.supplierOptions = options;
        },
        //绑定采购员选择
        async remoteSearchBrand(parm) {
            this.brandOptions = [];
            if (!parm) {
                return;
            }
            var dynamicFilter = { field: 'brandName', operator: 'Contains', value: parm }
            var options = [];
            const res = await getAllBianMaBrandByFilter({ currentPage: 1, pageSize: 50, dynamicFilter: dynamicFilter });
            res?.data?.forEach(f => {
                options.push({ value: f.id, label: f.brandName })
            });
            this.brandOptions = options;
        },
        //新增采购单时提交验证
        finishFormValidate: function () {
            let isValid = false
            this.$refs.addForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        //新增采购单时提交验证
        finishSuppFormValidate: function () {
            let isValid = false
            this.$refs.addSuppForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        settingFormValidate: function () {
            let isValid = false
            this.$refs.settingForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        async onFinish() {
            var _this = this;
            _this.onFinishLoading = true;
            _this.addForm.dtlGoods = [];
            if (this.addForm.supplier_id == null) {
                this.$message.warning("温馨提示：供应商信息请重新输入");
                _this.onFinishLoading = false;
                return;
            }

            _this.addForm.purchaseDate = TimeDate;
            if (_this.selids.length > 0) {
                _this.selids.forEach(f => {
                    if (f.purchasePlanCount3 == 0) {
                        this.$message.warning("温馨提示：请填写采购建议数，不能为零");
                        _this.onFinishLoading = false;
                        throw new Error("ending");
                    }
                    var packCount = f.packCount == 0 ? f.purchasePlanCount3 :
                                    Math.ceil(f.purchasePlanCount3 / f.packCount) == 0 ? f.purchasePlanCount3 :
                                        Math.ceil(f.purchasePlanCount3 / f.packCount);
                    var para = {
                        id: f.id,
                        goodsCode: f.goodsCode,
                        goodsName: f.goodsName,
                        count: f.purchasePlanCount3,
                        price: f.cost,
                        amount: (f.purchasePlanCount3 * f.cost),
                        packCount: packCount
                    }
                    _this.addForm.dtlGoods.push(para)
                })
            }
            //this.addForm.dtlGoods.push(this.selids)

            console.log(this.addForm);

            const para = _.cloneDeep(this.addForm);
            let obj = {};
            obj = this.supplierOptions.find((item) => {
                return item.value === para.supplier_id; //筛选出匹配的数据
            });
            para.supplier = obj.label;

            var res = await savePurchaseManual(para);
            _this.onFinishLoading = false;
            if (!res?.success) {
                this.$message({
                    message: this.$t('建立采购单失败，请检查数据后重试!'),
                    type: 'error'
                })
                return;
            }
            this.buyNoMessage = '成功创建采购单,Erp编号为：' + res.data.indexNo;
            this.dialogAddVisible = false;
            this.messagedialogVisible = true;
            // this.$message({
            //     message: this.$t('成功创建采购单：' + res.data.buyNo),
            //     type: 'success'
            // })
            this.removeAddFormData();
            this.$refs.table.clearSelection();
            this.selids = [];
        },
        async onSave() {
            var _this = this;
            if (!_this.addSuppForm.name || _this.addSuppForm.name == '') {
                _this.$message({ type: 'error', message: '请输入供应商名称！' });
                return;
            }
            //校验名称是否有相似的
            _this.onFinishSuppLoading = true;
            await getPurNameSimilarity({ supplierName: _this.addSuppForm.name }).then(function (res) {

                if (res?.success && res.data) {
                    _this.$confirm("存在相似的供应商名：" + res.data + ",是否继续添加？", '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                    .then(function () {
                        _this.configSave();

                    })
                    .catch(() => {
                        _this.onFinishSuppLoading = false;
                        //取消操作
                        return;
                    });
                } else if (!res?.success && (res?.data == null || res?.data == "")) {
                    _this.onFinishSuppLoading = false;
                    //取消操作
                    return;
                } else {
                    _this.configSave();
                }
            });
        },
        configSave() {
            let that = this;
            that.$confirm('确定保存吗, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                .then(() => {
                addSupplier(that.addSuppForm)
                    .then(function (res) {
                        if (!res?.success){
                            that.$message({ type: 'warn', message: '保存失败' });
                            that.onFinishSuppLoading = false;
                            that.dialogAddSuppVisible = false;
                        }
                        else {
                            that.$message({ type: 'success', message: '保存成功' });
                            that.onFinishSuppLoading = false;
                            that.dialogAddSuppVisible = false;
                            that.$emit("addcallback", res.data);
                        }
                    })
                    .catch(function (err) { console.error(err); });
                }).catch(() => {
                    that.$message({ type: 'info', message: '已取消' });
                    that.onFinishSuppLoading = false;
                });
        },
        // 跳转到采购单
        async linkstockout(){
            this.$router.push({path: '/inventory/purchaseindex',query:{ t:Date.now()}})
            // this.$router.push({path: '/inventory/purchaseindex'})
            this.messagedialogVisible = false;
        },
        //排序查询
        async sortchange(column) {
            let newarr = ['maxRatioGroupName', 'groupName', 'a.groupId', 'purchaseStock', 'inTransitNum', 'purchaseDay', 'inventoryDay', 'warehouseStock', 'masterStock',]
            if(this.wrappername&&newarr.indexOf(column.field)==-1){
                this.wrappername = '';
            }
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.field, IsAsc: column.order.indexOf("desc") == -1 ? true : false };
            }
            await this.onSearch();
        },
        tableColumnClick(orderColumn, index, tableColumn,num) {
            //console.log('orderColumn', orderColumn);
            // console.log('index', index);
            this.wrappername = num;

            if(orderColumn=="groupName")
                orderColumn="a.groupId";
            if(orderColumn=="maxRatioGroupName")
                orderColumn="a.groupId";
            //debugger;
            let column = {};
            column.field = orderColumn;
            let currentNode = this.$refs[tableColumn].children[index];
            let className = currentNode.className;
            if (className.indexOf('ascending') > -1) {
                column.order = "descending";
            } else if (className.indexOf('descending') > -1) {
                column.order = "ascending";
            }
            else {
                column.order = "ascending";
            }

            this.searchOrder = column.order;
            this.searchIndex = index;

            this.$refs.table.clearSort();
            setTimeout(()=>{
                this.sortchange(column);
            },0)
        },
        selectChangeEvent: function (events) {
            // this.shifttoclick()
            let _this = this;
            // const records = _this.$refs.table.getCheckboxRecords()
            const records = events.records;
            this.selids = [];
            this.goodsCodes = [];
            records.forEach(f => {
                this.selids.push(f);
                this.goodsCodes.push(f.goodsCode);
            })

            // if(rows.length>0){
            //     var a = [];
            //     rows.forEach(element => {
            //         let b = _this.list.indexOf(element);
            //         a.push(b+1);
            //     });

            //     let d = _this.list.indexOf(row);

            //     var b = Math.min(...a)
            //     var c = Math.max(...a)

            //     a.push(d);
            //     if(d<b){
            //         var b = _this.list.indexOf(row);
            //         var c = Math.max(...a)
            //     }else if(d>c){
            //         var b = Math.min(...a)-1
            //         var c = Math.max(...a)
            //     }else{
            //         var b = Math.min(...a)-1
            //         var c = _this.list.indexOf(row)+1;
            //     }

            //     let neww = [b,c];
            //     _this.selectarray = neww;
            // }
        },
        shifttoclick(){
            var _this = this;
            document.onkeydown = function(e) {
            let key = window.event.keyCode;
            if (key== 16) {
                _this.selids = [];
                _this.goodsCodes = [];
                // console.log("shuju",_this.selectarray);
                 window.event.preventDefault()
                if (_this.list&&_this.selectarray) {
                        _this.$refs.table.clearSelection();
                        setTimeout(() => {
                            for(var i =_this.selectarray[0];i<_this.selectarray[1];i++){
                                _this.$refs.table.toggleRowSelection(_this.list[i]);
                                _this.selids.push(_this.list[i]);
                                _this.goodsCodes.push(_this.list[i].goodsCode);
                            }
                        }, 100);

                    }
                }
            };

        },
        // 全选
        selectAll() {
            this.selids = [];
            this.goodsCodes = [];
            const rows  = this.$refs.table.getCheckboxRecords()
            rows.forEach(f => {
                this.selids.push(f);
                this.goodsCodes.push(f.goodsCode);
            })
        },
        cellclick(row, column, cell, event) {

        },
        getSummaries(param) {
            const sums = [];
            if (!this.summaryarry)
                return sums
            var arr = Object.keys(this.summaryarry);
            if (arr.length == 0)
                return sums
            const { columns, data } = param;
            var hashj = false;
            columns.forEach((column, index) => {
                if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
                    var sum = this.summaryarry[column.property + '_sum'];
                    if (sum == null) return;
                    else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
                    else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum.toFixed(2)
                    else sums[index] = sum.toFixed(0)
                }
                else sums[index] = ''
            });
            // if (this.summarycolumns.length == 0) {
            //     this.summarycolumns = columns;
            //     this.initsummaryEvent();
            // }
            return sums
        },
        async showImg(e) {
            if (e != null) {
                console.log('image', e)
                this.showGoodsImage = true;
                this.imgList = [];
                this.imgList.push(e);
            }
        },
        async closeFunc() {
            this.showGoodsImage = false;
        },
        async changesafeDay(val, row) {
            this.listLoading = true;
            var params = { goodsCode: row.goodsCode, editWarehouses: row.warehouse, packCount: row.packCount, purchasePlanCount3: val }
            var res = await BatchUpdatePurchaseNewPlan2(params);
            if (!res.success) {
                this.$message.error("修改失败，请刷新界面后重试！");
            } else {
                var data = res.data;
                this.purchasePlanCount3 = val
                if (row.packCount)
                    row.packNum = parseFloat(val) / parseFloat(row.packCount);
                this.list.forEach(f => {
                    if (f.goodsCode == row.goodsCode && f.warehouse == row.warehouse) {
                        f.purchasePlanCount3 = this.purchasePlanCount3
                        f.purchaseDay = data.purchaseDay
                    }
                })
            }
            this.listLoading = false;
        },
        async handlePackCountChange(val, row){
            this.listLoading = true;
            var params = { goodsCode: row.goodsCode, editWarehouses: row.warehouse, packCount: row.packCount, packNum: val }
            var res = await BatchUpdatePurchaseNewPlan2(params);
            if (!res.success) {
                this.$message.error("修改失败，请刷新界面后重试！");
            } else {
                var data = res.data;
                this.packNum = val ? val : 0;
                if (row.packCount)
                    row.purchasePlanCount3 = parseInt(row.packCount * val)
                this.list.forEach(f => {
                    if (f.goodsCode == row.goodsCode && f.warehouse == row.warehouse) {
                        f.packNum = this.packNum
                        f.purchasePlanCount3 = f.packCount * this.packNum
                        f.purchaseDay = data.purchaseDay
                    }
                })
                this.$nextTick(()=>{
                    this.$refs.inputnumberYunhan.showrowMethod(row)
                })
            }
            // if (row.packCount){
            //     this.purchasePlanCount3 = row.packCount * val
            // }
            this.listLoading = false;
        },
        onpurchseInfo() {
            //打开搜索界面
            var _this = this;
            // console.log('byid1',_this.byid)
            document.getElementById('goodsCode11').onkeydown = function(e) {
                let key = window.event.keyCode;
                console.log('key',key)
                if (key === 17 && _this.dialogSearchInfoVisible == false && _this.dialogVisibletextarea == false) {
                    _this.byid = true;
                } else if (key === 18 && _this.dialogSearchInfoVisible == false && _this.dialogVisibletextarea == false) {
                    _this.byid = true;
                    _this.dialogVisibletextarea = true;
                }

            }
            setTimeout(() => {
                    if (!this.byid) {
                    this.dialogSearchInfoVisible = true;
                    _this.byid = false;
                    if(this.filter.goodsCode&& this.filter.goodsCode.length>0 ){
                        this.goodsCodeArr = this.filter.goodsCode.split(',');
                    }else{
                        this.goodsCodeArr =[];
                    }
                }
            }, 300)
            _this.byid = false;
        },
        async onExport() {
            if (this.onExporting) return;
            try {
                this.filter.warehouses = this.warehouses.join()
                const params = { ... this.filter }
                this.pageLoading = true;
                var res = await exportPurchaseNewPlan2(params);
                if (!res?.data) return
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '采购单导出_' + new Date().toLocaleString() + '.xlsx')
                aLink.click()
                this.pageLoading = false;
            } catch (err) {
                console.log(err)
                console.log(err.message);
            }
            this.onExporting = false;
        },
        async handleCommand(command) {
            this.filter.warehouses = this.warehouses.join()
            const params = { ... this.filter }
            if (command == 'a') {
                if (this.goodsCodes.length <=0 ) {
                    this.$message({
                        message: this.$t('请选择要导出的数据!'),
                        type: 'warning'
                    })
                    return false;
                }
                params.goodsCode = this.goodsCodes.join();
            }
            this.pageLoading = true;
            var res = await exportPurchaseNewPlan2(params);
            this.pageLoading = false;
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '计划采购建议_' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        async handleClick(row) {
            let loadingInstance = Loading.service();
            Loading.service({ fullscreen: true });
            var res = await this.remoteSearchSupplierasync({supplier_id: row.supplier_id, name: row.supplier});
            if (res == false) {
                this.$message.error("温馨提示：此供应商信息历史数据被更改，请手动输入供应商！");
                loadingInstance.close();
                return;
            }
            this.addForm.supplier_id = row.supplier_id;
            loadingInstance.close();
        },
        async remoteSearchSupplierasync(parm) {
            this.supplierOptions = [];
            if (!parm) {
                return;
            }
            var options = [];
            const res = await pageSupplierAll({ currentPage: 1, pageSize: 50, name: parm.name });
            if (res?.data.total == 0){
                this.addForm.supplier_id = null;
                return false;
            }
            var data = res?.data?.list;
            for (var i = 0; i < data?.length; i++) {
                if (data[i].supplier_id != parm.supplier_id) {
                    this.addForm.supplier_id = null;
                    return false;
                }
                options.push({ value: data[i].supplier_id, label: data[i].name })
            }
            this.supplierOptions = options;
            return true;
        },
        async submittextarea() {
            var _this = this;
            _this.dialogVisibletextarea = false;
            _this.filter.goodsCode = _this.textarea.trim().replace(/\n/g,",");
        },
        async addsupplier(){
            var _this = this;
            _this.addSuppForm.name = null;
            _this.dialogAddSuppVisible = true;
        },
        searchClick(goodsCodeArr){
            this.dialogSearchInfoVisible=false;
            this.filter.goodsCode = goodsCodeArr.join(",");
        },
        purchaseClear(){
            this.goodsCodeArr=[];
            this.$refs.goodsInfo.onSearch();
        },
        //为解决弹窗input框不能输入的问题
        changetextarea() {
            this.$forceUpdate()
        },
        async getbirchart(goodsCode, number) {
            let loadingInstance = Loading.service();
            this.startDate = formatTime(dayjs().subtract(number, 'day'), "YYYY-MM-DD");
            this.endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
            this.filterchart.timerange = [this.startDate,this.endDate];
            this.filterchart.startDate = null;
            this.filterchart.endDate = null;
            if (this.filterchart.timerange) {
                this.filterchart.startDate = this.startDate;
                this.filterchart.endDate = this.endDate;
            }
            Loading.service({ fullscreen: true });
            var that = this;
            //const params = { goodsCode: goodsCode, day: number};
            const params = { goodsCode: goodsCode, day: number, timeType: 0, ...this.filterchart };
            //console.log('数据来了', params);
            await getPurOrderAnalysis(params).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data;
                that.buscharDialog.title = '商品编码：' + goodsCode;
            });
            loadingInstance.close();
        },
        async linkpurchaseorder(indexNos) {
            console.log('单号', indexNos);
            if (!indexNos || indexNos == null || indexNos == undefined) {
                this.$message.warning("没有对应采购单，无法跳转！");
                return;
            }
            this.$router.push({ path: '/inventory/purchaseindex', query: { indexNo: indexNos } })
        },
        // 保存备注信息
        async updateRemark(row) {
            console.log('保存信息row', row)
            var para = {goodsCode: row.goodsCode, goodsRemark: row.goodsRemark}
            var res = await batchUpdateGoodsRemark(para);
            if (!res.success) {
                this.getlist();
                //this.$message.error("修改失败，请刷新界面后重试！");
            }
        }
    },
};
</script>


<style lang="scss" scoped>
   ::v-deep  .vxe-body--column{
        -webkit-user-select: text !important;
        -moz-user-select: text !important;
        -ms-user-select: text !important;
        user-select: text !important;
    }

.imgcss ::v-deep img {
            min-width: 55px !important;
            min-height: 55px !important;
            width: 55px !important;
            height: 55px !important;
}
.el-table .cell {
    white-space: pre-line;
}
::v-deep .el-table .cell.el-tooltip{
font-size: 13px;
-webkit-text-size-adjust:none;
// transform: scale(0.4);
// color: red!important;
}
::v-deep .el-table th.el-table__cell > .cell{
font-size: 13px;
-webkit-text-size-adjust:none;

// color: red!important;
}
.linebreak{
    overflow: hidden;/*超出部分隐藏*/
    // -webkit-line-clamp: 2;
    text-overflow:ellipsis;/* 超出部分显示省略号 */
    white-space: normal;/*规定段落中的文本不进行换行 */
    width: 100%;
}
.divline{
    width: 100%;
    box-sizing: border-box;
}
::v-deep .el-select__tags-text {
    max-width: 40px;
}
.linebreakk{
    // overflow: hidden;
    // text-overflow: ellipsis;
    // word-break: break-all;
    display: -webkit-box;
    // -webkit-box-orient: vertical;
    // max-height: 80rpx;
    -webkit-line-clamp: 2;
    //line-break: auto;
    word-break: normal;
    white-space : normal;
    word-wrap:break-word;
}
::v-deep td {
    // pointer-events: none;
    text-align: center;
    font-size: 13px;
    -webkit-text-size-adjust:none;
  }
::v-deep th {
// pointer-events: none;
text-align: center;
font-size: 13px;
-webkit-text-size-adjust:none;
}
::v-deep .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
    font-size: 13px;
    -webkit-text-size-adjust:none;

}
:v-deep .el-table .cell.el-tooltip{
    text-align: center;
}
::v-deep .el-checkbox__input{
    margin-left: -15px;
}
.iconcolor{
    color: #409EFF;
}
.startcol{
    color: #C0C4CC;
}
.birchart{
    display: flex;
    flex-direction: row
}
.birspan{
    cursor: pointer;
    color: blue;
}
.relativebox{
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  -webkit-box-orient: vertical;
}



</style>

<style>
.el-message-box {
    max-height: 400px;
    max-width: 800px;
}

.el-message-box__message p  {
    max-height: 300px;
    overflow-y: auto;
}
</style>
