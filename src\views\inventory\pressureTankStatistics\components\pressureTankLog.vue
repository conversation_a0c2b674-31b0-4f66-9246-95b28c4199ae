<template>
    <MyContainer>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :rowHeight="60"
            showoverflow="ellipsis" @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
            :height="'100%'">
            <template #beforeContent="{ row }">
                <el-tooltip placement="top">
                    <div slot="content">{{ row.beforeContent }}<br />{{ row.afterContent }}</div>
                    <div style="display: flex;flex-direction: column;">
                        <div style="margin-bottom: 5px;">{{ row.beforeContent }}</div>
                        <div>{{ row.afterContent }}</div>
                    </div>
                </el-tooltip>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { GetPressureTankStatisticsLog } from '@/api/operatemanage/pressureTankStatistics'

const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdUserName', label: '操作人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '操作时间', },
    { width: 'auto', align: 'center', prop: 'beforeContent', label: '操作内容' },
]
export default {
    name: "scanCodePage",
    props: {
        id: {
            type: String,
            default: ''
        }
    },
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                id: this.id
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetPressureTankStatisticsLog(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
