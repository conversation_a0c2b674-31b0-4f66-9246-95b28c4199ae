<template>
  <my-container>

    <!-- 扣款原因  -->
    <el-row :gutter="0" class="row-condition">
      <el-col :span="23" :offset="1">
        <p class="my-title">扣款原因</p>
      </el-col>
    </el-row>
    <!-- 扣款原因-总扣款  -->
    <el-row v-if="sumData && sumData.sumZkkIll && sumData.sumZkkIll.orderCount > 0">
      <el-col :offset="1" :span="23" style="overflow: auto;white-space: nowrap;text-align: left;">
        <el-card :body-style="cardBodyStyle" class="card-header"
          shadow="always"  v-loading="isorderPinSumloading">
          <div class="grid-header">
            总扣款
            <el-tooltip class="item" effect="dark" content="总扣款，包含扣款原因(缺货+延迟发货+虚假发货)" placement="top">
              <i class="el-input__icon el-icon-question"></i>
            </el-tooltip>
          </div>
          <div class="grid-text" >
            线上订单量
            <span class="canclick" @click="showchart(0, sumData.sumZkkIll.illegalType)">{{ sumData.sumZkkIll.orderCount
            }}</span>
            <span class="detailsclick" @click="showtabledetailIllegalType(sumData.sumZkkIll.illegalType)"> 详情</span>
          </div>
          <div class="grid-text" >
            金额
            <span class="canclick" @click="showchart(0, sumData.sumZkkIll.illegalType)">{{ sumData.sumZkkIll.amountPaid
            }}</span>
          </div>
          <div class="grid-text" v-if="ysLxVisable">
            预售
            <span class="cantclick">{{ sumData.sumZkkIll.ysRatio }}%</span>
            非预售
            <span class="cantclick">{{ sumData.sumZkkIll.fysRatio }}%</span>
          </div>
        </el-card>

        <template v-for="item in sumData.sumZkkIllList">
          <el-card :body-style="cardBodyStyle" class="card-body"
            shadow="always"  v-loading="isorderPinSumloading">
            <br />
            <div class="grid-header">
              {{ item.illegalTypeName }}
              <span class="cantclick">{{ item.groupRatio }}%</span>
            </div>
            <div class="grid-text" >
              线上订单量
              <span class="canclick" @click="showchart(0, item.illegalType)">{{ item.orderCount }}</span>
              <span class="detailsclick" @click="showtabledetailIllegalType(item.illegalType)"> 详情</span>
            </div>
            <div class="grid-text" >
              金额
              <span class="canclick" @click="showchart(0, item.illegalType)">{{ item.amountPaid }}</span>
            </div>
            <div class="grid-text"  v-if="ysLxVisable">
              预售
              <span class="cantclick">{{ item.ysRatio }}%</span>
              非预售
              <span class="cantclick">{{ item.fysRatio }}%</span>
            </div>
          </el-card>

        </template>

      </el-col>
    </el-row>
    <!-- 扣款原因-非总扣款  -->
    <el-row v-if="sumData && sumData.sumNotZkkIll && sumData.sumNotZkkIll.orderCount > 0">
      <el-col :offset="1" :span="23" style="overflow: auto;white-space: nowrap;text-align: left;">
        <el-card :body-style="cardBodyStyle" class="card-header"
          shadow="always"  v-loading="isorderPinSumloading">
          <div class="grid-header">
            其他
            <el-tooltip class="item" effect="dark" content="不包含扣款(缺货+延迟发货+虚假发货)" placement="top">
              <i class="el-input__icon el-icon-question"></i>
            </el-tooltip>
          </div>
          <div class="grid-text" >
            线上订单量
            <span class="canclick" @click="showchart(0, sumData.sumNotZkkIll.illegalType)">{{
              sumData.sumNotZkkIll.orderCount }}</span>
            <span class="detailsclick" @click="showtabledetailIllegalType(sumData.sumNotZkkIll.illegalType)"> 详情</span>
          </div>
          <div class="grid-text" >
            金额
            <span class="canclick" @click="showchart(0, sumData.sumNotZkkIll.illegalType)">{{
              sumData.sumNotZkkIll.amountPaid }}</span>
          </div>
          <div class="grid-text"  v-if="ysLxVisable">
            预售
            <span class="cantclick">{{ sumData.sumNotZkkIll.ysRatio }}%</span>
            非预售
            <span class="cantclick">{{ sumData.sumNotZkkIll.fysRatio }}%</span>
          </div>
        </el-card>

        <template v-for="item in sumData.sumNotZkkIllList">
          <el-card :body-style="cardBodyStyle" class="card-body"
            shadow="always"  v-loading="isorderPinSumloading">
            <br />
            <div class="grid-header">
              {{ item.illegalTypeName }}
              <span class="cantclick">{{ item.groupRatio }}%</span>
            </div>
            <div class="grid-text" >
              线上订单量
              <span class="canclick" @click="showchart(0, item.illegalType)">{{ item.orderCount }}</span>
              <span class="detailsclick" @click="showtabledetailIllegalType(item.illegalType)"> 详情</span>
            </div>
            <div class="grid-text" >
              金额
              <span class="canclick" @click="showchart(0, item.illegalType)">{{ item.amountPaid }}</span>
            </div>
            <div class="grid-text"  v-if="ysLxVisable">
              预售
              <span class="cantclick">{{ item.ysRatio }}%</span>
              非预售
              <span class="cantclick">{{ item.fysRatio }}%</span>
            </div>
          </el-card>

        </template>

      </el-col>
    </el-row>



    <!-- 责任  -->
    <el-row :gutter="0" class="row-condition">
      <el-col :span="23" :offset="1">
        <p class="my-title">责任</p>
      </el-col>
    </el-row>
    <!-- 责任-仓库  -->
    <el-row v-if="sumData && sumData.sumDept_CK && sumData.sumDept_CK.orderNumber > 0">
      <el-col :offset="1" :span="23" style="overflow: auto;white-space: nowrap;text-align: left;">
        <el-card :body-style="cardBodyStyle" class="card-header"
          shadow="always"   v-loading="isorderPinSumloading">
          <div class="grid-header">
            仓库
            <span class="cantclick">{{ sumData.sumDept_CK.groupRatio }}%</span>

            <el-tooltip class="item" effect="dark" content="扣款责任为：仓库" placement="top">
              <i class="el-input__icon el-icon-question"></i>
            </el-tooltip>

          </div>

          <div class="grid-text" >
            线上订单量
            <span class="canclick" @click="showchart(22, sumData.sumDept_CK.dept)">{{ sumData.sumDept_CK.orderNumber
            }}</span>
            <span class="detailsclick" @click="showtabledetailZrDept(sumData.sumDept_CK.dept)"> 详情</span>
          </div>
          <div class="grid-text" >
            金额
            <span class="canclick" @click="showchart(22, sumData.sumDept_CK.dept)">{{ sumData.sumDept_CK.deptSumAmountPaid
            }}</span>
          </div>
          <div class="grid-text"  v-if="ysLxVisable">
            预售
            <span class="cantclick">{{ sumData.sumDept_CK.ysRatio }}%</span>
            非预售
            <span class="cantclick">{{ sumData.sumDept_CK.fysRatio }}%</span>
          </div>
        </el-card>
        <el-card :body-style="cardBodyStyle" class="card-header2"
          shadow="always"  v-loading="isorderPinSumloading">
          <div class="grid-header">
            发货仓
            <el-button type="text" @click="loadSumDept_CK_WmsId('')">全部</el-button>
          </div>
          
          <template v-for="item in sumData.sumDept_CK_FHC_List">
            
            <el-button type="text"              
              @click="loadSumDept_CK_WmsId(item.wmsCoId)">             

              <span class="region-title" :title="item.sendWarehouseName">
                {{ item.sendWarehouseName }} 
              </span>
              <span class="region-num region-count">
                {{ item.orderCount }}单
              </span>
              <span class="region-num region-amount">
                {{ item.amountPaid }}元
              </span>
              <span class="region-num region-ratio">
                {{ item.groupRatio }}% 
              </span>

            </el-button>
            <br />

              <!-- :disabled="!!sumData.sumDept_CK_List_WmsId && sumData.sumDept_CK_List_WmsId == item.wmsCoId" -->
          </template>

        </el-card>
        <template v-for="item in sumData.sumDept_CK_List">
          <el-card :body-style="cardBodyStyle" class="card-body"
            shadow="always" v-loading="isorderPinSumloading || sumDept_CK_List_Loading">
            <br />
            <div class="grid-header">
              {{ item.dept }}
              <span class="cantclick">{{ item.groupRatio }}%</span>
            </div>
            <div class="grid-text">
              线上订单量
              <span class="canclick" @click="showchart(2, item.dept, '', 'sumDept_CK_List_WmsId')">{{ item.orderNumber
              }}</span>
              <span class="detailsclick" @click="showtabledetailDept(item.dept, 'sumDept_CK_List_WmsId')"> 详情</span>
            </div>
            <div class="grid-text" >
              金额
              <span class="canclick" @click="showchart(2, item.dept, '', 'sumDept_CK_List_WmsId')">{{ item.deptSumAmountPaid
              }}</span>
            </div>
            <div class="grid-text"  v-if="ysLxVisable">
              预售
              <span class="cantclick">{{ item.ysRatio }}%</span>
              非预售
              <span class="cantclick">{{ item.fysRatio }}%</span>
            </div>
          </el-card>

        </template>

      </el-col>
    </el-row>
    <!-- 责任-运营  -->
    <el-row v-if="sumData && sumData.sumDept_YY && sumData.sumDept_YY.orderNumber > 0">
      <el-col :offset="1" :span="23" style="overflow: auto;white-space: nowrap;text-align: left;">
        <el-card :body-style="cardBodyStyle" class="card-header"
          shadow="always"  v-loading="isorderPinSumloading">
          <div class="grid-header">
            运营
            <span class="cantclick">{{ sumData.sumDept_YY.groupRatio }}%</span>
            <el-tooltip class="item" effect="dark" content="扣款责任为：运营" placement="top">
              <i class="el-input__icon el-icon-question"></i>
            </el-tooltip>
          </div>
          <div class="grid-text" >
            线上订单量
            <span class="canclick" @click="showchart(22, sumData.sumDept_YY.dept)">{{ sumData.sumDept_YY.orderNumber
            }}</span>
            <span class="detailsclick" @click="showtabledetailZrDept(sumData.sumDept_YY.dept)"> 详情</span>
          </div>
          <div class="grid-text" >
            金额
            <span class="canclick" @click="showchart(22, sumData.sumDept_YY.dept)">{{ sumData.sumDept_YY.deptSumAmountPaid
            }}</span>
          </div>
          <div class="grid-text"  v-if="ysLxVisable">
            预售
            <span class="cantclick">{{ sumData.sumDept_YY.ysRatio }}%</span>
            非预售
            <span class="cantclick">{{ sumData.sumDept_YY.fysRatio }}%</span>
          </div>
        </el-card>
        <template v-for="item in sumData.sumDept_YY_List">
          <el-card :body-style="cardBodyStyle" class="card-body"
            shadow="always"  v-loading="isorderPinSumloading">
            <br />
            <div class="grid-header">
              {{ item.groupName }}
              <span class="cantclick">{{ item.groupRatio }}%</span>
            </div>
            <div class="grid-text" >
              线上订单量
              <span class="canclick" @click="() => {
                sumData.sumDept_YY_List_groupId = item.groupId;
                showchart(22, sumData.sumDept_YY.dept, item.groupName, 'sumDept_YY_List_groupId');
              }">{{
  item.orderCount }}</span>
              <span class="detailsclick" @click="() => {
                sumData.sumDept_YY_List_groupId = item.groupId;
                showtabledetailZrDept(sumData.sumDept_YY.dept, item.groupName, 'sumDept_YY_List_groupId');
              }"> 详情</span>
            
            </div>
            <div class="grid-text" >
              金额
              <span class="canclick" @click="() => {
                sumData.sumDept_YY_List_groupId = item.groupId;
                showchart(22, sumData.sumDept_YY.dept, item.groupName, 'sumDept_YY_List_groupId');
              }">{{
  item.amountPaid }}</span>
            </div>
            <div class="grid-text"  v-if="ysLxVisable">
              预售
              <span class="cantclick">{{ item.ysRatio }}%</span>
              非预售
              <span class="cantclick">{{ item.fysRatio }}%</span>
            </div>
          </el-card>

        </template>

      </el-col>
    </el-row>
    <!-- 责任-快递  -->
    <el-row v-if="sumData && sumData.sumDept_KD && sumData.sumDept_KD.orderNumber > 0"
      :data-let2="item = sumData.sumDept_KD">
      <el-col :offset="1" :span="23" style="overflow: auto;white-space: nowrap;text-align: left;">
        <el-card :body-style="cardBodyStyle" class="card-header"
          shadow="always"  v-loading="isorderPinSumloading">
          <div class="grid-header">
            快递
            <span class="cantclick">{{ item.groupRatio }}%</span>
            <el-tooltip class="item" effect="dark" content="扣款责任为：快递" placement="top">
              <i class="el-input__icon el-icon-question"></i>
            </el-tooltip>
          </div>
          <div class="grid-text" >
            线上订单量
            <span class="canclick" @click="showchart(22, sumData.sumDept_KD.dept)">{{ item.orderNumber }}</span>
            <span class="detailsclick" @click="showtabledetailZrDept(sumData.sumDept_KD.dept)"> 详情</span>
          </div>
          <div class="grid-text" >
            金额
            <span class="canclick" @click="showchart(22, sumData.sumDept_KD.dept)">{{ item.deptSumAmountPaid }}</span>
          </div>
          <div class="grid-text"  v-if="ysLxVisable">
            预售
            <span class="cantclick">{{ item.ysRatio }}%</span>
            非预售
            <span class="cantclick">{{ item.fysRatio }}%</span>
          </div>
        </el-card>
        <el-card :body-style="cardBodyStyle" class="card-header2-short"
          shadow="always"  v-loading="isorderPinSumloading">
          <div class="grid-header">
            区域
            <el-button type="text" @click="loadSumDept_KD_Region('')">全部</el-button>
          </div>
          
          <template v-for="item in sumData.sumDept_KD_QY_List">
            
            <el-button type="text"                      
              @click="loadSumDept_KD_Region(item.dept)">   
              <span class="region-title-short">
                {{ item.dept }} 
              </span>
              <span class="region-num region-count" :title="item.orderNumber"> 
                {{ item.orderNumber }}单
              </span>
              <span class="region-num region-amount" :title="item.deptSumAmountPaid">
                {{ item.deptSumAmountPaid }}元
              </span>
              <span class="region-num region-ratio">
                {{ item.groupRatio }}% 
              </span>

            </el-button>
            <br />
              <!-- :disabled="!!sumData.sumDept_KD_QY_List_Region && sumData.sumDept_KD_QY_List_Region == item.dept" -->
          </template>
        </el-card>
        <template v-for="item in sumData.sumDept_KD_List">
          <el-card :body-style="cardBodyStyle" class="card-body"
            shadow="always"  v-loading="sumDept_KD_List_Loading || isorderPinSumloading">
            <br />
            <div class="grid-header">
              {{ item.expressCompanyName }}
              <span class="cantclick">{{ item.groupRatio }}%</span>
            </div>
            <div class="grid-text" >
              线上订单量
              <span class="canclick" @click="()=>{
                sumData.sumDept_KD_List_expressCompanyName = item.expressCompanyName;
                showchart(22, '快递',item.expressCompanyName,'sumDept_KD_List_expressCompanyName');}">{{ item.orderCount }}</span>
              <span class="detailsclick" @click="() => {
                sumData.sumDept_KD_List_expressCompanyName = item.expressCompanyName;
                showtabledetailZrDept(sumData.sumDept_KD.dept, item.expressCompanyName, 'sumDept_KD_List_expressCompanyName');
              }"> 详情</span>
             
            </div>
            <div class="grid-text" >
              金额
              <span class="canclick" @click="()=>{
                sumData.sumDept_KD_List_expressCompanyName = item.expressCompanyName;
                showchart(22, '快递',item.expressCompanyName,'sumDept_KD_List_expressCompanyName');}">{{ item.amountPaid }}</span>
            </div>
            <div class="grid-text"  v-if="ysLxVisable">
              预售
              <span class="cantclick">{{ item.ysRatio }}%</span>
              非预售
              <span class="cantclick">{{ item.fysRatio }}%</span>
            </div>
          </el-card>

        </template>

      </el-col>
    </el-row>
    <!-- 责任-采购  -->
    <el-row v-if="sumData && sumData.sumDept_CG && sumData.sumDept_CG.orderNumber > 0"
      :data-let3="item = sumData.sumDept_CG">
      <el-col :offset="1" :span="23" style="overflow: auto;white-space: nowrap;text-align: left;">
        <el-card :body-style="cardBodyStyle" class="card-header"
          shadow="always"  v-loading="isorderPinSumloading">
          <div class="grid-header">
            采购
            <span class="cantclick">{{ item.groupRatio }}%</span>
            <el-tooltip class="item" effect="dark" content="扣款责任为：采购" placement="top">
              <i class="el-input__icon el-icon-question"></i>
            </el-tooltip>
          </div>
          <div class="grid-text" >
            线上订单量
            <span class="canclick" @click="showchart(22, sumData.sumDept_CG.dept)">{{ item.orderNumber }}</span>
            <span class="detailsclick" @click="showtabledetailZrDept(sumData.sumDept_CG.dept)"> 详情</span>
          </div>
          <div class="grid-text" >
            金额
            <span class="canclick" @click="showchart(22, sumData.sumDept_CG.dept)">{{ item.deptSumAmountPaid }}</span>
          </div>
          <div class="grid-text"  v-if="ysLxVisable">
            预售
            <span class="cantclick">{{ item.ysRatio }}%</span>
            非预售
            <span class="cantclick">{{ item.fysRatio }}%</span>
          </div>
        </el-card>
        <template v-for="item in sumData.sumDept_CG_List">
          <el-card :body-style="cardBodyStyle" class="card-body"
            shadow="always"  v-loading="isorderPinSumloading">
            <br />
            <div class="grid-header">
              {{ item.brandName }}
              <span class="cantclick">{{ item.groupRatio }}%</span>
            </div>
            <div class="grid-text" >
              线上订单量
              <span class="canclick" @click="()=>{
                sumData.sumDept_CG_List_brandId = item.brandId;
                showchart(22, '采购',item.brandName,'sumDept_CG_List_brandId');}">{{ item.orderCount }}</span>
              <span class="detailsclick" @click="() => {
                sumData.sumDept_CG_List_brandId = item.brandId;
                showtabledetailZrDept(sumData.sumDept_CG.dept, item.brandName, 'sumDept_CG_List_brandId');
              }"> 详情</span>
              <!-- <span class="detailsclick" @click="showtabledetailGroup(item.groupId)"> 详情</span> -->
            </div>
            <div class="grid-text" >
              金额
              <span class="canclick" @click="()=>{
                sumData.sumDept_CG_List_brandId = item.brandId;
                showchart(22, '采购',item.brandName,'sumDept_CG_List_brandId');}">{{ item.amountPaid }}</span>
            </div>
            <div class="grid-text"  v-if="ysLxVisable">
              预售
              <span class="cantclick">{{ item.ysRatio }}%</span>
              非预售
              <span class="cantclick">{{ item.fysRatio }}%</span>
            </div>
          </el-card>

        </template>

      </el-col>
    </el-row>
    <!-- 责任-未计算、机器人  -->
    <el-row v-if="sumData && sumData.sumDept_OtherList && sumData.sumDept_OtherList.length > 0"    >
      <el-col :offset="1" :span="23" style="overflow: auto;white-space: nowrap;text-align: left;">
        <el-card :body-style="cardBodyStyle" class="card-header-err" v-for="item in sumData.sumDept_OtherList"
          shadow="always"  v-loading="isorderPinSumloading">
          <div class="grid-header">
            {{ item.dept }}
            <span class="cantclick">{{ item.groupRatio }}%</span>
            <el-tooltip v-if="item.dept=='未计算'" class="item" effect="dark" content="扣款原因(缺货+延迟发货+虚假发货),未能计算出责任数据" placement="top">
              <i class="el-input__icon el-icon-question"></i>
            </el-tooltip>
            <el-tooltip v-else  class="item" effect="dark" :content="item.dept" placement="top">
              <i class="el-input__icon el-icon-question"></i>
            </el-tooltip>
          </div>
          <div class="grid-text" >
            线上订单量
            <span class="canclick" @click="()=>{
              if(item.dept=='未计算')
                showchart(2, item.dept);
              else
                showchart(22, item.dept);
              }">{{ item.orderNumber }}</span>
            <span class="detailsclick" @click="()=>{
              if(item.dept=='未计算')
                showtabledetailDept(item.dept);
              else
                showtabledetailZrDept(item.dept);
              }"> 详情</span>
          </div>
          <div class="grid-text" >
            金额
            <span class="canclick" @click="()=>{
              if(item.dept=='未计算')
                showchart(2, item.dept);
              else
                showchart(22, item.dept);
              }">{{ item.deptSumAmountPaid }}</span>
          </div>
          <div class="grid-text"  v-if="ysLxVisable">
            预售
            <span class="cantclick">{{ item.ysRatio }}%</span>
            非预售
            <span class="cantclick">{{ item.fysRatio }}%</span>
          </div>
        </el-card>     

      </el-col>
    </el-row>


    <!-- 快递公司  -->
    <el-row :gutter="0" class="row-condition">
      <el-col :span="23" :offset="1">
        <p class="my-title">快递公司</p>
      </el-col>
    </el-row>
    <el-row v-if="sumData && sumData.sumExpress && sumData.sumExpress.orderCount > 0"
      :data-let4="item = sumData.sumExpress">
      <el-col :offset="1" :span="23" style="overflow: auto;white-space: nowrap;text-align: left;">
        <el-card :body-style="cardBodyStyle" class="card-header"
          shadow="always"  v-loading="isorderPinSumloading">
          <div class="grid-header">
            快递
            <el-tooltip class="item" effect="dark" content="与扣款责任无关，纯快递公司分组统计" placement="top">
              <i class="el-input__icon el-icon-question"></i>
            </el-tooltip>
          </div>
          <div class="grid-text" >
            线上订单量
            <span class="canclick" @click="showchart(4, '')">{{ item.orderCount }}</span>
            <span class="detailsclick" @click="showtabledetailExpressCompany('')"> 详情</span>
          </div>
          <div class="grid-text" >
            金额
            <span class="canclick" @click="showchart(4, '')">{{ item.amountPaid }}</span>
          </div>
          <div class="grid-text"  v-if="ysLxVisable">
            预售
            <span class="cantclick">{{ item.ysRatio }}%</span>
            非预售
            <span class="cantclick">{{ item.fysRatio }}%</span>
          </div>
        </el-card>
        <el-card :body-style="cardBodyStyle" class="card-header2-short"
          shadow="always"  v-loading="isorderPinSumloading"> 
          <div class="grid-header">
            区域
          
            <el-button type="text" @click="sumExpressList_Region('')">全部</el-button>
          </div>
          <template v-for="item in sumData.sumExpressQYList">
            
            <el-button type="text"     
              @click="sumExpressList_Region(item.expressCompanyName)">
              <span class="region-title-short">
                {{ item.expressCompanyName }} 
              </span>
              <span class="region-num region-count" :title="item.orderCount">
                {{ item.orderCount }}单
              </span>
              <span class="region-num region-amount" :title="item.amountPaid">
                {{ item.amountPaid }}元
              </span>
              <span class="region-num region-ratio" >
                {{ item.groupRatio }}% 
              </span>             
              
            </el-button>
              <!-- :disabled="!!sumData.sumExpressList_Region && sumData.sumExpressList_Region == item.dept" -->
              <br />
          </template>
        </el-card>
        <template v-for="item in sumData.sumExpressList">
          <el-card :body-style="cardBodyStyle" class="card-body"
            shadow="always"  v-loading="isorderPinSumloading || sumExpressList_Loading">
            <br />
            <div class="grid-header">
              {{ item.expressCompanyName }}
              <el-tooltip v-if="item.expressCompanyName == '未计算'" class="cardBodyStyle-tip" effect="dark" content="包含(缺货+延迟发货+虚假发货)"
                placement="top">
                  <i class="el-input__icon el-icon-question"></i>
                </el-tooltip>
                <el-tooltip v-if="item.expressCompanyName == '无需计算'" class="cardBodyStyle-tip" effect="dark"
                  content="除(缺货+延迟发货+虚假发货)之外记录，包含(商家责任退货、售后补偿、投诉赔偿、质量问题、其他 等)" placement="top">
                  <i class="el-input__icon el-icon-question"></i>
                </el-tooltip>
              <span class="cantclick">{{ item.groupRatio }}%</span>
            </div>
            <div class="grid-text" >
              线上订单量
              <span class="canclick" @click="showchart(4, item.expressCompanyName)">{{ item.orderCount }}</span>
              <span class="detailsclick" @click="showtabledetailExpressCompany(item.expressCompanyName)"> 详情</span>
            </div>
            <div class="grid-text" >
              金额
              <span class="canclick" @click="showchart(4, item.expressCompanyName)">{{ item.amountPaid }}</span>
            </div>
            <div class="grid-text"  v-if="ysLxVisable">
              预售
              <span class="cantclick">{{ item.ysRatio }}%</span>
              非预售
              <span class="cantclick">{{ item.fysRatio }}%</span>
            </div>
          </el-card>

        </template>
      </el-col>
    </el-row>


    <!-- 发货仓库 -->
    <el-row :gutter="0" class="row-condition">
      <el-col :span="23" :offset="1">
        <div class="my-title">发货仓库</div>
      </el-col>
    </el-row>
    <el-row v-if="sumData && sumData.sumWare && sumData.sumWare.orderCount > 0" :data-let5="item = sumData.sumWare">
      <el-col :offset="1" :span="23" style="overflow: auto;white-space: nowrap;text-align: left;">
        <el-card :body-style="cardBodyStyle" class="card-header"
          shadow="always"  v-loading="isorderPinSumloading">
          <div class="grid-header">
            发货仓
            <el-tooltip class="item" effect="dark" content="与扣款责任无关，纯发货仓分组统计" placement="top">
              <i class="el-input__icon el-icon-question"></i>
            </el-tooltip>
          </div>
          <div class="grid-text" >
            线上订单量
            <span class="canclick" @click="showchart(3, '')">{{ item.orderCount }}</span>
            <span class="detailsclick" @click="showtabledetailSendWarehouse('')"> 详情</span>
          </div>
          <div class="grid-text" >
            金额
            <span class="canclick" @click="showchart(3, '')">{{ item.amountPaid }}</span>
          </div>
          <div class="grid-text"  v-if="ysLxVisable">
            预售
            <span class="cantclick">{{ item.ysRatio }}%</span>
            非预售
            <span class="cantclick">{{ item.fysRatio }}%</span>
          </div>
        </el-card>
        <template v-for="item in sumData.sumWareList">
          <el-card :body-style="cardBodyStyle" class="card-body"
            shadow="always"  v-loading="isorderPinSumloading">
            <br />
            <div class="grid-header">
              {{ item.sendWarehouseName }}
              <el-tooltip v-if="item.sendWarehouseName == '未计算'" class="cardBodyStyle-tip" effect="dark" content="包含(缺货+延迟发货+虚假发货)"
                placement="top">
                  <i class="el-input__icon el-icon-question"></i>
                </el-tooltip>
                <el-tooltip v-if="item.sendWarehouseName == '无需计算'" class="cardBodyStyle-tip" effect="dark"
                  content="除(缺货+延迟发货+虚假发货)之外记录，包含(商家责任退货、售后补偿、投诉赔偿、质量问题、其他 等)" placement="top">
                  <i class="el-input__icon el-icon-question"></i>
                </el-tooltip>
              <span class="cantclick">{{ item.groupRatio }}%</span>
            </div>
            <div class="grid-text" >
              线上订单量
              <span class="canclick" @click="showchart(3, item.wmsCoId)">{{ item.orderCount }}</span>
              <span class="detailsclick" @click="showtabledetailSendWarehouse(item.wmsCoId)"> 详情</span>
            </div>
            <div class="grid-text" >
              金额
              <span class="canclick" @click="showchart(3, item.wmsCoId)">{{ item.amountPaid }}</span>
            </div>
            <div class="grid-text"  v-if="ysLxVisable">
              预售
              <span class="cantclick">{{ item.ysRatio }}%</span>
              非预售
              <span class="cantclick">{{ item.fysRatio }}%</span>
            </div>
          </el-card>

        </template>
      </el-col>
    </el-row>


    <el-dialog :visible.sync="showDetailVisible.visible" :title="showDetailVisible.title" width="72%" v-dialogDrag
      append-to-body>
      <span>
        <template>
          <el-date-picker v-model="filter.timerange" type="datetimerange" :picker-options="pickerOptions1"
            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期"></el-date-picker>
          <el-button type="primary" @click="onSearch1()">查询</el-button>
        </template>
      </span>
      <span>
        <buschar v-if="showDetailVisible.visible" ref="buschar" :analysisData="showDetailVisible.data"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showDetailVisible.visible = false">关闭</el-button>
      </span>
    </el-dialog>
    <!-- 详情表格 -->

    <el-dialog :visible.sync="showDetailVisibleTable" width="96%" v-dialogDrag append-to-body>

      <el-button type="primary" @click="onExport">导出</el-button>
      <orderIllgalBoardTableTx ref="orderIllgalBoardTableTx" :filter="orderIllgalBoardTableTx.filter"
        style="height:610px;"></orderIllgalBoardTableTx>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showDetailVisibleTable = false">关闭</el-button>
      </span>
    </el-dialog>


  </my-container>
</template>

<script>
import {
  getDirectorList,
  getDirectorGroupList,
  getProductBrandPageList,
  getList as getshopList,
} from "@/api/operatemanage/base/shop";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import buschar from "@/components/Bus/buschar";
import orderIllgalBoardTableTx from '@/views/order/orderillegal/orderIllgalBoardTableTx'
import {
  GetWithholdSumTxAsync,
  getOrderWithholdTxListChart as getOrderWithholdListChart,
  exportOrderWithholdTbaleTXList
} from "@/api/order/orderdeductmoney"

export default {
  name: "YunhanAdminOrderillegalboard",
  components: {
    cesTable,
    MyContainer,
    MyConfirmButton,
    orderIllgalBoardTableTx,
    buschar,
  },
  props: {
    filter: {},
  },
  data() {
    return {
      orderIllgalBoardTableTx: {
        filter: {
          timerange: [],
          remark: '',
          groupId: '',
          dutyDept: ''
        }
      },
      ysLxVisable:false,
      zkkContent: "缺货+延迟发货+虚假发货",
      pager: { OrderBy: "OccurrenceTime", IsAsc: false },
      that: this,
      activeName: "first",
      showDetailVisibleTable: false,

      directorList: [],
      directorGroupList: [],
      
      groupSumList: [],
      deptList: [],
      orderPinSumList: {},
      illegalTypeList: [],
      filter1: {
        dutyDept: null,
        startDate: null,
        endDate: null,
        platform: null,
        shopId: null,
        groupId: null,
        proCode: null,
        sendWarehouse: null,
        illegalType: null,
        illegalTypes: null,
        expressCompany: null,
        operateSpecialId: null,
        occurrenceTime: null,
        timerange1: [],
        ErrorType: '',
        startDate1: null,
        endDate1: null,
        timerange: [
          formatTime(dayjs().subtract(7, "day"), "YYYY-MM-DD"),
          formatTime(new Date(), "YYYY-MM-DD"),
        ],
      },
      filter2: {
        proCode: null,
      },
      filterInner: {
        illegalType: null,
        groupId: null,
      },

      pickerOptions1: {
        shortcuts: [{
          text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近十五天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },

      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      chatType: '',
      chatVal: '',
      isorderPinSumloading: false,
      platformList: [],
      onSearch1T: '',
      dialogVisible: false,
      showDetailVisible: { visible: false, title: "", data: [] },
      
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,
      deleteLoading: false,
      uploadLoading: false,
      fileHasSubmit: false,
      formtitle: "新增",
      fileList: [],
      selids: [], //选择的id
      orderIllgalBoardTablePdd: {
        filter: {
          timerange: [],
          timerange2: [],
          timerange3: [],
          remark: '',
          groupId: '',
          dutyDept: '',
          IllegalType: '',
          SendWarehouse: '',
          ExpressCompany: ''
        }
      },
      sumData: {

      },
      sumDept_CK_List_Loading: false,
      sumDept_KD_List_Loading:false,
      sumExpressList_Loading:false,
      cardBodyStyle:{ padding: '4px', paddingLeft: '10px', paddingRight: '10px' }
    };
  },

  async mounted() {
    let illegalType = await ruleIllegalType();
    this.illegalTypeList = illegalType.options;

    const res1 = await getDirectorList({});
    const res2 = await getDirectorGroupList({});

    this.directorList = res1.data;
    this.directorGroupList = [{ key: "0", value: "未知" }].concat(
      res2.data || []
    );

  },

  methods: {
    //导出
    async onExport() {
      let para = { ...this.orderIllgalBoardTableTx.filter };

      var res = await exportOrderWithholdTbaleTXList(para);
      if (!res?.data) {
        this.$message({ message: "没有数据", type: "warning" });
        return
      }

      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '淘系扣款详情看板列表_' + new Date().toLocaleString() + '_.xlsx')
      aLink.click()
    },
    async showtabledetailYsLx(val) {
      this.showDetailVisibleTable = true;
      var para = { ...this.filter, ...this.filter2 };
      para.ysLx = val;
      this.orderIllgalBoardTableTx.filter = { ...para };
      this.$nextTick(async () => {
        await this.$refs.orderIllgalBoardTableTx.onSearch();
      });
    },
    async showtabledetailIllegalType(val) {
      this.showDetailVisibleTable = true;
      var para = { ...this.filter, ...this.filter2 };
      para.illegalType = val;
      this.orderIllgalBoardTableTx.filter = { ...para };
      this.$nextTick(async () => {
        await this.$refs.orderIllgalBoardTableTx.onSearch();
      });
    },

    async showtabledetailGroup (val) {
      this.showDetailVisibleTable = true;
      var para = { ...this.filter, ...this.filter2 };
      para.groupId = val;
      this.orderIllgalBoardTableTx.filter = { ...para };
      this.$nextTick(async () => {
        await this.$refs.orderIllgalBoardTableTx.onSearch();
      });
    },

    async showtabledetailDept(val, childFilterType) {
      this.showDetailVisibleTable = true;
      var para = { ...this.filter, ...this.filter2 };
      para.dutyDept = val;
      // if (val == "仓库发货")
      //   para.dutyDept = "仓库";

      if (childFilterType) {
        if (childFilterType == "sumDept_CK_List_WmsId") {
          if (!(para.wmsCoId && para.wmsCoId.length > 0))
            para.wmsCoId = this.sumData.sumDept_CK_List_WmsId;
        }
      }
      
      this.orderIllgalBoardTableTx.filter = { ...para };
      this.$nextTick(async () => {
        await this.$refs.orderIllgalBoardTableTx.onSearch();
      });
    },
    async showtabledetailZrDept(val, name, childFilterType) {
      this.showDetailVisibleTable = true;
      var para = { ...this.filter, ...this.filter2 };
      para.zrDept = val;

      if (childFilterType == "sumDept_CK_List_WmsId") {
        if (!(para.wmsCoId && para.wmsCoId.length > 0))
          para.wmsCoId = this.sumData.sumDept_CK_List_WmsId;
        else
          para.wmsCoId = '';
      }
      else if (childFilterType == "sumDept_YY_List_groupId") {
        if (para.groupId != null && para.groupId != 0 || para.groupId == undefined)
          para.groupId = this.sumData.sumDept_YY_List_groupId;
        else
          para.groupId = null;
      }
      else if (childFilterType == "sumDept_KD_List_expressCompanyName") {
          if (!(para.expressCompanyName && para.expressCompanyName.length > 0))
            para.expressCompanyName = this.sumData.sumDept_KD_List_expressCompanyName;         
      }
      else if (childFilterType == "sumDept_CG_List_brandId") {
        if (para.brandId != null && para.brandId != 0 || para.brandId == undefined)
            para.brandId = this.sumData.sumDept_CG_List_brandId;         
      }

      

      this.orderIllgalBoardTableTx.filter = { ...para };
      this.$nextTick(async () => {
        await this.$refs.orderIllgalBoardTableTx.onSearch();
      });
    },
    async showtabledetailSendWarehouse (val) {
      this.showDetailVisibleTable = true;
      var para = { ...this.filter, ...this.filter2 };
      para.wmsCoId = val;
      this.orderIllgalBoardTableTx.filter = { ...para };
      this.$nextTick(async () => {
        await this.$refs.orderIllgalBoardTableTx.onSearch();
      });
    },
    async showtabledetailExpressCompany (val) {
      this.showDetailVisibleTable = true;
      var para = { ...this.filter, ...this.filter2 };
      para.ExpressCompanyName = val;
      this.orderIllgalBoardTableTx.filter = { ...para };
      this.$nextTick(async () => {
        await this.$refs.orderIllgalBoardTableTx.onSearch();
      });
    },
    async onSearch1() {
      this.showchart(this.chatType, this.chatVal, this.chatName, this.chatChildFilterType);
     

    },
    // 趋势图
    async showchart(type, val, name, childFilterType) {
      this.chatType = type;
      this.chatVal = val;
      this.chatName = !!name ? name : '';
      this.chatChildFilterType = !!childFilterType ? childFilterType : null;

      if (this.filter.timerange && this.filter.timerange.length > 1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      } else {
        this.$message({ message: "请先选择日期", type: "warning" });
        return false;
      }
      var para = {};

      let chartTitle = '';
      if (type == -1) {
        para = { ysLx: val }
        chartTitle = '预售类型：' + val;
      }
      else if (type == 0) {
        para = { illegalType: val }
        chartTitle = "扣款原因：" + (this.illegalTypeList.find(x => x.value == val) ? this.illegalTypeList.find(x => x.value == val)?.label : "总扣款");
      }
      else if (type == 1) {
        para = { groupId: val }
        chartTitle = "运营组：" + this.orderPinSumList.groupList.find(x => x.groupId == val)?.groupName;
      }
      else if (type == 2) {
        para = { dutyDept: val };
        // if (val === "仓库发货")
        //   para.dutyDept = "仓库";


        chartTitle = '责任部门：' + val;
      }
      else if (type == 22) {
        para = { zrDept: val };

        chartTitle = '责任部门：' + val;
        if (name)
          chartTitle += '-' + name;
      }
      else if (type == 3) {
        para = { wmsCoId: val }
        chartTitle = "发货仓库：" + this.orderPinSumList.wareList.find(x => x.wmsCoId == val)?.sendWarehouseName;
      }
      else if (type == 4) {
        para = { expressCompanyName: val }
        chartTitle = '快递公司：' + val;
      }
      else if (type == 5) {
        para = { brandId: val }
        chartTitle = '采购：' + name;
      }

      if (childFilterType) {
        if (childFilterType == "sumDept_CK_List_WmsId") {
          if (!(para.wmsCoId && para.wmsCoId.length > 0))
            para.wmsCoId = this.sumData.sumDept_CK_List_WmsId;
         
        }
        else if (childFilterType == "sumDept_YY_List_groupId") {
          if (para.groupId != null && para.groupId != 0 || para.groupId == undefined)
            para.groupId = this.sumData.sumDept_YY_List_groupId;
          
        } 
        else if (childFilterType == "sumDept_KD_List_expressCompanyName") {
          if (!(para.expressCompanyName && para.expressCompanyName.length > 0))
            para.expressCompanyName = this.sumData.sumDept_KD_List_expressCompanyName;         
        }
        else if (childFilterType == "sumDept_CG_List_brandId") {
          if (para.brandId != null && para.brandId != 0 || para.brandId == undefined)
              para.brandId = this.sumData.sumDept_CG_List_brandId;         
        }

       
        

      }

      var paras = { ...this.filter, ...para };


      let that = this;
      this.$nextTick(async () => {
        const res = await getOrderWithholdListChart(paras).then((res) => {
        
          that.showDetailVisible.visible = true;
          that.showDetailVisible.data = res.data;
          that.showDetailVisible.title = chartTitle;
        });
        await this.$refs.buschar.initcharts()
      });

    },

    async onSearch() {
      if (!this.filter.timerange) {
        this.$message({ message: "请选择日期", type: "warning" });
        return;
      }
      this.filter2.proCode = this.filter.proCode;
      this.filter1.proCode = this.filter.proCode;

      this.getSum();

    },


    async getSum() {
      this.isorderPinSumloading = true;
      var para = this.getFilterParams();
    
      var res = await GetWithholdSumTxAsync(para);
      if (!res?.code) {
        return false;
      }

      this.orderPinSumList = res.data;

      this.sumData = res.data;
      this.isorderPinSumloading = false;
    },
    getFilterParams() {
      this.filter2.proCode = this.filter.proCode;
      this.filter1.proCode = this.filter.proCode;
      this.filter.startPayDate = null
      this.filter.endPayDate = null
      this.filter.startSendDate = null
      this.filter.endSendDate = null
      if (this.filter.timerange2 && this.filter.timerange2.length > 1) {
        this.filter.startPayDate = this.filter.timerange2[0];
        this.filter.endPayDate = this.filter.timerange2[1];
      }
      if (this.filter.timerange3 && this.filter.timerange3.length > 1) {
        this.filter.startSendDate = this.filter.timerange3[0];
        this.filter.endSendDate = this.filter.timerange3[1];
      }
      var para = { ...this.filter, ...this.filter2 };  //,...this.filterInner

      return para;

    },
    //责任扣款-仓库-发货仓库过滤
    async loadSumDept_CK_WmsId(wmsId) {
      this.sumDept_CK_List_Loading = true;
      this.sumData.sumDept_CK_List_WmsId = wmsId;

      let para = this.getFilterParams();
      if (!(para.wmsCoId && para.wmsCoId.length > 0))
        para.wmsCoId = wmsId;

      let res = await GetWithholdSumTxAsync(para);
      if (!res?.code) {
        return false;
      }
      this.sumData.sumDept_CK_List = res.data.sumDept_CK_List;
      this.sumDept_CK_List_Loading = false;

    },
    //责任扣款-仓库-发货仓库过滤
    async loadSumDept_KD_Region(val) {
      this.sumDept_KD_List_Loading = true;
      this.sumData.sumDept_KD_List_Region = val;

      let para = this.getFilterParams();
      
      para.expressCompanyNameRegion = val;

      let res = await GetWithholdSumTxAsync(para);
      if (!res?.code) {
        return false;
      }
      this.sumData.sumDept_KD_List = res.data.sumDept_KD_List;
      this.sumDept_KD_List_Loading = false;

    },
    //快递-区域过滤
    async sumExpressList_Region(val){
      this.sumExpressList_Loading = true;
      this.sumData.sumExpressList_Region = val;

      let para = this.getFilterParams();
      
      para.expressCompanyNameRegion = val;

      let res = await GetWithholdSumTxAsync(para);
      if (!res?.code) {
        return false;
      }
      this.sumData.sumExpressList = res.data.sumExpressList;
      this.sumExpressList_Loading = false;
    },
    onSearchOrders(proCode) {
      this.getSum();
    },
  },
};
</script>

<style lang="scss" scoped>
::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

.card-header {
  width: 190px;
  height: 120px;
  display: inline-block;
  background-color:rgb(198, 226, 255);
}

.card-header2 {
  width: 260px;
  height: 120px;
  display: inline-block;
  background-color: rgb(217, 236, 255);
  overflow: auto;  
  line-height: 14px;
}
.card-header2-short {
  width: 230px;
  height: 120px;
  display: inline-block;
  background-color: rgb(217, 236, 255);
  overflow: auto;  
  line-height: 14px;
}

.card-header-err{
  width: 190px;
  height: 120px;
  display: inline-block;
  background-color:rgb(241, 156, 116);
}

.card-body {
  width: 190px;
  height: 120px;
  display: inline-block;
}

.cardBodyStyle-tip{
  height: 20px;
  line-height: 20px;
}

.region-title{
  float: left;
  text-align: left;
  width:60px;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}
.region-title-short{
  float: left;
  text-align: left;
  width:30px;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}
.region-num {
  float: right; 
  text-align: right;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;

}
.region-count{
  width:54px;
}
.region-amount{
  width:70px;
}
.region-ratio{
  width:40px;
}

.my-title {
  text-align: left;
  border-bottom: solid 1px silver;
  margin-bottom: 0px;
}

.row-condition {
  margin-top: 20px;
  margin-bottom: 10px;
  color: #606266;
  font-weight: bold;
}

.grid-header {
  color: #606266;
  font-size: 12px;
  // margin-top: 10px;
  font-weight: bold;
}

.card-header .grid-header{
  font-size: 14px;
  // margin-bottom: 10px;
}

.card-header2 .grid-header{
   margin-top: 10px;
}
.card-header2 button{
  padding: 0px;
}

.grid-text {
  color: #606266;
  font-size: 9px;
  // margin-top: 5px;
  padding: 5px;
  font-weight: bold;
}

.card-body .grid-header{
  margin-bottom: 10px;
}

.card-body .grid-text{
  padding:4px;
  font-weight:500
}

.el-col {
  text-align: center;
}

.el-row {
  margin-right: 80px;
}

.el-icon-question {
  color: #909399;
}

.abnormalcard {
  padding: 0;
}

.canclick {
  color: #409eff;
  cursor: pointer;
}

.cantclick {
  color: #409eff;
}

.detailsclick {
  color: #ff404a;
  cursor: pointer;
}</style>