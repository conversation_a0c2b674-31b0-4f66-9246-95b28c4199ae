<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" ref="form" label-width="120px" label-position="right" >       
                <el-row>
                    <el-col :span="23" :offset="1">
                        <span style="color:red">
                            认可操作须知：
                            <br/>
1、对方发起申诉后，【是否认可】操作时限为17:30到次日10:00，过时将自动认可！
<br/>
2、不认可，需要认真填写原因，便于后期审核。
<br/>
3、如认可，责任将按申诉填写的新责任部门与责任人划分。
                        </span>
                    </el-col>
                </el-row>        
                <el-row>
                    <el-col :span="4">
                        <el-form-item label="平台：">
                            {{ form.orderPlatformText}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="7">
                        <el-form-item label="线上单号：">
                            {{ form.orderNo }}
                            <span v-if="form.otherInfo && form.otherInfo.a" style="margin-left:5px;">扣款金额:{{ form.otherInfo.a.amountPaid }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="扣款时间：">
                            {{form.deductOccurTime}}
                        </el-form-item>
                    </el-col>    
                    <el-col :span="7">
                        <el-form-item label="平台原因：">
                            {{illegalTypeName}}
                        </el-form-item>
                    </el-col>                 
                </el-row>  
                <el-row>  
                    <el-col :span="4">
                        <el-form-item label="原责任类型：">
                            {{form.orgZrType1}}
                        </el-form-item>
                    </el-col>   
                    <el-col :span="7">
                        <el-form-item label="原责任原因：">
                            {{form.orgZrType2}}
                        </el-form-item>
                    </el-col>                      
                    <el-col :span="6">
                        <el-form-item label="新责任类型：">
                            {{form.newZrType1}}
                        </el-form-item>
                    </el-col>   
                    <el-col :span="7">
                        <el-form-item label="新责任原因：">
                            {{form.newZrType2}}
                        </el-form-item>
                    </el-col>                            
                </el-row>  
                <el-row>  
                    <el-col :span="4">
                        <el-form-item label="原责任部门：">
                            {{form.orgZrDeptAction}}
                        </el-form-item>
                    </el-col>   
                    <el-col :span="7">
                        <el-form-item label="原责任规则：">
                            {{form.orgZrConditionFullName}}
                        </el-form-item>
                    </el-col>                      
                    <el-col :span="6">
                        <el-form-item label="原责任原因2：" prop="orgZrReason" >                            
                            {{ form.orgZrReason }}
                        </el-form-item>
                    </el-col>    
                    <el-col :span="7">
                        <el-form-item label="原责任人：">
                            {{form.orgMemberName}}
                        </el-form-item>
                    </el-col>                  
                </el-row>  
                <el-row>     
                    
                    <el-col :span="4">
                        <el-form-item label="新责任部门：" prop="newZrDeptAction" >                           
                            <span >{{ form.newZrDeptAction }}</span>
                        </el-form-item>
                    </el-col>   
                    <el-col :span="7">
                        <el-form-item label="申诉理由：" prop="newZrConditionFullName" >                           
                            <span >{{ form.newZrConditionFullName }}</span>
                        </el-form-item>
                    </el-col>                      
                    <el-col :span="6">
                        <el-form-item label="新责任原因2：" prop="newZrReason" >                                             
                            <span >{{ form.newZrReason }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="7">
                        <el-form-item label="新责任人：" prop="newMemberName" >                                                  
                            <span >{{ form.newMemberName }}</span>
                        </el-form-item> 
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24" style="max-height:400px;overflow:auto">
                        <el-form-item label="定责资料："  prop="applyContent" >
                            <div  v-html="form.applyContent" class="tempdiv"></div>                       
                        </el-form-item>
                    </el-col>
                </el-row>

               <el-row>
                    <el-col :span="4">
                        <el-form-item label="是否认可：" prop="firstAuditState" :rules="[
                        { required: true, message: '请选择是否认可', trigger: ['blur', 'change'] }    
                        ]">
                            <el-radio-group v-model="form.firstAuditState">
                                <el-radio :label="-1">不认可</el-radio>
                                <el-radio :label="1">认可</el-radio>
                            </el-radio-group>     
                           
                        </el-form-item>
                        <br/>
                            <el-button type="primary" style="float:right" @click="trans2Other">转派给同部门其他人员</el-button>      
                    </el-col>
                    <el-col :span="20">
                        <el-form-item label="初审资料："  prop="firstAuditRemark" >
                            <yh-quill-editor :value.sync="form.firstAuditRemark" ></yh-quill-editor>    
                        </el-form-item>                        
                    </el-col>
               </el-row>
               <el-row v-if="form.firstAuditRecords && form.firstAuditRecords.length>0">
                    <el-col :span="24">
                        <el-form-item label="初审转派记录：" >
                            <table>
                                <tr v-for=" r in form.firstAuditRecords">
                                    <td>                                        
                                        {{ r.opTime }}
                                        <br/>
                                        【{{ r.opUserName }}】转【{{r.newMemberName}}】          
                                    </td>
                                    <td>
                                        <div  v-html="r.opContent" class="tempdiv"></div> 
                                    </td>
                                </tr>
                            </table>
                        </el-form-item>
                    </el-col>
               </el-row>
                      
              
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">  
                    <el-button @click="onClose">关闭</el-button>                     
                    <el-button  type="primary" @click="onSave(true)">确认保存</el-button> 
                </el-col>
            </el-row>
        </template>      

    </my-container>
</template>
<script>  

    import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode ,DeductOrderZrDeptActions,DeductOrderZrReasons} from "@/utils/tools";
    import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import { GetDeductZrAppeal4CRUD,FirstAuditDeductZrAppeal } from "@/api/order/orderdeductmoney";
    import {getAllWarehouse,getAllProBrand} from '@/api/inventory/warehouse'

    import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'

    import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";

    import {
        getDirectorList,
        getDirectorGroupList,
        getProductBrandPageList,
        getList as getshopList,
    } from "@/api/operatemanage/base/shop";


    //机器人查询状态 1成功、0下架、-1失败
    const fmtJqrNoticeState=(v)=>{
        switch(v){
            case 0:return '下架';
            case 1:return '已查询';
            case -1:return '失败';
        }
        return ' ';
    };

    const fmtApplyState=function(val){
        if(val==-1) return "已拒绝";
        else if(val==0) return "待申请";
        else if(val==1) return "申请中";
        else if(val==2) return "已审核";
        return val;
    }

    export default {
        name: "OrderDeductZrApplyFirstAuditForm",
        components: { MyContainer, MyConfirmButton,  YhQuillEditor ,OrderActionsByInnerNos},
        data() {
            return {              
                that: this,
                mode:3,
                illegalTypeList:[],
                zrDeptActions:DeductOrderZrDeptActions,
                zrReasons:DeductOrderZrReasons,

                brandlist: [], 
                directorList: [],

                form: {
                    firstAuditRecords:null,
                    newMemberName:"",
                    newMemberId:null,
                },
            
                //summaryarry: {},
                pageLoading: false,
                curRow: null,
                formEditMode: true,//是否编辑模式              
                dialogHisVisible:false,
                isTx:false,      
            };
        },
        async mounted() {
            let illegalType = await ruleIllegalType();
            this.illegalTypeList = illegalType.options;
            await this.setBandSelect();
            await this.getDirectorlist();
        },
        computed: {      
            illegalTypeName(){
                let opt=this.illegalTypeList.find(x=>x.value==this.form.illegalType);
                if(opt)
                    return opt.label;
                else
                    return '';
            }
        },
        methods: {   
            async getDirectorlist () {
                const res1 = await getDirectorList({});
                const res2 = await getDirectorGroupList({});

                this.directorList = res1.data;
                this.directorGroupList = [{ key: "0", value: "未知" }].concat(
                    res2.data || []
                );
            }, 
            async setBandSelect(){
                var res= await  getAllProBrand();
                if (!res?.success) return;
                this.brandlist = res.data;
            }, 
            fmtApplyState:fmtApplyState, 
            fmtJqrNoticeState:fmtJqrNoticeState,
            showLogDetail (orderNo) {
                this.isTx=this.form.deductPlatform==1;
                this.dialogHisVisible = true;
                this.orderNo = orderNo;
            },  
            newZrDeptActionChange(){
                if(this.form.newZrDeptAction){
                    let opt=this.zrDeptActions.find(x=>x.zrDeptAction==this.form.newZrDeptAction);
                    if(opt){
                        if(this.form.newZrDept!=opt.zrDept){
                            this.form.newMemberName="";
                            this.form.newMemberId=null;
                        }
                        this.form.newZrAction=opt.zrAction;
                        this.form.newZrDept=opt.zrDept;
                    }else{
                        this.form.newMemberName="";
                        this.form.newMemberId=null;
                    }                    
                }
            },   
            newMemberIdChange(){  
                let arr=null;
                if(this.form.newZrDept=="采购"){
                    arr=[...this.brandlist];                   
                }
                else if(this.form.newZrDept=="运营"){
                    arr=[...this.directorList];                    
                }    
                
                if(arr!=null && arr && this.form.newMemberId){                  
                    let opt=arr.find(x=>x.key==this.form.newMemberId);
                    if(opt){
                        this.form.newMemberName=opt.value;                      
                    }
                }
            },   
            trans2Other(){
                let self=this;            
                this.$showDialogform({
                    path: `@/views/order/orderillegal/zrApply/OrderDeductZrApplyFirstAuditTrans2OtherForm.vue`,
                    title: '初审转派',
                    autoTitle:false,
                    args: {appealId:self.form.id, newZrDeptAction:self.form.newZrDeptAction, newZrDept:self.form.newZrDept, newZrAction:self.form.newZrAction},
                    height: 300,
                    width: '80%',
                    callOk: ()=>{
                        self.$emit('afterSave');
                        self.$emit('close');
                    }
                })
            },
            onClose(){
                this.$emit('close');
            },  
            async onSave(isClose){
                if(await this.save()){
                    this.$emit('afterSave');
                    if(isClose)
                        this.$emit('close');
                }
            },
            async loadData({id, mode}) {     
                let self=this;         
                self.pageLoading = true;
                self.formEditMode = mode!=3;
                self.mode = mode;    
                 

                let rlt = await GetDeductZrAppeal4CRUD( {id:id} );
                if (rlt && rlt.success) {
                    let formDto= rlt.data;   
                    if(formDto.newMemberId)   
                        formDto.newMemberId=formDto.newMemberId.toString();
                    this.form = formDto;                                           
                    if(this.form.applyState>0){
                        self.mode=3;
                        self.formEditMode = self.mode!=3;
                    }
                    this.pageLoading = false;
                }else{
                    this.onClose();
                }
                self.pageLoading = false;
            },
            async save() {
                this.pageLoading = true;
                
                let saveData = { 
                    id:this.form.id,
                    auditState:this.form.firstAuditState ==1 ?1:0,
                    auditRemark:this.form.firstAuditRemark
                };  

                try {
                    await this.$refs["form"].validate();
                } catch (error) {
                    this.pageLoading = false;
                    return false;
                } 
               

                let rlt = await FirstAuditDeductZrAppeal(saveData);
                if (rlt && rlt.success) {
                    this.$message.success('操作成功！');           
                }

                this.pageLoading = false;
              
                return (rlt && rlt.success);
            }
        },
    };
</script>
<style lang="scss" scoped>
   
    .tempdiv ::v-deep img {
        width: auto;
        max-width: 1000px;
    }
</style>