<template>
    <div style="height:100%;width:95%;">
        <el-tabs v-model="activeName" style="height: 94%; width:95%;" @tab-click="tabclick">
            <el-tab-pane label="部门及用户" name="first" style="height: 100%;">
                <dingdingDeptUser ref="dingdingDeptUser" />
            </el-tab-pane>
            <el-tab-pane label="点餐规则" name="second" style="height: 100%; overflow: auto;">
                <el-form ref="form" :model="form" label-width="100px" >
                    <el-form-item label="固定充值" style="padding-top: 10px;">
                    </el-form-item>
                    <el-form-item label="">
                        <span>
                            每月
                            <el-select v-model="form.gdvalue" placeholder="几号" clearable filterable style="width: 70px; ">
                                <el-option v-for="i in 28" :label="i + '号'" :value="i"></el-option>
                            </el-select>
                            自动充值
                            <el-input-number placeholder="多少积分" style="width: 120px; "
                                v-model="form.gdvalue1" :step="1" step-strictly :max="999999" :min="0"></el-input-number>
                        </span>
                    </el-form-item>
                    <el-form-item label="岗位充值">
                        <el-button @click="addgwcz">新增</el-button>
                    </el-form-item>
                    <div style="max-height: 300px; overflow-y: auto; width: 40rem;">
                        <el-form-item label="" v-for="(item, index) in form.gwczList" :key="index">
                            <span>
                                岗位
                                <el-select v-model="item.ruleName" placeholder="岗位" clearable filterable style="width: 150px; "
                                    @change="confirmGw(item,index)">
                                    <el-option v-for="i in gwList" :label="i.title" :value="i.title"></el-option>
                                </el-select>
                                自动充值
                                <el-input-number placeholder="多少积分" :max="999999" :min="0" style="width: 120px; " v-model="item.ruleValue"
                                :step="1" step-strictly></el-input-number>
                                <el-button @click="removegwcz(index)">删除</el-button>
                            </span>
                        </el-form-item>
                    </div>
                    <el-form-item label="单日高限">
                    </el-form-item>
                    <el-form-item label="">
                        <span>
                            正式每天最高消费
                            <el-input-number placeholder="多少积分" style="width: 120px; "
                                v-model="form.zsvalue" :step="1" step-strictly :max="999999" :min="0"></el-input-number>
                            <el-switch v-model="form.zsEnabled" active-text="开启" inactive-text="关闭">
                            </el-switch>
                        </span>

                    </el-form-item>
                    <el-form-item label="">
                        <span>
                            试用每天最高消费
                            <el-input-number  placeholder="多少积分" style="width: 120px; "
                                v-model="form.syvalue" :step="1" step-strictly :max="999999" :min="0"></el-input-number>
                            <el-switch v-model="form.syEnabled" active-text="开启" inactive-text="关闭">
                            </el-switch>
                        </span>
                    </el-form-item>
                    <el-form-item label="积分有效期">
                    </el-form-item>
                    <el-form-item label="">
                        <span>
                            正式是否清零&nbsp;&nbsp;&nbsp;&nbsp;
                            每月
                            <el-select v-model="form.zsqlvalue" placeholder="几号" clearable filterable style="width: 70px; ">
                                <el-option v-for="i in 28" :label="i + '号'" :value="i"></el-option>
                            </el-select>
                            <el-switch v-model="form.zsqlEnabled" active-text="开启" inactive-text="关闭">
                            </el-switch>
                        </span>

                    </el-form-item>
                    <el-form-item label="">
                        <span>
                            试用是否清零&nbsp;&nbsp;&nbsp;&nbsp;
                            每月
                            <el-select v-model="form.syqlvalue" placeholder="几号" clearable filterable style="width: 70px; ">
                                <el-option v-for="i in 28" :label="i + '号'" :value="i"></el-option>
                            </el-select>
                            <el-switch v-model="form.syqlEnabled" active-text="开启" inactive-text="关闭">
                            </el-switch>
                        </span>
                    </el-form-item>
                    <el-form-item label="禁用时间" style="padding-top: 10px;">
                    </el-form-item>
                    <el-form-item label="">
                        <span>
                            点餐禁止下单/取消订单时间&nbsp;&nbsp;&nbsp;&nbsp;
                            每天
                            <el-select v-model="form.jzvalue" placeholder="小时" clearable filterable style="width: 70px; ">
                                <el-option :label="0 + '时'" :value="0"></el-option>
                                <el-option v-for="i in 23" :label="i + '时'" :value="i"></el-option>
                            </el-select>
                            <el-switch v-model="form.jzEnabled" active-text="开启" inactive-text="关闭">
                            </el-switch>
                        </span>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="saveIntegralRule()">保存</el-button>
                    </el-form-item>
                </el-form>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
  
<script>
//import {  } from '@/api/express/express'    
import dingdingDeptUser from '@/views/profit/dingdingDeptUser.vue'
import { getGwInfo, getIntegralRuleAsync, saveIntegralRuleAsync } from '@/api/profit/orderfood'

import dingdingUserAttendance from '@/views/operatemanage/base/dingdingUserAttendance.vue'
export default {
    name: 'DingDingShow',
    components: { dingdingDeptUser },
    data () {
        return {
            activeName: 'first',
            form: {
                gwczList: [],
                gdvalue: '',
                gdvalue1: '',
                zsvalue: '',
                zsEnabled: null,
                syvalue: '',
                syEnabled: null,
                zsqlvalue: '',
                zsqlEnabled: null,
                syqlvalue: '',
                syqlEnabled: null,
                jzvalue:'',
                jzEnabled:null,

            },
            gwList: [],
            ruleList: [],

        }
    },
    async mounted () {
        await this.getGw();
        this.initIntegralRule();
        //await this.onSearch()

    },
    methods: {
        confirmGw (item,index) { 
            let newArr = [];
            this.form.gwczList.forEach((v) => {
                if (v.ruleName == item.ruleName)
                    newArr.push(v.ruleName);
            })
            if (newArr.length > 1) { 
                this.form.gwczList[index].ruleName = '';
                this.$message({ type: 'warning', message: '存在相同岗位,请重新选择！' });
            }
        },
        async initIntegralRule () {
            await this.getIntegralRule();
            this.form.gwczList = [];
            this.ruleList.forEach(a => {
                if (a.ruleName == '固定充值') {
                    this.form.gdvalue = a.ruleValue
                    this.form.gdvalue1 = a.ruleValue1;
                }
                else if (a.ruleName == '正式每天最高消费') {
                    this.form.zsvalue = a.ruleValue
                    this.form.zsEnabled = a.enabled;
                }
                else if (a.ruleName == '试用每天最高消费') {
                    this.form.syvalue = a.ruleValue
                    this.form.syEnabled = a.enabled;
                }
                else if (a.ruleName == '正式是否清零') {
                    this.form.zsqlvalue = a.ruleValue
                    this.form.zsqlEnabled = a.enabled;
                }
                else if (a.ruleName == '试用是否清零') {
                    this.form.syqlvalue = a.ruleValue
                    this.form.syqlEnabled = a.enabled;
                }
                else if (a.ruleName == '点餐禁止下单时间') {
                    this.form.jzvalue = a.ruleValue
                    this.form.jzEnabled = a.enabled;
                }
                else if (a.ruleName.indexOf('岗位充值') != -1) {
                    let p = {}
                    p.ruleName = a.ruleName.substr(0, a.ruleName.length - 4)
                    p.ruleValue = a.ruleValue;
                    this.form.gwczList.push(p);
                }
            });
        },
        addgwcz () {
            this.form.gwczList.push({
                ruleName: '',
                ruleValue: null
            });
        },
        removegwcz (index) {
            this.form.gwczList.splice(index, 1)
        },
        async getGw () {
            const res = await getGwInfo();
            this.gwList = res?.data;
        },
        async saveIntegralRule () {
            this.$confirm('确定保存, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(async _ => {
                    let arrp = [];
                    arrp.push({
                        ruleName: '固定充值',
                        ruleValue: this.form.gdvalue,
                        ruleValue1: this.form.gdvalue1,
                        enabled: true
                    })
                    arrp.push({
                        ruleName: '正式每天最高消费',
                        ruleValue: this.form.zsvalue,
                        ruleValue1: null,
                        enabled: this.form.zsEnabled
                    })
                    arrp.push({
                        ruleName: '试用每天最高消费',
                        ruleValue: this.form.syvalue,
                        ruleValue1: null,
                        enabled: this.form.syEnabled
                    })
                    arrp.push({
                        ruleName: '正式是否清零',
                        ruleValue: this.form.zsqlvalue,
                        ruleValue1: null,
                        enabled: this.form.zsqlEnabled
                    })
                    arrp.push({
                        ruleName: '试用是否清零',
                        ruleValue: this.form.syqlvalue,
                        ruleValue1: null,
                        enabled: this.form.syqlEnabled
                    })
                    arrp.push({
                        ruleName: '点餐禁止下单时间',
                        ruleValue: this.form.jzvalue,
                        ruleValue1: null,
                        enabled: this.form.jzEnabled
                    })
                    this.form.gwczList.forEach(a => {
                        let p = {
                            ruleName: a.ruleName + '岗位充值',
                            ruleValue: a.ruleValue,
                            ruleValue1: null,
                            enabled: true
                        }
                        arrp.push(p)
                    })

                    const res = await saveIntegralRuleAsync(arrp);
                    if (res.data) {
                        this.$message({ type: 'success', message: '保存成功!' });
                    }
                })
                .catch(_ => { });

        },
        async getIntegralRule () {
            const res = await getIntegralRuleAsync();
            this.ruleList = res?.data;
        },
        async tabclick () {
            this.$nextTick(async () => {
                if (this.activeName == 'first') {
                    this.$refs.dingdingDeptUser.deptOnSearch()

                }
                else if (this.activeName == 'second') {
                    this.initIntegralRule();
                }
            })
        }
    }
}
</script>
  