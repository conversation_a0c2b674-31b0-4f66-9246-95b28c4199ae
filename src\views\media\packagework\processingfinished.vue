<template>
  <my-container>
    <template #header>
      <el-button-group style="width: 100%;">
        <div class="ssanc">
          <div style="width:65%;display: inline-block;text-align: left;">
            <div style="display: inline-block; margin-right:10px; margin-bottom: 10px;width: 160px;">
              <inputYunhan ref="productCode" :inputt.sync="filter.finishedProductCode"
                v-model="filter.finishedProductCode" placeholder="成品编码" :clearable="true" :clearabletext="true"
                :maxRows="50" :maxlength="3998" :width="'238'" @callback="callbackGoodsCode" title="成品编码">
              </inputYunhan>
            </div>

            <el-input style="width:200px;margin-right: 10px;" v-model.trim="filter.finishedProductName" :maxlength=100
              placeholder="成品名称" suffix-icon="el-icon-search" clearable />

            <el-input style="width:160px;margin-right: 10px;" v-model.trim="filter.halfProductCode" :maxlength=100
              placeholder="半成品编码" clearable />

            <el-input style="width:250px;margin-right: 10px;" v-model.trim="filter.halfProductName" :maxlength=100
              placeholder="半成品名称" suffix-icon="el-icon-search" clearable />

            <span>
                <el-date-picker style="width:25%;position: relative;top:1px;margin-right: 0;" type="daterange"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="加工日期"
                end-placeholder="加工日期" v-model="filter.processDaterange" />
            </span>
          </div>
          <div style="display: flex;align-items: center; margin-bottom: 10px; margin-right: 10px">
            <el-button type="primary" @click="onSearch" style="width: 90px;">查询</el-button>
            <el-button @click="onclear" plain>重置</el-button>
          </div>

          <div style="display: flex;align-items: center; margin-bottom: 10px;">
            <el-button size="mini" style="width:100px;border: 1px solid #b3d8ff;" type="primary" plain @click="addNew"><i
                class="el-icon-plus"></i>&nbsp;新增绑定</el-button>
            <el-button type="primary" @click="onImportPrice" v-if="checkPermission('exportworkprice')">导入工价</el-button>
            <el-button type="primary" @click="handleExportCommand"
              v-if="checkPermission('exportfinishproduct')">导出</el-button>
          </div>
        </div>

        <div class="heardcss">
          <span>
            <el-select style="width: 12%;" v-model="filter.brandId" placeholder="采购员" :collapse-tags="true" clearable
              filterable>
              <el-option v-for="item in allsellist.brandList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </span>
          <span>
            <el-select class="selectmin" style="width:12%;" v-model="filter.processTypeId" filterable
              :collapse-tags="true" placeholder="加工方式" clearable>
              <el-option v-for="item in machineTypeList" :key="item.setId" :label="item.sceneCode" :value="item.setId" />
            </el-select>
          </span>
          <span>
            <el-select style="width:12%;" filterable v-model="filter.createdUserName" placeholder="创建人" clearable>
              <el-option v-for="(item, i) in allsellist.createUser" :key="i + 1" :label="item" :value="item" />
            </el-select>
          </span>
          <span>
            <el-select style="width:12%;" ref="medicalAdviceRef" v-model="filter.timeTypeList" :clearable="false" placeholder="时间" multiple @change="cancelEvent">
              <el-option label="创建时间" value="1"></el-option>
              <el-option label="修改时间" value="2"></el-option>
            </el-select>
          </span>
          <span>
            <el-date-picker style="width:20%;position: relative;top:1px;margin-right: 0;" type="daterange"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" v-model="filter.createdtimerange" />
          </span>
          <span>
            <el-select style="width:8%;" ref="isNew" v-model="filter.isNew" :clearable="true" placeholder="是否最新">
              <el-option label="是" value="1"></el-option>
              <el-option label="否" value="0"></el-option>
            </el-select> 
          </span> 
        </div>
      </el-button-group>
    </template>
    <!-- <div style="height: 90%;"> -->
      <vxe-table ref="xTable" border="default" height="100%" :show-footer="true" style="width: 100%;"
        class="vxetable202212161323 mytable-scrollbar20221212" resizable stripe show-overflow :data="tableData"
        :show-footer-overflow="'tooltip'" keep-source size="mini" :loading="pageLoading" :scroll-y="{ gt: 100 }"
        :scroll-x="{ gt: 100 }" :header-row-class-name="'height cellheight1'" :expand-config="{ accordion: true }"
        :row-config="{ isCurrent: true, isHover: true }" @checkbox-all="checkmethod" @checkbox-change="checkmethod"
        :sort-config="{ transform: true, sortMethod: customSortMethod }" >
        <vxe-column type="checkbox" width="40" fixed="left"></vxe-column>
        <vxe-column title="编号" field="packagesProcessingId" width="55" align="left" fixed='left'>
        </vxe-column>
        <vxe-column field="isNew" title="是否最新" width="80" sortable align="left" fixed='left'>
          <template #default="{ row }"> 
               <div>{{ row.isNew == 1 ? "是" : "否"}}</div>
          </template>
        </vxe-column>
        <vxe-column field="finishedProductImg" title="成品图片" width="66" align="left" fixed='left'>
          <template #default="{ row }">
            <el-image style="width: 50px; height: 50px" :src="row.finishedProductImg"
              :preview-src-list="[row.finishedProductImg]">
            </el-image>
          </template>
        </vxe-column>
        <vxe-column type="expand" align="left" width="25" :edit-render="{}" fixed='left'>
          <template #content="{ row, rowIndex }">
            <div style="padding: 20px 220px 20px 180px;background-color: #8a8484;">
              <vxe-table height="200px" show-overflow resizable border="default" @checkbox-all="secondcheckmethod"
                @checkbox-change="secondcheckmethod" :row-config="{ height: '36', isCurrent: true, isHover: true }"
                :header-row-class-name="'height1'" :cell-class-name="'cellheight1'" :align="allAlign1" stripe
                :data="row.detialList" style="background-color:#fff">
                <vxe-column type="checkbox" width="40" fixed="left"></vxe-column>
                <vxe-column title="编号" width="60">
                  <template #default="{ row }">
                    <div>{{ row.showCode }}</div>
                  </template>
                </vxe-column>
                <!-- <vxe-column field="id" title="半成品Id" width="135" v-if="false" fixed='left'></vxe-column> -->
                <vxe-column field="halfProductImg" title="半成品图" width="90" align="left">
                  <template #default="{ row }">
                    <el-image style="width: 50px; height: 50px" :src="row.halfProductImg"
                      :preview-src-list="[row.halfProductImg]">
                    </el-image>
                  </template>
                </vxe-column>

                <vxe-column field="halfProductCode" title="半成品编码" width="220">
                  <template #default="{ row }">
                    <div class="relativebox">
                      <div class="textover" style="width: 100px;">{{ row.halfProductCode }}</div>
                      <div class="copyhover" @click="copytext(row.halfProductCode)">
                        <i class="el-icon-document-copy"></i>
                      </div>
                    </div>

                  </template>
                </vxe-column>
                <vxe-column field="halfProductName" title="半成品名称" width="530"></vxe-column>
                <vxe-column field="halfProductQuantity" title="半成品组合" width="150"></vxe-column>
                <vxe-column field="quantityRequired" title="所需半成品">
                  <template #default="{ row }">
                    <span>{{ row.quantityRequired.toFixed(4) }}</span>
                  </template>
                </vxe-column>

                <!-- <vxe-column title="操作">
                  <template #default="{ row }">
                    <el-button type="primary" icon="el-icon-picture-outline" plain @click="showupfuc(row)"></el-button>
                  </template>
                </vxe-column> -->
              </vxe-table>
            </div>
            <div style="padding: 0px 220px 10px 180px;background-color: #8a8484;">
              <div style="background-color: white;">
                <vxe-table v-if="row.consumableList" height="200px" show-overflow resizable border="default"
                  :row-config="{ height: '36', isCurrent: true, isHover: true }" :header-row-class-name="'height1'"
                  :cell-class-name="'cellheight1'" :align="allAlign2" stripe :data="row.consumableList">
                  <vxe-column type="checkbox" width="28"></vxe-column>
                  <vxe-column title="编号" width="60">
                    <template #default="{ row }">
                      <div>{{ row.showCode }}</div>
                    </template>
                  </vxe-column>
                  <vxe-column field="id" title="半成品Id" width="135" v-if="false" fixed='left'></vxe-column>
                  <vxe-column field="consumableImg" title="耗材图片" width="100" align="left">
                    <template #default="{ row }">
                      <el-image style="width: 50px; height: 50px" :src="row.consumableImg"
                        :preview-src-list="[row.consumableImg]">
                      </el-image>
                    </template>
                  </vxe-column>
                  <vxe-column field="consumableProductCode" title="耗材编码" width="140" min-width="200">
                    <template #default="{ row }">
                      <div class="relativebox">
                        <!-- <el-tooltip effect="dark" :content="row.consumableProductCode" placement="top-start"> -->
                        <div class="textover" style="width: 69%;">{{ row.consumableProductCode }}</div>
                        <!-- </el-tooltip> -->

                        <div class="copyhover" @click="copytext(row.consumableProductCode)">
                          <i class="el-icon-document-copy"></i>
                        </div>
                      </div>
                    </template>
                  </vxe-column>
                  <vxe-column field="consumableProductName" title="耗材名称" width="400" min-width="350"></vxe-column>
                  <vxe-column field="consumableQuantity" title="所用数量"></vxe-column>
                </vxe-table>
              </div>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="finishedProductCode" title="成品编码" width="175" align="left">
          <template #default="{ row }">
            <div class="relativebox">
              <div class="textover" style="width: 205px;">{{ row.finishedProductCode }}</div>
              <div class="copyhover" @click="copytext(row.finishedProductCode)">
                <i class="el-icon-document-copy"></i>
              </div>
            </div>
          </template>
        </vxe-column>

        <vxe-column field="finishedProductName" title="成品名称" width="390" align="left">
          <template #default="{ row }">
            <div class="relativeboxx">
              <!-- @click="doubleclick(row)" -->
              <el-tooltip effect="dark" :content="row.finishedProductName" placement="top">
                <div class="point  textover" style="width: 355px;color:#409EFF" @click="doubleclick(row)">{{
                  row.finishedProductName }}</div>
              </el-tooltip>
              <div v-show="row.isMark" class="positioncenter"><el-badge is-dot class="item"></el-badge></div>
            </div>
          </template>
        </vxe-column>

       

        <!-- <vxe-column field="workPrice" title="加工工价" width="100" v-if="checkPermission('api:Inventory:PackagesProcessing:ProcessWorkPrice')">
          <template #default="{ row }">
            <el-button type="text" style="color: #999" @click="changepriceshow(row, 1)" size="mini">{{
              row.workPrice ? row.workPrice : 0 }}</el-button>
          </template>
        </vxe-column> -->
        <vxe-column field="dispatchWorkPrice" title="加工工价" width="125" sortable>
          <template #default="{ row }">
            <el-button type="text" style="color: #999" @click="changepriceshow(row, 2)" size="mini">{{
              row.dispatchWorkPrice ? row.dispatchWorkPrice : 0 }}</el-button>
          </template>
        </vxe-column>
        <!-- <vxe-column field="allotWorkPrice" title="调拨工价" width="100">
          <template #default="{ row }">
            <el-button type="text" style="color: #999" @click="changepriceshow(row, 4)" size="mini">{{
              row.allotWorkPrice ? row.allotWorkPrice : 0 }}</el-button>
          </template>
        </vxe-column> -->
        <vxe-column field="machineTypeName" title="加工方式" width="150" align="left" sortable>
          <template #default="{ row }">
            <div>{{ row.machineTypeName }}</div>
          </template>
        </vxe-column>

        <vxe-column field="combineCode" title="组合编码" width="130" align="left">
          <template #default="{ row }">
            <el-tag type="danger" v-if="row.combineCode == '多编码组合'">{{ row.combineCode }}</el-tag>
            <el-tag type="success" v-else-if="row.combineCode == '单编码多装'">{{ row.combineCode }}</el-tag>
            <el-tag type="primary" v-else-if="row.combineCode == '单编码单装'">{{ row.combineCode }}</el-tag>
          </template>
          <template #footer>
            <el-tooltip effect="dark" :content="summaryarry.combineDetialCode_sum" placement="top-start">
              <span>
                {{ summaryarry.combineCode_sum }}
              </span>
            </el-tooltip>
          </template>
        </vxe-column>

        <vxe-column field="processDate" title="加工日期" width="150" align="left" sortable>
          <template #default="{ row }">
            <div>{{ row.processDate }}</div>
          </template>
        </vxe-column>

        <vxe-column field="modifiedTime" title="修改日期" width="150" align="left" sortable>
          <template #default="{ row }">
            <el-tooltip effect="dark"
              :content="`${row.modifiedUserName ? row.modifiedUserName : ''}  |  ${row.modifiedTime ? row.modifiedTime : ''}`"
              placement="top-start">
              <div>{{ row.modifiedTime }}</div>
            </el-tooltip>
          </template>
        </vxe-column>

        <vxe-column field="createdTime" title="创建日期" width="170" align="left" sortable>
          <template #default="{ row }">
            <el-tooltip effect="dark"
              :content="`${row.createdUserName ? row.createdUserName : ''}  |  ${row.createdTime ? row.createdTime : ''}`"
              placement="top-start">
              <div>{{ row.createdTime }}</div>
            </el-tooltip>
          </template>
        </vxe-column>

        <vxe-column title="操作" align="left" width="220">
          <template #default="{ row }">
            <el-button type="primary" @click="creationprocessing(row)">创建加工</el-button>
            <el-button type="primary" icon="el-icon-picture-outline" @click="editImg(row)"></el-button>
            <el-button type="danger" icon="el-icon-delete" @click="deltask(row)"></el-button>
          </template>
        </vxe-column>
      </vxe-table>
      <!-- <div style="margin-top: -2px;">
        <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="total"
          :checked-count="sels.length" @page-change="pagechange" @size-change="sizechange" />
      </div> -->
    <!-- </div> -->

    <template #footer>
      <my-pagination :sizes="[50, 100, 300, 1000, 2000, 5000]" :page-size="1000" ref="pager" :total="total"
        :checked-count="sels.length" @page-change="pagechange" @size-change="sizechange" />
    </template>

    <el-dialog :visible.sync="showAddnew" width="750px" v-dialogDrag :close-on-click-modal="true">
      <el-form :model="addForm" ref="addForm" label-width="120px">
        <div class="bzjzcjrw">
          <div class="bt">
            <span style="float: left">新增成品绑定</span>
          </div>
          <div class="bzccjlx">
            <div class="lxwz">成品名称</div>
            <div class="lxwz2 formtop">
              <el-form-item prop="finishedProductName" label=" " label-width="12px">
                <span style="color: #F56C6C; margin: 0 7px 0 -13px">*</span>
                <vxe-input :maxlength="100" disabled style="height: 28px; width: 320px; font-size: 12px;"
                  v-model="addForm.finishedProductName" placeholder="成品名称" clearable></vxe-input>
              </el-form-item>
            </div>
          </div>

          <div class="bzccjlx">
            <div class="lxwz">成品编码</div>
            <div class="lxwz2">
              <el-form-item label=" " label-width="12px">
                <div style="display: flex; flex-direction: row;">
                  <span style="color: #F56C6C; margin: 0 7px 0 -13px">*</span>
                  <div style="width:65%">
                    <el-input :clearable="true" disabled @change="changecode" v-model="addForm.finishedProductCode"
                      :maxlength=100 key="cpbm">
                      <el-button slot="append" icon="el-icon-plus" @click="onSelctCp(0)"></el-button>
                    </el-input>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="bzccjlx">
            <div class="lxwz">加工方式</div>
            <div class="lxwz2">
              <el-form-item prop="machineTypeCode" label=" " label-width="12px">
                <span style="color: #F56C6C; margin: 0 7px 0 -13px">*</span>
                <el-select style="width:40%" v-model="addForm.machineTypeCode" filterable clearable>
                  <el-option v-for="item in machineTypeList" :key="item.setId" :label="item.sceneCode"
                    :value="item.setId" />
                </el-select>
              </el-form-item>
            </div>
          </div>

          <div class="box-card">
            <div slot="header" class="clearfix" style="display: flex;margin-bottom: 10px;">
              <div style="width:50%;line-height:28px;font-size:16px;"><span style="color: #F56C6C;">*</span>半成品编码</div>
              <div style="width:50%;text-align: right;"><el-button type="primary"
                  @click="onSelctCp(1)">选择半成品编码</el-button></div>
            </div>
            <div style="width:100% ;height: 300px;overflow: auto; border: 1px solid #dcdfe6;">
              <el-table :data="addForm.detialList" header-row-class-name="bcpb">
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                </el-table-column>
                <!-- <el-table-column prop="id" label="id" v-if="false" /> -->
                <el-table-column prop="halfProductCode" label="半成品编码" width="160" />
                <el-table-column prop="halfProductName" label="半成品名称" width="160" />
                <el-table-column prop="halfProductQuantity" label="组合数量" width="160">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.halfProductQuantity" :min="0" :max="10000" placeholder="数量"
                      :precision="4">
                    </el-input-number>
                  </template>
                </el-table-column>
                <el-table-column lable="操作">
                  <template slot-scope="scope">
                    <el-button type="danger" @click="onDelDtlGood(scope.$index)">移除 <i class="el-icon-remove-outline"></i>
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

        </div>

      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showAddnew = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit" v-loading="btnloading" v-throttle="2000">提 交</el-button>
      </span>
    </el-dialog>

    <el-dialog title="文件上传" :visible.sync="consumabledialogVisible" width="30%" v-dialogDrag :close-on-click-modal="true">
      <div style="padding: 30px;">
        <el-row>
          <el-col :span="12" :push="10">
            耗材图片
            <!-- <el-form-item prop="purImageUrl" label="出厂图"> -->
            <yh-img-upload :value.sync="consumableupForm.consumableImg" ref="conpplier_id" :limit="1"
              keys="one"></yh-img-upload>
            <!-- </el-form-item> -->
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="consumabledialogVisible = false">取 消</el-button>
        <el-button type="primary" v-throttle="200" @click="consumableupfinish">上 传</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="innerDialog" width="750px" v-dialogDrag :close-on-click-modal="true">
      <el-form :model="ruleForm" ref="ruleForm" label-width="120px">
        <div class="bzjzcjrw">
          <div class="bt">
            <span style="float: left">创建加工</span>
          </div>
          <div class="bzccjlx">
            <div class="lxwz">成品名称</div>
            <div class="lxwz2 formtop">
              <el-form-item prop="finishedProductName" label=" " label-width="12px">
                <span style="color: #F56C6C; margin: 0 7px 0 -13px">*</span>
                <vxe-input :maxlength="100" disabled style="height: 28px; width: 320px; font-size: 12px;"
                  v-model="ruleForm.finishedProductName" placeholder="成品名称" clearable></vxe-input>
              </el-form-item>
            </div>
          </div>

          <div class="bzccjlx">
            <div class="lxwz">成品编码</div>
            <div class="lxwz2">
              <el-form-item label=" " label-width="12px">
                <div style="display: flex; flex-direction: row;">
                  <span style="color: #F56C6C; margin: 0 7px 0 -13px">*</span>
                  <div style="width:63%">
                    <el-input :clearable="true" disabled @change="changecode" v-model="ruleForm.finishedProductCode"
                      :maxlength=100 key="cpbm">
                    </el-input>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>

          <div class="bzccjlx">
            <div class="lxwz">成品数量</div>
            <div class="lxwz2">
              <el-form-item label=" " label-width="12px">
                <div style="display: flex; flex-direction: row;">
                  <span style="color: #F56C6C; margin: 0 7px 0 -13px">*</span>
                  <el-input-number :step="1" controls-position="right" style="width:250px" :clearable="true"
                    v-model.trim="ruleForm.finishedProductQuantity" :min="1" :max="99999"
                    :precision="0"></el-input-number>
                </div>
              </el-form-item>
            </div>
          </div>

          <div class="bzccjlx">
            <div class="lxwz">成品调入仓</div>
            <div class="lxwz2">
              <el-form-item prop="finishTranWarehouse" label=" " label-width="12px">
                <el-select style="width:45%" v-model="ruleForm.finishTranWarehouse" :clearable="true"
                  :collapse-tags="true" filterable>
                  <el-option v-for="item in wareList" :key="item.wms_co_id" :label="item.name" :value="item.wms_co_id" />
                </el-select>
              </el-form-item>
            </div>
          </div>

          <div class="bzccjlx">
            <div class="lxwz">计划完成日期</div>
            <div class="lxwz2">
              <el-form-item prop="pfDate" label=" " label-width="12px">
                <el-date-picker v-model="ruleForm.pfDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date"
                  style="width:35%" placeholder="结束时间">
                </el-date-picker>
              </el-form-item>
            </div>
          </div>

          <div class="box-card">
            <div slot="header" class="clearfix" style="display: flex;margin-bottom: 10px;">
              <div style="width:50%;line-height:28px;font-size:16px;"><span style="color: #F56C6C;">*</span>半成品编码</div>
            </div>
            <div style="width:100% ;height: 300px;overflow: auto; border: 1px solid #dcdfe6;">
              <el-table :data="ruleForm.detialList" header-row-class-name="bcpb">
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                </el-table-column>
                <!-- <el-table-column prop="id" label="id" v-if="false" /> -->
                <el-table-column prop="halfProductCode" label="半成品编码" width="150" />
                <el-table-column prop="halfProductName" label="半成品名称" width="250" />
                <el-table-column prop="halfProductQuantity" label="组合数量">
                  <!-- <template slot-scope="scope">
                    <el-input-number v-model="scope.row.halfProductQuantity" :min="1" :max="10000" placeholder="数量"
                      :precision="0">
                    </el-input-number>
                  </template> -->
                </el-table-column>
                <!-- <el-table-column lable="操作">
                  <template slot-scope="scope">
                    <el-button type="danger" @click="onDelDtlGood(scope.$index)">移除 <i class="el-icon-remove-outline"></i>
                    </el-button>
                  </template>
                </el-table-column> -->
              </el-table>
            </div>
          </div>

        </div>

      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerDialog = false">取 消</el-button>
        <el-button type="primary" @click="onaddprocess" v-loading="btnloading" v-throttle="2000">提 交</el-button>
      </span>
    </el-dialog>

    <el-dialog title="导入工价" :visible.sync="dialogVisibleSyj" width="30%" v-dialogDrag>
      <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action accept=".xlsx"
        :file-list="fileList" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
        <template #trigger>
          <el-button size="small" type="primary">选取文件</el-button>
        </template>
        <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{
          (uploadLoading ? '上传中' : '上传') }}</el-button>
      </el-upload>
    </el-dialog>

    <!--选择商品-->
    <el-dialog title="选择编码" :visible.sync="goodschoiceVisible" width='88%' height='500px' v-dialogDrag append-to-body>
      <goodschoice :ischoice="true" ref="goodschoice" style="z-index:10000;height:500px" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="goodschoiceVisible = false">取 消</el-button>
          <el-button type="primary" @click="onQueren">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog title="编辑成品图片" :visible.sync="showfinishedProductImg" width="30%" v-dialogDrag
      :close-on-click-modal="true">
      <div style="padding: 30px;">
        <el-row>
          <el-col :span="12" :push="10">
            成品图
            <!-- <el-form-item prop="purImageUrl" label="出厂图"> -->
            <yh-img-upload :value.sync="upFinishForm.finishedProductImg" ref="supplier_id" :limit="1"
              keys="three"></yh-img-upload>
            <!-- </el-form-item> -->
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showfinishedProductImg = false">取 消</el-button>
        <el-button type="primary" @click="saveImg" :loading="uploadLoading">{{ (uploadLoading ? '上传中' : '上传')
        }}</el-button>
      </span>
    </el-dialog>

    <el-drawer :visible.sync="editTaskshow" size="824px">
      <el-form :model="editoperation" ref="editoperation" label-width="100px" style="height: 100%;overflow-y: auto;">
        <div class="bzjzcjrw">
          <div class="bt">
            <span style="float: left">编辑/操作</span>
          </div>
          <div class="rwmc">
            <div class="xh" style="width: 150px">
              <el-tooltip class="item" effect="dark" :content="editoperation.finishedProductCode" placement="top">
                <div style="overflow: hidden;  text-overflow: ellipsis;  white-space: nowrap;  ">
                  {{ editoperation.finishedProductCode }}
                </div>
              </el-tooltip>
            </div>
            <div class="mc" style="height: 66px">|</div>
            <div class="mc" style="width: 406px; height: 60px;">
              <el-tooltip class="item" effect="dark" :content="editoperation.finishedProductName" placement="top">
                <div style="overflow: hidden;  text-overflow: ellipsis;  white-space: nowrap;  ">
                  {{ editoperation.finishedProductName }}
                </div>
              </el-tooltip>
            </div>
            <div class="icon" style="float: right;width: 70px;">
              <el-button type="primary" @click="handleSubmit">保存</el-button>
            </div>
          </div>
          <div class="bzccjlxpur">
            <div class="lxwz">成品图片</div>
            <div class="lxwz2">
              <el-form-item prop="purImageUrl" label=" " label-width="12px">
                <div style="display: flex; flex-direction: row;">
                  <yh-img-upload :value.sync="editoperation.finishedProductImg" ref="supplier_id"
                    :limit="1"></yh-img-upload>
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="bzccjlxpur">
            <div class="lxwz">加工工价</div>
            <div class="lxwz2">
              <el-form-item prop="purImageUrl" label=" " label-width="12px">
                <el-input-number style="width: 220px;" v-model="editoperation.dispatchWorkPrice" clearable
                  placehold="加工工价" :precision="4" :min="0" :max="99999" :controls="false" />
              </el-form-item>
            </div>
          </div>
          <div class="bzccjlxpur">
            <div class="lxwz">加工方式</div>
            <div class="lxwz2">
              <el-form-item prop="purImageUrl" label=" " label-width="12px">
                <el-select v-model="editoperation.machineTypeName" @change="changeName" placeholder="加工方式">
                  <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>


          <div class="box-card">
            <div slot="header" class="clearfix" style="display: flex;margin-bottom: 10px;">
              <div style="width:50%;line-height:28px;font-size:16px;"><span>关联成品</span></div>
            </div>
            <div style="width:100% ;height: 200px;overflow: auto; border: 1px solid #dcdfe6;">
              <el-table :data="editoperation.detialList" header-row-class-name="bcpb" height="250">
                <el-table-column label="#" width="50" align="center">
                  <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                </el-table-column>
                <el-table-column prop="id" label="id" v-if="false" />
                <el-table-column prop="halfProductCode" label="成品编码" width="150" />
                <el-table-column prop="halfProductName" label="成品名称" />
                <el-table-column prop="halfProductQuantity" label="半成品组合">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.halfProductQuantity" :min="0" :max="9999" :precision="4"
                      label="半成品组合"></el-input-number>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div slot="header" class="clearfix" style="display: flex;margin-bottom: 10px;">
              <div style="width:50%;line-height:28px;font-size:16px;padding: 10px 0px 0px 0px;"><span>耗材</span></div>
            </div>
            <div style="width:100% ;height: 200px;overflow: auto; border: 1px solid #dcdfe6;">
              <el-table :data="editoperation.consumableList" header-row-class-name="bcpb" height="250">
                <el-table-column label="#" width="40" align="center">
                  <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                </el-table-column>
                <el-table-column prop="id" label="id" v-if="false" />
                <el-table-column prop="consumableProductCode" label="耗材编码" width="140" />
                <el-table-column prop="consumableProductName" label="耗材编码名称" />
                <el-table-column prop="consumableQuantity" label="所用数量" width="140">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.consumableQuantity" :min="0" :max="9999" disabled
                      label="所用数量"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column prop="consumableLength" label="长" width="140">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.consumableLength" :min="0" :max="9999" disabled
                      label="所用数量"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column prop="consumableWide" label="宽" width="140">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.consumableWide" :min="0" :max="9999" disabled
                      label="所用数量"></el-input-number>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </el-form>
    </el-drawer>
  </my-container>
</template>

<script>
import inputYunhan from "@/components/Comm/inputYunhan";
import YhImgUpload from "@/components/upload/yh-img-upload1.vue";
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import { getShootingSetData } from '@/api/media/shootingset'
import goodschoice from "@/views/base/goods/goods2.vue";
import {
  getCreateUserList, getRecordUser, getPriceTemplateList, getPackagesTypeListAsync
  , exportPackagesWorkPriceData, importWorkPriceAsync, updateFinishBindHalfProductImg, createPackagesProcessing,
  delFinishBindHalf, updateFinishBindHalfDetial, getFinishBindHalfList, addPackagesFinishBindHalf, getAllWarehouse,
  getFinishBindHalfDetailList, updateFinishBindDetial, getSelBindList, updateConsoumableProductImg
} from "@/api/inventory/packagesprocess";//包装加工
export default {
  name: 'processingfinished',
  components: { goodschoice, YhImgUpload, MyContainer, inputYunhan },
  data() {
    return {
      consumableupForm: {},
      consumabledialogVisible: false,
      listLoading: false,
      filter: {
        pageSize: 100,
        currentPage: 1,
        orderBy: "",
        isAsc: true,
        finishedProductCode: '',
        finishedProductName: '',
        halfProductCode: '',
        halfProductName: '',
        timeTypeList: ['1'],
        brandId: '',
        processTypeId: null,
        createdtimerange: [],
        processDaterange: [],
        isNew:null
      },
      wareList: [],//成品调入仓
      sels: [], // 列表选中列
      showAddnew: false,
      addForm: {
        detialList: [],
        halfProductName: "",
        halfProductCode: "",
        halfProductQuantity: 0,
        // createdtimerange: [],
        packagesProcessId: 0,
        finishedProductName: "",
        finishedProductCode: "",
        finishedProductImg: "",
        brandCode: "",
        brandName: "",
        packingMaterialCode: "",
        packingMaterialName: "",
        machineTypeCode: "",
        machineTypeName: "",
        packageSizeCode: "",
        packageSizeName: "",
        finishedProductQuantity: 0,
        pfDate: "",
        urgencyDegree: "",
        quantityRequired: 0,
        certificateInfo: "",
        remark: "",
      },
      showfinishedProductImg: false,
      fileList: [],
      uploadLoading: false,
      userid: '',
      keys: 'four',
      btnloading: false,
      pageLoading: false,
      disabled: true,
      upFinishForm: {},
      tableData: [],
      allAlign1: null,
      allAlign2: null,
      typeId: null,
      dialogVisibleSyj: false,
      seltype: null,
      goodschoiceVisible: false,
      finishedProductCode: "",
      pager: {},
      pickerOptions: {
        disabledDate(time) {
          // 设置禁用最小日期
          const minDate = new Date(1970, 0, 1);
          return time.getTime() < minDate.getTime();
        }
      },
      machineTypeList: [],
      innerDialog: false,
      ruleForm: {},
      total: null,
      editoperation: {},
      editTaskshow: false,
      options: null,
      allsellist: {},
      lastSortArgs: {
        field: "",
        order: "",
      },
    };
  },

  async mounted() {
    this.cancelEvent();
    await this.searcher();//获取搜索人员
    await this.onSearch();
    await this.getDataSetList();
    await this.setWare();//获取成品调入仓
  },
  async created() {
  },
  methods: {
    cancelEvent() {
      this.$nextTick(() => {
        const closeIcons = this.$refs.medicalAdviceRef.$el.querySelector('.el-select__tags').querySelectorAll('.el-icon-close')
        if (closeIcons.length > 0) { // 确保至少有一个选项被选择
          Array.from(closeIcons).forEach((item, index) => {
            item.style.display = 'inline-block'; // 首先保证所有关闭图标都是可见的
            if (index === closeIcons.length - 1) { // 然后隐藏最后一个选项的关闭图标
              item.style.display = 'none';
            }
          });
        }
      });
    },
    //自定义排序
    async customSortMethod({ data, sortList }) {
      if (sortList && sortList.length > 0) {
        if (sortList[0].field != this.lastSortArgs.field || sortList[0].order != this.lastSortArgs.order) {
          this.lastSortArgs = { ...sortList[0] };
          let a = {
            order: (this.lastSortArgs.order.indexOf('desc') > -1 ? 'descending' : 'asc'),
            prop: this.lastSortArgs.field
          };
          this.filter.orderBy = a.prop
          this.filter.isAsc = a.order.indexOf("descending") == -1 ? true : false
          this.pageLoading = true;
          this.onSearch()
        }
      }
    },
    //成品编码
    async callbackGoodsCode(val) {
      this.filter.finishedProductCode = val;
    },
    async searcher() {
      const { data } = await getSelBindList()
      if (data) {
        this.allsellist.brandList = data.brandUserList.map(item => ({ value: item.id, label: item.brandName }));
        this.allsellist.createUser = data.createUserList;
      }
    },
    async handleSubmit() {
      const { success } = await updateFinishBindDetial(this.editoperation)
      if (success) {
        this.$message({
          message: '操作成功！',
          type: 'success'
        })
        this.editTaskshow = false;
      }
      //重新拉取数据
      await this.onSearch();
    },
    changeName(e) {
      this.editoperation.machineTypeCode = e
    },
    async consumableupfinish() {
      const res = await updateConsoumableProductImg(this.consumableupForm);
      if (!res?.success) {
        return
      }
      this.$message({
        message: '操作成功！',
        type: 'success'
      })
      // this.$emit('getTaskList');
      this.getneireq();
      this.consumabledialogVisible = false;
    },
    consumableshowupfuc(row) {
      this.consumableupForm.consumableId = row.id;
      this.consumableupForm.consumableImg = row.consumableImg;
      let _this = this;
      _this.$nextTick(() => {
        _this.consumabledialogVisible = true;
      })
    },
    //获取加工方式
    async getPrcductType() {
      const { data, success } = await getShootingSetData({ setType: 16 })
      if (success) {
        this.options = data.data.map(item => {
          return {
            label: item.sceneCode,
            value: item.setId
          }
        })
        if (this.editoperation.machineTypeCode) {
          this.options.filter(item => {
            if (item.value == this.editoperation.machineTypeCode) {
              this.editoperation.machineTypeName = item.label
            }
          })
        }
        console.log(this.editoperation.machineTypeCode, 'this.editoperation.machineTypeCode');
      }
    },
    async doubleclick(row) {
      const { data, success } = await getFinishBindHalfDetailList({ pid: row.packagesProcessingId })
      if (success) {
        this.editoperation = data;
        this.getPrcductType()
        this.editTaskshow = true;
      }
    },
    //创建加工保存
    async onaddprocess() {
      this.btnloading = true;
      const params = { ...this.ruleForm }
      const res = await createPackagesProcessing(params)
      if (res?.success) {
        this.$message({ message: res.data, type: "success" });
        this.innerDialog = false;
      }
      this.btnloading = false;
    },
    //获取成品调入仓
    async setWare() {
      const { data, success } = await getAllWarehouse();
      if (!success) {
        return
      }
      this.wareList = data;
    },
    //删除成品绑定
    async deltask(row) {
      this.$confirm("是否删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const { success } = await delFinishBindHalf({ packprocessId: row.packagesProcessingId });
          if (success) {
            this.$message({ message: '删除成功！', type: "success" });
            await this.onSearch();
          } else {
            this.$message({ showClose: false, message: '删除失败！', type: 'error' });
          }
        }).catch(() => {
        });
    },
    //编辑成品图片
    async saveImg() {
      this.uploadLoading = true;
      await updateFinishBindHalfProductImg(this.upFinishForm).then(res => {
        this.uploadLoading = false;
        if (!res?.success) {
          return
        }
        if (res?.success) {
          this.onSearch()
          this.$message({
            message: '操作成功！',
            type: 'success'
          })
          this.showfinishedProductImg = false;
        }
      });
    },
    // 编辑成品图
    editImg(row) {
      this.upFinishForm.priceTemplateId = row.packagesProcessingId;
      this.upFinishForm.finishedProductImg = row.finishedProductImg;
      this.upFinishForm.typeId = 1;
      this.$nextTick(() => {
        this.showfinishedProductImg = true;
      })
    },
    //重置
    onclear() {
      this.filter.finishedProductCode = '';
      this.filter.finishedProductName = '';
      this.filter.halfProductCode = null;
      this.filter.halfProductName = '';
    },
    //复制内容
    copytext(e) {
      let textarea = document.createElement("textarea")
      textarea.value = e
      textarea.readOnly = "readOnly"
      document.body.appendChild(textarea)
      textarea.select()
      let result = document.execCommand("copy")
      if (result) {
        this.$message({
          message: '复制成功',
          type: 'success'
        })
      }
      textarea.remove()
    },
    //导出
    async handleExportCommand(command) {
      var res = null;
      var fileName = "包装加工-"
      const params = { ... this.filter }
      this.pageLoading = true;
      var res = await exportPackagesWorkPriceData(params);
      fileName = fileName + " 成品绑定半成品_"
      this.pageLoading = false;
      const aLink = document.createElement("a");
      let blob = new Blob([res?.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', fileName + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    //导入
    submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      if (this.$refs.upload.uploadFiles.length != 0) {
        this.$refs.upload.submit();
      } else {
        this.$message({
          type: 'fail',
          message: '请选择文件！'
        })
      }
    },
    async uploadFile(item) {
      this.uploadLoading = true;
      if (!item || !item.file || !item.file.size) {
        this.uploadLoading = false;
        this.$message({ message: "请先选择文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      let res = await importWorkPriceAsync(form);
      if (res.code == 1) {
        // this.importDialog.visible = false
        this.dialogVisibleSyj = false;
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      }
      this.fileList = []
      this.uploadLoading = false
    },
    async uploadChange(file, fileList) {
      let files = [];
      files.push(file);
      this.fileList = files;
    },
    async uploadRemove(file, fileList) {
      let files = [];
      this.fileList = files;
    },
    //导入工价
    onImportPrice() {
      this.dialogVisibleSyj = true
    },
    //新增成品绑定保存
    onSubmit() {
      this.btnloading = true;
      if (this.addForm.machineTypeCode == '') {
        this.$message({ message: '请选择加工方式', type: 'error' });
        this.btnloading = false;
        return;
      }
      if (this.addForm.detialList.some(obj => obj.halfProductQuantity === 0)) {
        this.$message({ message: '请选择半成品组合', type: 'error' });
        this.btnloading = false;
        return;
      }
      if (!this.finishedProductCode) {
        this.$message({ message: '请选择成品编码', type: 'error' });
        this.btnloading = false;
        return;
      }
      if (this.addForm.detialList.length < 1) {
        this.$message({ message: '请选择半成品编码', type: 'error' });
        this.btnloading = false;
        return;
      }
      if (this.finishedProductCode == '暂无编码') {
        this.btnloading = false;
        this.addForm.finishedProductCode = '暂无编码';
      }
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          addPackagesFinishBindHalf(this.addForm).then(res => {
            if (!res.success) {
              this.btnloading = false;
              return
            } else {
              this.$message({ message: '添加成功', type: 'success' })
              this.showAddnew = false;
              this.btnloading = false;
              this.onSearch();
            }
          })
        }
      })
    },
    //移除明细
    async onDelDtlGood(index) {
      this.addForm.detialList.splice(index, 1);
    },
    //获取加工方式
    async getDataSetList() {
      const res = await getShootingSetData({ setType: 16 })
      this.machineTypeList = res.data.data.map(obj => ({ setId: obj.setId, sceneCode: obj.sceneCode }));
    },
    onSelctCp(type) {
      this.seltype = type;
      this.goodschoiceVisible = true;
      this.$nextTick(() => {
        this.$refs.goodschoice.removeSelData();
      })
    },
    changecode(val) {
      this.addForm.finishedProductCode = val;
    },
    //选择商品确定
    async onQueren() {
      if (this.seltype == 0) {
        //选择成品商品确定
        var choicelist = await this.$refs.goodschoice.getchoicelistOnly();
        if (choicelist && choicelist.length == 1) {
          choicelist.forEach(f => {
            //反填数据
            this.addForm.finishedProductCode = f.goodsCode;
            this.finishedProductCode = f.goodsCode;
            this.addForm.finishedProductName = f.goodsName;
          })
          this.goodschoiceVisible = false;
        }
      }
      else {
        //选择半成品商品确定
        var choicelist = await this.$refs.goodschoice.getchoicelist();
        if (choicelist && choicelist.length > 0) {
          //反填数据,
          // if (this.addForm.detialList) {
          if (!this.addForm.detialList) {
            this.addForm.detialList = []
          }
          //已存在的不添加
          var temp = this.addForm.detialList;
          var isNew = true;
          choicelist.forEach(f => {
            isNew = true;
            if (temp?.length > 0) {
              temp.forEach(old => {
                if (old.halfProductCode == f.goodsCode) {
                  isNew = false;
                }
              });
            }
            if (isNew) {
              this.addForm.detialList.push({ halfProductCode: f.goodsCode, halfProductName: f.goodsName, halfProductQuantity: 0, dtlActualGoodsAmount: 0 });

            } else {
              this.addForm.detialList.forEach(told => {
                if (told.halfProductCode == f.goodsCode) {
                  told.halfProductName = f.goodsName;
                }
              });
            }
          })
          // }
          this.goodschoiceVisible = false;
        }
      }
    },
    async onSearch() {
      // this.$refs.pager.setPage(1);
      this.getTaskList();
    },
    //获取数据
    async getTaskList() {
      // var pager = this.$refs.pager.getPager();
      if (this.filter.createdtimerange) {
        this.filter.startCreateTime = this.filter.createdtimerange[0];
        this.filter.endCreateTime = this.filter.createdtimerange[1];
      } else {
        this.filter.startCreateTime = null;
        this.filter.endCreateTime = null;
      }
      if (this.filter.processDaterange) {
        this.filter.startProcessDate = this.filter.processDaterange[0];
        this.filter.endProcessDate = this.filter.processDaterange[1];
      } else {
        this.filter.startProcessDate = null;
        this.filter.endProcessDate = null;
      }
      if (this.filter.startCreateTime && this.filter.endCreateTime) {
        if (this.filter.timeTypeList && this.filter.timeTypeList.length == 0) {
          this.$message({ message: '请选择时间维度', type: 'error' });
          return
        }
      }
      // this.filter.isComplate = this.filter.isComplateChecked == true ? 0 : 1;
      const params = {
        // ...pager,
        // ...this.pager,
        ...this.addForm,
        ...this.filter,
      };
      // debugger
      // params.platform.length>0?params.platform:params.platform=""
      this.listLoading = true;
      // this.$refs.refpackagelist.tableloading = true;
      this.pageLoading = true;
      const res = await getFinishBindHalfList(params);
      // debugger
      if (!res?.success) return;
      this.tableData = res.data.list;
      this.listLoading = false;
      this.total = res.data.total;
      this.summaryarry = res.data.summary;
      this.pageLoading = false;
    },
    //分页
    sizechange(val) {
      this.filter.currentPage = 1;
      this.filter.pageSize = val;
      this.onSearch();
    },
    //分页
    pagechange(val) {
      this.filter.currentPage = val;
      this.onSearch();
    },
    //二级表格复选框
    secondcheckmethod(row) {
      this.secondprintrow = row.records;
      this.seconduserIdList = row.records.map(record => record.userId)
    },
    //一级表格复选框
    checkmethod(row) {
      this.printrow = row.records;
      this.userIdList = row.records.map(record => record.userId)
    },
    //创建加工
    creationprocessing(row) {
      this.editIndex = this.tableData.indexOf(row); // 设置当前编辑的行索引
      this.innerDialog = true; // 打开编辑对话框
      this.ruleForm = { ...row };
    },
    //新增成品绑定
    addNew() {
      this.addForm = {
        packagesProcessId: 0,
        finishedProductName: "",
        finishedProductCode: "",
        finishedProductImg: "",
        brandCode: "",
        brandName: "",
        packingMaterialCode: "",
        packingMaterialName: "",
        machineTypeCode: "",
        machineTypeName: "",
        packageSizeCode: "",
        packageSizeName: "",
        finishedProductQuantity: 0,
        pfDate: "",
        urgencyDegree: "",
        quantityRequired: 0,
        certificateInfo: "",
        remark: "",
        detialList: []
      },
        this.finishedProductCode = '';
      this.showAddnew = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.heardcss {
  width: 100%;
  min-width: 1150px;
  min-height: 35px;
  // background-color: aqua;
  box-sizing: border-box;
  display: inline-block;
  margin-top: 8px;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.heardcss span,
.ssanc span {
  padding: 4px 0.2% 4px 0;
}

.ssanc {
  width: 100%;
  height: 38px;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  display: flex;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

::v-deep .el-button-group>.el-button:last-child {
  margin-left: -2px !important;
}

::v-deep .el-button-group {
  margin: 0 !important;
  padding: 0 !important;
}

::v-deep .el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 20px;
}

::v-deep .vxe-table--render-default .vxe-header--column {
  line-height: 18px !important;
}

/*滚动条整体部分*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar {
  width: 18px !important;
  height: 26px !important;
}

/*滚动条的轨道*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-track {
  background-color: #f1f1f1 !important;
}

/*滚动条里面的小方块，能向上向下移动*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
  background-color: #c1c1c1 !important;
  border-radius: 3px !important;
  box-sizing: border-box !important;
  border: 2px solid #F1F1F1 !important;
  box-shadow: inset 0 0 6px rgba(255, 255, 255, .5) !important;
}

// 滚动条鼠标悬停颜色
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
  background-color: #A8A8A8 !important;
}

// 滚动条拖动颜色
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
  background-color: #787878 !important;
}

/*边角，即两个滚动条的交汇处*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
  background-color: #dcdcdc !important;
}


// 表格内边距
::v-deep .vxe-table--render-default .vxe-cell {
  padding: 0 0 0 8px !important;
}





.flexrow {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.qualibtn ::v-deep .el-button {
  margin-left: 0 !important;
  margin-top: 10px;
}

.marginrt {
  margin: 0 10px 0 auto;
}

.point:hover {
  cursor: pointer;
}

.point {
  color: #606266;
}


.item {
  margin-bottom: 18px;
}

.clearfix {
  font-size: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both
}

::v-deep .box-card {
  margin-top: 20px;
  box-sizing: border-box;
  padding: 0 30px;

}

.flexcenter {
  display: flex;
  justify-content: left;
  align-items: left;
}

::v-deep .height {
  height: 58px !important;
}

::v-deep .height1 {
  height: 48px !important;
  font-size: 12px !important;
}

::v-deep .cellheight1 {
  font-size: 12px !important;
}

.relativebox {
  position: relative;
  width: 100%;
}

.relativeboxx {
  position: relative;
  width: 100%;
}

.positioncenter {
  position: absolute;
  right: 10px;
  top: 28%;
  bottom: 50%;
  // transform: translate(-50%,-50%);
}

::v-deep .droprow td {
  color: rgb(250, 9, 9);
  position: relative;
}

::v-deep .droprow ::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 0.1px;
  background-color: rgb(250, 9, 9);
  transform: translateY(-50%);
}


.copyhover {
  display: none;
  cursor: pointer;
}

.relativebox {
  width: 250px;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.relativebox:hover {
  width: 225px;
}

.textover {
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.relativebox:hover .textover {
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.relativebox:hover .copyhover {
  display: block;
  position: absolute;
  top: 50%;
  left: 60%;
  margin: 0 10px;
  z-index: 99;
  transform: translate(-50%, -50%);
  color: #409EFF;
  font-weight: 600;
}

// .copyhover:hover>.copyhover{
//   display: block;

//   position: absolute;
//   top: 50%;
//   left: 50%;
//   transform: translate(-50%,-50%);
// }

.minibtn ::v-deep .el-button--mini {
  padding: 7px 8px;
}

.vxeclass {
  z-index: 10000 !important;
}

::v-deep .vxe-body--expanded-cell {
  padding: 0 !important;
}

.el-icon-document-copy {
  font-size: 16px;
  margin-left: 2px;
}

.el-icon-document-copy:hover {
  font-size: 16px;
  margin-left: 2px;

}

::v-deep .vxe-modal--header {
  margin-top: 8px !important;
  background-color: transparent !important;
  padding: 0 6px;

}

::v-deep .vxe-modal--header-title {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

::v-deep .vxe-modal--header-right {
  // color: transparent ;
  font-size: 12px;
  line-height: 32px;
}

::v-deep .vxe-modal--content {
  padding: 20px 35px 35px 35px;
}

::v-deep .bzbjbt {
  height: 60px;
  background-color: rgb(255, 255, 255);
  font-size: 18px;
  color: #666;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 20px 35px;
}

::v-deep .rwmc {
  // width: 750px;
  height: 70px;
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 3px 5px #eeeeee;
  box-sizing: border-box;
  padding: 0 56px;
  display: flex;
}

::v-deep .rwmc .xh {
  height: 65px;
  font-size: 18px;
  line-height: 68px;
  box-sizing: border-box;
  padding: 0 2px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #666;
}

.tablehei {
  height: 80vh;
}

::v-deep .rwmc .mc,
::v-deep .icon {
  height: 65px;
  font-size: 18px;
  line-height: 68px;
  box-sizing: border-box;
  margin-left: 10px;
  padding: 0 2px;
  display: inline-block;
  color: #666;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::v-deep .el-drawer .el-drawer__header {
  padding: 0 !important;
}

::v-deep .vxe-footer--row {
  height: 50px;
}

.statuscss ::v-deep .el-tag--dark {
  border-color: #fff !important;
}

.minisize ::v-deep .vxe-button {
  padding: 0 !important;
}

::v-deep .el-main {
  overflow: hidden;
}

::v-deep .bzjzcjrw {
  width: 100%;
  margin-top: 15px;
  background-color: #fff;
}

::v-deep .bzjzcjrw .bt {
  height: 40px;
  /* background-color: aquamarine; */
  font-size: 18px;
  color: #666;
  margin-bottom: 20px;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 0 35px;
}

::v-deep .bzjzcjrw .bzccjlx {
  box-sizing: border-box;
  padding: 0 30px;
  display: flex;
}

::v-deep .bzjzcjrw .bzccjlx {
  width: 100%;
  height: 35px;
  box-sizing: border-box;
  padding: 0 30px;
  display: flex;
}

::v-deep .bzjzcjrw .bzccjlx .lxwz {
  width: 20%;
  font-size: 14px;
  color: #666;
  vertical-align: top;
  line-height: 26px;
  /* background-color: rgb(204, 204, 255); */
}

::v-deep .bzjzcjrw .bzccjlx .lxwz2 {
  width: 80%;
}

::v-deep .bzjzcjrw .bzccjlxpur {
  width: 100%;
  height: 75px;
  box-sizing: border-box;
  padding: 0 30px;
  display: flex;
}

::v-deep .bzjzcjrw .bzccjlxpur .lxwz {
  width: 20%;
  font-size: 14px;
  color: #666;
  vertical-align: top;
  line-height: 26px;
}

::v-deep .bzjzcjrw .bzccjlxpur .lxwz2 {
  width: 80%;
  text-align: left;
}
</style>

