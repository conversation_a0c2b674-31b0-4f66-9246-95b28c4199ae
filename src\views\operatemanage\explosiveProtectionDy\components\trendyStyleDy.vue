<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <div class="top">
        <!-- <el-date-picker style="width: 230px" class="publicCss" v-model="daterange" type="daterange" format="yyyy-MM-dd"
          value-format="yyyy-MM-dd" range-separator="至" start-placeholder="添加开始日期" end-placeholder="添加结束日期"
          :clearable="true" :picker-options="pickerOptions">
        </el-date-picker> -->
        <el-input v-model.trim="filter.styleCode" placeholder="系列编码" class="publicCss" clearable maxlength="40" />
        <el-input v-model.trim="filter.productID" placeholder="产品ID" class="publicCss" clearable maxlength="50" />
        <el-select v-model="filter.stats" clearable filterable placeholder="类型" allow-create default-first-option
          class="publicCss">
          <el-option v-for="item in statsList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-input v-model.trim="filter.attributeTag" placeholder="属性" class="publicCss" clearable maxlength="40" />
        <el-input v-model.trim="filter.title" placeholder="产品名称" class="publicCss" clearable maxlength="50" />
        <el-select v-model="filter.shopCode" class="publicCss" size="mini" placeholder="店铺" clearable filterable>
          <el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select filterable v-model="filter.groupId" placeholder="小组" class="publicCss" clearable>
          <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key" />
        </el-select>
        <el-select filterable v-model="filter.operateSpecialUserId" collapse-tags clearable placeholder="运营专员"
          class="publicCss">
          <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select filterable v-model="filter.userId" collapse-tags clearable placeholder="运营助理" class="publicCss">
          <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="filter.brands" clearable filterable placeholder="品牌" multiple collapse-tags
          class="publicCss" style="width: 150px;">
          <el-option v-for="item in brandList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-button type="primary" @click="onSearch">查询</el-button>
      </div>
      <div class="top">
        <el-button type="primary" @click="onExport">导出</el-button>
        <el-button type="primary" @click="onOneKeyLoss(1, {})"
          v-if="checkPermission('DyExplosiveProtectionOneClickLoss')">一键流失</el-button>
        <el-button type="primary" @click="onOneKeyLoss(2, {})"
          v-if="checkPermission('DyExplosiveProtectionOneKeyHit')">一键转爆款</el-button>
        <el-button type="primary" @click="onRebrand"
          v-if="checkPermission('DyExplosiveProtectionRebrand')">修改品牌</el-button>
        <el-button type="primary" @click="onModificationTimed"
          v-if="checkPermission('DyExplosiveProtectionRebrand')">修改爆款/趋势时间</el-button>
      </div>
    </template>

    <vxetablebase :id="'trendyStyleDy202502261850'" :border="true" :align="'center'"
      :tablekey="'trendyStyleDy202502261850'" ref="table" :that='that' :isIndex='true' :hasexpand='false'
      @sortchange='sortchange' :isSelectColumn="true" :showsummary='false' :tablefixed='true' :summaryarry='summaryarry'
      :tableData='datalist' :tableCols='tableCols' :loading="listLoading" style="width:100%;height:100%;margin: 0"
      :xgt="9999" @select="callback">
      <template slot="right">
        <vxe-column title="操作" width="120" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;align-items: center;">
              <el-button type="text" @click="onOneKeyLoss(3, row)"
                v-if="checkPermission('DyExplosiveProtectionOneKeyHit')">转爆款</el-button>
              <el-button type="text" @click="onOneKeyLoss(4, row)"
                v-if="checkPermission('DyExplosiveProtectionOneClickLoss')">流失</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>

    <el-dialog :title="isBrand ? '修改品牌' : '修改爆款/趋势时间'" :visible.sync="brandInfo.visible"
      :width="isBrand ? '20%' : '35%'" v-dialogDrag :close-on-click-modal="false">
      <div style="padding-top: 20px;">
        <el-form :model="brandInfo" label-width="130px" :rules="brandrules" ref="brandInfoRef">
          <el-row v-if="isBrand">
            <el-form-item label="品牌" prop="brand">
              <el-input v-model.trim="brandInfo.brand" placeholder="品牌" maxlength="5" clearable style="width: 80%;" />
            </el-form-item>
          </el-row>
          <el-row v-else>
            <el-col :span="12">
              <el-form-item label="爆款时间" prop="baokuanStartUseDate">
                <el-date-picker v-model="brandInfo.baokuanStartUseDate" type="datetime" placeholder="爆款时间"
                  style="width: 90%;" :value-format="'yyyy-MM-dd HH:mm:ss'" :picker-options="pickerOptionsFast" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="爆款结束时间" prop="baokuanEndUseDate">
                <el-date-picker v-model="brandInfo.baokuanEndUseDate" type="datetime" placeholder="爆款结束时间"
                  style="width: 90%;" :value-format="'yyyy-MM-dd HH:mm:ss'" :picker-options="pickerOptionsDisabled" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="普佣率" prop="grossCommissionRate">
                <div style="display: flex; align-items: center; width: 100%;">
                  <el-input-number v-model="brandInfo.grossCommissionRate" placeholder="普佣率" style="flex: 1;width:85%"
                    :controls="false" :precision="0" :min="0" :max="100" />
                  <span style="margin-left: 4px;">%</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最大定向佣金率" prop="maximumDirectionalGold">
                <div style="display: flex; align-items: center; width: 100%;">
                  <el-input-number v-model="brandInfo.maximumDirectionalGold" placeholder="最大定向佣金率"
                    style="flex: 1;width:85%" :controls="false" :precision="0" :min="0" :max="100" />
                  <span style="margin-left: 4px;">%</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="brandInfo.visible = false">关闭</el-button>
        <el-button type="primary" @click="onVerifyBrand">确定</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>
import dayjs from "dayjs";
import { platformlist } from '@/utils/tools'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatPlatform, formatTime, pickerOptions, formatLinkProCode } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import inputYunhan from "@/components/Comm/inputYunhan";
import { getExplosiveProtectionDyAsyncList, importExplosiveProtectionDyAsync, deleteExplosiveProtectionDyAsync, updateExplosiveProtectionDyAsync, exportExplosiveProtectionDy, editExplosiveProtectionDyBand } from '@/api/bookkeeper/reportdayV2'
import buschar from '@/components/Bus/buschar'
import { downloadLink } from "@/utils/tools.js";
import decimal from '@/utils/decimal'

const tableCols = [
  { istrue: true, label: '', type: "checkbox" },
  { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom', width: '200' },
  { istrue: true, prop: 'brand', label: '品牌', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'productCategoryName', label: '类目', sortable: 'custom', width: '120' },
  { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: '120' },
  { istrue: true, prop: 'proCode', label: '产品ID', sortable: 'custom', width: '120', type: 'html', formatter: (row) => formatLinkProCode(6, row.proCode) },
  { istrue: true, prop: 'stats', label: '类型', sortable: 'custom', width: '120' },
  { istrue: true, prop: 'attributeTag', label: '属性', sortable: 'custom', width: '120' },
  { istrue: true, prop: 'title', label: '产品名称', sortable: 'custom', width: '300' },
  { istrue: true, prop: 'groupName', label: '小组', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'operateSpecialUserName', label: '专员', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'userName', label: '助理', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'createdTime', label: '添加时间', sortable: 'custom', width: '150' },
  { istrue: true, prop: 'explosionEndTime', label: '趋势结束时间', sortable: 'custom', width: '150' },
  {
    istrue: true, summaryEvent: true, prop: 'allaround', label: `近三天`, merge: true, prop: 'mergeField3', width: '240',
    cols: [
      { istrue: true, prop: 'day3OrderCount', label: '订单量', width: '80', sortable: 'custom' },
      { istrue: true, prop: 'day3SaleAmount', label: '销售额', width: '80', sortable: 'custom' },
      { istrue: true, prop: 'day3SumAdv', label: '总广告费', width: '80', sortable: 'custom' },
      { istrue: true, prop: 'day3Profit6Rate', label: '毛六利润率', width: '80', sortable: 'custom', formatter: (row) => row.day3Profit6Rate ? row.day3Profit6Rate + '%' : '0%' },
      { istrue: true, prop: 'grossCommissionRate', label: '普佣率', width: '80', sortable: 'custom', formatter: (row) => row.grossCommissionRate ? row.grossCommissionRate * 100 + '%' : '0%' },
      { istrue: true, prop: 'maximumDirectionalGold', label: '最大定向佣金率', width: '80', sortable: 'custom', formatter: (row) => row.maximumDirectionalGold ? row.maximumDirectionalGold * 100 + '%' : '0%' },
    ]
  },
];
const startUseDate = formatTime(dayjs(), "YYYY-MM-DD");
const endUseDate = formatTime(dayjs(), "YYYY-MM-DD");

export default {
  name: "trendyStyleDy",
  components: {
    MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, vxetablebase, inputYunhan, buschar
  },
  props: {
    directorGroupList: {
      type: Array,
      default: () => []
    },
    shopList: {
      type: Array,
      default: () => []
    },
    directorlist: {
      type: Array,
      default: () => []
    },
    statsList: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      pickerOptionsFast: {
        shortcuts: [
          {
            text: '昨天',
            onClick(picker) {
              picker.$emit('pick', dayjs().subtract(1, 'day').toDate());
            }
          },
          {
            text: '三天前',
            onClick(picker) {
              picker.$emit('pick', dayjs().subtract(3, 'day').toDate());
            }
          },
          {
            text: '一周前',
            onClick(picker) {
              picker.$emit('pick', dayjs().subtract(1, 'week').toDate());
            }
          },
          {
            text: '半个月前',
            onClick(picker) {
              picker.$emit('pick', dayjs().subtract(15, 'day').toDate());
            }
          },
          {
            text: '一个月前',
            onClick(picker) {
              picker.$emit('pick', dayjs().subtract(1, 'month').toDate());
            }
          }
        ]
      },
      pickerOptionsDisabled: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        },
      },
      isBrand: false,
      brandrules: {
        brand: [{ required: true, message: '请输入品牌', trigger: 'blur' }],
        baokuanStartUseDate: [{ required: true, message: '请选择爆款时间', trigger: 'blur' }],
        baokuanEndUseDate: [{ required: true, message: '请选择爆款结束时间', trigger: 'blur' }],
        grossCommissionRate: [{ required: true, message: '请输入普佣率', trigger: 'blur' }],
        maximumDirectionalGold: [{ required: true, message: '请输入最大定向佣金率', trigger: 'blur' }]
      },
      brandInfo: {
        visible: false,
        brand: '',
        baokuanStartUseDate: '',
        baokuanEndUseDate: '',
        grossCommissionRate: undefined,
        maximumDirectionalGold: undefined,
        stats: null,
      },
      that: this,
      daterange: [startUseDate, endUseDate],
      filter: {
        shopCode: "",
        attributeTag: null,
        startUseDate: null,
        endUseDate: null,
        productID: "",
        title: "",
        styleCode: "",
        groupId: "",
        operateSpecialUserId: "",
        userId: "",
        dataType: '趋势款',
        stats: "",
        brands: [],
      },
      pickerOptions: pickerOptions,
      tableCols: tableCols,
      total: 0,
      datalist: [],
      brandList: [],
      pager: { OrderBy: "", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      summaryarry: {},
      selids: [],
      selrows: [],// 列表选中行
    };
  },
  async mounted() {
    await this.onSearch();
  },
  methods: {
    onModificationTimed() {
      if (this.selrows.length <= 0) {
        this.$message({ type: 'error', message: '请勾选行数据!' });
        return;
      }
      this.brandInfo.visible = true;
      this.$nextTick(() => {
        this.$refs.brandInfoRef.clearValidate();
        this.$refs.brandInfoRef.resetFields();
        this.isBrand = false;
        this.brandInfo.stats = '趋势款'
        if (this.selrows.length == 1) {
          this.brandInfo.baokuanStartUseDate = this.selrows[0].yearMonthDayDate;
          this.brandInfo.baokuanEndUseDate = this.selrows[0].explosionEndTime;
          this.brandInfo.grossCommissionRate = this.selrows[0].grossCommissionRate ? decimal(this.selrows[0].grossCommissionRate || 0, 100, 0, '*') : 0;
          this.brandInfo.maximumDirectionalGold = this.selrows[0].maximumDirectionalGold ? decimal(this.selrows[0].maximumDirectionalGold || 0, 100, 0, '*') : 0;
        } else {
          this.brandInfo.baokuanStartUseDate = '';
          this.brandInfo.baokuanEndUseDate = '';
          this.brandInfo.grossCommissionRate = undefined;
          this.brandInfo.maximumDirectionalGold = undefined;
        }
      });
    },
    onRebrand() {
      if (this.selrows.length <= 0) {
        this.$message({ type: 'error', message: '请勾选行数据!' });
        return;
      }
      this.brandInfo.visible = true;
      this.isBrand = true;
      this.$nextTick(() => {
        this.$refs.brandInfoRef.clearValidate();
        this.$refs.brandInfoRef.resetFields();
        if (this.selrows.length == 1) {
          this.brandInfo.brand = this.selrows[0].brand;
        } else {
          this.brandInfo.brand = '';
        }
      });
    },
    onVerifyBrand() {
      this.$refs.brandInfoRef.validate(async (valid) => {
        if (valid) {
          const params = {
            ids: this.selrows.map(f => f.id),
            brand: this.brandInfo.brand,
            stats: this.brandInfo.stats,
            baokuanStartUseDate: this.brandInfo.baokuanStartUseDate,
            baokuanEndUseDate: this.brandInfo.baokuanEndUseDate,
            grossCommissionRate: this.brandInfo.grossCommissionRate ? decimal(this.brandInfo.grossCommissionRate || 0, 100, 2, '/') : 0,
            maximumDirectionalGold: this.brandInfo.maximumDirectionalGold ? decimal(this.brandInfo.maximumDirectionalGold || 0, 100, 2, '/') : 0,
          }
          if (this.isBrand) {
            delete params.stats;
            delete params.baokuanStartUseDate;
            delete params.baokuanEndUseDate;
          } else {
            delete params.brand;
          }
          var res = await editExplosiveProtectionDyBand(params);
          if (res?.success) {
            if (!res?.data.success) {
              this.$message({ type: 'error', message: res?.data.msg });
            } else {
              this.$message({ type: 'success', message: '操作成功' });
              this.brandInfo.visible = false;
              this.selrows = [];
              await this.onSearch();
            }
          }
        }
      });
    },
    // 搜索
    async onSearch() {
      this.$refs.pager.setPage(1);
      await this.getList();
    },
    // 获取条件
    getCondition() {
      this.filter.startUseDate = null;
      this.filter.endUseDate = null;
      // if (this.daterange) {
      //   this.filter.startUseDate = this.daterange[0];
      //   this.filter.endUseDate = this.daterange[1];
      // } else {
      //   this.filter.startUseDate = '2020-01-01';
      //   this.filter.endUseDate = '2030-01-01';
      // }
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };
      return params;
    },
    // 获取列表
    async getList() {
      const params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await getExplosiveProtectionDyAsyncList(params);
      this.listLoading = false;
      this.total = res.data?.total;
      this.datalist = res.data?.list;
      const newBrands = new Set(this.datalist.map(f => f.brand)); // 本次获取的品牌
      this.brandList = Array.from(new Set([...this.brandList, ...newBrands]));
      this.summaryarry = res.data?.summary;
      this.selrows = [];
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    // 排序
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      await this.onSearch();
    },
    // 复选框
    callback(val) {
      this.selrows = [...val];
    },
    // 修改
    async onOneKeyLoss(val, row) {
      if (this.selrows.length <= 0 && (val === 1 || val === 2)) {
        this.$message({ type: 'error', message: '请勾选行数据!' });
        return;
      }
      const ids = (val === 1 || val === 2) ? this.selrows.map(f => f.id) : [row.id];
      const oldEntityStatsList = (val === 1 || val === 2) ? this.selrows.map(f => ({ id: f.id, oldStats: f.stats })) : [{ id: row.id, oldStats: row.stats }];
      const action = val === 1 || val === 4 ? '流失' : '爆款';
      const target = (val === 1 || val === 2) ? '所选' : '当前';
      try {
        await this.$confirm(`确定要一键${action}${target}数据吗?`, '提示', {
          confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
        });
        this.pageLoading = true;
        const res = await updateExplosiveProtectionDyAsync({ oldEntityStatsList, dataType: action });
        this.pageLoading = false;
        if (res?.success) {
          this.$message({ type: 'success', message: '操作成功' });
          this.selrows = [];
          await this.onSearch();
        }
      } catch (err) {
      }
    },
    // 导出
    async onExport() {
      const params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await exportExplosiveProtectionDy(params)
      this.listLoading = false;
      if (!res?.data) {
        this.$message({ type: 'error', message: '没有数据可导出或导出失败!' });
        return;
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', "抖音爆款趋势款_" + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
  }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 121px;
    margin-right: 3px;
  }
}

::v-deep .el-button+.el-button,
.el-checkbox.is-bordered+.el-checkbox.is-bordered {
  margin-left: 3px;
}
</style>
