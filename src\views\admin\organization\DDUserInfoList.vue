<template>
<el-container style="height: 100%; border: 1px solid #eee">
    <el-aside width="300px" style="background-color: rgb(238, 241, 246)">
            <DeptViewLeftTree @selected="leftTreeSelected" />
        </el-aside>
            <el-container style="height:100%;">
                <el-main>
                <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                    @select='selectchange' :isSelection='false' :isSelectColumn='true' :tableData='list'
                    :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
                    <template slot='extentbtn'>

                        <el-button-group style="border:none;">
                            <el-button style="padding: 0;border:none;">
                                <el-select v-model="filter.isContainChildDeptUser" 
                                    style="width: 130px">
                                    <el-option label="包含子部门用户" :value="true" />
                                    <el-option label="不包含子部门用户" :value="false" />
                                </el-select>
                            </el-button>
                            <el-button style="padding: 0;margin: 0;border:none;">
                                <el-select v-model="filter.isDelete" clearable placeholder="在职状态" style="width: 90px">
                                    <el-option label="在职状态" :value="null" />
                                    <el-option label="在职" :value="false" />
                                    <el-option label="离职" :value="true" />
                                </el-select>
                            </el-button>
                            <el-button style="padding: 0;border:none;">
                                <el-select v-model="filter.employeeStatus" clearable placeholder="员工结构" style="width: 90px">
                                    <el-option label="员工结构" :value="null" />
                                    <el-option label="正式" :value="3" />
                                    <el-option label="试用" :value="2" />
                                </el-select>
                            </el-button>
                            <el-button style="padding: 0;border:none;">
                                <el-input v-model.trim="filter.keywords" style="width: 160px" :maxLength="100"
                                    placeholder="关键字查询" @keyup.enter.native="deptOnSearch" clearable>
                                    <el-tooltip slot="suffix" effect="dark" content="钉ID、姓名、手机号、ERP昵称、ERP账号、职位、钉钉号。"
                                        placement="bottom">
                                        <i class="el-input__icon el-icon-question"></i>
                                    </el-tooltip>
                                </el-input>
                            </el-button>
                            <el-button type="primary" @click="deptOnSearch">查询</el-button>
                        </el-button-group>
                    </template>
                </ces-table>
            </el-main>
            <el-footer style="height: 35px;">
                <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
            </el-footer>
              
            </el-container>
      
        <el-dialog title="修改" :visible.sync="dialogVisibleSyj" width="20%" :show-close="false">
            <el-form>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="钉钉号:" prop="">
                            <el-input :clearable="true" v-model="dingCode" maxlength="20"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <my-confirm-button style="margin-left: 10px;" size="small" type="primary" @click="onSubmitupload2">
                    提交 </my-confirm-button>
                <el-button style="margin-left: 10px;" @click="closedialogVisibleSyj">关闭</el-button>
            </span>
        </el-dialog>
    </el-container>

</template>
<script>
import MyConfirmButton from "@/components/my-confirm-button";
import { getPageDeptUser, updateDDuserdingId } from '@/api/operatemanage/base/dingdingShow'
import { formatTime, } from "@/utils";
import { PageDDUserInfo,SaveUserDefaultDeptId } from '@/api/admin/deptuser'

import DeptViewLeftTree from '@/views/admin/organization/DeptViewLeftTree.vue'

import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
const tableCols = [
    { istrue: true, prop: 'avatar', label: '头像', type: "image", width: '70' },
    //{ istrue: true, prop: 'ddUserId', label: 'ID', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'userName', label: '姓名', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'erpUserName', label: 'ERP账号', sortable: 'custom', width: '90' },
    { istrue: true, prop: 'erpNickName', label: 'ERP昵称', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'title', label: '职位', sortable: 'custom', width: '70' },
    //{ istrue: true, prop: 'jstUserNames', label: '聚水潭账号', sortable: 'custom',minwidth:'120' },   
    { istrue:true, prop: 'jstUserNames', sortable: 'custom', label:'聚水潭账号', width:'140',type:'button',  btnList:[       
        {
            label:'',
            htmlformatter:(row)=> row.jstUserNames ?  row.jstUserNames :'设置',
            handle:(that,row)=>that.EditUserExtInfo(row),
            permission: "dingCodeEdit",               
        }          
    ]} ,

    { istrue: true, prop: 'deptFullName', label: '所属组织', sortable: 'custom', minwidth: '120',type:'button',btnList:[
        {
            label:'',
            htmlformatter:(row)=> row.deptFullName ?  row.deptFullName :'设置',
            handle:(that,row)=>that.OnSetDftDept(row),
            permission: "dingCodeEdit",               
        }
    ] },
    { istrue: true, prop: 'isDelete', label: '在职状态', style: 'center', width: '70', sortable: 'custom', formatter: (row) => row.isDeleteText },
    { istrue: true, prop: 'employeeStatus', label: '员工结构', style: 'center', width: '70', sortable: 'custom', formatter: (row) => row.employeeStatusText },
    { istrue:true, prop: 'dingCode', sortable: 'custom', label:'钉钉号', width:'130',type:'button',  btnList:[       
        {
            label:'',
            htmlformatter:(row)=> row.dingCode ?  row.dingCode :'设置',
            handle:(that,row)=>that.EditDingId(row),
            permission: "dingCodeEdit",               
        }          
    ]} ,

    { istrue: true, prop: 'hired_date', label: '入职时间',  width: '100', sortable: 'custom' , formatter: (row) => formatTime(row.hired_date, "YYYY-MM-DD") },
   
];
const tableHandles = []
export default {
    name: 'dingdingDeptUser',
    components: { DeptViewLeftTree, container, cesTable, MyConfirmButton },
    data() {
        return {
            dingCode: null,
            ddUserId: 0,
            dialogVisibleSyj: false,
            that: this,
            isLoad: false,
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: " createtime ", IsAsc: false },
            total: 0,
            sels: [],
            selids: [],
            listLoading: false,
            pageLoading: false,
            filter: {
                deptCode: "",
                isContainChildDeptUser: true,
                isDelete: false,
                employeeStatus: null,
                keywords: '',
                corpId:'',
            }
        }
    },
    mounted() {

    },
    beforeUpdate() { },
    methods: {
        leftTreeSelected(row) {      
            console.log(row);   
            this.filter.deptCode=row && row.full_code? row.full_code:'';
            this.filter.corpId= row && row.corpId? row.corpId:'';
            this.deptOnSearch();
          
        },
        EditUserExtInfo(row){
           
            let self=this;      
            this.$showDialogform({
                path: `@/views/admin/organization/UserExtInfoForm.vue`,
                title: '用户其他信息管理',
                autoTitle:false,
                args: {dduserId:row.ddUserId,userName:row.userName,mode:2},
                height: 300,
                width: '600px',
                callOk: self.deptOnSearch
            })
        },
        OnSetDftDept(row){
            
            let self=this;      
            this.$showDialogform({
                path: `@/views/admin/organization/UserDefaultDeptForm.vue`,
                title: '用户默认部门管理',
                autoTitle:false,
                args: {mode:2,...row},
                height: 300,
                width: '600px',
                callOk: self.deptOnSearch
            })
        },
        //保存编辑的信息
        async onSubmitupload2() {
            var res = await updateDDuserdingId({ ddUserId: this.ddUserId, dingCode: this.dingCode });
            if (res?.success) {
                this.$message({ message: '修改成功', type: "success" });
                this.dialogVisibleSyj = false;
                await this.getlist();
            }
        },
        //编辑钉钉号
        EditDingId(row) {
            this.dingCode = null;
            this.ddUserId = null;
            this.dialogVisibleSyj = true;
            this.dingCode = row.dingCode;
            this.ddUserId = row.ddUserId;
        },
        closedialogVisibleSyj() {
            this.dingCode = null;
            this.ddUserId = null;
            this.dialogVisibleSyj = false;
        },
        deptOnSearch() {
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        async getlist() {
         
            var pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }
            this.listLoading = true
            this.list = [];
            const res = await PageDDUserInfo(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res?.data?.total
            const data = res?.data?.list          
            this.list = data;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.deptOnSearch();
        },
        selsChange: function (sels) {
            this.sels = sels
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
    }
}
</script>
<style scoped lang="scss" >
::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: rgb(233, 233, 248);
    border-radius: 3px;
}

::v-deep .el-table__body-wrapper {
    overflow: auto;
}
</style>
  