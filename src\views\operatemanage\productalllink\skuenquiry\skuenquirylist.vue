<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                <el-button-group style="border:none;">
                  
                    <el-button style="padding: 0;margin: 0;border:none;width:140px;">
                        <el-select v-model="Filter.selfIsFb"  clearable placeholder="报价状态" >
                            <el-option label="待报价" :value="0"></el-option>
                            <el-option label="已报价" :value="1"></el-option>
                            <el-option label="已报价未采纳" :value="2"></el-option>
                            <el-option label="已采纳" :value="3"></el-option>
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none;">
                        <el-input v-model.trim="Filter.goodsCompeteId" clearable placeholder="竞品ID" style="width:120px;" :maxlength="40"/>
                    </el-button>


                    <el-button style="padding: 0;margin: 0;border:none;">
                        <el-input v-model.trim="Filter.keywords" clearable placeholder="关键字查询" style="width:160px;"  :maxlength="40">
                            <el-tooltip slot="suffix" class="item" effect="dark" :content="keywordsTip" placement="bottom">
                                <i  class="el-input__icon el-icon-question"></i>
                            </el-tooltip>           
                        </el-input>
                    </el-button>

                    <el-button type="primary" @click="onSearch" >查询</el-button>
                    <el-button type="primary" @click="()=>{Filter={};}">清空条件</el-button>

                </el-button-group>
            </el-form>
        </template>

         <vxetablebase :id="'skuenquirylist202301041800'"
            :that='that' :loading="listLoading"
            :tableData='tbdatalist' :tableCols='tableCols' 
            @cellClick="cellClickVxe" 
            @sortchange='sortchange'
            >
            <template slot="right">
                <vxe-column title="操作" :field="'col'+(tableCols.length+1)" width="80" fixed="right">
                    <template #default="{ row }">
                        <el-button type="text" @click="onOpenDtl(row)">
                            {{ (row.fbId && row.fbId.length>0)? '详情':'报价'}}                              
                        </el-button>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettbdatalist1" />
        </template>

        <!-- IM即时聊天 -->
        <el-dialog title="" v-if="chatVisible" :visible.sync="chatVisible" width="790px" v-dialogDrag :close-on-click-modal="false">
            <JWChat ref="jwchat" :winBarlist="null" :hotSaleGoodsChooseEnquiryId="hotSaleGoodsChooseEnquiryId"></JWChat>
        </el-dialog>
    </my-container>
</template>
<script>  

    import {
        pageChooseEnquiryAsync, 
    } from '@/api/operatemanage/productalllink/alllink'
    import dayjs from "dayjs";
    import cesTable from "@/components/Table/table.vue";
    import vxetablebase from "@/components/VxeTable/vxetablebase.vue";

    import { formatmoney, formatPercen, getUrlParam, platformlist, formatPlatform, formatTime, setStore, getStore, formatLinkProCode,formatNoLink } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";

   

    import YhImgUpload from "@/components/upload/yh-img-upload.vue";
    import JWChat from '@/components/Chat/ChatInterface/chatComponents';
    import { getHotSaleGoodChatUserInfosAsync } from "@/api/admin/user"

    const tableCols = [
        { istrue: true, prop: 'goodsCompeteImgUrl', label: '竞品图片', width: '80', type: 'images' ,fixed:'left'},        
        { istrue: true, prop: 'goodsCompeteId', label: '竞品ID', width: '160', sortable: 'custom',fixed:'left',     
        type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.goodsCompeteId) },        
        { istrue: true, prop: 'goodsCompeteShortName', label: '产品简称', width: '120', sortable: 'custom',fixed:'left' },

        { istrue: true, prop: 'goodsCompeteName', label: '竞品标题', minwidth: '220', sortable: 'custom' },
        
        { istrue: true, prop: 'isWinner', label: '是否采纳', width: '90',align:'center' },
        { istrue: true, prop: 'winnerTime', label: '采纳时间', width: '150' },
        { istrue: true, prop: 'fbUserCount', label: '报价人数', width: '94', sortable: 'custom',align:'center'},
        { istrue: true, prop: 'lastRewardAmount', label: '赏金', width: '94', sortable: 'custom',align:'center',formatter:(row)=>formatmoney(row.lastRewardAmount) },
        { istrue: true, prop: 'hopeCostPriceRange', label: '期望成本价', width: '110', sortable: 'custom' ,align:'center'},
        { istrue: true, prop: 'estimatedQuantity', label: '单次进货总量', width: '124', sortable: 'custom',align:'center' },

        // { istrue: true, prop: 'applyLastGroupName', label: '询价运营组', width: '120', sortable: 'custom' },
        { istrue: true, prop: 'applyLastUserName', label: '询价人', width: '100', sortable: 'custom',align:'center',type:'html',formatter:(row)=>formatNoLink(row.applyLastUserName) },
        { istrue: true, prop: 'applyLastTime', label: '询价时间', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'allDataState', label: '选品状态', width: '94', sortable: 'custom', formatter: (row) => formatAllDataState(row.allDataState) },
    ];


    function formatAllDataState(val) {
        //0已选品、10数据申请中、20数据已同步、30已计算利润、40已登记采样、50已提交编码、60已申请编码、70已审核编码
        switch (val) {
            case 0:
                return "已选品";
            case 10:
                return "数据申请中";
            case 20:
                return "数据已同步";
            case 30:
                return "已计算利润";
            case 40:
                return "已登记采样";
            case 50:
                return "已提交编码";
            case 60:
                return "已申请编码";
            case 70:
                return "已审核编码";
        }

        return "";

    }
   

    const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
    const endTime = formatTime(new Date(), "YYYY-MM-DD");
    const star = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");

    export default {
        name: "skuenquirylist",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable,vxetablebase,   YhImgUpload,JWChat},
        data() {
            return {
                that: this,
                Filter: {
                    selfIsFb:null
                },
                keywordsTip:'支持搜索的内容：产品简称、竞品标题'     ,
                platformlist: platformlist,
                tbdatalist: [],
                tableCols: tableCols,
                total: 0,
                summaryarry: { count_sum: 10 },
                pager: { OrderBy: "", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                //
                selids: [],
                fileList: [],
                selfInfo: {
                },           
             
                curRow: {},             
               

                //聊天控件属性
                chatVisible: false,
                hotSaleGoodsChooseEnquiryId: 0
            };
        },
        async mounted() {

            const userInfoName = "hotsalegoods_selfuserinfo";
            let selfInfo4Store = getStore(userInfoName);
            if (selfInfo4Store) {
                this.selfInfo = selfInfo4Store;
            }

            this.onSearch();
        },
        methods: {
            formatLinkProCode: formatLinkProCode,
            onOpenDtl(row){
                let self=this;
                this.$showDialogform({
                    path:`@/views/operatemanage/productalllink/skuenquiry/EnquiryFbForm.vue`,
                    title: (row.fbId && row.fbId.length>0)?'报价详情':'报价',
                    autoTitle:false,
                    args:{
                        chooseId:row.id,
                        mode: (row.fbId && row.fbId.length>0)?3:2               
                    },
                    height:500, 
                    width:'80%',
                    callOk:(rlt)=>{
                        self.onRefresh();
                    }
                })     
            },
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderField = column.prop;                   

                    this.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                }
                this.onSearch();
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.gettbdatalist1();
            },
            async gettbdatalist1() {
                if (this.Filter.gDate) {
                    this.Filter.startGDate = this.Filter.gDate[0];
                    this.Filter.endGDate = this.Filter.gDate[1];
                }
                else {
                    this.Filter.startGDate = null;
                    this.Filter.endGDate = null;

                }
                const para = { ...this.Filter };
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...para,
                };

                this.listLoading = true;
                const res = await pageChooseEnquiryAsync(params);

                this.listLoading = false;

                this.total = res.data.total
                this.tbdatalist = res.data.list;


            },

            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
            async cellClickVxe(cellObj){
                let column= cellObj.column;
                let row=cellObj.row;
                if (row && column && column.property == 'applyLastUserName') {
                    let res= await getHotSaleGoodChatUserInfosAsync({hotSaleGoodsChooseEnquiryId:row.id, messageId:null})
                    if (res && res.success) {
                        this.hotSaleGoodsChooseEnquiryId = row.id;
                        this.chatVisible = true;
                    }
                }
            }           
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
<style >
    .el-image__inner {
        max-width: 50px;
        max-height: 50px;
    }
</style>