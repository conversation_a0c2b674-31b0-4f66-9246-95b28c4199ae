<template>
    <container v-loading="pageLoading">
        <!--列表-->
        <vxetablebase :id="'goodsCostPriceChgList202301031318001'" :tableData='list' :tableCols='tableCols'
            :loading='listLoading' :border='true' :that="that" ref="vxetable" @sortchange='sortchange' />

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </container>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { pageWarehousingOrderVideo, getListBuyNo, bindWarehousingBuyNo, saveWarehousingAmont } from "@/api/inventory/warehousingordervide"


const tableCols = [
    { istrue: true, prop: 'warehousNo', label: '任务编号', width: '80', },
    { istrue: true, prop: '', label: '到货确认', width: '100', type: 'images' },
    { istrue: true, prop: 'goodsCode', label: '到货时间', width: '120', },
    { istrue: true, prop: 'createdUserName', label: '上传人', width: '80', },
    { istrue: true, prop: 'createdTime', label: '上传时间', width: '120', sortable: 'custom', },
    { istrue: true, prop: '', label: '上传时长', width: '80', },
    { istrue: true, prop: 'count', label: '到货件数', width: '80', sortable: 'custom', },
    { istrue: true, prop: 'buyNo', label: '采购单号', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'modifiedUserName', label: '绑定人', width: '80', sortable: 'custom', },
    { istrue: true, prop: 'bindBuyNoDate', label: '绑定采购单时间', width: '120', sortable: 'custom', },
    { istrue: true, prop: '', label: '绑定时长', width: '80', sortable: 'custom', },
    { istrue: true, prop: '', label: '质检人', width: '100', sortable: 'custom', },
    { istrue: true, prop: '', label: '质检完成时间', width: '120', sortable: 'custom', },
    { istrue: true, prop: '', label: '质检时长', width: '80', sortable: 'custom', },
    { istrue: true, prop: '', label: '入库完成人', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'warehousingDate', label: '入库完成时间', width: '120', sortable: 'custom', },
    { istrue: true, prop: '', label: '入库时长', width: 'auto', sortable: 'custom', },
];
const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
];


export default {
    name: 'YunHanAdminWarehousingordervidelate',
    components: { container, MyConfirmButton, vxetablebase },
    props: { filter: {} },

    data() {
        return {
            that: this,
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "yearMonthDay", IsAsc: false },
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            pickerOptions: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近半个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
        };
    },

    async mounted() {
        await this.onSearch();
    },

    methods: {
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await pageWarehousingOrderVideo(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.total = res.data.total;
            const data = res.data.list;

            data.map((item) => {
                item.istrue = false;
                item.buyNostr = item.buyNo;
                item.buyNo = item.buyNo ? item.buyNo.split(',') : [];
            })
            this.list = data
        },
        async cellclick(row, column, cell, event) {

        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>

<style lang="scss" scoped></style>