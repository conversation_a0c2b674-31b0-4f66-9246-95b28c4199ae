<template>
    <MyContainer v-loading="pageLoading">
        <template #header>

            <el-button style="padding: 0;margin: 0;border: none;">创建日期:
                <el-date-picker style="width: 280px" v-model="filter.timerange" type="daterange" clearable
                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="创建开始日期"
                    end-placeholder="创建结束日期">
                </el-date-picker>
            </el-button>

            <el-button style="padding: 0;margin: 0;border: none;">日报参考日期:
                <el-date-picker style="width: 280px" v-model="filter.timerange2" type="daterange" clearable
                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="日报参考开始日期"
                    end-placeholder="日报参考结束日期">
                </el-date-picker>
            </el-button>
            <!-- 
            <el-button style="padding: 0;margin: 0;border: none;">观察日期:
                <el-date-picker style="width: 280px" v-model="filter.timerange3" type="daterange" clearable
                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="观察开始日期"
                    end-placeholder="观察结束日期">
                </el-date-picker>
            </el-button> -->

            <el-button style="padding: 0;margin: 0;border: none;">
                <el-select v-model="filter.status" placeholder="状态" style="width:120px;" clearable>
                    <el-option label="观察中" :value=0 />
                    <el-option label="已完结" :value=1 />
                </el-select>
            </el-button>

            <el-button style="padding: 0;margin: 0;border: none;">
                <el-input v-model="filter.processPlanName" placeholder="方案名称" style="width:200px;" maxlength="20"
                    clearable />
            </el-button>

            <el-button style="padding: 0;margin: 0;border: 0;">
                <inputYunhan ref="mainProductCode" :inputt.sync="filter.proCode" v-model="filter.proCode"
                    class="publicCss" style="width:200px;" placeholder="宝贝ID" :clearable="true" :clearabletext="true"
                    :maxRows="300" :maxlength="6000" @callback="mainProCodeBack" title="宝贝ID">
                </inputYunhan>
            </el-button>

            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="onAutoPlanSet">自动生成方案设置</el-button>

        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :border="true"
            @sortchange='sortchange' @select='selectchange' :tableData='tableData' :tableCols='tableCols'
            :showsummary='true' :summaryarry='summaryarry' :isSelection="false" :isSelectColumn="false"
            style="width: 100%;  margin: 0" v-loading="listLoading" :height="'100%'">
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import datepicker from '@/views/customerservice/datepicker';
import { pickerOptions } from '@/utils/tools';
import dayjs from 'dayjs';
import { formatTime } from "@/utils/tools";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { GetProcodeProcessPlanPageList, DeleteProcodeProcessPlan } from '@/api/bookkeeper/procodeprocessplan'
import inputYunhan from "@/components/Comm/inputYunhan";
const tableCols = [
    { sortable: 'custom', width: '150', align: 'center', prop: 'createdTime', label: '创建时间', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'createdUserName', label: '创建人', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'status', label: '状态', formatter: (row) => row.status == 0 ? "观察中" : row.status == 1 ? "已完结" : "" },
    { sortable: 'custom', width: '120', align: 'center', prop: 'curDate', label: '日报参考日期', formatter: (row) => formatTime(row.curDate, 'YYYY-MM-DD') },
    { sortable: 'custom', width: '120', align: 'center', prop: 'startDate', label: '观察开始日期', formatter: (row) => formatTime(row.startDate, 'YYYY-MM-DD') },
    { sortable: 'custom', width: '120', align: 'center', prop: 'endDate', label: '观察结束日期', formatter: (row) => formatTime(row.endDate, 'YYYY-MM-DD') },
    { sortable: 'custom', align: 'center', prop: 'processPlanName', label: '方案名称', },
    {
        istrue: true, type: 'button', label: '操作', width: '120', align: 'center',
        btnList: [
            { label: "详情", handle: (that, row) => that.onXiangQing(row) },
            { label: "删除", handle: (that, row) => that.onDelete(row) },
        ]
    },
];
export default {
    name: "procodeprocessplanlist",
    components: {
        MyContainer, datepicker, vxetablebase, inputYunhan
    },
    data() {
        return {
            timeRanges: [],
            that: this,
            pickerOptions,
            filter: {
                timerange: [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
                createdStartDate: null,
                createdEndDate: null,
                timerange2: [],
                curStartDate: null,
                curEndDate: null,
                timerange3: [],
                startDate: null,
                endDate: null,
                status: null,
                processPlanName: null,
            },
            pager: { OrderBy: "createdTime", IsAsc: false },
            tableCols,
            tableData: [],
            total: 0,
            listLoading: false,
            pageLoading: false,
            sels: [],
            selids: [],
            summaryarry: {},
        }
    },
    async mounted() {
        await this.onSearch();
    },
    computed: {
    },
    methods: {
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getParam() {
            //创建日期
            if (this.filter.timerange && this.filter.timerange.length == 2) {
                this.filter.createdStartDate = this.filter.timerange[0];
                this.filter.createdEndDate = this.filter.timerange[1];
            }
            else {
                this.filter.createdStartDate = null;
                this.filter.createdEndDate = null;
            }
            //日报参考日期
            if (this.filter.timerange2 && this.filter.timerange2.length == 2) {
                this.filter.curStartDate = this.filter.timerange2[0];
                this.filter.curEndDate = this.filter.timerange2[1];
            }
            else {
                this.filter.curStartDate = null;
                this.filter.curEndDate = null;
            }
            //观察日期
            if (this.filter.timerange3 && this.filter.timerange3.length == 2) {
                this.filter.startDate = this.filter.timerange3[0];
                this.filter.endDate = this.filter.timerange3[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            let pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            return params;
        },
        async getList() {
            let param = this.getParam();
            this.listLoading = true
            const res = await GetProcodeProcessPlanPageList(param)
            this.listLoading = false
            console.log(res);
            if (res?.success) {
                this.tableData = res.data.list;
                this.total = res.data.total;
                this.summaryarry = res.data.summary;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        onXiangQing(row) {
            // this.$showDialogform({
            //     path: `@/views/bookkeeper/procodeprocessplan/procodeprocessplansee.vue`,
            //     title: '产品处理方案详情',
            //     autoTitle: false,
            //     args: {
            //         id: row.id,
            //     },
            //     height: '650px',
            //     width: '90%',
            // })
            this.$emit('toSee', row.id);
        },
        async onDelete(row) {
            this.$confirm('确定要删除吗？', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await DeleteProcodeProcessPlan({ id: row.id });
                if (res?.success == true) {
                    this.$message.success('删除成功')
                    await this.onSearch();
                }
            }).catch(() => {
                //this.$message({ type: 'info', message: '已取消计算' });
            });
        },
        onAutoPlanSet() {
            this.$showDialogform({
                path: `@/views/bookkeeper/procodeprocessplan/procodeprocessautoplanset.vue`,
                title: '自动生成方案设置',
                autoTitle: false,
                args: {

                },
                height: '560px',
                width: '60%',
                callOk: this.afterSet,
            })
        },
        async afterSet() {
            await this.onSearch();
        },
        mainProCodeBack(val) {
            this.filter.proCode = val;
        },

    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}
</style>
