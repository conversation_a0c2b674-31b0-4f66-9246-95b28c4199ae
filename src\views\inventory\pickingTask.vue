<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <!-- <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="发起审批开始日期"
                  end-placeholder="发起审批结束日期" :picker-options="pickerOptions" style="width: 200px;margin-right: 10px;"
                  :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker> -->
                <span style="font-size: 13px;padding-top: 5px;">审批发起月份:</span>
                <el-date-picker v-model="ListInfo.applyMonth" :clearable="false" style="width: 140px;margin-right: 5px;"
                    type="month" format="yyyy-MM" value-format="yyyy-MM" placeholder="选择月">
                </el-date-picker>
                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate"
                    :startPlaceholder="'审批通过开始日期'" :endPlaceholder="'审批通过结束日期'" class="publicCss"
                    style="width: 240px;" />
                <el-tooltip class="item" effect="dark" content="审批通过日期" placement="top-start">
                    <i class="el-icon-question" style="margin-right: 10px;padding-top: 5px;"></i>
                </el-tooltip>
                <el-input v-model.trim="ListInfo.styleCode" placeholder="款式编码" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码 末尾加*模糊搜索" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.userName" placeholder="添加人" maxlength="50" clearable
                    class="publicCss" />
                <el-select v-model="ListInfo.deptIds" placeholder="架构" class="publicCss" filterable clearable multiple
                    collapse-tags>
                    <el-option v-for="item in deptList" :key="item.deptId" :label="item.fullName" :value="item.deptId">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.titles" placeholder="岗位" class="publicCss" filterable clearable multiple
                    collapse-tags>
                    <el-option v-for="item in postList" :key="item.label" :label="item.label" :value="item.label">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.region" placeholder="区域" class="publicCss" filterable clearable>
                    <el-option label="义乌" value="义乌" />
                    <el-option label="南昌" value="南昌" />
                    <el-option label="武汉" value="武汉" />
                    <el-option label="深圳" value="深圳" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <div style="margin: 0 5px;">
                    <el-dropdown @command="commandClick">
                        <el-button type="primary">
                            导出<i class="el-icon-arrow-down el-icon--right"></i>
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="导出总表" :disabled="isExport">导出总表</el-dropdown-item>
                            <el-dropdown-item command="导出明细" :disabled="isExport1">导出明细</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :id="'allIdPicking20240807'" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' :tree-config="{}" @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
            :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="120">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="voidOperate(row.applyId)"
                                v-if="row.goodsCode
                                    && checkPermission('Api:Inventory:GoodsCostChg:SetGoodsCostApplyReject') && row.pickStatus == 1">失败</el-button>
                            <el-button type="text" @click="onInvalidMethod(row.applyId)"
                                v-if="row.goodsCode && row.pickStatus == 1 && checkPermission('Api:Inventory:GoodsCostChg:SetGoodsCostApplyInvalid')">无效</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools';
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { getPickingTaskPage, setGoodsCostApplyReject, getPurchaseDeptList, setGoodsCostTaskInvalid, exportGoodsCostPickingTask } from '@/api/inventory/goodscostpricechg'
// 采购助理、采购专员、预备采购组长、采购组长、采购主管
const postList = [
    {
        value: '1',
        label: '采购助理'
    },
    {
        value: '6',
        label: '正式采购助理'
    },
    {
        value: '2',
        label: '采购专员'
    },
    {
        value: '3',
        label: '预备采购组长'
    },
    {
        value: '4',
        label: '采购组长'
    },
    {
        value: '5',
        label: '采购主管'
    }
]
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'userName', label: '添加人', treeNode: true, },
    { width: 'auto', align: 'center', prop: 'region', label: '区域', },
    { width: 'auto', align: 'center', prop: 'deptName', label: '架构', },
    { width: 'auto', align: 'center', prop: 'jobs', label: '岗位', },
    { width: 'auto', align: 'center', prop: 'styleCode', label: '款式编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'styleCount', label: '款式编码数量', },
    { width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', },
    { width: 'auto', align: 'center', prop: 'prevPrice', label: '聚水潭成本', },
    { width: 'auto', align: 'center', prop: 'price', label: '新成本', },
    { width: 'auto', align: 'center', prop: 'goodsCount', label: '商品编码数量', },
    { width: 'auto', align: 'center', prop: 'applyTime', label: '发起审批日期', },
    { width: 'auto', align: 'center', prop: 'cptTime', label: '审批通过日期', },
    {
        width: 'auto', align: 'center', prop: 'pickStatus', label: '状态', formatter: (row) => {
            if (row.goodsCode) {
                return row.pickStatus == 1 ? '正常' : row.pickStatus == -1 ? '失败' : row.pickStatus == -2 ? '无效' : ''
            }
        }
    },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            postList,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                //当前月一号
                startDate: '',
                endDate: '',
                applyStartTime: null,
                applyEndTime: null,
                applyMonth: dayjs().format('YYYY-MM'),
                deptIds: [],
                titles: [],
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            isExport1: false,
            deptList: []
        }
    },
    async mounted() {
        this.getDptList()
        await this.getList()
    },
    methods: {
        async commandClick(e) {
            const exportType = e == '导出总表' ? 0 : e == '导出明细' ? 1 : ''
            if (e == '导出总表') {
                this.isExport = true
            } else if (e == '导出明细') {
                this.isExport1 = true
            }
            const deptIds = this.ListInfo.deptIds?.length ? this.ListInfo.deptIds.join(',') : '';
            const titles = this.ListInfo.titles?.length ? this.ListInfo.titles.join(',') : '';
            await exportGoodsCostPickingTask({ ...this.ListInfo, exportType, deptIds, titles }).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', `摘品奖励${e}` + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    if (e == '导出总表') {
                        this.isExport = false
                    } else if (e == '导出明细') {
                        this.isExport1 = false
                    }
                }
            }).catch(() => {
                if (e == '导出总表') {
                    this.isExport = false
                } else if (e == '导出明细') {
                    this.isExport1 = false
                }
            })
        },
        async changeTime(e) {
            this.ListInfo.applyStartTime = e ? e[0] : null
            this.ListInfo.applyEndTime = e ? e[1] : null
        },
        async onInvalidMethod(applyId) {
            this.$confirm('此操作将无效该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await setGoodsCostTaskInvalid({ applyId });
                if (success) {
                    this.getList();
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
        },
        async getDptList() {
            const { data } = await getPurchaseDeptList()
            this.deptList = data
        },
        async voidOperate(applyId) {
            this.$confirm('此操作将失败该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await setGoodsCostApplyReject({ applyId });
                if (success) {
                    this.getList();
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const deptIds = this.ListInfo.deptIds?.length ? this.ListInfo.deptIds.join(',') : '';
                const titles = this.ListInfo.titles?.length ? this.ListInfo.titles.join(',') : '';
                const params = { ...this.ListInfo, deptIds, titles };
                const { data, success } = await getPickingTaskPage(params)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 170px;
        margin-right: 5px;
    }
}

::v-deep .el-select__tags-text {
    max-width: 50px;
}
</style>
