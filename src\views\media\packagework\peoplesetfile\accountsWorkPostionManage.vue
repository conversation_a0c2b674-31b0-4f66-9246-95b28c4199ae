<template>
    <my-container>
        <el-row>
            <el-col :span="24" style="padding:5px 5px 5px 0;margin-top:5px;" v-show="!tichengshow">
                <div style="margin-bottom:5px;">
                    <el-button type="primary" @click="onAdd(0, '新增工作岗位')">新增工作岗位</el-button>
                    <el-button type="primary" @click="getDataSetList(0)">刷新</el-button>
                    <el-button type="primary" @click="saveDataOrderList(0)">保存排序</el-button>
                </div>
                <el-table :data="scenesjs" border class="draggable-table" highlight-current-row row-key="setId"
                    v-loading="listLoading11" default-expand-all>
                    <!--      <el-table-column type ="index" width="20"/> -->
                    <el-table-column prop="sceneCode" label="工作岗位" />
                    <el-table-column label="操作" width="200">
                        <template #default="{ $index, row }">
                            <el-button type="text" @click="onEdit($index, row, 0, '编辑工作岗位')">编辑</el-button>
                            <el-button type="text" :loading="row._loading" @click="onDelete($index, row)"> 删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-col>


            <!-- <el-col :span="24" style="padding:5px 5px 5px 0;margin-top:5px;" v-show="tichengshow">
                <div style="margin-bottom:5px;">
                    <el-button type="primary" @click="onAdd(10, '新增提成岗位')">新增提成岗位</el-button>
                    <el-button type="primary" @click="getDataSetList(10)">刷新</el-button>
                    <el-button type="primary" @click="saveDataOrderList(10)">保存排序</el-button>
                </div>
                <el-table :data="scenesck" border class="draggable-table" highlight-current-row row-key="setId"
                    v-loading="listLoading10" :height="'690px'" default-expand-all>

                    <el-table-column prop="sceneCode" label="提成岗位" />
                    <el-table-column label="操作" width="200">
                        <template #default="{ $index, row }">
                            <el-button type="text" @click="onEdit($index, row, 10, '编辑提成岗位')">编辑</el-button>
                            <el-button type="text" :loading="row._loading" @click="onDelete($index, row)"> 删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-col> -->
        </el-row>

        <el-dialog :title="formTitle" :visible.sync="addFormVisiblerole" width="60%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" @close="closeAddForm" v-dialogDrag :append-to-body="true">
            <span>
                <el-form :model="addForm" ref="addForm" label-width="120px" :rules="addFormRules">
                    <el-row :hidden="true">
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item label="主键" prop="setId">
                                <el-input v-model="addForm.sceneId" auto-complete="off" :disabled="true" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="名称" prop="sceneCode">
                                <el-input v-model="addForm.sceneCode" maxlength="20" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </span>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="closeAddForm">取 消</el-button>
                    <my-confirm-button type="submit" :validate="addForValidate" :loading="addLoading" @click="onAddSave" />
                </span>
            </template>
        </el-dialog>
    </my-container>
</template>
<script>
import {getPackagesSetData,savePackagesSet, getPackagesSetDataById, deletePackagesSet, saveDataOrderListDataAsync,  } from '@/api/inventory/packagesSetProcessing.js';

// import { getShootingSetDataById, getShootingSetData, saveShootingSet, deleteShootingSet, saveDataOrderListDataAsync } from '@/api/media/shootingset'
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import Sortable from 'sortablejs'
export default {
    name: "Users",
    props: {
        tichengshow: { type: Boolean, default: true },
    },
    components: { MyContainer, MyConfirmButton },
    data() {
        return {
            dataTreeckList: false,
            dataTreejsList: false,
            dataTreeshopList: false,
            formTitle: "新增",
            listLoading10: false,
            listLoading11: false,
            listLoading: false,//树列表加载
            addFormVisiblerole: false,//新增编辑显隐
            editFormLoading: false,//编辑时转圈
            addLoading: false,//新增编辑提交按钮
            scenesck: [],//列表数据集
            scenesjs: [],//列表数据集 
            isEdit: false,//是否编辑模式
            addForm: {
                sceneId: 0,
                sceneCode: null,
                OrderNum: 0,
                sceneName: '',
                setType: 0,
                setId: 0,
            },
            addFormRules: {
                sceneCode: [{ required: true, message: '请输入', trigger: 'blur' }],
            }
        };
    },
    async mounted() {
        this.getDataList();
        // 阻止默认行为
        document.body.ondrop = function (event) {
            event.preventDefault();
            event.stopPropagation();
        };
        this.rowDrop();
        // this.rowDrop2();
    },
    methods: {
        // rowDrop() {
        //     const tbody = document.querySelector('.draggable-table .el-table__body-wrapper tbody')
        //     const _this = this
        //     Sortable.create(tbody, {
        //         onEnd({ newIndex, oldIndex }) {
        //             if (newIndex == oldIndex) return
        //             _this.scenesck.splice(
        //                 newIndex,
        //                 0,
        //                 _this.scenesck.splice(oldIndex, 1)[0]
        //             )
        //             var newArray = _this.scenesck.slice(0)
        //             _this.scenesck = []
        //             _this.$nextTick(function () {
        //                 _this.scenesck = newArray
        //             })
        //         }
        //     })
        // },
        rowDrop() {
            const tbody = document.querySelector('.draggable-table .el-table__body-wrapper tbody')
            const _this = this
            Sortable.create(tbody, {
                onEnd({ newIndex, oldIndex }) {
                    if (newIndex == oldIndex) return
                    _this.scenesjs.splice(
                        newIndex,
                        0,
                        _this.scenesjs.splice(oldIndex, 1)[0]
                    )
                    var newArray = _this.scenesjs.slice(0)
                    _this.scenesjs = []
                    _this.$nextTick(function () {
                        _this.scenesjs = newArray
                    })
                }
            })
        },
        //获取数数据源
        async getDataList() {
            //工作岗位
            await this.getDataSetList();
            //提成岗位
            // await this.getDataSetList(11);

        },
        async getDataSetList(index) {
            this.listLoading = true;
            const res = await getPackagesSetData({ setType: 0 });
            if (!res?.success) {
                return
            }
            this.scenesjs = res?.data;
            // switch (index) {

            //     case 10:
            //         this.scenesck = res?.data?.data;
            //         break;
            //     case 11:
            //         this.scenesjs = res?.data?.data;
            //         break;
            // }
            if(index == 0){this.$message({ type: 'success', message: '刷新成功!' });}
            this.$emit('changework')
            this.listLoading = false;
            this.addLoading = false;
        },
        //排序保存
        async saveDataOrderList(index) {
            this.listLoading = true;
            switch (index) {
                case 10:
                    this.listLoading10 = true;
                    var res = await saveDataOrderListDataAsync(this.scenesck);
                    this.listLoading10 = false;
                    if (res?.success) {
                        this.$message({ type: 'success', message: '保存成功!' });
                        await this.getDataSetList(index);
                    }
                    break;
                case 0:
                    this.listLoading11 = true;
                    var res = await saveDataOrderListDataAsync(this.scenesjs);
                    this.listLoading11 = false;
                    if (res?.success) {
                        this.$message({ type: 'success', message: '保存成功!' });
                        await this.getDataSetList();
                    }
                    break;
            }
            this.listLoading = false;
        },
        //新增一级
        async onAdd(type, title) {
            this.formTitle = title;
            this.addFormVisiblerole = true;
            this.addForm.setType = type;
            this.addForm.setId = 0;
            this.addLoading = false;
        },
        //新增验证
        addForValidate() {
            let isValid = false
            this.$refs.addForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },

        async onAddSave() {
            this.addLoading = true;
            const para = _.cloneDeep(this.addForm);
            var res = await savePackagesSet(para);
            if (!res?.success) {
                this.addLoading = false;
                return;
            }
            this.addFormVisiblerole = false;
            this.$message({type:'success',message:'保存成功！'})
            await this.getDataSetList()
            this.$refs['addForm'].resetFields();

        },
        async onEdit(index, row, type, title) {
            this.formTitle = title;
            this.addFormVisiblerole = true;
            this.isEdit = true;
            this.editFormLoading = true;
            var res = await getPackagesSetDataById({ setId: row.setId });
            if (!res?.success) {
                return;
            }
            var curData = res?.data;
            this.addForm.setId = curData.setId;
            this.addForm.sceneCode = curData.sceneCode;
            this.addForm.setType = type;
            this.addForm.orderNum = curData.orderNum;
            this.editFormLoading = false;
        },
        async onDelete(index, row) {
            this.$confirm('确认删除, 是否继续?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await deletePackagesSet({ setId: row.setId });
                if (res?.success) {
                    this.$message({ type: 'success', message: '删除成功!' });
                    await this.getDataSetList();
                }
            }).catch(() => {
                this.$message({ type: 'info', message: '已取消删除' });
            });
        },
        //关闭新增编辑
        async closeAddForm() {
            this.isEdit = false;
            this.$refs.addForm.resetFields();
            this.addFormVisiblerole = false;
        }
    },
};
</script>
<style lang="scss" scoped>
::v-deep .el-table__body-wrapper {
    height: 650px !important;
}
</style>

