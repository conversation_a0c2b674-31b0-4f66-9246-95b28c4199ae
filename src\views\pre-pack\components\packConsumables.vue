<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="ListInfo.goodsCode" placeholder="包装编码" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.goodsName" placeholder="包装名称" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.specifications" placeholder="规格" maxlength="50" clearable class="publicCss" />
        <number-range class="publicCss" :min.sync="ListInfo.weightMin" :max.sync="ListInfo.weightMax" min-label="最小重量"
          max-label="最大重量" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" :disabled="isExport" @click="exportProps">导出</el-button>
        <el-button type="primary" @click="handleAdd">新增</el-button>
        <el-button type="primary" @click="importProps">导入</el-button>
      </div>
    </template>
    <vxetablebase ref="table" v-loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
      :border="true" :table-data="tableData" :table-cols="tableCols" :is-selection="false" :is-select-column="false"
      style="width: 100%; margin: 0" :height="'100%'" @sortchange="sortchange">
      <template slot="right">
        <vxe-column title="操作" width="70">
          <template #default="{ row, $index }">
            <div style="display: flex">
              <el-button type="text" @click="editProps(row.goodsCode)">编辑</el-button>
              <el-button type="text" @click="deleteProps(row.id)">删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog v-dialogDrag title="新增" :visible.sync="addVisable" width="20%">
      <el-form ref="ruleForm" :model="ruleForm" status-icon :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="包装编码" prop="goodsCode">
          <el-input v-model="ruleForm.goodsCode" placeholder="请输入包装编码" clearable maxlength="30" />
        </el-form-item>
        <el-form-item label="包装名称" prop="goodsName">
          <el-input v-model="ruleForm.goodsName" placeholder="请输入包装名称" clearable maxlength="30" />
        </el-form-item>
        <el-form-item label="规格" prop="specifications">
          <el-input v-model="ruleForm.specifications" placeholder="请输入规格" clearable maxlength="30" />
        </el-form-item>
        <el-form-item label="重量" prop="weight">
          <el-input-number v-model="ruleForm.weight" placeholder="请输入重量" :min="0" :max="9999" :controls="false" />
        </el-form-item>
        <el-form-item>
          <el-button @click="resetForm('ruleForm')">重置</el-button>
          <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
      <div style="display: flex;flex-direction: column;justify-content: center;">
        <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
          :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
          <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
            <el-button size="small" type="primary">点击上传</el-button>
          </el-tooltip>
        </el-upload>
      </div>
      <div class="btnGroup">
        <el-button type="primary" @click="importVisible = false">取消</el-button>
        <el-button type="primary" @click="sumbit">确定</el-button>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from '@/components/my-container'
import numberRange from '@/components/number-range/index.vue'
import vxetablebase from '@/components/VxeTable/yh_vxetableNotFixNum.vue'
import { pickerOptions } from '@/utils/tools'
import { download } from '@/utils/download'
import {
  consumableGetColumns,
  consumablePageGetData,
  consumableExportData,
  consumableGet,
  consumableMerge,
  consumableDelete,
  importData
} from '@/api/vo/prePack'
export default {
  name: 'ScanCodePage',
  components: {
    MyContainer,
    vxetablebase,
    numberRange
  },
  data() {
    return {
      rules: {
        goodsCode: [
          { required: true, message: '请输入包装编码', trigger: 'blur' },
          { max: 30, message: '长度不能超过30个字符', trigger: 'blur' }
        ],
        goodsName: [
          { required: true, message: '请输入包装名称', trigger: 'blur' },
          { max: 30, message: '长度不能超过30个字符', trigger: 'blur' }
        ],
        specifications: [
          { required: true, message: '请输入规格', trigger: 'blur' },
          { max: 30, message: '长度不能超过30个字符', trigger: 'blur' }
        ],
        weight: [
          { required: true, message: '请输入重量', trigger: 'blur' },
          // 最小值为0
          { type: 'number', message: '请输入数字', trigger: 'blur' }
        ]
      },
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        goodsCode: null, // 包装编码
        goodsName: null, // 包装名称
        specifications: null // 规格
      },
      timeRanges: [],
      tableCols: [],
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
      isExport: false,
      ruleForm: {
        goodsCode: '',
        goodsName: '',
        specifications: '',
        weight: null
      },
      addVisable: false,
      importVisible: false,
      importLoading: false,
      fileList: [],
      file: null,
    }
  },
  mounted() {
    this.getCol()
    this.getList()
  },
  methods: {
    async uploadFile(data) {
      this.file = data.file
    },
    async sumbit() {
      if (this.file == null) return this.$message.error('请上传文件')
      this.$message.info('正在导入中,请稍后...')
      const form = new FormData();
      form.append("file", this.file);
      this.importLoading = true
      await importData(form).then(({ success }) => {
        if (success) {
          this.$message.success('导入成功')
          this.importVisible = false
          this.getList()
        }
        this.importLoading = false
      }).catch(err => {
        this.importLoading = false
        this.$message.error('导入失败')
      })
    },
    importProps() {
      this.fileList = []
      this.file = null
      this.importVisible = true
    },
    removeFile(file, fileList) {
      this.file = null
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          if (this.ruleForm.weight <= 0) {
            this.$message.error('重量不能小于等于0')
            return
          }
          try {
            await consumableMerge(this.ruleForm)
            this.$message({
              type: 'success',
              message: '提交成功!'
            })
            this.addVisable = false
            this.getList()
          } catch (error) {
            this.$message.error('提交失败')
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    handleAdd() {
      this.ruleForm = {
        goodsCode: '',
        goodsName: '',
        specifications: '',
        weight: null
      }
      this.addVisable = true
    },
    // 编辑数据
    async editProps(goodsCode) {
      const { data, success } = await consumableGet({ code: goodsCode })
      if (success) {
        this.ruleForm = data
        this.addVisable = true
      }
    },
    // 删除数据
    deleteProps(id) {
      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          try {
            await consumableDelete([id])
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    async getCol() {
      const { data, success } = await consumableGetColumns()
      if (success) {
        this.tableCols = data
      }
    },
    // 导出数据,使用时将下面的方法替换成自己的接口
    async exportProps() {
      this.isExport = true
      await consumableExportData(this.ListInfo).then(download).finally(() => {
        this.isExport = false
      })
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      if (
        this.ListInfo.weightMin !== undefined &&
        this.ListInfo.weightMax !== undefined &&
        this.ListInfo.weightMin > this.ListInfo.weightMax
      ) {
        this.$message.error('最小重量不能大于最大重量')
        this.loading = false
        return
      }
      // 使用时将下面的方法替换成自己的接口
      try {
        const {
          data: { list, total, summary },
          success
        } = await consumablePageGetData(this.ListInfo)
        if (success) {
          this.tableData = list
          this.total = total
          this.summaryarry = summary
        } else {
          this.$message.error('获取列表失败')
        }
      } catch (error) {
        this.$message.error('获取列表失败')
      } finally {
        this.loading = false
      }
    },
    // 每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1
      this.ListInfo.pageSize = val
      this.getList()
    },
    // 当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf('descending') == -1
        this.getList()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}

.btnGroup {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
</style>
