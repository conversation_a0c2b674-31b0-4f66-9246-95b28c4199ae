<template>
    <container v-loading="pageLoading">
        <template #header>
            <div class="flexrow">
                <el-input v-model.trim="filter.styleCode" style="width: 160px" placeholder="系列编码" clearable
                    :maxlength="200" @change="onSearch" />
                <el-input v-model.trim="filter.category" style="width: 160px" placeholder="类目" clearable
                    :maxlength="200" @change="onSearch" />
                <el-select style="width:130px;" filterable v-model="filter.groupId" placeholder="运营组" :clearable="true"
                    :collapse-tags="true" @change="onSearch"
                    v-show="(tableType == 'two' && filter.listType != 0) && tableType == 'two'">
                    <!-- filter.listType != 1|| -->
                    <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key">
                    </el-option>
                </el-select>
                <el-select v-model="filter.platform" filterable placeholder="请选择平台" :clearable="true"
                    v-show="(tableType == 'two' && filter.listType != 0) || tableType == 'one'">
                    <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="whitelist" v-show="tableType == 'two' && filter.listType != 0"
                    >名单</el-button>
                <!-- <el-button type="primary" @click="exportCurrDataEvent4">导出订单</el-button> -->
            </div>
            <div class="flexrow" style="font-size: 12px;line-height: 30px; margin-top: 10px; margin-bottom: 10px;">
                总广告费:
                <el-input-number placeholder="总广告费" :min=-99999999 :max=99999999 :precision="2"
                    v-model="filter.advertCostBegin" style="width: 140px"></el-input-number>
                至
                <el-input-number placeholder="总广告费" :min=-99999999 :max=99999999 :precision="2"
                    v-model="filter.advertCostEnd" style="width: 140px"></el-input-number>
                快递费:
                <el-input-number placeholder="快递费" :min=-99999999 :max=99999999 :precision="2"
                    v-model="filter.deliveryCostBegin" style="width: 140px"></el-input-number>
                至
                <el-input-number placeholder="快递费" :min=-99999999 :max=99999999 :precision="2"
                    v-model="filter.deliveryCostEnd" style="width: 140px"></el-input-number>
                毛三利润:
                <el-input-number placeholder="毛三利润" :min=-99999999 :max=99999999 :precision="2"
                    v-model="filter.profit3Begin" style="width: 140px"></el-input-number>
                至
                <el-input-number placeholder="毛三利润" :min=-99999999 :max=99999999 :precision="2"
                    v-model="filter.profit3End" style="width: 140px"></el-input-number>
                毛三利润率:
                <el-input-number placeholder="毛三利润率" :min=-99999999 :max=99999999 :precision="2"
                    v-model="filter.profit3RateBegin" style="width: 140px"></el-input-number>
                至
                <el-input-number placeholder="毛三利润率" :min=-99999999 :max=99999999 :precision="2"
                    v-model="filter.profit3RateEnd" style="width: 140px"></el-input-number>
                <!-- <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">请选择平台:</el-button> -->
                <el-button type="warning" @click="clearall">重置</el-button>
            </div>
            <el-radio-group v-model="tableType" @change="changeListType">
                <el-radio-button label="one" @click="onSearch">系列编码汇总</el-radio-button>
                <el-radio-button label="two" @click="onSearch">运营申报</el-radio-button>
            </el-radio-group>
            <el-radio-group v-model="filter.listType" @change="changeListType">
                <el-radio-button :label="0">全部</el-radio-button>
                <el-radio-button :label="1">个人</el-radio-button>
            </el-radio-group>
            <!-- <el-button type="primary" @click="xilieshow = true" style="margin-left:10px;">系列编码</el-button>
             <el-button type="primary" @click="shenbaoshow = true">申报编码</el-button> -->
        </template>

        <!-- //系列编码 -->
        <el-dialog title="系列编码" :visible.sync="xilieshow" width="70%" v-dialogDrag>
            <!-- <yhVxetable ref="tabletwowee" v-if="tableshow" :resizable="true" :somerow="'dateStr'" :hasSeq='false' :xgt="-1" :ygt="-1" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
      :tableData='tableData' :tableCols='dialogOneCols' :border="true" :cellClassName="cellClassName"
      :isSelectColumn="false" :loading="listLoading">
      </yhVxetable> -->
            <el-form :model="xilieForm" label-position="left" style="margin-top: 10px;">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="系列编码" prop="platform" label-width="75px">
                            {{ xilieForm.styleCode }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="运营组" prop="groupStr" label-width="75px">
                            {{ xilieForm.groupStr }}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item :label="dialogTableType == 'one' ? '商品ID' : '商品编码'" prop="platform"
                            label-width="75px">
                            {{ dialogTableType == 'one' ? xilieForm.proIdTab.proCode : xilieForm.goodsCodeTab.goodsCode
                            }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="毛三利润率最高" prop="groupName" label-width="125px">
                            {{ dialogTableType == 'one' ? xilieForm.proIdTab.profit3Rate :
                                xilieForm.goodsCodeTab.profit3Rate
                            }}%
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-radio-group v-model="dialogTableType" @change="getstyleCodeidtype">
                <el-radio-button label="one">ID</el-radio-button>
                <el-radio-button label="two">商品编码</el-radio-button>
            </el-radio-group>
            <div v-show="tableshow && dialogTableType == 'one'">
                <yhVxetable :id="'serialLoseMoney_index202408041823_1'" ref="tabletwowee" :showsummary='true' height="400px" :toolbarshow="false" :resizable="true"
                    :somerow="'dateStr'" :hasSeq='false' :xgt="-1" :ygt="-1" :that='that' :isIndex='true'
                    :hasexpand='true' @sortchange='diasortchangeone' :summaryarry="xitableDataSummaryid"
                    :tableData='xitableDataid' :tableCols='dialogTwoCols' :border="true" :cellClassName="cellClassName"
                    :isSelectColumn="false" :loading="listLoading">
                    <template slot="right" v-if="tableType == 'one'">
                      <vxe-column title="操作" width="120" fixed="right">
                          <template #default="{ row, $index }">
                              <div style="display: flex;justify-content: center;align-items: center;">
                                  <el-button type="text" @click="onUnmountMethod(row)">{{ row.proStatus != 3 ? '下架' : '上架' }}</el-button>
                              </div>
                          </template>
                      </vxe-column>
                  </template>
                </yhVxetable>
                <my-pagination ref="refpagerone" :total="totalone" :checked-count="selsone.length"
                    @get-page="getstyleCodeidtype" />
            </div>
            <div v-show="tableshow && dialogTableType == 'two'">
                <yhVxetable :id="'serialLoseMoney_index202408041823_2'" ref="tabletwowee" :showsummary='true' height="400px" :toolbarshow="false" :resizable="true"
                    :somerow="'dateStr'" :hasSeq='false' :xgt="-1" :ygt="-1" :that='that' :isIndex='true'
                    :hasexpand='true' @sortchange='diasortchangetwo' :summaryarry="xitableDataSummary"
                    :tableData='xitableData' :tableCols='dialogThrCols' :border="true" :cellClassName="cellClassName"
                    :isSelectColumn="false" :loading="listLoading">
                </yhVxetable>
                <my-pagination ref="refpagertwo" :total="totaltwo" :checked-count="selstwo.length"
                    @get-page="getstyleCodeidtype" />
            </div>
        </el-dialog>

        <!-- //申报编码 -->
        <el-dialog :title="`系列编码: ${shenbaoForm.styleCode}`" :visible.sync="shenbaoshow" width="75%" v-dialogDrag>
            <!-- <div class="top">
                <el-select v-model="dailyValue" placeholder="日常进货选择" class="item" clearable
                    @change="changeValue($event, 'daily')">
                    <el-option v-for="item in daily" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select v-model="daysValue" placeholder="天数" class="item" clearable
                    @change="changeValue($event, 'days')">
                    <el-option v-for="item in days" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </div> -->
            <yhVxetable :id="'serialLoseMoney_index202408041823_3'" ref="tabletwowee" height="200px" v-if="tableshow" :toolbarshow="false" :resizable="true"
                :somerow="'dateStr'" :hasSeq='false' :xgt="-1" :ygt="-1" :that='that' :isIndex='true' :hasexpand='true'
                :tableData='shenbaoForm.stockApplyDtl' :tableCols='dialogfourCols' :border="true"
                :cellClassName="cellClassName" :isSelectColumn="false" :loading="listLoading">
                <template slot="right">
                    <vxe-colgroup title="进货量" field="applyQty">
                        <vxe-column field="applyQty">
                            <template #header="{ column }">
                                <el-select v-model="dailyValue" placeholder="日常进货选择"
                                    style="width: 150px;margin-right: 10px;" clearable
                                    @change="changeValue($event, 'daily')">
                                    <el-option v-for="item in daily" :key="item.value" :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                                <el-select v-model="daysValue" placeholder="天数" style="width: 150px;" clearable
                                    @change="changeValue($event, 'days')">
                                    <el-option v-for="item in days" :key="item.value" :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                            </template>
                            <template #default="{ row }">
                                <el-input-number placeholder="进货量" :min=0 :max=9999999 :precision="0"
                                    v-model="row.applyQty" :controls="false" style="width: 110px"></el-input-number>
                            </template>
                        </vxe-column>
                    </vxe-colgroup>
                </template>
            </yhVxetable>

            <yhVxetable :id="'serialLoseMoney_index202408041823_4'" ref="tabletwowee" height="200px" v-if="tableshow" :toolbarshow="false" :resizable="true"
                :somerow="'dateStr'" :hasSeq='false' :xgt="-1" :ygt="-1" :that='that' :isIndex='true' :hasexpand='true'
                :tableData='shenbaoForm.proList' :tableCols='dialogfiveCols' :border="true" :loading="productLoading"
                :cellClassName="cellClassName" :isSelectColumn="false">
            </yhVxetable>
            <!-- <div style="display: flex; flex-direction: row; width: 400px;"> -->
            <el-form ref="shenbaoForm" :model="shenbaoForm" label-position="left" style="margin-top: 10px;">
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="平台" prop="platform" label-width="55px"
                            :rules="[{ required: true, message: '请输入', trigger: 'blur' }]">
                            <el-select v-model="shenbaoForm.platform" filterable placeholder="请选择平台">
                                <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="运营组" prop="groupId" label-width="65px"
                            :rules="[{ required: true, message: '请输入', trigger: 'blur' }]">
                            <el-select style="width:180px;" disabled filterable v-model="shenbaoForm.groupId"
                                placeholder="运营组" :clearable="true" :collapse-tags="true">
                                <el-option v-for="item in groupList" :key="item.key" :label="item.value"
                                    :value="item.key">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="运营专员" label-width="70px">
                            <el-input v-model="userName" placeholder="运营专员" :maxlength="10" disabled />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="24">
                        <el-form-item label="申报原因" prop="applyReason"
                            :rules="[{ required: true, message: '请输入申报原因', trigger: 'blur' }]">
                            <el-input v-model="shenbaoForm.applyReason" placeholder="请输入申报原因" show-word-limit
                                type="textarea" :maxlength="100" :rows="3"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="预计计划" prop="estimatePlan"
                            :rules="[{ required: true, message: '请输入预计计划', trigger: 'blur' }]">
                            <el-input v-model="shenbaoForm.estimatePlan" placeholder="请输入预计计划" show-word-limit
                                type="textarea" :maxlength="100" :rows="3"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div style="display: flex; flex-direction: row; justify-content: center; width: 100%">
                    <div style="margin-right: 40px;">
                        <el-button @click="shenbaoshow = false">取消</el-button>
                    </div>
                    <el-button type="primary" @click="submitForm()" v-throttle="3000">确定提交</el-button>
                </div>
            </el-form>
            <!-- </div> -->

        </el-dialog>

        <el-dialog title="趋势图" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <!-- <span >
                 <el-form class="ad-form-query" :model="filter" @submit.native.prevent label-width="100px">
                     日期：<el-date-picker style="width: 260px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false"></el-date-picker>
                     <el-button type="primary" @click="showEchartByRefresh">刷新</el-button>
                 </el-form>
         </span> -->
            <span>
                <buschar ref="buschar" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <!-- //主列表 -->
        <div id="YunHanAdminGoods20231018" v-show="tableType == 'one'" style="width: 100%; height: 97%">
            <yhVxetable :id="'serialLoseMoney_index202408041823_5'" ref="tableone" :showsummary='true' :resizable="true" :somerow="'dateStr'" :hasSeq='true'
                :xgt="-1" :ygt="-1" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarryone" :tableData='tableData' :tableCols='tableCols' :border="true"
                :cellClassName="cellClassName" :isSelectColumn="false" :loading="listLoading">
            </yhVxetable>
        </div>

        <div v-show="tableType == 'two'" style="width: 100%; height: 97%">
            <yhVxetable :id="'serialLoseMoney_index202408041823_6'" ref="tabletwo" :showsummary='true' :resizable="true" :somerow="'dateStr'" :hasSeq='true'
                :xgt="-1" :ygt="-1" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarryone" :tableData='tableData' :tableCols='tableColstwo' :border="true"
                :cellClassName="cellClassName" :isSelectColumn="false" :loading="listLoading">
                <template slot="right">
                    <vxe-column title="操作" width="200" fixed="right" align="center">
                        <template #default="{ row }">
                            <div style="display: flex; justify-content: center; width: 100%;">
                              <el-button class="addsc" @click="addWhitelist(row,'black')" type="text"
                                    v-if="checkPermission('Api:OperateManage:ContinuLosses:SetStyleCodeInBlackList') && tableType == 'two'">添加黑名单</el-button>
                                <el-button class="addsc" @click="addWhitelist(row,'White')" type="text"
                                v-if="checkPermission('api:OperateManage:ContinuLosses:SetStyleCodeInWhitelist') && tableType == 'two' && filter.listType == 1">添加白名单</el-button>
                                <el-button class="addsc" @click="onWhiteListMethod(row)" type="text"
                                    v-if="checkPermission('api:OperateManage:ContinuLosses:SetStyleCodeInWhitelist') && tableType == 'two' && filter.listType == 0">添加白名单</el-button>
                                <el-button class="addsc" @click="getsubmitForm(row)" type="text"
                                    v-if="row.applyType == 0">运营申报</el-button>
                                <!--  <el-button class="addsc" @click="revokeOperation(row)" v-if="row.applyType == 1"
                                    type="text">撤销申报</el-button>
                                <el-button class="addsc" @click="getsubmitForm(row)" v-if="row.applyType == 2"
                                    type="text">再次申报</el-button> -->
                            </div>
                        </template>
                    </vxe-column>
                </template>
            </yhVxetable>
        </div>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

        <el-dialog title="状态" :visible.sync="statusPopup" width="50%" center v-dialogDrag>
            <div style="height: 400px;padding-bottom: 20px;">
                <yhVxetable :id="'serialLoseMoney_index202408041823_7'" ref="tablestatus" :showsummary='true' :resizable="true" :hasSeq='true' :xgt="-1" :ygt="-1"
                    :that='that' :isIndex='true' :hasexpand='true' :tableData='statusTableData' :toolbarshow="false"
                    @sortchange='statusSortchange' :tableCols='statustableCols' :border="true" :isSelectColumn="false">
                    <template slot="right">
                        <vxe-column title="操作" width="130" align="center">
                            <template #default="{ row }">
                                <div style="display: flex; justify-content: center; width: 100%;">
                                    <el-button class="addsc" type="text" v-if="row.applyStatus == 1"
                                        @click="revokeOperation(row.applyIdStr)">撤销申报</el-button>
                                </div>
                            </template>
                        </vxe-column>
                    </template>
                </yhVxetable>
                <my-pagination ref="pager" :total="statusTotal" @page-change="statusPagechange"
                    @size-change="statusSizechange" />
            </div>
        </el-dialog>

        <el-dialog title="名单" :visible.sync="whitelistPopupWindow" width="40%" center v-dialogDrag
            :before-close="close">
            <div>
              <el-tabs v-model="activeRollCall" @tab-click="onRollCallClick">
                <el-tab-pane v-if="checkPermission('api:OperateManage:ContinuLosses:GetWhitelist')" label="白名单" name="Whitelist"></el-tab-pane>
                <el-tab-pane v-if="checkPermission('Api:OperateManage:ContinuLosses:GetBlackList')" label="黑名单" name="blacklist"></el-tab-pane>
              </el-tabs>
            </div>
            <div style="margin: 10px 0 15px 0;">
                <el-input v-model="whiteInfo.styleCode" placeholder="请输入系列编码" style="width: 200px;margin-right: 10px;"
                    maxlength="40" clearable />
                <el-button class="addsc" type="primary" @click="whitelist">搜索</el-button>
            </div>
            <div style="height: 400px;padding-bottom: 20px;">
                <yhVxetable :id="'serialLoseMoney_index202408041823_8'" :toolbarshow="false" ref="tablestatus1" :showsummary='true' :resizable="true" :hasSeq='true'
                    :xgt="-1" :ygt="-1" :that='that' :isIndex='true' :hasexpand='true' :tableData='whitetableData'
                    @sortchange='whiteSortchange' :tableCols='whiteListtableCols' :border="true" :loading="whiteListlistLoading"
                    :isSelectColumn="false">
                    <template slot="right">
                        <vxe-column title="操作" width="130" align="center">
                            <template #default="{ row }">
                                <div style="display: flex; justify-content: center; width: 100%;">
                                    <el-button class="addsc" type="text" @click="delWhiteList(row)"
                                    v-if="checkPermission('api:OperateManage:ContinuLosses:RemoveWhitelistStyleCode') && activeRollCall == 'Whitelist'">移除</el-button>
                                    <el-button class="addsc" type="text" @click="delRemoveList(row.styleCode)"
                                    v-if="checkPermission('Api:OperateManage:ContinuLosses:RemoveBlacklistStyleCode') && activeRollCall == 'blacklist'">移除</el-button>
                                </div>
                            </template>
                        </vxe-column>
                    </template>
                </yhVxetable>
                <my-pagination ref="pager" :total="whitetableTotal" @page-change="whitePagechange"
                    @size-change="whiteSizechange" />
            </div>
        </el-dialog>

        <el-dialog :visible.sync="goodsCodeDetailsShow" width="40%" center v-dialogDrag :before-close="close">
            <goodsCodeDetails :queryInfo="queryInfo" :goodsDetailsInfo="goodsDetailsInfo" v-if="goodsCodeDetailsShow" />
        </el-dialog>

        <el-dialog title="添加白名单运营组" :visible.sync="whitelistOperationGroup" width="20%" v-dialogDrag>
          <div style="height: 100px;width: 100%;display: flex;align-items: center;">
            <span style="color: #F56C6C; margin: 0 2px 0 0">*</span>
            <span>运营组：</span>
            <el-select v-model="ListInfo.groupId" multiple collapse-tags style="margin-left: 10px;width: 180px;" placeholder="请选择运营组">
              <el-option v-for="item in ListInfo.whiteGroupList" :key="item.value":label="item.label":value="item.value" />
            </el-select>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button @click="whitelistOperationGroup = false">取 消</el-button>
            <el-button type="primary" @click="onWhitelistSaveMethod">确 定</el-button>
          </span>
        </el-dialog>
    </container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import container from '@/components/my-container';
import yhVxetable from '@/components/VxeTable/yh_vxetable.vue';
import buschar from './buschar.vue';
import exportExecl from "@/utils/exportExecl.js"
import { getGroupKeyValue } from '@/api/operatemanage/base/product';
import middlevue from "@/store/middle.js"
import goodsCodeDetails from './goodsCodeDetails.vue';
import {
    getStyleCodeContinuLossesList,
    submitOperationApply,
    revokeOperationApply,
    getContinuLossesGoodsCodeDetailList,
    getContinuLossesGoodsCodeDetailByStyleCodeAndGroup,
    getContinuLossesListAnalysis,
    getContinuLossesDtlAnalysis,
    getLastMonthProfitAnalysis,
    removeBlacklistStyleCode,
    getBlackList,
    setStyleCodeInBlackList,
    getLossesDayReportGroupList,
    batchSaveWhitelist,
    getReportDataForProId,
    setStyleCodeInWhitelist,
    getWhitelist,
    getContinuLossesInStockApplyList,
    removeWhitelistStyleCode,
    getDirectorAndDirectorGroup
} from '@/api/operatemanage/continuLosses' //持续亏损
import { getCurrentUser } from '@/api/inventory/packagesprocess'
import { formatLinkProCode, platformlist, formatPlatform } from '@/utils/tools'
import { getOrderFoodMenuList, exportOrderMenuManageAsync, exportOrderMenuGroupAsync, exportOrderMenuAsync, getOrderFoodMenuProvier, getOrderFoodMenuStatisList } from '@/api/profit/orderfood';
import { setTimeout } from "timers";
import { platform } from "os";
const status = [
    {
        label: '提交钉钉失败',
        value: -2
    },
    {
        label: '撤销',
        value: -1
    },
    {
        label: '发起钉钉申报',
        value: 0
    },
    {
        label: '审批中',
        value: 1
    },
    {
        label: '采购中',
        value: 2
    },
    {
        label: '已采购',
        value: 3
    }
]
const daily = [
    {
        label: '日常进货量1日销量',
        value: 1,
        prop: 'day1Sales'
    },
    {
        label: '日常进货量3日销量',
        value: 3,
        prop: 'day3Sales'
    },
    {
        label: '日常进货量7日销量',
        value: 7,
        prop: 'day7Sales'
    },
    {
        label: '日常进货量15日销量',
        value: 15,
        prop: 'day30Sales'
    },
    {
        label: '日常进货量30日销量',
        value: 30,
        prop: 'day30Sales'
    }
]
const days = [
    {
        label: '1天',
        value: 1
    },
    {
        label: '3天',
        value: 3
    },
    {
        label: '7天',
        value: 7
    },
    {
        label: '15天',
        value: 15
    },
]
const tableCols = [
    { istrue: true, prop: 'rptDate', label: '计算时间', width: '160' },
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: '160', align: 'center', type: "click", handle: (that, row, column, cell) => that.getstyleCodeid(row) },
    { istrue: true, prop: 'productCategoryName', label: '类目', width: '120' },
    // type:"click",handle:(that,row,column,cell)=>that.canclick(row,column,cell),formatter:(row)=> row.shopName
    { istrue: true, prop: 'day30OrderNum', label: '近30天订单数', width: '160', sortable: 'custom', align: 'center', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 0, 'day30OrderNum') },
    //  { istrue: true, prop: 'day30Sales', label: '近30天销量', width: '120', sortable: 'custom', type:"click",handle:(that,row,column,cell)=>that.getstyleCodeTu(row,0,'day30Sales') },
    { istrue: true, prop: 'day30SalesRevenue', label: '近30天销售额', width: '130', sortable: 'custom', align: 'center', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 0, 'day30SalesRevenue') },
    { istrue: true, prop: 'day3OrderNum', label: '近3天订单', width: '130', sortable: 'custom', align: 'center', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 0, 'day3OrderNum') },
    { istrue: true, prop: 'day3SalesRevenue', label: '近3天销售额', width: '130', sortable: 'custom', align: 'center', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 0, 'day3SalesRevenue') },
    { istrue: true, prop: 'day30Profit3', label: '毛三利润', width: '120', sortable: 'custom', align: 'center', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 0, 'day30Profit3') },
    { istrue: true, prop: 'profit3Rate', label: '毛三利率', width: '120', sortable: 'custom', align: 'center', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 0, 'profit3Rate'), formatter: (row) => row.profit3Rate + '%' },
    { istrue: true, prop: 'totalAdvertCost', label: '总广告费', width: '120', sortable: 'custom', formatter: (row) => row.totalAdvertCost === 0 ? '0' : !row.totalAdvertCost ? " " : row.totalAdvertCost.toFixed(2) },
    { istrue: true, prop: 'totalDeliveryCost', label: '快递费', width: 'auto', sortable: 'custom', formatter: (row) => row.totalDeliveryCost === 0 ? '0' : !row.totalDeliveryCost ? " " : row.totalDeliveryCost.toFixed(2) },
];

const tableColstwo = [
    { istrue: true, prop: 'rptDate', label: '计算时间', width: '160' },
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: '160', align: 'center', type: "click", handle: (that, row, column, cell) => that.getstyleCodeid(row) },
    { istrue: true, prop: 'platformStr', label: '平台', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'groupName', label: '运营组', width: '160', sortable: 'custom', },
    { istrue: true, prop: 'productCategoryName', label: '类目', width: '130', },
    { istrue: true, prop: 'day30OrderNum', label: '近30天订单数', width: '130', sortable: 'custom', align: 'center', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 1, 'day30OrderNum') },
    //  { istrue: true, prop: 'day30Sales', label: '近30天销量', width: '120', sortable: 'custom', type:"click",handle:(that,row,column,cell)=>that.getstyleCodeTu(row,1,'day30Sales') },

    { istrue: true, prop: 'day3OrderNum', label: '近3天订单数', width: '130', sortable: 'custom', align: 'center', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 1, 'day3OrderNum') },
    { istrue: true, prop: 'day30SalesRevenue', label: '近30天销售额', width: '130', sortable: 'custom', align: 'center', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 1, 'day30SalesRevenue') },
    { istrue: true, prop: 'day3SalesRevenue', label: '近3天销售额', width: '130', sortable: 'custom', align: 'center', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 1, 'day3SalesRevenue') },
    { istrue: true, prop: 'day30Profit3', label: '毛三利润', width: '210', sortable: 'custom', align: 'center', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 1, 'day30Profit3') },
    { istrue: true, prop: 'profit3Rate', label: '毛三利率', width: '210', sortable: 'custom', align: 'center', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 1, 'profit3Rate'), formatter: (row) => row.profit3Rate + '%' },
    { istrue: true, prop: 'totalAdvertCost', label: '总广告费', width: '210', sortable: 'custom' },
    { istrue: true, prop: 'totalDeliveryCost', label: '快递费', width: '210', sortable: 'custom' },
    {
        istrue: true, label: '状态', width: '200', type: 'button', btnList: [
            { label: '查看', handle: (that, row,) => that.statusClick(row.styleCode), },
        ]
    },
];

const dialogOneCols = [
    { istrue: true, prop: 'createdUserName', label: '系列编码', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'createdGroupName', label: '清洁剂', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'createdTime', label: '运营组', width: '160', sortable: 'custom', },
    { istrue: true, prop: 'gysName', label: '陈鹏辉', width: '120', sortable: 'custom' },
];

const dialogTwoCols = [
    { istrue: true, prop: 'platform', label: '运营平台', sortable: 'custom', width: '120', formatter: (row) => formatPlatform(row.platform), },
    { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'groupName', label: '运营组', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'operateSpecialUserName', label: '运营专员', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'proCode', label: '商品ID', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'day30OrderNum', label: '近30天订单量', width: '120', sortable: 'custom', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 2, 'day30OrderNum') },
    //  { istrue: true, prop: 'day30Sales', label: '近30天销量', width: '120', sortable: 'custom', type:"click", handle:(that,row,column,cell)=>that.getstyleCodeTu(row,2,'day30Sales') },

    { istrue: true, prop: 'day30SalesRevenue', label: '近30天销售额', width: '120', sortable: 'custom', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 2, 'day30SalesRevenue') },
    { istrue: true, prop: 'day30Profit3', label: '毛三利润', width: '100', sortable: 'custom', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 2, 'day30Profit3') },
    { istrue: true, prop: 'profit3Rate', label: '毛三利率', width: '100', sortable: 'custom', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 2, 'profit3Rate'), formatter: (row) => row.profit3Rate + '%' },
    { istrue: true, prop: 'onTime', label: '上架时间', width: '100', },
    { istrue: true, prop: 'onDays', label: '上架天数', width: '100', },
    { istrue: true, prop: 'lastMonthProfit3', label: '上月毛三', width: '100', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 4, 'lastMonthProfit3') },
    { istrue: true, prop: 'lastMonthProfit3Rate', label: '上月毛三率', width: '100', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 4, 'lastMonthProfit3Rate') },
];

const dialogThrCols = [
    { istrue: true, prop: 'platform', label: '运营平台', sortable: 'custom', width: '160', formatter: (row) => formatPlatform(row.platform), },
    // { istrue: true, prop: 'createdUserName', label: '店铺', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'groupName', label: '运营组', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'operateSpecialUserName', label: '运营专员', width: '160', sortable: 'custom', },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'day30OrderNum', label: '近30天订单量', width: '120', sortable: 'custom', type: "click", handle: (that, row, column, cell) => that.getstyleGoodsTu(row, 4, 'day30OrderNum') },
    //  { istrue: true, prop: 'day30Sales', label: '近30天销量', width: '120', sortable: 'custom', type:"click", handle:(that,row,column,cell)=>that.getstyleGoodsTu(row,4,'day30Sales') },
    { istrue: true, prop: 'day30SalesRevenue', label: '近30天销售额', width: '120', sortable: 'custom', type: "click", handle: (that, row, column, cell) => that.getstyleGoodsTu(row, 4, 'day30SalesRevenue') },
    { istrue: true, prop: 'day30Profit3', label: '毛三利润', width: '200', sortable: 'custom', type: "click", handle: (that, row, column, cell) => that.getstyleGoodsTu(row, 4, 'day30Profit3') },
    { istrue: true, prop: 'profit3Rate', label: '毛三利率', width: 'auto', sortable: 'custom', type: "click", handle: (that, row, column, cell) => that.getstyleGoodsTu(row, 4, 'profit3Rate'), formatter: (row) => row.profit3Rate + '%' },
];

const dialogfourCols = [
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '120' },
    { istrue: true, prop: 'day30OrderNum', label: '近30天订单量', width: '120', type: "click", handle: (that, row, column, cell) => that.getstyleGoodsTu(row, 5, 'day30OrderNum') },
    //  { istrue: true, prop: 'day30Sales', label: '近30天销量', width: '120', type:"click", handle:(that,row,column,cell)=>that.getstyleGoodsTu(row,5,'day30Sales') },
    { istrue: true, prop: 'day30Sales', label: '近30天销量', width: '120', type: "click", handle: (that, row, column, cell) => that.getstyleGoodsTu(row, 5, 'day30Sales') },
    { istrue: true, prop: 'day30SalesRevenue', label: '近30天销售额', width: '120', type: "click", handle: (that, row, column, cell) => that.getstyleGoodsTu(row, 5, 'day30SalesRevenue') },
    {
        istrue: true, summaryEvent: true, label: `销量`, merge: true, prop: 'mergeField',
        cols: [
            { istrue: true, prop: 'day1Sales', label: '昨日销量', width: '90' },
            { istrue: true, prop: 'day3Sales', label: '近3天销量', width: '90' },
            { istrue: true, prop: 'day7Sales', label: '近7天销量', width: '90' },
            { istrue: true, prop: 'day15Sales', label: '近15天销量', width: '90' },
        ]
    },
    { istrue: true, prop: 'day30Profit3', label: '毛三利润', width: '70', type: "click", handle: (that, row, column, cell) => that.getstyleGoodsTu(row, 5, 'day30Profit3') },
    { istrue: true, prop: 'profit3Rate', label: '毛三利润率', width: '70', type: "click", handle: (that, row, column, cell) => that.getstyleGoodsTu(row, 5, 'profit3Rate'), formatter: (row) => row.profit3Rate + '%' },
];

const dialogfiveCols = [
    { istrue: true, prop: 'proCode', label: '产品ID', },
    {
        istrue: true, label: '查看', align: 'center', type: 'button', btnList: [
            { label: '商品编码', handle: (that, row,) => that.showGoodsCodeDetails(row), },
        ]
    },
    { istrue: true, prop: 'day30OrderNum', label: '近30天订单量', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 3, 'day30OrderNum') },
    { istrue: true, prop: 'day30SalesRevenue', label: '近30天销售额', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 3, 'day30SalesRevenue') },
    { istrue: true, prop: 'day30Profit3', label: '近30天毛三利润', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 3, 'day30Profit3') },
    { istrue: true, prop: 'profit3Rate', label: '近30天毛三利率', type: "click", handle: (that, row, column, cell) => that.getstyleCodeTu(row, 3, 'profit3Rate'), formatter: (row) => (row.profit3Rate !== null && row.profit3Rate !== undefined) ? row.profit3Rate + '%' : null },
    { istrue: true, prop: 'onTime', label: '上架时间', width: '100', },
    { istrue: true, prop: 'onDays', label: '上架天数', width: '100', },
    { istrue: true, prop: 'lastMonthProfit3', label: '上月毛三', width: '100', },
    { istrue: true, prop: 'lastMonthProfit3Rate', label: '上月毛三率', width: '100', },
]

const statustableCols = [
    { istrue: true, width: 'auto', align: 'center', prop: 'styleCode', label: '系列编码', },
    { istrue: true, width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { istrue: true, width: 'auto', align: 'center', prop: 'appluUserName', label: '申请人', },
    { istrue: true, width: 'auto', align: 'center', prop: 'applyTime', label: '时间' },
    { istrue: true, width: 'auto', align: 'center', prop: 'applyStatus', label: '状态', formatter: (row) => status.find(item => item.value == row.applyStatus).label ? status.find(item => item.value == row.applyStatus).label : '待申请' },
];

const whiteListtableCols = [
    { istrue: true, width: 'auto', prop: 'styleCode', label: '系列编码', },
    { istrue: true, width: 'auto', prop: 'groupName', label: '运营组',  },
    { istrue: true, width: 'auto', align: 'center', prop: 'createdUserName', label: '操作人', },
    { istrue: true, width: 'auto', align: 'center', prop: 'createdTime', label: '操作时间', },
];

const getActiveRollCall = () => {
  return checkPermission('api:OperateManage:ContinuLosses:GetWhitelist') ? 'Whitelist' : 'blacklist';
};



const startDate = formatTime(dayjs(), "YYYY-MM-DD");
const endDate = startDate;
export default ({
    name: "styleCodeContinuLosses",
    components: { container, yhVxetable, buschar, goodsCodeDetails },
    data() {
        return {
            ListInfo: {
              whiteGroupList: [],
              styleCode: null,
              groupId: [],
            },
            whitelistOperationGroup: false,
            whiteListlistLoading: false,
            activeRollCall: getActiveRollCall,
            daily,
            days,
            dailyValue: null,
            daysValue: null,
            dailyparams: null,
            daysparams: null,
            statusPopup: false,
            whitelistPopupWindow: false,
            whiteListtableCols,
            statustableCols,
            version: '',
            whitetableTotal: 0,
            whitetableData: [],
            statusTableData: [],
            that: this,
            daochushow: false,
            xilieshow: false,
            tableshow: true,
            pickerOptions: {
                disabledDate(time) {
                    const currentDate = new Date();
                    const currentTime = currentDate.getTime();
                    return time.getTime() > currentTime;
                }
            },
            filter: {
                listType: 1,
                timerange: [
                    formatTime(dayjs().subtract(7, "day"), "YYYY-MM-DD"),
                    formatTime(new Date(), "YYYY-MM-DD"),
                ],
            },
            shenbaoForm: {
                stockApplyDtl: [],
                proList: [],
                estimatePlan: '',
                applyReason: '',
                groupId: '',
                platform: ''
            },
            shenbaoshow: false,
            tableType: 'one',
            dialogTableType: 'one',
            platformlist: platformlist,
            tableCols: tableCols,
            tableColstwo: tableColstwo,
            dialogTwoCols: dialogTwoCols,
            dialogOneCols: dialogOneCols,
            dialogThrCols: dialogThrCols,
            dialogfourCols: dialogfourCols,
            dialogfiveCols,
            idorgoods: 1,
            tableHandles: null,
            // tableData: [],
            total: 0,
            totalone: 0,
            selsone: [],
            totaltwo: 0,
            selstwo: [],
            pager: { OrderBy: "rptDate", IsAsc: false },
            pagerone: { OrderBy: "", IsAsc: false }, //id排序
            pagertwo: { OrderBy: "", IsAsc: false }, //非id排序
            listLoading: false,
            pageLoading: false,
            productLoading: false,
            summaryarry: {},
            sels: [],
            allAlign: 'center',
            groupList: [],
            tableData: [
            ],
            xitableData: [],
            tableData1: [
            ],
            mergeCells: [
                { row: 1, col: 1, rowspan: 3, colspan: 3 },
                { row: 5, col: 0, rowspan: 2, colspan: 2 }
            ],
            buscharDialog: {
                visible: false,
            },
            xitableDataid: [],
            getstyleCodeRow: {},
            xilieForm: {
                goodsCodeTab: {
                    goodCode: '',
                    profit3Rate: ''
                },
                proIdTab: {
                    proCode: '',
                    profit3Rate: ''
                }
            },
            xitableDataSummaryid: {},
            xitableDataSummary: {},
            summaryarryone: {},
            whiteInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: true,
                styleCode: null
            },
            whiteTotal: 0,
            statusTotal: 0,
            userName: null,
            queryInfo: {
                platform: null,
                styleCode: null,
                rptDate: null,
                proCode: null,
                groupId: null
            },
            goodsCodeDetailsShow: false,
            goodsDetailsInfo: {
                platformName: null,
                day30Sales: null,//近30天销量
                day30SalesRevenue: null,//近30天销售额
                groupName: null,
                procode: null,
            },
        };
    },
    async mounted() {
        await this.getUser()
        await this.setGroupSelect();
        await this.onSearch();
        await middlevue.$on('toStyleCodeLossPage', e => {
            this.filter.styleCode = e
            this.onSearch();
        });
    },
    //销毁
    beforeDestroy() {
        middlevue.$off('toStyleCodeLossPage');
    },
    methods: {

        onUnmountMethod(row){
          let rowList = [];
          if (row) {
            rowList.push(row);
          }
          this.$showDialogform({
              path: `@/views/base/batchListingDelist.vue`,
              title: '批量上下架',
              autoTitle: false,
              args: {
                checkdata: rowList
              },
              height: '650px',
              width: '1300px',
          })
        },
        async delRemoveList(styleCode) {
            this.$confirm('此操作将该系列编码移除黑名单, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const params = {
                    styleCode: String(styleCode)
                }
                const { success } = await removeBlacklistStyleCode(params)
                if (success) {
                    this.onRollCallClick({ name: 'blacklist' });
                    this.$message({
                        type: 'success',
                        message: '移除成功!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消移除'
                });
            });
        },
        async onRollCallClick(e) {
          this.$nextTick(async () => {
            this.whiteListlistLoading = true;
            let list = [];
            let total = 0;
            let success = false;

            if (e.name === 'Whitelist') {
              ({ data: { list, total }, success } = await getWhitelist(this.whiteInfo));
              this.activeRollCall = 'Whitelist';
              this.whitelistPopupWindow = true;
              this.$refs.tablestatus1.$refs.xTable.showColumn(this.$refs.tablestatus1.$refs.xTable.getColumnByField('groupName'));
            } else if (e.name === 'blacklist') {
              ({ data: { list, total }, success } = await getBlackList(this.whiteInfo));
              this.$refs.tablestatus1.$refs.xTable.hideColumn(this.$refs.tablestatus1.$refs.xTable.getColumnByField('groupName'));
            }

            if (success) {
              this.whitetableData = list;
              this.whitetableTotal = total;
            }

            this.whiteListlistLoading = false;
            this.$forceUpdate();
          });
        },
        async changeListType() {
            if (this.tableType == 'two' && this.filter.listType == 0) {
                this.$nextTick(() => {
                    this.$refs.tabletwo.$refs.xTable.hideColumn(this.$refs.tabletwo.$refs.xTable.getColumnByField('platformStr'))
                    this.$refs.tabletwo.$refs.xTable.hideColumn(this.$refs.tabletwo.$refs.xTable.getColumnByField('groupName'))
                })
            } else {
                this.$nextTick(() => {
                    this.$refs.tabletwo.$refs.xTable.showColumn(this.$refs.tabletwo.$refs.xTable.getColumnByField('platformStr'))
                    this.$refs.tabletwo.$refs.xTable.showColumn(this.$refs.tabletwo.$refs.xTable.getColumnByField('groupName'))
                })
            }
            await this.onSearch()
        },
        changeValue(e, type) {
            if (type == 'daily') {
                //找出对应的天数
                this.dailyparams = e ? this.daily.find(item => item.value == e) : null
            }
            if (this.dailyValue && this.daysValue) {
                this.shenbaoForm.stockApplyDtl.forEach(item => {
                    item.applyQty = item[this.dailyparams.prop] / this.dailyparams.value * this.daysValue
                })
            }
        },
        showGoodsCodeDetails(row) {
            this.goodsDetailsInfo = row
            const platformItem = this.platformlist.find(item => item.value == row.platform);
            if (platformItem) {
                this.goodsDetailsInfo.platformName = platformItem.label;
            }
            this.queryInfo.proCode = row.proCode;
            this.goodsCodeDetailsShow = true;
        },
        async getUser() {
            const { data, success } = await getCurrentUser()
            if (success) {
                this.userName = data.userName
            }
        },
        delWhiteList(row) {
            this.$confirm('此操作将该系列编码移除白名单, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const params = {
                  styleCode: String(row.styleCode),
                  groupId: row.groupId
                }
                const { success } = await removeWhitelistStyleCode(params)
                if (success) {
                    this.onRollCallClick({ name: 'Whitelist' });
                    this.$message({
                        type: 'success',
                        message: '移除成功!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消移除'
                });
            });
        },
        close() {
            this.whiteInfo = {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: true,
                styleCode: null
            }
            this.goodsCodeDetailsShow = false;
            this.whitelistPopupWindow = false;
            this.statusPopup = false;
        },
        //每页数量改变
        statusSizechange(val) {
            this.whiteInfo.currentPage = 1;
            this.whiteInfo.pageSize = val;
            this.statusClick(this.whiteInfo.styleCode)
        },
        //当前页改变
        statusPagechange(val) {
            this.whiteInfo.currentPage = val;
            this.statusClick(this.whiteInfo.styleCode)
        },
        statusSortchange({ order, prop }) {
            if (prop) {
                this.whiteInfo.orderBy = prop
                this.whiteInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.statusClick(this.whiteInfo.styleCode)
            }
        },
        //每页数量改变
        whiteSizechange(val) {
            this.whiteInfo.currentPage = 1;
            this.whiteInfo.pageSize = val;
            this.whitelist()
        },
        //当前页改变
        whitePagechange(val) {
            this.whiteInfo.currentPage = val;
            this.whitelist()
        },
        whiteSortchange({ order, prop }) {
            if (prop) {
                this.whiteInfo.orderBy = prop
                this.whiteInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.whitelist()
            }
        },
        async whitelist() {
            this.whiteListlistLoading = true
            const { data: { list, total }, success } = await getWhitelist(this.whiteInfo)
            if (success) {
                this.whitetableData = list
                this.whitetableTotal = total
            }
            this.activeRollCall = 'Whitelist'
            this.whiteListlistLoading = false
            this.whitelistPopupWindow = true;
        },
        async statusClick(styleCode) {
            this.whiteInfo.styleCode = styleCode
            const { data: { list, total }, success } = await getContinuLossesInStockApplyList(this.whiteInfo)
            if (success) {
                this.statusTableData = list
                this.statusTotal = total
            }
            this.statusPopup = true;
        },
        addWhitelist(row,tableType) {
            if(tableType == 'White' && !row.styleCode && !row.groupName){
              this.$message({
                type: 'warning',
                message: '系列编码和运营组不能为空'
              });
              return
            }else if(tableType == 'Black' && !row.styleCode){
              this.$message({
                type: 'warning',
                message: '系列编码不能为空'
              });
              return
            }
            const params = {
                styleCode: String(row.styleCode),
                groupId: row.groupId
            }
            this.$confirm(`此操作将该系列编码添加至${tableType === 'black' ? '黑名单' : '白名单'}, 是否继续?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                // let success = false
                if(tableType === 'black'){
                 var {success} = await setStyleCodeInBlackList(params)
                }else{
                 var {success} = await setStyleCodeInWhitelist(params)
                }
                if (success) {
                    this.onSearch()
                    this.$message({
                        type: 'success',
                        message: '添加成功!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消添加'
                });
            });

        },
        async onWhitelistSaveMethod(){
          if(this.ListInfo.groupId && this.ListInfo.groupId.length == 0){
            this.$message({
              type: 'warning',
              message: '请选择运营组'
            });
            return
          }
          const {data,success} = await batchSaveWhitelist({styleCode:this.ListInfo.styleCode,groupId:this.ListInfo.groupId})
          if (success) {
              this.whitelistOperationGroup = false
              this.onSearch()
              this.$message({
                  type: 'success',
                  message: '添加成功!'
              });
          }
        },
        async onWhiteListMethod(row){
          const {data,success} = await getLossesDayReportGroupList({styleCode:row.styleCode})
          if(!success) return
          this.ListInfo.styleCode = row.styleCode
          this.ListInfo.whiteGroupList = data.map(item => {
            return {
              value: item.id,
              label: item.userName
            }
          })
          this.whitelistOperationGroup = true
        },
        clearall() {
            this.filter.profit3RateEnd = undefined;
            this.filter.profit3RateBegin = undefined;
            this.filter.profit3End = undefined;
            this.filter.profit3Begin = undefined;
            this.filter.deliveryCostEnd = undefined;
            this.filter.deliveryCostBegin = undefined;
            this.filter.advertCostEnd = undefined;
            this.filter.advertCostBegin = undefined;
        },
        getstyleCodeidtype() {
            console.log(this.dialogTableType, 'this.dialogTableType');
            if (this.dialogTableType == 'one') {
                this.getstyleCodeid(this.getstyleCodeRow)
            } else if (this.dialogTableType == 'two') {
                this.getstyleCode(this.getstyleCodeRow)
            }
        },
        async setGroupSelect() {
            const res = await getGroupKeyValue({});
            this.groupList = res.data;
        },
        cellClassName({ row, column }) {
            return null
        },
        exportCurrDataEvent4() {
            let _this = this;
            exportExecl("YunHanAdminGoods20231018", `汇总订单统计——${_this.filter.gysName ? _this.filter.gysName : ''}` + endDate + '.xlsx');
        },
        async onSearch() {
            await this.getList();
        },
        async exportOrderMenuGroup() {
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange;
                this.filter.endTime = this.filter.timerange;
            }
            const params = { ...this.filter };
            const res = await exportOrderMenuAsync(params);
            if (!res?.data) return;
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '菜单数量合计_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
        async exportOrderMenuManage() {
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange;
                this.filter.endTime = this.filter.timerange;
            }
            const params = { ...this.filter };
            const res = await exportOrderMenuManageAsync(params);
            if (!res?.data) return;
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '点餐订单数据_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        async diasortchangeone(column) {
            if (!column.order)
                this.pagerone = {};
            else
                this.pagerone = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.getstyleCodeidtype();
        },
        async diasortchangetwo(column) {
            if (!column.order)
                this.pagertwo = {};
            else
                this.pagertwo = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.getstyleCodeidtype();
        },
        async getList() { //获取主列表
            var pager = this.$refs.pager.getPager()
            const params = {
                ...pager,
                ...this.pager,
                ... this.filter,
                queryType: this.tableType == 'one' ? 0 : this.tableType == 'two' ? 1 : ''
            }
            this.listLoading = true
            const res = await getStyleCodeContinuLossesList(params)
            this.listLoading = false

            if (!res?.success) { return }
            this.total = res.data.total
            const data = res.data.list
            data.forEach(d => { d._loading = false })
            this.tableData = data;
            this.summaryarryone = res.data.summary;
            this.selids = []
        },
        // async getGroup() {
        //     const { data } = await getDirectorAndDirectorGroup()
        //     console.log(data, 'data')
        // },
        async getsubmitForm(row) { //获取申报数据
            // await this.getGroup()
            const { data } = await getDirectorAndDirectorGroup()
            this.dailyValue = null
            this.daysValue = null
            this.getstyleCodeRow = row;
            const params = {
                groupId: data.groupId,
                platform: row.platform,
                styleCode: row.styleCode
                //rptDate: row.rptDate
            }
            this.queryInfo.styleCode = row.styleCode;
            //this.queryInfo.rptDate = row.rptDate;
            this.queryInfo.platform = row.platform;
            this.queryInfo.groupId = row.groupId;
            this.listLoading = true
            const res = await getContinuLossesGoodsCodeDetailByStyleCodeAndGroup(params)
            this.listLoading = false
            if (!res?.success) return
            this.shenbaoForm.stockApplyDtl = res.data.goodsList
            this.shenbaoForm.proList = res.data.proList
            this.shenbaoForm.styleCode = row.styleCode;
            this.shenbaoForm.platform = row.platform;
            this.shenbaoForm.groupId = data.groupId ? data.groupId.toString() : (row.groupId ? row.groupId.toString() : '');
            this.shenbaoForm.applyReason = row.applyReason;
            this.shenbaoForm.estimatePlan = row.estimatePlan;
            this.shenbaoshow = true;
        },
        async submitForm() { //提交申报
            let isValid = false
            this.$refs.shenbaoForm.validate(valid => {
                isValid = valid
            })
            if (!isValid) {
                return
            }
            const params = {
                ... this.shenbaoForm,
                rptDate: this.getstyleCodeRow.rptDate
            }
            this.listLoading = true
            const res = await submitOperationApply(params)
            this.listLoading = false
            if (!res?.success) { return }
            this.shenbaoshow = false;
            this.onSearch();
        },
        async revokeOperation(applyId) { //撤销申报
            this.$confirm("确认撤销申报, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                this.listLoading = true
                const res = await revokeOperationApply({ applyId })
                this.listLoading = false
                if (!res?.success) { return }
                await this.statusClick(this.whiteInfo.styleCode)
                await this.onSearch();
                this.$message({
                    type: "success",
                    message: "撤销成功!",
                });
            });
        },
        async getstyleCodeheard(row) { //获取系列编码头部
            // const params = {
            //  styleCode: styleCode,
            // }
            let params = this.tableType == 'one' ? {
                styleCode: row.styleCode,
                queryType: 2,
                listType: this.filter.listType,
            } : {
                styleCode: row.styleCode,
                platform: row.platform,
                groupId: row.groupId,
                queryType: 2,
                listType: this.filter.listType
            }
            if (this.tableType == 'two' && this.filter.listType == 0) {
                params = {
                    styleCode: row.styleCode,
                    queryType: 2,
                    listType: this.filter.listType
                }
            }
            this.listLoading = true
            const res = await getReportDataForProId(params)
            this.listLoading = false
            if (!res?.success) { return }
            // this.xilieForm = res.data;
            this.xilieForm = {
                goodsCodeTab: {
                    goodsCode: res.data?.goodsCodeTab?.goodsCode,
                    profit3Rate: res.data?.goodsCodeTab?.profit3Rate,
                },
                proIdTab: {
                    proCode: res.data?.proIdTab?.proCode,
                    profit3Rate: res.data?.proIdTab?.profit3Rate,
                },
                groupStr: res.data?.groupStr
            };
            // this.xilieForm.profit3Rate = row.profit3Rate;
            this.xilieForm.styleCode = row.styleCode;

            this.xilieshow = true;
        },
        async getstyleCodeid(row) { //获取系列编码=id维度
            let _this = this;
            this.xilieshow = true;
            // const params = {
            //  styleCode: styleCode,
            // }
            this.dialogTableType = 'one';
            this.getstyleCodeheard(row); //获取头部信息
            if (row == '') {
                row = this.getstyleCodeRow;
            }
            this.getstyleCodeRow = row;

            _this.$nextTick(async () => {
                let pagerone = _this.$refs.refpagerone.getPager()
                const params = this.tableType == 'one' ? {
                    styleCode: row.styleCode,
                    queryType: 2,
                    listType: this.filter.listType,
                    ...pagerone,  //分页新增
                    ...this.pagerone,
                } : {
                    styleCode: row.styleCode,
                    // platform: row.platform,
                    // groupId: row.groupId,
                    queryType: 2,
                    listType: this.filter.listType,
                    ...pagerone,  //分页新增
                    ...this.pagerone,
                }
                this.listLoading = true
                const res = await getContinuLossesGoodsCodeDetailList(params)
                this.listLoading = false
                if (!res?.success) { return }
                this.xitableDataid = res.data.list;
                this.xitableDataSummaryid = res.data.summary
                this.totalone = res.data.total;
            })
        },
        async getstyleCode(row) { //获取系列编码==非id维度
            // const params = {
            //  styleCode: styleCode,
            // }
            this.xilieshow = true;
            if (row == '') {
                row = this.getstyleCodeRow;
            }
            let _this = this;
            _this.$nextTick(async () => {
                let pagertwo = _this.$refs.refpagertwo.getPager()
                this.getstyleCodeRow = row;
                let params = this.tableType == 'one' ? {
                    styleCode: row.styleCode,
                    queryType: 3,
                    listType: this.filter.listType,
                    ...pagertwo,
                    ...this.pagertwo
                } : {
                    styleCode: row.styleCode,
                    platform: row.platform,
                    groupId: row.groupId,
                    queryType: 3,
                    listType: 1,
                    ...pagertwo,
                    ...this.pagertwo
                }
                if (this.tableType == 'two' && this.filter.listType == 0) {
                    params = {
                        styleCode: row.styleCode,
                        queryType: 3,
                        listType: 1,
                        ...pagertwo,
                        ...this.pagertwo
                    }
                }
                this.listLoading = true
                const res = await getContinuLossesGoodsCodeDetailList(params)
                this.listLoading = false
                if (!res?.success) { return }
                this.xitableData = res.data.list;
                this.xitableDataSummary = res.data.summary;
                this.totaltwo = res.data.total;
            })
        },
        async getstyleCodeTu(row, type, columnName) { //获取系列编码趋势图 //志成
            let that = this;

            // queryType：0系列编码汇总   1运营组申报     2系列编码详情Id维度     3系列编码详情编码维度
            // listType：0 全部   1个人
            // type 0: 汇总， 1：运营组汇总，2：汇总ID明细， 3：申报明细ID趋势图 4:上月毛三、毛三率
            let params = {};
            if (type === 0) {
                params.styleCode = row.styleCode;
                params.queryType = 0;
                params.listType = this.filter.listType;
            } else if (type === 1) {
                if (this.tableType == 'two' && this.filter.listType == 0) {
                    params.styleCode = row.styleCode;
                    params.queryType = 1;
                    params.listType = this.filter.listType;
                } else {
                    params.styleCode = row.styleCode;
                    params.groupId = row.groupId;
                    params.platform = row.platform;
                    params.queryType = 1;
                    params.listType = this.filter.listType;
                }
            } else if (type === 2) {
                params.styleCode = this.getstyleCodeRow.styleCode;
                params.groupId = row.groupId;
                params.platform = row.platform;
                params.proCode = row.proCode;
                params.operateSpecialUserName = row.operateSpecialUserName;
                params.shopName = row.shopName;
                params.operateSpecialUserId = row.operateSpecialUserId;
                params.shopCode = row.shopCode;
                params.queryType = 2;
                params.listType = this.filter.listType;
            } else if (type === 3) {
                params.proCode = row.proCode;
                params.platform = row.platform;
                params.groupId = this.shenbaoForm.groupId;
                params.styleCode = this.shenbaoForm.styleCode;
                params.selectColumn = columnName;
                params.operateSpecialUserId = row.operateSpecialUserId;
                params.queryType = 2;
                params.listType = 1;
            } else if(type == 4){
                params.styleCode = this.getstyleCodeRow.styleCode;
                params.groupId = row.groupId;
                params.platform = row.platform;
                params.proCode = row.proCode;
                params.operateSpecialUserName = row.operateSpecialUserName;
                params.shopName = row.shopName;
                params.operateSpecialUserId = row.operateSpecialUserId;
                params.shopCode = row.shopCode;
                params.queryType = 2;
                params.listType = this.filter.listType;
            }
            params.selectColumn = columnName;
            this.productLoading = true
            let res = new Object();
            if (type === 2 || type === 3) {
                res = await getContinuLossesDtlAnalysis(params)
            } else if(type == 4){
                res = await getLastMonthProfitAnalysis(params)
            } else {
                res = await getContinuLossesListAnalysis(params)
            }
            this.productLoading = false
            if (res && res.success) {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data;
                that.buscharDialog.title = "";
                that.$nextTick(() => {
                    that.$refs.buschar.initcharts();
                });
            }
        },
        async getstyleGoodsTu(row, type, columnName) { //获取最后一个趋势图 //管
            let that = this;
            // type 1: 汇总， 2：运营组汇总，3：汇总ID明细， 4：汇总编码明细 5:申报明细趋势图
            let params =
                type == 4 ? {//汇总编码明细===
                    styleCode: this.getstyleCodeRow.styleCode,
                    groupId: row.groupId,
                    operateSpecialUserId: row.operateSpecialUserId, //运营专员
                    goodsCode: row.goodsCode,
                    platform: row.platform,
                    queryType: 3,
                    listType: 1
                } : type == 5 ? {//申报明细趋势图===
                    styleCode: this.getstyleCodeRow.styleCode,
                    platform: this.getstyleCodeRow.platform,
                    groupId: this.getstyleCodeRow.groupId,
                    goodsCode: row.goodsCode,
                    queryType: 3,
                    listType: this.filter.listType
                } : {
                }
            params.selectColumn = columnName;
            this.listLoading = true
            const res = await getContinuLossesDtlAnalysis(params)
            this.listLoading = false
            if (!res?.success) { return }
            if (res && res.success) {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data;
                that.buscharDialog.title = "";
                that.$nextTick(() => {
                    that.$refs.buschar.initcharts();
                });
            }
        },
        removeDuplicatesByName(array, name) {
            return array.filter((item, index) => {
                return array.findIndex(obj => obj[name] === item[name]) === index;
            });
        }
    }
})
</script>
<style lang="scss" scoped>
::v-deep .el-table__fixed-footer-wrapper tbody td {
    color: blue;
}

// ::v-deep .vxe-table--body-wrapper{
//     overflow-x: hidden;
// }
.vxetable202212161323 {
    border: none !important;
}

.flexrow {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.top {
    display: flex;

    .item {
        margin-right: 10px;
    }
}

// ::v-deep .vxe-table--body-wrapper{
//     display: none;
// }
::v-deep .fixed-left--wrapper {
    overflow-x: hidden !important;
}

::v-deep .el-select__tags-text {
  max-width: 70px;
}
</style>
