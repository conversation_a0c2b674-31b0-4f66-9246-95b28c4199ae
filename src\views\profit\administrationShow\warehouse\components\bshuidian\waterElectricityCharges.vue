<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="ListInfo.calculateMonthArr" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" type="monthrange" style="width: 250px;margin-right: 5px;" :clearable="false"
          :value-format="'yyyy-MM'">
        </el-date-picker>
        <el-select v-model="ListInfo.warehouseNameArr" placeholder="仓储" class="publicCss" clearable multiple
          collapse-tags>
          <el-option v-for="item in warehouseList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="ListInfo.regionArr" placeholder="区域" class="publicCss" clearable multiple collapse-tags>
          <el-option v-for="item in districtList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-button type="primary" @click="getList('search')">查询</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" @click="downExcel">模板下载</el-button>
        <el-button type="primary" @click="exportExcel('search')">导出</el-button>
      </div>
    </template>
    <div style="width: 100%; height: 100%;">
      <vxe-table border show-footer width="100%" height="100%" ref="newtable" :row-config="{ height: 40 }" show-overflow
        :loading="loading" :column-config="{ resizable: true }" :merge-footer-items="mergeFooterItems"
        :footer-method="footerMethod" :span-method="mergeRowMethod" :row-class-name="rowClassName" :data="tableData">
        <vxe-column field="monthDate" width="150" title="月份"></vxe-column>
        <vxe-column field="warehouseName" width="130" title="仓储"></vxe-column>
        <vxe-column field="region" width="130" title="区域"></vxe-column>
        <vxe-colgroup title="日常水费费用" align="center">
          <vxe-column field="dailyWaterConsumption" width="180" title="耗水量(度)"></vxe-column>
          <vxe-column field="dailyWaterExpenses" width="180" title="费用(元)"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="日常电费费用" align="center">
          <vxe-column field="dailyElectricityConsumption" width="190" title="耗电量(度)"></vxe-column>
          <vxe-column field="dailyElectricityExpenses" width="190" title="费用(元)"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="总计" align="center">
          <vxe-column field="totalCost" width="190" title="总费用(元)"></vxe-column>
        </vxe-colgroup>
        <vxe-column field="remark" width="220" title="备注"></vxe-column>
        <vxe-column title="操作" footer-align="left" width="120px" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="mini" v-if="scope.row.region && scope.row.region.indexOf('小计') == -1"
              @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <el-button type="text" size="mini" style="color:red" :disabled="scope.row.status == 1"
              v-if="scope.row.region && scope.row.region.indexOf('小计') == -1"
              @click="handleRemove(scope.$index, scope.row)">删除</el-button>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <el-drawer title="编辑" :visible.sync="dialogVisibleEdit" size="30%">
      <waterElectricityEdit ref="waterElectricityEdit" v-if="dialogVisibleEdit" :editInfo="editInfo"
        @search="closeGetlist" @cancellationMethod="dialogVisibleEdit = false" />
    </el-drawer>
    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { downloadLink } from "@/utils/tools.js";
import { warehouseWaterElectricityListValue, warehouseWaterElectricityPage, dimissionManageArchive, warehouseWaterElectricityImport, warehouseWaterElectricityRemove } from '@/api/people/peoplessc.js';
import waterElectricityEdit from "./waterElectricityEdit.vue";
import checkPermission from '@/utils/permission'
export default {
  name: "waterElectricityCharges",
  components: {
    MyContainer, vxetablebase, waterElectricityEdit
  },
  data() {
    return {
      warehouseList: [],
      downloadLink,
      dialogVisibleEdit: false,
      editInfo: {},
      fileList: [],
      dialogVisible: false,
      districtList: [],
      timeCundang: '',
      tableData: [],
      footerData: [],
      mergeFooterItems: [],
      somerow: 'costType,monthDate,warehouseName',
      that: this,
      ListInfo: {
        calculateMonthArr: [
          dayjs().subtract(1, 'month').format('YYYY-MM'),
          dayjs().subtract(1, 'month').format('YYYY-MM')],
        monthDate: '',
        fieldName: 'fieldName',
        fieldValue: 'fieldValue',
        regionArr: [],
        warehouseNameArr: [],
      },
      timeRanges: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
    await this.init()
  },
  methods: {
    async init() {
      const { data, success } = await warehouseWaterElectricityListValue('warehouseName')
      if (success) {
        this.warehouseList = data
      }
    },
    footerMethod({ columns, data }) {
      const sums = [];
      if (!this.footerData)
        return sums
      let newfield = columns.map(item => item.field)
      let newfooterdata = [];
      this.footerData.forEach((item, index) => {
        let newarr2 = [];
        newfield.forEach((item2, index2) => {
          newarr2.push(item[item2])
        })
        newfooterdata.push(newarr2)
      })

      return newfooterdata;
    },
    rowClassName(event) {
      if (event.row.region == '小计') {
        return 'row-green'
      }
      return null
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("file", item.file);
      // form.append("isArchive", checkPermission("ArchiveStatusEditing"));
      // form.append("calculateMonth", this.ListInfo.calculateMonth);
      var res = await warehouseWaterElectricityImport(form);
      if (res?.success) {
        this.$message({ message: res.msg, type: "success" });
      }
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    downExcel() {
      //下载excel
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250515/1922924397497597952.xlsx', '水电费导入模板.xlsx');
    },
    async saveBane() {
      this.$confirm('是否存档？存档后不可修改！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { data, success } = await dimissionManageArchive(this.ListInfo)
        if (!success) {
          return;
        }
        this.getList();
        this.$message.success('保存存档成功！')

      }).catch(() => {
        // this.$message.error('取消')
      });
    },
    exportExcel() {
      this.$refs.newtable.exportData({ filename: '仓储看板-水电费', sheetName: 'Sheet1', type: 'xlsx' })
    },
    closeGetlist() {
      this.dialogVisibleEdit = false;
      this.getList()
    },
    handleEdit(index, row) {
      this.editInfo = row;
      this.dialogVisibleEdit = true;
    },
    async handleRemove(index, row) {
      this.$confirm('是否删除！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.editInfo = row;
        this.loading = true
        const { data, success } = await warehouseWaterElectricityRemove({ ids: row.id })
        this.loading = false
        if (success) {
          this.$message.success('删除成功')
          this.getList();
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {

      });
    },
    // 通用行合并函数（将相同多列数据合并为一行）
    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      const fields = this.somerow.split(',')
      const cellValue = row[column.property]
      if (cellValue && fields.includes(column.property)) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && prevRow[column.property] === cellValue) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow && nextRow[column.property] === cellValue) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      this.loading = true
      const { warehouseNameArr = [], regionArr = [], calculateMonthArr = [] } = this.ListInfo;
      const params = {
        ...this.ListInfo,
        warehouseName: warehouseNameArr.join(','),
        region: regionArr.join(','),
        monthDate: calculateMonthArr.join(','),
      };
      delete params.warehouseNameArr;
      delete params.regionArr;
      delete params.calculateMonthArr;
      const { data, success } = await warehouseWaterElectricityPage(params)
      if (success) {
        this.tableData = data.list
        data.summary.regionName = '合计';
        data.summary.calculateMonth = data.list.length > 0 ? data.list[0].calculateMonth : '';
        if (data.summary) {
          this.timeCundang = data.summary.archiveTime
        }
        // let zhanbi = ['eliminateRate', 'bodyReasonsRate', 'familyReasonsRate', 'hasWorkRate', 'notAdaptedWorkRate'];
        // zhanbi.map((item) => {
        //   data.summary[item] = data.summary[item] ? data.summary[item] + '%' : ''
        // })
        this.footerData = data.summary
        const fieldsToFormat = [
          "quitCount",
        ];
        this.footerData.forEach((item) => {
          fieldsToFormat.forEach((field) => {
            if (item[field] !== null && item[field] !== undefined) {
              item[field] = this.formatNumberWithThousandSeparator(item[field]);
            }
          });
        });
        //取列表中的区域
        const newDistricts = this.tableData.map(item => item.region).filter(district => district !== undefined && district !== null && district.indexOf('小计') == -1 && district.indexOf('占比') == -1)
        this.districtList = Array.from(new Set([...this.districtList, ...newDistricts]));
        this.loading = false
      } else {
        this.loading = false
        this.$message.error('获取列表失败')
      }
    },
    formatNumberWithThousandSeparator(value) {
      if (value === null || value === undefined) return value;
      return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

:deep(.vxe-header--column) {
  background: #00937e;
  color: white;
  font-weight: 600;
}

:deep(.vxe-footer--row) {
  background: #00937e;
  color: white;
  font-weight: 600;
}

:deep(.row-green) {
  background-color: rgb(247, 230, 193);
  // color: #fff;
}
</style>
