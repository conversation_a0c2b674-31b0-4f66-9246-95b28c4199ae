<template>
  <container v-loading="pageLoading">
     <ces-table ref="table" :that='that' :isIndex='true' :isSelectColumn='true' :hasexpand='true' @sortchange='sortchange' @expandchange='expandchange'
                           tablekey='warnorder' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
        <el-table-column type="expand">
          <template slot-scope="props">
             <div style="padding: 0;height: 100px;"> 
               <ces-table ref="tabledetail" :that='that' :isIndex='true' :isSelectColumn='false' :tableData.sync='props.row.items' 
                                tablekey='warnorderdetail' :tableCols='tableColsitems' :tableHandles='tableHandlesitems' :loading="listLoading">
               </ces-table>
           </div>
         </template>
        </el-table-column>
        <template slot='extentbtn'>
          <el-button-group>
            <el-button style="padding: 0;">
              <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组" style="width: 90px">
                <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter.remainingHoursType" placeholder="剩余小时订单" clearable style="width: 140px" @change='onchangeplatform'>
                <el-option label="剩8小时订单" :value="8"></el-option>
                <el-option label="剩7小时订单" :value="7"></el-option>
                <el-option label="剩6小时订单" :value="6"></el-option>
                <el-option label="剩5小时订单" :value="5"></el-option>
                <el-option label="剩4小时订单" :value="4"></el-option>
                <el-option label="剩3小时订单" :value="3"></el-option>
                <el-option label="剩2小时订单" :value="2"></el-option>
                <el-option label="剩1小时订单" :value="1"></el-option>
                <el-option label="严重告警订单" :value="0"></el-option>
                <el-option label="已延迟订单" :value="-1"></el-option>
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter.platform" placeholder="平台" clearable style="width: 100px" @change='onchangeplatform'>
                <el-option label="淘系" :value="1"></el-option>
                <el-option label="拼多多" :value="2"></el-option>
                <el-option label="工厂店" :value="8"></el-option>
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select filterable v-model="filter.shopCode" placeholder="请选择店铺" clearable style="width: 140px">
                <el-option v-for="item in shoplist" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"/>
              </el-select>
            </el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
        </el-button-group>
       </template>
     </ces-table>
     <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
    </template>
  </container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container/noheader";
import { formatPlatform,formatYesorno} from "@/utils/tools";
import { pageOrderWarn,queryOrderWarnGoods,getAllOrderWarnOrderNoInner} from "@/api/order/ordererror";
import {getDirectorGroupList,getList as getshopList} from '@/api/operatemanage/base/shop'
const tableCols =[
     {istrue:true,prop:'orderNoInner',label:'内部订单号', width:'100',sortable:'custom',type:'orderLogInfo',orderType:'orderNoInner' },
     {istrue:true,prop:'orderNo',label:'线上订单号', width:'120',sortable:'custom'},     
     {istrue:true,prop:'platform',label:'平台', width:'60',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
     {istrue:true,prop:'shopCode',label:'店铺', width:'120',sortable:'custom',formatter:(row)=>row.shopName},
     {istrue:true,prop:'status',label:'主订单状态', width:'100',sortable:'custom'},
     {istrue:true,prop:'payTime',label:'付款日期', width:'120',sortable:'custom'},
     {istrue:true,prop:'remainingHours',label:'剩余小时', width:'80'},
     {istrue:true,prop:'proCount',label:'宝贝数量', width:'80',sortable:'custom'},
     {istrue:true,prop:'proCountType',label:'宝贝数量类型', width:'120',sortable:'custom',formatter:(row)=>row.proCountType==1?'一单一品':row.proCountType==2?'一单多品': ' '},
     {istrue:true,prop:'statusId',label:'状态', width:'60',sortable:'custom',formatter:(row)=>row.statusId==1?'待付款':row.statusId==2?'发货中':row.statusId==3?'已发货': 
            row.statusId==4?'被拆分': row.statusId==5?'被合并': row.statusId==6?'异常': row.statusId==7?'取消': row.statusId==8?'已客审待财审':' '},
     {istrue:true,prop:'isSplit',label:'是否被拆分', width:'100',sortable:'custom',formatter:(row)=>formatYesorno(row.isSplit)},
     {istrue:true,prop:'isFalseShopping',label:'是否刷单', width:'80',sortable:'custom',formatter:(row)=>formatYesorno(row.isFalseShopping)},
     {istrue:true,prop:'isMerge',label:'是否合并', width:'80',sortable:'custom',formatter:(row)=>formatYesorno(row.isMerge)},
     {istrue:true,prop:'buyerRemark',label:'买家留言', width:'100',sortable:'custom'},
     {istrue:true,prop:'orderRemark',label:'订单备注', width:'100',sortable:'custom'},
     {istrue:true,prop:'label',label:'标签', width:'100',sortable:'custom'},
     {istrue:true,prop:'sendTime',label:'发货日期', width:'120',sortable:'custom'},
     {istrue:true,prop:'orderCost',label:'订单成本', width:'80',sortable:'custom'},
     {istrue:true,prop:'orderAmount',label:'订单金额', width:'80',sortable:'custom'},
     {istrue:true,prop:'amounted',label:'已付金额', width:'80',sortable:'custom'}
    ];
const tableColsitems=[
     {istrue:true,prop:'orderNoInner',label:'内部订单号', width:'100',sortable:'custom'},
     {istrue:true,prop:'orderNo',label:'线上订单号', width:'120',sortable:'custom'},
     {istrue:true,prop:'childrenOrderNo',label:'子订单编号', width:'120',sortable:'custom'},
     {istrue:true,prop:'goodsCode',label:'商品编码', width:'120',sortable:'custom'},
     {istrue:true,prop:'goodsName',label:'商品名称', width:'120',sortable:'custom'},
     {istrue:true,prop:'qty',label:'数量', width:'120',sortable:'custom'},
     {istrue:true,prop:'goodsAmount',label:'商品金额', width:'120',sortable:'custom'},
     {istrue:true,prop:'proCode',label:'店铺款式编码', width:'120',sortable:'custom',formatter:(row)=>row.proCountType==1?'一单一品':row.proCountType==2?'一单多品': ' '},
     {istrue:true,prop:'refundStatus',label:'退款状态', width:'120',sortable:'custom'},
     {istrue:true,prop:'returnQty',label:'实退数量', width:'120',sortable:'custom'},
     {istrue:true,prop:'goodsCost',label:'成本价', width:'120',sortable:'custom'},
     {istrue:true,prop:'groupId',label:'运营组', width:'120',sortable:'custom',formatter:(row)=>row.groupId==0?' ':row.groupName},
     {istrue:true,prop:'brandId',label:'采购组', width:'120',sortable:'custom',formatter:(row)=>row.brandId==0?' ':row.brandName},
     {istrue:true,prop:'isReissued',label:'是否补发', width:'120',sortable:'custom',formatter:(row)=>formatYesorno(row.isReissued)},
     {istrue:true,prop:'isGift',label:'是否赠品', width:'120',sortable:'custom',formatter:(row)=>formatYesorno(row.isGift)},
     {istrue:true,prop:'virtualType',label:'虚拟分类', width:'120',sortable:'custom'},
     {istrue:true,prop:'amounted',label:'已付金额', width:'120',sortable:'custom'}
    ];
    const tableHandles1=[{label:"复制所有订单号", handle:(that)=>that.copyAllOderNoInner()},];
    const tableHandles2=[ ];
export default {
  name: "warnorder",
  components: {cesTable, container },
  data() {
    return {
      that:this,
      filter: {groupId: null,shopCode:null,platform:null,remainingHoursType:null},
      list: [],
      listLoading: false,
      pageLoading: false,
      grouplist:[], 
      shoplist:[],
      tableCols:tableCols,
      tableHandles:tableHandles1,
      tableColsitems:tableColsitems,
      tableHandlesitems:tableHandles2,
      pager:{OrderBy:" id ",IsAsc:false},
      total:0,
      sels: [],
      selids: [], 
      listLoading: false,
      pageLoading: false
    };
  },
  async mounted() {
    await this.setGroupSelect();
    //await this.onSearch();
  },
  methods: {
    async setGroupSelect(){
      var res2= await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => {return { value: item.key, label: item.value }});
    },
    async onchangeplatform(val){
      const res = await getshopList({platform:val,CurrentPage:1,PageSize:300});
      this.shoplist=res.data.list
    },
    sortchange(column){
      if(!column.order) this.pager={OrderBy:"id",IsAsc:false}
      else this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getList();
    },
    onSearchWarnOrders(hours,groupid) {
      this.filter.remainingHoursType=hours
      this.filter.groupId=groupid
      this.$refs.pager.setPage(1)
      this.getList();
    },
    async getList() {
      var pager = this.$refs.pager.getPager()
      const params = {...pager, ...this.filter,...this.pager};
      this.listLoading = true;
      const res = await pageOrderWarn(params);
      this.listLoading = false;
      if (!res?.success) return; 
      this.total = res.data.total
      const data = res.data.list
      data.forEach((d) => {d._loading = false;});
      this.list = data;
    },
   async copyAllOderNoInner(){
      var pager = this.$refs.pager.getPager()
      const params = {...pager, ...this.filter,...this.pager};
      var res = await getAllOrderWarnOrderNoInner(params);
      if (!res.data) {
        this.$message({ message: "没有获取到订单号", type: "warning" });
        return;
      }
      this.doCopy(res.data)
   },
   doCopy: function (val) {       
      let that=this;                         
      this.$copyText(val).then(function (e) {
          that.$message({ message: "内容已复制到剪切板！", type: "success" });
      }, function (e) {
          that.$message({ message: "抱歉，复制失败！", type: "warning" });
      })
    },
   async expandchange(row, args){
      if(args.length>0){
        const res = await queryOrderWarnGoods(row.orderNoInner);
        if (!res?.success) return;
        row.items=res.data;
        let _that=this;
        this.$nextTick(() => {          
        setTimeout(function(){
          //row.items=res.data;
          _that.$refs.tabledetail.doLayout()
        },1000);
        });
      }
    },
    selsChange: function(sels) {
      this.sels = sels
    },
  },
};
</script>
 
