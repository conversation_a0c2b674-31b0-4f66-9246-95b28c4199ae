<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startReceiveDate" :endDate.sync="ListInfo.endReceiveDate"
                    class="publicCss" startPlaceholder="收寄时间" endPlaceholder="收寄时间" :clearable="false" />
                <el-select v-model="ListInfo.warehouses" clearable filterable placeholder="仓库" class="publicCss"
                    multiple collapse-tags>
                    <el-option v-for="item in warehouselist" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <number-range :min.sync="ListInfo.totalCountMin" :max.sync="ListInfo.totalCountMax"
                    min-label="订单量 - 最小值" max-label="订单量 - 最大值" class="publicCss" />
                <number-range :min.sync="ListInfo.totalFeeMin" :max.sync="ListInfo.totalFeeMax" min-label="总快递费 - 最小值"
                    max-label="总快递费 - 最大值" class="publicCss" />
                <number-range :min.sync="ListInfo.feeMin" :max.sync="ListInfo.feeMax" min-label="快递费 - 最小值"
                    max-label="快递费 - 最大值" class="publicCss" />
                <number-range :min.sync="ListInfo.weightMin" :max.sync="ListInfo.weightMax" min-label="重量 - 最小值"
                    max-label="重量 - 最大值" class="publicCss" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' border
            @sortchange='sortchange' :tableData='data.list' :tableCols='tableCols' :isSelection="false"
            :showsummary="data.summary ? true : false" :summaryarry="data.summary" id="20250405105848"
            :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog :visible.sync="dialogVisible" width="80%" v-dialogDrag>
            <courierFeeBreakdown :info="info" v-if="dialogVisible" @linkToDetail="linkToDetail" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, formatWarehouseNew, warehouselist } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import numberRange from "@/components/number-range/index.vue";
import { mergeTableCols, } from '@/utils/getCols'
import request from '@/utils/request'
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import courierFeeBreakdown from './courierFeeBreakdown.vue'
import billReview from './billReview.vue'
import decimal from "@/utils/decimalToFixed";
const tableCols = [
    { sortable: 'custom', width: '110', align: 'center', prop: 'warehouse', label: '仓库', formatter: (row) => formatWarehouseNew(row.warehouse) },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalCount', label: '订单量', summaryType: 'Sum', type: 'click', handle: (that, row) => that.openDetails1(row) },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalFee', label: '快递费', summaryType: 'Sum', type: 'click', handle: (that, row) => that.openDetails1(row) },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'count1', label: '订单量', mergeName: '0-500g' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'countRate1', label: '订单占比', mergeName: '0-500g' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'fee1', label: '快递费', mergeName: '0-500g' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'feeRate1', label: '快递费占比', mergeName: '0-500g' },

    { sortable: 'custom', width: 'auto', align: 'center', prop: 'count2', label: '订单量', mergeName: '500g-1kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'countRate2', label: '订单占比', mergeName: '500g-1kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'fee2', label: '快递费', mergeName: '500g-1kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'feeRate2', label: '快递费占比', mergeName: '500g-1kg' },

    { sortable: 'custom', width: 'auto', align: 'center', prop: 'count3', label: '订单量', mergeName: '1kg-2kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'countRate3', label: '订单占比', mergeName: '1kg-2kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'fee3', label: '快递费', mergeName: '1kg-2kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'feeRate3', label: '快递费占比', mergeName: '1kg-2kg' },

    { sortable: 'custom', width: 'auto', align: 'center', prop: 'count4', label: '订单量', mergeName: '2kg-3kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'countRate4', label: '订单占比', mergeName: '2kg-3kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'fee4', label: '快递费', mergeName: '2kg-3kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'feeRate4', label: '快递费占比', mergeName: '2kg-3kg' },

    { sortable: 'custom', width: 'auto', align: 'center', prop: 'count5', label: '订单量', mergeName: '3kg-4kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'countRate5', label: '订单占比', mergeName: '3kg-4kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'fee5', label: '快递费', mergeName: '3kg-4kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'feeRate5', label: '快递费占比', mergeName: '3kg-4kg' },

    { sortable: 'custom', width: 'auto', align: 'center', prop: 'count6', label: '订单量', mergeName: '4kg-5kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'countRate6', label: '订单占比', mergeName: '4kg-5kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'fee6', label: '快递费', mergeName: '4kg-5kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'feeRate6', label: '快递费占比', mergeName: '4kg-5kg' },

    { sortable: 'custom', width: 'auto', align: 'center', prop: 'count7', label: '订单量', mergeName: '5kg-6kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'countRate7', label: '订单占比', mergeName: '5kg-6kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'fee7', label: '快递费', mergeName: '5kg-6kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'feeRate7', label: '快递费占比', mergeName: '5kg-6kg' },

    { sortable: 'custom', width: 'auto', align: 'center', prop: 'count8', label: '订单量', mergeName: '6kg-7kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'countRate8', label: '订单占比', mergeName: '6kg-7kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'fee8', label: '快递费', mergeName: '6kg-7kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'feeRate8', label: '快递费占比', mergeName: '6kg-7kg' },

    { sortable: 'custom', width: 'auto', align: 'center', prop: 'count9', label: '订单量', mergeName: '7kg-8kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'countRate9', label: '订单占比', mergeName: '7kg-8kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'fee9', label: '快递费', mergeName: '7kg-8kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'feeRate9', label: '快递费占比', mergeName: '7kg-8kg' },

    { sortable: 'custom', width: 'auto', align: 'center', prop: 'count10', label: '订单量', mergeName: '8kg-9kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'countRate10', label: '订单占比', mergeName: '8kg-9kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'fee10', label: '快递费', mergeName: '8kg-9kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'feeRate10', label: '快递费占比', mergeName: '8kg-9kg' },

    { sortable: 'custom', width: 'auto', align: 'center', prop: 'count11', label: '订单量', mergeName: '9kg-10kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'countRate11', label: '订单占比', mergeName: '9kg-10kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'fee11', label: '快递费', mergeName: '9kg-10kg' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'feeRate11', label: '快递费占比', mergeName: '9kg-10kg' },

    { sortable: 'custom', width: 'auto', align: 'center', prop: 'count12', label: '订单量', mergeName: '10kg以上' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'countRate12', label: '订单占比', mergeName: '10kg以上' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'fee12', label: '快递费', mergeName: '10kg以上' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'feeRate12', label: '快递费占比', mergeName: '10kg以上' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, numberRange, chooseWareHouse, courierFeeBreakdown, billReview
    },
    data() {
        return {
            that: this,
            warehouselist,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                type: 0,
                startReceiveDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),//开始时间
                endReceiveDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),//结束时间
                summarys: []
            },
            api: '/api/express/Express/StatExpress/',
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            data: {},
            info: {},
            dialogVisible: false,
            dialogVisible1: false,
            changeTabInfo: {
                row: null,
                range: null,
                weightType: null,
            }
        }
    },
    created() {
        this.getCol()
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        linkToDetail(e) {
            this.dialogVisible = false
            this.info = e
            this.$emit('linkToDetail', { params: this.info, changeTabInfo: this.changeTabInfo })
        },
        openDetails1(row) {
            console.log(row.warehouse, 'warehouse');
            this.info = {
                startReceiveDate: this.ListInfo.startReceiveDate,
                endReceiveDate: this.ListInfo.endReceiveDate,
                warehouseId: row.warehouse,
            }
            this.$emit('linkToDetail', { params: this.info, changeTabInfo: {} })
        },
        openDetails(row, range, weightType) {
            this.info = {
                row,
                range,
                type: 0,
                list: [
                    { label: '快递费', prop: 'totalFee', },
                    { label: '订单量', prop: 'totalCount', },
                    { label: '仓库', prop: 'warehouse', },
                ],
                weightType,
                startReceiveDate: this.ListInfo.startReceiveDate,
                endReceiveDate: this.ListInfo.endReceiveDate,
                isDetails: false,
            }
            this.changeTabInfo = {
                row,
                range,
                weightType,
                type: 0,
                startReceiveDate: this.ListInfo.startReceiveDate,
                endReceiveDate: this.ListInfo.endReceiveDate,
            }
            if (weightType != 11) {
                this.info.warehouseId = [row.warehouse]
                let weightMin;//起始值
                let weightMax;//结束值
                [weightMin, weightMax] = this.extractNumbers(this.info.range)
                if (weightMax == 500) {
                    weightMax = 0.5
                } else if (weightMin == 500) {
                    weightMin = 0.5
                }
                this.info.weightMin = weightMin
                this.info.weightMax = weightMax
                this.dialogVisible = true
            } else {
                console.log(row.warehouse, 'row.warehouse');
                const params = { ...this.info, weightMin: 10, weightMax: null, warehouseId: row.warehouse, }
                this.$emit('linkToDetail', { params, changeTabInfo: {} })
            }
        },
        extractNumbers(str) {
            const matches = str.match(/\d+(\.\d+)?/g); // 匹配整数或小数
            return matches ? matches.map(Number) : []; // 转换为数字数组
        },
        async getCol() {
            const map = {
                '0-500g': 0.5,
                '500g-1kg': 1,
                '1kg-2kg': 2,
                '2kg-3kg': 3,
                '3kg-4kg': 4,
                '4kg-5kg': 5,
                '5kg-6kg': 6,
                '6kg-7kg': 7,
                '7kg-8kg': 8,
                '8kg-9kg': 9,
                '9kg-10kg': 10,
                '10kg以上': 11
            }
            this.tableCols.forEach(item => {
                item.enabled = true
                item.width = '100'
                if (item.mergeName) {
                    item.summaryType = 'Sum'
                    item.type = 'click'
                    item.handle = (that, row) => that.openDetails(row, item.mergeName, map[item.mergeName])
                    if (item.label?.includes('占比')) {
                        item.summaryType = null
                        item.formatter = (row) => {
                            return decimal(row[item.prop], 100, '*') + '%'
                        }
                    }
                }
            })
            this.ListInfo.summarys = this.tableCols
                .filter((a) => a.summaryType)
                .map((a) => {
                    return { column: a["prop"].charAt(0).toUpperCase() + a["prop"].slice(1), summaryType: a.summaryType };
                });
            this.$set(this, 'tableCols', mergeTableCols(this.tableCols))
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}

::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>
