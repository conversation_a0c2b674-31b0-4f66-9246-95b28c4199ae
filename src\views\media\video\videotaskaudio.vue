1
<template>
    <my-container st>

        <div style="padding:0 10px 0 10px;">
            <el-button v-if="islook" type="primary" @click="bacthdownVideo" :disabled ="isDown">一键下载视频</el-button>
            <el-form :lodading="downLoading">
                <el-row :gutter="15"  >
                    <el-col :xs="24" :sm="24" :lg="16">
                        <el-card class="box-card" :style="{width:'100%' ,height:cardHeight,overflow: 'auto'}">
                                <el-table :data="uploadVideoList" highlight-current-row  @selection-change="handleSelectionChange">
                                    <el-table-column type="selection" width="30" :selectable="selectEnable"> </el-table-column>
                                    <el-table-column prop="title" label="参考视频" width="80">
                                        <template slot-scope="scope">
                                            参考视频{{scope.row.videoIndex}}
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="title" label="参考片段" width="80">
                                        <template slot-scope="scope">
                                            <div class="cell el-tooltip" style="width: 83px;">
                                                <a :href="scope.row.url" target="_blank" style="color: blue; cursor: pointer;"> {{scope.row.url}}</a>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="title" label="片段标题" width="80">
                                    </el-table-column>
                                    <el-table-column prop="remark" label="备注">
                                    </el-table-column>
                                    <el-table-column prop="name" label="参考剪辑" width="80">
                                        <template slot-scope="scope">
                                            <div style="position: relative;">
                                                <el-image :src="scope.row.sourceImgPath" style="max-width: 50px; max-height: 50px;" fit="fill" :lazy="true"></el-image>
                                                <span style="display: block;position: absolute;top: 10px;left: 10px;">
                                                    <a size="mini" class="el-link el-link--primary is-underline" @click="playerVideo(scope.row.sourceVideoPath)" style="margin-left: 3px;color:#ccc;font-size: 23px;">
                                                        <i class="el-icon-video-play"></i>
                                                    </a>
                                                </span><br/>
                                                <a  style="margin-left: 3px;color:red;font-size: 12px;" @click="delCuteInfo(scope.row.cuteId)">&nbsp;&nbsp;删除&nbsp;&nbsp;</a>
                                            </div>
                                        </template>
                                    </el-table-column>                                
                                    <el-table-column prop="name" label="拍摄视频" width="80">
                                        <template slot-scope="scope" >
                                            <div style="position: relative;"  v-if="isShowUploadinfo(scope.row.id)">
                                                <el-image :src="scope.row.imgPath" style="max-width: 50px; max-height: 50px;" fit="fill" :lazy="true"></el-image>
                                                <span style="display: block;position: absolute;top: 10px;left: 10px;">
                                                    <a size="mini" class="el-link el-link--primary is-underline" @click="playerVideo(scope.row.videoPath)" style="margin-left: 3px;color:#ccc;font-size: 23px;">
                                                        <i class="el-icon-video-play"></i>
                                                    </a>
                                                </span><br/>
                                                <a  style="margin-left: 3px;color:red;font-size: 12px;"  @click="delUploadInfo(scope.row.uploadId)">&nbsp;&nbsp;删除&nbsp;&nbsp;</a>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="name" label="下载" width="50 "  v-if="islook">
                                        <template slot-scope="scope">
                                            <span>
                                                <a size="mini" v-if="scope.row.statusEnum==2 " class="el-link el-link--primary is-underline" @click="downVideo(scope.row)">
                                                    下载
                                                </a>
                                            </span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="createdUserName" label="上传人" width="80" >
                                    </el-table-column>
                                    <el-table-column prop="createdTime" label="上传时间" width="90">
                                    </el-table-column>
                                    <el-table-column  label="审核意见">
                                        <template slot-scope="scope">
                                            <el-tooltip :content="scope.row.approveRemark" placement="top"  >
                                                <el-input  type="textarea" v-model="scope.row.approveRemark" :disabled="islook" :max="1000" v-if="isShowUploadinfo(scope.row.id)  &&scope.row.cuteType ==0 "></el-input>
                                            </el-tooltip>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="createdTime" label="是否通过" width="120">
                                        <template slot-scope="scope">
                                            <el-radio-group v-model="scope.row.statusEnum"  :disabled="islook"  v-if="isShowUploadinfo(scope.row.id) &&scope.row.cuteType ==0 ">
                                                <el-radio :label="2">是</el-radio>
                                                <el-radio :label="3">否</el-radio>
                                            </el-radio-group>
                                        </template>
                                    </el-table-column>
                                <!--   <el-table-column  label="操作" v-if="!islook">
                                        <template slot-scope="scope">
                                            <a size="mini" v-if="isShowUploadinfo(scope.row.id)&&scope.row.cuteType ==0"  class="el-link el-link--primary is-underline" @click="auditUploadInfo(scope.row) ">
                                            单独审核
                                                </a>
                                        </template>
                                    </el-table-column> -->
                                </el-table>
                        </el-card>
                    </el-col>
                    <el-col :xs="24" :sm="24" :lg="8">
                    <my-container v-loading="liuyanLoading">
                        <el-scrollbar style="height:600px">
                            <videotaskbind :commendList="commendList" :disabled="islook" v-if="commendList.length>0" @getList="loadList2"></videotaskbind>
                        </el-scrollbar>
                    </my-container>
                    </el-col>
                </el-row> 
            </el-form>
        </div>

        <el-image-viewer v-if="showGoodsImage" :url-list="imgList" :on-close="closeFunc" style="z-index:9999;" />

    </my-container>
</template>
<script>
    import cesTable from "@/components/Table/table.vue";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import { getTaskUploadVideoList, getVideoTaskCommenList,delTaskCuteVideo,saveReplyCommentInfo} from '@/api/media/video';
    import { deleteUploadVideoByIdAsync} from '@/api/media/vediotask';
    import videotaskbind from '@/views/media/video/videotaskbind.vue';
    import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue';
    import ElImageViewer from 'element-ui/packages/image/src/image-viewer';
    export default {
        name: "Users",
        components: { MyContainer, MyConfirmButton, cesTable, ElImageViewer ,YhQuillEditor,videotaskbind},
        props: {
            videoTaskId: 0, islook: false, cardHeight: '300px',
            videoProductName:null,
            fatherMethod: {
                type: Function,
                default: null
            }
            , ckindex: 0,
        },
        data() {
            return {               
                uploadVideoList: [],
                commendList: [],
                selids:[],
                audioStatus: 1,
                memo: '',
                imgList: [],//完成界面图片显示器
                showGoodsImage: false,
                downLoading :false,
                isDown:false,
                liuyanLoading: false,
            };
        },
        async mounted() {
            
            this.loadList();
            this.loadList2();
        },
        methods: {
          
            //删除剪辑
            delCuteInfo(id){
                this.$confirm("确认删除当前片段吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(async () => {
                  var res=   await delTaskCuteVideo({  cuteId: id })
                    if (res?.success) {
                        this.$message({ message: '操作成功', type: "success" });
                        await this.loadList();
                    } 
                });
            },
            //删除上传
            delUploadInfo(id){
                this.$confirm("确认删除当前拍摄视频吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(async () => {
                  var res = await deleteUploadVideoByIdAsync({ videoId: id })
                    if (res?.success) {
                        this.$message({ message: '操作成功', type: "success" });
                       await this.loadList();
                    }
                }); 
            },
            // 选择下载
            handleSelectionChange(rows,row){
                this.selids=[];
                rows.forEach(f=>{
                    this.selids.push(f);
                })
            },
            //是否选择
            selectEnable(row, rowIndex){
                return  this.islook && row.statusEnum==2 ;
            },
            //单个视频
            auditUploadInfo(row){

            },
            //是显示
            isShowUploadinfo(id){
                return id !="0";
            },
            async bacthdownVideo() {         
                if( this.selids.length == 0 ){
                    this.$message({ message: '请选择要下载的视频', type: "error" });
                    return;
                }      
                this.selids.forEach((item, index) => {
                     if(item.statusEnum == 2)
                         this.downVideo(item);
                });
            },
            //下载方法
            async downVideo(row) {
                this.isDown =true;
                var xhr = new XMLHttpRequest();
                xhr.open('GET', row.videoPath, true);
                xhr.responseType = 'arraybuffer'; // 返回类型blob
                xhr.onload = function() {
                    if (xhr.readyState === 4 && xhr.status === 200) {
                        let blob = this.response;
                        // 转换一个blob链接
                        // 注: URL.createObjectURL() 静态方法会创建一个 DOMString(DOMString 是一个UTF-16字符串)，
                        // 其中包含一个表示参数中给出的对象的URL。这个URL的生命周期和创建它的窗口中的document绑定
                        let downLoadUrl = window.URL.createObjectURL(new Blob([blob], {type: 'video/mp4'}));
                        // 视频的type是video/mp4，图片是image/jpeg
                        // 01.创建a标签
                        let a = document.createElement('a');
                        // 02.给a标签的属性download设定名称 row.fileName ??
                        //a.download = row.fileName ??"参考视频"+row.videoIndex + "_" + row.title + "_" + row.remark + "_" +new Date().toLocaleString() + row.fileExt;
                        a.download = "参考视频"+row.videoIndex + "_" + row.title + "_" + row.remark + "_" +new Date().toLocaleString() + row.fileExt;
                        // 03.设置下载的文件名
                        a.href = downLoadUrl;
                        // 04.对a标签做一个隐藏处理
                        a.style.display = 'none';
                        // 05.向文档中添加a标签
                        document.body.appendChild(a);
                        // 06.启动点击事件
                        a.click();
                        // 07.下载完毕删除此标签
                        a.remove();
                    };
                };
                xhr.send();
                this.isDown =false;
            },
             //播放按钮
            async playerVideo(url) {
                this.$emit("playVideo", url);
            },
            async loadList() {
                
                var res = await getTaskUploadVideoList({ videoTaskId: this.videoTaskId, ckindex: this.ckindex });
                if (!res?.success) {
                    return
                }
                this.uploadVideoList = res.data.list;
           
            },
            async loadList2() {
                this.liuyanLoading=true;
                let res = await getVideoTaskCommenList({ videoTaskId: this.videoTaskId, ckindex: this.ckindex });
                if (!res?.success) {
                    return
                }
                this.commendList = res.data.list;
                this.liuyanLoading=false;
                console.log("打印fu数据",this.commendList)
            },


            //收集数据
            getSaveData() {
                var videos = [];
                var that = this;
                this.uploadVideoList.forEach((item, index) => {
                    var video = {};
                    video.Id = item.id;
                    video.CuteId = item.cuteId;
                    video.CuteType = item.cuteType;
                    video.TaskId = that.videoTaskId;
                    video.StatusEnum = item.statusEnum;
                    video.approveRemark =  item.approveRemark;
                    video.cuteTitle =  item.title;
                    video.cutevedioinex=  item.videoIndex;
                    videos.push(video);
                });
                var params = {}; 
                params.memo = this.memo;
                params.ckIndex = this.ckindex;
                params.audioStatus = this.audioStatus;
                params.videoTaskId = this.videoTaskId;
                params.videoList = videos;
                return params;
            }, 
            //完成表单界面显示图片
            async showImg(e) {
                this.showGoodsImage = true;
                this.imgList = [];
                if (e) {
                    this.imgList.push(e);
                }
                else {
                    this.imgList.push(this.imagedefault);
                }
            },
            //完成表单界面关闭图片
            async closeFunc() {
                this.showGoodsImage = false;
            },
            async editorClick(e) {
                if (e.target.nodeName.toLocaleLowerCase() == 'img') {
                    this.showImg(e.target.src);
                }
            },
        }
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
    .quill-editor {
        height: 250px;
    }
    .ql-container {
        font-family: "Avenir", Helvetica, Arial, sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-align: center;
        color: #2c3e50;
        height: 80%;
    }
    img {
        max-height: 60px;
    }

</style>

<style lang="css">

  .el-tooltip__popper{font-size: 14px; max-width:50% }

</style>
