<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dynamic-selecter v-model="ListInfo.dynamicFilter" :scene="$route.name + 'mainfest'"
                    :fields="tableCols.filter(item => item.label != '' && item.label != '操作')"
                    @update="getList('search')" />
                <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" :value-format="'yyyy-MM-dd'"
                    @change="changeTime($event, 'ks')">
                </el-date-picker>
                <el-date-picker v-model="ybTimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="预包开始时间" end-placeholder="预包结束时间" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" :value-format="'yyyy-MM-dd'"
                    @change="changeTime($event, 'yb')">
                </el-date-picker>
                <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable>
                    <el-option label="待加工" value="待加工" />
                    <el-option label="已领取" value="已领取" />
                    <el-option label="已加工" value="已加工" />
                    <el-option label="已作废" value="已作废" />
                </el-select>
                <el-input v-model.trim="ListInfo.combineCode" placeholder="组合编码" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.combineName" placeholder="组合名称" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.entityCode" placeholder="实体编码" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.batchNo" placeholder="批次号" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.skus" placeholder="Skus" maxlength="50" clearable class="publicCss" />
                <el-input v-model.trim="ListInfo.consumableCode" placeholder="包装编码" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.consumableName" placeholder="包装名称" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.claimUserName" placeholder="领取人" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.prePackUserName" placeholder="加工人 " maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.cancelUserName" placeholder="作废人" maxlength="50" clearable
                    class="publicCss" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                    <el-button type="primary" @click="batchReceivePlan">批量领取加工计划</el-button>
                    <el-button type="primary" @click="batchVoidPlan">批量作废加工计划</el-button>
                    <el-button type="primary" @click="batchDesignatePerson">批量指定加工人员</el-button>
                    <el-button type="primary"
                        @click="batchSetInvtoryVisable = !batchSetInvtoryVisable">批量设置加工单</el-button>
                </div>
            </div>
        </template>
        <vxetablebase :id="'machiningPlanning202408041847'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :border="true"
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isIndexFixed="false" :editconfig="{ trigger: 'click', mode: 'cell', showIcon: false }"
            @select="checkboxRangeEnd" :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading"
            :height="'100%'">
            <template v-slot:prePackQty="{ row, index }">
                <div v-if="!batchSetInvtoryVisable">
                    {{ row.prePackQty }}
                </div>
                <div v-else>
                    <el-input-number v-model="row.prePackQtyValue" :min="0" :max="9999" style="width:100%"
                        placeholder="已加工数量" :precision="0" :controls="false" @change="setPrePackQty_Time(row, index)" />
                </div>
            </template>
            <template v-slot:prePackTime="{ row, index }">
                <div v-if="!batchSetInvtoryVisable">
                    {{ row.prePackTime }}
                </div>
                <div v-else>
                    <el-date-picker v-model="row.prePackTimeValue" type="datetime" placeholder="加工完成时间"
                        style="width:100%" value-format="yyyy-MM-dd HH:mm:ss"
                        @change="setPrePackQty_Time(row, index)" />
                </div>
            </template>

        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog v-dialogDrag title="批量指定加工人员" :visible.sync="processingOrderVisable" width="15%">
            <div style="justify-content: center;">
                <YhUserelector :value.sync="processPerson.userId" maxlength="50" :text.sync="processPerson.userName"
                    style="width:100%;" placeholder="请输入加工人员" v-if=processingOrderVisable>
                </YhUserelector>
                <div class="btnGroup">
                    <el-button @click="processingOrderVisable = false">取消</el-button>
                    <el-button type="primary" @click="submitProcessing">确定</el-button>
                </div>
            </div>
        </el-dialog>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import dynamicSelecter from '@/components/customQuery/selecter.vue'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import YhUserelector from '@/components/YhCom/yh-userselector.vue'
import {
    manifestGetColumns,
    manifestPageGetData,
    manifestExportData,
    claimPackManifests,
    cancelPackManifests,
    assignPackManifests,
    setPackManifestsFinish
} from '@/api/vo/prePack'
import dayjs from 'dayjs'
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, YhUserelector, dynamicSelecter
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: null,//开始时间
                endDate: null,//结束时间
                prePackStartDate: null,//预包开始时间
                prePackEndDate: null,//预包结束时间
                combineCode: null,//组合码
                combineName: null,//组合名称    
                entityCode: null,//实体码
                batchNo: null,//批次号
                skus: null,//sku
                prePackWmsId: null,//预包id
                sendWmsId: null,//发货id
                consumableCode: null,//包装编码
                consumableName: null,//包装名称
                claimUserId: null,//认领人id
                prePackUserId: null,//预包人id
                status: null,//状态
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            isExport: false,
            ybTimeRanges: [],
            ids: [],
            processPerson: {
                userId: null,
                userName: null,
                ids: []
            },
            processingOrderVisable: false,
            setList: [],
            batchSetInvtoryVisable: false
        }
    },

    async mounted() {
        this.getCol()
        this.getList()
    },
    methods: {
        async setPrePackQty_Time(row, i) {
            var data = [{ id: row.id, prePackQty: row.prePackQtyValue, prePackTime: row.prePackTimeValue }]
            if (row.prePackQtyValue && row.prePackTimeValue) {
                const { success } = await setPackManifestsFinish(data)
                if (success) {
                    //为啥没有提示都没调
                    this.$message.success('设置成功')
                    row.prePackQty = row.prePackQtyValue
                    row.prePackTime = row.prePackTimeValue
                } else {
                    this.$message.error('设置失败')
                }
            }
        },
        close() {
            this.batchSetInvtoryVisable = false
        },
        async submitProcessing() {
            const { data, success } = await assignPackManifests(this.processPerson)
            if (success) {
                this.$message.success('指定成功')
                this.getList()
                this.processingOrderVisable = false
            } else {
                this.$message.error('指定失败')
            }
        },
        checkboxRangeEnd(row) {
            this.ids = row.map(item => item.id)
            this.setList = row
        },
        //批量领取加工计划
        async batchReceivePlan() {
            if (this.ids.length == 0) {
                this.$message.error('请选择数据')
                return
            }
            this.$confirm('是否确认领取', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await claimPackManifests({ ids: this.ids })
                if (success) {
                    this.$message.success('领取成功')
                    this.getList()
                } else {
                    this.$message.error('领取失败')
                }
            }).catch(() => {
                this.$message.info('已取消')
            })
        },
        //批量作废加工计划
        batchVoidPlan() {
            if (this.ids.length == 0) {
                this.$message.error('请选择数据')
                return
            }
            this.$confirm('是否确认作废', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await cancelPackManifests({ ids: this.ids })
                if (success) {
                    this.$message.success('作废成功')
                    this.getList()
                } else {
                    this.$message.error('作废失败')
                }
            }).catch(() => {
                this.$message.info('已取消')
            })
        },
        //批量指定加工人员
        batchDesignatePerson() {
            if (this.ids.length == 0) {
                this.$message.error('请选择数据')
                return
            }
            this.processPerson.ids = this.ids
            this.processingOrderVisable = true
        },
        //批量设置加工单
        batchSetInvtory() {
            this.batchSetInvtoryVisable = true
        },
        async getCol() {
            const { data, success } = await manifestGetColumns()
            if (success) {
                //在data顶部添加一列
                data.unshift({
                    label: '',
                    type: 'checkbox',
                })
                this.tableCols = data
            }
        },
        async changeTime(e, type) {
            this.ListInfo.startDate = (e && type == 'ks') ? dayjs(e[0]).format('YYYY-MM-DD') : null
            this.ListInfo.endDate = (e && type == 'ks') ? dayjs(e[1]).format('YYYY-MM-DD') : null
            this.ListInfo.prePackStartDate = (e && type == 'yb') ? dayjs(e[0]).format('YYYY-MM-DD') : null
            this.ListInfo.prePackEndDate = (e && type == 'yb') ? dayjs(e[1]).format('YYYY-MM-DD') : null
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await manifestExportData(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '加工计划' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data: { list, total, summary }, success } = await manifestPageGetData(this.ListInfo)
                if (success) {
                    this.tableData = list
                    this.total = total
                    this.summaryarry = summary
                } else {
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.$message.error('获取列表失败')
            } finally {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;

    .publicCss {
        width: 150px;
        margin: 0 5px 5px 0;
    }
}

.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 20px;

    .el-button {
        margin-left: 10px;
    }
}
</style>