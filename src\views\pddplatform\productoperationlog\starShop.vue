<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="timeList" type="daterange" align="right" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 240px"
                    @change="changeTime" :clearable="false">
                </el-date-picker>
                <el-select v-model="ListInfo.actModul" placeholder="模块选择" clearable style="width: 160px;">
                    <el-option v-for="item in actModulList" :key="item.label" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.prop" placeholder="属性" clearable style="width: 160px;">
                    <el-option v-for="item in propList" :key="item.label" :label="item.label" :value="item.label">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.propValueType" placeholder="类型" clearable style="width: 160px;">
                    <el-option v-for="item in propValueTypeList" :key="item.label" :label="item.label" :value="item.label">
                    </el-option>
                </el-select>
                <el-input v-model="ListInfo.shopName" placeholder="店铺" clearable maxlength="50"
                    style="width: 160px;"></el-input>
                <el-input v-model="ListInfo.proCode" placeholder="商品ID" clearable maxlength="50"
                    style="width: 160px;"></el-input>
                <el-input v-model="ListInfo.propValueString" placeholder="请输入值" clearable maxlength="50"
                    style="width: 160px;"></el-input>

                <el-select filterable v-model="ListInfo.groupId" placeholder="运营组长" style="width: 100px" clearable>
                    <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key" />
                </el-select>
                <el-select filterable v-model="ListInfo.operateSpecialId" placeholder="运营专员" clearable style="width: 100px">
                    <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
                </el-select>
                <el-select filterable v-model="ListInfo.user1Id" placeholder="运营助理" clearable style="width: 100px">
                    <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
                </el-select>

                <el-button type="primary" @click="getList('click')">搜索</el-button>
                <el-button type="primary" @click="exportProps">导出</el-button>
            </div>
        </template>
        <vxetablebase :id="'starShop202408041844'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            style="width: 100%; height: 690px; margin: 0" />
        <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="total"
            @page-change="detailPagechange" @size-change="detailSizechange" />
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from 'dayjs'
import { pickerOptions } from "@/utils/tools";
import { getProductOperationLogListAsync, importProCodeActionAsync,exportProductOperationLogListAsync } from '@/api/pddplatform/productoperationlog';
import { getDirectorList, getDirectorGroupList } from '@/api/operatemanage/base/shop'
const tableCols = [
    { istrue: true, prop: 'actTime', label: '操作时间', sortable: 'custom' },
    { istrue: true, prop: 'proCode', label: '商品ID', sortable: 'custom' },
    { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom' },
    { istrue: true, prop: 'groupId', label: '运营组', width: '100', sortable: 'custom', formatter: (row) => row.groupName || ' ' },
    { istrue: true, prop: 'operateSpecialUserId', label: '运营专员', width: '100', sortable: 'custom', formatter: (row) => row.operateSpecialUserName || ' ' },
    { istrue: true, prop: 'userId', label: '运营助理', width: '100', sortable: 'custom', formatter: (row) => row.userRealName || ' ' },
    { istrue: true, prop: 'actionor', label: '操作人', sortable: 'custom' },
    { istrue: true, prop: 'actModul', label: '操作模块', sortable: 'custom' },
    { istrue: true, prop: 'actType', label: '操作类型', sortable: 'custom' },
    { istrue: true, prop: 'prop', label: '属性', sortable: 'custom' },
    { istrue: true, prop: 'propValueType', label: '类型', sortable: 'custom' },
    { istrue: true, prop: 'propValueString', label: '值', sortable: 'custom' },
    { istrue: true, prop: 'actDetail', label: '详情', sortable: 'custom' },
    { istrue: true, prop: 'createdTime', label: '添加时间', sortable: 'custom' }
]

const actModulList = [
    {
        value: '计划相关',
        label: '计划相关',
    },
    {
        value: '单元相关',
        label: '单元相关',
    },
    {
        value: '创意相关',
        label: '创意相关',
    },
]

const propList = [
    {
        label: '投产比'
    },
    {
        label: '日限'
    },
    {
        label: '目标成交出价'
    },
]

const propValueTypeList = [
    {
        label: '升'
    },
    {
        label: '降'
    },
]
export default {
    name: "starShop",
    components: { MyContainer, vxetablebase },
    data() {
        return {
            tableCols,
            that: this,
            pickerOptions,
            actModulList,
            propList,
            propValueTypeList,
            tableData: [],
            timeList: null,
            ListInfo: {//列表参数
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                proCode: null,//商品ID
                shopCode: null,//店铺ID
                shopName: null,//店铺名称
                actModul: null,//操作模块
                startDate: null,//开始时间
                endDate: null,//结束时间
                logType: 5,//日志类型
                prop: null,//属性
                propValueType: null,//类型
                propValueString: null,//值
            },
            total: 0,
            directorList: [],
            directorGroupList: [],
        }
    },
    mounted() {
        this.getDirectorlist();
        this.getList()
    },
    methods: {
        async getDirectorlist() {
            const res1 = await getDirectorList({})
            const res2 = await getDirectorGroupList({})

            this.directorList = (res1.data || []);
            this.directorGroupList = (res2.data || []);
        },
        async exportProps() {
            const { data } = await exportProductOperationLogListAsync(this.ListInfo)
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '明星店铺' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        changeTime(e) {
            if (!e) {
                this.ListInfo.startDate = null
                this.ListInfo.endDate = null
            } else {
                this.ListInfo.startDate = dayjs(e[0]).format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs(e[1]).format('YYYY-MM-DD')
                this.timeList = [this.ListInfo.startDate, this.ListInfo.endDate]
            }
            this.getList()
        },
        async getList(type) {
            if (type == 'click') {
                this.ListInfo.currentPage = 1
                this.ListInfo.pageSize = 50
            }
            if (this.ListInfo.proCode) {
                this.ListInfo.proCode = this.ListInfo.proCode ? this.ListInfo.proCode.replace(/\s+/g, "") : null;
            }
            if (this.ListInfo.propValueString) {
                this.ListInfo.propValueString = this.ListInfo.propValueString ? this.ListInfo.propValueString.replace(/\s+/g, "") : null;
            }
            if (this.ListInfo.shopName) {
                this.ListInfo.shopName = this.ListInfo.shopName ? this.ListInfo.shopName.replace(/\s+/g, "") : null;
            }
            if (!this.timeList) {
                //默认时间为当前时间往前推一周
                this.ListInfo.startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
                this.timeList = [this.ListInfo.startDate, this.ListInfo.endDate]
            }
            const { data, success } = await getProductOperationLogListAsync(this.ListInfo)
            if (success) {
                let zz = this.directorGroupList;
                let zy = this.directorList;
                data.list.forEach(f => {
                    f.groupName = zz.find(x => x.key == f.groupId)?.value;
                    f.operateSpecialUserName = zy.find(x => x.key == f.operateSpecialUserId)?.value;
                    f.userRealName = zy.find(x => x.key == f.userId)?.value;
                });
                this.tableData = data.list
                this.total = data.total
            } else {
                //获取列表失败
                window.$message.error('获取列表失败')
            }
        },
        //页面数量改变
        detailSizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList();
        },
        //当前页改变
        detailPagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList();
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
}

.publicCss {
    width: 200px;
    margin-right: 10px;
}
</style>