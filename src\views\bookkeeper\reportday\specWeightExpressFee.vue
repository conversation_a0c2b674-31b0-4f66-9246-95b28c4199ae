<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-button type="primary" @click="onAddnewMethod">新增</el-button>
      </div>
    </template>
    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
      :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="120" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;align-items: center;">
              <el-button type="text" @click="onEditMethod(row)">编辑</el-button>
              <my-confirm-button  type="delete" :loading="row._loading" @click="onDelete($index, row)" />
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="editName" :visible.sync="editdialogVisible" width="600px" v-dialogDrag>
      <div style="height: 260px;width: 100%;" v-loading="editloading">
        <el-form :model="editform" ref="editform" :rules="editrules" label-width="150px" class="demo-ruleForm">
          <el-form-item label="最小重量>（KG）" prop="minWeight">
            <el-input v-model="editform.minWeight" style="width: 80%;"></el-input>
          </el-form-item>
          <el-form-item label="最大重量<=（KG）" prop="maxWeight">
            <el-input v-model="editform.maxWeight" style="width: 80%;"></el-input>
          </el-form-item>
          <el-form-item label="快递价格" prop="expressFee">
            <el-input v-model="editform.expressFee" style="width: 80%;"></el-input>
          </el-form-item>
          <el-form-item label="启用起始日期" prop="qyStartDate">
            <el-date-picker v-model="editform.qyStartDate" type="date" style="width: 80%;" :value-format="'yyyy-MM-dd'" />
          </el-form-item>
          <el-form-item label="启用结束日期" prop="qyEndDate">
            <el-date-picker v-model="editform.qyEndDate" type="date" style="width: 80%;" :value-format="'yyyy-MM-dd'" />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editdialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSaveMethod">确 定</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatTime } from "@/utils";
import { importSpecProCodeExpressFee, getSpecWeightExpressFee, editSpecWeightExpressFee ,delSpeWeightExpressFee,exportSpecProCode} from '@/api/bookkeeper/reportdayV2'
import MyConfirmButton from '@/components/my-confirm-button'
import inputYunhan from "@/components/Comm/inputYunhan";

const tableCols = [
  { sortable: 'custom', width: '300', align: 'center', prop: 'minWeight', label: '最小重量>（KG）', },
  { sortable: 'custom', width: '300', align: 'center', prop: 'maxWeight', label: '最大重量<=（KG）', },
  { sortable: 'custom', width: '300', align: 'center', prop: 'expressFee', label: '快递价格', },
  { sortable: 'custom', width: '300', align: 'center', prop: 'qyStartDate', label: '启用起始日期',formatter:(row)=> formatTime(row.qyStartDate,"YYYY-MM-DD")},
  { sortable: 'custom', width: '300', align: 'center', prop: 'qyEndDate', label: '启用结束日期',formatter:(row)=> formatTime(row.qyEndDate,"YYYY-MM-DD")},

]
export default {
  name: "particularlyExpressFee",
  components: {
    MyContainer, vxetablebase,MyConfirmButton,inputYunhan
  },
  data() {
    return {
      editloading: false,
      editform: {
        expressCompany: null,
        area: null,
        expressFee: null,
        id: 0,
      },
      editdialogVisible: false,
      editName: '新增',
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        proCode: null,
      },
      editrules: {
        minWeight: [{ required: true, message: '请输入最小重量', trigger: 'blur' }],
        maxWeight: [{ required: true, message: '请输入最大重量', trigger: 'blur' }],
        expressFee: [{ required: true, message: '请输入快递价格', trigger: 'blur' }],
        qyStartDate: [{ required: true, message: '请输入启用起始日期', trigger: 'blur' }],
        qyEndDate: [{ required: true, message: '请输入启用结束日期', trigger: 'blur' }],
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    mailNumberCallback(val) {
      this.ListInfo.proCode = val
    },
    async onSaveMethod() {
      // if (!this.editform.proCode || !this.editform.expressFee || !this.editform.qyDate) {
      //   this.$message({ message: "请填写完整信息", type: "warning" });
      //   return false;
      // }
      this.editloading = true
      var res = await editSpecWeightExpressFee(this.editform)
      this.editloading = false
      if (res?.success) {
        this.$message({ message: "保存成功", type: "success" });
        this.editdialogVisible = false
        await this.getList()
      }
    },
    onAddnewMethod() {
      this.setEditForm('新增', {
        expressCompany: null,
        area: null,
        expressFee: null,
        id: 0
      });
    },
    onEditMethod(row) {
      this.setEditForm('编辑', row);
    },
    async onDelete(index, row) {
        row._loading = true
        const para = { id: row.id }
        const res = await delSpeWeightExpressFee(para)
  
        row._loading = false
  
        if (!res?.success) {
          return
        }
        this.$message({
          message: this.$t('admin.deleteOk'),
          type: 'success'
        })
        await  this.getList()
      },
    setEditForm(editName, formData) {
      this.editName = editName;
      this.editform = {
        minWeight: formData.minWeight,
        maxWeight: formData.maxWeight,
        expressFee: formData.expressFee,
        qyStartDate: formData.qyStartDate,
        qyEndDate: formData.qyEndDate,
        id: formData.id
      };
      this.editdialogVisible = true;
    },
            //导出
            async onexport() {
            this.isExport = true
         

            await exportSpecProCode(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '特殊ID' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importSpecProCodeExpressFee(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getSpecWeightExpressFee(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 5px;
  }
}
</style>
