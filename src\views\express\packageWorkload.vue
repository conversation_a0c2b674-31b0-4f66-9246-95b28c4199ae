<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker
          v-model="timeRanges"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="publicCss"
          style="width: 350px"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          @change="changeTime"
          :picker-options="pickerOptions"
        />
        <el-input v-model="ListInfo.packagePersonName" placeholder="集包人员名称" clearable class="publicCss" />
        <div>
          <el-button type="primary" @click="getList('search')">查询</el-button>
          <el-button type="warning" @click="clearFilters" style="margin-left: 10px;">清空</el-button>
          <el-button type="success" @click="exportData" style="margin-left: 10px;">导出</el-button> <!-- 添加导出按钮 -->
        </div>
      </div>
    </template>
    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
                  @sortchange='sortchange' :summaryarry='summaryarry' :showsummary='true' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
                  :isSelectColumn="false" style="width: 100%;  margin: 0; " :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {pickerOptions} from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { packageWorkloadPage, packageWorkloadExport } from '@/api/order/orderData' // 添加导入导出接口
import { formatTime, } from "@/utils";

const tableCols = [
  {
    sortable: 'custom',
    width: '180',
    align: 'center',
    prop: 'date',
    label: '日期',
  },
  {sortable: 'custom', width: 'auto', align: 'center', prop: 'packagePersonName', label: '集包人员名称',},
  {sortable: 'custom', width: 'auto', align: 'center', prop: 'machineCount', label: '机台数',},
  {sortable: 'custom', width: 'auto', align: 'center', prop: 'packageCount', label: '包裹数量',},
  {sortable: 'custom', width: 'auto', align: 'center', prop: 'packageWeightCount', label: '包裹总重量(kg)',},
  { width: 'auto', align: 'center', prop: 'lastWeight', label: '包裹旧重量(kg)',},

]
export default {
  name: "packageWorkload",
  components: {
    MyContainer, vxetablebase, dateRange
  },
  data() {
    return {
      that: this,
      summaryarry: {},
      timeRanges: [],
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        packageTime: null,
        packagePersonName: null,
      },
      tableCols,
      tableData: [],
      total: 0,
      loading: true,
      pickerOptions,
    }
  },
  async mounted() {
    // 设置默认时间范围为近7天
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
    this.timeRanges = [start, end];
    this.ListInfo.startTime = dayjs(start).format('YYYY-MM-DD');
    this.ListInfo.endTime = dayjs(end).format('YYYY-MM-DD');
    await this.getList()
  },
  methods: {
    async changeTime(e) {
      if (e && e.length === 2) {
        this.ListInfo.startTime = dayjs(e[0]).format('YYYY-MM-DD');
        // 设置结束时间为当天的 23:59:59
        this.ListInfo.endTime = dayjs(e[1]).format('YYYY-MM-DD');
      } else {
        this.ListInfo.startTime = null;
        this.ListInfo.endTime = null;
      }
      await this.getList();
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1;
        this.$refs.pager.setPage(1);
      }

      this.loading = true;
      try {
        const res = await packageWorkloadPage(this.ListInfo);
        if (res?.success) {
          var data = res.data;
          this.tableData = data.list;
          this.total = Number(data.total);
          this.summaryarry = data.summary
          this.loading = false;
        } else {
          this.loading = false;
          this.$message.error('获取列表失败');
        }
      } catch (error) {
        this.loading = false;
      }
    },
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({order, prop}) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    clearFilters() {
      this.timeRanges = []
      this.ListInfo.packageTime = null;
      this.ListInfo.packageStartTime = null;
      this.ListInfo.startTime = null;
      this.ListInfo.packageEndTime = null;
      this.ListInfo.endTime = null;
      this.ListInfo.packagePersonName = null;
      this.getList();
    },
    validateInputLength(value, maxLength) {
      if (value && value.length > maxLength) {
        this.$message.error(`输入内容不能超过 ${maxLength} 个字符`);
        return value.slice(0, maxLength);
      }
      return value;
    },
    async exportData() {
      try {
        const data = await packageWorkloadExport(this.ListInfo);
        if (data) {
          // 处理导出逻辑，例如下载文件
          const url = window.URL.createObjectURL(new Blob([data]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', '集包工作量' + new Date().toLocaleString() + '.xlsx'); // 文件名
          document.body.appendChild(link);
          link.click();
        } else {
          this.$message.error('导出失败');
        }
      } catch (error) {
        this.$message.error('导出失败');
      }
    },
  },
  watch: {
    'ListInfo.packagePersonName': function (newVal) {
      this.ListInfo.packagePersonName = this.validateInputLength(newVal, 50);
    }
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 5px;

  .publicCss {
    width: 200px;
    margin: 0 5px 5px 0px;
  }
}
</style>
