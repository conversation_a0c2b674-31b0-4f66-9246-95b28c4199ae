<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <div class="publicCss" style="width: 165px;">
          <inputYunhan ref="productorderNos" :inputt.sync="ListInfo.orderNos" v-model="ListInfo.orderNos" width="165px"
            placeholder="线上订单号/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100" :maxlength="2000"
            @callback="callbackGoodsCode($event, 'orderNos')" title="线上订单号">
          </inputYunhan>
        </div>
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="确认开始日期" end-placeholder="确认结束日期" :picker-options="pickerOptions"
          style="width: 225px;margin-right: 3px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-select v-model="ListInfo.platform" clearable placeholder="平台" @change="onchangeplatform"
          style="width: 90px">
          <el-option v-for="item in platformlist" :key="item.label" :label="item.label" :value="item.value" />
        </el-select>
        <el-select class="publicCss" filterable clearable v-model="ListInfo.shopCodes" placeholder="店铺"
          @change="zrType2change()" multiple collapse-tags style="width: 165px">
          <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
            :value="item.shopCode"></el-option>
        </el-select>
        <el-select v-model="ListInfo.zrType2" placeholder="问题类型" class="publicCss" clearable filterable multiple
          collapse-tags>
          <el-option v-for="item in problemList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-input v-model.trim="ListInfo.zrUserName" placeholder="责任人" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.damagedReason" placeholder="具体原因" maxlength="50" clearable class="publicCss" />
        <yhWmsSelect :checkboxOrRadio="'checkbox'" :placeholderText="'发货仓'" style="margin-right: 3px;"
          @onChange="(v) => { ListInfo.sendWareHouseIds = v.checkedWarehouses; }">
        </yhWmsSelect>
        <div class="publicCss" style="width: 165px;">
          <inputYunhan ref="productgoodsCodes" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
            width="165px" placeholder="商品编码/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'goodsCodes')" title="商品编码">
          </inputYunhan>
        </div>
        <div class="publicCss" style="width: 165px;">
          <inputYunhan ref="productstyleCodes" :inputt.sync="ListInfo.styleCodes" v-model="ListInfo.styleCodes"
            width="165px" placeholder="款式编码/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'styleCodes')" title="款式编码">
          </inputYunhan>
        </div>
        <div class="publicCss" style="width: 165px;">
          <inputYunhan ref="productexpressOrders" :inputt.sync="ListInfo.expressOrders" v-model="ListInfo.expressOrders"
            width="165px" placeholder="快递单号/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'expressOrders')" title="快递单号">
          </inputYunhan>
        </div>
        <el-input v-model.trim="ListInfo.expressCompany" placeholder="快递公司" maxlength="50" clearable
          class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
      </div>
    </template>
    <vxetablebase :id="'chatRecordCheck20250417'" :tablekey="'chatRecordCheck20250417'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0"
      :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="聊天记录" v-if="dialogChatRecordHisVisible" :visible.sync="dialogChatRecordHisVisible" width="40%"
      height="600px" v-dialogDrag>
      <chartComponent ref="chartRef" :isShow="true" :api="getDamagedOrderChatRerordByOrderNo"></chartComponent>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist } from '@/utils/tools'
import { getList as getshopListt } from '@/api/operatemanage/base/shop';
import dayjs from 'dayjs'
import inputYunhan from "@/components/Comm/inputYunhan";
import yhWmsSelect from "@/components/YhCom/yhWmsSelect";
import { getDamagedOrderChatList, getDamagedOrderChatRerordByOrderNo, GetAllDamagedOrdersZrType } from '@/api/customerservice/DamagedOrders'
import chartComponent from "@/views/order/SeriesIllegalChatComponent.vue";
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '线上订单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'afterSaleApproveDate', label: '确认日期', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'zrType2', label: '问题类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'zrUserName', label: '责任人', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'damagedReason', label: '具体原因', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'sendWareHouse', label: '发货仓', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'styleCode', label: '款式编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'expressOrder', label: '快递单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'expressCompany', label: '快递公司', },
  {
    istrue: true, label: '聊天记录', fixed: 'right', type: 'button', width: '70', btnList: [
      {
        label: '查看',
        handle: (that, row) => that.showChatRecord(row),
        ishide: (that, row) => { return !row.isChatMsg; }
      }
    ]
  }
]
export default {
  name: "chatRecordCheck",
  components: {
    MyContainer, vxetablebase, inputYunhan, yhWmsSelect, chartComponent
  },
  data() {
    return {
      getDamagedOrderChatRerordByOrderNo,
      dialogChatRecordHisVisible: false,
      platformlist,//平台列表
      shopList: [],//店铺列表
      problemList: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        startDate: null,//开始时间
        endDate: null,//结束时间
        orderNos: null,//线上订单号
        goodsCodes: null,//商品编码
        styleCodes: null,//款式编码
        expressOrders: null,//快递单号
        shopCodes: null,//店铺
        sendWareHouseIds: [],//发货仓
        expressCompany: null,//快递公司
        zrType2: [],//问题类型
        zrUserName: null,//责任人
        damagedReason: null,//具体原因
        platform: null,//平台
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
    const { data } = await GetAllDamagedOrdersZrType()
    this.problemList = data || []
  },
  methods: {
    async showChatRecord(row) {
      this.dialogChatRecordHisVisible = true;
      this.$nextTick(() => {
        this.$refs.chartRef.dataJson = row.orderNo;
      });
    },
    zrType2change() {
      this.$forceUpdate();
    },
    async onchangeplatform(val) {
      this.ListInfo.shopCodes = null
      const res1 = await getshopListt({ platform: val, CurrentPage: 1, PageSize: 100000 });
      this.shopList = res1.data.list
    },
    callbackGoodsCode(val, type) {
      const map = {
        orderNos: () => (this.ListInfo.orderNos = val),
        goodsCodes: () => (this.ListInfo.goodsCodes = val),
        styleCodes: () => (this.ListInfo.styleCodes = val),
        expressOrders: () => (this.ListInfo.expressOrders = val),
      };
      map[type]?.();
    },
    async changeTime(e) {
      this.ListInfo.startDate = e ? e[0] : null
      this.ListInfo.endDate = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        let time = dayjs().format('YYYY-MM-DD')
        this.ListInfo.startDate = time
        this.ListInfo.endDate = time
        this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
      }
      let shopCodeStr = this.ListInfo.shopCodes
      if (shopCodeStr && shopCodeStr.length > 0) {
        shopCodeStr = shopCodeStr.join(',')
      } else {
        shopCodeStr = null
      }
      let params = {
        ...this.ListInfo,
        shopCodes: shopCodeStr,
      }
      this.loading = true
      const { data, success } = await getDamagedOrderChatList(params)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
  flex-wrap: wrap;

  .publicCss {
    width: 180px;
    margin-right: 3px;
  }
}

::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>
