<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker
          v-if="billingType == 5 || billingType == 1 || billingType == 2 || billingType == 3 || billingType == 4"
          v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-input
          v-if="billingType == 5 || billingType == 1 || billingType == 2 || billingType == 3 || billingType == 4"
          v-model.trim="ListInfo.orderNo" placeholder="订单编号" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>

        <el-dropdown v-show="billingType !== 5" style="box-sizing: border-box; margin-left:6px;" size="mini"
          split-button @click="startImport" type="primary" icon="el-icon-share" @command="handleCommand">
          导入
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a">
              下载模版
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button v-show="billingType == 5" type="primary" style="box-sizing: border-box; margin-left:6px;" size="mini"
          @click="startImport">导入</el-button>
        <el-button type="primary" @click="onExport" style="margin-left: 5px;" v-if="checkPermission('settlementData_kj_export')">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'halfPalletTemu202408041406'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0"
      :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="display: flex; align-items: baseline; height: 75px;margin-top: 10px;">
        <el-date-picker style="width: 150px; margin-right: 10px;" v-model="yearMonthDay" type="date" placeholder="选择日期"
          :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
        </el-date-picker>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import { pageSettleAccount_TemuAsync, importSettleAccount_TemuAsync, pageHalfSalesDetails_RefundAfterSale_TemuAsync, pageHalfSalesDetails_FreightRevenue_TemuAsync, pageHalfSalesDetails_FreightRefund_TemuAsync, pageTransportationCost_TemuAsync, importTransportationCost_TemuAsync } from '@/api/bookkeeper/reportdayV2'
import dayjs from 'dayjs'
import {settleAccount_Temu_Export,halfSalesDetails_RefundAfterSale_Temu_Export,halfSalesDetails_FreightRevenue_Temu_Export,halfSalesDetails_FreightRefund_Temu_Export,transportationCost_Temu_Export } from '@/api/bookkeeper/crossBorderV2'

//TEMU-半托交易收入
const tableCols1 = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopCode', label: '店铺ID', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '订单编号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'subOrderNo', label: '子订单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'skuDetails', label: 'SKU货号_SKU名称_', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'transactionIncome', label: '交易收入', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'coupon', label: '优惠券', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'currency', label: '币种', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'accountingTime', label: '账务时间', },
]
//TEMU-半托售后退款
const tableCols2 = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopCode', label: '店铺ID', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'afterOrderNo', label: '售后单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '订单编号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'subOrderNo', label: '子订单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'skuDetails', label: 'SKU明细', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'refundAmountAfterSale', label: '售后退款金额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'coupon', label: '单品券售后退款金额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'afterSalesReason', label: '售后原因', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'currency', label: '币种', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'accountingTime', label: '账务时间', },
]
//TEMU-半托运费收入
const tableCols3 = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopCode', label: '店铺ID', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '订单编号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'freightRevenue', label: '运费收入', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'currency', label: '币种', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'accountingTime', label: '账务时间', },
]
//TEMU-半托运费退款
const tableCols4 = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopCode', label: '店铺ID', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '订单编号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'freightRefund', label: '运费退款', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'currency', label: '币种', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'accountingTime', label: '账务时间', },
]
//TEMU-半托运输费用
const tableCols5 = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'violationType', label: '海外仓', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'oddNumber', label: '单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '订单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'dataDayTime', label: '时间', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'expressOrderNo', label: '主运单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'currency', label: '币种', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'expressFee', label: '运输费', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'operatingCost', label: '操作费', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouseFee', label: '仓储费', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'inWarehousingOnLinFee', label: '入库上架费用', },
]

export default {
  name: "halfPalletTemu",
  props: {
    billingType: {
      type: Number,
      default: 1
    }
  },
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      tableCols1,
      tableCols2,
      tableCols3,
      tableCols4,
      tableCols5,
      yearMonthDay: null,//日期
      dialogVisible: false,//导入弹窗
      fileList: [],//上传文件列表
      uploadLoading: false,//上传按钮loading
      fileparm: {},//上传文件参数
      timeRanges: [],//时间范围
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        startDate: null,//开始时间
        endDate: null,//结束时间
        orderNo: null,//线上单号
      },
      tableCols: [],
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() { 
    await this.init()
  },
  methods: {
    initialize() {
      this.ListInfo = {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        startDate: null,//开始时间
        endDate: null,//结束时间
        orderNo: null,//线上单号
      }
      this.tableData = []
    },
    async init() {
      this.initialize()
      if (this.billingType == 1) {
        this.tableCols = tableCols1//TEMU-半托交易收入
      } else if (this.billingType == 2) {
        this.tableCols = tableCols2//TEMU-半托售后退款
      } else if (this.billingType == 3) {
        this.tableCols = tableCols3//TEMU-半托运费收入
      } else if (this.billingType == 4) {
        this.tableCols = tableCols4//TEMU-半托运费退款
      } else if (this.billingType == 5) {
        this.tableCols = tableCols5//TEMU-半托运输费用
      }
      await this.getList()
    },
    async changeTime(e) {
      this.ListInfo.startDate = e ? e[0] : null
      this.ListInfo.endDate = e ? e[1] : null
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("yearMonthDay", dayjs(this.yearMonthDay).format('YYYYMMDD'));
      if (this.billingType == 5) {
        var res = await importTransportationCost_TemuAsync(form);
      } else {
        form.append("billingType", this.billingType);
        var res = await importSettleAccount_TemuAsync(form);
      }
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (!this.yearMonthDay) {
        this.$message({ message: "请选择日期", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.yearMonthDay = null
      this.dialogVisible = true;
    },
    async getList(type) {

      console.log("1", this.timeRanges)
      if (this.timeRanges) {
        this.ListInfo.startDate = this.timeRanges[0]
        this.ListInfo.endDate = this.timeRanges[1]
      }

      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const billingTypeFunctions = {
        1: pageSettleAccount_TemuAsync,//TEMU-半托交易收入
        2: pageHalfSalesDetails_RefundAfterSale_TemuAsync,//TEMU-半托售后退款
        3: pageHalfSalesDetails_FreightRevenue_TemuAsync,//TEMU-半托运费收入
        4: pageHalfSalesDetails_FreightRefund_TemuAsync,//TEMU-半托运费退款
        5: pageTransportationCost_TemuAsync,//TEMU-半托运输费用
      };
      const functionToCall = billingTypeFunctions[this.billingType];
      if (functionToCall) {
        const { data, success } = await functionToCall(this.ListInfo);
        this.loading = false;
        if (success) {
          this.tableData = data.list ? data.list : [];
          this.total = data.total ? data.total : 0;
          this.summaryarry = data.summary ? data.summary : {};
          if (this.billingType == 5) {
            this.tableData.forEach(item => {
              item.dataDayTime = dayjs(item.dataDayTime).format('YYYY-MM-DD')
            })
          }
        } else {
          this.$message.error('获取列表失败');
        }
      } else {
        this.$message.error('无效的账单类型');
        this.loading = false;
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async handleCommand(command) {
      switch (command) {
        //下载模版
        case 'a':
          await this.downLoadFile()
          break;
      }
    },
    async downLoadFile() {
      if (this.billingType == 1) {
        //TEMU-半托交易收入
        window.open("/static/excel/KjSettlementData/TEMU-半托交易收入.xlsx", "_blank");
      } else if (this.billingType == 2) {
        //TEMU-半托售后退款
        window.open("/static/excel/KjSettlementData/TEMU-半托售后退款.xlsx", "_blank");
      } else if (this.billingType == 3) {
        //TEMU-半托运费收入
        window.open("/static/excel/KjSettlementData/TEMU-半托运费收入.xlsx", "_blank");
      } else if (this.billingType == 4) {
        //TEMU-半托运费退款
        window.open("/static/excel/KjSettlementData/TEMU-半托运费退款.xlsx", "_blank");
      } else if (this.billingType == 5) {
        //TEMU-半托运输费用
      }

    },
    async onExport() {//导出列表数据；
      if (this.billingType == 1) {
        //TEMU-半托交易收入
        var res = await settleAccount_Temu_Export(this.ListInfo);
        if (res?.success) {
          this.$message({ message: res.msg, type: "success" });
        }
      } else if (this.billingType == 2) {
        //TEMU-半托售后退款
        var res = await halfSalesDetails_RefundAfterSale_Temu_Export(this.ListInfo);
        if (res?.success) {
          this.$message({ message: res.msg, type: "success" });
        }
      } else if (this.billingType == 3) {
        //TEMU-半托运费收入
        var res = await halfSalesDetails_FreightRevenue_Temu_Export(this.ListInfo);
        if (res?.success) {
          this.$message({ message: res.msg, type: "success" });
        }
      } else if (this.billingType == 4) {
        var res = await halfSalesDetails_FreightRefund_Temu_Export(this.ListInfo);
        if (res?.success) {
          this.$message({ message: res.msg, type: "success" });
        }
      } else if (this.billingType == 5) {
        var res = await transportationCost_Temu_Export(this.ListInfo);
        if (res?.success) {
          this.$message({ message: res.msg, type: "success" });
        }
      }

    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
