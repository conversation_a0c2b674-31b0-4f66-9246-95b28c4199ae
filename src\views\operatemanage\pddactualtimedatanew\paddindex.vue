<template>
  <div class="allbody">
    <div class="heard">
      <pddheard @search="getlist" :rows="rows"></pddheard>
    </div>
    <div class="content">
      <pddtablediy @search="getlist" :tableloading="tableloading" :rowschange.sync="rows" :alllist="alllist"></pddtablediy>
    </div>
    <div class="bottom">
      <my-pagination :sizes="[50, 100, 200, 300, 800, 1000, 2000]" :page-size="200" ref="pager" :total="total"
        :checked-count="sels.length" @get-page="getlist"  @page-change="pagechange" @size-change="sizechange" />
    </div>
  </div>
</template>

<script>
import pddtablediy from "@/views/operatemanage/pddactualtimedatanew/pddtablediy.vue"
import pddheard from "@/views/operatemanage/pddactualtimedatanew/pddheard.vue"
import { getPddActualTimeDataByProAsync } from '@/api/operatemanage/datapdd/actualtimedatapdd.js';

export default {
  name: 'Vue2demoPaddindex',
  components: { pddtablediy, pddheard },
  data() {
    return {
      total: 1,
      sels: [],
      pagelist: {
        currentPage: 1,
        pageSize: 200,
      },
      tableloading: true,
      alllist: {

      },
      rows: []
    };
  },

  mounted() {
    this.getlist();//获取主页数据
  },

  methods: {
    sizechange(val){
        this.pagelist.currentPage = 1;
        this.pagelist.pageSize = val;
        this.onRefresh();
    },
    pagechange(val){
        this.pagelist.currentPage = val;
        this.getlist();
    },
    // selsChange: function (sels) {
    //   this.sels = sels
    // },
    async getlist(data) {
      this.tableloading = true;
      let params = data?{
        ...this.pagelist,
        ...data,
        startTime: data.searchtime?data.searchtime[0]:null,
        endTime: data.searchtime?data.searchtime[1]:null,
      }:{
        ...this.pagelist,
      }
      let res = await getPddActualTimeDataByProAsync(params);
      if (!res.success) {
        return
      }
      this.total = res.data.total;
      this.alllist = res.data;
      this.tableloading = false;

    }
  },
};
</script>

<style lang="scss" scoped>
.allbody {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.heard {
  height: 80px;
  width: 100%;
  background: #eee;
}

.bottom {
  padding: 10px 0;
  height: 60px;
  width: 100%;
}

.content {
  height: calc(100% - 140px);
  width: 100%;
}</style>