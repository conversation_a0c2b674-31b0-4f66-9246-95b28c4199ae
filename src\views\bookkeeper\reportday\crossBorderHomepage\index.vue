<template>
    <MyContainer>
        <el-tabs v-model="current" style="height: 95%;">
            <el-tab-pane label="跨境运营公告" name="tab1" style="height: 98%;"
                v-if="checkPermission(['bookkeeper:reportday:crossBorderHomepage:tab:CrossborderAnnouncement'])">
                <CrossborderAnnouncement style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="运营人员业绩" name="tab2" :lazy="true" style="height: 98%;"
                v-if="checkPermission(['bookkeeper:reportday:crossBorderHomepage:tab:StaffPerformance'])">
                <StaffPerformance style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="店铺违规情况" name="tab3" :lazy="true" style="height: 98%;"
                v-if="checkPermission(['bookkeeper:reportday:crossBorderHomepage:tab:ShopViolationSituation'])">
                <ShopViolationSituation style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="系列编码盈亏" name="tab4" :lazy="true" style="height: 98%;"
                v-if="checkPermission(['bookkeeper:reportday:crossBorderHomepage:tab:TitleMode'])">
                <TitleMode style="height: 100%;" />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>
<script>
import MyContainer from "@/components/my-container";
import CrossborderAnnouncement from './tab/CrossborderAnnouncement.vue'
import StaffPerformance from './tab/StaffPerformance.vue'
import ShopViolationSituation from './tab/ShopViolationSituation.vue'
import TitleMode from './tab/TitleMode.vue'
export default {
    components: {
        MyContainer, CrossborderAnnouncement, StaffPerformance, ShopViolationSituation, TitleMode

    },
    data() {
        return {
            current: 'tab1'
        };
    },
    methods: {

    }
};
</script>

<style lang="scss" scoped></style>