<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <!-- <el-button-group>
           <el-button style="padding: 0;">
              <el-select filterable v-model="filter.version" placeholder="类型" style="width: 130px">
                <el-option label="工资月报" value="v1"></el-option>
                <el-option label="参考月报" value="v2"></el-option>
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-date-picker style="width: 120px" v-model="filter.yearMonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="核算月份"></el-date-picker>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model="filter.proCode" placeholder="商品ID" style="width:120px;"/>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
               <el-input v-model="filter.ProductName" placeholder="商品名称" style="width:160px;"/>
            </el-button>
             <el-button style="padding: 0;margin: 0;">
              <el-select filterable v-model="filter.platform" placeholder="平台" style="width:120px;">
                <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </el-button>
            <el-button style="padding: 0;">
              <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺">
                <el-option v-for="item in shopList" :key="item.shopCode"  :label="item.shopName" :value="item.shopCode"></el-option>
              </el-select>
            </el-button>
            <el-button style="padding: 0;">
              <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组" style="width: 90px">
                <el-option key="无运营组" label="无运营组" :value="0"></el-option>
                <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </el-button>
         <el-button type="primary" @click="onSearch">查询</el-button>
         <el-button type="primary" @click="onExport">导出</el-button>
      </el-button-group> -->

            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="年月:">
                    <el-date-picker style="width: 110px" v-model="filter.yearMonth" type="month" format="yyyyMM"
                        value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
                </el-form-item>
                <el-form-item label="平台:">
                    <el-select filterable v-model="filter.platform" placeholder="请选择平台" @change="onchangeplatform"
                        clearable style="width:70px">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="所属店铺:" label-position="right">
                    <el-select v-if="shopCheck" filterable clearable v-model="filter.shopCode" placeholder="所属店铺"
                        style="width: 130px">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode"></el-option>
                    </el-select>
                    <el-input v-else v-model.trim="filter.shopCode" placeholder="所属店铺" maxlength="50" clearable
                        style="width: 130px" />
                </el-form-item>
                <el-form-item label="订单编号:" label-position="right">
                    <el-input v-model="filter.serialNumberOrder" placeholder="商品订单编号" style="width:133px;" />
                </el-form-item>
                <el-form-item label="商品Id:" label-position="right">
                    <el-input v-model="filter.proCode" placeholder="商品Id" style="width:133px;" />
                </el-form-item>
                <el-form-item label="商品编码:" label-position="right">
                    <el-input v-model="filter.goodsCode" placeholder="商品Id" style="width:133px;" />
                </el-form-item>
                <el-form-item label="空白ID:" label-position="right">
                    <el-select filterable clearable v-model="filter.isNullProCode" placeholder="请选择"
                        style="width:80px;">
                        <el-option label="空白ID" :value="true"></el-option>
                        <el-option label="非空白ID" :value="false"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="类型" label-position="right">
                    <el-select filterable clearable v-model="filter.OrderType" placeholder="请选择" style="width:80px;">
                        <el-option label="非补发/换货单" :value="1"></el-option>
                        <el-option label="补发" :value="2"></el-option>
                        <el-option label="换货" :value="3"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="状态" label-position="right">
                    <el-select filterable clearable v-model="filter.Status" placeholder="请选择" style="width:80px;">
                        <el-option label="非取消" :value="1"></el-option>
                        <el-option label="已取消" :value="2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="是否特殊" label-position="right">
                    <el-select filterable clearable v-model="filter.IsSpecial" placeholder="请选择" style="width:80px;">
                        <el-option label="是" :value="true"></el-option>
                        <el-option label="否" :value="false"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="是否赠品" label-position="right">
                    <el-select filterable clearable v-model="filter.isGift" placeholder="请选择" style="width:80px;">
                        <el-option label="是" :value="true"></el-option>
                        <el-option label="否" :value="false"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="销售金额:">
                    <el-row style="width: 200px">
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                            <el-input type="number" placeholder="最小值" v-model="filter.saleAmontMin" />
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                            <el-input type="number" placeholder="最大值" v-model="filter.saleAmontMax" />
                        </el-col>
                    </el-row>
                </el-form-item>
                <el-form-item label="分组" label-position="right">
                    <el-select filterable clearable v-model="filter.QueryType" placeholder="请选择" style="width:80px;">
                        <el-option label="订单分组" :value="1"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="订单类型" label-position="right">
                    <el-select filterable clearable v-model="filter.typeOrder" placeholder="请选择" style="width:100px;">
                        <el-option label="补发订单" value="补发订单"></el-option>
                        <el-option label="普通订单" value="普通订单"></el-option>
                        <el-option label="分销Plus" value="分销Plus"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='ZTCKeyWordList' :showsummary='true' :summaryarry='summaryarry' @select='selectchange'
            :isSelection='false' :tableCols='tableCols' :loading="listLoading" :tableHandles='tableHandles'
            :isSelectColumn='true'>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
        <el-dialog title="导出店铺汇总" v-if="dialogShopSumVisible" :visible.sync="dialogShopSumVisible" width="50%"
            :close-on-click-modal="false" height="600px" v-dialogDrag>
            <el-row>
                <el-button type="primary" @click="onShopGatherExport(1)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总1-50</el-button>
                <el-button type="primary" @click="onShopGatherExport(2)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总51-100</el-button>
                <el-button type="primary" @click="onShopGatherExport(3)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总101-150</el-button>
                <el-button type="primary" @click="onShopGatherExport(4)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总151-200</el-button>
                <el-button type="primary" @click="onShopGatherExport(5)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总201-250</el-button>
            </el-row>
            <el-row style="padding-top: 5px;">
                <el-button type="primary" @click="onShopGatherExport(6)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总251-300</el-button>
                <el-button type="primary" @click="onShopGatherExport(7)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总301-350</el-button>
                <el-button type="primary" @click="onShopGatherExport(8)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总351-400</el-button>
                <el-button type="primary" @click="onShopGatherExport(9)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总401-450</el-button>
                <el-button type="primary" @click="onShopGatherExport(10)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总451-500</el-button>
            </el-row>
            <el-row style="padding-top: 5px;">
                <el-button type="primary" @click="onShopGatherExport(11)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总501-550</el-button>
                <el-button type="primary" @click="onShopGatherExport(12)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总551-600</el-button>
                <el-button type="primary" @click="onShopGatherExport(13)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总601-650</el-button>
                <el-button type="primary" @click="onShopGatherExport(14)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总651-700</el-button>
                <el-button type="primary" @click="onShopGatherExport(15)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总701-750</el-button>
            </el-row>
            <el-row style="padding-top: 5px;">
                <el-button type="primary" @click="onShopGatherExport(16)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总751-800</el-button>
                <el-button type="primary" @click="onShopGatherExport(17)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总801-850</el-button>
                <el-button type="primary" @click="onShopGatherExport(18)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总851-900</el-button>
                <el-button type="primary" @click="onShopGatherExport(19)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总901-950</el-button>
                <el-button type="primary" @click="onShopGatherExport(20)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总951-1000</el-button>
            </el-row>
            <el-row style="padding-top: 5px;">
                <el-button type="primary" @click="onShopGatherExport(21)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总1001-1050</el-button>
                <el-button type="primary" @click="onShopGatherExport(22)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总1051-1100</el-button>
                <el-button type="primary" @click="onShopGatherExport(23)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总1101-1150</el-button>
                <el-button type="primary" @click="onShopGatherExport(24)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总1151-1200</el-button>
                <el-button type="primary" @click="onShopGatherExport(25)" style="width: 150px;"
                    :loading="dialogShopSumLoading">导出店铺汇总1201-1250</el-button>
            </el-row>

        </el-dialog>
        <el-dialog title="同步空宝贝ID" v-if="dialogProCodeVisible" :visible.sync="dialogProCodeVisible" width="20%"
            :close-on-click-modal="false" height="600px" v-dialogDrag>
            <span>
                <el-date-picker style="width: 120px" v-model="dialogProCodeYearMonth" type="month" format="yyyyMM"
                    value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
                <el-select filterable v-model="dialogProCodePlatform" placeholder="请选择平台" clearable style="width:120px">
                    <el-option label="天猫" :value="1" />
                    <el-option label="拼多多" :value="2" />
                    <el-option label="阿里巴巴" :value="4" />
                    <el-option label="抖音" :value="6" />
                    <el-option label="京东" :value="7" />
                    <el-option label="淘工厂" :value="8" />
                    <el-option label="淘宝" :value="9" />
                    <el-option label="快手" :value="14" />
                    <el-option label="视频号" :value="20" />
                    <el-option label="小红书" :value="21" />
                </el-select>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogProCodeVisible = false">关闭</el-button>
                <el-button @click="onSyncProCodeSave" type="primary" :loading="dialogProCodeLoading">确定</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList, getList as getshopList } from '@/api/operatemanage/base/shop';
import { formatPlatform, formatLink, platformlist } from "@/utils/tools";
import {
    getSaleThemeAnalysisPageList as getPageList, exportGatherSaleThemeAnalysisList, ExportGatherSaleThemeAnalysisList2,
    getFenXiaoSaleThemeAnalysisPageList, SyncSaleThemeAnalysisEmptyProCode, SyncAlbbYanXuanOrders, ExportSaleThemeAnalysisList2
} from '@/api/monthbookkeeper/financialDetail'
const tableCols = [
    { istrue: true, prop: 'shopCode', label: '店铺编码', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'numberInternalOrder', label: '内部订单号', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'numberOnlineOrderOrigin', label: '线上订单号', sortable: 'custom', width: '100', type: 'html' },
    { istrue: true, prop: 'outNo', label: '出仓单号', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'proCode', label: '宝贝ID', sortable: 'custom', width: '80' },//原type: 'html'，2025-05-30 删除
    { istrue: true, prop: 'nameProductBianma', label: '商品编码', sortable: 'custom', width: '80', type: 'html' },
    { istrue: true, prop: 'nameMultiTag', label: '标签', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'typeOrder', label: '订单类型', width: '80', sortable: 'custom', type: 'html' },
    { istrue: true, prop: 'statusOrder', label: '订单状态', sortable: 'custom', width: '80', type: 'html' },
    //{istrue:true,prop:'nameWarehouse',label:'发货仓',sortable:'custom', width:'80',type:'html'},
    { istrue: true, prop: 'timeSend', label: '发货日期', sortable: 'custom', width: '80', type: 'html' },
    { istrue: true, prop: 'timePay', label: '支付时间', sortable: 'custom', width: '120', type: 'html' },
    // {istrue:true,prop:'nameExpressCompany',label:'快递公司',sortable:'custom', width:'80',type:'html'},
    // {istrue:true,prop:'numberExpress',label:'快递单号',sortable:'custom', width:'80',type:'html'},
    { istrue: true, prop: 'nameBrand', label: '品牌', sortable: 'custom', width: '80', type: 'html' },
    { istrue: true, prop: 'countSale', label: '销售数量', sortable: 'custom', width: '80', type: 'html' },
    { istrue: true, prop: 'countActual', label: '实发数量', sortable: 'custom', width: '80', type: 'html' },
    { istrue: true, prop: 'amountSaleJe', label: '销售金额', sortable: 'custom', width: '80', type: 'html', formatter: (row) => { return row.amountSaleJe?.toFixed(3) } },
    { istrue: true, prop: 'amountCoustJe', label: '销售成本', sortable: 'custom', width: '80', type: 'html', formatter: (row) => { return row.amountCoustJe?.toFixed(3) } },
    { istrue: true, prop: 'amountSaleSf', label: '实发金额', sortable: 'custom', width: '80', type: 'html', formatter: (row) => { return row.amountSaleSf?.toFixed(3) } },
    { istrue: true, prop: 'amountCoustSf', label: '实发成本', sortable: 'custom', width: '80', type: 'html', formatter: (row) => { return row.amountCoustSf?.toFixed(3) } },
    { istrue: true, prop: 'amountRefundDQ', label: '退货金额', sortable: 'custom', width: '80', type: 'html', formatter: (row) => { return row.amountRefundDQ?.toFixed(3) } },
    { istrue: true, prop: 'freightRevenue', label: '运费收入', sortable: 'custom', width: '80', type: 'html', formatter: (row) => { return row.freightRevenue?.toFixed(3) } },
    { istrue: true, prop: 'actualCost', label: '实际成本', sortable: 'custom', width: '80', type: 'html', formatter: (row) => { return row.actualCost?.toFixed(3) } },
    //{istrue:true,prop:'weightOrder',label:'订单重量',sortable:'custom', width:'80',type:'html'},
    //{istrue:true,prop:'nameProductBianmaGroup',label:'组合编码',sortable:'custom', width:'80',type:'html'},
    //{istrue:true,prop:'typeVirtualProduct',label:'虚拟分类',sortable:'custom', width:'80',type:'html'},
];
const tableHandles = [
    { label: "店铺汇总导出", handle: (that) => that.onShopGatherExport2() },
    //{ label: "导出店铺汇总", handle: (that) => that.onShopGatherExportShow() },
    { label: "同步空宝贝ID", handle: (that) => that.onSyncProCodeShow() },
    { label: "阿里巴巴严选订单同步", handle: (that) => that.onSyncAlbbYanXuanOrders() },
    { label: "单店导出", handle: (that) => that.onOneShopGatherExport() },
]
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
    data() {
        return {
            shopCheck: true,
            that: this,
            filter: {
                platform: null,
                yearMonth: null,
                shopCode: null,
                isNullProCode: null,
                proCode: null,
                goodsCode: null,
                isGift: null,
                typeOrder: null,
            },
            tableHandles: tableHandles,
            shopList: [],
            userList: [],
            groupList: [],
            platformlist: platformlist,
            ZTCKeyWordList: [],
            tableCols: tableCols,
            summaryarry: {},
            total: 0,
            pager: { OrderBy: "id", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],

            dialogShopSumVisible: false,
            dialogShopSumLoading: false,

            dialogProCodeVisible: false,
            dialogProCodeLoading: false,
            dialogProCodeYearMonth: null,
            dialogProCodePlatform: null,

        };
    },
    async mounted() {
        this.platformlist = this.platformlist.filter(item => item.value !== 0 && item.value !== 5);
        // this.platformlist.splice(0,1);
        // this.platformlist.splice(6,1);
    },
    methods: {
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async onchangeplatform(val) {
            this.filter.shopCode = null;
            const res1 = await getshopList({ platform: val, currentPage: 1, pageSize: 100000 });
            this.shopList = res1.data.list
            if (this.filter.platform == 11) {
                this.$nextTick(() => {
                    this.shopCheck = false;
                    this.tableCols.unshift({ istrue: true, prop: 'distributor', label: '分销商', width: '90', sortable: 'custom' });
                })
            } else {
                this.shopCheck = true;
                this.tableCols = this.tableCols.filter(f => f.label != '分销商');
            }
        },
        // async getShopList(){
        //   const res1 = await getAllShopList();
        //   this.shopList=[];
        //     res1.data?.forEach(f => {
        //       if(f.isCalcSettlement&&f.shopCode)
        //           this.shopList.push(f);
        //     });
        // },
        onRefresh() {
            this.onSearch()
        },
        onSearch() {
            if (this.filter.saleAmontMin < 0 || this.filter.saleAmontMin > 999999999) {
                this.$message({ message: '销售金额输入错误', type: 'error' });
                return;
            }
            if (this.filter.saleAmontMax < 0 || this.filter.saleAmontMax > 999999999) {
                this.$message({ message: '销售金额输入错误', type: 'error' });
                return;
            }
            this.$refs.pager.setPage(1);
            this.getList();
        },
        async getList() {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter,
            };
            this.listLoading = true;
            let res = {}
            if (this.filter.platform == 11) {
                res = await getFenXiaoSaleThemeAnalysisPageList(params);
            } else {
                res = await getPageList(params);
            }
            this.listLoading = false;
            this.total = res.data?.total
            this.ZTCKeyWordList = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onShopGatherExportShow() {
            if (!this.filter.yearMonth || !this.filter.platform) {
                this.$message({ message: "请输入年月和平台", type: "warning" });
                return
            }
            this.dialogShopSumVisible = true;
        },
        async onShopGatherExport(curpage) {
            if (!this.filter.yearMonth || !this.filter.platform) {
                this.$message({ message: "请输入年月和平台", type: "warning" });
                return
            }
            this.$confirm('确认要导出吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...this.filter,
                };
                params.currentPage = curpage, params.pageSize = 50;
                console.log(params, "params");
                var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
                this.dialogShopSumLoading = true;
                var res = await exportGatherSaleThemeAnalysisList(params);
                this.dialogShopSumLoading = false;
                loadingInstance.close();
                if (!res?.data) {
                    this.$message({ message: "没有数据", type: "warning" });
                    return
                }
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '销售主题分析店铺汇总_' + new Date().toLocaleString() + '.xlsx')
                aLink.click();
            }).catch(() => {
            });
        },
        async onShopGatherExport2(opt) {
            if (!this.filter.yearMonth || !this.filter.platform) {
                this.$message({ message: "请输入年月和平台", type: "warning" });
                return
            }
            let pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter,
            };
            let res = await ExportGatherSaleThemeAnalysisList2(params);
            if (!res?.data) {
                return
            }
            this.$message({ message: "正在后台导出中，请点击头像-在下载管理中查看", type: "success" });
        },
        async onOneShopGatherExport(opt) {
            if (!this.filter.yearMonth || !this.filter.platform || !this.filter.shopCode) {
                this.$message({ message: "请选择年月,平台和店铺", type: "warning" });
                return
            }
            let pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter,
            };
            let res = await ExportSaleThemeAnalysisList2(params);
            if (!res?.data) {
                return
            }
            this.$message({ message: "正在后台导出中，请点击头像-在下载管理中查看", type: "success" });
        },
        onSyncProCodeShow() {
            this.dialogProCodeVisible = true;
        },
        async onSyncProCodeSave() {
            if (!this.dialogProCodeYearMonth || !this.dialogProCodePlatform) {
                this.$message({ message: "请选择月份和平台", type: "warning" });
                return;
            }
            this.dialogProCodeLoading = true;
            let res = await SyncSaleThemeAnalysisEmptyProCode({ yearMonth: this.dialogProCodeYearMonth, platform: this.dialogProCodePlatform });
            this.dialogProCodeLoading = false;
            if (res?.success == true) {
                this.$message({ message: "正在后台努力同步中......请稍后刷新页面查看", type: "success" });
                this.dialogProCodeVisible = false;
            }
        },
        async onSyncAlbbYanXuanOrders() {
            if (!this.filter.yearMonth) {
                this.$message({ message: "请输入年月", type: "warning" });
                return
            }
            this.$confirm('确认要同步严选订单给阿里巴巴的宝贝吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                let res = await SyncAlbbYanXuanOrders({ yearMonth: this.filter.yearMonth });
                if (res?.success == true) {
                    this.$message({ message: "正在后台努力同步中......请稍后刷新页面查看", type: "success" });
                }
            }).catch(() => {
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
