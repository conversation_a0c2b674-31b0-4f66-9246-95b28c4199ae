<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="filter"
        @submit.native.prevent>
        <el-form-item  label="年月:">
           <el-date-picker style="width: 110px" v-model="filter.yearMonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
        </el-form-item>
        <el-form-item label="所属店铺:" label-position="right" >
          <el-select filterable v-model="filter.shopCode" placeholder="请选择" class="el-select-content">
            <el-option 
              v-for="item in shopList"
              :key="item.shopName"
              :label="item.shopName"
              :value="item.shopCode">             
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订单编号:" label-position="right" >
            <el-input v-model="filter.serialNumberOrder" style="width:183px;"/>
        </el-form-item>
         <!-- <el-form-item label="异常订单：:" label-position="right" >
          <el-select filterable v-model="filter.isException" placeholder="请选择" class="el-select-content">
            <el-option label="全部" value></el-option>
            <el-option label="不异常" :value="false"></el-option>
            <el-option label="异常" :value="true"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onExport">导出</el-button>
        </el-form-item>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' :isSelectColumn='false' :hasexpand='false' @sortchange='sortchange' :tableData='ZTCKeyWordList' 
                   @select='selectchange' :isSelection='false' :tableCols='tableCols' :loading="listLoading" :showsummary='true' :summaryarry='summaryarry'>
      <el-table-column type="expand">
        <template slot-scope="props">
        <div>
          <el-table :data="props.row.detaildata" style="width: 100%">
            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col"/>
          </el-table>
        </div>
      </template>
      </el-table-column>
       <template slot='extentbtn'>
          <el-button-group>            
           
          </el-button-group>
        </template>
    </ces-table>    
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length"  @get-page="getList" />
    </template>
  </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
import { formatPlatform,formatLink} from "@/utils/tools";
import {getFinancialResultPageList as getPageList ,exportFinacialResult} from '@/api/bookkeeper/financialDetail'

const tableCols =[
      {istrue:true,prop:'numberOnlineOrderOrigin',label:'原始线上订单号', width:'160'},
      {istrue:true,prop:'amountSale',label:'销售金额', width:'80',type:'html',formatter:(row)=>{return row.amountSale?.toFixed(2)}},
      {istrue:true,prop:'amountCost',label:'销售成本', width:'80',type:'html',formatter:(row)=>{return row.amountCost?.toFixed(2)}},
      {istrue:true,prop:'amountMargin',label:'差额', width:'80',type:'html',formatter:(row)=>{return row.amountMargin?.toFixed(2)}},
      {istrue:true,prop:'amountSettlement',label:'总结算收入', width:'90',type:'html',formatter:(row)=>{return row.amountSettlement?.toFixed(2)}},
      {istrue:true,prop:'amountCurrentMonthSettlement',label:'当月结算', width:'80',type:'html',formatter:(row)=>{return row.amountCurrentMonthSettlement?.toFixed(2)}},
      {istrue:true,prop:'amountNextMonthSettlement',label:'次月结算', width:'80',type:'html',formatter:(row)=>{return row.amountNextMonthSettlement?.toFixed(2)}},
      {istrue:true,prop:'amountReturn',label:'总退款', width:'80',type:'html',formatter:(row)=>{return row.amountReturn?.toFixed(2)}},
      {istrue:true,prop:'amountCurrentMonthReturn',label:'当月退款', width:'80',type:'html',formatter:(row)=>{return row.amountCurrentMonthReturn?.toFixed(2)}},
      {istrue:true,prop:'amountNextMonthReturn',label:'次月退款', width:'80',type:'html',formatter:(row)=>{return row.amountNextMonthReturn?.toFixed(2)}},
      {istrue:true,prop:'remark',label:'备注', width:'120'},
      {istrue:true,prop:'createdTime',label:'计算时间', width:'150'},
     ];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
  data() {
    return {
      that:this,
      filter: {
        yearMonth:null,
        shopCode:null,
        isException:null
      },
      shopList:[],
      userList:[],
      groupList:[],
      ZTCKeyWordList: [],
      summaryarry:{},
      tableCols:tableCols,
      total: 0,
      pager:{OrderBy:"orderDate",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
    };
  },
  async mounted() {
    await this.getShopList();
  },
  methods: {
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    async getShopList(){
      const res1 = await getAllShopList();
      this.shopList=[];
      res1.data?.forEach(f => {
        //f.isOpen==1&&
        if(f.isCalcSettlement&&f.shopCode)
            this.shopList.push(f);
      });
    },
    onRefresh(){
        this.onSearch()
    },
    onSearch(){
       this.$refs.pager.setPage(1);
       this.getList();
    },
    async onExport(){
      if (!this.filter.yearMonth) {
        this.$message({message:"请先选择月份！",type:"warning"});
        return;
      } 
      var pager = this.$refs.pager.getPager();
      const params = {  ...pager,   ...this.pager,   ...this.filter};
      var res= await exportFinacialResult(params);
      if(!res?.data) {
         this.$message({message:"没有数据",type:"warning"});
         return
      }

      const aLink = document.createElement("a");
      let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download','销售结算数据_' +  new Date().toLocaleString() + '_.xlsx' )
      aLink.click()
    },
    async getList(){ 
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...this.filter,
      };
      this.listLoading = true;
      const res = await getPageList(params);
      this.listLoading = false;
      this.total = res.data?.total
      this.ZTCKeyWordList = res.data?.list;
      this.summaryarry=res.data?.summary;
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>