<template>
  <my-container>
    <el-tabs v-model="activeName" style="height: 95%">
      <el-tab-pane label="汇总" name="first1" style="height: 99%" v-if="checkPermission('FreightCheckSummaryPage')">
        <summaryPage ref="refsummaryPage" />
      </el-tab-pane>
      <el-tab-pane label="仓库数据" name="first2" style="height: 99%" lazy
        v-if="checkPermission('FreightCheckWarehouseData')">
        <warehouseData ref="refwarehouseData" />
      </el-tab-pane>
      <el-tab-pane label="采购数据" name="first3" style="height: 99%" lazy
        v-if="checkPermission('FreightCheckProcurementData')">
        <procurementData ref="refprocurementData" />
      </el-tab-pane>
      <el-tab-pane label="财务初审" name="first4" style="height: 99%" lazy
        v-if="checkPermission('FreightCheckFinancialPreliminaryReview')">
        <financialPreliminaryReview ref="reffinancialPreliminaryReview" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import summaryPage from "./components/summaryPage.vue";
import warehouseData from "./components/warehouseData.vue";
import procurementData from "./components/procurementData.vue";
import financialPreliminaryReview from "./components/financialPreliminaryReview.vue";
export default {
  name: "collateIndex",
  components: {
    MyContainer, summaryPage, warehouseData, procurementData, financialPreliminaryReview
  },
  data() {
    return {
      activeName: "first1",
    };
  },
  methods: {},
};
</script>

<style lang="scss" scoped></style>
