<template>
  <div>
    <div style="width:100%;z-index: 999;box-shadow: 0px 3px 5px #eeeeee;">
      <div class="ybgdt" v-show="showDepartments">
        <div class="ybdhxx">
          <div class="ybsjbhn" @click="toggleActive('visualdesigndepartment')"
            :class="{ 'ybsjbhn-on': activeDepartment == 'visualdesigndepartment' }">视觉设计部</div>
          <div class="ybsjbhn" @click="toggleActive('packingplant')"
            :class="{ 'ybsjbhn-on': activeDepartment == 'packingplant' }">包装加工厂
          </div>
        </div>
      </div>
      <div class="ybsjxzbj">
        <div style="width:50%;display: flex;justify-content: left;">
          <div>
            <el-date-picker size="mini" clearable v-model="timerange" type="daterange" align="right" unlink-panels
              :disabled="forbidden" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" @change="changeTime">
            </el-date-picker>
          </div>
          <div style="margin-left:10px;"><el-button style="width:80px;" size="mini" type="primary"
              @click="onSearch(1)">查询</el-button>
          </div>
          <div style="margin-left:10px;"><el-button size="mini">刷新</el-button>
          </div>
        </div>
        <div style="width:50%;display: flex;justify-content: right;margin-right:0px;">
          <div style="margin-right:15px;">
            <el-radio-group v-model="showDepartments" size="mini">
              <el-radio :label="true">显示</el-radio>
              <el-radio :label="false">隐藏</el-radio>
            </el-radio-group>
          </div>
          <el-checkbox-group v-model="filter.checkData" :disabled="forbidden">
            <el-checkbox label="任务列表"></el-checkbox>
            <el-checkbox label="已确认"></el-checkbox>
            <el-checkbox label="统计列表"></el-checkbox>
            <el-checkbox label="存档"></el-checkbox>
          </el-checkbox-group>

        </div>
      </div>
    </div>

    <!-- 新品拍摄 -->
    <div class="sybjfa" v-if="shotment == 1">
      <div style="width:100%;background-color: #f3f4f6;">
        <div class="ybnrdhbj">
          <div class="ybnrdh1">
            <el-select style="width:230px;" clearable v-model="finitiveversionId" size="small" placeholder="请选择"
              @change="onSearch(1)">
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
            <span style="margin-left:10px;">
              <el-button style="width:90px;" size="small" plain @click="definitiveversion(1)">确定版本</el-button>
            </span>
          </div>
          <div class="ybnrdh2" v-if="activeDepartment == 'packingplant'">
            <div class="ybnrdhxx" @click="getlist(5)" :class="{ 'ybnrdhxx-on': shotment == 5 }">包装加工
            </div>
          </div>
          <div class="ybnrdh2" v-else>
            <div class="ybnrdhxx" @click="getlist(1)" :class="{ 'ybnrdhxx-on': shotment == 1 }">新品拍摄</div>
            <div class="ybnrdhxx" @click="getlist(2)" :class="{ 'ybnrdhxx-on': shotment == 2 }">短视频拍摄</div>
            <div class="ybnrdhxx" @click="getlist(3)" :class="{ 'ybnrdhxx-on': shotment == 3 }">直通车图</div>
            <div class="ybnrdhxx" @click="getlist(4)" :class="{ 'ybnrdhxx-on': shotment == 4 }">日常改图</div>
          </div>
          <div class="ybnrdh3">
            <span style="margin-right:10px;">
              <el-button style="width:90px;" size="small" plain @click="definitiveversion(2)">确定版本</el-button>
            </span>
            <el-select style="width:230px;" clearable v-model="versionversionId" size="small" placeholder="请选择"
              @change="onSearch(1)">
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div>
      </div>

      <div class="sybj" label-width="100px">
        <productShooting ref="productShooting" :myValue="shotment" @version="information" @deleteversion="deletedversion"
          :rightparams="rightparams" :leftparams="leftparams">
        </productShooting>
      </div>
    </div>

    <!-- 短视频拍摄 -->
    <div class="sybjfa" v-else-if="shotment == 2">
      <div style="width:100%;background-color: #f3f4f6;">
        <div class="ybnrdhbj">
          <div class="ybnrdh1">
            <el-select style="width:230px;" clearable v-model="finitiveversionId" size="small" placeholder="请选择"
              @change="onSearch(1)">
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
            <span style="margin-left:10px;">
              <el-button style="width:90px;" size="small" plain @click="definitiveversion(1)">确定版本</el-button>
            </span>
          </div>
          <div class="ybnrdh2" v-if="activeDepartment == 'packingplant'">
            <div class="ybnrdhxx" @click="getlist(5)" :class="{ 'ybnrdhxx-on': shotment == 5 }">包装加工
            </div>
          </div>
          <div class="ybnrdh2" v-else>
            <div class="ybnrdhxx" @click="getlist(1)" :class="{ 'ybnrdhxx-on': shotment == 1 }">新品拍摄</div>
            <div class="ybnrdhxx" @click="getlist(2)" :class="{ 'ybnrdhxx-on': shotment == 2 }">短视频拍摄</div>
            <div class="ybnrdhxx" @click="getlist(3)" :class="{ 'ybnrdhxx-on': shotment == 3 }">直通车图</div>
            <div class="ybnrdhxx" @click="getlist(4)" :class="{ 'ybnrdhxx-on': shotment == 4 }">日常改图</div>
          </div>
          <div class="ybnrdh3">
            <span style="margin-right:10px;">
              <el-button style="width:90px;" size="small" plain @click="definitiveversion(2)">确定版本</el-button>
            </span>
            <el-select style="width:230px;" clearable v-model="versionversionId" size="small" placeholder="请选择"
              @change="onSearch(1)">
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="sybj" label-width="100px">
          <shortVideoShooting ref="shortVideoShooting" :myValue="shotment" @version="information"
            @deleteversion="deletedversion" :rightparams="rightparams" :leftparams="leftparams">
          </shortVideoShooting>
        </div>
      </div>
    </div>

    <!-- 直通车图 -->
    <div class="sybjfa" v-else-if="shotment == 3">
      <div style="width:100%;background-color: #f3f4f6;">
        <div class="ybnrdhbj">
          <div class="ybnrdh1">
            <el-select style="width:230px;" clearable v-model="finitiveversionId" size="small" placeholder="请选择"
              @change="onSearch(1)">
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
            <span style="margin-left:10px;">
              <el-button style="width:90px;" size="small" plain @click="definitiveversion(1)">确定版本</el-button>
            </span>
          </div>
          <div class="ybnrdh2" v-if="activeDepartment == 'packingplant'">
            <div class="ybnrdhxx" @click="getlist(5)" :class="{ 'ybnrdhxx-on': shotment == 5 }">包装加工
            </div>
          </div>
          <div class="ybnrdh2" v-else>
            <div class="ybnrdhxx" @click="getlist(1)" :class="{ 'ybnrdhxx-on': shotment == 1 }">新品拍摄</div>
            <div class="ybnrdhxx" @click="getlist(2)" :class="{ 'ybnrdhxx-on': shotment == 2 }">短视频拍摄</div>
            <div class="ybnrdhxx" @click="getlist(3)" :class="{ 'ybnrdhxx-on': shotment == 3 }">直通车图</div>
            <div class="ybnrdhxx" @click="getlist(4)" :class="{ 'ybnrdhxx-on': shotment == 4 }">日常改图</div>
          </div>
          <div class="ybnrdh3">
            <span style="margin-right:10px;">
              <el-button style="width:90px;" size="small" plain @click="definitiveversion(2)">确定版本</el-button>
            </span>
            <el-select style="width:230px;" clearable v-model="versionversionId" size="small" placeholder="请选择"
              @change="onSearch(1)">
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="sybj" label-width="100px">
        <throughTrainChart ref="throughTrainChart" :myValue="shotment" @version="information"
          @deleteversion="deletedversion" :rightparams="rightparams" :leftparams="leftparams">
        </throughTrainChart>
      </div>
    </div>

    <!-- 日常改图 -->
    <div class="sybjfa" v-else-if="shotment == 4">
      <div style="width:100%;background-color: #f3f4f6;">
        <div class="ybnrdhbj">
          <div class="ybnrdh1">
            <el-select style="width:230px;" clearable v-model="finitiveversionId" size="small" placeholder="请选择"
              @change="onSearch(1)">
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
            <span style="margin-left:10px;">
              <el-button style="width:90px;" size="small" plain @click="definitiveversion(1)">确定版本</el-button>
            </span>
          </div>
          <div class="ybnrdh2" v-if="activeDepartment == 'packingplant'">
            <div class="ybnrdhxx" @click="getlist(5)" :class="{ 'ybnrdhxx-on': shotment == 5 }">包装加工
            </div>
          </div>
          <div class="ybnrdh2" v-else>
            <div class="ybnrdhxx" @click="getlist(1)" :class="{ 'ybnrdhxx-on': shotment == 1 }">新品拍摄</div>
            <div class="ybnrdhxx" @click="getlist(2)" :class="{ 'ybnrdhxx-on': shotment == 2 }">短视频拍摄</div>
            <div class="ybnrdhxx" @click="getlist(3)" :class="{ 'ybnrdhxx-on': shotment == 3 }">直通车图</div>
            <div class="ybnrdhxx" @click="getlist(4)" :class="{ 'ybnrdhxx-on': shotment == 4 }">日常改图</div>
          </div>
          <div class="ybnrdh3">
            <span style="margin-right:10px;">
              <el-button style="width:90px;" size="small" plain @click="definitiveversion(2)">确定版本</el-button>
            </span>
            <el-select style="width:230px;" clearable v-model="versionversionId" size="small" placeholder="请选择"
              @change="onSearch(1)">
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="sybj" label-width="100px">
        <dailyRedrawing ref="dailyRedrawing" :myValue="shotment" @version="information" @deleteversion="deletedversion"
          :rightparams="rightparams" :leftparams="leftparams">
        </dailyRedrawing>
      </div>
    </div>

    <!-- 包装加工 -->
    <div class="sybjfa" v-else-if="shotment == 5">
      <div style="width:100%;background-color: #f3f4f6;">
        <div class="ybnrdhbj">
          <div class="ybnrdh1">
            <el-select style="width:230px;" clearable v-model="finitiveversionId" size="small" placeholder="请选择"
              @change="onSearch(1)">
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
            <span style="margin-left:10px;">
              <el-button style="width:90px;" size="small" plain @click="definitiveversion(1)">确定版本</el-button>
            </span>
          </div>
          <div class="ybnrdh2" v-if="activeDepartment == 'packingplant'">
            <div class="ybnrdhxx" @click="getlist(5)" :class="{ 'ybnrdhxx-on': shotment == 5 }">包装加工
            </div>
          </div>
          <div class="ybnrdh2" v-else>
            <div class="ybnrdhxx" @click="getlist(1)" :class="{ 'ybnrdhxx-on': shotment == 1 }">新品拍摄</div>
            <div class="ybnrdhxx" @click="getlist(2)" :class="{ 'ybnrdhxx-on': shotment == 2 }">短视频拍摄</div>
            <div class="ybnrdhxx" @click="getlist(3)" :class="{ 'ybnrdhxx-on': shotment == 3 }">直通车图</div>
            <div class="ybnrdhxx" @click="getlist(4)" :class="{ 'ybnrdhxx-on': shotment == 4 }">日常改图</div>
          </div>
          <div class="ybnrdh3">
            <span style="margin-right:10px;">
              <el-button style="width:90px;" size="small" plain @click="definitiveversion(2)">确定版本</el-button>
            </span>
            <el-select style="width:230px;" clearable v-model="versionversionId" size="small" placeholder="请选择"
              @change="onSearch(1)">
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="sybj" label-width="100px">
        <packagingProcessing ref="refpackagingProcessing" :myValue="shotment" @deleteversion="deletedversion"
          @version="packinformation" :rightparams="rightparams" :leftparams="leftparams">
        </packagingProcessing>
      </div>
    </div>

  </div>
</template>

<script>
import * as echarts from "echarts";
import productShooting from "@/views/media/shooting/monthlyReport/productShooting.vue";
import shortVideoShooting from "@/views/media/shooting/monthlyReport/shortVideoShooting.vue";
import throughTrainChart from "@/views/media/shooting/monthlyReport/throughTrainChart.vue";
import dailyRedrawing from "@/views/media/shooting/monthlyReport/dailyRedrawing.vue";
import packagingProcessing from "@/views/media/shooting/monthlyReport/packagingProcessing.vue";
import { getMainStatVersionInfo, getMainStatVersionList, addainStatVersionList, saveMainStatVersionList, confirmMainStatVersionList } from '@/api/media/shootingset';
import { getMainStatVersionInfo as inventorygetMainStatVersionInfo, getMainStatVersionList as inventorygetMainStatVersionList, addainStatVersionList as inventoryaddainStatVersionList, saveMainStatVersionList as inventorysaveMainStatVersionList, confirmMainStatVersionList as inventoryconfirmMainStatVersionList } from '@/api/inventory/packagesSetProcessing';

export default {
  name: 'index',
  components: { shortVideoShooting, productShooting, throughTrainChart, dailyRedrawing, packagingProcessing },
  data() {
    return {
      querycheck: false,
      listLoading: false,
      forbidden: false,
      rightparams: {},
      leftparams: {},
      versionversionId: null,
      finitiveversionId: null,
      versionId: null,
      grouplist: [],
      timerange: [],
      tABswitching: 1,
      chartdataswitching: true,
      statisticalyear: false,
      statisticalmonth: false,
      radio: '',
      value3: null,
      value2: null,
      bocty: [],
      drawerVisible: false,
      input: null,
      versionsavepopup: false,
      activeDepartment: 'visualdesigndepartment',//视觉设计部
      shotment: 1,//新品拍摄
      proces: '',
      value5: null,
      value9: null,
      value: '',
      value2: null,
      filter: {
        startTime: null,
        endTime: null,
        checkData: ['任务列表', '已确认'],
      },
      pickerOptions: {
        shortcuts: [{
          text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近半个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      showDepartments: false,
    };
  },

  async mounted() {
    this.onSearch();
    this.getlist()
  },

  methods: {
    deletedversion() {
      this.onSearch();
      this.getlist()
    },
    // onInquire() {
    //   this.onSearch(1)
    //   this.getlist()
    // },
    changeTime(e) {
      if (e) {
        this.timerange = e
        this.filter.startTime = e[0]
        this.filter.endTime = e[1]
      } else {
        this.timerange = null
        this.filter.startTime = null
        this.filter.endTime = null
      }
      // this.onSearch()
    },
    async definitiveversion(val) {
      if (this.shotment !== 5) {
        if (val == 1) {
          var params = { versionId: this.finitiveversionId, leftOrRight: 1 }
        } else if (val == 2) {
          var params = { versionId: this.versionversionId, leftOrRight: 2 }
        }
        const { success } = await confirmMainStatVersionList(params)
        if (success) {
          this.$message({ message: "确定版本成功!", type: "success" });
          this.onSearch(1);
        }
      } else if (this.shotment == 5) {
        if (val == 1) {
          var params = { versionId: this.finitiveversionId, leftOrRight: 1 }
        } else if (val == 2) {
          var params = { versionId: this.versionversionId, leftOrRight: 2 }
        }
        const { success } = await inventoryconfirmMainStatVersionList(params)
        if (success) {
          this.$message({ message: "确定版本成功!", type: "success" });
          this.onSearch(1);
        }
      }
    },

    async information(value) {
      if (this.timerange && this.timerange.length > 1) {
        this.filter.startTime = null;
        this.filter.endTime = null;
        this.filter.startTime = this.timerange[0];
        this.filter.endTime = this.timerange[1];
      }
      const params = { ...this.filter, versionId: value }
      const { success } = await saveMainStatVersionList(params)
      if (success) {
        this.$message({ message: "保存成功", type: "success" });
        // this.getlist()
      }
    },
    async packinformation(value) {
      if (this.timerange && this.timerange.length > 1) {
        this.filter.startTime = null;
        this.filter.endTime = null;
        this.filter.startTime = this.timerange[0];
        this.filter.endTime = this.timerange[1];
      }
      const params = { ...this.filter, versionId: value }
      const { success } = await inventorysaveMainStatVersionList(params)
      if (success) {
        this.$message({ message: "保存成功", type: "success" });
        // this.getlist()
      }
    },

    //搜索
    onSearch(val) {
      // if (val !== undefined && val ==1) {
      //   this.querycheck = true
      // }
      if (this.versionversionId && this.finitiveversionId) {
        this.judgment()
      }

      if (!this.versionversionId || !this.finitiveversionId) {
        this.forbidden = false
      }

      if (this.filter.checkData.length == 0 && val == 1 && this.forbidden == false) {
        this.filter.checkData = ['任务列表', '已确认']
      }
      if (val !== 1 && (this.finitiveversionId == null || this.versionversionId == null)) {
        this.filter.checkData = ['任务列表', '已确认']
      }
      if (this.filter.checkData.length == 0 && this.forbidden == false) {
        this.$message.error('列表统计类型必须选择！');
        return
      }
      if (this.timerange && this.timerange.length > 1) {
        this.filter.startTime = null;
        this.filter.endTime = null;
        if (this.timerange[0] && this.timerange[1]) {
          this.filter.startTime = this.timerange[0];
          this.filter.endTime = this.timerange[1];
        }
      }
      let filterCopy = { ...this.filter }
      if (this.timerange && this.timerange.length > 1) {
        filterCopy.startTime = this.timerange[0]
        filterCopy.endTime = this.timerange[1]
      }
      this.leftparams = { ...this.filter, versionId: this.finitiveversionId }
      this.rightparams = { ...this.filter, versionId: this.versionversionId }
      if (this.shotment == 1) {
        this.$nextTick(() => {
          this.$refs.productShooting.leftproduct({ ...this.filter, versionId: this.finitiveversionId });
          this.$refs.productShooting.rightproduct({ ...this.filter, versionId: this.versionversionId });
        });
      } else if (this.shotment == 2) {
        this.$nextTick(() => {
          this.$refs.shortVideoShooting.leftproduct({ ...this.filter, versionId: this.finitiveversionId });
          this.$refs.shortVideoShooting.rightproduct({ ...this.filter, versionId: this.versionversionId });
        });
      } else if (this.shotment == 3) {
        this.$nextTick(() => {
          this.$refs.throughTrainChart.leftproduct({ ...this.filter, versionId: this.finitiveversionId });
          this.$refs.throughTrainChart.rightproduct({ ...this.filter, versionId: this.versionversionId });
        });
      } else if (this.shotment == 4) {
        this.$nextTick(() => {
          this.$refs.dailyRedrawing.leftproduct({ ...this.filter, versionId: this.finitiveversionId });
          this.$refs.dailyRedrawing.rightproduct({ ...this.filter, versionId: this.versionversionId });
        });
      } else if (this.shotment == 5) {
        this.$nextTick(() => {
          this.$refs.refpackagingProcessing.leftproduct({ ...this.filter, versionId: this.finitiveversionId });
          this.$refs.refpackagingProcessing.rightproduct({ ...this.filter, versionId: this.versionversionId });
        });
      }
      // if (this.querycheck == true) {
      //   this.getlist()
      //   console.log(111);
      // }
      // setTimeout(() => {
      //   this.querycheck = false
      // }, 200)
      this.listLoading = false;
    },
    async getlist(plant) {
      this.judgment()
      if (plant !== undefined) {
        this.shotment = plant
        this.tABswitching = plant
      }
      this.proces = ''
      this.listLoading = true;
      if (this.tABswitching !== 5) {
        this.finitiveversionId = null
        this.versionversionId = null
        this.grouplist = []
        const { data, success } = await getMainStatVersionList({ typeId: this.shotment })
        if (success) {
          this.grouplist = data?.map(item => { return { value: item.id, label: item.versionName }; });
        }
        const res = await getMainStatVersionInfo({ typeId: this.shotment })
        // if (res.data.length == 0) {
        //   this.finitiveversionId = null
        //   this.versionversionId = null
        //   this.grouplist = []
        // }
        res.data.forEach(item => {
          if (item.leftOrRight === 1) {
            this.finitiveversionId = item.id;
          } else if (item.leftOrRight === 2) {
            this.versionversionId = item.id;
          }
        });
        // if (Array.isArray(data) && data.length == 1) {
        //   this.finitiveversionId = null
        //   this.versionversionId = null
        //   this.handleData(data);
        // } else if (Array.isArray(data) && data.length === 2) {
        //   this.handleData(data);
        // }
      } else if (this.tABswitching == 5) {
        this.finitiveversionId = null
        this.versionversionId = null
        this.grouplist = []
        const { data, success } = await inventorygetMainStatVersionList()
        if (success) {
          this.grouplist = data?.map(item => { return { value: item.id, label: item.versionName }; });
        }
        const res = await inventorygetMainStatVersionInfo()
        // if (res.data.length == 0) {
        //   this.finitiveversionId = null
        //   this.versionversionId = null
        //   this.grouplist = []
        // }
        res.data.forEach(item => {
          if (item.leftOrRight === 1) {
            this.finitiveversionId = item.id;
          } else if (item.leftOrRight === 2) {
            this.versionversionId = item.id;
          }
        });
        // if (Array.isArray(data) && data.length == 1) {
        //   this.finitiveversionId = null
        //   this.versionversionId = null
        //   this.handleData(data);
        // } else if (Array.isArray(data) && data.length === 2) {
        //   this.handleData(data);
        // }
      }
      this.onSearch();
      // if (this.querycheck !== true) {
      //   this.onSearch();
      //   console.log(222);
      // }
      // setTimeout(() => {
      //   this.querycheck = false
      // }, 200)
      this.listLoading = false;
    },
    handleData(res) {
      let finitiveData = null;
      let versionData = null;
      res.forEach(item => {
        if (item.leftOrRight === 1) {
          finitiveData = item;
        } else if (item.leftOrRight === 2) {
          versionData = item;
        }
      });
      this.finitiveversionId = finitiveData?.id;
      this.versionversionId = versionData?.id;
    },
    judgment() {
      if (this.finitiveversionId !== null && this.versionversionId !== null) {
        this.filter.checkData = []
        this.timerange = []
        this.filter.startTime = null;
        this.filter.endTime = null;
        this.forbidden = true
      }
    },
    statistics(val) {
      if (val == 1) {
        this.statisticalyear = true
        this.statisticalmonth = false
        this.value2 = null
      } else {
        this.statisticalmonth = true
        this.statisticalyear = false
        this.value3 = null
      }
    },

    toggleActive(department) {
      this.activeDepartment = department;
      if (department == 'visualdesigndepartment') {
        this.shotment = 1
      } else if (department == 'packingplant') {
        this.shotment = 5
      }
      this.getlist(this.shotment)
      this.proces = 'processing'
    },
  },
};

</script>

<style lang="scss" scoped>
.sybj {
  background-color: #f3f4f6;
  padding: 5px;
  // height: 640px;
  // overflow-y: auto;
  display: flex;
  flex-wrap: nowrap;
}

.sybjfa {
  min-width: 1100px;
  background-color: #f3f4f6;
  height: 743px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.tjnrk1 {
  width: 50%;
  /* background-color: rgb(255, 187, 0); */
  // box-sizing: border-box;
  padding: 3px 5px;
  // display: inline-block;
}

.tjnrnk {
  width: 100%;
  background-color: rgb(255, 255, 255);
  box-sizing: border-box;
  padding: 15px 20px;
  float: left;
  border-radius: 6px;
}

.tjnrk4 {
  width: 100%;
  min-width: 1500px;
  /* background-color: rgb(255, 0, 0); */
  box-sizing: border-box;
  padding: 3px 5px;
  display: inline-block;
}

.tjbt {
  /* background-color: aquamarine; */
  /* font-weight: bold; */
  color: #333;
  line-height: 30px;
  font-size: 14px;
}

.bzjgsj {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  box-sizing: border-box;
  padding: 5px 5%;

}

.sztjk {
  width: 15%;
  min-width: 75px;
  height: 80px;
  background-color: #f5faff;
  padding: 20px;
  text-align: center;
  margin: 5px 0.5%;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sztjk .tjsz {
  font-size: 26px;
  text-align: center;
  // color: #409eff;
}

.sztjk .tjmc {
  font-size: 14px;
  text-align: center;
  // color: #409eff;
}

//
.ybsjbhn-on {
  width: 45%;
  height: 65px;
  font-size: 20px;
  font-weight: bold;
  line-height: 62px;
  color: #fff !important; //增加!important否则会被.ybsjbhn.color: #777;覆盖
  margin: 0 2%;
  background: linear-gradient(35deg, #43bfff, #6085ff);
  border-radius: 33px;
  display: flex;
  justify-content: center;
  cursor: pointer;
  transition: 0.5s;
  -webkit-user-select: none;
  user-select: none;
}

// 部门选择 star
.ybgdt {
  width: 100%;
  min-width: 1250px;
  /* height:135px; */
  background-color: #fff;
  box-sizing: border-box;
  padding: 20px 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
}

.ybdhxx {
  width: 38%;
  height: 80px;
  border-radius: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.ybsjbhn {
  width: 45%;
  height: 65px;
  font-size: 18px;
  line-height: 62px;
  color: #777;
  margin: 0 2%;
  background-color: #f7f8fa;
  border-radius: 33px;
  display: flex;
  justify-content: center;
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
}

.ybsjbhn:hover,
.ybsjbhn.on {
  width: 45%;
  height: 65px;
  font-size: 20px;
  font-weight: bold;
  line-height: 62px;
  color: #fff;
  margin: 0 2%;
  background: linear-gradient(35deg, #43bfff, #6085ff);
  border-radius: 33px;
  display: flex;
  justify-content: center;
  cursor: pointer;
  transition: 0.5s;
  -webkit-user-select: none;
  user-select: none;
}

.ybsjxzbj {
  width: 100%;
  height: 55px;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  padding: 0 10px;
}


// 项目选择 star
.ybnrdhbj {
  width: 100%;
  // background-color: #43bfff;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 20px 10px;
}

.ybnrdh1 {
  width: 25%;
  /* background-color: #3389b4; */
  display: flex;
  justify-content: left;
  align-items: center;
}

.ybnrdh2 {
  width: 50%;
  /* background-color: #65a3c2; */
  display: flex;
  justify-content: center;
  align-items: center;
}

.ybnrdh3 {
  width: 25%;
  /* background-color: #3389b4; */
  display: flex;
  justify-content: right;
  align-items: center;
}

//////////////////

.ybnrdhxx {
  width: 15%;
  height: 36px;
  font-size: 15px;
  line-height: 36px;
  color: #666;
  margin: 0 1%;
  background-color: #fff;
  /* background: linear-gradient(35deg, #43bfff, #6085ff); */
  border-radius: 20px;
  display: flex;
  justify-content: center;
  /* align-items: center; */
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
}

.ybnrdhxx:hover,
.ybnrdhxx.on {
  width: 15%;
  height: 36px;
  font-size: 15px;
  font-weight: bold;
  line-height: 36px;
  color: #555;
  margin: 0 1%;
  background-color: #f7f8fa;
  background: linear-gradient(35deg, #c7e9ff, #cedcff);
  border-radius: 20px;
  display: flex;
  justify-content: center;
  /* align-items: center; */
  transition: 0.5s;
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
}

.ybnrdhxx-on {
  width: 15%;
  height: 36px;
  font-size: 15px;
  font-weight: bold;
  line-height: 36px;
  color: #555;
  margin: 0 1%;
  background-color: #f7f8fa;
  background: linear-gradient(35deg, #c7e9ff, #cedcff);
  border-radius: 20px;
  display: flex;
  justify-content: center;
  /* align-items: center; */
  transition: 0.5s;
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
}

// 部门选择 end
</style>
