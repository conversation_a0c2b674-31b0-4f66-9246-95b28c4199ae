<template>
  <div>
    <div style="width:97%;">
      <div style="width:100%;display: flex;margin-bottom:5px;">
        <div style="width:50%;display: flex;align-items: center;font-size:14px;color:#666;font-weight:bold;">
          <el-tooltip v-if="textprompt" class="item" effect="dark" :content="textprompt" placement="right">
            <span>{{ examinationname }}</span>
          </el-tooltip>
          <span v-else>{{ examinationname }}</span>
        </div>
        <div style="width:50%;display: flex;justify-content: flex-end;">
          <el-button size="mini" type="primary" @click="newline">新增</el-button>
        </div>
      </div>
      <div class="xptcbl">
        <vxe-table border resizable show-overflow="ellipsis" ref="xTable" :loading="loading" :data="tableData" size="mini"
          :max-height="templatepage ? 269 : 216" :style="{ width: '100%', height: (templatepage ? 269 : 216) + 'px' }"
          :edit-config="{ trigger: 'manual', mode: 'row', showIcon: false, showStatus: true, autoClear: false }">
          <vxe-column field="styleName" title="款式" :edit-render="{}" v-if="isshowclounfuc('styleName')" width="150">
            <template #header>
              <span style="color: #F56C6C; margin: 0 7px 0 0">*</span>
              <span>款式</span>
            </template>
            <template #default="{ row }">
              <el-tooltip v-show="row.styleName.length > 5" class="item" effect="dark" :content="row.styleName"
                placement="top"><span>{{ row.styleName }}</span></el-tooltip>
              <span v-show="row.styleName.length <= 5">{{ row.styleName }}</span>
            </template>
            <template #edit="{ row }">
              <el-input v-model="row.styleName" type="text" placeholder="请输入款式" :maxlength="50"></el-input>
            </template>
          </vxe-column>
          <vxe-column field="urgencyJiDay" title="紧急款（≤）" :edit-render="{}" v-if="isshowclounfuc('urgencyJiDay')"
            width="120">
            <template #header>
              <span style="color: #F56C6C; margin: 0 7px 0 0">*</span>
              <span>紧急款（≤）</span>
            </template>
            <template #edit="{ row }">
              <el-input v-model="row.urgencyJiDay" type="number" placeholder="请输入紧急款（≤）" :maxlength="3" :max="100"
                pattern="^\d+$" @blur="BlurText($event)"></el-input>
            </template>
          </vxe-column>
          <vxe-column field="urgencyDay" title="加急款（≤）" :edit-render="{}" v-if="isshowclounfuc('urgencyDay')" width="120">
            <template #header>
              <span style="color: #F56C6C; margin: 0 7px 0 0">*</span>
              <span>加急款（≤）</span>
            </template>
            <template #edit="{ row }">
              <el-input v-model="row.urgencyDay" type="number" placeholder="请输入加急款（≤）" :maxlength="3" :max="100"
                pattern="^\d+$" @blur="BlurText($event)"></el-input>
            </template>
          </vxe-column>
          <vxe-column field="normalDay" title="正常款（≤）" :edit-render="{}" v-if="isshowclounfuc('normalDay')" width="120">
            <template #header>
              <span style="color: #F56C6C; margin: 0 7px 0 0">*</span>
              <span>正常款（≤）</span>
            </template>
            <template #edit="{ row }">
              <el-input v-model="row.normalDay" type="number" placeholder="请输入正常款（≤）" :maxlength="3" :max="100"
                pattern="^\d+$" @blur="BlurText($event)"></el-input>
            </template>
          </vxe-column>
          <vxe-column field="styleName" title="款式类型" :edit-render="{}" v-if="isshowclounfuc('styletype')"
            :width="templatepage ? 210 : 100">
            <template #header>
              <span style="color: #F56C6C; margin: 0 7px 0 0">*</span>
              <span>款式类型</span>
            </template>
            <template #edit="{ row }">
              <el-input v-model="row.styleName" type="text" placeholder="请输入" :maxlength="50"></el-input>
            </template>
          </vxe-column>
          <vxe-column field="equalOrGreaterScore" title="大于或等于" :edit-render="{}"
            v-if="isshowclounfuc('equalOrGreaterScore')" :width="templatepage ? 210 : 110">
            <template #header>
              <span style="color: #F56C6C; margin: 0 7px 0 0">*</span>
              <span>大于或等于</span>
            </template>
            <template #default="{ row }">
              {{ module === 'threetable' || module === 'fourtable' ? (row.equalOrGreaterScore !== null &&
                row.equalOrGreaterScore !==
                undefined ? (row.equalOrGreaterScore === 0 ? '0' : (row.equalOrGreaterScore + '%')) : '') : (module ===
                  'fivetable' || module
                  === 'sixtable' ? (row.equalOrGreaterScore !== null && row.equalOrGreaterScore !== undefined ?
                    (row.equalOrGreaterScore === 0 ? '0' :
                      row.equalOrGreaterScore) : '') : '') }}
            </template>
            <template #edit="{ row }">
              <el-input v-model="row.equalOrGreaterScore" type="number" placeholder="请输入" :maxlength="3" :max="100"
                pattern="^\d+$" @blur="performancescoreverify($event)"></el-input>
            </template>
          </vxe-column>
          <vxe-column field="lessScore" title="小于" :edit-render="{}" v-if="isshowclounfuc('lessScore')"
            :width="templatepage ? 210 : 90">
            <template #header>
              <span style="color: #F56C6C; margin: 0 7px 0 0">*</span>
              <span>小于</span>
            </template>
            <template #default="{ row }">
              {{ module === 'threetable' || module === 'fourtable' ? (row.lessScore !== null && row.lessScore !==
                undefined ? (row.lessScore === 0 ? '0' : (row.lessScore + '%')) : '') : (module === 'fivetable' || module
                  === 'sixtable' ? (row.lessScore !== null && row.lessScore !== undefined ? (row.lessScore === 0 ? '0' :
                    row.lessScore) : '') : '') }}
            </template>
            <template #edit="{ row }">
              <el-input v-model="row.lessScore" type="number" placeholder="请输入" :maxlength="3" :max="100" pattern="^\d+$"
                @blur="performancescoreverify($event)"></el-input>
            </template>
          </vxe-column>
          <vxe-column field="commissionReduced" title="提成比例减" :edit-render="{}" v-if="isshowclounfuc('commissionReduced')"
            :width="templatepage ? 210 : 110">
            <template #header>
              <span style="color: #F56C6C; margin: 0 7px 0 0">*</span>
              <span>提成比例减</span>
            </template>
            <template #default="{ row }">
              {{ row.commissionReduced !== null && row.commissionReduced !== undefined ? (row.commissionReduced === 0 ?
                '0' : row.commissionReduced + '%') : '' }}
            </template>
            <template #edit="{ row }">
              <el-input v-model="row.commissionReduced" type="number" placeholder="请输入" :maxlength="3" :max="100"
                pattern="^\d+$" @blur="commissionreducedverify($event)"></el-input>
            </template>
          </vxe-column>

          <vxe-column field="performanceScore" title="绩效分" :edit-render="{}" v-if="isshowclounfuc('performanceScore')"
            :width="templatepage ? 210 : 100">
            <template #header>
              <span style="color: #F56C6C; margin: 0 7px 0 0">*</span>
              <span>绩效分</span>
            </template>
            <template #edit="{ row }">
              <el-input v-model="row.performanceScore" type="number" placeholder="请输入" :maxlength="3" :max="100000"
                pattern="^\d+$" @blur="performancescoreverify($event)"></el-input>
            </template>
          </vxe-column>

          <vxe-column title="操作">
            <template #default="{ row }">
              <template v-if="$refs.xTable.isActiveByRow(row)">
                <el-button @click="saveRowEvent(row, 1)">保存</el-button>
                <el-button @click="cancelRowEvent(row, 1)">取消</el-button>
              </template>
              <template v-else>
                <el-button size="mini" @click="editRowEvent(row, 1)">编辑</el-button>
                <el-button size="mini" type="danger" plain @click="deletecomparison(row)">删除</el-button>
              </template>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
    </div>
  </div>
</template>

<script>
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import {
  getTimeLimitDrawingList, addOrUpdateTimeLimitDrawingList, delTimeLimitDrawingList,
  addOrUpdateInTimeRate, delInTimeRate,
  addOrUpdateAualityAdaptabilityDeduct, delAualityAdaptabilityDeduct, saveDeptManagerAssess
} from '@/api/media/shootingset'

export default {
  name: 'commissionAppraisalForm',
  props: {
    tableData: { type: Array, default: () => [] },
    ishowarr: { type: Array, default: () => [] },
    module: { type: String, default: () => "" },
    templatepage: { type: Boolean, default: () => false },
  },
  components: { vxetablebase },
  data() {
    return {
      styleName: false,
      urgencyJiDay: false,
      urgencyDay: false,
      normalDay: false,
      styletype: false,
      equalOrGreaterScore: false,
      lessScore: false,
      commissionReduced: false,
      ishowarrall: ['styleName', 'urgencyJiDay', 'urgencyDay', 'normalDay', 'styletype', 'equalOrGreaterScore', 'lessScore', 'commissionReduced', 'performanceScore'],
      listLoading: false,
      addedcomparisondata: false,
      addForm: {
        Normalstyle: null,
        Urgentpayment: null,
        Emergencypayment: null,
        Shootingstyle: null,
      },
      editRow: {
        row: null,
        index: -1
      },
      loading: false,
      posttableData: [],
      sectiontableData: [],
      editBackup: {}, //备份编辑前的数据
      secteditBackup: {}, //备份编辑前的数据
      error: false,
      verify: false,
      examinationname: null,
      textprompt: null,
      options: [{
        value: '1',
        label: '出款时效（岗位）'
      }, {
        value: '2',
        label: '出款时效（部门）'
      }, {
        value: '3',
        label: '及时完成率对应提成（岗位）'
      }, {
        value: '4',
        label: '及时完成率对应提成（部门）'
      }, {
        value: '5',
        label: '质量+配合度对应提成（岗位）'
      }, {
        value: '6',
        label: '质量+配合度对应提成（部门）'
      }],
    };
  },

  async mounted() {
    // await this.onSearch()
    this.ishowarr.forEach((item) => {
      if ([1, 2, 3, 4, 5, 6].includes(item)) {
        const option = this.options.find((opt) => opt.value === item.toString());
        if (option) {
          this.examinationname = option.label;
        }
      }
    });
    if (this.ishowarr.includes(3) || this.ishowarr.includes(4)) {
      this.textprompt = '所有款式及时完成率对应以下行条件';
    } else if (this.ishowarr.includes(5) || this.ishowarr.includes(6)) {
      this.textprompt = '所有款式质量+配合度平均分值对应以下行条件';
    } else {
      this.textprompt = null
    }
  },

  methods: {
    handleInput(row) {
      if (row.urgencyJiDay && row.urgencyJiDay.length > 3) {
        row.urgencyJiDay = row.urgencyJiDay.slice(0, 3);
      }
    },
    isshowclounfuc(fild) {
      var istrue = false;
      this.ishowarr.map((item) => {
        if (this.ishowarrall.indexOf(item) >= 0 && item == fild) {
          istrue = true;
        }
      })
      return istrue;
    },
    performancescoreverify(e) {
      let value = e.target.value;
      if (value > 100000) {
        this.$message.error('输入的值不能超过100000');
        e.target.value = ' '
        this.verify = true
        setTimeout(() => {
          this.verify = false;
        }, 2000);
        return
      }

      let boolean = new RegExp("^[0-9]+(\.[0-9]+)?$").test(e.target.value);
      if (!boolean) {
        this.$message.error('请输入整数或小数');
        e.target.value = '';
        this.verify = true;
        setTimeout(() => {
          this.verify = false;
        }, 2000);
        return;
      }
    },
    commissionreducedverify(e) {
      let value = e.target.value;
      if (value > 100) {
        this.$message.error('输入的值不能超过100');
        e.target.value = ' '
        this.verify = true
        setTimeout(() => {
          this.verify = false;
        }, 2000);
        return
      }
      let boolean = new RegExp("^[0-9]*$").test(e.target.value)
      if (!boolean) {
        this.$message.error('请输入正整数');
        e.target.value = ' '
        this.verify = true
        setTimeout(() => {
          this.verify = false;
        }, 2000);
        return
      }
    },
    BlurText(e) {
      let value = e.target.value;
      if (value > 100) {
        this.$message.error('输入的值不能超过100');
        e.target.value = ' '
        this.verify = true
        setTimeout(() => {
          this.verify = false;
        }, 2000);
        return
      }

      let boolean = new RegExp("^[1-9][0-9]*$").test(e.target.value)
      if (!boolean) {
        this.$message.error('请输入正整数');
        e.target.value = ' '
        this.verify = true
        setTimeout(() => {
          this.verify = false;
        }, 2000);
        return
      }
    },
    async onSearch() {
      await this.getlist();
    },
    async getlist() {
      this.listLoading = true
      const { data } = await getTimeLimitDrawingList()
      this.posttableData = data.postList
      this.sectiontableData = data.deptList
      this.listLoading = false
    },
    //删除
    async deletecomparison(row) {

      this.$confirm("是否删除数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let success = false;
          if (this.module === 'onetable' || this.module === 'twotable') {
            ({ success } = await delTimeLimitDrawingList({ id: row.id }));
          } else if (this.module === 'threetable' || this.module === 'fourtable') {
            ({ success } = await delInTimeRate({ id: row.id }));
          } else {
            ({ success } = await delAualityAdaptabilityDeduct({ id: row.id }));
          }
          if (success) {
            this.$message({ message: '删除成功！', type: 'success' });
            this.$emit('templatepageclose');
          }
        })
    },

    //新增
    newline() {
      if (this.module == 'onetable' || this.module == 'twotable') {
        var newObject = {
          id: 0,
          normalDay: null,
          urgencyDay: null,
          urgencyJiDay: null,
          styleName: '',
        };
      } else if (this.module == 'threetable' || this.module == 'fourtable') {
        var newObject = {
          id: 0,
          equalOrGreaterScore: null,
          lessScore: null,
          styleName: '',
          commissionReduced: null,
          performanceScore: null,
        };
      } else if (this.module == 'fivetable' || this.module == 'sixtable') {
        var newObject = {
          id: 0,
          performanceScore: null,
          lessScore: null,
          equalOrGreaterScore: null,
          commissionReduced: null,
        };
      }
      this.tableData.push(newObject);
    },
    //编辑
    editRowEvent(row, val) {
      row.isEdit = true;
      if (val == 1) {
        this.editBackup = Object.assign({}, row);
        var $table = this.$refs.xTable
      }
      $table.setActiveRow(row)
    },
    //保存
    saveRowEvent(row, val) {
      if (this.verify == true) {
        return
      }
      if (parseFloat(row.lessScore) < parseFloat(row.equalOrGreaterScore)) {
        this.$message.error('“大于或等于”必须小于“小于”');
        return;
      }
      if (this.module === 'onetable' || this.module === 'twotable') {
        if (!this.checkRequiredFields(row, ['urgencyJiDay', 'urgencyDay', 'normalDay'])) {
          return;
        }
      } else if (this.module === 'fourtable') {
        if (!this.checkRequiredFields(row, ['styleName', 'equalOrGreaterScore', 'lessScore', 'commissionReduced', 'performanceScore'])) {
          return;
        }
      } else if (this.module === 'threetable') {
        if (!this.checkRequiredFields(row, ['equalOrGreaterScore', 'lessScore', 'commissionReduced', 'performanceScore'])) {
          return;
        }
      } else {
        if (!this.checkRequiredFields(row, ['equalOrGreaterScore', 'lessScore', 'performanceScore'])) {
          return;
        }
      }

      if (val == 1) {
        const $table = this.$refs.xTable
        $table.clearActived().then(() => {
          setTimeout(() => {
            this.jobpreservation(1, row)
          }, 300)
        })
      }
    },
    checkRequiredFields(row, requiredFields) {
      for (let field of requiredFields) {
        if (row[field] === null) {
          this.$message({
            message: '请填写完整必填项！',
            type: 'warning'
          });
          return false;
        }
      }
      return true;
    },
    //取消
    cancelRowEvent(row, val) {
      if (val == 1) {
        Object.assign(row, this.editBackup);
        var $table = this.$refs.xTable
      }
      row.isEdit = false;
      $table.clearActived().then(() => {
        $table.revertData(row)
      })
    },
    async jobpreservation(val, row) {
      if (row.urgencyJiDay === null || row.urgencyDay === null || row.normalDay === null) {
        this.$message({
          message: '请填写完整必填项！',
          type: 'warning'
        });
        return;
      }
      row.urgencyJiDay = Number(row.urgencyJiDay);
      row.urgencyDay = Number(row.urgencyDay);
      row.normalDay = Number(row.normalDay);


      if (this.module === 'onetable' || this.module === 'threetable' || this.module === 'fivetable') {
        val = 1;
      } else if (this.module === 'twotable' || this.module === 'fourtable' || this.module === 'sixtable') {
        val = 2;
      }
      if (this.module === 'onetable' || this.module === 'twotable') {
        const params = { ...row, typeId: val };
        const { success } = await addOrUpdateTimeLimitDrawingList(params)
        if (success) {
          this.$message({ message: '保存成功！', type: 'success' });
          this.$emit('templatepageclose');
        }
      } else if (this.module === 'threetable' || this.module === 'fourtable') {
        if (val == 1) {
          row.styleName = null
        }
        const params = { ...row, typeId: val };
        const { success } = await addOrUpdateInTimeRate(params)
        if (success) {
          this.$message({ message: '保存成功！', type: 'success' });
          this.$emit('templatepageclose');
        }
      } else {
        const params = { ...row, typeId: val };
        const { success } = await addOrUpdateAualityAdaptabilityDeduct(params)
        if (success) {
          this.$message({ message: '保存成功！', type: 'success' });
          this.$emit('templatepageclose');
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.custom-input {
  position: relative;
  z-index: 9999;
  /* 设置一个较高的 z-index 值 */
}

.input-wrapper {
  position: relative;
  z-index: 10;
}

::v-deep .el-drawer__header {
  margin-bottom: 25px !important;
  padding-bottom: 25px !important;
  border-bottom: 1px #dcdfe6 solid !important;
}

.xptcblbj {
  width: 100%;
  box-sizing: border-box;
  padding: 0 35px;

}

.xptcblbj2 {
  width: 100%;
  box-sizing: border-box;
  padding: 10px 50px;
  display: flex;
  height: 31%;
  // overflow: auto;
}

.outer {
  height: 860px;
  overflow: auto;
}

.xptcbl {
  width: 100%;
  margin-bottom: 30px;
  // height: 260px;
  // background-color: aqua;
}

.xptcblmc {
  width: 12%;
  color: #333;
  background-color: beige;
  display: flex;
  align-items: center;
}

.xptcblts {
  background-color: bisque;
  display: flex;
  align-items: center;
}


.jxkhcj {
  width: 100%;
  height: 300px;
  box-sizing: border-box;
  display: flex;
}

//字体样式
.lxwz {
  width: 130px;
  font-size: 14px; //字体大小
  color: #666; //颜色
  line-height: 30px; //行高
}
</style>
