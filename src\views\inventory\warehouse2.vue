<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="分仓:">
          <el-select v-model="filter.warehouse" placeholder="请选择分仓" style="width: 100%">
            <el-option label="请选择" value></el-option>
            <el-option label="义乌" value="0"></el-option>
            <el-option label="昌东" value="1"></el-option>
            <el-option label="安徽" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商品编码:">
          <el-input v-model="filter.proBianMa" placeholder="商品编码"/>
        </el-form-item>
        <el-form-item label="商品编码名称:">
          <el-input v-model="filter.proBianMaName" placeholder="商品名称"/>
        </el-form-item>
        <!-- <el-form-item label="排除计算:">
          <el-select v-model="filter.removeCalc" placeholder="请选择运费计算">
            <el-option label="全部" value></el-option>
            <el-option label="是" value="true"></el-option>
            <el-option label="否" value="false"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="重量:">
         <el-row style="width: 180px;padding: 0">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-input type="number" placeholder="最小值" v-model="filter.minWeight"   />
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-input type="number" placeholder="最大值" v-model="filter.maxWeight"  />
          </el-col>
        </el-row>
       </el-form-item>
       <el-form-item label="数量:">
         <el-row style="width: 180px;padding: 0">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-input type="number" placeholder="最小值" v-model="filter.minCount"  />
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-input type="number" placeholder="最大值" v-model="filter.maxCount"  />
          </el-col>
        </el-row>
       </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' :isSelectColumn='false' @sortchange='sortchange'
        :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
    <el-drawer :title="formtitle" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="addFormVisible" 
                direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
    <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
     <div>
       <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
          <el-row style="width: 620px;padding: 0;padding-left: 50px">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="不计算:">
                    <el-cascader v-model="noCalcs" :options="buModuleOptions" :props="buModuleProps" :show-all-levels="false" collapse-tags clearable ></el-cascader>
               </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="不显示:">
                    <el-cascader v-model="noDisplays" :options="buModuleOptions" :props="buModuleProps" :show-all-levels="false" collapse-tags clearable></el-cascader>
               </el-form-item>
            </el-col>
          </el-row>
      </el-form>
     </div>
      <div class="drawer-footer">
        <el-button @click.native="addFormVisible = false">取消</el-button>
        <my-confirm-button type="submit"  :loading="addLoading" @click="onAddSubmit" />
      </div>
    </el-drawer>
  </my-container>
</template>

<script>
import {updateWarehouse,getWarehousesigle,getPageWarehouseSub} from '@/api/inventory/warehouse'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatWarehouse,formatSecondToHour,formatmoney,formatBianMaRemoveCalc} from "@/utils/tools";
import {getAllBuModule} from '@/api/admin/business'
const tableCols =[
      {istrue:true,prop:'warehouse',label:'分仓', width:'75',sortable:'custom',formatter:(row)=>formatWarehouse(row.warehouse)},
      {istrue:true,prop:'proBianMa',label:'商品编码', width:'130',sortable:'custom'},
      {istrue:true,prop:'proBianMaName',label:'商品编码名称', width:'200'},
      {istrue:true,prop:'image',label:'图片', width:'60',type:'image'},
      {istrue:true,prop:'count',label:'数量', width:'80',sortable:'custom',formatter:(row)=>formatmoney(row.count)},
      {istrue:true,prop:'removeCalcList',label:'排除计算', width:'100',formatter:(row)=>formatBianMaRemoveCalc(row.removeCalcList)},
      {istrue:true,prop:'weight',label:'重量(kg)', width:'100',sortable:'custom'},
      {istrue:true,prop:'volumeWeight',label:'计泡重量(kg)', width:'110',sortable:'custom'},
      {istrue:true,prop:'lastInTransitTime',label:'在途时长', width:'90',sortable:'custom',formatter:(row)=>formatSecondToHour(row.lastInTransitTime)},
      {istrue:true,prop:'avgInTransitTime',label:'平均在途时长', width:'110',sortable:'custom',formatter:(row)=>formatSecondToHour(row.avgInTransitTime)},
      // {istrue:true,prop:'noCalc',label:'不计算', width:'110',sortable:'custom'},
      // {istrue:true,prop:'noDisplay',label:'不显示', width:'110',sortable:'custom'},
     ];
const tableHandles1=[
        //{label:"新增", handle:(that)=>that.onAdd()},
        //{label:'编辑', handle:(that)=>that.onEdit()}
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: {
          warehouse:null,
          proBianMa:'',
          proBianMaName:'', 
          removeCalc :null,
          maxWeight :null,
          minWeight :null, 
          maxCount :null,
          minCount :null,
      },
      list: [],
      pager:{OrderBy:"id",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      platFormList:[],
      autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 22 }}}},
               rule:[ ]
        },
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,     
      deleteLoading: false,
      formtitle:"新增",
      groupList:[],
      brandlist:[],
      buModuleOptions:[],
      buModuleProps: { multiple: true ,},//checkStrictly: true,
      noCalcs:[],
      noDisplays:[],
    }
  },
  async mounted() {
    this.getlist();
    await this.initform();
    await this.initformBuModule();
    //await this.setBandSelect();
    //await this.setGroupSelect();
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
     async initform(){
       let that=this;
       this.autoform.rule= [{type:'hidden',field:'id',title:'id',value: ''},
                     //{type:'input',field:'proBianMaName',title:'商品编码名称',value: '',validate: [{type: 'string', required: true, message:'请输入商品编码名称'}]},props:{multiple: true,clearable: true},
                     {type:'input',field:'proBianMaName',title:'商品编码名称',value: ''},
                    //  {type:'cascader',field:'noCalcs',title:'不计算', ...await ruleBusinessModul(),col:{span: 6}},
                    //  {type:'cascader',field:'noDisplays',title:'不显示', ...await ruleBusinessModul(),col:{span: 6}},
                     {type:'inputNumber',field:'weight',title:'重量(kg)',value: 0,col: { span: 6 }},
                     {type:'inputNumber',field:'volumeWeight',title:'计泡重量(kg)',value: 0,col: { span: 6 }},
                ]
                //console.log('this.autoform.rule',this.autoform.rule)
    },
    async initformBuModule(){
      var res=await getAllBuModule();
      if (!res?.success) {return }
      this.buModuleOptions=[];
      res.data.forEach(f=>{
        var children=[];
        if(f.items) f.items.forEach(c=>children.push({value:c.code,label:c.name})) 
        this.buModuleOptions.push({value:f.code,label:f.name,children:children}) 
      })
    },
    async onSearch() {      
      if(this.filter.minCount<-99999999||this.filter.minCount>99999999||this.filter.maxCount<-99999999||this.filter.maxCount>99999999)
      {
        this.$message({ message: '数量输入错误', type: "error" });
        return;
      }
      if(this.filter.minWeight<-99999999||this.filter.minWeight>99999999||this.filter.maxWeight<-99999999||this.filter.maxWeight>99999999)
      {
        this.$message({ message: '重量输入错误', type: "error" });
        return;
      }

      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      this.listLoading = true
      const res = await getPageWarehouseSub(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
  async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
  async onEdit(row) {
      this.formtitle='编辑';
      this.addFormVisible = true
      const res = await getWarehousesigle({id:row.id})
      this.noCalcs=[]
      this.noDisplays=[]
      if (res.data.noCalc1) {         
          res.data.noCalc1.forEach(f=>{
             this.noCalcs.push([f.split(',')[0],f.split(',')[1]]);
          })
      }
      if (res.data.noDisplay1) {         
          res.data.noDisplay1.forEach(f=>{
             this.noDisplays.push([f.split(',')[0],f.split(',')[1]]);
          })
      }
      console.log('this.noCalcs',this.noCalcs)
      console.log('this.noDisplays',this.noDisplays)
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
         this.autoform.fApi.resetFields()
      await this.autoform.fApi.setValue(res.data)
    },  
  async onEditSubmit() {
      this.addFormVisible = true
      await onAddSubmit();
    },
  async onAddSubmit() {
      this.addLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          formData.id=formData.id?formData.id:0;
          formData.Enabled=true;
          formData.noCalcs=[];
          formData.noDisplays=[];
          this.noCalcs.forEach(f=>{
             formData.noCalcs.push(f[1]);
          })
          this.noDisplays.forEach(f=>{
             formData.noDisplays.push(f[1]);
          }) 
          const res = await updateWarehouse(formData);
          if(res.code==1){
            this.getlist();
            this.addFormVisible=false;
          }
        }else{
          //todo 表单验证未通过
        }
     })
     this.addLoading=false;
    },
    selsChange: function(sels) {
      this.sels = sels
    }
  }
}
</script>
