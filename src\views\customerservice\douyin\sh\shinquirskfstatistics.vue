<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :summaryarry="summaryarry" :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='groupinquirsstatisticslist' @select='selectchange' :isSelection='false'
            :tableCols='tableCols' :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.GroupNameList"   placeholder="分组"  clearable filterable multiple :collapse-tags="true">
                            <el-option v-for="item in filterGroupList" :key="item" :label="item" :value="item">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.sname" placeholder="姓名" style="width:120px;" clearable
                            :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <datepicker v-model="filter.sdate"></datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"
                @get-page="getgroupinquirsstatisticsList" />
        </template>

        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
            :close-on-click-modal="false" v-dialogDrag>
            <div>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="个人服务数据按店统计" :visible.sync="dialogVisibleSyj" width="50%" :close-on-click-modal="false"
            v-dialogDrag>
            <span>
                <shinquirskfstatisticsbyshop v-if="dialogVisibleSyj" ref="shinquirskfstatisticsbyshop"
                    style="height: 580px;" />
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="服务数据统计" :visible.sync="dialogVisibleSyjfw" width="70%" :close-on-click-modal="false" v-dialogDrag>
            <span>
                <shinquirskfdtl1 v-if="dialogVisibleSyjfw" ref="shinquirskfdtl1" style="height: 580px;" />
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyjfw = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import {
    getDouYinGroup,
    getDouYinPersonalEfficiencyKfPageList, getDouYinGroupEfficiencyChat
} from '@/api/customerservice/douyininquirs'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar'
import shinquirskfstatisticsbyshop from '@/views/customerservice/douyin/sh/shinquirskfstatisticsbyshop'
import shinquirskfdtl1 from '@/views/customerservice/douyin/sh/shinquirskfdtl1'
const tableCols = [
    { istrue: true, prop: 'groupName', label: '组名称', width: '120', sortable: 'custom' },
    {
        istrue: true, prop: 'sname', label: '姓名', width: '80', sortable: 'custom', type: "click",
        handle: (that, row, column, cell) => that.canclick(row, column, cell), formatter: (row) => row.sname
    },
    { istrue: true, prop: 'allCount', label: '评价数量', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'buManYiRate', label: '不满意率', width: '100', sortable: 'custom', formatter: (row) => row.buManYiRate + "%" },
    {
        istrue: true, prop: 'buManYiCount', label: '不满意人数', width: '100', sortable: 'custom', type: "click",
        handle: (that, row, column, cell) => that.canclick2(row, column, cell, "不满意")
    },
    { istrue: true, prop: 'manYiRate', label: '满意率', width: '100', sortable: 'custom', formatter: (row) => row.manYiRate + "%" },
    {
        istrue: true, prop: 'manYiCount', label: '满意人数', width: '100', sortable: 'custom', type: "click",
        handle: (that, row, column, cell) => that.canclick2(row, column, cell, "满意")
    },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar, shinquirskfstatisticsbyshop, shinquirskfdtl1 },
    props:["partInfo"],
    data() {
        return {
            dialogMapVisible: { visible: false, title: "", data: [] },
            that: this,
            filter: {
                groupType: 1,
                inquirsType: 1,
            },
            shopList: [],
            userList: [],
            filterGroupList: [],
            groupList: [],
            groupinquirsstatisticslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "groupName", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false, dialogVisibleSyjfw: false,
            fileList: [],
            isleavegroup:this.partInfo,//是否离组
        };
    },
    watch:{
        partInfo(){
            this.isleavegroup = this.partInfo;
            this.getDouYinGroup();
        }
    },
    async mounted() {
         this.isleavegroup = this.partInfo;
        await this.getDouYinGroup();
        window.showlist47 = this.showlist47;
    },
    methods: {
        showlist47(groupName, startdate, enddate) {
            this.filter.groupName = groupName;

            this.filter.sdate = [startdate, enddate];
            if (startdate == null || enddate == null)
                this.filter.sdate = ["", ""];
            this.filter.startDate = startdate;
            this.filter.endDate = enddate;
            this.onSearch()
        },
        async getDouYinGroup() {
            let groups = await getDouYinGroup({ groupType: 1 ,isleavegroup:this.isleavegroup});
            if (groups?.success && groups?.data && groups?.data.length > 0) {
                this.filterGroupList=groups.data;
            }
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getgroupinquirsstatisticsList();
        },
        async getgroupinquirsstatisticsList() {
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,

            };
            this.listLoading = true;
            const res = await getDouYinPersonalEfficiencyKfPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.groupinquirsstatisticslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async showchart(row) {
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            params.groupName = row.groupName;
            const res = await getDouYinGroupEfficiencyChat(params).then(res => {
                if (res) {
                    this.dialogMapVisible.visible = true;
                    this.dialogMapVisible.data = res;
                    this.dialogMapVisible.title = res.title;
                    res.title = "";
                }
            });
            this.dialogMapVisible.visible = true;
        },
        async canclick(row, column, cell) {
            var fstartsdate = "";
            var fendsdate = "";
            if (this.filter.sdate) {
                var d = new Date(this.filter.sdate[0])
                fstartsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
                d = new Date(this.filter.sdate[1])
                fendsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
            }
            if (fstartsdate == "NaN-NaN-NaN") {
                fstartsdate = "";
                fendsdate = "";
            }
            var fsname = row.sname;
            var fgroupName = row.groupName;
            this.dialogVisibleSyj = true;
            this.$nextTick(() => {
                this.$refs.shinquirskfstatisticsbyshop.dialogOpenAfter({
                    startDate: fstartsdate,
                    endDate: fendsdate,
                    sname: fsname,
                    groupName: fgroupName,
                });
            });
        },
        async canclick2(row, column, cell, isManYi) {
            var fstartsdate = "";
            var fendsdate = "";
            if (this.filter.sdate) {
                var d = new Date(this.filter.sdate[0])
                fstartsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
                d = new Date(this.filter.sdate[1])
                fendsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
            }
            if (fstartsdate == "NaN-NaN-NaN") {
                fstartsdate = "";
                fendsdate = "";
            }
            var fsname = row.sname;
            var fgroupName = row.groupName;
            this.dialogVisibleSyjfw = true;
            this.$nextTick(() => {
                this.$refs.shinquirskfdtl1.dialogOpenAfter({
                    startDate: fstartsdate,
                    endDate: fendsdate,
                    sname: fsname,
                    groupName: fgroupName,
                    isManYi: isManYi,
                });
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 60px;
}
</style>
