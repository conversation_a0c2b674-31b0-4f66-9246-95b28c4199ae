import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/customergroup/`

 
export const getcusgroups = (params, config = {}) => {
    return request.get(apiPrefix + 'getcustomergroups', { params: params, ...config })
}
export const addcusgroup = (params, config = {}) => {
    return request.get(apiPrefix + 'AddCustomerGroups', { params: params, ...config })
}
export const deletecusgroup = (params, config = {}) => {
    return request.get(apiPrefix + 'DeleteCustomerGroups', { params: params, ...config })
}
 
 