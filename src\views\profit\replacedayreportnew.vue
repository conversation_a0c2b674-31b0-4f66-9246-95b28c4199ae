<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="付款时间">
                    <el-date-picker style="width: 200px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <el-form-item label="内部订单号:">
                    <el-input v-model.trim="filter.orderNoInner" style="width: 150px" placeholder="内部订单号"
                        @keyup.enter.native="onSearch" clearable />
                </el-form-item>
                <el-form-item label="商品编码:">
                    <el-input v-model.trim="filter.goodsCode" style="width: 150px" placeholder="商品编码"
                        @keyup.enter.native="onSearch" clearable />
                </el-form-item>
                <el-form-item label="所属店铺:">
                    <el-select filterable v-model="filter.shopCode" placeholder="请选择店铺" clearable style="width: 120px">
                        <el-option v-for="item in shopList" :key="item.id" :label="item.shopName"
                            :value="item.shopCode" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
            @select="selectchange" :tableHandles='tableHandles' @cellclick="cellclick" :loading="listLoading">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="40%">
            <div>
                <el-alert type="warning" show-icon :closable="false"
                    title="温馨提示:导入的代拍时间需要与所选月份相同"></el-alert>
            </div>
            <span>
                <el-form class="ad-form-query" :inline="true" :model="importDialog.filter" @submit.native.prevent>
                    <el-form-item label="结算月份:">
                        <el-date-picker style="width: 120px" v-model="importDialog.filter.settMonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="选择" :picker-options="pickOptions">
                        </el-date-picker>
                    </el-form-item>
                </el-form>
            </span>
            <span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="4" action
                    accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }} </el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog :visible.sync="showDetailVisible" width="80%" :show-close="false" v-dialogDrag>
            <div style="height:680px;">
                <replacedayreportdetail ref="replacedayreportdetail" @nSearch="nSearch" style="height:100%;">
                </replacedayreportdetail>
            </div>
        </el-dialog>

    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime, formatTime1} from "@/utils";
import { importReplaceDayReportNew, getReplaceDayReportNewList, exportReplaceDayReportNewList } from "@/api/profit/reportday"
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import replacedayreportdetail from "./replacedayreportdetail.vue"

const tableCols = [
    { istrue: true, prop: 'flewCode', label: '流程编码', tipmesg: '', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'replaceTime', label: '代拍时间', tipmesg: '', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'proCode', label: '商品ID', tipmesg: '', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'brandId', label: '采购员', tipmesg: '', width: '100', sortable: 'custom', formatter: (row) => !row.brandName ? " " : row.brandName },
    { istrue: true, prop: 'shopCode', label: '店铺名', tipmesg: '', width: '100', formatter: (row) => !row.shopName ? " " : row.shopName },
    { istrue: true, prop: 'goodsCode', label: '商品编码', tipmesg: '', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: '125', },
    { istrue: true, prop: 'qty', label: '数量', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'payAmount', label: '买家支付金额', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'orderNo', label: '线上订单号（聚水潭公司店铺）', sortable: 'custom', tipmesg: '', width: '150', },
    { istrue: true, prop: 'payOrderNo', label: '支付订单编号（线上线下付款）', tipmesg: '', width: '150', sortable: 'custom', },
    { istrue: true, prop: 'orderNoInner', label: '内部订单号', tipmesg: '', width: '100', sortable: 'custom',type:'orderLogInfo',orderType:'orderNoInner'  },
    { istrue: true, prop: 'payTime', label: '付款时间', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'expCompanyName', label: '快递公司', tipmesg: '', width: '100', sortable: 'custom', formatter: (row) => !row.expCompanyName ? " " : row.expCompanyName },
    { istrue: true, prop: 'expressNo', label: '快递单号', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'replaceAmount', label: '代拍金额', tipmesg: '', width: 'auto', sortable: 'custom', },
    { istrue: true, prop: 'purchaseDitch', label: '采购渠道', tipmesg: '', width: 'auto', sortable: 'custom', },
    { istrue: true, prop: 'platform', label: '平台', tipmesg: '', width: 'auto', sortable: 'custom', formatter: (row) => !row.platformName ? " " : row.platformName },
    { istrue: true, prop: 'dWCost', label: '系统单位成本', tipmesg: '订单管理-订单列表中的内部单号+商品编码成本价，其次取内部单号对应的成本价', width: 'auto', sortable: 'custom', },
    { istrue: true, prop: 'xTCost', label: '系统成本', tipmesg: '数量*系统单位成本', width: 'auto', sortable: 'custom', },
    { istrue: true, prop: 'refundAmount', label: '订单退款金额', tipmesg: '', width: 'auto', sortable: 'custom', },
    { istrue: true, prop: 'amountCost', label: '销售成本', tipmesg: '订单管理-订单列表中的内部单号+商品编码对应的付款时间,其次取内部单号对应的"成本价"*订单列表中的数量', width: 'auto', sortable: 'custom', },
    { istrue: true, prop: 'operateCost', label: '运营费用', tipmesg: '对应平台日报中的商品ID付款月份该ID的总广告/销售金额*（买家支付金额-订单退款金额）', width: 'auto', sortable: 'custom', },
    { istrue: true, prop: 'billCost', label: '账单费用', tipmesg: '对应平台日报中的商品ID付款月份该ID的扣点合计/销售金额*（买家支付金额-订单退款金额）', width: 'auto', sortable: 'custom', },
    { istrue: true, prop: 'profit', label: '订单利润', tipmesg: '买家付款金额-退款金额-运营费用-账单费用-（代拍成本-系统成本）-销售成本', width: 'auto', sortable: 'custom', },
]

const tableHandles = [
    { label: "导入", handle: (that) => that.startImport() },
    { label: "导出", handle: (that) => that.onExportDetail() },
    { label: "模板-代拍导入模板", handle: (that) => that.downloadOrherTemplate() },
];

const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");
const curMonth =formatTime1(dayjs().startOf("month").subtract(1,"month"),"yyyyMM");

export default {
    name: 'YunHanAdminReplacedayreportnew',
    components: { container, cesTable, MyConfirmButton, replacedayreportdetail },

    data() {
        return {
            that: this,
            filter: {
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime],
                procode: null,
                title: null,
                platform: null,
                shopCode: null,
                groupId: null,
                operateSpecialId: null,
                operateName: null,
                user3Id: null,
                shopId: null,
                newPattern: null,
                customer: null,
                status: null,
                refundStatus: null
            },
            importDialog:{
                filter:{
                    settMonth:curMonth,
                }
            },
            list: [],
            shopList: [],
            summaryarry: {},
            pager: { OrderBy: "payTime", IsAsc: false },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            pickOptions:{
                disabledDate(time){
                return time.getTime() > Date.now()
                }
            },
            onHandNumber: null,
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            sels: [],
            editparmLoading: false,
            uploadLoading: false,
            editparmLoading1: false,
            editparmLoading2: false,
            editparmVisible: false,
            editparmVisible1: false,
            editparmVisible2: false,
            dialogVisible: false,
            listLoading: false,
            showDetailVisible: false,
        };
    },

    async mounted() {
        await this.onSearch()
        await this.onchangeplatform()
    },

    methods: {
        //获取店铺
        async onchangeplatform() {
            this.categorylist = []
            const res1 = await getshopList({ platform: null, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await getReplaceDayReportNewList(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            this.list = data
        },
        //导出
        async onExportDetail() {
            let page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...page, ... this.filter }

            let loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
            let res = await exportReplaceDayReportNewList(params);
            loadingInstance.close();
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '代拍日报详情_' + new Date().toLocaleString() + '.xlsx')
            aLink.click();
        },
        async showAmont(row) {
            if (row.orderCount === 1) return
            this.showDetailVisible = true
            let param = { orderNoInner: row.orderNoInner }
            let that = this
            that.$nextTick(async () => {
                await that.$refs.replacedayreportdetail.onSearch1(param)
            })

        },
        renderAmont(row) {
            if (row.orderCount > 1) return "color:blue;cursor:pointer;";
            else return "";
        },
        async nSearch() {
            await this.getlist()
        },
        //字体颜色
        renderRefundStatus(row) {
            if (row.refundStatus == '成功退款' || row.refundStatus == '等待退款') {
                return "color:red;cursor:pointer;";
            } else return "";
        },
        //开始导入
        startImport() {
            this.dialogVisible = true;
        },
        //取消导入
        cancelImport() {
            this.dialogVisible = false;
        },
        uploadSuccess(response, file, fileList) {
            if (response.code == 200) {
            } else {
                fileList.splice(fileList.indexOf(file), 1);
            }
        },
        async submitUpload() {
            if(!this.importDialog.filter.settMonth){
                this.$message({message: "请选择结算月份", type: "warning" });
                return false;
            }
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请先选取文件", type: "warning" });
                return false;
            }
            this.fileHasSubmit = true;
            this.uploadLoading = true;
            this.$refs.upload.submit();
        },
        async uploadFile(item) {
            if (!this.fileHasSubmit) {
                return false;
            }
            this.fileHasSubmit = false;
            this.uploadLoading = true;
            const form = new FormData();
            form.append("token", this.token);
            form.append("upfile", item.file);
            form.append("settMonth", this.importDialog.filter.settMonth);

            const res = await importReplaceDayReportNew(form);
            if (res.code == 1) {
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
                this.$refs.upload.clearFiles();
                this.dialogVisible = false;
            }
            else this.$message({ message: res.msg, type: "warning" });
            this.uploadLoading = false;
        },
        async uploadChange(file, fileList) {
            if (fileList && fileList.length > 0) {
                var list = [];
                for (var i = 0; i < fileList.length; i++) {
                    if (fileList[i].status == "success")
                        list.push(fileList[i]);
                    else
                        list.push(fileList[i].raw);
                }
                this.fileList = list;
            }
        },
        async uploadRemove(file, fileList) {
            this.uploadChange(file, fileList);
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event) {

        },
        async downloadOrherTemplate() {
            window.open("../static/excel/dayreport/新版代拍导入模板.xlsx", "_self");
        },
    }
};
</script>

<style lang="scss" scoped>

</style>
