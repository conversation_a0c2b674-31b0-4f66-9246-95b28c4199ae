<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
               
                <el-form-item label="编码:">
                    <el-input v-model.trim="filter.Coding" style="width: 150px" placeholder="请输入编码" @keyup.enter.native="onSearch" clearable/>
                </el-form-item>
              
         <el-form-item label="运营组:" >
          <el-select  filterable v-model="filter.operating" placeholder="请选择运营组" class="el-select-content"  style="width:150px;" clearable>
            <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="运营负责人:">
          <el-select  filterable v-model="filter.Procurement"  placeholder="请选择运营负责人"  style="width:150px;" clearable>
           
            <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.value"/>
          </el-select>
        </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
            :tableData='list'  :tableCols='tableCols' :isSelection="false" @select="selectchange"
            :tableHandles='tableHandles' @cellclick="cellclick"
            :loading="listLoading">
            </ces-table>
            <template #footer>
                <my-pagination ref="pager" :total="total" :checked-count="sels.length"  @get-page="getlist"/>
            </template>

        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="40%">
       
          <span>
            <el-upload ref="upload" class="upload-demo"
              :auto-upload="false" :multiple="true" :limit="4" action accept=".xlsx"
              :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
              <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}   </el-button>
            </el-upload>           
          </span>
          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">关闭</el-button>
          </span>
        </el-dialog>


        <el-drawer title="添加信息" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editparmVisible1"
                direction="btt" size="'auto'" class="el-drawer__wrapper"  style="position:absolute;">
            <form-create :rule="autoform1.rule" v-model="autoform1.fApi" :option="autoform1.options"/>
            <div class="drawer-footer">
                <el-button @click.native="editparmVisible1 = false">取消</el-button>
                <my-confirm-button type="submit"  :loading="editparmLoading1" @click="onAddInfo" />
            </div>
        </el-drawer>

        <el-drawer title="编辑信息" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editparmVisible2"
                direction="btt" size="'auto'" class="el-drawer__wrapper"  style="position:absolute;">
            <form-create :rule="autoform2.rule" v-model="autoform2.fApi" :option="autoform2.options"/>
            <div class="drawer-footer">
                <el-button @click.native="editparmVisible2 = false">取消</el-button>
                <my-confirm-button type="submit"  :loading="editparmLoading2" @click="onSetEditParm" />
            </div>
        </el-drawer>

        <el-dialog :visible.sync="showDetailVisible" width="80%" :show-close="false" v-dialogDrag>
            <div style="height:680px;"> 
                <replacedayreportdetail ref="replacedayreportdetail" @nSearch="nSearch" style="height:100%;"></replacedayreportdetail>
            </div>
        </el-dialog>

    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import { getGroupKeyValue } from '@/api/operatemanage/base/product';
import {importReplaceProductManagement,getReplaceProductManagement,editReplaceProductManagement } from "@/api/profit/replaceproductmanagement"
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import replacedayreportdetail from "./replacedayreportdetail.vue"
import {
  getDirectorList
} from "@/api/operatemanage/base/shop";
const tableCols =[
        {istrue:true,prop:'coding',label:'编码', tipmesg:'', width:'120',sortable:'custom',},
          {istrue:true,prop:'productName',label:'商品名称', tipmesg:'', width:'120',},
        {istrue:true,prop:'operating',label:'运营组', tipmesg:'',width:'60',sortable:'custom',}, 
        {istrue:true,prop:'procurement',label:'运营负责人', tipmesg:'', width:'100',sortable:'custom',},         
        {istrue:true,prop:'supplier',label:'供应商信息', tipmesg:'', width:'230',sortable:'custom'}, 
        {istrue:true,prop:'returnGoodsAddress',label:'退货地址', tipmesg:'', width:'300',sortable:'custom'},  
        {istrue:true,prop:'remark',label:'备注', tipmesg:'', width:'300',sortable:'custom'}, 
         {istrue:true,type:'button',label:'其他', btnList:[{label:"一键复制退货地址",handle:(that,row)=>that.doCopy(row.returnGoodsAddress)}]},     
        {istrue:true,type:'button',label:'操作', btnList:[{label:"编辑",handle:(that,row)=>that.onHand(row)}]},
]

const tableHandles=[
        {label:"导入", handle:(that)=>that.startImport()},     
        {label:"模板-代发产品管理导入模板", handle:(that)=>that.downloadOrherTemplate()},
        {label:"添加代发产品管理信息", handle:(that)=>that.addreplaceproduct()},
      ];

const startTime = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunhanAdminReplacedayreport',
    components: {container, cesTable, MyConfirmButton, replacedayreportdetail},

    data() {
        return {
            directorList: [],
            groupList:[],
            that: this,
            filter:{           
                Coding:null,
                operating:null,
                Procurement:null,
            },
            list: [],
            shopList: [],
            summaryarry:{},
            pager:{OrderBy:"Coding",IsAsc:false},
            pickerOptions:{
                disabledDate(time){
                return time.getTime()>Date.now();
                }
            },        
            onHandNumber:null,   
            tableCols:tableCols,
            tableHandles:tableHandles,
            total: 0,
            sels: [], 
            editparmLoading: false,
            editparmLoading1: false,
            editparmLoading2: false,
            // editparmVisible: false,
             editparmVisible1: false,
            editparmVisible2: false,
            dialogVisible: false,
            listLoading: false,
            dialogVisible: false,
            uploadLoading: false,
            showDetailVisible: false,
       
          autoform1:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[{type:'input',field:'coding',title:'编码',value: '',col:{span:6},validate:[{ required: true, message: '请输入编码', trigger: 'blur' },]},
                     {type:'input',field:'operating',title:'运营',value: '',col:{span:6}}, 
                     {type:'input',field:'procurement',title:'采购',col:{span:6}},      
                    {type:'input',field:'supplier',title:'供应商信息',col:{span:6}},  
                     {type:'input',field:'returnGoodsAddress',title:'退货地址',col:{span:6}},  
                     {type:'input',field:'remark',title:'备注',col:{span:6}},          
                    ]
          },
          autoform2:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[{type:'input',field:'coding',title:'编码',value: '',col:{span:6},props:{readonly:true}},
                     {type:'input',field:'operating',title:'运营',value: '',col:{span:6}}, 
                     {type:'input',field:'procurement',title:'采购',col:{span:6}},      
                    {type:'input',field:'supplier',title:'供应商信息',col:{span:6}},  
                     {type:'input',field:'returnGoodsAddress',title:'退货地址',col:{span:6}},   
                       {type:'input',field:'remark',title:'备注',col:{span:6}},         
                    ]
          },
        };
    },

    async mounted() {
        await this.setGroupSelect();
        await this.onSearch()
        await this.onchangeplatform()
        await this.getDirectorlist();
    },

    methods: {
    //一键复制退货地址
    doCopy: function (val) {       
      let that=this;                         
      this.$copyText(val).then(function (e) {
          that.$message({ message: "内容已复制到剪切板！", type: "success" });
      }, function (e) {
          that.$message({ message: "抱歉，复制失败！", type: "warning" });
      })
    },
    //运营负责人
    async getDirectorlist() {
      const res1 = await getDirectorList();
      this.directorList = res1.data;
    },
   //运营组
    async setGroupSelect(){
      const res = await getGroupKeyValue();
      this.groupList=res.data;
    },
       async addreplaceproduct(){
            
            this.editparmVisible1=true;
        },

        //获取店铺
        async onchangeplatform(){
            this.categorylist =[]
            const res1 = await getshopList({platform:null,CurrentPage:1,PageSize:100});
            this.shopList=res1.data.list
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page  = this.pager;
            const params = { ...pager,...page,... this.filter}
            if(params===false){
                    return;
            }
            this.listLoading = true
            const res = await getReplaceProductManagement(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
           
            this.list = data
       },
       async showAmont(row){
            if (row.orderCount === 1) return
            this.showDetailVisible = true
            let param = {orderNo: row.orderNo}
            let that = this
            that.$nextTick(async () =>{
                await that.$refs.replacedayreportdetail.onSearch1(param)
            })
            
        },
        renderAmont(row){
            if(row.orderCount > 1) return "color:blue;cursor:pointer;";
            else return "";
        },
       async onHand(row){   
      
            this.editparmVisible2 = true              
           
            var arr = Object.keys(this.autoform2.fApi)
            if (arr.length > 0)
                await this.autoform2.fApi.resetFields()

            this.$nextTick(async() =>{
                await this.autoform2.fApi.setValue(row)
            })  
         
           

       },
       async onSetEditParm(){
      
            this.editparmLoading2 = true
            this.autoform2.fApi.validate(async (valid) => {
                if(valid){
                    const formData = this.autoform2.fApi.formData();
                    // formData.isTrue = 3;
                    await editReplaceProductManagement(formData)
                    this.editparmLoading2 = false
                    this.editparmVisible2 = false                   
                    this.getlist()

                }
            })
        
            
       },

     async onAddInfo(){
        
           this.editparmLoading1 = true
            this.autoform1.fApi.validate(async (valid) => {
                if(valid){
                    const formData = this.autoform1.fApi.formData();
                    // formData.isTrue = 3;
                    await editReplaceProductManagement(formData)
                   await this.autoform1.fApi.resetFields()
                    this.editparmLoading1 = false
                    this.editparmVisible1 = false                
                    this.getlist()

                }
            })

      },
       
       async nSearch(){
            await this.getlist()
       },
       //开始导入
        startImport(){
            this.dialogVisible=true;
        },
        //取消导入
        cancelImport(){
            this.dialogVisible=false;
        },
        uploadSuccess(response, file, fileList) {
            if (response.code == 200) {
            } else {
                fileList.splice(fileList.indexOf(file), 1);
            }
        },
        async submitUpload() {
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请先选取文件", type: "warning" });
                return false;
            }
            this.fileHasSubmit=true;
            this.uploadLoading=true;
            this.$refs.upload.submit();
        },
        async uploadFile(item) {
            if(!this.fileHasSubmit){
                return false;
            }
            this.fileHasSubmit=false;
            const form = new FormData();
            form.append("token", this.token);
            form.append("upfile", item.file);
            const res =await importReplaceProductManagement(form);
            if (res.code==1)   this.$message({ message: "上传成功,正在导入中...", type: "success" });
            else  this.$message({ message: res.msg, type: "warning" });
            this.uploadLoading=false; 
        },
        async uploadChange(file, fileList) {
        if (fileList && fileList.length > 0) {
            var list = [];
            for(var i=0;i<fileList.length;i++){
            if(fileList[i].status=="success")
                list.push(fileList[i]);
            else
                list.push(fileList[i].raw);
            }
            this.fileList = list;
        }
        },
        async uploadRemove(file, fileList){
            this.uploadChange(file, fileList);
        },
        async sortchange(column){
        if(!column.order)
            this.pager={};
        else{
            this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
        }
        await this.onSearch();
        },  
        selectchange:function(rows,row) {
            this.selids=[];console.log(rows)
            rows.forEach(f=>{
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event){
        
        },
        async downloadOrherTemplate(){
            window.open("../static/excel/financial2/profit/代发产品管理模板.xlsx","_self");
        }, 
    },
};
</script>

<style lang="scss" scoped>

</style>