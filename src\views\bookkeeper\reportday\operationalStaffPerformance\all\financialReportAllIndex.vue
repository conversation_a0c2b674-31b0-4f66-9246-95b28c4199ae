<template>
  <my-container>
    <template #header>
      <div class="top">
        <el-date-picker class="timeCss" v-model="filter.timerange" @change="changeTime($event, 1)" type="daterange"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="fastTimePickerOptions">
        </el-date-picker>
        <el-date-picker class="timeCss" v-model="filter.timerangeOnTime" @change="changeTime($event, 2)"
          type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="true" range-separator="至"
          start-placeholder="上架开始日期" end-placeholder="上架结束日期" :picker-options="fastTimePickerOptions">
        </el-date-picker>
        <el-date-picker v-show="(activeName == 'first2' || activeName == 'first1')" class="timeCss"
          v-model="filter.timerangeEntry" @change="changeTime($event, 3)" type="daterange" format="yyyy-MM-dd"
          value-format="yyyy-MM-dd" :clearable="true" range-separator="至" start-placeholder="入职开始日期"
          end-placeholder="入职结束日期" :picker-options="fastTimePickerOptions">
        </el-date-picker>
        <el-select v-show="activeName == 'first6'" filterable v-model="filter.shopManager" collapse-tags clearable
          placeholder="店铺负责人" class="publicCss">
          <el-option v-for="item in shopList" :key="item.label" :label="item.label" :value="item.label" />
        </el-select>
        <!-- <el-select v-show="activeName == 'first6'" v-model="filter.property" placeholder="属性" collapse-tags clearable
          filterable class="publicCss">
          <el-option v-for="item in propertyList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select> -->
        <el-select v-model="filter.platform" placeholder="平台" filterable class="publicCss"
          @change="platformChange($event, 'all')">
          <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="txPlatform" placeholder="请选择淘系平台" filterable class="publicCss" v-if="filter.platform === 0"
          @change="platformChange($event, 'tx')">
          <el-option v-for="item in txPlatformList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="filter.shopCodeList" placeholder="店铺" clearable filterable multiple collapse-tags
          class="publicCss" v-if="activeName == 'first6'" style="width: 155px;">
          <el-option v-for="item in shopOptions" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
        </el-select>
        <el-select v-show="activeName != 'first5'" filterable v-model="filter.groupIds" collapse-tags multiple clearable
          placeholder="运营组" class="publicCss" style="width: 155px;">
          <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-show="activeName == 'first4' || activeName == 'first5'" filterable v-model="filter.superviseIds"
          collapse-tags clearable placeholder="运营主管" multiple class="publicCss" style="width: 155px;">
          <el-option v-for="item in superviselist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-show="activeName != 'first5'" filterable v-model="filter.company" collapse-tags clearable
          placeholder="分公司" class="publicCss">
          <el-option v-for="item in childcompany" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-show="activeName != 'first5'" filterable v-model="filter.onlineStatus" collapse-tags clearable
          placeholder="员工状态" class="publicCss">
          <el-option key="正式" label="正式" :value="3"></el-option>
          <el-option key="试用" label="试用" :value="2"></el-option>
          <el-option key="离职" label="离职" :value="1"></el-option>
        </el-select>
        <el-select filterable v-model="filter.Profit3Lose" collapse-tags clearable placeholder="毛三利润" class="publicCss">
          <el-option key="正利润" label="正利润" :value="0"></el-option>
          <el-option key="负利润" label="负利润" :value="1"></el-option>
        </el-select>
        <el-select filterable v-model="filter.Profit33Lose" collapse-tags clearable placeholder="毛四利润"
          class="publicCss">
          <el-option key="正利润" label="正利润" :value="0"></el-option>
          <el-option key="负利润" label="负利润" :value="1"></el-option>
        </el-select>
        <el-date-picker v-show="activeName == 'first6'" class="timeCss" v-model="filter.timerange2" type="daterange"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="店铺创建时间开始"
          end-placeholder="店铺创建时间结束" :picker-options="fastTimePickerOptions" @change="changeTime($event, 4)" />
        <el-select v-model="filter.xiFenPlatformList" filterable clearable placeholder="细分平台" multiple collapse-tags style="width: 170px" v-if="checkPermission('SegmentationPlatform')" >
          <el-option v-for="item in segmentationList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-button type="primary" @click="handleClick" style="width: 70px;">搜索</el-button>
      </div>
      <div style="margin-top: 10px;">
        <el-button-group style="margin-right: 5px;">
          <!-- <el-button type="primary" @click="editZlparam">设置助理目标参数</el-button>
          <el-button type="primary" @click="editZyparam">设置专员目标参数</el-button> -->
          <el-button v-for="(button, index) in buttons" :key="button.type"
            :type="button.type === targetType ? 'primary' : 'info'" @click="onShowTarget(button.type)"
            v-if="checkPermission(button.permission)">
            {{ button.label }}
          </el-button>
        </el-button-group>
        <el-button type="primary" @click="onExport">导出</el-button>
      </div>
    </template>
    <el-tabs v-model="activeName" style="height: 94%" @tab-click="handleClick" v-loading="loading">
      <el-tab-pane label="运营助理" name="first1" style="height: 100%">
        <operationsAssistantAll ref="reffirst1" :ListInfo="filter" :targetType="targetType"
          :argumentPlatform="argumentPlatform" @onExportProps="onExportProps" />
      </el-tab-pane>
      <el-tab-pane label="运营专员" name="first2" style="height: 100%" lazy>
        <operationsSpecialistAll ref="reffirst2" :ListInfo="filter" :targetType="targetType"
          :argumentPlatform="argumentPlatform" @onExportProps="onExportProps" />
      </el-tab-pane>
      <el-tab-pane label="运营带教" name="first3" style="height: 100%" lazy>
        <operationsTeachingAll ref="reffirst3" :ListInfo="filter" :targetType="targetType"
          :argumentPlatform="argumentPlatform" @onExportProps="onExportProps" />
      </el-tab-pane>
      <el-tab-pane label="运营组" name="first4" style="height: 100%" lazy>
        <operationsGroupsAll ref="reffirst4" :ListInfo="filter" :targetType="targetType"
          :argumentPlatform="argumentPlatform" @onExportProps="onExportProps" />
      </el-tab-pane>
      <el-tab-pane label="运营主管" name="first5" style="height: 100%" lazy>
        <operationsSupervisorAll ref="reffirst5" :ListInfo="filter" :targetType="targetType"
          :argumentPlatform="argumentPlatform" @onExportProps="onExportProps" />
      </el-tab-pane>
      <el-tab-pane label="店铺" name="first6" style="height: 100%" lazy>
        <operationsStore ref="reffirst6" :ListInfo="filter" :targetType="targetType"
          :argumentPlatform="argumentPlatform" @onExportProps="onExportProps" />
      </el-tab-pane>
    </el-tabs>

    <el-dialog title="设置助理目标参数" :visible.sync="dialogSetParamZlVisible" width="40%" v-dialogDrag>
      <span>
        <el-row>毛三目标(元)
          <el-input-number v-model="targetData" :min="1" :max="99999999" :controls="false" />
        </el-row>
        <el-row>净利目标(元)
          <el-input-number v-model="targetData1" :min="1" :max="99999999" :controls="false" />
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="saveZl()">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="设置专员目标参数" :visible.sync="dialogSetParamZyVisible" width="40%" v-dialogDrag>
      <span>
        <el-row>毛三目标(元)
          <el-input-number v-model="targetData" :min="1" :max="99999999" :controls="false" />
        </el-row>
        <el-row>净利目标(元)
          <el-input-number v-model="targetData1" :min="1" :max="99999999" :controls="false" />
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="dialogSetParamZyVisible = false">确定</el-button> -->
        <el-button @click="saveZy()">确定</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import { childcompany } from '@/utils/tools';
import { fastTimePickerOptions } from '@/utils/getCols'
import operationsAssistantAll from "./operationsAssistantAll.vue";
import operationsSpecialistAll from "./operationsSpecialistAll.vue";
import operationsTeachingAll from "./operationsTeachingAll.vue";
import operationsGroupsAll from "./operationsGroupsAll.vue";
import operationsSupervisorAll from "./operationsSupervisorAll.vue";
import operationsStore from "./operationsStoreAll.vue";
import dayjs from 'dayjs'
import { getDirectorGroupList, getDirectorGroupList2, getAllList as getAllShopList, getList as getshopList, getAllShopXiFenPlatform } from '@/api/operatemanage/base/shop'
import { SetPerformanceTarget, getPerformanceTarget, exportPerformanceStaticticsByGroupForDouYin, exportPerformanceStaticticsByShopForDouYin, exportPerformanceStaticticsByUser, } from '@/api/bookkeeper/reportday'
const cstTargetName_ZlProfit3 = "全平台运营助理毛三目标";
const cstTargetName_ZlProfit4 = "全平台运营助理净利目标";
const cstTargetName_ZyProfit3 = "全平台运营专员毛三目标";
const cstTargetName_ZyProfit4 = "全平台运营专员净利目标";
const platformList = [
  { value: 5, label: '全部' },
  { value: 2, label: '拼多多' },
  { value: 6, label: '抖音' },
  { value: 0, label: '淘系' },
  { value: 14, label: '快手' },
  { value: 20, label: '视频号' }
]
const txPlatformList = [
  { value: 0, label: '全部' },
  { value: 1, label: '天猫' },
  { value: 9, label: '淘宝' },
  { value: 8, label: '淘工厂' },
  { value: 10, label: '苏宁' }
]
export default {
  name: "financialReportAllIndex",
  components: {
    MyContainer, operationsAssistantAll, operationsSpecialistAll, operationsTeachingAll, operationsGroupsAll, operationsSupervisorAll, operationsStore
  },
  data() {
    return {
      txPlatformList,//淘系平台
      platformList,//全平台
      argumentPlatform: '',
      txPlatform: 0,
      loading: false,
      buttonStyle: ["primary", "info"],
      fastTimePickerOptions,
      targetData1: 0,
      targetData: 0,
      dialogSetParamZlVisible: false,
      dialogSetParamZyVisible: false,
      profitAuthority: true,
      targetType: 1,
      buttons: [
        { label: "毛三利润", type: 1, permission: 'AllPerformance_AllPerformanceProfit3' },
        { label: "净利润", type: 2, permission: 'AllPerformance_AllPerformanceNetProfit' }
        // 可以继续添加更多按钮项, 例如 { label: "毛四利润", type: 3, permission: 'AllPerformance_AllPerformanceProfit4' },
        // permission为权限码，label为按钮显示的文字，type为按钮对应的targetType
      ],
      shopList: [],
      shopOptions: [],
      txShopOptionsAll: [],
      allShopOptionsList: [],
      // propertyList: [
      //   { value: 1, label: '达播' },
      //   { value: 2, label: '商品卡' },
      // ],
      grouplist: [],
      superviselist: [],
      childcompany,
      activeName: "first1",
      segmentationList: [],
      filter: {
        timerange: [dayjs().subtract(1, 'day').format('YYYY-MM-DD'), dayjs().subtract(1, 'day').format('YYYY-MM-DD')],
        timerangeOnTime: [],
        timerangeEntry: [],
        shopManager: "",
        // property: "",
        groupIds: [],
        superviseIds: [],
        company: "",
        onlineStatus: "",
        Profit3Lose: "",
        Profit33Lose: "",
        timerange2: [],
        startTime2: "",
        endTime2: "",
        shopCodeList: [],
        startTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
        endTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
        targetData: 0,
        targetData1: 0,
        platform: 5,
        refundType: 1,//发生维度
        reportType: 1,
        xiFenPlatformList: [],
      },
    };
  },
  async mounted() {
    if (this.checkPermission('AllPerformance_AllPerformanceProfit3') && this.checkPermission('AllPerformance_AllPerformanceNetProfit')) {
      this.profitAuthority = false
    } else if (this.checkPermission('AllPerformance_AllPerformanceProfit3')) {
      this.targetType = 1
    } else if (this.checkPermission('AllPerformance_AllPerformanceNetProfit')) {
      this.targetType = 2
    }
    await this.handleClick()
    await this.init()
    const res1 = await getshopList({ platform: '', CurrentPage: 1, PageSize: 100000 });
    this.allShopOptionsList = res1.data.list
    const txPlatformValues = txPlatformList.filter(item => item.value !== 0).map(item => item.value);
    this.txShopOptionsAll = this.allShopOptionsList.filter(item =>
      txPlatformValues.includes(item.platform)
    );
    const seen = new Set();
    this.txShopOptionsAll = this.txShopOptionsAll.filter(item => {
      if (seen.has(item.shopCode)) return false;
      seen.add(item.shopCode);
      return true;
    });
  },
  methods: {
    async platformChange(a, type, val = 0) {
      let e = a
      if (val == 0) {
        this.filter.shopCodeList = []
      }
      if (type === 'all' && e !== 0) {
        this.txPlatform = 0
      }
      if (type === 'all' && e === 5) {
        this.txPlatform = e !== 0 ? 0 : null;
        this.shopOptions = this.allShopOptionsList
        return;
      }
      if ((type === 'tx' && e === 0) || (type === 'all' && e === 0 && !this.txPlatform)) {
        this.shopOptions = this.txShopOptionsAll
        return;
      }
      if (type === 'all' && this.filter.platform === 0 && this.txPlatform !== 0) {
        e = this.txPlatform
      }
      const res1 = await getshopList({ platform: e, CurrentPage: 1, PageSize: 100000 });
      this.shopOptions = res1.data.list
    },
    onExport() {
      this.$refs[`ref${this.activeName}`].onSublevelExport()
    },
    async onExportProps(tableCols, userType) {
      this.loading = true;
      //refundType为1是发生，为2是付款
      const exportCnColumns = tableCols
        .filter(item => item.label && item.prop)
        .map(item => {
          return this.filter.refundType == 2
            ? item.label.replace('(发生)', '(付款)').replace('(付款)', '(发生)')
            : item.label;
        });
      const exportColumns = tableCols
        .filter(item => item.label && item.prop)
        .map(item => item.exportField || item.prop);
      let groupIdsArr = this.filter.groupIds;
      const params = {
        ...this.filter,
        userType,
        targetType: this.targetType,
        YH_EXT_ExportCnColumns: exportCnColumns,
        YH_EXT_ExportColumns: exportColumns,
        groupIds: groupIdsArr.map(id => Number(id)),
        refundType: 1, //全平台导出
        reportType: 1,
      };
      const userTypeMap = {
        3: { func: exportPerformanceStaticticsByUser, name: "运营组" },
        4: { func: exportPerformanceStaticticsByUser, name: "店铺" },
        8: { func: exportPerformanceStaticticsByUser, name: "运营主管" },
        1: { func: exportPerformanceStaticticsByUser, name: "运营助理" },
        21: { func: exportPerformanceStaticticsByUser, name: "运营带教" },
        default: { func: exportPerformanceStaticticsByUser, name: "运营专员" }
      };
      const { func, name } = userTypeMap[userType] || userTypeMap.default;
      const res = await func(params);
      this.loading = false;
      if (!res?.data) return;
      const aLink = document.createElement("a");
      const blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute('download', `全平台人员业绩统计_${name}_${new Date().toLocaleString()}.xlsx`);
      aLink.click();
    },
    async init() {
      // 获取运营组
      let res1 = await getDirectorGroupList();
      this.grouplist = res1.data?.map(item => { return { value: item.key, label: item.value }; });

      // 获取运营主管
      this.superviselist = [];
      const res2 = await getDirectorGroupList2();
      const uniqueSupervisors = Array.from(
        new Set(res2.data?.filter(f => f.superviseId > 0).map(m => m.superviseId))
      );
      this.superviselist = uniqueSupervisors
        .map(id => res2.data.find(x => x.id === id))
        .filter(Boolean)
        .map(cur => ({ value: cur.id, label: cur.userName }));

      // 获取店铺负责人
      const { data } = await getAllShopList({ Platforms: [5, 2, 6, 1, 9, 8, 10, 0, 14, 21, 4, 7, 20], CurrentPage: 1, PageSize: 100000 });
      const seenLabels = new Set();//去重
      this.shopList = data
        .filter(item => item.shopManager !== null && item.shopManager !== '')
        .map(item => ({ label: item.shopManager, value: item.shopCode }))
        .filter(item => {
          if (seenLabels.has(item.label)) {
            return false; // 如果 label 已存在，则过滤掉
          } else {
            seenLabels.add(item.label); // 否则添加到 Set 中
            return true; // 保留这个 item
          }
        });
        const { data: data2 } = await getAllShopXiFenPlatform();
        this.segmentationList = data2;
    },
    async saveZl() {
      SetPerformanceTarget({ targetName: cstTargetName_ZlProfit3, targetData: this.targetData });
      SetPerformanceTarget({ targetName: cstTargetName_ZlProfit4, targetData: this.targetData1 });
      this.$message.success('设置助理目标参数成功');
      this.dialogSetParamZlVisible = false;
    },
    async saveZy() {
      SetPerformanceTarget({ targetName: cstTargetName_ZyProfit3, targetData: this.targetData });
      SetPerformanceTarget({ targetName: cstTargetName_ZyProfit4, targetData: this.targetData1 });
      this.$message.success('设置专员目标参数成功');
      this.dialogSetParamZyVisible = false;
    },
    async editZlparam() {
      this.targetData = (await getPerformanceTarget({ targetName: cstTargetName_ZlProfit3 })).data;
      this.targetData1 = (await getPerformanceTarget({ targetName: cstTargetName_ZlProfit4 })).data;
      this.dialogSetParamZlVisible = true;
    },
    async editZyparam() {
      this.targetData = (await getPerformanceTarget({ targetName: cstTargetName_ZyProfit3 })).data;
      this.targetData1 = (await getPerformanceTarget({ targetName: cstTargetName_ZyProfit4 })).data;
      this.dialogSetParamZyVisible = true;
    },
    async handleClick() {
      let e = this.activeName
      if (this.filter.platform == 0) {
        this.argumentPlatform = this.txPlatform;
      } else {
        this.argumentPlatform = this.filter.platform;
      }
      if (e !== 'first2' && e !== 'first1') {
        this.filter.startTimeEntry = null;
        this.filter.endTimeEntry = null;
        this.filter.timerangeEntry = [];
      }
      if (e !== 'first6') {
        this.filter.shopManager = null;
        this.filter.timerange2 = [];
        this.filter.startTime2 = null;
        this.filter.endTime2 = null;
        this.filter.shopCodeList = []
      }
      if (e === 'first6') {
        let label = this.filter.platform === 0 ? 'tx' : 'all'
        let value = this.filter.platform === 0 ? this.txPlatform : this.filter.platform
        if (label === 'tx' && value === 0) {
          this.shopOptions = this.txShopOptionsAll
        } else if (label === 'all' && value === 5) {
          this.shopOptions = this.allShopOptionsList
        } else {
          this.platformChange(value, label, 1)
        }
      }
      if (e !== 'first4' && e !== 'first5') {
        this.filter.superviseIds = [];
      }
      if (e === 'first5') {
        this.filter.groupIds = [];
        this.filter.company = '';
        this.filter.onlineStatus = '';
      }
      this.$nextTick(() => {
        this.$refs[`ref${this.activeName}`].getList();
      });
    },
    changeTime(e, val) {
      const [startKey, endKey] = val === 1 ? ['startTime', 'endTime'] : val === 2 ? ['startTime3', 'endTime3'] : val === 3 ? ['startTimeEntry', 'endTimeEntry'] : ['startTime2', 'endTime2'];
      this.filter[startKey] = e ? e[0] : null;
      this.filter[endKey] = e ? e[1] : null;
    },
    onShowTarget(type) {
      this.targetType = type;
      this.handleClick();
    },
  }
};
</script>

<style lang="scss" scoped>
.top {
  display: flex;

  .publicCss {
    width: 95px;
    margin-right: 2px;
  }
}

.timeCss {
  width: 230px;
  margin-right: 2px;
}

::v-deep .el-select__tags-text {
  max-width: 30px;
}
</style>
