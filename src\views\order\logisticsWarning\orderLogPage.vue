<template>
    <my-container v-loading="pageLoading">
        <div style="cursor:pointer;margin-left:95%; margin-top: 5px;margin-bottom: 2px;">
            <span @click="orderByList" style="color:#0094ff;display:inline-block;user-select:none;">
                ⬆⬇{{ isOrder ? "正序" : "倒序" }}
            </span>
        </div>
        <tbody v-for="orderLog in orderLogList">
            <tr style="line-height:30px;">
                <td style="width:150px;height:20px;">{{orderLog.created}}</td>
                <td style="width:120px;height:20px;">{{orderLog.creator_name}}</td>
                <td style="width:300px;height:20px;">{{orderLog.name}}</td>
                <td style="height:20px;">{{orderLog.remark}}</td>
            </tr>
        </tbody>
    </my-container>
</template>

<script>
    import MyContainer from '@/components/my-container'
    import { getOrderLogListAsync } from "@/api/order/logisticsEarlyWarPage";
    export default {
        name: 'OrderLogPage',
        components: { MyContainer },
        props: {
            orderNoInner: { type: String, default: "" },
        },
        data () {
            return {
                orderLogList: [],
                pageLoading: false,
                isOrder: true
            }
        },
        async mounted () {
            await this.initData();
        },
        methods: {
            async initData () {
                this.pageLoading = true
                const res = await getOrderLogListAsync(this.orderNoInner);
                this.pageLoading = false
                if (!res?.success) {
                    return
                }
                this.orderLogList = res.data;
            },
            orderByList () {
                this.isOrder = !this.isOrder;
                //进行数组排序
                if (this.isOrder) {
                    this.sortKeyAsc(this.orderLogList, "oa_id");
                } else {
                    this.sortKeyDesc(this.orderLogList, "oa_id");
                }
            },
            //正序
            sortKeyAsc (array, key) {
                return array.sort(function (a, b) {
                    var x = a[key];
                    var y = b[key];
                    return ((x < y) ? -1 : (x > y) ? 1 : 0)
                })
            },
            //倒序
            sortKeyDesc (array, key) {
                return array.sort(function (a, b) {
                    var x = a[key];
                    var y = b[key];
                    return ((x > y) ? -1 : (x < y) ? 1 : 0)
                })
            }

        },
    }
</script>

<style scoped>
</style>