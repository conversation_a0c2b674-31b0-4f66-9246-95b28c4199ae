<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>

        <el-form-item label="">
          <el-date-picker style="width: 320px" v-model="Filter.timerange" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="导入开始日期" end-placeholder="导入结束日期"
            :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>
        <el-form-item label="">
          <el-input v-model.trim="Filter.batchNumberStr" placeholder="批次" maxlength="50" clearable class="publicCss" />
        </el-form-item>
        <el-form-item label="">
          <el-input v-model.trim="Filter.shopName" placeholder="店铺名称" maxlength="50" clearable class="publicCss" />
        </el-form-item>
        <el-button style="padding: 0;margin-right: 10px;border: none;">
          <inputYunhan :key="'1'" :keys="'one'" :width="'220px'" ref="" :inputt.sync="Filter.goodsCode"
            v-model.trim="Filter.goodsCode" placeholder="商品编码/若输入多条请按回车" :clearable="true" @callback="callbackGoodsCode"
            title="商品编码" @entersearch="entersearch" :maxRows="100">
          </inputYunhan>
        </el-button>
        <el-form-item label="">
          <el-select v-model="Filter.status" multiple collapse-tags placeholder="消耗状态" style="width:180px"
            class="el-select-content" clearable filterable>
            <el-option v-for="item in DepotNameList" :key="item.label" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-select style="width: 180px;" v-model="Filter.platform" placeholder="平台" class="publicCss" clearable
          filterable collapse-tags>
          <el-option v-for="item in platformlistKj" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-form-item>
          <el-button style="margin-left: 10px;" type="primary" @click="onSearch">查询</el-button>
          <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button @click="startImport"
            type="primary" icon="el-icon-share" @command="handleCommand"> 导入
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a">下载模版</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button type="primary" @click="onExport" style="margin-left: 5px;"
            v-if="checkPermission('DailyDataMaintenance_kj_export')">导出</el-button>
          <el-button style="margin-left: 10px;" type="primary" @click="detailListGet('search')">查看消耗明细</el-button>
          <el-button style="margin-left: 10px;" type="primary" @click="checkBalance('search')">查看结余</el-button>
        </el-form-item>
      </el-form>
    </template>
    <vxetablebase ref="table" :id="'factoryProxySend20241103'" :that='that' :isIndex='true' :hasexpand='true'
      :tablefixed='true' @sortchange='sortchange' :tableData='dahuixionglist' :tableCols='tableCols'
      :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' :loading="listLoading"
      style="width: 100%;  margin: 0" :height="'100%'">
    </vxetablebase>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getjSpeedDriveList" />
    </template>
    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 75px;">
        <!-- <el-date-picker style="width: 200px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay" type="date"
            placeholder="选择日期" :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
          </el-date-picker> -->
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 查看明细 -->
    <el-dialog title="消耗明细" :visible.sync="detailVisible" :center="false" width="80%" v-dialogDrag>

      <el-form class="ad-form-query" :inline="true" :model="FilterVisible" @submit.native.prevent>
        <el-form-item label="">
          <el-date-picker style="width: 320px" v-model="FilterVisible.timerangeVisible" type="datetimerange"
            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="日报开始日期"
            end-placeholder="日报结束日期" :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>
        <el-form-item label="">
          <el-select style="width: 180px;" v-model="FilterVisible.platform" placeholder="平台" class="publicCss" clearable
            filterable collapse-tags>
            <el-option v-for="item in platformlistKj" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-input v-model.trim="FilterVisible.shopName" placeholder="店铺名称" maxlength="50" clearable
            class="publicCss" />
        </el-form-item>
        <el-form-item label="">
          <el-input v-model.trim="FilterVisible.batchNumberStr" placeholder="批次" maxlength="50" clearable
            class="publicCss" />
        </el-form-item>
        <el-button style="padding: 0;margin-right: 10px;border: none;">
          <inputYunhan :key="'1'" :keys="'one'" :width="'220px'" ref="" :inputt.sync="FilterVisible.goodsCode"
            v-model.trim="FilterVisible.goodsCode" placeholder="商品编码/若输入多条请按回车" :clearable="true"
            @callback="callbackGoodsCodeVisible" title="商品编码" @entersearch="entersearchVisible" :maxRows="100">
          </inputYunhan>
        </el-button>


        <el-button style="margin-left: 10px;" type="primary" @click="detailListGet('search')">查询</el-button>
        <el-button style="margin-left: 10px;" type="primary" @click="onExportVisible">导出</el-button>
      </el-form>

      <!-- <vxetablebase :id="'factoryProxySend20241106'" :tablekey="'factoryProxySend20241106'" :tableData='detailList'
        :summaryarry='detailSummaryarry' :showsummary='true' @sortchange='detailSortChange' :tableCols='detailTableCols'
        :loading='detailLoading' :border='true' :that="that" height="440px" 
        ref="detailVxetable" :toolbarshow="false">
      </vxetablebase> -->

      <vxetablebase ref="detailVxetable" :id="'factoryProxySend20241106'" :that='that' :isIndex='true' :hasexpand='true'
        :tablefixed='true' @sortchange='detailSortChange' :tableData='detailList' :tableCols='detailTableCols'
        :isSelection="false" :isSelectColumn="false" :summaryarry='detailSummaryarry' :showsummary='true'
        :loading="detailLoading" style="width: 100%;  margin: 0" height="440px">
      </vxetablebase>
      <my-pagination ref="detailPage" :total="detailTotal" @get-page="detailListGet()" />
    </el-dialog>

    <!-- 查看结余 -->
    <el-dialog title="查看结余" :visible.sync="balanceVisible" :center="false" width="80%" v-dialogDrag>

      <el-form class="ad-form-query" :inline="true" :model="CheckVisible" @submit.native.prevent>
        <el-form-item label="">
          <el-date-picker style="width: 320px" v-model="CheckVisible.timerangeCheck" type="datetimerange"
            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="日报开始日期"
            end-placeholder="日报结束日期" :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>
        <el-button style="padding: 0;margin-right: 10px;border: none;">
          <inputYunhan :key="'1'" :keys="'one'" :width="'220px'" ref="" :inputt.sync="CheckVisible.goodsCode"
            v-model.trim="CheckVisible.goodsCode" placeholder="商品编码/若输入多条请按回车" :clearable="true"
            @callback="callbackGoodsCodeCheck" title="商品编码" @entersearch="entersearchCheck" :maxRows="100">
          </inputYunhan>
        </el-button>
        <el-button style="margin-left: 10px;" type="primary" @click="checkBalance('search')">查询</el-button>
        <el-button style="margin-left: 10px;" type="primary" @click="onExportBalance">导出</el-button>
        <el-checkbox style="margin-left: 10px;" v-model="isHideZeroInventory"
          @change="hideInventoryData">隐藏期末数量为0数据</el-checkbox>
      </el-form>
      <!-- <vxetablebase :id="'balanceProxySend20241218'" :tablekey="'balanceProxySend20241218'" :tableData='balanceList'
        :tableCols='balanceTableCols' :summaryarry='balanceSummaryarry' :showsummary='true' :loading='balanceLoading'
        :border='true' :that="that" height="440px" @sortchange='sortchangeBalance' ref="balanceVxetable"
        :toolbarshow="false">
      </vxetablebase> -->


      <vxetablebase ref="balanceVxetable" :id="'balanceProxySend20241218'" :that='that' :isIndex='true'
        :hasexpand='true' :tablefixed='true' @sortchange='sortchangeBalance' :tableData='balanceList'
        :tableCols='balanceTableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='balanceSummaryarry'
        :showsummary='true' :loading="balanceLoading" style="width: 100%;  margin: 0" height="440px">
      </vxetablebase>
      <my-pagination ref="balancePage" :total="balanceTotal" @get-page="checkBalance()" />
    </el-dialog>


  </my-container>
</template>
<script>

import { PageDropshippingCostsAsync, ImportDropshippingCostsAsync, PageDropCostsDetailAsync, exportDropshippingCosts, exportDropCostsDetail, pageDropshippingCostsCashSurplus, exportDropCostsCashSurplus } from '@/api/bookkeeper/crossBorderV2'
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatPlatformkj, platformlistKj } from "@/utils/tools";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import inputYunhan from "@/components/Comm/inputYunhan";
import dayjs from 'dayjs';

const tableCols = [
  { istrue: true, prop: 'dateTime', label: '导入时间', sortable: 'custom', formatter: row => { return dayjs(row.dateTime).format('YYYY-MM-DD') } },
  { istrue: true, prop: 'batchNumberStr', label: '批次', sortable: 'custom', },
  { istrue: true, prop: 'enmPlatform', label: '平台', sortable: 'custom', formatter: (row) => { return formatPlatformkj(row.enmPlatform) } },
  { istrue: true, prop: 'shopName', label: '店铺名称', sortable: 'custom', },
  { istrue: true, prop: 'orderNo', label: '订单号', sortable: 'custom', },
  { istrue: true, prop: 'shopCode', label: '店铺id', sortable: 'custom', formatter: (row) => { return (row.shopCode).toString()+" " } },
  { istrue: true, prop: 'skc', label: 'skc', sortable: 'custom', },
  { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', },
  { istrue: true, prop: 'number', label: '数量', sortable: 'custom', },
  { istrue: true, prop: 'cost', label: '成本价', sortable: 'custom', },
  { istrue: true, prop: 'saleCost', label: '销售成本', sortable: 'custom', },
  { istrue: true, prop: 'currentConsumeAmount', label: '已消耗金额', tipmesg: '数据随着日报消耗同步变化' },
  { istrue: true, prop: 'status', label: '消耗状态', sortable: 'custom', formatter: (row, that) => that.DepotNameList.find(item => item.value == row.status).label },
  { istrue: true, prop: 'createdTime', label: '创建时间', sortable: 'custom', formatter: row => { return dayjs(row.createdTime).format('YYYY-MM-DD') } },
  // {
  //   istrue: true, type: "button", label: '操作', btnList: [
  //     { label: "查看消耗明细", permission: 'factoryProxySend_checkConsumeDetail', handle: (that, row) => that.detailListGet('search', row) }
  //   ]
  // }

];

//消耗明细
const detailTableCols = [
  { istrue: true, prop: 'dateTime', label: '日报时间', sortable: 'custom', formatter: row => { return dayjs(row.dateTime).format('YYYY-MM-DD') } },
  { istrue: true, prop: 'enmPlatform', label: '平台', sortable: 'custom', formatter: (row) => { return formatPlatformkj(row.enmPlatform) } },
  { istrue: true, prop: 'shopName', label: '店铺名称', sortable: 'custom', },
  { istrue: true, prop: 'shopCode', label: '店铺id', sortable: 'custom', },
  { istrue: true, prop: 'skc', label: 'skc', sortable: 'custom', },
  { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', },
  { istrue: true, prop: 'beginNumber', label: '期初数量', sortable: 'custom', },
  { istrue: true, prop: 'beginAmount', label: '期初金额', sortable: 'custom', },
  { istrue: true, prop: 'currentBatchNumberStr', label: '本期消耗批次', sortable: 'custom', formatter: (row) => { return row.currentBatchNumberStr.toString() } },
  { istrue: true, prop: 'currentConsumeNumber', label: '本期消耗数量', sortable: 'custom', },
  { istrue: true, prop: 'currentConsumeAmount', label: '本期消耗金额', sortable: 'custom', },
  { istrue: true, prop: 'overNumber', label: '期末数量', sortable: 'custom', },
  { istrue: true, prop: 'overAmount', label: '期末金额', sortable: 'custom', },
  { istrue: true, prop: 'createdTime', label: '消耗时间', sortable: 'custom', formatter: row => { return dayjs(row.createdTime).format('YYYY-MM-DD') } },

]

//查看结余
const balanceTableCols = [
  { istrue: true, prop: 'dateTimeYearMonthDay', label: '数据日期', sortable: 'custom', formatter: row => { return dayjs(row.dateTimeYearMonthDay).format('YYYY-MM-DD') } },
  { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', },
  { istrue: true, prop: 'beginAmount', label: '期初金额', sortable: 'custom', },
  { istrue: true, prop: 'beginNumber', label: '期初数量', sortable: 'custom', },
  { istrue: true, prop: 'todayRechargeAmount', label: '当期金额', sortable: 'custom', },
  { istrue: true, prop: 'todayRechargeNumber', label: '当期数量', sortable: 'custom', },
  { istrue: true, prop: 'currentConsumeAmount', label: '日报用额', sortable: 'custom', },
  { istrue: true, prop: 'currentConsumeNumber', label: '日报用量', sortable: 'custom', },
  { istrue: true, prop: 'overAmount', label: '期末金额', sortable: 'custom', },
  { istrue: true, prop: 'overNumber', label: '期末数量', sortable: 'custom', },

]

export default {
  name: "crossBorderCourierFeeAverage",
  components: { MyContainer, vxetablebase, inputYunhan },
  data() {
    return {
      that: this,
      Filter: {
        timerange: [],
        batchNumberStr: null,
        shopName: null,
        goodsCode: null,
        status: null,
        platform: null,
      },
      dahuixionglist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: {},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      defaultDate: new Date(),
      pickerOptions: {
        disabledDate(date) {
          // 设置禁用日期
          const start = new Date("1970/1/1");
          const end = new Date("9999/12/31");
          return date < start || date > end;
        },
      },
      dialogVisible: false,//导入弹窗
      fileList: [],//上传文件列表
      uploadLoading: false,//上传按钮loading
      fileparm: {},//上传文件参数
      yearMonthDay: null,//导入日期
      DepotNameList: [
        {
          label: '未消耗',
          value: '1',
        },
        {
          label: '已消耗',
          value: '2',
        },
        {
          label: '消耗中',
          value: '3',
        }
      ],

      //查看明细
      detailTableCols: detailTableCols,
      detailVisible: false,
      detailList: [],
      detailLoading: false,
      detailTotal: 0,
      detailEnum: {
        orderBy: '',
        isAsc: true,
      },
      currentRow: {},
      platformlistKj,
      //店铺列表
      shopList: [],
      FilterVisible: {
        timerangeVisible: [],
        batchNumberStr: null,
        shopName: null,
        goodsCode: null,
        status: null,
        platform: null,
      },
      // 查看结余
      balanceTableCols: balanceTableCols,
      balanceVisible: false,
      balanceList: [],
      balanceLoading: false,
      balanceTotal: 0,
      CheckVisible: {
        orderBy: '',
        isAsc: true,
        goodsCode: null,
        timerangeCheck: []
      },
      isHideZeroInventory: false,
      balanceSummaryarry: {},
      detailSummaryarry: {}
    };
  },
  async mounted() {
    this.getShopList();
    // this.isHideZeroInventory = false
    this.onSearch();
  },
  methods: {
    async getShopList() {

      const res1 = await getAllShopList({ platforms: [val] });
      this.shopList = [];
      res1.data?.forEach(f => {
        if (f.shopName && f.shopCode)
          this.shopList.push(f);
      });
    },
    //查看明细   
    detailSortChange({ order, prop }) {
      if (prop) {
        this.detailEnum.orderBy = prop
        this.detailEnum.isAsc = order.indexOf("descending") == -1 ? true : false
        this.detailListGet()
      }
    },
    sortchangeBalance({ order, prop }) {
      if (prop) {
        this.CheckVisible.orderBy = prop
        this.CheckVisible.isAsc = order.indexOf("descending") == -1 ? true : false
        this.checkBalance()
      }
    },

    async checkBalance(type) {
      if (!this.balanceVisible) {

        this.CheckVisible.goodsCode = null;
        this.CheckVisible.timerangeCheck = null;
        this.isHideZeroInventory = false

        this.balanceVisible = true
      }
      this.$nextTick(async () => {
        if (type) {
          this.$refs.balancePage.setPage(1);
        }
        var pager = this.$refs.balancePage.getPager();
        const para = { ...this.CheckVisible };
        if (this.CheckVisible.timerangeCheck) {
          para.start = this.CheckVisible.timerangeCheck[0];

          para.end = this.CheckVisible.timerangeCheck[1];
        }
        const params = {
          ...pager,
          ...para,
        };
        this.balanceLoading = true;
        const res = await pageDropshippingCostsCashSurplus(params);
        this.balanceLoading = false;
        this.balanceTotal = res.data.total
        this.balanceList = res.data.list;
        this.balanceSummaryarry = res.data.summary;
      })

    },
    async detailListGet(type, row) {


      if (!this.detailVisible) {
        this.currentRow = null
        this.FilterVisible.timerangeVisible = []
        this.FilterVisible.batchNumberStr = null
        this.FilterVisible.shopName = null
        this.FilterVisible.goodsCode = null
        this.FilterVisible.status = null
        this.FilterVisible.platform = null
        this.detailVisible = true
      }
      if (row) {
        this.currentRow = row
      }
      this.$nextTick(async () => {
        if (type) {
          this.$refs.detailPage.setPage(1);
        }
        var pager = this.$refs.detailPage.getPager();
        const para = { ...this.FilterVisible };
        if (this.FilterVisible.timerangeVisible) {
          para.start = this.FilterVisible.timerangeVisible[0];

          para.end = this.FilterVisible.timerangeVisible[1];
        }
        const params = {
          ...pager,
          ...this.detailEnum,
          ...this.currentRow,
          ...para,
        };
        this.detailLoading = true;
        const res = await PageDropCostsDetailAsync(params);
        this.detailLoading = false;
        this.detailTotal = res.data.total
        this.detailList = res.data.list;
        this.detailSummaryarry = res.data.summary
      })
    },
    ///
    sortchange(column) {
      if (!column.order)
        this.order = {};
      else
        this.order = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getjSpeedDriveList();
    },
    async getjSpeedDriveList() {

      // this.Filter.timerangeVisible = []
      // this.Filter.batchNumberStr = null
      // this.Filter.shopName = null
      // this.Filter.goodsCode = null
      // this.Filter.status = null
      // this.Filter.platform = null

      const para = { ...this.Filter };
      if (this.Filter.timerange) {
        para.start = this.Filter.timerange[0];

        para.end = this.Filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.order,
        ...para,
      };
      params.status = params.status.join(",");
      this.listLoading = true;
      const res = await PageDropshippingCostsAsync(params);
      this.listLoading = false;
      this.total = res.data.total
      this.dahuixionglist = res.data.list;
      this.summaryarry = res.data.summary;
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },

    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      // form.append("yearMonthDay", this.yearMonthDay);
      var res = await ImportDropshippingCostsAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      // this.onSearch()
      this.uploadLoading = false
      this.dialogVisible = false;
    },
    onSubmitUpload() {
      // if (!this.yearMonthDay) {
      //   this.$message({ message: "请选择日期", type: "warning" });
      //   return false;
      // }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    async handleCommand(command) {
      switch (command) {
        //下载模版
        case 'a':
          await this.downLoadFile()
          break;
      }
    },
    async downLoadFile() {
      window.open("/static/excel/CrossBorderDailyDataMaintenance/厂家代发导入模版.xlsx", "_blank");
    },
    async onExport() {//导出列表数据；
      const para = { ...this.Filter };
      if (this.Filter.timerange) {
        para.start = this.Filter.timerange[0];

        para.end = this.Filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.order,
        ...para,
      };
      params.status = params.status.join(",");
      var res = await exportDropshippingCosts(params);
      if (res?.data.success) {
        this.$message({ message: res.data.msg, type: "success" });
      }
    },
    //多条查询部分
    async entersearch(val) {
      this.getjSpeedDriveList();
    },
    async callbackGoodsCode(val) {
      this.Filter.goodsCode = val;
    },
    //多条查询部分
    async entersearchVisible(val) {
      this.detailListGet();
    },
    async callbackGoodsCodeVisible(val) {
      this.FilterVisible.goodsCode = val;
    },

    //结余
    async entersearchCheck(val) {
      this.checkBalance();
    },
    async callbackGoodsCodeCheck(val) {
      this.CheckVisible.goodsCode = val;
    },
    async onExportVisible() {//导出列表数据；
      const para = { ...this.FilterVisible };
      if (this.FilterVisible.timerangeVisible) {
        para.start = this.FilterVisible.timerangeVisible[0];

        para.end = this.FilterVisible.timerangeVisible[1];
      }
      const params = {
        ...para,
      };
      var res = await exportDropCostsDetail(params);
      if (res?.data.success) {
        this.$message({ message: res.data.msg, type: "success" });
      }
    },
    async onExportBalance() {//导出列表数据；
      const para = { ...this.CheckVisible };
      if (this.CheckVisible.timerangeCheck) {
        para.start = this.CheckVisible.timerangeCheck[0];

        para.end = this.CheckVisible.timerangeCheck[1];
      }
      const params = {
        ...para,
      };
      var res = await exportDropCostsCashSurplus(params);
      console.log("res ", res)
      if (res?.data.success) {
        this.$message({ message: res.data.msg, type: "success" });
      }
    },
    async hideInventoryData() {
      if (this.isHideZeroInventory) {
        this.CheckVisible.isOverNumber = true;
      } else {
        this.CheckVisible.isOverNumber = undefined;
      }
      await this.checkBalance();
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>