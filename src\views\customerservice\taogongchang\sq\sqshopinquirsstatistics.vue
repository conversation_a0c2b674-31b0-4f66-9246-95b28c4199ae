<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='inquirslist' @select='selectchange' :isSelection='false'
            :tableCols='tableCols' :loading="listLoading">

            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>

            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select multiple v-model="filter.groupNameList" placeholder="组名称" clearable collapse-tags
                            filterable>
                            <el-option v-for="item in filterGroupList" :key="item" :label="item"
                                :value="item"></el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select v-model="filter.shopCodeList" filterable placeholder="店铺" clearable multiple
                            collapse-tags>
                            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                                :value="item.shopCode">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <datepicker v-model="filter.sdate"></datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport" style="margin-left: 10px;">导出</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getinquirsList" />
        </template>

        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
            :close-on-click-modal="false" v-dialogDrag>
            <div>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>
<script>

import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import dayjs from "dayjs";
import datepicker from '@/views/customerservice/datepicker'
import buschar from '@/components/Bus/buschar'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import {
    GetGongChangShops, GetGongChangGroup,
    GetGongChangShopInquirsPageList, ExportGongChangShopInquirsList, GetGongChangShopInquirsChart
} from '@/api/customerservice/gongchanginquirs'

import Decimal from 'decimal.js';
function precision(number, multiple) {
    return new Decimal(number).mul(new Decimal(multiple));
}

const tableCols = [
    { istrue: true, prop: 'shopName', label: '店铺', width: '240', sortable: 'custom' },
    { istrue: true, prop: 'inquirs', label: '接起量', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'satisfactions', label: '满意量', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'satisfactions2', label: '一般满意量', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'noSatisfactions', label: '不满意量', width: '120', sortable: 'custom' },

    { istrue: true, prop: 'satisfactionRate', label: '满意度', width: '120', sortable: 'custom', formatter: (row) => { return (row.satisfactionRate ? precision(row.satisfactionRate, 100).toFixed(2) : 0) + "%" } },
    { istrue: true, prop: 'noSatisfactionRate', label: '不满意度', width: '120', sortable: 'custom', formatter: (row) => { return (row.noSatisfactionRate ? precision(row.noSatisfactionRate, 100).toFixed(2) : 0) + "%" } },

    { istrue: true, prop: 'responseTime', label: '平均响应时长', width: '140', sortable: 'custom' },
    { istrue: true, prop: 'outTimes', label: '出勤人次', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '120', sortable: 'custom' },

    { istrue: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 90, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar },
    props: [""],
    data() {
        return {
            that: this,
            filter: {
                groupType: 0,
                inquirsType: 0,
                sdate: [formatTime(new Date(new Date().getTime() - 3600 * 1000 * 24 * 30), "YYYY-MM-DD 00:00:00"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")],
                groupNameList: [],
                shopCodeList: []
            },
            shopList: [],
            filterGroupList: [],
            inquirslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "shopName", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],

            dialogMapVisible: { visible: false, title: "", data: [] },
            fileList: [],
        };
    },
    async mounted() {
        this.onSearch();
        await this.getGongChangShops();
    },
    methods: {
        async getGongChangShops() {
            let shops = await GetGongChangShops();
            this.shopList = [];
            shops.data?.forEach(f => {
                if (f.shopCode && f.platform == 8)
                    this.shopList.push(f);
            });
        },
        async getGongChangGroup() {
            let groups = await GetGongChangGroup({ groupType: 0, isleavegroup: false });
            if (groups?.success && groups?.data && groups?.data.length > 0) {
                this.filterGroupList = groups.data;
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getinquirsList();
            this.getGongChangGroup();
        },
        getParam() {
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            return params;
        },
        async getinquirsList() {
            let params = this.getParam();
            this.listLoading = true;
            const res = await GetGongChangShopInquirsPageList(params);
            this.listLoading = false;
            this.total = res.data.total;
            this.inquirslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async showchart(row) {
            let params = this.getParam();
            params.shopName = row.shopName;
            let that = this;
            const res = await GetGongChangShopInquirsChart(params).then(res => {
                that.dialogMapVisible.visible = true;
                that.dialogMapVisible.data = res
                that.dialogMapVisible.title = res.title;
                res.title = "";
            })
            this.dialogMapVisible.visible = true
        },
        async onExport() {
            let params = this.getParam();
            this.listLoading = true
            const res = await ExportGongChangShopInquirsList(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '淘工厂店效率统计(售前组)_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>
