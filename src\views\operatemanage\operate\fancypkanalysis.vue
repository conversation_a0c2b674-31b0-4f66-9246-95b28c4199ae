<template>
  <div style="height: 100%;">
     <template>
       <el-alert
          title="温馨提示:当选择自己与自己对比时是日数据，自己与竞品对比时是周数据"
          type="warning"
          show-icon
          :closable="false">
      </el-alert>
      <el-form class="ad-form-query" :model="filter" @submit.native.prevent label-width="100px">
        <el-form-item label="选择关键词:">
            <el-select v-model="filter1.keyWordId" placeholder="请选择竞品" class="el-select-content">
              <el-option
                  v-for="item in keyWords"
                  :key="item.id"
                  :label="item.keyWord"
                  :value="item.id">
              </el-option>
            </el-select>
         </el-form-item>
        <el-form-item label="对比1:">
          <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-cascader v-model="filter1.pk1" :options="Options" @change="pk1Change" placeholder="请选择" class="el-select-content"></el-cascader>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="选择竞品:" v-if="filter1.pk1isjp==true">
                 <el-select v-model="filter1.PK1ProCodeSel" @change="pk1childChange"  placeholder="请选择竞品" class="el-select-content">
                    <el-option
                        v-for="item in pk1jpprocodes"
                        :key="item.proCode"
                        :label="item.proName"
                        :value="item.proCode">
                        <span style="float: left">{{ item.shopName+' --' }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.proName }}</span>
                    </el-option>
                 </el-select>
                </el-form-item>
              </el-col>
          </el-row>
        </el-form-item>
       <el-form-item label="对比2:">
          <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-cascader v-model="filter1.pk2" :options="Options" @change="pk2Change" placeholder="请选择" class="el-select-content"></el-cascader>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="选择竞品:" v-if="filter1.pk2isjp==true">
                 <el-select v-model="filter1.PK2ProCodeSel" @change="pk2childChange" placeholder="请选择竞品" class="el-select-content">
                    <el-option
                        v-for="item in pk2jpprocodes"
                        :key="item.proCode"
                        :label="item.proName"
                        :value="item.proCode">
                        <span style="float: left">{{ item.shopName+' --' }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.proName }}</span>
                    </el-option>
                 </el-select>
                </el-form-item>
              </el-col>
          </el-row>
        </el-form-item>
         <el-form-item label="左边轴显示:">
           <el-select v-model="filter1.leftY" placeholder="请选择" class="el-select-content">
              <el-option v-for="item in Ylist" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="右边轴显示:">
           <el-select v-model="filter1.rightY" placeholder="请选择" class="el-select-content">
             <el-option v-for="item in Ylist" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onfresh">刷新</el-button>
        </el-form-item>
      </el-form>
      </template> 
       <div id="echartmonitfancypk" style="width: 100%;height: 389px; box-sizing:border-box; line-height: 389px;">     
      </div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
import {getAllJpProducts,getAllKeyWord,productKeyWordFancyPKAnalysis} from '@/api/operatemanage/operate'
export default {
  name: 'Roles',
  components: { },
   props:{
       filter: { }
     },
  data() {
    return {
      filter1:{
        PK1ProCodeSel:"",
        PK2ProCodeSel:"",
        keyWordId:"",
        leftY:0,
        rightY:1,
        pk1:[],
        pk1isjp:false,
        pk2isjp:false,
        pk1type:1,
        pk12:[],
        pk2type:1,
      },
   Ylist:[
          {value:0,unit:"",label:"访客人数"},
          {value:1,unit:"",label:"下单买家数"},
          {value:2,unit:"%",label:"下单转化率"},
          {value:3,unit:"",label:"支付人数"},
          {value:4,unit:"%",label:"支付转化率"}
         ],
   Options:[{value: 'self', label: '自己',children: [{value: 'st',label:'手淘搜索'}, {value: 'ztc', label: '直通车'},{value: 'all', label: '手淘搜索+直通车'}]},
            {value: 'jp', label: '竞品',children: [{value: 'st',label:'手淘搜索'}, {value: 'ztc', label: '直通车'},{value: 'all', label: '手淘搜索+直通车'}]}],
      pk1jpprocodes:[],
      pk2jpprocodes:[],
      keyWords:[],
      pageLoading: false
    }
  },
  mounted() {
  },
  beforeUpdate() {
  },
  methods: {
   async onSearch() {
    },
  async onfresh() {
      this.getdata()
    },
  async initdata() {
      var hasparm=false;
      var arry= Object.keys(this.filter)
      if (arry.length==0)  return;
      for (let key of Object.keys(this.filter)) {
        if(this.filter[key])
            hasparm=true;
      }
      if(!hasparm) return; 
      var res1 = await getAllKeyWord({...this.filter,...{type:3}});
      if (res1?.code)
           this.keyWords= res1.data;
    },
  async getdata() {
      var parm={...this.filter, ...this.filter1};
      var hasparm=false;
      var arry= Object.keys(parm)
      if (arry.length==0)  return;

      for (let key of Object.keys(parm)) {
        if(parm[key])
            hasparm=true;
      }
     if(!hasparm) return;
     if (this.filter1.pk1isjp&&!this.filter1.PK1ProCodeSel) {
        this.$message({message: "请先选择竞品！",type: "warning"});
        return;
     }
     if (this.filter1.pk2isjp&&!this.filter1.PK2ProCodeSel) {
        this.$message({message: "请先选择竞品！",type: "warning"});
        return;
     }
      if (this.filter1.PK1ProCodeSel) parm.PK1ProCode=this.filter1.PK1ProCodeSel
      if (this.filter1.PK2ProCodeSel) parm.PK2ProCode=this.filter1.PK2ProCodeSel
      if (this.filter1.pk1isjp) parm.PK1ProCode=this.filter.selfProCode
      if (this.filter1.pk2isjp) parm.PK2ProCode=this.filter.selfProCode

     console.log(parm)
      const res = await productKeyWordFancyPKAnalysis(parm);      
      if (!res?.code)  return;       
      var chartDom = document.getElementById('echartmonitfancypk');
      var myChart = echarts.init(chartDom);
       myChart.clear();
       if (!res.data) {
         this.$message({message: "没有数据!",type: "warning",});
         return;
       }
      var option = this.Getoptions(res.data);
      //console.log(JSON.stringify(option))
      option && myChart.setOption(option); 
    },
   Getoptions(element){
     var series=[]
     element.series.forEach(s=>{
       series.push({  smooth: true, ...s})
     })
     var yAxis=[]
     this.Ylist.forEach(s=>{
       if (s.value==this.filter1.leftY)
          yAxis.push({ type: 'value',name: s.label,axisLabel: {formatter: '{value}'+s.unit}})
     })
     this.Ylist.forEach(s=>{
       if (s.value==this.filter1.rightY)
          yAxis.push({ type: 'value',name: s.label,axisLabel: {formatter: '{value}'+s.unit}})
     })      
     var option = {
        title: {text: element.title},
        tooltip: {trigger: 'axis'},
        legend: {
            data: element.legend
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {feature: {
            magicType: {show: true, type: ['line', 'bar']},
            //restore: {show: true},
        }},
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis: yAxis,
        series:  series
    };
    return option;
   },
 async pk1Change(value) {
    this.filter1.PK1ProCodeSel='';
    if (value[1]=="st") this.filter1.pk1type=1
    else if (value[1]=="ztc") this.filter1.pk1type=2
    else if (value[1]=="all") this.filter1.pk1type=3
    if (value[0]=="jp") {
      this.filter1.pk1isjp=true;
      const res = await getAllJpProducts({...this.filter,...{type:this.filter1.pk1type}});
      if (res?.code) this.pk1jpprocodes= res.data;
    }
    else this.filter1.pk1isjp=false;
   },
  async pk2Change(value) {
     this.filter1.PK2ProCodeSel='';
    if (value[1]=="st") this.filter1.pk2type=1
    else if (value[1]=="ztc") this.filter1.pk2type=2
    else if (value[1]=="all") this.filter1.pk2type=3
    if (value[0]=="jp") {
      this.filter1.pk2isjp=true;
      const res = await getAllJpProducts({...this.filter,...{type:this.filter1.pk2type}});
      if (res?.code) this.pk2jpprocodes= res.data;
    }
    else this.filter1.pk2isjp=false;
   },
 async pk1childChange(value){
   if (value)  this.filter1.PK1ProCodeSel=value;
   },
 async pk2childChange(value){
    if (value)  this.filter1.PK2ProCodeSel=value;
   }
  }
}
</script>
