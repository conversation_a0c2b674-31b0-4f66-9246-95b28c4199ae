<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" :clearable="false"
          @change="changeTime">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.shopStyleCode" placeholder="店铺款式编码" maxlength="50" clearable
          class="publicCss" />
        <el-input v-model.trim="ListInfo.shopGoodsCode" placeholder="店铺商品编码" maxlength="50" clearable
          class="publicCss" />
        <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.orderNo" placeholder="原始线上单号" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.shopName" placeholder="店铺" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')" style="width: 70px">搜索</el-button>
        <!-- <el-button type="primary" @click="startImport">导入</el-button> -->

        <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button @click="startImport"
          type="primary" icon="el-icon-share" @command="handleCommand"> 导入
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a">下载模版</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button style="margin-left: 10px;" type="primary" @click="onExport"
          v-if="checkPermission('sheInInventory_Management_export')">导出</el-button>

      </div>
    </template>
    <vxetablebase :id="'shelnSalesSubjectAnalysis202408041733'" ref="table" :that='that' :isIndex='true'
      :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
      :isSelection="false" :isSelectColumn="false" :border="true" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%; margin: 0" v-loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="45%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 100px;">
        <el-date-picker style="width: 200px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay" type="date"
          placeholder="选择日期" :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
        </el-date-picker>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action=""
          accept=".xlsx" :file-list="fileList" :http-request="onUploadFile" :on-success="onUploadSuccess"
          :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary" style="width: 90px;">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px;width: 90px;" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pageCodeSalesThemeAnalysis_SheInAsync, importCodeSalesThemeAnalysis_SheInV2Async, exportCodeSalesThemeAnalysis_SheIn } from '@/api/bookkeeper/reportdayV2'
import dayjs from 'dayjs'
import { formatPlatform, formatTime, formatLinkProCode, platformlist } from "@/utils/tools";

const formatOrderType = {
  0: '普通订单',
  1: '补发订单',
  2: '供销plus',
  3: '其他',
}

const tableCols = [
  { sortable: 'custom', width: '80px', align: 'center', prop: 'yearMonthDay', label: '日期', },
  { sortable: 'custom', width: '110px', align: 'center', prop: 'shopName', label: '店铺' },
  { sortable: 'custom', width: '100px', align: 'center', prop: 'afterOrderNumber', label: '售后单号', },
  { sortable: 'custom', width: '80px', align: 'center', prop: 'platform', label: '平台', formatter: (row) => formatPlatform(row.platform) },
  { sortable: 'custom', width: '100px', align: 'center', prop: 'orderNoInner', label: '内部订单号', },
  { sortable: 'custom', width: '100px', align: 'center', prop: 'orderType', label: '订单类型', formatter: (row) => formatOrderType[row.orderType] },
  { sortable: 'custom', width: '100px', align: 'center', prop: 'orderStatus', label: '订单状态', },
  { sortable: 'custom', width: '150px', align: 'center', prop: 'tagMultiple', label: '标记多标签', },
  { sortable: 'custom', width: '130px', align: 'center', prop: 'proCode', label: 'SKC', },
  { sortable: 'custom', width: '130px', align: 'center', prop: 'shopStyleCode', label: '店铺款式编码', },
  { sortable: 'custom', width: '110px', align: 'center', prop: 'shopGoodsCode', label: '店铺商品编码', },
  { sortable: 'custom', width: '100px', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: '150px', align: 'center', prop: 'styleCode', label: '款式编码', },
  { sortable: 'custom', width: '110px', align: 'center', prop: 'orderNo', label: '原始线上单号', },
  { sortable: 'custom', width: '150px', align: 'center', prop: 'goodsName', label: '商品名称', },
  { sortable: 'custom', width: '80px', align: 'center', prop: 'cost', label: '成本价', },
  { sortable: 'custom', width: '80px', align: 'center', prop: 'qty', label: '销售数量', },
  { sortable: 'custom', width: '80px', align: 'center', prop: 'giftQty', label: '赠品数量', },
  { sortable: 'custom', width: '80px', align: 'center', prop: 'salesAmount', label: '销售金额', },
  { sortable: 'custom', width: '80px', align: 'center', prop: 'saleCost', label: '销售成本', },
]
export default {
  name: "sheInSalesSubjectAnalysis",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      dialogVisible: false,//导入弹窗
      uploadLoading: false,//上传loading
      fileList: [],//上传文件列表
      yearMonthDay: null,//导入日期
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        shopStyleCode: null,//店铺款式编码
        shopGoodsCode: null,//店铺商品编码
        goodsCode: null,//商品编码
        orderNo: null,//原始线上单号
        shopName: null,//店铺
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      summaryarry: {},//汇总
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("yearMonthDay", this.yearMonthDay);
      var res = await importCodeSalesThemeAnalysis_SheInV2Async(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (!this.yearMonthDay) {
        this.$message({ message: "请选择日期", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
      }
      if (this.timeRanges.length == 0) {
        //默认给近1天时间
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      const replaceArr = ['orderNo', 'shopName', 'goodsCode', 'shopGoodsCode', 'shopStyleCode']
      this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
      this.loading = true
      const { data, success } = await pageCodeSalesThemeAnalysis_SheInAsync(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    async onExport() {
      if (this.onExporting) return;
      try {
        if (this.timeRanges.length == 0) {
          //默认给近1天时间
          this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
          this.ListInfo.endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
          this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
        }
        const replaceArr = ['orderNo', 'shopName', 'goodsCode', 'shopGoodsCode', 'shopStyleCode']
        this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
        this.uploadLoading = true;
        const params = { ...this.pager, ...this.ListInfo }
        var res = await exportCodeSalesThemeAnalysis_SheIn(params);
        if (!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '希音销售主题分析_' + new Date().toLocaleString() + '.xlsx')
        this.uploadLoading = false;

        aLink.click()
      } catch (err) {
        console.log(err)
        console.log(err.message);
      }
      this.onExporting = false;
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async handleCommand(command) {
      switch (command) {
        //下载模版
        case 'a':
          await this.downLoadFile()
          break;
      }
    },
    async downLoadFile() {
      window.open("/static/excel/CrossBorderDownloadTemplate/希音销售主题分析导入模版.xlsx", "_blank");
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 180px;
    margin-right: 5px;
  }
}
</style>
