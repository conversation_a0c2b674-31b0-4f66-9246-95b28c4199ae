<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-button-group>
        <el-button style="padding: 0;margin-left: 0;">
          <el-select v-model="filter.noticeStatus" clearable placeholder="状态" style="width: 150px">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-select v-model="filter.noticeType" clearable placeholder="类型" style="width: 150px">
                    <el-option label="入职1周年" :value="1" :key="1"></el-option>
                    <el-option label="入职2周年" :value="2" :key="2"></el-option>
                    <el-option label="入职3周年" :value="3" :key="3"></el-option>
                    <el-option label="入职4周年" :value="4" :key="4"></el-option>
                    <el-option label="入职5周年" :value="5" :key="5"></el-option>
                  </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
                    <el-button type="primary" @click="onSearch">查询</el-button>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
                    <el-button type="primary" @click="onaddsumpackuser">新建</el-button>
        </el-button>
      </el-button-group>
    </template>
    <div>
      <ces-table :id="'anniversaryNotice202408041856'" ref="table" :tableData='list' :tableCols='tableCols' :border='true' :that="that" :showsummary='true'
        :isIndex='true' :tablefixed='true'   :hasexpand='false' :isSelectColumn="true" :isSelection="true"
        :summaryarry='summaryarry' style="width:100%;height:750px;margin: 0" :xgt="9999" @sortchange='sortchange'>
      </ces-table>
      <el-dialog :title="salaryuserTitle" :visible.sync="addFormVisible" width="30%" :loading="dialogAdding" v-dialogDrag :close-on-click-modal="false" :close-on-press-escape="false">
        <span>
          <el-form ref="addCateFormRef" class="ad-form-query" label-width="80px" :model="editForm" :inline="true"
            max-height="700" width="80%" @submit.prevent="uploadImage" :rules="rules">
            <el-row>
              <el-col :span="24">
                <div style="display: flex; align-items: center;">
                  <el-form-item label="类型" prop="noticeType"></el-form-item>
                    <el-select v-model="editForm.noticeType" style="width:35%">
                      <el-option label="入职1周年" :value="1" :key="1"></el-option>
                      <el-option label="入职2周年" :value="2" :key="2"></el-option>
                      <el-option label="入职3周年" :value="3" :key="3"></el-option>
                      <el-option label="入职4周年" :value="4" :key="4"></el-option>
                      <el-option label="入职5周年" :value="5" :key="5"></el-option>
                    </el-select>
                </div>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <div style="display: flex; align-items: center;">
                  <el-form-item label="生效日期" prop="daterange"></el-form-item>
                    <el-date-picker style="width: 60%;" v-model="editForm.daterange" type="daterange" format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd" clearable range-separator="至" start-placeholder="开始日期"
                      end-placeholder="结束日期" :picker-options="pickerOptions">
                    </el-date-picker>
                </div>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <div style="display: flex; align-items: center;">
                  <el-form-item prop="noticePics" label="图片"></el-form-item>
                    <YhImgUpload :Lisrztz="false" :value.sync="editForm.noticePics" ref="" :limit="10" style="width: 380px;"
                      :ismultiple="true"></YhImgUpload>
                </div>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <div style="display: flex; align-items: center;">
                  <el-form-item label=" " prop=""></el-form-item>
                    <el-checkbox v-model="editForm.israndom">是否随机图片</el-checkbox>
                </div>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <div style="display: flex; align-items: center;">
                <el-form-item label="接收部门" prop="EffectiveDeptId"></el-form-item>
                  <el-cascader v-model="editForm.EffectiveDeptId" :props="{label: 'label', value: 'deptId', multiple: true }" clearable placeholder="请选择接收部门"
                    collapse-tags style="width: 80%;" :options="whlist"
                    @change="selectItem">
                  </el-cascader>
                </div>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <div style="display: flex; align-items: center;">
                  <el-form-item label="不接收人" prop="notEffectiveDept"></el-form-item>
                    <el-select v-model="editForm.notEffectiveDept" filterable style="width: 80%;" clearable multiple placeholder="请选择不接收人(可搜索)"
                      collapse-tags @change="selectGet">
                      <el-option v-for="(item, i) in accountPersonList" :key="i" :label="item.label" :value="item.value" style="width: 100%;"></el-option>
                    </el-select>
                </div>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <div style="display: flex; align-items: center;">
                  <el-form-item label="查看"></el-form-item>
                    <el-button @click="dialogVisible = true">查看不接收人</el-button>
                      <el-dialog :visible.sync="dialogVisible" title="查看不接收人" :modal="false" v-dialogDrag >
                        <div v-for="(item,i) in accountPersonArray" :key="i">{{ item  }}</div>
                      </el-dialog>
                </div>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <div style="display: flex; align-items: center;">
                  <el-form-item label="是否启用" prop="noticeStatus"></el-form-item>
                    <el-switch v-model="editForm.noticeStatus" :active-value="1" :inactive-value="0">
                    </el-switch>
                </div>
              </el-col>
            </el-row>
          </el-form>
        </span>

        <div slot="footer">
          <my-confirm-button type="submit" class="right-align" @click="saveedit" /> &nbsp;
          <el-button @click="addFormVisible = false">取 消</el-button>
        </div>

      </el-dialog>
    </div>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>
  </my-container>
</template>
<script>
import dayjs from "dayjs";
import YhImgUpload from "@/views/profit/uploadfile.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import MySearch from "@/components/my-search";
import buschar from '@/components/Bus/buschar';
import MySearchWindow from "@/components/my-search-window";
import { formatTime } from "@/utils/tools";
import { listToTree } from '@/utils'
import { getAnniversaryNotice, saveAnniversaryNoticeRelation, getAllCorpDeptPersonList, getAllCorpDeptList, enabledAnniversaryNotice } from '@/api/admin/user'

const tableCols = [
  { istrue: true, prop: 'noticeStatus', label: '状态', width: '50', type: 'custom',sortable: 'custom', formatter: (row) => row.noticeStatus ? "启用" : "禁用" },
  { istrue: true, prop: 'noticeType', label: '类型', width: '100',type: 'custom', sortable: 'custom',formatter: (row) => "入职" + row.noticeType.toFixed() + "周年" },
  { istrue: true, prop: 'createdName', label: '创建人', width: '90',sortable: 'custom' },
  { istrue: true, prop: 'createdTime', label: '创建时间', width: '150', sortable: 'custom' },
  {
    istrue: true, type: 'button',align:'center',label: '操作', width: '100', btnList: [
      { label: "禁用", ishide: (that, row) => row.noticeStatus == 0, handle: (that, row) => that.onenabledsalaryuser(row.id, 0) },
      { label: "启用", ishide: (that, row) => row.noticeStatus == 1, handle: (that, row) => that.onenabledsalaryuser(row.id, 1) },
      { label: "编辑", handle: (that, row) => that.onEdit(row) }]
  },
];
const startDate = formatTime("YYYY-MM-DD");
const endDate = formatTime("YYYY-MM-DD");
export default {
  name: "Users",
  components: { cesTable, MyContainer, MyConfirmButton, YhImgUpload, MySearch, MySearchWindow, buschar },
  data() {
    return {
      dialogVisible: false,
      that: this,
      EffectiveDeptId: '',
      value: '',
      dateRange: [],
      pickerOptions: {
        disabledDate: (date) => {
          const today = new Date()
          return date < today.setHours(0, 0, 0, 0) // 禁用今日之前的日期
        },
      },
      list: [],
      total: 0,
      salaryuserTitle: "",
      sels: [],
      selids: [],
      map: null,
      pager: { OrderBy:"createdTime", IsAsc: false },
      summaryarry: {},
      whlist: [],
      addFormVisible: false,
      dialogAdding: false,
      filter:{
        noticeStatus:null,
        noticeType:null,
      },
      editForm: {
        noticeType: 0,
        EffectiveDeptId: '',
        daterange: [startDate, endDate],
        effectiveDateStart: null,
        effectiveDateEnd: null,
        notEffectiveDept: null,
        israndom: null,
        noticeStatus: 1,
        noticePics: [],
      },
      rules: {
                noticeType: [
                    { required: true, message: '请选择入职类型', trigger: 'change' }
                ],
                daterange: [
                    { required: true, message: '请选择日期', trigger: 'change' }
                ],
                EffectiveDeptId: [
                    { required: true, message: '请选择接收部门', trigger: 'change' }
                ],
                noticePics: [
                    { required: true, message: '请选择图片', trigger: 'change' }
                ],
            },
      tableCols: tableCols,
      accountPersonList: [],
      accountPersonArray: [],
      lxrname: '',
      pageLoading: false,
    }
  },
  props: {

  },
  async mounted() {
    this.onSearch()
  },
  async created() {
    await this.getWiseManDeptPersonList();
    await this.getWiseManCorpDeptList();
  },
  computed: {

  },
  methods: {
    //清空编辑数据
    resetEditForm() {
      this.editForm = {
        noticeType: 0,
        EffectiveDeptId: '',
        effectiveDateStart: null,
        effectiveDateEnd: null,
        notEffectiveDept: null,
        israndom: null,
        noticeStatus: 0,
        noticePics: [],
      }
    },
    //不接收人接口数据
    async getWiseManDeptPersonList() {
      const res1 = await getAllCorpDeptPersonList();
      this.accountPersonList = res1?.map(item => { return { value: item.id.toString(), label: item.corpName + '-' + item.dept + '-' + item.name }; });
    },
    //接收组接口数据
    async getWiseManCorpDeptList() {
      const res2 = await getAllCorpDeptList();
      if (res2?.success) {
        this.getChild(res2.data)
      }
    },
    getChild(datalist) {
      for (const data of datalist) {
        if (data.children.length > 0) {
          this.getChild(data.children)
        } else {
          data.children = undefined
        }
      }
      this.whlist = datalist
    },
    //接收部门change事件
    selectItem(val) {
      var string = '';
      val.map((item) => {
        string = string + item.join('.') + ','
      })
      this.EffectiveDeptId = string.slice(0, string.length - 1);
    },
    //不接收人change事件
    selectGet(vId) {
      var obj = {};
      const labelArr =  this.accountPersonList.filter(item => vId.includes(item.value)).map(item => item.label)
      console.log(labelArr,'labelArr');
      this.lxrname = obj.label;
    },
    //编辑
    async onEdit(row) {
      this.editForm.noticePics = '';
      this.resetEditForm()
      this.salaryuserTitle = "编辑";
      this.addFormVisible = true;
      let data = row;
      var a = data.effectiveDept.split(',');
      var arr = [];
      a.forEach((item, index) => {
        var b = [...item.split('.')]
        arr[index] = b;
      })
      const effectiveDateStart = row.effectiveDateStart;
      const effectiveDateEnd = row.effectiveDateEnd;
      let acc = [effectiveDateStart.slice(0,10),effectiveDateEnd.slice(0,10)];
      const add = JSON.parse(data.noticePics)
      this.accountPersonArray = this.accountPersonList.filter(person =>
        data.notEffectiveDept.includes(person.value)
      ).map(person => person.label);
      this.editForm = {
        id: data.id,
        noticeType: data.noticeType,
        daterange: acc,
        effectiveDateStart: data.effectiveDateStart,
        effectiveDateEnd: data.effectiveDateEnd,
        EffectiveDeptId: arr,
        noticePics: data.noticePics,
        israndom: data.israndom,
        notEffectiveDept: (data.notEffectiveDept === null || data.notEffectiveDept === '') ? [] : data.notEffectiveDept.split(','),
        noticeStatus: data.noticeStatus,
      }
      this.$nextTick(()=>{
        this.$refs.imgUpload && this.$refs.imgUpload.clear();
      })
    },
    //新增
    async onaddsumpackuser() {
      this.editForm = {
        id: 0,
        noticeType: null,
        daterange: [],
        EffectiveDeptId: null,
        noticePics: null,
        israndom: false,
        notEffectiveDept: null,
        noticeStatus:0,
      };
      this.accountPersonArray = null,

      this.salaryuserTitle = "新建";
      this.addFormVisible = true;
    },
    //启用禁用
    async onenabledsalaryuser(rowid, myenabled) {
      this.dialogAdding = true;
      const res = await enabledAnniversaryNotice({ id: rowid, noticeStatus: myenabled });
      this.dialogAdding = false;
      if (res?.success) {
        this.$message({ type: 'success', message: '操作成功!' });
        this.onSearch();
      }
    },
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist();
    },
    //分页查询
    async getlist() {
      this.editForm.effectiveDateStart = null;
      this.editForm.effectiveDateEnd = null;
      if (this.editForm.daterange) {
        this.editForm.effectiveDateStart = this.editForm.daterange[0];
        this.editForm.effectiveDateEnd = this.editForm.daterange[1];
      }
      this.sels = [];
      this.selids = [];
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = {
        ...pager,
        ...page,
        ...this.filter
      }
      if (params === false) {
        return;
      }
      this.dialogAdding = true;
      const res = await getAnniversaryNotice(params)
      this.dialogAdding = false;
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      data.forEach(d => {
        d._loading = false;
      })
      this.list = data;
    },
    //提交
    async saveedit() {
        if (this.editForm.EffectiveDeptId == null||(Array.isArray(this.editForm.EffectiveDeptId) && this.editForm.EffectiveDeptId.length == 0)||this.editForm.noticeType == NaN||this.editForm.noticeType == null||this.editForm.daterange == null||this.editForm.daterange.length == 0|| new Date(this.editForm.daterange[1]) < new Date().getTime() - (24 * 60 * 60 * 1000)||!this.editForm.noticePics) {
          if (new Date(this.editForm.daterange[1]) < new Date().getTime() - (24 * 60 * 60 * 1000)) {
            this.$message.warning('生效日期必须大于当前日期');
              } else {
                  this.$message.warning('请选择必填信息');
              }
            return;
      }
      if (this.editForm.daterange) {
        this.editForm.effectiveDateStart = this.editForm.daterange[0];
        this.editForm.effectiveDateEnd = this.editForm.daterange[1];
      }
      this.selectItem(this.editForm.EffectiveDeptId)
      this.editForm.EffectiveDeptId = this.EffectiveDeptId;
      var str = (this.editForm.notEffectiveDept === null || this.editForm.notEffectiveDept === '') ? "" : this.editForm.notEffectiveDept.join(',')
      this.editForm.notEffectiveDept = str;
      let num = parseInt(this.editForm.noticeType)
      this.editForm.noticeType = num;
      this.editForm.noticeStatus = this.editForm.noticeStatus == true ? 1 : 0;
      this.dialogAdding = true;
      const res = await saveAnniversaryNoticeRelation(this.editForm);
      this.dialogAdding = false;
        this.$message.success('添加成功！')
        this.addFormVisible = false;
        if (res?.success) {
          this.$message({ type: 'success', message: '操作成功!' });
          this.addFormVisible = false;
          this.onSearch();
      }
    },
    //排序查询
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
        this.onSearch();
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
  },
};

</script>

<style lang="scss" scoped>
// .dialog-footer {
//   margin-left: 380px;
// }
.dialog-footer {
  display: flex;
  justify-content: space-between;
}
.right-align {
  margin-left: 50%;
}
.custom-upload {
  margin-left: 80px;
}
</style>
