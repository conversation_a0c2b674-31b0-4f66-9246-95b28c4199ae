<template>
  <container v-loading="pageLoading">
      <!-- :tableHandles='tableHandles'  -->
     <ces-table ref="table" :that='that' :isIndex='true' 
         :hasexpand='true' :tableData='list' :tableCols='tableCols' @sortchange='sortchange' :loading="listLoading"
         :showsummary='true' :summaryarry='summaryarry'>
      
         <template slot='extentbtn'>
      <el-button-group>
     
        
    
    <el-button type="primary" @click="onSearch">刷新</el-button> 
      
    <!-- <el-button type="primary" @click="onSearch">查询</el-button> -->
      </el-button-group>
       </template>
      </ces-table>
      
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
   
    
   
 

  </container>
</template>
<script>
import {queryPurchaseOrderDetail,importPurchaseReturnGoods,getpurchaseReturnGoodsList,deletePurchaseReturnGoods} from '@/api/inventory/purchase'
import{getOperatingProfitAnalysisSum}from '@/api/bookkeeper/financialreport'
import {getAllSupplier,SupplierAccountsDetail,editSupplierAccountsDetail,exportSupplierAccountsDetail} from '@/api/inventory/supplier'
import {importOperatingProfitAnalysis} from '@/api/bookkeeper/import'
import {formatTime,formatYesornoBool,formatWarehouseArea,formatIsOutStock,formatSecondToHour,formatPlatform} from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import logistics from '@/components/Comm/logistics'
import goodscoderecord from '@/views/inventory/goodscoderecord'
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";
const tableCols =[
   
     {istrue:true,prop:'yearMonth',label:'年月',sortable:'custom', width:'80',},
      // {istrue:true,prop:'platform',label:'平台', width:'50',formatter:row=>formatPlatform(row.platform)},
      {istrue:true,prop:'operating',label:'小组',sortable:'custom', width:'50',},
      //  {istrue:true,prop:'settlementAmount',label:'结算金额', width:'80',},
        
      // {istrue:true,prop:'amountOut',label:'退款金额', width:'80',},
      {istrue:true,prop:'netSales',label:'净销售额', width:'80',},
       {istrue:true,prop:'netSalesRate',label:'净销售额占比', width:'100',formatter:(row)=> !row.netSalesRate?" ": (row.netSalesRate*100).toFixed(2)+'%'},
      //  {istrue:true,prop:'replacementAmount',label:'补发成本', width:'80',},
      // {istrue:true,prop:'abnormalAmount',label:'异常成本', width:'80',},
      {istrue:true,prop:'amountGrossProfitSale',label:'销售毛利',sortable:'custom', width:'80',},
       {istrue:true,prop:'amountGrossProfitSaleRate',label:'销售毛利率', width:'90',formatter:(row)=> !row.amountGrossProfitSaleRate?" ": (row.amountGrossProfitSaleRate*100).toFixed(2)+'%'},
         {istrue:true,prop:'profit1SumRate',label:'销售毛利占比', width:'100',formatter:(row)=> !row.profit1SumRate?" ": (row.profit1SumRate*100).toFixed(2)+'%'},
      //  {istrue:true,prop:'courierFees',label:'快递费', width:'80',},
      //  {istrue:true,prop:'expressDeductions',label:'其中：快递罚款', width:'120',},
       //包装费==辅料
        {istrue:true,prop:'packageFee',label:'辅料',sortable:'custom', width:'80',},
       //产品运费==物流费
      //  {istrue:true,prop:'productFreightfee',label:'物流费', width:'80',},
      //  {istrue:true,prop:'billsFee',label:'账单费用', width:'80',},
       {istrue:true,prop:'platformViolationsDeductions',label:'其中：扣款', width:'100',},
       {istrue:true,prop:'operatingFeeTotal',label:'推广费',sortable:'custom', width:'80',},
       {istrue:true,prop:'grossProfit',label:'产品利润',sortable:'custom', width:'80',},
       {istrue:true,prop:'profit3Rate',label:'产品利率',sortable:'custom', width:'100',formatter:(row)=> !row.profit3Rate?" ": (row.profit3Rate*100).toFixed(2)+'%'},
        {istrue:true,prop:'profit3SumRate',label:'产品利润占比', width:'100',formatter:(row)=> !row.profit3SumRate?" ": (row.profit3SumRate*100).toFixed(2)+'%'},
       {istrue:true,prop:'netProfit',label:'净利润',sortable:'custom', width:'80',},
       {istrue:true,prop:'netProfitRate',label:'净利率', width:'80',formatter:(row)=> !row.netProfitRate?" ": (row.netProfitRate*100).toFixed(2)+'%'},
      {istrue:true,prop:'netProfitSumRate',label:'净利润占比', width:'100',formatter:(row)=> !row.netProfitSumRate?" ": (row.netProfitSumRate*100).toFixed(2)+'%'},
     ];


export default {
  name: "Users",
  components: {container,cesTable,MyConfirmButton,logistics},
   props:{
       filter: { }
     },
  data() {
    return {
    
    //   sels2:[],
    
      uploadLoading:false,
      dialogVisible: false,
      that:this,
    //   formatWarehouseArea:formatWarehouseArea,
    //   formatYesornoBool:formatYesornoBool,
      formatTime:formatTime,
    //   formatIsOutStock:formatIsOutStock,
    //   formatSecondToHour:formatSecondToHour,
      // filter: {
      //   timerange:'',
       
      // },
    //   goodscoderecordfilter:{goodsCode:"",buyNo:""},
    //   imgPreview:{img:"",show:false},
    
      list: [],
    //   detaillist:[],
    //   oderDetailView:{},
    //   drawervisible:false,
    //   dialoganalysisVisible:false,
    //   visiblepopover: false,
    //   prevTarget: null, // 编辑 Popover 的 Reference （参照），用于 popover.js 对齐两个元素
    //   popperFlag: false, // 用于编辑 Popover 的刷新
    //   visiblepopoverdetail: false,
    //   dialogOrderDetailVisible:false,
    //   popperFlagdetail: false,
      tableCols:tableCols,
      pager:{OrderBy:"",IsAsc:false},
      summaryarry:{},
      total:0,
    //   total1:0,
      sels: [],
      selids: [],
      fileList:[],
      listLoading: false,
      pageLoading: false,
    //   editVisible:false,
    //   editLoading:false,
    //   hackReset:false,
    //   goodscoderecord1id: +new Date(),
    };
  },
  watch: {
    value(n) {
      if(n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
      }
    }
  },
 async mounted() {

    await this.onSearch();
    await this.getlist();
  },
 methods: {
//    onSelsChangeReturnGoods(sels){
//     this.sels2 = sels

//    },
   
   async onSearch() {
       this.$refs.pager.setPage(1)
       this.getlist()
    },
   async getlist() {
       this.filter.startTime =null;
       this.filter.endTime =null;
       if (this.filter.yearMonth && this.filter.yearMonth.length>0) {
                this.filter.startTime = this.filter.yearMonth[0];
                this.filter.endTime = this.filter.yearMonth[1];
            }
      if (!this.pager.OrderBy) this.pager.OrderBy="";
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      // if (params.timerange) {
      //    params.startDate = params.timerange[0];
      //    params.endDate = params.timerange[1];
      // }
    //   params.SuName=this.filter.SuName.join();
      // console.log("供应商id",params.SuName);
      // console.log('params',params)
      this.listLoading = true
      const res = await getOperatingProfitAnalysisSum(params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {d._loading = false})
      this.list = data
      this.summaryarry=res.data.summary;
    },

   onDisPlay(row){
     return row.isHandle==true;
    },

  
    beforeRemove() {
      return false;
    },
    
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   selsChange: function(sels) {
      this.sels = sels
    },
   selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.proBianMa);
      })
    },
   doCopy: function (val) {       
      let that=this;                         
      this.$copyText(val).then(function (e) {
          that.$message({ message: "内容已复制到剪切板！", type: "success" });
      }, function (e) {
          that.$message({ message: "抱歉，复制失败！", type: "warning" });
      })
    },
  },
};
</script>


