<template>
  <container>
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='true'
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry' :loading="listLoading"/>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
  </container>
</template>
<script>
import {pageDaySampleFee} from '@/api/financial/daycost'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import {formatFeeShareOper,formatTime,formatYesornoBool,companylist} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'yearMonthDay',label:'日期', width:'100',sortable:'custom',formatter:(row)=>formatTime(row.yearMonthDay,'YYYY-MM-DD')},
      {istrue:true,prop:'groupId',label:'运营组', width:'80',sortable:'custom',formatter:(row)=> row.groupName},
      {istrue:true,prop:'amont',label:'金额', width:'100',sortable:'custom'},
      {istrue:true,prop:'shareOper',label:'分摊方式', width:'100',sortable:'custom',formatter:(row)=>formatFeeShareOper(row.shareOper)},
      {istrue:true,prop:'computeStatus',label:'状态', width:'80',sortable:'custom',formatter:(row)=>{return row.computeStatus==0?'未计算':'已计算'}},
      {istrue:true,prop:'computeTime',label:'计算时间', width:'145',sortable:'custom',formatter:(row)=>formatTime(row.computeTime,'YYYY-MM-DD HH:mm:ss')},
      {istrue:true,prop:'createdTime',label:'导入时间', width:'145',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD HH:mm:ss')}
     ];
const tableHandles=[
        {label:"导入", handle:(that)=>that.onimport()},
        {label:"下载导入模板", handle:(that)=>that.ondownloadmb('样品费用导入模板')},
        {label:"批量删除", handle:(that)=>that.onbatchDelete()},
        {label:"刷新", handle:(that)=>that.getlist()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, container },
   props:{
       filter: { }
     },
  data() {
    return {
      shareFeeType:2,
      companylist:companylist,
      that:this,
      list: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"id",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [], 
      listLoading: false,
      pageLoading: false
    }
  },
  mounted() {
     //this.onSearch()
  },
  beforeUpdate() { },
  methods: {
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager()
      this.filter.shareFeeType=this.shareFeeType;
      const params = {...pager, ...this.pager, ... this.filter}
      this.listLoading = true
      const res = await pageDaySampleFee(params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   async onbatchDelete() {
       await this.$emit('ondeleteByBatch',this.shareFeeType);
    },
   async oncomput(){
      this.$emit('onstartcomput',this.shareFeeType);
   },
   async onimport(){
     await this.$emit('onstartImport',this.shareFeeType);
   },
   async ondownloadmb(name){
     await this.$emit('ondownloadmb',name);
   },
    selsChange: function(sels) {
      this.sels = sels
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
  }
}
</script>
