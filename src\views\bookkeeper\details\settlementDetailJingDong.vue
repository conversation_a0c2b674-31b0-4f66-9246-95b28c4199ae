<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="">
                    <el-date-picker style="width: 110px" v-model="filter.yearMonth" type="month" format="yyyyMM"
                        value-format="yyyyMM" placeholder="年月">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="">
                    <el-select filterable v-model="filter.platform" placeholder="平台" disabled clearable
                        style="width:110px">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="" label-position="right">
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺" style="width: 180px">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="" label-position="right">
                    <el-select filterable clearable v-model="filter.FeeItemEnm" placeholder="费用项" style="width:150px;">
                        <el-option label="货款" :value="1"></el-option>
                        <el-option label="价保返佣" :value="2"></el-option>
                        <el-option label="价保扣款" :value="3"></el-option>
                        <el-option label="商品保险服务费" :value="4"></el-option>
                        <el-option label="售后卖家赔付费" :value="5"></el-option>
                        <el-option label="随单送的京豆" :value="6"></el-option>
                        <el-option label="佣金" :value="7"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="" label-position="right">
                    <el-select filterable clearable v-model="filter.RecoganizeType" placeholder="收支方向"
                        style="width:100px;">
                        <el-option label="收入" :value="0"></el-option>
                        <el-option label="支出" :value="1"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="" label-position="right">
                    <el-input v-model="filter.recoganizeSerialNumberOrder" placeholder="订单编号" style="width:160px;" />
                </el-form-item>
                <el-form-item>
                    <el-select filterable v-model="filter.documentTypeList" placeholder="单据类型"  multiple collapse-tags  clearable style="width: 173px">
                    <el-option label="取消退款单" value="取消退款单" />
                    <el-option label="订单" value="订单" />
                    <el-option label="售后服务单" value="售后服务单" />
                    <el-option label="非销售单" value="非销售单" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport">汇总导出</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='ZTCKeyWordList' :showsummary='true' :summaryarry='summaryarry' @select='selectchange'
            :isSelection='false' :tableCols='tableCols' :loading="listLoading" :isSelectColumn='false'>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList, getList as getshopList } from '@/api/operatemanage/base/shop';
import { formatPlatform, formatLink, platformlist } from "@/utils/tools";
import { getFinancialDetailJD as getPageList, ExportFinancialDetailJD } from '@/api/monthbookkeeper/financialDetail'
const tableCols = [
    { istrue: true, prop: 'yearMonth', label: '年月', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'recoganizeSerialNumberOrder', label: '订单编号', sortable: 'custom', width: '160', type: 'html' },
    { istrue: true, prop: 'businessType', label: '单据类型', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'timeOccur', label: '费用发生时间', sortable: 'custom', width: '150', type: 'html' },
    { istrue: true, prop: 'timeBilling', label: '费用计费时间', sortable: 'custom', width: '150', type: 'html' },
    { istrue: true, prop: 'timeSettlement', label: '费用结算时间', sortable: 'custom', width: '150', type: 'html' },
    { istrue: true, prop: 'feeItem', label: '费用项', sortable: 'custom', width: '100', type: 'html' },
    { istrue: true, prop: 'amount', label: '订单实付应结', sortable: 'custom', width: '100', type: 'html', formatter: (row) => { return row.amount?.toFixed(2) } },
    { istrue: true, prop: 'currency', label: '币种', sortable: 'custom', width: '100', type: 'html' },
    { istrue: true, prop: 'feeType', label: '商家应收/应付', sortable: 'custom', width: '100', type: 'html' },
    { istrue: true, prop: 'accountDirection', label: '收支方向', sortable: 'custom', width: '100', type: 'html' },
    { istrue: true, prop: 'qty', label: '商品数量', sortable: 'custom', width: '100', type: 'html' },
    { istrue: true, prop: 'settlementRemark', label: '钱包结算备注', sortable: 'custom', width: '150', type: 'html' },
    { istrue: true, prop: 'descRemark', label: '备注', sortable: 'custom', width: '150', type: 'html' },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
    data() {
        return {
            that: this,
            filter: {
                platform: 7,
                yearMonth: null,
                shopCode: null,
                proCode: null,
                isNullProCode: null,
                RecoganizeType: null,
                documentTypeList: []
            },
            shopList: [],
            userList: [],
            groupList: [],
            platformlist: platformlist,
            ZTCKeyWordList: [],
            tableCols: tableCols,
            summaryarry: {},
            total: 0,
            pager: { OrderBy: "id", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
        };
    },
    async mounted() {
        this.onchangeplatform();
    },
    methods: {
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async onchangeplatform() {
            const res1 = await getshopList({ platform: 7, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
        },
        // async getShopList(){
        //   const res1 = await getAllShopList();
        //   this.shopList=[];
        //     res1.data?.forEach(f => {
        //       if(f.isCalcSettlement&&f.shopCode)
        //           this.shopList.push(f);
        //     });
        // },
        onRefresh() {
            this.onSearch()
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getList();
        },
        async getList() {
            let params = this.getCondition();
            this.listLoading = true;
            const res = await getPageList(params);
            this.listLoading = false;
            this.total = res.data?.total
            this.ZTCKeyWordList = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        getCondition() {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter,
            };
            return params;
        },
        async onExport(opt) {
            if (!this.filter.yearMonth) {
                this.$message({ message: "请先选择月份", type: "warning" });
                return;
            }
            let pars = this.getCondition();
            if (pars === false) {
                return;
            }
            const params = { ...pars, ...opt };
            let res = await ExportFinancialDetailJD(params);
            if (!res?.data) {
                return
            }
            this.$message({ message: "正在后台导出中，请点击头像-在下载管理中查看", type: "success" });
            // const aLink = document.createElement("a");
            // let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            // aLink.href = URL.createObjectURL(blob)
            // aLink.setAttribute('download', '月报按店铺汇总导出_支付宝_' + new Date().toLocaleString() + '_.xlsx')
            // aLink.click()
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
