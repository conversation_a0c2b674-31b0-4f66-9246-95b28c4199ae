<template>
    <container>
        <template #header>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
            :tableData='list'  :tableCols='tableCols' :isSelection="false" @select="selectchange"
            :tableHandles='tableHandles' @cellclick="cellclick"
            :loading="listLoading">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"  @get-page="getlist"/>
        </template>
      
    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import { getExpressCheckOrderDetail } from "@/api/express/express"

const tableCols =[
        {istrue:true,prop:'receiveTime',label:'揽收时间', tipmesg:'', width:'100',sortable:'custom',},
        {istrue:true,prop:'expressNo',label:'虚拟快递单号', tipmesg:'拆分后虚拟快递单号',width:'180',sortable:'custom',}, 
        {istrue:true,prop:'weight',label:'发往省份重量', tipmesg:'', width:'110',sortable:'custom',},  
        {istrue:true,prop:'province',label:'发往省份', tipmesg:'', width:'200',},         
        //{istrue:true,prop:'totalFee',label:'账单金额', tipmesg:'人工导入', width:'150',sortable:'custom',}, 
        //{istrue:true,prop:'hasVolune',label:'重量类型', tipmesg:'', width:'200',},      
        {istrue:true,prop:'jsTotalFee',label:'计算金额', tipmesg:'', width:'200',},               
        //{istrue:true,prop:'computeNum',label:'拆分单数', tipmesg:'', width:'auto',},  
        //{istrue:true,prop:'goodsName',label:'拆分重量', width:'125',sortable:'custom'},     
        // {istrue:true,prop:'orderAmount',label:'订单金额', tipmesg:'聚水潭订单付款金额', width:'100',sortable:'custom',},
        // {istrue:true,prop:'qty',label:'数量',sortable:'custom', tipmesg:'订单对应编码的下单数量', width:'80',},       
]

const tableHandles=[
        
      ];


const startTime = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunhanAdminEcpresscheckorder',
    components :{container, MyConfirmButton, cesTable},

    data() {
        return {
            that: this,
            filter:{
                startTime: null,
                endTime: null,
                timerange:[startTime, endTime],
                procode:null,
                title:null,
                platform:null,
                shopCode:null,
                groupId:null,
                operateSpecialId:null,
                operateName:null,
                user3Id:null,
                shopId:null,
                newPattern:null,
                customer:null,
                parentId:null
            },
            list:[],
            summaryarry:{},
            tableCols:tableCols,
            tableHandles:tableHandles,
            total: 0,
            sels: [],
            pager:{OrderBy:"receiveTime",IsAsc:false},
            dialogVisible: false,
            listLoading: false,
            pickerOptions:{
                disabledDate(time){
                return time.getTime()>Date.now();
                }
            },        
        };
    },

    async mounted() {
        await this.onSearch()
    },

    methods: {
         //查询第一页
        async onSearch1(para) {
            this.filter.parentId = para.id
            console.log('数据来了',para)
            await this.onSearch()
        },
        async onSearch() {
            await this.getlist()
            this.$refs.pager.setPage(1)
        },
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page  = this.pager;
            
            const params = { ...pager,...page,... this.filter}
            if(params===false){
                return;
            }
            this.listLoading = true
            const res = await getExpressCheckOrderDetail(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry=res.data.summary;
            data.forEach(d => {
                d._loading = false
            })
            this.list = data
       },
        async sortchange(column){
        if(!column.order)
            this.pager={};
        else{
            this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
        }
        await this.onSearch();
        },  
        selectchange:function(rows,row) {
            this.selids=[];console.log(rows)
            rows.forEach(f=>{
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event){
        
        },
    },
};
</script>

<style lang="scss" scoped>

</style>