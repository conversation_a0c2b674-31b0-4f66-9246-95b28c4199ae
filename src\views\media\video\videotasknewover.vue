<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
    <template #header>
      <videotaskfilter @topSearch="topSearch" @onAddTask="onAddTask" @onExport="onExport" :platformList="platformList"
        :warehouselist="warehouselist" :taskUrgencyList="taskUrgencyList" :islook="false" :groupList="groupList"
        @ShowHideonSearch="ShowHideonSearch" :fpPhotoLqNameList="fpPhotoLqNameList" :erpUserInfoList="erpUserInfoList"
        @handleCommand="handleCommand" :listtype="listtype"></videotaskfilter>
    </template>
    <!--列表----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
    <videotaskoverTable ref="vediomaintask" :id="'vediomaintaskover'" :tableData='tasklist' :tableCols='tableCols'
      :that='that' :height="'100%'" :showsummary='listtype == 4' :summaryarry='summaryarry' @summaryClick='onsummaryClick'
      :loading='listLoading' @sortchange='sortchange' checkboxall @checkboxall="selectchangeevent"
      @selectchangeevent="selectchangeevent" @rowChange="rowChange" @shootUrgencyCilck="shootUrgencyCilck"
      @openTaskRmarkInfo="openTaskRmarkInfo" @videotaskuploadfileDetal="videotaskuploadfileDetal" @editTask="editTask"
      @openComputOutInfo="openComputOutInfo" @onUploadVideoAudio="onUploadVideoAudio" :showCacle="listtype == 4">
    </videotaskoverTable>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" :sizes="[50, 100, 200, 300, 800, 1000, 2000]"
        :page-size="1000" @get-page="getTaskList" />
    </template>
    <!-- 编辑任务 -->
    <div class="dialog1">
      <!--编辑任务-->
      <el-drawer :visible.sync="editTaskshow" :close-on-click-modal="false" direction="rtl" :size="717"
        element-loading-text="拼命加载中" v-loading="addLoading" :show-close="false">
        <videoeditform v-if="editTaskshow" ref="videotaskeditform" :platformList="platformList"
          :onCloseAddForm="onCloseAddForm" :taskUrgencyList="taskUrgencyList" :warehouselist="warehouselist"
          :fpPhotoLqNameList="fpPhotoLqNameList" :erpUserInfoList="erpUserInfoList" :groupList="groupList" :islook="true"
          :isoverlist="islook" />
      </el-drawer>
    </div>
    <div class="dialog1">
      <el-drawer :visible.sync="approveTaskshow" :close-on-click-modal="false" direction="btt" :size="850"
        element-loading-text="拼命加载中" :with-header="false">
        <vediotaskapprove v-if="approveTaskshow" :videoTaskId="videoTaskId" :ckindex="ckindex"
          :closefun="onUploadVideoAudioClose" :islook="islook" />
      </el-drawer>
    </div>

     <!--查看详情-->
     <el-dialog title="查看备注" :key="markopentime" :visible.sync="viewReferenceRemark" width="60%"
            :close-on-click-modal="false" element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <shootingTaskRemark ref="shootingTaskRemark" :rowinfo="selectRowKey" :islook="islook"></shootingTaskRemark>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="viewReferenceRemark = false">关 闭</el-button>
                    <my-confirm-button type="submit" :loading="shootingTaskRemarkrawer" @click="sumbitshootingTaskRemark"
                        v-show="!islook" />
                </span>
            </template>
        </el-dialog>
  </my-container>
</template>
<script>
const tableCols = [];
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import {
  pageViewTaskAsync, overTaskActionAsync
  , unCaclTaskActionAsync, unShopTaskActionAsync, shopTaskActionAsync
  , caclTaskActionAsync, deleteTaskActionAsync, unSignTaskActionAsync
  , signTaskActionAsync, unEndTaskActionAsync, endTaskActionAsync
} from "@/api/media/vediotask";
import { getUserRoleList } from '@/api/media/packdesgin';
import { pageShortVideoCaclTaskAsync, getHistoryShortVideoTaskInfo } from '@/api/media/shootingset';
import videoeditform from "@/views/media/video/videoeditform.vue";
import videoaddform from "@/views/media/video/videoaddform.vue";
import videotaskoverTable from "@/views/media/video/maintable/videotaskoverTable .vue";
import videotaskfilter from "@/views/media/video/maintable/videotaskfilter.vue";
import vediotaskapprove from "@/views/media/video/maintable/vediotaskapprove.vue";
import shootingTaskRemark from '@/views/media/video/VideoTaskRemark'

export default {
  name: "Users",
  inject: ["reload"],
  props: {
    fpPhotoLqNameList: { type: Array, default: () => { return []; } },
    erpUserInfoList: { type: Array, default: () => { return []; } },

    taskUrgencyList: { type: Array, default: () => { return []; } },
    groupList: { type: Array, default: () => { return []; } },
    platformList: { type: Array, default: () => { return []; } },
    warehouselist: { type: Array, default: () => { return []; } },
    islook: { type: Boolean, default: false },
    listtype: { type: Number, default: 99 },
    versionId: { type: String, default: "0" },
    filter: { type: Object, default: { isAudioComplate: 0, isShop: 0 } },//
  },
  components: {
    shootingTaskRemark,
    MyContainer,
    MyConfirmButton,
    MySearch,
    MySearchWindow,
    vediotaskapprove,
    videotaskoverTable,
    videoeditform,
    videoaddform,
    videotaskfilter
  },
  data() {
    return {
      viewReferenceRemark: false,
      shootingTaskRemarkrawer: false,
      that: this,
      pageLoading: false,
      //列表相关
      selids: [],
      tasklist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: {},
      pager: {},
      sels: [], // 列表选中列
      listLoading: false,
      seafilter: {},
      //新编辑页面
      editTaskshow: false,
      addTask: false,
      //
      taskUrgencyAproved: false,
      taskUrgencyStatus: "1",
      selmicroVedioTaskId: 0,
      addLoading: false,

      approveTaskshow: false,
      approveTaskshowrefelsh: true,
      videoTaskId: 0,
      ckindex: 0,
      markopentime: new Date().getTime() + 3,
            //选中的行
            selectRowKey: null,
            // islook:true
    };
  },
  watch: {},
  async created() { },
  async mounted() {
    this.seafilter = this.filter;
    await this.onSearch();
    await this.getrole();
    // if (this.role == "b") {
      await this.ShowHideonSearch("b");
    // }
  },
  methods: {
    async getrole() {
      var res = await getUserRoleList();
      if (res?.success) {
        if (res.data == null) {
          this.role = "tz";
        } else if (res.data.indexOf("视觉部经理") > -1) {
          this.role = "b";
        }

      } else {
        this.role = "tz";
      }

    },
    async topSearch(searchfilter) {
      this.seafilter = {};
      this.seafilter = {
        ...searchfilter,
        ...this.filter
      }
      await this.onSearch();
    },
    //查询
    async onSearch() {
      this.$refs.pager.setPage(1);
      this.getTaskList();
      this.selids = [];
    },
    //刷新当前页
    async onRefresh() {
      await this.getTaskList();
    },
    //获取数据
    async getTaskList() {
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...this.seafilter
      };
      this.listLoading = true;
      let res = {}
      if (this.listtype == 4) {
        if (this.versionId != "0") {
          res = await getHistoryShortVideoTaskInfo(params);
        } else {
          res = await pageShortVideoCaclTaskAsync(params);
        }
      } else {
        res = await pageViewTaskAsync(params);
      }
      this.listLoading = false;
      if (res?.success) {
        this.total = res.data.total
        this.tasklist = res.data.list;
        this.summaryarry = res.data.summary;
      }
    },
    //列表排序
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    //显示-默认 b默认。a全部
    async ShowHideonSearch(com) {
      this.listLoading = true;
      var checkedColumnsFora = [];
      switch (com) {
        //显示全部 ,部门经理，超管
        case "a":
          checkedColumnsFora = [];
          break;
        //显示默认
        case "b":
          // checkedColumnsFora = [];
          checkedColumnsFora =
            ['Divisionline', 
              , 'claimTime1Str'
              , 'claimTime2Str'
              , 'claimTime3Str'
              , 'claimTime4Str'
              , 'claimTime5Str'
              , 'arrivalTimeDays', 'deliverTimeDays', 'applyTimeDays'
              , 'applyTime'
              , 'claimAudioTime1Str','claimAudio1','cutClaimDay1','cutClaimTime1Str'
              , 'claimAudioTime2Str','claimAudio2','cutClaimDay2','cutClaimTime2Str'
              , 'claimAudioTime3Str','claimAudio3','cutClaimDay3','cutClaimTime3Str'
              , 'claimAudioTime4Str','claimAudio4','cutClaimDay4','cutClaimTime4Str'
              , 'claimAudioTime5Str','claimAudio5','cutClaimDay5','cutClaimTime5Str'
            ];
          break;
        default:
          break;
      }
     
      await this.$refs.vediomaintask.ShowHidenColums(checkedColumnsFora);
      this.listLoading = false;
    },
    async selectchangeevent(records) {
      this.selids = [];
      records.forEach(f => {
        this.selids.push(f.videoTaskId);
      })
    },
    async rowChange(row) {
      await this.editTask(row);
    },
    async onsummaryClick(property) {

    },
    //紧急程度按钮点击
    async shootUrgencyCilck(row) {

    },
    async taskUrgencyApp() {

    },
    //查看成果文件
    openComputOutInfo(row) {
      if (this.listtype != 2) return;
      this.selectRowKey = row.videoTaskId;
      this.successfileshow = true;
    },
    //查看参考附件
    videotaskuploadfileDetal(row) {
      if (this.listtype != 2) return;
      let routeUrl = this.$router.resolve({
        path: '/VedioCutInfo',
        query: { id: row.videoTaskId }
      });
      window.open(routeUrl.href, '_blank');
    },
    //查看详情备注页
    openTaskRmarkInfo(row) {
      if (this.listtype != 2) return;
      this.selectRowKey = row.videoTaskId;
      this.viewReferenceRemark = true;
    },
    onUploadVideoAudio(row, ckindex) {
      this.videoTaskId = row.videoTaskId;
      this.ckindex = ckindex;
      this.approveTaskshow = true;
    },
    onUploadVideoAudioClose(index) {

      this.approveTaskshow = false;
    },
    //新增编辑窗口关闭
    async onCloseAddForm(type) {
      if (type == 1) {
        this.addTask = false;
      } else if (type == 2) {
        this.addTask = false;
        this.onSearch();
      } else {
        this.editTaskshow = false;
        this.onSearch();
      }
    },
    //导出事件
    async onExport() {
      this.$refs.vediomaintask.exportData("短视频列表导出");
    },
    //新增任务
    async onAddTask() {

    },
    //编辑任务
    async editTask(row) {
      this.editTaskshow = true;
      await this.$nextTick(function () {
        this.$refs.videotaskeditform.editTask(row);
      });
    },
    //批量操作相关------------------------------------------------------------------------------------
    async handleCommand(command) {
      if (this.selids.length == 0 && command != 'x') {
        this.$message({ type: 'warning', message: "请选择任务" });
        return;
      }
      switch (command) {
        case 'a'://批量终止重启
          await this.unEndTaskAction(this.selids);
          break;
        case 'b'://批量终止
          await this.endTaskAction(this.selids);
          break;
        case 'c'://批量标记
          await this.signTaskAction(this.selids);
          break;
        case 'd'://取消标记
          await this.unSignTaskAction(this.selids);
          break;
        case 'e'://批量删除
          await this.deleteTaskAction(this.selids);
          break;

        case 'g'://批量统计
          await this.caclTaskAction(this.selids);
          break;

        case 'j'://批量存档
          await this.shopTaskAction(this.selids);
          break;
        case 'k'://取消存档
          await this.unShopTaskAction(this.selids);
          break;
        case 'l'://取消统计
          await this.unCaclTaskAction(this.selids);
          break;
        case 'm'://批量完成
          await this.overTaskAction(this.selids);
          break;
      }
    },
    //批量终止重启
    async endTaskAction(array) {
      this.$confirm("终止任务，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await endTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
    //批量终止
    async unEndTaskAction(array) {
      this.$confirm("重启任务，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await unEndTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
    //批量标记
    async signTaskAction(array) {
      this.$confirm("标记任务，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await signTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
    //取消标记
    async unSignTaskAction(array) {
      this.$confirm("取消标记，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await unSignTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
    //批量删除
    async deleteTaskAction(array) {
      this.$confirm("删除任务，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await deleteTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
    //批量统计
    async caclTaskAction(array) {
      this.$confirm("批量统计，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await caclTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
    //批量存档
    async shopTaskAction(array) {
      this.$confirm("任务存档，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await shopTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
    //取消存档
    async unShopTaskAction(array) {
      this.$confirm("取消存档，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await unShopTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
    //取消统计
    async unCaclTaskAction(array) {
      this.$confirm("取消统计，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await unCaclTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
    //批量完成
    async overTaskAction(array) {
      this.$confirm("批量完成任务，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await overTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
    async sumbitshootingTaskRemark() {
        this.shootingTaskRemarkrawer = true;
        await this.$refs.shootingTaskRemark.onsubmit();
        this.shootingTaskRemarkrawer = false;
      },
        //查看详情备注页
     openTaskRmarkInfo(row) {
        this.markopentime = this.markopentime + 1;
        this.selectRowKey = row.videoTaskId;
        this.viewReferenceRemark = true;
    },
  },
};
</script>
<style lang="scss" scoped>
.dialog1 ::v-deep .el-dialog__body {
  padding: 0 !important;
  // background-color: red;
}

.dialog1 ::v-deep .el-drawer__header {
  display: none !important;
}

.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 15px;
}

::v-deep .myheader {
  padding: 5px 0px 0px 5px;
}
</style>
