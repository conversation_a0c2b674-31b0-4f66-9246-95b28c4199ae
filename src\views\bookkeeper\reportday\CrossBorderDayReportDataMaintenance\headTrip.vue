<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <!-- <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss" /> -->
                <el-input v-model.trim="ListInfo.warehouse" placeholder="仓库" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.batch" placeholder="批次" maxlength="50" clearable class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button
                    @click="importProps" type="primary" icon="el-icon-share" @command="handleCommand"> 导入
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item class="Batcoperation" style="padding: 0 25px"
                            command="a">下载模版</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
                <el-button type="primary" @click="onExport" style="margin-left: 5px;" v-if="checkPermission('DailyDataMaintenance_kj_export')">导出</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :showsummary='true'
            :summaryarry='summaryarry' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
            :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { GetFirstLeg_BanTuo_TemuAsync, ImportIncidentalExpensesAsync } from '@/api/bookkeeper/reportdayV2'
import { firstLeg_BanTuo_Temu_Export } from '@/api/bookkeeper/crossBorderV2'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'batch', label: '批次', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouse', label: '仓库', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'transportationMode', label: '运输方式', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'quantity', label: '商品数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalShippingCost', label: '总头程运费', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'individualShippingCost', label: '单个商品头程', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'firstLegDate', label: '开始计算时间', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            fileList: [],
            importLoading: false,
            importVisible: false,
            file: null,
            summaryarry: {}
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            //没有时间就提示
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            form.append("billingType", 2);
            this.importLoading = true
            await ImportIncidentalExpensesAsync(form).then(({ success }) => {
                if (success) {
                    this.$message.success('导入成功')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            if (this.timeRanges.length == 0) {
                //默认给近7天时间
                this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
                this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetFirstLeg_BanTuo_TemuAsync(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.summaryarry = data.summary
                    this.loading = false
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        async handleCommand(command) {
            switch (command) {
                //下载模版
                case 'a':
                    await this.downLoadFile()
                    break;
            }
        },
        async downLoadFile() {
            window.open("/static/excel/CrossBorderDailyDataMaintenance/头程导入模版.xlsx", "_blank");
        },

        async onExport() {//导出列表数据；
            var res = await firstLeg_BanTuo_Temu_Export(this.ListInfo);
            if (res?.success) {
                this.$message({ message: res.msg, type: "success" });
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>
