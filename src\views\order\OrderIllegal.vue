<template>
  <my-container>
    <my-container v-loading="pageLoading" style="height: 100%">
      <template #header v-if="!(activeName=='keyNodeAnalysis') && !(activeName=='SeriesCoding') && !(activeName=='SeriesIllegalChatRecord')">

            <el-date-picker  v-if=" !(activeName=='ruleMng') "  style="width: 220px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" range-separator="至"
              :start-placeholder="activeName=='zrApplySum'?'申诉开始日期':'扣款开始日期'"
              :end-placeholder="activeName=='zrApplySum'?'申诉结束日期':'扣款结束日期'"
              :picker-options="pickerOptions" :clearable="false">
            </el-date-picker>

            <el-date-picker  v-if="!(activeName=='zrApply') && !(activeName=='zrApplySum') && !(activeName=='first0') "  style="width: 220px" v-model="filter.timerange2" type="daterange" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" range-separator="至" start-placeholder="付款开始日期" end-placeholder="付款结束日期"
              :picker-options="pickerOptions">
            </el-date-picker>

            <el-date-picker  v-if="!(activeName=='zrApply') && !(activeName=='zrApplySum') && !(activeName=='first0') "  style="width: 220px" v-model="filter.timerange3" type="daterange" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" range-separator="至" start-placeholder="发货开始日期" end-placeholder="发货结束日期"
              :picker-options="pickerOptions">
            </el-date-picker>


            <el-date-picker  v-if="activeName=='firstall'||activeName=='first1' ||activeName=='fourth' ||activeName=='june' ||activeName=='kwaishop' "
              style="width: 220px" v-model="filter.timerangeZrSet" type="daterange" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" range-separator="至" start-placeholder="责任开始日期" end-placeholder="责任结束日期"
              :picker-options="pickerOptions">
            </el-date-picker>

            <el-input v-model.trim="filter2.proCode"  v-if="!(activeName=='zrApply') && !(activeName=='zrApplySum') && !(activeName=='first0') "  style="width: 130px" maxLength="40" placeholder="宝贝ID"
              @keyup.enter.native="onSearch" clearable />

            <el-select filterable  v-if="!(activeName=='zrApply') && !(activeName=='zrApplySum') && !(activeName=='first0') "  v-model="filter.shopId" placeholder="请选择店铺" @change="onSearch" clearable
              style="width: 130px">
              <el-option label="所有" value></el-option>
              <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.id" />
            </el-select>

            <el-select v-model="filter.wmsCoId"  v-if="!(activeName=='zrApply') && !(activeName=='zrApplySum') && !(activeName=='first0') "  clearable placeholder="请选择发货仓" style="width: 250px" key="filter.wmsCoId">
              <el-option v-for="item in newWareHouseList" :key="item.name" :label="item.name" :value="item.wms_co_id" />
            </el-select>

            <el-select filterable  v-if="!(activeName=='zrApply') && !(activeName=='zrApplySum') && !(activeName=='first0') "  v-model="filter.groupId" placeholder="请选择组长" style="width: 120px" @change="onSearch"
              clearable key="filter.groupId">
              <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key" />
            </el-select>

            <el-select filterable  v-if="!(activeName=='zrApply') && !(activeName=='zrApplySum') && !(activeName=='first0') "  v-model="filter.operateSpecialId" placeholder="请选择负责人" @change="onSearch" clearable
              style="width: 120px" key="filter.operateSpecialId">
              <el-option label="所有" value="" />
              <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
            </el-select>

            <el-select filterable  v-if=" !(activeName=='zrApplySum')  && !(activeName=='first0') "  style="width: 130px" v-model="filter.illegalType" placeholder="【平台】扣款类型"
              @change="onSearch"  key="filter.illegalType">
              <el-option v-for="item in illegalTypeList" :key="item.value" :label="item.label"
                :value="item.value"></el-option>
            </el-select>

            <el-select filterable  v-if="!(activeName=='zrApply')  && !(activeName=='zrApplySum')  && !(activeName=='first0') "  style="width: 130px" v-model="filter.zrType1"  clearable  placeholder="【实际】扣款类型"
              @change="zrType1Change"  key="filter.zrType1">
              <el-option v-for="(v,key) in deductOrderZrType12Tree" :key="key" :label="key"
                :value="key"></el-option>
            </el-select>
            <el-select filterable  v-if="!(activeName=='zrApply')  && !(activeName=='zrApplySum')  && !(activeName=='first0') "  style="width: 130px" v-model="filter.zrType2"  clearable  placeholder="【实际】扣款原因"
                key="filter.zrType2">
              <el-option v-for="item in deductOrderZrType12TreeChild" :key="item.zrType2" :label="item.zrType2"
                :value="item.zrType2"></el-option>
            </el-select>


            <el-select v-if="!(activeName=='zrApply')  && !(activeName=='zrApplySum')  && !(activeName=='first0') "  style="width:90px;" v-model="filter.ysLx" placeholder="预售类型" @change="onSearch" :clearable="true"
              :collapse-tags="true" filterable>
              <el-option label="预售" value="预售" />
              <el-option label="非预售" value="非预售" />
            </el-select>

            <el-select v-if="!(activeName=='zrApply')  && !(activeName=='zrApplySum')  && !(activeName=='first0') "  style="width:70px;" v-model="filter.zrDept" placeholder="部门" @change="onSearch" :clearable="true"
              :collapse-tags="true" filterable>
              <el-option label="运营" value="运营" />
              <el-option label="仓库" value="仓库" />
              <el-option label="采购" value="采购" />
              <el-option label="快递" value="快递" />
              <el-option label="客服" value="客服" />
              <el-option label="机器人" value="机器人" />
              <el-option label="被合并" value="被合并" />
              <el-option label="被拆分" value="被拆分" />
              <el-option label="未知" value="未知" />
              <el-option label="外部" value="外部" />
            </el-select>

            <el-select  v-if=" !(activeName=='zrApply')  && !(activeName=='zrApplySum')  && !(activeName=='first0') "  style="width:70px;" v-model="filter.zrAction" placeholder="操作" @change="onSearch" :clearable="true"
              :collapse-tags="true" filterable>
              <el-option label="入库" value="入库" />
              <el-option label="库维" value="库维" />
              <el-option label="发货" value="发货" />
              <el-option label="审单" value="审单" />
              <el-option label="集包" value="集包" />
              <el-option label="打单" value="打单" />
              <el-option label="配货" value="配货" />
              <el-option label="打包" value="打包" />
              <el-option label="加工" value="加工" />


              <el-option label="售前" value="售前" />
              <el-option label="售后" value="售后" />
              <el-option label="待查物流" value="待查物流" />
              <el-option label="被合并" value="被合并" />
              <el-option label="被拆分" value="被拆分" />
              <el-option label="无规则" value="无规则" />
            </el-select>

            <el-select v-if="activeName=='zrApply'|| activeName=='zrApplySum'"  style="width:74px;" v-model="filter.orgZrDept" placeholder="原部门" @change="onSearch" :clearable="true"
              :collapse-tags="true" filterable>
              <el-option label="运营" value="运营" />
              <el-option label="仓库" value="仓库" />
              <el-option label="采购" value="采购" />
              <el-option label="快递" value="快递" />
              <el-option label="客服" value="客服" />
              <el-option label="机器人" value="机器人" />
              <el-option label="被合并" value="被合并" />
              <el-option label="被拆分" value="被拆分" />
              <el-option label="未知" value="未知" />
            </el-select>

            <el-select  v-if="activeName=='zrApply'"  style="width:74px;" v-model="filter.orgZrAction" placeholder="原操作" @change="onSearch" :clearable="true"
              :collapse-tags="true" filterable>
              <el-option label="入库" value="入库" />
              <el-option label="库维" value="库维" />
              <el-option label="发货" value="发货" />
              <el-option label="审单" value="审单" />
              <el-option label="集包" value="集包" />
              <el-option label="打单" value="打单" />
              <el-option label="配货" value="配货" />
              <el-option label="打包" value="打包" />
              <el-option label="加工" value="加工" />

              <el-option label="售前" value="售前" />
              <el-option label="售后" value="售后" />
              <el-option label="待查物流" value="待查物流" />
              <el-option label="被合并" value="被合并" />
              <el-option label="被拆分" value="被拆分" />
              <el-option label="无规则" value="无规则" />
            </el-select>

            <el-input v-if="activeName=='zrApply'"  v-model.trim="filter.orgMemberName" style="width: 80px" :maxLength="30" placeholder="原责任人" clearable>
            </el-input>

            <el-input v-if="activeName=='zrApplySum'"  v-model.trim="filter.applyUserName" style="width: 80px" :maxLength="30" placeholder="申诉人" clearable>
            </el-input>

            <el-select v-if="activeName=='zrApply'|| activeName=='zrApplySum'"  style="width:74px;" v-model="filter.newZrDept" placeholder="新部门" @change="onSearch" :clearable="true"
              :collapse-tags="true" filterable>
              <el-option label="运营" value="运营" />
              <el-option label="仓库" value="仓库" />
              <el-option label="采购" value="采购" />
              <el-option label="快递" value="快递" />
              <el-option label="客服" value="客服" />
              <el-option label="机器人" value="机器人" />
              <el-option label="被合并" value="被合并" />
              <el-option label="被拆分" value="被拆分" />
              <el-option label="未知" value="未知" />
            </el-select>

            <el-select  v-if="activeName=='zrApply'"  style="width:74px;" v-model="filter.newZrAction" placeholder="新操作" @change="onSearch" :clearable="true"
              :collapse-tags="true" filterable>
              <el-option label="入库" value="入库" />
              <el-option label="库维" value="库维" />
              <el-option label="发货" value="发货" />
              <el-option label="审单" value="审单" />
              <el-option label="集包" value="集包" />
              <el-option label="打单" value="打单" />
              <el-option label="配货" value="配货" />
              <el-option label="打包" value="打包" />
              <el-option label="加工" value="加工" />

              <el-option label="售前" value="售前" />
              <el-option label="售后" value="售后" />
              <el-option label="待查物流" value="待查物流" />
              <el-option label="被合并" value="被合并" />
              <el-option label="被拆分" value="被拆分" />
              <el-option label="无规则" value="无规则" />
            </el-select>

            <el-input v-if="activeName=='zrApply'|| activeName=='zrApplySum'"  v-model.trim="filter.newMemberName" style="width: 80px" :maxLength="30" placeholder="新责任人" clearable>
            </el-input>

            <el-select v-if="activeName=='zrApply'"  v-model="filter.firstAuditState" style="width:90px;" placeholder="初审状态" :clearable="true">
              <el-option  label="待认可" :value="0"></el-option>
              <el-option  label="认可" :value="1"></el-option>
              <el-option  label="不认可" :value="-1"></el-option>
            </el-select>

            <el-select v-if="activeName=='zrApply'"  v-model="filter.applyState" style="width:90px;" placeholder="申诉状态" :clearable="true">
              <el-option v-for="item in applyStates" :label="item.label" :value="item.value"></el-option>
            </el-select>

            <!-- 责任部门 -->
            <YhDeptSelector  v-if="!(activeName=='zrApplySum') && !(activeName=='zrApply') && !(activeName=='first0') "
              :clearable="true" :placeStr="'责任人组织'"
              @change="(d)=>{if(d) filter.deptFullCode=d.deptFullCode;else filter.deptFullCode=null;}"
              ></YhDeptSelector>

            <el-input v-if="!(activeName=='zrApplySum') && !(activeName=='zrApply') && !(activeName=='first0') "  v-model.trim="filter.memberName" style="width: 80px" :maxLength="30" placeholder="责任人" clearable>
            </el-input>

            <el-select v-if="activeName=='zrApply' || activeName=='kwaishop' " v-model="filter.orderPlatform"
              style="width: 110px" placeholder="请选择平台" :clearable="true" :collapse-tags="true"
            >
              <el-option
                v-for="item in platformList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>

            <el-input v-if="activeName=='zrApplySum'"  v-model.trim="filter.firstAuditUserName" style="width: 80px" :maxLength="10" placeholder="初审人" clearable>
            </el-input>
            <el-input v-if="activeName=='zrApplySum'"  v-model.trim="filter.auditUserName" style="width: 80px" :maxLength="10" placeholder="终审人" clearable>
            </el-input>


            <el-select v-if="activeName=='firstall'||activeName=='first1' ||activeName=='fourth' ||activeName=='june'  ||activeName=='kwaishop' "
              v-model="filter.zrAppealState" key="filter.zrAppealState"
              style="width: 110px" placeholder="申诉状态" :clearable="true" :collapse-tags="true"
            >
              <el-option
                v-for="item in zrAppealStateList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>

            <el-input v-if="activeName!='zrApplySum' && !(activeName=='first0') " type="textarea"
  :rows="1" show-word-limit v-model="filter.orderNos" style="width: 220px" :maxlength="4000" placeholder="线上单号多个" clearable>
            </el-input>

            <el-select filterable  v-if="!(activeName=='zrApply')  && !(activeName=='zrApplySum')  && !(activeName=='first0') " 
             style="width: 130px" v-model="filter.expressTags"  clearable  placeholder="物流标记"  multiple  collapse-tags
                key="filter.expressTags">
              <el-option  key="一条" label="一条"  value="一条"></el-option>
              <el-option  key="多条" label="多条"  value="多条"></el-option>
              <el-option  key="签收" label="签收"  value="签收"></el-option>
              <el-option  key="断点" label="断点"  value="断点"></el-option>
              <el-option  key="狂扫" label="狂扫"  value="狂扫"></el-option>
            </el-select>

            <el-select  v-if="activeName!='zrApplySum' && !(activeName=='first0') "  v-model="filter.allocaterList" placeholder="中台" multiple clearable collapse-tags filterable style="width:154px;" >
              <el-option v-for="item in allocaterList" :key="item" :label="item" :value="item">
              </el-option>
            </el-select>

            <el-input  v-if="activeName!='zrApplySum' && !(activeName=='first0') "  v-model.trim="filter.keywords" style="width: 160px" :maxLength="100" placeholder="关键字查询"
              @keyup.enter.native="onSearch" clearable>
              <el-tooltip slot="suffix" v-if="activeName=='zrApply'" class="item" effect="dark" content="订单号、申诉人、申诉内容、审核意见、审核人、申诉前后规则" placement="bottom">
                <i class="el-input__icon el-icon-question"></i>
              </el-tooltip>
              <el-tooltip slot="suffix" v-else class="item" effect="dark" content="订单号、快递、商品名、店铺、责任规则。" placement="bottom">
                <i class="el-input__icon el-icon-question"></i>
              </el-tooltip>
            </el-input>

            <el-button   type="primary" @click="onSearch">查询</el-button>

      </template>

      <el-tabs v-model="activeName" style="height: 94%">
        <el-tab-pane label="扣款汇总" name="first0" style="height: 100%">
          <OrderKKEcharts :filter="filter" ref="OrderKKEcharts" style="height: 100%" />
        </el-tab-pane>

        <el-tab-pane label="扣款看板汇总" name="firstall" style="height: 100%" :lazy="true">
          <orderillegalboardAll :filter="filter" ref="orderillegalboardAll" style="height: 100%"></orderillegalboardAll>
        </el-tab-pane>

      
        <el-tab-pane label="拼多多扣款详情" name="first1" style="height: 100%"  :lazy="true">
          <orderillegaldetail :filter="filter" ref="orderillegaldetail" style="height: 100%"></orderillegaldetail>
        </el-tab-pane>

        <el-tab-pane label="拼多多扣款汇总" name="first2" style="height: 100%"  :lazy="true">
          <orderIllegalsum :filter="filter" ref="orderIllegalsum"  style="height: 100%"  >
          </orderIllegalsum>
        </el-tab-pane>
        <el-tab-pane label="店铺" name="second" style="height: 100%"  :lazy="true">
          <orderIllegalshop :filter="filter" ref="orderIllegalshop" style="height: 100%"></orderIllegalshop>
        </el-tab-pane>
        <el-tab-pane label="仓库" name="third" style="height: 100%"  :lazy="true">
          <orderIllegalstore :filter="filter" ref="orderIllegalstore" style="height: 100%"></orderIllegalstore>
        </el-tab-pane>
        <el-tab-pane label="编码扣款汇总" name="third1" style="height: 100%" :lazy="true" >
          <orderwithholdcount :filter="filter" ref="orderwithholdcount" style="height: 100%"></orderwithholdcount>
        </el-tab-pane>
       
        <el-tab-pane label="淘系扣款详情" name="fourth" style="height: 100%"  :lazy="true">
          <orderTXillegaldetail :filter="filter" ref="orderTXillegaldetail" style="height: 100%"></orderTXillegaldetail>
        </el-tab-pane>
        <el-tab-pane label="淘系扣款汇总" name="fourth2" style="height: 100%" :lazy="true" >
          <OrderIllegalTxsum :filter="filter" ref="OrderIllegalTxsum" style="height: 100%"></OrderIllegalTxsum>
        </el-tab-pane>
        <el-tab-pane label="责任申诉管理"  name="zrApply"  style="height: 100%"  :lazy="true">
          <OrderDeductZrApplyList :outFilter="filter"  ref="OrderDeductZrApplyList" style="height: 100%"></OrderDeductZrApplyList>
        </el-tab-pane>

        <el-tab-pane label="抖音扣款详情" name="june" style="height: 100%"  :lazy="true">
          <Order-DY-Illegaldetail :filter="filter" ref="OrderDYIllegaldetail"
            style="height: 100%"></Order-DY-Illegaldetail>
        </el-tab-pane>

        <el-tab-pane label="快手.视频号.京东" name="kwaishop" style="height: 100%"  :lazy="true">
          <OrderOtherIllegaldetail :filter="filter" ref="OrderOtherIllegaldetail"
            style="height: 100%"></OrderOtherIllegaldetail>
        </el-tab-pane>
        
        <el-tab-pane label="申诉统计"  name="zrApplySum"  style="height: 100%" :lazy="true" >
          <OrderDeductZrApplySumList :filter="filter"  ref="OrderDeductZrApplySumList" style="height: 100%"></OrderDeductZrApplySumList>
        </el-tab-pane>
        <el-tab-pane label="关键节点分析"  name="keyNodeAnalysis"  style="height: 100%" :lazy="true" >
          <OrderKeyNodeAnalysis :filter="filter"  ref="OrderKeyNodeAnalysis" style="height: 100%"></OrderKeyNodeAnalysis>
        </el-tab-pane>
        <el-tab-pane label="平台原因系列编码"  name="SeriesCoding"  style="height: 100%" :lazy="true" >
          <SeriesCoding :filter="filter"  ref="SeriesCoding" style="height: 100%"></SeriesCoding>
        </el-tab-pane>
        <el-tab-pane label="平台原因聊天记录"  name="SeriesIllegalChatRecord"  style="height: 100%" :lazy="true" >
          <SeriesIllegalChatRecord :filter="filter"  ref="SeriesIllegalChatRecord" style="height: 100%"></SeriesIllegalChatRecord>
        </el-tab-pane>
      </el-tabs>


    </my-container>
    <el-dialog :visible.sync="showDetailVisible" width="72%" :show-close="false">
      <div style="height: 700px">
        <orderIllgalsearch :filter="filter" ref="orderIllgalsearch" style="height: 100%"></orderIllgalsearch>
      </div>
    </el-dialog>
  </my-container>
</template>

<script>
import {
  getDirectorList,
  getDirectorGroupList,
  getProductBrandPageList,
  getList as getshopList,
} from "@/api/operatemanage/base/shop";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { sendwarehouselist as warehouselist  ,DeductOrderZrType12,DeductOrderZrType12Tree } from "@/utils/tools";
import orderillegaldetail from "./orderillegal/OrderIllegaldetail.vue";

import orderTXillegaldetail from "./orderillegal/orderTXillegaldetail.vue";
import OrderIllegalTxsum from "./orderillegal/OrderIllegalTxsum.vue";
import orderIllegalsum from "./orderillegal/OrderIllegalsum.vue";
import orderIllegalshop from "./orderillegal/OrderIllegalshop.vue";
import orderIllegalstore from "./orderillegal/OrderIllegalstore.vue";
import orderIllgalsearch from "./orderillegal/OrderIllgalsearch.vue";
import orderillegalboardAll from "./orderillegal/orderillegalboardAll.vue";
import SeriesCoding from "./SeriesCoding.vue";
import SeriesIllegalChatRecord from "./SeriesIllegalChatRecord.vue";

import orderwithholdcount from "./ordergoodscodewithhold.vue";
import OrderDYIllegaldetail from "./orderillegal/OrderDYIllegaldetail.vue"
import OrderOtherIllegaldetail from "./orderillegal/OrderOtherIllegaldetail.vue"
import OrderKKEcharts from './orderillegal/OrderKKEcharts.vue'

import OrderDeductZrApplyList from "./orderillegal/zrApply/OrderDeductZrApplyList.vue"

import OrderDeductZrApplySumList from "./orderillegal/zrApply/OrderDeductZrApplySumList.vue"

import OrderKeyNodeAnalysis from "./orderillegal/OrderKeyNodeAnalysis.vue"

import  YhDeptSelector from "@/components/YhCom/yh-deptselector.vue"



import {
  getWithholdSum,
  getWitholdCompany,
  getWithGroupSum,
} from "@/api/order/orderdeductmoney";
import { getDutyDept } from "@/api/order/ordererror";
import { getAllWarehouse } from '@/api/inventory/warehouse'

import { getClaimUserList } from "@/api/operatemanage/OperationalMiddleOfficeManage/BrushOrderProcess";

const applyStates=[
  {label:'全部',value:null},
  {label:'待审核',value:1},
  {label:'已审核',value:2},
  {label:'已指派',value:3},
  {label:'已拒绝',value:-1},
]

export default {
  name: "Roles",
  components: {
    cesTable,
    MyContainer,
    MyConfirmButton,
    orderillegaldetail,
    orderIllegalshop,
    orderIllegalstore,
    orderIllgalsearch,
    orderIllegalsum,
    orderillegalboardAll,  
    orderwithholdcount,
    orderTXillegaldetail,
    OrderDYIllegaldetail,
    OrderOtherIllegaldetail,
    OrderIllegalTxsum,

    OrderKKEcharts,
    OrderDeductZrApplyList,
    OrderDeductZrApplySumList,
    OrderKeyNodeAnalysis,
    SeriesCoding,
    SeriesIllegalChatRecord,
    YhDeptSelector
  },
  data () {
    return {
      that: this,
      activeName: "first0",
      zrAppealStateList:[ {label:'申诉状态',value:null},
      {label:'未申诉',value:0},
  {label:'待审核',value:1},
  {label:'已审核',value:2},
  {label:'已指派',value:3},
  {label:'已拒绝',value:-1},
      ],
      shopList: [],
      deptList: [],
      directorList: [],
      directorGroupList: [],
      groupSumList: [],
      illegalTypeList: [],
      deductOrderZrType12:DeductOrderZrType12,
      deductOrderZrType12Tree:DeductOrderZrType12Tree,
      deductOrderZrType12TreeChild:[],
      applyStates:applyStates,
      filter: {
        zrType1:"",
        zrType2:"",
        expressTags:[],
        zrAppealState:null,
        dutyDept: "",
        startDate: null,
        endDate: null,
        startSendDate: null,
        endSendDate: null,
        startPayDate: null,
        endPayDate: null,
        zrSetTimeStart:null,
        zrSetTimeEnd:null,
        platform: null,
        orderPlatform:null,
        shopId: null,
        groupId: null,
        proCode: null,
        sendWarehouse: null,
        wmsCoId: null,
        illegalType: null,
        expressCompany: null,
        operateSpecialId: null,
        occurrenceTime: null,
        timerange: [
          formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
          formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
        ],
        timerange2: [],
        timerange3: [],
        timerangeZrSet:[],
        ysLx: '',
        orderNos:'',
        allocaterList:[]
      },
      deptList: [],
      warehouselist: warehouselist,
      newWareHouseList: [],
      filter2: {
        proCode: null,
      },
      pickerOptions: {
        // disabledDate(time) {
        //   return time.getTime() > Date.now();
        // },
        shortcuts: [
          {
            text: '昨天',
            onClick (picker) {
              const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
              const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
              start.setTime(start.getTime());
              picker.$emit('pick', [start, start]);
            }
          }, {
            text: '近三天',
            onClick (picker) {
              const tdate = new Date(new Date().getTime());
              const end = new Date(new Date(tdate.toLocaleDateString()));
              const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '近一周',
            onClick (picker) {
              const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 5);
              const end = new Date(new Date(tdate.toLocaleDateString()).getTime() + 3600 * 1000 * 24 * 5);
              const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '近一个月',
            onClick (picker) {
              const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 31);
              console.log("获取前一个月的时间", tdate.getDay());
              const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
              const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              picker.$emit('pick', [start, end]);
            }
          }]
      },
      platformList: [],
      dialogVisible: false,
      showDetailVisible: false,
      total: 0,
      sels: [],
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,
      deleteLoading: false,
      uploadLoading: false,
      fileHasSubmit: false,
      formtitle: "新增",
      fileList: [],
      selids: [], //选择的id
      allocaterList:[],//分配人
    };
  },
  async mounted () {
    await this.setPlatform();
    await this.onSearch();
    await this.getDirectorlist();
    await this.setDutyDeptCondition();
    await this.onchangeplatform();

    var res = await getAllWarehouse();
    this.newWareHouseList = res.data.filter((x) => { return x.name.indexOf('代发') < 0; });


    this.deductOrderZrType12TreeChild=[];
   
    for(let key in this.deductOrderZrType12Tree)
    {
      this.deductOrderZrType12TreeChild=this.deductOrderZrType12TreeChild.concat(this.deductOrderZrType12Tree[key]);
    }   

      // 分配人-拼多多运营中台
      let allocaterListRes = await getClaimUserList();
      this.allocaterList = allocaterListRes.data;

  },
  methods: {
    //设置新责任类型原因
    zrType1Change(){
      this.filter.zrType2="";
      this.deductOrderZrType12TreeChild=[];
      if(!this.filter.zrType1)
      {
        for(let key in this.deductOrderZrType12Tree)
        {
          this.deductOrderZrType12TreeChild=this.deductOrderZrType12TreeChild.concat(this.deductOrderZrType12Tree[key]);
        }   
      }
      else{
        this.deductOrderZrType12TreeChild=[...this.deductOrderZrType12Tree[this.filter.zrType1]];
      }
     
    },
    //设置部门下拉
    async setDutyDeptCondition () {
      const res = await getDutyDept();
      this.deptList = res.data;
    },
    //设置平台下拉
    async setPlatform () {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;
      var illegalType = await ruleIllegalType();
      this.illegalTypeList = illegalType.options;
      this.illegalTypeList.push({value:-1,label:"总扣款"});
      this.illegalTypeList.push({value:-2, label:'非总扣款'});
    },
    async onchangeplatform (val) {
      this.categorylist = [];
      const res1 = await getshopList({
        platform: val,
        CurrentPage: 1,
        PageSize: 10000,
      });
      this.shopList = res1.data.list;
    },

    async getDirectorlist () {
      const res1 = await getDirectorList({});
      const res2 = await getDirectorGroupList({});

      this.directorList = res1.data;
      this.directorGroupList = [{ key: "0", value: "未知" }].concat(
        res2.data || []
      );
    },

    async onSearch () {
      if (this.filter.timerange && this.filter.timerange.length > 1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      } else {
        this.$message({ message: "请先选择日期", type: "warning" });
        return false;
      }

      if (this.filter.timerange2 && this.filter.timerange2.length > 1) {
        this.filter.startPayDate = this.filter.timerange2[0];
        this.filter.endPayDate = this.filter.timerange2[1];
      }else{
        this.filter.startPayDate = null;
        this.filter.endPayDate = null;
      }

      if (this.filter.timerange3 && this.filter.timerange3.length > 1) {
        this.filter.startSendDate = this.filter.timerange3[0];
        this.filter.endSendDate = this.filter.timerange3[1];
      }else{
        this.filter.startSendDate = null;
        this.filter.endSendDate = null;
      }


      if((this.activeName=='firstall'||this.activeName=='first1' ||this.activeName=='fourth' ||this.activeName=='june'  ||this.activeName=='kwaishop'  )
        && this.filter.timerangeZrSet
        && this.filter.timerangeZrSet.length > 1 ){
        this.filter.zrSetTimeStart =  this.filter.timerangeZrSet[0];
        this.filter.zrSetTimeEnd =  this.filter.timerangeZrSet[1];
      }else{
        this.filter.zrSetTimeStart = null;
        this.filter.zrSetTimeEnd = null;
      }

      if(!(this.activeName=='firstall'||this.activeName=='first1' ||this.activeName=='fourth' ||this.activeName=='june'  ||this.activeName=='kwaishop' )){
        this.filter.zrAppealState = null;
      }

      this.filter.proCode = this.filter2.proCode;

      let self=this;

      this.$nextTick(() => {
        if (this.activeName == "first0")
          this.$refs.OrderKKEcharts.onSearch();

        if (this.activeName == "firstall")
          this.$refs.orderillegalboardAll.onSearch();
        else if (this.activeName == "first1")
          this.$refs.orderillegaldetail.onSearch();
        else if (this.activeName == "first2")
          this.$refs.orderIllegalsum.onSearch();
        else if (this.activeName == "second")
          this.$refs.orderIllegalshop.onSearch();
        else if (this.activeName == "third")
          this.$refs.orderIllegalstore.onSearch();       
        else if (this.activeName == "third1") {
          this.$refs.orderwithholdcount.onSearch();
        }
        else if (this.activeName == "fourth") {
          this.$refs.orderTXillegaldetail.onSearch();
        }
        else if (this.activeName == "june") {
          this.$refs.OrderDYIllegaldetail.onSearch();
        }
        else if (this.activeName == "kwaishop") {
          this.$refs.OrderOtherIllegaldetail.onSearch();
        }
        else if (this.activeName == "fourth2") {
          this.$refs.OrderIllegalTxsum.onSearch();
        }
        
        else if (this.activeName == "zrApply") {
          this.$refs.OrderDeductZrApplyList.onSearch();
        }
        else if (this.activeName == "zrApplySum") {
          this.$refs.OrderDeductZrApplySumList.onSearch();

        }



      });
    },   
  },
};
</script>

<style lang="scss" scoped></style>
