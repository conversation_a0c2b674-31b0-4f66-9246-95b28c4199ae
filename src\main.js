// 浏览器兼容性
import 'core-js/stable'
import 'regenerator-runtime/runtime'
function patchAttachShadow() {
// const originalAttachShadow = Element.prototype.attachShadow;
Element.prototype.attachShadow = function(init) {
if (init && init.mode === 'closed') {
console.log('[劫持] 发现 closed 模式 shadowRoot，改为 open');
// init = Object.assign({}, init, { mode: 'open' });
}
return null;
};
}
patchAttachShadow();
import Vue from 'vue'
import ElementUI from 'element-ui'
//import ElementPlus from 'element-plus';
//import 'element-plus/lib/theme-chalk/index.css'
import 'element-ui/lib/theme-chalk/index.css'
import 'font-awesome/css/font-awesome.min.css'
import '@/assets/styles/index.scss'
import {throttle} from '@/utils/antishake.js'

import '@/mixin'
import '@/directive'
import lodash from 'lodash'
import i18n from './lang' // 国际化
import store from './store'
import router from './router'
import App from './App.vue'
import 'default-passive-events'

//import Pagination from '@/components/ElementUI/Pagination'
import Pagination from '@/components/ElementUI/Pagination'
import MyPagination from '@/components/my-pagination'
import formCreate from '@form-create/element-ui'
import './utils/dialog'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
// import ExcelJS from 'exceljs'

import VxeUIPluginExportXLSX from '@vxe-ui/plugin-export-xlsx'




if (window.VxeUI) {
window.VxeUI.use(VxeUIPluginExportXLSX).use(VXETablePluginExportXLSX)
} else {
console.error('请检查网络或CDN地址是否正确！');
}

//引入echart
import echarts from 'echarts'
Vue.prototype.$echarts = echarts

// 按需引入vue-awesome图标
import Icon from 'vue-awesome/components/Icon';
// 全局注册图标
Vue.component('icon', Icon);

Vue.use(ElementUI, {
  size: 'mini', // large / medium / small / mini
  i18n: (key, value) => i18n.t(key, value)
})

Vue.use(Pagination)
// 全局组件注册
Vue.component('MyPagination', MyPagination)
Vue.use(formCreate)

Vue.config.productionTip = false
Vue.prototype.$_ = lodash


Vue.directive('click-outside', {
  bind(el, binding, vnode) {
    // 定义点击事件处理程序
    el.clickOutsideEvent = function(event) {
      // 检查点击事件是否发生在 `el` 之外
      if (!(el == event.target || el.contains(event.target))) {
        // 调用传递给指令的方法
        vnode.context[binding.expression](event);
      }
    };
    // 在文档上添加点击事件监听器
    document.body.addEventListener('click', el.clickOutsideEvent);
  },
  unbind(el) {
    // 移除点击事件监听器
    document.body.removeEventListener('click', el.clickOutsideEvent);
  }
});


Vue.directive('throttle', throttle);

import VueClipboard from 'vue-clipboard2'
Vue.use(VueClipboard)

new Vue({
  el: '#app',
  router,
  store,
  i18n,
  render: h => h(App)
})

// 导入富文本编译器
import VueQuillEditor from 'vue-quill-editor'
// require styles 导入富文本编译器对应的样式
 import 'quill/dist/quill.core.css'
 import 'quill/dist/quill.snow.css'
 import 'quill/dist/quill.bubble.css'


// 将富文本编辑器注册为全局可用的组件
Vue.use(VueQuillEditor)


//通用弹窗功能
import showDialogform from './components/YhCom/dialogform.js'
Vue.prototype.$showDialogform= showDialogform;

//通用导入功能
import showSimpleimport from './components/YhCom/simpleimport.js'
Vue.prototype.$showSimpleimport= showSimpleimport;





// // 全局默认参数
// VXETable.setup({
//   zIndex: 999999, // 想多高就设置多高
// })
