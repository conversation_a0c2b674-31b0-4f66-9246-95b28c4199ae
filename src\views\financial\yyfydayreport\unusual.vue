<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true'
              :hasexpand='false' @sortchange='sortchange' :tableData='unusuallist'
              @select='selectchange' :isSelection='false'
         :tableCols='tableCols' :loading="listLoading">
      <el-table-column type="expand">
        <template slot-scope="props">
        <div>
          <el-table :data="props.row.detaildata" style="width: 100%">
            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
            </el-table-column>
          </el-table>
        </div>
      </template>
      </el-table-column>
       <template slot='extentbtn'>
          <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                   <el-input v-model="Filter.id" placeholder="数据ID" style="width:120px;"/>
                 </el-button>
                            <el-button style="padding: 0;margin: 0;">
                                <el-input v-model="Filter.ShopName" placeholder="店铺" style="width:120px;"/>
                            </el-button>
                            <el-button style="padding: 0;margin: 0;">
                                <el-input v-model="Filter.FeeType" placeholder="费用类型" style="width:120px;"/>
                            </el-button>
                            <el-button style="padding: 0;margin: 0;">
                                <el-input v-model="Filter.Mark" placeholder="备注" style="width:120px;"/>
                            </el-button>
            <!-- <el-button type="text" size="medium" disabled style="color: red;">周转对比:</el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input-number v-model="filter1.startdiff"></el-input-number>至<el-input-number v-model="filter1.enddiff"></el-input-number>
            </el-button> -->
            <el-button type="primary" @click="onSearch">查询</el-button>

           
          </el-button-group>
        </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getunusualList"
      />
    </template>

    <el-dialog title="异常数据表" :visible.sync="dialogVisibleSyj" width="30%">
      <span>
          <el-upload ref="upload2" class="upload-demo"
                  :auto-upload="false"
                  :multiple="false"
                  :limit="1"
                  action
                  accept=".xlsx"
                  :http-request="uploadFile2"
                  :on-success="uploadSuccess2">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">上传</my-confirm-button>
          </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyj = false">关闭</el-button>
      </span>
    </el-dialog>



<el-dialog title="摊派店铺和组选择" :visible.sync="betanpan" width="30%">
      <span><el-form v-model="tp">
          <el-form-item label="平台:" style="display:flex;">
          <el-select
            v-model="TPFilter.platform"
            placeholder="请选择"
            class="el-select-content"
            @change="changePlatform"
            style="width: 200px"
          >
            <el-option
              v-for="item in platformList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

          <el-form-item label="店铺:"  style="display:flex;">
          <el-select
            v-model="TPFilter.shopid"
            placeholder="请选择"
            class="el-select-content"
              style="width: 200px"
          >
            <el-option label="所有" value></el-option>
            <el-option
              v-for="item in shopList"
              :key="item.id"
              :label="item.shopName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
         
        <el-form-item label="运营组:" style="display:flex;">
          <el-select
            v-model="TPFilter.groupId"
            placeholder="请选择"
            class="el-select-content"
              style="width: 200px"
          >
            <el-option label="所有" value></el-option>
            <el-option
              v-for="item in groupList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>  
</el-form>
      </span>
      <span slot="footer" class="dialog-footer">
         <el-button type="primary" @click="tanpan">摊派</el-button>
        <el-button @click="betanpan = false">关闭</el-button>
      </span>
    </el-dialog>





  </my-container>
</template>
<script>

import {importUnusualAsync,getUnusualList,deleteUnusualBatch,chooseUnusualGroupCompute } from '@/api/financial/yyfyday'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

import { getGroupKeyValue } from "@/api/operatemanage/base/product";
import { getList as getshopList } from "@/api/operatemanage/base/shop";
import { rulePlatform, ruleSendWarehouse } from "@/utils/formruletools";





const tableCols =[
             {istrue:true,prop:'shopName',label:'店铺', width:'200',sortable:'custom'},
             {istrue:true,prop:'feeType',label:'费用类型', width:'200',sortable:'custom'},
             {istrue:true,prop:'dataID',label:'数据ID', width:'200',sortable:'custom'},
             {istrue:true,prop:'mark',label:'备注', width:'200',sortable:'custom'},
             {istrue:true,prop:'useMoney',label:'金额', width:'200',sortable:'custom'},
      {istrue:true,prop:'createdTime',label:'导入时间', width:'200',sortable:'custom'},
      {istrue:true,prop:'batchNumber',label:'导入批次', width:'200',sortable:'custom'},
      {istrue: true,type: "button",width: "430",label: '操作', btnList: [
      { label: "手动摊派", handle: (that, row) => that.showtanpan(row)},
      { label: "删除批次", handle: (that, row) => that.deleteBatch(row)}]}
     ];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
  data() {
    return {
      that:this,
      Filter: {
      },
      TPFilter:{},
      
      platformList: [],
      shopList: [],
      groupList: [],
      userList:[],     
      unusuallist: [],

      tableCols:tableCols,
      total: 0,
      summaryarry:{count_sum:10},
      pager:{OrderBy:"id",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      //
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
      betanpan:false,
      tp:{},
      betanpandata:{},


    };
  },
  async mounted() {
        await this.setGroupSelect();

    await this.setPlatform();
  },
  methods: {
    tanpan(){

      var that=this;
     chooseUnusualGroupCompute({id:this.betanpandata.id,groupid:this.TPFilter.groupId}).then(
        res=>{

          if(res.data)
          {
           this.$message({message: '摊派成功', type: "success"});
          that.onSearch();

          }
          else
          {
              this.$message({message: '摊派过程出现错误', type: "error"});
                that.onSearch();
          }









        }




      );



    },
     async changePlatform(val) {
      const res1 = await getshopList({
        platform: val,
        CurrentPage: 1,
        PageSize: 1000,
      });
      this.shopList = res1.data.list;
     // if (val) this.getcategorylist(val);
    },
    async setGroupSelect() {
      const res = await getGroupKeyValue({});
      this.groupList = res.data;
    },
    showtanpan(row){

      this.betanpan=true;
      this.betanpandata=row;


    },
     
    async setPlatform() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;
    },
   async deleteBatch(row){
      var that=this;
      this.$confirm("此操作将删除此批次异常数据表数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async() => {
      await deleteUnusualBatch({batchNumber:row.batchNumber})
      that.$message({message: '已删除', type: "success"});
      that.onRefresh()

        });

    },
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    onImportSyj(){
      this.dialogVisibleSyj = true
    },
    async uploadFile2(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      const res = importUnusualAsync(form);
      this.$message({message: '上传成功,正在导入中...', type: "success"});
    },
    async uploadSuccess2(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
    },
    async onSubmitupload2() {
      this.$refs.upload2.submit()
    },
    onRefresh(){
        this.onSearch()
    },
    onSearch(){
       this.$refs.pager.setPage(1);
       this.getunusualList();
    },
    async getunusualList(){
      if(this.Filter.UseDate){
        this.Filter.startAccountDate=this.Filter.UseDate[0];
         this.Filter.endAccountDate=this.Filter.UseDate[1];
       }
      const para = {...this.Filter};
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,

      };

      console.log(para)

      this.listLoading = true;
      const res = await getUnusualList(params);
      console.log(res)
      this.listLoading = false;
      console.log(res.data.list)
      //console.log(res.data.summary)

      this.total = res.data.total
      this.unusuallist = res.data.list;
      //this.summaryarry=res.data.summary;
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
