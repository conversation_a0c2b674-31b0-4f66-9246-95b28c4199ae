<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="110px" class="demo-ruleForm">
        <el-form-item label="场次" prop="session">
          <inputNumberYh v-model="ruleForm.session" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
        <el-form-item label="人数" prop="peopleNumber">
          <inputNumberYh v-model="ruleForm.peopleNumber" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
        <el-form-item label="下午茶" prop="afternoonTeaCost">
          <inputNumberYh v-model="ruleForm.afternoonTeaCost" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
        <el-form-item label="餐厅" prop="restaurantCost">
          <inputNumberYh v-model="ruleForm.restaurantCost" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
        <el-form-item label="酒店" prop="hotelCost">
          <inputNumberYh v-model="ruleForm.hotelCost" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
        <el-form-item label="活动" prop="activity">
          <inputNumberYh v-model="ruleForm.activity" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" v-model="ruleForm.remarks" placeholder="请输入" clearable :maxlength="100"
            show-word-limit :autosize="{ minRows: 6, maxRows: 6 }" class="publicCss" />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { pRReceptionSubmit } from '@/api/people/peoplessc.js';
export default {
  name: 'departmentEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  data() {
    return {
      ruleForm: {
        session: '',
        peopleNumber: '',
        afternoonTeaCost: '',
        restaurantCost: '',
        hotelCost: '',
        activity: '',
        remarks: '',
      },
      rules: {
        session: [
          { required: true, message: '请输入场次', trigger: 'blur' },
        ],
        afternoonTeaCost: [
          { required: true, message: '请输入下午茶', trigger: 'blur' },
        ],
        peopleNumber: [
          { required: true, message: '请输入人数', trigger: 'blur' },
        ],
        restaurantCost: [
          { required: true, message: '请输入餐厅', trigger: 'blur' },
        ],
        hotelCost: [
          { required: true, message: '请输入酒店', trigger: 'blur' },
        ],
        activity: [
          { required: true, message: '请输入活动', trigger: 'blur' },
        ],
      }
    }
  },

  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };
  },
  methods: {
    cancellationMethod() {
      this.$emit('cancellationMethod');
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          const { data, success } = await pRReceptionSubmit(this.ruleForm)
          if (!success) {
            return
          }
          this.$emit("search");
        } else {
          console.error('submit failed, reason: ', valid);
          return false;
        }
      });
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}
</style>
