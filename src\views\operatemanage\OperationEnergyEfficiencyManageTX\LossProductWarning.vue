<template>
    <my-container v-loading="pageLoading">

          <el-row>
            <el-col :span="24"><div class="div2">
                <div class="div2-1">
                    <el-button-group>
                    <el-button style="padding: 0;margin: 0;">
                        <el-date-picker style="width: 210px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false" :picker-options="pickerOptions">
                        </el-date-picker>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;  margin-left: 10px;">
                      <el-input v-model.trim="filter.ProCode" maxlength="50" clearable placeholder="商品ID" style="width:150px;" />
                    </el-button>
                    <el-button style="padding: 0;margin-left: 10px;" >
                        <el-select filterable v-model="filter.Platform" collapse-tags  placeholder="平台"
                          style="width: 90px">
                          <el-option label="淘系" :value="1" />
                          <el-option label="淘工厂" :value="8" />
                        </el-select>
                      </el-button>
                    <el-button style="padding: 0;margin-left: 10px;" v-if="checkPermission('NXOperationGroup')">
                    <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组"
                      style="width: 90px">
                      <el-option key="无运营组" label="无运营组" :value="0"></el-option>
                      <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-button>
                  <el-button style="padding: 0; margin-left: 10px;" v-if="checkPermission('NXOperationoperateSpecialUserId')">
                    <el-select filterable v-model="filter.operateSpecialUserId" collapse-tags clearable placeholder="运营专员"
                      style="width: 90px">
                      <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-button>
                  <el-button style="padding: 0;  margin-left: 10px;" v-if="checkPermission('NXOperationuserId')">
                    <el-select filterable v-model="filter.userId" collapse-tags clearable placeholder="运营助理"
                      style="width: 90px">
                      <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-button>
                  <el-button style="padding: 0;margin-left: 10px;" >
                    <el-select filterable v-model="filter.prolit3" collapse-tags  placeholder="查询维度"
                      style="width: 90px">
                      <el-option label="负毛三" :value="1" />
                      <el-option label="负净利" :value="2" />
                    </el-select>
                  </el-button>
                  <el-button style="padding: 0;margin-left: 10px;" >
                    <el-select filterable v-model="filter.DateType" collapse-tags  placeholder="统计维度"
                      style="width: 90px">
                      <el-option label="日" :value="1" />
                      <el-option label="周" :value="2" />
                      <el-option label="月" :value="3" />
                      <el-option label="季" :value="4" />
                    </el-select>
                  </el-button>
                  <el-button class="button" style="padding: 0;  margin-left: 10px;">
                    <el-button type="primary" @click="onSearch">查询</el-button>
                  </el-button>

                  <el-button class="button" style="padding: 0;  margin-left: 10px;">
                  <el-button type="info" @click="LossGroupId(11)">小组亏损</el-button>
                  </el-button>

                </el-button-group>
                </div>
                <div class="div2-3" >
                    <div v-show="echartsshow">
                   
                        
                    </div>
                    <a style=" top: 0; right: 150px; color: red; font-size: 15px; font-weight: bolder; position: absolute;" v-if="echartsshow" type="danger" @click="echartsfuc">切换表格</a>
                    <a style=" top: 0; right: 150px; color: red; font-size: 15px; font-weight: bolder; position: absolute;" v-if="!echartsshow" type="danger" @click="echartsfuc" >切换趋势图</a>
                </div>
                <div class="div2-2" v-show="echartsshow">
                  <buschar :thisStyle="{width:'80vw',height: '620px'}"  ref="buschar" :analysisData="charData"></buschar>
                </div>
                <div class="div2-4" style="height: 620px;" v-show="!echartsshow">
                  <!-- <el-button  style=" margin-left: 760px;" type="primary" @click="getEnergyEfficiencyYesterdayTX(1)">日</el-button>
                  <el-button  type="warning" @click="getEnergyEfficiencyYesterdayTX(2)">周</el-button>
                  <el-button  type="success" @click="getEnergyEfficiencyYesterdayTX(3)">月</el-button>
                  <el-button  type="danger" @click="getEnergyEfficiencyYesterdayTX(4)">季</el-button> -->
                    <ces-table v-if="showtable" ref="table" :that="that" :isIndex="true" @sortchange="sortchange"
                        :tableData="pddcontributeinfolist" @select="selectchange" :isSelection="false" :tableCols="tableCols"
                        :isSelectColumn="true"
                        :hasexpand='true'
                        :loading="listLoading" :summaryarry='summaryarry'>


                        <el-table-column width="120px" label="操作" fixed="right">
                          <template slot-scope="scope">
                              <el-select  v-model="scope.row.movetype" @change="moveProductType(scope.row)"  collapse-tags clearable placeholder="请选择操作"
                                    style="width: 100px">
                                    <el-option label="移至关注" :value="11"/>
                                    <el-option label="移至爆款" :value="22"/>
                                    <el-option label="移至任务中心" :value="33" />
                                  </el-select>
                          </template>
                      </el-table-column>

                    </ces-table>

                  </div>
            </div></el-col>

          </el-row>
          <template #footer>
                <my-pagination v-show="!echartsshow"  ref="pager" :total="total" :checked-count="sels.length" @get-page="getEnergyEfficiencyYesterdayTX" />
            </template>

            <!-- <el-dialog title="选择接收人" @close="closediolag"  :visible.sync="dialogChooseRecipientVisible" height="100px" v-dialogDrag>
              <el-row>

              </el-row>
               <el-row>
                  <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="24">
                     <el-select  v-model="dialogfilter.Majordomo"  collapse-tags clearable placeholder="运营总监"
                     style="width: 120px;margin-left: 150px;">
                     <el-option label="汪大侠" :value="lables"/>
                      </el-select>



                     <el-select filterable v-model="dialogfilter.groupId" collapse-tags clearable placeholder="运营组长"
                             style="width: 120px;margin-left: 50px;">
                             <el-option key="无运营组" label="无运营组" :value="0"></el-option>
                             <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.label" />
                       </el-select>



                     <el-select filterable v-model="dialogfilter.operateSpecialUserId" collapse-tags clearable placeholder="运营专员"
                       style="width: 120px;margin-left: 50px;">
                       <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.label" />
                       </el-select>



                     <el-select filterable v-model="dialogfilter.userId" collapse-tags clearable placeholder="运营助理"
                     style="width: 120px;margin-left: 50px;">
                     <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.label" />
                     </el-select>
                  </el-col>

              </el-row>

              <el-button style="margin-left: 800px;" type="primary" @click="Notarize" >确认</el-button>
              <el-button type="primary" @click="dialogclosds" >取消</el-button>

          </el-dialog> -->

          <el-dialog title="选择接收人" @close="closediolag" :visible.sync="dialogChooseRecipientVisible" width="1100px" height="100px" v-dialogDrag>
            <el-form :model="form" ref="form" label-width="120px" label-position="right" > 
             <el-row justify="center">

                <el-col :span="6" >
                    <el-form-item label="接收人：" :rules="[
                    { required: true, message: '请选择接收人', trigger: ['blur', 'change'] }    
                    ]">
                    <el-select filterable v-model="dialogfilter.userId" collapse-tags clearable placeholder="接收人"
                    style="width: 120px;">
                    <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.label" />
                    </el-select> 
                </el-form-item>
                </el-col>
            </el-row>
            <el-row style="margin-top: 10px;">
                <el-col :span="24" >
                    <el-form-item label="资料：" :rules="[
                    { required: true, message: '请填写资料', trigger: ['blur'] }    
                    ]">
                     <yh-quill-editor :value.sync="imageFath" @imgget="imggetfuc"></yh-quill-editor>
                    </el-form-item>
                 </el-col>
            </el-row>

            <div style="display: flex; flex-direction: row; height: auto; width: 100%; margin-top: 20px;">
            <el-button style="margin-left: auto;" type="primary" @click="UpdateSendMessage" >确认</el-button>
            <el-button  type="primary" @click="dialogclosds" >取消</el-button>
           </div>
        </el-form>
        </el-dialog>

    </my-container>
  </template>
  <script>

  import dayjs from "dayjs";
  import { mapGetters } from 'vuex'
  import cesTable from "@/components/Table/table.vue";
  import { formatPlatform, formatTime, formatYesornoBool, formatLinkProCode } from "@/utils/tools";
  import { getLossProductWarningTX_Chart,getLossProductWarningTX,getLossProductWarningTX_Chart_GroupId} from '@/api/bookkeeper/pddstaticsreport'
  import * as echarts from 'echarts'
  import buschar from '@/components/Bus/buscharOpeation'
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  import {getDirectorGroupList,getDirectorList } from '@/api/operatemanage/base/shop'
  import {editProductAttention} from '@/api/operatemanage/base/product'
  import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue';

  export default {
    name: "Users",
    components: {
      MyContainer,
      MyConfirmButton,
      MySearch,
      MySearchWindow,
      cesTable,
      buschar,
      YhQuillEditor
    },
    data() {
      return {
        imageFath:null,
        lables:"汪大侠",
        datatype:null,
        dialogfilter:{
                Majordomo:null,
                groupId:null,
                operateSpecialUserId:null,
                userId:null
            },
            dialogChooseRecipientVisible:false,
        that: this,
        tableCols: this.gettableCols(),
        listLoading: false,
        sels: [], // 列表选中列
        showtable: true,
        grouplist: [],
        pageLoading: false,
        charData:[],
        summaryarry: {},
        total: 0,
        pager: { OrderBy: "", IsAsc: false },
        fileList: [],
        dialogVisible: false,
        uploadLoading: false,
        echartsshow: true,
        directorlist: [],
        avatarDefault: require('@/assets/images/avatar.png'),

        pddcontributeinfolist: [],
        filter: {
        startTime: null,
        endTime: null,
        Platform:1,
        timerange: null,
        // 运营助理id
        userId: null,
        //组id
        groupId: null,
        // 运营专员 ID
        operateSpecialUserId: null,
        prolit3:1,
        DateType:3
       },
       filterMove:{
        ProCode:"",
        movetype:""
       },
       filter1:{
            DateType:null,
            startTime: null,
            endTime: null,
            Platform:null,
            timerange: null,
            // 运营助理id
            userId: null,
            //组id
            groupId: null,
            // 运营专员 ID
            operateSpecialUserId: null,
          },
          pickerOptions: {
                shortcuts: [{
                    text: '近一周',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                    picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近半个月',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                    picker.$emit('pick', [start, end]);
                    }
                },{
                    text: '近一个月',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                    picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近三个月',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                    picker.$emit('pick', [start, end]);
                    }
                }]
            },
      };
    },
    async mounted() {

    },
    async created() {
     await this.init()
     await this.showchart();
     await this.getGroupList();
    // await this.getShopList();
    await this.getGroupList()

  },
  computed:{
    ...mapGetters([
      'menus',
      'userName',
      'avatar'
    ]),




  },

    methods: {
      async UpdateSendMessage(){
             if(this.imageFath==null)
             {
           
                this.$message.error('请输入相关资料！！！！！！');
                return;
             }
             if(this.dialogfilter.userId==null||this.dialogfilter.userId==""||this.dialogfilter.userId==0)
             {
           
                this.$message.error('请选择接收人！！！！！！');
                return;
             }
            this.$confirm('确定要执行此操作吗, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
            }).then(async() => {
                this.Notarizelist.AppointUserName=this.dialogfilter.userId;
                this.Notarizelist.RelevantData=this.imageFath;
             const res = await editProductAttention(this.Notarizelist);
             
            if(res.code==1){
              
                this.$message.success('保存成功！');
                
                this.imageFath = null
                this.dialogfilter.userId=null
                this.dialogChooseRecipientVisible=false
            }else{
                this.imageFath = null
                this.dialogfilter.userId=null
                this.dialogChooseRecipientVisible=false
            }
            }).catch(() => {
            this.$message({
                type: 'info',
                message: '已取消操作'
            });
            });
        },
      LossGroupId(groupdatatype){
        this.filter.groupDataType=groupdatatype;
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
         var params = {
          ... this.filter
        };
         let that = this;


          const res =  getLossProductWarningTX_Chart_GroupId(params).then(res=>{

                that.charData=res.data

                this.$nextTick(() => {
                  this.$refs.buschar.initcharts()
                });
            });


      },
      closediolag(){
		            this.pddcontributeinfolist.map((data)=>{
		                        
		                        data.movetype = ''
		                    
		                })
		        },
            dialogclosds(){
            this.imageFath = null
            this.dialogfilter.userI=null
            this.dialogChooseRecipientVisible=false;

        },
      async Notarize(){
            this.$confirm('确定要添加吗, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(async() => {
                if(this.dialogfilter.operateSpecialUserId!=null)
                    {


                      this.Notarizelist.AppointUserName= this.dialogfilter.operateSpecialUserId

                    }
                    else if(this.dialogfilter.userId!=null)
                    {


                      this.Notarizelist.AppointUserName= this.dialogfilter.userId

                    }
                    else if(this.dialogfilter.groupId!=null)
                    {


                      this.Notarizelist.AppointUserName=this.dialogfilter.groupId
                    }
                    else if(this.dialogfilter.Majordomo!=null)
                    {


                      this.Notarizelist.AppointUserName=this.dialogfilter.Majordomo
                    }
                    else if(this.dialogfilter.Majordomo!="汪大侠"&&this.dialogfilter.userId==null&&this.dialogfilter.operateSpecialUserId==null&&this.dialogfilter.groupId==null)
                    {
                        this.$message.error('请选择接收人！！');
                       return;
                    }


                    if(this.dialogfilter.Majordomo!=null)
		                    {
		                        
		                        this.Notarizelist.AppointUserName=this.dialogfilter.Majordomo
		                    }

                const res = await editProductAttention(this.Notarizelist);
                if(res.code==1){
                    this.$message.success('添加成功！');
                    this.dialogChooseRecipientVisible=false;
                    this.pddcontributeinfolist.map((data)=>{

                            data.movetype = ''

                    })
                    this.dialogfilter.groupId=null
                        this.dialogfilter.userId=null
                        this.dialogfilter.Majordomo=null
                        this.dialogfilter.operateSpecialUserId=null
                    this.getEnergyEfficiencyYesterdayTX();
                }
              }).catch(() => {

                this.pddcontributeinfolist.map((data)=>{

                            data.movetype = ''

                    })
                    this.dialogfilter.groupId=null
                        this.dialogfilter.userId=null
                        this.dialogfilter.Majordomo=null
                        this.dialogfilter.operateSpecialUserId=null
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
              });


    },
    async getGroupList() {

      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });


    },
      async moveProductType(val){
          this.filterMove.ProCode=val.proCode;
          this.filterMove.movetype=val.movetype;
          const params = { ... this.filterMove }

              if(this.filterMove.movetype==33)
                {
                    this.dialogChooseRecipientVisible=true
                    this.Notarizelist=val;
                }

                else
                {

                    const res = await editProductAttention(params);
                  console.log("111",res)
                  if(res.code==1){
                      this.$message.success('添加成功！');
                      this.getEnergyEfficiencyYesterdayTX();
                      this.pddcontributeinfolist.map((data)=>{
                          if(data == val){
                              data.movetype = ''
                          }
                      })
                      this.dialogfilter.groupId=null
                        this.dialogfilter.userId=null
                        this.dialogfilter.Majordomo=null
                        this.dialogfilter.operateSpecialUserId=null

                  }else{
                      this.pddcontributeinfolist.map((data)=>{
                          if(data == val){
                              data.movetype = ''
                          }
                      })
                      this.dialogfilter.groupId=null
                        this.dialogfilter.userId=null
                        this.dialogfilter.Majordomo=null
                        this.dialogfilter.operateSpecialUserId=null
                  }


                }






      },
        echartsfuc(){
            this.echartsshow = !this.echartsshow;
        },
        sortchange(column) {
        if (!column.order) this.pager = {};
        else
          this.pager = {
            OrderBy: column.prop,
            IsAsc: column.order.indexOf("descending") == -1 ? true : false,
          };
        this.onSearch();
      },
        gettableCols() {
        return [
        { istrue: true,  prop: 'yearMonthDay', label: '日期', sortable: 'custom', width: '120' },
        { istrue: true,  prop: 'proCode', label: '商品ID',  width: '120' },
        { istrue: true,  prop: 'goodsName', label: '商品名称', width: '350' ,},
        { istrue: true,  prop: 'saleAmont',  label: '销售金额', width: '120' , sortable: 'custom'},
        { istrue: true,  prop: 'profit4',  label: '净利润', width: '120' , sortable: 'custom'},
        { istrue: true,  prop: 'inventoryCheckFee',  label: '盘点损益', width: '120' , sortable: 'custom'},
        { istrue: true,  prop: 'profit3',  label: '毛三', width: '120' , sortable: 'custom'},
        { istrue: true,  prop: 'alladv',  label: '总广告费', width: '120' , sortable: 'custom'},
        { istrue: true,  prop: 'advratio',  label: '广告费占比', width: '130' , sortable: 'custom'},
        { istrue: true,  prop: 'profit1',  label: '毛一', width: '130' , sortable: 'custom'},
        { istrue: true,  prop: 'profit1Rate',  label: '毛一利率', width: '130' , sortable: 'custom'},
        { istrue: true,  prop: 'deductAmount',  label: '违规总扣款', width: '130' , sortable: 'custom'},


        ]
      },

      async showchart(){
      
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
         var params = {
          ... this.filter
        };
         let that = this;


          const res =  getLossProductWarningTX_Chart(params).then(res=>{

                that.charData=res.data

                this.$nextTick(() => {
                  this.$refs.buschar.initcharts()
                });
            });







      },
      onSearch() {

        this.getEnergyEfficiencyYesterdayTX();
        this.showchart();

      },
      async getEnergyEfficiencyYesterdayTX() {
        this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager();
      const para = { ...this.filter ,...pager, ...this.pager};
        const params = {
          ...para,
        };
        this.listLoading = true;
        const res = await getLossProductWarningTX(params);
        this.listLoading = false;
        this.total = res.data.total;
        this.pddcontributeinfolist = res.data.list;
      },


    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init() {
      var date1 = new Date(); date1.setDate(date1.getDate() - 10);
      var date2 = new Date(); date2.setDate(date2.getDate() - 1);
      this.filter.timerange = [];
      this.filter.timerange[0] = this.datetostr(date1);
      this.filter.timerange[1] = this.datetostr(date2);
    },
    selectchange: function (rows, row) {
        this.selids = [];
        rows.forEach((f) => {
          this.selids.push(f.id);
        });
      },
    }
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }

  /* .div1 {
    margin-top: 0px;
    background: #d7dfeb;
    height: 100px;
  } */
  .div2 {
    background: white;
    height: 65vh;
  }
  /* .div3 {
    margin-top: 0px;
    background: #d7dfeb;
    height: 80px;
  } */
  .div2-1{
    margin-top: 0px;
    background: white;
     /* height: 50px; */
  }
  .div2-2{
    margin-top: 0px;
    /* background: red; */
    /* height: 710px; */
    height: 650px;
    width: 98%;
  }
  .div2-3{
    margin-top: 30px;
    background: white;
    position: relative;
    height: 40px;
  }
  .span1{
   margin-top: 0px;
   margin-left: 100px;
   width: 80px;
   color: red;
  }
  .span2{
   margin-top: 0px;
   margin-left: 100px;
   color: red;
  }
  .span3{
   margin-top: 0px;
   margin-left: 100px;
   color: red;
  }
  .span4{
   margin-top: 0px;
   margin-left: 270px;
   width: 80px;
   color: red;
  }
  .span5{
   margin-top: 0px;
   margin-left: 200px;
   color: red;
  }
  .span6{
   margin-top: 0px;
   margin-left: 200px;
   color: red;
  }
  .span7{
   margin-top: 0px;
   margin-left: 200px;
   color: red;
  }



  </style>
