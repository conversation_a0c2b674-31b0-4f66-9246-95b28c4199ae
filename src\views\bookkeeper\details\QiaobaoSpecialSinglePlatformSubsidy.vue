<template>
    <MyContainer>
        <!-- 查询条件 -->
      <template #header>
        <div class="top">
          <el-button style="padding: 0;border: none;">
            <el-date-picker style="width: 120px" v-model="ListInfo.yearMonth" type="month" format="yyyyMM"
              value-format="yyyyMM" clearable>
            </el-date-picker>
          </el-button>
          <el-button style="padding: 0;border: none;">
            <el-input v-model.trim="ListInfo.orderNo" placeholder="订单编号" maxlength="50" clearable class="publicCss" />
          </el-button>
          <el-button style="padding: 0;border: none;">
            <el-select filterable clearable v-model="ListInfo.shopCodes" placeholder="店铺" style="width: 160px" multiple collapse-tags>
              <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"></el-option>
            </el-select>
          </el-button>
          <el-button style="padding: 0;border: none;">
            <el-input v-model.trim="ListInfo.ProCode" placeholder="商品ID" maxlength="50" clearable class="publicCss" />
          </el-button>
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" @click="onTable">配置</el-button>
          <el-button type="primary" @click="startImport">导入</el-button>
          <el-button type="primary" @click="exportProps" :loading="isExport">导出</el-button>
          <el-button type="primary" @click="onCount" :loading="isCount">计算</el-button>
        </div>
      </template>
      <!-- table表单 -->
      <vxetablebase :id="'QiaobaoSpecialSinglePlatformSubsidy202507131001'" :tablekey="'OrderManagementDY202507131110'"
        ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
        :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
        :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
        :height="'100%'">
      </vxetablebase>
      <!-- 页签 -->
      <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template>
  
      <el-dialog title="配置" :visible.sync="isConfig" width="50%" v-dialogDrag :close-on-click-modal="false">
        <template>
            <div style="text-align: center;margin-bottom: 20px;">
                <el-select filterable clearable v-model="configListInfo.shopCode" placeholder="店铺" style="width: 280px;">
                    <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"></el-option>
                </el-select>
                <el-button type="primary" @click="onAddShop">添加</el-button>
                <el-button type="primary" @click="onQueryShop">查询</el-button>
                <el-button type="primary" @click="onImportShop">导入</el-button>
            </div>
        </template>
        <vxetablebase :id="'QiaobaoSpecialSinglePlatformSubsidy202507121002'" :tablekey="'QiaobaoSpecialSinglePlatformSubsidy202507121021'"
            ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange2'
            :tableData='tableData2' :tableCols='tableCols2' :isSelection="false" :isSelectColumn="false"
            style="width: 100%;  margin: 0" :loading="loading2"
            :height="'300'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager2" :total="total2" @page-change="Pagechange2" @size-change="Sizechange2" />
        </template>
  
        <span slot="footer" class="dialog-footer">
            <el-button @click="isConfig = false">关闭</el-button>
        </span>
      </el-dialog>
      <el-dialog title="编辑" :visible.sync="editVisible" width="25%" v-dialogDrag>
            <el-form :model="editform" ref="editform" :rules="editrules" label-width="100px" class="demo-ruleForm">
                <el-form-item label="店铺" prop="shopCode">
                    <el-select filterable clearable v-model="editform.shopCode" placeholder="店铺" style="width: 280px;">
                        <el-option v-for="item in dyShopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" :disabled="item.disable"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="editVisible = false">取 消</el-button>
                <el-button type="primary" @click="onEditShop">确 定</el-button>
            </span>
        </el-dialog>
      
      <el-dialog title="导入数据" :visible.sync="dialogVisible" width="35%" v-dialogDrag :close-on-click-modal="false">
        <div class="upload-section">
            <div class="upload-row">
            <el-upload ref="upload" class="upload-area" :auto-upload="false" :multiple="false" :limit="1" action
                accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <el-button size="small" type="success" :loading="uploadLoading" @click="onSubmitUpload" class="upload-btn">
                {{ uploadLoading ? '上传中' : '上传' }}
                </el-button>
            </el-upload>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </el-dialog>
      <el-dialog title="导入店铺" :visible.sync="dialogVisible2" width="30%" v-dialogDrag :close-on-click-modal="false">
        <div class="upload-section">
            <div class="upload-row">
            <el-upload ref="upload2" class="upload-area" :auto-upload="false" :multiple="false" :limit="1" action
                accept=".xlsx" :file-list="fileList2" :data="fileparm2" :http-request="onUploadFile2"
                :on-success="onUploadSuccess2" :on-change="onUploadChange2" :on-remove="onUploadRemove2">
                <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <el-button size="small" type="success" :loading="uploadLoading2" @click="onSubmitUpload2" class="upload-btn">
                {{ uploadLoading2 ? '上传中' : '上传' }}
                </el-button>
            </el-upload>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible2 = false">关闭</el-button>
        </span>
      </el-dialog>
    </MyContainer>
</template>
<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getQiaobaoSpecialSinglePlatformSubsidyShop, addQiaobaoSpecialSinglePlatformSubsidyShop, deleteQiaobaoSpecialSinglePlatformSubsidyShop, 
  importQiaobaoSpecialSinglePlatformSubsidyShop, getQiaobaoSpecialSinglePlatformSubsidy, exportQiaobaoSpecialSinglePlatformSubsidy, 
  importQiaobaoSpecialSinglePlatformSubsidy, GetDyShopList
 } from '@/api/bookkeeper/reportdayV2'
import { qiaobaoSpecialSinglePlatformSubsidyCalcTask } from "@/api/monthbookkeeper/financialDetail"
import dayjs from 'dayjs'
import { formatLinkProCode } from "@/utils/tools";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';

const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'yearMonth', label: '年月'},
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '订单编号'},
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'principal', label: '本金' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopCode', label: '店铺编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'proCode', label: '商品ID', type: 'html', formatter: (row) => formatLinkProCode(6, row.proCode)},
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'actualReceivedAmount', label: '实际收款金额'},
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'platformSubsidy', label: '平台补贴', },
  ]	
  
const tableCols2 = [
    { sortable: 'custom', width: '120', align: 'center', prop: 'shopCode', label: '店铺编码' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺' },
    {
        istrue: true, type: "button", label: '操作', width: "120",
        btnList: [
            { label: "编辑", handle: (that, row) => that.onEditConfig(row) },
            { label: "删除", handle: (that, row) => that.deleteShopConfig(row) }
        ]
    }
]
    

export default {
    name: "QiaobaoSpecialSinglePlatformSubsidy",
    components: {
      MyContainer, vxetablebase
    },
    data() {
      return {
        isConfig: false,//是否显示配置界面
        configListInfo: {
            shopCode: '',//配置店铺
            currentPage: 1,
            pageSize: 50,
            orderBy: null,
            isAsc: false
        },
        loading2: false,
        total2: 0,
        editform: {
            id: '',
            batchNumber: '',
            shopCode: ''
        },
        editrules: {
            shopCode: [{ required: true, message: '请选择店铺', trigger: 'blur' }],
        },
        editVisible: false,
        fileList2: [],
        uploadLoading2: false,//上传按钮loading
        fileparm2: {},//上传文件参数

        dialogVisible: false,//导入弹窗
        dialogVisible2: false,//导入弹窗
        fileList: [],//上传文件列表
        uploadLoading: false,//上传按钮loading
        fileparm: {},//上传文件参数
        that: this,
        ListInfo: {
          currentPage: 1,
          pageSize: 50,
          orderBy: 'createdTime',
          isAsc: false,
          yearMonth:dayjs().subtract(1, 'month').format('YYYYMM'),//月份
          orderNo:'',//订单编号
          ProCode:'',//商品ID
          shopCodes: [],//店铺编码
        },
        shopList: [],
        dyShopList: [],
        tableCols,
        tableCols2,
        tableData: [],
        tableData2: [],
        summaryarry: {},
        total: 0,
        loading: false,
        isExport:false,
        isCount: false
      }
    },
    async mounted() {
      this.shopList = (await getAllShopList({ platforms: [6] })).data;
      this.getList('search')
    },
    methods: {
      //打开配置
      onTable(){
        this.configListInfo.shopCode = '';
        this.isConfig = true;
        this.onQueryShop();
      },
      //查询配置店铺
      async onQueryShop(){
        this.loading2 = true
        const params = {
            ...this.configListInfo
        }
        const { data, success } = await getQiaobaoSpecialSinglePlatformSubsidyShop(params)
        this.loading2 = false;
        if(success){
            this.tableData2 = data.list
            this.total2 = data.total
        }else{
            this.$message.error('获取列表失败')
        }
      },
      //添加配置店铺
      async onAddShop(row){
        if(this.configListInfo.shopCode == undefined || this.configListInfo.shopCode == ''){
            this.$message.error('请先选择店铺！')
            return;
        }
        const params = {
            shopCode: this.configListInfo.shopCode
        }
        const res = await addQiaobaoSpecialSinglePlatformSubsidyShop(params)
        if(res.success){
            this.configListInfo.shopCode = null;
            this.$message.success(res.data)
            this.onQueryShop()
        }
      },
      //编辑配置店铺
      async onEditConfig(row){
        this.dyShopList = await GetDyShopList({shopCode: row.shopCode})
        this.editform = {
          id : row.id,
          batchNumber : row.batchNumber,
          shopCode : row.shopCode
        }
        this.editVisible = true
      },
      //提交配置店铺修改
      async onEditShop(){
        const res = await addQiaobaoSpecialSinglePlatformSubsidyShop(this.editform)
        if(res.success){
            this.editform = { id : '', batchNumber: '', shopCode: '' };
            this.$message.success(res.data)
        }
        this.editVisible = false
        this.onQueryShop()
      },
      //删除配置店铺
      async deleteShopConfig(row){
        this.$confirm('此操作将删除此条数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        const params = { ...row, deleteType: 1 }
        deleteQiaobaoSpecialSinglePlatformSubsidyShop(params).then(res => {
          if (res.success) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.onQueryShop()
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      })
      },
      onImportShop(){
        this.fileList2 = []
        this.dialogVisible2 = true
      },
      //上传文件
      onUploadRemove2(file, fileList) {
        this.fileList2 = []
      },
      async onUploadChange2(file, fileList) {
        this.fileList2 = fileList;
      },
      onUploadSuccess2(response, file, fileList) {
        fileList.splice(fileList.indexOf(file), 1);
        this.fileList2 = [];
        this.dialogVisible2 = false;
      },
      onSubmitUpload2() {
        if (this.fileList2.length == 0) {
            this.$message({ message: "请先上传文件", type: "warning" });
            return false;
        }
        this.$refs.upload2.submit();
      },
      async onUploadFile2(item) {
        if (!item || !item.file || !item.file.size) {
            this.$message({ message: "请先上传文件", type: "warning" });
            return false;
        }
        this.uploadLoading2 = true
        const form = new FormData();
        form.append("upfile", item.file);
        var res = await importQiaobaoSpecialSinglePlatformSubsidyShop(form);
        if (res?.success)
            this.$message({ message: res.message || "上传成功,正在导入中...", type: "success" });
        this.uploadLoading2 = false
        this.dialogVisible2 = false;
        await this.onQueryShop()
      },
      //查询
      async getList(type) {
        if (type == 'search') {
          this.ListInfo.currentPage = 1
          this.$refs.pager.setPage(1)
        }
        this.loading = true
        const params = { ...this.ListInfo }
        const res = await getQiaobaoSpecialSinglePlatformSubsidy(params)
        if (res.success) {
          this.tableData = res.data.list
          this.total = res.data.total
          this.summaryarry = res.data.summary
          this.loading = false
        } else {
          this.$message.error('获取列表失败')
        }
      },
      //导出
      async exportProps() {
        let pager = this.$refs.pager.getPager();
        const params = { ...pager, ...this.pager, ...this.ListInfo };
        this.isExport = true
        let res = await exportQiaobaoSpecialSinglePlatformSubsidy(params);
        this.isExport = false
        if (!res?.data) {
            this.$message({ message: "没有数据", type: "warning" });
            return
        }
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '订单管理_' + new Date().toLocaleString() + '_.xlsx')
        aLink.click()
      },
      //上传文件
      onUploadRemove(file, fileList) {
        this.fileList = []
      },
      async onUploadChange(file, fileList) {
        this.fileList = fileList;
      },
      onUploadSuccess(response, file, fileList) {
        fileList.splice(fileList.indexOf(file), 1);
        this.fileList = [];
        this.dialogVisible = false;
      },
      onSubmitUpload() {
        if (!this.yearMonthDay) {
            this.$message({ message: "请选择日期", type: "warning" });
            return false;
        }
        if (this.fileList.length == 0) {
            this.$message({ message: "请先上传文件", type: "warning" });
            return false;
        }
        this.$refs.upload.submit();
      },
      //导入弹窗
      startImport() {
        this.fileList = []
        this.yearMonthDay = dayjs().subtract(1, 'month').format('YYYYMM')//日期
        this.dialogVisible = true;
      },
      async onUploadFile(item) {
        if (!item || !item.file || !item.file.size) {
            this.$message({ message: "请先上传文件", type: "warning" });
            return false;
        }
        this.uploadLoading = true
        const form = new FormData();
        form.append("upfile", item.file);
        var res = await importQiaobaoSpecialSinglePlatformSubsidy(form);
        if (res?.success)
            this.$message({ message: res.message || "上传成功,正在导入中...", type: "success" });
        this.uploadLoading = false
        this.dialogVisible = false;
        await this.getList()
      },
      //计算
      async onCount(){
        this.isCount = true
        const res = await qiaobaoSpecialSinglePlatformSubsidyCalcTask(this.ListInfo)
        if(res.success){
          this.$message({
            type: 'success',
            message: '计算成功!'
          })
          this.getList()
        }
        this.isCount = false
      },
      //每页数量改变
      Sizechange(val) {
        this.ListInfo.currentPage = 1;
        this.ListInfo.pageSize = val;
        this.getList()
      },
      //当前页改变
      Pagechange(val) {
        this.ListInfo.currentPage = val;
        this.getList()
      },
      //每页数量改变
      Sizechange2(val) {
        this.configListInfo.currentPage = 1;
        this.configListInfo.pageSize = val;
        this.onQueryShop()
      },
      //当前页改变
      Pagechange2(val) {
        this.configListInfo.currentPage = val;
        this.onQueryShop()
      },
      //排序
      sortchange({ order, prop }) {
        if (prop) {
          this.ListInfo.orderBy = prop
          this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
      sortchange2({ order, prop }) {
        if (prop) {
          this.configListInfo.orderBy = prop
          this.configListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.onQueryShop()
        }
      },
    }
  }												
</script>
<style scoped lang="scss">
.top {
display: flex;
margin-bottom: 10px;

.publicCss {
    width: 150px;
    margin-right: 5px;
}
}
.upload-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 12px 0;
}

.upload-row {
    display: flex;
    align-items: center;
    gap: 12px;
}

.required-label {
    font-weight: 500;
    color: #333;
}

.required-mark {
    color: red;
    margin-right: 4px;
}

.upload-month {
    width: 200px;
}

.upload-area {
    display: flex;
    align-items: center;
    gap: 10px;
}

.upload-btn {
    margin-left: 0;
}
::v-deep .el-select__tags-text {
  max-width: 30px;
}
:deep(.vxe-table--render-default:not(.is--empty).is--footer.is--scroll-x .vxe-table--body-wrapper) {
	overflow-x: hidden !important;
}
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>