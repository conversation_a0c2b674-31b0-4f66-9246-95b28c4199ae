<template>
    <!-- 责任申诉审核 -->
    <div class="containal">
        <div class="top">
            <div>审核须知:</div>
            <div>1、申诉基础步骤：发起申诉-->提交申诉凭证-->审核凭证-->判定申诉结果-->申诉通过后调整新责任部门，原部门责任及相关事宜剔除。</div>
            <div>2、申诉机会仅一次，请您使用好申诉权益。</div>
            <div>3、申诉时间为责任计算时间(非扣款时间)起当天17:30至次日9:00,超时导致申诉入口关闭,无法支持再次申诉。</div>
        </div>
        <div class="center">
            <!-- 加上插值 -->
            <div class="center_item">
                <div class="item_box" style="width: 240px;">线上订单号: {{ auditDialogProps.otherInfo.b.orderNo }}</div>
                <div class="item_box">发起时间: {{ auditDialogProps.otherInfo.b.afterSaleApproveDate }}</div>
                <div class="item_box">损耗金额: {{ auditDialogProps.otherInfo.b.damagedAmount }}</div>
            </div>
            <div class="center_item">
                <div class="item_box">原责任部门: {{ auditDialogProps.orgZrDepartment }}</div>
                <div class="item_box">原责任类型: {{ auditDialogProps.orgZrType2 }}</div>
                <div class="item_box">原责任人: {{ auditDialogProps.orgZrUserName }}</div>
            </div>
            <div class="center_item">
                <div class="item_box">新责任部门: {{ auditDialogProps.newZrDepartment }}</div>
                <div class="item_box">新责任类型: {{ auditDialogProps.newZrType2 }}</div>
                <div class="item_box">新责任人: {{ auditDialogProps.newZrUserName }}</div>
            </div>
            <div class="center_item">申诉理由:{{ auditDialogProps.applyReason }}</div>
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px">
                <el-form-item label="申诉资料" prop="auditDialogProps.applyContent" >
                 <div v-html="auditDialogProps.applyContent" class="tempdivv"> </div>
                </el-form-item>
                <el-form-item label="审核意见" prop="auditDialogProps.auditRemark">
                    <el-input type="textarea" maxlength="200" minlength="1" placeholder="请输入"
                        v-model="auditRemark" show-word-limit :rows="4">
                    </el-input>
                </el-form-item>
            </el-form>

        </div>
        <div class="footer">
            <el-button size="medium" @click="handleClose">关闭</el-button>
            <el-button type="primary" size="medium" @click="auditFun(1)">审核通过</el-button>
            <el-button type="warning" size="medium" @click="auditFun(0)">审核驳回</el-button>
        </div>
    </div>
</template>

<script>
import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'
import { auditDeductZrAppeal } from '@/api/customerservice/DamagedOrders'
export default {
    name: 'auditDialog',
    props: {
        //接收父组件传递的值
        handleClose: {
            type: Function,
            required: true
        },
        auditDialogProps: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            textarea: '',
            ruleForm: null,
            rules: {
                desc: [
                    { required: true, message: '请选择活动资源', trigger: 'change,blur,input' }
                ]
            },
            auditRemark: null
        }

    },
    mounted() {
        this.auditRemark = null
        console.log(this.auditDialogProps)
    },
    methods: {
        async auditFun(val) {
            if(!this.auditRemark){
                this.$message({
                    message: '请输入审核意见',
                    type: 'warning'
                })
                return
            }
            let params = {
                id: this.auditDialogProps.id,
                auditState: val,
                auditRemark: this.auditRemark,
                newZrType: this.auditDialogProps.newZrType,
                newZrType2: this.auditDialogProps.newZrType2,
                newZrUserName: this.auditDialogProps.newZrUserName,
                newZrUserId: this.auditDialogProps.newZrUserId,
                newZrDDUserId: this.auditDialogProps.newZrDDUserId,
                newZrDepartment: this.auditDialogProps.newZrDepartment,
                newMemberId: this.auditDialogProps.newMemberId,
                newMemberName: this.auditDialogProps.newMemberName,
                newMemberDDUserId: this.auditDialogProps.newMemberDDUserId
            }
            const { success } = await auditDeductZrAppeal(params)
            if (success) {
                if ((val == 1)) {
                    this.$message({
                        message: '审核成功',
                        type: 'success'
                    })
                } else {
                    this.$message({
                        message: '驳回成功',
                        type: 'success'
                    })
                }
                this.handleClose()
                this.$emit('getList')
            }
        }
    }
}
</script>

<style scoped lang="scss">
.containal {
    padding: 0 20px;

    .top {
        color: red;
        margin-bottom: 30px;
    }

    .center {
        .center_item {
            padding-left: 33px;
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;

            .item_box {
                width: 220px;
            }
        }
    }

    .footer {
        display: flex;
        justify-content: flex-end;
    }
}

.tempdivv ::v-deep img{ max-width: 980px}
</style>
