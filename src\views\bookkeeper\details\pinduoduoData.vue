<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="销售后台明细" name="tab1" style="height: 100%;">
                <saleDetailpdd :filter="filter" ref="saleDetailpdd" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="结算数据" name="tab2" style="height: 100%;">
                <settlementDetailPdd :filter="filter" ref="settlementDetailPdd" style="height: 100%;" />
            </el-tab-pane>
        </el-tabs>
    </my-container>

</template>
<script>
    import MyContainer from "@/components/my-container";
    import saleDetailpdd from '@/views/bookkeeper/details/saleDetailpdd.vue'
    import settlementDetailPdd from '@/views/bookkeeper/details/settlementDetailPdd.vue'

    export default {
        name: "Users",
        components: { MyContainer, saleDetailpdd, settlementDetailPdd },
        data() {
            return {
                that: this,
                pageLoading: '',
                filter: {
                },
                shopList: [],
                userList: [],
                groupList: [],
                selids: [],
                dialogVisibleSyj: false,
                fileList: [],
                activeName: 'tab1'
            };
        },
        mounted() {

        },
        methods: {


        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
