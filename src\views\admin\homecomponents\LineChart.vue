<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
import { interceptor } from 'vxe-table';
//require('echarts/theme/macarons') // echarts theme
//import resize from './mixins/resize'

export default {
  //mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '98%'
    },
    gridbottom: {
      type: Number,
      default: 90
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Object,
      required: true
    },
    smooth: {
      type: Boolean,
      required: false
    },
    areaStyle: {
      type: Object,
      default:null,
    }
  },
  data() {
    return {
      chart: null,
      colors:['#432EB7','#CB1E21','#F43636','#A942F8','#AACD86','#5a8ebe','#BBB26E','#F46236','#d37bc8','#665F89', '#40A3FF','#D94E75','#404EF2','#A574B9','#299ACB','#96D81B','#A6BB47','#92814C','#C87F93','#3CB58E', '#CF844E','#4A9977','#F24087','#F240E0','#3DD6CE','#C6A41E','#698448','#8993D3','#7B849B','#924CAA', '#7344E0','#403B71','#F4B836','#CA00A5','#00B581','#5C6949','#557BDA','#8C7878','#DD6F70','#AB4F51', '#843382','#D36F38','#8E6A36','#D6411C','#00B1B1','#FD6A6A','#BE7831','#86AEA2','#429BEF','#19D200']
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {   
        this.setOptions(val)
      }
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.initChart()
    window.addEventListener('resize', this.handleResizeChart);
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  destroyed () {
    window.removeEventListener('resize', this.handleResizeChart());
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions(element) {
        var series = []
        if ( !element || element?.legend?.length <=1 ) return;
        element.series?.forEach(s=>{
          series.push({
            smooth: this.smooth,
            // areaStyle: this.areaStyle,
            stack: 'Total',
            emphasis: {
              focus: 'series'
            },
            itemStyle: {
            normal: {
              lineStyle: {
                width: 2
              },
            }
          },...s})
        })
        const color = element ? element?.legend?.map((item, i) => this.colors[i]) : []
        var yAxis=[]
        element.yAxis?.forEach(s=>{
        yAxis.push({axisLine:{show:true},type: 'value',minInterval:10,offset:s.offset,splitLine:s.splitLine,position:s.position,name: s.name,axisLabel: {formatter: '{value}'+s.unit}})
        }) 
        var selectedLegend={};//{};
        if(element.selectedLegend){
        element.legend.forEach(f=>{
            //if(!element.selectedLegend.includes(f)) selectedLegend["'"+f+"'"]=false
            if(!element.selectedLegend.includes(f)) selectedLegend[f]=false
            })
        } 
      this.chart.setOption({
        toolbox: {feature: {
            magicType: {show: true, type: ['line', 'bar']},
        }},
        xAxis: {
          type: 'category',
          data: element.xAxis,
          boundaryGap: false,
          axisPointer: {
              type: 'shadow'
          }
        },
        grid: {
          left: '1%',
          right: 15,
          bottom: this.gridbottom,
          top: '8%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        color,
        legend: {
          data: element.legend,
          selected: selectedLegend,
          bottom: 10,
        },
        yAxis:  yAxis,
        series: series
      })
    },
    handleResizeChart () {
      if (this.chart) {
        this.chart.resize();
      }
    }
  }
}
</script>
