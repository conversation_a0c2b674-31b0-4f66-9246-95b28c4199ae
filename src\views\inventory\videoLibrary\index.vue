<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select v-model="ListInfo.platform" placeholder="平台" class="publicCss"
                    @change="changePlatFrom($event, isIndex)" clearable>
                    <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.catalogId1" placeholder="一级类目" class="publicCss"
                    @change="changeCatalog($event, 'catalogId1', isIndex)" clearable filterable>
                    <el-option v-for="item in indexCatalogId1List" :key="item.id" :label="item.label" :value="item.id" />
                </el-select>
                <el-select v-model="ListInfo.catalogId2" placeholder="二级类目" class="publicCss"
                    @change="changeCatalog($event, 'catalogId2', isIndex)" clearable filterable>
                    <el-option v-for="item in indexCatalogId2List" :key="item.id" :label="item.label" :value="item.id" />
                </el-select>
                <el-select v-model="ListInfo.catalogId3" placeholder="三级类目" class="publicCss"
                    @change="changeCatalog($event, 'catalogId3', isIndex)" clearable filterable>
                    <el-option v-for="item in indexCatalogId3List" :key="item.id" :label="item.label" :value="item.id" />
                </el-select>
                <el-input v-model="ListInfo.uploaderName" placeholder="上传人" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.tag" placeholder="视频标签" maxlength="50" clearable class="publicCss" />
                <el-button type="primary" @click="getList(true)">查询</el-button>
                <el-button type="primary" @click="addVideo">新增视频</el-button>
                <el-button type="primary" @click="uploadList">下载/删除记录</el-button>
                <el-button type="primary" @click="downLoadVideo">批量下载</el-button>
                <el-button type="primary" @click="delProps">批量删除</el-button>
                <el-button type="primary" @click="downYhServeice">下载昀晗服务</el-button>
            </div>
        
        </template>
        <vxetablebase :id="'videoLibrary_index202408041631'" ref="table" @select="checkboxRangeEnd" :that='that' :isIndex='true' :hasexpand='true'
                :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
                :isSelection="false" :isSelectColumn="false" v-loading="loading"
                style="width: 100%;  margin: 0" height="100%"/>
                <template #footer>
                    <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
                </template>

            <el-dialog title="下载/删除记录" :visible.sync="uploadListVisable" width="70%" v-dialogDrag>
                <el-tabs v-model="activeName" @tab-click="tabClick">
                    <el-tab-pane label="下载记录" name="first">
                        <downList ref="downList" />
                    </el-tab-pane>
                    <el-tab-pane label="删除记录" name="second">
                        <deleteList ref="deleteList" />
                    </el-tab-pane>
                </el-tabs>
            </el-dialog>

            <el-drawer :title="isEdit ? '编辑视频' : '新增视频'" :visible.sync="drawer" direction="rtl" @close="closeDrawer(true)"
                v-loading="drawerLoding">
                <el-form :model="addVideoInfo" status-icon :rules="rules" ref="ruleForm" label-width="100px"
                    class="demo-ruleForm">
                    <el-form-item label="商品编码:" prop="goodsCode">
                        <el-input v-model="addVideoInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable
                            class="publicCss" @blur="searchGoodsName(addVideoInfo.goodsCode)" />
                    </el-form-item>
                    <el-form-item label="商品名称:" prop="goodsName">
                        <el-input v-model="addVideoInfo.goodsName" placeholder="商品名称" class="publicCss" disabled />
                    </el-form-item>
                    <el-form-item label="平台:" prop="platform">
                        <el-select v-model="addVideoInfo.platform" placeholder="平台" class="publicCss"
                            @change="changePlatFrom($event, isIndex)" clearable>
                            <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="一级类目:" prop="catalogId1">
                        <el-select v-model="addVideoInfo.catalogId1" placeholder="一级类目" style="width: 100%;"
                            @change="changeCatalog($event, 'catalogId1', isIndex)" filterable clearable>
                            <el-option v-for="item in catalogId1List" :key="item.id" :label="item.label" :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="二级类目:" prop="catalogId2">
                        <el-select v-model="addVideoInfo.catalogId2" placeholder="二级类目" style="width: 100%;"
                            @change="changeCatalog($event, 'catalogId2', isIndex)" filterable clearable>
                            <el-option v-for="item in catalogId2List" :key="item.id" :label="item.label" :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="三级类目:" prop="catalogId3">
                        <el-select v-model="addVideoInfo.catalogId3" placeholder="三级类目" style="width: 100%;"
                            @change="changeCatalog($event, 'catalogId3', isIndex)" filterable clearable>
                            <el-option v-for="item in catalogId3List" :key="item.id" :label="item.label" :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="视频标签:" prop="age">
                        <div class="videoTags">
                            <el-tag :key="tag" v-for="tag in dynamicTags" closable :disable-transitions="false"
                                @close="handleClose(tag)" class="tag">
                                {{ tag }}
                            </el-tag>
                            <el-input class="input-new-tag" v-if="inputVisible" v-model.trim="inputValue" ref="saveTagInput"
                                size="small" @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm"
                                :maxlength="50" clearable>
                            </el-input>
                            <el-button v-else class="button-new-tag" size="small" @click="showInput">+ 视频标签</el-button>
                        </div>
                    </el-form-item>
                    <el-form-item label="" prop="age">
                        <el-upload class="upload-demo" ref="upload" :show-file-list="false" :limit="1" accept=".mp4"
                            :on-change="uploadFinish">
                            <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
                            <div slot="tip" style="color:red;">上传视频前请先下载昀晗服务并安装,只能上传一个文件,后续上传的文件会替换上一个文件,文件名不变!</div>
                        </el-upload>
                    </el-form-item>
                    <el-form-item label="视频名称:" v-show="raw || addVideoInfo.title" style="margin-top: 10px;" prop="title">
                        <div>
                            <el-input v-model="addVideoInfo.title" placeholder="视频名称" maxlength="100" clearable />
                        </div>
                    </el-form-item>
                    <el-form-item>
                        <div class="btnGroup">
                            <el-button @click="drawer = false">取消</el-button>
                            <el-button type="primary" @click="submitForm('ruleForm')" v-throttle="1000">提交</el-button>
                        </div>
                    </el-form-item>
                </el-form>
            </el-drawer>

            <el-dialog title="视频播放" :visible.sync="dialogVisible" width="50%" @close="closeVideoPlyer"
                :append-to-body="true" v-dialogDrag>
                <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
                <span slot="footer" class="dialog-footer">
                    <el-button @click="closeVideoPlyer">关闭</el-button>
                </span>
            </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { platformlist, formatPlatform } from '@/utils/tools'
import { replaceSpace } from '@/utils/getCols'
import { pageGetVideoMtl, createVideoMtl, deleteVideoMaterial, createVideoMtlRec, updateStatus, updateVideoMtl } from '@/api/operatemanage/VideoMaterial/VideoMaterial'
import viodeUpload from "@/views/media/shooting/uploadfile.vue";
import { getUserPlatform, } from '@/api/inventory/companyGoods'
import { getCategoryLists } from '@/api/operatemanage/base/category'
import dayjs from 'dayjs'
import { getMyGoodsByCode, } from '@/api/order/tailorloss';
import downList from './components/downList.vue'
import deleteList from './components/deleteList.vue'
import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
import { uploadYYFileVideoBlockAsync, xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
const uploadStatusList = [
    { label: '上传失败', value: -1 },
    { label: '待上传', value: 0 },
    { label: '上传中', value: 1 },
    { label: '上传成功', value: 2 },
]
const tableCols = [
    { istrue: true, label: '', type: "checkbox" },
    { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'goodsName', label: '商品名称', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'catalogId1Name', label: '一级类目', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'catalogId2Name', label: '二级类目', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'catalogId3Name', label: '三级类目', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'tags', label: '视频标签', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'platform', label: '所属平台', sortable: 'custom', width: 'auto', formatter: (row) => formatPlatform(row.platform) },
    { istrue: true, prop: 'dptName', label: '小组', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'uploaderName', label: '上传人', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'uploadTime', label: '上传时间', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'title', label: '视频名称', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'videoExt', label: '视频格式', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'uploadStatus', label: '上传状态', sortable: 'custom', width: 'auto', formatter: (row) => uploadStatusList.find(item => item.value == row.uploadStatus).label },
    { istrue: true, prop: 'videoSize', label: '视频大小', sortable: 'custom', width: 'auto', formatter: (row) => (row.videoSize / 1024 / 1024).toFixed(2) + 'M' },
    { istrue: true, prop: 'videoUrl', label: '视频查看', width: 'auto', type: 'click', handle: (that, row) => that.openVideo(row) },
    {
        istrue: true, label: '操作', width: '150', type: 'button', btnList:
            [
                { istrue: true, label: '下载', handle: (that, row) => that.downLoad(row) },
                { istrue: true, label: '编辑', handle: (that, row) => that.editAddInfo(row) },
                { istrue: true, label: '删除', handle: (that, row) => that.delVideo(row) },
            ]
    },
]

const uploadRecordList = [
    { istrue: true, prop: 'createdTime', label: '状态', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'createdTime', label: '部门', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'createdTime', label: '上传人', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'createdTime', label: '视频名称', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'createdTime', label: '上传时间', sortable: 'custom', width: 'auto' },
]

export default {
    components: { MyContainer, vxetablebase, viodeUpload, downList, deleteList, videoplayer },
    data() {
        return {
            that: this,
            tableCols,
            platformlist,
            uploadStatusList,
            total: 0,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                platform: null,
                uploader: null,
                uploaderName: null,
                goodsCode: null,
                catalogId1: null,
                catalogId2: null,
                catalogId3: null,
                tag: null,
            },
            uploadListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
            downListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
            addVideoInfo: {
                platform: null,
                uploader: null,
                goodsCode: null,
                goodsName: null,
                tags: null,
                catalogId1: null,
                catalogId1Name: null,
                catalogId2: null,
                catalogId2Name: null,
                catalogId3: null,
                catalogId3Name: null,
                videoExt: 'mp4',
                videoSize: null,
                title: null,
                fileId: null,
                id: null,
            },
            file: null,
            activeName: 'first',
            tableData: [],
            uploadRecordTableData: [],
            loading: false,
            uploadRecordList,
            uploadListVisable: false,
            drawer: false,
            picFileList: [],
            rules: {
                goodsCode: [
                    { required: true, message: '请输入商品编码', trigger: 'blur,change' },
                ],
                goodsName: [
                    { required: true, message: '请输入商品名称', trigger: 'blur,change' },
                ],
                platform: [
                    { required: true, message: '请选择平台', trigger: 'blur,change' },
                ],
            },
            platform: null,
            catalogList: [],
            indexCatalogId1List: [],
            indexCatalogId2List: [],
            indexCatalogId3List: [],
            catalogId1List: [],
            catalogId2List: [],
            catalogId3List: [],
            dynamicTags: [],
            inputVisible: false,
            inputValue: '',
            socket: null,
            raw: null,
            delIds: [],
            videoList: [],
            videoplayerReload: false,
            dialogVisible: false,
            videoUrl: null,
            scoketStatus: true,
            isEdit: false,
            atfterUplaodData: null,
            drawerLoding: false,
            isIndex: true,
        }
    },
    async mounted() {
        this.getList()
        this.socket = await this.connectionSocket();
        this.socket.addEventListener("message", function (event) {
            if (event.data.indexOf('{') == 0) {
                var json = JSON.parse(event.data);
                if (event.data.indexOf("machineKey") > -1) {
                    machineKey = json.machineKey;
                }
                if (json.action == "Error") {
                    this.$message.error(json.message)
                }
            }
        });
    },
    //监听dawrer
    watch: {
        drawer(val) {
            this.isIndex = !val
        }
    },
    methods: {
        downYhServeice() {
            window.open("/YhService/YH-Server.zip", "_self");
        },
        clearRaw() {
            this.$refs.upload.clearFiles()
        },
        async editAddInfo(row) {
            this.isEdit = true
            this.raw = null
            const { data } = await getCategoryLists({ platform: row.platform })
            this.catalogList = data.data
            this.catalogId1List = data.data.filter(item => item.parentId == "0")
            this.catalogId2List = this.catalogList.filter(item => item.parentId == row.catalogId1)
            this.catalogId3List = this.catalogList.filter(item => item.parentId == row.catalogId2)
            this.catalogList = data.data
            this.dynamicTags = row.tags ? row.tags.split(',') : []
            this.addVideoInfo = {
                platform: row.platform,
                uploader: null,
                goodsCode: row.goodsCode,
                goodsName: row.goodsName,
                tags: row.tags,
                catalogId1: row.catalogId1,
                catalogId1Name: row.catalogId1Name,
                catalogId2: row.catalogId2,
                catalogId2Name: row.catalogId2Name,
                catalogId3: row.catalogId3,
                catalogId3Name: row.catalogId3Name,
                videoExt: 'mp4',
                videoSize: row.videoSize,
                title: row.title,
                fileId: null,
                id: row.id,
            }
            this.drawer = true
        },
        closeDrawer(isclose) {
            if (!isclose) return
            //遍历对象
            for (let key in this.addVideoInfo) {
                if (key != 'videoExt' && key != 'videoSize' && key != 'fileId') {
                    if (this.addVideoInfo[key] != null && this.addVideoInfo[key] != "") return this.$confirm('当前弹窗有未保存的数据确定要关闭吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.drawer = false
                    }).catch(() => {
                        this.drawer = true
                    });
                }
            }
        },
        closeVideoPlyer() {
            this.dialogVisible = false;
            this.videoplayerReload = false;
        },
        async downLoad(file) {
            if (file.videoUrl == null || file.videoUrl == '') return this.$message.error('该数据没有视频')
            await createVideoMtlRec({ videoMtrIds: [file.id], type: 0 })
            window.open(file.videoUrl)
        },
        async downLoadVideo() {
            if (this.videoList.length == 0) return this.$message.error('请选择要下载的视频')
            let videoMtrIds = this.videoList.map(item => item.id)
            await createVideoMtlRec({ videoMtrIds, type: 0 })
            this.videoList.forEach(item => {
                //在当前页面打开
                window.open(item.url)
            })
        },
        tabClick() {
            if (this.activeName == 'first') {
                this.$refs.downList.getList()
            } else {
                this.$refs.deleteList.getList()
            }
        },
        delProps() {
            if (this.delIds.length == 0) return this.$message.error('请选择要删除的数据');
            this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await deleteVideoMaterial(this.delIds)
                if (success) {
                    this.getList()
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }
                this.delIds = []
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        //点击复选框
        checkboxRangeEnd(row) {
            this.delIds = row.map(item => item.id)
            this.videoList = row.filter(item => item.videoUrl != null).map(item => {
                return {
                    url: item.videoUrl,
                    name: item.title,
                    id: item.id
                }
            })
        },
        async delVideo(row) {
            this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await deleteVideoMaterial([row.id])
                if (success) {
                    this.getList()
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        openVideo(row) {
            this.videoUrl = row.videoUrl
            this.dialogVisible = true
            this.videoplayerReload = true
        },
        uploadFinish(data) {
            if (data.size > 1024 * 1024 * 300) {
                this.$message.error('视频大小不能超过300M')
                this.$refs.upload.clearFiles()
                return
            }
            this.raw = data.raw
            if (!this.addVideoInfo.title) {
                this.addVideoInfo.title = data.name
            }
            this.addVideoInfo.videoSize = data.size
            this.$message.success('已选择');
            this.$refs.upload.clearFiles()
            return false
        },
        async searchGoodsName(e) {
            //去掉空格
            e = e.replace(/\s+/g, "")
            const { data, success } = await getMyGoodsByCode({ goodsCode: e })
            if (success) {
                this.addVideoInfo.goodsName = data.goodsName ? data.goodsName : ''
            }
        },
        handleClose(tag) {
            this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
        },
        showInput() {
            this.inputVisible = true;
            this.$nextTick(_ => {
                this.$refs.saveTagInput.$refs.input.focus();
            });
        },
        handleInputConfirm() {
            let inputValue = this.inputValue;
            if (inputValue) {
                this.dynamicTags.push(inputValue);
            }
            this.inputVisible = false;
            this.inputValue = '';
        },
        async changeCatalog(e, type, isIndex) {
            if (isIndex) {
                if (type == 'catalogId1') {
                    if (e) {
                        //找出二级类目
                        this.ListInfo.catalogId2 = null
                        this.indexCatalogId3List = []
                        this.ListInfo.catalogId3 = null
                        await this.getCategoryByPlatform(this.ListInfo.platform, e, type, isIndex)
                    } else {
                        this.ListInfo.catalogId2 = null
                        this.indexCatalogId2List = []
                        this.ListInfo.catalogId3 = null
                        this.indexCatalogId3List = []
                    }
                } else if (type == 'catalogId2') {
                    if (e) {
                        //找出三级类目
                        this.ListInfo.catalogId3 = null
                        await this.getCategoryByPlatform(this.ListInfo.platform, e, type, isIndex)
                    } else {
                        this.ListInfo.catalogId3 = null
                        this.indexCatalogId3List = []
                    }
                } else {
                    if (!e) {
                        this.ListInfo.catalogId3 = null
                    }
                }
            } else {
                if (type == 'catalogId1') {
                    if (e) {
                        //找出二级类目
                        this.addVideoInfo.catalogId2Name = null
                        this.addVideoInfo.catalogId3Name = null
                        this.addVideoInfo.catalogId2 = null
                        this.addVideoInfo.catalogId3 = null
                        this.catalogId3List = []
                        await this.getCategoryByPlatform(this.addVideoInfo.platform, e, type, isIndex)
                    } else {
                        this.addVideoInfo.catalogId1Name = null
                        this.addVideoInfo.catalogId2Name = null
                        this.addVideoInfo.catalogId3Name = null
                        this.addVideoInfo.catalogId2 = null
                        this.addVideoInfo.catalogId3 = null
                        this.catalogId2List = []
                        this.catalogId3List = []
                    }
                } else if (type == 'catalogId2') {
                    if (e) {
                        //找出三级类目
                        this.addVideoInfo.catalogId3Name = null
                        this.addVideoInfo.catalogId3 = null
                        await this.getCategoryByPlatform(this.addVideoInfo.platform, e, type, isIndex)
                    } else {
                        this.addVideoInfo.catalogId2Name = null
                        this.addVideoInfo.catalogId3Name = null
                        this.addVideoInfo.catalogId3 = null
                        this.catalogId3List = []
                    }
                } else {
                    if (e) {
                        this.addVideoInfo.catalogId3Name = null
                        await this.getCategoryByPlatform(this.addVideoInfo.platform, e, type, isIndex)
                    } else {
                        this.addVideoInfo.catalogId3Name = null
                        this.addVideoInfo.catalogId3 = null
                    }
                }
            }
        },
        async getCategoryByPlatform(platform, parentId, type, isIndex) {
            const { data, success } = await getCategoryLists({ platform, parentId })
            if (success) {
                //优化上面代码
                if (isIndex) {
                    if (type == 'platform') {
                        this.indexCatalogId1List = data.data
                    } else if (type == 'catalogId1') {
                        this.indexCatalogId2List = data.data
                    } else if (type == 'catalogId2') {
                        this.indexCatalogId3List = data.data
                    } else {
                        this.indexCatalogId3List = data.data
                    }
                } else {
                    if (type == 'platform') {
                        this.catalogId1List = data.data
                    } else if (type == 'catalogId1') {
                        this.catalogId2List = data.data
                        this.addVideoInfo.catalogId1Name = this.catalogId1List.find(item => item.id == parentId).label
                    } else if (type == 'catalogId2') {
                        this.catalogId3List = data.data
                        this.addVideoInfo.catalogId2Name = this.catalogId2List.find(item => item.id == parentId).label
                    } else {
                        this.addVideoInfo.catalogId3Name = this.catalogId3List.find(item => item.id == parentId).label
                    }
                }

            }
        },
        async changePlatFrom(e, isIndex) {
            if (e) {
                await this.getCategoryByPlatform(e, "0", 'platform', isIndex)
            } else {
                if (isIndex) {
                    this.indexCatalogId1List = []
                    this.indexCatalogId2List = []
                    this.indexCatalogId3List = []
                    this.ListInfo.catalogId1 = null
                    this.ListInfo.catalogId2 = null
                    this.ListInfo.catalogId3 = null
                } else {
                    this.catalogId1List = []
                    this.catalogId2List = []
                    this.catalogId3List = []
                    this.addVideoInfo.catalogId1 = null
                    this.addVideoInfo.catalogId2 = null
                    this.addVideoInfo.catalogId3 = null
                    this.addVideoInfo.catalogId1Name = null
                    this.addVideoInfo.catalogId2Name = null
                    this.addVideoInfo.catalogId3Name = null
                }
            }
        },
        async connectionSocket() {
            //第一步创建WebSocket对象
            return new Promise((resolve, reject) => {
                let sc = new WebSocket("ws://127.0.0.1:9999");
                sc.addEventListener("open", function () {
                    resolve(sc);
                });
                sc.addEventListener("error", function (event) {
                    this.$message.error('连接失败');
                    reject(null);
                });
            });
        },
        async onUpload() {
            if (this.socket.readyState != 1) {
                this.socket = await connectionSocket();
            }
            let domain;
            var env = process.env.NODE_ENV;
            domain = process.env.VUE_APP_BASE_API_UpLoad_Video;
            this.socket.send(JSON.stringify({
                action: 'htmlUpload',
                fileName: this.raw.name,
                size: this.addVideoInfo.videoSize,
                scene: '0perateVideo',
                sceneId: this.addVideoInfo.fileId,//业务ID
                // trigger: `http://192.168.16.22:32663/api/OperateManage/VideoMaterial/UpdateStatus`,//上传完回调接口，post
                trigger: `${domain}/api/operatemanage/VideoMaterial/UpdateStatus`,//上传完回调接口，post
            }))
            this.socket.send(this.raw)
        },
        submitForm(formName) {
            if (!this.raw && !this.isEdit) return this.$message.error('请上传视频');
            if ((this.addVideoInfo.title == null || this.addVideoInfo.title == '') && this.raw) return this.$message.error('请输入视频名称');
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    this.addVideoInfo.tags = this.dynamicTags.length > 0 ? this.dynamicTags.join(',') : null
                    if (!this.isEdit) {
                        const { data, success } = await createVideoMtl(this.addVideoInfo)
                        if (success) {
                            this.addVideoInfo.fileId = data ? data : null
                            if (this.raw) {
                                if (this.socket?.readyState != 1) {
                                    this.$message.success('正在上传,请等候......');
                                    await this.AjaxFile(this.raw, 0, "")
                                } else {
                                    await this.onUpload()
                                }
                            }
                            await this.getList()
                            this.$message.success('保存成功');
                            this.clear()
                            this.drawer = false
                        }
                    } else {
                        const { success } = await updateVideoMtl(this.addVideoInfo)
                        if (success) {
                            this.addVideoInfo.fileId = this.addVideoInfo.id ? this.addVideoInfo.id : null
                            if (this.raw) {
                                if (this.socket?.readyState != 1) {
                                    this.drawerLoding = true
                                    this.$message.success('正在上传,请等候......');
                                    await this.AjaxFile(this.raw, 0, "")
                                } else {
                                    await this.onUpload()
                                }
                            }
                            await this.getList()
                            this.$message.success('保存成功');
                            this.clear()
                            this.drawerLoding = false
                            this.drawer = false
                        }
                    }

                } else {
                    return this.$message.error('请完善信息');
                }
            });
        },
        clear() {
            //清空弹窗所有类目
            this.catalogId1List = []
            this.catalogId2List = []
            this.catalogId3List = []
            this.raw = null
            this.addVideoInfo = {
                platform: null,
                uploader: null,
                goodsCode: null,
                goodsName: null,
                tags: null,
                catalogId1: null,
                catalogId1Name: null,
                catalogId2: null,
                catalogId2Name: null,
                catalogId3: null,
                catalogId3Name: null,
                videoExt: 'mp4',
                videoSize: null,
                title: null,
                fileId: null,
                id: null,
            }
        },
        async AjaxFile(file, i, batchnumber) {
            let uploadTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss');
            var name = file.name; //文件名
            var size = file.size; //总大小shardSize = 2 * 1024 * 1024,
            var shardSize = 1024 * 512;
            var shardCount = Math.ceil(size / shardSize); //总片数
            if (i >= shardCount) {
                return;
            }
            //计算每一片的起始与结束位置
            var start = i * shardSize;
            var end = Math.min(size, start + shardSize);
            //构造一个表单，FormData是HTML5新增的
            i = i + 1;
            var form = new FormData();
            form.append("data", file.slice(start, end)); //slice方法用于切出文件的一部分
            form.append("batchnumber", batchnumber);
            form.append("fileName", name);
            form.append("total", shardCount); //总片数
            form.append("index", i); //当前是第几片
            const res = await xMTVideoUploadBlockAsync(form);
            if (res?.success) {
                this.percentage = (i * 100 / shardCount).toFixed(2);
                if (i == shardCount) {
                    this.atfterUplaodData = res.data;
                } else {
                    await this.AjaxFile(file, i, res.data);
                }
                if (this.atfterUplaodData.url) {
                    //获取当前时间
                    this.updateStatusAsync('0perateVideo', this.addVideoInfo.fileId, 2, this.atfterUplaodData.url, uploadTime)
                }
            } else {
                this.$message({ message: res?.msg, type: "warning" });
                this.ScpdLoading = false;
                this.updateStatusAsync('0perateVideo', this.addVideoInfo.fileId, -1, null, uploadTime)
            }
        },
        //更改视频状态
        async updateStatusAsync(scene, sceneId, uploadStatus, fileUrl, uploadTime) {
            await updateStatus({ scene, sceneId, uploadStatus, fileUrl, uploadTime })
        },
        addVideo() {
            this.clear()
            this.isEdit = false
            this.dynamicTags = []
            this.isIndex = false
            this.drawer = true
        },
        uploadList() {
            this.uploadListVisable = true
        },
        async getList(isSearch) {
            if (isSearch) {
                this.ListInfo.currentPage = 1
            }
            const replaceArr = ['goodsCode', 'tag', 'uploader']
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            const { data, success } = await pageGetVideoMtl(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        uploadRecordsortchange({ order, prop }) {
            if (prop) {
                this.uploadListInfo.orderBy = prop
                this.uploadListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getUploadList()
            }
        },

    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 20px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.videoTags {
    padding: 10px 0;
    height: 100px;
    border: 1px solid #e9e4e4;
    overflow: auto;
}

.button-new-tag {
    margin-left: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
}

.demo-ruleForm {
    padding: 10px;
}

.btnGroup {
    display: flex;
    justify-content: end;
}

.tag {
    height: 25px;
    margin-right: 10px;
    text-align: center;
    line-height: 25px;
}
</style>