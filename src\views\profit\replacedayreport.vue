<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="付款时间">
                    <el-date-picker style="width: 200px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                        range-separator="至" start-placeholder="开始" end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <el-form-item label="内部订单号:">
                    <el-input v-model="filter.orderNoInner" style="width: 150px" placeholder="内部订单号" @keyup.enter.native="onSearch" clearable/>
                </el-form-item>
                <el-form-item label="操作人:">
                    <el-input v-model="filter.operateName" style="width: 150px" placeholder="操作人" @keyup.enter.native="onSearch" clearable/>
                </el-form-item>
                <el-form-item label="订单状态:">
                    <el-select filterable v-model="filter.status" placeholder="订单状态" clearable style="width: 100px">
                        <el-option label="发货中" value="发货中"></el-option>
                        <el-option label="取消" value="取消"></el-option>
                        <el-option label="被合并" value="被合并"></el-option>
                        <el-option label="已发货" value="已发货"></el-option>
                        <el-option label="被拆分" value="被拆分"></el-option>
                        <el-option label="异常" value="异常"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="退款状态:">
                    <el-select filterable v-model="filter.refundStatus" placeholder="退款状态" clearable style="width: 100px">
                        <el-option label="成功退款" value="成功退款"></el-option>
                        <el-option label="未申请退款" value="未申请退款"></el-option>
                        <el-option label="等待退款" value="等待退款"></el-option>
                        <el-option label="退款关闭" value="退款关闭"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="商品编码:">
                    <el-input v-model="filter.goodsCode" style="width: 150px" placeholder="商品编码" @keyup.enter.native="onSearch" clearable/>
                </el-form-item>
                <el-form-item label="所属店铺:">
                    <el-select filterable v-model="filter.shopCode" placeholder="请选择店铺" clearable style="width: 120px">
                        <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.shopCode"/>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
            :tableData='list'  :tableCols='tableCols' :isSelection="false" @select="selectchange"
            :tableHandles='tableHandles' @cellclick="cellclick"
            :loading="listLoading">
            </ces-table>
            <template #footer>
                <my-pagination ref="pager" :total="total" :checked-count="sels.length"  @get-page="getlist"/>
            </template>

        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="40%">
          <!-- <el-alert title="温馨提示：导入文件只填入内部订单号即可"
                    type="success" :closable="false" style="margin-bottom:10px;">
          </el-alert> -->
          <span>
            <el-upload ref="upload" class="upload-demo"
              :auto-upload="false" :multiple="true" :limit="4" action accept=".xlsx"
              :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
              <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}   </el-button>
            </el-upload>           
          </span>
          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">关闭</el-button>
          </span>
        </el-dialog>

        <el-drawer title="设置" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editparmVisible"
                direction="btt" size="'auto'" class="el-drawer__wrapper"  style="position:absolute;">
            <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
            <div class="drawer-footer">
                <el-button @click.native="editparmVisible = false">取消</el-button>
                <my-confirm-button type="submit"  :loading="editparmLoading" @click="onSetEditParm" />
            </div>
        </el-drawer>

        <el-drawer title="设置" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editparmVisible1"
                direction="btt" size="'auto'" class="el-drawer__wrapper"  style="position:absolute;">
            <form-create :rule="autoform1.rule" v-model="autoform1.fApi" :option="autoform1.options"/>
            <div class="drawer-footer">
                <el-button @click.native="editparmVisible1 = false">取消</el-button>
                <my-confirm-button type="submit"  :loading="editparmLoading1" @click="onSetEditParm" />
            </div>
        </el-drawer>

        <el-drawer title="设置" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editparmVisible2"
                direction="btt" size="'auto'" class="el-drawer__wrapper"  style="position:absolute;">
            <form-create :rule="autoform2.rule" v-model="autoform2.fApi" :option="autoform2.options"/>
            <div class="drawer-footer">
                <el-button @click.native="editparmVisible2 = false">取消</el-button>
                <my-confirm-button type="submit"  :loading="editparmLoading2" @click="onSetEditParm" />
            </div>
        </el-drawer>

        <el-dialog :visible.sync="showDetailVisible" width="80%" :show-close="false" v-dialogDrag>
            <div style="height:680px;"> 
                <replacedayreportdetail ref="replacedayreportdetail" @nSearch="nSearch" style="height:100%;"></replacedayreportdetail>
            </div>
        </el-dialog>

    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import { importReplace, getReplaceDayReportList, batchUpdateReplace, exportReplaceDayReportList } from "@/api/profit/reportday"
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import replacedayreportdetail from "./replacedayreportdetail.vue"

const tableCols =[
        {istrue:true,prop:'payTime',label:'订单付款时间', tipmesg:'聚水潭维度的订单付款时间', width:'120',sortable:'custom',},
        {istrue:true,prop:'orderTime',label:'代拍操作时间', tipmesg:'系统自动判定为该笔订单ERP操作提交时间',width:'120',sortable:'custom',}, 
        {istrue:true,prop:'sendTime',label:'发货时间', tipmesg:'', width:'100',sortable:'custom',},         
        {istrue:true,prop:'orderType',label:'订单类型', tipmesg:'', width:'100',},                    
        {istrue:true,prop:'orderNoInner',label:'聚水潭内部订单号', tipmesg:'人工导入', width:'150',sortable:'custom',type:'click',style:(that,row)=>that.renderAmont(row),handle:(that,row)=>that.showAmont(row),}, 
        {istrue:true,prop:'shopName',label:'店铺名称', tipmesg:'', width:'200',},       
        {istrue:true,prop:'goodsCode',label:'商品编码', tipmesg:'订单内出现的商品编码', width:'120',sortable:'custom'},  
        {istrue:true,prop:'goodsName',label:'商品名称', width:'125',},     
        {istrue:true,prop:'orderAmount',label:'订单金额', tipmesg:'聚水潭订单付款金额', width:'100',sortable:'custom',},
        {istrue:true,prop:'deductAmountPaid',label:'平台扣点', tipmesg:'', width:'100',sortable:'custom',},
        {istrue:true,prop:'qty',label:'数量',sortable:'custom', tipmesg:'订单对应编码的下单数量', width:'80',},       
        //{istrue:true,prop:'goodsCost',label:'订单金额合计', width:'100',sortable:'custom',},
        {istrue:true,prop:'expressCompany',label:'快递公司', tipmesg:'采购或运营在聚水潭发货后会有快递发件信息', width:'100',sortable:'custom',formatter:(row)=> !row.expressName? " " : row.expressName},
        {istrue:true,prop:'expressNo',label:'快递单号', tipmesg:'采购或运营在聚水潭发货后会有快递发件信息', width:'100',sortable:'custom',},
        {istrue:true,prop:'productAmount',label:'产品金额', tipmesg:'实付给厂家供应商的金额', width:'100',sortable:'custom',},
        {istrue:true,prop:'freightAmount',label:'运费金额', tipmesg:'', width:'100',sortable:'custom',},
        {istrue:true,prop:'replaceAmount',label:'代拍总金额', tipmesg:'产品金额+运费金额', width:'100',sortable:'custom',},
        {istrue:true,prop:'payState',label:'付款状态', tipmesg:'', width:'auto',sortable:'custom',},
        {istrue:true,prop:'profit1',label:'利润情况', tipmesg:'订单金额-平台扣点-代拍总金额', width:'auto',sortable:'custom',},
        {istrue:true,prop:'purchaseDitch',label:'采购渠道', tipmesg:'', width:'auto',sortable:'custom',},
        {istrue:true,prop:'buyNo',label:'采购订单号', tipmesg:'阿里巴巴采购必填订单号，线下自动同步线下状态', width:'150',sortable:'custom',},
        {istrue:true,prop:'status',label:'订单状态', tipmesg:'', width:'100',sortable:'custom',},
        //{istrue:true,prop:'refundStatus',label:'退款状态', tipmesg:'', width:'100',sortable:'custom',},
        {istrue:true,prop:'createdUserName',label:'下单操作员', tipmesg:'采购或运营，提交信息人的系统名字', width:'100',},
        {istrue:true,prop:'remark',label:'特殊备注', tipmesg:'', width:'100',},
        {istrue:true,type:'button',label:'操作',btnList:[{label:"编辑",handle:(that,row)=>that.onHand(row,1)}]},
        {istrue:true,prop:'refundStatus',label:'退款状态', tipmesg:'', width:'100',sortable:'custom',type:'click',style:(that,row)=>that.renderRefundStatus(row),},
        {istrue:true,prop:'refundAmount',label:'实际退回金额', tipmesg:'实际退回金额，人工填写', width:'auto',sortable:'custom',},
        {istrue:true,prop:'operateName1',label:'退款操作员', tipmesg:'', width:'100',},
        {istrue:true,type:'button',label:'退款操作',btnList:[{label:"编辑",handle:(that,row)=>that.onHand(row,2)}]},
        {istrue:true,prop:'refundRemark',label:'财务备注', tipmesg:'财务确认后备注（成功退款、退款中）', width:'auto',sortable:'custom',},
        {istrue:true,prop:'operateName2',label:'财务操作员', tipmesg:'', width:'100',},
        {istrue:true,type:'button',label:'财务操作',btnList:[{label:"编辑",handle:(that,row)=>that.onHand(row,3)}]},
]

const tableHandles=[
        {label:"导入", handle:(that)=>that.startImport()},    
        {label:"导出", handle:(that)=>that.onExportDetail()},  
        {label:"模板-代拍导入模板", handle:(that)=>that.downloadOrherTemplate()},
      ];

const startTime = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunhanAdminReplacedayreport',
    components: {container, cesTable, MyConfirmButton, replacedayreportdetail},

    data() {
        return {
            that: this,
            filter:{
                startTime: null,
                endTime: null,
                timerange:[startTime, endTime],
                procode:null,
                title:null,
                platform:null,
                shopCode:null,
                groupId:null,
                operateSpecialId:null,
                operateName:null,
                user3Id:null,
                shopId:null,
                platform:null,
                newPattern:null,
                customer:null,
                status:null,
                refundStatus:null
            },
            list: [],
            shopList: [],
            summaryarry:{},
            pager:{OrderBy:"payTime",IsAsc:false},
            pickerOptions:{
                disabledDate(time){
                return time.getTime()>Date.now();
                }
            },        
            onHandNumber:null,   
            tableCols:tableCols,
            tableHandles:tableHandles,
            total: 0,
            sels: [], 
            editparmLoading: false,
            editparmLoading1: false,
            editparmLoading2: false,
            editparmVisible: false,
            editparmVisible1: false,
            editparmVisible2: false,
            dialogVisible: false,
            listLoading: false,
            dialogVisible: false,
            uploadLoading: false,
            showDetailVisible: false,
            autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[{type:'hidden',field:'orderNo',title:'orderNo',value: '',col:{span:12}},
                     {type:'hidden',field:'orderNoInner',title:'orderNoInner',value: '',col:{span:12}},   
                     {type:'InputNumber',field:'productAmount',title:'产品金额',value: null,props:{min:0,precision:2,step:0.5},col:{span:6}},
                     {type:'InputNumber',field:'freightAmount',title:'运费金额',value: null,props:{min:0,precision:2,step:0.5},col:{span:6}},
                     {type: "select",field: "purchaseDitch",title: "采购渠道",
                        options: [
                            {"value": "阿里巴巴", "label": "阿里巴巴", "disabled": false},
                            {"value": "线下", "label": "线下", "disabled": false},
                            {"value": "淘宝", "label": "淘宝", "disabled": false},
                            {"value": "拼多多", "label": "拼多多", "disabled": false},
                        ],
                     },
                     {type:'input',field:'buyNo',title:'采购订单号',validate:[{ required: true, message: '请输入信息', trigger: 'blur' },],},                    
                     {type:'input',field:'remark',title:'备注',props:{type:'textarea'}}                   
                    ]
          },
          autoform1:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[{type:'hidden',field:'orderNo',title:'orderNo',value: '',col:{span:12}},
                     {type:'hidden',field:'orderNoInner',title:'orderNoInner',value: '',col:{span:12}},    
                     {type: "select",field: "refundType",title: "退款状态",
                        options: [
                            {"value": "已申请退款中", "label": "已申请退款中", "disabled": false},
                            {"value": "已完成退款", "label": "已完成退款", "disabled": false},
                        ],
                     },
                     {type:'InputNumber',field:'refundAmount',title:'实际退回',value: null,props:{min:0,precision:2,step:0.5},col:{span:6}},                    
                    ]
          },
          autoform2:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[{type:'hidden',field:'orderNo',title:'orderNo',value: '',col:{span:12}},
                     {type:'hidden',field:'orderNoInner',title:'orderNoInner',value: '',col:{span:12}}, 
                     {type:'input',field:'refundRemark',title:'财务备注',props: {type: "textarea",}},                     
                    ]
          },
        };
    },

    async mounted() {
        await this.onSearch()
        await this.onchangeplatform()
    },

    methods: {
        //获取店铺
        async onchangeplatform(){
            this.categorylist =[]
            const res1 = await getshopList({platform:null,CurrentPage:1,PageSize:100});
            this.shopList=res1.data.list
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page  = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...pager,...page,... this.filter}
            if(params===false){
                return;
            }
            if(params===false){
                    return;
            }
            this.listLoading = true
            const res = await getReplaceDayReportList(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry=res.data.summary;
            this.list = data
       },
        //导出
        async onExportDetail(){
            var page  = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = {...page,... this.filter}
            if(params===false){
                return;
            }
            var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
            var res= await exportReplaceDayReportList(params);
            loadingInstance.close();
            if(!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download','代拍日报详情_' + new Date().toLocaleString() + '.xlsx' )
            aLink.click();
        },
       async showAmont(row){
            if (row.orderCount === 1) return
            this.showDetailVisible = true
            let param = {orderNoInner: row.orderNoInner}
            let that = this
            that.$nextTick(async () =>{
                await that.$refs.replacedayreportdetail.onSearch1(param)
            })
            
        },
        renderAmont(row){
            if(row.orderCount > 1) return "color:blue;cursor:pointer;";
            else return "";
        },
       async onHand(row, num){   
        this.onHandNumber = num
        var model;
        if (num == 1){
           this.editparmVisible = true      

           model = {orderNo: row.orderNo, orderNoInner: row.orderNoInner, buyNo: row.buyNo, replaceAmount: row.replaceAmount , 
                    isTrue: 1, purchaseDitch: row.purchaseDitch, freightAmount: row.freightAmount, productAmount: row.productAmount,remark: row.remark} 
           var arr = Object.keys(this.autoform.fApi)
           if (arr.length > 0)
             await this.autoform.fApi.resetFields()

           this.$nextTick(async() =>{
               await this.autoform.fApi.setValue(model)
           })  
        }
        if (num == 2){
            this.editparmVisible1 = true      
            
            model = {orderNo: row.orderNo, orderNoInner: row.orderNoInner, refundType: row.refundType, refundAmount: row.refundAmount , isTrue: 2, } 
            var arr = Object.keys(this.autoform1.fApi)
            if (arr.length > 0)
                await this.autoform1.fApi.resetFields()

            this.$nextTick(async() =>{
                await this.autoform1.fApi.setValue(model)
            })  
        }
        if (num == 3){
            this.editparmVisible2 = true    
            
            model = {orderNo: row.orderNo, orderNoInner: row.orderNoInner, refundRemark: row.refundRemark, isTrue: 3, } 
            var arr = Object.keys(this.autoform2.fApi)
            if (arr.length > 0)
                await this.autoform2.fApi.resetFields()

            this.$nextTick(async() =>{
                await this.autoform2.fApi.setValue(model)
            })  
        }   
           

       },
       async onSetEditParm(){
        if (this.onHandNumber == 1){          
            this.autoform.fApi.validate(async (valid, fail) => {
                if(valid){
                    this.editparmLoading = true
                    const formData = this.autoform.fApi.formData();
                    formData.isTrue = 1;
                    
                    await batchUpdateReplace(formData)

                    this.editparmLoading = false
                    this.editparmVisible = false                   
                    this.getlist()

                }
            })
        }
        if (this.onHandNumber == 2){            
            this.autoform1.fApi.validate(async (valid, fail) => {
                if(valid){
                    this.editparmLoading1 = true
                    const formData = this.autoform1.fApi.formData();
                    formData.isTrue = 2;
    
                    await batchUpdateReplace(formData)

                    this.editparmLoading1 = false
                    this.editparmVisible1 = false                   
                    this.getlist()

                }
            })
        }
        if (this.onHandNumber == 3){          
            this.autoform2.fApi.validate(async (valid, fail) => {
                if(valid){
                    this.editparmLoading2 = true
                    const formData = this.autoform2.fApi.formData();
                    formData.isTrue = 3;
    
                    await batchUpdateReplace(formData)

                    this.editparmLoading2 = false
                    this.editparmVisible2 = false                   
                    this.getlist()

                }
            })
        }
            
       },
       async nSearch(){
            await this.getlist()
       },
       //字体颜色
       renderRefundStatus(row){
            if (row.refundStatus == '成功退款' || row.refundStatus == '等待退款') {
                return "color:red;cursor:pointer;";
            } else return "";
        },
       //开始导入
        startImport(){
            this.dialogVisible=true;
        },
        //取消导入
        cancelImport(){
            this.dialogVisible=false;
        },
        uploadSuccess(response, file, fileList) {
            if (response.code == 200) {
            } else {
                fileList.splice(fileList.indexOf(file), 1);
            }
        },
        async submitUpload() {
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请先选取文件", type: "warning" });
                return false;
            }
            this.fileHasSubmit=true;
            this.uploadLoading=true;
            this.$refs.upload.submit();
        },
        async uploadFile(item) {
            if(!this.fileHasSubmit){
                return false;
            }
            this.fileHasSubmit=false;
            const form = new FormData();
            form.append("token", this.token);
            form.append("upfile", item.file);
            const res =await importReplace(form);
            if (res.code==1)   this.$message({ message: "上传成功,正在导入中...", type: "success" });
            else  this.$message({ message: res.msg, type: "warning" });
            this.uploadLoading=false; 
        },
        async uploadChange(file, fileList) {
        if (fileList && fileList.length > 0) {
            var list = [];
            for(var i=0;i<fileList.length;i++){
            if(fileList[i].status=="success")
                list.push(fileList[i]);
            else
                list.push(fileList[i].raw);
            }
            this.fileList = list;
        }
        },
        async uploadRemove(file, fileList){
            this.uploadChange(file, fileList);
        },
        async sortchange(column){
        if(!column.order)
            this.pager={};
        else{
            this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
        }
        await this.onSearch();
        },  
        selectchange:function(rows,row) {
            this.selids=[];console.log(rows)
            rows.forEach(f=>{
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event){
        
        },
        async downloadOrherTemplate(){
            window.open("../static/excel/dayreport/代拍导入模板.xlsx","_self");
        }, 
    },
};
</script>

<style lang="scss" scoped>

</style>