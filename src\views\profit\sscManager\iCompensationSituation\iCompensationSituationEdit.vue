<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%; overflow: hidden;">
    <el-scrollbar style="flex: 1; padding-right: 10px;">
      <el-form :model="ruleForm" ref="refruleForm" :rules="rules" label-width="180px" class="demo-ruleForm"
        style="padding-right: 20px;">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名：" prop="name">
              <el-input v-model.trim="ruleForm.name" placeholder="请输入姓名" maxlength="50" clearable class="publicCss" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="区域：" prop="region">
              <el-input v-model.trim="ruleForm.region" placeholder="请输入区域" maxlength="50" clearable class="publicCss" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="部门：" prop="dept">
              <el-input v-model.trim="ruleForm.dept" placeholder="请输入部门" maxlength="50" clearable class="publicCss" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型：" prop="type">
              <el-input v-model.trim="ruleForm.type" placeholder="请输入类型" maxlength="50" clearable class="publicCss" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="事故日期：" prop="accidentDate">
              <el-date-picker v-model="ruleForm.accidentDate" type="date" placeholder="选择事故日期" value-format="yyyy-MM-dd"
                class="publicCss" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="受伤时间：" prop="injuryTime">
              <el-date-picker v-model="ruleForm.injuryTime" type="date" placeholder="选择受伤时间" value-format="yyyy-MM-dd"
                class="publicCss">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工伤类型：" prop="workInjuriesType">
              <el-input v-model.trim="ruleForm.workInjuriesType" placeholder="请输入工伤类型" maxlength="50" clearable
                class="publicCss" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="受伤部位：" prop="injuredArea">
              <el-input v-model.trim="ruleForm.injuredArea" placeholder="请输入受伤部位" maxlength="50" clearable
                class="publicCss" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否参险：" prop="isInsurance">
              <el-select v-model="ruleForm.isInsurance" placeholder="请选择" class="publicCss">
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否骨折：" prop="isFracture">
              <el-select v-model="ruleForm.isFracture" placeholder="请选择" class="publicCss">
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="受伤原因及经过：" prop="injuryCauseProcess">
          <el-input v-model.trim="ruleForm.injuryCauseProcess" type="textarea" :rows="3" placeholder="请输入受伤原因及经过"
            maxlength="50" clearable class="publicCss" />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="涉及设备及工艺名称：" prop="involvingEquipmentProcessName">
              <el-input v-model.trim="ruleForm.involvingEquipmentProcessName" placeholder="请输入涉及设备及工艺名称" maxlength="50"
                clearable class="publicCss" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事故报警时间：" prop="accidentAlarmTime">
              <el-date-picker v-model="ruleForm.accidentAlarmTime" type="datetime" placeholder="选择事故报警时间"
                value-format="yyyy-MM-dd HH:mm:ss" class="publicCss">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="过程照片:">
          <uploadimgFile ref="imageuploadimgFile" v-if="logVisible" :disabled="isView" :ispaste="!isView"
            :noDel="isView" :reveal="true" :accepttyes="accepttyes" :isImage="true" :uploadInfo="ruleForm.processPhotos"
            :keys="[1, 1]" @callback="getImg($event, 'image')" @beforeUpload="beforeUpload($event, 'image')"
            :imgmaxsize="10" :limit="10" :multiple="true">
          </uploadimgFile>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="休息天数：" prop="restDays">
              <inputNumberYh v-model="ruleForm.restDays" placeholder="请输入休息天数" class="publicCss" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="住院天数：" prop="hospitalizationDays">
              <inputNumberYh v-model="ruleForm.hospitalizationDays" placeholder="请输入住院天数" class="publicCss" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="医疗费用：" prop="medicalExpenses">
              <inputNumberYh v-model="ruleForm.medicalExpenses" placeholder="请输入医疗费用" class="publicCss" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保险赔偿误工费：" prop="insuranceCompensationLostWages">
              <inputNumberYh v-model="ruleForm.insuranceCompensationLostWages" placeholder="请输入保险赔偿误工费"
                class="publicCss" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="公司赔付误工费：" prop="companyCompensatesLostWorkExpenses">
              <inputNumberYh v-model="ruleForm.companyCompensatesLostWorkExpenses" placeholder="请输入公司赔付误工费"
                class="publicCss" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合计产生费用：" prop="totalExpensesIncurred">
              <inputNumberYh v-model="ruleForm.totalExpensesIncurred" placeholder="请输入合计产生费用" class="publicCss" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="保险公司承担费用合计：" prop="totalCostBorneInsurance">
              <inputNumberYh v-model="ruleForm.totalCostBorneInsurance" placeholder="请输入保险公司承担费用合计" class="publicCss" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司承担费用合计：" prop="totalExpensesBorneCompany">
              <inputNumberYh v-model="ruleForm.totalExpensesBorneCompany" placeholder="请输入公司承担费用合计" class="publicCss" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="工伤事故受理编号：" prop="workInjuryAccidentAcceptanceNumber">
          <el-input v-model.trim="ruleForm.workInjuryAccidentAcceptanceNumber" placeholder="请输入工伤事故受理编号" maxlength="50"
            clearable class="publicCss" />
        </el-form-item>

        <el-form-item label="工伤认定决议书：" prop="workInjuryIdentificationResolution">
          <uploadimgFile :filemaxsize="1" :accepttyes="fileAccepttyes" :isImage="true" :reveal="false"
            :uploadInfo="annexa" :keys="[1, 1]" v-if="logVisible" @callback="getFile" :imgmaxsize="0" :limit="1"
            :multiple="true">
          </uploadimgFile>
        </el-form-item>

        <el-form-item label="伤残鉴定结论及时间：" prop="disabilityAssessmentConclusionAndTime">
          <el-input v-model.trim="ruleForm.disabilityAssessmentConclusionAndTime" placeholder="请输入伤残鉴定结论及时间"
            maxlength="50" clearable class="publicCss" />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from '@/components/Comm/inputNumberYh.vue';
import MyConfirmButton from '@/components/my-confirm-button';
import { workInjuryCompensationSituationInEachDistrictSubmit } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission';
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import dayjs from 'dayjs';

export default {
  name: 'iCompensationSituationEdit',
  components: {
    inputNumberYh,
    MyConfirmButton,
    uploadimgFile,
  },
  props: {
    editInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      annexa: [],
      fileAccepttyes: '.doc,.docx',//.xls,.xlsx
      isView: false,
      accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
      logVisible: false,
      ruleForm: {
        name: '',
        region: '',
        dept: '',
        type: '',
        accidentDate: null,  // 修改：日期字段使用 null 而不是空字符串
        injuryTime: null,    // 修改：日期时间字段使用 null 而不是空字符串
        accidentAlarmTime: null,  // 事故报警时间
        workInjuriesType: '',
        injuredArea: '',
        isInsurance: '',
        isFracture: '',
        injuryCauseProcess: '',
        involvingEquipmentProcessName: '',
        restDays: null,
        hospitalizationDays: null,
        medicalExpenses: null,
        insuranceCompensationLostWages: null,
        companyCompensatesLostWorkExpenses: null,
        totalExpensesIncurred: null,
        totalCostBorneInsurance: null,
        totalExpensesBorneCompany: null,
        workInjuryAccidentAcceptanceNumber: '',
        workInjuryIdentificationResolution: '',
        disabilityAssessmentConclusionAndTime: ''
      },
      rules: {
        region: [
          { required: true, message: '请输入区域', trigger: 'blur' }
        ],
        dept: [
          { required: true, message: '请输入部门', trigger: 'blur' }
        ],
        accidentDate: [
          { required: true, message: '请选择事故日期', trigger: 'change' }
        ],
        injuryTime: [
          { required: true, message: '请选择受伤时间', trigger: 'change' }
        ],
        injuredArea: [
          { required: true, message: '请输入受伤部位', trigger: 'blur' }
        ],
        isInsurance: [
          { required: true, message: '请选择是否参险', trigger: 'change' }
        ],
        isFracture: [
          { required: true, message: '请选择是否骨折', trigger: 'change' }
        ],
        injuryCauseProcess: [
          { required: true, message: '请输入受伤原因及经过', trigger: 'blur' }
        ],
        involvingEquipmentProcessName: [
          { required: true, message: '请输入涉及设备及工艺名称', trigger: 'blur' }
        ],
        restDays: [
          { required: true, message: '请输入休息天数', trigger: 'blur' }
        ],
        hospitalizationDays: [
          { required: true, message: '请输入住院天数', trigger: 'blur' }
        ],
        medicalExpenses: [
          { required: true, message: '请输入医疗费用', trigger: 'blur' }
        ],
        insuranceCompensationLostWages: [
          { required: true, message: '请输入保险赔偿误工费', trigger: 'blur' }
        ],
        companyCompensatesLostWorkExpenses: [
          { required: true, message: '请输入公司赔付误工费', trigger: 'blur' }
        ],
        totalExpensesIncurred: [
          { required: true, message: '请输入合计产生费用', trigger: 'blur' }
        ],
        totalCostBorneInsurance: [
          { required: true, message: '请输入保险公司承担费用合计', trigger: 'blur' }
        ],
        totalExpensesBorneCompany: [
          { required: true, message: '请输入公司承担费用合计', trigger: 'blur' }
        ],
        workInjuryAccidentAcceptanceNumber: [
          { required: true, message: '请输入工伤事故受理编号', trigger: 'blur' }
        ],
        workInjuryIdentificationResolution: [
          { required: true, message: '请输入工伤认定决议书', trigger: 'blur' }
        ],
        disabilityAssessmentConclusionAndTime: [
          { required: true, message: '请输入伤残鉴定结论及时间', trigger: 'blur' }
        ],
      }
    }
  },

  async mounted() {
    console.log(this.ruleForm, 'ruleForm')
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    const editData = JSON.parse(JSON.stringify(this.editInfo));

    const processedProcessPhotos = editData.processPhotos ? this.handleImage(editData, 'processPhotos') : [];
    const processedWorkInjuryResolution = editData.workInjuryIdentificationResolution ? this.handleImage(editData, 'workInjuryIdentificationResolution') : [];
    // 设置附件数据用于回显
    this.annexa = processedWorkInjuryResolution.map(item => ({
      url: item.url,
      name: item.name || this.getFileNameFromUrl(item.url),
      fileName: item.name || this.getFileNameFromUrl(item.url)
    }));

    // 清理数据，处理空字符串和数据类型问题
    const cleanedEditData = this.cleanFormData(editData);

    this.ruleForm = {
      ...this.ruleForm,
      ...cleanedEditData,
      processPhotos: processedProcessPhotos,
      workInjuryIdentificationResolution: processedWorkInjuryResolution
    };
    this.logVisible = true
  },

  methods: {
    // 清理数据，处理空字符串和数据类型问题
    cleanFormData(data) {
      const cleaned = { ...data };

      // 处理日期字段：空字符串转为 null，并处理只有年月日的情况
      const dateFields = ['accidentDate', 'injuryTime'];
      const dateTimeFields = ['accidentAlarmTime']; // 需要时分秒的字段

      dateFields.forEach(field => {
        if (cleaned[field] === '') {
          cleaned[field] = null;
        }
      });

      dateTimeFields.forEach(field => {
        if (cleaned[field] === '') {
          cleaned[field] = null;
        } else if (cleaned[field]) {
          // 如果只有年月日，使用 dayjs 添加时分秒 00:00:00
          if (cleaned[field].length === 10 && cleaned[field].match(/^\d{4}-\d{2}-\d{2}$/)) {
            cleaned[field] = dayjs(cleaned[field]).format('YYYY-MM-DD HH:mm:ss');
          }
        }
      });

      // 处理数值字段：空字符串转为 null
      const numberFields = [
        'restDays', 'hospitalizationDays', 'medicalExpenses',
        'insuranceCompensationLostWages', 'companyCompensatesLostWorkExpenses',
        'totalExpensesIncurred', 'totalCostBorneInsurance', 'totalExpensesBorneCompany'
      ];
      numberFields.forEach(field => {
        if (cleaned[field] === '' || cleaned[field] === undefined) {
          cleaned[field] = null;
        }
      });

      return cleaned;
    },

    getFileNameFromUrl(url) {
      if (!url) return '';
      const parts = url.split('/');
      const fileName = parts[parts.length - 1];
      return fileName || '';
    },
    getFile(data) {
      if (data) {
        this.ruleForm.workInjuryIdentificationResolution = data.map(item => item.url).join(',')
      }
    },
    beforeUpload(data, type) {
      if (data.length > 0) {
        const target = 'processPhotos';
        this.ruleForm[target] = this.ruleForm[target].concat(
          data.map(item => ({
            url: item.url,
            name: item.name
          }))
        );
      }
    },
    getImg(data, type) {
      if (data) {
        const target = 'processPhotos';
        this.ruleForm[target] = data.map(item => ({
          url: item.url,
          name: item.fileName
        }));
      }
    },
    handleImage(item, field) {
      const images = item[field];
      if (images) {
        return images.split(',').map(url => ({ url, name: '' }));
      }
      return [];
    },
    cancellationMethod() {
      this.$emit('cancellationMethod', 2);
    },

    async submitForm(formName) {
      try {
        const valid = await this.$refs[formName].validate();
        if (valid) {
          const submitData = {
            ...this.ruleForm,
            id: this.editInfo.id, // 保留ID用于更新
            processPhotos: this.ruleForm.processPhotos.map(item => item.url).filter(Boolean).join(','),
            isArchive: checkPermission("ArchiveStatusEditing")
          };
          const { success } = await workInjuryCompensationSituationInEachDistrictSubmit(submitData);
          if (success) {
            this.$message.success('保存成功');
            this.$emit('cancellationMethod', 1);
            this.resetForm(formName);
          } else {
            this.$message.error('保存失败');
          }
        }
      } catch (error) {
        this.$message.error(`请检查${error ? Object.keys(error).length : ''}必填项是否填写完整`);
      }
    },

    resetForm(formName) {
      this.$refs[formName].resetFields();
    }
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 100%;
  max-width: 100%;
}

.demo-ruleForm {
  .el-form-item {
    margin-bottom: 18px;
  }

  .el-form-item__label {
    padding-right: 8px !important;
    font-size: 14px;
  }

  .el-input,
  .el-select,
  .el-date-editor {
    width: 100% !important;
  }

  .el-textarea {
    width: 100% !important;
  }
}

// 确保容器不会产生横向滚动
.el-scrollbar__wrap {
  overflow-x: hidden !important;
}
</style>
