<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                <el-button-group style="border:none;">
                  
                    <el-button style="padding: 0;margin: 0;border:none;width:140px;">
                        <el-select v-model="Filter.expressCompany"  clearable placeholder="快递公司" >
                            <el-option v-for="item in expressCompanys" :label="item" :value="item"></el-option>                           
                        </el-select>
                    </el-button>

                    
                    <el-button style="padding: 0;margin: 0;border:none;width:140px;">
                        <yh-cityselector :clearable="true" :placeStr="'请选择省市'" :MaxLvl="2"  :orgProps="{ checkStrictly: true }"
                        :Address="[Filter.delayProvince,Filter.delayCity]" @change="(v)=>{Filter.delayProvince=v[0];Filter.delayCity=v[1]}" />
                    </el-button>
                    

                
                    <el-button style="padding: 0;margin: 0;border:none;width:140px;">
                        <el-select v-model="Filter.delayType"  clearable placeholder="滞留类型" >
                            <el-option v-for="item in delayTypes" :label="item.label" :value="item.value"></el-option>                           
                        </el-select>
                    </el-button>                    

                    <el-button style="padding: 0;margin: 0;border:none;">
                        <el-select v-model="Filter.timeType" style="width:100px;">
                            <el-option label="按违规时间" :value="1"></el-option>
                            <el-option label="按物流时间" :value="0"></el-option>
                        </el-select>
                        <el-date-picker style="width:220px" 
                        v-model="Filter.gDate" 
                        type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" 
                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" 
                        :picker-options="pickerOptions"></el-date-picker>
                    </el-button>
                 

                    <el-button style="padding: 0;margin: 0;border:none;">
                        <el-input v-model.trim="Filter.keywords" clearable placeholder="关键字查询" style="width:160px;"  :maxlength="40">
                            <el-tooltip slot="suffix" class="item" effect="dark" :content="keywordsTip" placement="bottom">
                                <i  class="el-input__icon el-icon-question"></i>
                            </el-tooltip>           
                        </el-input>
                    </el-button>

                    <el-button type="primary" @click="onSearch" >查询</el-button>
                    <el-button type="primary" @click="()=>{Filter={};}">清空条件</el-button>

                   

                </el-button-group>
                统计维度
                <el-button style="padding: 0;margin: 0;border:none;">
                     
                        <!-- "DelayProvince","DelayCity","ExpressCompany","OpName","DelayType"                         -->
                        <el-checkbox-group v-model="groupProps" @change="groupPropsChange">
                            <el-checkbox label="ExpressCompany">快递公司</el-checkbox>
                            <el-checkbox label="DelayProvince">省</el-checkbox>
                            <el-checkbox label="DelayCity">市</el-checkbox>
                            <el-checkbox label="OpName" >状态</el-checkbox>
                            <el-checkbox label="DelayType" >滞留类型</el-checkbox>
                        </el-checkbox-group>
                    </el-button>
            </el-form>
        </template>

         <vxetablebase :id="'OrderLogisticsDelayListDetail2023022615210002'" 
            ref="xTable" :showsummary="true"
            :summaryarry="summaryarry"
            :that='that' :loading="listLoading"
            :tableData='tbdatalist' :tableCols='calcTableCols'           
            @sortchange='sortchange'
            >           
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettbdatalist1" />
        </template>
       

    

    </my-container>
</template>
<script>  

    import {
        PageCalcOrderLogisticsDelay, FilterDatas
    } from '@/api/order/LogisticsAnalyse' 
    import dayjs from "dayjs";
    import cesTable from "@/components/Table/table.vue";
    import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";

    import { formatmoney, formatPercen, getUrlParam, platformlist, formatPlatform, formatTime, setStore, getStore, formatLinkProCode,formatNoLink } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";

    import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
    import orderLogisticsPage from "@/views/order/logisticsWarning/orderLogisticsPage.vue";
    import YhCityselector from '@/components/YhCom/yh-cityselector.vue';
   

    const tableCols = [    
        { istrue: true, prop: 'expressCompany', label: '快递公司',minwidth:'180', sortable: 'custom' },
        { istrue: true, prop: 'delayProvince', label: '滞留省',minwidth:'140', sortable: 'custom' },
        { istrue: true, prop: 'delayCity', label: '滞留市',minwidth:'140', sortable: 'custom' },        
        { istrue: true, prop: 'opName', label: '状态', minwidth: '100', sortable: 'custom' },       
        { istrue: true, prop: 'delayType', label: '滞留类型', minwidth: '110', sortable: 'custom', formatter: (row) => {return row.delayTypeText; }},
        { istrue: true, prop: 'orderCount', label: '订单数', minwidth: '100', sortable: 'custom' ,  type: 'clickLink', handle: (that, row) => that.showOrderDetailList(row)   },
        { istrue: true, prop: 'delayHoursAvg', label: '平均停留时长(小时)', minwidth: '160', sortable: 'custom'  },
    ];

   
    const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
    const endTime = formatTime(new Date(), "YYYY-MM-DD");
    const star = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");

    export default {
        name: "OrderLogisticsDelayList",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable,vxetablebase, OrderActionsByInnerNos, orderLogisticsPage , YhCityselector},
        data() {
            return {
                that: this,
                Filter: {
                    delayType:99,  
                    gDate: [
                        formatTime(dayjs().subtract(14, "day"), "YYYY-MM-DD"),
                        formatTime(new Date(), "YYYY-MM-DD"),
                    ],     
                    timeType:1             
                },
                lastFilter:{},
                summaryarry:{},
                groupProps:["DelayProvince","DelayCity","ExpressCompany","OpName","DelayType"],
                keywordsTip:'可查内容：省、市、单号、快递号、物流内容'     ,   
                expressCompanys:[],  
                delayTypes:[],           
                tbdatalist: [],
                tableCols: tableCols,
                total: 0,               
                pager: { OrderBy: "", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
              
                curRow: {},   
                
               

                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一月',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三月',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                        }
                    }]
                },

            };
        },
        computed:{
            calcTableCols(){
                var rltCols=this.tableCols.filter(x=> { 
                    if(x.prop=="orderCount" || x.prop=="delayHoursAvg" )
                        return true;
                    
                    if(this.groupProps!=null && this.groupProps.length>0){
                        let tempP=this.groupProps.find(y=> y.toUpperCase()== x.prop.toUpperCase());
                        if(tempP )
                            return true;
                    }

                    return false;
                });

                console.log(rltCols);
                return rltCols;
            }
        },
        async mounted() {           
            let fds=await FilterDatas();
            if(fds && fds.success){
                this.expressCompanys=fds.data.companys;
                this.delayTypes=fds.data.delayTypes;
            }

            this.onSearch();
        },
        methods: {             
            groupPropsChange(){     
                this.$nextTick(()=>{
                    this.$refs["xTable"].$refs.xTable.resetColumn(true);
                    //this.$refs["xTable"].$refs.xTable.recalculate();
                    this.onSearch();
                });     
            }  ,
            showOrderDetailList (row) {
                let self=this;   
                let fltArgs={...this.lastFilter};  
                //匹配的过滤条件带入
                if(this.groupProps!=null && this.groupProps.length>0){
                    this.calcTableCols.forEach(x=>{
                        let tempP=this.groupProps.find(y=> y.toUpperCase()== x.prop.toUpperCase());
                        if(tempP ){
                            fltArgs[x.prop]=row[x.prop];
                        }                            
                    });
                }
                
                this.$showDialogform({
                    path:`@/views/order/orderillegal/OrderLogisticsDelayDetailList.vue`,
                    title:'订单详情',
                    args:fltArgs,
                    width:'95%',
                    height:'700px',                    
                });   

            },      
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderField = column.prop;                   

                    this.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                }
                this.onSearch();
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.gettbdatalist1();
            },
            async gettbdatalist1() {
                if (this.Filter.gDate) {
                    this.Filter.opTimeStart = this.Filter.gDate[0];
                    this.Filter.opTimeEnd = this.Filter.gDate[1];
                }
                else {
                    this.Filter.startGDate = null;
                    this.Filter.endGDate = null;

                }
                const para = { ...this.Filter };
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...para,
                };

                params.groupProps=this.groupProps;

                this.lastFilter={...para};

                this.listLoading = true;
                const res = await PageCalcOrderLogisticsDelay(params);

                this.listLoading = false;

                this.total = res.data.total
                this.tbdatalist = res.data.list;
                this.summaryarry=res.data.summary;

            },
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
<style >
    .el-image__inner {
        max-width: 50px;
        max-height: 50px;
    }

    .numberNoLeftPadding20230223 input{
        padding-left: 1px !important;
        padding-right: 30px !important;
    }
</style>