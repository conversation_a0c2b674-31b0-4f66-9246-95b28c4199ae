<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.goodsCompeteId" clearable placeholder="竞品ID" style="width:120px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.goodsCompeteName" clearable placeholder="竞品标题" style="width:120px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.yyGroupName" clearable placeholder="运营组" style="width:90px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.sampleUserName" clearable placeholder="提交人" style="width:90px;" />
                    </el-button>

                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="()=>{Filter={};}">清空条件</el-button>

                </el-button-group>
            </el-form>
        </template>

        <!--列表-->
        <!-- <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' 
        @sortchange='sortchange' :tableData='tbdatalist' @select='selectchange' 
        :isSelection='false' :isSelectColumn="true" :tableCols='tableCols' 
        :loading="listLoading" rowkey="id" :treeprops="{children: 'dtlSkuOrderList', hasChildren: true}">
            <el-table-column width="100" label="操作" fixed="right">
                <template slot-scope="scope">
                    <el-button type="text" v-if="!(scope.row.isGoodsCompete==1)" @click="onEditSkuOrder(scope.row,true)">编辑</el-button>
                    <el-button type="text" v-if="!(scope.row.isGoodsCompete==1)" @click="onEditSkuOrder(scope.row,false)">查看</el-button>
                </template>
            </el-table-column>
        </ces-table> -->

        <vxetablebase :id="'hotsalegoodsskuorderlist20221212'"
            :tableData='tbdatalist' :tableCols='tableCols' 
            :treeProp="{ rowField: 'id', parentField: 'hotSaleGoodsChooseId' }"
             @sortchange='sortchange'
            >
                <template slot="right">
                    <vxe-column title="操作"  :field="'col'+(tableCols.length+1)"  width="90" fixed="right">
                        <template #default="{ row }">
                            <el-button type="text" v-if="!(row.isGoodsCompete==1)" @click="onEditSkuOrder(row,true)">编辑</el-button>
                            <el-button type="text" v-if="!(row.isGoodsCompete==1)" @click="onEditSkuOrder(row,false)">查看</el-button>
                        </template>
                    </vxe-column>
                </template>

            </vxetablebase>


        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettbdatalist1" />
        </template>

        <el-dialog title="新品采样" :visible.sync="dialogSkuVisible" width='90%' :close-on-click-modal="false" v-dialogDrag v-loading="dialogSkuLoading" element-loading-text="拼命加载中">
            <skuOrderPage4Enquiry ref="skuOrderPage4Enquiry" style="z-index:1000;" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogSkuVisible = false">取 消</el-button>

                    <el-button type="primary" @click="onSaveSkuOrder()" :loading="skuSaveLoading" v-if="skuSaveHiddle">保 存</el-button>
                </span>
            </template>
        </el-dialog>

    </my-container>
</template>
<script>  

    import {
        pageHotSaleGoodsChooseForSkuOrderAsync, getSkuOrderAsync, saveSkuOrderAsync
    } from '@/api/operatemanage/productalllink/alllink'
    import dayjs from "dayjs";
    import cesTable from "@/components/Table/table.vue";

    import { formatmoney, formatPercen, getUrlParam, platformlist, formatPlatform, formatTime, setStore, getStore, formatLinkProCode } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    import * as echarts from 'echarts';
    import buschar from '@/components/Bus/buschar';
    import skuOrderPage4Enquiry from '@/views/operatemanage/productalllink/skuenquiry/hotsalegoodskuorder4enquiry.vue';

    import vxetablebase from "@/components/VxeTable/vxetablebase.vue";

    //采样状态
    function formatSampleOrderState(val) {

        //采样状态 0无状态、10已登记、20已下单、30已到货
        switch (val) {
            case 0:
                return "已登记";
            case 10:
                return "已登记";
            case 20:
                return "已下单";
            case 30:
                return "已到货";
        }

        return "";
    }

    //对样结果
    function formatSampleCompareState(sts) {
        //对样结果 0无状态、10没问题、-10有问题     

        if (sts && sts != 0) {
            switch (sts) {
                case 0:
                    return "";
                case 10:
                    return "没问题";
                case -10:
                    return "有问题";
            }
            return "";
        }
        else {
            return "";
        }
    }


    const tableCols = [

        { istrue: true, prop: 'goodsCompeteId', label: '竞品ID', width: '160', sortable: 'custom',
        treeNode:true, fixed:'left', 
        type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.goodsCompeteId) },
        { istrue: true, prop: 'goodsCompeteImgUrl', label: '竞品图片', width: '80', type: 'images' },
        { istrue: true, prop: 'goodsCompeteName', label: '竞品标题', width: '220', sortable: 'custom', fixed:'left' },
        { istrue: true, prop: 'groupName', label: '运营组', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'sampleUserName', label: '提交人', width: '80' },
        { istrue: true, prop: 'createdTime', label: '提交时间', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'sampleSource', label: '采样来源', width: '80' },

        { istrue: true, prop: 'sampleOrderState', label: '采样状态', width: '80', formatter: (row) => formatSampleOrderState(row.sampleOrderState) },
        { istrue: true, prop: 'sampleOrderTime', label: '下单时间', width: '150' },
        { istrue: true, prop: 'sampleArrivalTime', label: '到货时间', width: '150' },


        { istrue: true, prop: 'factoryName', label: '厂家', width: '80' },
        { istrue: true, prop: 'factoryUrl', label: '厂家链接', width: '80' },
        { istrue: true, prop: 'skuName', label: 'SKU名称', width: '160' },
        { istrue: true, prop: 'price', label: '价格', width: '80' },
        { istrue: true, prop: 'quantity', label: '数量', width: '80' },
        { istrue: true, prop: 'expressNum', label: '快递单号', width: '80' },
        { istrue: true, prop: 'sampleWeight', label: '重量kg', width: '80' },
        // { istrue: true, prop: 'sampleVolume', label: '样品体积cm³', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'sampleLength', label: '长度cm', width: '100' },
        { istrue: true, prop: 'sampleWidth', label: '宽度cm', width: '100' },
        { istrue: true, prop: 'sampleHeigdth', label: '高度cm', width: '100' },

        { istrue: true, prop: 'sampleImgUrl', label: '产品图片', width: '80', type: 'images' },
        { istrue: true, prop: 'packingImgUrl', label: '包装图片', width: '80', type: 'images' },

        { istrue: true, prop: 'packingWeight', label: '包装重量kg', width: '100', },
        { istrue: true, prop: 'packingLength', label: '包装长度cm', width: '100', },
        { istrue: true, prop: 'packingWidth', label: '包装宽度cm', width: '100', },
        { istrue: true, prop: 'packingHeigdth', label: '包装高度cm', width: '100', },



        // { istrue: true, prop: 'sampleMaterial', label: '样品材质', width: '120', sortable: 'custom' },

        { istrue: true, prop: 'sampleCompareState', label: '对样结果', width: '80', formatter: (row) => formatSampleCompareState(row.sampleCompareState) },
    ];

    const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
    const endTime = formatTime(new Date(), "YYYY-MM-DD");
    const star = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");

    export default {
        name: "hotsalegoodsskuorderlist",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, skuOrderPage4Enquiry, cesTable,vxetablebase, buschar },
        data() {
            return {
                that: this,
                Filter: {
                },
                platformlist: platformlist,
                tbdatalist: [],
                tableCols: tableCols,
                total: 0,
                summaryarry: { count_sum: 10 },
                pager: { OrderBy: "", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                //
                selids: [],
                fileList: [],
                dialogSkuVisible: false,
                dialogSkuLoading: false,
                skuSaveLoading: false,
                skuSaveHiddle: false,
                dialogTmCmRefInfoVisible: false,
                selfInfo: {

                },
                dialogPddCmRefInfoVisible: false,
                dialogPddCmLoading: false,
            };
        },
        async mounted() {

            const userInfoName = "hotsalegoods_selfuserinfo";
            let selfInfo4Store = getStore(userInfoName);
            if (selfInfo4Store) {
                this.selfInfo = selfInfo4Store;
            }

            this.onSearch();
        },
        methods: {
            formatSampleCompareState: formatSampleCompareState,
            formatSampleOrderState: formatSampleOrderState,
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderField = column.prop;
                    var bFields = [];// ['skuDataState', 'skuDataTime', 'cmRefInfoState', 'cmRefInfoLastOkTime'];
                    if (orderField == "orgPlatformName") {
                        orderField = "PlatformName";
                    } else if (bFields.indexOf(orderField) > -1) {
                        orderField = "b." + orderField;
                    }

                    this.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                }
                this.onSearch();
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.gettbdatalist1();
            },
            async gettbdatalist1() {
                if (this.Filter.gDate) {
                    this.Filter.startGDate = this.Filter.gDate[0];
                    this.Filter.endGDate = this.Filter.gDate[1];
                }
                else {
                    this.Filter.startGDate = null;
                    this.Filter.endGDate = null;

                }
                const para = { ...this.Filter };
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...para,
                };

                this.listLoading = true;
                const res = await pageHotSaleGoodsChooseForSkuOrderAsync(params);

                this.listLoading = false;

                this.total = res.data.total;

                let dtList=[];
                res.data.list.forEach(x=>{
                    dtList.push(x);
                    if(x.dtlSkuOrderList && x.dtlSkuOrderList.length>0){
                        dtList=dtList.concat([...x.dtlSkuOrderList]);
                    }
                });

                this.tbdatalist = dtList;


            },

            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },

            //查看、编辑竞品SKU采样订单
            onEditSkuOrder(row, isEdit) {
                this.skuSaveHiddle = isEdit;
                this.dialogSkuVisible = true;
                this.$nextTick(() => {
                    this.$refs.skuOrderPage4Enquiry.getSkuOrderData(row.id, isEdit,true);
                });
            },
            //保存SKU
            async onSaveSkuOrder() {
                this.skuSaveLoading = true;

                await this.$refs.skuOrderPage4Enquiry.saveSkuOrderData();
                this.skuSaveLoading = false;

                this.dialogSkuVisible = false;
                this.onRefresh();

            }
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
<style >
    .el-image__inner {
        max-width: 50px;
        max-height: 50px;
    }
</style>