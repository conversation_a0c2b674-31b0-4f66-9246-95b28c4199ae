<template>
    <div>
        <!-- 基础信息 -->
        <div class="des-box">
            <el-descriptions title="基础信息" :column="3" size="medium" :colon="false">
                <template slot="extra" v-if="!isEdit">
                    <i @click="toggleContent()" class="el-icon-d-arrow-right"
                        :class="{ arrowTransform: !isOpen, arrowTransformReturn: isOpen }"></i>
                    <!-- <el-button type="info" circle plain icon="el-icon-plus" size="mini" @click="toggleContent"></el-button> -->
                </template>
                <el-descriptions-item label="" span="3" v-if="isEdit">
                    <el-form :model="ruleForm" :rules="rules" ref="baseForm" label-width="110px" class="ruleForm">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="姓名" prop="name">
                                    <el-input v-model="ruleForm.name" placeholder="请输入姓名" maxlength="10"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="性别" prop="gender">
                                    <el-select v-model="ruleForm.gender" placeholder="请选择性别" style="width: 100%;">
                                        <el-option label="男" :value="1"></el-option>
                                        <el-option label="女" :value="2"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="出生日期" prop="birthDate">
                                    <el-date-picker clearable value-format="yyyy-MM-dd" v-model="ruleForm.birthDate"
                                        type="date" style="width: 100%;" placeholder="选择日期" @change="changeBirthday">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="学历" prop="education">
                                    <el-select v-model="ruleForm.education" placeholder="请选择学历"  style="width: 50%;">
                                        <el-option label="高中" value="高中"></el-option>
                                        <el-option label="中专" value="中专"></el-option>
                                        <el-option label="大专" value="大专"></el-option>
                                        <el-option label="本科" value="本科"></el-option>
                                        <el-option label="硕士" value="硕士"></el-option>
                                        <el-option label="博士" value="博士"></el-option>
                                    </el-select>
                                    <el-select v-model="ruleForm.educationType" placeholder="学历类型" style="width: 50%;">
                                        <el-option label="全日制" value="全日制"></el-option>
                                        <el-option label="非全日制" value="非全日制"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                           
                            <el-col :span="8">
                                <el-form-item label="毕业院校" prop="graduationSchool">
                                    <el-input v-model="ruleForm.graduationSchool" placeholder="请输入毕业院校" maxlength="20"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="毕业日期" prop="graduationDate">
                                    <el-date-picker type="date" placeholder="选择日期" value-format="yyyy-MM-dd" clearable
                                        v-model="ruleForm.graduationDate" style="width: 100%;" ></el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="专业" prop="major">
                                    <el-input v-model="ruleForm.major" placeholder="请输入专业" maxlength="20"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="是否提供毕业证" prop="isGraduationCertificateProvided">
                                    <el-select v-model="ruleForm.isGraduationCertificateProvided" placeholder="请选择是否提供毕业证"
                                        style="width: 100%;">
                                        <el-option label="是" :value="true"></el-option>
                                        <el-option label="否" :value="false"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="年龄" prop="age">
                                    <el-input v-model="ruleForm.age" disabled placeholder="请输入年龄"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="16">
                                <el-form-item label="身份证" prop="idNumber">
                                    <el-input v-model="ruleForm.idNumber" maxlength="20"
                                        placeholder="请输入身份证号"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="身份证有效期" prop="idExpirationDate">
                                    <el-date-picker type="date" clearable value-format="yyyy-MM-dd" placeholder="选择日期"
                                        v-model="ruleForm.idExpirationDate" style="width: 100%;"></el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="联系电话" prop="phone">
                                    <el-input v-model="ruleForm.phone" placeholder="请输入联系电话" maxlength="20"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                       
                            <!-- <el-col :span="12">
                                <el-form-item label="银行卡" prop="bankAccountNumber">
                                    <el-input v-model="ruleForm.bankAccountNumber"
                                         placeholder="请输入银行卡" maxlength="30"></el-input>
                                </el-form-item>
                            </el-col> -->
                            <!-- <el-row>
                            <el-col :span="12">
                                <el-form-item label="银行" prop="bank">
                                    <el-input v-model="ruleForm.bank" placeholder="请输入银行"  maxlength="20"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="开户信息" prop="bankAccountInformation">
                                    <el-input v-model="ruleForm.bankAccountInformation" placeholder="请输入开户信息" maxlength="30"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="持卡人姓名" prop="cardholderName">
                                    <el-input v-model="ruleForm.cardholderName" placeholder="请输入持卡人姓名" maxlength="20"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row> -->
                        <!-- <el-row>
                            <el-col :span="8">
                                <el-form-item label="户口所在地" prop="hukouAddress">
                                    <el-input v-model="ruleForm.hukouAddress" placeholder="请输入户口所在地" maxlength="100"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="现居住地" prop="nowAddress">
                                    <el-input v-model="ruleForm.nowAddress" placeholder="请输入现居住地" maxlength="100"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row> -->
                        <!-- <el-row>
                            <el-col :span="8">
                                <el-form-item label="紧急联系人" prop="emergencyContact">
                                    <el-input v-model="ruleForm.emergencyContact" placeholder="请输入紧急联系人" maxlength="20"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="关系" prop="emergencyContactRelationship">
                                    <el-input v-model="ruleForm.emergencyContactRelationship" placeholder="请选择与紧急联系人的关系"
                                        style="width: 100%;" maxlength="10">
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="联系方式" prop="emergencyContactPhone">
                                    <el-input v-model="ruleForm.emergencyContactPhone" maxlength="20"
                                        placeholder="请输入紧急联系人联系方式"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row> -->
                    </el-form>
                </el-descriptions-item>
                <el-descriptions-item label="" span="3" v-else>
                    <el-descriptions title="" :column="3" size="medium" :colon="false">
                        <el-descriptions-item label="姓名：">{{ candidateInfo.name }}</el-descriptions-item>
                        <el-descriptions-item label="性别：">{{ candidateInfo.gender==null?'':candidateInfo.gender==1 ? '男' : '女' }}</el-descriptions-item>
                        <el-descriptions-item label="出生日期：">{{ candidateInfo.birthDate }}</el-descriptions-item>
                        <el-descriptions-item label="户口所在地：">{{ candidateInfo.hukouAddress }}</el-descriptions-item>
                        <el-descriptions-item label="现居住地：">{{ candidateInfo.nowAddress }}</el-descriptions-item>
                        <el-descriptions-item label="联系方式：">{{ candidateInfo.phone }}</el-descriptions-item>
                        <el-descriptions-item label="学历：">{{ candidateInfo.education }}</el-descriptions-item>
                        <el-descriptions-item label="毕业院校：">{{ candidateInfo.graduationSchool }}</el-descriptions-item>
                        <el-descriptions-item label="毕业日期：">{{ candidateInfo.graduationDate }}</el-descriptions-item>
                        <el-descriptions-item label="" span="3">
                            <el-collapse v-model="activeName">
                                <el-collapse-item name="content">
                                    <el-descriptions title="" :column="3" size="medium" :colon="false">
                                        <el-descriptions-item label="健康：">{{ candidateInfo.healthStatus
                                        }}</el-descriptions-item>
                                        <el-descriptions-item label="籍贯：">{{ candidateInfo.hukou }}</el-descriptions-item>
                                        <el-descriptions-item label="民族：">{{ candidateInfo.ethnicity
                                        }}</el-descriptions-item>
                                        <el-descriptions-item label="婚姻状况：">{{ candidateInfo.maritalStatus ? '已婚' : '未婚'
                                        }}</el-descriptions-item>
                                        <el-descriptions-item label="生育状况：" span="2">{{ candidateInfo.childrenQty==1?'已育':candidateInfo.childrenQty==0?'未育':''
                                        }}</el-descriptions-item>
                                        <el-descriptions-item label="紧急联系人：">{{ candidateInfo.emergencyContact
                                        }}</el-descriptions-item>
                                        <el-descriptions-item label="联系电话：" span="2">{{ candidateInfo.emergencyContactPhone
                                        }}</el-descriptions-item>
                                        <el-descriptions-item label="家庭联系人：" span="1">{{ candidateInfo.familyMemberList &&
                                            candidateInfo.familyMemberList[0].relationship }}</el-descriptions-item>
                                        <el-descriptions-item label="家庭联系人电话：" span="2">{{ candidateInfo.familyMemberList &&
                                            candidateInfo.familyMemberList[0].contact }}</el-descriptions-item>
                                        <el-descriptions-item label="公司是否有熟人：" span="3">{{
                                            candidateInfo.hasAcquaintanceInCompany ? '有' : '无' }}</el-descriptions-item>
                                        <el-descriptions-item label="关键字：" span="3">{{ candidateInfo.tags
                                        }}</el-descriptions-item>
                                    </el-descriptions>
                                </el-collapse-item>
                            </el-collapse>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-descriptions-item>
            </el-descriptions>
        </div>
        <el-divider></el-divider>
    </div>
</template>
  
<script>
import { validatePhoneTwo, validateIdNo } from "@/utils/validate";
export default {
    props: {
        isEdit: {
            type: Boolean,
            default: () => { return false; }
        },
        candidateInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
    },
    data () {
        return {
            activeName: '',
            isOpen: false,
            // isEdit: false,
            ruleForm: {
                name: null,
                gender: null,
                age: null,
                education: null,
                educationType:null,
                graduationSchool: null,
                graduationDate: null,
                major: null,
                isGraduationCertificateProvided: null,
                birthDate: null,
                idNumber: null,
                idExpirationDate: null,
                phone: null,
                // bankAccountNumber: null,
                // cardholderName:null,
                // bank: null,
                // bankAccountInformation: null,
                // hukouAddress: null,
                // nowAddress: null,
                // emergencyContact: null,
                // emergencyContactRelationship: null,
                // emergencyContactPhone: null,
            },
            rules: {
                name: [
                    { required: true, message: '请输入姓名', trigger: 'blur' }
                ],
                gender: [
                    { required: true, message: '请选择性别', trigger: 'change' }
                ],
                age: [
                    { required: true, message: '请输入年龄', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value < 1 || value > 100) {
                                return callback(new Error("年龄范围为1-100,请选择正确出生日期。"));
                            } else {
                                callback();
                            }
                        },
                        trigger: "change",
                    },
                ],
                education: [
                    { required: true, message: '请选择学历', trigger: 'change' }
                ],
                graduationSchool: [
                    { required: true, message: '请输入毕业院校', trigger: 'blur' }
                ],
                graduationDate: [
                    { required: true, message: '请选择毕业日期', trigger: 'change' }
                ],
                birthDate: [
                    { required: true, message: '请选择出生日期', trigger: 'change' }
                ],
                idNumber: [
                    // {
                    //     validator: validateIdNo,//身份证验证
                    //     trigger: "blur"
                    // }
                ],
                phone: [
                    { required: true, message: '请输入联系电话', trigger: 'blur' },
                    // {
                    //     validator: validatePhoneTwo,
                    //     trigger: "blur"
                    // }
                ],
                // bankAccountNumber: [
                //     { required: true, message: '请输入银行卡', trigger: 'blur' }
                // ],
                // hukouAddress: [
                //     { required: true, message: '请输入户口所在地', trigger: 'blur' }
                // ],
                // nowAddress: [
                //     { required: true, message: '请输现在居住地', trigger: 'blur' }
                // ],
                // emergencyContact: [
                //     { required: true, message: '请输入紧急联系人', trigger: 'blur' }
                // ],
                // emergencyContactRelationship: [
                //     { required: true, message: '请选择关系', trigger: 'change' }
                // ],
                // emergencyContactPhone: [
                //     { required: true, message: '请输入联系方式', trigger: 'blur' },
                //     // {
                //     //     validator: validatePhoneTwo,
                //     //     trigger: "blur"
                //     // }
                // ],
            }
        }
    },
    mounted () {
        for (const prop in this.candidateInfo) {
            if (prop in this.ruleForm) {
                this.ruleForm[prop] = this.candidateInfo[prop];
            }
        }
        this.changeBirthday(this.ruleForm.birthDate);
    },
    methods: {
        reset () {
            this.ruleForm = {
                name: null,
                gender: null,
                age: null,
                education: null,
                educationType:null,
                graduationSchool: null,
                graduationDate: null,
                major: null,
                isGraduationCertificateProvided: null,
                birthDate: null,
                idNumber: null,
                idExpirationDate: null,
                phone: null,
                // bankAccountNumber: null,
                // bank: null,
                // bankAccountInformation: null,
                // hukouAddress: null,
                // nowAddress: null,
                // emergencyContact: null,
                // emergencyContactRelationship: null,
                // emergencyContactPhone: null,
            }
        },
        // 计算年龄
        changeBirthday (data) {
            if (data) {
                var date = new Date();
                let year = date.getFullYear();
                this.ruleForm.age = year - data.slice(0, 4)
            }
        },
        toggleContent () {
            this.isOpen = !this.isOpen
            this.activeName = this.isOpen ? 'content' : ''
        },
        //提交
        submitForm (formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    alert('submit!');
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
    }
}
</script>
  
<style scoped>
.title {
    cursor: pointer;
}

.ruleForm {
    padding: 0px;
}

.des-box {
    padding: 0 10px 0 30px
}

::v-deep .el-descriptions__title {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
}

::v-deep .el-descriptions__header {
    margin-bottom: 10px;
}

::v-deep .el-divider--horizontal {
    margin: 5px 0;
}

/* ::v-deep .el-collapse-item__header.is-active {
    display: none;
} */
::v-deep .el-collapse-item__wrap {
    border-bottom: none;
}

::v-deep .el-collapse {
    border: none;
}

::v-deep .el-collapse-item__header {
    display: none;
}

::v-deep .el-collapse-item__content {
    padding-bottom: 0;
}

.arrowTransformReturn {
    transition: 0.2s;
    transform-origin: center;
    transform: rotateZ(90deg);
}

.arrowTransform {
    transition: 0.2s;
    transform-origin: center;
    transform: rotateZ(0deg);
}
</style>