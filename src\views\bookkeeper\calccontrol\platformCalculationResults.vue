<template>
    <container>
        <template #header>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            @select='selectchange' :isSelection='false' :isSelectColumn='true' :tableData='list' :tableCols='tableCols'
            :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry' :loading="listLoading" />
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sel.length" @get-page="getlist" />
        </template>
    </container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatPlatform } from "@/utils/tools";
import { GetPlatformCalculationResults, exportPlatformCalculationResults } from '@/api/monthbookkeeper/financialDetail'
import container from '@/components/my-container/noheader'
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";

const tableCols = [
    { istrue: true, prop: 'yearMonthType', label: '月份类型', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'platform', label: '平台', sortable: 'custom', width: '100', formatter: row => formatPlatform(row.platform) },
    { istrue: true, prop: 'amountSettlement', label: '结算收入', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amountSettlement_1', label: '结算收入1', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amountCost', label: '结算成本', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amountEmptyId', label: '空白链接成本', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'amountReSendCost', label: '补发成本', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amountExceptionCost', label: '异常成本', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amontFreightfee', label: '采购运费', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'xiaoTuiFanHuan', label: '销退仓返还成本', sortable: 'custom', width: '140' },
    { istrue: true, prop: 'dingZhiKuanTotalCost', label: '定制成本', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'cashRed_Tx', label: '淘系红包', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'dkTotalAmont', label: '账单费用', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amontPick', label: '产品运费', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amontSampleBX', label: '样品费', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amontSampleGrop', label: '运营拿样', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amontSampleMG', label: '美工拿样', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amontShoot', label: '拍摄道具费', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'amontCommissionXMT', label: '新媒体提成', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'amontCommissionCG', label: '采购提成', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'freightFee', label: '快递费', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'freightFeeAvg', label: '快递费均摊', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'packageFee', label: '包装费', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'packageFeeAuct', label: '真实包装费', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'packageFeePredict', label: '预估包装费', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'packageFeeShare', label: '分摊包装费', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'chuCangYg', label: '预估出仓成本', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'chuCangZs', label: '真实出仓成本', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'chuCang', label: '出仓成本', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'chuCangYunYing', label: '出仓成本（运）', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'wareWages', label: '仓库薪资', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'wareWagesYunYing', label: '仓库薪资（运）', sortable: 'custom', width: '160' },
];
const tableHandles = [
    { label: "导出", handle: (that) => that.onExport() },
]
export default {
    name: 'platformCalculationResults',
    components: { cesTable, container, vxetablebase, MyConfirmButton },
    props: {
        filter: {}
    },
    data() {
        return {
            that: this,
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "yearMonthType", IsAsc: false },
            total: 0,
            sel: [],
            listLoading: false,
            summaryarry: null,
        }
    },
    mounted() {

    },
    beforeUpdate() { },
    methods: {
        onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }

            if (!params.yearMonth) {
                this.$message({ message: "请选择月份", type: "warning" });
                return;
            }
            this.listLoading = true
            const res = await GetPlatformCalculationResults(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res.data?.total ?? 0
            const data = res.data?.list
            this.summaryarry = res.data?.summary;
            data?.forEach(d => {
                d._loading = false
            })
            this.list = data
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async onExport(){
            let pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }
            const res = await exportPlatformCalculationResults(params);
            if (!res?.data) return;
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            aLink.setAttribute("download", '平台计算结果_' + new Date().toLocaleString() + '.xlsx');
            aLink.click();
        }
    }
}
</script>
