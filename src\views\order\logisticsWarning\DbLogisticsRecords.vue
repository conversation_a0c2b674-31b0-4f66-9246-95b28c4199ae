<template>
    <my-container v-loading="pageLoading">
        <el-tabs v-if="activeName" v-model="activeName">
            <el-tab-pane v-for="item in expressNoDatas" :label="item.expressNo + ''" :name="item.expressNo + ''">
                <table style="width:100%;">
                    <tbody>
                        <tr style="line-height:30px;">
                            <td colspan="3">
                                <el-link type="primary" :href="'http://www.baidu.com/s?wd=' + activeName"
                                    target="_blank">使用百度查询：{{ activeName }}</el-link>
                            </td>
                            <td style="width:60px;">
                                <span @click="orderByList" style="color:#0094ff;display:inline-block;user-select:none;">
                                    ⬆⬇{{ isOrder ? "正序" : "倒序" }}
                                </span>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div style="width:100%;max-height: 560px;overflow: auto;">
                    <table >                   
                        <tbody v-for="orderLog in item.expressData">
                            <tr style="line-height:30px;">
                                <td style="width:150px;height:20px;">{{ orderLog.expressTime }}</td>
                                <td style="width:100px;height:20px;">{{ orderLog.operationName }}</td>
                                <td style="height:20px;">{{ orderLog.remark }}</td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                    <p v-if="!(item.expressData && item.expressData.length>0)">
                        当前物流单号没有已抓取的物流信息，建议使用百度查询！
                    </p>
                </div>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>

<script>
import MyContainer from '@/components/my-container';

import { GetLogisticsTrackByExpressNoFromDb } from "@/api/order/orderdeductmoney";
export default {
    name: 'DbLogisticsRecords',
    components: { MyContainer },
    data() {
        return {
            activeName: '',
            expressNoDatas: [],
            expressData: [],
            pageLoading: false,
            isOrder: true
        }
    },
    async mounted() {
    },
    methods: {
        async loadData({ expressNos }) {
            this.pageLoading = true;
            const res = await GetLogisticsTrackByExpressNoFromDb({ expressNos: expressNos });
            if (!res?.success) {
                return
            }
            this.expressNoDatas = res.data;
            if (this.expressNoDatas.length > 0) {
                this.activeName = this.expressNoDatas[0].expressNo + '';
                this.expressData = this.expressNoDatas[0].expressData;
            }

            this.pageLoading = false;
        },
        async handleClick(tab, event) {
            this.expressData = this.expressNoDatas[tab.index].expressData;
        },
        orderByList() {
            this.isOrder = !this.isOrder;
            //进行数组排序
            if (this.isOrder) {
                this.sortKeyAsc(this.expressData, "expressTime");
            } else {
                this.sortKeyDesc(this.expressData, "expressTime");
            }
        },
        //正序
        sortKeyAsc(array, key) {
            return array.sort(function (a, b) {
                var x = a[key];
                var y = b[key];
                return ((x < y) ? -1 : (x > y) ? 1 : 0)
            })
        },
        //倒序
        sortKeyDesc(array, key) {
            return array.sort(function (a, b) {
                var x = a[key];
                var y = b[key];
                return ((x > y) ? -1 : (x < y) ? 1 : 0)
            })
        }

    },
}
</script>

<style scoped></style>