<template>
    <my-container v-loading="pageLoading">
        <div class="marginauto">
            <div>
                <div class="danbancss"> 成品图片 <div class="flexrow smaslling">
                        <div id="click-scroll-X">
                            <!-- <i class="el-icon-arrow-left iconsize" @click="leftSlide(12)"  :class="{showcs:(left==true&&oneindex==12)}"></i> -->
                            <div class="scroll_wrapper" style="height: 500px;" ref="wrapperCon12">
                                <div class="scroll_list">
                                    <div class="item" style="display: flex; justify-content: center; align-items: center; border: 1px solid #eee;"
                                        v-for="(i, index) in mainImgList" :key="index">
                                        <div
                                            style="margin: 2px; display: flex; justify-content: center; flex-direction: column; align-items: center;">
                                            <img style="width: 90px; height: 90px; object-fit: contain;"   :src="i.url" @click="imgclick(index)"
                                                :alt="i.name" mode="center" />
                                            <div style="font-size: 14px;" class="overtext">{{i.name}}</div>    
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- <i class="el-icon-arrow-right iconsize" @click="rightSlide(12)" :class="{showcs:(right==true&&oneindex==12)}"></i> -->
                        </div>
                    </div>
                </div>
                <div class="danbancss"> 成品视频 <div class="flexrow smaslling">
                        <div id="click-scroll-X">
                            <!-- <i class="el-icon-arrow-left iconsize" @click="leftSlide(12)"  :class="{showcs:(left==true&&oneindex==12)}"></i> -->
                            <div class="scroll_wrapper" style="height: 151px;" ref="wrapperCon12">
                                <div class="scroll_list">
                                    <div class="item" style="display: flex; justify-content: center; align-items: center; border: 1px solid #eee;"
                                        v-for="(i, index) in mainVedioList" :key="index">
                                        <div  style="margin: 2px; display: flex; justify-content: center; flex-direction: column; align-items: center;">
                                            <!-- <img width="60px" height="60px" :src="i.url" @click="playVideo(i.url)"
                                                :alt="i.name" mode="center" /> -->
                                                <video  style="width: 90px; height: 90px; object-fit: contain;"  :src="i.url"  @click="playVideo(i.url)"></video>
                                            <div  style="font-size: 14px;" class="overtext">{{i.name}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- <i class="el-icon-arrow-right iconsize" @click="rightSlide(12)" :class="{showcs:(right==true&&oneindex==12)}"></i> -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--视频播放-->
        <el-dialog title="视频播放" :visible.sync="dialogVisible" width="50%" @close="closeVideoPlyer" :append-to-body="true">
            <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeVideoPlyer">关闭</el-button>
            </span>
        </el-dialog>
        <el-image-viewer :nodown="false" v-if="showGoodsImage" :initialIndex="imgindex" :url-list="imgList"
            :wrapperClosable="false" :on-close="closeFunc" style="z-index:9999;" />
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import { getPageDetailImgInfo } from '@/api/media/changeimg';
import ElImageViewer from '@/views/media/shooting/imageviewer.vue';
import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
export default {
    props: {
        rowinfo: { type: Number, default: 0 }, timee: { type: Number, default: 0 }
    },
    components: { MyContainer, ElImageViewer, videoplayer },
    data() {
        return {
            pageLoading: false,
            imgindex: 0,
            imgList: [],
            showGoodsImage: false,
            videoplayerReload: false,
            dialogVisible: false,
            videoUrl: null,
            mainImgList:[],
            mainVedioList: []
        };
    },
    watch: {
    },
    created() {
    },
    async  mounted() {
        await this.getlist();
    },
    methods: {
        async getlist() {
            let _this = this;
            _this.pageLoading = true;
            var res = await getPageDetailImgInfo({ taskid: this.rowinfo });
            if (res?.success) {
                _this.mainImgList = res.data.mainImgList || [];
                _this.mainVedioList = res.data.mainVedioList || [];
            }
            _this.pageLoading = false;
        },
        //完成表单界面显示图片
        async imgclick(i) {
            this.imgList = [];
            for (let num in this.mainImgList) {
                this.imgList.push(this.mainImgList[num].url);
            }
            this.imgindex = i;
            this.showGoodsImage = true;
        },
        async closeFunc() {
            this.showGoodsImage = false;
        },
        playVideo(videoUrl) {
            this.videoplayerReload = false;
            this.videoplayerReload = true;
            this.dialogVisible = true;
            this.videoUrl = videoUrl;
        },
        async closeVideoPlyer() {
            this.dialogVisible = false;
            this.videoplayerReload = false;
        },
    },
};
</script>
<style lang="scss" scoped>
.rowstyle {
    margin: 20px 170px;
}

.bancss {
    height: 220px;
    width: 100%;
    // border: 1px solid #eee;
}

.discloum ::-webkit-scrollbar-thumb {
    //   background: rgba(0, 0, 0, 0);
}

.danbancss {
    // height: 130px;
    width: 100%;
    // border: 1px solid #eee;
}

::-webkit-scrollbar-thumb {
    // background: rgba(0, 0, 0, 0);
}

.marginauto {
    margin: 0 50px;
    display: flex;
    flex-direction: column;
    height: 750px;
    overflow-y: auto;
}

.marginauto ::-webkit-scrollbar-thumb {
    //   background: rgba(0, 0, 0, 0);
}

#click-scroll-X {
    display: flex;
    align-items: center;

    .left_btn,
    .right_btn {
        font-size: 2.8rem;
        cursor: pointer;
    }

    .scroll_wrapper {
        width: 420px;
        overflow-y: auto;

        .scroll_list {
            display: flex;
            flex-wrap: wrap;
            overflow-x: auto;
            // justify-content: space-between;
            // overflow: hidden;

            .item {
                // width: 65px;
                // height: 65px;
                display: flex;
                align-items: center;
                justify-content: center;
                // border: 1px solid rgb(223, 223, 223);
                box-sizing: border-box;
                flex-shrink: 0;
            }
        }
    }
}

.hoverBg {
    border-radius: 5px;
    border: 1px solid #da1f1f;
    color: #fff;
    border-radius: 8px;
}

.hoverimg {
    border: 1px solid #eee;
    border-radius: 8px;
}

.smaslling {
    width: 100%;
    margin-top: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.flexrow {
    display: flex;
    flex-direction: row;
}

.doubleitem {
    width: 100%;
    height: 138px;
    display: flex;
    flex-direction: column;
}

.outline {
    display: flex;
    //   overflow-x: hidden;
    flex-wrap: wrap;
    flex-direction: column;
    box-sizing: border-box;
    flex-shrink: 0;
}

.doubleitemm {
    box-sizing: border-box;
    width: 65px;
    height: 65px;
    // flex-shrink: 0;
}

.martop {
    margin-top: 30px;
}

.marauto {
    margin: 20px 30px;
}

.borderstyle {
    border: 1px solid #eee;
}

.content-colorimg {
    height: 65px;
    width: 65px;
}

.hiddcs {
    color: #eee;
}

.showcs {
    color: #409EFF;
    font-weight: 600;
    font-size: 22px;
}

.itemm {
    width: 370px;
    height: 140px;
    // display: flex;
    // align-items: center;
    // justify-content: center;
    // border: 1px solid rgb(223, 223, 223);
    box-sizing: border-box;
    flex-shrink: 0;
}

.disbancss {
    width: 100px;
    height: 100%;
}

.flexaligncenter {
    display: flex;
    flex-direction: column;
    align-items: center;
}
.overtext{
    width: 50px;
    height: 17px;
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: normal;
}
</style>

