<template>
  <MyContainer>
    <vxetablebase :id="'superVisor202501111050'" :tablekey="'superVisor202501111050'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      @summaryClick='onsummaryClick' style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog :visible.sync="orderAnalysisChar.visible" width="80%" v-dialogDrag>
      <div style="height: 600px;" v-loading="orderLoading">
        <buschar v-if="orderAnalysisChar.visible" ref="detailtrendchartref" :analysisData="orderAnalysisChar.data">
        </buschar>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { getProductAdvMultipleDimensions_TXList, exportProductAdvMultipleDimensions_TX, getProductAdv_TXChartList } from '@/api/bookkeeper/reportdayV2'
import buschar from "@/components/Bus/buschar";
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: '100', align: 'center', prop: 'useDate', label: '日期', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'superviseName', label: '运营主管', },
  { sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openChart(row), align: 'center', prop: 'impressions', label: '展现量', },
  { sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openChart(row), align: 'center', prop: 'clicks', label: '点击量', },
  { sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openChart(row), align: 'center', prop: 'useMoney', label: '花费', },
  { sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openChart(row), align: 'center', prop: 'clickThroughRate', label: '点击率', formatter: (row) => !row.clickThroughRate ? " " : (row.clickThroughRate).toFixed(2) + "%", },
  { sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openChart(row), align: 'center', prop: 'averageCostPerClick', label: '平均点击花费', formatter: (row) => !row.averageCostPerClick ? " " : (row.averageCostPerClick), },
  { sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openChart(row), align: 'center', prop: 'costPerThousandImpressions', label: '千次展现花费', tipmesg: '(花费/展现量)*1000', },
  { sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openChart(row), align: 'center', prop: 'directTransactionAmount', label: '直接成交金额', },
  { sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openChart(row), align: 'center', prop: 'indirectTransactionAmount', label: '间接成交金额', },
  { sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openChart(row), align: 'center', prop: 'transactionAmount', label: '总成交金额', },
  { sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openChart(row), align: 'center', prop: 'totalTransactionCount', label: '总成交笔数', },
  { sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openChart(row), align: 'center', prop: 'directTransactionCount', label: '直接成交笔数', },
  { sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openChart(row), align: 'center', prop: 'clickConversionRate', label: '点击转化率', tipmesg: '总成交笔数/点击量', formatter: (row) => !row.clickConversionRate ? " " : (row.clickConversionRate).toFixed(2) + "%", },
  { sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openChart(row), align: 'center', prop: 'returnOnInvestment', label: '投入产出比', tipmesg: '总成交金额/花费', formatter: (row) => !row.returnOnInvestment ? " " : (row.returnOnInvestment.toFixed(2)) , },
  { sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openChart(row), align: 'center', prop: 'totalTransactionCost', label: '总成交成本', tipmesg: '花费/成交笔数', formatter: (row) => !row.totalTransactionCost ? " " : (row.totalTransactionCost.toFixed(2)), },
  { sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openChart(row), align: 'center', prop: 'totalCartCount', label: '总购物车数', },
  { sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openChart(row), align: 'center', prop: 'addToCartCost', label: '加购成本', tipmesg: '花费/总购物车数', formatter: (row) => !row.addToCartCost ? " " : (row.addToCartCost), },
  { sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openChart(row), align: 'center', prop: 'saleAmount', label: '销售金额', },
  { sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openChart(row), align: 'center', prop: 'profit3', label: '利润', },
  { sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openChart(row), align: 'center', prop: 'payIdAdvRate', label: '付费链接广告占比', tipmesg: '花费/销售额', formatter: (row) => !row.payIdAdvRate ? " " : (row.payIdAdvRate) + "%", },
]
export default {
  name: "superVisor",
  components: {
    MyContainer, vxetablebase, buschar
  },
  props: {
    ListInfo: {
      type: Object,
      default: () => { }
    },
  },
  data() {
    return {
      orderLoading: false,
      orderAnalysisChar: { visible: false, title: "", data: {} },
      that: this,
      childListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'useMoney',
        isAsc: false,
        calcType: 1,
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
    }
  },
  async mounted() {
    // await this.getList()
  },
  methods: {
    async fetchChartData(params) {
      this.loading = true;
      const { data, success } = await getProductAdv_TXChartList(params);
      this.loading = false;
      if (!success) return
      data.series.forEach(item => {
        item.itemStyle = {
          normal: {
            label: {
              show: true,
              position: "top",
              textStyle: {
                fontSize: 14,
              },
            },
          },
        };
        item.emphasis = {
          focus: "series",
        };
        item.smooth = false;
      });
      return data; // 返回处理后的数据
    },
    async onsummaryClick(property) {
      const data = await this.fetchChartData({ calcType: 6, ...this.ListInfo });
      if (data) {
        this.orderAnalysisChar.visible = true;
        this.orderAnalysisChar.data = data;
      }
    },
    async openChart(row) {
      const data = await this.fetchChartData({
        calcType: this.childListInfo.calcType,
        startTime: this.ListInfo.startTime,
        endTime: this.ListInfo.endTime,
        productName: this.ListInfo.productName,
        superviseId: row.superviseId,
        groupId: row.groupId
      });
      if (data) {
        this.orderAnalysisChar.visible = true;
        this.orderAnalysisChar.data = data;
      }
    },
    async derivationMethod() {
      this.loading = true
      const { data } = await exportProductAdvMultipleDimensions_TX({ ...this.childListInfo, ...this.ListInfo })
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '淘系广告费主管汇总数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async getList(type) {
      if (type == 'search') {
        this.childListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getProductAdvMultipleDimensions_TXList({ ...this.childListInfo, ...this.ListInfo })
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.useDate = dayjs(item.useDate).format('YYYY-MM-DD')
        })
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.childListInfo.currentPage = 1;
      this.childListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.childListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.childListInfo.orderBy = prop
        this.childListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss"></style>
