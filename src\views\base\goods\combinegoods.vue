<template>
   <!-- <el-tab-pane label="调拨单" name="TransferOrder" style="height:100%;">
            <TransferOrder :filter="filter" ref="TransferOrder" :isHistory="true"></TransferOrder>
        </el-tab-pane>         -->
  <!-- <container style="height:100%;">
        <el-tabs v-model="activeName" style="height:100%;">
          <el-tab-pane label="组合编码" name="tabCombineGoods" style="height:100%;">
              <tabCombineGoods :filter="filter" ref="tabCombineGoods" style="height:97%;"></tabCombineGoods>
          </el-tab-pane>
         
        </el-tabs>
        <template #footer>
        </template>
    
  </container> -->

  <my-container v-loading="pageLoading" style="height: 100%">
        <el-tabs v-model="activeName" style="height: 94%">
          <el-tab-pane label="组合编码" name="tabCombineGoods" style="height:100%;">
              <tabCombineGoods :filter="filter" ref="tabCombineGoods" style="height:100%;"></tabCombineGoods>
          </el-tab-pane>
            <!-- <el-tab-pane label="损耗统计" name="first1" style="height: 100%">
                <lossstatistics ref="lossstatistics" style="height: 100%"></lossstatistics>
            </el-tab-pane>
            <el-tab-pane label="裁剪订单" name="first2" style="height: 100%" lazy>
                <tailororder ref="tailororder" style="height: 100%"></tailororder>
            </el-tab-pane>
            <el-tab-pane label="原材料出库" name="first3" style="height: 100%" lazy>
                <materialoutstore ref="materialoutstore" style="height: 100%"></materialoutstore>
            </el-tab-pane>
            <el-tab-pane label="成品半成品设置" name="first4" style="height: 100%" lazy>
                <tailorlossgoodsset ref="tailorlossgoodsset" style="height: 100%"></tailorlossgoodsset>
            </el-tab-pane> -->
        </el-tabs>
    </my-container>
</template>

<script>
import container from '@/components/my-container/noheader'
import tabCombineGoods from "@/views/base/goods/combinegoodstemplate";
import TransferOrder from "@/views/base/goods/TransferOrder";
export default {
  name: 'Roles',
  components: { container, tabCombineGoods,TransferOrder},
  data() {
    return {
      activeName:"tabCombineGoods",
      that:this,
      filter: {
      },
      pageLoading: false,
    }
  },
  async mounted() {
  },
  methods: {
    //总查询
    async Query(){
      switch(this.activeName){
        case "tabCombineGoods":
          this.$refs.tabCombineGoods.onSearch();
          break;
      }
    },   
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary{
  margin-right: 7px;
  
}
</style>
