<template>
    <my-container>
        <!--顶部操作-->
        <template #header>
            <el-row style="margin-top:-20px;">
                <!-- 商品信息 -->
                <el-col :span="24">
                    <h3>
                        <strong>
                            竞品:{{info.goodsCompeteName}}
                            竞品ID:{{info.goodsCompeteId}}
                            日期：{{ dataStartDate }}|{{dataEndDate}}
                        </strong>
                    </h3>
                </el-col>
            </el-row>
        </template>
        <template>

            <el-row>
                <!-- 趋势图 -->
                <el-col :span="24" style="width:100%">
                    <p></p>
                </el-col>
                <el-col :span="24" style="width:100%">
                    <div :id="'cpIdDtl'+info.goodsCompeteId" style="width:100%;height:260px"></div>
                </el-col>
            </el-row>
            <el-row>
                <!-- 商品统计信息列表 -->
                <el-col :span="24" style="width:100%">
                    <p></p>
                </el-col>
                <el-col :span="24" style="height:160px">
                    <ces-table ref="sumTable" :that='that' :isIndex='false' :tableData='sumTableData' :tableCols='sumTableCols' :loading="listLoading" :isSelectColumn="false">
                    </ces-table>
                </el-col>
            </el-row>
            <el-row>
                <!-- 商品明细信息列表 -->
                <el-col :span="24" style="width:100%">
                    <p><strong>
                            <el-divider content-position="left">每日交易明细</el-divider>
                        </strong></p>
                </el-col>
                <el-col :span="24" style="height:280px">
                    <ces-table ref="dtlTable" :that='that' :isIndex='false' :tableData='dtlTableData' :tableCols='dtlTableCols' :loading="listLoading" :isSelectColumn="false">
                    </ces-table>
                </el-col>
            </el-row>
            <el-row>
                <!-- 流量统计 -->
                <el-col :span="24" style="width:100%">
                    <h3><strong>流量统计</strong></h3>
                </el-col>
            </el-row>
            <el-row>
                <!-- 流量分组图表 -->
                <el-col :span="24">
                    <div :id="'cpIdFlow'+info.goodsCompeteId" style="width:100%;height:260px"></div>
                </el-col>
            </el-row>
            <el-row>
                <!-- 流量列表 -->
                <el-col :span="24" style="height:280px;">
                    <ces-table ref="flowTable" :that='that' :isIndex='false' :tableData='flowTableData' :height='200' :tableCols='flowTableCols' :loading="listLoading" :isSelectColumn="false">
                    </ces-table>
                </el-col>
            </el-row>

        </template>

    </my-container>
</template>
<script>  

    import * as echarts from 'echarts';


    import cesTable from "@/components/Table/table.vue";
    import { formatTime } from "@/utils";
    import MyContainer from "@/components/my-container";

    const baseCols = [
        { istrue: true, prop: 'jyAmount', label: '交易金额', width: '100', sortable: true },
        { istrue: true, prop: 'fkCount', label: '访客人数', width: '100', sortable: true },
        { istrue: true, prop: 'ssCount', label: '搜索人数', width: '100', sortable: true },
        { istrue: true, prop: 'scCount', label: '收藏人数', width: '100', sortable: true },
        { istrue: true, prop: 'jgCount', label: '加购人数', width: '100', sortable: true },
        { istrue: true, prop: 'payerCount', label: '支付人数', width: '100', sortable: true },
        { istrue: true, prop: 'payTrunRatio', label: '支付转化率', width: '100', sortable: true },
        { istrue: true, prop: 'keUnitPrice', label: '客单价', width: '100', sortable: true },
        { istrue: true, prop: 'uvValue', label: 'UV价值', width: '100', sortable: true },
        { istrue: true, prop: 'ssRatio', label: '搜索占比', width: '100', sortable: true },
        { istrue: true, prop: 'scRatio', label: '收藏率', width: '100', sortable: true },
        { istrue: true, prop: 'jgRatio', label: '加购率', width: '100', sortable: true },
    ]
    const sumTableCols = [
        { istrue: true, prop: 'goodsName', label: '商品信息', minwidth: '100', sortable: true },
        { istrue: true, prop: 'dateRange', label: '日期范围', width: '120', sortable: true },//, formatter: (row) =>  formatTime(row.dataTime, 'YYYY-MM-DD') },
        ...baseCols
    ];

    const dtlTableCols = [
        { istrue: true, prop: 'goodsName', label: '商品信息', minwidth: '100', sortable: true },
        { istrue: true, prop: 'dataDate', label: '日期', width: '120', sortable: true, formatter: (row) => formatTime(row.dataDate, 'YYYY-MM-DD') },
        ...baseCols
    ];

    const flowTableCols = [
        { istrue: true, prop: 'goodsName', label: '商品信息', minwidth: '100', sortable: true },
        //{ istrue: true, prop: 'goodsId', label: '商品ID', width: '100', fixed: 'left' },
        { istrue: true, prop: 'dateRange', label: '日期', width: '120', sortable: true },
        { istrue: true, prop: 'flowSource', label: '流量来源', width: '120', sortable: true },
        { istrue: true, prop: 'jyAmount', label: '交易金额', width: '100', sortable: true },
        { istrue: true, prop: 'jyAmountRatio', label: '交易金额占比', width: '100', sortable: true },
        { istrue: true, prop: 'fkCount', label: '访客人数', width: '100', sortable: true },
        { istrue: true, prop: 'payTrunRatio', label: '支付转化率', width: '100', sortable: true },
        { istrue: true, prop: 'payerCount', label: '支付人数', width: '100', sortable: true },
        { istrue: true, prop: 'keUnitPrice', label: '客单价', width: '100', sortable: true },
        { istrue: true, prop: 'uvValue', label: 'UV价值', width: '100', sortable: true },
    ];

    export default {
        name: "cmRefTmPage",
        components: { MyContainer, cesTable },
        data() {
            return {
                that: this,
                info: {
                    shopName: "",
                    categoryName: "",
                    goodsName: "",
                    goodsCompeteId: "",
                    dataStartDate: "",
                    dataEndDate: ""
                },
                sumTableData: [],
                sumTableCols: sumTableCols,
                dtlTableData: [],
                dtlTableCols: dtlTableCols,
                flowTableData: [],
                flowTableCols: flowTableCols,
                listLoading: false
            };
        },
        async mounted() {

        },
        computed: {
            dataStartDate: function () {
                //return "111";
                return formatTime(this.info.dataStartDate, "YYYY-MM-DD");
            },
            dataEndDate: function () {
                return formatTime(this.info.dataEndDate, "YYYY-MM-DD");
                //return "222";
            }
        },
        methods: {
            sortchange: function () {
                //alert('aaaa');
            },
            setInfoData(infoData) {
                var info = this.info;
                info.shopName = infoData.shopName;
                info.categoryName = infoData.categoryName;
                info.goodsName = infoData.goodsName;
                info.goodsCompeteName = infoData.goodsCompeteName;
                info.goodsCompeteId = infoData.goodsCompeteId;
                info.dataStartDate = infoData.dataStartDate;
                info.dataEndDate = infoData.dataEndDate;

                this.sumTableData = infoData.sumTableData;
                this.dtlTableData = infoData.dtlTableData;
                this.flowTableData = infoData.flowTableData;

                this.$nextTick(() => {

                    var dtlEchartId = document.getElementById('cpIdDtl' + info.goodsCompeteId);
                    var dtlEchart = echarts.init(dtlEchartId);
                    dtlEchart.clear();
                    dtlEchart.resize();
                    if (infoData.dtlEchartOpt != null) {
                        dtlEchart.setOption(infoData.dtlEchartOpt);
                    }

                    var flowEchartId = document.getElementById('cpIdFlow' + info.goodsCompeteId);
                    var flowEchart = echarts.init(flowEchartId);
                    flowEchart.clear();
                    flowEchart.resize();
                    if (infoData.flowEchartOpt != null) {
                        flowEchart.setOption(infoData.flowEchartOpt);
                    }


                    window.addEventListener("resize", () => {
                        dtlEchart.resize();
                        flowEchart.resize();
                    });

                })
            }
        }
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>