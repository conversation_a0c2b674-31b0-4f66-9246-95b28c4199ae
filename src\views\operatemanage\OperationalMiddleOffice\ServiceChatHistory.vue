<template>
  <my-container>
    <!--顶部操作-->
    <template #header>
      <div class=".top">
        <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
          <el-form-item label="">
            <el-date-picker style="width: 320px" v-model="Filter.conversationTime" type="datetimerange"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="申请开始日期"
              end-placeholder="申请结束日期" :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
          </el-form-item>
          <el-form-item label=""  v-show="selectedTitles.includes('创建时间')">
            <el-date-picker style="width: 320px" v-model="Filter.createdTime" type="datetimerange" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" range-separator="至" start-placeholder="创建时间开始" end-placeholder="创建时间结束"
              :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
          </el-form-item>
          <el-form-item label="" v-show="selectedTitles.includes('下单日期')">
            <el-date-picker style="width: 320px" v-model="Filter.timerange" type="datetimerange" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" range-separator="至" start-placeholder="下单开始日期" end-placeholder="下单结束日期"
              :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
          </el-form-item>
          <el-form-item label="" v-show="selectedTitles.includes('审核状态')">
            <el-select v-model="Filter.auditState" placeholder="审核状态" style="width:100px" class="el-select-content"
              clearable>
              <el-option v-for="item in reviewStatusList" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="" v-show="selectedTitles.includes('订单状态')">
            <el-select v-model="Filter.orderState" placeholder="订单状态" style="width:100px" class="el-select-content"
              clearable>
              <el-option v-for="item in orderStatusList" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item> -->
          <el-form-item label="" v-show="selectedTitles.includes('售后状态')">
            <el-select v-model="Filter.afterSalesState" placeholder="售后状态" class="el-select-content" filterable
              multiple clearable collapse-tags >
              <el-option v-for="item in afterStatusArr" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="" v-show="selectedTitles.includes('售后类型')">
            <el-select v-model="Filter.afterSales" placeholder="售后类型" class="el-select-content" filterable
              multiple clearable collapse-tags>
              <el-option v-for="item in afterStatusList" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item> -->

          <!-- <el-form-item label="" v-show="selectedTitles.includes('聊天账号')">
            <el-input v-model.trim="Filter.chatAccount" placeholder="聊天账号" style="width:150px" maxlength="20"
              clearable />
          </el-form-item> -->

          <el-form-item label="" v-show="selectedTitles.includes('线上订单号')">
            <el-button-group>
              <inputYunhan :key="'1'" :keys="'one'" :width="'220px'" ref="" :inputt.sync="Filter.orderNoList"
                v-model.trim="Filter.orderNoList" placeholder="线上订单号/若输入多条请摁回车" :clearable="true" @callback="callback"
                title="线上订单号" @entersearch="entersearch" :maxRows="300">
              </inputYunhan>
            </el-button-group>
          </el-form-item>

          <el-form-item label="" v-show="selectedTitles.includes('宝贝ID')">
            <!-- <el-input maxlength="20" v-model.trim="Filter.proId" placeholder="宝贝ID" style="width:150px" clearable /> -->
            <el-button-group>
              <inputYunhan :key="'2'" :keys="'two'" :width="'220px'" ref="" :inputt.sync="Filter.proIdList"
                v-model.trim="Filter.proIdList" placeholder="宝贝ID/若输入多条请摁回车" :clearable="true" @callback="callback2"
                title="宝贝ID" @entersearch="entersearch" :maxRows="300">
              </inputYunhan>
            </el-button-group>
          </el-form-item>

          <el-form-item label="" v-show="selectedTitles.includes('账号使用人')">
            <el-select v-model="Filter.userName" placeholder="账号使用人" class="el-select-content"
              clearable filterable multiple collapse-tags>
              <el-option v-for="item in userNameList" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="" v-show="selectedTitles.includes('分组')">
            <el-select v-model="Filter.groupNameList" placeholder="分组" class="el-select-content" filterable multiple
              clearable collapse-tags >
              <el-option v-for="item in groupNameList" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item> -->
          <!-- <el-form-item label="" v-show="selectedTitles.includes('组长')">
            <el-select v-model="Filter.groupManager" placeholder="组长" style="width:120px" class="el-select-content"
              clearable filterable >
              <el-option v-for="item in groupManagerList" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item> -->
          <el-form-item label="" v-show="selectedTitles.includes('审核人')">
            <el-input maxlength="50" v-model.trim="Filter.initialOperator" placeholder="审核人" clearable />
          </el-form-item>
          <el-form-item label="" v-show="selectedTitles.includes('审核结果')">
            <el-select v-model="Filter.initialAuditType" placeholder="审核结果" style="width:120px"
              class="el-select-content" clearable>
              <el-option v-for="item in firstTrialList" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" v-show="selectedTitles.includes('审核类型')">
            <el-select v-model="Filter.refuseInitialAuditType" placeholder="审核类型"
              class="el-select-content" clearable filterable multiple collapse-tags>
              <el-option v-for="item in statusList" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item label="" v-show="selectedTitles.includes('店铺')">
            <el-select v-model="Filter.shopNameList" placeholder="店铺" class="el-select-content" filterable multiple
              clearable collapse-tags >
              <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.shopName" />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="" v-show="selectedTitles.includes('重复打款')">
            <el-select v-model="Filter.smallAmountCount" placeholder="重复打款" style="width:100px"
              class="el-select-content" clearable>
              <el-option v-for="item in spaymentList" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item> -->
          <el-form-item label="" v-show="selectedTitles.includes('退款原因稽查')">
            <el-select v-model="Filter.reasonForRefund" placeholder="退款原因稽查" class="el-select-content" filterable
              multiple clearable collapse-tags>
              <el-option v-for="item in reasonForRefundList" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="" v-show="selectedTitles.includes('责任人')">
            <el-select v-model="Filter.person" placeholder="责任人" class="el-select-content" clearable filterable>
              <el-option v-for="item in personList" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item> -->
          <el-form-item label="" v-show="selectedTitles.includes('负责运营')">
            <el-input v-model.trim="Filter.directorUserName" placeholder="负责运营" style="width:120px" maxlength="20"
              clearable />
          </el-form-item>

          <el-form-item label="" v-show="selectedTitles.includes('运营小组')">
            <el-select v-model="Filter.directorGroupIdList" placeholder="运营小组" class="el-select-content" filterable
              multiple clearable collapse-tags>
              <el-option v-for="item in directoList" :key="item.key" :label="item.value" :value="item.key" />
            </el-select>
          </el-form-item>

          <!-- <el-form-item label="" v-show="selectedTitles.includes('责任客服')">
            <el-input v-model.trim="Filter.responsibleName" placeholder="责任客服" style="width:120px" maxlength="20"
              clearable />
          </el-form-item> -->
          <el-form-item label="" v-show="selectedTitles.includes('审核日期')">
            <el-date-picker style="width: 320px" v-model="Filter.initialOperatorTime" type="datetimerange"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="审核开始日期"
              end-placeholder="审核结束日期" :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
          </el-form-item>

          <el-form-item label="" v-show="selectedTitles.includes('系列编码')">
            <el-input v-model.trim="Filter.styleCode" placeholder="系列编码" style="width:120px" maxlength="20" clearable />
          </el-form-item>

          <el-form-item label="聊天条数" v-show="selectedTitles.includes('聊天条数')">
            <el-col :span="11">
              <el-input
                placeholder="最小条数" v-model="Filter.chatCountStart" maxlength="4" clearable @input="validateNumber($event, 'chatCountStart')">
              </el-input>
            </el-col>
            <el-col class="line" :span="2">至</el-col>
            <el-col :span="11">
              <el-input placeholder="最大条数" v-model="Filter.chatCountEnd" maxlength="4" clearable @input="validateNumber($event, 'chatCountEnd')">
              </el-input>
            </el-col>
          </el-form-item>
          <!-- <el-form-item label="" v-show="selectedTitles.includes('审核人')">
            <el-input v-model.trim="Filter.assigner" placeholder="分配人" style="width:120px" maxlength="20" clearable />
          </el-form-item> -->
          <el-form-item label="" v-show="selectedTitles.includes('是否解决')">
            <el-select v-model="Filter.isSolved" placeholder="是否解决" class="el-select-content" clearable >
              <el-option key="是" label="是" :value=1 />
              <el-option key="否" label="否" :value=0 />
            </el-select>
          </el-form-item>
          <el-form-item label="" v-show="selectedTitles.includes('上下架状态')">
            <el-select v-model="Filter.status" placeholder="上下架状态" class="el-select-content" filterable
              multiple clearable collapse-tags >
              <el-option key=1 label="上架" value=1 ></el-option>
              <el-option key=3 label="下架" value=3 ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="ID评分" v-show="selectedTitles.includes('ID评分')">
            <el-col :span="11">
              <el-input placeholder="ID评分大于" v-model="Filter.minScore" maxlength="4" clearable @input="validateNumber2($event, 'minScore')">
              </el-input>
            </el-col>
            <el-col class="line" :span="2">至</el-col>
            <el-col :span="11">
              <el-input placeholder="ID评分小于" v-model="Filter.maxScore" maxlength="4" clearable @input="validateNumber2($event, 'maxScore')">
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item label="编码评分" v-show="selectedTitles.includes('编码评分')">
            <el-col :span="11">
              <el-input placeholder="编码评分大于" v-model="Filter.minSeriesNameScore" maxlength="4" clearable @input="validateNumber2($event, 'minSeriesNameScore')">
              </el-input>
            </el-col>
            <el-col class="line" :span="2">至</el-col>
            <el-col :span="11">
              <el-input placeholder="编码评分小于" v-model="Filter.maxSeriesNameScore" maxlength="4" clearable @input="validateNumber2($event, 'maxSeriesNameScore')">
              </el-input>
            </el-col>
          </el-form-item>

          <el-form-item>
            <!-- <el-button type="primary" @click="onSetRule" v-if="checkPermission(':api:operatemanage:operate:setorderprocessrule')">分配配置</el-button> -->
            <el-button type="primary" @click="onSearch1">查询</el-button>
            <el-dropdown style="box-sizing: border-box; margin-left:6px;"
              v-if="checkPermission(['api:customerservice:UnPayOrder:UnPayOrderAfterSalesExport'])" size="mini"
              split-button type="primary" icon="el-icon-share" @command="handleCommand"> 导出
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item class="Batcoperation" style="height: 10px"></el-dropdown-item>
                <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a">列表数据</el-dropdown-item>
                <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="b">聊天数据</el-dropdown-item>
                <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="c">聊天数据带关键词</el-dropdown-item>
                <el-dropdown-item class="Batcoperation" style="height: 10px"></el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button icon="vxe-icon-custom-column" @click="clickToolbar" />
            <el-tooltip content="会话时间查询规则：售后页面能看到以创建时间维度，当天只能看到创建时间4天前的数据，如果后补数据延迟创建只能延后看，例如：
会话是4号，正常入库创建时间是在5号，也就是4号的数据，只能5号+4天,也就是9号能看到，如果会话是4号，部分数据后补入库在6号创建，该部分数据在6号+4天，也就是10号能看到" placement="bottom">
              <el-button style="margin: 0; text-overflow: ellipsis; white-space: nowrap; overflow: hidden; width: 140px;">
                {{ ruleHints }}
              </el-button>
            </el-tooltip>
          </el-form-item>
          <div>数据更新时间: {{ upDataTime }}</div>
        </el-form>
      </div>
    </template>
    <div class="uptime">数据更新时间: {{ upDataTime }}</div>

    <!--列表-->
    <vxetablebase ref="table" :that="that" :isIndex="true" @sortchange="sortchange" :tableData="tableData"
      :showsummary="false" :tableCols="tableCols" :loading="listLoading"
      :tableHandles='tableHandles' @cellStyle="cellStyle" cellStyle>
      <template slot='extentbtn'>
        <el-button-group>
          <el-tooltip content="会话时间查询规则：售后页面能看到以创建时间维度，当天只能看到创建时间4天前的数据，如果后补数据延迟创建只能延后看，例如：
会话是4号，正常入库创建时间是在5号，也就是4号的数据，只能5号+4天,也就是9号能看到，如果会话是4号，部分数据后补入库在6号创建，该部分数据在6号+4天，也就是10号能看到" placement="top">
            <el-button style="margin: 0; text-overflow: ellipsis; white-space: nowrap; overflow: hidden; width: 500px;">
              {{ ruleHints }}
            </el-button>
          </el-tooltip>
        </el-button-group>
      </template>
    </vxetablebase>

    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" @get-page="gettrainplanList" />
    </template>
    <!-- 聊天记录 -->
    <ChartListDialog :v-if="resourcedialogVisible" :isShow="resourcedialogVisible"
      @closeDialog="resourcedialogVisible = false" ref="chartRef">
    </ChartListDialog>
    <!-- 审核 -->
    <FirstDialog :v-if="firstdialogVisible" :isShow="firstdialogVisible" @closeDialog="firstdialogVisible = false"
      ref="firstRef" @upData="onSearch">
    </FirstDialog>

    <!-- 审核结果 -->
    <ResultDialog :v-if="resultdialogVisible" :isShow="resultdialogVisible" @closeDialog="resultdialogVisible = false"
      ref="resultRef">
    </ResultDialog>
    <!-- 订单日志信息 -->
    <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
      v-dialogDrag append-to-body>
      <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo" :isTx="isTx"
        style="z-index:10000;height:600px" />
    </el-dialog>

    <el-dialog :visible.sync="dailyNewspaperToolbar" width="40%" v-dialogDrag>
      <template #title>
        <div class="dialog-header">
          查询条件设置
          <el-button style="margin-left: 10px;" @click="selectAll">全选/取消全选</el-button>
        </div>
      </template>
      <div style="height: 100px;">
        <el-checkbox-group v-model="colOptions" @change="changeOptions">
          <el-checkbox v-for="(item, index) in colSelect" v-if="(item !== '日期' || !item)" :label="item"
            :key="item"></el-checkbox>
        </el-checkbox-group>
      </div>
      <div style="margin-top: 40px;display: flex;justify-content: end;">
        <el-button @click="dailyNewspaperToolbar = false">取消</el-button>
        <el-button type="primary" @click="verifyOptions" v-throttle="3000">确认</el-button>
      </div>
    </el-dialog>

    <el-dialog title="关键词搜索" :visible.sync="keywordSearchdialogVisibleSyj" width="25%" :close-on-click-modal=false
      v-dialogDrag>
      <span>
        <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
          <el-form-item prop="chatContentKeyword" label="聊天记录带关键词:">
            <el-input style="width:80%" clearable v-model="Filter.chatContentKeyword" :maxlength="50"></el-input>
          </el-form-item>
        </el-form>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeGroup">取 消</el-button>
        <el-button type="primary" @click="addKeyword">确 定</el-button>
      </span>

    </el-dialog>

    <el-dialog title="分配人" :visible.sync="createVisible" width="350px" height="70%" @closeDialog="closeCreateDialog"
    v-dialogDrag @close="createCancle">

      <el-form ref="formCreate" :model="formCreate" label-width="100px"  >
        <el-form-item label="分配人：" prop="assigner">
          <el-select v-model="formCreate.assigner" placeholder="分配人" multiple clearable collapse-tags filterable style="width:180px;" >
            <el-option v-for="item in allocaterList" :key="item" :label="item" :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-button type="primary" @click="onDelete" style="margin-right: 5px; flex: right;">一键删除</el-button>
      </el-form>

      <vxetablebase :id="'OrderProcessDialog202410151750'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
        :tableData='tableData2' :tableCols='tableCols2' :isSelection="false" :isSelectColumn="false" @select="selectchange"
        style="width: 100%;margin: 0; height: 400px"
        :height="'100%'" >
      </vxetablebase>

      <div style="text-align: center;">
        <el-button type="primary" @click="createCancle">取消</el-button>
        <el-button type="primary" @click="createSubmit">确认</el-button>
      </div>

    </el-dialog>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import CesTable from "@/components/Table/table.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import ChartListDialog from "@/views/operatemanage/OperationalMiddleOffice/ChartListDialog.vue";
// import ChartListDialog from "@/views/customerservice/chartCheck/chartListDialog.vue";

import FirstDialog from "@/views/operatemanage/OperationalMiddleOffice/FirstDialog.vue";
import ResultDialog from "@/views/operatemanage/OperationalMiddleOffice/ResultDialog.vue";
import ReviewDialog from "@/views/operatemanage/OperationalMiddleOffice/ReviewDialog.vue";
import JudgeDialog from "@/views/operatemanage/OperationalMiddleOffice/JudgeDialog.vue";
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";

import { SetVxeTableColumnCacheAsync, GetVxeTableColumnCacheAsync } from '@/api/admin/business'
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { formatLinkProCode } from "@/utils/tools";
import { getDirectorGroupList, getList as getshopList } from "@/api/operatemanage/base/shop";
import {
  getAfterStatusList,
  getGroupManagerList,
  getGruopNameList,
  getUserNameList,
  // getPersonList
} from "@/api/customerservice/chartCheck";
import { getOrderAfterSalesPageList, exportUnPayOrderAfterSalesPageList, getPersonList,
  setOrderAfterSalesAssigner, getOrderAfterSalesAssigner, deleteOrderAfterSalesAssigner }
from "@/api/operatemanage/OperationalMiddleOfficeManage/ServiceChatHistory";
import { getClaimUserList } from "@/api/operatemanage/OperationalMiddleOfficeManage/BrushOrderProcess";

const tableHandles = [
];

const platformList = [
  { name: "拼多多", value: 2 },
  { name: "抖音", value: 6 },
  { name: "天猫", value: 1 },
  { name: "淘宝", value: 9 },
]
//打款筛选
const spaymentList = [
  { name: "一次", value: 1 },
  { name: "多次", value: 2 },
]
const tableCols = [
  {
    istrue: true, fixed: 'left', prop: "auditState", label: "审核状态", sortable: "custom", width: "90",
    formatter: (row) => {
      // return ['审核', '待复审', '完成', '待评判'][row.auditState]
      return ['未审核', '待复审', '已审核', '待评判'][row.auditState]
    },
  },
  {
    istrue: true, prop: "isSolved", label: "是否解决", sortable: "custom", width: "80",
    formatter: (row) => {
      if (row.isSolved == 1) return "是";
      else return "否";
    },
  },
  // {
  //   istrue: true, prop: "orderState", label: "订单状态", sortable: "custom", width: "90",
  //   formatter: (row) => {
  //     let text = ''
  //     if (row.orderState == 2) {
  //       text = '已发货'
  //     } else if (row.orderState == 3) {
  //       text = '未发货'
  //     }
  //     return text
  //   },
  // },
  {
    istrue: true, prop: "afterSalesState", label: "售后状态", sortable: "custom", width: "90",
    formatter: (row) => {
      let newArr = [
        { value: 100, name: "待商家收货" },
        { value: 101, name: "待商家发货" },
        { value: 102, name: "待商家处理" },
        { value: 103, name: "待买家退货" },
        { value: 104, name: "退款中" },
        { value: 105, name: "退款成功" },
        { value: 106, name: "退款失败" },
        { value: 107, name: "售后关闭" },
        { value: 108, name: "拒绝售后申请" },
        { value: 109, name: "退货后商家拒绝" },
        { value: 110, name: "换货/补寄/维修，待买家收货" },
        { value: 111, name: "拒签后退款" },
        { value: 112, name: "换货成功" },
        { value: 113, name: "补寄成功" },
        { value: 114, name: "维修成功" },
        { value: 300, name: "已同意退货退款,待用户发货" },
        { value: 301, name: "退款成功" },
        { value: 302, name: "已同意拒收退款，待用户拒收" },
        { value: 303, name: "已撤销" },
        { value: 304, name: "商家拒绝，待用户处理" },
        { value: 305, name: "待商家处理" },
        { value: 306, name: "待商家召回" },
        { value: 307, name: "待客服处理" },
        { value: 308, name: "换货补寄待商家处理" },
        { value: 309, name: "换货补寄待用户确认完成" },
        { value: 310, name: "换货补寄失败" },
        { value: 311, name: "退款中" },
        { value: 312, name: "补寄待商家发货" },
        { value: 313, name: "换货补寄待用户处理" },
        { value: 314, name: "客服驳回" },
        { value: 315, name: "换货补寄成功" },
        { value: 316, name: "维修关闭" },
        { value: 317, name: "待分配" },
        { value: 318, name: "待商家同意维修" },
        { value: 319, name: "售后单失败" },
        { value: 200, name: "退款成功" },
        { value: 201, name: "退款待处理" },
        { value: 202, name: "待取件" },
        { value: 203, name: "待商家收货" },
        { value: 204, name: "待买家发货" },
        { value: 205, name: "待接单" },
        { value: 206, name: "待支付取件费用" },
        { value: 207, name: "退款关闭" },
      ]
      return newArr.filter(item => item.value == row.afterSalesState)[0]?.name
    },
  },
  // {
  //   istrue: true, prop: "afterSales", label: "售后类型", sortable: "custom", width: "80",
  //   formatter: (row) => {
  //     return ['小额打款', '未发货仅退款', '已发货仅退款', '退货退款'][row.afterSales]
  //   },
  // },
  {
    istrue: true, prop: "orderNo", label: "线上订单号", sortable: "custom", width: "180", type: 'click',
    handle: (that, row) => that.showLogDetail(row)
  },
  // { istrue: true, prop: "amount", label: "订单金额", sortable: "custom", width: "80", },
  // { istrue: true, prop: "smallAmount", label: "小额打款总额", sortable: "custom", width: "120", },
  // { istrue: true, prop: "smallAmountCount", label: "小额打款次数", sortable: "custom", width: "120", },
  {
    istrue: true, display: true, prop: "proId", label: "宝贝ID", sortable: "custom", type: "html", width: "150",
    formatter: (row) => formatLinkProCode(row.platform, row.proId),
  },
  { istrue: true, prop: "status", label: "上下架状态", width: "90", sortable: "custom", 
    formatter: (row) => {
      if (1 == row.status) return "上架";
      else if (3 == row.status) return "下架";
    },
  },
  { istrue: true, prop: "score", label: "IDDSR", width: '100', sortable: "custom", align: "center" },
  {
    istrue: true, prop: "createdTime", label: "创建时间", width: "100", sortable: "custom",
    formatter: (row) => {
      return row.createdTime ? formatTime(row.createdTime, "YYYY-MM-DD") : "";
    },
  },
  {
    istrue: true, prop: "conversationTime", label: "申请时间", width: "100", sortable: "custom",
    formatter: (row) => {
      return row.conversationTime ? formatTime(row.conversationTime, "YYYY-MM-DD") : "";
    },
  },
  {
    istrue: true, prop: "orderTime", label: "下单时间", width: "100", sortable: "custom",
    formatter: (row) => {
      return row.orderTime ? formatTime(row.orderTime, "YYYY-MM-DD") : "";
    },
  },
  { istrue: true, prop: "shopName", label: "店铺", width: "120", sortable: "custom",
  },
  { istrue: true, prop: "styleCode", label: "系列编码", width: "120", sortable: "custom", },
  { istrue: true, prop: "seriesNameScore", label: "系列编码DSR", width: '100', sortable: "custom", align: "center" },
  { istrue: true, prop: "chatCount", label: "聊天条数", width: "120", sortable: "custom", },
  { istrue: true, prop: "brand", label: "采购", width: "120", sortable: "custom", },
  { istrue: true, prop: "directorUserName", label: "负责运营", width: "120", sortable: "custom", },
  { istrue: true, prop: "directorGroupUserName", label: "运营小组", width: "120", sortable: "custom", },
  // { istrue: true, prop: "chatAccount", label: "聊天账号", width: "120", sortable: "custom", },
  { istrue: true, prop: "userName", label: "使用账号人", sortable: "custom", width: "120", },
  // { istrue: true, prop: "responsibleName", label: "责任客服", sortable: "custom", width: "120", },
  // { istrue: true, prop: "groupName", label: "分组", sortable: "custom", width: "80", },
  // { istrue: true, prop: "groupManager", label: "组长", sortable: "custom", width: "80", },
  { istrue: true, prop: "initialAuditType", label: "审核结果", width: "80", sortable: "custom",
    formatter: (row) => {
      let name = ''
      if (row.initialAuditType == 1) {
        name = '合格'
      } else if (row.initialAuditType == 2) {
        name = '不合格'
      }
      return name;
    },
  },
  {
    istrue: true, prop: "refuseInitialAuditType", label: "审核类型", width: "90", sortable: "custom",
    formatter: (row) => {
      return row.refuseInitialAuditType
    },
  },
  { istrue: true, prop: "initialOperatorTime", label: " 审核时间", width: "90", sortable: "custom", },
  {
    istrue: true, prop: "reasonForRefund", label: "退款原因", width: "90", sortable: "custom",
    formatter: (row) => {
      return row.reasonForRefund
    },
  },
  {
    istrue: true, prop: "person", label: "责任人", width: "90", sortable: "custom",
    formatter: (row) => {
      return row.person
    },
  },
  { istrue: true, prop: "initialOperator", label: "审核人", width: "90", sortable: "custom", },
  // { istrue: true, prop: "assigner", label: "分配人", width: "90", sortable: "custom", },
  {
    istrue: true, type: "button", width: "120", label: "操作", align: "center", fixed: "right",
    btnList: [
      { label: "审核", handle: (that, row) => that.openFirstLog(row), display: (row) => { return row.auditState != 0 }, permission: "api:customerservice:UnPayOrder:AfterSalesInitial" },
      { label: "查看", handle: (that, row) => that.openResultLog(row) },
    ],
  },
];

const tableCols2 = [
  { istrue: true, width: '80', align: 'center', type: "checkbox" },
  { width: '180', align: 'center', label: '分配人', prop: 'assigner' },
  // {
  //   istrue: true, type: 'button', width: '150', label: '操作', btnList: [
  //     { label: "删除", handle: (that, row) => that.onDelete(row) }
  //   ]
  // }
];

const allList = ["回复问题", "流程问题", "敷衍怠慢", "专业能力", "存在违规/过度承诺", "存在违规/引导线下交易", "存在违规/其他违规", "态度问题/辱骂客户", "态度问题/怒怼客户", "态度问题/反问客户", "态度问题/不耐烦", "小额打款异常"];//全部
const reasonForRefundList = ["运营问题/虚假宣传", "运营问题/编码问题", "运营问题/低价引流问题", "仓库问题/订单错漏发", "仓库问题/包装问题", "仓库问题/定制问题", "售前问题/推荐错误", "售前问题/过度承诺", "售前问题/漏备注或备注错", "快递问题", "采购问题", "售后问题", "产品质量问题"]
export default {
  name: "ServiceChatHistory",
  components: { MyContainer, CesTable, inputYunhan, vxetablebase,
    ChartListDialog,
    FirstDialog,
    ReviewDialog,
    ResultDialog,
    JudgeDialog,
    OrderActionsByInnerNos
  },
  props: ["partInfo", "chatInfos"],
  data() {
    return {
      storeid: 'PDDMiddlePlatformDailyPaper202411121454',
      selectedTitles: [],
      dailyNewspaperToolbar: false,
      that: this,
      Filter: {
        conversationTime: [
          formatTime(dayjs().startOf("month"), "YYYY-MM-DD HH:mm:ss"),
          formatTime(new Date(), "YYYY-MM-DD 23:59:59"),
        ],//申请时间
        styleCode: "",//系列编码
        person: "",//责任人
        reasonForRefund: [],//退款原因稽查
        smallAmountCount: null,//重复打款
        shopNameList: [],//店铺
        refuseJudgmentAuditType: "",//稽查评判类型
        judgmentAuditType: null,//稽查评判结果
        refuseFinalAuditType: "",//复审类型
        finalAuditType: null,//复审结果
        refuseInitialAuditType: [],//审核类型
        initialAuditType: null,//审核结果
        initialOperator: "",//审核人
        groupManager: "",//组长
        groupNameList: [],//分组
        userName: [],//账号使用人
        proId: "",//宝贝ID
        orderNo: "",//线上订单号
        chatAccount: "",//聊天账号
        afterSales: [],//售后类型
        afterSalesState: [],//售后状态
        platform: 2,
        orderState: null,//订单状态
        auditState: null,//审核状态
        createdTime: [],//创建时间
        timerange: [],//下单日期
        auditTime: [],//审核时间
        initialOperatorTime: [],//审核时间
        directoList: [],//运营小组
        orderNoList: "",//线上订单号
        proIdList: "",//宝贝ID
        // assigner: "",//分配人
        directorUserName: "",//负责运营
        directorGroupIdList: [],//运营小组
        chatCountStart: null,//聊天条数
        chatCountEnd: null,//聊天条数
        isSolved: null,//是否解决
        status: [],//上下架状态
        minScore: null,//最小ID评分
        maxScore: null,//最大ID评分
        minSeriesNameScore: null,//最小系列编码评分
        maxSeriesNameScore: null,//最大系列编码评分
      },
      spaymentList: spaymentList,
      allList: allList,
      reviewStatusList: [
        // { name: "审核", value: 0 },
        // { name: "完成", value: 2 },
        { name: "未审核", value: 0 },
        { name: "已审核", value: 2 },
      ],
      afterStatusList: [
        { name: "小额打款", value: 0 },
        { name: "未发货仅退款", value: 1 },
        { name: "已发货仅退款", value: 2 },
        { name: "退货退款", value: 3 },
      ],
      afterStatusArr: [],
      afterJson: {
        dy: [],
        pdd: [],
        tx: []
      },
      orderStatusList: [
        { name: "已发货", value: 2 },
        { name: "未发货", value: 3 },
      ],
      platformList: platformList,
      firstTrialList: [
        { name: "合格", value: 1 },
        { name: "不合格", value: 2 },
      ],
      statusList: allList,
      groupManagerList: [],
      groupNameList: [],
      userNameList: [],
      tableData: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: null,
      pager: { orderBy: "createdTime", isAsc: false },
      listLoading: false,
      shopList: [],
      resourcedialogVisible: false,
      firstdialogVisible: false,
      reviewdialogVisible: false,
      resultdialogVisible: false,
      judgeRefdialogVisible: false,
      pickerOptions: {
        disabledDate(date) {
          // 设置禁用日期
          const start = new Date("1970/1/1");
          const end = new Date("9999/12/31");
          return date < start || date > end;
        },
      },
      defaultDate: new Date(),
      upDataTime: "",
      dialogHisVisible: false,
      orderNo: '',
      reasonForRefundList: reasonForRefundList,
      personList: [],
      paramsInfo: this.partInfo,
      refcurrentPag: 1,//传给审核页的当前页码，审核后刷新当前页
      paramsPreInfo: this.chatInfos,
      btnHide: false,//搜索默认收缩
      colOptions: [],
      colSelect: ["创建时间", "下单日期", "审核日期",
        "审核状态", //"订单状态", '平台', 
        "售后状态", //"售后类型", '聊天账号', 
        "线上订单号", '宝贝ID', '账号使用人', //'分组', '组长', '审核人', 
        '审核结果', '审核类型', '退款原因稽查',
        '店铺', //'重复打款', '退款原因稽查', '责任人', 
        '负责运营', '运营小组', //'责任客服',
        '系列编码', '聊天条数', '审核人',//'分配人'
        '是否解决', '上下架状态', 'ID评分', '编码评分'
      ],
      keywordSearchdialogVisibleSyj: false,     //添加弹出框控制
      chatContentKeyword: "",
      orderNoList: "",//线上订单号
      proIdList: "",//宝贝ID
      tableHandles: tableHandles,
      ruleHints: '',
      // 新建规则
      formCreate: {
        assigner: [],// 分配人
      },
      allocaterList: [],// 分配人列表
      createVisible: false,
      directoList: [],
      tableCols2: tableCols2,
      tableData2: [],
      selectList: [],//复选框选中数据
    };
  },
  watch: {
    partInfo: {
      handler: function (newVal, oldVal) {
        if (newVal) {
          this.paramsInfo = newVal;
          this.btnHide = true;//展开搜索
          setTimeout(() => {
            this.showlist();
          }, 200);
        }
      },
      immediate: true,
    },
    chatInfos: {
      handler: function (newVal, oldVal) {
        if (newVal) {
          this.paramsPreInfo = newVal;
          this.btnHide = true;//展开搜索
          setTimeout(() => {

            this.showlistPre();
          }, 200);
        }
      },
      immediate: true,
    },
  },
  async mounted() {
    await this.init();
    // await this.onSearch();
    this.oninitializeEcho();
    await this.getAfterStatusList();
    // this.ruleHints = "会话时间查询规则：售后页面能看到以创建时间维度，当天只能看到创建时间4天前的数据，如果后补数据延迟创建只能延后看，例如：会话是4号，正常入库创建时间是在5号，也就是4号的数据，只能5号+4天,也就是9号能看到，如果会话是4号，部分数据后补入库在6号创建，该部分数据在6号+4天，也就是10号能看到"
    this.ruleHints = "会话时间查询规则"
  },
  methods: {
    validateNumber(value, field) {
      // 只允许数字字符
      const numberValue = value.replace(/\D/g, '');
  
      // 更新绑定的数据
      this.Filter[field] = numberValue;
    },
    validateNumber2(value, field) {
      // 只允许数字字符，允许小数点
      const numberValue = value.replace(/[^0-9.]/g, '');
  
      // 更新绑定的数据
      this.Filter[field] = numberValue;
    },
    //复选框数据
    chooseCode(row) {
      this.unmountrows = row
    },
    //复选框数据
    chooseCode(row) {
      this.unmountrows = row
    },
    //点击确定
    async verifyOptions() {
      await SetVxeTableColumnCacheAsync({ tableId: this.storeid, ColumnConfig: JSON.stringify(this.colOptions) });
      var arr = this.colSelect.filter(i => this.colOptions.indexOf(i) < 0); // 未选中的
      this.selectedTitles = this.colSelect.filter(i => {
        if (i !== '日期') {
          return !arr.includes(i)
        }
      });
      this.dailyNewspaperToolbar = false;
    },
    //全选
    changeOptions(valArr) {
      this.colOptions = valArr;
    },
    //初始化
    selectAll() {
      if (this.colOptions.length != this.colSelect.length) {
        this.colOptions = this.colSelect.map(i => i);
      } else {
        this.colOptions = [];
      }
    },
    //数据初始化回显
    async oninitializeEcho() {
      const { data, success } = await GetVxeTableColumnCacheAsync({ tableId: this.storeid });
      if (success) {
        let storeData = data ? JSON.parse(data) : [];
        this.colOptions = this.colSelect.filter(i => storeData.includes(i));
      } else {
        this.colOptions = [];
      }
      this.selectedTitles = this.colOptions
    },
    //点击设置
    clickToolbar() {
      this.oninitializeEcho();
      this.dailyNewspaperToolbar = true;
    },
    showlistPre() {
      this.Filter.platform = 2
      // this.changePlatform(2)

      this.Filter.groupNameList = [];
      if (this.paramsPreInfo?.groupName) {
        this.Filter.groupNameList = [this.paramsPreInfo?.groupName]
      }

      this.Filter.userName = this.paramsPreInfo?.userName

      if (this.paramsPreInfo?.num == 1) {
        this.Filter.initialAuditType = 2
        this.Filter.refuseInitialAuditType = this.paramsPreInfo.typeName
      }

      if (this.paramsPreInfo?.num == 2) {
        this.Filter.judgmentAuditType = 2
        this.Filter.refuseJudgmentAuditType = this.paramsPreInfo?.typeName
      }

      this.Filter.createdTime = [];
      this.Filter.createdTimeStart = null;
      this.Filter.createdTimeEnd = null;

      this.Filter.conversationTime = [];
      if (this.paramsPreInfo.timerange.length > 0) {
        const data = this.paramsPreInfo.timerange;
        this.Filter.conversationTime = [data[0], data[1]];
        this.Filter.conversationTimeStart = data[0];
        this.Filter.conversationTimeEnd = data[1];
      }
      this.Filter.auditTime = [];
      if (this.paramsPreInfo.auditTime.length > 0) {
        const data = this.paramsPreInfo.auditTime;
        this.Filter.initialOperatorTime = [data[0], data[1]];
        this.Filter.initialOperatorTimeStart = data[0];
        this.Filter.initialOperatorTimeEnd = data[1];
      }
      this.Filter.timerange = [];
      // if (this.paramsPreInfo.roderTime.length > 0) {
      //   const data = this.paramsPreInfo.roderTime;
      //   this.Filter.timerange = [data[0], data[1]];
      //   this.Filter.orderTimeStart = data[0];
      //   this.Filter.orderTimeEnd = data[1];
      // }
      //维度是分组
      // if (this.paramsPreInfo.dataplatform == 1 && !this.paramsPreInfo?.groupName) {
      //   this.Filter.specialGroupName = "";
      // }
      //维度是个人
      if (this.paramsPreInfo.dataplatform == 2 && !this.paramsPreInfo?.userName) {
        this.Filter.specialUserName = "";
      }
      this.onSearch1()
    },
    showlist() {
      //拼多多
      this.Filter.platform = 2
      //系列编码
      this.Filter.styleCode = this.paramsInfo.styleCode;
      //退款原因稽查
      this.Filter.reasonForRefund = [];
      this.Filter.reasonForRefund.push(this.paramsInfo.typeName);
      // this.changePlatform(2)
      this.Filter.specialGroupName = null;
      //分组
      this.Filter.groupNameList = [];
      // if (this.paramsInfo.groupName) {
      //   this.Filter.groupNameList = [this.paramsInfo.groupName]
      // }
      //申请时间
      this.Filter.conversationTime = [];
      if (this.paramsInfo.conversationTime) {
        const time = this.paramsInfo.conversationTime;

        this.Filter.conversationTime[0] = time[0]
        this.Filter.conversationTime[1] = time[1]
        this.Filter.conversationTimeStart = time[0];
        this.Filter.conversationTimeEnd = time[1];
      }
      //审核时间
      this.Filter.initialOperatorTime = [];
      if (this.paramsInfo.auditTime) {
        const data = this.paramsInfo.auditTime;
        this.Filter.initialOperatorTime = [data[0], data[1]];
        this.Filter.operatorTimeStart = data[0];
        this.Filter.operatorTimeEnd = data[1];
      }
      this.Filter.specialGroupName = null;
      if (!this.paramsInfo?.groupName && this.paramsInfo.dataplatform != 0) {
        this.Filter.specialGroupName = "";
      }
      //创建时间
      this.Filter.createdTime = [];
      this.Filter.createdTimeStart = null;
      this.Filter.createdTimeEnd = null;
      //下单时间
      this.Filter.timerange = [];
      this.Filter.orderTimeStart = null;
      this.Filter.orderTimeEnd = null;
      //审核状态
      this.Filter.auditState = null;
      //订单状态
      this.Filter.orderState = null;
      //售后状态
      this.Filter.afterSalesState = [];
      //售后类型
      this.Filter.afterSales = [];
      //线上订单号
      this.Filter.orderId = "";
      this.Filter.orderNoList = "";
      //宝贝ID
      this.Filter.proId = "";
      this.Filter.proIdList = ""
      //账号使用人
      this.Filter.userName = [];
      //审核结果
      this.Filter.initialAuditType = null;
      //审核类型
      this.Filter.refuseInitialAuditType = [];
      //负责运营
      this.Filter.directorUserName = "";
      //运营组
      this.Filter.directorGroupIdList = [];
      //聊天条数
      this.Filter.chatCountStart = null;
      this.Filter.chatCountEnd = null;
      //分配人
      // this.Filter.assigner = "";
      //是否解决
      this.Filter.isSolved = null;
      //上下架状态
      this.Filter.status = [];
      //ID评分
      // this.Filter.minScore = 0;
      // this.Filter.maxScore = 5;
      //编码评分
      // this.Filter.minSeriesNameScore = 0;
      // this.Filter.maxSeriesNameScore = 5;
      
      this.onSearch1()
    },
    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init() {
      var date1 = new Date(); date1.setDate(date1.getDate() - 4);
      var date2 = new Date(); date2.setDate(date2.getDate() - 1);
      this.Filter.conversationTime = [];
      this.Filter.conversationTime[0] = this.datetostr(date1);
      this.Filter.conversationTime[1] = this.datetostr(date2);

      const directo = await getDirectorGroupList();
      this.directoList = directo.data;

      await this.getPersonNameList();

      const groupManager = await getGroupManagerList({ platform: 2 });
      const gruopName = await getGruopNameList({ platform: 2 });
      const userName = await getUserNameList({ platform: 2 });
      this.groupManagerList = groupManager.data;
      this.groupNameList = gruopName.data;
      this.userNameList = userName.data;
      const res1 = await getshopList({
        platform: 2,
        CurrentPage: 1,
        PageSize: 2000,
      });
      this.shopList = res1.data.list;

      this.afterJson = await getAfterStatusList();
      this.afterStatusArr = this.afterJson?.pdd;

      // 分配人-拼多多运营中台
      var { data } = await getClaimUserList();
      this.allocaterList = data;
    },

    async getPersonNameList() {
      const param = {};

      if (this.Filter.conversationTime) {
        param.conversationTimeStart = this.Filter.conversationTime[0];
        param.conversationTimeEnd = this.Filter.conversationTime[1];
      }
      const person = await getPersonList(param);//责任人
      this.personList = person.data;
    },
    showDetail(row) {   //查看聊天记录
      this.$refs.chartRef.interfaceType = true;
      this.$refs.chartRef.keyWord = row.conversationId;
      this.$refs.chartRef.platform = row.platform;
      this.$refs.chartRef.dataJson = row;
      this.$refs.chartRef.tableData = this.tableData;
      this.resourcedialogVisible = true;
    },
    openFirstLog(row) { //审核
      this.$refs.firstRef.interfaceType = true;
      this.$refs.firstRef.dataJson = row;
      this.$refs.firstRef.isShowOrHide = true;
      this.$refs.firstRef.salesType = 1
      this.$refs.firstRef.tableData = this.tableData.filter(item => { return item.auditState == 0 });
      this.firstdialogVisible = true;
    },
    openReviewLog(row) {  //复审
      this.$refs.reviewRef.interfaceType = true;
      this.$refs.reviewRef.dataJson = row
      this.$refs.reviewRef.isShowOrHide = true
      this.$refs.reviewRef.salesType = 1
      this.$refs.reviewRef.tableData = this.tableData.filter(item => { return item.auditState == 1 });
      this.reviewdialogVisible = true;
    },
    openResultLog(row) {  //查看
      this.$refs.resultRef.interfaceType = true;
      this.$refs.resultRef.dataJson = row
      this.$refs.resultRef.isShowOrHide = true
      this.$refs.resultRef.tableData = this.tableData;
      this.resultdialogVisible = true;
    },
    openJudgeLog(row) {  //评判
      this.$refs.judgeRef.interfaceType = true;
      this.$refs.judgeRef.dataJson = row
      this.$refs.judgeRef.isShowOrHide = true
      this.$refs.judgeRef.isShouQian = false
      this.$refs.judgeRef.salesType = 1
      this.$refs.judgeRef.tableData = this.tableData.filter(item => { return item.auditState == 3 });
      this.judgeRefdialogVisible = true;
    },
    showLogDetail(row) {
      this.dialogHisVisible = true;
      this.isTx = row.platform == 1 || row.platform == 8 || row.platform == 9 ? true : false;
      this.orderNo = row.orderNo;
    },
    // 查询
    onSearch() {
      const ii = this.refcurrentPag > 1 ? this.refcurrentPag : 1;
      this.$refs.pager.setPage(ii);
      this.gettrainplanList();
      this.getPersonNameList();
    },
    onSearch1() {
      this.$refs.pager.setPage(1);
      this.gettrainplanList();
      this.getPersonNameList();
    },
    getCondition() {
      const para = { ...this.Filter };
      if (this.Filter.createdTime) {
        para.createdTimeStart = this.Filter.createdTime[0];
        para.createdTimeEnd = this.Filter.createdTime[1];
      }
      if (this.Filter.timerange) {
        para.orderTimeStart = this.Filter.timerange[0];
        para.orderTimeEnd = this.Filter.timerange[1];
      }
      if (this.Filter.conversationTime) {
        para.conversationTimeStart = this.Filter.conversationTime[0];
        para.conversationTimeEnd = this.Filter.conversationTime[1];
      }
      if (this.Filter.auditTime) {
        para.operatorTimeStart = this.Filter.auditTime[0];
        para.operatorTimeEnd = this.Filter.auditTime[1];
      } else {
        para.operatorTimeStart = '';
        para.operatorTimeEnd = '';
      }
      if (this.Filter.initialOperatorTime) {
        para.initialOperatorTimeStart = this.Filter.initialOperatorTime[0];
        para.initialOperatorTimeEnd = this.Filter.initialOperatorTime[1];
      }
      if (this.Filter.finalOperatorTime) {
        para.FinalOperatorTimeStart = this.Filter.finalOperatorTime[0];
        para.FinalOperatorTimeEnd = this.Filter.finalOperatorTime[1];
      }
      if (this.Filter.judgmentOperatorTime) {
        para.JudgmentOperatorTimeStart = this.Filter.judgmentOperatorTime[0];
        para.JudgmentOperatorTimeEnd = this.Filter.judgmentOperatorTime[1];
      }
      if (this.Filter.minScore) {
      //   para.minScore = 0;
      // } else {
        para.minScore = parseFloat(this.Filter.minScore);
      }
      if (this.Filter.maxScore) {
      //   para.maxScore = 5;
      // } else {
        para.maxScore = parseFloat(this.Filter.maxScore);
      }
      if (this.Filter.minSeriesNameScore) {
      //   para.minSeriesNameScore = 0;
      // } else {
        para.minSeriesNameScore = parseFloat(this.Filter.minSeriesNameScore);
      }
      if (this.Filter.maxSeriesNameScore) {
      //   para.maxSeriesNameScore = 5;
      // } else {
        para.maxSeriesNameScore = parseFloat(this.Filter.maxSeriesNameScore);
      }

      if (this.Filter.shopNameList) para.shopNameList = this.Filter.shopNameList;
      else para.shopNameList = null;
      var pager = this.$refs.pager.getPager();
      this.refcurrentPag = pager.currentPage;//每次分页记录当前的页码，
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      return params;
    },
    async gettrainplanList() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await getOrderAfterSalesPageList(params);
      this.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.upDataTime = res.data.extData.dataUpdateTime;
      data.forEach((d) => {
        d._loading = false;
      });
      this.tableData = data;
    },
    // async changePlatform(val) {
    //   this.groupManagerList = [];
    //   this.groupNameList = [];
    //   this.userNameList = [];
    //   this.Filter.groupManager = null
    //   this.Filter.groupNameList = null
    //   this.Filter.userName = null;

    //   this.shopList = []
    //   this.Filter.shopNameList = null
    //   if (val) {
    //     const res1 = await getshopList({
    //       platform: 2,
    //       CurrentPage: 1,
    //       PageSize: 1000,
    //     });
    //     this.shopList = res1.data.list;
    //     if (val == 2) {
    //       this.afterStatusArr = this.afterJson?.pdd;
    //     } else if (val == 6) {
    //       this.afterStatusArr = this.afterJson?.dy;
    //     } else {
    //       this.afterStatusArr = this.afterJson?.tx;
    //     }
    //     const groupManager = await getGroupManagerList({ platform: 2 });
    //     const gruopName = await getGruopNameList({ platform: 2 });
    //     const userName = await getUserNameList({ platform: 2 });
    //     this.groupManagerList = groupManager.data;
    //     this.groupNameList = gruopName.data;
    //     this.userNameList = userName.data;
    //   }
    // },
    // changeAfterSalesState(val) {
    //   if (!this.Filter.platform) {
    //     // this.statusList=null;
    //     this.$message({ message: "请先选择平台！", type: "warning" });
    //     return false;
    //   }
    // },
    async getAfterStatusList(val) {
      const res = await getAfterStatusList();
      this.afterJson = res
    },
    sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          orderBy: column.prop,
          isAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      this.onSearch1();
    },
    async onExport(type) {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      params.dataType = type;  // 0：默认，1：带聊天记录
      var res = await exportUnPayOrderAfterSalesPageList(params);
      if (res?.success) {
        this.$message({ message: res.msg, type: "success" });
      }
    },
    async handleCommand(command) {
      switch (command) {
        //列表数据
        case 'a':
          await this.onExport(0)
          break;
        //聊天数据
        case 'b':
          await this.onExport(1)
          break;
        //聊天数据带关键词
        case 'c':
          this.keywordSearchdialogVisibleSyj = true
          break;
      }
    },
    topShowOrhide() {
      if (this.btnHide) {
        this.btnHide = false;
      } else {
        this.btnHide = true;
      }
    },
    async closeGroup() {
      this.keywordSearchdialogVisibleSyj = false
      this.Filter.chatContentKeyword = ""
    },
    //添加
    async addKeyword() {
      var params = this.getCondition();
      params.dataType = 1

      var res = await exportUnPayOrderAfterSalesPageList(params);
      if (res?.success) {
        this.$message({ message: res.msg, type: "success" });
      }
      this.keywordSearchdialogVisibleSyj = false
      this.Filter.chatContentKeyword = ""
    },
    //关闭
    closeDialog() {
      this.Filter.chatContentKeyword = ""
      this.$emit("closeDialog");
      this.keywordSearchdialogVisibleSyj = false
    },
    async callback(val) {
      this.Filter.orderNoList = val;
    },
    async callback2(val) {
      this.Filter.proIdList = val;
    },
    async entersearch(val) {
      this.onSearch1();
    },

    // 查询分配人
    async getAssigner() {
      const { data, success } = await getOrderAfterSalesAssigner();
      if (success) {
        this.tableData2 = data.list;
      } else {
        this.$message.error("获取分配人数据失败！");
      }
    },
    // 显示新建规则弹窗
    async onSetRule() {
      this.getAssigner();
      this.formCreate.assigner = [];
      this.formCreate.createTime = null;
      this.createVisible = true;
      this.$nextTick(() => {
        this.$refs.formCreate.clearValidate(); // 清除上次的校验结果
      });
    },
    // 取消新建规则弹窗
    createCancle() {
      this.createVisible = false;
    },
    // 关闭新建规则弹窗
    closeCreateDialog() {
      this.createVisible = false;
    },
    // 提交新建规则
    createSubmit() {
      var admitSubmit = false;
      this.$refs.formCreate.validate((valid) => {
        if (valid) {
          admitSubmit = true;
        } else {
          // 表单验证失败，提示用户
          this.$message.error('请检查填写的信息!');
        }
      });
      if (!admitSubmit) return;

      this.$refs.formCreate.validate(async valid => {
        if (valid && this.formCreate.createTime == null) {
          const res = await setOrderAfterSalesAssigner(this.formCreate.assigner);
          if (res.success) {
            this.$message.success('创建成功！');
            this.createVisible = false;
            this.getAssigner();
          }
        }
        // else if (valid && this.formCreate.createTime != null) {
        //   editOrderProcessRule(this.formCreate).then(response => {
        //     if (response.data) {
        //       this.$message.success("编辑成功!");
        //       this.createVisible = false;
        //       this.getRuleList();
        //     }
        //   });
        // }
      });
    },
    // 删除分配人
    async onDelete() {
      this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteOrderAfterSalesAssigner(this.selectList).then(response => {
        if (response.data) {
          this.$message.success('删除成功!');
          this.getAssigner();
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    // 已审核状态字体颜色为绿色
    async cellStyle(row, column, callback) {
      if(row.auditState != 2 && column.field == 'auditState') {
        callback({ color: 'red' })//#fe3836
      }
      else if (row.auditState == 2 && column.field == 'auditState') {
        callback({ color: 'green' })//#668f263
      }
    },
    // 复选框数据
    selectchange:function(rows,row) {
        this.selectList = [];
        rows.forEach(f => {
            this.selectList.push(f.assigner);
        })
    },
  }

};
</script>
<style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
      background-color: #fff;
  }

  ::v-deep .inner-container::-webkit-scrollbar {
      display: none;
  }

  ::v-deep .mycontainer {
      position: relative;
  }

  .uptime {
      font-size: 14px;
      position: absolute;
      right: 30px;
  }

  //解决下拉菜单多选由文字太长导致样式问题
  ::v-deep .el-select__tags-text {
      max-width: 45px;
  }
</style>
