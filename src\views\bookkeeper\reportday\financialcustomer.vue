<template>
  <container>
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
      @select='selectchange' :isSelection='false' :isSelectColumn='true' @summaryClick='onsummaryClick'
      :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true'  :tablekey='tablekey'
      :summaryarry='summaryarry' :loading="listLoading">
      <template slot='extentbtn'>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;">
            <el-select v-model="filter1.recoganizeType" placeholder="类型" style="width: 110px">
              <el-option label="全部" value />
              <el-option label="收款" :value="0" />
              <el-option label="退款" :value="1" />
            </el-select>
          </el-button>
          <el-button type="primary" @click="getlist">刷新</el-button>
          <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
        </el-button-group>
      </template>
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <!-- 系列编码趋势图 -->
    <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%">
      <span>
        <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </container>
</template>
<script>
import { getFinacialCustomerPage, exportFinacialCustomerRpt } from '@/api/monthbookkeeper/financialDetail'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import buschar from '@/components/Bus/buschar'
import { getAnalysisCommonResponse } from '@/api/admin/common'
const tableCols = [
  { istrue: true, prop: 'shopCode', label: '店铺名', sortable: 'custom', width: '160', formatter: (row) => { return row.shopName } },
  { istrue: true, prop: 'timeOccur', label: '发生时间', sortable: 'custom', width: '160' },
  { istrue: true, prop: 'serialNumberFinacial', label: '财务流水号', sortable: 'custom', width: '170' },
  { istrue: true, prop: 'originOrderNo', label: '订单编号', sortable: 'custom', width: '170' },
  { istrue: true, summaryEvent: true, prop: 'amountIncome', label: '收入金额', sortable: 'custom', width: '90' },
  { istrue: true, summaryEvent: true, prop: 'amountPaid', label: '支出金额', sortable: 'custom', width: '90' },
];
const tableHandles = []
export default {
  name: 'Roles',
  components: { cesTable, container, buschar },
  props: {
    filter: {},
    tablekey: { type: String, default:'' },
  },
  data() {
    return {
      that: this,
      filter1: { recoganizeType: null },
      list: [],
      tableCols: tableCols,
      tableHandles: tableHandles,
      pager: { OrderBy: " TimeOccur ", IsAsc: false },
      summaryarry: {},
      total: 0,
      sels: [],
      selids: [],
      isExport: false,
      listLoading: false,
      pageLoading: false,
      analysisFilter: {
        searchName: "tb_finacialcustomer_zfb",
        isYearMonthDay:true,
        isTimeFormat:true,
        extype: 5,
        selectColumn: "amountIncome",
        filterTime: "timeOccur",
        filter: null,
        columnList: [],
      },
      buscharDialog: { visible: false, title: "", data: [] },
    }
  },
  mounted() {

  },
  beforeUpdate() { },
  methods: {
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager()
      const params = { ...pager, ...this.pager, ... this.filter, ... this.filter1 }
      this.listLoading = true
      const res = await getFinacialCustomerPage(params)
      this.listLoading = false
      if (!res?.success) return
      this.total = res?.data?.total
      const data = res?.data?.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry = res.data?.summary;
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    selsChange: function (sels) {
      this.sels = sels
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    async onsummaryClick(property) {
      this.analysisFilter.columnList = [];
      this.analysisFilter.filter = null;
      let that = this;
      let summaryEventList = this.tableCols.filter(f => f.summaryEvent);
      summaryEventList.forEach(element => {
        this.analysisFilter.columnList.push({ columnNameCN: element.label, columnNameEN: element.prop });
      });

     
      this.analysisFilter.filter = {
        serialNumberFinacial: [this.filter.serialNumberFinacial, 0],
        originOrderNo: [this.filter.originOrderNo, 0],
        shopCode: [this.filter.shopCode, 0],
        recoganizeType:[this.filter1.recoganizeType,0]
      }
      if (this.filter.timerange) {
        this.analysisFilter.filter.timeOccur =  [this.filter.timerange[0], this.filter.timerange[1],0];
      }
      this.analysisFilter.selectColumn = property;
      const res = await getAnalysisCommonResponse(that.analysisFilter).then(res => {
        that.buscharDialog.visible = true;
        that.buscharDialog.data = res.data
        that.buscharDialog.title = res.data.legend[0]
      });
    },
    //导出
    async exportProps() {
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
          this.filter.startTime = this.filter.timerange[0];
          this.filter.endTime = this.filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager()
      const params = { ...pager, ...this.pager, ... this.filter, ... this.filter1 }
      this.isExport = true
      let res = await exportFinacialCustomerRpt(params);
      this.isExport = false
      if (!res?.data) {
          this.$message({ message: "没有数据", type: "warning" });
          return
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '支付宝账单-订单收支_' + new Date().toLocaleString() + '_.xlsx')
      aLink.click()
    },
  }
}
</script>
