<template>
  <div class="home-bg">
    <topCard ref="topCard"></topCard>
    <department ref="department"></department>
    <recruit ref="recruit"></recruit>
    <ring ref="ring"></ring>
    <characteristic ref="characteristic"></characteristic>
  </div>
</template>

<script>
import topCard from "@/views/profit/personnel/topCard";
import department from "@/views/profit/personnel/department";
import recruit from "@/views/profit/personnel/recruit";
import ring from '@/views/profit/personnel/ring';
import characteristic from '@/views/profit/personnel/characteristic';
export default {
    components: {
        topCard,department,recruit,ring,characteristic
    },

}
</script>

<style  lang="scss" scoped>
.home-bg {
    min-width: 1100px;
    background-color: #f3f4f6;
    padding: 5px;
    height:calc(100vh - 100px);
    overflow-y: auto;
}
</style>