<template>
    <container>
        <el-tabs v-model="activeName" style="height: calc(100% - 40px) ;">          

            <el-tab-pane label="公有可用" name="tab4" style="height: 100%;">
                <WhStockSurplusSumList ref="WhStockSurplusSumList"></WhStockSurplusSumList>
            </el-tab-pane>
            <el-tab-pane label="公有可用分配记录" name="tab5" style="height: 100%;">
                <WhStockSurplusFpRecordList ref="WhStockSurplusFpRecordList"></WhStockSurplusFpRecordList>
            </el-tab-pane>

        </el-tabs>

    </container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";


import WhStockSurplusSumList from "./WhStockSurplusSumList.vue";
import WhStockSurplusFpRecordList from "./WhStockSurplusFpRecordList.vue";


export default {
    name: 'WhStockSurplusMng',
    components: { container, MyConfirmButton, vxetablebase
        ,WhStockSurplusSumList,WhStockSurplusFpRecordList },

    data() {
        return {
            that: this,
            activeName: 'tab4',            
        };
    },

    async mounted() {
        await this.onSearch();
    },

    methods: {
        async onSearch() {
           
        },       
    },
};
</script>

<style lang="scss" scoped>
.three .el-tabs__item{
    color: red;
  }
</style>
