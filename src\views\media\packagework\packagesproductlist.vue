<template>
    <my-container v-loading="pageLoading">
      <template #header>
        <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent label-position="right" label-width="90px">
            <el-form-item label="">
                  <el-input v-model="filter.goodsCode" placeholder="商品编码" maxlength="50" clearable />
            </el-form-item>
            <el-form-item label="">
                  <el-input v-model="filter.goodsName" placeholder="商品名称" maxlength="100" clearable />
            </el-form-item>
            <el-form-item label="">
                  <el-input v-model="filter.styleCode" placeholder="款式编码" maxlength="80" clearable />
            </el-form-item>
            <el-form-item label="">
              <el-select style="width:130px;" v-model="filter.groupId" placeholder="运营组" :clearable="true" :collapse-tags="true" filterable>
                <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="">
              <el-select style="width:130px;" v-model="filter.buyerId" placeholder="采购组" :clearable="true" :collapse-tags="true" filterable>
                <el-option v-for="item in buyerList" :key="item.key" :label="item.value" :value="item.key">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="">
                <el-select style="width: 130px;" v-model="filter.brandCode" placeholder="包装" :collapse-tags="true" clearable multiple filterable>
                                <el-option v-for="item in brandList" :key="item.setId" :label="item.sceneCode" :value="item.setId" />
                </el-select>
            </el-form-item>
            <el-form-item label="">
                <el-select style="width: 130px;" v-model="filter.hasCertificate" placeholder="合格证" :collapse-tags="true" clearable filterable>
                        <el-option label="有" value="true"></el-option>
                        <el-option label="无" value="false"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="">
                <el-select style="width: 130px;" v-model="filter.brandStr" placeholder="品牌" :collapse-tags="true" clearable filterable>
                                <el-option v-for="(item,i) in selBrandList" :key="i" :label="item.value" :value="item.label" />
                </el-select>
            </el-form-item>
                <el-form-item label="">
                    <el-select style="width:130px;" v-model="filter.sortType"  placeholder="正常排序"
                        :collapse-tags="true" clearable >
                        <el-option label="正常排序" value="0"></el-option>
                        <el-option label="按款式编码升序" value="1"></el-option>
                        <el-option label="按商品编码升序" value="2"></el-option>
                        <el-option label="按商品名称升序" value="3"></el-option>
                        <el-option label="按修改时间升序" value="4"></el-option>
                        <el-option label="按修改时间降序" value="5"></el-option>
                        <el-option label="按创建时间升序" value="6"></el-option>
                        <el-option label="按创建时间降序" value="7"></el-option>
                    </el-select>
                </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <span>
            <el-radio-group style="margin-left:6px;" size="mini"
              v-model="onCommand" @change="ShowHideonSearch">
              <el-radio-button label="a">全部</el-radio-button>
              <el-radio-button label="b">原有</el-radio-button>
              <el-radio-button label="c">现有</el-radio-button>
            </el-radio-group>

            <el-button-group style="margin-left: 20px;">
              <el-button type="primary" @click="onExport">导出</el-button>
              <el-button v-show="false" type="primary" @click="onImportPrice">导入</el-button>
              <el-button type="primary" @click="onEditBatch">批量修改</el-button>
            </el-button-group>
          </span>
            </el-form-item>
          </el-form>
      </template>

      <ces-table ref="table" :showoverflowtooltip="false" :that='that' :isIndexFixed="false" :isIndex='true'  @sortchange='sortchange' :isSelection='true' @select='selectchange'
         :hasexpand='true' stripe :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading"
         :showsummary='true' :summaryarry='summaryarry' :isBorder="false"   @checkbox-range-end="selectchange" >
         <template #colslot="clovalue">
          <div @click="imgclick(clovalue.col)" style="cursor: pointer;">
            <el-image  class="imgstyle" :src="clovalue.col.pictureBig" fit="fill" >
            </el-image>
          </div>
         </template>
     </ces-table>
      <template #footer>
        <my-pagination :sizes="[50, 100, 200, 300, 800, 1000, 2000]" :page-size="200" ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
      </template>

      <el-drawer :visible.sync="editTaskshow" wrapperClosable :close-on-click-modal="true" direction="rtl" size="800px"
      element-loading-text="拼命加载中" v-loading="addLoading" :show-close="false">
      <dialogproductgright  v-if="editTaskshow" ref="dialogproductgright" style="height: 100%;width:100%" :heard="true" @onCloseAddForm="onCloseAddForm"></dialogproductgright>
      <span slot="title" style="height: 0px;"></span>
    </el-drawer>

    <el-dialog  :visible.sync="editationshow" wrapperClosable :close-on-click-modal="true" direction="rtl" size="800px"
      element-loading-text="拼命加载中" v-loading="addLoading" :show-close="false" :heard="false" v-dialogDrag>
      <dialogproductgright1 :heard="false" ref="dialogproduct" style="height: 100%;width:100%" @onCloseAddForm="onCloseAddForm"></dialogproductgright1>
      <span slot="title" style="height: 0px;"></span>
    </el-dialog >

    <el-dialog title="编辑图片" class="imgedit"  :visible.sync="imageshow" wrapperClosable :close-on-click-modal="true" direction="rtl" size="800px"
      element-loading-text="拼命加载中" v-loading="addLoading" :show-close="false" :heard="false" v-dialogDrag>
        <imagerow :jstimgnei="jstimg" :updatapro="getdata" @getimglist="getimglist" v-if="getdata"></imagerow>
      <div style="height: 10px;">
        <div style="float: right; ">
          <el-button type="danger" plain @click="imageshow = false">取消</el-button>
          <el-button type="primary" v-throttle="3000" @click="saveimg">保存</el-button>
        </div>
      </div>
      <!-- <span slot="title" style="height: 0px;"></span> -->
    </el-dialog >

    <el-dialog title="包装信息" :visible.sync="dialogVisibleSyj" width="30%">
      <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action accept=".xlsx"
        :file-list="fileList" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
        <template #trigger>
          <el-button size="small" type="primary">选取文件</el-button>
        </template>
        <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
        @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
      </el-upload>
    </el-dialog>
    </my-container>
  </template>

  <script>
  import MyContainer from '@/components/my-container'
  import MyConfirmButton from '@/components/my-confirm-button'
  import cesTable from "@/views/media/packagework/packgoodstable.vue";
  import { getGroupKeyValue } from '@/api/operatemanage/base/product';
  import {getAllProBrand} from '@/api/inventory/warehouse';
  import { getShootingSetData} from '@/api/media/shootingset'
  import { formatTime } from "@/utils";
  import dialogproductgright from '@/views/media/packagework/dialogrightproduct.vue';
  import dialogproductgright1 from '@/views/media/packagework/dialogrightproduct1.vue';

  import { getPackagesProcessingProductList,exportPackagesProcessingProductData, editPackagesProcessingProductImg,
     getPackagesProcessingProductImg,importPackagesProductAsync,getSelList} from "@/api/inventory/packagesprocess";
  import imagerow from "./imagerow.vue";
  const tableCols =[
         {istrue:true,type:'checkbox', width:'30'},
         {istrue:true,prop:'pictureBig',label:'图片', width:'60',type:'colslot'},
        //  {istrue:true,prop:'pictureBig',label:'图片', width:'60',type:'images'},
         {istrue:true,prop:'styleCode',label:'款式编码', width:'130', type: 'copy'},
         {istrue:true,prop:'goodsCode',label:'商品编码', width:'130', type: 'copy'},
         {istrue:true,prop:'goodsName',label:'商品名称', width:'230', type: 'copy'},
         {istrue:true,prop:'groupName',label:'运营组', width:'80'},
         {istrue:true,prop:'buyerName',label:'采购员', width:'80'},
         {istrue:true,prop:'id',label:'', width:'80',type:"ellips",formatter:(row)=> {return '...';},handle:(that,row)=>that.onEdit(row)},
         {istrue:true,prop:'packingName',label:'包装', width:'80'},
         {istrue:true,prop:'hasCertificateStr',label:'合格证', width:'90'},
         {istrue:true,prop:'brandName',label:'品牌', width:'100'},
         {istrue:true,prop:'execStandard',label:'执行标准', width:'100', type: 'copy'},
         {istrue:true,prop:'producer',label:'生产商', width:'100', type: 'copy'},
         {istrue:true,prop:'productAddress',label:'生产地址', width:'90', type: 'copy'},
         {istrue:true,prop:'seller',label:'销售方', width:'90', type: 'copy'},
         {istrue:true,prop:'sellerAddress',label:'销售方地址', width:'130', type: 'copy'},
        //  {istrue:true,prop:'orderWaitSend',label:'订单占有数', width:'100'},
         {istrue:true,prop:'sellStock',label:'可用数', width:'100',type:'html',formatter:(row)=>
          {return `<a href="javascript:void(0);" style="font-size:small;color:${row.orderWaitSend>row.sellStock?'red':''}";">${row.sellStock}</a>`;}},
         {istrue:true,prop:'salesDay30',label:'30天销量', width:'90'},
         {istrue:true,prop:'avgDay30',label:'30天日均', width:'90'},
         {istrue:true,prop:'modified',label:'修改时间', width:'100',formatter:(row)=> {return row.modified == null ? null : formatTime(row.modified, 'YY-MM-DD')},},
         {istrue:true,prop:'createdTime',label:'创建时间', width:'100',formatter:(row)=> {return row.createdTime == null ? null : formatTime(row.createdTime, 'YY-MM-DD')},},
        //  {istrue:true,prop:'isEnabled',label:'是否启用', width:'80',formatter:(row)=>row.isEnabled==0?"备用":(row.isEnabled==1?"启用":(row.isEnabled==-1?"禁用":""))}
       ];
  const tableHandles1=[
    // {label:"导出", handle:(that)=>that.onExport()},
    // {label:"批量修改", handle:(that)=>that.onEditBatch()},
  ];
  export default {
    name: 'goods',
    components: {cesTable, MyContainer, MyConfirmButton ,dialogproductgright, imagerow, dialogproductgright1},
    props:{
      ischoice:{type:Boolean,default:false},
    },
    data() {
      return {
        dialogVisibleSyj:false,
        uploadLoading: false,
        that:this,
        imageshow: false,
        jstimg: '',
        filter: {
          styleCode:null,
          goodsCode:null,
          goodsName:null,
          groupId:null,
          buyerId:null,
          brandId:null,
          isEnabled:1,
          brandCode:[],
          hasCertificate:null,
          brandStr:null
       },
        list: [],
        clovalue: {},
        summaryarry:{},
        pager:{OrderBy:"modified",IsAsc:false},
        tableCols:tableCols,
        tableHandles:tableHandles1,
        platformList: [],
        shopList: [],
        groupList:[],
        buyerList:[],
        brandList:[],
        selBrandList:[],
        dialogVisible: false,
        total: 0,
        sels: [],
        listLoading: false,
        pageLoading: false,
        fileList:[],
        selrows:[],
        editTaskshow: false,
        addLoading:false,
        onCommand: "c",
        editationshow:false,
        //选中的行id
        selids: [],
        updata: {
          packingImg: "",
          productImg: "",
          certificateImg: "",
        },
        getdata: {},
      }
    },
    created(){

    },
    async mounted() {
      this.listLoading = true;
      await this.setGroupSelect();
      await this.setBuyerSelect();
      await this.setBandSelect();
      await this.getlist();
    },
    watch: {
      routerchange(e){
      }
    },
    computed: {
      routerchange(){
        this.list = []
        this.router = this.$route.path;
        this.$nextTick(()=>{
          this.getlist();
        })
        return
      }
    },
    methods: {
      async saveimg(){
        let params ={
          ...this.updata
        }
        let res = await editPackagesProcessingProductImg(params)
        if(!res.success){
          return
        }
        this.imageshow = false;
        this.$message.success("保存成功！")
      },
      getimglist(e){
        let _this = this;

        // _this.updata = {
        //   packingImg: "",
        //   productImg: "",
        //   certificateImg: "",
        // };
        _this.updata.productImg = e.productImg;
        _this.updata.packingImg = e.packingImg;
        _this.updata.certificateImg = e.certificateImg;
      },
      async imgclick(e){
        this.jstimg = e.pictureBig;
        await this.getimgs(e.goodsCode);
        this.updata.goodsCode = e.goodsCode;
        this.imageshow = true;
      },
      async getimgs(e){
        this.getdata = {};
        let res = await getPackagesProcessingProductImg({goodsCode: e});
        if(!res.success){
          return
        }
        this.getdata = res.data||{};
      },
      async setGroupSelect(){
        const res = await getGroupKeyValue({});
        this.groupList=res.data;
      },
      async setBuyerSelect(){
        var res= await  getAllProBrand();
          if (!res?.success) return;
          this.buyerList = res.data;
      },
      async setBandSelect(){
        var res = await getShootingSetData({ setType:15});
          if (!res?.success) return;
          this.brandList = res?.data?.data;

          var resBrand = await getSelList();
          if(!resBrand?.success) return;
          this.selBrandList =resBrand?.data?.map(item => { return { value: item, label: item }; });
      },
      //获取查询条件
      getCondition(){
        var pager = this.$refs.pager.getPager();
        var page  = this.pager;
        const params = { ...pager, ...page, ... this.filter}
        return params;
      },
      //查询第一页
      async onSearch() {
        this.listLoading = true;
        this.$refs.pager.setPage(1)
        await this.getlist()
      },
      //分页查询
      async getlist() {
        var params=this.getCondition();
        if(params===false){
          return;
        }
        var res = await getPackagesProcessingProductList(params);
        if (!res?.success) {
          return
        }
        this.total = res.data.total;
        const data = res.data.list;
        this.summaryarry=res.data.summary;
        data.forEach(d => {
          d._loading = false;
          d.id = d.goodsCode;
          if(!d.pictureBig){
            d.pictureBig = 'https://s2.loli.net/2024/07/24/hDsOtLZNXdjAf7Q.png';
          }
        })
        this.list = data
        this.listLoading = false;
      },

      //排序查询
      async sortchange(column){
        if(!column.order)
          this.pager={};
        else{
          var orderBy =column.prop;

          this.pager={OrderBy:orderBy,IsAsc:column.order.indexOf("descending")==-1?true:false};
        }
        await this.onSearch();
      },
      selsChange: function(sels) {
        this.sels = sels
      },
      selectchange:function(rows) {
        if(rows.length>0)
        {
          var a = [];
          rows.forEach(x => {
            a.push(x.goodsCode);
          });
          this.selids = a;
        }else{
          this.selids = [];
        }
        this.selrows=rows;
      },
        async onExport() {
            this.$confirm('确认导出吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true;
                const rlt = await exportPackagesProcessingProductData(params);
                this.listLoading = false;
                if (rlt && rlt.data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([rlt.data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '包装加工商品资料_' + new Date().toLocaleString() + '_.xlsx')
                    aLink.click()
                }
            }).catch(() => {
            });
        },
     async getchoicelist(){
         if(!this.selrows||this.selrows.length==0)
           this.$message({message: "你还没有选择",type: "warning",});
         return this.selrows
      },
      async onEdit(row)
      {
        this.editTaskshow = true;
        // this.$refs.dialogproductgright.getfamsg(row.goodsCode);
        this.$nextTick(()=>{
          this.$refs.dialogproductgright.getfamsg(row.goodsCode,row.goodsName);
        });

      },
         //关闭窗口，初始化数
    async onCloseAddForm(type) {
        this.getlist();
         if(type==1)
         {
            this.editTaskshow = false;
         }else if(type==2)
         {
            this.selids=[];
            this.getlist();
            this.editationshow = false;
         }
    },
    ShowHideonSearch() {
      this.$emit('ShowHideonSearch', this.onCommand);
      if(this.onCommand=="b")
      {
        this.filter.isEnabled=-1;
      }else  if(this.onCommand=="c")
      {
        this.filter.isEnabled=1;
      }else
      {
        this.filter.isEnabled=-2;
      }
      this.onSearch();
    },
    async onEditBatch()
    {
      if(this.selids.length==0)
      {
        this.$message({message: "请先选择商品",type: "warning",});
        return false;
       }
       this.editationshow=true;
      this.$nextTick(()=>{
        this.$refs.dialogproduct.getfamsg();
        this.$refs.dialogproduct.gettabmsgbatch(this.selids);
      });
      },
      onImportPrice(){
      this.dialogVisibleSyj = true
    },
    submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true

      if(this.$refs.upload?.uploadFiles.length != 0){
        this.$refs.upload.submit();
      }else{
        this.$message({
              type: 'fail',
              message: '请选择文件！'
          })
      }
    },
    async uploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.uploadLoading = false;
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      console.log("上传文件",form)
      let res = await importPackagesProductAsync(form);
      this.uploadLoading = false
      if (res.code == 1) {
        this.importDialog.visible = false
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      }
      this.fileList = []
    },
    async uploadChange(file, fileList) {
      let files = [];
      files.push(file);
      this.fileList = files;
    },
    async uploadRemove(file, fileList) {
      let files = [];
      this.fileList = files;
    },
    },
  }
  </script>

  <style lang="scss" scoped>
  // ::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  //   width: 15px !important;
  //   height: 26px !important;
  // }

  /*滚动条整体部分*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar {
  width: 18px !important;
  height: 26px !important;
}

/*滚动条的轨道*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-track {
  background-color: #f1f1f1 !important;
}

/*滚动条里面的小方块，能向上向下移动*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
  background-color: #c1c1c1 !important;
  border-radius: 3px !important;
  box-sizing: border-box !important;
  border: 2px solid #F1F1F1 !important;
  box-shadow: inset 0 0 6px rgba(255, 255, 255, .5) !important;
}

// 滚动条鼠标悬停颜色
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
  background-color: #A8A8A8 !important;
}

// 滚动条拖动颜色
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
  background-color: #787878 !important;
}

/*边角，即两个滚动条的交汇处*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
  background-color: #dcdcdc !important;
}

.imgedit ::v-deep .el-dialog__header{
  border-bottom: 1px solid #eee;
}
  </style>
