<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding:0;margin:0;">
                    <el-input v-model.trim="filter.keyWord" placeholder="关键字搜索" :maxlength="50" clearable>
                        <el-tooltip slot="suffix" class="item" effect="dark" content="支持搜索的内容：商品名称，商品名称类型，价格，积分"
                            placement="bottom">
                            <i class="el-input__icon el-icon-question"></i>
                        </el-tooltip>
                    </el-input>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="dialogVisibleData = true">导入商品</el-button>
                <el-button type="primary" @click="openDialogMenuType">商品名称类型</el-button>
            </el-button-group>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :isSelectColumn="false" :showsummary='true' :tablefixed='false' :tableData='tableData' :tableCols='tableCols'
            :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:100%;margin: 0">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
        <el-dialog title="导入数据" :visible.sync="dialogVisibleData" width="600px" v-dialogDrag>
            <span>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action
                            accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleData = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="商品名称类型" :visible.sync="dialogMenuType" width="480px" v-dialogDrag>
            <span>
                <el-form ref="form" :model="form" label-width="10px" max-height="600">
                    <el-form-item label="">
                        <span>
                            <el-button @click="addGoodType">新增</el-button>
                        </span>
                    </el-form-item>
                    <div style="max-height: 400px; overflow-y: auto; width: 25rem;">
                        <el-form-item label="" v-for="(item, index) in form.goodTypeList" :key="index">
                            <span>
                                类型
                                <el-input placeholder="类型" style="width: 80px; " v-model="item.goodType"
                                    maxlength="10"></el-input>
                                &nbsp;&nbsp;
                                排序
                                <el-input-number placeholder="排序" v-model="item.orderId" :step="1" step-strictly
                                    :max="999999" style="width: 120px; "></el-input-number>
                                &nbsp;&nbsp;
                                <el-button @click="removeGoodType(index)">删除</el-button>
                            </span>
                        </el-form-item>
                    </div>
                </el-form>
            </span>

            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="saveGoodType">保存</el-button>
                <el-button @click="dialogMenuType = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="编辑商品" :visible.sync="dialogEdit" width="600px" v-dialogDrag>
            <span>
                <el-form ref="form" :model="form" label-width="80px" max-height="650">
                    <el-form-item label="商品名称:">
                        <el-input placeholder="商品名称" v-model="editForm.goodName" maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item label="商品类型:">
                        <el-select v-model="editForm.goodType" placeholder="类型" clearable filterable style="width: 100%; ">
                            <el-option v-for="i in form.goodTypeList" :label="i.goodType" :value="i.goodType"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="价格:">
                        <el-input-number placeholder="价格" v-model="editForm.price" :precision="2" :min="0" :max="999999"
                            style="width: 120px; "></el-input-number>
                    </el-form-item>
                    <el-form-item label="积分:">
                        <el-input-number placeholder="多少积分" v-model="editForm.integral" :precision="2" :min="0" :max="5000"
                            style="width: 120px; "></el-input-number>
                        <!-- <el-input type="number" placeholder="多少积分" v-model="editForm.integral" maxlength="8"></el-input> -->
                    </el-form-item>
                    <el-form-item label="销量:">
                        <el-input-number placeholder="销量" v-model="editForm.salesvolume" :step="1" step-strictly :min="0"
                            :max="99999999" style="width: 120px; "></el-input-number>
                    </el-form-item>
                    <el-form-item label="状态:">
                        <el-select v-model="editForm.goodStatus" placeholder="状态" clearable filterable
                            style="width: 100%; ">
                            <el-option label="在售" value="在售"></el-option>
                            <el-option label="下架" value="下架"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="排序:">
                        <el-input-number placeholder="排序" v-model="editForm.orderId" :step="1" step-strictly :min="0"
                            :max="999999"></el-input-number>
                    </el-form-item>
                    <el-form-item label="商品图片：">
                        <yh-img-upload1 :value.sync="editForm.imageUrl" :limit="1"></yh-img-upload1>
                    </el-form-item>
                </el-form>
            </span>

            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="updateBaseSupermaketGood">保存</el-button>
                <el-button @click="dialogEdit = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="增减库存" :visible.sync="dialogVisibleStock" width="500px" v-dialogDrag>
            <el-form label-width="80px" @submit.native.prevent>
                <el-form-item label="库存:">
                    <el-input-number placeholder="库存" style="width: 200px; " v-model="stockForm.stock" :step="1"
                        step-strictly :max="999999" :min="-999999"></el-input-number>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="updateStock">保存</el-button>
                <el-button @click="dialogVisibleStock = false">关闭</el-button>
            </span>
        </el-dialog>
    </container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import container from '@/components/my-container';
import cesTable from '@/components/Table/table.vue';
import YhImgUpload1 from "@/components/upload/yh-img-upload1.vue";
import { formatLinkProCode, platformlist } from '@/utils/tools'
import {
    importBaseSupermarketGoodAsync,
    getPageBaseSupermaketGoodAsync,
    updateBaseSupermaketGoodAsync,
    delBaseSupermaketGoodAsync,
    getGoodTypeAsync,
    saveGoodTypeAsync,
    updateBaseSupermaketGoodStockAsync
} from '@/api/profit/orderfood';

const tableCols = [
    // { istrue: true, prop: 'gysName', label: '供应商名称', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'goodName', label: '商品名称', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'goodType', label: '商品类型名称', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'price', label: '价格', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'integral', label: '积分', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'salesvolume', label: '销量', width: '100', sortable: 'custom' },
    // { istrue: true, prop: 'stock', label: '库存', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'goodStatus', label: '状态', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'orderId', label: '排序', width: '80', sortable: 'custom' },
    {
        istrue: true, type: "button", label: '操作', width: "200",
        btnList: [
            { label: "编辑", handle: (that, row) => that.editBaseMenu(row) },
            // { label: "增减库存", handle: (that, row) => that.editStock(row) },
            { label: "删除", handle: (that, row) => that.delBaseSupermaketGood(row) }
        ]
    }
];

const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endDate = startDate;
//const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default ({
    name: "Users",
    components: { container, cesTable, YhImgUpload1 },
    data () {
        return {
            dialogVisibleStock: false,
            fileList: [],
            dialogEdit: false,
            stockForm: {
                stock: 0,
                id: 0
            },
            editForm: {
            },
            form: {
                goodTypeList: [],
            },
            dialogMenuType: false,
            uploadLoading: false,
            dialogVisibleData: false,
            that: this,
            filter: {
                timerange: [startDate, endDate],
                startTime: startDate,
                endTime: endDate,
                keyWord: null,
                Enabled: true,
            },
            platformlist: platformlist,
            tableCols: tableCols,
            tableHandles: null,
            tableData: [],
            total: 0,
            pager: { OrderBy: "id", IsAsc: false },
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            sels: [],
        };
    },
    mounted () {
        this.getGoodType();
        this.onSearch();
    },
    methods: {
        async updateStock () {
            let p = this.stockForm;
            const res = await updateBaseSupermaketGoodStockAsync(p);
            if (res.data) {
                this.$message({ type: 'success', message: '保存成功!' });
                this.dialogVisibleStock = false;
                this.getList();
            }
        },
        editStock (row) {
            this.dialogVisibleStock = true;
            this.stockForm.id = row.id;
            this.stockForm.stock = 0;
        },
        async openDialogMenuType () {
            this.dialogMenuType = true;
            await this.getGoodType();
        },
        async updateBaseSupermaketGood () {
            let p = this.editForm;
            const res = await updateBaseSupermaketGoodAsync(p);
            if (res.data) {
                this.$message({ type: 'success', message: '保存成功!' });
                await this.getList();
                this.dialogEdit = false;
                
            }
        },
        async saveGoodType () {
            let par = this.form.goodTypeList;
            const res = await saveGoodTypeAsync(par);
            if (res.data) {
                this.$message({ type: 'success', message: '保存成功!' });
                this.dialogMenuType = false;
                await this.getGoodType();
                await this.getList();
            }
        },
        addGoodType () {
            this.form.goodTypeList.push({
                goodType: '',
                orderId: null
            });
        },
        removeGoodType (index) {
            this.form.goodTypeList.splice(index, 1)
        },
        editBaseMenu (row) {
            this.getGoodType();
            this.editForm = {
                id: row.id,
                goodName: row.goodName,
                goodType: row.goodType,
                price: row.price,
                integral: row.integral,
                salesvolume: row.salesvolume,
                goodStatus: row.goodStatus,
                orderId: row.orderId,
                imageUrl: row.imageUrl
            }
            console.log(this.editForm, '11111')
            this.dialogEdit = true;
            setTimeout(() => {
                
            },500)
            
        },
        async getGoodType () {
            const res = await getGoodTypeAsync();
            this.form.goodTypeList = res?.data;
        },
        async delBaseSupermaketGood (row) {
            this.$confirm('确定删除, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(async _ => {
                    let par = {
                        id: row.id
                    }
                    const res = await delBaseSupermaketGoodAsync(par)
                    if (res.data) {
                        this.$message({ type: 'success', message: '删除成功!' });
                        await this.getList();
                    }
                })
                .catch(_ => { });

        },
        async onSearch () {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        async sortchange (column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.$refs.pager.setPage(1);
            await this.onSearch();
        },
        async getList () {
            if (this.filter.timerange && this.filter.timerange.length > 0) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            var that = this;
            this.listLoading = true;
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter, ...this.myfilter };
            const res = await getPageBaseSupermaketGoodAsync(params).then(res => {
                that.total = res.data?.total;
                that.tableData = res.data?.list;
            });
            this.listLoading = false;
        },
        async uploadFile () {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false
            };
            const form = new FormData();
            form.append("upfile", this.fileList[0].raw);
            //form.append("platform", 1);
            var res = await importBaseSupermarketGoodAsync(form);
            if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
            // else this.$message({ message: res.msg, type: "warning" });
            this.$refs.upload.clearFiles()
            this.uploadLoading = false;
            this.dialogVisibleData = false;
            this.fileList = [];
        },
        async uploadChange (file, fileList) {
            if (fileList.length == 2) {
                fileList.splice(1, 1);
                this.$message({ message: "只允许单文件导入", type: "warning" });
                return false;
            }
            this.fileList.push(file);
            // console.log(this.$refs.upload,'dsad',fileList)
            // if (fileList && fileList.length > 0) {
            //     var list = [];
            //     for (var i = 0; i < fileList.length; i++) {
            //         if (fileList[i].status == "success")
            //             list.push(fileList[i]);
            //         else
            //             list.push(fileList[i].raw);
            //     }
            //     this.fileList = list;
            // }
        },
        uploadRemove (file, fileList) {
            this.fileList.splice(0, 1);
        },
        submitUpload () {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return;
            }
            this.uploadLoading = true
            this.$refs.upload.submit();
        },
    }
})
</script>
<style scoped>
::v-deep .el-table__fixed-footer-wrapper tbody td {
    color: blue;
}
</style>

