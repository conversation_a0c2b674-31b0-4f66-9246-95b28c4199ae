<template>
  <my-container v-loading="pageLoading">
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      :tableData='inquirsstatisticslist' @select='selectchange' :isSelection='false' :tableCols='tableCols'
      :loading="listLoading" :summaryarry="summaryarry" style="height: 529px;">
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getinquirsstatisticsList" />
    </template>
  </my-container>
</template>
<script>
import sqinquirsstatisticsbyshop from '@/views/customerservice/douyin/sq/sqinquirsstatisticsbyshop'
import {
  getDouYinGroup,
  getDouYinPersonalEfficiencyPageList, getDouYinPersonalEfficiencyChat, exportDouYinPersonalEfficiencyPageList
} from '@/api/customerservice/douyininquirs'
import datepicker from '@/views/customerservice/datepicker'
import buschar from '@/components/Bus/buschar'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import sqinquirskfstatisticsbyshop from '@/views/customerservice/douyin/sq/sqinquirskfstatisticsbyshop'
const tableCols = [
  { istrue: true, prop: 'groupName', label: '组名称', width: '110', sortable: 'custom' },
  { istrue: true, prop: 'sname', label: '姓名', width: '80', formatter: (row) => row.sname },
  { istrue: true, prop: 'inquirs', label: '人工已接待会话量', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'receiveds', label: '人工已接待人数', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'noSatisfactionRate', label: '不满意率', width: '80', sortable: 'custom', formatter: (row) => (row.noSatisfactionRate) + "%", },
  { istrue: true, prop: 'noSatisfactions', label: '不满意人数', width: '95', sortable: 'custom', formatter: (row) => (row.noSatisfactions).toFixed(2) },
  { istrue: true, prop: 'threeResponseRate', label: '3分钟人工回复率', width: '80', sortable: 'custom', formatter: (row) => (row.threeResponseRate).toFixed(2) + "%" },
  { istrue: true, prop: 'threeResponses', label: '3分钟回复人数', width: '80', sortable: 'custom', formatter: (row) => (row.threeResponses).toFixed(2) },
  { istrue: true, prop: 'responseTime', label: '平均响应时长(秒)', width: '80', sortable: 'custom', formatter: (row) => (row.responseTime).toFixed(2) },
  { istrue: true, prop: 'satisfactionRate', label: '满意率', width: '80', sortable: 'custom', formatter: (row) => (row.satisfactionRate).toFixed(2) + "%", },
  { istrue: true, prop: 'satisfactions', label: '满意人数', width: '80', sortable: 'custom', formatter: (row) => (row.satisfactions).toFixed(2) },
  { istrue: true, prop: 'salesvol', label: '客服销售额(元)', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'ipsCount', label: '询单人数', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'payers', label: '支付人数', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'ipsRate', label: '询单转化率', width: '80', sortable: 'custom', formatter: (row) => (row.ipsRate).toFixed(2) + "%" },
  { istrue: true, prop: 'outTimes', label: '出勤人次', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '95', sortable: 'custom' },
];
export default {
  name: "storeServiceDataStatistics",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar, sqinquirsstatisticsbyshop, sqinquirskfstatisticsbyshop },
  data() {
    return {
      that: this,
      shopCode: "",//店铺编码
      filter: {
        groupType: null,
        inquirsType: null,
      },
      inquirsstatisticslist: [],//列表数据
      tableCols: tableCols,//表头
      total: 0,//总数
      summaryarry: {},//汇总
      pager: { OrderBy: "inquirs", IsAsc: false },//排序
      sels: [], // 列表选中列
      listLoading: false,//加载
      pageLoading: false,//加载
      selids: [],//选中的id
    };
  },
  async mounted() {
    // this.onSearch()
  },
  methods: {
    // 查询
    onSearch(row, filters) {
      if (row) {
        this.shopCode = row.shopCode;
      }
      if (filters) {
        this.filter = filters;
      }
      this.$refs.pager.setPage(1);
      this.getinquirsstatisticsList();
    },
    getParam() {
      if (this.filter.sdate) {
        this.filter.startDate = this.filter.sdate[0];
        this.filter.endDate = this.filter.sdate[1];
      }
      else {
        this.filter.startDate = null;
        this.filter.endDate = null;
      }
      const para = { ...this.filter };
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
        shopCode: this.shopCode,
      };
      return params;
    },
    // 获取列表
    async getinquirsstatisticsList() {
      let params = this.getParam();
      this.listLoading = true;
      const res = await getDouYinPersonalEfficiencyPageList(params);
      this.listLoading = false;
      this.total = res.data.total
      this.inquirsstatisticslist = res.data.list;
      this.summaryarry = res.data.summary;
    },
    // 选中行
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    // 排序
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
