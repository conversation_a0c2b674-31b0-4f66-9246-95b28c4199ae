<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss"
                    startPlaceholder="创建时间" endPlaceholder="创建时间" />
                <dateRange :startDate.sync="ListInfo.payStartTime" :endDate.sync="ListInfo.payEndTime" class="publicCss"
                    startPlaceholder="付款时间" endPlaceholder="付款时间" />
                <el-input v-model="ListInfo.internalOrderNo" placeholder="内部订单号" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.buyUserId" placeholder="买家ID" maxlength="200" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.orderNo" placeholder="订单号" maxlength="200" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.createdUserName" placeholder="创建人" maxlength="50" clearable
                    class="publicCss" />
                <el-select v-model.trim="ListInfo.analysisError" placeholder="状态" class="publicCss" clearable>
                    <el-option label="异常" :value="true" />
                    <el-option label="正常" :value="false" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="importProps" v-if="checkPermission('yggrmImport')">导入</el-button>
                <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
            </div>
        </template>
        <vxetablebase :id="'yggrmQuotePage202408041634'" ref=" table" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' :toolbarshow="false" @sortchange='sortchange' :tableData='tableData'
            :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0"
            v-loading="loading" :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="120" align="center">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="viewProps(row.id)">编辑</el-button>
                            <el-button type="text"
                                v-if="checkPermission('api:inventory:CustomNormsGoods:DeleteYGGRMCSRecord')"
                                @click="yggrmDelete(row.id)">删除</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <!-- 校验买家id -->
        <el-dialog title="新增" :visible.sync="buyersIdVisable" width="15%" :close-on-click-modal="false" v-dialogDrag>
            <div style="display: flex;flex-direction: column;justify-content: center;align-items: center;">
                <el-input v-model="buyUserInfo.buyUserId" placeholder="请输入买家ID" maxlength="50" clearable
                    style="width: 180px;" />
                <div class="btnGroup">
                    <el-button @click="buyersIdVisable = false">取消</el-button>
                    <el-button type="primary" @click="verifyBuyerId" v-throttle="2000">确定</el-button>
                </div>
            </div>
        </el-dialog>

        <!-- 新增 -->
        <el-drawer title="编辑" :visible.sync="drawer" direction="rtl" size="90%" :wrapperClosable="false">
            <el-form :model="ruleForm" status-icon ref="ruleForm" label-width="100px" class="demo-ruleForm"
                style="padding: 16px;box-sizing: border-box;" :rules="rules">
                <el-form-item label="买家id:" prop="pass">
                    <div>{{ ruleForm.buyUserId }}</div>
                </el-form-item>
                <!-- <el-form-item label="聊天记录:" prop="chatPicUrl">
                    <div class="chatPicUrl">
                        <uploadimgFile v-if="drawer" ref="uploadimgFile" :accepttyes="accepttyes" :isImage="true"
                            :uploadInfo="chatUrls" :keys="[1, 1]" @callback="getImg" :imgmaxsize="10" :limit="10"
                            :multiple="true">
                        </uploadimgFile>
                        <span class="picTips">提示:点击灰框可直接贴图！！！</span>
                    </div>
                </el-form-item> -->
                <el-form-item label="订单备注:" prop="orderRemark">
                    <el-tooltip class="item" effect="dark" :content="ruleForm.orderRemark" placement="top-start">
                        <div class="orderRemark">{{ ruleForm.orderRemark }}</div>
                    </el-tooltip>
                </el-form-item>
                <el-form-item label="规格报价明细:" prop="pass">
                    <span style="color: red;">(禁止截图给客户！！！)</span>
                    <el-button style="margin-left: 20px;" @click="addProps" type="primary">新增一行</el-button>
                </el-form-item>
                <div style="height: 400px;">
                    <el-table :data="ruleForm.dtls" style="width: 100%;height:100%" max-height="400">
                        <el-table-column prop="customType" label="款式" width="75">
                            <template #header="{ column }">
                                <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>款式
                            </template>
                            <template #default="{ row, $index }">
                                <el-select v-model="row.customType" placeholder="款式"
                                    @change="changeType($event, $index)">
                                    <el-option label="定制款" :value="1" />
                                    <el-option label="常规款" :value="2" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="norms" label="规格" width="150">
                            <template #header="{ column }">
                                <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>规格
                            </template>
                            <template #default="{ row, $index }">
                                <el-select v-model="row.norms" placeholder="规格" filterable
                                    @change="changeName('gg', $index)">
                                    <!-- 定制款规格 -->
                                    <el-option v-if="row.customType && row.customType == 1" v-for="item in dzList"
                                        :key="item.norms" :label="item.norms" :value="item.norms" />
                                    <!-- 常规款 -->
                                    <el-option v-if="row.customType && row.customType == 2" v-for="item in cgList"
                                        :key="item.norms" :label="item.norms" :value="item.norms" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="bbType" label="是否包边" width="75">
                            <template #header="{ column }">
                                <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>是否包边
                            </template>
                            <template #default="{ row, $index }">
                                <el-select v-model="row.bbType" placeholder="包边" @change="changeName('bb', $index)"
                                    :disabled="row.customType == 2">
                                    <el-option key="不包边" label="不包边" value="不包边" />
                                    <el-option key="包黑边" label="包黑边" value="包黑边" />
                                    <el-option key="包灰边" label="包灰边" value="包灰边" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="isDK" label="是否打扣" width="50">
                            <template #default="{ row, $index }">
                                <div style="display: flex;justify-content: center;">
                                    <el-checkbox v-model="row.isDK" @change="changeName('dk', $index)"
                                        :disabled="row.customType == 2" />
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="isNeedXP" label="是否需要吸盘" width="60">
                            <template #default="{ row, $index }">
                                <div style="display: flex;justify-content: center;">
                                    <el-checkbox v-model="row.isNeedXP" @change="changeName('xp', $index)"
                                        :disabled="row.customType == 2" />
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="isNeedMST" label="是否送魔术贴" width="80">
                            <template #default="{ row, $index }">
                                <div style="display: flex;justify-content: center;">
                                    <el-checkbox v-model="row.isNeedMST" @change="changeName('mst', $index)"
                                        :disabled="row.customType == 2" />
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="fullNormsName" label="规格全称" width="200">
                            <template #default="{ row }">
                                <el-input v-model="row.fullNormsName" placeholder="规格全称" disabled />
                            </template>
                        </el-table-column>
                        <el-table-column prop="sheetSquareSaleAmount" label="1平方售价" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.sheetSquareSaleAmount" :min="0" :max="10000"
                                    :precision="2" :controls="false" label="1平方售价" class="iptCss"
                                    @change="changePrice($index)" disabled />
                            </template>
                        </el-table-column>
                        <el-table-column prop="goodsCode" label="商品编码" width="85" v-if="false">
                            <template #default="{ row }">
                                <el-input v-model="row.goodsCode" placeholder="商品编码" disabled />
                            </template>
                        </el-table-column>
                        <el-table-column prop="sheetDKCount" label="卡扣数" width="85">
                            <template #default="{ row }">
                                <el-input-number v-model="row.sheetDKCount" :min="0" :max="10000" :precision="0"
                                    :controls="false" label="包边布一米价格" class="iptCss" :disabled="row.customType == 2" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="sheetLength" label="长(米)" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.sheetLength" :min="0" :max="10000" :precision="4"
                                    :controls="false" label="长(米)" class="iptCss" @change="changePrice($index)"
                                    :disabled="row.customType == 2" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="sheetWidth" label="宽(米)" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.sheetWidth" :min="0" :max="10000" :precision="4"
                                    :controls="false" label="宽(米)" class="iptCss" @change="changePrice($index)"
                                    :disabled="row.customType == 2" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="sheetCount" label="张" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.sheetCount" :min="0" :max="10000" :precision="0"
                                    :controls="false" label="张" class="iptCss" @change="changePrice($index)" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="totalAmount" label="成本" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.totalCost" :min="0" :max="9999999" :precision="4"
                                    :controls="false" label="成本" class="iptCss" @change="changePrice($index)"
                                    disabled />
                            </template>
                        </el-table-column>
                        <!-- <el-table-column prop="discount" label="折扣" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.discount" :min="0" :max="10" :precision="2"
                                    :controls="false" label="折扣" class="iptCss" @change="changePrice($index)" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="actualAmount" label="折后价" width="85">
                            <template #default="{ row }">
                                <el-input-number v-model="row.actualAmount" :min="0" :max="9999999" :precision="2"
                                    :controls="false" label="折后价" class="iptCss" disabled />
                            </template>
                        </el-table-column> -->
                        <el-table-column prop="remark" label="备注" width="auto">
                            <template #default="{ row }">
                                <el-input v-model="row.remark" maxlength="50" clearable placeholder="备注" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="orderFreightBagFee" label="操作" width="140">
                            <template #default="{ row, $index }">
                                <div style="display: flex;">
                                    <el-button style="margin-left: 10px;" type="warning"
                                        @click="batchSet(row)">复制</el-button>
                                    <el-button type="danger" @click="() => {
                                        ruleForm.dtls.splice($index, 1)
                                        changePrice($index)
                                    }">删除</el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <el-form-item label="总成本:" prop="pass">
                    <div>{{ getSimilarity }}</div>
                </el-form-item>
                <el-form-item label="实际报价:" prop="pass">
                    <el-input-number v-model="ruleForm.actualTotalAmount" :min="0" :max="9999999" :precision="2"
                        :controls="false" label="实际报价" class="iptCss" />
                </el-form-item>
                <div style="display: flex;justify-content: end;margin-top: 100px;">
                    <el-button type="primary" @click="submitForm">确认修改</el-button>
                    <!-- <el-button type="primary" @click="openOrder">订单提交</el-button>
                    <el-button type="primary" @click="submitForm(false)" v-throttle="5000">非订单提交</el-button>
                    <el-button type="primary" @click="copyProps('bj')">复制报价</el-button>
                    <el-button type="primary" @click="copyProps('bz')">复制加工备注</el-button> -->
                </div>
            </el-form>
        </el-drawer>

        <el-dialog :visible.sync="copyVisable" width="15%" height="100">
            <div style="margin-left: 40px;">
                复制<el-input-number style="width: 40px;" v-model="copyRowCount" :min="0" :max="15" :precision="0"
                    :controls="false" />条
                <el-button style="margin-left: 10px;" type="primary" @click="doCopy">确定</el-button>
            </div>
        </el-dialog>

        <!-- <el-dialog title="订单提交" :visible.sync="orderVisable" width="15%" :close-on-click-modal="false" v-dialogDrag>
            <div style="display: flex;flex-direction: column;justify-content: center;align-items: center;">
                <el-input v-model="ruleForm.orderNo" placeholder="请输入订单号" maxlength="50" clearable
                    style="width: 180px;" />
                <div class="btnGroup">
                    <el-button @click="orderVisable = false">取消</el-button>
                    <el-button type="primary" @click="submitForm(true)" v-throttle="5000">确定</el-button>
                </div>
            </div>
        </el-dialog> -->

        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-date-picker v-model="importTimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始时间" end-placeholder="结束时间" :picker-options="pickerOptions"
                    style="width: 250px;margin:0 10px 10px 0;" :value-format="'yyyy-MM-dd'" @change="importChangeTime">
                </el-date-picker>
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import dateRange from "@/components/date-range/index.vue";
import dayjs from 'dayjs'
import decimal from '@/utils/decimal'
import {
    getYGGRMCSRecordPageList,
    getYGGRMCSRecordByBuyUserId,
    saveYGGRMCSRecord,
    getCostSetByYGGRMCache,
    getYGGRMCSRecordById,
    importCustomMadeMultipleAsync,
    exportYGGRMCSRecordList,
    deleteYGGRMCSRecord
} from '@/api/inventory/customNormsGoods'


const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'buyUserId', label: '买家ID', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'internalOrderNo', label: '内部订单号', type: 'orderLogInfo', orderType: 'orderNoInner' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '订单编号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdUserName', label: '创建人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '创建时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'payTime', label: '付款时间', },
    { sortable: 'custom', width: '65', align: 'center', prop: 'totalSheetCount', label: '总张数', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalCost', label: '总成本', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'actualTotalAmount', label: '实际报价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'analysisError', label: '状态', formatter: (row) => row.analysisError == false ? '正常' : '异常' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shipmentStatus', label: '订单状态', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'updatedUserName', label: '修改人', },
]
const ys = {
    norms: '规格',
    fullNormsName: '规格全称',
    sheetSquareSaleAmount: '1平方售价',
    sheetDKCount: '打扣数',
    sheetWidth: '单张宽',
    sheetLength: '单张长',
    sheetCount: '张数',
    // totalAmount: '总价',
    totalCosr: '成本',
    // discount: '折扣',
    // actualAmount: '实际报价',
    remark: '备注',
}
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, uploadimgFile, dateRange
    },
    data() {
        return {
            ys,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'createdTime',
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
                payStartTime: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),//付款开始时间
                payEndTime: dayjs().format('YYYY-MM-DD'),//付款结束时间
                buyUserId: null,//买家ID
                orderNo: null,//订单号
                createdUserName: null,//创建人
                internalOrderNo: null,//内部订单号
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            payTimeRanges: [],
            buyersIdVisable: false,
            buyUserInfo: {
                buyUserId: null,
                id: null
            },
            drawer: false,
            ruleForm: {
                buyUserId: null,//买家id
                orderNo: null,//订单号
                totalAmount: null,//总价
                actualTotalAmount: null,//实际总价
                // chatPicUrl: null,//聊天记录
                dtls: [
                    {
                        // bbColor: '无',//包边颜色
                        goodsCode: null,//商品编码
                        norms: null,//规格
                        bbType: null,//是否包边
                        isDK: true,//是否打扣
                        isNeedXP: true,//是否需要吸盘
                        fullNormsName: null,//规格全称
                        remark: null,//备注
                        // discount: 1,//折扣
                        sheetSquareSaleAmount: null,//1平方售价
                        sheetDKCount: 6,//打扣数
                        sheetWidth: null,//单张宽
                        sheetLength: null,//单张长
                        sheetCount: null,//张数
                        totalAmount: null,//总价
                        // actualAmount: null,//实际报价
                    }
                ]
            },
            chatUrls: [],
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            isView: false,
            catchList: [],
            orderVisable: false,
            rules: {
                // chatPicUrl: [
                //     { required: true, message: '请上传聊天记录', trigger: 'change' }
                // ]
            },
            dzList: [],//定制款规格
            cgList: [],//常规款规格
            importVisible: false,
            importLoading: false,
            fileList: [],
            importTimeRanges: [],
            importTime: {
                payStartTime: null,
                payEndTime: null
            },
            isExport: false,
            copyRow: {},
            copyRowCount: 0,
            copyVisable: false,
        }
    },
    computed: {
        getSimilarity() {
            let sum = 0
            this.ruleForm.dtls.forEach(item => {
                if (item.totalCost !== null && item.totalCost !== undefined && item.totalCost !== '') {
                    sum = decimal(sum, item.totalCost, 4, '+')
                }
            })
            this.ruleForm.totalCost = sum
            return sum
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async exportProps() {
            this.isExport = true
            await exportYGGRMCSRecordList(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '阳光隔热膜报价测算' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        importChangeTime(e) {
            this.importTime.payStartTime = e ? e[0] : null
            this.importTime.payEndTime = e ? e[1] : null
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            if (!this.importTime.payStartTime || !this.importTime.payEndTime) return this.$message.error('请选择时间')
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            form.append("payStartTime", this.importTime.payStartTime);
            form.append("payEndTime", this.importTime.payEndTime);
            this.importLoading = true
            await importCustomMadeMultipleAsync(form).then(({ success }) => {
                if (success) {
                    this.$message.success('导入成功')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.importTimeRanges = []
            this.importTime = {
                payStartTime: null,
                payEndTime: null
            }
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        //更改常规款规格
        changeCgNorms(e, i) {
            if (this.ruleForm.dtls[i].customType == 2 && e) {
                //匹配数据 
                let res = this.cgList.filter(item => item.norms == e)
            }
        },
        //更改款式
        changeType(val, i) {
            //清空其他值
            this.ruleForm.dtls[i].norms = null
            this.ruleForm.dtls[i].bbType = null
            this.ruleForm.dtls[i].isDK = val == 1 ? true : false
            this.ruleForm.dtls[i].isNeedXP = val == 1 ? true : false
            this.ruleForm.dtls[i].fullNormsName = null
            this.ruleForm.dtls[i].remark = null
            this.ruleForm.dtls[i].sheetSquareSaleAmount = null
            this.ruleForm.dtls[i].sheetDKCount = val == 1 ? 6 : null
            this.ruleForm.dtls[i].sheetWidth = null
            this.ruleForm.dtls[i].sheetLength = null
            this.ruleForm.dtls[i].sheetCount = null
            this.ruleForm.dtls[i].totalAmount = null
            this.ruleForm.dtls[i].goodsCode = null
            this.ruleForm.dtls[i].totalCost = 0
        },
        openOrder() {
            this.ruleForm.orderNo = null
            this.orderVisable = true
        },
        async viewProps(id) {
            await this.getCacheList()
            this.buyUserInfo.id = id
            this.buyUserInfo.buyUserId = null
            const { data, success } = await getYGGRMCSRecordById(this.buyUserInfo)
            if (success) {
                if (data) {
                    this.ruleForm = data
                    this.ruleForm.dtls = data.dtls.map(item => {
                        return {
                            cost: item.cost,
                            customType: item.customType ? item.customType : null,//款式
                            norms: item.norms ? item.norms : null,//规格
                            bbType: item.bbType,//是否包边
                            isDK: item.isDK,//是否打扣
                            isNeedXP: item.isNeedXP,//是否需要吸盘
                            isNeedMST: item.isNeedMST,//是否需要魔术贴
                            fullNormsName: item.fullNormsName,//规格全称
                            sheetSquareSaleAmount: item.sheetSquareSaleAmount,//1平方售价
                            sheetDKCount: item.sheetDKCount,//打扣数
                            sheetWidth: item.sheetWidth,//单张宽
                            sheetLength: item.sheetLength,//单张长
                            sheetCount: item.sheetCount,//张数
                            totalAmount: item.totalAmount,//总价
                            remark: item.remark,//备注
                            goodsCode: item.goodsCode,//商品编码
                            norsItem: item.norsItem,//规格数据
                            totalCost: item.totalCost//成本
                        }
                    })
                }
                this.drawer = true
            }
        },
        copyProps(type) {
            this.validate()
            let textarea = document.createElement("textarea")
            let e = ''
            let totalSheetCount = this.ruleForm.dtls.reduce((total, item) => total + item.sheetCount, 0);
            if (type == 'bj') {
                this.ruleForm.dtls.forEach(item => {
                    e += `定制:${item.fullNormsName},卡扣数:${item.sheetDKCount},#${item.goodsCode ? item.goodsCode : ''}@${item.sheetLength}*${item.sheetWidth}*${item.sheetCount}*张#,折扣后总价:${item.actualAmount}\n`
                })
                e = e + `共${totalSheetCount}张,合计:${this.ruleForm.totalAmount}元,优惠后总价:${this.ruleForm.actualTotalAmount}元\n`
            } else {
                this.ruleForm.dtls.forEach(item => {
                    e += `定制:${item.fullNormsName},卡扣数:${item.sheetDKCount},#${item.goodsCode ? item.goodsCode : ''}@${item.sheetLength}*${item.sheetWidth}*${item.sheetCount}*张#,备注:${item.remark ? item.remark : ''}\n`
                })
                e = e + `共${totalSheetCount}张\n`
            }
            textarea.value = e
            textarea.readOnly = "readOnly"
            document.body.appendChild(textarea)
            textarea.select()
            let result = document.execCommand("copy")
            if (result) {
                this.$message({
                    message: '复制成功',
                    type: 'success'
                })
            }
            textarea.remove()
        },
        validate() {
            const dzValidate = ['norms', 'bbType', 'fullNormsName', 'sheetSquareSaleAmount', 'sheetLength', 'sheetWidth', 'sheetCount', 'totalCost']
            const cgValidate = ['sheetCount', 'totalCost', 'norms', 'customType']
            const dzMap = {
                customType: '款式',
                norms: '规格',
                bbType: '是否包边',
                fullNormsName: '规格全称',
                sheetSquareSaleAmount: '1平方售价',
                sheetLength: '单张长',
                sheetWidth: '单张宽',
                sheetCount: '张数',
                totalCost: '成本',
            }
            const cgMap = {
                customType: '款式',
                norms: '规格',
                sheetCount: '张数',
                totalCost: '成本'
            }
            if (this.ruleForm.dtls.length == 0) return this.$message.error('至少添加一条数据')
            this.ruleForm.dtls.forEach((item, i) => {
                if (item.customType == 1 || item.customType == null) {
                    dzValidate.forEach(key => {
                        if (item[key] === null || item[key] === undefined || item[key] === '' || item[key] <= 0) {
                            this.$message.error(`第${i + 1}行${dzMap[key]}不能为空或小于等于0`)
                            throw new Error(`第${i + 1}行${dzMap[key]}不能为空或小于等于0`)
                        }
                    })
                } else {
                    cgValidate.forEach(key => {
                        if (item[key] === null || item[key] === undefined || item[key] === '' || item[key] <= 0) {
                            this.$message.error(`第${i + 1}行${cgMap[key]}不能为空或小于等于0`)
                            throw new Error(`第${i + 1}行${cgMap[key]}不能为空或小于等于0`)
                        }
                    })
                }
            })
            if (this.ruleForm.actualTotalAmount <= 0 || this.ruleForm.actualTotalAmount === null || this.ruleForm.actualTotalAmount === undefined) {
                this.$message.error('实际报价不能为空或小于等于0')
                throw new Error('实际报价不能为空或小于等于0')
            }
        },
        async submitForm() {
            if (this.ruleForm.dtls.length == 0) return this.$message.error('至少添加一条数据')
            this.validate()
            const { data, success } = await saveYGGRMCSRecord(this.ruleForm)
            if (success) {
                await this.getList()
                this.$message.success('提交成功')
                this.drawer = false
                this.orderVisable = false
            } else {
                this.$message.error('提交失败')
            }
        },
        addProps() {
            this.ruleForm.dtls.push({
                norms: null,//规格
                bbType: null,//是否包边
                isDK: true,//是否打扣
                isNeedXP: true,//是否需要吸盘
                isNeedMST: true,//是否需要魔术贴
                fullNormsName: null,//规格全称
                remark: null,//备注
                // discount: 1,//折扣
                sheetSquareSaleAmount: 0,//1平方售价
                sheetDKCount: 6,//打扣数
                sheetWidth: 0,//单张宽
                sheetLength: 0,//单张长
                sheetCount: 0,//张数
                // bbColor: '无',//包边颜色
                goodsCode: null,//商品编码
                totalCost: 0,
                customType: null,
            })
        },
        changePrice() {
            /*
            长*宽*张*单张价格=单张平方成本
            (长+宽)*2*张*包边布一米价格 = 包边成本
            张*包边加工费 = 包边加工成本
            张*6个孔铁成本 = 孔铁成本
            张*打扣加工费= 打扣成本
            张*6个吸盘 = 吸盘成本
            张*抹布 = 抹布成本
            张*喷壶 = 喷壶成本
            张*单张裁剪 = 裁剪成本
            快递袋费 = 最大快递袋费
            打包费 = 最大打包费
            快递袋费分摊 = (单行售价/总售价)*快递袋费
            打包费分摊 = (单行售价/总售价)*打包费
            */
            //计算价格 decimal norsItem
            this.ruleForm.dtls.forEach(item => {
                if (!item.norsItem) {
                    item.norsItem = {
                        sheetSquareCost: 0,//单张平方成本
                        metreBBClothCost: 0,//包边布一米价格
                        sheetBBPackProcessCost: 0,//包边加工费
                        sheetSixIronbuckleCost: 0,//孔铁成本
                        sheetDKPackProcessCost: 0,//打扣成本
                        sheetSixXPCost: 0,//吸盘成本
                        sheetDishClothcCost: 0,//抹布成本
                        sheetSprayKettleCost: 0,//喷壶成本
                        sheetCroppingCost: 0,//裁剪成本
                        orderFreightBagFee: 0,//快递袋费
                        orderPackProcessFee: 0, //打包费
                        cost: 0//成本
                    }
                    throw new Error('请先选择规格')
                } else if (item.customType == 1) {
                    //周长 = (长+宽)*2
                    item.circumference = decimal(decimal(item.sheetLength, item.sheetWidth, 4, '+'), 2, 4, '*')
                    //平方成本 = 长 * 宽 * 张数 * 单张价格
                    item.oneSquareCost = decimal(decimal(decimal(item.sheetLength, item.sheetWidth, 4, '*'), item.sheetCount, 4, '*'), item.norsItem.sheetSquareCost, 4, '*')
                    //包边成本 = (长+宽) * 2 * 张数 * 包边布一米价格
                    item.bbCost = decimal(decimal(decimal(decimal(item.sheetLength, item.sheetWidth, 4, '+'), 2, 4, '*'), item.sheetCount, 4, '*'), item.norsItem.metreBBClothCost, 4, '*')
                    //包边加工成本 = 张数 * (包边加工费 * 周长)
                    // item.bbProcessCost = decimal(item.sheetCount, item.norsItem.sheetBBPackProcessCost, 4, '*')
                    item.bbProcessCost = decimal(decimal(item.circumference, item.norsItem.sheetBBPackProcessCost, 4, '*'), item.sheetCount, 4, '*')
                    //孔铁成本 = 张数 * 6个孔铁成本
                    item.sixIronbuckleCost = decimal(item.sheetCount, item.norsItem.sheetSixIronbuckleCost, 4, '*')
                    //打扣成本 = 张数*打扣加工费
                    item.dkProcessCost = decimal(item.sheetCount, item.norsItem.sheetDKPackProcessCost, 4, '*')
                    //计算出来a
                    //吸盘成本 = 张数 * 6个吸盘
                    item.sixXPCost = decimal(item.sheetCount, item.norsItem.sheetSixXPCost, 4, '*')
                    //抹布成本 = 张数 * 抹布
                    item.dishClothcCost = decimal(item.sheetCount, item.norsItem.sheetDishClothcCost, 4, '*')
                    //喷壶成本 = 张数 * 喷壶
                    item.sprayKettleCost = decimal(item.sheetCount, item.norsItem.sheetSprayKettleCost, 4, '*')
                    //裁剪成本 = 张数 * 单张裁剪
                    item.croppingCost = decimal(item.sheetCount, item.norsItem.sheetCroppingCost, 4, '*')
                    //魔术贴成本
                    item.mstCost = decimal(item.sheetCount, item.norsItem.sheetMSTCost, 4, '*')
                    //总价 = 长 * 宽 * 张数 * 单张平方售价
                    item.totalAmount = decimal(decimal(decimal(item.sheetLength, item.sheetWidth, 2, '*'), item.sheetCount, 2, '*'), item.sheetSquareSaleAmount, 2, '*')
                    const a = decimal(decimal(decimal(decimal(item.oneSquareCost, item.bbCost, 4, '+'), item.bbProcessCost, 4, '+'), item.sixIronbuckleCost, 4, '+'), item.dkProcessCost, 4, '+')
                    //总成本 = 平方成本 + 包边成本 + 包边加工成本 + 孔铁成本 + 打扣成本 + 吸盘成本 + 抹布成本 + 喷壶成本 + 裁剪成本 + 魔术贴成本
                    item.totalCost = decimal(decimal(decimal(decimal(decimal(a, item.sixXPCost, 4, '+'), item.dishClothcCost, 4, '+'), item.sprayKettleCost, 4, '+'), item.croppingCost, 4, '+'), item.mstCost, 4, '+')

                } else if (item.customType == 2) {
                    item.totalCost = decimal(item.sheetCount, item.norsItem.cost, 4, '*')
                    item.totalAmount = 0
                }
            })
            //以上是计算成本
            // 定制款最大的快递袋费 ==> 更改为最大的快递袋费
            const DZmaxOrderFreightBagFee = this.ruleForm.dtls.reduce((total, item) => {
                // return item.customType == 1 ? total > item.norsItem.orderFreightBagFee ? total : item.norsItem.orderFreightBagFee : total
                return total > item.norsItem.orderFreightBagFee ? total : item.norsItem.orderFreightBagFee
            }, 0)

            //定制款最大的打包费 ==> 更改为最大的打包费
            const DZmaxOrderPackProcessFee = this.ruleForm.dtls.reduce((total, item) => {
                // return item.customType == 1 ? total > item.norsItem.orderPackProcessFee ? total : item.norsItem.orderPackProcessFee : total
                return total > item.norsItem.orderPackProcessFee ? total : item.norsItem.orderPackProcessFee
            }, 0)
            //计算定制款总售价 ==> 所有订单的总价之和(定制款取的是totalAmount,常规款取的是totalCost)
            const DZtotalAmount = this.ruleForm.dtls.reduce((total, item) => {
                // return item.customType == 1 ? decimal(total, item.totalAmount, 4, '+') : total
                //如果是定制款就加上
                return decimal(total, item.totalCost, 4, '+')
            }, 0)
            // console.log(DZtotalAmount, 'DZtotalAmount');

            //计算单行定制款快递袋费分摊 = (单行售价/总售价)*快递袋费
            this.ruleForm.dtls.forEach((item, i) => {
                if (item.customType == 1) {
                    //单行定制款快递袋费分摊
                    item.orderFreightBagFeeFt = decimal(decimal(item.totalCost, DZtotalAmount, 4, '/'), DZmaxOrderFreightBagFee, 4, '*')
                    //单行定制款打包费分摊
                    item.orderPackProcessFeeFt = decimal(decimal(item.totalCost, DZtotalAmount, 4, '/'), DZmaxOrderPackProcessFee, 4, '*')
                } else if (item.customType == 2) {
                    //单行常规款快递袋费分摊
                    item.orderFreightBagFeeFt = decimal(decimal(item.totalCost, DZtotalAmount, 4, '/'), DZmaxOrderFreightBagFee, 4, '*')
                    // //单行常规款打包费分摊
                    item.orderPackProcessFeeFt = decimal(decimal(item.totalCost, DZtotalAmount, 4, '/'), DZmaxOrderPackProcessFee, 4, '*')
                }
            })

            //计算单行定制款快递袋费的总和(分摊后的) ==> 变更为所有数据的快递袋费分摊之和
            const DZtotalOrderFreightBagFeeFt = this.ruleForm.dtls.reduce((total, item) => {
                // return (item.customType == 1) ? decimal(total, item.orderFreightBagFeeFt, 4, '+') : total
                return decimal(total, item.orderFreightBagFeeFt, 4, '+')
            }, 0)

            //如果单行定制款快递袋费的总和 不等于 最大的快递袋费 并且 最大快递袋费不等于0,就将最大的快递袋费减去单行定制款快递袋费的总和,并且给单行最大的快递袋费分摊进行补差
            if (DZmaxOrderFreightBagFee != DZtotalOrderFreightBagFeeFt) {
                //找出定制款最大的快递袋费分摊 ==> 更改为最大的快递袋费分摊
                const DZmaxOrderFreightBagFeeFt = this.ruleForm.dtls.reduce((total, item) => {
                    if (item.customType) {
                        return total > item.orderFreightBagFeeFt ? total : item.orderFreightBagFeeFt
                    } else {
                        return 0
                    }
                }, 0)
                //找出最大的快递袋费分摊的下标
                const index = this.ruleForm.dtls.findIndex(item => item.orderFreightBagFeeFt == DZmaxOrderFreightBagFeeFt)

                //将最大的快递袋费分摊减去差值
                this.ruleForm.dtls[index].orderFreightBagFeeFt = decimal(DZmaxOrderFreightBagFeeFt, decimal(DZmaxOrderFreightBagFee, DZtotalOrderFreightBagFeeFt, 4, '-'), 4, '+')
            }

            //计算单行定制款打包费的总和(分摊后的)  ==> 变更为所有数据的打包费分摊之和
            const DZtotalOrderPackProcessFeeFt = this.ruleForm.dtls.reduce((total, item) => {
                // return (item.customType == 1) ? decimal(total, item.orderPackProcessFeeFt, 4, '+') : total
                return decimal(total, item.orderPackProcessFeeFt, 4, '+')
            }, 0)

            //如果单行定制款打包费的总和 不等于 最大的打包费 并且 最大打包费不等于0,就将最大的打包费减去单行定制款打包费的总和,并且给单行最大的打包费分摊进行补差
            if (DZmaxOrderPackProcessFee != DZtotalOrderPackProcessFeeFt) {
                //找出定制款最大的打包费分摊 ==> 更改为最大的打包费分摊
                const DZmaxOrderPackProcessFeeFt = this.ruleForm.dtls.reduce((total, item) => {
                    // return (item.customType == 1) ? total > item.orderPackProcessFeeFt ? total : item.orderPackProcessFeeFt : total
                    // return total > item.orderPackProcessFeeFt ? total : item.orderPackProcessFeeFt
                    if (item.customType) {
                        return total > item.orderPackProcessFeeFt ? total : item.orderPackProcessFeeFt
                    } else {
                        return 0
                    }
                }, 0)
                //找出最大的打包费分摊的下标
                const index = this.ruleForm.dtls.findIndex(item => item.orderPackProcessFeeFt == DZmaxOrderPackProcessFeeFt)
                //将最大的打包费分摊减去差值
                this.ruleForm.dtls[index].orderPackProcessFeeFt = decimal(DZmaxOrderPackProcessFeeFt, decimal(DZmaxOrderPackProcessFee, DZtotalOrderPackProcessFeeFt, 4, '-'), 4, '+')
            }

            //单行总成本 = 总成本 + 快递袋费分摊 + 打包费分摊 (定制款)  ==> 变更为总成本 + 快递袋费分摊 + 打包费分摊 (所有款式)
            this.ruleForm.dtls.forEach(item => {
                // if ((item.customType == 1)) {
                item.totalCost = decimal(decimal(item.totalCost, item.orderFreightBagFeeFt, 4, '+'), item.orderPackProcessFeeFt, 4, '+')
                // }
            })

            //成本合计
            this.ruleForm.totalCost = this.ruleForm.dtls.reduce((total, item) => {
                return decimal(total, item.totalCost, 4, '+')
            }, 0)

            //总售价
            this.ruleForm.totalAmount = this.ruleForm.dtls.reduce((total, item) => {
                return decimal(total, item.totalAmount, 2, '+')
            }, 0)
        },
        async changeName(type, i) {
            let res = []
            if (this.ruleForm.dtls[i].customType == 1) {
                res = this.catchList.filter(item => item.norms == this.ruleForm.dtls[i].norms && item.bbType == this.ruleForm.dtls[i].bbType && item.isDK == this.ruleForm.dtls[i].isDK && item.isNeedXP == this.ruleForm.dtls[i].isNeedXP && item.isNeedMST == this.ruleForm.dtls[i].isNeedMST)
            } else if (this.ruleForm.dtls[i].customType == 2) {
                // 如果是常规款,就用匹配出来的成本价*张数
                const result = this.cgList.filter(item => item.norms == this.ruleForm.dtls[i].norms)[0]
                this.ruleForm.dtls[i].cgCost = result.cost
                this.ruleForm.dtls[i].goodsCode = result.goodsCode
                this.ruleForm.dtls[i].norsItem = result
            }
            console.log(res, 'res');
            //如果是定制款才匹配数据
            if (this.ruleForm.dtls[i].customType == 1) {
                if (res.length > 0) {
                    this.ruleForm.dtls[i].fullNormsName = res[0].fullNormsName
                    this.ruleForm.dtls[i].sheetSquareSaleAmount = res[0].sheetSquareSaleAmount
                    this.ruleForm.dtls[i].goodsCode = res[0].goodsCode
                    //将后面的数据全部清空
                    this.ruleForm.dtls[i].sheetDKCount = this.ruleForm.dtls[i].isDK ? 6 : null
                    this.ruleForm.dtls[i].sheetLength = null
                    this.ruleForm.dtls[i].sheetWidth = null
                    this.ruleForm.dtls[i].sheetCount = null
                    this.ruleForm.dtls[i].totalAmount = null
                    this.ruleForm.dtls[i].remark = null
                    this.ruleForm.dtls[i].norsItem = res[0]//找出匹配的设置数据
                    this.ruleForm.dtls[i].totalCost = 0//成本
                } else {
                    this.ruleForm.dtls[i].fullNormsName = null
                    this.ruleForm.dtls[i].sheetDKCount = this.ruleForm.dtls[i].isDK ? 6 : null
                    this.ruleForm.dtls[i].sheetSquareSaleAmount = null
                    this.ruleForm.dtls[i].sheetLength = null
                    this.ruleForm.dtls[i].sheetWidth = null
                    this.ruleForm.dtls[i].sheetCount = null
                    this.ruleForm.dtls[i].totalAmount = null
                    this.ruleForm.dtls[i].remark = null
                    this.ruleForm.dtls[i].goodsCode = null
                    this.ruleForm.dtls[i].norsItem = null
                    this.ruleForm.dtls[i].totalCost = 0//成本
                }
            }
            if (type == 'dk' && this.ruleForm.dtls[i].customType == 1) {
                this.ruleForm.dtls[i].sheetDKCount = this.ruleForm.dtls[i].isDK ? 6 : null
            }
            if (type == 'gg' && this.ruleForm.dtls[i].customType == 2 && this.ruleForm.dtls[i].norms) {
                //清空其他值
                this.ruleForm.dtls[i].sheetCount = null
                this.ruleForm.dtls[i].totalAmount = null
                this.ruleForm.dtls[i].remark = null
                this.ruleForm.dtls[i].goodsCode = this.ruleForm.dtls[i].norsItem.goodsCode
                this.ruleForm.dtls[i].sheetWidth = null
                this.ruleForm.dtls[i].sheetLength = null
                this.ruleForm.dtls[i].sheetCount = null
                this.ruleForm.dtls[i].totalCost = null
            }
        },
        async verifyBuyerId() {
            this.buyUserInfo.id = null
            if (!this.buyUserInfo.buyUserId) return this.$message.error('请输入买家ID')
            this.ruleForm.buyUserId = this.buyUserInfo.buyUserId
            const { data, success } = await getYGGRMCSRecordByBuyUserId(this.buyUserInfo)
            if (success) {
                if (data) {
                    this.ruleForm.dtls = data.dtls.map(item => {
                        //找出跟ys中的key相同的值,并返回一个数组
                        return {
                            norms: item.norms,//规格
                            bbType: item.bbType,//是否包边
                            isDK: item.isDK,//是否打扣
                            isNeedXP: item.isNeedXP,//是否需要吸盘
                            fullNormsName: item.fullNormsName,//规格全称
                            sheetSquareSaleAmount: item.sheetSquareSaleAmount,//1平方售价
                            sheetDKCount: item.sheetDKCount,//打扣数
                            sheetWidth: item.sheetWidth,//单张宽
                            sheetLength: item.sheetLength,//单张长
                            sheetCount: item.sheetCount,//张数
                            totalAmount: item.totalAmount,//总价
                            remark: item.remark,//备注
                            goodsCode: item.goodsCode,
                        }
                    })
                    this.ruleForm.actualTotalAmount = data.actualTotalAmount
                    setTimeout(() => {
                        if (data.isChangePrice == 1) {
                            alert('部分规格信息价格产生变动,请仔细核对')
                        }
                    }, 100);
                }
                this.buyersIdVisable = false
                this.drawer = true

            } else {
                this.$message.error('校验失败')
            }
        },
        async getCacheList() {
            const { data, success } = await getCostSetByYGGRMCache()
            if (success) {
                this.catchList = data
                //获取定制款的规格,并且去重
                this.dzList = data.filter(item => item.customType == 1)
                this.dzList = this.dzList.filter((value, index, self) => {
                    return self.findIndex((t) => {
                        return t.norms === value.norms
                    }) === index
                })
                //获取常规款的规格
                this.cgList = data.filter(item => item.customType == 2)
                this.cgList = this.cgList.filter((value, index, self) => {
                    return self.findIndex((t) => {
                        return t.norms === value.norms
                    }) === index
                })
            }
        },
        async changeTime(e, type) {
            if (type == 'create') {
                this.ListInfo.startTime = e ? e[0] : null
                this.ListInfo.endTime = e ? e[1] : null
            } else if (type == 'pay') {
                this.ListInfo.payStartTime = e ? e[0] : null
                this.ListInfo.payEndTime = e ? e[1] : null
            }

            await this.getList()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            const { data: { list, total }, success } = await getYGGRMCSRecordPageList(this.ListInfo)
            if (success) {
                this.tableData = list
                this.total = total
                this.loading = false
            } else {
                //获取列表失败
                this.loading = false
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        async yggrmDelete(id) {
            this.$confirm('删除后无法恢复，是否确认删除?', '删除订单信息', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await deleteYGGRMCSRecord({ Id: id })
                if (!res?.success) { return }
                this.$message({ type: 'success', message: '删除成功!' });
                this.getList()
            }).catch(() => {
                this.$message({ type: 'info', message: '已取消删除' });
            });
        },
        batchSet(row) {
            this.copyRow = row;
            this.copyVisable = true;
        },
        async doCopy() {
            for (let index = 0; index < this.copyRowCount; index++) {
                this.ruleForm.dtls.push(Object.assign({}, this.copyRow))
            }
            await this.changePrice()
            this.copyVisable = false;
        }
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 20px;

    el-button {
        margin-left: 10px;
    }
}

.iptCss {
    width: 80px;
}

::v-deep .el-table__body-wrapper {
    min-height: 300px !important;
    max-height: 300px !important;
}

::v-deep .cell {
    padding-left: 0;
}

.chatPicUrl {
    position: relative;

    .picTips {
        position: absolute;
        top: 0;
        left: 150px;
        color: #ff0000;
        font-size: 16px;
    }
}

.orderRemark {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
