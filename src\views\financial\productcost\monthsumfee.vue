<template>
  <container>
       <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='false'
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry'
              :loading="listLoading">
         <template slot='extentbtn'>
          <el-button-group>
            <el-button style="padding: 0;margin: 0;">
               <el-select filterable v-model="filter1.version" placeholder="类型" style="width: 110px">
                  <el-option label="工资月报" value="v1"></el-option>
                  <el-option label="参考月报" value="v2"></el-option>
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;"><el-input style="width: 100px" v-model="filter1.proCode" clearable placeholder="产品ID"/></el-button>
            <!-- <el-button style="padding: 0;margin: 0;"><el-input style="width: 100px" v-model="filter1.platform"  placeholder="平台"/></el-button>
            <el-button style="padding: 0;margin: 0;"><el-input style="width: 100px" v-model="filter1.platform"  placeholder="源Id"/></el-button> -->
            <!-- <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter1.groupId" filterable clearable placeholder="运营组" style="width: 110px">
                 <el-option label="全部" value/>
                 <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter1.shopId" filterable clearable placeholder="店铺" style="width: 110px">
                <el-option label="全部" value/>
                <el-option v-for="item in shoplist" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"/>
              </el-select>
            </el-button> -->
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-button-group>
        </template>
       </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
  </container>
</template>
<script>
import {pageProductMonthReport} from '@/api/financial/productcost'
import {getList as getshopList } from '@/api/operatemanage/base/shop'
import {getDirectorGroupList} from '@/api/operatemanage/base/shop'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import {formatWarehouse,formatTime,formatYesornoBool,formatLinkProCode} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'yearMonth',label:'年月', width:'70',sortable:'custom'},
      {istrue:true,prop:'proCode',label:'宝贝ID', width:'120',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
      {istrue:true,prop:'amontPick',label:'提货费', width:'80',sortable:'custom'},
      {istrue:true,prop:'amontPickAvg',label:'提货费分摊', width:'100',sortable:'custom'},
      {istrue:true,prop:'amontFreightfee',label:'采购运费', width:'80',sortable:'custom'},
      {istrue:true,prop:'amontFreightfeeAvg',label:'采购运费分摊', width:'110',sortable:'custom'},
      {istrue:true,prop:'amontShoot',label:'拍摄', width:'80',sortable:'custom'},
      {istrue:true,prop:'amontShootAvg',label:'拍摄分摊', width:'80',sortable:'custom'},
      {istrue:true,prop:'amontSampleBX',label:'样品报销', width:'80',sortable:'custom'},
      {istrue:true,prop:'amontSampleBXAvg',label:'样品报销分摊', width:'110',sortable:'custom'},
      {istrue:true,prop:'amontSampleGrop',label:'运营拿样费', width:'110',sortable:'custom'},
      {istrue:true,prop:'amontSampleMG',label:'美工拿样费', width:'110',sortable:'custom'},
      {istrue:true,prop:'amontSampleMGAvg',label:'美工拿样分摊', width:'110',sortable:'custom'},
      {istrue:true,prop:'amontOffLinefee',label:'运营下架', width:'80',sortable:'custom'},
      {istrue:true,prop:'amontOffLinefeeAvg',label:'运营下架分摊', width:'110',sortable:'custom'},
      {istrue:true,prop:'amontStoreLossfee',label:'仓储损耗', width:'80',sortable:'custom'},
      {istrue:true,prop:'amontStoreLossfeeAvg',label:'仓储损耗分摊', width:'110',sortable:'custom'},
     ];
const tableHandles=[
      ];
export default {
  name: 'Roles',
  components: {cesTable, container },
   props:{
       filter: { }
     },
  data() {
    return {
      filter1: {
        version:null,
        proCode:null,
        platform:null,
        fKId:null,
        groupId:null,
        shopId :null,
        type :null,
        shareOper :null
       },
      grouplist:[],
      shoplist:[],
      that:this,
      list: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"YearMonth",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [],
      listLoading: false,
      pageLoading: false,
      dialogExmainPurchaseFreightVisible:false
    }
  },
  async mounted() {
     await this.init()
     //this.onSearch()
  },
  beforeUpdate() { },
  methods: {
    async init(){
        this.shoplist =[]
        const res1 = await getshopList({isOpen:1,platform:1,CurrentPage:1,PageSize:100});
        res1?.data?.list.forEach(f=>{this.shoplist.push(f)})

        const res11 = await getshopList({isOpen:1,platform:2,CurrentPage:1,PageSize:100});
        res11?.data?.list.forEach(f=>{this.shoplist.push(f)})

        var res2= await getDirectorGroupList();
        this.grouplist = res2.data.map(item => {return { value: item.key, label: item.value };});
    },
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {...pager, ...this.pager,... this.filter, ... this.filter1}
      this.listLoading = true
      const res = await pageProductMonthReport(params)
      this.listLoading = false
      if (!res?.success) return
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   async onbatchDelete() {
       await this.$emit('ondeleteByBatch',this.shareFeeType);
    },
   async oncomput(){
      this.$emit('onstartcomput',this.shareFeeType);
   },
   async onimport(){
     await this.$emit('onstartImport',this.shareFeeType);
   },
    selsChange: function(sels) {
      this.sels = sels
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
  }
}
</script>
