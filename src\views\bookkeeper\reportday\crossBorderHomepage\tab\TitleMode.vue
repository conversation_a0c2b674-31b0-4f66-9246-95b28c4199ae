<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-radio-group v-model="ListInfo.type" style="margin-right:20px ;" @input="changeRadio">
                    <el-radio-button label="7天销量增长"></el-radio-button>
                    <el-radio-button label="15天销量增长"></el-radio-button>
                    <el-radio-button label="30天销量增长"></el-radio-button>
                    <el-radio-button  label="近15天无动销"></el-radio-button>
                    <el-radio-button label="近30天无动销"></el-radio-button>
                </el-radio-group>
                <el-input v-model.trim="ListInfo.styleCode" placeholder="系列编码" maxlength="9999" clearable class="publicCss" />
                <el-button style="padding: 0;border: none;float: left;">
                <inputYunhan :key="'1'" :keys="'one'" :width="'220px'" ref="" :inputt.sync="ListInfo.goodsCodes"
                    v-model.trim="ListInfo.goodsCodes" placeholder="商品编码/若输入多条请按回车" :clearable="true"
                    @callback="callbackGoodsCode" title="商品编码" @entersearch="entersearch" :maxRows="100">
                </inputYunhan>
                </el-button>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="onExport()" v-if="checkPermission(['ExportStylCodeHomePage_Kj'])">导出</el-button>

            </div>
        </template>
        <vxetablebase :id="'billingChargeTemu202408041401'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
            style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
        <!-- 表格行间操作栏echarts -->
        <el-dialog title="趋势图" :visible.sync="buscharDialog.visible" width="70%" v-dialogDrag append-to-body>
            <div>
                <buschar v-if="buscharDialog.visible" ref="refbuschar" :analysisData="buscharDialog.data"></buschar>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">取 消</el-button>
            </div>
        </el-dialog>

        <!-- 订单号弹框 -->
        <el-dialog :title="logVisibleTitle" center :visible.sync="logVisible" v-dialogDrag>
            <vxetablebase :id="'OrderCount20241212'" :tablekey="'OrderCount20241212'" :tableData='logData'
                :tableCols='logTableCols' :summaryarry='logSummaryarry' :showsummary='true' :loading='logLoading'
                :border='true' :that="that" height="440px" ref="logtable"
                @sortchange='logSortchange'>
            </vxetablebase>
        </el-dialog>

        
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
// import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import {  importBillingCharge_TemuAsync } from '@/api/bookkeeper/reportdayV2'
import buschar from '@/components/Bus/buschar'
import { getIllegalDeductionViolationTypeList, billingCharge_Temu_Export,GetStylCodeList,GetHomePageStylCode_KjAnalysis,exportStylCodeHomePage_Kj ,getPlatformOrderCount} from '@/api/bookkeeper/crossBorderV2'
import dayjs from 'dayjs'
import inputYunhan from "@/components/Comm/inputYunhan";
import {formatPlatformkj} from '@/utils/tools'

const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'styleCode', label: '系列编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    {
        sortable: 'custom', 
        width: 'auto', 
        align: 'center', 
        prop: 'orderCount', 
        label: '订单量', 
        type: 'colorClick',
        style: (that, row) => { return that.ListInfo.type !== '近15天无动销' && that.ListInfo.type !== '近30天无动销' ? "color: #409EFF;" : "color:gray;" },
        handle: (that, row) => {
            if (that.ListInfo.type !== '近15天无动销' && that.ListInfo.type !== '近30天无动销') {
                that.showOrderCount(row);
            }
        }
    },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'saleAmont', label: '订单销售额', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'ringRate', label: '环比增长率%', },
    {
        type: 'button',
        label: '操作',
        width: '150px',
        btnList: [
            {
                label: "趋势图",
                ishide: (that, row) => {
                    if (that.ListInfo.type === '近15天无动销' || that.ListInfo.type === '近30天无动销') return true;
                },
                handle: (that, row, field, val) => that.onTrendChart(row, field, val),
            },
        ],
    },
];
const logTableCols =[
  { istrue: true,  prop: 'platform', label: '平台', width: 'auto',formatter: (row) => {return formatPlatformkj(row.platform)}},
  { istrue: true,  prop: 'orderCount', label: '订单量', width: 'auto' },
  { istrue: true,  prop: 'dropshippingOrderCount', label: '代发数量', width: 'auto' },
  ]
export default {
    name: "refundAfterSale",
    components: {
        MyContainer, vxetablebase,buschar,inputYunhan
    },
    data() {
        return {
            yearMonthDay: null,//日期
            dialogVisible: false,//导入弹窗
            fileList: [],//上传文件列表
            uploadLoading: false,//上传按钮loading
            fileparm: {},//上传文件参数
            timeRanges: [],//时间范围
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: null,//开始时间
                endDate: null,//结束时间
                orderNo: null,//线上单号
                type:'7天销量增长',
                goodsCodes:null
            },
            tableCols,
            tableData: [],
            summaryarry: {},
            total: 0,
            loading: false,
            pickerOptions,
            billingExpenseType: [],

            //表格行间操作栏echarts
            buscharDialog: {
                visible: false,//趋势图弹窗
                data: {},//趋势图数据
            },

            //订单号弹框
            logVisible:false, //订单号弹框
            logData:[],
            logTableCols:logTableCols,
            logLoading:false,
            logVisibleTitle:null,
            logGoodsCodes:null, //弹出框商品编码
            logStyleCode:null, //弹出系列编码
            logSummaryarry:{},

        }
    },
    async mounted() {

        const billingItem = await getIllegalDeductionViolationTypeList();
        this.billingExpenseType = billingItem.data;//分组

        await this.getList()

    },
    methods: {
        async changeTime(e) {
            this.ListInfo.startDate = e ? e[0] : null
            this.ListInfo.endDate = e ? e[1] : null
        },

        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true            
            const { data, success } = await GetStylCodeList(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.summaryarry = data.summary
                this.loading = false
            } else {
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },

        //表格行间操作栏echarts
        async onTrendChart(row, field, val) {
           
            const params = {
                styleCode:row.styleCode,
                type:this.ListInfo.type,
                goodsCodes:row.goodsCode
            }
            this.loading = true
            const { data, success } = await GetHomePageStylCode_KjAnalysis(params)
            this.loading = false
            if (!success) return
            let that = this
            that.buscharDialog.visible = true;
            that.buscharDialog.data = data
            that.buscharDialog.title = data.legend[0]
            that.$nextTick(() => {
                that.$refs.refbuschar.initcharts();
            });
        },
        //切换 radio 直接查询
        changeRadio(){
            this.getList('search')
        },
        async onExport() {
            this.loading = true
            var res = await exportStylCodeHomePage_Kj(this.ListInfo);
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            aLink.setAttribute(
                "download",
                "系列编码盈亏_" + new Date().toLocaleString() + ".xlsx"
            );
            aLink.click();
            this.loading = false
            },
        async callbackGoodsCode(val) {
            this.ListInfo.goodsCodes = val;
            //this.onSearch();
            },
        async entersearch(val) {
            // this.filter.indexNo = val;
            this.getList();
            },
        async showOrderCount(row){

            if(this.ListInfo.type=='7天销量增长'){
                this.logVisibleTitle='7天销量增长'
            }
            if(this.ListInfo.type=='15天销量增长'){
                this.logVisibleTitle='15天销量增长'
            }
            if(this.ListInfo.type=='30天销量增长'){

                this.logVisibleTitle='30天销量增长'
            }
            this.logListGet()
            this.logStyleCode = row.styleCode
            this.logGoodsCodes = row.goodsCode

            // if(row.orderCount == 0 ){
            //     this.logVisible = false
            // }else{
                this.logVisible = true
            // }

            
        },
        logSortchange({ order, prop }) {
            if (prop) {
                this.logEnum.orderBy = prop
                this.logEnum.isAsc = order.indexOf("descending") == -1 ? true : false
                this.logListGet()
            }
        },
        async logListGet(){
                this.$nextTick(async()=>{
            let params = {
                styleCode :this.logStyleCode,
                goodsCodes :this.logGoodsCodes,
                type:this.ListInfo.type
            }
            this.logLoading = true
            const {data}= await getPlatformOrderCount(params) 
            this.logLoading = false
            this.logSummaryarry = data.summary
            this.logTotal  = data.total
            this.logData = data.list
            })
        },    
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 150px;
        margin-right: 5px;
    }
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
    max-width: 45px;
}
</style>