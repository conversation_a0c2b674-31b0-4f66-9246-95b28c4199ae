<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 95%;">
            <el-tab-pane label="汇总数据" name="first" :lazy="true" style="height: 100%;">
                <courierSumary />
            </el-tab-pane>
            <el-tab-pane label="明细" name="second" :lazy="true" style="height: 100%;">
                <courierDetails />
            </el-tab-pane>
            <el-tab-pane label="通知附件" name="third" :lazy="true" style="height: 100%;">
                <notificationAttachments />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import courierSumary from './courierSumary.vue'
import courierDetails from './courierDetails.vue'
import notificationAttachments from './notificationAttachments.vue'
export default {
    components: {
        MyContainer, courierSumary, courierDetails, notificationAttachments
    },
    data() {
        return {
            activeName: 'first'
        };
    },
    methods: {

    }
};
</script>

<style lang="scss" scoped></style>