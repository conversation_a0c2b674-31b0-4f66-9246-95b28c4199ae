<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" ref="form" label-width="120px" label-position="right">
                <el-row v-if="sellist && sellist.length > 0">
                    <el-col :span="24"  :style="'height:'+ tableHeight+'px;'">
                        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' :tableData='sellist'
                            :tableCols='tableCols' :isSelection="false" :isSelectColumn='false' >
                        </ces-table>
                    </el-col>
                </el-row>
                <el-row v-else>
                    <el-col :span="4">
                        <el-form-item label="平台：">
                            {{ form.deductPlatform == 1 ? '淘系' : form.deductPlatform == 6 ? '抖音' :  form.deductPlatform == 2 ? '拼多多' : form.deductPlatform == 14 ? '快手':'其他'  }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="7">
                        <el-form-item label="线上单号：">
                            <el-button type="text" @click="showLogDetail(form.orderNo)">{{ form.orderNo }}</el-button>
                            <span v-if="form.otherInfo && form.otherInfo.a" style="margin-left:5px;">扣款金额:{{
                                form.otherInfo.a.amountPaid }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="扣款时间：">
                            {{ form.deductOccurTime }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="7">
                        <el-form-item label="平台原因：">
                            {{ illegalTypeName }}
                        </el-form-item>
                    </el-col>
                </el-row> 
                <el-row>                                  
                    <el-col :span="4">
                        <el-form-item label="新责任类型：" prop="newZrType1" :rules="[
                                                        { required: true, message: '请选择新责任类型', trigger: ['blur', 'change'] }    
                                                        ]">
                                    <el-select v-if="formEditMode || (isAuditor && form.applyState==1)" v-model="form.newZrType1" @change="newZrType1Change">
                                        <el-option v-for="(v,key) in deductOrderZrType12Tree" :key="key" :label="key" :value="key"></el-option>
                                    </el-select>
                                    <span v-else>{{ form.newZrType1 }}</span>
                                </el-form-item>
                    </el-col>    
                    <el-col :span="7">
                        <el-form-item label="新责任原因：" prop="newZrType2" :rules="[
                                                        { required: true, message: '请选择新责任原因', trigger: ['blur', 'change'] }    
                                                        ]">
                                    <el-select  v-if="formEditMode || (isAuditor && form.applyState==1)"  v-model="form.newZrType2" >
                                        <el-option v-for="item in deductOrderZrType12Tree[form.newZrType1]" :key="item.zrType2" :label="item.zrType2"  :value="item.zrType2"></el-option>
                                    </el-select>
                                    <span v-else>{{ form.newZrType2 }}</span>
                                </el-form-item>
                    </el-col>                  
                </el-row>                
                <el-row>
                    <el-col :span="4">
                        <el-form-item label="新责任部门：" prop="newZrDeptAction" :rules="[
                            { required: true, message: '请选择新责任部门', trigger: ['blur', 'change'] }
                        ]">
                            <el-select v-if="formEditMode || (isAuditor && form.applyState == 1)"
                                v-model="form.newZrDeptAction" @change="newZrDeptActionChange">
                                <el-option v-for="item in zrDeptActions" :value="item.zrDeptAction"
                                    :label="item.zrDeptAction"></el-option>
                            </el-select>
                            <span v-else>{{ form.newZrDeptAction }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="7">
                        <el-form-item label="定则理由：" prop="newZrConditionFullName" :rules="[
                            { required: true, message: '请填写新责任部门对应理由', trigger: ['blur'] }
                        ]">
                            <el-input v-if="formEditMode" type="textarea" v-model="form.newZrConditionFullName" clearable
                                maxlength="100" show-word-limit />
                            <span v-else>{{ form.newZrConditionFullName }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="新责任原因：" prop="newZrReason" >                       
                            <span>{{ form.newZrType2 }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="7">
                        <el-form-item label="新责任人：" prop="newMemberName">
                            <template v-if="formEditMode || (isAuditor && form.applyState == 1)">
                                <el-select v-if="form.newZrDept == '采购'" v-model="form.newMemberId" filterable
                                    @change="newMemberIdChange">
                                    <el-option v-for="item in brandlist" :key="item.key" :label="item.value"
                                        :value="item.key" />
                                </el-select>
                                <el-select v-else-if="form.newZrDept == '运营'" v-model="form.newMemberId" filterable
                                    @change="newMemberIdChange">
                                    <el-option v-for="item in directorList" :key="item.key" :label="item.value"
                                        :value="item.key" />
                                </el-select>
                                <YhUserelector v-else-if="form.newZrDept!='机器人' && form.newZrDept!='外部'  && form.newZrDept!='快递'" 
                                            :value.sync="form.newMemberDDUserId" :text.sync="form.newMemberDDUserName"
                                        ></YhUserelector>
                                <el-input v-else v-model="form.newMemberName" clearable maxlength="10" />
                            </template>
                            <span v-else>{{ form.newMemberName }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24" style="max-height:400px;overflow:auto">
                        <el-form-item label="定责资料：" prop="applyContent" :rules="[
                            { required: true, message: '请填写新定责资料', trigger: ['blur'] }
                        ]">
                            <yh-quill-editor :value.sync="form.applyContent" v-if="formEditMode"></yh-quill-editor>
                            <div v-else v-html="form.applyContent" class="tempdiv"></div>
                        </el-form-item>
                    </el-col>
                </el-row>
                
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">
                    <el-button @click="onClose">关闭</el-button>
                    <el-button type="primary" @click="onSave(true)">确定</el-button>
                </el-col>
            </el-row>
        </template>

        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
            append-to-body>
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo" :isTx="isTx"
                style="z-index:10000;height:600px" />
        </el-dialog>

    </my-container>
</template>
<script>
 import cesTable from "@/components/Table/table.vue";
import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode, DeductOrderZrDeptActions, DeductOrderZrReasons
    ,DeductOrderZrType12,DeductOrderZrType12Tree  } from "@/utils/tools";
import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import { SetZrMemberCustomize } from "@/api/order/orderdeductmoney";
import { getAllWarehouse, getAllProBrand } from '@/api/inventory/warehouse'

import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'
import YhUserelector from '@/components/YhCom/yh-userselector.vue'

import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";

import {
    getDirectorList,
    getDirectorGroupList,
    getProductBrandPageList,
    getList as getshopList,
} from "@/api/operatemanage/base/shop";

const tableCols = [
    { istrue: true, prop: 'platform', label: '平台', width: '60', sortable: true, formatter: (row) => !row.platformName ? " " : row.platformName },
    { istrue: true, prop: 'orderNo', label: '线上订单号', width: '180', sortable: true },
    {
        istrue: true, prop: 'illegalType', label: '扣款原因', width: '180', sortable: true,
        formatter: (row) => !row.illegalTypeName ? " " : row.illegalTypeName
    },
    { istrue: true, prop: 'occurrenceTime', label: '扣款时间', sortable: true},
   
]


export default {
    name: "OrderDeductZrSetMemberForm",
    components: { MyContainer, MyConfirmButton, YhQuillEditor, OrderActionsByInnerNos,cesTable , YhUserelector},
    data() {
        return {
            that: this,
            mode: 3,
            tableCols: tableCols,
            sellist:[],
            illegalTypeList: [],
            zrDeptActions: DeductOrderZrDeptActions,
            zrReasons: DeductOrderZrReasons,
            
            deductOrderZrType12:DeductOrderZrType12,
            deductOrderZrType12Tree:DeductOrderZrType12Tree,
               

            sellistHeight:50,
            brandlist: [],
            directorList: [],

            form: {
                newMemberName: "",
                newMemberId: null,
                orderList: null,
                newMemberDDUserId:"",
                newMemberDDUserName:""
            },

            //summaryarry: {},
            pageLoading: false,
            curRow: null,
            formEditMode: true,//是否编辑模式              
            dialogHisVisible: false,
            isTx: false,
            tableHeightBase:41
        };
    },
    async mounted() {
        let illegalType = await ruleIllegalType();
        this.illegalTypeList = illegalType.options;
        await this.setBandSelect();
        await this.getDirectorlist();
    },
    computed: {      
        illegalTypeName() {
            let opt = this.illegalTypeList.find(x => x.value == this.form.illegalType);
            if (opt)
                return opt.label;
            else
                return '';
        },
        tableHeight: function () {
                let rowsCount = 1;
                if (this.sellist && this.sellist.length && this.sellist.length > 0) {
                    rowsCount = this.sellist.length;
                }
                let rowsHeight = (rowsCount + 1) * 40 + this.tableHeightBase;
                return rowsHeight > 200 ? 200 : rowsHeight;
            },
    },
    methods: {
        //设置新责任类型原因
        newZrType1Change(){             
            this.form.newZrType2="";              
        
        },
        async getDirectorlist() {
            const res1 = await getDirectorList({});
            const res2 = await getDirectorGroupList({});

            this.directorList = res1.data;
            this.directorGroupList = [{ key: "0", value: "未知" }].concat(
                res2.data || []
            );
        },
        async setBandSelect() {
            var res = await getAllProBrand();
            if (!res?.success) return;
            this.brandlist = res.data;
        },
        showLogDetail(orderNo) {
            this.isTx = this.form.deductPlatform == 1;
            this.dialogHisVisible = true;
            this.orderNo = orderNo;
        },
        newZrDeptActionChange() {
            if (this.form.newZrDeptAction) {
                let opt = this.zrDeptActions.find(x => x.zrDeptAction == this.form.newZrDeptAction);
                if (opt) {
                    if (this.form.newZrDept != opt.zrDept) {
                        this.form.newMemberName = "";
                        this.form.newMemberId = null;
                        this.form.newMemberDDUserId="";
                        this.form.newMemberDDUserName="";
                    }
                    this.form.newZrAction = opt.zrAction;
                    this.form.newZrDept = opt.zrDept;
                } else {
                    this.form.newMemberName = "";
                    this.form.newMemberId = null;
                    this.form.newMemberDDUserId="";
                    this.form.newMemberDDUserName="";
                }
            }
        },
        newMemberIdChange() {
            let arr = null;
            if (this.form.newZrDept == "采购") {
                arr = [...this.brandlist];
            }
            else if (this.form.newZrDept == "运营") {
                arr = [...this.directorList];
            }

            if (arr != null && arr && this.form.newMemberId) {
                let opt = arr.find(x => x.key == this.form.newMemberId);
                if (opt) {
                    this.form.newMemberName = opt.value;
                }
            }
        },
        onClose() {
            this.$emit('close');
        },
        async onSave(isClose) {
            if (await this.save()) {
                this.$emit('afterSave');
                if (isClose)
                    this.$emit('close');
            }
        },
        async loadData({ orderNo, occurrenceTime, illegalType, zrDept, zrAction, zrReason, zrDeptAction, zrConditionFullName,
            memberId, memberName, orderList,zrType1,zrType2,
            platform, mode }) {
            let self = this;
            self.pageLoading = true;
            self.formEditMode = mode != 3;
            self.mode = mode;

            let formDto = {
                deductPlatform: platform,
                newZrType1:zrType1,
                newZrType2:zrType2,
                newZrConditionFullName: zrConditionFullName,
                newZrDept: zrDept,
                newZrAction: zrAction,
                newZrReason: zrReason,
                newZrDeptAction: zrDeptAction,
                newMemberId: memberId,
                newMemberName: memberName,
                orderNo: orderNo,
                deductOccurTime: occurrenceTime,
                illegalType: illegalType
            };

            if (formDto.newMemberId){
                formDto.newMemberId = formDto.newMemberId.toString();
                
            }

            this.form = { ...formDto }
          

            if(orderList && orderList.length>0){
                this.sellist=orderList.map(x=>{
                    let y={...x};
                    y.deductOccurTime=x.occurrenceTime;
                    return y;
                });
             
            }
                

            self.pageLoading = false;
        },
        async save() {
            this.pageLoading = true;

            this.form.newZrReason=this.form.newZrType2;

            let saveData = { ...this.form };

            saveData.orderList = [];
            if(this.sellist && this.sellist.length>0)
                saveData.orderList =[...this.sellist];
            else
                saveData.orderList.push({
                    orderNo: saveData.orderNo,
                    deductOccurTime: saveData.deductOccurTime,
                    illegalType: saveData.illegalType
                });

            try {
                await this.$refs["form"].validate();
            } catch (error) {
                this.pageLoading = false;
                return false;
            }

            let rlt = await SetZrMemberCustomize(saveData);
            if (rlt && rlt.success) {
                this.$message.success('操作成功！');
            }

            this.pageLoading = false;

            return (rlt && rlt.success);
        }
    },
};
</script>
<style lang="scss" scoped>.tempdiv ::v-deep img {
    width: auto;
    max-width: 1000px;
}</style>