<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
               
        <el-form-item label="月份:" >
          <el-date-picker style="width: 120px" v-model="filter.yearMonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="月份"></el-date-picker>
        </el-form-item>
         <el-form-item label="运营组:" >
          <el-select  filterable v-model="filter.operating" placeholder="请选择运营组" class="el-select-content"  style="width:150px;" clearable>
            <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.value"/>
          </el-select>
        </el-form-item>
      
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
            :tableData='list'  :tableCols='tableCols' :isSelection="false" @select="selectchange"
            :tableHandles='tableHandles' @cellclick="cellclick"
            :loading="listLoading">
            </ces-table>
            <template #footer>
                <my-pagination ref="pager" :total="total" :checked-count="sels.length"  @get-page="getlist"/>
            </template>
       
        <el-drawer title="添加信息" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="addparmVisible1"
                direction="btt" size="'auto'" class="el-drawer__wrapper"  style="position:absolute;">
            <form-create :rule="autoform1.rule" v-model="autoform1.fApi" :option="autoform1.options"/>
            <div class="drawer-footer">
                <el-button @click.native="addparmVisible1 = false">取消</el-button>
                <my-confirm-button type="submit"  :loading="editparmLoading1" @click="onAddInfo" />
            </div>
        </el-drawer>
        <el-drawer title="编辑信息" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editparmVisible2"
                direction="btt" size="'auto'" class="el-drawer__wrapper"  style="position:absolute;">
            <form-create :rule="autoform2.rule" v-model="autoform2.fApi" :option="autoform2.options"/>
            <div class="drawer-footer">
                <el-button @click.native="editparmVisible2 = false">取消</el-button>
                <my-confirm-button type="submit"  :loading="editparmLoading2" @click="onSetEditParm" />
            </div>
        </el-drawer>
        
    </container>
</template>
<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import { getGroupKeyValue ,getOperatingGroupRatio,editOperatingGroupRatioAsync,getOperatingGroupPage,deleteOperatingGroupById} from '@/api/operatemanage/base/product';
import { format } from 'echarts';
const tableCols =[
        {istrue:true,prop:'yearMonth',label:'月份', tipmesg:'', width:'120',sortable:'custom',},
          {istrue:true,prop:'operating',label:'运营', tipmesg:'', width:'120',sortable:'custom',},
        {istrue:true,prop:'baseRatio',label:'基本工资比例', tipmesg:'',width:'150',sortable:'custom',formatter:(row)=> !row.baseRatio?"0%": (row.baseRatio).toFixed(2)+'%'}, 
        {istrue:true,prop:'commissionRatio',label:'提成工资比例', tipmesg:'',width:'150',sortable:'custom',formatter:(row)=> !row.commissionRatio?"0%": (row.commissionRatio).toFixed(2)+'%'}, 
        {istrue:true,prop:'sumRatio',label:'总工资比例', tipmesg:'',width:'200',sortable:'custom',formatter:(row)=> !row.sumRatio?"0%": (row.sumRatio).toFixed(2)+'%'}, 
        {istrue:true,prop:'createdUserName',label:'创建人', tipmesg:'', width:'300',sortable:'custom',},         
        {istrue:true,prop:'createdTime',label:'创建时间', tipmesg:'', width:'330',sortable:'custom'}, 
        {istrue:true,type:'button', width:'55',label:'操作', width:'200',btnList:[
          // {label:"编辑",handle:(that,row)=>that.onHand(row)},
          {label:"删除",handle:(that,row)=>that.onDelete(row)}]},
]
const tableHandles=[
        {label:"新增", handle:(that)=>that.addOperatingGroupRatio()},
      ];
const startTime = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");
export default {
    name: 'YunhanAdminReplacedayreport',
    components: {container, cesTable, MyConfirmButton},
    data() {
        return {
            deletefilter:{
              id:"",
              },
            groupList:[],
            that: this,
            filter:{           
                yearMonth:null,
                operating:null,
               
            },
            list: [],
            shopList: [],
            summaryarry:{},
            pager:{OrderBy:"",IsAsc:false},
            pickerOptions:{
                disabledDate(time){
                return time.getTime()>Date.now();
                }
            },        
            onHandNumber:null,   
            tableCols:tableCols,
            tableHandles:tableHandles,
            total: 0,
            sels: [], 
            editparmLoading: false,
            editparmLoading1: false,
            editparmLoading2: false,
            // editparmVisible: false,
             addparmVisible1: false,
            editparmVisible2: false,
           
            listLoading: false,
          
            uploadLoading: false,
          
       
          autoform1:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[ 
                
                {type:'DatePicker',field:'yearMonth',title:'月份',value:'',validate: [{type: 'string', required: true, message:'请选择月份'}],props: {type:'month',format:'yyyyMM',placeholder:'月份' ,valueFormat:'yyyyMM'},col:{span:6}},
                // {type:'input',field:'yearMonth',title:'月份',value: '',col:{span:6},validate:[{ required: true, message: '月份', trigger: 'blur' },]},
                //  {type:'select',field:'operating',title:'运营', validate: [{type: 'string', required: true, message:'请搜索运营'}],value: "",options: [],
                //                                 props:{filterable:true,
                //                                 allowCreate:false,clearable:true,
                //                                 remote:true,remoteMethod:(parm) => this.selectGroupSelect(parm)}}  
                    //  {type:'input',field:'operating',title:'运营',value: '',col:{span:6}}
                    , 
                    {type:'select',field:'operating',title:'运营', validate: [{type: 'string', required: true, message:'请搜索运营'}],value: "",options: [],
		                                                props:{filterable:true,
		                                                allowCreate:false,clearable:true,
		                                                remote:true,remoteMethod:(parm) => this.selectGroupSelect(parm)}}  ,
                     {type:'input',field:'baseRatio',title:'基本工资比例',col:{span:6}}, 
                     {type:'input',field:'commissionRatio',title:'提成工资比例',col:{span:6}},      
                                             
                    ]
          },
          autoform2:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[
                {type:'hidden',field:'id',title:'id',value: ''},
                {type:'DatePicker',field:'yearMonth',title:'月份',value:'',validate: [{type: 'string', required: true, message:'请选择月份'}],props: {type:'month',format:'yyyyMM',placeholder:'月份' ,valueFormat:'yyyyMM'},col:{span:6}},
                    //  {type:'input',field:'operating',title:'运营',value: '',col:{span:6}}, 
                      {type:'select',field:'operating',title:'运营', validate: [{type: 'string', required: true, message:'请搜索运营'}],value: "",options: [],
                                                props:{filterable:true,
                                                allowCreate:false,clearable:true,
                                                remote:true,remoteMethod:(parm) => this.selectGroupSelect(parm)}} ,
                     {type:'input',field:'ratio',title:'工资比例',col:{span:6}},         
                    ]
          },
        };
    },
    async mounted() {
        await this.setGroupSelect();
        await this.onSearch()
      
       
    },
    methods: {
      //单删
   async onDelete(row){
     this.deletefilter.id=row.id
     console.log("删除参数传递",this.deletefilter);
       this.$confirm('确认删除, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
        }).then(async () => {
            const res = await deleteOperatingGroupById(this.deletefilter)
            if (!res?.success) {return }
            this.$message({type: 'success',message: '删除成功!'});
            this.onSearch()
        }).catch(() => {
          this.$message({type: 'info',message: '已取消删除'});
        });
   },
     async selectGroupSelect(parm){
        if(!parm){
        //this.$message({ message: this.$t('api.sync'),type: 'warn'})
        return;
        }
        var options=[];
        const res = await getOperatingGroupPage({currentPage:1,pageSize:50, operating: parm})
        res?.data?.forEach(f=>{
        options.push({value:f.operating,label:f.operating})
        })
        this.autoform1.fApi.getRule('operating').options= options;
    },
   //运营组
    async setGroupSelect(){
      const res = await getGroupKeyValue();
      this.groupList=res.data;
    },
       async addOperatingGroupRatio(){
            
            this.addparmVisible1=true;
        },
     
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page  = this.pager;
            const params = { ...pager,...page,... this.filter}
            if(params===false){
                    return;
            }
            this.listLoading = true
            const res = await getOperatingGroupRatio(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
           
            this.list = data
       },
      
        renderAmont(row){
            if(row.orderCount > 1) return "color:blue;cursor:pointer;";
            else return "";
        },
       async onHand(row){   
      
            this.editparmVisible2 = true              
           
            var arr = Object.keys(this.autoform2.fApi)
            if (arr.length > 0)
                await this.autoform2.fApi.resetFields()
            this.$nextTick(async() =>{
                await this.autoform2.fApi.setValue(row)
            })  
         
           
       },
       async onSetEditParm(){
      
            this.editparmLoading2 = true
            this.autoform2.fApi.validate(async (valid) => {
                if(valid){
                    const formData = this.autoform2.fApi.formData();
                    // formData.isTrue = 3;
                    await editOperatingGroupRatioAsync(formData)
                    this.editparmLoading2 = false
                    this.editparmVisible2 = false                   
                    this.getlist()
                }
            })
        
            
       },
     async onAddInfo(){
        
           this.editparmLoading1 = true
            this.autoform1.fApi.validate(async (valid) => {
                if(valid){
                    const formData = this.autoform1.fApi.formData();
                    // formData.isTrue = 3;
                    console.log("添加数据运营",formData)
                    await editOperatingGroupRatioAsync(formData)
                   await this.autoform1.fApi.resetFields()
                    this.editparmLoading1 = false
                    this.addparmVisible1 = false                
                    this.getlist()
                }
            })
      },
       
       async nSearch(){
            await this.getlist()
       },
      
        
       
        async sortchange(column){
        if(!column.order)
            this.pager={};
        else{
            this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
        }
        await this.onSearch();
        },  
        selectchange:function(rows,row) {
            this.selids=[];console.log(rows)
            rows.forEach(f=>{
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event){
        
        },
       
    },
};
</script>
<style lang="scss" scoped>
</style>