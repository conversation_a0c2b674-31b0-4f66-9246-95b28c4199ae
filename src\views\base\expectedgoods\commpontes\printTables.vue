<template>
    <MyContainer v-loading="pageLoading">
        <div id="printTable">
            <div v-for="(model, i) in models" :ref="'PrintTable' + i" :key="model.id">
                <PrintTable style="margin-bottom: 20px;" class="tableItem" :model="model" :id="'allbonbody' + i" />
            </div>
        </div>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import PrintTable from './printTable.vue';
import { pageGetManifests } from '@/api/inventory/prepack'
import print from 'print-js'
import 'print-js/dist/print.css';

export default {
    name: 'printTable',
    components: { MyContainer, PrintTable },
    data() {
        return {
            models: [],
            allfile: [],
            pageLoading: false
        }
    },
    props: {
        batchNo: {
            type: Object,
            default: () => { }
        },
        tableData: {
            type: Array,
            default: () => []
        }
    },
    async mounted() {
        if (this.tableData && this.tableData.length > 0) {
            this.models = this.tableData
        } else {
            const { data, success } = await pageGetManifests({ batchNo: this.batchNo, pageSize: 50 })
            if (success) {
                this.models = data.list
            }
        }

    },
    methods: {
        doPrint() {
            print({
                printable: 'printTable',
                type: 'html',
                header: '加工清单',
                maxWidth: 1000,
                honorMarginPadding: false,
                repeatTableHeader: false,
                style: ".name1{overflow:hidden;} .name{font-size:12px;height:40px;max-height:40px;overflow:hidden;width:200px;padding-top:0px;line-height:20px;  } .ct{text-align:center} td{border: 1px solid #000;padding-left:5px;padding-right:5px;} th{border: 1px solid #000;} table{border-collapse: collapse; width: 100%;page-break-inside:avoid}thead{white-space: nowrap;}"
            })
        },

    }
}
</script>

<style scoped lang="scss"></style>