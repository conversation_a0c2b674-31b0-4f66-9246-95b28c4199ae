<template>
    <!-- <div> -->
        <div style="display: flex; flex-direction: column; height: 80vh; overflow-y: auto; overflow-x: hidden;">
            <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
                <el-select v-model="ListInfo.type" @change="changetype" placeholder="类型" class="publicCss" clearable>
                    <el-option v-for="item in typeList" :key="item" :label="item" :value="item" />
                </el-select>
                <el-select v-model="ListInfo.regionName" placeholder="片区" class="publicCss" clearable collapse-tags>
                    <el-option v-for="item in districtList" :key="item" :label="item" :value="item" />
                    <!-- <el-option key="南昌" label="南昌" value="南昌" />
                    <el-option key="义乌" label="义乌" value="义乌" />
                    <el-option key="深圳" label="深圳" value="深圳" />
                    <el-option key="西安" label="西安" value="西安" />
                    <el-option key="武汉" label="武汉" value="武汉" /> -->
                </el-select>
                <el-select :disabled="ListInfo.type == '仓储' || ListInfo.dataType == '面试人数'" v-model="ListInfo.deptType" @change="getdeptName" placeholder="部门类型" class="publicCss" clearable collapse-tags>
                <el-option v-for="item in sectionList" :key="item" :label="item" :value="item" />
                </el-select>
                <el-select :disabled="ListInfo.type == '仓储' || ListInfo.dataType == '面试人数'" v-model="ListInfo.deptName" placeholder="部门" class="publicCss" clearable collapse-tags>
                    <el-option v-for="item in sectionList2" :key="item" :label="item" :value="item" />
                </el-select>
                <el-select v-model="ListInfo.dataType" placeholder="数据类型" @change="selectDataType" class="publicCss" clearable collapse-tags>
                    <el-option v-for="item in sectionList3" :key="item" :label="item" :value="item" />
                </el-select>
                <el-date-picker v-model="ListInfo.calculateMonthArr" unlink-panels range-separator="至" start-placeholder="统计周期"
                end-placeholder="统计周期"   type="monthrange" style="width: 250px;margin-right: 5px;" :clearable="false"
                :value-format="'yyyy-MM'" >
                </el-date-picker>

                <!-- <el-select v-model="ListInfo.timeType" placeholder="统计周期" class="publicCss" clearable collapse-tags>
                    <el-option key="月" :label="'月'" :value="'月'" />
                    <el-option key="季度" :label="'季度'" :value="'季度'" />
                    <el-option key="年" :label="'年'" :value="'年'" />
                </el-select>
                <el-date-picker v-if="ListInfo.timeType == '月'" v-model="ListInfo.calculateMonthArr" unlink-panels range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期"   type="monthrange" style="width: 250px;margin-right: 5px;" :clearable="false"
                :value-format="'yyyy-MM'" >
                </el-date-picker>
                <el-date-picker
                v-if="ListInfo.timeType == '年'"
                v-model="ListInfo.yearvalue3"
                type="years"
                placeholder="选择年">
                </el-date-picker>
                <jiduTime v-if="ListInfo.timeType == '季度'" v-model="ListInfo.jiduvalue4"></jiduTime> -->

                <el-button type="primary" @click="getList('search')">查询</el-button>
            </div>

            <div style="min-height: 300px; width: 100%; border: 1px solid #ccc; margin-top: 10px;">
              <div class="chart-title">
                <h3>人事重点数据展示</h3>
              </div>
                <newcharbus v-if="oneCharts" :toolbox="{
                    show: true,
                    orient: 'vertical',
                    left: 'right',
                    top: 'center',
                    feature: {
                        magicType: { show: true, type: ['line', 'bar', 'stack'] },
                        saveAsImage: { show: true }
                        }
                    }"
                    ref="sptsxformchTyright"
                    :thisStyle="{
                    width: '100%', height: '275px', 'box-sizing': 'border-box', 'line-height': '300px'
                    }" :analysisData="oneCharts">
                </newcharbus>
            </div>

            <div>
                <charduibi trendChartType="one"></charduibi>
            </div>
            <div>
                <charduibitwo trendChartType="two"></charduibitwo>
            </div>
        </div>
    <!-- </div> -->
</template>

<script>
import newcharbus from "./newcharbus.vue";
import charduibi from "./charduibi.vue";
import charduibitwo from "./charduibitwo.vue";

import jiduTime from "./jiduTime.vue";
import dayjs from 'dayjs'
import {
    basisTrendChart, monthArchivesAnalysisListValue, getDeptList,
  } from '@/api/people/peoplessc.js';
    export default {
        components: {
            newcharbus, charduibi, jiduTime, charduibitwo
        },
        props: {
            editInfo: {
                type: Object,
                default: () => {
                    return {}
                }
            }
        },
        data() {
            return {
                warehouseName: '',
                ListInfo: {
                    calculateMonthArr: [dayjs().format('YYYY-MM'),dayjs().format('YYYY-MM')],
                    type: '',
                    twoDeptName: '',
                    deptName: '',
                    deptType: '',
                    regionName: ''
                },
                sectionList: [
                    "业务部门",
                    "职能部门",
                    "项目部",
                ],
                sectionList3: [
                    '招聘需求','面试人数','复试通过人数','离职人数','月度入职人数','异动人数','1年+离职人数','总人数'
                ],
                charbusdata: {},
                oneCharts: null
            }
        },
        async mounted() {
            console.log("查询")
            // this.ListInfo.deptType = this.sectionList[0];
            await this.getxiala('typeList', 'type');
            await this.getxiala('districtList', 'regionName');
            // await this.getxiala('sectionList', 'deptType');
            // await this.getxiala('sectionList2', 'deptName');
            // await this.getxiala('sectionList3', 'dataType');

            await setTimeout(async() => {
                await this.getList();
            }, 3000);




        },
        methods: {
            changetype(){
                if(this.ListInfo.type == '仓储'){
                    this.ListInfo.regionName = '';
                    this.districtList = [
                        '义乌仓',
                        '西安仓',
                        '南昌仓',
                    ]

                    this.sectionList = [];
                    this.sectionList2 = [];
                    this.ListInfo.deptName = "";
                    this.ListInfo.deptType = "";


                }else if (this.ListInfo.type == '办公室'){
                    this.sectionList = [
                        "业务部门",
                        "职能部门",
                        "项目部",
                    ];
                    this.districtList = [
                        '南昌',
                        '义乌',
                        '深圳',
                        '武汉',
                        '1688选品中心',
                    ]


                    // this.getdeptName(this.ListInfo.deptType)
                } else {
                    this.getxiala('districtList', 'regionName');
                }
            },
            fuzhi(){
                this.ListInfo.regionName = this.districtList[0];
                this.ListInfo.type = this.typeList[0];

                // this.ListInfo.deptName = this.sectionList2[0];
            },
            selectDataType(val) {
                if (val == '面试人数') {
                    this.ListInfo.deptType = null;
                    this.ListInfo.deptName = null;
                }
            },
            async getxiala(that,val){
                let res = await monthArchivesAnalysisListValue({
                    'fieldName': val
                })
                if(!res.success){
                    return
                }
                this[that] = res.data;
                if (that == 'typeList') {
                    this[that].push('全区域')
                    this.ListInfo[val] = '全区域'
                } else {
                    this.ListInfo[val] = res.data[0];
                }

                // setTimeout(async()=>{
                //     if(this.ListInfo.deptType){
                //         let res = await this.getdeptName(this.ListInfo.deptType)
                //         this.ListInfo.deptName = res[0];
                //     }
                // }, 1000)

            },
            async getdeptName(val){
                console.log(val)
                this.ListInfo.deptName = "";
                let res = await getDeptList({deptType: val});
                if(!res.success){
                    return;
                }
                this.sectionList2 = res.data;
                this.$forceUpdate();
                return  res.data;
            },
            async getList(){
                await this.getoneCharts();
            },
            async getoneCharts(){
                this.oneCharts = null;
                if (this.ListInfo.calculateMonthArr && this.ListInfo.calculateMonthArr.length > 0) {
                    this.ListInfo.startMonth = this.ListInfo.calculateMonthArr[0]
                    this.ListInfo.endMonth = this.ListInfo.calculateMonthArr[1]
                }else{
                    this.ListInfo.startMonth = null
                    this.ListInfo.endMonth = null
                }
                let params = {
                    ...this.ListInfo
                }
                let res = await basisTrendChart(params);
                if(!res.success){
                    return;
                }
                if(res.data?.series&&res.data.series.length>0){
                    res.data.series.map((item)=>{
                        item.label = {
                            show: true
                        }
                    })
                    res.data.xAxis = {
                        type: 'category',
                        data: res.data.xAxis
                    }
                    res.data.legend = {
                        data: res.data.legend
                    }
                    res.data.yAxis = [{
                        type: 'value'
                    }]
                }
                this.oneCharts = res.data;
                console.log(res);
            }
        }
    }
</script>

<style scoped lang="scss">
.chart-title {
  text-align: center;
  padding: 15px 0;
  background: linear-gradient(135deg, #00937e 0%, #00b894 100%);
  margin-bottom: 10px;
  border-radius: 8px 8px 0 0;
  position: relative;
  overflow: hidden;
}

.chart-title::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  animation: shimmer 2s infinite;
}

.chart-title h3 {
  margin: 0;
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  letter-spacing: 1px;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.publicCss {
  margin-right: 10px;
}
</style>
