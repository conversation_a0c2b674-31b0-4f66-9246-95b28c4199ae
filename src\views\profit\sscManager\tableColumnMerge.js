/**
 * 表格列合并功能的 mixin
 * 用于统一处理 vxe-table 的列合并逻辑
 * 基于 yh_vxetable.vue 中的 tableMergeColumn 功能封装
 */
export default {
  data() {
    return {
      // 默认的列合并配置，可以在组件中覆盖
      tableMergeColumn: {
        column: [], // 需要合并的列数组
        default: '', // 合并后展示的字段
        condition: null // 条件函数，返回 true 时才进行合并
      }
    }
  },
  
  methods: {
    /**
     * 通用的表格列合并函数（用于 span-method）
     * @param {Object} params - 参数对象
     * @param {Object} params.row - 行数据
     * @param {number} params._rowIndex - 行索引
     * @param {Object} params.column - 列配置
     * @param {Array} params.visibleData - 可见数据
     * @returns {Object} 合并配置 {rowspan, colspan}
     */
    tableColumnMergeMethod({ row, _rowIndex, column, visibleData }) {
      // 1. 首先处理列合并逻辑（tableMergeColumn）
      if (this.tableMergeColumn && this.tableMergeColumn.column && this.tableMergeColumn.column.length > 0) {
        const mergeColumns = this.tableMergeColumn.column;
        if (mergeColumns.includes(column.field || column.property)) {
          // 检查是否配置了条件函数
          if (this.tableMergeColumn.condition && typeof this.tableMergeColumn.condition === 'function') {
            // 如果配置了条件函数，需要满足条件才进行合并
            if (!this.tableMergeColumn.condition(row)) {
              // 条件不满足，不进行合并
              return { rowspan: 1, colspan: 1 };
            }
          }
          
          // 找到第一个需要合并的列
          const firstColumn = mergeColumns[0];
          const currentField = column.field || column.property;
          if (currentField === firstColumn) {
            // 这是第一个合并列，计算需要合并的列数
            return { rowspan: 1, colspan: mergeColumns.length };
          } else {
            // 这是后续的合并列，隐藏
            return { rowspan: 0, colspan: 0 };
          }
        }
      }

      // 2. 如果有原有的行合并逻辑（somerow），可以在这里处理
      if (this.somerow && this.somerow.trim() !== '') {
        const fields = this.somerow.split(',');
        const cellValue = row[column.field || column.property];
        if (cellValue !== null && fields.includes(column.field || column.property)) {
          const prevRow = visibleData[_rowIndex - 1];
          let nextRow = visibleData[_rowIndex + 1];
          if (prevRow && prevRow[column.field || column.property] === cellValue) {
            return { rowspan: 0, colspan: 0 };
          } else {
            let countRowspan = 1;
            while (nextRow && nextRow[column.field || column.property] === cellValue) {
              nextRow = visibleData[++countRowspan + _rowIndex];
            }
            if (countRowspan > 1) {
              return { rowspan: countRowspan, colspan: 1 };
            }
          }
        }
      }

      // 3. 默认返回不合并
      return { rowspan: 1, colspan: 1 };
    },

    /**
     * 获取表格列合并的显示值
     * @param {Object} row - 行数据
     * @returns {string} 显示值
     */
    getTableColumnMergeDisplayValue(row) {
      // 如果配置了默认显示字段且不为空，使用该字段的值
      if (this.tableMergeColumn?.default && this.tableMergeColumn.default.trim() !== '') {
        const value = row[this.tableMergeColumn.default];
        // 如果值为 null 或 undefined，返回空字符串
        return value != null ? value : '';
      }
      // 如果 default 为空字符串，返回空字符串
      if (this.tableMergeColumn?.hasOwnProperty('default') && this.tableMergeColumn.default === '') {
        return '';
      }
      // 否则使用第一个合并列的值
      const firstColumn = this.tableMergeColumn?.column?.[0];
      if (firstColumn) {
        const value = row[firstColumn];
        // 如果值为 null 或 undefined，返回空字符串
        return value != null ? value : '';
      }
      return '';
    },

    /**
     * 判断是否应该显示合并列（用于模板中的条件判断）
     * @param {Object} row - 行数据
     * @param {Object} col - 列配置
     * @returns {boolean} 是否显示合并列
     */
    shouldShowTableColumnMerged(row, col) {
      // 检查是否是合并列的第一列
      if (!this.tableMergeColumn?.column?.includes(col.field || col.prop) || 
          (col.field || col.prop) !== this.tableMergeColumn.column[0]) {
        return false;
      }
      
      // 如果配置了条件函数，需要满足条件才显示合并列
      if (this.tableMergeColumn.condition && typeof this.tableMergeColumn.condition === 'function') {
        return this.tableMergeColumn.condition(row);
      }
      
      // 没有条件函数，默认显示合并列
      return true;
    },

    /**
     * 创建列合并配置的工厂方法
     * @param {Array} columns - 需要合并的列数组
     * @param {string} defaultField - 合并后展示的字段
     * @param {Function} condition - 条件函数（可选）
     * @returns {Object} 列合并配置对象
     */
    createTableColumnMergeConfig(columns, defaultField = '', condition = null) {
      return {
        column: columns,
        default: defaultField,
        condition: condition
      };
    }
  }
};
