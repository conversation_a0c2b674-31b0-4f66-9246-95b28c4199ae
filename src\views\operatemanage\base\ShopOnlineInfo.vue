<template>
    <my-container v-loading="pageLoading">
      
      <el-row>
        <el-col :span="24" style="border-bottom: solid 1px silver;">
            <el-select v-model="filter.platform" clearable placeholder="请选择平台" style="width:120px;">
                <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
            <el-select v-model="filter.isOnline" clearable placeholder="请选择是否在线" style="width:120px;">
                <el-option label="全部" :value="null"></el-option>
                <el-option label="在线" :value="true"></el-option>
                <el-option label="不在线" :value="false"></el-option>
            </el-select>
            <el-input v-model="filter.keywords" clearable placeholder="请输入店铺名称" style="width:120px;"> </el-input>          

            <el-button type="primary" @click="getlist">刷新</el-button>
            
            查看明细： <el-switch
            v-model="showContent"
            active-color="#13ce66"
            inactive-color="#ff4949">
            </el-switch>

            <span v-if="showContent">
                
                查看任务： <el-switch 
                v-model="showTask"
                active-color="#13ce66"
                inactive-color="#ff4949">
                </el-switch>

            </span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" >          
            <div class="shop-info" v-for="shopInfo in shopList">             
                <div class="shop-info-item" >
                    <div class="shop-info-item-title">
                        <div :class="shopInfo.isOnline?'shop-online-color':'shop-unline-color'">
                        </div>
                        <div class="shop-info-item-title-text">
                            {{ shopInfo.shopName }}
                            <!-- {{shopInfo.shopName.indexOf('-')>0? shopInfo.shopName.split('-')[1]: shopInfo.shopName }} -->
                        </div>
                    </div>
                    <div v-if="showContent" class="shop-info-item-content">
                        <p>平台-内部ID：{{ shopInfo.shopPlatformId }}-{{ shopInfo.shopCode }}</p>                       
                        <p>最后在线时间：{{ shopInfo.lastOnlineTime }}</p>
                        <p  v-if="showTask"><b>=======任务执行清单======</b></p>
                        <div v-if="showTask" class="shop-info-item-content-task" >                            
                            <p v-for="task in shopInfo.shopTaskInfos">   
                                <b> [{{ task.taskExeTime }}][{{task.taskName}}]：</b>{{task.taskExeMsg}}
                            </p>
                        </div>                          
                    </div>                
                </div>
            </div>
        </el-col>
      </el-row>

    </my-container>
</template>

<script>
import { GetOnlineShopInfoList } from '@/api/operatemanage/base/ErpToolApi'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import middlevue from "@/store/middle.js"

import { formatPlatform,  platformlist, companylist } from "@/utils/tools";

export default {
    name: 'ShopOnlineInfo',
    components: {  MyContainer, MyConfirmButton },
    data() {
        return {
            that: this,
            filter: {   
                platform:null,
                isOnline:true,
                shopPlatformId:'',
                keywords:''
            },           
            
            platformlist: platformlist,         
            pageLoading: false,         
            shopList: [],
            showContent:true,
            showTask:true
        }
    },
    async beforeCreate() {
    },
    async mounted() {      
        await this.getlist();

        middlevue.$on('ErpTools_ShopChangeEvent', async (data) => {
            await this.getlist();
        })
    },
    beforeUpdate() {
    },
    async beforeDestroy() {
        middlevue.$off('ErpTools_ShopChangeEvent');
    },
    methods: {    
        //获取数据列表
        async getlist() {            
            const res = await GetOnlineShopInfoList(this.filter);
        
            if (!res?.success) return;
          
            this.shopList = res.data          
        }         
    }
}
</script>
<style scoped lang="scss">
.shop-info-item-title-text{
    //显示成一行，超出显示为...
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 240px;
}
.shop-info-item{
    border: solid 1px silver;
    margin-top:10px;
    margin-left:10px;
    padding-bottom: 2px;
}
.shop-online-color{
    margin-top: 2px;
    margin-left: 2px;
    width: 20px;
    height: 20px;
    background-color: #67C23A;
    float: left;
}
.shop-unline-color{
    margin-top: 2px;
    margin-left: 2px;
    width: 20px;
    height: 20px;
    background-color: #909399;
    float: left;
}

.shop-info-item-content{
    padding-left: 10px;
  
}

.shop-info-item-content p{
    padding-left: 10px;
    margin-top: 5px;
    margin-bottom: 2px;
    font-size: 12px;
}

.shop-info-item-content-task{
    height: 200px;
    overflow:auto;
}

    .shop-info{
        width:280px;
        float: left;
    }

    
</style>