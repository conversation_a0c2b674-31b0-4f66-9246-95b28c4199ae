<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-date-picker style="width: 220px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        :clearable="false" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-tooltip class="item" effect="dark" content="默认为精确匹配在开始或者结尾输入*进行模糊匹配" placement="top">
                        <inputYunhan :key="'3'" :keys="'three'" :width="'180px'" ref="childGoodsCode"
                            v-model="filter.goodsCode" :inputt.sync="filter.goodsCode" placeholder="成品编码" :clearable="true" @callback="callbackGoodsCode"
                            title="成品编码"></inputYunhan>
                    </el-tooltip>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-input v-model.trim="filter.goodsName" type="text" maxlength="100" clearable placeholder="请输入成品名称..."
                        style="width:180px;">
                    </el-input>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-input v-model.trim="filter.createdUserName" type="text" maxlength="100" clearable
                        placeholder="请输入提交人..." style="width:150px;">
                    </el-input>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select v-model="filter.taskStatus" clearable placeholder="任务状态" style="width: 150px">
                        <el-option label="已完成" value="1" />
                        <el-option label="进行中" value="9" />
                        <el-option label="终止" value="99" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select v-model="filter.brandCode" placeholder="品牌" :collapse-tags="true" style="width:150px;"
                        clearable>
                        <el-option v-for="item in brandList" :key="item.setId" :label="item.sceneCode"
                            :value="item.setId" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select v-model="filter.job_Position" placeholder="岗位" :collapse-tags="true" style="width:150px;"
                        clearable>
                        <el-option v-for="item in jobPositionList" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select v-model="filter.companyType" clearable placeholder="分公司" style="width: 150px">
                        <el-option label="义乌" value="0" />
                        <el-option label="南昌" value="1" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button>
                <p></p>
                <el-button style="padding: 10;margin-left: 0;">
                    <el-checkbox-group v-model="filter.checkList" @change="changeCheck" style="float:left;margin-top:1px;">
                        <el-checkbox :label="1">按编码</el-checkbox>
                        <el-checkbox :label="2">按人</el-checkbox>
                        <el-checkbox :label="3">按时间</el-checkbox>
                    </el-checkbox-group>
                </el-button>

            </el-button-group>
        </template>

        <ces-table :tablekey="'StoreTocktaking202305091124'" ref="tableCols" :hasexpandRight='true' :showsummary='true'
            :summaryarry='summaryarry' :that='that' :isIndex='true' :tablefixed='true' :hasexpand='false'
            @cellclick='cellclick' :isSelectColumn="false" :tableData='list' :tableCols='tableCols'
            :tableHandles='tableHandles' @sortchange="sortchange" :loading="listLoading"   :border="true">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="详情" :visible.sync="dialogShowInfoVisible" v-dialogDrag width='50%' :close-on-click-modal="false"
            height='800px'>
            <finishedpartdetailVue ref="finishedpartdetailVue" style="height: 500px;"></finishedpartdetailVue>
        </el-dialog>

    </container>
</template>

<script>
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import inputYunhan from "@/components/Comm/inputYunhan";
import { formatTime, formatNoLink } from "@/utils/tools";
import { getGoodsFinishedPartAutoLogAsync, getJob_PositionListAsync, getGoodsFinishedPartAutoLogGroupAsync } from "@/api/inventory/machine"
import { getShootingSetData } from '@/api/media/shootingset'
import finishedpartdetailVue from './finishedpartdetail.vue';

const tableCols = [
    { istrue: true, align: 'center', prop: 'taskStatus', label: '任务状态', width: 'auto', sortable: 'custom', formatter: (row) => row.taskStatusName },
    { istrue: true, align: 'center', prop: 'finishedProductCode', label: '成品编码', width: 'auto', sortable: 'custom', type: 'html', formatter: (row) => formatNoLink(row.finishedProductCode) },
    { istrue: true, align: 'center', prop: 'finishedProductName', label: '成品名称', width: 'auto', sortable: 'custom', },
    { istrue: true, align: 'center', prop: 'finishedProductQuantity', label: '需加工数量', width: '120', sortable: 'custom', },
    { istrue: true, align: 'center', prop: 'totalDispatchQuantity', label: '实际加工数量', width: '120', sortable: 'custom', },
    { istrue: true, align: 'center', prop: 'brandCode', label: '品牌', width: '100', sortable: 'custom', formatter: (row) => row.brandName },
    { istrue: true, align: 'center', prop: 'createdTime', label: '提交时间', width: '150', sortable: 'custom', },
    { istrue: true, align: 'center', prop: 'createdUserName', label: '提交人', width: '100', sortable: 'custom' },
    { istrue: true, align: 'center', prop: 'job_Position', label: '岗位', width: '100', sortable: 'custom' },
    { istrue: true, align: 'center', prop: 'companyName', label: '分公司', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, align: 'center', prop: 'pfDate', label: '计划完成时间', width: '110', sortable: 'custom', },
    { istrue: true, align: 'center', prop: 'completeDate', label: '完成时间', width: 'auto', sortable: 'custom', },
    { istrue: true, align: 'center', prop: 'durationTime', label: '实际用时', width: '120', sortable: 'custom' },
];
const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
];

const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");


export default {
    name: 'YunHanAdminfinishedpart',
    components: { container, cesTable, inputYunhan, finishedpartdetailVue },

    data() {
        return {
            that: this,
            filter: {
                beginDate: null,
                endDate: null,
                goodsCode: null,
                goodsName: null,
                keywords: null,
                createdUserName: null,
                brandCode: null,
                job_Position: null,
                companyType: null,
                taskStatus: null,
                checkList: [],
                timerange: [startTime, endTime],
            },
            list: [],
            summaryarry: {},
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "FinishedProductQuantity", IsAsc: false },
            total: 0,
            sels: [],
            brandList: [],
            jobPositionList: [],
            chooseTags: [],
            selids: [],
            pickerOptions: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近半个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            activeName: 'first',
            listLoading: false,
            pageLoading: false,
            dialogShowInfoVisible: false,
        };
    },

    async mounted() {
        await this.init();
        await this.onSearch();
    },

    methods: {
        async changeCheck() {
            if (this.filter.checkList.length > 0) {
                let tableCols1 = [
                    { istrue: true, align: 'center', prop: 'finishedProductQuantity', label: '需加工数量', width: '120', sortable: 'custom', },
                    { istrue: true, align: 'center', prop: 'totalDispatchQuantity', label: '实际加工数量', width: '120', sortable: 'custom', },
                    { istrue: true, align: 'center', prop: 'num', label: '次数', width: '120', sortable: 'custom', },
                ];
                if (this.filter.checkList.includes(1)) {
                    tableCols1.unshift({ istrue: true, align: 'center', prop: 'brandCode', label: '品牌', width: '120', formatter: (row) => row.brandName },)
                    tableCols1.unshift({ istrue: true, align: 'center', prop: 'finishedProductName', label: '成品名称', width: '260', sortable: 'custom', },)
                    tableCols1.unshift({ istrue: true, align: 'center', prop: 'finishedProductCode', label: '成品编码', width: '120', sortable: 'custom', },)
                }
                if (this.filter.checkList.includes(2)) {
                    tableCols1.unshift({ istrue: true, align: 'center', prop: 'createdUserName', label: '提交人', width: '120', sortable: 'custom' });
                }
                if (this.filter.checkList.includes(3)) {
                    tableCols1.unshift({ istrue: true, align: 'center', prop: 'createdTime', label: '提交时间', width: '120', sortable: 'custom', formatter: (row) => formatTime(row.createdTime, 'YYYY-MM-DD') });
                    tableCols1.push({ istrue: true, align: 'center', prop: 'totalDispatchQuantity', label: '完成数', width: '120', sortable: 'custom' });
                }
                this.tableCols = tableCols1;
            } else {
                this.tableCols = tableCols;
            }
            this.pager = {
                OrderBy: null,
                IsAsc: false,
            };
            this.$refs.tableCols.clearSort();
            this.$refs.pager.setPage(1);
            this.getlist();
        },
        async init() {
            const res = await getShootingSetData({ setType: 15 });
            if (!res?.success) {
                return
            }
            this.brandList = res?.data?.data;

            const res1 = await getJob_PositionListAsync();
            if (!res1?.success) {
                return
            }
            this.jobPositionList = res1?.data;
        },
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.beginDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange) {
                this.filter.beginDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            if (this.filter.checkList.length == 0) {
                var res = await getGoodsFinishedPartAutoLogAsync(params);
                this.listLoading = false
                if (!res?.success) {
                    return
                }

                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data?.summary;
                this.list = data
            } else {
                var res = await getGoodsFinishedPartAutoLogGroupAsync(params);
                this.listLoading = false
                if (!res?.success) {
                    return
                }

                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data?.summary;
                this.list = data
            }

        },
        async callbackGoodsCode(val) {
            this.filter.goodsCode = val;
            //this.onSearch();
        },
        selectchange: function (rows, row) {
            //先把当前也的数据全部移除
            this.list.forEach(f => {
                let index = this.chooseTags.findIndex((v) => (v === f.goodsCode));
                if (index !== -1) {
                    this.chooseTags.splice(index, 1);
                    this.selrows.splice(index, 1);
                }
            });
            //把选中的添加
            rows.forEach(f => {
                let index = this.chooseTags.findIndex((v) => (v === f.goodsCode));
                if (index === -1) {
                    this.chooseTags.push(f.goodsCode);
                    this.selrows.push(f);
                    console.log("选中数据", this.selrows);
                }
            });

            ///
            let _this = this;
            if (rows.length > 0) {
                var a = [];
                rows.forEach(element => {
                    let b = _this.list.indexOf(element);
                    a.push(b + 1);
                });

                let d = _this.list.indexOf(row);

                var b = Math.min(...a)
                var c = Math.max(...a)

                a.push(d);
                if (d < b) {
                    var b = _this.list.indexOf(row);
                    var c = Math.max(...a)
                } else if (d > c) {
                    var b = Math.min(...a) - 1
                    var c = Math.max(...a)
                } else {
                    var b = Math.min(...a) - 1
                    var c = _this.list.indexOf(row) + 1;
                }

                let neww = [b, c];
                _this.selids = neww;
            }
            console.log('选择的数据', this.selids)
        },
        callback(val) {
            this.selids = [...val];

            this.tablelist = [];
            this.tablelist = val;
            var goodsCode = val.map((item) => {
                return item.goodsCode;
            })
            this.chooseTags = goodsCode;
            console.log("goods返回值", this.chooseTags)
        },
        async cellclick(row, column, cell, event) {
            if (column.property == 'finishedProductCode') {
                if (this.filter.checkList.length <= 0) {
                    let selData = { packagesProcessingId: row.packagesProcessingId };
                    this.dialogShowInfoVisible = true;
                    this.$nextTick(async () => {
                        this.$refs.finishedpartdetailVue.loadData({ selRows: selData, });
                    });
                    // this.$showDialogform({
                    //     path: `@/views/inventory/machine/finishedpartdetail.vue`,
                    //     title: '详情信息',
                    //     autoTitle: false,
                    //     args: { selRows: selData, },
                    //     height: '600px',
                    //     width: '50%',
                    // })
                }

            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>


</style>
