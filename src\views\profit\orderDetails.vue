<template>
 <container v-loading="pageLoading">
     <template #header>
         <el-button-group>
             <el-button style="padding:0;margin:0;">
                    <!-- <el-date-picker v-model="filter.orderMenuDateRange" type="date" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" placeholder="选择日期"
                        style="width:150px;" :clearable="false">
                    </el-date-picker> -->

                    <el-date-picker style="width: 260px" v-model="filter.orderMenuDateRange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                     range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false"></el-date-picker>
              </el-button>
             <el-button style="padding:0;margin:0;">
                 <el-input v-model.trim="filter.userName" placeholder="名字搜索" style="width: 140px" :maxlength="50" clearable>
                 </el-input>
             </el-button>
             <el-button style="padding:0;margin:0;">
                <el-select v-model="filter.zoneName" placeholder="地区搜索" filterable style="width: 160px">
                    <el-option :label="item" :value="item" v-for="(item,index) in quyuList" :key="index" />
                </el-select>
             </el-button>
             <el-button style="padding:0;margin:0;">
                <el-input v-model.trim="filter.areaName" placeholder="区域" style="width: 140px" :maxlength="50" clearable></el-input>
             </el-button>
             <el-button style="padding:0;margin:0;">
                <el-input v-model.trim="filter.floorName" placeholder="楼层" style="width: 140px" :maxlength="50" clearable></el-input>
             </el-button>
             <el-button style="padding:0;margin:0;">
                <el-input v-model.trim="filter.deptName" placeholder="部门" style="width: 140px" :maxlength="50" clearable></el-input>
             </el-button>
             <el-button style="padding:0;margin:0;">
                <el-input v-model.trim="filter.groupName" placeholder="组" style="width: 140px" :maxlength="50" clearable></el-input>
             </el-button>
             <el-button style="padding:0;margin:0;">
                <el-input v-model.trim="filter.gysName" placeholder="供应商" style="width: 140px" :maxlength="50" clearable></el-input>
             </el-button>
             <el-button style="padding:0;margin:0;">
                 <el-select v-model="filter.menuLabel" placeholder="餐别" clearable  filterable style="width: 150px">
                     <el-option label="中餐" value="中餐" />
                     <el-option label="晚餐" value="晚餐" />
                 </el-select>
             </el-button>
             <el-button style="padding:0;margin:0;">
                 <el-select v-model="filter.menuType" placeholder="餐类" clearable  filterable style="width: 150px">
                    <el-option label="正餐" value="正餐" />
                     <el-option label="加餐" value="加餐" />
                 </el-select>
             </el-button>
             <el-button type="primary" @click="onSearch">查询</el-button>
             <el-button @click="exportEvent">导出</el-button>
         </el-button-group>
     </template>
     <!-- <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
         :isSelectColumn="false" :showsummary='true' :summaryarry='summaryarry' :tablefixed='false' :tableData='tableData' :tableCols='tableCols'
         :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:95.5%;margin: 0">
     </ces-table> -->
    <div id="orderDetails20250412" style="width: 100%; height: 95.5%" >
        <vxetablebase :id="'pageOperationLogl202408041350'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
        :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
        style="width: 100%; height:100%;margin: 0" :showsummary='true' :summaryarry='summaryarry' :loading="loading" :height="'100%'">
        </vxetablebase>
    </div>
     <!-- <template #footer>
         <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
     </template> -->
     <my-pagination ref="pager" :sizes="[50, 100,200,300,1000,2000,5000]" :total="total" :checked-count="sels.length" @get-page="getList" />

    
 </container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import container from '@/components/my-container';
import vxetablebase from '@/components/VxeTable/yh_vxetable.vue';
import YhImgUpload1 from "@/components/upload/yh-img-upload1.vue";
import { formatLinkProCode, platformlist } from '@/utils/tools'
import {
 importBaseSupermarketGoodAsync,
 getPageBaseSupermaketGoodAsync,
 updateBaseSupermaketGoodAsync,
 delBaseSupermaketGoodAsync,
 getGoodTypeAsync,
 saveGoodTypeAsync,
 updateBaseSupermaketGoodStockAsync
} from '@/api/profit/orderfood';
import { getOrderFoodMenuProvier, getAreaSetList, getUserOrderDetailList, quyuList } from '@/api/profit/orderfood';
import exportExecl from "@/utils/exportExecl.js"
const tableCols = [
 // { istrue: true, prop: 'gysName', label: '供应商名称', sortable: 'custom', width: '160' },
 { istrue: true, prop: 'orderMenuDate', label: '菜单时间', sortable: 'custom', width: '160', formatter:(row)=> row.orderMenuDateStr,},
 { istrue: true, prop: 'zoneName', label: '地区名称', sortable: 'custom', width: '160' },
 { istrue: true, prop: 'areaName', label: '区域名称', sortable: 'custom', width: '160' },
 { istrue: true, prop: 'floorName', label: '楼层名称', width: '120', sortable: 'custom', },
 { istrue: true, prop: 'deptName', label: '部门名称', width: '120', sortable: 'custom' },
 { istrue: true, prop: 'groupName', label: '组名称', width: '120', sortable: 'custom' },
 // { istrue: true, prop: 'stock', label: '库存', width: '100', sortable: 'custom' },
 { istrue: true, prop: 'userName', label: '点餐人', width: '120', sortable: 'custom' },
 { istrue: true, prop: 'gysName', label: '供应商名称', width: '120', sortable: 'custom' },
 { istrue: true, prop: 'menuLabel', label: '餐别', width: '120', sortable: 'custom' },
 { istrue: true, prop: 'menuType', label: '菜单类型', width: '120', sortable: 'custom' },
 { istrue: true, prop: 'menuName', label: '菜单名', width: 'auto', sortable: 'custom' },

 { istrue: true, prop: 'fenShu', label: '份数', width: '120', },
 { istrue: true, prop: 'price', label: '金额', width: '100', sortable: 'custom' },
];

const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endDate = startDate;

export default ({
 name: "Users",
 components: { container, vxetablebase, YhImgUpload1 },
 data () {
     return {
         dialogVisibleStock: false,
         fileList: [],
         gysList: [],
         dialogEdit: false,
         stockForm: {
             stock: 0,
             id: 0
         },
         editForm: {
         },
         form: {
             goodTypeList: [],
         },
         quyuList: [],
         dialogMenuType: false,
         uploadLoading: false,
         dialogVisibleData: false,
         that: this,
         filter: {
            //  timerange: [startDate, endDate],
             orderMenuDateRange: [
                //  formatTime(dayjs().subtract(30, "day"), "YYYY-MM-DD"),
                formatTime(dayjs().add(1, 'day'), "YYYY-MM-DD"),
                formatTime(dayjs().add(1, 'day'), "YYYY-MM-DD"),
             ],
             zoneName: '',
             orderMenuStartDate: startDate,
             orderMenuEndDate: endDate,
         },
         platformlist: platformlist,
         tableCols: tableCols,
         tableHandles: null,
         tableData: [],
         total: 0,
         pager: { OrderBy: "orderMenuDate", IsAsc: false },
         listLoading: false,
         pageLoading: false,
         summaryarry: {},
         sels: [],
     };
 },
 async mounted () {
    //  await this.getgyss();
     await this.getGoodType();
     await this.onSearch();
 },
    methods: {
        async exportEvent() {
            let that = this;
            that.loading = true
            that.pageLoading = true
            if (that.filter.orderMenuDateRange[0] == that.filter.orderMenuDateRange[1]) {
                await exportExecl("orderDetails20250412", that.filter.zoneName + '-'+ '点餐详情——' + that.filter.orderMenuDateRange[0] + '.xlsx')
            } else {
                await exportExecl("orderDetails20250412", that.filter.zoneName + '-'+ '点餐详情——' + that.filter.orderMenuDateRange[0]+'-'+that.filter.orderMenuDateRange[1] + '.xlsx')
            }
            await setTimeout(() => {
                that.loading = false
                that.pageLoading = false
                that.$message({ message: '导出成功', type: "success" });
            }, 100)


            
    },
    async getquyu(){
        const res = await getAreaSetList({getLevel: 1});
        if(!res.success){
            return
        }
        this.quyuList = res.data;
        if(!this.filter.zoneName){
            this.filter.zoneName = this.quyuList[0];
        }
    },
     async getsize(){
      let params = {

      }
      const res = await getOrderFoodMenuProvier();
         if(!res.success){
             return
         }
         this.gysList = res.data;
     },
     async getgyss(){
         const res = await getOrderFoodMenuProvier();
         if(!res.success){
             return
         }
         this.gysList = res.data;
         this.filter.gysName = this.gysList[0];
     },
     async getGoodType () {
         const res = await getGoodTypeAsync();
         this.form.goodTypeList = res?.data;
     },
     async onSearch () {
         this.$refs.pager.setPage(1);
         this.$refs.table.currentLvl = 9;
         await this.getquyu();
        
         await this.getList();
         
     },
     async sortchange (column) {
         if (!column.order)
             this.pager = {};
         else
             this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
         this.$refs.pager.setPage(1);
         await this.onSearch();
     },
     async getList () {
         if (this.filter.orderMenuDateRange && this.filter.orderMenuDateRange.length > 0) {
             this.filter.orderMenuStartDate = this.filter.orderMenuDateRange[0];
             this.filter.orderMenuEndDate = this.filter.orderMenuDateRange[1];
         }
         var that = this;
         this.listLoading = true;
         that.loading = true
         that.pageLoading = true
         var pager = this.$refs.pager.getPager();
         const params = { ...pager, ...this.pager, ...this.filter, ...this.myfilter };
         const res = await getUserOrderDetailList(params).then(res => {
             that.total = res.data?.total;
             that.tableData = res.data?.list;
             res.data.summary.fenShu_sum = res.data?.summary.quantity_sum;
             that.summaryarry = res.data?.summary;
         });
        

         this.listLoading = false;
         that.loading = false
         that.pageLoading = false
     },
 }
})
</script>
<style scoped>

</style>

