<template>
    <!-- 设置 -->
    <my-container>
        <div class="content">
            <el-menu default-active="1" class="el-menu-vertical-demo"  
                text-color="#909399" :collapse="isCollapse" style="height: 82vh;"
                :style="isCollapse ? { width: '65px', marginLeft: '-10px' } : { width: '150px' }"
                active-text-color="#409EFF">
                <el-menu-item style="margin-top:10px;" index="1" 
                    @click="menuview = 1">
                    <i class="el-icon-truck"></i>
                    <span slot="title">活动设置</span>
                </el-menu-item> 
                <el-menu-item index="3"  @click="menuview = 2">
                    <i class="el-icon-tickets"></i>
                    <span slot="title">自动分配</span>
                </el-menu-item> 
            </el-menu>
            <el-container style="display: flex; flex-direction: row;">
                <div style="height: 100%; width: 12px;justify-content: center; align-items: center; display: flex; background-color: #f6f8fa;color:#888;font-size:10px;"
                    @click="isCollapse = !isCollapse"><i
                        :class="isCollapse ? 'el-icon-arrow-right' : 'el-icon-arrow-left'"></i>
                </div>
                <el-main v-if="menuview == 1" >
                    <el-col :span="24" style="padding:  5px;margin-top:5px;">
                        <div style="margin-bottom:5px;">
                            <el-button type="primary"   
                                @click="onAdd(9, '新增活动类型')">新增活动类型</el-button>
                            <el-button type="primary" @click="getDataSetList(9)">刷新</el-button>
                        </div>
                        <el-table :data="scenesck" border highlight-current-row row-key="setId">
                            <el-table-column prop="sceneCode" label="活动类型" />
                            <el-table-column label="操作" width="200">
                                <template #default="{ $index, row }">
                                    <el-button type="text"
                                       
                                        @click="onEdit($index, row, 9, '编辑活动类型')">编辑</el-button>
                                    <el-button type="text" 
                                        :loading="row._loading" @click="onDelete($index, row)"> 删除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-col>
                </el-main>
         
                <el-main v-else-if="menuview == 2">
                    <shootAutoAssginmanage ref="accountsWorkCount" :listtype="6"></shootAutoAssginmanage>
                </el-main>
                
            </el-container>
        </div>

        <el-dialog :title="formTitle" :visible.sync="addFormVisiblerole" width="60%" :close-on-click-modal="false"
            :key="opentimes" element-loading-text="拼命加载中" @close="closeAddForm" v-dialogDrag>
            <span>
                <el-form :model="addForm" ref="addForm" label-width="120px" :rules="addFormRules">
                    <el-row :hidden="true">
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item label="主键" prop="setId">
                                <el-input v-model="addForm.sceneId" auto-complete="off" :disabled="true" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="名称" prop="sceneCode">
                                <el-input v-model="addForm.sceneCode" maxlength="50" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </span>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="closeAddForm">取 消</el-button>
                    <my-confirm-button type="submit" :validate="addForValidate" @click="onAddSave" />
                </span>
            </template>
        </el-dialog>
    </my-container>
</template>
<script>
import accountsWorkPostionManage from '@/views/media/shooting/adjustAccounts/accountsWorkPostionManage'; 
import accountsManage from '@/views/media/shooting/adjustAccounts/accountsManageedit'; 
import accountuserPositonManage from '@/views/media/shooting/adjustAccounts/accountuserPositonManage';
import accountGradeSet from '@/views/media/shooting/adjustAccounts/accountGradeSet';

import shootAutoAssginmanage from '@/views/media/shooting/shootAutoAssign/shootAutoAssginmanage';

import { getShootingSetDataById, getShootingSetData, saveShootingSet, deleteShootingSet, moveShootingSetIndex } from '@/api/media/shootingset'
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, accountsWorkPostionManage, 
        accountuserPositonManage, accountsManage, accountGradeSet,shootAutoAssginmanage },
    data() {
        return {
            isCollapse: false,
            opentimes: 1,
            menuview: 1,
            dataTreeckList: false,
            dataTreejsList: false,
            dataTreeshopList: false,
            formTitle: "新增",
            listLoading: false,//树列表加载
            addFormVisiblerole: false,//新增编辑显隐
            editFormLoading: false,//编辑时转圈
            addLoading: false,//新增编辑提交按钮
            scenesck: [],//列表数据集
            scenesjs: [],//列表数据集
            scenesshop: [],//列表数据集
            scenespt: [],//列表数据集
            isEdit: false,//是否编辑模式
            addForm: {
                sceneId: 0,
                sceneCode: null,
                OrderNum: 0,
                sceneName: '',
                setType: 0,
            },
            addFormRules: {
                sceneCode: [{ required: true, message: '请输入', trigger: 'blur' }],
            }
        };
    },
    async mounted() {
        this.getDataList();
    },
    methods: {
        handleClose() { },
        handleOpen() { },
        //获取数数据源
        async getDataList() {
  
            //紧急程度
            await this.getDataSetList(9);
        },
        async getDataSetList(index) {
            this.listLoading = true;
            const res = await getShootingSetData({ setType: index });
            if (!res?.success) {
                return
            }
            this.scenesck= res?.data?.data; 
            this.listLoading = false;
        },

        //新增一级
        async onAdd(type, title) {
            this.formTitle = title;
            this.addFormVisiblerole = true;
            this.opentimes = this.opentimes + 1;

            this.addForm.setType = type;
        },
        //新增验证
        addForValidate() {
            let isValid = false
            this.$refs.addForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },

        async onAddSave() {
            const para = _.cloneDeep(this.addForm);
            var res = await saveShootingSet(para);

            if (!res?.success) {
                return;
            }
            this.addFormVisiblerole = false;
            await this.getDataSetList(this.addForm.setType);
            this.$refs['addForm'].resetFields();

        },
        async onEdit(index, row, type, title) {
            this.formTitle = title;
            this.addFormVisiblerole = true;
            this.opentimes = this.opentimes + 1;
            this.isEdit = true;
            this.editFormLoading = true;
            var res = await getShootingSetDataById({ setId: row.setId });
            if (!res?.success) {

                return;
            }
            var curData = res?.data?.data;
            this.addForm.setId = curData.setId;

            this.addForm.sceneCode = curData.sceneCode;
            this.addForm.setType = type;
            this.addForm.orderNum = curData.orderNum;
            this.editFormLoading = false;
        },
        async onDelete(index, row) {
            this.$confirm('确认删除, 是否继续?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await deleteShootingSet({ setId: row.setId });
                if (res?.success) {
                    this.$message({ type: 'success', message: '删除成功!' });
                    await this.getDataSetList(row.setType);
                }
            }).catch(() => {
                this.$message({ type: 'info', message: '已取消删除' });
            });
        },
        //关闭新增编辑
        async closeAddForm() {
            this.isEdit = false;
            this.$refs.addForm.resetFields();
            this.addFormVisiblerole = false;
        }
    },
};
</script>
<style lang="scss" scoped>
.content {
    display: flex;
    flex-direction: row;
    margin: 0;
}

::v-deep .myheader {
    display: none;
}

.content::v-deep .el-submenu .el-menu-item {
    min-width: 155px !important;
}

// .content::v-deep .el-submenu__icon-arrow{
//     right: 5px !important;
// }
// .content::v-deep .el-menu-item{
//     padding: 0 5px !important;
// }
// .el-menu-vertical-demo:not(.el-menu--collapse) {
//     width: 155px;
//     min-height: 400px;
//   }
// ::v-deep .el-menu-item.is-active{
//     width: 155px;
// }
</style>

