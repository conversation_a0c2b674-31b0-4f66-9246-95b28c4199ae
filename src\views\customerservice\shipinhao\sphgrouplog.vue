<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-button style="padding: 0;margin: 0;">
        <datepicker v-model="filter.sdate"></datepicker>
      </el-button>
      <el-button style="padding: 0;margin: 0;">
        <el-input v-model="filter.updateUser" placeholder="操作人" clearable style="width:80px;" />
      </el-button>
      <el-button style="padding: 0;margin: 0;">
        <el-input v-model="filter.updateType" placeholder="操作类型" clearable style="width:120px;" />
      </el-button>
      <el-button style="padding: 0;margin: 0;">
        <el-input v-model.trim="filter.updateContextText" placeholder="操作内容" clearable style="width:120px;" />
      </el-button>
      <el-button type="primary" @click="onSearch">查询</el-button>
    </template>

    <!--列表-->
    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      :tableData='tableData' @select='selectchange' :isSelection='false' :tableCols='tableCols'
      :loading="listLoading">
    </vxetablebase>
    
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import cesTable from "@/components/Table/table.vue";
import datepicker from '@/views/customerservice/datepicker';
import { getGroupLogList } from '@/api/customerservice/shipinhaoinquirs.js';

const tableCols = [
  { sortable: 'custom', istrue: true, width: '150', align: 'center', prop: 'createdTime', label: '操作时间', },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', prop: 'createdUserName', label: '操作人', },
  { sortable: 'custom', istrue: true, width: '150', align: 'center', prop: 'updateType', label: '操作类型', },
  { sortable: 'custom', istrue: true, width: '900', align: 'center', prop: 'updateContextText', label: '操作内容', },
];

export default {
  name: "sphgrouplogtx",
  components: { MyContainer, cesTable, datepicker, vxetablebase },
  data() {
    return {
      that: this,
      pageLoading: false,
      listLoading: false,
      filter: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'createdTime',
        isAsc: false,
        //过滤条件
        sdate: [],
        startDate: '',
        endDate: '',
        updateUser: '',
        updateType: '',
        updateContextText: '',
      },
      tableCols: tableCols,
      tableData: [],
      total: 0,
      sels: [],
      selids: [],
    }
  },
  async mounted() {
    this.onSearch();
  },
  methods: {
    onRefresh() {
      this.onSearch();
    },
    //每页数量改变
    Sizechange(val) {
      this.filter.currentPage = 1;
      this.filter.pageSize = val;
      this.getList();
    },
    //当前页改变
    Pagechange(val) {
      this.filter.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.filter.orderBy = prop
        this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async onSearch() {
      //点击查询按钮时才将页数重置为1
      this.filter.currentPage = 1;
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      this.listLoading = true;
      if (this.filter.sdate) {
        this.filter.startDate = this.filter.sdate[0];
        this.filter.endDate = this.filter.sdate[1];
      }
      else {
        this.filter.startDate = null;
        this.filter.endDate = null;
      }
      const para = { ...this.filter };
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      try {
        const { data, success } = await getGroupLogList(params);
        if (success) {
          this.tableData = data.list;
          this.total = data.total;
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.listLoading = false;
      }
    },
  }
}
</script>