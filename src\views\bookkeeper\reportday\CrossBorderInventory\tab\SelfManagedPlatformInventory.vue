<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>

        <el-form-item label="">
          <el-date-picker v-model="Filter.yearMonth" type="month" placeholder="选择月" :value-format="'yyyy-MM'">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="">
          <el-input v-model.trim="Filter.skc" placeholder="SKC" maxlength="50" clearable class="publicCss" />
        </el-form-item>


        <el-form-item label="">
          <el-input v-model.trim="Filter.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
        </el-form-item>


        <el-form-item label="">
          <el-input v-model.trim="Filter.sku" placeholder="SKU" maxlength="50" clearable class="publicCss" />
        </el-form-item>


        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport" style="margin-left: 5px;"
            v-if="checkPermission('SelfManagedPlatformInventory')">导出</el-button>
          <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button @click="startImport"
            type="primary" icon="el-icon-share" @command="handleCommand"> 导入
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a">下载模版</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-form-item>
      </el-form>
    </template>
    <!--列表-->
    <vxetablebase ref="table" :id="'crossBorderCourierFeeAverage202408310425'" :that='that' :isIndex='true'
      :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='dahuixionglist' :tableCols='tableCols'
      :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="listLoading" :height="'100%'">
    </vxetablebase>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getjSpeedDriveList" />
    </template>
    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 75px;">
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

  </my-container>
</template>
<script>

import { pageInventorySheInSelfList, importInventorySheInSelf, exportInventoryPlatBySheInSelf } from '@/api/bookkeeper/crossBorderV2'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
const tableCols = [
  { istrue: true, prop: 'yearMonth', label: '月份', sortable: 'custom', },
  { istrue: true, prop: 'skc', label: 'SKC', sortable: 'custom', },
  { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', },
  { istrue: true, prop: 'sku', label: 'SKU', sortable: 'custom', },
  { istrue: true, prop: 'inventoryNumber', label: '库存数量', sortable: 'custom', },
  { istrue: true, prop: 'cost', label: '单个成本', sortable: 'custom', },
  { istrue: true, prop: 'totalCost', label: '总成本', sortable: 'custom', },
];
export default {
  name: "crossBorderCourierFeeAverage",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, vxetablebase },
  data() {
    return {
      that: this,
      editLoading: false,
      addVisible: false,
      Filter: {
      },
      userList: [],
      groupList: [],
      dahuixionglist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: {},
      pager: { OrderBy: "id", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
      platform: 1,
      yearMonth: "",
      dialogVisible: false,//导入弹窗
      fileList: [],//上传文件列表
      uploadLoading: false,//上传按钮loading
      fileparm: {},//上传文件参数
      yearMonthDay: null,//导入日期
      shopList: [],
    };
  },
  async mounted() {
    this.onSearch();
  },
  methods: {
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getjSpeedDriveList();
    },
    async getjSpeedDriveList() {
      const para = { ...this.Filter };
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,

      };
      this.listLoading = true;
      const res = await pageInventorySheInSelfList(params);
      this.listLoading = false;
      this.total = res.data.total
      this.dahuixionglist = res.data.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },

    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importInventorySheInSelf(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.onSearch()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    async handleCommand(command) {
      switch (command) {
        //下载模版
        case 'a':
          await this.downLoadFile()
          break;
      }
    },
    async downLoadFile() {
      window.open("/static/excel/CrossBorderDownloadTemplate/SHEIN自营平台库存数据导入模版.xlsx", "_blank");
    },
    async onExport() {//导出列表数据；

      this.loading = true
      const para = { ...this.Filter };
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,

      };
      var res = await exportInventoryPlatBySheInSelf(params);
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute(
        "download",
        "SHEIN自营-平台库存_" + new Date().toLocaleString() + ".xlsx"
      );
      aLink.click();
      this.loading = false
    },

  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>