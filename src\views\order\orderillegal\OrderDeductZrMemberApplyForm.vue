<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" ref="form" label-width="120px" label-position="right" >       
                <el-row>
                    <el-col :span="23" :offset="1">
                        <span style="color:red">
                            申诉须知：
                            <br/>
1、申诉基础步骤：发起申诉 --> 提交申诉凭证 --> 审核凭证 --> 判定申诉结果 --> 申诉通过后调整新责任部门，原部门责任及相关事宜剔除
<br/>
2、申诉机会仅一次，请您使用好申诉权利。
<br/>
3、申诉时效为线上订单扣罚发生起的48小时内，超时导致申诉入口关闭，无法支持再次申诉。
                        </span>
                    </el-col>
                </el-row>       
             
                <el-row>     
                    <el-col :span="12">
                        <el-form-item label="线上单号：">
                            <el-button type="text" @click="showLogDetail(form.orderNo)">{{ form.orderNo }}</el-button>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="新责任人员：" prop="newMemberId" :rules="[
                        { required: true, message: '请选择新责任成员', trigger: ['blur', 'change'] }    
                        ]">
                            <el-select v-if="formEditMode" v-model="form.newMemberId" filterable @change="newMemberIdChange">
                                <el-option v-for="item in brandlist" :key="item.key" :label="item.value"  :value="item.key"/>
                            </el-select>
                            <span v-else>{{ form.newMemberName }}</span>
                        </el-form-item>
                    </el-col>  
                </el-row>

                <el-row v-if="form.id>0 && (form.applyState==-1 || form.applyState==2)">
                    <el-col :span="4">
                        <el-form-item label="审核状态：">
                            {{fmtApplyState(form.applyState)}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="7">
                        <el-form-item label="审核时间：">
                            {{form.auditTime}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="审核人：">
                            {{form.auditUserName}}
                        </el-form-item>
                    </el-col>
                                   
                </el-row>
                <el-row  v-if="form.id>0 && (isAuditor || form.applyState==-1 || form.applyState==2)">
                    <el-col :span="24">
                        <el-form-item label="审核意见：" :rules="[
                        { required: true, message: '请填写审核意见', trigger: ['blur', 'change'] }    
                        ]">
                            <el-input  v-if="isAuditor && form.applyState==1" type="textarea"  :rows="3" v-model="form.auditRemark" clearable maxlength="100" show-word-limit />
                            <span v-else>{{ form.auditRemark }}</span>                         
                        </el-form-item>
                    </el-col>     
                </el-row>
                      
                <el-row>
                    <el-col :span="24" style="max-height:400px;overflow:auto">
                        <el-form-item label="申诉资料："  prop="applyContent" :rules="[
                        { required: true, message: '请填写申诉资料：', trigger: ['blur'] }    
                        ]">
                            <yh-quill-editor :value.sync="form.applyContent" v-if="formEditMode"></yh-quill-editor>
                            <div v-else v-html="form.applyContent" class="tempdiv"></div>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">  
                    <el-button @click="onClose">关闭</el-button>                     
                    <el-button v-if="mode<3 && (!form.applyState || form.applyState<1)" type="primary" @click="onSave(true)">提交申诉</el-button>   

                    <el-button v-if="form.applyState==1 && isAuditor && form.isSelfAudit" type="primary" @click="audit(true,true)">同意(仅新责任人可用)</el-button>
                    <el-button  v-if="form.applyState==1 && isAuditor && form.isSelfAudit" type="danger" @click="audit(true,false)">不同意(仅新责任人可用)</el-button>

                    <el-button v-if="form.applyState==1 && isAuditor" type="primary" @click="audit(false,true)">审核通过(管理人员使用)</el-button>
                    <el-button  v-if="form.applyState==1 && isAuditor" type="danger" @click="audit(false,false)">审核驳回(管理人员使用)</el-button>
                </el-col>
            </el-row>
        </template>

        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
            append-to-body>
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo" :isTx="isTx" style="z-index:10000;height:600px" />
        </el-dialog>

    </my-container>
</template>
<script>  

    import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode ,DeductOrderZrDeptActions,DeductOrderZrReasons} from "@/utils/tools";
    import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import {GetZrMemberAppealDtl, MemberAppealSubmit,AuditMemberAppealBySelf,AuditMemberAppealByMng } from "@/api/order/orderdeductmoney";

    import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'

    import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
    import {getAllWarehouse,getAllProBrand} from '@/api/inventory/warehouse'


    //机器人查询状态 1成功、0下架、-1失败
    const fmtJqrNoticeState=(v)=>{
        switch(v){
            case 0:return '下架';
            case 1:return '已查询';
            case -1:return '失败';
        }
        return ' ';
    };

    const fmtApplyState=function(val){
        if(val==-1) return "已拒绝";
        else if(val==0) return "待申请";
        else if(val==1) return "申请中";
        else if(val==2) return "已审核";
        return val;
    }

    export default {
        name: "OrderDeductZrMemberApplyForm",
        components: { MyContainer, MyConfirmButton,  YhQuillEditor ,OrderActionsByInnerNos},
        data() {
            return {              
                that: this,
                mode:3,
                illegalTypeList:[],
                brandlist: [], 
                zrDeptActions:DeductOrderZrDeptActions,
                zrReasons:DeductOrderZrReasons,
                form: {
                    newMemberId:null
                },
            
                //summaryarry: {},
                pageLoading: false,
                curRow: null,
                formEditMode: true,//是否编辑模式              
                dialogHisVisible:false,
                isTx:false,      
            };
        },
        async mounted() {
            let illegalType = await ruleIllegalType();
            this.illegalTypeList = illegalType.options;

            await this.setBandSelect();
        },
        computed: {      
            illegalTypeName(){
                let opt=this.illegalTypeList.find(x=>x.value==this.form.illegalType);
                if(opt)
                    return opt.label;
                else
                    return '';
            }
        },
        methods: {   
            async setBandSelect(){
                var res= await  getAllProBrand();
                if (!res?.success) return;
                this.brandlist = res.data;
            }, 
            fmtApplyState:fmtApplyState, 
            fmtJqrNoticeState:fmtJqrNoticeState,
            showLogDetail (orderNo) {
                this.isTx=orderNo.indexOf('-')<0;
                this.dialogHisVisible = true;
                this.orderNo = orderNo;
            },  
            newMemberIdChange(){              
                if(this.form.newMemberId){
                  
                    let opt=this.brandlist.find(x=>x.key==this.form.newMemberId);
                    if(opt){
                        this.form.newMemberName=opt.value;                      
                    }
                }
            },   
            //审核
            async audit(isSelf,isPass){
                if(!this.form.auditRemark){
                    this.$message.error('请填写审批意见！');
                    return false;
                }

                let dto={
                    id:this.form.id,
                    auditState:isPass?1:0,
                    auditRemark:this.form.auditRemark
                };

                this.pageLoading = true;

                let rlt={}
                if(isSelf)
                    rlt=await AuditMemberAppealBySelf(dto);
                else 
                    rlt=await AuditMemberAppealByMng(dto);
                if(rlt && rlt.success){
                    this.$message.success('操作成功！');
                    this.$emit('afterSave');
                    this.$emit('close');
                }      
                this.pageLoading = false;          
            },   
            onClose(){
                this.$emit('close');
            },  
            async onSave(isClose){
                if(await this.save()){
                    this.$emit('afterSave');
                    if(isClose)
                        this.$emit('close');
                }
            },
            async loadData({id,deductZrMemberId, memberType, orderNo,  mode,isAuditor}) {     
                let self=this;         
                self.pageLoading = true;
                self.formEditMode = mode!=3;
                self.mode = mode;     
                self.isAuditor= !!isAuditor;  
                
                this.form.orderNo=orderNo;
                this.form.deductZrMemberId=deductZrMemberId;
                this.form.newMemberId=null;
                this.form.newMemberName="";
                this.form.memberType=memberType;
                this.form.id=!!id? id:0;
                if(id && id>0){
                    let rlt = await GetZrMemberAppealDtl( {id:id} );
                    if (rlt && rlt.success) {
                        this.form = rlt.data;                                           
                        if(this.form.applyState>0){
                            self.mode=3;
                            self.formEditMode = self.mode!=3;
                        }
                        this.pageLoading = false;
                    }else{
                        this.onClose();
                    }
                }
               
                self.pageLoading = false;
            },
            async save() {
                this.pageLoading = true;
                
                let saveData = { ...this.form };            
                saveData.applyState=1;

                try {
                    await this.$refs["form"].validate();
                } catch (error) {
                    this.pageLoading = false;
                    return false;
                } 

                let rlt = await MemberAppealSubmit(saveData);
                if (rlt && rlt.success) {
                    this.$message.success('提交成功！');           
                }

                this.pageLoading = false;
              
                return (rlt && rlt.success);
            }
        },
    };
</script>
<style lang="scss" scoped>
   
    .tempdiv ::v-deep img {
        width: auto;
        max-width: 1000px;
    }
</style>