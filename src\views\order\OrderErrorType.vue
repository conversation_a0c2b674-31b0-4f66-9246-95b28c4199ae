<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="部门:">
                    <el-select v-model="filter.dutyDept" placeholder="请选择" @change="onSearch" :clearable="true" :collapse-tags="true" filterable>
                        <el-option v-for="item in deptList" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item label="原因:">
                    <el-input v-model="filter.errorType" placeholder="原因" @change="onSearch" />
                </el-form-item>
                <el-form-item label="建议:">
                    <el-input v-model="filter.errorTypeDesc" placeholder="建议" @change="onSearch" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>

        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
        </ces-table>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%">
            <el-alert title="温馨提示：重新导入会覆盖原有数据" type="info" :closable="false" show-icon style="margin-bottom:10px;"></el-alert>
            <span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx" :http-request="uploadFile" :file-list="fileList">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传</el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-drawer :title="formtitle" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="addFormVisible" direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
            <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options" style="margin-top:10px;" />
            <div class="drawer-footer">
                <el-button @click.native="addFormVisible = false">取消</el-button>
                <my-confirm-button type="submit" :loading="addLoading" @click="onAddSubmit" />
            </div>
        </el-drawer>
    </my-container>
</template>

<script>
    import MyContainer from '@/components/my-container'
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import {
        importOrderErrorType,
        pageOrderErrorType,
        exportOrderErrorType,
        getDutyDept,
        changeOrderErrorTypeDutyDept,
        getDutyDeptInfo,
        changeOrderErrorType,
    } from "@/api/order/ordererror"
    const tableCols = [
        { istrue: true, prop: 'dutyDept', label: '部门', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'dutyDeptPrincipal', label: '部门负责人', width: '120', sortable: 'custom', },
        { istrue: true, prop: 'errorType', label: '原因', width: '200', sortable: 'custom', },
        { istrue: true, prop: 'errorTypePrincipal', label: '原因负责人', width: '120', sortable: 'custom', },
        { istrue: true, prop: 'errorTypeDesc', label: '建议', width: 'auto', sortable: 'custom', },
        { istrue: true, prop: 'createdUserName', label: '创建人', width: '100', sortable: 'custom', },
        { istrue: true, prop: 'createdTime', label: '创建时间', width: '150', sortable: 'custom', },
        { istrue: true, prop: 'modifiedUserName', label: '修改人', width: '100', sortable: 'custom', },
        { istrue: true, prop: 'modifiedTime', label: '修改时间', width: '150', sortable: 'custom', },
        { istrue: true, type: 'button', label: '操作', width: '70', btnList: [{ label: "编辑", handle: (that, row) => that.onEditRow(row) }] },

    ];
    const tableHandles1 = [
        { label: "修改部门", handle: (that) => that.onEditDept() },
        { label: "导入", handle: (that) => that.startImport() },
        { label: "导出", handle: (that) => that.onExport() },
    ];
    export default {
        name: 'Roles',
        components: { cesTable, MyContainer, MyConfirmButton },
        data () {
            return {
                that: this,
                filter: {
                    dutyDept: "",
                    errorType: null,
                    errorTypeDesc: null
                },
                deptList: [],
                list: [],
                summaryarry: {},
                pager: { OrderBy: "dutyDept", IsAsc: false },
                tableCols: tableCols,
                tableHandles: tableHandles1,
                total: 0,
                sels: [],
                listLoading: false,
                pageLoading: false,
                dialogVisible: false,
                addFormVisible: false,
                addLoading: false,
                deleteLoading: false,
                formtitle: "新增",
                fileList: [],
                //自动表单 - Start
                autoform: {
                    fApi: {},
                    rule: [],
                    options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
                },
                addFormVisible: false,
                addLoading: false,
                formtitle: "",
                isEditDept: false,
                //自动表单 -End
            }
        },
        async mounted () {
            await this.setDutyDeptCondition();
            await this.getlist();
        },
        methods: {
            //设置部门下拉
            async setDutyDeptCondition (val) {
                const res = await getDutyDept();
                this.deptList = res.data;
            },
            //下载导入模板
            downloadTemplate () {
                window.open("../static/excel/异常分类导入模板.xlsx", "_self");
            },
            //开始导入
            startImport () {
                this.dialogVisible = true;
            },
            //取消导入
            cancelImport () {
                this.dialogVisible = false;
            },
            //上传成功
            uploadSuccess (response, file, fileList) {
                if (response.code == 200) {
                } else {
                    fileList.splice(fileList.indexOf(file), 1);
                }
            },
            //提交导入
            submitUpload () {
                this.$refs.upload.submit();
            },
            //上传文件
            async uploadFile (item) {
                const form = new FormData();
                form.append("token", this.token);
                form.append("upfile", item.file);
                const res = await importOrderErrorType(form);
                if (res?.success) {
                    this.$message({
                        message: '上传成功,正在导入中...',
                        type: "success",
                    });
                    this.dialogVisible = false;
                    this.fileList = [];
                }
            },
            //导出
            async onExport () {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
                var res = await exportOrderErrorType(params);
                loadingInstance.close();
                if (!res?.data) return
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '订单异常分类_' + new Date().toLocaleString() + '.xlsx')
                aLink.click();
            },
            //获取查询条件
            getCondition () {
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }

                return params;
            },
            //查询第一页
            async onSearch () {
                this.$refs.pager.setPage(1)
                await this.getlist();
            },
            //分页查询
            async getlist () {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }

                this.listLoading = true
                const res = await pageOrderErrorType(params)
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data.summary;
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
            },
            //排序查询
            async sortchange (column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderBy = column.prop;
                    this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            selsChange: function (sels) {
                this.sels = sels
            },

            //==自动表单Start===========================
            //加载部门
            async setDutyDept () {
                let rule = { validate: [{ type: 'string', required: true, message: '请选择' }], options: [{ value: null, label: '请选择' }] }
                var res = await getDutyDept();
                var data = [];
                for (var k = 0; k < res.data.length; k++) {
                    data.push({ value: res.data[k], label: res.data[k] });
                }
                rule.options = data;
                return rule;
            },
            async updateRuleDept (dept) {
                await this.autoform.fApi.setValue({ dutyDeptNew: dept });
                var res = await getDutyDeptInfo({ dutyDept: dept });
                if (!res?.code) {
                    return;
                }
                await this.autoform.fApi.setValue({ dutyDeptPrincipal: res.data.dutyDeptPrincipal });
            },
            async onEditDept () {
                this.formtitle = '修改部门';
                this.isEditDept = true;
                this.addFormVisible = true;
                var that = this;
                this.autoform.rule = [
                    {
                        type: 'select', field: 'dutyDept', title: '原部门', validate: [{ type: 'string', required: true, message: '请选择' }], value: "",
                        options: [], async update (val, rule) { if (val) { await that.updateRuleDept(val) } }, ...await this.setDutyDept(),
                    },
                    {
                        type: 'input', field: 'dutyDeptNew', title: '新部门', validate: [{ type: 'string', required: true, message: '新部门必填' }], value: "",
                        options: []
                    },
                    {
                        type: 'input', field: 'dutyDeptPrincipal', title: '部门负责人', validate: [{ type: 'string', required: true, message: '部门负责人必填' }], value: "",
                        options: []
                    },
                ];

                var arr = Object.keys(this.autoform.fApi);
                if (arr.length > 0)
                    this.autoform.fApi.resetFields();
            },
            async onAddSubmit () {
                this.addLoading = true;
                await this.autoform.fApi.validate(async (valid, fail) => {
                    if (valid) {
                        const formData = this.autoform.fApi.formData();
                        formData.id = formData.id ? formData.id : "";
                        formData.Enabled = true;
                        if (this.isEditDept) {
                            const res = await changeOrderErrorTypeDutyDept(formData);
                            if (res.code == 1) {
                                await this.getlist();
                                await this.setDutyDeptCondition();
                                this.addFormVisible = false;
                                this.$message({
                                    message: '修改成功',
                                    type: 'success'
                                });
                            }
                        }
                        else {
                            const res = await changeOrderErrorType(formData);
                            if (res.code == 1) {
                                await this.getlist();
                                this.addFormVisible = false;
                                this.$message({
                                    message: '修改成功',
                                    type: 'success'
                                });
                            }
                        }
                    } else {
                        //todo 表单验证未通过
                    }
                })
                this.addLoading = false;
            },
            async onEditRow (row) {
                this.formtitle = '修改';
                this.isEditDept = false;
                this.addFormVisible = true;
                var that = this;
                this.autoform.rule = [
                    { type: 'hidden', field: 'id', title: 'id', value: '' },
                    {
                        type: 'input', field: 'errorType', title: '原因', props: { disabled: true }, validate: [{ type: 'string', required: true, message: '请选择' }], value: "",
                        options: [],
                    },
                    {
                        type: 'input', field: 'errorTypePrincipal', title: '负责人', validate: [{ type: 'string', required: true, message: '负责人必填' }, { type: 'string', max: 5, message: '负责人不能大于5个字符' }], value: "",
                        options: []
                    },
                    {
                        type: 'input', field: 'errorTypeDesc', title: '建议', validate: [{ type: 'string', required: true, message: '建议必填' }, { type: 'string', max: 50, message: '建议不能大于50个字符' }], value: "",
                        options: []
                    },
                ];

                var arr = Object.keys(this.autoform.fApi);
                if (arr.length > 0)
                    this.autoform.fApi.resetFields();

                setTimeout(async () => {
                    await this.autoform.fApi.setValue({ id: row.id });
                    await this.autoform.fApi.setValue({ errorType: row.errorType });
                    await this.autoform.fApi.setValue({ errorTypePrincipal: row.errorTypePrincipal });
                    await this.autoform.fApi.setValue({ errorTypeDesc: row.errorTypeDesc });
                }, 100);

            },
            //==自动表单End  ===========================
        }
    }
</script>
<style scoped>
    ::v-deep .el-link.el-link--primary {
        margin-right: 7px;
    }
</style>
