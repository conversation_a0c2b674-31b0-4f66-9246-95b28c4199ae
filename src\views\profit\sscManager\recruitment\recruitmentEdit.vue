<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" ref="refruleForm" label-width="130px" class="demo-ruleForm">
        <el-form-item label="类型：">
            <!-- <el-select v-model="ruleForm.type" placeholder="类型：" class="publicCss" clearable>
                <el-option v-for="item in typeList" :key="item" :label="item" :value="item" />
            </el-select> -->
          {{ ruleForm.type }}
        </el-form-item>
        <el-form-item label="招聘人员：">
          {{ ruleForm.name }}
        </el-form-item>
        <div style="font-size: 15px; font-weight: 600; margin-left: 10px;">招聘软件数据(仓库选填)</div>
        <el-form-item label="被查看：">
          <el-input v-model.trim="ruleForm.viewed" placeholder="请输入" maxlength="50" clearable class="publicCss" />
        </el-form-item>
        <el-form-item label="我看过：">
          <el-input v-model.trim="ruleForm.saw" placeholder="请输入" maxlength="50" clearable class="publicCss" />
        </el-form-item>
        <el-form-item label="牛人新招呼：">
          <el-input v-model.trim="ruleForm.expertNewGreeting" placeholder="请输入" maxlength="50" clearable
            class="publicCss" />
        </el-form-item>
        <el-form-item label="我沟通：">
          <el-input v-model.trim="ruleForm.myCommunication" placeholder="请输入" maxlength="50" clearable
            class="publicCss" />
        </el-form-item>
        <el-form-item label="获取简历/联系方式：">
          <el-input v-model.trim="ruleForm.phoneCount" placeholder="请输入" maxlength="50" clearable
            class="publicCss" />
        </el-form-item>
        <!-- <el-form-item label="邀约排名：">
          <el-input v-model.trim="ruleForm.invitationRank" disabled placeholder="请输入" maxlength="50" clearable
            class="publicCss" />
        </el-form-item>
        <el-form-item label="入职排名：">
          <el-input v-model.trim="ruleForm.entryRank" disabled placeholder="请输入" maxlength="50" clearable
            class="publicCss" />
        </el-form-item>
        <el-form-item label="日均人效：">
          <el-input v-model.trim="ruleForm.dailyAverage" disabled placeholder="请输入" maxlength="50" clearable
            class="publicCss" />
        </el-form-item>
        <el-form-item label="人效排名：">
          <el-input v-model.trim="ruleForm.dailyAverageRank" disabled placeholder="请输入" maxlength="50" clearable
            class="publicCss" />
        </el-form-item> -->
        <el-form-item label="邀约人数：">
          <inputNumberYh v-model="ruleForm.inviteCount" :placeholder="'邀约人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="接受面试：">
          <el-input v-model.trim="ruleForm.interviewAccept" placeholder="请输入" maxlength="50" clearable
            class="publicCss" />
        </el-form-item>


        <div style="font-size: 15px; font-weight: 600; margin-left: 10px;">线下面试数据(全区域填报)</div>
        <el-form-item label="到面人数：">
          <inputNumberYh v-model="ruleForm.interviewAcceptCount" :placeholder="'到面人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="复试通过人数：">
          <inputNumberYh v-model="ruleForm.interviewPassCount" :placeholder="'复试通过人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="入职人数：">
          <inputNumberYh v-model="ruleForm.newHiresCount" :placeholder="'入职人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="离职人数：">
          <inputNumberYh v-model="ruleForm.resignationsCount" :placeholder="'离职人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="留存人数：">
          <inputNumberYh v-model="ruleForm.retainCount" :placeholder="'留存人数'" class="publicCss" />
        </el-form-item>


        <div style="font-size: 15px; font-weight: 600; margin-left: 10px;">招聘排名</div>
        <el-form-item label="入职排名：">
          <el-input v-model.trim="ruleForm.entryRank" disabled placeholder="请输入" maxlength="50" clearable
            class="publicCss" />
        </el-form-item>
        <el-form-item label="日均人效：">
          <el-input v-model.trim="ruleForm.dailyAverage" disabled placeholder="请输入" maxlength="50" clearable
            class="publicCss" />
        </el-form-item>
        <el-form-item label="人效排名：">
          <el-input v-model.trim="ruleForm.dailyAverageRank" disabled placeholder="请输入" maxlength="50" clearable
            class="publicCss" />
        </el-form-item>


      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { recruitDirectorSubmit } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission'
export default {
  name: 'recruitmentEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    typeList: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      selectProfitrates: [],
      ruleForm: {
        name: '',//招聘专员
        viewed: '',//被查看
        saw: '',//我看过
        expertNewGreeting: '',//牛人新招呼
        myCommunication: '',//我沟通
        interviewAccept: '',//接受面试
        inviteCount: '',//邀约人数
        interviewAcceptCount: '',//到面人数
        interviewPassCount: '',//复试通过人数
        newHiresCount: '',//入职人数
        resignationsCount: '',//离职人数
        retainCount: '',//留存人数
      },
    }
  },

  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };
  },
  methods: {
    cancellationMethod() {
      this.$emit('cancellationMethod', 2);
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
            this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing")
          const { data, success } = await recruitDirectorSubmit(this.ruleForm)
          if (!success) {
            return
          }
          this.$emit('cancellationMethod', 1);
          resetForm(formName);
        } else {
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}
</style>
