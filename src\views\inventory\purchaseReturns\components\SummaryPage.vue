<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss"
                    start-placeholder="开始到账日期" end-placeholder="结束到账日期" />
                <el-select filterable v-model="ListInfo.companyList" clearable placeholder="请选择地区" class="publicCss" multiple collapse-tags>
                    <el-option key="义乌" label="义乌" value="义乌"></el-option>
                    <el-option key="南昌" label="南昌" value="南昌"></el-option>
                    <el-option key="武汉" label="武汉" value="武汉"></el-option>
                    <el-option key="深圳" label="深圳" value="深圳"></el-option>
                    <el-option key="其他" label="其他" value="其他"></el-option>
                </el-select>
                <el-select v-model="ListInfo.deptIdList" clearable filterable placeholder="请选择架构" class="publicCss" multiple collapse-tags>
                    <el-option v-for="item in purchasegrouplist" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.brandIdList" clearable filterable placeholder="请选择采购员" class="publicCss" multiple collapse-tags>
                    <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.titleList" clearable filterable placeholder="请选择岗位" class="publicCss" multiple collapse-tags>
                    <el-option v-for="item in positionList" :key="item.titleName" :label="item.titleName"
                        :value="item.titleName" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :showsummary="true"
            :summaryarry="summary" @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
            :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { formatTime } from "@/utils";
import dateRange from "@/components/date-range/index.vue";
import { getAllProBrand, getBianManPositionListV2 } from '@/api/inventory/warehouse'
import { getPurchaseDeductionOnusDeptList } from '@/api/inventory/purchaseordernew'
import {
    getPurchaseReturnExWarehouseSummaryPage,
    exportPurchaseReturnExWarehouseSummary
} from '@/api/inventory/purchaseReturnExWarehouse'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'brandName', label: '采购', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'company', label: '地区', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'deptName', label: '架构', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'title', label: '岗位', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'refundAmountCount', label: '已退货退款笔数', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsTotalCost', label: '货物总成本', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalRefundAmount', label: '厂家退回金额', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'purchaseBonus', label: '采购奖金', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shippingFee', label: '退货运费', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'reportedLoss', label: '报损金额', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'actualMoney', label: '公司实际收回', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'unsalableCount', label: '滞销完成数', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'unsoldQty', label: '滞销笔数', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'recoveryRate', label: '公司回收成本比例(%)', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: formatTime(dayjs().startOf("month"), "YYYY-MM-DD"),//开始时间
                endDate: formatTime(new Date(), "YYYY-MM-DD"),
                companyList: [],
                deptIdList: [],
                brandIdList: [],
                titleList: []
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            positionList: [],
            brandlist: [],
            purchasegrouplist: [],
            summary: {}
        }
    },
    async mounted() {
        this.init()
        await this.getList()
    },
    methods: {
        async init() {


            var res2 = await getAllProBrand();
            this.brandlist1 = res2.data;
            this.brandlist = res2.data.map(item => {
                return { value: item.key, label: item.value };
            });
            var resPosition = await getBianManPositionListV2();
            this.positionList = resPosition?.data;
            //采购组
            let { data: deptList, success } = await getPurchaseDeductionOnusDeptList();
            if (success) {
                this.purchasegrouplist = deptList.map(item => { return { value: item.dept_id, label: item.full_name }; });
            }
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await exportPurchaseReturnExWarehouseSummary(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '采购退货出库汇总' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getPurchaseReturnExWarehouseSummaryPage(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.summary = data.summary
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
::v-deep .el-select__tags-text {
  max-width: 80px;
}
</style>
