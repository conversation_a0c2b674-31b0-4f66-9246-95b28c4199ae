<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :summaryarry="summaryarry" :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='pddmergeinquirsstatisticslist' @select='selectchange'
            :isSelection='false' :tableCols='tableCols' :loading="listLoading">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>

                <el-button style="padding: 0;margin: 0;">
                    <el-select style="float:left;" multiple v-model="Filter.groupNameList" placeholder="组名称" clearable
                        collapse-tags filterable>
                        <el-option v-for="item in groupList" :key="'merge1' + item" :label="item.groupname" :value="item.groupname" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border:none">
                    <el-select style="float:left;" multiple v-model="Filter.shopCodeList" placeholder="店铺" clearable
                        collapse-tags filterable>
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode">
                        </el-option>
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;">
                    <el-select style="float:left;" :multiple=false v-model="Filter.partitionName" placeholder="分区" clearable
                        collapse-tags filterable>
                        <el-option v-for="item in partitionList" :key="item.partitionName" :label="item.partitionName"
                            :value="item.partitionName">
                        </el-option>
                    </el-select>
                </el-button>

<!--                <el-button style="padding: 0;margin: 0;">
                    <el-select style="float:left;" v-model="Filter.position" placeholder="岗位" clearable>
                        <el-option v-for="item in positionOptions" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-button>-->

                <el-button style="padding: 0;margin: 0;">
                    <datepicker v-model="Filter.Sdate"></datepicker>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onExport" style="margin-left: 10px;">导出</el-button>

            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getpagelist" />
        </template>

        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
            :close-on-click-modal="true" v-dialogDrag>
            <div>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
                </span>
            </div>
            <!-- <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span> -->
        </el-dialog>

    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import {
    getPddGroup2,
    getPddShopInquirsStatisticsMargeList,
    getPddShopInquirsStatisticsMargeMap,
    exportPddShopInquirsStatisticsMargeList,
    getAllPartitions
} from '@/api/customerservice/pddInquirsnew'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar';
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';

import Decimal from 'decimal.js';
function precision(number, multiple) {
    return new Decimal(number).mul(new Decimal(multiple));
}

const tableCols = [
    { istrue: true, prop: 'partitionName', label: '分区名称', width: '140', sortable: 'custom' },
    { istrue: true, prop: 'shopCode', label: '店名', width: '160', sortable: 'custom', type: "click", handle: (that, row, column, cell) => that.groupclick(row, column, cell), formatter: (row) => row.shopName },

    { istrue: true, prop: 'inquirs', label: '咨询人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'manualReply', label: '人工接待人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsCount', label: '询单人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'receiveCount', label: '最终成团人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'successpayrate', label: '询单转化率', width: '80', sortable: 'custom', 
        formatter: (row) => { return (row.successpayrate ? row.successpayrate : 0) + "%" } 
        // formatter: (row) => { return (row.successpayrate ? precision(row.successpayrate, 100) : 0) + "%" }
    },

    { istrue: true, prop: 'threeSecondLost', label: '3分钟未回复人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'threeSecondGet', label: '3分钟回复人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'threeSecondReplyRate', label: '3分钟人工回复率', width: '90', sortable: 'custom', 
        formatter: (row) => { return (row.threeSecondReplyRate ? row.threeSecondReplyRate : 0) + "%" } 
        // formatter: (row) => { return (row.threeSecondReplyRate ? precision(row.threeSecondReplyRate, 100) : 0) + "%" }
    },
    { istrue: true, prop: 'responseTime', label: '均响(秒)', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'thirtySecondResponseRate', label: '30秒应答率', width: '80', sortable: 'custom', 
        formatter: (row) => { return (row.thirtySecondResponseRate ? row.thirtySecondResponseRate : 0) + "%" } 
        // formatter: (row) => { return (row.thirtySecondResponseRate ? precision(row.thirtySecondResponseRate, 100) : 0) + "%" }
    },

    { istrue: true, prop: 'outTimes', label: '出勤人次', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsPercentstr', label: '询单占比', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsPriceRate', label: '客单价', width: '80', sortable: 'custom' },

    { istrue: true, prop: 'salesvol', label: '客服销售额（元）', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'serviceScore', label: '客服服务分', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'lowRatingOrderCount', label: '评分≤3订单数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'disputeRefundCount', label: '纠纷退款数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'complainCount', label: '投诉数', width: '80', sortable: 'custom' },

    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];
export default {
    name: "pddmergeinquirsstatistics",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar },
    data() {
        return {
            dialogMapVisible: { visible: false, title: "", data: [] },
            that: this,
            Filter: {
                Sdate: [formatTime(new Date(new Date().getTime() - 3600 * 1000 * 24 * 30), "YYYY-MM-DD 00:00:00"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")],
                partitionName: '', // 分区筛选
                position: '',        // 岗位筛选：售前/售后/一体
            },
            shopList: [],
            userList: [],
            groupList: [],
            partitionList: [], // 分区列表
            positionOptions: ["售前", "售后", "一体"], // 岗位选项
            pddmergeinquirsstatisticslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "ipscount", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
        };
    },
    async mounted() {
        await this.getPddGroup2();
        await this.getAllShopList();
        await this.getPartitionList();
    },
    methods: {
        // 获取店铺
        async getAllShopList() {
            let shops = await getAllShopList();
            this.shopList = [];
            shops.data?.forEach(f => {
                if (f.shopCode && f.platform == 2)
                    this.shopList.push(f);
            });
        },
        // 获取分组
        async getPddGroup2() {
            let shops = await getPddGroup2({ isleavegroup: true });
            this.groupList = [];
            shops.data?.forEach(f => {
                this.groupList.push(f);
            });
        },
        // 获取分区列表
        async getPartitionList() {
            let partitions = await getAllPartitions({});
            if (partitions.success && partitions.data) {
                this.partitionList = partitions.data;
            }
        },

        async showchart(row) {
            if (this.Filter.timerange) {
                var d = new Date(this.Filter.Sdate[0])
                var startsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
                this.Filter.startSdate = startsdate;

                d = new Date(this.Filter.Sdate[1])
                var endsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
                this.Filter.startSdate = endsdate;
            }

            var params = { shopname: row.shopName, StartSdate: this.Filter.startSdate, EndSdate: this.Filter.endSdate }
            let that = this;

            const res = await getPddShopInquirsStatisticsMargeMap(params).then(res => {
                that.dialogMapVisible.visible = true;
                that.dialogMapVisible.data = res;
                that.dialogMapVisible.title = res.title;
                res.title = "";
            })
            this.dialogMapVisible.visible = true

        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getpagelist();
        },
        getParam() {
            if (this.Filter.Sdate) {
                this.Filter.startSdate = this.Filter.Sdate[0];
                this.Filter.endSdate = this.Filter.Sdate[1];
            }
            else {
                this.Filter.startSdate = null;
                this.Filter.endSdate = null;
            }
            this.Filter.EnmPddGroupType = 1;
            const para = { ...this.Filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para
            };
            return params;
        },
        async getpagelist() {
            let params = this.getParam();
            this.listLoading = true;
            const res = await getPddShopInquirsStatisticsMargeList(params);
            this.listLoading = false;

            this.total = res.total
            this.pddmergeinquirsstatisticslist = res.list;
            this.summaryarry = res.summary;
        },
        groupclick(row) {

            if (this.Filter.Sdate) {
                this.Filter.startSdate = this.Filter.Sdate[0];
                this.Filter.endSdate = this.Filter.Sdate[1];
            }
            else {
                this.Filter.startSdate = null;
                this.Filter.endSdate = null;
            }

            this.$showDialogform({
                path: `@/views/customerservice/pdd/pddmergeinquirsstatisticsshopNew.vue`,
                autoTitle: false,
                title: row.shopName,
                args: {
                    startSdate: null != this.Filter.Sdate ? this.Filter.Sdate[0] : null,
                    endSdate: null != this.Filter.Sdate ? this.Filter.Sdate[1] : null,
                    shopname: row.shopName
                },
                height: '600px',
                width: '85%',
                closeOnClickModal: true
            });
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport() {
            let params = this.getParam();
            this.listLoading = true
            const res = await exportPddShopInquirsStatisticsMargeList(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '合并店效率统计数据' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>
