<template>
    <my-container v-loading="pageLoading">
        <template #header>
           
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn="false"  
          :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='tableData' 
          :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:98%;margin: 0">
        </ces-table>
        <!--分页-->
        <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
        </template>
    </my-container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import MyContainer from '@/components/my-container';
import cesTable from '@/components/Table/table.vue';
import {formatLinkProCode} from '@/utils/tools'
import {goodsCostChgDetail} from "@/api/inventory/basicgoods"

const tableCols =[
    {istrue:true,prop:'buyNo',label:'采购单号',sortable:'custom', width:'120'},
    {istrue:true,prop:'indexNo',label:'Erp编号',sortable:'custom', width:'90'},
    {istrue:true,prop:'goodsCode',label:'商品编码', width:'110',sortable:'custom'},
    {istrue:true,prop:'checkDate',label:'变动时间', width:'200',sortable:'custom'},
    {istrue:true,prop:'oldCostPrice',label:'原成本', width:'150',sortable:'custom'},
    {istrue:true,prop:'newCostPrice',label:'新成本', width:'150',sortable:'custom'},
    {istrue:true,prop:'diffPrice',label:'差价单价', width:'150',sortable:'custom'},
    {istrue:true,prop:'count',label:'采购量', width:'150',sortable:'custom'},
    {istrue:true,prop:'diffTotal',label:'差价总额', width:'150',sortable:'custom'},
];

const tableHandles=[
       
      ];



export default ({
    name:"Users",
    components:{MyContainer,cesTable},
    data(){
        return {
            that:this, 
                              
            tableCols:tableCols,
            tableHandles:tableHandles,
            tableData:[],
            total: 0,
            pager:{OrderBy:"buyNo",IsAsc:false},
            listLoading: false,
            pageLoading: false,
            summaryarry:{},    
            sels:[],

        };
    },
    props:{
        filter:{
            id:null,
           
        },   
    },
    methods:{
        async sortchange(column){
            if(!column.order)
                this.pager={};
            else
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
            await this.onSearch();
            },
            async onSearch(){
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        async getList(){
            var that=this;
            this.listLoading=true;
            var pager = this.$refs.pager.getPager();
            const params = {...pager,...this.pager,...this.filter};
            const res = await goodsCostChgDetail(params).then(res=>{
                that.total = res.data?.total;
                that.tableData = res.data?.list;
                that.summaryarry=res.data?.summary;               
            });
            this.listLoading=false;
        },
       
    }
})
</script>
<style scoped>
    ::v-deep .el-table__fixed-footer-wrapper tbody td{
        color:blue;
    }
</style>

