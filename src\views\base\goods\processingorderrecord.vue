<template>
    <my-container v-loading="pageLoading2">
        <!-- <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter2" @submit.native.prevent label-position="right" label-width="90px">
                <el-form-item label="加工单号:">
                    <el-input type="number" v-model="filter2.receiptNo" placeholder="加工单号" style="width: 140px" />
                </el-form-item>
                <el-form-item label="标题:">
                    <el-input v-model="filter2.title" placeholder="标题" />
                </el-form-item>
                <el-form-item label="加工人:">
                    <el-input v-model="filter2.userName" placeholder="加工人" />
                </el-form-item>
                <el-form-item label="成品编码:">
                    <el-input v-model="filter2.goodsCode" placeholder="成品编码" />
                </el-form-item>
                <el-form-item label="成品名称:">
                    <el-input v-model="filter2.goodsName" placeholder="成品商品名称" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch2">查询</el-button>
                </el-form-item>
            </el-form>
        </template> -->
        <template>
            <ces-table ref="table2" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange2' :tableData='list2' :tableCols='tableCols2' :loading="listLoading2">
                <template slot='extentbtn'>
                    <el-button-group>
                        <el-button style="padding: 0;width: 100px;">
                            <el-input type="number" v-model="filter2.receiptNo" placeholder="加工单号" />
                        </el-button>
                        <el-button style="padding: 0;width: 120px;">
                            <el-input v-model="filter2.title" placeholder="标题" />
                        </el-button>
                        <el-button style="padding: 0;width: 80px;">
                            <el-input v-model="filter2.userName" placeholder="加工人" />
                        </el-button>
                        <el-button style="padding: 0;width: 120px;">
                            <el-input v-model="filter2.goodsCode" placeholder="成品编码" />
                        </el-button>
                        <el-button style="padding: 0;width: 120px;">
                            <el-input v-model="filter2.goodsName" placeholder="成品名称" />
                        </el-button>
                        <el-button style="padding: 0;width: 90px;">
                            <el-select v-model="filter2.warehouse" placeholder="仓库" :clearable="true">
                                <el-option v-for="item in warehouseList2" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-button>
                        <el-button style="padding: 0;width: 90px;">
                            <el-select v-model="filter2.groupId" placeholder="运营组" :clearable="true">
                                <el-option v-for="item in groupList2" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-button>
                        <el-button style="padding: 0;width: 230px;">
                            <el-date-picker style="width:230px" v-model="filter2.lqDate" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="领取开始日期" end-placeholder="领取结束日期" clearable :picker-options="pickerOptions2"></el-date-picker>
                        </el-button>
                        <el-button style="padding: 0;width: 230px;">
                            <el-date-picker style="width:230px" v-model="filter2.wcDate" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="完成开始日期" end-placeholder="完成结束日期" clearable :picker-options="pickerOptions2"></el-date-picker>
                        </el-button>
                        <el-button type="primary" @click="onSearch2">查询</el-button>
                        <el-button type="primary" @click="onExport2">导出</el-button>
                    </el-button-group>
                </template>
            </ces-table>
        </template>

        <template #footer>
            <my-pagination ref="pager2" :total="total2" @get-page="getlist2" />
        </template>
    </my-container>
</template>

<script>
    import { formatTime } from "@/utils";
    import MyContainer from '@/components/my-container';
    import MyConfirmButton from '@/components/my-confirm-button';
    import cesTable from "@/components/Table/table.vue";
    import { getDirectorGroupList } from '@/api/operatemanage/base/shop';
    import {
        getProcessReceiptFinishPageData, getProcessReceiptFinishExportData
    } from '@/api/inventory/processingorder';

    const tableCols2 = [
        { istrue: true, prop: 'receiptNo', label: '加工单号', width: '90', sortable: 'custom' },
        { istrue: true, prop: 'userName', label: '加工人', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'title', label: '标题' },
        { istrue: true, prop: 'goodsCode', label: '成品编码', width: '140', sortable: 'custom' },
        { istrue: true, prop: 'goodsName', label: '成品名称', sortable: 'custom' },
        { istrue: true, prop: 'goodsAmount', label: '成品数量', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'partiallyGoodsCode', label: '半成品编码及数量' },
        { istrue: true, prop: 'warehouseName', label: '仓库', width: '90' },
        { istrue: true, prop: 'platformName', label: '平台', width: '90' },
        { istrue: true, prop: 'groupName', label: '运营组', width: '90' },
        { istrue: true, prop: 'createdTime', label: '领取时间', sortable: 'custom', width: '150' },
        { istrue: true, prop: 'finishDate', label: '完成时间', sortable: 'custom', width: '150', formatter: (row) => row.finishDate == null ? null : formatTime(row.finishDate, 'YYYY-MM-DD HH:mm:ss') },
    ];

    export default {
        name: 'Roles',
        components: { cesTable, MyContainer, MyConfirmButton },
        props: {

        },
        data() {
            return {
                that: this,
                filter2: {
                    receiptNo: null,
                    beginDate: null,
                    endDate: null,
                    title: null,
                    goodsCode: "",
                    goodsName: "",
                    partiallyGoodsCode: "",
                    warehouse: null,
                    groupId: null,
                    userName: null,
                    lqsDate: null,
                    lqeDate: null,
                    lqDate: [],
                    wcsDate: null,
                    wceDate: null,
                    wcDate: []
                },
                pageLoading2: false,
                listLoading2: false,
                total2: 0,
                list2: [],
                pager2: { OrderBy: "ReceiptNo", IsAsc: false },
                tableCols2: tableCols2,
                groupList2: [],//运营组下拉
                warehouseList2: [
                    { label: '（本仓）', value: 1 },
                    { label: '南昌昌东', value: 3 }
                ],//仓库下拉
                pickerOptions2: {
                    disabledDate(time) {
                        return time.getTime() > Date.now();
                    }
                },
            }
        },
        async mounted() {
            await this.getGroupList();
            await this.getlist2();
        },
        methods: {
            //获取运营组下拉
            async getGroupList() {
                var res2 = await getDirectorGroupList();
                this.groupList2 = res2.data?.map(item => { return { value: item.key, label: item.value }; });
            },
            //获取查询条件
            getCondition2() {
                if (this.filter2.lqDate && this.filter2.lqDate.length > 1) {
                    this.filter2.lqsDate = this.filter2.lqDate[0];
                    this.filter2.lqeDate = this.filter2.lqDate[1];
                }
                if (this.filter2.wcDate && this.filter2.wcDate.length > 1) {
                    this.filter2.wcsDate = this.filter2.wcDate[0];
                    this.filter2.wceDate = this.filter2.wcDate[1];
                }
                var pager = this.$refs.pager2.getPager();
                var page = this.pager2;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter2
                }
                return params;
            },
            //查询第一页
            async onSearch2() {
                if (this.filter2.receiptNo < 0 || this.filter2.receiptNo > 999999999) {
                    this.$message({ message: '加工单号输入错误', type: 'error' });
                    return;
                }
                if (this.filter2.receiptNo && this.filter2.receiptNo.toString().indexOf(".") > -1) {
                    this.$message({ message: '加工单号输入错误', type: 'error' });
                    return;
                }
                this.$refs.pager2.setPage(1)
                await this.getlist2()
            },
            //分页查询
            async getlist2() {
                this.filter2.lqsDate = null;
                this.filter2.lqeDate = null;
                this.filter2.wcsDate = null;
                this.filter2.wceDate = null;
                var params = this.getCondition2();
                if (params === false) {
                    return;
                }
                this.listLoading2 = true
                var res = await getProcessReceiptFinishPageData(params);
                this.listLoading2 = false
                if (!res?.success) {
                    return
                }
                this.total2 = res.data.total;
                const data = res.data.list;
                data.forEach(d => {
                    d._loading = false
                })
                this.list2 = data
            },
            //排序查询
            async sortchange2(column) {
                if (!column.order)
                    this.pager2 = {};
                else {
                    var orderBy = column.prop;
                    this.pager2 = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch2();
            },
            async onExport2() {
                this.filter2.lqsDate = null;
                this.filter2.lqeDate = null;
                this.filter2.wcsDate = null;
                this.filter2.wceDate = null;
                var params = this.getCondition2();
                if (params === false) {
                    return;
                }
                var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
                var res = await getProcessReceiptFinishExportData(params);
                loadingInstance.close();
                if (!res?.data) return
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '导出加工日志_' + new Date().toLocaleString() + '.xlsx')
                aLink.click();
            },
        }
    }
</script>

<style scoped>
</style>