<template>
  <div>
    <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm"
      v-loading="loadingList">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="外仓仓库" prop="wmsId">
            <chooseWareHouse v-model="ruleForm.wmsId" class="item" :filter="sendWmsesFilter" @chooseWms="chooseWms"
              :disabled="!isAdd" />
          </el-form-item>
        </el-col>
         <el-col :span="12">
          <el-form-item label="快递费" prop="expressFee">
            <el-input-number v-model="ruleForm.expressFee" :precision="4" :min="0" :max="99" placeholder="快递费" style="width:100%" :controls="false"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="收货地址" prop="address">
            <el-input v-model.trim="ruleForm.address" clearable placeholder="收货地址" maxlength="100"
              class="item"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="发件量" prop="sendCount">
            <el-input-number :controls="false" :min="0" :max="99999999" :precision="0" placeholder="发件量" class="item"
              v-model="ruleForm.sendCount" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发件比例" prop="rate">
            <div style="display: flex; align-items: center; width: 100%;">
              <el-input-number v-model="ruleForm.rate" :controls="false" :min="0" :max="100" :precision="2"
                placeholder="发件比例" style="flex: 1;" class="item" />
              <span style="margin-left: 4px;">%</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最小克重" prop="weightMin">
            <el-input-number :controls="false" :min="0" :max="99999999" :precision="0" placeholder="最小克重" class="item"
              v-model="ruleForm.weightMin" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大克重" prop="weightMax">
            <el-input-number :controls="false" :min="0" :max="99999999" :precision="0" placeholder="最大克重" class="item"
              v-model="ruleForm.weightMax" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="包材关键字" prop="packMtlKey">
            <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 4 }" placeholder="请输入包材关键字"
              v-model="ruleForm.packMtlKey" maxlength="200" show-word-limit>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <div style="display: flex;justify-content: center;">
            <el-button @click="$emit('close')">取消</el-button>
            <el-button type="primary" @click="submitForm('ruleForm')" v-throttle="1000">保存</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import inputYunhan from "@/components/Comm/inputYunhan";
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import request from '@/utils/request'
const api = '/api/verifyOrder/SaleItems/SgwWmsSetting/'
import decimal from '@/utils/decimal'
export default {
  props: {
    isAdd: {
      type: Boolean,
      default: false
    },
    editForm: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    inputYunhan, chooseWareHouse
  },
  data() {
    return {
      api,
      platformlist,
      ruleForm: {
        rate: 93,
        wmsId: '',
        address: '',
        sendCount: undefined,
        weightMin: undefined,
        weightMax: undefined,
        packMtlKey: '',
        wmsName: '',
        expressFee: undefined,
      },
      loadingList: false,
      rules: {
        sendCount: [
          { required: true, message: '请输入发件量', trigger: 'blur' },
        ],
        rate: [
          { required: true, message: '请输入发件比例', trigger: 'blur' },
        ],
        expressFee: [
          { required: true, message: '请输入快递费', trigger: 'blur' },
        ],
      }
    }
  },
  created() {
    if (this.editForm.wmsId && !this.isAdd) {
      this.getProp()
    }
  },
  mounted() {
  },
  methods: {
    chooseWms(wms) {
      this.ruleForm.wmsName = wms.name
    },
    sendWmsesFilter(wmses) {
      return wmses.filter((a) => a.isSendWarehouse == '是' && a.isWc == 1);
    },
    async getProp() {
      this.loadingList = true;
      let { data, success } = await request.post(`${this.api}GetData?wmsId=${this.editForm.wmsId}`, {})
      this.loadingList = false;
      if (!success) return
      let a = data?.packMtlKey
      this.ruleForm = { ...data, packMtlKey: a?.join('\n') || '', rate: decimal(data.rate !== null && data.rate !== '' ? data.rate : 0.93, 100, 4, '*') };
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          let a = this.ruleForm.packMtlKey;
          this.loadingList = true;
          const { success } = await request.post(`${this.api}SaveData`, {
            ...this.ruleForm, packMtlKey: a
              ? a
                .split(/[\n,，;；/／]+/) // 支持换行、英文逗号、中文逗号、英文分号、中文分号、英文斜杠、中文斜杠
                .map(s => s.trim())
                .filter(Boolean) // 去除空字符串
              : [], rate: decimal(this.ruleForm.rate, 100, 4, '/')
          })
          this.loadingList = false;
          if (!success) return
          this.$message({
            type: 'success',
            message: '添加成功!'
          });
          setTimeout(() => {
            this.$emit('getList')
            this.$emit('close')
          }, 500)
        } else {
          return false;
        }
      });
    },
  }
}
</script>

<style scoped lang="scss">
.item {
  width: 100%;
}
</style>
