<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <span style="display: flex; align-items: center;"></span>
                <!-- <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 5px;" :clearable="false" :value-format="'yyyy-MM-dd'"
                    @change="changeTime">
                </el-date-picker> -->
                <el-date-picker v-model="timeRanges" type="month" placeholder="选择月">
                </el-date-picker>
                <!-- <el-select v-model="ListInfo.documentType" placeholder="账单类型" clearable class="publicCss">
                        <el-option :key="'客单发货'" label="客单发货" :value="'客单发货'" />
                        <el-option :key="'平台客单发货'" label="平台客单发货" :value="'平台客单发货'" />
                        <el-option :key="'线下销售'" label="线下销售" :value="'线下销售'" />
                    </el-select> -->

                <el-select style="width: 180px;" v-model="ListInfo.replenishmentDeductionCategorys" placeholder="补扣款分类"
                    class="publicCss" clearable multiple filterable collapse-tags>
                    <el-option v-for="item in ductionCategoryList" :key="item" :label="item" :value="item" />
                </el-select>

                <el-button style="padding: 0;border: none;float: left;">
                    <inputYunhan :key="'1'" :keys="'one'" :width="'220px'" ref="" :inputt.sync="ListInfo.shopName"
                        v-model.trim="ListInfo.shopName" placeholder="店铺/若输入多条请按回车" :clearable="true"
                        @callback="callbackShopName" title="店铺" @entersearch="entersearch" :maxRows="100">
                    </inputYunhan>
                </el-button>

                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button
                    @click="startImport" type="primary" icon="el-icon-share" @command="handleCommand"> 导入
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item class="Batcoperation" style="padding: 0 25px"
                            command="a">下载模版</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
                <el-button type="primary" @click="exportProps">导出</el-button>
            </div>
        </template>
        <vxetablebase :id="'transactionIncome202409090426'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
            style="width: 100%;margin: 0" v-loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
            <div style="height: 75px;">
                <!-- <el-date-picker style="width: 200px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay"
                    type="date" placeholder="选择日期" :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
                </el-date-picker> -->

                <el-date-picker v-model="yearMonthDay" type="month" placeholder="选择月" format="yyyyMM"
                    value-format="yyyyMM">
                </el-date-picker>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                    :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                </el-upload>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getList } from '@/api/operatemanage/base/shop'
import dayjs from 'dayjs'
import { importSettleDetail_SheInAsync, getSheIn_FullTrustBillSettlementPageList, getReplenishmentDeductionCategoryList, importSheIn_FullTrustBillSettlementAsync, exportSheIn_FullTrustBillSettlement } from '@/api/bookkeeper/crossBorderV2'
import inputYunhan from "@/components/Comm/inputYunhan";

const tableCols = [
    { istrue: true, prop: 'shopCode', label: '店铺ID', width: 'auto', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'shopName', label: '店铺', width: 'auto', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'yearMonthDayDateTime', label: '日期', width: 'auto', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'paymentType', label: '扣款类型', width: 'auto', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'replenishmentDeductionCategory', label: '补扣款分类', width: 'auto', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'associatedDocument', label: '关联单据', width: 'auto', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'unitPrice', label: '单价', width: 'auto', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'quantity', label: '数量', width: 'auto', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'totalAmount', label: '总金额', width: 'auto', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'documentStatus', label: '单据状态', width: 'auto', align: 'center', },
    { istrue: true, prop: 'associatedReportBill', label: '关联报账单', width: 'auto', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'accountingDate', label: '会计日期', width: 'auto', align: 'center', },
]
export default {
    name: "transactionIncome",
    components: {
        MyContainer, vxetablebase, inputYunhan
    },
    data() {
        return {
            summaryarry: {},
            yearMonthDay: null,//导入日期
            fileparm: {},//上传文件参数
            dialogVisible: false,//导入弹窗
            uploadLoading: false,//上传loading
            fileList: [],//上传文件列表
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                timeStart: null,//开始时间
                timeEnd: null,//结束时间
                inventoryNumber: null,//备货单号
                proCode: null,//产品ID
                shopName: null,//店铺
                billingType: null,//账单类型
                replenishmentDeductionCategorys: [],
            },
            timeRanges: null,
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            billingType: null,//账单类型
            ductionCategoryList: ""
        }
    },
    async mounted() {
        await this.getList()
        await this.getSelectOptionList()

    },
    methods: {
        async getSelectOptionList() {
            //     if (this.Filter.timeRanges) {
            //   para.timeStart = this.Filter.timerange[0];
            //   para.timeEnd = this.Filter.timerange[1];
            // }
            const param = {
                // timeStart:para.timeStart,
                // timeEnd:para.timeEnd
            }

            const ductionCategory = await getReplenishmentDeductionCategoryList(param);
            this.ductionCategoryList = ductionCategory.data;
        },
        //上传文件
        onUploadRemove(file, fileList) {
            this.fileList = []
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            this.dialogVisible = false;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("yearMonthDay", this.yearMonthDay);
            var res = await importSheIn_FullTrustBillSettlementAsync(form);
            if (res?.success)
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
            this.uploadLoading = false
            this.dialogVisible = false;
            await this.getList()
        },
        onSubmitUpload() {
            if (!this.yearMonthDay) {
                this.$message({ message: "请选择日期", type: "warning" });
                return false;
            }
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        //导入弹窗
        startImport() {
            this.fileList = []
            this.dialogVisible = true;
        },
        async changeTime(e) {
            this.ListInfo.timeStart = e ? e[0] : null
            this.ListInfo.timeEnd = e ? e[1] : null
        },
        formatMonth(date) {
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份从 0 开始，需要加 1
            return `${year}-${month}-01`;
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            if (this.timeRanges != null) {
                //默认给近7天时间
                this.ListInfo.timeStart = this.formatMonth(this.timeRanges)
                this.ListInfo.timeEnd = this.formatMonth(this.timeRanges)
            } else {
                this.ListInfo.timeStart = null;
                this.ListInfo.timeEnd = null;
            }
            this.ListInfo.replenishmentDeductionCategory = this.ListInfo.replenishmentDeductionCategorys.join(",")
            this.loading = true
            const { data, success } = await getSheIn_FullTrustBillSettlementPageList(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.summaryarry = data.summary
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        async exportProps() {

            if (this.timeRanges != null) {
                //默认给近7天时间
                this.ListInfo.timeStart = this.formatMonth(this.timeRanges)
                this.ListInfo.timeEnd = this.formatMonth(this.timeRanges)
            } else {
                this.ListInfo.timeStart = null;
                this.ListInfo.timeEnd = null;
            }
            this.ListInfo.replenishmentDeductionCategory = this.ListInfo.replenishmentDeductionCategorys.join(",")
            const res = await exportSheIn_FullTrustBillSettlement(this.ListInfo);
            const aLink = document.createElement("a");

            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            var excelName = '希音-全托钱包结算'
            aLink.setAttribute(
                "download",
                excelName + new Date().toLocaleString() + ".xlsx"
            );
            aLink.click();
        },
        async handleCommand(command) {
            switch (command) {
                //下载模版
                case 'a':
                    await this.downLoadFile()
                    break;
            }
        },
        async downLoadFile() {
            window.open("/static/excel/CrossBorderDownloadTemplate/希音-全托账单费用结算.xlsx", "_blank");
        },
        //多条查询部分
        async entersearch(val) {
            this.getList();
        },
        async callbackShopName(val) {
            this.ListInfo.shopName = val;
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 150px;
        margin-right: 5px;
    }
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
    max-width: 45px;
}
</style>