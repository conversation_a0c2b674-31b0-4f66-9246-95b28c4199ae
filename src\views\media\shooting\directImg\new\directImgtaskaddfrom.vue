<template>
    <!-- 创建任务 -->
    <my-container v-loading="pageLoading">
        <el-form :model="addForm" :rules="calcAddFormRules" :disabled="islook" ref="addForm" label-width="100px"
            class="demo-addForm">
            <div class="bzcjrw">
                <div class="bt">
                    <span style="float: left">创建任务</span>
                </div>
                <!-- 自定义类型处 star -->
                <div class="bzccjlx">
                    <div class="lxwz">产品简称</div>
                    <div style="display: inline-block">
                        <el-form-item label=" " label-width="12px" prop="productShortName">
                            <el-input size="mini" style="width: 120%" :maxlength=100 v-model="addForm.productShortName"
                                placeholder="请填写产品简称"></el-input>
                        </el-form-item>
                    </div>
                </div>
                <div class="bzccjlx">
                    <div class="lxwz">平台</div>
                    <div style="display: inline-block">
                        <el-form-item label=" " label-width="12px" prop="platform">
                            <el-select size="mini" style="width: 60%" v-model="addForm.platform" placeholder="请选择平台"
                                :clearable="true" :collapse-tags="true" filterable @change="onchangeplatform">
                                <el-option v-for="item in platformList" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>
                <div class="bzccjlx">
                    <div class="lxwz">店铺</div>
                    <div style="display: inline-block">
                        <el-form-item label=" " label-width="12px" prop="shopName">
                            <el-select size="mini" style="width: 100%" v-model="addForm.shopName" placeholder="请选择店铺"
                                :clearable="true" :collapse-tags="true" @change="selShopInfoChange(addForm.shopName, index)"
                                filterable>
                                <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                                    :value="item.shopCode" />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>
                <div class="bzccjlx">
                    <div class="lxwz">运营小组</div>
                    <div style="display: inline-block">
                        <el-form-item label=" " label-width="12px" prop="operationGroup">
                            <el-select size="mini" style="width: 60%" v-model="addForm.operationGroup" placeholder="请选择小组"
                                @change="selOpergroupInfoChange(addForm.operationGroup, index)" filterable>
                                <el-option v-for="item in groupList" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>
                <div class="bzccjlx">
                    <div class="lxwz">对接人</div>
                    <div style="display: inline-block">
                        <el-form-item label=" " label-width="12px" prop="dockingPeople">
                            <el-input size="mini" style="width: 50%" v-model="addForm.dockingPeople"
                                :clearable="true"></el-input>
                        </el-form-item>
                    </div>
                </div>
                <div class="bzccjlx">
                    <div class="lxwz">拍摄样品</div>
                    <div style="display: inline-block">
                        <el-form-item label=" " label-width="12px" label-position="left" prop="warehouse">
                            <el-select size="mini" style="width: 80%" v-model="addForm.warehouse" placeholder="请选择仓库"
                                @change="selwarehouseChange()">
                                <el-option v-for="item in cwarehouselist" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>
                <div
                    style="display: flex; flex-direction: row; overflow-wrap: break-word; align-items: center; flex-wrap: wrap; ">
                    <div style="margin-left: 60px;">拍摄任务</div>
                    <div style="margin-left: -33px; ">
                        <el-form-item label="" prop="shootingTaskPickList">
                            <el-checkbox-group v-model="addForm.shootingTaskPickList">
                                <el-checkbox label="1" border>照片拍摄</el-checkbox>
                                <el-checkbox style="margin-left: -8px;" label="4" border>车图修改</el-checkbox>
                                <el-checkbox style="margin-left: -8px;" label="5" border>照片建模</el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                    </div>
                </div>


                <!-- 自定义类型处 end -->
                <div class="bzccjlx" style="margin-top: 15px;height: 150px;">
                    <div class="lxwz">附件</div>
                    <div style="display: inline-block;width: 160px; vertical-align: top; margin-left: 12px;">
                        <uploadfile :minisize="false" ref="uploadexl" :islook="islook" :buttontext="'上传附件'" :isdown="true"
                            :uploadInfo="addForm.execlUpfiles" :limit="10000" :accepttyes="'.xlsx'" />
                    </div>
                </div>
                <div class="bzccjlx" style="margin: auto auto 20px auto">
                    <el-input type="textarea" :rows="2" :maxlength="800" show-word-limit placeholder="请输入内容"
                        v-model="addForm.taskRemark">
                    </el-input>
                </div>
                <div class="qxtj">
                    <span style="float: left"></span>
                    <span style="float: right">
                        <el-form-item>
                            <el-button size="mini" @click="onCloseAddForm(1)">取消</el-button>
                            <el-button size="mini" type="primary" @click="submitForm('addForm')">创建任务</el-button>
                        </el-form-item>
                    </span>
                </div>
            </div>
        </el-form>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import uploadfile from '@/views/media/shooting/uploadfile';
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import { addOrUpdateShootingVideoTaskAsync } from '@/api/media/directImgtask';
export default {
    props: {
        taskUrgencyList: { type: Array, default: () => { return [] } },
        userList: { type: Array, default: () => { return [] } },
        groupList: { type: Array, default: () => { return [] } },
        platformList: { type: Array, default: () => { return [] } },
        warehouselist: { type: Array, default: () => { return [] } },
        packclasslist: { type: Array, default: () => { return [] } },
        onCloseAddForm: { type: Function, default: null },
        islook: { type: Boolean, default: false },
        iscodeCreate: { type: Boolean, default: false },

    },
    components: {   uploadfile, MyContainer },
    data() {
        return {
            that: this,
            inputshow: true,
            pageLoading: false,
            productVisible: false,//选择产品窗口
            shopList: [],
            cwarehouselist: [],
            addForm: {
                directImgTaskId: 0,
                productShortName: null,
                productCode: null,
                productId: null,
                platform: null,
                shopName: null,
                operationGroup: null,
                dockingPeople: null,
                taskUrgency: 9,
                warehouse: null,
                yyOrderNum: null,
                yyExpressNum: null,
                photoUpfiles: [],
                execlUpfiles: [],
                taskRemark: null,
                loginfo: null,
                detailLqBtnStr: null,
                detailLqNameStr: null,
                detailOverTimeStr: null,
                detailIsOver: 0,
                chiCunStr: null,
                detailConfirmBtnStr: null,
                detailConfirmNameStr: null,
                detailConfirmTimeStr: null,
                detailConfirmIsOver: 0,
                brand: null,
                izcjdz: null,
                IscodeCreate: 0,
                shootingTaskPickList: []
            },
        };
    },
    computed: {
        calcAddFormRules()
        {
            return {
                productShortName: [{ required: true, message: '请填写', trigger: 'blur' }],
                shopName: [{ required: true, message: '请选择', trigger: 'blur' }],
                operationGroup: [{ required: true, message: '请选择', trigger: 'blur' }],
                dockingPeople: [{ required: true, message: '请填写', trigger: 'blur' }],
                shootingTaskPickList: [{ required: true, message: '请选择', trigger: 'blur' }],
                platform: [{ required: true, message: '请选择', trigger: 'blur' }],
                taskUrgency: [{ required: true, message: '请选择', trigger: 'blur' }],
                productCode: [{ required: true, message: '请选择', trigger: 'blur' }],
                fpDetailLqName: [{ required: true, message: '请选择', trigger: 'blur' }],
                packClass: [{ required: true, message: '请选择', trigger: 'blur' }],
                brand: [{ required: true, message: '请选择', trigger: 'blur' }],
                izcjdz: [{ required: true, message: '请选择', trigger: 'blur' }],
            }
        }
    },
    async mounted() {
        for (let num in this.warehouselist) {
            this.cwarehouselist.push(this.warehouselist[num]);
        }
        if (this.addForm.dockingPeople == null)
            this.addForm.dockingPeople = this.$store.getters.userName?.split("-")[0].trim();
    },
    methods: {

        //下拉改变值
        async onchangeplatform(val) {
            var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 10000 });
            this.addForm.shopName = "";
            this.shopList = res1.data.list;
        },
        selwarehouseChange() {
            for (let num in this.warehouselist) {
                if (this.warehouselist[num].value == this.addForm.warehouse) {
                    this.addForm.warehouseStr = this.warehouselist[num].label;
                }
            }
        },
        selShopInfoChange(val) {
            let resultArr = this.shopList.find((item) => {
                return item.shopCode == val;
            });
            this.addForm.shopNameStr = resultArr.shopName;
        },
        selOpergroupInfoChange(val) {
            var resultArr = this.groupList.find((item) => {
                return item.value == val;
            });
            this.addForm.operationGroupstr = resultArr.label;
        },
        onSelctProduct() {
            this.productVisible = true;
        },

        //下拉改变值结束
        async submitForm(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    var res = this.$refs.uploadexl.getReturns();
                    if (!res.success) return;
                    this.addForm.execlUpfiles = res.data;
                    let para = _.cloneDeep(this.addForm);
                    this.pageLoading = true;
                    var res = await addOrUpdateShootingVideoTaskAsync(para);
                    this.pageLoading = false;
                    if (!res?.success) { return; }
                    this.$message({ message: this.$t('保存成功'), type: 'success' });
                    this.onCloseAddForm(2);
                } else {
                    return false;
                }
            });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
    },
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item {
    margin: 0px !important;
}

::v-deep .el-form-item__error {
    position: absolute !important;
    top: 30% !important;
    left: 400px !important;
    width: 60px !important;
}

::v-deep .bzcjrw {
    width: 100%;
    background-color: #fff;
}

::v-deep .bzcjrw .bt {
    height: 40px;
    /* background-color: aquamarine; */
    font-size: 18px;
    color: #666;
    margin-bottom: 20px;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
    box-sizing: border-box;
    padding: 0 35px;
}

::v-deep .bzcjrw .bt i {
    color: #999;
}

::v-deep .bzcjrw .bt i {
    margin-left: 8px;
    line-height: 26px;
}

::v-deep .bzcjrw .bt i:hover {
    margin-left: 8px;
    line-height: 26px;
    color: #409eff;
    position: relative;
    top: -2px;
}

::v-deep .bzcjrw .bzccjlx {
    width: 100%;
    height: 35px;
    /* background-color: bisque; */
    box-sizing: border-box;
    padding: 0 60px;
}

::v-deep .bzcjrw .bzbjlxf {
    width: 100%;
    /* background-color: bisque; */
    box-sizing: border-box;
    padding: 0 60px;
}

::v-deep .bzcjrw .bzccjlx .lxwz {
    width: 112px;
    font-size: 14px;
    color: #666;
    vertical-align: top;
    line-height: 26px;
    /* background-color: rgb(204, 204, 255); */
    display: inline-block;
}

::v-deep .bzcjrw .scpd {
    width: 100px;
    font-size: 14px;
    color: #666;
    line-height: 36px;
    /* background-color: rgb(204, 204, 255); */
    float: left;
}

.bzcjrw .zjck {
    display: inline-block;
    float: right;
    position: relative;
    top: 5px;
}

.bzcjrw .qxtj {
    height: 100px;
    /* background-color: aquamarine; */
    box-sizing: border-box;
    padding: 25px 60px;
}
</style>
