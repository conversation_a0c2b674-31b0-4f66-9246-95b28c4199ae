<template>
 <div style="height:100%;padding:10px;overflow: auto;">
    <div id="echartturnoverranganalysis11" style="width: 100%;height: 389px; box-sizing:border-box; line-height: 360px;"/>
    <div id="echartturnoverranganalysis21" style="width: 100%;height: 389px; box-sizing:border-box; line-height: 360px;"/>
  </div>
</template>
<script>
import * as echarts from 'echarts';
import container from '@/components/my-container/nofooter'
import cesTable from "@/components/Table/table.vue";
import {getProCodeUnsalableAnalysisByDate,getProCodeUnsalableAnalysisByGroup} from '@/api/inventory/unsalable'
export default {
  name: 'Roles',
  components: {container,cesTable},
   props:{
       filter: { }
     },
  data() {
    return {
      that:this,
      period:0,
      pageLoading: false,
      listLoading:false
    }
  },
  mounted() {
  },
  beforeUpdate() {
  },
methods: {
   async onSearch(period) {
      this.period=period
      if (!this.filter.date1) {this.$message({message: "请选择日期1",type: "warning",});return;}
      if (!this.filter.date2) {this.$message({message: "请选择日期2",type: "warning",});return;}   
     await this.getanalysisdata()
    },
   async onfresh() {
     await this.onSearch()
    },
   async getanalysisdata() {
      var parm={...this.filter};
      parm.period=this.period;
      const res = await getProCodeUnsalableAnalysisByDate(parm);
      if (!res?.code)  return;       
      var chartDom = document.getElementById('echartturnoverranganalysis11');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      if (!res.data) {
         this.$message({message: "没有数据!",type: "warning",});
         return;
       }
      var option = this.Getoptions(res.data);
      option && myChart.setOption(option);
      let that=this;
      myChart.on('click', function (params) {
        that.getgroupanalysisdata(params.name);
      });
    },
   async getgroupanalysisdata(date) {
       var parm={...this.filter};
       parm.dateStr=date;
       parm.period=this.period;
      const res = await getProCodeUnsalableAnalysisByGroup(parm);
      if (!res?.code)  return;       
      var chartDom = document.getElementById('echartturnoverranganalysis21');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      if (!res.data) {
         this.$message({message: "没有数据!",type: "warning",});
         return;
      }
      var option = this.Getoptions(res.data);
      option.title={text:`${date}运营组库存情况`}
      option && myChart.setOption(option);
    },
    Getoptions(element){
     var series=[]
     element.series.forEach(s=>{
       series.push({smooth: true, ...s})
     })
     var yAxis=[]
     element.yAxis.forEach(s=>{
       yAxis.push({type: 'value',offset:s.offset,position:s.position,name: s.name,axisLabel: {formatter: '{value}'+s.unit}})
     })
     var option = {
        title: {text: element.title},
        tooltip: {trigger: 'axis'},
        legend: {
           selected:{'总编码数':false,'15天以内编码数':false,'15-30天编码数':false,'1-2个月编码数':false,'2-3个月编码数':false,
                     '3-6个月编码数':false,'6-12个月编码数':false,'12个月以上编码数':false,'2个月以上编码数':false},
           data: element.legend
         },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {feature: {
            magicType: {show: true, type: ['line', 'bar']},
            //restore: {show: true},
        }},
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis: yAxis,
        series:  series
    };
    return option;
   },
  }
}
</script>
<style>
.el-select-content { 
    width: calc(100% - 10px);
    margin: 0;
 }
 
</style>
