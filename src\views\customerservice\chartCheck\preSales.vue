<template>
  <my-container>
    <!--顶部操作-->
    <template #header>
    <div class=".top">
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
        <el-form-item label="">
          <el-date-picker style="width: 320px" v-model="Filter.conversationTime" type="datetimerange"
            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="会话开始日期"
            end-placeholder="会话结束日期" :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>
        <el-form-item label="" >
          <el-input v-model.trim="Filter.chatRecordKeywords" placeholder="聊天记录关键词" style="width:160px" maxlength="50" clearable />
        </el-form-item>
        <el-form-item label=""  v-show="selectedTitles.includes('创建时间')">
          <el-date-picker style="width: 320px" v-model="Filter.createdTime" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="创建时间开始" end-placeholder="创建时间结束"
            :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>
        <el-form-item label=""  v-show="selectedTitles.includes('下单日期')">
          <el-date-picker style="width: 320px" v-model="Filter.timerange" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="下单开始日期" end-placeholder="下单结束日期"
            :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>
<!--        <el-form-item label=""  v-show="selectedTitles.includes('审核日期')">
          <el-date-picker style="width: 320px" v-model="Filter.auditTime" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="审核开始日期" end-placeholder="审核结束日期"
            :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>-->
        <el-form-item label=""  v-show="selectedTitles.includes('审核状态')">
          <el-select v-model="Filter.auditState" placeholder="审核状态"
            style="width:100px" class="el-select-content" clearable>
            <el-option v-for="item in reviewStatusList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label=""  v-show="selectedTitles.includes('订单状态')">
          <el-select v-model="Filter.orderState" placeholder="订单状态" style="width:100px" class="el-select-content"
            clearable>
            <el-option v-for="item in orderStatusList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label=""  v-show="selectedTitles.includes('平台')">
          <el-select v-model="Filter.platform" placeholder="平台" style="width:120px" class="el-select-content" clearable
            @change="changePlatform">
            <el-option v-for="item in platformList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item label=""  v-show="selectedTitles.includes('聊天账号')">
          <el-input v-model.trim="Filter.chatAccount" placeholder="聊天账号" style="width:120px" maxlength="20" clearable />
        </el-form-item>

        <el-form-item label=""  v-show="selectedTitles.includes('线上订单号')">
            <el-button-group>
              <inputYunhan :key="'1'" :keys="'one'" :width="'220px'" ref="" :inputt.sync="Filter.orderNoList"
                v-model.trim="Filter.orderNoList" placeholder="线上订单号/若输入多条请摁回车" :clearable="true" @callback="callback" title="线上订单号"
                @entersearch="entersearch" :maxRows="300">
              </inputYunhan>
            </el-button-group>
       </el-form-item>

        <el-form-item label=""  v-show="selectedTitles.includes('宝贝ID')">
          <el-input maxlength="20" v-model.trim="Filter.proId" placeholder="宝贝ID" style="width:120px" clearable />
        </el-form-item>
        <el-form-item label=""  v-show="selectedTitles.includes('审核人')">
          <el-input maxlength="50" placeholder="审核人" v-model.trim="Filter.initialOperator" style="width:120px"
            clearable />
        </el-form-item>
        <el-form-item label="" v-show="selectedTitles.includes('店铺')">
          <el-select v-model="Filter.shopNameList" placeholder="店铺" class="el-select-content" filterable multiple
            clearable collapse-tags @focus="changPlatformState">
            <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.shopName" />
          </el-select>
        </el-form-item>
        <el-form-item label=""  v-show="selectedTitles.includes('账号使用人')">
          <el-select v-model="Filter.userName" placeholder="账号使用人" class="el-select-content" clearable filterable
            @focus="changPlatformState">
            <el-option v-for="item in userNameList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="" v-show="selectedTitles.includes('分组')">
          <el-select v-model="Filter.groupNameList" placeholder="分组" class="el-select-content" filterable multiple
            clearable collapse-tags @focus="changPlatformState">
            <el-option v-for="item in groupNameList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label=""  v-show="selectedTitles.includes('组长')">
          <el-select v-model="Filter.groupManager" placeholder="组长" style="width:120px" class="el-select-content"
            clearable filterable @focus="changPlatformState">
            <el-option v-for="item in groupManagerList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label=""  v-show="selectedTitles.includes('审核结果')">
          <el-select v-model="Filter.initialAuditType" placeholder="审核结果" style="width:120px" class="el-select-content"
            clearable>
            <el-option v-for="item in firstTrialList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label=""  v-show="selectedTitles.includes('审核类型')">
          <el-select v-model="Filter.refuseInitialAuditType" placeholder="审核类型" style="width:120px"
            class="el-select-content" clearable>
            <el-option v-for="item in statusList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
<!--        <el-form-item label=""  v-show="selectedTitles.includes('复审结果')">
          <el-select v-model="Filter.finalAuditType" placeholder="复审结果" style="width:120px" class="el-select-content"
            clearable>
            <el-option v-for="item in firstTrialList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label=""  v-show="selectedTitles.includes('复审类型')">
          <el-select v-model="Filter.refuseFinalAuditType" placeholder="复审类型" style="width:120px"
            class="el-select-content" clearable>
            <el-option v-for="item in statusList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>-->
<!--        <el-form-item label=""  v-show="selectedTitles.includes('评判结果')">
          <el-select v-model="Filter.judgmentAuditType" placeholder="评判结果" style="width:120px" class="el-select-content"
            clearable>
            <el-option v-for="item in firstTrialList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label=""  v-show="selectedTitles.includes('评判类型')">
          <el-select v-model="Filter.refuseJudgmentAuditType" placeholder="评判类型" style="width:120px"
            class="el-select-content" clearable>
            <el-option v-for="item in statusList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>-->

        <el-form-item label=""  v-show="selectedTitles.includes('负责运营')">
          <el-input v-model.trim="Filter.directorUserName" placeholder="负责运营" style="width:120px" maxlength="20"
            clearable />
        </el-form-item>

        <el-form-item label=""  v-show="selectedTitles.includes('运营小组')">
          <el-select v-model="Filter.directorGroupIdList" placeholder="运营小组" class="el-select-content" filterable
            multiple clearable collapse-tags>
            <el-option v-for="item in directoList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item>
        <el-form-item label=""  v-show="selectedTitles.includes('责任客服')">
          <el-input v-model.trim="Filter.responsibleName" placeholder="责任客服" style="width:120px" maxlength="20"
            clearable />
        </el-form-item>
<!--        <el-form-item label=""  v-show="selectedTitles.includes('复审人')">
          <el-input v-model.trim="Filter.finalOperator" placeholder="复审人" style="width:120px" maxlength="20"
            clearable />
        </el-form-item>
        <el-form-item label=""  v-show="selectedTitles.includes('评判人')">
          <el-input v-model.trim="Filter.judgmentOperator" placeholder="评判人" style="width:120px" maxlength="20"
            clearable />
        </el-form-item>-->
<!--        <el-form-item label=""  v-show="selectedTitles.includes('审核日期')">
          <el-date-picker style="width: 320px" v-model="Filter.auditTime" type="datetimerange" format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd" range-separator="至" start-placeholder="审核开始日期" end-placeholder="审核结束日期"
                          :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>-->
        <el-form-item label=""  v-show="selectedTitles.includes('审核日期')">
          <el-date-picker style="width: 320px" v-model="Filter.initialOperatorTime" type="datetimerange"
            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="审核开始日期"
            end-placeholder="审核结束日期" :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>
<!--        <el-form-item label=""  v-show="selectedTitles.includes('复审日期')">
          <el-date-picker style="width: 320px" v-model="Filter.finalOperatorTime" type="datetimerange"
            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="复审开始日期"
            end-placeholder="复审结束日期" :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>-->
<!--        <el-form-item label="" v-show="selectedTitles.includes('评判日期')">
          <el-date-picker style="width: 320px" v-model="Filter.judgmentOperatorTime" type="datetimerange"
            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="评判开始日期"
            end-placeholder="评判结束日期" :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>-->

        <el-form-item label=""  v-show="selectedTitles.includes('数据编码')">
          <el-input v-model.trim="Filter.conversationId" placeholder="数据编码" style="width:120px" maxlength="20"
            clearable />
        </el-form-item>

        <el-form-item label=""  v-show="selectedTitles.includes('系列编码')">
          <el-input v-model.trim="Filter.styleCode" placeholder="系列编码" style="width:120px" maxlength="20"
            clearable />
        </el-form-item>

        <el-form-item label="聊天条数" v-show="selectedTitles.includes('聊天条数')">
          <el-col :span="11">
            <el-input
              placeholder="最小条数"
              v-model="Filter.chatCountStart"
              maxlength="4"
              clearable
              @input="validateNumber($event, 'chatCountStart')">
            </el-input>
          </el-col>
          <el-col class="line" :span="2">至</el-col>
          <el-col :span="11">
            <el-input
              placeholder="最大条数"
              v-model="Filter.chatCountEnd"
              maxlength="4"
              clearable
              @input="validateNumber($event, 'chatCountEnd')">
            </el-input>
          </el-col>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSearch1">查询</el-button>
          <el-dropdown style="box-sizing: border-box; margin-left:6px;"
            v-if="checkPermission(['api:customerservice:UnPayOrder:UnPayOrderExport'])" size="mini" split-button
            type="primary" icon="el-icon-share" @command="handleCommand"> 导出
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item class="Batcoperation" style="height: 10px"></el-dropdown-item>
              <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a">列表数据</el-dropdown-item>
              <el-dropdown-item class="Batcoperation"  style="padding: 0 25px" command="b">聊天数据</el-dropdown-item>
              <el-dropdown-item class="Batcoperation"  style="padding: 0 25px" command="c">聊天数据带关键词</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" style="height: 10px"></el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button icon="vxe-icon-custom-column" @click="clickToolbar" />
        </el-form-item>
      </el-form>
    </div>
  </template>

    <div class="uptime">数据更新时间: {{ upDataTime }}</div>
    <!--列表-->
    <Ces-table ref="table" :that="that" :isIndex="true" @sortchange="sortchange" :tableData="tableData"
      :showsummary="true" :summaryarry="summaryarry" :tableCols="tableCols" :loading="listLoading"
      :tableHandles='tableHandles'>
      <template slot='extentbtn'>
        <el-button-group>
          <el-tooltip content="会话时间查询规则：售后页面能看到以创建时间维度，当天只能看到创建时间4天前的数据，如果后补数据延迟创建只能延后看，例如：
会话是4号，正常入库创建时间是在5号，也就是4号的数据，只能5号+4天,也就是9号能看到，如果会话是4号，部分数据后补入库在6号创建，该部分数据在6号+4天，也就是10号能看到" placement="top">
            <el-button style="margin: 0; text-overflow: ellipsis; white-space: nowrap; overflow: hidden; width: 500px;">
              {{ ruleHints }}
            </el-button>
          </el-tooltip>
        </el-button-group>
      </template>
    </Ces-table>


    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" @get-page="gettrainplanList" />
    </template>
    <!-- 聊天记录 -->
<!--    <ChartListDialog :v-if="resourcedialogVisible" :isShow="resourcedialogVisible"
      @closeDialog="resourcedialogVisible = false" ref="chartRef">
    </ChartListDialog>-->
    <!-- 审核 -->
    <FirstDialog :v-if="firstdialogVisible" :isShow="firstdialogVisible" @closeDialog="firstdialogVisible = false"
      ref="firstRef" @upData="onSearch">
    </FirstDialog>
    <!-- 复审 -->
    <ReviewDialog :v-if="reviewdialogVisible" :isShow="reviewdialogVisible" @closeDialog="reviewdialogVisible = false"
      ref="reviewRef" @upData="onSearch">
    </ReviewDialog>
    <!-- 评判 -->
    <JudgeDialog :v-if="judgeRefdialogVisible" :isShow="judgeRefdialogVisible"
      @closeDialog="judgeRefdialogVisible = false" ref="judgeRef" @upData="onSearch">
    </JudgeDialog>

    <!-- 审核结果 -->
    <ResultDialog :v-if="resultdialogVisible" :isShow="resultdialogVisible" @closeDialog="resultdialogVisible = false"
      ref="resultRef">
    </ResultDialog>
    <!-- 订单日志信息 -->
    <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
      v-dialogDrag append-to-body>
      <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo" :isTx="isTx"
        style="z-index:10000;height:600px" />
    </el-dialog>



    <el-dialog :visible.sync="dailyNewspaperToolbar" width="40%" v-dialogDrag>
      <template #title>
        <div class="dialog-header">
          查询条件设置
          <el-button style="margin-left: 10px;" @click="selectAll">全选/取消全选</el-button>
        </div>
      </template>
      <div style="height: 100px;">
        <el-checkbox-group v-model="colOptions" @change="changeOptions">
          <el-checkbox v-for="(item, index) in colSelect" v-if="(item !== '日期' || !item)" :label="item"
            :key="item"></el-checkbox>
        </el-checkbox-group>
      </div>
      <div style="margin-top: 40px;display: flex;justify-content: end;">
        <el-button @click="dailyNewspaperToolbar = false">取消</el-button>
        <el-button type="primary" @click="verifyOptions" v-throttle="3000">确认</el-button>
      </div>
    </el-dialog>

    <el-dialog title="关键词搜索" :visible.sync="keywordSearchdialogVisibleSyj" width="25%" :close-on-click-modal=false :before-close="closeDialog"
      v-dialogDrag>
      <span>
        <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
          <el-form-item prop="chatContentKeyword" label="聊天记录带关键词:">
            <el-input style="width:80%" clearable v-model="Filter.chatContentKeyword" :maxlength="50"></el-input>
          </el-form-item>
        </el-form>
      </span>
      <span  slot="footer"  class="dialog-footer">
          <el-button @click="closeGroup">取 消</el-button>
          <el-button type="primary" @click="addKeyword">确 定</el-button>
        </span>

    </el-dialog>

  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import CesTable from "@/components/Table/table.vue";
import FirstDialog from "@/views/customerservice/chartCheck/firstDialog.vue";
import ReviewDialog from "@/views/customerservice/chartCheck/reviewDialog.vue";
import ChartListDialog from "@/views/customerservice/chartCheck/chartListDialog.vue";
import ResultDialog from "@/views/customerservice/chartCheck/resultDialog.vue";
import JudgeDialog from "@/views/customerservice/chartCheck/judgeDialog.vue";
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { formatLinkProCode } from "@/utils/tools";

import { getList as getshopList } from "@/api/operatemanage/base/shop";
import { SetVxeTableColumnCacheAsync, GetVxeTableColumnCacheAsync } from '@/api/admin/business'
import {
  getUnpayOrderList,
  exportUnpay,
  getGroupManagerList,
  getGruopNameList,
  getUserNameList, GetUnpayOrderSalesList,
} from "@/api/customerservice/chartCheck";

import {
  getDirectorGroupList
} from "@/api/operatemanage/base/shop.js";

import { param } from "jquery";
const tableHandles = [


];
const platformList = [
  { name: "拼多多", value: 2 },
  { name: "抖音", value: 6 },
  { name: "天猫", value: 1 },
  // { name: "淘工厂", value: 8 },
  { name: "淘宝", value: 9 },
]
const tableCols = [
  {
    istrue: true,
    prop: "conversationId",
    label: "数据编码",
    width: "180",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "auditState",
    label: "审核状态",
    sortable: "custom",
    width: "90",
    formatter: (row) => {
      console.log(row.auditState);
      return ['审核', '待复审', '完成', '待评判'][row.auditState]
    },
  },
  {
    istrue: true,
    prop: "orderState",
    label: "订单状态",
    sortable: "custom",
    width: "90",
    formatter: (row) => {
      return ['未付款', '已关闭'][row.orderState]
    },
  },
  {
    istrue: true,
    prop: "orderNo",
    label: "线上订单号",
    sortable: "custom",
    width: "180",
    type: 'click',
    handle: (that, row) => that.showLogDetail(row)
  }, {
    istrue: true,
    display: true,
    prop: "proId",
    label: "宝贝ID",
    sortable: "custom",
    type: "html",
    width: "150",
    formatter: (row) => formatLinkProCode(row.platform, row.proId),
  }, {
    istrue: true,
    prop: "createdTime",
    label: "创建时间",
    width: "100",
    sortable: "custom",
    formatter: (row) => {
      return row.createdTime ? formatTime(row.createdTime, "YYYY-MM-DD") : "";
    },
  }, {
    istrue: true,
    prop: "conversationTime",
    label: "会话时间",
    width: "100",
    sortable: "custom",
    formatter: (row) => {
      return row.conversationTime ? formatTime(row.conversationTime, "YYYY-MM-DD") : "";
    },
  }, {
    istrue: true,
    prop: "orderTime",
    label: "下单时间",
    width: "100",
    sortable: "custom",
    formatter: (row) => {
      return row.orderTime ? formatTime(row.orderTime, "YYYY-MM-DD") : "";
    },
  }, {
    istrue: true,
    prop: "platform",
    label: "平台",
    width: "60",
    sortable: "custom",
    formatter: (row) => {
      return platformList.filter(item => item.value == row.platform)[0].name
    },
  },
  {
    istrue: true,
    prop: "shopName",
    label: "店铺",
    width: "120",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "styleCode",
    label: "系列编码",
    width: "120",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "chatCount",
    label: "聊天条数",
    width: "120",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "directorUserName",
    label: "负责运营",
    width: "120",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "directorGroupUserName",
    label: "运营小组",
    width: "120",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "chatAccount",
    label: "聊天账号",
    width: "120",
    sortable: "custom",
  },
  {
    istrue: false,
    prop: "userName",
    label: "使用账号人",
    width: "120",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "responsibleName",
    label: "责任客服",
    sortable: "custom",
    width: "120",
  },
  {
    istrue: false,
    prop: "groupName",
    label: "分组",
    width: "120",
    sortable: "custom",
  },
  {
    istrue: false,
    prop: "groupManager",
    label: "组长",
    width: "120",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "initialAuditType",
    label: "审核结果",
    width: "80",
    sortable: "custom",
    formatter: (row) => {
      let name = ''
      if (row.initialAuditType == 1) {
        name = '合格'
      } else if (row.initialAuditType == 2) {
        name = '不合格'
      }
      return name;
    },
  },
  {
    istrue: true,
    prop: "refuseInitialAuditType",
    label: "审核类型",
    width: "90",
    sortable: "custom",
    formatter: (row) => {
      return row.refuseInitialAuditType
    },
  },
  {
    istrue: true,
    prop: "initialOperatorTime",
    label: " 审核时间",
    width: "90",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "initialOperator",
    label: "审核人",
    width: "90",
    sortable: "custom",
  },
  /*{
    istrue: true,
    prop: "finalOperator",
    label: "复审人",
    width: "90",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "finalAuditType",
    label: "复审结果",
    width: "80",
    sortable: "custom",
    formatter: (row) => {
      let name = ''
      if (row.finalAuditType == 1) {
        name = '合格'
      } else if (row.finalAuditType == 2) {
        name = '不合格'
      }
      return name;
    },
  },
  {
    istrue: true,
    prop: "refuseFinalAuditType",
    label: "复审类型",
    width: "80",
    sortable: "custom",
    formatter: (row) => {
      return row.refuseFinalAuditType;
    },
  },
  {
    istrue: true,
    prop: "finalOperatorTime",
    label: "复审时间",
    width: "90",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "judgmentOperator",
    label: "评判人",
    width: "90",
    sortable: "custom",
  },
  {
    istrue: false,
    prop: "judgmentAuditType",
    label: "评判结果",
    width: "80",
    sortable: "custom",
    formatter: (row) => {
      let name = ''
      if (row.judgmentAuditType == 1) {
        name = '合格'
      } else if (row.judgmentAuditType == 2) {
        name = '不合格'
      }
      return name;
    },
  },
  {
    istrue: false,
    prop: "refuseJudgmentAuditType",
    label: "评判类型",
    width: "80",
    sortable: "custom",
    formatter: (row) => {
      return row.refuseJudgmentAuditType;
    },
  },*/
  /* {
     istrue: true,
     prop: "expirationTime",
     label: "剩余审核时间",
     width: "120",
     sortable: "custom",
   },
   {
     istrue: true,
     prop: "judgmentOperatorTime",
     label: "评判时间",
     width: "90",
     sortable: "custom",
   },
  {
    istrue: false,
    prop: "lastOperator",
    label: "最后审核人",
    width: "120",
    sortable: "custom",
  },
  {
    istrue: false,
    prop: "lastOperatorTime",
    label: "最后审核时间",
    width: "160",
    sortable: "custom",
  },*/
  {
    istrue: true,
    type: "button",
    label: "操作",
    align: "center",
    fixed: "right",
    width: "120",
    btnList: [
      // { label: "聊天记录", handle: (that, row) => that.showDetail(row) },
      { label: "审核", display: (row) => { return row.auditState != 0 }, handle: (that, row) => that.openFirstLog(row), permission: "api:customerservice:UnPayOrder:Initial" },
     /* { label: "复审", display: (row) => { return row.auditState != 1 }, handle: (that, row) => that.openReviewLog(row), permission: "api:customerservice:UnPayOrder:Final" },
      {
        label: "评判", display: (row) => { return row.auditState != 3 }, handle: (that, row) => that.openJudgeLog(row), permission: "api:customerservice:UnPayOrder:Judgment",
        ishide: (that, row) => { return row.platform == 2 }
      },*/
      { label: "查看", handle: (that, row) => that.openResultLog(row) },
    ],
  },
];
// const tianMaoList=["回复问题","专业能力","敷衍怠慢","态度问题","客户自身原因","存在违规"];//天猫/淘宝/抖音
// const pxxList= ["对产品不熟悉","辱骂/反问/嘲讽/阴阳顾客","违背/过度承诺","推荐错误","答非所问","敷衍了事","话术不专业","其他"];//pxx
const allList = ["回复问题", "专业能力", "敷衍怠慢", "存在违规/过度承诺", "存在违规/引导线下交易", "存在违规/其他违规", "态度问题/辱骂客户", "态度问题/怒怼客户", "态度问题/反问客户", "态度问题/不耐烦"];//全部
export default {
  name: "pareSales",
  components: {
    MyContainer,
    CesTable,
    FirstDialog,
    ReviewDialog,
    ChartListDialog,
    ResultDialog,
    JudgeDialog,
    OrderActionsByInnerNos,
    vxetablebase,
    inputYunhan
  },
  props: ["chatInfos"],
  data() {
    return {
      storeid: 'pinduoduoDailyPaper20248100425',
      selectedTitles: [],
      dailyNewspaperToolbar: false,
      that: this,
      Filter: {
        conversationTime: [
          formatTime(dayjs().startOf("month"), "YYYY-MM-DD HH:mm:ss"),
          formatTime(new Date(), "YYYY-MM-DD 23:59:59"),
        ],
        refuseJudgmentAuditType: "",
        judgmentAuditType: null,
        refuseFinalAuditType: "",
        finalAuditType: null,
        refuseInitialAuditType: "",
        initialAuditType: null,
        groupManager: "",
        groupNameList: [],
        userName: "",
        shopNameList: [],
        initialOperator: null,
        proId: "",
        orderNo: "",
        chatAccount: "",
        platform: null,
        orderState: null,
        auditTime: [],
        createdTime: [],
        timerange: [],
        initialOperatorTime: [],//审核时间
        finalOperatorTime: [],//复审时间
        judgmentOperatorTime: [],//评判时间
        chatContentKeyword:"",
        orderNoList:"",
        chatCountStart: '', // 默认值为空字符串
        chatCountEnd: ''
        // timerange: [
        //   formatTime(dayjs().startOf("month"), "YYYY-MM-DD HH:mm:ss"),
        //   formatTime(new Date(), "YYYY-MM-DD 23:59:59"),
        // ],
        // orderNo: "",
        // proId: "",
        // shopNameList: null,
        // chatAccount: "",
        // isChat: null,
        // auditState: null,
        // initialOperator: null,//审核人
        // finalAuditType: null,//复审类型
        // orderState: null,
        // platform: null,
        // initialAuditType: null,//审核类型
        // userName: null,//账号使用人
        // groupNameList: [],//组名
        // groupManager: null,//组长
        // RefuseJudgmentAuditType: null,//评判类型
        // JudgmentAuditType: null,//评判结果
      },
      // pxxList:pxxList,
      // tianMaoList:tianMaoList,
      // allList:allList,
      reviewStatusList: [
        { name: "审核", value: 0 },
       /* { name: "待复审", value: 1 },
        { name: "待评判", value: 3 },*/
        { name: "完成", value: 2 },
      ],
      orderStatusList: [
        { name: "未付款", value: 0 },
        { name: "已关闭", value: 1 },
      ],
      platformList: platformList,
      firstTrialList: [
        { name: "合格", value: 1 },
        { name: "不合格", value: 2 },
      ],
      groupManagerList: [],
      groupNameList: [],
      userNameList: [],
      statusList: allList,
      tableData: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: null,
      pager: { orderBy: "createdTime", isAsc: false },
      listLoading: false,
      shopList: [],
      resourcedialogVisible: false,
      firstdialogVisible: false,
      reviewdialogVisible: false,
      judgeRefdialogVisible: false,
      resultdialogVisible: false,
      pickerOptions: {
        disabledDate(date) {
          // 设置禁用日期
          const start = new Date("1970/1/1");
          const end = new Date("9999/12/31");
          return date < start || date > end;
        },
      },
      defaultDate: new Date(),
      upDataTime: "",
      dialogHisVisible: false,
      orderNo: '',
      isTx: false,
      refcurrentPag: 0,
      paramsPreInfo: this.chatInfos,
      btnHide: true,//搜索默认收缩
      directoList: [],
      colOptions: [],
      colSelect: ["创建时间", "下单日期", "审核日期","审核状态",
        "订单状态", '平台',
        '聊天账号', "线上订单号", '宝贝ID',
        '审核人', '店铺',
        '账号使用人', '分组',
        '组长', '审核结果',
        '审核类型',  '负责运营',
        // '复审结果', '复审类型', '评判结果','评判类型',
        '运营小组', '责任客服',
        // '复审人','评判人','审核日期', '复审日期','评判日期',
        '数据编码',
        '系列编码','聊天条数'],
        keywordSearchdialogVisibleSyj: false,     //添加弹出框控制
        orderNoList:'',
        tableHandles: tableHandles,
        ruleHints: '',
    };
  },
  watch: {
    chatInfos: {
      handler: function (newVal, oldVal) {
        if (newVal) {
          this.paramsPreInfo = newVal;
          // this.btnHide=true;//展开搜索
          setTimeout(() => {
            this.showlistPre();
          }, 200);
        }
      },
      immediate: true,
    },
  },
  async mounted() {
    await this.init();
    this.oninitializeEcho();
    this.ruleHints = "会话时间查询规则：售后页面能看到以创建时间维度，当天只能看到创建时间4天前的数据，如果后补数据延迟创建只能延后看，例如：会话是4号，正常入库创建时间是在5号，也就是4号的数据，只能5号+4天,也就是9号能看到，如果会话是4号，部分数据后补入库在6号创建，该部分数据在6号+4天，也就是10号能看到"

    // await this.onSearch();
  },
  methods: {
    validateNumber(value, field) {
    // 只允许数字字符
    const numberValue = value.replace(/\D/g, '');

    // 更新绑定的数据
    this.Filter[field] = numberValue;
  },
     //复选框数据
     chooseCode(row) {
      this.unmountrows = row
    },
    //点击确定
    async verifyOptions() {
      await SetVxeTableColumnCacheAsync({ tableId: this.storeid, ColumnConfig: JSON.stringify(this.colOptions) });
      var arr = this.colSelect.filter(i => this.colOptions.indexOf(i) < 0); // 未选中的
      this.selectedTitles = this.colSelect.filter(i => {
        if (i !== '日期') {
          return !arr.includes(i)
        }
      });
      this.dailyNewspaperToolbar = false;
    },
    //全选
    changeOptions(valArr) {
      this.colOptions = valArr;
    },
    //初始化
    selectAll() {
      if (this.colOptions.length != this.colSelect.length) {
        this.colOptions = this.colSelect.map(i => i);
      } else {
        this.colOptions = [];
      }
    },
    //数据初始化回显
    async oninitializeEcho() {
      const { data, success } = await GetVxeTableColumnCacheAsync({ tableId: this.storeid });
      if (success) {
        let storeData = data ? JSON.parse(data) : [];
        this.colOptions = this.colSelect.filter(i => storeData.includes(i));
      } else {
        this.colOptions = [];
      }
      this.selectedTitles = this.colOptions
    },
    //点击设置
    clickToolbar() {
      this.oninitializeEcho();
      this.dailyNewspaperToolbar = true;
    },
    showlistPre() {
      this.Filter.platform = this.paramsPreInfo.platform
      this.changePlatform(this.paramsPreInfo.platform)

      this.Filter.groupNameList = [];
      if (this.paramsPreInfo.groupName) {
        this.Filter.groupNameList = [this.paramsPreInfo.groupName]
      }
      this.Filter.userName = this.paramsPreInfo.userName
      if (this.paramsPreInfo.num == 1) {
        this.Filter.initialAuditType = 2
        this.Filter.refuseInitialAuditType = this.paramsPreInfo.typeName
      }

      if (this.paramsPreInfo.num == 2) {
        this.Filter.judgmentAuditType = 2
        this.Filter.refuseJudgmentAuditType = this.paramsPreInfo.typeName
      }

      this.Filter.createdTime = [];
      this.Filter.createdTimeStart = null;
      this.Filter.createdTimeEnd = null;

      this.Filter.conversationTime = [];
      this.Filter.conversationTimeStart = null;
      this.Filter.conversationTimeEnd = null;

      this.Filter.auditTime = [];
      if (this.paramsPreInfo.auditTime.length > 0) {
        const data = this.paramsPreInfo.auditTime;
       /* this.Filter.auditTime = [data[0], data[1]];
        this.Filter.operatorTimeStart = data[0];
        this.Filter.operatorTimeEnd = data[1];*/
        this.Filter.initialOperatorTime = [data[0], data[1]];
        if (this.Filter.initialOperatorTime) {
          this.Filter.initialOperatorTimeStart = data[0];
          this.Filter.initialOperatorTimeEnd = data[1];
        } else {
          this.Filter.initialOperatorTimeStart = null;
          this.Filter.initialOperatorTimeEnd = null;
        }
      }
      this.Filter.timerange = [];
      if (this.paramsPreInfo.roderTime.length > 0) {
        const data = this.paramsPreInfo.roderTime;
        this.Filter.timerange = [data[0], data[1]];
        this.Filter.orderTimeStart = data[0];
        this.Filter.orderTimeEnd = data[1];
      }

      // this.Filter.specialGroupName = null;
      // if (!this.paramsPreInfo?.groupName && this.paramsPreInfo.dataplatform != 0) {
      //   this.Filter.specialGroupName = "";
      // }
      // this.Filter.specialUserName = null;
      // if (!this.paramsPreInfo?.userName && this.paramsPreInfo.dataplatform == 2) {
      //   this.Filter.specialUserName = "";
      // }

      //维度是分组
      if(this.paramsPreInfo.dataplatform== 1 &&  !this.paramsPreInfo?.groupName ){
        this.Filter.specialGroupName = "";
      }
      //维度是个人
      if(this.paramsPreInfo.dataplatform== 2 &&  !this.paramsPreInfo?.userName ){
        this.Filter.specialUserName = "";
      }
      this.onSearch1()
    },

    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init() {
      var date1 = new Date(); date1.setDate(date1.getDate() - 4);
      var date2 = new Date(); date2.setDate(date2.getDate() - 1);
      this.Filter.conversationTime = [];
      this.Filter.conversationTime[0] = this.datetostr(date1);
      this.Filter.conversationTime[1] = this.datetostr(date2);
      //   const groupManager = await getGroupManagerList({platform:0,groupType:0});
      //   const gruopName = await getGruopNameList({platform:0,groupType:0});
      //   const userName = await getUserNameList({platform:0,groupType:0});
      //  this.Filter.groupManager=groupManager.data;
      //  this.Filter.gruopName=gruopName.data;
      //  this.Filter.userName=userName.data;
      const directo = await getDirectorGroupList();
      this.directoList = directo.data;
      // directoList
    },
   /* showDetail(row) { //查看聊天记录
      this.$refs.chartRef.keyWord = row.conversationId
      this.$refs.chartRef.platform = row.platform
      this.$refs.chartRef.dataJson = row;
      this.$refs.chartRef.tableData = this.tableData;
      this.resourcedialogVisible = true;
    },*/
    async openFirstLog(row) { //初级审核
      this.$refs.firstRef.dataJson = row
      this.$refs.firstRef.isShouQian = true
      this.$refs.firstRef.isShowOrHide = true
      this.$refs.firstRef.salesType  = 0
      var ii = this.tableData.filter(item => { return item.auditState == 0 });
      this.$refs.firstRef.tableData = this.tableData.filter(item => { return item.auditState == 0 });
      if (row.conversationUserId) {
        var params = {
          conversationUserId: row.conversationUserId,
          conversationId: row.conversationId,
          salesType: 0,
          shopId: row.shopId,
          auditState:2,
          orderBy: "createdTime",
          isAsc: false
        }
        const res = await GetUnpayOrderSalesList(params);
        if (!res?.success) {
          return;
        }
        this.$refs.firstRef.reviewList = res.data.list;
      } else {
        this.$refs.firstRef.reviewList = [];
      }
      this.firstdialogVisible = true;
    },
    openReviewLog(row) {//复审
      this.$refs.reviewRef.dataJson = row
      this.$refs.reviewRef.isShouQian = true
      this.$refs.reviewRef.isShowOrHide = true
      this.$refs.reviewRef.salesType  = 0
      var ii = this.tableData.filter(item => { return item.auditState == 1 });
      this.$refs.reviewRef.tableData = this.tableData.filter(item => { return item.auditState == 1 });
      this.reviewdialogVisible = true;
    },
    openJudgeLog(row) {  //评判
      this.$refs.judgeRef.dataJson = row
      this.$refs.judgeRef.isShouQian = true
      this.$refs.judgeRef.isShowOrHide = true
      this.$refs.judgeRef.salesType  = 0
      this.$refs.judgeRef.tableData = this.tableData.filter(item => { return item.auditState == 3 });
      this.judgeRefdialogVisible = true;
    },
    async openResultLog(row) { //查看
      this.$refs.resultRef.dataJson = row
      this.$refs.resultRef.isShowOrHide = true
      this.$refs.resultRef.tableData = this.tableData;
      // 设置为售前类型(0)
      this.$refs.resultRef.salesType = 0;
      if (row.conversationUserId) {
        var params = {
          conversationUserId: row.conversationUserId,
          conversationId: row.conversationId,
          salesType: 0,
          shopId: row.shopId,
          auditState:2,
          orderBy: "createdTime",
          isAsc: false
        }
        //获取审核的聊天数据
        const res = await GetUnpayOrderSalesList(params);
        if (!res?.success) {
          return;
        }
        this.$refs.resultRef.reviewList = res.data.list;
      } else {
        this.$refs.resultRef.reviewList = [];
      }
      this.$refs.resultRef.chatRecordKeywords = this.Filter.chatRecordKeywords; // 新增传递
      this.resultdialogVisible = true;
    },
    showLogDetail(row) {
      this.dialogHisVisible = true;
      this.isTx = row.platform == 1 || row.platform == 8 || row.platform == 9 ? true : false;
      this.orderNo = row.orderNo;
    },
    // 查询（子页面调用）
    onSearch() {
      this.$nextTick(() => {
        const ii = this.refcurrentPag > 1 ? this.refcurrentPag : 1;
        this.$refs.pager.setPage(ii);
        this.gettrainplanList();
      });
    },
        // 查询
    onSearch1() {
      this.$nextTick(() => {
        this.$refs.pager.setPage(1);
        this.gettrainplanList();
      });
    },
    async changePlatform(val) {
      this.groupManagerList = [];
      this.groupNameList = [];
      this.userNameList = [];
      this.Filter.groupManager = null
      this.Filter.groupNameList = null
      this.Filter.userName = null;

      this.shopList = []
      this.Filter.shopNameList = null
      if (val) {

        const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 2000 });
        this.shopList = res1.data.list
        const groupManager = await getGroupManagerList({ platform: val });
        const gruopName = await getGruopNameList({ platform: val });
        const userName = await getUserNameList({ platform: val});
        this.groupManagerList = groupManager.data;
        this.groupNameList = gruopName.data;
        this.userNameList = userName.data;
      }
    },
    getCondition() {//条件获取
      const para = { ...this.Filter };
      if (this.Filter.createdTime) {
        para.createdTimeStart = this.Filter.createdTime[0];
        para.createdTimeEnd = this.Filter.createdTime[1];
      }
      if (this.Filter.timerange) {
        para.orderTimeStart = this.Filter.timerange[0];
        para.orderTimeEnd = this.Filter.timerange[1];
      }
      if (this.Filter.conversationTime) {
        para.conversationTimeStart = this.Filter.conversationTime[0];
        para.conversationTimeEnd = this.Filter.conversationTime[1];
      }
      if (this.Filter.auditTime) {
        para.operatorTimeStart = this.Filter.auditTime[0];
        para.operatorTimeEnd = this.Filter.auditTime[1];
      } else {
        para.operatorTimeStart = '';
        para.operatorTimeEnd = '';
      }
      if (this.Filter.initialOperatorTime) {
        para.initialOperatorTimeStart = this.Filter.initialOperatorTime[0];
        para.initialOperatorTimeEnd = this.Filter.initialOperatorTime[1];
      } else {
        para.initialOperatorTimeStart = null;
        para.initialOperatorTimeEnd = null;
      }
      if (this.Filter.finalOperatorTime) {
        para.FinalOperatorTimeStart = this.Filter.finalOperatorTime[0];
        para.FinalOperatorTimeEnd = this.Filter.finalOperatorTime[1];
      }
      if (this.Filter.judgmentOperatorTime) {
        para.JudgmentOperatorTimeStart = this.Filter.judgmentOperatorTime[0];
        para.JudgmentOperatorTimeEnd = this.Filter.judgmentOperatorTime[1];
      }

      // if (!(para.orderTimeStart && para.orderTimeEnd)) {
      //   this.$message({ message: "请先选择日期！", type: "warning" });
      //   return false;
      // }
      if (this.Filter.shopNameList) para.shopNameList = this.Filter.shopNameList;
      else para.shopNameList = null;
      var pager = this.$refs.pager.getPager();
      this.refcurrentPag = pager.currentPage;//每次分页记录当前的页码，
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      return params;
    },
    async gettrainplanList() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await getUnpayOrderList(params);
      this.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.upDataTime = res.data.extData.dataUpdateTime;
      data.forEach((d) => {
        d._loading = false;
      });
      this.tableData = data;
      this.summaryarry = res.data.summary;
    },
    sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          orderBy: column.prop,
          isAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      this.onSearch1();
    },
    async onExport(type) {//导出列表数据；
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      // var loadingInstance = this.$loading({
      //   text: "正在导出，请稍后",
      //   fullscreen: false,
      // });
      params.dataType = type;  // 0：默认，1：带聊天记录
      var res = await exportUnpay(params);
      if (res?.success) {
        this.$message({ message: res.msg, type: "success" });
      }
      // loadingInstance.close();
      // const aLink = document.createElement("a");
      // let blob = new Blob([res], { type: "application/vnd.ms-excel" });
      // aLink.href = URL.createObjectURL(blob);
      // aLink.setAttribute(
      //   "download",
      //   "售前_" + new Date().toLocaleString() + ".xlsx"
      // );
      // aLink.click();
    },

    changPlatformState(val) {
      if (!this.Filter.platform) {
        this.$message({ message: "请先选择平台！", type: "warning" });
        return false;
      }
    },
    async handleCommand(command) {
      // if (this.selids.length == 0 && command != 'x') {
      //     this.$message({ type: 'warning', message: "请选择任务" });
      //     return;
      // }
      switch (command) {
        //列表数据
        case 'a':
          await this.onExport(0)
          break;
        //聊天数据
        case 'b':
          await this.onExport(1)
          break;
        //聊天数据带关键词
          case 'c':
        this.keywordSearchdialogVisibleSyj = true
          break;
      }
    },
    topShowOrhide() {
      if (this.btnHide) {
        this.btnHide = false;
      } else {
        this.btnHide = true;
      }
    },
    async closeGroup() {
      this.keywordSearchdialogVisibleSyj = false
      this.Filter.chatContentKeyword=""
    },
        //添加
     async addKeyword() {
      var params = this.getCondition();
       params.dataType = 1

       var res = await exportUnpay(params);
      if (res?.success) {
        this.$message({ message: res.msg, type: "success" });
        this.keywordSearchdialogVisibleSyj = false
        this.Filter.chatContentKeyword=""
      }else{
        this.Filter.chatContentKeyword=""
      }

      },
     closeDialog() {//关闭
      this.Filter.chatContentKeyword=""
      this.$emit("closeDialog");
      this.keywordSearchdialogVisibleSyj = false
    },
    async callback(val) {
            this.Filter.orderNoList = val;
            //this.onSearch();
        },
  },
  async entersearch(val) {
            // this.filter.indexNo = val;
            this.onSearch1();
        },

};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

::v-deep .mycontainer {
  position: relative;
}

.uptime {
  font-size: 14px;
  position: absolute;
  right: 30px;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 45px;
}
</style>
