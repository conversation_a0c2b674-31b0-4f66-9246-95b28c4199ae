<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->           
            <el-row>             
                <el-col :span="24" style="height:560px">                 
                    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' :tableData='list' :hasexpandRight="true"
                        :tableCols='tableCols' :isSelection="false" :isSelectColumn='false' :loading="pageLoading"  @sortchange='sortchange'>                      
                    </ces-table>
                </el-col>
            </el-row>
        </template>
        <template slot="footer">
            <my-pagination ref="pager" :total="total" :checked-count="0" @get-page="getlist" />
        </template>
    </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils/tools";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";


import { PageEmployeeStatusReportDtl } from '@/api/admin/deptuser'



const tableCols = [
    { istrue: true, prop: 'userName', label: '姓名', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'position', label: '岗位', sortable: 'custom' },
    { istrue: true, prop: 'hiredDate', label: '入职时间',  width: '120', sortable: 'custom' , 
        formatter: (row) =>row.hiredDate ? formatTime(row.hiredDate, "YYYY-MM-DD"):'' },
    { istrue: true, prop: 'leaveDate', label: '离职时间',  width: '120', sortable: 'custom' , 
        formatter: (row) =>row.leaveDate ? formatTime(row.leaveDate, "YYYY-MM-DD"):'' },
    
    { istrue: true, prop: 'workAge', label: '工龄' },   
    
]
export default {
    name: "UserStatusReportDtlList",
    components: { cesTable, MyContainer, MyConfirmButton },
    data() {
        return {
            that: this,
            total:0,
            list:[],              
            tableCols: tableCols,
            mode: 3,       
            summaryarry: {},
            pageLoading: false,           
            formEditMode: true,//是否编辑模式   
            pager: { OrderBy: "", IsAsc: false },
            filter:{}           
        };
    },
    async mounted() {       
    },
    computed: {

    },
    methods: {  
        async getlist() {           
            
            var pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }
            this.listLoading = true;
            this.list = [];
            this.pageLoading=true;
            const res = await PageEmployeeStatusReportDtl(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res?.data?.total
            const data = res?.data?.list     
                 
            this.list = data;
            this.pageLoading=false;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.getlist();
        },
        async loadData(filterArgs) {
            this.filter=filterArgs;

            let rsp=await this.getlist();           
          
        },
        onClose() {
            this.$emit('close');
        }       
    },
};
</script>
<style lang="scss" scoped>
.tempdiv ::v-deep img {
    width: auto;
    max-width: 1000px;
}
</style>