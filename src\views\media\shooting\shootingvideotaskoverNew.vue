<template>
    <!-- 已完成 -->
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-button-group>
                <div class="ssanc">

                    <div style="width:20%;display: inline-block;text-align: left;">
                        <span style="margin-right:0.8%;">
                            <el-input style="width:30%;" v-model.trim="filter.shootingTaskId" :maxlength=100
                                placeholder="编号" @keyup.enter.native="onSearch" clearable />
                        </span>
                        <span style="margin-right:0.8%;">
                            <el-input style="width:68%;" v-model.trim="filter.productShortName" :maxlength=100
                                placeholder="产品简称" suffix-icon="el-icon-search" @keyup.enter.native="onSearch" clearable />
                        </span>

                    </div>

                    <div style="width:25%;display: inline-block;">

                        <span style="margin-left:10px;">
                            <el-button style="width:90px;" type="primary" @click="onSearch">查&nbsp;询</el-button>
                        </span>

                        <span style="margin-left:5px;">
                            <el-button @click="onclear" plain>重置</el-button>
                        </span>


                    </div>

                    <div style="width:55%;display: inline-block;text-align: right;">


                        <span>
                            <el-dropdown style="box-sizing: border-box; margin: 1px 2px 2px 0"
                                v-if="checkPermission('shootingBatchOp')" size="mini" split-button type="primary"
                                icon="el-icon-share" @command="handleCommand">
                                批量操作
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item class="Batcoperation" command="x"
                                        style="height:10px;"></el-dropdown-item>
                                    <el-dropdown-item class="Batcoperation" command="h"
                                        style="padding: 0 25px">批量重启</el-dropdown-item>
                                    <el-dropdown-item class="Batcoperation" command="g"
                                        style="padding: 0 25px">批量统计</el-dropdown-item>
                                    <el-dropdown-item class="Batcoperation" command="x"
                                        style="height:10px;"></el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </span>

                        <span style="box-sizing: border-box; margin-left:6px;">
                            <el-button type="primary" @click="onExeprotShootingTask"
                                v-if="checkPermission('api:media:shootingvideo:ExportShootingTaskReport')">导出</el-button>
                        </span>


                        <span>
                            <el-radio-group style="margin-left:6px;" size="mini"
                                v-if="checkPermission('shootingDropDownList')" v-model="Oncommand"
                                @change="ShowHideonSearch">
                                <el-radio-button label="b">默认</el-radio-button>
                                <el-radio-button label="c">时效</el-radio-button>
                                <el-radio-button label="d">评分</el-radio-button>
                                <el-radio-button label="e">补改</el-radio-button>
                                <el-radio-button label="a">全部</el-radio-button>
                            </el-radio-group>
                        </span>
                    </div>
                </div>

                <div class="heardcss">

                    <span>
                        <el-select style="width: 6%;" v-model="filter.isTopOld" :clearable="true" placeholder="是否标记">
                            <el-option label="是" value="1"></el-option>
                            <el-option label="否" value="0"></el-option>
                        </el-select>
                    </span>

                    <span>
                        <el-select style="width: 6%;" v-model="filter.hasOverTime" :clearable="true" placeholder="是否完成">
                            <el-option label="是" value="1"></el-option>
                            <el-option label="否" value="0"></el-option>
                        </el-select>
                    </span>
                    <span>
                        <el-select style="width: 6%;" v-model="filter.hasConfirmTime" :clearable="true" placeholder="是否确认">
                            <el-option label="是" value="1"></el-option>
                            <el-option label="否" value="0"></el-option>
                        </el-select>
                    </span>
                    <span>
                        <el-select style="width: 12%;" v-model="filter.warehouse" placeholder="大货仓" multiple
                            :collapse-tags="true" clearable>
                            <el-option v-for="item in warehouselist" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </span>
                    <span>
                        <el-select style="width: 10%;" v-model="filter.fpPhotoLqName" placeholder="分配查询" multiple
                            :collapse-tags="true" filterable clearable>
                            <el-option v-for="item in fpPhotoLqNameList" :key="item.userName" :label="item.userName"
                                :value="item.userName" />
                        </el-select>
                    </span>
                    <span>
                        <el-select style="width:6%;" v-model="filter.operationGroup" :clearable="true" placeholder="运营组"
                            filterable>
                            <el-option v-for="item in groupList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </span>
                    <span>
                        <el-select style="width: 6%;" v-model="filter.dockingPeople" :clearable="true" placeholder="对接人"
                            filterable>
                            <el-option v-for="item in dockingPeopleList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </span>


                    <span>
                        <el-select style="width:7.5%;" v-model="filter.platform" placeholder="平台" multiple
                            :collapse-tags="true" clearable @change="onchangeplatform">
                            <el-option v-for="item in platformList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </span>
                    <span>
                        <el-select style="width:12.8%;" filterable v-model="filter.shopName" placeholder="店铺" clearable>
                            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                                :value="item.shopCode" />
                        </el-select>
                    </span>
                    <!--下拉（完成时间，创建时间，到货日期，申请日期，）-->
                    <span>
                        <el-select style="width:5.5%;" v-model="filter.searchTimeType" :clearable="true" placeholder="选择时间">

                            <el-option label="创建时间" value="2"></el-option>
                            <el-option label="完成时间" value="1"></el-option>
                            <el-option label="确认时间" value="3"></el-option>
                        </el-select>
                    </span>
                    <span>
                        <el-date-picker style="width:18%;position: relative;top:1px;" type="daterange" format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                            v-model="filter.createdtimerange" />
                    </span>



                </div>




                <!-- <div class="gddwz">
                    <p class="gddwznr">
                        暂无公告……暂22无公告……暂无公告……暂无公告……暂无公告……暂无公告……
                    </p>
                </div> -->



            </el-button-group>

        </template>
        <!--列表-->
        <!--  <vxetablebase
            ref ="shootingvideotask"
            :id="'shootingvideotaskoverNew202301291318001'"
            :tableData='tasklist' :tableCols='tableCols'
            :that='that'
            :hascheck='true'
            :hasSeq='false'
            :showsummary='true'
            :summaryarry='summaryarry'
            @summaryClick='onsummaryClick'
            :loading='listLoading'
            @sortchange='sortchange'
            checkboxall
            @checkboxall="selectchangeevent"
            @selectchangeevent="selectchangeevent"
            @rowChange="rowChange"
            >
        </vxetablebase> -->
        <shootingvideotaskTable ref="shootingvideotaskoverNew" :id="'shootingvideotaskoverNew202301291318001'"
            :tableData='tasklist' :tableCols='tableCols' :that='that' :hascheck='true' :hasSeq='false' :height="'100%'"
            :showsummary='true' :summaryarry='summaryarry' @summaryClick='onsummaryClick' :loading='listLoading'
            @sortchange='sortchange'  @checkboxall="selectchangeevent" @selectchangeevent="selectchangeevent"
            @rowChange="rowChange" @shootUrgencyCilck="shootUrgencyCilck" @openTaskRmarkInfo="openTaskRmarkInfo"
            @videotaskuploadfileDetal="videotaskuploadfileDetal" @editTask="editTask" @openComputOutInfo="openComputOutInfo"
            @onShowOrderDtl="onShowOrderDtl" @onShowExproessHttp="onShowExproessHttp">
        </shootingvideotaskTable>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList"
                :sizes="[50, 100, 200, 300, 800, 1000, 2000]" :page-size="1000" />
        </template>

        <!--编辑任务-->
        <el-drawer :visible.sync="editTaskshow" :close-on-click-modal="false" direction="rtl" :size="750"
            element-loading-text="拼命加载中" v-loading="addLoading" :show-close="false">
            <shootingvideotaskeditfrom :key="editopentime" ref="shootingvideotaskeditfrom" style="height: 100%;width:100%"
                :taskUrgencyList="taskUrgencyList" :groupList="groupList" :warehouselist="warehouselist"
                :platformList="platformList"  :islook='islook'></shootingvideotaskeditfrom>
        </el-drawer>
        <!--上传文件-->
        <el-drawer title="上传成果文件" :key="outopentime" :visible.sync="successfiledrawer" direction="rtl"
            :wrapperClosable="true" :close-on-press-escape="false" :before-close="successfiledrawerClose" size="60%">
            <shootinguploadaction ref="successfileinfo" :rowinfo="selectRowKey" :islook="islook"
                style="height: 92%;width:100%"></shootinguploadaction>
            <div class="drawer-footer">
                <el-button @click="successfiledrawer = false">取 消</el-button>
            </div>
        </el-drawer>

        <!--查看上传文件并打分-->
        <el-drawer title="查看成果文件" :key="outsueccessopentime" :visible.sync="successfileshow" direction="ltr"
            :wrapperClosable="true" :close-on-press-escape="false" size="50%" >
            <shootingvideotaskuploadsuccessfilesocre ref="shootingvideotaskuploadsuccessfilesocre" :platform="platform"
                :clostfun="successfiledrawerscoreClose" :rowinfo="selectRowKey" :isOverList="true" :islook="islook"
                style="height: 92%;width:100%"></shootingvideotaskuploadsuccessfilesocre>
            <div class="drawer-footer">
                <el-button @click="successfileshow = false">关 闭</el-button>
                <my-confirm-button type="submit" :loading="filesocreLoading" @click="onSubComputOutInfo" v-show="islook" />
            </div>
        </el-drawer>

        <!--查看上传附件-->
        <el-dialog title="查看参考" :key="outconfilekey" :visible.sync="viewReference" width="60%" :close-on-click-modal="true"
            element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <shootingvideotaskuploadfile ref="shootingvideotaskuploadfile" :rowinfo="selectRowKey">
            </shootingvideotaskuploadfile>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="viewReference = false">关 闭</el-button>
                </span>
            </template>
        </el-dialog>
        <!--查看详情-->
        <el-dialog title="查看备注" :key="markopentime" :visible.sync="viewReferenceRemark" width="60%"
            :close-on-click-modal="false" element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <shootingTaskRemark ref="shootingTaskRemark" :rowinfo="selectRowKey" :islook="islook"></shootingTaskRemark>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="viewReferenceRemark = false">关 闭</el-button>
                    <my-confirm-button type="submit" :loading="shootingTaskRemarkrawer" @click="sumbitshootingTaskRemark"
                        v-show="!islook" />
                </span>
            </template>
        </el-dialog>
        <!--加急审核-->
        <el-dialog title="加急审核" :visible.sync="taskUrgencyAproved" width="20%" :close-on-click-modal="false"
            :key="urgencyopentime" element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <div style="vertical-align: middle; margin-top: 20px;margin-left: 80px;">
                <el-radio v-model="taskUrgencyStatus" label="1" border>同意</el-radio>
                <el-radio v-model="taskUrgencyStatus" label="9" border>驳回</el-radio>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="(taskUrgencyAproved = false)">取 消</el-button>
                    <my-confirm-button type="submit" @click="taskUrgencyApp" />
                </span>
            </template>
        </el-dialog>

        <el-dialog title="下单发货" :visible.sync="dialogAddOrderVisible" width="50%" @close="closeAddOrder" v-dialogDrag
            v-loading="dialogAddOrderLoading" :close-on-click-modal="false">
            <template>
                <el-form class="ad-form-query" :model="addOrderForm" ref="addOrderForm" :rules="addOrderFormRules"
                    label-position="right" label-width="100px">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="19" :xl="19">
                            <el-form-item label="已选任务编号:">
                                <span v-for="(item, index) in selShootingTaskIdSpanList" :key="index" v-html="item"></span>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="5" :xl="5">
                            <el-form-item label="">
                                <el-button @click="onAddressSet" type="text">发货地址维护
                                </el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :hidden="true">
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                            <el-form-item label="收货人" prop="receiverName">
                                <el-input v-model="addOrderForm.receiverName" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                            <el-form-item label="收货电话" prop="receiverPhone">
                                <el-input v-model="addOrderForm.receiverPhone" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item label="拿样方式" prop="isZt">
                                <el-radio v-model="addOrderForm.isZt" label="1">仓库自提</el-radio>
                                <el-radio v-model="addOrderForm.isZt" label="0">快递寄样</el-radio>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="calculateUnit" label="核算单位">
                                <el-select v-model="addOrderForm.calculateUnit" placeholder="请选择核算单位" style="width:100%;">
                                    <el-option v-for="item in calculateUnitlist" :key="item" :label="item"
                                        :value="item" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="addOrderForm.isZt != 1">
                            <el-form-item prop="warehouse" label="自提仓库">
                                <el-select v-model="addOrderForm.warehouse" placeholder="请选择自提仓库" style="width:100%;">
                                    <el-option v-for="item in warehouselist" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" :hidden="addOrderForm.isZt != 0">
                            <el-form-item label="详细地址" prop="receiverAddressAllInfo">
                                <el-select v-model="addOrderForm.receiverAddressAllInfo" placeholder="选择详细地址"
                                    style="width:100%;" @change="receiverAddressSelChange">
                                    <el-option v-for="item in receiverAddressList" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                            <el-form-item label="收货省" prop="receiverStateCode">
                                <el-select v-model="addOrderForm.receiverStateCode" placeholder="收货省" style="width:100%;"
                                    @change="receiverStateChange">
                                    <el-option v-for="item in receiverStateList" :key="item.code" :label="item.name"
                                        :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                            <el-form-item label="收货市" prop="receiverCityCode">
                                <el-select v-model="addOrderForm.receiverCityCode" placeholder="收货市" style="width:100%;"
                                    @change="receiverCityChange">
                                    <el-option v-for="item in receiverCityList" :key="item.code" :label="item.name"
                                        :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                            <el-form-item label="收货区" prop="receiverDistrictCode">
                                <el-select v-model="addOrderForm.receiverDistrictCode" placeholder="收货区"
                                    style="width:100%;">
                                    <el-option v-for="item in receiverDistrictList" :key="item.code" :label="item.name"
                                        :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" :hidden="true">
                            <el-form-item label="详细地址" prop="receiverAddress">
                                <el-input v-model="addOrderForm.receiverAddress" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="备注" prop="remark">
                                <el-input v-model="addOrderForm.remark" type="textarea" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </template>
            <template>
                <el-row>
                    <el-card class="box-card" style="width:100% ;height: 45px;overflow: hidden;">
                        <div slot="header" class="clearfix">
                            <span>商品明细</span>
                            <el-button @click="onSelctOrderGoods()" style="float: right; padding: 3px 0"
                                type="text">添加商品明细</el-button>
                            <el-button @click="onSyncOrderGoods()" style="float: right; padding: 3px 0;margin-right: 60px;"
                                type="text">同步附件商品</el-button>
                        </div>
                    </el-card>
                    <el-card class="box-card" style="width:100% ;height: 300px;overflow: auto;">
                        <el-table :data="addOrderForm.orderGoods">
                            <el-table-column label="序号" width="50">
                                <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                            </el-table-column>
                            <el-table-column prop="goodsCode" width="150" label="商品编码" />
                            <el-table-column prop="goodsName" label="商品名称" />
                            <el-table-column prop="goodsPrice" width="100" label="单价" v-if="false" />
                            <el-table-column prop="goodsQty" width="150" label="数量">
                                <template slot-scope="scope">
                                    <el-input-number v-model="scope.row.goodsQty" :min="1" :max="100000000" placeholder="数量"
                                        :precision="0" @change="addOrderFormGoodsQtyChange(scope.row)">
                                    </el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column prop="goodsAmount" width="100" label="总额" v-if="false" />
                            <el-table-column lable="操作" width="100">
                                <template slot-scope="scope">
                                    <el-button type="danger" @click="onDelDtlGood(scope.$index)">删除 <i
                                            class="el-icon-remove-outline"></i>
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-card>
                </el-row>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <span
                        style="font-size:10px;color:red;">点击提交按钮将发起钉钉审批，自提单审批通过即代表到样，非自提单审批通过则自动同步订单到聚水潭。&nbsp;&nbsp;</span>
                    <el-button @click="dialogAddOrderVisible = false">取 消</el-button>
                    <my-confirm-button type="submit" :validate="addOrderFormValidate" :loading="dialogAddOrderSubmitLoding"
                        @click="onAddOrderSave">
                        提交
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="选择商品编码" :visible.sync="orderGoodschoiceVisible" width='85%' height='500px' v-dialogDrag
            :close-on-click-modal="false">
            <goodschoice :ischoice="true" ref="orderGoodschoice" style="z-index:10000;height:500px" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="orderGoodschoiceVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onQuerenOrderGoods()">确 定</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="收货地址维护" :visible.sync="dialogAddressVisible" width='50%' height='400px' v-dialogDrag
            :close-on-click-modal="false">
            <template>
                <el-form class="ad-form-query" :model="addAddressForm" ref="addAddressForm" :rules="addAddressFormRules"
                    label-position="right" label-width="100px">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item label="收货人" prop="receiverName">
                                <el-input v-model="addAddressForm.receiverName" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item label="收货电话" prop="receiverPhone">
                                <el-input v-model="addAddressForm.receiverPhone" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item label="收货省" prop="receiverStateCode">
                                <el-select v-model="addAddressForm.receiverStateCode" placeholder="收货省" style="width:100%;"
                                    @change="receiverStateChange2">
                                    <el-option v-for="item in receiverStateList" :key="item.code" :label="item.name"
                                        :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item label="收货市" prop="receiverCityCode">
                                <el-select v-model="addAddressForm.receiverCityCode" placeholder="收货市" style="width:100%;"
                                    @change="receiverCityChange2">
                                    <el-option v-for="item in receiverCityList2" :key="item.code" :label="item.name"
                                        :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item label="收货区" prop="receiverDistrictCode">
                                <el-select v-model="addAddressForm.receiverDistrictCode" placeholder="收货区"
                                    style="width:100%;">
                                    <el-option v-for="item in receiverDistrictList2" :key="item.code" :label="item.name"
                                        :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="详细地址" prop="receiverAddress">
                                <el-input v-model="addAddressForm.receiverAddress" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <ces-table ref="addresstable" :that='that' :isIndex='true' :hasexpand='false'
                                :isSelectColumn="false" style="height:350px" :tableData='addressList'
                                :tableCols='addressTableCols' :loading="addressListLoading">
                            </ces-table>
                        </el-col>
                    </el-row>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogAddressVisible = false">取 消</el-button>
                    <my-confirm-button type="submit" :loading="dialogAddAddressSubmitLoding" @click="onAddAddressSave">
                        提交
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="任务下单记录" :visible.sync="dialogOrderDtlVisible" width='88%' v-dialogDrag
            :close-on-click-modal="false" :v-loading="dialogOrderDtlLoading" @close="dialogOrderDtlColsed">
            <el-row
                style="height:35px;line-height:35px; font-size: 16px;font-weight: bold;color: black;margin-bottom: 2px;">
                <span>下单发货信息：</span>
            </el-row>
            <el-container>
                <el-main style="height:192px;">
                    <ces-table ref="tablexdfhmain" :that='that' :isIndex='true' :hasexpand='true' :tableData='xdfhmainlist'
                        :tableCols='xdfhmainTableCols' :loading="xdfhmainLoading" style="height:190px"
                        :isSelectColumn="false" @cellclick="onxdfhmainCellClick">
                    </ces-table>
                </el-main>
            </el-container>
            <el-row
                style="height:35px;line-height:35px; font-size: 16px;font-weight: bold;color: black; margin-top: 10px;margin-bottom: 2px;">
                <span>下单发货商品明细信息：</span>
            </el-row>
            <el-container>
                <el-main style="height:262px;">
                    <ces-table ref="tablexdfhdtl" :that='that' :isIndex='true' :hasexpand='true' :tableData='xdfhdtllist'
                        :tableCols='xdfhdtlTableCols' :loading="xdfhdtlLoading" style="height:260px"
                        :isSelectColumn="false">
                    </ces-table>
                </el-main>
            </el-container>
        </el-dialog>
        <el-drawer title="物流跟踪" :visible.sync="drawervisible" direction="rtl" :append-to-body="true">
            <logistics ref="logistics"></logistics>
        </el-drawer>
        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
            v-dialogDrag>
            <orderLogPage ref="orderLogPage" :orderNoInner="sendOrderNoInner" style="z-index:10000;height:600px" />
        </el-dialog>
        <!--分配人趋势图-->
        <el-drawer title="分配任务统计" :visible.sync="shootingchartforfpvisible" direction="btt" :append-to-body="true"
            size='75%' :key="tjopentime">
            <shootingchartforfp ref="shootingchartforfp" :charttype="fpcharttype"></shootingchartforfp>
        </el-drawer>
    </my-container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import cesTable from "@/components/Table/tableforvedio.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import {
    pageShootingViewTaskAsync, getTaskReferenceInfo, delShootingTploadFileTaskAsync, taskOverActionsAsync, exportShootingTaskReport
    , shootUrgencyCilckAsync, getCityAllData, shootingTaskAddOrderSaveCheckTaskIds, shootingTaskAddOrderSave, getShootingTaskOrderListById
    , signShootingTaskActionAsync, unSignShootingTaskActionAsync, endShootingTaskActionAsync, deleteTaskActionAsync, caclShootingTaskActionAsync,
    taskRestartActionAsync
} from '@/api/media/ShootingVideo';
import checkPermission from '@/utils/permission'
import uploadfile from '@/views/media/shooting/uploadfile'
import shootinguploadaction from '@/views/media/shooting/shootinguploadaction'
import shootingTaskRemark from '@/views/media/shooting/ShootingTaskRemark'
import shootingvideotaskuploadfile from '@/views/media/shooting/shootingvideotaskuploadfile'
import shootingvideotaskuploadsuccessfilesocre from '@/views/media/shooting/shootingvideotaskuploadsuccessfilesocre'
import shootingvideotaskeditfrom from '@/views/media/shooting/shootingvideotaskeditfromNew'
import shootingchartforfp from '@/views/media/shooting/shootingchartforfp'
import { getVedioTaskOrderAddressList, saveVedioTaskOrderAddress, deleteVedioTaskOrderAddress } from '@/api/media/vediotask';
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import { formatTime } from "@/utils";
import { rulePlatform } from "@/utils/formruletools";
import { formatWarehouse, ShootingVideoTaskUrgencyOptions } from "@/utils/tools";
import { getDirectorGroupList } from '@/api/operatemanage/base/shop';
import goodschoice from "@/views/base/goods/goods4.vue";
import logistics from '@/components/Comm/logistics'
import orderLogPage from "@/views/order/logisticsWarning/orderLogPage.vue";
import shootingvideotaskTable from '@/views/media/shooting/shootingvideotaskTable'
const tableCols = [
    { istrue: true, permission: 'shootTaskId', prop: 'shootingTaskId', label: '编号', width: '50', fixed: 'left' },
    { istrue: true, permission: 'shootProudctName', prop: 'productShortName', label: '产品简称', width: '135', fixed: 'left', type: "click", handle: (that, row) => that.openComputOutInfo(row) },
    { istrue: true, type: "urgencyediticon", width: "38", fixed: 'left', label: '' },
    {
        istrue: true, permission: 'shootUrgency', prop: 'taskUrgencyName', label: '紧急程度', width: '80', fixed: 'left', type: 'UrgencyButton'
        , handle: (that, row) => that.shootUrgencyCilck(row.taskUrgencyName, row.shootingTaskId)
    },
    { istrue: true, permission: 'shootWarehouse', prop: 'warehouseStr', label: '大货仓', width: '100', fixed: 'left' },
    { istrue: true, permission: 'shootRemarks', prop: 'beizhu', label: '', width: '38', type: "clickflag", fixed: 'left', handle: (that, row) => that.openTaskRmarkInfo(row) },
    { istrue: true, permission: 'shootReference', prop: 'cankao', type: "fileicon", width: "38", label: '', fixed: 'left', handle: (that, row) => that.videotaskuploadfileDetal(row) },
    { istrue: true, permission: "api:media:shootingvideo:AddOrUpdateShootingVideoTaskAsync", prop: 'caozoulie', type: "editicon", fixed: 'left', width: "40", label: '', handle: (that, row) => that.editTask(row) },
    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)' },
    //照片
    { istrue: true, summaryEvent: true, permission: 'shootPhoto', prop: 'photoLqNameStr', label: '照片', width: '65' },
    { istrue: true, permission: 'shootDays', type: "time", prop: 'photoDaysStr', label: '天数', width: '53', },
    { istrue: true, permission: 'shootCompleteDate', type: "time", prop: 'photoOverTimeStr', width: '75', label: '完成日期' },
    { istrue: true, type: "time", prop: 'scoreType1', width: '75', label: '质量评分' },
    { istrue: true, type: "time", prop: 'scoreType11', width: '75', label: '配合度评分' },

    //视频
    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)', label: '' },
    { istrue: true, summaryEvent: true, permission: 'shootVideo', prop: 'vedioLqNameStr', label: '视频', width: "65", },
    { istrue: true, permission: 'shootDays', type: "time", prop: 'vedioDaysStr', label: '天数', width: '53' },
    { istrue: true, permission: 'shootCompleteDate', type: "time", prop: 'vedioOverTimeStr', label: '完成日期', width: '75', },
    { istrue: true, type: "time", prop: 'scoreType2', width: '75', label: '质量评分' },
    { istrue: true, type: "time", prop: 'scoreType12', width: '75', label: '配合度评分' },
    { istrue: true, permission: 'shootConfirmationPersion', prop: 'vedioConfirmNameStr', label: '确认人', width: "65", },
    { istrue: true, permission: 'shootConfirmationDate', type: "time", prop: 'vedioConfirmTimeStr', label: '确认日期', width: '75', },
    //微详情
    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)', label: '' },
    { istrue: true, summaryEvent: true, permission: 'shootMicrodetails', prop: 'microDetailLqNameStr', label: '微详情', width: "65", },
    { istrue: true, permission: 'shootDays', type: "time", prop: 'microDetailDaysStr', label: '天数', width: '53', },
    { istrue: true, permission: 'shootCompleteDate', type: "time", prop: 'microDetailOverTimeStr', width: '75', label: '完成日期' },
    { istrue: true, type: "time", prop: 'scoreType3', width: '75', label: '质量评分' },
    { istrue: true, type: "time", prop: 'scoreType13', width: '75', label: '配合度评分' },
    { istrue: true, permission: 'shootQuantity', prop: 'microDetailVedioCounts', width: '54', label: '数量' },
    { istrue: true, permission: 'shootConfirmationPersion', prop: 'microDetailConfirmNameStr', label: '确认人', width: "65", },
    { istrue: true, permission: 'shootConfirmationDate', type: "time", prop: 'microDetailConfirmTimeStr', label: '确认日期', width: '75', },
    //详情页
    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)', label: '' },
    { istrue: true, summaryEvent: true, permission: 'shootDetailsPage', prop: 'detailLqNameStr', label: '详情页', width: "65", },
    { istrue: true, permission: 'shootDays', prop: 'detailDaysStr', type: "time", label: '天数', width: '53', },
    { istrue: true, permission: 'shootCompleteDate', prop: 'detailOverTimeStr', type: "time", width: '75', label: '完成日期', },
    { istrue: true, type: "time", prop: 'scoreType4', width: '75', label: '质量评分' },
    { istrue: true, type: "time", prop: 'scoreType14', width: '75', label: '配合度评分' },
    { istrue: true, permission: 'shootConfirmationPersion', prop: 'detailConfirmNameStr', label: '确认人', width: "65", },
    { istrue: true, permission: 'shootConfirmationDate', type: "time", prop: 'detailConfirmTimeStr', label: '确认日期', width: '75', },

    //照片建模
    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)', label: '' },
    { istrue: true, summaryEvent: true, permission: 'shootPhotoModeling', prop: 'modelPhotosLqNameStr', width: "75", label: '照片建模' },
    { istrue: true, permission: 'shootDays', prop: 'modelPhotosDaysStr', type: "time", label: '天数', width: '53', },
    { istrue: true, permission: 'shootCompleteDate', prop: 'modelPhotosOverTimeStr', type: "time", width: '75', label: '完成日期' },
    { istrue: true, type: "time", prop: 'scoreType5', width: '75', label: '质量评分' },
    { istrue: true, type: "time", prop: 'scoreType15', width: '75', label: '配合度评分' },
    { istrue: true, permission: 'shootSheets', prop: 'modelPhotoCounts', width: '53', label: '张数' },


    //视频建模
    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)', label: '' },
    { istrue: true, summaryEvent: true, permission: 'shootVideoModeling', prop: 'modelVideoLqNameStr', width: "75", label: '视频建模' },
    { istrue: true, permission: 'shootDays', prop: 'modelVideoDaysStr', type: "time", label: '天数', width: '53' },
    { istrue: true, permission: 'shootCompleteDate', prop: 'modelVideoOverTimeStr', type: "time", width: '80', label: '完成日期' },
    { istrue: true, type: "time", prop: 'scoreType6', width: '75', label: '质量评分' },
    { istrue: true, type: "time", prop: 'scoreType16', width: '75', label: '配合度评分' },
    { istrue: true, permission: 'shootGs', prop: 'modelVedioCounts', width: '53', label: '个数' },

    //分配
    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)', label: '' },
    { istrue: true, summaryEvent: true, permission: 'shootFpPhoto', prop: 'fpPhotoLqNameStr', width: '75', label: '分配照片' },
    { istrue: true, summaryEvent: true, permission: 'shootFpVedio', prop: 'fpVideoLqNameStr', width: '75', label: '分配视频' },
    { istrue: true, summaryEvent: true, permission: 'shootFpDetail', prop: 'fpDetailLqNameStr', width: '75', label: '分配详情' },
    { istrue: true, summaryEvent: true, permission: 'shootFpModel', prop: 'fpModelLqNameStr', width: '75', label: '分配建模' },
    //
    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)', label: '' },
    { istrue: true, permission: 'shootOperate', prop: 'operationGroupstr', align: 'left', width: '80', label: '运营小组' },
    { istrue: true, permission: 'shootCounter', prop: 'dockingPeople', align: 'left', width: '65', label: '对接人' },
    { istrue: true, permission: 'shootPlatform', prop: 'platformStr', align: 'left', width: '80', label: '平台' },
    { istrue: true, permission: 'shootShop', prop: 'shopNameStr', align: 'left', width: '180', label: '店铺' },

    { istrue: true, prop: 'productID', align: 'left', width: '80', label: '产品ID' },
    { istrue: true, summaryEvent: true, permission: 'shootTaskOverTime', prop: 'taskOverTimeStr', width: '100', label: '完成时间' },
    { istrue: true, prop: 'confirmTimeStr', align: 'left', width: '100', label: '确认时间' },

    { istrue: true, permission: 'shootArrivalTime', prop: 'arrivalTimeStr', width: '75', label: '到货日期' },
    { istrue: true, permission: 'shootArrivalDays', prop: 'arrivalTimeDays', width: '53', label: '到货天数' },
    { istrue: true, permission: 'shootDeliverTime', prop: 'deliverTimeStr', width: '75', label: '发货日期' },
    { istrue: true, permission: 'shootDeliverDays', prop: 'deliverTimeDays', width: '53', label: '发货天数' },
    { istrue: true, permission: 'shootApplicationTime', prop: 'applyTimeStr', width: '75', label: '申请日期' },
    { istrue: true, permission: 'shootApplicationDay', prop: 'applyTimeDays', width: '53', label: '申请天数' },
    { istrue: true, permission: 'shootCreationDate', prop: 'createdTime', width: '90', label: '创建日期', sortable: 'custom', formatter: (row) => row.createdTimeStr },

    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)', label: '' },
    { istrue: true, permission: 'shootorderNo', prop: 'orderNoInner', width: '100', label: '内部单号', type: 'click', handle: (that, row, column) => that.onShowOrderDtl(row) },
    { istrue: true, permission: 'shootorderExpressNumber', prop: 'expressNo', width: '135', label: '快递单号', type: 'click', handle: (that, row, column) => that.onShowExproessHttp(row) },
    { istrue: true, permission: 'shootorderNo', prop: 'shootOrderTrack', width: '80', label: '拿样跟踪', type: 'click', handle: (that, row, column) => that.onShowOrderDtl(row) },
];
const xdfhmainTableCols = [
    { istrue: true, prop: 'shootingTaskId', label: '当前任务', width: '80' },
    { istrue: true, prop: 'shootingTaskIds', label: '涉及任务', width: '100' },
    { istrue: true, prop: 'shootingTaskOrderId', label: '下单号', width: '70' },
    { istrue: true, prop: 'createdUserName', label: '下单人', width: '70' },
    { istrue: true, prop: 'receiverName', label: '收货人', width: '70' },
    { istrue: true, prop: 'receiverPhone', label: '收货电话', width: '80' },
    { istrue: true, prop: 'receiverState', label: '收货省', width: '70' },
    { istrue: true, prop: 'receiverCity', label: '收货市', width: '80' },
    { istrue: true, prop: 'receiverDistrict', label: '收货区', width: '80' },
    { istrue: true, prop: 'receiverAddress', label: '收货地址' },
    { istrue: true, prop: 'sampleRrderNo', label: '聚水潭内部单号', width: '120', type: 'click', handle: (that, row) => that.showLogDetail(row) },
    { istrue: true, prop: 'sampleExpressNo', label: '快递单号', width: '120', type: 'click', handle: (that, row, column) => that.onShowExproessHttp(row) },
    { istrue: true, prop: 'sampleExpressCom', label: '物流公司', width: '80' },
    { istrue: true, prop: 'arrivalDate', label: '到货日期', width: '100', formatter: (row) => row.arrivalDate == null ? null : formatTime(row.arrivalDate, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'isDy', label: '是否到样', width: '80', formatter: (row) => row.isDy == 1 ? "是" : "否" },
    { istrue: true, prop: 'isZt', label: '是否自提', width: '80', formatter: (row) => row.isZt == 1 ? "是" : "否" },
    { istrue: true, prop: 'approveStateName', label: '状态', width: '80' },
    { istrue: true, prop: 'createdTime', label: '创建时间', width: '150', formatter: (row) => row.createdTime == null ? null : formatTime(row.createdTime, 'YYYY-MM-DD HH:mm:ss') },
];
const xdfhdtlTableCols = [
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '200' },
    { istrue: true, prop: 'goodsName', label: '商品名称' },
    { istrue: true, prop: 'goodsPrice', label: '单价', width: '120', display: false },
    { istrue: true, prop: 'goodsQty', label: '数量', width: '120' },
    { istrue: true, prop: 'goodsAmount', label: '总额', width: '120', display: false },
];
const addressTableCols = [
    { istrue: true, prop: 'receiverAllAddress', label: '地址' },
    { istrue: true, prop: 'receiverName', label: '收货人', width: '120' },
    { istrue: true, prop: 'receiverPhone', label: '收货电话', width: '120' },
    {
        istrue: true, type: 'button', label: '操作', width: '60',
        btnList: [
            { label: "删除", handle: (that, row) => that.onAddressDelete(row) }
        ]
    }
];
export default {
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, shootingchartforfp
        , vxetablebase, cesTable, goodschoice, logistics, uploadfile, shootinguploadaction, shootingTaskRemark
        , shootingvideotaskuploadfile, shootingvideotaskuploadsuccessfilesocre, shootingvideotaskeditfrom, orderLogPage, shootingvideotaskTable
    },
    inject: ['getUserListRelod', 'getwarehouseListRelod', 'getShootingViewPersonRelod'],
    props: ["tablekey", 'role'],
    watch: {
    },
    data() {
        return {
            platform: null,
            taskname: '',
            inputshow: true,
            selshootingTaskId: 0,
            taskUrgencyStatus: "1",
            taskUrgencyAproved: false,
            Oncommand: 'a',
            shootingTaskRemarkrawer: false,
            viewReferenceRemark: false,
            shootingvediotask: 'shootingvediotask',
            opentime: new Date().getTime(),
            tjopentime: new Date().getTime() + 1,
            urgencyopentime: new Date().getTime() + 2,
            markopentime: new Date().getTime() + 3,
            fileopentime: new Date().getTime() + 4,
            outsueccessopentime: new Date().getTime() + 5,
            outopentime: new Date().getTime() + 6,
            editopentime: new Date().getTime() + 7,
            outconfilekey: null,
            fpcharttype: null,
            //分配趋势图
            shootingchartforfpvisible: false,
            //上传成果文件
            successfiledrawer: false,
            filesocreLoading: false,
            successfileshow: false,
            //查看参考
            viewReference: false,
            //选中的行
            selectRowKey: null,
            that: this,
            pageLoading: false,
            islook: true,
            filter: {
                isShop: 0,
                isdel: 0,
                isComplate: 1,
                shopName: null,
                operationGroup: null,
                createdtimerange: [],
                searchTimeType: "1",
                fpPhotoLqName: [],
                isTopOld: null,
                warehouse: [],
            },
            tasklist: [],
            taskPageTitle: "创建任务",
            referenceVideoList: [],
            multipleSelection: [],
            addLoading: false,
            formatWarehouse: formatWarehouse,
            warehouselist: [],
            shopList: [],
            userList: [],
            groupList: [],
            fpDetailLqNameList: [],
            fpModelLqNameList: [],
            fpPhotoLqNameList: [],
            fpVideoLqNameList: [],
            dockingPeopleList: [],
            taskUrgencyList: ShootingVideoTaskUrgencyOptions,
            platformList: [],
            tableCols: tableCols,
            total: 0,
            //选中的行id
            selids: [],
            taskPhotofileList: [],
            taskExeclfileList: [],
            addTask: false,
            editTaskshow: false,
            loginfo: null,
            summaryarry: {},
            pager: { OrderBy: "shootingTaskId", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            //下单发货
            orderGoodschoiceVisible: false,//选择下单任务商品窗口
            receiverStateList: [],//省
            receiverCityList: [],//市
            receiverDistrictList: [],//区
            receiverCityList2: [],//市
            receiverDistrictList2: [],//区
            dialogAddOrderVisible: false,
            dialogAddOrderLoading: false,
            dialogAddOrderSubmitLoding: false,
            addOrderForm: {
                shootingTaskIds: "",
                receiverName: "",
                receiverPhone: "",
                receiverStateCode: "",
                receiverCityCode: "",
                receiverDistrictCode: "",
                receiverState: "",
                receiverCity: "",
                receiverDistrict: "",
                receiverAddress: "",
                isZt: null,
                warehouse: null,
                remark: "",
                receiverAddressAllInfo: "",
                orderGoods: [],
            },
            selShootingTaskIdSpanList: [],
            orderGoodschoiceVisible: false,
            addOrderFormRules: {
                //receiverName: [{ required: true, message: '请输入收货人', trigger: 'blur' }],
                //receiverPhone: [{ required: true, message: '请输入收货电话', trigger: 'blur' }],
                isZt: [{ required: true, message: '请输入是否自提', trigger: 'blur' }],
                calculateUnit: [{ required: true, message: '请选择核算单位', trigger: 'blur' }],
                //receiverStateCode: [{ required: true, message: '请输入收货省', trigger: 'blur' }],
                //receiverCityCode: [{ required: true, message: '请输入收货市', trigger: 'blur' }],
                //receiverDistrictCode: [{ required: true, message: '请输入成收货区', trigger: 'blur' }],
                //receiverAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
                //receiverAddressAllInfo: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
            },
            dialogOrderDtlVisible: false,
            dialogOrderDtlLoading: false,

            xdfhmainlist: [],
            xdfhmainTableCols: xdfhmainTableCols,
            xdfhmainLoading: false,

            xdfhdtllist: [],
            xdfhdtlTableCols: xdfhdtlTableCols,
            xdfhdtlLoading: false,

            receiverAddressList: [],
            dialogAddressVisible: false,
            addressTableCols: addressTableCols,
            addressListLoading: false,
            dialogAddAddressSubmitLoding: false,
            addAddressForm: {
                receiverName: "",
                receiverPhone: "",
                receiverStateCode: null,
                receiverCityCode: null,
                receiverDistrictCode: null,
                receiverAddress: "",
            },
            calculateUnitlist: ['国内', '1688选品中心' , '跨境'],
            addressList: [],
            addAddressFormRules: {
                receiverName: [{ required: true, message: '请输入收货人', trigger: 'blur' }],
                receiverPhone: [{ required: true, message: '请输入收货电话', trigger: 'blur' }],
                receiverStateCode: [{ required: true, message: '请输入收货省', trigger: 'blur' }],
                receiverCityCode: [{ required: true, message: '请输入收货市', trigger: 'blur' }],
                receiverDistrictCode: [{ required: true, message: '请输入成收货区', trigger: 'blur' }],
                receiverAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
            },
            drawervisible: false,
            sendOrderNoInner: "",
            dialogHisVisible: false,
        };
    },
    watch: {
    },
    async created() {

    },
    async mounted() {

    },
    methods: {
        async initSearch() {
            await this.onSearch();
            if(checkPermission(['shootingDropDownList']))
            {
                this.Oncommand = "b";
                await this.ShowHideonSearch();
            }else if (this.role == "b") {
                this.Oncommand = "b";
                await this.ShowHideonSearch();
            }
            await this.onGetdrowList();
            await this.onGetdrowList2();
            await this.getShootingViewPer();
            //省
            await this.getCity(0, 1);
            await this.getAddressList();
        },
        //成果文件提交
        async onSubComputOutInfo() {
            this.filesocreLoading = true;
            await this.$refs.shootingvideotaskuploadsuccessfilesocre.onSubComputOutInfo();
            this.filesocreLoading = false;
        },
        onclear() {

            this.filter.isTopOld = '';
            this.filter.productShortName = '';
            this.filter.shootingTaskId = null;
            this.filter.hasOverTime = '';
            this.filter.hasConfirmTime = '';
            this.filter.warehouse = [];
            this.filter.createdtimerange = [];
            this.filter.searchTimeType = '1';
            this.filter.shopName = null;
            this.filter.platform = '';
            this.filter.dockingPeople = '';
            this.filter.operationGroup = null;
            this.filter.fpPhotoLqName = [];
        },
        inputshowfunc() {
            this.inputshow = false;
        },
        toResultmatter(row) {
            if (row != null) {
                let routeUrl = this.$router.resolve({
                    path: '/resultmatter',
                    query: { id: row.shootingTaskId }
                });
                window.open(routeUrl.href, '_blank');
            }
        },
        async onsummaryClick(property) {
            /*   this.fpcharttype = property;
              this.$nextTick(function () {
                  this.$refs.shootingchartforfp.showviewMain();
              });
              this.shootingchartforfpvisible = true; */
        },
        async onExeprotShootingTask() {
            var pager = this.$refs.pager.getPager();
            pager.CurrentPage =1;
            pager.pageSize = 20000;
            if (this.filter.createdtimerange) {
                this.filter.createdstartTime = this.filter.createdtimerange[0];
                this.filter.createdendTime = this.filter.createdtimerange[1];
            } else {
                this.filter.createdstartTime = null;
                this.filter.createdendTime = null;
            }
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            params.platform.length>0?params.platform:params.platform=""
            this.listLoading = true;
            const res = await pageShootingViewTaskAsync(params);
            this.listLoading = false;
            this.ShowHideonSearchExport();
            this.$refs.shootingvideotaskoverNew.exportData("新品拍摄-完成导出",res.data.list)
            this.ShowHideonSearch();
        },
        async handleCommand(command) {
            if (this.selids.length == 0 && command != 'x') {
                this.$message({ type: 'warning', message: "请选择任务" });
                return;
            }
            switch (command) {
                //批量完成
                case 'a':
                    await this.onTaskOverActionShared(this.selids)
                    break;


                //批量重启
                case 'b':
                    await this.onEndRestartActionAsyncShared(this.selids);
                    break;
                //批量终止
                case 'c':
                    await this.OnendShootingTaskAction(this.selids)
                    break;
                //批量标记
                case 'd':
                    await this.onSignShootingTaskActionShared(this.selids)
                    break;
                //取消标记
                case 'f':
                    await this.onUnSignShootingTaskActionShared(this.selids)
                    break;
                //批量删除
                case 'e':
                    await this.deleteTaskActionShared(this.selids)
                    break;
                // 批量统计
                case 'g':
                    await this.caclShootingTaskActionAsyncShared(this.selids)
                    break;
                //任务批量重启
                case 'h':
                    await this.taskRestartActionAsyncShared(this.selids)
                    break;
            }
        },
        //任务批量重启
        async taskRestartActionAsyncShared(array) {
            this.$confirm("选中的任务将重启，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await taskRestartActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.selids = [];
                    await this.onRefresh();
                }
            });
        },
        //批量统计
        async caclShootingTaskActionAsyncShared(array) {
            this.$confirm("选中的任务将进行统计，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await caclShootingTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.selids = [];
                    await this.onRefresh();
                }
            });
        },
        //完成操作公共
        async onTaskOverActionShared(array) {
            this.$confirm("选中的任务移动完成列表，是否确定完成", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await taskOverActionsAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.selids = [];
                    await this.onRefresh();
                }
            });
        },
        //存档操作
        async onTaskShopActionShared(array) {
            this.$confirm("选中的任务移动到存档列表，是否确定存档", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await taskShopActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //重新启动
        async onEndRestartActionAsyncShared(array) {
            this.$confirm("选中的任务将会移动至任务列表，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await EndRestartActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //批量标记
        async onSignShootingTaskActionShared(array) {
            this.$confirm("选中的任务将会进行标记，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await signShootingTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //取消标记
        async onUnSignShootingTaskActionShared(array) {
            this.$confirm("选中的任务将会取消标记，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await unSignShootingTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },

        //回收站彻底删除操作
        async deleteTaskActionShared(array) {
            this.$confirm("选中的任务会彻底删除，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await deleteTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //批量终止
        async OnendShootingTaskAction(array) {
            this.$confirm("选中任务将会终止，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await endShootingTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //获取下拉数据
        async onGetdrowList() {
            var pfrule = await rulePlatform();
            this.platformList = pfrule.options;
            var res = await getDirectorGroupList();
            this.groupList = res.data?.map(item => { return { value: item.key, label: item.value }; });
        },
        async onGetdrowList2() {
            var res = await this.getwarehouseListRelod();
            this.warehouselist = res?.map(item => { return { value: item.id, label: item.label }; });
        },
        async onchangeplatform(val) {
            var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100 });
            this.filter.shopName = "";
            this.shopList = res1.data.list;
        },
        //获取分配人下拉，对接人下啦
        async getShootingViewPer() {
            var res = await this.getShootingViewPersonRelod();
            if (res) {
                this.dockingPeopleList = res.dockingPeopleList;
                this.fpPhotoLqNameList = res.fpallList;
            }
        },
        async ShowHideonSearch() {
            this.listLoading = true;
            var checkedColumnsFora = [];
            switch (this.Oncommand) {
                //显示全部 ,部门经理，超管
                case "a":
                    // checkedColumnsFora = ['scoreType1','scoreType2','scoreType3','scoreType4','scoreType5','scoreType6'
                    // ,"addTaskTimeStr1" ,"addTaskTimeStr2" ,"addTaskTimeStr3" ,"addTaskTimeStr4" ,"addTaskTimeStr5","addTaskTimeStr6","addTaskTimeStr","degreeUpTimeStr"];
                  //照片评分，视频评分，微详情评分,详情页评分，照片建模评分，视频建模评分

                    // 显示所有

                    break;
                //显示默认
                case "b":
                    checkedColumnsFora =

                        ['warehouseStr'
                            , 'photoDaysStr', 'photoOverTimeStr', 'isAssess'
                            , 'vedioDaysStr', 'vedioOverTimeStr', 'vedioConfirmTimeStr'
                            , 'microDetailDaysStr', 'microDetailOverTimeStr', 'microDetailVedioCounts'
                            , 'microDetailConfirmTimeStr'
                            , 'detailDaysStr', 'detailOverTimeStr', 'detailConfirmTimeStr','modelPhotosConfirmNameStr' , 'Divisionline3'
                            , 'modelPhotosDaysStr', 'modelPhotosOverTimeStr', 'modelPhotoCounts'
                            , 'modelVideoDaysStr', 'modelVideoOverTimeStr', 'modelVedioCounts'
                            // , 'operationGroupstr', 'dockingPeople', 'platformStr', 'shopNameStr'
                            , 'arrivalTimeDays', 'deliverTimeStr', 'deliverTimeDays', 'scoreType1','scoreType2','scoreType3','scoreType4','scoreType5','scoreType6'
                            , 'scoreType11','scoreType12','scoreType13','scoreType14','scoreType15','scoreType16'
                            ,'photoConfirmNameStr','photoConfirmTimeStr'
                            ,'modelPhotosConfirmTimeStr','modelVideoConfirmTimeStr','modelVideoConfirmNameStr'
                            ,'makeUpDatetime1','makeUpDay1','makeUpDatetime2','makeUpDay2','makeUpDatetime3','makeUpDay3','makeUpDay4'
                            ,'makeUpDatetime4','makeUpDatetime5','makeUpDay5','makeUpDatetime6','makeUpDay6'
                            ,"bhDate1","bhDate2","bhDate3","bhDate4","bhDate5","bhDate6"
                            ,"bcDate1","bcDate2","bcDate3","bcDate4","bcDate5","bcDate6"
                            , 'fpPhotoLqNameStr', 'fpVideoLqNameStr', 'fpDetailLqNameStr', 'fpModelLqNameStr'
                            , 'applyTimeDays', 'applyTimeStr'
                            ,"addTaskTimeStr1" ,"addTaskTimeStr2" ,"addTaskTimeStr3" ,"addTaskTimeStr4" ,"addTaskTimeStr5","addTaskTimeStr6","degreeUpTimeStr"
                            ,'orderNoInner', 'expressNo', 'shootOrderTrack','Divisionline4'
                        ];

                            // 排除
                            // 分割线，操作
                            // ，照片天数，照片完成日期
                            // ，视频天数，视频完成日期，视频确认人，视频确认日期，微。视频天数，微。视频完成日期
                            // ，微。视频数量，微。视频确认人，微。视频确认日期，详情页天数，详情页完成日期，详情页确认人，详情页确认日期
                            // ，建模照片天数，建模照片完成日期，建模照片张数，建模视频天数，建模视频完成日期，建模视频个数
                            // ，产品ID
                            // ，运营小组，对接人，平台，店铺
                            // ，到货天数，发货日期，发货天数，照片评分，视频评分，微详情评分,详情页评分，照片建模评分,视频建模评分

                    break;
                     //时效
                case "c":
                  checkedColumnsFora = ['warehouseStr', 'cankao', 'isAssess', 'allScoreAvg'
                    , 'vedioConfirmTimeStr'
                    , 'microDetailVedioCounts',
                    , 'microDetailConfirmTimeStr'
                    , 'detailConfirmTimeStr'
                    , 'modelPhotoCounts'
                    , 'modelVedioCounts', 'bcDate6'
                    , 'vedioConfirmNameStr', 'microDetailConfirmNameStr', 'detailConfirmNameStr'
                    , 'platformStr', 'shopNameStr'
                    , 'arrivalTimeDays', 'deliverTimeStr', 'deliverTimeDays', 'scoreType1', 'scoreType2', 'scoreType3', 'scoreType4', 'scoreType5', 'scoreType6'
                    , 'scoreType11', 'scoreType12', 'scoreType13', 'scoreType14', 'scoreType15', 'scoreType16'
                    , 'makeUpDatetime1', 'makeUpDatetime2', 'makeUpDatetime3', 'makeUpDatetime4', 'makeUpDatetime5', 'makeUpDatetime6'
                    , 'makeUpDay1', 'makeUpDay2', 'makeUpDay3', 'makeUpDay4', 'makeUpDay5', 'makeUpDay6'
                    , 'photoConfirmNameStr', 'photoConfirmTimeStr', 'modelPhotosConfirmNameStr'
                    , 'modelPhotosConfirmTimeStr', 'modelVideoConfirmTimeStr', 'modelVideoConfirmNameStr'
                    , "bhDate1", "bhDate2", "bhDate3", "bhDate4", "bhDate5", "bhDate6"
                    , "bcDate1", "bcDate2", "bcDate3", "bcDate4", "bcDate5", "bDate6"
                    , "addTaskTimeStr1", "addTaskTimeStr2", "addTaskTimeStr3", "addTaskTimeStr4", "addTaskTimeStr5", "addTaskTimeStr6", "addTaskTimeStr", "degreeUpTimeStr"
                    , 'fpPhotoLqNameStr', 'fpVideoLqNameStr', 'fpDetailLqNameStr', 'fpModelLqNameStr'
                    , 'productID', 'arrivalTimeStr', 'applyTimeDays', 'applyTimeStr'
                    , 'orderNoInner', 'expressNo', 'shootOrderTrack', 'Divisionline3','Divisionline4'];
                  break;
                //评分
                case "d":
                  checkedColumnsFora = ['warehouseStr', 'cankao',, 'isAssess',
                    , 'vedioConfirmTimeStr'
                    , 'photoOverTimeStr', 'vedioOverTimeStr', 'microDetailOverTimeStr', 'detailOverTimeStr', 'modelPhotosOverTimeStr', 'modelVideoOverTimeStr'
                    , 'photoDaysStr', 'vedioDaysStr', 'microDetailDaysStr', 'detailDaysStr', 'modelPhotosDaysStr', 'modelVideoDaysStr',
                    , 'microDetailVedioCounts',
                    , 'microDetailConfirmTimeStr'
                    , 'detailConfirmTimeStr'
                    , 'modelPhotoCounts', 'actualCompleteDay'
                    , 'modelVedioCounts', 'bcDate6'
                    , 'vedioConfirmNameStr', 'microDetailConfirmNameStr', 'detailConfirmNameStr'
                    , 'platformStr', 'shopNameStr'
                    , 'arrivalTimeDays', 'deliverTimeStr', 'deliverTimeDays',
                    , 'makeUpDatetime1', 'makeUpDatetime2', 'makeUpDatetime3', 'makeUpDatetime4', 'makeUpDatetime5', 'makeUpDatetime6'
                    , 'makeUpDay1', 'makeUpDay2', 'makeUpDay3', 'makeUpDay4', 'makeUpDay5', 'makeUpDay6'
                    , 'photoConfirmNameStr', 'photoConfirmTimeStr', 'modelPhotosConfirmNameStr'
                    , 'modelPhotosConfirmTimeStr', 'modelVideoConfirmTimeStr', 'modelVideoConfirmNameStr'
                    , "bhDate1", "bhDate2", "bhDate3", "bhDate4", "bhDate5", "bhDate6"
                    , "bcDate1", "bcDate2", "bcDate3", "bcDate4", "bcDate5", "bDate6"
                    , "addTaskTimeStr1", "addTaskTimeStr2", "addTaskTimeStr3", "addTaskTimeStr4", "addTaskTimeStr5", "addTaskTimeStr6", "addTaskTimeStr", "degreeUpTimeStr"
                    , 'fpPhotoLqNameStr', 'fpVideoLqNameStr', 'fpDetailLqNameStr', 'fpModelLqNameStr'
                    , 'productID', 'arrivalTimeStr', 'applyTimeDays', 'applyTimeStr'
                    , 'orderNoInner', 'expressNo', 'shootOrderTrack', 'Divisionline3','Divisionline4'];
                  break;
                //补改
                case "e":
                  checkedColumnsFora = ['warehouseStr', 'cankao', 'isAssess', 'allScoreAvg'
                    , 'vedioConfirmTimeStr'
                    , 'microDetailVedioCounts',
                    , 'microDetailConfirmTimeStr'
                    , 'detailConfirmTimeStr'
                    , 'modelPhotoCounts', 'actualCompleteDay'
                    , 'modelVedioCounts',
                    , 'vedioConfirmNameStr', 'microDetailConfirmNameStr', 'detailConfirmNameStr'
                    , 'photoDaysStr', 'vedioDaysStr', 'microDetailDaysStr', 'detailDaysStr', 'modelPhotosDaysStr', 'modelVideoDaysStr',
                    , 'platformStr', 'shopNameStr'
                    , 'arrivalTimeDays', 'deliverTimeStr', 'deliverTimeDays',
                    , 'scoreType1', 'scoreType2', 'scoreType3', 'scoreType4', 'scoreType5', 'scoreType6'
                    , 'scoreType11', 'scoreType12', 'scoreType13', 'scoreType14', 'scoreType15', 'scoreType16'
                    , 'photoConfirmNameStr', 'photoConfirmTimeStr', 'modelPhotosConfirmNameStr'
                    , 'modelPhotosConfirmTimeStr', 'modelVideoConfirmTimeStr', 'modelVideoConfirmNameStr'
                    , "addTaskTimeStr1", "addTaskTimeStr2", "addTaskTimeStr3", "addTaskTimeStr4", "addTaskTimeStr5", "addTaskTimeStr6", "addTaskTimeStr", "degreeUpTimeStr"
                    , 'fpPhotoLqNameStr', 'fpVideoLqNameStr', 'fpDetailLqNameStr', 'fpModelLqNameStr'
                    , 'productID', 'arrivalTimeStr', 'applyTimeDays', 'applyTimeStr'
                    , 'orderNoInner', 'expressNo', 'shootOrderTrack', 'Divisionline3','Divisionline4'];
                  break;

                default:
                    break;
            }
            await this.$refs.shootingvideotaskoverNew.ShowHidenColums(checkedColumnsFora);
            this.listLoading = false;
        },
        async ShowHideonSearchExport() {
            this.listLoading = true;
            var checkedColumnsFora = [];
            switch (this.Oncommand) {
                //显示全部 ,部门经理，超管
                case "a":
                    checkedColumnsFora = ['scoreType1','scoreType2','scoreType3','scoreType4','scoreType5','scoreType6'];
                  //照片评分，视频评分，微详情评分,详情页评分，照片建模评分，视频建模评分

                    // 显示所有

                    break;
                //显示默认
                case "b":
                    checkedColumnsFora =

                        ['warehouseStr'
                            , 'photoDaysStr', 'photoOverTimeStr'
                            , 'vedioDaysStr', 'vedioOverTimeStr', 'vedioConfirmTimeStr'
                            , 'microDetailDaysStr', 'microDetailOverTimeStr', 'microDetailVedioCounts'
                            , 'microDetailConfirmTimeStr'
                            , 'detailDaysStr', 'detailOverTimeStr', 'detailConfirmTimeStr','modelPhotosConfirmNameStr' , 'styleName', 'Divisionline3'
                            , 'modelPhotosDaysStr', 'modelPhotosOverTimeStr', 'modelPhotoCounts'
                            , 'modelVideoDaysStr', 'modelVideoOverTimeStr', 'modelVedioCounts'
                            // , 'operationGroupstr', 'dockingPeople', 'platformStr', 'shopNameStr'
                            , 'arrivalTimeDays', 'deliverTimeStr', 'deliverTimeDays', 'scoreType1','scoreType2','scoreType3','scoreType4','scoreType5','scoreType6'
                            , 'scoreType11','scoreType12','scoreType13','scoreType14','scoreType15','scoreType16'
                            ,'photoConfirmNameStr','photoConfirmTimeStr', 'actualCompleteDay'
                            ,'modelPhotosConfirmTimeStr','modelVideoConfirmTimeStr','modelVideoConfirmNameStr'
                            ,'makeUpDatetime1','makeUpDay1','makeUpDatetime2','makeUpDay2','makeUpDatetime3','makeUpDay3','makeUpDay4'
                            ,'makeUpDatetime4','makeUpDatetime5','makeUpDay5','makeUpDatetime6','makeUpDay6'
                            ,"bhDate1","bhDate2","bhDate3","bhDate4","bhDate5","bhDate6"
                            ,"bcDate1","bcDate2","bcDate3","bcDate4","bcDate5","bcDate6"
                            , 'fpPhotoLqNameStr', 'fpVideoLqNameStr', 'fpDetailLqNameStr', 'fpModelLqNameStr'
                            , 'applyTimeDays', 'applyTimeStr'
                            ,"addTaskTimeStr1" ,"addTaskTimeStr2" ,"addTaskTimeStr3" ,"addTaskTimeStr4" ,"addTaskTimeStr5","addTaskTimeStr6","degreeUpTimeStr"
                            ,'orderNoInner', 'expressNo', 'shootOrderTrack','Divisionline4'
                        ];

                            // 排除
                            // 分割线，操作
                            // ，照片天数，照片完成日期
                            // ，视频天数，视频完成日期，视频确认人，视频确认日期，微。视频天数，微。视频完成日期
                            // ，微。视频数量，微。视频确认人，微。视频确认日期，详情页天数，详情页完成日期，详情页确认人，详情页确认日期
                            // ，建模照片天数，建模照片完成日期，建模照片张数，建模视频天数，建模视频完成日期，建模视频个数
                            // ，产品ID
                            // ，运营小组，对接人，平台，店铺
                            // ，到货天数，发货日期，发货天数，照片评分，视频评分，微详情评分,详情页评分，照片建模评分,视频建模评分

                    break;
                     //时效
                case "c":
                  checkedColumnsFora = ['warehouseStr', 'cankao'
                    , 'vedioConfirmTimeStr'
                    , 'microDetailVedioCounts',
                    , 'microDetailConfirmTimeStr'
                    , 'detailConfirmTimeStr', 'styleName'
                    , 'modelPhotoCounts'
                    , 'modelVedioCounts', 'bcDate6'
                    , 'vedioConfirmNameStr', 'microDetailConfirmNameStr', 'detailConfirmNameStr'
                    , 'platformStr', 'shopNameStr'
                    , 'arrivalTimeDays', 'deliverTimeStr', 'deliverTimeDays', 'scoreType1', 'scoreType2', 'scoreType3', 'scoreType4', 'scoreType5', 'scoreType6'
                    , 'scoreType11', 'scoreType12', 'scoreType13', 'scoreType14', 'scoreType15', 'scoreType16'
                    , 'makeUpDatetime1', 'makeUpDatetime2', 'makeUpDatetime3', 'makeUpDatetime4', 'makeUpDatetime5', 'makeUpDatetime6'
                    , 'makeUpDay1', 'makeUpDay2', 'makeUpDay3', 'makeUpDay4', 'makeUpDay5', 'makeUpDay6'
                    , 'photoConfirmNameStr', 'photoConfirmTimeStr', 'modelPhotosConfirmNameStr'
                    , 'modelPhotosConfirmTimeStr', 'modelVideoConfirmTimeStr', 'modelVideoConfirmNameStr'
                    , "bhDate1", "bhDate2", "bhDate3", "bhDate4", "bhDate5", "bhDate6"
                    , "bcDate1", "bcDate2", "bcDate3", "bcDate4", "bcDate5", "bDate6"
                    , "addTaskTimeStr1", "addTaskTimeStr2", "addTaskTimeStr3", "addTaskTimeStr4", "addTaskTimeStr5", "addTaskTimeStr6", "addTaskTimeStr", "degreeUpTimeStr"
                    , 'fpPhotoLqNameStr', 'fpVideoLqNameStr', 'fpDetailLqNameStr', 'fpModelLqNameStr'
                    , 'productID', 'arrivalTimeStr', 'applyTimeDays', 'applyTimeStr'
                    , 'orderNoInner', 'expressNo', 'shootOrderTrack', 'Divisionline3','Divisionline4'];
                  break;
                //评分
                case "d":
                  checkedColumnsFora = ['warehouseStr', 'cankao',
                    , 'vedioConfirmTimeStr'
                    , 'photoOverTimeStr', 'vedioOverTimeStr', 'microDetailOverTimeStr', 'detailOverTimeStr', 'modelPhotosOverTimeStr', 'modelVideoOverTimeStr'
                    , 'photoDaysStr', 'vedioDaysStr', 'microDetailDaysStr', 'detailDaysStr', 'modelPhotosDaysStr', 'modelVideoDaysStr',
                    , 'microDetailVedioCounts',
                    , 'microDetailConfirmTimeStr'
                    , 'detailConfirmTimeStr', 'styleName'
                    , 'modelPhotoCounts', 'actualCompleteDay'
                    , 'modelVedioCounts', 'bcDate6'
                    , 'vedioConfirmNameStr', 'microDetailConfirmNameStr', 'detailConfirmNameStr'
                    , 'platformStr', 'shopNameStr'
                    , 'arrivalTimeDays', 'deliverTimeStr', 'deliverTimeDays',
                    , 'makeUpDatetime1', 'makeUpDatetime2', 'makeUpDatetime3', 'makeUpDatetime4', 'makeUpDatetime5', 'makeUpDatetime6'
                    , 'makeUpDay1', 'makeUpDay2', 'makeUpDay3', 'makeUpDay4', 'makeUpDay5', 'makeUpDay6'
                    , 'photoConfirmNameStr', 'photoConfirmTimeStr', 'modelPhotosConfirmNameStr'
                    , 'modelPhotosConfirmTimeStr', 'modelVideoConfirmTimeStr', 'modelVideoConfirmNameStr'
                    , "bhDate1", "bhDate2", "bhDate3", "bhDate4", "bhDate5", "bhDate6"
                    , "bcDate1", "bcDate2", "bcDate3", "bcDate4", "bcDate5", "bDate6"
                    , "addTaskTimeStr1", "addTaskTimeStr2", "addTaskTimeStr3", "addTaskTimeStr4", "addTaskTimeStr5", "addTaskTimeStr6", "addTaskTimeStr", "degreeUpTimeStr"
                    , 'fpPhotoLqNameStr', 'fpVideoLqNameStr', 'fpDetailLqNameStr', 'fpModelLqNameStr'
                    , 'productID', 'arrivalTimeStr', 'applyTimeDays', 'applyTimeStr'
                    , 'orderNoInner', 'expressNo', 'shootOrderTrack', 'Divisionline3','Divisionline4'];
                  break;
                //补改
                case "e":
                  checkedColumnsFora = ['warehouseStr', 'cankao'
                    , 'vedioConfirmTimeStr'
                    , 'microDetailVedioCounts',
                    , 'microDetailConfirmTimeStr'
                    , 'detailConfirmTimeStr', 'styleName'
                    , 'modelPhotoCounts', 'actualCompleteDay'
                    , 'modelVedioCounts',
                    , 'vedioConfirmNameStr', 'microDetailConfirmNameStr', 'detailConfirmNameStr'
                    , 'photoDaysStr', 'vedioDaysStr', 'microDetailDaysStr', 'detailDaysStr', 'modelPhotosDaysStr', 'modelVideoDaysStr',
                    , 'platformStr', 'shopNameStr'
                    , 'arrivalTimeDays', 'deliverTimeStr', 'deliverTimeDays',
                    , 'scoreType1', 'scoreType2', 'scoreType3', 'scoreType4', 'scoreType5', 'scoreType6'
                    , 'scoreType11', 'scoreType12', 'scoreType13', 'scoreType14', 'scoreType15', 'scoreType16'
                    , 'photoConfirmNameStr', 'photoConfirmTimeStr', 'modelPhotosConfirmNameStr'
                    , 'modelPhotosConfirmTimeStr', 'modelVideoConfirmTimeStr', 'modelVideoConfirmNameStr'
                    , "addTaskTimeStr1", "addTaskTimeStr2", "addTaskTimeStr3", "addTaskTimeStr4", "addTaskTimeStr5", "addTaskTimeStr6", "addTaskTimeStr", "degreeUpTimeStr"
                    , 'fpPhotoLqNameStr', 'fpVideoLqNameStr', 'fpDetailLqNameStr', 'fpModelLqNameStr'
                    , 'productID', 'arrivalTimeStr', 'applyTimeDays', 'applyTimeStr'
                    , 'orderNoInner', 'expressNo', 'shootOrderTrack', 'Divisionline3','Divisionline4'];
                  break;

                default:
                    break;
            }
            await this.$refs.shootingvideotaskoverNew.ShowHidenColums(checkedColumnsFora);
            this.listLoading = false;
        },
        //查询
        async onSearch() {
            this.$refs.pager.setPage(1);
            this.getTaskList();
            this.selids = [];
        },
        //刷新当前页
        async onRefresh() {
            await this.getTaskList();
        },
        //获取数据
        async getTaskList() {
            var pager = this.$refs.pager.getPager();
            if (this.filter.createdtimerange) {
                this.filter.createdstartTime = this.filter.createdtimerange[0];
                this.filter.createdendTime = this.filter.createdtimerange[1];
            } else {
                this.filter.createdstartTime = null;
                this.filter.createdendTime = null;
            }
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            params.platform.length>0?params.platform:params.platform=""
            this.listLoading = true;
            const res = await pageShootingViewTaskAsync(params);
            this.listLoading = false;
            this.total = res.data.total;
            this.tasklist = res.data.list;
            console.log("567",this.tasklist);
            // this.summaryarry = { shootingTaskId_sum: "_" };
            this.summaryarry = res.data.summary;
        },
        //列表排序
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async selectchangeevent(records) {
            this.selids = [];
            records.forEach(f => {
                this.selids.push(f.shootingTaskId);
            })
        },
        async rowChange(row) {

            await this.editTask(row);
        },
        //编辑任务
        async editTask(row) {
            this.addLoading = true;
            this.editopentime = this.editopentime + 1;
            this.taskPageTitle = "编辑任务";
            this.editTaskshow = true;
            await this.$nextTick(function () { this.$refs.shootingvideotaskeditfrom.editTask(row,1); });
            this.addLoading = false;
        },
        //新增任务
        async onAddTask() {
            this.addLoading = true;
            this.opentime = this.opentime + 1;
            this.addTask = true;
            this.taskPageTitle = "创建任务";
            this.islook = false;
            this.addLoading = false;
        },
        //提交保存
        async onSubmit() {
            this.addLoading = true;
            await this.$nextTick(function () {
                this.$refs.shootingvideotaskeditfrom.onSubmit();
            });
            this.addLoading = false;
        },
        //删除上传附件操作
        async deluplogexl(ret) {
            this.addLoading = true;

            await delShootingTploadFileTaskAsync({ upLoadPhotoId: ret.upLoadPhotoId, type: 2 }).catch(_ => {
                this.addLoading = false;
            });

            this.addLoading = false;
        },
        //删除上传图片操作
        async deluplogimg(ret) {
            this.addLoading = true;
            await delShootingTploadFileTaskAsync({ upLoadPhotoId: ret.upLoadPhotoId, type: 1 }).catch(_ => {
                this.addLoading = false;
            });
            this.addLoading = false;
        },
        //关闭窗口，初始化数
        onCloseAddForm(type) {
            this.onSearch();
            if (type == 1) {
                this.addTask = false;
                this.editTaskshow = false;
            }
        },

        //紧急程度按钮点击
        async shootUrgencyCilck(btnstr, shootingTaskId) {

        },
        async taskUrgencyApp() {
            var res = await shootUrgencyCilckAsync({ taskid: this.selshootingTaskId, index: this.taskUrgencyStatus });
            if (res?.success) {
                this.taskUrgencyAproved = false;
                this.$message({ message: '操作成功', type: "success" });
                await this.onRefresh();
            }
        },

        //查看详情备注页
        openTaskRmarkInfo(row) {
            this.markopentime = this.markopentime + 1;
            this.selectRowKey = row.shootingTaskId;
            this.viewReferenceRemark = true;
        },
        //成果文件上传
        onUploadSuccessFile(row) {
            this.opentime = this.opentime + 1;
            this.selectRowKey = row.shootingTaskId;
            this.outconfilekey = row.shootingTaskId;
            this.successfiledrawer = true;
        },
        //关闭成果文件上传
        successfiledrawerClose() {
            this.successfiledrawer = false;
        },
        //查看参考附件
        videotaskuploadfileDetal(row) {
            this.opentime = this.opentime + 1;
            this.selectRowKey = row.shootingTaskId;
            // this.viewReference = true;

            if (row != null) {
                let routeUrl = this.$router.resolve({
                    path: '/seereference',
                    query: { id: row.shootingTaskId, refid: row.referenceId }
                });
                window.open(routeUrl.href, '_blank');
            }

        },
        //查看成果文件
        openComputOutInfo(row) {
            this.outsueccessopentime = this.outsueccessopentime + 1;
            this.selectRowKey = row.shootingTaskId;
            this.successfileshow = true;
            this.platform = row.platform;
        },
        successfiledrawerscoreClose() {
            this.successfileshow = false;
        },

        async sumbitshootingTaskRemark() {
            this.shootingTaskRemarkrawer = true;
            await this.$refs.shootingTaskRemark.onsubmit();
            this.shootingTaskRemarkrawer = false;

        },
        //下单发货表单
        async getCity(parentCode, type,) {
            const res = await getCityAllData({ parentCode: parentCode });
            if (res?.success) {
                if (type == 1) {
                    this.receiverStateList = res?.data;
                }
                if (type == 2) {
                    this.receiverCityList = res?.data;
                }
                if (type == 3) {
                    this.receiverDistrictList = res?.data;
                }
            }
        },
        async receiverStateChange() {
            this.receiverCityList = [];
            this.receiverDistrictList = [];
            this.addOrderForm.receiverCityCode = "";
            this.addOrderForm.receiverDistrictCode = "";
            var parentCode = this.addOrderForm.receiverStateCode;
            if (parentCode) {
                this.getCity(parentCode, 2);
            }
        },
        async receiverCityChange() {
            this.receiverDistrictList = [];
            this.addOrderForm.receiverDistrictCode = "";
            var parentCode = this.addOrderForm.receiverCityCode;
            if (parentCode) {
                this.getCity(parentCode, 3);
            }
        },
        async getCity2(parentCode, type,) {
            const res = await getCityAllData({ parentCode: parentCode });
            if (res?.success) {
                if (type == 1) {
                    this.receiverStateList = res?.data;
                }
                if (type == 2) {
                    this.receiverCityList2 = res?.data;
                }
                if (type == 3) {
                    this.receiverDistrictList2 = res?.data;
                }
            }
        },
        async receiverStateChange2() {
            this.receiverCityList2 = [];
            this.receiverDistrictList2 = [];
            this.addAddressForm.receiverCityCode = "";
            this.addAddressForm.receiverDistrictCode = "";
            var parentCode = this.addAddressForm.receiverStateCode;
            if (parentCode) {
                this.getCity2(parentCode, 2);
            }
        },
        async receiverCityChange2() {
            this.receiverDistrictList2 = [];
            this.addAddressForm.receiverDistrictCode = "";
            var parentCode = this.addAddressForm.receiverCityCode;
            if (parentCode) {
                this.getCity2(parentCode, 3);
            }
        },
        //下单发货
        async onAddOrder() {
            if (this.selids.length <= 0) {
                this.$message({ message: '请勾选任务', type: "warning" });
                return;
            }
            const res = await shootingTaskAddOrderSaveCheckTaskIds(this.selids);
            if (!res?.success) {
                return;
            }
            this.addOrderForm.shootingTaskIds = "";
            this.addOrderForm.orderGoods = [];
            this.selShootingTaskIdSpanList = [];
            this.selids.forEach(shootingTaskId => {
                this.addOrderForm.shootingTaskIds += (shootingTaskId.toString() + ";");
                this.selShootingTaskIdSpanList.push((shootingTaskId.toString() + ";&nbsp"));
            });
            this.dialogAddOrderVisible = true;
        },
        //关闭下单发货界面
        async closeAddOrder() {
            this.dialogAddOrderSubmitLoding = false;
        },
        //下单发货：选择商品
        async onSelctOrderGoods() {
            this.orderGoodschoiceVisible = true;
            this.$refs.orderGoodschoice.removeSelData();
        },
        //同步附件中的sku商品
        async onSyncOrderGoods() {
            var res = await getTaskReferenceInfo({ shootingTaskIds: this.addOrderForm.shootingTaskIds });
            if (res?.success) {
                var temp = this.addOrderForm.orderGoods;
                var isNew = true;
                res?.data.forEach((item) => {
                    temp.forEach(old => {
                        if (old.goodsCode == item.goodsCode) {
                            isNew = false;
                        }
                    });
                    if (isNew) {
                        this.addOrderForm.orderGoods.push({
                            goodsCode: item.goodsCode,
                            goodsName: item.goodsName,
                            shopCode: null,
                            shopName: null,
                            proCode: null,
                            goodsPrice: item.costPrice ?? 0,
                            goodsQty: 1,
                            goodsAmount: item.costPrice ?? 0
                        });
                    }
                });
            }
        },
        //下单发货：选择商品确定
        async onQuerenOrderGoods() {
            var choicelist = await this.$refs.orderGoodschoice.getchoicelist();
            if (choicelist && choicelist.length > 0) {
                //已存在的不添加
                var temp = this.addOrderForm.orderGoods;
                var isNew = true;
                choicelist.forEach((item) => {
                    isNew = true;
                    temp.forEach(old => {
                        if (old.goodsCode == item.goodsCode) {
                            isNew = false;
                        }
                    });
                    if (isNew) {
                        this.addOrderForm.orderGoods.push({
                            goodsCode: item.goodsCode,
                            goodsName: item.goodsName,
                            shopCode: item.shopId,
                            shopName: item.shopName,
                            proCode: item.shopStyleCode,
                            goodsPrice: item.costPrice ?? 0,
                            goodsQty: 1,
                            goodsAmount: item.costPrice ?? 0
                        });
                    }
                });
                this.orderGoodschoiceVisible = false;
            }
        },
        //移除明细
        async onDelDtlGood(index) {
            this.addOrderForm.orderGoods.splice(index, 1);
        },
        async addOrderFormGoodsQtyChange(row) {
            row.goodsAmount = (row.goodsQty * (row.goodsPrice ?? 0)).toFixed(2);
        },
        //新增编辑提交时验证
        addOrderFormValidate: function () {
            let isValid = false
            this.$refs.addOrderForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        //下单发货-保存
        async onAddOrderSave() {
            if (this.addOrderForm.shootingTaskIds == "" || this.addOrderForm.orderGoods.length <= 0) {
                this.$message({ message: '下单发货信息不完整，请填写', type: "warning" });
                return;
            }
            if (this.addOrderForm.isZt == 1) {
                if (this.addOrderForm.warehouse == "" || this.addOrderForm.warehouse == null) {
                    this.$message({ message: '拿样方式=仓库自提时必须填写自提仓库', type: "warning" });
                    return;
                }
            }
            else {
                this.addOrderForm.warehouse = null;
            }
            if (this.addOrderForm.isZt == 0) {
                if (this.addOrderForm.receiverAddressAllInfo == "" || this.addOrderForm.receiverAddressAllInfo == null) {
                    this.$message({ message: '拿样方式=快递寄样时必须选择详细地址', type: "warning" });
                    return;
                }
                if (this.addOrderForm.receiverName == "" || this.addOrderForm.receiverPhone == "") {
                    this.$message({ message: '收货人信息错误，请刷新后重试', type: "warning" });
                    return;
                }
                if (this.addOrderForm.receiverAddress == "") {
                    this.$message({ message: '下单发货地址错误，请刷新后重试', type: "warning" });
                    return;
                }
            }
            else {
                this.addOrderForm.receiverName = "";
                this.addOrderForm.receiverPhone = "";
                this.addOrderForm.receiverState = "";
                this.addOrderForm.receiverStateCode = "";
                this.addOrderForm.receiverCity = "";
                this.addOrderForm.receiverCityCode = "";
                this.addOrderForm.receiverDistrict = "";
                this.addOrderForm.receiverDistrictCode = "";
                this.addOrderForm.receiverAddress = "";
                this.addOrderForm.receiverAddressAllInfo = "";
            }
            this.dialogAddOrderSubmitLoding = true;
            this.dialogAddOrderLoading = true;
            const res = await shootingTaskAddOrderSave(this.addOrderForm);
            this.dialogAddOrderLoading = false;
            if (res?.success) {
                if (this.addOrderForm.isZt == 1) {
                    this.$message({ message: '操作成功，请关注钉钉审批流程', type: "success" });
                }
                else {
                    this.$message({ message: '操作成功，请关注钉钉审批流程', type: "success" });
                }
                this.onSearch();
                this.dialogAddOrderVisible = false;
                this.dialogAddOrderSubmitLoding = false;
            }
            // else {
            //     if (res?.msg)
            //         this.$message({ message: res?.msg, type: "error" });
            //     else
            //         this.$message({ message: '操作失败，请刷新后重试', type: "error" });
            // }
        },
        async dialogOrderDtlColsed() {
            this.xdfhmainlist = [];
            this.xdfhdtllist = [];
        },
        async onShowOrderDtl(row) {
            this.dialogOrderDtlVisible = true;
            this.xdfhmainLoading = true;
            this.xdfhdtlLoading = true;
            var ret = await getShootingTaskOrderListById({ shootingTaskId: row.shootingTaskId });
            this.xdfhmainLoading = false;
            this.xdfhdtlLoading = false;
            if (ret?.success && ret.data.length > 0) {
                ret.data.forEach(f => f.shootingTaskId = row.shootingTaskId);
                this.xdfhmainlist = ret.data;
                this.xdfhdtllist = ret.data[0].dtlEntities;
            }
        },
        async onxdfhmainCellClick(row) {
            this.xdfhmainlist.forEach(
                f => {
                    if (f.shootingTaskOrderId == row.shootingTaskOrderId) {
                        this.xdfhdtllist = f.dtlEntities;
                    }
                }
            );
        },
        async receiverAddressSelChange() {
            this.addressList.forEach(f => {
                if (f.vedioTaskOrderAddressId == this.addOrderForm.receiverAddressAllInfo && this.addOrderForm.receiverAddressAllInfo != "") {
                    this.addOrderForm.receiverStateCode = f.receiverStateCode;
                    this.addOrderForm.receiverCityCode = f.receiverCityCode;
                    this.addOrderForm.receiverDistrictCode = f.receiverDistrictCode;
                    this.addOrderForm.receiverAddress = f.receiverAddress;
                    if (f.receiverName)
                        this.addOrderForm.receiverName = f.receiverName;
                    if (f.receiverPhone)
                        this.addOrderForm.receiverPhone = f.receiverPhone;

                    return;
                }
            });
            ;
        },
        async getAddressList() {
            this.addressList = [];
            this.addressListLoading = true;
            var ret = await getVedioTaskOrderAddressList();
            this.addressListLoading = false;
            this.addressList = ret?.data;
            this.receiverAddressList = [];
            if (ret?.success) {
                this.receiverAddressList = ret.data?.map(item => { return { value: item.vedioTaskOrderAddressId, label: item.receiverAllAddress }; });
            }
        },
        async onAddressSet() {
            this.dialogAddressVisible = true;
            this.getAddressList();
        },
        async onAddAddressSave() {
            this.dialogAddAddressSubmitLoding = true;
            var ret = await saveVedioTaskOrderAddress(this.addAddressForm);
            this.dialogAddAddressSubmitLoding = false;
            if (ret?.success) {
                this.addAddressForm = {
                    receiverName: "",
                    receiverPhone: "",
                    receiverStateCode: null,
                    receiverCityCode: null,
                    receiverDistrictCode: null,
                    receiverAddress: "",
                };
                this.getAddressList();
            }
        },
        async onAddressDelete(row) {
            this.$confirm("是否确定删除, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                this.addressListLoading = true;
                var ret = await deleteVedioTaskOrderAddress({ vedioTaskOrderAddressId: row.vedioTaskOrderAddressId });
                this.addressListLoading = false;
                this.getAddressList();
            });
        },
        async onShowExproessHttp(row) {
            this.drawervisible = true;
            let expressNo = row.expressNo;
            if (!expressNo) {
                expressNo = row.sampleExpressNo;
            }
            this.$nextTick(function () {
                this.$refs.logistics.showlogistics("", expressNo);
            })
        },
        showLogDetail(row) {
            this.dialogHisVisible = true;
            let sampleRrderNo = row.sampleRrderNo;
            if (!sampleRrderNo) {
                sampleRrderNo = row.orderNoInner;
            }
            this.sendOrderNoInner = sampleRrderNo;
        },
    },
};
</script>
<style lang="scss" scoped>
::v-deep .el-drawer__header {
    border: none !important;
    padding: 16px 24px 0 24px;
}

::v-deep .el-header {
    padding: 10px 5px 5px 5px !important;
}

.ssanc {
    width: 100%;
    height: 38px;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
    display: inline-block;

}

.heardcss {
    width: 100%;
    min-width: 1150px;
    min-height: 35px;
    // background-color: aqua;
    box-sizing: border-box;
    display: inline-block;
    margin-top: 8px;
}


.heardcss span,
.ssanc span {
    padding: 4px 0.2% 4px 0;
}

::v-deep span .el-radio-button__inner {
    line-height: 14px !important;
}

::v-deep .vxetablecss {
    width: 100%;
    margin-top: -20px !important;
}

// ::v-deep .vxetoolbar20221212 {
//     top: 97px;
//     right: 15px;
// }

::v-deep .el-button-group>.el-button:last-child {
    margin-left: -2px !important;
}

::v-deep .el-button-group {
    margin: 0 !important;
    padding: 0 !important;
}

// ::v-deep .vxe-table--render-default .vxe-cell {
//     padding:0 5px !important;
// }
::v-deep .vxe-header--row {
    height: 58px;
}

::v-deep .el-table__body-wrapper {
    height: 220px !important;
}

::v-deep .el-table__body-wrapper {
    overflow-y: auto;
}


.gddwz {
    width: 100%;
    /* background-color: #F2F6FC; */
    //   border: 1px solid #ff1414;
    overflow: hidden;
    white-space: nowrap;
    box-sizing: border-box;
    text-align: center;
}

.gddwznr {
    padding-left: 20px;
    font-size: 14px;
    color: #eb0000;
    display: inline-block;
    white-space: nowrap;
    animation: 20s wordsLoop linear infinite normal;
    margin: 0;
}

@keyframes wordsLoop {
    0% {
        transform: translateX(100%);
        -webkit-transform: translateX(100%);
    }

    100% {
        transform: translateX(-100%);
        -webkit-transform: translateX(-100%);
    }
}

@-webkit-keyframes wordsLoop {
    0% {
        transform: translateX(100%);
        -webkit-transform: translateX(100%);
    }

    100% {
        transform: translateX(-100%);
        -webkit-transform: translateX(-100%);
    }
}
</style>

