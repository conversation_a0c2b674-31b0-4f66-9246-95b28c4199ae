<template>
    <MyContainer>
        <template #header>
            <div class="header">
                <!-- <div class="checkBoxCss">
                    <span style="font-size: 12px">统计维度:&nbsp;&nbsp;</span>
                    <el-checkbox-group v-model="currentInfo.groupTypes" @change="changeCheackBox">
                        <el-checkbox :label="1"
                            :disabled="currentInfo.groupTypes.length == 1 && currentInfo.groupTypes.includes(1) && isdisabled">发货日期</el-checkbox>
                        <el-checkbox :label="2"
                            :disabled="currentInfo.groupTypes.length == 1 && currentInfo.groupTypes.includes(2) && isdisabled">发货仓</el-checkbox>
                        <el-checkbox :label="3"
                            :disabled="currentInfo.groupTypes.length == 1 && currentInfo.groupTypes.includes(3) && isdisabled">平台</el-checkbox>
                        <el-checkbox :label="4"
                            :disabled="currentInfo.groupTypes.length == 1 && currentInfo.groupTypes.includes(4) && isdisabled">系列编码</el-checkbox>
                        <el-checkbox :label="5"
                            :disabled="currentInfo.groupTypes.length == 1 && currentInfo.groupTypes.includes(5) && isdisabled">商品编码</el-checkbox>
                        <el-checkbox :label="6"
                            :disabled="currentInfo.groupTypes.length == 1 && currentInfo.groupTypes.includes(6) && isdisabled">损耗部门</el-checkbox>
                        <el-checkbox :label="7"
                            :disabled="currentInfo.groupTypes.length == 1 && currentInfo.groupTypes.includes(7) && isdisabled">损耗类型</el-checkbox>
                        <el-checkbox :label="8"
                            :disabled="currentInfo.groupTypes.length == 1 && currentInfo.groupTypes.includes(8) && isdisabled">处理方式</el-checkbox>
                        <el-checkbox :label="9"
                            :disabled="currentInfo.groupTypes.length == 1 && currentInfo.groupTypes.includes(9) && isdisabled">责任人</el-checkbox>
                    </el-checkbox-group>
                </div> -->
                <div class="header_item">
                    <span style="font-size: 12px">发货时间:</span>
                    <el-date-picker v-model="filter.timerange" type="daterange" align="right" unlink-panels
                        range-separator="至" format="yyyy-MM-dd" start-placeholder="发货开始日期" @change="changedate"
                        end-placeholder="发货结束日期" :picker-options="pickerOptions" style="width:260px;" />
                    <el-select v-model="filter.sendWareHouseId" placeholder="发货仓" clearable>
                        <el-option v-for="(item, i) in newWareHouseList" :key="i" :label="item.name" :value="item.wms_co_id"
                            class="publicCss" />
                    </el-select>
                    <el-input v-model="filter.styleCode" placeholder="系列编码" maxlength="50" clearable class="publicCss" />
                    <el-input v-model="filter.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
                    <el-input v-model="filter.expressOrder" placeholder="快递单号" maxlength="50" clearable class="publicCss" />
                    <el-select class="publicCss" v-model="filter.zrDepartment" clearable placeholder="损耗部门"
                        @change="getZrType(filter.zrDepartment)">
                        <el-option v-for="(item, i) in damagedList" :key="i" :label="item" :value="item" />
                    </el-select>
                    <el-select v-model="filter.zrType2" placeholder="损耗类型" clearable @visible-change="shopIncident">
                        <el-option v-for="(item, i) in damagedList2" :key="i" :label="item" :value="item"
                            class="publicCss" />
                    </el-select>
                    <el-select v-model="filter.damagedHandType" placeholder="处理方式" clearable>
                        <el-option v-for="(item, i) in damagedHandList" :key="i" :label="item.name" :value="item.value"
                            class="publicCss" />
                    </el-select>
                </div>
                <div class="header_item">
                    <span style="font-size: 12px">售后发起时间:</span>
                    <el-date-picker v-model="filter.AfterSaleApproveDate" type="daterange" align="right" unlink-panels
                        range-separator="至" start-placeholder="售后发起开始时间" end-placeholder="售后发起结束时间" format="yyyy-MM-dd"
                        :picker-options="pickerOptions" @change="update" style="width:280px" />
                    <span style="font-size: 12px">责任计算时间:</span>
                    <el-date-picker v-model="filter.ZrSetDate" type="daterange" align="right" unlink-panels
                        range-separator="至" start-placeholder="责任计算开始时间" end-placeholder="责任计算结束时间" format="yyyy-MM-dd"
                        :picker-options="pickerOptions" style="width:280px" @change="update" />
                    <el-input v-model="filter.orderInnerNo" placeholder="内部订单号" maxlength="50" clearable
                        class="publicCss1" />
                    <el-input v-model="filter.orderNo" placeholder="线上订单号" maxlength="50" clearable class="publicCss1" />
                    <el-select v-model="filter.platform" placeholder="平台" @change="onchangeplatform" clearable>
                        <el-option v-for="item in platformList" :key="item.label" :label="item.label" :value="item.value"
                            class="publicCss1" />
                    </el-select>
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺" @change="zrType2change()"
                        class="publicCss1">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode"></el-option>
                    </el-select>
                    <el-select clearable v-model="filter.picOrder" placeholder="图片排序" @change="getlist()"
                        style="width: 100px;">
                        <el-option label="升序" :value="1" />
                        <el-option label="降序" :value="2" />
                    </el-select>
                    <el-input class="publicCss1" v-model="filter.memberName" clearable placeholder="责任人"
                        style="width: 160px" maxlength="50" />
                    <el-button type="primary" @click="onSearch('click')">查询</el-button>
                </div>
            </div>
        </template>

        <div class="bodyCss">
            <div class="viewImageBox father" v-loading="domLoading">
                <div class="viewImageBox_item" v-for="(item, i) in tableList">
                    <el-image class="viewImageBox_item_img" :src="item.damagedZrFileUrls ? item.damagedZrFileUrls[0] : ''"
                        @click="deliverImg(item, i)"></el-image>
                    <div class="viewImageBox_item_bottom">
                        <div class="viewImageBox_item_info" v-show="currentInfo.groupTypes.includes(1)">
                            <div class="viewImageBox_item_info_left">发货日期</div>
                            <el-tooltip class="viewImageBox_item_info_right" effect="dark" :content="item.sendGoodsDate"
                                placement="top-end">
                                <div>{{ item.sendGoodsDate }}</div>
                            </el-tooltip>
                        </div>
                        <div class="viewImageBox_item_info" v-show="currentInfo.groupTypes.includes(2)">
                            <div class="viewImageBox_item_info_left">发货仓</div>
                            <el-tooltip class="viewImageBox_item_info_right" effect="dark" :content="item.sendWareHouse"
                                placement="top-end">
                                <div>{{ item.sendWareHouse }}</div>
                            </el-tooltip>
                        </div>
                        <div class="viewImageBox_item_info" v-show="currentInfo.groupTypes.includes(3)">
                            <div class="viewImageBox_item_info_left">平台</div>
                            <el-tooltip class="viewImageBox_item_info_right" effect="dark" :content="item.platform"
                                placement="top-end">
                                <div>{{ item.platform }}</div>
                            </el-tooltip>
                        </div>
                        <div class="viewImageBox_item_info" v-show="currentInfo.groupTypes.includes(4)">
                            <div class="viewImageBox_item_info_left">系列编码</div>
                            <el-tooltip class="viewImageBox_item_info_right" effect="dark" :content="item.styleCode"
                                placement="top-end">
                                <div>{{ item.styleCode }}</div>
                            </el-tooltip>
                        </div>
                        <div class="viewImageBox_item_info" v-show="currentInfo.groupTypes.includes(5)">
                            <div class="viewImageBox_item_info_left">商品编码</div>
                            <el-tooltip class="viewImageBox_item_info_right" effect="dark" :content="item.goodsCode"
                                placement="top-end">
                                <div>{{ item.goodsCode }}</div>
                            </el-tooltip>
                        </div>
                        <div class="viewImageBox_item_info" v-show="currentInfo.groupTypes.includes(6)">
                            <div class="viewImageBox_item_info_left">损耗部门</div>
                            <el-tooltip class="viewImageBox_item_info_right" effect="dark" :content="item.zrDepartment"
                                placement="top-end">
                                <div>{{ item.zrDepartment }}</div>
                            </el-tooltip>
                        </div>
                        <div class="viewImageBox_item_info" v-show="currentInfo.groupTypes.includes(7)">
                            <div class="viewImageBox_item_info_left">损耗类型</div>
                            <el-tooltip class="viewImageBox_item_info_right" effect="dark" :content="item.zrType2"
                                placement="top-end">
                                <div>{{ item.zrType2 }}</div>
                            </el-tooltip>
                        </div>
                        <div class="viewImageBox_item_info" v-show="currentInfo.groupTypes.includes(8)">
                            <div class="viewImageBox_item_info_left">处理方式</div>
                            <el-tooltip class="viewImageBox_item_info_right" effect="dark" :content="item.damagedHandType"
                                placement="top-end">
                                <div>{{ item.damagedHandType }}</div>
                            </el-tooltip>
                        </div>
                        <div class="viewImageBox_item_info" v-show="currentInfo.groupTypes.includes(9)">
                            <div class="viewImageBox_item_info_left">责任人</div>
                            <el-tooltip class="viewImageBox_item_info_right" effect="dark" :content="item.memberName"
                                placement="top-end">
                                <div>{{ item.memberName }}</div>
                            </el-tooltip>
                        </div>
                    </div>
                    <div class="viewImageBox_item_fixed">{{ item.damagedZrFileUrlCount }}</div>
                </div>
            </div>
        </div>
        <my-pagination ref="pager"  :total="total" :checked-count="sels.length"
            @page-change="ImgViewPagechange" @size-change="ImgViewSizechange" />
        <div class="imageModalPos" v-if="imageModelVisiable">
            <viewImageCarousel :index="imageIndex" :maxIndex="maxIndex" v-if="imgList" :imgList="imgList"
                :imageInfo="imageInfo" :domList="domList" :closeModal="closeModal" ref="imageModelRef"
                :checkList="currentInfo.groupTypes" />
        </div>
    </MyContainer>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import imageModel from "../components/viewImages.vue"
import dayjs from "dayjs";
import { getAllWarehouse } from '@/api/inventory/warehouse';
import { getAllListInCalc as getAllShopList, getList as getshopListt } from '@/api/operatemanage/base/shop';
import viewImageCarousel from "@/views/order/LossOrderCostStatistics/components/viewImageCarousel.vue";
import {
    getDamagedOrdersStatisAsync, importDamagedOrdersBF, importDamagedOrdersHB, importDamagedOrdersQT,
    getDamagedOrdersStatisAnlysisAsync, getDamagedOrdersZrDept, getDamagedOrdersZrType, exportDamagedOrdersStatisAsync, getDamagedOrdersStatisRowPageAsync
} from '@/api/customerservice/DamagedOrders'
import { formatTime, formatPlatform, pickerOptions } from "@/utils/tools";
export default {
    name: 'imageDetailInfo',
    components: { vxetablebase, MyContainer, viewImageCarousel },
    data() {
        return {
            tableList: [],
            wdlist: [],
            shopList: [],
            filter: {
                groupTypes: [],
                timerange: [],
                charttimerange: [],
                zrType2: null,
                AfterSaleApproveDate: [],
                ZrSetDate: [],
                startAfterSaleApproveDate: null,
                endAfterSaleApproveDate: null,
                endZrSetDate: null,
                startZrSetDate: null,
                endZrSetDate: null,
                sendWareHouseId: null,
                picOrder:null
            },
            imageIndex: 0,
            maxIndex: 0,
            imageModelVisiable: false,
            imgList: null,
            imageInfo: {},
            domLoading: true,
            damagedList: [],
            domList: [],
            damagedList2: [],
            newWareHouseList: [],
            damagedHandList: [
                { name: '补发', value: '补发' },
                { name: '红包补偿', value: '红包补偿' },
                { name: '订单部分补偿', value: '订单部分补偿' },
                { name: '订单全额退款', value: '订单全额退款' },
                { name: '订单退货退款', value: '订单退货退款' },
                { name: '订单退货补发', value: '订单退货补发' },
            ],
            pickerOptions,
            total: 0,
            sels: [],
            platformList: [{ label: '天猫', value: 1 }, { label: '拼多多', value: 2 }, { label: '阿里巴巴', value: 4 },
            { label: '抖音', value: 6 }, { label: '京东', value: 7 }, { label: '淘工厂', value: 8 }, { label: '淘宝', value: 9 }, { label: '苏宁', value: 10 },],
            currentInfo: {
                currentPage: 1,
                pageSize: 50,
                groupTypes: [4],
            },
            isdisabled: false,
        }
    },
    async mounted() {
        this.getZrDept()
        localStorage.removeItem('groupTypes');
        // this.currentInfo.groupTypes = JSON.parse(localStorage.getItem('groupTypes')) || [4]
        this.currentInfo.groupTypes = []
        this.currentInfo.groupTypes = [1,2,3,4,5,6,7,8,9]
        const { data } = await getAllWarehouse();
        this.newWareHouseList = data.filter((x) => { return x.name.indexOf('代发') < 0; });
        console.log(this.newWareHouseList, 'this.newWareHouseList');
        this.onSearch();
    },
    methods: {
        shopIncident(e) {
          if (e && e == true && !this.filter.zrDepartment) {
            this.$message.warning('请先选择损耗部门')
          }
        },
        async changeCheackBox() {
            if (this.currentInfo.groupTypes.length == 1) {
                this.isdisabled = true
            } else {
                this.isdisabled = false
            }
            //存到localStorage中
            localStorage.setItem('groupTypes', JSON.stringify(this.currentInfo.groupTypes))
            await this.getlist('click')
        },
        ImgViewPagechange(val) {
            this.currentInfo.currentPage = val;
            this.getlist()
        },
        ImgViewSizechange(val) {
            this.currentInfo.currentPage = 1;
            this.currentInfo.pageSize = val;
            this.getlist()
        },
        onSearch(type) {
            this.$refs.pager.setPage(1)
            this.getlist(type)
        },
        deliverImg(item, i) {
            console.log(item, 'item');
            console.log(i, 'i');
            console.log(item.damagedZrFileUrls, 'damagedZrFileUrls');
            this.imageModelVisiable = true
            // this.imgList = item.damagedZrFileUrls
            this.imageInfo = item
            this.imageIndex = i
            const length = this.tableList.length
            // 判断length减去当前索引是否小于7,如果小于7,就将后面所有数据放入domList中,否则就将后面7条数据放入domList中
            if (length - i < 7) {
                this.domList = this.tableList.slice(i, length)
            } else {
                this.domList = this.tableList.slice(i, i + 7)
            }
            this.$nextTick(() => {
                this.$refs.imageModelRef.clearCss()
            })
        },
        async getlist(type) {
            if(type == 'click'){
                this.currentInfo.currentPage = 1;
                this.currentInfo.pageSize = 50;
            }
            console.log(this.filter.sendWareHouseId, 'this.filter.sendWareHouseId');
            if (this.currentInfo.groupTypes.length == 1) {
                this.isdisabled = true
            }
            //发货时间默认为当前时间往前推一个月
            if (!this.filter.timerange || this.filter.timerange.length == 0) {
                this.filter.timerange = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
            }

            this.domLoading = true
            let _this = this;
            const pager = this.$refs.pager.getPager()
            this.filter.startSendGoodsDate = dayjs(this.filter.timerange[0]).format('YYYY-MM-DD');
            this.filter.endSendGoodsDate = dayjs(this.filter.timerange[1]).format('YYYY-MM-DD');
            if (this.filter.AfterSaleApproveDate && this.filter.AfterSaleApproveDate.length > 0) {
                this.filter.AfterSaleApproveDate = this.filter.AfterSaleApproveDate ? this.filter.AfterSaleApproveDate : []
                this.filter.startAfterSaleApproveDate = dayjs(this.filter.AfterSaleApproveDate[0]).format('YYYY-MM-DD');
                this.filter.endAfterSaleApproveDate = dayjs(this.filter.AfterSaleApproveDate[1]).format('YYYY-MM-DD');
            }else{
                this.filter.startAfterSaleApproveDate = null;
                this.filter.endAfterSaleApproveDate = null;
            }
            if (this.filter.ZrSetDate && this.filter.ZrSetDate.length > 0) {
                this.filter.ZrSetDate = this.filter.ZrSetDate ? this.filter.ZrSetDate : []
                this.filter.startZrSetDate = dayjs(this.filter.ZrSetDate[0]).format('YYYY-MM-DD');
                this.filter.endZrSetDate = dayjs(this.filter.ZrSetDate[1]).format('YYYY-MM-DD');
            }else{
                this.filter.startZrSetDate = null;
                this.filter.endZrSetDate = null;
            }
            const params = {
                ...pager,
                ...this.filter,
                isFileMode: 1,
                ...this.currentInfo
            }
            if (this.filter.picOrder)
            {
                params.orderBy="DamagedZrFileUrlCount";
                params.isAsc = this.filter.picOrder == 1;
            }else {
                params.isAsc = false;
            }
            this.listLoading = true
            // this.imgList =null;
            let newObj = JSON.parse(JSON.stringify(params))

            const { data, success } = await getDamagedOrdersStatisAsync(newObj)
            this.listLoading = false
            if (!success) return
            this.tableList = data.list
            this.tableList.forEach(item => {
                item.sendGoodsDate = dayjs(item.sendGoodsDate).format('YYYY-MM-DD')
                item.damagedZrFileUrls = item.damagedZrFileUrls ? item.damagedZrFileUrls.split(',') : null
                item.platform = formatPlatform(item.platform)
            })
            //取出this.tableList每项中的damagedZrFileUrls的每项,放入一个新数组中
            // this.tableList.forEach(item => {
            //     if (item.damagedZrFileUrls) {
            //         item.damagedZrFileUrls.forEach(item => {
            //             this.imgList.push(item)
            //         })
            //     }
            // })
            this.imgList = this.tableList
            this.total = data.total
            this.domLoading = false
            _this.$forceUpdate();
        },
        zrType2change() {
            this.$forceUpdate();
        },
        async onchangeplatform(val) {
            this.filter.shopCode = null;
            const res1 = await getshopListt({ platform: val, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
        },
        update() {
            this.$forceUpdate();
        },
        async getZrType(name) {
            const { data } = await getDamagedOrdersZrType(name);
            this.damagedList2 = data;
            this.filter.zrType2 = '';
        },
        async getZrDept() {
            let res = await getDamagedOrdersZrDept();
            this.damagedList = res?.data;
        },
        changedate(val) {
            let startDate;
            let endDate;
            startDate = val[0];
            endDate = val[1];
            this.filter.timerange = [startDate, endDate];
        },
        openViewImageCarousel() {
            this.imageModelVisiable = true
        },
        closeModal() {
            this.imageModelVisiable = false
            this.$refs.imageModelRef.clearCss()
        },
    }
}
</script>

<style scoped lang="scss">
.header {
    display: flex;
    //主轴变成侧轴
    flex-direction: column;

    .header_item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        font-size: 14px;
    }

    .checkBoxCss {
        height: 40px;
        display: flex;
        font-size: 14px;
        align-items: center;
    }
}

.publicCss {
    width: 200px;
}

.publicCss1 {
    width: 150px;
}

.imageModalPos {
    width: 100vw;
    height: 100vh;
    top: 0px;
    left: 0px;
    z-index: 999;
    overflow: hidden;
    position: fixed;
}

.bodyCss {
    height: 650px;
    overflow: auto;
    box-sizing: border-box;

    .viewImageBox {
        width: 100%;
        display: flex;
        flex-wrap: wrap;

        .viewImageBox_item {
            //黑色盒子阴影
            box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2);

            display: flex;
            flex-direction: column;
            margin: 0 10px 15px 0;
            position: relative;

            .viewImageBox_item_fixed {
                position: absolute;
                top: 0;
                left: 0;
                width: 50px;
                height: 30px;
                background-color: red;
                color: #fff;
                text-align: center;
                line-height: 30px;
            }


            .viewImageBox_item_bottom {
                width: 220px;

                .viewImageBox_item_info {
                    display: flex;

                    .viewImageBox_item_info_left,
                    .viewImageBox_item_info_right {
                        text-align: center;
                        height: 20px;
                        box-sizing: border-box;
                        border: 1px solid #ccc;
                        line-height: 20px;
                        box-sizing: border-box;
                        font-size: 12px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    .viewImageBox_item_info_left {
                        width: 80px;
                    }

                    .viewImageBox_item_info_right {
                        flex: 1;
                    }
                }
            }
        }
    }
}


.viewImageBox_item_img ::v-deep img {
    min-width: 220px !important;
    min-height: 130px !important;
    width: 220px !important;
    height: 130px !important;
    object-fit: contain !important;
}

.viewImageBox_item_img ::v-deep div {
    min-width: 220px !important;
    min-height: 130px !important;
    width: 220px !important;
    height: 130px !important;
}</style>
