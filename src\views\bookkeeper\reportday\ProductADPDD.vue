<template>
  <div style="height:100%;padding:10px;overflow: auto;position: relative; display: flex;">
     <div id="echartturnoverranganalysis1111" style="width: 100%;height: 489px; box-sizing:border-box; line-height: 360px;"/>
     <div id="chart"></div>
     <div v-show="ishover" style="position: absolute; left:400px; top: 440px; width: 700px; max-height: 140px; padding: 10px; border: 1px solid #eee; overflow-y: auto;">
       <span><span style="fontWeight: 600;">时间：</span> {{ timeremark }}</span>
       <div v-for="(item,i) in listremark" style="margin-left: 20px;" :key="i">
         <span style="font-weight: 600;">{{i+1}}、</span>
         {{ item }}
       </div>
     </div>
   </div>
 </template>
 <script>
 import * as echarts from 'echarts';
 import container from '@/components/my-container/nofooter'
 import cesTable from "@/components/Table/table.vue";
 import {getProCodeUnsalableAnalysisByDate,getProCodeUnsalableAnalysisByGroup} from '@/api/inventory/unsalable'
 import {getADdata} from '@/api/bookkeeper/reportday'
 export default {
   name: 'Roles',
   components: {container,cesTable},
    props:{
        filter: { },
        submitdata: []
      },
   data() {
     return {
       that:this,
       period:0,
       pageLoading: false,
       listLoading:false,
       procode:'',
       ishover: false,
       listremark: null,
       timeremark: null,
       lastseeprcodedrchartdata: []
     }
   },
  async mounted() {
 this.procode=window['lastseeprcodedrchart'];
 this.lastseeprcodedrchartdata = window['lastseeprcodedrchartdata'];
 await  this.getanalysisdata();
   },
   beforeUpdate() {
   },
  async onload(){
 this.procode=window['lastseeprcodedrchart'];
 await this.getanalysisdata();
   },
 methods: {
    async getanalysisdata() {
         var p=this.submitdata?{ProCode:this.procode,
          startDate: this.submitdata.createdtimerange[0],
          endDate: this.submitdata.createdtimerange[1],
        }:{
          ProCode:this.procode,
        };
       const respr=await getADdata(p);
       var chartDom = document.getElementById('echartturnoverranganalysis1111');
       var myChart = echarts.init(chartDom);
       // myChart.clear();
       if (!respr.data) {
         //  this.$message({message: "没有数据!",type: "warning",});
         const dom = document.getElementById('chart');
         dom.innerHTML = '-暂无相关数据-';
         dom.style.cssText = 'text-align:center; color: #999; border: none;line-height: 300px';
         dom.removeAttribute('_echarts_instance_');
         /* chartDom.innerHTML = '暂无数据'
         dom.style.cssText = 'text-align:center; color: #999; border: none;line-height: 300px';
         dom.removeAttribute('_echarts_instance_');
           return; */
        } 
     //console.log(respr.data.series[0].data);
     var max1=Math.max.apply(Math,respr.data.series[0].data);
      var max2=Math.max.apply(Math,respr.data.series[1].data);
     var max3=Math.max.apply(Math,respr.data.series[3].data);
     var max4=Math.max.apply(Math,respr.data.series[4].data);
     var max5=Math.max.apply(Math,respr.data.series[5].data);
     var max=Math.max.apply(Math,[max1,max2,max3,max4,max5]); 
     var min1=Math.min.apply(Math,respr.data.series[0].data);
     var min2=Math.min.apply(Math,respr.data.series[1].data);
     var min3=Math.min.apply(Math,respr.data.series[3].data);
     var min4=Math.min.apply(Math,respr.data.series[4].data);
     var min5=Math.min.apply(Math,respr.data.series[5].data);
     var min=Math.min.apply(Math,[min1,min2,min3,min4,min5]);
     var minmaolilv=Math.min.apply(Math,respr.data.series[2].data); 
     //respr.data.series[2].type='line'
   // if(max<200)
     //  max=200;
   max=(max/10)*10;
   respr.data.yAxis= [{position: "left", splitLine: {show: true}, min:0,max:max,unit: "", name: "", offset: 0},{position: "right",splitLine: {show: false}, unit: "%", name: "占比(%)", offset: 0}];
       var option = this.Getoptions(respr.data);
     //option.yAxis[1].max=100;
   //  if(min<0)
   //   {
   //   option.yAxis[0].splitNumber=10;
   //   option.yAxis[1].splitNumber=10;
   //   var minx=Math.abs(min)/(Math.abs(min)+max);
   //   var yx=minx*10;
   //    if(minmaolilv>-Math.ceil(yx)*10)
   //   option.yAxis[1].min=-Math.ceil(yx)*10; 
   //   }
   //   else
   //   {
   //     option.yAxis[0].splitNumber=10;
   //     option.yAxis[1].splitNumber=10;
   //     option.yAxis[1].min=0; 
   //     option.yAxis[1].max=100; 
   //   }
     option && myChart.setOption(option);
     let that=this;
     },
     Getoptions(element){
      var series=[]
      element.series.forEach(s=>{
        series.push({smooth: true, ...s})
 
      })
     let _this = this;
     let arrt = {
           "smooth": true,
           "name": "备注",
           "type": "line",
           "yAxisIndex": 0,
           "data": [
               0,
               0,
               0,
               0,
               0,
               0,
               0,
               0,
               0,
               0,
           ],
           "backColor": null,
           "stack": null,
           "markPoint": {
               "symbol": "triangle",
               "symbolSize": 10,
               "label": {
                   "show": true
               },
               "itemStyle":{
                 "color": "red",
               },
               data: _this.lastseeprcodedrchartdata
           }
       }
       let a = series.push(arrt);
      const domm = document.getElementById('echartturnoverranganalysis1111');
       echarts.init(domm).on('mouseover', function(params) {
           if (params.componentType === "markPoint") {
               _this.listremark = params.data.name;
               _this.timeremark = params.data.coord[0];
               _this.ishover = true;
           }
       });
       echarts.init(domm).on('click', function(params) {
           if (params.componentType === "markPoint") {
               _this.ishover = !_this.ishover;
           }
       });
 
     
 
      var yAxis=[]
      element.yAxis.forEach(s=>{
        yAxis.push({type: 'value',minInterval:10,offset:s.offset,splitLine:s.splitLine,position:s.position,name: s.name,axisLabel: {formatter: '{value}'+s.unit}})
      })
  
      var option = {
         title: {text:element.title},
         tooltip: {
           trigger: 'axis',
         },
         legend: {
             
             padding: [
                     30,  // 上
                     10, // 右
                     50,  // 下
                     10, // 左
                 ],
            // top: '-10',
            height:'100px',
            selected: {
                     // 选中'系列1'
                     '商品收藏数': false,
                     // 不选中'系列2'
                     '点击量': false,
                     '点击率': false,
                     '投入产出比': false,
                     '曝光量': false,
                     '花费(元)': true,
                     '直接交易额（元）': true,
                     '间接交易额（元）': true,
                     '直接成交笔数': false,
                     '间接成交笔数': false,
                     '成交笔数': false,
                     '每笔成交花费（元）': true,
                     '每笔成交金额': true,
                     '每笔直接成交金额': true,
                     '每笔间接成交金额': true,
                     '扣费点击': false,
                     '平均点击花费': true,
                     '全站推广费比': false,
                     '支付转化率': false,
                     '点击转化率': false,
                     '千次曝光花费（元）': true,
                     '店铺关注数': false,
                     '店铺关注成本': false,
                     '询单量': false,
                     '吸粉率': false,
                     '直播观看量': false,
                     '直播评论量': false,
                     '支付件数': false,
                     '支付买家数': false,
                     '交易额（元）': true,
                     '流量损失指数': false,
                     '下单率': false,
                     '视频成交人数': false,
                     '视频播放量': false,
                     '视频成交金额': true,
                     '视频订单数': false,
                     '智能推广': true
                     
                    },
            data:element.legend
          },
          
         grid: {
             left: '3%',
             right: '4%',
             bottom: '13%',
            top: '25%',
             containLabel: true
         },
         toolbox: {feature: {
             magicType: {show: true, type: ['line', 'bar']},
             //restore: {show: true},
         }},
         xAxis: {
             type: 'category',
             data: element.xAxis
         },
         yAxis: yAxis,
         series:  series
     };
     return option;
    },
   }
 }
 </script>
 <style>
 .el-select-content { 
     width: calc(100% - 10px);
     margin: 0;
  }
  
 </style>
 