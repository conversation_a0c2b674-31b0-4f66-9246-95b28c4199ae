<template>
  <MyContainer>
    <el-tabs v-model="activeName" style="height: 94%;">
      <el-tab-pane label="品牌店铺" name="first1" style="height: 100%;">
        <brandStores />
      </el-tab-pane>
      <el-tab-pane label="品牌管理费用1" name="first2" style="height: 100%;" lazy>
        <brandManagementFee1 />
      </el-tab-pane>
      <el-tab-pane label="品牌管理费用2" name="first3" style="height: 100%;" lazy>
        <brandManagementFee2 />
      </el-tab-pane>
      <el-tab-pane label="品牌仓库" name="first4" style="height: 100%;" lazy>
        <brandManagementWare />
      </el-tab-pane>
      <el-tab-pane label="品牌快递费" name="first5" style="height: 100%;" lazy>
        <brandSpecFrightFeeAvg />
      </el-tab-pane>
    </el-tabs> 
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import brandStores from './brandStores.vue'
import brandManagementFee1 from './brandManagementFee1.vue'
import brandManagementFee2 from './brandManagementFee2.vue'
import brandManagementWare from './brandManagementWare.vue'
import brandSpecFrightFeeAvg from './brandSpecFrightFeeAvg.vue'

export default {
  components: {
    MyContainer, brandStores, brandManagementFee1, brandManagementFee2, brandManagementWare
    , brandSpecFrightFeeAvg
  },
  data() {
    return {
      activeName: 'first1'
    };
  },
  methods: {

  }
};
</script>

<style lang="scss" scoped></style>
