<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <el-tabs v-model="activeName" style="height:94%;">
      
        <el-tab-pane label="淘系售后退款" name="tab1" style="height: 100%;">
          <AfterSaleTx :filter="filter" ref="AfterSaleTx" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane label="淘系售后退款(汇总)" name="tab2" style="height: 100%;">
        <AfterSaleTxSum :filter="filter" ref="AfterSaleTxSum" style="height: 100%;"/>
    </el-tab-pane>
      <el-tab-pane label="拼多多售后退款" name="tab3" style="height: 100%;">
          <AfterSalePdd :filter="filter" ref="AfterSalePdd" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane label="拼多多售后退款(汇总)" name="tab4" style="height: 100%;">
        <AfterSalePddSum :filter="filter" ref="AfterSalePddSum" style="height: 100%;"/>
    </el-tab-pane>
      <el-tab-pane label="拼多多小额打款" name="tab5" style="height: 100%;">
        <PettyPaymentPDD :filter="filter" ref="PettyPaymentPDD" style="height: 100%;"/>
    </el-tab-pane>
    
    </el-tabs>
    </my-container >
  
   </template>
  <script>
  import MyContainer from "@/components/my-container";
  import AfterSalePdd from '@/views/customerservice/refundAfterSale/AfterSalePdd' 
  import AfterSalePddSum from '@/views/customerservice/refundAfterSale/AfterSalePddSum'
  import AfterSaleTx from '@/views/customerservice/refundAfterSale/AfterSaleTx' 
  import AfterSaleTxSum from '@/views/customerservice/refundAfterSale/AfterSaleTxSum'
  import PettyPaymentPDD from '@/views/customerservice/refundAfterSale/PettyPaymentPDD'



  
  export default {
    name: "Users",
    components: { MyContainer,AfterSalePdd,AfterSaleTx,AfterSalePddSum,AfterSaleTxSum,PettyPaymentPDD},
    data() {
      return {
        that:this,
        pageLoading:'',
        filter: {
        },
        shopList:[],
        userList:[],
        groupList:[],
        selids:[],
        dialogVisibleSyj:false,
        fileList:[],
        activeName:'tab1'
      };
    },
    mounted() { 
      window.showtab4=this.showtab4
    },
    methods: {
      showtab4(){
       this.activeName=""
  
  
      }
  
    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
  