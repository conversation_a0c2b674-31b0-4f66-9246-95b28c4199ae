<template>
  <my-container v-loading="pageLoading">
    <!--查询-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="goodsFilter" @submit.native.prevent label-position="right"
        label-width="90px">
        <el-form-item label="商品编码:">
          <el-input v-model.trim="goodsFilter.goodsCode" placeholder="商品编码" @change="onSearch" />
        </el-form-item>
        <el-form-item label="商品名称:">
          <el-input v-model.trim="goodsFilter.goodsName" placeholder="商品名称" @change="onSearch" />
        </el-form-item>
        <el-form-item label="运营组:">
          <el-select style="width:130px;" v-model="goodsFilter.groupId" placeholder="请选择" :clearable="true"
            :collapse-tags="true" @change="onSearch">
            <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="采购组:">
          <el-select style="width:130px;" v-model="goodsFilter.brandId" placeholder="请选择" :clearable="true"
            :collapse-tags="true" @change="onSearch">
            <el-option v-for="item in brandList" :key="item.key" :label="item.value" :value="item.key">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="存在变更记录:" label-width="120px">
          <el-switch v-model="goodsFilter.hasCostPriceChg" active-color="#13ce66" inactive-color="#ff4949"
            active-value="1" inactive-value="0" @change="onSearch">
          </el-switch>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
    <!--左边显示部分（显示角色信息）-->
    <el-row style="height:100%">
      <el-col :span="12" style="height:100%">
        <div class="grid-content bg-purple" style="height:100%">
          <!--列表-->
          <el-table ref="dataTreeList" v-loading="listLoading" @sort-change='sortchange' style="width:100%;"
            height="500px" :data="goods" border @row-click="GetRoleUsersinfo">
            <el-table-column type="index" width="50" label="#" />
            <el-table-column prop="goodsCode" sortable='custom' label="商品编码" width="180" />
            <el-table-column prop="goodsName" sortable label="商品名称" width="240" />
            <el-table-column prop="costPrice" sortable label="成本价" width="120" />
            <el-table-column prop="groupId" sortable label="运营组" width="120"
              :formatter='(row, column) => row.groupId == 0 ? " " : row.groupName' />
            <el-table-column prop="brandId" sortable label="成本组" width="120"
              :formatter='(row, column) => row.brandId == 0 ? " " : row.brandName' />
          </el-table>
        </div>
      </el-col>
      <el-col :span="12" style="height:100%">
        <div style="height:100%">
          <!--弹出显示成本变更记录-->
          <el-table :data="list" border style="width:100%;" height="500px" @cell-click="cellClick">
            <el-table-column type="index" width="50" label="#" />
            <el-table-column prop="checkDate" sortable label="变动时间" width="160"></el-table-column>
            <el-table-column prop="oldCostPrice" sortable label="原成本" width="100"></el-table-column>
            <el-table-column prop="newCostPrice" sortable label="新成本" width="100"></el-table-column>
            <el-table-column prop="diffPrice" sortable label="差价单价" width="100"></el-table-column>
            <el-table-column prop="count" sortable  label="采购量" width="100"></el-table-column>
            <el-table-column prop="diffTotal" sortable  label="差价总额" width="120"></el-table-column>
            <el-table-column prop="brandName" sortable  label="采购员" width="120"></el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>

    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getGoodsList" />
    </template>

    <!-- 成本价格变更详情 -->
    <el-dialog :visible.sync="dialogCostPriceChgVisible" width="80%" v-dialogDrag :show-close="false"
      @close="dialogCostPriceChgVisible = false">
      <goodsCostPriceChgDtl v-if="showChg"  :id="id" style="height: 550px">
      </goodsCostPriceChgDtl>
    </el-dialog>

  </my-container>
</template>

    
<script>
import { formatTime } from "@/utils";
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { getGroupKeyValue } from '@/api/operatemanage/base/product';
import { getAllProBrand } from '@/api/inventory/warehouse';
import goodsCostPriceChgDtl from '@/views/inventory/components/goodsCostPriceChgDtl.vue'

import {
  //分页查询店铺商品资料
  getList, GetCostPriceListByCodeAsync
} from "@/api/inventory/basicgoods"


export default {
  name: 'GoodsCostPriceChg',
  components: { cesTable, MyContainer, MyConfirmButton,goodsCostPriceChgDtl },
  data() {
    return {
      adduserFormVisible: false,
      userNameReadonly: true,
      showMenuList: [],
      dialogTableVisible: false,
      goods: [],
      goodsFilter: {
        goodsCode: null,
        goodsName: null,
        groupId: null,
        brandId: null,
        hasCostPriceChg: 0
      },
      groupList: [],
      brandList: [],
      list: [],
      total: 0,
      sels: [], // 列表选中列
      statusList: [
        { name: '激活', value: true },
        { name: '禁用', value: false }
      ],
      listLoading: false,
      pageLoading: false,
      addDialogFormVisible: false,
      editFormVisible: false, // 编辑界面是否显示
      editLoading: false,
      id: 0,
      editFormRules: {
        name: [{ required: true, message: '请输入角色名', trigger: 'blur' }],
        enabled: [{ required: true, message: '请输入状态', trigger: 'change' }]
      },
      // 编辑界面数据
      editForm: {
        id: 0,
        name: '',
        description: '',
        enabled: '',
        menuId: "",
        menuIdList: "",
        parentId: "",
        hierarchyCode: ""
      },
      editFormRef: null,
      addFormVisiblerole: false, // 新增界面是否显示
      addLoading: false,
      addFormRules: {
        name: [{ required: true, message: '请输入角色名', trigger: 'blur' }],
        enabled: [{ required: true, message: '请输入状态', trigger: 'change' }]
      },
      // 新增界面数据
      addForm: {
        name: '',
        description: '',
        enabled: true,
        menuId: "",
        menuIdList: "",
        parentId: "",
        hierarchyCode: ""
      },
      addFormRef: null,
      deleteLoading: false,
      dialogCostPriceChgVisible: false,
      showChg: false,
      autoform: {
        fApi: {},
        options: {
          submitBtn: false, form: { labelWidth: '145px' },
          global: { '*': { props: { disabled: false }, col: { span: 8 } } },
          rule: [{ type: 'hidden', field: 'roleid', title: 'id', value: '' },

          {
            type: 'select', field: 'userId', title: '用户名', validate: [{ type: 'number', required: true, message: '请选择用户' }], value: 0, options: [],
            props: { filterable: true, allowCreate: false, clearable: true, remote: true, remoteMethod: (parm) => this.remoteSearchUser(parm) }
          }
          ],
        },
      },
      isExpansion: true
    }
  },
  async mounted() {
    await this.setGroupSelect();
    await this.setBandSelect();
    await this.getGoodsList();
  },
  beforeUpdate() {

  },
  created() {
  },
  methods: {
    //排序查询
    async sortchange(column) {
      if (!column.order) this.pager = {};
      else {
        this.pager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      }
      await this.onSearch();
    },
    cellClick(row, column, cell, event) {
      this.id = row.id;
      //打开页面
      this.showChg = false;
      this.dialogCostPriceChgVisible = true;
      this.$nextTick(() => {
        this.showChg = true;
      });

    },
    selsChange: function (sels) {
      this.sels = sels
    },
    async setGroupSelect() {
      const res = await getGroupKeyValue({});
      this.groupList = res.data;
    },
    async setBandSelect() {
      var res = await getAllProBrand();
      if (!res?.success) return;
      this.brandList = res.data;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getGoodsList()
    },
    //根据角色id获取该角色的用户信息
    async GetRoleUsersinfo(row, column, e) {
      //展开行
      this.dialogTableVisible = true;
      let list = await GetCostPriceListByCodeAsync({ goodsCode: row.goodsCode })
      this.list = list.data
    },
    formatCreatedTime: function (row, column, time) {
      return formatTime(time, 'YYYY-MM-DD')
    },
    //获取查询条件
    getCondition() {
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = { ...pager, ...page, ... this.goodsFilter }
      return params;
    },
    // 获取商品编码列表
    async getGoodsList() {
      this.listLoading = true

      let params = this.getCondition();
      if (params === false) {
        return;
      }
      const res = await getList(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      // data.forEach(d => {
      //   d._loading = false
      // })
      this.goods = res.data.list
    },
    closeEditForm() {
      this.$refs.editForm.resetFields()
    },
  },
}
</script>