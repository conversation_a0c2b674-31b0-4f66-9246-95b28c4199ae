<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :inline="true" :label-position="labelPosition" :model="form" label-width="100px"
                :disabled="!formEditMode" class="overtexc" style="font-size:12px;">
                <el-row style="z-index: 1;">
                    <!-- //图片 -->
                    <el-col :span="5.5" class="imgcss">
                        <el-form-item label="竞品图片：" style="z-index: 100;">
                            <el-image style="width: 172px; height: 172px" width="200px" height="200px" class="formimg"
                                :src="form.goodsCompeteImgUrl" :preview-src-list="[form.goodsCompeteImgUrl]">
                            </el-image>
                        </el-form-item>
                    </el-col>
                    <el-col :span="19" type="flex" justify="start " style="position: relative; z-index: 99;">
                        <el-row>
                            <el-col :span="6" type="flex" justify="start " :offset="1">
                                <el-form-item label="竞品平台：" type="flex" justify="center">
                                    <el-select disabled v-model="form.platform" style="width:172px; height: 29px;">
                                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                                            :value="item.value"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="7" type="flex" justify="start ">
                                <el-form-item label="运营组：" :rules="[{ required: true, message: '请选择运营组：', trigger: ['blur', 'change'] }]">
                                    <!-- {{ form.yyGroupName }} -->
                                    <el-select style="width:160px;" v-model="form.yyGroupId" placeholder="请选择" :clearable="true" filterable :collapse-tags="true" @change="onSearch">
                                        <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="6" type="flex" justify="start " :offset="1">
                                <el-form-item label="竞品ID：" class="left-align">
                                    <div v-html="formatLinkProCode(form.platform, form.goodsCompeteId)"></div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6.5" type="flex" justify="start ">
                                <el-form-item label="竞品标题：">
                                    <!-- <div style="overflow: hidden;">
                            {{ form.goodsCompeteName}}
                          </div> -->
                                    <el-tooltip effect="dark" :content="form.goodsCompeteName"
                                        placement="top">
                                        <div class="overtext" style="overflow: hidden;width:172px; height: 29px">
                                            {{ form.goodsCompeteName }}
                                        </div>
                                    </el-tooltip>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6" type="flex" justify="start ">
                                <el-form-item label="项目：">
                                    <el-select v-model="form.projectDept" style="width:172px; height: 29px"
                                        placeholder="请选择项目" clearable>
                                        <el-option label="项目部-大侠" value="项目部-大侠"/>
                                        <el-option label="项目部-徐琛" value="项目部-徐琛"/>
                                        <el-option label="项目部-左玉玲" value="项目部-左玉玲"/>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="6" type="flex" justify="start " :offset="1">
                                <el-form-item prop="goodsCompeteShortName" label="产品简称：">
                                    <el-input v-model="form.goodsCompeteShortName" auto-complete="off"
                                        placeholder="请输入产品简称" style="width:172px; height: 29px" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="6.5" type="flex" justify="start ">
                                <el-form-item prop="goodsState" label="产品状态：">
                                    <el-select v-model="form.goodsState" style="width:172px; height: 29px"
                                        placeholder="请选择产品状态">
                                        <el-option v-for="item in goodsStatelist" :key="item.value" :label="item.label"
                                            :value="item.value"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="5.5" type="flex" justify="start ">
                                <el-form :inline="true" :label-position="labelPosition" :model="form"
                                    label-width="101px" :disabled="!formEditMode">
                                    <el-form-item prop="outerPackaLanguage" label="外包装语言：">
                                        <el-select v-model="form.outerPackaLanguage" style="width:154px; height: 29px"
                                            :disabled="form.refType == 2 && oldformOuterPackaLanguage > 0"
                                            placeholder="请选择外包装语言">
                                            <el-option label="中文" :value="1"></el-option>
                                            <el-option label="英文" :value="2"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-form>
                            </el-col>
                            <el-col :span="5.5" type="flex" justify="start ">
                                <el-form-item label="是否拍摄：" prop="neeShootingTask">
                                    <el-select v-model="form.neeShootingTask" style="width:154px; height: 29px"
                                        placeholder="请选择是否拍摄">
                                        <el-option label="是" :value="1"></el-option>
                                        <el-option label="否" :value="0"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="6" type="flex" justify="start " :offset="1">
                                <el-form-item prop="forNewWarehouse" label="上新仓库：">
                                    <el-select v-model="form.forNewWarehouse" style="width: 172px ;height: 29px"
                                        placeholder="请输入上新仓库">
                                        <!-- <el-option v-for="item in fomSendWarehouse4HotGoodsBuildGoodsDocList" :key="item.value" :label="item.label" :value="item.value"/> -->
                                        <el-option v-for="item in fomSendWarehouse4HotGoodsBuildGoodsDocList"
                                            :key="item.name" :label="item.name" :value="item.wms_co_id" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6.5" type="flex" justify="start ">
                                <el-form-item prop="delayedPurchase" label="延时进货：">
                                    <el-select v-model="form.delayedPurchase" style="width:172px; height: 29px"
                                        placeholder="请选择是否延时进货">
                                        <el-option
                                            v-if="form.newPlatform == 2 || form.newPlatform == 12 || form.newPlatform == 13"
                                            label="是" :value="1"></el-option>
                                        <el-option label="否" :value="0"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="5.5" type="flex" justify="start ">
                                <el-form-item prop="labels2List" label="编码标签：" :rules="[
                                    { required: true, message: '请选择编码标签', trigger: ['blur', 'change'] }
                                ]">
                                    <el-select v-model="form.labels2List" style="width:154px; height: 29px" multiple
                                        collapse-tags clearable placeholder="请选择编码标签">
                                        <el-option key="正常" label="正常" value="正常"></el-option>
                                        <el-option key="含电池" label="含电池" value="含电池"></el-option>
                                        <el-option key="液体" label="液体" value="液体"></el-option>
                                        <el-option key="粉末" label="粉末" value="粉末"></el-option>
                                        <el-option key="昌东-压力罐" label="昌东-压力罐" value="昌东-压力罐"></el-option>
                                        <el-option key="刀" label="刀" value="刀"></el-option>
                                        <el-option key="点火器" label="点火器" value="点火器"></el-option>
                                        <el-option key="【义乌跨境仓】" label="【义乌跨境仓】" value="【义乌跨境仓】"></el-option>
                                        <el-option key="跨境专用" label="跨境专用" value="跨境专用"></el-option>
                                        <el-option key="昀晗-跨境仓" label="昀晗-跨境仓" value="昀晗-跨境仓"></el-option>
                                        <el-option key="*跨境*" label="*跨境*" value="*跨境*"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="5.5" type="flex" justify="start ">
                                <el-form-item prop="isNeedSubmission" label="产品送检：" :rules="[
                                    { required: true, message: '请选择是否产品送检', trigger: ['blur', 'change'] }
                                ]">
                                    <el-select v-model="form.isNeedSubmission" style="width:154px; height: 29px"
                                        collapse-tags clearable placeholder="是否产品送检：">
                                        <el-option key="是" label="是" :value="1"></el-option>
                                        <el-option key="否" label="否" :value="0"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <!-- <el-col :span="6" type="flex" justify="start " :offset="1">
                                <el-form-item prop="styleTag" label="款式标签：" :rules="[
                                    { required: true, message: '请选择款式标签：', trigger: ['blur', 'change'] }
                                ]">
                                    <el-select v-model="styleTagList" style="width: 172px ;height: 29px" @change="changeTag"
                                        multiple collapse-tags placeholder="款式标签">
                                        <el-option label="春季款" :value="'春季款'" />
                                        <el-option label="夏季款" :value="'夏季款'" />
                                        <el-option label="秋季款" :value="'秋季款'" />
                                        <el-option label="冬季款" :value="'冬季款'" />
                                        <el-option label="春节款" :value="'春节款'" />
                                        <el-option label="开学季款" :value="'开学季款'" />
                                        <el-option label="强季款" :value="'强季款'" />
                                        <el-option label="四季款" :value="'四季款'" />
                                    </el-select>
                                </el-form-item>
                            </el-col> -->
                            <el-col :span="6" type="flex" justify="start " :offset="1">
                                <el-form-item prop="attributeTag" label="属性" :rules="[
                                    { required: true, message: '请选择属性：', trigger: ['blur', 'change'] }
                                ]">
                                    <el-select v-model="form.attributeTag" style="width: 172px ;height: 29px"
                                        placeholder="属性">
                                        <el-option label="常规款" :value="'常规款'" />
                                        <el-option label="定制款" :value="'定制款'" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6.5" type="flex" justify="start ">
                                <el-form-item prop="seasonOrFestivalTag" label="季节/节日" :rules="[
                                    { required: true, message: '请选择季节/节日标签：', trigger: ['blur', 'change'] }
                                ]">
                                    <el-select v-model="form.seasonOrFestivalTag" style="width: 172px ;height: 29px"
                                        placeholder="季节/节日">
                                        <el-option v-for="item in seasonList" :key="item.value" :label="item.label"
                                            :value="item.value" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="5.5" type="flex" justify="start ">
                                <el-form-item prop="weatherTag" label="天气" :rules="[
                                    { required: true, message: '请选择天气标签：', trigger: ['blur', 'change'] }
                                ]">
                                    <el-select v-model="form.weatherTag" style="width: 154px ;height: 29px"
                                        placeholder="天气">
                                        <el-option label="阳光" value="阳光" />
                                        <el-option label="雨水" value="雨水" />
                                        <el-option label="冰雪" value="冰雪" />
                                        <el-option label="台风" value="台风" />
                                        <el-option label="无" value="无" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="5.5" type="flex" justify="start ">
                                <el-form-item prop="temperatureTag" label="温度：" :rules="[
                                    { required: true, message: '请选择温度标签：', trigger: ['blur', 'change'] }
                                ]">
                                    <el-select v-model="form.temperatureTag" style="width: 154px ;height: 29px"
                                        placeholder="温度标签">
                                        <el-option v-for="item in temperatureList" :key="item.value" :label="item.label"
                                            :value="item.value" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col v-if="form.labels2List?.some(item => item.includes('压力罐'))" :span="6" type="flex"
                                justify="start " :offset="1">
                                <el-form-item prop="ingredient" label="产品成分" :rules="[
                                    { required: true, message: '请输入产品成分', trigger: ['blur', 'change'] }
                                ]">
                                    <el-input v-model="form.ingredient" auto-complete="off" placeholder="产品成分"
                                        style="width:172px; height: 29px" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="3">
                        <el-form-item label="质检报告">
                            <YhImgUpload2Table :value.sync="form.inspectionReportImgUrl">
                            </YhImgUpload2Table>
                        </el-form-item>
                    </el-col>

                    <el-col :span="4">
                        <el-form-item label="质检报告文件">
                            <YhImgUpload :value.sync="form.inspectionReportPdfUrls" :isImg="false"
                                accept=".pdf,.xlsx,.docx" :limit="11"></YhImgUpload>
                        </el-form-item>
                    </el-col>

                    <el-col :span="4">
                        <el-form-item label="专利资质">
                            <YhImgUpload :value.sync="form.patentQualificationImgUrls" :limit="10"></YhImgUpload>
                        </el-form-item>
                    </el-col>

                    <el-col :span="4">
                        <el-form-item label="专利资质PDF" label-width="140px" class="commonstyle">
                            <YhImgUpload :value.sync="form.patentQualificationPdfUrls" :isImg="false" accept=".pdf"
                                :limit="11"></YhImgUpload>
                        </el-form-item>
                    </el-col>

                    <el-col :span="3">
                        <el-form-item label="报价凭证">
                            <el-badge :value="form.voucherImgUrls.length"
                                :hidden="!(form.voucherImgUrls && form.voucherImgUrls.length > 0)" style="width: 280px;"
                                :limit="10" class="item">
                                <el-image style="width: 50px; height: 50px" :src="form.voucherImgUrl"
                                    :preview-src-list="form.voucherImgUrls" fit="scale-down" :lazy="true">
                                </el-image>
                            </el-badge>
                        </el-form-item>
                    </el-col>

                    <el-col :span="4">
                        <el-form-item label="包装图片">
                            <YhImgUpload :value.sync="form.packingImgUrls" :limit="10">
                            </YhImgUpload>
                        </el-form-item>
                    </el-col>

                </el-row>
                <el-row v-if="form.supplierList && form.supplierList.length > 0 && form.isGroupMember">
                    <el-col :span="24">
                        <el-row :gutter="24">
                            <el-col :span="4">
                                <el-form-item prop="supplierPlatForm" label="供应商平台：">
                                    <el-select v-model="calcCurSupplier.supplierPlatForm" style="width:100%;" disabled>
                                        <el-option v-for="item in supplierPlatFormList" :key="item.value"
                                            :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="4">
                                <el-form-item prop="supplierName" label="供应商名称：">
                                    <el-select v-model="calcCurSupplier.supplierName" style="width:100%;"
                                        disabled></el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="4" :hidden="(calcCurSupplier.supplierPlatForm != 2)">
                                <el-form-item label="供应商链接：">
                                    <el-select v-model="calcCurSupplier.supplierLink" style="width:100%;"
                                        disabled></el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="4" :hidden="(calcCurSupplier.supplierPlatForm != 2)">
                                <el-form-item prop="supplierGoodLink" label="产品链接：">
                                    <el-select v-model="calcCurSupplier.supplierGoodLink" style="width:100%;"
                                        disabled></el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="4">
                                <el-form-item prop="remark" label="备注">
                                    <el-input v-model.trim="form.remark" style="width:100%;" maxlength="200"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-col>
                </el-row>
                <!-- <el-row>
                    <el-col :span="8"  v-if="form.supplierList && form.supplierList.length>0 && calcCurSupplier.hasProductionLicense==1">
                        <el-form-item prop="hasProductionLicense" label="生产许可证：" style="float: left;">
                            <el-image style="margin-left:10px;width: 50px; height: 50px" :src="calcCurSupplier.productionLicenseUrl" :preview-src-list="[calcCurSupplier.productionLicenseUrl]">
                            </el-image>
                        </el-form-item>
                    </el-col>
                </el-row> -->
                <el-row v-show="form.isGroupMember">
                    <el-col :span="24">
                        <el-form-item prop="chatImgUrls" label="供应商聊天记录：" label-width="140px">
                            <div style="width: 1300px;">
                                <uploadfile ref="uploadChatImg" :islook="!formEditMode" :minisize="true"
                                    style="width: 140px;" placeholder="请上传供应商聊天记录"
                                    :uploadInfo="form.chatImgUrls == null ? [] : form.chatImgUrls" :limit="10000"
                                    :accepttyes="'.image/jpg,image/jpeg,image/png'" uploadVerifyType="图片" />
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form style="float:left">
                            <el-radio-group v-model="curSupplierId" size="mini" v-if="form.isGroupMember">
                                <el-radio-button v-for="item in form.supplierList" :label="item.id" :key="item.id">
                                    {{ item.supplierName }}
                                </el-radio-button>
                            </el-radio-group>
                        </el-form>
                        <span style="display:none">
                            {{ calcEstimateStockInCount }}
                            {{ calcEstimateStockInAmount }}
                        </span>
                        <el-button-group style="margin-left:10px;">
                            <el-button type="primary" @click="onOpenAddSupplier"
                                v-if="form.isGroupMember">添加供应商</el-button>
                            <el-button type="primary" @click="onOpenEditSupplier"
                                v-if="form.supplierList && form.supplierList.length > 0 && form.isGroupMember">修改供应商</el-button>
                            <el-button type="danger" @click="onDelSupplier"
                                v-if="form.supplierList && form.supplierList.length > 0 && form.isGroupMember">删除当前供应商</el-button>
                            <el-button v-if="form.supplierList && form.supplierList.length > 0" type="primary"
                                @click="skuTableData.push({ id: rowTempIndex--, supplierId: curSupplierId, goodsProgressType: '成品', isMainSale: true, isZengPin: false, isJiaGong: false });">新增一行</el-button>
                            <el-button type="primary" @click="onBatchImport">批量导入</el-button>
                        </el-button-group>
                        <div v-if="form.supplierList && form.supplierList.length > 0"
                            :style="'height:' + tableHeight + 'px;'">
                            <!--列表-->
                            <ces-table ref="skutable" :showsummary="true" :summaryarry="summaryarry" :that='that'
                                :isIndex='false' :hasexpandRight='true' :hasexpand='true' :tableData='calcSkuTableData'
                                :tableCols='skuTableCols' :loading="listLoading" :isSelectColumn="false" rowkey="id">

                                <template>
                                    <el-table-column width="90" label="商品图片" :render-header="renderHeader"
                                        column-key="id">
                                        <template slot-scope="scope">
                                            <YhImgUpload2Table :value.sync="scope.row.goodsImageUrl">
                                            </YhImgUpload2Table>
                                        </template>
                                    </el-table-column>
                                </template>
                                <!-- center from jsonCols array  -->
                                <template slot="right">
                                    <el-table-column width="230" label="长宽高（cm）" :render-header="renderHeader">
                                        <template slot-scope="scope">
                                            <el-input-number type="number" :precision="2" :min="0" :max="10000"
                                                :controls="false" v-model.number="scope.row.goodsLength"
                                                style="width:50px;">
                                            </el-input-number>*
                                            <el-input-number type="number" :precision="2" :min="0" :max="10000"
                                                :controls="false" v-model.number="scope.row.goodsWidth"
                                                style="width:50px;">
                                            </el-input-number>*
                                            <el-input-number type="number" :precision="2" :min="0" :max="10000"
                                                :controls="false" v-model.number="scope.row.goodsHeigdth"
                                                style="width:50px;">
                                            </el-input-number>
                                            <el-button type="text" @click="batchSetRowHeighetWidth(scope.row)">
                                                批量
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                    <el-table-column width="100" label="重量（kg）" :render-header="renderHeader">
                                        <template slot-scope="scope">
                                            <el-input-number type="number" :precision="2" :min="0" :max="10000"
                                                :controls="false" v-model.number="scope.row.goodsWeight"
                                                style="width:50px;">
                                            </el-input-number>
                                            <el-button type="text" @click="batchSetRowWeight(scope.row)">
                                                批量
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                    <el-table-column width="140" label="备注">
                                        <template slot-scope="scope">
                                            <el-input type="text" :maxlength="100" :controls="false"
                                                v-model="scope.row.remark" style="width:130px;">
                                            </el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column width="50" label="">
                                        <template slot-scope="scope">
                                            <el-button type="text"
                                                @click="scope.row.id = -999999 + (rowTempIndex--);">删除</el-button>
                                        </template>
                                    </el-table-column>
                                </template>
                            </ces-table>
                        </div>
                    </el-col>
                </el-row>
            </el-form>
        </template>

        <el-dialog :title="dialogTitle" :visible.sync="supOpt.visible" width="780px" :close-on-click-modal="false"
            append-to-body v-dialogDrag>

            <el-form :model="supOpt.form" :rules="supOpt.rules" label-width="140px" label-position="right">
                <el-row>
                    <el-col :span="24">
                        <el-form-item prop="supplierPlatForm" label="供应商平台：">
                            <el-select v-model="supOpt.form.supplierPlatForm">
                                <el-option v-for="item in supplierPlatFormList" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" :hidden="(supOpt.form.supplierPlatForm != 1)">
                        <el-form-item prop="supplierWxNum" label="微信账号：">
                            <!-- {{ supOpt.form.supplierWxNum}} -->
                            <el-input v-model.trim="supOpt.form.supplierWxNum" maxlength="30"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="supplierName" label="供应商名称：">
                            <!-- {{ supOpt.form.supplierName}} -->
                            <el-input v-model.trim="supOpt.form.supplierName" maxlength="30"
                                style="width:100%;"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" :hidden="(supOpt.form.supplierPlatForm != 2)">
                        <el-form-item prop="supplierLink" label="供应商链接：" style="width:100%;">
                            <!-- {{ supOpt.form.supplierLink}} -->
                            <el-input v-model.trim="supOpt.form.supplierLink" maxlength="1000"
                                style="width:100%;"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" :hidden="(supOpt.form.supplierPlatForm != 2)">
                        <el-form-item prop="supplierGoodLink" label="产品链接：" style="width:100%;">
                            <!-- {{ supOpt.form.supplierGoodLink}} -->
                            <el-input v-model.trim="supOpt.form.supplierGoodLink" maxlength="1000"
                                style="width:100%;"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col>
                        <el-form-item prop="hasProductionLicense" label="生产许可证：" style="float: left;">
                            <el-select v-model="supOpt.form.hasProductionLicense" style="width:200px;">
                                <el-option label="无" :value="0"></el-option>
                                <el-option label="有" :value="1"></el-option>
                            </el-select>
                            <div v-if="supOpt.form.hasProductionLicense == 1" class="el-form-item__error"
                                style="color:red; width: 200px; margin-top: 5px;">食品级产品需食品级检测报告和生产许可证，且需与供应商名称、地址一致
                            </div>
                        </el-form-item>
                        <YhImgUpload2Table v-if="supOpt.form.hasProductionLicense == 1"
                            style="margin-left: 20px;float: left;" :value.sync="supOpt.form.productionLicenseUrl">
                        </YhImgUpload2Table>
                    </el-col>
                </el-row>
            </el-form>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="supOpt.visible = false">取 消</el-button>
                    <el-button type="primary" @click="onAddSupplier" v-if="!supOpt.isEdit">添 加</el-button>
                    <el-button type="primary" @click="onSaveEditSupplier" v-if="supOpt.isEdit">保 存</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="批量导入数据" :visible.sync="batchImportVisible" width="30%" v-dialogDrag
            :close-on-click-modal="false" append-to-body>
            <span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :file-list="batchfileList" :data="batchfileparm" :http-request="onUploadFile"
                    :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="batchuploadLoading"
                        @click="onSubmitUpload">{{ (batchuploadLoading ? '上传中' : '上传') }}</el-button>
                    <el-button style="margin-left: 10px" size="small" type="primary"
                        @click="onDownloadTemplate">下载模版</el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="batchImportVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import uploadfile from '@/components/Comm/uploadfile.vue';
import formCreate from '@form-create/element-ui';
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import { formatmoney, formatPercen, platformlist, setStore, getStore, formatLinkProCode, sendWarehouse4HotGoodsBuildGoodsDocList } from "@/utils/tools";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import {
    getBuildGoodsDocAsync, getBuildGoodsDocOtherAsync, buildGoodsDocAsync, GetSnowflakeId, importHotSaleGoodsBuildGoodsDtl
} from '@/api/operatemanage/productalllink/alllink';
import { getAllWarehouse } from '@/api/inventory/warehouse'
import FcEditor from "@form-create/component-wangeditor";
import { getGroupKeyValue } from '@/api/operatemanage/base/product';
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
import YhImgUpload2Table from '@/components/upload/yh-img-upload2Table.vue';
import { number } from 'echarts';
import ret from 'bluebird/js/release/util';
//const process = process;
function rowChanged4EstimateStockInAmount(x) {
    //let x = row;
    let estimateStockInCount = isNaN(x.estimateStockInCount) ? 0 : x.estimateStockInCount;
    let costPrice = isNaN(x.costPrice) ? 0 : x.costPrice;
    x.estimateStockInAmount = (estimateStockInCount * costPrice);
}

const skuTableCols = [

    //{ istrue: true, prop: 'yhGoodsCode', type: "inputtext", label: '商品编码', width: '180', maxlength: 30, isRequired: true },
    { istrue: true, prop: 'yhGoodsName', type: "inputtext", label: '商品名称', minwidth: '200', maxlength: 40, isRequired: true },
    //{ istrue: true, prop: 'yhGoodsUnit', type: "inputtext", label: '品名单位', width: '120', maxlength: 5, isRequired: true },
    { istrue: true, prop: 'costPrice', type: "inputnumber", precision: 3, label: '成本单价', width: '90', min: 0, max: 900000, isRequired: true, change: rowChanged4EstimateStockInAmount },//
    { istrue: true, prop: 'forNewWarehouse', type: "select", label: '上新仓库', width: '100', isRequired: true, display: false, options: sendWarehouse4HotGoodsBuildGoodsDocList },
    { istrue: true, prop: 'goodsProgressType', type: "select", label: '商品类型', width: '90', isRequired: true, options: [{ label: '成品', value: '成品' }, { label: '半成品', value: '半成品' }] },
    { istrue: true, prop: 'isMainSale', type: "select", label: '是否主卖', width: '80', isRequired: true, options: [{ label: '是', value: true }, { label: '否', value: false }] },
    { istrue: true, prop: 'mainSaleAvgCount', type: "inputnumber", label: '主卖人均件数', width: '80', min: 0, max: 10000, isRequired: true },
    { istrue: true, prop: 'estimateStockInCount', type: "inputnumber", label: '预计进货数量', width: '100', min: 0, max: 900000, isRequired: true, change: rowChanged4EstimateStockInAmount },
    { istrue: true, prop: 'estimateStockInAmount', type: "inputnumber", precision: 2, label: '预计进货金额', width: '100', min: 0, isRequired: true, max: 999999999 },
    { istrue: true, prop: 'isZengPin', type: "select", label: '有无赠品', width: '80', options: [{ label: '有', value: true }, { label: '无', value: false }] },
    { istrue: true, prop: 'isJiaGong', type: "select", label: '是否加工', width: '80', options: [{ label: '是', value: true }, { label: '否', value: false }] }
];
const seasonList = [
    { label: '四季款（常年）', value: '四季款（常年）' },
    { label: '春季款（1月1日-4月底）', value: '春季款（1月1日-4月底）' },
    { label: '夏季款（3月15日-9月底）', value: '夏季款（3月15日-9月底）' },
    { label: '秋季款（8月15日-10月底）', value: '秋季款（8月15日-10月底）' },
    { label: '冬季款（9月15日-1月底）', value: '冬季款（9月15日-1月底）' },
    { label: '开学季（1月1日至2月底）', value: '开学季（1月1日至2月底）' },
    { label: '开学季（7月1日至8月底）', value: '开学季（7月1日至8月底）' },
    { label: '清明节（3月1日至3月底）', value: '清明节（3月1日至3月底）' },
    { label: '端午节（农历四月初五至四月底）', value: '端午节（农历四月初五至四月底）' },
    { label: '中秋节（农历七月十五至八月初十）', value: '中秋节（农历七月十五至八月初十）' },
    { label: '国庆节（9月1日至9月25日）', value: '国庆节（9月1日至9月25日）' },
    { label: '圣诞节（不允许进货）', value: '圣诞节（不允许进货）' },
    { label: '元旦节（农历十一月初一至腊月十五）', value: '元旦节（农历十一月初一至腊月十五）' },
    { label: '春节（农历十一月初一至腊月十五）', value: '春节（农历十一月初一至腊月十五）' }
]
const temperatureList = [
    { label: '酷热（38度以上）', value: '酷热（38度以上）' },
    { label: '炎热（35到37度）', value: '炎热（35到37度）' },
    { label: '闷热（28到35度）', value: '闷热（28到35度）' },
    { label: '温暖（10到27度）', value: '温暖（10到27度）' },
    { label: '凉爽（0到9度）', value: '凉爽（0到9度）' },
    { label: '寒冷（-1到-9度）', value: '寒冷（-1到-9度）' },
    { label: '严寒（低于-10度）', value: '严寒（低于-10度）' },
    { label: '无', value: '无' }
]
export default {
    name: "hotsalegoodsbuildgoodsdoc",
    components: { MyContainer, MyConfirmButton, cesTable, YhImgUpload, uploadfile, YhImgUpload2Table },
    data() {
        return {
            batchImportVisible: false,//批量导入弹窗
            batchuploadLoading: false,//批量导入上传loading
            batchfileList: [],//批量导入文件列表
            batchfileparm: {},//批量导入文件参数
            temperatureList,
            seasonList,
            labelPosition: 'left',

            //: process,
            that: this,
            supplierPlatFormList: [{ value: 1, label: "微信" }, { value: 2, label: "1688" }],
            //fomSendWarehouse4HotGoodsBuildGoodsDocList:sendWarehouse4HotGoodsBuildGoodsDocList,
            fomSendWarehouse4HotGoodsBuildGoodsDocList: [],
            form: {
                sampleImgUrls: [],
                voucherImgUrls: [],
                labels2List: [],
                attributeTag: null,
                seasonOrFestivalTag: null,
                weatherTag: null,
                temperatureTag: null,
                // styleTagList: [],
            },
            oldformOuterPackaLanguage: null,
            goodsStatelist: [
                { label: '新品', value: 1 },
                { label: '老品补SKU', value: 2 },
                { label: '代拍', value: 3 },
            ],
            styleTagList: [],
            // thisformRules: {
            //     goodsCompeteShortName: [{ required: true, message: '请输入产品简称', trigger: 'blur' }],
            //     forNewWarehouse: [{ required: true, message: '请输入上新仓库', trigger: 'blur' }],
            //     neeShootingTask: [{ required: true, message: '请选择是否拍摄', trigger: 'change' }],
            //     goodsState: [{ required: true, message: '请选择产品状态' }],
            //     delayedPurchase:[ { required: true, message: '请选择是否延时进货' }],
            //     outerPackaLanguage:[ { required: true, message: '请选择外包装语言' }],
            //     chatImgUrls:[ { required: true, message: '请上传供应商聊天记录' }],
            // },

            rowTempIndex: -1,
            skuTableCols: skuTableCols,
            platformlist: platformlist,

            skuTableData: [],
            summaryarry: {
                'estimateStockInCount_sum': 0,
                'estimateStockInAmount_sum': 0
            },
            listLoading: false,
            pageLoading: false,
            goodschoiceVisible: false,//选择商品显隐
            curRow: null,
            curCol: "",
            formEditMode: true,//是否编辑模式

            selfInfo: {

            },
            curSupplierId: null,//当前选中的供应商
            supOpt: {
                visible: false,
                isEdit: false,
                form: {
                    supplierPlatForm: 2,
                    supplierWxNum: "",
                    supplierName: "",
                    supplierLink: "",
                    supplierGoodLink: "",
                    remark: "",
                    chatImgUrls: []
                },
                rules: {
                    supplierPlatForm: [{ required: true, message: '平台必填', trigger: 'change' }],
                    supplierName: [{ required: true, message: '请填写供应商', trigger: 'blur' }],
                    supplierLink: [{ required: true, message: '请填写供应商链接', trigger: 'blur' }],
                    supplierWxNum: [{ required: true, message: '请填写微信账号', trigger: 'blur' }],
                    supplierGoodLink: [{ required: true, message: '请填写产品链接', trigger: 'blur' }]
                }
            },
            dialogTitle: '',
            batchSet: {
                form: {
                    goodsLength: 0,
                    goodsWidth: 0,
                    goodsHeigdth: 0,
                    goodsWeight: 0,
                    remark: ""
                },
            },
            groupList: [],
        };
    },
    async mounted() {
        const res = await getGroupKeyValue({});
        let gList = res?.data??[];
        gList.forEach((item)=>{
            if(item.key){
                this.groupList.push({
                    key: Number(item.key),
                    value: item.value
                });
            }
        })
        formCreate.component('editor', FcEditor);
        await this.init();
        const userInfoName = "hotsalegoods_selfuserinfo";
        let selfInfo4Store = getStore(userInfoName);
        if (selfInfo4Store) {
            this.selfInfo = selfInfo4Store;
        }

    },
    computed: {
        calcCurSupplier: function () {
            if (this.form.supplierList && this.form.supplierList.length > 0) {
                let sp = this.form.supplierList.find(x => x.id == this.curSupplierId);
                if (sp) {
                    return sp;
                }

            }

            return {
                id: '',
                supplierName: '未知',
                bldDocId: "",
                createdTime: null,
                enabled: true,
                supplierGoodLink: null,
                supplierLink: null,
                supplierName: null,
                supplierPlatForm: 2,
                supplierWxNum: null,
            }
        },
        calcPatentQualificationImgUrl: function () {
            if (this.form.patentQualificationImgUrl) {
                let v = (process.env.NODE_ENV === 'development' ? 'http://192.168.90.12:8004/' : 'http://192.168.90.12:8001/') + this.form.patentQualificationImgUrl;

                return v;
            } else {
                return "";
            }

        },
        tableHeight: function () {
            let rowsCount = 1;
            if (this.calcSkuTableData && this.calcSkuTableData.length > 0) {
                rowsCount = this.calcSkuTableData.length;
            }
            let rowsHeight = (rowsCount + 2) * 50 + 80;
            return rowsHeight > 400 ? 400 : rowsHeight;
        },
        calcSkuTableData: function () {

            return this.skuTableData.filter(x => {
                if (x.id > -10000 && x.supplierId == this.curSupplierId) {
                    return true;
                }
                return false;
            });
        },
        calcSaveSkuTableData: function () {
            return this.skuTableData.filter(x => {
                if (x.id > -10000) {
                    return true;
                }
                return false;
            });
        },
        calcEstimateStockInCount: function () {
            let that = this;
            let estimateStockInCount_sum = 0;
            this.calcSaveSkuTableData.forEach((x) => {
                let estimateStockInCount = isNaN(x.estimateStockInCount) ? 0 : x.estimateStockInCount;
                estimateStockInCount_sum += estimateStockInCount
            });

            that.summaryarry.estimateStockInCount_sum = estimateStockInCount_sum.toFixed(0);
            return that.summaryarry.estimateStockInCount_sum;
        },
        calcEstimateStockInAmount: function () {
            let that = this;
            let estimateStockInAmount_sum = 0;
            this.calcSaveSkuTableData.forEach((x) => {
                let estimateStockInAmount = isNaN(x.estimateStockInAmount) ? 0 : x.estimateStockInAmount;
                estimateStockInAmount_sum += estimateStockInAmount;
            });

            that.summaryarry.estimateStockInAmount_sum = estimateStockInAmount_sum.toFixed(2);
            return that.summaryarry.estimateStockInAmount_sum;
        },
        calcSampleVolume: function () {
            let sampleVolume = ((isNaN(this.form.sampleLength) ? 0 : this.form.sampleLength) *
                (isNaN(this.form.sampleWidth) ? 0 : this.form.sampleWidth) *
                (isNaN(this.form.sampleHeigdth) ? 0 : this.form.sampleHeigdth)
            ).toFixed(2);
            this.form.sampleVolume = sampleVolume;
            return sampleVolume;
        }
    },
    methods: {
        onDownloadTemplate() {
            window.open("../../../static/excel/operateManage/批量新建编码导入模板.xlsx", "_self");
        },
        //批量上传文件
        onUploadRemove(file, fileList) {
            this.batchfileList = []
        },
        async onUploadChange(file, fileList) {
            this.batchfileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.batchfileList = [];
            this.batchImportVisible = false;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.batchuploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            var res = await importHotSaleGoodsBuildGoodsDtl(form);
            this.batchuploadLoading = false
            if (res?.success) {
                this.$message({ message: "上传成功", type: "success" });
            } else {
                this.$message({ message: "上传失败", type: "error" });
                return
            }
            let newData = res.data;
            newData.forEach((item) => {
                item.supplierId = this.curSupplierId;//当前供应商
                this.skuTableData.push(item);//添加到表格
            });
            this.batchImportVisible = false;
        },
        onSubmitUpload() {
            if (this.batchfileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        //导入弹窗
        onBatchImport() {
            this.batchfileList = []
            this.batchImportVisible = true;
        },
        changeTag(e) {
            this.form.styleTag = e.join(',');
        },
        async init() {
            var res3 = await getAllWarehouse();
            var warehouselist1 = res3.data;
            this.fomSendWarehouse4HotGoodsBuildGoodsDocList = warehouselist1;
        },
        formatLinkProCode: formatLinkProCode,
        formatTime: formatTime,
        async onOpenAddSupplier() {
            let getIdRlt = await GetSnowflakeId();
            if (getIdRlt && getIdRlt.success) {
                this.supOpt.form = {
                    id: getIdRlt.data,
                    supplierPlatForm: 2,
                    supplierWxNum: null,
                    supplierName: null,
                    supplierLink: null,
                    supplierGoodLink: null,
                    hasProductionLicense: 0
                };
                this.dialogTitle = "添加供应商"
                this.supOpt.visible = true;
                this.supOpt.isEdit = false;
            }

        },
        async onOpenEditSupplier() {
            let sp = this.form.supplierList.find(x => x.id == this.curSupplierId);
            if (sp && sp.id) {
                this.supOpt.form = { ...sp };
                this.dialogTitle = "修改供应商"
                this.supOpt.visible = true;
                this.supOpt.isEdit = true;
            }
            else {
                this.$alert("未选择可修改的供应商");
            }

        },
        onDelSupplier() {
            if (!this.form.supplierList || this.form.supplierList.length <= 1) {
                this.$alert("请至少保留一个供应商！");
                return false;
            }

            this.$confirm('删除供应商时将同步删除当前供应商下商品，是否确定?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let self = this;
                let idx = -1;
                for (let i = 0; i < self.form.supplierList.length; i++) {
                    if (self.form.supplierList[i].id == self.curSupplierId) {
                        idx = i;
                        break;
                    }
                }
                if (idx > -1) {
                    //删除供应商
                    self.form.supplierList.splice(idx, 1);
                    //删除商品
                    self.skuTableData.forEach(x => {
                        if (x.supplierId && x.supplierId == self.curSupplierId) {
                            x.id = -999999 + (self.rowTempIndex--);
                        }
                    })

                    this.curSupplierId = this.form.supplierList[0].id;
                }
            }).catch(() => {

            });
        },
        onSaveEditSupplier() {
            let sp = this.form.supplierList.find(x => x.id == this.curSupplierId);

            let supOpt = this.supOpt;
            let errMsg = "";
            if (!supOpt.form.supplierName) {
                errMsg += "请输入供应商名称;";
            } else {
                if (this.form.supplierList && this.form.supplierList.length > 0) {
                    let findSup = this.form.supplierList.find(x => x.supplierName == supOpt.form.supplierName && x.id != sp.id);
                    if (findSup && findSup.supplierName)
                        errMsg += "已存在同名供应商；";
                }
            }
            if (supOpt.form.supplierPlatForm == 1) {
                if (!supOpt.form.supplierWxNum) {
                    errMsg += "请输入微信账号;";
                }
            } else if (supOpt.form.supplierPlatForm == 2) {
                if (!supOpt.form.supplierLink) {
                    errMsg += "请输入供应商链接;";
                }
                if (!supOpt.form.supplierGoodLink) {
                    errMsg += "请输入产品链接;";
                }
            }
            if (supOpt.form.hasProductionLicense == 1 && !supOpt.form.productionLicenseUrl) {
                errMsg += "请上传生产许可证;";
            }
            if (errMsg) {
                this.$alert(errMsg);
                return false;
            }

            sp.supplierPlatForm = supOpt.form.supplierPlatForm;
            sp.supplierWxNum = supOpt.form.supplierWxNum;
            sp.supplierName = supOpt.form.supplierName;
            sp.supplierLink = supOpt.form.supplierLink;
            sp.supplierGoodLink = supOpt.form.supplierGoodLink;
            sp.hasProductionLicense = supOpt.form.hasProductionLicense;
            sp.productionLicenseUrl = supOpt.form.productionLicenseUrl;
            supOpt.visible = false;
        },
        async onAddSupplier() {
            let supOpt = this.supOpt;
            let errMsg = "";
            if (!supOpt.form.supplierName) {
                errMsg += "请输入供应商名称;";
            } else {
                if (this.form.supplierList && this.form.supplierList.length > 0) {
                    let findSup = this.form.supplierList.find(x => x.supplierName == supOpt.form.supplierName);
                    if (findSup && findSup.supplierName)
                        errMsg += "已存在同名供应商；";
                }
            }
            if (supOpt.form.supplierPlatForm == 1) {
                if (!supOpt.form.supplierWxNum) {
                    errMsg += "请输入微信账号;";
                }
            } else if (supOpt.form.supplierPlatForm == 2) {
                if (!supOpt.form.supplierLink) {
                    errMsg += "请输入供应商链接;";
                }
                if (!supOpt.form.supplierGoodLink) {
                    errMsg += "请输入产品链接;";
                }
            }
            if (supOpt.form.hasProductionLicense == 1 && !supOpt.form.productionLicenseUrl) {
                errMsg += "请上传生产许可证;";
            }
            if (errMsg) {
                this.$alert(errMsg);
                return false;
            }
            this.form.supplierList.push({ ...supOpt.form });
            this.curSupplierId = supOpt.form.id;

            //历史数据，可能存在没有供应商的，默认给这个供应商
            var nonSupplierGoods = this.skuTableData.filter(x => x.supplierId == undefined || x.supplierId == null || x.supplierId == 0);
            if (nonSupplierGoods && nonSupplierGoods.length > 0) {
                nonSupplierGoods.forEach(x => {
                    x.supplierId = supOpt.form.id;
                })
            }

            supOpt.visible = false;
        },
        // handleAvatarSuccess: function (response, file, fileList) {
        //     if (response && response.success && response.data.url) {
        //         this.form.patentQualificationImgUrl = response.data.relativePath;
        //     }
        // },
        rowChanged4EstimateStockInAmount: rowChanged4EstimateStockInAmount,
        async getSkuTableData(docId, formEditMode) {
            //this.form = { platform: null };
            let form = this.form;
            console.log(this.form, 'this.form');
            // 清空from对象

            Object.keys(form).forEach(key => {
                if (key == "sampleImgUrl" || key == "voucherImgUrl")
                    form[key] = "";
                else if (key == "sampleImgUrls" || key == "voucherImgUrls")
                    form[key] = [];
                else
                    form[key] = null;
            });
            this.form.sampleWeight = null;
            this.form.sampleLength = null;
            this.form.sampleWidth = null;
            this.form.sampleHeigdth = null;

            //处理编辑模式
            this.formEditMode = formEditMode;
            this.$refs.uploadChatImg.setData([]);
            this.listLoading = true;
            const res = await getBuildGoodsDocAsync({ docOrChooseId: docId });

            if (res && res.success && res.data.auditState && res.data.auditState > 0) {
                this.formEditMode = false;
                this.$emit("loaded", res.data);
            }


            const otherRes = await getBuildGoodsDocOtherAsync({ docId: docId });
            this.listLoading = false;

            let emptyImgs = {
                sampleImgUrl: "",
                sampleImgUrls: [],
                voucherImgUrl: "",
                voucherImgUrls: []
            };

            this.skuTableData = res?.data?.goodsDtlEntities;

            let tempData = { ...res?.data, ...emptyImgs };

            tempData.labels2List = [];
            if (tempData.labels2 && tempData.labels2.length > 0) {
                let splitlables2 = tempData.labels2.split(",");
                splitlables2.forEach(f => {
                    tempData.labels2List.push(f.toString());
                });
            }

            this.form = tempData;
            let styleTagList = [];
            if (tempData.styleTag && tempData.styleTag.length > 0) {
                let styleTag2 = tempData.styleTag.split(",");
                styleTag2.forEach(f => {
                    styleTagList.push(f.toString());
                });
            }
            this.styleTagList = styleTagList;


            if (this.form.supplierList && this.form.supplierList.length > 0)
                this.curSupplierId = this.form.supplierList[0].id;

            this.form.supplierName = otherRes?.data?.supplierName;
            this.form.supplierPlatForm = otherRes?.data?.supplierPlatForm;
            this.form.supplierWxNum = otherRes?.data?.supplierWxNum;
            this.form.supplierLink = otherRes?.data?.supplierLink;
            this.form.supplierGoodLink = otherRes?.data?.supplierGoodLink;

            if (otherRes?.data?.sampleImgUrls && otherRes?.data?.sampleImgUrls.length > 0) {
                this.form.sampleImgUrl = otherRes?.data?.sampleImgUrls[0];
                this.form.sampleImgUrls = otherRes?.data?.sampleImgUrls;
            } else {
                this.form.sampleImgUrl = '';
                this.form.sampleImgUrls = []
            }

            if (otherRes?.data?.voucherImgUrls && otherRes?.data?.voucherImgUrls.length > 0) {
                this.form.voucherImgUrl = otherRes?.data?.voucherImgUrls[0];
                this.form.voucherImgUrls = otherRes?.data?.voucherImgUrls;
            } else {
                this.form.voucherImgUrl = '';
                this.form.voucherImgUrls = []
            }
            if (!this.form.sampleWeight) {
                this.form.sampleWeight = otherRes?.data?.sampleWeight;
            }
            if (!this.form.sampleLength && !this.form.sampleWidth && !this.form.sampleHeigdth) {
                this.form.sampleLength = otherRes?.data?.sampleLength;
                this.form.sampleWidth = otherRes?.data?.sampleWidth;
                this.form.sampleHeigdth = otherRes?.data?.sampleHeigdth;
            }
            this.oldformOuterPackaLanguage = this.form.outerPackaLanguage;
            this.$refs.uploadChatImg.setData(this.form.chatImgUrls == null ? [] : this.form.chatImgUrls);

        },
        async saveSkuTableData(isApply) {
            console.log(this.form, 'this.form');
            // if (this.form.styleTag == '' || this.form.styleTag == null || this.form.styleTag == undefined || this.form.styleTag.length == 0) {
            //     return this.$message.error("请选择款式标签！");

            // }
            if (this.form.attributeTag == '' || this.form.attributeTag == null || this.form.attributeTag == undefined || this.form.attributeTag.length == 0) {
                return this.$message.error("请选择属性标签！");
            }
            if (this.form.seasonOrFestivalTag == '' || this.form.seasonOrFestivalTag == null || this.form.seasonOrFestivalTag == undefined || this.form.seasonOrFestivalTag.length == 0) {
                return this.$message.error("请选择季节/节日标签！");
            }
            if (this.form.weatherTag == '' || this.form.weatherTag == null || this.form.weatherTag == undefined || this.form.weatherTag.length == 0) {
                return this.$message.error("请选择天气标签！");
            }
            if (this.form.temperatureTag == '' || this.form.temperatureTag == null || this.form.temperatureTag == undefined || this.form.temperatureTag.length == 0) {
                return this.$message.error("请选择温度标签！");
            }

            if (!(this.form.supplierList && this.form.supplierList.length > 0)) {
                this.$alert("请维护供应商信息！");
                return false;
            }

            if (!this.form.yyGroupId || this.form.yyGroupId == ""){
                return this.$message.error("请选择运营组！");
            }

            //有供应商无商品的，不允许保存
            for (let i = 0; i < this.form.supplierList.length; i++) {
                var skuData = this.calcSaveSkuTableData.find(x => x.supplierId == this.form.supplierList[i].id);
                if (skuData == undefined || skuData == null) {
                    this.$alert("供应商【" + this.form.supplierList[i].supplierName + "】没有商品数据，如果不需要该供应商，请删除！");
                    return false;
                }
            }

            //this.listLoading = true;
            let listData = [...this.calcSaveSkuTableData];
            let res = this.$refs.uploadChatImg.getReturns();
            this.form.chatImgUrls = res.data;
            this.form.labels2 = "";
            if (this.form.labels2List && this.form.labels2List.length > 0) {
                this.form.labels2 = this.form.labels2List.toString();
            }
            let dtoData = {
                ...this.form
            };
            dtoData.goodsDtlEntities = listData;
            dtoData.SaveAndAppply = isApply;
            var rlt = buildGoodsDocAsync(dtoData);

            await rlt;
            //this.listLoading = false;

            return rlt;

            // if (res?.success) {
            //     this.$message({ message: '保存成功', type: "success" });
            //     return true;
            // } else {
            //     return false;
            // }
        },
        getPreviewImgList(index, imgArr) {
            let arr = []
            let i = 0;
            for (i; i < imgArr.length; i++) {
                arr.push(imgArr[i + index])
                if (i + index >= imgArr.length - 1) {
                    index = 0 - (i + 1);
                }
            }
            return arr;
        },
        renderHeader(h, { column }) { // h即为cerateElement的简写
            return h(
                'div',
                [
                    h('div', { domProps: { innerHTML: "<span style='color:red'>*</span><span>" + column.label + "</span>" } })
                ],
            );
        },
        batchSetRowHeighetWidth(row) {
            let self = this;
            self.$confirm('此操作将覆盖其他行当前列数据, 确定继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let goodsLength = row.goodsLength;
                let goodsWidth = row.goodsWidth;
                let goodsHeigdth = row.goodsHeigdth;
                self.skuTableData.forEach((x) => {
                    if (x.supplierId == this.curSupplierId) {
                        x["goodsLength"] = goodsLength;
                        x["goodsWidth"] = goodsWidth;
                        x["goodsHeigdth"] = goodsHeigdth;
                    }
                });
            })
                .catch(() => {

                });
        },
        batchSetRowWeight(row) {
            let self = this;
            self.$confirm('此操作将覆盖其他行当前列数据, 确定继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let goodsWeight = row.goodsWeight;
                self.skuTableData.forEach((x) => {
                    if (x.supplierId == this.curSupplierId) {
                        x["goodsWeight"] = goodsWeight;
                    }
                });
            })
                .catch(() => {

                });
        }
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

.tempdiv ::v-deep img {
    width: auto;
    max-width: 1000px;
}

::v-deep .el-image__inner {
    max-height: 172px !important;
    max-width: 172px !important;
}

.left-align .el-form-item__content {
    text-align: left;
}

.el-form-item {
    margin-bottom: 12px;
}

.imgcss {
    height: 172px;
    z-index: 2 !important;
    position: relative;
}

.overtext {
    max-width: 600px;
    // min-width: 50px;
    // width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.commonstyle ::v-deep .el-form-item__label {
    width: 140px;
}

.overtexc {
    overflow: hidden;
}
</style>
