<template>
    <my-container v-loading="pageLoading" :element-loading-text="upmsginfo">
    <div class="body" @click="textshowfunc(false)" >
        <div style="display: flex; flex-direction: row;align-items: center; justify-content: center;" v-if="main">
            <span style="font-weight: 600;">任务标题：</span>
            <el-input maxlength="100" :disabled="!ispaste"  @change="ischange(true)" v-model="main.taskName" style="width: auto; display: flex;" clearable ></el-input>
        </div>
        <div style="display: flex;" v-for="(item,i) in vedioInfo" :key="i">
            <div style="flex:1; flex-direction: column; display: flex; min-height: 780px;" v-if="resultshow">
                <slideshow @getlist="getlistt" :ref="refname" @changefuc="ischange" :key="num" :i="i" :item="name == 'skuimg'?item.photo:item" :list="alllist" :bannum="bannum" :name="name"></slideshow>
            </div>
        <div class="fourbox" @click="isindexx(i)" style="border:1px solid #eee; flex:3;">
            <div style="display: flex; flex-direction: row;" v-if="!showbottom">
                <span class="fontwei">视频链接或上传视频:</span>
            </div>
            
            <el-row style="margin-top: 20px;" v-if="!showbottom">
                <el-col :span="1.5">链接一：</el-col>
                <el-col :span="19"><div class="grid-content bg-purple"><el-input maxlength="500" clearable @change="inputvideo1" :disabled="disabled" v-model="item.url" style="width:100%" placeholder="上传视频"></el-input></div></el-col>
                <el-col :span="3" ><div class="grid-content bg-purple-light" style="display: flex; flex-direction: row;">
                    <el-upload
                    v-if="ispaste"
                    action="#"
                    ref="uploadversion"
                    accept=".mp4,.mov,.vedio,.av,.wmv,.mpg,.mpeg,.rm,.flv,.swf"
                    :limit="1"
                    :show-file-list="false"
                    :auto-upload="false"
                    :on-change="onchangevideo">
                    <el-button size="mini" style="margin-left: 6px;" type="primary">上传视频</el-button>
                    </el-upload>
                    <el-button size="mini" type="primary" @click="addurl(i)" style="margin-left: 6px;" v-if="ispaste">新增一行</el-button>
                    <el-button size="mini" type="primary" style="" @click="pasteline(item.url)" v-if="!ispaste">复制链接</el-button>
                </div></el-col>
            </el-row>
            <div @click="isindexx2(inde)" v-if="!showbottom">
                <div style="display: flex; flex-direction: row; margin: 5px 0; width: 100%; "  v-for="(itemm,inde) in item.urls" :key="inde">
                    <span>链接{{changeNumToHan(inde+2)}}：</span>
                    <div :style="ispaste?{flex:95}:{flex:835}"  ><el-input maxlength="500" clearable @change="inputvideo2" :disabled="disabled" v-model="itemm.url" style="width:100%" placeholder="可以是链接，也可以是自己输入的一些文字备注"></el-input></div>
                    <div :style="ispaste?{flex:5}:{flex:7}" v-if="ispaste"><el-button size="mini" type="danger" @click="splice1(i,inde)"  >移除</el-button></div>
                    <div :style="ispaste?{flex:5}:{flex:165}" v-if="!ispaste"><el-button size="mini" type="primary"  @click="pasteline(itemm.url)" >复制链接</el-button></div>
                </div>
            </div>

            <div class="flexcolumn" style="align-items: center; width: 100%; display: flex;" v-if="!showbottom">
                <span style="width: 80px;margin-right: auto;">重点要求：</span>
                <div class="flexcenter" @click.stop="textshowfunc(true)" style="border: 1px solid #eee; width: 97%; font-weight: 600; font-size: 20px; border-radius: 5px;">
                    <span style="line-height: 40px; word-break: break-all;" v-if="textshow">{{ item.mainMarkInfo||"点击输入文字" }}</span>
                    <el-input maxlength="500" v-else clearable  v-model="item.mainMarkInfo" @change="mainMarkInfofuc(i,item.mainMarkInfo)" :placeholder="placeholdersize" :disabled="disabled"></el-input>
                </div>
            </div>

            
            <div class="bottombox">
                <div class="bottombox-left flexcolumn">
                    <div style="width: 100%; padding: 7.5px 0; background-color: white; align-items: center; z-index: 99;" class="flexrow">
                        <el-button size="mini" type="danger" @click="delmoudle(i)" v-if="ispaste">删除模块</el-button>
                        <span style="width:70px; margin-left: 5px;">尺寸:</span>
                        <div class="flexrow" style="width:210px;">
                            <el-button-group>
                                <el-button style="width: 40px;" class="flexcenter" :type="cicunnum==1?'primary':item.vedioType==1?'primary':''" size="mini" @click="vedioTypefuc(i,1)" :disabled="!ispaste">1:1</el-button>
                                <el-button style="width: 40px;" class="flexcenter" :type="cicunnum==2?'primary':item.vedioType==2?'primary':''" size="mini" @click="vedioTypefuc(i,2)" :disabled="!ispaste">3:4</el-button>
                                <el-button style="width: 40px;" class="flexcenter" :type="cicunnum==3?'primary':item.vedioType==3?'primary':''" size="mini" @click="vedioTypefuc(i,3)" :disabled="!ispaste">9:16</el-button>
                            </el-button-group>
                        </div>
                        <div style="width:210px;">
                            <el-input maxlength="500" clearable  v-model="item.vedioSize" @change="inputvideo3" :placeholder="placeholdersize" :disabled="disabled"></el-input>
                        </div>
                        
                        
                        <span style="width: 70px;margin-left: 5px;">要求:</span>
                        <div class="flexrow" style="width:160px;">
                            <el-button-group v-if="requirementshow">
                            <el-button style="width: 40px;" class="flexcenter" :type="item.isHen==1?'primary':''" size="mini" @click="whactclick(i,1)" :disabled="!ispaste">横拍</el-button>
                            <el-button style="width: 40px;" class="flexcenter" :type="item.isHen==2?'primary':''" size="mini" @click="whactclick(i,2)" :disabled="!ispaste">竖拍</el-button>
                            </el-button-group>
                        </div>
                    </div>

                    <div style="align-items: center; justify-content: center; height: auto;">
                        <video crossorigin="anonymous" style="width: 520px; height: auto; display: flex; align-self: center;"  :src="item.url?item.url:upvideo" controls loop muted v-if="item.url"></video>
                        <div v-else style="width:520px; height:520px; background: #eee;" class="flexcenter">
                            <span>请先上传视频</span>
                        </div>
                    </div>

                </div>

                <div class="bottombox-right">
                    <div style="display: flex; width: 100%; padding: 7.5px 0;">
                        <div style="width: 100%; height: 100%; display: flex; flex-direction: column; justify-content: center;">
                            <div style=" width: 100%; z-index: 5;" class="flexrow">
                                <span style="width: 50px; font-weight: 600;">风格：</span>
                                <el-input maxlength="500" v-model="item.styleInfo" placeholder="按图一风格设计" :disabled="disabled" @change="styleInfofuc(i,item.styleInfo)"  :style="ispaste?{width:'60%'}:{width:'30%'}"></el-input>
                                <el-button :disabled="disabled" @click="addcanimg(i)" style="height: 28px;" v-if="ispaste">添加图文</el-button>
                                <el-upload
                                v-if="ispaste"
                                action="#"
                                ref="upvideofucup"
                                accept=".mp4,.mov,.vedio,.av,.wmv,.mpg,.mpeg,.rm,.flv,.swf"
                                :limit="100"
                                multiple
                                :show-file-list="false"
                                :auto-upload="false"
                                :on-change="(file, fileList) => {upvideofuc(file, fileList, i)}">
                                    <el-button :disabled="disabled" style="height: 28px;">添加视频</el-button>
                                </el-upload>
                                
                            </div>
                        </div>
                    </div>

                    <div style="display: flex; width: 100%; height: 700px; flex-direction: column; overflow-y: auto;">
                        <div v-if="showbottom" style="margin: 0 20px; height: auto;">
                            <div class="flexcolumn" style="align-items: center; width: 100%; display: flex; margin-bottom: 5px;">
                                <span style="width: 80px;margin-right: auto;">重点要求：</span>
                                <div class="flexcenter" @click.stop="textshowfunc(true)" style="border: 1px solid #eee; width: 97%; font-weight: 600; font-size: 20px; border-radius: 5px; word-break: break-all;">
                                    <span v-if="textshow">{{ item.vedioSize }}</span>
                                    <el-input maxlength="500" v-else clearable  v-model="item.vedioSize" @change="inputvideo3" :placeholder="placeholdersize" :disabled="disabled"></el-input>
                                </div>
                            </div>
                            <div style="height:30px; margin-bottom: 5px; display: flex; flex-direction: row;">
                                <span style="font-size: 13px; width: 80px;">链接一:</span>
                                <el-input maxlength="500" clearable @change="inputvideo1" :disabled="disabled" v-model="item.url" style="width:100%" placeholder="上传视频"></el-input>
                                <el-button size="mini" type="primary" style="" @click="pasteline(item.url)" v-if="!ispaste">复制链接</el-button>
                            </div>
                            <div style="height:30px; margin-bottom: 5px; display: flex; flex-direction: row;" v-for="(itemm,inde) in item.urls" :key="inde">
                                <span style="font-size: 13px;width: 80px;">链接{{changeNumToHan(inde+2)}}:</span>
                                <el-input maxlength="500" clearable @change="inputvideo2" :disabled="disabled" v-model="itemm.url" style="width:100%" placeholder="可以是链接，也可以是自己输入的一些文字备注"></el-input>
                                <el-button size="mini" type="primary"  @click="pasteline(itemm.url)" >复制链接</el-button>
                            </div>
                        </div>
                        <div style="height:99%;display: flex; margin: 0 20px;">
                            <div style="flex: 7; height: 99%; display: flex; flex-direction: column;">
                                <div class="flexrow" style="margin-bottom: 15px;" v-for="(item1,i1) in item.marks" :key="i1">
                                    <i class="el-icon-error" style="color:red; display: flex; align-self: center;" v-if="!disabled" @click="delmsgvideo(i,i1)"></i>
                                    <div style="height:103px; flex: 3;border: 1px solid #eee;">
                                        <pastimgVue @ischange="ischange" :id="i" :toimage="['',item1.url]" :name="'marks'" :inputedit="false" :ispaste="!resultshow" @childtoimg="childtoimgg" :keyy="[i,i1]"></pastimgVue>
                                    </div>
                                    <div style="min-height:100px; flex: 7;">
                                        <el-input
                                        type="textarea"
                                        maxlength="500"
                                        @change="ischange(true)"
                                        :autosize="{ minRows: 5, maxRows: 99}"
                                        placeholder="请输入内容"
                                        v-model="item1.markinfo"
                                        :disabled="disabled">
                                        </el-input>
                                    </div>
                                </div>
                                
                            </div>
                            <!-- style="width: 110px; height: 110px;  margin-bottom: 15px; border: 1px solid #eee; position: relative;" -->
                            <div style="flex: 3; height: 99%; text-align: center; display: flex; flex-direction: column; align-items: center;">
                                <div style="width: 110px; height: 110px; position: relative;  margin-bottom: 15px; justify-content: center; display: flex;" v-for="(item2,i2) in item.assVedioInfo" :key="i2" >
                                    <i class="el-icon-error" style="color:red; top: 0; right: 0; z-index: 99; position: absolute;" @click="delassVedio(i,i2)" v-if="!disabled"></i>
                                    <video crossorigin="anonymous" :src="item2!=null?item2.url:''" style="width: 100px; height: 100px;" @click="videoclick(item2.url)" alt="" loop muted></video>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
        <el-dialog
        title="请输入保存参考名称"
        :visible.sync="dialogVisible"
        append-to-body
        width="30%">
        <el-input
        maxlength="100"
        placeholder="请输入保存参考名称"
        v-model="titleval">
        </el-input>
        <el-row>
            <el-col :span="22"><div class="grid-content bg-purple-dark"></div></el-col>
            <el-col :span="2"><div class="grid-content bg-purple-dark"><el-button style="margin-left: auto;" type="primary" @click="finsubmit">保存</el-button></div></el-col>
        </el-row>
        </el-dialog>

        <el-dialog title="视频播放" :visible.sync="dialogVisible" width="50%" @close="closeVideoPlyer"  :append-to-body="true" >
            <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeVideoPlyer">关闭</el-button>
            </span>
        </el-dialog>
    </div>
</my-container>
</template>

<script>
import slideshow from '@/views/media/shooting/fuJianmanage/slideshow';
import MyContainer from "@/components/my-container";
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
import drawImg from '@/views/media/shooting/fuJianmanage/drawimgs';
import pastimgVue from './pastimg.vue';
import videoplayer from '@/views/media/video/videoplaynotdown'
import { pageReferenceViewTaskAsync,getReferenceMainReferencs,saveReferenceMainReferencsForVedio,saveReferenceMainReferencsForImg,saveReferenceMainReferencsForSku} from '@/api/media/referencemanage';

export default {
    name: 'DEMOShootingcreateindex2',
    props:{
        name: {type: String,default: ''},
        btnshow: {type: Boolean,default: true},
        disabled: {type: Boolean,default: false},
        minheight: {type: Boolean,default: true},
        ispaste: {type: Boolean,default: true},
        bannum: {type: Number,default: 1},
        alllist: {type: Array,default: function() {
			return []
		}},
        main: {type: Object,default: null},
        listid: {type: Number,default: 0},
        isroll: {type: Boolean,default: false},
        isvideoh: {type: Boolean,default: false},
        showbottom: {type: Boolean,default: false},
        resultshow: {type: Boolean,default: false},
        refname: {type: String,default: ''},
    },
    components: {drawImg,MyContainer,pastimgVue,videoplayer,slideshow},
    data() {
        return {
            videoplayerReload:false,
            dialogVisible:false,
            requirementshow: true,
            upmsginfo: '努力加载中...',
            drawnum: -1,
            imgUrl: '',
            tomsg: [],
            cutImgSrc: '',
            sizeinput2: '',
            draw:false,
            isshow: false,
            startdemo: null,
            contenteditable: true,
            placetext: '请按Ctrl+v粘贴图片...',
            list:[1],
            canvasimg: [],
            upvideo:'',
            watname: '',
            input: '',
            sizeinput: '',
            num: -1,
            beiinput: '',
            demohtml: [],
            videomsg: null,
            tableData: [],
            vedioInfo: [],
            dialogVisible: false,
            titleval: '',
            indexx: null,
            indexx2: null,
            indexx3: null,
            input1: '',
            pageLoading: false,
            placeholdersize: '750*1000',
            textshow: true,
            cicunnum: null,
            nummber: 0
            // accept: ['mp4','mov',]
        };
    },
    watch: {
        handler(newValue, oldValue) {
            // if(oldValue!=newValue&&oldValue.length>0){
            //     this.$emit("changefuc",true)
            // }
        },
        deep: true,
        alllist: 'alllistchange'
    },
    computed: {
        handler(){
            return [...this.vedioInfo]
        }
    },

    mounted() {
        this.nummber = this.bannum-1;
        if(this.name){
            this.watname = this.name;
        }
        if(this.alllist.length>0){
            this.vedioInfo = this.alllist;
        }
    },

    methods: {
        alllistchange(val){
            this.recash(val);
        },
        recash(val){
            this.nummber = this.bannum-1;
            if(this.name){
                this.watname = this.name;
            }
            if(val.length>0){
                this.vedioInfo = val;
            }
        },
        submitty(){
            let _this = this;
            this.$refs[`${_this.refname}`][0].submitt();
        },
        getlistt(){
            this.$emit("getlist") 
        },
        videoclick(val){
            this.videoplayerReload = false;
            this.videoplayerReload = true;
            this.dialogVisible = true;
            this.videoUrl = val;
        },
        async closeVideoPlyer() {
            this.dialogVisible = false;
            this.videoplayerReload = false;
        },
        delassVedio(i,index){
            this.vedioInfo[i].assVedioInfo.splice(index,1)
            this.ischange(true);
        },
        delmsgvideo(i,index){
            this.vedioInfo[i].marks.splice(index,1)
            this.ischange(true);
        },
        async childtoimgg(name,arrval){
            await this.creimgfuc(name,arrval)
        },
        creimgfuc(val,arrval){
            if(val&&arrval!=null){
                this.vedioInfo[arrval[0][0]][val][arrval[0][1]].fileName = arrval[1].fileName;
                this.vedioInfo[arrval[0][0]][val][arrval[0][1]].filePath = arrval[1].relativePath;
                this.vedioInfo[arrval[0][0]][val][arrval[0][1]].url = arrval[1].url;

            }
        },
        addcanimg(val){
            let param = {fileName:'',filePath:'',url:''};
            this.vedioInfo[val].marks.push(param)
        },
        styleInfofuc(index,val){
            this.ischange(true);
            this.vedioInfo[index].styleInfo = val;
        },
        vedioTypefuc(index,val){
            this.cicunnum = val;
            this.ischange(true);
            this.vedioInfo[index].vedioType = val;
        },
        mainMarkInfofuc(index,val){
            // this.vedioInfo[index].mainMarkInfo = val;
            this.ischange(true);
        },
        textshowfunc(val){
            if(val){
                this.textshow = false;
            }else{
                this.textshow = true;
            }
        },
        changeNumToHan(num) {
            var arr1 = new Array('零', '一', '二', '三', '四', '五', '六', '七', '八', '九');
            var arr2 = new Array('', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '万', '十', '百', '千', '亿');//可继续追加更高位转换值
            if (!num || isNaN(num)) {
                return "零";
            }
            var english = num.toString().split("");
            var result = "";
            for (var i = 0; i < english.length; i++) {
                var des_i = english.length - 1 - i;//倒序排列设值
                result = arr2[i] + result;
                var arr1_index = english[des_i];
                result = arr1[arr1_index] + result;
            }
            //将【零千、零百】换成【零】 【十零】换成【十】
            result = result.replace(/零(千|百|十)/g, '零').replace(/十零/g, '十');
            //合并中间多个零为一个零
            result = result.replace(/零+/g, '零');
            //将【零亿】换成【亿】【零万】换成【万】
            result = result.replace(/零亿/g, '亿').replace(/零万/g, '万');
            //将【亿万】换成【亿】
            result = result.replace(/亿万/g, '亿');
            //移除末尾的零
            result = result.replace(/零+$/, '');
            //将【零一十】换成【零十】
            //result = result.replace(/零一十/g, '零十');//貌似正规读法是零一十
            //将【一十】换成【十】
            result = result.replace(/^一十/g, '十');
            return result;
        },
        delmoudle(ind){
            this.vedioInfo.splice(ind,1);
            this.ischange(true);
        },
        ischange(val){
            if(val){
                this.$emit("changefuc",val)
                console.log("改变值")
            }
        },
        pasteline(line){
            let oInput = document.createElement('input');
            oInput.value = line;
            document.body.appendChild(oInput);
            oInput.select();
            document.execCommand('Copy');
            this.$message.success('复制成功!');
            oInput.remove();
        },
        splice1(i,index){
            this.vedioInfo[i].urls.splice(index,1)
            this.ischange(true);
        },
        splice2(index){
            this.ischange(true);
            this.vedioInfo[this.indexx].marks.splice(index,1)
        },
        // httprequest(){

        // },
        whactclick(index,value){
            let _this = this;
            _this.requirementshow =false;
            _this.ischange(true);
            _this.vedioInfo[index].isHen = value
            _this.requirementshow =true;
        },
        isindexx(val){
            this.indexx = val;
        },
        isindexx2(val){
            this.indexx2 = val;
        },
        isindexx3(val){
            this.indexx3 = val;
        },
        async getlist(){
                let _this = this;
                let params = {
                type: _this.bannum,
                mainTask: _this.mainTask,
                vedioInfo: _this.vedioInfo,
    
            }
           let res = await saveReferenceMainReferencsForVedio(params);
           if(res.success){
                _this.$message.success({
                    message: "保存成功！",
                    offset: 150,
                    duration: 2000
                })
                _this.listidd = res.data.referenceManageTaskId;
                _this.dialogVisible = false;
           }else{
                _this.$message("保存失败，请重试！");
            }
        },
        //第一次保存
        async submitt(){
            let _this = this;
            _this.dialogVisible = true;
        },
        //第二次保存
        async tosubmitt(){
            let _this = this;
            _this.mainTask = {
                referenceManageTaskId: _this.listid? _this.listid:0,
                taskName: _this.titleval?_this.titleval:_this.main.taskName,
            };
            await _this.getlist();
            await _this.$emit('getalllist');
        },
        async finsubmit(){
            let _this = this;
            _this.mainTask = {
                referenceManageTaskId: 0,
                taskName: _this.titleval?_this.titleval:_this.main.taskName,
            };
            await this.getlist();
            await _this.$emit('getalllist',_this.listidd);
        },
        addlist(){
            let _this = this;
            let param = {
                    mainVedioId: 0,
                    fileName: "",
                    filePath: "",
                    url: "",
                    vedioSize: "",
                    type: _this.bannum,
                    isHen: "",
                    referenceManageTaskId: _this.listid? _this.listid:0,
                    marks: [
                        {
                        markinfo: "",
                        fileName: "string",
                        filePath: "string",
                        url: "string",
                        }
                    ],
                    assVedioInfo: [],
                    urls: [
                        {
                        urlIds: 0,
                        url: "",
                        type: _this.bannum,
                        mainVedioId: 0,
                        referenceManageTaskId: _this.listid? _this.listid:0,
                        }
                    ]
                }
                _this.ischange(true);
                _this.vedioInfo.push(param);
        },
        addurl(index){
            let _this = this;
            _this.vedioInfo[index].urls.push({
                url: "",
                type: _this.bannum,
                referenceManageTaskId: _this.listid? _this.listid:0,
            });
            _this.ischange(true);
        },
        addmark(index){
            let _this = this;
            _this.vedioInfo[index].marks.push({
                url: "",
                type: _this.bannum,
                referenceManageTaskId: _this.listid? _this.listid:0,
            });
            _this.ischange(true);
        },
        inputvideo1(value){
            let _this = this;
            let isvideo = value.indexOf(".mp4")
            if(isvideo!=-1){
                _this.ischange(true);
            }else{
                this.$message("请粘贴或者上传正确的视频文件")
            }
        },
        inputvideo2(value){
            let _this = this;
            this.ischange(true);
        },
        inputvideo4(value){
            let _this = this;
            this.ischange(true);
        },
        inputvideo3(value){
            let _this = this;
            _this.ischange(true);
        },
        async upvideofuc(obj,arr,i){
            let _this = this;
            _this.pageLoading = true;
            await _this.AjaxFile(obj,0,"");
            await _this.assVedioInfofuc(i);
            this.ischange(true);

            let uploadFilesArr = this.$refs.upvideofucup[i].uploadFiles;
            if(uploadFilesArr.length ==0){
            }else{
            this.$refs.upvideofucup[i].uploadFiles =[]
        
            }
            _this.pageLoading = false;

        },
        assVedioInfofuc(index){
            let _this = this;
            if(_this.vedioInfo[index].assVedioInfo==null){
                _this.vedioInfo[index].assVedioInfo = []
            }
            let params = {
                        fileName: this.atfterUplaodData.fileName,
                        filePath: this.atfterUplaodData.relativePath,
                        url: this.atfterUplaodData.url,
                    }
            _this.vedioInfo[index].assVedioInfo.push(params)
        },
        async onchangevideo(e){
            let _this = this;
            var file = e.raw;
            _this.pageLoading = true;
            await _this.AjaxFile(e,0,"");
            await _this.fuzhi();
            _this.ischange(true);

            let uploadFilesArr = this.$refs.uploadversion[_this.indexx].uploadFiles;
            if(uploadFilesArr.length ==0){
            }else{
            this.$refs.uploadversion[_this.indexx].uploadFiles =[]
        
            }
            _this.pageLoading = false;

        },
        fuzhi(){
            let _this = this;
            _this.vedioInfo[_this.indexx].url = this.atfterUplaodData.url;
            _this.vedioInfo[_this.indexx].filePath = this.atfterUplaodData.filePath;
            _this.vedioInfo[_this.indexx].fileName = this.atfterUplaodData.fileName;
        },
        //切片上传
        async AjaxFile(file, i, batchnumber) {
            var name = file.name; //文件名
            var size = file.size; //总大小shardSize = 2 * 1024 * 1024,
            var shardSize = 2 * 1024 * 1024;
            var shardCount = Math.ceil(size / shardSize); //总片数
            if (i >= shardCount) {
                return;
            }
            //计算每一片的起始与结束位置
            var start = i * shardSize;
            var end = Math.min(size, start + shardSize);
            //构造一个表单，FormData是HTML5新增的
            i = i + 1;
            var form = new FormData();
            form.append("data", file.raw.slice(start, end)); //slice方法用于切出文件的一部分
            form.append("batchnumber", batchnumber);
            form.append("fileName", name);
            form.append("total", shardCount); //总片数
            form.append("index", i); //当前是第几片
            const res = await xMTVideoUploadBlockAsync(form);
            if (res?.success) {
                this.percentage = (i * 100 / shardCount).toFixed(2);
                this.upmsginfo ="视频上传中" +  this.percentage +"%";
                if (i == shardCount) {
                    this.atfterUplaodData = res.data;
                } else {
                    await this.AjaxFile(file, i, res.data);
                }
            } else {
                this.$message({ message: res?.msg, type: "warning" });
            }
        }, 
        uploadToServer(file, callback) {
            var xhr = new XMLHttpRequest()
            var formData = new FormData()
            formData.append('file', file)
            xhr.open('post', '/api/uploadnew/file/UploadCommonFileAsync')
            xhr.withCredentials = true
            xhr.responseType = 'json'
            xhr.send(formData)
            xhr.onreadystatechange = () => {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    // debugger;
                    callback(xhr.response)
                }
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.body{
    // padding: 10px;
    position: relative;
}
.box{
    // padding: 10px 0;
    // margin: 20px 0;
    min-height: 780px;
    // border: 1px solid #eee;
    display: flex;
    flex-direction: row;
}
.twobox{
    // padding: 10px 0;
    // margin: 20px 0;
    min-height: 780px;
    display: flex;
    flex-direction: row;
}
.fourbox{
    // padding: 20px;
    // margin: 20px 0;
    min-height: 780px;
    // border: 1px solid #eee;
    display: flex;
    flex-direction: column;
    // margin-bottom: 30px;
    .bottombox{
        width: 100%;
        min-height: 780px;
        overflow-y: auto;
        // padding: 20px 0 20px 0;
        display: flex;
        .bottombox-left{
            // flex: 4;
            width: 520px;
            display: flex;
            align-items: center;
        }
        .bottombox-right{
            // flex: 6;
            // padding: 0 10px;
            width: 100%;
            display: flex;
            flex-direction: column;
            // justify-content: center;
            align-items: center;
            .right-bot{
                // flex: 8;
                // padding: 20px;
                margin: 0;
                // border: 1px solid #eee;
            }
            .right-top{
                display: flex;
                flex-direction: row;
                // flex: 2;
                // padding: 20px;
                margin: 0;
                // border: 1px solid #eee;
                .top-right{
                    width: auto;
                    margin-left: 20px;
                }

                .top-left{
                    width: 200px;
                }

            }
        }
    }
}
.imgbox{
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 6;
    position: relative;

}
.flexcenter{
    display: flex;
    justify-content: center;
    align-items: center;
}
.msgbox{
    flex: 4;
    // padding: 50px 0;
}
.fontwei{
    font-weight: 600;
}
.flexcolumn{
    display: flex;
    flex-direction: column;
}
.flexrow{
    display: flex;
    flex-direction: row;
    width: 100%;
}
.pastimg{
    width: 400px;
    height: 400px;
    background-color: #eee;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}
// div{
//     caret-color: transparent;
// }
.point{
    cursor: crosshair;
}
.module{
    // background-color: #eee;
    margin-top: 30px;
    width: 100%;
    min-height: 400px;
}
.flexle{
    flex: 98.7
}
.flexri{
    flex: 1.3
}
</style>