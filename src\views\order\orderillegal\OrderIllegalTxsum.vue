<template>
    <my-container v-loading="pageLoading">      
      <template>  
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
          :tableData='list'  :tableCols='tableCols' :isSelection="false" @select="selectchange" :isSelectColumn='false'
          :tableHandles='tableHandles' :loading="listLoading">
        </ces-table>
      </template>
          <!-- <template slot='extentbtn'>
          <el-button-group>
            <el-button style="padding: 0;margin: 0;">
               <el-checkbox v-model="filter1.isgroupbyreson">原因分组</el-checkbox>
            </el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
        </el-button-group>
       </template> -->

      
        <template #footer>
          <my-pagination
            ref="pager"
            :total="total"
            :checked-count="sels.length"
            @get-page="getlist"/>
        </template>

        <!-- 扣款金额趋势图 -->
      <!-- <el-dialog :visible.sync="dialoganalysisVisible" width="80%" v-dialogDrag :show-close="false">
          <orderIllAnalysis  ref="orderIllAnalysis" style="height: 550px"></orderIllAnalysis> 
      </el-dialog> -->

      <!-- 扣款金额汇总趋势图 -->
      <el-dialog title="趋势图" :visible.sync="dialogMapVisible.visible" width="80%">
        <div>
          <span>
            <template>
            
     
      <el-date-picker
        v-model="filter3.timeRange1"
        type="datetimerange" 
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :picker-options="pickerOptions1"
      ></el-date-picker>
      <el-button type="primary" @click="onSearch1()">查询</el-button>
        </template> 
        </span>
          <span>     
             <buschar v-if="dialogMapVisible.visible" 
             ref="buschar" :analysisData="dialogMapVisible.data"></buschar>
         </span>
        </div>
         <span slot="footer" class="dialog-footer">
           <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
         </span>
       </el-dialog>

      <el-dialog :visible.sync="dialosisboardVisible" width="85%" v-dialogDrag :show-close="false">
        <orderillegalboardTx :filter="filterBoard" ref="orderillegalboardTx" style="height: 100%"></orderillegalboardTx>
      </el-dialog>
      
    </my-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import buschar from '@/components/Bus/buschar'
import { formatTime, } from "@/utils";
import {formatLinkProCode, formatSendWarehouse} from "@/utils/tools";
import { getOrderWithholdList, exportOrderWithhold, importPinOrderIllegal, 
  getWithProCodeTxSum as  getWithProCodeSum,
  queryWithholdAnalysisTx,
  QueryWithholdAnalysis4TxKkhz} from "@/api/order/orderdeductmoney"
import { rulePlatform, ruleIllegalType} from "@/utils/formruletools";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import orderIllAnalysis from './OrderIllAnalysis.vue'

import orderillegalboarddetailTx from './orderillegalboarddetailTx.vue'
import OrderIllgalsearchTx from './OrderIllgalsearchTx.vue'
import orderillegalboardTx from "./orderillegalboardTx.vue";

const tableCols =[
        {istrue:true,prop:'proCode',label:'宝贝ID',tipmesg:'查询生效条件：扣款时间、宝贝ID、扣款原因', width:'120',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(1,row.proCode)},
        {istrue:true,prop:'goodsName',label:'商品名称', width:'600',formatter:(row)=> !row.goodsName? "" : row.goodsName},       
        {istrue:true,prop:'groupId',label:'小组', width:'60',formatter:(row)=> !row.groupName?" " : row.groupName },
        {istrue:true,prop:'shopId',label:'店铺', width:'300',formatter:(row)=> !row.shopName? " " : row.shopName},  
        //{istrue:true,fixed:true,prop:'proCode',fix:true,label:'商品ID', width:'120',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
        // {istrue:true,prop:'illegalType',label:'扣款原因', width:'auto',sortable:'custom',formatter:(row)=> !row.illegalTypeName?" " : row.illegalTypeName},
        {istrue:true,prop:'amountPaid',label:'扣款金额', width:'100',sortable:'custom',formatter:(row)=>parseFloat(row.amountPaid.toFixed(2))},
        // {istrue:true,prop:'proCode',fix:true,label:'趋势图', text:'趋势图',style:"color:red;cursor:pointer;",width:'70', formatter:(row)=>'趋势图',type:'click',handle:(that,row)=>that.showchart(row.proCode)},

        {istrue:true,display:true,label:'趋势图', style:"color:red;cursor:pointer;",width:70,formatter:(row)=>'趋势图', type:'click',handle:(that,row)=>that.showchart(row)},
        //{istrue:true,prop:'proCode2',fix:true,label:'看板', text:'看板',style:"color:red;cursor:pointer;",width:'70', formatter:(row)=>'看板',type:'click',handle:(that,row)=>that.showpboard(row.proCode)},
]
const tableHandles=[ ];
export default {
    name: 'YunhanAdminOrderillegaldetail1',
    components: {cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, 
      orderIllAnalysis,  OrderIllgalsearchTx,
      buschar,orderillegalboarddetailTx,orderillegalboardTx},
    props:{
        filter:{ }
    },
    data() {
        return {
          pickerOptions1: {
		          shortcuts: [{
		            text: '7天内',
		            onClick(picker) {     
		              const yestoday=new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
		              const end = new Date(new Date(new Date(new Date().getTime() + 3600 * 1000 * 24).toLocaleDateString()).getTime());
		              const start = new Date(new Date(yestoday.toLocaleDateString()).getTime()- 3600 * 1000 * 24 * 6);
		              //start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
		              picker.$emit('pick', [start, end]);
		            }
		          }, {
		            text: '昨天',
		            onClick(picker) {
		              const yestoday=new Date(new Date().getTime() - 3600 * 1000 * 24 );
		              const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
		              const start = new Date(new Date(new Date().toLocaleDateString()).getTime());
		              start.setTime(start.getTime() - 3600 * 1000 * 24);
		              picker.$emit('pick', [start, end]);
		            }
		          }, {
		            text: '今天',
		            onClick(picker) {
		              const end = new Date(new Date(new Date(new Date().getTime() + 3600 * 1000 * 24).toLocaleDateString()).getTime());
		              const start = new Date(new Date(new Date().toLocaleDateString()).getTime());
		              start.setTime(start);
		              picker.$emit('pick', [start, end]);
		            }
		          }]},
            dialogMapVisible:{visible:false,title:"",data:[]},
            that:this,
            filter1:{isgroupbyreson:null },
            filter2 : {},
            filterBoard:{},
            list: [],
            platformList: [],
            illegalTypeList : [],
            summaryarry:{},
            pager:{OrderBy:"AmountPaid",IsAsc:false},
            filterImport:{
            platform:1,
            occurrenceTime:formatTime(dayjs().subtract(1,"day"), "YYYY-MM-DD")
            },
            pickerOptions:{
                disabledDate(time){
                return time.getTime()>Date.now();
                }
            },           
            tableCols:tableCols,
            tableHandles:tableHandles,
            total: 0,
            sels: [], 
            listLoading: false,
            pageLoading: false,
            dialogVisible: false,
            uploadLoading: false,
            dialoganalysisVisible: false,
            dialosisboardVisible: false,
            showDetailVisible : false,
            filter3: {timeRange1:[],StartDate:null,EndDate:null,},
            proCode:null
        };
    },
    async mounted() {
        await this.onSearch()
        await this.init()
    },
    methods: {
      datetostr(date) {
		      var y = date.getFullYear();
		      var m = ("0" + (date.getMonth() + 1)).slice(-2);
		      var d = ("0" + date.getDate()).slice(-2);
		      return y + "-" + m + "-" + d;
		    },
      async init(){
		        var date1 = new Date(); date1.setDate(date1.getDate()-7);
		        var date2 = new Date(); date2.setDate(date2.getDate());
		        this.filter3.timeRange1=[];
		        this.filter3.timeRange1[0]=this.datetostr(date1);
		        this.filter3.timeRange1[1]=this.datetostr(date2);
		  },
      async onSearch1(){
          this.filter3.StartDate =null;
        this.filter3.EndDate =null;
        if (this.filter3.timeRange1) {
          this.filter3.StartDate = this.filter3.timeRange1[0];
          this.filter3.EndDate = this.filter3.timeRange1[1];
        }  
        
        var params = {
          StartDate:this.filter3.StartDate,
          EndDate:this.filter3.EndDate,
          ProCode:this.proCode,
          Column:'amountPaid',
          illegalType:this.filter.illegalType
        }
             
             console.log("趋势图最终参数",params);
        let that = this;
        
        const res = await QueryWithholdAnalysis4TxKkhz(params).then(res=>{                    
            that.dialogMapVisible.visible=true;
            that.dialogMapVisible.data=res.data
            that.dialogMapVisible.title=res.data.legend[0]
        })
            this.dialogMapVisible.visible=true
            await this.$refs.buschar.initcharts();
          },

      //汇总趋势图
  async showchart(row){
    console.log("趋势图商品ID参数",row.proCode);
    console.log("趋势图最终参数row",row);
      this.filter3.timeRange1=this.filter.timerange;
        this.filter3.StartDate =this.filter.startDate ;
        this.filter3.EndDate = this.filter.endDate ;
       
        this.proCode=row.proCode;
        var params = {
          StartDate:this.filter3.StartDate,
          EndDate:this.filter3.EndDate,
          ProCode:row.proCode,
          Column:'amountPaid',
          illegalType:this.filter.illegalType
        }

        this.filterBoard={...params};
             
        console.log("趋势图最终参数",params);
        let that = this;
        
        const res = await QueryWithholdAnalysis4TxKkhz(params).then(res=>{                    
            that.dialogMapVisible.visible=true;
            that.dialogMapVisible.data=res.data
            that.dialogMapVisible.title=res.data.legend[0]
        })
        this.dialogMapVisible.visible=true
     },

    async onSearch() {
      if (!this.filter.timerange) {this.$message({message: "请选择日期",type: "warning",});return;}
      this.$refs.pager.setPage(1)
      await this.getlist();
    },   
    //获取查询条件
    getCondition(){
      if (this.filter.timerange&&this.filter.timerange.length>1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      else {
        this.$message({message:"请先选择日期",type:"warning"});
        return false;
      }
      var pager = this.$refs.pager.getPager();
      var page  = this.pager;
      const params = {
        ...pager,
        ...page,
        ... this.filter,
      }
      return params;
    }, 
    //分页查询
     async getlist() {
        var params=this.getCondition();
        if(params===false){
                return;
        }
        this.listLoading = true
        const res = await getWithProCodeSum(params)
        this.listLoading = false
        if (!res?.success) {
            return
        }
        this.total = res.data.total;
        const data = res.data.list;
        this.summaryarry=res.data.summary;
        // if(this.summaryarry)
        //     this.summaryarry.amountPaid_sum=parseFloat(this.summaryarry.amountPaid_sum.toFixed(6));
        data.forEach(d => {
            d._loading = false
        })
        this.list = data
       },
    //排序查询
    async sortchange(column){
    if(!column.order)
        this.pager={};
    else{
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
    }
    await this.onSearch();
    },
    // async showprchart(row){
    //   this.dialoganalysisVisible=true;
    //   let para ={proCode : row, timerange : this.filter.timerange,  reportType: 0}
    //   this.$nextTick(() => {
    //       this.$refs.orderIllAnalysis.onSearch(para);
    //   });
    // },

    


    //父子传值
    async showpboard(row){
       this.filterBoard={...this.filter};
       this.filterBoard.proCode=row;       
       this.dialosisboardVisible = true;
       this.$nextTick( async () =>{
         await this.$refs.orderillegalboardTx.onSearch()
       })
    },
    async onSearchDetail(para){
        this.filter2 = para
        console.log('输出',this.filter2)
        this.filter2.groupId = null
        this.showDetailVisible = true
        this.$nextTick( async () =>{
          await this.$refs.OrderIllgalsearchTx.onSearch()
        })        
    },
    selectchange:function(rows,row) {
      this.selids=[];console.log(rows)
      rows.forEach(f=>{
          this.selids.push(f.id);
      })
    }
  }
};
</script>

<style lang="scss" scoped>
</style>
