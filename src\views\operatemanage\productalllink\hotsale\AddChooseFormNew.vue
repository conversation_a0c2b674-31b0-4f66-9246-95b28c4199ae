<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <!--  -->
            <el-form class="ad-form-query" :model="chooseFormData" ref="chooseForm" @submit.native.prevent
                label-width="140px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="产品简称" prop="goodsCompeteShortName" :rules="[
                            { required: true, message: '请填写产品简称', trigger: 'blur', type: 'string' },
                            { max: 50, message: '长度50 个字符内', trigger: 'blur', type: 'string' }]">
                            <el-input v-model="chooseFormData.goodsCompeteShortName" style="width: 250px"
                                :maxlength="50"></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="产品标题" prop="goodsCompeteName" :rules="[
                            { required: true, message: '请填写产品标题', trigger: 'blur', type: 'string' },
                            { max: 50, message: '长度50 个字符内', trigger: 'blur', type: 'string' }]">
                            <el-input v-model="chooseFormData.goodsCompeteName" style="width: 250px"
                                :maxlength="50"></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="竞品ID" prop="goodsCompeteId" :rules="[
                            { required: true, message: '请填写竞品ID', trigger: 'blur', type: 'string' },
                            { min: 4, max: 30, message: '长度4到30 个字符内', trigger: 'blur', type: 'string' },
                            { validator: checkSearch, trigger: 'blur' }]">
                            <el-input style="width: 250px" v-model.trim="chooseFormData.goodsCompeteId" :maxlength="30"
                                :minlength="4"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="8">
                        <!-- <el-form-item label="产品类目" prop="goodsCategorys" :rules="[
                            { required: true, message: '请选择产品类目', trigger: 'blur', }]">
                            <el-select v-model="chooseFormData.goodsCategorys" placeholder="请选择"
                                @change="setSelectCategorys" style="width: 250px" clearable filterable
                                :collapse-tags="true">
                                <el-option v-for="(item) in categoryall.categoryone"
                                    :key="item.mainCategory + '-' + item.categoryLevel1 + '-' + item.categoryLevel2"
                                    :label="item.mainCategory + '-' + item.categoryLevel1 + '-' + item.categoryLevel2"
                                    :value="item.mainCategory + '-' + item.categoryLevel1 + '-' + item.categoryLevel2">
                                </el-option>
                            </el-select>
                        </el-form-item> -->
                        <el-form-item label="产品类目" prop="goodsCategory" :rules="[
                            { required: true, message: '请选择产品类目', trigger: 'blur', }]">
                            <el-select v-model="chooseFormData.goodsCategory" placeholder="请选择" style="width: 250px"
                                clearable filterable :collapse-tags="true">
                                <el-option v-for="(item) in goodsCategoryBase" :key="item" :label="item" :value="item">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="是否支持一件代发" prop="isOneDistribution" label-width="150px" :rules="[
                            { required: true, message: '请选择是否支持一件代发', trigger: 'blur', }]">
                            <el-select v-model="chooseFormData.isOneDistribution" placeholder="请选择"
                                style="width: 190px; margin-left: 10px;" clearable filterable :collapse-tags="true">
                                <el-option label="是" :value="1">
                                </el-option>
                                <el-option label="否" :value="0">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="国内/跨境" prop="internationalType" :rules="[
                            { required: true, message: '请选择国内/跨境', trigger: 'blur', }]">
                            <el-select v-model="chooseFormData.internationalType" placeholder="请选择"
                                @change="onInternationalTypeChange" style="width: 250px" clearable filterable
                                :collapse-tags="true">
                                <el-option :key="0" :value="0" label="国内" />
                                <el-option :key="1" :value="1" label="跨境" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="8">
                        <el-form-item label="资质" prop="qualificationLists" :rules="[
                            { required: true, message: '请选择资质', trigger: 'blur', }]">
                            <el-select :collapse-tags="true" v-model="chooseFormData.qualificationLists" multiple
                                placeholder="类型" style="width: 250px">
                                <el-option label="无" value="无"></el-option>
                                <el-option label="质检报告" value="质检报告"></el-option>
                                <el-option label="食品级质检报告" value="食品级质检报告"></el-option>
                                <el-option label="全国工业生产许可证" value="全国工业生产许可证"></el-option>
                                <el-option label="化妆品生产许可证" value="化妆品生产许可证"></el-option>
                                <el-option label="化妆品备案信息" value="化妆品备案信息"></el-option>
                                <el-option label="农业生产许可证" value="农业生产许可证"></el-option>
                                <el-option label="农药登记证书" value="农药登记证书"></el-option>
                                <el-option label="肥料生产许可证" value="肥料生产许可证"></el-option>
                                <el-option label="3c认证报告" value="3c认证报告"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="是否专利" prop="isPatent" :rules="[
                            { required: true, message: '是否专利', trigger: 'blur', }]">
                            <el-select v-model="chooseFormData.isPatent"
                                @change="chooseFormData.isPatent == 0 ? chooseFormData.patentType = '' : ''"
                                placeholder="是否专利" style="width: 250px">
                                <el-option label="是" :value="1"></el-option>
                                <el-option label="否" :value="0"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="专利类型" prop="patentType" v-if="chooseFormData.isPatent == 1" :rules="[
                            { required: true, message: '专利类型', trigger: 'blur', }]">
                            <el-select v-model="chooseFormData.patentType" placeholder="专利类型" style="width: 250px">
                                <el-option label="专利" :value="'专利'"></el-option>
                                <el-option label="品牌" :value="'品牌'"></el-option>
                                <el-option label="著作" :value="'著作'"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="8">
                        <el-form-item label="成本价" prop="costPrice" :rules="[
                            { required: true, message: '请填写成本价', trigger: 'blur', }]">
                            <!-- <el-input v-model="chooseFormData.costPrice" @blur="chooseFormData.costPrice = chooseFormData.costPrice.slice(0,10)" type="number" :max="9999999" style="width: 250px" :maxlength="8"></el-input> -->

                            <el-input-number v-model="chooseFormData.costPrice" type="number" placeholder="请填写成本价"
                                clearable :controls="false" :precision="3" :max="9999999" :min="0" style="width: 250px"
                                @change="onCostPrice" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="售价" prop="salePrice" :rules="[
                            { required: true, message: '请填写售价', trigger: 'blur', }]">
                            <!-- <el-input v-model="chooseFormData.salePrice" type="number" @blur="chooseFormData.salePrice = chooseFormData.salePrice.slice(0,10)" :max="9999999" style="width: 250px" :maxlength="8"></el-input> -->

                            <el-input-number v-model="chooseFormData.salePrice" type="number" placeholder="请填写售价"
                                clearable :controls="false" :precision="3" :max="9999999" :min="0" style="width: 250px"
                                @change="onSalePrice" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="利润率%" prop="profitRate">
                            <el-input-number v-model="chooseFormData.profitRate" type="number" placeholder="" disabled
                                :controls="false" :precision="2" :max="9999999" :min="-9999999" style="width: 250px" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="8">
                        <el-form-item label="商品图片" prop="goodsCompeteImgUrl" :rules="[
                            { required: true, message: '请上传', trigger: 'blur' }]">
                            <!-- <el-input v-model=" chooseFormData.goodsCompeteImgUrl " style="width: 250px" :maxlength="100"></el-input> -->
                            <yh-img-upload :limit="1" :value.sync="chooseFormData.goodsCompeteImgUrl" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="是否可开票" prop="isInvoicing" :rules="[
                            { required: true, message: '请选择是否可开票', trigger: 'blur', }]">
                            <el-select v-model="chooseFormData.isInvoicing" placeholder="是否可开票" style="width: 250px">
                                <el-option label="是" :value="1"></el-option>
                                <el-option label="否" :value="0"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item v-if="chooseFormData.isInvoicing == 1" label="税点%" prop="isInvoicingRate" :rules="[
                            { required: true, message: '请填写税点%', trigger: 'blur', }]">
                            <el-input-number v-model="chooseFormData.isInvoicingRate" type="number" placeholder="请填写税点%"
                                clearable :controls="false" :precision="2" :max="100" :min="0" style="width: 250px" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="外包装语言" prop="outerPackaLanguage" :rules="[
                            { required: true, message: '请选择外包装语言', trigger: 'blur' }]">
                            <el-select v-model="chooseFormData.outerPackaLanguage" placeholder="请选择外包装语言">
                                <el-option label="中文" :value="1"></el-option>
                                <el-option label="英文" :value="2"></el-option>
                                <el-option label="无" :value="-1"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div style=" display: flex; width: 100%; z-index: 99;">
                    <div
                        style="font-weight: Bold; font-size: 14px; margin-right: auto; margin-bottom: 10px;color: #333333;">
                        竞品其他信息</div>
                    <div style="margin-left: auto; color: #BDBDBD; cursor:pointer;" @click="closeopen">{{ tansitionshow
                        ? '关闭' : '展开'
                        }}<i :class="tansitionshow ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i></div>
                    <!-- el-icon-arrow-up -->
                </div>
                <transition name="el-zoom-in-top">
                    <div v-show="tansitionshow" class="transition-box">
                        <el-row>
                            <el-col :span="6">
                                <el-form-item label="竞品平台" prop="goodsCompetePlatform">
                                    <el-select v-model="chooseFormData.goodsCompetePlatform" placeholder="请选择竞品平台"
                                        style="width: 180px;" @change="onGoodsCompetePlatformChange" clearable>
                                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                                            :value="item.value"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="竞品店铺名称" prop="goodsCompeteShopName">
                                    <el-input v-model="chooseFormData.goodsCompeteShopName" style="width: 180px"
                                        placeholder="请填写竞品店铺名称" :maxlength="50" clearable></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="产品规格成本" prop="goodsCompeteGgCostPrice">
                                    <el-input-number v-model="chooseFormData.goodsCompeteGgCostPrice" type="number"
                                        @change="onSalePrice" placeholder="请填写产品规格成本" clearable :controls="false"
                                        :precision="3" :max="9999999" :min="0" style="width: 180px" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="出仓成本" prop="goodsCompeteCccb">
                                    <el-input-number v-model="chooseFormData.goodsCompeteCccb" type="number"
                                        @change="onSalePrice" placeholder="请填写出仓成本" clearable :controls="false"
                                        :precision="3" :max="9999999" :min="0" style="width: 180px" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="6">
                                <el-form-item label="平台扣点比例%" prop="goodsCompetePlatformKdRate">
                                    <el-input-number v-model="chooseFormData.goodsCompetePlatformKdRate" type="number"
                                        @change="onSalePrice" placeholder="请填写平台扣点比例(%)" clearable :controls="false"
                                        :precision="3" :max="100" :min="0" style="width: 180px" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="平台扣点金额" prop="goodsCompetePlatformKdAmount">
                                    <el-tooltip class="item" effect="dark" content="售价*平台扣点比例" placement="top">
                                        <el-input-number v-model="chooseFormData.goodsCompetePlatformKdAmount"
                                            @change="onCompLrWc" type="number" placeholder="请填写平台扣点金额" clearable
                                            :controls="false" :precision="3" :max="9999999" :min="0"
                                            style="width: 180px" />
                                    </el-tooltip>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="平台罚款比例%" prop="goodsCompetePlatformFkRate">
                                    <el-input-number v-model="chooseFormData.goodsCompetePlatformFkRate" type="number"
                                        @change="onSalePrice" placeholder="请填写平台罚款比例(%)" clearable :controls="false"
                                        :precision="3" :max="100" :min="0" style="width: 180px" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="平台罚款金额" prop="goodsCompetePlatformFkAmount">
                                    <el-tooltip class="item" effect="dark" content="售价*平台罚款比例" placement="top">
                                        <el-input-number v-model="chooseFormData.goodsCompetePlatformFkAmount"
                                            @change="onCompLrWc" type="number" placeholder="请填写平台罚款金额" clearable
                                            :controls="false" :precision="3" :max="9999999" :min="0"
                                            style="width: 180px" />
                                    </el-tooltip>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="6">
                                <el-form-item label="发货后退款比例%" prop="goodsCompeteFhhtkRate"
                                    v-if="!(chooseFormData.goodsCompetePlatform == 8)">
                                    <el-input-number v-model="chooseFormData.goodsCompeteFhhtkRate" type="number"
                                        @change="onSalePrice" placeholder="请填写发货后退款比例(%)" clearable :controls="false"
                                        :precision="3" :max="100" :min="0" style="width: 180px" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="发货后退款金额" prop="goodsCompeteFhhtkAmount"
                                    v-if="!(chooseFormData.goodsCompetePlatform == 8)">
                                    <el-tooltip class="item" effect="dark" content="(出仓成本+产品规格成本)*发货后退款比例"
                                        placement="top">
                                        <el-input-number v-model="chooseFormData.goodsCompeteFhhtkAmount" type="number"
                                            @change="onCompLrWc" placeholder="请填写发货后退款金额" clearable :controls="false"
                                            :precision="3" :max="9999999" :min="0" style="width: 180px" />
                                    </el-tooltip>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="进货运费比例%" prop="goodsCompeteJhyfRate"
                                    v-if="!(chooseFormData.goodsCompetePlatform == 8)">
                                    <el-input-number v-model="chooseFormData.goodsCompeteJhyfRate" type="number"
                                        @change="onSalePrice" placeholder="请填写进货运费比例(%)" clearable :controls="false"
                                        :precision="3" :max="100" :min="0" style="width: 180px" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="进货运费金额" prop="goodsCompeteJhyfAmount"
                                    v-if="!(chooseFormData.goodsCompetePlatform == 8)">
                                    <el-tooltip class="item" effect="dark" content="产品规格成本*进货运费比例" placement="top">
                                        <el-input-number v-model="chooseFormData.goodsCompeteJhyfAmount" type="number"
                                            @change="onCompLrWc" placeholder="请填写进货运费金额" clearable :controls="false"
                                            :precision="3" :max="9999999" :min="0" style="width: 180px" />
                                    </el-tooltip>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="6">
                                <el-form-item label="进货退货损耗比例%" prop="goodsCompeteJhThShRate"
                                    v-if="chooseFormData.goodsCompetePlatform == 8">
                                    <el-input-number v-model="chooseFormData.goodsCompeteJhThShRate" type="number"
                                        @change="onSalePrice" placeholder="请填写进货退货损耗比例(%)" clearable :controls="false"
                                        :precision="3" :max="100" :min="0" style="width: 180px" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="进货退货损耗金额" prop="goodsCompeteJhThShAmount"
                                    v-if="chooseFormData.goodsCompetePlatform == 8">
                                    <el-input-number v-model="chooseFormData.goodsCompeteJhThShAmount" type="number"
                                        @change="onCompLrWc" placeholder="请填写进货退货损耗金额" clearable :controls="false"
                                        :precision="3" :max="9999999" :min="0" style="width: 180px" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="利润(外仓)" prop="goodsCompeteLrWc">
                                    <el-input-number v-model="chooseFormData.goodsCompeteLrWc" type="number"
                                        placeholder="请填写利润(外仓)" disabled clearable :controls="false" :precision="3"
                                        :max="9999999" :min="-9999999" style="width: 180px" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="利润率%(外仓)" prop="goodsCompeteLrlWc">
                                    <el-input-number v-model="chooseFormData.goodsCompeteLrlWc" type="number"
                                        placeholder="" disabled clearable :controls="false" :precision="2"
                                        :max="9999999" :min="-9999999" style="width: 180px" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </transition>

                <el-row>
                    <el-col :span="8">
                        <!-- :rules="[
                            { required: true, message: '请填写竞品标题', trigger: 'blur' },
                            { min: 4, max: 100, message: '长度在 4 到 100 个字符', trigger: 'blur' }]" -->
                        <el-form-item label="供应商名称" prop="supplierName" :rules="[
                            { required: true, message: '请填写供应商名称', trigger: 'blur', type: 'string' },
                            { max: 50, message: '长度50个字符内', trigger: 'blur', type: 'string' }]">
                            <el-input v-model="chooseFormData.supplierName" style="width: 250px"
                                :maxlength="50"></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="供应商链接" prop="supplierLink" :rules="[
                            { required: true, message: '请填写供应商链接', trigger: 'blur', type: 'string' },
                            { max: 100, message: '长度100个字符内', trigger: 'blur', type: 'string' }]">
                            <el-input v-model="chooseFormData.supplierLink" style="width: 250px"
                                :maxlength="100"></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <!-- :rules="[
                        { min: 0, max: 100, message: '长度不能超过100个字符', trigger: 'blur' }]" -->
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model="chooseFormData.remark" @input="changeup" :maxlength="100"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="4">
                        <!-- :rules="[
                        { required: true, message: '请选择资质图片', trigger: 'blur' }]" -->
                        <el-form-item label="质检报告" prop="inspectionReportImgUrl">
                            <yh-img-upload :limit="1" :value.sync="chooseFormData.inspectionReportImgUrl" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="4">
                        <!-- :rules="[
                        { required: true, message: '请选择资质图片', trigger: 'blur' }]" -->
                        <el-form-item label="质检报告文件" prop="inspectionReportPdfUrls">
                            <YhImgUpload :value.sync="chooseFormData.inspectionReportPdfUrls" :isImg="false"
                                accept=".pdf,.xlsx,.docx" :limit="11"></YhImgUpload>
                        </el-form-item>
                    </el-col>

                    <el-col :span="4">
                        <!-- :rules="[
                        { required: true, message: '请选择专利图片', trigger: 'blur' }]" -->
                        <el-form-item label="专利资质" prop="patentQualificationImgUrls">
                            <yh-img-upload :limit="10" :value.sync="chooseFormData.patentQualificationImgUrls" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="4">
                        <!-- :rules="[
                        { required: true, message: '请选择专利PDF', trigger: 'blur' }]" -->
                        <el-form-item label="专利资质文件" prop="patentQualificationPdfUrls">
                            <YhImgUpload :value.sync="chooseFormData.patentQualificationPdfUrls" :isImg="false"
                                accept=".pdf,.xlsx,.docx" :limit="11"></YhImgUpload>
                        </el-form-item>
                    </el-col>

                    <!-- <el-col   :span="4">
                        <el-form-item label="报价凭证" prop="qualificationPdfUrls" >
                            <YhImgUpload :value.sync="chooseFormData.qualificationPdfUrls"  :isImg="false"  :limit="9" ></YhImgUpload>
                        </el-form-item>
                    </el-col> -->

                    <el-col :span="4">
                        <!-- :rules="[
                        { required: true, message: '请选择资质PDF', trigger: 'blur' }]" -->
                        <el-form-item label="包装图片" prop="packingImgUrls">
                            <YhImgUpload :value.sync="chooseFormData.packingImgUrls" :limit="10"></YhImgUpload>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    竞品平台
                </el-row>
                <vxe-table :data="chooseFormData.tablelist">
                    <!-- <vxe-column type="seq" width="60"></vxe-column> -->
                    <vxe-column field="platform" width="150" title="平台"></vxe-column>
                    <vxe-column field="price" width="200" title="价格">
                        <template #default="{ row }">
                            <!-- <el-input :max="9999999" @blur="row.price = row.price.slice(0,10)" type="number" v-model="row.price"></el-input> -->

                            <el-input-number v-model="row.price" type="number" clearable :controls="false"
                                :precision="3" :max="9999999" :min="0" @blur="row.price = row.price.slice(0, 10)" />
                        </template>
                    </vxe-column>
                    <vxe-column field="link" title="链接">
                        <template #default="{ row }">
                            <el-input maxlength="300" v-model="row.link"
                                @paste.native='pasteDescription(row)'></el-input>
                        </template>
                    </vxe-column>
                </vxe-table>



            </el-form>
        </template>

        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">
                    <!-- <el-button v-if="chooseFormData.type != 2" @click="onClose">取消</el-button>
                    <el-button v-if="chooseFormData.type != 2 && this.formEditMode" type="primary"
                        @click="onSave(true)">添加选品</el-button> -->

                    <!-- <el-button v-if="chooseFormData.type === 2" @click="onClose">取消</el-button>
                    <el-button v-if="chooseFormData.type === 2" type="primary" @click="onSave(true)">确定</el-button> -->
                    <el-button @click="onClose">取消</el-button>
                    <el-button type="primary" @click="onSave(true)">添加选品</el-button>
                </el-col>
            </el-row>
        </template>

    </my-container>
</template>
<script>


import { platformlist, formatPlatform } from "@/utils/tools";

import MyContainer from "@/components/my-container";

import YhImgUpload from '@/components/upload/yh-img-upload.vue';

import { getProductsByFilter } from '@/api/operatemanage/base/product'

import {
    isDoHotSaleGoodsAsync, getHotSaleGoodsByFilter
} from '@/api/operatemanage/productalllink/alllink'

import {
    saveHotSaleBrandPushNew, getAllBrandProductCategorys, getBrandProductCategory, getHotSaleBrandPushNewById, validateGoodsIdIsRepeat
} from '@/api/operatemanage/productalllink/LogisticsAnalyse.js';


import { getCurUserDepartmentName } from '@/api/operatemanage/base/dingdingShow'

export default {
    name: "AddChooseForm",
    components: { MyContainer, YhImgUpload },
    props: {
        isedit: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            goodsCategoryBase: ['收纳整理/箱包皮具', '艺术收藏用品', '服饰配件', '清洁用品', '运动健身用品', '户外渔具', '生活工具', '节庆用品礼品', '3C数码配件丶电器', '厨房用品',
                '孕产妇/婴童用品', '五金建材工具/仪器仪表', '宠物用品', '载具用品', '办公文具', '美妆美容美发美体用品', '饰品装饰', '玩具动漫周边', '居家布艺'],
            platformlist: platformlist,
            that: this,
            categoryall: {
                categoryone: [],
                categorytwo: [],
                categorythr: [],
            },
            chooseFormData: {
                oldType: 3,
                type: 3,
                newPlatform: null,
                platform: null,
                goodsCompeteId: "",
                goodsId: "",
                goodsName: "",
                goodsCompeteName: "",
                goodsCompeteImgUrl: "",
                chooseRemark: "",
                chooseGoodsType: "1",
                patentType: '',
                hotSaleGoodsChooseId: null,
                tablelist: [
                    {
                        platform: '拼多多',
                    },
                    {
                        platform: '抖音',
                    },
                    {
                        platform: '淘宝',
                    },
                ],
                goodsCompeteCccb: 1.2,
                goodsCompetePlatformKdRate: 1,
                goodsCompetePlatformFkRate: 0.5,
                goodsCompeteFhhtkRate: 5,
                goodsCompeteJhyfRate: 2,
                goodsCompeteJhThShRate: 5,

            },
            pageLoading: false,
            formEditMode: true,//是否编辑模式
            productList: [],
            rowid: 0,
            tansitionshow: false,
        };
    },
    created() {
        this.rowid = 0;
    },
    async mounted() {

        this.getcategoryallview();

    },
    methods: {
        async checkSearch(rule, value, callback) {
            let params = {
                id: this.rowid != 0 ? this.rowid : 0,
                GoodsCompeteId: value,
            }
            let res = await validateGoodsIdIsRepeat(params);
            if (!res.success) {
                return
            }
            if (res.data) {
                callback(new Error('该竞品ID已存在'));
            }
        },
        containsChinese(str) {
            const regex = /[\u4e00-\u9fa5]/;
            return regex.test(str);
        },
        extractLink(start, str) {
            if (start == 'https') {
                const regex = /https?:\/\/[^\s]+/g;
                const match = str.match(regex);
                return match ? match[0] : '';
            } else if (start == 'http') {
                const regex = /http?:\/\/[^\s]+/g;
                const match = str.match(regex);
                return match ? match[0] : '';
            } else {
                this.$message("请填写正确的链接")
            }

        },
        pasteDescription(val) {
            let _this = this;
            setTimeout(() => {
                let ischese = _this.containsChinese(val.link);
                let url = "";
                if (val.link.indexOf('https') != -1) {
                    url = _this.extractLink('https', val.link);
                } else if (val.link.indexOf('http') != -1) {
                    url = _this.extractLink('http', val.link);
                }

                if (val.link && ischese) {
                    this.$message({ message: "链接复制匹配更换成" + url, type: "success", });
                    if (val.platform == '拼多多') {
                        this.chooseFormData.tablelist[0].link = url;
                    } else if (val.platform == '抖音') {
                        this.chooseFormData.tablelist[1].link = url;
                    } else if (val.platform == '淘宝') {
                        this.chooseFormData.tablelist[2].link = url;
                    }

                }
            }, 600)


        },
        inputmax(num) {
            this.inputmax = this.inputmax.splice(0, num)
        },
        async getmsg(id) {
            this.rowid = id;
            let res = await getHotSaleBrandPushNewById({
                id: id
            });
            if (res?.success) {
                let allobj = res.data;
                this.chooseFormData = allobj;

                this.chooseFormData.goodsCategorys = this.chooseFormData.goodsCategory + "-" + this.chooseFormData.categoryLeve1 + "-" + this.chooseFormData.categoryLeve2;
                this.chooseFormData.tablelist = [
                    {
                        platform: '拼多多',
                        price: allobj.pddPrice,
                        link: allobj.pddLink
                    },
                    {
                        platform: '抖音',
                        price: allobj.dyPrice,
                        link: allobj.dyLink
                    },
                    {
                        platform: '淘宝',
                        price: allobj.tbPrice,
                        link: allobj.tbLink
                    },
                ]
            }
        },
        changeup() {
            this.$forceUpdate();
        },
        async getcategoryallview() {
            let res = await getAllBrandProductCategorys();
            if (res?.success) {
                this.categoryall.categorytwo = [];
                this.categoryall.categorythr = [];
                // this.chooseFormData.categoryLeve1 = "";
                // this.chooseFormData.categoryLeve2 = "";
                this.categoryall.categoryone = res.data;
            }
            this.$forceUpdate();
        },
        setSelectCategorys(data) {
            if (data == null || data == "") {
                this.chooseFormData.goodsCategory = "";
                this.chooseFormData.categoryLeve1 = "";
                this.chooseFormData.categoryLeve2 = "";
            } else {
                let obj = {}
                obj = this.categoryall.categoryone.find(function (item) {
                    return (item.mainCategory + '-' + item.categoryLevel1 + '-' + item.categoryLevel2) === data;
                })
                this.chooseFormData.goodsCategory = obj.mainCategory;
                this.chooseFormData.categoryLeve1 = obj.categoryLevel1;
                this.chooseFormData.categoryLeve2 = obj.categoryLevel2;
            }
            this.changeup();
        },
        chooseGoodsTypeChange(v) {
            this.chooseFormData.goodsCompeteId = "";
            this.chooseFormData.goodsCompeteName = "";
            this.productList = [];
            this.ProductChange(this.chooseFormData.goodsCompeteId);
        },
        typeChange(v) {
            if ((v == 1 || v == 2) && (this.chooseFormData.oldType == 1 || this.chooseFormData.oldType == 2)) {
                this.chooseFormData.oldType = v;
            }
            else if ((v == 3 || v == 4) && (this.chooseFormData.oldType == 3 || this.chooseFormData.oldType == 4)) {
                this.chooseFormData.oldType = v;
            }
            else {
                this.chooseFormData.oldType = v;
                this.chooseFormData.goodsCompeteId = ""
                this.chooseFormData.goodsCompeteName = ""
                this.productList = [];
            }
            this.ProductChange(this.chooseFormData.goodsCompeteId);
            if (v == 4) {
                this.chooseFormData.platform = 0;
                this.platformChange(0)
            } else {
                this.chooseFormData.platform = this.chooseFormData.platform == 0 ? null : this.chooseFormData.platform;
                this.chooseFormData.newPlatform = this.chooseFormData.newPlatform == 0 ? null : this.chooseFormData.newPlatform;
            }
            this.$refs["chooseForm"].clearValidate();
        },
        platformChange(v) {
            this.chooseFormData.goodsCompeteImgUrl = '';
            if (v == 0 && !this.chooseFormData.goodsCompeteId) {
                this.chooseFormData.goodsCompeteId = 'CJ_' + (new Date().valueOf()).toString();
            }
        },
        onClose() {
            this.$emit('close');
        },
        async getProductList(v) {
            if (v == null || v == "") {
                return;
            }
            this.productList = [];
            if ((this.chooseFormData.type == 1 || this.chooseFormData.type == 2) && this.chooseFormData.chooseGoodsType == 2) {
                //选择选品
                let reqRlt = await getHotSaleGoodsByFilter({ proCode: v })
                if (reqRlt && reqRlt.success) {
                    this.productList = reqRlt.data.map(item => {
                        return {
                            value: item.id, label: '【' + item.goodsCompeteId + '】【' + formatPlatform(item.newPlatForm) + '】' + item.goodsCompeteName,
                            title: item.goodsCompeteName, newPlatform: (item.newPlatForm == 0 ? null : item.newPlatForm), goodsCompeteId: item.goodsCompeteId
                        };
                    });
                }
            } else {
                let reqRlt = await getProductsByFilter({ proCode: v })
                if (reqRlt && reqRlt.success) {
                    this.productList = reqRlt.data.map(item => {
                        return { value: item.proCode, label: '【' + item.proCode + '】' + item.shopName + '【' + item.title + '】', title: item.title, newPlatform: item.platform };
                    });
                }
            }
        },
        ProductChange(value) {
            let obj = null;
            obj = this.productList.find(x => x.value == value);
            if (obj != null) {
                this.chooseFormData.goodsCompeteName = obj.title
                this.chooseFormData.platform = obj.newPlatform
                if (this.chooseFormData.type == 2) {
                    this.chooseFormData.newPlatform = obj.newPlatform
                }
            } else {
                this.chooseFormData.goodsCompeteName = ""
                if (this.chooseFormData.type == 2) {
                    this.chooseFormData.newPlatform = null
                }
                if (this.chooseFormData.type == 1 || this.chooseFormData.type == 2) {
                    this.chooseFormData.platform = null
                }
            }
        },
        async onSave(isClose) {
            if (await this.save()) {
                this.$emit('afterSave');
                if (isClose)
                    this.$emit('close');
            }
        },
        async loadData(val) {
            console.log(val, 'val');
            this.chooseFormData.goodsId = '';
            this.chooseFormData.goodsName = '';
            this.chooseFormData.isDo = true;
            this.chooseFormData.goodsCompeteId = "";
            this.chooseFormData.goodsCompeteName = "";
            this.chooseFormData.platform = null;
            this.chooseFormData.goodsCompeteImgUrl = "";
            this.chooseFormData.chooseRemark = "";
            this.chooseFormData.chooseGoodsType = "1";
            if (val.isOut == 'pdd') {
                this.chooseFormData.platform = 2
                this.chooseFormData.newPlatform = 2
                this.chooseFormData.goodsCompeteId = val.goodsId
                this.chooseFormData.goodsCompeteName = val.goodsName
            }
        },
        async save() {
            let that = this;
            this.pageLoading = true;
            this.onInternationalTypeChange();
            let saveData = { ...this.chooseFormData };

            //同品跨平台并且是选择的选品，需要重新对goodsCompeteId赋值
            if ((this.chooseFormData.type == 1 || this.chooseFormData.type == 2) && this.chooseFormData.chooseGoodsType == 2) {
                let obj = this.productList.find(x => x.value == this.chooseFormData.goodsCompeteId);
                saveData.hotSaleGoodsChooseId = this.chooseFormData.goodsCompeteId;
                saveData.goodsCompeteId = obj.goodsCompeteId;
            } else {
                saveData.hotSaleGoodsChooseId = null;
            }

            this.chooseFormData.tablelist.map((item) => {
                if (item.platform == '拼多多') {
                    saveData.pddPrice = item.price;
                    saveData.pddLink = item.link;
                } else if (item.platform == '抖音') {
                    saveData.dyPrice = item.price;
                    saveData.dyLink = item.link;
                } else if (item.platform == '淘宝') {
                    saveData.tbPrice = item.price;
                    saveData.tbLink = item.link;
                }
            })
            if (saveData.profitRate < 20) {
                that.$message({ message: '利润率不能小于20%', type: "error" });
                this.pageLoading = false;
                return false;
            }
            if (saveData.isInvoicing == 1 && !saveData.isInvoicingRate) {
                that.$message({ message: '请填写税点%且大于0', type: "error" });
                this.pageLoading = false;
                return false;
            }
            if (!saveData.isInvoicing) {
                saveData.isInvoicingRate = null;
            }

            try {
                let valid = await this.$refs["chooseForm"].validate();
                // let valid = true;
                if (valid) {
                    let reqRlt = await saveHotSaleBrandPushNew(saveData);
                    if (reqRlt && reqRlt.success) {
                        that.$message({ message: '操作成功！', type: "success" });
                    }
                    this.pageLoading = false;
                    this.$emit('onSearch');
                    return reqRlt && reqRlt.success;
                } else {
                    this.pageLoading = false;
                    return false;
                }

            } catch (error) {
                this.pageLoading = false;
                return false;
            }
        },
        onInternationalTypeChange(value) {
            if (this.chooseFormData.internationalType == 1) {
                if (this.chooseFormData.goodsCompeteId.toLowerCase().indexOf("kj") != 0) {
                    this.chooseFormData.goodsCompeteId = ("KJ" + this.chooseFormData.goodsCompeteId);
                }
            }
            else {
                if (this.chooseFormData.goodsCompeteId.toLowerCase().indexOf("kj") == 0) {
                    this.chooseFormData.goodsCompeteId = (this.chooseFormData.goodsCompeteId.substring(2));
                }
            }
        },
        onCostPrice() {
            let cost = (this.chooseFormData.costPrice ?? 0);
            let sale = (this.chooseFormData.salePrice ?? 0);
            if (cost && sale)
                this.chooseFormData.profitRate = (sale - cost) / sale * 100.0;
        },
        onGoodsCompetePlatformChange() {
            if (this.chooseFormData.goodsCompetePlatform == 8) {
                this.chooseFormData.goodsCompetePlatformKdRate = 6.5;
                this.chooseFormData.goodsCompetePlatformFkRate = 0.3;
            }
            else {
                this.chooseFormData.goodsCompetePlatformKdRate = 1;
                this.chooseFormData.goodsCompetePlatformFkRate = 0.5;
            }

            this.onSalePrice();
        },
        onSalePrice() {
            console.log("onSalePriceonSalePriceonSalePrice", "onSalePrice")
            let cost = (this.chooseFormData.costPrice ?? 0);
            let sale = (this.chooseFormData.salePrice ?? 0);
            console.log(cost, "costcostcost");
            console.log(sale, "salesalesale");

            if (cost && sale) {
                this.chooseFormData.profitRate = (sale - cost) / sale * 100.00;
            }

            //平台扣款金额
            let rate1 = this.chooseFormData.goodsCompetePlatformKdRate ?? 0.00;
            this.chooseFormData.goodsCompetePlatformKdAmount = sale * rate1 / 100.00;
            //平台罚款金额
            let rate2 = this.chooseFormData.goodsCompetePlatformFkRate ?? 0.00;
            this.chooseFormData.goodsCompetePlatformFkAmount = sale * rate2 / 100.0;

            let cpggcb = this.chooseFormData.goodsCompeteGgCostPrice ?? 0.00;
            let cccb = this.chooseFormData.goodsCompeteCccb ?? 0.00;
            //发货后退款金额 （出仓成本+产品规格成本）*发货后退款 比例
            let rate3 = this.chooseFormData.goodsCompeteFhhtkRate ?? 0.00;
            this.chooseFormData.goodsCompeteFhhtkAmount = (cpggcb + cccb) * rate3 / 100.00;
            //进货运费金额=产品规格成本*产品进货运费比例 
            let rate4 = this.chooseFormData.goodsCompeteJhyfRate ?? 0.00;
            this.chooseFormData.goodsCompeteJhyfAmount = cpggcb * rate4 / 100.00;
            //进货与退货损耗金额=售价*进货与退货损耗比例
            let rate5 = this.chooseFormData.goodsCompeteJhThShRate ?? 0.00;
            this.chooseFormData.goodsCompeteJhThShAmount = sale * rate5 / 100.00;

            this.onCompLrWc();
        },

        onCompLrWc() {
            let sale = (this.chooseFormData.salePrice ?? 0);
            let cpggcb = this.chooseFormData.goodsCompeteGgCostPrice ?? 0.00;
            let cccb = this.chooseFormData.goodsCompeteCccb ?? 0.00;
            //利润(外仓) 
            //利润=售价-产品规格成本-平台扣点-平台罚款-发货后退款-产品进货运费-出仓成本
            //利润=售价-产品规格成本-出成成本-平台罚款-进货与退货损耗-平台扣点
            if (sale && !(this.chooseFormData.goodsCompetePlatform == 8)) {
                this.chooseFormData.goodsCompeteLrWc =
                    (sale - cpggcb - cccb
                        - (this.chooseFormData.goodsCompetePlatformKdAmount ?? 0)
                        - (this.chooseFormData.goodsCompetePlatformFkAmount ?? 0)
                        - (this.chooseFormData.goodsCompeteFhhtkAmount ?? 0)
                        - (this.chooseFormData.goodsCompeteJhyfAmount ?? 0)
                    );
                this.chooseFormData.goodsCompeteLrlWc = this.chooseFormData.goodsCompeteLrWc / sale * 100;
            }
            else if (sale && (this.chooseFormData.goodsCompetePlatform == 8)) {
                this.chooseFormData.goodsCompeteLrWc =
                    (sale - cpggcb - cccb
                        - (this.chooseFormData.goodsCompetePlatformKdAmount ?? 0)
                        - (this.chooseFormData.goodsCompetePlatformFkAmount ?? 0)
                        - (this.chooseFormData.goodsCompeteJhThShAmount ?? 0)
                    );
                this.chooseFormData.goodsCompeteLrlWc = this.chooseFormData.goodsCompeteLrWc / sale * 100;
            }
            else {
                this.chooseFormData.goodsCompeteLrWc = null;
                this.chooseFormData.goodsCompeteLrlWc = null;
            }
        },

        closeopen() {
            this.tansitionshow = !this.tansitionshow;
        },




    },
};
</script>

<style lang="scss" scoped>
.flexrow {
    display: flex;
    flex-direction: row;
}
</style>
