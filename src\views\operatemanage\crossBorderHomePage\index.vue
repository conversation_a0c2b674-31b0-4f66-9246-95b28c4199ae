<template>
  <MyContainer style="height: 98%;">
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :clearable="false" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-select v-model="ListInfo.district" placeholder="地区" class="publicCss" filterable multiple collapse-tags
          clearable>
          <el-option key="义乌" label="义乌" value="义乌" />
          <el-option key="南昌" label="南昌" value="南昌" />
          <el-option key="深圳" label="深圳" value="深圳" />
          <el-option key="西安" label="西安" value="西安" />
          <el-option key="武汉" label="武汉" value="武汉" />
        </el-select>
        <el-select v-model="ListInfo.post" placeholder="岗位" class="publicCss" filterable multiple collapse-tags
          clearable>
          <el-option key="无运营组业绩" label="无运营组业绩" value="无运营组业绩" />
          <el-option key="跨境平台经理" label="跨境平台经理" value="跨境平台经理" />
          <el-option key="上架运营助理" label="上架运营助理" value="上架运营助理" />
          <el-option v-for="item in postData" :key="item.district" :label="item.district" :value="item.district" />
        </el-select>
        <el-select v-model="ListInfo.platform" placeholder="平台" class="publicCss" filterable clearable>
          <el-option key="SHEIN" label="SHEIN" :value="12" />
          <el-option key="TEMU" label="TEMU" :value="13" />
        </el-select>
        <el-select v-model="ListInfo.userName" placeholder="人员" filterable clearable class="publicCss">
          <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.label" />
        </el-select>
        <el-select v-model="ListInfo.employeeStatus" placeholder="工作状态" class="publicCss" filterable collapse-tags clearable>
          <el-option label="在职" :value="1" />
          <el-option label="离职" :value="2" />
        </el-select>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" v-if="checkPermission('expenseSettingAuthority')"
          @click="onCostSetting">费用设置</el-button>
      </div>
    </template>
    <vxetablebase :id="'crossBorderHomePage_index202408041650'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :showsummary="true"
      :summaryarry="summaryarry" style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'"  @summaryClick='onsummaryClick'>
      <template slot="right">
        <vxe-column width="60">
          <template #default="{ row, $index }">
            <div style="display: flex">
              <el-button type="text" @click="onTrendChart(row, null)">趋势图</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <!-- <vxetablebase :id="'overseasGodownCost20240906'" :tablekey="'overseasGodownCost20240906'" :tableData='tableData'
        :tableCols='tableCols' @sortchange='sortchange' :loading='loading' :border='true' :that="that" ref="vxetable"
        :showsummary='true' :summaryarry="summaryarry" @summaryClick='onsummaryClick'>
      </vxetablebase> -->
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="趋势图" :visible.sync="buscharDialog.visible" width="70%" v-dialogDrag append-to-body>
      <div>
        <buschar v-if="buscharDialog.visible" ref="refbuschar" :analysisData="buscharDialog.data"></buschar>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="costSettingPopup" width="20%" v-dialogDrag>
      <span slot="title">
        <span style="margin-right: 5%;">费用设置</span>
        <i class="el-icon-warning-outline"></i>
        <span style="color: red; font-size: 15px;">仅限temu</span>
      </span>
      <div style="height: 150px;">
        <el-form :model="expenseForm" ref="expenseForm" label-width="80px">
          <el-form-item label="快递费:" style="margin: 30px 0;">
            <el-input-number v-model="expenseForm.express" style="width: 80%;" :controls="false" :precision="2" :min="0"
              :max="1000"></el-input-number>
            元
          </el-form-item>
          <el-form-item label="打包费:" style="margin: 30px 0;">
            <el-input-number v-model="expenseForm.packageFree" style="width: 80%;" :controls="false" :precision="2"
              :min="0" :max="1000"></el-input-number>
            元
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="costSettingPopup = false">取 消</el-button>
        <el-button type="primary" @click="onExpenseSettingsSave">保 存</el-button>
      </div>
    </el-dialog>
    
    <el-dialog :visible.sync="buscharDialogSum.visible" width="80%" v-dialogDrag>
      <span>
        <buschar ref="buschar" v-if="buscharDialogSum.visible" :analysisData="buscharDialogSum.data"
          :loading="buscharDialogSum.loading"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialogSum.visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getDirectorGroupList, getDirectorList, getList as getshopList } from '@/api/operatemanage/base/shop'
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions, formatPlatform } from '@/utils/tools'
import buschar from '@/components/Bus/buschar'
import { getCrossBorderHomePage_Kj, importTemuListingAsync, getTemuExpressPackage, addOrEditTemuExpressPackage, getCrossBorderHomePage_KjAnalysis, getKjDistrict } from '@/api/bookkeeper/reportdayV2'
import dayjs from 'dayjs'
import { init } from "echarts";
const tableCols = [
  { width: 'auto', align: 'center', prop: 'district', label: '地区', },
  { width: 'auto', align: 'center', prop: 'post', label: '岗位', },
  { width: 'auto', align: 'center', prop: 'platForm', label: '平台', formatter: (row) => row.platForm == 12 ? 'SHEIN' : row.platForm == 13 ? 'TEMU' : '' },
  { width: 'auto', align: 'center', prop: 'userName', label: '人员', },
  { sortable: 'custom',summaryEvent: true, width: 'auto', align: 'center', prop: 'orderCount', label: '订单量', type: 'click', handle: (that, row, field, val) => that.onTrendChart(row, field, val), },
  { sortable: 'custom',summaryEvent: true, width: 'auto', align: 'center', prop: 'saleAmount', label: '订单销售金额', type: 'click', handle: (that, row, field, val) => that.onTrendChart(row, field, val), },
  { sortable: 'custom',summaryEvent: true, width: 'auto', align: 'center', prop: 'saleCost', label: '订单销售成本', type: 'click', handle: (that, row, field, val) => that.onTrendChart(row, field, val), },
  { sortable: 'custom',summaryEvent: true, width: 'auto', align: 'center', prop: 'profit3', label: '毛三利润', type: 'click', handle: (that, row, field, val) => that.onTrendChart(row, field, val), },
  { sortable: 'custom',summaryEvent: true, width: 'auto', align: 'center', prop: 'profit3Rate', label: '毛三利润率', type: 'click', handle: (that, row, field, val) => that.onTrendChart(row, field, val), },
  { sortable: 'custom',summaryEvent: true, width: 'auto', align: 'center', prop: 'onlineCount', label: '上架数量', type: 'click', handle: (that, row, field, val) => that.onTrendChart(row, field, val), },
  { sortable: 'custom',summaryEvent: true, width: 'auto', align: 'center', prop: 'onlinePassCount', label: '上架通过数量', type: 'click', handle: (that, row, field, val) => that.onTrendChart(row, field, val), },
]
export default {
  name: "index",
  components: {
    MyContainer, vxetablebase, buschar
  },
  data() {
    return {
      expenseForm: {
        express: undefined,//快递费
        packageFree: undefined,//打包费
      },
      costSettingPopup: false,//费用设置弹窗
      fileList: [],//上传文件列表
      postData: [],//地区岗位
      fileparm: {},//上传文件参数
      dialogVisible: false,//导入弹窗
      uploadLoading: false,//上传loading
      directorlist: [],//人员
      buscharDialog: {
        visible: false,//趋势图弹窗
        data: {},//趋势图数据
      },
      buscharDialogSum:{
        visible: false,//趋势图弹窗
        data: {},//趋势图数据
      },
      summaryarry: {},//汇总
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        district: [],//地区
        post: [],//岗位
        platform: null,//平台
        userName: null,//人员
        column: null,//列
        employeeStatus:1
      },
      timeRanges: [],//时间范围
      tableCols,//表头
      tableData: [],//表格数据
      total: 0,//总数
      loading: false,//加载中
      pickerOptions,//时间选择器配置
    }
  },
  async mounted() {
    await this.init()
    await this.getList()
  },
  methods: {
    async onExpenseSettingsSave() {
      const params = {
        express: this.expenseForm.express,
        packageFree: this.expenseForm.packageFree,
        platform: 13,
      }
      const { success } = await addOrEditTemuExpressPackage(params)
      if (success) {
        this.$message.success('保存成功')
        this.costSettingPopup = false
      }
    },
    async onCostSetting() {
      const { data, success } = await getTemuExpressPackage({ platform: 13 })
      if (!success) return
      if (data.list && data.list[0]) {
        this.expenseForm.express = data.list[0].express
        this.expenseForm.packageFree = data.list[0].packageFree
      }
      this.costSettingPopup = true
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importTemuListingAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async init() {
      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });

      let { data, success } = await getKjDistrict()
      if (!success) return
      this.postData = data
    },
    async onTrendChart(row, field, val) {
      let startTimes = null
      let endTimes = null
      if (this.ListInfo.startTime && this.ListInfo.endTime) {
        startTimes = this.ListInfo.startTime
        endTimes = this.ListInfo.endTime
        if (dayjs(endTimes).diff(dayjs(startTimes), 'month') < 1) {
          startTimes = dayjs(endTimes).subtract(1, 'month').format('YYYY-MM-DD')
        }
      }
      const params = {
        column: field ? field.prop : null,
        district: row.district,
        post: row.post,
        platform: row.platForm,
        userName: row.userName,
        startTime: startTimes,
        endTime: endTimes
      }
      const { data, success } = await getCrossBorderHomePage_KjAnalysis(params)
      if (!success) return
      let that = this
      that.buscharDialog.visible = true;
      that.buscharDialog.data = data
      that.buscharDialog.title = data.legend[0]
      that.$nextTick(() => {
        that.$refs.refbuschar.initcharts();
      });
    },

    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      const { data, success } = await getCrossBorderHomePage_Kj(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async onsummaryClick(property) {
      let startTimes = null
      let endTimes = null
      if (this.ListInfo.startTime && this.ListInfo.endTime) {
        startTimes = this.ListInfo.startTime
        endTimes = this.ListInfo.endTime
        if (dayjs(endTimes).diff(dayjs(startTimes), 'month') < 1) {
          startTimes = dayjs(endTimes).subtract(1, 'month').format('YYYY-MM-DD')
        }
      }
      var pager = this.$refs.pager.getPager();
      const params = {
        ...this.ListInfo,
        startTime: startTimes,
        endTime: endTimes
      };
      params.column = property;
      let that = this;
      that.listLoading = true;
      that.buscharDialogSum.loading = true;
      const res = await getCrossBorderHomePage_KjAnalysis(params).then(res => {
        that.buscharDialogSum.loading = false;
        that.buscharDialogSum.data = res.data;
        that.buscharDialogSum.title = res.data.legend[0];
      });
      that.listLoading = false;
      that.buscharDialogSum.visible = true;
      await that.$refs.buschar.initcharts();

    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 190px;
    margin-right: 5px;
  }
}

::v-deep .el-select__tags-text {
  max-width: 60px;
}
</style>
