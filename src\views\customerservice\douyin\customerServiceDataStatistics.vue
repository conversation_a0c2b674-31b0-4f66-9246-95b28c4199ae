<template>
  <my-container v-loading="pageLoading">
    <!--列表-->
    <ces-table ref="table" :that='that' :summaryarry="summaryarry" :isIndex='true' :hasexpand='false'
      @sortchange='sortchange' :tableData='groupinquirsstatisticslist' @select='selectchange' :isSelection='false' :isSelectColumn='false'
      :tableCols='tableCols' :loading="listLoading" style="height: 525px;">
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length"
        @get-page="getgroupinquirsstatisticsList" />
    </template>

    <el-dialog title="服务数据统计" :visible.sync="dialogVisibleSyjfw" width="60%" v-dialogDrag append-to-body>
      <sqinquirskfdtl1 v-if="dialogVisibleSyjfw" ref="sqinquirskfdtl1" style="height: 580px;" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyjfw = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import {
  getDouYinGroup,
  getDouYinPersonalEfficiencyKfPageList, getDouYinGroupEfficiencyChat
} from '@/api/customerservice/douyininquirs'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar'
import sqinquirskfstatisticsbyshop from '@/views/customerservice/douyin/sq/sqinquirskfstatisticsbyshop'
import sqinquirskfdtl1 from '@/views/customerservice/douyin/sq/sqinquirskfdtl1'
const tableCols = [
  { istrue: true, prop: 'groupName', label: '组名称', width: '120', sortable: 'custom' },
  {
    istrue: true, prop: 'sname', label: '姓名', width: '100', sortable: 'custom', formatter: (row) => row.sname
  },
  { istrue: true, prop: 'allCount', label: '评价数量', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'buManYiRate', label: '不满意率', width: '100', sortable: 'custom', formatter: (row) => row.buManYiRate + "%" },
  {
    istrue: true, prop: 'buManYiCount', label: '不满意人数', width: '100', sortable: 'custom', type: "click",
    handle: (that, row, column, cell) => that.canclick2(row, column, cell, "不满意")
  },
  { istrue: true, prop: 'manYiRate', label: '满意率', width: '100', sortable: 'custom', formatter: (row) => row.manYiRate + "%" },
  {
    istrue: true, prop: 'manYiCount', label: '满意人数', sortable: 'custom', type: "click",
    handle: (that, row, column, cell) => that.canclick2(row, column, cell, "满意")
  },
];
export default {
  name: "customerServiceDataStatistics",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar, sqinquirskfstatisticsbyshop, sqinquirskfdtl1 },
  data() {
    return {
      shopCode: "",//店铺编码
      that: this,
      filter: {
        groupType: null,
        inquirsType: null,
      },
      groupinquirsstatisticslist: [],//列表数据
      tableCols: tableCols,//表头
      total: 0,//总数
      summaryarry: { count_sum: 10 },//合计
      pager: { OrderBy: "groupName", IsAsc: false },//排序
      sels: [], // 列表选中列
      listLoading: false,//加载
      pageLoading: false,//加载
      selids: [],//选中的id
      dialogVisibleSyjfw: false,//服务数据统计
    };
  },
  async mounted() {

  },
  methods: {
    // 查询
    onSearch(row, filters) {
      if (row) {
        this.shopCode = row.shopCode;
      }
      if (filters) {
        this.filter = filters;
      }
      this.$refs.pager.setPage(1);
      this.getgroupinquirsstatisticsList();
    },
    async getgroupinquirsstatisticsList() {
      if (this.filter.sdate) {
        this.filter.startDate = this.filter.sdate[0];
        this.filter.endDate = this.filter.sdate[1];
      }
      else {
        this.filter.startDate = null;
        this.filter.endDate = null;
      }
      const para = { ...this.filter };
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
        shopCode: this.shopCode,
      };
      this.listLoading = true;
      const res = await getDouYinPersonalEfficiencyKfPageList(params);
      this.listLoading = false;
      this.total = res.data.total
      this.groupinquirsstatisticslist = res.data.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    async canclick2(row, column, cell, isManYi) {
      var fstartsdate = "";
      var fendsdate = "";
      if (this.filter.sdate) {
        var d = new Date(this.filter.sdate[0])
        fstartsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
        d = new Date(this.filter.sdate[1])
        fendsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
      }
      if (fstartsdate == "NaN-NaN-NaN") {
        fstartsdate = "";
        fendsdate = "";
      }
      var fsname = row.sname;
      var fgroupName = row.groupName;
      this.dialogVisibleSyjfw = true;
      this.$nextTick(() => {
        this.$refs.sqinquirskfdtl1.dialogOpenAfter({
          startDate: fstartsdate,
          endDate: fendsdate,
          sname: fsname,
          groupName: fgroupName,
          isManYi: isManYi,
          shopCode: this.shopCode,
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
