<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 10px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.styleProductCode" placeholder="系列产品" maxlength="50" clearable
          class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'productSeriesSummary202408041512'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :summaryarry='summaryarry' :showsummary='true'
      :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { getPettyPaymentProductSeriesSum_DYPageList, exportPettyPaymentProductSeriesSum_DYList } from '@/api/customerservice/douyinrefund'

const tableCols = [
  { istrue: true, prop: 'styleProductCode', label: '系列产品', align: 'center', width: '100' },
  { istrue: true, prop: 'onlyOrderCount', label: '仅退款单量', sortable: 'custom', align: 'center', width: '100', type: 'click', handle: (that, row) => that.onclick(row, 1), },
  { istrue: true, prop: 'onlyYSendOrderCount', label: '已发货仅退款单量', sortable: 'custom', align: 'center', width: '100', type: 'click', handle: (that, row) => that.onclick(row, 1), },
  { istrue: true, prop: 'onlyNSendOrderCount', label: '未发货仅退款单量', sortable: 'custom', align: 'center', width: '100', type: 'click', handle: (that, row) => that.onclick(row, 1), },
  { istrue: true, prop: 'onlyAllAmount', label: '仅退款总金额', sortable: 'custom', align: 'center', width: '100', type: 'click', handle: (that, row) => that.onclick(row, 1), },
  { istrue: true, prop: 'onlyYSendOrderAmount', label: '已发货仅退款金额', sortable: 'custom', align: 'center', width: '100', type: 'click', handle: (that, row) => that.onclick(row, 1), },
  { istrue: true, prop: 'onlyNSendOrderAmount', label: '未发货仅退款金额', sortable: 'custom', align: 'center', width: '100', type: 'click', handle: (that, row) => that.onclick(row, 1), },
  { istrue: true, prop: 'onlysaleAfterReasonCount', label: '仅退款售后原因', sortable: 'custom', align: 'center', width: '100', type: 'click', handle: (that, row) => that.onclick(row, 1), },
  { istrue: true, prop: 'backOrderCount', label: '退货退款单量', sortable: 'custom', align: 'center', width: '100', type: 'click', handle: (that, row) => that.onclick(row, 2), },
  { istrue: true, prop: 'backAllAmount', label: '退货退款总金额', sortable: 'custom', align: 'center', width: '100', type: 'click', handle: (that, row) => that.onclick(row, 2), },
  { istrue: true, prop: 'backExpressCount', label: '退回快递', sortable: 'custom', align: 'center', width: '100', type: 'click', handle: (that, row) => that.onclick(row, 2), },
  { istrue: true, prop: 'backExpressComCount', label: '退货物流公司', sortable: 'custom', align: 'center', width: '100', type: 'click', handle: (that, row) => that.onclick(row, 2), },
  { istrue: true, prop: 'backSaleAfterReasonCount', label: '退货退款售后原因', sortable: 'custom', align: 'center', width: '100', type: 'click', handle: (that, row) => that.onclick(row, 2), },
  { istrue: true, prop: 'backYesOrderCount', label: '已退货退款单量', sortable: 'custom', align: 'center', width: '100', type: 'click', handle: (that, row) => that.onclick(row, 2), },
  { istrue: true, prop: 'backNoOrderCount', label: '未退货退款单量', sortable: 'custom', align: 'center', width: '100', type: 'click', handle: (that, row) => that.onclick(row, 2), },
  { istrue: true, prop: 'backYesOrderAmount', label: '已退货退款金额', sortable: 'custom', align: 'center', width: '100', type: 'click', handle: (that, row) => that.onclick(row, 2), },
  { istrue: true, prop: 'backNoOrderAmount', label: '未退货退款金额', sortable: 'custom', align: 'center', width: '100', type: 'click', handle: (that, row) => that.onclick(row, 2), },
  { istrue: true, prop: 'sumOrderCount', label: '总单量', sortable: 'custom', align: 'center', width: '100' },
]
export default {
  name: "productSeriesSummary",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startDate: null,//开始时间
        endDate: null,//结束时间
        styleProductCode: null,//系列产品
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      summaryarry: {},//汇总
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    onclick(row, val) {
      this.$emit('update', row, this.timeRanges, val);
    },
    async changeTime(e) {
      this.ListInfo.startDate = e ? e[0] : null
      this.ListInfo.endDate = e ? e[1] : null
    },
    async exportProps() {
      this.loading = true
      const { data } = await exportPettyPaymentProductSeriesSum_DYList(this.ListInfo)
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '汇总数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
      this.loading = false
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges?.length == 0) {
        this.ListInfo.startDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
      }
      const replaceArr = ['styleProductCode']
      this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
      this.loading = true
      const { data, success } = await getPettyPaymentProductSeriesSum_DYPageList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}
</style>
