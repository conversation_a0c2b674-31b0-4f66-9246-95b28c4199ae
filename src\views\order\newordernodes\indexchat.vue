<template >
    <my-container v-loading="pageLoading" style="height:100%">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item style="padding: 0;margin: 0; ">
                    <el-select v-model="filter.orderSituation" placeholder="订单情况" style="width:100px;"
                        @change="onSearch(false)" clearable>
                        <el-option label="正常" :value="1"></el-option>
                        <el-option label="停发" :value="4"></el-option>
                        <el-option label="预售" :value="2"></el-option>
                        <el-option label="缺货" :value="3"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0; ">
                    <el-select v-model="filter.seriesTimeType" placeholder="请选择周期" style="width:100px;"
                        @change="onSearch(false)">
                        <el-option label="日" :value="0"></el-option>
                        <el-option label="周" :value="1"></el-option>
                        <el-option label="月" :value="2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="" style="padding: 0;margin: 0; ">
                    <el-date-picker style="width: 320px" v-model="filter.timerange" type="datetimerange"
                        format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至"
                        start-placeholder="开始申请时间" end-placeholder="结束申请时间" :picker-options="pickerOptions">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="" style="padding: 0;">
                    <el-checkbox name="curno0bh" style="margin-left: 20px;" v-model="filter.curNo0Bh"
                        @change="onCurNo0Bh">筛选非0补货时长</el-checkbox>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch(true)">查询</el-button>
                    <el-checkbox name="curallselect" style="margin-left: 20px;" v-model="curAllTyped"
                        @change="onCurAllType">全选仓库</el-checkbox>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <!-- <div style="padding-top: 10px; font-size: 20px; font-weight: bold; text-align:center;">
                各仓平均时长趋势图
            </div> -->
            <div style="text-align: center; margin-bottom: 10px;">
                <el-checkbox name="curallselect" v-model="mycheck2.checkZc" @change="onChangeCheck2()">在仓</el-checkbox>
                <el-checkbox name="curallselect" v-model="mycheck2.checkSd" @change="onChangeCheck2()">审单</el-checkbox>
                <el-checkbox name="curallselect" v-model="mycheck2.checkBh" @change="onChangeCheck2()">补货</el-checkbox>
                <el-checkbox name="curallselect" v-model="mycheck2.checkDd" @change="onChangeCheck2()">打单</el-checkbox>
                <el-checkbox name="curallselect" v-model="mycheck2.checkPh" @change="onChangeCheck2()">配货</el-checkbox>
                <el-checkbox name="curallselect" v-model="mycheck2.checkDb" @change="onChangeCheck2()">打包</el-checkbox>
            </div>
            <div style="text-align: center; margin-bottom: 10px;">
                <el-checkbox name="curallselect" v-model="mycheck.checkQc" @change="onChangeCheck()">全仓汇总</el-checkbox>
                <el-checkbox name="curallselect" v-model="mycheck.checkYw"
                    @change="onChangeCheck()">义乌昀晗供应链管理有限公司</el-checkbox>
                <el-checkbox name="curallselect" v-model="mycheck.checkYwyt" @change="onChangeCheck()">义乌圆通</el-checkbox>
                <el-checkbox name="curallselect" v-model="mycheck.checkYt5l" @change="onChangeCheck()">圆通5楼仓</el-checkbox>
                <el-checkbox name="curallselect" v-model="mycheck.checkYtfh" @change="onChangeCheck()">圆通孵化仓</el-checkbox>
                <el-checkbox name="curallselect" v-model="mycheck.checkNccd" @change="onChangeCheck()">南昌昌东</el-checkbox>
                <el-checkbox name="curallselect" v-model="mycheck.checkNcdz" @change="onChangeCheck()">南昌定制仓</el-checkbox>
                <el-checkbox name="curallselect" v-model="mycheck.checkHzyh" @change="onChangeCheck()">杭州昀晗仓</el-checkbox>
                <el-checkbox name="curallselect" v-model="mycheck.checkXazj" @change="onChangeCheck()">西安重件仓</el-checkbox>
                <el-checkbox name="curallselect" v-model="mycheck.checkNccj" @change="onChangeCheck()">南昌裁剪仓</el-checkbox>
                <el-checkbox name="curallselect" v-model="mycheck.checkQt"
                    @change="onChangeCheck()">其他(包含未设定仓(分销)和代发仓)</el-checkbox>
            </div>
            <div>
                <buschar ref="indexchatbuschar" :analysisData="indexChatData" :legendChanges="legendChanges">
                </buschar>
            </div>
            <div style="padding-top: 10px; font-size: 20px; font-weight: bold; text-align:center;">
                影响原因趋势图
            </div>
            <div>
                <buschar ref="indexchatbuschar2" :analysisData="indexChatData2">
                </buschar>
            </div>
        </template>
    </my-container>
</template>
<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import {
    getNewAllOrderNotesChat
} from '@/api/order/newordernodes';
import { getInventoryOrderLackOrderMap } from '@/api/inventory/inventoryorder';
import MyContainer from "@/components/my-container";
import buschar from '@/components/Bus/buschar';
export default {
    name: "indexchat",
    components: { MyContainer, buschar },
    props: {
    },
    data() {
        return {
            that: this,
            pageLoading: false,
            filter: {
                timerange: [formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD 00:00:00"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")],
                startTime: null,
                endTime: null,
                orderSituation: null,
                seriesTimeType: 0,
                timeRangeType: 0,
                curNo0Bh: false,
            },
            mycheck: {
                checkQc: true,//全仓
                checkYw: false,//义乌昀晗供应链管理有限公司
                checkYwyt: false,//义乌圆通
                checkYt5l: false,//圆通5楼仓
                checkYtfh: false,//圆通孵化仓
                checkNccd: false,//南昌昌东
                checkNcdz: false,//南昌定制仓
                checkHzyh: false,//杭州昀晗仓
                checkXazj: false,//西安重件仓
                checkNccj: false,//南昌裁剪仓
                checkQt: false,//其他(包含未设定仓(分销)和代发仓)
            },
            selectmycheck: {
                checkQc: true,//全仓
                checkYw: false,//义乌昀晗供应链管理有限公司
                checkYwyt: false,//义乌圆通
                checkYt5l: false,//圆通5楼仓
                checkYtfh: false,//圆通孵化仓
                checkNccd: false,//南昌昌东
                checkNcdz: false,//南昌定制仓
                checkHzyh: false,//杭州昀晗仓
                checkXazj: false,//西安重件仓
                checkNccj: false,//南昌裁剪仓
                checkQt: false,//其他(包含未设定仓(分销)和代发仓)
            },
            mycheck2: {
                checkZc: true,
                checkSd: false,
                checkBh: false,
                checkDd: false,
                checkPh: false,
                checkDb: false,
            },
            indexChatData: [],
            indexChatSelectedLegend: [],
            curAllTyped: false,


            indexChatData2: [],
        };
    },
    async mounted() {
        await this.onSearch(true);
    },
    methods: {
        async onCurAllType() {
            if (this.curAllTyped) {
                Object.keys(this.mycheck).forEach(f => {
                    this.mycheck[f] = true;
                });
            }
            else {
                Object.keys(this.mycheck).forEach(f => {
                    this.mycheck[f] = this.selectmycheck[f];
                });
            }
            await this.onSearch(false);
        },
        async onChangeCheck() {
            Object.keys(this.selectmycheck).forEach(f => {
                this.selectmycheck[f] = this.mycheck[f];
            });
            await this.onSearch(false);
        },
        async onChangeCheck2() {
            await this.onSearch(false);
        },
        //非0补货时长
        async onCurNo0Bh() {
            await this.onSearch(true);
        },
        async legendChanges(selected) {
            this.indexChatSelectedLegend = selected;
        },
        async onSearch(isLoadChat) {
            //isLoadChat:是否重新加载数据源，否则从redis拿
            if (this.filter.timerange.length <= 0) {
                this.$message({ message: "请选择日期", type: "warning" });
                return;
            }
            this.filter.startTime = this.filter.timerange[0];
            this.filter.endTime = this.filter.timerange[1];
            var params = { ...this.filter, ...this.mycheck };
            params.isLoadChat = isLoadChat;
            console.log(params);
            this.pageLoading = true;
            //第一个趋势图数据源
            const res = await getNewAllOrderNotesChat(params);
            //第二个趋势图数据源
            const res2 = await getInventoryOrderLackOrderMap({ sdate: this.filter.startTime, edate: this.filter.endTime });
            this.pageLoading = false;
            // if (this.indexChatSelectedLegend) {
            //     var myselect = [];
            //     Object.keys(this.indexChatSelectedLegend).forEach(f => {
            //         if (this.indexChatSelectedLegend[f] == true) {
            //             myselect.push(f);
            //         }
            //     });
            //     if (myselect.length > 0) {
            //         //为了切换类型
            //         if (res.legend && res.legend.length > 0) {
            //             res.legend.forEach(f => {
            //                 if (f.includes("在仓") && myselect.findIndex(s => s === f) <= -1) {
            //                     myselect.push(f)
            //                 }
            //             });
            //         }
            //         res.selectedLegend = myselect;
            //     }
            // }
            if (res.legend && res.legend.length > 0) {
                res.legend.forEach(f => {
                    if (this.mycheck2.checkZc) {
                        if (f.includes("在仓") && res.selectedLegend.findIndex(s => s === f) <= -1) {
                            res.selectedLegend.push(f)
                        }
                    }
                    if (this.mycheck2.checkSd) {
                        if (f.includes("审单") && res.selectedLegend.findIndex(s => s === f) <= -1) {
                            res.selectedLegend.push(f)
                        }
                    }
                    if (this.mycheck2.checkBh) {
                        if (f.includes("补货") && res.selectedLegend.findIndex(s => s === f) <= -1) {
                            res.selectedLegend.push(f)
                        }
                    }
                    if (this.mycheck2.checkDd) {
                        if (f.includes("打单") && res.selectedLegend.findIndex(s => s === f) <= -1) {
                            res.selectedLegend.push(f)
                        }
                    }
                    if (this.mycheck2.checkPh) {
                        if (f.includes("配货") && res.selectedLegend.findIndex(s => s === f) <= -1) {
                            res.selectedLegend.push(f)
                        }
                    }
                    if (this.mycheck2.checkDb) {
                        if (f.includes("打包") && res.selectedLegend.findIndex(s => s === f) <= -1) {
                            res.selectedLegend.push(f)
                        }
                    }
                });
            }
            this.indexChatData = res;
            await this.$refs.indexchatbuschar.initcharts();

            this.indexChatData2 = res2;
            await this.$refs.indexchatbuschar2.initcharts();
        },
    },
};
</script>
<style lang="scss" scoped></style>
