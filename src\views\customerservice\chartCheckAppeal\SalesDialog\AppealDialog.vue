<template>
    <el-dialog title="申诉" :visible.sync="isShow" width="50%" :before-close="closeDialog" v-if="isShow" v-dialogDrag>
      <div class="">
        <el-descriptions size="middle" class="margin-top" title="" :column="3">
          <el-descriptions-item label="平台/店铺"> {{platformName}} / {{dataJson?.shopName}}</el-descriptions-item>
          <el-descriptions-item label="线上单号">{{dataJson?.orderNo}}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{conversationTime }}</el-descriptions-item>
          <el-descriptions-item v-if="interfaceType && dataJson?.afterSalesRemark !='' &&  dataJson?.afterSalesRemark  !=null" label="售后原因">{{dataJson?.afterSalesRemark }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <div style="height: 500px; overflow: auto" v-loading="isLoading">
        <div class="message-container" v-for="(message, index) in chartList" :key="index"
             :class="getMessageClass(message.userType)">
          <div v-if="message.userType == 3">
            <div class="system-box">
              <div class="system">
                <div>系统消息{{ chartList.length }}</div>
                <div style="margin-left: 20px">{{ message.recordTime }}</div>
              </div>
              <div style="color: #333" v-html="message.content"></div>
            </div>
          </div>
          <div v-if="message.userType == 0" style=" width:100%">
            <el-row>
              <el-col :span="1">
                <img src="@/assets/images/消费者.png" />
              </el-col>
              <el-col :span="23">
                <div class="bubble">
                  <div style="display: flex; color: #999;margin-bottom: 5px;">
                    <div>
                      <span class="consumer">消费者</span>
                      <span class="username"> {{ message.userName }}</span>
                    </div>
                    <div class="recordTime" style="margin-left: 10px">
                      {{ message.recordTime }}
                    </div>
                  </div>
                  <div v-if="isProbablyImage(message.content)"  >
                    <el-image   :src="message.content" :preview-src-list="[message.content]" ></el-image>
                  </div>
                  <div v-else class="message msg_consumer" style="text-align: left" v-html="message.content"></div>
                </div>
              </el-col>
            </el-row>

          </div>
          <div v-if="message.userType == 1" style=" width:100%">
            <el-row>
              <el-col :span="1">
                <img src="@/assets/images/商家.png" />
              </el-col>
              <el-col :span="23">
                <div class="bubble">
                  <div style="display: flex; color: #999;margin-bottom: 5px;">
                    <div>
                      <span class="merchant">商家</span>
                      <span class="username"> {{ message.userName }}</span>
                    </div>
                    <div class="recordTime" style="margin-left: 10px">
                      {{ message.recordTime }}
                    </div>
                  </div>
                  <div v-if="isProbablyImage(message.content)"  >
                    <el-image style="object-fit: cover;" :src="message.content" :preview-src-list="[message.content]"></el-image>
                  </div>
                  <div v-else class="message msg_merchant" v-html="message.content"></div>
                </div>
              </el-col>
            </el-row>
          </div>
          <div v-if="message.userType == 2">
            <div style="display: flex;align-items: left; color: #999;margin-bottom: 5px;">
              <div class="name">
                {{ message.userName }}
              </div>
              <div style="margin-left: 10px;line-height: 29px;">
                {{ message.recordTime }}
              </div>
            </div>
            <div class="avatar">
              <div v-if="isProbablyImage(message.content)">
                <el-image style="width: 500px; height: 500px" :src="message.content" :preview-src-list="[message.content]"></el-image>
              </div>
              <div v-else class="message" style="margin-right: 10px; text-align: left; color: #333" v-html="message.content"></div>
            </div>
          </div>

        </div>
      </div>
      <my-pagination  ref="chartPager" :total="chartTotal" @get-page="getChartList" />

      <el-descriptions size="middle" style="margin-top: 20px;" :column="2">
        <el-descriptions-item label="审核" style="flex: 1;">
          {{ filterInitialAuditType(dataJson.initialAuditType) }}
        </el-descriptions-item>
        <el-descriptions-item label="审核类型" style="flex: 1;"  v-if="dataJson.initialAuditType == 2">
          {{ dataJson.refuseInitialAuditType }}
        </el-descriptions-item>
        <el-descriptions-item label="责任人" style="flex: 1;"  v-if="!interfaceType">
          {{ dataJson?.person }}
        </el-descriptions-item>
        <el-descriptions-item label="退款原因稽查" style="flex: 1;"  v-if="!interfaceType">
          {{ dataJson.reasonForRefund }}
        </el-descriptions-item>
        <el-descriptions-item label="责任客服" style="flex: 1;"  >
          {{ dataJson.oldResponsibleName }}
        </el-descriptions-item>
        <el-descriptions-item label="说明" style="flex: 1;"  >
          {{ dataJson.initialAuditRemark }}
        </el-descriptions-item>
        <el-descriptions-item label="审核人" style="flex: 1;"  >
          {{ dataJson.initialOperator }}
        </el-descriptions-item>
        <el-descriptions-item label="审核凭证"   >
          <div style="display: flex; flex-direction: row; flex-wrap: wrap; height: 60px; width: 220px; overflow: auto;align-items: center">
            <div v-for="(image, index) in iniImgsSplitList" :key="index">
              <el-image v-if="image" :src="image" style="height: auto;width: 40px;margin-left: 5px;vertical-align: center"
                        :preview-src-list="[image]"></el-image>
            </div>
          </div>
        </el-descriptions-item>

      </el-descriptions>

        <el-form ref="formDataRef" :model="formData" label-width="120px" style="margin-top:10px" :rules="addFormRules">
            <el-form-item label="责任客服：" prop="newMemberName">
                <template>
                    <el-select v-model="formData.responsibleName" :remote-method="remoteMethod" remote clearable
                        filterable maxlength="20">
                        <el-option v-for="item in customerlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </template>
            </el-form-item>

            <el-form-item label="说明:" prop="auditRemark">
                <el-input show-word-limit :maxlength="100" v-model="formData.auditRemark" type="textarea" />
            </el-form-item>

            <el-form-item label="上传凭证:" prop="firstStatus" v-if="isShow">
                <uploadimgFile v-if="editPriceVisible" ref="uploadimgFile" :disabled="isView" :ispaste="!isView"
                    :noDel="isView" :accepttyes="accepttyes" :isImage="true" :uploadInfo="chatUrls" :keys="[1, 1]"
                    @callback="getImg" :imgmaxsize="1" :limit="1" :multiple="true">
                </uploadimgFile>
            </el-form-item>

          <el-collapse :value="activeReviewIndex" @change="(val) => activeReviewIndex = val">
            <el-collapse-item :name="0" v-if="reviewList.length > 0">
              <template #title>
                <span>审核信息 1</span>
              </template>

              <div style="display: flex">
                <el-form-item label="审核:" class="first custom-label">
                  <div style="width: 50px;">{{ filterInitialAuditType(reviewList[0].initialAuditType) }}</div>
                </el-form-item>
                <el-form-item label="审核类型:" style="margin-left: 144px" class="first custom-label"
                              v-if="reviewList[0].initialAuditType == 2">
                  <div style="width:300px">{{ reviewList[0].refuseInitialAuditType }}</div>
                </el-form-item>
              </div>
              <div style="display: flex">
                <el-form-item label="数据编码:"  class="first custom-label">
                  <div style="width: 180px;">{{ reviewList[0].conversationId }}</div>
                </el-form-item>
                <el-form-item label="审核人:" class="custom-label">
                  <div style='width:100px'>{{ reviewList[0].initialOperator }}</div>
                </el-form-item>
              </div>
              <div style="display: flex">
                <el-form-item label="审核日期:"  class="custom-label">
                  <div style='width:150px'>{{ reviewList[0].initialOperatorTime }}</div>
                </el-form-item>
                <el-form-item label="审核凭证:" class="custom-label" style="margin-left: 44px">
                <span v-for="(image, imgIndex) in reviewList[0].initialAuditImgs ? reviewList[0].initialAuditImgs.split(',') : []" :key="imgIndex">
                  <el-image v-if="image" :src="image" :preview-src-list="[image]" style="padding-right:10px"></el-image>
                  <span v-else>无</span>
                </span>
                </el-form-item>
              </div>
              <div style="display: flex">
                <el-form-item label="责任客服:"  class="custom-label">
                  <div style='width:100px'>{{ reviewList[0].initialResponsibleName }}</div>
                </el-form-item>
                <el-form-item label="说明:"  class="custom-label">
                  <div>{{ reviewList[0].initialAuditRemark }}</div>
                </el-form-item>
              </div>
            </el-collapse-item>

            <el-collapse-item v-for="(review, index) in reviewList.slice(1)" :key="index" :name="(index + 1).toString()">
              <template #title>
                <span>审核信息 {{ index + 2 }}</span>
              </template>

              <div style="display: flex">
                <el-form-item label="审核:" class="first custom-label">
                  <div style="width: 50px;">{{ filterInitialAuditType(review.initialAuditType) }}</div>
                </el-form-item>
                <el-form-item label="审核类型:" style="margin-left: 144px" class="first custom-label"
                              v-if="review.initialAuditType == 2">
                  <div style="width:300px">{{ review.refuseInitialAuditType }}</div>
                </el-form-item>
              </div>
              <div style="display: flex">
                <el-form-item label="数据编码:"  class="first custom-label">
                  <div style="width: 180px;">{{ review.conversationId }}</div>
                </el-form-item>
                <el-form-item label="审核人:" class="custom-label">
                  <div style='width:100px'>{{ review.initialOperator }}</div>
                </el-form-item>
              </div>
              <div style="display: flex">
                <el-form-item label="审核日期:"  class="custom-label">
                  <div style='width:150px'>{{ review.initialOperatorTime }}</div>
                </el-form-item>
                <el-form-item label="审核凭证:" class="custom-label" style="margin-left: 44px">
                <span v-for="(image, imgIndex) in review.initialAuditImgs ? review.initialAuditImgs.split(',') : []" :key="imgIndex">
                  <el-image v-if="image" :src="image" :preview-src-list="[image]" style="padding-right:10px"></el-image>
                  <span v-else>无</span>
                </span>
                </el-form-item>
              </div>
              <div style="display: flex">
                <el-form-item label="责任客服:"  class="custom-label">
                  <div style='width:100px'>{{ review.initialResponsibleName }}</div>
                </el-form-item>
                <el-form-item label="说明:"  class="custom-label">
                  <div>{{ review.initialAuditRemark }}</div>
                </el-form-item>
              </div>
            </el-collapse-item>
          </el-collapse>


            <div class="dialog-footer" style="display:flex;justify-content: flex-end;margin-top: 20px">
              <div style="position: relative;">
                  <el-button @click="accept" type="primary" :disabled="responsiblePersonName">认责</el-button>
                  <div style="position: absolute;right:-20px;top:-20px;cursor:pointer;" >
                    <el-tooltip class="item" effect="dark" content="放弃申诉，直接认责" placement="top"><i class="el-icon-question"></i></el-tooltip>
                  </div>
                </div>
              <div style="position: relative;margin-left:20px;">
                  <el-button @click="btnChange('last')" type="primary" :disabled="isLastButtonDisabled">查看上一个</el-button>
                  <div style="position: absolute;right:-20px;top:-20px;cursor:pointer;" >
                    <el-tooltip class="item" effect="dark" content="点击键盘的上箭头↑，可以快速查看上一个" placement="top"><i class="el-icon-question"></i></el-tooltip>
                  </div>
                </div>
                <div style="position: relative;margin-left:20px;">
                  <el-button @click="btnChange('next')" type="primary" :disabled="isNextButtonDisabled">查看下一个</el-button>
                  <div  style="position: absolute;right:-20px;top:-20px; cursor:pointer;" >
                    <el-tooltip class="item" effect="dark" content="点击键盘的下箭头↓，可以快速查看下一个" placement="top"> <i class="el-icon-question"></i></el-tooltip>
                  </div>
                </div>
              <div style="position: relative;margin-left:20px;">
                <el-button @click="onSubmitDot" :disabled="isSubmitDisabled" :loading="isLoading" type="primary"
                           v-throttle="3000">确认提交</el-button>
              </div>


            </div>

        </el-form>


    </el-dialog>
</template>
<script>
import { queryAllCustomerServiceDDUserTop100 } from "@/api/admin/deptuser";
import {
  getGroupSnameList,
  getUnPayOrderById,
  GetUnpayOrderSalesList,
  UnpayOrderAcceptResponsibility
} from "@/api/customerservice/chartCheck";
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import { getChartList } from "@/api/customerservice/unpaidorder";
import { formatTime } from "@/utils";


import {
    appealApplication, appealApplicationAfterSales
} from "@/api/customerservice/chartAppeal";
import { e, re } from "mathjs";
export default {

    props: {
        isShow: {
            type: Boolean,
            default: false,
        },

        isView: {
            type: Boolean,
            default: false
        },

        HistoryROW1: { type: Object, default: () => { } },
    },
    components: { uploadimgFile },
    data() {
        return {
            addFormRules: {
                auditRemark: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
            formData: {
                responsibleName: "",
                auditRemark: "",
                changeGroupName:"",
                changeGroupManager:"",
                changeResponsibleName:"",
            },
            chartTotal: 0,
            isLoading: false,
            chartList: [],
            dataJson: null,
            customerlist: [],
            orgOptions: [],
            editPriceVisible: false,//图片
            chatUrls: [],//图片
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',//上传格式
            isSubmitDisabled: false,
            pictures: null,
          keyWord: null,
          platform: null,
          responsiblePersonName:null,
          tableData:[],
          interfaceType:true,// true：售前 false：售后
          isLastButtonDisabled:false,
          isNextButtonDisabled:false,
          reviewList: [],
          activeReviewIndex: [0]
        };
    },
  created(){
    document.addEventListener('keydown', this.handleArrowUp);
  },
    async mounted() {
        this.orgOptions = [...this.customerlist];
        if (this.pictures) {
            this.chatUrls = this.pictures.split(',').map((item, i) => {
                return {
                    url: item,
                    name: `聊天截图${i + 1}`
                }
            })
        }
        this.editPriceVisible = true
    },
    watch: {
        isShow(newVal, oldVal) {
          if (newVal) {
            this.$nextTick(() => {
              this.$refs.chartPager.setPage(1);
              this.getChartList();
            });
          }
        },
    },
  computed:{
    platformName()//平台初始化
    {
      let platformList = [
        { name: "拼多多", value: 2 },
        { name: "抖音", value: 6 },
        { name: "天猫", value: 1 },
        { name: "淘工厂", value: 8 },
        { name: "淘宝", value: 9 },
      ]
      // console.log(this.dataJson);
      if (this.dataJson?.platform) {
        return platformList.filter(item => item.value == this.dataJson?.platform)[0].name
      } else {
        return ""
      }
    },
    conversationTime() //日期转换
    {
      return this.dataJson?.conversationTime?formatTime(this.dataJson?.conversationTime , "YYYY-MM-DD"):""
    },
    iniImgsSplitList() //审核图片分割
    {
      return this.dataJson?.initialAuditImgs ? this.dataJson?.initialAuditImgs.split(",") : "";
    },
  },
    methods: {
      filterInitialAuditType(type) {
        let name = ''
        if (type == 1) {
          name = '合格'
        } else if (type == 2) {
          name = '不合格'
        }
        return name;
      },
      async getChartList() {
        this.isLoading = true;
        var pager = this.$refs.chartPager.getPager();
        let data = {
          platform: this.platform,
          keyWordType: 2,
          keyWord: this.keyWord,
          ...pager,
        };
        const res = await getChartList(data);
        this.chartTotal = res.data.total;
        this.chartList = res.data.list;
        await this.buttonDisabled()//按钮是否禁用
        this.isLoading = false;
      },
      async buttonDisabled(){ //按钮是否禁用
        this.isLastButtonDisabled=false;
        this.isNextButtonDisabled=false;
        const index= this.tableData.indexOf(this.dataJson);
        if(index==0){
          this.isLastButtonDisabled=true;
        }
        if(index==this.tableData.length-1){
          this.isNextButtonDisabled=true;
        }
      },
      //消息框样式动态选择
      getMessageClass(isSent) {
        let className = ""
        switch (isSent) {
          case 0:// 用户
            className = "message-container-left"
            break;
          case 1:// 客服
            className = "message-container-right"
            break;
          case 2:// 机器人
            className = "message-container-right"
            break;
          case 3:// 系统
            className = "message-container-left"
            break;
        }
        return className
        // return isSent != 0 && isSent != 3 ? "message-container-right" : "message-container-left";
      },
      handleArrowUp(event) {
        if(!this.isShow){
          return
        }
        if (event.key === 'ArrowUp' && !this.isLastButtonDisabled) {
          this.btnChange('last');
        }
        if (event.key === 'ArrowDown' && !this.isNextButtonDisabled) {
          this.btnChange('next');
        }
      },
      async btnChange(last){
        const index= this.tableData.indexOf(this.dataJson);
        this.$refs.chartPager.setPage(1);
        let info;
        if(last=='last'){
          info=this.tableData[index-1]
        }else if(last=='next'){
          info=this.tableData[index+1]
        }
        this.dataJson=info;
        this.keyWord=info.conversationId;
        this.platform=info.platform;
        const res = await getUnPayOrderById({ id: this.dataJson.conversationId });
        if (res.success) {
          const thisData = res.data;
          console.log("thisData", thisData);
          if (thisData) {
            // 将接口数据插入
            this.dataJson.initialAuditType = thisData.initialAuditType;
            this.dataJson.refuseInitialAuditType = thisData.refuseInitialAuditType;
            this.dataJson.oldResponsibleName = thisData.responsibleName;
            this.dataJson.initialAuditRemark = thisData.initialAuditRemark;
            this.dataJson.initialOperator = thisData.initialOperator;
            this.dataJson.initialAuditImgs = thisData.initialAuditImgs;
            this.dataJson.person = thisData.person;
            this.dataJson.reasonForRefund = thisData.reasonForRefund;
          }
        }
        //获取是否已经审核的数据
        if (this.dataJson.conversationUserId) {
          var params = {
            conversationUserId: this.dataJson.conversationUserId,
            conversationId: this.dataJson.conversationId,
            salesType: 0,
            shopId: this.dataJson.shopId,
            auditState:2,
            orderBy: "createdTime",
            isAsc: false
          }
          const res = await GetUnpayOrderSalesList(params);
          if (!res?.success) {
            return;
          }
          this.reviewList = res.data.list;
        } else {
          this.reviewList = [];
        }
        await this.getChartList();
        await this.buttonDisabled()//按钮是否禁用
      },
      isProbablyImage(url) {
        return (url.match(/\.(jpeg|jpg|gif|png)$/) != null)
      },
        closeDialog() {
            this.$emit("closeDialog");
        },
        async remoteMethod(query) {
            if (query && query.length > 50) return this.$message.error("输入内容过长");
            if (query !== '') {
                var params = {
                    sname: query,
                    platform: this.dataJson.platform,
                    salesType: this.HistoryROW1.salesType
                }
                var res = await getGroupSnameList(params);
                if (res && res.success) {
                    this.customerlist = res.data?.map(item => {
                        return {
                            label: [
                                item.sname ? item.sname : '',
                                item.groupName ? `(${item.groupName})` : '',
                                item.groupManager ? ` 组长:` : '',
                                item.groupManager ? ` ${item.groupManager}` : ''
                            ].filter(Boolean).join(''),
                            value: [
                                item.sname ? item.sname : '',
                                item.groupName ? `${item.groupName}` : '',
                                item.groupManager ? ` ${item.groupManager}` : ''
                            ].filter(Boolean).join(','),
                            userName: item.sname,
                            extData: item
                        }
                    });
                }
            }
            else {
                this.customerlist = [...this.orgOptions];
            }
        },
        getImg(data) {
            console.log("pictures", this.pictures)
            if (data) {
                this.chatUrls = data
                this.pictures = data.map(item => item.url).join(',')
            }
        },
        async onSubmitDot() { //保存关闭
            this.isLoading = true;

            if (this.formData.responsibleName!=null) {
            var formDatas=this.formData.responsibleName.split(',');
            this.formData.changeResponsibleName=formDatas[0];
            this.formData.changeGroupName =formDatas[1];
            this.formData.changeGroupManager =formDatas[2];
            }
            let data = {
                ...this.formData,
                conversationId: this.dataJson.conversationId,
                remark: this.formData.auditRemark,
                // changeResponsibleName: this.formData.responsibleName,
                imgs: this.pictures,
            };
            const res = this.HistoryROW1.salesType == 0 ? await appealApplication(data) : await appealApplicationAfterSales(data);
            if (res?.success) {
                this.$emit("upData");
                this.formData.responsibleName = "";
                this.formData.auditRemark = null;
                this.pictures = null;
                this.chatUrls = [];
                this.$message = res.message;
                this.isLoading = false;
                this.closeDialog();
            } else {
                this.formData.responsibleName = "";
                this.formData.auditRemark = null;
                this.pictures = null;
                this.chatUrls = [];
                this.$message = res.message;
                this.isLoading = false;
            }


        },
        async accept()
        {
          this.$confirm('是否进行认责?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(async () => {
            var res = await UnpayOrderAcceptResponsibility({ conversationId: this.keyWord });
            if (res?.success) {
              this.$message({ message: "认责成功", type: "success" });
              this.closeDialog();
            }
          }).catch(() => {
          });

        }
    },



};
</script>
<style lang="scss" scoped>
.s{
  position: absolute;
  justify-content: flex-end;
  cursor: pointer;
}
//顶部可点击div样式
::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
    padding: 15px;
}

::v-deep .el-descriptions__body .el-descriptions__table {
    background-color: rgb(242, 244, 245);
}

::v-deep .el-form-item__label {
    color: #888888 !important;
    /* 更改为你想要的颜色 */
}

.tipColor {
    color: red;
}

.violations {

    margin-top: 20px;
    margin-left: 20px;
    margin-right: 20px;
    height: 200px;
}

.msg_merchant {
    padding: 10px;
    color: white;
    border-radius: 5px;
    background-color: rgb(64, 158, 255);
}

.msg_consumer {
    padding: 10px;
    border-radius: 5px;
    background-color: rgb(240, 246, 255);
}

.system-box {
    background-color: #fafafa;
    padding: 10px;
    box-sizing: border-box;
    width: 300px;
}

.system {
    display: flex;
    margin-bottom: 4px;
    color: #999;
}

.message-container {
    display: flex;
    align-items: center;
    margin: 10px 0;
}

.name {
    text-align: right;
    margin: 5px 0;
    color: #999;
}

.avatar {
    float: right;
    margin-right: 0px;
    /* 修改这里将头像放在消息框的右边 */
    display: flex;
    align-items: center;
    text-align: right
}

.avatar-image {
    width: 400px;
    height: 400px;
    // object-fit: cover;
}

.bubble {
    color: #000;
    border-radius: 5px;
    padding-left: 10px;
    padding-bottom: 10px;
}

.bubble2 {
    color: #000;
    border-radius: 5px;
    padding-right: 10px;
}

.message {
    text-align: left;
    margin: 0;
    width: 280px;
}

.message-container-right {
    justify-content: flex-end;
    margin-right: 10px !important;
}

.message-container-left {
    justify-content: flex-start;
}

.merchant {
    color: rgb(88, 170, 255) !important;
    border: 1px rgb(88, 170, 255) solid;
}

.consumer {
    color: red !important;
    border: 1px red solid;
}

.username {
    color: black !important;
    margin-right: 5px;
    margin-left: 5px;
}

.recordTime {
    color: gray !important;
}

//::v-deep .el-image .el-image__inner {
//    max-width: 500px !important;
//    max-height: 1000px !important;
//    height: auto;
//    width: 500px !important;
//}
</style>
