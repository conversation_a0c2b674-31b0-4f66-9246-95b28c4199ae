<template>
    <my-container v-loading="pageLoading">
        <el-row>
            <el-col :span="24">
                <el-input placeholder="请输入链接ID" v-model.trim="queryProCode" style="width:150px">
                    
                </el-input>
                <el-button type="primary" @click="onQuery">查询</el-button>
                <el-button type="waring" @click="onClear" style="margin-left:0">清理</el-button>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24">
                <el-tag size="mini" :key="tag.proCode" v-for="tag in queryList" closable
                    @close="onRemoveProCode(tag.proCode)" @click="onClickProCode(tag.proCode)">
                    {{ tag.proCode }}
                </el-tag>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24" style="max-height:180px;overflow-y: auto;">
                {{ currentProCodeInfo.proCode }}
                <br />
                <div v-for="tag in currentProCodeInfo.combSkuList" style="width:100%">                 
                    <el-link type="primary" @click="onCombSkuClick(tag)"
                        style="float:left;width:270px;height:50px;font-size:12px;overflow: hidden; text-overflow: ellipsis;">
                        <el-image v-if="tag.combPic && tag.combPic.length>5" style="width: 50px; height: 50px;float: left;" :src="tag.combPic"
                        :preview-src-list="[tag.combPic]">
                    </el-image>
                    {{ tag.combSku }}
                        {{ tag.combName }}
                    </el-link>
                  
                </div>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24">

            </el-col>
        </el-row>
        <el-row>
            <!-- 显示skus -->
            <el-col :xs="24" :sm="24" :md="8" :lg="6" :xl="4" v-for="(item, index) in currentCombSkuInfo.skuList">

                <el-descriptions :labelStyle="{width:'70px'}" :column="1" size="mini" border>
                    <el-descriptions-item>
                        <template slot="label">
                            <span style="color:red">组合SKU</span>
                        </template>
                        {{ item.sku }} (*{{ item.qty }})
                    </el-descriptions-item>
                    <el-descriptions-item v-if="item.info && item.info.goodFrontBackImgs && item.info.goodFrontBackImgs.length>5">
                        <template slot="label">
                            <span style="color:red">实物图</span>
                        </template>                       
                        <el-image :src="miniPic(JSON.parse(item.info.goodFrontBackImgs)[0].url)" 
                            :preview-src-list="JSON.parse(item.info.goodFrontBackImgs).map(x => x.url)">
                        </el-image>

                    </el-descriptions-item>                  
                    <el-descriptions-item  v-if="item.info">
                        <template slot="label">
                            长宽高重
                        </template>
                        {{ item.info.clLength }}*{{ item.info.clWeight }}*{{ item.info.clHeight }}mm_{{ item.info.clWeight }}g
                    </el-descriptions-item>
                    <el-descriptions-item v-if="item.info">
                        <template slot="label">
                            长宽高_重(图)
                        </template>
                        <el-image v-if="item.info.clLengthImgs && item.info.clLengthImgs.length>5"  :src="miniPic(JSON.parse(item.info.clLengthImgs)[0].url)" style="margin-right:5px"
                            :preview-src-list="JSON.parse(item.info.clLengthImgs).map(x => x.url)">
                        </el-image>

                        <el-image v-if="item.info.clWidthImgs && item.info.clWidthImgs.length>5"  :src="miniPic(JSON.parse(item.info.clWidthImgs)[0].url)" style="margin-right:5px"
                            :preview-src-list="JSON.parse(item.info.clWidthImgs).map(x => x.url)">
                        </el-image>
                        
                        <el-image v-if="item.info.clHeightImgs && item.info.clHeightImgs.length>5"  :src="miniPic(JSON.parse(item.info.clHeightImgs)[0].url)" style="margin-right:5px"
                            :preview-src-list="JSON.parse(item.info.clHeightImgs).map(x => x.url)">
                        </el-image>
                        <el-image v-if="item.info.clWeightImgs && item.info.clWeightImgs.length>5" :src="miniPic(JSON.parse(item.info.clWeightImgs)[0].url)" 
                            :preview-src-list="JSON.parse(item.info.clWeightImgs).map(x => x.url)">
                        </el-image>
                    </el-descriptions-item>
                    <el-descriptions-item v-if="item.info && item.info.clThicknessImgs && item.info.clThicknessImgs.length > 5">
                        <template slot="label">
                            厚({{ item.info.clThickness }})
                        </template>
                        <el-image :src="miniPic(JSON.parse(item.info.clThicknessImgs)[0].url)" 
                            :preview-src-list="JSON.parse(item.info.clThicknessImgs).map(x => x.url)">
                        </el-image>
                    </el-descriptions-item>
                    <el-descriptions-item v-if="item.info && item.info.qualificationType && item.info.qualificationType.length > 5">
                        <template slot="label">
                            专利资质
                        </template>
                        <span style="color:blue;cursor:pointer;"  @click="downloadFiles(item.info.qualificationType)">{{JSON.parse(item.info.qualificationType).length}}个文件</span>
                    </el-descriptions-item>
                    <el-descriptions-item v-if="item.info && item.info.qualifiedImgs && item.info.qualifiedImgs.length > 5">
                        <template slot="label">
                            合格证
                        </template>
                        <el-image :src="miniPic(JSON.parse(item.info.qualifiedImgs)[0].url)" 
                            :preview-src-list="JSON.parse(item.info.qualifiedImgs).map(x => x.url)">
                        </el-image>
                    </el-descriptions-item>
                    <el-descriptions-item v-if="item.info && item.info.execStandard && item.info.execStandard.length > 0">
                        <template slot="label">
                            执行标准
                        </template>
                        {{ item.info.execStandard }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="item.info && item.info.material && item.info.material.length > 0">
                        <template slot="label">
                            材质
                        </template>
                        {{ item.info.material }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="item.info && item.info.isPatent && item.info.isPatent.length > 0">
                        <template slot="label">
                            是否专利
                        </template>
                        {{ item.info.isPatent == true ? '是' : item.info.isPatent == false ? '否' : '' }}
                    </el-descriptions-item>
                </el-descriptions>


            </el-col>
        </el-row>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import { GetProductDocInfoById } from "@/api/customerext/ProductGoodsDocInfo";
import { matchImg } from '@/utils/getCols'

export default {
    name: "PubProductInfo",//商品资料库
    components: {
        MyContainer
    },
    data() {
        return {
            pageLoading: false,
            queryProCode: '',//查询商品ID
            currentProCode: '',//当前商品ID
            currentProCodeInfo: {},//当前商品信息
            currentCombSkuInfo: {},//组合子sku
            queryList: [],
        };
    },
    async mounted() {
    },
    methods: {
        downloadFiles(files){
                if(files){
                    let jsonF=JSON.parse(files);

                    this.$showDialogform({
                        path: `@/views/base/DownloadFilesForm.vue`,
                        title: '文件列表',
                        autoTitle: false,
                        args: { files:jsonF, mode: 3 },
                        height: 300,
                        width: '600px',
                        callOk:null
                    })
                }

            },
        onClickProCode(v) {
            this.queryProCode = v;
            this.onQuery();
        },
        miniPic(picUrl) {
            return matchImg(picUrl);
        },
        onCombSkuClick(combSkuInfo) {
            this.currentCombSkuInfo = combSkuInfo;
        },
        onRemoveProCode() {
            //清理记录
        },
        onClear() {
            this.currentProCode = '',
                this.queryList = [];

        },
        async onQuery() {
            if (!this.queryProCode || this.queryProCode.length < 5) {
                $message({
                    message: '请输入正确的链接ID',
                    type: 'warning'
                });
                return;
            }

            this.currentCombSkuInfo={};

            if (this.queryList && this.queryList.length > 0) {
                for (let i = 0; i < this.queryList.length; i++) {
                    if (this.queryList[i].proCode == this.queryProCode) {
                        this.currentProCode = this.queryList[i].proCode;
                        this.currentProCodeInfo = this.queryList[i];

                        this.queryProCode = '';

                        return;
                    }
                }
            }

            this.pageLoading = true;

            const res = await GetProductDocInfoById({
                proCode: this.queryProCode
            });

            if (res && res.success) {
                this.queryList.unshift(res.data);
                this.currentProCode = res.data.proCode;
                this.currentProCodeInfo = res.data;

                this.queryProCode = '';
            }
            this.pageLoading = false;
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep .el-image__inner{
    width:50px;
    height:50px;
}
</style>
