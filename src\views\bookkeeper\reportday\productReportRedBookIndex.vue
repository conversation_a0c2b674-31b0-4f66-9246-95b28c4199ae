<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="小红书日报" name="first1" style="height: 100%">
        <productReportRedBook ref="productReportRedBook" style="height: 100%"></productReportRedBook>
      </el-tab-pane>
      <el-tab-pane label="订单日报" name="first2" :lazy="true" style="height: 100%"
        v-if="checkPermission('RedBookOrderDayReport')">
        <RedBookOrderDayReport @ChangeActiveName2="ChangeActiveName2" :orderNoInner="orderNoInner"
          ref="RedBookOrderDayReport" style="height: 100%"></RedBookOrderDayReport>
      </el-tab-pane>
      <el-tab-pane label="编码日报" name="first3" :lazy="true" style="height: 100%"
        v-if="checkPermission('RedBookGoodCodeDayReport')">
        <RedBookGoodCodeDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="RedBookGoodCodeDayReport" style="height: 100%"></RedBookGoodCodeDayReport>
      </el-tab-pane>
      <el-tab-pane label="ID日报" name="first4" :lazy="true" style="height: 100%"
        v-if="checkPermission('RedBookIdDayReport')">
        <RedBookIdDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="RedBookIdDayReport" style="height: 100%"></RedBookIdDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺日报" name="first5" :lazy="true" style="height: 100%"
        v-if="checkPermission('RedBookShopDayReport')">
        <RedBookShopDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="RedBookShopDayReport" style="height: 100%"></RedBookShopDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺SKU日报" name="first7" :lazy="true" style="height: 100%"
        v-if="checkPermission('RedBookCommodityDayReport')">
        <RedBookCommodityDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="RedBookCommodityDayReport" style="height: 100%"></RedBookCommodityDayReport>
      </el-tab-pane>
      <el-tab-pane label="明细日报" name="first6" :lazy="true" style="height: 100%"
        v-if="checkPermission('RedBookDetailDayReport')">
        <RedBookDetailDayReport @ChangeActiveName="ChangeActiveName" @ChangeActiveName2="ChangeActiveName2"
          ref="RedBookDetailDayReport" style="height: 100%"></RedBookDetailDayReport>
      </el-tab-pane>
      <el-tab-pane label="出仓负利润ID订单明细" name="first8" :lazy="true" style="height: 100%"
        v-if="checkPermission('RedBookOutgoingprofitIDorderdetail')">
        <RedBookOutgoingprofitIDorderdetail @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="RedBookOutgoingprofitIDorderdetail" style="height: 100%"></RedBookOutgoingprofitIDorderdetail>
      </el-tab-pane>
      <el-tab-pane label="SKUS日报" name="first9" :lazy="true" style="height: 100%"
        v-if="checkPermission('RedBookOrderDayReport')">
        <RedBookSkusDayReport @ChangeActiveName2="ChangeActiveName2" :orderNoInner="orderNoInner" ref="RedBookSkusDayReport"
          style="height: 100%"></RedBookSkusDayReport>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import productReportRedBook from "./productReportRedBook.vue";
import RedBookOrderDayReport from "./RedBookOrderDayReport.vue";
import RedBookSkusDayReport from "./RedBookSkusDayReport.vue";
import RedBookGoodCodeDayReport from "./RedBookGoodCodeDayReport.vue";
import RedBookIdDayReport from "./RedBookIdDayReport.vue";
import RedBookCommodityDayReport from "./RedBookCommodityDayReport.vue";
import RedBookOutgoingprofitIDorderdetail from "./RedBookOutgoingprofitIDorderdetail.vue";
import RedBookShopDayReport from "./RedBookShopDayReport.vue";
import RedBookDetailDayReport from "./RedBookDetailDayReport.vue";
export default {
  name: "productReportRedBookIndex",
  components: {
    MyContainer, productReportRedBook, RedBookOrderDayReport, RedBookSkusDayReport, RedBookGoodCodeDayReport, RedBookIdDayReport, RedBookShopDayReport, RedBookDetailDayReport, RedBookCommodityDayReport, RedBookOutgoingprofitIDorderdetail
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },
  async mounted() {
    middlevue.$on('toLinkTxDetailDayReport', (data) => {
      if (data.isTrue && data.plat == 'tm') {
        this.activeName = 'first6';
        setTimeout(() => {
          middlevue.$emit('toLinkTxDetailDayReportQuery', data)
        }, 500);

      } else {
        this.activeName = 'first8';
        setTimeout(() => {
          middlevue.$emit('toLinkRedBookOutgoingprofitIDorderdetailQuery', data)
        }, 500);
      }
    })
  },
  //销毁事件
  beforeDestroy() {
    middlevue.$off('toLinkTxDetailDayReport')
  },
  methods: {
    ChangeActiveName(activeName) {
      this.activeName = 'first2';
      this.$refs.RedBookOrderDayReport.RedBookGoodCodeDayReportArgument(activeName)
    },
    ChangeActiveName2(activeName, No, Time) {
      this.activeName = 'first6';
      this.$refs.RedBookDetailDayReport.RedBookDetailDayReportArgument(activeName, No, Time)
    }
  },
};
</script>

<style lang="scss" scoped></style>
