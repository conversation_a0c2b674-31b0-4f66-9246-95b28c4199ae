<template>
  <MyContainer  v-loading="pageLoading">
    <template #header>
      <div class="top">
        <div class="top_one" style="display: flex;">
          <el-input v-model="ListInfo.distributor" placeholder="请输入分销商" maxlength="100" clearable class="publicCss" />
          <div class="publicCss">
            <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode" width="200px"
              placeholder="SKU编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
              @callback="productCodeCallback" title="SKU编码">
            </inputYunhan>
          </div>
          <el-input v-model="ListInfo.lables" placeholder="请输入商品标签" maxlength="100" clearable class="publicCss" />
          <el-input v-model="ListInfo.goodClassification" placeholder="请输入商品分类" maxlength="100" clearable
            class="publicCss" />
          <el-input v-model="ListInfo.brandName" placeholder="请输入商品品牌" maxlength="100" clearable class="publicCss" />
          <div style="border: 1px solid #ccc; border-radius: 5px; display: flex; margin-right: 10px;">
            <el-select v-model="ListInfo.selectAmountColName" slot="prepend" placeholder="销售金额" style="width: 100px;">
              <el-option label="销售金额" value="saleAmount"></el-option>
            </el-select>
            <el-input v-model.number="ListInfo.selectAmount1" placeholder="最小值" maxlength="8" clearable type="number"
              oninput="if(value.replace('-', '').length > 8) value = value.slice(0, 8)" class="pass_input"
              @input="validateInput($event, 'selectAmount1')" style="width: 90px; margin-right: 5px;" />
            <span style="margin: 0 0px;">-</span>
            <el-input v-model.number="ListInfo.selectAmount2" placeholder="最大值" maxlength="8" clearable type="number"
              oninput="if(value.replace('-', '').length > 8) value = value.slice(0, 8)" class="pass_input"
              @input="validateInput($event, 'selectAmount2')" style="width: 90px;" />
          </div>
          <div style="border: 1px solid #ccc; border-radius: 5px; display: flex; margin-right: 10px;">
            <el-select v-model="ListInfo.selectCountColName" slot="prepend" placeholder="销售订单数" style="width: 100px;">
              <el-option label="销售订单数" value="saleOrderCount"></el-option>
              <el-option label="销售件数" value="saleGoodCount"></el-option>
            </el-select>
            <el-input v-model="ListInfo.selectCount1" placeholder="最小值" maxlength="8" clearable class="pass_input"
              oninput="if(value.replace('-', '').length > 8) value = value.slice(0, 8)" type="number"
              @input="validateInput($event, 'selectCount1')" style="width: 90px; margin-right: 5px;" />
            <span style="margin: 0 0px;">-</span>
            <el-input v-model="ListInfo.selectCount2" placeholder="最大值" maxlength="8" clearable class="pass_input"
              oninput="if(value.replace('-', '').length > 8) value = value.slice(0, 8)" type="number"
              @input="validateInput($event, 'selectCount2')" style="width: 90px;" />
          </div>
        </div>

        <div class="top_thr" style="display: flex; align-items: center;">
          <div style="border: 1px solid #ccc; border-radius: 5px; display: flex; margin-right: 10px; width: 350px;">
            <el-select v-model="ListInfo.selectRateColName" slot="prepend" placeholder="销售金额环比增幅" style="width: 134px;">
              <el-option label="销售金额环比增幅" value="saleAmountQOQ"></el-option>
              <el-option label="销售订单数环比增幅" value="saleOrderCountQOQ"></el-option>
              <el-option label="销售件数环比增幅" value="saleGoodCountQOQ"></el-option>
            </el-select>
            <el-input v-model="ListInfo.selectRate1" placeholder="最小值" maxlength="8" clearable class="pass_input"
              oninput="if(value.replace('-', '').length > 8) value = value.slice(0, 8)" type="number"
              @input="validateInput($event, 'selectRate1')" style="width: 90px; margin-right: 5px;" />
            <span style="margin: 0 0px;">-</span>
            <el-input v-model="ListInfo.selectRate2" placeholder="最大值" maxlength="8" clearable class="pass_input"
              oninput="if(value.replace('-', '').length > 8) value = value.slice(0, 8)" type="number"
              @input="validateInput($event, 'selectRate2')" style="width: 90px;" />
          </div>
          <div style="display: flex;align-items: center;">
            <span>订单支付时间:</span>
            <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" :clearable="false"
              style="width: 250px;margin-right: 10px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
            </el-date-picker>
            <el-button type="primary" @click="getList('search')" style="width: 70px;">搜索</el-button>
            <el-button type="primary" @click="reset">重置</el-button>
            <el-button type="primary" @click="startImport">导入</el-button>
            <el-button type="primary" @click="exportProps(1)">导出</el-button>
            <el-button type="primary" @click="exportProps(2)">导出子表</el-button>
          </div>
        </div>
      </div>
    </template>
    <vxetablebase :id="'distributionSalesCodingAnalysis202408041642_1'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" @cellClick="cellClick"
      style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
      <template slot="left">
        <vxe-column title="SKU信息" width="180" fixed="left" align="center">
          <template #default="{ row, $index }">
            <div class="beforeBox">
              <span class="beforeBox_item1">{{ row.goodsName }}</span>
              <span class="beforeBox_item2">{{ row.goodsCode }}</span>
            </div>
          </template>
        </vxe-column>
      </template>
      <template slot="right">
        <vxe-column title="操作" width="120" fixed="right" align="center">
          <template #default="{ row, $index }">
            <div style="display: flex; justify-content: center;">
              <el-button type="text" @click="handleViewDetails(row)">查看详情</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <vxe-modal v-model="detailedInformation" title="详情明细" :width="1500" :mask-closable="true" marginSize='-500'>
      <template #default>
      <div style="margin-top: 10px;height: 500px;">
        <vxetablebase :id="'distributionSalesCodingAnalysis202408041642_2'" ref="dialogtable" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
          @sortchange='detailssortchange' :tableData='detailstableData' :tableCols='detailstableCols'
          :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="detailsloading"
          height='100%'>
        </vxetablebase>
      </div>
      <div>
        <my-pagination ref="pager" :total="detailstotal" @page-change="detailsPagechange"
          @size-change="detailsSizechange" />
      </div>
      </template>
    </vxe-modal>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="45%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 100px;">
        <el-date-picker style="width: 200px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay" type="date"
          placeholder="选择日期" :clearable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
        </el-date-picker>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action=""
          accept=".xlsx" :file-list="fileList" :http-request="onUploadFile" :on-success="onUploadSuccess"
          :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary" style="width: 90px;">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px;width: 90px;" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="趋势图" :visible.sync="buscharDialog.visible" width="70%" v-dialogDrag>
      <span>
        <buschar v-if="buscharDialog.visible" ref="buschar" :analysisData="buscharDialog.data">
        </buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import dayjs from 'dayjs'
import { getSkuSaleConvertAnalysePageList, importFileSkuSaleConvertAnalyse, exportSkuSaleConvertAnalysePageList } from '@/api/operatemanage/skuSaleConvertAnalyse'
import inputYunhan from "@/components/Comm/inputYunhan";
import buschar from '@/components/Bus/buschar'

const tableCols = [
  { sortable: 'custom', width: '110', align: 'center', prop: 'saleAmountArray', label: '销售金额(环比)', tipmesg: '统计周期内支付成功的所有订单金额', type: 'levelbefore', icon: true, },
  { prop: 'echart', istrue: true, type: 'echarts', chartProp: 'chartData', fix: true, label: '销售金额趋势图', width: '100' },
  { sortable: 'custom', width: '110', align: 'center', prop: 'saleAmountAdd', label: '销售金额增量', tipmesg: '对比上一个统计周期销售金额的增量', },
  { sortable: 'custom', width: '120', align: 'center', prop: 'saleOrderCountArray', label: '销售订单数(环比)', tipmesg: '统计周期内支付成功的所有订单数', type: 'levelbefore', icon: true, },
  { sortable: 'custom', width: '110', align: 'center', prop: 'saleOrderCountAdd', label: '销售订单数增量', tipmesg: '对比上一个统计周期销售订单的增量', },
  { sortable: 'custom', width: '120', align: 'center', prop: 'saleGoodCountArray', label: '销售件数(环比)', tipmesg: '统计周期内支付成功的所有商品件数', type: 'levelbefore', icon: true, },
  { sortable: 'custom', width: '110', align: 'center', prop: 'saleGoodCountAdd', label: '销售件数增量', tipmesg: '对比上一个统计周期销售件数的增量', },
  { width: '110', align: 'center', prop: 'sellStock', label: '可售库存数', tipmesg: '配置的实际库存数-未发货件数', },
  { width: '110', align: 'center', prop: 'sellStockDay', label: '可售库存天数', tipmesg: '根据配置公式得出，（实际库存数-未发货件数）/近N天日均销量，计算结果保留1位小数', },
  { width: '145', align: 'center', prop: 'lables', label: '标签', type: 'tagline', showoverflow: 'ellipsis' },
  { width: '110', align: 'center', prop: 'brandName', label: '商品品牌', },
  { width: '110', align: 'center', prop: 'goodClassification', label: '商品分类', },
  { width: '110', align: 'center', prop: 'virtualClassification', label: '虚拟分类', },
  { sortable: 'custom', width: '120', align: 'center', prop: 'saleAfterOrderCountArray', label: '售后订单数(环比)', tipmesg: '统计周期内支付成功的所有订单中，截至当前售后单状态为已确认的订单数', type: 'levelbefore', icon: true, },
  { sortable: 'custom', width: '110', align: 'center', prop: 'payOrderSaleAfterRateArray', label: '支付订单售后率(变化)', tipmesg: '统计周期内支付成功的所有订单中，截至当前售后单状态为已确认的订单数占比', type: 'levelbefore', icon: true, },
  { sortable: 'custom', width: '120', align: 'center', prop: 'payOrderBackAmountArray', label: '支付订单退款金额(环比)', tipmesg: '统计周期内支付成功的所有订单中，售后类型为仅退款或退货退款，售后单状态为已确认的商品应退金额', type: 'levelbefore', icon: true, },
  { sortable: 'custom', width: '120', align: 'center', prop: 'allBackAmountArray', label: '全部退款金额(环比)', tipmesg: '统计周期内确认售后的所有售后单中，售后类型为仅退款或退货退款的商品退款金额', type: 'levelbefore', icon: true, },
  { sortable: 'custom', width: '120', align: 'center', prop: 'payOrderBackCountArray', label: '支付订单退款件数(环比)', tipmesg: '统计周期内支付成功的所有订单中，售后类型为仅退款或退货退款，售后单状态为已确认的商品退款件数', type: 'levelbefore', icon: true, },
  { sortable: 'custom', width: '120', align: 'center', prop: 'allBackCountArray', label: '全部退款件数(环比)', tipmesg: '统计周期内确认售后的所有售后单中，售后类型为仅退款或退货退款的商品退款件数', type: 'levelbefore', icon: true, },
]

const detailstableCols = [
  { fixed: 'left', sortable: 'custom', width: '100', align: 'center', prop: 'distributor', label: '店铺分销商', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'saleAmountArray', label: '销售金额(环比)', tipmesg: '统计周期内支付成功的所有订单金额', type: 'levelbefore', icon: true, },
  { sortable: 'custom', width: '110', align: 'center', prop: 'saleAmountAdd', label: '销售金额增量', tipmesg: '对比上一个统计周期销售金额的增量', },
  { sortable: 'custom', width: '120', align: 'center', prop: 'saleOrderCountArray', label: '销售订单数(环比)', tipmesg: '统计周期内支付成功的所有订单数', type: 'levelbefore', icon: true, },
  { sortable: 'custom', width: '110', align: 'center', prop: 'saleOrderCountAdd', label: '销售订单数增量', tipmesg: '对比上一个统计周期销售订单的增量', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'saleGoodCountArray', label: '销售件数(环比)', tipmesg: '统计周期内支付成功的所有商品件数', type: 'levelbefore', icon: true, },
  { sortable: 'custom', width: '110', align: 'center', prop: 'saleGoodCountAdd', label: '销售件数增量', tipmesg: '对比上一个统计周期销售件数的增量', },
  { sortable: 'custom', width: '120', align: 'center', prop: 'saleAfterOrderCountArray', label: '售后订单数(环比)', tipmesg: '统计周期内支付成功的所有订单中，截至当前售后单状态为已确认的订单数', type: 'levelbefore', icon: true, },
  { sortable: 'custom', width: '130', align: 'center', prop: 'payOrderSaleAfterRateArray', label: '支付订单售后率(变化)', tipmesg: '统计周期内支付成功的所有订单中，截至当前售后单状态为已确认的订单数占比', type: 'levelbefore', icon: true, },
  { sortable: 'custom', width: '130', align: 'center', prop: 'payOrderBackAmountArray', label: '支付订单退款金额(环比)', tipmesg: '统计周期内支付成功的所有订单中，售后类型为仅退款或退货退款，售后单状态为已确认的商品应退金额', type: 'levelbefore', icon: true, },
  { sortable: 'custom', width: '130', align: 'center', prop: 'allBackAmountArray', label: '全部退款金额(环比)', tipmesg: '统计周期内确认售后的所有售后单中，售后类型为仅退款或退货退款的商品退款金额', type: 'levelbefore', icon: true, },
  { sortable: 'custom', width: '120', align: 'center', prop: 'payOrderBackCountArray', label: '支付订单退款件数(环比)', tipmesg: '统计周期内支付成功的所有订单中，售后类型为仅退款或退货退款，售后单状态为已确认的商品退款件数', type: 'levelbefore', icon: true, },
  { sortable: 'custom', width: '120', align: 'center', prop: 'allBackCountArray', label: '全部退款件数(环比)', tipmesg: '统计周期内确认售后的所有售后单中，售后类型为仅退款或退货退款的商品退款件数', type: 'levelbefore', icon: true, },
]
export default {
  name: "distributionSalesCodingAnalysis",
  components: {
    MyContainer, vxetablebase, inputYunhan, buschar
  },
  data() {
    return {
      buscharDialog: { visible: false, title: "", data: {} },
      dialogDrVisible: false,
      yearMonthDay: null,
      fileList: [],
      fileparm: {},
      uploadLoading: false,
      pageLoading: false,
      dialogVisible: false,
      lables: null,
      sublistList: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
      },
      detailstableData: [],
      detailsloading: false,
      detailstotal: 0,
      detailstableCols: detailstableCols,
      goodCode: null,
      detailedInformation: false,
      allotResultStatus: [],
      select: null,
      select1: null,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        goodsCode: null,//编码
        distributor: null,//分销商
        lables: null,//标签
        goodClassification: null,//商品分类
        brandName: null,//商品品牌
        selectAmountColName: 'saleAmount',
        selectAmount1: null,
        selectAmount2: null,
        selectCountColName: 'saleOrderCount',
        selectCount1: null,
        selectCount2: null,
        selectRateColName: 'saleAmountQOQ',
        selectRate1: null,
        selectRate2: null,
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions: {
        shortcuts: [{
          text: '昨天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
            window.setshowprogress(false);
          }
        }, {
          text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
            window.setshowprogress(false);
          }
        }, {
          text: '近一个月',
          onClick(picker) {
            const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate() - 1);
            const date2 = new Date(); date2.setDate(date2.getDate() - 1);
            picker.$emit('pick', [date1, date2]);
            window.setshowprogress(false);
          }
        }, {
          text: '近三个月',
          onClick(picker) {
            const date1 = new Date(); date1.setMonth(date1.getMonth() - 3); date1.setDate(date1.getDate() - 1);
            const date2 = new Date(); date2.setDate(date2.getDate() - 1);
            picker.$emit('pick', [date1, date2]);
            window.setshowprogress(true);
          }
        }, {
          text: '近六个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setMonth(start.getMonth() - 6);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },
  async mounted() {
    //默认给近7天时间
    this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
    this.ListInfo.endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
    this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
    await this.getList()
    this.$refs.table.changecolumn(['saleAfterOrderCountArray', 'payOrderSaleAfterRateArray', 'payOrderBackAmountArray', 'allBackAmountArray', 'payOrderBackCountArray', 'allBackCountArray']);//显示隐藏列
  },
  methods: {
    validateInput(event, prop) {
      let value = event
      const decimal = value.split('.')[1];
      if (decimal && decimal.length > 4) {
        this.ListInfo[prop] = value.slice(0, value.length - 1);
      }
      if (prop == 'selectCount1') {
        this.ListInfo.selectCount1 = this.ListInfo.selectCount1.replace(/[^0-9]/g, '');
      } else if (prop == 'selectCount2') {
        this.ListInfo.selectCount2 = this.ListInfo.selectCount2.replace(/[^0-9]/g, '');
      }
    },
    cellClick({ row, column }) {
      if (column?.field === 'echart') {
        const { chartData } = row;
        const series = chartData.series[0];
        Object.assign(series, {
          type: "line",
          yAxisIndex: 0,
          backColor: null,
          stack: null,
          itemStyle: {
            normal: {
              label: {
                show: true,
                position: "top",
                textStyle: {
                  fontSize: 14
                }
              }
            }
          },
          emphasis: {
            focus: "series"
          },
          smooth: false
        });
        delete series.lineStyle;
        chartData.yAxis = [{ position: "left", unit: "", name: "", offset: 0 }];
        this.buscharDialog = {
          visible: true,
          data: chartData
        };
        this.$nextTick(() => {
          this.$refs.buschar.initcharts();
        });
      }
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("fsDate", this.yearMonthDay);
      var res = await importFileSkuSaleConvertAnalyse(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (!this.yearMonthDay) {
        this.$message({ message: "请选择日期", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    //SKU编码数据
    productCodeCallback(val) {
      this.ListInfo.goodsCode = val;
    },
    //重置
    reset() {
      this.ListInfo.goodsCode = "";
      this.$refs.productCode.textarea = '';
      this.$refs.productCode.input = '';
      Object.keys(this.ListInfo).forEach(key => {
        if (key !== 'currentPage' && key !== 'pageSize' && key !== 'orderBy' && key !== 'isAsc' && key !== 'startTime' && key !== 'endTime' && key !== 'selectAmountColName' && key !== 'selectCountColName' && key !== 'selectRateColName') {
          this.ListInfo[key] = null;
        }
      });
    },
    //子表排序
    detailssortchange({ order, prop }) {
      let sort = ''
      if (prop && prop.includes('Array')) {
        sort = prop.replace('Array', '');
      } else {
        sort = prop;
      }
      if (sort) {
        this.sublistList.orderBy = sort
        this.sublistList.isAsc = order.indexOf("descending") == -1 ? true : false
        this.handleViewDetails()
      }
    },
    //子表每页数量改变
    detailsSizechange(val) {
      this.sublistList.currentPage = 1;
      this.sublistList.pageSize = val;
      this.handleViewDetails()
    },
    //子表当前页改变
    detailsPagechange(val) {
      this.sublistList.currentPage = val;
      this.handleViewDetails()
    },
    //查看详情-子表弹窗
    async handleViewDetails(row) {
      if (row) {
        this.goodCode = row.goodsCode
      }
      const params = { ...this.sublistList, goodsCode: this.goodCode, distributor: this.ListInfo.distributor, lables: this.ListInfo.lables, goodClassification: this.ListInfo.goodClassification, brandName: this.ListInfo.brandName, dateType: 2, endTime: this.ListInfo.endTime, startTime: this.ListInfo.startTime }
      this.detailsloading = true
      const { data, success } = await getSkuSaleConvertAnalysePageList(params)
      if (success) {
        this.detailstableData = data.list
        this.detailstotal = data.total
        this.detailsloading = false
        this.detailedInformation = true
      }
    },
    //日期
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
      await this.getList()
    },
    //导出数据
    async exportProps(val) {
      this.pageLoading = true
      const params = { dateType: val, ...this.ListInfo }
      const { data } = await exportSkuSaleConvertAnalysePageList(params)
      this.pageLoading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', val == 1 ? '分销SKU商品销售转换分析导出' : '子表分销SKU商品销售转换分析导出' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    //获取数据
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
      }
      if (!this.timeRanges) {
        this.$message.error('请选择日期');
        return
      }
      const replaceArr = ['distributor', 'goodClassification', 'lables', 'brandName', 'selectAmountColName'] //替换空格的方法,该数组对应str类型的input双向绑定的值
      this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
      this.loading = true
      const { data, success } = await getSkuSaleConvertAnalysePageList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.loading = false
        this.$refs.table.loadRowEcharts();//加载echarts
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    //排序
    sortchange({ order, prop }) {
      let sort = ''
      if (prop && prop.includes('Array')) {
        sort = prop.replace('Array', '');
      } else {
        sort = prop;
      }
      if (sort) {
        this.ListInfo.orderBy = sort
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}

.top_one,
.top_thr {
  width: 100%;
  margin-bottom: 10px;
}

::v-deep .pass_input input::-webkit-outer-spin-button,
::v-deep .pass_input input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

::v-deep .pass_input input[type="number"] {
  -moz-appearance: textfield !important;
}

//去掉input的边框
.pass_input {
  float: left;
  width: 215px;
  margin-left: 10px;

  // el-input__inner是el-input的input类名
  ::v-deep .el-input__inner {
    border: none;
  }
}

.beforeBox {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 40px;

  .beforeBox_item1 {
    position: absolute;
    bottom: 0;
    left: 0;
    color: #86afff;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    text-align: left;
  }

  .beforeBox_item2 {
    position: absolute;
    top: 0;
    left: 0;
  }
}
</style>
