<template>
  <container v-loading="pageLoading">
    <el-card style="padding:1px;">
         <div v-for="(item,index) in oderDetailView.procodeViews" :key="index">
             <el-descriptions :column="6" size="mini">
                  <el-descriptions-item label="平台">{{formatPlatform(item.platform)}}</el-descriptions-item>
                  <el-descriptions-item label="产品ID" :span="2">
                      <el-link type="primary" :href="`${((item.platform==1||item.platform==9||item.platform==8)?' https://detail.tmall.com/item.htm?id':item.platform==2?'https://mobile.yangkeduo.com/goods2.html?goods_id':'')}=${item.proCode}`" target="_blank">{{item.proCode}}</el-link>
                    </el-descriptions-item>
                  <el-descriptions-item label="压单天数">{{item.waitDays}}</el-descriptions-item>
                  <el-descriptions-item label="数量">{{item.number}}</el-descriptions-item>
                  <el-descriptions-item label="运营小组">{{item.grouper}}</el-descriptions-item>
            </el-descriptions>
         </div>
        <el-button type="success" size="mini" @click="doCopy(oderDetailView.orderNoInners)">一键复制所有订单号</el-button>
       </el-card>
      <el-table :data="oderDetailView.details">
        <el-table-column width="50" type="index" label="#" align="center"></el-table-column>
        <el-table-column width="150" property="payTime" label="支付日期"></el-table-column>
        <el-table-column width="100" property="orderNoInner" label="内部订单号"></el-table-column>
        <el-table-column width="110" property="goodsCode" label="商品编码"></el-table-column>
        <el-table-column width="70" property="qty" label="压品数"></el-table-column>
        <el-table-column width="70" property="platform" label="平台">
          <template slot-scope="scope" >
             <span>{{formatPlatform(scope.row['platform'])}}</span>
          </template>
        </el-table-column>
        <el-table-column width="190" property="shopName" label="店铺"></el-table-column>
        <el-table-column width="150" property="proCode" label="商品ID"></el-table-column>
      </el-table>
      <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
  </container>
</template>
<script>
import {pageAbnormalInventory,importAbnormal,editAbnormalInventory,queryAbnormalInventoryDetail,getLastUpdateTime,getAllAbnormalCheckErrorOderNo,getAllAbnormalCheckErrorGoodsCode,getAllAbnormalReasonRate,
        queryAbnormalInventory,queryAbnormalInventoryRecord,queryAbnormalOderDetail,exportAbnormalInventory, GetLastUpdateTimeAbnormalInventory} from '@/api/inventory/abnormal'
import {formatTime,formatYesornoBool,formatPlatform,formatNoLink,formatIsCheckError} from "@/utils/tools";
import container from "@/components/my-container";
import { Alert } from 'element-ui';
export default {
  name: "orderabnormalorderdetail",
  components: { container},
  data() {
    return {
      that:this,
      formatPlatform:formatPlatform,
      formatTime:formatTime,
      formatIsCheckError:formatIsCheckError,
      parentid:0,
      list: [],
      orderNoInners:[],
      recodelist: [],
      detaillist:[],
      importtimelist: [],
      oderDetailView:[],
      visiblepopover: false,
      prevTarget: null, // 编辑 Popover 的 Reference （参照），用于 popover.js 对齐两个元素
      popperFlag: false, // 用于编辑 Popover 的刷新
      visiblepopoverdetail: false,
      dialogOrderDetailVisible:false,
      popperFlagdetail: false,
      importtimedialogVisible: false,
      pager:{OrderBy:"",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [],
      fileList:[],
      listLoading: false,
      dialogVisible: false,
      pageLoading: false,
    };
  },
 methods: {async onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
   async getdetaillist(parentid){
       this.detaillist=[];
       const res = await queryAbnormalInventoryDetail({parentid:parentid})
       if (!(res.code==1&&res.data)) return
       this.detaillist=res.data;
    },reloadParentId(parentid){
      this.parentid=parentid
    },
    async getlist(){
      this.pageLoading=true;
       this.oderDetailView={};
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter,parentId:this.parentid}
       const res = await queryAbnormalOderDetail(params)
      this.total = res.data.total
       this.oderDetailView=res.data;
       this.orderNoInners=res.data.orderNoInners
      this.pageLoading=false;
    },
   doCopy: function () {
      let that=this;
      this.$copyText(this.orderNoInners).then(function (e) {
          that.$message({ message: "内容已复制到剪切板！", type: "success" });
      }, function (e) {
          that.$message({ message: "抱歉，复制失败！", type: "warning" });
      })
    }
  }
};
</script>
<style lang="scss" scoped>
.imgDolg {
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: 9999;
  background-color: rgba(140, 134, 134, 0.6);
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  #imgDolgClose {
    position: fixed;
    top: 35px;
    cursor: pointer;
    right: 7%;
    font-size: 50px;
    color: white;
  }
  img{
    width: 80%;
  }
}
</style>

