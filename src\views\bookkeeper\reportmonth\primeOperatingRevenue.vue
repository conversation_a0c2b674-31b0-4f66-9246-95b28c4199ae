<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <span style="display: flex; align-items: center;"></span>
                <!-- <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 5px;" :clearable="false" :value-format="'yyyy-MM-dd'"
                    @change="changeTime">
                </el-date-picker> -->


                <el-date-picker v-model="timeRanges" type="month" placeholder="选择月">
                </el-date-picker>

                <el-date-picker v-model="timeActualSettle" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="结算开始日期" end-placeholder="结算结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 5px;" :clearable="false" :value-format="'yyyy-MM-dd'"
                    @change="changeTime" clearable>
                </el-date-picker>


                <el-input v-model.trim="ListInfo.skc" placeholder="SKC" maxlength="50" clearable class="publicCss" />
                <el-input v-model.trim="ListInfo.merchantSKU" placeholder="商家SKU" maxlength="50" clearable
                    class="publicCss" />


                <el-select v-model="ListInfo.financialType" placeholder="财务类型" clearable filterable class="publicCss">
                    <el-option value="交易收入" label="交易收入" />
                    <el-option value="退款" label="退款" />
                    <el-option value="库存盘亏" label="库存盘亏" />
                    <el-option value="库存盘盈" label="库存盘盈" />
                </el-select>

                <el-select v-model="ListInfo.documentType" placeholder="账单类型" filterable clearable class="publicCss">
                    <el-option v-for="option in documentOptions" :key="option.value" :label="option.label"
                        :value="option.value" />
                </el-select>


                <el-button style="padding: 0;border: none;float: left;">
                    <inputYunhan :key="'1'" :keys="'one'" :width="'220px'" ref="" :inputt.sync="ListInfo.shopName"
                        v-model.trim="ListInfo.shopName" placeholder="店铺/若输入多条请按回车" :clearable="true"
                        @callback="callbackShopName" title="店铺" @entersearch="entersearch" :maxRows="100">
                    </inputYunhan>
                </el-button>

                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button
                    @click="startImport" type="primary" icon="el-icon-share" @command="handleCommand"> 导入
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item class="Batcoperation" style="padding: 0 25px"
                            command="a">下载模版</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
                <el-button type="primary" @click="exportProps">导出</el-button>
            </div>
        </template>
        <vxetablebase :id="'transactionIncome202409090425'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
            style="width: 100%;margin: 0" v-loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
            <div style="height: 75px;">


                <!-- <el-date-picker style="width: 200px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay"
                    type="date" placeholder="选择日期" :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
                </el-date-picker> -->

                <el-date-picker v-model="yearMonthDay" type="month" placeholder="选择月" format="yyyyMM"
                    value-format="yyyyMM">
                </el-date-picker>

                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                    :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                </el-upload>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getList } from '@/api/operatemanage/base/shop'
import dayjs from 'dayjs'
import { formatTime } from "@/utils";
// import { ElMessage } from 'element-plus';
import { importSettleDetail_SheInAsync, pageSettleDetail_SheInResult, exportSettleDetail_SheInResult } from '@/api/bookkeeper/crossBorderV2'
import inputYunhan from "@/components/Comm/inputYunhan";

const tableCols = [
    { istrue: true, prop: 'shopCode', label: '店铺ID', width: 'auto', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'shopName', label: '店铺', width: 'auto', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'yearMonthDay', label: '日期', width: 'auto', align: 'center', sortable: 'custom', },
    {
        istrue: true, prop: 'actualSettleTime', label: '实际结算日期', width: 'auto', align: 'center', sortable: 'custom',
        formatter: (row) => {
            return row.actualSettleTime ? formatTime(row.actualSettleTime, "YYYY-MM-DD") : "";
        },
    },
    { istrue: true, prop: 'billNumber', label: '报账单号', width: 'auto', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'skc', label: 'SKC', width: 'auto', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'platformSKU', label: '平台SKU', width: 'auto', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'merchantSKU', label: '商家SKU', width: 'auto', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'properties', label: '属性集', width: 'auto', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'count', label: '商品数量', width: 'auto', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'documentType', label: '账单类型', width: 'auto', align: 'center', },
    { istrue: true, prop: 'balanceType', label: '收入类型', width: 'auto', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'documentNumber', label: '业务单号', width: 'auto', align: 'center', },
    { istrue: true, prop: 'money', label: '金额', width: 'auto', align: 'center', sortable: 'custom', },
]
export default {
    name: "transactionIncome",
    components: {
        MyContainer, vxetablebase, inputYunhan
    },
    data() {
        return {
            summaryarry: {},
            yearMonthDay: null,//导入日期
            fileparm: {},//上传文件参数
            dialogVisible: false,//导入弹窗
            uploadLoading: false,//上传loading
            fileList: [],//上传文件列表
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                dataStartDate: null,//开始时间
                dataEndDate: null,//结束时间
                inventoryNumber: null,//备货单号
                proCode: null,//产品ID
                shopName: null,//店铺
                billingType: null,//账单类型
                documentType: '',
                financialType: '',
                startASettleTime: null,
                endASettleTime: null,
            },
            timeRanges: null,
            timeActualSettle: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            billingType: null,//账单类型
            selectedValue: [], // 存储选择的完整路径
            documentOptions: [],
        }
    },
    async mounted() {
        await this.getList()
    },
    watch: {
        'ListInfo.financialType'(newVal) {
            this.updateDocumentOptions(newVal);
        }
    },
    methods: {
        async changeTime(e) {
            this.ListInfo.startASettleTime = e ? e[0] : null
            this.ListInfo.endASettleTime = e ? e[1] : null
        },
        updateDocumentOptions(financialType) {
            switch (financialType) {
                case '交易收入':
                    this.documentOptions = [
                        { value: '线下销售', label: '线下销售' },
                        { value: '客单发货', label: '客单发货' },
                        { value: '交易收入', label: '交易收入' },
                        { value: '平台客单发货', label: '平台客单发货' },
                    ];
                    break;
                case '退款':
                    this.documentOptions = [
                        { value: '客单退货', label: '客单退货' },
                        { value: '平台客单退货', label: '平台客单退货' },
                    ];
                    break;
                case '库存盘亏':
                    this.documentOptions = [
                        { value: '库存盘亏', label: '库存盘亏' },
                        { value: '盘亏', label: '盘亏' },
                        { value: '退货丢货补款', label: '退货丢货补款' }
                    ];
                    break;
                case '库存盘盈':
                    this.documentOptions = [
                        { value: '库存盘盈', label: '库存盘盈' }
                    ];

                    break;
                default:
                    this.documentOptions = [];
            }
        },
        //上传文件
        onUploadRemove(file, fileList) {
            this.fileList = []
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            this.dialogVisible = false;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("yearMonthDay", this.yearMonthDay);
            var res = await importSettleDetail_SheInAsync(form);
            if (res?.success)
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
            this.uploadLoading = false
            this.dialogVisible = false;
            await this.getList()
        },
        onSubmitUpload() {
            if (!this.yearMonthDay) {
                this.$message({ message: "请选择日期", type: "warning" });
                return false;
            }
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        //导入弹窗
        startImport() {
            this.fileList = []
            this.dialogVisible = true;
        },
        formatMonth(date) {
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份从 0 开始，需要加 1
            return `${year}-${month}-01`;
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            if (this.timeRanges != null) {
                //默认给近7天时间
                this.ListInfo.dataStartDate = this.formatMonth(this.timeRanges)
                this.ListInfo.dataEndDate = this.formatMonth(this.timeRanges)
            } else {
                this.ListInfo.dataStartDate = null;
                this.ListInfo.dataEndDate = null;
            }
            if (this.ListInfo.timeActualSettle) {
                his.ListInfo.startASettleTime = this.Filter.timeActualSettle[0];
                his.ListInfo.endASettleTime = this.Filter.timeActualSettle[1];
            }

            this.loading = true
            const { data, success } = await pageSettleDetail_SheInResult(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.summaryarry = data.summary
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },

        async exportProps() {
            if (this.timeRanges != null) {
                //默认给近7天时间
                this.ListInfo.dataStartDate = this.formatMonth(this.timeRanges)
                this.ListInfo.dataEndDate = this.formatMonth(this.timeRanges)
            } else {
                this.ListInfo.dataStartDate = null;
                this.ListInfo.dataEndDate = null;
            }
            const res = await exportSettleDetail_SheInResult(this.ListInfo);
            const aLink = document.createElement("a");

            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            var excelName = '希音全托账单费用结算'
            aLink.setAttribute(
                "download",
                excelName + new Date().toLocaleString() + ".xlsx"
            );
            aLink.click();
        },
        async handleCommand(command) {
            switch (command) {
                //下载模版
                case 'a':
                    await this.downLoadFile()
                    break;
            }
        },
        async downLoadFile() {
            window.open("/static/excel/CrossBorderDownloadTemplate/希音-全托交易收入导入模版.xlsx", "_blank");
        },
        //多条查询部分
        async entersearch(val) {
            this.getList();
        },
        async callbackShopName(val) {
            this.ListInfo.shopName = val;
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 150px;
        margin-right: 5px;
    }
}
</style>