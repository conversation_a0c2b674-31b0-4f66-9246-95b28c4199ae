<template>
    <div>
        <!-- 系列编码 -->
        <vxetablebase :id="'vendorGroup202408041617_1'" ref="xlTable" :tableData="xltableData" :tableCols="xlTableCol" :is-index="true" :that="that"
            :showsummary="true" style="width: 100%; height: 620px; margin: 0" @sortchange='sortchange' :tablefixed='true'
            v-if="checkList.includes('系列编码')" :loading="listLoading" class="detail">
        </vxetablebase>
        <!-- 商品编码 -->
        <vxetablebase :id="'vendorGroup202408041617_2'" ref="goodTable" :tableData="goodTableData" :tableCols="goodTableCol" :is-index="true" :that="that"
            :showsummary="true" style="width: 100%; height: 620px; margin: 0" @sortchange='sortchange' :tablefixed='true'
            v-if="checkList.includes('商品编码')" :loading="listLoading" class="detail">
        </vxetablebase>
        <!-- 供应商 -->
        <vxetablebase :id="'vendorGroup202408041617_3'" ref="gysTable" :tableData="gysTableData" :tableCols="gysTableCol" :is-index="true" :that="that"
            :showsummary="true" style="width: 100%; height: 620px; margin: 0" @sortchange='sortchange' :tablefixed='true'
            v-if="checkList.includes('供应商')" :loading="listLoading" class="detail">
        </vxetablebase>
        <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="detailTotal"
            @page-change="detailPagechange" @size-change="detailSizechange" />

        <!-- 最后报价时间 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible.lastTimeDialog" v-dialogDrag
            :before-close="handleColse">
            <vxetablebase :id="'vendorGroup202408041617_4'" ref="table" :that='that' :isIndex='true' @sortchange='lastSortchange'
                :tableData='dialogTableData.lastTimeData' :tableCols='lastTableCol' :isSelection="false"
                :isSelectColumn="false" style="width: 100%; height: 300px; margin: 0" class="detail">
            </vxetablebase>
            <my-pagination ref="pager" :total="dialogTotal.lastTimeTotal" @page-change="dialogPageChange"
                @size-change="dialogSizeChange" />
        </el-dialog>

        <!-- 累计报价次数 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible.cumulativeQuotesDialog" v-dialogDrag
            :before-close="handleColse">
            <vxetablebase :id="'vendorGroup202408041617_5'" ref="table" :that='that' :isIndex='true' @sortchange='lastSortchange'
                :tableData='dialogTableData.cumulativeQuotesData' :tableCols='quotesTableCol' :isSelection="false"
                :isSelectColumn="false" style="width: 100%; height: 300px; margin: 0" class="detail">
            </vxetablebase>
            <my-pagination ref="pager" :total="dialogTotal.cumulativeQuotesTotal" @page-change="dialogPageChange"
                @size-change="dialogSizeChange" />
        </el-dialog>

        <!-- 进货量 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible.quotationDialog" v-dialogDrag
            :before-close="handleColse">
            <vxetablebase :id="'vendorGroup202408041617_6'" ref="table" :that='that' :isIndex='true' @sortchange='lastSortchange'
                :tableData='dialogTableData.quotationData' :tableCols='quotationTableCol' :isSelection="false"
                :isSelectColumn="false" style="width: 100%; height: 300px; margin: 0" class="detail">
            </vxetablebase>
            <my-pagination ref="pager" :total="dialogTotal.quotationTotal" @page-change="dialogPageChange"
                @size-change="dialogSizeChange" />
        </el-dialog>

        <!-- 对接状态 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible.dockingStatusDialog" v-dialogDrag
            :before-close="handleColse">
            <vxetablebase :id="'vendorGroup202408041617_7'" ref="table" :that='that' :isIndex='true' @sortchange='lastSortchange'
                :tableData='dialogTableData.dockingStatusData' :tableCols='dockingStatus' :isSelection="false"
                :isSelectColumn="false" style="width: 100%; height: 300px; margin: 0" class="detail">
            </vxetablebase>
            <my-pagination ref="pager" :total="dialogTotal.dockingStatusTotal" @page-change="dialogPageChange"
                @size-change="dialogSizeChange" />
        </el-dialog>

        <!-- 点击供应商名字 -->
        <el-dialog title="采购记录" :visible.sync="nameVisible" width="40%" :before-close="handleClose" v-dialogDrag>
            <vxetablebase :id="'vendorGroup202408041617_8'" ref="detailTable" :tableData="nameTableData" :tableCols="tableCols5" :is-index="true" :that="that"
                :showsummary="true" :summaryarry="nameSummary" style="width: 100%; height: 500px" @sortchange='sortchange3' class="detail">
            </vxetablebase>
            <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="nameTotal"
                @page-change="namePagechange" @size-change="nameSizechange" style="margin-top: 40px;" />
        </el-dialog>
    </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    getProviderQuotationRecordOverViewGroupPageList,
    getProviderQuotationOverViewGroupDialogStylePageList,
    getProviderQuotationOverViewGroupDialogGoodPageList,
    getProviderQuotationOverViewGroupDialogProviderPageList
}
    from '@/api/openPlatform/ProviderQuotation'
import { pagePurchaseOrderByProviderNameAsync } from '@/api/inventory/purchase'
//系列编码
const xlTableCol = [
    { istrue: true, prop: 'stylePic', label: '商品图片', type: 'images' },
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom' },
    { istrue: true, prop: 'brandName', label: '采购', sortable: 'custom' },
    { istrue: true, prop: 'modifiedTime', label: '最后填写日期', sortable: 'custom', type: 'click', handle: (that, row) => that.openDialog(row, '最后填写日期', 'style', 'modifiedTime') },
    { istrue: true, prop: 'quotationCount', label: '累计报价次数', sortable: 'custom', type: 'click', handle: (that, row) => that.openDialog(row, '累计报价次数', 'style', 'quotationCount') },
    { istrue: true, prop: 'providerCount', label: '累计报价供应商', sortable: 'custom', type: 'click', handle: (that, row) => that.openDialog(row, '最后填写日期', 'style', 'providerCount') },
]

//商品编码
const goodTableCol = [
    { istrue: true, prop: 'goodPic', label: '商品图片', type: 'images' },
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom' },
    { istrue: true, prop: 'goodCode', label: '商品编码', sortable: 'custom' },
    { istrue: true, prop: 'goodName', label: '商品名称', sortable: 'custom' },
    { istrue: true, prop: 'brandName', label: '采购', sortable: 'custom' },
    { istrue: true, prop: 'costPrice', label: '成本价', sortable: 'custom' },
    { istrue: true, prop: 'quotation1', label: '进货量100报价', sortable: 'custom', type: 'click', handle: (that, row) => that.openDialog(row, '最后填写日期', 'good', 'quotation1') },
    { istrue: true, prop: 'quotation2', label: '进货量10000报价', sortable: 'custom', type: 'click', handle: (that, row) => that.openDialog(row, '最后填写日期', 'good', 'quotation2') },
    { istrue: true, prop: 'quotation3', label: '进货量100000报价', sortable: 'custom', type: 'click', handle: (that, row) => that.openDialog(row, '最后填写日期', 'good', 'quotation3') },
    { istrue: true, prop: 'quotationCount', label: '累计报价次数', sortable: 'custom', type: 'click', handle: (that, row) => that.openDialog(row, '累计报价次数', 'good', 'quotationCount') },
]

//供应商
const gysTableCol = [
    { istrue: true, prop: 'providerName', label: '供应商名称', sortable: 'custom', type: 'treeStar1', style: "color: rgb(72, 132, 243);cursor:pointer;", handle: (that, row) => that.openNameDialog(row) },
    { istrue: true, prop: 'sourceName', label: '推荐人', sortable: 'custom', },
    { istrue: true, prop: 'sourceType', label: '来源', sortable: 'custom', },
    { istrue: true, prop: 'quotationCount', label: '累计报价次数', sortable: 'custom', type: 'click', handle: (that, row) => that.openDialog(row, '累计报价次数', 'provider', 'quotationCount') },
    { istrue: true, prop: 'modifiedTime', label: '最后填写日期', sortable: 'custom', type: 'click', handle: (that, row) => that.openDialog(row, '最后填写日期', 'provider', 'modifiedTime') },
    { istrue: true, prop: 'dgt', label: '待沟通', sortable: 'custom', type: 'click', handle: (that, row) => that.openDialog(row, '对接状态', 'provider', 'dgt') },
    { istrue: true, prop: 'gtz', label: '沟通中', sortable: 'custom', type: 'click', handle: (that, row) => that.openDialog(row, '对接状态', 'provider', 'gtz') },
    { istrue: true, prop: 'jyz', label: '寄样中', sortable: 'custom', type: 'click', handle: (that, row) => that.openDialog(row, '对接状态', 'provider', 'jyz') },
    { istrue: true, prop: 'cgz', label: '采购中', sortable: 'custom', type: 'click', handle: (that, row) => that.openDialog(row, '对接状态', 'provider', 'cgz') },
    { istrue: true, prop: 'cgwc', label: '采购完成', sortable: 'custom', type: 'click', handle: (that, row) => that.openDialog(row, '对接状态', 'provider', 'cgwc') },
    { istrue: true, prop: 'bsh', label: '不适合', sortable: 'custom', type: 'click', handle: (that, row) => that.openDialog(row, '对接状态', 'provider', 'bsh') },
]

//点击最后填写时间和累计报价供应商的弹窗
const lastTableCol = [
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', },
    { istrue: true, prop: 'brandName', label: '采购', sortable: 'custom', },
    { istrue: true, prop: 'providerName', label: '供应商名称', sortable: 'custom',type: 'treeStar1', },
    { istrue: true, prop: 'sourceName', label: '推荐人', sortable: 'custom', },
    { istrue: true, prop: 'sourceType', label: '来源', sortable: 'custom', },
    { istrue: true, prop: 'modifiedTime', label: '最后填写日期', sortable: 'custom', },
    { istrue: true, prop: 'dockingBrandName', label: '对接人', sortable: 'custom', },
    { istrue: true, prop: 'dockingStatus', label: '对接状态', sortable: 'custom', },
]

//累计报价弹窗
const quotesTableCol = [
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', },
    { istrue: true, prop: 'brandName', label: '采购', sortable: 'custom', },
    { istrue: true, prop: 'providerName', label: '供应商名称', sortable: 'custom', },
    { istrue: true, prop: 'sourceName', label: '推荐人', sortable: 'custom', },
    { istrue: true, prop: 'sourceType', label: '来源', sortable: 'custom', },
    { istrue: true, prop: 'modifiedTime', label: '最后填写日期', sortable: 'custom', },
]

//进货量报价弹窗
const quotationTableCol = [
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', },
    { istrue: true, prop: 'goodCode', label: '商品编码', sortable: 'custom', },
    { istrue: true, prop: 'goodPic', label: '图片', sortable: 'custom',type:'images' },
    { istrue: true, prop: 'goodName', label: '商品名称', sortable: 'custom', },
    { istrue: true, prop: 'costPrice', label: '成本价', sortable: 'custom', },
    { istrue: true, prop: 'providerName', label: '供应商名称', sortable: 'custom', },
    { istrue: true, prop: 'sourceName', label: '推荐人', sortable: 'custom', },
    { istrue: true, prop: 'sourceType', label: '来源', sortable: 'custom', },
    { istrue: true, prop: 'modifiedTime', label: '最后填写日期', sortable: 'custom', },
    { istrue: true, prop: 'dockingStatus', label: '对接状态', sortable: 'custom', },
    { istrue: true, prop: 'dockingBrandName', label: '对接人', sortable: 'custom', },
]

//对接状态
const dockingStatus = [
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', },
    { istrue: true, prop: 'brandName', label: '采购', sortable: 'custom', },
    { istrue: true, prop: 'providerName', label: '供应商名称', sortable: 'custom', },
    { istrue: true, prop: 'sourceName', label: '推荐人', sortable: 'custom', },
    { istrue: true, prop: 'sourceType', label: '来源', sortable: 'custom', },
    { istrue: true, prop: 'modifiedTime', label: '最后填写日期', sortable: 'custom', },
    { istrue: true, prop: 'dockingBrandName', label: '对接人', sortable: 'custom', },
    { istrue: true, prop: 'dockingStatus', label: '对接状态', sortable: 'custom', },
]

//采购记录
const tableCols5 = [
    { istrue: true, prop: 'purchaseDate', label: '采购时间', sortable: 'custom' },
    { istrue: true, prop: 'buyNo', label: '采购单号', sortable: 'custom' },
    { istrue: true, prop: 'totalAmont', label: '采购金额', sortable: 'custom' },
    { istrue: true, prop: 'count', label: '采购量', sortable: 'custom' },
]

export default {
    components: { MyContainer, vxetablebase },
    name: "vendorGroup",
    props: {
        ListInfo: {
            type: Object,
            default: () => { }
        },
        checkList: {
            type: Array,
            default: () => { }
        }
    },
    data() {
        return {
            that: this,
            xlTableCol,//系列编码
            gysTableCol,//供应商
            goodTableCol,//商品编码
            lastTableCol,//最后填写时间
            quotesTableCol,//累计报价次数
            quotationTableCol,//进货量报价
            tableCols5,//采购记录
            dockingStatus,//对接状态
            xltableData: null,//系列编码
            goodTableData: [],//商品编码
            gysTableData: [],//供应商
            detailTotal: 0,//总数
            listLoading: true,//加载中
            dialogTitle: null,//弹窗标题
            dialogVisible: {//弹窗
                lastTimeDialog: false,//最后填写时间
                quotationDialog: false,//进货量报价
                cumulativeQuotesDialog: false,//累计报价次数
                dockingStatusDialog: false,//对接状态
            },
            dialogTotal: {//弹窗总数
                lastTimeTotal: 0,//最后填写时间
                cumulativeQuotesTotal: 0,//累计报价次数
                quotationTotal: 0,//进货量报价
                dockingStatusTotal: 0,//对接状态
            },
            dialogTableData: {//弹窗数据
                lastTimeData: [],//最后填写时间
                cumulativeQuotesData: [],//累计报价次数
                quotationData: [],//进货量报价
                dockingStatusData: [],//对接状态
            },
            dialogInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                styleCode: null,//系列编码
                categoryId: null,//品类id
                openId: null,//openId
                goodCode: null,//商品编码
                isBY: null,//是否包邮
                isContaisTax: null,//是否含税
                providerName: null,//供应商名称
                phone: null,//联系电话
                dockingStatus: null,//对接状态
                userName: null,//对接人
                brandId: null,//品牌id
                sourceType: null,//来源
                position: null,//职位
                isExitProvider: null,//是否存在供应商
                groupType: null,//分组类型
                lastModifiedTime: null,//最后填写时间
                groupDialogType: null,//弹窗类型
                clickOrder: null,//判断点击的是什么
            },
            nameInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                supplier: null,//供应商名称
            },
            nameVisible: false,//点击供应商名字弹层
            nameTotal: 0,//点击供应商名字弹层总数
            nameTableData: [],
            nameSummary:null
        };
    },
    mounted() {

    },
    //监听checkList
    watch: {
        checkList: {
            handler: function (newVal, oldVal) {
                //如果不一样就清空
                if (newVal != oldVal) {
                    this.clear()
                }
                this.listLoading = true
            },
            deep: true
        }
    },
    methods: {
        handleClose() {
            this.nameVisible = false
        },
        //点击供应商名称打开弹层
        async openNameDialog(row) {
            this.nameInfo.supplier = row.providerName ? row.providerName : row.supplier
            const { data, success } = await pagePurchaseOrderByProviderNameAsync(this.nameInfo)
            if (success) {
                this.nameTableData = data.list
                this.nameTotal = data.total
                this.nameSummary = data.summary
                this.nameVisible = true
                this.nameInfo.orderBy = null
            } else {
                this.$message.error('获取供应商采购记录失败')
            }
        },
        //对接记录弹层每页数量改变
        nameSizechange(val) {
            this.nameInfo.currentPage = 1;
            this.nameInfo.pageSize = val;
            this.openNameDialog(this.nameInfo)
        },
        //对接记录弹层当前页改变
        namePagechange(val) {
            this.nameInfo.currentPage = val;
            this.openNameDialog(this.nameInfo)
        },
        //匹配title
        matchingTitle(clickOrder) {
            const queryTitle = {
                modifiedTime: '最后填写日期',
                quotationCount: '累计报价次数',
                providerCount: '累计报价供应商',
                quotation1: '进货量100报价',
                quotation2: '进货量10000报价',
                quotation3: '进货量100000报价',
                dgt: '待沟通',
                gtz: '沟通中',
                jyz: '寄样中',
                cgz: '采购中',
                cgwc: '采购完成',
                bsh: '不适合',
            }
            //根据titleOrder找出对应的value
            this.dialogTitle = queryTitle[clickOrder]
        },
        dialogSizeChange(val) {
            this.dialogInfo.pageSize = val
            this.publicGetStyleList(this.dialogInfo.groupDialogType, this.dialogInfo.groupType, this.dialogInfo.clickOrder)
        },
        dialogPageChange(val) {
            this.dialogInfo.currentPage = val
            this.publicGetStyleList(this.dialogInfo.groupDialogType, this.dialogInfo.groupType, this.dialogInfo.clickOrder)
        },
        handleColse() {
            this.dialogVisible.lastTimeDialog = false
            this.dialogVisible.cumulativeQuotesDialog = false
            this.dialogVisible.dockingStatusDialog = false
            this.dialogVisible.quotationDialog = false
        },
        async openDialog(row, type, groupType, clickOrder) {
            this.clear()
            this.matchingTitle(clickOrder)//根据字段匹配title
            this.dialogInfo.clickOrder = clickOrder
            if (type == '最后填写日期') {
                if (groupType == 'style') {
                    if (clickOrder == 'modifiedTime') {
                        this.dialogInfo.lastModifiedTime = row.modifiedTime
                    } else {
                        this.dialogInfo.lastModifiedTime = null
                    }
                    this.dialogInfo.styleCode = row.styleCode
                }
                if (groupType == 'good') {
                    this.dialogInfo.goodCode = row.goodCode
                }
                if (groupType == 'provider') {
                    this.dialogInfo.lastModifiedTime = row.modifiedTime
                    this.dialogInfo.providerName = row.providerName
                }
                this.dialogInfo.groupDialogType = type
                this.dialogInfo.groupType = groupType
                this.publicGetStyleList(type, groupType, clickOrder)
            }
            if (type == '累计报价次数') {
                if (groupType == 'style') {
                    this.dialogInfo.styleCode = row.styleCode
                }
                if (groupType == 'good') {
                    this.dialogInfo.goodCode = row.goodCode
                }
                if (groupType == 'provider') {
                    this.dialogInfo.providerName = row.providerName
                }
                this.dialogInfo.groupDialogType = type
                this.dialogInfo.groupType = groupType
                this.publicGetStyleList(type, groupType, clickOrder)
            }
            if (type == '对接状态') {
                if (groupType == 'provider') {
                    this.dialogInfo.dockingStatus = this.dialogTitle
                    this.dialogInfo.providerName = row.providerName
                    this.dialogInfo.groupDialogType = type
                    this.dialogInfo.groupType = groupType
                    this.publicGetStyleList(type, groupType, clickOrder)
                }
            }
        },
        publicChange(obj, type, groupType) {
            if (type == '最后填写日期') {
                if (groupType == 'style') {
                    obj.styleCode = this.ListInfo.styleCode ? this.ListInfo.styleCode : this.dialogInfo.styleCode
                }
                if (groupType == 'good') {
                    obj.goodCode = this.ListInfo.goodCode ? this.ListInfo.goodCode : this.dialogInfo.goodCode
                }
                if (groupType == 'provider') {
                    obj.providerName = this.ListInfo.providerName ? this.ListInfo.providerName : this.dialogInfo.providerName
                }
            }
            if (type == '累计报价次数') {
                if (groupType == 'style') {
                    obj.styleCode = this.ListInfo.styleCode ? this.ListInfo.styleCode : this.dialogInfo.styleCode
                }
                if (groupType == 'good') {
                    obj.goodCode = this.ListInfo.goodCode ? this.ListInfo.goodCode : this.dialogInfo.goodCode
                }
                if (groupType == 'provider') {
                    obj.providerName = this.ListInfo.providerName ? this.ListInfo.providerName : this.dialogInfo.providerName
                }
            }
            if (type == '对接状态') {
                if (groupType == 'provider') {
                    obj.providerName = this.ListInfo.providerName ? this.ListInfo.providerName : this.dialogInfo.providerName
                    obj.dockingStatus = this.ListInfo.dockingStatus ? this.ListInfo.dockingStatus : this.dialogInfo.dockingStatus
                }
            }
            obj.orderBy = this.dialogInfo.orderBy
            obj.isAsc = this.dialogInfo.isAsc
            return obj
        },
        async publicGetStyleList(type, groupType, clickOrder) {
            let queryObj = { ...this.dialogInfo, ...this.ListInfo }
            let tableList = []
            let tableTotal = 0
            let successStaus = null
            if (type == '最后填写日期') {
                if (groupType == 'style') {
                    queryObj = this.publicChange(queryObj, type, groupType)
                    const { data, success } = await getProviderQuotationOverViewGroupDialogStylePageList(queryObj)
                    tableList = data.list
                    tableTotal = data.total
                    successStaus = success
                }
                if (groupType == 'good') {
                    queryObj = this.publicChange(queryObj, type, groupType)
                    const { data, success } = await getProviderQuotationOverViewGroupDialogGoodPageList(queryObj)
                    tableList = data.list
                    tableTotal = data.total
                    successStaus = success
                }
                if (groupType == 'provider') {
                    queryObj = this.publicChange(queryObj, type, groupType)
                    const { data, success } = await getProviderQuotationOverViewGroupDialogProviderPageList(queryObj)
                    tableList = data.list
                    tableTotal = data.total
                    successStaus = success
                }
                if (successStaus) {
                    if (clickOrder == 'quotation1' || clickOrder == 'quotation2' || clickOrder == 'quotation3') {
                        this.dialogTableData.quotationData = tableList
                        this.dialogTotal.quotationTotal = tableTotal
                        this.dialogVisible.quotationDialog = true
                        return
                    }
                    this.dialogTableData.lastTimeData = tableList
                    this.dialogTotal.lastTimeTotal = tableTotal
                    this.dialogVisible.lastTimeDialog = true
                }
            }
            if (type == '累计报价次数') {
                if (groupType == 'style') {
                    queryObj = this.publicChange(queryObj, type, groupType)
                    const { data, success } = await getProviderQuotationOverViewGroupDialogStylePageList(queryObj)
                    tableList = data.list
                    tableTotal = data.total
                    successStaus = success
                }
                if (groupType == 'good') {
                    queryObj = this.publicChange(queryObj, type, groupType)
                    const { data, success } = await getProviderQuotationOverViewGroupDialogGoodPageList(queryObj)
                    tableList = data.list
                    tableTotal = data.total
                    successStaus = success
                }
                if (groupType == 'provider') {
                    queryObj = this.publicChange(queryObj, type, groupType)
                    const { data, success } = await getProviderQuotationOverViewGroupDialogProviderPageList(queryObj)
                    tableList = data.list
                    tableTotal = data.total
                    successStaus = success
                }
                if (successStaus) {
                    this.dialogTableData.cumulativeQuotesData = tableList
                    this.dialogTotal.cumulativeQuotesTotal = tableTotal
                    this.dialogVisible.cumulativeQuotesDialog = true
                }
            }
            if (type == '对接状态') {
                if (groupType == 'provider') {
                    queryObj = this.publicChange(queryObj, type, groupType)
                    const { data, success } = await getProviderQuotationOverViewGroupDialogProviderPageList(queryObj)
                    tableList = data.list
                    tableTotal = data.total
                    successStaus = success
                    if (successStaus) {
                        this.dialogTableData.dockingStatusData = tableList
                        this.dialogTotal.dockingStatusTotal = tableTotal
                        this.dialogVisible.dockingStatusDialog = true
                    }
                }
            }
        },
        //获取分组列表
        async getGroupList(info) {
            this.ListInfo.listLoading = true
            const queryObj = [
                {
                    label: '供应商',
                    value: 'provider'
                },
                {
                    label: '商品编码',
                    value: 'good'
                },
                {
                    label: '系列编码',
                    value: 'style'
                },
            ]
            //根据info找出对应的value
            if (info) {
                this.ListInfo.groupType = queryObj.find(item => item.label == info).value
            }
            const { data, success } = await getProviderQuotationRecordOverViewGroupPageList(this.ListInfo)
            this.$nextTick(() => {
                if (success) {
                    if (this.ListInfo.groupType == 'style') {
                        this.xltableData = data.list
                    } else if (this.ListInfo.groupType == 'good') {
                        this.goodTableData = data.list
                    } else if (this.ListInfo.groupType == 'provider') {
                        this.gysTableData = data.list
                    }
                    this.detailTotal = data.total
                    this.listLoading = false
                }
            });
        },
        //页面数量改变
        detailSizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.PageSize = val;
            this.getGroupList(this.checkList[0]);
        },
        //当前页改变
        detailPagechange(val) {
            this.ListInfo.currentPage = val;
            this.getGroupList(this.checkList[0]);
        },
        sortchange({ order, prop }) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = 50;
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getGroupList(this.checkList[0])
            }
        },
        lastSortchange({ order, prop }) {
            if (prop) {
                this.dialogInfo.orderBy = prop
                this.dialogInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.publicGetStyleList(this.dialogInfo.groupDialogType, this.dialogInfo.groupType, this.dialogInfo.clickOrder)
            }
        },
        clear() {
            this.dialogInfo = {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                styleCode: null,//系列编码
                categoryId: null,//品类id
                openId: null,//openId
                goodCode: null,//商品编码
                isBY: null,//是否包邮
                isContaisTax: null,//是否含税
                providerName: null,//供应商名称
                phone: null,//联系电话
                dockingStatus: null,//对接状态
                userName: null,//对接人
                brandId: null,//品牌id
                sourceType: null,//来源
                position: null,//职位
                isExitProvider: null,//是否存在供应商
                groupType: null,//分组类型
                lastModifiedTime: null,//最后填写时间
                groupDialogType: null//弹窗类型
            }
        },
        sortchange3({ order, prop }) {
            if (prop) {
                if (prop == 'count') {
                    this.nameInfo.orderBy = 'totalCount'
                } else {
                    this.nameInfo.orderBy = prop
                }
                this.nameInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.openNameDialog(this.nameInfo)
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.header {
    display: flex;
    margin-bottom: 10px;
}

.publicMargin {
    margin-right: 20px;
}

.detail ::v-deep .vxetoolbar20221212 {
    display: none !important;
}

::v-deep .el-badge__content {
    padding: 0 4px;
    top: 7px;
    right: 0;
}

.detail ::v-deep .vxe-custom--wrapper {
    display: none !important;
}

::v-deep .el-checkbox__input {
    border-radius: 7px !important;
}
</style>
