<template>
  <container v-loading="pageLoading">
      <!-- :tableHandles='tableHandles'  -->
     <ces-table ref="table" :that='that' :isIndex='true'   
         :hasexpand='true' :tableData='list' :tableCols='tableCols' @sortchange='sortchange' :loading="listLoading"
         :showsummary='true' :summaryarry='summaryarry'>
      
         <template slot='extentbtn'>
      <el-button-group>
     
        
  
    <el-button type="primary" @click="onSearch">刷新</el-button> 
     
      </el-button-group>
       </template>
      </ces-table>
      
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
   
  
 

  </container>
</template>
<script>
import {getShopProfitAnalysisSum,getOperatingProfitAnalysisSum,getDetail1Pdd}from '@/api/bookkeeper/financialreport'
import {formatTime,formatYesornoBool,formatWarehouseArea,formatIsOutStock,formatSecondToHour,formatPlatform} from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import logistics from '@/components/Comm/logistics'
import goodscoderecord from '@/views/inventory/goodscoderecord'
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";

const tableCols =[
   
     {istrue:true,prop:'version',label:'类型', width:'50',formatter:(row)=>!row.version?"": row.version=='v1'?"工资月报":"参考月报"},
      {istrue:true,prop:'yearMonth',label:'年月',sortable:'custom', width:'80',},
      {istrue:true,prop:'productID',label:'产品ID',sortable:'custom', width:'80',},
      {istrue:true,prop:'operating',label:'运营',sortable:'custom', width:'80',},
      {istrue:true,prop:'operatingFee',label:'运营提成', width:'80',},
      {istrue:true,prop:'shopName',label:'店铺',sortable:'custom', width:'60',},
      {istrue:true,prop:'goodsCode',label:'商品编码',sortable:'custom', width:'80',},
      {istrue:true,prop:'seriesCoding',label:'系列编码',sortable:'custom', width:'80',},
      {istrue:true,prop:'productName',label:'产品名称', width:'80',sortable:'custom'},
      {istrue:true,prop:'fLtype',label:'辅料类型', width:'80'},
      {istrue:true,prop:'idNumber',label:'ID计数', width:'80',sortable:'custom'},
      {istrue:true,prop:'settlementIncome',label:'结算收入', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountSettlement_2',label:'2月之前月份收入', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountTaoKeNot',label:'淘客不计', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountShare',label:'参与公摊金额', width:'80',sortable:'custom'},
      {istrue:true,prop:'saleBen',label:'销售成本', width:'80',sortable:'custom'},
      {istrue:true,prop:'replacementAmount',label:'补发成本', width:'80',sortable:'custom'},
      {istrue:true,prop:'abnormalAmount',label:'异常成本', width:'80',sortable:'custom'},
      {istrue:true,prop:'replacePoor',label:'代发成本差', width:'80',sortable:'custom'},
      {istrue:true,prop:'cornerBalance',label:'定制护墙角差额', width:'80',sortable:'custom'},
      {istrue:true,prop:'purchaseFreight',label:'采购运费', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountGrossProfitSale',label:'销售毛利', width:'80',sortable:'custom'},


      {istrue:true,prop:'platformViolationsDeductions',label:'平台违规扣款', width:'80',sortable:'custom'},
      {istrue:true,prop:'duoDuoDeductionsFee',label:'多多进宝费用扣除', width:'80',sortable:'custom'},
      {istrue:true,prop:'duoDuoReturnFee',label:'多多进宝费用返还', width:'80',sortable:'custom'},
      {istrue:true,prop:'technologyFee',label:'技术服务费', width:'80',sortable:'custom'},
      {istrue:true,prop:'smallMoney',label:'小额打款', width:'80',sortable:'custom'},
      {istrue:true,prop:'loanFullReturn',label:'贷款充值单店满返', width:'80',sortable:'custom'},
      {istrue:true,prop:'billFeeTotal',label:'账单费用合计', width:'80',sortable:'custom'},
      {istrue:true,prop:'courierFees',label:'快递费', width:'80',sortable:'custom'},
      {istrue:true,prop:'courierFeesWgkk',label:'快递违规扣款', width:'80',sortable:'custom'},
      {istrue:true,prop:'packageFee',label:'包装费', width:'80',sortable:'custom'},
      {istrue:true,prop:'orderTotalAmount',label:'订单费用合计', width:'80',sortable:'custom'},
      {istrue:true,prop:'searchTuiGuang',label:'搜索推广', width:'80',sortable:'custom'},
      {istrue:true,prop:'scenarioShow',label:'场景展示', width:'80',sortable:'custom'},
      {istrue:true,prop:'fangXinTui',label:'放心推', width:'80',sortable:'custom'},
      {istrue:true,prop:'deposit',label:'放心推id', width:'80',sortable:'custom'},
      {istrue:true,prop:'fangXinTuiId',label:'保证金', width:'80',sortable:'custom'},
      {istrue:true,prop:'fangXinTuiAvg',label:'放心推均值', width:'80',sortable:'custom'},
      {istrue:true,prop:'zhiBoTuiGuang',label:'直播推广', width:'80',sortable:'custom'},
      {istrue:true,prop:'quanZhanTuiGuang',label:'全站推广', width:'80',sortable:'custom'},
      {istrue:true,prop:'taolijing',label:'淘礼金', width:'80',sortable:'custom'},
      {istrue:true,prop:'teShuDanFee',label:'特殊单费用', width:'80',sortable:'custom'},
      {istrue:true,prop:'teshudanyongjin',label:'特殊单佣金', width:'80',sortable:'custom'},
      {istrue:true,prop:'teshudanben',label:'特殊单成本', width:'80',sortable:'custom'},
      {istrue:true,prop:'operatingFeeTotal',label:'运营费合计', width:'80',sortable:'custom'},
      {istrue:true,prop:'advRate',label:'广告费占比', width:'80',sortable:'custom'},
      {istrue:true,prop:'amontSample',label:'样品费', width:'80',sortable:'custom'},
      {istrue:true,prop:'operatingSample',label:'拿样费', width:'80',sortable:'custom'},
      {istrue:true,prop:'shootingFeeMG',label:'美工拍摄费用', width:'80',sortable:'custom'},
      {istrue:true,prop:'productFreightfee',label:'产品运费', width:'80',sortable:'custom'},
      {istrue:true,prop:'totalProductCost',label:'产品费用合计', width:'80',sortable:'custom'},
      {istrue:true,prop:'amontCommissionMG',label:'美工提成', width:'80',sortable:'custom'},
      {istrue:true,prop:'amontCommissionCG',label:'采购提成', width:'80',sortable:'custom'},
      {istrue:true,prop:'amontWagesGroup',label:'小组运营工资', width:'80',sortable:'custom'},
      {istrue:true,prop:'amontMachineAvg',label:'加工费分摊', width:'80',sortable:'custom'},
      {istrue:true,prop:'totalWagesCost',label:'工资合计', width:'80',sortable:'custom'},
      {istrue:true,prop:'storeLossfee',label:'仓库损耗', width:'80',sortable:'custom'},
      {istrue:true,prop:'operatingDown',label:'运营下架', width:'80',sortable:'custom'},
      {istrue:true,prop:'grossProfit',label:'产品利润', width:'80',sortable:'custom'},
      {istrue:true,prop:'deposit',label:'保证金', width:'80',sortable:'custom'},
      {istrue:true,prop:'deductions',label:'扣款', width:'80',sortable:'custom'},
     
     ];


export default {
  name: "Users",
  components: {container,cesTable,MyConfirmButton,logistics},
   props:{
       filter: { }
     },
  data() {
    return {
    
      uploadLoading:false,
      dialogVisible: false,
      that:this,
      // filter: {
      //   timerange:'',   
      // },
      list: [],
      drawervisible:false,
      tableCols:tableCols,
      pager:{OrderBy:"",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      listLoading: false,
      pageLoading: false,
    };
  },
  watch: {
    value(n) {
      if(n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
      }
    }
  },
 async mounted() {
    await this.onSearch();
    await this.getlist();
  },
 methods: {
   onSelsChangeReturnGoods(sels){
    this.sels2 = sels
   },

  
   async onSearch() {
       this.$refs.pager.setPage(1)
       this.getlist()
    },
   async getlist() {
     this.filter.startTime =null;
       this.filter.endTime =null;
       if (this.filter.yearMonth && this.filter.yearMonth.length>0) {
                this.filter.startTime = this.filter.yearMonth[0];
                this.filter.endTime = this.filter.yearMonth[1];
            }
      if (!this.pager.OrderBy) this.pager.OrderBy="";
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      params.SeriesCoding=this.filter.SeriesCoding.join();
      this.listLoading = true
      const res = await getDetail1Pdd(params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {d._loading = false})
      this.list = data
      this.summaryarry=res.data.summary;
    },

    beforeRemove() {
      return false;
    },
    
   doCopy: function (val) {       
      let that=this;                         
      this.$copyText(val).then(function (e) {
          that.$message({ message: "内容已复制到剪切板！", type: "success" });
      }, function (e) {
          that.$message({ message: "抱歉，复制失败！", type: "warning" });
      })
    },
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   
  },
};
</script>


