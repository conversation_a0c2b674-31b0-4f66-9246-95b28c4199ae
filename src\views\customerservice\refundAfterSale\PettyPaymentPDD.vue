<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <template #header>
        <el-form  class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent ></el-form>
      </template>
      <!--列表-->
      <ces-table      ref="table"
        :that="that"
        :isIndex="true"
        :hasexpand="false"
        @sortchange="sortchange"
        :tableData="pddcontributeinfolist"
        @select="selectchange"
        :isSelection="false"
        :tableCols="tableCols"
        :loading="listLoading"
        :summaryarry='summaryarry'
      >
        <el-table-column type="expand">
          <template slot-scope="props">
            <div>
              <el-table :data="props.row.detaildata" style="width: 100%">
                <el-table-column
                  v-for="col in props.row.detailcols"
                  :prop="col.prop"
                  :label="col.label"
                  :key="col"
                >
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <template slot="extentbtn">
          <el-button-group>
            
            <el-button style="padding: 0;margin: 0;">
                <el-input v-model.trim="Filter.OrderNo" maxlength="50" clearable placeholder="订单号" style="width:150px;" />
              </el-button>
              
              <el-button style="padding: 0;margin: 0;">
                <el-input v-model.trim="Filter.Proposer" maxlength="50" clearable placeholder="申请人" style="width:150px;" />
              </el-button>
              <el-button style="padding: 0;margin: 0;">
                <el-input v-model.trim="Filter.ShopName" maxlength="50" clearable placeholder="店铺名" style="width:150px;" />
              </el-button>
             
              <el-button style="padding: 0;margin: 0;">
                <el-date-picker style="width: 250px" v-model="Filter.timerange" type="datetimerange" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd" range-separator="至" start-placeholder="申请开始日期" end-placeholder="申请结束日期">
                </el-date-picker>
              </el-button>
              
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="onstartImport">导入</el-button>
            <el-button type="primary" @click="onExport">导出</el-button>
          </el-button-group>
        </template>
      </ces-table>
      <!--分页-->
      <template #footer>
        <my-pagination
          ref="pager"
          :total="total"
          :checked-count="sels.length"
          @get-page="getpddcontributeinfoList"
        />
      </template>
      <el-dialog title="导入" :visible.sync="dialogVisible" width="40%">
        <span>
          <el-row>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
              <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action accept=".xlsx"
                :http-request="uploadFile" :file-list="fileList" :on-change="uploadChange">
                <template #trigger>
                  <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                  @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
              </el-upload>
            </el-col>
          </el-row>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </el-dialog>
      
    </my-container>
  </template>
  <script>
  import { getSaleAfterPddList,exportSaleAfterPddList,getPettyPaymentPDDList,exportPettyPaymentPDDList} from '@/api/bookkeeper/reportday'
  import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
  import dayjs from "dayjs";
  import cesTable from "@/components/Table/table.vue";
  import { formatPlatform, formatTime, formatYesornoBool, formatLinkProCode } from "@/utils/tools";
  import { getListByStyleCode } from "@/api/inventory/basicgoods"
  import { importSaleAfterPdd,importPettyPaymentPDD } from '@/api/bookkeeper/import'
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  const tableCols = [
  { istrue: true,  prop: 'orderNo', label: '订单号', sortable: 'custom', width: '150' },
  { istrue: true,  prop: 'shopName',  label: '店铺', width: '100',sortable: 'custom' },
//   { istrue: true,  prop: 'proCode', label: '商品ID', sortable: 'custom', width: '150', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
  { istrue: true,  prop: 'proName', label: '商品名称', sortable: 'custom', width: '150' },
  { istrue: true,  prop: 'deductMoneyReason', label: '扣款原因', sortable: 'custom', width: '150'},
  { istrue: true,  prop: 'proposer', label: '申请人', sortable: 'custom', width: '150' },
  { istrue: true,  prop: 'applicationTime', label: '申请时间', sortable: 'custom', width: '150',},
  { istrue: true,  prop: 'applicantDeductMoney', label: '申请扣款金额', sortable: 'custom', width: '150', },
  { istrue: true,  prop: 'paymentStatus', label: '打款状态', sortable: 'custom', width: '150', },
  { istrue: true,  prop: 'remark', label: '备注', sortable: 'custom', width: '150',  },
  
  ];
  export default {
    name: "Users",
    components: {
      MyContainer,
      MyConfirmButton,
      MySearch,
      MySearchWindow,
      cesTable,
      
    },
    data() {
      return {
        // onExportDialogVisible: false,
        dialogVisible: false,
        uploadLoading:false,
        searchloading:false,
        importfilter:{
          version:''
        },
        that: this,
        Filter: {
        platform: 2,
        ShopName: null,
        ProCode: null,
        StartApplicationTime: null,
        EndApplicationTime: null,
        Proposer: null,
        timerange: null,
        ExportType: '',
        SumType:null
      },
      options: [],
      styleCode: null,
        shopList: [],
        userList: [],
        groupList: [],
        pddcontributeinfolist: [],
        tableCols: tableCols,
        total: 0,
        summaryarry: {  },
        pager: { OrderBy: "", IsAsc: false },
        sels: [], // 列表选中列
        listLoading: false,
        pageLoading: false,
        selids: [],
        fileList: [],
      };
    },
    async mounted() {},
    async created() {
    await this.init()
    await this.getShopList();
    
  },
    methods: {

 //导出
 async onExport() {
     if (this.onExporting) return;
     try{
          this.Filter.StartApplicationTime = null;
          this.Filter.EndApplicationTime = null;
          if (this.Filter.timerange) {
          this.Filter.StartApplicationTime = this.Filter.timerange[0];
          this.Filter.EndApplicationTime = this.Filter.timerange[1];
        }
       
        this.uploadLoading = true;
        const params = {...this.pager,...this.Filter}
        var res= await exportPettyPaymentPDDList(params);
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','拼多多小额打款_' + new Date().toLocaleString() + '.xlsx' )
        this.uploadLoading = false;
       
        aLink.click()
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
      this.onExporting=false;
     },


      async onstartImport() {
      this.fileList = [];
      this.uploadLoading = false;
      this.dialogVisible = true;
    },
    submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      this.$refs.upload.submit();
    },
    async uploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      let res = await importPettyPaymentPDD(form);
      if (res.code == 1) {
        this.dialogVisible = false
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      }
      this.fileList = []
      this.uploadLoading = false
    },
    async uploadChange(file, fileList) {
      let files = [];
      files.push(file);
      this.fileList = files;
    },
      
      
      datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init() {
      var date1 = new Date(); date1.setDate(date1.getDate() - 10);
      var date2 = new Date(); date2.setDate(date2.getDate() - 1);
      this.Filter.timerange = [];
      this.Filter.timerange[0] = this.datetostr(date1);
      this.Filter.timerange[1] = this.datetostr(date2);
    },
      
      sortchange(column) {
        if (!column.order) this.pager = {};
        else
          this.pager = {
            OrderBy: column.prop,
            IsAsc: column.order.indexOf("descending") == -1 ? true : false,
          };
        this.onSearch();
      },
      
      onSearch() {
        this.$refs.pager.setPage(1);
        this.getpddcontributeinfoList();
      },
      async getpddcontributeinfoList() {
        this.Filter.StartApplicationTime = null;
          this.Filter.EndApplicationTime = null;
      
        if (this.Filter.timerange) {
          this.Filter.StartApplicationTime = this.Filter.timerange[0];
          this.Filter.EndApplicationTime = this.Filter.timerange[1];
        }
      
        const para = { ...this.Filter };
        var pager = this.$refs.pager.getPager();
        const params = {
          ...pager,
          ...this.pager,
          ...para,
        };
        this.listLoading = true;
        const res = await getPettyPaymentPDDList(params);
        console.log(res);
        this.listLoading = false;
        console.log(res.data.list);
        this.total = res.data.total;
        this.pddcontributeinfolist = res.data.list;
        this.summaryarry=res.data.summary;
      },
      selectchange: function (rows, row) {
        this.selids = [];
        rows.forEach((f) => {
          this.selids.push(f.id);
        });
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
  