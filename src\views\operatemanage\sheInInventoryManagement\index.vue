<template>
  <my-container>
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="希音销售主题分析" name="first1" style="height: 100%" :lazy="true">
        <sheInSalesSubjectAnalysis ref="sheInSalesSubjectAnalysis"></sheInSalesSubjectAnalysis>
      </el-tab-pane>
      <el-tab-pane label="希音台账变动明细" name="first2" style="height: 100%" :lazy="true">
        <sheInLedgerChangeDetails ref="sheInLedgerChangeDetails"></sheInLedgerChangeDetails>
      </el-tab-pane>
      <el-tab-pane label="希音期初明细" name="first3" style="height: 100%" :lazy="true">
        <OpeningDetailSheIn ref="sheInWarehousingVariance"></OpeningDetailSheIn>
      </el-tab-pane>
      <el-tab-pane label="公司发货与平台入库差异" name="first4" style="height: 100%" :lazy="true">
        <sheInWarehousingVariance ref="sheInWarehousingVariance"></sheInWarehousingVariance>
      </el-tab-pane>
      <el-tab-pane label="希音平台库存台账" name="first5" style="height: 100%" :lazy="true">
        <sheInStockLedger ref="sheInStockLedger"></sheInStockLedger>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import sheInSalesSubjectAnalysis from "./sheInSalesSubjectAnalysis.vue";
import sheInLedgerChangeDetails from "./sheInLedgerChangeDetails.vue";
import sheInWarehousingVariance from "./sheInWarehousingVariance.vue";
import sheInStockLedger from "./sheInStockLedger.vue";
import OpeningDetailSheIn from "./OpeningDetailSheIn.vue";


export default {
  name: "index",
  components: {
    MyContainer, sheInSalesSubjectAnalysis, sheInLedgerChangeDetails, sheInWarehousingVariance, sheInStockLedger,OpeningDetailSheIn
  },
  data() {
    return {
      activeName: "first1",
    };
  },
};
</script>

<style lang="scss" scoped></style>
