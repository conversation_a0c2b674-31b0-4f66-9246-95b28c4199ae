<template>
    <my-container >
        <el-tabs v-model="activeName"  style="height: calc(100% - 40px);" >
            <el-tab-pane label="推荐人汇总" name="first"  style="height: 100%;">
                <distributionDetails v-if="activeName === 'first'"></distributionDetails>
            </el-tab-pane>
            <el-tab-pane label="分销商管理" name="second"  style="height: 100%;">
                <distributionManage v-if="activeName === 'second'"></distributionManage>
            </el-tab-pane>
            <el-tab-pane label="外部推荐人" name="third"  style="height: 100%;">
                <externalReferrers v-if="activeName === 'third'"></externalReferrers>
            </el-tab-pane>
        </el-tabs>
    </my-container >
</template>

<script>
import distributionManage from './distributionManage.vue'
import distributionDetails from './distributionDetails.vue'
import externalReferrers from './externalReferrers.vue'
import MyContainer from "@/components/my-container";

export default {
    name: "distributionManagement",
    components: {
        distributionManage,
        distributionDetails,
        externalReferrers,
        MyContainer
    },
    data () {
        return {
            activeName: 'first'
        }
    },
}
</script>

<style scoped lang="scss"></style>