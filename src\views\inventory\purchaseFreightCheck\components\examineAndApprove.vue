<template>
  <div style="width: 100%;padding: 5px 0;">
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="145px" class="demo-ruleForm">
      <el-row :gutter="20"> <!-- 间距 -->
        <el-col :span="12">
          <el-form-item label="流程" prop="processCode">
            <el-select v-model="ruleForm.processCode" placeholder="钉钉流程" filterable clearable class="publicCss"
              @chagne="processChange">
              <!-- <el-option key="浙江总公司费用申请" label="浙江总公司费用申请" value="PROC-A4A62373-054A-4D41-AA16-035484DA0BC8"></el-option>
              <el-option key="南昌分公司费用申请" label="南昌分公司费用申请" value="PROC-B95BAED2-B58C-4890-AC9D-CB235CD61AFF"></el-option>
              <el-option key="西安分公司费用申请" label="西安分公司费用申请" value="PROC-31A0F63E-A8E8-418D-9C43-1A7B728AACE1"></el-option> -->
              <el-option key="深圳分公司费用申请" label="深圳分公司费用申请"
                value="PROC-69DF0D28-AC89-4D34-AF2A-5FEFB3C105EF"></el-option>
              <el-option key="物流费用申请" label="物流费用申请" value="PROC-A179EC32-4E24-4B5D-9EAB-4670CB4CC460"></el-option>
              <el-option key="“独立核算”费用申请" label="“独立核算”费用申请"
                value="PROC-E29D6F69-C8CC-40D0-A801-315C911E329D"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所在部门" prop="deptName">
            <el-input v-model.trim="ruleForm.deptName" placeholder="所在部门" maxlength="50" clearable class="publicCss" />
          </el-form-item>
          <el-form-item label="申请事由" prop="applyReason">
            <el-input v-model.trim="ruleForm.applyReason" placeholder="申请事由" maxlength="50" clearable
              class="publicCss" />
          </el-form-item>
          <el-form-item label="使用部门" prop="useWare"
            v-if="ruleForm.processCode != 'PROC-A179EC32-4E24-4B5D-9EAB-4670CB4CC460'">
            <el-select v-model="ruleForm.useWare" placeholder="使用部门" multiple collapse-tags filterable clearable
              class="publicCss">
              <el-option v-for="item in warehouselist" :label="item.name" :value="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="使用平台" prop="usePlatform">
            <el-select v-model="ruleForm.usePlatform" placeholder="使用平台" multiple collapse-tags filterable clearable
              class="publicCss">
              <el-option key="天猫" label="天猫" value="天猫"></el-option>
              <el-option key="拼多多" label="拼多多" value="拼多多"></el-option>
              <el-option key="抖音" label="抖音" value="抖音"></el-option>
              <el-option key="京东" label="京东" value="京东"></el-option>
              <el-option key="阿里巴巴" label="阿里巴巴" value="阿里巴巴"></el-option>
              <el-option key="分销" label="分销" value="分销"></el-option>
              <el-option key="跨境-SHEIN" label="跨境-SHEIN" value="跨境-SHEIN"></el-option>
              <el-option key="跨境-TEMU" label="跨境-TEMU" value="跨境-TEMU"></el-option>
              <el-option key="淘宝" label="淘宝" value="淘宝"></el-option>
              <el-option key="淘工厂" label="淘工厂" value="淘工厂"></el-option>
              <el-option key="苏宁" label="苏宁" value="苏宁"></el-option>
              <el-option key="全平台" label="全平台" value="全平台"></el-option>
              <el-option key="视频号" label="视频号" value="视频号"></el-option>
              <el-option key="快手" label="快手" value="快手"></el-option>
              <el-option key="线下" label="线下" value="线下"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="费用分类" prop="expenseType">
            <el-input v-model.trim="ruleForm.expenseType" placeholder="费用分类" maxlength="50" disabled clearable
              class="publicCss" />
          </el-form-item>
          <el-form-item label="开户行" prop="openingBank">
            <el-input v-model.trim="ruleForm.openingBank" placeholder="开户行" maxlength="50" clearable
              class="publicCss" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最晚支付日期" prop="lastPaymentDate">
            <el-date-picker class="publicCss" v-model="ruleForm.lastPaymentDate" type="date" placeholder="选择日期"
              :clearable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="金额(元)" prop="totalPrice">
            <el-input v-model.trim="ruleForm.totalPrice" placeholder="金额(元)" maxlength="50" disabled clearable
              class="publicCss" />
          </el-form-item>
          <el-form-item label="大写" prop="totalPriceCN">
            <el-input v-model.trim="ruleForm.totalPriceCN" placeholder="大写" maxlength="50" disabled clearable
              class="publicCss" />
          </el-form-item>
          <el-form-item label="付款方式" prop="payment">
            <el-select v-model="ruleForm.payment" placeholder="付款方式" filterable clearable class="publicCss">
              <el-option key="银行卡" label="银行卡" value="银行卡"></el-option>
              <el-option key="支付宝" label="支付宝" value="支付宝"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="账户名" prop="accountName">
            <el-input v-model.trim="ruleForm.accountName" placeholder="账户名" maxlength="50" clearable
              class="publicCss" />
          </el-form-item>
          <el-form-item label="账号" prop="accountNumber">
            <el-input v-model.trim="ruleForm.accountNumber" placeholder="账号" maxlength="50" clearable
              class="publicCss" />
          </el-form-item>
          <el-form-item label="核算单位" prop="checkUnit"
            v-if="ruleForm.processCode == 'PROC-E29D6F69-C8CC-40D0-A801-315C911E329D'">
            <el-select v-model="ruleForm.checkUnit" placeholder="核算单位" filterable clearable class="publicCss">
              <el-option key="南昌餐饮" label="南昌餐饮" value="南昌餐饮"></el-option>
              <el-option key="武汉小卖部" label="武汉小卖部" value="武汉小卖部"></el-option>
              <el-option key="南昌小卖部" label="南昌小卖部" value="南昌小卖部"></el-option>
              <el-option key="南昌裁剪仓" label="南昌裁剪仓" value="南昌裁剪仓"></el-option>
              <el-option key="首力供应链" label="首力供应链" value="首力供应链"></el-option>
              <el-option key="美甲项目部" label="美甲项目部" value="美甲项目部"></el-option>
              <el-option key="集包厂" label="集包厂" value="集包厂"></el-option>
              <el-option key="其他" label="其他" value="其他"></el-option>
              <el-option key="云仓-图兰朵" label="云仓-图兰朵" value="云仓-图兰朵"></el-option>
              <el-option key="云仓-微鸿" label="云仓-微鸿" value="云仓-微鸿"></el-option>
              <el-option key="云仓-千艺" label="云仓-千艺" value="云仓-千艺"></el-option>
              <el-option key="云仓-娆酱" label="云仓-娆酱" value="云仓-娆酱"></el-option>
              <el-option key="云仓-罗小曼" label="云仓-罗小曼" value="云仓-罗小曼"></el-option>
              <el-option key="1688选品中心项目" label="1688选品中心项目" value="1688选品中心项目"></el-option>
              <el-option key="直播协会-厉激" label="直播协会-厉激" value="直播协会-厉激"></el-option>
              <el-option key="义乌餐饮" label="义乌餐饮" value="义乌餐饮"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div>
      <div
        style="display: flex; align-items: center; justify-content: space-between; padding: 10px 0; background: #f8f9fa; border-radius: 6px; margin-bottom: 10px;">
        <el-button type="primary" size="small" icon="el-icon-plus" @click="addRow"
          style="margin-left: 15px;">添加数据</el-button>
        <span style="color: #e6a23c; font-size: 13px; font-weight: 500; margin-right: 15px;">
          <i class="el-icon-warning" style="margin-right: 4px;"></i>
          列表中最少需有一条数据
        </span>
      </div>
      <MyContainer>
        <vxetablebase :id="'examineAndApprove202410181049'" :tablekey="'examineAndApprove202410181049'" ref="table"
          :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
          :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
          style="width: 100%;  margin: 0" :loading="loading" :height="'300px'" :border="true" :isRemoteSort="false">
          <template slot="right">
            <vxe-column title="操作" width="70" fixed="right">
              <template #default="{ row, rowIndex }">
                <div style="display: flex;justify-content: center;">
                  <el-button type="text" style="color: red;" @click="handleDelete(rowIndex)">删除</el-button>
                </div>
              </template>
            </vxe-column>
          </template>
        </vxetablebase>
        <!-- <template #footer>
          <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template> -->
      </MyContainer>
    </div>
    <div style="display: flex;justify-content: center;gap: 30px;margin-top: 20px;">
      <el-button @click="resetForm('ruleForm')">取消</el-button>
      <el-button type="primary" @click="submitForm('ruleForm')" :loading="submitLoading">发起审批</el-button>
    </div>
    <el-dialog title="添加数据" :visible.sync="addInfo.visible" width="60%" v-dialogDrag :close-on-click-modal="false"
      append-to-body>
      <MyContainer>
        <vxetablebase :id="'examineAndApprove202507061012'" :tablekey="'examineAndApprove202507061012'" ref="table1"
          :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchangeAdd'
          :tableData='addInfo.tableData' :tableCols='tableCols1' :isSelection="false" :isSelectColumn="false"
          style="width: 100%;  margin: 0" :loading="addInfo.loading" :height="'400px'" @select="selectchange"
          :border="true">
        </vxetablebase>
        <template #footer>
          <my-pagination ref="pager1" :total="addInfo.total" @page-change="PagechangeAdd"
            @size-change="SizechangeAdd" />
        </template>
      </MyContainer>
      <div style="display: flex;justify-content: center;gap: 20px;">
        <el-button @click="addInfo.visible = false">取 消</el-button>
        <el-button type="primary" @click="onAddSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import dayjs from 'dayjs'
import { sendPurchaseCostVerifyApply, getPurchaseCostVerifyApplyData, getPurchaseCostVerifyApplyTableData, getPurchaseCostVerifyWarehousePage } from '@/api/inventory/purchaseCostVerify'
import { getAllWarehouse } from '@/api/inventory/warehouse'
import decimal from '@/utils/decimal'
import { removeTrailingZeros, numberToChinese } from '@/utils/tools'
const tableCols = [
  { width: '100', align: 'center', prop: 'regionCost', label: '区域费用', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'warehousingNo', label: '入库单号', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'buyNo1', label: '采购单号1', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'buyNo2', label: '采购单号2', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'brandName', label: '采购', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'warehouseName', label: '仓库', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'totalPerTicket', label: '单票合计', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'orderFee1', label: '单号1费用', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'orderFee2', label: '单号2费用', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'haulage', label: '托运费', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'deliveryFee', label: '送货费', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'pickUpFee', label: '提货费', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'huoLaLa', label: '货拉拉', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'loadingFee', label: '装车费', },
  { width: '100', align: 'center', prop: 'proof1', label: '凭证1', type: "images", },
  { width: '100', align: 'center', prop: 'proof2', label: '凭证2', type: "images", },
  { width: '100', align: 'center', prop: 'proof3', label: '凭证3', type: "images", },
  { width: '100', align: 'center', prop: 'proof4', label: '凭证4', type: "images", },
]
const tableCols1 = [
  { istrue: true, width: '40', type: "checkbox" },
  { sortable: 'custom', width: '100', align: 'center', prop: 'status', label: '状态', },
  { sortable: 'custom', width: '150', align: 'center', prop: 'businessId', label: '流程编号', },
  { sortable: 'custom', width: '150', align: 'center', prop: 'batchNo', label: '批次号', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'fsYmdDate', label: '发生日期', },
  { width: '100', align: 'center', prop: 'regionCost', label: '区域费用', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'warehousingNo', label: '入库单号', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'buyNo1', label: '采购单号1', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'buyNo2', label: '采购单号2', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'brandName', label: '采购', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'warehouseName', label: '仓库', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'totalPerTicket', label: '单票合计', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'orderFee1', label: '单号1费用', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'orderFee2', label: '单号2费用', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'haulage', label: '托运费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'deliveryFee', label: '送货费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'pickUpFee', label: '提货费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'huoLaLa', label: '货拉拉', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'loadingFee', label: '装车费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'remark', label: '备注', },
  { width: '100', align: 'center', prop: 'proof1', label: '凭证1', type: "images", },
  { width: '100', align: 'center', prop: 'proof2', label: '凭证2', type: "images", },
  { width: '100', align: 'center', prop: 'proof3', label: '凭证3', type: "images", },
  { width: '100', align: 'center', prop: 'proof4', label: '凭证4', type: "images", },
  { sortable: 'custom', width: '100', align: 'center', prop: 'createdUserName', label: '添加人', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'reviewerName', label: '初审人', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'auditRemarks', label: '初审备注', },
]
export default {
  name: "examineAndApprove",
  props: {
    approvalParams: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      checkboxData: [],
      addInfo: {
        visible: false,
        tableData: [],
        total: 0,
        loading: false,
        summaryarry: {},
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
      },
      ruleForm: {
        deptName: '',
        applyReason: '',
        useWare: [],
        usePlatform: [],
        expenseType: '采购物流费',
        totalPrice: '',
        totalPriceCN: '',
        payment: '',
        accountName: '',
        accountNumber: '',
        openingBank: '',
        lastPaymentDate: '',
      },
      rules: {
        processCode: [
          { required: true, message: '请选择流程', trigger: 'change' }
        ],
        deptName: [
          { required: true, message: '请输入所在部门', trigger: 'blur' }
        ],
        applyReason: [
          { required: true, message: '请选择申请事由', trigger: 'change' }
        ],
        useWare: [
          { required: true, message: '请选择使用部门', trigger: 'blur' }
        ],
        usePlatform: [
          { required: true, message: '请选择使用平台', trigger: 'blur' }
        ],
        payment: [
          { required: true, message: '请选择付款方式', trigger: 'change' }
        ],
        accountName: [
          { required: true, message: '请输入账户名', trigger: 'blur' }
        ],
        accountNumber: [
          { required: true, message: '请输入账号', trigger: 'blur' }
        ],
        openingBank: [
          { required: true, message: '请输入开户行', trigger: 'blur' }
        ],
        lastPaymentDate: [
          { required: true, message: '请选择最晚支付日期', trigger: 'change' }
        ],
        checkUnit: [
          { required: true, message: '请选择核算单位', trigger: 'change' }
        ],
      },
      that: this,
      tableData: [],
      summaryarry: {},
      tableCols,
      tableCols1,
      total: 0,
      ListInfo: {
        currentPage: 1,
        pageSize: 500,
        startTime: '',
        endTime: '',
        orderBy: '',
        isAsc: true,
        ids: [],
      },
      loading: false,
      submitLoading: false,
      warehouselist: []
    }
  },
  async mounted() {
    Object.assign(this.ListInfo, {
      startTime: this.approvalParams.startTime,
      endTime: this.approvalParams.endTime,
      orderBy: this.approvalParams.orderBy,
      isAsc: this.approvalParams.isAsc,
      ids: this.approvalParams.ids,
    });

    var res3 = await getAllWarehouse();
    this.warehouselist = res3.data;

    await this.getList();
  },
  methods: {
    processChange(e) {
      if (e == 'PROC-A179EC32-4E24-4B5D-9EAB-4670CB4CC460 ') {
        this.ruleForm.useWare = []
      }
    },
    // 计算总金额
    calculateTotal() {
      let total = 0;
      for (let i = 0; i < this.tableData.length; i++) {
        const amount = parseFloat(this.tableData[i].totalPerTicket) || 0;
        total = decimal(total, amount, 4, '+');
      }
      // 去除末尾无意义的0，保留有效小数位
      const formattedTotal = removeTrailingZeros(total.toFixed(4));
      this.ruleForm.totalPrice = formattedTotal;
      // 转换为大写
      this.ruleForm.totalPriceCN = numberToChinese(parseFloat(formattedTotal));
    },
    async onAddSubmit() {
      if (!this.checkboxData.length) {
        this.$message.error('请至少选择一条数据')
        return;
      }
      this.addInfo.loading = true
      // 获取当前主表格中已存在的id集合
      const existingIds = new Set(this.tableData.map(item => item.id));
      // 过滤掉已存在的数据，只添加新的数据
      const newData = this.checkboxData.filter(item => !existingIds.has(item.id));
      // 将新数据添加到主表格
      const updatedTableData = [...this.tableData, ...newData];
      // 使用$set重新渲染表格数据
      this.$set(this, 'tableData', updatedTableData);
      // 计算总金额
      this.calculateTotal();
      // 等待 loading 完成
      await new Promise(resolve => {
        setTimeout(() => {
          this.$message.success('添加成功,已自动过滤掉重复数据')
          this.addInfo.visible = false;
          this.addInfo.loading = false
          resolve()
        }, 1000);
      });
    },
    selectchange(val) {
      this.checkboxData = val;
    },
    SizechangeAdd(val) {
      this.addInfo.currentPage = 1;
      this.addInfo.pageSize = val;
      this.addRow()
    },
    PagechangeAdd(val) {
      this.addInfo.currentPage = val;
      this.addRow()
    },
    sortchangeAdd({ order, prop }) {
      if (prop) {
        this.addInfo.orderBy = prop
        this.addInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.addRow()
      }
    },
    async addRow() {
      this.addInfo.loading = true
      this.loading = true
      const { data, success } = await getPurchaseCostVerifyWarehousePage({ status: '财务初审通过', currentPage: this.addInfo.currentPage, pageSize: this.addInfo.pageSize, orderBy: this.addInfo.orderBy, isAsc: this.addInfo.isAsc })
      this.addInfo.loading = false
      this.loading = false
      if (!success) return
      this.checkboxData = [];
      this.addInfo.tableData = data.list
      this.addInfo.tableData.forEach(item => {
        item.fsYmdDate = dayjs(item.fsYmdDate).format('YYYY-MM-DD');
        const processProof = (proof) => {
          try {
            const proofArray = JSON.parse(proof);
            if (Array.isArray(proofArray)) {
              return proofArray.map(proofItem => ({ url: proofItem.url }));
            }
          } catch (error) {
            console.error('Invalid JSON in proof:', proof);
          }
          return [];
        };
        item.Url = JSON.stringify([
          ...processProof(item.proof1),
          ...processProof(item.proof2),
          ...processProof(item.proof3),
          ...processProof(item.proof4)
        ]);
      });
      this.addInfo.total = data.total
      let summary = data.summary || {};
      Object.entries(summary).forEach(([key, value]) => {
        if (typeof value !== 'string') {
          summary[key] = String(value);
        }
      });
      this.addInfo.summaryarry = summary
      this.addInfo.visible = true;
    },
    handleDelete(i) {
      this.tableData.splice(i, 1);
      // 删除后重新计算总金额
      this.calculateTotal();
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          if (!this.tableData.length) {
            this.$message.error('请添加数据')
            return
          }
          this.submitLoading = true
          try {
            const params = { ...this.ruleForm, ids: this.tableData.map(item => item.id) }
            params.usePlatform = params.usePlatform?.length ? params.usePlatform.join(',') : ''
            params.useWare = params.useWare?.length ? params.useWare.join(',') : ''
            const { data, success } = await sendPurchaseCostVerifyApply(params)
            if (!success) return
            this.$message.success('发起审批成功')
            // this.$emit('close')
            this.$emit('successClose');
          } finally {
            this.submitLoading = false
          }
        } else {
          return false;
        }
      });
    },
    //重置关闭表单
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.$emit('close')
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getPurchaseCostVerifyApplyData(this.ListInfo)
      if (success) {
        const { totalPrice, totalPriceCN, deptName } = data;
        Object.assign(this.ruleForm, { totalPrice, totalPriceCN, deptName });
      } else {
        this.$message.error('获取表单失败')
      }
      const { data: data1, success: success1 } = await getPurchaseCostVerifyApplyTableData(this.ListInfo)
      this.loading = false
      if (success1) {
        this.tableData = data1.list
        this.tableData.forEach(item => {
          const processProof = (proof) => {
            try {
              const proofArray = JSON.parse(proof);
              if (Array.isArray(proofArray)) {
                return proofArray.map(proofItem => ({
                  name: proofItem.name || "",  // 保留name字段，如果不存在则设为空字符串
                  url: proofItem.url
                }));
              }
            } catch (error) {
              console.error('Invalid JSON in proof:', proof);
            }
            return [];
          };
          item.proof1 = JSON.stringify(processProof(item.proof1));
          item.proof2 = JSON.stringify(processProof(item.proof2));
          item.proof3 = JSON.stringify(processProof(item.proof3));
          item.proof4 = JSON.stringify(processProof(item.proof4));
        });
        this.total = data1.total
        this.summaryarry = data1.summary
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
}

.publicCss {
  width: 260px;
}

::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>
