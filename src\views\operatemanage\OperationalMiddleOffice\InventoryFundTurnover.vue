<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="filter.styleCode" placeholder="系列编码" style="width: 180px;" clearable >
        </el-input>
        <el-input v-model="filter.goodsCode" placeholder="商品编码" style="width: 180px;;" clearable >
        </el-input>
        <el-select v-model="filter.groupIdList" placeholder="请选择运营组" style="width:180px;" clearable multiple filterable collapse-tags >
          <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key">
          </el-option>
        </el-select>
        <el-input-number v-model="filter.minFundTurnoverDays" auto-complete="off" placeholder="最小资金周转天数" style="width: 180px;" clearable :controls="false" :precision="0" />
        <el-input-number v-model="filter.maxFundTurnoverDays" auto-complete="off" placeholder="最大资金周转天数" style="width: 180px;" clearable :controls="false" :precision="0" />
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onExport">导出</el-button>
      </div>
    </template>

    <vxetablebase ref="table" :id="'InventoryFundTurnover202506031148'"
      :tablekey="'InventoryFundTurnover202506031148'" :loading="loading" :that="that" 
      :tablefixed="true" :table-data="data.list" :table-cols="tableCols" :tableHandles="tableHandles" 
      style="width: 100%; margin: 0;" height="100%" :showsummary="data.summary ? true : false"
      :summaryarry="data.summary" @sortchange="sortchange" :treeProp="{ childrenField: 'children' }" >
    </vxetablebase>

    <template #footer>
      <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { getInventoryFundTurnover, exportInventoryFundTurnover } from "@/api/operatemanage/OperationalMiddleOfficeManage/InventoryFundTurnover";
import { getGroupKeyValue } from '@/api/operatemanage/base/product';

const tableCols = [
  { sortable: 'custom', istrue: true, width: '180', align: 'center', label: '系列编码', prop: 'styleCode', treeNode: true },
  { istrue: true, width: '170', align: 'center', label: '商品编码', prop: 'goodsCode' },
  { istrue: true, width: '170', align: 'center', label: '运营组', prop: 'groupName' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '库存', prop: 'fullWarehouseStock' },
  { istrue: true, width: '120', align: 'center', label: '商品成本', prop: 'costPrice' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '在途数', prop: 'inTransitNum' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '昨日销量', prop: 'yesterdaySales', permission: 'api:OperateManage:OperationalMiddleOfficeManage:InventoryFundTurnover:GetInventoryFundTurnover' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '昨日销量成本', prop: 'yesterdaySaleCost', permission: 'api:OperateManage:OperationalMiddleOfficeManage:InventoryFundTurnover:GetInventoryFundTurnover' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '资金周转天数', prop: 'fundTurnoverDays' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '销售周转天数', prop: 'saleTurnoverDays' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '库存资金', prop: 'stockFund' },
];
const tableHandles = [
  { label: "一键展开", handle: (that) => that.expendAllprop(true) },
  { label: "一键收缩", handle: (that) => that.expendAllprop(false) }
];


export default { 
  name: "InventoryFundTurnover",
  components: { MyContainer, vxetablebase },
  data() {
    return {
      tableHandles,
      that: this,
      loading: false,
      tableCols: tableCols,
      filter: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'fullWarehouseStock',
        isAsc: false,
        //过滤条件
        styleCode: '',
        goodsCode: '',
        groupIdList: [],
        minFundTurnoverDays: undefined,
        maxFundTurnoverDays: undefined,
      },
      data: {},
      groupList: [],
    };
  },
  async mounted() {
    this.init();
  },
  methods: {
    async init() {
      await this.setGroupSelect();
      await this.getList();
    },
    async setGroupSelect() {
      const res = await getGroupKeyValue({});
      this.groupList = res.data;
    },
    async getList() {
      this.loading = true;
      try {
        const { data, success } = await getInventoryFundTurnover(this.filter);
        if (success) {
          this.data = data;
        } else {
          // this.$message.error("获取列表失败");
        }
      } catch (error) {
        // this.$message.error("获取列表失败");
      } finally {
        this.loading = false;
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.filter.currentPage = 1;
      this.filter.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.filter.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.filter.orderBy = prop
        this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    onSearch() {
      //点击查询时才将页数重置为1
      this.filter.currentPage = 1;
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async onExport() {
      this.loading = true;
      const res = await exportInventoryFundTurnover(this.filter);
      this.loading = false;
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute("download", '库存资金周转导出_' + new Date().toLocaleString() + '.xlsx');
      aLink.click();
    },
    // 一键展开、一键收缩
    expendAllprop(val) {
      if (val) {
          this.$refs.table.expandAllTree(true);
      } else {
          this.$refs.table.expandAllTree(false);
      }
    },

  },

};

</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 5px;

  .publicCss {
    width: 115px;
    margin: 0 5px 0 0;
  }
}

::v-deep .el-select__tags-text {
  max-width: 50px;
}
</style>
