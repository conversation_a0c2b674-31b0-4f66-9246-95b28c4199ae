<template>
    <MyContainer>
      <template #header>
        <div class="top">
          <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
            :value-format="'yyyy-MM-dd'" @change="changeTime" :clearable="false">
          </el-date-picker>
          <div>
            <queryCondition ref="refqueryCondition" :valueChanged.sync="topfilter" />
          </div>
          <div style="display: flex;align-items: center;padding-top: 1px;">
            <el-checkbox v-model="ListInfo.noUseCatch" class="publicCss" style="width: 60px;">非缓存</el-checkbox>
          </div>
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" @click="exportProps">导出</el-button>
        </div>
      </template>
      <vxetablebase :id="'courierCompaniesCollect202411181007'" :tablekey="'courierCompaniesCollect202411181007'"
        ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
        :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
        :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
        :height="'100%'">
        <template #addFee="{ row }">
          <div v-if="row.verify">
            <el-input-number v-model.trim="row.addFee" placeholder="请输入" size="small" :max="9999999" :precision="2"
              :controls="false" style="width: 100%;" />
          </div>
          <div v-else>
            {{ parseFloat(row.addFee).toFixed(2) }}
          </div>
        </template>
        <!-- <template slot="right">
          <vxe-column title="操作" width="100" fixed="right">
            <template #default="{ row, $index }">
              <div style="display: flex;justify-content: center;align-items: center;">
                <div v-if="!row.verify">
                  <el-button type="text" @click="editProduct(row, $index)">编辑</el-button>
                </div>
                <div v-else>
                  <el-button type="text" @click="saveRowEvent(row, 1)">保存</el-button>
                  <el-button type="text" @click="saveRowEvent(row, 0)">取消</el-button>
                </div>
              </div>
            </template>
          </vxe-column>
        </template> -->
      </vxetablebase>
      <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template>
    </MyContainer>
  </template>
  
  <script>
  import MyContainer from "@/components/my-container";
  import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
  import queryCondition from '@/views/express/dailyCourierFee/components/queryCondition'
  import { getXiAnWarehouseArea_MonthBill_NoAddFeeList as getWarehouseArea_MonthBill_NoAddFeeList, addOrEditMonthBillExpressAddFee, exportXiAnExpress_MonthBillList   as exportExpressInfoData_Month } from "@/api/express/express";
  import { pickerOptions } from '@/utils/tools'
  import dayjs from 'dayjs'
  import decimal from '@/utils/decimal'
  const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'expressCompanyFullName', label: '快递公司', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderWaybill', label: '面单费', formatter: (row) => !row.orderWaybill ? " " : row.orderWaybill+"/单"},
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'vote', label: '快递单数', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'sumWeight', label: '总重量（KG）', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'additionalWeightFee', label: '续重费', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'waybill', label: '面单费', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalFreight', label: '运费合计', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'meanValue', label: '运费均值', tipmesg: '运费合计/快递单数', },
    {
    istrue: true, summaryEvent: true, rop: '', label: `0-0.5KG`, merge: true, prop: '', width: '70',
    cols: [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'quantity0', label: '数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight0', label: '重量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'amount0', label: '费用', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'meanValue0', label: '占比', tipmesg: '0-0.5KG数量/快递单数', formatter: (row) => !row.meanValue0 ? " " : row.meanValue0.toFixed(2) + '%' },
    ]},
    {
    istrue: true, summaryEvent: true, rop: '', label: `0.5-1KG`, merge: true, prop: '', width: '70',
    cols: [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'quantity00', label: '数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight00', label: '重量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'amount00', label: '费用', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'meanValue00', label: '占比', tipmesg: '0.5-1KG数量/快递单数', formatter: (row) => !row.meanValue00 ? " " : row.meanValue00.toFixed(2) + '%' },
    ]},
    {
    istrue: true, summaryEvent: true, rop: '', label: `1-2KG`, merge: true, prop: '', width: '70',
    cols: [
      { sortable: 'custom', width: 'auto', align: 'center', prop: 'quantity2', label: '数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight2', label: '重量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'amount2', label: '费用', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'meanValue2', label: '占比', tipmesg: '1-2KG数量/快递单数', formatter: (row) => !row.meanValue2 ? " " : row.meanValue2.toFixed(2) + '%' },
    ]},{
    istrue: true, summaryEvent: true, rop: '', label: `2-3KG`, merge: true, prop: '', width: '70',
    cols: [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'quantity23', label: '数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight23', label: '重量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'amount23', label: '费用', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'meanValue23', label: '占比', tipmesg: '2-3KG数量/快递单数', formatter: (row) => !row.meanValue23 ? " " : row.meanValue23.toFixed(2)+ '%'  },
    ]},
  ]
  export default {
    name: "courierCompaniesCollectNoYiWuCang",
    components: {
      MyContainer, vxetablebase, queryCondition
    },
    data() {
      return {
        topfilter: {
          expressCompanyId: null,
          prosimstateId: null,
          warehouseId: null,
        },
        that: this,
        ListInfo: {
          currentPage: 1,
          pageSize: 50,
          orderBy: null,
          isAsc: false,
          startTime: null,//开始时间
          endTime: null,//结束时间
          dataType: 10,
          noUseCatch: false,//非缓存
          noYiWuCang:true
        },
        timeRanges: [],
        tableCols,
        tableData: [],
        summaryarry: {},
        total: 0,
        loading: false,
        pickerOptions,
      }
    },
    async mounted() {
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      // await this.getList()
      this.$nextTick(() => {
        this.$refs.refqueryCondition.init()
      })
    },
    methods: {
      async saveRowEvent(row, type) {
        this.$nextTick(async () => {
          if (type == 1) {
            row.addFeeSave = row.addFee;
            row.noAddFeeAmount = decimal(row.totalFreight, row.addFee, 2, '-');
            row.noAddFeeAvg = decimal(row.noAddFeeAmount, row.noAddFeeCount, 2, '/');
            const { data, success } = await addOrEditMonthBillExpressAddFee({ ...row });
            if (!success) return;
            row.verify = false;
            this.$message.success('保存成功');
            this.getList();
          } else {
            row.addFee = row.addFeeSave;
            row.verify = false;
          }
        })
        this.$forceUpdate()
      },
      editProduct(row) {
        let index = this.tableData.findIndex(item => item === row)
        this.$nextTick(async () => {
          this.tableData.findIndex((item, i) => {
            if (i == index) {
              item.verify = true
            } else {
              item.verify = false
            }
          })
        })
        this.$forceUpdate()
      },
      async changeTime(e) {
        this.ListInfo.startTime = e ? e[0] : null
        this.ListInfo.endTime = e ? e[1] : null
      },
      async getList(type) {
        if (type == 'search') {
          this.ListInfo.currentPage = 1
          this.$refs.pager.setPage(1)
        }
        this.loading = true
        const { data, success } = await getWarehouseArea_MonthBill_NoAddFeeList({ ...this.ListInfo, ...this.topfilter })
        if (success) {
          this.tableData = data.list
          this.tableData.forEach(item => {
            item.verify = false
            item.addFeeSave = item.addFee
            // item.noAddFeeAmount = decimal(item.totalFreight, item.addFee, 2, '-')
            // item.noAddFeeAvg = decimal(item.noAddFeeAmount, item.noAddFeeCount, 2, '/')
          })
          this.total = data.total
          this.summaryarry = data.summary
          this.loading = false
        } else {
          this.$message.error('获取列表失败')
        }
      },
          //导出
          async exportProps() {
        this.loading = true
        const res = await exportExpressInfoData_Month({...this.ListInfo,...this.topfilter})
        this.loading = false
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '快递公司汇总导出' + new Date().toLocaleString() + '.xlsx')
        aLink.click()
      },
      //每页数量改变
      Sizechange(val) {
        this.ListInfo.currentPage = 1;
        this.ListInfo.pageSize = val;
        this.getList()
      },
      //当前页改变
      Pagechange(val) {
        this.ListInfo.currentPage = val;
        this.getList()
      },
      sortchange({ order, prop }) {
        if (prop) {
          this.ListInfo.orderBy = prop
          this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
    }
  }
  </script>
  
  <style scoped lang="scss">
  .top {
    display: flex;
    margin-bottom: 10px;
  
    .publicCss {
      width: 150px;
      margin-right: 5px;
    }
  }
  </style>
  