<template>
  <container>
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="添加时间">
          <el-date-picker style="width: 230px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
            :picker-options="pickerOptions"></el-date-picker>
        </el-form-item>
        <el-form-item label="运营组">
          <el-select filterable v-model="filter.groupId" placeholder="运营组" style="width: 100px" clearable>
            <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item>
        <el-form-item label="运营">
          <el-select filterable v-model="filter.directorId" placeholder="运营" clearable style="width: 100px">
            <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item>
        <el-form-item label="竞品ID">
          <el-input v-model="filter.goodsID" v-model.trim="filter.goodsID" placeholder="竞品ID" style="width: 150px"
            maxlength="50" clearable></el-input>
        </el-form-item>
        <el-form-item label="宝贝ID">
          <el-input v-model="filter.productCode" v-model.trim="filter.productCode" placeholder="宝贝ID" style="width: 150px"
            maxlength="50" clearable></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select filterable v-model="filter.status" placeholder="状态" clearable style="width: 100px">
            <el-option label="待获取" value="待获取" /> 
            <el-option label="未上架" value="未上架" />
            <el-option label="上架中" value="上架中" />
            <el-option label="上架成功" value="上架成功" />
            <el-option label="上架失败" value="上架失败" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>

    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='list'
      :tableCols='tableCols' :isSelection="false" :tableHandles='tableHandles' :loading="listLoading"
      :summaryarry="summaryarry">
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <!-- 编辑 -->
    <el-dialog :title="dialog.title" :visible.sync="editVisible" width="60%" v-dialogDrag top="5vh">
      <edit ref="pddEdit" @onSave="onSave"></edit>
      <!-- <template slot="footer">
      <el-row>
        <el-col :span="24" style="text-align:right;padding-top:10px;">
          <el-button @click="editVisible=false">取 消</el-button>
          <el-button type="primary" @click="onSave()" :loading="saveLoading">{{ '保存&关闭' }}</el-button>
        </el-col>
      </el-row>
    </template> -->
    </el-dialog>

    <!-- 新增 -->
    <el-dialog title="新增竞品" :visible.sync="addDialog.visible" width="25%" v-dialogDrag>
      <el-form ref="addForm" :model="addDialog.data" label-width="100px"
        style="margin-top: 10px; height: 50px; margin-bottom: -40px;">
        <el-form-item label="竞品id" prop="goodsID" :rules="[{ required: true, message: '竞品id', trigger: 'blur' }]">
          <el-input maxlength="50" clearable style="width: 300px;" v-model="addDialog.data.goodsID" placeholder="竞品id" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click.native="addDialog.visible = false">取消</el-button>
          <my-confirm-button type="submit" :loading="addDialog.addLoading" :validate="addFormValidate"
            @click="onAddSubmit" />
        </div>
      </template>
    </el-dialog>

  </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue"; 
import { addDistributionOnlineProduct,getDistributionOnlineProductList,onlineDistributionProduct } from "@/api/operatemanage/distributiongoodsOnline.js"
import { getDirectorList, getDirectorGroupList } from '@/api/operatemanage/base/shop'
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { formatLinkProCode } from "@/utils/tools";
import edit from "./edit.vue"


let getCountHtml = function(status,downloadCount){ 
  if(status == '待获取'){
    return "<span></span>";
  }else{
    return "<span style='color:"+(downloadCount>0?"red":"green")+"'>"+(downloadCount>0?'下载中('+downloadCount+')':'已完成')+"</span>"
  }
}

const tableCols = [
  { istrue: true, prop: 'goodsID', label: '竞品Id', tipmesg: '', type:'html',  width: '120', sortable: 'custom',formatter: (row) => formatLinkProCode(2, row.goodsID)},
  { istrue: true, prop: 'status', label: '状态', tipmesg: '', width: '80', sortable: 'custom', },
  { istrue: true, prop: 'productCategory', label: '类目', tipmesg: '', sortable: 'custom',formatter:(row)=>row.productCategoryName },
  { istrue: true, prop: 'productName', label: '商品名称', tipmesg: '', sortable: 'custom', },
  { istrue: true, prop: 'viewImageDownLoadCount', label: '竞品信息下载', tipmesg: '', width: '80', sortable: 'custom', 
  type:'html',formatter: (row) => getCountHtml(row.status,row.viewImageDownLoadCount)
  },
  { istrue: true, prop: 'yellowLabel', label: '营销方案', tipmesg: '', width: '120', sortable: 'custom',formatter:(row)=>row.yellowLabelList },
  { istrue: true, prop: 'createdTime', label: '添加时间', tipmesg: '', width: '160', sortable: 'custom', formatter: (row) => (row.createdTime == null ? "" : formatTime(row.createdTime, "YYYY-MM-DD HH:mm:ss")) },
  { istrue: true, prop: 'rpaSyncTime', label: '获取时间', tipmesg: '', width: '160', sortable: 'custom',formatter: (row) => (row.rpaSyncTime == null ? "" : formatTime(row.rpaSyncTime, "YYYY-MM-DD HH:mm:ss")) },
  { istrue: true, prop: 'groupId', label: '运营组', tipmesg: '', width: '100', sortable: 'custom',formatter:(row)=>row.groupName },
  { istrue: true, prop: 'directorId', label: '添加人', tipmesg: '运营', width: '100', sortable: 'custom',formatter:(row)=>row.directorName }, 
  { istrue: true, prop: 'productCode', label: '宝贝ID', tipmesg: '',type:'html',  width: '120', sortable: 'custom',formatter: (row) => formatLinkProCode(2, row.productCode)},
  { istrue: true, prop: 'errorReason', label: '错误原因', tipmesg: '',width: '120', sortable: 'custom', },
  { istrue: true, type: 'button', width: '80', label: '操作', 
  btnList: [
    { label: "编辑", display: (row) => { return (row.status == '待获取' || row.status == '上架中' || row.status == '上架成功'); }, handle: (that, row) => that.showEdit(row.id) },
    { label: "上架", display: (row) => { return (row.isEdited == 0 || row.status == '待获取' || row.status == '上架中' || row.status == '上架成功'); }, handle: (that, row) => that.OnlineProduct(row.id) }
  ] }
]

const tableHandles = [
  { label: "新增竞品", handle: (that) => that.showAdd() }
];

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
  name: 'YunHanDistributionOnlineIndex',
  components: { container, cesTable, MyConfirmButton, edit },
  data() {
    return {
      saveLoading:false,
      editVisible:false,
      that: this,
      filter: {
        startTime: null,
        endTime: null,
        groupId: null,
        directorId: null,
        productCode:null,
        goodsID: null,
        status: null,
        timerange: [startDate, endDate],
      },
      list: [],
      directorList: [],
      directorGroupList: [],
      pager: { OrderBy: "createdTime", IsAsc: false },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      pickOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      sels: [],
      dialog: {
        visible: false,
        title: '编辑'
      },
      addDialog: {
        addLoading: false,
        visible: false,
        data: {
          goodsID: null
        }
      },
      summaryarry: {},
      listLoading: false,

    };
  },

  async mounted() {
    await this.getDirectorlist();
    await this.onSearch();
  },

  methods: {  
    // 保存编辑
    onSave () {
        this.editVisible = false;
        this.onSearch();
    },
    async getDirectorlist () {
      const res1 = await getDirectorList({})
      const res2 = await getDirectorGroupList({}) 

      this.directorList = res1.data
      this.directorGroupList = res2.data 
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    async getlist() {
      if (this.pager.OrderBy == null) {
        this.pager.OrderBy = "createdTime";
        this.pager.IsAsc = false;
      }
      let pager = this.$refs.pager.getPager();
      let page = this.pager;
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      const params = { ...pager, ...page, ... this.filter }
      if (params === false) {
        return;
      }
      this.listLoading = true
      const res = await getDistributionOnlineProductList(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.list = data
      this.summaryarry = res.data.summary;
    },
    async nSearch() {
      await this.getlist()
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      await this.onSearch();
    },
    OnlineProduct(id){
      this.$confirm('是否上架?', '提示', {
        confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
      }).then(async () => {
        let params = { id: id };
        const res = await onlineDistributionProduct(params)
        if (!res?.success) { 
          this.$message({ type: 'error', message: '操作失败！' });
          return
        }
        this.$message({ type: 'success', message: '操作成功，正在上架中！' });
        this.onSearch()
      })
    },
    showEdit(id) {
      this.editVisible = true;
      this.$nextTick(() => {
        this.$refs.pddEdit.getFormData(id);
      })
    },
    showAdd() {
      this.addDialog.visible = true;
      this.addDialog.data.goodID = null;
    },
    addFormValidate: function () {
      let isValid = false
      this.$refs.addForm.validate(valid => {
        isValid = valid
      })
      return isValid
    },
    async onAddSubmit() {
      this.addDialog.addLoading = true
      if (!this.$refs['addForm'].validate()) {
        this.addDialog.addLoading = false;
        return;
      }
      const res = await addDistributionOnlineProduct(this.addDialog.data)
      this.addDialog.addLoading = false
      if (!res?.success) {
        return
      }
      this.$message.success("保存成功！")
      this.$refs['addForm'].resetFields()
      this.addDialog.visible = false
      this.onSearch()
    },
  }
};
</script>

<style lang="scss" scoped></style>