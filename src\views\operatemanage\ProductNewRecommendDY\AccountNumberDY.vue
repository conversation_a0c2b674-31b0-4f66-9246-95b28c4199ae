<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <template #header>
        <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent></el-form>
      </template>
      <!--列表-->
      <ces-table v-if="showtable" ref="table" :that="that" :isIndex="true" :hasexpand="false" @sortchange="sortchange"
        :tableData="pddcontributeinfolist" @select="selectchange" :isSelection="false" :tableCols="tableCols"
        :loading="listLoading" :summaryarry='summaryarry'>
        <el-table-column type="expand">
          <template slot-scope="props">
            <div>
              <el-table :data="props.row.detaildata" style="width: 100%">
                <el-table-column v-for="col in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <template slot="extentbtn">
          <el-button-group>
          
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="Filter.AccountNumberDY" maxlength="300" clearable placeholder="抖音账号" style="width:150px;" />
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="Filter.CreatedUserName" maxlength="300" clearable placeholder="抖音号创建人" style="width:150px;" />
            </el-button>
            
            <el-button type="primary" @click="onSearch">查询</el-button> 
            <el-button type="primary" @click="onstartImport">导入</el-button>
            <el-button type="warning" @click="ClickdownloadTemplate">抖音账号模板</el-button>
            <el-button type="danger" @click="addAccountNumberDY">添加抖音账号</el-button> 
          </el-button-group>
        </template>
      </ces-table>
      <!--分页-->
      <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
      </template>
  
      <el-dialog title="导入" :visible.sync="dialogVisible" width="40%">
        <span>
          <el-row>
            <el-alert title="温馨提示：导入时抖音账号或抖音账号名称选填其一即可（认证过的抖音账号无需填入抖音账号,可填入抖音账号名称）" type="error" :closable="false" style="margin-bottom:10px;">
            </el-alert>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
              <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action accept=".xlsx"
                :http-request="uploadFile" :file-list="fileList" :on-change="uploadChange">
                <template #trigger>
                  <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                  @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
              </el-upload>
            </el-col>
          </el-row>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </el-dialog>
     

      <el-drawer title="添加抖音账号" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="addVisible" 
        direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
        <el-alert title="温馨提示：添加时抖音账号或抖音账号名称选填其一即可（认证过的抖音账号无需填入抖音账号,可填入抖音账号名称）" type="error" :closable="false" style="margin-bottom:10px;">
        </el-alert>
        <form-create style="height: 90px;" :rule="autoform.rule1" v-model="autoform.fApi" :option="autoform.options"/>
        <div class="drawer-footer">
        <el-button @click.native="addVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="editLoading" @click="onaddSubmit" />
        </div>
      </el-drawer>
    </my-container>
  </template>
  <script>
  
  import { getAccountNumberDYList,editAccountNumberDY,changeAccountNumberDYStatus} from '@/api/bookkeeper/reportday'
  import { importAccountNumberDY as importSaleAfterTx } from '@/api/bookkeeper/import'
  import dayjs from "dayjs";
  import cesTable from "@/components/Table/table.vue";
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  import { formatPlatform, formatLinkProCode, formatTime } from "@/utils/tools";
  
  
  export default {
    name: "AccountNumberDY",
    components: {
      MyContainer,
      MyConfirmButton,
      MySearch,
      MySearchWindow,
      cesTable,
    },
    data() {
      return {
       
       
        importfilter: {
          version: ''
        },
        that: this,
        Filter: {
          
          AccountNumberDY: null,
          CreatedUserName: null
        },
        shopList: [],
        addVisible:false,
        styleCode: null,
        editLoading:false,
        userList: [],
        groupList: [],
        pddcontributeinfolist: [],
        tableCols: this.gettableCols(),
        total: 0,
        summaryarry: {},
        pager: { OrderBy: "AccountNumberDY", IsAsc: false },
        sels: [], // 列表选中列
        listLoading: false,
        pageLoading: false,
        selids: [],
        fileList: [],
        dialogVisible: false,
        uploadLoading: false,
        showtable: true,
        autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[]
        },
      };
    },
    // async created() { 
    //   await this.getShopList();
      
    // },  
    mounted() {
        this.initform();
    },
    methods: {
      //下载模板
    ClickdownloadTemplate() {
      window.open("../../static/excel/operateManage/抖音账号模板.xlsx", "_self");
    },
       //添加抖音账号信息
      addAccountNumberDY(){
      this.addVisible=true;

    },

    async onaddSubmit(){
      this.editLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          if(formData.accountNumberDY==''&&formData.numberNameDY=='')
          {
             
            this.$message.error('抖音账号和名称任填一个');
            await this.autoform.fApi.resetFields()
            this.addVisible=false;
            this.getList();
            return;
          }
          
          const res = await editAccountNumberDY(formData);
          await this.autoform.fApi.resetFields()
          console.log("添加抖音账号的数据呀",res)
          if(res.success==true){
            this.$message.success('添加成功！');
            await this.autoform.fApi.resetFields()
            this.getList();
            this.addVisible=false;
          }
         
        }else{}
     })
     this.editLoading=false;
        

     },
     async initform(){
         let that=this;
         this.autoform.rule1= [
                     {type:'input',field:'accountNumberDY',title:'抖音账号',value: '',col:{span:6} ,props: { maxlength:50}
                  } ,  
                  {type:'input',field:'numberNameDY',title:'抖音账号名称',value: '',col:{span:6} ,props: { maxlength:50}
                  } ,                       
                    ]
      },

      gettableCols() {
        return [
        { istrue: true,  prop: 'accountNumberDY', label: '抖音账号', sortable: 'custom', width: '150' },
        { istrue: true,  prop: 'numberNameDY', label: '抖音号名称', sortable: 'custom', width: '150' },
        { istrue: true,  prop: 'createdUserName', label: '抖音号创建人', width: '150' , sortable: 'custom'},
        { istrue: true,  prop: 'createdTime',  label: '导入时间', width: '150' , sortable: 'custom'},
        // {istrue:true,prop:'isUpdate',label:'是否监控', width:'100',sortable:'custom',type:'switch', 
        // change:(row,that)=>that.changeStatus(row)},
        ]
      },
      async changeStatus(row){
      this.$confirm('将改变抖音号的监控状态，是否继续?', '提示', {confirmButtonText: '是',cancelButtonText: '否',type: 'warning'
        }).then(async () => {
            var params={
              accountNumberDY:row.accountNumberDY,
              numberNameDY:row.numberNameDY,
              isUpdate:row.isUpdate,
            };
            const res= await changeAccountNumberDYStatus(params);
            if (!res?.success) return ;
            this.$message({message:"重置成功",type:"success"});
        }).catch(() => {
           row.isUpdate=!row.isUpdate;
        });    
    },
      showClo(){
        return this.Filter.startTime==this.Filter.endTime;
      },
      changeSelectType() { 
        this.getList();
      },
      sortchange(column) {
        if (!column.order) this.pager = {};
        else
          this.pager = {
            OrderBy: column.prop,
            IsAsc: column.order.indexOf("descending") == -1 ? true : false,
          };
        this.onSearch();
      },
      onSearch() {
        this.$refs.pager.setPage(1);
        this.getList();
      },
      async getList() {
        
        const para = { ...this.Filter };
        let pager = this.$refs.pager.getPager();
        const params = {
          ...pager,
          ...this.pager,
          ...para,
        };
        this.listLoading = true;
        const res = await getAccountNumberDYList(params);
        if (!res.success) {
          this.listLoading = false;
          return;
        }
        this.listLoading = false;
        this.total = res.data.total;
        this.pddcontributeinfolist = res.data.list;
        this.summaryarry = res.data.summary;
      },
      selectchange: function (rows, row) {
        this.selids = [];
        rows.forEach((f) => {
          this.selids.push(f.id);
        });
      },
      async onstartImport() {
        this.fileList = [];
        this.uploadLoading = false;
        this.dialogVisible = true;
      },
      submitUpload() {
        this.uploadLoading = true
        this.$refs.upload.submit();
      },
      async uploadFile(item) {
        if (!item || !item.file || !item.file.size) {
          this.$message({ message: "请先上传文件", type: "warning" });
          return false;
        }
        const form = new FormData();
        form.append("upfile", item.file);
        let res = await importSaleAfterTx(form);
        if (res.code == 1) {
          this.dialogVisible = false
          this.$message({ message: "上传成功,正在导入中...", type: "success" });
        }
        this.fileList = []
        this.uploadLoading = false
      },
      async uploadChange(file, fileList) {
        let files = [];
        files.push(file);
        this.fileList = files;
      },
    },
  };
  
  
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
    