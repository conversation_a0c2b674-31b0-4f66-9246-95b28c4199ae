<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" ref="refruleForm" label-width="140px" class="demo-ruleForm">
        <el-form-item label="类型：">
          {{ ruleForm.type }}
        </el-form-item>
        <el-form-item label="仓库：">
          {{ ruleForm.warehouseName }}
        </el-form-item>
        <!-- <el-form-item label="部门类型：">
          {{ ruleForm.deptType }}
        </el-form-item>
        <el-form-item label="部门：">
          {{ ruleForm.deptName }}
        </el-form-item> -->
        <!-- <el-form-item label="二级部门：">
          {{ ruleForm.twoDeptName }}
        </el-form-item>
        <el-form-item label="班次：">
            {{ ruleForm.shiftName }}
        </el-form-item>
        <el-form-item label="劳务公司：">
          {{ ruleForm.aborServiceCompany }}
        </el-form-item> -->
        <div style="font-size: 15px; font-weight: 600; margin-left: 20px">计时-临时工</div>
        <!-- <el-form-item label="工时：">
          <inputNumberYh v-model="ruleForm.temporaryWorkers" :placeholder="'请输入'" class="publicCss" />
        </el-form-item> -->
        <el-form-item label="工时(H)：">
          <inputNumberYh v-model="ruleForm.workHours" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
        <el-form-item label="计时均价：">
          <inputNumberYh v-model="ruleForm.hourWage" :fixed="2" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
        <el-form-item label="计时工费用：">
            {{ computedthree() || ruleForm.hourSalary}}
          <!-- <inputNumberYh v-model="ruleForm.hourSalary" :fixed="2" @blur="computedall" :placeholder="'请输入'" class="publicCss" /> -->
        </el-form-item>

        <div style="font-size: 15px; font-weight: 600; margin-left: 20px">计件-临时工</div>
        <el-form-item label="计件产量：">
          <inputNumberYh v-model="ruleForm.pieceWorkload"  @blur="computedall" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
        <el-form-item label="计件均价：">
          <inputNumberYh v-model="ruleForm.pieceWage" :fixed="2" @blur="computedall" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
        <el-form-item label="计件费用：">
          <inputNumberYh v-model="ruleForm.pieceSalary" @blur="computedall" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>

        <el-form-item label="备注：">
          <el-input v-model.trim="ruleForm.notes" :maxlength="150" placeholder="备注" clearable class="publicCss" />
        </el-form-item>
        <!-- <el-form-item label="计件薪资：">
          {{ computedone() ||  ruleForm.pieceSalary}}
        </el-form-item>
        <el-form-item label="合计成本：">
          {{ compputedtwo() || ruleForm.totalSalary }}
        </el-form-item> -->


        <!-- <el-form-item label="期初人数：">
          <inputNumberYh v-model="ruleForm.initialCount" :placeholder="'期初人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="入职人数：">
          <inputNumberYh v-model="ruleForm.newHiresCount" :placeholder="'入职人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="试用期离职人数：">
          <inputNumberYh v-model="ruleForm.newPeopleResignCount" :placeholder="'试用期离职人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="正式离职人数：">
          <inputNumberYh v-model="ruleForm.oldPeopleResignCount" :placeholder="'正式离职人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="调入人数：">
          <inputNumberYh v-model="ruleForm.transferCount" :placeholder="'调入人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="调出人数：">
          <inputNumberYh v-model="ruleForm.outCount" :placeholder="'调出人数'" class="publicCss" />
        </el-form-item> -->
      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
// import { departmentDimensionSubmit } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission'
import decimal from '@/utils/decimal'

import {
    personAnalysisPage, temporaryLaborCostsSubmit,temporaryLaborCostsListValue
  } from '@/api/people/peoplessc.js';

export default {
  name: 'departmentEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
        sectionList1: [],
      selectProfitrates: [],
      ruleForm: {
        regionName: '',// 区域
        deptName: '',// 部门
        recruitDemandCount: '',// 招聘需求人数
        initialCount: '',// 期初人数
        newHiresCount: '',// 入职人数
        newPeopleResignCount: '',// 试用期离职人数
        oldPeopleResignCount: '',// 正式离职人数
        transferCount: '',// 调入人数
        outCount: '',// 调出人数
      },
    }
  },

  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };

    // this.getxiala('typeList', 'type');
    // this.getxiala('districtList', 'warehouseName');
    // this.getxiala('sectionList', 'twoDeptName');
    this.getxiala('sectionList1', 'shiftName');
    // this.getxiala('sectionList2', 'aborServiceCompany');
  },
  methods: {
    computedall(){
        this.computedone();
        this.compputedtwo();
    },
    computedone(){
        let a = this.ruleForm.pieceWorkload ? this.ruleForm.pieceWorkload :  0;
        let b = this.ruleForm.pieceWage ? this.ruleForm.pieceWage :  0;

        this.ruleForm.pieceSalary = decimal(a, b, 2, '*').toFixed(2);
        return decimal(a, b, 2, '*').toFixed(2);
    },
    compputedtwo(){
        // return (a + b).toFixed(2);
        let a = this.ruleForm.pieceSalary ? this.ruleForm.pieceSalary :  0;
        let b = this.ruleForm.hourSalary ? this.ruleForm.hourSalary :  0;

        let c = decimal(a, b, 2, '+').toFixed(2);

        let d = this.ruleForm.deduction? this.ruleForm.deduction :  0;
        let e = decimal(c, d, 2, '-').toFixed(2);
        this.ruleForm.totalSalary = e;
        return e;
    },
    computedthree(){
        let a = this.ruleForm.hourWage ? this.ruleForm.hourWage :  0;
        let b = this.ruleForm.workHours ? this.ruleForm.workHours :  0;

        this.ruleForm.hourSalary = decimal(a, b, 2, '*').toFixed(2);
        return decimal(a, b, 2, '*').toFixed(2);
    },
    async getxiala(that,val){
    let res = await temporaryLaborCostsListValue({
        'fieldName': val
    })
    if(!res.success){
        return
    }
    this[that] = res.data;
    },
    cancellationMethod() {
      this.$emit('cancellationMethod', 2);
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
            this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing")
          const { data, success } = await temporaryLaborCostsSubmit(this.ruleForm)
          if (!success) {
            return
          }
          this.$emit('cancellationMethod', 1);
          resetForm(formName);
        } else {
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}
</style>
