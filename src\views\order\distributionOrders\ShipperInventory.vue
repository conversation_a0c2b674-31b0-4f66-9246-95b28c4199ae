<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.payTimeStart" :endDate.sync="ListInfo.payTimeEnd"
                    class="publicCss" />
                <inputYunhan ref="goodsCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="proCodeCallback" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <el-select v-model="ListInfo.shipperFxName" placeholder="货主分销" class="publicCss" clearable>
                    <el-option v-for="item in fxUserNames" :label="item" :value="item" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="exportProps">导出</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' border
            :summaryarry='summaryarry' :showsummary='true' @sortchange='sortchange' :tableData='tableData'
            :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0"
            :loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <vxe-modal v-model="dialogVisible" :width="1200" marginSize='-500' :title="title">
            <ShipperInventoryPdDetail :filter="dialogFilter" v-if="dialogVisible" />
        </vxe-modal>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { getShipperFxNameList,GetShipperFxGoodsRptList,exportShipperFxGoodsRptList } from '@/api/order/shipperFxOrder';
import inputYunhan from "@/components/Comm/inputYunhan";
import ShipperInventoryPdDetail from "./ShipperInventoryPdDetail.vue";
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'rptDate', label: '日期', formatter: (row) => row.rptDate ? dayjs(row.rptDate).format('YYYY-MM-DD') : row.rptDate },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shipperFxName', label: '货主分销', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehousingGoodCount', label: '入库数', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shipperFxSaleGoodCount', label: '昀晗代发数' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'saleGoodCount', label: '昀晗销售数' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'pdCount', label: '盘点', type: 'click', tipmesg: '其它出库+其它入库+盘点',  handle: (that, row) => that.showPd(row)},
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'pdCost', label: '金额'},
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'saleReturnGoodsCount', label: '销售退货'},
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'scanCodeReturnGoodsCount', label: '扫码退货'},
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'changeGoodInventoryCount', label: '发生库存',tipmesg: '入库数-昀晗代发数-昀晗销售数+盘点+销售退货+扫码退货' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'balanceInventoryCount', label: '结余库存' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'inventoryGoodCount', label: '库存' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'inventoryMarginCount', label: '库存差',tipmesg: '库存-结余库存' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'inventoryMarginAmount', label: '库存差额' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, inputYunhan,ShipperInventoryPdDetail
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
            },
            timeRanges: [], 
            fxUserNames: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            inventoryVisible: false,
            summaryarry: {},
            title:"盘点明细",
            dialogVisible: false,
            dialogFilter: {
                goodsCode: '',
                startTime: '',
                endTime:'',
                type: '',
            }
        }
    },
    async mounted() {
        await this.getList();
        await getShipperFxNameList()
        .then(({ data }) => {
            this.fxUserNames = data;
        })
    },
    methods: {
        proCodeCallback(val) {
            this.ListInfo.goodsCode = val
        },
        openInventory(row) {
            this.query = {
                goodsCode: row.goodsCode,
            }
            this.inventoryVisible = true
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await exportShipperFxGoodsRptList(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '货主分销-库存' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetShipperFxGoodsRptList(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.summaryarry = data.summary
                    this.total = data.total
                    this.loading = false
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        showPd(row) {
            this.dialogFilter = {
                    goodsCode: row.goodsCode,
                    startTime: row.rptDate,
                    endTime: row.rptDate,
                    type: ''
                };
            this.dialogVisible = true;
        }
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
