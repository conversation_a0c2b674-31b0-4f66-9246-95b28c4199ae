<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <!-- <el-select v-model="filter.zoneName" placeholder="地区搜索" clearable filterable style="width: 160px">
            <el-option :label="item" :value="item" v-for="(item,index) in quyuList" :key="index" />
        </el-select> -->
        <!-- <el-input v-model.trim="filter.zoneName" placeholder="地区搜索" style="width: 160px" :maxlength="50" clearable></el-input> -->
        <el-button type="primary" @click="getList">刷新</el-button>
        <el-button type="primary" @click="handleClick">新增</el-button>
      </div>
    </template>
    <!-- <vxetablebase :isSelectLvl="true" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      style="width: 100%; height: 680px; margin: 0" v-loading="loading" 
      :treeProp="{ transform: true, rowField: 'id', parentField: 'parentId' }">
      <template slot="right">
        <vxe-column title="操作" width="120">
          <template #default="{ row, $index }">
            <div style="display: flex;margin-right: 10px;">
              <div v-if="!row.groupName">
                <el-button class="addsc" @click="addInfo(row)" type="text">新增</el-button>
              </div>
              <el-button class="addsc" @click="oneditsalaryuser(row)" type="text">编辑</el-button>
              <el-button class="addsc" @click="ondeletesalaryuser(row)" type="text">删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase> -->

    <vxetablebase :id="'settingsMenuManage202408041859'"  ref="table" v-if="tableshow"  :somerow="'areaName,deptName,floorName,groupName,gysNames'" :border="true" :that='that' :isIndex='true' :tablefixed='true'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :hasexpand='true' :treeProp="{transform: true, rowField: 'id', parentField: 'parentId', accordion: true}"
      style="width: 100%; height: 100%; margin: 0; " v-loading="loading"
      >
      <template slot="right">
        <vxe-column title="操作" width="120">
          <template #default="{ row, $index }">
            <div style="display: flex;margin-right: 10px;">
              <div v-if="!row.groupName">
                <el-button class="addsc" @click="addInfo(row)" type="text">新增</el-button>
              </div>
              <el-button class="addsc" @click="oneditsalaryuser(row)" type="text">编辑</el-button>
              <el-button class="addsc" @click="ondeletesalaryuser(row)" type="text">删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>



    <!-- <my-pagination ref="pager" @page-change="Pagechange" @size-change="Sizechange" /> -->
    <el-dialog title="提示" :visible.sync="operationLog" width="30%" v-dialogDrag>
      <el-form label-width="120px" :model="ruleForm" :rules="rules" ref="ruleForm">
        <el-form-item label="地区：" prop="zoneName">
          <el-input v-model.trim="ruleForm.zoneName" :disabled="isEdit && ruleForm.floorName?true:false" clearable maxlength="20" />
        </el-form-item>
        <el-form-item label="区域："  prop="areaName" v-if="(ruleForm.zoneName && !isEdit) || (!(isEdit && !ruleForm.zoneName) && conditions)">
          <el-input v-if="isAdd" v-model.trim="ruleForm.areaName" :disabled="ruleForm.parentId=='0'?false:true"  clearable maxlength="20" />
          <el-input v-if="!isAdd" v-model.trim="ruleForm.areaName" :disabled="ruleForm.parentId=='0'"  clearable maxlength="20" />
        </el-form-item>
        <el-form-item label="供应商：" v-if="(ruleForm.areaName && !isEdit) || (!(isEdit && !ruleForm.areaName) && conditions)">
          <el-input v-if="isAdd" v-model.trim="ruleForm.gysNames" :disabled="ruleForm.parentId=='0'?false:true"  clearable maxlength="50" />
          <el-input v-if="!isAdd" v-model.trim="ruleForm.gysNames" :disabled="ruleForm.parentId=='0'"  clearable maxlength="50" />

        </el-form-item>
        <el-form-item label="楼层："
          v-if="(ruleForm.areaName && !isEdit) || (!(isEdit && !ruleForm.floorName) && conditions)">
          <el-input v-model.trim="ruleForm.floorName" :disabled="isEdit && ruleForm.deptName?true:false" clearable maxlength="20" />
        </el-form-item>
        <el-form-item label="部门："
          v-if="(ruleForm.floorName && !isEdit) || (!(isEdit && !ruleForm.deptName) && conditions)">
          <el-input v-model.trim="ruleForm.deptName" :disabled="isEdit && ruleForm.groupName?true:false" clearable maxlength="20" />
        </el-form-item>
        <el-form-item label="组："
          v-if="(ruleForm.deptName && !isEdit) || (!(isEdit && !ruleForm.groupName) && conditions)">
          <el-input v-model.trim="ruleForm.groupName" clearable maxlength="20" />
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="operationLog = false">取 消</el-button>
        <el-button type="primary" @click="sumbit('ruleForm')">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="提示" :visible.sync="addoperationLog" width="30%" v-dialogDrag>
      <el-form label-width="120px" :model="ruleForm" :rules="rules" ref="ruleForm">
        <el-form-item label="地区：" prop="zoneName">
          <el-input v-model.trim="ruleForm.zoneName"  clearable maxlength="20" />
        </el-form-item>
        <el-form-item label="区域：" prop="areaName" v-if="(ruleForm.zoneName && !isEdit) || (!(isEdit && !ruleForm.zoneName) && conditions)">
          <el-input v-model.trim="ruleForm.areaName"   clearable maxlength="20" />
        </el-form-item>
        <el-form-item label="供应商：" v-if="(ruleForm.areaName && !isEdit) || (!(isEdit && !ruleForm.areaName) && conditions)">
          <el-input v-model.trim="ruleForm.gysNames"   clearable maxlength="20" />
        </el-form-item>
        <el-form-item label="楼层："
          v-if="(ruleForm.areaName && !isEdit) || (!(isEdit && !ruleForm.floorName) && conditions)">
          <el-input v-model.trim="ruleForm.floorName"  clearable maxlength="20" />
        </el-form-item>
        <el-form-item label="部门："
          v-if="(ruleForm.floorName && !isEdit) || (!(isEdit && !ruleForm.deptName) && conditions)">
          <el-input v-model.trim="ruleForm.deptName"  clearable maxlength="20" />
        </el-form-item>
        <el-form-item label="组："
          v-if="(ruleForm.deptName && !isEdit) || (!(isEdit && !ruleForm.groupName) && conditions)">
          <el-input v-model.trim="ruleForm.groupName" clearable maxlength="20" />
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="addoperationLog = false">取 消</el-button>
        <el-button type="primary" @click="sumbit('ruleForm')">确 定</el-button>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { getBaseMenuAreaSetInfos, addOrUpdateBaseMenuAreaSetInfo, delBaseMenuAreaSetInfo, getAreaSetList } from '@/api/profit/orderfood';
const tableCols = [
  { istrue: true, treeNode: true, prop: 'zoneName', label: '地区', width: 'auto' },
  { istrue: true, prop: 'areaName', label: '区域', width: 'auto' },
  { istrue: true, prop: 'gysNames', label: '供应商', width: 'auto' },
  { istrue: true, prop: 'floorName', label: '楼层', width: 'auto' },
  { istrue: true, prop: 'deptName', label: '部门', width: 'auto' },
  { istrue: true, prop: 'groupName', label: '组', width: 'auto' },
]
export default {
  name: "scanCodePage",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      rules: {
        zoneName: [
        { required: true, message: '请输入地区名称', trigger: 'blur change' },
        ],
        // areaName: [
        //   { required: true, message: '请输入区域名称', trigger: 'blur change' },
        // ],
      },
      filter: {
        zoneName: ''
      },
      quyuList: [],
      tableshow: true,
      ruleForm: {
        groupName: null,
        floorName: null,
        deptName: null,
        areaName: null,
        gysNames: null,
      },
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      ruleFormarr: [],
      isAdd: true,
      loading: false,
      operationLog: false,
      addoperationLog: false,
      isEdit: false,
      isFloorName: false,
      conditions: false,
      isDeptName: false
    }
  },
  async mounted() {
    // await this.getquyu()
    await this.getList()
    // this.$nextTick(() => {
    //   this.$refs.table.$refs.xTable.setAllTreeExpand(true)
    //   // this.$refs.table.$refs.xTable.setTreeExpand(this.tableData, true)
    // })
  },
  methods: {
    zhankai(){
      let _this = this;
      _this.ruleFormarr.forEach((element) => {
        _this.$refs.table.$refs.xTable.setTreeExpand(element, true)
      });
    },
    clear() {
      this.ruleForm = {
        groupName: null,
        floorName: null,
        deptName: null,
        areaName: null,
        gysNames: null,
      }
    },
    oneditsalaryuser(row) {
      this.ruleFormarr.push(row);

      this.clear()
      this.ruleForm = row
      this.isAdd = false;
      this.isEdit = true
      this.conditions = true
      this.operationLog = true
    },
    ondeletesalaryuser(id) {
      this.$confirm('此操作将永久删除下级数据, 是否确认?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await delBaseMenuAreaSetInfo({ id })
        if (success) {
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
          await this.getList()
        } else {
          this.$message({
            type: 'error',
            message: '删除失败!'
          });
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    addInfo(row) {
      this.clear()
      row.id = 0
      this.ruleForm = row
      this.isAdd = true;
      this.isEdit = false
      this.conditions = false
      this.operationLog = true
    },
    sumbit(formName) {
      let _this = this;
      _this.ruleFormarr = _this.$refs.table.$refs.xTable.getTreeExpandRecords();
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          for (let key in this.ruleForm) {
            if (key == 'floorName' && !this.ruleForm.floorName) {
              this.ruleForm.deptName = null
              this.ruleForm.groupName = null
            }
            if (key == 'deptName' && !this.ruleForm.deptName) {
              this.ruleForm.groupName = null
            }
            if (key == 'areaName' && !this.ruleForm.floorName) {
              this.ruleForm.floorName = null
              this.ruleForm.deptName = null
              this.ruleForm.groupName = null
            }
          }
          const replaceArr = ['zoneName','groupName', 'floorName', 'deptName', 'areaName', 'gysNames']
          this.ruleForm = replaceSpace(replaceArr, this.ruleForm)
          // if (!this.ruleForm.floorName && !this.ruleForm.gysNames) return this.$message.error('请输入供应商');
          const { success } = await addOrUpdateBaseMenuAreaSetInfo(this.ruleForm)
          if (success) {
            this.$message.success('添加成功')
            this.operationLog = false
            this.getList()

            _this.tableshow= false;
            _this.$nextTick(() => {
              _this.tableshow= true;
              setTimeout(()=>{
                _this.zhankai();
              },0)
            })
            
          } else {
            this.$message.error('添加失败')
          }
        } else {
          return this.$message.error('请完善信息');
        }
      });
    },
    handleClick() {
      this.clear()
      this.isEdit = false
      this.isAdd = false;
      this.conditions = false
      // this.operationLog = true
      this.addoperationLog= true;
    },
    async getList() {
      this.loading = true
      // 使用时将下面的方法替换成自己的接口
      const { data, success } = await getBaseMenuAreaSetInfos({zoneName: this.filter.zoneName})
      if (success) {
        this.tableData = data
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
      
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
