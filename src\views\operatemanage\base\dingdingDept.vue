<template>
    <el-table max-height="720px" :indent="10" :show-header="false" :data="deptList" border :default-expand-all="true" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" row-key="id" highlight-current-row style="width: 100%;" @row-click="deptSelect">
        <el-table-column prop="name" />
    </el-table>
</template>
  <script>

    import { getDeptTreeInfo } from '@/api/operatemanage/base/dingdingShow'
    import { listToTree, getTreeParents } from '@/utils'
    export default {
        name: 'DingDingDept',
        props: {
            fatherMethod: {
                type: Function,
                default: null
            }
        },
        components: {},
        data () {
            return {
                deptList: [],
                selectDept: 597959144
            }
        },
        async mounted () {
            await this.initDept();
        },
        methods: {
            async initDept () {
                const data = await getDeptTreeInfo()
                if (data) {
                    this.deptList = listToTree(_.cloneDeep(data))
                }
            },
            deptSelect (row, column) {
                this.selectDept = row.id
                if (this.fatherMethod) {
                    this.fatherMethod();
                }

            }
        }
    }
  </script>
  

<style scoped lang="scss" >
</style>