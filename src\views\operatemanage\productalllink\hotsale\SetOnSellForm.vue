<template>
<!-- 选品上架页面 -->
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form class="ad-form-query" :model="chooseFormData" ref="chooseForm" @submit.native.prevent label-width="100px">  
                        <el-form-item  label="竞品ID" prop="goodsCompeteId" >
                            {{chooseFormData.goodsCompeteId}}
                        </el-form-item>
                        <el-form-item  label="竞品标题" prop="goodsCompeteName" >
                            {{chooseFormData.goodsCompeteName}}
                        </el-form-item>

                        <el-form-item  label="商品ID" prop="proCode" >
                           <el-select v-model="chooseFormData.proCode" collapse-tags filterable clearable placeholder="请选择商品" 
                           :filter-method="getProductList" style="width: 100%" >
                             <el-option v-for="item in productList" :key="item.value" :label="item.label" :value="item.value"/>
                           </el-select>
                        </el-form-item>                     

                    </el-form>
        </template>

        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">  
                    <el-button @click="onClose">取 消</el-button>                                       
                    <el-button  type="primary" @click="onSave(true)">上 架</el-button>
                </el-col>
            </el-row>
        </template>

    </my-container>
</template>
<script>  


    import {  platformlist} from "@/utils/tools";
    
    import MyContainer from "@/components/my-container";

   

    import {
         isDoHotSaleGoodsAsync,GetProductByKeywords,SetOnSell
    } from '@/api/operatemanage/productalllink/alllink'


    export default {
        name: "SetOnSellForm",
        components: { MyContainer },
        data() {
            return {
                platformlist: platformlist,
                that: this,
                chooseFormData: {                 
                    goodsCompeteId: "", 
                    goodsId: "", 
                    goodsName: "", 
                    goodsCompeteName: "",
                    goodsCompeteImgUrl:"",
                    chooseRemark:"",
                    proCode:null
                },
                pageLoading: false,               
                formEditMode: true,//是否编辑模式
                productList:[]
            };
        }, 
        async mounted() {
            let index = this.platformlist.findIndex(item => item.value == 0)
            if(index>=0){
                this.platformlist.splice(index, 1) 
            }
        },
        computed: {
           
        },
        methods: {             
           
            onClose(){
                this.$emit('close');
            },  
            async getProductList(v){ 
                if(v==null || v==""){
                    return;
                }
                this.productList = [];
                let reqRlt = await GetProductByKeywords({kw:v}) 
                if(reqRlt && reqRlt.success){
                    this.productList=reqRlt.data.map(item=>{
                        return { value: item.proCode, label:'【'+item.proCode+'】'+item.shopName+ '【'+item.title+'】' , title:item.title };
                    });
                }       
            },          
            async onSave(isClose){
                if(await this.save()){
                    this.$emit('afterSave');
                    if(isClose)
                        this.$emit('close');
                }
            },
            async loadData(data) {   
                this.chooseFormData={...data};          
            },
            async save() {
                let that=this;
              

                let saveData = {
                    chooseId:that.chooseFormData.chooseId,
                };
                if(that.chooseFormData.proCode  && that.chooseFormData.proCode.length>0){
                    saveData.proCode=that.chooseFormData.proCode ;
                }else{
                    this.$alert("请填写关联上架的商品ID!");
                    return false;
                }

                that.pageLoading = true;
                let reqRlt=await SetOnSell(saveData);
                that.pageLoading = false;
                if(reqRlt && reqRlt.success){
                    that.$message({ message: '上架成功！', type: "success" });
                }               
                return reqRlt && reqRlt.success;    
            }
        },
    };
</script>
