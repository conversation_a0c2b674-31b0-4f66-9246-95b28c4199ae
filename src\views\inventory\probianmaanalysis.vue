<template>
  <div style="height: 100%;">
     <template>
      <el-form class="ad-form-query" :model="filter" @submit.native.prevent label-width="100px">
         <el-row>
            <el-col :xs="24" :sm="10" :md="10" :lg="10" :xl="10">
                 <el-form-item label="商品编码:">
                  <template>
                    <el-tag type="success" v-for="item in probianmalist" :key="item.id"> {{item.proBianMa+':'+item.proBianMaName}}</el-tag>
                  </template>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
                 <div style="text-align:right;">
                    <el-link type="primary" @click="topurchase(probianmalist[0].proBianMa)">历史采购</el-link>
                 </div>
              </el-col>
             <!-- <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
                <el-form-item label="左指标:">
                  <el-select v-model="filter1.leftY" placeholder="请选择" class="el-select-content">
                    <el-option v-for="item in Ylist" :key="item.value" :label="item.label" :value="item.value"/>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
                <el-form-item label="右指标:">
                  <el-select v-model="filter1.rightY" placeholder="请选择" class="el-select-content">
                    <el-option v-for="item in Ylist" :key="item.value" :label="item.label" :value="item.value"/>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="1" :md="1" :lg="1" :xl="1">
                <el-form-item>
                  <el-button type="primary" @click="onfresh">刷新</el-button>
                </el-form-item>
              </el-col> -->
         </el-row>
      </el-form>
      </template> 
       <div id="echartmonitst" style="width: 100%;height: 300px; box-sizing:border-box; line-height: 300px;">
      </div>
       <div id="echartmonitst365" style="width: 100%;height: 300px; box-sizing:border-box; line-height: 300px;">
      </div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
import {getWarehouse,warehouseRecordAnalysis} from '@/api/inventory/warehouse'
export default {
  name: 'Roles',
  components: { },
   props:{
       filter: { 
        startDate: null,
        endDate: null,
        proBianMa:"",
        warehouse:null
        }
     },
  data() {
    return {
      filter1:{
        probianmas:[],
        warehouse:null,
        leftY:0,
        rightY:2
      },
   Ylist:[
          {value:0,unit:"",label:"每日库存数"},
          {value:1,unit:"",label:"期末金额"},
          {value:2,unit:"",label:"销售数量"},
         ],
      probianmalist:[],
      pageLoading: false
    }
  },
  mounted() {
  },
  beforeUpdate() {
  },
  methods: {
    async addDate(date,days){ 
        var d=new Date(date); 
        d.setDate(d.getDate()+days); 
        var m=d.getMonth()+1; 
        return d.getFullYear()+'-'+m+'-'+d.getDate(); 
    },
   async onSearch() {
      if (this.filter1.probianmas.length==0) return;      
      this.getdata()
      this.getdata365()
    },
   async onfresh() {
    if (this.filter1.probianmas.length==0) {
        this.$message({message: "请先选择编码！",type: "warning",});
        return;
      }
     await this.getdata()
     await this.getdata365()
    },
   async onpk(probianmas,warehouse) {
     if (probianmas) {
         this.filter1.probianmas=probianmas.split(",")
         this.filter1.warehouse=warehouse;
        // var res = await getWarehouse({proBianMas: probianmas,warehouse:warehouse});
        // if (!res?.code)  return;
        // this.filter1.probianmas=[]
        // this.probianmalist= res.data;
        // this.probianmalist.forEach(f=>{this.filter1.probianmas.push(f.proBianMa)})
        await this.onfresh()
     }
  },
  async getdata() {
      var parm={...this.filter, ...this.filter1};
      if (parm.probianmas.length==0) return;
      var hasparm=false;
      var arry= Object.keys(parm)
      if (arry.length==0)  return;

      for (let key of Object.keys(parm)) {
        if(parm[key]) hasparm=true;
      }
      if(!hasparm) return;
      console.log(parm)
      parm.StartTime=this.filter.startDate
      parm.endDate=this.filter.endDate      
      const res = await warehouseRecordAnalysis(parm);      
      if (!res?.code)  {
        this.$message({message: "服务错误",type: "warning",});
        return;
      }
      if (!res.data){
        this.$message({message: "没有数据",type: "warning",});
        return;
      }
      var chartDom = document.getElementById('echartmonitst');
      var myChart = echarts.init(chartDom);
      var option = this.Getoptions(res.data);
      //console.log(JSON.stringify(option))
      myChart.clear();
      option && myChart.setOption(option); 
    },
    async getdata365() {
      var parm={...this.filter, ...this.filter1};
      parm.startDate=await this.addDate(parm.endDate,-365);
      if (parm.probianmas.length==0) return;
      var hasparm=false;
      var arry= Object.keys(parm)
      if (arry.length==0)  return;

      for (let key of Object.keys(parm)) {
        if(parm[key])
            hasparm=true;
      }
      if(!hasparm) return;
      console.log(parm)
      parm.StartTime=this.filter.startDate
      parm.endDate=this.filter.endDate      
      const res = await warehouseRecordAnalysis(parm);      
      if (!res?.code)  {
        this.$message({message: "服务错误",type: "warning",});
         return;
      }  
      if (!res.data){
        this.$message({message: "没有数据",type: "warning",});
        return;
      }
      var chartDom = document.getElementById('echartmonitst365');
      var myChart = echarts.init(chartDom);
      var option = this.Getoptions(res.data);
      //console.log(JSON.stringify(option))
      myChart.clear();
      option && myChart.setOption(option); 
    },
   Getoptions(element){
     var series=[]
     var max=0;
     element.series.forEach(s=>{
       if (s.type=='line')  s.type='bar'
       else if (s.type=='bar') s.type='line'
      //  s.yAxisIndex=0;
      //  s.type='line';
       series.push({ smooth: true, ...s})
     })
     var yAxis=[]
     this.Ylist.forEach(s=>{
       if (s.value==this.filter1.leftY)
          yAxis.push({ type: 'value',name: s.label,axisLabel: {formatter: '{value}'+s.unit}})
     })
     this.Ylist.forEach(s=>{
       if (s.value==this.filter1.rightY)
          yAxis.push({ type: 'value',name: s.label,axisLabel: {formatter: '{value}'+s.unit}})
     })
      //var yAxis=[{ type: 'value',name: '数量',axisLabel: {formatter: '{value}'}}]
     var option = {
        title: {text: element.title},
        tooltip: {trigger: 'axis'},
        legend: {
            data: element.legend
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {feature: {
            magicType: {show: true, type: ['line', 'bar']},
            //restore: {show: true},
        }},
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis: yAxis,
        series:  series
    };
    return option;
   },
  async topurchase(goodscodes){
      this.$router.push({path: '/inventory/purchaseindex', query: {goodscodes:goodscodes}})
   }
  }
}
</script>
