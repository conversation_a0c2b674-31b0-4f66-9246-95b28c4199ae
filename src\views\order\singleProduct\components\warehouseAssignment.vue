<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker class="publicCss" v-model="ListInfo.date" align="right" type="date" placeholder="选择日期"
          :clearable="false" value-format="yyyy-MM-dd" :picker-options="pickerOptionsDate" />
        <inputYunhan ref="refgoodsCodes" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes" width="190px"
          placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500" :maxlength="1000000"
          @callback="goodsCodesCallback($event, 1)" title="商品编码" style="margin:0 2px 0 0;">
        </inputYunhan>
        <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable class="publicCss" />
        <inputYunhan ref="refstyleCodes" :inputt.sync="ListInfo.styleCodes" v-model="ListInfo.styleCodes" width="190px"
          placeholder="款式编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500" :maxlength="1000000"
          @callback="goodsCodesCallback($event, 2)" title="款式编码" style="margin:0 2px 0 0;">
        </inputYunhan>
        <chooseWareHouse v-model="ListInfo.wmsIds" class="publicCss" multiple :filter="sendWmsesFilter"
          :placeholder="'请选择指派仓库'" />
        <chooseWareHouse v-model="ListInfo.nowWmsIds" class="publicCss" multiple :filter="sendWmsesFilter"
          :placeholder="'请选择已设置仓库'" :mergeData="mergeData" />
        <el-select v-model="ListInfo.isSame" placeholder="是否一致" class="publicCss" clearable filterable>
          <el-option key="是" label="是" :value="true" />
          <el-option key="否" label="否" :value="false" />
        </el-select>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="ruleSetVisiable = true">设置</el-button>
        <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
        <el-button type="primary" @click="onQuickSettings">一键设置</el-button>
      </div>
    </template>
    <vxetablebase ref="table" :id="'warehouseAssignment202505091530'" :tablekey="'warehouseAssignment202505091530'"
      :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true" :has-seq="false"
      :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false" :is-select-column="true"
      :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%" :showsummary="data.summary ? true : false"
      :summaryarry="data.summary" @sortchange="sortchange" :showTrendChart="false" @select="selectCheckBox" />
    <template #footer>
      <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog :visible.sync="ruleSetVisiable" width="65%" :close-on-click-modal="false" v-dialogDrag append-to-body>
      <wareStorehouseSetting v-if="ruleSetVisiable" />
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode, pickerOptionsDate } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/SaleItems/SgwWms/'
import { mergeTableCols } from '@/utils/getCols'
import wareStorehouseSetting from "./wareStorehouseSetting.vue";
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
export default {
  name: "warehouseAssignment",
  components: {
    MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, wareStorehouseSetting, chooseWareHouse
  },
  data() {
    return {
      api,
      platformlist,
      pickerOptionsDate,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        summarys: [],
        date: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
        goodsCodes: '',
        goodsName: '',
        styleCodes: '',
        nowWmsIds: [],
        isSame: null,
        wmsIds: []
      },
      mergeData: {
        name: '未设置',
        wms_co_id: 0,
      },
      selectList: [],// 复选框选中列表
      data: {},
      chatProp: {
        chatDialog: false, // 趋势图弹窗
        chatTime: null, // 趋势图时间
        chatLoading: true, // 趋势图loading
        data: [], // 趋势图数据
      },
      ruleSetVisiable: false,
      timeRanges: [],
      tableCols: [],
      tableData: [],
      total: 0,
      loading: true,
      pickerOptions,
      isExport: false
    }
  },
  async mounted() {
    await this.getCol();
    await this.getList()
  },
  methods: {
    onQuickSettings() {
      if (this.selectList.length == 0) {
        this.$message.error('请选择数据')
        return
      }
      this.$confirm('此操作将一键设置所选数据发货仓, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const { data, success } = await request.post('/api/verifyOrder/SaleItems/SgwWmsSetting/PageGetData', { currentPage: 1, pageSize: 9999999, orderBy: '', isAsc: false, summarys: [], })
        if (!success) {
          this.$message.error('获取仓库列表失败')
          return
        }
        let settingsData = data.list // 仓库列表
        const matchedResult = this.selectList.map(item => {
          const matchedSetting = settingsData.find(
            s => s.wmsId === item.nowWmsId && s.wmsName === item.nowWmsName
          )
          if (!matchedSetting) {
            this.$message.error(`未找到匹配的仓库信息: ${item.nowWmsId} - ${item.nowWmsName}`);
            return null
          }
          return {
            wmsId: item.wmsId,
            wmsName: item.wmsName,
            goodsCode: item.goodsCode,
            safeDay: 7,
            weightMin: matchedSetting?.weightMin ?? null,
            weightMax: matchedSetting?.weightMax ?? null,
            rate: matchedSetting?.rate ?? null,
            oldWmsId: item.nowWmsId,
            isDelete: false
          }
        })
        this.loading = true
        const { success: success1, msg } = await request.post('/api/verifyOrder/SaleItems/WmsCode/ChangeWms', matchedResult)
        this.loading = false
        if (!success1) {
          this.$message.error(msg || '设置仓库失败')
        } else {
          this.$message.success('设置仓库成功')
          this.getList()
        }
      }).catch(() => {
      })
    },
    selectCheckBox(val) {
      this.selectList = JSON.parse(JSON.stringify(val))
    },
    sendWmsesFilter(wmses) {
      return wmses.filter((a) => a.isSendWarehouse == '是' && a.isWc == 1);
    },
    goodsCodesCallback(e, val) {
      if (val == 1) {
        this.ListInfo.goodsCodes = e
      } else if (val == 2) {
        this.ListInfo.styleCodes = e
      }
    },
    // 导出数据,这里前端可以封装一个方法
    async exportProps() {
      this.isExport = true
      await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
        this.isExport = false
      })
    },
    async getCol() {
      const { data, success } = await request.post(`${this.api}GetColumns`)
      if (success) {
        data.forEach(item => {
          if (item.prop == 'orderNoInner') {
            item.type = 'orderLogInfo'
            item.orderType = 'orderNoInner'
          }
          if (item.prop == 'proCode') {
            item.type = 'html'
            item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
          }
        })
        this.tableCols = mergeTableCols(data)
        this.tableCols.unshift({ label: '', type: 'checkbox' })
        this.ListInfo.summarys = data
          .filter((a) => a.summaryType)
          .map((a) => {
            return { column: a["sort-by"], summaryType: a.summaryType };
          });
      }
    },
    async getList(type) {
      if (type === "search") {
        this.ListInfo.currentPage = 1;
        this.$refs.pager.setPage(1);
      }
      this.loading = true;
      // 使用时将下面的方法替换成自己的接口
      try {
        const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
        if (success) {
          this.data = data;
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.loading = false;
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 5px;
  align-items: center;

  .publicCss {
    width: 190px;
    margin-right: 5px;
  }
}

::v-deep .el-select__tags-text {
  max-width: 50px;
}
</style>
