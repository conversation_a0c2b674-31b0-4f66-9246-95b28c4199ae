<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="ListInfo.yearMonthDay" type="date" placeholder="选择日期" :clearable="false"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd" class="publicCss" style="width: 130px;">
        </el-date-picker>
        <div class="publicCss" style="width: 150px;">
          <inputYunhan ref="productstyleCode" :inputt.sync="ListInfo.styleCode" v-model="ListInfo.styleCode"
            width="150px" placeholder="款式编码/Enter多行输入" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackMethod($event, 'styleCode')" title="款式编码">
          </inputYunhan>
        </div>
        <div class="publicCss" style="width: 150px;">
          <inputYunhan ref="productgoodsCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode"
            width="150px" placeholder="商品编码/Enter多行输入" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackMethod($event, 'goodsCode')" title="商品编码">
          </inputYunhan>
        </div>
        <el-select filterable v-model.trim="ListInfo.deptId" clearable multiple collapse-tags style="width: 150px;"
          placeholder="采购架构" class="publicCss">
          <el-option v-for="item in purchasegrouplist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select filterable v-model.trim="ListInfo.brandId" clearable multiple collapse-tags style="width: 150px;"
          placeholder="采购" class="publicCss">
          <el-option v-for="item in procurementList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <div class="publicCss" style="width: 150px;">
          <inputYunhan ref="productgoodsLabels" :inputt.sync="ListInfo.goodsLabels" v-model="ListInfo.goodsLabels"
            width="150px" placeholder="标签/Enter多行输入" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackMethod($event, 'goodsLabels')" title="标签">
          </inputYunhan>
        </div>
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="编码创建开始日期" end-placeholder="编码创建结束日期" :picker-options="pickerOptions"
          style="width: 230px;margin-right: 2px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-select filterable v-model="ListInfo.platform" placeholder="平台" clearable multiple collapse-tags
          style="width: 150px;" class="publicCss">
          <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select filterable v-model="ListInfo.groupId" placeholder="运营组" class="publicCss" clearable multiple
          collapse-tags style="width: 150px;">
          <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key" />
        </el-select>
        <el-select filterable v-model="ListInfo.continuousNoSales" placeholder="连续多少天无销量" class="publicCss" clearable>
          <el-option v-for="item in consecutiveDays" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <div class="publicCss" style="width: 150px;margin-left: 10px;">
          <label style="color: #606266; display: inline-block; font-size: 14px; margin-right: 5px;">有无库存</label>
          <el-switch v-model="ListInfo.hasStock" active-color="#13ce66" inactive-color="#c0c0c0"></el-switch>
        </div>
      </div>
      <div class="top">
        <el-button type="primary" class="top_button" @click="exportProps">导出</el-button>
        <el-button type="primary" class="top_button" @click="onSettingsMethod">设置</el-button>
        <el-button type="primary" class="top_button" @click="onBrandSetting">品牌设置</el-button>
        <el-button type="primary" class="top_button" @click="getList('search')">搜索</el-button>
      </div>
    </template>
    <vxetablebase :id="'productSalesDetails202505121901'" :tablekey="'productSalesDetails202505121901'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'" :border="true" @summaryClick='onsummaryClick'>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="详情" :visible.sync="chartDisplayvisible" width="65%" v-dialogDrag @opened="initCharts">
      <div class="chart_CSS">
        <div class="chart_top">
          <el-descriptions class="margin-top" :column="2" :size="size" border>
            <el-descriptions-item label="款式编码">
              {{ chartDisplay.styleCode }}
            </el-descriptions-item>
            <el-descriptions-item label="商品编码">
              {{ chartDisplay.goodsCode }}
            </el-descriptions-item>
            <el-descriptions-item label="公有可用数">
              {{ chartDisplay.publicUsableQty }}
            </el-descriptions-item>
            <el-descriptions-item label="库存资金">
              {{ chartDisplay.styleInventoryFunds }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="chart_bottom">
          <div class="chart_item">
            <div element-loading-text="加载中" element-loading-spinner="el-icon-loading">
              <div>
                <div id="shopCharts" :style="thisStyle"></div>
              </div>
            </div>
          </div>
          <div class="chart_item">
            <div element-loading-text="加载中" element-loading-spinner="el-icon-loading">
              <div>
                <div id="groupCharts" :style="thisStyle"></div>
              </div>
            </div>
          </div>
          <div class="chart_item">
            <div element-loading-text="加载中" element-loading-spinner="el-icon-loading">
              <div>
                <div id="groupWeekCharts" :style="thisStyle"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <el-dialog title="设置" :visible.sync="settingsInfo.visible" width="20%" v-dialogDrag>
      <el-form :model="settingsInfo.form" ref="settingsForm" label-width="150px" :rules="settingsrules">
        <el-form-item label="连续多少天无销量" prop="continuousNoSales">
          <el-select filterable v-model="settingsInfo.form.continuousNoSales" placeholder="连续多少天无销量" clearable
            style="width: 100%;">
            <el-option v-for="item in consecutiveDays" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="settingsInfo.visible = false">取消</el-button>
        <el-button type="primary" @click="onSettingsSubmit">确定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="明细" :visible.sync="detailsInfo.visible" width="45%" v-dialogDrag>
      <vxetablebase :id="'productSalesDetailsDetails202505151530'" :tablekey="'productSalesDetailsDetails202505151530'"
        ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :tableData='detailsInfo.data'
        :tableCols='tableColsDetails' :isSelection="false" :isSelectColumn="false" :showsummary='true' :border="true"
        style="width: 100%;  margin: 0" :loading="detailsInfo.loading" :height="'300px'" :isNeedExpend="false">
      </vxetablebase>
      <my-pagination ref="pager" :total="detailsInfo.total" @page-change="onDetailsPagechange"
        @size-change="onDetailsSizechange" />
    </el-dialog>

    <el-dialog title="品牌排除设置" :visible.sync="brandFilter.visible" width="25%" v-dialogDrag :collapse-tags="true">
      <div style="height: 100px;">
        <el-form>
            <el-row>
                <el-col>
                    <el-select style="width:40%" clearable placeholder="请选择采购" v-model.trim="brandFilter.brand"
                        :collapse-tags="true" filterable>
                        <el-option v-for="(item, i) in brandFilter.brandList" :key="'dialogGroupName' + i + 1"
                            :label="item" :value="item" />
                    </el-select>
                </el-col>
            </el-row>
            <el-row>
                <el-col style="margin-top: 10px;">
                    <el-button type="primary" @click="onAddBrandFilter">添加</el-button>
                </el-col>
            </el-row>
            <el-row>
                <el-col>
                    <div style="border: 1px solid #dcdfe6;border-radius: 5px;height: 150px;margin: 10px 0;">
                        <el-scrollbar style="height: 100%;">
                            <el-tag v-for="(item, i) in brandFilter.brandSelecteds" :key="item" closable
                                style="margin: 5px;" @close="handleClose(item, i)">
                                {{ item }}
                            </el-tag>
                        </el-scrollbar>
                    </div>
                </el-col>
            </el-row>
        </el-form>
    </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="brandFilter.visible = false">取消</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { platformlist, pickerOptions } from '@/utils/tools'
import {
  getPurchaseNewPlanTurnDayDeptList,
  getPurchaseNewPlanTurnDayBrandList,
  gtePurchaseNewInventoryDongXiaoDtlPage,
  exportPurchaseNewInventoryDongXiaoDtl,
  getPurchaseNewInventoryDongXiaoPieChart,
  getDongXiaoGoodsPurchaseOrder,
  editPurchaseNewInventoryDongXiaoSet,
  getPurchaseNewInventoryDongXiaoSet,
  getDongXiaoBrandFilterDropdownData,
  getDongXiaoBrandFilterSet,
  saveDongXiaoBrandFilterSet,
  delDongXiaoBrandFilterSet
} from '@/api/inventory/purchaseordernew'
import dayjs from 'dayjs'
import * as echarts from "echarts";
import inputYunhan from "@/components/Comm/inputYunhan";
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import buscharpie from "@/views/order/LossOrderCostStatistics/childPages/piebus.vue";
const consecutiveDays = [
  { label: '3天无销量', value: 'day3Sales' },
  { label: '7天无销量', value: 'day7Sales' },
  { label: '15天无销量', value: 'day15Sales' },
  { label: '30天无销量', value: 'day30Sales' },
  { label: '60天无销量', value: 'day60Sales' },
  { label: '90天无销量', value: 'day90Sales' }
]
const tableCols = [
  { summaryEvent: true, sortable: 'custom', width: '150', align: 'center', prop: 'styleCode', label: '款式编码', type: 'click', handle: (that, row) => that.onCoding(row, 1) },
  { width: '50', align: 'left', prop: 'picture', label: '图片', type: "images", },
  { summaryEvent: true, sortable: 'custom', width: '100', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: '240', align: 'center', prop: 'goodsName', label: '商品名称', },
  { sortable: 'custom', width: '240', align: 'center', prop: 'goodsLabels', label: '标签', },
  { sortable: 'custom', width: '120', align: 'center', prop: 'goodsCreatedTime', label: '编码创建日期', },
  { sortable: 'custom', width: '240', align: 'center', prop: 'deptName', label: '采购架构', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'brandName', label: '采购', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'publicUsableQty', label: '公有可用数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'cost', label: '成本价', },
  { summaryEvent: true, sortable: 'custom', width: '100', align: 'center', prop: 'goodsInventoryFunds', label: '商品库存资金', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'styleInventoryFunds', label: '款式库存资金', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'platformStr', label: '平台', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'groupName', label: '运营组', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'day3Sales', label: '3天销量', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'day7Sales', label: '7天销量', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'day15Sales', label: '15天销量', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'day30Sales', label: '30天销量', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'day60Sales', label: '60天销量', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'day90Sales', label: '90天销量', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'buyNoCount', label: '合计已确认采购单', type: 'click', handle: (that, row) => that.onDetails(row), },
  { sortable: 'custom', width: '210', align: 'center', prop: 'lastCheckDate', label: '最后一次确认采购单日期', },
]

const tableColsDetails = [
  { width: 'auto', align: 'center', prop: 'buyNo', label: '采购单号', },
  { width: 'auto', align: 'center', prop: 'purchaseDate', label: '采购日期', },
  { width: 'auto', align: 'center', prop: 'checkDate', label: '确认日期', },
]
export default {
  name: "productSalesDetails",
  components: {
    MyContainer, vxetablebase, inputYunhan, buscharpie
  },
  data() {
    return {
      chartDisplayvisible: false,
      thisStyle: {
        width: '100%', height: '270px', 'box-sizing': 'border-box', 'line-height': '420px'
      },
      gridStyle: {
        top: '20%',
        left: '10%',
        right: '15%',
        bottom: '10%',
        containLabel: false
      },
      chartDisplay: {
        styleCode: null,// 款式编码信息
        goodsCode: null,// 商品编码信息
        publicUsableQty: null,// 公有可用数
        styleInventoryFunds: null, // 款式库存资金
      },
      settingsInfo: {
        visible: false,
        form: {
          continuousNoSales: null,
        }
      },
      settingsrules: {
        continuousNoSales: [
          { required: true, message: '请选择连续多少天无销量', trigger: 'blur' }
        ]
      },
      detailsInfo: {
        loading: false,
        data: [],
        visible: false,
        title: '',
        total: 0,
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        goodsCode: null,//商品编码
      },
      tableColsDetails,
      consecutiveDays,//连续天数
      platformlist,//平台
      purchasegrouplist: [],//采购架构
      directorGroupList: [],//运营组
      procurementList: [],//采购
      yearMonthDayImport: dayjs().format('YYYY-MM-DD'),//导入日期
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        goodsCreatedStartTime: null,//开始时间
        goodsCreatedEndTime: null,//结束时间
        yearMonthDay: dayjs().format('YYYY-MM-DD'),//日期
        styleCode: null,//款式编码
        goodsCode: null,//商品编码
        goodsLabels: null,//标签
        deptId: [],//采购架构
        brandId: [],//采购
        platform: [],//平台
        groupId: [],//运营组
        continuousNoSales: null,//连续多少天无销量
        hasStock: false,
      },
      myChartShop: null,
      myChartShop1: null,
      myChartShop2: null,
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      brandFilter: {
        brandList: [],
        brand: null,
        brandSelecteds: [],
        visible: false
      }
    }
  },
  async mounted() {
    await this.getList()
    await this.init()
  },
  methods: {
    onDetailsSizechange(val) {
      this.detailsInfo.currentPage = 1;
      this.detailsInfo.pageSize = val;
      this.onDetails({})
    },
    //当前页改变
    onDetailsPagechange(val) {
      this.detailsInfo.currentPage = val;
      this.onDetails({})
    },
    async onDetails(row) {
      this.detailsInfo.loading = true;
      this.detailsInfo.goodsCode = row.goodsCode || this.detailsInfo.goodsCode;
      const { data, success } = await getDongXiaoGoodsPurchaseOrder({ goodsCode: row.goodsCode || this.detailsInfo.goodsCode, currentPage: this.detailsInfo.currentPage, pageSize: this.detailsInfo.pageSize })
      this.detailsInfo.loading = false;
      if (success) {
        this.detailsInfo.data = data.list;
        this.detailsInfo.total = data.total;
        this.detailsInfo.visible = true;
      }
    },
    async onsummaryClick(property) {
      await this.onCoding({}, 2)
    },
    async initCharts() {
      const chartDom = document.getElementById("shopCharts");
      const chartDom1 = document.getElementById("groupCharts");
      const chartDom2 = document.getElementById("groupWeekCharts");
      if (!chartDom || !chartDom1 || !chartDom2) {
        console.warn('图表 DOM 未准备好');
        return;
      }
      this.myChartShop?.clear();
      this.myChartShop = this.myChartShop ?? echarts.init(chartDom);
      const option = await this.Getoptions(this.chartDisplay.platformAnalysis, '平台占比:');
      option && this.myChartShop.setOption(option);

      this.myChartShop1?.clear();
      this.myChartShop1 = this.myChartShop1 ?? echarts.init(chartDom1);
      const option1 = await this.Getoptions(this.chartDisplay.groupAnalysis, '运营组占比:');
      option1 && this.myChartShop1.setOption(option1);

      this.myChartShop2?.clear();
      this.myChartShop2 = this.myChartShop2 ?? echarts.init(chartDom2);
      const option2 = await this.Getoptions(this.chartDisplay.brandAnalysis, '采购占比:');
      option2 && this.myChartShop2.setOption(option2);
    },
    async onCoding(row, val) {
      const params = {
        chartType: val,
        ...(val === 1 ? { styleCode: row.styleCode, yearMonthDay: this.ListInfo.yearMonthDay } : this.ListInfo)
      };
      this.loading = true;
      const { data, success } = await getPurchaseNewInventoryDongXiaoPieChart(params)
      this.loading = false;
      if (success) {
        this.chartDisplay = { ...data }
        this.chartDisplayvisible = true
      };
    },
    async Getoptions(data, title) {
      var option = {
        title: {
          text: title,
          left: 'left',
          top: 0,
          textStyle: {
            fontSize: 14,
            fontWeight: 'bold', // 标题加粗
            padding: [0, 0, 10, 0] // 下边距10px：上右下左
          }
        },
        tooltip: {
          trigger: "item",
          textStyle: { align: "left" },
        },
        legend: {
          type: "scroll",
          orient: 'vertical',
          x: '72%',       // 调整图例靠右但不贴边
          y: 'center',
          itemWidth: 10,
          itemHeight: 10,
          pageIconColor: "#409EFF",
          pageIconInactiveColor: "#909399",
          formatter: function (name) {
            return echarts.format.truncateText(
              name,
              200,
              "10px Microsoft Yahei",
              "..."
            );
          },
          tooltip: { show: true }
        },
        series: {
          type: 'pie',
          radius: '45%',
          center: ['40%', '58%'], // 向左移动，确保图例和饼图间隔20px
          data: data,
          labelLine: { show: true },
          label: {
            show: true,
            formatter: '{d}%',
            position: 'outside'
          }
        },
        grid: {
          left: "1%",
          right: "1%",
          bottom: "1%",
          containLabel: true,
        },
        series: {
          type: 'pie',
          radius: '45%',
          center: ["40%", "58%"],
          data: data,
          /*emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
          },*/
          labelLine: {
            show: true
          },
          label: {
            show: true,
            formatter: '{d}%',//'{b}'+'\n\r'+'{c}' + '\n\r' + '{d}%',
            position: 'outside'
          }
        },
      };
      return option;
    },
    async onSettingsMethod() {
      const { data, success } = await getPurchaseNewInventoryDongXiaoSet()
      this.settingsInfo.visible = true
      this.$refs.settingsForm && this.$refs.settingsForm.resetFields();
      this.$refs.settingsForm && this.$refs.settingsForm.clearValidate();
      this.settingsInfo.form.continuousNoSales = success ? data : null
    },
    onSettingsSubmit() {
      this.$refs.settingsForm.validate(async valid => {
        if (valid) {
          const { data, success } = await editPurchaseNewInventoryDongXiaoSet({ continuousNoSales: this.settingsInfo.form.continuousNoSales }) // 修改设置
          if (success) {
            this.$message({ message: '设置成功', type: 'success' })
            this.settingsInfo.visible = false
            this.getList()
          }
        }
      })
    },
    async init() {
      //采购
      var { data } = await getPurchaseNewPlanTurnDayBrandList();
      this.procurementList = data.map(item => { return { value: item.id, label: item.brandName }; });
      //架构
      let { data: deptList, success } = await getPurchaseNewPlanTurnDayDeptList();
      if (success) {
        this.purchasegrouplist = deptList.map(item => { return { value: item.dept_id, label: item.full_name }; });
      }
      //运营组
      const res2 = await getDirectorGroupList({})
      this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);
    },
    callbackMethod(val, type) {
      const map = {
        styleCode: () => (this.ListInfo.styleCode = val),
        goodsCode: () => (this.ListInfo.goodsCode = val),
        goodsLabels: () => (this.ListInfo.goodsLabels = val),
      };
      map[type]?.();
    },
    async changeTime(e) {
      this.ListInfo.goodsCreatedStartTime = e ? e[0] : null
      this.ListInfo.goodsCreatedEndTime = e ? e[1] : null
    },
    async exportProps() {
      const res = await exportPurchaseNewInventoryDongXiaoDtl(this.ListInfo)
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await gtePurchaseNewInventoryDongXiaoDtlPage(this.ListInfo)
      if (success) {
        data.list.forEach(item => {
          const processVoucher = (voucher) => {
            if (!voucher) return null;
            try {
              // 如果已经是 JSON 字符串且可解析为数组，直接返回
              const parsed = JSON.parse(voucher);
              if (Array.isArray(parsed)) {
                return voucher;
              }
            } catch (e) {
              // 不是 JSON 格式，继续处理
            }
            // 把逗号分隔的字符串或单个链接处理成数组对象
            const urls = voucher.split(',').map(url => ({ url: url.trim() }));
            return JSON.stringify(urls);
          };
          item.picture = processVoucher(item.picture);
        });
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async onBrandSetting() {
      var res = await getDongXiaoBrandFilterDropdownData()
      this.brandFilter.brandList = res?.data ?? [];
      this.brandFilter.brand = null;
      var res2 = await getDongXiaoBrandFilterSet();
      this.brandFilter.brandSelecteds = res2?.data ?? [];
      this.brandFilter.visible = true;
    },
    async onAddBrandFilter() {
      var params = { brandName: this.brandFilter.brand };
      var res = await saveDongXiaoBrandFilterSet(params);
      if (res?.success) {
        this.brandFilter.brandSelecteds.push(this.brandFilter.brand);
        this.brandFilter.brandList.splice(this.brandFilter.brandList.indexOf(this.brandFilter.brand), 1);
        this.brandFilter.brand = null;
      }
    },
    async handleClose(e, i) {
      var params = { brandName: e };
      var res = await delDongXiaoBrandFilterSet(params);
      if (res?.success) {
        this.brandFilter.brandSelecteds.splice(i, 1);
        this.brandFilter.brandList.push(e);
      }
    }
  }
}
</script>

<style scoped lang="scss">
#shopCharts {
  width: 100%;
  height: 300px;
}

#groupCharts {
  width: 100%;
  height: 300px;
}

#groupWeekCharts {
  width: 100%;
  height: 300px;
}

.top {
  display: flex;
  margin-bottom: 5px;
  align-items: center;

  .publicCss {
    width: 150px;
    margin-right: 2px;
  }
}

::v-deep .el-select__tags-text {
  max-width: 30px;
}

::v-deep(.el-button.top_button + .el-button.top_button) {
  margin-left: 1px;
}

.chart_CSS {
  display: flex;
  flex-direction: column;
  margin-top: 10px;
}

.chart_bottom {
  display: flex;
  justify-content: space-between;
  column-gap: 5px;
  /* 设置列间距为5px */
  padding-top: 20px;
}

.chart_item {
  flex: 1;
  text-align: center;
}
</style>
