<template>
    <MyContainer>
        <el-tabs v-model="activeName">
            <el-tab-pane label="组绩效统计(售前组)" name="first">
                <pddGroupStatistics @toUser="toUser"></pddGroupStatistics>
            </el-tab-pane>
            <el-tab-pane label="店绩效统计(售前组)" name="second">
                <pddShopStatistics></pddShopStatistics>
            </el-tab-pane>
            <el-tab-pane label="个人绩效统计(售前组)" name="third">
                <pddPersonnelStatistics></pddPersonnelStatistics>
            </el-tab-pane>
            <el-tab-pane label="新绩效核算表(售前组)" name="forth">
                <pddNewPerformanceStatement></pddNewPerformanceStatement>
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import pddPersonnelStatistics from './components/pddPersonnelStatistics.vue';
import pddShopStatistics from './components/pddShopStatistics.vue';
import pddGroupStatistics from './components/pddGroupStatistics.vue';
import pddNewPerformanceStatement from './components/pddNewPerformanceStatement.vue'
export default {
    components: {
        MyContainer,pddPersonnelStatistics,pddShopStatistics,pddGroupStatistics,pddNewPerformanceStatement
    },
    data() {
        return {
            activeName: 'first'
        };
    },
    methods: {
        toUser(e){
            this.activeName = e
        }
    }
};
</script>

<style lang="scss" scoped></style>