<template>
    <MyContainer>
        <vxetablebase :id="'positionInfo202408041752'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            style="width: 100%; height: 480px; margin: 0" v-loading="loading" />
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
const tableCols = [
    { istrue: true, prop: 'GoodsCode', align: 'center', label: '商品编码', width: 'auto' },
    { istrue: true, prop: 'Thoroughfare', label: '仓位', width: 'auto', },
]
export default {
    name: "positionInfo",
    components: {
        MyContainer, vxetablebase
    },
    props: ['jsonParse'],
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
            },
            tableCols: tableCols,
            tableData: [],
            total: 0,
            loading: false,
        }
    },
    async mounted() {
        if (this.jsonParse) {
            this.tableData = this.jsonParse
        }
    },
    methods: {

    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>