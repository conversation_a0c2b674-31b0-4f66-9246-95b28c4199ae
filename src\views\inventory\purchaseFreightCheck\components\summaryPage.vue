<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-select v-model="ListInfo.payment" placeholder="采购单类型" class="publicCss" clearable multiple collapse-tags>
          <el-option key="包邮" label="包邮" value="包邮" />
          <el-option key="寄付" label="寄付" value="寄付" />
          <el-option key="仓库到付" label="仓库到付" value="仓库到付" />
        </el-select>
        <el-input v-model.trim="ListInfo.supplier" placeholder="供应商名称" maxlength="64" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click=exportProps>导出</el-button>
      </div>
      <div style="width: 100%;margin-bottom: 5px;">
        <el-scrollbar horizontal style="width: 1690px;margin-bottom: 5px;">
          <div style="display: flex; flex-shrink: 0;">
            <div v-for="(item, i) in purchaseList" :key="i" :style="{
              margin: '5px 10px',
              marginRight: i === 0 ? '40px' : '5px',
              width: '200px',
              height: '100px',
              border: '1px solid #dcdfe6',
              borderRadius: '4px',
              backgroundColor: 'white',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '12px',
              flexShrink: 0,
            }">
              <div>
                {{ item.label }}
              </div>
              <div style="font-weight: bold;font-size: 18px;">
                {{ item.value !== null && item.value !== undefined ? item.value.toLocaleString() : '0' }}
              </div>
            </div>
          </div>
        </el-scrollbar>
        <el-scrollbar style="width: 1690px;margin-bottom: 5px;">
          <div style="display: flex; flex-shrink: 0;">
            <div v-for="(item, i) in freightList" :key="i" :style="{
              margin: '5px 10px',
              marginRight: i === 0 ? '40px' : '5px',
              width: '200px',
              height: '100px',
              border: '1px solid #dcdfe6',
              borderRadius: '4px',
              backgroundColor: 'white',
              flexShrink: 0,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '12px',
            }">
              <div>
                {{ item.label }}
              </div>
              <div style="font-weight: bold;font-size: 18px;">
                {{ item.value !== null && item.value !== undefined ? item.value.toLocaleString() : '0' }}
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </template>
    <vxetablebase :id="'summaryPage202410151500'" :tablekey="'summaryPage202410151500'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getSummaryOrderQtyFreight, getSummaryListPage, exportSummaryList } from '@/api/inventory/purchaseCostVerify'
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'supplier', label: '供应商名称', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'payment', label: '采购单类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'purQty', label: '采购单数量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'freight', label: '采购运费', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'haulage', label: '托运费', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'deliveryFee', label: '送货费', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'pickUpFee', label: '提货费', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'huoLaLa', label: '货拉拉', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'loadingFee', label: '装车费', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'processRate', label: '处理率', formatter: (row) => (row.processRate !== null && row.processRate !== undefined) ? row.processRate + '%' : '', },
]
export default {
  name: "summaryPage",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      labelMap: {
        purQty: '采购单',
        freeShippingQty: '包邮',
        consignPaymentQty: '寄付',
        codQty: '仓库到付',
        freight: '采购运费',
        haulage: '托运费',
        deliveryFee: '送货费',
        pickUpFee: '提货费',
        huoLaLa: '货拉拉',
        loadingFee: '装车费',
      },
      purchaseList: [],
      freightList: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startDate: null,//开始时间
        endDate: null,//结束时间
        payment: [],//采购单类型
        supplier: '',//供应商名称
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    this.purchaseList = [
      { label: '采购单', value: 0 },
      { label: '包邮', value: 0 },
      { label: '寄付', value: 0 },
      { label: '仓库到付', value: 0 },
    ]
    this.freightList = [
      { label: '采购运费', value: 0 },
      { label: '托运费', value: 0 },
      { label: '送货费', value: 0 },
      { label: '提货费', value: 0 },
      { label: '货拉拉', value: 0 },
      { label: '装车费', value: 0 },
    ]
    await this.getList()
  },
  methods: {
    async changeTime(e) {
      this.ListInfo.startDate = e ? e[0] : null
      this.ListInfo.endDate = e ? e[1] : null
    },
    async exportProps() {
      this.loading = true
      const params = this.queryCondition()
      await exportSummaryList(params)
      this.loading = false
    },
    queryCondition() {
      return {
        currentPage: this.ListInfo.currentPage,
        pageSize: this.ListInfo.pageSize,
        orderBy: this.ListInfo.orderBy,
        isAsc: this.ListInfo.isAsc,
        startDate: this.ListInfo.startDate,
        endDate: this.ListInfo.endDate,
        payment: this.ListInfo.payment?.length ? this.ListInfo.payment.join(',') : '',
        supplier: this.ListInfo.supplier,
      }
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给当前月第一天至今天
        this.ListInfo.startDate = dayjs().startOf('month').format('YYYY-MM-DD')
        this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
      }
      this.loading = true
      const params = this.queryCondition();
      const { data: data1, success: success1 } = await getSummaryOrderQtyFreight(params)
      if (success1) {
        this.purchaseList = [];
        this.freightList = [];
        for (const [key, value] of Object.entries(data1)) {
          if (['purQty', 'freeShippingQty', 'consignPaymentQty', 'codQty'].includes(key)) {
            this.purchaseList.push({
              label: this.labelMap[key] || key,
              value: value || 0
            });
          } else if (['freight', 'haulage', 'deliveryFee', 'pickUpFee', 'huoLaLa', 'loadingFee'].includes(key)) {
            this.freightList.push({
              label: this.labelMap[key] || key,
              value: value || 0
            });
          }
        }
      } else {
        this.$message.error('获取汇总数据失败')
      }
      const { data, success } = await getSummaryListPage(params);
      if (success) {
        this.tableData = data.list;
        this.total = data.total;
        let summary = data.summary || {}; // 如果 summary 为 null，则赋值为空对象
        Object.entries(summary).forEach(([key, value]) => {
          if (typeof value !== 'string') {
            summary[key] = String(value);
          }
        });
        this.summaryarry = summary;
        if (this.summaryarry.processRate_sum !== undefined) {
          this.summaryarry.processRate_sum = this.summaryarry.processRate_sum + '%';
        }
        this.loading = false;
      } else {
        this.$message.error('获取列表失败');
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
// ::v-deep.sidebar-wrapper .el-scrollbar__wrap {
// overflow-x: hidden;
// }

// ::v-deep.is-horizontal {
// display: none;
// }
// ::v-deep .el-scrollbar__wrap {
// overflow-x: auto;
// height: calc(100% + 20px);
// }

// ::v-deep .el-scrollbar .el-scrollbar__wrap .el-scrollbar__view {
// white-space: nowrap;
// display: inline-block;
// }
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 170px;
    margin-right: 5px;
  }
}

::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>
