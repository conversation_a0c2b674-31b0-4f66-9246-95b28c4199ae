<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
    <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent>
      <el-row>
        <el-form-item label="时间:">
          <el-date-picker style="width:320px"
            v-model="Filter.timerange"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="从："
            end-placeholder="到："
            :picker-options="pickerOptions"
          ></el-date-picker>
        </el-form-item>
         <el-form-item label="分析维度">
          <el-radio-group @change="dimChange" v-model="Filter.dim">
                    <el-radio
                  v-for="item in dimList"
                  :key="item.id"
                  :label="item.id">{{item.name}}             
                </el-radio>
              </el-radio-group>
        </el-form-item>
      </el-row>
      <hr/>
      <el-row v-if="Filter.dim != 6">
          <el-col v-if="Filter.dim == 1" :span="6">
              <el-form-item  label="组长:" label-position="left" label-width="72px">
                <el-select filterable @change="dimGroupChange"  v-model="Filter.idDirectorGroup" placeholder="请选择" class="el-select-content" style="width:200px">
                  <el-option 
                  v-for="item in groupList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key">             
                  </el-option>
                </el-select>
              </el-form-item>
          </el-col>
          <el-col v-if="Filter.dim == 2" :span="6">
              <el-form-item  label="运营专员:" label-position="left" label-width="72px">
                <el-select filterable @change="dimOperateSpecialChange"  v-model="Filter.idOperateSpecial" placeholder="请选择" class="el-select-content" style="width:200px">
                  <el-option 
                  v-for="item in userList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key">             
                  </el-option>
                </el-select>
              </el-form-item>
          </el-col>
          <el-col v-if="Filter.dim == 3" :span="6">
              <el-form-item  label="车手:" label-position="left" label-width="72px">
                <el-select filterable @change="dimUser2Change"  v-model="Filter.idUser2" placeholder="请选择" class="el-select-content" style="width:200px">
                  <el-option 
                  v-for="item in userList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key">             
                  </el-option>
                </el-select>
              </el-form-item>
          </el-col>
          <el-col v-if="Filter.dim == 4" :span="6">
              <el-form-item  label="计划:" label-position="left" label-width="72px">
                <el-select filterable @change="dimProjectChange"  v-model="Filter.idProject" placeholder="请选择" class="el-select-content" style="width:200px">
                  <el-option 
                  v-for="item in projectList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key">             
                  </el-option>
                </el-select>
              </el-form-item>
          </el-col>
          <el-col v-if="Filter.dim == 5" :span="6">
              <el-form-item  label="产品:" label-position="left" label-width="72px">
                <el-select filterable remote :remote-method="remoteFilterProduct" @change="dimProductChange"  v-model="Filter.idProduct" placeholder="请选择" class="el-select-content" style="width:200px">
                  <el-option 
                  v-for="item in productList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key">             
                  </el-option>
                </el-select>
              </el-form-item>
          </el-col>
           <el-col :span="10">
            <el-form-item label="关键词">
              <el-select multiple clearable filterable @change="dimNameKeyWordListChange" v-model="Filter.nameKeyWordList" placeholder="请选择" class="el-select-content" style="width:500px">
                  <el-option label="所有"  value></el-option>
                  <el-option 
                    v-for="item in keyWordList"
                    :key="item"
                    :label="item"
                    :value="item">             
                  </el-option>
                </el-select>
            </el-form-item>
         </el-col>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport">导出</el-button>
        </el-form-item>
      </el-row>
      <div v-else>
        <el-row>
        <el-form-item label="关键词分析维度">
          <el-radio-group @change="keyWordDimChange" v-model="Filter.keyWordDim">
                    <el-radio
                  v-for="item in keyWordDimList"
                  :key="item.id"
                  :label="item.id">{{item.name}}             
                </el-radio>
              </el-radio-group>
        </el-form-item>
        </el-row>
        <el-row>
          <!--
          <el-col :span="8">
              <el-form-item  label="关键词:" label-position="left" label-width="72px">
                <el-select filterable @change="keyWordChange"  v-model="Filter.nameKeyWord" placeholder="请选择" class="el-select-content" style="width:500px">
                  <el-option 
                  v-for="item in allKeyWordList"
                  :key="item"
                  :label="item"
                  :value="item">             
                  </el-option>
                </el-select>
              </el-form-item>
          </el-col>
          -->
          <el-col :span="8">
              <el-form-item  label="关键词:" label-position="left" label-width="72px">
                <el-select filterable remote :remote-method="remoteFilterMethod" @change="keyWordChange"  v-model="Filter.nameKeyWord" placeholder="请选择" class="el-select-content" style="width:500px">
                  <el-option 
                  v-for="item in filterKeyWordList"
                  :key="item"
                  :label="item"
                  :value="item">             
                  </el-option>
                </el-select>
              </el-form-item>
          </el-col>
          <el-col v-if="Filter.keyWordDim == 1" :span="12">
             <el-form-item  label="运营专员:" label-position="left" label-width="72px">
                <el-select multiple clearable filterable  v-model="Filter.keyWordIdUserList" placeholder="请选择" class="el-select-content" style="width:800px">
                  <el-option 
                  v-for="item in keyWordUserList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key">             
                  </el-option>
                </el-select>
              </el-form-item>
          </el-col>
          <el-col v-if="Filter.keyWordDim == 2" :span="12">
             <el-form-item  label="车手:" label-position="left" label-width="72px">
                <el-select multiple clearable filterable v-model="Filter.keyWordIdUserList" placeholder="请选择" class="el-select-content" style="width:500px">
                  <el-option 
                  v-for="item in keyWordUserList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key">             
                  </el-option>
                </el-select>
              </el-form-item>
          </el-col>
           <el-col v-if="Filter.keyWordDim == 3" :span="12">
              <el-form-item  label="计划:" label-position="left" label-width="72px">
                <el-select multiple clearable filterable v-model="Filter.keyWordIdProjectList" placeholder="请选择" class="el-select-content" style="width:500px">
                  <el-option 
                  v-for="item in keyWordProjectList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key">             
                  </el-option>
                </el-select>
              </el-form-item>
          </el-col>
           <el-col v-if="Filter.keyWordDim == 4" :span="12">
              <el-form-item  label="产品:" label-position="left" label-width="72px">
                <el-select multiple clearable filterable v-model="Filter.keyWordIdProductList" placeholder="请选择" class="el-select-content" style="width:500px">
                  <el-option 
                  v-for="item in keyWordPorductList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key">             
                  </el-option>
                </el-select>
              </el-form-item>
          </el-col>
           <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport">导出</el-button>
        </el-form-item>
        </el-row>
      </div>
        <hr/>
        <el-row>
          <el-form-item label="固定列:" label-position="right" label-width="72px">
          <el-select multiple clearable filterable v-model="fixedColumn" placeholder="请选择" class="el-select-content" style="width:400px">
            <el-option 
              v-for="item in tableCols"
              :key="item.index"
              :label="item.label"
              :value="item.index">             
            </el-option>
          </el-select>
        </el-form-item>

          <el-form-item label="显示列:" label-position="right" label-width="72px">
          <el-select multiple clearable filterable v-model="showColumn" placeholder="请选择" class="el-select-content" style="width:400px">
            <el-option label="所有"  :value=999></el-option>
            <el-option 
              v-for="item in tableCols"
              :key="item.index"
              :label="item.label"
              :value="item.index">             
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onClearFilter">清空列头条件</el-button>
        </el-form-item>
        </el-row>
      </el-form>
    </template>
    <!--列表-->
      <el-table
      ref="table"
      :data='ZTCKeyWordList'
      :tableCols='tableCols'
      @sort-change='sortchange'
      :border=true
      max-height=630px
      style="width: 100%;"
      @cell-click='cellclick'>
       <el-table-column type="index" :fixed=true>
        </el-table-column>
        <template v-for="(item, index) in tableCols">
          <el-table-column :sortable ="(item.typeId != null) || (item.prop == 'keyWord')"  show-overflow-tooltip  v-if="!(item.display==false)"
          :key="index"
          :prop="item.prop" 
          :label="item.label"
          :width="item.width"
          :fixed=item.fixed
          align="left">
            <template slot="header">
              <span>{{item.label}}</span>
              <el-button v-if="item.typeId != null"
                size="mini" style="margin:5px" icon="el-icon-edit" @click.stop="headerClick(item)"></el-button>
            </template>
            <template slot-scope="scope" >
                    <span v-if="item.type==='html'" v-html="(item.formatter && item.formatter(scope.row))"></span>
                    <span v-if="item.type==='format'">{{  }} </span>
                        <!-- 默认 -->
                    <span v-if="!item.type" 
                          :style="item.itemStyle && item.itemStyle(scope.row)" 
                          :class="item.itemClass && item.item.itemClass(scope.row)">{{(item.formatter && item.formatter(scope.row)) || scope.row[item.prop]}}</span>
            </template>
          </el-table-column>
        </template>
    </el-table>
        <!--分页-->
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getZTCKeyWordList"
      />
    </template>
    <el-popover
      placement="bottom-end"
      :width="1000"
      :height="700"
      v-model="visiblepopover"
      :reference="prevTarget"
      :key="popperFlag"
      >
      <el-row>
        <el-col :span="12">
            <el-select multiple clearable filterable v-model="Filter.propLine" placeholder="请选择" class="el-select-content" style="width:400px">
               <template v-for="item in tableCols">
                      <el-option v-if="item.typeId != null" 
                        :key="item.prop"
                        :label="item.label"
                        :value="item.prop">
                      </el-option>
               </template>
            </el-select>
        </el-col>
        <el-col :span="3">
          <el-button @click="onRefreshTableChart" size="mini">刷新</el-button>
        </el-col>
      </el-row>
      <el-row>
          <div id="chartDivCell"  style="height: 500px;width:1000px; box-sizing:border-box; line-height: 400px;"></div>
      </el-row>
    </el-popover>
    <el-dialog :title="currentFilterTypeTitle" :visible.sync="dialogVisibleCountShow" :show-close=false :close-on-click-modal=false :close-on-press-escape=false width="330px">
      <el-row>
        大于等于<el-input v-model="currentFilterTypeMin" style="width:183px; margin:10px;"/>
      </el-row>
      <el-row style="margin:10px">
      </el-row>
      <el-row >
        小于等于<el-input v-model="currentFilterTypeMax" style="width:183px;margin:10px;"/>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="headerFilterConfirm">确认</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList,
         getDirectorGroupList as getDirectorGroupList,
         getDirectorList as getDirectorList
       } from '@/api/operatemanage/base/shop';
import {
        getAllProductList,
        filterProductAsync
       } from '@/api/operatemanage/base/product';
import { formatPlatform,formatLink} from "@/utils/tools";
import { TimelineComponent } from 'echarts/components';
import { importZTCKeyWordAsync, 
        getPageList as getZTCPageList, 
        getKeyWordListByGroupId,
        ztcKeyWordAnalysisTableAsync,
        getKeyWordListByOperateSpecialId,
        getKeyWordListByUser2Id,
        getAllProjectList,
        getKeyWordListByProjectId,
        getKeyWordListByProducttId,
        getAllKeyWordListAsync,
        getOperateSpcialListByKeyWordAsync,
        getUser2ListByKeyWordAsync,
        getProjectListByKeyWordAsync,
        getProductListByKeyWordAsync,
        filterKeyWordListAsync,
        ztcKeyWordAnalysisWithCost,
        exportZtcKeyWordAnalysis} from '@/api/operatemanage/ztc/ztckeyword'
import * as echarts from 'echarts';
import { color } from 'echarts/core';

const tableCols = [
      {index:0, iistrue:true, fixed:false, display:false,prop:'keyWord',label:'关键词', width:'100'},
      /*
      {index:1, iistrue:true, fixed:false, display:false,prop:'platform',label:'平台', width:'80', formatter:(row)=>formatPlatform(row.platform)},
      {index:2, iistrue:true, fixed:false, display:false,prop:'nameShop',label:'店铺名', width:'150'},
      */
      {index:3, iistrue:true, fixed:false, display:false,prop:'nameProduct',label:'商品名称', width:'180',type:'html', formatter:(row)=>{
            if(row.idProduct == null) return ""
            var  proBaseUrl="https://detail.tmall.com/item.htm?id="+row.idProduct;
            return formatLink(row.idProduct + "//" + row.nameProduct,proBaseUrl);
      }},
      //{index:4, iistrue:true, fixed:false, display:false,prop:'idProduct',label:'商品id', width:'120'},
      //{index:31, iistrue:true, fixed:false, display:false,prop:'idProject',label:'计划id', width:'120'},
      {index:32, iistrue:true, fixed:false, display:false,prop:'nameProject',label:'计划名称', width:'120', type:'html', formatter:(row) => {
        if(row.idProject == null) return ""
        var result = row.idProject + "//" + row.nameProject;
        return result;
      }},

      
      {index:5, iistrue:true, fixed:false, display:false,prop:'nameGroup',label:'小组名', width:'80'},
      //{index:6, iistrue:true, fixed:false, display:false,prop:'nameUser',label:'用户名', width:'80'},
      
      {index:7, iistrue:true, fixed:false, display:false,prop:'nameOperateSpecial',label:'运营专员', width:'80'},
      //{index:8, iistrue:true, fixed:false, display:false,prop:'nameUser1',label:'运营助理', width:'80'},
      {index:9, iistrue:true, fixed:false, display:false,prop:'nameUser2',label:'车手', width:'80'},
      //{index:10, iistrue:true, fixed:false, display:false,prop:'nameUser3',label:'备用人员', width:'80'},
      
      {index:11, iistrue:true, typeId: 1, fixed:false, display:false,prop:'countShow',label:'展现量', width:'80', type:'html',formatter:(row)=>{
        var html = '<span style="color:#0000FF">' + (row.countShow != null?row.countShow:"") + '</span>'
        return html}},
      {index:12, iistrue:true, typeId: 2, fixed:false, display:false,prop:'countClick',label:'点击量', width:'80',type:'html',formatter:(row)=>{
        var html = '<span style="color:#0000FF">' + (row.countClick != null ? row.countClick : "") + '</span>'
        return html}},
      {index:13, iistrue:true, typeId: 3, fixed:false, display:false,prop:'amountAll',label:'花费', width:'80',type:'html',formatter:(row)=>{
        var html =  '<span style="color:#0000FF">' + (row.amountAll != null ? row.amountAll : "") + '</span>'
        return html}},
      {index:14, iistrue:true, typeId: 4, fixed:false, display:false,prop:'rateClick',label:'点击率', width:'80',type:'html',formatter:(row)=>{
        var html =  '<span style="color:#0000FF">' + (row.rateClick!=null?(row.rateClick.toFixed(2)+'%'):"") + '</span>'
        //return row.rateClick?(row.rateClick.toFixed(2)+'%'):null
        return html}},
      {index:15, iistrue:true, typeId: 5, fixed:false, display:false,prop:'amountClickAvg',label:'平均点击花费', width:'80',type:'html',formatter:(row)=>{
        //return row.amountClickAvg?.toFixed(2)}},
        var html = '<span style="color:#0000FF">' + (row.amountClickAvg!=null?row.amountClickAvg.toFixed(2):"") + '</span>'
        return html}},
      {index:16, iistrue:true, typeId: 6, fixed:false, display:false,prop:'countStar',label:'总收藏数', width:'80',type:'html',formatter:(row)=>{
        var html = '<span style="color:#0000FF">' + (row.countStar != null ? row.countStar : "") + '</span>'
        return html}},
      {index:17, iistrue:true, typeId: 7, fixed:false, display:false,prop:'amountStar',label:'收藏成本', width:'80',type:'html',formatter:(row)=>{
        //return row.amountStar?.toFixed(2)}},
        var html =  '<span style="color:#0000FF">' + (row.amountStar != null ?row.amountStar.toFixed(2):"") + '</span>'
        return html }},
      {index:18, iistrue:true, typeId: 8, fixed:false, display:false,prop:'countShoppingCarAll',label:'总购物车数', width:'80',type:'html',formatter:(row)=>{
        var html = '<span style="color:#0000FF">' + (row.countShoppingCarAll != null ? row.countShoppingCarAll : "") + '</span>'
        return html }},
      {index:19, iistrue:true, typeId: 9, fixed:false, display:false,prop:'amountShoppingCar',label:'加购成本', width:'80',type:'html',formatter:(row)=>{
        //return row.amountShoppingCar?.toFixed(2)}},
        var html = '<span style="color:#0000FF">' + (row.amountShoppingCar!=null?row.amountShoppingCar.toFixed(2):"") + '</span>'
        return html}},
      {index:20, iistrue:true, typeId: 10, fixed:false, display:false,prop:'rateStarConversion',label:'宝贝收藏转化率', width:'80',type:'html',formatter:(row)=>{
        //return row.rateStarConversion?(row.rateStarConversion.toFixed(2) + '%'):null;}},
        var html = '<span style="color:#0000FF">' + (row.rateStarConversion!=null?(row.rateStarConversion.toFixed(2) + '%'):"") + '</span>'
        return html }},
      {index:21, iistrue:true, typeId: 11, fixed:false, display:false,prop:'rateShoppingCarConversion',label:'加购率', width:'80',type:'html',formatter:(row)=>{
        //return row.rateShoppingCarConversion?(row.rateShoppingCarConversion.toFixed(2) + '%'):null}},
        var html = '<span style="color:#0000FF">' + (row.rateShoppingCarConversion!=null?(row.rateShoppingCarConversion.toFixed(2) + '%'):"") + '</span>'
        return html}},
      {index:22, iistrue:true, typeId: 12, fixed:false, display:false,prop:'amountDealAll',label:'总成交金额', width:'80',type:'html',formatter:(row)=>{
        //return row.amountDealAll?.toFixed(2)}},
        var html = '<span style="color:#0000FF">' + (row.amountDealAll!=null?row.amountDealAll.toFixed(2):"") + '</span>'
        return html}},
      {index:23, iistrue:true, typeId: 13, fixed:false, display:false,prop:'amountDealDirect',label:'直接成交金额', width:'80',type:'html',formatter:(row)=>{
        //return row.amountDealDirect?.toFixed(2)}},
        var html = '<span style="color:#0000FF">' + (row.amountDealDirect!=null?row.amountDealDirect.toFixed(2):"") + '</span>'
        return html}},
      {index:24, iistrue:true, typeId: 14, fixed:false, display:false,prop:'amountDealIndirect',label:'间接成交金额', width:'80',type:'html',formatter:(row)=>{
        //return row.amountDealIndirect?.toFixed(2)}},
        var html = '<span style="color:#0000FF">' + (row.amountDealIndirect!=null?row.amountDealIndirect.toFixed(2):"") + '</span>'
        return html}},
      {index:25, iistrue:true, typeId: 15, fixed:false, display:false,prop:'countDealAll',label:'总成交笔数', width:'80',type:'html',formatter:(row)=>{
        var html =  '<span style="color:#0000FF">' + (row.countDealAll != null ? row.countDealAll : "") + '</span>'
        return html }},
      {index:26, iistrue:true, typeId: 16, fixed:false, display:false,prop:'countDealDirect',label:'直接成交笔数', width:'80',type:'html',formatter:(row)=>{
        var html =  '<span style="color:#0000FF">' + (row.countDealDirect != null ? row.countDealDirect : "") + '</span>'
        return html }},
      {index:27, iistrue:true, typeId: 17, fixed:false, display:false,prop:'countDealIndirect',label:'间接成交笔数', width:'80',type:'html',formatter:(row)=>{
        var html = '<span style="color:#0000FF">' + (row.countDealIndirect != null ? row.countDealIndirect : "")+ '</span>'
        return html }},
      {index:28, iistrue:true, typeId: 18, fixed:false, display:false,prop:'rateInputOutput',label:'投入产出比', width:'80',type:'html',formatter:(row)=>{
        //return row.rateInputOutput?row.rateInputOutput.toFixed(2):null}},
        var html = '<span style="color:#0000FF">' + (row.rateInputOutput!=null?row.rateInputOutput.toFixed(2):"") + '</span>'
        return html}},
      {index:29, iistrue:true, typeId: 19, fixed:false, display:false,prop:'rateClickConversion',label:'点击转化率', width:'80',type:'html',formatter:(row)=>{
        //return row.rateClickConversion?(row.rateClickConversion.toFixed(2)+'%'):null}},
        var html = '<span style="color:#0000FF">' + (row.rateClickConversion!=null?(row.rateClickConversion.toFixed(2)+'%'):"") + '</span>'
        return html}},    
      {index:30, iistrue:true, typeId: 20, fixed:false, display:false,prop:'amountDealAvg',label:'平均每笔花费', width:'80',type:'html',formatter:(row)=>{
        var html = '<span style="color:#0000FF">' + (row.amountDealAvg!=null?row.amountDealAvg.toFixed(2):"") + '</span>'
        return html}},
      {index:33, iistrue:true, fixed:false, display:false,prop:'stockOut',label:'缺货', width:'80',type:'html',formatter:(row)=>{
        var html
        if(row.stockOut != null && row.stockOut)
        {
           html = ' <span style="color: white; background-color:red; font-weight: 500;text-align: center;">缺货</span>'
        }
        else if(row.stockOut == null)
        {
          html = '无数据'
        }
        
        return html}},
     ];

export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
  data() {
    return {
      that:this,
      Filter: {
        timerange: [formatTime(dayjs().startOf("month"), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")],
        dim:0,
        keyWordDim:1,
        nameKeyWordList:[''],
        nameKeyWord:'',
        filterMaxMin:[],
        idProject:'',
        idProduct:'',
        keyWordIdUserList:[],
        keyWordIdProjectList:[],
        keyWordIdProductList:[],
        //表格点击时的当前行与列
        row:{},
        propLine:[],
        //展示的统计元素
        showElements:[]
      },
      pager:{OrderBy:"amountAll",IsAsc:false},
      currentFilterTypeId:1,
      currentFilterTypeTitle:"展现量",
      currentFilterTypeMax:0,
      currentFilterTypeMin:0,
      showColumn:[33],
      fixedColumn:[0,3,32,5,7,9],
      keyWordList:[],
      allKeyWordList:[],
      filterKeyWordList:[],
      dimList:[{id:0,name:"所有关键词"},{id:1,name:"小组"},{id:2,name:"运营专员"},
      {id:3,name:"车手"},{id:4,name:"计划"},
      {id:5,name:"产品"},{id:6,name:"关键词"}],
      keyWordDimList:[{id:1,name:"运营专员"},
      {id:2,name:"车手"},{id:3,name:"计划"},
      {id:4,name:"产品"}],
      shopList:[],
      userList:[],
      groupList:[],
      projectList:[],
      productList:[],
      keyWordUserList:[],
      keyWordProjectList:[],
      keyWordPorductList:[],
      ZTCKeyWordList: [],
      tableCols:tableCols,
      total: 0,
      summaryarry:null,
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      //
      pickerOptions:{
        shortcuts: [{
            text: '今天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date().setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '昨天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 1).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '3天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 2).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '7天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 6).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '15天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 14).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '30天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 29).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }]
      },
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
      dialogVisibleCountShow:false,
      prevTarget:null,
      visiblepopover:false,
      popperFlag:false
    };
  },
  async mounted() {
    await this.getGroupList();
  },
  methods: {
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.getZTCKeyWordList()
    },
    async cellclick(row, column, cell, event){
        this.Filter.propLine = []

        if(column.property == 'keyWord' ||
           column.property == 'nameProduct' || 
           column.property == 'nameProject' || 
           column.property == 'nameGroup' || 
           column.property == 'nameOperateSpecial' || 
           column.property == 'nameUser2' ||
           column.property == 'stockOut')
           {
             return
           }

        let currentTarget = event.target
        if(currentTarget === this.prevTarget)
        {
           this.prevTarget = null
           this.visiblepopover = false
           return
        }

        this.visiblepopover = false
        this.prevTarget = null
        this.popperFlag = !this.popperFlag

        this.$nextTick(async ()=>{
          this.prevTarget = currentTarget
          this.visiblepopover = true

          this.Filter.row = row
          this.Filter.propLine.push(column.property)
          if(column.property != 'amountAll')
          {
              this.Filter.propLine.push('amountAll')
          }

          this.onRefreshTableChart()
        })
    },
    GetChartoptions(element, para){

      //数据
        var series=[]
        element.series.forEach(s=>{
          series.push({  smooth: true, ...s})
        })

        //标志
        

        //y轴
        var yAxis = []

        for(var i = 0; i < element.yAxis.length; i++)
        {
          yAxis.push({type:'value', position: element.yAxis[i].position, offset:element.yAxis[i].offset ,name:element.yAxis[i].name, axisLabel:{formatter: '{value}'}})
        }

      var option = {
        title: {
              text: '',
              subtext: '',
              left: 'left'
          },
        tooltip: {trigger: 'axis'},
        legend: {
            data: element.legend.data,
            selected : element.legend.selected
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {feature: {
        }},
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis: yAxis,
        series:  series
    };
    return option;
    },
    async remoteFilterMethod(query){
        query = query.trim()
        if(query != null && query != '')
        {
          const res = await filterKeyWordListAsync(query)
          this.filterKeyWordList = res.data
        }
        
        return
    },
    async remoteFilterProduct(query){
        query = query.trim()
         if(query != null && query != '')
        {
          const res = await filterProductAsync(query)
          this.productList = res.data
        }
        
        return
    },
    onClearFilter(){
        this.Filter.filterMaxMin = []
        this.onSearch()
    },
    async keyWordChange(val){
      this.Filter.keyWordIdUserList = []
      this.Filter.keyWordIdProjectList = []
      this.Filter.keyWordIdProductList = []
      this.keyWordUserList = []
      this.keyWordProjectList = []
      this.keyWordPorductList = []

      //根据当前跨的维度，查找不同数据
      switch(this.Filter.keyWordDim)
      {
        case 1:
          await this.getOperateSpcialListByKeyWord(val)
          break
        case 2:
          await this.getUser2ListByKeyWord(val)
          break
        case 3:
          await this.getProjectListByKeyWord(val)
          break
        case 4:
          await this.getProductListByKeyWord(val)
          break
      }
    },
    async dimGroupChange(val){
       this.keyWordList = []
       this.Filter.nameKeyWordList = []
       
       //当前小组下的关键词
       var ret = await getKeyWordListByGroupId(val)
       this.keyWordList = ret.data    
    },
  async dimOperateSpecialChange(val){
       this.keyWordList = []
       this.Filter.nameKeyWordList = []
       
       //当前运营专员下的关键词
       var ret = await getKeyWordListByOperateSpecialId(val)
       this.keyWordList = ret.data    
    },
    async dimUser2Change(val){
       this.keyWordList = []
       this.Filter.nameKeyWordList = []
       
       //当前车手下的关键词
       var ret = await getKeyWordListByUser2Id(val)
       this.keyWordList = ret.data    
    },
    async dimProjectChange(val){
       this.keyWordList = []
       this.Filter.nameKeyWordList = []
       
       //当前计划下的关键词
       var ret = await getKeyWordListByProjectId(val)
       this.keyWordList = ret.data    
    },
    async dimProductChange(val){
         this.keyWordList = []
       this.Filter.nameKeyWordList = []
       
       //当前计划下的关键词
       var ret = await getKeyWordListByProducttId(val)
       this.keyWordList = ret.data    
    },
    dimNameKeyWordListChange:function(val){
        for(var i = 0; i < val.length; i++)
        {
          //选取所有
          if(val[i] == "")
          {
            this.Filter.nameKeyWordList = [""]
            break
          }
        }
    },
    async keyWordDimChange(val){
        this.Filter.nameKeyWord = null
        this.Filter.keyWordIdUserList = []
        this.Filter.keyWordIdProjectList = []
        this.Filter.keyWordIdProductList = []
        this.keyWordUserList = []
        this.keyWordProjectList = []
        this.keyWordPorductList = []
        this.ZTCKeyWordList = []
    },
    async dimChange(val){
        //维度变化时，需要清空keyword
        this.Filter.nameKeyWordList = []
        this.keyWordList = []
        this.ZTCKeyWordList = []

        this.Filter.idDirectorGroup = null
        this.Filter.idOperateSpecial = null
        this.Filter.idUser2 = null
        this.Filter.idProject = null
        this.Filter.idProduct = null

       if(val == 0)
       {
           this.Filter.nameKeyWordList = ['']
       }
       else if(val == 1)
       {
          if(this.groupList == null || (this.groupList != null && this.groupList.length < 1))
          {
            await this.getGroupList()
          }
       }
       else if(val == 2 || val == 3)
        {
          if(this.userList == null || (this.userList != null && this.userList.length < 1))
          {
            await this.getUserList()
          }
        }
        else if(val == 4)
        {
          if(this.projectList == null || (this.projectList != null && this.projectList.length < 1))
          {
             await this.getProjectList()
          }
        }
        else if(val == 5)
        {
           if(this.productList == null || (this.productList != null && this.productList.length < 1))
           {
             // await this.getProductList()
           }
        }
        else if(val == 6)
        {
           this.Filter.keyWordDim = 1
           this.Filter.nameKeyWord = null
           this.Filter.keyWordIdUserList = []
           this.Filter.keyWordIdProjectList = []
           this.Filter.keyWordIdProductList = []
           this.keyWordUserList = []
           this.keyWordProjectList = []
           this.keyWordPorductList = []

           if(this.allKeyWordList == null || (this.allKeyWordList != null && this.allKeyWordList.length < 1))
           {
             await this.getAllKeyWordList()
           }
        }
    },
    getSummaries(param) {
        return []
        /*
        if(this.summaryarry == null)
        {
          return []
        }

        //需要考虑固定列、显示列的影响
        const sums = [];
        var fixedColumn = this.fixedColumn.slice(0)
        var showColumn = this.showColumn.slice(0)

        var columnSequence = []
        fixedColumn.sort()
        for(var i = 0; i < fixedColumn.length; i++)
        {
            columnSequence.push(fixedColumn[i])
        }

        //固定列与显示列之间会有重复
        showColumn.sort()
        var newArray = showColumn.filter(function(item){ return fixedColumn.indexOf(item) == -1})
        for(var i = 0; i < newArray.length; i++)
        {
            columnSequence.push(newArray[i])
        }

        console.log("showsecqueence " + columnSequence)

        return sums;
        */
      },
    headerClick(val){
        this.dialogVisibleCountShow = true
        this.currentFilterTypeId = val.typeId
        this.currentFilterTypeTitle = val.label
        this.currentFilterTypeMin = this.Filter.filterMaxMin[val.typeId]?.Min
        this.currentFilterTypeMax = this.Filter.filterMaxMin[val.typeId]?.Max
    },
    headerFilterConfirm(){
      this.Filter.filterMaxMin[this.currentFilterTypeId] = {}
      this.Filter.filterMaxMin[this.currentFilterTypeId].Min = this.currentFilterTypeMin
      this.Filter.filterMaxMin[this.currentFilterTypeId].Max = this.currentFilterTypeMax
      this.dialogVisibleCountShow = false
      this.onSearch()
    },

    //所属店铺列表
    async getShopList(){
      const res1 = await getAllShopList();
      this.shopList=res1.data;
      return;
    },
    //负责人列表
    async getUserList(){
      const res = await getDirectorList();
      this.userList = res.data;
      return;
    },
    //组长列表
    async getGroupList(){
      const res = await getDirectorGroupList();
      this.groupList = res.data;
      return;
    },
    //计划列表
    async getProjectList(){
       const res = await getAllProjectList()
       this.projectList = res.data
       return
    },
    //产品列表
    async getProductList(){
        const res = await getAllProductList()
        this.productList = res.data
        return
    },
    async getAllKeyWordList(){
        const res = await getAllKeyWordListAsync()
        this.allKeyWordList = res.data
        return
    },
    async getOperateSpcialListByKeyWord(val){
      //TODO 根据关键词，查找运营专员列表
      const res = await getOperateSpcialListByKeyWordAsync(val)
      this.keyWordUserList = res.data
      return
    },
    async getUser2ListByKeyWord(val){
      //TODO 根据关键词，查找车手列表
      const res = await getUser2ListByKeyWordAsync(val)
      this.keyWordUserList = res.data
      return
    },
    async getProjectListByKeyWord(val){
      //TODO 根据关键词，查找计划列表
      const res = await getProjectListByKeyWordAsync(val)
      this.keyWordProjectList = res.data
      return
    },
    async getProductListByKeyWord(val){
      //TODO 根据关键词，查找产品列表
      const res = await getProductListByKeyWordAsync(val)
      this.keyWordPorductList = res.data
      return
    },
    onImportSyj(){
      this.dialogVisibleSyj = true
    },
    async uploadFile2(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      const res = importZTCKeyWordAsync(form);
      this.$message({message: '上传成功,正在导入中...', type: "success"}); 
    },
    async uploadSuccess2(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
    },
    async onSubmitupload2() {
      this.$refs.upload2.submit()
    },
    async onRefreshTableChart(){
          var pieChart = document.getElementById("chartDivCell");
          var myChartPie = echarts.init(pieChart);
          myChartPie.clear();
         //向服务器请求数据，并绘图
          const para = {...this.Filter};
          if (this.Filter.timerange) {
            para.dateStart = this.Filter.timerange[0];
            para.dateEnd = this.Filter.timerange[1];
          }

          if(!(para.dateStart&&para.dateEnd)){
            this.$message({message:"请先选择日期！",type:"warning"});
            return;
          }

          const pieSumChart = await ztcKeyWordAnalysisWithCost(para);
            if (!pieSumChart?.code)  return; 
            if (!pieSumChart.data) {
            this.$message({message: "没有数据!",type: "warning",});
            return;
          }

          var optionPie = this.GetChartoptions(pieSumChart.data, para);

          optionPie && myChartPie.setOption(optionPie)
    },
    async onExport(){
        const para = {...this.Filter};
        if (this.Filter.timerange) {
          para.dateStart = this.Filter.timerange[0];
          para.dateEnd = this.Filter.timerange[1];
        }

        if(!(para.dateStart&&para.dateEnd)){
          this.$message({message:"请先选择日期！",type:"warning"});
          return;
        }
        
        

        var pager = this.$refs.pager.getPager();
        const params = {
          ...pager,
          ...this.pager,
          ...para,
        };

        var strJson = JSON.stringify(params)
       var res= await exportZtcKeyWordAnalysis(strJson);
      if(!res?.data)
      {
         return
      }
  
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download','关键词分析数据' +  new Date().toLocaleString() + '_.xlsx' )
      aLink.click()
    },
    onSearch(){
      this.$refs.table.clearSort()
      this.$refs.pager.setPage(1);
      this.pager.OrderBy = ""
      this.pager.IsAsc = false
      this.ZTCKeyWordList = []

      tableCols.forEach(element =>{
          element.display = false
          element.fixed = false
       })

       var fixedColumn = this.fixedColumn.slice(0)
        var showColumn = this.showColumn.slice(0)

         //显示全部
        if(showColumn.indexOf(999) != -1)
        {
          for(var i = 0; i < tableCols.length; i++)
          {
            tableCols[i].display = true
          }
        }
        else
        {
              var columnSequence = []
              fixedColumn.sort()
              for(var i = 0; i < fixedColumn.length; i++)
              {
                  columnSequence.push(fixedColumn[i])
              }

              //固定列与显示列之间会有重复
              showColumn.sort()
              var newArray = showColumn.filter(function(item){ return fixedColumn.indexOf(item) == -1})
              for(var i = 0; i < newArray.length; i++)
              {
                  columnSequence.push(newArray[i])
              }

              columnSequence.forEach(index =>{
              for(var i = 0; i < tableCols.length; i++)
              {
                if(tableCols[i].index == index)
                {
                  //展示
                  tableCols[i].display = true
                  break
                }
              }
              })
        }

          this.fixedColumn.forEach(index => {
            for(var i = 0; i < tableCols.length; i++)
            {
              if(tableCols[i].index == index)
              {
                tableCols[i].fixed = true
                break
              }
            }
          })

       this.getZTCKeyWordList();

       setTimeout(() => {
            this.$nextTick(() => {
                this.$refs.table.doLayout()
            })
        }, 300)
    },
    async getZTCKeyWordList(){
      const para = {...this.Filter};
      if (this.Filter.timerange) {
        para.dateStart = this.Filter.timerange[0];
        para.dateEnd = this.Filter.timerange[1];
      }

      if(!(para.dateStart&&para.dateEnd)){
        this.$message({message:"请先选择日期！",type:"warning"});
        return;
      }
      
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };


      this.listLoading = true;
      const res = await ztcKeyWordAnalysisTableAsync(params);
      this.listLoading = false;

      if (!res?.code)  return
      if (!res?.data) return
      
      if(!res?.data.list) return


      this.ZTCKeyWordList = res.data.list
      this.total = res.data.total
      //this.summaryarry=res.data.summary;
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>