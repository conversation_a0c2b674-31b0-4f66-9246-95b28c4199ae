<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                <el-button-group>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-radio-group v-model="Filter.internationalType" @change="onSearch()">
                            <el-radio-button :label="-1">全部</el-radio-button>
                            <el-radio-button :label="0">国内</el-radio-button>
                            <el-radio-button :label="1">跨境</el-radio-button>
                        </el-radio-group>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="开始创建时间" end-placeholder="结束创建时间" :picker-options="pickerOptions"
                            style="width: 250px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
                        </el-date-picker>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-date-picker v-model="timeRanges2" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="开始出现时间" end-placeholder="结束出现时间" :picker-options="pickerOptions2"
                            style="width: 250px;" :value-format="'yyyy-MM-dd'" @change="changeTime2">
                        </el-date-picker>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-date-picker v-model="timeRanges3" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="分配时间" end-placeholder="分配时间" :picker-options="pickerOptions3"
                            style="width: 250px;" :value-format="'yyyy-MM-dd'" @change="changeTime3">
                        </el-date-picker>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-date-picker v-model="timeRanges4" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="运营驳回时间" end-placeholder="运营驳回时间" :picker-options="pickerOptions4"
                            style="width: 250px;" :value-format="'yyyy-MM-dd'" @change="changeTime4">
                        </el-date-picker>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <!-- <label style="margin-left: 5px;">系列编码: </label> -->
                        <el-input v-model.trim="Filter.styleCode" type="text" maxlength="20" clearable
                            placeholder="请输入系列编码" style="width:160px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <!-- <el-input v-model.trim="Filter.createdUserName" type="text" maxlength="20" clearable placeholder="推荐人" style="width:120px;" /> -->
                        <el-select v-model="Filter.createdUserNameList" multiple collapse-tags clearable filterable
                            placeholder="推荐人" style="width: 160px">
                            <el-option v-for="(item) in hotSaleBrandPushNewCreatedUserNameList" :key="'tjr' + item.key"
                                :label="item.value" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <!-- <el-input v-model.trim="Filter.goodsCompeteId" type="text" maxlength="50" clearable
                            placeholder="竞品ID" style="width:120px;" /> -->
                        <inputYunhan :width="'180px'" ref="" :inputt.sync="Filter.goodsCompeteId" v-model.trim="Filter.goodsCompeteId" placeholder="竞品ID/若输入多条请摁回车" 
                            :clearabletext="true" :clearable="true" @callback="callback" title="竞品ID" :maxRows="1000" v-dialogDrag :valuedOpen="true">
                        </inputYunhan>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="Filter.goodsCompeteShortName" type="text" maxlength="100" clearable
                            placeholder="产品简称" style="width:120px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select :collapse-tags="true" v-model="Filter.qualificationList" clearable multiple
                            placeholder="已有资质" style="width: 210px">
                            <el-option label="无" value="无"></el-option>
                            <el-option label="质检报告" value="质检报告"></el-option>
                            <el-option label="食品级质检报告" value="食品级质检报告"></el-option>
                            <el-option label="全国工业生产许可证" value="全国工业生产许可证"></el-option>
                            <el-option label="化妆品生产许可证" value="化妆品生产许可证"></el-option>
                            <el-option label="化妆品备案信息" value="化妆品备案信息"></el-option>
                            <el-option label="农业生产许可证" value="农业生产许可证"></el-option>
                            <el-option label="农药登记证书" value="农药登记证书"></el-option>
                            <el-option label="肥料生产许可证" value="肥料生产许可证"></el-option>
                            <el-option label="3c认证报告" value="3c认证报告"></el-option>
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="Filter.groupPlatform" clearable filterable placeholder="运营平台"
                            style="width: 140px">
                            <el-option v-for="(item) in platformlist" :key="'p1p-' + item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <!-- <el-cascader class="custom-cascader" placeholder="产品类目" collapse-tags
                            style="width:350px;height:28px;" v-model="Filter.goodsCategorysList"
                            :options="goodsCategorysOptions" :props="goodsCategorysProps" filterable clearable>
                        </el-cascader> -->

                        <el-select v-model="Filter.goodsCategory" clearable filterable placeholder="产品类目"
                            style="width: 200px">
                            <el-option v-for="(item) in goodsCategoryBase" :key="item" :label="item" :value="item">
                            </el-option>
                        </el-select>

                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select style="width:120px" clearable v-model="Filter.isLastProfitSumDate"
                            :collapse-tags="true" placeholder="是否计算利润">
                            <el-option value="已计算利润" label="已计算利润" />
                            <el-option value="未计算利润" label="未计算利润" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select style="width:200px" clearable v-model="Filter.allDataStates" multiple
                            :collapse-tags="true" placeholder="选品状态">
                            <el-option v-for="item in AllDataStateOpts" :value="item.value" :label="item.label"
                                :key="'allDataStates' + item.value"></el-option>
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-tooltip class="item" effect="dark" content="此下拉框需去掉“未选品或已归档”的条件或选择“未选品或已归档”以外的选品状态后进行筛选"
                            placement="top">
                            <el-select style="width:120px" clearable v-model="Filter.proStatus" :collapse-tags="true"
                                placeholder="状态">
                                <el-option :key="99" :value="99" label="已进货"></el-option>
                                <el-option :key="100" :value="100" label="已上架(有销量)"></el-option>
                                <el-option :key="-10" :value="-10" label="已归档过"></el-option>
                            </el-select>
                        </el-tooltip>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="Filter.groupName" type="text" maxlength="50" clearable
                            placeholder="请输入组名" style="width:100px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="Filter.area" type="text" maxlength="50" clearable placeholder="请输入地区"
                            style="width:100px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-tooltip class="item" effect="dark" content="此文本框需去掉“未选品或已归档”的条件或选择“未选品或已归档”以外的选品状态后进行筛选"
                            placement="top">
                            <el-input v-model.trim="Filter.chooseUserName" type="text" maxlength="20" clearable
                                placeholder="请输入选品运营" style="width:120px;" />
                        </el-tooltip>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select style="width:80px" clearable v-model="Filter.isOneDistribution" :collapse-tags="true"
                            placeholder="代发">
                            <el-option :key="0" :value="0" label="否"></el-option>
                            <el-option :key="1" :value="1" label="是"></el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select style="width:100px" clearable v-model="Filter.isInvoicing" :collapse-tags="true"
                            placeholder="是否可开票">
                            <el-option :key="0" :value="0" label="否"></el-option>
                            <el-option :key="1" :value="1" label="是"></el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select style="width:120px" clearable v-model="Filter.refType" :collapse-tags="true"
                            placeholder="数据来源">
                            <el-option value=-1 label="采购手动新增"></el-option>
                            <el-option value=1 label="跨境运营选品"></el-option>
                            <el-option value=2 label="询价报价被采纳"></el-option>
                            <el-option value=3 label="运营询价新增"></el-option>
                            <el-option value=4 label="1688选品中心"></el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select style="width:160px" clearable multiple filterable remote reserve-keyword
                            placeholder="询价人" :remote-method="yunyingBoHuiMethod" :loading="yunyingBoHuiMethodLoading"
                            v-model="Filter.xjUserIds" :collapse-tags="true">
                            <el-option v-for="(item, i) in yunyingBoHuiMethodList"
                                :key="'yunyingBoHuiMethodList' + i + 1" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select style="width:120px" v-model="Filter.xjUserPlatform" placeholder="询价平台" clearable>
                            <el-option v-for="item in platformlist" :key="'xjpt-' + item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select style="width:120px" clearable v-model="Filter.status" :collapse-tags="true"
                            placeholder="数据状态">
                            <el-option value=1 label="新增询价/1688选品"></el-option>
                            <el-option value=5 label="采购自推"></el-option>
                            <el-option value=6 label="采购已认领"></el-option>
                            <el-option value=7 label="已指派采购"></el-option>
                            <el-option value=10 label="采购已计算利润"></el-option>
                            <el-option value=15 label="已分配运营"></el-option>
                            <el-option value=20 label="已添加选品"></el-option>
                            <el-option value=98 label="询价被驳回"></el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select style="width:160px" clearable filterable multiple v-model="Filter.selDirectorlist"
                            :collapse-tags="true" placeholder="选品人">
                            <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select style="width:160px" clearable multiple filterable remote reserve-keyword
                            placeholder="运营驳回人" :remote-method="yunyingBoHuiMethod" :loading="yunyingBoHuiMethodLoading"
                            v-model="Filter.xjFpBoHuiUserIds" :collapse-tags="true">
                            <el-option v-for="(item, i) in yunyingBoHuiMethodList"
                                :key="'yunyingBoHuiMethodList' + i + 1" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select style="width:120px" clearable v-model="Filter.isClose" placeholder="是否被采购归档">
                            <el-option :value=1 label="已被采购归档"></el-option>
                            <el-option :value=0 label="未被采购归档"></el-option>
                        </el-select>
                    </el-button>

                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="clearQueryParam">清空条件</el-button>
                    <el-button type="primary" @click="setExportCols">导出</el-button>
                    <el-button style="padding: 0;margin-left:20px;border: none;">
                        <el-dropdown size="mini" split-button type="primary" icon="el-icon-share"
                            @command="handleCommand">
                            批量操作
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item class="Batcoperation" style="padding: 0 25px"
                                    v-if="checkPermission('api:operatemanage:AllLink:DelSaleBrandPushNewById')"
                                    command="a">批量删除</el-dropdown-item>
                                <el-dropdown-item class="Batcoperation" style="padding: 0 25px"
                                    v-if="(selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'cg')"
                                    command="b">批量复制为跨境</el-dropdown-item>
                                <el-dropdown-item class="Batcoperation" style="padding: 0 25px"
                                    v-if="(selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'cg')"
                                    command="c">批量复制为国内</el-dropdown-item>
                                <el-dropdown-item class="Batcoperation" style="padding: 0 25px"
                                    v-if="checkPermission('api:operatemanage:AllLink:ZhiPaiSaleBrandPushNewXjById') && (selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'cg')"
                                    command="d">批量指派</el-dropdown-item>
                                <el-dropdown-item class="Batcoperation" style="padding: 0 25px"
                                    v-if="checkPermission('api:operatemanage:AllLink:GuiDangSaleBrandPushNewXjById') && (selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'cg')"
                                    command="e">批量归档</el-dropdown-item>
                                <el-dropdown-item class="Batcoperation" style="padding: 0 25px"
                                    v-if="checkPermission('api:operatemanage:AllLink:GuiDangHfSaleBrandPushNewXjById') && (selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'cg')"
                                    command="f">批量归档恢复</el-dropdown-item>
                                <!-- <el-dropdown-item class="Batcoperation" style="padding: 0 25px"
                                    v-if="checkPermission('api:operatemanage:AllLink:FenPeiSaleBrandPushNewXjById') && (selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'yy')"
                                    command="g">批量分配</el-dropdown-item> -->
                            </el-dropdown-menu>
                        </el-dropdown>
                    </el-button>
                    <el-button type="primary" @click="openChooseGoodsForm"
                        v-if="selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'cg' || checkPermission('HotSaleBrandPushListNewAdd')">新增选品</el-button>
                    <!-- <el-button type="primary" @click="openChooseGoodsFormXj"
                        v-if="(selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'yy')">运营询价</el-button> -->
                </el-button-group>

            </el-form>
        </template>
        <!-- 列表 -->
        <vxetablebase :id="'hotsalebrandpushlistnew202408041706_1'" ref="table" :that='that' :cstmExportFunc="onExport"
            :isIndex='true' :hasexpand='true' :hasexpandRight='true' @sortchange='sortchange' :tableData='tbdatalist'
            @select='selectchange' :isSelection='false' :isSelectColumn="true" :tableCols='tableCols'
            :loading="listLoading">
            <template slot="right">
                <vxe-column width="360" title="操作" fixed="right">
                    <template #default="{ row }">
                        <!-- <el-button type="text"
                            v-if="(selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'yy') && row.refType == 3 && row.status == 1"
                            @click="openChooseGoodsFormXj(row)">编辑询价</el-button>
                        <el-button type="text" style="color:red"
                            v-if="(selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'yy') && row.refType == 3 && row.status == 1"
                            @click="delrowXj(row.id)">删除询价</el-button> -->
                        <el-button type="text"
                            v-if="(selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'cg' || specialGroup) && (row.refType == 3 || row.refType == 4) && row.status == 1 "
                            @click="renlingrowXj(row.id)">认领</el-button>
                        <el-button type="text"
                            v-if="checkPermission('api:operatemanage:AllLink:ZhiPaiSaleBrandPushNewXjById') &&
                                (selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'cg') && (row.refType == 3 || row.refType == 4) && (row.status == 1 || row.status == 6 || row.status == 7)"
                            @click="zhipairowXj(row.id)">指派</el-button>
                        <!-- <el-button type="text"
                            v-if="(selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'cg') && (row.refType == 3 || row.refType == 4) && (row.status == 6 || row.status == 7 || row.status == 10)"
                            @click="bohuirowXj(row.id)">采购驳回</el-button> -->
                        <el-button type="text" 
                            v-if="(selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'yy') && row.refType != 3&& row.refType != 4 && row.status == 10"
                            @click="inquiryClaim(row.id)">运营认领</el-button>
                        <el-button type="text" 
                            v-if="(selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'cg') && (row.refType == 3 || row.refType == 4) && (row.status == 6 || row.status == 7 || row.status == 10)"
                            @click="brandInquiryReject(row.id)">询价驳回</el-button>

                        <el-button type="text" v-if="(selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'cg' || checkPermission('HotSaleBrandPushListNewAdd')) && row.status != 98"
                            @click="openChooseGoodsForm(row)">编辑</el-button>
                        <el-button type="text" v-if="(selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'cg') && row.status != 98"
                            @click="onEditCompeteGoodSKU(row, false)">计算利润</el-button>


                        <!-- <el-button type="text" v-if="(selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'yy') && row.status == 10 &&
                            checkPermission('api:operatemanage:AllLink:FenPeiSaleBrandPushNewXjById')"
                            @click="fenpeirowXj(row.id)">分配</el-button> -->
                        <el-button type="text"
                            v-if="(((selfInfo.otherRole == 'gly' ||row.exclude || specialGroup) || checkPermission('CGTXGroupPermissions')) && row.refType != 4 && row.status == 15)"
                            @click="yunyingBoHuirowXj(row.id)">运营驳回</el-button>
                        <el-button type="text" @click="Purchase(row)" v-if="row.lastProfitSumDate &&
                            ((selfInfo.otherRole == 'gly' ||row.exclude || specialGroup) || checkPermission('CGTXGroupPermissions'))">添加到已选品</el-button>

                        <el-button type="text" @click="showLog(row)">日志</el-button>
                        <el-button type="text"
                            v-if="(selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'cg') && row.isClose == 0 && checkPermission('api:operatemanage:AllLink:GuiDangSaleBrandPushNewXjById')"
                            @click="caigouGuiDangrowXj(row.id)">归档</el-button>

                        <el-button type="text"
                            v-if="(selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'cg') && row.isClose == 1 && checkPermission('api:operatemanage:AllLink:GuiDangHfSaleBrandPushNewXjById')"
                            @click="caigouGuiDangHfrowXj(row.id)">归档恢复</el-button>

                        <el-button v-if="row.isCanDel == 1 && checkPermission('api:operatemanage:AllLink:DelSaleBrandPushNewById') &&
                            (selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'cg') && row.status != 98" type="text" style="color:red"
                            @click="delrow(row.id)">删除</el-button>

                        <el-button type="text" @click="CopyForKuaJing(row.id)"
                            v-if="row.internationalType == 0 && !row.copyHotSaleBrandPushNewId && !row.oldHotSaleBrandPushNewId &&
                                row.refType != 3 && row.refType != 4 && (selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'cg')">复制为跨境</el-button>
                        <el-button type="text" @click="CopyForGuoNei(row.id)"
                            v-if="row.internationalType == 1 && !row.copyHotSaleBrandPushNewId && !row.oldHotSaleBrandPushNewId &&
                                row.refType != 3 && row.refType != 4 && (selfInfo.otherRole == 'gly' || selfInfo.otherRole == 'cg')">复制为国内</el-button>
                    </template>
                </vxe-column>

            </template>
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettbdatalist1" />
        </template>

        <el-dialog title="竞品利润" :visible.sync="dialogSkuVisible" width='80%' top="1vh" :close-on-click-modal="false"
            v-dialogDrag v-loading="dialogSkuLoading" element-loading-text="拼命加载中">
            <skuPage ref="skuPage" style="z-index:1000;" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogSkuVisible = false">取 消</el-button>
                    <!-- <el-button type="primary" @click="onSkuSave(false)" :loading="skuSaveLoading" v-if="skuSaveHiddle">保存草稿</el-button> -->
                    <el-button type="primary" @click="onSkuSave(true)" :loading="skuSaveLoading"
                        v-if="skuSaveHiddle">保存利润&关闭</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="竞品参谋数据" :visible.sync="dialogTmCmRefInfoVisible" width='95%' :close-on-click-modal="false"
            v-dialogDrag v-loading="true" element-loading-text="拼命加载中">
            <cmRefTmPage ref="cmRefTmPage" style="z-index:1000;width:100%" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogTmCmRefInfoVisible = false">关 闭</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="竞品参谋数据" :visible.sync="dialogPddCmRefInfoVisible" width='80%' :close-on-click-modal="false"
            v-dialogDrag v-loading="dialogPddCmLoading" element-loading-text="拼命加载中">
            <cmRefPddPage ref="cmRefPddPage" style="z-index:1000;height:610px;" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogPddCmRefInfoVisible = false">关 闭</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="竞品建编码" :visible.sync="dialogBuildGoodsDocListVisible" width='80%'
            :close-on-click-modal="false" v-dialogDrag v-loading="dialogBuildGoodsDocListLoading"
            element-loading-text="拼命加载中">
            <el-row
                style="height:35px;line-height:35px; font-size: 16px;font-weight: bold;color: black;margin-bottom: 2px;">
                <span>竞品信息：</span>
                <el-button type="primary" @click="onBuildGoodsDocAdd">新增</el-button>
            </el-row>
            <el-container>
                <el-main style="height:190px;">
                    <ces-table ref="tablejdmain" :that='that' :isIndex='true' :hasexpand='true' :tableData='jdmainlist'
                        :tableCols='jdmainTableCols' :loading="jdmainLoading" style="height:190px"
                        :isSelectColumn="false" @cellclick="onjdmainCellClick">
                    </ces-table>
                </el-main>
            </el-container>
            <el-row
                style="height:35px;line-height:35px; font-size: 16px;font-weight: bold;color: black; margin-top: 10px;margin-bottom: 2px;">
                <span>竞品商品明细信息：</span>
            </el-row>
            <el-container>
                <el-main style="height:350px;">
                    <ces-table ref="tablejddtl" :that='that' :isIndex='true' :hasexpand='true' :tableData='jddtllist'
                        :tableCols='jddtlTableCols' :loading="jddtlLoading" style="height:350px"
                        :isSelectColumn="false">
                    </ces-table>
                </el-main>
            </el-container>

        </el-dialog>
        <el-dialog title="竞品建编码" :visible.sync="dialogBuildGoodsDocVisible" append-to-body width='80%'
            :close-on-click-modal="false" v-dialogDrag v-loading="dialogBuildGoodsDocLoading"
            element-loading-text="拼命加载中">
            <buildGoodsDocPage ref="buildGoodsDocPage" style="z-index:1000;" @loaded="(d) => {
                if (d.auditState > 0) {
                    buildGoodsDocHiddle = false;
                }

            }" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogBuildGoodsDocVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onBuildGoodsDocSave(false)" :loading="buildGoodsDocLoading"
                        v-if="buildGoodsDocHiddle">保存&关闭</el-button>
                    <el-button type="primary" @click="onBuildGoodsDocSave(true)" :loading="buildGoodsDocLoading"
                        v-if="buildGoodsDocHiddle">保存&申请编码</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="日志" :visible.sync="dialogShowLog" width='80%' :close-on-click-modal="false" v-dialogDrag
            v-loading="logTableLoding" element-loading-text="拼命加载中">
            <el-row style="line-height:35px; font-size: 14px;color: black;margin-bottom: 2px;">
                <el-col :span="6" style="float: left; margin-left: 20px;">
                    <span style="font-weight: bold;">竞品Id：</span>
                    <span>{{ showLogData.goodsCompeteId }}</span>
                </el-col>
                <el-col :span="12" style="float: left;">
                    <span style="font-weight: bold;">竞品标题：</span>
                    <span>{{ showLogData.goodsCompeteName }}</span>
                </el-col>
                <!-- <el-col :span="4" style="float: left;">
                    <span style="font-weight: bold;">运营组：</span>
                    <span>{{ showLogData.groupName }}</span>
                </el-col> -->
            </el-row>
            <el-row>
                <el-container>
                    <el-main style="height:500px;">
                        <ces-table ref="tableLog" :that='that' :isIndex='false' :hasexpand='true'
                            :tableData='showLogData.logList' :tableCols='logTableCols' style="height:450px;"
                            :isSelectColumn="false">
                        </ces-table>
                        <my-pagination ref="logpager" :total="logtotal" :checked-count="sels.length"
                            @get-page="changepage" />
                    </el-main>
                </el-container>
            </el-row>
        </el-dialog>

        <el-dialog title="添加到已选品" :visible.sync="addChooseDialogInfo.visible" width="50%" :close-on-click-modal="false"
            v-dialogDrag v-loading="addChooseDialogInfo.vLoading" element-loading-text="拼命加载中">
            <el-form class="ad-form-query" :model="addChooseDialogInfo.addChooseFormData" ref="addChooseFormData"
                @submit.native.prevent label-width="100px" :rules="addChooseDialogInfo.addChooseFormRules">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="运营平台" prop="newPlatform">
                            <el-select v-model="addChooseDialogInfo.addChooseFormData.newPlatform"
                                placeholder="请选择要做的平台">
                                <el-option v-for="item in platformlist" :key="'np2f-' + item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="竞品ID" prop="goodsCompeteId">
                            <el-input v-model.trim="addChooseDialogInfo.addChooseFormData.goodsCompeteId"
                                @input="changeup" :maxlength="50" :minlength="4"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="竞品标题" prop="goodsCompeteName">
                            <el-input v-model.trim="addChooseDialogInfo.addChooseFormData.goodsCompeteName"
                                @input="changeup" :maxlength="100" :minlength="4"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="5">
                        <el-form-item label="竞品图片" prop="goodsCompeteImgUrl">
                            <YhImgUpload :value.sync="addChooseDialogInfo.addChooseFormData.goodsCompeteImgUrl">
                            </YhImgUpload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="15">
                        <!-- <el-button type="primary" @click="onUpdateImg1">上一张</el-button> -->
                        <el-button type="primary" @click="onUpdateImg"
                            v-if="updateImgInfo.maxselindex > 1">更换主图</el-button>
                        <!-- <el-button type="primary" @click="onUpdateImg2">下一张</el-button> -->
                        <el-select v-model="updateImgInfo.selindex" style="width:80px;margin-left: 5px;"
                            v-if="updateImgInfo.visible" @change="onupdateImgInfo">
                            <el-option :value="0" label="第1张" />
                            <el-option :value="1" label="第2张" v-if="updateImgInfo.maxselindex > 1" />
                            <el-option :value="2" label="第3张" v-if="updateImgInfo.maxselindex > 2" />
                            <el-option :value="3" label="第4张" v-if="updateImgInfo.maxselindex > 3" />
                            <el-option :value="4" label="第5张" v-if="updateImgInfo.maxselindex > 4" />
                            <el-option :value="5" label="第6张" v-if="updateImgInfo.maxselindex > 5" />
                            <el-option :value="6" label="第7张" v-if="updateImgInfo.maxselindex > 6" />
                            <el-option :value="7" label="第8张" v-if="updateImgInfo.maxselindex > 7" />
                            <el-option :value="8" label="第9张" v-if="updateImgInfo.maxselindex > 8" />
                        </el-select>
                    </el-col>
                </el-row>
                <el-row>
                    <!-- v-if="updateImgInfo.visible" -->
                    <el-col :span="24">
                        <el-form-item>
                            <YhImgUpload :value.sync="updateImgInfo.goodsImgUrl" :multiple="true" :limit="9"
                                :disabled="true" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注">
                            <el-input :value="addChooseDialogInfo.addChooseFormData.remark" @input="bzhuinput"
                                :maxlength="100"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="addChooseDialogInfo.visible = false">取消</el-button>
                    &nbsp;
                    <el-button type="primary" @click="onAddChooseSave" v-throttle="3000"
                        :loading="addChooseDialogInfo.vLoading">添加选品</el-button>
                </span>
            </template>
        </el-dialog>

        <!--创建任务-->
        <el-dialog :title="addTaskPageTitle" :visible.sync="addTask" width="80%" :close-on-click-modal="false"
            :key="addTaskOpentime" element-loading-text="拼命加载中" v-dialogDrag v-loading="addTaskLoading">

            <shootingvideotaskeditfrom :key="'shootingvideotaskeditfrom' + addTaskOpentime"
                ref="shootingvideotaskeditfrom" :taskUrgencyList="addTaskUrgencyList" :groupList="addTaskGroupList"
                :warehouselist="addTaskWarehouselist" :platformList="addTaskPlatformList" :islook='addTaskIslook'
                :onCloseAddForm="() => { addTask = false; onRefresh(); }">
            </shootingvideotaskeditfrom>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="addTask = false">取 消</el-button>
                    <my-confirm-button type="submit" @click="addTaskOnSubmit" v-show="!addTaskIslook" />
                </span>
            </template>
        </el-dialog>

        <el-dialog title="历史进货" :visible.sync="hotstockinapplylistVisible" append-to-body width='80%'
            :close-on-click-modal="false" v-dialogDrag>
            <hotstockinapplylist ref="hotstockinapplylistPage" :filter="hotstockinapplylistFilter"
                style="z-index:1000;" />
        </el-dialog>

        <el-dialog title="编辑选品" :visible.sync="addChooseFormNewshow" append-to-body width='80%'
            :close-on-click-modal="false" v-dialogDrag>
            <AddChooseFormNew @close="() => { addChooseFormNewshow = false }" @onSearch="onSearch" :isedit="true"
                ref="addChooseFormNewref" style="z-index:1000;" />
        </el-dialog>

        <el-dialog title="商品信息趋势图" :visible.sync="listTrendChart" width="70%" v-dialogDrag>
            <el-tabs v-model="tabularName" type="card" @tab-click="onListClickEvent(tabularrows, tabularcolumns)">
                <el-tab-pane label="商品信息" name="first">
                    <vxetablebase :id="'hotsalebrandpushlistnew202408041706_2'" ref="tabulartable" :that='that'
                        :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='tabularsortchange'
                        :tableData='tabulartableData' :tableCols='tabulartableCols' :isSelection="false"
                        :isSelectColumn="false" style="width: 100%;margin: 0" v-loading="tabularloading"
                        :height="'550px'">
                    </vxetablebase>
                    <my-pagination ref="pager" :total="tabulartotal" @page-change="tabularPagechange"
                        @size-change="tabularSizechange" />
                </el-tab-pane>
                <el-tab-pane label="趋势图" name="second">
                    <div style="width:100%;height:550px" v-loading="trendChartLoading">
                        <buschar v-if="quantityprocessed.visible" ref="buschar" :analysisData="quantityprocessed.data">
                        </buschar>
                    </div>
                </el-tab-pane>
            </el-tabs>
            <div style="position: absolute;top: 55px;right: 20px;">
                <el-button type="primary" @click="onSeriesCoding"
                    v-if="checkPermission('CommodityInformationAdd')">新增</el-button>
            </div>
        </el-dialog>

        <el-dialog title="新增" :visible.sync="addedCodingPopup" width="20%" v-dialogDrag append-to-body>
            <div style="height: 150px;margin-top: 10%;">
                <el-form :model="seriesencoding" :rules="seriesencodingrules" ref="seriesencoding" label-width="100px"
                    class="demo-ruleForm">
                    <el-form-item label="系列编码" prop="styleCode">
                        <el-input v-model="seriesencoding.styleCode" placeholder="请输入系列编码" maxlength="50" disabled
                            clearable style="width: 90%;" />
                    </el-form-item>
                    <el-form-item label="商品编码" prop="goodsCode">
                        <el-select v-model="seriesencoding.goodsCode" placeholder="请选择商品编码" clearable
                            style="width: 90%;">
                            <el-option v-for="(item) in commodityData" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="addedCodingPopup = false">取 消</el-button>
                <el-button type="primary" :loading="savingLoading" @click="codedPreservationMethod">{{ (savingLoading ?
                    '保存中' :
                    '保 存') }}</el-button>
            </span>
        </el-dialog>


        <el-dialog title="运营询价" :visible.sync="addChooseFormNewXjshow" append-to-body width='80%'
            :close-on-click-modal="false" v-dialogDrag>
            <AddChooseFormNewXj @close="() => { addChooseFormNewXjshow = false }" @onSearch="onSearch" :isedit="true"
                ref="addChooseFormNewXjref" style="z-index:1000;" />
        </el-dialog>


        <el-dialog :title="zhipaiCaiGouTitle" :visible.sync="zhipaiCaiGouVisible" width="20%" v-dialogDrag
            append-to-body :close-on-click-modal="false">
            <el-select v-model="zhipaiCaiGouSel" placeholder="请选择采购" clearable filterable style="width: 140px;">
                <el-option v-for="(item) in caiGouAllUserList" :key="'xzcg' + item.key" :label="item.value"
                    :value="item.key" />
            </el-select>
            <span slot="footer" class="dialog-footer">
                <el-button @click="zhipaiCaiGouVisible = false">取 消</el-button>
                <el-button type="primary" :loading="zhipaiCaiGouLoading" @click="zhipairowXjSave">确认指派</el-button>
            </span>
        </el-dialog>

        <el-dialog title="采购驳回询价" :visible.sync="caigouBoHuiVisible" width="20%" v-dialogDrag append-to-body
            :close-on-click-modal="false">
            <el-input type="textarea" placeholder="请输入驳回原因" v-model="caigouBoHuiCon" maxlength="200" show-word-limit
                rows="4" />
            <span slot="footer" class="dialog-footer">
                <el-button @click="caigouBoHuiVisible = false">取 消</el-button>
                <el-button type="primary" :loading="caigouBoHuiLoading" @click="bohuirowXjSave">确认驳回</el-button>
            </span>
        </el-dialog>

        <el-dialog title="采购驳回询价" :visible.sync="brandInquiryRejectVisible" with="20%" v-dialogDrag append-to-body :close-on-click-modal="false">
            <el-input type="textarea" placeholder="请输入驳回原因" v-model="rejectReason" maxlength="200" show-word-limit rows="4" style="margin-bottom: 5px;" />
            <aauploadimgFile ref="paymentInfoPictureList" :ispaste="true" :isuploadfile="false" :noDel="false" :accepttyes="accepttyes" :isImage="true"
                :uploadInfo="paymentInfoPictureList" :keys="[1, 1]" @callback="callBackPaymentPicture" :imgmaxsize="9" :limit="9" :multiple="true">
            </aauploadimgFile>
            <span slot="footer" class="dialog-footer">
                <el-button @click="brandInquiryRejectVisible = false">取 消</el-button>
                <el-button type="primary" :loading="brandInquiryRejectLoading" @click="btnBrandInquiryReject">确认驳回</el-button>
            </span>
        </el-dialog>

        <el-dialog :title="fenpeiYunYingTitle" :visible.sync="fenpeiYunYingVisible" width="25%" v-dialogDrag
            append-to-body :close-on-click-modal="false">
            运营组:
            <el-select v-model="fenpeiYunYingGroupSel" placeholder="运营组" clearable filterable style="width: 150px;"
                @change="fenpeirowGroupChange">
                <el-option v-for="(item) in fenpeiYunYingGroupList" :key="'fpyyz' + item.value" :label="item.label"
                    :value="item.value" />
            </el-select>
            运营:
            <el-select v-model="fenpeiYunYingSelId" placeholder="运营" clearable filterable style="width: 150px;"
                @change="fenpeirowYunYingChange">
                <el-option v-for="(item) in fenpeiYunYingList" :key="'fpyy' + item.key" :label="item.label"
                    :value="item.key" />
            </el-select>


            <span slot="footer" class="dialog-footer">
                <el-button @click="fenpeiYunYingVisible = false">取 消</el-button>
                <el-button type="primary" :loading="fenpeiYunYingLoading" @click="fenpeirowXjSave">确认分配</el-button>
            </span>
        </el-dialog>


        <el-dialog title="运营驳回分配" :visible.sync="yunyingBoHuiVisible" width="20%" v-dialogDrag append-to-body
            :close-on-click-modal="false">
            <el-input type="textarea" placeholder="请输入驳回原因" v-model="yunyingBoHuiCon" maxlength="200" show-word-limit
                rows="4" />
            <span slot="footer" class="dialog-footer">
                <el-button @click="yunyingBoHuiVisible = false">取 消</el-button>
                <el-button type="primary" :loading="yunyingBoHuiLoading" @click="yunyingBoHuirowXjSave">确认驳回</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>
<script>
import { getCurUserDepartmentName } from '@/api/operatemanage/base/dingdingShow'
import {
    pageHotSaleGoodsChooseAsync, isDoHotSaleGoodsAsync, getAllLinkPlantformsAsync,
    getAllLinkCategoryNamesAsyncByParent, goodsInfoQueryReqAsync, reqGoodsCmRefInfoQueryAsync, getHotGoodsCmRefInfoAsync,
    getAllHotGoodsWaitQueryAsync, getHotSaleGoodsEchartByDateAsync, getUserInfo,
    applyCompeteGoodSKU, registerSkuOrderAsync, dingDingApproval_BuildGoodsDoc, pageHotSaleGoodsBuildGoodsAsync, pageHotSaleGoodsBuildGoodsCodeAsync,
    DingDingApproval_BuildGoodsDocBeforConfirm, getHotSaleGoodsChooseLog, GetDataBeforeBuildGoodsMediaTask, setHotSaleBrandPushNewGoodsDel, getHotSaleBrandPushNewGoodsPage,
    getDelGoodsCode, addGoodsCodeToBrandPushNew
} from '@/api/operatemanage/productalllink/alllink'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import aauploadimgFile from "@/components/Comm/uploadimgFile.vue";
import {
    formatmoney, formatPercen, getUrlParam, platformlist,
    formatPlatform, formatTime, setStore, getStore,
    formatLinkProCode, sendWarehouse4HotGoodsBuildGoodsDocList,
    pickerOptions, fmtAllLinkChooseProfitState, AllLinkChooseProfitStateOpts,
    ShootingVideoTaskUrgencyOptions,
} from "@/utils/tools";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import * as echarts from 'echarts';
import buschar from '@/components/Bus/buschar';
import skuPage from '@/views/operatemanage/productalllink/hotsale/hotsalegoodschooselistsku.vue';
import cmRefTmPage from '@/views/operatemanage/productalllink/hotsalegoodscmrefinfotmall.vue';
import cmRefPddPage from '@/views/operatemanage/productalllink/hotsalegoodscmrefinfopdd.vue';

import buildGoodsDocPage from '@/views/operatemanage/productalllink/hotsale/hotsalegoodsbuildgoodsdoc.vue';
import hotsalegoodspurchasegoods from '@/views/operatemanage/productalllink/hotsale/hotsalegoodspurchasegoods.vue'
import hotstockinapplylist from '@/views/operatemanage/productalllink/hotsale/hotstockinapplylist.vue'
import { assertRegexLiteral } from '@babel/types';

import YhImgUpload from '@/components/upload/yh-img-upload.vue';

import { getDirectorGroupList, GetDirectorAllList, getDirectorList } from '@/api/operatemanage/base/shop';
import shootingvideotaskeditfrom from '@/views/media/shooting/shootingvideotaskeditfromNew'
import { getShootOperationsGroup, getOperationsGroup, getErpUserInfoView } from '@/api/media/mediashare';
import { rulePlatform } from "@/utils/formruletools";

import AddChooseFormNew from '@/views/operatemanage/productalllink/hotsale/AddChooseFormNew.vue';
import AddChooseFormNewXj from '@/views/operatemanage/productalllink/hotsale/AddChooseFormNewXj.vue';
import {
    pageHotSaleBrandPushNew, addChooseByHotSaleBrandPushNew,
    getAllBrandProductCategorys, getBrandProductCategory, getAllBrandProductCategorysCascader, getHotSaleBrandPushNewCreatedUserNameList,
    saveHotSaleBrandPushNewCalProfit, getHotSaleBrandPushNewById, pageHotSaleBrandPushNewLog, exportHotSaleBrandPushNew
    , delSaleBrandPushNewById, getAnalysisResponse,
    hotSaleBrandPushNewCopyForKuaJing,
    hotSaleBrandPushNewCopyForGuoNei,
    getBrandAllUserList, getYunYingAllUserList,
    delSaleBrandPushNewXjById, renLingSaleBrandPushNewXjById, zhiPaiSaleBrandPushNewXjById, boHuiSaleBrandPushNewXjById,
    fenPeiSaleBrandPushNewXjById, yunYingBoHuiSaleBrandPushNewXjById,
    guiDangSaleBrandPushNewXjById, guiDangHfSaleBrandPushNewXjById, directorClaimHotSaleBrandPushNew
} from '@/api/operatemanage/productalllink/LogisticsAnalyse.js';

import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { re } from 'mathjs';
import inputYunhan from "@/components/Comm/inputYunhan";

const taskPageParams = {
    addTaskPageTitle: "创建任务",
    addTask: false,
    addTaskOpentime: 0,
    addTaskLoading: false,
    addTaskUrgencyList: ShootingVideoTaskUrgencyOptions,
    addTaskGroupList: [],
    addTaskWarehouselist: [],
    addTaskPlatformList: [],
    addTaskIslook: false,
};

const formatLinkProCodeurl = (value, url) => {
    var proBaseUrl = url;
    if (proBaseUrl)
        return formatLink(value, proBaseUrl);
    return value;
}

const nameadd = (value, name) => {
    // let bf = value?value:''+name?name:'';
    return value + name;
}

const bmnameadd = (value, name) => {
    // let allname = value?value:''+'-'+ name?name:'';
    // return allname=='-'?'':allname;

    // return value?value:''+'-'+ name?name:'';
    let allname = '';
    if (value) {
        allname = value
    } else {
        allname = ''
    }
    if (name) {
        allname = allname + '-' + name
    } else {
        allname = allname
    }
    return allname;
}


const formatLink = (value, url) => {
    if (!value) return ''
    if (!url) return value;

    var html = `<a href="${url}" target="_blank" style="color: #1000ff;">${value}</a>`;
    // var html= `<el-link type="primary" href="${url}" target="_blank">${value}</el-link>`;
    return html
}

function formatInfoQueryState(val) {
    //-1同步失败、0未申请、1申请中、2同步中、3已同步
    switch (val) {
        case 0:
            return "未申请";
        case 1:
            return "申请中";
        case 2:
            return "同步中";
        case 3:
            return "存在";
        case -1:
            return "同步失败";
        default:
            return "";
    }
}

//选品全状态
const AllDataStateOpts = [
    { label: "未被选品或已归档", value: -20 },
    { label: "已选品", value: 0 },
    // { label: "数据申请中", value: 10 },
    // { label: "数据已同步", value: 20 },
    // { label: "已计算利润", value: 30 },
    //{label:"已登记采样",value:40},
    { label: "组长确认中", value: 45 },
    { label: "组长已拒绝", value: 46 },
    { label: "组长已确认", value: 47 },

    { label: "编码已拒绝", value: 51 },
    { label: "编码已申请", value: 60 },
    { label: "编码已审核", value: 70 },
    // { label: "已上架", value: 100 },

    // { label: "已归档", value: -10 },//已归档，单独处理
]

const tableCols = [
    { istrue: true, label: '', type: "checkbox" },
    //{ istrue: true, prop: 'id', label: '主键', width: '200', display: false },
    {
        istrue: true, prop: 'goodsCompeteId', label: 'ID', width: '110', sortable: 'custom', tipmesg: '包括竞品ID、商品ID', type: 'html',
        formatter: (row) => formatLinkProCode(row.platform, row.goodsCompeteId)
    },
    {
        istrue: true, prop: 'kuaJingGuoNei', label: '国内跨境', width: '95', formatter: (row) => {
            if (row.oldHotSaleBrandPushNewId || row.copyHotSaleBrandPushNewId) {
                return '全部'
            } else {
                if (row.internationalType === 1) {
                    return '跨境'
                } else {
                    return '国内'
                }
            }
        }
    },
    {
        istrue: true, prop: 'status', label: '数据状态', width: '120', sortable: 'custom', exportField: 'statusName',
        formatter: (row) => (row.isClose == 1 ? "采购归档" : row.statusName)
    },
    { istrue: true, prop: 'appearTime', label: '出现时间', width: '95', sortable: 'custom' },
    {
        istrue: true, prop: 'styleCode', label: '系列编码', width: '80', sortable: 'custom', align: 'center',
    },
    { istrue: true, prop: 'goodsCategory', label: '产品类目', width: '80', sortable: 'custom', align: 'center', },
    { istrue: true, prop: 'categoryLeve1', label: '一级类目', width: '100', sortable: 'custom', align: 'center', },
    { istrue: true, prop: 'categoryLeve2', label: '二级类目', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'goodsCompeteShortName', label: '产品简称', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'goodsCompeteImgUrl', label: '竞品图片', width: '80', type: 'images', align: 'center' },
    {
        istrue: true, prop: 'outerPackaLanguage', label: '外包装语言', width: '80', exportField: 'outerPackaLanguageName',
        formatter: (row) => (row.outerPackaLanguage == -1 ? "无" : row.outerPackaLanguage == 1 ? "中文" : row.outerPackaLanguage == 2 ? "英文" : "")
    },

    { istrue: true, prop: 'xjUserId', label: '询价运营', sortable: 'custom', width: '95', exportField: 'xjUserName', formatter: (row) => row.xjUserName },
    { istrue: true, prop: 'xjUserPlatform', label: '询价平台', sortable: 'custom', width: '95', exportField: 'xjUserPlatformName', formatter: (row) => row.xjUserPlatformName },
    {
        istrue: true, prop: 'exMinPrice', label: '期望价格', width: '120', sortable: 'custom', exportField: 'qwjg',
        formatter: (row) => ((row.exMinPrice && row.exMaxPrice) ? (row.exMinPrice.toString() + '~' + row.exMaxPrice.toString()) : "")
    },

    { istrue: true, prop: 'costPrice', label: '成本价', width: '100', sortable: 'custom', classname: "xAllDataStateCol202301111351001", align: 'center' },
    { istrue: true, prop: 'salePrice', label: '售价', width: '70', sortable: 'custom' },
    { istrue: true, prop: 'qualifications', label: '资质', width: '70', sortable: 'custom' },
    { istrue: true, prop: 'patentType', label: '专利类型', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'isInvoicing', label: '是否可开票', width: '100', sortable: 'custom', formatter: (row) => row.isInvoicing == 0 ? "否" : row.isInvoicing == 1 ? "是" : "" },
    { istrue: true, prop: 'isInvoicingRate', label: '税点%', width: '70', sortable: 'custom', formatter: (row) => row.isInvoicingRate ? row.isInvoicingRate.toString() + '%' : "" },
    { istrue: true, prop: 'isOneDistribution', label: '是否支持一键代发', width: '170', sortable: 'custom', formatter: (row) => (row.isOneDistribution == 1 || row.isOneDistribution == "1") ? "是" : (row.isOneDistribution == 0 || row.isOneDistribution == "0") ? "否" : "" },
    { istrue: true, prop: 'remark', label: '备注', width: '100', sortable: 'custom' },
    {
        istrue: true, prop: 'refType', label: '数据来源', width: '100', sortable: 'custom',
        formatter: (row) => (row.refType == 1 ? "跨境运营选品" : (row.refType == 2 ? "询价报价被采纳" : (row.refType == 3 ? "运营询价新增" : (row.refType == 4 ? "1688选品中心" :"手动新增"))))
    },
    { istrue: true, prop: 'profitRate', label: '利润率', width: '100', sortable: 'custom', formatter: (row) => nameadd(row.profitRate, '%') },
    { istrue: true, prop: 'pddPrice', label: '拼多多', width: '100', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCodeurl(row.pddPrice, row.pddLink) },
    { istrue: true, prop: 'dyPrice', label: '抖音', width: '100', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCodeurl(row.dyPrice, row.dyLink) },
    { istrue: true, prop: 'tbPrice', label: '淘宝', width: '100', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCodeurl(row.tbPrice, row.tbLink) },
    { istrue: true, prop: 'skuCount', label: 'SKU数量', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'createdUserName', label: '推荐人', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'createdUserDept', label: '部门', width: '200', formatter: (row) => bmnameadd(row.createUserDeptName, row.createUserRole) },
    { istrue: true, prop: 'createdTime', label: '创建时间', width: '150', sortable: 'custom' },

    // { istrue: true, prop: 'xjFpUserId', label: '分配人', width: '100', sortable: 'custom', exportField: 'xjFpUserName', formatter: (row) => row.xjFpUserName },
    { istrue: true, prop: 'xjFpUseGroup', label: '选品组', width: '100', sortable: 'custom', exportField: 'xjFpUseGroupName', formatter: (row) => row.xjFpUseGroupName },
    { istrue: true, prop: 'xjFpUseUserId', label: '选品人', width: '100', sortable: 'custom', exportField: 'xjFpUseUserName', formatter: (row) => row.xjFpUseUserName },
    { istrue: true, prop: 'xjFpBoHuiUserId', label: '询价驳回人', width: '100', sortable: 'custom', exportField: 'xjFpBoHuiUserName', formatter: (row) => row.xjFpBoHuiUserName },
    { istrue: true, prop: 'xjFpTime', label: '选品时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'xjFpBoHuiTime', label: '询价驳回时间', width: '150', sortable: 'custom' },

    { istrue: true, prop: 'goodsCompetePlatform', label: '竞品平台', width: '80', sortable: 'custom', formatter: row => formatPlatform(row.goodsCompetePlatform), exportField: 'goodsCompetePlatformName' },
    { istrue: true, prop: 'goodsCompeteShopName', label: '竞品店铺名称', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'goodsCompeteGgCostPrice', label: '产品规格成本', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'goodsCompeteCccb', label: '出仓成本', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'goodsCompetePlatformKdRate', label: '平台扣点比例%', width: '100', sortable: 'custom', formatter: (row) => row.goodsCompetePlatformKdRate ? row.goodsCompetePlatformKdRate.toString() + '%' : "" },
    { istrue: true, prop: 'goodsCompetePlatformKdAmount', label: '平台扣点金额', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'goodsCompetePlatformFkRate', label: '平台罚款比例%', width: '100', sortable: 'custom', formatter: (row) => row.goodsCompetePlatformFkRate ? row.goodsCompetePlatformFkRate.toString() + '%' : "" },
    { istrue: true, prop: 'goodsCompetePlatformFkAmount', label: '平台罚款金额', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'goodsCompeteFhhtkRate', label: '发货后退款比例%', width: '100', sortable: 'custom', formatter: (row) => row.goodsCompeteFhhtkRate ? row.goodsCompeteFhhtkRate.toString() + '%' : "" },
    { istrue: true, prop: 'goodsCompeteFhhtkAmount', label: '发货后退款金额', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'goodsCompeteJhyfRate', label: '进货运费比例%', width: '100', sortable: 'custom', formatter: (row) => row.goodsCompeteJhyfRate ? row.goodsCompeteJhyfRate.toString() + '%' : "" },
    { istrue: true, prop: 'goodsCompeteJhyfAmount', label: '进货运费金额', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'goodsCompeteJhThShRate', label: '进货退货损耗比例%', width: '120', sortable: 'custom', formatter: (row) => row.goodsCompeteJhThShRate ? row.goodsCompeteJhThShRate.toString() + '%' : "" },
    { istrue: true, prop: 'goodsCompeteJhThShAmount', label: '进货退货损耗金额', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'goodsCompeteLrWc', label: '利润(外仓)', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'goodsCompeteLrlWc', label: '利润率%(外仓)', width: '100', sortable: 'custom', formatter: (row) => row.goodsCompeteLrlWc ? row.goodsCompeteLrlWc.toString() + '%' : "" },

    { istrue: true, prop: 'allDataStateTxt', label: '最近状态', width: '95', },
    { istrue: true, prop: 'chooseUserName', label: '最新添加人', width: '95', sortable: 'custom' },
    { istrue: true, prop: 'chooseGroupName', label: '添加人小组', width: '95', sortable: 'custom' },
    { istrue: true, prop: 'userPlatform', label: '添加人运营平台', width: '120', sortable: 'custom', formatter: row => formatPlatform(row.userPlatform) },
    { istrue: true, prop: 'chooseDate', label: '最新添加时间', width: '150', sortable: 'custom' },

    // { istrue: true, prop: 'chooseRemark', label: '平台？？？', width: '100', sortable: 'custom' },
    // { istrue: true, prop: 'chooseRemark', label: '运营小组', width: '100', sortable: 'custom' },
    // { istrue: true, prop: 'chooseRemark', label: '编码创建人', width: '100', sortable: 'custom' },
    // { istrue: true, prop: 'chooseRemark', label: '领取时间', width: '100', sortable: 'custom' },
    // { istrue: true, prop: 'chooseRemark', label: '领取后状态', width: '100', sortable: 'custom' },
    // { istrue: true, prop: 'chooseRemark', label: '月销售量汇总', width: '100', sortable: 'custom' },

    { istrue: true, prop: 'totalSales', label: '总销量', width: '95', sortable: 'custom', type: 'click', handle: (that, row, column) => that.onListClickEvent(row, column.prop) },
    { istrue: true, prop: 'totalSaleAmount', label: '总销售额', width: '95', sortable: 'custom', type: 'click', handle: (that, row, column) => that.onListClickEvent(row, column.prop) },
    { istrue: true, prop: 'totalLastMonthSaleCount', label: '月销售量汇总', props: 'platformSaleInfos', width: '100', type: 'xptooltip', trendChart: true, handle: (that, row, column) => that.onListClickEvent(row, column.prop) },
    { istrue: true, prop: 'totalLastMonthSaleAmount', label: '月销售额汇总', props: 'platformSaleInfos', width: '100', type: 'xptooltip', trendChart: true, handle: (that, row, column) => that.onListClickEvent(row, column.prop) },
    { istrue: true, prop: 'totalLastMonthProfitAmount', label: '毛三利润', width: '95', sortable: 'custom', type: 'click', handle: (that, row, column) => that.onListClickEvent(row, column.prop) },
    { istrue: true, prop: 'totalLastMonthProfitRate', label: '毛三利率', width: '95', sortable: 'custom', type: 'click', formatter: (row) => row.totalLastMonthProfitRate ? row.totalLastMonthProfitRate + '%' : '', handle: (that, row, column) => that.onListClickEvent(row, column.prop) },
];
const jdmainTableCols = [
    { istrue: true, prop: 'id', label: '选品建编码主键', width: '160', display: false },
    { istrue: true, prop: 'hotSaleGoodsChooseId', label: '选品主键', width: '160', display: false },
    { istrue: true, prop: 'goodsCompeteId', label: '竞品ID', width: '120' },
    { istrue: true, prop: 'goodsCompeteName', label: '竞品标题' },
    { istrue: true, prop: 'goodsCompeteShortName', label: '产品简称' },
    { istrue: true, prop: 'goodsCompeteImgUrl', label: '竞品图片', width: '80', type: 'image' },
    { istrue: true, prop: 'platform', label: '平台', width: '70', formatter: (row) => row.platformName || ' ' },
    { istrue: true, prop: 'yyGroupName', label: '运营组', width: '70' },
    { istrue: true, prop: 'patentQualificationImgUrls', label: '专利资质', width: '80', type: 'images' },
    { istrue: true, prop: 'patentQualificationPdfUrls', label: '专利PDF', width: '80', type: 'files' },
    { istrue: true, prop: 'packingImgUrls', label: '包装图片', width: '80', type: 'images' },
    { istrue: true, prop: 'submitUserName', label: '提交人', width: '70' },
    { istrue: true, prop: 'submitTime', label: '提交时间', width: '150', formatter: (row) => formatTime(row.submitTime, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'applyUserName', label: '申请人', width: '70' },
    { istrue: true, prop: 'applyTime', label: '申请时间', width: '150', formatter: (row) => formatTime(row.applyTime, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'auditState', label: '状态', width: '80', formatter: (row) => row.auditStateName || ' ' },
    { istrue: true, prop: 'auditTime', label: '审核时间', width: '150', formatter: (row) => formatTime(row.auditTime, 'YYYY-MM-DD HH:mm:ss') },
    {
        istrue: true, type: 'button', label: '操作', width: '150',
        btnList: [
            {
                label: "编辑", handle: (that, row) => that.onEditBuildGoodsDoc(row, true), permission: "api:operatemanage:alllink:BuildGoodsDocAsync",
                ishide: (that, row) => (row.auditState > 0)
            },
            {
                label: "申请编码", handle: (that, row) => that.onApprovalGoodsDoc(row), permission: "api:operatemanage:alllink:ApplyBuildGoodsDocCodeAsync",
                ishide: (that, row) => (row.auditState > 0)
            },
            {
                label: "查看", handle: (that, row) => that.onEditBuildGoodsDoc(row, false)
            },
        ]
    }
];
const jddtlTableCols = [
    { istrue: true, prop: 'id', label: '选品建编码明细主键', width: '160', display: false },
    { istrue: true, prop: 'parentId', label: '选品建编码主键', width: '160', display: false },
    // { istrue: true, prop: 'yhGoodsCode', label: '商品编码', width: '160' },//
    { istrue: true, prop: 'yhGoodsName', label: '商品名称' },
    //{ istrue: true, prop: 'yhGoodsUnit', label: '品名单位', width: '80' },
    { istrue: true, prop: 'costPrice', label: '成本单价', width: '120' },
    { istrue: true, prop: 'forNewWarehouse', type: "select", label: '上新仓库', width: '120', isDisabled: (row) => true, options: sendWarehouse4HotGoodsBuildGoodsDocList },
    { istrue: true, prop: 'goodsProgressType', type: "select", label: '商品类型', width: '120', isDisabled: (row) => true, options: [{ label: '成品', value: '成品' }, { label: '半成品', value: '半成品' }] },
    { istrue: true, prop: 'isMainSale', type: "select", label: '是否主卖', width: '120', isDisabled: (row) => true, options: [{ label: '是', value: true }, { label: '否', value: false }] },
    { istrue: true, prop: 'estimateStockInCount', label: '预计进货数量', width: '120' },
    { istrue: true, prop: 'estimateStockInAmount', label: '预计进货金额', width: '120' },
    { istrue: true, prop: 'remark', label: '备注', },

];
const logTableCols = [
    { istrue: true, prop: 'id', label: '日志主键', width: '160', display: false },
    { istrue: true, prop: 'logContent', label: '事件', align: 'center' },
    { istrue: true, prop: 'opearTime', label: '日期', align: 'center', formatter: (row) => formatTime(row.opearTime, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'opearName', label: '操作人', align: 'center' }
]

const tabulartableCols = [
    { istrue: true, prop: 'styleCode', label: '系列编码', width: 'auto', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: 'auto', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'totalSales', label: '总销量', width: 'auto', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'totalSaleAmount', label: '总销售额', width: 'auto', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'day30Sales', label: '月销售量汇总', width: 'auto', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'day30SalesAmount', label: '月销售额汇总', width: 'auto', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'day30Profit3', label: '毛三利润', width: 'auto', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'day30Profit3Rate', label: '毛三利率', width: 'auto', align: 'center', sortable: 'custom', formatter: (row) => (row.day30Profit3Rate || row.day30Profit3Rate == 0) ? row.day30Profit3Rate + '%' : " " },
    { istrue: true, prop: 'isDelete', label: '状态', align: 'center', sortable: 'custom', formatter: (row) => row.isDelete ? '已删除' : "正常" },
    {
        istrue: true, type: 'button', label: '操作', width: '150',
        btnList: [
            {
                label: "删除", handle: (that, row) => that.ondeleteOperation(row), ishide: (that, row) => (row.isDelete == true), permission: "Api:OperateManage:AllLink:SetHotSaleBrandPushNewGoodsDel"
            },
            {
                label: "恢复", handle: (that, row) => that.ondeleteOperation(row), ishide: (that, row) => (row.isDelete == false), permission: "Api:OperateManage:AllLink:SetHotSaleBrandPushNewGoodsDel"
            },
        ]
    }
]

const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");
const star = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");

export default {
    name: "hotsalebrandpushlistnew",
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, skuPage, cesTable, AddChooseFormNew, AddChooseFormNewXj,aauploadimgFile,
        buschar, cmRefTmPage, cmRefPddPage, buildGoodsDocPage, YhImgUpload, shootingvideotaskeditfrom, hotsalegoodspurchasegoods, hotstockinapplylist, vxetablebase, inputYunhan
    },
    data() {
        return {
            goodsCategoryBase: ['收纳整理/箱包皮具', '艺术收藏用品', '服饰配件', '清洁用品', '运动健身用品', '户外渔具', '生活工具', '节庆用品礼品', '3C数码配件丶电器', '厨房用品',
                '孕产妇/婴童用品', '五金建材工具/仪器仪表', '宠物用品', '载具用品', '办公文具', '美妆美容美发美体用品', '饰品装饰', '玩具动漫周边', '居家布艺'],
            directorlist: [],
            seriesencodingrules: {
                styleCode: [
                    { required: true, message: '请输入系列编码', trigger: 'blur' },
                ],
                goodsCode: [
                    { required: true, message: '请选择商品编码', trigger: 'change' }
                ],
            },
            commodityData: [],
            seriesencoding: {
                styleCode: null,
                goodsCode: null,
            },
            savingLoading: false,
            addedCodingPopup: false,
            trendChartLoading: false,//趋势图加载
            tabularloading: false,//表格加载
            tabularrows: {},//表格数据
            tabularcolumns: {},//表格列
            tabulartotal: 0,//表格总数
            commodityInformation: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
            tabularName: 'first',//表格名称
            tabulartableData: [],//表格数据
            tabulartableCols,//表格列
            listTrendChart: false,//趋势图
            quantityprocessed: { visible: false, title: "", data: {} },
            that: this,
            addChooseFormNewshow: false,
            hotSaleBrandPushNewId: '',
            AllLinkChooseProfitStateOpts: AllLinkChooseProfitStateOpts,
            AllDataStateOpts: AllDataStateOpts,
            pickerOptions: pickerOptions,
            pickerOptions2: pickerOptions,
            pickerOptions3: pickerOptions,
            pickerOptions4: pickerOptions,
            categoryall: {
                categoryone: [],
                categorytwo: [],
                categorythr: [],
            },
            updateImgInfo: {
                visible: false,
                selindex: 0,
                maxselindex: 1,
                goodsImgUrl: null,
                goodsImgUrlList: [],
            },
            buscharDialog: { visible: false, isNot7Day: true, title: "", data: [] },
            goodsCategorysProps: { multiple: true, expandTrigger: 'hover' },
            goodsCategorysOptions: [],
            Filter: {
                isClose: 0,
                internationalType: -1,
                platform: null,
                newPlatform: null,
                goodsCategory: '',
                categoryLeve1: '',
                categoryLeve2: '',
                allDataStates: [-20],
                qualificationList: [],
                createdUserNameList: [],
                groupName: null,
                area: null,
                startDate: null,
                endDate: null,
                proStatus: null,
                chooseUserName: null,
                isOneDistribution: null,
                appearStartDate: null,
                appearEndDate: null,
                fpStartDate: null,
                fpEndDate: null,
                yybhStartDate: null,
                yybhEndDate: null,
                goodsCategorysList: [],
                groupPlatform: null,
                goodsCompeteId: null,
                selDirectorlist: [],
            },
            addChooseDialogInfo: {
                visible: false,
                addChooseFormData: {}
            },
            logfliter: {
                "currentPage": 1,
                "pageSize": 50,
                "isAsc": true,
            },
            platformlist: platformlist,
            logtotal: 0,
            shopList: [],
            userList: [],
            groupList: [],
            tbdatalist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            //
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            hotSaleBrandPushNewCreatedUserNameList: [],
            plantformList: [],
            categoryNameList1: [],
            categoryNameList2: [],
            categoryNameList3: [],
            categoryNameList4: [],
            echartsLoading: false,
            goodsEchartDtlFilter: {
                goodsId: null,
                startTime: null,
                endTime: null,
                timerange: [star, endTime]
            },
            dialogSkuVisible: false,
            dialogSkuLoading: false,
            skuSaveLoading: false,
            skuSaveHiddle: false,

            dialogBuildGoodsDocVisible: false,
            dialogBuildGoodsDocLoading: false,
            buildGoodsDocLoading: false,
            buildGoodsDocHiddle: false,
            dialogTmCmRefInfoVisible: false,
            selfInfo: {
                otherRole: "",
            },
            dialogPddCmRefInfoVisible: false,
            dialogPddCmLoading: false,
            timeRanges: [],
            timeRanges2: [],
            timeRanges3: [],
            timeRanges4: [],
            selHotSaleGoodsChooseId: 0,
            dialogBuildGoodsDocListVisible: false,
            dialogBuildGoodsDocListLoading: false,
            jdmainLoading: false,
            jdmainlist: [],
            jdmainTableCols: jdmainTableCols,
            jddtlLoading: false,
            jddtllist: [],
            jddtlTableCols: jddtlTableCols,
            seldocId: null,
            //日志
            dialogShowLog: false,
            logTableCols: logTableCols,
            logTableLoding: false,
            showLogData: {
                goodsCompeteId: '',
                goodsCompeteName: '',
                groupName: '',
                logList: []
            },
            //进货
            hotstockinapplylistVisible: false,
            hotstockinapplylistFilter: {
                chooseId: ""
            },
            //拍摄任务
            ...taskPageParams,

            addChooseFormNewXjshow: false,

            caiGouAllUserList: [],
            zhipaiCaiGouTitle: "",
            zhipaiCaiGouVisible: false,
            zhipaiCaiGouLoading: false,
            zhipaiCaiGouSel: null,
            zhipaiCaiGouSelMainId: null,

            caigouBoHuiVisible: false,
            caigouBoHuiLoading: false,
            caigouBoHuiCon: null,
            caigouBoHuiMainId: null,

            brandInquiryRejectId: null,
            brandInquiryRejectVisible: false,
            brandInquiryRejectLoading: false,
            rejectReason: null,
            paymentInfoPictureList: [],
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',

            fenpeiYunYingTitle: "",
            fenpeiYunYingVisible: false,
            fenpeiYunYingLoading: false,
            fenpeiYunYingList: [],
            fenpeiYunYingSelId: null,
            fenpeiYunYingSelUserId: null,
            fenpeiYunYingSelName: null,
            fenpeiYunYingGroupList: [],
            fenpeiYunYingGroupSel: null,
            fenpeiYunYingGroupSelName: null,
            fenpeiYunYingMainId: null,

            yunyingBoHuiVisible: false,
            yunyingBoHuiLoading: false,
            yunyingBoHuiCon: null,
            yunyingBoHuiMainId: null,

            yunyingBoHuiMethodLoading: false,
            yunyingBoHuiMethodList: [],
            specialGroup: false,
        };
    },
    async mounted() {
        const userInfoName = "hotsalegoods_selfuserinfo";
        // let selfInfo4Store = getStore(userInfoName);
        // if (selfInfo4Store) {
        //     this.selfInfo = selfInfo4Store;
        // } else {
        //     this.selfInfo = (await getUserInfo()).data;
        //     setStore(userInfoName, { ...this.selfInfo }, 60 * 60 * 2);
        // }

        //跨境排除掉
        this.platformlist = this.platformlist.filter(f => f.value != 3);

        this.selfInfo = (await getUserInfo()).data;
        if (this.selfInfo && this.selfInfo.fullName) {
            if (this.selfInfo.fullName.indexOf("跨境") > -1) {
                this.Filter.internationalType = 1;
            }
            else if (this.selfInfo.fullName.indexOf("运营") > -1) {
                this.Filter.internationalType = 0;
            }

            if (this.selfInfo.fullName?.includes('跨境运营部') || this.selfInfo.fullName?.includes('首力供应链项目部')) {
                this.specialGroup = true
            }else{
                this.specialGroup = false
            }
            console.log(this.specialGroup, 'this.specialGroup');
        }
        setStore(userInfoName, { ...this.selfInfo }, 60 * 60 * 2);
        this.getuserGroup();

        //拍摄任务参数
        let res_group = await getDirectorGroupList();
        this.addTaskGroupList = res_group.data?.map(item => { return { value: item.key, label: item.value }; });
        this.fenpeiYunYingGroupList = res_group.data?.map(item => { return { value: item.key, label: item.value }; });
        let res_warehouse = await getShootOperationsGroup({ type: 3 });
        this.addTaskWarehouselist = res_warehouse?.map(item => { return { value: item.id, label: item.label }; });
        let pfrule = await rulePlatform();
        this.addTaskPlatformList = pfrule.options;

        var res3 = await getDirectorList();
        this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });
        //this.getcategoryall();
        this.getCategorysCascader();
        this.getHotSaleBrandPushNewCreatedUserNameList();

        this.onSearch();

        this.$nextTick(() => {
            this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('categoryLeve1'))
            this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('categoryLeve2'))

            this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('goodsCompetePlatform'))
            this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('goodsCompeteShopName'))
            // this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('goodsCompeteGgCostPrice'))
            this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('goodsCompeteCccb'))
            this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('goodsCompetePlatformKdRate'))
            this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('goodsCompetePlatformKdAmount'))
            this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('goodsCompetePlatformFkRate'))
            this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('goodsCompetePlatformFkAmount'))
            this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('goodsCompeteFhhtkRate'))
            this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('goodsCompeteFhhtkAmount'))
            this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('goodsCompeteJhyfRate'))
            this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('goodsCompeteJhyfAmount'))
            this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('goodsCompeteJhThShRate'))
            this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('goodsCompeteJhThShAmount'))
            // this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('goodsCompeteLrWc'))
            // this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('goodsCompeteLrlWc'))
        });

    },
    methods: {
        async yunyingBoHuiMethod(value) {
            this.yunyingBoHuiMethodLoading = true;
            let res = await getYunYingAllUserList({ name: value });
            this.yunyingBoHuiMethodLoading = false;
            if (res && res.success) {
                this.yunyingBoHuiMethodList = res.data.map(item => { return { value: item.key, label: item.value }; });
            }
        },
        async codedPreservationMethod() {
            if (!this.seriesencoding.goodsCode) {
                this.$message({ type: "error", message: "请选择商品编码!" });
                return
            }
            this.savingLoading = true;
            const { success } = await addGoodsCodeToBrandPushNew(this.seriesencoding);
            this.savingLoading = false;
            if (!success) return
            this.addedCodingPopup = false;
            this.onListClickEvent(this.tabularrows, this.tabularcolumns);
            this.$message({ type: "success", message: "操作成功!" });
        },
        async onSeriesCoding() {
            this.seriesencoding.styleCode = this.tabularrows.styleCode;
            const { data, success } = await getDelGoodsCode({ styleCode: this.tabularrows.styleCode });
            if (!success) return
            this.commodityData = data;
            this.seriesencoding.goodsCode = null;
            this.addedCodingPopup = true
        },
        //表格操作
        async ondeleteOperation(row) {
            this.$confirm(`确定要${row.isDelete ? '恢复' : '删除'}吗?`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(async () => {
                    let params = { styleCode: row.styleCode, isDelete: !row.isDelete, goodsCode: row.goodsCode };
                    this.tabularloading = true;
                    let res = await setHotSaleBrandPushNewGoodsDel(params);
                    if (res?.success == true) {
                        this.onListClickEvent(this.tabularrows, this.tabularcolumns);
                        this.onSearch();
                        this.$message({
                            type: "success",
                            message: "操作成功!"
                        });
                    }
                    this.tabularloading = false;
                });
        },
        async getCategorysCascader() {
            this.goodsCategorysOptions = [];
            const res = await getAllBrandProductCategorysCascader();
            if (res?.success == true) {
                this.goodsCategorysOptions = res.data;
            }
        },
        async getHotSaleBrandPushNewCreatedUserNameList() {
            this.hotSaleBrandPushNewCreatedUserNameList = [];
            const res = await getHotSaleBrandPushNewCreatedUserNameList();
            if (res?.success == true) {
                this.hotSaleBrandPushNewCreatedUserNameList = res.data;
            }

            const res2 = await getBrandAllUserList()
            if (res2?.success == true) {
                this.caiGouAllUserList = res2.data;
            }
        },

        //每页数量改变
        tabularSizechange(val) {
            this.commodityInformation.currentPage = 1;
            this.commodityInformation.pageSize = val;
            this.onListClickEvent()
        },
        //当前页改变
        tabularPagechange(val) {
            this.commodityInformation.currentPage = val;
            this.onListClickEvent()
        },
        tabularsortchange({ order, prop }) {
            if (prop) {
                this.commodityInformation.orderBy = prop
                this.commodityInformation.isAsc = order.indexOf("descending") == -1 ? true : false
                this.onListClickEvent()
            }
        },
        //查询
        async commodityInformationMethod(row, type) {
            if (type == 'search') {
                this.commodityInformation.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            const params = { styleCode: row.styleCode, ...this.commodityInformation }
            this.tabularloading = true;
            const { data, success } = await getHotSaleBrandPushNewGoodsPage(params);
            if (!success) return
            this.tabulartableData = data.list;
            this.tabulartotal = data.total;
            this.listLoading = false;
            this.tabularloading = false;
            this.listTrendChart = true;
        },
        //列表点击事件
        onListClickEvent(row, column) {
            console.log(row, column, 'row,column')
            if (row && column) {
                this.listLoading = true;
                this.tabularrows = row
                this.tabularcolumns = column
            }
            if (this.tabularName == 'first') {
                this.commodityInformationMethod(this.tabularrows, 'search')
            } else if (this.tabularName == 'second') {
                console.log(this.tabularrows, this.tabularcolumns, 'this.tabularrows,this.tabularcolumns')
                this.showchart(this.tabularrows, this.tabularcolumns)
            }
        },
        async showchart(row, column) {
            this.trendChartLoading = true;
            const { data, success } = await getAnalysisResponse({ styleCode: row.styleCode, selectColumn: column });
            if (!success) {
                return;
            }
            data.series.map((item) => {
                item.itemStyle = {
                    "normal": {
                        "label": {
                            "show": true,
                            "position": "top",
                            "textStyle": {
                                "fontSize": 14
                            }
                        }
                    }
                }

                item.emphasis = {
                    "focus": "series"
                }
                item.smooth = false;
            })
            this.quantityprocessed.visible = true;
            this.quantityprocessed.data = data
            this.quantityprocessed.title = data.legend[0]
            this.$nextTick(() => {
                this.$refs.buschar.initcharts();
            });
            this.listLoading = false;
            this.trendChartLoading = false;
            this.listTrendChart = true;
        },
        async changeTime(e) {
            this.Filter.startDate = e ? e[0] : null
            this.Filter.endDate = e ? e[1] : null
        },
        async changeTime2(e) {
            this.Filter.appearStartDate = e ? e[0] : null
            this.Filter.appearEndDate = e ? e[1] : null
        },
        async changeTime3(e) {
            this.Filter.fpStartDate = e ? e[0] : null
            this.Filter.fpEndDate = e ? e[1] : null
        },
        async changeTime4(e) {
            this.Filter.yybhStartDate = e ? e[0] : null
            this.Filter.yybhEndDate = e ? e[1] : null
        },
        delrow(id) {
            this.$confirm("确定要删除吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(async () => {
                    let ids = [id];
                    let res = await delSaleBrandPushNewById(ids);
                    if (res?.success == true) {
                        this.onSearch();
                        this.$message({
                            type: "success",
                            message: "删除成功!"
                        });
                    }
                });
        },
        async onExport(opt) {
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.Filter, ...opt };
            var res = await exportHotSaleBrandPushNew(params);
            if (!res?.data) {
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '采购推新-新-' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        async setExportCols() {
            await this.$refs.table.setExportCols();
        },
        bzhuinput(e) {
            this.addChooseDialogInfo.addChooseFormData.remark = e;
            this.changeup();
        },
        async getuserGroup() {
            let rqs = await getCurUserDepartmentName()
            if (rqs && rqs.success) {
                let deptName = rqs.data;
                if (deptName != null && deptName != "") {
                    if (deptName.includes("淘系")) {
                        this.addChooseDialogInfo.addChooseFormData.newPlatform = 9;
                    } else if (deptName.includes("拼多多")) {
                        this.addChooseDialogInfo.addChooseFormData.newPlatform = 2;
                    } else if (deptName.includes("跨境")) {
                        this.addChooseDialogInfo.addChooseFormData.newPlatform = 3;
                    } else if (deptName.includes("天猫")) {
                        this.addChooseDialogInfo.addChooseFormData.newPlatform = 1;
                    } else if (deptName.includes("京东")) {
                        this.addChooseDialogInfo.addChooseFormData.newPlatform = 7;
                    } else if (deptName.includes("抖音")) {
                        this.addChooseDialogInfo.addChooseFormData.newPlatform = 6;
                    }
                }
            }
        },
        changeup() {
            this.$forceUpdate();
        },
        async setSelectCategorys(data) {
            if (data == null || data == "") {
                this.Filter.goodsCategory = "";
                this.Filter.categoryLeve1 = "";
                this.Filter.categoryLeve2 = "";
            } else {
                let obj = {}
                obj = this.categoryall.categoryone.find(function (item) {
                    return (item.mainCategory + '-' + item.categoryLevel1 + '-' + item.categoryLevel2) === data;
                })
                this.Filter.goodsCategory = obj.mainCategory;
                this.Filter.categoryLeve1 = obj.categoryLevel1;
                this.Filter.categoryLeve2 = obj.categoryLevel2;
            }
        },
        async getcategoryall() {
            let res = await getAllBrandProductCategorys();
            if (res?.success) {
                this.categoryall.categorytwo = [];
                this.categoryall.categorythr = [];
                this.Filter.categoryLeve1 = "";
                this.Filter.categoryLeve2 = "";
                this.categoryall.categoryone = res.data;
            }
            this.$forceUpdate();
        },
        async onAddChooseSave() {
            console.log("打印数据", this.addChooseDialogInfo)
            this.$confirm('确认要执行选品操作吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                this.addChooseDialogInfo.vLoading = true;
                this.addChooseDialogInfo.addChooseFormData.chooseType = 4;
                let res = await addChooseByHotSaleBrandPushNew(this.addChooseDialogInfo.addChooseFormData);
                this.addChooseDialogInfo.vLoading = false;
                if (res?.success) {
                    this.$message({ message: '添加选品成功', type: "success" });
                    this.addChooseDialogInfo.visible = false;
                    this.onSearch();
                }
            }).catch(() => { });
        },
        async chooseTypeChange(value) {
            if (this.addChooseDialogInfo.addChooseFormData.chooseType == 3) {
                this.addChooseDialogInfo.platformdisabled = false;
            }
            else {
                this.addChooseDialogInfo.addChooseFormData.platform = 0;
                this.addChooseDialogInfo.platformdisabled = true;
            }
            // if (!this.addChooseDialogInfo.addChooseFormData.goodsCompeteId) {
            //     this.addChooseDialogInfo.addChooseFormData.goodsCompeteId = "CGTX_" + (new Date().valueOf()).toString();
            // }
        },
        async onUpdateImg() {
            this.updateImgInfo.visible = !this.updateImgInfo.visible;
            let self = this;
            let img = this.addChooseDialogInfo.changeImgs;
            _.delay(function () {
                if (!self.updateImgInfo.visible)
                    self.updateImgInfo.goodsImgUrl = "";
                else
                    self.updateImgInfo.goodsImgUrl = img;
            }, 200);
            console.log(this.updateImgInfo, "updateImgInfo");
        },
        async onupdateImgInfo() {

            this.addChooseDialogInfo.addChooseFormData.goodsCompeteImgUrl = this.updateImgInfo.goodsImgUrlList[this.updateImgInfo.selindex].url;
        },
        async onUpdateImg1() {
            if (this.updateImgInfo.goodsImgUrlList && this.updateImgInfo.goodsImgUrlList.length > 0) {
                this.updateImgInfo.selindex = this.updateImgInfo.selindex - 1;
                if (this.updateImgInfo.selindex < 0)
                    this.updateImgInfo.selindex = 0;
                this.addChooseDialogInfo.addChooseFormData.goodsCompeteImgUrl = this.updateImgInfo.goodsImgUrlList[this.updateImgInfo.selindex].url;
            }
        },
        async onUpdateImg2() {
            if (this.updateImgInfo.goodsImgUrlList && this.updateImgInfo.goodsImgUrlList.length > 0) {
                this.updateImgInfo.selindex = this.updateImgInfo.selindex + 1;
                if (this.updateImgInfo.selindex > (this.updateImgInfo.goodsImgUrlList.length - 1))
                    this.updateImgInfo.selindex = (this.updateImgInfo.goodsImgUrlList.length - 1);
                this.addChooseDialogInfo.addChooseFormData.goodsCompeteImgUrl = this.updateImgInfo.goodsImgUrlList[this.updateImgInfo.selindex].url;
            }
        },

        formatInfoQueryState: formatInfoQueryState,
        //拍摄任务
        async onOpenAddTask(row, mode) {

            let bfData = await GetDataBeforeBuildGoodsMediaTask({ "chooseId": row.id });
            if (bfData && bfData.errMsg) {
                this.$alert(bfData.errMsg);
                return;
            }

            this.addTaskLoading = true;
            this.addTaskOpentime = this.addTaskOpentime + 1;
            this.addTask = true;
            this.addTaskPageTitle = "创建任务";
            this.addTaskIslook = false;
            this.addTaskLoading = false;
            this.$nextTick(() => {
                this.$refs.shootingvideotaskeditfrom.initaddform({
                    productShortName: row.goodsCompeteShortName,
                    platform: row.platform,
                    operationGroup: (this.selfInfo && this.selfInfo.groupId ? this.selfInfo.groupId.toString() : null),
                    dockingPeople: (this.selfInfo && this.selfInfo.nickName ? this.selfInfo.nickName : null),
                    extBzTypeArgs: {
                        extBzType: '建编码',
                        extBzIdOne: row.id,
                        extBzInWarehouse: bfData.warehouseCount > 0
                    }

                });
            });
        },
        //保存拍摄任务
        async addTaskOnSubmit() {
            this.addTaskLoading = true;
            await this.$nextTick(function () {
                this.$refs.shootingvideotaskeditfrom.onSubmit();
            });
            this.addTaskLoading = false;

        },
        onBeforeApplyBuildDoc(row) {
            let that = this;
            //建编码前，要确认选品。
            this.$confirm('是否确认选品？确定后将发起确认流程，确认流程审批通过后（拼多多无审核流程直接通过），就可以进行建编码操作了。', '', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                DingDingApproval_BuildGoodsDocBeforConfirm({ hotSaleGoodsChooseId: row.id }).then((reqRlt) => {
                    if (reqRlt.success) {
                        that.$message({ message: '确认选品流程已提交，请关注钉钉审批流（拼多多无审核流程直接通过）', type: "success" });
                        that.onRefresh();
                    }
                });
            }).catch(() => {
            });
        },
        //归档
        onSealClose(row) {
            let self = this;

            if (!(
                (row.groupId == self.selfInfo.groupId && self.selfInfo.id == self.selfInfo.groupLeaderId)
                ||
                (row.userId == self.selfInfo.id)
            )) {
                self.$alert('只有选品本人或对应组的组长才能进行手动归档！');

                return;
            }

            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsale/HostChooseSealCloseForm.vue`,
                title: '已选品归档',
                args: { ...row },
                height: 300,
                width: '600px',
                callOk: self.onRefresh
            })
        },
        async decideState(row) {
            //决定状态，1做，0不做，null未选择
            this.$alert("开发中：" + row.decideState);
        },
        showShop(row) {
            if (row.shopUrl) {
                var urlParams = getUrlParam(row.shopUrl);
                var realUrl = urlParams["goto"];
                if (realUrl)
                    window.open(realUrl);
                else
                    window.open(row.shopUrl);
            } else {
                this.$alert("当前店铺未导入链接！");
            }
        },
        showGoods(row) {
            if (row.goodsUrl) {
                var urlParams = getUrlParam(row.goodsUrl);
                var realUrl = urlParams["goto"];
                if (realUrl)
                    window.open(realUrl);
                else
                    window.open(row.goodsUrl);
            } else {
                this.$alert("当前商品未导入链接！");
            }
        },
        //申请数据
        async reqCmRefDateInfo(row, isReQuery) {
            let that = this;
            if (isReQuery) {
                //重查要确认
                this.$confirm('已存在参谋数据，重新查询将覆盖当前数据，是否继续?', '', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    reqGoodsCmRefInfoQueryAsync({ id: row.id }).then((reqRlt) => {
                        if (reqRlt.success) {
                            that.$message({ message: '已提交申请', type: "success" });
                            that.onRefresh();
                        }
                    });
                }).catch(() => {
                });

            } else {
                var reqRlt = await reqGoodsCmRefInfoQueryAsync({ id: row.id });
                if (reqRlt.success) {
                    this.$message({ message: '已提交申请', type: "success" });
                    this.onRefresh();
                }
            }

        },
        //查看参谋数据
        async showCmRefDateInfo(row) {
            if (row.platform == 1 || row.platform == 9) {
                //天猫
                var reqRlt = await getHotGoodsCmRefInfoAsync({ platform: row.platform, goodsCompeteId: row.goodsCompeteId, goodsId: row.goodsId });
                if (!reqRlt.success || reqRlt.data == null) {
                    //this.$message({ message: '未查询到有效参谋数据！', type: "error" });
                    return;
                }

                this.dialogTmCmRefInfoVisible = true;

                this.$nextTick(() => {
                    this.$refs.cmRefTmPage.setInfoData(reqRlt.data);
                });
            } else if (row.platform == 2) {
                //拼多多
                this.dialogPddCmRefInfoVisible = true;
                this.$nextTick(() => {
                    this.$refs.cmRefPddPage.pddsalescharLoadData(row.platform, row.goodsCompeteId);
                });

            } else {
                this.$alert("当前平台暂不支持！");
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                var orderField = column.prop;
                var bFields = [];// ['skuDataState', 'skuDataTime', 'cmRefInfoState', 'cmRefInfoLastOkTime'];
                if (bFields.indexOf(orderField) > -1) {
                    orderField = "b." + orderField;
                }

                this.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            }
            this.onSearch();
        },
        onImport() {
            this.dialogVisibleSyj = true
        },
        async uploadSuccess2(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
        },
        async onSubmitupload2() {
            this.$refs.upload2.submit()
        },
        onRefresh() {
            this.onSearch()
        },
        clearQueryParam() {
            this.timeRanges = [];
            this.timeRanges2 = [];
            this.timeRanges3 = [];
            this.timeRanges4 = [];
            this.Filter = { chooseType: 0, internationalType: -1, isClose: 0 };
        },
        onSearch() {
            this.caigouBoHuiCon = null;
            this.yunyingBoHuiCon = null;

            this.$refs.pager.setPage(1);
            this.gettbdatalist1();
        },
        async gettbdatalist1() {
            // if (this.Filter.gDate) {
            //     this.Filter.startGDate = this.Filter.gDate[0];
            //     this.Filter.endGDate = this.Filter.gDate[1];
            // }
            // else {
            //     this.Filter.startGDate = null;
            //     this.Filter.endGDate = null;

            // }
            const para = { ...this.Filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            this.listLoading = true;
            console.log(params, "paramsparamsparams");
            // const res = await pageHotSaleGoodsChooseAsync(params);
            const res = await pageHotSaleBrandPushNew(params);
            this.listLoading = false;
            res.data.list.forEach(item => {
                //推荐人是当前用户
                if (item.createdUserId == this.selfInfo.id) {
                    item.exclude = true
                } else {
                    item.exclude = false
                }
            })
            this.total = res.data.total
            this.tbdatalist = res.data.list;
            this.selids = [];
        },

        selectchange: function (rows) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        openChooseGoodsForm(row) {
            if (row.id) {
                console.log("编辑")
                let _this = this;
                _this.addChooseFormNewshow = true;
                setTimeout(() => {
                    _this.$refs.addChooseFormNewref.getmsg(row.id);
                }, 600)

                return;
            }
            let self = this;
            self.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsale/AddChooseFormNew.vue`,
                title: '新增选品',
                args: { oid: '', mode: 0 },
                height: 700,
                callOk: self.onSearch
            });
        },
        //上架
        openSetOnSellForm: function (row) {
            let self = this;
            self.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsale/SetOnSellForm.vue`,
                title: '上架',
                args: {
                    chooseId: row.id,
                    goodsCompeteName: row.goodsCompeteName,
                    goodsCompeteId: row.goodsCompeteId,
                    goodsCompeteShortName: row.goodsCompeteShortName
                },
                height: 700,
                callOk: self.onRefresh
            });
        },
        async categoryChanged(val, cIdx) {
            var prixParentname = "";
            for (var i = 1; i <= cIdx; i++) {
                if (prixParentname == "") {
                    prixParentname = this.Filter["categoryName" + i];
                } else {
                    prixParentname += "-" + this.Filter["categoryName" + i];
                }
            }
            for (var i = cIdx + 1; i <= 4; i++) {
                this["categoryNameList" + i] = [];
                this.Filter["categoryName" + i] = "";
                if (i == cIdx + 1) {

                    var reqRlt = await getAllLinkCategoryNamesAsyncByParent({ parentName: prixParentname });
                    this["categoryNameList" + i] = reqRlt.data;
                }
            }
        },
        categoryChanged1: function (val) {
            this.categoryChanged(val, 1);
        },
        categoryChanged2: function (val) {
            this.categoryChanged(val, 2);
        },
        categoryChanged3: function (val) {
            this.categoryChanged(val, 3);
        },

        //申请竞品SKU
        async onApplyCompeteGoodSKU(row, isFirst) {
            if (isFirst) {
                var reqRlt = await applyCompeteGoodSKU({ hotSaleGoodsChooseId: row.id, reReq: false });
                if (reqRlt?.success && reqRlt?.data) {
                    this.$message({ message: '已提交申请', type: "success" });
                    this.onRefresh();
                }
                else {
                    if (reqRlt?.data == false)
                        this.$message({ message: '申请失败，请联系管理员', type: "error" });
                }
            } else {
                this.$confirm('重新申请SKU成功后，已计算的利润需要重新计算，是否确认继续?', '', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    var reqRlt = await applyCompeteGoodSKU({ hotSaleGoodsChooseId: row.id, reReq: true });
                    if (reqRlt?.success && reqRlt?.data) {
                        this.$message({ message: '已提交申请', type: "success" });
                        this.onRefresh();
                    }
                    else {
                        if (reqRlt?.data == false)
                            this.$message({ message: '申请失败，请联系管理员', type: "error" });
                    }
                }).catch(() => {
                    //this.$message({ type: 'info', message: '已取消' });
                });
            }

        },
        //编辑竞品SKU
        onEditCompeteGoodSKU(row, disable) {
            let self = this;
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsale/hotsalegoodschooselistskunew.vue`,
                args: { oid: row.id, mode: 2, disable: disable },
                title: "竞品利润",
                top: '50px',
                autoTitle: false,
                width: '95%',
                height: '95%',
                callOk: () => {
                    self.onRefresh();
                }
            });

            // this.skuSaveHiddle = true;
            // this.dialogSkuVisible = true;
            // this.$nextTick(() => {
            //     this.$refs.skuPage.getSkuTableData(row.id, true);
            // });
        },
        //查看竞品SKU
        async onSeeCompeteGoodSKU(row) {

            let self = this;
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsale/hotsalegoodschooselistsku.vue`,
                args: { oid: row.id, mode: 3 },
                title: "查看竞品利润",
                autoTitle: false,
                width: '95%',
                height: '95%',
                callOk: () => {
                    self.onRefresh();
                }
            });

            // this.skuSaveHiddle = false;
            // this.dialogSkuVisible = true;
            // this.$nextTick(() => {
            //     this.$refs.skuPage.getSkuTableData(row.id, false);
            // });
        },
        //保存SKU
        async onSkuSave(isEnd) {
            this.skuSaveLoading = true;

            let save = await this.$refs.skuPage.saveSkuTableData(isEnd);
            this.skuSaveLoading = false;
            if (save) {
                if (isEnd) {
                    this.dialogSkuVisible = false;
                    this.onRefresh();
                }
            }
        },
        //登记采样
        async onRegisterSkuOrder(row) {
            const rlt = await registerSkuOrderAsync({ chooseId: row.id });

            if (rlt.success) {
                this.$message({ message: '已登记采样', type: "success" });
                this.onRefresh();
            }
        },
        //建编码
        async onBuildGoodsDoc(row) {
            const params = {
                currentPage: 1,
                pageSize: 100,
                orderBy: "CreatedTime",
                isAsc: false,
                hotSaleGoodsChooseId: (row.id ?? 0),
            };
            this.dialogBuildGoodsDocListVisible = true;
            this.jdmainLoading = true;
            const res = await pageHotSaleGoodsBuildGoodsAsync(params);
            this.jdmainLoading = false;
            this.jdmainlist = res?.data.list;
            if (this.jdmainlist.length > 0) {
                await this.onBuildGoodsDocDtl(this.jdmainlist[0].id);
            }
            else {
                this.jddtllist = [];
            }
            this.selHotSaleGoodsChooseId = (row.id ?? 0);
        },
        async onBuildGoodsDocDtl(parentId) {
            const params = {
                currentPage: 1,
                pageSize: 100,
                orderBy: "CreatedTime",
                isAsc: false,
                parentId: (parentId ?? 0),
            };
            this.jddtlLoading = false;
            const res = await pageHotSaleGoodsBuildGoodsCodeAsync(params);
            this.jddtlLoading = false;
            this.jddtllist = res.data.list;
        },
        async onjdmainCellClick(row, column, cell) {
            var isLoad = true;
            if (this.jddtllist.length > 0) {
                var parentId = this.jddtllist[0].parentId;
                if (row.id == parentId)
                    isLoad = false;
            }
            if (isLoad == true) {
                await this.onBuildGoodsDocDtl(row.id);
            }
        },
        //新建编码功能
        async onBuildGoodsDocNew(row, mode) {
            this.buildGoodsDocHiddle = true;
            this.dialogBuildGoodsDocVisible = true;
            this.$nextTick(() => {
                this.$refs.buildGoodsDocPage.getSkuTableData(row.id, mode != 3);
            });
        },
        async onBuildGoodsDocAdd() {
            this.buildGoodsDocHiddle = true;
            this.dialogBuildGoodsDocVisible = true;
            this.$nextTick(() => {
                this.$refs.buildGoodsDocPage.getSkuTableData(this.selHotSaleGoodsChooseId, true);
            });
        },
        async onEditBuildGoodsDoc(row, edit) {
            this.buildGoodsDocHiddle = true;
            this.dialogBuildGoodsDocVisible = true;
            this.seldocId = row.id;
            if (!edit) {
                edit = false;
                this.buildGoodsDocHiddle = false;
            }
            this.$nextTick(() => {
                this.$refs.buildGoodsDocPage.getSkuTableData(row.id, edit);
            });
        },
        //保存竞品编码建档
        async onBuildGoodsDocSave(isApply) {
            this.buildGoodsDocLoading = true;
            let rlt = await this.$refs.buildGoodsDocPage.saveSkuTableData(isApply);
            this.buildGoodsDocLoading = false;
            if (rlt && rlt.success) {
                this.$message({ message: '保存成功', type: "success" });
                this.dialogBuildGoodsDocVisible = false;

                // //刷新主明细
                // await this.onBuildGoodsDoc({ id: this.selHotSaleGoodsChooseId });
                // if (this.jddtllist.length > 0 && this.jddtllist[0].parentId == this.seldocId) {
                //     await this.onBuildGoodsDocDtl(this.seldocId);
                // }
                this.onRefresh();
            }
        },
        //申请编码-走钉钉审批
        async onApprovalGoodsDoc(row) {
            this.$confirm('申请编码将推送到钉钉审批，是否继续?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await dingDingApproval_BuildGoodsDoc({ docId: row.id });
                if (!res?.success) {
                    //this.$message({ type: 'success', message: res?.msg });
                } else {
                    this.$message({ type: 'success', message: '已发起申请，请关注钉钉审批流程!' });
                }
                //刷新主明细
                await this.onBuildGoodsDoc({ id: this.selHotSaleGoodsChooseId });
                if (this.jddtllist.length > 0 && this.jddtllist[0].parentId == this.seldocId) {
                    await this.onBuildGoodsDocDtl(this.seldocId);
                }
                this.onRefresh();
            }).catch(() => {
                this.$message({ type: 'info', message: '已取消' });
            });
        },
        async showLog(row) {
            this.showLogData.goodsCompeteId = row.goodsCompeteId;
            this.showLogData.goodsCompeteName = row.goodsCompeteName;
            this.showLogData.groupName = row.groupName;
            this.showLogData.logList = [];
            this.dialogShowLog = true;
            let params = {
                ...this.logfliter,
                "hotSaleBrandPushNewId": row.id
            };
            this.hotSaleBrandPushNewId = row.id;
            // let params = { ...this.Filter };

            this.logTableLoding = true;
            let res = await pageHotSaleBrandPushNewLog(params);
            this.logTableLoding = false;
            this.showLogData.logList = res?.data?.list;
            this.logtotal = res?.data?.total;
        },
        async changepage() {
            this.dialogShowLog = true;
            var pager = this.$refs.logpager.getPager();
            let params = {
                ...this.logfliter,
                ...this.pager,
                "hotSaleBrandPushNewId": this.hotSaleBrandPushNewId
            };
            // let params = { ...this.Filter };

            this.logTableLoding = true;
            let res = await pageHotSaleBrandPushNewLog(params);
            this.logTableLoding = false;
            this.showLogData.logList = res?.data?.list;
            this.logtotal = res?.data?.total;
        },
        async trunToOther(row) {
            let self = this;
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsale/hotsalegoodschoosetranofform.vue`,
                args: { ...row, oid: row.id },
                title: "转派运营",
                width: '400px',
                height: 400,
                callOk: () => {
                    self.onRefresh();
                }
            });
        },
        async Purchase(row) {
            console.log(row)
            // this.hotstockinapplylistFilter.chooseId = row.id;
            // this.addChooseDialogInfo.addChooseFormData = row.id;
            let res = await getHotSaleBrandPushNewById({
                id: row.id
            });
            if (res?.success) {
                // this.addChooseDialogInfo.addChooseFormData = res.data;

                this.addChooseDialogInfo.addChooseFormData.goodsCompeteId = res.data.goodsCompeteId;
                this.addChooseDialogInfo.addChooseFormData.goodsCompeteName = res.data.goodsCompeteName;
                this.addChooseDialogInfo.addChooseFormData.goodsCompeteImgUrl = res.data.goodsCompeteImgUrl;
                this.addChooseDialogInfo.addChooseFormData.remark = res.data.remark;

                this.addChooseDialogInfo.addChooseFormData.hotSaleBrandPushNewId = row.id;
                this.addChooseDialogInfo.visible = true;
            }


            // this.addChooseDialogInfo.addChooseFormData = row;





            // this.$nextTick(() => {
            //     this.$refs.hotstockinapplylistPage.onSearch();
            // });
        },
        async CopyForKuaJing(oldid) {
            this.$confirm("确定要执行复制吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(async () => {
                    let ids = [oldid];
                    this.pageLoading = true;
                    let res = await hotSaleBrandPushNewCopyForKuaJing(ids);
                    this.pageLoading = false;
                    if (res?.success == true) {
                        this.onSearch();
                        this.$message({
                            type: "success",
                            message: "复制成功!"
                        });
                    }
                });
        },
        async CopyForGuoNei(oldid) {
            this.$confirm("确定要执行复制吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(async () => {
                    let ids = [oldid];
                    this.pageLoading = true;
                    let res = await hotSaleBrandPushNewCopyForGuoNei(ids);
                    this.pageLoading = false;
                    if (res?.success == true) {
                        this.onSearch();
                        this.$message({
                            type: "success",
                            message: "复制成功!"
                        });
                    }
                });
        },
        async onBatchCopyForKuaJing() {
            if (!this.selids || this.selids.length <= 0) {
                this.$message({ type: "warning", message: "请至少勾选一行数据!" });
                return;
            }
            this.$confirm("确定要将勾选的数据复制为跨境吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(async () => {
                    let ids = this.selids;
                    this.pageLoading = true;
                    let res = await hotSaleBrandPushNewCopyForKuaJing(ids);
                    this.pageLoading = false;
                    if (res?.success == true) {
                        this.onSearch();
                        this.$message({
                            type: "success",
                            message: "复制成功!"
                        });
                    }
                });
        },
        async onBatchCopyForGuoNei() {
            if (!this.selids || this.selids.length <= 0) {
                this.$message({ type: "warning", message: "请至少勾选一行数据!" });
                return;
            }
            this.$confirm("确定要将勾选的数据复制为国内吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(async () => {
                    let ids = this.selids;
                    this.pageLoading = true;
                    let res = await hotSaleBrandPushNewCopyForGuoNei(ids);
                    this.pageLoading = false;
                    if (res?.success == true) {
                        this.onSearch();
                        this.$message({
                            type: "success",
                            message: "复制成功!"
                        });
                    }
                });
        },
        async onBatchDelete() {
            if (!this.selids || this.selids.length <= 0) {
                this.$message({ type: "warning", message: "请至少勾选一行数据!" });
                return;
            }
            this.$confirm("确定要删除勾选的数据吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(async () => {
                    let ids = this.selids;
                    this.listLoading = true;
                    let res = await delSaleBrandPushNewById(ids);
                    this.listLoading = false;
                    if (res?.success == true) {
                        this.onSearch();
                        this.$message({
                            type: "success",
                            message: "批量删除成功!"
                        });
                    }
                });
        },
        async handleCommand(command) {
            if (!command) {
                this.$message({ type: 'warning', message: "请选择" });
                return;
            }
            switch (command) {
                //批量删除
                case 'a':
                    await this.onBatchDelete()
                    break;

                //批量复制为跨境
                case 'b':
                    await this.onBatchCopyForKuaJing();
                    break;

                //批量复制为国内
                case 'c':
                    await this.onBatchCopyForGuoNei();
                    break;

                //批量指派
                case 'd':
                    await this.zhipairowXj_batch();
                    break;

                //批量归档
                case 'e':
                    await this.caigouGuiDangrowXj(null);
                    break;

                //批量归档恢复
                case 'f':
                    await this.caigouGuiDangHfrowXj(null);
                    break;

                //批量分配
                case 'g':
                    await this.fenpeirowXj(null);
                    break;


            }
        },

        openChooseGoodsFormXj(row) {
            let self = this;
            self.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsale/AddChooseFormNewXj.vue`,
                title: '运营询价',
                args: { oid: row.id, mode: 0 },
                height: 700,
                callOk: self.onSearch
            });
        },
        delrowXj(id) {
            this.$confirm("确定要删除询价/1688选品吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(async () => {
                let ids = [id];
                let res = await delSaleBrandPushNewXjById(ids);
                if (res?.success == true) {
                    this.onSearch();
                    this.$message({ type: "success", message: "删除成功!" });
                }
            });
        },
        renlingrowXj(id) {
            this.$confirm("确定要认领询价/1688选品吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(async () => {
                let ids = [id];
                let res = await renLingSaleBrandPushNewXjById(ids);
                if (res?.success == true) {
                    this.onSearch();
                    this.$message({ type: "success", message: "认领成功!" });
                }
            });
        },
        zhipairowXj(id) {
            this.zhipaiCaiGouTitle = "单个指派采购";
            this.zhipaiCaiGouSelMainId = id;
            this.zhipaiCaiGouVisible = true;
        },
        zhipairowXjSave() {
            if (!this.zhipaiCaiGouSel) {
                this.$message({ type: "error", message: "请选择采购!" });
                return;
            }
            let crname = this.caiGouAllUserList.find(f => f.key == this.zhipaiCaiGouSel)?.value;
            if (!crname) {
                this.$message({ type: "error", message: "请选择采购!" });
                return;
            }
            if (this.zhipaiCaiGouSelMainId) {

                this.$confirm("确定要指派吗?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(async () => {
                    let ids = [{ id: this.zhipaiCaiGouSelMainId, createdUserId: this.zhipaiCaiGouSel, createdUserName: crname }];
                    this.zhipaiCaiGouLoading = true;
                    let res = await zhiPaiSaleBrandPushNewXjById(ids);
                    this.zhipaiCaiGouLoading = false;
                    if (res?.success == true) {
                        this.onSearch();
                        this.$message({ type: "success", message: "指派成功!" });
                        this.zhipaiCaiGouVisible = false;
                    }
                });
            }
            else {
                if (!this.selids || this.selids.length <= 0) {
                    this.$message({ type: "warning", message: "请至少勾选一行数据!" });
                    return;
                }
                let ids = [];
                this.selids.forEach(f => {
                    ids.push({ id: f, createdUserId: this.zhipaiCaiGouSel, createdUserName: crname });
                });
                this.$confirm("确定要批量指派吗?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(async () => {
                    this.zhipaiCaiGouLoading = true;
                    let res = await zhiPaiSaleBrandPushNewXjById(ids);
                    this.zhipaiCaiGouLoading = false;
                    if (res?.success == true) {
                        this.onSearch();
                        this.$message({ type: "success", message: "批量指派成功!" });
                        this.zhipaiCaiGouVisible = false;
                    }
                });

            }
        },
        zhipairowXj_batch() {
            if (!this.selids || this.selids.length <= 0) {
                this.$message({ type: "warning", message: "请至少勾选一行数据!" });
                return;
            }
            this.zhipaiCaiGouTitle = "批量指派采购"
            this.zhipaiCaiGouSelMainId = null;
            this.zhipaiCaiGouVisible = true;
        },

        bohuirowXj(id) {
            this.caigouBoHuiMainId = id;
            this.caigouBoHuiVisible = true;
        },
        bohuirowXjSave() {
            if (!this.caigouBoHuiCon) {
                this.$message({ type: "error", message: "请填写驳回原因!" });
                return;
            }
            this.$confirm("确定要驳回询价吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(async () => {
                let ids = [{ id: this.caigouBoHuiMainId, xjCaiGouBoHui: this.caigouBoHuiCon }];
                this.caigouBoHuiLoading = true;
                let res = await boHuiSaleBrandPushNewXjById(ids);
                this.caigouBoHuiLoading = false;
                if (res?.success == true) {
                    this.onSearch();
                    this.$message({ type: "success", message: "驳回询价成功!" });
                    this.caigouBoHuiVisible = false;
                }
            });
        },
        brandInquiryReject(id) {
            this.brandInquiryRejectId = id;
            this.brandInquiryRejectVisible = true;
        },
        btnBrandInquiryReject() {
            if (!this.rejectReason) {
                this.$message({ type: "error", message: "请填写驳回原因!" });
                return;
            }
            if (this.paymentInfoPictureList.length == 0){
                this.$message({ type: "error", message: "请上传图片!" });
                return;
            }
            this.$confirm("确定要驳回吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(async () => {
                let imgJson = JSON.stringify(this.paymentInfoPictureList);
                let param = [{ id: this.brandInquiryRejectId, xjCaiGouBoHui: this.rejectReason, xjRejectImgUrl: imgJson }];
                this.brandInquiryRejectLoading = true;
                let res = await boHuiSaleBrandPushNewXjById(param);
                this.brandInquiryRejectLoading = false;
                if (res?.success == true) {
                    this.onSearch();
                    this.$message({ type: "success", message: "驳回询价成功!" });
                    this.brandInquiryRejectVisible = false;
                }
            });
        },
        inquiryClaim(id) {
            this.$confirm("确定要认领吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(async () => {
                var param = { id: id };
                let res = await directorClaimHotSaleBrandPushNew(param);
                if (res?.success == true) {
                    this.onSearch();
                    this.$message({ type: "success", message: "认领成功!" });
                }
            });
        },
        callBackPaymentPicture(data) {
            let _that = this;
            if(data){
                _that.paymentInfoPictureList = _that.paymentInfoPictureList ? _that.paymentInfoPictureList : [];
                let newarr = [];
                data.map((item) => {
                    newarr.push({ url: item.url, name: item.fileName })
                });
                _that.paymentInfoPictureList = newarr;
            }
        },
        fenpeirowXj(id) {
            if (id) {
                this.fenpeiYunYingTitle = "单个分配运营或运营组";
            }
            else {
                if (!this.selids || this.selids.length <= 0) {
                    this.$message({ type: "warning", message: "请至少勾选一行数据!" });
                    return;
                }
                this.fenpeiYunYingTitle = "批量分配运营或运营组";
            }
            this.fenpeiYunYingSelId = null;
            this.fenpeiYunYingSelUserId = null;
            this.fenpeiYunYingSelName = null;
            this.fenpeiYunYingList = [];
            this.fenpeiYunYingGroupSel = null;
            this.fenpeiYunYingGroupSelName = null;
            this.fenpeiYunYingMainId = id;
            this.fenpeiYunYingVisible = true;
        },
        async fenpeirowGroupChange() {
            this.fenpeiYunYingSelId = null;
            this.fenpeiYunYingSelUserId = null;
            this.fenpeiYunYingSelName = null;
            this.fenpeiYunYingList = [];
            this.fenpeiYunYingGroupSelName = this.fenpeiYunYingGroupList.find(w => w.value == this.fenpeiYunYingGroupSel)?.label;
            let res = await GetDirectorAllList({ groupid: this.fenpeiYunYingGroupSel, currentPage: 1, pagesize: 1000 });
            if (res && res.success) {
                this.fenpeiYunYingList = res.data.map(item => { return { value: item.userId, label: item.value, key: item.key }; });
            }
        },
        fenpeirowYunYingChange() {
            if (this.fenpeiYunYingSelId) {
                let my = this.fenpeiYunYingList.find(w => w.key == this.fenpeiYunYingSelId);
                this.fenpeiYunYingSelUserId = my?.value;
                this.fenpeiYunYingSelName = my?.label;
            }
        },
        fenpeirowXjSave() {
            console.log(this.fenpeiYunYingGroupSel, "this.fenpeiYunYingGroupSel");
            console.log(this.fenpeiYunYingGroupSelName, "this.fenpeiYunYingGroupSelName");
            console.log(this.fenpeiYunYingSelUserId, "this.fenpeiYunYingSelUserId");
            console.log(this.fenpeiYunYingSelName, "this.fenpeiYunYingSelName");

            if (!this.fenpeiYunYingGroupSel && !this.fenpeiYunYingSelUserId) {
                this.$message({ type: "error", message: "请填写运营组或运营!" });
                return;
            }
            if (!this.fenpeiYunYingGroupSelName && !this.fenpeiYunYingSelName) {
                this.$message({ type: "error", message: "请填写运营组或运营!" });
                return;
            }

            //如果不是首力项目部或者跨境运营部就必填,否则不必填
            if (!this.specialGroup) {
                if (!this.fenpeiYunYingSelUserId && !this.fenpeiYunYingSelName) {
                    this.$message({ type: "error", message: "请填写运营!" });
                    return;
                }
            }

            if (this.fenpeiYunYingMainId) {
                this.$confirm("确定要分配吗?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(async () => {
                    let ids = [{
                        id: this.fenpeiYunYingMainId,
                        xjFpUseGroup: this.fenpeiYunYingGroupSel, xjFpUseGroupName: this.fenpeiYunYingGroupSelName,
                        xjFpUseUserId: this.fenpeiYunYingSelUserId, xjFpUseUserName: this.fenpeiYunYingSelName
                    }];
                    this.fenpeiYunYingLoading = true;
                    let res = await fenPeiSaleBrandPushNewXjById(ids);
                    this.fenpeiYunYingLoading = false;
                    if (res?.success == true) {
                        this.onSearch();
                        this.$message({ type: "success", message: "分配成功!" });
                        this.fenpeiYunYingVisible = false;
                    }
                });
            }
            else {
                if (!this.selids || this.selids.length <= 0) {
                    this.$message({ type: "warning", message: "请至少勾选一行数据!" });
                    return;
                }
                let ids = [];
                this.selids.forEach(f => {
                    ids.push({
                        id: f,
                        xjFpUseGroup: this.fenpeiYunYingGroupSel, xjFpUseGroupName: this.fenpeiYunYingGroupSelName,
                        xjFpUseUserId: this.fenpeiYunYingSelUserId, xjFpUseUserName: this.fenpeiYunYingSelName
                    });
                });
                this.$confirm("确定要批量分配吗?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(async () => {
                    this.fenpeiYunYingLoading = true;
                    let res = await fenPeiSaleBrandPushNewXjById(ids);
                    this.fenpeiYunYingLoading = false;
                    if (res?.success == true) {
                        this.onSearch();
                        this.$message({ type: "success", message: "批量分配成功!" });
                        this.fenpeiYunYingVisible = false;
                    }
                });
            }
        },
        yunyingBoHuirowXj(id) {
            this.yunyingBoHuiMainId = id;
            this.yunyingBoHuiVisible = true;
        },
        yunyingBoHuirowXjSave() {
            if (!this.yunyingBoHuiCon) {
                this.$message({ type: "error", message: "请填写驳回原因!" });
                return;
            }
            this.$confirm("确定要驳回询价吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(async () => {
                let ids = [{ id: this.yunyingBoHuiMainId, xjFpBoHuiContext: this.yunyingBoHuiCon }];
                this.yunyingBoHuiLoading = true;
                let res = await yunYingBoHuiSaleBrandPushNewXjById(ids);
                this.yunyingBoHuiLoading = false;
                if (res?.success == true) {
                    this.onSearch();
                    this.$message({ type: "success", message: "驳回分配成功!" });
                    this.yunyingBoHuiVisible = false;
                }
            });
        },
        caigouGuiDangrowXj(id) {
            if (id) {
                this.$confirm("确定要归档吗?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(async () => {
                    let ids = [id];
                    this.yunyingBoHuiLoading = true;
                    let res = await guiDangSaleBrandPushNewXjById(ids);
                    this.yunyingBoHuiLoading = false;
                    if (res?.success == true) {
                        this.onSearch();
                        this.$message({ type: "success", message: "归档成功!" });
                        this.yunyingBoHuiVisible = false;
                    }
                });
            }
            else {
                if (!this.selids || this.selids.length <= 0) {
                    this.$message({ type: "warning", message: "请至少勾选一行数据!" });
                    return;
                }
                this.$confirm("确定要批量归档吗?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(async () => {
                    let ids = this.selids;
                    this.yunyingBoHuiLoading = true;
                    let res = await guiDangSaleBrandPushNewXjById(ids);
                    this.yunyingBoHuiLoading = false;
                    if (res?.success == true) {
                        this.onSearch();
                        this.$message({ type: "success", message: "批量归档成功!" });
                        this.yunyingBoHuiVisible = false;
                    }
                });
            }
        },
        caigouGuiDangHfrowXj(id) {
            if (id) {
                this.$confirm("确定要归档恢复吗?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(async () => {
                    let ids = [id];
                    this.yunyingBoHuiLoading = true;
                    let res = await guiDangHfSaleBrandPushNewXjById(ids);
                    this.yunyingBoHuiLoading = false;
                    if (res?.success == true) {
                        this.onSearch();
                        this.$message({ type: "success", message: "归档恢复成功!" });
                        this.yunyingBoHuiVisible = false;
                    }
                });
            }
            else {
                if (!this.selids || this.selids.length <= 0) {
                    this.$message({ type: "warning", message: "请至少勾选一行数据!" });
                    return;
                }
                this.$confirm("确定要批量归档恢复吗?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(async () => {
                    let ids = this.selids;
                    this.yunyingBoHuiLoading = true;
                    let res = await guiDangHfSaleBrandPushNewXjById(ids);
                    this.yunyingBoHuiLoading = false;
                    if (res?.success == true) {
                        this.onSearch();
                        this.$message({ type: "success", message: "批量归档恢复成功!" });
                        this.yunyingBoHuiVisible = false;
                    }
                });
            }
        },
        async callback(val) {
            this.Filter.goodsCompeteId = val;
        },

    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

::v-deep .xAllDataStateCol202301111351001 div {
    text-align: center;
    padding-left: 1px !important;
    padding-right: 1px !important;
}

::v-deep .xAllDataStateCol202301111351001 p {
    padding: 0;
    text-align: center;
    margin: 1px 1px 1px 1px;
    font-size: 9pt;
    line-height: 14px;
}

::v-deep .custom-cascader .el-cascader__search-input {
    margin: 0 0 0 6px !important;
}

// ::v-deep .xAllDataStateCol202301111351001primary{
//     background-color:#409EFF;
//     color: white;
// }
// ::v-deep .xAllDataStateCol202301111351001info{
//     background-color:#909399;
//     color: white;
// }
// ::v-deep .xAllDataStateCol202301111351001success{
//     background-color: #67C23A;
//     color: white;
// }
// ::v-deep .xAllDataStateCol202301111351001warning{
//     background-color: #E6A23C;
//     color: white;
// }
// ::v-deep .xAllDataStateCol202301111351001danger{
//     background-color: #F56C6C;
//     color: white;
// }</style>
<style>
.el-image__inner {
    max-width: 50px;
    max-height: 50px;
}
</style>
