<template>
    <div>
        <div class="tjgd" v-if="!showHome">
            <div class="flexrow">
                <span>
                    <el-date-picker  style="position: relative; top: 1px; width: 400px"
                        type="daterange" align="right" unlink-panels range-separator="至" start-placeholder="开始日期"
                         format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                        end-placeholder="结束日期" v-model="filter.createdtimerange"  :picker-options="pickerOptions">
                    </el-date-picker>
                </span>
                <span>
                    <el-select v-model="filter.isTaoXi">
                        <el-option  label="淘系" value="0" ></el-option>
                        <el-option  label="非淘系" value="1"></el-option>
                    </el-select>
                </span>
                <span>
                    <el-input v-model.trim="filter.processCode" maxlength="50" clearable placeholder="编码" style="width:150px;" />
                </span>
                <span v-if="filter.isTaoXi!=0">
                    <el-select v-model="filter.isDelivery" clearable="" placeholder="状态" >
                        <el-option label="已发货" value="0"></el-option>
                        <el-option label="未发货" value="1"></el-option>
                    </el-select>
                </span>
                <span>
                    <el-button type="primary" v-throttle="2000" @click="onSearch()">查询</el-button>
                </span>
            </div>
        </div>
        <div class="sybj">
            <!--加工统计 start-->
            <div class="tjnrk10">
                <div class="tjnrnk">
                    <div class="tjbt">
                        <span>{{ showHome?'订单占比':'加工统计' }}</span>
                            <div class="flexroww" v-if="this.$route.path == '/media/index/homepage'">
                              <span>
                                  <el-date-picker  style="position: relative; top: 1px; width: 250px" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                      type="daterange" align="right" unlink-panels range-separator="至" start-placeholder="开始日期"
                                      end-placeholder="结束日期" v-model="filter.createdtimerange"  :picker-options="pickerOptions">
                                  </el-date-picker>
                              </span>
                              <span>
                                  <el-select v-model="filter.isTaoXi" style="width: 100px;">
                                      <el-option  label="淘系" value="0" ></el-option>
                                      <el-option  label="非淘系" value="1"></el-option>
                                  </el-select>
                              </span>
                              <span>
                                  <el-input v-model.trim="filter.processCode" maxlength="50" clearable placeholder="编码" style="width:100px;" />
                              </span>
                              <span v-if="filter.isTaoXi!=0">
                                  <el-select v-model="filter.isDelivery" clearable="" placeholder="状态" style="width:100px;">
                                      <el-option label="已发货" value="0"></el-option>
                                      <el-option label="未发货" value="1"></el-option>
                                  </el-select>
                              </span>
                              <span>
                                  <el-button type="primary" v-throttle="2000" @click="onSearch()">查询</el-button>
                              </span>
                          </div>
                        <el-radio-group v-model="showlable" size="mini" @input="onHandleShowlable()">
                            <el-radio-button :label="true">显示</el-radio-button>
                            <el-radio-button :label="false">隐藏</el-radio-button>
                        </el-radio-group>
                    </div>
                    <div style="height: 660px;" class="nrqk">
                        <!-- v-if="packagesbuscharDialog.visible" -->
                        <buscharr v-if="packagesbuscharDialog.visible" ref="buscharrRef" :toolbox="toolbox" :analysisData="packagesbuscharDialog?.data" :thisStyle="thisStyleView5" :gridStyle="gridStyleView5">
                            </buscharr>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import buscharr from '@/components/Bus/buscharforShooting.vue';
import dayjs from "dayjs";
import { formatTime } from "@/utils/tools";
import {getStatDailyOrderList
} from '@/api/inventory/packagesSetProcessing';//包装加工
 const startDate = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
 const endDate = formatTime(new Date(), "YYYY-MM-DD");
export default {
    name: "statdailyorder",
    components: { buscharr },
    props: {
        showHome: { type: Boolean, default: false }, // 本组件是否在首页中显示
    },
    data(){
        return {
            showlable:true,
            filter: {
                createdtimerange: [],
                stDate:startDate,
                edDate:endDate,
                isTaoXi:"0",
                isDelivery:null,
                processCode:null
            },
            toolbox: {
                magicType: { show: true, type: ['line', 'bar'] },
                restore: { show: false },
                saveAsImage: { show: true }},
            thisStyleView5: { width: '100%', height: '600px', 'box-sizing': 'border-box', 'line-height': '240px' },
            gridStyleView5: { left: '1%', right: 15, bottom: 0, top: '10%', containLabel: true },
            pickerOptions: {
                disabledDate(time) {
                // 设置禁用最小日期为2022年1月1日
                const minDate = new Date(1970, 0, 1);
                return time.getTime() < minDate.getTime();
                }
            },
            packagesbuscharDialog: { visible: false, title: "", data: [] }
        }
    },
    async mounted() {
        this.filter.createdtimerange = [startDate,endDate]
        this.onSearch();
    },
    methods: {
        onHandleShowlable () {
            setTimeout(() => {
                this.$refs.buscharrRef.onHandleShow(this.showlable);
             },100)
            // this.$nextTick(() => {
            // this.$refs.buscharrRef.onHandleShow(this.showlable)
            // })
        },
        async onSearch(){
            this.packagesbuscharDialog.visible = false;
                this.filter.startTime = null;
                this.filter.endTime = null;
                if (this.filter.createdtimerange) {
                    this.filter.stDate = this.filter.createdtimerange[0];
                    this.filter.edDate = this.filter.createdtimerange[1];
                }else
                {
                    this.$message({ message: '日期必须选择', type: "warning" });
                    return;
                }
                if(this.filter.isTaoXi==0)
                {
                    this.filter.isDelivery=null;
                }
                let params = { ...this.filter};
                var res = await getStatDailyOrderList(params);
                this.packagesbuscharDialog.visible = true;
                this.packagesbuscharDialog.data = res
             this.packagesbuscharDialog.title = ""
            this.onHandleShowlable()
        },


    },
};
</script>

<style lang="scss" scoped>
.sybj {
    min-width: 1100px;
    background-color: #f3f4f6;
    // padding: 75px 5px 5px 5px;
    height: 100%;
    overflow-y: auto;
}

.tjgd {
    width: 100%;
    // position: fixed;
    z-index: 999;
    box-sizing: border-box;
    padding: 10px 22px 20px 5px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    align-items: center;
    background-color: #fff;
}
.flexrow{
    display: flex; flex-direction: row;
}

.flexroww{
    // display: flex; flex-direction: row;
    float: left;
    margin-right: 850px;
}

.flexcolum{
    display: flex; flex-direction: column;
}

.tjgd span {
    margin: 5px;
}

.tjbt {
    /* background-color: aquamarine; */
    /* font-weight: bold; */
    color: #333;
    line-height: 30px;
    font-size: 14px;
    display: flex;
  justify-content: space-between;
}

.tjnrk1 {
    width: 60%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk2 {
    width: 40%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk3 {
    width: 50%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk4 {
    width: 50%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}
.nerbox{
    margin: 0 200px 0 auto;
}

.tjnrk5 {
    width: 100%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk6 {
    width: 100%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk10 {
    width: 100%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}


.tjnrnk {
    width: 100%;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 15px 20px;
    float: left;
    border-radius: 6px;
}

.ptsx {
    width: 85%;
    height: 90px;
    background-color: #f7f7f7;
    border-radius: 8px;
    margin: 10px auto;
    box-sizing: border-box;
    padding: 0 35px;
    line-height: 90px;
}

.ptsx span {
    font-size: 16px;
    color: #555;
}


.sydh {
    width: 100%;
    min-width: 1100px;
    height: 125px;

    z-index: 999;
}

.bzjgsj {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    box-sizing: border-box;
    padding: 35px 5%;

}

.sztjk {
    width: 15%;
    min-width: 75px;
    height: 80px;
    background-color: #f5faff;
    padding: 20px;
    text-align: center;
    margin: 5px 0.5%;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;


}

.sztjk .tjsz {
    font-size: 26px;
    text-align: center;
    color: #409eff;
}

.sztjk .tjmc {
    font-size: 14px;
    text-align: center;
    color: #409eff;
}



.sydhsx {
    width: 100%;
    height: 125px;
    background-color: #f3f4f6;
    margin: 0 auto;
    box-sizing: border-box;
    padding: 15px;
}
</style>
