<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent label-position="right"
        label-width="90px">
        <el-row>
          <el-form-item label="款式编码:" label-width="100">
            <el-input v-model.trim="filter.styleCode" placeholder="款式编码" @change="onSearch" />
          </el-form-item>
          <el-form-item label="商品编码:" label-width="100">
            <el-tooltip class="item" effect="dark" content="默认为精确匹配在开始或者结尾输入*进行模糊匹配" placement="top">
              <inputYunhan :inputt.sync="filter.goodsCode" :maxRows="500" :width="'150px'" v-model="filter.goodsCode"
                placeholder="商品编码" :clearable="true" @callback="callbackGoodsCode" title="商品编码"></inputYunhan>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="商品名称:" label-width="100">
            <el-input v-model.trim="filter.goodsName" placeholder="商品名称" @change="onSearch" />
          </el-form-item>
          <el-form-item label="采购员:" label-width="100">
            <el-select v-model="filter.brandId" clearable filterable placeholder="请选择采购员" style="width: 160px" multiple collapse-tags>
              <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="是否启用:" label-width="80">
            <el-select v-model="filter.isEnabled" clearable filterable placeholder="请选择状态" style="width: 140px">
              <el-option label="备用" value="0" />
              <el-option label="启用" value="1" />
              <el-option label="禁用" value="-1" />
            </el-select>
          </el-form-item>
          <el-form-item label="开款人:">
            <el-input v-model="filter.firstUserName" placeholder="开款人" maxlength="20" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onBack">确定返回</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onClear">清空已选</el-button>
          </el-form-item>
        </el-row>
        <!-- 已选择的商品编码 -->
        <el-row>
          <el-form-item label="已选择编码:">
            <div style="max-height: 120px; overflow: auto;">
              <el-tag :key="tag" v-for="(tag, index) in chooseTags" closable :disable-transitions="true"
                @close="handleClose(tag, index)">
                {{ tag }}
              </el-tag>
            </div>
          </el-form-item>
        </el-row>
      </el-form>

    </template>

    <!-- <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
      :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' @select='selectchange'
      :isSelection.sync="ischoice" :tableHandles='tableHandles' :isSelectColumn="!ischoice" :loading="listLoading">
    </ces-table> -->

    <vxetablebase :id="'hotsalegoodsbuildgoodsdoclist20221212'" :hasSeq="false" :border="true" :align="'center'"
      ref="table" :that='that' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list'
      :tableCols='tableCols' @select='selectchange' :isSelection.sync="ischoice" :tableHandles='tableHandles'
      :isSelectColumn="!ischoice" :loading="listLoading"
      :checkbox-config="{ labelField: 'id', highlight: true, range: true }" @checkbox-range-end="callback"
       ></vxetablebase>

    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>
  </my-container>
</template>

<script>
import inputYunhan from "@/components/Comm/inputYunhan";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatTime } from "@/utils";
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { getGroupKeyValue } from '@/api/operatemanage/base/product';
import { getAllProBrand } from '@/api/inventory/warehouse';
import {
  //分页查询店铺商品资料
  getList,
  //导入
  importData,
} from "@/api/inventory/basicgoods"
const tableCols = [
  { istrue: true, label: '', width: '100', type: "checkbox", },
  { istrue: true, prop: 'goodsCode', label: '商品编码', width: '140', sortable: 'custom', },
  { istrue: true, prop: 'goodsName', label: '商品名称', width: '230', },
  { istrue: true, prop: 'firstUserName', label: '开款人', width: '100', sortable: 'custom', },
  { istrue: true, prop: 'pictureBig', label: '图片', width: '60', type: 'imageGoodsCode', goods: { code: 'goodsCode', name: 'goodsName' } },
  { istrue: true, prop: 'styleCode', label: '款式编码', width: '130', sortable: 'custom', },
  { istrue: true, prop: 'shortName', label: '简称', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'costPrice', label: '成本价', width: '80', sortable: 'custom', },
  { istrue: true, prop: 'groupId', label: '运营组', width: '80', sortable: 'custom', formatter: (row) => row.groupId == 0 ? " " : row.groupName },
  { istrue: true, prop: 'modified', label: '修改时间', width: '150', sortable: 'custom', formatter: (row) => formatTime(row.modified, 'YYYY-MM-DD HH:mm:ss') },
  { istrue: true, prop: 'isEnabled', label: '是否启用', width: '100', sortable: 'custom', formatter: (row) => row.isEnabled == 0 ? "备用" : (row.isEnabled == 1 ? "启用" : (row.isEnabled == -1 ? "禁用" : "")) },
  { istrue: true, prop: 'brandId', label: '采购组', width: '80', sortable: 'custom', formatter: (row) => row.brandId == 0 ? " " : row.brandName },
  { istrue: true, prop: 'weight', label: '重量', width: '60', sortable: 'custom' },
  { istrue: true, prop: 'expressWeight', label: '快递账单重量', width: '120', sortable: 'custom' },
  { istrue: false, prop: 'computeRatio', label: '计算重量占比', width: '80', sortable: 'custom', formatter: (row) => !row.computeRatio ? " " : row.computeRatio * 100 + '%' },
  { istrue: false, prop: 'weightDifference', label: '重量差', width: '80', sortable: 'custom' },
  { istrue: false, prop: 'supplierCode', label: '供应商编码', width: '100', sortable: 'custom' },
  { istrue: false, prop: 'supplierName', label: '供应商名称', width: '100', sortable: 'custom' },
  { istrue: false, prop: 'barcode', label: '国际条码', width: '90', sortable: 'custom' },
  { istrue: false, prop: 'color', label: '颜色', width: '60', sortable: 'custom' },
  { istrue: false, prop: 'supplierGoodsCode', label: '供应商商品编码', width: '130', sortable: 'custom' },
  { istrue: false, prop: 'supplierStyle', label: '供应商商品款号', width: '130', sortable: 'custom' },
  { istrue: false, prop: 'virtualType', label: '虚拟分类', width: '100', sortable: 'custom' },
  { istrue: false, prop: 'goodsType', label: '商品类型', width: '100', sortable: 'custom' },
  { istrue: false, prop: 'remark', label: '备注', width: '100', sortable: 'custom' },
  { istrue: false, prop: 'itemType', label: '商品属性', width: '100', sortable: 'custom' },
  { istrue: false, prop: 'stockDisabled', label: '是否禁止同步', width: '120', sortable: 'custom', formatter: (row) => row.stockDisabled == 0 ? "启用同步" : (row.stockDisabled == 1 ? "禁用同步" : (row.stockDisabled == -1 ? "部分禁用" : "")) },
  { istrue: false, prop: 'unit', label: '单位', width: '100', sortable: 'custom' },
  { istrue: false, prop: 'labels', label: '标签', width: '100', sortable: 'custom' },
  { istrue: false, prop: 'stockType', label: '链接同步状态', width: '120', sortable: 'custom' },
  { istrue: false, prop: 'skuCodes', label: '辅助码', width: '100', sortable: 'custom' }
];
const tableHandles1 = [
  // {label:"导入", handle:(that)=>that.startImport()},
  // {label:"确定选择", handle:(that)=>that.onselected()},
];
export default {
  name: 'goodsinfo',
  components: { cesTable, MyContainer, MyConfirmButton, vxetablebase, inputYunhan },
  props: {
    ischoice: { type: Boolean, default: false },
    chooseTags: { type: Array, default: () => [] }
  },
  data() {
    return {
      that: this,
      filter: {
        styleCode: null,
        goodsCode: null,
        goodsName: null,
        groupId: null,
        brandId: [],
        isEnabled: '1',
      },
      list: [],
      selectarray: [],
      summaryarry: {},
      pager: { OrderBy: "goodsCode", IsAsc: true },
      tableCols: tableCols,
      tableHandles: tableHandles1,
      platformList: [],
      shopList: [],
      groupList: [],
      brandList: [],
      dialogVisible: false,
      total: 0,
      sels: [],
      listLoading: false,
      pageLoading: false,
      fileList: [],
      yesnoList: [
        { value: true, label: "是" },
        { value: false, label: "否" }
      ],
      selrows: [],
    }
  },
  async mounted() {
    await this.setGroupSelect();
    await this.setBandSelect();
    await this.getlist();
    //this.shifttoclick();
  },
  methods: {
    async callbackGoodsCode(val) {
      this.filter.goodsCode = val;
    },
    callback(val) {
      this.tablelist = [];
      this.tablelist = val;
      var goodsCode = val.map((item) => {
        return item.goodsCode;
      })
      this.chooseTags = goodsCode;
      console.log("goods返回值", this.chooseTags)
    },
    onBack() {
      this.$emit('searchClick', this.chooseTags);
    },
    onClear() {
      this.chooseTags = [];
      this.selrows = [];
      this.$refs.table.clearSelection();
    },
    handleClose(tag, index) {
      this.chooseTags.splice(index, 1);
      this.selrows.splice(index, 1);
      this.tablelist.splice(index, 1)
      // this.list.forEach(f => {
      //   let obj = this.chooseTags.findIndex((v) => (v == f.goodsCode));
      //   if (obj === -1) {
      //     this.$refs.table.toggleRowSelection(f, false);
      //   }
      // });
      this.$refs.table.toggleRowSelection(this.tablelist, false);
    },
    async setGroupSelect() {
      const res = await getGroupKeyValue({});
      this.groupList = res.data;
    },
    async setBandSelect() {
      var res = await getAllProBrand();
      if (!res?.success) return;
      this.brandlist = res.data.map(item => {
        return { value: item.key, label: item.value };
      });
    },
    //获取查询条件
    getCondition() {
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = { ...pager, ...page, ... this.filter }
      return params;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    onShowChoice() {
      // this.ischoice=true;
    },
    //分页查询
    async getlist() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true;
      var res = await getList(params);
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry = res.data.summary;
      data.forEach(d => {
        d._loading = false;
        d.id = d.goodsCode;
      })
      this.list = data
      this.listLoading = false;
    },

    //排序查询
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        var orderBy = column.prop;
        this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    },
    selsChange: function (sels) {
      this.sels = sels
    },
    selectchange: function (rows, row) {
      //先把当前也的数据全部移除
      this.list.forEach(f => {
        let index = this.chooseTags.findIndex((v) => (v === f.goodsCode));
        if (index !== -1) {
          this.chooseTags.splice(index, 1);
          this.selrows.splice(index, 1);
        }
      });
      //把选中的添加
      rows.forEach(f => {
        let index = this.chooseTags.findIndex((v) => (v === f.goodsCode));
        if (index === -1) {
          this.chooseTags.push(f.goodsCode);
          this.selrows.push(f);
          console.log("选中数据", this.selrows);
        }
      });

      ///
      let _this = this;
      if (rows.length > 0) {
        var a = [];
        rows.forEach(element => {
          let b = _this.list.indexOf(element);
          a.push(b + 1);
        });

        let d = _this.list.indexOf(row);

        var b = Math.min(...a)
        var c = Math.max(...a)

        a.push(d);
        if (d < b) {
          var b = _this.list.indexOf(row);
          var c = Math.max(...a)
        } else if (d > c) {
          var b = Math.min(...a) - 1
          var c = Math.max(...a)
        } else {
          var b = Math.min(...a) - 1
          var c = _this.list.indexOf(row) + 1;
        }

        let neww = [b, c];
        _this.selectarray = neww;
      }
    },
    shifttoclick() {
      var _this = this;
      document.onkeydown = function (e) {
        let key = window.event.keyCode;
        if (key == 16) {
          _this.selrows = [];
          _this.chooseTags = [];
          window.event.preventDefault()
          if (_this.list && _this.selectarray) {
            _this.$refs.table.clearSelection();
            setTimeout(() => {
              for (var i = _this.selectarray[0]; i < _this.selectarray[1]; i++) {
                _this.$refs.table.toggleRowSelection(_this.list[i]);
                _this.chooseTags.push(_this.list[i].goodsCode);
                _this.selrows.push(_this.list[i]);
              }
            }, 100);
          }
        }
      };

    },
    async getchoicelist() {
      if (!this.selrows || this.selrows.length == 0)
        this.$message({ message: "你还没有选择", type: "warning", });
      return this.selrows
    },
    async getchoicelistOnly() {
      if (!this.selrows || this.selrows.length == 0)
        this.$message({ message: "请选择一条数据，", type: "warning", });
      if (!this.selrows || this.selrows.length > 1)
        this.$message({ message: "只能选择一条数据", type: "warning", });
      return this.selrows
    },
    startImport() {
      this.dialogVisible = true;
    },
    cancelImport() {
      this.dialogVisible = false;
    },
    beforeRemove() {
      return false;
    },
    //上传成功
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    async uploadFile(item) {
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      const res = await importData(form);
      if (res?.success) {
        this.$message({ message: '上传成功,正在导入中...', type: "success", });
      }
      else {
        this.$message({ message: res.message, type: "warning", });
      }
    },
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary {
  margin-right: 7px;
}
</style>
