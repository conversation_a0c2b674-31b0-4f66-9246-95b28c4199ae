<template>
    <div style="height:100%;padding:10px;overflow: auto;">
        <template>
           
        </template> 
        <div id="echartprocodeanalysis1" style="width: 100%;height: 450px; box-sizing:border-box; line-height: 360px;"/>
    </div>
</template>

<script>
import * as echarts from 'echarts'
import container from '@/components/my-container/nofooter'
import { queryWithholdAnalysis,QueryWithholdAnalysis4PddKkhz} from "@/api/order/orderdeductmoney"

export default {
    name: 'YunhanAdminProcodesimilarityanalysis',
    components: {container},

    data() {
        return {
            filter: {
                timerange:null,
                startDate:null,
                endDate:null,
                proCode:null,
                platform:null,
                reportType:null
            },
            period:0,
            pageLoading: false,
            listLoading:false
            };
    },

    mounted() {
        
    },

    methods: {
        async onSearch(para){          
            this.filter.proCode = para.proCode
            this.filter.platform = para.platform
            this.filter.reportType = para.reportType
            this.filter.timerange = para.timerange
            await this.getanalysisdata()
        },
        async onSearchPddKkhz(para){
            this.filter.proCode = para.proCode
            this.filter.platform = para.platform
            this.filter.reportType = para.reportType
            this.filter.timerange = para.timerange

            if (!this.filter.timerange||this.filter.timerange.length<2){
                this.$message({message: "请先选择日期！",type: "warning",});
                return;
            } 
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            var parm={...this.filter};
            parm.period=this.period;
            const res = await QueryWithholdAnalysis4PddKkhz(parm);
            if (!res?.code)  return;       
            var chartDom = document.getElementById('echartprocodeanalysis1');
            var myChart = echarts.init(chartDom);
            myChart.clear();

            if (!res.data) {
                this.$message({message: "没有数据!",type: "warning",});
                return;
            }
            var option = this.Getoptions(res.data);
            option && myChart.setOption(option);

        },
        async onfresh() {
            await this.getanalysisdata()
        },
        async getanalysisdata() {
            if (!this.filter.timerange||this.filter.timerange.length<2){
                this.$message({message: "请先选择日期！",type: "warning",});
                return;
            } 
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            var parm={...this.filter};
            parm.period=this.period;
            const res = await queryWithholdAnalysis(parm);
            if (!res?.code)  return;       
            var chartDom = document.getElementById('echartprocodeanalysis1');
            var myChart = echarts.init(chartDom);
            myChart.clear();
            if (!res.data) {
                this.$message({message: "没有数据!",type: "warning",});
                return;
            }
            var option = this.Getoptions(res.data);
            option && myChart.setOption(option);
        },
        Getoptions(element){
            var series=[]
            element.series.forEach(s=>{
            series.push({smooth: true, ...s})
            })
            var yAxis=[]
            element.yAxis.forEach(s=>{
            yAxis.push({type: 'value',offset:s.offset,position:s.position,name: s.name,axisLabel: {formatter: '{value}'+s.unit}})
            })
            var option = {
                title: {text: element.title},
                tooltip: {trigger: 'axis'},
                legend: {
                data: element.legend
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                toolbox: {feature: {
                    magicType: {show: true, type: ['line', 'bar']},
                    //restore: {show: true},
                }},
                xAxis: {
                    type: 'category',
                    data: element.xAxis
                },
                yAxis: yAxis,
                series:  series
            };
            return option;
        },
    },
};
</script>

<style lang="scss" scoped>

</style>