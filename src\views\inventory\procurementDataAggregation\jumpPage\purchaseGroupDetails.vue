<template>
  <my-container>
    <template #header>
      <div style="margin-bottom: 10px;">
        <el-button type="primary" @click="onExport">导出</el-button>
        <el-button style="margin-left: 5px; text-overflow: ellipsis; white-space: nowrap; overflow: hidden;"
          @click="onUpdateTime">
          更新时间：{{ renewTime }}
        </el-button>
      </div>
    </template>
    <div id="YunHanAdminGoodspurchaseGroupDetails202501181550" v-show="!dataVisible" style="width: 100%;height: 100%;">
      <vxetablebase v-show="!dataVisible" :id="'purchaseGroupDetails2025010420223'" ref="table" :that='that'
        :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
        :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" style="width: 100%;margin: 0"
        :loading="loading" :height="'100%'" :showheaderoverflow="false" @cellStyle="cellStyle" cellStyle
        :hasSeq="hasSeq">
      </vxetablebase>
    </div>

    <el-dialog title="更新时间" :visible.sync="updateTimeVisible" width="30%" v-dialogDrag>
      <div style="display: flex;">
        <vxetablebase :id="'updateTime202501051635'" :tablekey="'updateTime202501051635'" ref="table2" :that='that'
          :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='updatesortchange' :isNeedExpend="false"
          :tableData='updateTableData' :tableCols='updateTableCols' :isSelection="false" :isSelectColumn="false"
          style="width: 100%;  margin: 0" :loading="updateloading" :height="'250px'">
        </vxetablebase>
      </div>
    </el-dialog>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from 'dayjs';
import { formatTime } from "@/utils";
import html2canvas from 'html2canvas';
import decimal from '@/utils/decimal'
import exportExecl from "@/utils/exportExecl.js"
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew';
import setScorecomponent from '@/views/inventory/procurementDataAggregation/profit3CommissionPercentage/setScorecomponent';
import scoreSetLogDialog from '@/views/inventory/procurementDataAggregation/profit3CommissionPercentage/scoreSetLogDialog';
import { importProfit3GroupDetail, getProfit3GroupStopDetailList, calPurchaseSumProfit3GroupRpt, getRenewLog } from '@/api/inventory/purchaseSummary';

const tableCols = [
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '日期', prop: 'rptDate', formatter: (row) => formatTime(row.rptDate, "YYYY-MM") },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '组别', prop: 'deptName' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '区域', prop: 'areaName' },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '全量扣款（专员平均值）', prop: 'deductAmountAvg' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '全量得分', prop: 'deductAmountAvgScore', headerBgColor: '#FFFF00' },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '个人扣款（小组合计）', prop: 'deductAmount' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '个人得分', prop: 'deductAmountScore', headerBgColor: '#FFFF00' },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '涨降价（小组合计）', prop: 'allotDownAmount' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '降价得分', prop: 'allotDownAmountScore', headerBgColor: '#FFFF00' },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '周转天数（小组合计）', prop: 'brandTurnoverDay' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '周转得分', prop: 'brandTurnoverDayScore', headerBgColor: '#FFFF00' },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '滞销退款金额（小组合计）', prop: 'refundAmount' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '退款得分', prop: 'refundAmountScore', headerBgColor: '#FFFF00' },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '全仓缺货次数（小组合计）', prop: 'fullWmsMonthLackCount' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '全仓缺货得分', prop: 'fullWmsMonthLackCountScore', headerBgColor: '#FFFF00' },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '本仓缺货次数（小组合计）', prop: 'inWmsMonthLackCount' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '本仓缺货得分', prop: 'inWmsMonthLackCountScore', headerBgColor: '#FFFF00' },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '云仓缺货次数（小组合计）', prop: 'wmsMonthLackCount' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '云仓缺货得分', prop: 'wmsMonthLackCountScore', headerBgColor: '#FFFF00' },

  { sortable: 'custom', istrue: true, width: '110', align: 'center', label: '小组日常检查扣分（小组合计）', prop: 'dayWorkDeductScore' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '小组摘品扣分（小组合计）', prop: 'packingGoodsTaskDeductScore' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '小组滞销扣分（小组合计）', prop: 'brandRefundDeductScore' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '扣分总计', prop: 'allDeductScore', headerBgColor: '#FFFF00' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '合计得分', prop: 'allScore' },
];

const updateTableCols = [
  // { sortable: 'custom', istrue: true, align: 'center', label: '更新时间', prop: 'rptDate' },
  { sortable: 'custom', istrue: true, width: 'auto', align: 'center', label: '操作类型', prop: 'calType' },
  { sortable: 'custom', istrue: true, width: 'auto', align: 'center', label: '操作人', prop: 'operateUserName' },
  { sortable: 'custom', istrue: true, width: 'auto', align: 'center', label: '更新时间', prop: 'operateTime' },
];

export default {
  name: "purchaseGroupDetails",
  components: {
    MyContainer, vxetablebase, setScorecomponent, scoreSetLogDialog
  },
  data() {
    return {
      hiddenColumns: [],
      hasSeq: true,
      that: this,
      loading: false,
      total: 0,
      tableData: [],
      tableCols: tableCols,
      dataVisible: false,
      renewTime: null,//更新时间
      rptDate: null,//月份
      filter: {
        currentPage: 1,
        pageSize: 99999,
        orderBy: 'rptDate',
        isAsc: false,
        //过滤条件
        rptDate: null,//月份
        deptNameList: [],//组别
        areaNameList: [],//区域
      },
      computeVisible: false,//计算
      startTime: null,//计算日期
      uploadLoading: false,//导入加载
      importVisible: false,//导入弹窗显示
      importMonth: null,//导入月份
      fileList: [],//上传文件列表
      setScoreVisible: false,//分值设置弹窗
      scoreLogVisible: false,//分值日志
      loadingUrl: false,
      updateTimeInfo: {
        type: '小组-试算',
        orderBy: 'operateTime',
        isAsc: false,
      },
      updateTimeVisible: false,//更新时间
      updateTableData: [],//更新时间
      updateTableCols: updateTableCols,//更新时间列
      updateloading: false,//更新时间加载
    };
  },
  async mounted() {
    let _this = this;
    _this.rptDate = _this.$route.query.rptDate;
    if (_this.rptDate != 0) {
      _this.filter.rptDate = _this.rptDate;
      _this.getList()
    }
  },
  methods: {
    async onExport() {
      this.loading = true;
      await new Promise(resolve => setTimeout(resolve, 500));
      this.hasSeq = false
      setTimeout(async () => {
        const endDate = formatTime(dayjs(), "YYYY-MM-DD");
        exportExecl("YunHanAdminGoodspurchaseGroupDetails202501181550", `小组明细数据` + endDate + '.xlsx');
        this.hasSeq = true
        await new Promise(resolve => setTimeout(resolve, 500));
        this.loading = false;
      }, 1000);
    },
    async cellStyle(row, column, callback) {
      if (column.field === 'allDeductScore' || column.field === 'fullWmsMonthLackCountScore' || column.field === 'brandTurnoverDayScore' || column.field === 'allotDownAmountScore' || column.field === 'deductAmountScore' || column.field === 'deductAmountAvgScore' || column.field === 'inWmsMonthLackCountScore' || column.field === 'wmsMonthLackCountScore' || column.field === 'refundAmountScore') {
        callback({ backgroundColor: '#FFFF00' });
      }
    },
    async getList(type) {
      if (type == 'search') {
        this.filter.currentPage = 1;
      }
      const firstDayOfMonth = this.rptDate ? dayjs(this.rptDate).startOf('month').format('YYYY-MM-DD') : ''
      const params = { ...this.filter, rptDate: firstDayOfMonth };
      this.loading = true;
      const { data, success } = await getProfit3GroupStopDetailList(params);
      if (success) {
        this.tableData = data.list;
        this.renewTime = data.extData.lastRenewTime;
        this.tableData.forEach(item => {
          item.rptDate = dayjs(item.rptDate).format('YYYY-MM');
        })
        this.total = data.total;
        let hiddenList = data.extData.hiddenRows
          .filter(item => item.includes("得分"))
          .map(item => item.replace("得分", ""));
        this.onShowupMethod(hiddenList);
        this.loading = false;
      } else {
        this.$message.error('获取列表失败');
        this.loading = false;
      }
    },
    async onUpdateTime() {
      this.updateloading = true
      const { data, success } = await getRenewLog({ rptDate: this.filter.rptDate, ...this.updateTimeInfo })
      this.updateloading = false
      if (!success) return
      this.updateTableData = data.list
      this.updateTimeVisible = true;
    },
    updatesortchange({ order, prop }) {
      if (prop) {
        this.updateTimeInfo.orderBy = prop
        this.updateTimeInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.onUpdateTime()
      }
    },
    async onShowupMethod(list) {
      this.$nextTick(() => {
        const tableRef = this.$refs.table;
        if (tableRef) {
          let takePlace = [];
          let payment = [];
          this.tableCols.forEach(item => {
            if (list.some(substring => item.label.includes(substring))) {
              if (item.cols && Array.isArray(item.cols)) {
                item.cols.forEach(col => {
                  payment.push(col.prop);
                });
              } else {
                payment.push(item.prop);
              }
            } else {
              if (item.cols) {
                item.cols.forEach(col => {
                  takePlace.push(col.prop);
                });
              } else {
                takePlace.push(item.prop);
              }
            }
          });
          this.hiddenColumns = payment
          tableRef.changecolumn(payment);
          tableRef.changecolumn_setTrue(takePlace);
          if (this.$refs.table1) {
            this.$refs.table1.changecolumn(payment);
            this.$refs.table1.changecolumn_setTrue(takePlace);
          }
        }
        this.$forceUpdate();
      });
    },
    // 排序查询
    sortchange({ order, prop }) {
      if (prop) {
        this.filter.orderBy = prop
        this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  },
}
</script>
<style scoped lang="scss">
::v-deep .el-select__tags-text {
  max-width: 40px;
}

.dialogcss ::v-deep .body--wrapper {
  height: auto !important;
}
</style>
