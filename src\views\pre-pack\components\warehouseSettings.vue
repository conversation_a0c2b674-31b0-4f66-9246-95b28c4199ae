<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="filter"
        label-position="right"
        label-width="90px"
        @submit.native.prevent
      >
        <el-select
          v-model="filter.warehouseId"
          clearable
          filterable
          :collapse-tags="true"
          placeholder="请选择仓库"
          style="width: 160px"
        >
          <el-option v-for="item in warehouselist" :key="item.name" :label="item.name" :value="item.wms_co_id" />
        </el-select>
        <el-select v-model="filter.isUse" clearable filterable placeholder="是否启用" style="width: 110px">
          <el-option :value="1" label="启用"></el-option>
          <el-option :value="0" label="禁用"></el-option>
        </el-select>
        <el-select v-model="filter.isWc" clearable filterable placeholder="是否外仓" style="width: 110px">
          <el-option :value="1" label="外仓"></el-option>
          <el-option :value="0" label="非外仓"></el-option>
        </el-select>
        <el-select v-model="filter.isJiaGong" clearable filterable placeholder="是否加工仓" style="width: 110px">
          <el-option :value="1" label="加工仓"></el-option>
          <el-option :value="0" label="非加工仓"></el-option>
        </el-select>
        <el-select v-model="filter.isDaiFa" clearable filterable placeholder="是否代发" style="width: 110px">
          <el-option :value="1" label="代发仓"></el-option>
          <el-option :value="0" label="非代发仓"></el-option>
        </el-select>
        <el-select v-model="filter.isSendWarehouse" clearable filterable placeholder="发货仓" style="width: 110px">
          <el-option value="是" label="发货仓"></el-option>
          <el-option value="否" label="非发货仓"></el-option>
        </el-select>

        <el-input v-model="filter.keywords" placeholder="请输入关键字进行查询" style="width:200px;"></el-input>


        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>

    <vxetablebase
      :id="'PrepackTbWarehouse20240123'"
      ref="table"
      :that="that"
      :is-index="true"
      :hasexpand="true"
      :summaryarry="summaryarry"
      :table-data="list"
      :table-cols="tableCols"
      :border="true"
      :table-handles="tableHandles"
      :loading="listLoading"
      @sortchange="sortchange"
    >
      <template slot="right">
        <vxe-column title="操作" :field="'col_opratorcol'" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="text" @click="onEdit(row)">编辑</el-button>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>

    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <el-dialog v-dialogDrag title="编辑仓库" :visible.sync="addshow" width="900" style="margin-top: -6vh;">
      <el-form ref="addForm" :model="addForm" label-width="160px" :rules="addFormRules">
        <el-form-item label="仓库名称" prop="name">
            {{ addForm.name }}
          </el-form-item>
        <el-form-item label="是否启用" prop="isUse">
            <el-radio-group v-model="addForm.isUse">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="区域" prop="region">
            <el-input v-model.trim="addForm.region" clearable></el-input>
            <span>可填：<el-button type="text" @click="addForm.region='义乌'">义乌</el-button>
              <el-button type="text" @click="addForm.region='南昌'">南昌</el-button>
              <el-button type="text" @click="addForm.region='其他'">其他</el-button>
             等</span>
          </el-form-item>

        <el-form-item label="是否发货仓" prop="isSendWarehouse">
          <el-radio-group v-model="addForm.isSendWarehouse">
              <el-radio label="是">是</el-radio>
              <el-radio label="否">否</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="是否外仓" prop="isWc">
          <el-radio-group v-model="addForm.isWc">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>

        <el-form-item label="是否加工仓" prop="isJiaGong">
          <el-radio-group v-model="addForm.isJiaGong">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>

        <el-form-item label="是否代发" prop="isDaiFa">
          <el-radio-group v-model="addForm.isDaiFa" >
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="计划采购建议导入名" prop="purchasePlanImpKeyName">
            <el-input v-model.trim="addForm.purchasePlanImpKeyName" clearable></el-input>
            <span>可 <el-button type="text" @click="addForm.purchasePlanImpKeyName=addForm.name">填入仓库名</el-button>，不需要导入计划采购建议的数据，请
              <el-button type="text" @click="addForm.purchasePlanImpKeyName=''">清空</el-button></span>
          </el-form-item>

        <el-form-item label="是否预包加工仓" prop="isPrePack">
          <el-radio-group v-model="addForm.isPrePack" >
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
        </el-form-item>

        <el-form-item label="发货率" prop="sendGoodRate">
          <el-input-number v-model="addForm.sendGoodRate" :min="0" :max="100" :precision="2" auto-complete="off" />
        </el-form-item>

        <el-form-item label="最大发货重量" prop="maxWeight">
          <el-input-number v-model="addForm.maxWeight" :min="0" :max="9999" :precision="2" auto-complete="off" />
        </el-form-item>
        <el-form-item label="商品重量负责人" prop="productWeightManagerList">
          <el-select v-model="addForm.productWeightManagerList" multiple :clearable="true" filterable style="width: 100%;"
            remote reserve-keyword placeholder="请选择商品重量负责人" :remote-method="remoteMethod" @change="valueChanged($event)"
            :loading="loading" ref="refSelect">
            <el-option v-for="item in options" :key="selectKey + item.value + item.extData.defaultDeptId"
              :label="item.label" :value="item.value">
              <span>{{ item.label }}</span>
              <span style=" color: #8492a6; " v-show="item.extData.empStatusText">
                ({{ item.extData.position }},{{ item.extData.empStatusText }}{{ item.extData.jstUserName ?
                  "," + item.extData.jstUserName:""}})
              </span>
              <span style=" color: #8492a6; "> {{ item.extData.deptName }}</span>
            </el-option>
          </el-select>
        </el-form-item>


      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click.native="addshow = false">取消</el-button>
          <my-confirm-button type="submit" :validate="saveFormValidate" :loading="saveLoading" @click="onSave" />
        </div>
      </template>
    </el-dialog>

  </my-container>
</template>

<script>
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser'
import MyContainer from '@/components/my-container'
import vxetablebase from '@/components/VxeTable/yh_vxetable.vue'
import { getAllWarehouse } from '@/api/inventory/warehouse'
import MyConfirmButton from '@/components/my-confirm-button'
import {
  pageGetTbWarehouseAsync,
  // 更新
  setTbWarehouseRateAsync

} from '@/api/inventory/prepack.js'

const tableCols = [
  { istrue: true, prop: 'name', label: '仓库', align: 'left', sortable: 'custom' },
  { istrue: true, prop: 'region', label: '区域', sortable: 'custom',width: 90 },
  { istrue: true, prop: 'isUse', label: '启用', sortable: 'custom',width: 90 , formatter: (row) => row.isUse && row.isUse == 1 ? "是" : "否" },
  { istrue: true, prop: 'isSendWarehouse', label: '发货仓', sortable: 'custom',width: 90  },
  { istrue: true, prop: 'isWc', label: '外仓', sortable: 'custom',width: 90 , formatter: (row) => row.isWc && row.isWc == 1 ? "是" : "否" },
  { istrue: true, prop: 'purchasePlanImpKeyName', label: '计划采购建议名', sortable: 'custom' },
  { istrue: true, prop: 'maxWeight', label: '最大发货重量', sortable: 'custom' ,width: 120 },
  // { istrue: true, prop: 'sendGoodRate', label: '发货率', sortable: 'custom', formatter: (row) => row.sendGoodRate == null ? "" : row.sendGoodRate + "%" },
  { istrue: true, prop: 'isDaiFa', label: '代发仓', sortable: 'custom',width: 90 , formatter: (row) => row.isDaiFa && row.isDaiFa == 1 ? "是" : "否" },
  { istrue: true, prop: 'isJiaGong', label: '加工仓', sortable: 'custom',width: 90 , formatter: (row) => row.isJiaGong && row.isJiaGong == 1 ? "是" : "否" },
  { istrue: true, prop: 'isPrePack', label: '预包加工仓', sortable: 'custom',width: 90 , formatter: (row) => row.isPrePack ? '是' : '否' }
]

const tableHandles1 = [
]

export default {
  name: 'PrepackTbWarehouse',
  components: { vxetablebase, MyContainer, MyConfirmButton},
  data() {
    return {
      that: this,
      saveLoading: false,
      isadd: true,
      addForm: {
        isWc: 0,
        isUse:1,
        region:'',
        isSendWarehouse:'',
        isJiaGong:0,
        isDaiFa:0,
        purchasePlanImpKeyName:'',
        isPrePack:false,
        sendGoodRate:0,
        maxWeight:0,
        productWeightManagerList:[],
        productWeightManagerNameList:[]
      },
      warehouselist: [],
      addFormRules: {
        isUse: [{ required: true, message: '请选择是否启用', trigger: 'change' }],
        isWc: [{ required: true, message: '请选择是否外仓', trigger: 'change' }],
        isJiaGong: [{ required: true, message: '请选择是否外仓', trigger: 'change' }],
        isDaiFa: [{ required: true, message: '请选择是否代发仓', trigger: 'change' }],
        isSendWarehouse: [{ required: true, message: '请选择是否发货仓', trigger: 'change' }],
        sendGoodRate: [{ required: true, message: '请输入发货率', trigger: 'blur' }]
      },
      filter: {
        warehouseId: null,
        isUse:null,
        isWc:null,
        isJiaGong:null,
        isDaiFa:null,
        isSendWarehouse:''
      },
      addshow: false,
      list: [],
      summaryarry: {},
      pager: { orderBy: '', isAsc: false },
      tableCols: tableCols,
      tableHandles: tableHandles1,
      total: 0,
      sels: [],
      listLoading: false,
      pageLoading: false,
      loading:false,
      options: [],
      orgOptions: [],
      selrows: []
    }
  },
  async mounted() {
    await this.init()
    await this.getlist()
  },
  methods: {
    valueChanged(newValue) {
      if (newValue.length == 0) {
        this.addForm.productWeightManagerList = []
        this.addForm.productWeightManagerNameList = []
        this.options = []
        return
      }
      this.addForm.productWeightManagerList = newValue
      this.$refs['refSelect'].query = '';
      let a = this.options.filter(opt => newValue.includes(opt.value)).map(opt => opt.label);
      this.addForm.productWeightManagerNameList.push(...a)
    },
    async remoteMethod(query) {
        if (query && query.length > 50) return this.$message.error("输入内容过长");
        this.loading = true;
        if (query !== '') {
            let rlt= await QueryAllDDUserTop100({ keywords: query });
            if (rlt && rlt.success) {
                this.options = rlt.data?.map(item => {
                    return { label: item.userName, value: item.ddUserId, extData:item }
                });
            }
        } else {
            this.options = [...this.orgOptions];
        }
        this.loading = false;
    },
    async callback(val) {
      this.filter.goodscodes = val
    },
    async init() {
      const res = await getAllWarehouse()
      const warehouselist1 = res.data.filter((x) => x.name.indexOf('代发') < 0)
      this.warehouselist = warehouselist1
    },
    saveFormValidate() {
      let isValid = false
      this.$refs.addForm.validate(valid => {
        isValid = valid
      })
      return isValid
    },
    async onSave() {
      const res = await setTbWarehouseRateAsync(this.addForm)
      this.saveLoading = false
      if (!res.success) {
        return
      }
      await this.onSearch()
      this.$message.success('保存成功')
      this.addshow = false
    },
    async onEdit(row) {
      this.isadd = false
      this.addForm = {...row}
      this.addForm.productWeightManagerList = []
      this.addForm.productWeightManagerNameList = []
      this.options = [];
      const idList = row.productWeightManagerList || [];
      const nameList = row.productWeightManagerNameList || [];
      this.listLoading = true;
      for (let i = 0; i < idList.length; i++) {
        const userName = nameList[i];
        try {
          const res = await QueryAllDDUserTop100({ keywords: userName });
          if (res && res.success && Array.isArray(res.data)) {
            // 按 ddUserId 匹配（不是只按名字）
            const matched = res.data.find(item => idList.includes(item.ddUserId));
            if (matched) {
              const optionItem = {
                label: matched.userName,
                value: matched.ddUserId,
                extData: matched,
              };
              this.options.push(optionItem);
              this.addForm.productWeightManagerList.push(optionItem.value);
              this.addForm.productWeightManagerNameList.push(optionItem.label);
            }
          }
        } catch (error) {
          console.error(`搜索 ${userName}失败`, error);
        }
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      this.listLoading = false;
      await this.$nextTick(() => {
        this.addshow = true
      })
    },
    // 获取查询条件
    getCondition() {
      var pager = this.$refs.pager.getPager()
      var page = this.pager
      const params = { ...pager, ...page, ... this.filter }
      return params
    },
    // 查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    // 分页查询
    async getlist() {
      var params = this.getCondition()
      if (params === false) {
        return
      }
      this.pageLoading = true
      var res = await pageGetTbWarehouseAsync(params)
      this.pageLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total
      res.data.list.forEach((item) => {
        if (item.isWc === null) {
          item.isWc = 0
        }
      })
      const data = res.data.list
      this.summaryarry = res.data.summary
      this.list = data
    },
    // 排序查询
    async sortchange(column) {
      if (!column.order) { this.pager = {} } else {
        var orderBy = column.prop
        this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf('descending') == -1 }
      }
      await this.onSearch()
    }
  }
}
</script>
