<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="日期:">
          <el-date-picker style="width:220px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
            :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
        </el-form-item>
        <el-form-item label="平台:">
          <el-select v-model="filter.platform" placeholder="平台" style="width: 100px" @change="onchangeplatform"
            :clearable="true" :collapse-tags="true" filterable>
            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="店铺:">
          <el-select v-model="filter.shopCode" @change="onSearch" placeholder="请选择" clearable :collapse-tags="true"
            filterable>
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
          </el-select>
        </el-form-item>
        <el-form-item label="运营组:">
          <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组" style="width: 90px">
            <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="内部订单:">
          <el-input v-model="filter.orderNoInner" placeholder="内部订单" @change="onSearch" />
        </el-form-item>
        <el-form-item label="链接ID:">
          <el-input v-model="filter.proCode" placeholder="链接ID" @change="onSearch" />
        </el-form-item>
        <el-form-item label="商品编码:">
          <el-input v-model="filter.goodsCode" placeholder="商品编码" @change="onSearch" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>

    <!-- <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
      :summaryarry="summaryarry" @summaryClick='onsummaryClick' :tableData='list' :tableCols='tableCols'
      :isSelectColumn="true" :tableHandles='tableHandles' :loading="listLoading">
    </ces-table> -->

    <vxetablebase
      :id="'replacesenddaily202302031421'" :border="true" :align="'center'" :tablekey="'replacesenddaily202302031421'"
      ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      :isSelectColumn="true" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry'
      :tableData='list' @summaryClick='onsummaryClick' :tableCols='tableCols'
      :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:96%;margin: 0"   :xgt="9999">
    </vxetablebase>

    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag>
      <el-row>
        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-select v-model="filterImport.platform" placeholder="请选择平台" :clearable="true" :collapse-tags="true">
            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row>
        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
            accept=".xlsx" :http-request="uploadFile" :file-list="fileList">
            <template #trigger>
              <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload"
              :loading="filterImport.uploadLoading">{{ (filterImport.uploadLoading ? '上传中' : '上传') }}</el-button>
          </el-upload>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
      <span>
        <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>

<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { getList as getshopList, getDirectorGroupList } from '@/api/operatemanage/base/shop'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatPlatform, formatYesorno, formatLinkProCode } from "@/utils/tools";
import { rulePlatform, ruleShopCode } from "@/utils/formruletools";
import buschar from '@/components/Bus/buschar'
import { importReplaceSend, pageReplaceSend, queryReplaceSendDailyAnalysis } from "@/api/financial/replacesend";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";

const tableCols = [
  { istrue: true, prop: 'recordDate', label: '日期', width: '90', sortable: 'custom', tipmesg: '订单支付日期', formatter: row => formatTime(row.recordDate, "YYYY-MM-DD") },
  { istrue: true, prop: 'proCode', label: '宝贝ID', width: '115', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
  { istrue: true, prop: 'goodsCode', label: '商品编码', width: '90', sortable: 'custom', },
  { istrue: true, prop: 'orderNoInner', label: '内部订单号', width: '100', sortable: 'custom',type:'orderLogInfo',orderType:'orderNoInner'  },
  { istrue: true, summaryEvent: true, prop: 'cost', label: '代拍成本', width: '90', tipmesg: '需要包含快递费', sortable: 'custom' },
  { istrue: true, summaryEvent: true, prop: 'amounted', label: '已付金额', width: '90', tipmesg: '已付金额不参与计算', sortable: 'custom' },
  { istrue: true, summaryEvent: true, prop: 'freightFee', label: '快递费', width: '70', sortable: 'custom', },
  { istrue: true, summaryEvent: true, prop: 'weight', label: '重量', width: '70', sortable: 'custom', },
  { istrue: true, prop: 'createdUserName', label: '上传人', width: '70', sortable: 'custom', },
  { istrue: true, prop: 'createdTime', label: '上传时间', width: '150', sortable: 'custom', },
  { istrue: true, prop: 'platform', label: '平台', width: '60', sortable: 'custom', formatter: row => formatPlatform(row.platform) },
  { istrue: true, prop: 'shopCode', label: '店铺名', width: '80', sortable: 'custom', formatter: row => row.shopName },
  { istrue: true, prop: 'goodsId', label: '运营组', width: '70', sortable: 'custom', formatter: row => row.groupName },
  { istrue: true, prop: 'expressCompany', label: '快递公司', width: '80', sortable: 'custom', },
  { istrue: true, prop: 'expressNo', label: '快递单号', width: '110', sortable: 'custom', },
  { istrue: true, prop: 'remark', label: '订单备注/型号', width: '150', sortable: 'custom', },
];
const tableHandles1 = [
  { label: "导入", handle: (that) => that.startImport() },
  { label: "导入模板", handle: (that) => that.downloadTemplate() },
  //{label:"导出", handle:(that)=>that.onExport()},
];
export default {
  name: 'Roles',
  components: { cesTable, MyContainer, MyConfirmButton, buschar,vxetablebase},
  data() {
    return {
      that: this,
      filter: {
        startDate: null,
        endDate: null,
        platform: null,
        shopCode: "",
        groupId: null,
        orderNo: null,
        goodsCode: null,
        timerange: [formatTime(dayjs().startOf("month"), "YYYY-MM-DD"), formatTime(new Date(), "YYYY-MM-DD")]
      },
      filterImport: { platform: null, uploadLoading: false },
      list: [],
      summaryarry: {},
      pager: { OrderBy: "recordDate", IsAsc: false },
      tableCols: tableCols,
      tableHandles: tableHandles1,
      platformList: [],
      shopList: [],
      grouplist: [],
      dialogVisible: false,
      total: 0,
      sels: [],
      listLoading: false,
      pageLoading: false,
      fileList: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      buscharDialog: { visible: false, title: "", data: [] },
    }
  },
  async mounted() {
    await this.setPlatform();

    await this.getlist();

  },
  methods: {
    async onsummaryClick(property) {
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      let pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };
      params.column = property;
      let that = this;
      await queryReplaceSendDailyAnalysis(params).then(res => {
        that.buscharDialog.visible = true;
        that.buscharDialog.data = res.data;
        that.buscharDialog.title = res.data.legend[0];
      });
    },
    //设置平台下拉
    async setPlatform() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;

      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
    },
    //设置店铺下拉
    async onchangeplatform(val) {
      const res = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
      this.shopList = res.data.list || [];
      this.shopList.push({ shopCode: "{线下}", shopName: "{线下}" });
      this.filter.shopCode = "";
      await this.onSearch();
    },
    //下载导入模板
    downloadTemplate() {
      window.open("../../static/excel/financial2/代拍成本模板.xlsx", "_self");
    },
    //开始导入
    startImport() {
      this.dialogVisible = true;
    },
    //取消导入
    cancelImport() {
      this.dialogVisible = false;
    },
    //上传成功
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    //提交导入
    submitUpload() {
      if (!this.filterImport.platform) {
        this.$message({ message: "请先选择平台！", type: "warning" });
        return;
      }
      this.$refs.upload.submit();
    },
    //上传文件
    async uploadFile(item) {
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      form.append("platform", this.filterImport.platform);
      const res = await importReplaceSend(form);
      if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
      else this.$message({ message: res.msg, type: "warning" });

      this.uploadLoading = false;
    },
    //获取查询条件
    getCondition() {
      if (this.filter.timerange && this.filter.timerange.length > 1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      else {
        this.$message({ message: "请先选择日期！", type: "warning" });
        return false;
      }
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = {
        ...pager,
        ...page,
        ... this.filter
      }

      return params;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    //分页查询
    async getlist() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }

      this.listLoading = true
      const res = await pageReplaceSend(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry = res.data.summary;
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
    //排序查询
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        if(column.prop=="goodsId"){
          column.prop="GroupId";
        }
        var orderBy = column.prop;
        this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    },
    selsChange: function (sels) {
      this.sels = sels
    }
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary {
  margin-right: 7px;
}
</style>
